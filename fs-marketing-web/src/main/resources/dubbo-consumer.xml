<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       ">

  <dubbo:application name="${dubbo.application.name}"/>
  <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}" timeout="120000"/>
  <dubbo:consumer check="false" filter="tracerpc" timeout="7000"/>
  <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" protocol="dubbo"/>
  <dubbo:reference id="openAppAdminService" interface="com.facishare.open.app.center.api.service.OpenAppAdminService" version="1.3"/>

  <dubbo:reference id="productService" interface="com.facishare.marketing.api.service.ProductService" version="${dubbo.provider.version}">
    <dubbo:method name="addEnterpriseProduct" retries="0" timeout="15000"/>
  </dubbo:reference>

  <dubbo:reference id="articleService" interface="com.facishare.marketing.api.service.ArticleService" version="${dubbo.provider.version}">
    <dubbo:method name="addWebCrawlerArticle" retries="0" timeout="500000"/>
    <dubbo:method name="initWebCrawlerArticle" retries="0" timeout="120000"/>
  </dubbo:reference>

  <dubbo:reference id="objectGroupService" interface="com.facishare.marketing.api.service.ObjectGroupService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="fileService" interface="com.facishare.marketing.api.service.FileService" version="${dubbo.provider.version}">
    <dubbo:method name="uploadFile" retries="0" timeout="60000"/>
  </dubbo:reference>

  <dubbo:reference id="activityService" interface="com.facishare.marketing.api.service.ActivityService" version="${dubbo.provider.version}">
    <dubbo:method name="mergeActivity" retries="0" timeout="15000"/>
  </dubbo:reference>

  <dubbo:reference id="marketingEventService" interface="com.facishare.marketing.api.service.MarketingEventService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="materielService" interface="com.facishare.marketing.api.service.distribution.MaterielService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="settingService" interface="com.facishare.marketing.api.service.SettingService" version="${dubbo.provider.version}">
    <dubbo:method name="initMarketingData" timeout="300000"/>
    <dubbo:method name="commitCodeAndSubmitAudit" timeout="30000" retries="0"/>
    <dubbo:method name="releaseCode" timeout="30000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference id="noticeService" interface="com.facishare.marketing.api.service.NoticeService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="systemNoticeService" interface="com.facishare.marketing.api.service.SystemNoticeService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="userService" interface="com.facishare.marketing.api.service.UserService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="objectFieldService" interface="com.facishare.marketing.api.service.ObjectFieldService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseStatisticService" interface="com.facishare.marketing.api.service.EnterpriseStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="distributorService" interface="com.facishare.marketing.api.service.distribution.DistributorService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseObjectStatisticService" interface="com.facishare.marketing.api.service.EnterpriseObjectStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseEmployeeStatisticService" interface="com.facishare.marketing.api.service.EnterpriseEmployeeStatisticService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="activityTemplateService" interface="com.facishare.marketing.api.service.ActivityTemplateService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="formTemplateService" interface="com.facishare.marketing.api.service.FormTemplateService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="objectDescriptionService" interface="com.facishare.marketing.api.service.ObjectDescriptionService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="formDataService" interface="com.facishare.marketing.api.service.FormDataService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CrmService" id="crmService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.FsBindService" id="fsBindService" version="${dubbo.provider.version}">
    <dubbo:method name="listEmployee" timeout="15000"/>
    <dubbo:method name="exportBindCardDetail" timeout="60000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.OperatorService" id="operatorService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.DistributionIndexService" id="distributionIndexService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="outerServiceWechatService" interface="com.facishare.wechat.union.core.api.service.OuterServiceWechatService" version="1.0" timeout="10000"/>
  <dubbo:reference id="douYinService" interface="com.facishare.marketing.api.service.connector.douyin.DouYinService" version="1.0" timeout="10000"/>

  <dubbo:reference id="groupSpaceService" interface="com.facishare.mankeep.api.service.GroupSpaceService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="clueService" interface="com.facishare.marketing.api.service.distribution.ClueService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="memberService" interface="com.facishare.marketing.api.service.MemberService" version="${dubbo.provider.version}"/>
  <dubbo:reference check="false" id="outEnterpriseSocialGroupService" interface="com.facishare.mankeep.api.outService.service.OutEnterpriseSocialGroupService" timeout="3000" version="1.0"/>

  <dubbo:reference id="applyService" interface="com.facishare.marketing.api.service.sms.ApplyService" version="${dubbo.provider.version}">
    <dubbo:method name="applySignature" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference id="payService" interface="com.facishare.marketing.api.service.sms.PayService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="smsVerCodeSettingService" interface="com.facishare.marketing.api.service.sms.SmsVerCodeSettingService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="quotaService" interface="com.facishare.marketing.api.service.sms.QuotaService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="sendService" interface="com.facishare.marketing.api.service.sms.SendService" version="${dubbo.provider.version}">
    <dubbo:method name="sendGroupSms" retries="0" timeout="60000"/>
    <dubbo:method name="getSMSSendDetail" retries="0" timeout="12000"/>
  </dubbo:reference>
  <dubbo:reference id="outCardService" interface="com.facishare.mankeep.api.outService.service.OutCardService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="enterpriseFeedService" interface="com.facishare.marketing.api.service.enterpriseFeed.EnterpriseFeedService" version="${dubbo.provider.version}"/>

  <dubbo:reference check="false" id="outEnterpriseDefaultCardService" interface="com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService" timeout="10000" version="1.0"/>
  <dubbo:reference id="marketingUserGroupService" interface="com.facishare.marketing.api.service.MarketingUserGroupService" version="${dubbo.provider.version}"/>

  <dubbo:reference check="false" id="weChatServiceMarketingActivityService" interface="com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService" timeout="30000"
      version="1.0"/>
  <dubbo:reference check="false" id="marketingActivityExternalConfigService" interface="com.facishare.marketing.api.service.marketingactivity.MarketingActivityExternalConfigService" timeout="10000"
      version="1.0"/>
  <dubbo:reference check="false" id="marketingActivityService" interface="com.facishare.marketing.api.service.marketingactivity.MarketingActivityService" timeout="10000" version="1.0">
    <dubbo:method name="addMarketingActivity" retries="0" timeout="60000"/>
  </dubbo:reference>

  <dubbo:reference check="false" id="userMarketingTagService" interface="com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService" timeout="10000" version="1.0"/>
  <dubbo:reference check="false" id="userMarketingAccountService" interface="com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService" timeout="30000" retries="0" version="1.0"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingReportService" id="marketingReportService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.EnterpriseSpreadStatisticService" id="enterpriseSpreadStatisticService" version="${dubbo.provider.version}">
    <dubbo:method name="listMarketingActivityEmployeeRanking"  timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference id="distributePlanGradeService" interface="com.facishare.marketing.api.service.distribution.DistributePlanGradeService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="customizeFormDataService" interface="com.facishare.marketing.api.service.CustomizeFormDataService"  version="${dubbo.provider.version}">
    <dubbo:method name="addCustomizeFormData" retries="0" timeout="10000"/>
    <dubbo:method name="updateCustomizeFormDataDetail" retries="0" timeout="10000"/>
    <dubbo:method name="queryMultipleFormUserData" retries="0" timeout="15000"/>
    <dubbo:method name="exportMultipleFormEnrollsData" retries="0" timeout="30000"/>
    <dubbo:method name="queryFormUserData" retries="0" timeout="15000"/>
    <dubbo:method name="reImportDataToCrm" retries="0" timeout="15000"/>
    <dubbo:method name="importFormEnrollData" retries="0" timeout="60000"/>
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.EnterpriseSocialGroupService" id="enterpriseSocialGroupService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingFlowService" id="marketingFlowService" version="1.0"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.MomentPosterService" id="momentPosterService" timeout="15000" version="${dubbo.provider.version}"/>

  <dubbo:reference id="marketingFlowInstanceService" interface="com.facishare.marketing.api.service.MarketingFlowInstanceService"  protocol="dubbo" version="1.0"/>

  <dubbo:reference id="conferenceService" interface="com.facishare.marketing.api.service.conference.ConferenceService" version="${dubbo.provider.version}">
    <dubbo:method name="addConference" retries="0" timeout="10000"/>
    <dubbo:method name="importEnrollData" retries="0" timeout="15000"/>
    <dubbo:method name="queryConferenceQrCode" retries="0" timeout="30000"/>
    <dubbo:method name="queryConferenceFormQrCode" retries="0" timeout="30000"/>
    <dubbo:method name="create" retries="0" timeout="15000"/>
    <dubbo:method name="update" retries="0" timeout="15000"/>
    <dubbo:method name="queryConferenceParticipants" retries="0" timeout="30000"/>
    <dubbo:method name="exportConferenceParticipants" retries="0" timeout="60000"/>
    <dubbo:method name="saveConferenceParticipantsToCrm" retries="0" timeout="60000"/>
    <dubbo:method name="upsertInvitationCommonSetting" retries="0" timeout="10000"/>
  </dubbo:reference>

  <dubbo:reference id="qrPosterCodeService" interface="com.facishare.marketing.api.service.qr.QRCodeService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="qrPosterService" interface="com.facishare.marketing.api.service.qr.QRPosterService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="spreadWorkService"  interface="com.facishare.marketing.api.service.kis.SpreadWorkService" protocol="dubbo" version="1.0">
    <dubbo:method name="createWXQRCodeByFeed" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference id="migrateService" interface="com.facishare.marketing.api.service.MigrateService" version="1.0" retries="0"  timeout="150000"/>
  <dubbo:reference id="wxTicketService" interface="com.facishare.marketing.api.service.WxTicketService" protocol="dubbo" version="1.0"/>

  <dubbo:reference id="adAccountService" interface="com.facishare.marketing.api.service.baidu.AdAccountService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="baiduCampaignService" interface="com.facishare.marketing.api.service.baidu.BaiduCampaignService" version="${dubbo.provider.version}" timeout="15000"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.WxOfficialAccountsService" id="wxOfficialAccountsService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="officialWebsiteService" interface="com.facishare.marketing.api.service.OfficialWebsiteService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.BrowserUserService" id="browserUserService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="contentMarketingEventService" interface="com.facishare.marketing.api.service.ContentMarketingEventService" version="${dubbo.provider.version}">
    <dubbo:method name="getOrCreateQrCodeByMarketingEventIdAndObjectInfo" retries="0" timeout="30000"/>
  </dubbo:reference>

  <dubbo:reference id="hexagonService" interface="com.facishare.marketing.api.service.hexagon.HexagonService" version="${dubbo.provider.version}">
    <dubbo:method name="marketingCopySite" retries="0" timeout="15000"/>
    <dubbo:method name="hexagonCopySite" retries="0" timeout="15000"/>
    <dubbo:method name="sitePreview" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.counselor.CounselorService" id="counselorService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdCallbackService" id="wxThirdCallbackService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdAuthService" id="wxThirdAuthService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdPlatformInnerSupportService" id="wxThirdPlatformInnerSupportService" version="${dubbo.provider.version}">
      <dubbo:method name="batchCommitCodeAndSubmitAudit" retries="0" timeout="60000"/>
      <dubbo:method name="batchReleaseCode" retries="0" timeout="60000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.pay.MerchantService" id="merchantService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.pay.PayResultCallbackService" id="payResultCallbackService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.live.LiveService" id="liveService" version="${dubbo.provider.version}" timeout="15000">
    <dubbo:method name="createLive" retries="0"/>
    <dubbo:method name="updateLive" retries="0"/>
    <dubbo:method name="queryLiveEnrollList" retries="0" timeout="60000"/>
    <dubbo:method name="exportLiveEnrollList" retries="0" timeout="60000"/>
    <dubbo:method name="syncLiveStatistics" retries="0" timeout="60000"/>
    <dubbo:method name="bindXiaoketongAccount" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.VideoService" id="videoService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ObjectBindDetailService" id="objectBindDetailService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QyweixinAccountBindService" id="qyweixinAccountBindService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.BoardService" id="boardService" version="${dubbo.provider.version}">
    <dubbo:method name="addBoard" retries="0" timeout="15000"/>
    <dubbo:method name="addEnterpriseTemplate" retries="0" timeout="30000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.WebHookService" id="webHookService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="mailService" interface="com.facishare.marketing.api.service.mail.MailService" version="${dubbo.provider.version}">
    <dubbo:method name="createEmailDomain" retries="0" timeout="30000"/>
    <dubbo:method name="createApiUser" retries="0" timeout="30000"/>
    <dubbo:method name="updateDomainConfig" retries="0" timeout="30000"/>
    <dubbo:method name="queryMailUserSendDetail" timeout="30000"/>
    <dubbo:method name="queryClickLinkDetail" timeout="30000"/>
    <dubbo:method name="createWebHook" retries="0" timeout="30000"/>
    <dubbo:method name="addTemplate" retries="0" timeout="30000"/>
    <dubbo:method name="updateTemplateDetail" retries="0" timeout="30000"/>
    <dubbo:method name="queryLeadTransforInfo" timeout="30000"/>
    <dubbo:method name="listMailMarketing" timeout="30000"/>
    <dubbo:method name="checkDomainConfig" timeout="30000"/>
    <dubbo:method name="queryFilterAddress" timeout="30000"/>
    <dubbo:method name="querySendErrorMail" timeout="30000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.EnterpriseMetaConfigService" id="enterpriseMetaConfigService" timeout="15000" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.StatisticService" id="statisticService" timeout="15000" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.statistic.outapi.service.MigrateService" id="statisticMigrateService" retries="0" timeout="15000" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.SpreadChannelService" id="spreadChannelService" retries="0" timeout="15000" version="1.0"></dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAppNavbarService" id="customizeMiniAppNavbarService" retries="0" timeout="15000" version="1.0"></dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingTriggerService" id="marketingTriggerService" retries="0" timeout="15000" version="1.0"></dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingSceneService" id="marketingSceneService" retries="0" timeout="15000" version="1.0"></dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.ShareContentService" id="shareContentService" version="${dubbo.provider.version}">
   <dubbo:method name="create" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.SafetyManagementService" id="safetyManagementService" version="${dubbo.provider.version}">
    <dubbo:method name="updateSafetyManagementSetting" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.ClueManagementService" id="clueManagementService" version="${dubbo.provider.version}">
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.baidu.AdObjectFieldMappingService" id="adObjectFieldMappingService" timeout="15000" protocol="dubbo" version="1.0" />

  <dubbo:reference id="qywxCardService" interface="com.facishare.marketing.api.service.qywx.CardService" protocol="dubbo" version="${dubbo.provider.version}">
    <dubbo:method name="addCardInfo" timeout="30000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference id="miniAppSettingService" interface="com.facishare.marketing.api.service.MiniAppSettingService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXSettingService" id="qywxSettingService" version="${dubbo.provider.version}"/>

  <dubbo:reference id="marketingEventCommonSettingService" interface="com.facishare.marketing.api.service.MarketingEventCommonSettingService" protocol="dubbo" version="${dubbo.provider.version}">
    <dubbo:method name="updateMarketingEventCommonSetting" timeout="15000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference id="enterpriseSelfDomainService" interface="com.facishare.marketing.api.service.EnterpriseSelfDomainService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.fileLibrary.FileLibraryService" id="fileLibraryService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.hexagon.HexagonTemplateService" id="hexagonTemplateService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CampaignMergeDataService" id="campaignMergeDataService" protocol="dubbo" version="1.0">
    <dubbo:method name="queryEnrollData" timeout="30000" retries="0"/>
    <dubbo:method name="exportEnrollData" timeout="60000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAuthorizeService" id="customizeMiniAuthorizeService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService" id="photoLibraryService" protocol="dubbo" version="1.0">
    <dubbo:method name="syncChuangKeTiePoster" timeout="30000" retries="0"/>
    <dubbo:method name="uploadPhoto" timeout="30000" retries="0"/>
    <dubbo:method name="batchUploadPhotos" timeout="200000" retries="0"/>
    <dubbo:method name="batchUploadTcPathPhotos" timeout="200000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.CrmDashBoardService" id="crmDashBoardService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAppCardNavbarService" id="customizeMiniAppCardNavbarService"  protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingEventStatisticsService" id="marketingEventStatisticsService" protocol="dubbo" version="1.0">
    <dubbo:method name="queryMarketingEventContentStatistics" timeout="30000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.wxthirdplatform.WxThirdCloudInnerService" id="wxThirdCloudInnerService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeContentService" id="customizeContentService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.advertiser.headlines.AuthCallbackService" id="authCallbackService" version="${dubbo.provider.version}"/>
    <dubbo:reference interface="com.facishare.marketing.api.service.advertiser.headlines.HeadlinesService" id="headlinesService"  version="${dubbo.provider.version}"/>

  <dubbo:reference id="miniappLoginService" interface="com.facishare.marketing.api.service.qywx.MiniappLoginService" version="${dubbo.provider.version}">
    <dubbo:method name="pLogin" timeout="15000" retries="0"/>
    <dubbo:method name="eLogin" timeout="15000" retries="0"/>
    <dubbo:method name="updateEUserInfo" timeout="15000" retries="0"/>
    <dubbo:method name="updatePUserInfo" timeout="15000" retries="0"/>
    <dubbo:method name="wxLogin" timeout="15000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference id="dingMiniappCallBackService" interface="com.facishare.marketing.api.service.DingMiniappCallBackService" version="${dubbo.provider.version}">
    <dubbo:method name="dingCallBack" timeout="15000" retries="0"/>
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.api.service.marketingplugin.MarketingPluginService" id="marketingPluginService" protocol="dubbo" version="1.0" />
  <dubbo:reference id="couponTemplateService" interface="com.facishare.marketing.api.service.marketingplugin.CouponTemplateService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="wxCouponPayService" interface="com.facishare.marketing.api.service.wxcoupon.WxCouponPayService" version="${dubbo.provider.version}" timeout="15000" retries="0"/>
  <dubbo:reference id="officialWebsiteThirdPlatformService" interface="com.facishare.marketing.api.service.OfficialWebsiteThirdPlatformService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXContactService"
                   id="qywxContactService" version="${dubbo.provider.version}">
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxWelcomeMsgService"
                   id="qywxWelcomeMsgService" version="${dubbo.provider.version}">
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService" id="qywxSelfBuildAppCallbackService" version="${dubbo.provider.version}">
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.advertiser.TencentAdService" id="tencentAdService" version="${dubbo.provider.version}">
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.pay.FsPayService" id="fsPayService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.OutLinkService" id="outLinkService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CardTemplateService" id="cardTemplateService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.card.EnterpriseDefaultCardService" id="enterpriseDefaultCardService" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.open.material.MaterialOpenService" id="materialService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.MarketingEvenCustomTagSettingService" id="marketingEvenCustomTagSettingService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxGroupCodeService" id="qywxGroupCodeService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.advertiser.ocpc.AdOCPCService" id="adOCPCService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.MktContentMgmtLogObjService" id="mktContentMgmtLogObjService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.sms.SmsQuotaNoticeSettingService" id="smsQuotaNoticeSettingService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.open.material.MaterialShowSettingService" id="materialShowSettingService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference id="dataPermissionService" interface="com.facishare.marketing.api.service.permission.DataPermissionService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="userMarketingCustomizeActionService" interface="com.facishare.marketing.api.service.UserMarketingCustomizeActionService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="advertiserService" interface="com.facishare.marketing.api.service.advertiser.AdvertiserService" version="${dubbo.provider.version}">
    <dubbo:method name="bigScreen" timeout="20000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference id="authenticationService" interface="com.facishare.marketing.api.service.AuthenticationService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="shanShanEditService" interface="com.facishare.marketing.api.service.shanshan.ShanShanEditService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxUserService" id="qywxUserService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.DingMiniAppStaffService" id="dingMiniAppStaffService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.crowd.TargetCrowdOperationService" id="targetCrowdOperationService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ObjectSloganRelationService" id="objectSloganRelationService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.WebsiteSeoService" id="websiteSeoService" protocol="dubbo" version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.whatsapp.WhatsAppService" id="whatsAppService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.OutUserMarketingActionService" id="outUserMarketingActionService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.baidu.BaiduFeedService" id="baiduFeedService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.marketingAssistant.YxzsNoticeSendService" id="yxzsNoticeSendService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.userrelation.UserRelationService" id="userRelationService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.cta.CtaService" id="ctaService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.appMenu.AppMenuTemplateService" id="appMenuTemplateService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ai.AiChatService" id="aiChatService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.FunctionService" id="functionService" protocol="dubbo" version="${dubbo.provider.version}">
    <dubbo:method name="sendMarketingEmail" timeout="15000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.connector.xiaohongshu.XiaoHongShuService" id="xiaoHongShuService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ai.ObjectQueryProxyService" id="objectQueryProxyService" protocol="dubbo" version="1.0"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.emailMaterial.EmailMaterialService" id="emailMaterialService" protocol="dubbo" version="${dubbo.provider.version}"/>
</beans>
