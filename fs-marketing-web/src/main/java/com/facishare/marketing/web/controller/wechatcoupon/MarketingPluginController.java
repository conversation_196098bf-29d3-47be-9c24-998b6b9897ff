package com.facishare.marketing.web.controller.wechatcoupon;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.plugin.BatchGetPluginStatusArg;
import com.facishare.marketing.api.arg.plugin.GetPluginSettingArg;
import com.facishare.marketing.api.arg.plugin.UpdatePluginSettingArg;
import com.facishare.marketing.api.result.PluginStatusResult;
import com.facishare.marketing.api.result.QueryScenaryResult;
import com.facishare.marketing.api.result.marketingplugin.CheckLicenseListResult;
import com.facishare.marketing.api.service.marketingplugin.MarketingPluginService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.arg.CheckLicenseArg;
import com.facishare.marketing.web.arg.CheckLicenseListArg;
import com.facishare.marketing.web.arg.IdArg;
import com.facishare.marketing.web.arg.wechatcoupon.QueryMarketingPluginArg;
import com.facishare.marketing.web.arg.wechatcoupon.UpdateMarketingPluginArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/9/9 11:21
 */

@RestController
    @RequestMapping("marketingPlugin")
@Slf4j
@Api(description = "营销插件接口",tags = "MarketingPluginController")
public class MarketingPluginController {

    @Autowired
    private MarketingPluginService marketingPluginService;

    @RequestMapping(value = "/saveOrUpdateMarketingPlugin", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "开启/关闭营销插件")
    public Result<Void> saveOrUpdateMarketingPlugin(@RequestBody UpdateMarketingPluginArg arg){
        if (arg.getStatus() == null || arg.getPluginType() == null){
            log.warn("MarketingPluginController.saveOrUpdateMarketingPlugin param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return marketingPluginService.saveOrUpdateMarketingPlugin(UserInfoKeeper.getEa(),arg.getPluginType(),arg.getStatus());
    }

    @RequestMapping(value = "/isOpenMarketingAdPlugin", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "是否开启广告营销插件")
    public Result<Boolean> isOpenMarketingAdPlugin(@RequestHeader Map<String, String> header){
        // 这个接口不用了
        return Result.newSuccess(false);
    }
//
//    @RequestMapping(value = "queryEnterCustomerServiceEaList",method = RequestMethod.POST,produces = "application/json;charset=UTF-8")
//    @ApiOperation(value = "判断企业是否加入客服插件灰度")
//    public Result<Boolean> queryEnterCustomerServiceEaList(){
//        return marketingPluginService.queryEnterCustomerServiceEaList(UserInfoKeeper.getEa());
//    }

    @RequestMapping(value = "/queryMarketingPluginStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询插件状态")
    public Result<Boolean> queryMarketingPluginStatus(@RequestBody QueryMarketingPluginArg arg){
        if (arg.getPluginType() == null){
            log.warn("MarketingPluginController.queryMarketingPluginStatus param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return marketingPluginService.queryMarketingPluginStatus(UserInfoKeeper.getEa(),arg.getPluginType());
    }
    @RequestMapping(value = "/queryServiceKnowledgeScenaryList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取知识库场景值列表")
    public Result<List<QueryScenaryResult>> queryServiceKnowledgeScenaryList(){
        return marketingPluginService.queryServiceKnowledgeScenaryList(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "/queryServiceKnowledgeScenarySetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "知识库场景设置")
    public Result<QueryScenaryResult> queryServiceKnowledgeScenarySetting(){
        return marketingPluginService.queryServiceKnowledgeScenarySetting(UserInfoKeeper.getEa());
    }
    @RequestMapping(value = "/insertOrUpdateServiceKnowledgeScenary", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新知识库场景设置")
    public Result insertOrUpdateServiceKnowledgeScenary(@RequestBody IdArg arg){
        if(!arg.verify()){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return marketingPluginService.insertOrUpdateServiceKnowledgeScenary(UserInfoKeeper.getEa(),arg.getId());
    }

    @RequestMapping(value = "/checkLicense", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "检查是否有license")
    public Result<Boolean> checkLicense(@RequestBody CheckLicenseArg arg){
        if(arg == null || StringUtils.isBlank(arg.getModuleCode())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return marketingPluginService.checkLicense(UserInfoKeeper.getEa(), arg.getModuleCode());
    }

    @RequestMapping(value = "/batchGetMarketingPluginStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "批量查询插件状态")
    public Result<PluginStatusResult> batchGetMarketingPluginStatus(@RequestBody BatchGetPluginStatusArg arg){
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingPluginService.batchGetMarketingPluginStaus(arg);
    }

    @RequestMapping(value = "/checkLicenseList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "批量检查是否有license")
    public Result<CheckLicenseListResult> checkLicenseList(@RequestBody CheckLicenseListArg arg){
        if(arg == null || CollectionUtils.isEmpty(arg.getModuleCodes())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return marketingPluginService.checkLicenseList(UserInfoKeeper.getEa(), arg.getModuleCodes());
    }
}
