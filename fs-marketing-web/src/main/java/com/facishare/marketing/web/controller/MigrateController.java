package com.facishare.marketing.web.controller;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.result.qywx.ListQywxMarketingActivityEmployeeRankingResult;
import com.facishare.marketing.api.service.EnterpriseSpreadStatisticService;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.MigrateService;
import com.facishare.marketing.api.service.marketingplugin.MarketingPluginService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.statistic.outapi.arg.InitUserBehaviorRecordObjArg;
import com.facishare.marketing.web.arg.RefreshCampaignDataArg;
import com.facishare.marketing.web.arg.wechatcoupon.UpdateMarketingPluginArg;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("migrate")
@Slf4j
public class MigrateController {
    @Autowired
    private MigrateService migrateService;
    @Autowired
    private MarketingEventService marketingEventService;
    @Autowired
    private com.facishare.marketing.statistic.outapi.service.MigrateService statisticMigrateService;

    @Autowired
    private MarketingPluginService marketingPluginService;

    @Autowired
    private EnterpriseSpreadStatisticService enterpriseSpreadStatisticService;

    @RequestMapping(path = "deleteAllMarketingFlowByEa", method = RequestMethod.POST)
    public Result<Map<String, Integer>> deleteAllMarketingFlowByEas(@RequestBody MigrateEasArg arg){
        Preconditions.checkArgument(arg.getEas() != null && !arg.getEas().isEmpty());
        return migrateService.deleteAllMarketingFlowByEas(arg);
    }

    @RequestMapping(path = "initMarketingFlowObjects", method = RequestMethod.POST)
    public Result<Void> initMarketingFlowObjects(@RequestBody MigrateEasArg arg){
        Preconditions.checkArgument(arg != null && arg.verify());
        return migrateService.initMarketingFlowObjects(arg);
    }

    @RequestMapping(path = "initUserMarketingAccountObj", method = RequestMethod.POST)
    public Result<Void> initUserMarketingAccountObj(@RequestBody MigrateEasArg arg){
        Preconditions.checkArgument(arg != null && arg.verify());
        return migrateService.initUserMarketingAccountObj(arg);
    }

    @RequestMapping(path = "listAllProEas", method = RequestMethod.POST)
    public Result<List<String>> initUserMarketingAccountObj(){
        return migrateService.listAllProEas();
    }

    @RequestMapping(path = "initMarketingFlowObjectsForProEas", method = RequestMethod.POST)
    public Result<Void> initMarketingFlowObjectsForProEas(){
        return migrateService.initMarketingFlowObjectsForProEas();
    }

    @RequestMapping(path = "migrateOldTemplateDataToMarketing", method = RequestMethod.POST)
    public Result<Void> migrateOldTemplateDataToMarketing(@RequestBody MigrateEasArg arg){
        return migrateService.migrateOldTemplateDataToMarketing(arg);
    }

    @RequestMapping(path = "removeInCompleteTemplateData", method = RequestMethod.POST)
    public Result<Void> removeInCompleteTemplateData(@RequestBody MigrateEasArg arg){
        return migrateService.removeInCompleteTemplateData(arg);
    }
    @RequestMapping(path = "migrateMarketingUserGroupTags", method = RequestMethod.POST)
    public Result<Void> migrateMarketingUserGroupTags(@RequestBody MigrateEasArg arg){
        return migrateService.migrateMarketingUserGroupTags(arg);
    }
    @RequestMapping(path = "migrateUserMarketingAccountTags", method = RequestMethod.POST)
    public Result<Void> migrateUserMarketingAccountTags(@RequestBody MigrateEasArg arg){
        return migrateService.migrateUserMarketingAccountTags(arg);
    }
    @RequestMapping(path = "migrateFlowTags", method = RequestMethod.POST)
    public Result<Void> migrateFlowTags(@RequestBody MigrateEasArg arg){
        return migrateService.migrateFlowTags(arg);
    }
    @RequestMapping(path = "migrateFlowDraftTags", method = RequestMethod.POST)
    public Result<Void> migrateFlowDraftTags(@RequestBody MigrateEasArg arg){
        return migrateService.migrateFlowDraftTags(arg);
    }
    @RequestMapping(path = "migrateTagsManage", method = RequestMethod.POST)
    public Result<Void> migrateTagsManage(@RequestBody MigrateEasArg arg){
        return migrateService.migrateTagsManage(arg);
    }

    @RequestMapping(path = "migrateMaterial", method = RequestMethod.POST)
    public Result<Void> migrateMaterial(@RequestBody MigrateEasArg arg){
        return migrateService.migrateMaterial(arg);
    }

    @RequestMapping(path = "updateMarketingActivitySpreadType", method = RequestMethod.POST)
    public Result<Void> updateMarketingActivitySpreadType(@RequestBody MigrateEasArg arg){
        return migrateService.updateMarketingActivitySpreadType(arg);
    }

    @RequestMapping(path = "addCancelStatusOptionToMarketingActivity", method = RequestMethod.POST)
    public Result<Void> addCancelStatusOptionToMarketingActivity(@RequestBody MigrateEasArg arg){
        return migrateService.addCancelStatusOptionToMarketingActivity(arg);
    }

    @RequestMapping(path = "initEnterpriseWxWorkExternalUserData", method = RequestMethod.POST)
    public Result<Void> initEnterpriseWxWorkExternalUserData(@RequestBody MigrateEasArg arg){
        return migrateService.initEnterpriseWxWorkExternalUserData(arg);
    }


    @RequestMapping(path = "tryInitMarketingBehaviorObject", method = RequestMethod.POST)
    Result<Void> tryInitMarketingBehaviorObject(@RequestBody MigrateEasArg arg){
        return migrateService.tryInitMarketingBehaviorObject(arg);
    }

    @RequestMapping(value = "addDefaultEventTypeOptionsToFieldDescribeByEas", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> addDefaultEventTypeOptionsToFieldDescribeByEas(@RequestBody MigrateEasArg arg) {
        return migrateService.addDefaultEventTypeOptionsToFieldDescribeByEas(arg);
    }

    @RequestMapping(path = "initMarketingEventCommonSetting", method = RequestMethod.POST)
    Result<Void> initMarketingEventCommonSetting(@RequestBody MigrateEasArg arg){
        return migrateService.initMarketingEventCommonSetting(arg);
    }

    @RequestMapping(value = "migrateAllEaWechatAccountBind", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> migrateAllEaWechatAccountBind(){
        return migrateService.migrateAllEaWechatAccountBind();
    }

    @RequestMapping(value = "migrateTryAppendFieldsToWxWorkExternalUserObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> migrateTryAppendFieldsToWxWorkExternalUserObj(@RequestBody MigrateEasArg arg){
        return migrateService.migrateTryAppendFieldsToWxWorkExternalUserObj(arg);
    }

    @RequestMapping(value = "migrateTryUpdateUnionIdToWxWorkExternalUserObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> migrateTryUpdateUnionIdToWxWorkExternalUserObj(@RequestBody MigrateEasArg arg){
        return migrateService.migrateTryUpdateUnionIdToWxWorkExternalUserObj(arg);
    }

    @RequestMapping(value = "migrateChangeTagGroupName", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> migrateChangeTagGroupName(@RequestBody MigrateEasArg arg){
        return migrateService.migrateChangeTagGroupName(arg);
    }

    @RequestMapping(value = "migrateChangeTagGroupNameByEis", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> migrateChangeTagGroupName(@RequestBody MigrateEiasArg arg){
        return migrateService.migrateChangeTagGroupName(arg);
    }

    @RequestMapping(value = "migrateLeadsUtmFieldByEas", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result migrateLeadsUtmFieldByEas(@RequestBody MigrateEasArg arg){
        return migrateService.migrateLeadsUtmFieldByEas(arg);
    }

    @RequestMapping(value = "migrateLeadsMarketingActivity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result migrateLeadsMarketingActivity(@RequestBody MigrateEasArg arg){
        return migrateService.migrateLeadsMarketingActivity(arg);
    }


    @RequestMapping(value = "migrateTagsToDefaultTagModel", method = RequestMethod.POST)
    public Result<Void> migrateTagsToDefaultTagModel(@RequestBody MigrateEasArg arg){
        return migrateService.migrateTagsToDefaultTagModel(arg);
    }

    @RequestMapping(value = "migratePaasSecondTagToFirstTags", method = RequestMethod.POST)
    public Result<Void> migratePaasSecondTagToFirstTags(@RequestBody MigrateEasArg arg){
        return migrateService.migratePaasSecondTagToFirstTags(arg);
    }

    @RequestMapping(value = "migrateRoleUsers", method = RequestMethod.POST)
    public Result<Void> migrateRoleUsers(@RequestBody MigrateEasArg arg){
        return migrateService.migrateRoleUsers(arg);
    }

    @RequestMapping(value = "migrateDefaultIntroductionPageSiteId", method = RequestMethod.POST)
    public Result<Void> migrateDefaultIntroductionPageSiteId(@RequestBody MigrateEasArg arg) {
        return migrateService.migrateDefaultIntroductionPageSiteId(arg);
    }

    @RequestMapping(value = "presetEnterpriseBoard", method = RequestMethod.POST)
    public Result<Void> presetEnterpriseBoard(@RequestBody MigrateEasArg arg) {
        return migrateService.presetEnterpriseBoard(arg);
    }

    @RequestMapping(value = "statisticAllMemberData", method = RequestMethod.POST)
    public Result<Void> statisticAllMemberData(@RequestBody MigrateEasArg arg){
        return migrateService.statisticAllMemberData(arg);
    }

    @RequestMapping(value = "migrateAppendLeadMarketingSpreadUser", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result migrateAppendLeadMarketingSpreadUser(@RequestBody MigrateEasArg arg) {
        return migrateService.migrateAppendLeadMarketingSpreadUser(arg);
    }

    @RequestMapping(value = "migrateStatisticData", method = RequestMethod.POST)
    public Result<Void> migrateStatisticData(@RequestBody MigrateEasArg arg){
        statisticMigrateService.migrateStatisticData(arg.getEas(), arg.getInitAll());
        return Result.newSuccess();
    }

    @RequestMapping(value = "migrateIntegrateCategory", method = RequestMethod.POST)
    public Result<Void> migrateIntegrateCategory(){
        return migrateService.migrateIntegrateCategory();
    }

    @RequestMapping(value = "migrateMemberDescribeAndLayout", method = RequestMethod.POST)
    public Result<Void> migrateMemberDescribeAndLayout(@RequestBody MigrateEasArg arg){
        return migrateService.migrateMemberDescribeAndLayout(arg);
    }

    @RequestMapping(value = "enableMarketingPromotionChannel", method = RequestMethod.POST)
    public Result<Void> enableMarketingPromotionChannel(@RequestBody MigrateEasArg arg){
        return migrateService.enableMarketingPromotionChannel(arg);
    }

    @RequestMapping(value = "addCampaignMembersObjField", method = RequestMethod.POST)
    public Result addCampaignMembersObjField(@RequestBody MigrateEasArg arg) {
        return migrateService.addCampaignMembersObjField(arg);
    }

    @RequestMapping(value = "syncCrmInvalidConference", method = RequestMethod.POST)
    public Result syncCrmInvalidConference(@RequestBody MigrateEasArg arg){
        return migrateService.syncCrmInvalidConference(arg);
    }

    @RequestMapping(value = "resetCampaignMergeData", method = RequestMethod.POST)
    public Result resetCampaignMergeData(@RequestBody MigrateEasArg arg) {
        return migrateService.resetCampaignMergeData(arg);
    }

    @RequestMapping(value = "syncLiveFormDataToMember", method = RequestMethod.POST)
    public Result syncLiveFormDataToMember(@RequestBody MigrateEasArg arg){
        return migrateService.syncLiveFormDataToMember(arg);
    }

    @RequestMapping(value = "migrateUserToCrmMemberManagerRole", method = RequestMethod.POST)
    public Result migrateUserToCrmMemberManagerRole(){
        return migrateService.migrateUserToCrmMemberManagerRole();
    }

    @RequestMapping(value = "resetNoSaveCrmData", method = RequestMethod.POST)
    public Result resetNoSaveCrmData(@RequestBody MigrateEasArg arg) {
        return migrateService.resetNoSaveCrmData(arg);
    }

    @RequestMapping(value = "saveCrmCampaignData", method = RequestMethod.POST)
    public Result saveCrmCampaignData(@RequestBody MigrateEasArg arg) {
        return migrateService.saveCrmCampaignData(arg);
    }

    @RequestMapping(value = "reImportDataToCrm", method = RequestMethod.POST)
    public Result reImportDataToCrm(@RequestBody MigrateEasArg arg) {
        return migrateService.reImportDataToCrm(arg);
    }

    @RequestMapping(value = "tryAppendWechatFanFields", method = RequestMethod.POST)
    public Result<Void> tryAppendWechatFanFields(@RequestBody MigrateEasArg arg) {
        return migrateService.tryAppendWechatFanFields(arg);
    }

    @RequestMapping(value = "markWxFanSpreadUserAsSingle", method = RequestMethod.POST)
    public Result<Void> markWxFanSpreadUserAsSingle(@RequestBody MigrateEasArg arg) {
        return migrateService.markWxFanSpreadUserAsSingle(arg);
    }

    @RequestMapping(value = "deleteMankeepRedisAccessToken", method = RequestMethod.POST)
    public Result deleteMankeepRedisAccessToken(@RequestBody MigrateEasArg arg) {
        return migrateService.deleteMankeepRedisAccessToken();
    }

    @RequestMapping(value = "deleteMankeepProRedisAccessToken", method = RequestMethod.POST)
    public Result deleteMankeepProRedisAccessToken(@RequestBody MigrateEasArg arg) {
        return migrateService.deleteMankeepProRedisAccessToken();
    }

    @RequestMapping(value = "reflashMlData", method = RequestMethod.POST)
    public Result reflashMlData(@RequestBody MigrateEasArg arg) {
        return migrateService.reflashMlData();
    }

    @RequestMapping(value = "resetLiveAllData", method = RequestMethod.POST)
    public Result resetLiveAllData(@RequestBody MigrateEasArg arg) {
        return migrateService.resetLiveAllData(arg);
    }

    @RequestMapping(value = "syncCrmDataToMarketingUserAccount", method = RequestMethod.POST)
    public Result syncCrmDataToMarketingUserAccount(@RequestBody MigrateEasArg arg) {
        return migrateService.syncCrmDataToMarketingUserAccount(arg);
    }

    @RequestMapping(value = "calculateMarketingUserGroup", method = RequestMethod.POST)
    public Result calculateMarketingUserGroup(@RequestBody MigrateEasArg arg) {
        return migrateService.calculateMarketingUserGroup(arg);
    }

    @RequestMapping(value = "migrateAllSystemPages", method = RequestMethod.POST)
    public Result migrateAllSystemPages() {
        return migrateService.migrateAllSystemPages();
    }

    @RequestMapping(value = "resetMarketingNotActivityAndLive", method = RequestMethod.POST)
    public Result resetMarketingNotActivityAndLive(@RequestBody MigrateEasArg arg) {
        return migrateService.resetMarketingNotActivityAndLive(arg);
    }

    @RequestMapping(value = "resetSpreadUserZeroData", method = RequestMethod.POST)
    public Result resetSpreadUserZeroData(@RequestBody MigrateEasArg arg) {
        return migrateService.resetSpreadUserZeroData(arg);
    }

    @RequestMapping(value = "resetCardQrCodeByEa", method = RequestMethod.POST)
    public Result resetCardQrCodeByEa(@RequestBody MigrateEasArg arg) {
        return migrateService.resetCardQrCodeByEa(arg);
    }

    @RequestMapping(value = "resetCardQrCodeByUid", method = RequestMethod.POST)
    public Result resetCardQrCodeByUid(@RequestBody MigrateEasArg arg) {
        return migrateService.resetCardQrCodeByUid(arg);
    }

    @RequestMapping(value = "resetEmailData", method = RequestMethod.POST)
    public Result resetEmailData() {
        return migrateService.resetEmailData();
    }

    @RequestMapping(value = "resetUtmFieldName", method = RequestMethod.POST)
    public Result resetUtmFieldName(@RequestBody MigrateEasArg arg) {
        return migrateService.resetUtmFieldName(arg);
    }

    @RequestMapping(value = "resetMarketingEventUV", method = RequestMethod.POST)
    public Result resetMarketingEventUV(@RequestBody MigrateEasArg arg) {
        return migrateService.resetMarketingEventUV(arg);
    }

    @RequestMapping(value = "setCustomizeFormToMarketingEvent", method = RequestMethod.POST)
    public Result setCustomizeFormToMarketingEvent(@RequestBody MigrateEasArg arg) {
        return migrateService.setCustomizeFormToMarketingEvent(arg);
    }

    @RequestMapping(value = "syncMarketingActivityLookupRoler", method = RequestMethod.POST)
    public Result syncMarketingActivityLookupRoler(@RequestBody MigrateEasArg arg){
        return migrateService.syncMarketingActivityLookupRoler(arg);
    }

    @RequestMapping(value = "resetCustomizeFormData", method = RequestMethod.POST)
    public Result resetCustomizeFormData(@RequestBody MigrateEasArg arg){
        return migrateService.resetCustomizeFormData(arg);
    }

    @RequestMapping(value = "resetCustomizeFormDataById", method = RequestMethod.POST)
    public Result resetCustomizeFormDataById(@RequestBody MigrateEasArg arg) {
        return migrateService.resetCustomizeFormDataById(arg);
    }

    @RequestMapping(value = "resetCustomizeMiniAppCardNavbar", method = RequestMethod.POST)
    public Result resetCustomizeMiniAppCardNavbar(@RequestBody MigrateEasArg arg){
        return migrateService.resetCustomizeMiniAppCardNavbar(arg);
    }

    @RequestMapping(value = "initOldMakeringRoleFunctionPrivilege", method = RequestMethod.POST)
    public Result initOldMakeringRoleFunctionPrivilege(@RequestBody MigrateEasArg arg){
        return migrateService.initOldMakeringRoleFunctionPrivilege(arg);
    }

    @RequestMapping(value = "createDefaultConferenceSite", method = RequestMethod.POST)
    public Result createDefaultConferenceSite(@RequestBody MigrateEasArg arg) {
        return migrateService.createDefaultConferenceSite(arg);
    }

    @RequestMapping(value = "syncContentMarketingEventMaterialRelationEventType")
    public Result syncContentMarketingEventMaterialRelationEventType(@RequestBody MigrateEasArg arg){
        return migrateService.syncContentMarketingEventMaterialRelationEventType(arg);
    }

    @RequestMapping(value = "syncFileRegisterActionMaterial")
    public Result syncFileRegisterActionMaterial(@RequestBody MigrateEasArg arg){
        return migrateService.syncFileRegisterActionMaterial(arg);
    }

    @RequestMapping(value = "resetConferenceStyle")
    public Result resetConferenceStyle(@RequestBody MigrateEasArg arg) {
        return migrateService.resetConferenceStyle(arg);
    }

    @RequestMapping(value = "resetConferenceCustomizeFormDataUser")
    public Result resetConferenceCustomizeFormDataUser(@RequestBody MigrateEasArg arg) {
        return migrateService.resetConferenceCustomizeFormDataUser(arg);
    }

    @RequestMapping(value = "initQywxAddressBook")
    public Result initQywxAddressBook(@RequestBody MigrateEasArg arg) {
        return migrateService.initQywxAddressBook(arg);
    }

    @RequestMapping(value = "refreshCampaignMergeDataFormMarketingEvent")
    public Result refreshCampaignMergeDataFormMarketingEvent(@RequestBody MigrateEasArg arg) {
        return migrateService.refreshCampaignMergeDataFormMarketingEvent(arg);
    }

    @RequestMapping(value = "fixQywxEmployeeMarketingActivtyStatData")
    public Result fixQywxEmployeeMarketingActivtyStatData(@RequestBody MigrateEasArg arg) {
        return migrateService.fixQywxEmployeeMarketingActivtyStatData(arg);
    }

    @RequestMapping(value = "syncCrmInvalidMarketingEventLive")
    public Result syncCrmInvalidMarketingEventLive(@RequestBody MigrateEasArg arg) {
        return migrateService.syncCrmInvalidMarketingEventLive(arg);
    }

    @RequestMapping(value = "updateWeChatMarketingActivityWxAppId")
    public Result updateWeChatMarketingActivityWxAppId(@RequestBody MigrateEasArg arg) {
        return migrateService.updateWeChatMarketingActivityWxAppId(arg);
    }


    @RequestMapping(value = "addMarketingActivityObjField")
    public Result addMarketingActivityObjField(@RequestBody MigrateEasArg arg) {
        return migrateService.addMarketingActivityObjField(arg);
    }

    @RequestMapping(value = "initMarketingLicense")
    public Result initMarketingLicense(@RequestBody MigrateEasArg arg) {
        return migrateService.initMarketingLicense(arg);
    }

    @RequestMapping(value = "addLeadObjSpreadChannelField")
    public Result addLeadsObjSpreadChannelField(@RequestBody MigrateEasArg arg) {
        return migrateService.addLeadsObjSpreadChannelField(arg);
    }

    @RequestMapping(value = "updateLeadCreator")
    public Result updateLeadCreator(@RequestBody MigrateEasArg arg) {
        return migrateService.updateLeadCreator(arg);
    }

    @RequestMapping(value = "initEnterpriseMarketing")
    public Result initEnterpriseMarketing(@RequestBody MigrateEasArg arg){
        return migrateService.initEnterpriseMarketing(arg);
    }

    @RequestMapping(value = "updateCardCover")
    public Result updateCardCover(@RequestBody MigrateEasArg arg) {
        return migrateService.updateCardCover(arg);
    }

    @RequestMapping(value = "insertCorpIdMapping")
    public Result insertCorpIdMapping(@RequestBody MigrateEasArg arg) {
        return migrateService.insertCorpIdMapping(arg);
    }
    @RequestMapping(value = "initPubPlatAuthComponent")
    public Result initPubPlatAuthComponent(@RequestBody MigrateEasArg arg) {
        return migrateService.initPubPlatAuthComponent(arg);
    }

    @RequestMapping(value = "initAdMarketingPlugin")
    public Result initAdMarketingPlugin(@RequestBody MigrateEasArg arg) {
        return migrateService.initAdMarketingPlugin(arg);
    }

    @RequestMapping(value = "initHeadlinesAdMarketingPlugin")
    public Result initHeadlinesAdMarketingPlugin(@RequestBody MigrateEasArg arg) {
        return migrateService.initHeadlinesAdMarketingPlugin(arg);
    }

    @RequestMapping(value = "initWeChatGroup")
    public Result initWeChatGroup(@RequestBody MigrateEasArg arg) {
        return migrateService.initWeChatGroup(arg);
    }

    @RequestMapping(value = "updateMarketingBehavior")
    public Result updateMarketingBehavior(@RequestBody MigrateEasArg arg) {
        return migrateService.updateMarketingBehavior(arg);
    }
    @RequestMapping(value = "appendLeadMarketingPartner")
    public Result appendLeadMarketingPartner(@RequestBody MigrateEasArg arg) {
        return migrateService.appendLeadMarketingPartner(arg);
    }

    @RequestMapping(value = "updateMarketingObjStatus")
    public Result updateMarketingObjStatus(@RequestBody MigrateEasArg arg) {
        return migrateService.updateMarketingObjStatus(arg);
    }

    @RequestMapping(value = "appendCampaignMembersLiveData")
    public Result appendCampaignMembersLiveData(@RequestBody MigrateEasArg arg) {
        return migrateService.appendCampaignMembersLiveData(arg);
    }

    @RequestMapping(value = "fixCrmObjectData")
    public Result fixCrmObjectData(@RequestBody MigrateEasArg arg) {
        return migrateService.fixCrmObjectData(arg);
    }

    @RequestMapping(value = "appendCampaignMembersPayData")
    public Result appendCampaignMembersPayData(@RequestBody MigrateEasArg arg) {
        return migrateService.appendCampaignMembersPayData(arg);
    }

    @RequestMapping(value = "initWechatFriendsRecordObj")
    public Result initWechatFriendsRecordObj(@RequestBody MigrateEasArg arg) {
        return migrateService.initWechatFriendsRecordObj(arg);
    }

    @RequestMapping(value = "syncExternalcontactData")
    public Result syncExternalcontactData(@RequestBody MigrateEasArg arg) {
        return migrateService.syncExternalcontactData(arg);
    }

    @RequestMapping(value = "deleteWechatFriendsRecordObj")
    public Result deleteWechatFriendsRecordObj(@RequestBody MigrateEasArg arg) {
        return migrateService.deleteWechatFriendsRecordObj(arg);
    }

    @RequestMapping(value = "refactorWechatFriendsRecord")
    public Result refactorWechatFriendsRecord(@RequestBody MigrateEasArg arg) {
        return migrateService.refactorWechatFriendsRecord(arg);
    }

    @RequestMapping(value = "appendCampaignMembersSpreadData")
    public Result appendCampaignMembersSpreadData(@RequestBody MigrateEasArg arg) {
        return migrateService.appendCampaignMembersSpreadData(arg);
    }

    @RequestMapping(value = "add4Obj")
    public Result add4Obj(@RequestBody MigrateEasArg arg) {
        return migrateService.add4Obj(arg);
    }

    @RequestMapping(value = "updateScrmObj")
    public Result updateScrmObj(@RequestBody MigrateEasArg arg) {
        return migrateService.updateScrmObj(arg);
    }

    @RequestMapping(value = "tryDeleteDescribeCustomField")
    public Result tryDeleteDescribeCustomField(@RequestBody MigrateEasArg arg) {
        return migrateService.tryDeleteDescribeCustomField(arg);
    }

    @RequestMapping(value = "outAccountToFsAccountBatchByEas")
    public Result outAccountToFsAccountBatchByEas(@RequestBody MigrateEasArg arg) {
        return migrateService.outAccountToFsAccountBatchByEas(arg);
    }

    @RequestMapping(value = "appendWechatFriendsRecordData")
    public Result appendWechatFriendsRecordData(@RequestBody MigrateEasArg arg) {
        return migrateService.appendWechatFriendsRecordData(arg);
    }

    @RequestMapping(value = "updateMarketingBehaviorDescribe")
    public Result updateMarketingBehaviorDescribe(@RequestBody MigrateEasArg arg) {
        return migrateService.updateMarketingBehaviorDescribe(arg);
    }
    @RequestMapping(value = "initAdDataReturnDetailObj")
    public Result initAdDataReturnDetailObj(@RequestBody MigrateEasArg arg) {
        return migrateService.initAdDataReturnDetailObj(arg);
    }

    @RequestMapping(value = "saveOrUpdateMarketingPlugin", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result saveOrUpdateMarketingPlugin(@RequestBody UpdateMarketingPluginArg arg) {
        if (StringUtils.isBlank(arg.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return marketingPluginService.saveOrUpdateMarketingPlugin(arg.getEa(), arg.getPluginType(), arg.getStatus());
    }

    @RequestMapping(value = "initMktContentMgmtLogObjManager")
    public Result initMktContentMgmtLogObjManager(@RequestBody MigrateEasArg arg) {
        return migrateService.initMktContentMgmtLogObjManager(arg);
    }

    @RequestMapping(value = "refreshAdCampaignData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result refreshAdCampaignData(@RequestBody RefreshCampaignDataArg arg) {
        if (StringUtils.isBlank(arg.getEa()) || StringUtils.isBlank(arg.getSource())
                || StringUtils.isBlank(arg.getAdAccountId()) || arg.getDay() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return migrateService.refreshAdCampaignData(arg.getEa(), arg.getAdAccountId(), arg.getSource(), arg.getDay());
    }

    @RequestMapping(value = "syncMarketingTermServingLinesDataByKeyword", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result syncMarketingTermServingLinesDataByKeyword(@RequestBody RefreshCampaignDataArg arg) {
        if (StringUtils.isBlank(arg.getAdAccountId()) || arg.getDay() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return migrateService.syncMarketingTermServingLinesDataByKeyword(arg.getAdAccountId(), arg.getDay());
    }

    @RequestMapping(value = "syncMarketingTermServingLinesDataByKeywordByDate", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> syncMarketingTermServingLinesDataByKeywordByDate(@RequestBody RefreshCampaignDataArg arg) {
        if (StringUtils.isBlank(arg.getAdAccountId()) || arg.getDay() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return migrateService.syncMarketingTermServingLinesDataByKeywordByDate(arg.getAdAccountId(), arg.getDate());
    }

    @RequestMapping(value = "initSmsSendRecordObj")
    public Result initSmsSendRecordObj(@RequestBody MigrateEasArg arg) {
        return migrateService.initSmsSendRecordObj(arg);
    }

    @RequestMapping(value = "initQywxMomentTaskStatisticData")
    public Result initQywxMomentTaskStatisticData (@RequestBody MigrateEasArg arg){
        return migrateService.initQywxMomentTaskStatisticData(arg);
    }

    @RequestMapping(value = "updateMemberObj")
    public Result updateMemberObj(@RequestBody MigrateEasArg arg) {
        return migrateService.updateMemberObj(arg);
    }
    @RequestMapping(value = "syncUserAccountRelationWxData")
    public Result syncUserAccountRelationWxData (@RequestBody MigrateEasArg arg){
        return migrateService.syncUserAccountRelationWxData(arg);
    }

    @RequestMapping(value = "RefreshAllAdAccountData")
    public Result RefreshAllAdAccountData (@RequestBody RefreshAllAdAccountData arg){
        return migrateService.refreshAllAdAccountData(arg);
    }

    @RequestMapping(value = "updateQywxObjectOwnerAsBindFsUser")
    public Result updateQywxObjectOwnerAsBindFsUser(@RequestBody MigrateEasArg arg){
        return migrateService.updateQywxObjectOwnerAsBindFsUser(arg);
    }

    @RequestMapping(value = "updateMarketingSpreadDataAsBindFsUser")
    public Result updateMarketingSpreadDataAsBindFsUser(@RequestBody MigrateEasArg arg){
        return migrateService.updateMarketingSpreadDataAsBindFsUser(arg);
    }
    @RequestMapping(value = "initQywxActivatedAccount")
    public Result RefreshAllAdAccountData (@RequestBody MigrateEasArg arg){
        return migrateService.initQywxActivatedAccount(arg);
    }

    @RequestMapping(value = "userMarketingActionUpgrade", method = RequestMethod.POST)
    public Result<Void> userMarketingActionUpgrade(@RequestBody UpgradeUserMarketingActionArg arg){
        statisticMigrateService.userMarketingActionUpgrade(arg.getEas(), arg.getInitAll(), arg.getNeedDeleteMongo());
        return Result.newSuccess();
    }

    @RequestMapping(value = "initMongoCollection", method = RequestMethod.POST)
    public Result<Void> initMongoCollection(){
        statisticMigrateService.initMongoCollection();
        return Result.newSuccess();
    }
    @RequestMapping(value = "cleanDuplicateData")
    public Result cleanDuplicateData (@RequestBody MigrateEasArg arg){
        return migrateService.cleanDuplicateData(arg);
    }

    @RequestMapping(value = "updateCampaignMembersObjSpreadData")
    public Result updateCampaignMembersObjSpreadData (@RequestBody MigrateEasArg arg){
        return migrateService.updateCampaignMembersObjSpreadData(arg);
    }

    @RequestMapping(value = "updateMemberObjData")
    public Result updateMemberObjData (@RequestBody MigrateEasArg arg){
        return migrateService.updateMemberObjData(arg);
    }

    /**
     * 终极方法,后续无特殊需求不再新增接口
     *
     * @param arg
     * @return
     */
    @RequestMapping(value = "invoke")
    public Result invoke(@RequestBody MigrateEasArg arg) {
        return migrateService.invoke(arg);
    }

    @RequestMapping(value = "refreshCards")
    public Result refreshCards(@RequestBody MigrateEasArg arg) {
        return migrateService.refreshCards(arg);
    }

    @RequestMapping(value = "addTeamMemberByQywxBindCrmEvent")
    public Result addTeamMemberByQywxBindCrmEvent(@RequestBody MigrateEasArg arg) {
        return migrateService.addTeamMemberByQywxBindCrmEvent(arg);
    }

    @RequestMapping(value = "initWechatGroupTeamMember")
    public Result initWechatGroupTeamMember(@RequestBody MigrateEasArg arg) {
        return migrateService.initWechatGroupTeamMember(arg);
    }

    @RequestMapping(value = "refreshWechatExternalUserAddSource")
    public Result refreshWechatExternalUserAddSource(@RequestBody MigrateEasArg arg) {
        return migrateService.refreshWechatExternalUserAddSource(arg);
    }

    @RequestMapping(value = "initAdvertisingDetailsObj")
    public Result initAdvertisingDetailsObj(@RequestBody MigrateEasArg arg) {
        return migrateService.initAdvertisingDetailsObj(arg);
    }

    @RequestMapping(value = "initLeadJoinCompensateStatusField")
    public Result initLeadJoinCompensateStatusField(@RequestBody MigrateEasArg arg) {
        return migrateService.initLeadJoinCompensateStatusField(arg);
    }

    @RequestMapping(value = "mergeUserMarketingActionStatistic")
    public Result<Void> mergeUserMarketingActionStatistic(@RequestBody MigrateEasArg arg) {
        return migrateService.mergeUserMarketingActionStatistic(arg);
    }

    @RequestMapping(value = "updateWxExternalUserObjSceneAndHideButton")
    public Result<Void> updateWxExternalUserObjSceneAndHideButton(@RequestBody MigrateEasArg arg) {
        return migrateService.updateWxExternalUserObjSceneAndHideButton(arg);
    }

    @RequestMapping(value = "initObjectAllStatistics", method = RequestMethod.POST)
    public Result<Void> initObjectAllStatistics(@RequestBody MigrateEasArg arg){
        statisticMigrateService.initObjectAllStatistics(arg.getEas(), arg.getInitAll());
        return Result.newSuccess();
    }
    @RequestMapping(value = "registerMarketingLiveOldMaterial", method = RequestMethod.POST)
    public Result<Void> registerMarketingLiveOldMaterial(@RequestBody MigrateEasArg arg){
        migrateService.registerMarketingLiveOldMaterial();
        return Result.newSuccess();
    }

    @RequestMapping(value = "reindexAdvertisingDetailsObj", method = RequestMethod.POST)
    public Result<Void> reindexAdvertisingDetailsObj(@RequestBody MigrateEasArg arg){
        return migrateService.reindexAdvertisingDetailsObj(arg);
    }

    @RequestMapping(value = "generateThirdPlatformSecret", method = RequestMethod.POST)
    public Result<Void> generateThirdPlatformSecret(@RequestBody MigrateEasArg arg){
        return migrateService.generateThirdPlatformSecret(arg);
    }

    @RequestMapping(value = "createEmployeeCard", method = RequestMethod.POST)
    public Result<Void> createEmployeeCard(@RequestBody MigrateEasArg arg){
        return migrateService.createEmployeeCard(arg);
    }

    @RequestMapping(value = "initObjectActionTypeStatistics", method = RequestMethod.POST)
    public Result<Void> initObjectActionTypeStatistics(@RequestBody MigrateEasArg arg){
        statisticMigrateService.initObjectActionTypeStatistics(arg.getEas(), arg.getInitAll());
        return Result.newSuccess();
    }

    @RequestMapping(value = "addMarketingTagEa", method = RequestMethod.POST)
    public Result<Void> addMarketingTagEa(@RequestBody MigrateEasArg arg){
        migrateService.addMarketingTagEa(arg.getEas());
        return Result.newSuccess();
    }

    @RequestMapping(value = "initMarketingLiveChannelAccount", method = RequestMethod.POST)
    public Result<Void> initMarketingLiveChannelAccount(@RequestBody MigrateEasArg arg){
        return migrateService.initMarketingLiveChannelAccount();
    }

    @RequestMapping(value = "pullQywxDataByEnterpriseUserIds", method = RequestMethod.POST)
    public Result<Void> pullQywxDataByEnterpriseUserIds(@RequestBody MigrateEasArg arg){
        return migrateService.pullQywxDataByEnterpriseUserIds(arg);
    }

    @RequestMapping(value = "updateMiniappDomain", method = RequestMethod.POST)
    public Result<Void> updateMiniappDomain(@RequestBody MigrateEasArg arg){
        return migrateService.updateMiniappDomain(arg);
    }

    @RequestMapping(value = "enablePublicData", method = RequestMethod.POST)
    public Result<Void> enablePublicData(@RequestBody MigrateEasArg arg){
        return migrateService.enablePublicData(arg);
    }

    @RequestMapping(value = "batchPurgeData", method = RequestMethod.POST)
    public Result<Void> batchPurgeData(@RequestBody MigrateEasArg arg){
        return migrateService.batchPurgeData(arg);
    }

    @RequestMapping(value = "initUserBehaviorRecordObj", method = RequestMethod.POST)
    public Result<Void> initUserBehaviorRecordObj(@RequestBody InitUserBehaviorRecordObjArg arg){
        statisticMigrateService.initUserBehaviorRecordObj(arg);
        return Result.newSuccess();
    }

    @RequestMapping(value = "checkDataByMongo", method = RequestMethod.POST)
    public Result<Void> checkDataByMongo(@RequestBody MigrateEasArg arg){
        String ea = arg.getEas().get(0);
        String[] arr = ea.split("_");
        statisticMigrateService.checkDataByMongo(arr[0], Boolean.parseBoolean(arr[1]));
        return Result.newSuccess();
    }

    @RequestMapping(value = "checkRepeatData", method = RequestMethod.POST)
    public Result<Void> checkRepeatData(@RequestBody MigrateEasArg arg){
        String ea = arg.getEas().get(0);
        String[] arr = ea.split("_");
        statisticMigrateService.checkRepeatData(arr[0], Boolean.parseBoolean(arr[1]));
        return Result.newSuccess();
    }

    @RequestMapping(value = "deleteCouponDistributionObj", method = RequestMethod.POST)
    public Result<Void> deleteCouponDistributionObj(@RequestBody MigrateEasArg arg){
        return migrateService.deleteCouponDistributionObj(arg);
    }

    @RequestMapping(value = "updateCouponMaxCoupons", method = RequestMethod.POST)
    public Result<Void> updateCouponMaxCoupons(@RequestBody MigrateEasArg arg){
        return migrateService.updateCouponMaxCoupons(arg);
    }

    @RequestMapping(value = "testQywxMomentTaskEmployeeRanking", method = RequestMethod.POST)
    public Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> testQywxMomentTaskEmployeeRanking() {
        ListQywxMarketingActivityEmployeeRankingArg arg = new ListQywxMarketingActivityEmployeeRankingArg();
        arg.setMarketingActivityId("668619d8e01a9b000166c2ba");
        arg.setPageNum(1);
        arg.setPageSize(10);
        return enterpriseSpreadStatisticService.listQywxMomentTaskEmployeeRanking("dongheng2024", 1001, arg);
    }

    @RequestMapping(value = "deleteSessionKey", method = RequestMethod.POST)
    public Result<Void> deleteSessionKey(@RequestBody SessionUidArg arg) {
        return migrateService.deleteSessionKey(arg);
    }

    @RequestMapping(value = "createSystemMenuTemplate", method = RequestMethod.POST)
    public Result<Void> createSystemMenuTemplate(@RequestBody MigrateEasArg arg) {
        return migrateService.createSystemMenuTemplate(arg);
    }

    @RequestMapping(value = "queryAccountObjectExist", method = RequestMethod.POST)
    public Result<Void> queryAccountObjectExist(@RequestBody MigrateEasArg arg){
        return migrateService.queryAccountObjectExist(arg);
    }

    @RequestMapping(value = "syncQywxBindInfo", method = RequestMethod.POST)
    public Result<Void> syncQywxBindInfo(@RequestBody MigrateEasArg arg) {
        return migrateService.syncQywxBindInfo(arg);
    }

    //解绑营销用户
    @RequestMapping(value = "unbindMarketingUserBindInfo", method = RequestMethod.POST)
    public Result<Void> unbindMarketingUserBindInfo(@RequestBody MigrateEasArg arg) {
        return migrateService.unbindMarketingUserBindInfo(arg);
    }

    /**
     * 修复市场活动活动成员缺失数据，前提条件：要有报名数据表
     * @param arg
     * @return
     */
    @RequestMapping(value = "fixCampaignMergeData", method = RequestMethod.POST)
    public Result<Void> fixCampaignMergeData(@RequestBody FixCampaignMergeDataArg arg) {
        return migrateService.fixCampaignMergeData(arg);
    }

    @RequestMapping(value = "deleteMiniAppReleaseRecord", method = RequestMethod.POST)
    public Result<Void> deleteMiniAppReleaseRecord(@RequestBody DeleteMiniAppReleaseRecordArg arg) {
        return migrateService.deleteMiniAppReleaseRecord(arg);
    }
}
