package com.facishare.marketing.web.arg.live;

import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2020/3/23.
 */
@Data
@ToString(callSuper = true)
public class ListArg implements Serializable{
    @ApiModelProperty("直播状态集合")
    private List<Integer> statusList;

    @ApiModelProperty("搜索直播标题关键字")
    private String keyword;

    @ApiModelProperty("是否只展示允许推广的会议")
    private Boolean isShowSpread;

    @ApiModelProperty("当前页码")
    private Integer pageSize;

    @ApiModelProperty("每页显示数量")
    private Integer pageNum;

    @ApiModelProperty("对象选择器")
    private FilterData filterData;

    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;
}
