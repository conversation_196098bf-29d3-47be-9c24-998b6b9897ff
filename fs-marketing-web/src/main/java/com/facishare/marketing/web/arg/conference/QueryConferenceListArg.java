package com.facishare.marketing.web.arg.conference;

import com.facishare.marketing.api.arg.BasePageArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
/**
 * Created by zhengh on 2019/7/17.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryConferenceListArg extends BasePageArg {
//    @ApiModelProperty("会议状态 (0全部，1未发布，2 会议未开始 3 会议进行中 4 会议已结束)")
//    private Integer flowStatus;

    @ApiModelProperty("会议搜索关键词")
    private String keyword;
    
    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("是否为推广查询")
    private Boolean spreadSearch;

    @ApiModelProperty("是否只展示允许推广的会议")
    private Boolean isShowSpread;

    @ApiModelProperty("市场活动对象选择器")
    private FilterData filterData;

    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;
}
