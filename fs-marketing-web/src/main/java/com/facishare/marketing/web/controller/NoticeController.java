package com.facishare.marketing.web.controller;

import com.facishare.marketing.api.arg.spreadTask.GetNoticeByUserArg;
import com.facishare.marketing.api.result.spreadTask.GetNoticeByUserResult;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.BasePageArg;
import com.facishare.marketing.api.arg.ListNoticeArg;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.result.NoticeResult;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.NoticeSendUserArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created By tianh, 2018/7/2.
 **/
@RestController
@RequestMapping("/notice")
@Slf4j
public class NoticeController {
    @Autowired
    private NoticeService noticeService;

    @CheckIdentityTrigger
    @RequestMapping(value = "sendNotice", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result sendNotice(@RequestBody com.facishare.marketing.web.arg.NoticeSendArg arg) {
        // TODO 校验必填字段
        if (arg.getNoticeVisibilityArg() == null || arg.getNoticeVisibilityArg().isInvalidVisibility() || arg.getContentType() == null || Strings.isNullOrEmpty(arg.getContent()) || Strings
            .isNullOrEmpty(arg.getTitle())) {
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        NoticeSendArg vo = BeanUtil.copy(arg, NoticeSendArg.class);
        NoticeSendArg.NoticeVisibilityVO noticeVisibilityVO = BeanUtil.copy(arg.getNoticeVisibilityArg(), NoticeSendArg.NoticeVisibilityVO.class);
        vo.setNoticeVisibilityVO(noticeVisibilityVO);

        return noticeService.sendNotice(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), vo);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "sendNoticeInvite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result sendNoticeInvite() {
        return noticeService.sendNoticeInvite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @ApiOperation(value = "单个发送邀请", notes = "单个发送邀请", tags = "1.5")
    @CheckIdentityTrigger
    @RequestMapping(value = "sendfsUserIdNoticeInvite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result sendfsUserIdNoticeInvite(@RequestBody NoticeSendUserArg arg) {
        Preconditions.checkNotNull(arg.getFsUserId(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_NOTICECONTROLLER_59));
        return noticeService.sendfsUserIdNoticeInvite(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),arg.getFsUserId());
    }

    @ApiOperation(value = "员工端查看邮件推广任务详情", notes = "员工端查看邮件推广任务详情", tags = "1.5")
    @CheckIdentityTrigger
    @RequestMapping(value = "getNoticeByUser", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetNoticeByUserResult> getNoticeByUser(@RequestBody GetNoticeByUserArg arg) {
        Result checkResult = arg.checkParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return noticeService.getNoticeByUser(arg);
    }

}