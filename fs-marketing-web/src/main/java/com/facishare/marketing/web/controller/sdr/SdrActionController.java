package com.facishare.marketing.web.controller.sdr;

import com.facishare.marketing.api.arg.sdr.SdrActionArg;
import com.facishare.marketing.api.service.sdr.SdrActionService;
import com.facishare.marketing.common.result.FunctionResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("out/sdr/action")
@Slf4j
public class SdrActionController {

    @Autowired
    private SdrActionService sdrActionService;

    @PostMapping(value = "/marketingInsights", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "营销AI洞察")
    public FunctionResult<Map<String, Object>> marketingInsights(@RequestBody SdrActionArg arg, @RequestHeader Map<String, String> header) {
        Integer tenantId = Integer.parseInt(header.get("x-fs-ei"));
        Integer userId = Integer.parseInt(header.get("x-fs-userinfo"));
        arg.setTenantId(tenantId);
        arg.setFsUserId(userId);
        return sdrActionService.marketingInsights(arg);
    }

    @PostMapping(value = "/marketingInsightsDeepOptimized", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "营销AI洞察-DeepOptimized")
    public FunctionResult<Map<String, Object>> marketingInsightsDeepOptimized(@RequestBody SdrActionArg arg, @RequestHeader Map<String, String> header) {
        Integer tenantId = Integer.parseInt(header.get("x-fs-ei"));
        Integer userId = Integer.parseInt(header.get("x-fs-userinfo"));
        arg.setTenantId(tenantId);
        arg.setFsUserId(userId);
        return sdrActionService.marketingInsightsDeepOptimized(arg);
    }

    @PostMapping(value = "/batchQuerySDRVariableData", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "sdr相关变量数据")
    public FunctionResult<Map<String, Object>> batchQuerySDRVariableData(@RequestBody SdrActionArg arg, @RequestHeader Map<String, String> header) {
        Integer tenantId = Integer.parseInt(header.get("x-fs-ei"));
        Integer userId = Integer.parseInt(header.get("x-fs-userinfo"));
        arg.setTenantId(tenantId);
        arg.setFsUserId(userId);
        return sdrActionService.batchQuerySDRVariableData(arg);
    }

    @PostMapping(value = "/batchQuerySDRRelatedScripts", produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "sdr相关话术案例")
    public FunctionResult<Map<String, Object>> batchQuerySDRRelatedScripts(@RequestBody SdrActionArg arg, @RequestHeader Map<String, String> header) {
        Integer tenantId = Integer.parseInt(header.get("x-fs-ei"));
        Integer userId = Integer.parseInt(header.get("x-fs-userinfo"));
        arg.setTenantId(tenantId);
        arg.setFsUserId(userId);
        return sdrActionService.batchQuerySDRRelatedScripts(arg);
    }

}
