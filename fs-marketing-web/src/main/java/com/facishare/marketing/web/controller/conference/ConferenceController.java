package com.facishare.marketing.web.controller.conference;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.GetSignInSuccessSettingArg;
import com.facishare.marketing.api.UpdateSignInSuccessSettingArg;
import com.facishare.marketing.api.arg.conference.QueryInviteCountInfoArg;
import com.facishare.marketing.api.result.BuildCrmObjectByEnrollDataResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.ImportUserDataResult;
import com.facishare.marketing.api.result.conference.AddCampaignMembersObjResult;
import com.facishare.marketing.api.result.conference.AddConferenceResult;
import com.facishare.marketing.api.result.conference.CheckMarketingEventResult;
import com.facishare.marketing.api.result.conference.CreateOrUpdateConferenceResult;
import com.facishare.marketing.api.result.conference.GetAllEnrollDataByCampaignResult;
import com.facishare.marketing.api.result.conference.GetConferenceStatisticDataResult;
import com.facishare.marketing.api.result.conference.GetEnrollImportTemplateResult;
import com.facishare.marketing.api.result.conference.GetInvitationCommonSettingResult;
import com.facishare.marketing.api.result.conference.GetSignInQrUrlResult;
import com.facishare.marketing.api.result.conference.GetSignInSettingResult;
import com.facishare.marketing.api.result.conference.GetTemplateDataResult;
import com.facishare.marketing.api.result.conference.QueryAllCampaignDataResult;
import com.facishare.marketing.api.result.conference.QueryCampaignExternalContactStatisticsResult;
import com.facishare.marketing.api.result.conference.QueryCampaignWxStatisticsResult;
import com.facishare.marketing.api.result.conference.QueryConferenceDetailResult;
import com.facishare.marketing.api.result.conference.QueryConferenceFormQrCodeResult;
import com.facishare.marketing.api.result.conference.QueryConferenceListResult;
import com.facishare.marketing.api.result.conference.QueryConferenceParticipantsResult;
import com.facishare.marketing.api.result.conference.QueryConferenceQrCodeResult;
import com.facishare.marketing.api.result.conference.QueryConferenceUserGroupResult;
import com.facishare.marketing.api.result.conference.QueryInvitationResult;
import com.facishare.marketing.api.result.conference.QueryInviteCountInfoResult;
import com.facishare.marketing.api.result.conference.QueryMarketingEventDetailResult;
import com.facishare.marketing.api.result.conference.QueryNotificationSettingsResult;
import com.facishare.marketing.api.result.conference.QueryTicketManagerResult;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.util.XssfExcelUtil;
import com.facishare.marketing.api.vo.conference.AddCampaignMembersObjVO;
import com.facishare.marketing.api.vo.conference.AddConferenceVO;
import com.facishare.marketing.api.vo.conference.AddTicketMangerVO;
import com.facishare.marketing.api.vo.conference.BindCrmObjectByCampaignIdVO;
import com.facishare.marketing.api.vo.conference.ChangeConferenceParticipantsReviewStatusVO;
import com.facishare.marketing.api.vo.conference.ChangeConferenceParticipantsSignStatusVO;
import com.facishare.marketing.api.vo.conference.ConferenceEnrollSettingVO;
import com.facishare.marketing.api.vo.conference.CreateOrUpdateConferenceVO;
import com.facishare.marketing.api.vo.conference.DeleteParticipantVO;
import com.facishare.marketing.api.vo.conference.GetConferenceStatisticDataVO;
import com.facishare.marketing.api.vo.conference.GetInvitationCommonSettingVO;
import com.facishare.marketing.api.vo.conference.GetSignInQrUrlVO;
import com.facishare.marketing.api.vo.conference.GetSignInSettingVO;
import com.facishare.marketing.api.vo.conference.ImportSignInDataVO;
import com.facishare.marketing.api.vo.conference.ImportUserDataVO;
import com.facishare.marketing.api.vo.conference.InvitationSettingVO;
import com.facishare.marketing.api.vo.conference.InviteParticipantVO;
import com.facishare.marketing.api.vo.conference.NotificationSettingsVO;
import com.facishare.marketing.api.vo.conference.QueryAllCampaignDataVO;
import com.facishare.marketing.api.vo.conference.QueryConferenceDetailVO;
import com.facishare.marketing.api.vo.conference.QueryConferenceFormQrCodeVO;
import com.facishare.marketing.api.vo.conference.QueryConferenceListVO;
import com.facishare.marketing.api.vo.conference.QueryConferenceParticipantsVO;
import com.facishare.marketing.api.vo.conference.QueryConferenceQrCodeVO;
import com.facishare.marketing.api.vo.conference.QueryInvitationListVO;
import com.facishare.marketing.api.vo.conference.QueryInvitationVO;
import com.facishare.marketing.api.vo.conference.QueryInviteParticipantVO;
import com.facishare.marketing.api.vo.conference.QueryMarketingEventDetailVO;
import com.facishare.marketing.api.vo.conference.QueryNotificationSettingsVO;
import com.facishare.marketing.api.vo.conference.ResetConferenceQrCodeVO;
import com.facishare.marketing.api.vo.conference.SaveConferenceParticipantsToCrmVO;
import com.facishare.marketing.api.vo.conference.SignInByEnrollFieldVO;
import com.facishare.marketing.api.vo.conference.UpdateConferenceContentVO;
import com.facishare.marketing.api.vo.conference.UpdateConferenceDetailVO;
import com.facishare.marketing.api.vo.conference.UpdateConferenceStatusVO;
import com.facishare.marketing.api.vo.conference.UpdateGroupInfoVO;
import com.facishare.marketing.api.vo.conference.UpdateInvitationStatusVO;
import com.facishare.marketing.api.vo.conference.UpdateInviteInfoVO;
import com.facishare.marketing.api.vo.conference.UpdateSignInSettingVO;
import com.facishare.marketing.api.vo.conference.UpsertInvitationCommonSettingVO;
import com.facishare.marketing.common.enums.ActivityStatusEnum;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.ImportTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.arg.AddTicketMangerArg;
import com.facishare.marketing.web.arg.DeleteParticipantArg;
import com.facishare.marketing.web.arg.QueryTicketManagerArg;
import com.facishare.marketing.web.arg.conference.AddCampaignMembersObjArg;
import com.facishare.marketing.web.arg.conference.AddConferenceArg;
import com.facishare.marketing.web.arg.conference.BindCrmObjectByCampaignIdArg;
import com.facishare.marketing.web.arg.conference.BuildCrmObjectByCampaignIdArg;
import com.facishare.marketing.web.arg.conference.ChangeConferenceParticipantsReviewStatusArg;
import com.facishare.marketing.web.arg.conference.ChangeConferenceParticipantsSignStatusArg;
import com.facishare.marketing.web.arg.conference.CheckMarketingEventArg;
import com.facishare.marketing.web.arg.conference.ConferenceEnrollSettingArg;
import com.facishare.marketing.web.arg.conference.CreateOrUpdateConferenceArg;
import com.facishare.marketing.web.arg.conference.DeleteInviteArg;
import com.facishare.marketing.web.arg.conference.GetAllEnrollDataByCampaignArg;
import com.facishare.marketing.web.arg.conference.GetCampaignMembersObjFieldArg;
import com.facishare.marketing.web.arg.conference.GetConferenceArg;
import com.facishare.marketing.web.arg.conference.GetConferenceStatisticDataArg;
import com.facishare.marketing.web.arg.conference.GetEnrollImportTemplateArg;
import com.facishare.marketing.web.arg.conference.GetInvitationCommonSettingArg;
import com.facishare.marketing.web.arg.conference.GetSignInQrUrlArg;
import com.facishare.marketing.web.arg.conference.GetSignInSettingArg;
import com.facishare.marketing.web.arg.conference.ImportSignInDataArg;
import com.facishare.marketing.web.arg.conference.ImportUserDataArg;
import com.facishare.marketing.web.arg.conference.InvitationSettingArg;
import com.facishare.marketing.web.arg.conference.InviteParticipantArg;
import com.facishare.marketing.web.arg.conference.NotificationSettingsArg;
import com.facishare.marketing.web.arg.conference.QueryAllCampaignDataArg;
import com.facishare.marketing.web.arg.conference.QueryCampaignWxStatisticsArg;
import com.facishare.marketing.web.arg.conference.QueryConferenceDetailArg;
import com.facishare.marketing.web.arg.conference.QueryConferenceFormQrCodeArg;
import com.facishare.marketing.web.arg.conference.QueryConferenceListArg;
import com.facishare.marketing.web.arg.conference.QueryConferenceParticipantsArg;
import com.facishare.marketing.web.arg.conference.QueryConferenceQrCodeArg;
import com.facishare.marketing.web.arg.conference.QueryConferenceUserGroupArg;
import com.facishare.marketing.web.arg.conference.QueryInvitationArg;
import com.facishare.marketing.web.arg.conference.QueryInvitationListArg;
import com.facishare.marketing.web.arg.conference.QueryInviteParticipantArg;
import com.facishare.marketing.web.arg.conference.QueryMarketingEventDetailArg;
import com.facishare.marketing.web.arg.conference.QueryNotificationSettingsArg;
import com.facishare.marketing.web.arg.conference.ResetConferenceQrCodeArg;
import com.facishare.marketing.web.arg.conference.SaveConferenceParticipantsToCrmArg;
import com.facishare.marketing.web.arg.conference.SignInByEnrollFieldArg;
import com.facishare.marketing.web.arg.conference.UpdateConferenceContentArg;
import com.facishare.marketing.web.arg.conference.UpdateConferenceDetailArg;
import com.facishare.marketing.web.arg.conference.UpdateConferenceStatusArg;
import com.facishare.marketing.web.arg.conference.UpdateGroupInfoArg;
import com.facishare.marketing.web.arg.conference.UpdateInvitationStatusArg;
import com.facishare.marketing.web.arg.conference.UpdateInviteInfoArg;
import com.facishare.marketing.web.arg.conference.UpdateInviteStatusArg;
import com.facishare.marketing.web.arg.conference.UpdateSignInSettingArg;
import com.facishare.marketing.web.arg.conference.UpsertInvitationCommonSettingArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by ranluch on 2019/7/17.
 */
@RestController
@RequestMapping("conference")
@Slf4j
@Api(description = "会议营销", tags = "ConferenceController")
public class ConferenceController {
    @Autowired
    private ConferenceService conferenceService;

    @RequestMapping(value = "createOrUpdate", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建会议", notes = "新版创建会议")
    public Result<CreateOrUpdateConferenceResult> create(@RequestBody CreateOrUpdateConferenceArg arg){
        if (arg == null || !arg.checkParam()){
            log.warn("ConferenceController.create error param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        CreateOrUpdateConferenceVO vo = BeanUtil.copy(arg, CreateOrUpdateConferenceVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());

        if (StringUtils.isNotEmpty(vo.getConferenceId())){
            return conferenceService.update(vo);
        }else {
            return conferenceService.create(vo);
        }
    }


    @RequestMapping(value = "updateConferenceContent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新会议详情")
    public Result updateConferenceContent(@RequestBody UpdateConferenceContentArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.updateConferenceContent param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateConferenceContentVO vo = BeanUtil.copy(arg, UpdateConferenceContentVO.class);
        return conferenceService.updateConferenceContent(vo);
    }

    @RequestMapping(value = "addConference", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "添加会议", notes = "添加会议")
    public Result<AddConferenceResult> addConference(@RequestBody AddConferenceArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.addConference error param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AddConferenceVO vo = BeanUtil.copy(arg, AddConferenceVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.addConference(vo);
    }

    @RequestMapping(value = "list", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询会议列表", notes = "查询会议列表")
    public Result<PageResult<QueryConferenceListResult>> queryConferenceList(@RequestBody QueryConferenceListArg arg) {
        if (arg.getPageNum() == null || arg.getPageSize() == null) {
            log.info("ConferenceController.queryConferenceList failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getSpreadSearch() == null){
            arg.setSpreadSearch(false);
        }
        QueryConferenceListVO vo = BeanUtil.copy(arg, QueryConferenceListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        if (arg.getSpreadSearch()) {
            vo.setActivityStatus(Lists.newArrayList(ActivityStatusEnum.ENABLED.getStatus()));
        }
        vo.setNeedMarketingActivityResult(false);
        try {
            return conferenceService.queryConferenceList(vo);
        } catch (Exception e) {
            log.error("ConferenceController.queryConferenceList failed arg:{} e:", arg, e);
            return Result.newError(SHErrorCode.QUERY_CONFERENCE_LIST_FAILED);
        }
    }

    @RequestMapping(value = "updateConferenceDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新会议详情", notes = "更新会议详情")
    public Result updateConferenceDetail(@RequestBody UpdateConferenceDetailArg arg) {
        if (arg == null || arg.isParamError()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateConferenceDetailVO vo = BeanUtil.copy(arg, UpdateConferenceDetailVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.updateConferenceDetail(vo);
    }

    @RequestMapping(value = "updateConferenceStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新会议状态", notes = "更新会议状态")
    public Result updateConferenceStatus(@RequestBody UpdateConferenceStatusArg arg) {
        if (arg == null || arg.getStatus() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateConferenceStatusVO vo = BeanUtil.copy(arg, UpdateConferenceStatusVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.updateConferenceStatus(vo);
    }

    @RequestMapping(value = "queryConferenceDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询会议详情", notes = "查询会议详情")
    public Result<QueryConferenceDetailResult> queryConferenceDetail(@RequestBody QueryConferenceDetailArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryConferenceDetailVO vo = BeanUtil.copy(arg, QueryConferenceDetailVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setCheckMarketingEvenAuth(true);
        return conferenceService.queryConferenceDetail(vo);
    }

    @RequestMapping(value = "updateConferenceEnrollSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新会议报名设置", notes = "更新会议报名设置")
    public Result updateConferenceEnrollSetting(@RequestBody ConferenceEnrollSettingArg arg) {
        if (arg == null || arg.isParamError()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ConferenceEnrollSettingVO vo = BeanUtil.copy(arg, ConferenceEnrollSettingVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.updateConferenceEnrollSetting(vo);
    }

    @RequestMapping(value = "queryConferenceParticipants", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询参会人员列表", notes = "查询参会人员列表")
    public Result<PageResult<QueryConferenceParticipantsResult>> queryConferenceParticipants(@RequestBody QueryConferenceParticipantsArg arg) {
        if (arg.getConferenceId() == null) {
            log.info("ConferenceController.queryConferenceParticipants failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getFilterPhoneUser() == null) {
            arg.setFilterPhoneUser(false);
        }

        QueryConferenceParticipantsVO vo = BeanUtil.copy(arg, QueryConferenceParticipantsVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        try {
            return conferenceService.queryConferenceParticipants(vo);
        } catch (Exception e) {
            log.error("ConferenceController.queryConferenceParticipants failed arg:{} e:{}", arg, e);
            return Result.newError(SHErrorCode.QUERY_CONFERENCE_PARTICIPANTS_FAILED);
        }
    }

    @RequestMapping(value = "exportConferenceParticipants", method = RequestMethod.POST)
    @ApiOperation(value = "导出参会人员列表", notes = "导出参会人员列表")
    public void exportConferenceParticipants(QueryConferenceParticipantsArg arg, HttpServletResponse httpServletResponse) throws Exception {
        if (arg.getConferenceId() == null) {
            log.info("ConferenceController.queryConferenceParticipants failed param error arg:{}", arg);
            return;
        }

        QueryConferenceParticipantsVO vo = BeanUtil.copy(arg, QueryConferenceParticipantsVO.class);
        try {
            vo.setEa(UserInfoKeeper.getEa());
            vo.setUserId(UserInfoKeeper.getFsUserId());
            conferenceService.exportConferenceParticipants(vo);
        } catch (Exception e) {
            log.error("ConferenceController.exportConferenceParticipants failed arg:{} e:{}", arg, e.fillInStackTrace());
        }
    }

    @RequestMapping(value = "invitationSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "邀请函设置", notes = "邀请函设置")
    public Result invitationSetting(@RequestBody InvitationSettingArg arg) {
        if (arg == null || arg.isParamError()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        InvitationSettingVO vo = BeanUtil.copy(arg, InvitationSettingVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.invitationSetting(vo);
    }

    @RequestMapping(value = "updateInvitationStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新邀请函状态", notes = "更新邀请函状态")
    public Result updateInvitationStatus(@RequestBody UpdateInvitationStatusArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId()) || arg.getStatus() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateInvitationStatusVO vo = BeanUtil.copy(arg, UpdateInvitationStatusVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.updateInvitationStatus(vo);
    }

    @RequestMapping(value = "queryInvitationInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询邀请函详情", notes = "查询邀请函详情")
    public Result<QueryInvitationResult> queryInvitationInfo(@RequestBody QueryInvitationArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryInvitationVO vo = BeanUtil.copy(arg, QueryInvitationVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryInvitationInfo(vo);
    }

    @RequestMapping(value = "queryInvitationList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "分页查询邀请函", notes = "分页查询邀请函")
    public Result<PageResult<QueryInvitationResult>> queryInvitationList(@RequestBody QueryInvitationListArg arg) {
        if (arg == null || (StringUtils.isBlank(arg.getConferenceId()) && StringUtils.isBlank(arg.getMarketingEventId()))) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryInvitationListVO vo = BeanUtil.copy(arg, QueryInvitationListVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryInvitationList(vo);
    }

    @RequestMapping(value = "notificationSettings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "会议提醒设置", notes = "会议提醒设置")
    public Result notificationSettings(@RequestBody NotificationSettingsArg arg) {
        if (arg == null || arg.getNotificationSettings() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        NotificationSettingsVO vo = BeanUtil.copy(arg, NotificationSettingsVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.notificationSettings(vo);
    }

    @RequestMapping(value = "queryNotificationSettings", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询会议提醒设置", notes = "查询会议提醒设置")
    public Result<QueryNotificationSettingsResult> queryNotificationSettings(@RequestBody QueryNotificationSettingsArg arg) {
        if (arg == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryNotificationSettingsVO vo = BeanUtil.copy(arg, QueryNotificationSettingsVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryNotificationSettings(vo);
    }

    @RequestMapping(value = "getConferenceStatisticData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取会议统计数据", notes = "获取会议统计数据")
    public Result<GetConferenceStatisticDataResult> getConferenceStatisticData(@RequestBody GetConferenceStatisticDataArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getConferenceStatisticData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetConferenceStatisticDataVO vo = BeanUtil.copy(arg, GetConferenceStatisticDataVO.class);
        return conferenceService.getConferenceStatisticData(vo);
    }

    @RequestMapping(value = "queryConferenceQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询会议二维码", notes = "查询会议二维码")
    public Result<QueryConferenceQrCodeResult> queryConferenceQrCode(@RequestBody QueryConferenceQrCodeArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryConferenceQrCodeVO vo = BeanUtil.copy(arg, QueryConferenceQrCodeVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryConferenceQrCode(vo);
    }

    @RequestMapping(value = "queryConferenceFormQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询会议表单二维码", notes = "查询会议表单二维码")
    public Result<QueryConferenceFormQrCodeResult> queryConferenceFormQrCode(@RequestBody QueryConferenceFormQrCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryConferenceFormQrCode param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryConferenceFormQrCodeVO vo = BeanUtil.copy(arg, QueryConferenceFormQrCodeVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryConferenceFormQrCode(vo);
    }

    @RequestMapping(value = "resetConferenceQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "重置会议二维码", notes = "重置会议二维码")
    public Result<QueryConferenceQrCodeResult> resetConferenceQrCode(@RequestBody ResetConferenceQrCodeArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ResetConferenceQrCodeVO vo = BeanUtil.copy(arg, ResetConferenceQrCodeVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.resetConferenceQrCode(vo);
    }

    @RequestMapping(value = "changeConferenceParticipantsSignStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更改参会人员签到状态", notes = "更改参会人员签到状态")
    public Result<Boolean> changeConferenceParticipantsSignStatus(@RequestBody ChangeConferenceParticipantsSignStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.changeConferenceParticipantsSignStatus param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        ChangeConferenceParticipantsSignStatusVO vo = BeanUtil.copy(arg, ChangeConferenceParticipantsSignStatusVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        try {
            return conferenceService.changeConferenceParticipantsSignStatus(vo);
        } catch (Exception e) {
            log.error("ConferenceController.changeConferenceParticipantsSignStatus failed arg:{} e:", arg, e);
            return Result.newError(SHErrorCode.CHANGE_CONFERENCE_PARTICIPANTS_SIGN_STATUS_FAILED);
        }
    }

    @RequestMapping(value = "changeConferenceParticipantsReviewStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "手动审核参会人员状态", notes = "手动审核参会人员状态")
    public Result<Boolean> changeConferenceParticipantsReviewStatus(@RequestBody ChangeConferenceParticipantsReviewStatusArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.changeConferenceParticipantsReviewStatus param arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ChangeConferenceParticipantsReviewStatusVO vo = BeanUtil.copy(arg, ChangeConferenceParticipantsReviewStatusVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        try {
            return conferenceService.changeConferenceParticipantsReviewStatus(vo);
        } catch (Exception e) {
            log.error("ConferenceController.changeConferenceParticipantsReviewStatus failed arg:{} e:", arg, e);
            return Result.newError(SHErrorCode.CHANGE_CONFERENCE_PARTICIPANTS_REVIEW_STATUS_FAILED);
        }
    }

    @RequestMapping(value = "saveConferenceParticipantsToCrm", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "重新将参会人员存入销售线索", notes = "重新将参会人员存入销售线索")
    public Result<Boolean> saveConferenceParticipantsToCrm(@RequestBody SaveConferenceParticipantsToCrmArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.saveConferenceParticipantsToCrm param arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        SaveConferenceParticipantsToCrmVO vo = BeanUtil.copy(arg, SaveConferenceParticipantsToCrmVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        try {
            return conferenceService.saveConferenceParticipantsToCrm(vo);
        } catch (Exception e) {
            log.error("ConferenceController.saveConferenceParticipantsToCrm failed arg:{} e:", arg, e);
            return Result.newError(SHErrorCode.SAVE_CONFERENCE_PARTICIPANTS_TO_CRM_FAILED);
        }
    }

    @RequestMapping(value = "getSignInQrUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取会议签到二维码", notes = "获取会议签到二维码")
    public Result<GetSignInQrUrlResult> getSignInQrUrl(@RequestBody GetSignInQrUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getSignInQrUrl param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetSignInQrUrlVO vo = BeanUtil.copy(arg, GetSignInQrUrlVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.getSignInQrUrl(vo);
    }

    @RequestMapping(value = "getTemplateData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取会议模板数据", notes = "获取会议模板数据")
    public Result<List<GetTemplateDataResult>> getTemplateData() {
        return conferenceService.getTemplateData();
    }

    @RequestMapping(value = "checkConferenceStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "校验市场活动", notes = "校验市场活动")
    public Result<CheckMarketingEventResult> checkMarketingEvent(@RequestBody CheckMarketingEventArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.checkMarketingEvent param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.checkMarketingEvent(arg.getMarketingEventId(), UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @RequestMapping(value = "queryMarketingEventDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询市场活动详情", notes = "查询市场活动详情")
    public Result<QueryMarketingEventDetailResult> queryMarketingEventDetail(@RequestBody QueryMarketingEventDetailArg arg) {
        if (arg == null || (StringUtils.isBlank(arg.getConferenceId())) && StringUtils.isBlank(arg.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryMarketingEventDetailVO vo = BeanUtil.copy(arg, QueryMarketingEventDetailVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryMarketingEventDetail(vo);
    }

    @Deprecated
    @RequestMapping(value = "getEnrollImportTemplate", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取报名导入模板", notes = "获取报名导入模板")
    public void getEnrollImportTemplate(GetEnrollImportTemplateArg arg, HttpServletResponse httpServletResponse) throws IOException {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getEnrollImportTemplate param error arg:{}", arg);
            return;
        }
        Result<GetEnrollImportTemplateResult> getEnrollImportTemplate = conferenceService.getEnrollImportTemplate(arg.getConferenceId(), arg.getImportType());
        if (!getEnrollImportTemplate.isSuccess()) {
            log.warn("ConferenceController.getEnrollImportTemplate getEnrollImportTemplate error result:{}", getEnrollImportTemplate);
            return;
        }
        GetEnrollImportTemplateResult getEnrollImportTemplateResult = getEnrollImportTemplate.getData();
        StringBuilder filename = new StringBuilder(getEnrollImportTemplateResult.getFileName()).append(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCECONTROLLER_559) + ".xlsx");
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename.toString());
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, getEnrollImportTemplateResult.getTitleList(), getEnrollImportTemplateResult.getDataList());
        httpServletResponse.setContentType(excelConfigMap.get(ExcelConfigEnum.CONTENT_TYPE).toString());
        httpServletResponse.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(filename.toString(), "UTF-8"));
        OutputStream bos = httpServletResponse.getOutputStream();
        xssfWorkbook.write(bos);
        httpServletResponse.flushBuffer();
        bos.flush();
        bos.close();
    }

    @Deprecated
    @RequestMapping(value = "importUserData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "导入会议报名/邀约名单", notes = "导入会议报名/邀约名单")
    public Result<ImportUserDataResult> importUserData(@RequestBody ImportUserDataArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.importUserData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ImportUserDataVO vo = BeanUtil.copy(arg, ImportUserDataVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return arg.getImportType().equals(ImportTypeEnum.CONFERENCE_ENROLL.getType()) ? conferenceService.importEnrollData(vo) : conferenceService.importInviteData(vo);
    }

    @RequestMapping(value = "exportInviteData", method = RequestMethod.POST)
    @ApiOperation(value = "导出邀约人员列表", notes = "导出邀约人员列表")
    public void exportInviteData(QueryInviteParticipantArg arg, HttpServletResponse httpServletResponse) throws Exception {
        if (arg.getConferenceId() == null) {
            log.info("ConferenceController.exportInviteData failed param error arg:{}", arg);
            return;
        }

        QueryInviteParticipantVO vo = BeanUtil.copy(arg, QueryInviteParticipantVO.class);
        try {
            vo.setEa(UserInfoKeeper.getEa());
            Result<ExportEnrollsDataResult> enrollsDataResult = conferenceService.exportInviteData(vo);
            if (enrollsDataResult.isSuccess() && enrollsDataResult.getData() != null) {
                XssfExcelUtil exportResult = new XssfExcelUtil(enrollsDataResult.getData(), I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCECONTROLLER_602));
                exportResult.xssfWorkBook2Binary(httpServletResponse);
            }
        } catch (Exception e) {
            log.error("ConferenceController.exportInviteData failed arg:{} e:{}", arg, e.fillInStackTrace());
        }
        return;
    }

    @RequestMapping(value = "inviteParticipants", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "发送邀约企信", notes = "发送邀约企信")
    public Result<Void> inviteParticipants(@RequestBody InviteParticipantArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.inviteParticipants param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getNeedFilterEnrollData() == null) {
            arg.setNeedFilterEnrollData(false);
        }
        InviteParticipantVO vo = BeanUtil.copy(arg, InviteParticipantVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.inviteParticipants(vo);
    }

    @RequestMapping(value = "queryInviteCountInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询邀约进展", notes = "查询邀约进展")
    public Result<QueryInviteCountInfoResult> queryInviteCountInfo(@RequestBody com.facishare.marketing.web.arg.conference.QueryInviteCountInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryInviteCountInfo param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<Integer> fsUserIds = arg.getFsUserIds();
        if (CollectionUtils.isEmpty(fsUserIds) && CollectionUtils.isEmpty(arg.getCircleIds())) {
            // 默认选择自己
            fsUserIds = Lists.newArrayList(UserInfoKeeper.getFsUserId());
        }
        QueryInviteCountInfoArg vo = new QueryInviteCountInfoArg();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setFsUserIds(fsUserIds);
        vo.setConferenceId(arg.getConferenceId());
        vo.setCircleIds(arg.getCircleIds());
        return conferenceService.queryInviteCountInfo(vo);
    }

    @RequestMapping(value = "updateInviteStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改邀约状态", notes = "修改邀约状态")
    public Result<Void> updateInviteStatus(@RequestBody UpdateInviteStatusArg arg) {
        if (arg == null || CollectionUtils.isEmpty(arg.getIds()) || arg.getStatus() == null) {
            log.warn("ConferenceController.updateInviteStatus param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return conferenceService.updateInviteStatus(arg.getIds(), arg.getStatus());
    }

    @RequestMapping(value = "deleteInvite", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除邀约人", notes = "删除邀约人")
    public Result<Void> deleteInvite(@RequestBody DeleteInviteArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getInviteId())) {
            log.warn("ConferenceController.deleteInvite param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return conferenceService.deleteInvite(arg.getInviteId());
    }

    @RequestMapping(value = "updateInviteInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新邀约人信息", notes = "更新邀约人信息")
    public Result<Void> updateInviteInfo(@RequestBody UpdateInviteInfoArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getId())) {
            log.warn("ConferenceController.updateInviteInfo param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        UpdateInviteInfoVO vo = BeanUtil.copy(arg, UpdateInviteInfoVO.class);
        return conferenceService.updateInviteInfo(vo);
    }

    @RequestMapping(value = "updateGroupInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新分组信息", notes = "更新分组信息")
    public Result updateGroupInfo(@RequestBody UpdateGroupInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.updateGroupInfo param error  arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateGroupInfoVO updateGroupInfoVO = BeanUtil.copy(arg, UpdateGroupInfoVO.class);
        updateGroupInfoVO.setEa(UserInfoKeeper.getEa());
        updateGroupInfoVO.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.updateGroupInfo(updateGroupInfoVO);
    }

    @RequestMapping(value = "deleteParticipants", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除参会人员", notes = "删除参会人员")
    public Result<Void> deleteParticipants(@RequestBody DeleteParticipantArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.deleteParticipants param error  arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        DeleteParticipantVO vo = new DeleteParticipantVO();
        vo.setCampaignId(arg.getCampaignId());
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.deleteParticipants(vo);
    }

    @RequestMapping(value = "addTicketManger", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "设置验票信息", notes = "设置验票信息")
    public Result<Void> addTicketManger(@RequestBody AddTicketMangerArg arg){
        if (arg == null || arg.wrongParam()){
            log.error("ConferenceController.addTicketManger param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        AddTicketMangerVO vo = BeanUtil.copy(arg, AddTicketMangerVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.addTicketManger(vo);
    }

    @RequestMapping(value = "queryTicketManager", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询验票信息", notes = "查询验票信息")
    public Result<QueryTicketManagerResult> queryTicketManager(@RequestBody QueryTicketManagerArg arg){
        if (arg == null || StringUtils.isEmpty(arg.getConferenceId())){
            log.error("ConferenceController.queryTicketManager param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return conferenceService.queryTicketManager(UserInfoKeeper.getEa(), arg.getConferenceId());
    }

    @RequestMapping(value = "getAllEnrollDataByCampaign", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据参会人员id查询参会人员下全部报名数据", notes = "根据参会人员id查询参会人员下全部报名数据")
    public Result<GetAllEnrollDataByCampaignResult> getAllEnrollDataByCampaign(@RequestBody GetAllEnrollDataByCampaignArg arg) {
        if(arg.isWrongParam()) {
            log.warn("ConferenceController.getAllEnrollDataByCampaign param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.getAllEnrollDataByCampaign(UserInfoKeeper.getEa(), arg.getCampaignId());
    }

    @RequestMapping(value = "queryConferenceUserGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询会议下所有标签", notes = "查询会议下所有标签")
    public Result<List<QueryConferenceUserGroupResult>> queryConferenceUserGroup(@RequestBody QueryConferenceUserGroupArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryConferenceUserGroup param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.queryConferenceUserGroup(UserInfoKeeper.getEa(), arg.getConferenceId());
    }

    @RequestMapping(value = "addCampaignMembersObj", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "添加活动成员", notes = "添加活动成员")
    public Result<AddCampaignMembersObjResult> addCampaignMembersObj(@RequestBody AddCampaignMembersObjArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.addCampaignMembersObj param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AddCampaignMembersObjVO vo = new AddCampaignMembersObjVO();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        vo.setMarketingEventId(arg.getMarketingEventId());
        vo.setCampaignMembersStatus(arg.getCampaignMembersStatus());
        vo.setMemberObjDetails(BeanUtil.copy(arg.getMemberObjDetails(), AddCampaignMembersObjVO.MemberObjDetail.class));
        return conferenceService.addCampaignMembersObj(vo);
    }

    @RequestMapping(value = "getCampaignMembersObjField", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取活动成员对象字段", notes = "获取活动成员对象字段")
    public Result<List<FieldInfo>> getCampaignMembersObjField(@RequestBody GetCampaignMembersObjFieldArg arg) {
        return conferenceService.getCampaignMembersObjField(UserInfoKeeper.getEa());
    }


    @RequestMapping(value = "queryCampaignWxStatistics", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动下公众号统计信息")
    public Result<QueryCampaignWxStatisticsResult> queryCampaignWxStatistics(@RequestBody QueryCampaignWxStatisticsArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryCampaignWxStatistics param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.queryCampaignWxStatistics(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getMarketingEventId());
    }

    @RequestMapping(value = "queryCampaignExternalContactStatistics", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动下微信客户统计信息")
    public Result<QueryCampaignExternalContactStatisticsResult> queryCampaignExternalContactStatistics(@RequestBody QueryCampaignWxStatisticsArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.queryCampaignExternalContactStatistics param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.queryCampaignExternalContactStatistics(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getMarketingEventId());
    }

    @RequestMapping(value = "queryAllCampaignData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取市场活动下所有参会人员信息")
    public Result<List<QueryAllCampaignDataResult>> queryAllCampaignData(@RequestBody QueryAllCampaignDataArg arg) {
        if(arg.isWrongParam()) {
            log.warn("ConferenceController.queryAllCampaignData error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryAllCampaignDataVO vo = BeanUtil.copy(arg, QueryAllCampaignDataVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.queryAllCampaignData(vo);
    }

    @RequestMapping(value = "buildCrmObjectByCampaignId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据参会人员id构建crm对象数据")
    public Result<BuildCrmObjectByEnrollDataResult> buildCrmObjectByCampaignId(@RequestBody BuildCrmObjectByCampaignIdArg arg) {
        if(arg.isWrongParam()) {
            log.warn("ConferenceController.buildCrmObjectByCampaignId param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.buildCrmObjectByCampaignId(arg.getCampaignId(), UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId());
    }

    @RequestMapping(value = "bindCrmObjectByCampaignId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据参会人员id绑定crm对象数据")
    public Result bindCrmObjectByCampaignId(@RequestBody BindCrmObjectByCampaignIdArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.bindCrmObjectByCampaignId param arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        BindCrmObjectByCampaignIdVO vo = BeanUtil.copy(arg, BindCrmObjectByCampaignIdVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.bindCrmObjectByCampaignId(vo);
    }

    @RequestMapping(value = "signInByEnrollField", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据报名字段进行签到")
    public Result signInByEnrollField(@RequestBody SignInByEnrollFieldArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.signInByEnrollField param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        SignInByEnrollFieldVO vo = BeanUtil.copy(arg, SignInByEnrollFieldVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.signInByEnrollField(vo);
    }


    @RequestMapping(value = "importSignInData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "导入签到数据")
    public Result<ImportUserDataResult> importSignInData(@RequestBody ImportSignInDataArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.importSignInData param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ImportSignInDataVO vo = BeanUtil.copy(arg, ImportSignInDataVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.importSignInData(vo);
    }

    @RequestMapping(value = "getSignInSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取签到设置")
    public Result<GetSignInSettingResult> getSignInSetting(@RequestBody GetSignInSettingArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.getSignInSetting error param arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetSignInSettingVO vo = BeanUtil.copy(arg, GetSignInSettingVO.class);
        return conferenceService.getSignInSetting(vo);
    }

    @RequestMapping(value = "updateSignInSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新签到设置")
    public Result updateSignInSetting(@RequestBody UpdateSignInSettingArg arg) {
        if (arg.isWrongParam()) {
            log.warn("ConferenceController.updateSignInSetting error param arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpdateSignInSettingVO vo = BeanUtil.copy(arg, UpdateSignInSettingVO.class);
        return conferenceService.updateSignInSetting(vo);
    }

    @RequestMapping(value = "updateSignInSuccessSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "保存签到成功设置", tags = "f-7.8-activities")
    public Result updateSignInSuccessSetting(@RequestBody UpdateSignInSuccessSettingArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.updateSignInSuccessSetting(UserInfoKeeper.getEa(), arg);
    }

    @RequestMapping(value = "getSignInSuccessSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取签到成功设置", tags = "f-7.8-activities")
    public Result getSignInSuccessSetting(@RequestBody GetSignInSuccessSettingArg arg) {
        if (StringUtils.isBlank(arg.getConferenceId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.getSignInSuccessSetting(arg);
    }

    @RequestMapping(value = "getObjectCustomFields", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取活动成员自定义字段", tags = "f-7.8-activities")
    public Result getObjectCustomFields() {
        return conferenceService.getObjectCustomFields(UserInfoKeeper.getEa());
    }

    @RequestMapping(value = "getInvitationCommonSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取邀约通用设置")
    public Result<GetInvitationCommonSettingResult> getInvitationCommonSetting(@RequestBody GetInvitationCommonSettingArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetInvitationCommonSettingVO vo = BeanUtil.copy(arg, GetInvitationCommonSettingVO.class);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.getInvitationCommonSetting(vo);
    }

    @RequestMapping(value = "upsertInvitationCommonSetting", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更改/添加邀约通用设置")
    public Result upsertInvitationCommonSetting(@RequestBody UpsertInvitationCommonSettingArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        UpsertInvitationCommonSettingVO vo = BeanUtil.copy(arg, UpsertInvitationCommonSettingVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return conferenceService.upsertInvitationCommonSetting(vo);
    }

    @RequestMapping(value = "getConferenceId", method = RequestMethod.POST)
    @ApiOperation(value = "通过市场活动id获取会议id")
    public Result<String> getConferenceId(@RequestBody JSONObject arg) {
        String marketingEventId = arg.getString("marketingEventId");
        if (StringUtils.isBlank(marketingEventId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.getConferenceIdByMarketingEventId(UserInfoKeeper.getEa(),marketingEventId);
    }

    @RequestMapping(value = "getConferenceTag", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "通过会议id获取最新一次添加的标签列表")
    public Result<List<TagName>> getConferenceTag(@RequestBody GetConferenceArg arg) {
        if (StringUtils.isBlank(arg.getConferenceId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return conferenceService.getConferenceTag(arg.getConferenceId());
    }
}