package com.facishare.marketing.web.controller;


import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.arg.hexagon.HexagonStatisticArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsResult;
import com.facishare.marketing.api.result.contentmaketing.ContentMarketingEventSimpleVO;
import com.facishare.marketing.api.result.contentmaketing.ContentMarketingEventVO;
import com.facishare.marketing.api.result.contentmaketing.MarketingContentVO;
import com.facishare.marketing.api.result.contentmaketing.SetContentMobileDisplayArg;
import com.facishare.marketing.api.result.kis.TempListAllMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityResult;
import com.facishare.marketing.api.result.qr.QueryQRCodeResult;
import com.facishare.marketing.api.service.ContentMarketingEventService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("contentMarketingEvent")
@Slf4j
@Api(description = "内容营销", tags = "ContentMarketingEventController")
public class ContentMarketingEventController {

    @Autowired
    private ContentMarketingEventService contentMarketingEventService;

    @RequestMapping(value = "listContentMarketingEvent", method = RequestMethod.POST)
    @ApiOperation(value = "分页拉取所有内容营销的市场活动", tags = "322")
    public Result<PageResult<MarketingEventsBriefResult>> listContentMarketingEvent(@RequestBody ListContentMarketingEventArg arg){
        return contentMarketingEventService.listContentMarketingEvent(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(),arg);
    }


    @RequestMapping(value = "listMarketingContent", method = RequestMethod.POST)
    @ApiOperation(value = "获取营销内容列表", tags = "322")
    public Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>> listMarketingContent(@RequestBody ListMarketingContentArg arg){
        return contentMarketingEventService.listMarketingContent(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(),arg.getMarketingEventId(),arg.getObjectTypes(),arg.getPageNo(),arg.getPageSize(), false);
    }

    @RequestMapping(value = "setContentMobileDisplay", method = RequestMethod.POST)
    @ApiOperation("设置营销内容是否在移动端展示")
    public  Result<Void>  setContentMobileDisplay(@RequestBody SetContentMobileDisplayArg arg) {
        return contentMarketingEventService.setContentMobileDisplay(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(),arg);
    }

    @RequestMapping(value = "listMarketingActivityInContentMarketingEvent", method = RequestMethod.POST)
    @ApiOperation(value = "在内容营销场景下的拉取营销活动", tags = "322")
    public  Result<com.facishare.marketing.common.result.PageResult<TempListAllMarketingActivityResult>>  listMarketingActivityInContentMarketingEvent(@RequestBody ListMarketingActivityInContentMarketingEventArg arg) {
        return contentMarketingEventService.listMarketingActivityInContentMarketingEvent(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(),arg);
    }

    @RequestMapping(value = "getOrCreateQrCodeByMarketingEventIdAndObjectInfo", method = RequestMethod.POST)
    @ApiOperation(value = "获取或者创建二维码", tags = "322")
    public Result<GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult> getOrCreateQrCodeByMarketingEventIdAndObjectInfo(@RequestBody GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg arg) {
        return contentMarketingEventService.getOrCreateQrCodeByMarketingEventIdAndObjectInfo(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(),arg);
    }

    @ApiOperation(value = "获取微页面的统计数据")
    @RequestMapping(value = "/getHexagonContent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<MarketingContentVO> getHexagonContent(@RequestBody HexagonStatisticArg arg) {
        if (arg==null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return contentMarketingEventService.getHexagonContent(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

}
