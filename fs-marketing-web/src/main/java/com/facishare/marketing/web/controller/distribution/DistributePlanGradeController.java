package com.facishare.marketing.web.controller.distribution;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.marketing.api.result.distribution.ListDistributePlanResult;
import com.facishare.marketing.api.result.distribution.PageDistributePlanResult;
import com.facishare.marketing.api.result.distribution.QueryPlanGradesByPlanIdResult;
import com.facishare.marketing.api.service.distribution.DistributePlanGradeService;
import com.facishare.marketing.api.vo.AddOrUpdateDistributePlanGradeVO;
import com.facishare.marketing.api.vo.DeleteDistributePlanGradeVO;
import com.facishare.marketing.api.vo.ListDistributePlanVO;
import com.facishare.marketing.api.vo.PageDistributePlanVO;
import com.facishare.marketing.api.vo.QueryPlanGradesByPlanIdVO;
import com.facishare.marketing.api.vo.UpdateDistributePlanStatusVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.distribution.AddOrUpdateDistributePlanGradeArg;
import com.facishare.marketing.web.arg.distribution.DeleteDistributePlanGradeArg;
import com.facishare.marketing.web.arg.distribution.ListDistributePlanArg;
import com.facishare.marketing.web.arg.distribution.PageDistributePlanArg;
import com.facishare.marketing.web.arg.distribution.QueryPlanGradesByPlanIdArg;
import com.facishare.marketing.web.arg.distribution.UpdateDistributePlanStatusArg;
import com.facishare.marketing.web.enums.CheckIndentityAuthScopeEnum;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by ranluch on 2019/4/4.
 */
@Api(description = "分销计划相关接口")
@RestController
@RequestMapping("/distributePlan")
@Slf4j
public class DistributePlanGradeController {
    @Autowired
    private DistributePlanGradeService planGradeService;

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/addOrUpdateDistributePlanGrade", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "新增或者编辑分销计划等级")
    public Result<Void> addOrUpdateDistributePlanGrade(@RequestBody AddOrUpdateDistributePlanGradeArg arg) {
        if (arg == null || arg.isParamError()) {
            log.warn("DistributePlanGradeController addOrUpdateDistributePlanGrade arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        AddOrUpdateDistributePlanGradeVO vo = BeanUtil.copy(arg, AddOrUpdateDistributePlanGradeVO.class);
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return planGradeService.addOrUpdateDistributePlanGrade(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/queryPlanGradesByPlanId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询分销计划等级列表")
    public Result<PageResult<QueryPlanGradesByPlanIdResult>> queryPlanGradesByPlanId(@RequestBody QueryPlanGradesByPlanIdArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getPlanId())) {
            log.warn("DistributePlanGradeController queryPlanGradesByPlanId arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        QueryPlanGradesByPlanIdVO vo = BeanUtil.copy(arg, QueryPlanGradesByPlanIdVO.class);
        return planGradeService.queryPlanGradesByPlanId(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/deleteDistributePlanGrade", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除分销计划等级")
    public Result<Void> deleteDistributePlanGrade(@RequestBody DeleteDistributePlanGradeArg arg) {
        if (arg == null || StringUtils.isEmpty(arg.getId())) {
            log.warn("DistributePlanGradeController deleteDistributePlanGrade arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        DeleteDistributePlanGradeVO vo = BeanUtil.copy(arg, DeleteDistributePlanGradeVO.class);
        return planGradeService.deleteDistributePlanGrade(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/listDistributePlan", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询分销计划列表")
    public Result<ListDistributePlanResult> listDistributePlan(@RequestBody ListDistributePlanArg arg) {
        if (arg == null) {
            log.warn("DistributePlanGradeController listDistributePlan arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        ListDistributePlanVO vo = BeanUtil.copy(arg, ListDistributePlanVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        vo.setAppAdmin(UserInfoKeeper.getIsAppAdmin());
        vo.setTitle(arg.getTitle());
        return planGradeService.listDistributePlan(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/pageDistributePlan", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "分页查询分销计划")
    public Result<PageResult<PageDistributePlanResult>> pageDistributePlan(@RequestBody PageDistributePlanArg arg) {
        if (arg == null) {
            log.warn("DistributePlanGradeController pageDistributePlan arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        PageDistributePlanVO vo = BeanUtil.copy(arg, PageDistributePlanVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        vo.setAppAdmin(UserInfoKeeper.getIsAppAdmin());
        return planGradeService.pageDistributePlan(vo);
    }

    @CheckIdentityTrigger(authScope = CheckIndentityAuthScopeEnum.OPERATOR_OR_APP_ADMIN)
    @RequestMapping(value = "/updateDistributePlanStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改分销计划状态")
    public Result<Void> updateDistributePlanStatus(@RequestBody UpdateDistributePlanStatusArg arg) {
        if (arg == null || arg.isWrongParam()) {
            log.warn("DistributePlanGradeController updateDistributePlanStatus arg error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        UpdateDistributePlanStatusVO vo = BeanUtil.copy(arg, UpdateDistributePlanStatusVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        vo.setAppAdmin(UserInfoKeeper.getIsAppAdmin());
        return planGradeService.updateDistributePlanStatus(vo);
    }

}
