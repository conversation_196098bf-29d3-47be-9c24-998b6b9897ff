package com.facishare.marketing.web.controller.qr;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.qr.QueryCreateChuangKeTieJsSdkOptionResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterByEaListUnitResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterDetailResult;
import com.facishare.marketing.api.result.qr.SyncChuangKeTiePosterResult;
import com.facishare.marketing.api.service.qr.QRPosterService;
import com.facishare.marketing.api.vo.CreateQRPosterVO;
import com.facishare.marketing.api.vo.QueryCreateChuangKeTieJsSdkOptionVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.enums.qr.QRPosterTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.web.arg.qr.*;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("qrPoster")
@Slf4j
@Api(value = "二维码海报")
public class QRPosterController {

    @Autowired
    private QRPosterService qrPosterService;

    @RequestMapping(value = "queryListByEa", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("查询当前企业所有二维码海报")
    public Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByEa(@RequestBody QueryListByEaArg arg) {
        if (arg.getType() == null) {
            arg.setType(QRPosterTypeEnum.NORMAL.getType());
        }

        return qrPosterService.queryListByEa(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),
                arg.getPageSize(), arg.getPageNum(), arg.getTime(), arg.getMarketingEventId(),
                arg.getTitle(), true, arg.getType(), arg.getGroupId());
    }

    @RequestMapping(value = "createQRPoster", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "创建/编辑二维码海报", tags = {"3.1.1","322", "580"})
    public Result<QueryQRPosterByEaListUnitResult> createQRPoster(@RequestBody CreateQRPosterArg arg) {
        if (null == arg.getType()) {
            arg.setType(QRPosterTypeEnum.NORMAL.getType());
        }

        CreateQRPosterVO vo = BeanUtil.copy(arg, CreateQRPosterVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        return qrPosterService.createQRPoster(vo);
    }

    @RequestMapping(value = "deleteQRPoster", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除二维码海报",tags = {"322"})
    public Result deleteQRPoster(@RequestBody DeleteQRPosterArg arg) {
        return qrPosterService.deleteQRPoster(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getQrPosterId());
    }

    @RequestMapping(value = "queryListByForwardTypeAndTargetId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据跳转类型及素材ID查询海报", tags = {"3.1.1","322"})
    public Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByForwardTypeAndTargetId(@RequestBody QueryQRPosterByForwardTypeAndTargetIdArg arg) {
        if (EmptyUtil.isNull(arg.getType())) {
            arg.setType(QRPosterTypeEnum.NORMAL.getType());
        }
        if (EmptyUtil.isNull(arg.isNeedDetail())) {
            arg.setNeedDetail(false);
        }
        return qrPosterService.queryListByForwardTypeAndTargetId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getPageSize(), arg.getPageNum(), arg.getTime(),arg.getMarketingEventId(), arg.getForwardTypes(), arg.getTargetId(), arg.getType(), arg.isNeedDetail());
    }

    @RequestMapping(value = "queryDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询海报详情", tags = {"322", "580"})
    public Result<QueryQRPosterDetailResult> queryDetail(@RequestBody QueryQRPosterDetailArg arg) {
        if (EmptyUtil.isNullForList(arg, arg.getQrPosterId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.queryDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getQrPosterId(), arg.getInviteId(), false);
    }

    @RequestMapping(value = "queryChuangKeTieJsSdkOption", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "查询调用创客贴JSSDK参数", tags = {"580"})
    public Result<QueryCreateChuangKeTieJsSdkOptionResult> queryChuangKeTieJsSdkOption(@RequestBody QueryCreateChuangKeTieJsSdkOptionArg arg){
        if (arg == null || (arg.getKindId() == null && arg.getDesignId() == null)){
            log.info("QRPosterController.queryChuangKeTieJsSdkOption failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        QueryCreateChuangKeTieJsSdkOptionVO vo = new QueryCreateChuangKeTieJsSdkOptionVO();
        vo.setEa(UserInfoKeeper.getEa());
        vo.setUserId(UserInfoKeeper.getFsUserId());
        vo.setKindId(arg.getKindId());
        vo.setDesignId(arg.getDesignId());

        return qrPosterService.queryChuangKeTieJsSdkOption(vo);
    }

    @RequestMapping(value = "syncChuangKeTiePoster", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "同步创客贴素材内容", tags = {"580"})
    public Result<SyncChuangKeTiePosterResult> syncChuangKeTiePoster(@RequestBody SyncChuangKeTiePosterArg arg){
        if (arg == null || StringUtils.isEmpty(arg.getDesignId())){
            log.info("QRPosterController.syncChuangKeTiePoster failed param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return qrPosterService.syncChuangKeTiePoster(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),arg.getDesignId(), arg.getUrl());
    }

    @ApiOperation(value = "创建&编辑海报分组")
    @RequestMapping(value = "/editQRPosterGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<EditObjectGroupResult> editQRPosterGroup(@RequestBody EditObjectGroupArg arg) {
        if (!arg.checkParamValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return qrPosterService.editQRPosterGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "删除海报分组")
    @RequestMapping(value = "/deleteQRPosterGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> deleteQRPosterGroup(@RequestBody DeleteObjectGroupArg arg) {
        if (!arg.checkParamValid()){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.deleteQRPosterGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "海报设置分组")
    @RequestMapping(value = "/setQRPosterGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> setQRPosterGroup(@RequestBody SetObjectGroupArg arg){
        if (org.apache.commons.lang3.StringUtils.isEmpty(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getObjectIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.setQRPosterGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "批量删除海报")
    @RequestMapping(value = "/deleteQRPosterBatch", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> deleteQRPosterBatch(@RequestBody DeleteMaterialArg arg){
        if (CollectionUtils.isEmpty(arg.getIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.deleteQRPosterBatch(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "置顶海报")
    @RequestMapping(value = "/topQRPoster", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> topQRPoster(@RequestBody TopMaterialArg arg){
        if (StringUtils.isBlank(arg.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.topQRPoster(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "取消置顶海报")
    @RequestMapping(value = "/cancelTopQRPoster", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> cancelTopQRPoster(@RequestBody CancelMaterialTopArg arg){
        if (StringUtils.isBlank(arg.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.cancelTopQRPoster(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "海报分组增加适用角色")
    @RequestMapping(value = "/addQRPosterGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> addQRPosterGroupRole(@RequestBody SaveObjectGroupVisibleArg arg){
        if (StringUtils.isBlank(arg.getGroupId()) || CollectionUtils.isEmpty(arg.getRoleIdList())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return qrPosterService.addQRPosterGroupRole(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取海报分组列表")
    @RequestMapping(value = "/listQRPosterGroup", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<ObjectGroupListResult> listQRPosterGroup(@RequestBody ListGroupArg arg){
        return qrPosterService.listQRPosterGroup(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @ApiOperation(value = "获取海报分组适用角色")
    @RequestMapping(value = "/getGroupRole", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<List<String>> getGroupRole(@RequestBody JSONObject arg){
//        @ApiParam("分组ID") @RequestParam("groupId")
        String groupId = arg.getString("groupId");
        return qrPosterService.getGroupRole(groupId);
    }

}
