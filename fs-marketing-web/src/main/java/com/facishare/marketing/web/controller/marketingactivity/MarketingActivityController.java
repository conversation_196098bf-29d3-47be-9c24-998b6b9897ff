package com.facishare.marketing.web.controller.marketingactivity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.InitMarketingActivityArg;
import com.facishare.marketing.api.arg.InvalidMarketingActivityByIdsArg;
import com.facishare.marketing.api.arg.marketingactivity.*;
import com.facishare.marketing.api.result.marketingactivity.*;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.AssertUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.IdArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: dongzhb
 * @date: 2019/2/25
 * @Description:
 */
@RestController
@RequestMapping("marketingActivity")
@Slf4j
@Api(description = "营销活动", tags = "marketingActivity")
public class MarketingActivityController {
    @Autowired
    private MarketingActivityService marketingActivityService;

    @RequestMapping(value = "listMarketingActivity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "营销活动列表", notes = "营销活动列表", tags = {"1.9.1", "530"})
    public Result<PageResult<MarketingActivityResult>> listMarketingActivity(@RequestBody ListMarketingActivityArg arg) {
        Preconditions.checkNotNull(arg.getPageNum(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_60));
        Preconditions.checkNotNull(arg.getPageSize(), I18nUtil.get(I18nKeyEnum.MARK_CONTROLLER_ACTIVITYCONTROLLER_61));
        return marketingActivityService.listMarketingActivity(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }
    
    @RequestMapping(value = "listMarketingActivityByMarketingUserGroupId", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "拉取人群的推广记录", notes = "拉取人群的推广记录", tags = {"600"})
    public Result<PageResult<MarketingActivityResult>> listMarketingActivity(@RequestBody ListMarketingActivityByMarketingUserGroupIdArg arg) {
        return marketingActivityService.listMarketingActivityByMarketingUserGroupId(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }
    

    @RequestMapping(value = "cancelSend", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "取消定时发送", notes = "取消定时发送", tags = {"530"})
    public Result<Boolean> cancelSend(@RequestBody IdArg arg) {
        return marketingActivityService.cancelSend(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @RequestMapping(value = "immediatelySend", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "立即发送", notes = "立即发送")
    public Result<Boolean> immediatelySend(@RequestBody IdArg arg) {
        return marketingActivityService.immediatelySend(UserInfoKeeper.getEa(), arg.getId());
    }

    @RequestMapping(value = "revokeSend", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "撤回发送", notes = "撤回发送")
    public Result<Boolean> revokeSend(@RequestBody IdArg arg) {
        return marketingActivityService.revokeSend(UserInfoKeeper.getEa(), arg.getId());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getMarketingActivityDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取营销活动详情", notes = "获取营销活动详情", tags = "1.9.1")
    public Result<GetMarketingActivityResult> getMarketingActivityDetail(@RequestBody GetMarketingActivityArg getArg) {
        return marketingActivityService.getMarketingActivity(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), getArg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "addMarketingActivity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "新增营销活动", notes = "新增营销活动", tags = "1.9.1")
    public Result<AddMarketingActivityResult> addMarketingActivity(@RequestBody AddMarketingActivityArg addMarketingActivityArg) {
        return marketingActivityService.addMarketingActivity(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), addMarketingActivityArg, true);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getMarketingActivityAuditData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation("获取营销活动审批数据")
    public Result<AddMarketingActivityArg.MarketingActivityAuditData> getMarketingActivityAuditData(@RequestBody AddMarketingActivityArg addMarketingActivityArg) {
        return marketingActivityService.getMarketingActivityAuditData(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), addMarketingActivityArg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateMarketingActivity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改营销活动", notes = "修改营销活动", tags = "1.9.1")
    public Result<UpdateMarketingActivityResult> updateMarketingActivity(@RequestBody UpdateMarketingActivityArg updateArg) {
        return marketingActivityService.updateMarketingActivity(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), updateArg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateBaseInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新全员营销基本信息", notes = "更新全员营销基本信息", tags = "1.9.1")
    public Result<Void> updateBaseInfo(@RequestBody UpdateMarketingActivityBaseInfoArg arg) {
        Result checkResult = arg.checkParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        arg.setEa(UserInfoKeeper.getEa());
        arg.setFsUserId(UserInfoKeeper.getFsUserId());
        return marketingActivityService.updateBaseInfo(arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "updateMarketingActivityDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "修改营销活动详细", notes = "修改营销活动详细", tags = "1.9.1")
    public Result<AddMarketingActivityResult> updateMarketingActivityDetail(@RequestBody AddMarketingActivityArg addMarketingActivityArg) {
        if (addMarketingActivityArg == null || addMarketingActivityArg.getMarketingActivityId() == null  ||  Strings.isNullOrEmpty(addMarketingActivityArg.getMarketingActivityId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return marketingActivityService.updateMarketingActivityDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), addMarketingActivityArg, true);
    }



    @CheckIdentityTrigger
    @RequestMapping(value = "initCrmMarketingActivityByOldEa", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始化营销活动旧企业", notes = "初始化营销活动旧企业", tags = "1.9.1")
    public Result<Integer> initCrmMarketingActivityByOldEa() {
        return marketingActivityService.initCrmMarketingActivityByOldEa();
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getEaInitCrmMarketingActivity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "单独企业初始化营销活动", notes = "单独企业初始化营销活动", tags = "1.9.1")
    public Result<Integer> getEaInitCrmMarketingActivity(@RequestBody InitMarketingActivityArg arg) {
        AssertUtil.checkNotNull(arg.getEa(), "ea ");
        return marketingActivityService.getEaInitCrmMarketingActivity(arg.getEa());
    }

    @RequestMapping(value = "InvalidMarketingActivityByIds", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "作废营销活动数据", notes = "作废营销活动数据", tags = "1.9.1")
    public Result<Void> invalidMarketingActivityByIds(@RequestBody InvalidMarketingActivityByIdsArg arg){
         return marketingActivityService.invalidMarketingActivityByIds(arg);
    }

    /*
    @RequestMapping(value = "deleteMarketingActivity", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "删除营销活动", notes = "删除营销活动", tags = "1.9.1")
    public Result<Void> deleteMarketingActivity(@RequestBody DeleteMarketingActivityArg arg){
        return marketingActivityService.deleteMarketingActivity(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getMarketingActivityId());
    }
    */

    @RequestMapping(value = "spreadMarketingActivityToSpecialEmployeeAgain",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "重新按员工推广", notes = "重新按员工推广", tags = "1.9.1")
    public Result<Void> spreadMarketingActivityToSpecialEmployeeAgain(@RequestBody IdArg arg){
        return marketingActivityService.spreadMarketingActivityToSpecialEmployeeAgain(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @RequestMapping(value = "spreadPartnerActivityToSpecialEmployeeAgain",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "重新通知伙伴", notes = "重新通知伙伴工", tags = "1.9.1")
    public Result<Void> spreadPartnerActivityAgain(@RequestBody IdArg arg){
        return marketingActivityService.spreadPartnerActivityToSpecialEmployeeAgain(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @RequestMapping(value = "spreadMomentActivityAgain",method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "发送通知提醒发布朋友圈", notes = "发送通知提醒发布朋友圈", tags = "1.9.1")
    public Result<Void> spreadMomentActivityAgain(@RequestBody IdArg arg){
        return marketingActivityService.spreadMomentActivityToSpecialEmployeeAgain(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getId());
    }

    @RequestMapping(value = "getSendNotificationStatistic", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取发送通知统计数据", notes = "获取发送通知统计数据")
    public Result<GetSendNotificationStatisticResult> getSendNotificationStatistic(@RequestBody GetSendNotificationStatisticArg arg){
        return marketingActivityService.getSendNotificationStatistic(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @RequestMapping(value = "batchGetSendNotificationStatistic", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "批量获取发送通知统计数据", notes = "批量获取发送通知统计数据")
    public Result<BatchGetSendNotificationStatisticResult> batchGetSendNotificationStatistic(@RequestBody BatchGetSendNotificationStatisticArg arg){
        return marketingActivityService.batchGetSendNotificationStatistic(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "getPreviewData", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取营销活动预览数据", notes = "获取营销活动预览数据", tags = "1.9.1")
    public Result<MarketingActivityPreviewData> getPreviewData(@RequestBody MarketingActivityPreviewArg arg) {
        return marketingActivityService.getPreviewData(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg);
    }
}