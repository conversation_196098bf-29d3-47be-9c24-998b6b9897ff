package com.facishare.marketing.web.arg.live;

import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.common.enums.live.LiveAutoRecordEnum;
import com.facishare.marketing.common.enums.live.LiveChatEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2020/3/19.
 */
@Data
@ToString(callSuper = true)
@Slf4j
public class CreateLiveArg implements Serializable{
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("直播标题")
    private String title;

    @ApiModelProperty("直播简介")
    private String desc;

    @ApiModelProperty("直播封面")
    private String coverTaPath;

    @ApiModelProperty("封面图片后缀")
    private String ext;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("截至时间")
    private Long endTime;

    @ApiModelProperty("讲师名称")
    private String lectureUserName;

    @ApiModelProperty("直播平台")
    private Integer livePlatform;

    @ApiModelProperty("讲师密码")
    private String lecturePassword;

    //是否启动直播聊天 0:开启  1：不开启
    @ApiModelProperty("是否启动直播聊天")
    private Integer chatOn;

    //liveLimit为1是有效
    @ApiModelProperty("直播最大观看次数")
    private Integer maxLiveCount;

    //0为否,1为是(默认为否),是否自动回放
    @ApiModelProperty("是否自动回放")
    private Integer autoRecord;

    @ApiModelProperty("设置标签")
    private TagNameList tagNames;

    @ApiModelProperty("小鹅通直播链接")
    private String xiaoetongLiveUrl;

    @ApiModelProperty("其他直播平台直播链接")
    private String otherPlatformLiveUrl;

    @ApiModelProperty("设置市场活动必填字段")
    private CreateObjectDataModel.Arg createObjectDataModel;

    @ApiModelProperty("是否在小程序活动列表展示")
    private boolean showActivityList;

    @ApiModelProperty("外部平台直播id（小鹅通/保利威/目睹）")
    private String xiaoetongLiveId;

    @ApiModelProperty("视频号头像")
    private String channelsAvatar;

    @ApiModelProperty("视频号名称")
    private String channelsName;

    @ApiModelProperty("视频号id")
    private String channelsId;

    @ApiModelProperty("父级市场活动id")
    private String parentId;

    @ApiModelProperty("市场活动默认模板id")
    private String marketingTemplateId;

    @ApiModelProperty("原图APath")
    private String originalImageAPath;

    @ApiModelProperty("cdn截图位置")
    private List<PhotoCutOffset> cutOffsetList;

    @ApiModelProperty("关联的账号id,目前只有视频号需要关联")
    private String associatedAccountId;

    public boolean isWrongParam(){
        if (StringUtils.isBlank(title)){
            log.warn("LiveController.createLive title cannot be null");
            return true;
        }

        if (lecturePassword != null && lecturePassword.length() != 4){
            log.warn("LiveController.createLive lecture password size not 4");
            return true;
        }

        if (title.length() > 30){
            log.warn("LiveController.createLive title length beyond 30");
            return true;
        }

        if (startTime == null || endTime == null){
            log.warn("LiveController.createLive live startTime and endTime cannot be null");
            return true;
        }

        if (StringUtils.isBlank(coverTaPath)){
            log.warn("LiveController.createLive live coverPath cannot be null");
            return true;
        }

        if (coverTaPath.startsWith("TA_") && StringUtils.isEmpty(ext)){
            log.warn("LiveController.createLive live ext is null livePlatform:{}", livePlatform);
            return true;
        }

        if (maxLiveCount != null && maxLiveCount.intValue() > 5000){
            log.warn("LiveController.createLive max view count > 5000");
            return true;
        }

        if (livePlatform == null || (LivePlatformEnum.getEnumByType(livePlatform) == null)){
            log.warn("LiveController.createLive live platform value error livePlatform:{}", livePlatform);
            return true;
        }

        //选择其他直播平台
        if (livePlatform == LivePlatformEnum.OTHER_PLATFORM.getType()){
            if (StringUtils.isBlank(otherPlatformLiveUrl)){
                log.warn("LiveController.createLive live otherPlatformLiveUrl is null livePlatform:{}", livePlatform);
                return true;
            }
        } else if((livePlatform == LivePlatformEnum.POLYV.getType() ||livePlatform == LivePlatformEnum.XIAOETONG.getType()) && StringUtils.isEmpty(xiaoetongLiveId)){
            log.warn("LiveController.createLive xiaoetong or polyv info is null livePlatform:{}", livePlatform);
            return true;
        } else {
            if (chatOn == null || (chatOn.intValue() != LiveChatEnum.CLOSE_CHAT.getStatus() && chatOn.intValue() != LiveChatEnum.OPEN_CHAT.getStatus())){
                log.warn("LiveController.createLive live chatOn value error chatOn:{} livePlatform:{}", chatOn, livePlatform);
                return true;
            }

            if (autoRecord == null || (autoRecord.intValue() != LiveAutoRecordEnum.DEFAULT_RECORD.getStatus() &&
                                      autoRecord.intValue() != LiveAutoRecordEnum.NO_RECORD.getStatus())){
                log.warn("LiveController.createLive live autoRecord value error autoRecord:{}", autoRecord);
                return true;
            }
        }


        return false;
    }
}
