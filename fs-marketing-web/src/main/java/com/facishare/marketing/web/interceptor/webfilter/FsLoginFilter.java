package com.facishare.marketing.web.interceptor.webfilter;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.distribution.OperatorService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.RequestUtil;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import java.io.IOException;
import java.util.Optional;
import java.util.Set;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * Created by huangzhw on 2016/9/5.
 */
@Slf4j
@Component("fsLoginFilter")
public class FsLoginFilter extends OncePerRequestFilter {
    private static final String CEP_AUTH_PREFIX_URLS_TO_IGNORE = "/cepAuth";
    private static final String PREFIX_URLS_TO_ONLY_ACCESS_LOCAL = "/marketing/migrate";
    private static final Set<String> URLS_TO_IGNORE = ImmutableSet
            .of("/marketing/v2/api-docs", "/marketing/pay/callback", "/marketing/send/statusCallback", "/marketing/distributor/queryDistributorGradeByOrder", "/marketing/file/redirectDownload", "/marketing/userMarketingAccount/addTagToCrmData", "/marketing/send/sendSms", "/marketing/apply/checkSmsStatus",
                    "/marketing/userMarketingTag/searchTags", "/marketing/email/webhook", "/marketing/wechatService/sendTemplateMessage", "/inner/wxThirdPlatformInnerSupport", "/inner/wxThirdCloudInner", "/inner/wxThirdCallback/cloudCallback", "/inner/wxThirdAuth/authentication", "/marketing/userMarketingAccount/deleteTagsToCrmData",
                    "/marketing/headlinesCallback/authCallback", "/marketing/pay/querySmsOrderById", "/marketing/wxCoupon/callback", "/marketing/xiaoetong/xiaoetongMessageCallback", "/marketing/officialWebsite/createOrUpdateWebsiteFanQrCode", "/marketing/websiteThirdCustomer/kfCallback", "marketing/wxThirdAuth/qywxAppAgentCallback",
                    "marketing/wxOfficialAccounts/createOfficialWebsiteWxQrCode", "marketing/wxOfficialAccounts/getMemberInfo", "marketing/wxOfficialAccounts/queryOfficialWebsiteLoginWxQrCode", "marketing/wxOfficialAccounts/createOfficialWebsiteLoginWxQrCode", "/marketing/xiaoetong/shareXiaotongAccessToken", "/marketing/tencentCallback/authCallback",
                    "ocpc/jointDebugging", "/marketing/baidu/authCallback", "/marketing/baidu/inner/authCallback", "/marketing/send/sendSmsByFunction", "/marketing/marketingTrigger/outTriggerByFunction","/marketing/marketingTrigger/getOutSopQywxGroupSendResult", "/marketing/wxCoupon/queryCouponPlanList", "/marketing/marketingPlugin/isOpenMarketingAdPlugin", "/marketing/ocpc/isOpenInvalidRebate",
                    "/marketing/shanShanEdit/shanShanEditCallBack", "/marketing/shanShanEdit/handleShanShanCallback", "/marketing/setting/getVersionAuditStatus", "/marketing/officialWebsite/getCustomizeFormDataById", "/userMarketing/customize/action", "/marketing/userMarketingAccount/associateExternalMarketingAccountByUnionIdAndOpenId", "/marketing/adThridCallBack/tencent/recvData", "/marketing/qywxBind/getQywxAccessToken",
                    "/fanQrCode/customize/action/create", "out/userMarketing/action", "userMarketingAccount/getInfoByIdentify", "marketingFunction/sendQywxApplicationMessage", "marketingFunction/queryRelationInfo", "marketingFunction/updateQywxExternalUserRemark","marketingFunction/batchUpdateTagByDataId"
            );
    private static final Set<String> PREFIX_URLS_TO_IGNORE = ImmutableSet.of("/marketing/wxThirdCallback", "/marketing/wxThirdAuth/authRedirect", "/marketing/payResultCallback/miniAppPaid", "marketing/adThridCallBack/recvData", "adThridCallBack/inner/recvData", "/fs/pay", "open/material", "/marketing/wxThirdCloudInner", "/marketing/cepAuth");
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Autowired
    private SettingService settingService;
    @Value("${marketing_appid}")
    private String appId;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private OperatorService operatorService;
    @ReloadableProperty("Access-Control-Allow-Origin")
    private String accessControlAllowOrigin;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        accessControllAllownConfig(response);
        String requestURI = request.getRequestURI();
        log.info("FsLoginFilter.doFilterInternal,requestURI:{}", requestURI);
        if (isMigrate(request) || isCepAuth(request)) {
            filterChain.doFilter(request, response);
            return;
        }
        if (isConnector(request)) {
            filterChain.doFilter(request, response);
            return;
        }
        String enterpriseAccount = RequestUtil.getEa(request);
        Integer enterpriseId = RequestUtil.getTenantId(request);
        Integer userId = RequestUtil.getUserId(request);
        if (StringUtils.isBlank(enterpriseAccount) || userId == null) {
            /**完全切换前兼容用**/
            if (URLS_TO_IGNORE.stream().anyMatch(requestURI::contains)) {
                filterChain.doFilter(request, response);
                return;
            }
            for (String prefixUrl : PREFIX_URLS_TO_IGNORE) {
                if (requestURI.contains(prefixUrl)) {
                    filterChain.doFilter(request, response);
                    return;
                }
            }

            Optional<String> optionalFsCookie = doGetFsCookie(request);
            if (optionalFsCookie.isPresent()) {
                CookieToAuth.Argument argument = new CookieToAuth.Argument();
                argument.setCookie(optionalFsCookie.get());
                argument.setIp(request.getRemoteAddr());
                CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
                ValidateStatus validateStatus = result.getValidateStatus();
                if (validateStatus.is(ValidateStatus.NORMAL)) {
                    AuthXC authXC = result.getBody();
                    enterpriseAccount = authXC.getEnterpriseAccount();
                    enterpriseId = authXC.getEnterpriseId();
                    userId = authXC.getEmployeeId();
                }
            }
        }

        if (!Strings.isNullOrEmpty(enterpriseAccount) && userId != null) {
            boolean isAppAdmin = false;
            boolean isOperator = false;

            String fsUserId = "E." + enterpriseAccount + "." + userId;
            // 让isAppAdmin与isOperator也支持走灰度服务
            UserInfoKeeper.setUserInfo(enterpriseAccount, enterpriseId, userId);

            BaseResult<Boolean> baseResult = openAppAdminService.isAppAdmin(fsUserId, appId);
            if (!baseResult.isSuccess()) {
                log.error("operatorService.isOperator failed, ea={}, userId={}", enterpriseAccount, userId);
            }
            if (BooleanUtils.isTrue(baseResult.getResult())) {
                isAppAdmin = true;
            }
            Result<Boolean> operatorResult = operatorService.isOperator(enterpriseAccount, userId);
            if (!operatorResult.isSuccess()) {
                log.error("operatorService.isOperator failed, ea={}, userId={}", enterpriseAccount, userId);
            }
            if (BooleanUtils.isTrue(operatorResult.getData())) {
                isOperator = true;
            }
            UserInfoKeeper.setUserInfo(enterpriseAccount, enterpriseId, userId, isAppAdmin, isOperator);
            try {
                filterChain.doFilter(request, response);
            } finally {
                UserInfoKeeper.clearUserInfo();
            }
            return;
        }

        doWriteErrorMsg(response, new Result<>(SHErrorCode.INVALID_FS_LOGIN, SHErrorCode.INVALID_FS_LOGIN.getErrorMessage()));
    }

    private boolean isConnector(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/hub/connector") || requestURI.contains("/hub/check")) {
            return true;
        }
        return false;
    }

    private void doWriteErrorMsg(HttpServletResponse response, Result<?> result) throws IOException {
        response.addHeader("Content-Type", "application/json;charset=utf-8");
        response.getWriter().write(GsonUtil.toJson(result));
    }

    private Optional<String> doGetFsCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            Optional<String> cookieXC = doGetCookieByName(cookies, "FSAuthXC");
            if (cookieXC.isPresent()) {
                return cookieXC;
            }
            Optional<String> cookieX = doGetCookieByName(cookies, "FSAuthX");
            if (cookieX.isPresent()) {
                return cookieX;
            }
        }
        return Optional.empty();
    }

    private Optional<String> doGetCookieByName(Cookie[] cookies, String cookieName) {
        for (Cookie cookie : cookies) {
            if (cookieName.equalsIgnoreCase(cookie.getName())) {
                return Optional.of(cookie.getValue());
            }
        }
        return Optional.empty();
    }

    private void accessControllAllownConfig(HttpServletResponse response){
        response.setHeader("Access-Control-Allow-Origin", accessControlAllowOrigin);
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Headers", "content-type, token");
        response.setHeader("Access-Control-Request-Method", "GET, POST");
        response.setHeader("Access-Control-Max-Age", "86400");
    }

    private boolean isMigrate(HttpServletRequest request){
        String requestURI = request.getRequestURI();
        String remoteIP = request.getRemoteAddr();
        if (requestURI.contains(PREFIX_URLS_TO_ONLY_ACCESS_LOCAL) && remoteIP.equals("127.0.0.1")){
            return true;
        }
        return false;
    }

    private boolean isCepAuth(HttpServletRequest request){
        String requestURI = request.getRequestURI();
        if (requestURI.contains(CEP_AUTH_PREFIX_URLS_TO_IGNORE)){
            return true;
        }
        return false;
    }
}
