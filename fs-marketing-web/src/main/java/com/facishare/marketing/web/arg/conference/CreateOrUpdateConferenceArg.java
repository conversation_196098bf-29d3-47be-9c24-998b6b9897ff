package com.facishare.marketing.web.arg.conference;

import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.typehandlers.value.MapLocation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import java.io.Serializable;
import java.util.List;


/**
 * Created by zhengh on 2020/10/30.
 */
@Data
public class CreateOrUpdateConferenceArg implements Serializable{
    @ApiModelProperty("会议id")
    private String conferenceId;

    @ApiModelProperty("会议标题")
    private String title;

    @ApiModelProperty("会议开始时间")
    private Long startTime;

    @ApiModelProperty("会议结束时间")
    private Long endTime;

    @ApiModelProperty("会议地点")
    private String location;

    @FilterLog
    @ApiModelProperty("会议详情")
    private String conferenceDetails;

    @ApiModelProperty("会议封面")
    private String coverImagePath;

    @ApiModelProperty("是否更新会议详情")
    private boolean updateConferenceDetails;

    @ApiModelProperty("是否更新会议封面")
    private boolean updateCoverImage;

    @ApiModelProperty("是否在小程序活动列表展示")
    private boolean showActivityList;

    @ApiModelProperty("设置市场活动必填字段")
    private CreateObjectDataModel.Arg createObjectDataModel;

    @ApiModelProperty("地图组件上的会议地点")
    private String mapAddress;

    @ApiModelProperty("地图经纬度")
    private MapLocation mapLocation;

    @ApiModelProperty("父级市场活动id")
    private String parentId;

    @ApiModelProperty("市场活动默认模板id")
    private String marketingTemplateId;

    @ApiModelProperty("原图APath")
    private String originalImageAPath;

    @ApiModelProperty("cdn截图位置")
    private List<PhotoCutOffset> cutOffsetList;

    public boolean checkParam(){
        if (StringUtils.isBlank(title) || startTime == null || endTime == null || StringUtils.isBlank(location)
                || StringUtils.isBlank(coverImagePath) || StringUtils.isBlank(conferenceDetails)
                || createObjectDataModel == null){
            return false;
        }

        return true;
    }
}
