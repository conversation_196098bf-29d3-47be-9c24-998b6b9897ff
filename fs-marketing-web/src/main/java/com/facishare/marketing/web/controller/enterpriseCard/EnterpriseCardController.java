package com.facishare.marketing.web.controller.enterpriseCard;

import com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.AddEnterpriseDefaultCardArg;
import com.facishare.mankeep.api.outService.result.EnterpriseDefaultCard.InitEnterpriseDefaultCardStatusResult;
import com.facishare.mankeep.api.outService.result.EnterpriseDefaultCard.QueryDefaultProductListResult;
import com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService;
import com.facishare.mankeep.api.result.QueryTradeListResult;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.result.card.GetEnterpriseDefaultCardResult;
import com.facishare.marketing.api.result.fsBind.ExportBindCardDetailResult;
import com.facishare.marketing.api.result.fsBind.GetEmployeeCardQrCodeResult;
import com.facishare.marketing.api.result.qywx.card.CardInfoResult;
import com.facishare.marketing.api.service.FsBindService;
import com.facishare.marketing.api.service.card.EnterpriseDefaultCardService;
import com.facishare.marketing.api.service.qywx.CardService;
import com.facishare.marketing.api.vo.UpdateEnterpriseDefaultProductVO;
import com.facishare.marketing.api.vo.card.GetEnterpriseDefaultCardVo;
import com.facishare.marketing.common.enums.qywx.UserCardOpenStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.web.annotation.CheckIdentityTrigger;
import com.facishare.marketing.web.arg.card.GetEnterpriseDefaultCardArg;
import com.facishare.marketing.web.arg.enterpriseCard.AddEnterpriseCardArg;
import com.facishare.marketing.web.arg.enterpriseCard.BatchUpdateUserCardArg;
import com.facishare.marketing.web.arg.enterpriseCard.ExportBindCardDetailArg;
import com.facishare.marketing.web.arg.enterpriseCard.GetEmployeeCardQrCodeArg;
import com.facishare.marketing.web.arg.enterpriseCard.QueryMyBaseCardInfoWithFsInfoArg;
import com.facishare.marketing.web.arg.enterpriseCard.UpdateEnterpriseDefaultProductArg;
import com.facishare.marketing.web.interceptor.UserInfoKeeper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @创建人 zhengliy
 * @创建时间 2019/1/15 19:02
 * @描述
 */
@RestController
@RequestMapping("enterpriseCard")
@Slf4j
@Api(description = "企业员工名片", tags = "1.8.2")
public class EnterpriseCardController {
    @Autowired
    OutEnterpriseDefaultCardService outEnterpriseDefaultCardService;

    @Autowired
    private EnterpriseDefaultCardService enterpriseDefaultCardService;

    @Autowired
    private FsBindService fsBindService;

    @Autowired
    private CardService cardService;

    @CheckIdentityTrigger
    @RequestMapping(value = "getInitEnterpriseDefaultCardStatus", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取初始化企业名片状态", notes = "获取初始化企业名片状态")
    public Result<InitEnterpriseDefaultCardStatusResult> getInitEnterpriseDefaultCardStatus() {
        String ea = UserInfoKeeper.getEa();
        ModelResult<InitEnterpriseDefaultCardStatusResult> result = outEnterpriseDefaultCardService.getInitEnterpriseDefaultCardStatus(ea);
        return new Result<>(result.getErrCode(), result.getErrMsg(), result.getData());
    }

    /**
     * 初始与修改预设名片，包括产品信息
     * @param arg
     * @return
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "addEnterpriseDefaultCard", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始与修改预设名片", notes = "初始与修改预设名片")
    public Result<Void> addEnterpriseDefaultCard(@RequestBody AddEnterpriseCardArg arg) {
        if (arg.isWrongParam()) {
            log.warn("EnterpriseCardController.addEnterpriseDefaultCard wrong params arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        AddEnterpriseDefaultCardArg vo = BeanUtil.copy(arg, AddEnterpriseDefaultCardArg.class);
        String ea = UserInfoKeeper.getEa();
        Integer userId = UserInfoKeeper.getFsUserId();
        vo.setEa(ea);
        vo.setUserId(userId);
        Result<List<String>> newPhotoResult = cardService.changePhotosType(vo.getPhotos(),ea);
        if (newPhotoResult != null && CollectionUtils.isNotEmpty(newPhotoResult.getData())) {
            vo.setPhotos(newPhotoResult.getData());
        }
        ModelResult<Void> result = outEnterpriseDefaultCardService.addEnterpriseDefaultCard(vo);
        return new Result<>(result.getErrCode(), result.getErrMsg(), result.getData());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "batchUpdateUserCard", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "批量更新员工名片", notes = "批量更新员工名片")
    public Result batchUpdateUserCard(@RequestBody BatchUpdateUserCardArg arg) {
        if (arg.isWrongParam()) {
            log.warn("EnterpriseCardController.batchUpdateUserCard wrong param arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return fsBindService.batchUpdateUserCard(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getFsUserId(), arg.getUpdateType());
    }

    @RequestMapping(value = "exportBindCardDetail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "导出企业名片开通详情", notes = "导出企业名片开通详情")
    public void exportBindCardDetail(ExportBindCardDetailArg arg, HttpServletResponse httpServletResponse) throws IOException {
        if (arg.getIsOpen() == null) {
            arg.setIsOpen(UserCardOpenStatusEnum.ALL.getStatus());
        }
        Result<ExportBindCardDetailResult> exportBindCardDetailResult = fsBindService.exportBindCardDetail(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(), arg.getIsOpen());
//        if (!exportBindCardDetailResult.isSuccess()) {
//            log.warn("EnterpriseCardController.exportBindCardDetail error result :{}", exportBindCardDetailResult);
//            return;
//        }
//        ExportBindCardDetailResult result = exportBindCardDetailResult.getData();
//        StringBuilder filename = new StringBuilder(result.getFileName()).append(".xlsx");
//        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
//        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename.toString());
//        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "openSheet");
//        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
//        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
//        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
//        xssfSheet.setDefaultColumnWidth(20);
//        ExcelUtil.fillContent(xssfSheet, result.getTitleList(), result.getCardInfoList());
//        httpServletResponse.setContentType(excelConfigMap.get(ExcelConfigEnum.CONTENT_TYPE).toString());
//        httpServletResponse.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(filename.toString(), "UTF-8"));
//        OutputStream bos = httpServletResponse.getOutputStream();
//        xssfWorkbook.write(bos);
//        httpServletResponse.flushBuffer();
//        bos.flush();
//        bos.close();
    }

    @RequestMapping(value = "getEmployeeCardQrCode", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取员工名片二维码", notes = "获取员工名片二维码")
    public Result<GetEmployeeCardQrCodeResult> getEmployeeCardQrCode(@RequestBody GetEmployeeCardQrCodeArg arg) {
        if (arg.isWrongParam()) {
            log.warn("EnterpriseCardController.getEmployeeCardQrCode param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fsBindService.getEmployeeCardQrCode(UserInfoKeeper.getEa(), arg.getQywxUserId(), arg.getFsUserId());
    }



    /**
     * 初始与修改产品列表
     * @param arg
     * @return
     */
    @CheckIdentityTrigger
    @RequestMapping(value = "addProducts", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "初始与修改产品列表", notes = "初始与修改产品列表", tags = "520")
    public Result<Void> addProducts(@RequestBody AddEnterpriseCardArg arg) {
        if (arg.isWrongParam()) {
            log.warn("EnterpriseCardController.addEnterpriseDefaultCard wrong params arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        AddEnterpriseDefaultCardArg vo = BeanUtil.copy(arg, AddEnterpriseDefaultCardArg.class);
        String ea = UserInfoKeeper.getEa();
        Integer userId = UserInfoKeeper.getFsUserId();
        vo.setEa(ea);
        vo.setUserId(userId);
        ModelResult<Void> result = outEnterpriseDefaultCardService.addProducts(vo);
        return new Result<>(result.getErrCode(), result.getErrMsg(), result.getData());
    }



    @CheckIdentityTrigger
    @RequestMapping(value = "getEnterpriseCard", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取预设名片信息", notes = "获取预设名片信息")
    public Result<GetEnterpriseDefaultCardResult> getEnterpriseDefaultCard(@RequestBody GetEnterpriseDefaultCardArg arg) {
        Result checkResult = arg.validateParam();
        if (!checkResult.isSuccess()) {
            return checkResult;
        }

        GetEnterpriseDefaultCardVo vo = BeanUtil.copy(arg, GetEnterpriseDefaultCardVo.class);
        vo.setEa(UserInfoKeeper.getEa());
        vo.setFsUserId(UserInfoKeeper.getFsUserId());
        return enterpriseDefaultCardService.getEnterpriseDefaultCard(vo);
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "queryDefaultProductList", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取需要预设或已经预设的产品", notes = "获取需要预设或已经预设的产品")
    public Result<QueryDefaultProductListResult> queryDefaultProductList(@RequestBody com.facishare.marketing.web.arg.enterpriseCard.QueryDefaultProductListArg arg) {
        if (arg.isWrongParam()) {
            log.warn("EnterpriseCardController.queryDefaultProductList wrong params arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        String ea = UserInfoKeeper.getEa();
        com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.QueryDefaultProductListArg vo = BeanUtil
            .copy(arg, com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.QueryDefaultProductListArg.class);
        vo.setEa(ea);
        ModelResult<QueryDefaultProductListResult> result = outEnterpriseDefaultCardService.queryDefaultProductList(vo);
        return new Result<>(result.getErrCode(), result.getErrMsg(), result.getData());
    }

    @CheckIdentityTrigger
    @RequestMapping(value = "queryAllTradeInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "获取企业码", notes = "获取企业码")
    public Result<QueryTradeListResult> queryAllTradeInfo() {
        ModelResult<QueryTradeListResult> result = outEnterpriseDefaultCardService.queryAllTradeInfo();
        return new Result<>(result.getErrCode(), result.getErrMsg(), result.getData());
    }


    @RequestMapping(value = "queryMyBaseCardInfoWithFsInfo", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "根据纷享身份获取用户名片信息", notes = "根据纷享身份获取用户名片信息")
    public Result<CardInfoResult> queryMyBaseCardInfoWithFsInfo(@RequestBody QueryMyBaseCardInfoWithFsInfoArg arg) {
        if (arg.isWrongParam()) {
            log.warn("EnterpriseCardController.queryMyBaseCardInfoWithFsInfo param error arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return cardService.queryMyBaseCardInfoWithFsInfo(UserInfoKeeper.getEa(), arg.getFsUserId());
    }

    @RequestMapping(value = "updateEnterpriseDefaultProduct", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ApiOperation(value = "更新企业预设/动态产品", notes = "更新企业预设/动态产品")
    public Result updateEnterpriseDefaultProduct(@RequestBody UpdateEnterpriseDefaultProductArg arg) {
        UpdateEnterpriseDefaultProductVO vo = BeanUtil.copy(arg, UpdateEnterpriseDefaultProductVO.class);
        vo.setEa(UserInfoKeeper.getEa());
        return cardService.updateEnterpriseDefaultProduct(vo);
    }
}
