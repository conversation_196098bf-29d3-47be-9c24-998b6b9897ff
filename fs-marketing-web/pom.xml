<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-marketing</artifactId>
    <groupId>com.facishare.marketing</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fs-marketing-web</artifactId>
  <packaging>war</packaging>


  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-wechat-rest-api</artifactId>
      <version>1.1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-api</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>morphia</artifactId>
          <groupId>org.mongodb.morphia</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi</artifactId>
      <version>3.17</version>
    </dependency>

    <dependency>
    <artifactId>poi-ooxml</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>poi</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
      <groupId>org.apache.poi</groupId>
      <version>3.17</version>
    </dependency>

    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
    <!-- Dubbo -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>

    <!-- Netty: Required by dubbo-->
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
    </dependency>

    <!-- 接入dubbo监控 -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>core-filter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>rpc-trace</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 配置中心 -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>config-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
    </dependency>
    <!-- swagger -->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <version>2.8.0</version>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.5.14</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-active-session-manage-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-open-app-center-api</artifactId>
      <version>1.0.58-SNAPSHOT</version>
    </dependency>
    <!-- fileUpload -->
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.3.3</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-wechat-union-core-api</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>asm</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fs-enterprise-relation-outapi</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-collectionschema</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-core</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protostuff-runtime</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
        <exclusion>
          <artifactId>i18n-client</artifactId>
          <groupId>com.fxiaoke</groupId>
        </exclusion>
        <exclusion>
          <artifactId>bcprov-jdk16</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 客脉 -->
    <dependency>
      <groupId>com.facishare.mankeep</groupId>
      <artifactId>fs-mankeep-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>morphia</artifactId>
          <groupId>org.mongodb.morphia</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mongo-java-driver</artifactId>
          <groupId>org.mongodb</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-excelant</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare.marketing</groupId>
      <artifactId>fs-marketing-statistic-outapi</artifactId>
      <version>1.0.2-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-wechat-dubbo-rest-outer-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>bcprov-jdk16</artifactId>
          <groupId>org.bouncycastle</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>poi-ooxml</artifactId>
          <groupId>org.apache.poi</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-common-mq</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>fs-dingtalk-api</artifactId>
      <version>1.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.erpdss</groupId>
      <artifactId>connector-spring-support</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>

  </dependencies>
</project>
