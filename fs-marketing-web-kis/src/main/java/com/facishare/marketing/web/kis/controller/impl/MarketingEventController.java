package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.GetEntityOpennessArg;
import com.facishare.marketing.api.arg.ListMaterialsArg;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.result.GetEntityOpennessResult;
import com.facishare.marketing.api.service.CrmService;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.web.kis.controller.IMarketingEventController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 * Created  By zhoux 2019/08/19
 **/
@Controller
@Slf4j
public class MarketingEventController implements IMarketingEventController {

    @Autowired
    private MarketingEventService marketingEventService;
    @Autowired
    private CrmService  crmService;

    @Override
    public Result<PageResult<AbstractMaterialData>> listMaterials(ListMaterialsArg arg) {
        return Result.newSuccess(marketingEventService.listMaterials(UserInfoKeeper.getEa(), UserInfoKeeper.getFsUserId(),arg));
    }
}
