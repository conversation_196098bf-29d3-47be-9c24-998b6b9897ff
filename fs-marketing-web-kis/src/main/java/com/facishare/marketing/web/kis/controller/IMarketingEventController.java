package com.facishare.marketing.web.kis.controller;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.marketing.api.arg.GetEntityOpennessArg;
import com.facishare.marketing.api.arg.ListMaterialsArg;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created  By zhoux 2019/08/19
 **/
@FcpService("marketingEvent")
@RequestMapping("/web/marketingEvent")
public interface IMarketingEventController {

    /**
     * 拉取素材
     * @param arg
     * @return
     */
    @FcpMethod("listMaterials")
    @ResponseBody
    @RequestMapping(value = "/listMaterials", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<PageResult<AbstractMaterialData>> listMaterials(@RequestBody ListMaterialsArg arg);
}
