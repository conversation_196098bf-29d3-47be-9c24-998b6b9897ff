package com.facishare.marketing.web.kis.controller.impl;

import com.facishare.marketing.api.arg.ListMarketingContentArg;
import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.result.contentmaketing.MarketingContentVO;
import com.facishare.marketing.api.result.hexagon.GetPageDetailResult;
import com.facishare.marketing.api.service.ContentMarketingEventService;
import com.facishare.marketing.common.enums.IdentityCheckTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.web.kis.annotation.IdentityCheckingTrigger;
import com.facishare.marketing.web.kis.controller.IContentMarketingEventController;
import com.facishare.marketing.web.kis.interceptor.UserInfoKeeper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

@Controller
@Slf4j
public class ContentMarketingEventController implements IContentMarketingEventController {

    @Autowired
    private ContentMarketingEventService contentMarketingEventService;

    @Override
    @IdentityCheckingTrigger(authScope = {IdentityCheckTypeEnum.FXIAOKE})
    public Result<PageResult<MarketingContentVO>> listMarketingContent(@RequestBody ListMarketingContentArg arg) {
        if (arg == null){
            log.info("ContentMarketingEventController.listMarketingContent failed arg == null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return contentMarketingEventService.listMarketingContent(UserInfoKeeper.getEa(),UserInfoKeeper.getFsUserId(),arg.getMarketingEventId(),arg.getObjectTypes(),arg.getPageNo(),arg.getPageSize(), true);
    }
}
