package com.facishare.marketing.web.kis.arg.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2021/02/03
 **/
@Data
public class GetConferenceTimeStatusArg implements Serializable {

    @ApiModelProperty("会议id")
    private String conferenceId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(conferenceId);
    }

}
