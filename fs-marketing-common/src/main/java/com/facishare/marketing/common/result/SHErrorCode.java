package com.facishare.marketing.common.result;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
public enum SHErrorCode {
    // 成功
    SUCCESS(0, I18nKeyEnum.MARK_RESULT_SHERRORCODE_4),
    // 错误
    PARAMS_ERROR(-1, I18n<PERSON>eyEnum.MARK_RESULT_SHERRORCODE_6),
    SERVER_BUSY(-2, I18nKeyEnum.MARK_RESULT_SHERRORCODE_7),
    SYSTEM_ERROR(-3, I18nKeyEnum.MARK_RESULT_SHERRORCODE_8),
    OPERATE_DB_FAIL(-4, I18n<PERSON>eyEnum.MARK_RESULT_SHERRORCODE_9),
    UNKNOWN(-5, I18nKeyEnum.MARK_RESULT_SHERRORCODE_10),
    JSO<PERSON>_FAIL(-6, I18n<PERSON>ey<PERSON>num.MARK_RESULT_SHERRORCODE_11),
    NO_DATA(-7, I18n<PERSON><PERSON>Enum.MARK_RESULT_SHERRORCODE_12),
    PH<PERSON>E_NUMBER_ILLEGAL(-8, I18nKeyEnum.MARK_RESULT_SHERRORCODE_13),
    ILLEGAL_REQUEST(-9, I18nKeyEnum.ILLEGAL_REQUEST),
    PHONE_NUMBER_CAPTCHA(-801, I18nKeyEnum.MARK_RESULT_SHERRORCODE_65),
    PHONE_NUMBER_CAPTCHA_ERROR(-802, I18nKeyEnum.MARK_RESULT_SHERRORCODE_68),
    SEND_PHONE_SMS_TOO_FREQUENTLY(-9, I18nKeyEnum.MARK_RESULT_SHERRORCODE_14),
    NOT_APP_MANAGER(-10, I18nKeyEnum.MARK_RESULT_SHERRORCODE_15),
    NO_AUTH(-11, I18nKeyEnum.MARK_RESULT_SHERRORCODE_16),
    JSON_DESERIALIZATION_FAIL(-12, I18nKeyEnum.MARK_RESULT_SHERRORCODE_17),
    THIRD_APPLICATION_ERROR(-13, I18nKeyEnum.MARK_RESULT_SHERRORCODE_18),
    THREAD_NOT_FINISHED(-14, I18nKeyEnum.MARK_RESULT_SHERRORCODE_19),
    NOT_BUG(-20, I18nKeyEnum.MARK_RESULT_SHERRORCODE_20),
    GRAY_ENTERPRISE(7, I18nKeyEnum.MARK_RESULT_SHERRORCODE_21),
    IP_ADDRESS_NOT_FOUND(-21, I18nKeyEnum.MARK_RESULT_SHERRORCODE_22),
    IDENTITY_INVAILED(-22, I18nKeyEnum.MARK_RESULT_SHERRORCODE_23),
    DATA_IS_EMPTY_OR_MORE_THAN_ONE(-23, I18nKeyEnum.MARK_RESULT_SHERRORCODE_24),
    DATA_MORE_THAN_ONE(-24, I18nKeyEnum.MARK_RESULT_SHERRORCODE_25),
    CREATE_SHORT_URL_FAILED(-25, I18nKeyEnum.MARK_RESULT_SHERRORCODE_26),
    PHONE_VERIFY_CODE_ERROR(-26, I18nKeyEnum.MARK_RESULT_SHERRORCODE_27),
    MARKETING_EVENT_EXIST(-27, I18nKeyEnum.MARK_RESULT_SHERRORCODE_28),
    PATH_ERROR(-28, I18nKeyEnum.MARK_RESULT_SHERRORCODE_29),
    SET_SHARE_RESOURCE_FAILED(-29, I18nKeyEnum.MARK_RESULT_SHERRORCODE_30),
    WITHOUT_MARKETING_LICENESE(-30, I18nKeyEnum.MARK_RESULT_SHERRORCODE_31),
    FORBID_SEND_MARKETING_MESSAGE(-9999, I18nKeyEnum.MARK_RESULT_SHERRORCODE_32),

    // 微信(1000-1999)
    REQUEST_WX_FAILED(1000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_35),
    WX_RESPONSE_DATA_ERROR(1001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_36),
    DECRYPT_FAILED(1002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_37),
    WX_USER_NOFOUND(1003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_38),
    QYWX_GET_FS_ACCOUNT_FAILED(1004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_39),
    QYWX_INNER_ACCESS_TOKEN_NOT_FOUND(1005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_40),
    // 小程序登录(2000-2999)
    LOGIN_TOKEN_INVALID(2000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_42),
    LOGIN_NEED_RELOGIN(2001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_43),
    CHECK_LOGIN_PHONE_MESSAGE_FAILED(2002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_44),
    LOGIN_BLACK_LIST(2003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_45),
    APPID_INVAILED(2004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_46),
    NOT_BIND_CUSTOMIZE_MINIAPP(2005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_47),
    APPID_NOT_FOUND(2006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_48),
    ACCESS_TOKEN_FAILED(2007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_49),
    APP_AUTHORIZE_AGAIN(2008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_50),
    STAFF_MEMBER_LOGIN_REFRESH_TOKEN(2009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_51),
    LOGIN_GET_USER_ID_FAILED(2010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_52),
    LOGIN_GET_USER_INFO_DETAIL_FAILED(2011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_53),
    USER_NUM_MEET_LIMIT(2012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_54),
    GET_OPENID_FAIL(2013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_55),
    LOGIN_FAILED(2014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_56),

    // 纷享登录(3000-3999)
    FS_SEND_SMCODE_FAIL(3000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_59),
    FS_VERITY_SMCODE_FAIL(3001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_60),
    FS_GET_ENTERPRISEUSER_LIST_FAIL(3002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_61),
    FS_COOKIE_INVALID(3003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_62),
    FS_COOKIE_NOT_FOUND(3004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_63),
    USER_ACCOUNT_NOT_EXIST(3005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_64),
    NEED_PHOTO_CHECK_CODE(3006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_65),
    SEND_PHONE_CHECK_ONE_MINUTE_LATER(3007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_66),
    ENTERPRSE_ACCOUNT_STOP(3008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_67),
    PHOTO_CHECK_CODE_ERROR(3009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_68),
    PHOTO_NOT_LOGIN_FS(3010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_69),
    ENTERPRISE_ACCOUNT_INVALID(3011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_70),
    FS_ACCOUNT_FORBIDDEN(3012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_71),
    FS_ACCOUNT_STOPPED(3013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_72),
    LOGINFS_NEED_SCAN_QR_CODE(3005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_73),
    LOGINFS_INITIAL_PASSWORD_WITH_MOBILE(3006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_74),
    LOGINFS_INITIAL_PASSWORD_WITHOUT_MOBILE(3007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_75),
    LOGINFS_SMS_CODE_NEVER_SET_PASSWORD(3008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_76),
    LOGINFS_SMS_CODE_INITIAL_PASSWORD(3009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_77),
    LOGINFS_WEAK_PASSWORD_WITH_MOBILE(3010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_78),
    LOGINFS_WEAK_PASSWORD_WITHOUT_MOBILE(3011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_79),
    LOGINFS_NEED_SMCODE_AUTHORIZE(3012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_80),
    LOGINFS_NEED_SMCODE_DEVICE_AUTHORIZE(3013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_81),
    // 小程序注册(4000-4999)
    ACCOUNT_PHONE_NOT_FOUND(4000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_83),
    ACCOUNT_PHONE_USED(4001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_84),
    ACCOUNT_NOT_FOUND(4002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_85),
    ACCOUNT_EXIST(4003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_86),
    ACCOUNT_FSAPPLYINFOKEY_INVALID(4004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_87),
    ACCOUNT_PHONE_OTHER_USED(4005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_88),
    ACCOUNT_PHONE_RESET_PHONE_CANT_BE_SAME(4006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_89),
    ACCOUNT_PHONE_UPDATE_FAIL(4007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_90),
    ACCOUNT_COOKIEKEY_INVALID(4008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_91),
    // 小程序访客(5000-5100)
    USER_NOT_FOUND(5000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_93),
    USER_ADD_FAIL(5001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_94),
    //小程序菜单(5101-5999)
    CUSTOMIZE_MINIAPP_NAVBAR_SEQ_EXIST(5101, I18nKeyEnum.MARK_RESULT_SHERRORCODE_96),
    CUSTOMIZE_MINIAPP_NAVBAR_MUST_KEEP_ONE(5102, I18nKeyEnum.MARK_RESULT_SHERRORCODE_97),
    CUSTOMIZE_MINIAPP_NAVBAR_MAX(5103, I18nKeyEnum.MARK_RESULT_SHERRORCODE_98),

    //小程序绑定(6000-6999)
    FSBIND_USER_EXIST(6000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_101),
    FSBIND_USER_NOFOUND(6001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_102),
    FSBIND_USER_FAIL(6002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_103),
    FSBIND_OTHER_COMPANY(6003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_104),
    FSBIND_PHONE_NULL(6004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_105),
    GET_ENTERPRISE_BY_PHONE_FAILED(6005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_106),
    GET_ENTERPRISE_NAME_FAILED(6006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_107),
    PHONE_NOT_LOGIN_ENTERPRISE(6007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_108),
    FSBIND_DISSOCIATE_FAIL(6008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_109),
    NOT_LOGIN_DINGDING_MINIAPP(6009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_110),

    //营销活动
    MARKETING_ACTIVITY_DATA_NOT_EXIST(7000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_113),
    INVALID_MARKETING_ACTIVITY_DATA_FAILED(7001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_114),
    MARKETING_ACTIVITY_TYPE_ERROR(7002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_115),
    DELETE_MARKETING_ALL_SPREAD_FAILED(7003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_116),
    DELETE_GROUP_SMS_SPREAD_FAILED(7004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_117),
    DELETE_MAIL_SPREAD_FAILED(7005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_118),
    DELETE_QYWX_SPREAD_FAILED(7007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_119),
    PROCESSING_MARKETINGACTIVITY_CANNOT_BE_DELETED(7008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_120),
    EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER(7009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_121),
    MEMBER_MARKETING_FILTER_NOT_NULL(7010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_122),

    // 物料分组(8000-8200)
    OBJECT_GROUP_LEVEL_MAX(8000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_125),
    OBJECT_GROUP_NOT_EXISTS(8001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_126),
    OBJECT_GROUP_PARENT_NOT_EXISTS(8002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_127),
    OBJECT_GROUP_NAME_EXISTS(8003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_128),
    OBJECT_GROUP_DELETE_FAIL_EXISTS_SUB(8004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_129),
    OBJECT_GROUP_DELETE_FAIL_EXISTS_OBJECT(8004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_130),


    //以下为业务错误码,从10000起累加,每个业务预设区间100

    // BusinessCard(10000-10099)
    BUSINESSCARD_USER_NOFOUND(10000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_136),
    BUSINESSCARD_ADD_FAIL(10001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_137),
    BUSINESSCARD_UPDATE_FAIL(10002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_138),
    BUSINESSCARD_QRURL_CREATE_FAIL(10003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_139),
    BUSINESSCARD_QRURL_NOFOUND(10004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_140),
    CARDFRIENDRELATION_SEEN_ADD(10005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_141),
    CARDFRIENDRELATION_SEEN_UPDATE(10006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_142),
    CARDFRIENDRELATION_EXCHANGE_ADD(10007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_143),
    CARDFRIENDRELATION_EXCHANGE_UPDATE(10008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_144),
    BUSINESSCARD_PREPARETOSWAP_MYCARD_NOFOUND(10009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_145),
    BUSINESSCARD_PREPARETOSWAP_TARGETCARD_NOFOUND(10010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_146),
    BUSINESSCARD_PREPARETOSWAP_CANTSWAPOWNS(10011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_147),
    BUSINESSCARD_SERVICER_NOFOUND(10012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_148),
    BUSINESSCARD_CANT_EDIT_OTHERS(10013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_149),
    CARDFRIENDRELATION_SEEN_QUERY_FAILED(10014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_150),
    CARDFRIENDRELATION_EXCHANGE_QUERY_FAILED(10015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_151),
    CARDFRIENDRELATION_SWAP_ACCEPTED(10016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_152),
    CARDFRIENDRELATION_SWAP_REFUSED(10017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_153),
    CARDFRIENDRELATION_SWAP_WAITTING(10018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_154),
    CARDFRIENDRELATION_TOTAL_QUERY_FAILED(10019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_155),
    BUSINESSCARD_PRIVACY_KEY_EMAIL_NOFOUND(10019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_156),
    BUSINESSCARD_PRIVACY_KEY_PHONE_NOFOUND(10020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_157),
    BUSINESSCARD_PRIVACY_KEY_WECHAT_NOFOUND(10021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_158),
    BUSINESSCARD_PRIVACY_VALUE_INVALID(10022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_159),
    BUSINESSCARD_USER_EXIST(10023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_160),
    BUSINESSCARD_UPDATE_PRIVACY_FAILED(10024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_161),
    CARDFRIENDRELATION_SWAP_HIS_WAIT(10025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_162),
    USER_NOT_CREATE_CARD(10026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_163),
    CARD_TEMPLATE_DELETE_FAIL(10027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_164),
    CARD_TEMPLATE_FAIL(10028, I18nKeyEnum.MARK_RESULT_SHERRORCODE_165),
    CARD_TEMPLATE_NOT_FOUND_FAIL(10029, I18nKeyEnum.MARK_RESULT_SHERRORCODE_166),
    // Product(10100-10199)
    PRODUCT_CREATE_FAIL(10100, I18nKeyEnum.MARK_RESULT_SHERRORCODE_168),
    PRODUCT_UPDATE_FAIL(10101, I18nKeyEnum.MARK_RESULT_SHERRORCODE_169),
    PRODUCT_LIST_FAIL(10102, I18nKeyEnum.MARK_RESULT_SHERRORCODE_170),
    PRODUCT_DETAIL_FAIL(10103, I18nKeyEnum.MARK_RESULT_SHERRORCODE_171),
    PRODUCT_USER_ADD_FAIL(10104, I18nKeyEnum.MARK_RESULT_SHERRORCODE_172),
    PRODUCT_USER_DEL_FAIL(10105, I18nKeyEnum.MARK_RESULT_SHERRORCODE_173),
    PRODUCT_RECOMMEND_FAIL(10106, I18nKeyEnum.MARK_RESULT_SHERRORCODE_174),
    PRODUCT_CAN_NOT_DELETE(10107, I18nKeyEnum.MARK_RESULT_SHERRORCODE_175),
    PRODUCT_USER_LIST_NOT_FOUND(10108, I18nKeyEnum.MARK_RESULT_SHERRORCODE_176),
    PRODUCT_MORE_THAN_ONE_RECOMMEND(10109, I18nKeyEnum.MARK_RESULT_SHERRORCODE_177),
    PRODUCT_CORPORATE_DEL_FAIL(10110, I18nKeyEnum.MARK_RESULT_SHERRORCODE_178),
    PRODUCT_ID_NOT_NULL(10111, I18nKeyEnum.MARK_RESULT_SHERRORCODE_179),
    PRODUCT_NAME_EXIST(10112, I18nKeyEnum.MARK_RESULT_SHERRORCODE_180),
    PRODUCT_NOT_FOUND(10113, I18nKeyEnum.MARK_RESULT_SHERRORCODE_181),
    PART_PRODUCT_NOT_FOUND(10114, I18nKeyEnum.MARK_RESULT_SHERRORCODE_182),
    TRY_PRODUCT_CAN_NOT_DELETE(10107, I18nKeyEnum.MARK_RESULT_SHERRORCODE_183),
    CANT_MOVE_UP_TOP_PRODUCT(10108, I18nKeyEnum.MARK_RESULT_SHERRORCODE_184),
    CANT_MOVE_DOWN_TOP_PRODUCT(10109, I18nKeyEnum.MARK_RESULT_SHERRORCODE_185),
    CANT_DELETE_NORMAL_PRODUCT(10110, I18nKeyEnum.MARK_RESULT_SHERRORCODE_186),

    // 文件系统(10300-10399)
    FILE_UPLOAD_FAILED(10300, I18nKeyEnum.MARK_RESULT_SHERRORCODE_189),
    FILE_PREVIEW_URL_GET_FAILED(10301, I18nKeyEnum.MARK_RESULT_SHERRORCODE_190),
    FILE_NOT_FOUND(10302, I18nKeyEnum.MARK_RESULT_SHERRORCODE_191),
    FILE_FORMAT_ERROR(10303, I18nKeyEnum.MARK_RESULT_SHERRORCODE_192),
    PIC_CONVERT_FAILED(10304, I18nKeyEnum.MARK_RESULT_SHERRORCODE_193),
    PIC_FILE_FAILED(10305, I18nKeyEnum.MARK_RESULT_SHERRORCODE_194),
    NOT_SUPPORT_DOWNLOAD_PIC_FILE_EA(10306, I18nKeyEnum.MARK_RESULT_SHERRORCODE_195),
    CANNOT_DELETE_SYSTEM_FILE(10307, I18nKeyEnum.MARK_RESULT_SHERRORCODE_196),
    CANNOT_RENAME_SYSTEM_FILE(10308, I18nKeyEnum.MARK_RESULT_SHERRORCODE_197),
    PIC_NUM_LIMIT_EXCEEDED(10309, I18nKeyEnum.MARK_RESULT_SHERRORCODE_198),
    SINGLE_PIC_LIMIT_EXCEEDED(10310, I18nKeyEnum.MARK_RESULT_SHERRORCODE_199),
    FILE_NAME_EXIST(10311, I18nKeyEnum.MARK_RESULT_SHERRORCODE_200),
    File_UPLOAD_PART_SUCCESS(10312, I18nKeyEnum.MARK_RESULT_SHERRORCODE_201),
    PIC_NAME_EXIST(10313, I18nKeyEnum.MARK_RESULT_SHERRORCODE_202),
    PART_FILE_NOT_FOUND(10314, I18nKeyEnum.MARK_RESULT_SHERRORCODE_203),

    // 模板消息(10500-10599)
    SEND_FAILED(10500, I18nKeyEnum.MARK_RESULT_SHERRORCODE_206),
    // 用户产品关联(10600-10699)
    USERPRODUCT_ADD_FAIL(10600, I18nKeyEnum.MARK_RESULT_SHERRORCODE_208),
    USERPRODUCT_DEL_FAIL(10601, I18nKeyEnum.MARK_RESULT_SHERRORCODE_209),
    USERPRODUCT_SELECT_FAIL(10602, I18nKeyEnum.MARK_RESULT_SHERRORCODE_210),
    // 标签(10700-10799)
    TAG_NOFOUND(10700, I18nKeyEnum.MARK_RESULT_SHERRORCODE_212),
    TAG_UPDATE_FAIL(10701, I18nKeyEnum.MARK_RESULT_SHERRORCODE_213),
    TAG_DEL_FAIL(10702, I18nKeyEnum.MARK_RESULT_SHERRORCODE_214),
    TAG_GROUP_NOFOUND(10703, I18nKeyEnum.MARK_RESULT_SHERRORCODE_215),
    DISPLAY_ORDER_CHANGED(10704, I18nKeyEnum.MARK_RESULT_SHERRORCODE_216),

    NOT_SUPPORT_OBJECT_TYPE(10705, I18nKeyEnum.MARK_RESULT_SHERRORCODE_218),


    // 群(10800-10899)
    GROUP_BINDUSER_FAIL(10800, I18nKeyEnum.MARK_RESULT_SHERRORCODE_222),
    GROUP_ADDCARD_FAIL(10800, I18nKeyEnum.MARK_RESULT_SHERRORCODE_223),
    GROUP_ADD_FAIL(10801, I18nKeyEnum.MARK_RESULT_SHERRORCODE_224),
    GROUP_QUERY_FAILED(10802, I18nKeyEnum.MARK_RESULT_SHERRORCODE_225),
    // 埋点(10900-10999)
    TRACE_OBJTTYPE_ERROR(10900, I18nKeyEnum.MARK_RESULT_SHERRORCODE_227),
    // 纷享相关(11000-11999)
    EMPLOYEE_NOT_FOUND(11000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_229),
    ENTERPRISE_NOT_FOUND(11001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_230),
    DEPARTMENT_NOT_FOUND(11002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_231),
    PHONE_NOT_ACCOUNT_BIND(11003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_232),
    USER_NOT_BIND_EA(11004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_233),
    EMPLOYEE_MOBILE_NOT_FOUND(11005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_234),
    FS_COOKIE_LOGIN_FAILED(11006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_235),
    EMPLOYEE_GET_FAILED(11007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_236),
    // 文章(12000-12999)
    ADD_FAIL(12000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_238),
    UPDATE_FAIL(12001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_239),
    DEL_FAIL(12002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_240),
    DEL_ARTICLE_FAIL_TIP(12003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_241),
    ARTICLE_NOT_EXIST(12004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_242),
    ARTICLE_EXIST(12010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_243),
    ADD_PHOTO_FAIL(12005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_244),
    FEED_NOT_EXIST(12006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_245),
    WEB_CRAWLER_FAIL(12007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_246),
    ARTICLE_NOT_EXIST_FEED(12008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_247),
    ARTICLE_CAN_NOT_DELETE(12009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_248),
    UID_QUERY_BY_FEED_ID_FAIL(12010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_249),
    ARTICLE_INNER_HTML_NOT_EXIST(12011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_250),
    ARTICLE_DELETED(12012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_251),
    ARTICLE_DETAIL_FAIL(12013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_252),
    UPDATE_COVER_FAIL(12004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_253),
    ADD_COVER_FAIL(12005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_254),
    PREVIEW_ARTICLE_CARD_QRURL_CREATE_FAIL(12006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_255),
    ADD_NO_COVER(12007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_256),
    ARTICLE_LIST_FAILED(12008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_257),
    ARTICLE_NAME_EXIST(12009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_258),
    PART_ARTICLE_NOT_FOUND(12010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_259),

    // 客脉web（13000-13999）
    COOKIE_NOT_FOUND(13000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_262),
    // 参数校验错误码，名片、产品以及客脉（14000-14999）
    CARD_NAME_INVALID(14000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_264),
    CARD_PHONE_INVALID(14001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_265),
    CARD_AVATAR_INVALID(14002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_266),
    CARD_COMPANY_INVALID(14003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_267),
    CARD_ADDRESS_INVALID(14004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_268),
    CARD_SUMMARY_INVALID(14005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_269),
    CARD_VACATION_INVALID(14006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_270),
    CARD_DEPARTMENT_INVALID(14007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_271),
    CARD_WECHAT_INVALID(14008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_272),
    CARD_EMAIL_INVALID(14009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_273),
    CARD_PIC_INVALID(14010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_274),
    PRODUCT_SUMMARY_INVALID(14011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_275),
    PRODUCT_HEAD_INVALID(14012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_276),
    PRODUCT_DETAIL_INVALID(14013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_277),
    PRODUCT_NAME_INVALID(14014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_278),
    MANKEEP_COMPANY_INVALID(14015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_267),
    MANKEEP_PHONE_INVALID(14016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_265),
    MANKEEP_EMAIL_INVALID(14017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_273),
    MANKEEP_WECHAT_INVALID(14018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_272),
    MANKEEP_QQ_INVALID(14019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_283),
    MANKEEP_ADDRESS_INVALID(14020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_268),
    MANKEEP_VACATION_INVALID(14021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_270),
    MANKEEP_DEPARTMENT_INVALID(14022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_271),
    MANKEEP_NAME_INVALID(14023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_287),
    TAG_NAME_INVALID(14024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_288),
    INTERACT_CONTENT_INVALID(14025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_289),
    LEAD_REMARK_INVALID(14026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_290),
    FEED_SUMMARY_INVALID(14027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_291),
    FEED_RECONMENDATION_INVALID(14028, I18nKeyEnum.MARK_RESULT_SHERRORCODE_292),
    // customer(15000-15999)
    CUSTOMER_ADD_EXIST(15000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_294),
    CUSTOMER_ADD_FAILED(15001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_295),
    CUSTOMER_NOT_FOUND(15002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_296),
    CUSTOMER_UPDATE_FAILED(15003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_297),
    CUSTOMER_UPGRADE_CANT_DOWNGRADE(15004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_298),
    CUSTOMER_FOLLOWUP_ADD_FAILED(15005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_299),
    CUSTOMER_UPGRADE_CANT_SKIPGRADE(15006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_300),
    // feed_like(16000-16999)
    FEED_LIKE_FEED_NOT_EXISTS(16000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_302),
    FEED_LIKE_HAS_LIKED(16001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_303),
    // 灰度(17000-17999)
    GRAY_LUCKYMONEY_IS_NOT_FSBIND(17000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_305),
    GRAY_LUCKYMONEY_COMPANY_REFUSED(17001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_306),
    //notice (18000 - 18999)
    NOTICE_NOT_EXIST(18000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_308),
    NOTICE_RELATE_CONTENT_NOT_EXIST(18001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_309),
    // 保存数据到CRM异常 (20000 - 21000)
    CRM_BUSINESS_ERROR(20000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_311),
    FIELD_MAPPING_VERIFY_ERROR(20001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_312),
    NOT_NULL_CRM_FIELD_NOT_MAPPED(20002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_313),
    CUSTOMER_FIELD_SHOULD_NOT_BE_NULL(20003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_314),
    SAVE_CUSTOMER_TO_CRM_DISABLED(20004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_315),
    CUSTOMER_HAS_EXIST_IN_CRM(20005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_316),
    NOT_A_MANKEEP(20006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_317),
    NOT_A_CUSTOMER(20007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_318),
    LEAD_HAS_EXIST_IN_CRM(20008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_319),
    CUSTOMER_SAVED(20009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_320),
    MANKEEP_SAVED(20010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_321),
    SAVE_MANKEEP_TO_CRM_DISABLED(20011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_322),
    // 活动邀请相关错误码 (21001 - 22000)
    ACTIVITY_DISABLE(21001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_324),
    ENROLL_STOPPED(21002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_325),
    ACTIVITY_DETAIL_FAIL(21003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_326),
    ACTIVITY_NOT_SET_SIGN(21004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_327),
    ACTIVITY_NOT_EXIST(21005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_328),
    ACTIVITY_FULL(21006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_329),
    ACTIVITY_NOT_REVIEW_SUCCESS(21007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_330),
    ACTIVITY_END(21008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_331),
    ACTIVITY_NOT_START_NOT_ALLOW_SIGN_IN(21009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_332),
    ACTIVITY_DELETED(21010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_333),
    ACTIVITY_NOT_REVIEW_FAIL(21011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_334),
    // 六度人脉 (22001 - 23000)
    SPREAD_NOT_FOUND(22001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_335),
    SPREAD_READ_FAILED(22002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_336),
    SPREAD_FORWARD_FAILED(22003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_337),
    SPREAD_ADD_FAILED(22003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_337),
    // 营销通相关的错误码（50000 - 51000）
    INVALID_FS_LOGIN(50000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_340),
    USER_IS_NOT_MARKETING_APP_MANAGER(50001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_341),
    USER_IS_NOT_FSBIND(50002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_342),
    USER_HAS_ENROLL(50003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_343),
    USER_IS_NOT_OPERATOR(50004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_344),
    USER_DOSE_NOT_HAVE_PERMISSION(50005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_345),
    USER_NOT_ENROLL(50006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_346),
    FORM_TEMPLATE_NOT_EXIST(30000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_347),
    OBJECT_DESCRIPTION_NOT_EXIST(40000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_348),
    OBJECT_API_NAME_NOT_EXIST(40000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_349),
    CRM_DATA_LIMIT_ERROR(50010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_350),
    ONNY_ALLOW_APP_ADMIN(50011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_351),
    GET_CRM_LEADS_ERROR(50012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_352),
    LEAD_STAGE_DESCRIBE_NOT_EXIST(50013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_353),
    LEAD_STAGE_OPTION_NOT_EXIST(50014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_354),
    LEAD_DESCRIBE_NOT_EXIST(50015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_355),
    LEAD_BIZ_STATUS_DESCRIBE_NOT_EXIST(50016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_356),
    //伙伴营销相关的错误码(51500-52000)
    PARTNER_MARKETING_PERSONNEL_ROLE_NOT_EXIST(51500, I18nKeyEnum.MARK_RESULT_SHERRORCODE_358),
    // 社会化分销(52000 - 53000)
    OPERATOR_ONT_FOUND(52000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_360),
    DELETE_OPERATOR_FAILED_AS_CONNECT_DISTRIBUTOR(52001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_361),
    OPERATOR_ADD_TO_APP_WEB_COMPONENT_FAILED(52002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_362),
    OPERATOR_GET_QRCODE_FAILED(52003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_360),
    LEAD_POOL_ONT_FOUND(52004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_364),
    OPERATOR_GET_DISTRIBUTE_PLAN_FAILED(52005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_365),
    MORE_THAN_NINE_PHOTOS(52006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_366),
    DISTRIBUTE_PLAN_ONT_FOUND(52007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_367),
    SAVE_CRM_LEAD_ERROR(52008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_368),
    OBJECT_FIELD_MAPPING_ONT_FOUND(52009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_369),
    GRAY_DISTRIBUTION_COMPANY_REFUSED(52010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_370),
    GET_ADMIN_FAILED(52011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_371),
    GET_APP_VIEW_LIST_FAILED(52012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_372),
    ADD_OPERATOR_FAILED(52013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_373),
    CLUE_NOT_EXIST_IN_DISTRIBUTE(52014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_374),
    TOTAL_GRANTED_BEYOND_REWARD(52015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_375),
    PLAN_GRADE_NOT_MAX(52016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_376),
    PLAN_GRADE_IS_ERROR(52017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_377),
    PLAN_GRADE_MONEY_ERROR(52018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_378),
    PLAN_GRADE_MONEY_FIRST_ERROR(52019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_379),
    PLAN_GRADE_MONEY_PREVIOUS_ERROR(52020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_380),
    PLAN_GRADE_MONEY_NEXT_ERROR(52021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_381),
    QUERY_CLUE_DETAIL_ERROR(52022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_382),
    QUERY_DISTRIBUTION_MATERIAL_ERROR(52023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_383),
    QUERY_OPERATOR_ERROR(52024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_384),
    QUERY_PLAN_LIST_ERROR(52025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_385),
    QUERY_DISTRIBUTOR_INFO_ERROR(52026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_386),
    QUERY_CLUE_LIST_ERROR(52027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_387),
    DISTRIBUTOR_NOT_EXIST(52028, I18nKeyEnum.MARK_RESULT_SHERRORCODE_388),
    PLAN_GRADE_DELETE_BUT_EXIST(52029, I18nKeyEnum.MARK_RESULT_SHERRORCODE_389),
    DISTRIBUTOR_APPLICATION_NOT_EXIST(52030, I18nKeyEnum.MARK_RESULT_SHERRORCODE_390),
    MANUAL_REWARD_SET_FAIL(52031, I18nKeyEnum.MARK_RESULT_SHERRORCODE_391),
    PLAN_GRADE_RULE_TYPE_ERROR(52032, I18nKeyEnum.MARK_RESULT_SHERRORCODE_392),
    PLAN_GRADE_CLUE_NUM_ERROR(52033, I18nKeyEnum.MARK_RESULT_SHERRORCODE_393),
    PLAN_GRADE_DISTRIBUTOR_NUM_ERROR(52034, I18nKeyEnum.MARK_RESULT_SHERRORCODE_394),
    PLAN_GRADE_CLUE_WIN_NUM_ERROR(52035, I18nKeyEnum.MARK_RESULT_SHERRORCODE_395),
    SEND_SMS_SIGNATURE_FAILED(60001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_396),
    APPLY_SMS_SIGNATURE_BEYOND(60002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_397),
    APPLY_APPID_NOTICE(60003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_397),
    SIGNATURE_NOT_EXIST(60004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_399),
    DELETE_SIGNATURE_FAILED(60005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_400),
    APPLY_SIGNATURE_BEFORE_TEMPLATE(60006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_401),
    APPLY_TEMPLATE_FAILED(60007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_402),
    APPLY_SMS_TEMPLATE_BEYOND(60008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_403),
    SMS_TEMPLATE_NOT_EXIST(60009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_404),
    DELETE_TEMPLATE_FAILED(60010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_405),
    MOVE_TEMPLATE_FAILED(60011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_406),
    MODIFY_SIGNATURE_FAILED(60012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_407),
    MODIFY_SIGNATURE_CNT_LIMIT(60013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_408),
    EXCEL_TEMPLATE_PHONE_ERROR(60014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_409),
    EXCEL_PHONE_NUMBER_ZERO(60015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_410),
    EXCEL_PARAM_NUMBER_ERROR(60016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_411),
    EXCEL_PARAM_NOT_EQUAL_TEMPLATE(60017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_412),
    DELETE_SMS_STATUS_ERROR(60018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_413),
    QUOTA_LESS_THAN_SPEND(60019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_414),
    SMS_SIGNATRUE_LENGTH_ERROR(60020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_415),
    SMS_SMS_CONTENT_LENGTH_ERROR(60021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_416),
    SMS_UPDATE_OPPORTUNITY_ERROR(60022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_417),
    SMS_SIGNATURE_STATUS_ERROR(60023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_418),
    SMS_CHOOSE_PHONES_ERROR(60024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_419),
    SMS_SCHEDULE_TIME_ERROR(60025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_420),
    SMS_SEND_NOT_EXIST_ERROR(60026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_421),
    SMS_UPDATE_TEMPLATE_FAILED(60027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_422),
    SMS_SEND_STATUS_ERROR(60028, I18nKeyEnum.MARK_RESULT_SHERRORCODE_423),
    SMS_DETAIL_CREATE_ERROR(60029, I18nKeyEnum.MARK_RESULT_SHERRORCODE_424),
    SMS_BUY_MEAL_NULL_ERROR(60030, I18nKeyEnum.MARK_RESULT_SHERRORCODE_425),
    SMS_USER_GROUP_ERROR(60031, I18nKeyEnum.MARK_RESULT_SHERRORCODE_426),
    SMS_EXCEL_DOWNLOAD_ERROR(60032, I18nKeyEnum.MARK_RESULT_SHERRORCODE_427),
    SMS_PARAM_REPLACE_ERROR(60033, I18nKeyEnum.MARK_RESULT_SHERRORCODE_428),
    SMS_WORD_LIMIT_ERROR(60034, I18nKeyEnum.MARK_RESULT_SHERRORCODE_429),
    SMS_DETAIL_CREATE_EXCEPTION_ERROR(60035, I18nKeyEnum.MARK_RESULT_SHERRORCODE_430),
    SMS_USER_GROUP_NO_PHONE_ERROR(60036, I18nKeyEnum.MARK_RESULT_SHERRORCODE_431),
    SMS_USER_GROUP_EXCEPTION_ERROR(60037, I18nKeyEnum.MARK_RESULT_SHERRORCODE_432),
    APPLY_TEMPLATE_NULL_FAILED(60038, I18nKeyEnum.MARK_RESULT_SHERRORCODE_433),
    SMS_USER_GROUP_PHONE_ERROR(60039, I18nKeyEnum.MARK_RESULT_SHERRORCODE_434),
    SMS_PHONE_EMPTY_ERROR(60053, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_437_1)),
    SMS_VARIABLE_ERROR(60054, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_437_2)),
    OPEN_USER_GROUP_NOT_CAN_DELETE(60040, I18nKeyEnum.MARK_RESULT_SHERRORCODE_435),
    USED_USER_GROUP_NOT_CAN_DELETE(60041, I18nKeyEnum.MARK_RESULT_SHERRORCODE_436),
    SMS_TRIAL_APPLY_ERROR(60042, I18nKeyEnum.MARK_RESULT_SHERRORCODE_437),
    SMS_TRIAL_APPLY_FAIL(60043, I18nKeyEnum.MARK_RESULT_SHERRORCODE_438),
    USER_GROUP_NAME_DUPLICATED(60047, I18nKeyEnum.MARK_RESULT_SHERRORCODE_439),
    AUTO_CALCULATE_GROUP_REACH_LIMIT(60048, I18nKeyEnum.MARK_RESULT_SHERRORCODE_440),
    EXISTED_ADD_USER_TO_GROUP_TASK(60049, I18nKeyEnum.MARK_RESULT_SHERRORCODE_441),
    GROUP_WITH_CUSTOMIZE_OBJECT_BEYOND_LIMIT(60050, I18nKeyEnum.MARK_RESULT_SHERRORCODE_442),
    DATA_RANGE_CANT_NULL(60051, I18nKeyEnum.MARK_RESULT_SHERRORCODE_443),
    DATA_RANGE_PARAM_ERROR(60052, I18nKeyEnum.MARK_RESULT_SHERRORCODE_444),

    SMS_MODIFY_TEMPLATE_FAILED(600048, I18nKeyEnum.MARK_RESULT_SHERRORCODE_446),
    UPDATE_MODIFY_TEMPLATE_FAILED(600049, I18nKeyEnum.MARK_RESULT_SHERRORCODE_447),
    QUERY_TEMPLATE_FAILED(600050, I18nKeyEnum.MARK_RESULT_SHERRORCODE_448),
    MANAGE_TEMPLATE_FAILED(600051, I18nKeyEnum.MARK_RESULT_SHERRORCODE_449),
    SMS_TEMPLATE_STATUS_DENY(600052, I18nKeyEnum.MARK_RESULT_SHERRORCODE_450),
    TEMPLATE_FORM_NOT_SUPPORT(600053, I18nKeyEnum.MARK_RESULT_SHERRORCODE_451),

    MARKETING_USER_GROUP_NOT_EXIST(600054, I18nKeyEnum.MARK_RESULT_SHERRORCODE_453),
    MARKETING_USER_GROUP_IS_EMPTY(600055, I18nKeyEnum.MARK_RESULT_SHERRORCODE_454),

    TAG_DUPLICATION_OF_NAME(60500, I18nKeyEnum.MARK_RESULT_SHERRORCODE_456),
    TAG_GROUP_DUPLICATION_OF_NAME(60501, I18nKeyEnum.MARK_RESULT_SHERRORCODE_457),
    LAST_ONE_TAG_GROUP_NOT_CAN_DELETE(60502, I18nKeyEnum.MARK_RESULT_SHERRORCODE_458),
    NOT_CAN_CREATE_SYSTEM_GROUP(60503, I18nKeyEnum.MARK_RESULT_SHERRORCODE_459),
    NOT_CAN_DELETE_TAG(60504, I18nKeyEnum.MARK_RESULT_SHERRORCODE_460),
    NOT_CAN_DELETE_TAG_GROUP(60505, I18nKeyEnum.MARK_RESULT_SHERRORCODE_461),
    SET_ENTERPRSE_INFO_FAILED(70001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_462),
    QUERY_ENTERPRSE_INFO_FAILED(70002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_463),
    MARKETING_WE_SERVCE_MAX(70500, I18nKeyEnum.MARK_RESULT_SHERRORCODE_464),
    DUPLICATE_ADD(70501, I18nKeyEnum.MARK_RESULT_SHERRORCODE_465),
    MARKETING_WE_SERVCE_NOT_EXISTS(70502, I18nKeyEnum.MARK_RESULT_SHERRORCODE_466),
    QUERY_SPREAD_TASK_FAILED(80001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_467),
    UPDATE_SPREAD_TASK_STATUS_FAILED(80002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_468),
    KIS_MARKETING_ACTIVITY_NOT_FOUND(80003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_469),
    KIS_REMOVE_RED_DOT_FAILED(80004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_470),
    KIS_QUERY_RED_DOT_FAILED(80005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_471),
    KIS_QUERY_SPREAD_NOTICE_FAILED(80006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_472),
    KIS_QUERY_SPREAD_STATUS_FAILED(80008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_473),
    KIS_MARKETING_ACTIVITY_ADD_FAILED(80007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_474),

    KIS_MARKETING_ACTIVITY_UPDATE_FAILED(80008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_476),
    KIS_MARKETING_ACTIVITY_DONOT_UPDATE(80009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_477),
    // 自定义表单(80500 - 80599)
    CUSTOMIZE_FORM_DATA_NOT_FOUND(80500, I18nKeyEnum.MARK_RESULT_SHERRORCODE_479),
    CUSTOMIZE_FORM_DATA_DISABLE(80501, I18nKeyEnum.MARK_RESULT_SHERRORCODE_480),
    CUSTOMIZE_FORM_DATA_DELETE(80502, I18nKeyEnum.MARK_RESULT_SHERRORCODE_481),
    USERS_HAVE_REGISTERED(80503, I18nKeyEnum.MARK_RESULT_SHERRORCODE_482),
    UNBINDING_BEFORE_DELETING(80504, I18nKeyEnum.MARK_RESULT_SHERRORCODE_483),
    CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND(80505, I18nKeyEnum.MARK_RESULT_SHERRORCODE_484),
    CUSTOMIZE_FORM_DATA_USER_NOT_FOUND(80506, I18nKeyEnum.MARK_RESULT_SHERRORCODE_485),
    CUSTOMIZE_FORM_DATA_USER_FULL(80507, I18nKeyEnum.MARK_RESULT_SHERRORCODE_486),
    CUSTOMIZE_FORM_NOT_PHONE_SETTING(80508, I18nKeyEnum.MARK_RESULT_SHERRORCODE_487),
    CUSTOMIZE_FORM_CRM_VERIFY_FIELDS_ERROR(80509, I18nKeyEnum.MARK_RESULT_SHERRORCODE_488),
    CUSTOMIZE_FORM_BIND_CRM_DATA_ERROR(80510, I18nKeyEnum.MARK_RESULT_SHERRORCODE_489),
    OBJECT_NOT_BIND_CUSTOMIZE_FORM(80511, I18nKeyEnum.MARK_RESULT_SHERRORCODE_490),
    NOT_ENTER_PHONE_VERIFY_CODE(80512, I18nKeyEnum.MARK_RESULT_SHERRORCODE_491),
    CUSTOMZIE_FORM_SITE_NAME_EXIST(80513, I18nKeyEnum.MARK_RESULT_SHERRORCODE_492),
    PART_CUSTOMIZE_FORM_DATA_NOT_FOUND(80514, I18nKeyEnum.MARK_RESULT_SHERRORCODE_493),

    //朋友圈海报(80600-80699)
    MOMENT_POST_MATERIAL_NOT_FOUND(80600, I18nKeyEnum.MARK_RESULT_SHERRORCODE_496),
    // 微信公众号(80700-80799)
    NOT_ENTERPRISE_WX_OFFICIAL_ACCOUNTS(80700, I18nKeyEnum.MARK_RESULT_SHERRORCODE_498),
    ENTERPRISE_WX_OFFICIAL_ACCOUNTS_NOT_FOUND(80701, I18nKeyEnum.MARK_RESULT_SHERRORCODE_499),
    UN_BIND_WX_APP_FAIL(80702, I18nKeyEnum.MARK_RESULT_SHERRORCODE_500),
    WX_OFFICIAL_NOT_SUPPORT(80703, I18nKeyEnum.MARK_RESULT_SHERRORCODE_501),
    WX_OFFICIAL_CODE_INVALID(80704, I18nKeyEnum.MARK_RESULT_SHERRORCODE_502),
    WX_OFFICIAL_COMPONTAPPID_FILE(80705, I18nKeyEnum.MARK_RESULT_SHERRORCODE_503),
    WX_OFFICIAL_NOT_FOUND_EA(80706, I18nKeyEnum.MARK_RESULT_SHERRORCODE_504),
    WX_USE_NOT_FOUND(80707, I18nKeyEnum.MARK_RESULT_SHERRORCODE_505),
    WX_QR_CODE_NOT_FOUND(80708, I18nKeyEnum.MARK_RESULT_SHERRORCODE_506),

    WX_QR_CODE_NOT_SCAN(80709, I18nKeyEnum.MARK_RESULT_SHERRORCODE_508),
    WX_USE_NOT_BIND_PHONE(80710, I18nKeyEnum.MARK_RESULT_SHERRORCODE_509),
    WX_QR_CODE_SETTING_NOT_FOUND(80711, I18nKeyEnum.MARK_RESULT_SHERRORCODE_510),
    // 会议营销(80800-80899)
    CONFERENCE_MARKETING_EVENT_NOT_FOUND(80801, I18nKeyEnum.MARK_RESULT_SHERRORCODE_512),
    CONFERENCE_MARKETING_EVENT_EXCEPTION(80802, I18nKeyEnum.MARK_RESULT_SHERRORCODE_513),
    CONFERENCE_UPDATE_NOT_FOUND(80803, I18nKeyEnum.MARK_RESULT_SHERRORCODE_514),
    CONFERENCE_IMAGE_PARAMS_ERROR(80804, I18nKeyEnum.MARK_RESULT_SHERRORCODE_515),
    CONFERENCE_NOT_FOUND(80805, I18nKeyEnum.MARK_RESULT_SHERRORCODE_516),
    CONFERENCE_ENROLL_FULL(80806, I18nKeyEnum.MARK_RESULT_SHERRORCODE_517),
    QUERY_CONFERENCE_LIST_FAILED(80807, I18nKeyEnum.MARK_RESULT_SHERRORCODE_518),
    QUERY_CONFERENCE_PARTICIPANTS_FAILED(80808, I18nKeyEnum.MARK_RESULT_SHERRORCODE_519),
    CONFERENCE_INVITATION_NOT_FOUND(80809, I18nKeyEnum.MARK_RESULT_SHERRORCODE_520),
    CHANGE_CONFERENCE_PARTICIPANTS_SIGN_STATUS_FAILED(80810, I18nKeyEnum.MARK_RESULT_SHERRORCODE_521),
    CHANGE_CONFERENCE_PARTICIPANTS_REVIEW_STATUS_FAILED(80811, I18nKeyEnum.MARK_RESULT_SHERRORCODE_522),
    SAVE_CONFERENCE_PARTICIPANTS_TO_CRM_FAILED(80812, I18nKeyEnum.MARK_RESULT_SHERRORCODE_523),
    CONFERENCE_QRCODE_CREATE_FAIL(80810, I18nKeyEnum.MARK_RESULT_SHERRORCODE_524),
    CONFERENCE_UPDATE_IMAGE_FAIL(80811, I18nKeyEnum.MARK_RESULT_SHERRORCODE_525),
    CONFERENCE_USER_HAS_SIGNED(80812, I18nKeyEnum.MARK_RESULT_SHERRORCODE_526),
    CONFERENCE_USER_SIGN_IN_AFTER_ENROLL(80813, I18nKeyEnum.MARK_RESULT_SHERRORCODE_527),
    CONFERENCE_STATUS_TO_DISABLED_FAIL(80814, I18nKeyEnum.MARK_RESULT_SHERRORCODE_528),
    CONFERENCE_STATUS_TO_ENABLED_FAIL(80815, I18nKeyEnum.MARK_RESULT_SHERRORCODE_529),
    CONFERENCE_STATUS_TO_DELETE_FAIL(80816, I18nKeyEnum.MARK_RESULT_SHERRORCODE_530),
    CONFERENCE_INVITE_PARTICIPANT_FAIL(80817, I18nKeyEnum.MARK_RESULT_SHERRORCODE_531),
    CONFERENCE_IMPORT_INVITE_TITLE_ERROR(80818, I18nKeyEnum.MARK_RESULT_SHERRORCODE_532),
    CONFERENCE_QUERY_ENROLL_REVIEW_DETAIL_FAILED(80819, I18nKeyEnum.MARK_RESULT_SHERRORCODE_533),
    CONFERENCE_VERIFY_PHONE_FAILED(80820, I18nKeyEnum.MARK_RESULT_SHERRORCODE_534),
    CONFERENCE_UPDATE_INVITE_STATUS_FAILED(80821, I18nKeyEnum.MARK_RESULT_SHERRORCODE_535),
    CONFERENCE_DELETE_INVITE_FAILED(80822, I18nKeyEnum.MARK_RESULT_SHERRORCODE_536),
    CONFERENCE_UPDATE_INVITE_INFO_FAILED(80823, I18nKeyEnum.MARK_RESULT_SHERRORCODE_537),
    CONFERENCE_NOT_MARKETING_VIEW_AUTH(80824, I18nKeyEnum.MARK_RESULT_SHERRORCODE_538),
    CONFERENCE_PARTICIPATN_SAVED_LEAD(80825, I18nKeyEnum.MARK_RESULT_SHERRORCODE_539),
    CONFERENCE_DELETE_MARKETINGEVENT_FAILED(80826, I18nKeyEnum.MARK_RESULT_SHERRORCODE_540),
    CONFERENCE_PARTICIPATN_NOT_FOUND(80827, I18nKeyEnum.MARK_RESULT_SHERRORCODE_541),
    NOT_HAVE_PENDING_REVIEW_PARTICIPANTS(80828, I18nKeyEnum.MARK_RESULT_SHERRORCODE_542),

    IMPORT_CONFERENC_PARTICIPANT_FILE_FAIL(80829, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_547_1)),

    // 二维码
    QRCODE_NO_FOUND(80901, I18nKeyEnum.MARK_RESULT_SHERRORCODE_545),
    QRCODE_AUTH_FAILED(80902, I18nKeyEnum.MARK_RESULT_SHERRORCODE_546),
    QRCODE_AUTH_CODE_NOT_FOUND(80903, I18nKeyEnum.MARK_RESULT_SHERRORCODE_547),
    QRPOSTER_NOT_FOUND(80904, I18nKeyEnum.MARK_RESULT_SHERRORCODE_548),
    QRPOSTER_INVATE_EXIST(80905, I18nKeyEnum.MARK_RESULT_SHERRORCODE_549),
    QRPOSTER_ELOGO_NOT_FOUND(80906, I18nKeyEnum.MARK_RESULT_SHERRORCODE_550),
    QRCODE_CREATE_FAILED(80907, I18nKeyEnum.MARK_RESULT_SHERRORCODE_551),
    QRCODE_NOT_FOUND(80908, I18nKeyEnum.MARK_RESULT_SHERRORCODE_552),
    DOWNLOAD_QRPOSTER_FAILED(80909, I18nKeyEnum.MARK_RESULT_SHERRORCODE_553),
    UPLOAD_QRPOSTER_FAILED(80910, I18nKeyEnum.MARK_RESULT_SHERRORCODE_554),
    PART_QRPOSTER_NOT_FOUND(80911, I18nKeyEnum.MARK_RESULT_SHERRORCODE_555),
    OUT_QR_CODE_PARAM_NOT_FOUND(80912,I18nKeyEnum.MARK_RESULT_SHERRORCODE_556),
    OUT_QR_CODE_CARD_NOT_FOUND(80913,I18nKeyEnum.MARK_RESULT_SHERRORCODE_557),
    OUT_QR_CODE_CREATE_FAILED(80914,I18nKeyEnum.MARK_RESULT_SHERRORCODE_558),
    QR_CODE_TOO_LARGE(80915,I18nKeyEnum.MARK_RESULT_SHERRORCODE_559),
    OUT_QR_CODE_URL_NOT_PARSE(80916,I18nKeyEnum.MARK_RESULT_SHERRORCODE_560),

    //营销用户 81000-82000
    THIS_IS_A_VISITOR(81000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_563),
    NO_PERMISSION_TO_VIEW(81001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_564),
    NOT_EXIST_OR_CREATING(81002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_565),
    NOT_EXIST_MARKETING_USER(81003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_565),


    // 营销流程 82001 - 83000
    ENABLED_FLOW_REACH_LIMIT(82001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_569),
    TRIGGER_NOT_FOUND(82002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_570),

    // 素材相关 83001 ~ 84000
    MATERIAL_NOT_FOUND(83001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_573),

    // 导入数据 84001 - 84500
    EXCEL_HAS_DIFFERENCE_TO_FORM(84001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_532),
    IMPORT_FILE_NO_DATA(84002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_577),
    IMPORT_DATA_EXCEED_THE_LIMIT(84003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_578),

    // 卡券 84501 - 85000
    CUSTOMIZE_TICKET_DATA_NOT_FOUND(84501, I18nKeyEnum.MARK_RESULT_SHERRORCODE_581),
    WX_MEETING_TICKET_FOUND(84502, I18nKeyEnum.MARK_RESULT_SHERRORCODE_582),
    WX_MEETING_TICKET_LOGO_SAVE_FAIL(84503, I18nKeyEnum.MARK_RESULT_SHERRORCODE_583),
    WX_MEETING_TICKET_SAVE_TO_DB_FAIL(84504, I18nKeyEnum.MARK_RESULT_SHERRORCODE_584),
    WX_TICKET_CHECK_FUNCTIONS_FAIL(84505, I18nKeyEnum.MARK_RESULT_SHERRORCODE_585),
    WX_TICKET_CHECK_NO_FUNCTIONS(84506, I18nKeyEnum.MARK_RESULT_SHERRORCODE_586),
    WX_TICKET_CREATE_FAIL(84507, I18nKeyEnum.MARK_RESULT_SHERRORCODE_587),
    WX_TICKET_STATUS_CHECK_FAIL(84508, I18nKeyEnum.MARK_RESULT_SHERRORCODE_588),
    WX_MEETING_TICKET_NOT_FOUND(84509, I18nKeyEnum.MARK_RESULT_SHERRORCODE_589),
    WX_TICKET_RECEIVE_NOT_FOUND(84510, I18nKeyEnum.MARK_RESULT_SHERRORCODE_590),
    WX_TICKET_CONSUME_CONSUMED(84511, I18nKeyEnum.MARK_RESULT_SHERRORCODE_591),
    WX_TICKET_CONSUME_EXPIRE(84512, I18nKeyEnum.MARK_RESULT_SHERRORCODE_592),
    WX_TICKET_CONSUME_GIFTING(84513, I18nKeyEnum.MARK_RESULT_SHERRORCODE_593),
    WX_TICKET_CONSUME_GIFT_TIMEOUT(84514, I18nKeyEnum.MARK_RESULT_SHERRORCODE_594),
    WX_TICKET_CONSUME_DELETE(84515, I18nKeyEnum.MARK_RESULT_SHERRORCODE_595),
    WX_TICKET_CONSUME_UNAVAILABLE(84516, I18nKeyEnum.MARK_RESULT_SHERRORCODE_596),
    WX_TICKET_CONSUME_OTHER_ERROR(84517, I18nKeyEnum.MARK_RESULT_SHERRORCODE_597),
    CUSTOMIZE_TICKET_CONSUME_CONSUMED(84518, I18nKeyEnum.MARK_RESULT_SHERRORCODE_598),

    // Hexagon 85001-86000
    HEXAGON_SITE_NOT_FOUND(85001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_601),
    HEXAGON_CANT_MODIFITY_OTHER_EA(85002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_602),
    HEXAGON_PAGE_NOT_FOUND(85003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_603),
    HEXAGON_TEMPLATE_SITE_NOT_FOUND(85004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_604),
    HEXAGON_TEMPLATE_PAGE_NOT_FOUND(85005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_605),
    HEXAGON_STATUS_CHANGE_FAILED(85006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_606),
    HEXAGON_PAGE_AT_LEAST_ONE(85007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_607),
    HEXAGON_SITE_STOPED(85008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_608),
    HEXAGON_SITE_DELETED(85009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_609),
    HEXAGON_PAGE_DELETED(85010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_609),
    HEXAGON_SITE_BIND_MARKETING(85011,I18nKeyEnum.MARK_RESULT_SHERRORCODE_611),
    HEXAGON_GROUP_EXIST(85012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_612),
    HEXAGON_GROUP_NOT_EXIST(85013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_126),
    HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME(85014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_614),
    HEXAGON_TEMPLATE_LIST_FAILED(85015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_615),
    HEXAGON_PAGE_NOT_EXIST_FOR_COPY(85016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_616),
    HEXAGON_SITE_NAME_EXIST(85017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_617),
    MICROSTATION_CREATE_FAILED(85018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_618),
    PART_HEXAGON_SITE_NOT_FOUND(85019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_619),
    PARTY_HEXAGON_TEMPLATE_SITE_NOT_FOUND(85020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_620),
    CANT_DELETE_NORMAL_HEXAGON_SITE(85021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_621),

    HEXAGON_TEMPLATE_SITE_NAME_EXIST(85022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_623),

    // 百度广告 86001 - 86200
    BAIDU_ACCOUNT_BIND_ONE(86001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_626),
    BAIDU_API_REQUEST_ERROR(86002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_627),
    BAIDU_API_REQUEST_DATA_ERROR(86003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_628),
    BAIDU_API_REQUEST_DATA_EMPTY(86004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_629),
    CAMPAIGN_RELATE_MARKETING_EVENT_ERROR(86005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_630),
    BAIDU_ACCOUNT_NOT_BIND(86006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_631),
    BAIDU_ACCOUNT_STOP_USE(86007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_632),
    BAIDU_ACCOUNT_REFRESH_GAP(86008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_633),
    BAIDU_ACCOUNT_AUTHORIZED_DENIED(86009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_634),
    UTM_SYNC_STATUS_DENY_WITHOUT_MAPPING(86010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_635),
    BAIDU_ACCOUNT_USERNAME_INVALID(86011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_636),
    BAIDU_ACCOUNT_PASSWORD_INVALID(86012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_637),
    BAIDU_ACCOUNT_TOKEN_INVALID(86013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_638),
    BAIDU_ACCOUNT_ALREADY_BIND(86014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_639),
    BAIDU_ACCOUNT_NOT_BIND_NEED_RESTART(86014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_640),
    BAIDU_ACCOUNT_DELETED(86015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_641),
    AD_ACCOUNT_NOT_FOUND(86016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_642),
    AD_ACCOUNT_EA_NOT_EQUAL(86017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_643),
    AD_OCPC_CONFIG_NOT_FOUND(86018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_644),
    AD_OCPC_TOKEN_NOT_FOUND(86019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_645),
    CRM_LEAD_NOT_FOUND(86020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_646),
    LEAD_NOT_RELATED_MARKETING_EVENT(86021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_647),
    MARKETING_EVENT_NOT_RELATED_CAMPAIGN(86022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_648),
    AD_ACCOUNT_NOT_ENABLE(86023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_649),
    AD_OCPC_NOT_ENABLE(86024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_650),
    AD_INVALID_REBATE_NOT_ENABLE(86025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_651),
    LEAD_NOT_RELATED_MARKETING_PROMOTION_SOURCE(86026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_652),
    LEAD_LANDING_URL_NOT_CONTAIN_BD_VID(86027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_653),
    AD_THIRD_PLATFORM_ID_IS_NULL(86028, I18nKeyEnum.MARK_RESULT_SHERRORCODE_654),
    LEAD_MOBILE_IS_NULL(86029, I18nKeyEnum.MARK_RESULT_SHERRORCODE_655),
    GET_VIRTUAL_PHONE_ERROR(86030, I18nKeyEnum.MARK_RESULT_SHERRORCODE_656),
    TIME_RANGE_ERROR(86031, I18nKeyEnum.MARK_RESULT_SHERRORCODE_657),
    TITLE_TOO_LONG(86032, I18nKeyEnum.MARK_RESULT_SHERRORCODE_658),
    CUSTOMER_ACQUISITION_COST_ARG_ERROR(86033, I18nKeyEnum.MARK_RESULT_SHERRORCODE_659),
    FORBID_EDIT_FIELD(86034, I18nKeyEnum.MARK_RESULT_SHERRORCODE_660),
    LAUNCH_EFFECT_TREND_ARG_ERROR(86035, I18nKeyEnum.MARK_RESULT_SHERRORCODE_661),
    ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR(86036, I18nKeyEnum.MARK_RESULT_SHERRORCODE_662),
    FIELD_NAME_IS_NULL(86037, I18nKeyEnum.MARK_RESULT_SHERRORCODE_663),
    FIELD_ID_IS_NULL(86038, I18nKeyEnum.MARK_RESULT_SHERRORCODE_664),
    COMPARE_TIME_TYPE_ERROR(86039, I18nKeyEnum.MARK_RESULT_SHERRORCODE_665),
    CUSTOMIZE_TIME_BETWEEN_DAY_ERROR(86040, I18nKeyEnum.MARK_RESULT_SHERRORCODE_666),
    MODULE_POSITION_NOT_FOUND(86041, I18nKeyEnum.MARK_RESULT_SHERRORCODE_667),
    TIME_SETTING_ERROR(86042, I18nKeyEnum.MARK_RESULT_SHERRORCODE_668),
    OVER_VIEW_SETTING_NOT_FOUND(86043, I18nKeyEnum.MARK_RESULT_SHERRORCODE_669),
    AREAL_DISTRIBUTE_SETTING_NOT_FOUND(86044, I18nKeyEnum.MARK_RESULT_SHERRORCODE_670),
    AREAL_DISTRIBUTE_SETTING_NOT_ERROR(86045, I18nKeyEnum.MARK_RESULT_SHERRORCODE_671),
    FIELD_VALUE_LIST_NOT_FOUND(86046, I18nKeyEnum.MARK_RESULT_SHERRORCODE_672),
    SELECT_ONE_FIELD_VALUE_ERROR(86047, I18nKeyEnum.MARK_RESULT_SHERRORCODE_673),
    SELECT_FIELD_VALUE_IS_EMPTY(86048, I18nKeyEnum.MARK_RESULT_SHERRORCODE_674),
    TIME_DIFF_TOO_LONG(86049, I18nKeyEnum.MARK_RESULT_SHERRORCODE_675),
    CONVERT_PERIOD_SETTING_NOT_FOUND(86050, I18nKeyEnum.MARK_RESULT_SHERRORCODE_676),
    PROTOTYPE_ACCOUNT_FORBID_UNBIND(86051, I18nKeyEnum.MARK_RESULT_SHERRORCODE_677),
    DO_NOT_CONFIG_SECRET(86052, I18nKeyEnum.MARK_RESULT_SHERRORCODE_678),
    DO_NOT_HAVE_SIGNATURE(86053, I18nKeyEnum.MARK_RESULT_SHERRORCODE_679),
    SIGNATURE_LENGTH_ERROR(86054, I18nKeyEnum.MARK_RESULT_SHERRORCODE_680),
    SIGNATURE_NOT_EQUEAL(86055, I18nKeyEnum.MARK_RESULT_SHERRORCODE_681),
    REQUEST_EXPIRE(86056, I18nKeyEnum.MARK_RESULT_SHERRORCODE_682),
    OCPC_CONFIG_NOT_EXIST(86057, I18nKeyEnum.MARK_RESULT_SHERRORCODE_683),
    AD_RETURN_OBJ_NOT_FOUND(86058, I18nKeyEnum.MARK_RESULT_SHERRORCODE_684),
    AD_ENCRYPTION_TYE_NOT_EXIST(86059, I18nKeyEnum.MARK_RESULT_SHERRORCODE_685),


    // 官网 86201 - 86400
    OFFICIAL_WEBSITE_NOT_FOUND(86201, I18nKeyEnum.MARK_RESULT_SHERRORCODE_689),
    OFFICIAL_WEBSITE_TRACK_NOT_FOUND(86202, I18nKeyEnum.MARK_RESULT_SHERRORCODE_690),
    OFFICIAL_WEBSITE_CUSTOMIZE_ID_REPEAT(86203, I18nKeyEnum.MARK_RESULT_SHERRORCODE_691),
    OFFICIAL_WEBSITE_ATTRIBUTES_CUSTOMIZE_ID_REPEAT(86204, I18nKeyEnum.MARK_RESULT_SHERRORCODE_692),
    OFFICIAL_WEBSITE_NAME_TOO_LONG(86205, I18nKeyEnum.MARK_RESULT_SHERRORCODE_693),
    OFFICIAL_WEBSITE_NAME_EXIST(86206, I18nKeyEnum.MARK_RESULT_SHERRORCODE_694),
    OFFICIAL_WEBSITE_BIND_EXIST(86207, I18nKeyEnum.MARK_RESULT_SHERRORCODE_695),

    //营销通顾问 86401-86600
    MARKETING_COUNSELOR_OUTAPI_ERROR(86401,I18nKeyEnum.MARK_RESULT_SHERRORCODE_698),
    MARKETING_COUNSELOR_INIT_ERROR(86402,I18nKeyEnum.MARK_RESULT_SHERRORCODE_699),
    MARKETING_COUNSELOR_WECHAT_ERROR(86403,I18nKeyEnum.MARK_RESULT_SHERRORCODE_700),


    //企业微信9000 - 9200
    QYWX_GET_ACCESS_TOKEN_FAIL(9000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_704),
    QYWX_GET_CORP_TICKET_SIGNATURE_FAIL(9001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_705),
    QYWX_GET_AGENT_TICKET_SIGNATURE_FAIL(9002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_706),
    QYWX_GET_USER_INFO_FAIL(9003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_707),
    QYWX_GET_BIND_INFO_FAIL(9004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_708),
    QYWX_CREATE_USER_TOKEN_FAIL(9005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_709),
    QYWX_QUERY_CUSTOMER_GROUP_LIST_FAIL(9006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_710),
    QYWX_QUERY_CUSTOMER_GROUP_STATISTICS_FAIL(9006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_711),
    QUERY_QYWX_APP_FAILED(9007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_712),
    QYWX_CONTACT_NOT_FOUND(9008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_713),
    QYWX_CONTACT_ME_FAILED(9009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_714),
    QYWX_SETTING_FAILE(9010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_715),
    QYWX_FAN_QRCODE_NOT_FOUND(9011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_716),
    PART_FAN_QRCODE_NOT_FOUND(9011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_717),
    QYWX_FAN_QRCODE_IS_DELETED(9012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_718),
    QYWX_SEND_MINIAPP_MESSAGE_ERROR(9013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_719),
    QYWX_FAN_QRCODE_DELETED_FAILED(9014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_718),
    ENTERPRISE_NOT_BIND_QYWX(9015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_721),
    QYWX_PHONE_NOT_SAME_FROM_FS(9016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_722),
    QYWX_AGENT_CONFIG_NOT_FOUND(9017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_723),
    QYWX_VIRTUAL_USER_NOT_FOUND(9018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_724),
    QYWX_NOT_NEED_RESET_DATA(9019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_725),
    QYWX_MINIAPP_USER_NOT_FOUND(9020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_726),
    APP_ID_DIFFERENT_TO_BIND_APP(9021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_727),
    USER_BIND_ACCOUNT_NEED_IN_QYWX(9022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_728),
    ENTERPRISE_NEED_BIND_QYWX(9023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_729),
    QYWX_UPLOAD_TOO_LAGER_FILE(9024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_730),
    QYWX_UPLOAD_FILE_TYPE_ERROR(9025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_731),
    QYWX_MOMENT_JOB_ERROR(9026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_732),
    QYWX_SOP_TASK_ERROR(9027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_733),
    QYWX_QR_CODE_BIND_FAILED(9028, I18nKeyEnum.MARK_RESULT_SHERRORCODE_733),
    QYWX_Authorize_WARN(9029, I18nKeyEnum.MARK_RESULT_SHERRORCODE_735),
    QYWX_GROUP_QRCODE_NOT_FOUND(9030, I18nKeyEnum.MARK_RESULT_SHERRORCODE_736),
    PART_GROUP_QRCODE_NOT_FOUND(9031, I18nKeyEnum.MARK_RESULT_SHERRORCODE_737),
    QYWX_GROUP_QRCODE_IS_DELETED(9032, I18nKeyEnum.MARK_RESULT_SHERRORCODE_738),
    QYWX_WELCOME_MSG_NOT_FOUND(9030, I18nKeyEnum.MARK_RESULT_SHERRORCODE_739),
    PART_WELCOME_MSG_NOT_FOUND(9031, I18nKeyEnum.MARK_RESULT_SHERRORCODE_740),
    QYWX_WELCOME_MSG_IS_DELETED(9032, I18nKeyEnum.MARK_RESULT_SHERRORCODE_741),
    QYWX_SOP_NOT_FOUND(9030, I18nKeyEnum.MARK_RESULT_SHERRORCODE_742),
    PART_QYWX_SOP_NOT_FOUND(9031, I18nKeyEnum.MARK_RESULT_SHERRORCODE_743),
    QYWX_SOP_IS_DELETED(9032, I18nKeyEnum.MARK_RESULT_SHERRORCODE_744),
    PARTNER_NOT_EXIST_USER_RELATION(9033, I18nKeyEnum.PARTNER_NOT_EXIST_USER_RELATION),
    PARTNER_USER_RELATION_NOT_EQUAL(9034, I18nKeyEnum.PARTNER_USER_RELATION_NOT_EQUAL),
    PARTNER_BOUNDED_ANOTHER_USER(9035, I18nKeyEnum.PARTNER_BOUNDED_ANOTHER_USER),
    CREATE_USER_FAIL(9035, I18nKeyEnum.CREATE_USER_FAIL),

    //在线直播
    MARKETING_EVENT_CREATE_FAILED(88001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_747),
    MARKETING_LIVE_CREATE_FAILED(88002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_748),
    MARKETING_LIVE_NOT_EXIST(88003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_749),
    MARKETING_LIVE_UPDATE_FAILED(88004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_750),
    MARKETING_LIVE_STATISTICS_NOT_EXIST(88005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_751),
    MARKETING_LIVE_CREATE_COVER_FAILED(88006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_752),
    MARKETING_LIVE_DELETE(88007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_753),
    MARKETING_LIVE_NOT_ENROLL(88008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_754),
    MARKETING_EVENT_DUPLICATE_NAME(88009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_755),
    MARKETING_LIVE_GET_STATUS_FAILED(88010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_756),
    MARKETING_LIVE_SET_COVER(88011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_757),
    MARKETING_CHANGE_PLATFORM_DENY(88012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_758),
    GET_XIAOETONG_ACCESS_TOKEN_FAILED(88013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_759),
    LIST_XIAOKETONG_LIVE_FAILED(88014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_760),
    LOGIN_XIAOKETONG_USER_FAILED(88015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_761),
    QUERY_XIAOETONG_USER_FAILED(88016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_762),
    UN_BIND_XIAOETONG_ACCOUNT(88017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_763),
    XIAOETONG_ACCOUNT_EXIST(88018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_764),
    SYNC_XIAOETONG_LOGIN_FAILED(88019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_765),
    POLYV_SIGN_FAIL(88020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_766),
    LIST_POLYV_LIVE_FAILED(88021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_767),
    DETAIL_POLYV_LIVE_FAILED(88022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_768),
    POLYV_BIND_INFO_ERROR(88023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_769),
    POLYV_NOT_BIND_ACCOUNT(88024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_770),
    XIAOETONG_NOT_BIND_ACCOUNT(88025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_771),
    THIRD_LIVE_RELATION_EXIST(88026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_772),

    LIVE_CHANNELS_ACCOUNT_EXIST(88027, I18nKeyEnum.MARK_RESULT_SHERRORCODE_774),

    //目睹
    MUDU_NOT_BIND_ACCOUNT(88050, I18nKeyEnum.MARK_RESULT_SHERRORCODE_777),
    MUDU_GET_EVENT_LIST_ERROR(88051, I18nKeyEnum.MARK_RESULT_SHERRORCODE_778),
    MUDU_ACCOUNT_EXISTS_BIND(88052, I18nKeyEnum.MARK_RESULT_SHERRORCODE_779),
    MUDU_NOT_EXISTS_PARENT(88053, I18nKeyEnum.MARK_RESULT_SHERRORCODE_780),

    //微吼
    VHALL_NOT_BIND_ACCOUNT(88070, I18nKeyEnum.MARK_RESULT_SHERRORCODE_783),
    VHALL_QUERY_LIVE_ERROR(88071, I18nKeyEnum.MARK_RESULT_SHERRORCODE_784),
    VHALL_GET_LIVE_ERROR(88072, I18nKeyEnum.MARK_RESULT_SHERRORCODE_785),

    //视频
    VIDEO_IS_EXIST(88100, I18nKeyEnum.MARK_RESULT_SHERRORCODE_788),
    UPLOAD_VIDEO_TRANS_FAILED(88101, I18nKeyEnum.MARK_RESULT_SHERRORCODE_789),
    CANCLE_VIDEO_TRANS_FAILED(88102, I18nKeyEnum.MARK_RESULT_SHERRORCODE_790),
    DELETE_VIDEO_FAILED(88103, I18nKeyEnum.MARK_RESULT_SHERRORCODE_790),
    VIDEO_NOT_FOUND(88100, I18nKeyEnum.MARK_RESULT_SHERRORCODE_792),
    PART_VIDEO_NOT_FOUND(88100, I18nKeyEnum.MARK_RESULT_SHERRORCODE_793),

    EA_NOT_BIND_TO_MINIAPP(100001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_795),
    COMMIT_CODE_FAIL(100002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_796),
    SUBMIT_AUDIT_FAIL(100003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_797),
    RELEASE_CODE_FAIL(100004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_798),
    WX_APP_ID_NOT_AUTH_TO_PLATFORM(100005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_799),

    ORDER_PAID(200002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_801),
    ORDER_CLOSED(200003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_802),
    ORDER_EXPIRED(200004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_803),
    ORDER_STATE_UNKNOWN(200005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_804),
    MERCHANT_NOT_CONFIG_YET(210001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_805),

    ENTERPRSE_BIND_MAIL_ACCOUNT(220001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_807),
    ENTERPRSE_NOT_BIND_MAIL_ACCOUNT(220002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_808),
    MAIL_TEMPLATE_COUNT_MAX_LIMIT(220003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_809),
    CREATE_MAIL_TEMPLATE_FAILED(220004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_810),
    PAGE_QUERY_MAIL_TEMPLATE_FAILED(220005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_811),
    QUERY_MAIL_TEMPLATE_DETAIL_FAILED(220006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_812),
    DELETE_MAIL_TEMPLATE_FAILED(220007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_813),
    UPDATE_MAIL_TEMPLATE_FAILED(220008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_814),
    QUERY_MAIL_STATISTICS_FAILED(220009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_815),
    ADD_MAIL_LABEL_FAILED(220010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_816),
    DELETE_MAIL_LABEL_FAILED(220011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_817),
    UPDATE_MAIL_LABEL_FAILED(220012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_818),
    ADD_MAIL_DOMAIN_FAILED(220013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_819),
    MAIL_DOMAIN_CHECK_FAILED(220014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_820),
    UPDATE_MAIL_DOMAIN_FAILED(220015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_821),
    ADD_MAIL_ADDRESS_LIST_FAILED(220016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_822),
    API_USER_NOT_FOUND(220017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_823),
    MAIL_DOMAIN_NOT_EXIST(220018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_824),
    MAIL_DOMAIN_AVAILABLE_NOT_ALLOW_UPDATE(220019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_825),
    MAIL_ADDRESS_EMAIL_ERROR(220020,I18nKeyEnum.MARK_RESULT_SHERRORCODE_826),
    MAIL_ACCOUNT_FOUND_ERROR(220021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_827),
    MAIL_ACCOUNT_INSUFFICIENT_BALANCE(220022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_828),
    MAIL_USER_LIST_NO_EMAIL(22023, I18nKeyEnum.MARK_RESULT_SHERRORCODE_829),
    MAIL_ATTACHMENT_ERROR(22024, I18nKeyEnum.MARK_RESULT_SHERRORCODE_830),
    MAIL_EXPORT_STATUS_ERROR(22025, I18nKeyEnum.MARK_RESULT_SHERRORCODE_831),
    MAIL_ACCOUNT_ERROR(22026, I18nKeyEnum.MARK_RESULT_SHERRORCODE_832),
    // 看板 300000 - 301000
    EXIST_BOARD_CARD_CAN_NOT_DELETE(300000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_834),
    NOT_PERMISSION_WITH_BOARD(300001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_835),
    BOARD_NOT_EXIST(300002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_836),
    // 会员 301001 - 302000
    PHONE_HAVE_BEEN_REGISTERED(301001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_838),
    MEMBER_ACCOUNT_NOT_EXISTED(301002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_839),
    WX_MINI_APP_USER_HAVE_BEEN_BOUND(301003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_840),
    HEXAGON_SITE_ALREADY_IN_ACCESSIBLE_LIST(301004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_841),
    NOT_MEMBER_DENY_ACCESS(301005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_842),
    NOT_MEMBER(301006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_843),
    MEMBER_HAVE_NO_PHONE(301007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_844),
    MEMBER_ENROLL_FAIL(301008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_845),
    MEMBER_WXOPENID_FAIL(301009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_846),
    MEMBER_NOT_EXIST(301010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_846),
    MEMBER_REVIEW_WAITING_OR_FAIL(301011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_848),
    CAN_NOT_TRANSFER_EA(301012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_849),
    CAN_NOT_ENABLE_MEMBER_MARKETING(301013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_850),
    CARD_TEMPLATE_CONFIG_NOT_FOUND(301014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_851),

    ADD_SPREAD_CHANNEL_FAILED(310000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_853),
    EXIST_DEFAULT_CHANNEL(310001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_854),
    LIST_DEFAULT_CHANNEL_FAILED(310002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_855),
    DELETE_CHANNEL_FAILED(310003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_856),
    SPREAD_CHANNEL_EXIST(310004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_857),
    SPREAD_CHANNEL_UPDATE_FAILED(310005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_858),
    EXIST_SAME_CHANNEL_LABEL(310006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_859),
    SPREAD_CHANNEL_NOT_EXIST(310007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_860),
    CANNOT_DELETE_DEFAULT_CHANNEL(310008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_861),
    SPREAD_CHANNEL_DISABLE(310009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_862),
    // 任务 312000 - 312500
    TASK_IN_PROGRESS(312000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_864),
    // 小程序设置 312501 - 312600
    NOT_HOSTING_MINIAPP(312501, I18nKeyEnum.MARK_RESULT_SHERRORCODE_866),
    TIME_RANEG_ERROR(312502, I18nKeyEnum.MARK_RESULT_SHERRORCODE_867),
    THIRD_PLATFORM_NOT_EXIST(312503, I18nKeyEnum.MARK_RESULT_SHERRORCODE_868),
    MINIAPP_NOT_EXIST(312504, I18nKeyEnum.MARK_RESULT_SHERRORCODE_869),

    // 优惠券 302001 - 303000
    NO_COUPON_TEMPLATE(302001,I18nKeyEnum.MARK_RESULT_SHERRORCODE_872),
    SUYUAN_COUPON_NOT_CLOSE(302002,I18nKeyEnum.MARK_RESULT_SHERRORCODE_873),
    VERIFY_SIGN_ERROR(302003,I18nKeyEnum.MARK_RESULT_SHERRORCODE_874),
    CALLBACK_TYPE_ERROR(302004,I18nKeyEnum.MARK_RESULT_SHERRORCODE_875),
    WX_REQUEST_API_ERROR(302005,I18nKeyEnum.MARK_RESULT_SHERRORCODE_876),
    NOT_FIND_SOURCE_PACKAGE(302006,I18nKeyEnum.MARK_RESULT_SHERRORCODE_877),
    COUPON_NOT_COUNT(302007,I18nKeyEnum.MARK_RESULT_SHERRORCODE_878),
    COUPON_INST_OBJ_NOT_CREATED(302008,I18nKeyEnum.MARK_RESULT_SHERRORCODE_879),
    COUPON_OPEN_FAIL(302009,I18nKeyEnum.MARK_RESULT_SHERRORCODE_880),
    PART_COUPON_TEMPLATE_NOT_FOUND(302010,I18nKeyEnum.MARK_RESULT_SHERRORCODE_881),

    NOT_CONNECT_MERCHANT(302011,I18nKeyEnum.MARK_RESULT_SHERRORCODE_883),

    USER_RECEIVE_COUNT(302012,I18nKeyEnum.MARK_RESULT_SHERRORCODE_885),

    NOT_FOUND_COUPON(302013,I18nKeyEnum.MARK_RESULT_SHERRORCODE_887),

    COUNT_MORE_RECEIVE(302014,I18nKeyEnum.MARK_RESULT_SHERRORCODE_889),

    NOT_FOUND_SEND_ACCOUNT(302015,I18nKeyEnum.MARK_RESULT_SHERRORCODE_891),
    NOT_FOUND_RECEIVE_ACCOUNT(302016,I18nKeyEnum.MARK_RESULT_SHERRORCODE_892),

    NOT_FOUND_DOWN_TENANT(302017,I18nKeyEnum.MARK_RESULT_SHERRORCODE_894),

    NOT_FOUND_DOWN_ACCOUNT(302018,I18nKeyEnum.MARK_RESULT_SHERRORCODE_896),

    COUPON_INVALIDATION(302019,I18nKeyEnum.MARK_RESULT_SHERRORCODE_898),

    COUPON_INVALIDATION_NOT_RECEIVE(302020,I18nKeyEnum.MARK_RESULT_SHERRORCODE_900),

    NOT_RECEIVE_STORE_RANGE(302021,I18nKeyEnum.MARK_RESULT_SHERRORCODE_902),

    NOT_AUTH_LOOKUP_COUPON(302022,I18nKeyEnum.MARK_RESULT_SHERRORCODE_904),

    // 头条广告 312601 - 312700
    HEADLINES_API_REQUEST_ERROR(312601, I18nKeyEnum.MARK_RESULT_SHERRORCODE_907),
    HEADLINES_API_REQUEST_DATA_EMPTY(312602, I18nKeyEnum.MARK_RESULT_SHERRORCODE_908),
    HEADLINES_API_REQUEST_DATA_ERROR(312603, I18nKeyEnum.MARK_RESULT_SHERRORCODE_909),
    HEADLINES_ACCOUNT_AUTHORIZED_DENIED(312604, I18nKeyEnum.MARK_RESULT_SHERRORCODE_910),
    AD_ACCOUNT_TYPE_ERROR(312605, I18nKeyEnum.MARK_RESULT_SHERRORCODE_911),
    HEADLINES_GET_ACCESS_TOKEN_ERROR(312606, I18nKeyEnum.MARK_RESULT_SHERRORCODE_912),
    HEADLINES_REFRESH_ACCESS_TOKEN_ERROR(312607, I18nKeyEnum.MARK_RESULT_SHERRORCODE_913),
    AD_RELATE_SUB_MARKETING_ERROR(312608, I18nKeyEnum.MARK_RESULT_SHERRORCODE_914),
    AD_ACCOUNT_NOT_EXIST_ERROR(312609, I18nKeyEnum.MARK_RESULT_SHERRORCODE_915),
    HEADLINES_PERMISSION_ERROR(312610, I18nKeyEnum.MARK_RESULT_SHERRORCODE_916),
    HEADLINES_SQL_FILTER_FIELD_ERROR(312611, I18nKeyEnum.MARK_RESULT_SHERRORCODE_917),
    HEADLINES_REQUEST_TOO_FREQUENT(312612, I18nKeyEnum.MARK_RESULT_SHERRORCODE_918),
    HEADLINES_ERROR_CODE_INVALID_PARTNER(312613, I18nKeyEnum.MARK_RESULT_SHERRORCODE_919),
    HEADLINES_ACCESS_TOKEN_EXPIRE(312614, I18nKeyEnum.MARK_RESULT_SHERRORCODE_920),
    HEADLINES_REFRESH_TOKEN_EXPIRE(312615, I18nKeyEnum.MARK_RESULT_SHERRORCODE_921),
    HEADLINES_EMPTY_ACCESS_TOKEN(312616, I18nKeyEnum.MARK_RESULT_SHERRORCODE_922),
    HEADLINES_INVALID_ACCESS_TOKEN(312617, I18nKeyEnum.MARK_RESULT_SHERRORCODE_923),
    HEADLINES_INVALID_CORE_USER(312618, I18nKeyEnum.MARK_RESULT_SHERRORCODE_924),
    HEADLINES_INVALID_REFRESH_TOKEN(312619, I18nKeyEnum.MARK_RESULT_SHERRORCODE_925),
    HEADLINES_INVALID_GRANT_TYPE(312620, I18nKeyEnum.MARK_RESULT_SHERRORCODE_926),
    HEADLINES_ERROR_AES_DECIPHER(312621, I18nKeyEnum.MARK_RESULT_SHERRORCODE_927),
    HEADLINES_RECHARGE_AMOUNT_TOO_SMALL(312622, I18nKeyEnum.MARK_RESULT_SHERRORCODE_928),
    HEADLINES_BALANCE_NOT_ENOUGH(312623, I18nKeyEnum.MARK_RESULT_SHERRORCODE_929),
    HEADLINES_ADVERTISER_IS_NOT_ENABLE(312624, I18nKeyEnum.MARK_RESULT_SHERRORCODE_930),
    HEADLINES_ADVERTISER_IS_IN_BLACK_LIST(312625, I18nKeyEnum.MARK_RESULT_SHERRORCODE_931),
    HEADLINES_PASSWORD_TOO_SIMPLE(312626, I18nKeyEnum.MARK_RESULT_SHERRORCODE_932),
    HEADLINES_EMAIL_EXISTED(312627, I18nKeyEnum.MARK_RESULT_SHERRORCODE_933),
    HEADLINES_INVALID_EMAIL(312628, I18nKeyEnum.MARK_RESULT_SHERRORCODE_934),
    HEADLINES_NAME_EXISTED(312629, I18nKeyEnum.MARK_RESULT_SHERRORCODE_935),
    HEADLINES_SIGNATURE_ERROR(312630, I18nKeyEnum.MARK_RESULT_SHERRORCODE_936),
    HEADLINES_SYS_ERROR(312631, I18nKeyEnum.MARK_RESULT_SHERRORCODE_8),
    HEADLINES_DEV_HEALTH(312632, I18nKeyEnum.MARK_RESULT_SHERRORCODE_938),
    HEADLINES_CUS_HEALTH(312633, I18nKeyEnum.MARK_RESULT_SHERRORCODE_939),
    HEADLINES_REMOTE_NULL(312634, I18nKeyEnum.MARK_RESULT_SHERRORCODE_940),
    HEADLINES_REMOTE_DATA_NULL(312635, I18nKeyEnum.MARK_RESULT_SHERRORCODE_941),
    HEADLINES_AUTH_SUCCESS(312636, I18nKeyEnum.MARK_RESULT_SHERRORCODE_942),
    AD_NOT_SYNC(312637, I18nKeyEnum.MARK_RESULT_SHERRORCODE_943),
    HEADLINES_AUTHORISE_USER_ERROR(312638, I18nKeyEnum.MARK_RESULT_SHERRORCODE_944),
    HEADLINES_AUTHORISE_ACCOUNT_ERROR(312639, I18nKeyEnum.MARK_RESULT_SHERRORCODE_945),
    HEADLINES_CAN_NOT_FOUND_ADVERTISER(312640, I18nKeyEnum.MARK_RESULT_SHERRORCODE_946),
    HEADLINES_CAN_NOT_FOUND_ADVERTISER_PUBLIC_INFO(312641, I18nKeyEnum.MARK_RESULT_SHERRORCODE_947),

    //通讯录 312701 - 312800
    BOOK_USER_NOT_EXIST(312701,I18nKeyEnum.MARK_RESULT_SHERRORCODE_950),

    // 物料open api 312801 - 312900
    MATERIAL_TYPE_CANT_NULL(312801, I18nKeyEnum.MARK_RESULT_SHERRORCODE_953),
    PRODUCT_ARG_CANT_NULL(312802, I18nKeyEnum.MARK_RESULT_SHERRORCODE_954),
    PRODUCT_NAME_CANT_NULL(312803, I18nKeyEnum.MARK_RESULT_SHERRORCODE_955),
    PRODUCT_SUMMARY_CANT_NULL(312803, I18nKeyEnum.MARK_RESULT_SHERRORCODE_956),
    PRODUCT_HEAD_PICTURE_CANT_NULL(312804, I18nKeyEnum.MARK_RESULT_SHERRORCODE_957),
    PRODUCT_DETAIL_CANT_NULL(312805, I18nKeyEnum.MARK_RESULT_SHERRORCODE_958),
    TRY_FORM_ID_CANT_NULL(312806, I18nKeyEnum.MARK_RESULT_SHERRORCODE_959),
    TRY_BUTTON_VALUE_CANT_NULL(312807, I18nKeyEnum.MARK_RESULT_SHERRORCODE_960),
    PRODUCT_HEAD_PICTURE_LIMIT(312808, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961),
    PRODUCT_DETAIL_PICTURE_LIMIT(312809, I18nKeyEnum.MARK_RESULT_SHERRORCODE_962),
    PRODUCT_PICTURE_NAME_ERROR(312810, I18nKeyEnum.MARK_RESULT_SHERRORCODE_963),
    UNKNOWN_MATERIAL_TYPE(312811, I18nKeyEnum.MARK_RESULT_SHERRORCODE_964),
    VIDEO_URL_NOT_SUPPORT(312812, I18nKeyEnum.MARK_RESULT_SHERRORCODE_965),
    IS_UPDATE_PICTURE_CANT_NULL(312813, I18nKeyEnum.MARK_RESULT_SHERRORCODE_966),
    TRY_BUTTON_VALUE_LENGTH_LIMIT(312814, I18nKeyEnum.MARK_RESULT_SHERRORCODE_967),
    MATERIAL_ID_CANT_NULL(312815, I18nKeyEnum.MARK_RESULT_SHERRORCODE_968),
    PRODUCT_QUERY_CANT_NULL(312816, I18nKeyEnum.MARK_RESULT_SHERRORCODE_969),
    PAGE_PARAM_CANT_NULL(312817, I18nKeyEnum.MARK_RESULT_SHERRORCODE_970),
    PAGE_SIZE_OUT_LIMIT(312818, I18nKeyEnum.MARK_RESULT_SHERRORCODE_971),
    PAGE_NUM_OUT_LIMIT(312817, I18nKeyEnum.MARK_RESULT_SHERRORCODE_972),

    // 多会场活动
    MULTIVENUE_RELATE_SUB_ERROR(312901,I18nKeyEnum.MARK_RESULT_SHERRORCODE_975),

    REVOCATION_FAIL(312902,I18nKeyEnum.MARK_RESULT_SHERRORCODE_977),

    // 广告数据回传 313000 - 313100
    TARGET_OBJ_API_NAME_NOT_FOUND(313000,I18nKeyEnum.MARK_RESULT_SHERRORCODE_980),
    LEAD_CONVERT_TARGET_NOT_FOUND(313001,I18nKeyEnum.MARK_RESULT_SHERRORCODE_981),
    ROLES_NOT_FOUND(313002,I18nKeyEnum.MARK_RESULT_SHERRORCODE_982),
    CONVERSION_TYPE_NOT_FOUND(313003,I18nKeyEnum.MARK_RESULT_SHERRORCODE_983),
    GROUP_MSG_SENDER_NOT_FOUND(313004,I18nKeyEnum.MARK_RESULT_SHERRORCODE_984),

    GROUP_MSG_CHAT_ID_LIST_SIZE_EXCEEDED(313005,I18nKeyEnum.MARK_RESULT_SHERRORCODE_986),

    //营销客服一体化  313101-313200
    NEED_MARKETING_PRO_VERSION(313101,I18nKeyEnum.MARK_RESULT_SHERRORCODE_989),
    NEED_ORDER_DHT(313102,I18nKeyEnum.MARK_RESULT_SHERRORCODE_990),
    NOT_BUG_CUSTOMER_SERVICE(313103,I18nKeyEnum.MARK_RESULT_SHERRORCODE_991),

    //数据权限
    DATA_OWN_ORGANIZATION(313104,I18nKeyEnum.MARK_RESULT_SHERRORCODE_994),
    NO_DATA_OWN_ORGANIZATION(313105,I18nKeyEnum.MARK_RESULT_SHERRORCODE_995),

    NEED_MARKETING_STREN_VERSION(313106,I18nKeyEnum.MARK_RESULT_SHERRORCODE_997),


    //营销动态 313301-313400
    IDENTIFY_TRANSFER_ERROR(313301,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1001),
    ACTION_TYPE_NOT_REGISTER(313302,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1002),
    ACTION_DESCRIPTION_CAN_NOT_NULL(313303,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1003),

    NOT_BUG_KNOWLEDGE_MANAGEMENT_APP(313106,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1005),

    TENANT_BRAND_COLOR_NOT_FIND(314100,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1007),

    NOT_OPEN_ENTERPRISE_INFO_OBJ(314101,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1009),

    // 营销通参数鉴权 313401-313500
    EA_NOT_FOUND(313401,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1012),
    TIME_STAMP_NOT_FOUND(313402,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1013),
    NONCE_NOT_FOUND(313403,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1014),
    NONCE_TOO_LONG(313404,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1015),
    NONCE_REPEAT(313405,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1016),
    TIME_STAMP_EXPIRE(313406,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1017),
    SIGN_NOT_FOUND(313407,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1018),
    SIGN_VERIFY_ERROR(313408,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1019),
    SECRET_KEY_NOT_CONFIG(313409,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1020),
    MSG_REPEAT(313410,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1021),


    //shanshanedit
    CREATE_EMAIL_FAIL(314101,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1025),
    GET_EMAIL_CODE_FAIL(314102,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1026),

    DETELE_TEMPLATE_FAIL(314103,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1028),

    //活动成员
    CREATE_CAPMAIGN_MEMBER_FAILD(315000,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1031),

    ADD_CAMPAIGN_MEMBER_BEYOND_LIMIT(315001,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1033),

    TARGET_CROWD_OPERATION_CREATE_FAIL(900001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1035),
    TARGET_CROWD_OPERATION_UPDATE_FAIL(900002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1036),
    TARGET_CROWD_OPERATION_ILLEGALITY_SETTING(900003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1037),

    //AI助手
    AI_CHAT_QUESTION_TOO_LONG(910001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1040),

    //seo
    OFFICIAL_WEBSITE_SEO_DATA_NOT_FOUND(920001,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1043),

    OFFICIAL_WEBSITE_DOMAIN_RESOLUTION_EXCEPTION(920002,I18nKeyEnum.MARK_RESULT_SHERRORCODE_1045),



    // 市场活动归因数据
    ATTRIBUTE_CALCULATING(930001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1050),
    MQL_DEFINITION_MUST_SELECTED(930002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1051),
    SQL_DEFINITION_MUST_SELECTED(930003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1052),

    // whatsapp
    ENTERPRISE_IS_BIND_WHATSAPP(940001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1055),
    ENTERPRISE_NOT_HAVE_AUTHORIZATION(940002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1056),
    FORBID_TO_OPEN_WHATSAPP_PLUGIN(940003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1057),
    PHONE_CAN_NOT_EMPTY(940004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1058),
    SEND_TYPE_CAN_NOT_NULL(940006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1059),
    SEND_TARGET_CAN_NOT_NULL(940007, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1060),
    TA_PATH_CAN_NOT_NULL(940008, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1061),
    MARKETING_USER_GROUP_CAN_NOT_NULL(940009, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1062),
    TEMPLATE_CAN_NOT_NULL(940010, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1063),
    SEND_TIME_CAN_NOT_NULL(940011, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1064),
    TEMPLATE_NOT_EXIST(940012, I18nKeyEnum.MARK_RESULT_SHERRORCODE_347),
    PARAMETER_COUNT_NOT_MATCH(940013, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1066),
    SEND_TASK_NOT_EXIST(940014, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1067),
    TASK_IS_OPERATING(940015, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1068),
    ONLY_CAN_EDIT_SCHEDULE_TASK(940016, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1069),
    ONLY_CAN_DELETE_WAIT_CANCEL_ERROR_TASK(940017, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1070),
    ONLY_CAN_DELETE_SCHEDULE_TASK(940018, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1071),
    ONLY_CAN_CANCEL_SCHEDULE_TASK(940019, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1072),
    ONLY_CAN_CANCEL_WAIT_TASK(940020, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1073),
    ONLY_CAN_UPDATE_WAIT_TASK(940021, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1074),
    SAVE_AUTHORIZATION_ERROR(940022, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1075),
    WHATSAPP_GROUP_DATA_NOT_SYNC(940023, I18nKeyEnum.MARK_WHATSAPP_WHATSAPPMANAGER_803),
    WHATSAPP_CONTACT_DATA_NOT_SYNC(940024, I18nKeyEnum.MARK_WHATSAPP_WHATSAPPMANAGER_804),

    // 领英连接器
    FORBID_OPEN_LINKEDIN_PLUGINS(950001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1078),

    FORBID_OPEN_MARKETING_AI_PLUGIN(960001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_1080),

    INVALID_ACCOUNT_TYPE (960002, I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_1088)),

    MENU_IS_NOT_OBJECT_GROUP_RULE (960003, I18nKeyEnum.MENU_IS_NOT_OBJECT_GROUP_RULE),

    EMPLOYEE_TYPE_FORBID_BIND_PARTNER_CARD(960004, I18nKeyEnum.EMPLOYEE_TYPE_FORBID_BIND_PARTNER_CARD),

    MEMBER_TYPE_FORBID_BIND_PARTNER_CARD(960005, I18nKeyEnum.MEMBER_TYPE_FORBID_BIND_PARTNER_CARD),

    PARTNER_NOT_EXIST(960006, I18nKeyEnum.PARTNER_NOT_EXIST),

    MOBILE_BOUND_UPSTREAM_EA(960007, I18nKeyEnum.MOBILE_BOUND_UPSTREAM_EA),

    MOBILE_BOUND_OTHER_PUBLIC_ENTERPRISE(960008, I18nKeyEnum.MOBILE_BOUND_OTHER_PUBLIC_ENTERPRISE),


    //CTA组件
    CTA_NOT_FOUND(961000, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961000),
    PART_CTA_NOT_FOUND(961001, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961001),
    CTA_EXISTS_RELATION(961002, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961002),
    CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE(961003, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961003),
    CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE(961004, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961004),
    CTA_GUIDE_COMPONENT_FEWER_THAN_THREE(961005, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961005),
    CTA_NAME_EXISTS(961006, I18nKeyEnum.MARK_RESULT_SHERRORCODE_961006),

    ;



    private int errorCode;
    private String errorMessage;
    private I18nKeyEnum i18nKeyEnum;

    SHErrorCode(int errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.i18nKeyEnum = null;
    }

    SHErrorCode(int errorCode, I18nKeyEnum i18nKeyEnum) {
        this.errorCode = errorCode;
        this.errorMessage = i18nKeyEnum.getDefaultValue();
        this.i18nKeyEnum = i18nKeyEnum;
    }

    SHErrorCode(int errorCode, String errorMessage, I18nKeyEnum i18nKeyEnum) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.i18nKeyEnum = i18nKeyEnum;
    }

    public static SHErrorCode getByCode(int code) {
        for (SHErrorCode shErrorCode : values()) {
            if (shErrorCode.getErrorCode() == code) {
                return shErrorCode;
            }
        }
        return null;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return i18nKeyEnum != null ? I18nUtil.get(i18nKeyEnum) : errorMessage;
    }
}