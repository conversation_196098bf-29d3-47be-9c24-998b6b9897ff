package com.facishare.marketing.common.enums.hexagon;

import com.google.common.collect.ImmutableSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1系统 2自定义 3会议-市场活动设置模板 4直播-市场活动设置模板
 */
@AllArgsConstructor
@Getter
public enum HexagonTemplateTypeEnum {
    SYSTEM(1),
    CUSOTM(2),
    CONFERENCE(3, "conference"),
    LIVE(4, "live"),
    MICROSTATION_DECORATION_TEMPLATE(5, "microstation_decoration_template"),  //微站装修模板
    ;

    private final Integer type;
    private String fieldName;

    HexagonTemplateTypeEnum(Integer type) {
        this.type = type;
    }

    public static boolean isValid(Integer type){
        for (HexagonTemplateTypeEnum value : values()) {
            if (value.type.equals(type)){
                return true;
            }
        }
        return false;
    }

    public static final Set<HexagonTemplateTypeEnum> SCENE_TYPE = ImmutableSet.of(CONFERENCE, LIVE);
}
