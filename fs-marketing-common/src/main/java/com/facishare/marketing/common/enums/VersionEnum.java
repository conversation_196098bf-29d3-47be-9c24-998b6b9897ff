package com.facishare.marketing.common.enums;

import com.facishare.marketing.common.enums.advertiser.headlines.AccountRoleEnum;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@ToString
public enum VersionEnum {
    STREN("marketing_strategy_strengthen_app", 1),
    PRO("marketing_strategy_pro_app", 2),
    STAN("marketing_strategy_stan_app", 3),

    // 钉钉小程序版本
    PRO_DINGTALK_500_APP("marketing_strategy_pro_dingtalk_500_app", 10),
    STAN_DINGTALK_500_APP("marketing_strategy_stan_dingtalk_500_app", 11),
    PRO_DINGTALK_100_APP("marketing_strategy_pro_dingtalk_100_app", 12),
    STAN_DINGTALK_100_APP("marketing_strategy_stan_dingtalk_100_app", 13),
    PRO_DINGTALK_30_APP("marketing_strategy_pro_dingtalk_30_app", 14),
    STAN_DINGTALK_30_APP("marketing_strategy_stan_dingtalk_30_app", 15),
    DING_FREE_APP("marketing_strategy_free_dingtalk_app", 16),

    THIRD_PLATFORM_BIND("mkt_miniapp_service_app", 20),
    CHUANG_KE_TIE_APP("chuangkitdesign_app", 21),
    ENTERPRISE_LIBRARY_APP("enterprise_library_app", 22),
    MARKETING_CRM_INTEGRATING("marketing_integration_app", 23),  //营销一体化

    //在线客服插件license
    CUSTOMER_SERVICE_APP("customerservice_app",51),   //在线客服标准版
    CUSTOMER_SERVICE_PRO_APP("customerservice_pro_app",52),  //在线客服高级版本

    //广告营销插件
    MARKETING_AD_PLUGIN_APP("marketing_ad_plugin_app",53),  //在线客服高级版本

    KNOWLEDGE_MANAGEMENT_APP("knowledgemanagement_app",54),  //知识库版本
    OVERSEAS_PLUGIN("marketing_overseas_plugin_app",55),  //海外营销插件

    MARKETING_AI_PLUGIN_APP("marketing_ai_plugin_app",56),  //AI营销插件
    BEHAVIOR_INTEGRAL_APP("behavior_integral_app",57),  //行为积分

    //新版本插件 100-200
    MARKETING_MEETING_PLUGIN_APP("marketing_meeting_plugin_app",100), //会议营销插件
    MARKETING_LIVE_PLUGIN_APP("marketing_live_plugin_app",101), //会议营销插件
    MARKETING_WXMINI_PLUGIN_APP("marketing_wxmini_plugin_app",102), //数字展厅插件
    MARKETING_PARTNER_PLUGIN_APP("marketing_partner_plugin_app",103), //伙伴营销插件
    MARKETING_MEMBER_PLUGIN_APP("marketing_member_plugin_app",104), //会员营销插件
    MARKETING_WECOM_PLUGIN_APP("marketing_wecom_plugin_app",105), //企微营销插件
    FACEBOOK_PLUGIN_APP("facebook_data_sync_app",106), //Facebook营销插件
    GOOGLE_PLUGIN_APP("google_data_sync_app",107), //Google营销插件
    LINKED_PLUGIN_APP("linkedin_data_sync_app",108), //LinkedIn营销插件
    ZHIHU_PLUGIN_APP("zhihu_huabao_data_sync_app",109),//知乎营销插件
    SOUGOU_PLUGIN_APP("sogo_xiansuocrm_data_sync_app",110),//搜狗营销插件
    KUAISHOU_PLUGIN_APP("kuaishou_xiansuocrm_data_sync_app",111),//快手营销插件
    SHENMA_PLUGIN_APP("uc_shenma_data_sync_app",112),//UC神马营销插件
    RED_BOOK_PLUGIN_APP("redbook_data_sync_app",113),
    DOU_YIN_LIFE_PLUGIN_APP("douyinlife_data_sync_app",114),  // 抖音来客营销插件
    ;

    private String version;
    private int priority;

    VersionEnum(String version, int priority) {
        this.version = version;
        this.priority = priority;
    }

    public static Integer fromVersion(String version) {
        for (VersionEnum value : VersionEnum.values()) {
            if (String.valueOf(value.version).equals(version)) {
                return value.priority;
            }
        }
        return null;
    }

    public static String fromPriority(int priority) {
        for (VersionEnum value : VersionEnum.values()) {
            if (value.priority == priority) {
                return value.version;
            }
        }
        return null;
    }

    public static boolean isDingDingVersion(String version){
        if (StringUtils.isEmpty(version)){
            return false;
        }

        if (version.contains("dingtalk")){
            return true;
        }

        return false;
    }
}
