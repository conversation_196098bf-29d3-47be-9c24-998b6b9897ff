package com.facishare.marketing.common.enums;

public enum I18nKeyEnum {
    MARK_RESULT_SHERRORCODE_4("mark.result.sherrorcode_4", "成功"),
    MARK_RESULT_SHERRORCODE_6("mark.result.sherrorcode_6", "参数错误"),
    MARK_RESULT_SHERRORCODE_7("mark.result.sherrorcode_7", "系统繁忙"),
    MARK_RESULT_SHERRORCODE_8("mark.result.sherrorcode_8", "系统错误"),
    MARK_RESULT_SHERRORCODE_9("mark.result.sherrorcode_9", "数据库操作失败"),
    MARK_RESULT_SHERRORCODE_10("mark.result.sherrorcode_10", "未知错误"),
    MARK_RESULT_SHERRORCODE_11("mark.result.sherrorcode_11", "JSON序列化失败"),
    MARK_RESULT_SHERRORCODE_12("mark.result.sherrorcode_12", "数据不存在"),
    MARK_RESULT_SHERRORCODE_13("mark.result.sherrorcode_13", "手机号码不合法"),
    MARK_RESULT_SHERRORCODE_14("mark.result.sherrorcode_14", "发送短信太频繁"),
    MARK_RESULT_SHERRORCODE_15("mark.result.sherrorcode_15", "用户不是应用管理员"),
    MARK_RESULT_SHERRORCODE_16("mark.result.sherrorcode_16", "暂未在灰度范围内,敬请期待"),
    MARK_RESULT_SHERRORCODE_17("mark.result.sherrorcode_17", "JSON反序列化失败"),
    MARK_RESULT_SHERRORCODE_18("mark.result.sherrorcode_18", "请求第三方响应异常"),
    MARK_RESULT_SHERRORCODE_19("mark.result.sherrorcode_19", "线程未执行完成"),
    MARK_RESULT_SHERRORCODE_20("mark.result.sherrorcode_20", "未购买"),
    MARK_RESULT_SHERRORCODE_21("mark.result.sherrorcode_21", "不是灰度企业"),
    MARK_RESULT_SHERRORCODE_22("mark.result.sherrorcode_22", "获取本机ip失败"),
    MARK_RESULT_SHERRORCODE_23("mark.result.sherrorcode_23", "身份鉴权失败"),
    MARK_RESULT_SHERRORCODE_24("mark.result.sherrorcode_24", "数据为空或多于1条"),
    MARK_RESULT_SHERRORCODE_25("mark.result.sherrorcode_25", "数据多于1条"),
    MARK_RESULT_SHERRORCODE_26("mark.result.sherrorcode_26", "创建短链失败"),
    MARK_RESULT_SHERRORCODE_27("mark.result.sherrorcode_27", "手机校验码错误"),
    MARK_RESULT_SHERRORCODE_28("mark.result.sherrorcode_28", "市场活动已经存在"),
    MARK_RESULT_SHERRORCODE_29("mark.result.sherrorcode_29", "禁止访问的路径"),
    MARK_RESULT_SHERRORCODE_30("mark.result.sherrorcode_30", "设置转发信息失败"),
    MARK_RESULT_SHERRORCODE_31("mark.result.sherrorcode_31", "未购买营销通"),
    MARK_RESULT_SHERRORCODE_32("mark.result.sherrorcode_32", "禁止在%s-%s点发送营销消息"),
    MARK_RESULT_SHERRORCODE_35("mark.result.sherrorcode_35", "获取微信后台数据失败"),
    MARK_RESULT_SHERRORCODE_36("mark.result.sherrorcode_36", "微信后台数据错误"),
    MARK_RESULT_SHERRORCODE_37("mark.result.sherrorcode_37", "解密失败"),
    MARK_RESULT_SHERRORCODE_38("mark.result.sherrorcode_38", "相关微信用户数据获取失败"),
    MARK_RESULT_SHERRORCODE_39("mark.result.sherrorcode_39", "获取企业微信用户的纷享身份失败"),
    MARK_RESULT_SHERRORCODE_40("mark.result.sherrorcode_40", "企业微信内部accessToken获取失败"),
    MARK_RESULT_SHERRORCODE_42("mark.result.sherrorcode_42", "token无效"),
    MARK_RESULT_SHERRORCODE_43("mark.result.sherrorcode_43", "需要重新登录"),
    MARK_RESULT_SHERRORCODE_44("mark.result.sherrorcode_44", "验证码错误"),
    MARK_RESULT_SHERRORCODE_45("mark.result.sherrorcode_45", "本账号已被禁用"),
    MARK_RESULT_SHERRORCODE_46("mark.result.sherrorcode_46", "小程序appId无效"),
    MARK_RESULT_SHERRORCODE_47("mark.result.sherrorcode_47", "未绑定专属小程序"),
    MARK_RESULT_SHERRORCODE_48("mark.result.sherrorcode_48", "小程序appid不存在"),
    MARK_RESULT_SHERRORCODE_49("mark.result.sherrorcode_49", "获取小程序access_token失败"),
    MARK_RESULT_SHERRORCODE_50("mark.result.sherrorcode_50", "请重新授权小程序"),
    MARK_RESULT_SHERRORCODE_51("mark.result.sherrorcode_51", "员工会员登录需刷新token"),
    MARK_RESULT_SHERRORCODE_52("mark.result.sherrorcode_52", "使用token获取用户信息失败"),
    MARK_RESULT_SHERRORCODE_53("mark.result.sherrorcode_53", "获取钉钉用户详情失败"),
    MARK_RESULT_SHERRORCODE_54("mark.result.sherrorcode_54", "注册人数达到限制，请升级更高版本"),
    MARK_RESULT_SHERRORCODE_55("mark.result.sherrorcode_55", "获取openid失败"),
    MARK_RESULT_SHERRORCODE_56("mark.result.sherrorcode_56", "登录失败"),
    MARK_RESULT_SHERRORCODE_59("mark.result.sherrorcode_59", "获取验证码失败"),
    MARK_RESULT_SHERRORCODE_60("mark.result.sherrorcode_60", "验证码验证失败"),
    MARK_RESULT_SHERRORCODE_61("mark.result.sherrorcode_61", "获取企业列表失败"),
    MARK_RESULT_SHERRORCODE_62("mark.result.sherrorcode_62", "cookie失效"),
    MARK_RESULT_SHERRORCODE_63("mark.result.sherrorcode_63", "找不到cookie"),
    MARK_RESULT_SHERRORCODE_64("mark.result.sherrorcode_64", "用户帐号不存在"),
    MARK_RESULT_SHERRORCODE_65("mark.result.sherrorcode_65", "需要图形验证码"),
    MARK_RESULT_SHERRORCODE_66("mark.result.sherrorcode_66", "发送验证码太频繁，请一分钟后再次获取"),
    MARK_RESULT_SHERRORCODE_67("mark.result.sherrorcode_67", "企业帐号已终止"),
    MARK_RESULT_SHERRORCODE_68("mark.result.sherrorcode_68", "图形验证码错误"),
    MARK_RESULT_SHERRORCODE_69("mark.result.sherrorcode_69", "未注册手机号"),
    MARK_RESULT_SHERRORCODE_70("mark.result.sherrorcode_70", "纷享企业账号无效"),
    MARK_RESULT_SHERRORCODE_71("mark.result.sherrorcode_71", "纷享账号无效禁止登录"),
    MARK_RESULT_SHERRORCODE_72("mark.result.sherrorcode_72", "纷享账号被停用"),
    MARK_RESULT_SHERRORCODE_73("mark.result.sherrorcode_73", "需要强制扫码登录,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_74("mark.result.sherrorcode_74", "是初始化密码并且绑定了手机号,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_75("mark.result.sherrorcode_75", "初始化密码却没有绑定手机号,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_76("mark.result.sherrorcode_76", "用动态密码登录且从未设置密码,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_77("mark.result.sherrorcode_77", "用动态密码登录且未初始密码,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_78("mark.result.sherrorcode_78", "弱密码并且绑定手机号,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_79("mark.result.sherrorcode_79", "弱密码却没有绑定手机号,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_80("mark.result.sherrorcode_80", "需要短信验证码授权,请登录网页端操作"),
    MARK_RESULT_SHERRORCODE_81("mark.result.sherrorcode_81", "需要短信验证码或者其他设备授权"),
    MARK_RESULT_SHERRORCODE_83("mark.result.sherrorcode_83", "该手机号未注册"),
    MARK_RESULT_SHERRORCODE_84("mark.result.sherrorcode_84", "该手机号已注册"),
    MARK_RESULT_SHERRORCODE_85("mark.result.sherrorcode_85", "找不到该注册用户"),
    MARK_RESULT_SHERRORCODE_86("mark.result.sherrorcode_86", "该用户已存在"),
    MARK_RESULT_SHERRORCODE_87("mark.result.sherrorcode_87", "FSApplyInfoKey无效"),
    MARK_RESULT_SHERRORCODE_88("mark.result.sherrorcode_88", "该手机号已被其他微信号所注册"),
    MARK_RESULT_SHERRORCODE_89("mark.result.sherrorcode_89", "新手机号不能与旧手机号相同"),
    MARK_RESULT_SHERRORCODE_90("mark.result.sherrorcode_90", "更换手机号失败"),
    MARK_RESULT_SHERRORCODE_91("mark.result.sherrorcode_91", "CookieKey无效"),
    MARK_RESULT_SHERRORCODE_93("mark.result.sherrorcode_93", "找不到该访客"),
    MARK_RESULT_SHERRORCODE_94("mark.result.sherrorcode_94", "用户创建失败"),
    MARK_RESULT_SHERRORCODE_96("mark.result.sherrorcode_96", "小程序菜单栏设置序号有误"),
    MARK_RESULT_SHERRORCODE_97("mark.result.sherrorcode_97", "至少需要保留一个菜单"),
    MARK_RESULT_SHERRORCODE_98("mark.result.sherrorcode_98", "菜单超过最大值"),
    MARK_RESULT_SHERRORCODE_101("mark.result.sherrorcode_101", "该手机号已被绑定"),
    MARK_RESULT_SHERRORCODE_102("mark.result.sherrorcode_102", "该用户未绑定纷享企业"),
    MARK_RESULT_SHERRORCODE_103("mark.result.sherrorcode_103", "绑定纷享企业失败"),
    MARK_RESULT_SHERRORCODE_104("mark.result.sherrorcode_104", "绑定其他纷享企业"),
    MARK_RESULT_SHERRORCODE_105("mark.result.sherrorcode_105", "绑定手机号不能为空"),
    MARK_RESULT_SHERRORCODE_106("mark.result.sherrorcode_106", "通过纷享注册手机号获取注册公司信息失败"),
    MARK_RESULT_SHERRORCODE_107("mark.result.sherrorcode_107", "查询公司名称失败"),
    MARK_RESULT_SHERRORCODE_108("mark.result.sherrorcode_108", "手机号未注册纷享帐号"),
    MARK_RESULT_SHERRORCODE_109("mark.result.sherrorcode_109", "解除绑定失败"),
    MARK_RESULT_SHERRORCODE_110("mark.result.sherrorcode_110", "请先登录营销通钉钉小程序"),
    MARK_RESULT_SHERRORCODE_113("mark.result.sherrorcode_113", "营销活动数据不存在"),
    MARK_RESULT_SHERRORCODE_114("mark.result.sherrorcode_114", "作废营销活动数据失败"),
    MARK_RESULT_SHERRORCODE_115("mark.result.sherrorcode_115", "营销活动数据类型错误"),
    MARK_RESULT_SHERRORCODE_116("mark.result.sherrorcode_116", "删除全员推广数据失败"),
    MARK_RESULT_SHERRORCODE_117("mark.result.sherrorcode_117", "删除短信推广数据失败"),
    MARK_RESULT_SHERRORCODE_118("mark.result.sherrorcode_118", "删除邮件推广数据失败"),
    MARK_RESULT_SHERRORCODE_119("mark.result.sherrorcode_119", "删除企业微信推广数据失败"),
    MARK_RESULT_SHERRORCODE_120("mark.result.sherrorcode_120", "无法删除进行中的推广任务"),
    MARK_RESULT_SHERRORCODE_121("mark.result.sherrorcode_121", "过滤后发送用户为空"),
    MARK_RESULT_SHERRORCODE_122("mark.result.sherrorcode_122", "会员营销的过滤范围不能为空"),
    MARK_RESULT_SHERRORCODE_125("mark.result.sherrorcode_125", "物料分组最大支持四级"),
    MARK_RESULT_SHERRORCODE_126("mark.result.sherrorcode_126", "分组不存在"),
    MARK_RESULT_SHERRORCODE_127("mark.result.sherrorcode_127", "当前分组不存在父级分组，不允许设置"),
    MARK_RESULT_SHERRORCODE_128("mark.result.sherrorcode_128", "分组名称已存在"),
    MARK_RESULT_SHERRORCODE_129("mark.result.sherrorcode_129", "存在下级分组，不允许删除"),
    MARK_RESULT_SHERRORCODE_130("mark.result.sherrorcode_130", "当前分组下存在物料，不允许删除"),
    MARK_RESULT_SHERRORCODE_136("mark.result.sherrorcode_136", "找不到该名片"),
    MARK_RESULT_SHERRORCODE_137("mark.result.sherrorcode_137", "名片创建失败"),
    MARK_RESULT_SHERRORCODE_138("mark.result.sherrorcode_138", "名片更新失败"),
    MARK_RESULT_SHERRORCODE_139("mark.result.sherrorcode_139", "名片专属码创建失败"),
    MARK_RESULT_SHERRORCODE_140("mark.result.sherrorcode_140", "未创建名片专属码"),
    MARK_RESULT_SHERRORCODE_141("mark.result.sherrorcode_141", "创建我看过的名片关系失败"),
    MARK_RESULT_SHERRORCODE_142("mark.result.sherrorcode_142", "更新我看过的名片关系失败"),
    MARK_RESULT_SHERRORCODE_143("mark.result.sherrorcode_143", "创建交换名片关系失败"),
    MARK_RESULT_SHERRORCODE_144("mark.result.sherrorcode_144", "更新交换名片关系失败"),
    MARK_RESULT_SHERRORCODE_145("mark.result.sherrorcode_145", "找不到自己的名片"),
    MARK_RESULT_SHERRORCODE_146("mark.result.sherrorcode_146", "找不到目标名片"),
    MARK_RESULT_SHERRORCODE_147("mark.result.sherrorcode_147", "不能交换自己的名片"),
    MARK_RESULT_SHERRORCODE_148("mark.result.sherrorcode_148", "客脉小秘书名片不存在"),
    MARK_RESULT_SHERRORCODE_149("mark.result.sherrorcode_149", "不能编辑非自己的名片"),
    MARK_RESULT_SHERRORCODE_150("mark.result.sherrorcode_150", "查询我看过的名片失败"),
    MARK_RESULT_SHERRORCODE_151("mark.result.sherrorcode_151", "查询交换名片失败"),
    MARK_RESULT_SHERRORCODE_152("mark.result.sherrorcode_152", "该交换名片已被同意过"),
    MARK_RESULT_SHERRORCODE_153("mark.result.sherrorcode_153", "该交换名片已被拒绝过"),
    MARK_RESULT_SHERRORCODE_154("mark.result.sherrorcode_154", "该交换名片等待对方处理中"),
    MARK_RESULT_SHERRORCODE_155("mark.result.sherrorcode_155", "查询名片列表失败"),
    MARK_RESULT_SHERRORCODE_156("mark.result.sherrorcode_156", "找不到邮箱隐私字段"),
    MARK_RESULT_SHERRORCODE_157("mark.result.sherrorcode_157", "找不到手机号隐私字段"),
    MARK_RESULT_SHERRORCODE_158("mark.result.sherrorcode_158", "找不到微信号隐私字段"),
    MARK_RESULT_SHERRORCODE_159("mark.result.sherrorcode_159", "非法的隐私枚举值"),
    MARK_RESULT_SHERRORCODE_160("mark.result.sherrorcode_160", "名片已存在"),
    MARK_RESULT_SHERRORCODE_161("mark.result.sherrorcode_161", "名片隐私字段更新失败"),
    MARK_RESULT_SHERRORCODE_162("mark.result.sherrorcode_162", "对方已邀请过交换名片,请直接同意"),
    MARK_RESULT_SHERRORCODE_163("mark.result.sherrorcode_163", "用户未开通小程序名片，开通后才能进行此操作"),
    MARK_RESULT_SHERRORCODE_164("mark.result.sherrorcode_164", "名片模板删除失败"),
    MARK_RESULT_SHERRORCODE_165("mark.result.sherrorcode_165", "名片模板设置有误"),
    MARK_RESULT_SHERRORCODE_166("mark.result.sherrorcode_166", "未找到名片模板"),
    MARK_RESULT_SHERRORCODE_168("mark.result.sherrorcode_168", "产品创建失败"),
    MARK_RESULT_SHERRORCODE_169("mark.result.sherrorcode_169", "产品更新失败"),
    MARK_RESULT_SHERRORCODE_170("mark.result.sherrorcode_170", "获取产品列表失败"),
    MARK_RESULT_SHERRORCODE_171("mark.result.sherrorcode_171", "获取产品详情失败"),
    MARK_RESULT_SHERRORCODE_172("mark.result.sherrorcode_172", "关联用户产品失败"),
    MARK_RESULT_SHERRORCODE_173("mark.result.sherrorcode_173", "解除关联用户产品失败"),
    MARK_RESULT_SHERRORCODE_174("mark.result.sherrorcode_174", "推荐产品失败"),
    MARK_RESULT_SHERRORCODE_175("mark.result.sherrorcode_175", "产品不可删除"),
    MARK_RESULT_SHERRORCODE_176("mark.result.sherrorcode_176", "该用户暂未关联任何产品"),
    MARK_RESULT_SHERRORCODE_177("mark.result.sherrorcode_177", "推荐产品超过一个"),
    MARK_RESULT_SHERRORCODE_178("mark.result.sherrorcode_178", "公司产品解除关联失败"),
    MARK_RESULT_SHERRORCODE_179("mark.result.sherrorcode_179", "产品ID为空"),
    MARK_RESULT_SHERRORCODE_180("mark.result.sherrorcode_180", "产品名称不能同名"),
    MARK_RESULT_SHERRORCODE_181("mark.result.sherrorcode_181", "产品不存在"),
    MARK_RESULT_SHERRORCODE_182("mark.result.sherrorcode_182", "部分产品不存在,请刷新"),
    MARK_RESULT_SHERRORCODE_183("mark.result.sherrorcode_183", "开启试用产品不可删除"),
    MARK_RESULT_SHERRORCODE_184("mark.result.sherrorcode_184", "置顶产品不允许上移"),
    MARK_RESULT_SHERRORCODE_185("mark.result.sherrorcode_185", "置顶产品不允许下移"),
    MARK_RESULT_SHERRORCODE_186("mark.result.sherrorcode_186", "启用的商品无法删除,请先停用"),
    MARK_RESULT_SHERRORCODE_189("mark.result.sherrorcode_189", "文件保存失败"),
    MARK_RESULT_SHERRORCODE_190("mark.result.sherrorcode_190", "文件预览连接获取失败"),
    MARK_RESULT_SHERRORCODE_191("mark.result.sherrorcode_191", "文件不存在"),
    MARK_RESULT_SHERRORCODE_192("mark.result.sherrorcode_192", "文件格式错误"),
    MARK_RESULT_SHERRORCODE_193("mark.result.sherrorcode_193", "图片上传失败"),
    MARK_RESULT_SHERRORCODE_194("mark.result.sherrorcode_194", "文件图片获取失败，图片为空或者不存在"),
    MARK_RESULT_SHERRORCODE_195("mark.result.sherrorcode_195", "不支持该企业下载文件图片"),
    MARK_RESULT_SHERRORCODE_196("mark.result.sherrorcode_196", "不能删除系统文件"),
    MARK_RESULT_SHERRORCODE_197("mark.result.sherrorcode_197", "不能重命名系统文件"),
    MARK_RESULT_SHERRORCODE_198("mark.result.sherrorcode_198", "图片数量超出限制或图片数量为0"),
    MARK_RESULT_SHERRORCODE_199("mark.result.sherrorcode_199", "单张图片大小不能超过5M"),
    MARK_RESULT_SHERRORCODE_200("mark.result.sherrorcode_200", "文件名称不能同名"),
    MARK_RESULT_SHERRORCODE_201("mark.result.sherrorcode_201", "存在同名文件，上传成功个数{1}, 失败个个数{2}"),
    MARK_RESULT_SHERRORCODE_202("mark.result.sherrorcode_202", "图片名称不能同名"),
    MARK_RESULT_SHERRORCODE_203("mark.result.sherrorcode_203", "部分文件不存在,请刷新"),
    MARK_RESULT_SHERRORCODE_206("mark.result.sherrorcode_206", "发送模板消息失败"),
    MARK_RESULT_SHERRORCODE_208("mark.result.sherrorcode_208", "用户产品创建关联失败"),
    MARK_RESULT_SHERRORCODE_209("mark.result.sherrorcode_209", "用户产品解除关联失败"),
    MARK_RESULT_SHERRORCODE_210("mark.result.sherrorcode_210", "用户产品选择关联失败"),
    MARK_RESULT_SHERRORCODE_212("mark.result.sherrorcode_212", "标签不存在"),
    MARK_RESULT_SHERRORCODE_213("mark.result.sherrorcode_213", "标签更新失败"),
    MARK_RESULT_SHERRORCODE_214("mark.result.sherrorcode_214", "删除标签失败"),
    MARK_RESULT_SHERRORCODE_215("mark.result.sherrorcode_215", "标签组不存在"),
    MARK_RESULT_SHERRORCODE_216("mark.result.sherrorcode_216", "展示顺序已被更改，需要重新尝试"),
    MARK_RESULT_SHERRORCODE_218("mark.result.sherrorcode_218", "不支持的物料类型"),
    MARK_RESULT_SHERRORCODE_222("mark.result.sherrorcode_222", "绑定群用户失败"),
    MARK_RESULT_SHERRORCODE_223("mark.result.sherrorcode_223", "添加群卡片失败"),
    MARK_RESULT_SHERRORCODE_224("mark.result.sherrorcode_224", "添加群失败"),
    MARK_RESULT_SHERRORCODE_225("mark.result.sherrorcode_225", "查询群失败"),
    MARK_RESULT_SHERRORCODE_227("mark.result.sherrorcode_227", "埋点对象类型错误"),
    MARK_RESULT_SHERRORCODE_229("mark.result.sherrorcode_229", "员工信息不存在"),
    MARK_RESULT_SHERRORCODE_230("mark.result.sherrorcode_230", "员工企业信息不存在"),
    MARK_RESULT_SHERRORCODE_231("mark.result.sherrorcode_231", "员工部门信息不存在"),
    MARK_RESULT_SHERRORCODE_232("mark.result.sherrorcode_232", "手机号未绑定"),
    MARK_RESULT_SHERRORCODE_233("mark.result.sherrorcode_233", "未绑定企业"),
    MARK_RESULT_SHERRORCODE_234("mark.result.sherrorcode_234", "员工手机号信息不存在"),
    MARK_RESULT_SHERRORCODE_235("mark.result.sherrorcode_235", "纷享cookie登录失败"),
    MARK_RESULT_SHERRORCODE_236("mark.result.sherrorcode_236", "查询员工信息失败"),
    MARK_RESULT_SHERRORCODE_238("mark.result.sherrorcode_238", "新建失败"),
    MARK_RESULT_SHERRORCODE_239("mark.result.sherrorcode_239", "修改失败"),
    MARK_RESULT_SHERRORCODE_240("mark.result.sherrorcode_240", "删除失败"),
    MARK_RESULT_SHERRORCODE_241("mark.result.sherrorcode_241", "已启用的文章不能删除，请先停用!"),
    MARK_RESULT_SHERRORCODE_242("mark.result.sherrorcode_242", "该文章不存在"),
    MARK_RESULT_SHERRORCODE_243("mark.result.sherrorcode_243", "文章已存在"),
    MARK_RESULT_SHERRORCODE_244("mark.result.sherrorcode_244", "新建动态分享图片失败"),
    MARK_RESULT_SHERRORCODE_245("mark.result.sherrorcode_245", "动态不存在"),
    MARK_RESULT_SHERRORCODE_246("mark.result.sherrorcode_246", "网络异常，请重试!"),
    MARK_RESULT_SHERRORCODE_247("mark.result.sherrorcode_247", "文章没有存动态"),
    MARK_RESULT_SHERRORCODE_248("mark.result.sherrorcode_248", "文章不可删除"),
    MARK_RESULT_SHERRORCODE_249("mark.result.sherrorcode_249", "通过feedId查询redis获取uid失败"),
    MARK_RESULT_SHERRORCODE_250("mark.result.sherrorcode_250", "html不存在"),
    MARK_RESULT_SHERRORCODE_251("mark.result.sherrorcode_251", "文章已被删除"),
    MARK_RESULT_SHERRORCODE_252("mark.result.sherrorcode_252", "获取文章详情失败"),
    MARK_RESULT_SHERRORCODE_253("mark.result.sherrorcode_253", "修改封面失败"),
    MARK_RESULT_SHERRORCODE_254("mark.result.sherrorcode_254", "添加封面失败"),
    MARK_RESULT_SHERRORCODE_255("mark.result.sherrorcode_255", "预览文章专属码创建失败"),
    MARK_RESULT_SHERRORCODE_256("mark.result.sherrorcode_256", "缺少文章封面"),
    MARK_RESULT_SHERRORCODE_257("mark.result.sherrorcode_257", "查询文章列表失败"),
    MARK_RESULT_SHERRORCODE_258("mark.result.sherrorcode_258", "文章名称不能同名"),
    MARK_RESULT_SHERRORCODE_259("mark.result.sherrorcode_259", "部分文章不存在,请刷新"),
    MARK_RESULT_SHERRORCODE_262("mark.result.sherrorcode_262", "cookie 不存在"),
    MARK_RESULT_SHERRORCODE_264("mark.result.sherrorcode_264", "姓名不能为空且不能超过20个字"),
    MARK_RESULT_SHERRORCODE_265("mark.result.sherrorcode_265", "手机不能为空且为11位数字"),
    MARK_RESULT_SHERRORCODE_266("mark.result.sherrorcode_266", "头像不能为空"),
    MARK_RESULT_SHERRORCODE_267("mark.result.sherrorcode_267", "公司名字不能为空且不能超过50个字"),
    MARK_RESULT_SHERRORCODE_268("mark.result.sherrorcode_268", "公司地址不能超过100个字"),
    MARK_RESULT_SHERRORCODE_269("mark.result.sherrorcode_269", "个人介绍不能超过100个字"),
    MARK_RESULT_SHERRORCODE_270("mark.result.sherrorcode_270", "职位不能超过20个字"),
    MARK_RESULT_SHERRORCODE_271("mark.result.sherrorcode_271", "部门不能超过20个字"),
    MARK_RESULT_SHERRORCODE_272("mark.result.sherrorcode_272", "微信号不能超过20个字"),
    MARK_RESULT_SHERRORCODE_273("mark.result.sherrorcode_273", "邮箱不能超过50个字"),
    MARK_RESULT_SHERRORCODE_274("mark.result.sherrorcode_274", "照片不能超过20张"),
    MARK_RESULT_SHERRORCODE_275("mark.result.sherrorcode_275", "产品介绍不能为空且不能超过140个字"),
    MARK_RESULT_SHERRORCODE_276("mark.result.sherrorcode_276", "产品封面不能为空且不超过5张"),
    MARK_RESULT_SHERRORCODE_277("mark.result.sherrorcode_277", "产品详情不能为空且不超过9张"),
    MARK_RESULT_SHERRORCODE_278("mark.result.sherrorcode_278", "产品名字不能为空且不能超过50个字"),
    MARK_RESULT_SHERRORCODE_283("mark.result.sherrorcode_283", "qq号不能超过20个字"),
    MARK_RESULT_SHERRORCODE_287("mark.result.sherrorcode_287", "客户名字不能为空且不能超过20个字"),
    MARK_RESULT_SHERRORCODE_288("mark.result.sherrorcode_288", "标签不能超过10个字"),
    MARK_RESULT_SHERRORCODE_289("mark.result.sherrorcode_289", "互动不能超过1024个字"),
    MARK_RESULT_SHERRORCODE_290("mark.result.sherrorcode_290", "备注不能超过20个字"),
    MARK_RESULT_SHERRORCODE_291("mark.result.sherrorcode_291", "文章摘要长度超过限制"),
    MARK_RESULT_SHERRORCODE_292("mark.result.sherrorcode_292", "推荐语长度超过限制"),
    MARK_RESULT_SHERRORCODE_294("mark.result.sherrorcode_294", "该客脉已存在"),
    MARK_RESULT_SHERRORCODE_295("mark.result.sherrorcode_295", "客脉创建失败"),
    MARK_RESULT_SHERRORCODE_296("mark.result.sherrorcode_296", "该客脉不存在"),
    MARK_RESULT_SHERRORCODE_297("mark.result.sherrorcode_297", "客脉更新失败"),
    MARK_RESULT_SHERRORCODE_298("mark.result.sherrorcode_298", "客脉暂不允许降级"),
    MARK_RESULT_SHERRORCODE_299("mark.result.sherrorcode_299", "客脉添加跟进失败"),
    MARK_RESULT_SHERRORCODE_300("mark.result.sherrorcode_300", "客脉暂不允许跨越升级"),
    MARK_RESULT_SHERRORCODE_302("mark.result.sherrorcode_302", "找不到feed"),
    MARK_RESULT_SHERRORCODE_303("mark.result.sherrorcode_303", "已经感谢过该文章了"),
    MARK_RESULT_SHERRORCODE_305("mark.result.sherrorcode_305", "该账号未绑定纷享企业, 暂不在客脉红包灰度范围内"),
    MARK_RESULT_SHERRORCODE_306("mark.result.sherrorcode_306", "该企业暂不在客脉红包灰度范围内"),
    MARK_RESULT_SHERRORCODE_308("mark.result.sherrorcode_308", "推广通知不存在"),
    MARK_RESULT_SHERRORCODE_309("mark.result.sherrorcode_309", "推广通知关联对象不存在"),
    MARK_RESULT_SHERRORCODE_311("mark.result.sherrorcode_311", "CRM业务异常"),
    MARK_RESULT_SHERRORCODE_312("mark.result.sherrorcode_312", "字段映射校验错误"),
    MARK_RESULT_SHERRORCODE_313("mark.result.sherrorcode_313", "CRM必填字段没有正确映射"),
    MARK_RESULT_SHERRORCODE_314("mark.result.sherrorcode_314", "客脉字段不能为空"),
    MARK_RESULT_SHERRORCODE_315("mark.result.sherrorcode_315", "暂未开启客户保存到CRM，请联系客脉通管理员"),
    MARK_RESULT_SHERRORCODE_316("mark.result.sherrorcode_316", "该客户在CRM中已存在"),
    MARK_RESULT_SHERRORCODE_317("mark.result.sherrorcode_317", "不是人脉"),
    MARK_RESULT_SHERRORCODE_318("mark.result.sherrorcode_318", "不是客户"),
    MARK_RESULT_SHERRORCODE_319("mark.result.sherrorcode_319", "该线索在CRM中已存在"),
    MARK_RESULT_SHERRORCODE_320("mark.result.sherrorcode_320", "客户已保存，无需重复保存"),
    MARK_RESULT_SHERRORCODE_321("mark.result.sherrorcode_321", "人脉已保存，无需重复保存"),
    MARK_RESULT_SHERRORCODE_322("mark.result.sherrorcode_322", "暂未开启人脉保存到CRM，请联系客脉通管理员"),
    MARK_RESULT_SHERRORCODE_324("mark.result.sherrorcode_324", "感谢您的访问，本次会议已停用"),
    MARK_RESULT_SHERRORCODE_325("mark.result.sherrorcode_325", "感谢您的访问，本次已截止报名"),
    MARK_RESULT_SHERRORCODE_326("mark.result.sherrorcode_326", "获取活动详情失败"),
    MARK_RESULT_SHERRORCODE_327("mark.result.sherrorcode_327", "没有设置签到状态"),
    MARK_RESULT_SHERRORCODE_328("mark.result.sherrorcode_328", "会议不存在"),
    MARK_RESULT_SHERRORCODE_329("mark.result.sherrorcode_329", "感谢您的访问，本次活动已满员"),
    MARK_RESULT_SHERRORCODE_330("mark.result.sherrorcode_330", "报名数据正在审核或未审核通过"),
    MARK_RESULT_SHERRORCODE_331("mark.result.sherrorcode_331", "感谢您的访问，本次活动已结束"),
    MARK_RESULT_SHERRORCODE_332("mark.result.sherrorcode_332", "会议尚未开始，无法签到"),
    MARK_RESULT_SHERRORCODE_333("mark.result.sherrorcode_333", "感谢您的访问，本次会议已删除"),
    MARK_RESULT_SHERRORCODE_334("mark.result.sherrorcode_334", "报名数据存在审核失败"),
    MARK_RESULT_SHERRORCODE_335("mark.result.sherrorcode_335", "六度人脉数据不存在"),
    MARK_RESULT_SHERRORCODE_336("mark.result.sherrorcode_336", "六度人脉数据更新阅读量失败"),
    MARK_RESULT_SHERRORCODE_337("mark.result.sherrorcode_337", "六度人脉数据更新转发量量失败"),
    MARK_RESULT_SHERRORCODE_340("mark.result.sherrorcode_340", "纷享用户登录状态错误"),
    MARK_RESULT_SHERRORCODE_341("mark.result.sherrorcode_341", "该用户不是营销通应用管理员"),
    MARK_RESULT_SHERRORCODE_342("mark.result.sherrorcode_342", "该账号未绑定纷享企业"),
    MARK_RESULT_SHERRORCODE_343("mark.result.sherrorcode_343", "该用户已经报名此活动"),
    MARK_RESULT_SHERRORCODE_344("mark.result.sherrorcode_344", "该用户不是社会化分销运营人员"),
    MARK_RESULT_SHERRORCODE_345("mark.result.sherrorcode_345", "该用户无权限"),
    MARK_RESULT_SHERRORCODE_346("mark.result.sherrorcode_346", "该用户未参加此活动"),
    MARK_RESULT_SHERRORCODE_347("mark.result.sherrorcode_347", "模板不存在"),
    MARK_RESULT_SHERRORCODE_348("mark.result.sherrorcode_348", "查询对象详情不存在"),
    MARK_RESULT_SHERRORCODE_349("mark.result.sherrorcode_349", "api	Name值为空"),
    MARK_RESULT_SHERRORCODE_350("mark.result.sherrorcode_350", "查询数据超过限制"),
    MARK_RESULT_SHERRORCODE_351("mark.result.sherrorcode_351", "仅管理员有权限操作"),
    MARK_RESULT_SHERRORCODE_352("mark.result.sherrorcode_352", "分页查询CRM线索列表出现异常"),
    MARK_RESULT_SHERRORCODE_353("mark.result.sherrorcode_353", "线索阶段字段描述不存在"),
    MARK_RESULT_SHERRORCODE_354("mark.result.sherrorcode_354", "线索阶段字段的选项不存在"),
    MARK_RESULT_SHERRORCODE_355("mark.result.sherrorcode_355", "线索描述不存在"),
    MARK_RESULT_SHERRORCODE_356("mark.result.sherrorcode_356", "线索状态字段描述不存在"),
    MARK_RESULT_SHERRORCODE_358("mark.result.sherrorcode_358", "伙伴营销人员角色不存在"),
    MARK_RESULT_SHERRORCODE_360("mark.result.sherrorcode_360", "运营人员不存在"),
    MARK_RESULT_SHERRORCODE_361("mark.result.sherrorcode_361", "移除运营人员前，请先重新分配该运营人员管理的分销人员"),
    MARK_RESULT_SHERRORCODE_362("mark.result.sherrorcode_362", "运营人员添加到营销通应用web组建失败"),
    MARK_RESULT_SHERRORCODE_364("mark.result.sherrorcode_364", "线索池不存在"),
    MARK_RESULT_SHERRORCODE_365("mark.result.sherrorcode_365", "营销计划不存在"),
    MARK_RESULT_SHERRORCODE_366("mark.result.sherrorcode_366", "图片个数超过9个"),
    MARK_RESULT_SHERRORCODE_367("mark.result.sherrorcode_367", "未找到分销计划"),
    MARK_RESULT_SHERRORCODE_368("mark.result.sherrorcode_368", "保存crm线索失败"),
    MARK_RESULT_SHERRORCODE_369("mark.result.sherrorcode_369", "crm映射不存在"),
    MARK_RESULT_SHERRORCODE_370("mark.result.sherrorcode_370", "当前企业不在社会化分销灰度范围内"),
    MARK_RESULT_SHERRORCODE_371("mark.result.sherrorcode_371", "获取营销通应用管理员失败"),
    MARK_RESULT_SHERRORCODE_372("mark.result.sherrorcode_372", "获取营销通应用web可见列表失败"),
    MARK_RESULT_SHERRORCODE_373("mark.result.sherrorcode_373", "添加运营人员失败"),
    MARK_RESULT_SHERRORCODE_374("mark.result.sherrorcode_374", "分销线索表中找不到该线索"),
    MARK_RESULT_SHERRORCODE_375("mark.result.sherrorcode_375", "发放奖金总额超过了奖励总金额"),
    MARK_RESULT_SHERRORCODE_376("mark.result.sherrorcode_376", "只能删除级数最大的分销等级"),
    MARK_RESULT_SHERRORCODE_377("mark.result.sherrorcode_377", "期待建立的分销等级有误"),
    MARK_RESULT_SHERRORCODE_378("mark.result.sherrorcode_378", "设置的累计订单总金额必须大于上一等级的数值"),
    MARK_RESULT_SHERRORCODE_379("mark.result.sherrorcode_379", "第一个分销等级的条件必须为0"),
    MARK_RESULT_SHERRORCODE_380("mark.result.sherrorcode_380", "分销等级金额应大于上一级金额"),
    MARK_RESULT_SHERRORCODE_381("mark.result.sherrorcode_381", "分销等级条件应小于下一级条件"),
    MARK_RESULT_SHERRORCODE_382("mark.result.sherrorcode_382", "查询线索详情失败"),
    MARK_RESULT_SHERRORCODE_383("mark.result.sherrorcode_383", "查询分销素材失败"),
    MARK_RESULT_SHERRORCODE_384("mark.result.sherrorcode_384", "查询运营人员失败"),
    MARK_RESULT_SHERRORCODE_385("mark.result.sherrorcode_385", "查询分销计划列表失败"),
    MARK_RESULT_SHERRORCODE_386("mark.result.sherrorcode_386", "查询分销人员详情失败"),
    MARK_RESULT_SHERRORCODE_387("mark.result.sherrorcode_387", "查询线索列表失败"),
    MARK_RESULT_SHERRORCODE_388("mark.result.sherrorcode_388", "分销人员不存在"),
    MARK_RESULT_SHERRORCODE_389("mark.result.sherrorcode_389", "存在该分销等级的分销员，无法删除"),
    MARK_RESULT_SHERRORCODE_390("mark.result.sherrorcode_390", "分销员不存在待审批的申请"),
    MARK_RESULT_SHERRORCODE_391("mark.result.sherrorcode_391", "设置失败，线索需要是有效状态或者赢单状态"),
    MARK_RESULT_SHERRORCODE_392("mark.result.sherrorcode_392", "高等级的升级类型需要跟低等级保持一致"),
    MARK_RESULT_SHERRORCODE_393("mark.result.sherrorcode_393", "设置的累计提交有效线索个数必须大于上一等级的数值"),
    MARK_RESULT_SHERRORCODE_394("mark.result.sherrorcode_394", "设置的累计招募分销员人数必须大于上一等级的数值"),
    MARK_RESULT_SHERRORCODE_395("mark.result.sherrorcode_395", "设置的线索成单个数必须不小于上一等级的数值"),
    MARK_RESULT_SHERRORCODE_396("mark.result.sherrorcode_396", "短信签名申请失败，请重新提交"),
    MARK_RESULT_SHERRORCODE_397("mark.result.sherrorcode_397", "短信签名数量已经达到上限，暂不能提交短信签名申请"),
    MARK_RESULT_SHERRORCODE_399("mark.result.sherrorcode_399", "短信签名不存在，请先设置短信签名"),
    MARK_RESULT_SHERRORCODE_400("mark.result.sherrorcode_400", "删除短信签名失败，请重试"),
    MARK_RESULT_SHERRORCODE_401("mark.result.sherrorcode_401", "贵司还没有短信签名，申请短信模板前请先申请短信签名"),
    MARK_RESULT_SHERRORCODE_402("mark.result.sherrorcode_402", "申请短信模板失败，请修改后重新提交"),
    MARK_RESULT_SHERRORCODE_403("mark.result.sherrorcode_403", "短信模板数量已经达到上限，请删除部分短信模板后重新提交"),
    MARK_RESULT_SHERRORCODE_404("mark.result.sherrorcode_404", "短信模板不存在"),
    MARK_RESULT_SHERRORCODE_405("mark.result.sherrorcode_405", "删除短信模板失败，请重试"),
    MARK_RESULT_SHERRORCODE_406("mark.result.sherrorcode_406", "移动短信模板失败，请重试"),
    MARK_RESULT_SHERRORCODE_407("mark.result.sherrorcode_407", "修改短信签名失败，请重试"),
    MARK_RESULT_SHERRORCODE_408("mark.result.sherrorcode_408", "修改短信签名超过次数限制"),
    MARK_RESULT_SHERRORCODE_409("mark.result.sherrorcode_409", "发送电话格式填写错误，请核实后重新提交"),
    MARK_RESULT_SHERRORCODE_410("mark.result.sherrorcode_410", "Excel文件中电话名单数量为0，请核实后重新提交"),
    MARK_RESULT_SHERRORCODE_411("mark.result.sherrorcode_411", "Excel文件内容格式错误，请核实后重新提交"),
    MARK_RESULT_SHERRORCODE_412("mark.result.sherrorcode_412", "短信模板的变量个数与Excel的变量个数不相等，请核实后重新提交"),
    MARK_RESULT_SHERRORCODE_413("mark.result.sherrorcode_413", "只能删除发送失败的群发短信"),
    MARK_RESULT_SHERRORCODE_414("mark.result.sherrorcode_414", "剩余短信配额不足，请购买短信包"),
    MARK_RESULT_SHERRORCODE_415("mark.result.sherrorcode_415", "签名长度不符合要求，请修改短信签名后重试"),
    MARK_RESULT_SHERRORCODE_416("mark.result.sherrorcode_416", "发送短信长度过长"),
    MARK_RESULT_SHERRORCODE_417("mark.result.sherrorcode_417", "短信状态不是草稿状态"),
    MARK_RESULT_SHERRORCODE_418("mark.result.sherrorcode_418", "签名状态不是审核通过状态"),
    MARK_RESULT_SHERRORCODE_419("mark.result.sherrorcode_419", "请检查是否选择了短信接收人"),
    MARK_RESULT_SHERRORCODE_420("mark.result.sherrorcode_420", "定时发送短信需要设置发送时间"),
    MARK_RESULT_SHERRORCODE_421("mark.result.sherrorcode_421", "该短信不存在"),
    MARK_RESULT_SHERRORCODE_422("mark.result.sherrorcode_422", "编辑短信修改短信模板失败，请检查模板后重试"),
    MARK_RESULT_SHERRORCODE_423("mark.result.sherrorcode_423", "短信状态错误"),
    MARK_RESULT_SHERRORCODE_424("mark.result.sherrorcode_424", "短信明细创建失败"),
    MARK_RESULT_SHERRORCODE_425("mark.result.sherrorcode_425", "购买的短信套餐不存在"),
    MARK_RESULT_SHERRORCODE_426("mark.result.sherrorcode_426", "选择人群创建短信失败"),
    MARK_RESULT_SHERRORCODE_427("mark.result.sherrorcode_427", "Excel文件下载失败"),
    MARK_RESULT_SHERRORCODE_428("mark.result.sherrorcode_428", "模板中参数赋值失败"),
    MARK_RESULT_SHERRORCODE_429("mark.result.sherrorcode_429", "短信字数已超出1000个字"),
    MARK_RESULT_SHERRORCODE_430("mark.result.sherrorcode_430", "短信明细创建产生异常"),
    MARK_RESULT_SHERRORCODE_431("mark.result.sherrorcode_431", "目标人群没有有效手机号用户"),
    MARK_RESULT_SHERRORCODE_432("mark.result.sherrorcode_432", "获取目标人群用户异常"),
    MARK_RESULT_SHERRORCODE_433("mark.result.sherrorcode_433", "短信模板标题或模板内容为空"),
    MARK_RESULT_SHERRORCODE_434("mark.result.sherrorcode_434", "手机号码不符合要求"),
    MARK_RESULT_SHERRORCODE_435("mark.result.sherrorcode_435", "请先停用该人群"),
    MARK_RESULT_SHERRORCODE_436("mark.result.sherrorcode_436", "使用中的人群不允许删除"),
    MARK_RESULT_SHERRORCODE_437("mark.result.sherrorcode_437", "短信试用申请已存在，无须再次申请"),
    MARK_RESULT_SHERRORCODE_438("mark.result.sherrorcode_438", "短信签名已存在，不能申请试用"),
    MARK_RESULT_SHERRORCODE_439("mark.result.sherrorcode_439", "人群名称重复"),
    MARK_RESULT_SHERRORCODE_440("mark.result.sherrorcode_440", "自动计算的动态人群达到上限"),
    MARK_RESULT_SHERRORCODE_441("mark.result.sherrorcode_441", "存在往人群中添加用户的操作未计算完成，请等待计算完成后再添加"),
    MARK_RESULT_SHERRORCODE_442("mark.result.sherrorcode_442", "支持自定义对象数量超过限额"),
    MARK_RESULT_SHERRORCODE_443("mark.result.sherrorcode_443", "数据范围不能为空"),
    MARK_RESULT_SHERRORCODE_444("mark.result.sherrorcode_444", "数据范围参数有误"),
    MARK_RESULT_SHERRORCODE_446("mark.result.sherrorcode_446", "修改模板失败"),
    MARK_RESULT_SHERRORCODE_447("mark.result.sherrorcode_447", "上传模板审核失败"),
    MARK_RESULT_SHERRORCODE_448("mark.result.sherrorcode_448", "查询模板审核状态失败"),
    MARK_RESULT_SHERRORCODE_449("mark.result.sherrorcode_449", "管理模板失败"),
    MARK_RESULT_SHERRORCODE_450("mark.result.sherrorcode_450", "仅支持审核通过的短信模板"),
    MARK_RESULT_SHERRORCODE_451("mark.result.sherrorcode_451", "模板格式不正确"),
    MARK_RESULT_SHERRORCODE_453("mark.result.sherrorcode_453", "目标人群不存在"),
    MARK_RESULT_SHERRORCODE_454("mark.result.sherrorcode_454", "目标人群不存在营销用户"),
    MARK_RESULT_SHERRORCODE_456("mark.result.sherrorcode_456", "标签名不允许重复"),
    MARK_RESULT_SHERRORCODE_457("mark.result.sherrorcode_457", "标签分组名称不允许重复"),
    MARK_RESULT_SHERRORCODE_458("mark.result.sherrorcode_458", "最后一个标签组不允许删除"),
    MARK_RESULT_SHERRORCODE_459("mark.result.sherrorcode_459", "不能使用该标签分组名称，请重新输入"),
    MARK_RESULT_SHERRORCODE_460("mark.result.sherrorcode_460", "不能删除该标签，已有用户使用该标签属性。"),
    MARK_RESULT_SHERRORCODE_461("mark.result.sherrorcode_461", "不能删除该标签分组，该分组下已有标签被使用。"),
    MARK_RESULT_SHERRORCODE_462("mark.result.sherrorcode_462", "设置公司信息失败"),
    MARK_RESULT_SHERRORCODE_463("mark.result.sherrorcode_463", "查询公司信息失败"),
    MARK_RESULT_SHERRORCODE_464("mark.result.sherrorcode_464", "添加失败，服务号数量已达到当前版本上限"),
    MARK_RESULT_SHERRORCODE_465("mark.result.sherrorcode_465", "该服务号已添加，无需重复操作"),
    MARK_RESULT_SHERRORCODE_466("mark.result.sherrorcode_466", "服务号不存在，请联系公众号管理员检查服务号授权情况"),
    MARK_RESULT_SHERRORCODE_467("mark.result.sherrorcode_467", "查询推广任务失败"),
    MARK_RESULT_SHERRORCODE_468("mark.result.sherrorcode_468", "更新推广任务状态失败"),
    MARK_RESULT_SHERRORCODE_469("mark.result.sherrorcode_469", "营销活动不存在"),
    MARK_RESULT_SHERRORCODE_470("mark.result.sherrorcode_470", "消除营销红点提示失败"),
    MARK_RESULT_SHERRORCODE_471("mark.result.sherrorcode_471", "查询营销红点失败"),
    MARK_RESULT_SHERRORCODE_472("mark.result.sherrorcode_472", "查询推广通知失败"),
    MARK_RESULT_SHERRORCODE_473("mark.result.sherrorcode_473", "查询推广任务状态失败"),
    MARK_RESULT_SHERRORCODE_474("mark.result.sherrorcode_474", "营销活动创建失败"),
    MARK_RESULT_SHERRORCODE_476("mark.result.sherrorcode_476", "营销活动更新失败"),
    MARK_RESULT_SHERRORCODE_477("mark.result.sherrorcode_477", "任务执行中或已完成,不允许更新操作"),
    MARK_RESULT_SHERRORCODE_479("mark.result.sherrorcode_479", "自定义表单不存在"),
    MARK_RESULT_SHERRORCODE_480("mark.result.sherrorcode_480", "自定义表单已停用"),
    MARK_RESULT_SHERRORCODE_481("mark.result.sherrorcode_481", "自定义表单已删除"),
    MARK_RESULT_SHERRORCODE_482("mark.result.sherrorcode_482", "信息已提交，请勿重复提交"),
    MARK_RESULT_SHERRORCODE_483("mark.result.sherrorcode_483", "删除表单前需停用"),
    MARK_RESULT_SHERRORCODE_484("mark.result.sherrorcode_484", "对应物料不存在"),
    MARK_RESULT_SHERRORCODE_485("mark.result.sherrorcode_485", "报名数据不存在"),
    MARK_RESULT_SHERRORCODE_486("mark.result.sherrorcode_486", "自定义表单报名数据已满"),
    MARK_RESULT_SHERRORCODE_487("mark.result.sherrorcode_487", "该自定义表单无手机号设置项，不能绑定物料"),
    MARK_RESULT_SHERRORCODE_488("mark.result.sherrorcode_488", "自定义表单映射错误"),
    MARK_RESULT_SHERRORCODE_489("mark.result.sherrorcode_489", "绑定数据有误，请检查后重试"),
    MARK_RESULT_SHERRORCODE_490("mark.result.sherrorcode_490", "物料未绑定表单"),
    MARK_RESULT_SHERRORCODE_491("mark.result.sherrorcode_491", "请输入验证码"),
    MARK_RESULT_SHERRORCODE_492("mark.result.sherrorcode_492", "自定义表单名称不能同名"),
    MARK_RESULT_SHERRORCODE_493("mark.result.sherrorcode_493", "部分自定义表单不存在,请书信"),
    MARK_RESULT_SHERRORCODE_496("mark.result.sherrorcode_496", "找不到该企业素材"),
    MARK_RESULT_SHERRORCODE_498("mark.result.sherrorcode_498", "该企业没有设置公众号"),
    MARK_RESULT_SHERRORCODE_499("mark.result.sherrorcode_499", "未查询到公众号数据"),
    MARK_RESULT_SHERRORCODE_500("mark.result.sherrorcode_500", "解除绑定营销通微信公众号失败"),
    MARK_RESULT_SHERRORCODE_501("mark.result.sherrorcode_501", "公众号永久素材库已关闭，查找失败"),
    MARK_RESULT_SHERRORCODE_502("mark.result.sherrorcode_502", "code错误或失效"),
    MARK_RESULT_SHERRORCODE_503("mark.result.sherrorcode_503", "获取平台id失败"),
    MARK_RESULT_SHERRORCODE_504("mark.result.sherrorcode_504", "未查询到该公众号绑定的企业ea"),
    MARK_RESULT_SHERRORCODE_505("mark.result.sherrorcode_505", "查询微信用户对象失败"),
    MARK_RESULT_SHERRORCODE_506("mark.result.sherrorcode_506", "公众号登录二维码不存在或已过期"),
    MARK_RESULT_SHERRORCODE_508("mark.result.sherrorcode_508", "待扫码"),
    MARK_RESULT_SHERRORCODE_509("mark.result.sherrorcode_509", "未绑定手机号前往绑定"),
    MARK_RESULT_SHERRORCODE_510("mark.result.sherrorcode_510", "公众号登录二维码设置不存在"),
    MARK_RESULT_SHERRORCODE_512("mark.result.sherrorcode_512", "会议创建失败，未查询到市场活动数据"),
    MARK_RESULT_SHERRORCODE_513("mark.result.sherrorcode_513", "会议创建失败"),
    MARK_RESULT_SHERRORCODE_514("mark.result.sherrorcode_514", "会议详情更新失败，未查询到会议数据"),
    MARK_RESULT_SHERRORCODE_515("mark.result.sherrorcode_515", "会议详情更新失败，封面参数错误"),
    MARK_RESULT_SHERRORCODE_516("mark.result.sherrorcode_516", "未查找到相关会议数据"),
    MARK_RESULT_SHERRORCODE_517("mark.result.sherrorcode_517", "会议报名已满员"),
    MARK_RESULT_SHERRORCODE_518("mark.result.sherrorcode_518", "查询会议列表失败"),
    MARK_RESULT_SHERRORCODE_519("mark.result.sherrorcode_519", "查询参会人信息失败"),
    MARK_RESULT_SHERRORCODE_520("mark.result.sherrorcode_520", "未查找到会议邀请函数据"),
    MARK_RESULT_SHERRORCODE_521("mark.result.sherrorcode_521", "更改参会人员签到状态失败"),
    MARK_RESULT_SHERRORCODE_522("mark.result.sherrorcode_522", "更改参会人员审核状态失败"),
    MARK_RESULT_SHERRORCODE_523("mark.result.sherrorcode_523", "保存参会人员到CRM失败"),
    MARK_RESULT_SHERRORCODE_524("mark.result.sherrorcode_524", "会议二维码创建失败"),
    MARK_RESULT_SHERRORCODE_525("mark.result.sherrorcode_525", "会议详情更新失败，封面更新失败"),
    MARK_RESULT_SHERRORCODE_526("mark.result.sherrorcode_526", "用户已签到"),
    MARK_RESULT_SHERRORCODE_527("mark.result.sherrorcode_527", "用户在报名后自动签到"),
    MARK_RESULT_SHERRORCODE_528("mark.result.sherrorcode_528", "启用状态下才能停用"),
    MARK_RESULT_SHERRORCODE_529("mark.result.sherrorcode_529", "停用状态下才能启用"),
    MARK_RESULT_SHERRORCODE_530("mark.result.sherrorcode_530", "无法删除，存在关联数据"),
    MARK_RESULT_SHERRORCODE_531("mark.result.sherrorcode_531", "邀请参会客户失败"),
    MARK_RESULT_SHERRORCODE_532("mark.result.sherrorcode_532", "导入失败，请使用标准的导入模板重新导入"),
    MARK_RESULT_SHERRORCODE_533("mark.result.sherrorcode_533", "查询报名审核信息失败"),
    MARK_RESULT_SHERRORCODE_534("mark.result.sherrorcode_534", "存在相同尾号参会人员，请通过其他方式进行验票"),
    MARK_RESULT_SHERRORCODE_535("mark.result.sherrorcode_535", "修改邀约人员状态失败"),
    MARK_RESULT_SHERRORCODE_536("mark.result.sherrorcode_536", "删除邀约人员失败"),
    MARK_RESULT_SHERRORCODE_537("mark.result.sherrorcode_537", "更新邀约人员信息失败"),
    MARK_RESULT_SHERRORCODE_538("mark.result.sherrorcode_538", "暂无市场活动查看权限"),
    MARK_RESULT_SHERRORCODE_539("mark.result.sherrorcode_539", "参会人员已经保存为线索"),
    MARK_RESULT_SHERRORCODE_540("mark.result.sherrorcode_540", "删除市场活动数据失败"),
    MARK_RESULT_SHERRORCODE_541("mark.result.sherrorcode_541", "参会人员不存在"),
    MARK_RESULT_SHERRORCODE_542("mark.result.sherrorcode_542", "未包含待审核参会数据"),
    MARK_RESULT_SHERRORCODE_545("mark.result.sherrorcode_545", "找不到该二维码信息"),
    MARK_RESULT_SHERRORCODE_546("mark.result.sherrorcode_546", "二维码验证失败"),
    MARK_RESULT_SHERRORCODE_547("mark.result.sherrorcode_547", "缺少二维码验证码"),
    MARK_RESULT_SHERRORCODE_548("mark.result.sherrorcode_548", "找不到该二维码海报"),
    MARK_RESULT_SHERRORCODE_549("mark.result.sherrorcode_549", "邀约海报不能重复创建"),
    MARK_RESULT_SHERRORCODE_550("mark.result.sherrorcode_550", "查询不到企业logo"),
    MARK_RESULT_SHERRORCODE_551("mark.result.sherrorcode_551", "二维码创建失败"),
    MARK_RESULT_SHERRORCODE_552("mark.result.sherrorcode_552", "查找不到该二维码"),
    MARK_RESULT_SHERRORCODE_553("mark.result.sherrorcode_553", "下载海报失败"),
    MARK_RESULT_SHERRORCODE_554("mark.result.sherrorcode_554", "上传海报失败"),
    MARK_RESULT_SHERRORCODE_555("mark.result.sherrorcode_555", "部分海报找不到,请刷新"),
    MARK_RESULT_SHERRORCODE_556("mark.result.sherrorcode_556", "外部系统二维码参数缺失"),
    MARK_RESULT_SHERRORCODE_557("mark.result.sherrorcode_557", "用户名片信息不存在"),
    MARK_RESULT_SHERRORCODE_558("mark.result.sherrorcode_558", "您当前账号暂未绑定手机号，请前往【工作台>营销助手>名片】点击右上角编辑完善手机号信息后再重试"),
    MARK_RESULT_SHERRORCODE_559("mark.result.sherrorcode_559", "海报大小不能超过10M"),
    MARK_RESULT_SHERRORCODE_560("mark.result.sherrorcode_560", "当前生成的外部二维码无法解析，请检查参数配置是否正确"),
    MARK_RESULT_SHERRORCODE_563("mark.result.sherrorcode_563", "访客"),
    MARK_RESULT_SHERRORCODE_564("mark.result.sherrorcode_564", "无权限查看此营销用户"),
    MARK_RESULT_SHERRORCODE_565("mark.result.sherrorcode_565", "营销用户不存在或创建中"),
    MARK_RESULT_SHERRORCODE_569("mark.result.sherrorcode_569", "已启用流程达到上限"),
    MARK_RESULT_SHERRORCODE_570("mark.result.sherrorcode_570", "sop任务没找到"),
    MARK_RESULT_SHERRORCODE_573("mark.result.sherrorcode_573", "未找到素材"),
    MARK_RESULT_SHERRORCODE_577("mark.result.sherrorcode_577", "导入文件无数据，请检查文件"),
    MARK_RESULT_SHERRORCODE_578("mark.result.sherrorcode_578", "导入数据超出"),
    MARK_RESULT_SHERRORCODE_581("mark.result.sherrorcode_581", "未查询到参会人员，请进行确认"),
    MARK_RESULT_SHERRORCODE_582("mark.result.sherrorcode_582", "微信会议卡券已经存在"),
    MARK_RESULT_SHERRORCODE_583("mark.result.sherrorcode_583", "微信会议卡券Logo保存失败"),
    MARK_RESULT_SHERRORCODE_584("mark.result.sherrorcode_584", "微信会议卡券保存数据库失败"),
    MARK_RESULT_SHERRORCODE_585("mark.result.sherrorcode_585", "检查公众号是否开通卡券功能失败"),
    MARK_RESULT_SHERRORCODE_586("mark.result.sherrorcode_586", "公众号未开通卡券功能，请先开通"),
    MARK_RESULT_SHERRORCODE_587("mark.result.sherrorcode_587", "微信卡券创建失败"),
    MARK_RESULT_SHERRORCODE_588("mark.result.sherrorcode_588", "微信卡券状态检查失败"),
    MARK_RESULT_SHERRORCODE_589("mark.result.sherrorcode_589", "公众号卡券不存在"),
    MARK_RESULT_SHERRORCODE_590("mark.result.sherrorcode_590", "公众号卡券报名记录不存在"),
    MARK_RESULT_SHERRORCODE_591("mark.result.sherrorcode_591", "核销失败，您的卡券已核销"),
    MARK_RESULT_SHERRORCODE_592("mark.result.sherrorcode_592", "核销失败，您的卡券已过期"),
    MARK_RESULT_SHERRORCODE_593("mark.result.sherrorcode_593", "核销失败，您的卡券转赠中"),
    MARK_RESULT_SHERRORCODE_594("mark.result.sherrorcode_594", "核销失败，您的卡券转赠超时"),
    MARK_RESULT_SHERRORCODE_595("mark.result.sherrorcode_595", "核销失败，您的卡券已删除"),
    MARK_RESULT_SHERRORCODE_596("mark.result.sherrorcode_596", "核销失败，您的卡券已失效"),
    MARK_RESULT_SHERRORCODE_597("mark.result.sherrorcode_597", "核销失败，您的卡券发生未知异常"),
    MARK_RESULT_SHERRORCODE_598("mark.result.sherrorcode_598", "该用户已签到"),
    MARK_RESULT_SHERRORCODE_601("mark.result.sherrorcode_601", "找不到该站点"),
    MARK_RESULT_SHERRORCODE_602("mark.result.sherrorcode_602", "没有权限查看及修改内容"),
    MARK_RESULT_SHERRORCODE_603("mark.result.sherrorcode_603", "找不到该页面"),
    MARK_RESULT_SHERRORCODE_604("mark.result.sherrorcode_604", "找不到该站点模板"),
    MARK_RESULT_SHERRORCODE_605("mark.result.sherrorcode_605", "找不到该模板页面"),
    MARK_RESULT_SHERRORCODE_606("mark.result.sherrorcode_606", "状态变更非法"),
    MARK_RESULT_SHERRORCODE_607("mark.result.sherrorcode_607", "微页面下必须至少含有一个页面"),
    MARK_RESULT_SHERRORCODE_608("mark.result.sherrorcode_608", "该页面已停用"),
    MARK_RESULT_SHERRORCODE_609("mark.result.sherrorcode_609", "该页面已删除"),
    MARK_RESULT_SHERRORCODE_611("mark.result.sherrorcode_611", "复制站点绑定市场活动失败"),
    MARK_RESULT_SHERRORCODE_612("mark.result.sherrorcode_612", "分组名已经存在"),
    MARK_RESULT_SHERRORCODE_614("mark.result.sherrorcode_614", "不能使用默认分组名称"),
    MARK_RESULT_SHERRORCODE_615("mark.result.sherrorcode_615", "查询微页面模板列表失败"),
    MARK_RESULT_SHERRORCODE_616("mark.result.sherrorcode_616", "要复制的微页面不存在"),
    MARK_RESULT_SHERRORCODE_617("mark.result.sherrorcode_617", "微页面名称不能同名"),
    MARK_RESULT_SHERRORCODE_618("mark.result.sherrorcode_618", "创建微站失败"),
    MARK_RESULT_SHERRORCODE_619("mark.result.sherrorcode_619", "部分页面找不到,请刷新"),
    MARK_RESULT_SHERRORCODE_620("mark.result.sherrorcode_620", "部门模板找不到,请刷新"),
    MARK_RESULT_SHERRORCODE_621("mark.result.sherrorcode_621", "只能删除停用状态的微页面"),
    MARK_RESULT_SHERRORCODE_623("mark.result.sherrorcode_623", "微页面模板名称不能同名"),
    MARK_RESULT_SHERRORCODE_626("mark.result.sherrorcode_626", "只能绑定一个百度账户"),
    MARK_RESULT_SHERRORCODE_627("mark.result.sherrorcode_627", "百度api请求出错"),
    MARK_RESULT_SHERRORCODE_628("mark.result.sherrorcode_628", "百度api返回数据异常"),
    MARK_RESULT_SHERRORCODE_629("mark.result.sherrorcode_629", "百度api返回数据为空"),
    MARK_RESULT_SHERRORCODE_630("mark.result.sherrorcode_630", "该市场活动已关联到其他推广计划，无法重复关联"),
    MARK_RESULT_SHERRORCODE_631("mark.result.sherrorcode_631", "未绑定广告账户或已解绑"),
    MARK_RESULT_SHERRORCODE_632("mark.result.sherrorcode_632", "绑定的广告账户已停止使用"),
    MARK_RESULT_SHERRORCODE_633("mark.result.sherrorcode_633", "一个小时内只能刷新一次"),
    MARK_RESULT_SHERRORCODE_634("mark.result.sherrorcode_634", "获取百度广告权限失败，请检查帐号正确性"),
    MARK_RESULT_SHERRORCODE_635("mark.result.sherrorcode_635", "设置同步开关前请先映射对象字段"),
    MARK_RESULT_SHERRORCODE_636("mark.result.sherrorcode_636", "百度账号用户名无效"),
    MARK_RESULT_SHERRORCODE_637("mark.result.sherrorcode_637", "百度账号密码无效"),
    MARK_RESULT_SHERRORCODE_638("mark.result.sherrorcode_638", "百度账号token无效"),
    MARK_RESULT_SHERRORCODE_639("mark.result.sherrorcode_639", "该百度账号已绑定成功，请勿重复绑定"),
    MARK_RESULT_SHERRORCODE_640("mark.result.sherrorcode_640", "未绑定广告账户,清重新绑定"),
    MARK_RESULT_SHERRORCODE_641("mark.result.sherrorcode_641", "该广告账户已删除"),
    MARK_RESULT_SHERRORCODE_642("mark.result.sherrorcode_642", "该广告账户不存在"),
    MARK_RESULT_SHERRORCODE_643("mark.result.sherrorcode_643", "广告账户对应的企业不一致"),
    MARK_RESULT_SHERRORCODE_644("mark.result.sherrorcode_644", "OCPC配置不存在"),
    MARK_RESULT_SHERRORCODE_645("mark.result.sherrorcode_645", "TOKEN不存在"),
    MARK_RESULT_SHERRORCODE_646("mark.result.sherrorcode_646", "线索不存在"),
    MARK_RESULT_SHERRORCODE_647("mark.result.sherrorcode_647", "线索没有关联市场活动"),
    MARK_RESULT_SHERRORCODE_648("mark.result.sherrorcode_648", "市场活动没有关联广告计划"),
    MARK_RESULT_SHERRORCODE_649("mark.result.sherrorcode_649", "广告账号状态未启用或已被删除"),
    MARK_RESULT_SHERRORCODE_650("mark.result.sherrorcode_650", "未开启广告回传"),
    MARK_RESULT_SHERRORCODE_651("mark.result.sherrorcode_651", "未开启无效返款"),
    MARK_RESULT_SHERRORCODE_652("mark.result.sherrorcode_652", "线索没有营销推广来源"),
    MARK_RESULT_SHERRORCODE_653("mark.result.sherrorcode_653", "线索关联的落地页不包含bd_vid"),
    MARK_RESULT_SHERRORCODE_654("mark.result.sherrorcode_654", "营销推广来源的外部平台线索id为空"),
    MARK_RESULT_SHERRORCODE_655("mark.result.sherrorcode_655", "线索的号码为空"),
    MARK_RESULT_SHERRORCODE_656("mark.result.sherrorcode_656", "获取虚拟中间号失败"),
    MARK_RESULT_SHERRORCODE_657("mark.result.sherrorcode_657", "时间范围参数错误"),
    MARK_RESULT_SHERRORCODE_658("mark.result.sherrorcode_658", "标题不能超过15个字"),
    MARK_RESULT_SHERRORCODE_659("mark.result.sherrorcode_659", "获客成本设置参数出错"),
    MARK_RESULT_SHERRORCODE_660("mark.result.sherrorcode_660", "不能编辑不可编辑字段"),
    MARK_RESULT_SHERRORCODE_661("mark.result.sherrorcode_661", "广告投放效果趋势设置参数出错"),
    MARK_RESULT_SHERRORCODE_662("mark.result.sherrorcode_662", "广告账户获客设置参数出错"),
    MARK_RESULT_SHERRORCODE_663("mark.result.sherrorcode_663", "字段名称不能为空"),
    MARK_RESULT_SHERRORCODE_664("mark.result.sherrorcode_664", "字段ID不能为空"),
    MARK_RESULT_SHERRORCODE_665("mark.result.sherrorcode_665", "时间对比类型不对"),
    MARK_RESULT_SHERRORCODE_666("mark.result.sherrorcode_666", "自定义时间的间隔天数不一致"),
    MARK_RESULT_SHERRORCODE_667("mark.result.sherrorcode_667", "模块位置信息不存在"),
    MARK_RESULT_SHERRORCODE_668("mark.result.sherrorcode_668", "时间参数设置错误"),
    MARK_RESULT_SHERRORCODE_669("mark.result.sherrorcode_669", "概览设置不存在"),
    MARK_RESULT_SHERRORCODE_670("mark.result.sherrorcode_670", "广告地域分布设置不存在"),
    MARK_RESULT_SHERRORCODE_671("mark.result.sherrorcode_671", "广告地域分布设置错误"),
    MARK_RESULT_SHERRORCODE_672("mark.result.sherrorcode_672", "字段值不存在"),
    MARK_RESULT_SHERRORCODE_673("mark.result.sherrorcode_673", "单选字段不允许选择多个值"),
    MARK_RESULT_SHERRORCODE_674("mark.result.sherrorcode_674", "选择字段不允许为空"),
    MARK_RESULT_SHERRORCODE_675("mark.result.sherrorcode_675", "时间间隔不能超过365天"),
    MARK_RESULT_SHERRORCODE_676("mark.result.sherrorcode_676", "转化周期设置不存在"),
    MARK_RESULT_SHERRORCODE_677("mark.result.sherrorcode_677", "样板间账号不支持解绑"),
    MARK_RESULT_SHERRORCODE_678("mark.result.sherrorcode_678", "没有配置秘钥"),
    MARK_RESULT_SHERRORCODE_679("mark.result.sherrorcode_679", "头部没有签名参数"),
    MARK_RESULT_SHERRORCODE_680("mark.result.sherrorcode_680", "签名长度不对"),
    MARK_RESULT_SHERRORCODE_681("mark.result.sherrorcode_681", "签名不一致"),
    MARK_RESULT_SHERRORCODE_682("mark.result.sherrorcode_682", "请求过期"),
    MARK_RESULT_SHERRORCODE_683("mark.result.sherrorcode_683", "请先点击【设置】,并按照步骤完成API联调"),
    MARK_RESULT_SHERRORCODE_684("mark.result.sherrorcode_684", "广告回传明细对象不存在"),
    MARK_RESULT_SHERRORCODE_685("mark.result.sherrorcode_685", "加密方式不存在"),
    MARK_RESULT_SHERRORCODE_689("mark.result.sherrorcode_689", "官网信息不存在，请先配置"),
    MARK_RESULT_SHERRORCODE_690("mark.result.sherrorcode_690", "追踪数据不存在"),
    MARK_RESULT_SHERRORCODE_691("mark.result.sherrorcode_691", "自定义ID已存在，请重新填写"),
    MARK_RESULT_SHERRORCODE_692("mark.result.sherrorcode_692", "属性自定义ID已存在，请重新填写"),
    MARK_RESULT_SHERRORCODE_693("mark.result.sherrorcode_693", "输入名称过长，最多30字符，请修改后重试"),
    MARK_RESULT_SHERRORCODE_694("mark.result.sherrorcode_694", "官网名称或URL已经存在，请修改后重试"),
    MARK_RESULT_SHERRORCODE_695("mark.result.sherrorcode_695", "物料已被其他官网绑定"),
    MARK_RESULT_SHERRORCODE_698("mark.result.sherrorcode_698", "营销顾问外部接口错误"),
    MARK_RESULT_SHERRORCODE_699("mark.result.sherrorcode_699", "营销顾问初始化失败"),
    MARK_RESULT_SHERRORCODE_700("mark.result.sherrorcode_700", "服务号取消授权"),
    MARK_RESULT_SHERRORCODE_704("mark.result.sherrorcode_704", "获取access token 失败"),
    MARK_RESULT_SHERRORCODE_705("mark.result.sherrorcode_705", "获取企业ticket签名失败"),
    MARK_RESULT_SHERRORCODE_706("mark.result.sherrorcode_706", "获取应用ticket签名失败"),
    MARK_RESULT_SHERRORCODE_707("mark.result.sherrorcode_707", "企业微信获取用户信息失败"),
    MARK_RESULT_SHERRORCODE_708("mark.result.sherrorcode_708", "获取企业微信绑定纷享用户信息失败"),
    MARK_RESULT_SHERRORCODE_709("mark.result.sherrorcode_709", "企业微信回调创建fs token失败"),
    MARK_RESULT_SHERRORCODE_710("mark.result.sherrorcode_710", "获取客户群列表失败"),
    MARK_RESULT_SHERRORCODE_711("mark.result.sherrorcode_711", "获取客户群统计数据失败"),
    MARK_RESULT_SHERRORCODE_712("mark.result.sherrorcode_712", "查询企业微信详情失败"),
    MARK_RESULT_SHERRORCODE_713("mark.result.sherrorcode_713", "企业微信客户数据不存在"),
    MARK_RESULT_SHERRORCODE_714("mark.result.sherrorcode_714", "设置企业微信联系我失败"),
    MARK_RESULT_SHERRORCODE_715("mark.result.sherrorcode_715", "配置企业微信应用失败"),
    MARK_RESULT_SHERRORCODE_716("mark.result.sherrorcode_716", "吸纷二维码不存在"),
    MARK_RESULT_SHERRORCODE_717("mark.result.sherrorcode_717", "部分员工活码不存在,请刷新后再试"),
    MARK_RESULT_SHERRORCODE_718("mark.result.sherrorcode_718", "吸纷二维码已经删除"),
    MARK_RESULT_SHERRORCODE_719("mark.result.sherrorcode_719", "发送企业微信小程序信息失败"),
    MARK_RESULT_SHERRORCODE_721("mark.result.sherrorcode_721", "企业未绑定企业微信"),
    MARK_RESULT_SHERRORCODE_722("mark.result.sherrorcode_722", "企业微信绑定手机与纷享账不一致"),
    MARK_RESULT_SHERRORCODE_723("mark.result.sherrorcode_723", "企业微信配置信息获取失败"),
    MARK_RESULT_SHERRORCODE_724("mark.result.sherrorcode_724", "企业微信用户绑定信息获取失败"),
    MARK_RESULT_SHERRORCODE_725("mark.result.sherrorcode_725", "当前用户无需执行此操作"),
    MARK_RESULT_SHERRORCODE_726("mark.result.sherrorcode_726", "对应用户小程序信息获取失败"),
    MARK_RESULT_SHERRORCODE_727("mark.result.sherrorcode_727", "当前appId与企业绑定appid不同"),
    MARK_RESULT_SHERRORCODE_728("mark.result.sherrorcode_728", "该企业已开通企业微信请在企业微信开通账号"),
    MARK_RESULT_SHERRORCODE_729("mark.result.sherrorcode_729", "企业需要绑定企业微信"),
    MARK_RESULT_SHERRORCODE_730("mark.result.sherrorcode_730", "文件太大或太小上传失败"),
    MARK_RESULT_SHERRORCODE_731("mark.result.sherrorcode_731", "发送文件类型错误"),
    MARK_RESULT_SHERRORCODE_732("mark.result.sherrorcode_732", "企业微信朋友圈发布任务异常"),
    MARK_RESULT_SHERRORCODE_733("mark.result.sherrorcode_733", "未找到sop任务"),
    MARK_RESULT_SHERRORCODE_735("mark.result.sherrorcode_735", "企业微信未授权"),
    MARK_RESULT_SHERRORCODE_736("mark.result.sherrorcode_736", "企微群活码不存在"),
    MARK_RESULT_SHERRORCODE_737("mark.result.sherrorcode_737", "部分企微群活码不存在,请刷新后再试"),
    MARK_RESULT_SHERRORCODE_738("mark.result.sherrorcode_738", "企微群活码已经删除"),
    MARK_RESULT_SHERRORCODE_739("mark.result.sherrorcode_739", "企微欢迎语不存在"),
    MARK_RESULT_SHERRORCODE_740("mark.result.sherrorcode_740", "部分企微欢迎语不存在,请刷新后再试"),
    MARK_RESULT_SHERRORCODE_741("mark.result.sherrorcode_741", "企微欢迎语已经删除"),
    MARK_RESULT_SHERRORCODE_742("mark.result.sherrorcode_742", "企微SOP不存在"),
    MARK_RESULT_SHERRORCODE_743("mark.result.sherrorcode_743", "部分企微SOP不存在,请刷新后再试"),
    MARK_RESULT_SHERRORCODE_744("mark.result.sherrorcode_744", "企微SOP已经删除"),
    MARK_RESULT_SHERRORCODE_747("mark.result.sherrorcode_747", "创建市场活动失败"),
    MARK_RESULT_SHERRORCODE_748("mark.result.sherrorcode_748", "直播创建失败"),
    MARK_RESULT_SHERRORCODE_749("mark.result.sherrorcode_749", "直播不存在"),
    MARK_RESULT_SHERRORCODE_750("mark.result.sherrorcode_750", "更新直播失败"),
    MARK_RESULT_SHERRORCODE_751("mark.result.sherrorcode_751", "直播统计数据不存在"),
    MARK_RESULT_SHERRORCODE_752("mark.result.sherrorcode_752", "创建直播封面失败"),
    MARK_RESULT_SHERRORCODE_753("mark.result.sherrorcode_753", "直播已经删除"),
    MARK_RESULT_SHERRORCODE_754("mark.result.sherrorcode_754", "用户未报名"),
    MARK_RESULT_SHERRORCODE_755("mark.result.sherrorcode_755", "市场活动重名"),
    MARK_RESULT_SHERRORCODE_756("mark.result.sherrorcode_756", "获取直播状态失败"),
    MARK_RESULT_SHERRORCODE_757("mark.result.sherrorcode_757", "请设置直播封面"),
    MARK_RESULT_SHERRORCODE_758("mark.result.sherrorcode_758", "不能更改直播平台"),
    MARK_RESULT_SHERRORCODE_759("mark.result.sherrorcode_759", "获取小鹅通accessToken失败"),
    MARK_RESULT_SHERRORCODE_760("mark.result.sherrorcode_760", "获取小鹅通直播列表失败"),
    MARK_RESULT_SHERRORCODE_761("mark.result.sherrorcode_761", "注册小鹅通用户失败"),
    MARK_RESULT_SHERRORCODE_762("mark.result.sherrorcode_762", "查询小鹅通用户失败"),
    MARK_RESULT_SHERRORCODE_763("mark.result.sherrorcode_763", "未绑定小鹅通帐号"),
    MARK_RESULT_SHERRORCODE_764("mark.result.sherrorcode_764", "小鹅通帐号已经存在"),
    MARK_RESULT_SHERRORCODE_765("mark.result.sherrorcode_765", "同步用户小鹅通登陆状态失败"),
    MARK_RESULT_SHERRORCODE_766("mark.result.sherrorcode_766", "获取保利威签名失败"),
    MARK_RESULT_SHERRORCODE_767("mark.result.sherrorcode_767", "获取保利威直播列表失败"),
    MARK_RESULT_SHERRORCODE_768("mark.result.sherrorcode_768", "获取保利威直播详情失败"),
    MARK_RESULT_SHERRORCODE_769("mark.result.sherrorcode_769", "绑定信息错误"),
    MARK_RESULT_SHERRORCODE_770("mark.result.sherrorcode_770", "未绑定保利威账号"),
    MARK_RESULT_SHERRORCODE_771("mark.result.sherrorcode_771", "小鹅通账号配置有误"),
    MARK_RESULT_SHERRORCODE_772("mark.result.sherrorcode_772", "当前直播已关联其他市场活动"),
    MARK_RESULT_SHERRORCODE_774("mark.result.sherrorcode_774", "视频号已经被绑定"),
    MARK_RESULT_SHERRORCODE_777("mark.result.sherrorcode_777", "目睹账号配置有误"),
    MARK_RESULT_SHERRORCODE_778("mark.result.sherrorcode_778", "获取目睹活动列表失败"),
    MARK_RESULT_SHERRORCODE_779("mark.result.sherrorcode_779", "该账号已被绑定过"),
    MARK_RESULT_SHERRORCODE_780("mark.result.sherrorcode_780", "当前直播不存在父活动"),
    MARK_RESULT_SHERRORCODE_783("mark.result.sherrorcode_783", "微吼账号配置有误"),
    MARK_RESULT_SHERRORCODE_784("mark.result.sherrorcode_784", "获取微吼直播列表失败"),
    MARK_RESULT_SHERRORCODE_785("mark.result.sherrorcode_785", "获取微吼直播详情失败"),
    MARK_RESULT_SHERRORCODE_788("mark.result.sherrorcode_788", "视频已经存在"),
    MARK_RESULT_SHERRORCODE_789("mark.result.sherrorcode_789", "视频上传转码失败"),
    MARK_RESULT_SHERRORCODE_790("mark.result.sherrorcode_790", "取消视频转码失败"),
    MARK_RESULT_SHERRORCODE_792("mark.result.sherrorcode_792", "视频不存在"),
    MARK_RESULT_SHERRORCODE_793("mark.result.sherrorcode_793", "部分视频不存在,请刷新"),
    MARK_RESULT_SHERRORCODE_795("mark.result.sherrorcode_795", "企业还未绑定小程序"),
    MARK_RESULT_SHERRORCODE_796("mark.result.sherrorcode_796", "提交代码错误"),
    MARK_RESULT_SHERRORCODE_797("mark.result.sherrorcode_797", "提交审核错误"),
    MARK_RESULT_SHERRORCODE_798("mark.result.sherrorcode_798", "代码发布失败"),
    MARK_RESULT_SHERRORCODE_799("mark.result.sherrorcode_799", "小程序未授权给该平台"),
    MARK_RESULT_SHERRORCODE_801("mark.result.sherrorcode_801", "订单已支付"),
    MARK_RESULT_SHERRORCODE_802("mark.result.sherrorcode_802", "订单已关闭"),
    MARK_RESULT_SHERRORCODE_803("mark.result.sherrorcode_803", "订单已过期"),
    MARK_RESULT_SHERRORCODE_804("mark.result.sherrorcode_804", "订单已留单，但状态未知"),
    MARK_RESULT_SHERRORCODE_805("mark.result.sherrorcode_805", "商家配置未配置"),
    MARK_RESULT_SHERRORCODE_807("mark.result.sherrorcode_807", "企业已经绑定邮件帐号"),
    MARK_RESULT_SHERRORCODE_808("mark.result.sherrorcode_808", "企业未绑定邮件帐号"),
    MARK_RESULT_SHERRORCODE_809("mark.result.sherrorcode_809", "当前企业邮件模板达到上限"),
    MARK_RESULT_SHERRORCODE_810("mark.result.sherrorcode_810", "创建邮件模板失败"),
    MARK_RESULT_SHERRORCODE_811("mark.result.sherrorcode_811", "批量查询邮件模板失败"),
    MARK_RESULT_SHERRORCODE_812("mark.result.sherrorcode_812", "查询邮件模板详情失败"),
    MARK_RESULT_SHERRORCODE_813("mark.result.sherrorcode_813", "删除邮件模板失败"),
    MARK_RESULT_SHERRORCODE_814("mark.result.sherrorcode_814", "更新邮件模板"),
    MARK_RESULT_SHERRORCODE_815("mark.result.sherrorcode_815", "查询邮件统计数据失败"),
    MARK_RESULT_SHERRORCODE_816("mark.result.sherrorcode_816", "创建邮件标签失败"),
    MARK_RESULT_SHERRORCODE_817("mark.result.sherrorcode_817", "删除邮件标签失败"),
    MARK_RESULT_SHERRORCODE_818("mark.result.sherrorcode_818", "更新邮件标签失败"),
    MARK_RESULT_SHERRORCODE_819("mark.result.sherrorcode_819", "新增邮件发信域名失败"),
    MARK_RESULT_SHERRORCODE_820("mark.result.sherrorcode_820", "发信域名验证未通过"),
    MARK_RESULT_SHERRORCODE_821("mark.result.sherrorcode_821", "更新发信域名失败"),
    MARK_RESULT_SHERRORCODE_822("mark.result.sherrorcode_822", "创建邮件地址列表失败"),
    MARK_RESULT_SHERRORCODE_823("mark.result.sherrorcode_823", "api_user 信息不存在"),
    MARK_RESULT_SHERRORCODE_824("mark.result.sherrorcode_824", "域名信息不存在"),
    MARK_RESULT_SHERRORCODE_825("mark.result.sherrorcode_825", "域名信息已校验通过不允许修改"),
    MARK_RESULT_SHERRORCODE_826("mark.result.sherrorcode_826", "邮件地址格式不正确"),
    MARK_RESULT_SHERRORCODE_827("mark.result.sherrorcode_827", "邮件账户信息查询失败"),
    MARK_RESULT_SHERRORCODE_828("mark.result.sherrorcode_828", "邮件账户余额不足,请充值后再发送"),
    MARK_RESULT_SHERRORCODE_829("mark.result.sherrorcode_829", "所选列表无可发送邮件"),
    MARK_RESULT_SHERRORCODE_830("mark.result.sherrorcode_830", "附件转换失败"),
    MARK_RESULT_SHERRORCODE_831("mark.result.sherrorcode_831", "发送失败或者发送完成邮件才能导出"),
    MARK_RESULT_SHERRORCODE_832("mark.result.sherrorcode_832", "邮件账号配置有误"),
    MARK_RESULT_SHERRORCODE_834("mark.result.sherrorcode_834", "存在卡片不能删除"),
    MARK_RESULT_SHERRORCODE_835("mark.result.sherrorcode_835", "没有编辑和查看该看板权限"),
    MARK_RESULT_SHERRORCODE_836("mark.result.sherrorcode_836", "看板不存在"),
    MARK_RESULT_SHERRORCODE_838("mark.result.sherrorcode_838", "手机号已被注册"),
    MARK_RESULT_SHERRORCODE_839("mark.result.sherrorcode_839", "会员帐号不存在"),
    MARK_RESULT_SHERRORCODE_840("mark.result.sherrorcode_840", "小程序用户已绑定会员"),
    MARK_RESULT_SHERRORCODE_841("mark.result.sherrorcode_841", "微页面已在访问控制列表中"),
    MARK_RESULT_SHERRORCODE_842("mark.result.sherrorcode_842", "非会员拒绝访问"),
    MARK_RESULT_SHERRORCODE_843("mark.result.sherrorcode_843", "非会员"),
    MARK_RESULT_SHERRORCODE_844("mark.result.sherrorcode_844", "会员没有手机"),
    MARK_RESULT_SHERRORCODE_845("mark.result.sherrorcode_845", "会员报名失败"),
    MARK_RESULT_SHERRORCODE_846("mark.result.sherrorcode_846", "会员不存在对应的wxOpenId"),
    MARK_RESULT_SHERRORCODE_848("mark.result.sherrorcode_848", "会员未审核或未通过"),
    MARK_RESULT_SHERRORCODE_849("mark.result.sherrorcode_849", "wxAppId无法转换成纷享账号"),
    MARK_RESULT_SHERRORCODE_850("mark.result.sherrorcode_850", "请开通会员模块"),
    MARK_RESULT_SHERRORCODE_851("mark.result.sherrorcode_851", "请配置完成名片模板设置后再开启"),
    MARK_RESULT_SHERRORCODE_853("mark.result.sherrorcode_853", "新增推广渠道失败"),
    MARK_RESULT_SHERRORCODE_854("mark.result.sherrorcode_854", "已经存在默认推广渠道"),
    MARK_RESULT_SHERRORCODE_855("mark.result.sherrorcode_855", "查询推广渠道失败"),
    MARK_RESULT_SHERRORCODE_856("mark.result.sherrorcode_856", "删除推广渠道失败"),
    MARK_RESULT_SHERRORCODE_857("mark.result.sherrorcode_857", "渠道已存在，请勿重复添加"),
    MARK_RESULT_SHERRORCODE_858("mark.result.sherrorcode_858", "更新推广渠道失败"),
    MARK_RESULT_SHERRORCODE_859("mark.result.sherrorcode_859", "渠道名称不能同名"),
    MARK_RESULT_SHERRORCODE_860("mark.result.sherrorcode_860", "渠道不存在"),
    MARK_RESULT_SHERRORCODE_861("mark.result.sherrorcode_861", "不能删除预设渠道"),
    MARK_RESULT_SHERRORCODE_862("mark.result.sherrorcode_862", "推广渠道已经被禁用"),
    MARK_RESULT_SHERRORCODE_864("mark.result.sherrorcode_864", "有相同任务正在执行中，请稍后刷新重试"),
    MARK_RESULT_SHERRORCODE_866("mark.result.sherrorcode_866", "非托管小程序，无法查询数据"),
    MARK_RESULT_SHERRORCODE_867("mark.result.sherrorcode_867", "时间范围有误"),
    MARK_RESULT_SHERRORCODE_868("mark.result.sherrorcode_868", "第三方平台不存在"),
    MARK_RESULT_SHERRORCODE_869("mark.result.sherrorcode_869", "小程序不存在"),
    MARK_RESULT_SHERRORCODE_872("mark.result.sherrorcode_872", "优惠券模板不存在"),
    MARK_RESULT_SHERRORCODE_873("mark.result.sherrorcode_873", "溯源优惠券不能关闭"),
    MARK_RESULT_SHERRORCODE_874("mark.result.sherrorcode_874", "验证签名失败"),
    MARK_RESULT_SHERRORCODE_875("mark.result.sherrorcode_875", "回调类型错误"),
    MARK_RESULT_SHERRORCODE_876("mark.result.sherrorcode_876", "请求微信api接口错误"),
    MARK_RESULT_SHERRORCODE_877("mark.result.sherrorcode_877", "您的企业还未开通互联应用基础包，请先应用互联业务后，才可启用伙伴营销"),
    MARK_RESULT_SHERRORCODE_878("mark.result.sherrorcode_878", "券已被领完"),
    MARK_RESULT_SHERRORCODE_879("mark.result.sherrorcode_879", "优惠券开关暂未开启,请您先开启"),
    MARK_RESULT_SHERRORCODE_880("mark.result.sherrorcode_880", "优惠券实例对象初始化失败"),
    MARK_RESULT_SHERRORCODE_881("mark.result.sherrorcode_881", "部分优惠券模板不存在,请刷新"),
    MARK_RESULT_SHERRORCODE_883("mark.result.sherrorcode_883", "未连接微信商户"),
    MARK_RESULT_SHERRORCODE_885("mark.result.sherrorcode_885", "已超过每人领取数量,无法继续领取"),
    MARK_RESULT_SHERRORCODE_887("mark.result.sherrorcode_887", "该企业暂未发放优惠券"),
    MARK_RESULT_SHERRORCODE_889("mark.result.sherrorcode_889", "发放总量必须大于已领取数量"),
    MARK_RESULT_SHERRORCODE_891("mark.result.sherrorcode_891", "不存在优惠券可下发企业"),
    MARK_RESULT_SHERRORCODE_892("mark.result.sherrorcode_892", "不存在优惠券可领取企业"),
    MARK_RESULT_SHERRORCODE_894("mark.result.sherrorcode_894", "未查询到下游tenantId"),
    MARK_RESULT_SHERRORCODE_896("mark.result.sherrorcode_896", "下游门店未关联客户"),
    MARK_RESULT_SHERRORCODE_898("mark.result.sherrorcode_898", "优惠券已终止,参与不成功"),
    MARK_RESULT_SHERRORCODE_900("mark.result.sherrorcode_900", "优惠券已终止,无法领取"),
    MARK_RESULT_SHERRORCODE_902("mark.result.sherrorcode_902", "不在领取范围,无法领取"),
    MARK_RESULT_SHERRORCODE_904("mark.result.sherrorcode_904", "暂无查看优惠券权限"),
    MARK_RESULT_SHERRORCODE_907("mark.result.sherrorcode_907", "巨量引擎api请求出错"),
    MARK_RESULT_SHERRORCODE_908("mark.result.sherrorcode_908", "巨量引擎api返回数据为空"),
    MARK_RESULT_SHERRORCODE_909("mark.result.sherrorcode_909", "巨量引擎api返回数据异常"),
    MARK_RESULT_SHERRORCODE_910("mark.result.sherrorcode_910", "获取巨量引擎权限失败，请查看账号正确性"),
    MARK_RESULT_SHERRORCODE_911("mark.result.sherrorcode_911", "不支持的广告账户类型"),
    MARK_RESULT_SHERRORCODE_912("mark.result.sherrorcode_912", "获取头条access_token出错"),
    MARK_RESULT_SHERRORCODE_913("mark.result.sherrorcode_913", "刷新头条access_token出错"),
    MARK_RESULT_SHERRORCODE_914("mark.result.sherrorcode_914", "广告计划已经关联子级市场活动，不能重复关联"),
    MARK_RESULT_SHERRORCODE_915("mark.result.sherrorcode_915", "该广告账户在营销通中不存在"),
    MARK_RESULT_SHERRORCODE_916("mark.result.sherrorcode_916", "没有权限对头条账户进行相关操作"),
    MARK_RESULT_SHERRORCODE_917("mark.result.sherrorcode_917", "头条过滤条件的field字段错误"),
    MARK_RESULT_SHERRORCODE_918("mark.result.sherrorcode_918", "请求巨量引擎远程接口过于频繁"),
    MARK_RESULT_SHERRORCODE_919("mark.result.sherrorcode_919", "不合法的接入用户"),
    MARK_RESULT_SHERRORCODE_920("mark.result.sherrorcode_920", "access token已过期"),
    MARK_RESULT_SHERRORCODE_921("mark.result.sherrorcode_921", "refresh token已过期"),
    MARK_RESULT_SHERRORCODE_922("mark.result.sherrorcode_922", "access token为空"),
    MARK_RESULT_SHERRORCODE_923("mark.result.sherrorcode_923", "access token错误"),
    MARK_RESULT_SHERRORCODE_924("mark.result.sherrorcode_924", "账户登录异常"),
    MARK_RESULT_SHERRORCODE_925("mark.result.sherrorcode_925", "refresh token错误"),
    MARK_RESULT_SHERRORCODE_926("mark.result.sherrorcode_926", "授权类型错误"),
    MARK_RESULT_SHERRORCODE_927("mark.result.sherrorcode_927", "密码AES加密错误"),
    MARK_RESULT_SHERRORCODE_928("mark.result.sherrorcode_928", "充值金额太少"),
    MARK_RESULT_SHERRORCODE_929("mark.result.sherrorcode_929", "账户余额不足"),
    MARK_RESULT_SHERRORCODE_930("mark.result.sherrorcode_930", "广告主状态不可用"),
    MARK_RESULT_SHERRORCODE_931("mark.result.sherrorcode_931", "广告主在黑名单中"),
    MARK_RESULT_SHERRORCODE_932("mark.result.sherrorcode_932", "密码过于简单"),
    MARK_RESULT_SHERRORCODE_933("mark.result.sherrorcode_933", "邮箱已存在"),
    MARK_RESULT_SHERRORCODE_934("mark.result.sherrorcode_934", "邮箱不合法"),
    MARK_RESULT_SHERRORCODE_935("mark.result.sherrorcode_935", "名字已存在"),
    MARK_RESULT_SHERRORCODE_936("mark.result.sherrorcode_936", "文件签名错误"),
    MARK_RESULT_SHERRORCODE_938("mark.result.sherrorcode_938", "当前开发者账号日累计调用接口次数超限"),
    MARK_RESULT_SHERRORCODE_939("mark.result.sherrorcode_939", "调用广告主账户和其他同主体广告主账户使用受限"),
    MARK_RESULT_SHERRORCODE_940("mark.result.sherrorcode_940", "调用巨量引擎远程接口返回为空"),
    MARK_RESULT_SHERRORCODE_941("mark.result.sherrorcode_941", "调用巨量引擎远程接口返回Data为空"),
    MARK_RESULT_SHERRORCODE_942("mark.result.sherrorcode_942", "授权成功"),
    MARK_RESULT_SHERRORCODE_943("mark.result.sherrorcode_943", "该线索关联的广告计划尚未同步，请在前往营销通点击手动刷新按钮"),
    MARK_RESULT_SHERRORCODE_944("mark.result.sherrorcode_944", "获取授权用户信息失败"),
    MARK_RESULT_SHERRORCODE_945("mark.result.sherrorcode_945", "获取授权账户信息失败"),
    MARK_RESULT_SHERRORCODE_946("mark.result.sherrorcode_946", "用户没有绑定广告主账号"),
    MARK_RESULT_SHERRORCODE_947("mark.result.sherrorcode_947", "找不到广告主账号信息"),
    MARK_RESULT_SHERRORCODE_950("mark.result.sherrorcode_950", "系统正在同步通讯录信息，请关闭页面后重新进入"),
    MARK_RESULT_SHERRORCODE_953("mark.result.sherrorcode_953", "materialType不能为空"),
    MARK_RESULT_SHERRORCODE_954("mark.result.sherrorcode_954", "产品参数不能为空"),
    MARK_RESULT_SHERRORCODE_955("mark.result.sherrorcode_955", "产品名字不能为空"),
    MARK_RESULT_SHERRORCODE_956("mark.result.sherrorcode_956", "产品简介不能为空"),
    MARK_RESULT_SHERRORCODE_957("mark.result.sherrorcode_957", "产品封面不能为空"),
    MARK_RESULT_SHERRORCODE_958("mark.result.sherrorcode_958", "产品详情不能为空"),
    MARK_RESULT_SHERRORCODE_959("mark.result.sherrorcode_959", "产品试用表单ID不能为空"),
    MARK_RESULT_SHERRORCODE_960("mark.result.sherrorcode_960", "产品试用按钮名称不能为空"),
    MARK_RESULT_SHERRORCODE_961("mark.result.sherrorcode_961", "产品封面不能超过3张"),
    MARK_RESULT_SHERRORCODE_962("mark.result.sherrorcode_962", "产品详情图不能超过20张"),
    MARK_RESULT_SHERRORCODE_963("mark.result.sherrorcode_963", "产品图片名字错误"),
    MARK_RESULT_SHERRORCODE_964("mark.result.sherrorcode_964", "未知materialType"),
    MARK_RESULT_SHERRORCODE_965("mark.result.sherrorcode_965", "视频URL只支持腾讯视频"),
    MARK_RESULT_SHERRORCODE_966("mark.result.sherrorcode_966", "更新产品isUpdatePicture不能为空"),
    MARK_RESULT_SHERRORCODE_967("mark.result.sherrorcode_967", "产品试用按钮名称长度不能超过6"),
    MARK_RESULT_SHERRORCODE_968("mark.result.sherrorcode_968", "物料ID不能为空"),
    MARK_RESULT_SHERRORCODE_969("mark.result.sherrorcode_969", "产品查询参数不能为空"),
    MARK_RESULT_SHERRORCODE_970("mark.result.sherrorcode_970", "请设置分页参数pageNum,pageSize"),
    MARK_RESULT_SHERRORCODE_971("mark.result.sherrorcode_971", "pageSize不能超过500"),
    MARK_RESULT_SHERRORCODE_972("mark.result.sherrorcode_972", "pageNum不能超过500"),
    MARK_RESULT_SHERRORCODE_975("mark.result.sherrorcode_975", "当前选中的活动中存在主活动或已经关联其他主活动的市场活动，此类活动不允许关联"),
    MARK_RESULT_SHERRORCODE_977("mark.result.sherrorcode_977", "操作失败，请检查推广活动及关联的市场活动数据是否异常"),
    MARK_RESULT_SHERRORCODE_980("mark.result.sherrorcode_980", "targetObjApiName不能为空"),
    MARK_RESULT_SHERRORCODE_981("mark.result.sherrorcode_981", "leadCoverTarget不能为空"),
    MARK_RESULT_SHERRORCODE_982("mark.result.sherrorcode_982", "rulesTarget不能为空"),
    MARK_RESULT_SHERRORCODE_983("mark.result.sherrorcode_983", "conversionType不能为空"),
    MARK_RESULT_SHERRORCODE_984("mark.result.sherrorcode_984", "查询无满足条件的群主，请重新选择"),
    MARK_RESULT_SHERRORCODE_986("mark.result.sherrorcode_986", "最多可一次指定2000个客户群，请重新选择"),
    MARK_RESULT_SHERRORCODE_989("mark.result.sherrorcode_989", "需要购买营销通专业版及以上版本"),
    MARK_RESULT_SHERRORCODE_990("mark.result.sherrorcode_990", "需要购买订货通"),
    MARK_RESULT_SHERRORCODE_991("mark.result.sherrorcode_991", "未购买在线客服插件"),
    MARK_RESULT_SHERRORCODE_994("mark.result.sherrorcode_994", "您企业的CRM暂未开启多组织，请联系CRM管理员了解详情"),
    MARK_RESULT_SHERRORCODE_995("mark.result.sherrorcode_995", "无拥有数据权限的使用人员"),
    MARK_RESULT_SHERRORCODE_997("mark.result.sherrorcode_997", "需要购买营销通旗舰版"),
    MARK_RESULT_SHERRORCODE_1001("mark.result.sherrorcode_1001", "身份转换营销用户失败"),
    MARK_RESULT_SHERRORCODE_1002("mark.result.sherrorcode_1002", "actionType没有注册到营销通"),
    MARK_RESULT_SHERRORCODE_1003("mark.result.sherrorcode_1003", "自定义行为类型时, actionDescription不能为空"),
    MARK_RESULT_SHERRORCODE_1005("mark.result.sherrorcode_1005", "未购买知识库"),
    MARK_RESULT_SHERRORCODE_1007("mark.result.sherrorcode_1007", "品牌色查询失败"),
    MARK_RESULT_SHERRORCODE_1009("mark.result.sherrorcode_1009", "当前企业暂无【客户】订单，请联系客户经理购买"),
    MARK_RESULT_SHERRORCODE_1012("mark.result.sherrorcode_1012", "参数ea不存在"),
    MARK_RESULT_SHERRORCODE_1013("mark.result.sherrorcode_1013", "参数timestamp不存在"),
    MARK_RESULT_SHERRORCODE_1014("mark.result.sherrorcode_1014", "参数nonce不存在"),
    MARK_RESULT_SHERRORCODE_1015("mark.result.sherrorcode_1015", "参数nonce长度不能超过128"),
    MARK_RESULT_SHERRORCODE_1016("mark.result.sherrorcode_1016", "参数nonce一分钟内重复"),
    MARK_RESULT_SHERRORCODE_1017("mark.result.sherrorcode_1017", "参数timestamp已超过一分钟,请获取最新时间"),
    MARK_RESULT_SHERRORCODE_1018("mark.result.sherrorcode_1018", "参数sign不存在"),
    MARK_RESULT_SHERRORCODE_1019("mark.result.sherrorcode_1019", "参数sign校验出错"),
    MARK_RESULT_SHERRORCODE_1020("mark.result.sherrorcode_1020", "秘钥未配置"),
    MARK_RESULT_SHERRORCODE_1021("mark.result.sherrorcode_1021", "验证码发送频繁,请一分钟后再试"),
    MARK_RESULT_SHERRORCODE_1025("mark.result.sherrorcode_1025", "新建邮件模板失败"),
    MARK_RESULT_SHERRORCODE_1026("mark.result.sherrorcode_1026", "获取code失败"),
    MARK_RESULT_SHERRORCODE_1028("mark.result.sherrorcode_1028", "删除模板失败"),
    MARK_RESULT_SHERRORCODE_1031("mark.result.sherrorcode_1031", "创建活动成员失败"),
    MARK_RESULT_SHERRORCODE_1033("mark.result.sherrorcode_1033", "一次添加活动成员数量超过限制"),
    MARK_RESULT_SHERRORCODE_1035("mark.result.sherrorcode_1035", "新建目标人群运营失败"),
    MARK_RESULT_SHERRORCODE_1036("mark.result.sherrorcode_1036", "更新目标人群运营失败"),
    MARK_RESULT_SHERRORCODE_1037("mark.result.sherrorcode_1037", "当前运营时间范围内不会创建子运营活动，请调整运营时间范围或计划执行时间"),
    MARK_RESULT_SHERRORCODE_1040("mark.result.sherrorcode_1040", "输入内容长度不能大于100"),
    MARK_RESULT_SHERRORCODE_1043("mark.result.sherrorcode_1043", "查询不到seo数据"),
    MARK_RESULT_SHERRORCODE_1045("mark.result.sherrorcode_1045", "域名解析异常错"),
    MARK_RESULT_SHERRORCODE_1050("mark.result.sherrorcode_1050", "数据正在计算中"),
    MARK_RESULT_SHERRORCODE_1051("mark.result.sherrorcode_1051", "MQL定义必须选择一个"),
    MARK_RESULT_SHERRORCODE_1052("mark.result.sherrorcode_1052", "SQL定义必须选择一个"),
    MARK_RESULT_SHERRORCODE_1055("mark.result.sherrorcode_1055", "该企业已经绑定whatsapp"),
    MARK_RESULT_SHERRORCODE_1056("mark.result.sherrorcode_1056", "该企业没有授权信息"),
    MARK_RESULT_SHERRORCODE_1057("mark.result.sherrorcode_1057", "请联系客户经理下单【WhatsApp营销插件】或者升级营销通专业版"),
    MARK_RESULT_SHERRORCODE_1058("mark.result.sherrorcode_1058", "手机号不能为空"),
    MARK_RESULT_SHERRORCODE_1059("mark.result.sherrorcode_1059", "发送类型不能为空"),
    MARK_RESULT_SHERRORCODE_1060("mark.result.sherrorcode_1060", "发送目标对象不能为空"),
    MARK_RESULT_SHERRORCODE_1061("mark.result.sherrorcode_1061", "excel文件不能为空"),
    MARK_RESULT_SHERRORCODE_1062("mark.result.sherrorcode_1062", "目标人群不能为空"),
    MARK_RESULT_SHERRORCODE_1063("mark.result.sherrorcode_1063", "模板不能为空"),
    MARK_RESULT_SHERRORCODE_1064("mark.result.sherrorcode_1064", " 定时发送时间不能为空"),
    MARK_RESULT_SHERRORCODE_1066("mark.result.sherrorcode_1066", "参数数量不匹配"),
    MARK_RESULT_SHERRORCODE_1067("mark.result.sherrorcode_1067", "发送任务不存在"),
    MARK_RESULT_SHERRORCODE_1068("mark.result.sherrorcode_1068", "任务正在处理中,请刷新重试"),
    MARK_RESULT_SHERRORCODE_1069("mark.result.sherrorcode_1069", "只能编辑定时发送的任务"),
    MARK_RESULT_SHERRORCODE_1070("mark.result.sherrorcode_1070", "只能删除待发送、失败或者已取消的任务"),
    MARK_RESULT_SHERRORCODE_1071("mark.result.sherrorcode_1071", "只能删除定时发送的任务"),
    MARK_RESULT_SHERRORCODE_1072("mark.result.sherrorcode_1072", "只能取消定时发送的任务"),
    MARK_RESULT_SHERRORCODE_1073("mark.result.sherrorcode_1073", "只能取消待发送的任务"),
    MARK_RESULT_SHERRORCODE_1074("mark.result.sherrorcode_1074", "只能编辑待发送的任务"),
    MARK_RESULT_SHERRORCODE_1075("mark.result.sherrorcode_1075", "保存授权信息失败,请联系客户经理"),
    MARK_RESULT_SHERRORCODE_1078("mark.result.sherrorcode_1078", "请联系客户经理下单【海外营销插件】或者升级营销通专业版"),
    MARK_RESULT_SHERRORCODE_1080("mark.result.sherrorcode_1080", "请联系客户经理下单【AI营销插件】"),
    MARK_RESULT_SHERRORCODE_1081("mark.result.sherrorcode_1081", "未找到连接对象"),
    MARK_MARKETINGEVENT_ATTRIBUTEDATACALCULATEBO_50("mark.marketingevent.attributedatacalculatebo_50", "新增的商机数量: "),
    MARK_MARKETINGEVENT_ATTRIBUTEDATACALCULATEBO_50_1("mark.marketingevent.attributedatacalculatebo_50_1", ", 影响的商机数量: "),
    MARK_MARKETINGEVENT_ATTRIBUTEDATACALCULATEBO_50_2("mark.marketingevent.attributedatacalculatebo_50_2", ", 影响的订单数量: "),
    MARK_ENTITY_ACTIVITYENTITY_117("mark.entity.activityentity_117", "会议标题"),
    MARK_ENTITY_ACTIVITYENTITY_118("mark.entity.activityentity_118", "会议开始时间"),
    MARK_ENTITY_ACTIVITYENTITY_119("mark.entity.activityentity_119", "会议结束时间"),
    MARK_ENTITY_ACTIVITYENTITY_120("mark.entity.activityentity_120", "会议地点"),
    MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70("mark.entity.campaignmergedataentity_70", "姓名"),
    MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_72("mark.entity.campaignmergedataentity_72", "电话"),
    MARK_LIVE_MARKETINGLIVEENTITY_118("mark.live.marketingliveentity_118", "直播标题"),
    MARK_LIVE_MARKETINGLIVEENTITY_119("mark.live.marketingliveentity_119", "直播开始时间"),
    MARK_LIVE_MARKETINGLIVEENTITY_120("mark.live.marketingliveentity_120", "直播结束时间"),
    MARK_LIVE_MARKETINGLIVEENTITY_121("mark.live.marketingliveentity_121", "讲师"),
    MARK_LIVE_MARKETINGLIVEENTITY_122("mark.live.marketingliveentity_122", "直播链接"),
    MARK_ENTITY_MARKETINGEVENTCOMMONSETTINGENTITY_39("mark.entity.marketingeventcommonsettingentity_39", "线上活动"),
    MARK_ENTITY_MARKETINGEVENTCOMMONSETTINGENTITY_42("mark.entity.marketingeventcommonsettingentity_42", "多会场活动"),
    MARK_MANAGER_ACTIVITYMANAGER_1023("mark.manager.activitymanager_1023", "多会场活动推广模板示例"),
    MARK_MANAGER_ACTIVITYMANAGER_1196("mark.manager.activitymanager_1196", "报名已截止"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_542("mark.advertiser.adbigscreenmanager_542", "广告营销数据大屏"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_551("mark.advertiser.adbigscreenmanager_551", "广告获客成本"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_567("mark.advertiser.adbigscreenmanager_567", "广告投放效果趋势"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_584("mark.advertiser.adbigscreenmanager_584", "广告获客转化漏斗"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_594("mark.advertiser.adbigscreenmanager_594", "广告账户获客对比"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_611("mark.advertiser.adbigscreenmanager_611", "广告账户投入产出分析"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_639("mark.advertiser.adbigscreenmanager_639", "转化周期"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_776("mark.advertiser.adbigscreenmanager_776", "广告线索地域分布"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_838("mark.advertiser.adbigscreenmanager_838", "概览设置"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_972("mark.advertiser.adbigscreenmanager_972", "月"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_980("mark.advertiser.adbigscreenmanager_980", "年"),
    MARK_ADVERTISER_ADBIGSCREENMANAGER_1681("mark.advertiser.adbigscreenmanager_1681", "全国"),
    MARK_ADVERTISER_ADLEADDATAMANAGER_167("mark.advertiser.adleaddatamanager_167", "广告账户不存在"),
    MARK_ADVERTISER_ADLEADDATAMANAGER_173("mark.advertiser.adleaddatamanager_173", "广告相关的市场活动不存在"),
    MARK_OCPC_ADOCPCUPLOADMANAGER_822("mark.ocpc.adocpcuploadmanager_822", "创建"),
    MARK_OCPC_ADOCPCUPLOADMANAGER_822_1("mark.ocpc.adocpcuploadmanager_822_1", "对象:"),
    MARK_OCPC_ADOCPCUPLOADMANAGER_843("mark.ocpc.adocpcuploadmanager_843", "标签修改:"),
    MARK_OCPC_ADOCPCUPLOADMANAGER_847("mark.ocpc.adocpcuploadmanager_847", "更新为: "),
    MARK_OCPC_ADOCPCUPLOADMANAGER_868("mark.ocpc.adocpcuploadmanager_868", "系统自动生成"),
    MARK_OCPC_ADOCPCUPLOADMANAGER_1038("mark.ocpc.adocpcuploadmanager_1038", "未知"),
    MARK_BAIDU_CAMPAIGNDATAMANAGER_238("mark.baidu.campaigndatamanager_238", "异常: "),
    MARK_BAIDU_CAMPAIGNDATAMANAGER_255("mark.baidu.campaigndatamanager_255", "长时间处于刷新中"),
    MARK_MANAGER_BOARDMANAGER_193("mark.manager.boardmanager_193", "系统"),
    MARK_MANAGER_BOARDMANAGER_291("mark.manager.boardmanager_291", "SOP看板的ObjectId不能为空"),
    MARK_MANAGER_BOARDMANAGER_296("mark.manager.boardmanager_296", "此营销活动的SOP看板已存在"),
    MARK_MANAGER_BOARDMANAGER_315("mark.manager.boardmanager_315", "添加看板失败"),
    MARK_MANAGER_BOARDMANAGER_333("mark.manager.boardmanager_333", "插入数据失败"),
    MARK_MANAGER_BOARDMANAGER_957("mark.manager.boardmanager_957", "触发器'"),
    MARK_MANAGER_BOARDMANAGER_960("mark.manager.boardmanager_960", "'自动"),
    MARK_MANAGER_BOARDMANAGER_969("mark.manager.boardmanager_969", "创建了工作项:"),
    MARK_MANAGER_BOARDMANAGER_973("mark.manager.boardmanager_973", "更新了标题:"),
    MARK_MANAGER_BOARDMANAGER_977("mark.manager.boardmanager_977", "设置了工作项负责人:"),
    MARK_MANAGER_BOARDMANAGER_984("mark.manager.boardmanager_984", "更新了备注:"),
    MARK_MANAGER_BOARDMANAGER_987("mark.manager.boardmanager_987", "关联了市场活动:"),
    MARK_MANAGER_BOARDMANAGER_991("mark.manager.boardmanager_991", "关联了营销活动:"),
    MARK_MANAGER_BOARDMANAGER_995("mark.manager.boardmanager_995", "关联了社会化分销:"),
    MARK_MANAGER_BOARDMANAGER_999("mark.manager.boardmanager_999", "将工作项从'"),
    MARK_MANAGER_BOARDMANAGER_1001("mark.manager.boardmanager_1001", "'移动到'"),
    MARK_MANAGER_BOARDMANAGER_1006("mark.manager.boardmanager_1006", "设置了目标'"),
    MARK_MANAGER_BOARDMANAGER_1008("mark.manager.boardmanager_1008", "'为："),
    MARK_MANAGER_BOARDMANAGER_1012("mark.manager.boardmanager_1012", "创建了任务："),
    MARK_MANAGER_BOARDMANAGER_1016("mark.manager.boardmanager_1016", "完成了任务："),
    MARK_MANAGER_BOARDMANAGER_1020("mark.manager.boardmanager_1020", "删除了任务："),
    MARK_MANAGER_BOARDMANAGER_1024("mark.manager.boardmanager_1024", "重做了任务："),
    MARK_MANAGER_BOARDMANAGER_1028("mark.manager.boardmanager_1028", "认领了任务："),
    MARK_MANAGER_BOARDMANAGER_1032("mark.manager.boardmanager_1032", "修改了任务：'"),
    MARK_MANAGER_BOARDMANAGER_1034("mark.manager.boardmanager_1034", "'为'"),
    MARK_MANAGER_BOARDMANAGER_1038("mark.manager.boardmanager_1038", "更新任务'"),
    MARK_MANAGER_BOARDMANAGER_1040("mark.manager.boardmanager_1040", "'的开始时间为:"),
    MARK_MANAGER_BOARDMANAGER_1046("mark.manager.boardmanager_1046", "'的截止时间为:"),
    MARK_MANAGER_BOARDMANAGER_1050("mark.manager.boardmanager_1050", "添加了评论"),
    MARK_MANAGER_BOARDMANAGER_1053("mark.manager.boardmanager_1053", "把任务'"),
    MARK_MANAGER_BOARDMANAGER_1055("mark.manager.boardmanager_1055", "'指派给了 "),
    MARK_MANAGER_BOARDMANAGER_1064("mark.manager.boardmanager_1064", "创建了看板："),
    MARK_MANAGER_BOARDMANAGER_1068("mark.manager.boardmanager_1068", "删除了看板："),
    MARK_MANAGER_BOARDMANAGER_1071("mark.manager.boardmanager_1071", "完成了工作项'"),
    MARK_MANAGER_BOARDMANAGER_1074("mark.manager.boardmanager_1074", "重做了工作项'"),
    MARK_MANAGER_BOARDMANAGER_1153("mark.manager.boardmanager_1153", "看板"),
    MARK_MANAGER_BOARDMANAGER_1154("mark.manager.boardmanager_1154", "工作项"),
    MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_175("mark.cardtemplate.cardtemplatemanager_175", "默认模板"),
    MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_415("mark.cardtemplate.cardtemplatemanager_415", "咨询"),
    MARK_CARDTEMPLATE_CARDTEMPLATEMANAGER_466("mark.cardtemplate.cardtemplatemanager_466", "默认模板不可删除"),
    MARK_TEMPLATE_BLACKGOLDCARDTEMPLATE_116("mark.template.blackgoldcardtemplate_116", "Ta暂未提供姓名"),
    MARK_TEMPLATE_BLACKGOLDCARDTEMPLATE_122("mark.template.blackgoldcardtemplate_122", "Ta暂未提供职位信息"),
    MARK_TEMPLATE_BLACKGOLDCARDTEMPLATE_128("mark.template.blackgoldcardtemplate_128", "Ta暂未提供手机号"),
    MARK_TEMPLATE_BLACKGOLDCARDTEMPLATE_134("mark.template.blackgoldcardtemplate_134", "Ta暂未提供公司名称"),
    MARK_TEMPLATE_BLACKGOLDCARDTEMPLATE_163("mark.template.blackgoldcardtemplate_163", "Ta暂未提供公司地址"),
    MARK_TEMPLATE_BLACKGOLDCARDTEMPLATE_164("mark.template.blackgoldcardtemplate_164", "Ta暂未提供邮箱地址"),
    MARK_TEMPLATE_SIMPLECARDTEMPLATE_122("mark.template.simplecardtemplate_122", " /Ta暂未提供职位信息"),
    MARK_CONFERENCE_CONFERENCEMANAGER_996("mark.conference.conferencemanager_996", "暂无"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1071("mark.conference.conferencemanager_1071", "成员类型"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1072("mark.conference.conferencemanager_1072", "邀约人"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1073("mark.conference.conferencemanager_1073", "推广人"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1074("mark.conference.conferencemanager_1074", "业务负责人"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1075("mark.conference.conferencemanager_1075", "来源推广内容"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1076("mark.conference.conferencemanager_1076", "来源渠道"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1077("mark.conference.conferencemanager_1077", "邀约状态"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1078("mark.conference.conferencemanager_1078", "审核状态"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1079("mark.conference.conferencemanager_1079", "参会码"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1080("mark.conference.conferencemanager_1080", "签到状态"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1081("mark.conference.conferencemanager_1081", "签到时间"),
    MARK_CONFERENCE_CONFERENCEMANAGER_1082("mark.conference.conferencemanager_1082", "分组"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2682("mark.conference.conferencemanager_2682", "未找到首页模板"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2711("mark.conference.conferencemanager_2711", "未找到表单页模板"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2715("mark.conference.conferencemanager_2715", "未找到表单模板"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2718("mark.conference.conferencemanager_2718", "报名表单"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2759("mark.conference.conferencemanager_2759", "未找到成功页模板"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2765("mark.conference.conferencemanager_2765", "报名成功页"),
    MARK_CONFERENCE_CONFERENCEMANAGER_2847("mark.conference.conferencemanager_2847", "开始时间："),
    MARK_CONFERENCE_CONFERENCEMANAGER_2847_1("mark.conference.conferencemanager_2847_1", "地点: "),
    MARK_CONFERENCE_IMPORTMANAGER_625("mark.conference.importmanager_625", "错误原因"),
    MARK_CONFERENCE_IMPORTMANAGER_687("mark.conference.importmanager_687", "导入说明"),
    MARK_COUPON_PUBLICCOUPONMANAGER_804("mark.coupon.publiccouponmanager_804", "导入结果"),
    MARK_COUPON_PUBLICCOUPONMANAGER_816("mark.coupon.publiccouponmanager_816", "导入失败,查询不到该客户"),
    MARK_COUPON_PUBLICCOUPONMANAGER_822("mark.coupon.publiccouponmanager_822", "导入成功"),
    MARK_COUPON_PUBLICCOUPONMANAGER_833("mark.coupon.publiccouponmanager_833", "发送范围导入数据结果_"),
    MARK_COUPON_PUBLICCOUPONMANAGER_960("mark.coupon.publiccouponmanager_960", "领取门店导入数据结果_"),
    MARK_MANAGER_COVERIMAGEMANAGER_110("mark.manager.coverimagemanager_110", "时间："),
    MARK_MANAGER_COVERIMAGEMANAGER_110_1("mark.manager.coverimagemanager_110_1", "\n地点："),
    MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_119("mark.customizeformdata.resetcustomizeformdatamanager_119", "表单"),
    MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_279("mark.customizeformdata.resetcustomizeformdatamanager_279", "图片"),
    MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_325("mark.customizeformdata.resetcustomizeformdatamanager_325", "文本"),
    MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_1090("mark.customizeformdata.resetcustomizeformdatamanager_1090", "提交"),
    MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_1091("mark.customizeformdata.resetcustomizeformdatamanager_1091", "提交成功"),
    MARK_CUSTOMIZEFORMDATA_RESETCUSTOMIZEFORMDATAMANAGER_1252("mark.customizeformdata.resetcustomizeformdatamanager_1252", "我们会尽快联系您，请您耐心等候。"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572("mark.manager.customizeformdatamanager_572", "已报名审核中"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573("mark.manager.customizeformdatamanager_573", "报名审核未通过"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1677("mark.manager.customizeformdatamanager_1677", "企业微信群发"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1731("mark.manager.customizeformdatamanager_1731", "广告来源(utm_source)"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1732("mark.manager.customizeformdatamanager_1732", "广告媒介(utm_medium)"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1733("mark.manager.customizeformdatamanager_1733", "广告计划(utm_campaign)"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1734("mark.manager.customizeformdatamanager_1734", "广告内容(utm_content)"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1735("mark.manager.customizeformdatamanager_1735", "广告关键词(utm_term)"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1736("mark.manager.customizeformdatamanager_1736", "渠道类型"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1740("mark.manager.customizeformdatamanager_1740", "产品名称"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1741("mark.manager.customizeformdatamanager_1741", "支付金额"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1742("mark.manager.customizeformdatamanager_1742", "支付状态"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1743("mark.manager.customizeformdatamanager_1743", "交易单号"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1746("mark.manager.customizeformdatamanager_1746", "伙伴名称"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1750("mark.manager.customizeformdatamanager_1750", "提交时间"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_2328("mark.manager.customizeformdatamanager_2328", "系统错误！"),
    MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_3172("mark.manager.customizeformdatamanager_3172", "存入失败"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_389("mark.customizeformdatausermanager.querymultipleformuserdatamanager_389", "请输入姓名"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_400("mark.customizeformdatausermanager.querymultipleformuserdatamanager_400", "请输入手机号"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_565("mark.customizeformdatausermanager.querymultipleformuserdatamanager_565", "推广内容"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_579("mark.customizeformdatausermanager.querymultipleformuserdatamanager_579", "订单id"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_580("mark.customizeformdatausermanager.querymultipleformuserdatamanager_580", "订单状态"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_581("mark.customizeformdatausermanager.querymultipleformuserdatamanager_581", "订单金额"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_586("mark.customizeformdatausermanager.querymultipleformuserdatamanager_586", "是否观看"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594("mark.customizeformdatausermanager.querymultipleformuserdatamanager_594", "手机号"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_642("mark.customizeformdatausermanager.querymultipleformuserdatamanager_642", "未观看"),
    MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_644("mark.customizeformdatausermanager.querymultipleformuserdatamanager_644", "已观看"),
    MARK_MANAGER_CUSTOMIZEMINAPPNAVBARMANAGER_18("mark.manager.customizeminappnavbarmanager_18", "官网"),
    MARK_MANAGER_CUSTOMIZEMINAPPNAVBARMANAGER_19("mark.manager.customizeminappnavbarmanager_19", "公司动态"),
    MARK_MANAGER_CUSTOMIZEMINAPPNAVBARMANAGER_20("mark.manager.customizeminappnavbarmanager_20", "微页面"),
    MARK_MANAGER_CUSTOMIZEMINAPPNAVBARMANAGER_21("mark.manager.customizeminappnavbarmanager_21", "直播"),
    MARK_DISTRIBUTION_DISTRIBUTORMANAGER_271("mark.distribution.distributormanager_271", "初级分销员"),
    MARK_MANAGER_EMPLOYEEMSGSENDER_66("mark.manager.employeemsgsender_66", "查看详情"),
    MARK_MANAGER_FILEMANAGER_252("mark.manager.filemanager_252", "客脉文件夹"),
    MARK_MANAGER_FSMESSAGEMANAGER_191("mark.manager.fsmessagemanager_191", "请登录至营销通管理后台，前往该活动检查活动参与人员并进行处理"),
    MARK_MANAGER_FSMESSAGEMANAGER_296("mark.manager.fsmessagemanager_296", "报名审核通知"),
    MARK_MANAGER_FSMESSAGEMANAGER_313("mark.manager.fsmessagemanager_313", "点击进入报名人员审核列表进行快速审核"),
    MARK_MANAGER_FSMESSAGEMANAGER_332("mark.manager.fsmessagemanager_332", "管理员已为您开通微信名片，快来体验吧。"),
    MARK_MANAGER_FSMESSAGEMANAGER_340("mark.manager.fsmessagemanager_340", "管理员"),
    MARK_MANAGER_FSMESSAGEMANAGER_340_1("mark.manager.fsmessagemanager_340_1", "已为您开通微信名片，快来体验吧。"),
    MARK_MANAGER_FSMESSAGEMANAGER_343("mark.manager.fsmessagemanager_343", "马上体验"),
    MARK_MANAGER_FSMESSAGEMANAGER_547("mark.manager.fsmessagemanager_547", "阅读全文"),
    MARK_MATERIAL_CARDPOSTER_147("mark.material.cardposter_147", "扫一扫小程序二维码，查看名片"),
    MARK_MATERIAL_MATERIALPOSTER_121("mark.material.materialposter_121", "推荐了"),
    MARK_MATERIAL_MATERIALPOSTER_123("mark.material.materialposter_123", "产品"),
    MARK_MATERIAL_MATERIALPOSTER_125("mark.material.materialposter_125", "文章"),
    MARK_MATERIAL_MATERIALPOSTER_127("mark.material.materialposter_127", "会议"),
    MARK_MATERIAL_MATERIALPOSTER_162("mark.material.materialposter_162", "长按识别小程序码"),
    MARK_MATERIAL_MATERIALPOSTER_166("mark.material.materialposter_166", "查看产品详情"),
    MARK_MATERIAL_MATERIALPOSTER_168("mark.material.materialposter_168", "进入阅读全文"),
    MARK_MATERIAL_MATERIALPOSTER_170("mark.material.materialposter_170", "查看会议详情"),
    MARK_MAIL_SENDCLOUDERROR_10("mark.mail.sendclouderror_10", "请求成功"),
    MARK_MAIL_SENDCLOUDERROR_11("mark.mail.sendclouderror_11", "start不能为空"),
    MARK_MAIL_SENDCLOUDERROR_12("mark.mail.sendclouderror_12", "start非法"),
    MARK_MAIL_SENDCLOUDERROR_13("mark.mail.sendclouderror_13", "limit不能为空"),
    MARK_MAIL_SENDCLOUDERROR_14("mark.mail.sendclouderror_14", "limit非法"),
    MARK_MAIL_SENDCLOUDERROR_15("mark.mail.sendclouderror_15", "认证失败"),
    MARK_MAIL_SENDCLOUDERROR_16("mark.mail.sendclouderror_16", "days格式非法,必须是大于0的正整数"),
    MARK_MAIL_SENDCLOUDERROR_17("mark.mail.sendclouderror_17", "startDate格式错误,应该类似'2013-03-19'"),
    MARK_MAIL_SENDCLOUDERROR_18("mark.mail.sendclouderror_18", "endDate格式错误,应该类似'2013-03-19'"),
    MARK_MAIL_SENDCLOUDERROR_20("mark.mail.sendclouderror_20", "apiUserList不能为空"),
    MARK_MAIL_SENDCLOUDERROR_21("mark.mail.sendclouderror_21", "email不能为空"),
    MARK_MAIL_SENDCLOUDERROR_22("mark.mail.sendclouderror_22", "email格式非法"),
    MARK_MAIL_SENDCLOUDERROR_23("mark.mail.sendclouderror_23", "domainList不能为空"),
    MARK_MAIL_SENDCLOUDERROR_24("mark.mail.sendclouderror_24", "标签ID不能为空"),
    MARK_MAIL_SENDCLOUDERROR_25("mark.mail.sendclouderror_25", "标签ID格式错误"),
    MARK_MAIL_SENDCLOUDERROR_26("mark.mail.sendclouderror_26", "apiUserList格式非法"),
    MARK_MAIL_SENDCLOUDERROR_27("mark.mail.sendclouderror_27", "聚合参数格式错误"),
    MARK_MAIL_SENDCLOUDERROR_28("mark.mail.sendclouderror_28", "标签创建成功"),
    MARK_MAIL_SENDCLOUDERROR_29("mark.mail.sendclouderror_29", "标签创建失败"),
    MARK_MAIL_SENDCLOUDERROR_31("mark.mail.sendclouderror_31", "标签ID非法"),
    MARK_MAIL_SENDCLOUDERROR_32("mark.mail.sendclouderror_32", "标签名称不能为空"),
    MARK_MAIL_SENDCLOUDERROR_33("mark.mail.sendclouderror_33", "标签名称的长度应该为1-255个字符"),
    MARK_MAIL_SENDCLOUDERROR_34("mark.mail.sendclouderror_34", "标签ID对应的标签不存在"),
    MARK_MAIL_SENDCLOUDERROR_35("mark.mail.sendclouderror_35", "标签删除成功"),
    MARK_MAIL_SENDCLOUDERROR_36("mark.mail.sendclouderror_36", "标签删除失败"),
    MARK_MAIL_SENDCLOUDERROR_37("mark.mail.sendclouderror_37", "标签更新成功"),
    MARK_MAIL_SENDCLOUDERROR_39("mark.mail.sendclouderror_39", "query不能为空"),
    MARK_MAIL_SENDCLOUDERROR_40("mark.mail.sendclouderror_40", "query的长度的长度应该为1-255个字符"),
    MARK_MAIL_SENDCLOUDERROR_41("mark.mail.sendclouderror_41", "标签名称已经存在"),
    MARK_MAIL_SENDCLOUDERROR_42("mark.mail.sendclouderror_42", "模版调用名称invokeName不能为空"),
    MARK_MAIL_SENDCLOUDERROR_43("mark.mail.sendclouderror_43", "模版调用名称invokeName格式错误"),
    MARK_MAIL_SENDCLOUDERROR_44("mark.mail.sendclouderror_44", "模版类型不能为空"),
    MARK_MAIL_SENDCLOUDERROR_45("mark.mail.sendclouderror_45", "非法的模板类型, 只能是0或者1"),
    MARK_MAIL_SENDCLOUDERROR_46("mark.mail.sendclouderror_46", "templateStat不能为空"),
    MARK_MAIL_SENDCLOUDERROR_47("mark.mail.sendclouderror_47", "templateStat非法, 只能是-1, -2, 1, 0中的值"),
    MARK_MAIL_SENDCLOUDERROR_48("mark.mail.sendclouderror_48", "name不能为空"),
    MARK_MAIL_SENDCLOUDERROR_49("mark.mail.sendclouderror_49", "name格式非法"),
    MARK_MAIL_SENDCLOUDERROR_50("mark.mail.sendclouderror_50", "subject不能为空"),
    MARK_MAIL_SENDCLOUDERROR_51("mark.mail.sendclouderror_51", "subject格式非法"),
    MARK_MAIL_SENDCLOUDERROR_52("mark.mail.sendclouderror_52", "html不能为空"),
    MARK_MAIL_SENDCLOUDERROR_53("mark.mail.sendclouderror_53", "html格式非法"),
    MARK_MAIL_SENDCLOUDERROR_54("mark.mail.sendclouderror_54", "text不能为空"),
    MARK_MAIL_SENDCLOUDERROR_55("mark.mail.sendclouderror_55", "text格式非法"),
    MARK_MAIL_SENDCLOUDERROR_56("mark.mail.sendclouderror_56", "模板创建失败"),
    MARK_MAIL_SENDCLOUDERROR_57("mark.mail.sendclouderror_57", "模板调用名称对应的模板不存在"),
    MARK_MAIL_SENDCLOUDERROR_58("mark.mail.sendclouderror_58", "模板删除失败"),
    MARK_MAIL_SENDCLOUDERROR_59("mark.mail.sendclouderror_59", "模板更新失败"),
    MARK_MAIL_SENDCLOUDERROR_60("mark.mail.sendclouderror_60", "用户最多只能有50个模板"),
    MARK_MAIL_SENDCLOUDERROR_61("mark.mail.sendclouderror_61", "模版调用名称已经存在"),
    MARK_MAIL_SENDCLOUDERROR_62("mark.mail.sendclouderror_62", "isSubmitAudit不能为空"),
    MARK_MAIL_SENDCLOUDERROR_63("mark.mail.sendclouderror_63", "isSubmitAudit格式错误"),
    MARK_MAIL_SENDCLOUDERROR_64("mark.mail.sendclouderror_64", "模板处于待审核状态, 不能修改"),
    MARK_MAIL_SENDCLOUDERROR_65("mark.mail.sendclouderror_65", "cancel不能为空"),
    MARK_MAIL_SENDCLOUDERROR_66("mark.mail.sendclouderror_66", "cancel格式错误"),
    MARK_MAIL_SENDCLOUDERROR_67("mark.mail.sendclouderror_67", "模板处于待审核状态, 无需再次提交"),
    MARK_MAIL_SENDCLOUDERROR_68("mark.mail.sendclouderror_68", "模板已经审核通过, 无需再次提交"),
    MARK_MAIL_SENDCLOUDERROR_69("mark.mail.sendclouderror_69", "模板处于审核失败状态, 无需撤销审核"),
    MARK_MAIL_SENDCLOUDERROR_70("mark.mail.sendclouderror_70", "模板还未提交审核, 无法撤销审核"),
    MARK_MAIL_SENDCLOUDERROR_71("mark.mail.sendclouderror_71", "用户不存在"),
    MARK_MAIL_SENDCLOUDERROR_72("mark.mail.sendclouderror_72", "取消订阅记录创建成功"),
    MARK_MAIL_SENDCLOUDERROR_73("mark.mail.sendclouderror_73", "取消订阅记录创建失败"),
    MARK_MAIL_SENDCLOUDERROR_74("mark.mail.sendclouderror_74", "取消订阅记录删除成功"),
    MARK_MAIL_SENDCLOUDERROR_75("mark.mail.sendclouderror_75", "取消订阅记录删除失败"),
    MARK_MAIL_SENDCLOUDERROR_77("mark.mail.sendclouderror_77", "地址列表名称的长度应该为1-48个字符"),
    MARK_MAIL_SENDCLOUDERROR_78("mark.mail.sendclouderror_78", "address不能为空"),
    MARK_MAIL_SENDCLOUDERROR_79("mark.mail.sendclouderror_79", "地址列表别名的长度应该为1-48个字符"),
    MARK_MAIL_SENDCLOUDERROR_80("mark.mail.sendclouderror_80", "地址列表别名已经存在"),
    MARK_MAIL_SENDCLOUDERROR_81("mark.mail.sendclouderror_81", "desc不能为空"),
    MARK_MAIL_SENDCLOUDERROR_82("mark.mail.sendclouderror_82", "地址列表描述的长度应该为1-250个字符"),
    MARK_MAIL_SENDCLOUDERROR_83("mark.mail.sendclouderror_83", "地址列表创建失败"),
    MARK_MAIL_SENDCLOUDERROR_84("mark.mail.sendclouderror_84", "newAddress不能为空"),
    MARK_MAIL_SENDCLOUDERROR_85("mark.mail.sendclouderror_85", "新的地址列表别名的长度应该为1-48个字符"),
    MARK_MAIL_SENDCLOUDERROR_86("mark.mail.sendclouderror_86", "address参数错误"),
    MARK_MAIL_SENDCLOUDERROR_87("mark.mail.sendclouderror_87", "members不能为空"),
    MARK_MAIL_SENDCLOUDERROR_88("mark.mail.sendclouderror_88", "成员地址的长度应该为1-48个字符"),
    MARK_MAIL_SENDCLOUDERROR_89("mark.mail.sendclouderror_89", "成员地址的个数不能小于0"),
    MARK_MAIL_SENDCLOUDERROR_90("mark.mail.sendclouderror_90", "成员地址的个数不能超过1000"),
    MARK_MAIL_SENDCLOUDERROR_91("mark.mail.sendclouderror_91", "添加成员失败"),
    MARK_MAIL_SENDCLOUDERROR_92("mark.mail.sendclouderror_92", "地址列表不属于此用户"),
    MARK_MAIL_SENDCLOUDERROR_93("mark.mail.sendclouderror_93", "成员地址不符合邮件地址规范"),
    MARK_MAIL_SENDCLOUDERROR_94("mark.mail.sendclouderror_94", "删除成员失败"),
    MARK_MAIL_SENDCLOUDERROR_95("mark.mail.sendclouderror_95", "vars不能为空"),
    MARK_MAIL_SENDCLOUDERROR_96("mark.mail.sendclouderror_96", "vars参数中变量个数和成员地址个数不相等"),
    MARK_MAIL_SENDCLOUDERROR_97("mark.mail.sendclouderror_97", "vars参数不符合JSON字符串语法"),
    MARK_MAIL_SENDCLOUDERROR_98("mark.mail.sendclouderror_98", "退信记录删除成功"),
    MARK_MAIL_SENDCLOUDERROR_99("mark.mail.sendclouderror_99", "退信记录删除失败"),
    MARK_MAIL_SENDCLOUDERROR_100("mark.mail.sendclouderror_100", "邮箱地址已经存在"),
    MARK_MAIL_SENDCLOUDERROR_101("mark.mail.sendclouderror_101", "过期时间格式为： 2018-03-19"),
    MARK_MAIL_SENDCLOUDERROR_102("mark.mail.sendclouderror_102", "分组ID不能为空"),
    MARK_MAIL_SENDCLOUDERROR_103("mark.mail.sendclouderror_103", "分组ID格式错误"),
    MARK_MAIL_SENDCLOUDERROR_104("mark.mail.sendclouderror_104", "事件类型不能为空"),
    MARK_MAIL_SENDCLOUDERROR_105("mark.mail.sendclouderror_105", "事件类型格式错误,没有可用的事件类型"),
    MARK_MAIL_SENDCLOUDERROR_106("mark.mail.sendclouderror_106", "url不能为空"),
    MARK_MAIL_SENDCLOUDERROR_107("mark.mail.sendclouderror_107", "url格式错误"),
    MARK_MAIL_SENDCLOUDERROR_108("mark.mail.sendclouderror_108", "url测试失败"),
    MARK_MAIL_SENDCLOUDERROR_109("mark.mail.sendclouderror_109", "url已存在"),
    MARK_MAIL_SENDCLOUDERROR_110("mark.mail.sendclouderror_110", "groupId对应的webhook配置未找到"),
    MARK_MAIL_SENDCLOUDERROR_111("mark.mail.sendclouderror_111", "webhook配置创建失败"),
    MARK_MAIL_SENDCLOUDERROR_112("mark.mail.sendclouderror_112", "webhook配置删除失败"),
    MARK_MAIL_SENDCLOUDERROR_113("mark.mail.sendclouderror_113", "webhook配置修改失败"),
    MARK_MAIL_SENDCLOUDERROR_114("mark.mail.sendclouderror_114", "发信人地址from不能为空"),
    MARK_MAIL_SENDCLOUDERROR_115("mark.mail.sendclouderror_115", "发信人地址from格式错误"),
    MARK_MAIL_SENDCLOUDERROR_116("mark.mail.sendclouderror_116", "发信人名称fromName不能为空"),
    MARK_MAIL_SENDCLOUDERROR_117("mark.mail.sendclouderror_117", "发信人名称fromName格式错误"),
    MARK_MAIL_SENDCLOUDERROR_118("mark.mail.sendclouderror_118", "收件人地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_119("mark.mail.sendclouderror_119", "收件人地址数组中, 存在非法地址"),
    MARK_MAIL_SENDCLOUDERROR_120("mark.mail.sendclouderror_120", "收件人地址的数目不能超过100"),
    MARK_MAIL_SENDCLOUDERROR_121("mark.mail.sendclouderror_121", "邮件主题subject不能为空"),
    MARK_MAIL_SENDCLOUDERROR_122("mark.mail.sendclouderror_122", "邮件主题subject格式错误"),
    MARK_MAIL_SENDCLOUDERROR_123("mark.mail.sendclouderror_123", "回复地址replyto不能为空"),
    MARK_MAIL_SENDCLOUDERROR_124("mark.mail.sendclouderror_124", "回复地址replyto格式错误"),
    MARK_MAIL_SENDCLOUDERROR_125("mark.mail.sendclouderror_125", "xsmtpapi不能为空"),
    MARK_MAIL_SENDCLOUDERROR_126("mark.mail.sendclouderror_126", "xsmtpapi格式错误"),
    MARK_MAIL_SENDCLOUDERROR_127("mark.mail.sendclouderror_127", "xsmtpapi解析值不能为空"),
    MARK_MAIL_SENDCLOUDERROR_128("mark.mail.sendclouderror_128", "xsmtpapi必须含有to字段"),
    MARK_MAIL_SENDCLOUDERROR_129("mark.mail.sendclouderror_129", "xsmtpapi中to字段的解析值不能为空"),
    MARK_MAIL_SENDCLOUDERROR_130("mark.mail.sendclouderror_130", "xsmtpapi解析错误"),
    MARK_MAIL_SENDCLOUDERROR_131("mark.mail.sendclouderror_131", "attachments不能为空"),
    MARK_MAIL_SENDCLOUDERROR_132("mark.mail.sendclouderror_132", "附件大小不能超过10485760字节"),
    MARK_MAIL_SENDCLOUDERROR_133("mark.mail.sendclouderror_133", "此用户没有使用地址列表的权限"),
    MARK_MAIL_SENDCLOUDERROR_134("mark.mail.sendclouderror_134", "地址列表任务创建成功"),
    MARK_MAIL_SENDCLOUDERROR_135("mark.mail.sendclouderror_135", "地址列表任务创建失败"),
    MARK_MAIL_SENDCLOUDERROR_136("mark.mail.sendclouderror_136", "邮件模板不存在"),
    MARK_MAIL_SENDCLOUDERROR_137("mark.mail.sendclouderror_137", "模板未通过审核"),
    MARK_MAIL_SENDCLOUDERROR_138("mark.mail.sendclouderror_138", "邮件模板和API_USER类型不匹配"),
    MARK_MAIL_SENDCLOUDERROR_139("mark.mail.sendclouderror_139", "参数subject和模板主题不能同时为空"),
    MARK_MAIL_SENDCLOUDERROR_140("mark.mail.sendclouderror_140", "xsmtpapi中to数组长度不能超过100"),
    MARK_MAIL_SENDCLOUDERROR_141("mark.mail.sendclouderror_141", "回执地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_142("mark.mail.sendclouderror_142", "回执地址格式错误"),
    MARK_MAIL_SENDCLOUDERROR_143("mark.mail.sendclouderror_143", "plain内容不能为空"),
    MARK_MAIL_SENDCLOUDERROR_144("mark.mail.sendclouderror_144", "plain内容格式错误"),
    MARK_MAIL_SENDCLOUDERROR_145("mark.mail.sendclouderror_145", "会议起始时间startTime不能为空"),
    MARK_MAIL_SENDCLOUDERROR_146("mark.mail.sendclouderror_146", "会议起始时间startTime格式错误"),
    MARK_MAIL_SENDCLOUDERROR_147("mark.mail.sendclouderror_147", "会议结束时间endTime不能为空"),
    MARK_MAIL_SENDCLOUDERROR_148("mark.mail.sendclouderror_148", "会议结束时间endTime格式错误"),
    MARK_MAIL_SENDCLOUDERROR_149("mark.mail.sendclouderror_149", "会议标题title不能为空"),
    MARK_MAIL_SENDCLOUDERROR_150("mark.mail.sendclouderror_150", "会议标题title格式错误"),
    MARK_MAIL_SENDCLOUDERROR_151("mark.mail.sendclouderror_151", "会议组织者名称不能为空"),
    MARK_MAIL_SENDCLOUDERROR_152("mark.mail.sendclouderror_152", "会议组织者名称格式错误"),
    MARK_MAIL_SENDCLOUDERROR_153("mark.mail.sendclouderror_153", "会议组织者邮件地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_154("mark.mail.sendclouderror_154", "会议组织者邮件地址格式错误"),
    MARK_MAIL_SENDCLOUDERROR_155("mark.mail.sendclouderror_155", "会议地点location不能为空"),
    MARK_MAIL_SENDCLOUDERROR_156("mark.mail.sendclouderror_156", "会议地点location格式错误"),
    MARK_MAIL_SENDCLOUDERROR_157("mark.mail.sendclouderror_157", "会议描述description不能为空"),
    MARK_MAIL_SENDCLOUDERROR_158("mark.mail.sendclouderror_158", "会议描述description格式错误"),
    MARK_MAIL_SENDCLOUDERROR_159("mark.mail.sendclouderror_159", "会议参与者名称不能为空"),
    MARK_MAIL_SENDCLOUDERROR_160("mark.mail.sendclouderror_160", "会议参与者名称格式不对"),
    MARK_MAIL_SENDCLOUDERROR_161("mark.mail.sendclouderror_161", "会议参与者邮件地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_162("mark.mail.sendclouderror_162", "会议参与者邮件地址格式错误"),
    MARK_MAIL_SENDCLOUDERROR_163("mark.mail.sendclouderror_163", "会议参与者名称个数和会议参与者邮件地址个数不相等"),
    MARK_MAIL_SENDCLOUDERROR_164("mark.mail.sendclouderror_164", "会议邮件拼装失败"),
    MARK_MAIL_SENDCLOUDERROR_165("mark.mail.sendclouderror_165", "cc地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_166("mark.mail.sendclouderror_166", "cc地址格式错误"),
    MARK_MAIL_SENDCLOUDERROR_167("mark.mail.sendclouderror_167", "CC地址的数目不能超过100"),
    MARK_MAIL_SENDCLOUDERROR_168("mark.mail.sendclouderror_168", "bcc地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_169("mark.mail.sendclouderror_169", "bcc地址格式错误"),
    MARK_MAIL_SENDCLOUDERROR_170("mark.mail.sendclouderror_170", "BCC地址的数目不能超过100"),
    MARK_MAIL_SENDCLOUDERROR_171("mark.mail.sendclouderror_171", "respEmailId不能为空"),
    MARK_MAIL_SENDCLOUDERROR_172("mark.mail.sendclouderror_172", "respEmailId格式错误"),
    MARK_MAIL_SENDCLOUDERROR_173("mark.mail.sendclouderror_173", "gzipCompress不能为空"),
    MARK_MAIL_SENDCLOUDERROR_174("mark.mail.sendclouderror_174", "gzipCompress格式错误"),
    MARK_MAIL_SENDCLOUDERROR_175("mark.mail.sendclouderror_175", "to中有格式错误的地址列表"),
    MARK_MAIL_SENDCLOUDERROR_176("mark.mail.sendclouderror_176", "to中有不存在的地址列表"),
    MARK_MAIL_SENDCLOUDERROR_177("mark.mail.sendclouderror_177", "地址列表的数目不能超过5"),
    MARK_MAIL_SENDCLOUDERROR_178("mark.mail.sendclouderror_178", "html解压失败"),
    MARK_MAIL_SENDCLOUDERROR_179("mark.mail.sendclouderror_179", "plain解压失败"),
    MARK_MAIL_SENDCLOUDERROR_180("mark.mail.sendclouderror_180", "处理附件发生异常"),
    MARK_MAIL_SENDCLOUDERROR_181("mark.mail.sendclouderror_181", "headers不能为空"),
    MARK_MAIL_SENDCLOUDERROR_182("mark.mail.sendclouderror_182", "headers格式错误"),
    MARK_MAIL_SENDCLOUDERROR_183("mark.mail.sendclouderror_183", "html和plain不能同时为空"),
    MARK_MAIL_SENDCLOUDERROR_184("mark.mail.sendclouderror_184", "html格式错误"),
    MARK_MAIL_SENDCLOUDERROR_185("mark.mail.sendclouderror_185", "邮件列表地址不能为空"),
    MARK_MAIL_SENDCLOUDERROR_186("mark.mail.sendclouderror_186", "useAddressList不能为空"),
    MARK_MAIL_SENDCLOUDERROR_187("mark.mail.sendclouderror_187", "useAddressList格式错误"),
    MARK_MAIL_SENDCLOUDERROR_188("mark.mail.sendclouderror_188", "内嵌图片ID或内嵌图片附件长度不一致"),
    MARK_MAIL_SENDCLOUDERROR_189("mark.mail.sendclouderror_189", "是否取消日程参数isCancel格式错误"),
    MARK_MAIL_SENDCLOUDERROR_190("mark.mail.sendclouderror_190", "摘要不能为空"),
    MARK_MAIL_SENDCLOUDERROR_191("mark.mail.sendclouderror_191", "摘要长度不能超过200个字节"),
    MARK_MAIL_SENDCLOUDERROR_192("mark.mail.sendclouderror_192", "回复地址replyto个数不能超过3个"),
    MARK_MAIL_SENDCLOUDERROR_193("mark.mail.sendclouderror_193", "xsmtpapi中to字段含有非法邮箱格式"),
    MARK_MAIL_SENDCLOUDERROR_194("mark.mail.sendclouderror_194", "邮件发送失败."),
    MARK_MAIL_SENDCLOUDERROR_195("mark.mail.sendclouderror_195", "邮件处理发生未知异常"),
    MARK_MAIL_SENDCLOUDERROR_196("mark.mail.sendclouderror_196", "邮件发送成功"),
    MARK_MAIL_SENDCLOUDERROR_197("mark.mail.sendclouderror_197", "额度检查失败"),
    MARK_MAIL_SENDCLOUDERROR_198("mark.mail.sendclouderror_198", "额度检查通过"),
    MARK_MAIL_SENDCLOUDERROR_199("mark.mail.sendclouderror_199", "额度检查临时通过"),
    MARK_MAIL_SENDCLOUDERROR_200("mark.mail.sendclouderror_200", "该API_USER对应的内容不需要进行模板匹配"),
    MARK_MAIL_SENDCLOUDERROR_201("mark.mail.sendclouderror_201", "邮件内容和邮件模板匹配不通过"),
    MARK_MAIL_SENDCLOUDERROR_202("mark.mail.sendclouderror_202", "邮件内容和邮件模板匹配通过"),
    MARK_MAIL_SENDCLOUDERROR_203("mark.mail.sendclouderror_203", "邮件内容和邮件模板匹配临时通过"),
    MARK_MAIL_SENDCLOUDERROR_204("mark.mail.sendclouderror_204", "邮件内容和邮件模板匹配时出现编码错误"),
    MARK_MAIL_SENDCLOUDERROR_205("mark.mail.sendclouderror_205", "name不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_206("mark.mail.sendclouderror_206", "name的长度应该为1-250个字符"),
    MARK_MAIL_SENDCLOUDERROR_207("mark.mail.sendclouderror_207", "name不符合域名规则"),
    MARK_MAIL_SENDCLOUDERROR_208("mark.mail.sendclouderror_208", "newName不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_209("mark.mail.sendclouderror_209", "newName的长度应该为1-250个字符"),
    MARK_MAIL_SENDCLOUDERROR_210("mark.mail.sendclouderror_210", "newName不符合域名规则"),
    MARK_MAIL_SENDCLOUDERROR_211("mark.mail.sendclouderror_211", "type不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_212("mark.mail.sendclouderror_212", "type不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_213("mark.mail.sendclouderror_213", "verify不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_214("mark.mail.sendclouderror_214", "verify不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_215("mark.mail.sendclouderror_215", "verify解析错误"),
    MARK_MAIL_SENDCLOUDERROR_216("mark.mail.sendclouderror_216", "用户创建域名不能超过5个"),
    MARK_MAIL_SENDCLOUDERROR_217("mark.mail.sendclouderror_217", "name参数错误, 多个域名"),
    MARK_MAIL_SENDCLOUDERROR_218("mark.mail.sendclouderror_218", "域名不存在"),
    MARK_MAIL_SENDCLOUDERROR_219("mark.mail.sendclouderror_219", "domain创建失败"),
    MARK_MAIL_SENDCLOUDERROR_220("mark.mail.sendclouderror_220", "domain修改失败"),
    MARK_MAIL_SENDCLOUDERROR_221("mark.mail.sendclouderror_221", "emailType不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_222("mark.mail.sendclouderror_222", "emailType不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_223("mark.mail.sendclouderror_223", "cType不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_224("mark.mail.sendclouderror_224", "cType不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_225("mark.mail.sendclouderror_225", "domainName不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_226("mark.mail.sendclouderror_226", "domainName不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_227("mark.mail.sendclouderror_227", "domainName的长度应该为1-250个字符"),
    MARK_MAIL_SENDCLOUDERROR_228("mark.mail.sendclouderror_228", "domainName所属的域名不存在"),
    MARK_MAIL_SENDCLOUDERROR_229("mark.mail.sendclouderror_229", "用户信息不存在"),
    MARK_MAIL_SENDCLOUDERROR_231("mark.mail.sendclouderror_231", "name不符合规则, name的长度为6-32的字符串, 只能含有(A-Z,a-z,0-9,_)"),
    MARK_MAIL_SENDCLOUDERROR_232("mark.mail.sendclouderror_232", "apiUser不能超过10个"),
    MARK_MAIL_SENDCLOUDERROR_233("mark.mail.sendclouderror_233", "open不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_234("mark.mail.sendclouderror_234", "open不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_235("mark.mail.sendclouderror_235", "click不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_236("mark.mail.sendclouderror_236", "click不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_237("mark.mail.sendclouderror_237", "unsubscribe不能为空串"),
    MARK_MAIL_SENDCLOUDERROR_238("mark.mail.sendclouderror_238", "unsubscribe不符合规则"),
    MARK_MAIL_SENDCLOUDERROR_239("mark.mail.sendclouderror_239", "apiUser创建失败"),
    MARK_MAIL_SENDCLOUDERROR_241("mark.mail.sendclouderror_241", "http请求执行异常"),
    MARK_MAIL_SENDCLOUDERROR_242("mark.mail.sendclouderror_242", "http请求执行失败"),
    MARK_MAIL_SENDCLOUDERROR_243("mark.mail.sendclouderror_243", "http请求执行成功"),
    MARK_MAIL_SENDCLOUDERROR_244("mark.mail.sendclouderror_244", "http返回结果解析错误"),
    MARK_MAIL_SENDCLOUDERROR_245("mark.mail.sendclouderror_245", "http其他错误"),
    MARK_MAIL_SENDCLOUDERROR_246("mark.mail.sendclouderror_246", "接口频率受限(每个apiuser,每个接口、每分钟调用4000次，目前只限制投递回应)"),
    MARK_MAIL_SENDCLOUDERROR_247("mark.mail.sendclouderror_247", "邮件发送失败.536 Frequency limited（每个apiuser每分钟调用不能超过2万次（api和smtp总共））"),
    MARK_MAIL_SENDCLOUDERROR_248("mark.mail.sendclouderror_248", "sender不能为空"),
    MARK_MAIL_SENDCLOUDERROR_249("mark.mail.sendclouderror_249", "sender长度不能超过250字符"),
    MARK_MAIL_SENDCLOUDERROR_250("mark.mail.sendclouderror_250", "sender前缀不能包含@符号"),
    MARK_MAIL_SENDCLOUDERROR_251("mark.mail.sendclouderror_251", "domain长度不能超过250字符"),
    MARK_MAIL_SENDCLOUDERROR_252("mark.mail.sendclouderror_252", "指定删除数据不存在"),
    MARK_MAIL_SENDCLOUDERROR_253("mark.mail.sendclouderror_253", "删除成功"),
    MARK_MAIL_SENDCLOUDERROR_254("mark.mail.sendclouderror_254", "categoryName不能为空"),
    MARK_MAIL_SENDCLOUDERROR_255("mark.mail.sendclouderror_255", "服务器异常"),
    MARK_MAIL_SENDCLOUDERROR_256("mark.mail.sendclouderror_256", "你没有权限访问为空串"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_584("mark.marketingactivity.qywxgroupsendmanager_584", "全部企业微信客户"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_586("mark.marketingactivity.qywxgroupsendmanager_586", "按标签和条件筛选"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_708("mark.marketingactivity.qywxgroupsendmanager_708", "微信昵称"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_720("mark.marketingactivity.qywxgroupsendmanager_720", "外部联系人ID"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_745("mark.marketingactivity.qywxgroupsendmanager_745", "来源二维码名称"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_748("mark.marketingactivity.qywxgroupsendmanager_748", "来源二维码ID"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_751("mark.marketingactivity.qywxgroupsendmanager_751", "微信UnionId"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_782("mark.marketingactivity.qywxgroupsendmanager_782", " 且 "),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_884("mark.marketingactivity.qywxgroupsendmanager_884", "锁定"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_886("mark.marketingactivity.qywxgroupsendmanager_886", "未锁定"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_888("mark.marketingactivity.qywxgroupsendmanager_888", "其他"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_903("mark.marketingactivity.qywxgroupsendmanager_903", "吸粉二维码"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_905("mark.marketingactivity.qywxgroupsendmanager_905", "员工添加"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_925("mark.marketingactivity.qywxgroupsendmanager_925", "及子部门"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_938("mark.marketingactivity.qywxgroupsendmanager_938", "正常"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_940("mark.marketingactivity.qywxgroupsendmanager_940", "已删除"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_957("mark.marketingactivity.qywxgroupsendmanager_957", "营销通"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_972("mark.marketingactivity.qywxgroupsendmanager_972", "女"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_974("mark.marketingactivity.qywxgroupsendmanager_974", "男"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_991("mark.marketingactivity.qywxgroupsendmanager_991", "微信客户"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_993("mark.marketingactivity.qywxgroupsendmanager_993", "企业微信客户"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1025("mark.marketingactivity.qywxgroupsendmanager_1025", "USD-美元"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1027("mark.marketingactivity.qywxgroupsendmanager_1027", "CNY-人民币"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1029("mark.marketingactivity.qywxgroupsendmanager_1029", "ALL-阿尔巴尼亚列克"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1031("mark.marketingactivity.qywxgroupsendmanager_1031", "AUD-澳元"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1033("mark.marketingactivity.qywxgroupsendmanager_1033", "AED-阿联酋迪拉姆"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1035("mark.marketingactivity.qywxgroupsendmanager_1035", "EUR欧元"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1050("mark.marketingactivity.qywxgroupsendmanager_1050", "未生效"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1052("mark.marketingactivity.qywxgroupsendmanager_1052", "审核中"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1056("mark.marketingactivity.qywxgroupsendmanager_1056", "变更中"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1058("mark.marketingactivity.qywxgroupsendmanager_1058", "作废"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1117("mark.marketingactivity.qywxgroupsendmanager_1117", "位于:"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1120("mark.marketingactivity.qywxgroupsendmanager_1120", "本年度"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1122("mark.marketingactivity.qywxgroupsendmanager_1122", "上一年度"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1124("mark.marketingactivity.qywxgroupsendmanager_1124", "下一年度"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1126("mark.marketingactivity.qywxgroupsendmanager_1126", "本季度"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1128("mark.marketingactivity.qywxgroupsendmanager_1128", "上一季度"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1130("mark.marketingactivity.qywxgroupsendmanager_1130", "下一季度"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1132("mark.marketingactivity.qywxgroupsendmanager_1132", "本月"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1134("mark.marketingactivity.qywxgroupsendmanager_1134", "上月"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1136("mark.marketingactivity.qywxgroupsendmanager_1136", "下月"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1138("mark.marketingactivity.qywxgroupsendmanager_1138", "本周"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1140("mark.marketingactivity.qywxgroupsendmanager_1140", "上周"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1142("mark.marketingactivity.qywxgroupsendmanager_1142", "下周"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1144("mark.marketingactivity.qywxgroupsendmanager_1144", "今天"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1146("mark.marketingactivity.qywxgroupsendmanager_1146", "昨天"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1148("mark.marketingactivity.qywxgroupsendmanager_1148", "明天"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1157("mark.marketingactivity.qywxgroupsendmanager_1157", "位于时间段:"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1167("mark.marketingactivity.qywxgroupsendmanager_1167", "后"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1167_1("mark.marketingactivity.qywxgroupsendmanager_1167_1", "天"),
    MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1186("mark.marketingactivity.qywxgroupsendmanager_1186", "前"),
    MARK_MARKETINGACTIVITY_SENDGROUPSMSMANAGER_134("mark.marketingactivity.sendgroupsmsmanager_134", "短信推广"),
    MARK_MARKETINGACTIVITY_WECHATSERVICEMANAGER_1173("mark.marketingactivity.wechatservicemanager_1173", "微信用户昵称"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_279("mark.manager.marketingflowinstancemanager_279", "流程中"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_287("mark.manager.marketingflowinstancemanager_287", "营销用户没有手机"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_293("mark.manager.marketingflowinstancemanager_293", "服务号已取消授权"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_362("mark.manager.marketingflowinstancemanager_362", "微信消息类型错误"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_395("mark.manager.marketingflowinstancemanager_395", "营销用户没有邮件"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_399("mark.manager.marketingflowinstancemanager_399", "执行异常"),
    MARK_MANAGER_MARKETINGFLOWINSTANCEMANAGER_465("mark.manager.marketingflowinstancemanager_465", "未找到关联的微信用户"),
    MARK_MANAGER_MARKETINGORDERMANAGER_82("mark.manager.marketingordermanager_82", "订货通模板"),
    MARK_MANAGER_MARKETINGUSERGROUPMANAGER_147("mark.manager.marketingusergroupmanager_147", "自动更新了人群，用户总数为"),
    MARK_MANAGER_MARKETINGUSERGROUPMANAGER_147_1("mark.manager.marketingusergroupmanager_147_1", "人"),
    MARK_MANAGER_MARKETINGUSERGROUPMANAGER_149("mark.manager.marketingusergroupmanager_149", "手动更新了人群，用户总数为"),
    MARK_MANAGER_MARKETINGUSERGROUPMANAGER_468("mark.manager.marketingusergroupmanager_468", "添加 0 个到人群"),
    MARK_MANAGER_MARKETINGUSERGROUPMANAGER_471("mark.manager.marketingusergroupmanager_471", "添加 "),
    MARK_MANAGER_MARKETINGUSERGROUPMANAGER_471_1("mark.manager.marketingusergroupmanager_471_1", "到人群"),
    MARK_MANAGER_MATERIALTAGMANAGER_57("mark.manager.materialtagmanager_57", "该模型下已有标签"),
    MARK_MANAGER_MATERIALTAGMANAGER_78("mark.manager.materialtagmanager_78", "该标签已经被使用，不允许删除"),
    MARK_MANAGER_MATERIALTAGMANAGER_83("mark.manager.materialtagmanager_83", "该标签存在下级标签，不允许删除"),
    MARK_MANAGER_MINIAPPSUBSCRIBEMESSAGEMANAGER_93("mark.manager.miniappsubscribemessagemanager_93", "你提交的表单"),
    MARK_MANAGER_MINIAPPSUBSCRIBEMESSAGEMANAGER_93_1("mark.manager.miniappsubscribemessagemanager_93_1", "待支付"),
    MARK_MANAGER_MINIAPPSUBSCRIBEMESSAGEMANAGER_95("mark.manager.miniappsubscribemessagemanager_95", "请及时付款，如已支付请忽略。"),
    MARK_MANAGER_MINIAPPSUBSCRIBEMESSAGEMANAGER_111_1("mark.manager.miniappsubscribemessagemanager_111_1", "已支付"),
    MARK_MANAGER_OBJECTGROUPMANAGER_491("mark.manager.objectgroupmanager_491", "全部"),
    MARK_PIECHART_LEADSOURCENAMEPIECHART_56("mark.piechart.leadsourcenamepiechart_56", "百度"),
    MARK_PAY_FSPAYORDERMANAGER_88("mark.pay.fspayordermanager_88", "购买"),
    MARK_PAY_FSPAYORDERMANAGER_229("mark.pay.fspayordermanager_229", "商品名称未设置"),
    MARK_PAY_FSPAYORDERMANAGER_230("mark.pay.fspayordermanager_230", "支付金额未设置"),
    MARK_MANAGER_PICTUREMANAGER_272("mark.manager.picturemanager_272", "公众号："),
    MARK_QR_QRPOSTERMANAGER_481("mark.qr.qrpostermanager_481", "营销素材"),
    MARK_QR_QRPOSTERMANAGER_503("mark.qr.qrpostermanager_503", "网页"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2208("mark.qywx.groupsendmessagemanager_2208", "查询时间不存在"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2704("mark.qywx.groupsendmessagemanager_2704", "营销助手"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2709("mark.qywx.groupsendmessagemanager_2709", "您有一个客户产生新的互动"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2722("mark.qywx.groupsendmessagemanager_2722", "详情"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2753("mark.qywx.groupsendmessagemanager_2753", "你有一个新的推广任务"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2766("mark.qywx.groupsendmessagemanager_2766", "宣传语"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2769("mark.qywx.groupsendmessagemanager_2769", "推广时间"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1("mark.qywx.groupsendmessagemanager_2774_1", "日"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2780("mark.qywx.groupsendmessagemanager_2780", "立即去推广"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2824("mark.qywx.groupsendmessagemanager_2824", "你有一个新的推广"),
    MARK_QYWX_GROUPSENDMESSAGEMANAGER_2958("mark.qywx.groupsendmessagemanager_2958", "用户ID为空"),
    MARK_QYWX_MOMENTMANAGER_1158("mark.qywx.momentmanager_1158", "【H5链接】"),
    MARK_QYWX_MOMENTMANAGER_1161("mark.qywx.momentmanager_1161", "【图片】"),
    MARK_QYWX_MOMENTMANAGER_1164("mark.qywx.momentmanager_1164", "【视频】"),
    MARK_QYWX_MOMENTMANAGER_1167("mark.qywx.momentmanager_1167", "【文件】"),
    MARK_QYWX_MOMENTMANAGER_1540("mark.qywx.momentmanager_1540", "【任务提醒通知】 \n管理员给您下发了发表朋友圈任务，请前往【工作台>客户朋友圈>右上角企业通知】中确认发送"),
    MARK_QYWX_MOMENTMANAGER_1625("mark.qywx.momentmanager_1625", "待发送"),
    MARK_QYWX_MOMENTMANAGER_1628("mark.qywx.momentmanager_1628", "发送中"),
    MARK_QYWX_MOMENTMANAGER_1631("mark.qywx.momentmanager_1631", "发送完成"),
    MARK_QYWX_MOMENTMANAGER_1634("mark.qywx.momentmanager_1634", "取消发送"),
    MARK_QYWX_QYWXCONTACTMANAGER_497("mark.qywx.qywxcontactmanager_497", "您好，这是我的名片，请惠存"),
    MARK_QYWX_QYWXMANAGER_1485("mark.qywx.qywxmanager_1485", "研发中心"),
    MARK_QYWX_QYWXMANAGER_1490("mark.qywx.qywxmanager_1490", "深圳研发中心"),
    MARK_QYWX_QYWXMANAGER_1495("mark.qywx.qywxmanager_1495", "营销通业务部"),
    MARK_QYWX_QYWXMANAGER_3238("mark.qywx.qywxmanager_3238", "agentConfig记录不存在"),
    MARK_QYWX_QYWXMANAGER_3244("mark.qywx.qywxmanager_3244", "agentId不存在"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_68("mark.qywx.qywxminiappmessagemanager_68", "分销人员注册审核通知"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_102("mark.qywx.qywxminiappmessagemanager_102", "您有一个新的邀约任务"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_126("mark.qywx.qywxminiappmessagemanager_126", "管理员邀请您开通微信名片"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_159("mark.qywx.qywxminiappmessagemanager_159", "提示"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_186("mark.qywx.qywxminiappmessagemanager_186", "收到留言通知"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_187("mark.qywx.qywxminiappmessagemanager_187", "留言内容"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_187_1("mark.qywx.qywxminiappmessagemanager_187_1", "你有未读消息"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_188("mark.qywx.qywxminiappmessagemanager_188", "留言者"),
    MARK_QYWX_QYWXMINIAPPMESSAGEMANAGER_189("mark.qywx.qywxminiappmessagemanager_189", "留言时间"),
    MARK_QYWX_QYWXUPGRADEIDMANAGER_246("mark.qywx.qywxupgradeidmanager_246", "更新本地表失败"),
    MARK_QYWX_QYWXUPGRADEIDMANAGER_571("mark.qywx.qywxupgradeidmanager_571", "找不到新的员工ID"),
    MARK_QYWX_QYWXUPGRADEIDMANAGER_652("mark.qywx.qywxupgradeidmanager_652", "找不到新的外部联系人ID"),
    MARK_MANAGER_SETTINGMANAGER_788("mark.manager.settingmanager_788", "弹窗"),
    MARK_MANAGER_SETTINGMANAGER_792("mark.manager.settingmanager_792", "小程序内用户个人名片信息展示"),
    MARK_MANAGER_SETTINGMANAGER_793("mark.manager.settingmanager_793", "应用内保存海报"),
    MARK_MANAGER_SETTINGMANAGER_794("mark.manager.settingmanager_794", "保存客户的联系方式"),
    MARK_MANAGER_SETTINGMANAGER_795("mark.manager.settingmanager_795", "推荐附近的门店"),
    MARK_MANAGER_SETTINGMANAGER_796("mark.manager.settingmanager_796", "应用内文档的分享"),
    MARK_MANAGER_SETTINGMANAGER_797("mark.manager.settingmanager_797", "便捷填写线索表单"),
    MARK_MANAGER_SETTINGMANAGER_798("mark.manager.settingmanager_798", "注册、登录小程序"),
    MARK_MANAGER_SETTINGMANAGER_799("mark.manager.settingmanager_799", "便捷扫码填写表单信息"),
    MARK_MANAGER_SETTINGMANAGER_800("mark.manager.settingmanager_800", "通过录音搜索内容"),
    MARK_MANAGER_SETTINGMANAGER_801("mark.manager.settingmanager_801", "便捷复制文本内容"),
    MARK_MANAGER_SETTINGMANAGER_802("mark.manager.settingmanager_802", "应用内使用相机拍摄的照片"),
    MARK_MANAGER_SETTINGMANAGER_803("mark.manager.settingmanager_803", "应用内使用蓝牙 "),
    MARK_MANAGER_SETTINGMANAGER_804("mark.manager.settingmanager_804", "应用内使用日历（仅写入）权限"),
    MARK_MANAGER_SETTINGMANAGER_805("mark.manager.settingmanager_805", "应用内使用微信运动数据"),
    MARK_MW_MWSENDMANAGER_1287("mark.mw.mwsendmanager_1287", "其他："),
    MARK_MW_SMSPARAMMANAGER_96("mark.mw.smsparammanager_96", "市场活动名称"),
    MARK_MW_SMSPARAMMANAGER_97("mark.mw.smsparammanager_97", "活动开始时间"),
    MARK_MW_SMSPARAMMANAGER_98("mark.mw.smsparammanager_98", "活动结束时间"),
    MARK_MW_SMSPARAMMANAGER_105("mark.mw.smsparammanager_105", "门票URL"),
    MARK_MW_SMSPARAMMANAGER_106("mark.mw.smsparammanager_106", "会议URL"),
    MARK_SMS_PAYMANAGER_79("mark.sms.paymanager_79", "营销短信包"),
    MARK_SMS_QUOTAMANAGER_113("mark.sms.quotamanager_113", "手工订单-操作人("),
    MARK_MANAGER_SPREADCHANNELMANAGER_607("mark.manager.spreadchannelmanager_607", "其它:"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_471("mark.manager.triggertaskinstancemanager_471", "未命中分支"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_781("mark.manager.triggertaskinstancemanager_781", "无群主"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_785("mark.manager.triggertaskinstancemanager_785", "无绑定企业微信"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_792("mark.manager.triggertaskinstancemanager_792", "无授权应用"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_798("mark.manager.triggertaskinstancemanager_798", "客户运营任务通知"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_799("mark.manager.triggertaskinstancemanager_799", "管理员给您分配了一个新的客户消息推送任务，已同步至侧边栏【营销助手】待办列表，请及时跟进"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_831("mark.manager.triggertaskinstancemanager_831", "未找到负责人"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_854("mark.manager.triggertaskinstancemanager_854", "无绑授权应用"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_929("mark.manager.triggertaskinstancemanager_929", "无marketingUserId"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_933("mark.manager.triggertaskinstancemanager_933", "无WxWorkExternalUser-entry"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_949("mark.manager.triggertaskinstancemanager_949", "无WxWorkExternalUser-extenalUserIds"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_950("mark.manager.triggertaskinstancemanager_950", "根据过滤设置规则，排除近n天内已接收过推送通知的营销用户"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_951("mark.manager.triggertaskinstancemanager_951", "根据过滤设置规则，排除近n天内已接收过推送任务的群主"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1018("mark.manager.triggertaskinstancemanager_1018", "消息发送参数为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1081("mark.manager.triggertaskinstancemanager_1081", "小程序封面上传失败"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1116("mark.manager.triggertaskinstancemanager_1116", "arg参数无效"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1134("mark.manager.triggertaskinstancemanager_1134", "营销用户不存在"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1158("mark.manager.triggertaskinstancemanager_1158", "此SOP下多次访问同一内容仅触发一次通知"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1168("mark.manager.triggertaskinstancemanager_1168", "仅有姓名或昵称的用户产生行为才触发互动通知"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1170("mark.manager.triggertaskinstancemanager_1170", "用户"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1177("mark.manager.triggertaskinstancemanager_1177", "客户名称"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1178("mark.manager.triggertaskinstancemanager_1178", "互动行为"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1179("mark.manager.triggertaskinstancemanager_1179", "互动内容"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1180("mark.manager.triggertaskinstancemanager_1180", "互动时间"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1372("mark.manager.triggertaskinstancemanager_1372", "自定义模板内容为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1391("mark.manager.triggertaskinstancemanager_1391", "待发手机号为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1398("mark.manager.triggertaskinstancemanager_1398", "流程结束"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1418("mark.manager.triggertaskinstancemanager_1418", "待发员工id为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1430("mark.manager.triggertaskinstancemanager_1430", "你有一个客户产生新的互动"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1436("mark.manager.triggertaskinstancemanager_1436", "自定义模板标题/内容为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1442("mark.manager.triggertaskinstancemanager_1442", "名称"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1570("mark.manager.triggertaskinstancemanager_1570", "无关联的邮箱信息"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1877("mark.manager.triggertaskinstancemanager_1877", "类型错误"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1880("mark.manager.triggertaskinstancemanager_1880", "消息类型错误"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1889("mark.manager.triggertaskinstancemanager_1889", "换取appId失败:"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1926("mark.manager.triggertaskinstancemanager_1926", "找不到关联的活动成员"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1955("mark.manager.triggertaskinstancemanager_1955", "关联的手机为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1995("mark.manager.triggertaskinstancemanager_1995", "营销通(SOP)"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_2021("mark.manager.triggertaskinstancemanager_2021", "任务的标签列表为空"),
    MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_2271("mark.manager.triggertaskinstancemanager_2271", "触发"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797("mark.usermarketingaccount.usermarketingaccountassociationmanager_797", "邮箱"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_903("mark.usermarketingaccount.usermarketingaccountassociationmanager_903", "合并账号"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060("mark.usermarketingaccount.usermarketingaccountmanager_3060", "分钟"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3062("mark.usermarketingaccount.usermarketingaccountmanager_3062", " 1分钟"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065("mark.usermarketingaccount.usermarketingaccountmanager_3065", "秒"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3066("mark.usermarketingaccount.usermarketingaccountmanager_3066", "毫秒"),

    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3070("mark.usermarketingaccount.usermarketingaccountmanager_3070", " 浏览至"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3074("mark.usermarketingaccount.usermarketingaccountmanager_3074", "在线客服咨询"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3105("mark.usermarketingaccount.usermarketingaccountmanager_3105", "服务号推广"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3106("mark.usermarketingaccount.usermarketingaccountmanager_3106", "微信"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3111("mark.usermarketingaccount.usermarketingaccountmanager_3111", "企微群发"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3112("mark.usermarketingaccount.usermarketingaccountmanager_3112", "企业微信"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3117("mark.usermarketingaccount.usermarketingaccountmanager_3117", "企微朋友圈"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3126("mark.usermarketingaccount.usermarketingaccountmanager_3126", "访问直播"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3126_1("mark.usermarketingaccount.usermarketingaccountmanager_3126_1", "校验页"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3437("mark.usermarketingaccount.usermarketingaccountmanager_3437", "观看直播"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3446("mark.usermarketingaccount.usermarketingaccountmanager_3446", "预约直播"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3449("mark.usermarketingaccount.usermarketingaccountmanager_3449", "回放直播"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3458("mark.usermarketingaccount.usermarketingaccountmanager_3458", "参与互动"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3462("mark.usermarketingaccount.usermarketingaccountmanager_3462", "次"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3467("mark.usermarketingaccount.usermarketingaccountmanager_3467", "报名会议"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3470("mark.usermarketingaccount.usermarketingaccountmanager_3470", "参加会议"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3473("mark.usermarketingaccount.usermarketingaccountmanager_3473", "点击官网页面元素"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3476("mark.usermarketingaccount.usermarketingaccountmanager_3476", "回复邮件"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3479("mark.usermarketingaccount.usermarketingaccountmanager_3479", "收到短信"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3482("mark.usermarketingaccount.usermarketingaccountmanager_3482", "查看资料"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3485("mark.usermarketingaccount.usermarketingaccountmanager_3485", "下载文件"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3488("mark.usermarketingaccount.usermarketingaccountmanager_3488", "转发表单"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3491("mark.usermarketingaccount.usermarketingaccountmanager_3491", "查看名片"),
    MARK_MANAGER_USERTAGMANAGER_252("mark.manager.usertagmanager_252", "该名称已存在"),
    MARK_MANAGER_VHALLMANAGER_707("mark.manager.vhallmanager_707", "线索字段为空"),
    MARK_MANAGER_VHALLMANAGER_727("mark.manager.vhallmanager_727", "查重校验失败"),
    MARK_MANAGER_VHALLMANAGER_738("mark.manager.vhallmanager_738", "线索重复"),
    MARK_MANAGER_VHALLMANAGER_750("mark.manager.vhallmanager_750", "同步成功"),
    MARK_MANAGER_VHALLMANAGER_888("mark.manager.vhallmanager_888", "微吼"),
    MARK_MANAGER_WEBHOOKMANAGER_71("mark.manager.webhookmanager_71", "获取token失败"),
    MARK_MANAGER_WEBHOOKMANAGER_111("mark.manager.webhookmanager_111", "调用失败"),
    MARK_MANAGER_WECHATFANOUTERTAGSYNCHRONIZATIONMANAGER_106("mark.manager.wechatfanoutertagsynchronizationmanager_106", "公众号:"),
    MARK_MANAGER_WECHATFANOUTERTAGSYNCHRONIZATIONMANAGER_106_1("mark.manager.wechatfanoutertagsynchronizationmanager_106_1", "公众号同步的标签"),
    MARK_WHATSAPP_WHATSAPPMANAGER_355("mark.whatsapp.whatsappmanager_355", "该号码在"),
    MARK_WHATSAPP_WHATSAPPMANAGER_355_1("mark.whatsapp.whatsappmanager_355_1", "天内已经发送过了"),
    MARK_WHATSAPP_WHATSAPPMANAGER_721("mark.whatsapp.whatsappmanager_721", "自定义变量"),
    MARK_WHATSAPP_WHATSAPPMANAGER_721_1("mark.whatsapp.whatsappmanager_721_1", "填写指南"),
    MARK_WHATSAPP_WHATSAPPMANAGER_802("mark.whatsapp.whatsappmanager_802", "个手机号码"),
    MARK_WHATSAPP_WHATSAPPMANAGER_803("mark.whatsapp.whatsappmanager_803", "Whats App群未同步，请先同步群数据后再同步群成员"),
    MARK_WHATSAPP_WHATSAPPMANAGER_804("mark.whatsapp.whatsappmanager_804", "Whats App联系人未同步，请先同步联系人数据后再同步群成员"),
    MARK_MANAGER_WXCOUPONPAYMANAGER_735("mark.manager.wxcouponpaymanager_735", "更新CouponObj crm对象异常"),
    MARK_WXOFFICIALACCOUNTSPROXY_TEMPCHANNELQRCODEMANAGER_74("mark.wxofficialaccountsproxy.tempchannelqrcodemanager_74", "扫描二维码："),
    MARK_MANAGER_WXWORKTAGSYNCHRONIZATIONMANAGER_47("mark.manager.wxworktagsynchronizationmanager_47", "不支持只创建分组"),
    MARK_MANAGER_WXWORKTAGSYNCHRONIZATIONMANAGER_160_1("mark.manager.wxworktagsynchronizationmanager_160_1", "企业微信同步的标签"),
    MARK_MANAGER_XIAOETONGMANAGER_1101("mark.manager.xiaoetongmanager_1101", "小鹅通"),
    MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_175("mark.handlers.officialwebsitethirdplateformeventhandler_175", "线索数据重复同步"),
    MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_182("mark.handlers.officialwebsitethirdplateformeventhandler_182", "线索数据为空"),
    MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_183("mark.handlers.officialwebsitethirdplateformeventhandler_183", "解析后线索字段为空"),
    MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_283("mark.handlers.officialwebsitethirdplateformeventhandler_283", "53快服"),
    MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_284("mark.handlers.officialwebsitethirdplateformeventhandler_284", "抖音来客"),
    MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_285("mark.handlers.officialwebsitethirdplateformeventhandler_285", "橙子建站"),
    MARK_DOUYIN_CONNECOTR_1("mark.douyin.connector_1", "抖音来客品牌账户id"),
    MARK_HANDLER_CRMMQNEWMESSAGEHANDLER_1159("mark.handler.crmmqnewmessagehandler_1159", "小时"),
    MARK_HANDLER_CRMMQNEWMESSAGEHANDLER_1167("mark.handler.crmmqnewmessagehandler_1167", "分"),
    MARK_REMOTE_CRMV2MAPPINGMANAGER_484("mark.remote.crmv2mappingmanager_484", ":未填写"),
    MARK_REMOTE_CRMV2MAPPINGMANAGER_536("mark.remote.crmv2mappingmanager_536", "未填写"),
    MARK_REMOTE_MEMBERMANAGER_835("mark.remote.membermanager_835", "会员一键报名设置失败"),
    MARK_REMOTE_MEMBERMANAGER_840("mark.remote.membermanager_840", "会员一键报名映射配置错误"),
    MARK_REMOTE_MEMBERMANAGER_1221("mark.remote.membermanager_1221", "会员个人中心"),
    MARK_REMOTE_MEMBERMANAGER_1235("mark.remote.membermanager_1235", "会员信息编辑页"),
    MARK_REMOTE_MEMBERMANAGER_1255("mark.remote.membermanager_1255", "会员注册页"),
    MARK_REMOTE_MEMBERMANAGER_1258("mark.remote.membermanager_1258", "复制注册页失败"),
    MARK_REMOTE_MEMBERMANAGER_1262("mark.remote.membermanager_1262", "会员登录页"),
    MARK_REMOTE_MEMBERMANAGER_1265("mark.remote.membermanager_1265", "复制登录页失败"),
    MARK_REMOTE_MEMBERMANAGER_1272("mark.remote.membermanager_1272", "复制会员个人中心页失败"),
    MARK_REMOTE_MEMBERMANAGER_1279("mark.remote.membermanager_1279", "复制会员信息编辑页失败"),
    MARK_REMOTE_MEMBERMANAGER_1306("mark.remote.membermanager_1306", "需要手工查看会员配置信息！！！， ea:"),
    MARK_REMOTE_MEMBERMANAGER_1359("mark.remote.membermanager_1359", "未开始"),
    MARK_REMOTE_MEMBERMANAGER_1366("mark.remote.membermanager_1366", "进行中"),
    MARK_REMOTE_MEMBERMANAGER_1374("mark.remote.membermanager_1374", "已结束"),
    MARK_SERVICE_ACTIVITYSERVICEIMPL_155("mark.service.activityserviceimpl_155", "企业为空"),
    MARK_SERVICE_ACTIVITYSERVICEIMPL_334("mark.service.activityserviceimpl_334", "activity 为null"),
    MARK_SERVICE_ACTIVITYSERVICEIMPL_696("mark.service.activityserviceimpl_696", "活动详情包含name为空的数据! "),
    MARK_ADVERTISER_ADVERTISERSERVICEIMPL_462("mark.advertiser.advertiserserviceimpl_462", "广告投放人群包_"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_330("mark.headlines.authcallbackserviceimpl_330", "state为空"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_363("mark.headlines.authcallbackserviceimpl_363", "userId为空"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_366("mark.headlines.authcallbackserviceimpl_366", "签名为空"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_370("mark.headlines.authcallbackserviceimpl_370", "appId为空"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_373("mark.headlines.authcallbackserviceimpl_373", "appId不一致"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_383("mark.headlines.authcallbackserviceimpl_383", "签名错误"),
    MARK_HEADLINES_AUTHCALLBACKSERVICEIMPL_401("mark.headlines.authcallbackserviceimpl_401", "获取账户信息失败"),
    MARK_OCPC_ADOCPCSERVICEIMPL_210("mark.ocpc.adocpcserviceimpl_210", "API接口TOKEN校验失败"),
    MARK_OCPC_ADOCPCSERVICEIMPL_213("mark.ocpc.adocpcserviceimpl_213", "百度服务内部错误"),
    MARK_OCPC_ADOCPCSERVICEIMPL_259("mark.ocpc.adocpcserviceimpl_259", "数据不存在，请刷新"),
    MARK_OCPC_ADOCPCSERVICEIMPL_271("mark.ocpc.adocpcserviceimpl_271", "找不到落地页对象"),
    MARK_OCPC_ADOCPCSERVICEIMPL_275("mark.ocpc.adocpcserviceimpl_275", "广告账号不存在"),
    MARK_OCPC_ADOCPCSERVICEIMPL_280("mark.ocpc.adocpcserviceimpl_280", "广告回传配置不存在"),
    MARK_OCPC_ADOCPCSERVICEIMPL_416("mark.ocpc.adocpcserviceimpl_416", "目前仅支持百度平台的无效返款"),
    MARK_SERVICE_ARTICLESERVICEIMPL_703("mark.service.articleserviceimpl_703", "0秒"),
    MARK_BAIDU_BAIDUCAMPAIGNSERVICEIMPL_907("mark.baidu.baiducampaignserviceimpl_907", "百度营销通"),
    MARK_BAIDU_BAIDUCAMPAIGNSERVICEIMPL_909("mark.baidu.baiducampaignserviceimpl_909", "飞鱼线索CRM"),
    MARK_BAIDU_BAIDUCAMPAIGNSERVICEIMPL_911("mark.baidu.baiducampaignserviceimpl_911", "腾讯线索平台"),
    MARK_SERVICE_BOARDSERVICEIMPL_167("mark.service.boardserviceimpl_167", "无权操作"),
    MARK_SERVICE_BOARDSERVICEIMPL_189("mark.service.boardserviceimpl_189", "名字不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_191("mark.service.boardserviceimpl_191", "boardId不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_227("mark.service.boardserviceimpl_227", "列表id不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_229("mark.service.boardserviceimpl_229", "卡片类型非法"),
    MARK_SERVICE_BOARDSERVICEIMPL_231("mark.service.boardserviceimpl_231", "列表id错误"),
    MARK_SERVICE_BOARDSERVICEIMPL_238("mark.service.boardserviceimpl_238", "卡片id不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_239("mark.service.boardserviceimpl_239", "名称不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_240("mark.service.boardserviceimpl_240", "看板id不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_241("mark.service.boardserviceimpl_241", "卡片状态非法"),
    MARK_SERVICE_BOARDSERVICEIMPL_246("mark.service.boardserviceimpl_246", "系统模板不允许修改"),
    MARK_SERVICE_BOARDSERVICEIMPL_248("mark.service.boardserviceimpl_248", "卡片不存在"),
    MARK_SERVICE_BOARDSERVICEIMPL_290("mark.service.boardserviceimpl_290", "id错误"),
    MARK_SERVICE_BOARDSERVICEIMPL_302("mark.service.boardserviceimpl_302", "卡片ID不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_304("mark.service.boardserviceimpl_304", "目标列表不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_306("mark.service.boardserviceimpl_306", "卡片ID错误"),
    MARK_SERVICE_BOARDSERVICEIMPL_308("mark.service.boardserviceimpl_308", "目标列表ID错误"),
    MARK_SERVICE_BOARDSERVICEIMPL_412("mark.service.boardserviceimpl_412", "boardCardId不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_423("mark.service.boardserviceimpl_423", "看板Id不能不为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_424("mark.service.boardserviceimpl_424", "看板卡片Id不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_425("mark.service.boardserviceimpl_425", "id不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_430("mark.service.boardserviceimpl_430", "结束时间要大于开始时间"),
    MARK_SERVICE_BOARDSERVICEIMPL_433("mark.service.boardserviceimpl_433", "任务不存在"),
    MARK_SERVICE_BOARDSERVICEIMPL_478("mark.service.boardserviceimpl_478", "看板卡片id不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_479("mark.service.boardserviceimpl_479", "任务名称不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_481("mark.service.boardserviceimpl_481", "状态非法"),
    MARK_SERVICE_BOARDSERVICEIMPL_580("mark.service.boardserviceimpl_580", "参数为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_583("mark.service.boardserviceimpl_583", "SOP看板的objectId不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_587("mark.service.boardserviceimpl_587", "看板名称不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_691("mark.service.boardserviceimpl_691", "没有权限删除"),
    MARK_SERVICE_BOARDSERVICEIMPL_713("mark.service.boardserviceimpl_713", "UpdateBoardArg参数为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_714("mark.service.boardserviceimpl_714", "看板id为空或者名称为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_726("mark.service.boardserviceimpl_726", "看板人员不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_738("mark.service.boardserviceimpl_738", "删除人员失败"),
    MARK_SERVICE_BOARDSERVICEIMPL_744("mark.service.boardserviceimpl_744", "添加人员失败"),
    MARK_SERVICE_BOARDSERVICEIMPL_751("mark.service.boardserviceimpl_751", "参数有误"),
    MARK_SERVICE_BOARDSERVICEIMPL_757("mark.service.boardserviceimpl_757", "评论内容为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_767("mark.service.boardserviceimpl_767", "看板ID为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_774("mark.service.boardserviceimpl_774", "可见范围只能为private或者public"),
    MARK_SERVICE_BOARDSERVICEIMPL_776("mark.service.boardserviceimpl_776", "操作者非当前看板的可见用户，没有权限修改看板"),
    MARK_SERVICE_BOARDSERVICEIMPL_1031("mark.service.boardserviceimpl_1031", "卡片列表不存在"),
    MARK_SERVICE_BOARDSERVICEIMPL_1163("mark.service.boardserviceimpl_1163", "关联对象类型不能为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_1260("mark.service.boardserviceimpl_1260", "看板Id不允许为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_1299("mark.service.boardserviceimpl_1299", "应用场景不合法"),
    MARK_SERVICE_BOARDSERVICEIMPL_1314("mark.service.boardserviceimpl_1314", "传入参数非法"),
    MARK_SERVICE_BOARDSERVICEIMPL_1340("mark.service.boardserviceimpl_1340", "市场活动ID不为空"),
    MARK_SERVICE_BOARDSERVICEIMPL_1411("mark.service.boardserviceimpl_1411", "停用员工"),
    MARK_SERVICE_BROWSERUSERSERVICEIMPL_68("mark.service.browseruserserviceimpl_68", "营销用户id错误"),
    MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_270("mark.service.campaignmergedataserviceimpl_270", "报名数据"),
    MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_275("mark.service.campaignmergedataserviceimpl_275", "公司名称"),
    MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_276("mark.service.campaignmergedataserviceimpl_276", "职务"),
    MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_418("mark.service.campaignmergedataserviceimpl_418", "报名人员列表"),
    MARK_SERVICE_CLUEMANAGEMENTSERVICEIMPL_257("mark.service.cluemanagementserviceimpl_257", "不能同时导入超100条"),
    MARK_SERVICE_CLUEMANAGEMENTSERVICEIMPL_275("mark.service.cluemanagementserviceimpl_275", "数据同步中,请勿多次同步"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_1999("mark.conference.conferenceserviceimpl_1999", "参会人员列表"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_2175("mark.conference.conferenceserviceimpl_2175", "会议邀约名单"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_1("mark.conference.conferenceserviceimpl_2180_1", "公司"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_3("mark.conference.conferenceserviceimpl_2180_3", "手机"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_5("mark.conference.conferenceserviceimpl_2180_5", "邀约员工"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_2200("mark.conference.conferenceserviceimpl_2200", "未知状态"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3506("mark.conference.conferenceserviceimpl_3506", "导入的签到数据错误报告"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3618("mark.conference.conferenceserviceimpl_3618", "诚邀您参加："),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3620("mark.conference.conferenceserviceimpl_3620", "会议地点："),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3950("mark.conference.conferenceserviceimpl_3950", "请邀约自己所负责的客户参与会议"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3952("mark.conference.conferenceserviceimpl_3952", "立即去邀请"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3965("mark.conference.conferenceserviceimpl_3965", "至"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3990("mark.conference.conferenceserviceimpl_3990", "任务描述："),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3994("mark.conference.conferenceserviceimpl_3994", "推广时间："),
    MARK_SERVICE_CRMDASHBOARDSERVICEIMPL_30("mark.service.crmdashboardserviceimpl_30", "市场活动分析"),
    MARK_SERVICE_CRMDASHBOARDSERVICEIMPL_31("mark.service.crmdashboardserviceimpl_31", "广告投放效果分析"),
    MARK_SERVICE_CRMDASHBOARDSERVICEIMPL_32("mark.service.crmdashboardserviceimpl_32", "线索转换分析"),
    MARK_SERVICE_CRMDASHBOARDSERVICEIMPL_33("mark.service.crmdashboardserviceimpl_33", "市场ROI分析"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_513("mark.service.customizeformdataserviceimpl_513", "渠道"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_741("mark.service.customizeformdataserviceimpl_741", "必须设置支付信息"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_742("mark.service.customizeformdataserviceimpl_742", "必须设置支付商品名称"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_743("mark.service.customizeformdataserviceimpl_743", "必须设置正确的支付金额"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_757("mark.service.customizeformdataserviceimpl_757", "模板已存在"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_821("mark.service.customizeformdataserviceimpl_821", "不可变更表单用途"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_951("mark.service.customizeformdataserviceimpl_951", "线索存入到线索池:"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_953("mark.service.customizeformdataserviceimpl_953", "线索存入到销售线索"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_972("mark.service.customizeformdataserviceimpl_972", "表单字段:"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_972_1("mark.service.customizeformdataserviceimpl_972_1", "映射到CRM字段:"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_980("mark.service.customizeformdataserviceimpl_980", "线索存入成功后自动创建会员"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_983("mark.service.customizeformdataserviceimpl_983", "每人只能填写一次"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_986("mark.service.customizeformdataserviceimpl_986", "自动识别会员身份"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_989("mark.service.customizeformdataserviceimpl_989", "表单提交次数不超过:"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_990("mark.service.customizeformdataserviceimpl_990", "提交满额时提示:"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1908("mark.service.customizeformdataserviceimpl_1908", "内容表单设置未完成"),
    MARK_SERVICE_CUSTOMIZEMINIAPPNAVBARSERVICEIMPL_157("mark.service.customizeminiappnavbarserviceimpl_157", "首页"),
    MARK_SERVICE_CUSTOMIZEMINIAPPNAVBARSERVICEIMPL_184("mark.service.customizeminiappnavbarserviceimpl_184", "介绍页"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_384("mark.distribution.clueserviceimpl_384", "营销数据概况"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_401("mark.distribution.clueserviceimpl_401", "手机号码"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_402("mark.distribution.clueserviceimpl_402", "职位"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_404("mark.distribution.clueserviceimpl_404", "线索详情"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_406("mark.distribution.clueserviceimpl_406", "分销人员"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_407("mark.distribution.clueserviceimpl_407", "线索成单奖励"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_408("mark.distribution.clueserviceimpl_408", "招募人员奖励"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_409("mark.distribution.clueserviceimpl_409", "线索提交奖励"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_410("mark.distribution.clueserviceimpl_410", "线索状态"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_411("mark.distribution.clueserviceimpl_411", "无效描述"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_229("mark.distribution.distributorserviceimpl_229", "yyyy年MM月dd日"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_259("mark.distribution.distributorserviceimpl_259", "审核未通过"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_261("mark.distribution.distributorserviceimpl_261", "待处理"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_263("mark.distribution.distributorserviceimpl_263", "审核通过"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_273("mark.distribution.distributorserviceimpl_273", "分销等级"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_274("mark.distribution.distributorserviceimpl_274", "分销等级名称"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_275("mark.distribution.distributorserviceimpl_275", "提供线索数"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_276("mark.distribution.distributorserviceimpl_276", "奖励总额"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_277("mark.distribution.distributorserviceimpl_277", "招募人数"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_278("mark.distribution.distributorserviceimpl_278", "招募人"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_279("mark.distribution.distributorserviceimpl_279", "所属分销管理员"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_281("mark.distribution.distributorserviceimpl_281", "拒绝原因"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_282("mark.distribution.distributorserviceimpl_282", "注册时间"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_294("mark.distribution.distributorserviceimpl_294", "此申请已由管理员分配给其他人审批"),
    MARK_SERVICE_EMPLOYEESPREADSTATISTICSERVICEIMPL_585("mark.service.employeespreadstatisticserviceimpl_585", "我的微信名片"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_73("mark.service.enterpriseemployeestatisticserviceimpl_73", "员工推广统计"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_92("mark.service.enterpriseemployeestatisticserviceimpl_92", "员工姓名"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_93("mark.service.enterpriseemployeestatisticserviceimpl_93", "主属部门"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_94("mark.service.enterpriseemployeestatisticserviceimpl_94", "推广次数"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_95("mark.service.enterpriseemployeestatisticserviceimpl_95", "访问次数"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_96("mark.service.enterpriseemployeestatisticserviceimpl_96", "转发次数"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_97("mark.service.enterpriseemployeestatisticserviceimpl_97", "存入CRM线索数"),
    MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_98("mark.service.enterpriseemployeestatisticserviceimpl_98", "存入CRM客户数"),
    MARK_SERVICE_ENTERPRISEMETACONFIGSERVICEIMPL_68("mark.service.enterprisemetaconfigserviceimpl_68", "错误，不存在该企业"),
    MARK_SERVICE_ENTERPRISEMETACONFIGSERVICEIMPL_127("mark.service.enterprisemetaconfigserviceimpl_127", "参数为空或者参数个数有误"),
    MARK_SERVICE_ENTERPRISEMETACONFIGSERVICEIMPL_135("mark.service.enterprisemetaconfigserviceimpl_135", "名片位置不可移动、不可开关，文案不可修改"),
    MARK_SERVICE_ENTERPRISEMETACONFIGSERVICEIMPL_141("mark.service.enterprisemetaconfigserviceimpl_141", "频道id出错"),
    MARK_SERVICE_ENTERPRISEMETACONFIGSERVICEIMPL_172("mark.service.enterprisemetaconfigserviceimpl_172", "更新失败，不存在该企业"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_267("mark.service.enterprisespreadstatisticserviceimpl_267", "客户"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268("mark.service.enterprisespreadstatisticserviceimpl_268", "发送状态"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_269("mark.service.enterprisespreadstatisticserviceimpl_269", "发送时间"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_270("mark.service.enterprisespreadstatisticserviceimpl_270", "负责员工"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271("mark.service.enterprisespreadstatisticserviceimpl_271", "部门"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_276("mark.service.enterprisespreadstatisticserviceimpl_276", "客户群"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285("mark.service.enterprisespreadstatisticserviceimpl_285", "员工"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_288("mark.service.enterprisespreadstatisticserviceimpl_288", "关联客户数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_289("mark.service.enterprisespreadstatisticserviceimpl_289", "触达客户数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_298("mark.service.enterprisespreadstatisticserviceimpl_298", "未发送客户"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_299("mark.service.enterprisespreadstatisticserviceimpl_299", "已发送客户"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_300("mark.service.enterprisespreadstatisticserviceimpl_300", "接受达上限"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_301("mark.service.enterprisespreadstatisticserviceimpl_301", "不是好友关系"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_310("mark.service.enterprisespreadstatisticserviceimpl_310", "已发送客户群"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_316("mark.service.enterprisespreadstatisticserviceimpl_316", "触达状态"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_317("mark.service.enterprisespreadstatisticserviceimpl_317", "触达时间"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_327("mark.service.enterprisespreadstatisticserviceimpl_327", "推广员工"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1074("mark.service.enterprisespreadstatisticserviceimpl_1074", "没有数据导出"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1095("mark.service.enterprisespreadstatisticserviceimpl_1095", "消息群发-发送详情"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1172("mark.service.enterprisespreadstatisticserviceimpl_1172", "未发送"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1175("mark.service.enterprisespreadstatisticserviceimpl_1175", "已发送"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1178("mark.service.enterprisespreadstatisticserviceimpl_1178", "已不是好友"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1181("mark.service.enterprisespreadstatisticserviceimpl_1181", "接收达上限"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1191("mark.service.enterprisespreadstatisticserviceimpl_1191", "无"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1264("mark.service.enterprisespreadstatisticserviceimpl_1264", "未发表"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1267("mark.service.enterprisespreadstatisticserviceimpl_1267", "已发表"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1313("mark.service.enterprisespreadstatisticserviceimpl_1313", "朋友圈-发表明细"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1350("mark.service.enterprisespreadstatisticserviceimpl_1350", "未触达"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1352("mark.service.enterprisespreadstatisticserviceimpl_1352", "已触达"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629("mark.service.enterprisespreadstatisticserviceimpl_1629", "未找到该员工"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1630("mark.service.enterprisespreadstatisticserviceimpl_1630", "未找到员工号码"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704("mark.service.enterprisespreadstatisticserviceimpl_1704", "未找到该部门"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1729("mark.service.enterprisespreadstatisticserviceimpl_1729", "(已删除)"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1731("mark.service.enterprisespreadstatisticserviceimpl_1731", "(已停用)"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1795("mark.service.enterprisespreadstatisticserviceimpl_1795", "未推广员工"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1808("mark.service.enterprisespreadstatisticserviceimpl_1808", "已推广员工"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1821("mark.service.enterprisespreadstatisticserviceimpl_1821", "推广排名"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1841("mark.service.enterprisespreadstatisticserviceimpl_1841", "访问人数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1842("mark.service.enterprisespreadstatisticserviceimpl_1842", "访问人次"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1843("mark.service.enterprisespreadstatisticserviceimpl_1843", "转发人数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1844("mark.service.enterprisespreadstatisticserviceimpl_1844", "转发人次"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1845("mark.service.enterprisespreadstatisticserviceimpl_1845", "获取线索数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1846("mark.service.enterprisespreadstatisticserviceimpl_1846", "发送人数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1847("mark.service.enterprisespreadstatisticserviceimpl_1847", "送达人数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1851("mark.service.enterprisespreadstatisticserviceimpl_1851", "会员"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2004("mark.service.enterprisespreadstatisticserviceimpl_2004", "未找到相关企业"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2074("mark.service.enterprisespreadstatisticserviceimpl_2074", "互联对接企业"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2075("mark.service.enterprisespreadstatisticserviceimpl_2075", "参与推广员工数"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2135("mark.service.enterprisespreadstatisticserviceimpl_2135", "全员营销-客户详情"),
    MARK_SERVICE_FSBINDSERVICEIMPL_558_2("mark.service.fsbindserviceimpl_558_2", "开通状态"),
    MARK_HEXAGON_HEXAGONSERVICEIMPL_714("mark.hexagon.hexagonserviceimpl_714", "内容中心"),
    MARK_HEXAGON_HEXAGONSERVICEIMPL_784("mark.hexagon.hexagonserviceimpl_784", "活动中心"),
    MARK_HEXAGON_HEXAGONSERVICEIMPL_855("mark.hexagon.hexagonserviceimpl_855", "产品推广"),
    MARK_HEXAGON_HEXAGONSERVICEIMPL_992("mark.hexagon.hexagonserviceimpl_992", "删除的Site_id为空"),
    MARK_HEXAGON_HEXAGONSERVICEIMPL_997("mark.hexagon.hexagonserviceimpl_997", "通用介绍页，通用介绍页不允许删除"),
    MARK_HEXAGON_HEXAGONSERVICEIMPL_1625("mark.hexagon.hexagonserviceimpl_1625", "站点模板"),
    MARK_LIVE_LIVEBASESERVICE_249("mark.live.livebaseservice_249", "报名预约"),
    MARK_LIVE_LIVEBASESERVICE_280("mark.live.livebaseservice_280", "获取讲师PPT"),
    MARK_LIVE_LIVESERVICEIMPL_359("mark.live.liveserviceimpl_359", "(主)"),
    MARK_LIVE_LIVESERVICEIMPL_576("mark.live.liveserviceimpl_576", "视频号中转页面"),
    MARK_LIVE_LIVESERVICEIMPL_2388("mark.live.liveserviceimpl_2388", "报名明细"),
    MARK_LIVE_LIVESERVICEIMPL_2391("mark.live.liveserviceimpl_2391", "直播平台用户ID"),
    MARK_LIVE_LIVESERVICEIMPL_2406("mark.live.liveserviceimpl_2406", "是否回放"),
    MARK_LIVE_LIVESERVICEIMPL_2407("mark.live.liveserviceimpl_2407", "是否互动"),
    MARK_LIVE_LIVESERVICEIMPL_2408("mark.live.liveserviceimpl_2408", "观看时长(分钟)"),
    MARK_LIVE_LIVESERVICEIMPL_2409("mark.live.liveserviceimpl_2409", "回看时长(分钟)"),
    MARK_LIVE_LIVESERVICEIMPL_2410("mark.live.liveserviceimpl_2410", "互动次数"),
    MARK_LIVE_LIVESERVICEIMPL_2580("mark.live.liveserviceimpl_2580", "未回放"),
    MARK_LIVE_LIVESERVICEIMPL_2583("mark.live.liveserviceimpl_2583", "已互动"),
    MARK_LIVE_LIVESERVICEIMPL_2583_1("mark.live.liveserviceimpl_2583_1", "未互动"),
    MARK_LIVE_LIVESERVICEIMPL_2590("mark.live.liveserviceimpl_2590", "0分钟"),
    MARK_LIVE_LIVESERVICEIMPL_2937("mark.live.liveserviceimpl_2937", "当前直播已关联营销通【"),
    MARK_LIVE_LIVESERVICEIMPL_2937_1("mark.live.liveserviceimpl_2937_1", "】，无法再次绑定"),
    MARK_MAIL_EMAILSERVICEIMPL_256("mark.mail.emailserviceimpl_256", "收件邮箱"),
    MARK_MAIL_EMAILSERVICEIMPL_259("mark.mail.emailserviceimpl_259", "失败原因"),
    MARK_MAIL_EMAILSERVICEIMPL_261("mark.mail.emailserviceimpl_261", "打开次数"),
    MARK_MAIL_EMAILSERVICEIMPL_262("mark.mail.emailserviceimpl_262", "点击链接次数"),
    MARK_MAIL_EMAILSERVICEIMPL_263("mark.mail.emailserviceimpl_263", "是否取消订阅"),
    MARK_MAIL_EMAILSERVICEIMPL_264("mark.mail.emailserviceimpl_264", "是否垃圾举报"),
    MARK_MAIL_EMAILSERVICEIMPL_1800("mark.mail.emailserviceimpl_1800", "邮件营销-"),
    MARK_MAIL_EMAILSERVICEIMPL_1849("mark.mail.emailserviceimpl_1849", "是"),
    MARK_MAIL_EMAILSERVICEIMPL_1849_1("mark.mail.emailserviceimpl_1849_1", "否"),
    MARK_MARKETINGACTIVITY_WECHATSERVICEMARKETINGACTIVITYSERVICEIMPL_921("mark.marketingactivity.wechatservicemarketingactivityserviceimpl_921", "粉丝"),
    MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_260("mark.service.marketingeventcommonsettingserviceimpl_260", "时"),
    MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_369("mark.service.marketingeventcommonsettingserviceimpl_369", "会议营销"),
    MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_370("mark.service.marketingeventcommonsettingserviceimpl_370", "在线直播"),
    MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_418("mark.service.marketingeventcommonsettingserviceimpl_418", "发送"),
    MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_419("mark.service.marketingeventcommonsettingserviceimpl_419", "添加"),
    MARK_SERVICE_MARKETINGEVENTSERVICEIMPL_712("mark.service.marketingeventserviceimpl_712", "未生成活动成员，不允许同步"),
    MARK_SERVICE_MARKETINGMODULESTATISTICSSERVICELMPL_1230("mark.service.marketingmodulestatisticsservicelmpl_1230", "[版本:"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_108("mark.marketingplugin.coupontemplateserviceimpl_108", "模板id为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_147("mark.marketingplugin.coupontemplateserviceimpl_147", "企业ea为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_148("mark.marketingplugin.coupontemplateserviceimpl_148", "每页条数为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_149("mark.marketingplugin.coupontemplateserviceimpl_149", "页码为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_175("mark.marketingplugin.coupontemplateserviceimpl_175", "优惠券类型为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_176("mark.marketingplugin.coupontemplateserviceimpl_176", "优惠券名称为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_177("mark.marketingplugin.coupontemplateserviceimpl_177", "门槛不能为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_185("mark.marketingplugin.coupontemplateserviceimpl_185", "状态为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_203("mark.marketingplugin.coupontemplateserviceimpl_203", "满减金额为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_205("mark.marketingplugin.coupontemplateserviceimpl_205", "折扣比例为空"),
    MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_207("mark.marketingplugin.coupontemplateserviceimpl_207", "换购金额为空"),
    MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_222("mark.marketingplugin.marketingpluginserviceimpl_222", "营销插件类型为空"),
    MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_251("mark.marketingplugin.marketingpluginserviceimpl_251", "营销插件状态为空"),
    MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_485("mark.marketingplugin.marketingpluginserviceimpl_485", "知识记录场景字段值不存在"),
    MARK_MARKETINGPLUGIN_MARKETINGPLUGINSERVICEIMPL_489("mark.marketingplugin.marketingpluginserviceimpl_489", "场景值不存在"),
    MARK_SERVICE_MARKETINGREPORTSERVICEIMPL_512("mark.service.marketingreportserviceimpl_512", "客脉访客"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_379("mark.service.marketingtriggerserviceimpl_379", "触发器不存在"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_446("mark.service.marketingtriggerserviceimpl_446", "邮件营销"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_642("mark.service.marketingtriggerserviceimpl_642", "最近天数错误"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_818("mark.service.marketingtriggerserviceimpl_818", "管理员给您分配了一个新的客户消息推送任务，已同步至侧边栏【营销助手】待办列表，请尽快处理"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_847("mark.service.marketingtriggerserviceimpl_847", "【客户群发通知】 \n管理员给您下发了客户群发任务，请前往【群发助手】通知中确认后点击发送"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_930("mark.service.marketingtriggerserviceimpl_930", "微页面:"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_933("mark.service.marketingtriggerserviceimpl_933", "文章:"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_936("mark.service.marketingtriggerserviceimpl_936", "产品:"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_939("mark.service.marketingtriggerserviceimpl_939", "海报:"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1032("mark.service.marketingtriggerserviceimpl_1032", "未找到员工"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1263("mark.service.marketingtriggerserviceimpl_1263", "triggerInstanceId为空"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1367("mark.service.marketingtriggerserviceimpl_1367", "短信参数（接收手机号/模板信息等）有误"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1557("mark.service.marketingtriggerserviceimpl_1557", "sopId传入有误"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1560("mark.service.marketingtriggerserviceimpl_1560", "sop已停止"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1573("mark.service.marketingtriggerserviceimpl_1573", "sop已停用"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1593("mark.service.marketingtriggerserviceimpl_1593", "传入的对象数据不予支持"),
    MARK_SERVICE_MARKETINGTRIGGERSERVICEIMPL_1600("mark.service.marketingtriggerserviceimpl_1600", "未找到营销用户"),
    MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_372("mark.service.marketingusergroupserviceimpl_372", "新建了人群"),
    MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_466("mark.service.marketingusergroupserviceimpl_466", "编辑了人群"),
    MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_490("mark.service.marketingusergroupserviceimpl_490", "启用了人群"),
    MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_493("mark.service.marketingusergroupserviceimpl_493", "停用了人群"),
    MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_511("mark.service.marketingusergroupserviceimpl_511", "删除了人群"),
    MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_591("mark.service.marketingusergroupserviceimpl_591", "从人群移除 "),
    MARK_MATERIAL_MATERIALSHOWSETTINGSERVICEIMPL_99("mark.material.materialshowsettingserviceimpl_99", "菜单类型为空"),
    MARK_SERVICE_MEMBERSERVICEIMPL_424("mark.service.memberserviceimpl_424", "错误的类型"),
    MARK_SERVICE_MEMBERSERVICEIMPL_1773("mark.service.memberserviceimpl_1773", "ea不存在"),
    MARK_SERVICE_NOTICESERVICEIMPL_529("mark.service.noticeserviceimpl_529", "上游企业"),
    MARK_SERVICE_NOTICESERVICEIMPL_551("mark.service.noticeserviceimpl_551", "推广任务通知"),
    MARK_SERVICE_NOTICESERVICEIMPL_560("mark.service.noticeserviceimpl_560", "任务号"),
    MARK_SERVICE_NOTICESERVICEIMPL_562("mark.service.noticeserviceimpl_562", "任务类型"),
    MARK_SERVICE_NOTICESERVICEIMPL_562_1("mark.service.noticeserviceimpl_562_1", "伙伴推广任务"),
    MARK_SERVICE_NOTICESERVICEIMPL_564("mark.service.noticeserviceimpl_564", "执行人"),
    MARK_SERVICE_NOTICESERVICEIMPL_564_1("mark.service.noticeserviceimpl_564_1", "我"),
    MARK_SERVICE_NOTICESERVICEIMPL_566("mark.service.noticeserviceimpl_566", "分派人"),
    MARK_SERVICE_NOTICESERVICEIMPL_1408("mark.service.noticeserviceimpl_1408", "您有一个新的推广任务"),
    MARK_SERVICE_NOTICESERVICEIMPL_1417("mark.service.noticeserviceimpl_1417", "宣传语："),
    MARK_SERVICE_NOTICESERVICEIMPL_2946("mark.service.noticeserviceimpl_2946", "任务分派提醒"),
    MARK_SERVICE_NOTICESERVICEIMPL_2956_1("mark.service.noticeserviceimpl_2956_1", "优惠券活动参与提醒"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_197("mark.service.officialwebsiteserviceimpl_197", "页面名称"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_198("mark.service.officialwebsiteserviceimpl_198", "页面URL"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_255("mark.service.officialwebsiteserviceimpl_255", "官网全站页面"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_527("mark.service.officialwebsiteserviceimpl_527", "跟踪页面不能超过"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_979("mark.service.officialwebsiteserviceimpl_979", "找不到导入的文件"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_984("mark.service.officialwebsiteserviceimpl_984", "导入的文件为空"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_988("mark.service.officialwebsiteserviceimpl_988", "导入模板不对"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_1035("mark.service.officialwebsiteserviceimpl_1035", "客服线索明细"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_1344("mark.service.officialwebsiteserviceimpl_1344", "创建时间"),
    MARK_SERVICE_OUTUSERMARKETINGACTIONSERVICEIMPL_55("mark.service.outusermarketingactionserviceimpl_55", "objectApiName和objectId不能为空"),
    MARK_SERVICE_OUTUSERMARKETINGACTIONSERVICEIMPL_61("mark.service.outusermarketingactionserviceimpl_61", "对象id不存在"),
    MARK_SERVICE_OUTUSERMARKETINGACTIONSERVICEIMPL_66("mark.service.outusermarketingactionserviceimpl_66", "获取营销用户失败"),
    MARK_PAY_MERCHANTSERVICEIMPL_35("mark.pay.merchantserviceimpl_35", "appSecret为空"),
    MARK_PAY_MERCHANTSERVICEIMPL_36("mark.pay.merchantserviceimpl_36", "appSecret长度必须为32"),
    MARK_PAY_MERCHANTSERVICEIMPL_37("mark.pay.merchantserviceimpl_37", "appV3Secret长度必须为32"),
    MARK_PHOTOLIBRARY_PHOTOLIBRARYSERVICEIMPL_891("mark.photolibrary.photolibraryserviceimpl_891", "未命名"),
    MARK_SERVICE_PRODUCTSERVICEIMPL_306("mark.service.productserviceimpl_306", "已开启试用,无法删除"),
    MARK_QR_QRCODESERVICEIMPL_203("mark.qr.qrcodeserviceimpl_203", "创建专属海报二维码失败,请联系管理员"),
    MARK_QR_QRCODESERVICEIMPL_217("mark.qr.qrcodeserviceimpl_217", "-多人活码"),
    MARK_QYWX_CARDSERVICEIMPL_342("mark.qywx.cardserviceimpl_342", "企微二维码"),
    MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_251("mark.qywx.customizeformdataserviceimpl_251", "设备信息为空"),
    MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_253("mark.qywx.customizeformdataserviceimpl_253", "未找到相应的小程序帐号"),
    MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_254("mark.qywx.customizeformdataserviceimpl_254", "营销通的托管小程序未配置"),
    MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_256("mark.qywx.customizeformdataserviceimpl_256", "商家号信息未配置"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_388("mark.qywx.groupsendmessageserviceimpl_388", "没有待发送的客户"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_392("mark.qywx.groupsendmessageserviceimpl_392", "参数长度超过最大限制"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_396("mark.qywx.groupsendmessageserviceimpl_396", "未设置消息封面"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_452("mark.qywx.groupsendmessageserviceimpl_452", "等待执行"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_455("mark.qywx.groupsendmessageserviceimpl_455", "任务执行中"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_458("mark.qywx.groupsendmessageserviceimpl_458", "发送成功"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_461("mark.qywx.groupsendmessageserviceimpl_461", "发送失败"),
    MARK_QYWX_GROUPSENDMESSAGESERVICEIMPL_467("mark.qywx.groupsendmessageserviceimpl_467", "无效"),
    MARK_QYWX_QYWXCONTACTSERVICEIMPL_599("mark.qywx.qywxcontactserviceimpl_599", "跟进客户: "),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_140("mark.qywx.qywxstaffserviceimpl_140", "名片开通状态"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_141("mark.qywx.qywxstaffserviceimpl_141", "名片加企微好友数"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_142("mark.qywx.qywxstaffserviceimpl_142", "员工激活状态"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_143("mark.qywx.qywxstaffserviceimpl_143", "激活时间"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_144("mark.qywx.qywxstaffserviceimpl_144", "过期时间"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_145("mark.qywx.qywxstaffserviceimpl_145", "身份绑定状态"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_146("mark.qywx.qywxstaffserviceimpl_146", "身份绑定时间"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_722("mark.qywx.qywxstaffserviceimpl_722", "企微员工列表-"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_762("mark.qywx.qywxstaffserviceimpl_762", "未开通"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_764("mark.qywx.qywxstaffserviceimpl_764", "已开通"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_773("mark.qywx.qywxstaffserviceimpl_773", "未激活"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_775("mark.qywx.qywxstaffserviceimpl_775", "已激活"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_793("mark.qywx.qywxstaffserviceimpl_793", "未绑定"),
    MARK_QYWX_QYWXSTAFFSERVICEIMPL_795("mark.qywx.qywxstaffserviceimpl_795", "已绑定"),
    MARK_QYWX_QYWXWELCOMEMSGSERVICEIMPL_445("mark.qywx.qywxwelcomemsgserviceimpl_445", "欢迎语id为空"),
    MARK_SERVICE_SETTINGSERVICEIMPL_829("mark.service.settingserviceimpl_829", "该小程序有任务正在执行中，请稍后"),
    MARK_SERVICE_SETTINGSERVICEIMPL_836("mark.service.settingserviceimpl_836", "有未完成的发布流程"),
    MARK_SERVICE_SETTINGSERVICEIMPL_856("mark.service.settingserviceimpl_856", "小程序隐私设置失败"),
    MARK_SERVICE_SETTINGSERVICEIMPL_915("mark.service.settingserviceimpl_915", "小程序未处于待发布状态"),
    MARK_SERVICE_SETTINGSERVICEIMPL_1674("mark.service.settingserviceimpl_1674", "营销运营周报("),
    MARK_SERVICE_SETTINGSERVICEIMPL_1677("mark.service.settingserviceimpl_1677", "上周营销推广 "),
    MARK_SERVICE_SETTINGSERVICEIMPL_1678("mark.service.settingserviceimpl_1678", " 次，新增线索 "),
    MARK_SERVICE_SETTINGSERVICEIMPL_1678_1("mark.service.settingserviceimpl_1678_1", " 条"),
    MARK_SMS_APPLYSERVICEIMPL_255("mark.sms.applyserviceimpl_255", "试用赠送"),
    MARK_SMS_SMSQUOTANOTICESETTINGSERVICEIMPL_163("mark.sms.smsquotanoticesettingserviceimpl_163", "短信剩余配额不足"),
    MARK_SMS_SMSQUOTANOTICESETTINGSERVICEIMPL_167("mark.sms.smsquotanoticesettingserviceimpl_167", "短信配额剩余"),
    MARK_SMS_SMSQUOTANOTICESETTINGSERVICEIMPL_167_1("mark.sms.smsquotanoticesettingserviceimpl_167_1", "条，请管理员及时充值，避免影响业务。"),
    MARK_SERVICE_STATISTICSERVICEIMPL_249("mark.service.statisticserviceimpl_249", "开始时间不能为空"),
    MARK_SERVICE_STATISTICSERVICEIMPL_250("mark.service.statisticserviceimpl_250", "结束时间不能为空"),
    MARK_SERVICE_STATISTICSERVICEIMPL_251("mark.service.statisticserviceimpl_251", "时间范围错误"),
    MARK_SERVICE_STATISTICSERVICEIMPL_564_2("mark.service.statisticserviceimpl_564_2", "收到总任务数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_564_3("mark.service.statisticserviceimpl_564_3", "已推广任务数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_564_4("mark.service.statisticserviceimpl_564_4", "推广率"),
    MARK_SERVICE_STATISTICSERVICEIMPL_564_5("mark.service.statisticserviceimpl_564_5", "累计推广次数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_564_8("mark.service.statisticserviceimpl_564_8", "提交表单数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_820("mark.service.statisticserviceimpl_820", "互联对接企业名称"),
    MARK_SERVICE_STATISTICSERVICEIMPL_820_1("mark.service.statisticserviceimpl_820_1", "参与推广对接企业员工数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_995("mark.service.statisticserviceimpl_995", "物料类型不能为空"),
    MARK_SERVICE_STATISTICSERVICEIMPL_996("mark.service.statisticserviceimpl_996", "趋势数据开始时间不能为空"),
    MARK_SERVICE_STATISTICSERVICEIMPL_997("mark.service.statisticserviceimpl_997", "趋势数据结束时间不能为空"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1000("mark.service.statisticserviceimpl_1000", "物料不存在"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1675("mark.service.statisticserviceimpl_1675", "未知员工"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1770("mark.service.statisticserviceimpl_1770", "未知部门"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1966_2("mark.service.statisticserviceimpl_1966_2", "用户类型"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1966_4("mark.service.statisticserviceimpl_1966_4", "被转发次数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1966_5("mark.service.statisticserviceimpl_1966_5", "被转发人数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3082_2("mark.service.statisticserviceimpl_3082_2", "收到群发数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3082_3("mark.service.statisticserviceimpl_3082_3", "已群发数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3082_4("mark.service.statisticserviceimpl_3082_4", "群发率"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3082_5("mark.service.statisticserviceimpl_3082_5", "送达客户数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3265_2("mark.service.statisticserviceimpl_3265_2", "收到任务数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3265_3("mark.service.statisticserviceimpl_3265_3", "已发布数"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3265_4("mark.service.statisticserviceimpl_3265_4", "发布率"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_1495("mark.usermarketingaccount.usermarketingaccountserviceimpl_1495", "访问明细"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_1536("mark.usermarketingaccount.usermarketingaccountserviceimpl_1536", "访问总时长"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2073("mark.usermarketingaccount.usermarketingaccountserviceimpl_2073", "对象不存在,请确认"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2086("mark.usermarketingaccount.usermarketingaccountserviceimpl_2086", "营销用户详情不存在"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2155("mark.usermarketingaccount.usermarketingaccountserviceimpl_2155", "ea不能为空"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2158("mark.usermarketingaccount.usermarketingaccountserviceimpl_2158", "openId不能为空"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2161("mark.usermarketingaccount.usermarketingaccountserviceimpl_2161", "访客id不能为空"),
    MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2164("mark.usermarketingaccount.usermarketingaccountserviceimpl_2164", "wxAppId不能为空"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_327("mark.usermarketingaccounttag.usermarketingtagserviceimpl_327", "企业不能为空"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_332("mark.usermarketingaccounttag.usermarketingtagserviceimpl_332", "标签名称不符合规范"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_335("mark.usermarketingaccounttag.usermarketingtagserviceimpl_335", "一级标签重名"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_339("mark.usermarketingaccounttag.usermarketingtagserviceimpl_339", "二级标签重名"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_356("mark.usermarketingaccounttag.usermarketingtagserviceimpl_356", "类型非法"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_384("mark.usermarketingaccounttag.usermarketingtagserviceimpl_384", "状态错误"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_393("mark.usermarketingaccounttag.usermarketingtagserviceimpl_393", "该模型为系统模型不允许删除"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_411("mark.usermarketingaccounttag.usermarketingtagserviceimpl_411", "标签名不符合规范"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_412("mark.usermarketingaccounttag.usermarketingtagserviceimpl_412", "模型id"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_438("mark.usermarketingaccounttag.usermarketingtagserviceimpl_438", "该一级标签名称在模型中已存在"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_460("mark.usermarketingaccounttag.usermarketingtagserviceimpl_460", "标签模型id不能为空"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_461("mark.usermarketingaccounttag.usermarketingtagserviceimpl_461", "标签id不能为空"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_463("mark.usermarketingaccounttag.usermarketingtagserviceimpl_463", "老的标签id必须为一级标签id"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_501("mark.usermarketingaccounttag.usermarketingtagserviceimpl_501", "父标签"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_502("mark.usermarketingaccounttag.usermarketingtagserviceimpl_502", "模型不能为空"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_503("mark.usermarketingaccounttag.usermarketingtagserviceimpl_503", "标签名不合法"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_507("mark.usermarketingaccounttag.usermarketingtagserviceimpl_507", "parentTagId不是一级标签"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_509("mark.usermarketingaccounttag.usermarketingtagserviceimpl_509", "父标签ID错误，其不在模型中"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_546("mark.usermarketingaccounttag.usermarketingtagserviceimpl_546", "该标签必须在模型内"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_548("mark.usermarketingaccounttag.usermarketingtagserviceimpl_548", "必须为二级标签"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_553("mark.usermarketingaccounttag.usermarketingtagserviceimpl_553", "父标签必须在模型内"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_632("mark.usermarketingaccounttag.usermarketingtagserviceimpl_632", "该displayKey不存在"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_46("mark.service.webhookserviceimpl_46", "页号、页面大小不能为空"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_68("mark.service.webhookserviceimpl_68", "未获得参数"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_75("mark.service.webhookserviceimpl_75", "triggerType非法"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_94("mark.service.webhookserviceimpl_94", "WebHook的id不能为空"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_96("mark.service.webhookserviceimpl_96", "目标生命状态不合法"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_128("mark.service.webhookserviceimpl_128", "webHookId不能为空"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_141("mark.service.webhookserviceimpl_141", "应用场景不正确"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_155("mark.service.webhookserviceimpl_155", "seceneType非法"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_156("mark.service.webhookserviceimpl_156", "authType非法"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_157("mark.service.webhookserviceimpl_157", "bodyType非法"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_159("mark.service.webhookserviceimpl_159", "秘钥鉴权方式appSecret不能为空"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_161("mark.service.webhookserviceimpl_161", "基本认证鉴权方式username、password不能为空"),
    MARK_SERVICE_WEBHOOKSERVICEIMPL_164("mark.service.webhookserviceimpl_164", "OAuth2鉴权方式getOauthTokenUrl、oauthAppId、oauthSecret不能为空"),
    MARK_WHATSAPP_WHATSAPPSERVICEIMPL_207("mark.whatsapp.whatsappserviceimpl_207", "部分号码发送失败: "),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_610("mark.wxcoupon.publiccouponserviceimpl_610", "objectId为空"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2097("mark.wxcoupon.publiccouponserviceimpl_2097", "经销商参与情况列表"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2099("mark.wxcoupon.publiccouponserviceimpl_2099", "经销商参与情况"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2117("mark.wxcoupon.publiccouponserviceimpl_2117", "门店领取情况列表"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2119("mark.wxcoupon.publiccouponserviceimpl_2119", "门店领取情况"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2151("mark.wxcoupon.publiccouponserviceimpl_2151", "未使用"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2153("mark.wxcoupon.publiccouponserviceimpl_2153", "已使用"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2162("mark.wxcoupon.publiccouponserviceimpl_2162", "企业名称"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2163("mark.wxcoupon.publiccouponserviceimpl_2163", "领取时间"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2164("mark.wxcoupon.publiccouponserviceimpl_2164", "使用状态"),
    MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2211("mark.wxcoupon.publiccouponserviceimpl_2211", "已领取"),
    MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_215("mark.wxcoupon.wxcouponpayserviceimpl_215", "关联的模板id为空"),
    MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_216("mark.wxcoupon.wxcouponpayserviceimpl_216", "关联的活动营销id为空"),
    MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_217("mark.wxcoupon.wxcouponpayserviceimpl_217", "券可用开始时间为空"),
    MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_218("mark.wxcoupon.wxcouponpayserviceimpl_218", "券可用结束时间为空"),
    MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_150("mark.wxthirdplatform.wxthirdauthserviceimpl_150", "小程序已被其他平台绑定"),
    MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_155("mark.wxthirdplatform.wxthirdauthserviceimpl_155", "小程序已被其他企业绑定"),
    MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_186("mark.wxthirdplatform.wxthirdauthserviceimpl_186", "企业已绑定了小程序"),
    MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_203("mark.wxthirdplatform.wxthirdauthserviceimpl_203", "企业专属营销小程序"),
    MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_215("mark.wxthirdplatform.wxthirdauthserviceimpl_215", "更新失败"),
    MARK_WXTHIRDPLATFORM_WXTHIRDAUTHSERVICEIMPL_219("mark.wxthirdplatform.wxthirdauthserviceimpl_219", "设置默认通用介绍页出现异常"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_193("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_193", "平台ID不能为空"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_195("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_195", "条件搜索时，条件类型不能为空"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_317("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_317", "获取代码模板错误，请稍后重试"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_329("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_329", "有任务正在执行中，请稍后</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_336("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_336", "已是最新版本</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_340("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_340", "有未完成的发布流程</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_348("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_348", "设置服务器域名发生错误"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_356("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_356", "设置业务域名发生错误"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_379("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_379", "提交代码发生错误"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_384("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_384", "更新出错："),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_431("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_431", "全部成功！"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_433("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_433", "其余均成功"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_457("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_457", "尚未提交更新</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_461("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_461", "不在待发布步骤</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_470("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_470", "发布失败"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_474("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_474", "发布出错："),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_492("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_492", "全部发布成功！"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_499("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_499", "platformId不能为空"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_501("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_501", "平台ID有误"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_537("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_537", "全部撤回成功！"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_539("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_539", "其余均撤回成功"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_555("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_555", "不在审核过程中</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_560("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_560", "请求微信服务器失败</br>"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_566("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_566", "撤回失败"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_572("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_572", "撤回出错："),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_598("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_598", "更新成功"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_604("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_604", "未收到platformId"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_608("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_608", "platformId不正确"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_612("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_612", "向微信服务器获取版本列表错误"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_620("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_620", "使用最新版本"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTSERVICEIMPL_621("mark.wxthirdplatform.wxthirdplatforminnersupportserviceimpl_621", "动态使用最新版本"),
    MARK_UTIL_CCHMACUTILS_64("mark.util.cchmacutils_64", "java小工匠"),
    MARK_UTIL_CCHMACUTILS_69("mark.util.cchmacutils_69", "HMAC Md5 密钥:"),
    MARK_UTIL_CCHMACUTILS_71("mark.util.cchmacutils_71", "HMAC Md5 加密:"),
    MARK_UTIL_CCHMACUTILS_77("mark.util.cchmacutils_77", "HMAC SHA256 密钥:"),
    MARK_UTIL_CCHMACUTILS_79("mark.util.cchmacutils_79", "HMAC SHA256 加密:"),
    MARK_UTIL_CCHMACUTILS_84("mark.util.cchmacutils_84", "HMAC SHA512 密钥:"),
    MARK_UTIL_CCHMACUTILS_86("mark.util.cchmacutils_86", "HMAC SHA512 加密:"),
    MARK_UTIL_EXCELUTIL_209("mark.util.excelutil_209", "内容类型sheetName不为String类型"),
    MARK_UTIL_EXCELUTIL_218("mark.util.excelutil_218", "内容类型contentType不为String类型"),
    MARK_UTIL_EXCELUTIL_227("mark.util.excelutil_227", "文件名filename不为String类型"),
    MARK_UTIL_EXCELUTIL_249("mark.util.excelutil_249", "不为String 类型"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_41("mark.util.fieldverificationutil_41", ": apiName 不在范围内"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_45("mark.util.fieldverificationutil_45", " 找不到该字段类型"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_48("mark.util.fieldverificationutil_48", ": apiName 重复"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_51("mark.util.fieldverificationutil_51", ": 类型错误"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_98("mark.util.fieldverificationutil_98", " 为必填"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_112("mark.util.fieldverificationutil_112", " 找不到该选项:"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_138("mark.util.fieldverificationutil_138", " 邮件格式错误"),
    MARK_UTIL_FIELDVERIFICATIONUTIL_147("mark.util.fieldverificationutil_147", " 手机格式错误"),
    MARK_QYWX_SHA1_71("mark.qywx.sha1_71", "非法请求参数，有部分参数为空 : "),
    MARK_UTIL_READEXCELUTIL_169("mark.util.readexcelutil_169", "非法字符"),
    MARK_UTIL_READEXCELUTIL_172("mark.util.readexcelutil_172", "未知类型"),
    MARK_UTIL_READEXCELUTIL_181("mark.util.readexcelutil_181", "文件不存在！"),
    MARK_UTIL_READEXCELUTIL_188("mark.util.readexcelutil_188", "不是excel文件"),
    MARK_UTIL_REXUTIL_46("mark.util.rexutil_46", "失败"),
    MARK_UTIL_ARTICLESTATISTICSUTIL_103("mark.util.articlestatisticsutil_103", "  文章下载失败数:"),
    MARK_UTIL_ARTICLESTATISTICSUTIL_103_1("mark.util.articlestatisticsutil_103_1", "  文章解析成功数:"),
    MARK_UTIL_ARTICLESTATISTICSUTIL_103_2("mark.util.articlestatisticsutil_103_2", "  文章解析失败数:"),
    MARK_WXPAY_AESUTIL_83("mark.wxpay.aesutil_83", "微信商家券回调验证签名错误 : "),
    MARK_WXPAY_AESUTIL_125("mark.wxpay.aesutil_125", "微信商家券密文解密错误 : "),
    MARK_WXPAY_AESUTIL_149("mark.wxpay.aesutil_149", "微信商家券获取微信v3证书时发生错误,无法获取响应体"),
    MARK_WXPAY_AESUTIL_170("mark.wxpay.aesutil_170", "微信商家券获取微信v3证书时发生错误,原因: "),
    MARK_WXPAY_AUTHSIGNUTILS_72("mark.wxpay.authsignutils_72", "当前Java环境不支持RSA"),
    MARK_WXPAY_AUTHSIGNUTILS_74("mark.wxpay.authsignutils_74", "无效的密钥格式"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_60("mark.controller.activitycontroller_60", "pageNum 为空"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_61("mark.controller.activitycontroller_61", "pageSize 为空"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_62("mark.controller.activitycontroller_62", "state 为空"),
    MARK_CONTROLLER_DISTRIBUTORCONTROLLER_71("mark.controller.distributorcontroller_71", "分销人员id为空"),
    MARK_CONTROLLER_DISTRIBUTORCONTROLLER_527("mark.controller.distributorcontroller_527", "运营人员为空"),
    MARK_CONTROLLER_DISTRIBUTORCONTROLLER_528("mark.controller.distributorcontroller_528", "物料类型为空"),
    MARK_CONTROLLER_DISTRIBUTORCONTROLLER_529("mark.controller.distributorcontroller_529", "分销计划id空"),
    MARK_CONTROLLER_MARKETINGACTIVITYCONTROLLER_97("mark.controller.marketingactivitycontroller_97", "marketingActivityId 为空"),
    MARK_CONTROLLER_OPERATORCONTROLLER_54("mark.controller.operatorcontroller_54", "运营人员id为空"),
    MARK_CONTROLLER_OPERATORCONTROLLER_55("mark.controller.operatorcontroller_55", "分销计划id为空"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_87("mark.controller.activitycontroller_87", "id 为空"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_134("mark.controller.activitycontroller_134", "arg 为空"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_174("mark.controller.activitycontroller_174", "activityId 为空"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_231("mark.controller.activitycontroller_231", "活动标题"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_234("mark.controller.activitycontroller_234", "活动地址"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_235("mark.controller.activitycontroller_235", "活动人数"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_236("mark.controller.activitycontroller_236", "活动报名截止时间"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_237("mark.controller.activitycontroller_237", "用户微信昵称"),
    MARK_CONTROLLER_ACTIVITYCONTROLLER_242("mark.controller.activitycontroller_242", "报名时间"),
    MARK_CONFERENCE_CONFERENCECONTROLLER_602("mark.conference.conferencecontroller_602", "邀约人员列表"),
    MARK_DISTRIBUTION_CLUECONTROLLER_127("mark.distribution.cluecontroller_127", "线索id为空"),
    MARK_DISTRIBUTION_DISTRIBUTORCONTROLLER_66("mark.distribution.distributorcontroller_66", "不是应用管理员"),
    MARK_CONTROLLER_ENTERPRISEOBJECTSTATISTICCONTROLLER_111("mark.controller.enterpriseobjectstatisticcontroller_111", "objectId 为空"),
    MARK_CONTROLLER_ENTERPRISEOBJECTSTATISTICCONTROLLER_150("mark.controller.enterpriseobjectstatisticcontroller_150", "员工名称"),
    MARK_CONTROLLER_ENTERPRISEOBJECTSTATISTICCONTROLLER_151("mark.controller.enterpriseobjectstatisticcontroller_151", "员工所在部门"),
    MARK_CONTROLLER_ENTERPRISEOBJECTSTATISTICCONTROLLER_153("mark.controller.enterpriseobjectstatisticcontroller_153", "互动人数"),
    MARK_CONTROLLER_ENTERPRISEOBJECTSTATISTICCONTROLLER_157("mark.controller.enterpriseobjectstatisticcontroller_157", "表单线索数"),
    MARK_CONTROLLER_ENTERPRISESPREADCONTROLLER_63("mark.controller.enterprisespreadcontroller_63", "搜索类型为null"),
    MARK_CONTROLLER_ENTERPRISESPREADCONTROLLER_73("mark.controller.enterprisespreadcontroller_73", "营销活动ID为空"),
    MARK_CONTROLLER_ENTERPRISESPREADCONTROLLER_115("mark.controller.enterprisespreadcontroller_115", "触发器ID为空"),
    MARK_CONTROLLER_ENTERPRISESPREADCONTROLLER_156("mark.controller.enterprisespreadcontroller_156", "查询类型为空"),
    MARK_CONTROLLER_ENTERPRISESTATISTICCONTROLLER_129("mark.controller.enterprisestatisticcontroller_129", "时间"),
    MARK_CONTROLLER_ENTERPRISESTATISTICCONTROLLER_132("mark.controller.enterprisestatisticcontroller_132", "推广内容数量"),
    MARK_CONTROLLER_ENTERPRISESTATISTICCONTROLLER_133("mark.controller.enterprisestatisticcontroller_133", "全员推广次数"),
    MARK_CONTROLLER_FORMDATACONTROLLER_94("mark.controller.formdatacontroller_94", "产品试用"),
    MARK_CONTROLLER_FORMDATACONTROLLER_114("mark.controller.formdatacontroller_114", "推广人名称"),
    MARK_CONTROLLER_FORMDATACONTROLLER_117("mark.controller.formdatacontroller_117", "微信用户"),
    MARK_CONTROLLER_MARKETINGUSERGROUPCONTROLLER_63("mark.controller.marketingusergroupcontroller_63", "查询ID为null"),
    MARK_CONTROLLER_MARKETINGUSERGROUPCONTROLLER_111("mark.controller.marketingusergroupcontroller_111", "id值为空"),
    MARK_CONTROLLER_NOTICECONTROLLER_59("mark.controller.noticecontroller_59", "用户Id为空"),
    MARK_CONTROLLER_OBJECTDESCRIPTIONCONTROLLER_36("mark.controller.objectdescriptioncontroller_36", "apiName为空"),
    MARK_CONTROLLER_SETTINGCONTROLLER_318("mark.controller.settingcontroller_318", "介绍页id不能为空"),
    MARK_CONTROLLER_SETTINGCONTROLLER_391("mark.controller.settingcontroller_391", "会议链接"),
    MARK_CONTROLLER_SETTINGCONTROLLER_392("mark.controller.settingcontroller_392", "门票链接"),
    MARK_CONTROLLER_SETTINGCONTROLLER_394("mark.controller.settingcontroller_394", "名字"),
    MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGCONTROLLER_139("mark.usermarketingaccounttag.usermarketingtagcontroller_139", "displayKey 格式错误"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTCONTROLLER_73("mark.wxthirdplatform.wxthirdplatforminnersupportcontroller_73", "未提交任何小程序"),
    MARK_WXTHIRDPLATFORM_WXTHIRDPLATFORMINNERSUPPORTCONTROLLER_123("mark.wxthirdplatform.wxthirdplatforminnersupportcontroller_123", "只允许在纷享云操作"),
    MARK_IMPL_CLUECONTROLLER_58("mark.impl.cluecontroller_58", "线索状态为空"),
    MARK_IMPL_CLUECONTROLLER_59("mark.impl.cluecontroller_59", "pageSize为空"),
    MARK_IMPL_CLUECONTROLLER_60("mark.impl.cluecontroller_60", "pageNum为空"),
    MARK_IMPL_CUSTOMIZEMINIAPPCARDNAVBARCONTROLLER_40("mark.impl.customizeminiappcardnavbarcontroller_40", "cardUid不能为空"),
    MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_443("mark.outer.outerofficialwebsitecontroller_443", "header未携带referer"),
    MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_447("mark.outer.outerofficialwebsitecontroller_447", "未配置官网"),
    MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_454("mark.outer.outerofficialwebsitecontroller_454", "referer异常"),
    MARK_OUTER_OUTEROFFICIALWEBSITECONTROLLER_463("mark.outer.outerofficialwebsitecontroller_463", "非官网请求"),
    MARK_CONFERENCE_IMPORTMANAGER_662("mark.conference.importmanager_662", "导入参会人员错误报告"),
    MARK_CONFERENCE_IMPORTMANAGER_706("mark.conference.importmanager_706", "导入表单数据错误报告"),
    MARK_CONFERENCE_IMPORTMANAGER_891("mark.conference.importmanager_891", "导入邀约人员错误报告"),
    MARK_CONFERENCE_CONFERENCESERVICEIMPL_3507("mark.conference.conferenceserviceimpl_3507", "导入的签到数据结果_"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_660("mark.service.customizeformdataserviceimpl_660", "表单导入数据结果_"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_1632("mark.service.customizeformdataserviceimpl_1632", "数据"),
    MARK_SERVICE_CUSTOMIZEFORMDATASERVICEIMPL_3096("mark.service.customizeformdataserviceimpl_3096", "线索明细"),
    MARK_DISTRIBUTION_CLUESERVICEIMPL_383("mark.distribution.clueserviceimpl_383", "营销数据概况"),
    MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_233("mark.distribution.distributorserviceimpl_233", "表单数据"),
    MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1391("mark.service.enterprisespreadstatisticserviceimpl_1391", "客户朋友圈-发表明细"),
    MARK_SERVICE_FILESERVICEIMPL_362("mark.service.fileserviceimpl_362", "批量下载包"),
    MARK_SERVICE_FSBINDSERVICEIMPL_552("mark.service.fsbindserviceimpl_552", "员工名片开通情况"),
    MARK_SERVICE_OFFICIALWEBSITESERVICEIMPL_485("mark.service.officialwebsiteserviceimpl_485", "官网追踪页面-"),
    MARK_SERVICE_STATISTICSERVICEIMPL_558("mark.service.statisticserviceimpl_558", "员工推广排行"),
    MARK_SERVICE_STATISTICSERVICEIMPL_814("mark.service.statisticserviceimpl_814", "伙伴推广排行"),
    MARK_SERVICE_STATISTICSERVICEIMPL_1960("mark.service.statisticserviceimpl_1960", "用户转发排行"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3076("mark.service.statisticserviceimpl_3076", "员工群发排行"),
    MARK_SERVICE_STATISTICSERVICEIMPL_3259("mark.service.statisticserviceimpl_3259", "员工发布排行"),
    MARK_CONFERENCE_CONFERENCECONTROLLER_559("mark.conference.conferencecontroller_559", "导入模板"),
    MARK_CONTROLLER_ENTERPRISEOBJECTSTATISTICCONTROLLER_144("mark.controller.enterpriseobjectstatisticcontroller_144", "员工推广数据"),
    MARK_CONTROLLER_FORMDATACONTROLLER_95("mark.controller.formdatacontroller_95", "产品试用数据"),
    MARK_RESULT_SHERRORCODE_437_1("mark.result.sherrorcode_437_1", "没有有效手机号"),
    MARK_RESULT_SHERRORCODE_437_2("mark.result.sherrorcode_437_2", "上传的Excel中变量名称与此短信模板中的变量名称不一致，请检查后重新上传"),
    MARK_RESULT_SHERRORCODE_547_1("mark.result.sherrorcode_547", "导入参会人员文件失败，请确认导入文件是否过大"),
    MARK_RESULT_SHERRORCODE_1088("mark.result.sherrorcode_1088", "不支持的账号类型"),
    MARK_USERRELATION_USER_TYPE("mark.userrelation.userType", "类型"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_1("mark.userrelation.userrelationserviceimpl_1", "初始用户类型"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_2("mark.userrelation.userrelationserviceimpl_2", "CRM账号"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_3("mark.userrelation.userrelationserviceimpl_3", "企微账号"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_4("mark.userrelation.userrelationserviceimpl_4", "伙伴账号"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_5("mark.userrelation.userrelationserviceimpl_5", "钉钉账号"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_6("mark.userrelation.userrelationserviceimpl_6", "公众号粉丝"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_7("mark.userrelation.userrelationserviceimpl_7", "小程序用户"),
    MARK_USERRELATION_USERRELATIONSERVICEIMPL_8("mark.userrelation.userrelationserviceimpl_8", "关联会员"),

    MARK_RESULT_SHERRORCODE_961000("mark.result.sherrorcode_961000", "CTA组件不存在"),
    MARK_RESULT_SHERRORCODE_961001("mark.result.sherrorcode_961001", "部分CTA组件不存在,请刷新"),
    MARK_RESULT_SHERRORCODE_961002("mark.result.sherrorcode_961002", "当前还有%s个内容关联此CTA组件，请取消关联后再删除"),
    MARK_RESULT_SHERRORCODE_961003("mark.result.sherrorcode_961003", "CTA组件至少使用一种触发方式"),
    MARK_RESULT_SHERRORCODE_961004("mark.result.sherrorcode_961004", "CTA组件至少使用一个引导组件"),
    MARK_RESULT_SHERRORCODE_961005("mark.result.sherrorcode_961005", "CTA组件最多使用三个引导组件"),
    MARK_RESULT_SHERRORCODE_961006("mark.result.sherrorcode_961006", "CTA组件名称不允许重复"),

    MARK_APP_MENU_TEMPLATE_COPY("mark.app.menu.template.copy", "副本"),
    MARK_APP_MENU_TEMPLATE_SYSTEM("mark.app.menu.template.system", "预置员工推广工作台"),
    MARK_APP_MENU_TEMPLATE_TITLE("mark.app.menu.template.title", "纷享营销通"),
    MENU_IS_NOT_OBJECT_GROUP_RULE("menu_is_not_object_group_rule", "菜单没有配置分组规则"),
    MART_APP_MENU_PARTNER_TEMPLATE("mark.app.menu.partner.tempalate", "预置伙伴推广工作台"),
    MART_APP_MENU_MEMBER_TEMPLATE("mark.app.menu.member.template", "预置会员推广工作台"),
    MARK_APP_MENU_PARTNER_TEMPLATE_TITLE("mark.app.menu.partner.template.title", "营销助手"),
    EMPLOYEE_TYPE_FORBID_BIND_PARTNER_CARD("mark.employee_type_forbid_bind_partner_card", "当前号码已经绑定上游企业,上游企业:"),
    MEMBER_TYPE_FORBID_BIND_PARTNER_CARD("mark.member_type_forbid_bind_partner_card", "当前号码已经绑定上游推广会员,上游企业:"),
    PARTNER_NOT_EXIST_USER_RELATION("mark.partner.not.exist.user.relation", "互联用户不存在使用成员身份"),
    PARTNER_USER_RELATION_NOT_EQUAL("mark.partner.user.relation.not.equal", "互联用户使用成员身份不一致"),
    PARTNER_BOUNDED_ANOTHER_USER("mark.partner.bounded.another.user", "当前互联用户也绑定至其他用户"),
    CREATE_USER_FAIL("mark.create.user.fail", "生成用户失败"),
    ILLEGAL_REQUEST("mark.illegal.request", "非法请求"),
    PARTNER_NOT_EXIST("mark.partner.not.exist", "互联用户不存在"),
    MOBILE_BOUND_UPSTREAM_EA("mark.mobile.bound.upstream.ea", "当前登录手机号已经在上游企业中绑定,暂时无法使用,绑定企业:"),
    MOBILE_BOUND_OTHER_PUBLIC_ENTERPRISE("mark.mobile.bound.other.public.enterprise", "当前登录手机号已经在其他企业中绑定,暂时无法使用,绑定企业:"),

    ;

    private String i18nKey;
    private String defaultValue;

    I18nKeyEnum(String i18nKey, String defaultValue) {
        this.i18nKey = i18nKey;
        this.defaultValue = defaultValue;
    }

    public String getI18nKey() {
        return i18nKey;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public static I18nKeyEnum getByI18nKey(String i18nKey) {
        for (I18nKeyEnum i18nKeyEnum : I18nKeyEnum.values()) {
            if (i18nKeyEnum.getI18nKey().equals(i18nKey)) {
                return i18nKeyEnum;
            }
        }
        return null;
    }
}
