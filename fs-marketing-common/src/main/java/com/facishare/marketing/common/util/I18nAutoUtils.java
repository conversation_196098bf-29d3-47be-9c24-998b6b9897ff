package com.facishare.marketing.common;

import lombok.Data;
import lombok.SneakyThrows;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.reflections.Reflections;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
public class I18nAutoUtils {

    public static void main(String[] args) {
        relpaceFile("D:\\FS\\soft\\IdeaProjects\\fs-marketing\\fs-marketing-api\\src\\main\\java\\com\\facishare\\marketing\\api\\arg\\usermarketingaccounttag\\UpdateTagArg.java");
    }

    private static boolean replaceEnable = true;
    private static final String regex = "([\"])(?:(?!\\1).)*?[\\u4e00-\\u9fa5](?:(?!\\1).)*?\\1";
    private static final Map<String, I18nKeyEnumKey> i18nKeyEnums = new LinkedHashMap<>();

    @SneakyThrows
    private static void relpaceFile(String path) {
        List<FileInfo> fileInfos = listFiles(new File(path));
        int totalLineSize = 0;
        int replaceableSize = 0;
        //relpace java
        for (FileInfo fileInfo : fileInfos) {
            List<LineInfo> lines = fileInfo.lines;
            System.out.println("#########" + fileInfo.getFile().getAbsolutePath() + ":" + lines.size());
            List<String> updatedLines = Files.readAllLines(fileInfo.file.toPath());
            totalLineSize = totalLineSize + lines.size();
            for (LineInfo line : lines) {
                if (line.replaceable) {
                    updatedLines.set(line.lineNumber, line.newLine);
                    System.out.println(line.lineNumber + ":" + line.line);
                    replaceableSize++;
                }
            }
            if (replaceEnable) {
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileInfo.file))) {
                    writer.write(updatedLines.get(0));
                    writer.newLine();
                    if (updatedLines.get(1).equals("")) {
                        writer.newLine();
                    } else {
                        writer.write(updatedLines.get(1));
                        writer.newLine();
                    }
                    if (!fileInfo.hasI18nKeyEnumPacakge) {
                        writer.write("import com.facishare.marketing.common.enums.I18nKeyEnumV3;");
                        writer.newLine();
                        writer.write("import com.facishare.marketing.common.util.I18nUtil;");
                        writer.newLine();
                    }
                    for (int i = 2; i < updatedLines.size(); i++) {
                        String updatedLine = updatedLines.get(i);
                        writer.write(updatedLine);
                        if (i < updatedLines.size() - 1) {
                            writer.newLine();
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //relpace enums
        System.out.println("#################I18nKeyEnumKey################# ");
        Set<String> set = new HashSet<>();
        for (I18nKeyEnumKey newI18nKeyEnum : i18nKeyEnums.values().stream().collect(Collectors.toList())) {
            if (set.contains(newI18nKeyEnum.getI18nKey())) {
                continue;
            }
            set.add(newI18nKeyEnum.getI18nKey());
            String enumStr = newI18nKeyEnum.getEnumKey() + "(\"" + newI18nKeyEnum.i18nKey + "\", \"" + newI18nKeyEnum.defaultValue + "\"),";
            System.out.println(enumStr);
        }
        System.out.println("#################TOTAL################# ");
        System.out.println("file size = " + fileInfos.size() + ",totalLineSize=" + totalLineSize + ", I18nKeyEnumKey size = " + set.size());
        System.out.println("replaceSize=" + replaceableSize);
    }

    @SneakyThrows
    private static List<FileInfo> listFiles(File targetFile) {
        List<FileInfo> files = new ArrayList<>();
        File[] fileList = new File[1];
        if (!targetFile.isDirectory()) {
            fileList[0] = targetFile;
        } else {
            fileList = targetFile.listFiles();
        }
        for (File file : fileList) {
            if (file.isDirectory()) {
                files.addAll(listFiles(file));
            } else {
                if (file.getName().endsWith(".java") && !file.getAbsolutePath().contains("src\\test")) {
                    FileInfo fileInfo = new FileInfo();
                    fileInfo.file = file;
                    List<LineInfo> newLineInfos = new ArrayList<>();
                    List<String> lines = Files.readAllLines(file.toPath());
                    fileInfo.lines = newLineInfos;
                    for (int lineNumber = 0; lineNumber < lines.size(); lineNumber++) {
                        String line = lines.get(lineNumber);
                        if (line.contains("I18nKeyEnum")) {
                            fileInfo.hasI18nKeyEnumPacakge = true;
                        }
                        String newLine = newLine(line, file, lineNumber);
                        if (newLine != null && !newLine.equals(line)) {
                            LineInfo lineInfo = new LineInfo();
                            lineInfo.line = line;
                            lineInfo.lineNumber = lineNumber;
                            lineInfo.newLine = newLine;
                            newLineInfos.add(lineInfo);
                        }
                    }
                    if (newLineInfos.size() > 0) {
                        files.add(fileInfo);
                    }
                }
            }
        }
        return files;
    }

    public static String newLine(String line, File file, int lineNumber) {
        if (filter(file, line)) {
            return null;
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(line);
        StringBuffer result = new StringBuffer();

        int idx = 0;
        while (matcher.find()) {
            String str = matcher.group(0);
            String key = str.substring(1, str.length() - 1);
            I18nKeyEnumKey i18nKeyEnumKey = i18nKeyEnums.get(key);
            if (i18nKeyEnumKey == null) {
                i18nKeyEnumKey = new I18nKeyEnumKey();
                i18nKeyEnumKey.i18nKey = generateI18nKey(file, lineNumber, idx);
                i18nKeyEnumKey.enumKey = i18nKeyEnumKey.i18nKey.replace(".", "_").toUpperCase();
                i18nKeyEnumKey.defaultValue = key;
                i18nKeyEnums.put(key, i18nKeyEnumKey);
            }
            String replacement = "I18nUtil.get(I18nKeyEnumV3." + i18nKeyEnumKey.enumKey + ")";
            matcher.appendReplacement(result, replacement);
            idx++;
        }
        matcher.appendTail(result);

        if (result.toString().contains("I18nUtil")) {
//            System.out.println("替换前:" + line);
//            System.out.println("替换后:" + result.toString());
//            System.out.println("---------------------------");
            return result.toString();
        }
        return null;
    }


    private static boolean filter(File file, String line) {
        line = line.trim();
        if (line.startsWith("@")) {
            return true;
        }
        if (line.startsWith("//") || line.startsWith("/*") || line.startsWith("*") ||
                line.startsWith("log.") || line.contains(".warn(") || line.contains(".info(") ||
                line.contains(".compile(") || line.contains("[^\\\\.") ||
                line.contains(".replace(") ||
                line.contains(".equals(") ||
                line.contains(".format(") ||
                line.contains(".setFieldDescribe(") ||
                line.contains(".setLayoutList(") ||
                line.contains("final") || line.contains("static") ||
                line.contains("label") ||
                line.contains(".html") ||
                line.contains("help_text") ||
                line.contains("objectDescribe.put(") ||
                line.contains("<p ") ||
                line.contains("SpreadChannelManager.promotionChannelMap.get") ||
                line.contains(".setSource(") ||
                line.contains(".setLabel(") ||
                line.contains(".put(\"header\"") ||
                line.contains(".getName().contains(") ||
                line.contains("new Font(")
        ) {
            return true;
        }

        if (line.contains("{") && line.contains("}")) {
            return true;
        }


        String fileName = file.getName();
        if (fileName.contains("SHErrorCode")) {
            return false;
        }
        List<String> fileNameFilters = Arrays.asList(
                "I18nAutoUtils",
//                "Enum",
                "Migrate",
                "ArticleConstants",
                "AdCommonManager",
                "ScheduleType",
                "AddArg",
                "ResetConferenceManager",
                "MarketingPromotionSourceObjManager",
                "PaasUtil"
        );
        if (fileNameFilters.stream().anyMatch(e -> fileName.contains(e))) {
            return true;
        }

        String path = file.getAbsolutePath();
        List<String> pathFilters = Arrays.asList(
//                "fs-marketing-common",
                "fs-marketing-task",
                "fs-marketing-outapi",
//                "fs-marketing-api",
                "swagger",
                "config",
                "dao",
//                "arg",
                "sharegpt",
                "crmobjectcreator"
        );
        if (pathFilters.stream().anyMatch(e -> path.contains(e))) {
            return true;
        }

        return false;
    }

    private static String generateI18nKey(File file, int lineNumber, int lineNumberIdx) {
        String name = file.getName().replace(".java", "");
        String packageName = file.getParentFile().getName();
        if (lineNumberIdx == 0) {
            return ("MARK." + packageName + "." + name + "_" + lineNumber).toLowerCase();
        } else {
            return ("MARK." + packageName + "." + name + "_" + lineNumber + "_" + lineNumberIdx).toLowerCase();
        }
    }

    @Data
    private static class FileInfo {
        private File file;
        private List<LineInfo> lines;
        private boolean hasI18nKeyEnumPacakge = false;
    }

    private static class LineInfo {
        public String newLine;
        public String line;
        public int lineNumber;
        public boolean replaceable = true;
    }

    @Data
    private static class I18nKeyEnumKey {
        private String enumKey;
        private String i18nKey;
        private String defaultValue;
    }
//    public static void main(String[] args) {
//        try {
//            List<String> results = scanForChineseCharacters("com.facishare.marketing"); // 替换为你的包名
//            System.out.println("---------开始打印---------");
//            results.forEach(System.out::println);
//            System.out.println("---------结束打印---------");
//        } catch (ClassNotFoundException e) {
//            e.printStackTrace();
//        }
//    }


    public static List<String> scanForChineseCharacters(String packageName) throws ClassNotFoundException {
        List<String> variablesWithChinese = new ArrayList<>();

        // 获取包下的所有类
        List<Class<?>> classes = getClassesForPackage(packageName);

        for (Class<?> clazz : classes) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if (Modifier.isStatic(field.getModifiers())) { // 确保字段是静态的
                    field.setAccessible(true);
                    try {
                        if (field.getType() == String.class) {
                            String value = (String) field.get(null);
                            if (value != null && containsChinese(value)) {
                                variablesWithChinese.add(clazz.getName() + "." + field.getName() + " = " + value);
                            }
                        } else if (field.getType() == List.class || field.getType() == Set.class) {
                            Object value = field.get(null);
                            if (value != null) {
                                if (value instanceof List) {
                                    List<?> list = (List<?>) value;
                                    for (Object item : list) {
                                        if (item instanceof String && containsChinese((String) item)) {
                                            variablesWithChinese.add(clazz.getName() + "." + field.getName() + " contains " + item);
                                        }
                                    }
                                } else if (value instanceof Set) {
                                    Set<?> set = (Set<?>) value;
                                    for (Object item : set) {
                                        if (item instanceof String && containsChinese((String) item)) {
                                            variablesWithChinese.add(clazz.getName() + "." + field.getName() + " contains " + item);
                                        }
                                    }
                                }
                            }
                        } else if (field.getType().isArray() && field.getType().getComponentType() == String.class) {
                            String[] array = (String[]) field.get(null);
                            if (array != null) {
                                for (String item : array) {
                                    if (item != null && containsChinese(item)) {
                                        variablesWithChinese.add(clazz.getName() + "." + field.getName() + " contains " + item);
                                    }
                                }
                            }
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return variablesWithChinese;
    }

    private static boolean containsChinese(String value) {
        if (value == null) return false;
        return value.chars().anyMatch(ch -> (ch >= 0x4E00 && ch <= 0x9FFF));
    }

    // 使用 Reflections 库获取包下的所有类
    private static List<Class<?>> getClassesForPackage(String packageName) {
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(packageName))
                .setScanners(new SubTypesScanner(false)));

        Set<Class<?>> classes = reflections.getSubTypesOf(Object.class);
        return new ArrayList<>(classes);
    }
}