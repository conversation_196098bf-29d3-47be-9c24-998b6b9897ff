package com.facishare.marketing.common.flow;

import java.lang.management.ManagementFactory;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiFunction;
import java.util.function.Function;

public final class MemoryFlow {

    private MemoryFlow() {
    }

    public static class PocketFlowException extends RuntimeException {
        public PocketFlowException(String message) {
            super(message);
        }

        public PocketFlowException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    // 性能优化：使用静态变量和原子计数器代替System.nanoTime()
    private static final AtomicLong NODE_ID_COUNTER = new AtomicLong(0);
    private static final String PROCESS_ID = ManagementFactory.getRuntimeMXBean().getName();

    // 性能优化：自定义线程池配置
    private static final ThreadPoolExecutor PARALLEL_EXECUTOR = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(), // 核心线程数
            Runtime.getRuntime().availableProcessors() * 2, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲线程存活时间
            new LinkedBlockingQueue<>(1000), // 任务队列
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "MemoryFlow-" + threadNumber.getAndIncrement());
                    t.setDaemon(true);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
    );

    /**
     * Base class for all nodes in the workflow.
     *
     * @param <P> Type of the result from the prep phase.
     * @param <E> Type of the result from the exec phase.
     *            The return type of post/run is always String (or null for default action).
     */
    public static abstract class BaseNode<P, E> {
        protected Map<String, Object> params = new HashMap<>();
        protected final Map<String, BaseNode<?, ?>> successors = new HashMap<>();
        public static final String DEFAULT_ACTION = "default";

        public BaseNode<P, E> setParams(Map<String, Object> params) {
            this.params = params != null ? new HashMap<>(params) : new HashMap<>();
            return this;
        }

        public <NEXT_P, NEXT_E> BaseNode<NEXT_P, NEXT_E> next(BaseNode<NEXT_P, NEXT_E> node) {
            return next(node, DEFAULT_ACTION);
        }

        public <NEXT_P, NEXT_E> BaseNode<NEXT_P, NEXT_E> next(BaseNode<NEXT_P, NEXT_E> node, String action) {
            Objects.requireNonNull(node, "Successor node cannot be null");
            Objects.requireNonNull(action, "Action cannot be null");
            if (this.successors.containsKey(action)) {
                // Overwriting successor - this is expected behavior in flow setup
            }
            this.successors.put(action, node);
            return node;
        }

        public P prep(Map<String, Object> sharedContext) {
            return null;
        }

        public abstract E exec(P prepResult);

        /**
         * Post method MUST return a String action, or null for the default action.
         */
        public String post(Map<String, Object> sharedContext, P prepResult, E execResult) {
            return null;
        }

        protected E internalExec(P prepResult) {
            return exec(prepResult);
        }

        protected String internalRun(Map<String, Object> sharedContext) {
            // 性能优化：使用更高效的节点ID生成
            String nodeId = this.getClass().getSimpleName() + "_" + PROCESS_ID + "_" + NODE_ID_COUNTER.incrementAndGet();
            String nodeName = this.getClass().getSimpleName();
            String nodeType = this.getClass().getSuperclass().getSimpleName();

            NodeExecutionInfo trackingInfo = GlobalExecutionTracker.trackNodeStart(nodeId, nodeName, nodeType);

            try {
                P prepRes = prep(sharedContext);

                // 记录输入信息 - 性能优化：延迟序列化
                if (trackingInfo != null && GlobalExecutionTracker.isTrackingEnabled()) {
                    trackingInfo.setInputSummary(prepRes);
                }

                E execRes = internalExec(prepRes);
                String postRes = post(sharedContext, prepRes, execRes);

                // 标记节点成功完成
                if (trackingInfo != null) {
                    trackingInfo.markCompleted(execRes);
                    trackingInfo.addMetadata("PostResult", postRes);
                }

                return postRes;
            } catch (Exception e) {
                // 标记节点执行失败
                if (trackingInfo != null) {
                    trackingInfo.markFailed(e);
                }
                throw e;
            }
        }

        public String run(Map<String, Object> sharedContext) {
            if (!successors.isEmpty()) {
                // Note: Node has successors, but run() was called. Successors won't be executed. Use Flow.
            }
            return internalRun(sharedContext);
        }

        protected BaseNode<?, ?> getNextNode(String action) {
            String actionKey = (action != null) ? action : DEFAULT_ACTION;
            BaseNode<?, ?> nextNode = successors.get(actionKey);
            if (nextNode == null && !successors.isEmpty() && !successors.containsKey(actionKey)) {
                // Flow might end: Action not found in successors
            }
            return nextNode;
        }
    }

    /**
     * A synchronous node with built-in retry capabilities.
     */
    public static abstract class Node<P, E> extends BaseNode<P, E> {
        protected final int maxRetries;
        protected final long waitMillis;
        protected int currentRetry = 0;

        public Node() {
            this(1, 0);
        }

        public Node(int maxRetries, long waitMillis) {
            if (maxRetries < 1) throw new IllegalArgumentException("maxRetries must be at least 1");
            if (waitMillis < 0) throw new IllegalArgumentException("waitMillis cannot be negative");
            this.maxRetries = maxRetries;
            this.waitMillis = waitMillis;
        }

        public E execFallback(P prepResult, Exception lastException) throws Exception {
            throw lastException;
        }

        @Override
        protected E internalExec(P prepResult) {
            Exception lastException = null;
            for (currentRetry = 0; currentRetry < maxRetries; currentRetry++) {
                try {
                    return exec(prepResult);
                } catch (Exception e) {
                    lastException = e;
                    if (currentRetry < maxRetries - 1 && waitMillis > 0) {
                        try {
                            TimeUnit.MILLISECONDS.sleep(waitMillis);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new PocketFlowException("Thread interrupted during retry wait", ie);
                        }
                    }
                }
            }
            try {
                if (lastException == null)
                    throw new PocketFlowException("Execution failed, but no exception was captured.");
                return execFallback(prepResult, lastException);
            } catch (Exception fallbackException) {
                if (lastException != null && fallbackException != lastException)
                    fallbackException.addSuppressed(lastException);
                if (fallbackException instanceof RuntimeException) throw (RuntimeException) fallbackException;
                else throw new PocketFlowException("Fallback execution failed", fallbackException);
            }
        }
    }

    /**
     * A synchronous node that processes a list of items individually.
     */
    public static abstract class BatchNode<IN_ITEM, OUT_ITEM> extends Node<List<IN_ITEM>, List<OUT_ITEM>> {
        public BatchNode() {
            super();
        }

        public BatchNode(int maxRetries, long waitMillis) {
            super(maxRetries, waitMillis);
        }

        public abstract OUT_ITEM execItem(IN_ITEM item);

        public OUT_ITEM execItemFallback(IN_ITEM item, Exception lastException) throws Exception {
            throw lastException;
        }

        @Override
        protected List<OUT_ITEM> internalExec(List<IN_ITEM> batchPrepResult) {
            if (batchPrepResult == null || batchPrepResult.isEmpty()) return Collections.emptyList();
            List<OUT_ITEM> results = new ArrayList<>(batchPrepResult.size());
            for (IN_ITEM item : batchPrepResult) {
                Exception lastItemException = null;
                OUT_ITEM itemResult = null;
                boolean itemSuccess = false;
                for (currentRetry = 0; currentRetry < maxRetries; currentRetry++) {
                    try {
                        itemResult = execItem(item);
                        itemSuccess = true;
                        break;
                    } catch (Exception e) {
                        lastItemException = e;
                        if (currentRetry < maxRetries - 1 && waitMillis > 0) {
                            try {
                                TimeUnit.MILLISECONDS.sleep(waitMillis);
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                throw new PocketFlowException("Interrupted batch retry wait: " + item, ie);
                            }
                        }
                    }
                }
                if (!itemSuccess) {
                    try {
                        if (lastItemException == null)
                            throw new PocketFlowException("Item exec failed without exception: " + item);
                        itemResult = execItemFallback(item, lastItemException);
                    } catch (Exception fallbackException) {
                        if (lastItemException != null && fallbackException != lastItemException)
                            fallbackException.addSuppressed(lastItemException);
                        if (fallbackException instanceof RuntimeException) throw (RuntimeException) fallbackException;
                        else throw new PocketFlowException("Item fallback failed: " + item, fallbackException);
                    }
                }
                results.add(itemResult);
            }
            return results;
        }
    }

    /**
     * Orchestrates the execution of a sequence of connected nodes.
     */
    public static class Flow extends BaseNode<Void, String> {
        protected BaseNode<?, ?> startNode;

        public Flow() {
            this(null);
        }

        public Flow(BaseNode<?, ?> startNode) {
            this.start(startNode);
        }

        public <SN_P, SN_E> BaseNode<SN_P, SN_E> start(BaseNode<SN_P, SN_E> startNode) {
            this.startNode = Objects.requireNonNull(startNode, "Start node cannot be null");
            return startNode;
        }

        @Override
        public final String exec(Void prepResult) {
            throw new UnsupportedOperationException("Flow.exec() is internal and should not be called directly.");
        }


        @SuppressWarnings({"unchecked", "rawtypes"}) // Raw types needed for successors map
        protected String orchestrate(Map<String, Object> sharedContext, Map<String, Object> initialParams) {
            if (startNode == null) {
                // Flow started with no start node
                return null;
            }
            BaseNode<?, ?> currentNode = this.startNode;
            String lastAction = null;
            Map<String, Object> currentParams = new HashMap<>(this.params);
            if (initialParams != null) {
                currentParams.putAll(initialParams);
            }
            while (currentNode != null) {
                currentNode.setParams(currentParams);
                lastAction = (String) ((BaseNode) currentNode).internalRun(sharedContext);
                currentNode = currentNode.getNextNode(lastAction);
            }
            return lastAction;
        }

        @Override
        protected String internalRun(Map<String, Object> sharedContext) {
            Void prepRes = prep(sharedContext);
            String orchRes = orchestrate(sharedContext, null);
            return post(sharedContext, prepRes, orchRes);
        }

        /**
         * Post method for the Flow itself. Default returns the last action from orchestration.
         */
        @Override
        public String post(Map<String, Object> sharedContext, Void prepResult, String execResult) {
            return execResult;
        }

        /**
         * 启动流程执行并生成追踪报告
         */
        public FlowExecutionReport runWithTracking(Map<String, Object> sharedContext) {
            String flowId = this.getClass().getSimpleName() + "_" + System.currentTimeMillis();
            GlobalExecutionTracker.startFlow(flowId);

            try {
                run(sharedContext);
                return GlobalExecutionTracker.endFlow();
            } catch (Exception e) {
                FlowExecutionReport report = GlobalExecutionTracker.endFlow();
                // Flow execution failed - exception will be propagated
                throw e;
            }
        }
    }

    /**
     * A flow that runs its entire sequence for each parameter set from `prepBatch`.
     */
    public static abstract class BatchFlow extends Flow {
        public BatchFlow() {
            super();
        }

        public BatchFlow(BaseNode<?, ?> startNode) {
            super(startNode);
        }

        public abstract List<Map<String, Object>> prepBatch(Map<String, Object> sharedContext);

        /**
         * Post method MUST return a String action, or null for the default action.
         */
        public abstract String postBatch(Map<String, Object> sharedContext, List<Map<String, Object>> batchPrepResult);

        @Override
        protected String internalRun(Map<String, Object> sharedContext) {
            List<Map<String, Object>> batchParamsList = this.prepBatch(sharedContext);
            if (batchParamsList == null) {
                batchParamsList = Collections.emptyList();
            }
            for (Map<String, Object> batchParams : batchParamsList) {
                Map<String, Object> currentRunParams = new HashMap<>(this.params);
                if (batchParams != null) {
                    currentRunParams.putAll(batchParams);
                }
                orchestrate(sharedContext, currentRunParams); // Run for side-effects
            }
            return postBatch(sharedContext, batchParamsList);
        }

        @Override
        public final String post(Map<String, Object> sharedContext, Void prepResult, String execResult) {
            throw new UnsupportedOperationException("Use postBatch for BatchFlow, not post.");
        }
    }

    /**
     * A truly parallel node that executes multiple tasks concurrently using CompletableFuture.
     * Each item in the input list is processed in a separate thread.
     *
     * <p><b>超时控制：</b></p>
     * <ul>
     *   <li>timeoutMillis > 0: 指定超时时间（毫秒），超时后抛出 PocketFlowException</li>
     *   <li>timeoutMillis <= 0: 无限等待，直到所有任务完成（适合测试阶段）</li>
     * </ul>
     */
    public static abstract class ParallelNode<IN_ITEM, OUT_ITEM> extends Node<List<IN_ITEM>, List<OUT_ITEM>> {
        protected final long timeoutMillis;
        protected final String nodeName;
        // 新增：任务抽象支持
        protected final List<ParallelTask<IN_ITEM, OUT_ITEM>> tasks;
        protected final Map<String, Object> nodeContext = new HashMap<>();

        /**
         * 默认构造函数，使用30秒超时
         */
        public ParallelNode() {
            this(30000L); // 30 seconds default timeout
        }

        /**
         * 指定超时时间的构造函数
         *
         * @param timeoutMillis 超时时间(毫秒)，设置为 0 或负数表示无限等待
         */
        public ParallelNode(long timeoutMillis) {
            this(timeoutMillis, null);
        }

        /**
         * 完整构造函数 - 原有的execItem方式
         *
         * @param timeoutMillis 超时时间(毫秒)，设置为 0 或负数表示无限等待
         * @param nodeName      节点名称，用于日志和追踪
         */
        public ParallelNode(long timeoutMillis, String nodeName) {
            super();
            this.timeoutMillis = timeoutMillis; // <= 0 表示无限等待
            this.nodeName = nodeName != null ? nodeName : this.getClass().getSimpleName();
            this.tasks = null; // 传统方式不使用任务抽象
        }

        /**
         * 任务抽象构造函数 - 使用ParallelTask列表
         *
         * @param tasks         要并行执行的任务列表
         * @param timeoutMillis 超时时间，≤0表示无限等待
         * @param nodeName      节点名称
         */
        public ParallelNode(List<ParallelTask<IN_ITEM, OUT_ITEM>> tasks, long timeoutMillis, String nodeName) {
            super();
            this.tasks = new ArrayList<>(tasks);
            this.timeoutMillis = timeoutMillis;
            this.nodeName = nodeName != null ? nodeName : "ParallelNode";
        }

        /**
         * 任务抽象构造函数 - 简化版本
         *
         * @param tasks 要并行执行的任务列表
         */
        public ParallelNode(List<ParallelTask<IN_ITEM, OUT_ITEM>> tasks) {
            this(tasks, 30000L, null);
        }

        /**
         * 传统方式：子类实现此方法处理单个item
         */
        public OUT_ITEM execItem(IN_ITEM item) {
            throw new UnsupportedOperationException("Either override execItem() or use task-based constructor");
        }

        public OUT_ITEM execItemFallback(IN_ITEM item, Exception lastException) throws Exception {
            throw lastException;
        }

        /**
         * 获取节点上下文，供任务使用
         */
        public Map<String, Object> getNodeContext() {
            return nodeContext;
        }

        /**
         * 添加节点级别的共享数据
         */
        public ParallelNode<IN_ITEM, OUT_ITEM> addNodeContext(String key, Object value) {
            nodeContext.put(key, value);
            return this;
        }

        /**
         * 判断是否使用任务抽象模式
         */
        protected boolean isTaskBasedMode() {
            return tasks != null && !tasks.isEmpty();
        }

        @Override
        protected List<OUT_ITEM> internalExec(List<IN_ITEM> batchPrepResult) {
            if (batchPrepResult == null || batchPrepResult.isEmpty()) {
                return Collections.emptyList();
            }

            // 根据模式选择执行策略
            if (isTaskBasedMode()) {
                return executeWithTasks(batchPrepResult);
            } else {
                return executeWithExecItem(batchPrepResult);
            }
        }

        /**
         * 基于任务抽象的执行方式
         */
        private List<OUT_ITEM> executeWithTasks(List<IN_ITEM> inputs) {
            long startTime = System.currentTimeMillis();
            String parallelGroupId = nodeName + "_parallel_" + System.currentTimeMillis();

            // 创建任务执行追踪
            List<TaskExecutionTracker<IN_ITEM, OUT_ITEM>> taskTrackers = new ArrayList<>();
            for (int i = 0; i < tasks.size(); i++) {
                ParallelTask<IN_ITEM, OUT_ITEM> task = tasks.get(i);
                IN_ITEM input = i < inputs.size() ? inputs.get(i) : null;

                TaskExecutionTracker<IN_ITEM, OUT_ITEM> tracker = new TaskExecutionTracker<>(
                        task, input, parallelGroupId + "_" + task.getTaskId()
                );
                taskTrackers.add(tracker);
            }

            // 执行任务准备工作
            for (TaskExecutionTracker<IN_ITEM, OUT_ITEM> tracker : taskTrackers) {
                try {
                    tracker.getTask().prepare(nodeContext);
                } catch (Exception e) {
                    // 准备失败不影响其他任务
                }
            }

            List<java.util.concurrent.CompletableFuture<OUT_ITEM>> futures = new ArrayList<>();

            // 启动并行任务
            for (TaskExecutionTracker<IN_ITEM, OUT_ITEM> tracker : taskTrackers) {
                java.util.concurrent.CompletableFuture<OUT_ITEM> future = java.util.concurrent.CompletableFuture.supplyAsync(() -> {
                    tracker.markStarted();

                    try {
                        OUT_ITEM result = tracker.getTask().execute(tracker.getInput(), nodeContext);
                        tracker.markCompleted(result);
                        return result;
                    } catch (Exception e) {
                        tracker.markFailed(e);
                        try {
                            OUT_ITEM fallbackResult = tracker.getTask().fallback(
                                    tracker.getInput(), e, nodeContext);
                            tracker.markCompletedWithFallback(fallbackResult);
                            return fallbackResult;
                        } catch (Exception fallbackException) {
                            tracker.markFallbackFailed(fallbackException);
                            throw new RuntimeException("Task " + tracker.getTask().getTaskId() +
                                    " failed with fallback", fallbackException);
                        }
                    }
                }, PARALLEL_EXECUTOR);

                futures.add(future);
            }

            try {
                // 等待所有任务完成
                java.util.concurrent.CompletableFuture<Void> allOf = java.util.concurrent.CompletableFuture.allOf(
                        futures.toArray(new java.util.concurrent.CompletableFuture[0]));

                if (timeoutMillis > 0) {
                    allOf.get(timeoutMillis, java.util.concurrent.TimeUnit.MILLISECONDS);
                } else {
                    allOf.join();
                }

                // 收集结果
                List<OUT_ITEM> results = new ArrayList<>();
                for (int i = 0; i < futures.size(); i++) {
                    try {
                        results.add(futures.get(i).get());
                    } catch (Exception e) {
                        throw new PocketFlowException("Failed to collect result for task " +
                                tasks.get(i).getTaskId(), e);
                    }
                }

                // 执行清理工作
                for (TaskExecutionTracker<IN_ITEM, OUT_ITEM> tracker : taskTrackers) {
                    try {
                        tracker.getTask().cleanup(nodeContext);
                    } catch (Exception e) {
                        // 清理失败不影响结果
                    }
                }

                // 添加追踪信息到元数据
                addTaskTrackingMetadata(parallelGroupId, taskTrackers, startTime);

                return results;

            } catch (java.util.concurrent.TimeoutException e) {
                // 标记超时任务
                for (TaskExecutionTracker<IN_ITEM, OUT_ITEM> tracker : taskTrackers) {
                    if (!tracker.isCompleted()) {
                        tracker.markTimeout();
                    }
                }
                futures.forEach(f -> f.cancel(true));
                throw new PocketFlowException("Parallel tasks execution timed out", e);
            } catch (Exception e) {
                futures.forEach(f -> f.cancel(true));
                throw new PocketFlowException("Parallel tasks execution failed", e);
            }
        }

        /**
         * 基于传统execItem的执行方式
         */
        private List<OUT_ITEM> executeWithExecItem(List<IN_ITEM> batchPrepResult) {
            long startTime = System.currentTimeMillis();
            String parallelGroupId = nodeName + "_parallel_" + System.currentTimeMillis();

            // 创建子任务追踪信息
            List<SubTaskTracker> subTaskTrackers = new ArrayList<>();
            for (int i = 0; i < batchPrepResult.size(); i++) {
                SubTaskTracker tracker = new SubTaskTracker(
                        parallelGroupId + "_task_" + i,
                        batchPrepResult.get(i).toString()
                );
                subTaskTrackers.add(tracker);
            }

            List<java.util.concurrent.CompletableFuture<OUT_ITEM>> futures = new ArrayList<>(batchPrepResult.size());

            // Start all items in parallel using custom thread pool
            for (int i = 0; i < batchPrepResult.size(); i++) {
                final IN_ITEM item = batchPrepResult.get(i);
                final int itemIndex = i;
                final SubTaskTracker taskTracker = subTaskTrackers.get(i);

                java.util.concurrent.CompletableFuture<OUT_ITEM> future =
                        java.util.concurrent.CompletableFuture.supplyAsync(() -> {
                            taskTracker.markStarted();

                            try {
                                OUT_ITEM result = execItem(item);
                                taskTracker.markCompleted(result);
                                return result;
                            } catch (Exception e) {
                                taskTracker.markFailed(e);
                                try {
                                    OUT_ITEM fallbackResult = execItemFallback(item, e);
                                    taskTracker.markCompletedWithFallback(fallbackResult);
                                    return fallbackResult;
                                } catch (Exception fallbackException) {
                                    // taskTracker.markFallbackFailed(fallbackException);
                                    throw new RuntimeException("Item " + itemIndex + " failed with fallback", fallbackException);
                                }
                            }
                        }, PARALLEL_EXECUTOR); // 使用自定义线程池

                futures.add(future);
            }

            try {
                // Wait for all tasks with timeout
                java.util.concurrent.CompletableFuture<Void> allOf = java.util.concurrent.CompletableFuture.allOf(
                        futures.toArray(new java.util.concurrent.CompletableFuture[0]));

                if (timeoutMillis > 0) {
                    allOf.get(timeoutMillis, java.util.concurrent.TimeUnit.MILLISECONDS);
                } else {
                    allOf.join();
                }

                // Collect results
                List<OUT_ITEM> results = new ArrayList<>(futures.size());
                for (int i = 0; i < futures.size(); i++) {
                    try {
                        results.add(futures.get(i).get());
                    } catch (Exception e) {
                        throw new PocketFlowException("Failed to collect result for item " + i, e);
                    }
                }

                long totalDuration = System.currentTimeMillis() - startTime;

                // 将子任务追踪信息添加到当前节点的元数据中
                if (GlobalExecutionTracker.isTrackingEnabled()) {
                    ExecutionTracker tracker = GlobalExecutionTracker.getCurrentTracker();
                    if (tracker != null) {
                        String nodeId = this.getClass().getSimpleName() + "_" + PROCESS_ID + "_" + NODE_ID_COUNTER.get();
                        NodeExecutionInfo nodeInfo = tracker.getNodeExecutions().get(nodeId);
                        if (nodeInfo != null) {
                            // 添加并行执行的详细信息
                            nodeInfo.addMetadata("parallelGroupId", parallelGroupId);
                            nodeInfo.addMetadata("totalParallelTasks", subTaskTrackers.size());
                            nodeInfo.addMetadata("totalParallelDuration", totalDuration);

                            // 添加每个子任务的详细信息
                            List<Map<String, Object>> subTaskDetails = new ArrayList<>();
                            for (SubTaskTracker taskTracker : subTaskTrackers) {
                                subTaskDetails.add(taskTracker.toDetailMap());
                            }
                            nodeInfo.addMetadata("subTaskDetails", subTaskDetails);

                            // 计算并行效率
                            long totalSubTaskTime = subTaskTrackers.stream()
                                    .mapToLong(SubTaskTracker::getDuration)
                                    .sum();
                            double parallelEfficiency = totalSubTaskTime > 0 ?
                                    (double) totalSubTaskTime / (totalDuration * subTaskTrackers.size()) * 100 : 0;
                            nodeInfo.addMetadata("parallelEfficiency", String.format("%.1f%%", parallelEfficiency));

                            // 找出最慢和最快的子任务
                            SubTaskTracker slowest = subTaskTrackers.stream()
                                    .max(Comparator.comparingLong(SubTaskTracker::getDuration))
                                    .orElse(null);
                            SubTaskTracker fastest = subTaskTrackers.stream()
                                    .min(Comparator.comparingLong(SubTaskTracker::getDuration))
                                    .orElse(null);

                            if (slowest != null) {
                                nodeInfo.addMetadata("slowestSubTask", slowest.toSummaryMap());
                            }
                            if (fastest != null) {
                                nodeInfo.addMetadata("fastestSubTask", fastest.toSummaryMap());
                            }
                        }
                    }
                }

                return results;

            } catch (java.util.concurrent.TimeoutException e) {
                // 标记所有未完成的子任务为超时
                for (SubTaskTracker taskTracker : subTaskTrackers) {
                    if (!taskTracker.isCompleted()) {
                        taskTracker.markTimeout();
                    }
                }
                futures.forEach(f -> f.cancel(true));
                throw new PocketFlowException("Parallel execution timed out", e);
            } catch (Exception e) {
                futures.forEach(f -> f.cancel(true));
                throw new PocketFlowException("Parallel execution failed", e);
            }
        }

        /**
         * 添加任务抽象的追踪元数据
         */
        private void addTaskTrackingMetadata(String parallelGroupId,
                                             List<TaskExecutionTracker<IN_ITEM, OUT_ITEM>> taskTrackers,
                                             long startTime) {
            if (!GlobalExecutionTracker.isTrackingEnabled()) {
                return;
            }

            ExecutionTracker tracker = GlobalExecutionTracker.getCurrentTracker();
            if (tracker == null) {
                return;
            }

            String nodeId = this.getClass().getSimpleName() + "_" + PROCESS_ID + "_" + NODE_ID_COUNTER.get();
            NodeExecutionInfo nodeInfo = tracker.getNodeExecutions().get(nodeId);
            if (nodeInfo == null) {
                return;
            }

            long totalDuration = System.currentTimeMillis() - startTime;

            // 添加任务级别的追踪信息
            nodeInfo.addMetadata("parallelGroupId", parallelGroupId);
            nodeInfo.addMetadata("totalParallelTasks", taskTrackers.size());
            nodeInfo.addMetadata("totalParallelDuration", totalDuration);
            nodeInfo.addMetadata("executionMode", "task-based");

            // 转换为标准格式的子任务详情
            List<Map<String, Object>> subTaskDetails = new ArrayList<>();
            for (TaskExecutionTracker<IN_ITEM, OUT_ITEM> taskTracker : taskTrackers) {
                subTaskDetails.add(taskTracker.toDetailMap());
            }
            nodeInfo.addMetadata("subTaskDetails", subTaskDetails);

            // 计算任务级别的性能指标
            addTaskPerformanceMetrics(nodeInfo, taskTrackers, totalDuration);
        }

        /**
         * 添加任务性能指标
         */
        private void addTaskPerformanceMetrics(NodeExecutionInfo nodeInfo,
                                               List<TaskExecutionTracker<IN_ITEM, OUT_ITEM>> taskTrackers,
                                               long totalDuration) {
            // 计算并行效率
            long totalTaskTime = taskTrackers.stream()
                    .mapToLong(TaskExecutionTracker::getDuration)
                    .sum();
            double parallelEfficiency = totalTaskTime > 0 ?
                    (double) totalTaskTime / (totalDuration * taskTrackers.size()) * 100 : 0;
            nodeInfo.addMetadata("parallelEfficiency", String.format("%.1f%%", parallelEfficiency));

            // 找出最慢和最快的任务
            java.util.Optional<TaskExecutionTracker<IN_ITEM, OUT_ITEM>> slowest = taskTrackers.stream()
                    .max(Comparator.comparingLong(TaskExecutionTracker::getDuration));
            java.util.Optional<TaskExecutionTracker<IN_ITEM, OUT_ITEM>> fastest = taskTrackers.stream()
                    .min(Comparator.comparingLong(TaskExecutionTracker::getDuration));

            if (slowest.isPresent()) {
                nodeInfo.addMetadata("slowestTask", slowest.get().toSummaryMap());
            }
            if (fastest.isPresent()) {
                nodeInfo.addMetadata("fastestTask", fastest.get().toSummaryMap());
            }

            // 计算预期 vs 实际执行时间
            long totalEstimated = taskTrackers.stream()
                    .mapToLong(t -> t.getTask().getEstimatedDuration())
                    .sum();
            if (totalEstimated > 0) {
                double accuracyRatio = (double) totalTaskTime / totalEstimated * 100;
                nodeInfo.addMetadata("estimationAccuracy", String.format("%.1f%%", accuracyRatio));
            }
        }
    }

    /**
     * A flow that can execute multiple sub-flows in parallel.
     * Each sub-flow runs independently and their results are collected.
     *
     * <p><b>超时控制：</b></p>
     * <ul>
     *   <li>timeoutMillis > 0: 指定超时时间（毫秒），超时后抛出 PocketFlowException</li>
     *   <li>timeoutMillis <= 0: 无限等待，直到所有子流程完成（适合测试阶段）</li>
     * </ul>
     */
    public static abstract class ParallelFlow extends Flow {
        protected final long timeoutMillis;
        protected final String flowName;

        /**
         * 默认构造函数，使用30秒超时
         */
        public ParallelFlow() {
            this(30000L); // 30 seconds default timeout
        }

        /**
         * 指定超时时间的构造函数
         *
         * @param timeoutMillis 超时时间(毫秒)，设置为 0 或负数表示无限等待
         */
        public ParallelFlow(long timeoutMillis) {
            this(timeoutMillis, null);
        }

        /**
         * 完整构造函数
         *
         * @param timeoutMillis 超时时间(毫秒)，设置为 0 或负数表示无限等待
         * @param flowName      流程名称，用于日志和追踪
         */
        public ParallelFlow(long timeoutMillis, String flowName) {
            super();
            this.timeoutMillis = timeoutMillis; // <= 0 表示无限等待
            this.flowName = flowName != null ? flowName : this.getClass().getSimpleName();
        }

        /**
         * Define the parallel sub-flows to execute.
         * Each map represents the parameters for one sub-flow.
         */
        public abstract List<Map<String, Object>> prepParallelFlows(Map<String, Object> sharedContext);

        /**
         * Execute a single sub-flow with the given parameters.
         * This method should define the flow logic for each parallel branch.
         */
        public abstract Map<String, Object> execSubFlow(Map<String, Object> subFlowParams, Map<String, Object> sharedContext);

        /**
         * Post-process the results from all parallel sub-flows.
         */
        public abstract String postParallelFlows(Map<String, Object> sharedContext,
                                                 List<Map<String, Object>> subFlowParams,
                                                 List<Map<String, Object>> subFlowResults);

        @Override
        protected String internalRun(Map<String, Object> sharedContext) {
            long startTime = System.currentTimeMillis();
            // logInfo(String.format("[%s] Starting parallel flow execution", flowName));

            List<Map<String, Object>> subFlowParamsList = prepParallelFlows(sharedContext);
            if (subFlowParamsList == null || subFlowParamsList.isEmpty()) {
                // logInfo(String.format("[%s] No sub-flows to execute", flowName));
                return postParallelFlows(sharedContext, Collections.emptyList(), Collections.emptyList());
            }

            // logInfo(String.format("[%s] Preparing to execute %d parallel sub-flows", flowName, subFlowParamsList.size()));

            List<java.util.concurrent.CompletableFuture<Map<String, Object>>> futures = new ArrayList<>(subFlowParamsList.size());

            // Start all sub-flows in parallel
            for (int i = 0; i < subFlowParamsList.size(); i++) {
                final Map<String, Object> subFlowParams = subFlowParamsList.get(i);
                final int flowIndex = i;

                java.util.concurrent.CompletableFuture<Map<String, Object>> future =
                        java.util.concurrent.CompletableFuture.supplyAsync(() -> {
                            long subFlowStartTime = System.currentTimeMillis();
                            String threadName = Thread.currentThread().getName();
                            // logDebug(String.format("[%s] Sub-flow %d started in thread: %s", flowName, flowIndex, threadName));

                            try {
                                // Create a copy of shared context for this sub-flow
                                Map<String, Object> subFlowContext = new HashMap<>(sharedContext);
                                Map<String, Object> result = execSubFlow(subFlowParams, subFlowContext);

                                long subFlowDuration = System.currentTimeMillis() - subFlowStartTime;
                                // logDebug(String.format("[%s] Sub-flow %d completed in %dms (thread: %s)",
                                //         flowName, flowIndex, subFlowDuration, threadName));

                                return result;
                            } catch (Exception e) {
                                long subFlowDuration = System.currentTimeMillis() - subFlowStartTime;
                                // logWarn(String.format("[%s] Sub-flow %d failed after %dms (thread: %s): %s",
                                //         flowName, flowIndex, subFlowDuration, threadName, e.getMessage()));
                                throw new RuntimeException("Sub-flow " + flowIndex + " failed", e);
                            }
                        });

                futures.add(future);
            }

            try {
                // Wait for all sub-flows with timeout
                java.util.concurrent.CompletableFuture<Void> allOf = java.util.concurrent.CompletableFuture.allOf(
                        futures.toArray(new java.util.concurrent.CompletableFuture[0]));

                if (timeoutMillis > 0) {
                    allOf.get(timeoutMillis, java.util.concurrent.TimeUnit.MILLISECONDS);
                } else {
                    allOf.join();
                }

                // Collect results
                List<Map<String, Object>> results = new ArrayList<>(futures.size());
                for (int i = 0; i < futures.size(); i++) {
                    try {
                        results.add(futures.get(i).get());
                    } catch (Exception e) {
                        // logWarn(String.format("[%s] Failed to get result for sub-flow %d: %s", flowName, i, e.getMessage()));
                        throw new PocketFlowException("Failed to collect result for sub-flow " + i, e);
                    }
                }

                long totalDuration = System.currentTimeMillis() - startTime;
                // logInfo(String.format("[%s] Parallel flow execution completed: %d sub-flows in %dms", 
                //         flowName, subFlowParamsList.size(), totalDuration));

                return postParallelFlows(sharedContext, subFlowParamsList, results);

            } catch (java.util.concurrent.TimeoutException e) {
                // logWarn(String.format("[%s] Parallel flow execution timed out after %dms", flowName, timeoutMillis));
                futures.forEach(f -> f.cancel(true));
                throw new PocketFlowException("Parallel flow execution timed out", e);
            } catch (Exception e) {
                // logWarn(String.format("[%s] Parallel flow execution failed: %s", flowName, e.getMessage()));
                futures.forEach(f -> f.cancel(true));
                throw new PocketFlowException("Parallel flow execution failed", e);
            }
        }

        @Override
        public final String post(Map<String, Object> sharedContext, Void prepResult, String execResult) {
            return execResult;
        }
    }

    // ==================== 并行任务抽象 ====================

    /**
     * 并行任务接口 - 定义单个并行执行任务的规范
     */
    public interface ParallelTask<INPUT, OUTPUT> {

        /**
         * 任务的唯一标识
         */
        String getTaskId();

        /**
         * 任务的描述名称
         */
        default String getTaskName() {
            return getTaskId();
        }

        /**
         * 执行任务的核心逻辑
         *
         * @param input         任务输入
         * @param sharedContext 共享上下文
         * @return 任务输出
         */
        OUTPUT execute(INPUT input, Map<String, Object> sharedContext) throws Exception;

        /**
         * 任务失败时的降级处理
         *
         * @param input         原始输入
         * @param exception     失败异常
         * @param sharedContext 共享上下文
         * @return 降级结果
         */
        default OUTPUT fallback(INPUT input, Exception exception, Map<String, Object> sharedContext) throws Exception {
            throw exception; // 默认直接抛出异常
        }

        /**
         * 获取任务的预期执行时间（毫秒），用于监控和分析
         */
        default long getEstimatedDuration() {
            return 1000L; // 默认1秒
        }

        /**
         * 任务执行前的准备工作
         */
        default void prepare(Map<String, Object> sharedContext) {
            // 默认无操作
        }

        /**
         * 任务执行后的清理工作
         */
        default void cleanup(Map<String, Object> sharedContext) {
            // 默认无操作
        }
    }

    /**
     * 抽象并行任务基类 - 提供通用功能
     */
    public static abstract class AbstractParallelTask<INPUT, OUTPUT> implements ParallelTask<INPUT, OUTPUT> {

        protected final String taskId;
        protected final String taskName;
        protected final long estimatedDuration;

        public AbstractParallelTask(String taskId) {
            this(taskId, taskId, 1000L);
        }

        public AbstractParallelTask(String taskId, String taskName) {
            this(taskId, taskName, 1000L);
        }

        public AbstractParallelTask(String taskId, String taskName, long estimatedDuration) {
            this.taskId = taskId;
            this.taskName = taskName;
            this.estimatedDuration = estimatedDuration;
        }

        @Override
        public String getTaskId() {
            return taskId;
        }

        @Override
        public String getTaskName() {
            return taskName;
        }

        @Override
        public long getEstimatedDuration() {
            return estimatedDuration;
        }

        /**
         * 创建默认的降级结果
         */
        protected OUTPUT createDefaultResult() {
            return null; // 子类可以重写
        }

        @Override
        public OUTPUT fallback(INPUT input, Exception exception, Map<String, Object> sharedContext) throws Exception {
            OUTPUT defaultResult = createDefaultResult();
            if (defaultResult != null) {
                return defaultResult;
            }
            throw exception;
        }
    }

    // ==================== 功能增强：条件分支和循环支持 ====================

    /**
     * 条件分支节点 - 根据条件选择不同的执行路径
     */
    public static abstract class ConditionalNode<P, E> extends BaseNode<P, E> {

        /**
         * 评估条件，返回要执行的分支名称
         */
        public abstract String evaluateCondition(P prepResult, Map<String, Object> sharedContext);

        @Override
        protected String internalRun(Map<String, Object> sharedContext) {
            String nodeId = this.getClass().getSimpleName() + "_" + PROCESS_ID + "_" + NODE_ID_COUNTER.incrementAndGet();
            String nodeName = this.getClass().getSimpleName();
            String nodeType = "ConditionalNode";

            NodeExecutionInfo trackingInfo = GlobalExecutionTracker.trackNodeStart(nodeId, nodeName, nodeType);

            try {
                P prepRes = prep(sharedContext);

                if (trackingInfo != null && GlobalExecutionTracker.isTrackingEnabled()) {
                    trackingInfo.setInputSummary(prepRes);
                }

                // 执行条件评估
                String branchName = evaluateCondition(prepRes, sharedContext);
                E execRes = exec(prepRes);
                String postRes = post(sharedContext, prepRes, execRes);

                // 返回条件分支名称而不是post结果
                String finalAction = branchName != null ? branchName : postRes;

                if (trackingInfo != null) {
                    trackingInfo.markCompleted(execRes);
                    trackingInfo.addMetadata("Branch", branchName);
                    trackingInfo.addMetadata("PostResult", postRes);
                }

                return finalAction;
            } catch (Exception e) {
                if (trackingInfo != null) {
                    trackingInfo.markFailed(e);
                }
                throw e;
            }
        }
    }

    /**
     * 流程构建器 - 提供更优雅的流程构建API
     */
    public static class FlowBuilder {
        private BaseNode<?, ?> startNode;
        private BaseNode<?, ?> currentNode;

        public static FlowBuilder create() {
            return new FlowBuilder();
        }

        public <P, E> FlowBuilder start(BaseNode<P, E> node) {
            this.startNode = node;
            this.currentNode = node;
            return this;
        }

        public <P, E> FlowBuilder then(BaseNode<P, E> node) {
            if (currentNode == null) {
                throw new IllegalStateException("Must call start() first");
            }
            currentNode.next(node);
            currentNode = node;
            return this;
        }

        public <P, E> FlowBuilder then(BaseNode<P, E> node, String action) {
            if (currentNode == null) {
                throw new IllegalStateException("Must call start() first");
            }
            currentNode.next(node, action);
            currentNode = node;
            return this;
        }

        public <P, E> FlowBuilder branch(String condition, BaseNode<P, E> node) {
            if (currentNode == null) {
                throw new IllegalStateException("Must call start() first");
            }
            currentNode.next(node, condition);
            return this;
        }

        public Flow build() {
            if (startNode == null) {
                throw new IllegalStateException("Flow must have a start node");
            }
            return new Flow(startNode);
        }

        public BatchFlow buildBatch() {
            if (startNode == null) {
                throw new IllegalStateException("Flow must have a start node");
            }
            return new BatchFlow(startNode) {
                @Override
                public List<Map<String, Object>> prepBatch(Map<String, Object> sharedContext) {
                    return Collections.emptyList();
                }

                @Override
                public String postBatch(Map<String, Object> sharedContext, List<Map<String, Object>> batchPrepResult) {
                    return null;
                }
            };
        }
    }

    /**
     * 并行节点构建器 - 简化并行节点的创建
     */
    public static class ParallelNodeBuilder<IN_ITEM, OUT_ITEM> {
        private long timeoutMillis = 30000L;
        private String nodeName;
        private Function<IN_ITEM, OUT_ITEM> execFunction;
        private BiFunction<IN_ITEM, Exception, OUT_ITEM> fallbackFunction;

        public static <IN, OUT> ParallelNodeBuilder<IN, OUT> create() {
            return new ParallelNodeBuilder<>();
        }

        public ParallelNodeBuilder<IN_ITEM, OUT_ITEM> timeout(long timeoutMillis) {
            this.timeoutMillis = timeoutMillis;
            return this;
        }

        public ParallelNodeBuilder<IN_ITEM, OUT_ITEM> name(String nodeName) {
            this.nodeName = nodeName;
            return this;
        }

        public ParallelNodeBuilder<IN_ITEM, OUT_ITEM> executor(Function<IN_ITEM, OUT_ITEM> execFunction) {
            this.execFunction = execFunction;
            return this;
        }

        public ParallelNodeBuilder<IN_ITEM, OUT_ITEM> fallback(BiFunction<IN_ITEM, Exception, OUT_ITEM> fallbackFunction) {
            this.fallbackFunction = fallbackFunction;
            return this;
        }

        public ParallelNode<IN_ITEM, OUT_ITEM> build() {
            if (execFunction == null) {
                throw new IllegalStateException("Executor function is required");
            }

            final Function<IN_ITEM, OUT_ITEM> finalExecFunction = execFunction;
            final BiFunction<IN_ITEM, Exception, OUT_ITEM> finalFallbackFunction = fallbackFunction;

            return new ParallelNode<IN_ITEM, OUT_ITEM>(timeoutMillis, nodeName) {
                @Override
                public List<OUT_ITEM> exec(List<IN_ITEM> prepResult) {
                    return super.internalExec(prepResult);
                }

                @Override
                public OUT_ITEM execItem(IN_ITEM item) {
                    return finalExecFunction.apply(item);
                }

                @Override
                public OUT_ITEM execItemFallback(IN_ITEM item, Exception lastException) throws Exception {
                    if (finalFallbackFunction != null) {
                        return finalFallbackFunction.apply(item, lastException);
                    }
                    return super.execItemFallback(item, lastException);
                }
            };
        }
    }

    // ==================== 执行追踪和可视化报告系统 ====================

    /**
     * 节点执行信息记录
     */
    public static class NodeExecutionInfo {
        private final String nodeId;
        private final String nodeName;
        private final String nodeType;
        private final long startTime;
        private long endTime;
        private String status = "RUNNING";
        private String threadName;
        private Object inputSummary;
        private Object outputSummary;
        private Exception exception;
        private final Map<String, Object> metadata = new HashMap<>();

        public NodeExecutionInfo(String nodeId, String nodeName, String nodeType) {
            this.nodeId = nodeId;
            this.nodeName = nodeName;
            this.nodeType = nodeType;
            this.startTime = System.currentTimeMillis();
            this.threadName = Thread.currentThread().getName();
        }

        public void markCompleted(Object output) {
            this.endTime = System.currentTimeMillis();
            this.status = "SUCCESS";
            this.outputSummary = summarizeData(output);
        }

        public void markFailed(Exception ex) {
            this.endTime = System.currentTimeMillis();
            this.status = "FAILED";
            this.exception = ex;
        }

        public void setInputSummary(Object input) {
            this.inputSummary = summarizeData(input);
        }

        public void addMetadata(String key, Object value) {
            this.metadata.put(key, value);
        }

        public void addTaskDetails(String taskType, int taskCount) {
            this.metadata.put("TaskType", taskType);
            this.metadata.put("TaskCount", taskCount);
        }

        public void addParallelInfo(int parallelTasks, List<String> threadNames) {
            this.metadata.put("ParallelTasks", parallelTasks);
            this.metadata.put("ThreadsUsed", threadNames.size());
            this.metadata.put("ThreadNames", threadNames.subList(0, Math.min(3, threadNames.size())));
        }

        private Object summarizeData(Object data) {
            if (data == null) return "null";

            try {
                // 尝试JSON序列化 - 不截断，显示完整内容
                return toJsonString(data);
            } catch (Exception e) {
                // JSON序列化失败时的降级处理
                return fallbackSummary(data);
            }
        }

        private String toJsonString(Object data) {
            if (data == null) return "null";
            if (data instanceof String) {
                return "\"" + data + "\"";
            }
            if (data instanceof Number || data instanceof Boolean) {
                return data.toString();
            }
            if (data instanceof Collection) {
                Collection<?> collection = (Collection<?>) data;
                if (collection.isEmpty()) {
                    return "[]";
                }

                StringBuilder sb = new StringBuilder();
                sb.append("[");
                int count = 0;
                for (Object item : collection) {
                    if (count > 0) sb.append(", ");
                    sb.append(toJsonString(item));
                    count++;
                }
                sb.append("]");
                return sb.toString();
            }
            if (data instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) data;
                if (map.isEmpty()) {
                    return "{}";
                }

                StringBuilder sb = new StringBuilder();
                sb.append("{");
                int count = 0;
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    if (count > 0) sb.append(", ");
                    sb.append("\"").append(entry.getKey()).append("\": ");
                    sb.append(toJsonString(entry.getValue()));
                    count++;
                }
                sb.append("}");
                return sb.toString();
            }

            // 特殊处理某些业务对象
            return handleSpecialObjects(data);
        }

        private String handleSpecialObjects(Object data) {
            if (data == null) return "null";

            String className = data.getClass().getSimpleName();

            // 特殊处理常见的业务对象
            if (className.contains("List") || className.contains("ArrayList")) {
                Collection<?> list = (Collection<?>) data;
                if (list.isEmpty()) return "[]";

                StringBuilder sb = new StringBuilder();
                sb.append("[");
                int count = 0;
                for (Object item : list) {
                    if (count > 0) sb.append(", ");

                    // 对于列表项，显示完整信息
                    if (item instanceof String) {
                        String str = (String) item;
                        sb.append("\"").append(str).append("\"");
                    } else {
                        sb.append(toJsonString(item));
                    }
                    count++;
                }
                sb.append("]");
                return sb.toString();
            }

            // 对于HashMap等Map类型
            if (data instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) data;
                if (map.isEmpty()) return "{}";

                StringBuilder sb = new StringBuilder();
                sb.append("{");
                int count = 0;
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    if (count > 0) sb.append(", ");

                    String key = String.valueOf(entry.getKey());
                    Object value = entry.getValue();

                    sb.append("\"").append(key).append("\": ");

                    if (value instanceof String) {
                        String str = (String) value;
                        sb.append("\"").append(str).append("\"");
                    } else {
                        sb.append(toJsonString(value));
                    }
                    count++;
                }
                sb.append("}");
                return sb.toString();
            }

            // 默认使用反射处理
            return reflectObjectToJson(data);
        }

        private String reflectObjectToJson(Object data) {
            if (data == null) return "null";

            Class<?> clazz = data.getClass();

            // 对于基本类型包装类
            if (clazz.isPrimitive() ||
                    clazz == String.class ||
                    clazz == Integer.class ||
                    clazz == Long.class ||
                    clazz == Double.class ||
                    clazz == Float.class ||
                    clazz == Boolean.class) {
                return "\"" + data.toString() + "\"";
            }

            // 对于数组
            if (clazz.isArray()) {
                return "Array[" + java.lang.reflect.Array.getLength(data) + " items]";
            }

            // 对于其他自定义对象，显示类名和字段数量
            try {
                java.lang.reflect.Field[] fields = clazz.getDeclaredFields();
                return "{\"@type\": \"" + clazz.getSimpleName() + "\", \"@fields\": " + fields.length + "}";
            } catch (Exception e) {
                return "\"" + clazz.getSimpleName() + "@" + Integer.toHexString(data.hashCode()) + "\"";
            }
        }

        private Object fallbackSummary(Object data) {
            if (data == null) return "null";
            if (data instanceof String) {
                String str = (String) data;
                return "\"" + str + "\"";
            }
            if (data instanceof Collection) {
                Collection<?> collection = (Collection<?>) data;
                return "{\"type\": \"Collection\", \"size\": " + collection.size() + ", \"class\": \"" + collection.getClass().getSimpleName() + "\"}";
            }
            if (data instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) data;
                return "{\"type\": \"Map\", \"size\": " + map.size() + ", \"class\": \"" + map.getClass().getSimpleName() + "\"}";
            }
            return "{\"type\": \"Object\", \"class\": \"" + data.getClass().getSimpleName() + "\", \"hashCode\": \"" + Integer.toHexString(data.hashCode()) + "\"}";
        }

        // Getters
        public String getNodeId() {
            return nodeId;
        }

        public String getNodeName() {
            return nodeName;
        }

        public String getNodeType() {
            return nodeType;
        }

        public long getStartTime() {
            return startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public long getDuration() {
            return endTime - startTime;
        }

        public String getStatus() {
            return status;
        }

        public String getThreadName() {
            return threadName;
        }

        public Object getInputSummary() {
            return inputSummary;
        }

        public Object getOutputSummary() {
            return outputSummary;
        }

        public Exception getException() {
            return exception;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public boolean isSuccess() {
            return "SUCCESS".equals(status);
        }

        public boolean isFailed() {
            return "FAILED".equals(status);
        }
    }

    /**
     * 执行追踪器 - 记录整个流程的执行情况
     */
    public static class ExecutionTracker {
        private final String flowId;
        private final long flowStartTime;
        private final Map<String, NodeExecutionInfo> nodeExecutions = new LinkedHashMap<>();
        private final List<String> executionOrder = new ArrayList<>();
        private final Map<String, List<String>> parallelGroups = new HashMap<>();
        private boolean completed = false;

        public ExecutionTracker(String flowId) {
            this.flowId = flowId;
            this.flowStartTime = System.currentTimeMillis();
        }

        public NodeExecutionInfo startNode(String nodeId, String nodeName, String nodeType) {
            NodeExecutionInfo info = new NodeExecutionInfo(nodeId, nodeName, nodeType);
            nodeExecutions.put(nodeId, info);
            executionOrder.add(nodeId);
            return info;
        }

        public void markParallelExecution(String groupId, List<String> nodeIds) {
            parallelGroups.put(groupId, new ArrayList<>(nodeIds));
        }

        public void markFlowCompleted() {
            this.completed = true;
        }

        public FlowExecutionReport generateReport() {
            return new FlowExecutionReport(this);
        }

        // Getters
        public String getFlowId() {
            return flowId;
        }

        public long getFlowStartTime() {
            return flowStartTime;
        }

        public long getFlowDuration() {
            return System.currentTimeMillis() - flowStartTime;
        }

        public Map<String, NodeExecutionInfo> getNodeExecutions() {
            return nodeExecutions;
        }

        public List<String> getExecutionOrder() {
            return executionOrder;
        }

        public Map<String, List<String>> getParallelGroups() {
            return parallelGroups;
        }

        public boolean isCompleted() {
            return completed;
        }
    }

    /**
     * 流程执行报告
     */
    public static class FlowExecutionReport {
        private final ExecutionTracker tracker;
        private final long reportTime;

        public FlowExecutionReport(ExecutionTracker tracker) {
            this.tracker = tracker;
            this.reportTime = System.currentTimeMillis();
        }

        /**
         * 生成可视化的执行报告
         */
        public String generateVisualReport() {
            StringBuilder report = new StringBuilder();

            // 报告头部
            report.append("\n");
            report.append("╔═══════════════════════════════════════════════════════════════════════════════════╗\n");
            report.append("║                           📊 MEMORY FLOW EXECUTION REPORT                         ║\n");
            report.append("╠═══════════════════════════════════════════════════════════════════════════════════╣\n");
            report.append(String.format("║ Flow ID: %-20s │ Total Duration: %-10d ms │ Status: %-8s ║\n",
                    tracker.getFlowId(), tracker.getFlowDuration(), tracker.isCompleted() ? "✅ SUCCESS" : "❌ RUNNING"));
            report.append("╚═══════════════════════════════════════════════════════════════════════════════════╝\n\n");

            // 执行时间线
            report.append("🕐 EXECUTION TIMELINE:\n");
            report.append("═══════════════════════════════════════════════════════════════════════════════════\n");

            long baseTime = tracker.getFlowStartTime();
            for (String nodeId : tracker.getExecutionOrder()) {
                NodeExecutionInfo info = tracker.getNodeExecutions().get(nodeId);
                appendNodeTimeline(report, info, baseTime);
            }

            // 并行执行分析
            if (!tracker.getParallelGroups().isEmpty()) {
                report.append("\n🚀 PARALLEL EXECUTION ANALYSIS:\n");
                report.append("═══════════════════════════════════════════════════════════════════════════════════\n");
                for (Map.Entry<String, List<String>> entry : tracker.getParallelGroups().entrySet()) {
                    appendParallelGroupAnalysis(report, entry.getKey(), entry.getValue());
                }
            }

            // 性能统计
            report.append("\n📈 PERFORMANCE STATISTICS:\n");
            report.append("═══════════════════════════════════════════════════════════════════════════════════\n");
            appendPerformanceStats(report);

            // 执行详情
            report.append("\n📋 DETAILED EXECUTION INFO:\n");
            report.append("═══════════════════════════════════════════════════════════════════════════════════\n");
            for (NodeExecutionInfo info : tracker.getNodeExecutions().values()) {
                appendNodeDetails(report, info);
            }

            return report.toString();
        }

        private void appendNodeTimeline(StringBuilder report, NodeExecutionInfo info, long baseTime) {
            long relativeStart = info.getStartTime() - baseTime;
            long relativeEnd = info.getEndTime() - baseTime;

            String statusIcon = info.isSuccess() ? "✅" : info.isFailed() ? "❌" : "🔄";
            String timeBar = generateTimeBar(relativeStart, relativeEnd, tracker.getFlowDuration());

            report.append(String.format("%s [%6d-%6dms] %s %s (%dms)\n",
                    statusIcon, relativeStart, relativeEnd, timeBar, info.getNodeName(), info.getDuration()));
        }

        private String generateTimeBar(long start, long end, long total) {
            int barLength = 40;
            int startPos = (int) (start * barLength / total);
            int endPos = (int) (end * barLength / total);

            StringBuilder bar = new StringBuilder();
            for (int i = 0; i < barLength; i++) {
                if (i >= startPos && i <= endPos) {
                    bar.append("█");
                } else {
                    bar.append("░");
                }
            }
            return bar.toString();
        }

        private void appendParallelGroupAnalysis(StringBuilder report, String groupId, List<String> nodeIds) {
            report.append(String.format("┌─ Parallel Group: %s\n", groupId));

            long groupStart = Long.MAX_VALUE;
            long groupEnd = Long.MIN_VALUE;
            int successCount = 0;
            int failCount = 0;

            for (String nodeId : nodeIds) {
                NodeExecutionInfo info = tracker.getNodeExecutions().get(nodeId);
                if (info != null) {
                    groupStart = Math.min(groupStart, info.getStartTime());
                    groupEnd = Math.max(groupEnd, info.getEndTime());
                    if (info.isSuccess()) successCount++;
                    else if (info.isFailed()) failCount++;

                    String status = info.isSuccess() ? "✅" : info.isFailed() ? "❌" : "🔄";
                    report.append(String.format("│  %s %s (%dms) [%s]\n",
                            status, info.getNodeName(), info.getDuration(), info.getThreadName()));
                }
            }

            long parallelEfficiency = nodeIds.size() > 0 ?
                    (nodeIds.stream().mapToLong(id -> {
                        NodeExecutionInfo info = tracker.getNodeExecutions().get(id);
                        return info != null ? info.getDuration() : 0;
                    }).sum() * 100) / (groupEnd - groupStart) : 0;

            report.append(String.format("└─ Summary: %d tasks, %d✅ %d❌, %dms total, %d%% efficiency\n\n",
                    nodeIds.size(), successCount, failCount, groupEnd - groupStart, parallelEfficiency));
        }

        private void appendPerformanceStats(StringBuilder report) {
            Collection<NodeExecutionInfo> nodes = tracker.getNodeExecutions().values();

            long totalDuration = tracker.getFlowDuration();
            long sumNodeDurations = nodes.stream().mapToLong(NodeExecutionInfo::getDuration).sum();

            NodeExecutionInfo slowest = nodes.stream()
                    .max(Comparator.comparingLong(NodeExecutionInfo::getDuration))
                    .orElse(null);

            NodeExecutionInfo fastest = nodes.stream()
                    .filter(n -> n.getDuration() > 0)
                    .min(Comparator.comparingLong(NodeExecutionInfo::getDuration))
                    .orElse(null);

            long successCount = nodes.stream().mapToLong(n -> n.isSuccess() ? 1 : 0).sum();
            long failCount = nodes.stream().mapToLong(n -> n.isFailed() ? 1 : 0).sum();

            report.append(String.format("• Total Nodes: %d │ Success: %d │ Failed: %d │ Success Rate: %.1f%%\n",
                    nodes.size(), successCount, failCount,
                    nodes.size() > 0 ? (successCount * 100.0 / nodes.size()) : 0));

            report.append(String.format("• Flow Duration: %dms │ Sum of Node Durations: %dms │ Parallelism Efficiency: %.1f%%\n",
                    totalDuration, sumNodeDurations,
                    totalDuration > 0 ? (sumNodeDurations * 100.0 / totalDuration) : 0));

            if (slowest != null) {
                report.append(String.format("• Slowest Node: %s (%dms) │ ", slowest.getNodeName(), slowest.getDuration()));
            }
            if (fastest != null) {
                report.append(String.format("Fastest Node: %s (%dms)\n", fastest.getNodeName(), fastest.getDuration()));
            }
        }

        private void appendNodeDetails(StringBuilder report, NodeExecutionInfo info) {
            String statusIcon = info.isSuccess() ? "✅" : info.isFailed() ? "❌" : "🔄";

            report.append(String.format("┌─ %s %s [%s]\n", statusIcon, info.getNodeName(), info.getNodeType()));
            report.append(String.format("│  Duration: %dms │ Thread: %s\n", info.getDuration(), info.getThreadName()));

            if (info.getInputSummary() != null) {
                report.append(String.format("│  Input: %s\n", info.getInputSummary()));
            }

            if (info.getOutputSummary() != null) {
                report.append(String.format("│  Output: %s\n", info.getOutputSummary()));
            }

            if (info.isFailed() && info.getException() != null) {
                report.append(String.format("│  ❌ Error: %s\n", info.getException().getMessage()));
            }

            // 处理并行子任务详情
            if (info.getMetadata().containsKey("subTaskDetails")) {
                appendSubTaskDetails(report, info);
            } else if (!info.getMetadata().isEmpty()) {
                for (Map.Entry<String, Object> entry : info.getMetadata().entrySet()) {
                    if (!"subTaskDetails".equals(entry.getKey())) {
                        report.append(String.format("│  📊 %s: %s\n", entry.getKey(), entry.getValue()));
                    }
                }
            }

            report.append("└─\n");
        }

        @SuppressWarnings("unchecked")
        private void appendSubTaskDetails(StringBuilder report, NodeExecutionInfo info) {
            report.append("│  🔗 Parallel Execution Analysis:\n");

            // 显示汇总信息
            Object totalTasks = info.getMetadata().get("totalParallelTasks");
            Object totalDuration = info.getMetadata().get("totalParallelDuration");
            Object efficiency = info.getMetadata().get("parallelEfficiency");

            if (totalTasks != null && totalDuration != null) {
                report.append(String.format("│     📊 %s parallel tasks completed in %sms\n", totalTasks, totalDuration));
            }
            if (efficiency != null) {
                report.append(String.format("│     ⚡ Parallel efficiency: %s\n", efficiency));
            }

            // 显示最慢和最快的子任务
            Object slowest = info.getMetadata().get("slowestSubTask");
            Object fastest = info.getMetadata().get("fastestSubTask");

            if (slowest instanceof Map && fastest instanceof Map) {
                Map<String, Object> slowestMap = (Map<String, Object>) slowest;
                Map<String, Object> fastestMap = (Map<String, Object>) fastest;

                report.append(String.format("│     🐌 Slowest: %s (%sms, %s)\n",
                        truncateString(String.valueOf(slowestMap.get("taskInput")), 30),
                        slowestMap.get("duration"),
                        slowestMap.get("threadName")));

                report.append(String.format("│     🚀 Fastest: %s (%sms, %s)\n",
                        truncateString(String.valueOf(fastestMap.get("taskInput")), 30),
                        fastestMap.get("duration"),
                        fastestMap.get("threadName")));
            }

            // 显示详细的子任务信息
            Object subTaskDetails = info.getMetadata().get("subTaskDetails");
            if (subTaskDetails instanceof List) {
                List<Map<String, Object>> taskList = (List<Map<String, Object>>) subTaskDetails;
                report.append("│     📋 Individual SubTask Performance:\n");

                for (int i = 0; i < taskList.size(); i++) {
                    Map<String, Object> task = taskList.get(i);
                    String status = String.valueOf(task.get("status"));
                    String statusIcon = getStatusIcon(status);
                    String taskInput = truncateString(String.valueOf(task.get("taskInput")), 25);

                    report.append(String.format("│        %s [%d] %s → %sms",
                            statusIcon, i + 1, taskInput, task.get("duration")));

                    if (task.get("threadName") != null) {
                        String threadName = String.valueOf(task.get("threadName"));
                        // 只显示线程名的关键部分
                        if (threadName.contains("-")) {
                            threadName = threadName.substring(threadName.lastIndexOf("-") + 1);
                        }
                        report.append(String.format(" (%s)", threadName));
                    }

                    if (Boolean.TRUE.equals(task.get("usedFallback"))) {
                        report.append(" [fallback]");
                    }

                    report.append("\n");

                    if (task.get("exception") != null) {
                        String errorMsg = String.valueOf(task.get("exception"));
                        report.append(String.format("│           ❌ %s\n", truncateString(errorMsg, 60)));
                    }
                }
            }
        }

        private String getStatusIcon(String status) {
            switch (status) {
                case "SUCCESS":
                    return "✅";
                case "SUCCESS_WITH_FALLBACK":
                    return "🔄";
                case "FAILED":
                    return "❌";
                case "FALLBACK_FAILED":
                    return "💥";
                case "TIMEOUT":
                    return "⏰";
                case "RUNNING":
                    return "🔄";
                default:
                    return "❓";
            }
        }

        private String truncateString(String str, int maxLength) {
            if (str == null) return "null";
            return str.length() <= maxLength ? str : str.substring(0, maxLength - 3) + "...";
        }

        /**
         * 生成简洁的一行总结
         */
        public String generateSummary() {
            Collection<NodeExecutionInfo> nodes = tracker.getNodeExecutions().values();
            long successCount = nodes.stream().mapToLong(n -> n.isSuccess() ? 1 : 0).sum();
            long failCount = nodes.stream().mapToLong(n -> n.isFailed() ? 1 : 0).sum();

            return String.format("🏁 Flow[%s] completed in %dms: %d nodes (%d✅ %d❌)",
                    tracker.getFlowId(), tracker.getFlowDuration(), nodes.size(), successCount, failCount);
        }
    }

    /**
     * 全局执行追踪管理器
     */
    public static class GlobalExecutionTracker {
        private static final ThreadLocal<ExecutionTracker> currentTracker = new ThreadLocal<>();
        private static final Map<String, ExecutionTracker> allTrackers = new ConcurrentHashMap<>();
        private static volatile boolean trackingEnabled = true; // 性能优化：可关闭追踪

        public static void startFlow(String flowId) {
            ExecutionTracker tracker = new ExecutionTracker(flowId);
            currentTracker.set(tracker);
            allTrackers.put(flowId, tracker);
        }

        public static boolean isTrackingEnabled() {
            return trackingEnabled;
        }

        public static void setTrackingEnabled(boolean enabled) {
            trackingEnabled = enabled;
        }

        // 性能优化：定期清理旧的追踪记录
        public static void cleanupOldTrackers(long maxAgeMs) {
            long cutoffTime = System.currentTimeMillis() - maxAgeMs;
            allTrackers.entrySet().removeIf(entry ->
                    entry.getValue().getFlowStartTime() < cutoffTime);
        }

        public static ExecutionTracker getCurrentTracker() {
            return currentTracker.get();
        }

        public static NodeExecutionInfo trackNodeStart(String nodeId, String nodeName, String nodeType) {
            ExecutionTracker tracker = getCurrentTracker();
            if (tracker != null) {
                return tracker.startNode(nodeId, nodeName, nodeType);
            }
            return null;
        }

        public static void trackParallelGroup(String groupId, List<String> nodeIds) {
            ExecutionTracker tracker = getCurrentTracker();
            if (tracker != null) {
                tracker.markParallelExecution(groupId, nodeIds);
            }
        }

        public static FlowExecutionReport endFlow() {
            ExecutionTracker tracker = getCurrentTracker();
            if (tracker != null) {
                tracker.markFlowCompleted();
                currentTracker.remove();
                return tracker.generateReport();
            }
            return null;
        }

        public static FlowExecutionReport getReport(String flowId) {
            ExecutionTracker tracker = allTrackers.get(flowId);
            return tracker != null ? tracker.generateReport() : null;
        }
    }

    /**
     * 子任务追踪器 - 用于追踪并行执行中每个子任务的详细信息
     */
    public static class SubTaskTracker {
        private final String taskId;
        private final String taskInput;
        private final long createTime;
        private long startTime;
        private long endTime;
        private String status = "CREATED";
        private String threadName;
        private Object result;
        private Exception exception;
        private Exception fallbackException;
        private boolean usedFallback = false;

        public SubTaskTracker(String taskId, String taskInput) {
            this.taskId = taskId;
            this.taskInput = taskInput;
            this.createTime = System.currentTimeMillis();
        }

        public void markStarted() {
            this.startTime = System.currentTimeMillis();
            this.threadName = Thread.currentThread().getName();
            this.status = "RUNNING";
        }

        public void markCompleted(Object result) {
            this.endTime = System.currentTimeMillis();
            this.result = result;
            this.status = "SUCCESS";
        }

        public void markFailed(Exception exception) {
            this.exception = exception;
            this.status = "FAILED";
        }

        public void markCompletedWithFallback(Object result) {
            this.endTime = System.currentTimeMillis();
            this.result = result;
            this.usedFallback = true;
            this.status = "SUCCESS_WITH_FALLBACK";
        }

        public void markTimeout() {
            this.endTime = System.currentTimeMillis();
            this.status = "TIMEOUT";
        }

        public long getDuration() {
            if (startTime == 0) return 0;
            return (endTime > 0 ? endTime : System.currentTimeMillis()) - startTime;
        }

        public long getWaitTime() {
            return startTime > 0 ? startTime - createTime : 0;
        }

        public boolean isCompleted() {
            return "SUCCESS".equals(status) || "SUCCESS_WITH_FALLBACK".equals(status);
        }

        public Map<String, Object> toDetailMap() {
            Map<String, Object> detail = new HashMap<>();
            detail.put("taskId", taskId);
            detail.put("taskInput", taskInput);
            detail.put("status", status);
            detail.put("threadName", threadName);
            detail.put("waitTime", getWaitTime());
            detail.put("duration", getDuration());
            detail.put("usedFallback", usedFallback);

            if (result != null) {
                detail.put("resultSummary", summarizeResult(result));
            }
            if (exception != null) {
                detail.put("exception", exception.getMessage());
            }
            if (fallbackException != null) {
                detail.put("fallbackException", fallbackException.getMessage());
            }

            return detail;
        }

        public Map<String, Object> toSummaryMap() {
            Map<String, Object> summary = new HashMap<>();
            summary.put("taskId", taskId);
            summary.put("taskInput", taskInput.length() > 50 ? taskInput.substring(0, 50) + "..." : taskInput);
            summary.put("status", status);
            summary.put("duration", getDuration());
            summary.put("threadName", threadName);
            return summary;
        }

        private String summarizeResult(Object result) {
            if (result == null) return "null";
            if (result instanceof String) {
                String str = (String) result;
                return str.length() > 100 ? str.substring(0, 100) + "..." : str;
            }
            if (result instanceof Map) {
                return "Map[" + ((Map<?, ?>) result).size() + " entries]";
            }
            if (result instanceof List) {
                return "List[" + ((List<?>) result).size() + " items]";
            }
            return result.getClass().getSimpleName();
        }
    }

    public static class TaskExecutionTracker<INPUT, OUTPUT> {
        private final ParallelTask<INPUT, OUTPUT> task;
        private final INPUT input;
        private final String executionId;
        private final long createTime;
        private long startTime;
        private long endTime;
        private String status = "CREATED";
        private String threadName;
        private OUTPUT result;
        private Exception exception;
        private Exception fallbackException;
        private boolean usedFallback = false;

        public TaskExecutionTracker(ParallelTask<INPUT, OUTPUT> task, INPUT input, String executionId) {
            this.task = task;
            this.input = input;
            this.executionId = executionId;
            this.createTime = System.currentTimeMillis();
        }

        public void markStarted() {
            this.startTime = System.currentTimeMillis();
            this.threadName = Thread.currentThread().getName();
            this.status = "RUNNING";
        }

        public void markCompleted(OUTPUT result) {
            this.endTime = System.currentTimeMillis();
            this.result = result;
            this.status = "SUCCESS";
        }

        public void markFailed(Exception exception) {
            this.exception = exception;
            this.status = "FAILED";
        }

        public void markCompletedWithFallback(OUTPUT result) {
            this.endTime = System.currentTimeMillis();
            this.result = result;
            this.usedFallback = true;
            this.status = "SUCCESS_WITH_FALLBACK";
        }

        public void markFallbackFailed(Exception fallbackException) {
            this.endTime = System.currentTimeMillis();
            this.fallbackException = fallbackException;
            this.status = "FALLBACK_FAILED";
        }

        public void markTimeout() {
            this.endTime = System.currentTimeMillis();
            this.status = "TIMEOUT";
        }

        public long getDuration() {
            if (startTime == 0) return 0;
            return (endTime > 0 ? endTime : System.currentTimeMillis()) - startTime;
        }

        public long getWaitTime() {
            return startTime > 0 ? startTime - createTime : 0;
        }

        public boolean isCompleted() {
            return "SUCCESS".equals(status) || "SUCCESS_WITH_FALLBACK".equals(status);
        }

        public Map<String, Object> toDetailMap() {
            Map<String, Object> detail = new HashMap<>();
            detail.put("taskId", task.getTaskId());
            detail.put("taskName", task.getTaskName());
            detail.put("taskInput", String.valueOf(input));
            detail.put("status", status);
            detail.put("threadName", threadName);
            detail.put("waitTime", getWaitTime());
            detail.put("duration", getDuration());
            detail.put("estimatedDuration", task.getEstimatedDuration());
            detail.put("usedFallback", usedFallback);

            if (result != null) {
                detail.put("resultSummary", summarizeResult(result));
            }
            if (exception != null) {
                detail.put("exception", exception.getMessage());
            }
            if (fallbackException != null) {
                detail.put("fallbackException", fallbackException.getMessage());
            }

            return detail;
        }

        public Map<String, Object> toSummaryMap() {
            Map<String, Object> summary = new HashMap<>();
            summary.put("taskId", task.getTaskId());
            summary.put("taskName", task.getTaskName());
            summary.put("status", status);
            summary.put("duration", getDuration());
            summary.put("estimatedDuration", task.getEstimatedDuration());
            summary.put("threadName", threadName);
            return summary;
        }

        private String summarizeResult(Object result) {
            if (result == null) return "null";
            if (result instanceof String) {
                String str = (String) result;
                return str.length() > 100 ? str.substring(0, 100) + "..." : str;
            }
            if (result instanceof Map) {
                return "Map[" + ((Map<?, ?>) result).size() + " entries]";
            }
            if (result instanceof List) {
                return "List[" + ((List<?>) result).size() + " items]";
            }
            return result.getClass().getSimpleName();
        }

        // Getters
        public ParallelTask<INPUT, OUTPUT> getTask() {
            return task;
        }

        public INPUT getInput() {
            return input;
        }

        public String getExecutionId() {
            return executionId;
        }

        public long getCreateTime() {
            return createTime;
        }

        public long getStartTime() {
            return startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public String getStatus() {
            return status;
        }

        public String getThreadName() {
            return threadName;
        }

        public OUTPUT getResult() {
            return result;
        }

        public Exception getException() {
            return exception;
        }

        public boolean isUsedFallback() {
            return usedFallback;
        }
    }

}