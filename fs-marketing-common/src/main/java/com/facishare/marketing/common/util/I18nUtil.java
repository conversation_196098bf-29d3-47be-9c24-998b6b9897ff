package com.facishare.marketing.common.util;

import com.facishare.fcp.protocol.FcpRequest;
import com.facishare.fcp.service.DefaultFcpServiceContextManager;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.I18nKeyEnumV2;
import com.facishare.marketing.common.enums.I18nKeyEnumV3;
import com.facishare.marketing.common.enums.I18nKeyStaticEnum;
import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Locale;

/**
 * 国际化工具类
 */
@Slf4j
public class I18nUtil {
    public static final String ZH_CN = "zh-CN";
    public static final String EN = "en";
    static {
        try {
            I18nClient.getInstance().initWithTags("mark", "server");
        } catch (Throwable e) {
            log.warn("init error", e);
        }
    }

    public static String getLanguage() {
        String language;
        String step = "";
        try {
            step = "TraceContext";
            language = TraceContext.get().getLocale();
            if (StringUtils.isNotEmpty(language)) {
                log.info("get language TraceContext {}", language);
                return language;
            }
            step = "FcpContext";
            FcpRequest fcpRequest = DefaultFcpServiceContextManager.getInstance().getContext().getFcpRequest();
            if (fcpRequest != null) {
                Locale locale = DefaultFcpServiceContextManager.getInstance().getContext().getAuthInfo().getLocale();
                language = locale.toLanguageTag();
                log.info("get language FcpContext {}", language);
            } else {
                language = ZH_CN;
                log.info("get language FcpContext Exception, use default language: {} step: {}", language, step);
            }
        } catch (Throwable e) {
            language = ZH_CN;
            log.info("get language Exception, use default language: {} step: {}", language, step, e);
        }
        log.info("getLanguage result {}", language);
        return language;
    }

    public static String get(String key, String defaultVal) {
        String value = getByKey(key, 0, getLanguage());
        if (value == null) {
            return defaultVal;
        }
        return value;
    }

    public static String get(I18nKeyEnum i18nKeyEnum) {
        String value = null;
        if (i18nKeyEnum != null) {
            value = getByKey(i18nKeyEnum.getI18nKey(), 0, getLanguage());
        }
        if (value == null && i18nKeyEnum != null) {
            return i18nKeyEnum.getDefaultValue();
        }
        return value;
    }

    public static String get(I18nKeyEnumV2 i18nKeyEnum) {
        String value = null;
        if (i18nKeyEnum != null) {
            value = getByKey(i18nKeyEnum.getI18nKey(), 0, getLanguage());
        }
        if (value == null && i18nKeyEnum != null) {
            return i18nKeyEnum.getDefaultValue();
        }
        return value;
    }

    public static String get(I18nKeyEnumV3 i18nKeyEnum) {
        String value = null;
        if (i18nKeyEnum != null) {
            value = getByKey(i18nKeyEnum.getI18nKey(), 0, getLanguage());
        }
        if (value == null && i18nKeyEnum != null) {
            return i18nKeyEnum.getDefaultValue();
        }
        return value;
    }

    public static String get(I18nKeyStaticEnum i18nKeyEnum) {
        String value = null;
        if (i18nKeyEnum != null) {
            value = getByKey(i18nKeyEnum.getKey(), 0, getLanguage());
        }
        if (value == null && i18nKeyEnum != null) {
            return i18nKeyEnum.getValue();
        }
        return value;
    }

    public static String getSuitedLangText(String textCN, String textEN) {
        if(!ZH_CN.equals(getLanguage())) {
            return StringUtils.isNotBlank(textEN) ? textEN : textCN;
        }
        return textCN;
    }

    public static String getStaticByKey(String i18nKey) {
        String value = null;
        if (i18nKey != null) {
            value = getByKey(i18nKey, 0, getLanguage());
        }
        if (value == null) {
            return I18nKeyStaticEnum.getValueByKey(i18nKey);
        }
        return value;
    }

    private static String getByKey(String i18nKey, long tenantId, String language) {
        try {
            return I18nClient.getInstance().get(i18nKey, tenantId, language);
        } catch (Exception e) {
            log.error("getByKey error, key: {}", i18nKey, e);
        }
        return null;
    }
}
