package com.facishare.marketing.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/19.
 */
@AllArgsConstructor
@Getter
public enum MarketingEventEnum {
    PROMOTION_ACTIVITY("1","促销活动"),
    BRAND_ACTIVITY("2","品牌活动"),
    MEETING_SALES("3","会议营销"),
    SEARCH_ENGINE("4","搜索引擎"),
    INTERNET_ADVERTISEMENT ("5","互联网广告"),
    PRINT_ADVERTISING ("6","平面媒体广告"),
    TELEVISION_ADVERTISING ("7","电视媒体广告"),
    PUBLIC_RELATION ("8","关系公关"),
    TELEMARKETING ("9","电话营销"),
    SMS_MARKETING ("10","短信营销"),
    EMAIL_MARKETING ("11","邮件营销"),
    CONTENT_MARKETING("content_marketing", "线上活动"),
    OFFLINE_CONFERENCE("offline_conference", "线下会议"),
    LIVE_MARKETING("live_marketing", "在线直播"),
    AD_MARKETING("advertising_marketing", "广告营销"),
    MULTIVENUE_MARKETING("multivenue_marketing", "多会场活动"),
    TARGET_CROWD_OPERATION_ONCE("once", "单次目标人群运营"),
    TARGET_CROWD_OPERATION_PERIODICITY("periodicity", "周期目标人群运营"),

    ;

    private String eventType;
    private String value;

    public static String getEventType(String eventType) {
        for (MarketingEventEnum marketingEventEnum : MarketingEventEnum.values()) {
            if (eventType.equals(marketingEventEnum.eventType)) {
                return marketingEventEnum.value;
            }
        }
        return "";
    }

    public static List<String> getAllEventType(){
        List<String> eventTypeLists = Lists.newArrayList();
        for (MarketingEventEnum marketingEventEnum : MarketingEventEnum.values()){
            eventTypeLists.add(marketingEventEnum.getEventType());
        }
        return eventTypeLists;
    }
}
