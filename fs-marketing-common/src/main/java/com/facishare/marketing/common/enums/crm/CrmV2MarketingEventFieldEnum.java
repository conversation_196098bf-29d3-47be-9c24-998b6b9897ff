package com.facishare.marketing.common.enums.crm;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Created  By zhoux 2019/05/15
 **/
@Getter
@ToString
@AllArgsConstructor
public enum CrmV2MarketingEventFieldEnum {
    ID("_id"),
    NAME("name"),
    BEGIN_TIME("begin_time"),
    END_TIME("end_time"),
    LOCATION("location"),
    EVENT_TYPE("event_type"),
    PARENT_ID("parent_id"),

    AD_SOURCE("ad_source"),   //广告渠道
    AD_ACCOUNT("advertising_account"),
    // ocpc投放
    OCPC_LAUNCH("ocpc_launch"),
    // 广告类型
    ADVERTISING_TYPE("advertising_type"),


    ;

    private String fieldName;




}
