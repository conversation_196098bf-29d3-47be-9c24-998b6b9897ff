package com.facishare.marketing.common.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Set;

@Getter
@AllArgsConstructor
public enum MarketingPluginTypeEnum {

    SUYUAN_COUPON(1,"溯源优惠券"),

    SOCIETY_DISTRIBUTE(2,"社会化分销"),

    PARTNER_MARKETING(3,"伙伴营销"),

    CUSTOMER_SERVICE(4,"53KF云客服线索对接"),

    // 直播相关插件
    POLYV_LIVE(5,"保利威直播对接"),
    XIAOETONG_LIVE(501,"小鹅通直播对接"),
    VHALL_LIVE(502,"微吼直播对接"),
    CHANNELS_LIVE(503,"视频号直播对接"),
    MUDU_LIVE(504,"目睹直播对接"),

    //线索相关插件
    ZHIHU_CLUE(11,"知乎营销画报连接器"),
    SOGOU_CLUE(12,"搜狗线索通连接器"),
    KUAISHOU_CLUE(13,"快手线索CRM连接器"),
    SHENMA_CLUE(14,"UC神马建站工具连接器"),
    DIANJING_CLUE(15,"360点睛营销"),

    // 30~40 广告营销插件
    BAIDU_AD(30,"百度广告"),
    TENCENT_AD(31,"腾讯广告"),
    HEADLINES_AD(32,"头条广告"),
    WALLET(33,"企业钱包"),
    AD_OCPC(34, "广告效果数据回传"),


    // 40~50 移动端插件
    MOBILE_PHOTO_LIBRARY(41,"移动端图片库"),

    // 100-200 其他插件
    KNOWLEDGE_MANAGEMENT_APP(100,"话术库插件"),
    SERVICE_KNOWLEDGE(101,"知识库插件"),
    OUT_LINK_QR_CODE(51,"外部链接二维码"),
    MARKETING_ORDER_INTEGRATION(52,"营销订货一体化"),
    MARKETING_CUSTOMER_INTEGRATION(53,"营销客服一体化"),
    DATA_PERMISSION(54,"数据权限隔离"),
    WECHAT_COUPON(55,"微信商家券"),
    MARKETING_USER_PLUGIN(56,"营销用户插件"),
    FACEBOOK_CLUE(57,"facebook线索插件"),
    LINKEDIN_CLUE(58,"linkedin线索插件"),
    GOOGLE_AD(59,"谷歌广告插件"),
    MARKETING_SDR(60,"SDR插件"),
    MARKETING_DATA_ISOLATION(63,"营销数据隔离"),
    WHATS_APP(61,"WhatsApp营销"),
    LINKEDIN(62,"linkedin广告"),
    MEMBER_MARKETING(64,"会员营销"),

    MARKETING_AI_PLUGIN(66, "AI营销插件"),

    DIGITAL_HUMANS(1001, "小冰数字人"),
    MEMBER_LOYALTY_PLAN_PLUGIN(67, "会员忠诚度计划插件"),

    // 201-300 新分版
    CONFERENCE_PLUGIN(201,"会议营销插件"),
    LIVE_PLUGIN(202,"直播营销插件"),
    AD_PLUGIN(203,"广告营销插件"),
    OFFICIAL_WEBSITE_PLUGIN(204,"官网营销插件"),
    MP_SUITE_PLUGIN(205,"数字展厅插件"),
    STAFF_PROMOTION_PLUGIN(206,"全员营销插件"),
    SMS_MARKETING_PLUGIN(207,"短信营销插件"),
    EMAIL_MARKETING_PLUGIN(208,"邮件营销插件"),
    WECHAT_MARKETING_PLUGIN(209,"企微营销插件"),
    OFFICIAL_ACCOUNTS_PLUGIN(210,"公众号营销插件"),
    OPERATION_PLAN_PLUGIN(211,"运营计划插件"),
    SOCIAL_DISTRIBUTION_PLUGIN(212,"社会化分销/营销流程 插件"),
    FACEBOOK_MARKETING_PLUGIN(213,"Facebook营销插件"),
    XIAO_HONG_SHU_MARKETING_PLUGIN(214,"小红书营销插件"),
    DOU_YIN_LAI_KE_MARKETING_PLUGIN(215,"抖音来客营销插件"),
    ;


    private int type;

    private String name;

    public static String getName(Integer value) {
        MarketingPluginTypeEnum[] pluginTypeEnums = values();
        for (MarketingPluginTypeEnum marketingPluginTypeEnum : pluginTypeEnums) {
            if (value == marketingPluginTypeEnum.type) {
                return marketingPluginTypeEnum.name;
            }
        }
        return null;
    }

    public static MarketingPluginTypeEnum fromType(Integer type) {
        MarketingPluginTypeEnum[] pluginTypeEnums = values();
        for (MarketingPluginTypeEnum marketingPluginTypeEnum : pluginTypeEnums) {
            if (type == marketingPluginTypeEnum.type) {
                return marketingPluginTypeEnum;
            }
        }
        return null;
    }

    public static List<Integer> getAdPluginTypeList() {
        return Lists.newArrayList(MarketingPluginTypeEnum.BAIDU_AD.getType(), MarketingPluginTypeEnum.TENCENT_AD.getType(), MarketingPluginTypeEnum.HEADLINES_AD.getType(), MarketingPluginTypeEnum.GOOGLE_AD.getType());
    }
}
