package com.facishare.marketing.common.enums;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.Set;

/**
 * 对象类型，用于字段中的object_type.
 */
@Getter
public enum ObjectTypeEnum {

    /**
     * 名片
     */
    CARD(1, "名片"),
    /**
     *
     * 线索
     */
    LEAD(2, "线索"),
    /**
     * 客脉
     */
    MANKEEP(3, "客脉"),
    /**
     * 产品
     */
    PRODUCT(4, "产品"),
    /**
     * 标签
     */
    TAG(5, "标签"),

    /**
     * 文章
     */
    ARTICLE(6, "文章"),
    /**
     * 图片
     */
    IMAGE(7, "图片"),
    /**
     * 文件
     */
    FILE(8, "文件"),
    /**
     * 动态
     */
    FEED(9, "动态"),
    /**
     * 客户
     */
    CUSTOMER(10, "客户"),
    /**
     * 查看转发
     */
    LOOK_UP_FORWARDED(11, "查看转发"),
    /**
     * 私信互动
     */
    SEND_MESSAGE(12, "私信互动"),
    /**
     * 活动
     */
    ACTIVITY(13, "活动"),
    /**
     * 账号（神策统计使用）
     */
    ACCOUNT(14, "账号"),

    /**
     * 推广(神策统计使用)
     */
    NOTICE(15, "推广"),
    /**
     * 自定义表单
     */
    CUSTOMIZE_FORM(16, "自定义表单"),
    /**
     * 视频
     */
    VIDEO(17, "视频"),

    /**
     * 企业动态
     */
    ENTERPRISE_FEED(18, "企业动态"),

    /**
     * 营销活动
     */
    MARKETING_ACTIVITY(19, "营销活动"),

    /**
     * 社会化分销
     */
    DISTRIBUTION(20, "社会化分销"),

    /**
     * 企业群组
     */
    ENTERPRISE_SOCIAL_GROUP(21, "企业群组"),

    /**
     * 短信
     */
    SMS(22, "短信"),

    /**
     * 移动营销通(神策统计使用)
     */
    APP_MARKETING(23, "移动营销通"),

    /**
     * 二维码海报
     */
    QR_POSTER(24, "二维码海报"),

    /**
     * 活动邀请函
     */
    ACTIVITY_INVITATION(25, "活动邀请函"),

    /**
     * 微页面
     */
    HEXAGON_SITE(26, "微页面"),

    /**
     * 微页面子页面
     */
    HEXAGON_PAGE(27, "微页面"),

    /**
     * 官网
     */
    OFFICIAL_WEBSITE(28, "官网"),

    /**
     * 官网追踪页面
     */
    OFFICIAL_WEBSITE_TRACK(29, "官网跟踪页面"),

    /**
     * 直播
     */
    LIVE(30, "直播"),

    /**
     * 邮件营销Task
     */
    MAIL_TASK(31, "邮件"),

    /**
     * 官网事件/属性
     */
    OFFICIAL_WEBSITE_EVENT_ATTRIBUTES(32, "官网事件"),
    
    /**
     * 不再使用，请使用ACTIVITY 13
     */
    @Deprecated
    CONFERENCE(33, "会议"),

    /**
     * 文件库文件
     */
    FILE_LIBRARY(34, "文件库文件"),

    /**
     * 优惠券
     */
    WX_COUPON(35, "优惠券"),


    /**
     * 员工活码
     */
    FAN_CODE(36, "员工活码"),

    /**
     * 企微群活码
     */
    QY_GROUP_CODE(37, "企微群活码"),

    /**
     * 企微欢迎语
     */
    QY_WELCOME_MSG(38, "企微欢迎语"),

    /**
     * 企微SOP
     */
    QY_SOP(39, "企微SOP"),

    /**
     * 企微客户群SOP
     */
    QY_GROUP_SOP(40, "企微群SOP"),

    /**
     * AI聊天分享页
     */
    AI_SHARE_PAGE(41, "AI聊天分享页"),
    /**
     * SOP邮件通知任务
     */
    SOP_MAIL_TASK(42, "SOP邮件通知"),
    /**
     * CTA
     */
    CTA(43, "CTA组件"),
    /**
     * 微信公众号
     */
    WX_SERVICE_ACCOUNT(1000, "微信公众号"),
    /**
     * 渠道二维码
     */
    WX_QR_CODE(1001, "渠道二维码"),
    /**
     * 微信公众号菜单
     */
    WX_SERVICE_MENU(1002, "微信公众号菜单"),
    /**
     * 公众号链接
     */
    WX_SERVICE_LINK(1003, "公众号链接"),

    MEMBER_MARKETING_NOTICE(1007, "会员营销通知"),

    /**
     * ===============================================
     * 后台自用type(5000 - 6000)
     * ===============================================
     */
    /**
     * 市场活动场景下会员CRM映射配置
     */
    MEMBER_MARKETING_EVENT_CRM_CONFIG(5000, "会员CRM映射"),

    /**
     * 微页面模板
     */
    HEXAGON_TEMPLATE(5001, "微页面模板"),

    /**
     * 优惠券模板
     */
    COUPON_TEMPLATE(5002, "优惠券模板"),

    /**
     * 名片模板
     */
    CARD_TEMPLATE(5003, "名片模板"),

    /**
     * 官网会员登陆模板
     */
    MEMBER_LOGIN_TEMPLATE(9527, "会员登录模板"),
    MEMBER_REGISTER_TEMPLATE(9528, "会员注册模板"),

    OUT_LINK(9999, "外部链接"),

    USER_MARKETING_GROUP(10000, "目标人群"),

    //标签模型
    TAG_MODEL(10001, "标签"),

    CHAT_ONLINE(10002, "在线客服"),

    // 落地页
    LANDING_PAGE(10003, "落地页"),

    KEYWORD(10004, "关键词"),

    QYWX_EMPLOYEE(10005, "企微员工"),

    QYWX_GROUP(10006, "企微群"),

    //公众号群发消息
    WECHAT_SERVICE_MESSAGE(10007, "公众号群发消息"),

    //公众号模板消息
    WECHAT_SERVICE_TEMPLATE_MESSAGE(10008, "公众号模板消息"),

    //日签海报
    SIGNUP_POSTER(10009, "日签海报"),

    //"微信用户"
    WECHAT_FAN(10010, "微信用户"),
    //"会员"
    MEMBER(10011, "会员"),
    EMAIL_MATERIAL(10012, "邮件物料"),

    ;

    private final int type;

    private final String name;

    ObjectTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public static List<Integer> getAllTypes(){
        List<Integer> allTypes = Lists.newArrayList();
        for (ObjectTypeEnum value : ObjectTypeEnum.values()) {
            allTypes.add(value.getType());
        }
        return allTypes;
    }

    public static ObjectTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
            if (objectTypeEnum.getType() == type) {
                return objectTypeEnum;
            }
        }
        return null;
    }

    public static boolean isSpreadContentObjectType(Integer objectType) {
        return ImmutableSet.of(CARD, ARTICLE, PRODUCT, IMAGE, FILE, ACTIVITY).contains(getByType(objectType));
    }

    public static ObjectTypeEnum fromContentType(Integer contentType){
        if(contentType == null){
            throw new IllegalArgumentException();
        }
        switch (contentType){
            case 1:
                return ObjectTypeEnum.ARTICLE;
            case 2:
                return ObjectTypeEnum.IMAGE;
            case 3:
                return ObjectTypeEnum.ACTIVITY;
            case 4:
                return ObjectTypeEnum.PRODUCT;
            case 5:
                return ObjectTypeEnum.QR_POSTER;
            case 10:
                return ObjectTypeEnum.HEXAGON_SITE;
            case 11:
                return ObjectTypeEnum.CUSTOMIZE_FORM;
            default:
                throw new IllegalArgumentException();
        }
    }

    public int toNoticeContentType() {
        switch (this) {
            case PRODUCT:
                return NoticeContentTypeEnum.PRODUCT.getType();
            case ARTICLE:
                return NoticeContentTypeEnum.ARTICLE.getType();
            case ACTIVITY:
                return NoticeContentTypeEnum.ACTIVITY.getType();
            case QR_POSTER:
                return NoticeContentTypeEnum.QR_POSTER.getType();
            case HEXAGON_SITE:
                return NoticeContentTypeEnum.HEXAGON.getType();
            case CUSTOMIZE_FORM:
                return NoticeContentTypeEnum.CUSTOMIZE_FORM_DATA.getType();
            case OUT_LINK:
                return NoticeContentTypeEnum.OUT_LINK.getType();
            case IMAGE:
                return NoticeContentTypeEnum.PICTURE.getType();
            case FILE:
                return NoticeContentTypeEnum.QYWX_FILE.getType();
            case VIDEO:
                return NoticeContentTypeEnum.QYWX_VIDEO.getType();
            default:
                throw new UnsupportedOperationException();
        }
    }

    //推广明细对象内容分类选项和objecttype的映射关系
    public static String getObjectTypeName(Integer objectType){
        if(objectType == null){
            return "other";
        }
        switch (objectType){
            case 1:
                return "10";//"名片";
            case 4:
                return "3";//"产品";
            case 6:
                return "2";//"文章";
            case 13:
                return "9";//"会议";
            case 26:
                return "1";   //"微页面";
            case 8:
                return "4";//"文件";
            case 17:
                return "5";//"视频";
            case 7:
                return "6";//"图片";
            case 24:
                return "7";//"二维码海报";
            case 30:
                return "8";//"直播";
            default:
                return "other";
        }
    }

    public static Integer getObjectTypeContentType(String contentType){
        if(contentType == null){
            return null;
        }
        switch (contentType){
            case "pdf":
                return ObjectTypeEnum.HEXAGON_SITE.getType();
            case "article":
                return ObjectTypeEnum.ARTICLE.getType();
            case "external_link":
                return ObjectTypeEnum.OUT_LINK.getType();
            default:
                return null;
        }
    }

    public static final Set<Integer> ADD_TAG_OBJECT_TYPE = ImmutableSet.of(ObjectTypeEnum.FILE.getType(), ObjectTypeEnum.HEXAGON_SITE.getType(), ObjectTypeEnum.ARTICLE.getType(),
            ObjectTypeEnum.OUT_LINK.getType());
}
