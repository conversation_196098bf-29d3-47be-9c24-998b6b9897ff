package com.facishare.marketing.common.enums.crm;

import com.google.common.collect.ImmutableSet;
import lombok.Getter;
import lombok.ToString;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@ToString
public enum CrmObjectApiNameEnum {
    CRM_LEAD("LeadsObj", 1, "线索"),//
    CUSTOMER("AccountObj", 2, "客户"),//
    CONTACT("ContactObj", 3, "联系人"),//
    OPPORTUNITY("OpportunityObj", 4 ,"商机"),
    /**
     * crm微信对象
     * **/
    WECHAT("WechatFanObj", 5 ,"微信用户"),//
    MEMBER("MemberObj", 6 ,"会员"),//
    PARTNER("PartnerObj", 7,"合作伙伴"),
    NEW_OPPORTUNITY("NewOpportunityObj", 8, "商机2.0"),
    MARKETING_ACTIVITY("MarketingActivityObj", 9, "营销活动"),

    SALES_ORDER("SalesOrderObj", 10, "销售订单"),
    MARKETING_PROCESS_OBJ("MarketingProcessObj", 11, "营销流程对象"),
    MARKETING_PROCESS_LATENCY_RESULT_OBJ("MarketingProcessLatencyResultObj", -1, "营销流程等待结果对象"),
    USER_MARKETING_ACCOUNT_OBJ("UserMarketingAccountObj", 12, "营销用户对象"),
    MARKETING_EVENT("MarketingEventObj", 13, "市场活动"),
//    SUB_MARKETING_EVENT("SubMarketingEventObj", 14, "子级市场活动"),
    MEMBER_GRADE("MemberGradeObj", 15, "会员等级"),
    DISTRIBUTION_CLUE("object_h2XUo__c", 798, "分销线索"),   //自定义对象没有number
    SDR_CLUE("object_Xh012__c", 799, "SDR对象"),
    WECHAT_WORK_EXTERNAL_USER_OBJ("WechatWorkExternalUserObj", -1, "企业微信客户"),//
    WECHAT_FRIENDS_RECORD_OBJ("WechatFriendsRecordObj", -1, "企微员工加客户好友记录对象"),
    WECHAT_ACCOUNT_STATISTICS_OBJ("WechatAccountStatisticsObj", -1, "企微客户统计"),
    WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ("WechatAccountGroupStatisticsObj", -1, "企微客户群统计"),
    EMPLOYEE_PROMOTE_DETAIL_OBJ("EmployeePromoteDetailObj", -1, "员工推广明细"),
    CONTENT_PROPAGATION_DETAIL_OBJ("ContentPropagationDetailObj", -1, "内容传播明细"),
    CAMPAIGN_MEMBERS_OBJ("CampaignMembersObj", -1, "活动成员"),
    ENTERPRISE_INFO_OBJ("EnterpriseInfoObj", -1, "企业库"),
    MARKETING_KEYWORD("MarketingKeywordObj", -1, "营销关键词"),
    MARKETING_KEYWORD_PLAN("KeywordServingPlanObj", -1, "关键词投放计划"),
    TERM_SERVING_LINES("TermServingLinesObj", -1, "关键词投放明细"),
    WECHAT_COUPON_OBJ("WechatCouponObj", -1, "微信商家券"),
    USER_COUPON_OBJ("UserCouponObj", -1, "用户券"),
    COUPON_OBJ("CouponObj", -1, "优惠券"),
    COUPON_INST_OBJ("CouponInstanceObj", -1, "优惠券实例"),
    COUPON_PLAN_OBJ("CouponPlanObj", -1, "优惠券方案"),
    WECHAT_GROUP_OBJ("WechatGroupObj", -1, "企微客户群"),
    WECHAT_GROUP_USER_OBJ("WechatGroupUserObj", -1, "企微客户群成员"),
    AD_DATA_RETURN_DETAIL_OBJ("AdDataReturnDetailObj", -1 ,"广告回传明细"),
    CONTRACT_OBJ("ContractObj", -1 ,"合同"),
    QUOTE_OBJ("QuoteObj", -1 ,"报价单"),
    // 线索转换，CRM并没有这个对象，给广告数据回传上报规则定义用的
    LEAD_CONVERT_TO("LeadConvertTo", -1 ,"线索转换"),
    LANDING_PAGE_OBJ("LandingPageObj", -1 ,"落地页"),
    MKT_CONTENT_MGMT_LOG_OBJ("MktContentMgmtLogObj", -1 ,"营销内容管理日志"),
    SMS_SEND_RECORD_OBJ("SmsSendRecordObj", -1 ,"短信发送记录"),
    ENTERPRISE_RELATION_OBJ("EnterpriseRelationObj", -1 ,"互联企业"),
    PUBLIC_EMPLOYEE_OBJ("PublicEmployeeObj", -1 ,"互联用户"),
    MARKETING_PROMOTION_SOURCE_OBJ("MarketingPromotionSourceObj", -1 ,"营销推广来源"),
    CUSTOMER_SERVICE_SESSION_OBJ("CustomerServiceSessionObj", -1 ,"客服会话列表"),
    MARKETING_LEAD_SYNC_RECORD_OBJ("MarketingLeadSyncRecordObj", -1 ,"营销线索同步记录"),
    ADVERTISING_DETAILS_OBJ("AdvertisingDetailsObj", -1 ,"广告投放明细"),
    PERSONNEL_OBJ("PersonnelObj", -1 ,"人员"),
    EMAIL_SEND_RECORD_DETAIL_OBJ("EmailSendRecordObj", -1 ,"邮件发送明细"),
    SERVICE_KNOWLEDGE_OBJ("ServiceKnowledgeObj", -1 ,"知识记录"),

    LANDING_PAGE_VISITOR_DETAIL_OBJ("LandingPageVisitorDetailObj", -1 ,"落地页访问明细"),

    WECHAT_EMPLOYEE_OBJ("WechatEmployeeObj", -1 ,"企业微信员工"),

    MEMBER_GROWTH_VALUE_DETAIL_OBJ("MemberGrowthValueDetailObj", -1 ,"会员成长值明细"),

    MEMBER_GRADE_EQUITIES_RULE_OBJ("MemberGradeEquitiesRuleObj", -1 ,"会员等级权益规则"),

    MEMBER_EQUITIES_OBJ("MemberEquitiesObj", -1 ,"会员权益"),

    MEMBER_INTEGRAL_DETAIL_OBJ("MemberIntegralDetailObj", -1 ,"会员成长值明细"),

    MARKETING_BEHAVIOR_OBJ("MarketingBehaviorObj", -1 ,"营销动态"),

    COUPON_DISTRIBUTION_OBJ("CouponDistributionObj",-1,"优惠券下发记录"),

    WHATSAPP_SEND_RECORD_OBJ("WhatsAppSendRecordObj",-1,"whatsapp发送记录"),

    LEADS_TRANSFER_LOG_OBJ("LeadsTransferLogObj",-1,"线索转换记录"),

    USER_BEHAVIOR_RECORDS_OBJ("UserBehaviorRecordsObj",-1,"用户行为记录"),

    MARKETING_CONTENT_OBJ("MarketingContentObj",-1,"营销内容库"),

    USER_MARKETING_ACCOUNT_CHANGE_LOG_OBJ("UserMarketingAccountChangeLogObj",-1,"营销用户变更记录"),
    WECHAT_SESSION_OBJ("WechatSessionObj",-1,"企业微信会话存档"),
    WHATSAPP_CHAT_MESSAGE_OBJ("WhatsAppChatMessageObj",-1,"WhatsApp聊天记录"),

    CAMPAIGN_BUDGET_OBJ("CampaignBudgetObj", -1, "市场活动费用"),

    CAMPAIGN_BUDGET_LINE_ITEM_OBJ("CampaignBudgetLineItemObj", -1, "市场活动费用明细"),
    
    SOCIAL_PLATFORM_OBJ("SocialPlatformObj", -1, "社媒平台"),
  
    SOCIAL_ACCOUNT_OBJ("SocialAccountObj", -1, "社媒账号"),

    SOCIAL_POST_OBJ("SocialPostObj", -1, "社媒帖子"),

    SOCIAL_INTERACTION_OBJ("SocialInteractionObj", -1, "社媒互动"),

    SOCIAL_POST_METRICS_OBJ("SocialPostMetricsObj", -1, "社媒统计指标"),

    SOCIAL_STREAM_OBJ("SocialStreamObj", -1, "社媒流"),

    ;

    private String name;
    private int number;
    private String label;

    CrmObjectApiNameEnum(String name, int number, String label) {
        this.name = name;
        this.number = number;
        this.label = label;
    }

    public static CrmObjectApiNameEnum fromName(String name) {
        for (CrmObjectApiNameEnum crmObjectApiNameEnum : CrmObjectApiNameEnum.values()) {
            if (crmObjectApiNameEnum.getName().equals(name)) {
                return crmObjectApiNameEnum;
            }
        }
        return null;
    }

    public static Set<String> getAllUserApiNames(){
        return ImmutableSet.of(CRM_LEAD.getName(), CUSTOMER.getName(), CONTACT.getName(), WECHAT.getName(), WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), MEMBER.getName());
    }
    //广告管理员对象权限
    public static Set<String> getAllAdApiNames(){
        return ImmutableSet.of(AD_DATA_RETURN_DETAIL_OBJ.getName(), ADVERTISING_DETAILS_OBJ.getName(), LANDING_PAGE_OBJ.getName(), LANDING_PAGE_VISITOR_DETAIL_OBJ.getName(), TERM_SERVING_LINES.getName(),
                MARKETING_KEYWORD.getName(),MARKETING_KEYWORD_PLAN.getName());
    }


    //企微管理员对象权限
    public static Set<String> getAllQywxApiNames(){
        return ImmutableSet.of(WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), WECHAT_FRIENDS_RECORD_OBJ.getName(), WECHAT_ACCOUNT_STATISTICS_OBJ.getName(), WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ.getName(), WECHAT_GROUP_OBJ.getName(),
                WECHAT_GROUP_USER_OBJ.getName(),WECHAT_EMPLOYEE_OBJ.getName());
    }

    public static boolean isQywxObject(String objectApiName) {
        return WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(objectApiName) || WECHAT_FRIENDS_RECORD_OBJ.getName().equals(objectApiName) || WECHAT_GROUP_OBJ.getName().equals(objectApiName) || WECHAT_GROUP_USER_OBJ.getName().equals(objectApiName);
    }


//    //除了客户，线索，联系人外，市场管理员有所有对象的数据权限
//    public static Set<String> getAllMarketingManagerRoleApiNames(){
//        return Stream.of(getAllQywxApiNames(), getAllMemberApiNames(), getAllWechatApiNames(), getAllUniversalRoleApiNames(),getAllAdApiNames(),
//                        Sets.newHashSet(MARKETING_PROMOTION_SOURCE_OBJ.getName(),MKT_CONTENT_MGMT_LOG_OBJ.getName(),WECHAT_COUPON_OBJ.getName(),MARKETING_BEHAVIOR_OBJ.getName()))
//                .flatMap(Set::stream)
//                .collect(Collectors.toSet());
//    }
//
//
//
//    //会员管理角色对象权限
//    public static Set<String> getAllMemberApiNames(){
//        return ImmutableSet.of(MEMBER_GRADE.getName(), MEMBER.getName(), MEMBER_GROWTH_VALUE_DETAIL_OBJ.getName(), MEMBER_GRADE_EQUITIES_RULE_OBJ.getName(), MEMBER_EQUITIES_OBJ.getName(),
//                MEMBER_INTEGRAL_DETAIL_OBJ.getName());
//    }
//
//    //公众号运营对象权限
//    public static Set<String> getAllWechatApiNames(){
//        return ImmutableSet.of(WECHAT.getName());
//    }
//
//    //市场人员权限
//    public static Set<String> getAllUniversalRoleApiNames(){
//        return ImmutableSet.of(MARKETING_EVENT.getName(), MARKETING_ACTIVITY.getName(), SMS_SEND_RECORD_OBJ.getName(), EMAIL_SEND_RECORD_DETAIL_OBJ.getName());
//    }

}
