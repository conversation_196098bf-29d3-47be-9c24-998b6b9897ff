package com.facishare.marketing.api.arg.marketingactivity;

import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/8 17:54
 * @Version 1.0
 */
@Data
public class GetSendNotificationStatisticArg implements Serializable {
    @ApiModelProperty("参会人员列表")
    private List<String> campaignIds;
    /** {@link MarketingActivityActionEnum} */
    @ApiModelProperty("推广类型 2:微信公众号  3：短信  5：企业微信  6：邮件")
    private Integer spreadType;
    @ApiModelProperty("spreadType = 2时，需要指定wxAppId")
    private String wxAppId;

    public GetSendNotificationStatisticArg() {
    }

    public GetSendNotificationStatisticArg(List<String> campaignIds, Integer spreadType) {
        this.campaignIds = campaignIds;
        this.spreadType = spreadType;
    }

    public GetSendNotificationStatisticArg(List<String> campaignIds, Integer spreadType, String wxAppId) {
        this.campaignIds = campaignIds;
        this.spreadType = spreadType;
        this.wxAppId = wxAppId;
    }
}
