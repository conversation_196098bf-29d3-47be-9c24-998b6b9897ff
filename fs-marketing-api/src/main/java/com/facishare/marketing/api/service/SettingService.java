package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.distribution.OpenDistributeStatusArg;
import com.facishare.marketing.api.arg.miniAppSetting.ConfigMiniAppAutoUpgradeArg;
import com.facishare.marketing.api.arg.miniAppSetting.GetMiniAppAutoUpgradeStatusArg;
import com.facishare.marketing.api.arg.usermarketingaccount.MarketingUserGroupCustomizeObjectMappingArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.distribution.CommitClueRewardStatusResult;
import com.facishare.marketing.api.result.distribution.QueryClueAuditStatusResult;
import com.facishare.marketing.api.vo.SystemOperationRecordVO;
import com.facishare.marketing.common.result.Result;
import com.google.gson.internal.LinkedTreeMap;
//import com.facishare.marketing.web.arg.OpenDistributeStatusArg;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface SettingService {

    Result<AllConfigResult> listAllConfig(String ea, Integer fsUserId);

    Result<Void> updateSaveConfig(String ea, Integer fsUserId, boolean newIsSaveCustomerToCrmStatus);

    Result<Void> updateIsSaveMankeepToCrmEnabled(String ea, Integer fsUserId, boolean newIsSaveMankeepToCrmStatus);

    Result<Void> mergeFieldMappings(String ea, Integer fsUserId, List<FieldMappingConfigResult> newFieldMappingConfigResults);

    Result<ListMappingSettingsResult> listMappingSettings(String ea, Integer fsUserId, String mankeepObjectApiName, String crmObjectApiName);

    Result<Void> initMarketingData(String ea, Integer fsUserId);

    Result<Void> brushLibrary(String randomNum);

    Result<Void> openDistributeStatus(OpenDistributeStatusArg arg);

    Result<Void> updateDistributePlan(OpenDistributeStatusArg arg);

    Result<DistributeStatusResult> judgeDistributeStatus(String ea);

    Result<SocialDistributionDetailResult> getSocialDistributeTableDetail(String ea, String planId);

    Result<Void> setEnterpriseInfo(SetEnterpriseInfoArg arg);

    Result<EnterpriseInfoResult> queryEnterpriseInfo(String ea, Integer userId);

    Result<Void> setClueAuditStatus(String ea, Integer userId, Integer status);

    Result<QueryClueAuditStatusResult> queryClueAuditStatus(String ea, Integer userId);

    Result<Void> setCommitClueRewardStatus(String planId, Integer status, Double clueReward);

    Result<CommitClueRewardStatusResult> getCommitClueRewardStatus(String planId);

    Result<GetAppIdResult> getAppId();

    Result<GetBoundMiniappInfoResult> getBoundMiniappInfo(String ea, String platformId);

    Result<GetBoundMiniappInfoResult> getBoundMiniappInfoLightVersion(String ea, String platformId);

    Result<GetSimpleBoundMiniappInfoResult> getSimpleBoundMiniappInfo(String ea, String platformId);

    Result<String> commitCodeAndSubmitAudit(String ea, Integer userId, CommitCodeAndSubmitAuditArg commitCodeAndSubmitAuditArg);

    Result<String> releaseCode(String ea, ReleaseCodeArg releaseCodeArg);

    Result<String> unAuthWxAppId(String ea, UnAuthWxAppArg releaseCodeArg);

    Result<byte[]> getMaterialBytes(String ea, String wxAppId, String materialId);

    Result<List<RoleResult>> listRole(String ea);

    Result<List<UserRoleResult>> listUserRoles(String ea);

    Result<Void> addUserRoles(String ea, Set<Integer> employeeId, Set<String> roleIds);

    Result<Void> editUserRoles(String ea, Integer employeeId, Set<String> roleIds);

    Result<Void> deleteUserRoleByUser(String ea, Integer employeeId);

    Result<Void> deleteUserRole(String ea, Integer employeeId, Collection<String> roleIds);

    Result<Set<String>> getUserRoles(String ea, Integer employeeId);

    Result<Void> setMiniappIntroductionSite(String ea, SetMiniappIntroductionSiteConfigArg arg);
    
    Result<PageResult<SystemOperationRecordVO>> pageListOperationRecord(String ea, PageListOperationRecordArg arg);

    Result<ListParamsBySceneAndSpreadTypeResult> listParamsBySceneAndSpreadType(String ea, ListParamsBySceneAndSpreadTypeArg arg);

    /**
     *  设置目标人群自定义对象映射
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    Result setMarketingUserGroupCustomizeObjectMapping(String ea, Integer fsUserId, MarketingUserGroupCustomizeObjectMappingArg arg);

    /**
     * 查询目标人群自定义对象映射
     * @param ea
     * @return
     */
    Result<List<MarketingUserGroupCustomizeObjectMappingResult>> getMarketingUserGroupCustomizeObjectMapping(String ea);

    /**
     * 删除目标人群自定义对象映射
     * @param id
     * @return
     */
    Result deleteMarketingUserGroupCustomizeObjectMapping(String id);

    Result getH5AccessPermissionsSeeting(String objectId, Integer objectType, String ea);

    Result saveH5AccessPermissionsSeeting(String ea, Integer userId, H5AccessPermissionsSeetingArg arg);

    Result<EnterpriseSettingsResult> getEnterpriseSettings(String ea,Integer userId);

    /**
     * 根据企业是否灰度返回对应的apiName
     * @param ea
     * @return
     */
    Result<List<String>> getApiNameList(String ea);

    /**
     * 查询用户自己的域名
     * @param ea
     * @return
     */
    Result<String> getSpreadContentDomain(String ea);

    //删除缓存,重新获取配置文件并存最新的灰度设置
    Result getConfigProfile();

    Result<String> queryTenantBrandColor(TenantBrandColorArg arg);

    Result<Integer> getVersionAuditStatus(String wxAppId, String platform);


    void sendMarketingReport();

    void spreardReport(String ea);

    Result<EnterpriseChannelsAndRolesInfoResult> queryEnterpriseChannelsAndRolesInfo(String ea);

    //提前计算统计数据
    void calculateMarketingStatistic();

    Result<LinkedTreeMap<String,Object>> queryI18n(QueryI18nArg arg);


    //设置用户同步对象标签同步，开关打开后，关联营销用户时，将标签同步到所有关联对象
    Result setMarketingUserSyncObjectTagsStatus(String ea, Integer fsUserId, Integer syncObjetctTagStatus);

    Result<QueryMarketingUserSyncObjectTagsStatus> queryMarketingUserSyncObjectTagsStatus(String ea, Integer fsUserId);

    Result<Void> addUserAccessible(AddUserAccessibleArg arg);


    Result<Void> editUserAccessible(AddUserAccessibleArg arg);

    Result<UserAccessibleResult> queryUserAccessible(UserAccessibleArg arg);

    Result saveMiniappAccessPermissionsSetting(String ea, Integer fsUserId, MiniappAccessPermissionsSettingArg arg);

    Result getMiniappAccessPermissionsSetting(String ea);

    Result configMiniAppAutoUpgrade(String ea, Integer fsUserId, ConfigMiniAppAutoUpgradeArg arg);

    Result<String> getMiniAppAutoUpgradeStatus(String ea, Integer fsUserId, GetMiniAppAutoUpgradeStatusArg arg);

    Result<Boolean> isPluginEnabled(PluginEnabledArg arg);
}
