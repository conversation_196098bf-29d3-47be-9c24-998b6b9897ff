package com.facishare.marketing.api.result.calendar;

import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.common.enums.EventActivityStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/12.
 */
@Data
public class MarketingEventsBriefResult  implements Serializable {
    @ApiModelProperty(value = "市场活动ID")
    private String id;
    /**
     * {@link EventActivityStatusEnum}
     */
    @ApiModelProperty(value = "市场活动状态")
    private String bizStatus;
    @ApiModelProperty(value = "市场活动名称")
    private String name;
    @ApiModelProperty(value = "市场活动开始时间")
    private Long beginTime;
    @ApiModelProperty(value = "市场活动结束时间")
    private Long endTime;
    @ApiModelProperty(value = "市场活动类型")
    private String eventType;
    @ApiModelProperty(value = "市场活动位置")
    private String location;
    @ApiModelProperty(value = "实际成本")
    private Double actualCost;
    @ApiModelProperty(value = "负责人")
    private List<Integer> owners;
    @ApiModelProperty(value = "内容数")
    private Integer contentNum;
    @ApiModelProperty(value = "访问人数")
    private Integer uv;
    @ApiModelProperty(value = "访问次数")
    private Integer pv;
    @ApiModelProperty(value = "线索数")
    private Integer leadNum;
    @ApiModelProperty(value = "创建人")
    private Integer createBy;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    @ApiModelProperty(value = "关联子活动数")
    private Integer relateSubCount;
    @ApiModelProperty(value = "生命状态")
    private String lifeStatus;
    @ApiModelProperty(value = "人群数量")
    private Integer marketingGroupUserNum;
    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;
    @ApiModelProperty(value = "活动封面")
    private List<Map<String,Object>> cover;
    @ApiModelProperty("定时任务状态")
    private String timedTaskStatus;//todo:进行中； executing：执行中； executed：已结束； canceled：已取消； error：数据异常
}
