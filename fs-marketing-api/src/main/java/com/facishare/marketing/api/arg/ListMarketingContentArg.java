package com.facishare.marketing.api.arg;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ListMarketingContentArg extends PageArg implements Serializable {
    @ApiModelProperty(value = "市场活动Id ")
    private String marketingEventId;
    @ApiModelProperty(value = "objectTypes ")
    private  List<Integer> objectTypes;
}
