package com.facishare.marketing.api.vo.conference;

import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.typehandlers.value.MapLocation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2020/10/30.
 */
@Data
public class CreateOrUpdateConferenceVO implements Serializable{
    private String conferenceId;
    private String ea;
    private Integer fsUserId;
    private String title;
    private Long startTime;
    private Long endTime;
    private String location;

    @FilterLog
    private String conferenceDetails;
    private String coverImagePath;
    private boolean updateConferenceDetails;
    private boolean updateCoverImage;
    private boolean showActivityList;
    private CreateObjectDataModel.Arg createObjectDataModel;
    private String mapAddress;
    private MapLocation mapLocation;

    /**
     * 父级市场活动id
     */
    private String parentId;
    private String marketingTemplateId;



    @ApiModelProperty("原图APath")
    private String originalImageAPath;

    @ApiModelProperty("cdn截图位置")
    private List<PhotoCutOffset> cutOffsetList;
}
