package com.facishare.marketing.api.arg;

import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class MigrateEasArg implements Serializable {
    public List<String> eas;
    public Boolean initAll = false;
    public List<String> ids;
    public List<String> excludes;
    public String beanName;
    public String methodName;
    public Map<String, String> otherParams;
    private Boolean containTestEa;
    private Boolean onlyContainTestEa;
    private Boolean initGrayEa;

    public boolean verify(){
        if(BooleanUtils.isNotTrue(initAll) && (eas == null || eas.isEmpty())){
            return false;
        }
        return true;
    }
}
