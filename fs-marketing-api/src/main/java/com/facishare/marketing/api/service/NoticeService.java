package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.spreadTask.GetNoticeByUserArg;
import com.facishare.marketing.api.result.NoticeDetailResult;
import com.facishare.marketing.api.result.NoticeResult;
import com.facishare.marketing.api.result.spreadTask.GetNoticeByUserResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

import java.io.UnsupportedEncodingException;

/**
 * Created By tianh, 2018/7/2.
 **/
public interface NoticeService {
    Result<String> sendNotice(String ea, Integer fsUserId, NoticeSendArg vo);
    Result<Integer> sendNoticeCount(String ea, Integer fsUserId, NoticeSendArg vo);

    Result<String> sendPartnerNotice(String ea, Integer fsUserId, PartnerNoticeSendArg vo) throws UnsupportedEncodingException;

    Result<Void> sendNoticeById(String noticeId, String marketingActivityId,String marketingEventId);

    Result<Void> sendPartnerNoticeById(String noticeId, String marketingActivityId);

    Result<PageResult<NoticeResult>> listNotices(String ea, Integer fsUserId, ListNoticeArg vo);

    Result<PageResult<NoticeResult>> listPartnerNotices(String ea, Integer fsUserId, ListNoticeArg vo);

    Result<NoticeDetailResult> getDetail(GetNoticeDetailArg vo);

    Result sendNoticeInvite(String ea, Integer fsUserId);

    Result sendfsUserIdNoticeInvite(String ea, Integer fsUserId, Integer sendFsUserId);

    /**
     * 取消发送
     * @param noticeId
     * @param ea
     * @return
     */
    Result<Boolean> cancelSendSpread(String noticeId, String ea);

    /**
     * 撤回发送
     *
     * @param noticeId
     * @param ea
     * @param marketingActivityId
     * @return
     */
    Result<Boolean> revokeSendSpread(String noticeId, String ea, String marketingActivityId);

    /**
     * 重新按照指定人群推广，说明：不是重新创建推广
     * @param ea
     * @param userId
     * @param noticeId
     * @param marketingActivity
     * @return
     */
    Result<Void> spreadMarketingActivityToSpecialEmployeeAgain(String ea, Integer userId, String noticeId, String marketingActivity);

    Result<Void> partnerNoticeAgain(String ea, Integer userId, String associateId, String marketingActivityId) throws UnsupportedEncodingException;

    Result<String> sendCouponNotice(String ea, Integer fsUserId, PartnerNoticeSendArg vo) throws UnsupportedEncodingException;

    /**
     * 广告数据同步完成后发送同步结果
     *
     * @param ea
     * @param userId
     * @return
     */
    Result<Void> sendAdDataSyncNotice(String ea, Integer userId);

    /**
     * 员工端推广任务详情
     * @param arg
     * @return
     */
    Result<GetNoticeByUserResult> getNoticeByUser(GetNoticeByUserArg arg);
}
