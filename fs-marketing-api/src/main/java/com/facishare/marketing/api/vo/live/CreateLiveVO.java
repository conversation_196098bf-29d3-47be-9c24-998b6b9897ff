package com.facishare.marketing.api.vo.live;

import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2020/3/20.
 */
@Data
public class CreateLiveVO implements Serializable{
    private String id;               //id
    private String ea;               //企业账号
    private Integer corpId;          //ei
    private Integer fsUserId;        //员工账号
    private String title;            //直播标题
    private String desc;             //直播简介
    private String coverTaPath;      //直播封面tapath
    private String ext;              //封面后缀
    private Long startTime;          //开始时间
    private Long endTime;            //截至时间
    private String lectureUserName;  //讲师名称
    private Integer livePlatform;    //直播平台
    private String lecturePassword;  //讲师密码
    private Integer chatOn;          //是否启动直播聊天 0:开启  1：不开启
    private Integer maxLiveCount;    //直播最大观看次数
    private Integer autoRecord;      //0为否,1为是(默认为否),是否自动回放
    private TagNameList tagNames;    //设置标签
    private String xiaoetongLiveUrl; //小鹅通直播链接
    private String otherPlatformLiveUrl;          //直播链接，针对livePlatform=2
    private boolean showActivityList;  //是否在小程序活动列表展示
    private CreateObjectDataModel.Arg createObjectDataModel;  //设置市场活动必填字段
    private String xiaoetongLiveId;

    /**
     * 父级市场活动id
     */
    private String parentId;
    private String marketingTemplateId; //市场活动默认模板id


    @ApiModelProperty("原图APath")
    private String originalImageAPath;

    @ApiModelProperty("cdn截图位置")
    private List<PhotoCutOffset> cutOffsetList;

    private Integer subEvent = 0; //是否为子活动 1是 0否 当前只有目睹场所活动需要自动创建

    private String muduParentId; //目睹主活动id

    private String parentHexagonFromId; //父级微页面表单id

    //关联的账号id,目前只有视频号需要关联
    private String associatedAccountId;
}
