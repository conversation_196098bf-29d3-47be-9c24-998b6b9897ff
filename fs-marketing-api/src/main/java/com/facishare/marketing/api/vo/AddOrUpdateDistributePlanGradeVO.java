package com.facishare.marketing.api.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by ranluch on 2019/4/4.
 */
@Data
public class AddOrUpdateDistributePlanGradeVO implements Serializable{
    /**
     *  id主键
     * **/
    private String id;

    /**
     *  分销计划id
     * **/
    private String planId;

    /**
     *  等级
     * **/
    private Integer grade;

    /**
     *  等级对应的名称
     * **/
    private String name;

    /**
     * 1分销员等级，2招募等级
     * {@link com.facishare.marketing.common.enums.distribution.DistributorGradeTypeEnum}
     * **/
    private Integer type;

    /**
     * 升级规则类型：1订单总额，2有效线索数，3招募分销员数
     * {@link com.facishare.marketing.common.enums.distribution.DistributorGradeRuleTypeEnum}
     * **/
    private Integer ruleType;

    //升级条件1
    private Integer condition1;

    //升级条件2
    private Integer condition2;

    /**
     * 权益类型：1订单总额比例，2CRM销售订单的分销员分润总金额，3CRM销售订单的招募人奖励金额
     * {@link com.facishare.marketing.common.enums.distribution.DistributorRightsTypeEnum}
     * **/
    private Integer rightsType;

    //权益值1
    private Float value1;

    //权益值2
    private Float value2;

    //权益描述
    private String remark;

    /**
     *  创建人
     * **/
    private Integer fsUserId;
}
