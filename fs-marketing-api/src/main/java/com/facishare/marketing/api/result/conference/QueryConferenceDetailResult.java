package com.facishare.marketing.api.result.conference;

import com.facishare.marketing.api.result.ShareContentResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import com.facishare.marketing.common.typehandlers.value.MapLocation;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * Created by ranluch on 2019/7/17.
 */
@Data
public class QueryConferenceDetailResult implements Serializable {
    @ApiModelProperty("会议id")
    private String id;

    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("会议标题")
    private String title;

    @ApiModelProperty("会议开始时间")
    private Long startTime;

    @ApiModelProperty("会议结束时间")
    private Long endTime;

    @ApiModelProperty("会议类型")
    private String marketingEventType;

    @ApiModelProperty("会议地点")
    private String location;

    @ApiModelProperty("会议规模")
    private Integer scale;

    @FilterLog
    @ApiModelProperty("会议详情")
    private String conferenceDetails;

    @ApiModelProperty("会议报名截止时间")
    private Long enrollEndTime;

    @ApiModelProperty("会议报名审核")
    private Boolean enrollReview;

    @ApiModelProperty("会议按钮样式")
    private ButtonStyle enrollButton;

    @ApiModelProperty("会议状态 1 有效 2 失效 3 删除")
    private Integer status;

    @ApiModelProperty("表单id")
    private String formId;

    @ApiModelProperty("表单名称")
    private String formName;

    @ApiModelProperty("封面图片url")
    private String coverImageUrl;

    @ApiModelProperty("封面图片path")
    private String coverImagePath;

    @ApiModelProperty("封面缩略图url")
    private String coverImageThumbUrl;

    @ApiModelProperty("会议二维码url")
    private String qrUrl;

    @ApiModelProperty("会议二维码Apath")
    private String qrApath;

    @ApiModelProperty("会议进行状态 2 会议未开始 3 会议进行中 4 会议已结束")
    private Integer flowStatus;

    @ApiModelProperty("会议形式：1线下会议，2线上会议")
    private Integer type;

    @ApiModelProperty("关联的市场活动详情")
    private QueryMarketingEventDetailResult marketingEventDetail;

    @ApiModelProperty(value = "进群卡片图片链接")
    private String cardPhotoUrl;

    @ApiModelProperty("报名通知时机")
    private Integer enrollNoticePoint;

    @ApiModelProperty("会议审核人")
    private List<Integer> enrollCheckEmployee;

    @ApiModelProperty("会议审核人员部门id")
    private List<Integer> enrollCheckDepartment;

    @ApiModelProperty("会议创建人")
    private Integer creator;

    @ApiModelProperty("标签Name列表")
    private TagNameList tagNameList;

    @ApiModelProperty("纷享到微信设置")
    private ShareContentResult shareContentResult;

    @ApiModelProperty("会议详情站点id")
    private String activityDetailSiteId;

    @ApiModelProperty("会议是否完成相关映射")
    private boolean conferenceCompleteMapping;

    @ApiModelProperty("是否在小程序活动列表展示")
    private boolean showActivityList;

    @ApiModelProperty("outer会议审核人")
    private List<String> outEnrollCheckEmployee;

    @ApiModelProperty("地图组件上的会议地点")
    private String mapAddress;

    @ApiModelProperty("地图经纬度")
    private MapLocation mapLocation;


    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicMiniAppCutUrl;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicH5CutUrl;

    @ApiModelProperty("普通推广微页面分享与封面裁剪图url")
    private String sharePicOrdinaryCutUrl;

    @ApiModelProperty("原图封面url")
    private String sharePicOrdinaryUrl;

    @ApiModelProperty("移动端展示默认内容")
    private Boolean defaultContentMobileDisplay;

    @ApiModelProperty("移动端展示默认海报")
    private Boolean defaultPosterMobileDisplay;

    @ApiModelProperty("会议报名审核中提示信息")
    private String enrollPendingReviewTip;

    @ApiModelProperty("会议报名审核不通过提示信息")
    private String enrollReviewFailureTip;

}
