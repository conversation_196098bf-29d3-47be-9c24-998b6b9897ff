package com.facishare.marketing.api.arg.qywx.card;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created  By zhoux 2020/01/15
 **/
@Data
public class AddCardInfoArg implements Serializable {

    private String uid;

    private String id;

    private String name;

    private String avatar;

    private String avatarThumbnail;

    private Integer gender;

    private String phone;

    private String email;

    private String wechat;

    private String qq;

    private String introduction;

    private String vocation;

    private String province;

    private String city;

    private String department;

    private String companyName;

    private String companyAddress;

    private List<String> photoList;

    private String visualRangeString;

    private Integer from;  //见ReqFromEnum

    private Integer isVideoOperation;

    private String kmToken;

    private String videoUrl;

    private Integer isOperation;

    private String tradeCode;  //行业编码

    private List<AddCardFileVo> addCardFileList;

    @ApiModelProperty(value = "是否展示员工名片")
    private Boolean cardDisplay;

    private String videoCover;

    private Integer contentType;

    private String cardHexagonId;
}
