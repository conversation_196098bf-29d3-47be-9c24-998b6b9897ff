package com.facishare.marketing.api.arg.marketingactivity;

import com.facishare.marketing.api.arg.sms.SmsVarArg;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.api.vo.dingding.DingSendMessageVO;
import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO;
import com.facishare.marketing.api.vo.qywx.MomentMessageVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupSendMessageVO;
import com.facishare.marketing.common.enums.sms.mw.SmsGroupTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsSceneTypeEnum;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/25.
 */
@Data
@ToString(callSuper = true)
public class AddMarketingActivityArg implements Serializable {
    @ApiModelProperty(value = "推广类型: 1->全员推广 2->服务号推广 3->短信推广 4->个人推广 5->企业微信群发 6->邮件群发 7->伙伴营销 8->朋友圈营销", allowableValues = "1,2,3,4,5,6,7,8")
    private Integer spreadType;
    @ApiModelProperty(value = "市场活动ID")
    private String marketingEventId;
    @ApiModelProperty(value = "营销活动ID")
    private String marketingActivityId;
    @ApiModelProperty(value = "素材列表")
    private List<MaterialInfo> materialInfos;
     @ApiModelProperty(value = "微信消息类型")
    private Integer wechatMessageType;
    @ApiModelProperty(value = "推广活动审核数据")
    private MarketingActivityAuditData marketingActivityAuditData;
    @ApiModelProperty(value = "全员营销 ")
    private MarketingActivityNoticeSendVO marketingActivityNoticeSendVO;
    @ApiModelProperty(value = "伙伴营销 ")
    private MarketingActivityPartnerNoticeSendVO marketingActivityPartnerNoticeSendVO;
    @ApiModelProperty(value = "短信营销")
    private MarketingActivityGroupSenderVO marketingActivityGroupSenderVO;
    @ApiModelProperty(value = "微信营销活动")
    private WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO;
    @ApiModelProperty(value = "微信模版消息Data")
    private WeChatTemplateMessageData weChatTemplateMessageVO;
    @ApiModelProperty(value = "企业微信群发消息")
    private QywxGroupSendMessageVO qywxGroupSendMessageVO;
    @ApiModelProperty(value = "邮件营销")
    private MailServiceMarketingActivityVO mailServiceMarketingActivityVO;
    @ApiModelProperty(value = "钉钉模板消息")
    private DingSendMessageVO dingSendMessageVO;

    @ApiModelProperty(value = "企微朋友圈")
    private MomentMessageVO momentMessageVO;

    @ApiModelProperty(value = "whatsapp营销")
    private WhatsAppMarketingActivityArg whatsAppMarketingActivityArg;

    @Data
    public static class MarketingActivityAuditData implements Serializable {
        @ApiModelProperty(value = "发送人群")
        private String sendGroup;
        @ApiModelProperty(value = "发送人群数量")
        private Integer sendGroupCount;
        @ApiModelProperty(value = "执行员工/伙伴")
        private String executor;
        @ApiModelProperty(value = "执行员工/伙伴数量")
        private Integer executorCount;
        @ApiModelProperty(value = "发送账号")
        private String sendAccount;
        @ApiModelProperty(value = "推送链接")
        private String sendLink;
        @ApiModelProperty("推送附件")
        private List<Map<String, Object>> sendAttachment;
        @ApiModelProperty("推送内容")
        private String sendContent;
    }

    @Data
    public static class MaterialInfo implements Serializable{
        @ApiModelProperty(value = "素材Id")
        private String objectId;
        @ApiModelProperty(value = "内容类型")
        private Integer contentType;
        @ApiModelProperty("素材封面TAPath")
        private String coverPath;
        @ApiModelProperty("素材封面url")
        private String coverUrl;
        @ApiModelProperty("物料类型")
        private Integer objectType;
        public MaterialInfo(){

        }

        public MaterialInfo(String objectId, Integer contentType) {
            this.objectId = objectId;
            this.contentType = contentType;
        }
    }

    @Data
    @ToString(callSuper = true)
    public static class MarketingActivityNoticeSendVO implements Serializable {
        @ApiModelProperty("通知标题")
        private String title;
        @ApiModelProperty("推广物料")
        private Integer contentType;
        @ApiModelProperty("推广内容")
        private String content;
        @ApiModelProperty("宣传语")
        private String description;
        @ApiModelProperty("发送类型： 1：立即发送 2：定时发送 ")
        private Integer sendType;
        @ApiModelProperty("发送范围")
        private NoticeVisibilityArg noticeVisibilityArg;
        @ApiModelProperty("定时时间")
        private Long timingDate;
        @ApiModelProperty("推广开始时间")
        private Long startTime;
        @ApiModelProperty("推广结束时间")
        private Long endTime;
        @ApiModelProperty("封面TAPath")
        private String coverPath;
        @ApiModelProperty(value = "通讯录类型")
        private int addressBookType;
        @ApiModelProperty(value = "图片或海报多张素材")
        private List<MaterialInfo> materialInfoList;

        @ApiModelProperty(value = "通知类型， see com.facishare.marketing.common.enums.SendNoticeTypeEnum")
        private Integer type;
    }

    /**
     * 伙伴营销通知发送VO
     */
    @Data
    @ToString(callSuper = true)
    public static class MarketingActivityPartnerNoticeSendVO implements Serializable{
        @ApiModelProperty("通知标题")
        private String title;
        @ApiModelProperty("推广物料 通知内容类型")
        private Integer contentType;
        @ApiModelProperty("推广内容 物料id")
        private String content;
        @ApiModelProperty("宣传语")
        private String description;
        @ApiModelProperty("发送类型： 1：立即发送 2：定时发送 ")
        private Integer sendType;
        @ApiModelProperty("发送范围")
        private PartnerNoticeVisibilityArg partnerNoticeVisibilityArg;
        @ApiModelProperty("定时时间")
        private Long timingDate;
        @ApiModelProperty("推广开始时间")
        private Long startTime;
        @ApiModelProperty("推广结束时间")
        private Long endTime;
        @ApiModelProperty("封面TAPath")
        private String coverPath;
        /** 会议里微页面id **/
        @ApiModelProperty(value = "会议里微页面id")
        private String activityDetailSiteId;
        @ApiModelProperty(value = "图片或海报多张素材")
        private List<MaterialInfo> materialInfoList;
        @ApiModelProperty(value = "海报图片推广展示,0关闭,1打开")
        private Integer staffInfoShow;
    }

    @Data
    @ToString
    public static class NoticeVisibilityArg implements Serializable {
        /** 部门列表 **/
        @ApiModelProperty("部门列表")
        private List<Integer> departmentIds;
        /** 用户id列表 **/
        @ApiModelProperty("用户id列表 ")
        private List<Integer> userIds;
        /** 用户id列表 **/
        @ApiModelProperty("用户id列表 ")
        private List<String> outUserIds;
        /**角色**/
        @ApiModelProperty("用户角色列表 ")
        private List<RoleItem> roles;
        /**用户组**/
        @ApiModelProperty("用户组列表")
        private List<UserGroup> userGroups;

        @ApiModelProperty(value = "会员营销筛选条件")
        private List<Filter> filters;
    }

    @Data
    @ToString
    public static class PartnerNoticeVisibilityArg implements Serializable {
        /** 企业名称列表 **/
        private List<String> eaList;
        /** 企业群组id列表 **/
        private List<String> tenantGroupIdList;
    }

    @Data
    @ToString
    public static class RoleItem implements Serializable{
        @ApiModelProperty("用户角色id")
        private String roleCode;
        @ApiModelProperty("用户角色名称")
        private String roleName;
    }

    @Data
    @ToString
    public static class UserGroup implements Serializable{
        @ApiModelProperty("用户组id")
        private String userGroupId;
        @ApiModelProperty("用户组名称")
        private String userGroupName;
    }


    @Data
    @ToString(callSuper = true)
    public static class MarketingActivityGroupSenderVO implements Serializable {
        @ApiModelProperty(value = "事件类型  1保存为草稿  2 发送 ", required = true)
        private Integer eventType;
        @ApiModelProperty(value = "群发短信id  需要修改的时候传")
        private String smsSendId;
        /** 用已有模板发送需要填写 */
        @ApiModelProperty(value = "模板id", required = true)
        private String templateId;
        @ApiModelProperty(value = "短信发送时机  1->立即发送  2 ->延迟发送 ", required = true)
        private Integer type;
        @ApiModelProperty(value = "定时发送时间  若type为2时，传入", required = false)
        private Long fixedTime;
        @ApiModelProperty(value = "模板名称")
        private String templateName;
        @ApiModelProperty(value = "模板内容")
        private String templateContent;
        @ApiModelProperty(value = "Excel的taPath", required = true)
        private String taPath;
        @ApiModelProperty(value = "Excel的taPath名称", required = true)
        private String taPathName;
        /** {@link SmsGroupTypeEnum} */
        @ApiModelProperty(value = " 1导入excel表格，2按选择人群, 3选择会议参会人  5手机号和参数 6直播报名人 8 活动营销")
        private Integer sendRange;
        @ApiModelProperty("人群id  只有sendRange=2才有效")
        private List<String> marketingUserGroupIds;
        @ApiModelProperty(value = "电话号码列表")
        private List<PhoneContentResult> phones;
        @ApiModelProperty(value = "活动成员列表id")
        private List<String> campaignIds;
        @Deprecated
        @ApiModelProperty(value = "会议邀约人id列表")
        private List<String> conferenceInviteIds;
        //@ApiModelProperty(value = "直播报名人id列表")
        //private List<String> liveCustomizeFormDataUserIds;  //直播报名
        /** {@link SmsSceneTypeEnum} */
        @ApiModelProperty(value = "短信的发送来源场景")
        private Integer sceneType;
        @ApiModelProperty(value = "过滤掉N天内发送过的用户， 为空表示不过滤")
        private Integer filterNDaySentUser;
        /** 使用该方法作埋点 {@see com.facishare.marketing.provider.manager.sms.mw.MwSendManager#doTrackSmsLink(java.lang.String, java.util.List)} */
//        @Deprecated
        @ApiModelProperty(value = "需要拼接营销活动id的短链map")
        private Map<String, String> shortUrlMap;
        @ApiModelProperty(value = "忽略错误手机号,针对excel导入的方式")
        private boolean ignoreErrorPhone;
        @ApiModelProperty(value = "变量列表")
        private List<SmsVarArg> smsVarArgs;
        @ApiModelProperty(value = "是否过滤当前活动中已成功收到此短信内容的用户")
        private boolean filterReceived;
    }

    @Data
    @ToString(callSuper = true)
    public static class WeChatServiceMarketingActivityVO extends BaseVo implements Serializable {
        @ApiModelProperty(value = "标题")
        private String title;
        @ApiModelProperty(value = " 消息类型 2：文本， 3:图片 4:图文", example = "1")
        private Integer msgType;
        @ApiModelProperty("消息内容 msgType==2 表示文本内容 msgType==3 表示图片TApath")
        private String content;
        @ApiModelProperty("发送类型 1：立即发送 2：定时发送")
        private Integer type;
        @ApiModelProperty("定时推送时间, 仅type=2时有值")
        private Long fixedTime;
        @ApiModelProperty(value = "过滤掉N天内发送过的用户， 为空表示不过滤")
        private Integer filterNDaySentUser;
        @ApiModelProperty("客户ID列表")
        private List<String> customerIds;
        @ApiModelProperty("发送范围  0-全部 1-筛选条件 2-人群")
        private Integer sendRange;
        @ApiModelProperty("人群id  只有sendRange=2才有效")
        private List<String> marketingUserGroupIds;
        @ApiModelProperty("筛选条件  只有sendRange=1才有效")
        private List<Map<String, Object>> filters = Lists.newArrayList();
        @ApiModelProperty("标签列表")
        private List<TagName> tagIdList;
        @ApiModelProperty("需要发送的客户数量")
        private Integer customerCount;
        @ApiModelProperty("实际发送人数")
        private Integer actualCompletedCount;
        @ApiModelProperty("需要发送的人数")
        private Integer needSendCount;
        @ApiModelProperty("发送失败人数")
        private Integer faillSenderCount;
        @ApiModelProperty(value = "素材列表")
        private List<MaterialInfo> materialInfos;
        @ApiModelProperty(value = "数据迁移用")
        private Integer msgId;
        @ApiModelProperty(value = "是否旧数据迁移，数据迁移用")
        private Boolean migration =false;
        @ApiModelProperty(value = "图文消息标题")
        private String graphicMessageTitle;
        @ApiModelProperty(value = "图文消息图片")
        private String graphicMessagePic;

    }

    @Data
    public static class BaseVo implements Serializable {
        /**
         * 微联服务号ID
         */
        protected String appId;
        /**
         * 当前操作人的员工ID
         */
        protected Integer userId;
        /**
         * 当前操作人的公司ID，即EI
         */
        protected Long corpId;
        /**
         * 当前操作人的公司账号，即EA
         */
        protected String enterpriseAccount;
        /**
         * 当前操作人的客户端信息
         */
        protected String client;
        /**
         * 区分终端请求，用于排重
         **/
        protected String postId;

        public String getFsUserId() {
            return Joiner.on(".").join("E", enterpriseAccount, userId);
        }
    }

    @Data
    public static class WhatsAppMarketingActivityArg implements Serializable {

        @ApiModelProperty(value = "任务的名称")
        private String name;

        @ApiModelProperty(value = "发送类型 SCHEDULED:定时发送 IMMEDIATELY:立即发送")
        private String sendType;

        @ApiModelProperty(value = "发送的目标 MARKETING_USER_GROUP:目标人群 EXCEL:excel表格 PHONE_LIST:号码列表")
        private String sendTarget;

        @ApiModelProperty(value = "发送的目标id sendTarget = MARKETING_USER_GROUP 时为目标人群id sendTarget = PHONE_LIST 时为号码列表")
        private List<String> targetIdList;

        // taPath就不要放targetIdList了，因为fileName通过taPath大概率获取不到文件名,所以直接放外面
        @ApiModelProperty(value = "上传的taPath")
        private String taPath;

        @ApiModelProperty(value = "上传的文件名")
        private String fileName;

        @ApiModelProperty(value = "定时发送的时间")
        private Date sendTime;

        @ApiModelProperty(value = "模板名称")
        private String templateName;

        @ApiModelProperty(value = "模板语言")
        private String templateLanguage;

        @ApiModelProperty(value = "发送的whatsapp号码")
        private String businessPhone;

        @ApiModelProperty(value = "过滤掉N天内发送过的用户， 为空表示不过滤")
        private Integer filterNDaySentUser;

        @ApiModelProperty("模板对应的参数列表 顺序要和模板一致")
        private List<String> parameterList;
    }
}
