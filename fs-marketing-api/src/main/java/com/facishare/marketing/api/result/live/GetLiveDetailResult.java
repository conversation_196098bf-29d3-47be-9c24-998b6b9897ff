package com.facishare.marketing.api.result.live;

import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by z<PERSON><PERSON> on 2020/3/30.
 */
@Data
public class GetLiveDetailResult implements Serializable{
    //主键
    private String id;

    //市场活动id
    private String marketingEventId;

    //直播标题
    private String title;

    //直播详情
    private String desc;

    //直播开始时间
    private Long startTime;

    //直播结束时间
    private Long endTime;

    //讲师名称
    private String lectureUserName;

    //讲师直播密码
    private String lecturePassword;

    //标签
    private TagNameList tagNames;

    //直播状态 1: 直播中 2:预告 3:已结束
    private Integer status;

    //直播封面地址
    private String coverTaPath;

    //是否启动直播聊天 0:不开启  1：开启
    private Integer chatOn;

    //是否生成回放 0：不生成  1：生成
    private Integer autoRecord;

    // 直播最大运行观看观看次数
    private Integer maxLiveCount;

    //直播平台 1:纷享合作平台  2：其他
    private Integer livePlatform;

    //其他直播平台直播地址
    private String otherPlatformLiveUrl;

    //讲师地址
    private String lectureUrl;

    //直播地址
    private String viewUrl;

    //报名表单微页面id
    private String formHexagonId;

    private boolean hasLecturePassword;

    //微吼直播内部id
    private Integer vhallId;

    //会员id
    private String memberId;

    //会员手机号
    private String memberPhone;

    //会员姓名
    private String memberName;

    //是否可以编辑 false：不能编辑 true:可以编辑
    private boolean editable;

    private boolean showActivityList;

    private String xiaoetongLiveId;

    private String xiaoetongUrl;

    private String jumpObjectId;    //跳转报名物料id
    private Integer jumpObjectType; //跳转报名物料类型
    private String jumpUrl;         //跳转报名地址

    //视频号直播报名页站点
    private String signUpSiteId;

    //视频号直播中转页站点
    private String transitSiteId;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicMiniAppCutUrl;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicH5CutUrl;

    @ApiModelProperty("普通推广微页面分享与封面裁剪图url")
    private String sharePicOrdinaryCutUrl;

    @ApiModelProperty("原图封面url")
    private String sharePicOrdinaryUrl;

    @ApiModelProperty("是否为子活动 1是 0否 当前只有目睹场所活动需要自动创建")
    private Integer subEvent;

    @ApiModelProperty("关联的账号id,目前只有视频号需要关联")
    private String associatedAccountId;

}
