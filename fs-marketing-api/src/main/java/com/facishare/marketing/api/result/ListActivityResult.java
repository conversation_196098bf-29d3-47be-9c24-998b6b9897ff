package com.facishare.marketing.api.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ListActivityResult implements Serializable{
    private String marketingEventId;
    private String objectId;
    private String title;
    private String hexagonSiteId;
    private Long startTime;
    private Long endTime;
    private String url;
    private String thumbnailUrl;
    private String eventType;
    private List<Map<String,Object>> cover;
}
