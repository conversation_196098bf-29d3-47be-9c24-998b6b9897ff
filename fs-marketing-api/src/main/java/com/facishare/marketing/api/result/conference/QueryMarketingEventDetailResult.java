package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by ranluch on 2019/7/17.
 */
@Data
public class QueryMarketingEventDetailResult implements Serializable {
    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("会议标题")
    private String title;

    @ApiModelProperty("会议开始时间")
    private Long startTime;

    @ApiModelProperty("会议结束时间")
    private Long endTime;

    @ApiModelProperty("会议类型")
    private String marketingEventType;

    @ApiModelProperty("会议地点")
    private String location;
}
