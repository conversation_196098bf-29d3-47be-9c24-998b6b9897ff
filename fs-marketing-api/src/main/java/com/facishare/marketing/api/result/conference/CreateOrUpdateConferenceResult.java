package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2020/10/30.
 */
@Data
public class CreateOrUpdateConferenceResult implements Serializable{
    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("会议id")
    private String conferenceId;


}
