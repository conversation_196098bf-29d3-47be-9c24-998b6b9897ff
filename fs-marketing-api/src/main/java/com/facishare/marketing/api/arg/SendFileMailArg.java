package com.facishare.marketing.api.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.common.enums.mail.MailApiUserTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class SendFileMailArg extends QYWXBaseArg {

    private String ea;

    private String fileId;
    /**
     * 发送账号类型
     * {@link MailApiUserTypeEnum}
     */
    private Integer accountType;

    // 发送人id
    private List<String> senderIds;

    // 回复人id
    private List<String> replyIds;

    // 邮件标题
    private String title;

    // 邮件内容
    private String content;

    // 发送人员列表
    private List<String> mailList;

    private String epxId;
    private String code;
//    //下面是埋点相关的
//    private String userMarketingId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(fileId) || CollectionUtils.isEmpty(senderIds)||CollectionUtils.isEmpty(replyIds)||StringUtils.isBlank(title) || StringUtils.isBlank(content)|| CollectionUtils.isEmpty(mailList);
    }
}
