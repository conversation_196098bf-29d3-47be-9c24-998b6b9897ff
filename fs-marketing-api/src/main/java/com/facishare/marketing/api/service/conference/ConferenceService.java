package com.facishare.marketing.api.service.conference;

import com.facishare.marketing.api.GetCampaignDataArg;
import com.facishare.marketing.api.GetSignInDetailArg;
import com.facishare.marketing.api.GetSignInSuccessSettingArg;
import com.facishare.marketing.api.UpdateSignInSuccessSettingArg;
import com.facishare.marketing.api.arg.conference.CreateSignInQrCodeArg;
import com.facishare.marketing.api.arg.conference.QueryInviteCountInfoArg;
import com.facishare.marketing.api.result.BuildCrmObjectByEnrollDataResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.HexagonQrCodeResult;
import com.facishare.marketing.api.result.ImportUserDataResult;
import com.facishare.marketing.api.result.conference.*;
import com.facishare.marketing.api.result.ticket.ConsumeTicketResult;
import com.facishare.marketing.api.result.ticket.QueryTicketListResult;
import com.facishare.marketing.api.vo.conference.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.typehandlers.value.TagName;

import java.util.List;
import java.util.Map;

/**
 * Created  By zhoux 2019/07/16
 **/
public interface ConferenceService {
    Result<AddConferenceResult> addConference(AddConferenceVO vo);

    Result<CreateOrUpdateConferenceResult> create(CreateOrUpdateConferenceVO vo);

    Result<CreateOrUpdateConferenceResult> update(CreateOrUpdateConferenceVO vo);

    /**
     * 分页查询会议列表
     * @param vo
     * @return
     */
    Result<PageResult<QueryConferenceListResult>> queryConferenceList(QueryConferenceListVO vo);

    Result<PageResult<QueryConferenceListResult>> queryConferenceAppList(QueryConferenceListVO vo);

    Result updateConferenceDetail(UpdateConferenceDetailVO vo);

    Result updateConferenceStatus(UpdateConferenceStatusVO vo);

    Result<QueryConferenceDetailResult> queryConferenceDetail(QueryConferenceDetailVO vo);

    Result updateConferenceEnrollSetting(ConferenceEnrollSettingVO vo);

    /**
     * 分页查询参会人员信息
     * @param vo
     * @return
     */
    Result<PageResult<QueryConferenceParticipantsResult>> queryConferenceParticipants(QueryConferenceParticipantsVO vo);

    /**
     * 手动设置参会人员的签到状态
     * @param vo
     * @return
     */
    Result<Boolean> changeConferenceParticipantsSignStatus(ChangeConferenceParticipantsSignStatusVO vo);

    /**
     * 手动设置参会人员审核状态
     * @param vo
     * @return
     */
    Result<Boolean> changeConferenceParticipantsReviewStatus(ChangeConferenceParticipantsReviewStatusVO vo);

    /**
     * 手动保存参会人员到CRM
     * @param vo
     * @return
     */
    Result<Boolean> saveConferenceParticipantsToCrm(SaveConferenceParticipantsToCrmVO vo);

    Result invitationSetting(InvitationSettingVO vo);

    Result updateInvitationStatus(UpdateInvitationStatusVO vo);

    Result<PageResult<QueryInvitationResult>> queryInvitationList(QueryInvitationListVO vo);

    Result<QueryInvitationResult> queryInvitationInfo(QueryInvitationVO vo);

    Result<GetConferenceStatisticDataResult> getConferenceStatisticData(GetConferenceStatisticDataVO vo);

    Result notificationSettings(NotificationSettingsVO vo);

    Result<QueryNotificationSettingsResult> queryNotificationSettings(QueryNotificationSettingsVO vo);

    Result<QueryConferenceQrCodeResult> queryConferenceQrCode(QueryConferenceQrCodeVO vo);

    Result<QueryConferenceFormQrCodeResult> queryConferenceFormQrCode(QueryConferenceFormQrCodeVO vo);

    Result<QueryConferenceQrCodeResult> resetConferenceQrCode(ResetConferenceQrCodeVO vo);

    Result<GetSignInQrUrlResult> getSignInQrUrl(GetSignInQrUrlVO vo);

    Result<List<GetTemplateDataResult>> getTemplateData();

    Result<CheckMarketingEventResult> checkMarketingEvent(String marketingEventId, String ea, Integer fsUserId);

    Result<QueryMarketingEventDetailResult> queryMarketingEventDetail(QueryMarketingEventDetailVO vo);

    Result<ExportEnrollsDataResult> exportConferenceParticipants(QueryConferenceParticipantsVO vo);

    Result<GetEnrollImportTemplateResult> getEnrollImportTemplate(String conferenceId, Integer importType);

    Result<ImportUserDataResult> importEnrollData(ImportUserDataVO vo);

    Result<ImportUserDataResult> importInviteData(ImportUserDataVO vo);

    Result<ExportEnrollsDataResult> exportInviteData(QueryInviteParticipantVO vo);

    /**
     * 查询邀约参会人员
     * @param vo
     * @return
     */
    Result<PageResult<QueryInviteParticipantResult>> queryInviteParticipants(QueryInviteParticipantVO vo);

    /**
     * 发送邀约企信通知
     * @param arg
     * @return
     */
    Result<Void> inviteParticipants(InviteParticipantVO arg);

    /**
     * 查选邀约列表
     * @param ea
     * @param userId
     * @param conferenceId
     * @return
     */
    Result<List<MyInvitation>> queryMyInvite(String ea, Integer userId, String conferenceId, Integer status, String nameKey);

    /**
     * 根据人员查询邀约列表
     * @param vo
     * @return
     */
    Result<PageResult<QueryInviteListByUserResult>> queryInviteListByUser(QueryInviteListByUserVO vo);

    /**
     * 查询待审核人员列表
     * @param ea
     * @param userId
     * @return
     */
    Result<List<QueryEnrollReviewResult>> queryEnrollReviewList(String ea, Integer userId, String conferenceId);

    /**
     * 查询待审核人员详情
     * @param ea
     * @param userId
     * @param campaignId
     * @return
     */
    Result<QueryEnrollReviewDetailResult> queryEnrollReviewDetail(String ea, Integer userId, String campaignId);

    /**
     * 签到验证参会人报名手机号
     * @param vo
     * @return
     */
    Result<ConsumeTicketResult> verifyEnrollPhone(VerifyEnrollPhoneVO vo);

    /**
     * 查询邀约统计信息
     * @param vo
     * @return
     */
    Result<QueryInviteCountInfoResult> queryInviteCountInfo(QueryInviteCountInfoArg vo);

    /**
     * 更新邀约状态
     * @param inviteIds
     * @param status
     * @return
     */
    Result<Void> updateInviteStatus(List<String> inviteIds, Integer status);

    /**
     * 删除邀约人
     * @param inviteId
     * @return
     */
    Result<Void> deleteInvite(String inviteId);

    /**
     * 更新邀约人员信息
     * @param vo
     * @return
     */
    Result<Void> updateInviteInfo(UpdateInviteInfoVO vo);

    /**
     * 更新用户分组信息
     * @param vo
     * @return
     */
    Result updateGroupInfo(UpdateGroupInfoVO vo);

    /**
     * 删除参会人员
     * @param vo
     * @return
     */
    Result<Void> deleteParticipants(DeleteParticipantVO vo);

    /**
     * 通过二维码获取签到情况（企业微信小程序）
     */
    Result<ActivityAndSignOrReportResult> activityStatusSignOrReport(String activityId, String uid,String tagId);

    /**
     * 会议报名（企业微信小程序）
     */
    Result activitySign(String activityId, String uid, String phone, String tagId, Boolean delaySingIn,String email);

    /**
     * 审查会议验票人员信息
     * @param vo
     * @return
     */
    Result<Void> addTicketManger(AddTicketMangerVO vo);

    /**
     * 查询验票信息
     * @param ea
     * @param conferenceId
     * @return
     */
    Result<QueryTicketManagerResult> queryTicketManager(String ea, String conferenceId);

    /**
     * 查询当前员工是否可以开始为该会议验票
     * @param ea
     * @param fsUserId
     * @param conferenceId
     * @return
     */
    Result<Boolean> queryTicketCheckPoint(String ea, Integer fsUserId, String conferenceId);


    Result<GetAllEnrollDataByCampaignResult> getAllEnrollDataByCampaign(String ea, String campaignId);

    Result<List<QueryConferenceUserGroupResult>> queryConferenceUserGroup(String ea, String conferenceId);

    Result<AddCampaignMembersObjResult> addCampaignMembersObj(AddCampaignMembersObjVO vo);

    Result<List<FieldInfo>> getCampaignMembersObjField(String ea);

    Result<PersonalSettingDetailsResult> personalSettingDetails(String ea, Integer fsUserId, String conferenceId);

    /**
     * 查询指定用户能看到的统计数据
     * @param vo
     * @return
     */
    Result<QueryOwerViewConferenceStatisticsDataResult> queryOwerViewConferenceStatisticsData(QueryOwerViewConferenceStatisticsDataVO vo);

    /**
     * 查询指定用户能看到的报名数据
     * @param vo
     * @return
     */
    Result<PageResult<QueryOwerViewConferenceEnrollResult>> queryOwerViewConferenceEnrollData(QueryOwerViewConferenceEnrollDataVO vo);


    Result<QueryCampaignWxStatisticsResult> queryCampaignWxStatistics(String ea, Integer fsUserId, String marketingEventId);

    Result<List<QueryAllCampaignDataResult>> queryAllCampaignData(QueryAllCampaignDataVO vo);

    Result<BuildCrmObjectByEnrollDataResult> buildCrmObjectByCampaignId(String campaignId, String ea, Integer fsUserId);

    Result bindCrmObjectByCampaignId(BindCrmObjectByCampaignIdVO vo);

    Result<GetConferenceTimeStatusResult> getConferenceTimeStatus(GetConferenceTimeStatusVO vo);

    Result signInByEnrollField(SignInByEnrollFieldVO vo);

    Result<ImportUserDataResult> importSignInData(ImportSignInDataVO vo);

    Result<GetSignInSettingResult> getSignInSetting(GetSignInSettingVO vo);

    Result updateSignInSetting(UpdateSignInSettingVO vo);

    Result<GetInvitationCommonSettingResult> getInvitationCommonSetting(GetInvitationCommonSettingVO vo);

    Result upsertInvitationCommonSetting(UpsertInvitationCommonSettingVO vo);

    Result addInvitationUserByCrmObj(AddInvitationUserByCrmObjVO vo);

    /**
     * 异步不批量添加邀请人员
     * @param vo
     * @return
     */
    Result asynBatchAddInvitationUser(AsynBatchAddInvitationUserByCrmObjVO vo);

    Result changeInvitationStatus(ChangeInvitationStatusVO vo);

    Result updateConferenceContent(UpdateConferenceContentVO vo);

    Result<GetMarketingActivityChannelByIdResult> getMarketingActivityChannelById(String marketingActivityId);

    Result sendConferenceEnrollNoticeTask();

    Result<String> getConferenceIdByMarketingEventId(String ea, String marketingEventId);

    Result updateSignInSuccessSetting(String ea, UpdateSignInSuccessSettingArg arg);

    Result getSignInSuccessSetting(GetSignInSuccessSettingArg arg);

    Result getObjectCustomFields(String ea);

    Result getCampaignData(GetCampaignDataArg arg);


    Result<QueryCampaignExternalContactStatisticsResult> queryCampaignExternalContactStatistics(String ea, Integer fsUserId, String marketingEventId);

    Result<List<TagName>> getConferenceTag(String conferenceId);

    //查询会议签到情况
    Result<GetSignInDetailResult> getSignInDetail(GetSignInDetailArg arg);

    //查询会议详情的简单接口--用于签到场景
    Result<GetSimpleConferenceDetail> getSimpleDetail(String conferenceId);

    Result<HexagonQrCodeResult> createSignInQrCode(CreateSignInQrCodeArg arg);

    Result<List<QueryTicketListResult>> queryTicketCampaignList(VerifyEnrollPhoneVO vo);

    Result<List<Map<String, Object>>> queryCustomizeTicketCampaignData(VerifyEnrollPhoneVO vo);
}
