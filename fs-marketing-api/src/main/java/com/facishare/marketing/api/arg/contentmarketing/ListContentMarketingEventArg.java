package com.facishare.marketing.api.arg.contentmarketing;

import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class ListContentMarketingEventArg extends PageArg {
    @ApiModelProperty(value = "市场活动状态")
    private String bizStatus;
    @ApiModelProperty(value = "市场活动名称")
    private String name;
    @ApiModelProperty(value = "活动类型 content_marketing 内容营销")
    private String eventType;
    @ApiModelProperty("查询指定父级市场活动id")
    private String parentMarketingEventId;
    @ApiModelProperty("对象选择器")
    private FilterData filterData;
    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;
}
