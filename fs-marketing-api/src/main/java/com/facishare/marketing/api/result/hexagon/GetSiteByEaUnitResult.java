package com.facishare.marketing.api.result.hexagon;

import com.facishare.marketing.api.result.MaterialTagResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GetSiteByEaUnitResult implements Serializable {
    @ApiModelProperty("站点ID")
    private String id;

    @ApiModelProperty("站点名称")
    private String name;

    @ApiModelProperty("外部展示名称")
    private String outDisplayName;

    @ApiModelProperty("站点封面")
    private String coverUrl;

    @ApiModelProperty("站点封面APath")
    private String coverAPath;

    @ApiModelProperty("关联市场活动数")
    private Integer marketingEventCount;
    @ApiModelProperty("访问次数")
    private Integer accessCount;
    @ApiModelProperty("线索数")
    private Integer leadCount;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("创建人")
    private String creator;
    @ApiModelProperty("创建人Id")
    private Integer creatorBy;
    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("更新人")
    private String updater;
    @ApiModelProperty("更新时间")
    private Long updateTime;

    @ApiModelProperty("表单ID")
    private String formId;
    @ApiModelProperty("表单是否映射")
    private Boolean hadCrmMapping;
    @ApiModelProperty("表单是否映射")
    private Integer formUsage;
    @ApiModelProperty("分享标题")
    private String shareTitle;
    @ApiModelProperty("分享描述")
    private String shareDesc;

    // 营销活动（推广内容）
    private String marketingActivityTitle;

    // 营销活动id
    private String marketingActivityId;

    private Integer marketingActivityCount = 0;

    @ApiModelProperty("是否为会员信息更新站点")
    private Boolean updateMemberInfoSite;

    @ApiModelProperty("微页面分组名")
    private String groupName;

    @ApiModelProperty("是否市场活动专属微页面")
    private boolean isSystemSite;

    @ApiModelProperty("置顶标识")
    private boolean top;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String shareImgUrl;
    @ApiModelProperty("分享朋友圈封面apath")
    private String shareImgAPath;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicMiniAppCutUrl;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicH5CutUrl;

    @ApiModelProperty("普通推广微页面分享与封面裁剪图url")
    private String sharePicOrdinaryCutUrl;

    @ApiModelProperty("原图封面url")
    private String sharePicOrdinaryUrl;

    @ApiModelProperty("平均访问时长")
    private String actionDurationTimeAvg;

    //鸿曦说访问总数先改成以营销动态记录表为准为准
    @ApiModelProperty("物料访问总数")
    private Integer objectLookUpCount;

    @ApiModelProperty("封面图大小")
    private Long coverSize;
/*
    @ApiModelProperty("微页面权限")
    private Integer authorize;   //HexagonAuthorizeTypeEnum

    @ApiModelProperty("共享员工姓名")
    private List<String> sharedEmployees;
*/
    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;

    @ApiModelProperty("文件类型文件微页面专用")
    private String fileType;

    @ApiModelProperty("文件生成微页面转码状态") //com.facishare.marketing.common.enums.FileToHexagonStatusEnum
    private Integer fileToHexagonStatus;

    @ApiModelProperty("文件生成微页面转码失败原因，当fileToHexagonStatus=2才有值")
    private String fileToHexagonFailReason;

    @ApiModelProperty("是否关联CTA组件")
    private Boolean hasCta;

    @ApiModelProperty("是否被CTA组件使用")
    private Boolean usedByCta;
}
