package com.facishare.marketing.api.arg;

import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/12.
 */
@Data
public class ListBriefMarketingEventsArg implements Serializable {
    @ApiModelProperty(value = "开始时间")
    private Long begin;
    @ApiModelProperty(value = "结束时间")
    private Long end;
    @ApiModelProperty(value = "市场活动名称")
    private String marketingEventName;
    @ApiModelProperty(value = "场景Id")
    private String searchTemplateId;
    @ApiModelProperty(value = "场景类型")
    private String searchTemplateType;
    @ApiModelProperty(value = "活动类型")
    private String eventType;
    @ApiModelProperty(value = "市场活动状态")
    private String bizStatus;
    @ApiModelProperty(value = "市场活动名称")
    private String name;
    @ApiModelProperty(value = "限制拉取数量")
    private Integer queryLimitCount;
    @ApiModelProperty(value = "orderBy")
    private List<OrderBy> orderByList = Lists.newArrayList();
    @ApiModelProperty("活动类型列表")
    private List<String> eventTypeList;
    @ApiModelProperty("不包含的活动类型")
    private List<String> nlikeEventTypeList;
    @ApiModelProperty("查询指定市场活动id")
    private List<String> marketingEventIds;
    @ApiModelProperty("查询指定父级市场活动id")
    private String parentMarketingEventId;
    @ApiModelProperty("市场活动选择器")
    private FilterData filterData;
    @ApiModelProperty("是否过滤子数据")
    private Boolean filterSonData;

    @Data
    public static class OrderBy implements Serializable{
        private String field;
        private Boolean ascending;
        public OrderBy(String field,Boolean ascending){
            this.field = field;
            this.ascending = ascending;
        }
    }
}
