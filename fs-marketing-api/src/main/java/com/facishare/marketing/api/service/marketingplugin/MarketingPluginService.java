package com.facishare.marketing.api.service.marketingplugin;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.plugin.BatchGetPluginStatusArg;
import com.facishare.marketing.api.arg.plugin.GetPluginSettingArg;
import com.facishare.marketing.api.arg.plugin.UpdatePluginSettingArg;
import com.facishare.marketing.api.result.PluginStatusResult;
import com.facishare.marketing.api.result.QueryScenaryResult;
import com.facishare.marketing.api.result.marketingplugin.CheckLicenseListResult;
import com.facishare.marketing.api.result.marketingplugin.MarketingPluginInfoResult;
import com.facishare.marketing.api.vo.marketingplugin.MarketingPluginConfigVo;
import com.facishare.marketing.common.result.Result;

import java.util.List;

public interface MarketingPluginService {

    /**
     * @description:根据企业ea,插件类型查询插件信息
     * @author: ming<PERSON>ao
     * @date: 2021/9/7 16:31
     * @param ea: 企业ea
     * @param pluginType: 插件类型 1:溯源优惠券 2:社会化分销
     * @return: com.facishare.marketing.api.result.marketingplugin.MarketingPluginInfoResult
     */
    Result<MarketingPluginInfoResult>  queryMarketingPluginInfo(String ea,Integer pluginType);

    /**
     * @description:根据企业ea查询所有的插件
     * @author: mingqiao
     * @date: 2021/9/7 16:33
     * @param ea:
     * @return: java.util.List<com.facishare.marketing.api.result.marketingplugin.MarketingPluginInfoResult>
     */
    Result<List<MarketingPluginInfoResult>> queryMarketingPluginByEa(String ea);

    /**
     * @description:保存营销插件信息
     * @author: mingqiao
     * @date: 2021/9/7 16:38
     * @param vo:
     * @return: int
     */
    Result<Void> saveMarketingPluginConfig(MarketingPluginConfigVo vo);

    /**
     * @description:开启/关闭营销插件开关
     * @author: mingqiao
     * @date: 2021/9/7 19:38
     * @param ea:
     * @param pluginType:
     * @return: com.facishare.marketing.common.result.Result<java.lang.Void>
     */
    Result<Void> saveOrUpdateMarketingPlugin(String ea, Integer pluginType, Boolean status);

    Result<Boolean> queryMarketingPluginStatus(String ea, Integer pluginType);

    Result<Boolean> isOpenInvalidRebate(Integer tenantId, Integer userId);

    Result<List<QueryScenaryResult>> queryServiceKnowledgeScenaryList(String ea);

    Result insertOrUpdateServiceKnowledgeScenary(String ea, String id);

    Result<QueryScenaryResult> queryServiceKnowledgeScenarySetting(String ea);

    Result<Boolean> checkLicense(String ea, String moduleCode);

    Result<PluginStatusResult> batchGetMarketingPluginStaus(BatchGetPluginStatusArg arg);

    Result<CheckLicenseListResult> checkLicenseList(String ea, List<String> moduleCodes);

//    Result<Boolean> queryEnterCustomerServiceEaList(String ea);
}
