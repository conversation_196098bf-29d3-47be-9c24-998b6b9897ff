package com.facishare.marketing.api.arg.marketingactivity;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivityListArg extends QYWXBaseArg {

    @ApiModelProperty(value = "活动标题", required = false)
    private String title; // 非必填
    @ApiModelProperty(value = "分页大小", required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "分页页码", required = true)
    private Integer pageNum;
    @ApiModelProperty("对象选择器")
    private FilterData filterData;

    private String menuId;

}
