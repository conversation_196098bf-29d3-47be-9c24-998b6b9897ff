package com.facishare.marketing.api.vo.ai;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PromptCompletionsArg extends PromptCompletionsBaseArg {
    private String business;
    private Integer objectType;
    private String objectId;
    private String campaignName;//活动名称
    private String promotionSlogan;//宣传语
    private List<String> materialInfos;//多图
}
