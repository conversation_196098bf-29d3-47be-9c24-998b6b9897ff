package com.facishare.marketing.api.data.material;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.nio.channels.InterruptedByTimeoutException;
import java.util.Date;

@Data
public class HexagonSiteBriefData extends AbstractMaterialData implements Serializable {

    @ApiModelProperty("站点名称")
    private String name;

    @ApiModelProperty("站点封面")
    private String coverUrl;

    @ApiModelProperty("站点封面apath")
    private String coverApath;

    @ApiModelProperty("关联市场活动数")
    private Integer marketingEventCount;

    @ApiModelProperty("访问次数")
    private Integer accessCount;

    //鸿曦说访问总数先改成以营销动态记录表为准为准
    @ApiModelProperty("物料访问总数")
    private Integer objectLookUpCount;

    @ApiModelProperty("线索数")
    private Integer leadCount;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("更新人")
    private String updater;

    @ApiModelProperty("表单ID")
    private String formId;

    @ApiModelProperty("表单用途")
    private Integer formUsage;

    @ApiModelProperty("表单是否映射")
    private Boolean hadCrmMapping;

    @ApiModelProperty(value = "是否是专属")
    private boolean system;

    @ApiModelProperty("分享标题")
    private String shareTitle;
    @ApiModelProperty("分享描述")
    private String shareDesc;

    @ApiModelProperty("文件类型文件微页面专用")
    private String fileType;

    @ApiModelProperty("文件生成微页面转码状态") //com.facishare.marketing.common.enums.FileToHexagonStatusEnum
    private Integer fileToHexagonStatus;
    @ApiModelProperty("文件生成微页面转码失败原因，当fileToHexagonStatus=2才有值")
    private String fileToHexagonFailReason;
}
