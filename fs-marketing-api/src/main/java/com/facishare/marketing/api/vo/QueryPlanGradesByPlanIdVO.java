package com.facishare.marketing.api.vo;

import com.facishare.marketing.api.arg.BasePageArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by ranluch on 2019/4/4.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QueryPlanGradesByPlanIdVO extends BasePageArg {

    // 分销计划id
    private String planId;

    // 等级类型:1分销员等级，2招募等级
    private Integer type;

}
