package com.facishare.marketing.api.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-08-05
 * @IgnoreI18nFile
 */
@Data
public class UserMarketingActionResult implements Serializable {
    private String id;
    @ApiModelProperty(value = "New 营销用户id")
    private String userMarketingId;
    @ApiModelProperty(value = "用户名称")
    private String userName;
    @ApiModelProperty(value = "用户头像")
    private List<Map<String, String>> userAvatar;
    @ApiModelProperty(value = "微信头像url,仅给移动端提供")
    private String weChatAvatarUrl;
    @ApiModelProperty(value = "New 来源渠道:1表示小程序，2表示微信服务号，3表示h5")
    private Integer channelType;
    @ApiModelProperty(value = "New 动作类型 新类型： 1000014 -> 查看文章，1000010 -> 查看产品， 1000038 -> 查看会议， " + "1000064 -> 查看微页面, 1000111 -> 查看官网页面, 1000062 -> 查看表单, 1000017 -> 转发文章, 1000012 -> 转发产品, 1000039 -> 转发会议, 1000065 -> 转发微页面, " + "1000061 -> 提交表单, 1000066 -> 会议签到, 1001001 -> 关注公众号, 1001002 -> 取消关注公众号, 1001003 -> 点击公众号菜单, 1001004 -> 点击公众号链接, 1001005 -> 发送公众号消息, 1001006 -> 扫描公众号二维码" + "    旧类型（Deprecated）：1查看文章、2查看产品、3查看表单、4查看活动、5查看文件、3001转发文章、3002转发产品、3003转发活动、3004转发文件、" + "6001填写表单、6002报名活动、6003活动签到 || 10001: 关注服务号、10002取消关注服务号、10003点击菜单、10004点击链接、10005发送消息、10006扫描二维码" + "113点击短信下的链接")
    private Integer actionType;
    @ApiModelProperty(value = "New 4产品、6文章、13会议、16表单、26微页面, 当为公众号关注等事件时，该值为null")
    private Integer objectType;
    @ApiModelProperty(value = "New 对象id")
    private String objectId;
    @ApiModelProperty(value = "New 对象名称")
    private String objectName;
    @ApiModelProperty(value = "New 对象名称(pure)")
    private String pureObjectName;
    @ApiModelProperty(value = "New 浏览时间")
    private String actionDurationTime;
    @ApiModelProperty(value = "New 浏览进度")
    private String actionProportion;
    @ApiModelProperty(value = "New 对象链接:目前只有文章对象有值")
    private String objectLink;
    /** 场景类型 */
    @ApiModelProperty(value = "New 场景类型:1产品表单 2文章表单 3会议表单 5官网表单 6微页面表单 其他:通用表单")
    private Integer sceneType;
    /** 场景id */
    @ApiModelProperty(value = "New 场景id:如产品表单，则这里是产品id")
    private String sceneId;
    /** 场景名称 */
    @ApiModelProperty(value = "New 场景名称:产品名称")
    private String sceneName;
    @ApiModelProperty(value = "New 微信服务号id:只有channelType为2时有该值")
    private String wxAppId;
    @ApiModelProperty(value = "New 微联服务号id:只有channelType为2时有该值")
    private String wxServiceAccountId;
    @ApiModelProperty(value = "New 微联服务号名称:只有channelType为2时有该值")
    private String wxServiceAccountName;
    @ApiModelProperty(value = "New 动作次数")
    private Integer count;
    @ApiModelProperty(value = "New 创建时间")
    private Long createTime;

    @ApiModelProperty(value = "Deprecated:标题:只有channelType为2时有该值，如菜单文本、链接文本")
    private String title;
    @ApiModelProperty(value = "Deprecated:链接地址:只有channelType为2时有该值")
    private String link;
    @ApiModelProperty(value = "Deprecated:产品试用名称")
    private String productTryOutButtonValue;
    @ApiModelProperty(value = "动作名称")
    private String actionName;

    @ApiModelProperty(value = "累计观看时长")
    private Integer liveViewDuration;

//    @ApiModelProperty(value = "行为时长")
//    private Integer actionDurationTime;
//    @ApiModelProperty(value = "行为深度")
//    private Double actionProportion;
    @ApiModelProperty(value = "市场活动id")
    private String marketingEventId;
    @ApiModelProperty(value = "市场活动名称")
    private String marketingEventName;

    @ApiModelProperty(value = "营销场景")
    private String marketingScene;

    @ApiModelProperty(value = "互动渠道")
    private String spreadChannel;

    @ApiModelProperty(value = "渠道账号")
    private String channelAccountName;

    @ApiModelProperty(value = "渠道平台")
    private String channelAccountPlatform;

    @ApiModelProperty(value = "推广方式")
    private String spreadType;

    @ApiModelProperty(value = "客户端")
    private String client;

    @ApiModelProperty(value = "浏览器")
    private String browser;

    @ApiModelProperty(value = "IP归属地")
    private String ipQCellCore;

    @ApiModelProperty(value = "操作系统")
    private String operateSystem;

    @ApiModelProperty(value = "转发的营销用户id")
    private String fromUserMarketingId;

    @ApiModelProperty(value = "转发的营销用户名称")
    private String fromUserMarketingName;

    @ApiModelProperty(value = "转发的营销用户名称")
    private String dataSource;

    @ApiModelProperty(value = "推广员工id")
    private Integer spreadFsUid;

    @ApiModelProperty(value = "推广员工姓名")
    private String spreadFsUserName;

    @ApiModelProperty(value = "营销活动id")
    private String marketingActivityId;

    @ApiModelProperty(value = "cta id")
    private String ctaId;
    @ApiModelProperty(value = "cta名称")
    private String ctaName;
}
