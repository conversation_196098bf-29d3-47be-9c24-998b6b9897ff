package com.facishare.marketing.api.result.marketingEvent;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/8/15
 * @Desc 单个活动简单数据
 **/
@Data
public class MarketingEventSimpleResult implements Serializable {

    @ApiModelProperty(value = "市场活动ID")
    private String id;
    @ApiModelProperty(value = "唯一ID")
    private String uniqueId;
    @ApiModelProperty(value = "市场活动名称")
    private String name;
    @ApiModelProperty(value = "市场活动开始时间")
    private Long beginTime;
    @ApiModelProperty(value = "市场活动结束时间")
    private Long endTime;
    @ApiModelProperty(value = "市场活动类型")
    private String eventType;
    @ApiModelProperty(value = "市场活动位置")
    private String location;
    @ApiModelProperty(value = "直播链接")
    private String liveUrl;

    @ApiModelProperty(value = "推广次数")
    private Integer spreadCount = 0;
    @ApiModelProperty(value = "访问人次")
    private Integer pv = 0;
    @ApiModelProperty(value = "报名人次")
    private Integer enrollCount = 0;
    @ApiModelProperty(value = "签到人次")
    private Integer signInCount = 0;
    @ApiModelProperty(value = "观看人次")
    private Integer viewCount = 0;
    @ApiModelProperty(value = "回放人次")
    private Integer replayCount = 0;
    @ApiModelProperty(value = "互动人次")
    private Integer chatCount = 0;

}
