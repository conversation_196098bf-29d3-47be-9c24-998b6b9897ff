package com.facishare.marketing.api.arg.usermarketingaccount;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量修改标签参数
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
@Data
public class BatchUpdateObjectTagArg implements Serializable {

    /**
     * 对象类型的apiName，只能是系统预置的6个对象：
     * LeadsObj(销售线索)、AccountObj(客户)、ContactObj(联系人)、
     * MemberObj(会员)、WechatWorkExternalUserObj(企业微信客户)、WechatFanObj(微信用户对象)
     */
    private String objectApiName;

    /**
     * 具体的对象id
     */
    private String objectId;

    /**
     * 标签模型名称
     */
    private String modelName;

    /**
     * 标签组名称
     */
    private String tagGroupName;

    /**
     * 标签名称数组
     */
    private List<String> tagNames;

    /**
     * 打标签时，发现传入的某个标签不存在，是否新建
     * false: 不新建标签，查询其中有一个标签之前库中没有，直接返回。默认false
     * true: 子标签没有时，先新建一个，再执行后续的打标签操作
     */
    private Boolean addTag = false;

    public boolean isValid() {
        return StringUtils.isNotBlank(objectApiName) && StringUtils.isNotBlank(objectId) && StringUtils.isNotBlank(modelName) && StringUtils.isNotBlank(tagGroupName) ;
    }
}
