package com.facishare.marketing.api.service.marketingactivity;

import com.facishare.marketing.api.arg.InvalidMarketingActivityByIdsArg;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.*;
import com.facishare.marketing.api.result.marketingactivity.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/21.
 */
public interface MarketingActivityService {
    Result<PageResult<WxMarketingActivityResult>> listWxMarketingActivity(String ea, Integer fsUserId, String name, String appId, Integer pageNum, Integer pageSize);

    Result<PageResult<MarketingActivityResult>> listMarketingActivity(String ea, Integer fsUserId, ListMarketingActivityArg arg);

    Result<PageResult<MarketingActivityResult>> listMarketingActivityByMarketingUserGroupId(String ea, Integer fsUserId, ListMarketingActivityByMarketingUserGroupIdArg arg);

    Result<GetMarketingActivityResult> getMarketingActivity(String ea, Integer fsUserId, GetMarketingActivityArg getMarketingActivityArg);

    Result<AddMarketingActivityResult> addMarketingActivity(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg, boolean fromWeb);
    Result<AddMarketingActivityArg.MarketingActivityAuditData> getMarketingActivityAuditData(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg);

    Result<UpdateMarketingActivityResult> updateMarketingActivity(String ea, Integer fsUserId, UpdateMarketingActivityArg updateMarketingActivityArg);

    Result<Void> updateBaseInfo(UpdateMarketingActivityBaseInfoArg arg);

    Result<Integer> initCrmMarketingActivityByOldEa();

    Result<Integer> getEaInitCrmMarketingActivity(String ea);

    Result<Boolean> cancelSend(String ea, Integer fsUserId, String marketingActivityId);

    Result<Boolean> revokeSend(String ea, String marketingActivityId);

    Result<Boolean> updateMarketingActivityStatus(String ea, String marketingActivityId,String marketingActivityStatus);

    Result<Void> deleteMarketingActivity(String ea, Integer fsUserId, String marketingActivityId);

    Result<Void> invalidMarketingActivityByIds(InvalidMarketingActivityByIdsArg arg);

    Result<Void> spreadMarketingActivityToSpecialEmployeeAgain(String ea, Integer userId, String marketingActivityId);
    Result<Void> spreadPartnerActivityToSpecialEmployeeAgain(String ea, Integer userId, String marketingActivityId);
    Result<Void> spreadMomentActivityToSpecialEmployeeAgain(String ea, Integer userId, String marketingActivityId);

    Result<GetSendNotificationStatisticResult> getSendNotificationStatistic(String ea, Integer fsUserId, GetSendNotificationStatisticArg arg);

    Result<BatchGetSendNotificationStatisticResult> batchGetSendNotificationStatistic(String ea, Integer fsUserId, BatchGetSendNotificationStatisticArg arg);

    Result<String> getMarketingActivityDescription(String marketingActivityId, boolean partner);

    Result<AddMarketingActivityResult> updateMarketingActivityDetail(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg, boolean fromWeb);

    void wechatServiceNoticeSchedule();

    Result<Boolean> immediatelySend(String ea, String marketingActivityId);

    Result<MarketingActivityPreviewData> getPreviewData(String ea, Integer fsUserId, MarketingActivityPreviewArg arg);
}
