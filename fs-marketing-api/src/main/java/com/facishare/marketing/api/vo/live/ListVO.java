package com.facishare.marketing.api.vo.live;

import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhengh on 2020/3/23.
 */
@Data
public class ListVO implements Serializable{
    private List<Integer> statusList;     //直播状态的集合
    private String keyword;           //搜索直播标题关键字
    private String ea;                //企业账号
    private Integer fsUserId;         //员工账号
    private Integer ei;               //公司账号
    private Boolean isShowSpread;     //只展示可对外推广的直播
    private Integer pageSize;         //当前页码
    private Integer pageNum;          //每页显示数量
    private FilterData filterData;    //对象选择器

    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;

    private String menuId;
}
