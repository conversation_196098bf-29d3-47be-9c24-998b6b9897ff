package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.ListMarketingActivityInContentMarketingEventArg;
import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.arg.hexagon.HexagonStatisticArg;
import com.facishare.marketing.api.result.GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult;
import com.facishare.marketing.api.result.ListMarketingActivityInContentMarketingEventResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsResult;
import com.facishare.marketing.api.result.contentmaketing.ContentMarketingEventSimpleVO;
import com.facishare.marketing.api.result.contentmaketing.ContentMarketingEventVO;
import com.facishare.marketing.api.result.contentmaketing.MarketingContentVO;
import com.facishare.marketing.api.result.contentmaketing.SetContentMobileDisplayArg;
import com.facishare.marketing.api.result.kis.TempListAllMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityResult;
import com.facishare.marketing.api.result.qr.QueryQRCodeResult;
import com.facishare.marketing.common.result.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

public interface ContentMarketingEventService {
     Result<PageResult<MarketingEventsBriefResult>> listContentMarketingEvent(String ea, Integer fsUserId, ListContentMarketingEventArg arg);

     Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>> listMarketingContent(String ea, Integer fsUserId, String marketingEventId, List<Integer> objectTypes , Integer pageNo, Integer pageSize);
     Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>> listMarketingContent(String ea, Integer fsUserId, String marketingEventId, List<Integer> objectTypes , Integer pageNo, Integer pageSize, boolean needCheckMobileDisplay);

     Result<com.facishare.marketing.common.result.PageResult<TempListAllMarketingActivityResult>> listMarketingActivityInContentMarketingEvent(String ea, Integer fsUserId, ListMarketingActivityInContentMarketingEventArg arg) ;

     Result<GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult> getOrCreateQrCodeByMarketingEventIdAndObjectInfo(String ea, Integer fsUserId, GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg arg) ;

    Result<MarketingContentVO> getHexagonContent(String ea, Integer fsUserId, HexagonStatisticArg arg);

    Result<Void> setContentMobileDisplay(String ea, Integer fsUserId, SetContentMobileDisplayArg arg);
}
