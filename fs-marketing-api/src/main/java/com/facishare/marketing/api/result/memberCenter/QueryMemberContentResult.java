package com.facishare.marketing.api.result.memberCenter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2021/1/4.
 */
@Data
public class QueryMemberContentResult implements Serializable{
    @ApiModelProperty(name = "市场活动id")
    private String marketingEventId;

    @ApiModelProperty(name = "物料id")
    private String objectId;

    @ApiModelProperty(name = "物料类型")
    private Integer objectType;

    @ApiModelProperty(name = "标题")
    private String title;

    @ApiModelProperty(name = "市场活动状态")
    private String marketingStatus;

    @ApiModelProperty(name = "市场活动类型")
    private String eventType;

    @ApiModelProperty(name = "活动状态")
    private Integer flowStatus;

    @ApiModelProperty(name = "开始时间")
    private Long startTime;

    @ApiModelProperty(name = "截至时间")
    private Long endTime;

    @ApiModelProperty(name = "封面图url")
    private String url;

    @ApiModelProperty(name = "封面缩略图url")
    private String thumbnailUrl;

    @ApiModelProperty(name = "会议地点")
    private String location;

    @ApiModelProperty(name = "报名时间")
    private Long submitTime;

    @ApiModelProperty(name = "报名页物料类型")
    private Integer submitMaterialType;

    @ApiModelProperty(name = "报名页物料id")
    private String submitMaterialId;
}
