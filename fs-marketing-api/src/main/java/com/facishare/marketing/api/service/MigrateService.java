package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.common.result.Result;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MigrateService {
    Result<Map<String, Integer>> deleteAllMarketingFlowByEas(MigrateEasArg arg);

    Result<Void> initMarketingFlowObjects(MigrateEasArg arg);

    Result<Void> initUserMarketingAccountObj(MigrateEasArg arg);

    Result<Void> initMarketingFlowObjectsForProEas();

    Result<List<String>> listAllProEas();

    Result<Void> migrateOldTemplateDataToMarketing(MigrateEasArg arg);

    Result<Void> removeInCompleteTemplateData(MigrateEasArg arg);

    Result<Void> migrateMarketingUserGroupTags(MigrateEasArg arg);

    Result<Void> migrateUserMarketingAccountTags(MigrateEasArg arg);

    Result<Void> migrateFlowTags(MigrateEasArg arg);

    Result<Void>  migrateFlowDraftTags(MigrateEasArg arg);

    Result<Void> migrateTagsManage(MigrateEasArg arg);

    Result<Void> migrateMaterial(MigrateEasArg arg);

    Result<Void> updateMarketingActivitySpreadType(MigrateEasArg migrateEasArg);

    Result<Void> addCancelStatusOptionToMarketingActivity(MigrateEasArg migrateEasArg);

    Result<Void> initEnterpriseWxWorkExternalUserData(MigrateEasArg migrateEasArg);

    Result<Void> tryInitMarketingBehaviorObject(MigrateEasArg arg);

    Result<Void> initMarketingEventCommonSetting(MigrateEasArg arg);

    Result<List<String>> migrateAllEaWechatAccountBind();

    Result<List<String>> migrateTryAppendFieldsToWxWorkExternalUserObj(MigrateEasArg arg);

    Result<List<String>> migrateTryUpdateUnionIdToWxWorkExternalUserObj(MigrateEasArg arg);

    Result<Void> migrateChangeTagGroupName(MigrateEasArg arg);

    Result<Void> migrateChangeTagGroupName(MigrateEiasArg arg);

    Result<Void> migrateTagsToDefaultTagModel(MigrateEasArg arg);

    Result<Void> migratePaasSecondTagToFirstTags(MigrateEasArg arg);

    Result migrateLeadsUtmFieldByEas(MigrateEasArg arg);

    Result migrateLeadsMarketingActivity(MigrateEasArg arg);

    Result<Void> migrateRoleUsers(MigrateEasArg arg);

    Result<Void> migrateDefaultIntroductionPageSiteId(MigrateEasArg arg);

    Result<Void> presetEnterpriseBoard(MigrateEasArg arg);

    Result<Void> statisticAllMemberData(MigrateEasArg arg);

    Result<Void> migrateMemberDescribeAndLayout(MigrateEasArg arg);

    Result migrateAppendLeadMarketingSpreadUser(MigrateEasArg arg);

    Result<Void> enableMarketingPromotionChannel(MigrateEasArg migrateEasArg);

    Result addCampaignMembersObjField(MigrateEasArg arg);

    Result syncCrmInvalidConference(MigrateEasArg arg);

    Result resetCampaignMergeData(MigrateEasArg arg);

    Result<Void> migrateIntegrateCategory();

    Result syncLiveFormDataToMember(MigrateEasArg arg);

    Result resetNoSaveCrmData(MigrateEasArg arg);

    Result saveCrmCampaignData(MigrateEasArg arg);

    Result<String> migrateUserToCrmMemberManagerRole();

    Result reImportDataToCrm(MigrateEasArg arg);

    Result<Void> tryAppendWechatFanFields(MigrateEasArg arg);

    Result<Void> markWxFanSpreadUserAsSingle(MigrateEasArg arg);

    Result deleteMankeepRedisAccessToken();

    Result deleteMankeepProRedisAccessToken();

    Result reflashMlData();

    Result resetLiveAllData(MigrateEasArg arg);

    Result<Void> syncCrmDataToMarketingUserAccount(MigrateEasArg easArg);

    Result<Void> calculateMarketingUserGroup(MigrateEasArg easArg);

    Result<Integer> migrateAllSystemPages();

    Result resetMarketingNotActivityAndLive(MigrateEasArg easArg);

    Result resetSpreadUserZeroData(MigrateEasArg easArg);

    Result resetCardQrCodeByEa(MigrateEasArg easArg);

    Result resetCardQrCodeByUid(MigrateEasArg easArg);

    Result resetEmailData();

    Result resetUtmFieldName(MigrateEasArg easArg);

    Result resetMarketingEventUV(MigrateEasArg arg);

    Result setCustomizeFormToMarketingEvent(MigrateEasArg easArg);

    Result syncMarketingActivityLookupRoler(MigrateEasArg easArg);

    Result resetCustomizeFormData(MigrateEasArg easArg);

    Result resetCustomizeFormDataById(MigrateEasArg easArg);

    Result resetCustomizeMiniAppCardNavbar(MigrateEasArg easArg);

    Result initOldMakeringRoleFunctionPrivilege(MigrateEasArg arg);

    Result createDefaultConferenceSite(MigrateEasArg arg);

    Result syncContentMarketingEventMaterialRelationEventType(MigrateEasArg arg);

    Result syncFileRegisterActionMaterial(MigrateEasArg arg);

    Result resetConferenceStyle(MigrateEasArg arg);

    Result resetConferenceCustomizeFormDataUser(MigrateEasArg arg);

    Result initQywxAddressBook(MigrateEasArg arg);

    Result refreshCampaignMergeDataFormMarketingEvent(MigrateEasArg arg);


    Result fixQywxEmployeeMarketingActivtyStatData(MigrateEasArg arg);

    Result syncCrmInvalidMarketingEventLive(MigrateEasArg arg);

    Result addMarketingActivityObjField(MigrateEasArg arg);

    Result updateWeChatMarketingActivityWxAppId(MigrateEasArg arg);

    Result initMarketingLicense(MigrateEasArg arg);

    Result addLeadsObjSpreadChannelField(MigrateEasArg arg);

    Result updateLeadCreator(MigrateEasArg arg);

    Result initEnterpriseMarketing(MigrateEasArg arg);

    Result updateCardCover(MigrateEasArg arg);

    Result insertCorpIdMapping(MigrateEasArg arg);

    Result initPubPlatAuthComponent(MigrateEasArg arg);

    Result initAdMarketingPlugin(MigrateEasArg arg);

    Result initHeadlinesAdMarketingPlugin(MigrateEasArg arg);

    Result initWeChatGroup(MigrateEasArg arg);

    Result updateMarketingBehavior(MigrateEasArg arg);

    Result appendLeadMarketingPartner(MigrateEasArg arg);

    Result updateMarketingObjStatus(MigrateEasArg arg);

    Result appendCampaignMembersLiveData(MigrateEasArg arg);

    Result appendCampaignMembersPayData(MigrateEasArg arg);

    Result fixCrmObjectData(MigrateEasArg arg);

    Result updateExternalObjUnionIdField(MigrateEasArg arg);

    Result initWechatFriendsRecordObj(MigrateEasArg arg);

    Result appendCampaignMembersSpreadData(MigrateEasArg arg);

    Result syncExternalcontactData(MigrateEasArg arg);

    Result deleteWechatFriendsRecordObj(MigrateEasArg arg);

    Result refactorWechatFriendsRecord(MigrateEasArg arg);

    Result addDefaultEventTypeOptionsToFieldDescribeByEas(MigrateEasArg arg);

    Result add4Obj(MigrateEasArg arg);

    Result updateScrmObj(MigrateEasArg arg);

    Result tryDeleteDescribeCustomField(MigrateEasArg arg);

    Result outAccountToFsAccountBatchByEas(MigrateEasArg arg);


    Result appendWechatFriendsRecordData(MigrateEasArg arg);

    Result updateMarketingBehaviorDescribe(MigrateEasArg arg);

    Result initAdDataReturnDetailObj(MigrateEasArg arg);

    Result refreshAdCampaignData(String ea, String adAccountId, String source, int day);

    Result syncMarketingTermServingLinesDataByKeyword(String accountId, int day);

    Result<Void> syncMarketingTermServingLinesDataByKeywordByDate(String accountId, String date);

    Result initMktContentMgmtLogObjManager(MigrateEasArg arg);

    Result initSmsSendRecordObj(MigrateEasArg arg);

    Result initQywxMomentTaskStatisticData(MigrateEasArg arg);

    Result updateMemberObj(MigrateEasArg arg);

    Result syncUserAccountRelationWxData(MigrateEasArg arg);

    Result refreshAllAdAccountData(RefreshAllAdAccountData arg);

    /**
     * 更新企业微信对象的ower 从系统->CRM绑定员工
     * @param arg
     * @return
     */
    Result updateQywxObjectOwnerAsBindFsUser(MigrateEasArg arg);

    /**
     * 用户绑定CRM账号后，将虚拟身份的推广数据合并到CRM账号上，包括更新绑定表，虚拟账号表，推广任务表，数据统计表，表单表；
     * 更新企业微信对象中的负责人从系统到CRM账号
     * 传参：List<String> ids   *********:1001:wowx1mDAAA3rclRZTy8B1VpCFFHxCgrQ 第一部分为虚拟账号，第二部分为CRM账号，第三部分为企业微信账号
     */
    Result updateMarketingSpreadDataAsBindFsUser(MigrateEasArg arg);

    Result initQywxActivatedAccount(MigrateEasArg arg);
    Result cleanDuplicateData(MigrateEasArg arg);
    /**
     * 更新活动成员推广人字段 is_need_convert属性
     */
    Result updateCampaignMembersObjSpreadData(MigrateEasArg arg);
    /**
     * 更新会员预设字段 marketing_source_type 为单选字段
     */
    Result updateMemberObjData(MigrateEasArg arg);

    Result invoke(MigrateEasArg arg);

    Result<Void> refreshCards(MigrateEasArg arg);

    Result<Void> addTeamMemberByQywxBindCrmEvent(MigrateEasArg arg);

    Result initWechatGroupTeamMember(MigrateEasArg arg);

    Result refreshWechatExternalUserAddSource(MigrateEasArg arg);

    Result<Void> mergeUserMarketingActionStatistic(MigrateEasArg arg);

    Result initAdvertisingDetailsObj(MigrateEasArg arg);

    Result initLeadJoinCompensateStatusField(MigrateEasArg arg);

    Result<Void> deleteIdempotentRecordData(int day);

    Result<Void> updateWxExternalUserObjSceneAndHideButton(MigrateEasArg arg);

    void registerMarketingLiveOldMaterial();

    Result<Void> reindexAdvertisingDetailsObj(MigrateEasArg arg);

    Result<Void> generateThirdPlatformSecret(MigrateEasArg arg);

    //重新生成名片二维码
    Result<Void> createEmployeeCard(MigrateEasArg arg);

    void addMarketingTagEa(List<String> eas);

    Result<Void> initMarketingLiveChannelAccount();

    Result<Void> pullQywxDataByEnterpriseUserIds(MigrateEasArg arg);

    Result<Void> updateMiniappDomain(MigrateEasArg arg);

    Result<Void> enablePublicData(MigrateEasArg arg);

    Result<Void> batchPurgeData(MigrateEasArg arg);

    Result<Void> deleteCouponDistributionObj(MigrateEasArg arg);

    Result<Void> updateCouponMaxCoupons(MigrateEasArg arg);

    Result<Void> deleteSessionKey(SessionUidArg arg);

    Result<Void> createSystemMenuTemplate(MigrateEasArg arg);

    Result<Void> queryAccountObjectExist(MigrateEasArg arg);

    Result<Void> syncQywxBindInfo(MigrateEasArg arg);

    Result<Void> unbindMarketingUserBindInfo(MigrateEasArg arg);

    Result<Void> fixCampaignMergeData(FixCampaignMergeDataArg arg);

    Result<Void> deleteMiniAppReleaseRecord(DeleteMiniAppReleaseRecordArg arg);
}
