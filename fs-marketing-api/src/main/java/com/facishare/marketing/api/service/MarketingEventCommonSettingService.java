package com.facishare.marketing.api.service;

import com.facishare.marketing.api.arg.marketingEvent.UpdateMarketingEventAnalysisSettingArg;
import com.facishare.marketing.api.arg.marketingEventCommonSetting.UpdateMarketingEventCommonSettingArg;
import com.facishare.marketing.api.result.MemberEnrollMarketingEventDisplayTypeConfigItem;
import com.facishare.marketing.api.result.marketingEventCommonSetting.GetMarketingEventCommonSettingResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.GetMarketingEventTypeFieldResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.SceneHexagonTemplateResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.SceneTriggerTemplateSimpleResult;
import com.facishare.marketing.api.vo.marketingevent.MarketingEventAnalysisSettingVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import java.util.List;

/**
 * Created  By zhoux 2021/03/08
 **/
public interface MarketingEventCommonSettingService {

    Result<GetMarketingEventCommonSettingResult> getMarketingEventCommonSetting(Integer type, String ea);

    Result updateMarketingEventCommonSetting(UpdateMarketingEventCommonSettingArg arg);

    Result<List<GetMarketingEventTypeFieldResult>> getMarketingEventTypeField(String ea);

    Result<SceneTriggerTemplateSimpleResult> getSceneTriggerTemplates(String ea);

    Result<SceneHexagonTemplateResult> getSceneHexagonTemplates(String ea);

    Result<Boolean> setDefaultTemplate(String ea,String templateId);


    Result<List<MemberEnrollMarketingEventDisplayTypeConfigItem>> getMemberEnrollMarketingEventDisplayTypeConfig(String ea, Integer fsUserId);

    Result setMemberEnrollMarketingEventDisplayTypeConfig(String ea, Integer fsUserId, List<MemberEnrollMarketingEventDisplayTypeConfigItem> configItemList);

    Result<Void> updateAnalysisSetting(UpdateMarketingEventAnalysisSettingArg arg);

    Result<MarketingEventAnalysisSettingVO> getAnalysisSetting(String ea);
}
