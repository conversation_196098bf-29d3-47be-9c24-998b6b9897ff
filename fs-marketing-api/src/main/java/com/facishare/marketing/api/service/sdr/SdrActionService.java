package com.facishare.marketing.api.service.sdr;

import com.facishare.marketing.api.arg.sdr.SdrActionArg;
import com.facishare.marketing.common.result.FunctionResult;

import java.util.Map;

public interface SdrActionService {
    FunctionResult<Map<String, Object>> marketingInsights(SdrActionArg arg);
    FunctionResult<Map<String, Object>> marketingInsightsDeepOptimized(SdrActionArg arg);
    FunctionResult<Map<String, Object>> batchQuerySDRVariableData(SdrActionArg arg);
    FunctionResult<Map<String, Object>> batchQuerySDRRelatedScripts(SdrActionArg arg);
}
