package com.facishare.marketing.outapi.service;

import com.facishare.marketing.common.result.Result;

import java.util.List;
import java.util.Map;

public interface OutObjectService {

    /**
     * 获取物料名称
     * @param ea 企业
     * @param objectTypeToObjectIdListMap   key: 物料类型  value: 物料的id集合
     * @return key: 物料id, value: 物料名字
     */
    Result<Map<String, String>> getObjectName(String ea, Map<Integer, List<String>> objectTypeToObjectIdListMap);

}
