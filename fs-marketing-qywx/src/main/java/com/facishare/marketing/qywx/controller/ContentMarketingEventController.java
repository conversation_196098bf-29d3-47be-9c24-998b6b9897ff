package com.facishare.marketing.qywx.controller;

import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.contentmaketing.MarketingContentVO;
import com.facishare.marketing.api.service.ContentMarketingEventService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.ListContentMarketingEventArg;
import com.facishare.marketing.qywx.arg.ListMarketingContentArg;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2020/9/8 15:24
 * @Version 1.0
 */
@RestController
@RequestMapping("/contentMarketingEvent")
@Slf4j
public class ContentMarketingEventController {
    @Autowired
    private ContentMarketingEventService contentMarketingEventService;

    @ApiOperation(value = "查询内容营销列表")
    @TokenCheckTrigger
    @RequestMapping(value = "listContentMarketingEvent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PageResult<MarketingEventsBriefResult>> listContentMarketingEvent(@RequestBody ListContentMarketingEventArg arg){
        if (StringUtils.isBlank(arg.getToken())){
            log.warn("ContentMarketingEventController.listContentMarketingEvent failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null){
            log.warn("ContentMarketingEventController.listContentMarketingEvent failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg listContentMarketingEventArg = new com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg();
        BeanUtils.copyProperties(arg, listContentMarketingEventArg);
        return contentMarketingEventService.listContentMarketingEvent(arg.getFsEa(), arg.getFsUserId(), listContentMarketingEventArg);
    }

    @ApiOperation(value = "查询市场活动下的物料")
    @TokenCheckTrigger
    @RequestMapping(value = "listMarketingContent", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>> listMarketingContent(@RequestBody ListMarketingContentArg arg){
        if (StringUtils.isBlank(arg.getToken())){
            log.warn("ContentMarketingEventController.listMarketingContent failed token is null");
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        if (arg == null){
            log.warn("ContentMarketingEventController.listMarketingContent failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        return contentMarketingEventService.listMarketingContent(arg.getFsEa(),arg.getFsUserId(),arg.getMarketingEventId(),arg.getObjectTypes(),arg.getPageNo(),arg.getPageSize(), true);
    }
}
