package com.facishare.marketing.qywx.arg;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2020/9/8 15:26
 * @Version 1.0
 */
@Data
public class ListContentMarketingEventArg extends QYWXBaseArg {
    @ApiModelProperty(value = "市场活动状态")
    private String bizStatus;
    @ApiModelProperty(value = "市场活动名称")
    private String name;
    @ApiModelProperty(value = "活动类型 content_marketing 内容营销")
    private String eventType;
    @ApiModelProperty(value = "要查询的页号", allowableValues = "range[1, infinity]")
    private Integer pageNo;
    @ApiModelProperty(value = "分页大小", allowableValues = "range[1, 50]")
    private Integer pageSize;
    @ApiModelProperty(value = "活动类型列表 不传则查询全部")
    private List<String> eventTypeList;
    // 标签过滤
    private MaterialTagFilterArg materialTagFilter;

    public ListContentMarketingEventArg(){}

    public ListContentMarketingEventArg(Integer pageNo, Integer pageSize){
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public int getOffset() {
        return (pageNo - 1) * pageSize;
    }

    public int getLimit() {
        return pageSize;
    }
}
