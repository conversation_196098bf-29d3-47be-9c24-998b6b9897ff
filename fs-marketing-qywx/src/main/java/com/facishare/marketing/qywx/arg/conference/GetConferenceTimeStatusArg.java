package com.facishare.marketing.qywx.arg.conference;

import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2021/02/03
 **/
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetConferenceTimeStatusArg extends QYWXBaseArg {

    @ApiModelProperty("会议id")
    private String conferenceId;

    public boolean isWrongParam() {
        return StringUtils.isBlank(conferenceId);
    }

}
