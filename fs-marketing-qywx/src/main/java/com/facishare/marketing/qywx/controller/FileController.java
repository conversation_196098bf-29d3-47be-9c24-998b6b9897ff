package com.facishare.marketing.qywx.controller;


import com.facishare.mankeep.api.result.PreviewResult;
import com.facishare.mankeep.api.result.WXFileResult;
import com.facishare.mankeep.api.vo.PreviewVO;
import com.facishare.mankeep.api.vo.distribution.DistributionFileVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.arg.GetDownLoadUrlArg;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.SendFileMailArg;
import com.facishare.marketing.api.arg.file.UploadFileArg;
import com.facishare.marketing.api.result.FilePreviewResult;
import com.facishare.marketing.api.result.GetDownLoadResult;
import com.facishare.marketing.api.result.UploadFileResult;
import com.facishare.marketing.api.result.file.BatchGetUrlByPathResult;
import com.facishare.marketing.api.result.file.GenerateUploadFileOmitResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileByGroupResult;
import com.facishare.marketing.api.result.video.QueryVideoPlayUrlByIdResult;
import com.facishare.marketing.api.service.FileService;
import com.facishare.marketing.api.service.VideoService;
import com.facishare.marketing.api.service.fileLibrary.FileLibraryService;
import com.facishare.marketing.common.enums.FileTypeEnum;
import com.facishare.marketing.common.filters.IPLimiterFilter;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.HttpUtil;
import com.facishare.marketing.common.util.URLValidator;
import com.facishare.marketing.qywx.annoation.TokenCheckTrigger;
import com.facishare.marketing.qywx.arg.*;
import com.facishare.marketing.qywx.arg.distribute.DistributionFileArg;
import com.facishare.uc.api.service.CaptchaService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.Arrays;

/**
 * Created by zhengh on 2020/1/13.
 */
@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {
    @Autowired
    private FileService fileService;
    @Autowired
    private com.facishare.mankeep.api.service.FileService outerFileService;
    @Autowired
    private FileLibraryService fileLibraryService;
    @Autowired
    private VideoService videoService;
    @Autowired
    private IPLimiterFilter ipLimiterFilter;
    @Autowired
    private CaptchaService captchaService;

    @ApiOperation(value = "上传文件")
    @TokenCheckTrigger
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<UploadFileResult> uploadFile(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer type,
                                        @RequestParam(value = "needApath", required = false) Boolean needApath,
                                        @RequestParam(value = "needPermanent", required = false) Boolean needPermanent){
        try {
            Result checkResult = checkFileFormat(type, file);
            if (!checkResult.isSuccess()) {
                return new Result<>(SHErrorCode.getByCode(checkResult.getErrCode()));
            }

            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setExt(getUploadFileExt(file));
            uploadFileArg.setFileBytes(file.getBytes());
            log.info("uploadFile file size: {}", uploadFileArg.getFileBytes().length);


            uploadFileArg.setFileName(file.getOriginalFilename());
            uploadFileArg.setNeedApath(needApath);
            uploadFileArg.setNeedPermanent(needPermanent);

            return fileService.uploadFile(uploadFileArg);
        } catch (IOException e) {
            log.warn("exception:",  e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @ApiOperation(value = "上传C文件")
    @TokenCheckTrigger
    @RequestMapping(value = "/uploadToCFile", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<UploadFileResult> uploadToCFile(@RequestParam("file") MultipartFile file, @RequestParam("type") Integer type, @RequestParam(value = "needCpath", required = false) Integer needCpath
            , @RequestParam(value = "ea", required = false) String ea, @RequestParam(value = "objectId", required = false) String objectId
            , @RequestParam(value = "objectType", required = false) Integer objectType) {
        try {
            Result checkResult = checkFileFormat(type, file);
            if (!checkResult.isSuccess()){
                return new Result<>(SHErrorCode.getByCode(checkResult.getErrCode()));
            }

            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setExt(getUploadFileExt(file));
            uploadFileArg.setFileBytes(file.getBytes());
            log.info("uploadToCFile file size: {}", uploadFileArg.getFileBytes().length);
            uploadFileArg.setFileName(file.getOriginalFilename());
            uploadFileArg.setNeedCpath(needCpath);
            uploadFileArg.setEa(ea);
            uploadFileArg.setObjectId(objectId);
            uploadFileArg.setObjectType(objectType);
            return fileService.uploadToCFile(uploadFileArg);
        }catch (IOException e) {
            log.warn("exception:",  e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @ApiOperation(value = "上传文件至npath")
    @TokenCheckTrigger
    @RequestMapping(value = "/uploadNFileByObject", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<UploadFileResult> uploadNFileByObject(@RequestParam("file") MultipartFile file, @RequestParam("objectId") String objectId, @RequestParam("objectType") Integer objectType) {
        if (StringUtils.isBlank(objectId) || objectType == null) {
            log.warn("FileController.uploadNFileByObject param error");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try {
            UploadFileArg uploadFileArg = new UploadFileArg();
            uploadFileArg.setExt(getUploadFileExt(file));
            uploadFileArg.setFileBytes(file.getBytes());
            uploadFileArg.setFileName(file.getOriginalFilename());
            uploadFileArg.setObjectId(objectId);
            uploadFileArg.setObjectType(objectType);
            return fileService.uploadNFileByObject(uploadFileArg);
        } catch (Exception e) {
            log.warn("FileController.uploadNFileByObject error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }


    @ApiOperation(value = "文件路径转换")
    @TokenCheckTrigger
    @RequestMapping(value = "/transferUrl", produces = "image/jpg")
    byte[] transferUrl(@RequestBody FiletransferUrlArg arg){
        if (StringUtils.isBlank(arg.getToken())){
            log.warn("FileController.transferUrl failed token is null");
            return null;
        }
        if (arg == null || StringUtils.isBlank(arg.getPath())){
            log.warn("FileController.transferUrl failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return null;
        }
        return fileService.transferUrl(arg.getPath());
    }

    @ApiOperation(value = "批量转换path到url")
    @TokenCheckTrigger
    @RequestMapping(value = "/batchGetUrlByPath", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    Result<BatchGetUrlByPathResult> batchGetUrlByPath(@RequestBody BatchGetUrlByPathArg arg){
        if (StringUtils.isBlank(arg.getToken())){
            log.warn("FileController.batchGetUrlByPath failed token is null");
            return null;
        }
        if (arg == null || CollectionUtils.isEmpty(arg.getPathList())){
            log.warn("FileController.batchGetUrlByPath failed arg param error token:{} arg:{}", arg.getToken(), arg);
            return null;
        }

        com.facishare.marketing.api.arg.file.BatchGetUrlByPathArg vo = BeanUtil.copy(arg, com.facishare.marketing.api.arg.file.BatchGetUrlByPathArg.class);
        arg.setEa(arg.getFsEa());
        return fileService.batchGetUrlByPath(vo);
    }

    @RequestMapping(value = "redirectDownload", method = RequestMethod.GET, produces = "image/jpg")
    void redirectDownload(HttpServletResponse response, @RequestParam("url") String path){
        if (path.startsWith("//") && !path.contains("http") && !path.contains("https")) {
            path = "https" + path;
        }

        try {
            log.info("origin path = {}", path);
            path = URLDecoder.decode(path, "UTF-8");
            log.info("decoded path = {}", path);
        } catch (Exception e) {
            log.warn("exception:",  e);
        }

        String lastCha = "\"";
        String lastCha2 = "\"/";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(path)) {
            if (path.endsWith(lastCha)) {
                path = path.substring(0, path.length() - 1);
            } else if (path.endsWith(lastCha2)) {
                path = path.substring(0, path.length() - 2);
            }
        }

        if (path.contains("qpic")) {
            if (path.contains("&tp=webp&wxfrom=5")) {
                path = path.replaceAll("&tp=webp&wxfrom=5", "");
            }
            if (path.contains("wx_fmt=png")) {
                path = path.replaceAll("wx_fmt=png", "wx_fmt=jpg");
            }
        }

        if (!URLValidator.validateURL(path)) {
            HttpUtil.writeErrorInfo(response);
            return;
        }

        InputStream inputStream = null;
        OutputStream os = null;
        try {
            byte[] bytes = fileService.redirectDownload(path);
            if (null == bytes) {
                HttpUtil.writeErrorInfo(response);
                return;
            }
            os = response.getOutputStream();
            inputStream = new ByteArrayInputStream(bytes);
            int len = 0;
            byte[] bis = new byte[1024];
            while ((len = inputStream.read(bis)) != -1) {
                os.write(bis, 0, len);
            }
            inputStream.close();
        } catch (Exception e) {
            log.error("Error: getImageFromNetByUrl! url:{}", path, e);
        } finally {
            try {
                if (null != os) {
                    os.close();
                }
            } catch (Exception e) {
                log.error("Error: getImageFromNetByUrl! url:{}", path, e);
            }
        }
    }

    private Result checkFileFormat(Integer type, MultipartFile file){

        String originalFilename = file.getOriginalFilename();
        String[] strings = originalFilename.split("\\.");
        // check file's ext
        String ext = strings[strings.length - 1].toLowerCase();
        if (strings.length == 0) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        if (type == FileTypeEnum.PICTURE.getType()) {
            String[] picExtList = {"jpeg", "jpg", "png", "gif", "bmp"};
            if (!Arrays.asList(picExtList).contains(ext)) {
                log.error("originalFilename : " + originalFilename);
                return new Result<>(SHErrorCode.FILE_FORMAT_ERROR);
            }
        }

        return new Result<>(SHErrorCode.SUCCESS);
    }

    private String getUploadFileExt(MultipartFile file){
        String originalFilename = file.getOriginalFilename();
        String[] strings = originalFilename.split("\\.");
        // check file's ext
        String ext = strings[strings.length - 1].toLowerCase();
        return ext;
    }

    @ApiOperation(value = "获取分销资料文件")
    @TokenCheckTrigger
    @RequestMapping(value = "/getDistributionMaterialFile", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<WXFileResult> getDistributionMaterialFile(@RequestBody DistributionFileArg arg) {
        try {
            if (null == arg) {
                log.info("FileController.getDistributionMaterialFile failed arg is null");
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(arg.getFolderId())) {
                log.info("FileController.getDistributionMaterialFile failed folderId is null");
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            if (org.apache.commons.lang3.StringUtils.isBlank(arg.getGroupId()) && org.apache.commons.lang3.StringUtils.isBlank(arg.getDistributorId()) && org.apache.commons.lang3.StringUtils.isBlank(arg.getOperatorId())) {
                log.info("FileController.getDistributionMaterialFile failed groupId , distributorId  and  operatorId all are null");
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            DistributionFileVO vo = com.facishare.mankeep.common.util.BeanUtil.copy(arg, DistributionFileVO.class);
            ModelResult<WXFileResult> modelResult = outerFileService.getDistributionMaterialFile(vo);
            if (!modelResult.isSuccess()){
                return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
            }
            return Result.newSuccess(modelResult.getData());
        } catch (Exception e) {
            log.error("FileController.getDistributionMaterialFile error, arg={} exception", arg, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }


    @ApiOperation(value = "获取文件浏览地址")
    @TokenCheckTrigger
    @RequestMapping(value = "/getPreviewUrl", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<PreviewResult> getPreviewUrl(@RequestBody GetPreviewUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.getPreviewUrl param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PreviewVO previewVO = new PreviewVO();
        previewVO.setNPath(arg.getNPath());
        previewVO.setFsEa(arg.getFileEa());
        ModelResult<PreviewResult> previewResultModelResult = outerFileService.getPreviewUrl(previewVO);
        if(!previewResultModelResult.isSuccess()) {
            return Result.newError(previewResultModelResult.getErrCode(), previewResultModelResult.getErrMsg());
        }
        return Result.newSuccess(previewResultModelResult.getData());
    }

    @ApiOperation(value = "查询文件详情")
    @TokenCheckTrigger
    @RequestMapping(value = "/getFileDetailById", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<ListFileByGroupResult> getFileDetailById(@RequestBody IdArg arg) {
        if(arg==null|| StringUtils.isBlank(arg.getId())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return fileLibraryService.getFileDetailById(arg.getId());
    }

    @ApiOperation(value = "查询视频详情")
    @TokenCheckTrigger
    @RequestMapping(value = "/getVideoDetailById", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<QueryVideoPlayUrlByIdResult> getVideoDetailById(@RequestBody IdArg arg) {
        if(arg==null|| StringUtils.isBlank(arg.getId())){
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return videoService.queryVideoPlayUrlById(arg.getId());
    }

    @ApiOperation(value = "通过tcpath获取cdn的url地址")
    @TokenCheckTrigger
    @RequestMapping(value = "/getCdnUrlByTcpath", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<String> getCdnUrlByTcpath(@RequestBody GetCdnUrlByTcpathArg arg){
        if (StringUtils.isEmpty(arg.getTcpath()) || StringUtils.isEmpty(arg.getFsEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fileService.getCdnUrlByTcpath(arg.getFsEa(), arg.getTcpath());
    }

    @ApiOperation(value = "获取文件签名上传信息")
    @TokenCheckTrigger
    @RequestMapping(value = "/generateUploadFileOmit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GenerateUploadFileOmitResult> generateUploadFileOmit(@RequestBody GenerateUploadFileOmitArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.generateUploadFileOmit param error arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = StringUtils.isBlank(arg.getEa()) ? arg.getFsEa() : arg.getEa();
        if (StringUtils.isBlank(ea)) {
            log.warn("FileController.generateUploadFileOmit ea is null arg:{}", arg);
        }
        Result<GenerateUploadFileOmitResult> uploadFileOmitResult = fileService.generateUploadFileOmit(ea, arg.getResourceType(), arg.getFilename(), arg.getExtension(), arg.getFileSize());
        if (!uploadFileOmitResult.isSuccess()) {
            return Result.newError(uploadFileOmitResult.getErrCode(), uploadFileOmitResult.getErrMsg());
        }

        return Result.newSuccess(uploadFileOmitResult.getData());
    }

    @ApiOperation(value = "邮件发送，带图形验证码")
    @TokenCheckTrigger
    @RequestMapping(value = "/sendFileMail", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<Void> sendFileMail(HttpServletRequest httpServletRequest, @RequestBody SendFileMailArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.sendFileMail param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        // 配合 IpLimiterFilter 配置使用:
        // 1. 调用获取接口是否异常告警
        // 2. 超出限制,返回图形验证码
        // 3. 校验图形验证码
        try {
//            if (ipLimiterFilter.alarm(httpServletRequest)) {
                if (StringUtils.isEmpty(arg.getEpxId()) || StringUtils.isEmpty(arg.getCode())) {
                    return Result.newError(SHErrorCode.PHONE_NUMBER_CAPTCHA);
                }
                if (!captchaService.verify(arg.getEpxId(), arg.getCode())) {
                    return Result.newError(SHErrorCode.PHONE_NUMBER_CAPTCHA_ERROR);
                }
//            }
        } catch (Exception e) {
            log.warn("captcha fail e:", e);
        }

        return fileService.sendFileMail(arg);
    }

    @ApiOperation(value = "获取文件浏览地址")
    @TokenCheckTrigger
    @RequestMapping(value = "/getPreviewUrlV2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<FilePreviewResult> getPreviewUrlV2(@RequestBody com.facishare.marketing.api.arg.GetPreviewUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.getPreviewUrlV2 param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fileService.getPreviewUrlV2(arg);
    }

    @ApiOperation(value = "获取文件下载地址")
    @TokenCheckTrigger
    @RequestMapping(value = "/getDownLoadUrlV2", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    public Result<GetDownLoadResult> getDownLoadUrlV2(@RequestBody GetDownLoadUrlArg arg) {
        if (arg.isWrongParam()) {
            log.warn("FileController.getPreviewUrlV2 param error arg:{}");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return fileService.getDownLoadUrlV2(arg);
    }
}
