<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:duboo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       ">

  <dubbo:application name="${dubbo.application.name}"/>
  <dubbo:registry address="${dubbo.registry.address}" file="${dubbo.registry.file}" timeout="120000"/>
  <dubbo:consumer check="false" filter="tracerpc" timeout="7000"/>
  <!--外部服务api -->
  <dubbo:reference id="activeSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" protocol="dubbo"/>

  <!--营销通内部api -->
  <dubbo:reference id="qywxInnerService" interface="com.facishare.marketing.api.service.qywx.QYWXInnerService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="innerUserInfoService" interface="com.facishare.marketing.api.service.qywx.inner.InnerUserInfoService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="qywxThirdService" interface="com.facishare.marketing.api.service.qywx.QYWXThirdService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="miniappLoginService" interface="com.facishare.marketing.api.service.qywx.MiniappLoginService" version="${dubbo.provider.version}">
    <dubbo:method name="pLogin" timeout="15000" retries="0"/>
    <dubbo:method name="eLogin" timeout="15000" retries="0"/>
    <dubbo:method name="updateEUserInfo" timeout="15000" retries="0"/>
    <dubbo:method name="updatePUserInfo" timeout="15000" retries="0"/>
    <dubbo:method name="wxLogin" timeout="15000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference id="userService" interface="com.facishare.marketing.api.service.UserService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="spreadTaskService" interface="com.facishare.marketing.api.service.kis.SpreadTaskService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="articleService" interface="com.facishare.marketing.api.service.kis.ArticleService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="employeeSpreadStatisticService" interface="com.facishare.marketing.api.service.EmployeeSpreadStatisticService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="productService" interface="com.facishare.marketing.api.service.ProductService"  version="${dubbo.provider.version}"/>
  <dubbo:reference id="kisMarketingActivityService" interface="com.facishare.marketing.api.service.kis.KisMarketingActivityService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="marketingReportService" interface="com.facishare.marketing.api.service.MarketingReportService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="customerService" interface="com.facishare.marketing.api.service.CustomerService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="hexagonService" interface="com.facishare.marketing.api.service.hexagon.HexagonService" version="${dubbo.provider.version}"/>
  <duboo:reference id="spreadWorkService" interface="com.facishare.marketing.api.service.kis.SpreadWorkService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="accountService" interface="com.facishare.marketing.api.service.AccountService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="kisActionService" interface="com.facishare.marketing.api.service.kis.KisActionService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="sendService" interface="com.facishare.marketing.api.service.sms.SendService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="momentPosterService" interface="com.facishare.marketing.api.service.MomentPosterService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="qrPosterService" interface="com.facishare.marketing.api.service.qr.QRPosterService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="qrCodeService" interface="com.facishare.marketing.api.service.qr.QRCodeService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="fileService" interface="com.facishare.marketing.api.service.FileService" timeout="60000" version="${dubbo.provider.version}"/>
  <dubbo:reference id="qywxCardService" interface="com.facishare.marketing.api.service.qywx.CardService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="conferenceService" interface="com.facishare.marketing.api.service.conference.ConferenceService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="materialSearchService" interface="com.facishare.marketing.api.service.MaterialSearchService" version="${dubbo.provider.version}">
    <dubbo:method name="search" retries="0" timeout="15000"/>
  </dubbo:reference>
  <dubbo:reference id="objectGroupService" interface="com.facishare.marketing.api.service.ObjectGroupService" version="${dubbo.provider.version}"/>

  <dubbo:reference check="false" id="userMarketingAccountService" interface="com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService" timeout="10000" version="1.0"/>
  <dubbo:reference check="false" id="userMarketingTagService" interface="com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService" timeout="10000" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.EnterpriseSpreadStatisticService" id="enterpriseSpreadStatisticService" version="${dubbo.provider.version}">
    <dubbo:method name="addMarketingActivityKis" timeout="10000" retries="0"/>
    <dubbo:method name="getClueListByMarketingActivityId" timeout="15000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXContactService"
    id="qywxContactService" version="${dubbo.provider.version}">
    <dubbo:method name="queryAddfanQrCodeList" timeout="30000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.MiniAppProductService" id="miniAppProductService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.MiniAppFeedService" id="miniAppFeedService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXCustomerGroupService" id="qywxCustomerGroupService" timeout="15000" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.CustomizeFormDataService" id="qywxCustomizeFormDataServiceImpl" version="${dubbo.provider.version}">
    <dubbo:method name="customizeFormDataEnroll" timeout="15000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeFormDataService" id="customizeFormDataService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QYWXSettingService" id="qywxSettingService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService" id="qywxSelfBuildAppCallbackService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.GroupSendMessageService" id="groupSendMessageService" version="${dubbo.provider.version}" timeout="60000"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.ArticleService" id="mankeepArticleService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxUserService" id="qywxUserService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.ConferenceService" id="mankeepConferenceService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxStaffService" id="qywxStaffService" version="${dubbo.provider.version}">
    <dubbo:method name="queryQywxStaff" timeout="60000" retries="0"/>
    <dubbo:method name="queryUserCardDetail" timeout="60000" retries="0"/>
    <dubbo:method name="userCardStatistic" timeout="60000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QyWxDepartmentService" id="qyWxDepartmentService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.DistributorService" id="distributorService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.DistributionPlanService" id="distributionPlanService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.DistributePlanGradeService" id="distributePlanGradeService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.OperatorService" id="operatorService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.DistributionService" id="outerDistributionService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.ClueService" id="outerClueService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.GroupSpaceService" id="outerGroupSpaceService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.FileService" id="outerFileService" timeout="60000" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OutImageService" id="outImageService" timeout="60000" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.distribution.MaterielService" id="materielService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.mankeep.api.service.OperatorService" id="outerOperatorService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="customizeTicketService" interface="com.facishare.marketing.api.service.CustomizeTicketService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="wxTicketService" interface="com.facishare.marketing.api.service.WxTicketService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QyweixinAccountBindService" id="qyweixinAccountBindService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="taskCenterService" interface="com.facishare.marketing.api.service.TaskCenterService" version="${dubbo.provider.version}">
    <dubbo:method name="pageTaskList" timeout="60000" retries="0"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.BoardService" id="boardService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.live.LiveService" id="liveService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ContentMarketingEventService" id="contentMarketingEventService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.marketingactivity.MarketingActivityService" id="marketingActivityService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.PrivateMessageService" id="privateMessageService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.StatisticService" id="statisticService" timeout="15000" version="1.0"/>
  <dubbo:reference id="memberService" interface="com.facishare.marketing.api.service.MemberService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.FsBindService" id="fsBindService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAppNavbarService" id="customizeMiniAppNavbarService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAuthorizeService" id="customizeMiniAuthorizeService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeMiniAppCardNavbarService" id="customizeMiniAppCardNavbarService"  protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.CustomizeContentService" id="customizeContentService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.DingMiniAppStaffService" id="dingMiniAppStaffService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.DingMiniAppDepartmentService" id="dingMiniAppDepartmentService" protocol="dubbo" version="1.0"/>
  <dubbo:reference id="wxCouponPayService" interface="com.facishare.marketing.api.service.wxcoupon.WxCouponPayService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="qywxMediaService" interface="com.facishare.marketing.api.service.qywx.QYWXMediaService" version="${dubbo.provider.version}" timeout="60000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.VideoService" id="videoService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.fileLibrary.FileLibraryService" id="fileLibraryService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.QywxMomentService" id="qywxMomentService" version="${dubbo.provider.version}" timeout="60000"/>
  <dubbo:reference id="qywxSopTaskService" interface="com.facishare.marketing.api.service.qywx.QywxSopTaskService" version="${dubbo.provider.version}" timeout="15000"/>
  <dubbo:reference id="openAppAdminService" interface="com.facishare.open.app.center.api.service.OpenAppAdminService" version="1.3"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.pay.FsPayService" id="fsPayService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.ActivityService" id="activityService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.kis.KisPermissionService" id="kisPermissionService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.OutLinkService" id="outLinkService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService" id="photoLibraryService" protocol="dubbo" version="1.0"/>
  <dubbo:reference id="settingService" interface="com.facishare.marketing.api.service.SettingService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.qywx.QywxObjectSendConfigService" id="qywxObjectSendConfigService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference id="materialShowSettingService" interface="com.facishare.marketing.api.service.open.material.MaterialShowSettingService" version="${dubbo.provider.version}"/>
  <dubbo:reference id="dataPermissionService" interface="com.facishare.marketing.api.service.permission.DataPermissionService" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ai.AiChatService" id="aiChatService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ai.ObjectQueryProxyService" id="objectQueryProxyService" protocol="dubbo" version="1.0"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.ObjectSloganRelationService" id="objectSloganRelationService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.digitalHumans.DigitalHumansService" id="digitalHumansService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference interface="com.facishare.marketing.api.service.UserProfileRepositoryService" id="userProfileRepositoryService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference id="providerArticleService" interface="com.facishare.marketing.api.service.ArticleService" version="${dubbo.provider.version}">
    <dubbo:method name="addWebCrawlerArticle" retries="0" timeout="500000"/>
  </dubbo:reference>
  <dubbo:reference interface="com.facishare.marketing.api.service.appMenu.AppMenuTemplateService" id="appMenuTemplateService" protocol="dubbo" version="${dubbo.provider.version}"/>
  <dubbo:reference id="kisCtaService" interface="com.facishare.marketing.api.service.kis.CtaService" protocol="dubbo"  version="${dubbo.provider.version}"/>

  <dubbo:reference interface="com.facishare.marketing.api.service.ImageCaptchaService" id="imageCaptchaService" protocol="dubbo" version="${dubbo.provider.version}"/>

</beans>
