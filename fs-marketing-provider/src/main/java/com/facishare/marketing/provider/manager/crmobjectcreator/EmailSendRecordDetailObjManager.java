package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.mail.QueryMailUserSendDetailResult;
import com.facishare.marketing.api.service.mail.MailService;
import com.facishare.marketing.api.vo.mail.QueryMailUserSendDetailVO;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.bo.email.EmailOpenClickStatisticBO;
import com.facishare.marketing.provider.dao.mail.MailTaskStatisticsDAO;
import com.facishare.marketing.provider.entity.mail.MailTaskStatisticsEntity;
import com.facishare.marketing.provider.innerArg.CreateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.innerArg.UpdateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.lock.LockManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.util.ObjDescribeUtil;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.limit.GuavaLimiter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("emailSendRecordDetailObjManager")
@Slf4j
public class EmailSendRecordDetailObjManager extends AbstractObjManager {

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private MailTaskStatisticsDAO mailTaskStatisticsDAO;

    @Autowired
    private MailService mailService;

    public static final String EMAIL_SEND_RECORD_RATE_LIMIT_KEY = "limit-yxt-email-send-obj";

    private static final String EMAIL_SEND_RECORD_OP_KEY = "mk:mail:o:%s:%s:%s";

    @Override
    public String getApiName() {
        return CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName();
    }

    @Override
    public String getJsonData() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/email_send_record_detail_json_data.json");
    }

    @Override
    public String getJsonLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/email_send_record_detail_json_layout.json");
    }

    @Override
    public String getJsonListLayout() {
        return ObjDescribeUtil.getObjectDescribe("/object_describe/email_send_record_detail_json_list_layout.json");
    }

    @Override
    public ObjectDescribe getOrCreateObjDescribe(String ea) {
        ObjectDescribe describe = super.getOrCreateObjDescribe(ea);
        if (describe != null) {
            tryHideAddImportFormButton(ea, getApiName());
        }
        return describe;
    }

    public String tryCreateOrUpdateObj(CreateEmailSendRecordDetailObjArg arg) {
        String ea = arg.getEa();
        String taskId = arg.getTaskId();
        String receiver = arg.getReceiver();
        if (StringUtils.isBlank(taskId) || StringUtils.isBlank(receiver)) {
            return null;
        }
        receiver = receiver.toLowerCase();
        String key = String.format(EMAIL_SEND_RECORD_OP_KEY, ea, taskId, receiver);
        boolean lock = redisManager.lock(key,10);
        if (!lock) {
            log.warn("email send record try create obj get lock fail, arg: {}", arg);
            UpdateEmailSendRecordDetailObjArg updateArg = BeanUtil.copy(arg, UpdateEmailSendRecordDetailObjArg.class);
            tryUpdateAndAccumulateCountObj(updateArg);
            return null;
        }
        try {
            Map<String, Object> crmObjectData = new HashMap<>();
            // 限流 不能写太快了
            GuavaLimiter.acquire(EMAIL_SEND_RECORD_RATE_LIMIT_KEY, ea);
            crmObjectData.put("click_count", arg.getClickCount() == null ? 0 : arg.getClickCount());
            crmObjectData.put("open_count", arg.getOpenCount() == null ? 0 : arg.getOpenCount());

            if (arg.getIsUnSubscribe() != null) {
                crmObjectData.put("is_unsubscribe", arg.getIsUnSubscribe());
            }

            if (arg.getSpamReport() != null) {
                crmObjectData.put("spam_report", arg.getSpamReport());
            }

            if (StringUtils.isNotBlank(arg.getStatusCode())) {
                crmObjectData.put("status_code", arg.getStatusCode());
            }

            if (arg.getTaskId() != null) {
                crmObjectData.put("task_id", arg.getTaskId());
            }

            if (StringUtils.isNotBlank(arg.getMarketingActivityId())) {
                crmObjectData.put("marketing_activity_id", arg.getMarketingActivityId());
            }

            if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
                crmObjectData.put("marketing_event_id", arg.getMarketingEventId());
            }

            if (StringUtils.isNotBlank(arg.getBusinessType())) {
                crmObjectData.put("business_type", arg.getBusinessType());
            }

            if (StringUtils.isNotBlank(arg.getSendObject())) {
                crmObjectData.put("send_object", arg.getSendObject());
            }

            if (StringUtils.isNotBlank(arg.getSendNode())) {
                crmObjectData.put("send_node", arg.getSendNode());
            }

            if (arg.getSendStatus() != null) {
                crmObjectData.put("send_status", arg.getSendStatus());
            }

            if (StringUtils.isNotBlank(arg.getReceiver())) {
                crmObjectData.put("receiver", arg.getReceiver().toLowerCase());
            }

            if (arg.getSendTime() != null) {
                crmObjectData.put("send_time", arg.getSendTime().getTime());
            }

            if (StringUtils.isNotBlank(arg.getPreviewUrl())) {
                crmObjectData.put("preview_url", arg.getPreviewUrl());
            }

            if (StringUtils.isNotBlank(arg.getFailReason())) {
                crmObjectData.put("fail_reason", arg.getFailReason());
            }

            int owner = arg.getFsUserId() != null ? arg.getFsUserId() : SuperUserConstants.USER_ID;
            crmObjectData.put("owner", Lists.newArrayList(String.valueOf(owner)));
            crmObjectData.put("created_by", Lists.newArrayList(String.valueOf(owner)));

            log.info("创建邮件发送明细,ea:[{}], param:[{}]", ea, crmObjectData);
            Map<String, Object> result = crmMetadataManager.addMetadata(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName(), crmObjectData);
            return result.get("_id").toString();
        } catch (Exception e) {
            log.error("创建邮件发送明细异常, arg: {}", arg, e);
        } finally {
            redisManager.unLock(key);
        }
        return null;
    }

    public void tryUpdateAndAccumulateCountObj(UpdateEmailSendRecordDetailObjArg arg) {
        String ea = arg.getEa();
        String taskId = arg.getTaskId();
        String receiver = arg.getReceiver();
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(receiver) || StringUtils.isBlank(taskId)) {
            log.warn("email send record try update param error, arg: {}", arg);
            return;
        }
        receiver = receiver.toLowerCase();
        String key = String.format(EMAIL_SEND_RECORD_OP_KEY, ea, taskId, receiver);
        boolean lock = lockManager.retryGetLock(key, 50, 10, 200);
        if (!lock) {
            log.warn("email send record try update get lock fail, arg: {}", arg);
            return;
        }
        try {
            ObjectData objectData = getByTaskIdAndReceiver(ea, taskId, receiver);
            if (objectData == null) {
                log.warn("邮件发送明细对象不存在，更新失败, arg: {}", arg);
                return;
            }
            Map<String, Object> crmObjectData = new HashMap<>();
            crmObjectData.put("_id", objectData.getId());

            if (arg.getClickCount() != null) {
                int clickCount = objectData.getInt("click_count") == null ? 0 : objectData.getInt("click_count");
                crmObjectData.put("click_count", arg.getClickCount() + clickCount);
            }

            if (arg.getOpenCount() != null) {
                int openCount = objectData.getInt("open_count") == null ? 0 : objectData.getInt("open_count");
                crmObjectData.put("open_count", arg.getOpenCount() + openCount);
            }

            if (arg.getIsUnSubscribe() != null) {
                crmObjectData.put("is_unsubscribe", arg.getIsUnSubscribe());
            }

            if (arg.getSpamReport() != null) {
                crmObjectData.put("spam_report", arg.getSpamReport());
            }

            if (StringUtils.isNotBlank(arg.getStatusCode())) {
                crmObjectData.put("status_code", arg.getStatusCode());
            }

            if (arg.getSendStatus() != null) {
                crmObjectData.put("send_status", arg.getSendStatus());
            }

            if (StringUtils.isNotBlank(arg.getFailReason())) {
                crmObjectData.put("fail_reason", arg.getFailReason());
            }
            Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName(), crmObjectData);
            log.info("更新邮件发送明细, data: {} result: {}", crmObjectData, result);
        } catch (Exception e) {
            log.error("更新邮件发送明细异常, arg: {}", arg, e);
        } finally {
            lockManager.unlock(key);
        }
    }

    @FilterLog
    public ObjectData getByTaskIdAndReceiver(String ea, String taskId, String receiver) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(taskId) || StringUtils.isBlank(receiver)) {
            return null;
        }
        receiver = receiver.toLowerCase();
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName());

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("task_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(taskId));
        paasQueryArg.addFilter("receiver", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(receiver));

        queryFilterArg.setQuery(paasQueryArg);
        queryFilterArg.setSelectFields(null);

        InnerPage<ObjectData> page = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, 1, 1);
        if (page == null || CollectionUtils.isEmpty(page.getDataList())) {
            return null;
        }
        return page.getDataList().get(0);
    }

    public void updateReceiverFieldDescribe(String ea) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(ei, SuperUserConstants.USER_ID);
        Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(headerObj, this.getApiName());
        if (!getDescribeResultResult.isSuccess() || getDescribeResultResult.getData() == null || getDescribeResultResult.getData().getDescribe() == null) {
            log.warn("updateReceiverFieldDescribe obj describe not exit ea: {}", ea);
            return;
        }
        ObjectDescribe objectDescribe = getDescribeResultResult.getData().getDescribe();
        FieldDescribe fieldDescribe = objectDescribe.getFields().get("receiver");
        fieldDescribe.put("pattern", "");
        fieldDescribe.put("type", "text");
        Result<ControllerGetDescribeResult> result = objectDescribeService.updateField(headerObj, this.getApiName(), "receiver", fieldDescribe);
        log.info("updateReceiverFieldDescribe result : {}", result);
    }

    public Map<String, EmailOpenClickStatisticBO> getOpenAndClickMap(String ea, String taskId, List<String> recipientList) {
        if (CollectionUtils.isEmpty(recipientList)) {
            return Maps.newHashMap();
        }
        recipientList = recipientList.stream().map(String::toLowerCase).collect(Collectors.toList());
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("task_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(taskId));
        paasQueryArg.addFilter("receiver", PaasAndCrmOperatorEnum.IN.getCrmOperator(), recipientList);
        queryFilterArg.setQuery(paasQueryArg);
        queryFilterArg.setSelectFields(Lists.newArrayList("open_count", "click_count", "receiver"));
        InnerPage<ObjectData> page = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, 1, recipientList.size());
        Map<String, EmailOpenClickStatisticBO> result = Maps.newHashMap();
        if (page == null || CollectionUtils.isEmpty(page.getDataList())) {
            return result;
        }
        for (ObjectData objectData : page.getDataList()) {
            EmailOpenClickStatisticBO emailOpenClickStatisticBO = new EmailOpenClickStatisticBO();
            Integer clickCount = objectData.getInt("click_count");
            emailOpenClickStatisticBO.setClick(clickCount == null ? 0 : clickCount);
            Integer openCount = objectData.getInt("open_count");
            emailOpenClickStatisticBO.setOpen(openCount == null ? 0 : openCount);
            emailOpenClickStatisticBO.setEmail(objectData.getString("receiver"));
            result.put(emailOpenClickStatisticBO.getEmail(), emailOpenClickStatisticBO);
        }
        return result;
    }

    public void fixSendStatistic(String param) {
        String[] arr = param.split(",");
        String ea = arr[0];
        String taskIdArrStr = arr[1];
        String onlyCheckStr = arr[2];
        String forceFixStr = arr[3];
        String[] taskIdArr = taskIdArrStr.split(",");
        boolean onlyCheck = Boolean.parseBoolean(onlyCheckStr);
        boolean forceFix = Boolean.parseBoolean(forceFixStr);

        for (String taskId : taskIdArr) {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName());
            queryFilterArg.setSelectFields(Lists.newArrayList("_id", "task_id", "receiver"));
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("task_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(taskId));
            paasQueryArg.addFilter("send_status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("sent"));
            queryFilterArg.setQuery(paasQueryArg);
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            MailTaskStatisticsEntity mailTaskStatisticsEntity = mailTaskStatisticsDAO.getByEaAndTaskId(ea, taskId);
            if (mailTaskStatisticsEntity == null) {
                log.warn("fixSendStatistic mailTaskStatisticsEntity is null ea: {} taskId: {}", ea, taskId);
                continue;
            }
            long deliveredNum = mailTaskStatisticsEntity.getDeliveredNum() == null ? 0L : mailTaskStatisticsEntity.getDeliveredNum();
            QueryMailUserSendDetailVO vo = new QueryMailUserSendDetailVO();
            vo.setEa(ea);
            vo.setFsUserId(SuperUserConstants.USER_ID);
            vo.setTaskId(taskId);
            vo.setStatus(0);
            vo.setPageNum(1);
            vo.setPageSize(1);
            com.facishare.marketing.common.result.Result<PageResult<QueryMailUserSendDetailResult>> result = mailService.queryMailUserSendDetail(vo);
            int sendCloudDetailTotalCount = result.getData().getTotalCount() == null ? 0 : result.getData().getTotalCount();
            log.warn("fixSendStatistic, ea: {} taskId: {} objCount: {} statisticCount: {} sendCloudDetailTotalCount: {}", ea, taskId, totalCount, deliveredNum, sendCloudDetailTotalCount);
            boolean sendCloudCheck = sendCloudDetailTotalCount != deliveredNum;
            if (sendCloudCheck) {
                log.warn("fixSendStatistic sendCloud detail total count and statistic total count not equal, ea: {} taskId: {} objCount: {} sendCloudDetailTotalCount: {} statisticCount: {}", ea, taskId, totalCount, sendCloudDetailTotalCount, deliveredNum);
            }
            if (forceFix || (!onlyCheck && sendCloudCheck)) {
                mailService.manualHandleSingleTaskSendStatus(taskId);
            }
        }


    }

    public void invalidRepeatObj(String param) {
       String[] arr = param.split(",");
       String ea = arr[0];
       String beginTimeStr = arr[1];
       String endTimeStr = arr[2];
       Date beginTime = DateUtil.parse(beginTimeStr);
       Date endTime = DateUtil.parse(endTimeStr);

        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "task_id", "receiver"));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("create_time", PaasAndCrmOperatorEnum.GTE.getCrmOperator(), Lists.newArrayList(String.valueOf(beginTime.getTime())));
        paasQueryArg.addFilter("create_time", PaasAndCrmOperatorEnum.LTE.getCrmOperator(), Lists.newArrayList(String.valueOf(endTime.getTime())));
        queryFilterArg.setQuery(paasQueryArg);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        log.info("邮件发送明细数量 ea: {} param: {} 总数：{}", ea, param, totalCount);
        if (totalCount <= 0) {
            return ;
        }
        int currentCount = 0;
        int pageSize = 500;
        String lastId = null;
        Set<String> alreadyInvalidIdSet = Sets.newHashSet();
        while (currentCount < totalCount) {
            InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
            if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
                return;
            }
            List<ObjectData> objectDataList = innerPage.getDataList();
            currentCount += objectDataList.size();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
            for (List<ObjectData> part : Lists.partition(objectDataList, 100)) {
                Set<String> taskIdSet = Sets.newHashSet();
                Set<String> receiverSet = Sets.newHashSet();
                for (ObjectData objectData : part) {
                    String id = objectData.getId();
                    if (alreadyInvalidIdSet.contains(id)) {
                        continue;
                    }
                    String taskId = objectData.getString("task_id");
                    if (StringUtils.isNotBlank(taskId)) {
                        taskIdSet.add(taskId);
                    }
                    String receiver = objectData.getString("receiver");
                    if (StringUtils.isNotBlank(receiver)) {
                        receiverSet.add(receiver);
                    }
                }

                PaasQueryArg paasQueryArg2 = new PaasQueryArg(0, 1);
                paasQueryArg2.addFilter("task_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(taskIdSet));
                paasQueryArg2.addFilter("receiver", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(receiverSet));
                List<ObjectData> allObj = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName(), Lists.newArrayList("_id", "task_id", "receiver"), paasQueryArg2);
                Map<String, Set<String>> keyToObjIdMap = Maps.newHashMap();
                for (ObjectData objectData : allObj) {
                    String key = objectData.getString("task_id") + "-" + objectData.getString("receiver");
                    keyToObjIdMap.computeIfAbsent(key, k -> Sets.newHashSet()).add(objectData.getId());
                }
                Set<String> invalidIdSet = Sets.newHashSet();
                keyToObjIdMap.values().stream().filter(e -> e.size() > 1).forEach(e -> e.stream().skip(1).forEach(invalidIdSet::add));
                alreadyInvalidIdSet.addAll(invalidIdSet);
                log.info("邮件发送明细数量 ea: {} param: {} 当前处理数量：{} 总数：{} 重复数量：{} 已处理数量：{}", ea, param, currentCount, totalCount, invalidIdSet.size(), alreadyInvalidIdSet.size());
                crmV2Manager.bulkInvalidIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.EMAIL_SEND_RECORD_DETAIL_OBJ.getName(), Lists.newArrayList(invalidIdSet));
            }
        }
    }
}
