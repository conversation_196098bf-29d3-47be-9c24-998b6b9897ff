package com.facishare.marketing.provider.service.qywx;

import com.facishare.mankeep.api.service.FeedService;
import com.facishare.mankeep.api.vo.FeedVO;
import com.facishare.mankeep.api.vo.ListFeedVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.marketing.api.arg.qywx.miniappFeed.AddFeedArg;
import com.facishare.marketing.api.arg.qywx.miniappFeed.FeedListArg;
import com.facishare.marketing.api.arg.qywx.miniappFeed.QueryEnterpriseFeedArg;
import com.facishare.marketing.api.result.EnterpriseFeedResult;
import com.facishare.marketing.api.result.qywx.card.FeedActivityVO;
import com.facishare.marketing.api.result.qywx.card.FeedCommentUnitResult;
import com.facishare.marketing.api.result.qywx.card.FeedDetailResult;
import com.facishare.marketing.api.result.qywx.miniappFeed.AddFeedsResult;
import com.facishare.marketing.api.service.enterpriseFeed.EnterpriseFeedService;
import com.facishare.marketing.api.service.qywx.MiniAppFeedService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.MiniAppConstants;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.google.common.collect.Lists;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service("miniAppFeedServiceImpl")
public class MiniAppFeedServiceImpl implements MiniAppFeedService {

    @Autowired
    private FeedService feedService;

    @Autowired
    private EnterpriseFeedService enterpriseFeedService;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Override
    public Result<PageResult<FeedDetailResult>> listFeeds(FeedListArg arg) {
        if (arg.getTime() == null) {
            arg.setTime(new Date().getTime());
        }
        ListFeedVO vo = BeanUtil.copy(arg, ListFeedVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<PageObject<com.facishare.mankeep.api.result.FeedDetailResult>> feedResult = feedService.listFeeds(vo);
        if (!feedResult.isSuccess()) {
            log.warn("MiniAppFeedServiceImpl.listFeeds error arg:{}, feedResult:{}", arg, feedResult);
            return Result.newError(feedResult.getErrCode(), feedResult.getErrMsg());
        }
        List<FeedDetailResult> feedDetailResults = Lists.newArrayList();
        PageResult<FeedDetailResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setTime(arg.getTime());
        pageResult.setResult(Lists.newArrayList());
        if (feedResult.getData() == null || CollectionUtils.isEmpty(feedResult.getData().getResult())) {
            return Result.newSuccess(pageResult);
        }
        //feedDetailResults = BeanUtil.copy(feedResult.getData().getResult(), FeedDetailResult.class);
        for (com.facishare.mankeep.api.result.FeedDetailResult data : feedResult.getData().getResult()) {
            FeedDetailResult feedDetailResult = BeanUtil.copy(data, FeedDetailResult.class);
            if (CollectionUtils.isNotEmpty(data.getFeedCommentUnitResults())) {
                feedDetailResult.setFeedCommentUnitResults(BeanUtil.copy(data.getFeedCommentUnitResults(), FeedCommentUnitResult.class));
            }
            feedDetailResult.setFeedActivityVO(BeanUtil.copy(data.getFeedActivityVO(), FeedActivityVO.class));
            feedDetailResults.add(feedDetailResult);
        }
        pageResult.setTotalCount(feedResult.getData().getTotalCount());
        pageResult.setResult(feedDetailResults);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<AddFeedsResult> addFeeds(AddFeedArg arg) {
        FeedVO vo = BeanUtil.copy(arg, FeedVO.class);
        if (StringUtils.isNotBlank(arg.getArticleKey())) {
            vo.setFeedKey(arg.getArticleKey());
        }
        if (null != arg.getSyncFeed()) {
            vo.setVisible(arg.getSyncFeed());
        }
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<String> feedResult = feedService.addFeeds(vo);
        if (!feedResult.isSuccess()) {
            return Result.newError(feedResult.getErrCode(), feedResult.getErrMsg());
        }
        AddFeedsResult addFeedsResult = new AddFeedsResult();
        addFeedsResult.setFeedId(feedResult.getData());
        return Result.newSuccess(addFeedsResult);
    }

    @Override
    public Result<PageResult<EnterpriseFeedResult>> queryEnterpriseFeed(QueryEnterpriseFeedArg arg) {
        PageResult<EnterpriseFeedResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        String ea = null;
        Integer fsUserId = null;
        if (StringUtils.isBlank(arg.getCardUid())) {
            Collection<String> eas = wechatAccountManager.listEaByPlatformIdAndWxAppId(MKThirdPlatformConstants.PLATFORM_ID, arg.getAppId());
            if (CollectionUtils.isEmpty(eas)) {
                return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
            }
            ea = eas.stream().findAny().get();
            fsUserId = 1000;
        } else {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getCardUid());
            if (fsBindEntity == null) {
                log.warn("MiniAppFeedServiceImpl.queryEnterpriseFeed fsBindEntity is null arg:{}", arg);
                return Result.newSuccess(pageResult);
            }
            ea = fsBindEntity.getFsEa();
            fsUserId = fsBindEntity.getFsUserId();
        }
        com.facishare.marketing.api.arg.QueryEnterpriseFeedArg vo = new com.facishare.marketing.api.arg.QueryEnterpriseFeedArg();
        vo.setEa(ea);
        vo.setUserId(fsUserId);
        vo.setTime(new Date().getTime());
        vo.setPageNum(arg.getPageNum());
        vo.setPageSize(arg.getPageSize());
        return enterpriseFeedService.queryEnterpriseFeedResult(vo);
    }
}
