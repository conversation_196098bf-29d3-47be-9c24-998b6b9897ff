package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.MiniappReleaseRecordEntity;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MiniappReleaseRecordDao {
    @Insert("INSERT INTO miniapp_release_record(wx_app_id, status, code_template_id, code_version, code_description, end_reason, create_time, update_time) " +
            " VALUES(#{wxAppId}, 1, #{codeTemplateId}, #{codeVersion}, #{codeDescription}, #{endReason}, NOW(), NOW())")
    int commitCodeFail(@Param("wxAppId") String wxAppId, @Param("codeTemplateId") String codeTemplateId, @Param("codeVersion") String codeVersion, @Param("codeDescription") String codeDescription, @Param("endReason") String endReason);

    @Insert("INSERT INTO miniapp_release_record(wx_app_id, status, code_template_id, code_version, code_description, end_reason, submit_audit_time, create_time, update_time) " +
            " VALUES(#{wxAppId}, 2, #{codeTemplateId}, #{codeVersion}, #{codeDescription}, #{endReason}, NOW(), NOW(), NOW())")
    int submitAuditFail(@Param("wxAppId") String wxAppId, @Param("codeTemplateId") String codeTemplateId, @Param("codeVersion") String codeVersion, @Param("codeDescription") String codeDescription, @Param("endReason") String endReason);

    @Update("UPDATE miniapp_release_record SET status=#{status}, audit_id=#{auditId}, end_reason=#{endReason}, update_time=NOW() WHERE id=#{id} AND wx_app_id=#{wxAppId}")
    int updateById(@Param("id")long id, @Param("wxAppId")String wxAppId, @Param("status")int status, @Param("auditId")String auditId, @Param("endReason")String endReason);

    @Insert("INSERT INTO miniapp_release_record(wx_app_id, status, code_template_id, code_version, code_description, audit_id, submit_audit_time, create_time, update_time) " +
            " VALUES(#{wxAppId}, 3, #{codeTemplateId}, #{codeVersion}, #{codeDescription}, #{auditId}, NOW(), NOW(), NOW())")
    int submitAuditSuccess(@Param("wxAppId") String wxAppId, @Param("codeTemplateId") String codeTemplateId, @Param("codeVersion") String codeVersion, @Param("codeDescription") String codeDescription, @Param("auditId") String auditId);

    @Update("UPDATE miniapp_release_record SET status=4, audit_end_time=#{auditEndTime}, update_time=NOW() WHERE wx_app_id=#{wxAppId} AND status=3")
    int auditSuccess(@Param("wxAppId") String wxAppId, @Param("auditEndTime")Date auditEndTime);

    @Update("UPDATE miniapp_release_record SET status=5, audit_end_time=#{auditEndTime}, end_reason=#{endReason}, screen_shots=#{screenShots}, update_time=NOW() WHERE wx_app_id=#{wxAppId} AND status=3")
    int auditFail(@Param("wxAppId") String wxAppId, @Param("endReason") String endReason, @Param("screenShots") String screenShots, @Param("auditEndTime") Date auditEndTime);

    @Update("UPDATE miniapp_release_record SET status=6, end_reason=#{endReason}, update_time=NOW() WHERE wx_app_id=#{wxAppId} AND status=4")
    int relaseSuccess( @Param("wxAppId") String wxAppId, @Param("endReason") String endReason);

    @Delete("DELETE FROM miniapp_release_record WHERE \"id\" = #{id} AND wx_app_id = #{wxAppId} AND status = 3")
    int undoAudit(@Param("id") Integer id, @Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND status=6 ORDER BY update_time DESC LIMIT 1")
    MiniappReleaseRecordEntity getLatestReleaseSuccessRecord(@Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND audit_id != '0' ORDER BY create_time DESC LIMIT 1")
    MiniappReleaseRecordEntity getLatestReleaseRecord(@Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} ORDER BY create_time DESC LIMIT 1")
    MiniappReleaseRecordEntity getLatestReleaseAndAuditingRecord(@Param("wxAppId") String wxAppId);


    @Select("SELECT * FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND status=6 ORDER BY create_time DESC LIMIT 1")
    MiniappReleaseRecordEntity getLatestAlreadyReleasedRecord(@Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND status=6 ORDER BY create_time DESC")
    List<MiniappReleaseRecordEntity> getLatestAlreadyReleasedRecordList(@Param("wxAppId") String wxAppId);


    @Select("SELECT * FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND status=3 and audit_id='0' ORDER BY create_time DESC LIMIT 1")
    MiniappReleaseRecordEntity getLatestConfigReleasedRecord(@Param("wxAppId") String wxAppId);

    @Delete("DELETE FROM miniapp_release_record WHERE  id=#{id}")
    int deleteById(@Param("id") Long id);

    @Delete("DELETE FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND id=#{id}")
    int deleteByIdAndWxAppId(@Param("wxAppId")String wxAppId, @Param("id")long id);

    @Delete("DELETE FROM miniapp_release_record WHERE wx_app_id=#{wxAppId}")
    int deleteByWxAppId(@Param("wxAppId") String wxAppId);

    @Select("<script>"
        + "SELECT wx_app_id, end_reason, status, audit_id FROM ("
        + "SELECT wx_app_id, end_reason, status, audit_id, ROW_NUMBER() OVER(PARTITION BY wx_app_id ORDER BY create_time DESC) AS rid FROM miniapp_release_record "
        + "WHERE wx_app_id IN <foreach open='(' close=')' separator=',' collection='wxAppIds' item='wxAppId'>#{wxAppId}</foreach>) AS t1 "
        + "WHERE rid = 1"
        + "</script>")
    @MapKey("wx_app_id")
    Map<String, Map<String, Object>> batchGetEndReason(@Param("wxAppIds") Collection<String> wxAppIds);

    @Select("<script>"
        + "SELECT wx_app_id, status, \"id\" FROM ("
        + "SELECT wx_app_id, status, \"id\", ROW_NUMBER() OVER(PARTITION BY wx_app_id ORDER BY create_time DESC) AS rid FROM miniapp_release_record "
        + "WHERE wx_app_id IN <foreach open='(' close=')' separator=',' collection='wxAppIds' item='wxAppId'>#{wxAppId}</foreach>) AS t1 "
        + "WHERE rid = 1"
        + "</script>")
    @MapKey("wx_app_id")
    Map<String, Map<String, Integer>> batchGetStatus(@Param("wxAppIds") Collection<String> wxAppIds);

    @Select("SELECT COUNT(*) FROM miniapp_release_record WHERE wx_app_id=#{wxAppId} AND status in(4,6)  AND audit_id != '0'")
    int haveReleaseVersion(@Param("wxAppId")String wxAppId);
}
