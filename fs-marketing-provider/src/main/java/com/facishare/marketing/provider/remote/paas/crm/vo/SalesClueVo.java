package com.facishare.marketing.provider.remote.paas.crm.vo;

import com.facishare.fsi.proxy.model.enterprise.organization.EmployeeSolidEntity;
import com.google.gson.annotations.SerializedName;
import java.io.Serializable;
import lombok.Data;

/**
 * @Auther: dzb
 * @Date: 2018/12/13
 * @Description:
 */
@Data
public class SalesClueVo implements Serializable {
    private static final long serialVersionUID = -881892893577537863L;

    private String ei;
    private String salesClueID;//销售ID
    private String salesCluePoolID;
    private Integer status;
    private String contactWay;
    private String source;//来源
    private String remark;
    private Integer isOverTime;
    private Integer assignerID;
    private String assignerName;
    private Long assignTime;
    private Integer ownerID;
    private String ownerName;
    private String dealResult;
    private Long doneTime;
    private Integer creatorID;
    private String creatorName;
    private Long createTime;
    private Integer updatorID;
    private Long updateTime;
    private String customerID;
    private String salesCluePoolName;
    private String customerName;
    private Integer customerStatus;
    private boolean isUnRead;
    private String circles;
    private EmployeeSolidEntity assigner;
    private EmployeeSolidEntity creator;
    private EmployeeSolidEntity owner;
    private Integer CreatorLeaderID;
    private Integer OwnerLeaderID;
    private String SalesClueName;//姓名
    private String OpportunityID;
    private String OpportunityName;
    private String ContactID;
    private String ContactName;
    private String Company;//公司
    private String ContactWay;//电话
    private String Department;
    private String Mobile;//手机
    private String URL;
    private String Address;
    private String Email;
    private String Position;
    private String MarketingEventName;
    private String MarketingEventID;

}
