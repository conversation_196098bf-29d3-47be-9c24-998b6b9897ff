package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceInviteStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceNotificationTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.arg.conference.SendSmsNotificationArg;
import com.facishare.marketing.outapi.result.GetSignInSettingResult;
import com.facishare.marketing.outapi.service.ConferenceService;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceInviteParticipantDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceSignInJumpSettingDAO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.conference.ConferenceSignInJumpSettingEntity;
import com.facishare.marketing.provider.entity.sms.ExtraSmsParamObject;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by ranluch on 2019/7/26.
 */
@Service("conferenceOutService")
@Slf4j
public class ConferenceOutServiceImpl implements ConferenceService {
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private ConferenceInviteParticipantDAO conferenceInviteParticipantDAO;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private ConferenceSignInJumpSettingDAO conferenceSignInJumpSettingDAO;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Override
    public boolean sendSmsNotification(SendSmsNotificationArg arg) {
        if (StringUtils.isBlank(arg.getActivityId()) || StringUtils.isBlank(arg.getFormDataUserEntityId())) {
            return false;
        }
        ActivityEntity activityEntity = activityDAO.getById(arg.getActivityId());
        if (activityEntity == null) {
            return false;
        }
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getFormDataUserEntityId());
        if (customizeFormDataUserEntity == null || customizeFormDataUserEntity.getSubmitContent() == null || StringUtils.isBlank(customizeFormDataUserEntity.getSubmitContent().getPhone())) {
            log.info("ConferenceOutServiceImpl sendSmsNotification failed, arg={}, customizeFormDataUserEntity={}", arg, customizeFormDataUserEntity);
            return false;
        }

        Map<String, ExtraSmsParamObject> extraSmsParamObjectMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(customizeFormDataUserEntity.getCampaignId())) {
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(customizeFormDataUserEntity.getCampaignId());
            if (campaignMergeDataEntity != null) {
                conferenceManager.buildTicketParam(activityEntity.getId(), activityEntity.getEa(), campaignMergeDataEntity, extraSmsParamObjectMap);
            }
        }
        if (activityEntity.getEnrollReview()) {
            ThreadPoolUtils.execute(() -> {
                conferenceManager.sendConferenceNotification(ConferenceNotificationTypeEnum.ENROLL_AUDIT.getType(), Lists.newArrayList(customizeFormDataUserEntity.getSubmitContent().getPhone()), activityEntity, Maps.newHashMap());
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        } else {
            ThreadPoolUtils.execute(() -> {
                conferenceManager.sendConferenceNotification(ConferenceNotificationTypeEnum.ENROLL_SUCCESS.getType(), Lists.newArrayList(customizeFormDataUserEntity.getSubmitContent().getPhone()), activityEntity, extraSmsParamObjectMap);
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
        return true;
    }

    @Override
    public void sendReviewMessage(String conferenceId, String customizeFormDataUserId) {
        CustomizeFormDataUserEntity customizeFormDataUserEntity =customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataUserId);
        if (customizeFormDataUserEntity != null){
            CustomizeFormDataEnroll submitContent = customizeFormDataUserEntity.getSubmitContent();
            if (submitContent != null){
               if (submitContent.getPhone() != null){
                   conferenceInviteParticipantDAO.updateInviteStatusByPhone(conferenceId, submitContent.getPhone(), ConferenceInviteStatusEnum.SIGN_UP_UN_PARTICIPATN.getStatus());
               }
            }
        }
        conferenceManager.sendConferenceEnrollNoticeRealTime(conferenceId);
    }

    @Override
    public void updateInviteStatusAfterSignIn(String conferenceId, String phone) {
        conferenceManager.updateInviteStatusAfterSignIn(conferenceId, phone);
    }

    @Override
    public String addCampaignMergeDataByUserEnroll(String formDataUserId) {
        return campaignMergeDataManager.addCampaignMergeDataByUserEnroll(formDataUserId, true);
    }

    @Override
    public void updateParticipantsPasscode(String campaignMergeDataById, String code) {
        campaignMergeDataManager.updateParticipantsPasscode(campaignMergeDataById, code);
    }

    @Override
    public void updateSignInStatus(List<String> activityEnrollDataId, Integer signInStatus) {
        campaignMergeDataManager.updateSignInStatus(activityEnrollDataId, signInStatus, true);
    }

    @Override
    public Result<GetSignInSettingResult> getSignInSetting(String activityId) {
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        if (activityEntity == null) {
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        GetSignInSettingResult getSignInSettingResult = new GetSignInSettingResult();
        ConferenceSignInJumpSettingEntity jumpSettingInfo = conferenceSignInJumpSettingDAO.getJumpSettingInfo(activityEntity.getEa(), activityEntity.getId());
        if (jumpSettingInfo == null) {
            // 默认返回跳转会议主页
            getSignInSettingResult.setJumpObjectId(activityId);
            getSignInSettingResult.setJumpObjectType(ObjectTypeEnum.ACTIVITY.getType());
        } else {
            getSignInSettingResult.setJumpObjectType(jumpSettingInfo.getJumpObjectType());
            getSignInSettingResult.setJumpObjectId(jumpSettingInfo.getJumpObjectId());
            getSignInSettingResult.setJumpUrl(jumpSettingInfo.getJumpUrl());
        }
        return Result.newSuccess(getSignInSettingResult);
    }

    @Override
    public Result signInUserByCampaignId(String campaignId,String tagId) {
       return conferenceManager.signInUserByCampaignId(campaignId,tagId);
    }

    @Override
    public void updateSignInStatusAndUid(List<String> activityEnrollDataId, Integer signInStatus, String Uid) {
        campaignMergeDataManager.updateSignInStatusAndUid(activityEnrollDataId, signInStatus,Uid);
    }
}
