package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.outapi.result.BriefProductMsg;
import com.facishare.marketing.outapi.service.ProductService;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.entity.ProductEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("productOutService")
public class ProductServiceImpl implements ProductService {
    @Autowired
    private ProductDAO productDAO;

    @Override
    public Result<BriefProductMsg> getBriefMsgById(String ea, String productId) {
        ProductEntity productEntity = productDAO.getById(productId);
        if(productEntity != null){
            BriefProductMsg briefProductMsg = new BriefProductMsg();
            briefProductMsg.setId(productId);
            briefProductMsg.setName(productEntity.getName());
            briefProductMsg.setTryOutButtonValue(productEntity.getTryOutButtonValue());
            return Result.newSuccess(briefProductMsg);
        }
        return Result.newSuccess(null);
    }
}
