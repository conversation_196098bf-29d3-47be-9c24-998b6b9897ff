package com.facishare.marketing.provider.service;

import com.facishare.mankeep.api.outService.arg.sensors.AddSensorsDataArg;
import com.facishare.mankeep.api.outService.service.OutSensorsService;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.mankeep.common.typehandlers.value.ActionVO;
import com.facishare.marketing.api.result.ListSopTaskResult;
import com.facishare.marketing.api.result.kis.QuerySpreadTaskListResult;
import com.facishare.marketing.api.result.kis.QuerySpreadTaskResult;
import com.facishare.marketing.api.result.taskCenter.TaskDetailResult;
import com.facishare.marketing.api.result.taskCenter.TaskDetailResult.ActivityInvitationDetail;
import com.facishare.marketing.api.result.taskCenter.TaskDetailResult.BoardCardDetail;
import com.facishare.marketing.api.service.TaskCenterService;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.vo.taskCenter.PageTaskListVO;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.BoardCardTaskStatus;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.taskCenter.TaskTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationUserDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceStatisticsDTO;
import com.facishare.marketing.provider.dto.distribution.DistributorStatisticsDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationUserEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.util.ContextUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2020/08/05
 **/
@Service("taskCenterService")
@Slf4j
public class TaskCenterServiceImpl implements TaskCenterService {

    @Autowired
    private OutSensorsService sensorsService;

    @Autowired
    private SpreadTaskManager spreadTaskManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private QYWXContactService qywxContactService;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private OperatorDao operatorDao;

    @Autowired
    private DistributorDao distributorDao;

    @Autowired
    private BoardCardTaskDao boardCardTaskDao;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private ConferenceInvitationUserDAO conferenceInvitationUserDAO;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;

    @Autowired
    private TriggerInstanceDao triggerInstanceDao;
    @Autowired
    private TriggerTaskSnapshotDao triggerTaskSnapshotDao;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Override
    public Result<PageResult<TaskDetailResult>> pageTaskList(PageTaskListVO vo) {
        if (!vo.isPartner()) {
            try {
                AddSensorsDataArg sensorsDataArg = new AddSensorsDataArg();
                List<ActionVO> vos = Lists.newArrayList();
                ActionVO actionVO = new ActionVO();
                actionVO.setId(UUIDUtil.getUUID());
                actionVO.setActionType(ActionTypeEnum.APP_MARKETING_RECORD.getAction());
                actionVO.setObjectType(ObjectTypeEnum.APP_MARKETING.getType());
                actionVO.setObjectName(ObjectTypeEnum.APP_MARKETING.name());
                actionVO.setFsEa(vo.getEa());
                actionVO.setUserId(vo.getUserId());
                actionVO.setActionTime(new Date());
                vos.add(actionVO);
                sensorsDataArg.setVos(vos);
                sensorsService.addSensorsData(sensorsDataArg);

                if (vo.isFromQywx()) {
                    QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(vo.getEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(vo.getEa()));
                    if (qywxMiniappConfigEntity != null) {
                        String uid = fsBindManager.queryUidByFsUserIdAndFsEa(vo.getEa(), vo.getUserId(), AccountTypeEnum.QYWX_MINI_APP.getType(), qywxMiniappConfigEntity.getAppid());
                        if (uid != null) {
                            qywxContactService.setContactConfigByEmployee(uid, vo.getEa(), vo.getUserId());
                        }
                    }
                }
            } catch (Exception e) {
                log.info("SpreadTaskServiceImpl querySpreadTaskList sensorsService addSensorsData, exception: {}", e.fillInStackTrace());
            }
        }

        List<TaskDetailResult> taskDetailResults = Lists.newCopyOnWriteArrayList();

        // 推广任务
        try {
//            Result<QuerySpreadTaskListResult> spreadTaskResult ;
//            if (vo.isPartner() || QywxUserConstants.isPartnerVirtualUserId(vo.getUserId())) {
//                UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(vo.getEa(), vo.getUserId());
//                if (userRelationEntity != null) {
//                    int outUid = userRelationEntity.getOuterUid().intValue();
//                    String outTenantId = String.valueOf(userRelationEntity.getOuterTenantId());
//                    spreadTaskResult = spreadTaskManager.querySpreadTaskList(outTenantId, outUid, vo.getEa());
//                } else {
//                    log.warn("TaskCenterServiceImpl.pageTaskList spreadTaskListFuture userRelationEntity is null, ea:{}, userId:{}", vo.getEa(), vo.getUserId());
//                    spreadTaskResult = Result.newError(SHErrorCode.PARTNER_NOT_EXIST_USER_RELATION);
//                }
//            } else {
//                spreadTaskResult = spreadTaskManager.querySpreadTaskList(vo.getEa(), vo.getUserId(), null);
//            }
            Result<QuerySpreadTaskListResult> spreadTaskResult = spreadTaskManager.querySpreadTaskList(vo.getEa(), vo.getUserId(), null);
            if (spreadTaskResult.isSuccess()) {
                QuerySpreadTaskListResult querySpreadTaskListResult = spreadTaskResult.getData();
                if (CollectionUtils.isNotEmpty(querySpreadTaskListResult.getSpreadedTaskList())) {
                    for (QuerySpreadTaskResult querySpreadTaskResult : querySpreadTaskListResult.getSpreadedTaskList()) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        taskDetailResult.setTaskType(TaskTypeEnum.SPREAD_TASK.getType());
                        taskDetailResult.setQuerySpreadTaskResult(querySpreadTaskResult);
                        taskDetailResult.setCompleteSpreadTask(true);
                        taskDetailResult.setBaseTime(querySpreadTaskResult.getCreateTimestamp());
                        if(Lists.newArrayList(4,6,16,26,13).contains(taskDetailResult.getQuerySpreadTaskResult().getTargetType())){
                            // 获取裁剪封面图
                            String photoTargetId = taskDetailResult.getQuerySpreadTaskResult().getTargetId();
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==26){
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                photoTargetId = homePage.getId();
                            }
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==13){
                                ActivityEntity activity = activityDAO.getById(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activity.getActivityDetailSiteId());
                                photoTargetId = homePage.getId();
                            }
                            List<PhotoTargetTypeEnum> PhotoTargetTypeList = PhotoTargetTypeEnum.getByTypeByObjectType(taskDetailResult.getQuerySpreadTaskResult().getTargetType());
                            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(0).getType(), photoTargetId);
                            if (coverCutMiniAppPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(1).getType(), photoTargetId);
                            if (coverCutH5PhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(2).getType(), photoTargetId);
                            if (coverCutOrdinaryPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                                //返回原图
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                            }
                        }
                        taskDetailResults.add(taskDetailResult);
                    }
                }
                if (CollectionUtils.isNotEmpty(querySpreadTaskListResult.getUnspreadTaskList())) {
                    for (QuerySpreadTaskResult querySpreadTaskResult : querySpreadTaskListResult.getUnspreadTaskList()) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        taskDetailResult.setTaskType(TaskTypeEnum.SPREAD_TASK.getType());
                        taskDetailResult.setQuerySpreadTaskResult(querySpreadTaskResult);
                        taskDetailResult.setCompleteSpreadTask(false);
                        taskDetailResult.setBaseTime(querySpreadTaskResult.getCreateTimestamp());
                        if(Lists.newArrayList(4,6,16,26,13).contains(taskDetailResult.getQuerySpreadTaskResult().getTargetType())){
                            // 获取裁剪封面图
                            String photoTargetId = taskDetailResult.getQuerySpreadTaskResult().getTargetId();
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==26){
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                photoTargetId = homePage.getId();
                            }
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==13){
                                ActivityEntity activity = activityDAO.getById(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activity.getActivityDetailSiteId());
                                photoTargetId = homePage.getId();
                            }
                            List<PhotoTargetTypeEnum> PhotoTargetTypeList = PhotoTargetTypeEnum.getByTypeByObjectType(taskDetailResult.getQuerySpreadTaskResult().getTargetType());
                            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(0).getType(), photoTargetId);
                            if (coverCutMiniAppPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(1).getType(), photoTargetId);
                            if (coverCutH5PhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(2).getType(), photoTargetId);
                            if (coverCutOrdinaryPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                                //返回原图
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                            }
                        }
                        taskDetailResults.add(taskDetailResult);
                    }
                }
                querySpreadTaskListResult = null;
            }
        } catch (Exception e) {
            log.warn("TaskCenterServiceImpl.pageTaskList spreadTaskListFuture error e:{}", e);
        }

        // 会议审核与分销人员
        if(!vo.isPartner()) {
            try {
                // 会议审核
                ConferenceStatisticsDTO conferenceStatisticsDTO = conferenceDAO.getConferenceEnrollStatistics(vo.getEa(), vo.getUserId(), ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus());
                if (conferenceStatisticsDTO != null && conferenceStatisticsDTO.getEnrollTime() != null && conferenceStatisticsDTO.getCount() != null && conferenceStatisticsDTO.getCount() > 0) {
                    TaskDetailResult taskDetailResult = new TaskDetailResult();
                    TaskDetailResult.ConferenceEnrollDetail conferenceEnrollDetail = new TaskDetailResult.ConferenceEnrollDetail();
                    conferenceEnrollDetail.setUncheckEnrollCount(conferenceStatisticsDTO.getCount());
                    conferenceEnrollDetail.setLastEnrollTime(conferenceStatisticsDTO.getEnrollTime().getTime());
                    taskDetailResult.setConferenceEnrollDetail(conferenceEnrollDetail);
                    taskDetailResult.setTaskType(TaskTypeEnum.CONFERENCE_ENROLL_REVIEW.getType());
                    taskDetailResult.setBaseTime(conferenceStatisticsDTO.getEnrollTime().getTime());
                    taskDetailResults.add(taskDetailResult);
                } else {
                    conferenceStatisticsDTO = conferenceDAO.getConferenceEnrollStatistics(vo.getEa(), vo.getUserId(), null);
                    if (conferenceStatisticsDTO != null && conferenceStatisticsDTO.getEnrollTime() != null) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        TaskDetailResult.ConferenceEnrollDetail conferenceEnrollDetail = new TaskDetailResult.ConferenceEnrollDetail();
                        conferenceEnrollDetail.setUncheckEnrollCount(0);
                        conferenceEnrollDetail.setLastEnrollTime(conferenceStatisticsDTO.getEnrollTime().getTime());
                        taskDetailResult.setConferenceEnrollDetail(conferenceEnrollDetail);
                        taskDetailResult.setTaskType(TaskTypeEnum.CONFERENCE_ENROLL_REVIEW.getType());
                        taskDetailResult.setBaseTime(conferenceStatisticsDTO.getEnrollTime().getTime());
                        taskDetailResults.add(taskDetailResult);
                    }
                }

                // 分销审核
                // 查询该用户下所有运营人员身份
                List<Operator> operatorList = operatorDao.getOperatorByFsUidAndFsEa(vo.getEa(), vo.getUserId());
                if (CollectionUtils.isNotEmpty(operatorList)) {
                    // 分别查出所有运营身份下的待审核信息
                    List<DistributorStatisticsDTO> distributorStatisticsDTOList = distributorDao
                            .queryDistributiorStatisticsByOperatorId(operatorList.stream().map(Operator::getId).collect(Collectors.toList()), vo.getEa());
                    if (CollectionUtils.isNotEmpty(distributorStatisticsDTOList)) {
                        distributorStatisticsDTOList.forEach(data -> {
                            if (data.getApplicationTime() != null) {
                                TaskDetailResult taskDetailResult = new TaskDetailResult();
                                TaskDetailResult.DistributorEnrollDetail distributorEnrollDetail = new TaskDetailResult.DistributorEnrollDetail();
                                distributorEnrollDetail.setOperatorId(data.getOperatorId());
                                distributorEnrollDetail.setPlanTitle(data.getPlanTitle());
                                distributorEnrollDetail.setUncheckEnrollCount(data.getCount());
                                distributorEnrollDetail.setLastEnrollTime(data.getApplicationTime().getTime());
                                taskDetailResult.setTaskType(TaskTypeEnum.DISTRIBUTOR_REVIEW.getType());
                                taskDetailResult.setDistributorEnrollDetail(distributorEnrollDetail);
                                taskDetailResult.setBaseTime(data.getApplicationTime().getTime());
                                taskDetailResults.add(taskDetailResult);
                            }
                        });
                    }
                }
                operatorList.clear();
            } catch (Exception e) {
                log.warn("TaskCenterServiceImpl.pageTaskList conferenceEnrollAndDistributorFuture error e:{}", e);
            }
        }

        if (!vo.isPartner()) {
            try {
                List<BoardCardTaskEntity> boardCardTaskEntityList = boardCardTaskDao.queryByEaAndExecutor(vo.getEa(), vo.getUserId());
                if (CollectionUtils.isNotEmpty(boardCardTaskEntityList)) {
                    for (BoardCardTaskEntity boardCardTaskEntity : boardCardTaskEntityList) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        BoardCardDetail boardCardDetail = new BoardCardDetail();
                        boardCardDetail.setId(boardCardTaskEntity.getId());
                        boardCardDetail.setBoardId(boardCardTaskEntity.getBoardId());
                        boardCardDetail.setName(boardCardTaskEntity.getName());
                        boardCardDetail.setBoardCardId(boardCardTaskEntity.getBoardCardId());
                        boardCardDetail.setStartTime(boardCardTaskEntity.getStartTime() != null ? boardCardTaskEntity.getStartTime().getTime() : null);
                        boardCardDetail.setEndTime(boardCardTaskEntity.getEndTime() != null ? boardCardTaskEntity.getEndTime().getTime() : null);
                        boardCardDetail.setStatus(boardCardTaskEntity.getStatus());
                        taskDetailResult.setBoardCardDetail(boardCardDetail);
                        taskDetailResult.setTaskType(TaskTypeEnum.BOARD_TASK.getType());
                        taskDetailResult.setBaseTime(boardCardTaskEntity.getCreateTime().getTime());
                        taskDetailResults.add(taskDetailResult);
                    }
                }
                // 邀约列表
                List<ConferenceInvitationUserEntity> conferenceInvitationUserEntityList = conferenceInvitationUserDAO.queryLatestConferenceInvitationUserByUser(vo.getEa(), vo.getUserId());
                if (CollectionUtils.isNotEmpty(conferenceInvitationUserEntityList)) {
                    List<String> campaignMergeDataIds = conferenceInvitationUserEntityList.stream().map(ConferenceInvitationUserEntity::getCampaignMergeDataId).collect(Collectors.toList());
                    List<CampaignStatisticDTO> campaignStatisticDTOList = campaignMergeDataDAO.queryNotInviteStatisticByCampaignMembersObjId(vo.getEa(), campaignMergeDataIds);
                    Map<String, Integer> invitationMap = campaignStatisticDTOList.stream().collect(Collectors.toMap(CampaignStatisticDTO::getActivityId, CampaignStatisticDTO::getNotInvitedCount, (v1, v2) -> v1));
                    List<ActivityEntity> activityEntityList = activityDAO.getByIds(conferenceInvitationUserEntityList.stream().map(ConferenceInvitationUserEntity::getActivityId).collect(Collectors.toList()));
                    Map<String, String> activityNameMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getId, ActivityEntity::getTitle, (v1, v2) -> v1));
                    Map<String, ConferenceInvitationUserEntity> latestStartTimeMap = conferenceInvitationUserEntityList.stream()
                            .collect(Collectors.toMap(ConferenceInvitationUserEntity::getActivityId, data -> data, (v1, v2) -> v1.getStartTime().getTime() > v2.getStartTime().getTime() ? v1 : v2));
                    for (Map.Entry<String, Integer> entry : invitationMap.entrySet()) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        ActivityInvitationDetail activityInvitationDetail = new ActivityInvitationDetail();
                        String activityName = activityNameMap.get(entry.getKey());
                        if (StringUtils.isBlank(activityName)) {
                            continue;
                        }
                        activityInvitationDetail.setName(activityName);
                        ConferenceInvitationUserEntity conferenceInvitationUserEntity = latestStartTimeMap.get(entry.getKey());
                        if (conferenceInvitationUserEntity == null) {
                            continue;
                        }
                        activityInvitationDetail.setStartTime(conferenceInvitationUserEntity.getStartTime().getTime());
                        activityInvitationDetail.setEndTime(conferenceInvitationUserEntity.getEndTime().getTime());
                        activityInvitationDetail.setActivityId(entry.getKey());
                        Integer invitationCount = invitationMap.get(conferenceInvitationUserEntity.getActivityId());
                        if (invitationCount == null || invitationCount == 0) {
                            activityInvitationDetail.setNeedInviteCount(0);
                        } else {
                            activityInvitationDetail.setNeedInviteCount(invitationCount);
                        }
                        taskDetailResult.setActivityInvitationDetail(activityInvitationDetail);
                        taskDetailResult.setTaskType(TaskTypeEnum.CONFERENCE_INVITATION.getType());
                        taskDetailResult.setBaseTime(conferenceInvitationUserEntity.getCreateTime().getTime());
                        taskDetailResults.add(taskDetailResult);
                    }
                    campaignMergeDataIds.clear();
                    campaignStatisticDTOList.clear();
                    invitationMap.clear();
                    activityEntityList.clear();
                    activityNameMap.clear();
                    latestStartTimeMap.clear();
                }
                boardCardTaskEntityList.clear();
                conferenceInvitationUserEntityList.clear();
            } catch (Exception e) {
                log.warn("TaskCenterServiceImpl.pageTaskList boardCardTaskFuture error e:{}", e);
            }
        }

        PageResult<TaskDetailResult> pageResult = new PageResult<>();
        List<TaskDetailResult> unCompleteTaskList = Lists.newArrayList();
        List<TaskDetailResult> completeTaskList = Lists.newArrayList();
        List<TaskDetailResult> finalResult = Lists.newArrayList();
        pageResult.setResult(finalResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Map<String, String> otherData = Maps.newHashMap();

        // 排序(未完成的推广任务 -> 未审核的人员 -> 未审核分销 -> 已处理信息时间倒序)
        if (CollectionUtils.isEmpty(taskDetailResults)) {
            return Result.newSuccess(pageResult);
        }

        Integer unCompleteTaskNum = 0;
        for (TaskDetailResult taskDetailResult : taskDetailResults) {
            // 先将未完成任务放入
            if (unCompleteTask(taskDetailResult)) {
                unCompleteTaskNum = countUnCompleteTaskNum(taskDetailResult, unCompleteTaskNum);
                unCompleteTaskList.add(taskDetailResult);
            } else {
                completeTaskList.add(taskDetailResult);
            }
        }
        // 将两组时间倒序合并
        unCompleteTaskList.sort(Comparator.comparing(TaskDetailResult::getBaseTime).reversed());
        completeTaskList = completeTaskList.stream().filter(e->e.getBaseTime() != null).sorted(Comparator.comparing(TaskDetailResult::getBaseTime).reversed()).collect(Collectors.toList());
        otherData.put("unCompleteTask", unCompleteTaskNum + "");
        finalResult.addAll(unCompleteTaskList);
        finalResult.addAll(completeTaskList);
        PageUtil<TaskDetailResult> taskDetailResultPage = new PageUtil<>(finalResult, vo.getPageSize());
        pageResult.setTotalCount(finalResult.size());
        finalResult = taskDetailResultPage.getPagedList(vo.getPageNum());
        pageResult.setResult(finalResult);
        pageResult.setOtherData(otherData);
        completeTaskList = null;
        unCompleteTaskList = null;
        return Result.newSuccess(pageResult);
    }

    private boolean unCompleteTask(TaskDetailResult taskDetailResult) {
        return (taskDetailResult.getTaskType().equals(TaskTypeEnum.SPREAD_TASK.getType()) && !taskDetailResult.getCompleteSpreadTask())
            || (taskDetailResult.getTaskType().equals(TaskTypeEnum.CONFERENCE_ENROLL_REVIEW.getType()) && taskDetailResult.getConferenceEnrollDetail().getUncheckEnrollCount() > 0)
            || (taskDetailResult.getTaskType().equals(TaskTypeEnum.DISTRIBUTOR_REVIEW.getType()) && taskDetailResult.getDistributorEnrollDetail().getUncheckEnrollCount() > 0)
            || (taskDetailResult.getTaskType().equals(TaskTypeEnum.SOP.getType()) && taskDetailResult.getListSopTaskResult().getStatus() == 0)
            || (taskDetailResult.getTaskType().equals(TaskTypeEnum.BOARD_TASK.getType()) && taskDetailResult.getBoardCardDetail().getStatus().equals(BoardCardTaskStatus.NOT_START.getStatus()))
            || (taskDetailResult.getTaskType().equals(TaskTypeEnum.CONFERENCE_INVITATION.getType()) && taskDetailResult.getActivityInvitationDetail().getNeedInviteCount() > 0);
    }

    private Integer countUnCompleteTaskNum(TaskDetailResult taskDetailResult, Integer unCompleteTaskNum) {
        if (taskDetailResult.getTaskType().equals(TaskTypeEnum.SPREAD_TASK.getType()) || taskDetailResult.getTaskType().equals(TaskTypeEnum.BOARD_TASK.getType())|| taskDetailResult.getTaskType().equals(TaskTypeEnum.SOP.getType())) {
            unCompleteTaskNum = unCompleteTaskNum + 1;
        } else if (taskDetailResult.getTaskType().equals(TaskTypeEnum.CONFERENCE_ENROLL_REVIEW.getType())) {
            unCompleteTaskNum = unCompleteTaskNum + taskDetailResult.getConferenceEnrollDetail().getUncheckEnrollCount();
        } else if (taskDetailResult.getTaskType().equals(TaskTypeEnum.DISTRIBUTOR_REVIEW.getType())) {
            unCompleteTaskNum = unCompleteTaskNum + taskDetailResult.getDistributorEnrollDetail().getUncheckEnrollCount();
        } else if (taskDetailResult.getTaskType().equals(TaskTypeEnum.CONFERENCE_INVITATION.getType())) {
            unCompleteTaskNum = unCompleteTaskNum + taskDetailResult.getActivityInvitationDetail().getNeedInviteCount();
        }
        return unCompleteTaskNum;
    }

    @Override
    public Result<PageResult<TaskDetailResult>> pageSpreadTaskList(PageTaskListVO vo) {
        ThreadPoolUtils.execute(() -> {
            if (!vo.isPartner()) {
                try {
                    AddSensorsDataArg sensorsDataArg = new AddSensorsDataArg();
                    List<ActionVO> vos = Lists.newArrayList();
                    ActionVO actionVO = new ActionVO();
                    actionVO.setId(UUIDUtil.getUUID());
                    actionVO.setActionType(ActionTypeEnum.APP_MARKETING_RECORD.getAction());
                    actionVO.setObjectType(ObjectTypeEnum.APP_MARKETING.getType());
                    actionVO.setObjectName(ObjectTypeEnum.APP_MARKETING.name());
                    actionVO.setFsEa(vo.getEa());
                    actionVO.setUserId(vo.getUserId());
                    actionVO.setActionTime(new Date());
                    vos.add(actionVO);
                    sensorsDataArg.setVos(vos);
                    sensorsService.addSensorsData(sensorsDataArg);

                    if (vo.isFromQywx()) {
                        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(vo.getEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(vo.getEa()));
                        if (qywxMiniappConfigEntity != null) {
                            String uid = fsBindManager.queryUidByFsUserIdAndFsEa(vo.getEa(), vo.getUserId(), AccountTypeEnum.QYWX_MINI_APP.getType(), qywxMiniappConfigEntity.getAppid());
                            if (uid != null) {
                                qywxContactService.setContactConfigByEmployee(uid, vo.getEa(), vo.getUserId());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.info("SpreadTaskServiceImpl querySpreadTaskList sensorsService addSensorsData, exception: {}", e.fillInStackTrace());
                }
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        List<TaskDetailResult> taskDetailResults = Lists.newCopyOnWriteArrayList();
        TraceContext context = TraceContext.get();
        String traceId = context.getTraceId();
        // 推广任务
        CompletableFuture<Void> spreadTaskListFuture = CompletableFuture.runAsync(() -> {
            TraceContext currentContext = TraceContext.get();
            currentContext.setTraceId(traceId);
            if (StringUtils.isEmpty(vo.getExternalUserId()) || null == vo.getChatType()) {
                return;
            }
            String userId = vo.getQyUserId();
            List<ListSopTaskResult> listSopTaskResults = null;
            //客户
            if (null != vo.getChatType() && 1 == vo.getChatType()) {
                UserMarketingWxWorkExternalUserRelationEntity entity = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(vo.getEa(), vo.getExternalUserId());
                String userMarketingId = entity.getUserMarketingId();
                listSopTaskResults = triggerInstanceDao.querySingleChatSopTask(vo.getEa(), userMarketingId, userId, System.currentTimeMillis());
            }
            //群聊
            if (null != vo.getChatType() && 2 == vo.getChatType()) {
                listSopTaskResults = triggerInstanceDao.queryGroupChatSopTask(vo.getEa(),userId,vo.getExternalUserId(),System.currentTimeMillis());
            }

            if (CollectionUtils.isNotEmpty(listSopTaskResults)) {
                for (ListSopTaskResult listSopTaskResult : listSopTaskResults) {
                    TriggerTaskSnapshotEntity taskSnapshotEntity = triggerTaskSnapshotDao.getById(listSopTaskResult.getTriggerTaskSnapshotId());
                    listSopTaskResult.setContent(taskSnapshotEntity.getMessageContent());
                    listSopTaskResult.setQywxMessageContent(taskSnapshotEntity.getWxMessageContent());
                }
                List<ListSopTaskResult> finishList = listSopTaskResults.stream().filter(l -> l.getStatus() == 1).collect(Collectors.toList());
                List<ListSopTaskResult> unFinishList = listSopTaskResults.stream().filter(l -> l.getStatus() == 0).collect(Collectors.toList());
                //去掉已完成的企微SOP任务 yxt-9.9
//                for (ListSopTaskResult res : finishList) {
//                    TaskDetailResult taskDetailResult = new TaskDetailResult();
//                    taskDetailResult.setTaskType(TaskTypeEnum.SOP.getType());
//                    taskDetailResult.setListSopTaskResult(res);
//                    taskDetailResult.setCompleteSpreadTask(true);
//                    taskDetailResult.setBaseTime(res.getCreateTime().getTime());
//                    taskDetailResults.add(taskDetailResult);
//                }

                for (ListSopTaskResult res : unFinishList) {
                    TaskDetailResult taskDetailResult = new TaskDetailResult();
                    taskDetailResult.setTaskType(TaskTypeEnum.SOP.getType());
                    taskDetailResult.setListSopTaskResult(res);
                    taskDetailResult.setCompleteSpreadTask(false);
                    taskDetailResult.setBaseTime(res.getCreateTime().getTime());
                    taskDetailResults.add(taskDetailResult);
                }
            }
        }, ThreadPoolUtils.getService(ThreadPoolTypeEnums.LIGHT_BUSINESS)).exceptionally(e -> {
            log.warn("TaskCenterServiceImpl spread task error e:{}", e);
            return null;
        }).thenRunAsync(()-> {
            TraceContext currentContext = TraceContext.get();
            currentContext.setTraceId(traceId);
            Result<QuerySpreadTaskListResult> spreadTaskResult ;
            if (vo.isPartner()) {
                Integer outUid = null != vo.getOuterUid() ? Integer.valueOf(vo.getOuterUid()) : null;
                spreadTaskResult = spreadTaskManager.querySpreadTaskList(vo.getOuterTenantId(), outUid, vo.getUpstreamEa());
            } else {
                spreadTaskResult = spreadTaskManager.querySpreadTaskList(vo.getEa(), vo.getUserId(), null);
            }
            if (spreadTaskResult.isSuccess()) {
                QuerySpreadTaskListResult querySpreadTaskListResult = spreadTaskResult.getData();
                if (CollectionUtils.isNotEmpty(querySpreadTaskListResult.getSpreadedTaskList())) {
                    for (QuerySpreadTaskResult querySpreadTaskResult : querySpreadTaskListResult.getSpreadedTaskList()) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        taskDetailResult.setTaskType(TaskTypeEnum.SPREAD_TASK.getType());
                        taskDetailResult.setQuerySpreadTaskResult(querySpreadTaskResult);
                        taskDetailResult.setCompleteSpreadTask(true);
                        taskDetailResult.setBaseTime(querySpreadTaskResult.getCreateTimestamp());
                        if(Lists.newArrayList(4,6,16,26,13).contains(taskDetailResult.getQuerySpreadTaskResult().getTargetType())){
                            // 获取裁剪封面图
                            String photoTargetId = taskDetailResult.getQuerySpreadTaskResult().getTargetId();
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==26){
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                photoTargetId = homePage.getId();
                            }
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==13){
                                ActivityEntity activity = activityDAO.getById(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activity.getActivityDetailSiteId());
                                photoTargetId = homePage.getId();
                            }
                            List<PhotoTargetTypeEnum> PhotoTargetTypeList = PhotoTargetTypeEnum.getByTypeByObjectType(taskDetailResult.getQuerySpreadTaskResult().getTargetType());
                            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(0).getType(), photoTargetId);
                            if (coverCutMiniAppPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(1).getType(), photoTargetId);
                            if (coverCutH5PhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(2).getType(), photoTargetId);
                            if (coverCutOrdinaryPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                                //返回原图
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                            }
                        }
                        taskDetailResults.add(taskDetailResult);
                    }
                }
                if (CollectionUtils.isNotEmpty(querySpreadTaskListResult.getUnspreadTaskList())) {
                    for (QuerySpreadTaskResult querySpreadTaskResult : querySpreadTaskListResult.getUnspreadTaskList()) {
                        TaskDetailResult taskDetailResult = new TaskDetailResult();
                        taskDetailResult.setTaskType(TaskTypeEnum.SPREAD_TASK.getType());
                        taskDetailResult.setQuerySpreadTaskResult(querySpreadTaskResult);
                        taskDetailResult.setCompleteSpreadTask(false);
                        taskDetailResult.setBaseTime(querySpreadTaskResult.getCreateTimestamp());
                        if(Lists.newArrayList(4,6,16,26,13).contains(taskDetailResult.getQuerySpreadTaskResult().getTargetType())){
                            // 获取裁剪封面图
                            String photoTargetId = taskDetailResult.getQuerySpreadTaskResult().getTargetId();
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==26){
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                photoTargetId = homePage.getId();
                            }
                            if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==13){
                                ActivityEntity activity = activityDAO.getById(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(activity.getActivityDetailSiteId());
                                photoTargetId = homePage.getId();
                            }
                            List<PhotoTargetTypeEnum> PhotoTargetTypeList = PhotoTargetTypeEnum.getByTypeByObjectType(taskDetailResult.getQuerySpreadTaskResult().getTargetType());
                            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(0).getType(), photoTargetId);
                            if (coverCutMiniAppPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(1).getType(), photoTargetId);
                            if (coverCutH5PhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                            }
                            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(2).getType(), photoTargetId);
                            if (coverCutOrdinaryPhotoEntity != null) {
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                                //返回原图
                                taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                            }
                        }
                        taskDetailResults.add(taskDetailResult);
                    }
                }
                querySpreadTaskListResult = null;
            }

        },ThreadPoolUtils.getService(ThreadPoolTypeEnums.LIGHT_BUSINESS)).exceptionally(e -> {
            log.warn("TaskCenterServiceImpl sop task error e:{}", e);
            return null;
        });

        spreadTaskListFuture.join();
        PageResult<TaskDetailResult> pageResult = new PageResult<>();
        List<TaskDetailResult> unCompleteTaskList = Lists.newArrayList();
        List<TaskDetailResult> completeTaskList = Lists.newArrayList();
        List<TaskDetailResult> finalResult = Lists.newArrayList();
        pageResult.setResult(finalResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Map<String, String> otherData = Maps.newHashMap();

        // 排序(未完成的推广任务 -> 未审核的人员 -> 未审核分销 -> 已处理信息时间倒序)
        if (CollectionUtils.isEmpty(taskDetailResults)) {
            return Result.newSuccess(pageResult);
        }

        Integer unCompleteTaskNum = 0;
        for (TaskDetailResult taskDetailResult : taskDetailResults) {
            // 先将未完成任务放入
            if (unCompleteTask(taskDetailResult)) {
                unCompleteTaskNum = countUnCompleteTaskNum(taskDetailResult, unCompleteTaskNum);
                unCompleteTaskList.add(taskDetailResult);
            } else {
                completeTaskList.add(taskDetailResult);
            }
        }
        // 将两组时间倒序合并
        unCompleteTaskList.stream().filter(e->e.getBaseTime() != null).collect(Collectors.toList()).sort(Comparator.comparing(TaskDetailResult::getBaseTime).reversed());
        completeTaskList.stream().filter(e->e.getBaseTime() != null).collect(Collectors.toList()).sort(Comparator.comparing(TaskDetailResult::getBaseTime).reversed());
        otherData.put("unCompleteTask", unCompleteTaskNum + "");
        finalResult.addAll(unCompleteTaskList);
        finalResult.addAll(completeTaskList);
        PageUtil<TaskDetailResult> taskDetailResultPage = new PageUtil<>(finalResult, vo.getPageSize());
        pageResult.setTotalCount(finalResult.size());
        finalResult = taskDetailResultPage.getPagedList(vo.getPageNum());
        pageResult.setResult(finalResult);
        pageResult.setOtherData(otherData);
        completeTaskList = null;
        unCompleteTaskList = null;
        return Result.newSuccess(pageResult);
    }

    public Result<PageResult<TaskDetailResult>> pageSpreadTaskListV2(PageTaskListVO vo) {
        try {
            // 1. 并行执行数据采集和任务处理
            if(!vo.isPartner()) {
                ThreadPoolUtils.execute(() -> {
                    collectSensorsData(vo);
                    if (vo.isFromQywx()) {
                        handleQywxConfig(vo);
                    }
                }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            }

            List<TaskDetailResult> taskDetailResults = processAllTasks(vo);
            return processTaskResults(taskDetailResults, vo);
        } catch (Exception e) {
            log.error("pageSpreadTaskListV2 execution error, vo: {}", vo, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    public Result<PageResult<TaskDetailResult>> pageSpreadTaskListV3(PageTaskListVO vo) {
        try {
            // 1. 并行执行数据采集和任务处理
            CompletableFuture<Void> sensorsFuture = null;
            if (!vo.isPartner()) {
                sensorsFuture = CompletableFuture.runAsync(() -> {
                            collectSensorsData(vo);
                            if (vo.isFromQywx()) {
                                handleQywxConfig(vo);
                            }
                        },
                        ThreadPoolUtils.getService(ThreadPoolTypeEnums.MEDIUM_BUSINESS)
                );
            }

            // 2. 任务处理
            CompletableFuture<List<TaskDetailResult>> tasksFuture = CompletableFuture.supplyAsync(() ->
                            processAllTasks(vo),
                    ThreadPoolUtils.getService(ThreadPoolTypeEnums.LIGHT_BUSINESS)
            );

            // 3. 等待所有任务完成
            if (sensorsFuture != null) {
                CompletableFuture.allOf(sensorsFuture, tasksFuture)
                        .get(30, TimeUnit.SECONDS);
            } else {
                tasksFuture.get(30, TimeUnit.SECONDS);
            }

            // 4. 获取结果并处理
            List<TaskDetailResult> taskDetailResults = tasksFuture.get();
            return processTaskResults(taskDetailResults, vo);
        } catch (TimeoutException e) {
            log.error("pageSpreadTaskList execution timeout, vo: {}", vo, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        } catch (Exception e) {
            log.error("pageSpreadTaskList execution error, vo: {}", vo, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    /**
     * 处理数据采集
     */
    private void collectSensorsData(PageTaskListVO vo) {
        try {
            AddSensorsDataArg sensorsDataArg = new AddSensorsDataArg();
            List<ActionVO> vos = Lists.newArrayList();
            ActionVO actionVO = new ActionVO();
            actionVO.setId(UUIDUtil.getUUID());
            actionVO.setActionType(ActionTypeEnum.APP_MARKETING_RECORD.getAction());
            actionVO.setObjectType(ObjectTypeEnum.APP_MARKETING.getType());
            actionVO.setObjectName(ObjectTypeEnum.APP_MARKETING.name());
            actionVO.setFsEa(vo.getEa());
            actionVO.setUserId(vo.getUserId());
            actionVO.setActionTime(new Date());
            vos.add(actionVO);
            sensorsDataArg.setVos(vos);
            sensorsService.addSensorsData(sensorsDataArg);
        } catch (Exception e) {
            log.error("collectSensorsData error, vo: {}", vo, e);
        }
    }

    /**
     * 处理企业微信配置
     */
    private void handleQywxConfig(PageTaskListVO vo) {
        try {
            QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(
                    vo.getEa(),
                    wechatAccountManager.getNotEmptyWxAppIdByEa(vo.getEa())
            );
            if (qywxMiniappConfigEntity != null) {
                String uid = fsBindManager.queryUidByFsUserIdAndFsEa(
                        vo.getEa(),
                        vo.getUserId(),
                        AccountTypeEnum.QYWX_MINI_APP.getType(),
                        qywxMiniappConfigEntity.getAppid()
                );
                if (uid != null) {
                    qywxContactService.setContactConfigByEmployee(uid, vo.getEa(), vo.getUserId());
                }
            }
        } catch (Exception e) {
            log.error("handleQywxConfig error, vo: {}", vo, e);
        }
    }

    /**
     * 处理所有任务
     */
    private List<TaskDetailResult> processAllTasks(PageTaskListVO vo) {
        List<TaskDetailResult> taskDetailResults = Collections.synchronizedList(new ArrayList<>());

        // 1. 处理SOP任务
        processSopTasks(vo, taskDetailResults);

        // 2. 处理推广任务
        processSpreadTasks(vo, taskDetailResults);

        return taskDetailResults;
    }

    /**
     * 处理SOP任务
     */
    private void processSopTasks(PageTaskListVO vo, List<TaskDetailResult> taskDetailResults) {
        try {
            if (StringUtils.isEmpty(vo.getExternalUserId()) || null == vo.getChatType()) {
                return;
            }

            String userId = vo.getQyUserId();
            List<ListSopTaskResult> listSopTaskResults = null;

            // 处理客户聊天
            if (vo.getChatType() == 1) {
                UserMarketingWxWorkExternalUserRelationEntity entity =
                        userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(
                                vo.getEa(),
                                vo.getExternalUserId()
                        );
                if (entity != null) {
                    String userMarketingId = entity.getUserMarketingId();
                    listSopTaskResults = triggerInstanceDao.querySingleChatSopTask(
                            vo.getEa(),
                            userMarketingId,
                            userId,
                            System.currentTimeMillis()
                    );
                }
            }
            // 处理群聊
            else if (vo.getChatType() == 2) {
                listSopTaskResults = triggerInstanceDao.queryGroupChatSopTask(
                        vo.getEa(),
                        userId,
                        vo.getExternalUserId(),
                        System.currentTimeMillis()
                );
            }

            if (CollectionUtils.isNotEmpty(listSopTaskResults)) {
                enrichSopTasksWithSnapshot(vo.getEa(),  listSopTaskResults);
                convertSopTasksToDetailResults(listSopTaskResults, taskDetailResults);
            }
        } catch (Exception e) {
            log.error("processSopTasks error, vo: {}", vo, e);
        }
    }

    /**
     * 处理推广任务
     */
    private void processSpreadTasks(PageTaskListVO vo, List<TaskDetailResult> taskDetailResults) {
        try {
            Result<QuerySpreadTaskListResult> spreadTaskResult;
            if (vo.isPartner()) {
                Integer outUid = null != vo.getOuterUid() ? Integer.valueOf(vo.getOuterUid()) : null;
                spreadTaskResult = spreadTaskManager.querySpreadTaskList(
                        vo.getOuterTenantId(),
                        outUid,
                        vo.getUpstreamEa()
                );
            } else {
                spreadTaskResult = spreadTaskManager.querySpreadTaskList(
                        vo.getEa(),
                        vo.getUserId(),
                        null
                );
            }

            if (spreadTaskResult.isSuccess()) {
                processSpreadTaskResult(vo, spreadTaskResult.getData(), taskDetailResults);
            }
        } catch (Exception e) {
            log.error("processSpreadTasks error, vo: {}", vo, e);
        }
    }

    /**
     * 处理任务结果
     */
    private Result<PageResult<TaskDetailResult>> processTaskResults(List<TaskDetailResult> taskDetailResults, PageTaskListVO vo) {
        PageResult<TaskDetailResult> pageResult = new PageResult<>();
        List<TaskDetailResult> finalResult = Lists.newArrayList();
        pageResult.setResult(finalResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Map<String, String> otherData = Maps.newHashMap();
        pageResult.setOtherData(otherData);
        if (CollectionUtils.isEmpty(taskDetailResults)) {
            return Result.newSuccess(pageResult);
        }

        List<TaskDetailResult> unCompleteTaskList = Lists.newArrayList();
        List<TaskDetailResult> completeTaskList = Lists.newArrayList();

        // 统计未完成任务
        Integer unCompleteTaskNum = 0;
        for (TaskDetailResult taskDetailResult : taskDetailResults) {
            if (unCompleteTask(taskDetailResult)) {
                unCompleteTaskNum = countUnCompleteTaskNum(taskDetailResult, unCompleteTaskNum);
                unCompleteTaskList.add(taskDetailResult);
            } else {
                completeTaskList.add(taskDetailResult);
            }
        }

        // 排序并合并结果
        unCompleteTaskList.sort(Comparator.comparing(TaskDetailResult::getBaseTime).reversed());
        completeTaskList = completeTaskList.stream()
                .filter(e -> e.getBaseTime() != null)
                .sorted(Comparator.comparing(TaskDetailResult::getBaseTime).reversed())
                .collect(Collectors.toList());

        finalResult.addAll(unCompleteTaskList);
        finalResult.addAll(completeTaskList);
        otherData.put("unCompleteTask", String.valueOf(unCompleteTaskNum));
        // 设置分页结果
        return Result.newSuccess(pageResult);
    }

    /**
     * 丰富SOP任务的快照信息
     * @param listSopTaskResults SOP任务列表
     */
    private void enrichSopTasksWithSnapshot(String ea, List<ListSopTaskResult> listSopTaskResults) {
        if (CollectionUtils.isEmpty(listSopTaskResults)) {
            return;
        }

        // 收集所有快照ID
        List<String> snapshotIds = listSopTaskResults.stream()
                .map(ListSopTaskResult::getTriggerTaskSnapshotId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(snapshotIds)) {
            return;
        }

        // 批量查询快照信息
        List<TriggerTaskSnapshotEntity> snapshotEntities = triggerTaskSnapshotDao.getBySnapshotIds(ea, snapshotIds);
        if (CollectionUtils.isEmpty(snapshotEntities)) {
            return;
        }

        // 构建快照ID到实体的映射
        Map<String, TriggerTaskSnapshotEntity> snapshotMap = snapshotEntities.stream()
                .collect(Collectors.toMap(TriggerTaskSnapshotEntity::getId, entity -> entity, (v1, v2) -> v1));

        // 为每个任务设置快照信息
        listSopTaskResults.forEach(task -> {
            if (StringUtils.isNotBlank(task.getTriggerTaskSnapshotId())) {
                TriggerTaskSnapshotEntity snapshot = snapshotMap.get(task.getTriggerTaskSnapshotId());
                if (snapshot != null) {
                    task.setContent(snapshot.getMessageContent());
                    task.setQywxMessageContent(snapshot.getWxMessageContent());
                }
            }
        });
    }

    /**
     * 将SOP任务转换为任务详情结果
     * @param listSopTaskResults SOP任务列表
     * @param taskDetailResults 任务详情结果列表
     */
    private void convertSopTasksToDetailResults(List<ListSopTaskResult> listSopTaskResults, List<TaskDetailResult> taskDetailResults) {
        if (CollectionUtils.isEmpty(listSopTaskResults)) {
            return;
        }

        List<ListSopTaskResult> unFinishList = listSopTaskResults.stream().filter(l -> l.getStatus() == 0).collect(Collectors.toList());
        unFinishList.forEach(sopTask -> {
            TaskDetailResult taskDetailResult = new TaskDetailResult();
            taskDetailResult.setTaskType(TaskTypeEnum.SOP.getType());
            taskDetailResult.setListSopTaskResult(sopTask);
            taskDetailResult.setCompleteSpreadTask(false);
            taskDetailResult.setBaseTime(sopTask.getCreateTime().getTime());
            taskDetailResults.add(taskDetailResult);
        });

        //去掉已完成的企微SOP任务 yxt-9.9
//        List<ListSopTaskResult> finishList = listSopTaskResults.stream().filter(l -> l.getStatus() == 1).collect(Collectors.toList());
//        finishList.forEach(sopTask -> {
//            TaskDetailResult taskDetailResult = new TaskDetailResult();
//            taskDetailResult.setTaskType(TaskTypeEnum.SOP.getType());
//            taskDetailResult.setListSopTaskResult(sopTask);
//            taskDetailResult.setCompleteSpreadTask(true);
//            taskDetailResult.setBaseTime(sopTask.getCreateTime().getTime());
//            taskDetailResults.add(taskDetailResult);
//        });
    }

    /**
     * 处理推广任务结果
     * @param querySpreadTaskListResult 推广任务列表结果
     * @param taskDetailResults 任务详情结果列表
     */
    private void processSpreadTaskResult(PageTaskListVO vo, QuerySpreadTaskListResult querySpreadTaskListResult, List<TaskDetailResult> taskDetailResults) {
        if (querySpreadTaskListResult == null) {
            return;
        }

        // 处理已完成的推广任务
        processSpreadTaskList(vo, querySpreadTaskListResult.getSpreadedTaskList(), true, taskDetailResults);

        // 处理未完成的推广任务
        processSpreadTaskList(vo, querySpreadTaskListResult.getUnspreadTaskList(), false, taskDetailResults);
    }

    private void processSpreadTaskList(PageTaskListVO vo, List<QuerySpreadTaskResult> taskList, boolean isCompleted, List<TaskDetailResult> taskDetailResults) {
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        Set<String> homePageIds = taskList.stream()
                .filter(task -> task.getTargetType() == 26).map(QuerySpreadTaskResult::getTargetId)
                .collect(Collectors.toSet());

        Map<String, ActivityEntity> activityEntityMap = Maps.newHashMap();
        List<QuerySpreadTaskResult> targetType13List = taskList.stream()
                .filter(task -> task.getTargetType() == 13)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(targetType13List)) {
            List<String> activityIds = targetType13List.stream().map(QuerySpreadTaskResult::getTargetId).collect(Collectors.toList());
            List<ActivityEntity> activityEntities = activityDAO.getByIds(activityIds);
            if(CollectionUtils.isNotEmpty(activityEntities)) {
                List<String> siteIds = activityEntities.stream().map(ActivityEntity::getActivityDetailSiteId).collect(Collectors.toList());
                homePageIds.addAll(siteIds);
                activityEntityMap = activityEntities.stream()
                        .collect(Collectors.toMap(ActivityEntity::getId, entity -> entity, (v1, v2) -> v1));
            }
        }
        List<HexagonPageEntity> homePageEntities = hexagonPageDAO.getHomePageByIds(vo.getEa(), Lists.newArrayList(homePageIds));
        // 构建HomePageID到实体的映射
        Map<String, HexagonPageEntity> hexagonPageEntityMap = homePageEntities.stream()
                .collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, entity -> entity, (v1, v2) -> v1));

        List<Integer> needProcessTargetTypeList = Lists.newArrayList(4,6,16,26,13);
        List<Integer> photoTargetTypeList = Lists.newArrayList();
        needProcessTargetTypeList.forEach(photoTargetType -> photoTargetTypeList.addAll(PhotoTargetTypeEnum.getByTypeByObjectType(photoTargetType).stream().map(PhotoTargetTypeEnum::getType).collect(Collectors.toList())));
        Map<String, Map<Integer, PhotoEntity>> photoEntityMap = photoManager.batchQueryPhotoByTypesAndIds(photoTargetTypeList, Lists.newArrayList(homePageIds));
        Map<String, ActivityEntity> finalAcitivityEntityMap = activityEntityMap;
        taskList.forEach(task -> {
            TaskDetailResult taskDetailResult = new TaskDetailResult();
            taskDetailResult.setTaskType(TaskTypeEnum.SPREAD_TASK.getType());
            taskDetailResult.setQuerySpreadTaskResult(task);
            taskDetailResult.setCompleteSpreadTask(isCompleted);
            taskDetailResult.setBaseTime(task.getCreateTimestamp());
            if(needProcessTargetTypeList.contains(task.getTargetType())) {
                // 获取裁剪封面图
                String photoTargetId = taskDetailResult.getQuerySpreadTaskResult().getTargetId();
                if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==26){
                    HexagonPageEntity homePage = hexagonPageEntityMap.get(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                    if(homePage != null){
                        photoTargetId = homePage.getId();
                    }
                }
                if(taskDetailResult.getQuerySpreadTaskResult().getTargetType()==13){
                    ActivityEntity activity = finalAcitivityEntityMap.get(taskDetailResult.getQuerySpreadTaskResult().getTargetId());
                    if(activity != null){
                        HexagonPageEntity homePage = hexagonPageEntityMap.get(activity.getActivityDetailSiteId());
                        if(homePage != null){
                            photoTargetId = homePage.getId();
                        }
                    }
                }

                List<PhotoTargetTypeEnum> photoTargetTypes = PhotoTargetTypeEnum.getByTypeByObjectType(taskDetailResult.getQuerySpreadTaskResult().getTargetType());
                Map<Integer, PhotoEntity> dataMap = photoEntityMap.get(photoTargetId);
                if(dataMap != null){
                    PhotoEntity coverCutMiniAppPhotoEntity = dataMap.get(photoTargetTypes.get(0).getType());
                    if (coverCutMiniAppPhotoEntity != null) {
                        taskDetailResult.getQuerySpreadTaskResult().setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                    }
                    PhotoEntity coverCutH5PhotoEntity = dataMap.get(photoTargetTypes.get(1).getType());
                    if (coverCutH5PhotoEntity != null) {
                        taskDetailResult.getQuerySpreadTaskResult().setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                    }
                    PhotoEntity coverCutOrdinaryPhotoEntity = dataMap.get(photoTargetTypes.get(2).getType());
                    if (coverCutOrdinaryPhotoEntity != null) {
                        taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                        //返回原图
                        taskDetailResult.getQuerySpreadTaskResult().setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                    }
                }
            }
            taskDetailResults.add(taskDetailResult);
        });
    }
}
