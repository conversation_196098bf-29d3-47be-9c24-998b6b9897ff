package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.kis.GetEmployeeRankingByMarketingActivityVo;
import com.facishare.marketing.api.arg.kis.ListSpreadRadarMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.GetMarketingActivityArg;
import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppDepartmentArg;
import com.facishare.marketing.api.data.OutEmployeeSpreadData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.kis.*;
import com.facishare.marketing.api.result.kis.GetEmployeeRankingByMarketingActivityResult.ActivityRankingResult;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.GetNoticeResult;
import com.facishare.marketing.api.result.qywx.ListEmployeeQywxGroupSendDetailResult;
import com.facishare.marketing.api.result.qywx.ListQywxMarketingActivityEmployeeRankingResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryCustomerGroupListResult;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppDepartmentResult;
import com.facishare.marketing.api.service.DingMiniAppDepartmentService;
import com.facishare.marketing.api.service.EnterpriseSpreadStatisticService;
import com.facishare.marketing.api.service.StatisticService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.mail.MailScheduleTypeEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.enums.qywx.QywxGroupSendOjbectTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwSendStatusEnum;
import com.facishare.marketing.common.enums.sms.mw.MwSendTaskTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.MwTemplateStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.bo.whatsapp.SendStatusStatisticBO;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.kis.*;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.mail.MailTaskStatisticsDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qywx.QYWXMomentTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendGroupResultDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendResultDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendTaskDAO;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.dto.MarketingActivityCompanyByClueDTO;
import com.facishare.marketing.provider.dto.MarketingActivityEmployeedsByClueDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.entity.ExternalConfig;
import com.facishare.marketing.provider.entity.NoticeEmailDetailEntity;
import com.facishare.marketing.provider.entity.NoticeEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.data.*;
import com.facishare.marketing.provider.entity.data.MarketingActivityNoticeSendData.NoticeVisibilityData;
import com.facishare.marketing.provider.entity.kis.GetEmployeeRankingByMarketingActivityEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeStatisticEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivityStatisticEntity;
import com.facishare.marketing.provider.entity.kis.SpreadTaskEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import com.facishare.marketing.provider.entity.mail.MailTaskStatisticsEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendTaskEntity;
import com.facishare.marketing.provider.entity.qywx.QywxMomentTaskEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSendEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.entity.whatsapp.WhatsAppSendTaskEntity;
import com.facishare.marketing.provider.innerResult.EmployeeEmailSendResult;
import com.facishare.marketing.provider.innerResult.ding.DingStaffInfoResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.feed.MaterailDataManager;
import com.facishare.marketing.provider.manager.feed.MaterailDataManagerFactory;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityActionManager;
import com.facishare.marketing.provider.manager.marketingactivity.QywxGroupSendManager;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.manager.qywx.MomentManager;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.whatsapp.WhatsAppManager;
import com.facishare.marketing.provider.manager.whatsapp.WhatsAppSendTaskManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.GetMarketingActivityDetailVo;
import com.facishare.marketing.provider.remote.whatsapp.result.TemplateResult;
import com.facishare.marketing.statistic.outapi.service.MarketingActivityStatisticService;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.wechat.sender.api.enums.WeChatMsgTypeEnum;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.github.mybatis.pagination.Page;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.contstant.OperatorConstants.IN;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("enterpriseSpreadStatisticService")
public class EnterpriseSpreadStatisticServiceImpl implements EnterpriseSpreadStatisticService {
    @Autowired
    MarketingActivityStatisticDao marketingActivityStatisticDao;
    @Autowired
    MarketingActivityEmployeeStatisticDAO marketingActivityEmployeeStatisticDao;
    @Autowired
    MarketingEnterpriseSpreadDao marketingEnterpriseSpreadDao;
    @Autowired
    FsAddressBookManager fsAddressBookManager;
    @Autowired
    WeChatServiceNoticeManager weChatServiceNoticeManager;
    @Autowired
    MwSmsSendDao mwSmsSendDao;
    @Autowired
    private NoticeDAO noticeDAO;
    @Autowired
    private NoticeManager noticeManager;
    @Autowired
    ArticleDAO articleDAO;
    @Autowired
    ActivityDAO activityDAO;
    @Autowired
    ProductDAO productDAO;
    @Autowired
    QRPosterDAO qrPosterDAO;
    @Autowired
    PhotoManager photoManager;
    @Autowired
    MarketingActivityService marketingActivityService;
    @Autowired
    MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;
    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MwSendManager sendManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private MaterailDataManagerFactory materailDataManagerFactory;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private GroupSendMessageManager groupSendMessageManager;
    @Autowired
    private QywxGroupSendTaskDAO qywxGroupSendTaskDAO;
    @Autowired
    private QywxGroupSendResultDAO groupSendResultDAO;
    @Autowired
    private QywxGroupSendManager qywxGroupSendManager;
    @Autowired
    private MarketingActivityStatisticService marketingActivityStatisticService;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private SpreadTaskDAO spreadTaskDAO;
    @Autowired
    private MailTaskStatisticsDAO mailTaskStatisticsDAO;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingMiniAppDepartmentService dingMiniAppDepartmentService;
    @Autowired
    private MomentManager momentManager;

    @Autowired
    private MarketingCrmManager marketingCrmManager;

    @Autowired
    private MarketingActivityActionManager marketingActivityActionManager;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private StatisticService statisticService;

    @Autowired
    private QywxGroupSendTaskDAO sendTaskDAO;

    @Autowired
    private QYWXMomentTaskDAO qywxMomentTaskDAO;

    @Autowired
    private QywxGroupSendGroupResultDAO qywxGroupSendGroupResultDAO;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;

    @Autowired
    private WhatsAppManager whatsAppManager;

    @Autowired
    private WhatsAppSendTaskManager whatsAppSendTaskManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    private final static List<I18nKeyEnum> SEND_TO_USER_TITLE_LIST = new ArrayList<>();
    static {
        SEND_TO_USER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_267);
        SEND_TO_USER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268);
        SEND_TO_USER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_269);
        SEND_TO_USER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_270);
        SEND_TO_USER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
    }

    private final static List<I18nKeyEnum> SEND_TO_GROUP_TITLE_LIST = new ArrayList<>();
    static {
        SEND_TO_GROUP_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_276);
        SEND_TO_GROUP_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268);
        SEND_TO_GROUP_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_269);
        SEND_TO_GROUP_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_270);
        SEND_TO_GROUP_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
    }

    private final static List<I18nKeyEnum> MOMENT_EMPLOYEE_TITLE_LIST = new ArrayList<>();
    static {
        MOMENT_EMPLOYEE_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285);
        MOMENT_EMPLOYEE_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
        MOMENT_EMPLOYEE_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268);
        MOMENT_EMPLOYEE_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_288);
        MOMENT_EMPLOYEE_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_289);
    }

    private final static List<I18nKeyEnum> EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST = new ArrayList<>();
    static {
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_269);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_298);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_299);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_300);
        EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_301);
    }

    private final static List<I18nKeyEnum> EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST = new ArrayList<>();
    static {
        EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285);
        EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
        EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268);
        EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_269);
        EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_310);
    }

    private final static List<I18nKeyEnum> MOMENT_CUSTOMER_TITLE_LIST = new ArrayList<>();
    static {
        MOMENT_CUSTOMER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_267);
        MOMENT_CUSTOMER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_316);
        MOMENT_CUSTOMER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_317);
        MOMENT_CUSTOMER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285);
        MOMENT_CUSTOMER_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
    }

    private final static List<I18nKeyEnum> ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST = new ArrayList<>();
    static {
        ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_267);
        ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_95);
        ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_96);
        ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_327);
        ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
    }


    private GetEnterpriseSpreadStatisticSumUpResult doSetAllSpreadData(MarketingActivityExternalConfigEntity externalConfigEntity, Integer spreadType) {
        if(null == externalConfigEntity || null == spreadType){
            return null;
        }

        if ( spreadType == MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType() || spreadType == MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType()) {
            MarketingActivityStatisticEntity marketingActivityStatisticEntity = marketingActivityStatisticDao.queryByMarketingActivityIdAndEa(externalConfigEntity.getMarketingActivityId(),externalConfigEntity.getEa());
            GetEnterpriseSpreadStatisticSumUpResult result = BeanUtil.copy(marketingActivityStatisticEntity, GetEnterpriseSpreadStatisticSumUpResult.class);
            if (null == result) {
                result = new GetEnterpriseSpreadStatisticSumUpResult();
            }
            MarketingActivityNoticeSendData vo = externalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO();
            if (vo != null) {
                if (null != vo.getNoticeVisibilityArg()) {
                    NoticeVisibilityData noticeVisibility = vo.getNoticeVisibilityArg();
                    result.setCountDepartment(noticeVisibility.getDepartmentIds() == null ? 0 : noticeVisibility.getDepartmentIds().size());
                    result.setCountUser(noticeVisibility.getUserIds() == null ? 0 : noticeVisibility.getUserIds().size());
                }
                // 物料的封面
                List<PhotoEntity> photoEntities = null;
                if (vo.getContentType() == NoticeContentTypeEnum.PRODUCT.getType()) {
                    photoEntities = photoManager.queryPhotoNoReset(PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType(), vo.getContent());
                    result.setTitle(productDAO.getNameById(vo.getContent()));
                } else if (vo.getContentType() == NoticeContentTypeEnum.ARTICLE.getType()) {
                    photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), vo.getContent());
                    result.setTitle(articleDAO.getTitleById(vo.getContent()));
                } else if (vo.getContentType() == NoticeContentTypeEnum.ACTIVITY.getType()) {
                    photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), vo.getContent());
                    result.setTitle(activityDAO.getTitleById(vo.getContent()));
                } else if(vo.getContentType() == NoticeContentTypeEnum.HEXAGON.getType()){
                    Map<String, HexagonBaseInfoDTO> hexagonMap = hexagonManager.getHexagonBaseInfoById(
                            Lists.newArrayList(vo.getContent()), externalConfigEntity.getEa());
                    if (hexagonMap != null && hexagonMap.get(vo.getContent()) != null){
                        result.setTitle(hexagonMap.get(vo.getContent()).getTitle());
                        result.setCoverUrl(hexagonMap.get(vo.getContent()).getCoverUrl());
                    }
                } else {
                    photoEntities = Lists.newArrayList();
                    if (vo.getContentType() == NoticeContentTypeEnum.QR_POSTER.getType()) {
                        QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(vo.getContent());
                        if (qrPosterEntity != null) {
                            result.setCoverUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), externalConfigEntity.getEa(), false));
                            result.setTitle(qrPosterEntity.getTitle());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(photoEntities)) {
                    result.setCoverUrl(photoEntities.get(0).getThumbnailUrl());
                }
                result.setObjectType(NoticeContentTypeEnum.fromType(vo.getContentType()).toObjectType());
                result.setObjectId(vo.getContent());
            }
            // 获取线索数从表单统计
            result.setLeadAccumulationCount(customizeFormClueManager.countClueNumByMarketingActivityId(externalConfigEntity.getEa(), externalConfigEntity.getMarketingActivityId(), false, 0));
            return result;
        } else if( spreadType == MarketingActivitySpreadTypeEnum.PARTNER_GROUP_SEND.getSpreadType()){
            List<MarketingActivityStatisticEntity> marketingActivityStatisticEntities = marketingActivityStatisticDao.queryByMarketingActivityIdForPartner(externalConfigEntity.getMarketingActivityId());
            GetEnterpriseSpreadStatisticSumUpResult result =new GetEnterpriseSpreadStatisticSumUpResult();
            int spreadCount = 0;
            int spreadUpUserCount = 0;
            int lookUpCount = 0;
            int forwardCount = 0;
            if (marketingActivityStatisticEntities != null && marketingActivityStatisticEntities.size() > 0) {
                for (MarketingActivityStatisticEntity temp : marketingActivityStatisticEntities) {
                    spreadCount += temp.getSpreadCount() == null ? 0 : temp.getSpreadCount();
                    spreadUpUserCount += temp.getSpreadUserCount() == null ? 0 : temp.getSpreadUserCount();
                    lookUpCount += temp.getLookUpCount() == null ? 0 : temp.getLookUpCount();
                    forwardCount += temp.getForwardCount() == null ? 0 : temp.getForwardCount();
                }
            }
            result.setForwardCount(forwardCount);
            result.setLookUpCount(lookUpCount);
            result.setSpreadCount(spreadCount);
            result.setSpreadUserCount(spreadUpUserCount);
            MarketingActivityPartnerNoticeSendData vo = externalConfigEntity.getExternalConfig().getMarketingActivityPartnerNoticeSendData();
            if (vo != null) {
                if (null != vo.getPartnerNoticeVisibilityArg()) {
                    MarketingActivityPartnerNoticeSendData.PartnerNoticeVisibilityData noticeVisibility = vo.getPartnerNoticeVisibilityArg();
                    result.setCountDepartment(noticeVisibility.getEaList() == null ? 0 : noticeVisibility.getEaList().size());
                    result.setCountUser(noticeVisibility.getTenantGroupIdList() == null ? 0 : noticeVisibility.getTenantGroupIdList().size());
                }
                // 物料的封面
                List<PhotoEntity> photoEntities = null;
                if (vo.getContentType() == NoticeContentTypeEnum.PRODUCT.getType()) {
                    photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), vo.getContent());
                    result.setTitle(productDAO.getNameById(vo.getContent()));
                } else if (vo.getContentType() == NoticeContentTypeEnum.ARTICLE.getType()) {
                    photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), vo.getContent());
                    result.setTitle(articleDAO.getTitleById(vo.getContent()));
                } else if (vo.getContentType() == NoticeContentTypeEnum.ACTIVITY.getType()) {
                    photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), vo.getContent());
                    result.setTitle(activityDAO.getTitleById(vo.getContent()));
                } else if(vo.getContentType() == NoticeContentTypeEnum.HEXAGON.getType()){
                    Map<String, HexagonBaseInfoDTO> hexagonMap = hexagonManager.getHexagonBaseInfoById(
                                                   Lists.newArrayList(vo.getContent()), externalConfigEntity.getEa());
                    if (hexagonMap != null && hexagonMap.get(vo.getContent()) != null){
                        result.setTitle(hexagonMap.get(vo.getContent()).getTitle());
                        result.setCoverUrl(hexagonMap.get(vo.getContent()).getCoverUrl());
                    }
                } else {
                    photoEntities = Lists.newArrayList();
                    if (vo.getContentType() == NoticeContentTypeEnum.QR_POSTER.getType()) {
                        QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(vo.getContent());
                        if (qrPosterEntity != null) {
                            result.setCoverUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), externalConfigEntity.getEa(), false));
                            result.setTitle(qrPosterEntity.getTitle());
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(photoEntities)) {
                    result.setCoverUrl(photoEntities.get(0).getThumbnailUrl());
                }
                result.setObjectType(NoticeContentTypeEnum.fromType(vo.getContentType()).toObjectType());
                result.setObjectId(vo.getContent());
            }
            // 获取线索数从表单统计
            result.setLeadAccumulationCount(customizeFormClueManager.countClueNumByMarketingActivityId(externalConfigEntity.getEa(), externalConfigEntity.getMarketingActivityId(), false, 0));
            return result;
        }
        return null;
    }

    private MarketingActivityWeChatServiceStatisticResult doSetWechatData(MarketingActivityExternalConfigEntity externalConfigEntity, Integer spreadType) {
        if (null == externalConfigEntity || null == spreadType || spreadType != MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType()) {
            return null;
        }
        MarketingActivityWeChatServiceStatisticResult result = new MarketingActivityWeChatServiceStatisticResult();
        result.setAssociateIdType(externalConfigEntity.getAssociateIdType());
        if (externalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() && externalConfigEntity.getExternalConfig() != null
            && externalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO() != null) {
            WeChatServiceMarketingActivityData wechatData = externalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO();
            if (wechatData.getSendRange() != null) {
                result.setSendRange(wechatData.getSendRange());
                if (wechatData.getSendRange() == com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_MARKETING_USER_GROUP.getCode()) {
                    List<String> tmpTitle = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(wechatData.getMarketingUserGroupIds())) {
                        tmpTitle = marketingUserGroupDao.batchGetNameByStatus(wechatData.getMarketingUserGroupIds(), MarketingUserGroupStatusEnum.OPEN.getStatus());
                    }
                    List<String> titles = tmpTitle == null ? Lists.newArrayList() : tmpTitle;
                    result.setMarketingUserGroupTitle(titles);
                }

                result.setMsgType(wechatData.getMsgType());
                result.setContent(wechatData.getContent());
                if (null != wechatData.getMsgType() && wechatData.getMsgType() == WeChatMsgTypeEnum.PIC_MSG.getType()) {
                    String imageUrl = fileV2Manager.getUrlByPath(wechatData.getContent(), externalConfigEntity.getEa(), false);
                    result.setContent(imageUrl);
                }
            }
        } else if (externalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType() && externalConfigEntity.getExternalConfig() != null
            && externalConfigEntity.getExternalConfig().getWeChatTemplateMessageVO() != null) {
            WeChatTemplateMessageData wechatData = externalConfigEntity.getExternalConfig().getWeChatTemplateMessageVO();
            if (wechatData.getSendRange() != null) {
                result.setSendRange(wechatData.getSendRange());
                if (wechatData.getSendRange() == com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_MARKETING_USER_GROUP.getCode()) {
                    List<String> tmpTitle = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(wechatData.getMarketingUserGroupIds())) {
                        tmpTitle = marketingUserGroupDao.batchGetNameByStatus(wechatData.getMarketingUserGroupIds(), MarketingUserGroupStatusEnum.OPEN.getStatus());
                    }
                    List<String> titles = tmpTitle == null ? Lists.newArrayList() : tmpTitle;
                    result.setMarketingUserGroupTitle(titles);
                }
                if (wechatData.getRedirectType().equals(RedirectTypeEnum.MATERIAL.getType())) {
                    result.setMaterialId(wechatData.getMaterialId());
                    result.setMaterialType(wechatData.getMaterialType());
                    MaterailDataManager materailDataManager = materailDataManagerFactory.get(wechatData.getMaterialType());
                    if (null != materailDataManager) {
                        AbstractMaterialData data = materailDataManager.get(externalConfigEntity.getEa(), wechatData.getMaterialId());
                        result.setMaterialData(data);
                    }
                }
                result.setRedirectType(wechatData.getRedirectType());
                result.setContent(wechatData.getRedirectUrl());
            }
        }

        GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByMarketingActivityId(externalConfigEntity.getMarketingActivityId());
        if (null == getNoticeResult) {
            return result;
        }
        result.setActualSend(getNoticeResult.getActualCompletedCount());
        result.setFailedSend(getNoticeResult.getFailSendCount());
        result.setNeedSend(getNoticeResult.getNeedSendCount());
        return result;
    }

    private MarketingActivitySmsSendStatisticResult doSetSendNoteData(MarketingActivityExternalConfigEntity externalConfigEntity, Integer spreadType) {
        if (null == externalConfigEntity || null == spreadType || spreadType != MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType()) {
            return null;
        }
        MarketingActivitySmsSendStatisticResult result = new MarketingActivitySmsSendStatisticResult();
        String smsContent = null;
        if (externalConfigEntity.getExternalConfig() != null && externalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO() != null) {
            MarketingActivityGroupSenderData groupSenderData = externalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO();
            if (groupSenderData.getSendRange() != null) {
                result.setSendRange(groupSenderData.getSendRange());
                if (groupSenderData.getSendRange() == SendRangeEnum.USER_GROUP.getType()) {
                    List<String> tmpTitle = marketingUserGroupDao.batchGetNameByStatus(groupSenderData.getMarketingUserGroupIds(), MarketingUserGroupStatusEnum.OPEN.getStatus());
                    List<String> titles = tmpTitle == null ? Lists.newArrayList() : tmpTitle;
                    result.setMarketingUserGroupTitle(titles);
                }
            }
            smsContent = externalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO().getTemplateContent();
        }

        MwSmsSendEntity mwSmsSendEntity = mwSmsSendDao.getSMSSendById(externalConfigEntity.getAssociateId());
        if (null == mwSmsSendEntity) {
            return result;
        }

        MwSmsTemplateEntity mwSmsTemplateEntity = mwSmsTemplateDao.getTemplateById(mwSmsSendEntity.getTemplateId());
        result.setActualSend(mwSmsSendEntity.getActualSenderCount());
        if (mwSmsSendEntity.getStatus() == MwSendStatusEnum.NEW.getStatus()){
            result.setActualSend(0);
        }
        result.setNeedSend(mwSmsSendEntity.getToSenderCount());
        result.setFailedSend(MathUtil.subtract(mwSmsSendEntity.getToSenderCount(), mwSmsSendEntity.getActualSenderCount()));

        if (StringUtils.isBlank(smsContent)) {
            smsContent = sendManager.buildSmsContentById(mwSmsSendEntity.getSignatureId(), mwSmsSendEntity.getTemplateId());
        } else {
            smsContent = sendManager.buildSmsContentBySignatureId(mwSmsSendEntity.getSignatureId(), smsContent);
        }
        result.setSmsContent(smsContent);
        result.setTemplateName(mwSmsTemplateEntity != null ? mwSmsTemplateEntity.getName() : null);
        return result;
    }

    private MarketingActivityQywxGroupSendMessageResult doSetQywxGroupSendData(MarketingActivityExternalConfigEntity externalConfigEntity, Integer spreadType) {
        if (externalConfigEntity == null || spreadType == null || spreadType != MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType()) {
            return null;
        }
        //MarketingActivityStatisticEntity marketingActivityStatisticEntity = marketingActivityStatisticDao.queryByMarketingActivityId(externalConfigEntity.getMarketingActivityId());
        MarketingActivityQywxGroupSendMessageResult marketingActivityQywxGroupSendMessageResult = new MarketingActivityQywxGroupSendMessageResult();
        //Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(externalConfigEntity.getEa(), Sets.newHashSet(externalConfigEntity.getMarketingActivityId())).getData();
        Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(externalConfigEntity.getEa(), Sets.newHashSet(externalConfigEntity.getMarketingActivityId())).getData();
        // TODO 转发人数 目前不支持
        Integer uv = pvMap.get(externalConfigEntity.getMarketingActivityId());
        marketingActivityQywxGroupSendMessageResult.setLookUpUserCount(uv != null ? uv : 0);
        QywxGroupSendTaskEntity qywxGroupSendTaskEntity = qywxGroupSendTaskDAO.queryById(externalConfigEntity.getAssociateId());
        if (qywxGroupSendTaskEntity == null) {
            return null;
        }
        String objectId = qywxGroupSendTaskEntity.getObjectId();
        Integer objectType = qywxGroupSendTaskEntity.getObjectType();
        marketingActivityQywxGroupSendMessageResult.setObjectId(objectId);
        marketingActivityQywxGroupSendMessageResult.setObjectType(objectType);
        List<PhotoEntity> photoEntities = Lists.newArrayList();
        if (Objects.isNull(objectType)) {
            marketingActivityQywxGroupSendMessageResult.setTitle(qywxGroupSendTaskEntity.getTitle());
        } else if (ObjectTypeEnum.PRODUCT.getType() == objectType) {
            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), objectId);
            marketingActivityQywxGroupSendMessageResult.setTitle(productDAO.getNameById(objectId));
        } else if (ObjectTypeEnum.ARTICLE.getType() == objectType) {
            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), objectId);
            marketingActivityQywxGroupSendMessageResult.setTitle(articleDAO.getTitleById(objectId));
        } else if (ObjectTypeEnum.HEXAGON_SITE.getType() == objectType) {
            Map<String, HexagonBaseInfoDTO> hexagonMap = hexagonManager.getHexagonBaseInfoById(
                Lists.newArrayList(objectId), externalConfigEntity.getEa());
            if (hexagonMap != null && hexagonMap.get(objectId) != null) {
                marketingActivityQywxGroupSendMessageResult.setTitle(hexagonMap.get(objectId).getTitle());
                marketingActivityQywxGroupSendMessageResult.setCoverUrl(hexagonMap.get(objectId).getCoverUrl());
            }
        } else if (ObjectTypeEnum.QR_POSTER.getType() == objectType) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(objectId);
            if (qrPosterEntity != null) {
                marketingActivityQywxGroupSendMessageResult.setCoverUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), null, false));
                marketingActivityQywxGroupSendMessageResult.setTitle(qrPosterEntity.getTitle());
            }
        }
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            marketingActivityQywxGroupSendMessageResult.setCoverUrl(photoEntities.get(0).getThumbnailUrl());
        }
        marketingActivityQywxGroupSendMessageResult
            .setLeadAccumulationCount(customizeFormClueManager.countClueNumByMarketingActivityId(externalConfigEntity.getEa(), externalConfigEntity.getMarketingActivityId(), false, 0));
        return marketingActivityQywxGroupSendMessageResult;
    }

    private MarketingMailGroupSendResult doSetMarketingMailGroupSendResult(MarketingActivityExternalConfigEntity externalConfigEntity, Integer spreadType) {
        if (externalConfigEntity == null || spreadType == null || spreadType != MarketingActivitySpreadTypeEnum.MAIL_GROUP_SEND.getSpreadType()) {
            return null;
        }
        MarketingMailGroupSendResult marketingMailGroupSendResult = new MarketingMailGroupSendResult();
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(externalConfigEntity.getAssociateId());
        if (mailSendTaskEntity == null) {
            return null;
        }
        marketingMailGroupSendResult.setTaskId(mailSendTaskEntity.getId());
        marketingMailGroupSendResult.setMarketingEvenId(externalConfigEntity.getMarketingEventId());
        marketingMailGroupSendResult.setScheduleType(mailSendTaskEntity.getScheduleType());
        marketingMailGroupSendResult.setSubject(mailSendTaskEntity.getSubject());
        marketingMailGroupSendResult.setSendRange(mailSendTaskEntity.getSendRange());
        marketingMailGroupSendResult.setCreateUser(mailSendTaskEntity.getFsUserId());
        marketingMailGroupSendResult.setSendStatus(mailSendTaskEntity.getSendStatus());
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            marketingMailGroupSendResult.setSendTime(mailSendTaskEntity.getCreateTime().getTime());
        } else {
            marketingMailGroupSendResult.setSendTime(mailSendTaskEntity.getFixTime());
        }

        Long requestNum = 0L;
        Long deliveredNum = 0L;
        MailTaskStatisticsEntity mailTaskStatisticsEntity = mailTaskStatisticsDAO.getByEaAndTaskId(mailSendTaskEntity.getEa(), mailSendTaskEntity.getId());
        if (mailTaskStatisticsEntity != null) {
            requestNum = mailTaskStatisticsEntity.getSendNum();
            deliveredNum = mailTaskStatisticsEntity.getDeliveredNum();
        }

        marketingMailGroupSendResult.setSendCount(requestNum);
        marketingMailGroupSendResult.setSuccessCount(deliveredNum);
        marketingMailGroupSendResult.setFailedCount((requestNum - deliveredNum) > 0 ? (requestNum - deliveredNum) : 0);
        return marketingMailGroupSendResult;
    }

    @Override
    public Result<PageResult<TempListAllMarketingActivityResult>> listMarketingActivity(String ea, Integer fsUserId, ListSpreadRadarMarketingActivityArg vo) {
        // 分页查询营销活动信息
        PaasQueryMarketingActivityArg arg = new PaasQueryMarketingActivityArg();
        arg.setName(vo.getName());
        arg.setMarketingEventId(vo.getMarketingEventId());
        arg.setIds(vo.getMarketingActivityIds());
        arg.setSpreadType(vo.getSpreadType());
        if (CollectionUtils.isNotEmpty(vo.getSpreadTypeList())) {
            arg.setSpreadTypes(vo.getSpreadTypeList().stream().map(String::valueOf).collect(Collectors.toList()));
        }
        arg.setPageNumber((vo.getPageNum() - 1) * vo.getPageSize());
        arg.setPageSize(vo.getPageSize());
        if(CollectionUtils.isNotEmpty(vo.getDataPermission())){
            arg.setDataPermission(vo.getDataPermission());
        }
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> marketingActivityListVo = marketingActivityCrmManager.listMarketingActivity(ea, -10000, arg);
        log.info("listMarketingActivity pageSize:{}  result count:{}", vo.getPageSize(), marketingActivityListVo.getDataList().size());
        long startTime = System.currentTimeMillis();
        log.info("listMarketingActivity  cost startTime:{}", startTime);
        // 组装数据
        List<TempListAllMarketingActivityResult> tempListMarketingActivityResult = Lists.newArrayList();
        if (marketingActivityListVo != null && CollectionUtils.isNotEmpty(marketingActivityListVo.getDataList())) {
            List<String> marketingActivityIds = marketingActivityListVo.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
            List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityList = marketingActivityExternalConfigDao.getByMarketingActivityIds(marketingActivityIds);
            Map<String, MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityMap = marketingActivityExternalConfigEntityList.stream()
                .collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getMarketingActivityId, data -> data));
            CountDownLatch countDownLatch = new CountDownLatch(marketingActivityListVo.getDataList().size());
            ExecutorService executorService = Executors.newFixedThreadPool(Math.min(5, marketingActivityListVo.getDataList().size()));
            TraceContext traceContext = TraceContext.get();
            marketingActivityListVo.getDataList().forEach(data -> {
                executorService.submit(()->{
                    try{
                        TraceContext._set(traceContext);
                        MarketingActivityData marketActivityData = MarketingActivityData.wrap(data);
                        Integer spreadType = Integer.valueOf(marketActivityData.getSpreadType());
                        TempListAllMarketingActivityResult tempResult = new TempListAllMarketingActivityResult();
                        tempResult.setCreateTime(data.getCreateTime());
                        tempResult.setMarketingActivityId(data.getId());
                        tempResult.setSpreadType(spreadType);
                        tempResult.setSpreadContent(data.getName());
                        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigEntityMap.get(data.getId());
                        if (marketingActivityExternalConfigEntity == null) {
                            return;
                        }
                        if(marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO()!=null){
                            WeChatServiceMarketingActivityData vo1 = marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO();
                            //               MarketingActivityNoticeSendData vo1 = marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO();
                            tempResult.setGraphicMessagePic(vo1.getGraphicMessagePic());
                            tempResult.setGraphicMessageTitle(vo1.getGraphicMessageTitle());
                        }
                        tempResult.setMarketingActivityWeChatServiceStatisticResult(doSetWechatData(marketingActivityExternalConfigEntity, spreadType));
                        tempResult.setMarketingActivitySmsSendStatisticResult(doSetSendNoteData(marketingActivityExternalConfigEntity, spreadType));
                        tempResult.setQywxGroupSendMessageDetailResult(qywxGroupSendManager.buildGroupSendMessageDetail(ea, marketingActivityExternalConfigEntity.getMarketingActivityId()));
                        tempResult.setMarketingActivityQywxGroupSendMessageResult(doSetQywxGroupSendData(marketingActivityExternalConfigEntity, spreadType));
                        tempResult.setMarketingMailGroupSendResult(doSetMarketingMailGroupSendResult(marketingActivityExternalConfigEntity, spreadType));
                        GetEnterpriseSpreadStatisticSumUpResult tmpAllSpread = doSetAllSpreadData(marketingActivityExternalConfigEntity, spreadType);
                        if (spreadType == MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType()) {
                            if (tempResult.getMarketingActivitySmsSendStatisticResult() != null) {
                                tempResult.setSpreadContent(tempResult.getMarketingActivitySmsSendStatisticResult().getTemplateName());
                            }
                            MwSmsSendEntity mwSmsSendEntity = mwSmsSendDao.getSMSSendById(marketingActivityExternalConfigEntity.getAssociateId());
                            if (null == mwSmsSendEntity) {
                                tempResult.setSendCancelable(false);
                            }
                            MwSmsTemplateEntity templateEntity = mwSmsTemplateDao.getTemplateById(mwSmsSendEntity.getTemplateId());
                            if (templateEntity != null) {
                                if (mwSmsSendEntity.getStatus() == MwSendStatusEnum.NEW.getStatus() && templateEntity.getStatus() >= MwTemplateStatusEnum.INVALID.getStatus()){
                                    tempResult.getMarketingActivitySmsSendStatisticResult().setActualSend(0);
                                }
                            }
                            if (mwSmsSendEntity.getType() == MwSendTaskTypeEnum.SCHEDULE_SEND.getType()
                                    && mwSmsSendEntity.getStatus() == MwSendStatusEnum.SCHEDULE_SENDING.getStatus()){
                                tempResult.setSendCancelable(true);
                                //如果是定时任务，则前端实现的实际发送数据人数为0
                                tempResult.getMarketingActivitySmsSendStatisticResult().setActualSend(0);
                            }else {
                                tempResult.setSendCancelable(false);
                            }
                        }
                        //检查是否能取消
                        if (spreadType == MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType()||spreadType == MarketingActivitySpreadTypeEnum.PARTNER_GROUP_SEND.getSpreadType() ||spreadType == MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType()){
                            NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId());
                            if (noticeEntity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus() && noticeEntity.getSendType() == NoticeSendTypeEnum.TIMING.getType()){
                                tempResult.setSendCancelable(true);
                            }else {
                                tempResult.setSendCancelable(false);
                            }
                        }

                        if (spreadType == MarketingActivitySpreadTypeEnum.MAIL_GROUP_SEND.getSpreadType()){
                            tempResult.setSendCancelable(mailManager.checkSendCancelable(marketingActivityExternalConfigEntity.getAssociateId()));
                        }
                        if (spreadType == MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType()){
                            tempResult.setSendCancelable(qywxGroupSendManager.checkSendCancelable(marketingActivityExternalConfigEntity.getAssociateId()));
                        }
                        if(spreadType == MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType() || spreadType == MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType()){
                            MarketingActivityNoticeSendStatisticResult allSpread = BeanUtil.copy(tmpAllSpread, MarketingActivityNoticeSendStatisticResult.class);
                            if (null != allSpread) {
                                allSpread.setForwardUserCount(tmpAllSpread.getForwardCount());
                                allSpread.setLookUpUserCount(tmpAllSpread.getLookUpCount());
                            }
                            tempResult.setMarketingActivityNoticeSendStatisticResult(allSpread);
                        }
                        if(spreadType == MarketingActivitySpreadTypeEnum.PARTNER_GROUP_SEND.getSpreadType()){
                            MarketingActivityPartnerSendStatisticResult partnerSpread = BeanUtil.copy(tmpAllSpread, MarketingActivityPartnerSendStatisticResult.class);
                            if (null != partnerSpread) {
                                partnerSpread.setForwardUserCount(tmpAllSpread.getForwardCount());
                                partnerSpread.setLookUpUserCount(tmpAllSpread.getLookUpCount());
                            }
                            tempResult.setMarketingActivityPartnerSendStatisticResult(partnerSpread);
                        }

                        if (spreadType == MarketingActivitySpreadTypeEnum.WHATS_APP_SPREAD.getSpreadType()) {
                            MarketingActivityWhatsAppSpreadResult whatsAppSpreadResult = new MarketingActivityWhatsAppSpreadResult();
                            List<SendStatusStatisticBO> sendStatusStatisticBOList = whatsAppManager.statisticSendStatusCount(ea, Lists.newArrayList(data.getId()));
                            List<WhatsAppSendTaskEntity>  whatsAppSendTaskEntityList = whatsAppSendTaskManager.getByMarketingActivityIdList(ea, Lists.newArrayList(data.getId()));
                            if (CollectionUtils.isNotEmpty(whatsAppSendTaskEntityList)) {
                                WhatsAppSendTaskEntity whatsAppSendTaskEntity = whatsAppSendTaskEntityList.get(0);
                                TemplateResult.TemplateInfo  templateInfo = whatsAppManager.getTemplateByNameAndLanguage(ea, whatsAppSendTaskEntity.getBusinessPhone(), whatsAppSendTaskEntity.getTemplateName(), whatsAppSendTaskEntity.getTemplateLanguage());
                                whatsAppSpreadResult.setTemplateContent(whatsAppManager.getTemplateBody(templateInfo));
                            }
                            if (CollectionUtils.isNotEmpty(sendStatusStatisticBOList)) {
                                SendStatusStatisticBO sendStatusStatisticBO = sendStatusStatisticBOList.get(0);
                                whatsAppSpreadResult.setSendCount(sendStatusStatisticBO.getSendCount());
                                whatsAppSpreadResult.setSendSuccessCount(sendStatusStatisticBO.getDeliveredCount());
                            } else {
                                whatsAppSpreadResult.setSendCount(0);
                                whatsAppSpreadResult.setSendSuccessCount(0);
                            }
                            tempResult.setMarketingActivityWhatsAppSpreadResult(whatsAppSpreadResult);
                        }

                        tempResult.setAuditStatus(marketActivityData.getLifeStatus());
                        Integer status = marketActivityData.getStatus();
                        if (status != null) {
                            tempResult.setStatus(status);
                            if (marketingActivityExternalConfigEntity.getExternalConfig() != null) {
                                int spreadStatus = 0;
                                if (MarketingActivityActionEnum.getAction(spreadType) == MarketingActivityActionEnum.ALL_SPREAD || MarketingActivityActionEnum.getAction(spreadType) == MarketingActivityActionEnum.MEMBER_MARKETING) {
                                    MarketingActivityNoticeSendData marketingActivityNoticeSendVO = marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityNoticeSendVO();
                                    status = MarketingCrmManager.noticeStatusEnumMap.get(String.valueOf(status));
                                    spreadStatus = getSpreadStatus(status, marketingActivityNoticeSendVO.getStartTime(), new Date().getTime(), marketingActivityNoticeSendVO.getEndTime());
                                }
                                if (MarketingActivityActionEnum.getAction(spreadType) == MarketingActivityActionEnum.PARTNER_SPREAD) {
                                    MarketingActivityPartnerNoticeSendData marketingActivityPartnerNoticeSendData = marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityPartnerNoticeSendData();
                                    status = MarketingCrmManager.noticeStatusEnumMap.get(String.valueOf(status));
                                    spreadStatus = getSpreadStatus(status, marketingActivityPartnerNoticeSendData.getStartTime(), new Date().getTime(), marketingActivityPartnerNoticeSendData.getEndTime());
                                }
                                tempResult.setSpreadStatus(spreadStatus);
                            }
                        }
                        tempListMarketingActivityResult.add(tempResult);
                    }catch (Exception e) {
                        log.warn("buildActivityDetail error", e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            });
            try {
                countDownLatch.await(5L, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.warn("buildActivityDetail timeout");
            }
            if (!executorService.isShutdown()) {
                executorService.shutdown();
            }
        }
        if(!tempListMarketingActivityResult.isEmpty()){

            List<String> marketingActivityIds = marketingActivityListVo.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
            // 记录位置
            Map<String, Integer> idIndexMap = marketingActivityIds.stream().collect(Collectors.toMap(id -> id, marketingActivityIds::indexOf));
            // 使用 sort 方法对 ObjectData 列表排序
            tempListMarketingActivityResult.sort(Comparator.comparingInt(o -> idIndexMap.get(o.getMarketingActivityId())));

            Set<String> marketingActivityIdSet = tempListMarketingActivityResult.stream().map(TempListAllMarketingActivityResult::getMarketingActivityId).collect(Collectors.toSet());
            BatchObjectUserMarketingStatisticsArg statisticsArg = new BatchObjectUserMarketingStatisticsArg();
            statisticsArg.setEa(ea);
            statisticsArg.setMarketingActivityIdList(Lists.newArrayList(marketingActivityIdSet));
            Result<List<ObjectUserMarketingStatisticsResult>> statisticsResultResult = statisticService.batchGetObjectMarketingActivityStatistics(statisticsArg);
            Map<String, ObjectUserMarketingStatisticsResult> marketingActivityIdToStatisticsMap = Maps.newHashMap();
            if (statisticsResultResult.isSuccess() && org.apache.commons.collections.CollectionUtils.isNotEmpty(statisticsResultResult.getData())) {
                statisticsResultResult.getData().forEach(e -> marketingActivityIdToStatisticsMap.put(e.getMarketingActivityId(), e));
            }
            Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(ea, marketingActivityIdSet).getData();
            Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(ea, marketingActivityIdSet).getData();
            Map<String, Integer> leadCountMap = customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(ea, new ArrayList<>(marketingActivityIdSet), null, null, false).stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getMarketingActivityId, CustomizeFormClueNumDTO::getCount, (v1, v2) -> v1));
            tempListMarketingActivityResult.forEach(d -> {
                ObjectUserMarketingStatisticsResult statisticsResult = marketingActivityIdToStatisticsMap.get(d.getMarketingActivityId());
                if (statisticsResult != null) {
                    d.setUv(statisticsResult.getLookUpUserCount());
                    d.setPv(statisticsResult.getLookUpCount());
                } else if (null != pvMap) {
                    d.setUv(uvMap.get(d.getMarketingActivityId()));
                    d.setPv(pvMap.get(d.getMarketingActivityId()));
                }
                d.setLeadCount(leadCountMap.get(d.getMarketingActivityId()));
            });
        }
        PageResult<TempListAllMarketingActivityResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(tempListMarketingActivityResult);
        pageResult.setTotalCount(marketingActivityListVo == null ? 0 : marketingActivityListVo.getTotal());
        pageResult.setTime(vo.getTime());
        log.info("listMarketingActivity  cost time:{} ", System.currentTimeMillis() -startTime);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<com.facishare.marketing.api.result.kis.GetMarketingActivityResult> getMarketingActivity(String ea, Integer fsUserId, GetMarketingActivityArg getMarketingActivityArg) {
        GetMarketingActivityDetailVo vo = marketingActivityCrmManager.getByIdMarketingActivity(ea, -10000, getMarketingActivityArg.getId());
        if (vo == null) {
            return Result.newError(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
        }
        MarketingActivityExternalConfigEntity externalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(getMarketingActivityArg.getId());

        GetMarketingActivityResult result = new GetMarketingActivityResult();

        result.setMarketingActivityId(vo.getId());
        Integer spreadType = vo.getSpreadType();
        result.setSpreadType(spreadType);
        result.setSpreadContent(vo.getName());

        if (externalConfig != null) {
            result.setCreateTime(externalConfig.getCreateTime().getTime());
            if (externalConfig.getExternalConfig() != null) {
                ExternalConfig configInfo = externalConfig.getExternalConfig();
                if (configInfo.getWeChatServiceMarketingActivityVO() != null && configInfo.getWeChatServiceMarketingActivityVO().getFixedTime() != null) {
                    result.setCreateTime(configInfo.getWeChatServiceMarketingActivityVO().getFixedTime());
                }

                if (configInfo.getMarketingActivityNoticeSendVO() != null && configInfo.getMarketingActivityNoticeSendVO().getTimingDate() != null) {
                    result.setCreateTime(configInfo.getMarketingActivityNoticeSendVO().getTimingDate());
                }

                if (configInfo.getMarketingActivityGroupSenderVO() != null && configInfo.getMarketingActivityGroupSenderVO().getScheduleTime() != null) {
                    result.setCreateTime(configInfo.getMarketingActivityGroupSenderVO().getScheduleTime());
                    if (externalConfig.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType()) {
                        result.setGraphicMessagePic(configInfo.getWeChatServiceMarketingActivityVO().getGraphicMessagePic());
                        result.setGraphicMessageTitle(configInfo.getWeChatServiceMarketingActivityVO().getGraphicMessageTitle());
                    }

                }
//                if (externalConfig.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType()) {
//                    result.setGraphicMessagePic(configInfo.getWeChatServiceMarketingActivityVO().getGraphicMessagePic());
//                    result.setGraphicMessageTitle(configInfo.getWeChatServiceMarketingActivityVO().getGraphicMessageTitle());
//                }
            }
        }

        // 查询市场活动名称
        try {
            if (StringUtils.isNotBlank(vo.getMarketingEventId())) {
                ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), vo.getMarketingEventId());
                result.setMarketingEventName(objectData == null ? null : objectData.getName());
            }
        } catch (Exception e) {
            log.warn("EnterpriseSpreadStatisticServiceImpl.getMarketingActivity get MarketingEventObj error e:", e);
        }

        result.setMarketingActivityWeChatServiceStatisticResult(doSetWechatData(externalConfig, spreadType));
        result.setMarketingActivitySmsSendStatisticResult(doSetSendNoteData(externalConfig, spreadType));
        result.setSpreadStatisticSumUpResult(doSetAllSpreadData(externalConfig, spreadType));
        result.setMarketingActivityQywxGroupSendMessageResult(doSetQywxGroupSendData(externalConfig, spreadType));
        result.setQywxGroupSendMessageDetailResult(qywxGroupSendManager.buildGroupSendMessageDetail(ea, getMarketingActivityArg.getId()));
        result.setMarketingMailGroupSendResult(doSetMarketingMailGroupSendResult(externalConfig, spreadType));
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PageResult<GetClueListByMarketingAcvitiyIdUnitResult>> getClueListByMarketingActivityId(String ea, Integer userId, String marketingActivityId, Integer pageSize, Integer pageNum,
        boolean fromEnterprise) {
        if (EmptyUtil.isNullForList(ea, userId, marketingActivityId, pageSize, pageNum)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        CustomizeFormClueInfoResult customizeFormClueInfoResult = null;
        if (fromEnterprise) {
            customizeFormClueInfoResult = customizeFormClueManager.getClueInfoByMarketingActivityId(ea, marketingActivityId, false, 0);
        } else {
            customizeFormClueInfoResult = customizeFormClueManager.getClueInfoByMarketingActivityIdAndFsUserId(ea, marketingActivityId, userId, false, 0);
        }

        if (null == customizeFormClueInfoResult) {
            log.warn("customizeFormClueManager.getClueInfoByMarketingActivityId failed, ea={}, marketingActivityId={}, userId={}", ea, marketingActivityId, userId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        List<String> leadsIds = customizeFormClueInfoResult.getLeadIds();
        Integer totalCount = customizeFormClueInfoResult.getResultCount();
        List<GetClueListByMarketingAcvitiyIdUnitResult> resultList = new ArrayList<>();
        PageResult<GetClueListByMarketingAcvitiyIdUnitResult> pageResult = new PageResult<>();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        pageResult.setOtherData(totalCount);
        pageResult.setResult(resultList);
        log.info("leadsIds={}", leadsIds);
        if (CollectionUtils.isNotEmpty(leadsIds)) {
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(pageSize);
            searchQuery.setOffset((pageNum-1)*pageSize);
            searchQuery.addFilter(CrmV2LeadFieldEnum.ID.getNewFieldName(), leadsIds, IN);
            Map<String, String> weChatAvatarUrlMap = userMarketingAccountManager.getLeadAndWeChatAvatarUrlMap(ea, -10000, leadsIds);
            try {
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> crmOpportunityResult = crmV2Manager.getList(ea, -10000, LeadsFieldContants.API_NAME, searchQuery);
                List<ObjectData> dataList = crmOpportunityResult.getDataList();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
                    pageResult.setTotalCount(crmOpportunityResult.getTotal());
                    pageResult.setOtherData(totalCount - crmOpportunityResult.getTotal());
                    for (ObjectData objectData : dataList) {
                        LeadsData leadsData = LeadsData.wrap(objectData);
                        GetClueListByMarketingAcvitiyIdUnitResult result = new GetClueListByMarketingAcvitiyIdUnitResult();
                        result.setClueId(leadsData.getId());
                        result.setName(leadsData.getName());
                        String avatar = weChatAvatarUrlMap.get(leadsData.getId());
                        result.setAvatar(avatar);
                        resultList.add(result);
                    }
                }
            } catch (CrmBusinessException e) {
                return new Result(SHErrorCode.CRM_BUSINESS_ERROR, e.getMessage());
            }
        }

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> listQywxMarketingActivityEmployeeRanking(String ea, Integer userId,ListQywxMarketingActivityEmployeeRankingArg arg) {
        return groupSendMessageManager.listQywxMarketingActivityEmployeeRanking(ea, arg);
    }

    @Override
    public Result<PageResult<ListEmployeeQywxGroupSendDetailResult>> listEmployeeQywxGroupSendDetail(String ea, Integer fsUserId, ListQywxMarketingActivityEmployeeRankingArg arg) {
        return groupSendMessageManager.listEmployeeQywxGroupSendDetail(ea, arg);
    }

    @Override
    public Result<PageResult<GroupSendMsgUserMarketingListResult>> listQywxGroupSendUserMarketing(String ea, Integer fsUserId, ListQywxGroupSendUserMarketingArg arg) {
        PageResult<GroupSendMsgUserMarketingListResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        QywxGroupSendTaskEntity taskEntity = sendTaskDAO.getByMarketingActivityId(arg.getMarketingActivityId());
        if (taskEntity == null) {
            log.info("listEmployeeQywxGroupSendDetail send task is not exist ea{} arg:{}", ea, arg);
            return Result.newSuccess(pageResult);
        }
        com.github.mybatis.pagination.Page<ListEmployeeQywxGroupSendDetailResult> page = new com.github.mybatis.pagination.Page<>(arg.getPageNum(), arg.getPageSize(), true);

        if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
            List<String> externalIdList = groupSendResultDAO.listQywxGroupSendUserMarketing(ea, arg.getMarketingActivityId(), arg.getEmployeeId(), arg.getStatus(), page);
            if (CollectionUtils.isEmpty(externalIdList)) {
                return Result.newSuccess(pageResult);
            }
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("external_user_id", externalIdList, IN);
            List<PaasQueryArg.Condition> conditionList = Lists.newArrayList(condition);
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, externalIdList.size());
            paasQueryArg.setFilters(conditionList);
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            List<String> selectFields = conditionList.stream().map(PaasQueryArg.Condition::getFieldName).collect(Collectors.toList());
            findByQueryV3Arg.setSelectFields(selectFields);
            InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, -10000, findByQueryV3Arg);
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            if (CollectionUtils.isEmpty(objectDataList)) {
                return Result.newSuccess(pageResult);
            }
            Map<String, String> externalUserIdMap = objectDataList.stream().collect(Collectors.toMap(e -> e.getString("external_user_id"), ObjectData::getId, (v1, v2) -> v1));
            List<String> objectIdList = Lists.newArrayList(externalUserIdMap.values());
            List<UserMarketingCrmWxWorkExternalUserRelationEntity> relationEntityList = userMarketingCrmWxWorkExternalUserRelationDao.entityListByCrmWxWorkExternalUserIds(ea, objectIdList);
            Map<String, String> objectIdMap = relationEntityList.stream().collect(Collectors.toMap(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId,
                    UserMarketingCrmWxWorkExternalUserRelationEntity::getUserMarketingId, (v1, v2) -> v1));
            List<String> userMarketingIds = relationEntityList.stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getUserMarketingId).collect(Collectors.toList());;
            Map<String, com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, SuperUserConstants.USER_ID, userMarketingIds, InfoStateEnum.DETAIL);
            List<GroupSendMsgUserMarketingListResult> resultList = Lists.newArrayList();
            for (String externalId : externalIdList) {
                GroupSendMsgUserMarketingListResult item = new GroupSendMsgUserMarketingListResult();
                String objectId = externalUserIdMap.get(externalId);
                String userMarketingId = objectIdMap.get(objectId);
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingId);
                item.setUserMarketingId(userMarketingId);
                if (userMarketingAccountData != null) {
                    String name = userMarketingAccountData.getWeChatName() == null ?
                            userMarketingAccountData.getName() : userMarketingAccountData.getWeChatName();
                    item.setName(name);
                    item.setWeChatAvatar(userMarketingAccountData.getWeChatAvatar());

                }
                resultList.add(item);
            }
            pageResult.setTotalCount(page.getTotalNum());
            pageResult.setResult(resultList);
            return Result.newSuccess(pageResult);
        } else {
            List<String> taskIds = Lists.newArrayList();
            taskIds.add(taskEntity.getId());
            List<QywxGroupSendGroupResultEntity> sendGroupResultEntities =
                    qywxGroupSendGroupResultDAO.queryGroupResultBySender(taskIds, Lists.newArrayList(arg.getEmployeeId()), null);
            sendGroupResultEntities = sendGroupResultEntities.stream().filter(e -> StringUtils.isNotBlank(e.getSendGroupIds())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sendGroupResultEntities)) {
                return Result.newSuccess(pageResult);
            }
            List<String> groupIdList = Lists.newArrayList();
            for (QywxGroupSendGroupResultEntity entity : sendGroupResultEntities) {
                groupIdList.addAll(JSONObject.parseArray(entity.getSendGroupIds(), String.class));
            };
            pageResult.setTotalCount(groupIdList.size());
            groupIdList = groupIdList.stream().skip((long) arg.getPageSize() * (arg.getPageNum() - 1))
                    .limit(arg.getPageSize()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupIdList)) {
                return Result.newSuccess(pageResult);
            }
            List<GroupSendMsgUserMarketingListResult> resultList = Lists.newArrayList();
            for (String groupId : groupIdList) {
                QueryCustomerGroupListResult groupListResult = groupSendMessageManager.queryCachedGroupDetail(ea,groupId);
                GroupSendMsgUserMarketingListResult item = new GroupSendMsgUserMarketingListResult();
                item.setGroupId(groupId);
                if (groupListResult != null) {
                    item.setGroupName(groupListResult.getGroupName());
                }
                resultList.add(item);
            }
            pageResult.setResult(resultList);
            return Result.newSuccess(pageResult);
        }
    }

    @Override
    public Result<Void> exportEmployeeQywxGroupSendDetail(String ea, Integer fsUserId, ListQywxMarketingActivityEmployeeRankingArg arg) {
        QywxGroupSendTaskEntity taskEntity = sendTaskDAO.getByMarketingActivityId(arg.getMarketingActivityId());
        if (taskEntity == null) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1074));
        }
        ThreadPoolUtils.execute(() -> {
            int pageSize = 1000;
            arg.setPageNum(1);
            arg.setPageSize(pageSize);
            Result<PageResult<ListEmployeeQywxGroupSendDetailResult>> result = groupSendMessageManager.listEmployeeQywxGroupSendDetail(ea, arg);
            if (CollectionUtils.isEmpty(result.getData().getResult())) {
                return ;
            }
            List<I18nKeyEnum> titleList;
            List<List<Object>> dataList;
            if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                titleList = EMPLOYEE_QYWX_USER_GROUP_SEND_DETAIL_LIST;
                dataList = buildEmployeeUserSendDataList(result.getData().getResult());
            } else {
                titleList = EMPLOYEE_QYWX_GROUP_GROUP_SEND_DETAIL_LIST;
                dataList = buildEmployeeGroupSendDataList(result.getData().getResult());
            }

            SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1095)
                    + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
            SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
            sxssfSheet.setDefaultColumnWidth(20);

            // 填充标题行
            ExcelUtil.fillTitles(sxssfSheet, titleList);
            // 填充第一页的数据
            ExcelUtil.appendContent(sxssfSheet, dataList, 1);
            Integer totalCount = result.getData().getTotalCount();
            int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
            if (totalPage > 1) {
                for (int i = 2; i <= totalPage; i++) {
                    arg.setPageNum(i);
                    result = groupSendMessageManager.listEmployeeQywxGroupSendDetail(ea, arg);
                    if (CollectionUtils.isEmpty(result.getData().getResult())) {
                        break;
                    }
                    if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                        dataList = buildEmployeeUserSendDataList(result.getData().getResult());
                    } else {
                        dataList = buildEmployeeGroupSendDataList(result.getData().getResult());
                    }
                    // 追加填充每一页的数据
                    ExcelUtil.appendContent(sxssfSheet, dataList, (i - 1) * pageSize + 1);
                }
            }
            pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> exportQywxMarketingActivityEmployeeRanking(String ea, Integer fsUserId, ListQywxMarketingActivityEmployeeRankingArg arg) {
        QywxGroupSendTaskEntity taskEntity = sendTaskDAO.getByMarketingActivityId(arg.getMarketingActivityId());
        if (taskEntity == null) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1074));
        }
        ThreadPoolUtils.execute(() -> {
            arg.setPageNum(1);
            arg.setPageSize(10000);
            Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> result = groupSendMessageManager.listQywxMarketingActivityEmployeeRanking(ea, arg);
            if (CollectionUtils.isEmpty(result.getData().getResult())) {
                return;
            }
            List<I18nKeyEnum> titleList;
            if (taskEntity.getChatType() == QywxGroupSendOjbectTypeEnum.SEND_TO_USER.getType()) {
                titleList = SEND_TO_USER_TITLE_LIST;
            } else {
                titleList = SEND_TO_GROUP_TITLE_LIST;
            }
            List<List<Object>> dataList = buildSendToUserDataList(result.getData().getResult());

            SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1095)
                    + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
            SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
            sxssfSheet.setDefaultColumnWidth(20);

            // 填充标题行
            ExcelUtil.fillTitles(sxssfSheet, titleList);
            // 填充第一页的数据
            ExcelUtil.appendContent(sxssfSheet, dataList, 1);

            pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    private List<List<Object>> buildSendToUserDataList(List<ListQywxMarketingActivityEmployeeRankingResult> resultList) {
        List<List<Object>> data = Lists.newArrayList();
        for (ListQywxMarketingActivityEmployeeRankingResult result : resultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(result.getCustomerName());
            String status ;
            switch (result.getStatus()) {
                case 0:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1172);
                    break;
                case 1:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1175);
                    break;
                case 2:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1178);
                    break;
                case 3:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1181);
                    break;
                case 10:
                    status = I18nUtil.get(I18nKeyEnum.MARK_QYWX_MOMENTMANAGER_1634);
                    break;
                default:
                    status = I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCUPLOADMANAGER_1038);
                    break;
            }
            objectList.add(status);
            String sendTime = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1191);
            if (result.getSendTime() != null) {
                sendTime = DateUtil.format(result.getSendTime());
            }
            objectList.add(sendTime);
            objectList.add(result.getEmployeeName());
            objectList.add(result.getDepartment());
            data.add(objectList);
        }
        return data;
    }

    private List<List<Object>> buildEmployeeGroupSendDataList(List<ListEmployeeQywxGroupSendDetailResult> resultList) {
        List<List<Object>> data = Lists.newArrayList();
        for (ListEmployeeQywxGroupSendDetailResult result : resultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(result.getEmployeeName());
            objectList.add(result.getDepartment());
            String status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1175) ;
            objectList.add(status);
            String sendTime = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1191);
            if (result.getSendTime() != null) {
                sendTime = DateUtil.format(result.getSendTime());
            }
            objectList.add(sendTime);
            objectList.add(result.getSuccessGroupCount());
            data.add(objectList);
        }
        return data;
    }
    private List<List<Object>> buildEmployeeUserSendDataList(List<ListEmployeeQywxGroupSendDetailResult> resultList) {
        List<List<Object>> data = Lists.newArrayList();
        for (ListEmployeeQywxGroupSendDetailResult result : resultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(result.getEmployeeName());
            objectList.add(result.getDepartment());
            String status ;
            switch (result.getStatus()) {
                case 0:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1172);
                    break;
                case 1:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1175);
                    break;
                default:
                    status = I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCUPLOADMANAGER_1038);
                    break;
            }
            objectList.add(status);

            String sendTime = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1191);
            if (result.getSendTime() != null) {
                sendTime = DateUtil.format(result.getSendTime());
            }
            objectList.add(sendTime);
            objectList.add(result.getUnSendCount());
            objectList.add(result.getSentCount());
            objectList.add(result.getOutOfLimitCount());
            objectList.add(result.getNotFriendRelationCount());
            data.add(objectList);
        }
        return data;
    }

    private List<List<Object>> buildMomentEmployeeDataList(List<ListQywxMarketingActivityEmployeeRankingResult> resultList) {
        List<List<Object>> data = Lists.newArrayList();
        for (ListQywxMarketingActivityEmployeeRankingResult result : resultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(result.getEmployeeName());
            objectList.add(result.getDepartment());
            String status ;
            switch (result.getStatus()) {
                case 0:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1264);
                    break;
                case 1:
                    status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1267);
                    break;
                default:
                    status = I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCUPLOADMANAGER_1038);
                    break;
            }
            objectList.add(status);
            objectList.add(result.getAssociatedCustomerCount());
            objectList.add(result.getCustomerCount());
            data.add(objectList);
        }
        return data;
    }

    @Override
    public Result<Void> cachedGroupDetail() {
        groupSendMessageManager.cachedGroupDetail();
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> listSopQywxMsgEmployeeRanking(String ea, Integer userId, ListSopQywxMsgEmployeeRankingArg arg) {
        return groupSendMessageManager.listSopQywxMsgEmployeeRanking(ea, arg);
    }

    @Override
    public Result<PageResult<MomentCustomer>> listMomentCustomer(String ea, MomentCustomerArg arg) {
        return Result.newSuccess(groupSendMessageManager.listMomentCustomer(ea, arg));
    }

    @Override
    public Result<Void> exportMomentCustomer(String ea, Integer fsUserId, MomentCustomerArg arg) {
        QywxMomentTaskEntity taskEntity = qywxMomentTaskDAO.getByMarketingActivityId(ea, arg.getMarketingActivityId());
        if (taskEntity == null) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1074));
        }
        ThreadPoolUtils.execute(() -> {
            int pageSize = 500;
            arg.setPageNum(1);
            arg.setPageSize(pageSize);
            PageResult<MomentCustomer> result = groupSendMessageManager.listMomentCustomer(ea, arg);
            if (CollectionUtils.isEmpty(result.getResult())) {
                return ;
            }

            SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1313)
                    + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
            SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
            sxssfSheet.setDefaultColumnWidth(20);

            // 填充标题行
            ExcelUtil.fillTitles(sxssfSheet, MOMENT_CUSTOMER_TITLE_LIST);

            List<List<Object>> dataList = buildMomentCustomerData(result.getResult());
            // 填充第一页的数据
            ExcelUtil.appendContent(sxssfSheet, dataList, 1);
            Integer totalCount = result.getTotalCount();
            int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
            if (totalPage > 1) {
                for (int i = 2; i <= totalPage; i++) {
                    arg.setPageNum(i);
                    result = groupSendMessageManager.listMomentCustomer(ea, arg);
                    if (CollectionUtils.isEmpty(result.getResult())) {
                        break;
                    }
                    dataList = buildMomentCustomerData(result.getResult());
                    // 追加填充每一页的数据
                    ExcelUtil.appendContent(sxssfSheet, dataList, (i - 1) * pageSize + 1);
                }
            }
            pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    private List<List<Object>> buildMomentCustomerData(List<MomentCustomer> resultList) {
        List<List<Object>> data = Lists.newArrayList();
        for (MomentCustomer result : resultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(result.getUserName());
            String status = I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCUPLOADMANAGER_1038);
            if (result.getPublishStatus() == 0) {
                status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1350);
            } else if (result.getPublishStatus() == 1) {
                status = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1352);
            }
            objectList.add(status);
            String sendTime = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1191);
            if (result.getSendTime() != null) {
                sendTime = DateUtil.format(result.getSendTime());
            }
            objectList.add(sendTime);
            objectList.add(result.getEmployeeName());
            objectList.add(result.getDepartment());
            data.add(objectList);
        }
        return data;
    }

    @Override
    public Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> listQywxMomentTaskEmployeeRanking(String ea, Integer userId,ListQywxMarketingActivityEmployeeRankingArg arg) {
        return momentManager.listQywxMomentTaskEmployeeRanking(ea, arg);
    }

    @Override
    public Result<Void> exportMomentEmployeeRanking(String ea, Integer fsUserId, ListQywxMarketingActivityEmployeeRankingArg arg) {
        int pageSize = 1000;
        arg.setPageNum(1);
        arg.setPageSize(pageSize);
        if (arg.getStatus() != null && (arg.getStatus() != 0 && arg.getStatus() != 1)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        ThreadPoolUtils.execute(() -> {
            Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> pageResult = momentManager.listQywxMomentTaskEmployeeRanking(ea, arg);
            List<ListQywxMarketingActivityEmployeeRankingResult> resultList = pageResult.getData().getResult();
            if (CollectionUtils.isEmpty(resultList)) {
                return ;
            }
            List<List<Object>> dataList = buildMomentEmployeeDataList(resultList);
            SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1391) + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
            SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
            sxssfSheet.setDefaultColumnWidth(20);
            // 填充标题行
            ExcelUtil.fillTitles(sxssfSheet, MOMENT_EMPLOYEE_TITLE_LIST);
            // 填充第一页的数据
            ExcelUtil.appendContent(sxssfSheet, dataList, 1);
            Integer totalCount = pageResult.getData().getTotalCount();
            int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
            if (totalPage > 1) {
                for (int i = 2; i <= totalPage; i++) {
                    arg.setPageNum(i);
                    Result<PageResult<ListQywxMarketingActivityEmployeeRankingResult>> tempResult = momentManager.listQywxMomentTaskEmployeeRanking(ea, arg);
                    if (CollectionUtils.isEmpty(tempResult.getData().getResult())) {
                        break;
                    }
                    // 追加填充每一页的数据
                    ExcelUtil.appendContent(sxssfSheet,buildMomentEmployeeDataList(tempResult.getData().getResult()), (i - 1) * pageSize + 1);
                }
            }
            pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<GetEmployeeRankingByMarketingActivityResult> getEmployeeRankingByMarketingActivity(GetEmployeeRankingByMarketingActivityVo vo) {
        List<GetEmployeeRankingByMarketingActivityEntity> employeeRankingEntities;

        if (vo.getRecentDay() != -1) {

            DateTime endDate = DateTime.now();
            DateTime startDate = endDate.minusDays(vo.getRecentDay() - 1);
            employeeRankingEntities = marketingActivityDayStatisticDAO
                .getActivityRankingByMarketingActivityId(vo.getMarketingActivityId(), startDate.toDate(), endDate.toDate(), vo.getSearchType(), vo.getTopRankingCount());
        } else {
            employeeRankingEntities = marketingActivityDayStatisticDAO.getActivityRankingByMarketingActivityId(vo.getMarketingActivityId(), null, null, vo.getSearchType(), vo.getTopRankingCount());
        }

        List<GetEmployeeRankingByMarketingActivityResult.ActivityRankingResult> activityRankingResults = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(employeeRankingEntities)) {
            List<Integer> userList = Lists.newArrayList();
            employeeRankingEntities.forEach(value -> userList.add(value.getFsUserId()));
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), userList, true);
            for (GetEmployeeRankingByMarketingActivityEntity employeeRankingEntity : employeeRankingEntities) {
                GetEmployeeRankingByMarketingActivityResult.ActivityRankingResult activityRankingResult = new ActivityRankingResult();
                activityRankingResult.setCount(employeeRankingEntity.getCount());
                activityRankingResult.setFsUserId(employeeRankingEntity.getFsUserId());
                activityRankingResult.setName(fsEmployeeMsgMap.get(employeeRankingEntity.getFsUserId()).getName());
                activityRankingResults.add(activityRankingResult);
            }
        }
        GetEmployeeRankingByMarketingActivityResult result = new GetEmployeeRankingByMarketingActivityResult();
        result.setDataList(activityRankingResults);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PageResult<ListMarketingActivityEmployeeRankingResult>> listMarketingActivityEmployeeRanking(String ea, Integer fsUserId, ListMarketingActivityEmployeeRankingArg arg){
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        //员工推广排名结果
        List<ListMarketingActivityEmployeeRankingResult> listActivityRankingResult = Lists.newArrayList();
        //未推广人数，未推广员工姓名，已推广人数
        EmployeeRankingTotalStat employeeRankingTotalStat = new EmployeeRankingTotalStat();
        PageResult<ListMarketingActivityEmployeeRankingResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(listActivityRankingResult);
        pageResult.setTotalCount(0);

        GetMarketingActivityArg getMarketingActivityArg = new GetMarketingActivityArg();
        getMarketingActivityArg.setId(arg.getMarketingActivityId());
        getMarketingActivityArg.setPartner(arg.isPartner());
        Result<com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult> marketingActivity = marketingActivityService.getMarketingActivity(ea, fsUserId, getMarketingActivityArg);
        if (marketingActivity.getData() != null && marketingActivity.getData().getSpreadStatus() != null
                && SpreadStatusEnum.REVOCATION.getStatus() != marketingActivity.getData().getSpreadStatus()) {
            pageResult.setOtherData(employeeRankingTotalStat);
        }
        boolean isMemberMarketing = marketingActivity.getData() != null && MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType() == marketingActivity.getData().getSpreadType();
        List<MarketingActivityEmployeedsByClueDTO> employeedsByClueDTOList;
        Boolean dingTalkVersion = dingManager.isDingAddressbook(ea);
        List<Integer> employeeRange = null;
        if (isMemberMarketing) {
            employeeRange = getMemberEmployeeId(ea, arg.getFilters());
            if (CollectionUtils.isEmpty(employeeRange) && CollectionUtils.isNotEmpty(arg.getFilters())) {
                return Result.newSuccess(pageResult);
            }
        } else {
            employeeRange = statisticService.getStaticsticsVisitUserRange(ea, fsUserId, arg.getEmployeeRange(), arg.getDepartmentRange(), false);
        }
        String employeeRangeStr = employeeRange == null ? null : StringUtils.join(employeeRange, ",");
//        if(arg.getPromoteStatue()!=null && arg.getPromoteStatue()==1){
//            //当前分页查询未推广数据
//            employeedsByClueDTOList = customizeFormDataUserDAO.queryPageMarketingActivityUncompleteTaskEmployeedsByClueCount(ea, arg.getMarketingActivityId(), page, dingTalkVersion, employeeRangeStr);
//        } else if (arg.getPromoteStatue() != null && arg.getPromoteStatue() == 2) {
//            employeedsByClueDTOList = customizeFormDataUserDAO.queryPageMarketingActivityCompleteTaskEmployeedsByClueCount(ea, arg.getMarketingActivityId(), page, dingTalkVersion, employeeRangeStr);
//        } else {
//            //当前分页查询数据（包括已推广和未推广）
//            employeedsByClueDTOList = customizeFormDataUserDAO.queryPageMarketingActivityEmployeedsByClueCount(ea, arg.getMarketingActivityId(), page, dingTalkVersion, employeeRangeStr);
//        }
        employeedsByClueDTOList = customizeFormDataUserDAO.queryPageMarketingActivityEmployeesByClueCountV2(ea, arg.getMarketingActivityId(), page, arg.getPromoteStatue(), employeeRangeStr);

        if(org.apache.commons.collections4.CollectionUtils.isEmpty(employeedsByClueDTOList)){
            return Result.newSuccess(pageResult);
        }

        List<Integer> pageActivityEmployeeIds = employeedsByClueDTOList.stream().map(MarketingActivityEmployeedsByClueDTO::getUserId).filter(Objects::nonNull).collect(Collectors.toList());

      //  pageResult.setTotalCount(page.getTotalNum());
        //已推广员工
        List<Integer> spreadMarketingActivityEmployeeIds = marketingActivityEmployeeStatisticDao.queryAllMarketingActivityEmployeeIds(ea, arg.getMarketingActivityId());

        //查询对应的是否已读状态
        List<SpreadTaskEntity> spreadTaskEntities = spreadTaskDAO.batchGetSpreadLookUpStatus(ea,arg.getMarketingActivityId(),pageActivityEmployeeIds);
        Map<Integer, Boolean> employeeLookUpStatusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spreadTaskEntities)){
            employeeLookUpStatusMap = spreadTaskEntities.stream().collect(Collectors.toMap(SpreadTaskEntity::getUserId,SpreadTaskEntity::getLookUpStatus,(k1,k2) -> k1));
        }
        //设置了推广任务的员工，可能包含没开通名片的
        List<Integer> employeeIds = pageActivityEmployeeIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<MarketingActivityEmployeeStatisticEntity> employeeRankingByMarketingActivityEntityList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(employeeIds)){
            employeeRankingByMarketingActivityEntityList = marketingActivityEmployeeStatisticDao.listMarketingActivityEmployeeRankingByEmployeeIds(ea, arg.getMarketingActivityId(), employeeIds);
        }
        Map<Integer, MarketingActivityEmployeeStatisticEntity> marketingActivityEmployeeStatisticEntityMap = null;
        if (CollectionUtils.isNotEmpty(employeeRankingByMarketingActivityEntityList)){
            marketingActivityEmployeeStatisticEntityMap = employeeRankingByMarketingActivityEntityList.stream().collect(Collectors.toMap(MarketingActivityEmployeeStatisticEntity::getFsUserId, Function.identity(), (k1,k2) -> k1));
        }
        Map<Integer, ObjectUserMarketingStatisticsResult> userIdToStatisticsMap = getUserIdToMarketingStatisticsMap(ea, fsUserId, arg.getMarketingActivityId(), employeeIds);
        // 全员营销邮件推广统计数据处理
        Map<Integer, EmployeeEmailSendResult> employeeEmailSendResultMap = noticeManager.getEmployeeEmailSendCount(ea, employeeIds, arg.getMarketingActivityId());

        //根据用户id得到用户详细信息
        if (!dingTalkVersion) {
//            pageResult.setTotalCount(customizeFormDataUserDAO.queryCountMarketingActivityEmployeedsByClueCount(ea, arg.getMarketingActivityId(), employeeRangeStr));
            pageResult.setTotalCount(page.getTotalNum());
            List<Integer> spreadTaskEmployeeIds = spreadTaskDAO.getTotalUserId(ea, arg.getMarketingActivityId());
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, employeeIds, true);
            for (MarketingActivityEmployeedsByClueDTO employeedsByClueDTO : employeedsByClueDTOList) {
                ListMarketingActivityEmployeeRankingResult employeeRankingResult = new ListMarketingActivityEmployeeRankingResult();
                employeeRankingResult.setFsUserId(employeedsByClueDTO.getUserId());
                fillEmployeeInfo(employeedsByClueDTO, fsEmployeeMsgMap, employeeRankingResult);
                employeeRankingResult.setLookUpStatus(false);
                if (employeeLookUpStatusMap.containsKey(employeedsByClueDTO.getUserId())) {
                    employeeRankingResult.setLookUpStatus(employeeLookUpStatusMap.get(employeedsByClueDTO.getUserId()));
                }
                if (employeeEmailSendResultMap.containsKey(employeedsByClueDTO.getUserId())) {
                    EmployeeEmailSendResult employeeEmailSendResult = employeeEmailSendResultMap.get(employeedsByClueDTO.getUserId());
                    if (employeeEmailSendResult != null) {
                        employeeRankingResult.setSendCount(employeeEmailSendResult.getSendCount());
                        employeeRankingResult.setReceiveCount(employeeEmailSendResult.getReceiveCount());
                    }
                } else {
                    employeeRankingResult.setSendCount(0);
                    employeeRankingResult.setReceiveCount(0);
                }
                if (marketingActivityEmployeeStatisticEntityMap != null && marketingActivityEmployeeStatisticEntityMap.get(employeedsByClueDTO.getUserId()) != null) {
                    MarketingActivityEmployeeStatisticEntity entity = marketingActivityEmployeeStatisticEntityMap.get(employeedsByClueDTO.getUserId());
                    Integer fsUserClueNum = employeedsByClueDTO.getClueCount();
                    employeeRankingResult.setLeadAccumulationCount(fsUserClueNum != null ? fsUserClueNum : 0);
                    employeeRankingResult.setSpreadCount(entity.getSpreadCount() == null ? 0 : entity.getSpreadCount());
                    ObjectUserMarketingStatisticsResult statisticsResult = userIdToStatisticsMap.get(employeedsByClueDTO.getUserId());
                    if (statisticsResult != null) {
                        employeeRankingResult.setLookUpCount(statisticsResult.getLookUpCount());
                        employeeRankingResult.setLookUpUserCount(statisticsResult.getLookUpUserCount());
                        employeeRankingResult.setForwardCount(statisticsResult.getForwardCount());
                        employeeRankingResult.setForwardUserCount(statisticsResult.getForwardUserCount());
                    }
                } else {
                    //没状态直接获取用户姓名
                    //employeeRankingResult.setName(employeeMsg.getName());
                    employeeRankingResult.setForwardCount(0);
                    employeeRankingResult.setLeadAccumulationCount(0);
                    employeeRankingResult.setLookUpCount(0);
                    employeeRankingResult.setSpreadCount(0);
                    employeeRankingResult.setLookUpUserCount(0);
                    employeeRankingResult.setForwardUserCount(0);
                }
                listActivityRankingResult.add(employeeRankingResult);
            }

        //未推广员工
        List<Integer> uncompleteEmployeeIds = Lists.newArrayList();
        //已推广员工数量
        int completedTaskUserCount = spreadMarketingActivityEmployeeIds == null ? 0 : spreadMarketingActivityEmployeeIds.size();
        for (Integer taskUserId : spreadTaskEmployeeIds){
            if (completedTaskUserCount == 0){
                //说明都是未推广的
                uncompleteEmployeeIds.add(taskUserId);
            } else {
                //已推广员工中不包含该员工，说明该员工未推广
                if (!spreadMarketingActivityEmployeeIds.contains(taskUserId)){
                    uncompleteEmployeeIds.add(taskUserId);
                }
            }
            //未推广员工最多只取10个
            if (uncompleteEmployeeIds.size() >= 10){
                break;
                }
            }
            employeeRankingTotalStat.setCompletedTaskUserCount(completedTaskUserCount);
            employeeRankingTotalStat.setUncompleteTaskUserCount(pageResult.getTotalCount() - completedTaskUserCount);
//            if(arg.getPromoteStatue()!=null && arg.getPromoteStatue()==1){
//                //未推广总数
//                pageResult.setTotalCount(pageResult.getTotalCount() - completedTaskUserCount);
//            } else if (arg.getPromoteStatue()!=null && arg.getPromoteStatue()==2) {
//                pageResult.setTotalCount(completedTaskUserCount);
//            }
            if (arg.getPromoteStatue()!=null && arg.getPromoteStatue()==1) {
                employeeRankingTotalStat.setUncompleteTaskUserCount(pageResult.getTotalCount());
            }
            if (CollectionUtils.isNotEmpty(uncompleteEmployeeIds)) {
                Map<Integer, FSEmployeeMsg> uncomplateTaskFsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, uncompleteEmployeeIds, true);
                if (uncomplateTaskFsEmployeeMsgMap != null) {
                    List<String> uncompleteTaskNameList = uncomplateTaskFsEmployeeMsgMap.values().stream().map(fsEmployeeMsg -> fsEmployeeMsg.getName()).collect(Collectors.toList());
                    employeeRankingTotalStat.setUncompleteTaskUserName(uncompleteTaskNameList);
                }
            }
        }else{
            //所有需推广的钉钉id
            List<SpreadTaskEntity> allSpreadUser = spreadTaskDAO.queryOutUserIdByEa(ea, arg.getMarketingActivityId());
//            pageResult.setTotalCount(allSpreadUser.size());
            pageResult.setTotalCount(page.getTotalNum());
            List<String> totalOutUserId = allSpreadUser.stream().map(t->t.getOutUserId()).collect(Collectors.toList());
//            Map<String,Integer> outIdToFsIDMap = new HashMap<>();
            Map<Integer,String> userIdToOutIdMap = new HashMap<>();
            for (SpreadTaskEntity spreadTaskEntity : allSpreadUser) {
                if (null != spreadTaskEntity.getUserId()) {
                    userIdToOutIdMap.put(spreadTaskEntity.getUserId(), spreadTaskEntity.getOutUserId());
                }
            }
            //已推广的钉钉id
            List<String> spreadOutUserId = new ArrayList<>();
            Map<String, DingStaffInfoResult.UserGetResponse> outUidToNameMap = getOutUidToNameMap(totalOutUserId, ea);
            QueryDingMiniAppDepartmentArg deptArg = new QueryDingMiniAppDepartmentArg();
            deptArg.setFsEa(ea);
            com.facishare.marketing.common.result.Result<List<DingMiniAppDepartmentResult>> miniAppDepartmentResult = dingMiniAppDepartmentService.queryDingMiniAppDepartment(deptArg);
            Map<Integer, DingMiniAppDepartmentResult> departmentResultMap = miniAppDepartmentResult.getData().stream().collect(Collectors.toMap(DingMiniAppDepartmentResult::getDeptId, data -> data, (v1, v2) -> v1));
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = Maps.newHashMap();
            if (isMemberMarketing) {
                fsEmployeeMsgMap  = fsAddressBookManager.getEmployeeInfoByUserIds(ea, pageActivityEmployeeIds, false);
            }
            for (MarketingActivityEmployeedsByClueDTO temp : employeedsByClueDTOList) {
                if (null == temp.getOutUserId() && null != temp.getUserId()) {
                    temp.setOutUserId(userIdToOutIdMap.get(temp.getUserId()));
                }
                if (spreadMarketingActivityEmployeeIds.contains(temp.getUserId()) && null != temp.getUserId()) {
                    if (null == temp.getOutUserId()) {
                        spreadOutUserId.add(userIdToOutIdMap.get(temp.getUserId()));
                    } else {
                        spreadOutUserId.add(temp.getOutUserId());
                    }
                }
                ListMarketingActivityEmployeeRankingResult employeeRankingResult = new ListMarketingActivityEmployeeRankingResult();
                if (isMemberMarketing) {
                    FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(temp.getUserId());
                    employeeRankingResult.setName(fsEmployeeMsg == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629) : fsEmployeeMsg.getName());
                    employeeRankingResult.setMobile(fsEmployeeMsg == null ? I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1630) : fsEmployeeMsg.getMobile());
                } else {
                    fillDingEmployeeInfo(temp, outUidToNameMap, employeeRankingResult, departmentResultMap);
                }
                //处理员工查看状态
                employeeRankingResult.setLookUpStatus(false);
                if (employeeLookUpStatusMap.containsKey(temp.getUserId())) {
                    employeeRankingResult.setLookUpStatus(employeeLookUpStatusMap.get(temp.getUserId()));
                }
                if (marketingActivityEmployeeStatisticEntityMap != null && marketingActivityEmployeeStatisticEntityMap.get(temp.getUserId()) != null) {
                    MarketingActivityEmployeeStatisticEntity entity = marketingActivityEmployeeStatisticEntityMap.get(temp.getUserId());
                    // 获取线索数从表单统计
                    Integer fsUserClueNum = temp.getClueCount();
                    employeeRankingResult.setLeadAccumulationCount(fsUserClueNum != null ? fsUserClueNum : 0);
                    employeeRankingResult.setSpreadCount(entity.getSpreadCount() == null ? 0 : entity.getSpreadCount());
                    ObjectUserMarketingStatisticsResult statisticsResult = userIdToStatisticsMap.get(temp.getUserId());
                    if (statisticsResult != null) {
                        employeeRankingResult.setLookUpCount(statisticsResult.getLookUpCount());
                        employeeRankingResult.setLookUpUserCount(statisticsResult.getLookUpUserCount());
                        employeeRankingResult.setForwardCount(statisticsResult.getForwardCount());
                        employeeRankingResult.setForwardUserCount(statisticsResult.getForwardUserCount());
                    }
                } else {
                    employeeRankingResult.setForwardCount(0);
                    employeeRankingResult.setLeadAccumulationCount(0);
                    employeeRankingResult.setLookUpCount(0);
                    employeeRankingResult.setSpreadCount(0);
                    employeeRankingResult.setLookUpUserCount(0);
                    employeeRankingResult.setForwardUserCount(0);
                }
                listActivityRankingResult.add(employeeRankingResult);

            }
            //已推广的id  spreadMarketingActivityEmployeeIds


            int complateCount = spreadMarketingActivityEmployeeIds == null ? 0 : spreadMarketingActivityEmployeeIds.size();
            employeeRankingTotalStat.setCompletedTaskUserCount(complateCount);
            employeeRankingTotalStat.setUncompleteTaskUserCount(pageResult.getTotalCount() - complateCount);
            if(arg.getPromoteStatue()!=null && arg.getPromoteStatue()==1){
                //未推广总数
                pageResult.setTotalCount(pageResult.getTotalCount() - complateCount);
            } else if (arg.getPromoteStatue()!=null && arg.getPromoteStatue()==2) {
                pageResult.setTotalCount(complateCount);
            }
            //未推广列表
            List<String> uncompleteEmployeeIds = new ArrayList<>() ;
            if (totalOutUserId.size() > spreadOutUserId.size()) {
                List<String> name = new ArrayList<>();
                int count = 0;
                for (String total : totalOutUserId) {
                    if (count > 4) {
                        break;
                    }
                    if (!spreadOutUserId.contains(total)) {
                        count++;
                        uncompleteEmployeeIds.add(total);
                    }
                }
                for (String uncompleteEmployeeId : uncompleteEmployeeIds) {
                    if(null!=outUidToNameMap.get(uncompleteEmployeeId)){
                        name.add(outUidToNameMap.get(uncompleteEmployeeId).getName());
                    }else{
                        name.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
                    }
                }
                employeeRankingTotalStat.setUncompleteTaskUserName(name);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    private void fillDingEmployeeInfo(MarketingActivityEmployeedsByClueDTO temp, Map<String, DingStaffInfoResult.UserGetResponse> outUidToNameMap, ListMarketingActivityEmployeeRankingResult employeeRankingResult, Map<Integer, DingMiniAppDepartmentResult> departmentResultMap) {
        if (null == outUidToNameMap.get(temp.getOutUserId())) {
            employeeRankingResult.setDepartment(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704));
            employeeRankingResult.setName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
            return;
        }
        employeeRankingResult.setName(outUidToNameMap.get(temp.getOutUserId()).getName());
        List<Integer> departmentList = outUidToNameMap.get(temp.getOutUserId()).getDeptIdList();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(outUidToNameMap.get(temp.getOutUserId()).getDeptIdList())) {
            if (departmentList.get(0) != 1) {
                employeeRankingResult.setDepartment(departmentResultMap.get(departmentList.get(0)).getName());
            } else if (departmentList.size() > 1) {
                employeeRankingResult.setDepartment(departmentResultMap.get(departmentList.get(departmentList.size() - 1)).getName());
            }
        } else {
            employeeRankingResult.setDepartment(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1704));
        }
    }

    private void fillEmployeeInfo(MarketingActivityEmployeedsByClueDTO employeedsByClueDTO, Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap, ListMarketingActivityEmployeeRankingResult employeeRankingResult) {
        FSEmployeeMsg employeeMsg = fsEmployeeMsgMap.get(employeedsByClueDTO.getUserId());
        if (employeeMsg == null) {
            employeeRankingResult.setName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
            return;
        }
        if (employeeMsg.getStatus() != null) {
            if (employeeMsg.getStatus().getValue() == EmployeeEntityStatus.DELETE.getValue()) {
                employeeRankingResult.setName(employeeMsg.getName() + I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1729));
            } else if (employeeMsg.getStatus().getValue() == EmployeeEntityStatus.STOP.getValue()) {
                employeeRankingResult.setName(employeeMsg.getName() + I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1731));
            } else {
                employeeRankingResult.setName(employeeMsg.getName());
            }
        } else {
            employeeRankingResult.setName(employeeMsg.getName());
        }
        employeeRankingResult.setMobile(employeeMsg.getMobile());
        employeeRankingResult.setDepartment(employeeMsg.getDepartment());
    }

    private List<Integer> getMemberEmployeeId(String ea, List<Filter> filterList) {
        List<Integer> employeeRange = Lists.newArrayList();
        if (CollectionUtils.isEmpty(filterList)) {
           return null;
        }
        List<PaasQueryArg.Condition> conditionList = BeanUtil.copyByGson(filterList, PaasQueryArg.Condition.class);
        PaasQueryArg.Condition memberTypeCondition = new PaasQueryArg.Condition(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), Lists.newArrayList(MemberTypeEnum.EMPLOYEE.getType(), MemberTypeEnum.PARTNER.getType()), OperatorConstants.IN);
        conditionList.add(memberTypeCondition);
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.setFilters(conditionList);
        List<ObjectData>  memberObjectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.MEMBER.getName(), Lists.newArrayList("_id"), paasQueryArg);
        if (CollectionUtils.isEmpty(memberObjectDataList)) {
            return employeeRange;
        }
        List<String> memberIdList = memberObjectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByMemberIdList(ea, memberIdList);
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            log.info("会员找不到员工信息,ea: {} memberIdList: {}", ea, memberIdList);
            return employeeRange;
        }
        employeeRange = userRelationEntityList.stream().map(UserRelationEntity::getFsUserId).distinct().collect(Collectors.toList());
        return employeeRange;
    }

    private Map<Integer, ObjectUserMarketingStatisticsResult> getUserIdToMarketingStatisticsMap(String ea, Integer fsUserId, String marketingActivityId, List<Integer> employeeIds) {
        ObjectUserMarketingStatisticsArg statisticsArg = new ObjectUserMarketingStatisticsArg();
        statisticsArg.setMarketingActivityId(marketingActivityId);
        statisticsArg.setEa(ea);
        statisticsArg.setUserId(fsUserId);
        statisticsArg.setSpreadFsUserIdList(employeeIds);
        Map<Integer, ObjectUserMarketingStatisticsResult> userIdToStatisticsMap = Maps.newHashMap();
        try {
            Result<List<ObjectUserMarketingStatisticsResult>> result = statisticService.getObjectMarketingActivityStatisticsBySpreadUserIdList(statisticsArg);
            if (result.isSuccess()) {
                result.getData().forEach(e -> userIdToStatisticsMap.put(e.getSpreadUserId(), e));
            }
        } catch (Exception e) {
            log.error("statisticService.getObjectMarketingActivityStatisticsByUserIdList error", e);
        }
        return userIdToStatisticsMap;
    }


    @Override
    public Result<Void> exportMarketingActivityEmployeeRanking(String ea, Integer fsUserId, ListMarketingActivityEmployeeRankingArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            String excelName = I18nUtil.get(I18nKeyStaticEnum.MARK_STATIC_ENTERPRISESPREADSTATISTICSERVICEIMPL_EXCEL_TITLE) + ".xlsx";
            String unCompleteExcelName = I18nUtil.get(I18nKeyStaticEnum.MARK_STATIC_ENTERPRISESPREADSTATISTICSERVICEIMPL_UNCOMPLETE_EXCEL_TITLE) + ".xlsx";
            ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), arg.getMarketingActivityId());
            boolean isMemberMarketing = objectData != null && String.valueOf(MarketingActivitySpreadTypeEnum.MEMBER_MARKETING_SPREAD.getSpreadType()).equals(objectData.getString("spread_type"));
            boolean isEmployeeEmailSpread = noticeManager.isEmployeeEmailSpread(ea, arg.getMarketingActivityId());
            if((arg.getPromoteStatue()!=null && arg.getPromoteStatue()==1)){
                List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults = listMarketingActivityEmployeeRanking(ea, fsUserId, arg).getData().getResult();
                Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
                excelConfigMap.put(ExcelConfigEnum.FILE_NAME, unCompleteExcelName);
                excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1795));
                excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
                XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
                XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
                xssfSheet.setDefaultColumnWidth(20);
                List<String> titleList = isMemberMarketing ? generateMemberMarketingUncompleteExcelTitleList() : generateUncompleteExcelTitleList();
                List<List<Object>> marketingActivityEmployeeRankingList = isMemberMarketing ? generateMemberMarketingUncompleteExcelDataList(listMarketingActivityEmployeeRankingResults) : generateUncompleteExcelDataList(listMarketingActivityEmployeeRankingResults);
                ExcelUtil.fillContent(xssfSheet, titleList, marketingActivityEmployeeRankingList);
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, unCompleteExcelName, ea, fsUserId);
            } else if (arg.getPromoteStatue()!=null && arg.getPromoteStatue()==2) {
                List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults = listMarketingActivityEmployeeRanking(ea, fsUserId, arg).getData().getResult();
                Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
                excelConfigMap.put(ExcelConfigEnum.FILE_NAME, excelName);
                excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1808));
                excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
                XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
                XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
                xssfSheet.setDefaultColumnWidth(20);
                List<String> titleList = isMemberMarketing ? generateMemberMarketingExcelTitleList() : generateExcelTitleList(isEmployeeEmailSpread);
                List<List<Object>> marketingActivityEmployeeRankingList = isMemberMarketing ? generateMemberMarketingExcelDataList(listMarketingActivityEmployeeRankingResults)
                        : generateExcelDataList(listMarketingActivityEmployeeRankingResults, isEmployeeEmailSpread);
                ExcelUtil.fillContent(xssfSheet, titleList, marketingActivityEmployeeRankingList);
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, excelName, ea, fsUserId);
            } else {
                List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults = listMarketingActivityEmployeeRanking(ea, fsUserId, arg).getData().getResult();
                Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
                excelConfigMap.put(ExcelConfigEnum.FILE_NAME, excelName);
                excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1821));
                excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
                XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
                XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
                xssfSheet.setDefaultColumnWidth(20);
                List<String> titleList = isMemberMarketing ? generateMemberMarketingExcelTitleList() : generateExcelTitleList(isEmployeeEmailSpread);
                List<List<Object>> marketingActivityEmployeeRankingList = isMemberMarketing ? generateMemberMarketingExcelDataList(listMarketingActivityEmployeeRankingResults)
                        : generateExcelDataList(listMarketingActivityEmployeeRankingResults, isEmployeeEmailSpread);
                ExcelUtil.fillContent(xssfSheet, titleList, marketingActivityEmployeeRankingList);
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, excelName, ea, fsUserId);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    private static List<String> generateExcelTitleList(Boolean isEmployeeEmailSpread) {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_94));
        if (isEmployeeEmailSpread) {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1846));//发送人数
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1847));//送达人数
        } else {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1841));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1842));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1843));
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1844));
        }
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1845));
        return titleList;
    }

    private static List<String> generateMemberMarketingExcelTitleList() {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1851));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_94));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1841));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1842));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1843));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1844));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1845));
        return titleList;
    }

    private static List<String> generateUncompleteExcelTitleList() {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_285));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271));
        return titleList;
    }

    private static List<String> generateMemberMarketingUncompleteExcelTitleList() {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1851));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
        return titleList;
    }

    private static List<List<Object>> generateUncompleteExcelDataList(List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (ListMarketingActivityEmployeeRankingResult result : listMarketingActivityEmployeeRankingResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(result.getName());
            objList.add(result.getDepartment());
            dataList.add(objList);
        }
        return dataList;
    }

    private static List<List<Object>> generateMemberMarketingUncompleteExcelDataList(List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (ListMarketingActivityEmployeeRankingResult result : listMarketingActivityEmployeeRankingResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(result.getName());
            objList.add(result.getMobile());
            dataList.add(objList);
        }
        return dataList;
    }

    private static List<List<Object>> generateExcelDataList(List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults, Boolean isEmployeeEmailSpread) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (ListMarketingActivityEmployeeRankingResult result : listMarketingActivityEmployeeRankingResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(result.getName());
            objList.add(result.getDepartment());
            objList.add(result.getSpreadCount());
            if (isEmployeeEmailSpread) {
                objList.add(result.getSendCount());
                objList.add(result.getReceiveCount());
            } else {
                objList.add(result.getLookUpUserCount());
                objList.add(result.getLookUpCount());
                objList.add(result.getForwardUserCount());
                objList.add(result.getForwardCount());
            }
            objList.add(result.getLeadAccumulationCount());
            dataList.add(objList);
        }
        return dataList;
    }

    private static List<List<Object>> generateMemberMarketingExcelDataList(List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (ListMarketingActivityEmployeeRankingResult result : listMarketingActivityEmployeeRankingResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(result.getName());
            objList.add(result.getMobile());
            objList.add(result.getSpreadCount());
            objList.add(result.getLookUpUserCount());
            objList.add(result.getLookUpCount());
            objList.add(result.getForwardUserCount());
            objList.add(result.getForwardCount());
            objList.add(result.getLeadAccumulationCount());
            dataList.add(objList);
        }
        return dataList;
    }

    private int getSpreadStatus(Integer status, Long startTime, Long nowDate, Long endTime) {
        int spreadStatus = 0;
        if (status == NoticeStatusEnum.UN_SEND.getStatus()) {
            spreadStatus = SpreadStatusEnum.UN_SEND.getStatus();
        } else if (status == NoticeStatusEnum.SENDING.getStatus()) {
            spreadStatus = SpreadStatusEnum.SENDING.getStatus();
        } else if (startTime < nowDate && endTime > nowDate && status != NoticeStatusEnum.REVOCATION.getStatus()) {
            spreadStatus = SpreadStatusEnum.SUCCESS.getStatus();
        } else if (status == NoticeStatusEnum.REVOCATION.getStatus()) {
            spreadStatus = SpreadStatusEnum.REVOCATION.getStatus();
        } else if (endTime < nowDate && status != NoticeStatusEnum.REVOCATION.getStatus()) {
            spreadStatus = SpreadStatusEnum.END.getStatus();
        }
        return spreadStatus;
    }

    /**
     * 伙伴推广统计--推广排行榜
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    @Override
    public Result<PageResult<ListMarketingActivityEmployeeRankingResult>> listMarketingActivityCompanyRanking(String ea, Integer fsUserId, ListMarketingActivityEmployeeRankingArg arg) {
        Page<ListMarketingActivityEmployeeRankingResult> page = new Page<>(arg.getPageNum(), arg.getPageSize(), false);
        //伙伴推广排名结果
        List<ListMarketingActivityEmployeeRankingResult> listActivityRankingResult = Lists.newArrayList();
        //未推广人数，未推广员工姓名，已推广人数
        EmployeeRankingTotalStat employeeRankingTotalStat = new EmployeeRankingTotalStat();
        PageResult<ListMarketingActivityEmployeeRankingResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(listActivityRankingResult);
        pageResult.setTotalCount(0);
        pageResult.setOtherData(employeeRankingTotalStat);
        int totalCount = spreadTaskDAO.countPartnerSpreadTask(ea, Lists.newArrayList(arg.getMarketingActivityId()), null);
        if (totalCount <= 0) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(totalCount);
        //当前分页查询数据（包括已推广和未推广）
        List<MarketingActivityCompanyByClueDTO> employeedsByClueDTOList = spreadTaskDAO.queryPagePartnerSpreadTask(ea, Lists.newArrayList(arg.getMarketingActivityId()), null, page);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(employeedsByClueDTOList)) {
            return Result.newSuccess(pageResult);
        }
        //已推广公司
        List<Long> spreadMarketingActivityCompanyIds = marketingActivityEmployeeStatisticDao.queryAllMarketingActivityCompanyIds(ea, arg.getMarketingActivityId());
        spreadMarketingActivityCompanyIds = spreadMarketingActivityCompanyIds == null ? Lists.newArrayList() : spreadMarketingActivityCompanyIds;
        //设置了推广任务的公司
        List<Long> allOutTenantIdList = spreadTaskDAO.getAllOutTenantIdList(ea, arg.getMarketingActivityId());

        List<OutEmployeeSpreadData> outEmployeeSpreadData = marketingActivityEmployeeStatisticDao.listPartnerSpreadEmployeeNumByMarketingActivity(ea, arg.getMarketingActivityId());
        Map<String, Integer> outTenantIdToSpeadEmployee = new HashMap<>();
        if (CollectionUtils.isNotEmpty(outEmployeeSpreadData)) {
            outTenantIdToSpeadEmployee = outEmployeeSpreadData.stream().collect(Collectors.toMap(OutEmployeeSpreadData::getOutTenantId, OutEmployeeSpreadData::getOutEmployeeSpread, (k1, k2) -> k2));
        }
        List<String> spreadTaskCompanyIds = allOutTenantIdList.stream().map(String::valueOf).collect(Collectors.toList());
        Map<String, String> companyIdToCompanyNameMap = fsAddressBookManager.getCompanyIdToCompanyMap(spreadTaskCompanyIds);
        for (MarketingActivityCompanyByClueDTO employeeByClueDTO : employeedsByClueDTOList) {
            ListMarketingActivityEmployeeRankingResult employeeRankingResult = new ListMarketingActivityEmployeeRankingResult();
            if (companyIdToCompanyNameMap.containsKey(employeeByClueDTO.getOuterTenantId())) {
                employeeRankingResult.setName(companyIdToCompanyNameMap.get(employeeByClueDTO.getOuterTenantId()));
            } else {
                employeeRankingResult.setName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2004));
            }
            employeeRankingResult.setOutTenantSpreadEmployee(outTenantIdToSpeadEmployee.getOrDefault(employeeByClueDTO.getOuterTenantId(), 0));
            employeeRankingResult.setOutTenantId(employeeByClueDTO.getOuterTenantId());
            employeeRankingResult.setSpreadCount(employeeByClueDTO.getSpreadCount());
            employeeRankingResult.setLeadAccumulationCount(employeeByClueDTO.getClueCount());
            employeeRankingResult.setForwardCount(employeeByClueDTO.getForwardCount() == null ? 0 : employeeByClueDTO.getForwardCount());
            employeeRankingResult.setLookUpCount(employeeByClueDTO.getLookUpCount() == null ? 0 : employeeByClueDTO.getLookUpCount());

            employeeRankingResult.setLookUpUserCount(employeeByClueDTO.getLookUpUserCount() == null ? 0 : employeeByClueDTO.getLookUpUserCount());
            employeeRankingResult.setForwardUserCount(employeeByClueDTO.getForwardUserCount() == null ? 0 : employeeByClueDTO.getForwardUserCount());
            listActivityRankingResult.add(employeeRankingResult);
        }
        //已推广企业数量
        int completedTaskCompanyCount = spreadMarketingActivityCompanyIds.size();
        employeeRankingTotalStat.setCompletedTaskUserCount(completedTaskCompanyCount);
        employeeRankingTotalStat.setUncompleteTaskUserCount(pageResult.getTotalCount() - completedTaskCompanyCount);
        List<String> name = new ArrayList<>();
        for (int i = 0; i < allOutTenantIdList.size(); i++) {
            if (i > 4) {
                break;
            }
            if (spreadMarketingActivityCompanyIds.contains(allOutTenantIdList.get(i))) {
                continue;
            }
            name.add(companyIdToCompanyNameMap.get(spreadTaskCompanyIds.get(i)));
        }
        employeeRankingTotalStat.setUncompleteTaskUserName(name);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }


    @Override
    public Result<Void> exportMarketingActivityCompanyRanking(String ea, Integer fsUserId, ListMarketingActivityEmployeeRankingArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<PageResult<ListMarketingActivityEmployeeRankingResult>> pageResult = listMarketingActivityCompanyRanking(ea, fsUserId, arg);
            if (!pageResult.isSuccess()) {
                log.warn("EnterpriseSpreadController.exportMarketingActivityPartnerRanking error");
                return;
            }
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            String excelName = I18nUtil.get(I18nKeyStaticEnum.MARK_STATIC_ENTERPRISESPREADSTATISTICSERVICEIMPL_EXCEL_TITLE_PARTNER) + ".xlsx";
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, excelName);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1821));
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = generateExcelTitleListForPartner();
            List<List<Object>> marketingActivityEmployeeRankingList = generateExcelDataListForPartner(pageResult.getData().getResult());
            ExcelUtil.fillContent(xssfSheet, titleList, marketingActivityEmployeeRankingList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, excelName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }
    private List<String> generateExcelTitleListForPartner() {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2074));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2075));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_94));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1842));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_96));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1845));
        return titleList;
    }

    private List<List<Object>> generateExcelDataListForPartner(List<ListMarketingActivityEmployeeRankingResult> listMarketingActivityEmployeeRankingResults) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (ListMarketingActivityEmployeeRankingResult result : listMarketingActivityEmployeeRankingResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(result.getName());
            objList.add(result.getOutTenantSpreadEmployee());
            objList.add(result.getSpreadCount());
            objList.add(result.getLookUpCount());
            objList.add(result.getForwardCount());
            objList.add(result.getLeadAccumulationCount());
            dataList.add(objList);
        }
        return dataList;
    }

    private Map<String, DingStaffInfoResult.UserGetResponse> getOutUidToNameMap(List<String> totalOutUserId, String ea) {
        Map<String, DingStaffInfoResult.UserGetResponse> res = new HashMap<>();
        Set<String> outUidSet =new HashSet<>(totalOutUserId);
        outUidSet.remove(null);
        if (null != outUidSet && outUidSet.size() > 0) {
            //没开通名片也没推广的
            List<DingStaffInfoResult.UserGetResponse> staffList = dingManager.getStaffByDingUserIds(ea, outUidSet);
            for (DingStaffInfoResult.UserGetResponse temp : staffList) {
                res.put(temp.getUserid(),temp);
            }
        }
        return res;
    }

    @Override
    public Result<AddMarketingActivityResult> addMarketingActivityKis(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        return marketingActivityService.addMarketingActivity(ea, fsUserId, addMarketingActivityArg, false);
    }

    @Override
    public Result<PageResult<EnterpriseSpreadUserMarketingListResult>> userMarketingList(EnterpriseSpreadUserMarketingListArg arg) {
        return statisticService.enterpriseSpreadUserMarketingList(arg);
    }

    @Override
    public Result<Void> exportUserMarketingList(EnterpriseSpreadUserMarketingListArg arg) {
        ThreadPoolUtils.execute(() -> {
            int pageSize = arg.getPageSize();
            arg.setPageNum(1);
            arg.setPageSize(pageSize);
            Result<com.facishare.marketing.common.result.PageResult<EnterpriseSpreadUserMarketingListResult>> result = statisticService.enterpriseSpreadUserMarketingList(arg);
            com.facishare.marketing.common.result.PageResult<EnterpriseSpreadUserMarketingListResult> pageResult = result.getData();
            if (pageResult == null || CollectionUtils.isEmpty(pageResult.getResult())) {
                return ;
            }

            SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_2135)
                    + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
            SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
            sxssfSheet.setDefaultColumnWidth(20);

            // 填充标题行
            ExcelUtil.fillTitles(sxssfSheet, ENTERPRISE_SPREAD_USER_MARKETING_TITLE_LIST);

            List<List<Object>> dataList = buildUserMarketingListData(pageResult.getResult());
            // 填充第一页的数据
            ExcelUtil.appendContent(sxssfSheet, dataList, 1);
            Integer totalCount = pageResult.getTotalCount();
            int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
            if (totalPage > 1) {
                for (int i = 2; i <= totalPage; i++) {
                    arg.setPageNum(i);
                    result = statisticService.enterpriseSpreadUserMarketingList(arg);
                    pageResult = result.getData();
                    if (pageResult == null || CollectionUtils.isEmpty(pageResult.getResult())) {
                        break;
                    }
                    dataList = buildUserMarketingListData(pageResult.getResult());
                    // 追加填充每一页的数据
                    ExcelUtil.appendContent(sxssfSheet, dataList, (i - 1) * pageSize + 1);
                }
            }
            pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, arg.getEa(), arg.getFsUserId());
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    private List<List<Object>> buildUserMarketingListData(List<EnterpriseSpreadUserMarketingListResult> resultList) {
        List<List<Object>> data = Lists.newArrayList();
        for (EnterpriseSpreadUserMarketingListResult result : resultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(result.getUserMarketingName());
            objectList.add(result.getLookUpCount());
            objectList.add(result.getForwardCount());
            objectList.add(result.getFsUserName());
            objectList.add(result.getDepartment());
            data.add(objectList);
        }
        return data;
    }
}