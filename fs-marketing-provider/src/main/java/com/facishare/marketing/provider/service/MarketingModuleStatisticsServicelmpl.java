/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.miniAppSetting.BaseTimingArg;
import com.facishare.marketing.api.result.MarketingWxServiceResult;
import com.facishare.marketing.api.service.MarketingModuleStatisticsService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.advertiser.bigScreen.AdLeadDataDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduAccountDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.mail.MailAccountDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSignatureDao;
import com.facishare.marketing.provider.dao.statistic.MarketingObjectDayStatisticDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.MarketingMuduleDataStatisticsEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.manager.EnterpriseInfoManager;
import com.facishare.marketing.provider.manager.LicenseManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.miniAppSetting.MiniAppSettingManager;
import com.facishare.marketing.provider.manager.miniAppSetting.result.BaseVisitTrendResult;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.statistic.common.result.Result;
import com.facishare.marketing.statistic.outapi.arg.CountNumByEaAndTimeArg;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.Serializable;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("marketingModuleStatisticsService")
public class MarketingModuleStatisticsServicelmpl implements MarketingModuleStatisticsService {
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private MiniAppSettingManager miniAppSettingManager;
    @Autowired
    private OfficialWebsiteDAO officialWebsiteDAO;
    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;
    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;
    @Autowired
    private MwSmsSignatureDao mwSmsSignatureDao;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private MwSmsSendDao mwSmsSendDao;
    @Autowired
    private MailAccountDAO mailAccountDAO;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private BaiduAccountDAO baiduAccountDAO;
    @Autowired
    private AdLeadDataDAO adLeadDataDAO;
    @Autowired
    private ModuleStatisticsDAO moduleStatisticsDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private LicenseManager licenseManager;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private MarketingObjectDayStatisticDao marketingObjectDayStatisticDao;
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @ReloadableProperty("activity.enterprise.white.list")
    private String activityEnterpriseWhiteList;
    @ReloadableProperty("purchased.enterprise.white.list")
    private String purchasedEnterpriseWhiteList; //已购买客户
    @ReloadableProperty("free.enterprise.white.list")
    private String freeEnterpriseWhiteList; //免费客户
    @ReloadableProperty("trial.enterprise.white.list")
    private String trialEnterpriseWhiteList; //试用客户
    private long statStartTime;
    private long statEndTime;

    private final String  activity_module_key = "activity_module_key";  //活动营销历史活跃
    private final String activity_module_month_key = "activity_module_month_key"; //活动营销月活跃
    private final String live_module_key = "live_module_key"; //直播营销历史活跃
    private final String live_module_month_key = "live_module_month_key"; //直播营销月活跃
    private final String conference_module_key = "conference_module_key"; //会议营销历史活跃
    private final String conference_module_month_key = "conference_module_month_key"; //会议营销月活跃
    private final String employee_module_key = "employee_module_key"; //员工营销历史活跃
    private final String employee_module_month_key = "employee_module_month_key"; //员工营销月活跃
    private final String hexagon_site_module_key = "hexagon_site_module_key"; //企业微站历史活跃
    private final String hexagon_site_module_month_key = "hexagon_site_module_month_key"; //企业微站月活跃
    private final String website_module_key = "website_module_key"; //官网历史活跃
    private final String website_module_month_key = "website_module_month_key"; //官网月活跃
    private final String wechat_account_module_key = "wechat_account_module_key"; //微信公众号历史活跃
    private final String wechat_account_module_month_key = "wechat_account_module_month_key"; //微信公众号月活跃
    private final String qywx_module_key = "qywx_module_key"; //企业微信历史活跃
    private final String qywx_module_month_key = "qywx_module_month_key"; //企业微信月活跃
    private final String sms_module_key = "sms_module_key"; //短信营销历史活跃
    private final String sms_module_month_key = "sms_module_month_key"; //短信营销月活跃
    private final String email_module_key = "email_module_key"; //邮件营销历史活跃
    private final String email_module_month_key = "email_module_month_key"; //邮件营销月活跃
    private final String ad_module_key = "ad_module_key"; //广告营销历史活跃
    private final String ad_module_month_key = "ad_module_month_key"; //广告营销月活跃
    private final String partner_module_key = "partner_module_key"; //伙伴营销历史活跃
    private final String partner_module_month_key = "partner_module_month_key"; //伙伴营销月活跃
    private final String usergroup_module_key = "usergroup_module_key"; //目标人群模块统计
    private final String usergroup_module_month_key = "usergroup_module_month_key"; //目标人群模块月活跃统计
    private final String marketing_automation_module_key = "marketing_automation_module_key"; //营销自动化模块统计
    private final String marketing_automation_module_month_key = "marketing_automation_module_month_key"; //营销自动化模块月活跃统计
    private final String whatsapp_module_key = "whatsapp_module_key"; //WhatsApp营销历史活跃
    private final String whatsapp_module_month_key = "whatsapp_module_month_key"; //WhatsApp营销月活跃

    private Map<String, List<String>> moduleStatMap = null;
    @Override
    public void marketingMuduleStatistics() {
        ThreadPoolUtils.execute(this::marketingModuleStatisticsByDate, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    public void marketingModuleStatisticsByDate() {
        log.info("start marketing mudule statistics");
        log.info("activityEnterpriseWhiteList:{} , purchasedEnterpriseWhiteList:{}, freeEnterpriseWhiteList:{}, trialEnterpriseWhiteList:{}", activityEnterpriseWhiteList, purchasedEnterpriseWhiteList, freeEnterpriseWhiteList, trialEnterpriseWhiteList);
        List<String> whiteEas = GsonUtil.fromJson(activityEnterpriseWhiteList, new TypeToken<List<String>>(){}.getType());
        whiteEas = Lists.newArrayList(whiteEas.stream().distinct().collect(Collectors.toList()));
        List<String> purchasedEas = GsonUtil.fromJson(purchasedEnterpriseWhiteList, new TypeToken<List<String>>(){}.getType());
        purchasedEas = Lists.newArrayList(purchasedEas.stream().distinct().collect(Collectors.toList()));
        List<String> freeEas = GsonUtil.fromJson(freeEnterpriseWhiteList, new TypeToken<List<String>>(){}.getType());
        freeEas = Lists.newArrayList(freeEas.stream().distinct().collect(Collectors.toList()));
        List<String> trialEas = GsonUtil.fromJson(trialEnterpriseWhiteList, new TypeToken<List<String>>(){}.getType());
        trialEas = Lists.newArrayList(trialEas.stream().distinct().collect(Collectors.toList()));
        log.info("marketing mudule statistics valid enterprise size: {}  purchasedEas:{}, freeEas:{} trialEas:{}", whiteEas.size(),  purchasedEas, freeEas, trialEas);
        List<String> totalEaList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(whiteEas) && !whiteEas.contains("NO")) {
            totalEaList.addAll(whiteEas);
        }
        if (CollectionUtils.isNotEmpty(purchasedEas) && !purchasedEas.contains("NO")) {
            totalEaList.addAll(purchasedEas);
        }
        if (CollectionUtils.isNotEmpty(freeEas) && !freeEas.contains("NO")) {
            totalEaList.addAll(freeEas);
        }
        if (CollectionUtils.isNotEmpty(trialEas) && !trialEas.contains("NO")) {
            totalEaList.addAll(trialEas);
        }
        if (CollectionUtils.isEmpty(totalEaList)) {
            return;
        }


        totalEaList = Lists.newArrayList(totalEaList.stream().distinct().collect(Collectors.toList()));
        //统计时间-按月统计
        statStartTime = getStatisticStartTime();
        statEndTime = getStatisticEndTime();

        moduleStatMap = new HashMap<>(); //重置统计数据map
        marketingActivityModuleStatistics(totalEaList);
        marketingLiveModuleStatistics(totalEaList);
        marketingConferenceModuleStatistics(totalEaList);
        marketingEmployeeModuleStatistics(totalEaList);
        marketingHexagonSiteModuleStatistics(totalEaList);
        marketingWebsiteModuleStatistics(totalEaList);
        marketingWechatAccountModuleStatistics(totalEaList);
        marketingQywxModuleStatistics(totalEaList);
        marketingSmsModuleStatistics(totalEaList);
        marketingEmailModuleStatistics(totalEaList);
        marketingAdModuleStatistics(totalEaList);
        marketingPartnerModuleStatistics(totalEaList);
        marketingUserGroupModuleStatistics(totalEaList);
        marketingAutomationModuleStatistics(totalEaList);
        whatsappModuleStatistics(totalEaList);

        //白名单企业统计
        if (CollectionUtils.isNotEmpty(whiteEas) && !whiteEas.contains("NO")) {
            //白名单企业统计
            MarketingMuduleDataStatisticsEntity whiteListEntity = saveData(whiteEas, 0);
            printRatio(whiteListEntity, whiteEas);
        }

        //购买企业统计
        if (CollectionUtils.isNotEmpty(purchasedEas) && !purchasedEas.contains("NO")) {
            MarketingMuduleDataStatisticsEntity purchasedEntity = saveData(purchasedEas, 1);
            printRatio(purchasedEntity, purchasedEas);
        }

       //免费企业统计
        if (CollectionUtils.isNotEmpty(freeEas) && !freeEas.contains("NO")) {
            MarketingMuduleDataStatisticsEntity freeEntity = saveData(freeEas,2);
            printRatio(freeEntity, freeEas);
        }

        //试用企业统计
        if (CollectionUtils.isNotEmpty(trialEas) && !trialEas.contains("NO")) {
            MarketingMuduleDataStatisticsEntity trialEntity = saveData(trialEas,3);
            printRatio(trialEntity, trialEas);
        }
    }


    //获取上个月的起始时间和结束时间
    private long getStatisticStartTime() {
        Calendar calendar = Calendar.getInstance();

        // 获取当前日期
        Date currentDate = new Date();
        calendar.setTime(currentDate);

        // 将日期设置为上个月
        calendar.add(Calendar.MONTH, -1);

        // 获取上个月的起始时间
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date lastMonthStartDate = calendar.getTime();

        return lastMonthStartDate.getTime();
    }

    //获取上个月的23：59：59结束时间
 private long getStatisticEndTime() {
        Calendar calendar = Calendar.getInstance();
        // 获取当前日期
        Date currentDate = new Date();
        calendar.setTime(currentDate);
        // 将日期设置为上个月
        calendar.add(Calendar.MONTH, -1);
        // 获取上个月的结束时间
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DATE));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date lastMonthEndDate = calendar.getTime();
        return lastMonthEndDate.getTime();
 }

    private void marketingActivityModuleStatistics(List<String> eas) {
        //开通企业数--创建过活动类型非会议和直播的活动的企业总数
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        paasQueryArg.addFilter("event_type", PaasAndCrmOperatorEnum.NOT_IN.getCrmOperator(),
                Lists.newArrayList(MarketingEventEnum.LIVE_MARKETING.getEventType(), MarketingEventEnum.MEETING_SALES.getEventType()));
        queryFilterArg.setSelectFields(selectFields);
        try {
            for (String ea : eas) {
                int count = getObjectCountByFilter(ea, -10000, queryFilterArg);
                if (count > 0) {
                    moduleStatMap.computeIfAbsent(activity_module_key, k -> new ArrayList<>());
                    moduleStatMap.get(activity_module_key).add(ea);
                }
            }

            //活跃企业数据--近30天内创建过活动类型非会议和直播的活动的企业总数
            paasQueryArg.addFilter("create_time", PaasAndCrmOperatorEnum.BETWEEN.getCrmOperator(),
                    Lists.newArrayList(String.valueOf(statStartTime), String.valueOf(statEndTime)));
            for (String ea : eas) {
                int count = getObjectCountByFilter(ea, -10000, queryFilterArg);
                if (count > 0) {
                    moduleStatMap.computeIfAbsent(activity_module_month_key, k -> new ArrayList<>());
                    moduleStatMap.get(activity_module_month_key).add(ea);
                }
            }
        }catch (Exception e){
            log.info("marketingActivityModuleStatistics exception e:{}", e);
        }
        log.info("marketing activity stat openEnterpriseCount:{}, activityEnterpriseCount:{}", moduleStatMap.get(activity_module_key) == null ? 0 : moduleStatMap.get(activity_module_key).size(), moduleStatMap.get(activity_module_month_key) == null ? 0 : moduleStatMap.get(activity_module_month_key).size());
    }

    //统计营销活动的开通数和活跃数


    private int getObjectCountByFilter(String ea, Integer fsUserId, PaasQueryFilterArg filter) {
        int count = 0;
        try{
            count = crmV2Manager.countCrmObjectByFilterV3(ea, -10000, filter);
        }catch (Exception e){
            log.info("getObjectCountByFilter failed ea:{}, apiName:{} error:{}", ea, filter.getObjectAPIName(), e);
        }

        return count;
    }

    //统计直播营销的开通数和活跃数
    private void marketingLiveModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通企业数--查询创建的活动类型为直播的市场活动的企业数量
        List<Integer> corpIds = marketingLiveDAO.getAllCorpId(null, null);
        if (CollectionUtils.isEmpty(corpIds)){
            return;
        }
        List<String> liveEas = corpIds.stream().map(ea -> eieaConverter.enterpriseIdToAccount(ea)).collect(Collectors.toList());
        List<String> distinctEas = liveEas.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.put(live_module_key, distinctEas);

        //活跃企业数据--查询近30天有创建过直播的企业数量
        corpIds = marketingLiveDAO.getAllCorpId(startTime, endTime);
        if (!CollectionUtils.isEmpty(corpIds)){
            liveEas = corpIds.stream().map(ea -> eieaConverter.enterpriseIdToAccount(ea)).collect(Collectors.toList());
            distinctEas = liveEas.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.put(live_module_month_key, distinctEas);
        }

        log.info("marketingLiveModuleStatistics openEnterpriseCount:{}, activityEnterpriseCount:{}", moduleStatMap.get(live_module_key) == null ? 0 : moduleStatMap.get(live_module_key).size(), moduleStatMap.get(live_module_month_key) == null ? 0 : moduleStatMap.get(live_module_month_key).size());
    }

    //统计会议营销的开通数和活跃数
    private void marketingConferenceModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通企业数--查询创建的活动类型为会议的市场活动的企业数量
        List<String> conferenceEas = activityDAO.getByEasAndDate(eas, null, null);
        if (CollectionUtils.isEmpty(conferenceEas)){
            return;
        }

        moduleStatMap.put(conference_module_key, conferenceEas);

        //活跃企业数据--查询近30天有创建过直播的企业数量
        conferenceEas = activityDAO.getByEasAndDate(eas, startTime, endTime);
        if (!conferenceEas.isEmpty()){
            List<String> distinctEas = conferenceEas.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.put(conference_module_month_key, distinctEas);
        }

        log.info("conference stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(conference_module_key) == null ? 0 : moduleStatMap.get(conference_module_key).size(), moduleStatMap.get(conference_module_month_key) == null ? 0 : moduleStatMap.get(conference_module_month_key).size());
    }

    //统计全员营销的开通数和活跃数
    private void marketingEmployeeModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //全员营销开通企业数--创建过全员营销的企业数量
        List<String> spreadList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(Lists.newArrayList(AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType()), null, null);
        if (CollectionUtils.isEmpty(spreadList)){
            return;
        }

        List<String> distinctEas = spreadList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.put(employee_module_key, distinctEas);

        //全员营销的活跃企业数--近30天有创建过全员营销的企业数量
        spreadList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(Lists.newArrayList(AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType()), startTime, endTime);
        if (CollectionUtils.isNotEmpty(spreadList)){
            distinctEas = spreadList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.put(employee_module_month_key, distinctEas);
        }

        log.info("employee spread stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(employee_module_key) == null ? 0 : moduleStatMap.get(employee_module_key).size(), moduleStatMap.get(employee_module_month_key) == null ? 0 : moduleStatMap.get(employee_module_month_key).size());
    }

    //统计数字展厅的开通数和活跃数
    private void marketingHexagonSiteModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //数字展厅开通企业数--绑定数字展厅的企业数量
        List<String> bindEas = eaWechatAccountBindDao.getEaByThirdPlatformIdAndWxAppId();
        if (CollectionUtils.isEmpty(bindEas)){
            return;
        }
        List<String> distinctEas = bindEas.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.put(hexagon_site_module_key, distinctEas);

        //数字展厅活跃企业数--近30天访问数字展厅模块菜单1次
        BaseTimingArg arg = new BaseTimingArg();
        arg.setStartTime(startTime.getTime());
        arg.setEndTime(endTime.getTime());
        arg.setFilterType(1);
        for (String ea : distinctEas) {
            Optional<String> appIdOptional = wechatAccountManager.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID);
            if (!appIdOptional.isPresent() || StringUtils.isBlank(appIdOptional.get()) || WxAppInfoEnum.isSystemApp(appIdOptional.get())) {
                log.warn("marketingHexagonSiteModuleStatistics.dataBriefing error getWxAppIdByEa failed ea:{}", ea);
                continue;
            }
            arg.setEa(ea);
            try {
                BaseVisitTrendResult baseVisitTrendResult = getVisitData(arg, appIdOptional.get());
                if (baseVisitTrendResult != null && baseVisitTrendResult.getVisitPv() > 0) {
                    moduleStatMap.putIfAbsent(hexagon_site_module_month_key, Lists.newArrayList());
                    moduleStatMap.get(hexagon_site_module_month_key).add(ea);
                }
            } catch (Exception e) {
                log.info("marketingHexagonSiteModuleStatistics.getVisitTrend as day exception ea:{} e:", ea, e);
            }
        }

        log.info("hexagon site stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(hexagon_site_module_key) == null ? 0 : moduleStatMap.get(hexagon_site_module_key).size(), moduleStatMap.get(hexagon_site_module_month_key) == null ? 0 : moduleStatMap.get(hexagon_site_module_month_key).size());
    }

    //统计官网的开通数和活跃数
    private void marketingWebsiteModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //官网营销开通数--绑定官网营销的企业数量
        List<String> bindEas = officialWebsiteDAO.getAllEa();
        if (CollectionUtils.isEmpty(bindEas)){
            return;
        }
        List<String> distinctEas = bindEas.stream().filter(ea -> eas.contains(ea)).distinct().collect(Collectors.toList());
        moduleStatMap.putIfAbsent(website_module_key, distinctEas);

        //官网营销活跃数--近30天访问官网模块菜单1次
        CountNumByEaAndTimeArg arg = new CountNumByEaAndTimeArg();
        arg.setBeginTime(startTime.getTime());
        arg.setEndTime(endTime.getTime());
        for (String ea : distinctEas){
            arg.setEa(ea);
            try {
                String existed = marketingObjectDayStatisticDao.existOfficialWebStatisticsByTime(ea, startTime, endTime);
                if (StringUtils.isNotBlank(existed)) {
                    moduleStatMap.putIfAbsent(website_module_month_key, Lists.newArrayList());
                    moduleStatMap.get(website_module_month_key).add(ea);
                }
            }catch (Exception e){
                log.info("marketingWebsiteModuleStatistics.existOfficialWebStatisticsByTime ea:{} e:", ea, e);
            }
        }

        log.info("website stat openEnterpriseCount={},activityEnterpriseCount={}", moduleStatMap.get(website_module_key) == null ? 0 : moduleStatMap.get(website_module_key).size(), moduleStatMap.get(website_module_month_key) == null ? 0 : moduleStatMap.get(website_module_month_key).size());
    }

    //统计公众号的开通数和活跃数
    private void marketingWechatAccountModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通公众号企业数量--成功绑定了公众号
        try {
            for (String ea : eas) {
                com.facishare.marketing.common.result.Result<List<MarketingWxServiceResult>> weChatServiceResult = weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea, 1000, false);
                if (weChatServiceResult != null && CollectionUtils.isNotEmpty(weChatServiceResult.getData())) {
                    moduleStatMap.putIfAbsent(wechat_account_module_key, Lists.newArrayList());
                    moduleStatMap.get(wechat_account_module_key).add(ea);
                }
            }

            //公众号活跃企业数量--近30天发起过1次消息，或创建过1个渠道活码
            List<Integer> associateIdTypeList = Lists.newArrayList(AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType(), AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType());
            List<String> eaList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(associateIdTypeList, startTime, endTime);
            if (CollectionUtils.isNotEmpty(eaList)) {
                List<String> distinctEaList = eaList.stream().filter(ea -> eas.contains(ea)).distinct().collect(Collectors.toList());
                moduleStatMap.putIfAbsent(wechat_account_module_month_key, distinctEaList);
            }
        }catch (Exception e){
            log.info("marketingWechatAccountModuleStatistics exception e:{}", e);
        }

        log.info("wechat account stat openEnterpriseCount={},activityEnterpriseCount={}", moduleStatMap.get(wechat_account_module_key) == null ? 0 : moduleStatMap.get(wechat_account_module_key).size(), moduleStatMap.get(wechat_account_module_month_key) == null ? 0 : moduleStatMap.get(wechat_account_module_month_key).size());
    }

    //统计企微营销的开通数和活跃数
    private void marketingQywxModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通企微营销企业数量--成功绑定了企微
        List<QywxMiniappConfigEntity> entities = qywxMiniappConfigDAO.listAll();
        if (CollectionUtils.isEmpty(entities)){
            return;
        }
        List<String> bindQywxEaList = entities.stream().map(QywxMiniappConfigEntity::getEa).filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.putIfAbsent(qywx_module_key, bindQywxEaList);

        //企微营销活跃企业--近30天发起过群发消息/企微朋友圈1次，或创建过1个员工活码（排除员工名片自动创建的）
        Set<String> activityEasSet = new HashSet<>();
        List<Integer> associateIdTypes = Lists.newArrayList(AssociateIdTypeEnum.MOMENT_SEND_ACTIVITY.getType(), AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType());
        List<String> qywxMsgSendEaList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(associateIdTypes, startTime, endTime);
        if (CollectionUtils.isNotEmpty(qywxMsgSendEaList)){
            List<String> distinctEaList = qywxMsgSendEaList.stream().filter(ea -> eas.contains(ea)).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(distinctEaList)){
                activityEasSet.addAll(distinctEaList);
            }
        }

        //查询最近30天创建过员工二维码的企业
        List<String> fanQrCodeEas =  qywxAddFanQrCodeDAO.queryEmployeeCreateFanQrCodeEaByCreateTime(startTime, endTime, bindQywxEaList);
        if (CollectionUtils.isNotEmpty(fanQrCodeEas)){
            List<String> distinctEaList = fanQrCodeEas.stream().filter(ea -> eas.contains(ea)).distinct().collect(Collectors.toList());
            activityEasSet.addAll(distinctEaList);
        }
        moduleStatMap.putIfAbsent(qywx_module_month_key, Lists.newArrayList(activityEasSet));

        log.info("qywx stat openEnterpriseCount={},activityEnterpriseCount={}", moduleStatMap.get(qywx_module_key) == null ? 0 : moduleStatMap.get(qywx_module_key).size(), moduleStatMap.get(qywx_module_month_key) == null ? 0 : moduleStatMap.get(qywx_module_month_key).size());
    }

    //统计短信营销的开通数和活跃数
    private void marketingSmsModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通短信营销企业：绑定短信签名的企业
        List<String> applyEaList = mwSmsSignatureDao.findApplyEaByEas(new HashSet<>(eas));
        if (CollectionUtils.isEmpty(applyEaList)){
            return;
        }
        List<String> distinctEaList = applyEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.putIfAbsent(sms_module_key, distinctEaList);

        //活跃企业：近30天有发送短信的企业
        List<String> sendedEaList = mwSmsSendDao.findSendedEaByEasAndCreateTime(distinctEaList, startTime, endTime);
        if (CollectionUtils.isNotEmpty(sendedEaList)){
            distinctEaList = sendedEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.putIfAbsent(sms_module_month_key, distinctEaList);
        }

        log.info("sms stat - openEnterpriseCount={},activityEnterpriseCount={}", moduleStatMap.get(sms_module_key) == null ? 0 : moduleStatMap.get(sms_module_key).size(), moduleStatMap.get(sms_module_month_key) == null ? 0 : moduleStatMap.get(sms_module_month_key).size());
    }

    //统计邮件营销的开通数和活跃数
    private void marketingEmailModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通邮件营销的企业：绑定邮件营销账号的企业
        List<String> bindEaList = mailAccountDAO.getAllEas();
        if (CollectionUtils.isEmpty(bindEaList)){
            return;
        }

        List<String> distinctEaList = bindEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.putIfAbsent(email_module_key, distinctEaList);

        //活跃企业：近30天有发送邮件的企业
        List<String> sendTaskEaList = mailSendTaskDAO.getSendTaskEasByCreateTime(startTime, endTime);
        if (CollectionUtils.isNotEmpty(sendTaskEaList)){
            distinctEaList = sendTaskEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.putIfAbsent(email_module_month_key, distinctEaList);
        }

        log.info("email stat - openEnterpriseCount={},activityEnterpriseCount={}", moduleStatMap.get(email_module_key) == null ? 0 : moduleStatMap.get(email_module_key).size(), moduleStatMap.get(email_module_month_key) == null ? 0 : moduleStatMap.get(email_module_month_key).size());
    }

    //开通广告营销的开通数和活跃数
    private void marketingAdModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //开通企业数：绑定了广告平台的企业
        List<String> bindEaList = baiduAccountDAO.findAllEa();
        if (CollectionUtils.isEmpty(bindEaList)){
            return;
        }

        List<String> distinctEaList = bindEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.putIfAbsent(ad_module_key, distinctEaList);

        //获取企业数：近30天内有广告来源线索的企业数
        List<String> leadEaList = adLeadDataDAO.getLeadEasByCreateTime(startTime, endTime);
        if (CollectionUtils.isNotEmpty(leadEaList)){
            distinctEaList = leadEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.putIfAbsent(ad_module_month_key, distinctEaList);
        }

        log.info("ad stat - openEnterpriseCount={},activityEnterpriseCount={}", moduleStatMap.get(ad_module_key) == null ? 0 : moduleStatMap.get(ad_module_key).size(), moduleStatMap.get(ad_module_month_key) == null ? 0 : moduleStatMap.get(ad_module_month_key).size());
    }

    //伙伴营销的开通数和活跃数
    private void marketingPartnerModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //全员营销开通企业数--创建过全员营销的企业数量
        List<String> spreadList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(Lists.newArrayList(AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType()), null, null);
        if (CollectionUtils.isEmpty(spreadList)){
            return;
        }

        List<String> distinctEas = spreadList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        moduleStatMap.put(partner_module_key, distinctEas);

        //全员营销的活跃企业数--近30天有创建过全员营销的企业数量
        spreadList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(Lists.newArrayList(AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType()), startTime, endTime);
        if (CollectionUtils.isNotEmpty(spreadList)){
            distinctEas = spreadList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            moduleStatMap.put(partner_module_month_key, distinctEas);
        }

        log.info("partner spread stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(partner_module_key) == null ? 0 : moduleStatMap.get(partner_module_key).size(), moduleStatMap.get(partner_module_month_key) == null ? 0 : moduleStatMap.get(partner_module_month_key).size());
    }

    //目标人群的开通数和活跃数
    private void marketingUserGroupModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        List<String> userGroupEas = marketingUserGroupDao.queryEaByTime(null, null);
        if (CollectionUtils.isNotEmpty(userGroupEas)){
            List<String> distinctEas = userGroupEas.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(distinctEas)){
                moduleStatMap.putIfAbsent(usergroup_module_key, distinctEas);
            }
        }

        userGroupEas = marketingUserGroupDao.queryEaByTime(startTime, endTime);
        if (CollectionUtils.isNotEmpty(userGroupEas)){
            List<String> distinctEas = userGroupEas.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(distinctEas)){
                moduleStatMap.putIfAbsent(usergroup_module_month_key, distinctEas);
            }
        }
        log.info("userGroup stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(usergroup_module_key) == null ? 0 : moduleStatMap.get(usergroup_module_key).size(), moduleStatMap.get(usergroup_module_month_key) == null ? 0 : moduleStatMap.get(usergroup_module_month_key).size());
    }

    //营销自动化开通数和活跃数
    private void marketingAutomationModuleStatistics(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)) {
            return;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);
        //营销自动化开通企业数--创建过营销自动化的企业数量
        List<Integer> automationEiList = marketingFlowAdditionalConfigDao.queryEiByTime(null, null);
        if (CollectionUtils.isEmpty(automationEiList)){
            return;
        }
        List<String> automationEaList = automationEiList.stream().map(ei -> eieaConverter.enterpriseIdToAccount(ei)).collect(Collectors.toList());
        List<String> distinctEas = automationEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(distinctEas)){
            moduleStatMap.putIfAbsent(marketing_automation_module_key, distinctEas);
        }
        //营销自动化活跃企业数--近30天有创建过营销自动化的企业数量
        automationEiList = marketingFlowAdditionalConfigDao.queryEiByTime(startTime, endTime);
        if (CollectionUtils.isNotEmpty(automationEiList)){
            automationEaList = automationEiList.stream().map(ei -> eieaConverter.enterpriseIdToAccount(ei)).collect(Collectors.toList());
            distinctEas = automationEaList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(distinctEas)){
                moduleStatMap.putIfAbsent(marketing_automation_module_month_key, distinctEas);
            }
        }
        log.info("marketing automation  stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(marketing_automation_module_key) == null ? 0 : moduleStatMap.get(marketing_automation_module_key).size(), moduleStatMap.get(marketing_automation_module_month_key) == null ? 0 : moduleStatMap.get(marketing_automation_module_month_key).size());
    }

    //whatsapp模块统计
    private void whatsappModuleStatistics(List<String> eas){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date startTime = new Date(statStartTime);
        Date endTime = new Date(statEndTime);

        //whatsapp推广的活跃企业数--创建过whatsapp推广的企业数量
        List<String> spreadList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(Lists.newArrayList(AssociateIdTypeEnum.WHATS_APP_SPREAD.getType()), null, null);
        if (CollectionUtils.isEmpty(spreadList)){
            return;
        }

        List<String> distinctEas = spreadList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(distinctEas)) {
            moduleStatMap.put(whatsapp_module_key, distinctEas);
        }

        //whatsapp推广的活跃企业数--近30天有创建过whatsapp推广的企业数量
        spreadList = marketingActivityExternalConfigDao.getMarketingActivitySpreadEnterpriseEaByAssociateIdType(Lists.newArrayList(AssociateIdTypeEnum.WHATS_APP_SPREAD.getType()), startTime, endTime);
        if (CollectionUtils.isEmpty(spreadList)){
            return;
        }
        distinctEas = spreadList.stream().filter(eas::contains).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(distinctEas)){
            moduleStatMap.put(whatsapp_module_month_key, distinctEas);
        }

        log.info("whatsapp  stat openEnterpriseCount:{} activityEnterpriseCount:{}", moduleStatMap.get(whatsapp_module_key) == null ? 0 : moduleStatMap.get(whatsapp_module_key).size(), moduleStatMap.get(whatsapp_module_month_key) == null ? 0 : moduleStatMap.get(whatsapp_module_month_key).size());

    }

    private MarketingMuduleDataStatisticsEntity saveData(List<String> statisticEas, int type){
        MarketingMuduleDataStatisticsEntity entity = new MarketingMuduleDataStatisticsEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setCreateTime(new Date());
        entity.setStatisticsType(type);
        entity.setTotalEnterpriseCount(statisticEas.size());
        //活动营销活跃企业
        List<String> openEas = moduleStatMap.get(activity_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingActivityModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingActivityModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        List<String> activityEas = moduleStatMap.get(activity_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingActivityModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingActivityModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingActivityModuleEas() == null){
            entity.setMarketingActivityModuleEas("");
        }
        if (entity.getMarketingActivityModuleActivityEas() == null){
            entity.setMarketingActivityModuleActivityEas("");
        }

        //直播营销企业
        openEas = moduleStatMap.get(live_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingLiveModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingLiveModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(live_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingLiveModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingLiveModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingLiveModuleEas() == null){
            entity.setMarketingLiveModuleEas("");
        }
        if (entity.getMarketingLiveModuleActivityEas() == null){
            entity.setMarketingLiveModuleActivityEas("");
        }

        //会议营销企业
        openEas = moduleStatMap.get(conference_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingConferenceModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingConferenceModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(conference_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingConferenceModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingConferenceModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingConferenceModuleEas() == null){
            entity.setMarketingConferenceModuleEas("");
        }
        if (entity.getMarketingConferenceModuleActivityEas() == null){
            entity.setMarketingConferenceModuleActivityEas("");
        }

        //全员营销企业
        openEas = moduleStatMap.get(employee_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingEmployeeModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingEmployeeModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(employee_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingEmployeeModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingEmployeeModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingEmployeeModuleEas() == null){
            entity.setMarketingEmployeeModuleEas("");
        }
        if (entity.getMarketingEmployeeModuleActivityEas() == null){
            entity.setMarketingEmployeeModuleActivityEas("");
        }

        //微站营销企业
        openEas = moduleStatMap.get(hexagon_site_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingHexagonSiteModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingHexagonSiteModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(hexagon_site_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingHexagonSiteModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingHexagonSiteModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingHexagonSiteModuleEas() == null){
            entity.setMarketingHexagonSiteModuleEas("");
        }
        if (entity.getMarketingHexagonSiteModuleActivityEas() == null){
            entity.setMarketingHexagonSiteModuleActivityEas("");
        }

        //官网营销企业
        openEas = moduleStatMap.get(website_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingWebsiteModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingWebsiteModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(website_module_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingWebsiteModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingWebsiteModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingWebsiteModuleEas() == null){
            entity.setMarketingWebsiteModuleEas("");
        }
        if (entity.getMarketingWebsiteModuleActivityEas() == null){
            entity.setMarketingWebsiteModuleActivityEas("");
        }

        //微信公众号营销
        openEas = moduleStatMap.get(wechat_account_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingWechatAccountModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingWechatAccountModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(wechat_account_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingWechatAccountModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingWechatAccountModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingWechatAccountModuleEas() == null){
            entity.setMarketingWechatAccountModuleEas("");
        }
        if (entity.getMarketingWechatAccountModuleActivityEas() == null){
            entity.setMarketingWechatAccountModuleActivityEas("");
        }

        //企业微信营销
        openEas = moduleStatMap.get(qywx_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingQywxModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingQywxModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(qywx_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingQywxModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingQywxModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingQywxModuleEas() == null){
            entity.setMarketingQywxModuleEas("");
        }
        if (entity.getMarketingQywxModuleActivityEas() == null){
            entity.setMarketingQywxModuleActivityEas("");
        }

        //短信营销企业
        openEas = moduleStatMap.get(sms_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingSmsModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingSmsModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(sms_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingSmsModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingSmsModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingSmsModuleEas() == null){
            entity.setMarketingSmsModuleEas("");
        }
        if (entity.getMarketingSmsModuleActivityEas() == null){
            entity.setMarketingSmsModuleActivityEas("");
        }

        //邮件营销企业
        openEas = moduleStatMap.get(email_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingEmailModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingEmailModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(email_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingEmailModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingEmailModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingEmailModuleEas() == null){
            entity.setMarketingEmailModuleEas("");
        }
        if (entity.getMarketingEmailModuleActivityEas() == null){
            entity.setMarketingEmailModuleActivityEas("");
        }

        //广告营销企业
        openEas = moduleStatMap.get(ad_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingAdModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingAdModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(ad_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingAdModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingAdModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingAdModuleEas() == null){
            entity.setMarketingAdModuleEas("");
        }
        if (entity.getMarketingAdModuleActivityEas() == null){
            entity.setMarketingAdModuleActivityEas("");
        }

        //伙伴营销企业
        openEas = moduleStatMap.get(partner_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingPartnerModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingPartnerModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(partner_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingPartnerModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingPartnerModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingPartnerModuleEas() == null){
            entity.setMarketingPartnerModuleEas("");
        }
        if (entity.getMarketingPartnerModuleActivityEas() == null){
            entity.setMarketingPartnerModuleActivityEas("");
        }

        //目标人群
        openEas = moduleStatMap.get(usergroup_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingUserGroupModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingUserGroupModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(usergroup_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingUserGroupModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingUserGroupModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingUserGroupModuleEas() == null){
            entity.setMarketingUserGroupModuleEas("");
        }
        if (entity.getMarketingUserGroupModuleActivityEas() == null){
            entity.setMarketingUserGroupModuleActivityEas("");
        }

        //营销自动化企业
        openEas = moduleStatMap.get(marketing_automation_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingAutomationModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingAutomationModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(marketing_automation_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingAutomationModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingAutomationModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingAutomationModuleEas() == null){
            entity.setMarketingAutomationModuleEas("");
        }
        if (entity.getMarketingAutomationModuleActivityEas() == null){
            entity.setMarketingAutomationModuleActivityEas("");
        }

        //whatsapp 企业
        openEas = moduleStatMap.get(whatsapp_module_key);
        if (CollectionUtils.isNotEmpty(openEas)){
            List<String> filterEas = openEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingWhatsappModuleEas(String.join(",", filterEas));
            }
            entity.setMarketingWhatsappModuleOpenCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        activityEas = moduleStatMap.get(whatsapp_module_month_key);
        if (CollectionUtils.isNotEmpty(activityEas)){
            List<String> filterEas = activityEas.stream().filter(statisticEas::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterEas)){
                entity.setMarketingWhatsappModuleActivityEas(String.join(",", filterEas));
            }
            entity.setMarketingWhatsappModuleActivityCount(CollectionUtils.isEmpty(filterEas) ? 0 : filterEas.size());
        }
        if (entity.getMarketingWhatsappModuleEas() == null){
            entity.setMarketingWhatsappModuleEas("");
        }
        if (entity.getMarketingWhatsappModuleActivityEas() == null){
            entity.setMarketingWhatsappModuleActivityEas("");
        }

        moduleStatisticsDAO.insert(entity);
        return entity;
    }

    private MarketingVersionData getMarketingVersionInfo(String ea){
        List<String> versionList = new ArrayList<>();
        MarketingVersionData marketingVersionData = new MarketingVersionData();
        try {
            List<ProductVersionPojo> list = licenseManager.listProductVersion(ea, "CRM");
            List<MarketingVersionPojo> marketingVersionPojoList = Lists.newArrayList();
            for (ProductVersionPojo productVersionPojo : list) {
                String currentVersion = productVersionPojo.getCurrentVersion().trim();
                Integer versionPriority = VersionEnum.fromVersion(currentVersion);
                if (null != versionPriority && versionPriority <= VersionEnum.DING_FREE_APP.getPriority() && !versionList.contains(currentVersion)) {
                    versionList.add(currentVersion);
                    marketingVersionPojoList.add(new MarketingVersionPojo(currentVersion, productVersionPojo.getStartTime(), productVersionPojo.getExpiredTime()));
                }
            }


            List<ModuleInfoPojo> moduleResults = licenseManager.listModule(ea, "CRM");
            for (ModuleInfoPojo moduleResult : moduleResults) {
                String currentVersion = moduleResult.getModuleCode().trim();
                Integer versionPriority = VersionEnum.fromVersion(currentVersion);
                if (null != versionPriority && versionPriority <= VersionEnum.DING_FREE_APP.getPriority() && !versionList.contains(currentVersion)) {
                    versionList.add(currentVersion);
                    marketingVersionPojoList.add(new MarketingVersionPojo(currentVersion, null, null));
                }
            }

            marketingVersionData.setEa(ea);
            marketingVersionData.setMarketingVersionPojos(marketingVersionPojoList);
            GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
            arg.setEnterpriseAccount(ea);
            arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
            GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(arg);
            if (enterpriseDataResult != null && enterpriseDataResult.getEnterpriseData() != null) {
                marketingVersionData.setEnterpriseName(enterpriseDataResult.getEnterpriseData().getEnterpriseName());
            }
        }catch (Exception e) {
            log.info("getMarketingVersionInfo ea:{} error:{}", ea, e);
        }

        return marketingVersionData;
    }

    @Data
    public class MarketingVersionData implements Serializable{
        private static final long serialVersionUID = 1L;
        private String ea;
        private String enterpriseName;
        List<MarketingVersionPojo> marketingVersionPojos;

        public String getVersionInfo(){
            if (CollectionUtils.isEmpty(marketingVersionPojos)){
                return null;
            }
            String version = "";
            for (MarketingVersionPojo marketingVersionPojo : marketingVersionPojos) {
                version += marketingVersionPojo.getVersonInfo();
            }

            return version;
        }
    }

    @Data
    public class MarketingVersionPojo implements Serializable{
        private String version;
        private Long versionStartTime;
        private Long versionEndTime;

        public MarketingVersionPojo(String version, Long versionStartTime, Long versionEndTime) {
            this.version = version;
            this.versionStartTime = versionStartTime;
            this.versionEndTime = versionEndTime;
        }

        public String getVersonInfo(){
            String versionInfo = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGMODULESTATISTICSSERVICELMPL_1230) + version;
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (versionStartTime != null){
                versionInfo += ",     开始时间:" + dateTimeFormatter.format(LocalDateTime.ofInstant(new Date(versionStartTime).toInstant(), ZoneId.systemDefault()));
            }
            if (versionEndTime != null) {
                versionInfo += ",     过期时间:" + dateTimeFormatter.format(LocalDateTime.ofInstant(new Date(versionEndTime).toInstant(), ZoneId.systemDefault()));
            }
            versionInfo += "]";

            return versionInfo;
        }
    }


    private void printRatio(MarketingMuduleDataStatisticsEntity entity, List<String> eas){
        if (CollectionUtils.isEmpty(eas)){
            return;
        }

        int totalEnterpriseCount = eas.size();
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);

        //活动营销开通率、活跃率、渗透率
        int marketingActivityModuleOpenCount = entity.getMarketingActivityModuleOpenCount();
        int marketingActivityModuleActivityCount = entity.getMarketingActivityModuleActivityCount();
        double marketingActivityModuleOpenCountRatio = (double)marketingActivityModuleOpenCount / totalEnterpriseCount;  //活动营销开通率
        double marketingActivityModuleActivityCountRatio = (double)marketingActivityModuleActivityCount / totalEnterpriseCount;  //活动营销活跃率
        double marketingActivityModuleOpenActivityCountRatio = 0.0; //活动营销渗透率
        if (marketingActivityModuleOpenCount != 0) {
            marketingActivityModuleOpenActivityCountRatio = (double) marketingActivityModuleActivityCount / marketingActivityModuleOpenCount;
        }

        //直播营销开通率、活跃率、渗透率
        int marketingLiveModuleOpenCount = entity.getMarketingLiveModuleOpenCount();
        int marketingLiveModuleActivityCount = entity.getMarketingLiveModuleActivityCount();
        double marketingLiveModuleOpenCountRation = marketingLiveModuleOpenCount / totalEnterpriseCount;  //直播营销开通率
        double marketingLiveModuleActivityCountRatio = (double)marketingLiveModuleActivityCount/ totalEnterpriseCount;  //直播营销活跃率
        double marketingLiveModuleOpenActivityCountRatio = 0.0; //直播营销渗透率
        if (marketingLiveModuleOpenCount != 0) {
            marketingLiveModuleOpenActivityCountRatio = (double) marketingLiveModuleActivityCount / marketingLiveModuleOpenCount;
        }

        //会议营销开通率、活跃率、渗透率
        int marketingConferenceModuleOpenCount = entity.getMarketingConferenceModuleOpenCount();
        int marketingConferenceModuleActivityCount = entity.getMarketingConferenceModuleActivityCount();
        double marketingConferenceModuleOpenCountRatio = (double)marketingConferenceModuleOpenCount / totalEnterpriseCount;  //会议营销开通率
        double marketingConferenceModuleActivityCountRatio = (double)marketingConferenceModuleActivityCount / totalEnterpriseCount;  //会议营销活跃率
        double marketingConferenceModuleOpenActivityCountRatio = 0.0; //会议营销渗透率
        if (marketingConferenceModuleOpenCount != 0) {
            marketingConferenceModuleOpenActivityCountRatio = (double) marketingConferenceModuleActivityCount / marketingConferenceModuleOpenCount;
        }

        //全员营销开通率、活跃率、渗透率
        int marketingEmployeeModuleOpenCount = entity.getMarketingEmployeeModuleOpenCount();
        int marketingEmployeeModuleActivityCount = entity.getMarketingEmployeeModuleActivityCount();
        double marketingEmployeeModuleOpenCountRatio = (double)marketingEmployeeModuleOpenCount / totalEnterpriseCount;   //全员营销开通率
        double marketingEmployeeModuleActivityCountRatio = (double)marketingEmployeeModuleActivityCount / totalEnterpriseCount; //全员营销活跃率
        double marketingEmployeeModuleOpenActivityCountRatio = 0.0; //全员营销渗透率
        if (marketingEmployeeModuleOpenCount != 0) {
            marketingEmployeeModuleOpenActivityCountRatio = (double) marketingEmployeeModuleActivityCount / marketingEmployeeModuleOpenCount;
        }

        //数字展厅开通率、活跃率、渗透率
        int marketingHexagonSiteModuleOpenCount = entity.getMarketingHexagonSiteModuleOpenCount();
        int marketingHexagonSiteModuleActivityCount = entity.getMarketingHexagonSiteModuleActivityCount();
        double marketingHexagonSiteModuleOpenCountRatio = (double)marketingHexagonSiteModuleOpenCount / totalEnterpriseCount;  //数字展厅开通率
        double marketingHexagonSiteModuleActivityCountRatio = (double)marketingHexagonSiteModuleActivityCount / totalEnterpriseCount;  //数字展厅活跃率
        double marketingHexagonSiteModuleOpenActivityCountRatio = 0.0; //数字展厅渗透率
        if (marketingHexagonSiteModuleOpenCount != 0) {
            marketingHexagonSiteModuleOpenActivityCountRatio = (double) marketingHexagonSiteModuleActivityCount / marketingHexagonSiteModuleOpenCount;
        }

        //官网营销开通率、活跃率、渗透率
        int marketingWebsiteModuleOpenCount = entity.getMarketingWebsiteModuleOpenCount();
        int marketingWebsiteModuleActivityCount = entity.getMarketingWebsiteModuleActivityCount();
        double marketingWebsiteModuleOpenCountRatio = (double)marketingWebsiteModuleOpenCount / totalEnterpriseCount;   //官网营销开通率
        double marketingWebsiteModuleActivityCountRatio = (double)marketingWebsiteModuleActivityCount / totalEnterpriseCount;  //官网营销活跃率
        double marketingWebsiteModuleOpenActivityCountRatio = 0.0; //官网营销渗透率
        if (marketingWebsiteModuleOpenCount != 0) {
            marketingWebsiteModuleOpenActivityCountRatio = (double) marketingWebsiteModuleActivityCount / marketingWebsiteModuleOpenCount;
        }

        //公众号营销开通率、活跃率、渗透率
        int marketingWechatAccountModuleOpenCount = entity.getMarketingWechatAccountModuleOpenCount();
        int marketingWechatAccountModuleActivityCount = entity.getMarketingWechatAccountModuleActivityCount();
        double marketingWechatAccountModuleOpenCountRatio = (double)marketingWechatAccountModuleOpenCount / totalEnterpriseCount;  //公众号营销开通率
        double marketingWechatAccountModuleActivityCountRatio = (double)marketingWechatAccountModuleActivityCount / totalEnterpriseCount;  //公众号营销活跃率
        double marketingWechatAccountModuleOpenActivityCountRatio = 0.0; //公众号营销渗透率
        if (marketingWechatAccountModuleOpenCount != 0) {
            marketingWechatAccountModuleOpenActivityCountRatio = (double) marketingWechatAccountModuleActivityCount / marketingWechatAccountModuleOpenCount;
        }

        //企微营销开通率、活跃率、渗透率
        int marketingQywxModuleOpenCount = entity.getMarketingQywxModuleOpenCount();
        int marketingQywxModuleActivityCount = entity.getMarketingQywxModuleActivityCount();
        double marketingQywxModuleOpenCountRatio = (double)marketingQywxModuleOpenCount / totalEnterpriseCount;  //企微营销开通率
        double marketingQywxModuleActivityCountRatio = (double)marketingQywxModuleActivityCount / totalEnterpriseCount;  //企微营销活跃率
        double marketingQywxModuleOpenActivityCountRatio = 0.0; //企微营销渗透率
        if (marketingQywxModuleOpenCount != 0) {
            marketingQywxModuleOpenActivityCountRatio = (double) marketingQywxModuleActivityCount / marketingQywxModuleOpenCount;
        }

        //短信营销开通率、活跃率、渗透率
        int marketingSmsModuleOpenCount = entity.getMarketingSmsModuleOpenCount();
        int marketingSmsModuleActivityCount = entity.getMarketingSmsModuleActivityCount();
        double marketingSmsModuleOpenCountRatio = (double)marketingSmsModuleOpenCount / totalEnterpriseCount;  //短信营销开通率
        double marketingSmsModuleActivityCountRatio = (double)marketingSmsModuleActivityCount / totalEnterpriseCount;  //短信营销活跃率
        double marketingSmsModuleOpenActivityCountRatio = 0.0; //短信营销渗透率
        if (marketingSmsModuleOpenCount != 0) {
            marketingSmsModuleOpenActivityCountRatio = (double) marketingSmsModuleActivityCount / marketingSmsModuleOpenCount;
        }

        //邮件营销开通率、活跃率、渗透率
        int marketingEmailModuleOpenCount = entity.getMarketingEmailModuleOpenCount();
        int marketingEmailModuleActivityCount = entity.getMarketingEmailModuleActivityCount();
        double marketingEmailModuleOpenCountRatio = (double)marketingEmailModuleOpenCount / totalEnterpriseCount;  //邮件营销开通率
        double marketingEmailModuleActivityCountRatio = (double)marketingEmailModuleActivityCount / totalEnterpriseCount;  //邮件营销活跃率
        double marketingEmailModuleOpenActivityCountRatio = 0.0; //邮件营销渗透率
        if (marketingEmailModuleOpenCount != 0){
            marketingEmailModuleOpenActivityCountRatio = (double) marketingEmailModuleActivityCount / marketingEmailModuleOpenCount;
        }

        //广告营销开通率、活跃率、渗透率
        int marketingAdModuleOpenCount = entity.getMarketingAdModuleOpenCount();
        int marketingAdModuleActivityCount = entity.getMarketingAdModuleActivityCount();
        double marketingAdModuleOpenCountRatio = (double)marketingAdModuleOpenCount / totalEnterpriseCount;  //广告营销开通率
        double marketingAdModuleActivityCountRatio = (double)marketingAdModuleActivityCount / totalEnterpriseCount;  //广告营销活跃率
        double marketingAdModuleOpenActivityCountRatio = 0.0; //广告营销渗透率
        if (marketingAdModuleOpenCount != 0) {
            marketingAdModuleOpenActivityCountRatio = (double) marketingAdModuleActivityCount / marketingAdModuleOpenCount;
        }

        //伙伴营销
        int marketingPartnerModuleOpenCount = entity.getMarketingPartnerModuleOpenCount();
        int marketingPartnerModuleActivityCount = entity.getMarketingPartnerModuleActivityCount();
        double marketingPartnerModuleOpenCountRatio = (double)marketingPartnerModuleOpenCount / totalEnterpriseCount;  //伙伴营销开通率
        double marketingPartnerModuleActivityCountRatio = (double)marketingPartnerModuleActivityCount / totalEnterpriseCount;  //伙伴营销活跃率
        double marketingPartnerModuleOpenActivityCountRatio = 0.0; //伙伴营销渗透率
        if (marketingPartnerModuleOpenCount != 0) {
            marketingPartnerModuleOpenActivityCountRatio = (double) marketingPartnerModuleActivityCount / marketingPartnerModuleOpenCount;
        }

        //目标人群
        int marketingUserGroupModuleOpenCount = entity.getMarketingUserGroupModuleOpenCount();
        int marketingUserGroupModuleActivityCount = entity.getMarketingUserGroupModuleActivityCount();
        double marketingUserGroupModuleOpenCountRatio = (double)marketingUserGroupModuleOpenCount / totalEnterpriseCount;  //目标人群开通率
        double marketingUserGroupModuleActivityCountRatio = (double)marketingUserGroupModuleActivityCount / totalEnterpriseCount;  //目标人群活跃率
        double marketingUserGroupModuleOpenActivityCountRatio = 0.0; //目标人群渗透率
        if (marketingUserGroupModuleOpenCount != 0) {
            marketingUserGroupModuleOpenActivityCountRatio = (double) marketingUserGroupModuleActivityCount / marketingUserGroupModuleOpenCount;
        }

        //营销自动化
        int marketingAutomationModuleOpenCount = entity.getMarketingAutomationModuleOpenCount();
        int marketingAutomationModuleActivityCount = entity.getMarketingAutomationModuleActivityCount();
        double marketingAutomationModuleOpenCountRatio = (double)marketingAutomationModuleOpenCount / totalEnterpriseCount;  //营销自动化开通率
        double marketingAutomationModuleActivityCountRatio = (double)marketingAutomationModuleActivityCount / totalEnterpriseCount;  //营销自动化活跃率
        double marketingAutomationModuleOpenActivityCountRatio = 0.0; //营销开通活跃率
        if (marketingAutomationModuleOpenCount != 0) {
            marketingAutomationModuleOpenActivityCountRatio = (double) marketingAutomationModuleActivityCount / marketingAutomationModuleOpenCount;
        }

        //whatsapp推广
        int marketingWhatsappModuleOpenCount = entity.getMarketingWhatsappModuleOpenCount();
        int marketingWhatsappModuleActivityCount = entity.getMarketingWhatsappModuleActivityCount();
        double marketingWhatsappModuleOpenCountRatio = (double)marketingWhatsappModuleOpenCount / totalEnterpriseCount;  //whatsapp开通率
        double marketingWhatsappModuleActivityCountRatio = (double)marketingWhatsappModuleActivityCount / totalEnterpriseCount;  //whatsapp活跃率
        double marketingWhatsappModuleOpenActivityCountRatio = 0.0; //whatsapp渗透率
        if (marketingWhatsappModuleOpenCount != 0) {
            marketingWhatsappModuleOpenActivityCountRatio = (double) marketingWhatsappModuleActivityCount / marketingWhatsappModuleOpenCount;
        }
        log.info("********************* begin to print statistics data *********************");
        log.info("活动营销-统计企业类型:{}    企业总数:{}     活动营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingActivityModuleOpenCount, marketingActivityModuleActivityCount, numberFormat.format(marketingActivityModuleOpenCountRatio), numberFormat.format(marketingActivityModuleActivityCountRatio), numberFormat.format(marketingActivityModuleOpenActivityCountRatio), entity.getMarketingActivityModuleEas(), entity.getMarketingActivityModuleActivityEas());
        log.info("直播营销-统计企业类型:{}    企业总数:{}     直播营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingLiveModuleOpenCount, marketingLiveModuleActivityCount, numberFormat.format(marketingLiveModuleOpenCountRation), numberFormat.format(marketingLiveModuleActivityCountRatio), numberFormat.format(marketingLiveModuleOpenActivityCountRatio), entity.getMarketingLiveModuleEas(), entity.getMarketingLiveModuleActivityEas());
        log.info("会议营销-统计企业类型:{}    企业总数:{}     会议营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingConferenceModuleOpenCount, marketingConferenceModuleActivityCount, numberFormat.format(marketingConferenceModuleOpenCountRatio), numberFormat.format(marketingConferenceModuleActivityCountRatio), numberFormat.format(marketingConferenceModuleOpenActivityCountRatio), entity.getMarketingConferenceModuleEas(),entity.getMarketingConferenceModuleActivityEas());
        log.info("全员营销-统计企业类型:{}    企业总数:{}     全员营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingEmployeeModuleOpenCount, marketingEmployeeModuleActivityCount, numberFormat.format(marketingEmployeeModuleOpenCountRatio), numberFormat.format(marketingEmployeeModuleActivityCountRatio), numberFormat.format(marketingEmployeeModuleOpenActivityCountRatio), entity.getMarketingEmployeeModuleEas(), entity.getMarketingEmployeeModuleActivityEas());
        log.info("数字展厅-统计企业类型:{}    企业总数:{}     数字展厅企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingHexagonSiteModuleOpenCount, marketingHexagonSiteModuleActivityCount, numberFormat.format(marketingHexagonSiteModuleOpenCountRatio), numberFormat.format(marketingHexagonSiteModuleActivityCountRatio), numberFormat.format(marketingHexagonSiteModuleOpenActivityCountRatio), entity.getMarketingHexagonSiteModuleEas(), entity.getMarketingHexagonSiteModuleActivityEas());
        log.info("官网营销-统计企业类型:{}    企业总数:{}     官网营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingWebsiteModuleOpenCount, marketingWebsiteModuleActivityCount, numberFormat.format(marketingWebsiteModuleOpenCountRatio), numberFormat.format(marketingWebsiteModuleActivityCountRatio),numberFormat.format(marketingWebsiteModuleOpenActivityCountRatio), entity.getMarketingWebsiteModuleEas(), entity.getMarketingWebsiteModuleActivityEas());
        log.info("公众号营销-统计企业类型:{}    企业总数:{}     公众号营销企业开通数:{}  企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingWechatAccountModuleOpenCount, marketingWechatAccountModuleActivityCount, numberFormat.format(marketingWechatAccountModuleOpenCountRatio), numberFormat.format(marketingWechatAccountModuleActivityCountRatio), numberFormat.format(marketingWechatAccountModuleOpenActivityCountRatio), entity.getMarketingWechatAccountModuleEas(), entity.getMarketingWechatAccountModuleActivityEas());
        log.info("企微营销-统计企业类型:{}    企业总数:{}     企微营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingQywxModuleOpenCount, marketingQywxModuleActivityCount, numberFormat.format(marketingQywxModuleOpenCountRatio), numberFormat.format(marketingQywxModuleActivityCountRatio), numberFormat.format(marketingQywxModuleOpenActivityCountRatio), entity.getMarketingQywxModuleEas(), entity.getMarketingQywxModuleActivityEas());
        log.info("短信营销-统计企业类型:{}    企业总数:{}     短信营销企业开通数:{}    企业活跃数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingSmsModuleOpenCount, marketingSmsModuleActivityCount, numberFormat.format(marketingSmsModuleOpenCountRatio), numberFormat.format(marketingSmsModuleActivityCountRatio), numberFormat.format(marketingSmsModuleOpenActivityCountRatio), entity.getMarketingSmsModuleEas(), entity.getMarketingSmsModuleActivityEas());
        log.info("邮件营销-统计企业类型:{}    企业总数:{}     邮件营销企业开通数:{}    活跃企业数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingEmailModuleOpenCount, marketingEmailModuleActivityCount, numberFormat.format(marketingEmailModuleOpenCountRatio), numberFormat.format(marketingEmailModuleActivityCountRatio), numberFormat.format(marketingEmailModuleOpenActivityCountRatio), entity.getMarketingEmailModuleEas(), entity.getMarketingEmailModuleActivityEas());
        log.info("广告营销-统计企业类型:{}    企业总数:{}     广告营销企业开通数:{}    活跃企业数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingAdModuleOpenCount, marketingAdModuleActivityCount, numberFormat.format(marketingAdModuleOpenCountRatio), numberFormat.format(marketingAdModuleActivityCountRatio), numberFormat.format(marketingAdModuleOpenActivityCountRatio), entity.getMarketingAdModuleEas(), entity.getMarketingAdModuleActivityEas());
        log.info("伙伴营销-统计企业类型:{}    企业总数:{}     伙伴营销企业开通数:{}    活跃企业数:{}    渗透率:{}    活跃率:{}    开通活跃率:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingPartnerModuleOpenCount, marketingPartnerModuleActivityCount, numberFormat.format(marketingPartnerModuleOpenCountRatio), numberFormat.format(marketingPartnerModuleActivityCountRatio), numberFormat.format(marketingPartnerModuleOpenActivityCountRatio), entity.getMarketingPartnerModuleEas(), entity.getMarketingPartnerModuleActivityEas());
        log.info("目标人群-统计企业类型:{}    企业总数:{}     目标人群企业开通数:{}    活跃企业数:{}    渗透率:{}    活跃率:{}    开通活跃:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingUserGroupModuleOpenCount, marketingUserGroupModuleActivityCount, numberFormat.format(marketingUserGroupModuleOpenCountRatio), numberFormat.format(marketingUserGroupModuleActivityCountRatio), numberFormat.format(marketingUserGroupModuleOpenActivityCountRatio), entity.getMarketingUserGroupModuleEas(), entity.getMarketingUserGroupModuleActivityEas());
        log.info("自动化营销-统计企业类型:{}    企业总数:{}     自动化营销企业开通数:{}    活跃企业数:{}    渗透率:{}    活跃率:{}    开通活跃:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingAutomationModuleOpenCount, marketingAutomationModuleActivityCount, numberFormat.format(marketingAutomationModuleOpenCountRatio), numberFormat.format(marketingAutomationModuleActivityCountRatio), numberFormat.format(marketingAutomationModuleOpenActivityCountRatio), entity.getMarketingAutomationModuleEas(), entity.getMarketingAutomationModuleActivityEas());
        log.info("whats营销统计企业类型:{}    企业总数:{}     whats营销企业开通数:{}    活跃企业数:{}    渗透率:{}    活跃率:{}    开通活跃:{}     开通企业账号:{}     活跃企业账号:{}", entity.getStatisticsType(), totalEnterpriseCount, marketingWhatsappModuleOpenCount, marketingWhatsappModuleActivityCount, numberFormat.format(marketingWhatsappModuleOpenCountRatio), numberFormat.format(marketingWhatsappModuleActivityCountRatio), numberFormat.format(marketingWhatsappModuleOpenActivityCountRatio), entity.getMarketingWhatsappModuleEas(), entity.getMarketingWhatsappModuleActivityEas());
        log.info("********************* end to print statistics data *********************");


    }

    private BaseVisitTrendResult getVisitData(BaseTimingArg arg,String appId) {
        List<DateUtil.DateArg>  dateArgList;
        Map<String, BaseVisitTrendResult> dateNumMap = Maps.newConcurrentMap();
        try {
            dateArgList = DateUtil.getMonAndWeekAndDate(arg.getStartTime(), arg.getEndTime(), "yyyyMMdd");
        } catch (ParseException e) {
            return null;
        }
        if (CollectionUtils.isEmpty(dateArgList)){
            return null;
        }

        log.info("visitTrend dateArgList:{}", dateArgList);
        for (DateUtil.DateArg data : dateArgList) {
            BaseVisitTrendResult baseVisitTrendResult = null;
            try {
                baseVisitTrendResult = miniAppSettingManager.getVisitTrend(appId, data.getStartDate(), data.getEndDate(), data.getTimeRangeTypeEnum());
                log.info("visitTrend result:{}", baseVisitTrendResult);
                if (baseVisitTrendResult != null) {
                    dateNumMap.put(data.getStartDate()+"-"+data.getEndDate(),baseVisitTrendResult);
                }
            } catch (Exception e) {
                log.warn("MiniAppSettingServiceImpl.visitTrend error e:{}", e);
                continue;
            }
        }
        log.info("dateNumMap:{}", dateNumMap);

        Long countPv=0L;
        Long countUv=0L;
        for (Map.Entry<String, BaseVisitTrendResult> entry : dateNumMap.entrySet()) {
            countUv+=entry.getValue().getVisitUv();
            countPv+=entry.getValue().getVisitPv();
        }
        BaseVisitTrendResult result = new BaseVisitTrendResult();
        result.setVisitPv(countPv);
        result.setVisitUv(countUv);

        return result;
    }

    @Data
    public static class StatisticsData implements Serializable {
        private int enterpriseType = 0;   // 0:白名单企业 1:购买企业 2：免费企业  3：试用企业
        private List<String> eaList;
        private int totalCount = 0;
        private int totalEnterpriseCount = 0;
        private int marketingActivityModuleOpenCount = 0;  //活动营销开通企业数量
        private int marketingActivityModuleActivityCount = 0;  //活动营销活跃企业数量
        private List<String> marketingActivityModuleOpenList = Lists.newArrayList();  //活动营销开通企业列表
        private List<String> marketingActivityModuleActivityList = Lists.newArrayList();  //活动营销活跃企业列表

        private int marketingLiveModuleOpenCount = 0;   //直播营销开通企业数量
        private int marketingLiveModuleActivityCount = 0;  //直播营销活跃企业数量
        private List<String> marketingLiveModuleOpenList = Lists.newArrayList();  //直播营销开通企业列表
        private List<String> marketingLiveModuleActivityList = Lists.newArrayList();  //直播营销活跃企业列表

        private int marketingConferenceModuleOpenCount = 0;  //会议营销开通企业数量
        private int marketingConferenceModuleActivityCount = 0;  //会议营销活跃企业数量
        private List<String> marketingConferenceModuleOpenList = Lists.newArrayList();  //会议营销开通企业列表
        private List<String> marketingConferenceModuleActivityList = Lists.newArrayList();  //会议营销活跃企业列表

        private int marketingEmployeeModuleOpenCount = 0;  //全员营销开通企业数量
        private int marketingEmployeeModuleActivityCount = 0;  //全员营销活跃企业数量
        private List<String> marketingEmployeeModuleOpenList = Lists.newArrayList();  //全员营销开通企业列表
        private List<String> marketingEmployeeModuleActivityList = Lists.newArrayList();  //全员营销活跃企业列表

        private int marketingHexagonSiteModuleOpenCount = 0;  //数字展厅开通企业数量
        private int marketingHexagonSiteModuleActivityCount = 0;  //数字展厅活跃企业数量
        private List<String> marketingHexagonSiteModuleOpenList = Lists.newArrayList();  //数字展厅开通企业列表
        private List<String> marketingHexagonSiteModuleActivityList = Lists.newArrayList();  //数字展厅活跃企业列表

        private int marketingWebsiteModuleOpenCount = 0;  //官网开通企业数量
        private int marketingWebsiteModuleActivityCount = 0;  //官网活跃企业数量
        private List<String> marketingWebsiteModuleOpenList = Lists.newArrayList();  //官网开通企业列表
        private List<String> marketingWebsiteModuleActivityList = Lists.newArrayList();  //官网活跃企业列表

        private int marketingWechatAccountModuleOpenCount = 0;  //微信公众号开通企业数量
        private int marketingWechatAccountModuleActivityCount = 0;  //微信公众号活跃企业数量
        private List<String> marketingWechatAccountModuleOpenList = Lists.newArrayList();  //微信公众号开通企业列表
        private List<String> marketingWechatAccountModuleActivityList = Lists.newArrayList();  //微信公众号活跃企业列表

        private int marketingQywxModuleOpenCount = 0;  //企微营销开通企业数量
        private int marketingQywxModuleActivityCount = 0;  //企微营销信活跃企业数量
        private List<String> marketingQywxModuleOpenList = Lists.newArrayList();  //企微营销开通企业列表
        private List<String> marketingQywxModuleActivityList = Lists.newArrayList();  //企微营销信活跃企业列表

        private int marketingSmsModuleOpenCount = 0;  //短信营销开通企业数量
        private int marketingSmsModuleActivityCount = 0;  //短信营销活跃企业数量
        private List<String> marketingSmsModuleOpenList = Lists.newArrayList();  //短信营销开通企业列表
        private List<String> marketingSmsModuleActivityList = Lists.newArrayList();  //短信营销活跃企业列表

        private int marketingEmailModuleOpenCount = 0;  //邮件营销开通企业数量
        private int marketingEmailModuleActivityCount = 0;  //邮件营销活跃企业数量
        private List<String> marketingEmailModuleOpenList = Lists.newArrayList();  //邮件营销开通企业列表
        private List<String> marketingEmailModuleActivityList = Lists.newArrayList();  //邮件营销活跃企业列表

        private int marketingAdModuleOpenCount = 0;  //广告营销开通企业数量
        private int marketingAdModuleActivityCount = 0;  //广告营销活跃企业数量
        private List<String> marketingAdModuleOpenList = Lists.newArrayList();  //广告营销开通企业列表
        private List<String> marketingAdModuleActivityList = Lists.newArrayList();  //广告营销活跃企业列表
    }
}