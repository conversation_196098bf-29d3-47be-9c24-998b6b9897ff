/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.marketingactivity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.GetMarketingActivityDetailData;
import com.facishare.marketing.api.arg.PartnerNoticeSendArg;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPreviewArg;
import com.facishare.marketing.api.arg.marketingactivity.SendArg;
import com.facishare.marketing.api.arg.marketingactivity.cancelSpreadActivity;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.WxAutoReplyRuleResult;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.GetNoticeResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityPreviewData;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityActionService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.api.vo.ExternalConfigVO;
import com.facishare.marketing.api.vo.MarketingActivityExternalConfigVO;
import com.facishare.marketing.api.vo.WeChatServiceMarketingActivityVO;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData.FileAttachment;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LiveParamEnum;
import com.facishare.marketing.common.enums.wx.WxAutoReplyRuleStatusEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.wx.WxAutoReplyRuleDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceEntity;
import com.facishare.marketing.provider.entity.data.MarketingActivityNoticeSendData;
import com.facishare.marketing.provider.entity.data.WeChatServiceMarketingActivityData;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendTaskEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.entity.wx.WxAutoReplyRuleEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.feed.MaterailDataManagerFactory;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasAddMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasUpdateMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.ConvertUtil;
import com.facishare.marketing.provider.util.MarketingJobUtil;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.wechat.dubborestouterapi.data.WeChatMenuFormData;
import com.facishare.wechat.dubborestouterapi.service.union.WeChatCustomerMenuRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.sender.api.enums.RedirectTypeEnum;
import com.facishare.wechat.sender.api.enums.WeChatMsgTypeEnum;
import com.facishare.wechat.sender.api.result.NoticeSendResult;
import com.facishare.wechat.sender.api.service.NoticeService;
import com.facishare.wechat.sender.api.vo.NoticeSendVo;
import com.facishare.wechat.union.common.data.TagSearchQueryData;
import com.facishare.wechat.union.common.data.TemplateMessageVariableData;
import com.facishare.wechat.union.common.data.TemplateMessageWxOpenIdData;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.facishare.wechat.union.core.api.model.result.WechatInfoAndFunsNumResult;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.MarketingActivityData;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: dongzhb
 * @date: 2019/3/8
 * @Description:
 */
@Slf4j
@Service("weChatServiceManager")
public class WeChatServiceManager implements MarketingActivityActionService {
    private static Map<Integer, Integer> weChatServiceSendStatusEnumMap = Maps.newHashMap();
    private static Map<Integer, Integer> wechatTemplateMessageSendStatusEnumMap = Maps.newHashMap();
    private static Pattern CONTENT_URLS_PATTERN = Pattern.compile("href=\"(.+)\"");
    private static Pattern URL_PATTERN = Pattern.compile("([^?]+)\\?.*businessUrl=([^&]+)");
    // 互联平台数据库最大标题长度
    private static final int MAX_TITLE_LENGTH = 50;

    static {
        weChatServiceSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus(), SendStatusEnum.WAIT_SEND.getStatus());
        weChatServiceSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.SENDING.getStatus(), SendStatusEnum.PROCESSING.getStatus());
        weChatServiceSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.FAILED.getStatus(), SendStatusEnum.FAIL.getStatus());
        weChatServiceSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.COMPLETE_SENDING.getStatus(), SendStatusEnum.FINISHED.getStatus());

        wechatTemplateMessageSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus(), SendStatusEnum.WAIT_SEND.getStatus());
        wechatTemplateMessageSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.SENDING.getStatus(), SendStatusEnum.PROCESSING.getStatus());
        wechatTemplateMessageSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.FAILED.getStatus(), SendStatusEnum.FAIL.getStatus());
        wechatTemplateMessageSendStatusEnumMap.put(com.facishare.wechat.sender.api.enums.SendStatusEnum.COMPLETE_SENDING.getStatus(), SendStatusEnum.FINISHED.getStatus());
    }

    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private WeChatServiceNoticeManager weChatServiceNoticeManager;
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MaterailDataManagerFactory materailDataManagerFactory;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private WxAutoReplyRuleDao wxAutoReplyRuleDao;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private WeChatCustomerMenuRestService weChatCustomerMenuRestService;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @ReloadableProperty("host")
    private String host;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Override
    public AddMarketingActivityResult doAddAction(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        if (isMarketingJobForbidExec(ea, addMarketingActivityArg)) {
            AddMarketingActivityResult addMarketingActivityResult = new AddMarketingActivityResult();
            addMarketingActivityResult.setCrmErrorCode(SHErrorCode.FORBID_SEND_MARKETING_MESSAGE.getErrorCode());
            addMarketingActivityResult.setCrmErrorMsg(MarketingJobUtil.getErrorMessage());
            return addMarketingActivityResult;
        }
        if (addMarketingActivityArg.getWechatMessageType().equals(WechatMessageTypeEnum.WECHAT_SERVICE.getType())) {
            return doAddWeChatServiceMarketingActivityResult(ea, fsUserId, addMarketingActivityArg);
        } else if (addMarketingActivityArg.getWechatMessageType().equals(WechatMessageTypeEnum.WECHAT_TEMPLATE_MESSAGE.getType())) {
            return doAddWechatTemplateMessageMarketingActivityResult(ea, fsUserId, addMarketingActivityArg);
        }
        return null;
    }

    private boolean isMarketingJobForbidExec(String ea, AddMarketingActivityArg addMarketingActivityArg) {
        Integer type = null;
        Long fixTime = null;
        if (addMarketingActivityArg.getWechatMessageType().equals(WechatMessageTypeEnum.WECHAT_SERVICE.getType())) {
            type = addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getType();
            fixTime = addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getFixedTime();
        } else if (addMarketingActivityArg.getWechatMessageType().equals(WechatMessageTypeEnum.WECHAT_TEMPLATE_MESSAGE.getType())) {
            type = addMarketingActivityArg.getWeChatTemplateMessageVO().getType();
            fixTime = addMarketingActivityArg.getWeChatTemplateMessageVO().getFixedTime();
        }
        if (type == null) {
            return true;
        }
        if (type == 1) {
            return MarketingJobUtil.isMarketingJobForbidExec(ea);
        }
        if (type == 2) {
            return MarketingJobUtil.isMarketingJobForbidExecByScheduleTime(fixTime);
        }
        return true;
    }

    @Override
    public GetMarketingActivityResult doDetailAction(String ea, Integer fsUserId, GetMarketingActivityDetailData getMarketingActivityDetailData) {
        //todo CCQ
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(getMarketingActivityDetailData.getId());
        Integer associateIdType = marketingActivityExternalConfigEntity.getAssociateIdType();
        if (AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() != associateIdType && AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType() != associateIdType){
            return null;
        }
        GetMarketingActivityResult result;
        if (associateIdType.equals(AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType())) {
            result = doDetailWeChatServiceMarketingActivityResult(ea, fsUserId, getMarketingActivityDetailData, marketingActivityExternalConfigEntity);
        } else{
            result = doWechatTemplateMessageMarketingActivityResult(ea, fsUserId, getMarketingActivityDetailData, marketingActivityExternalConfigEntity);
        }
        if (result == null){
            return null;
        }
        GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByMarketingActivityId(marketingActivityExternalConfigEntity.getMarketingActivityId());
        if (getNoticeResult != null && getNoticeResult.getStatus() != null && com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus() == getNoticeResult.getStatus()){
            result.setSendCancelable(true);
        }
        return result;
    }

    @Override
    public com.facishare.marketing.common.result.Result<Boolean> doDeleteAction(String ea, Integer fsUserId, String marketingActivityId) {
        return com.facishare.marketing.common.result.Result.newSuccess();
    }

    @Override
    public AddMarketingActivityResult doUpdateAction(String ea, Integer fsUserId, AddMarketingActivityArg updateMarketingActivityArg) {
        return null;
    }

    @Override
    public AddMarketingActivityArg.MarketingActivityAuditData getMarketingActivityAuditData(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        AddMarketingActivityArg.MarketingActivityAuditData data = new AddMarketingActivityArg.MarketingActivityAuditData();
        if (AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() == addMarketingActivityArg.getWechatMessageType()) {
            AddMarketingActivityArg.WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO = addMarketingActivityArg.getWeChatServiceMarketingActivityVO();
            String appId = weChatServiceMarketingActivityVO.getAppId();
            ModelResult<String> stringModelResult = outerServiceWechatService.transAppIdToWxAppId(appId);
            String wxAppId = stringModelResult.getResult();
            Integer calculateCount = calculateCount(ea, fsUserId, weChatServiceMarketingActivityVO.getSendRange(), wxAppId, weChatServiceMarketingActivityVO.getFilters(), weChatServiceMarketingActivityVO.getMarketingUserGroupIds(), weChatServiceMarketingActivityVO.getTagIdList());
            data.setSendGroupCount(calculateCount);
            ModelResult<WechatInfoAndFunsNumResult> wechatInfoAndFunsNum = outerServiceWechatService.getWechatInfoAndFunsNum(appId, ea);
            if (wechatInfoAndFunsNum != null && wechatInfoAndFunsNum.getResult() != null) {
                data.setSendAccount(wechatInfoAndFunsNum.getResult().getApiName());
            }
            if (weChatServiceMarketingActivityVO.getMsgType() == 2) {
                data.setSendContent(weChatServiceMarketingActivityVO.getContent());
            }
            if (StringUtils.isNotEmpty(weChatServiceMarketingActivityVO.getGraphicMessagePic())) {
                List<Map<String, Object>> imgs = Lists.newArrayList();
                String attachmentPath = weChatServiceMarketingActivityVO.getGraphicMessagePic();
                byte[] bytes = null;
                if (attachmentPath.startsWith("TA_")) {
                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(attachmentPath, ea, null);
                    if (null != fileManagerPicResult && StringUtils.isNotBlank(fileManagerPicResult.getUrlAPath())) {
                        bytes = fileV2Manager.downloadAFile(fileManagerPicResult.getUrlAPath(), -10000, ea);
                    }
                } else if (attachmentPath.startsWith("A_")) {
                    bytes = fileV2Manager.downloadAFile(attachmentPath, -10000, ea);
                } else if (attachmentPath.startsWith("C_")) {
                    bytes = fileV2Manager.downloadFileByUrl(fileV2Manager.getUrlByPath(ea, attachmentPath), null);
                }
                if (bytes != null) {
                    FileV2Manager.FileManagerPicResult jpg = fileV2Manager.uploadToNPath(bytes, "jpg", ea, -10000);
                    Map<String, Object> img = new HashMap<>();
                    img.put("ext", "jpg");
                    img.put("path", jpg.getNPath().split("\\.")[0]);
                    img.put("filename", jpg.getNPath());
                    img.put("create_time", new Date().getTime());
                    img.put("size", jpg.getSize());
                    imgs.add(img);
                }
                data.setSendAttachment(imgs);
            }
        } else if (AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType() == addMarketingActivityArg.getWechatMessageType()) {
            WeChatTemplateMessageData weChatTemplateMessageVO = addMarketingActivityArg.getWeChatTemplateMessageVO();
            String appId = weChatTemplateMessageVO.getAppId();
            ModelResult<String> stringModelResult = outerServiceWechatService.transAppIdToWxAppId(appId);
            String wxAppId = stringModelResult.getResult();
            Integer calculateCount = calculateCount(ea, fsUserId, weChatTemplateMessageVO.getSendRange(), wxAppId, weChatTemplateMessageVO.getFilters(), weChatTemplateMessageVO.getMarketingUserGroupIds(), weChatTemplateMessageVO.getTagIdList());
            data.setSendGroupCount(calculateCount);
            ModelResult<WechatInfoAndFunsNumResult> wechatInfoAndFunsNum = outerServiceWechatService.getWechatInfoAndFunsNum(appId, ea);
            if (wechatInfoAndFunsNum != null && wechatInfoAndFunsNum.getResult() != null) {
                data.setSendAccount(wechatInfoAndFunsNum.getResult().getApiName());
            }
            data.setSendContent(weChatTemplateMessageVO.getContent());
        }
        return data;
    }

    private Integer calculateCount(String ea, Integer fsUserId, Integer sendRange, String appId, List<Map<String, Object>> filters, List<String> marketingUserGroupIds, List<TagName> tagIdList) {
        Integer totalCount = 0;
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        List<Integer> dataPermission = null;
        if (isOpen) {
            dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
        }
        if (0 == sendRange) {
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("wx_app_id", "EQ", Collections.singletonList(appId));
//            if (isOpen) {
//                paasQueryArg.addFilter("data_own_organization", "IN", dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//            }
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
            findByQueryV3Arg.setSelectFields(Collections.singletonList("_id"));
            InnerPage<ObjectData> result = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
            totalCount = result.getTotalCount();
        } else if (1 == sendRange) {
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            List<PaasQueryArg.Condition> conditions = filters.stream().map(e -> {
                if ("tag".equals(e.get("fieldName"))) {
                    e.put("fieldType", 11);
                    e.put("operator", "IN".equals(e.get("operator")) ? "LIKE" : e.get("operator"));
                }
                PaasQueryArg.Condition condition = new PaasQueryArg.Condition(e.get("fieldName").toString(), ((List<String>) e.get("fieldValues")), e.get("operator").toString());
                condition.setValueType(Integer.valueOf(e.get("fieldType").toString()));
                return condition;
            }).collect(Collectors.toList());
            paasQueryArg.setFilters(conditions);
            paasQueryArg.addFilter("wx_app_id", "EQ", Collections.singletonList(appId));
//            if (isOpen) {
//                paasQueryArg.addFilter("data_own_organization", "IN", dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//            }
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
            findByQueryV3Arg.setSelectFields(Collections.singletonList("_id"));
            InnerPage<ObjectData> result = crmMetadataManager.listV3(ea, -10000, findByQueryV3Arg);
            totalCount = result.getTotalCount();
        } else if (4 == sendRange) {
            Set<String> marketingUserIds = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(ea, marketingUserGroupIds);
            List<String> wxUserIds = marketingUserGroupManager.doListCrmWxUserByMarketingUserIds(ea, marketingUserIds);
            totalCount = wxUserIds.size();
        } else if (5 == sendRange) {
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            List<PaasQueryArg.Condition> conditions = tagIdList.stream().map(e -> {
                PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag", Collections.singletonList(e.getCombineName()), "IN");
                condition.setValueType(11);
                return condition;
            }).collect(Collectors.toList());
            paasQueryArg.setFilters(conditions);
            paasQueryArg.addFilter("wx_app_id", "EQ", Collections.singletonList(appId));
//            if (isOpen) {
//                paasQueryArg.addFilter("data_own_organization", "IN", dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//            }
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
            findByQueryV3Arg.setSelectFields(Collections.singletonList("_id"));
            InnerPage<ObjectData> result = crmMetadataManager.listV3(ea, -10000, findByQueryV3Arg);
            totalCount = result.getTotalCount();
        }
        return totalCount;
    }

    private GetMarketingActivityResult doWechatTemplateMessageMarketingActivityResult(String ea, Integer fsUserId, GetMarketingActivityDetailData getMarketingActivityDetailData,
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity) {
        if (AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType() != marketingActivityExternalConfigEntity.getAssociateIdType()) {
            log.warn("WechatTemplateMessageManager doDetailAction,AssociateIdType don't match , AssociateIdType : {} ", marketingActivityExternalConfigEntity.getAssociateIdType());
            return null;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.facishare.marketing.provider.entity.data.WeChatTemplateMessageData weChatTemplateMessageData = marketingActivityExternalConfigEntity.getExternalConfig().getWeChatTemplateMessageVO();
        String appId = weChatTemplateMessageData.getAppId();
        Integer associateId = marketingActivityExternalConfigEntity.getAssociateId() == null ? null : Integer.valueOf(marketingActivityExternalConfigEntity.getAssociateId());
        GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByIdAndEiAndAppId(associateId, ei, appId);
        GetMarketingActivityResult result = this
            .convert(ea, fsUserId, getMarketingActivityDetailData, getNoticeResult, marketingActivityExternalConfigEntity.getExternalConfig().getWeChatTemplateMessageVO());
        if (weChatTemplateMessageData.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.MATERIAL.getType())) {
            AbstractMaterialData materialData = materailDataManagerFactory.get(weChatTemplateMessageData.getMaterialType()).get(marketingActivityExternalConfigEntity.getEa(),weChatTemplateMessageData.getMaterialId());
            result.getWeChatTemplateMessageVO().setMaterialData(materialData);
        }
        return result;
    }

    private GetMarketingActivityResult doDetailWeChatServiceMarketingActivityResult(String ea, Integer fsUserId, GetMarketingActivityDetailData getMarketingActivityDetailData,
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity) {
        GetMarketingActivityResult getMarketingActivityResult = new GetMarketingActivityResult();
        getMarketingActivityResult.setSpreadType(getMarketingActivityDetailData.getSpreadType());
        getMarketingActivityResult.setStatus(getMarketingActivityDetailData.getStatus());
        BeanUtils.copyProperties(getMarketingActivityDetailData, getMarketingActivityResult);
        String appId = marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().getAppId();
        WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO = new WeChatServiceMarketingActivityVO();
        BeanUtils.copyProperties(marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO(), weChatServiceMarketingActivityVO);
        weChatServiceMarketingActivityVO.setMsgId(marketingActivityExternalConfigEntity.getAssociateId());
        OuterServiceResult outerServiceInfo = outerServiceWechatManager.getOuterServiceInfo(ea, fsUserId, appId);
        if (outerServiceInfo != null) {
            weChatServiceMarketingActivityVO.setAppLogoUrl(outerServiceInfo.getAppLogoFxiaokeUrl());
            weChatServiceMarketingActivityVO.setAppName(outerServiceInfo.getAppName());
        }
        if (SendStatusEnum.isSendActionHandled(getMarketingActivityDetailData.getStatus())) {
            Integer wxNoticeId = Integer.valueOf(marketingActivityExternalConfigEntity.getAssociateId());
            Integer ei = eieaConverter.enterpriseAccountToId(marketingActivityExternalConfigEntity.getEa());
            GetNoticeResult result = weChatServiceNoticeManager.findByIdAndEiAndAppId(wxNoticeId, ei, appId);
            if (result != null) {
                weChatServiceMarketingActivityVO.setSendTime(result.getSendTime());
                weChatServiceMarketingActivityVO.setContent(result.getContent());
                weChatServiceMarketingActivityVO.setActualCompletedCount(result.getActualCompletedCount());
                weChatServiceMarketingActivityVO.setNeedSendCount(result.getNeedSendCount());
                /**计算发送失败数*/
                weChatServiceMarketingActivityVO.setFailSendCount(
                    Math.subtractExact(result.getNeedSendCount() == null ? 0 : result.getNeedSendCount(), result.getActualCompletedCount() == null ? 0 : result.getActualCompletedCount()));
            }
        }
        getMarketingActivityResult.setWechatMessageType(AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType());
        getMarketingActivityResult.setWeChatServiceMarketingActivityVO(weChatServiceMarketingActivityVO);
        return getMarketingActivityResult;
    }

    private AddMarketingActivityResult doAddWechatTemplateMessageMarketingActivityResult(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        WeChatTemplateMessageData weChatTemplateMessageVO = addMarketingActivityArg.getWeChatTemplateMessageVO();
        if (CollectionUtils.isNotEmpty(weChatTemplateMessageVO.getCampaignIds())) {
            weChatTemplateMessageVO.setSendRange(com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_WECHAT_FANOBJ_OPEN_IDS.getCode());
        }
        weChatTemplateMessageVO.setDefaultRedirectType();
        if (!weChatTemplateMessageVO.checkRedirectTypeAndRedirectUrlValid() || !weChatTemplateMessageVO.checkTemplateMessageDatasValid()) {
            AddMarketingActivityResult result = new AddMarketingActivityResult();
            result.setErrorCode(SHErrorCode.PARAMS_ERROR);
            return result;
        }
        /** 插入营销活动 */
        if(weChatTemplateMessageVO.getRedirectUrl() != null){
            weChatTemplateMessageVO.setRedirectUrl(weChatTemplateMessageVO.getRedirectUrl().trim());
        }
        String marketingEventId = addMarketingActivityArg.getMarketingEventId();
        Integer spreadType = addMarketingActivityArg.getSpreadType();
        String title = weChatTemplateMessageVO.getTitle();
        String wxAppId = addMarketingActivityArg.getWeChatTemplateMessageVO().getAppId();
        Optional<String> wxAppIdRes = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(wxAppId);
        if (wxAppIdRes.isPresent()) {
            wxAppId = wxAppIdRes.get();
        }
        Integer status = wechatTemplateMessageSendStatusEnumMap.get(com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus());
        boolean needAudit = marketingActivityAuditManager.isNeedAudit(ea);
        ObjectData objectData = null;
        if (needAudit) {
            objectData = marketingActivityAuditManager.buildObjectData(ea, addMarketingActivityArg.getMarketingActivityAuditData());
        } else {
            status = wechatTemplateMessageSendStatusEnumMap.get(com.facishare.wechat.sender.api.enums.SendStatusEnum.SENDING.getStatus());
        }
        com.facishare.marketing.common.result.Result<String> marketingActivityIdResult = addMarketingActivityMetadata(ea, fsUserId, marketingEventId, spreadType, title, status,wxAppId, objectData);
        if (!marketingActivityIdResult.isSuccess()){
            AddMarketingActivityResult result = new AddMarketingActivityResult();
            result.setCrmErrorCode(marketingActivityIdResult.getErrCode());
            result.setCrmErrorMsg(marketingActivityIdResult.getErrMsg());
            return result;
        }
        String marketingActivityId = marketingActivityIdResult.getData();

        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        /** 链接特殊字段替换*/
        if (com.facishare.marketing.common.enums.RedirectTypeEnum.MATERIAL.getType().equals(weChatTemplateMessageVO.getRedirectType())) {
            Optional<String> optionalWxAppId = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(weChatTemplateMessageVO.getAppId());
            if (!optionalWxAppId.isPresent()) {
                // 作废CRM营销活动（一致性）
                crmV2Manager.bulkInvalid(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), ImmutableList.of(marketingActivityId));
                return null;
            }
            String redirectUrl = ReplaceUtil.replaceWxAppId(weChatTemplateMessageVO.getRedirectUrl(), optionalWxAppId.get());
            redirectUrl = ReplaceUtil.replaceMarketingActivityId(redirectUrl, marketingActivityId);
            weChatTemplateMessageVO.setRedirectUrl(redirectUrl);
        } else if (com.facishare.marketing.common.enums.RedirectTypeEnum.LIVE_VIEW.getType().equals(weChatTemplateMessageVO.getRedirectType())) {
            MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
            String redirectUrl = "{直播链接}";
            if (liveEntity != null) {
                List<LiveParamEnum> needReplaceParam = new ArrayList<>(1);
                needReplaceParam.add(LiveParamEnum.VIEW_URL);
                redirectUrl = LiveParamEnum.replaceContent(redirectUrl, needReplaceParam, liveEntity);
            } else {
                redirectUrl = host;
            }
            weChatTemplateMessageVO.setRedirectUrl(redirectUrl);
        } else if (com.facishare.marketing.common.enums.RedirectTypeEnum.CONFERENCE_MAIN_PAGE.getType().equals(weChatTemplateMessageVO.getRedirectType())) {
            ActivityEntity conference = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
            weChatTemplateMessageVO.setRedirectUrl(host + "/proj/page/marketing-page?id=" + conference.getActivityDetailSiteId() + "&marketingEventId=" + marketingEventId + "&ea=" + ea
                + "&type=1&targetObjectType=13&targetObjectId=" + conference.getId() + "&marketingActivityId=" + marketingActivityId);
        } else if (com.facishare.marketing.common.enums.RedirectTypeEnum.LIVE_SIGN.getType().equals(weChatTemplateMessageVO.getRedirectType())) {
            MarketingLiveEntity live = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
            List<String> objectIds = contentMarketingEventMaterialRelationDAO.getApplyObjectIdsByMarketingEventId(ea, marketingEventId);
            String objectId = live.getFormHexagonId();
            if (!objectIds.isEmpty() && !objectIds.contains(objectId)) {
                objectId = objectIds.get(0);
            }
            weChatTemplateMessageVO.setRedirectUrl(host + "/proj/page/marketing-page?id=" + objectId + "&marketingEventId=" + marketingEventId + "&type=1&marketingActivityId=" + marketingActivityId + "&ea=" + ea);
        }

        /** 发送模版消息 */
        Integer msgId = null;
        try {
            NoticeSendVo noticeSendVo = this.convert(ea, fsUserId, marketingEventId, weChatTemplateMessageVO.getAppId(), weChatTemplateMessageVO);
            if (weChatTemplateMessageVO.getMigration() && weChatTemplateMessageVO.getRedirectType().equals(RedirectTypeEnum.OLD_ARTICLE.getType())) {
                msgId = weChatTemplateMessageVO.getMsgId();
            } else if (!weChatTemplateMessageVO.getMigration()) {
                if (!needAudit) {
                    com.facishare.wechat.union.common.result.Result<NoticeSendResult> noticeSendResult = noticeService.sendNotice(noticeSendVo);
                    log.info(" noticeService.sendNotice arg:{} noticeSendResult:{}", noticeSendVo, noticeSendResult);
                    if (!noticeSendResult.isSuccess()) {
                        throw new OuterServiceRuntimeException(SHErrorCode.SYSTEM_ERROR.getErrorCode(), noticeSendResult.getErrorCode() + " " + noticeSendResult.getErrorMessage());
                    }
                    msgId = noticeSendResult.getData().getId();
                }
            }
        } catch (Exception e) {
            log.warn("doSendTemplateMessageService exception :", e);
            status = SendStatusEnum.FAIL.getStatus();
        }
        /** 从微联服务号获取消息发送状态 */
        if (msgId != null) {
            GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByIdAndEiAndAppId(msgId, ei, weChatTemplateMessageVO.getAppId());
            if (getNoticeResult != null) {
                status = wechatTemplateMessageSendStatusEnumMap.get(getNoticeResult.getStatus());
            }
        } else {
            if (!needAudit) {
                status = wechatTemplateMessageSendStatusEnumMap.get(com.facishare.wechat.sender.api.enums.SendStatusEnum.FAILED.getStatus());
            }
        }
        /** 更新营销活动状态 */
        ActionEditArg actionEditArg = new ActionEditArg();
        MarketingActivityData updateMarketingActivityData = new MarketingActivityData();
        updateMarketingActivityData.put(ObjectDescribeContants.ID,marketingActivityId);
        updateMarketingActivityData.put(MarketingActivityFieldContants.STATUS,status);
        actionEditArg.setObjectData(updateMarketingActivityData);
        Result<ActionEditResult> actionEditResult = metadataActionService.edit(new HeaderObj(ei, -10000), MarketingActivityFieldContants.API_NAME, true, true, actionEditArg);
        if (!actionEditResult.isSuccess()) {
            /** 警告但继续执行，把额外配置存进去 */
            log.warn("WechatTemplateMessageManager.doAddAction metadataActionService.edit fail , arg : {} , actionEditResult : {} ", actionEditArg, actionEditResult);
        }
        /** 保存额外配置*/
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = new MarketingActivityExternalConfigEntity();
        marketingActivityExternalConfigEntity.setAssociateId(msgId == null ? null : String.valueOf(msgId));
        marketingActivityExternalConfigEntity.setAssociateIdType(AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType());
        ExternalConfig externalConfig = new ExternalConfig(ConvertUtil.convert(weChatTemplateMessageVO));
        marketingActivityExternalConfigEntity.setExternalConfig(externalConfig);
        marketingActivityExternalConfigEntity.setId(UUIDUtil.getUUID());
        marketingActivityExternalConfigEntity.setEa(ea);
        marketingActivityExternalConfigEntity.setMarketingActivityType(MarketingActivityTypeEnum.ENTERPRISE.getType());
        marketingActivityExternalConfigEntity.setMarketingEventId(marketingEventId);
        marketingActivityExternalConfigEntity.setMarketingActivityId(marketingActivityId);
        if (needAudit) {
            marketingActivityExternalConfigEntity.setIsNeedAudit("true");
        }
        marketingActivityExternalConfigDao.insert(marketingActivityExternalConfigEntity);
        if (weChatTemplateMessageVO.getMigration()) {
            GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByIdAndEiAndAppId(msgId, ei, weChatTemplateMessageVO.getAppId());
            if (getNoticeResult.getCreateTime() != null) {
                marketingActivityExternalConfigDao.updateCreateTime(marketingActivityExternalConfigEntity.getId(), new Date(getNoticeResult.getCreateTime()));
            }
        }
        /** 返回结果 */
        AddMarketingActivityResult result = new AddMarketingActivityResult();
        result.setMarketingActivityId(marketingActivityExternalConfigEntity.getMarketingActivityId());
        result.setAssociateId(marketingActivityExternalConfigEntity.getAssociateId());
        return result;
    }

    private com.facishare.marketing.common.result.Result<String> addMarketingActivityMetadata(String ea, Integer fsUserId, String marketingEventId, Integer spreadType, String name, Integer status, String appId, ObjectData objectData) {
        PaasAddMarketingActivityArg arg = new PaasAddMarketingActivityArg();
        arg.setMarketingEventId(marketingEventId);
        arg.setName(name);
        arg.setSpreadType(String.valueOf(spreadType));
        //服务号营销
        arg.setStatus("" + status);
        arg.setWxAppId(appId);
        arg.setObjectData(objectData);
        return marketingActivityCrmManager.addMarketingActivity(ea, fsUserId, arg);
    }

    private AddMarketingActivityResult doAddWeChatServiceMarketingActivityResult(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        Long tenantId = (long) eieaConverter.enterpriseAccountToId(ea);
        addMarketingActivityArg.getWeChatServiceMarketingActivityVO().setUserId(fsUserId);
        addMarketingActivityArg.getWeChatServiceMarketingActivityVO().setEnterpriseAccount(ea);
        addMarketingActivityArg.getWeChatServiceMarketingActivityVO().setCorpId(tenantId);
        PaasAddMarketingActivityArg arg = new PaasAddMarketingActivityArg();
        arg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        arg.setName(addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getTitle());
        arg.setSpreadType(String.valueOf(addMarketingActivityArg.getSpreadType()));
        String wxAppId = addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getAppId();
        Optional<String> wxAppIdRes = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(wxAppId);
        if (wxAppIdRes.isPresent()) {
            wxAppId = wxAppIdRes.get();
        }
        arg.setWxAppId(wxAppId);
        //服务号营销
        boolean needAudit = marketingActivityAuditManager.isNeedAudit(ea);
        if (needAudit) {
            ObjectData objectData = marketingActivityAuditManager.buildObjectData(ea, addMarketingActivityArg.getMarketingActivityAuditData());
            arg.setObjectData(objectData);
            arg.setStatus("" + wechatTemplateMessageSendStatusEnumMap.get(com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus()));
        } else {
            arg.setStatus("" + com.facishare.wechat.sender.api.enums.SendStatusEnum.SENDING.getStatus());
        }
        com.facishare.marketing.common.result.Result<String> marketingActivityIdResult = marketingActivityCrmManager.addMarketingActivity(ea, fsUserId, arg);
        if (!marketingActivityIdResult.isSuccess()){
            AddMarketingActivityResult crmResult = new AddMarketingActivityResult();
            crmResult.setCrmErrorCode(marketingActivityIdResult.getErrCode());
            crmResult.setCrmErrorMsg(marketingActivityIdResult.getErrMsg());
            return crmResult;
        }
        String marketingActivityId = marketingActivityIdResult.getData();
        String messageId = null;
        try {
            if (addMarketingActivityArg.getWeChatServiceMarketingActivityVO() != null
                    && addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMsgType() == WeChatMsgTypeEnum.PIC_MSG.getType()
                    && addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getContent() != null
                    && (addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getContent().startsWith("A_") || addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getContent().startsWith("C_"))){
                byte[] bytes = fileV2Manager.downloadAFile(addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getContent(), ea);
                String tapath = fileV2Manager.doUploadTempNFile(bytes, ea, fsUserId);
                if (tapath != null){
                    addMarketingActivityArg.getWeChatServiceMarketingActivityVO().setContent(tapath);
                }
                log.info("doAddWeChatServiceMarketingActivityResult content path:{}", tapath);
            }
            SendArg sendArg = BeanUtil.copyByGson(addMarketingActivityArg, SendArg.class);
            sendArg.getWeChatServiceMarketingActivityVO().setEnterpriseAccount(ea);
            sendArg.getWeChatServiceMarketingActivityVO().setUserId(fsUserId);
            sendArg.getWeChatServiceMarketingActivityVO().setCorpId(tenantId);
            Optional<String> optionalWxAppId = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(sendArg.getWeChatServiceMarketingActivityVO().getAppId());
            if (!optionalWxAppId.isPresent()) {
                return null;
            }
            String replacedWxAppIdContent = ReplaceUtil.replaceWxAppId(sendArg.getWeChatServiceMarketingActivityVO().getContent(), optionalWxAppId.get());
            replacedWxAppIdContent = ReplaceUtil.replaceMiniAppId(replacedWxAppIdContent, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
            sendArg.getWeChatServiceMarketingActivityVO().setContent(replacedWxAppIdContent);
            if (sendArg.getWeChatServiceMarketingActivityVO().getMsgType() == WxMassMessageType.TEXT.getType()) {
                String replacedMarketingActivityIdContent = ReplaceUtil.replaceMarketingActivityId(sendArg.getWeChatServiceMarketingActivityVO().getContent(), marketingActivityId);
                sendArg.getWeChatServiceMarketingActivityVO().setContent(replacedMarketingActivityIdContent);
            }
            if (addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMigration() && addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMsgId() != null
                && addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMsgId() != 0) {
                messageId = String.valueOf(addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMsgId());
            } else if (!addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMigration()) {
                if (!needAudit) {
                    messageId = weChatServiceNoticeManager.sendMessage(ea, fsUserId, sendArg.getWeChatServiceMarketingActivityVO());
                }
            }
//            GetNoticeResult getNoticeResult = weChatServiceNoticeManager
//                .findByIdAndEiAndAppId(Integer.valueOf(messageId), tenantId.intValue(), addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getAppId());
//            arg.setStatus(String.valueOf(weChatServiceSendStatusEnumMap.get(getNoticeResult.getStatus())));
        } catch (Exception ex) {
            log.error("doSendWeChatService exception :", ex);
            arg.setStatus(String.valueOf(SendStatusEnum.FAIL.getStatus()));
        }

        if (messageId != null) {
            GetNoticeResult getNoticeResult = weChatServiceNoticeManager
                    .findByIdAndEiAndAppId(Integer.valueOf(messageId), tenantId.intValue(), addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getAppId());
            arg.setStatus(String.valueOf(weChatServiceSendStatusEnumMap.get(getNoticeResult.getStatus())));
        } else {
            if (!needAudit) {
                arg.setStatus("" + com.facishare.wechat.sender.api.enums.SendStatusEnum.FAILED.getStatus());
            }
        }
        PaasUpdateMarketingActivityArg updateArg = BeanUtil.copyByGson(arg, PaasUpdateMarketingActivityArg.class);
        updateArg.setId(marketingActivityId);
        marketingActivityCrmManager.updateMarketingActivity(ea, fsUserId, updateArg);
        MarketingActivityExternalConfigVO marketingActivityExternalConfigVO = new MarketingActivityExternalConfigVO();
        marketingActivityExternalConfigVO.setAssociateId(messageId);
        marketingActivityExternalConfigVO.setAssociateIdType(AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType());
        marketingActivityExternalConfigVO.setMarketingActivityId(marketingActivityId);
        WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO = BeanUtil.copyByGson(addMarketingActivityArg.getWeChatServiceMarketingActivityVO(), WeChatServiceMarketingActivityVO.class);
        ExternalConfigVO externalConfigVO = new ExternalConfigVO();
        externalConfigVO.setWeChatServiceMarketingActivityVO(weChatServiceMarketingActivityVO);
        marketingActivityExternalConfigVO.setExternalConfig(externalConfigVO);
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = BeanUtil.copyByGson(marketingActivityExternalConfigVO, MarketingActivityExternalConfigEntity.class);
        if (marketingActivityExternalConfigEntity.getExternalConfig() != null && marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO() != null) {
            marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().setMigration(addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMigration());
        }
        if (marketingActivityExternalConfigEntity.getExternalConfig() != null && marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO() != null
            && marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().getMsgType() == WeChatMsgTypeEnum.PIC_MSG.getType()
            && marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().getContent() != null) {
            String nPath = fileV2Manager.changeTempFileToPermanent(ea, fsUserId, ".jpg", marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().getContent());
            marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().setContent(nPath);
        }
        marketingActivityExternalConfigEntity.setId(UUIDUtil.getUUID());
        marketingActivityExternalConfigEntity.setEa(ea);
        marketingActivityExternalConfigEntity.setMarketingActivityType(MarketingActivityTypeEnum.ENTERPRISE.getType());
        marketingActivityExternalConfigEntity.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        if (needAudit) {
            marketingActivityExternalConfigEntity.setIsNeedAudit("true");
        }
        marketingActivityExternalConfigDao.insert(marketingActivityExternalConfigEntity);
        if (addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getMigration()) {
            if (messageId != null) {
                GetNoticeResult getNoticeResult = weChatServiceNoticeManager
                        .findByIdAndEiAndAppId(Integer.valueOf(messageId), tenantId.intValue(), addMarketingActivityArg.getWeChatServiceMarketingActivityVO().getAppId());
                if (getNoticeResult.getCreateTime() != null) {
                    marketingActivityExternalConfigDao.updateCreateTime(marketingActivityExternalConfigEntity.getId(), new Date(getNoticeResult.getCreateTime()));
                }
            }
        }
        log.info("doWeChatServiceManager ,marketingActivityExternalConfigEntity ,{}, messageId ,{},marketingActivityId ,{} ", marketingActivityExternalConfigEntity, messageId, marketingActivityId);
        AddMarketingActivityResult result = new AddMarketingActivityResult();
        result.setMarketingActivityId(marketingActivityExternalConfigVO.getMarketingActivityId());
        result.setAssociateId(marketingActivityExternalConfigVO.getAssociateId());
        return result;
    }

    /**
     * 获取有效期内的WxAutoReplyRuleResult
     * @param ea 非空
     * @param wxAppId 非空
     * @param actionType 可为空
     * @return
     */
    public Optional<List<WxAutoReplyRuleResult>> listValidWxAutoReplyRuleResult(String ea, String wxAppId, Integer actionType) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(wxAppId)) {
            log.warn("WeChatServiceManager.listValidWxAutoReplyRuleResult ea or wxAppId is Empty ea:{} wxAppId:{}", ea, wxAppId);
            return Optional.empty();
        }
        List<WxAutoReplyRuleEntity> wxAutoReplyRuleEntities = wxAutoReplyRuleDao.getWxAutoReplyRuleEntitiesByEaAndWxAppIdAndActionTypeAndStatus(ea, wxAppId, actionType, null, null, null);
        List<WxAutoReplyRuleEntity> todoValidStatusEntities = new LinkedList<>();
        List<WxAutoReplyRuleEntity> todoInvalidStatusEntities = new LinkedList<>();
        List<WxAutoReplyRuleResult> result = wxAutoReplyRuleEntities.stream().map(entity -> {
            Integer oldStatus = entity.getStatus();
            Integer newStatus = entity.refreshStatus();
            if (!newStatus.equals(oldStatus) && WxAutoReplyRuleStatusEnum.VALID.getStatus().equals(newStatus)) {
                todoValidStatusEntities.add(entity);
            } else if (!newStatus.equals(oldStatus) && WxAutoReplyRuleStatusEnum.INVALID.getStatus().equals(newStatus)) {
                todoInvalidStatusEntities.add(entity);
            }
            WxAutoReplyRuleResult wxAutoReplyRuleResult = new WxAutoReplyRuleResult();
            BeanUtils.copyProperties(entity, wxAutoReplyRuleResult, "validFrom", "validTo", "createTime");
            wxAutoReplyRuleResult.setValidFrom(entity.getValidFrom() == null ? null : entity.getValidFrom().getTime());
            wxAutoReplyRuleResult.setValidTo(entity.getValidTo() == null ? null : entity.getValidTo().getTime());
            wxAutoReplyRuleResult.setCreateTime(entity.getCreateTime().getTime());
            return wxAutoReplyRuleResult;
        }).filter(WxAutoReplyRuleResult::isValid).collect(Collectors.toList());
        // 惰性更新数据库过期状态
        updateInvalidEntity(ea, todoInvalidStatusEntities.stream().map(WxAutoReplyRuleEntity::getId).collect(Collectors.toList()));
        updateValidEntity(ea, todoValidStatusEntities.stream().map(WxAutoReplyRuleEntity::getId).collect(Collectors.toList()));
        return Optional.of(result);
    }

    /**
     * 返回所有的结果
     * @param ea 非空
     * @param wxAppId 非空
     * @param actionType 可为空
     * @return
     */
    public Optional<List<WxAutoReplyRuleResult>> listAllWxAutoReplyRuleResult(String ea, String wxAppId, Integer actionType, String ruleName, Page page) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(wxAppId)) {
            log.warn("WeChatServiceManager.listAllWxAutoReplyRuleResult ea or wxAppId is Empty ea:{} wxAppId:{}", ea, wxAppId);
            return Optional.empty();
        }
        List<WxAutoReplyRuleEntity> wxAutoReplyRuleEntities = wxAutoReplyRuleDao.getWxAutoReplyRuleEntitiesByEaAndWxAppIdAndActionTypeAndStatus(ea, wxAppId, actionType, null, ruleName, page);
        List<WxAutoReplyRuleEntity> todoValidStatusEntities = new LinkedList<>();
        List<WxAutoReplyRuleEntity> todoInvalidStatusEntities = new LinkedList<>();
        List<WxAutoReplyRuleResult> result = wxAutoReplyRuleEntities.stream().map(entity -> {
            Integer oldStatus = entity.getStatus();
            Integer newStatus = entity.refreshStatus();
            if (!newStatus.equals(oldStatus) && WxAutoReplyRuleStatusEnum.VALID.getStatus().equals(newStatus)) {
                todoValidStatusEntities.add(entity);
            } else if (!newStatus.equals(oldStatus) && WxAutoReplyRuleStatusEnum.INVALID.getStatus().equals(newStatus)) {
                todoInvalidStatusEntities.add(entity);
            }
            WxAutoReplyRuleResult wxAutoReplyRuleResult = new WxAutoReplyRuleResult();
            BeanUtils.copyProperties(entity, wxAutoReplyRuleResult, "validFrom", "validTo", "createTime");
            wxAutoReplyRuleResult.setValidFrom(entity.getValidFrom() == null ? null : entity.getValidFrom().getTime());
            wxAutoReplyRuleResult.setValidTo(entity.getValidTo() == null ? null : entity.getValidTo().getTime());
            wxAutoReplyRuleResult.setCreateTime(entity.getCreateTime().getTime());
            return wxAutoReplyRuleResult;
        }).collect(Collectors.toList());
        // 惰性更新数据库过期状态
        updateInvalidEntity(ea, todoInvalidStatusEntities.stream().map(WxAutoReplyRuleEntity::getId).collect(Collectors.toList()));
        updateValidEntity(ea, todoValidStatusEntities.stream().map(WxAutoReplyRuleEntity::getId).collect(Collectors.toList()));
        return Optional.of(result);
    }

    public Optional<WxAutoReplyRuleResult> getWxAutoReplyRuleDetail(String id, String ea) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(ea)) {
            return Optional.empty();
        }
        WxAutoReplyRuleEntity wxAutoReplyRuleEntity = wxAutoReplyRuleDao.getWxAutoReplyRuleEntityByIdAndEa(id, ea);
        if (wxAutoReplyRuleEntity == null) {
            return Optional.empty();
        }
        WxAutoReplyRuleResult wxAutoReplyRuleResult = new WxAutoReplyRuleResult();
        long now = System.currentTimeMillis();
        BeanUtils.copyProperties(wxAutoReplyRuleEntity, wxAutoReplyRuleResult, "validFrom", "validTo");
        wxAutoReplyRuleResult.setValidFrom(wxAutoReplyRuleEntity.getValidFrom() == null ? null : wxAutoReplyRuleEntity.getValidFrom().getTime());
        wxAutoReplyRuleResult.setValidTo(wxAutoReplyRuleEntity.getValidTo() == null ? null : wxAutoReplyRuleEntity.getValidTo().getTime());
        return Optional.of(wxAutoReplyRuleResult);
    }

    public int updateInvalidEntity(String ea, List<String> ids) {
        if (StringUtils.isEmpty(ea) || CollectionUtils.isEmpty(ids)) {
            log.warn("WeChatServiceManager.updateInvalidEntity ea or ids is empty ea:{} ids:{}", ea, ids);
            return -1;
        }
        return wxAutoReplyRuleDao.updateStatusByIdsAndEa(ids, ea, WxAutoReplyRuleStatusEnum.INVALID.getStatus());
    }

    public int updateValidEntity(String ea, List<String> ids) {
        if (StringUtils.isEmpty(ea) || CollectionUtils.isEmpty(ids)) {
            log.warn("WeChatServiceManager.updateValidEntity ea or ids is empty ea:{} ids:{}", ea, ids);
            return -1;
        }
        return wxAutoReplyRuleDao.updateStatusByIdsAndEa(ids, ea, WxAutoReplyRuleStatusEnum.VALID.getStatus());
    }

    public GetMarketingActivityResult convert(String ea, Integer fsUserId, GetMarketingActivityDetailData getMarketingActivityDetailData, GetNoticeResult getNoticeResult,
        com.facishare.marketing.provider.entity.data.WeChatTemplateMessageData weChatTemplateMessageData) {
        GetMarketingActivityResult result = new GetMarketingActivityResult();
        result.setWechatMessageType(AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType());
        if (getMarketingActivityDetailData != null) {
            result.setId(getMarketingActivityDetailData.getId());
            result.setMarketingEventId(getMarketingActivityDetailData.getMarketingEventId());
            result.setName(getMarketingActivityDetailData.getName());
            result.setSpreadType(getMarketingActivityDetailData.getSpreadType());
            result.setStatus(getMarketingActivityDetailData.getStatus());
        }
        com.facishare.marketing.api.vo.WeChatTemplateMessageData data = ConvertUtil.convert(weChatTemplateMessageData);
        if (getNoticeResult != null) {
            data.setAppId(getNoticeResult.getAppId());
            data.setTitle(getNoticeResult.getTitle());
            data.setAuthor(getNoticeResult.getAuthor());
            data.setContent(getNoticeResult.getContent());
            data.setSendTime(getNoticeResult.getFixedTime());
            data.setCustomerCount(getNoticeResult.getCustomerCount());
            data.setActualCompletedCount(getNoticeResult.getActualCompletedCount());
            data.setNeedSendCount(getNoticeResult.getNeedSendCount());
            data.setFailSendCount(getNoticeResult.getFailSendCount());
            data.setReadCount(getNoticeResult.getReadCount());
            data.setMsgId(getNoticeResult.getMsgId());
            /** 如果com.facishare.marketing.provider.entity.ExternalConfig externalConfig中的消息模板内容非空，则使用config的消息模板。否则使用平台组拉取的模板消息（兼容旧数据） */
            if (weChatTemplateMessageData != null && weChatTemplateMessageData.getTemplateMessageDatas() != null) {
                data.setTemplateMessageDatas(weChatTemplateMessageData.getTemplateMessageDatas());
            } else {
                data.setTemplateMessageDatas(getNoticeResult.getTemplateMessageDatas());
            }
        }
        /**
         * 模版Id
         */
        if (weChatTemplateMessageData != null) {
            data.setWeChatOfficialTemplateId(weChatTemplateMessageData.getWeChatOfficialTemplateId());
            String appId = weChatTemplateMessageData.getAppId();
            OuterServiceResult outerServiceInfo = outerServiceWechatManager.getOuterServiceInfo(ea, fsUserId, appId);
            if (outerServiceInfo != null) {
                data.setAppName(outerServiceInfo.getAppName());
                data.setAppLogoUrl(outerServiceInfo.getAppLogoFxiaokeUrl());
            }
        }
        result.setWeChatTemplateMessageVO(data);
        return result;
    }

    public WeChatMenuFormData getWxMenuDataById(String ea, String appId, String menuId) {
        FsUserVO fsUserVO = new FsUserVO(ea, -10000, appId);
        ModelResult<List<WeChatMenuFormData>> result = weChatCustomerMenuRestService.queryCustomerMenu(fsUserVO, appId);
        if (!result.isSuccess() || result.getResult() == null) {
            return null;
        }
        List<WeChatMenuFormData> list = result.getResult();
        return findWxMenuDataInList(list, menuId);
    }

    private WeChatMenuFormData findWxMenuDataInList(List<WeChatMenuFormData> list, String menuId) {
        for (WeChatMenuFormData data : list) {
            if (data.getId().equals(menuId)) {
                return data;
            }
            List<WeChatMenuFormData> children = data.getChildren();
            if (children != null && !children.isEmpty()) {
                WeChatMenuFormData childrenResult = findWxMenuDataInList(children, menuId);
                if (childrenResult != null) {
                    return childrenResult;
                }
            }
        }
        return null;
    }

    private NoticeSendVo convert(String ea, Integer fsUserId, String marketingEventId, String appId, WeChatTemplateMessageData source) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        NoticeSendVo result = new NoticeSendVo();
        // 微信模板消息变量
        Map<String, String> paramValueMap = new HashMap<>();
        paramValueMap.putAll(getWechatParamValueMap());
        String marketingEventType = null;
        if (StringUtils.isNotBlank(marketingEventId)) {
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, marketingEventId);
            if (marketingEventData != null) {
                fixMarketingEventDate(ea, marketingEventData);  //直播&会议的开始时间和结束时间从数据库中获取
                Map<String, String> marketingEventDataParamValueMap = marketingEventData.getParamValueMap();
                paramValueMap.putAll(marketingEventDataParamValueMap);
                marketingEventType = marketingEventData.getEventType();
                addMarketingScenesParam(paramValueMap, ea, marketingEventId, marketingEventType);
            }
        }
        // 平台数据库标题最大仅支持50
        if (source.getTitle() != null && source.getTitle().length() >= MAX_TITLE_LENGTH) {
            result.setTitle(source.getTitle().substring(0, MAX_TITLE_LENGTH));
        } else if (source.getTitle() != null) {
            result.setTitle(source.getTitle());
        } else {
            result.setTitle(source.getTemplateMessageDatas().getTitle());
        }
        result.setContent(source.getContent());
        result.setSummary(source.getSummary());
        result.setCustomerIds(source.getCustomerIds());
        result.setType(source.getType());
        result.setFixedTime(source.getFixedTime());
        result.setWeChatOfficialTemplateId(source.getWeChatOfficialTemplateId());
        result.setAuthor(source.getAuthor());
        result.setFileAttachmentList(this.convert(source.getFileAttachmentList()));
        result.setSendRange(source.getSendRange());
        List<Map<String, Object>> filters = source.getFilters();
//        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
//        if (isOpen) {
//            List<Integer> dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
//            Map<String, Object> filter = Maps.newHashMap();
//            filter.put("fieldName", "data_own_organization");
//            filter.put("fieldType", 8.0);
//            filter.put("fieldValues", dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//            filter.put("operator", "IN");
//            filters.add(filter);
//            if (result.getSendRange() == 0) {
//                result.setSendRange(1);
//            }
//        }
        result.setFilters(VariableNameUtil.convertFilterCamelToHyphen(filters)); // 驼峰转下划线
        result.setTagSearchQueryData(createTagSearchQueryData(ea, fsUserId, source.getAppId(), source.getTagIdList()));
        result.setAppId(appId);
        result.setUserId(fsUserId);
        result.setCorpId(Long.valueOf(ei));
        result.setEnterpriseAccount(ea);
        // 个性化发送
        if (CollectionUtils.isNotEmpty(source.getCampaignIds())) {
            result.setTemplateMessageWxOpenIdDataList(createTemplateMessageWxOpenIdDataList(ea, marketingEventId, marketingEventType, source.getAppId(), source.getCampaignIds(), source.getType()));
        }
        if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.OLD_ARTICLE.getType())) {
            result.setRedirectType(RedirectTypeEnum.OLD_ARTICLE.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.MATERIAL.getType())) {
            result.setRedirectType(RedirectTypeEnum.WEB_URL.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.MINI_APP.getType())) {
            result.setRedirectType(RedirectTypeEnum.MINI_APP.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.WEB_URL.getType())) {
            result.setRedirectType(RedirectTypeEnum.WEB_URL.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.CONFERENCE_TICKET.getType())) {
            result.setRedirectType(RedirectTypeEnum.WEB_URL.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.LIVE_VIEW.getType())) {
            result.setRedirectType(RedirectTypeEnum.WEB_URL.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.CONFERENCE_MAIN_PAGE.getType())) {
            result.setRedirectType(RedirectTypeEnum.WEB_URL.getType());
        } else if (source.getRedirectType().equals(com.facishare.marketing.common.enums.RedirectTypeEnum.LIVE_SIGN.getType())) {
            result.setRedirectType(RedirectTypeEnum.WEB_URL.getType());
        }
        result.setMiniAppId(source.getMiniAppId());
        result.setMiniAppPagePath(source.getMiniAppPagePath());
        result.setRedirectUrl(source.getRedirectUrl());
        result.setTemplateMessageDatas(ConvertUtil.convert(paramValueMap, source.getTemplateMessageDatas()));
        result.setMarketingUserGroupIds(source.getMarketingUserGroupIds());
        return result;
    }

    private void fixMarketingEventDate(String ea, MarketingEventData marketingEventData){
        if (marketingEventData.getEventType().equals(MarketingEventEnum.LIVE_MARKETING.getEventType())) {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventData.getId());
            if (marketingLiveEntity != null) {
                marketingEventData.setBeginTime(marketingLiveEntity.getStartTime().getTime());
                marketingEventData.setEndTime(marketingLiveEntity.getEndTime().getTime());
            }
        }
        if (marketingEventData.getEventType().equals(MarketingEventEnum.MEETING_SALES.getEventType())){
            ActivityEntity conferenceEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventData.getId(), ea);
            if (conferenceEntity != null) {
                marketingEventData.setBeginTime(conferenceEntity.getStartTime().getTime());
                marketingEventData.setEndTime(conferenceEntity.getEndTime().getTime());
            }
        }
    }

    private void addMarketingScenesParam(Map<String, String> paramValueMap, String ea, String marketingEventId, String marketingEventType) {
        if (marketingEventType.equals(MarketingEventEnum.LIVE_MARKETING.getEventType())) {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
            if (marketingLiveEntity != null) {
                paramValueMap.put("{直播链接}", marketingLiveEntity.getViewUrl());
            }
        }
    }
//
//    /**
//     * 将营销通的com.facishare.marketing.api.vo.WeChatTemplateMessageData#sendRange转化为平台的com.facishare.wechat.union.common.enums.SendRangeEnum
//     * @param marketingSendRange
//     * @return
//     */
//    public static Integer marketingSendRangeToUnionSendRange(Integer marketingSendRange) {
//        if (marketingSendRange == null) {
//            return null;
//        }
//        Integer unionSendRange = marketingSendRange;
//        if (WeChatTemplateMessageDataSendRangeEnum.MARKETING_USER_GROUP.getCode().equals(marketingSendRange)) {
//            unionSendRange = com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_MARKETING_USER_GROUP.getCode();
//        } else if (WeChatTemplateMessageDataSendRangeEnum.CRM_CUSTOMER.getCode().equals(marketingSendRange)) {
//            unionSendRange = com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_CRM.getCode();
//        } else if (WeChatTemplateMessageDataSendRangeEnum.CAMPAIGN.getCode().equals(marketingSendRange)) {
//            unionSendRange = com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_WECHAT_FANOBJ_OPEN_IDS.getCode();
//        }
//        return unionSendRange;
//    }
//
//    /**
//     * 将平台的com.facishare.wechat.union.common.enums.SendRangeEnum转化为营销通的com.facishare.marketing.api.vo.WeChatTemplateMessageData#sendRange
//     * @param unionSendRange
//     * @return
//     */
//    public static Integer unionSendRangeToMarketingSendRange(Integer unionSendRange) {
//        if (unionSendRange == null) {
//            return null;
//        }
//        Integer marketingSendRange = unionSendRange;
//        if (com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_CRM.getCode() == unionSendRange) {
//            marketingSendRange = WeChatTemplateMessageDataSendRangeEnum.CRM_CUSTOMER.getCode();
//        } else if (com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_MARKETING_USER_GROUP.getCode() == unionSendRange) {
//            marketingSendRange = WeChatTemplateMessageDataSendRangeEnum.MARKETING_USER_GROUP.getCode();
//        } else if (com.facishare.wechat.union.common.enums.SendRangeEnum.SEND_BY_WECHAT_FANOBJ_OPEN_IDS.getCode() == unionSendRange) {
//            marketingSendRange = WeChatTemplateMessageDataSendRangeEnum.CAMPAIGN.getCode();
//        }
//        return marketingSendRange;
//    }

    private List<TemplateMessageWxOpenIdData> createTemplateMessageWxOpenIdDataList(String ea, String marketingEventId, String marketingEventType, String appId, List<String> campaignIds, Integer redirectType) {
        log.info("WeChatServiceManager.createTemplateMessageWxOpenIdDataList ea:{} appId:{} marketingEventType:{} campaignIds：{} redirectType:{}", ea, appId, marketingEventType, campaignIds, redirectType);
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(appId) || CollectionUtils.isEmpty(campaignIds)) {
            return new ArrayList<>();
        }
        // 将appId换取wxAppId
        ModelResult<String> modelResult = outerServiceWechatService.transAppIdToWxAppId(appId);
        if (!modelResult.isSuccess()) {
            log.warn("WeChatServiceManager.createTemplateMessageWxOpenIdDataList transAppIdToWxAppId error modelResult:{}", modelResult);
            return new ArrayList<>();
        }
        String wxAppId = modelResult.getResult();
        Map<String, String> campaignId2OpenIdMap = new HashMap<>();
        Optional<Map<String, UserMarketingWxServiceAccountRelationEntity>> userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional = userMarketingAccountManager.getUserMarketingWxServiceAccountRelationEntitiesByCampaignIds(ea, wxAppId, campaignIds);
        if (userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional.isPresent() && MapUtils.isNotEmpty(userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional.get())) {
            Map<String, UserMarketingWxServiceAccountRelationEntity> campaignId2UserMarketingWxServiceAccountRelationEntity = userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional.get();
            for (Map.Entry<String, UserMarketingWxServiceAccountRelationEntity> entry : campaignId2UserMarketingWxServiceAccountRelationEntity.entrySet()) {
                campaignId2OpenIdMap.putIfAbsent(entry.getKey(), entry.getValue() == null ? null : entry.getValue().getWxOpenId());
            }
        }
        Map<String, Map<String, String>> campaignId2ParamMap = Maps.newHashMap();
        if (MarketingEventEnum.MEETING_SALES.getEventType().equals(marketingEventType)) {
            List<String> validCampaignIds = campaignId2OpenIdMap.entrySet().stream().filter(entry -> StringUtils.isNotEmpty(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
            Optional<Map<String, Map<String, String>>> campaignId2ParamMapOptional = conferenceManager.getEnrollParamToWechatServiceTemplate(ea, validCampaignIds, marketingEventId);
            if (campaignId2ParamMapOptional.isPresent() && MapUtils.isNotEmpty(campaignId2ParamMapOptional.get())) {
                campaignId2ParamMap = campaignId2ParamMapOptional.get();
            }
        } else if (MarketingEventEnum.LIVE_MARKETING.getEventType().equals(marketingEventType)) {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
            if (marketingLiveEntity != null) {
                List<String> validCampaignIds = campaignId2OpenIdMap.entrySet().stream().filter(entry -> StringUtils.isNotEmpty(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
                for (String campaignId : validCampaignIds) {
                    campaignId2ParamMap.put(campaignId, LiveParamEnum.conversionBaseParamByObject(marketingLiveEntity, true));
                }
            }
        }

        List<TemplateMessageWxOpenIdData> result = new LinkedList<>();
        for (Map.Entry<String, String> campaignId2OpenId : campaignId2OpenIdMap.entrySet()) {
            String campaignId = campaignId2OpenId.getKey();
            String wxOpenId = campaignId2OpenId.getValue();
            if (StringUtils.isEmpty(wxOpenId)) {
                continue;
            }
            // 过滤相同的微信用户
            if (result.stream().anyMatch(ele -> wxOpenId.equals(ele.getWxOpenId()))) {
                continue;
            }
            // 微信wxOpenId信息
            TemplateMessageWxOpenIdData templateMessageWxOpenIdData = new TemplateMessageWxOpenIdData();
            templateMessageWxOpenIdData.setWxOpenId(wxOpenId);
            result.add(templateMessageWxOpenIdData);
            // 占位变量信息
            // 微信用户对应的占位符替换信息
            List<TemplateMessageVariableData> templateMessageVariableDatas = new ArrayList<>();
            if (MapUtils.isNotEmpty(campaignId2ParamMap)) {
                Map<String, String> paramMap = campaignId2ParamMap.get(campaignId);
                if (MapUtils.isNotEmpty(paramMap)) {
                    for (Map.Entry<String, String> nameToValueMap : paramMap.entrySet()) {
                        TemplateMessageVariableData data = new TemplateMessageVariableData();
                        data.setPlaceHolder(nameToValueMap.getKey());
                        data.setReplaceValue(nameToValueMap.getValue());
                        templateMessageVariableDatas.add(data);
                    }
                }
            }
            CampaignMergeDataEntity campaignMergeDataById = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
            if (campaignMergeDataById != null) {
                Map<String, String> customFields = new HashMap<>();
                List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
                if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
                    crmCustomerFields.forEach(e -> {
                        if (2 == e.getFieldProperty()) {
                            customFields.put(e.getFieldCaption(), e.getFieldName());
                        }
                    });
                }
                if (!customFields.isEmpty()) {
                    String campaignMembersObjId = campaignMergeDataById.getCampaignMembersObjId();
                    Map<String, String> detail = null;
                    if (StringUtils.isNotBlank(campaignMembersObjId)) {
                        detail = crmV2Manager.getObjectDataEnTextVal(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMembersObjId);
                    }
                    for (Map.Entry<String, String> customField : customFields.entrySet()) {
                        if (detail == null) {
                            TemplateMessageVariableData data = new TemplateMessageVariableData();
                            data.setPlaceHolder("{" + customField.getKey() + "}");
                            data.setReplaceValue(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                            templateMessageVariableDatas.add(data);
                        } else {
                            Object value = detail.get(customFields.get(customField.getKey()));
                            TemplateMessageVariableData data = new TemplateMessageVariableData();
                            data.setPlaceHolder("{" + customField.getKey() + "}");
                            data.setReplaceValue(value == null ? I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996) : value.toString());
                            templateMessageVariableDatas.add(data);
                        }
                    }
                }
            }
            templateMessageWxOpenIdData.setTemplateMessageVariableDatas(templateMessageVariableDatas);
        }
        return result;
    }

    public Map<String, String> getConferenceParamDescMap() {
        Map<String, String> map = new HashMap<>();
        map.put("ticketCode", I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1079));
        return map;
    }

    public Map<String, String> getWechatParamDescMap() {
        Map<String, String> map = new HashMap<>();
        map.put("wechatName", I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_WECHATSERVICEMANAGER_1173));
        return map;
    }

    public Map<String, String> getWechatParamValueMap() {
        Map<String, String> map = new HashMap<>();
        map.put("{微信用户昵称}", "{WechatFanObj#name}");
        return map;
    }

    public TagSearchQueryData createTagSearchQueryData(String ea, Integer fsUserId, String appId, List<TagName> tagIdList) {
        log.info("WeChatServiceManager.createTagSearchQueryData ea:{} fsUserId:{} appId:{} tagIdList:{}", ea, fsUserId, appId, tagIdList);
        if (CollectionUtils.isEmpty(tagIdList)) {
            return null;
        }
        Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, WechatFanFieldContants.API_NAME, tagIdList);
        if (MapUtils.isEmpty(tagNamesIdMap)) {
            log.warn("WeChatServiceManager.createTagSearchQueryData tagNamesIdMap is empty");
            return null;
        }
        Collection<String> tagIdCollection = tagNamesIdMap.values();
        TagSearchQueryData tagSearchQueryData = new TagSearchQueryData(TagOperatorEnum.IN.getValue(), ConnectorEnum.OR.getValue());
        tagSearchQueryData.getTagParameter().put("subTagIdList", new ArrayList<>(tagIdCollection));
        List<com.facishare.wechat.union.common.data.FilterData> filters = Lists.newArrayList();
        com.facishare.wechat.union.common.data.FilterData filterData = new com.facishare.wechat.union.common.data.FilterData();
        filterData.setFieldName("app_id");
        filterData.setFieldValues(Lists.newArrayList(appId));
        filterData.setOperator(OperatorConstants.EQ);
        filters.add(filterData);
        tagSearchQueryData.setFilters(filters);
        return tagSearchQueryData;
    }

    private void initFilterData(String appId, List<TagName> tagIdList, FilterData filterData) {
        filterData.setObjectAPIName("WechatFanObj");
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<Filter> filters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFieldName("app_id");
        filter.setFieldValues(Lists.newArrayList(appId));
        filter.setOperator("EQ");
        filters.add(filter);
        query.setFilters(filters);
        query.setTagConnector("or");
        query.setTagNames(tagIdList);
        query.setTagOperator("IN");
        filterData.setQuery(query);
    }

    private com.facishare.wechat.sender.api.vo.NoticeSendVo.FileAttachment convert(FileAttachment source) {
        com.facishare.wechat.sender.api.vo.NoticeSendVo.FileAttachment result = new com.facishare.wechat.sender.api.vo.NoticeSendVo.FileAttachment();
        result.setName(source.getName());
        result.setExtensionName(source.getExtensionName());
        result.setSize(source.getSize());
        result.setAPath(source.getAPath());
        result.setPath(source.getPath());
        result.setOrder(source.getOrder());
        result.setSource(source.getSource());
        return result;
    }

    private List<NoticeSendVo.FileAttachment> convert(List<FileAttachment> sourceList) {
        if (sourceList == null) {
            return null;
        }
        return sourceList.stream().map(this::convert).collect(Collectors.toList());
    }

    public void wechatServiceNoticeSchedule() {
        try {
            Date endTime = new Date();
            String startTimeString = DateUtil.format(endTime.getTime() - 5 * 60 * 1000);
            Date startTime = DateUtil.parse(startTimeString);
            List<MarketingActivityExternalConfigEntity> taskList = marketingActivityExternalConfigDao.getWechatServiceNoticeTask(startTime, endTime);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(taskList)) {
                return;
            }
            taskList.stream().filter(task -> marketingActivityAuditManager.checkAudtStatus(task.getEa(), task.getMarketingActivityId()))
                    .forEach(task -> {
                        if (marketingActivityRemoteManager.enterpriseStop(task.getEa()) || appVersionManager.getCurrentAppVersion(task.getEa()) == null) {
                            log.info("WeChatServiceManager.wechatServiceNoticeSchedule failed enterprise stop or license expire ea:{}", task.getEa());
                            return;
                        }
                        if (MarketingJobUtil.isMarketingJobForbidExec(task.getEa())) {
                            log.warn("当前时间禁止发送营销消息, 公众号job: {}", task);
                            return;
                        }
                        ThreadPoolUtils.execute(() -> handlerSendTask(task), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                    });
        } catch (Exception e) {
            log.warn("WeChatServiceManager.wechatServiceNoticeSchedule exception:{}", e.fillInStackTrace());
        }
    }

    public void handlerSendTask(MarketingActivityExternalConfigEntity task) {
        String appId = null;
        String msgId = null;
        String ea = task.getEa();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        ObjectData marketingActivity = crmV2Manager.getObjectData(ea, -10000, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), task.getMarketingActivityId());
        if (marketingActivity.getInt(MarketingActivityFieldContants.STATUS) == null
                || marketingActivity.getInt(MarketingActivityFieldContants.STATUS) != SendStatusEnum.WAIT_SEND.getStatus()) {
            return;
        }
        ActionEditArg actionEditArg = new ActionEditArg();
        MarketingActivityData updateMarketingActivityData = new MarketingActivityData();
        updateMarketingActivityData.put(ObjectDescribeContants.ID, task.getMarketingActivityId());
        updateMarketingActivityData.put(MarketingActivityFieldContants.STATUS, SendStatusEnum.PROCESSING.getStatus());
        if (AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() == task.getAssociateIdType()) {
            WeChatServiceMarketingActivityData weChatServiceMarketingActivityData = task.getExternalConfig().getWeChatServiceMarketingActivityVO();
            appId = weChatServiceMarketingActivityData.getAppId();
            WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO = JSON.parseObject(JSON.toJSONString(weChatServiceMarketingActivityData), WeChatServiceMarketingActivityVO.class);
            if (weChatServiceMarketingActivityVO.getFixedTime() == null) {
                weChatServiceMarketingActivityVO.setType(1);
            }
            msgId = weChatServiceNoticeManager.sendMessage(ea, marketingActivity.getCreateBy(), weChatServiceMarketingActivityVO);
        } else if (AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType() == task.getAssociateIdType()) {
            com.facishare.marketing.provider.entity.data.WeChatTemplateMessageData weChatTemplateMessageVO = task.getExternalConfig().getWeChatTemplateMessageVO();
            appId = weChatTemplateMessageVO.getAppId();
            WeChatTemplateMessageData source = JSON.parseObject(JSON.toJSONString(weChatTemplateMessageVO), WeChatTemplateMessageData.class);
            NoticeSendVo vo = this.convert(ea, marketingActivity.getCreateBy(), task.getMarketingEventId(), appId, source);
            if (vo.getFixedTime() == null) {
                vo.setType(1);
            }
            com.facishare.wechat.union.common.result.Result<NoticeSendResult> noticeSendResultResult = noticeService.sendNotice(vo);
            log.info("handlerSendTask noticeSendResultResult params:{} result:{}", vo, noticeSendResultResult);
            if (noticeSendResultResult.isSuccess()) {
                msgId = "" + noticeSendResultResult.getData().getId();
            }
        }
        if (msgId != null && appId != null) {
            GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByIdAndEiAndAppId(Integer.valueOf(msgId), ei, appId);
            if (getNoticeResult != null) {
                if (SendStatusEnum.WAIT_SEND.getStatus() != wechatTemplateMessageSendStatusEnumMap.get(getNoticeResult.getStatus())) {
                    updateMarketingActivityData.put(MarketingActivityFieldContants.STATUS, wechatTemplateMessageSendStatusEnumMap.get(getNoticeResult.getStatus()));
                }
                if (getNoticeResult.getCreateTime() != null) {
                    marketingActivityExternalConfigDao.updateAssociateIdAndCreateTime(task.getId(), msgId, new Date(getNoticeResult.getCreateTime()));
                }
            }
        }
        actionEditArg.setObjectData(updateMarketingActivityData);
        metadataActionService.edit(new HeaderObj(ei, -10000), MarketingActivityFieldContants.API_NAME, true, true, actionEditArg);
    }

    @Override
    public com.facishare.marketing.common.result.Result<MarketingActivityPreviewData> getPreviewData(String ea, Integer fsUserId, MarketingActivityPreviewArg arg) {
        return com.facishare.marketing.common.result.Result.newSuccess();
    }
}