package com.facishare.marketing.provider.dao.conference;

import com.facishare.marketing.provider.dto.MarketingEventIdTypeDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceIdNameDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceStatisticsDTO;
import com.facishare.marketing.provider.dto.conference.EnrollNoticeTaskDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Created  By zhoux 2019/07/16
 **/
public interface ConferenceDAO {
    @Insert("INSERT INTO activity(id, ea, marketing_event_id, title, start_time, end_time, enroll_end_time, marketing_event_type, status, location, create_by, update_by, create_time, update_time, type, show_acitivity_list, map_address, map_location )\n"
        + "        VALUES (#{id}, #{ea}, #{marketingEventId}, #{title}, #{startTime}, #{endTime}, #{enrollEndTime}, #{marketingEventType}, #{status}, #{location}, #{createBy}, #{updateBy}, #{createTime}, #{updateTime}, #{type}, #{showAcitivityList}, #{mapAddress}, #{mapLocation})")
    boolean addConference(ActivityEntity entity);

    @Insert("<script>"
            + "INSERT INTO activity(id, ea, marketing_event_id, title, start_time, end_time, marketing_event_type, status, location, create_by, update_by, create_time, update_time, type, show_acitivity_list )\n"
            + " VALUES\n"
            + "<foreach collection=\"entities\" item=\"entity\" index=\"index\" separator=\",\"> "
            + " (#{entity.id}, #{entity.ea}, #{entity.marketingEventId}, #{entity.title}, #{entity.startTime}, #{entity.endTime}, #{entity.marketingEventType}, #{entity.status}, #{entity.location}, #{entity.createBy}, #{entity.updateBy}, #{entity.createTime}, #{entity.updateTime}, #{entity.type}, #{entity.showAcitivityList})"
            + "</foreach>"
            + " ON CONFLICT DO NOTHING"
            + "</script>")
    boolean batchInsert(@Param("entities")List<ActivityEntity> entities);

    @Update("<script>"
        +" UPDATE activity\n"
        + "       <set>\n"
        + "            <if test=\"title != null\">\n"
        + "                title = #{title},\n"
        + "            </if>\n"
        + "            <if test=\"startTime != null\">\n"
        + "                start_time = #{startTime},\n"
        + "            </if>\n"
        + "            <if test=\"endTime != null\">\n"
        + "                end_time = #{endTime},\n"
        + "            </if>\n"
        + "            <if test=\"location != null\">\n"
        + "                location = #{location},\n"
        + "            </if>\n"
        + "             \"scale\" = #{scale},\n"
        + "            <if test=\"conferenceDetails != null\">\n"
        + "                conference_details = #{conferenceDetails},\n"
        + "            </if>\n"
        + "            <if test=\"type != null\">\n"
        + "                \"type\" = #{type},\n"
        + "            </if>\n"
        + "            <if test=\"showAcitivityList != null\">\n"
        + "                \"show_acitivity_list\" = #{showAcitivityList},\n"
        + "            </if>\n"
        + "            <if test=\"mapAddress != null\">\n"
        + "                \"map_address\" = #{mapAddress},\n"
        + "            </if>\n"
        + "            <if test=\"mapLocation != null\">\n"
        + "                \"map_location\" = #{mapLocation},\n"
        + "            </if>\n"
        + "            update_time = now(),\n"
        + "            update_by = #{updateBy}\n"
        + "        </set>\n"
        + "  WHERE \"id\" = #{id}"
        + "</script>")
    boolean updateConference(ActivityEntity entity);

    @Update("<script>"
        +" UPDATE activity\n"
        + "       <set>\n"
        + "            \"status\" = #{status},\n"
        + "            update_time = now(),\n"
        + "            update_by = #{updateBy}\n"
        + "        </set>\n"
        + "  WHERE \"id\" = #{id}"
        + "</script>")
    boolean updateConferenceStatus(@Param("id") String id, @Param("status") Integer status, @Param("updateBy") Integer updateBy);

    @Update("<script>"
        +" UPDATE activity\n"
        + "       <set>\n"
        + "            <if test=\"enrollReview != null\">\n"
        + "                enroll_review = #{enrollReview},\n"
        + "            </if>\n"
        + "            <if test=\"enrollButton != null\">\n"
        + "                enroll_button = #{enrollButton, typeHandler=ButtonStyleTypeHandler},\n"
        + "            </if>\n"

        + "            <if test=\"enrollEndTime != null\">\n"
        + "                enroll_end_time = #{enrollEndTime},\n"
        + "            </if>\n"
        + "            <if test=\"enrollNoticePoint != null\">\n"
        + "                enroll_notice_point = #{enrollNoticePoint},\n"
        + "            </if>\n"
        + "            <if test=\"defaultContentMobileDisplay != null\">\n"
        + "                default_content_mobile_display = #{defaultContentMobileDisplay},\n"
        + "            </if>\n"
        + "            <if test=\"defaultPosterMobileDisplay != null\">\n"
        + "                default_poster_mobile_display = #{defaultPosterMobileDisplay},\n"
        + "            </if>\n"
        + "            <if test=\"enrollPendingReviewTip != null\">\n"
        + "                enroll_pending_review_tip = #{enrollPendingReviewTip},\n"
        + "            </if>\n"
        + "            <if test=\"enrollReviewFailureTip != null\">\n"
        + "                enroll_review_failure_tip = #{enrollReviewFailureTip},\n"
        + "            </if>\n"
        + "            enroll_check_employee = #{enrollCheckEmployee},\n"
        + "            enroll_check_department = #{enrollCheckDepartment},\n"
        + "            update_time = now(),\n"
        + "            update_by = #{updateBy}\n"
        + "        </set>\n"
        + "  WHERE \"id\" = #{id}"
        + "</script>")
    boolean updateConferenceEnrollSetting(ActivityEntity entity);

    @Select("<script>"
        + "SELECT * FROM activity\n"
        + "  WHERE id = #{id}"
        + "</script>")
    ActivityEntity getConferenceById(@Param("id") String id);

    @Select("<script>"
        + "SELECT * FROM activity\n"
        + "  WHERE marketing_event_id = #{marketingEventId} AND ea = #{ea}"
        + "</script>")
    ActivityEntity getConferenceByMarketingEventId(@Param("marketingEventId") String marketingEventId, @Param("ea") String ea);

    @Select("SELECT * FROM activity WHERE ea=#{ea} AND marketing_event_id = #{marketingEventId}")
    ActivityEntity getConferenceByEaAndMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("<script>"
            + "SELECT * FROM activity WHERE ea=#{ea}\n"
            + "AND marketing_event_id IS NOT NULL\n"
            + "AND status = 1\n"
            + "<if test=\"marketingEventIds != null\">\n"
            + "  AND marketing_event_id IN\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingEventIds[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + "<if test=\"activityStatus != null \">"
            + "AND status IN\n"
            +   "<foreach collection = 'activityStatus' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{activityStatus[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + "<if test=\"keyword != null\">\n"
            +     "AND title LIKE CONCAT('%', #{keyword}, '%')\n"
            + "</if>\n"
            + "<if test=\"flowStatusList != null \">\n"
            + "   <trim prefix=\"AND (\" prefixOverrides=\"AND|OR\" suffix=\")\">\n"
            + "     <foreach collection=\"flowStatusList\" index=\"idx\" item=\"state\">\n"
            + "       <if test=\"flowStatusList[idx] == 1\">\n"
            + "         <![CDATA[ OR (start_time < now() AND end_time > now()) ]]>\n"
            + "       </if>\n"
            + "       <if test=\"flowStatusList[idx] == 2\">\n"
            + "         <![CDATA[ OR start_time >= now()]]>\n"
            + "       </if>\n"
            + "       <if test=\"flowStatusList[idx] == 3\">\n"
            + "         <![CDATA[ OR end_time <= now() ]]>\n"
            + "       </if>\n"
            + "     </foreach>\n"
            + "   </trim>\n"
            + "</if>\n"
            + "ORDER BY start_time DESC"
            + "</script>")
    List<ActivityEntity> pageActivityEnrollsByEaAndId(@Param("ea") String ea, @Param("keyword")String keyword, @Param("activityStatus") List<Integer> activityStatus,
                                                      @Param("marketingEventIds") List<String> marketingEventIds, @Param("page") Page page, @Param("flowStatusList") List<Integer> flowStatusList);

    @Select("<script>"
            + "SELECT * FROM activity WHERE ea=#{ea}\n"
            + "<if test=\"marketingEventIds != null \">"
            + "  AND marketing_event_id IN\n"
            +   "<foreach collection = 'marketingEventIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +       "#{marketingEventIds[${num}]}"
            +   "</foreach>"
            + "</if>\n"
            + "ORDER BY create_time DESC"
            + "</script>")
    List<ActivityEntity> getActivityByEaAndMarketingEventIds(@Param("ea") String ea, @Param("marketingEventIds") List<String> marketingEventIds);

    @Select("SELECT id FROM activity WHERE marketing_event_id = #{marketingEventId} ")
    String getActivityIdByMarketingEventId(@Param("marketingEventId") String marketingEventId, @Param("ea") String ea);

    @Select("SELECT id AS conferenceId, ea, create_by AS creatorUserId, last_enroll_notice_time AS lastSendTime,\n"
            +  "enroll_check_employee AS enrollCheckEmployee, enroll_notice_point AS enrollNoticePoint, marketing_event_id AS marketingEventId FROM activity WHERE\n"
            +  "enroll_review = TRUE AND last_enroll_notice_time < #{checkpoint} AND enroll_notice_point != 0 AND (enroll_end_time is null OR (last_enroll_notice_time <= enroll_end_time))")
    List<EnrollNoticeTaskDTO> queryCheckEnrollNotice(@Param("checkpoint")Date checkpoint);

    @Update("UPDATE activity SET last_enroll_notice_time = now() WHERE id = #{conferenceId} AND last_enroll_notice_time < #{checkpoint} AND enroll_notice_point = #{type}")
    int updateSendCheckEnrollNoticeFlag(@Param("conferenceId")String conferenceId, @Param("checkpoint")Date checkpoint, @Param("type")Integer type);

    @Select("<script>"
            + "SELECT id AS conferenceId, title AS conferenceName, marketing_event_id AS marketingEventId, activity_detail_site_id AS activityDetailSiteId FROM activity WHERE id IN"
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<ConferenceIdNameDTO> queryConferenceNameDTO(@Param("ids")List<String> ids);


    @Select("<script>"
        + " SELECT count(1) AS count, MAX(C.create_time) AS enroll_time FROM conference_review_employee AS A \n"
        + " JOIN activity_enroll_data AS B ON A.conference_id = B.activity_id\n"
        + " JOIN campaign_merge_data AS C ON B.form_data_user_id = C.id\n"
        + " WHERE C.ea = #{ea} and user_id = #{userId}\n"
        + "<if test=\"reviewStatus != null\">\n"
        +     "AND review_status = #{reviewStatus}\n"
        + "</if>\n"
        + " GROUP BY C.ea"
        + "</script>")
    ConferenceStatisticsDTO getConferenceEnrollStatistics(@Param("ea") String ea, @Param("userId") Integer userId, @Param("reviewStatus") Integer reviewStatus);

    @Select("SELECT marketing_event_id FROM activity WHERE ea=#{ea}")
    List<String> getNormalMarketingEventByEa(@Param("ea")String ea);

    @Select("<script>"
        + " SELECT * FROM activity "
        + "  <if test=\"ea != null\">\n"
        + "        WHERE ea = #{ea}\n"
        + "  </if>\n"
        + "</script>")
    List<ActivityEntity> getAllDataByEa(@Param("ea") String ea);

    @Update("<script>"
            +" UPDATE activity\n"
            + "       <set>\n"
            + "            \"status\" = #{status},\n"
            + "            update_time = now(),\n"
            + "        </set>\n"
            + "  WHERE \"marketing_event_id\" =#{marketingEventId} AND ea=#{ea}"
            + "</script>")
    int updateConferenceStatusByMarketingEvent(@Param("ea")String ea, @Param("marketingEventId")String marketingEventId, @Param("status")int status);

    @Update("UPDATE activity SET activity_detail_site_id = #{siteId} WHERE id = #{id}")
    int updateConferenceDetailSiteId(@Param("id") String id, @Param("siteId") String siteId);

    @Update("UPDATE activity SET conference_details = #{conferenceDetail} WHERE id = #{id}")
    int updateConferenceContent(@Param("conferenceDetail") String conferenceDetail, @Param("id") String id);

    @Select("SELECT * FROM activity WHERE activity_detail_site_id = #{siteId}")
    ActivityEntity getActivityByDetailSiteId(@Param("siteId") String siteId);

    @Select("<script>"
            + "SELECT * FROM activity WHERE activity_detail_site_id in\n"
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>"
    )
    List<ActivityEntity> getActivityByDetailSiteIds(@Param("ids") List<String> ids);

}
