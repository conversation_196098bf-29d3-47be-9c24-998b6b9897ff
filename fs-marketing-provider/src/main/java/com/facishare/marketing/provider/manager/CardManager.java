package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.BatchUpdateUserCardArg;
import com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService;
import com.facishare.mankeep.common.enums.CardVisualRangeEnum;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.MemberTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.card.CardTemplateDao;
import com.facishare.marketing.provider.dao.card.CardTemplateDepartmentDao;
import com.facishare.marketing.provider.dao.card.EnterpriseDefaultCardDao;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.CardTemplateDepartmentEntity;
import com.facishare.marketing.provider.entity.CardTemplateEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.enterprisecard.EnterpriseDefaultCardEntity;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * Created  By zhoux 2020/12/23
 **/
@Component
@Slf4j
public class CardManager {

    private static final ExecutorService executorService = NamedThreadPool.newFixedThreadPool(6, "marketing_card_update");

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private AccountManager accountManager;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private CardTemplateDao cardTemplateDao;

    @Autowired
    private CardTemplateDepartmentDao cardTemplateDepartmentDao;

    @Autowired
    private AuthManager authManager;

    @Autowired
    private CoverImageManager coverImageManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private OutEnterpriseDefaultCardService outEnterpriseDefaultCardService;

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private EnterpriseDefaultCardDao enterpriseDefaultCardDao;

    public void resetCardInfoByVisualRange(CardEntity cardEntity) {
        if (cardEntity == null || StringUtils.isBlank(cardEntity.getVisualRangeString())) {
            return;
        }
        CardVisualRange cardVisualRange = GsonUtil.getGson().fromJson(cardEntity.getVisualRangeString(), CardVisualRange.class);
        if (cardVisualRange == null) {
            return;
        }
        if (cardVisualRange.getPhone() != null && cardVisualRange.getPhone() == CardVisualRangeEnum.NONE.getType()) {
            //String phone = cardEntity.getPhone().replaceAll("(\\d{3})\\d{8}", "$1********");
            cardEntity.setPhone("********");
        }
        if (cardVisualRange.getEmail() != null && cardVisualRange.getEmail() == CardVisualRangeEnum.NONE.getType()) {
            cardEntity.setEmail("********");
        }
        if (cardVisualRange.getWechat() != null && cardVisualRange.getWechat() == CardVisualRangeEnum.NONE.getType()) {
            cardEntity.setWechat("********");
        }
    }

    public void resetCardQrCodeByEa(String ea) {
        Optional<String> appIdOptional = wechatAccountManager.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID);
        if (!appIdOptional.isPresent()) {
            return;
        }
        String appId = appIdOptional.get();
        List<FSBindEntity> fsBindEntityList = Lists.newArrayList();
        if (WxAppInfoEnum.isMankeep(appId)) {
            fsBindEntityList = fsBindManager.queryFSBindByType(ea, AccountTypeEnum.MINI_APP.getType(), null);
        } else {
            fsBindEntityList = fsBindManager.queryFSBindByType(ea, AccountTypeEnum.QYWX_MINI_APP.getType(), appId);
        }
        for (FSBindEntity fsBindEntity : fsBindEntityList) {
            if (StringUtils.isBlank(fsBindEntity.getUid()) || StringUtils.isBlank(fsBindEntity.getFsEa())) {
                continue;
            }
            try {
                accountManager.createCardQRCode(fsBindEntity.getUid(), fsBindEntity.getFsEa());
            } catch (Exception e) {
                log.warn("CardManager.resetCardQrCodeByEa error e:{}", e);
            }
        }
    }

    public void resetCardQrCodeByUid(String uid) {
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
        if (fsBindEntity == null) {
            return;
        }
        accountManager.createCardQRCode(fsBindEntity.getUid(), fsBindEntity.getFsEa());
    }

    @Data
    public class CardVisualRange implements Serializable {

        private Integer email;

        private Integer phone;

        private Integer wechat;
    }


    /**
     * 根据uid查询当前使用的模板
     * @param uid
     * @return
     */
    public String getCardTemplateIdByUid(String uid){
        String cardTemplateId = null;
        CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
        if (cardEntity != null) {
            cardTemplateId = cardEntity.getCardTemplateId();
        }
        if (StringUtils.isBlank(cardTemplateId)) {
            String ea = fsBindManager.getEaByUid(uid);
            if (StringUtils.isBlank(ea)) {
                return null;
            }
            CardTemplateEntity defaultEntity = cardTemplateDao.getDefault(ea);
            if (defaultEntity != null) {
                cardTemplateId = defaultEntity.getId();
            }
        }
        return cardTemplateId;
    }

    /**
     * 根据uid查询当前使用的模板(适配某些场景下没传uid的情况)
     * @param uid
     * @param ea
     * @return
     */
    public String getCardTemplateIdByUidOrEa(String uid, String ea){
        if (StringUtils.isNotBlank(uid)) {
            return getCardTemplateIdByUid(uid);
        } else {
            // 获取默认模板
            CardTemplateEntity cardTemplateEntity = cardTemplateDao.getDefault(ea);
            if (cardTemplateEntity != null) {
                return cardTemplateEntity.getId();
            }
            return null;
        }
    }

    /**
     * 修正单个员工名片
     * @param uid
     */
    public void correctCard(String uid, Integer updateType){
        try {
            CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
            if (cardEntity == null) {
                return;
            }

            // 查询绑定表
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            if (fsBindEntity == null) {
                return;
            }

            // 检查名片表的模板id和实际模板是否一致，不一致则更新，一致则直接返回
            String oldCardTemplateId = cardEntity.getCardTemplateId();
            String newCardTemplateId = matchCardTemplateByUid(uid);
            if (Objects.equals(oldCardTemplateId, newCardTemplateId) || StringUtils.isBlank(newCardTemplateId)) {
                log.info("名片模板无需更新, uid:{}, cardTemplateId:{}", uid, newCardTemplateId);
                return;
            }

            String ea = fsBindEntity.getFsEa();
            if (StringUtils.isBlank(newCardTemplateId)) {
                log.info("not found match cardTemplate");
                return;
            }
            // 更新名片表的模板id
            cardDAO.updateCardTemplateById(uid, newCardTemplateId);

            // 更新名片
            updateOneCard(uid, updateType);
            /*BatchUpdateUserCardArg arg = new BatchUpdateUserCardArg();
            arg.setEa(ea);
            arg.setUid(uid);
            arg.setUpdateType(updateType);
            outEnterpriseDefaultCardService.batchUpdateUserCard(arg);
            coverImageManager.createCardShareCoverAsync(uid);*/
        } catch (Exception e) {
            log.warn("CardManager -> correctCard error, uid:{}", uid);
        }
    }

    /**
     * 批量修正名片（异步线程池处理）
     * @param uids
     * @param updateType
     */
    public void batchCorrectCard(List<String> uids, Integer updateType){
        CountDownLatch countDownLatch = new CountDownLatch(uids.size());
        TraceContext context = TraceContext.get();
        for (String uid : uids) {
            executorService.execute(()->{
                try {
                    if (context != null ) {
                        TraceContext._set(context);
                    }
                    correctCard(uid, updateType);
                } catch (Exception e) {
                    log.info("batchCorrectCard error, uid:{}", uid, e);
                } finally {
                    countDownLatch.countDown();
                    if (context != null ) {
                        TraceContext.remove();
                    }
                }
            });
        }
    }

    /**
     * 更新单个员工名片
     * @param uid
     */
    public void updateOneCard(String uid, Integer updateType){
        try {
            // 查询绑定表
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            if (fsBindEntity == null) {
                return;
            }
            String ea = fsBindEntity.getFsEa();

            // 更新名片
            BatchUpdateUserCardArg arg = new BatchUpdateUserCardArg();
            arg.setEa(ea);
            arg.setUid(uid);
            arg.setUpdateType(updateType);
            outEnterpriseDefaultCardService.batchUpdateUserCard(arg);
            coverImageManager.createCardShareCoverAsync(ea,uid);
        } catch (Exception e) {
            log.warn("CardManager -> updateOneCard error, uid:{}", uid, e);
        }
    }

    /**
     * 批量更新名片（异步线程池处理）
     * @param uids
     * @param updateType
     */
    public void batchUpdateCard(List<String> uids, Integer updateType){
        CountDownLatch countDownLatch = new CountDownLatch(uids.size());
        TraceContext context = TraceContext.get();
        for (String uid : uids) {
            executorService.execute(()->{
                try {
                    if (context != null ) {
                        TraceContext._set(context);
                    }
                    updateOneCard(uid, updateType);
                } catch (Exception e) {
                    log.info("batchUpdateCard card error, uid:{}", uid, e);
                } finally {
                    countDownLatch.countDown();
                    if (context != null ) {
                        TraceContext.remove();
                    }
                }
            });
        }
    }

    /**
     * 更新全部名片
     * @param ea
     */
    public void updateAllCard(String ea, Integer updateType){
        // 查询全企业已开通名片uid
        List<CardEntity> cardEntities = fsBindManager.getAllCardByEa(ea);
        if (CollectionUtils.isEmpty(cardEntities)) {
            return;
        }

        List<String> cardUids = cardEntities.stream().map(CardEntity::getUid).collect(Collectors.toList());
        batchCorrectCard(cardUids, updateType);
    }


    /**
     * 更新使用当前模板的人员名片，适用于修改某个模板的设置时触发
     * @param ea
     * @param cardTemplateId
     */
    public void updateUserCardByCardTemplateId(String ea, String cardTemplateId) {
        // 查询该模板的使用人
        List<CardEntity> cardEntities = cardDAO.queryByCardTplId(cardTemplateId);
        if (CollectionUtils.isEmpty(cardEntities)) {
            return;
        }
        // 更新名片
        List<String> cardUids = cardEntities.stream().map(CardEntity::getUid).collect(Collectors.toList());
        batchUpdateCard(cardUids, 1);
    }

    /**
     * 根据uid匹配适用模板
     * @param uid
     * @return
     */
    private String matchCardTemplateByUid(String uid){
        // 查询绑定表
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
        if (fsBindEntity == null) {
            return null;
        }

        String fsEa = fsBindEntity.getFsEa();
        Integer fsUserId = fsBindEntity.getFsUserId();
        if (QywxUserConstants.isVirtualUserId(fsUserId)){
            // 虚拟账号，使用默认模板
            CardTemplateEntity defaultEntity = cardTemplateDao.getDefault(fsEa);
            return defaultEntity.getId();
        }
        String cardTemplateId = matchCardTemplateByUser(fsEa, fsUserId);
        log.info("matchCardTemplateByUid, uid:{}, ea:{}, userId:{}, cardTemplateId:{}", uid, fsEa, fsUserId, cardTemplateId);
        return cardTemplateId;
    }

    /**
     * 匹配员工适用模板
     * @param ea
     * @param userId
     * @return
     */
    private String matchCardTemplateByUser(String ea, Integer userId){
        int ei = eieaConverter.enterpriseAccountToId(ea);
        CardTemplateEntity defaultCardTplEntity = cardTemplateDao.getDefault(ea);

        // 获取员工部门(包含上级)
        List<Integer> deptIds = authManager.getUserMainDepartmentsOrderly(ei, userId);
        // 查询部门有无设置模板，如果没有，则返回默认模板id
        List<CardTemplateDepartmentEntity> cardTemplateDepartmentEntities = cardTemplateDepartmentDao.batchGetByDeptIds(ea, deptIds);
        if (CollectionUtils.isEmpty(cardTemplateDepartmentEntities)) {
            return defaultCardTplEntity.getId();
        }

        for (Integer deptId : deptIds) {
            List<CardTemplateDepartmentEntity> cardTemplateDepartmentEntityList = cardTemplateDepartmentDao.getByDeptId(ea, deptId);
            if (CollectionUtils.isNotEmpty(cardTemplateDepartmentEntityList)) {
                return cardTemplateDepartmentEntityList.get(0).getCardTemplateId();
            }
        }
        return defaultCardTplEntity.getId();
    }

    public void asyncCreateMemberCard(String ea, String memberId, String uid) {
        ThreadPoolUtils.execute(() -> createMemberCard(ea, memberId, uid), ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public void createMemberCard(String ea, String memberId, String uid) {
        CardEntity existCardEntity = cardDAO.queryCardInfoByUid(uid);
        if (existCardEntity != null) {
            log.info("会员已存在名片,ea: {} memberId: {} uid: {}", ea, memberId, uid);
            return;
        }
        ObjectData memberObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        if (memberObjectData == null) {
            log.warn("会员对象数据不存在, ea: {} memberId: {}", ea, memberId);
            return;
        }
        String memberType = memberObjectData.getString("member_type");
        if (!MemberTypeEnum.isMemberMarketing(memberType)) {
            log.info("会员不是员工或者伙伴，ea: {} memberId: {} memberType: {}", ea, memberId, memberType);
            return;
        }
        CardTemplateEntity cardTemplateEntity = cardTemplateDao.getDefault(ea);
        if (cardTemplateEntity == null) {
            log.warn("默认名片模板不存在, ea: {}", ea);
            return;
        }
        String avatar = null;
        Object avatarObject = memberObjectData.get("avatar");
        if (avatarObject != null) {
            List<Object> avatarObjectList = (ArrayList) avatarObject;
            if (CollectionUtils.isNotEmpty(avatarObjectList)) {
                Object path = ((Map) avatarObjectList.get(0)).get("path");
                if (path != null) {
                    String npath = path.toString();
                    avatar = fileV2Manager.getUrlByPath(npath, ea, false);
                }
            }
        }

        String memberGender =  memberObjectData.getString("gender");
        int gender = memberGender == null ? 0 : (memberGender.equals("male") ? 1 : 2);
        CardEntity cardEntity = new CardEntity();
        cardEntity.setId(UUIDUtil.getUUID());
        cardEntity.setUid(uid);
        cardEntity.setName(memberObjectData.getName());
        cardEntity.setGender(gender);
        cardEntity.setAvatar(avatar);
        cardEntity.setAvatarThumbnail(avatar);
        cardEntity.setPhone(memberObjectData.getString("phone"));
        cardEntity.setEmail(memberObjectData.getString("email"));
        cardEntity.setCardTemplateId(cardTemplateEntity.getId());
        EnterpriseDefaultCardEntity enterpriseDefaultCard = enterpriseDefaultCardDao.queryEnterpriseDefaultCardByEa(ea, cardTemplateEntity.getId());
        if (enterpriseDefaultCard == null) {
            enterpriseDefaultCard = enterpriseDefaultCardDao.getByEa(ea);
        }
        if (enterpriseDefaultCard != null) {
            cardEntity.setCompanyName(enterpriseDefaultCard.getCompanyName());
            cardEntity.setCompanyAddress(enterpriseDefaultCard.getCompanyAddress());
        }
        cardDAO.insert(cardEntity);
    }

}
