package com.facishare.marketing.provider.service.kis;

import com.facishare.mankeep.api.outService.arg.sensors.AddSensorsDataArg;
import com.facishare.mankeep.api.outService.service.OutSensorsService;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.mankeep.common.enums.ObjectTypeEnum;
import com.facishare.mankeep.common.typehandlers.value.ActionVO;
import com.facishare.marketing.api.result.SpreadTaskNormalResult;
import com.facishare.marketing.api.result.kis.KisNoticeDetailResult;
import com.facishare.marketing.api.result.kis.QuerySpreadTaskListResult;
import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.QywxTaskDao;
import com.facishare.marketing.provider.dao.TriggerTaskInstanceDao;
import com.facishare.marketing.provider.dao.kis.SpreadTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.QywxTaskEntity;
import com.facishare.marketing.provider.entity.TriggerTaskInstanceEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2019/2/21.
 */
@Service("spreadTaskService")
@Slf4j
public class SpreadTaskServiceImpl implements SpreadTaskService {
    @Autowired
    private SpreadTaskManager spreadTaskManager;

    @Autowired
    private OutSensorsService sensorsService;

    @Autowired
    private QYWXContactService qywxContactService;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxTaskDao qywxTaskDao;

    @Autowired
    private SpreadTaskDAO spreadTaskDAO;

    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Override
    public Result<QuerySpreadTaskListResult> querySpreadTaskList(String ea, Integer userId, boolean isQywx,String upstreamEa){
        ThreadPoolUtils.execute(() -> {
            try {
                AddSensorsDataArg sensorsDataArg = new AddSensorsDataArg();
                List<ActionVO> vos = Lists.newArrayList();
                ActionVO actionVO = new ActionVO();
                actionVO.setId(UUIDUtil.getUUID());
                actionVO.setActionType(ActionTypeEnum.APP_MARKETING_RECORD.getAction());
                actionVO.setObjectType(ObjectTypeEnum.APP_MARKETING.getType());
                actionVO.setObjectName(ObjectTypeEnum.APP_MARKETING.name());
                actionVO.setFsEa(ea);
                actionVO.setUserId(userId);
                actionVO.setActionTime(new Date());
                vos.add(actionVO);
                sensorsDataArg.setVos(vos);
                sensorsService.addSensorsData(sensorsDataArg);

                if (isQywx) {
                    QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
                    if (qywxMiniappConfigEntity != null) {
                        String uid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, userId, AccountTypeEnum.QYWX_MINI_APP.getType(), qywxMiniappConfigEntity.getAppid());
                        if (uid != null) {
                            qywxContactService.setContactConfigByEmployee(uid, ea, userId);
                        }
                    }
                }
            } catch (Exception e) {
                log.info("SpreadTaskServiceImpl querySpreadTaskList sensorsService addSensorsData, exception: {}", e.fillInStackTrace());
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return spreadTaskManager.querySpreadTaskList(ea, userId,null);
    }

    @Override
    public Result<Void> spreadTaskByNotice(String ea, Integer userId, String noticeId) {
        spreadTaskManager.spreadTaskByNotice(ea, userId, noticeId);
        return Result.newSuccess();
    }

    @Override
    public Result<KisNoticeDetailResult> getNoticeDetail(String noticeId) {
        return spreadTaskManager.getNoticeDetail(noticeId);
    }

    @Override
    public Result<Void> qywxTaskSendRecord(String fsEa, Integer fsUserId, String externalUserId, String qyUserId, String marketingActivityId, String triggerTaskInstanceId, Integer type) {
        QywxTaskEntity qywxTaskEntity = new QywxTaskEntity();
        Integer sopStatus = null;
        if (type != 3) {
            qywxTaskEntity.setId(UUIDUtil.getUUID());
            qywxTaskEntity.setEa(fsEa);
            qywxTaskEntity.setMarketingActivityId(marketingActivityId);
            TriggerTaskInstanceEntity instence = triggerTaskInstanceDao.getById(triggerTaskInstanceId);
            sopStatus = qywxTaskDao.getSopStatus(fsEa, instence.getTriggerId(), triggerTaskInstanceId,qyUserId,externalUserId);
            qywxTaskEntity.setTriggerId(instence.getTriggerId());
            qywxTaskEntity.setTriggerSnapshotId(instence.getTriggerSnapshotId());
            qywxTaskEntity.setTriggerTaskSnapshotId(instence.getTriggerTaskSnapshotId());
            qywxTaskEntity.setTriggerTaskInstanceId(triggerTaskInstanceId);
            qywxTaskEntity.setExternalUserId(externalUserId);

            if (null != sopStatus && sopStatus > 0) {
                return null;
            }
            qywxTaskEntity.setQywxUserId(qyUserId);
            qywxTaskEntity.setTaskType(type);
            qywxTaskEntity.setFsUserId(fsUserId);
            qywxTaskEntity.setStatus(1);
            qywxTaskDao.insert(qywxTaskEntity);
        }
        return null;
    }

    @Override
    public Result<Boolean> spreadTaskIsRevocation(String id) {
       return spreadTaskManager.spreadTaskIsRevocation(id);
    }

    @Override
    public Result<SpreadTaskNormalResult> spreadTaskIsNormal(String objectId,Integer objectType,String marketingActivityId,String marketingEventId, String fsEa) {
        String ea = null;
        if (StringUtils.isNotBlank(fsEa)) {
            ea = fsEa;
        } else {
            ea = objectManager.getObjectEa(objectId, objectType);
        }

        if (ea == null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return spreadTaskManager.spreadTaskIsNormal(ea,marketingActivityId,marketingEventId);
    }
}
