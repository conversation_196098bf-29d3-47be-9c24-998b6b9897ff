package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.api.result.GetBoundMiniappInfoResult;
import com.facishare.marketing.api.result.WxThirdPlatformVersionResult;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.MiniappIntroductionSiteTypeEnum;
import com.facishare.marketing.common.enums.MiniappReleaseStatusEnum;
import com.facishare.marketing.common.enums.MiniappTypeEnum;
import com.facishare.marketing.common.enums.NextReleaseActionTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.outapi.arg.result.MiniappInfoResult;
import com.facishare.marketing.outapi.service.WechatAccountOutService;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.EnterpriseMetaConfigEntity;
import com.facishare.marketing.provider.entity.MiniappReleaseRecordEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.fxiaoke.wechatrestapi.arg.GetAuditStatusArg;
import com.fxiaoke.wechatrestapi.data.BusinessInfo;
import com.fxiaoke.wechatrestapi.result.GetAuditStatusResult;
import com.fxiaoke.wechatrestapi.result.GetCodeTemplateListResult;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Created  By zhoux 2020/04/08
 **/
@Service("wechatAccountOutService")
public class WechatAccountOutServiceImpl implements WechatAccountOutService {

    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;

    @Override
    public Result<String> getAccessTokenByWxAppId(String wxAppId) {
        String accessToken = wechatAccountManager.getAccessTokenByWxAppId(wxAppId);
        if (StringUtils.isNotBlank(accessToken)) {
            return Result.newSuccess(accessToken);
        } else {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<MiniappInfoResult> getMiniappInfoByWxAppId(String platformId, String wxAppId) {
        List<String> eaByWxAppIds = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(platformId,wxAppId);
        if (CollectionUtils.isEmpty(eaByWxAppIds)){
            return Result.newError(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        MiniappInfoResult result = new MiniappInfoResult();
        result.setEa(eaByWxAppIds.get(0));
        result.setWxAppId(wxAppId);
        String realWxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(eaByWxAppIds.get(0));
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(wxAppId);
        BeanUtils.copyProperties(wechatAccountConfig, result);
        return Result.newSuccess(result);
    }
}
