package com.facishare.marketing.provider.service.dingding;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.qywx.miniapp.IsBindArg;
import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppDepartmentArg;
import com.facishare.marketing.api.arg.qywx.miniapp.QueryDingMiniAppStaffArg;
import com.facishare.marketing.api.result.dingding.DingSendByTemplateMessageResult;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppDepartmentResult;
import com.facishare.marketing.api.result.qywx.miniapp.DingMiniAppStaffResult;
import com.facishare.marketing.api.result.qywx.miniapp.IsBindResult;
import com.facishare.marketing.api.service.DingMiniAppDepartmentService;
import com.facishare.marketing.api.service.DingMiniAppStaffService;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.entity.UserEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.innerResult.ding.DingAuthScopeResult;
import com.facishare.marketing.provider.innerResult.ding.DingStaffInfoResult;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.open.ding.api.arg.DingOutSendMessageArg;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 16:45
 */
@Slf4j
@Service("dingMiniAppStaffService")
public class DingMiniAppStaffServiceImpl implements DingMiniAppStaffService {

    @Autowired
    private DingManager dingManager;

    @Autowired
    private DingMiniAppDepartmentService dingMiniAppDepartmentService;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private DingAuthService dingAuthService;

    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private UserManager userManager;

    @ReloadableProperty("dingding.miniapp.suiteId")
    private String SUITE_ID;

    @Override
    public Result<List<DingMiniAppStaffResult>> queryDingMiniAppStaff(QueryDingMiniAppStaffArg arg) {
        //获取钉钉accessToken
        String dingAccessToken = dingManager.getAuthAccessToken(arg.getFsEa());

        //获取通讯录范围
        DingAuthScopeResult authScopes = dingManager.getAuthScopes(dingAccessToken);
        if (!authScopes.isSuccess() || authScopes.getAuthOrgScopes() == null){
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        //获取指定的用户userIds
        List<String> authedUser = authScopes.getAuthOrgScopes().getAuthedUser();

        //获取所有部门
        QueryDingMiniAppDepartmentArg departmentArg = new QueryDingMiniAppDepartmentArg();
        departmentArg.setFsEa(arg.getFsEa());
        Result<List<DingMiniAppDepartmentResult>> listResult = dingMiniAppDepartmentService.queryDingMiniAppDepartment(departmentArg);
        List<Integer> departmentIds = Lists.newArrayList();
        if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())){
            departmentIds = listResult.getData().stream().map(DingMiniAppDepartmentResult::getDeptId).collect(Collectors.toList());
        }
        if (authScopes.getAuthOrgScopes().getAuthedDept().contains(1)){
            departmentIds.add(1);
        }
        List<DingStaffInfoResult.UserGetResponse> dingStaffList = dingManager.getAllDingStaff(arg.getFsEa(), dingAccessToken,authedUser, departmentIds);
        List<DingMiniAppStaffResult> resultList = BeanUtil.copy(dingStaffList, DingMiniAppStaffResult.class);
        for (DingMiniAppStaffResult dingMiniAppStaffResult : resultList) {
            if (StringUtils.isNotBlank(dingMiniAppStaffResult.getAvatar())){
                dingMiniAppStaffResult.setAvatar(fileV2Manager.replaceUrlToHttps(dingMiniAppStaffResult.getAvatar()));
            }
            QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getVirtualUserByEaAndQyId(arg.getFsEa(), dingMiniAppStaffResult.getUserid());
            dingMiniAppStaffResult.setFsUserId( qywxVirtualFsUserEntity == null ? null : qywxVirtualFsUserEntity.getUserId());
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<Integer> getUserIdByDingInfo(String fsEa, String corpId, String dingUserId) {
        Integer fsUserId = qywxUserManager.getUserIdByDingInfo(fsEa, corpId, dingUserId, QywxUserConstants.TRY_TIME);
        return Result.newSuccess(fsUserId);
    }

    @Override
    public Result<DingSendByTemplateMessageResult> dingSendMessage(String suiteId, String templateId, Map<String, Object> dataMap, List<String> userIdList, String corpId) {
        DingOutSendMessageArg arg = new DingOutSendMessageArg();
        arg.setSuiteId(SUITE_ID);
        arg.setTemplateId(templateId);
        arg.setDingCorpId(corpId);
        arg.setDingEmpIds(userIdList);
        arg.setDataMap(dataMap);
        com.facishare.open.ding.common.result.Result<String> sendMessageResult = dingAuthService.sendMessage(arg);
        if (!sendMessageResult.isSuccess()) {
            log.warn("dingAuthService.sendMessage fail, corpId:{}, templateId:{}, dingEmpIds:{}", corpId, templateId, userIdList);
            return Result.newError(SHErrorCode.SEND_FAILED);
        }

        DingSendByTemplateMessageResult dingSendByTemplateMessageResult = JSONObject.parseObject(sendMessageResult.getData(), DingSendByTemplateMessageResult.class);
        return Result.newSuccess(dingSendByTemplateMessageResult);
    }

    @Override
    public Result<Void> addressBookCallBack(String eventType, String fsEa, String corpId, List<String> userIds) {
        //获取钉钉accessToken
        String dingAccessToken = dingManager.getAuthAccessToken(fsEa);
        //获取变更userIds 信息列表
        List<DingStaffInfoResult.UserGetResponse> staffList = dingManager.getStaffByUserId(dingAccessToken, Sets.newHashSet(userIds));

        //根据eventType 进行相应的处理
        switch (eventType){
            case "user_add_org":
                return dingManager.addStaffToDB(fsEa,staffList);
            case "user_modify_org":
                return dingManager.modifyStaffToDB(fsEa,staffList);
            case "user_leave_org":
                return dingManager.leaveStaffToDB(fsEa,userIds);
            case "user_active_org":
                return dingManager.activeStaffToDB(fsEa,userIds);
            case "org_admin_add":
                return dingManager.addAdminStaffToDB(fsEa,userIds);
            case "org_admin_remove":
                return dingManager.removeAdminStaffToDB(fsEa,userIds);
            default:
                return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
    }

    @Override
    public Result<List<DingMiniAppStaffResult>> getDingStaffByDepartmentIds(String ea, List<Integer> departmentIds) {
        //获取钉钉accessToken
        String dingAccessToken = dingManager.getAuthAccessToken(ea);
        Result<List<DingMiniAppDepartmentResult>> listResult = dingMiniAppDepartmentService.queryDepartmentIds(ea,departmentIds);
        if (!listResult.isSuccess() || CollectionUtils.isEmpty(listResult.getData())){
            return Result.newError(SHErrorCode.DEPARTMENT_NOT_FOUND);
        }
        List<Integer> deptIds = listResult.getData().stream().map(DingMiniAppDepartmentResult::getDeptId).collect(Collectors.toList());
        List<DingStaffInfoResult.UserGetResponse> dingStaffList = dingManager.getAllDingStaff(ea, dingAccessToken, Lists.newArrayList(), deptIds);
        List<DingMiniAppStaffResult> resultList = BeanUtil.copy(dingStaffList, DingMiniAppStaffResult.class);
        for (DingMiniAppStaffResult dingMiniAppStaffResult : resultList) {
            if (StringUtils.isNotBlank(dingMiniAppStaffResult.getAvatar())){
                dingMiniAppStaffResult.setAvatar(fileV2Manager.replaceUrlToHttps(dingMiniAppStaffResult.getAvatar()));
            }
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<IsBindResult> isBind(IsBindArg arg) {
        boolean isBindWechatMiniApp = false;
        UserEntity userEntity = userManager.queryByUid(arg.getUid());
        if (null == userEntity) {
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        if (StringUtils.isNotBlank(userEntity.getOpenid()) && StringUtils.isNotBlank(userEntity.getAppid())) {
            isBindWechatMiniApp = true;
        }
        IsBindResult result = new IsBindResult();
        result.setIsBindWechatMiniApp(isBindWechatMiniApp);
        return Result.newSuccess(result);
    }
}
