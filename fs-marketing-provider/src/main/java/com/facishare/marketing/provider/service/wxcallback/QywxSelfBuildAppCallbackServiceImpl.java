package com.facishare.marketing.provider.service.wxcallback;

import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.qywx.QywxAppAgentCallbackMsg;
import com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.RequestBufferTagConstants;
import com.facishare.marketing.common.enums.qywx.QywxSuiteEnum;
import com.facishare.marketing.common.mq.sender.RequestBufferSender;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpEaMappingEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.innerData.qywx.ChangeExternalContactEventMsg;
import com.facishare.marketing.provider.innerData.qywx.ExternalUserMagCallBackBufferRequestData;
import com.facishare.marketing.provider.innerData.qywx.QywxCallBackEncryptMsg;
import com.facishare.marketing.provider.innerData.qywx.QywxCallBackEventMsg;
import com.facishare.marketing.provider.manager.EnterpriseInfoManager;
import com.facishare.marketing.provider.manager.MarketingPromotionSourceArgObjectRelationManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxContactManager;
import com.facishare.marketing.provider.manager.qywx.QywxEventCallBackManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.mq.sender.MarketingMessageSender;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.mq.sender.QywxCallbackMessageSender;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.util.qywx.CryptHelper;
import com.facishare.marketing.provider.util.qywx.XMLParse;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Slf4j
@Service("qywxSelfBuildAppCallbackService")
public class QywxSelfBuildAppCallbackServiceImpl implements QywxSelfBuildAppCallbackService {
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;
    @Autowired
    private QywxAddFanQrCodeRelationDAO qywxAddFanQrCodeRelationDAO;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private QywxCorpEaMappingDAO qywxCorpEaMappingDAO;
    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;
    @Autowired
    private RequestBufferSender requestBufferSender;
    @Autowired
    private QywxCallbackMessageSender qywxCallbackMessageSender;
    @Autowired
    private MarketingMessageSender qywxReplyMessageSender;
    @Autowired
    private QywxEventCallBackManager qywxEventCallBackManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;
    @Autowired
    private QywxContactManager qywxContactManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private QywxCallbackEventBus qywxCallbackEventBus;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @ReloadableProperty("generation.development.token")
    private String generationDevelopmentToken;
    @ReloadableProperty("encoding.aes.key")
    private String encodingAESKey;
    @ReloadableProperty("center.host")
    private String centerHost;

    @Autowired
    @Qualifier("qywxDecryptMessageSender")
    private MarketingMessageSender qywxDecryptMessageSender;

    @Override
    public Result<String> callback(String corpId, String msgSignature, String timestamp, String nonce,
                                   String verifyUsableStr, String encryptXmlBody) {
        Optional<String> eaOptional = qywxManager.getFsEaByQyWxCorpId(corpId, null);
        if (!eaOptional.isPresent()) {
            log.warn("CorpId:{} not bind to fs ea", corpId);
            return Result.newSuccess("fail corp");
        }
        String ea = eaOptional.get();
        //判断企业是否停用或过期
        if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(ea)) {
            log.warn("callback ea is stop or license expire ea :{}",ea);
            return Result.newSuccess("callback ea is stop or license expire");
        }

        QywxCustomerAppInfoEntity  qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectByEa(ea, 0);
        if (qywxCustomerAppInfoEntity != null) {
            // 已经安装了代开发应用，不处理自建应用的回调
            return Result.newSuccess("no deal self app callback");
        }

        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null) {
            log.warn("Ea:{} not config yet.", ea);
            return Result.newSuccess("fail ea");
        }
        // Here we verify url
        if (!Strings.isNullOrEmpty(verifyUsableStr)) {
            String msg = CryptHelper.verifyURL(qywxCorpAgentConfigEntity.getSelfAppToken(),
                    Base64.getDecoder().decode(qywxCorpAgentConfigEntity.getSelfAppEncodingAesKey()), msgSignature,
                    timestamp, nonce, verifyUsableStr);
            if (Strings.isNullOrEmpty(msg)) {
                log.warn("verifyError");
                return Result.newSuccess("verify url error");
            }
            return Result.newSuccess(msg);
        }
        // Here we handle msg
        Object[] encryptXmlObjects = XMLParse.extract(encryptXmlBody);
        String msg = CryptHelper.decryptMsg(qywxCorpAgentConfigEntity.getSelfAppToken(),
                Base64.getDecoder().decode(qywxCorpAgentConfigEntity.getSelfAppEncodingAesKey()), msgSignature,
                timestamp, nonce, encryptXmlObjects[1].toString());
        if (Strings.isNullOrEmpty(msg)) {
            log.warn("decrypt error");
            return Result.newSuccess("decrypt error");
        }
        QywxCallBackEventMsg eventMsg = QywxCallBackEventMsg.buildEventMsg(msg);
        if (eventMsg != null && (eventMsg.isAgentSendSuiteTicketEvent() || eventMsg.isAgentCreateAuthEvent() || eventMsg.isAgentCancelAuthEvent())) {
            eventMsg.setExtraData(msgSignature, timestamp, nonce, verifyUsableStr, encryptXmlBody);
            eventMsg.setEventType(ExternalUserMagCallBackBufferRequestData.OLD_SELF_APP);
            qywxCallbackEventBus.post(eventMsg);
        } else {
            //单独处理自动回复
            handleMsgCallBack(ExternalUserMagCallBackBufferRequestData.OLD_SELF_APP, null, eventMsg);
            ExternalUserMagCallBackBufferRequestData data = new ExternalUserMagCallBackBufferRequestData();
            data.setEventType(ExternalUserMagCallBackBufferRequestData.OLD_SELF_APP);
            data.setMsg(msg);
            log.info("qywxCallback OLD_SELF_APP data: {}", data);
            sendQywxCallbackEventToRequestBuffer(ea, data);
        }
        return Result.newSuccess("success");
    }

    @Override
    public Result<String> agentCallback(String msgSignature, String timestamp, String nonce, String verifyUsableStr,
                                        String encryptXmlBody, String suitId) {
        // Here we verify url
        if (!Strings.isNullOrEmpty(verifyUsableStr)) {
            String msg = CryptHelper.verifyURL(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey)
                    , msgSignature, timestamp, nonce, verifyUsableStr);
            if (Strings.isNullOrEmpty(msg)) {
                log.warn("verifyError");
                return Result.newSuccess("verify url error");
            }
            return Result.newSuccess(msg);
        }
        // Here we handle msg
        Object[] encryptXmlObjects = XMLParse.extract(encryptXmlBody);
        String msg = CryptHelper.decryptMsg(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey),
                msgSignature, timestamp, nonce, encryptXmlObjects[1].toString());
        if (Strings.isNullOrEmpty(msg)) {
            log.warn("decrypt error");
            return Result.newSuccess("decrypt error");
        }
        //单独处理自动回复
        QywxCallBackEventMsg eventMsg = QywxCallBackEventMsg.buildEventMsg(msg);
        String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(ExternalUserMagCallBackBufferRequestData.OLD_AGNENT_APP, eventMsg.getToUserName(), eventMsg.getSuitId());
        if (StringUtils.isBlank(ea)) {
            log.warn("CorpId:{} not bind to fs ea", eventMsg.getToUserName());
            return Result.newSuccess("fail corp");
        }
        //判断企业是否停用或过期
        if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(ea)) {
            log.warn("agentCallback ea is stop or license expire ea :{}", ea);
            return Result.newSuccess("agentCallback ea is stop or license expire");
        }
        if (eventMsg != null && (eventMsg.isAgentSendSuiteTicketEvent() || eventMsg.isAgentCreateAuthEvent() || eventMsg.isAgentCancelAuthEvent())) {
            eventMsg.setExtraData(msgSignature, timestamp, nonce, verifyUsableStr, encryptXmlBody);
            eventMsg.setEventType(ExternalUserMagCallBackBufferRequestData.OLD_AGNENT_APP);
            qywxCallbackEventBus.post(eventMsg);
        } else {
            //单独处理自动回复
            handleMsgCallBack(ExternalUserMagCallBackBufferRequestData.OLD_AGNENT_APP, suitId, eventMsg);
            ExternalUserMagCallBackBufferRequestData data = new ExternalUserMagCallBackBufferRequestData();
            data.setEventType(ExternalUserMagCallBackBufferRequestData.OLD_AGNENT_APP);
            data.setMsg(msg);
            data.setSuitId(suitId);
            log.info("qywxCallback OLD_AGNENT_APP data: {}", data);
            sendQywxCallbackEventToRequestBuffer(ea, data);
        }
        return Result.newSuccess("success");
    }

    //代开发应用回调处理
    public Result<String> qywxAppAgentCallback(String msgSignature, String timestamp, String nonce,
                                               String verifyUsableStr,
                                               String encryptXmlBody, String suitId) {
        // Here we verify url
        if (!Strings.isNullOrEmpty(verifyUsableStr)) {
            String msg = CryptHelper.verifyURL(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey)
                    , msgSignature, timestamp, nonce, verifyUsableStr);
            if (Strings.isNullOrEmpty(msg)) {
                log.warn("verifyError");
                return Result.newSuccess("verify url error");
            }
            return Result.newSuccess(msg);
        }
        // Here we handle msg
        long t1 = System.currentTimeMillis();
        Object[] encryptXmlObjects = XMLParse.extract(encryptXmlBody);
        long t2 = System.currentTimeMillis();
        String msg = CryptHelper.decryptMsg(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey),
                msgSignature, timestamp, nonce, encryptXmlObjects[1].toString());
        long t3 = System.currentTimeMillis();
        if (Strings.isNullOrEmpty(msg)) {
            log.warn("decrypt error");
            return Result.newSuccess("decrypt error");
        }
        QywxCallBackEventMsg eventMsg = QywxCallBackEventMsg.buildEventMsg(msg);
        long t4 = System.currentTimeMillis();
        log.info("decryptMsg XMLParse.extract cost:{} decryptMsg cost: {} buildEventMsg: {}", t2 - t1, t3 - t2, t4 - t3);
        if (eventMsg != null && (eventMsg.isAgentSendSuiteTicketEvent() || eventMsg.isAgentCreateAuthEvent() || eventMsg.isAgentCancelAuthEvent())) {
            eventMsg.setExtraData(msgSignature, timestamp, nonce, verifyUsableStr, encryptXmlBody);
            eventMsg.setEventType(ExternalUserMagCallBackBufferRequestData.AGENT_APP);
            qywxCallbackEventBus.post(eventMsg);
        } else if (eventMsg != null) {
            // 代开发小程序回调，不处理后续业务逻辑
            if (QywxSuiteEnum.isMiniApp(suitId)) {
                return Result.newSuccess("success");
            }
            //单独处理自动回复
            handleMsgCallBack(ExternalUserMagCallBackBufferRequestData.AGENT_APP, suitId, eventMsg);
            String corpId = eventMsg.getToUserName();
            QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectOne(suitId, corpId);
            if (Objects.nonNull(qywxCustomerAppInfoEntity)) {
                String ea = qywxCustomerAppInfoEntity.getEa();
                //判断企业是否停用或过期
                if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(ea)) {
                    log.warn("handleCommonAgentAppCallbackEvent ea is stop or license expire ea :{}",ea);
                    return Result.newSuccess("handleCommonAgentAppCallbackEvent ea is stop or license expire");
                }
                ExternalUserMagCallBackBufferRequestData data = new ExternalUserMagCallBackBufferRequestData();
                data.setEventType(ExternalUserMagCallBackBufferRequestData.AGENT_APP);
                data.setSuitId(suitId);
                data.setMsg(msg);
                log.info("qywxCallback AGENT_APP data: {}", data);
                sendQywxCallbackEventToRequestBuffer(ea, data);
            } else {
                // 专属云ea
                // 跳转对应云
                Optional<String> eaOptional = qywxManager.getEaByQyWxMapping(corpId);
                if (eaOptional.isPresent()) {
                    //判断企业是否停用或过期
                    if (marketingActivityRemoteManager.enterpriseStop(eaOptional.get()) || appVersionManager.getCurrentAppVersion(eaOptional.get()) == null) {
                        log.warn("handleCommonAgentAppCallbackEvent wxThirdCloudInner ea is stop or license expire ea :{}",eaOptional.get());
                        return Result.newSuccess("handleCommonAgentAppCallbackEvent wxThirdCloudInner ea is stop or license expire");
                    }
                    wxThirdCloudInnerSend(timestamp, nonce, encryptXmlBody, suitId, eaOptional.get(), msgSignature);
                } else {
                    log.warn("CorpId:{} not bind to fs ea", corpId);
                }
            }
        }
        return Result.newSuccess("success");
    }

    //代开发应用回调处理
    @FilterLog
    public void sendQywxAppAgentCallbackMsg(QywxAppAgentCallbackMsg msg) {
        qywxDecryptMessageSender.send("QywxDecryptMessageHandler",msg);
    }

    /**
     * 代开发回调验证url
     *
     * @param msgSignature
     * @param timestamp
     * @param nonce
     * @param verifyUsableStr
     * @param encryptXmlBody
     * @param suitId
     * @return
     */
    @FilterLog
    public Result<String> qywxAppAgentVerifyURL(String msgSignature, String timestamp, String nonce,
                                                String verifyUsableStr,
                                                String encryptXmlBody, String suitId) {
        // Here we verify url
        String msg = CryptHelper.verifyURL(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey), msgSignature, timestamp, nonce, verifyUsableStr);
        if (Strings.isNullOrEmpty(msg)) {
            log.warn("verifyError");
            return Result.newSuccess("verify url error");
        }
        return Result.newSuccess(msg);
    }

    /**
     * 企微回调解密&消息分发
     *
     * @param msgSignature
     * @param timestamp
     * @param nonce
     * @param verifyUsableStr
     * @param encryptXmlBody
     * @param suitId
     */
    @FilterLog
    public void qywxAppAgentCallbackV2(String msgSignature, String timestamp, String nonce,
                                       String verifyUsableStr,
                                       String encryptXmlBody, String suitId) {
        // 解密
        long t1 = System.currentTimeMillis();
        QywxCallBackEncryptMsg qywxCallBackEncryptMsg = QywxCallBackEventMsg.fromXmlWithCache(encryptXmlBody, QywxCallBackEncryptMsg.class);
        long t2 = System.currentTimeMillis();
        String msg = CryptHelper.decryptMsg(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey), msgSignature, timestamp, nonce, qywxCallBackEncryptMsg.getEncrypt());
        long t3 = System.currentTimeMillis();
        if (Strings.isNullOrEmpty(msg)) {
            log.warn("qywxAppAgentCallbackV2 decrypt error");
            return;
        }
        QywxCallBackEventMsg eventMsg = QywxCallBackEventMsg.buildEventMsg(msg);
        long t4 = System.currentTimeMillis();
        log.info("qywxAppAgentCallbackV2 first parse xml cost:{} decryptMsg cost: {} buildEventMsg: {}", t2 - t1, t3 - t2, t4 - t3);
        if (eventMsg == null) {
            return;
        }
        // 推送Ticket/授权/取消授权：
        // 获取ea不统一，且少有改动，不考虑支持灰度
        if (eventMsg.isAgentSendSuiteTicketEvent() || eventMsg.isAgentCreateAuthEvent() || eventMsg.isAgentCancelAuthEvent()) {
            eventMsg.setExtraData(msgSignature, timestamp, nonce, verifyUsableStr, encryptXmlBody);
            eventMsg.setEventType(ExternalUserMagCallBackBufferRequestData.AGENT_APP);
            qywxCallbackEventBus.post(eventMsg);
            return;
        }
        // 其他事件:
        // 代开发小程序回调，不处理后续业务逻辑
        if (QywxSuiteEnum.isMiniApp(suitId)) {
            return;
        }
        String corpId = eventMsg.getToUserName();
        QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity = qywxCustomerAppInfoDAO.selectOne(suitId, corpId);
        if (Objects.nonNull(qywxCustomerAppInfoEntity)) {
            // 当前云
            String ea = qywxCustomerAppInfoEntity.getEa();
            //判断企业是否停用或过期
            if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(ea)) {
                log.warn("qywxAppAgentCallbackV2 ea is stop or license expire ea :{}", ea);
                return;
            }
            ExternalUserMagCallBackBufferRequestData data = new ExternalUserMagCallBackBufferRequestData();
            data.setEventType(ExternalUserMagCallBackBufferRequestData.AGENT_APP);
            data.setSuitId(suitId);
            data.setMsg(msg);
            data.setEa(ea);
            log.info("qywxAppAgentCallbackV2 AGENT_APP data: {}", data);
            sendQywxCallbackEventToRequestBuffer(ea, data);
            // 转发自动回复队列
            qywxReplyMessageSender.send("QywxReplyMessageHandler",data);
            return;
        }
        // 专属云ea
        // 跳转对应云
        Optional<String> eaOptional = qywxManager.getEaByQyWxMapping(corpId);
        if (eaOptional.isPresent()) {
            //判断企业是否停用或过期
            if (marketingActivityRemoteManager.enterpriseStop(eaOptional.get()) || appVersionManager.getCurrentAppVersion(eaOptional.get()) == null) {
                log.warn("qywxAppAgentCallbackV2 wxThirdCloudInner ea is stop or license expire ea :{}", eaOptional.get());
                return;
            }
            wxThirdCloudInnerSend(timestamp, nonce, encryptXmlBody, suitId, eaOptional.get(), msgSignature);
        } else {
            log.warn("qywxAppAgentCallbackV2 CorpId:{} not bind to fs ea", corpId);
        }
    }

    /**
     * 企微回调自动回复逻辑
     *
     * @param msgObj
     */
    public void handleQywxReply(QywxAppAgentCallbackMsg msgObj) {
        QywxCallBackEventMsg eventMsg = QywxCallBackEventMsg.buildEventMsg(msgObj.getMsg());
        // 推送Ticket/授权/取消授权： 获取ea不统一，且少有改动，不考虑支持灰度
        if (eventMsg != null && (eventMsg.isAddExternalUserEvent() || eventMsg.isAddHalfExternalUserEvent())) {
            ChangeExternalContactEventMsg changeExternalContactEventMsg = (ChangeExternalContactEventMsg) eventMsg;
            String toUserName = changeExternalContactEventMsg.getToUserName();
            String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(msgObj.getType(), toUserName, msgObj.getSuitId());
            changeExternalContactEventMsg.setEa(ea);
            //发送消息
            qywxContactManager.recvNewCustomerCallBack(changeExternalContactEventMsg);
        }
    }

    private void wxThirdCloudInnerSend(String timestamp, String nonce, String encryptXmlBody, String suitId, String ea, String msgSignature) {
        QywxCorpEaMappingEntity mappingEntity = qywxCorpEaMappingDAO.queryMappingByEa(ea);
        String callBackURL = centerHost + "/marketing/wxThirdCloudInner" + "/handleQywxAppAgentCallback";
        Map<String, String> header = new HashMap<>();
        header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(mappingEntity.getEa())));
        header.put("Content-Type", "multipart/form-data");
        FormBody requestBody = new FormBody.Builder()
                .add("encryptXmlBody", encryptXmlBody)
                .add("timestamp", timestamp)
                .add("nonce", nonce)
                .add("msg_signature", msgSignature)
                .add("autoCode","")
                .add("corpId",mappingEntity.getCorpId())
                .add("agentId", "")
                .add("suitId", suitId)
                .add("ea", mappingEntity.getEa()).build();
        httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, callBackURL, new TypeToken<String>() {}, header);
    }

    private void handleMsgCallBack(Integer type, String suitId, QywxCallBackEventMsg eventMsg) {
        if (eventMsg != null && (eventMsg.isAddExternalUserEvent() || eventMsg.isAddHalfExternalUserEvent())) {
            ChangeExternalContactEventMsg changeExternalContactEventMsg = (ChangeExternalContactEventMsg) eventMsg;
            String toUserName = changeExternalContactEventMsg.getToUserName();
            ThreadPoolUtils.execute(() -> {
                String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(type, toUserName, suitId);
                changeExternalContactEventMsg.setEa(ea);
                //发送消息
                qywxContactManager.recvNewCustomerCallBack(changeExternalContactEventMsg);
            }, ThreadPoolTypeEnums.CUSTOMER_WELCOME_MESSAGE);
        }
    }

    //发送处理消息到mq，由RequestBufferConsumer进行处理
    private void sendQywxCallbackEventToRequestBuffer(String ea, ExternalUserMagCallBackBufferRequestData data){
        data.setEa(ea);
//        requestBufferSender.send(ea, data, RequestBufferTagConstants.QYWX_CALLBACK_TAG);
        qywxCallbackMessageSender.send(ea, data);
    }

    @Override
    public Result<String> handleQywxAppAgentCallback(String encryptXmlBody, String timestamp, String nonce, String msgSignature,
                                                     String autoCode, String corpId, String agentId, String suitId, String ea) {
        // Here we handle msg
        Object[] encryptXmlObjects = XMLParse.extract(encryptXmlBody);
        String msg = CryptHelper.decryptMsg(generationDevelopmentToken, Base64.getDecoder().decode(encodingAESKey),
                msgSignature, timestamp, nonce, encryptXmlObjects[1].toString());
        if (Strings.isNullOrEmpty(msg)) {
            log.warn("decrypt error");
            return Result.newSuccess("decrypt error");
        }
        QywxCallBackEventMsg eventMsg = QywxCallBackEventMsg.buildEventMsg(msg);
        log.info("--------QywxCallBackEventMsg------------>: {}", eventMsg);
        if (eventMsg != null && (eventMsg.isAddExternalUserEvent() || eventMsg.isAddHalfExternalUserEvent())) {
            ChangeExternalContactEventMsg changeExternalContactEventMsg = (ChangeExternalContactEventMsg) eventMsg;
            ThreadPoolUtils.execute(() -> {
                changeExternalContactEventMsg.setEa(ea);
                //发送消息
                qywxContactManager.recvNewCustomerCallBack(changeExternalContactEventMsg);
            }, ThreadPoolTypeEnums.CUSTOMER_WELCOME_MESSAGE);
        }
        // 特殊处理专属云的企微授权事件(因为专属云的授权回调在纷享云已经解析过了，这里直接用解析后的参数就行，反而重复解析会出现临时授权码已经被用过的错误信息)
        if (eventMsg != null && eventMsg.isAgentCreateAuthEvent()) {
            qywxManager.saveQywxAgentPermanentCode(autoCode, suitId, corpId, agentId, ea);
            return Result.newSuccess("success");
        }
        ThreadPoolUtils.execute(() -> {
            if (eventMsg != null) {
                eventMsg.setEventType(ExternalUserMagCallBackBufferRequestData.AGENT_APP);
                eventMsg.setExtraData(msgSignature, timestamp, nonce, null, encryptXmlBody);
                eventMsg.setSuitId(suitId);
                qywxCallbackEventBus.post(eventMsg);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess("success");
    }

    @Override
    public Result<String> saveQYWXCorpIdAndEa(String ea, String host) {
        QywxCorpEaMappingEntity selectEntity = qywxCorpEaMappingDAO.queryMappingByEa(ea);
        if (selectEntity != null) {
            return Result.newSuccess();
        } else {
            QywxCorpEaMappingEntity qywxCorpEaMappingEntity = new QywxCorpEaMappingEntity();
            qywxCorpEaMappingEntity.setId(UUIDUtil.getUUID());
            qywxCorpEaMappingEntity.setCorpId("-");
            qywxCorpEaMappingEntity.setEa(ea);
            qywxCorpEaMappingEntity.setHost(host);
            qywxCorpEaMappingDAO.addMapping(qywxCorpEaMappingEntity);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<String> getQywxSuiteToken(String suiteId) {
        return Result.newSuccess(qywxManager.getSuitAccessToken(suiteId));
    }
}
