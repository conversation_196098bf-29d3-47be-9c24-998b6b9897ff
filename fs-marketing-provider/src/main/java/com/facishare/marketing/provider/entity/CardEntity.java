package com.facishare.marketing.provider.entity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class CardEntity implements Serializable {
    private String id;

    private String uid;

    private String name;

    private String avatar;

    private String avatarThumbnail;

    // 0:未知 1:男 2:女
    private Integer gender;

    private String phone;

    private String email;

    private String wechat;

    private String introduction;

    private String vocation;

    private String department;

    private String companyName;

    private String companyAddress;

    private String qrUrl;

    private String visualRangeString;

    private Date createTime;

    private Date lastModifyTime;

    private String shareUrl; // 分享名片封面图

    private String exchangeUrl; // 交换名片封面图

    private String avatarPath; // 头像对应的A_Path

    private String sharePath;  // 分享名片封面图A_Path

    private String exchangePath; // 交换名片对应A_Path

    private String tradeCode;  //行业编码

    private String avatarThumbnailPath; //头像缩略图path

    private String cardTemplateId; //模板id
}
