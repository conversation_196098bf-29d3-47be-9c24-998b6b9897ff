package com.facishare.marketing.provider.manager;

import com.facishare.marketing.api.arg.MigrateEasArg;
import com.facishare.marketing.api.result.marketingEventCommonSetting.GetMarketingEventCommonSettingResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.MarketingEventMappingTypeEnum;
import com.facishare.marketing.common.enums.MarketingSceneEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping.MappingDetail;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingEventCommonSettingDAO;
import com.facishare.marketing.provider.entity.MarketingEventCommonSettingEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2021/03/08
 **/
@Slf4j
@Component
public class MarketingEventCommonSettingManager {

    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    public List<String> getBindActivityTypeMappingByType(String ea, Integer type, Boolean needInitIfNull) {
        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
        if (marketingEventCommonSettingEntity == null) {
            if (type.equals(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType()) && needInitIfNull) {
                return Lists.newArrayList(MarketingEventEnum.CONTENT_MARKETING.getEventType(), MarketingEventEnum.MULTIVENUE_MARKETING.getEventType());
            }
            return Lists.newArrayList();
        }
        List<ActivityTypeMapping.ActivityTypeMappingDetail> activityTypeMappingDetailList = marketingEventCommonSettingEntity.getActivityTypeMapping();
        if (CollectionUtils.isEmpty(activityTypeMappingDetailList)) {
            return Lists.newArrayList();
        }
        List<ActivityTypeMapping.MappingDetail> mappingDetailList = Lists.newArrayList();
        for (ActivityTypeMapping.ActivityTypeMappingDetail activityTypeMappingDetail : activityTypeMappingDetailList) {
            if (type.equals(activityTypeMappingDetail.getActivityType())) {
                mappingDetailList = activityTypeMappingDetail.getMapping();
            }
        }
        if (CollectionUtils.isEmpty(mappingDetailList)) {
            return Lists.newArrayList();
        }
        return mappingDetailList.stream().map(MappingDetail::getApiName).collect(Collectors.toList());
    }

    public int getMarketingScene(String ea, String marketingEventId, String marketingActivityId, Integer objectType) {
        if (StringUtils.isBlank(marketingEventId) && StringUtils.isNotBlank(marketingActivityId)) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
            marketingEventId = externalConfigEntity == null ? null : externalConfigEntity.getMarketingEventId();
        }
        if (StringUtils.isBlank(marketingEventId)) {
            // 如果市场活动为空，判断物料类型是否是内容中心里的物料 是则为内容营销
            Set<Integer> marketingObjectTypeSet = Sets.newHashSet(ObjectTypeEnum.PRODUCT.getType(), ObjectTypeEnum.HEXAGON_PAGE.getType(), ObjectTypeEnum.HEXAGON_SITE.getType(),
                    ObjectTypeEnum.ARTICLE.getType(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), ObjectTypeEnum.QR_POSTER.getType(), ObjectTypeEnum.FILE.getType(), ObjectTypeEnum.VIDEO.getType());
            if (objectType != null && marketingObjectTypeSet.contains(objectType)) {
                return MarketingSceneEnum.CONTENT.getCode();
            }
            return MarketingSceneEnum.OTHER.getCode();
        }
        boolean isRelatedAd = adCommonManager.checkMarketingEventIsRelatedAd(ea, marketingEventId);
        if (isRelatedAd) {
            return MarketingSceneEnum.AD.getCode();
        }
        // 如果没传过来 根据市场活动的类型处理：直播营销 --> 市场活动为在线直播 会议营销-->市场活动为会议营销 市场活动为营销通中设置的活动营销
        ObjectData objectData = crmMetadataManager.getById(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (objectData != null && objectData.getString("event_type") != null) {
            String eventType = objectData.getString("event_type");
            if ("live_marketing".equals(eventType)) {
                return MarketingSceneEnum.LIVE.getCode();
            } else if ("3".equals(eventType)) {
                return MarketingSceneEnum.CONFERENCE.getCode();
            } else {
                Result<GetMarketingEventCommonSettingResult> settingResult = marketingEventCommonSettingService.getMarketingEventCommonSetting(null, ea);
                GetMarketingEventCommonSettingResult setting = settingResult.getData();
                if (CollectionUtils.isEmpty(setting.getActivityTypeMapping())) {
                    return MarketingSceneEnum.OTHER.getCode();
                }
                for (ActivityTypeMapping.ActivityTypeMappingDetail activityTypeMappingDetail : setting.getActivityTypeMapping()) {
                    if (activityTypeMappingDetail.getActivityType() != 0) {
                        continue;
                    }
                    if (activityTypeMappingDetail.getMapping().stream().anyMatch(e -> e.getApiName().equals(eventType))) {
                        return MarketingSceneEnum.ACTIVITY.getCode();
                    }
                }
            }
        }
        return MarketingSceneEnum.OTHER.getCode();
    }
}
