/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.typehandlers.value.FileAttachmentContainer;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.eservice.common.utils.SpringContextUtil;
import com.facishare.mankeep.api.result.MemberCheckResult;
import com.facishare.mankeep.api.vo.CustomizeFormDataEnrollVO;
import com.facishare.mankeep.api.vo.CustomizeFormDataShowSettingVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.api.arg.qywx.customizeFormData.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.qywx.customizeFormData.CustomizeFormDataShowSettingArg;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.CustomizeFormDataEnrollResult;
import com.facishare.marketing.api.result.CustomizeFormDataShowSettingResult;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.qywx.CustomizeFormDataService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.MiniAppConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.hexagon.SaveCrmObjectTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.PicContainer;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.WXInfoDecryptUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerData.CreateFsPayOrderResult;
import com.facishare.marketing.provider.manager.ActivityManager;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.pay.FsPayOrderManager;
import com.facishare.marketing.provider.manager.pay.MerchantConfigManager;
import com.facishare.marketing.provider.manager.pay.PayOrderManager;
import com.facishare.marketing.provider.manager.sms.VerificationCodeManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service("qywxCustomizeFormDataServiceImpl")
public class CustomizeFormDataServiceImpl implements CustomizeFormDataService {

    @Autowired
    private com.facishare.mankeep.api.service.CustomizeFormDataService customizeFormDataService;
    @Autowired
    private PayOrderManager payOrderManager;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private MerchantConfigManager merchantConfigManager;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private VerificationCodeManager verificationCodeManager;
    @Autowired
    private MemberConfigDao memberConfigDao;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private WxMiniAppUserMemberBindDao wxMiniAppUserMemberBindDao;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private MemberService memberService;
    @Autowired
    private CampaignPayOrderDao campaignPayOrderDao;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private DingAuthService dingAuthService;
    @Autowired
    private DingManager dingManager;

    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private FsPayOrderManager fsPayOrderManager;
    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Autowired
    private RedisManager redisManager;
    @Value("${host}")
    private String host;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Override
    public Result<CustomizeFormDataEnrollResult> customizeFormDataEnroll(CustomizeFormDataEnrollArg arg) {
        Result result = new Result();
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
        arg.setFsEa(customizeFormDataEntity.getEa());
        //如果有来源营销身份,获取是否是会员
        if (StringUtils.isNotBlank(arg.getFromUserMarketingId())) {
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                    .getBaseInfosByIds(arg.getFsEa(), 1000, Lists.newArrayList(arg.getFromUserMarketingId()), InfoStateEnum.DETAIL);
            UserMarketingAccountData marketingAccountData = userMarketingAccountDataMap.get(arg.getFromUserMarketingId());
            if(marketingAccountData!=null&& CollectionUtils.isNotEmpty(marketingAccountData.getCrmMemberInfos())){
                arg.setSpreadUserIdentifyId(marketingAccountData.getCrmMemberInfos().get(0).getId());
                arg.setSpreadUserType(ChannelEnum.CRM_MEMBER.getType());
            }
        }
        String lockKey = "MARKETING_LOCK_" + customizeFormDataEntity.getEa() + arg.getFormId() + arg.getAppId() + arg.getUid();
        boolean lock = redisManager.lock(lockKey, 120);
        try {
            if (lock) {
                result = doCustomizeFormDataEnroll(arg, customizeFormDataEntity);
            } else {
                return Result.newError(SHErrorCode.USERS_HAVE_REGISTERED);
            }
        } catch (Exception e) {
        } finally {
            redisManager.unLock(lockKey);
        }
        return result;
    }

    private Result doCustomizeFormDataEnroll(CustomizeFormDataEnrollArg arg, CustomizeFormDataEntity customizeFormDataEntity) {
        if (!Strings.isNullOrEmpty(arg.getSubmitContent().getEncryptedPhoneData()) && !Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneIv())) {
            try {
                JSONObject jsonObject = WXInfoDecryptUtil.decryptUserInfo(arg.getSubmitContent().getEncryptedPhoneData(), arg.getSubmitContent().getPhoneIv(), arg.getSessionKey(), arg.getUid());
                arg.getSubmitContent().setPhone(jsonObject.getString("phoneNumber"));
            } catch (Exception e) {
                log.warn("Exception");
                return Result.newSuccess();
            }
            arg.getSubmitContent().setEncryptedPhoneData(null);
            arg.getSubmitContent().setPhoneIv(null);
        } else {
            // TODO 版本兼容问题, 暂时不对小程序进行校验
//            Result checkEnrollField = customizeFormDataManager.checkEnrollField(customizeFormDataEntity, arg.getSubmitContent(), arg.getObjectType(), arg.getObjectId(), arg.getStepFormComponentId());
//            if (!checkEnrollField.isSuccess()) {
//                return checkEnrollField;
//            }
            if (!Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) && !Strings.isNullOrEmpty(arg.getSubmitContent().getPhoneVerifyCode())) {
                boolean phoneVerified = verificationCodeManager.checkSMCode(arg.getSubmitContent().getPhone(), arg.getSubmitContent().getPhoneVerifyCode()).isSuccess();
                if (!phoneVerified) {
                    return Result.newError(SHErrorCode.PHONE_VERIFY_CODE_ERROR);
                }
                arg.getSubmitContent().setPhoneVerifyCode(null);
            }
        }
        Integer spreadFsUid = null;
        if (!arg.getPartner() && StringUtils.isNotBlank(arg.getSpreadFsUid())) {
            spreadFsUid = Integer.parseInt(arg.getSpreadFsUid());
        } else if(!arg.getPartner() && StringUtils.isBlank(arg.getSpreadFsUid()) && StringUtils.isNotBlank(arg.getSpreadUid())) {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getSpreadUid());
            if (fsBindEntity != null && fsBindEntity.getFsEa().equals(customizeFormDataEntity.getEa())) {
                spreadFsUid = fsBindEntity.getFsUserId();
            }
        }
        //单独处理任意对象存入的问题
        if (customizeFormDataEntity != null && Objects.equals(customizeFormDataEntity.getFormMoreSetting().getSaveCrmObjectType(), SaveCrmObjectTypeEnum.OBJ.getType())) {
            ApplicationContext applicationContext = SpringContextUtil.getApplicationContext();
            com.facishare.marketing.api.service.CustomizeFormDataService formDataService = applicationContext.getBean(com.facishare.marketing.api.service.CustomizeFormDataService.class);
            com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg enrollArg = BeanUtil.copy(arg,com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg.class);
            enrollArg.setOpenId(arg.getOpenid());
            enrollArg.setWxAppId(arg.getAppId());
            enrollArg.setSpreadFsUid(spreadFsUid);
            return formDataService.saveWxAppObjectToCrm(enrollArg);
        }
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == arg.getObjectType()){
            com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg copy = BeanUtil.copy(arg, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg.class);
            if (spreadFsUid != null) {
                copy.setSpreadFsUid(spreadFsUid);
            }
            copy.setWxAppId(arg.getAppId());
            Result<Boolean> registerOrLoginResult = memberService.tryWxMiniAppUserRegisterOrLogin(customizeFormDataEntity.getEa(), arg.getUid(), arg.getObjectId(), copy);
            if (BooleanUtils.isTrue(registerOrLoginResult.getData())){
                if (registerOrLoginResult.isSuccess()){
                    CustomizeFormDataEnrollResult formDataEnrollResult = new CustomizeFormDataEnrollResult();
                    Optional<ObjectData> member = memberManager.getMemberByEaAndPhone(customizeFormDataEntity.getEa(), arg.getSubmitContent().getPhone());
                    if (member.isPresent()){
                        ObjectData memberObjectData = member.get();
                        String approvalStatus =(String) memberObjectData.get("approval_status");
                        //历史数据+导入数据审核状态为空默认放行
                        if(Strings.isNullOrEmpty(approvalStatus)){
                            formDataEnrollResult.setMemberApprovalStatus(MemberApprovalStatusEnum.REVIEW_SUCCESS.getType());
                        }else {
                            formDataEnrollResult.setMemberApprovalStatus(Integer.parseInt(approvalStatus));
                        }
                        String memberId = member.get().getId();
                        UserRelationEntity userRelationEntity = userRelationManager.getByMemberId(customizeFormDataEntity.getEa(), memberId);
                        if (userRelationEntity != null) {
                            formDataEnrollResult.setFsUserId(userRelationEntity.getFsUserId());
                        }
                        formDataEnrollResult.setMemberType(memberObjectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName()));
                    }
                    return Result.newSuccess(formDataEnrollResult);
                } else{
                    return Result.newError(registerOrLoginResult.getErrCode(), registerOrLoginResult.getErrMsg());
                }
            }
        }
        String crmMemberId = null;
        if(FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER && !customizeFormDataEntity.getFormMoreSetting().isSyncToMember()){
            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getSubmitContent().getDeviceInfo()), I18nUtil.get(I18nKeyEnum.MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_251));
            Optional<String> optionalWxAppId = wechatAccountManager.getWxAppIdByEa(arg.getFsEa(), MKThirdPlatformConstants.PLATFORM_ID);
            Preconditions.checkArgument(optionalWxAppId.isPresent(), I18nUtil.get(I18nKeyEnum.MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_253));
            Preconditions.checkArgument(!WxAppInfoEnum.isSystemApp(optionalWxAppId.get()), I18nUtil.get(I18nKeyEnum.MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_254));
            Preconditions.checkArgument(optionalWxAppId.get().equals(arg.getAppId()), "营销通的托管小程序与当前身份不匹配");
            Preconditions.checkArgument(!Strings.isNullOrEmpty(merchantConfigManager.getMerchantIdByEa(arg.getFsEa())), I18nUtil.get(I18nKeyEnum.MARK_QYWX_CUSTOMIZEFORMDATASERVICEIMPL_256));
            Result<String> memberResult = fsPayOrderManager.getMemberResult(arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount(), arg.getObjectType(), arg.getObjectId(), null, null, null, null, arg.getUid());
            // 获取会员ID
            if (memberResult.isSuccess() && StringUtils.isNotBlank(memberResult.getData())) {
                crmMemberId = memberResult.getData();
            } else {
                return Result.newError(memberResult.getErrCode(), memberResult.getErrMsg());
            }
        }
        CustomizeFormDataEnrollVO vo = BeanUtil.copy(arg, CustomizeFormDataEnrollVO.class);
        try {
            if (StringUtils.isNotBlank(arg.getSpreadFsUid())) {
                vo.setSpreadFsUid(Integer.valueOf(arg.getSpreadFsUid()));
            }
        } catch (Exception e) {
            log.warn("CustomizeFormDataServiceImpl.customizeFormDataEnroll error arg:{}", arg, e);
        }
        vo.setSubmitContent(BeanUtil.copy(arg.getSubmitContent(), CustomizeFormDataEnroll.class));
        vo.setIpAddr(arg.getIpAddr());
        vo.setUserAgent(arg.getUserAgent());
        vo.setPartner(arg.getPartner());
        if (arg.getSubmitContent() != null && MapUtils.isNotEmpty(arg.getSubmitContent().getPicMap())) {
            Map<String, List<com.facishare.mankeep.common.typehandlers.value.PicContainer>> picContainerMap = Maps.newHashMap();
            for (Map.Entry<String, List<PicContainer>> entry : arg.getSubmitContent().getPicMap().entrySet()) {
                picContainerMap.put(entry.getKey(), BeanUtil.copy(entry.getValue(), com.facishare.mankeep.common.typehandlers.value.PicContainer.class));
            }
            vo.getSubmitContent().setPicMap(picContainerMap);
        }
        if (arg.getSubmitContent() != null && MapUtils.isNotEmpty(arg.getSubmitContent().getFileAttachmentMap())){
            Map<String, List<com.facishare.mankeep.common.typehandlers.value.FileAttachmentContainer>> fileAttachmentMap = Maps.newHashMap();
            for (Map.Entry<String, List<FileAttachmentContainer>> entry : arg.getSubmitContent().getFileAttachmentMap().entrySet()) {
                fileAttachmentMap.put(entry.getKey(), BeanUtil.copy(entry.getValue(), com.facishare.mankeep.common.typehandlers.value.FileAttachmentContainer.class));
            }
            vo.getSubmitContent().setFileAttachmentMap(fileAttachmentMap);
        }
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        vo.setFormQyWxMiniApp(true);
        vo.setEnrollId(arg.getEnrollId());
        com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg copy = BeanUtil.copy(arg, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg.class);
        if (spreadFsUid != null) {
            copy.setSpreadFsUid(spreadFsUid);
        }
        // 特殊数据校验
        if (StringUtils.isBlank(arg.getMarketingEventId()) && StringUtils.isNotBlank(arg.getMarketingActivityId())) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getMarketingActivityId());
            if (externalConfigEntity != null) {
                // arg以前是不填充市场活动ID的，但是营销推广来源需要
                copy.setMarketingEventId(externalConfigEntity.getMarketingEventId());
            }
        }
        CustomizeFormDataUserEntity customizeFormDataUserEntity = BeanUtil.copy(arg, CustomizeFormDataUserEntity.class);
        copy.setSourceType(customizeFormDataManager.getEnrollType(customizeFormDataUserEntity));
        copy.setChannelValue(customizeFormDataManager.getSystemPromotionChannelType(customizeFormDataUserEntity));
        copy.setEa(customizeFormDataEntity.getEa());
        // 创建营销推广来源
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(copy);
        if (vo.getSubmitContent() != null) {
            vo.getSubmitContent().setMarketingPromotionSourceId(marketingPromotionSourceId);
        }
        ModelResult<com.facishare.mankeep.api.result.CustomizeFormDataEnrollResult> customizeFormDataEnrollResult = customizeFormDataService.customizeFormDataEnroll(vo);
        try {
            String ea = objectManager.getObjectEa(vo.getObjectId(), vo.getObjectType());
            if (!StringUtils.isBlank(ea)) {
                customizeFormDataManager.sendSaveClueFailMessage(ea, vo.getFormId(), vo.getMarketingEventId(), false);
            }
        } catch (Exception e) {
            log.warn("CustomizeFormDataServiceImpl.customizeFormDataEnroll sendSaveClueFailMessage arg:{}", arg, e);
        }
        if (!customizeFormDataEnrollResult.isSuccess()) {
            com.facishare.mankeep.api.result.CustomizeFormDataEnrollResult rst = customizeFormDataEnrollResult.getData();
            CustomizeFormDataEnrollResult data = new CustomizeFormDataEnrollResult();
            if (29504 == customizeFormDataEnrollResult.getErrCode()) {
                data.setEnrollId(rst.getEnrollId());
//                ThreadPoolUtils.execute(() -> {
//                    Preconditions.checkArgument(!Strings.isNullOrEmpty(customizeFormDataEntity.getEa()));
//                    Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getUid()));
//                    AssociationArg associationArg = new AssociationArg();
//                    associationArg.setEa(customizeFormDataEntity.getEa());
//                    associationArg.setPhone(arg.getSubmitContent().getPhone());
//                    associationArg.setAssociationId(arg.getUid());
//                    associationArg.setType(ChannelEnum.MINIAPP.getType());
//                    try {
//                        userMarketingAccountAssociationManager.associate(associationArg);
//                    } catch (Exception e) {
//                        log.warn("CustomizeFormDataServiceImpl.noIdentityFormDataEnroll error arg:{}", JSON.toJSONString(associationArg), e);
//                    }
//                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
            return Result.newError(customizeFormDataEnrollResult.getErrCode(), customizeFormDataEnrollResult.getErrMsg(), data);
        }
//        ThreadPoolUtils.execute(() -> {
//            CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(customizeFormDataEnrollResult.getData().getEnrollId());
//            if (SaveCrmStatusEnum.LINKED.getValue().equals(customizeFormDataUserEntity.getSaveCrmStatus()) && null != customizeFormDataUserEntity.getSubmitContent()) {
//                customizeFormDataManager.associateMarketingUserId(customizeFormDataEntity, customizeFormDataUserEntity);
//            }
//        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        CustomizeFormDataEnrollResult result = BeanUtil.copy(customizeFormDataEnrollResult.getData(), CustomizeFormDataEnrollResult.class);
        if(FormDataUsage.getByType(customizeFormDataEntity.getFormUsage()) == FormDataUsage.COLLECT_ORDER){
            Result<CreateFsPayOrderResult> createPayOrderResult = fsPayOrderManager.createMiniAppFsPayOrder(
                    customizeFormDataEntity.getEa(), arg.getMarketingEventId(), customizeFormDataEntity.getId(), arg.getObjectType(), arg.getObjectId(),
                    Integer.valueOf(customizeFormDataEntity.getCreateBy()), crmMemberId, arg.getOpenid(), arg.getAppId(), arg.getSubmitContent().getGoodsName(), arg.getSubmitContent().getAmount());
            if (!createPayOrderResult.isSuccess()) {
                return Result.newError(createPayOrderResult.getErrCode(), createPayOrderResult.getErrMsg());
            }
            CreateFsPayOrderResult data = createPayOrderResult.getData();
            BeanUtils.copyProperties(data, result);
            customizeFormDataUserDAO.updateCustomizeFormDataUserOrderIdById(result.getEnrollId(), data.getOrderNo());
        }
        //查询是否是会议营销,并处理审核状态
        customizeFormDataManager.checkConferenceEnrollReview(customizeFormDataEntity.getEa(),arg.getMarketingEventId(),result);
        // 发送企业微信审核通知
        if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
            ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(arg.getMarketingEventId(), customizeFormDataEntity.getEa());
            if (activityEntity != null) {
                if(dingManager.isDingAddressbook(customizeFormDataEntity.getEa())){
                    ThreadPoolUtils.execute(() -> {
                        String activityId = activityManager.getActivityIdByObject(arg.getObjectId(),  arg.getObjectType(), activityEntity.getEa(),  activityEntity.getMarketingEventId());
                        conferenceManager.sendDingdingConferenceEnrollNoticeRealTime(activityId);
                    }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
                }else{
                    ThreadPoolUtils.execute(() -> {
                        String activityId = activityManager.getActivityIdByObject(arg.getObjectId(), arg.getObjectType(), activityEntity.getEa(), activityEntity.getMarketingEventId());
                        conferenceManager.sendQywxConferenceEnrollNoticeRealTime(activityId);
                    }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }
            }
        }
        return Result.newSuccess(result);
    }
    @Override
    public Result<CustomizeFormDataShowSettingResult> customizeFormDataShowSetting(CustomizeFormDataShowSettingArg arg) {
        CustomizeFormDataShowSettingVO vo = BeanUtil.copy(arg, CustomizeFormDataShowSettingVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        ModelResult<com.facishare.mankeep.api.result.CustomizeFormDataShowSettingResult> customizeFormDataShowSettingResult = customizeFormDataService.customizeFormDataShowSetting(vo);
        if (!customizeFormDataShowSettingResult.isSuccess()) {
            return Result.newError(customizeFormDataShowSettingResult.getErrCode(), customizeFormDataShowSettingResult.getErrMsg());
        }
        CustomizeFormDataShowSettingResult result = BeanUtil.copy(customizeFormDataShowSettingResult.getData(), CustomizeFormDataShowSettingResult.class);
        customizeFormDataManager.checkShowSettingConferenceEnrollReview(ea,arg.getMarketingEventId(),arg.getObjectId(),arg.getObjectType(),
                arg.getFormId(),null,null,null,arg.getUid(),result);
        MemberCheckResult memberCheckResult = customizeFormDataShowSettingResult.getData().getMemberCheckResult();
        com.facishare.marketing.api.result.MemberCheckResult checkResult = BeanUtil.copy(memberCheckResult, com.facishare.marketing.api.result.MemberCheckResult.class);
        result.setMemberCheckResult(checkResult);
        if (checkResult != null && StringUtils.isNotBlank(checkResult.getId())) {
            // 查询会员信息
            ObjectData memberObjectData = memberManager.getDetail(checkResult.getId(), ea);
            checkResult.setMemberInfo(memberObjectData);
        }
        // 查询会员信息自动填写到表单映射关系
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
        if (customizeFormDataEntity != null) {
            result.setMemberToFormMapping(customizeFormDataEntity.getMemberToFormMapping());
        }
        return Result.newSuccess(result);
    }
}