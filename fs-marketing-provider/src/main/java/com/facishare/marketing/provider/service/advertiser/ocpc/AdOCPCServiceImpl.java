package com.facishare.marketing.provider.service.advertiser.ocpc;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.advertiser.*;
import com.facishare.marketing.api.result.advertiser.AdOCPCBeforeAfterDataReportResult;
import com.facishare.marketing.api.result.advertiser.AdOCPCUploadRuleResult;
import com.facishare.marketing.api.result.advertiser.GetVirtualPhoneResult;
import com.facishare.marketing.api.result.ocpc.AdOCPCConfigResult;
import com.facishare.marketing.api.service.advertiser.ocpc.AdOCPCService;
import com.facishare.marketing.common.enums.advertiser.AdOCPCStatusEnum;
import com.facishare.marketing.common.enums.advertiser.BaiduConversionTypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.baidu.BaiduHttpManager;
import com.facishare.marketing.provider.baidu.GetBaiduVirtualPhoneBody;
import com.facishare.marketing.provider.baidu.GetBaiduVirtualPhoneResult;
import com.facishare.marketing.provider.baidu.RequestResult;
import com.facishare.marketing.provider.bo.advertise.AdConvertCostStatisticsBO;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignDataDAO;
import com.facishare.marketing.provider.entity.EnterpriseMetaConfigEntity;
import com.facishare.marketing.provider.entity.advertiser.ocpc.AdOCPCConfigEntity;
import com.facishare.marketing.provider.entity.advertiser.ocpc.AdOCPCUploadRuleEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignEntity;
import com.facishare.marketing.provider.innerResult.BaiduUploadConvertResult;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCConfigManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("adOCPCService")
public class AdOCPCServiceImpl implements AdOCPCService {

    @Autowired
    private AdOCPCConfigManager adOCPCConfigManager;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private BaiduHttpManager baiduHttpManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MetadataActionService metadataActionService;

    @Autowired
    private EIEAConverter eIEAConverter;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private BaiduCampaignDataDAO baiduCampaignDataDAO;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> saveOCPCConfig(AdOCPCConfigArg adOCPCConfigArg) {
        if (StringUtils.isBlank(adOCPCConfigArg.getAdAccountId()) || StringUtils.isBlank(adOCPCConfigArg.getToken())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String id = adOCPCConfigManager.saveOCPCConfig(adOCPCConfigArg);
        AdOCPCConfigEntity adOCPCConfigEntity = adOCPCConfigManager.getById(adOCPCConfigArg.getEa(), id);
        return Result.newSuccess(adOCPCConfigEntity.getJointDebuggingLandingUrl());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<AdOCPCConfigResult>> queryOCPCConfig(AdOCPCConfigQueryArg queryArg) {
        if (StringUtils.isBlank(queryArg.getAdAccountSource())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String source = AdSourceEnum.getSourceByValue(queryArg.getAdAccountSource());
        if (StringUtils.isBlank(source)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEaAndSource(queryArg.getEa(), null, source, true);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return Result.newSuccess();
        }

        List<String> adAccountIdList = adAccountEntityList.stream().map(AdAccountEntity::getId).collect(Collectors.toList());
        List<AdOCPCConfigEntity> adOCPCConfigEntityList = adOCPCConfigManager.getByAdAccountIdList(queryArg.getEa(), adAccountIdList);

        Map<String, AdOCPCConfigEntity> accountIdToConfigMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adOCPCConfigEntityList)) {
            accountIdToConfigMap = adOCPCConfigEntityList.stream().collect(Collectors.toMap(AdOCPCConfigEntity::getAdAccountId, Function.identity(), (v1, v2) -> v1));
        }
        List<AdOCPCConfigResult> resultList = Lists.newArrayList();

        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            AdOCPCConfigEntity configEntity = accountIdToConfigMap.get(adAccountEntity.getId());
            boolean isConfigExist = configEntity != null;
            AdOCPCConfigResult result = new AdOCPCConfigResult();
            result.setId(isConfigExist ? configEntity.getId() : null);
            result.setAdAccountName(adAccountEntity.getUsername());
            result.setStatus(isConfigExist ? configEntity.getStatus() : AdOCPCStatusEnum.STOP.getStatus());
            result.setJointDebuggingLandingUrl(isConfigExist ? configEntity.getJointDebuggingLandingUrl() : null);
            result.setToken(isConfigExist ? configEntity.getToken() : null);
            result.setAdAccountSource(adAccountEntity.getSource());
            result.setAdAccountId(adAccountEntity.getId());
            result.setInvalidRebateStatus(isConfigExist ? configEntity.getInvalidRebateStatus() : AdOCPCStatusEnum.STOP.getStatus());
            resultList.add(result);
        }
        return Result.newSuccess(resultList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateStatus(AdOCPCConfigUpdateArg adOCPCConfigUpdateArg) {
        if (adOCPCConfigUpdateArg == null || (adOCPCConfigUpdateArg.getStatus() == null && adOCPCConfigUpdateArg.getInvalidRebateStatus() == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AdOCPCConfigEntity entity = adOCPCConfigManager.getById(adOCPCConfigUpdateArg.getEa(), adOCPCConfigUpdateArg.getId());
        if (entity == null) {
            return Result.newError(SHErrorCode.OCPC_CONFIG_NOT_EXIST);
        }
        adOCPCConfigManager.updateStatus(adOCPCConfigUpdateArg);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> jointDebugging(JointDebuggingArg arg) {
        if (StringUtils.isBlank(arg.getEa()) || StringUtils.isBlank(arg.getAdAccountId())
                || StringUtils.isBlank(arg.getLandingUrl()) || arg.getConversionType() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(arg.getAdAccountId());
        if (adAccountEntity == null) {
            return Result.newError(SHErrorCode.AD_ACCOUNT_NOT_FOUND);
        }
        if (!StringUtils.equals(arg.getEa(), adAccountEntity.getEa())) {
            return Result.newError(SHErrorCode.AD_ACCOUNT_EA_NOT_EQUAL);
        }
        AdOCPCConfigEntity adOCPCConfigEntity = adOCPCConfigManager.getByAdAccountId(arg.getEa(), arg.getAdAccountId());
        if (adOCPCConfigEntity == null) {
            return Result.newError(SHErrorCode.AD_OCPC_CONFIG_NOT_FOUND);
        }
        if (StringUtils.isBlank(adOCPCConfigEntity.getToken())) {
            return Result.newError(SHErrorCode.AD_OCPC_TOKEN_NOT_FOUND);
        }
        if (!arg.getLandingUrl().contains("bd_vid=")) {
            log.error("回传百度落地URL不包含bd_vid, url:[{}]", arg.getLandingUrl());
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        BaiduUploadConvertResult result = baiduHttpManager.sendAdDataToBaidu(adOCPCConfigEntity.getToken(), arg.getLandingUrl(), Collections.singletonList(arg.getConversionType()));
        if (result == null) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (result.getStatus() == -1) {
            return Result.newError(result.getStatus(), result.getDesc());
        }
        if (result.getStatus() == 0) {
            return Result.newSuccess();
        }
        if (result.getStatus() == 1 || result.getStatus() == 2) {
            BaiduUploadConvertResult.ErrorResult errorResult = result.getErrors().get(0);
            String error = errorResult.getMessage();
            return Result.newError(-1, error);
        }
        if (result.getStatus() == 3) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_210));
        }
        if (result.getStatus() == 4) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_213));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> saveUploadRule(OCPCUploadRuleCreateArg createArg) {
        return adOCPCUploadManager.saveUploadRule(createArg);
    }

    @Override
    public Result<AdOCPCUploadRuleResult> getUploadRule(String ea, String adAccountSource, Integer conversionType) {
        if (StringUtils.isBlank(adAccountSource)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String source = AdSourceEnum.getSourceByValue(adAccountSource);
        if (StringUtils.isBlank(source)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<AdOCPCUploadRuleEntity> entityList = adOCPCUploadManager.getByEaAndAdAccountSourceAndConvertType(ea, source, conversionType);

        AdOCPCUploadRuleResult result = new AdOCPCUploadRuleResult();
        result.setConversionType(conversionType);
        result.setAdAccountSource(adAccountSource);
        List<AdOCPCUploadRuleResult.OCPCUploadRuleDetailResult> ruleDetailList = Lists.newArrayList();
        for (AdOCPCUploadRuleEntity ruleEntity : entityList) {
            AdOCPCUploadRuleResult.OCPCUploadRuleDetailResult detailResult = new AdOCPCUploadRuleResult.OCPCUploadRuleDetailResult();
            detailResult.setId(ruleEntity.getId());
            detailResult.setTargetObjApiName(ruleEntity.getTargetObjApiName());
            detailResult.setRules(ruleEntity.getRules());
            ruleDetailList.add(detailResult);
        }
        result.setRuleDetailList(ruleDetailList);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> reUploadAdData(String ea, ReUploadAdDataArg reUploadAdDataArg) {
        List<String> idList = reUploadAdDataArg.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        // 查询广告回传明细数据
        List<String> selectFields = Lists.newArrayList("_id", "ad_platform", "convent_type", "ad_account_id", "landing_page", "status");
        List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, -10000, CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName(), selectFields, idList);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_259));
        }
        objectDataList = objectDataList.stream().filter(e -> e.get("status") != null && !"success".equals(e.get("status").toString()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Result.newSuccess();
        }
        // 查询落地页对象数据
        selectFields = Lists.newArrayList("_id", "landing_page_url");
        List<String> landingObjIdList = objectDataList.stream().map(e -> e.get("landing_page").toString()).collect(Collectors.toList());
        List<ObjectData> landingObjectDataList = crmMetadataManager.batchGetByIdsV3(ea, -10000, CrmObjectApiNameEnum.LANDING_PAGE_OBJ.getName(), selectFields, landingObjIdList);
        if (CollectionUtils.isEmpty(landingObjectDataList)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_271));
        }
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryEnableAccountByEa(ea);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_275));
        }
        List<String> adAccountIdList = adAccountEntityList.stream().map(AdAccountEntity::getId).collect(Collectors.toList());
        List<AdOCPCConfigEntity> configEntityList = adOCPCConfigManager.getByAdAccountIdList(ea, adAccountIdList);
        if (CollectionUtils.isEmpty(configEntityList)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_280));
        }
        Map<String, ObjectData> landingIdToObjMap = landingObjectDataList.stream().collect(Collectors.toMap(ObjectData::getId, e -> e, (v1, v2) -> v1));
        Map<String, AdAccountEntity> adAccountEntityMap = adAccountEntityList.stream().collect(Collectors.toMap(e -> e.getSource() + e.getUsername(), e -> e, (v1, v2) -> v1));
        Map<String, AdOCPCConfigEntity> accountIdToConfigmap = configEntityList.stream().filter(e -> e.getStatus() == 1).collect(Collectors.toMap(AdOCPCConfigEntity::getAdAccountId, e -> e, (v1, v2) -> v1));
        int tenantId = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(tenantId, -10000);
        for (ObjectData objectData : objectDataList) {
            String adPlatform = objectData.get("ad_platform").toString();
            AdSourceEnum adSourceEnum = AdSourceEnum.getBySourceCode(adPlatform);
            if (adSourceEnum == null) {
                continue;
            }
            String adUserName = objectData.get("ad_account_id").toString();
            AdAccountEntity adAccountEntity = adAccountEntityMap.get(adSourceEnum.getSource() + adUserName);
            if (adAccountEntity == null) {
                continue;
            }
            AdOCPCConfigEntity adOCPCConfigEntity = accountIdToConfigmap.get(adAccountEntity.getId());
            if (adOCPCConfigEntity == null) {
                continue;
            }
            String landingId = objectData.get("landing_page").toString();
            ObjectData landingObj = landingIdToObjMap.get(landingId);
            String landingPageUrl = landingObj.get("landing_page_url").toString();
            String convertType = objectData.get("convent_type").toString();
            Integer conversionType = BaiduConversionTypeEnum.getConversionTypeCodeByOptionCode(Integer.parseInt(convertType));
            BaiduUploadConvertResult result = baiduHttpManager.sendAdDataToBaidu(adOCPCConfigEntity.getToken(), landingPageUrl, Lists.newArrayList(conversionType));
            log.info("上传百度平台,ea:[{}], id:[{}], result:[{}]", ea, objectData.getId(), result);
            ObjectData updateObjectData = new ObjectData();
            updateObjectData.put("_id", objectData.getId());
            if (result.getStatus() == 0) {
                updateObjectData.put("status", "success");
            } else if (result.getStatus() == 1 || result.getStatus() == 2) {
                updateObjectData.put("status", "failure");
                String errorMsg = I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8);
                if (CollectionUtils.isNotEmpty(result.getErrors())) {
                    errorMsg = result.getErrors().get(0).getMessage();
                }
                updateObjectData.put("remark", errorMsg);
            } else {
                updateObjectData.put("status", "failure");
                updateObjectData.put("remark", result.getDesc());
            }
            ActionEditArg arg = new ActionEditArg();
            arg.setObjectData(updateObjectData);
            metadataActionService.edit(headerObj, CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName(), true, true, arg);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<GetVirtualPhoneResult> getVirtualPhone(GetVirtualPhoneArg getVirtualPhoneArg) {
        if (StringUtils.isBlank(getVirtualPhoneArg.getLeadId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = getVirtualPhoneArg.getEa();
        Integer fsUserId = getVirtualPhoneArg.getFsUserId();
        ObjectData leadObjectData = crmV2Manager.getDetail(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(), getVirtualPhoneArg.getLeadId());
        if (leadObjectData == null) {
            return Result.newError(SHErrorCode.CRM_LEAD_NOT_FOUND);
        }
        // 根据市场活动来查询该线索是通过那个广告账号过来的
        String marketingEventId = leadObjectData.getString("marketing_event_id");
        if (StringUtils.isBlank(marketingEventId)) {
            return Result.newError(SHErrorCode.LEAD_NOT_RELATED_MARKETING_EVENT);
        }
        // 根据营销推广的外部平台id就是广告平台的线索ID
        String marketingPromotionSourceId = leadObjectData.getString("marketing_promotion_source_id");
        if (StringUtils.isBlank(marketingPromotionSourceId)) {
            return Result.newError(SHErrorCode.LEAD_NOT_RELATED_MARKETING_PROMOTION_SOURCE);
        }
        // 当前无效返款只有百度基木鱼的无效返款，这里只要查询百度的就行，如果后续还要接入其他平台，这里都得要查询，为了获取广告账户id
        BaiduCampaignEntity baiduCampaignEntity = baiduCampaignDAO.queryCampaignByMarketingEventId(ea, marketingEventId);
        if (baiduCampaignEntity == null) {
            return Result.newError(SHErrorCode.MARKETING_EVENT_NOT_RELATED_CAMPAIGN);
        }
        String adAccountId = baiduCampaignEntity.getAdAccountId();
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(adAccountId);
        if (adAccountEntity == null) {
            return Result.newError(SHErrorCode.AD_ACCOUNT_NOT_ENABLE);
        }
        // 查询该广告账户是否开启ocpc和是否开启无效返款，必须两个都开启无效返款才会生效
        AdOCPCConfigEntity adOCPCConfigEntity = adOCPCConfigManager.getByAdAccountId(ea, adAccountId);
        if (adOCPCConfigEntity == null) {
            return Result.newError(SHErrorCode.AD_OCPC_CONFIG_NOT_FOUND);
        }
        if (adOCPCConfigEntity.getStatus() == null || adOCPCConfigEntity.getStatus() == AdOCPCStatusEnum.STOP.getStatus()) {
            return Result.newError(SHErrorCode.AD_OCPC_NOT_ENABLE);
        }
        if (adOCPCConfigEntity.getInvalidRebateStatus() == null || adOCPCConfigEntity.getInvalidRebateStatus() == AdOCPCStatusEnum.STOP.getStatus()) {
            return Result.newError(SHErrorCode.AD_INVALID_REBATE_NOT_ENABLE);
        }

        ObjectData marketingPromotionSourceData = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_PROMOTION_SOURCE_OBJ.getName(), marketingPromotionSourceId);
        String landingUrl = marketingPromotionSourceData.getString("landing_url");
        // 百度ocpc的落地页才有bd_vid(基木鱼的要在百度广告后台把bd_vid这个字段配置上才会有)  其他平台的的落地页是不一样的，目前只处理百度
        if (StringUtils.isBlank(landingUrl) || !landingUrl.contains("bd_vid")) {
            return Result.newError(SHErrorCode.LEAD_LANDING_URL_NOT_CONTAIN_BD_VID);
        }
        // 广告平台的线索id
        String thirdPlatformDataId = marketingPromotionSourceData.getString("third_platform_data_id");
        if (StringUtils.isBlank(thirdPlatformDataId)) {
            return Result.newError(SHErrorCode.AD_THIRD_PLATFORM_ID_IS_NULL);
        }
        String mobile = getVirtualPhoneArg.getMobile();
        // 线索的号码
        if (StringUtils.isBlank(mobile)) {
            mobile = leadObjectData.getString("mobile");
        }
        if (StringUtils.isBlank(mobile)) {
            mobile = leadObjectData.getString("tel");
        }
        if (StringUtils.isBlank(mobile)) {
            return Result.newError(SHErrorCode.LEAD_MOBILE_IS_NULL);
        }
        if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(adAccountEntity.getSource())) {
            GetBaiduVirtualPhoneBody getBaiduVirtualPhoneBody = new GetBaiduVirtualPhoneBody();
            getBaiduVirtualPhoneBody.setCallee(mobile);
            getBaiduVirtualPhoneBody.setRefClueId(thirdPlatformDataId);
            RequestResult<GetBaiduVirtualPhoneResult> getBaiduVirtualPhoneResult = baiduHttpManager.getVirtualPhone(adAccountEntity, getBaiduVirtualPhoneBody);
            log.info("获取百度虚拟中间号,ea: {} arg: {} result: {}", ea, getBaiduVirtualPhoneBody, getBaiduVirtualPhoneResult);
            List<GetBaiduVirtualPhoneResult> baiduVirtualPhoneResultList = getBaiduVirtualPhoneResult.getData();
            if (CollectionUtils.isEmpty(baiduVirtualPhoneResultList)) {
                return Result.newError(SHErrorCode.GET_VIRTUAL_PHONE_ERROR);
            }
            GetBaiduVirtualPhoneResult baiduVirtualPhoneResult = baiduVirtualPhoneResultList.get(0);
            if (baiduVirtualPhoneResult.getResCode() != 101) {
                return Result.newError(baiduVirtualPhoneResult.getResCode(), baiduVirtualPhoneResult.getResMessage());
            }
            GetVirtualPhoneResult result = new GetVirtualPhoneResult();
            result.setExpireTime(baiduVirtualPhoneResult.getExpireTime());
            result.setRealPhone(baiduVirtualPhoneResult.getRealPhone());
            result.setVirtualPhone(baiduVirtualPhoneResult.getVirtualPhone());
            return Result.newSuccess(result);
        }
        return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCSERVICEIMPL_416));
    }

    @Override
    public Result<Boolean> isOpenInvalidRebate(Integer tenantId, Integer userId) {
        if (tenantId == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        log.info("isOpenMarketingAdPlugin tenantId: {}", tenantId);
        try {
            String ea = eieaConverter.enterpriseIdToAccount(tenantId);
            EnterpriseMetaConfigEntity metaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
            if (metaConfigEntity == null) {
                return Result.newSuccess(false);
            }
            List<AdOCPCConfigEntity> configEntityList = adOCPCConfigManager.getByEaAndInvalidRebateStatus(ea, AdOCPCStatusEnum.NORMAL.getStatus());
            if (CollectionUtils.isEmpty(configEntityList)) {
                return Result.newSuccess(false);
            }
            return Result.newSuccess(true);
        } catch (Exception e) {
            log.error("isOpenMarketingAdPlugin error, tenantId: {}", tenantId);
        }
        return Result.newSuccess(false);
    }

    /**
     * 以最早的回传成功的回传时间为基准
     * 注意：就算不开启ocpc的推广计划，百度那边也是有转化量的
     * 使用ocpc前的转化成本数据： 查询基准时间开始过去六个月的 平均转化成本数据
     * 使用ocpc后的转化成本数据： 查询当前时间开始过去六个月的 平均转化成本数据
     */
    @Override
    public Result<AdOCPCBeforeAfterDataReportResult> beforeAfterDataReport(AdOCPCBeforeAfterDataReportArg arg) {
        String ea = arg.getEa();
        int ei = eIEAConverter.enterpriseAccountToId(ea);
        HeaderObj headerObj = new HeaderObj(ei, SuperUserConstants.USER_ID);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = objectDescribeService.getDescribe(headerObj, CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName());
        if (describeResult == null || !describeResult.isSuccess() && describeResult.getData() == null) {
            return Result.newError(SHErrorCode.AD_RETURN_OBJ_NOT_FOUND);
        }
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.AD_DATA_RETURN_DETAIL_OBJ.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "send_back_time"));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("status", Filter.OperatorContants.EQ, Lists.newArrayList("success"));
        paasQueryArg.addOrderByAsc("send_back_time", true);
        queryFilterArg.setQuery(paasQueryArg);
        InnerPage<ObjectData> adReturnObjectDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, 1, 1);
        // 如果不存在广告回传数据，直接返回空
        if (adReturnObjectDataPage == null || CollectionUtils.isEmpty(adReturnObjectDataPage.getDataList())) {
            return Result.newSuccess(new AdOCPCBeforeAfterDataReportResult());
        }
        List<Long> campaignIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(arg.getMarketingEventIdList())) {
            List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryByMarketingEventIdList(ea, arg.getMarketingEventIdList());
            if (CollectionUtils.isEmpty(baiduCampaignEntityList)) {
                return Result.newError(SHErrorCode.MARKETING_EVENT_NOT_RELATED_CAMPAIGN);
            }
            baiduCampaignEntityList.stream().map(BaiduCampaignEntity::getCampaignId).forEach(campaignIdList::add);
        } else {
            List<String> marketingEventId = getAllOCPCMarketingEventId(ea);
            if (CollectionUtils.isNotEmpty(marketingEventId)) {
                List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryByMarketingEventIdList(ea, marketingEventId);
                baiduCampaignEntityList.stream().map(BaiduCampaignEntity::getCampaignId).forEach(campaignIdList::add);
            }
        }
        long earliestSendBackTime = adReturnObjectDataPage.getDataList().get(0).getLong("send_back_time");
        // 最早的广告回传时间
        Date earliestSendBackDate = DateUtil.getMorningDate(new Date(earliestSendBackTime));
        // 最早的广告回传前六个月
        Date beforeBeginTime = DateUtil.plusMonth(earliestSendBackDate, -6);
        Date beforeEndTime = DateUtil.minusDay(earliestSendBackDate, 1);
        // 现在的时间
        Date afterEndTime = DateUtil.getMorningDate(new Date());
        // 过去六个月的时间
        Date afterBeginTime = DateUtil.plusMonth(afterEndTime, -6);
        // 如果当前时间 - 六个月的时间比最早广告回传时间还要早，直接用基准时间
        if (afterBeginTime.before(earliestSendBackDate)) {
            afterBeginTime = earliestSendBackDate;
        }
        // 查询最早广告回传时间过去六个月的转化成本
        List<AdConvertCostStatisticsBO> beforeConvertCostStatisticsList = baiduCampaignDataDAO.statisticsConvertCostData(ea, beforeBeginTime, beforeEndTime, Lists.newArrayList());
        // 查询当前时间过去六个月的转化成本
        List<AdConvertCostStatisticsBO> afterConvertCostStatisticsList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            afterConvertCostStatisticsList = baiduCampaignDataDAO.statisticsConvertCostData(ea, afterBeginTime, afterEndTime, campaignIdList);
        }
        // 组装结果
        AdOCPCBeforeAfterDataReportResult result = new AdOCPCBeforeAfterDataReportResult();
        result.setBeforeList(buildReportItem(beforeConvertCostStatisticsList));
        result.setAfterList(buildReportItem(afterConvertCostStatisticsList));
        result.setAdReturnBeginTime(DateUtil.format2(earliestSendBackDate));
        result.setBeforeAverageCoverageCost(getAverageConvertCost(beforeConvertCostStatisticsList));
        result.setAfterAverageCoverageCost(getAverageConvertCost(afterConvertCostStatisticsList));
        return Result.newSuccess(result);
    }

    private BigDecimal getAverageConvertCost(List<AdConvertCostStatisticsBO> convertCostStatisticsList) {
        if (CollectionUtils.isEmpty(convertCostStatisticsList)) {
            return BigDecimal.ZERO;
        }
        return convertCostStatisticsList.stream().map(AdConvertCostStatisticsBO::getConvertCost).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(convertCostStatisticsList.size()), 2, RoundingMode.HALF_UP);
    }

    private List<AdOCPCBeforeAfterDataReportResult.Item> buildReportItem(List<AdConvertCostStatisticsBO> afterConvertCostStatisticsList) {
        List<AdOCPCBeforeAfterDataReportResult.Item> list = Lists.newArrayList();
        for (AdConvertCostStatisticsBO adConvertCostStatisticsBO : afterConvertCostStatisticsList) {
            AdOCPCBeforeAfterDataReportResult.Item item = new AdOCPCBeforeAfterDataReportResult.Item();
            item.setDate(DateUtil.format2(adConvertCostStatisticsBO.getActionDate()));
            item.setConvertCost(adConvertCostStatisticsBO.getConvertCost().setScale(2, RoundingMode.HALF_UP));
            list.add(item);
        }
        return list;
    }

    public List<String> getAllOCPCMarketingEventId(String ea) {
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("ocpc_launch", Filter.OperatorContants.EQ, Lists.newArrayList("yes"));
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), null, paasQueryArg);
        return objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
    }
}