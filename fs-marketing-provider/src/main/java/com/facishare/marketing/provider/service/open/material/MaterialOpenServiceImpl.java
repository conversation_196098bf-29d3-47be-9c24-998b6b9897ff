package com.facishare.marketing.provider.service.open.material;

import com.facishare.marketing.api.arg.AddProductArg;
import com.facishare.marketing.api.arg.ListProductArg;
import com.facishare.marketing.api.arg.open.material.*;
import com.facishare.marketing.api.result.AddEnterpriseProductResult;
import com.facishare.marketing.api.result.ProductListResult;
import com.facishare.marketing.api.result.QueryProductDetailResult;
import com.facishare.marketing.api.result.open.material.*;
import com.facishare.marketing.api.service.ProductService;
import com.facishare.marketing.api.service.open.material.MaterialOpenService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.VideoDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.entity.VideoEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("materialOpenService")
public class MaterialOpenServiceImpl implements MaterialOpenService {

    @Autowired
    private ProductService productService;

    @Autowired
    private VideoDAO videoDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private ProductDAO productDAO;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Override
    public Result<CreateUpdateMaterialResult> createMaterial(String ea, Integer fsUserId, CreateMaterialArg arg) {
        Integer materialType = arg.getMaterialType();
        if (materialType == null) {
            return Result.newError(SHErrorCode.MATERIAL_TYPE_CANT_NULL);
        }
        log.info("createMaterial, ea:[{}], userId:[{}], arg: [{}]", ea, fsUserId, arg);
        if (materialType == 1) {
            CreateProductArg createProductArg = arg.getCreateProductArg();
            if (createProductArg == null) {
                return Result.newError(SHErrorCode.PRODUCT_ARG_CANT_NULL);
            }
            CreateOrUpdateProductArg createOrUpdateProductArg  =
                    BeanUtil.copyProperties(createProductArg, CreateOrUpdateProductArg.class);
            // 创建产品
            return createOrUpdateProduct(ea, fsUserId, createOrUpdateProductArg);
        }
        return Result.newError(SHErrorCode.UNKNOWN_MATERIAL_TYPE);
    }

    @Override
    public Result<CreateUpdateMaterialResult> updateMaterial(String ea, Integer fsUserId, UpdateMaterialArg arg) {
        Integer materialType = arg.getMaterialType();
        if (materialType == null) {
            return Result.newError(SHErrorCode.MATERIAL_TYPE_CANT_NULL);
        }
        log.info("updateMaterial, ea:[{}], userId:[{}], arg: [{}]", ea, fsUserId, arg);
        if (materialType == 1) {
            UpdateProductArg updateProductArg = arg.getUpdateProductArg();
            if (updateProductArg == null) {
                return Result.newError(SHErrorCode.PRODUCT_ARG_CANT_NULL);
            }
            CreateOrUpdateProductArg createOrUpdateProductArg  =
                    BeanUtil.copyProperties(updateProductArg, CreateOrUpdateProductArg.class);
            // 更新产品
            return createOrUpdateProduct(ea, fsUserId, createOrUpdateProductArg);
        }
        return Result.newError(SHErrorCode.UNKNOWN_MATERIAL_TYPE);
    }


    @Override
    public Result<Void> deleteMaterial(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        Integer materialType = arg.getMaterialType();
        if (materialType == null) {
            return Result.newError(SHErrorCode.MATERIAL_TYPE_CANT_NULL);
        }
        List<String> materialIdList = arg.getMaterialIdList();
        if (CollectionUtils.isEmpty(materialIdList)) {
            return Result.newError(SHErrorCode.MATERIAL_ID_CANT_NULL);
        }
        log.info("deleteMaterial, ea:[{}], userId:[{}], arg:[{}]", ea, fsUserId, arg);
        if (materialType == 1) {
            List<ProductEntity> productEntityList = productDAO.getByIds(arg.getMaterialIdList());
            if (CollectionUtils.isEmpty(productEntityList)) {
                return Result.newError(SHErrorCode.PRODUCT_NOT_FOUND);
            }
            productEntityList = productEntityList.stream()
                    .filter(e -> StringUtils.equalsIgnoreCase(e.getFsEa(), ea)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productEntityList)) {
                return Result.newError(SHErrorCode.PRODUCT_NOT_FOUND);
            }
            List<String> productIdList = productEntityList.stream().map(ProductEntity::getId).collect(Collectors.toList());
            com.facishare.marketing.api.arg.DeleteMaterialArg deleteMaterialArg = new com.facishare.marketing.api.arg.DeleteMaterialArg();
            deleteMaterialArg.setIdList(productIdList);
            return productService.deleteEnterpriseProductBatch(ea, fsUserId, deleteMaterialArg);
        }
        return Result.newError(SHErrorCode.UNKNOWN_MATERIAL_TYPE);
    }

    @Override
    public Result<MaterialQueryResult> queryMaterial(String ea, Integer fsUserId, MaterialQueryArg arg) {
        Integer materialType = arg.getMaterialType();
        if (materialType == null) {
            return Result.newError(SHErrorCode.MATERIAL_TYPE_CANT_NULL);
        }
        if (arg.getPageNum() == null || arg.getPageSize() == null) {
            return Result.newError(SHErrorCode.PAGE_PARAM_CANT_NULL);
        }
        if (arg.getPageSize() > 500) {
            return Result.newError(SHErrorCode.PAGE_SIZE_OUT_LIMIT);
        }
        if (arg.getPageNum() > 500) {
            return Result.newError(SHErrorCode.PAGE_NUM_OUT_LIMIT);
        }
        if (materialType == 1) {
            return queryProduct(ea, fsUserId, arg);
        }
        return Result.newError(SHErrorCode.UNKNOWN_MATERIAL_TYPE);
    }

    @Override
    public Result<MaterialDetailResult> queryMaterialDetail(String ea, Integer fsUserId, MaterialDetailArg detailArg) {

        Integer materialType = detailArg.getMaterialType();
        if (materialType == null) {
            return Result.newError(SHErrorCode.MATERIAL_TYPE_CANT_NULL);
        }
        if (CollectionUtils.isEmpty(detailArg.getMaterialIdList())) {
            return Result.newError(SHErrorCode.MATERIAL_ID_CANT_NULL);
        }
        if (materialType == 1) {
            return queryProductDetail(ea, fsUserId, detailArg);
        }
        return Result.newError(SHErrorCode.UNKNOWN_MATERIAL_TYPE);
    }

    private Result<MaterialDetailResult> queryProductDetail(String ea, Integer fsUserId, MaterialDetailArg detailArg) {
        List<String> productIdList = detailArg.getMaterialIdList();
        List<ProductEntity> productEntities = productDAO.getByIdsIgnoreDeleted(ea, productIdList);
        if (CollectionUtils.isEmpty(productEntities)) {
            return Result.newSuccess();
        }
        List<PhotoEntity> photoEntityList = photoManager.listByProductIds(productIdList,ea);
        Map<String, List<PhotoEntity>> productIdToPhotoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productEntities)) {
             productIdToPhotoMap = photoEntityList.stream().collect(Collectors.groupingBy(PhotoEntity::getTargetId));
        }
        Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap =
                customizeFormDataManager.getBindFormDataByObjects(ea, productIdList);
        List<HexagonSiteObjectEntity> hexagonSiteObjectEntityList =
                hexagonSiteObjectDAO.getByObjectIdList(ea, productIdList, ObjectTypeEnum.PRODUCT.getType());
        Map<String, List<HexagonSiteObjectEntity>> hexagonSiteToProductMap = new HashMap<>();
        Map<String, HexagonSiteEntity> hexagonSiteEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hexagonSiteObjectEntityList)) {
            hexagonSiteToProductMap = hexagonSiteObjectEntityList.stream().collect(Collectors.groupingBy(HexagonSiteObjectEntity::getObjectId));
            List<HexagonSiteEntity>  siteEntityList = hexagonSiteDAO.getByIds(hexagonSiteObjectEntityList.stream()
                    .map(HexagonSiteObjectEntity::getSiteId).collect(Collectors.toList()));
            hexagonSiteEntityMap = siteEntityList.stream().collect(Collectors.toMap(HexagonSiteEntity::getId, e -> e, (v1, v2) -> v1));
        }
        List<ProductDetailResult> resultList = Lists.newArrayList();
        for (ProductEntity productEntity : productEntities) {
            ProductDetailResult result = new ProductDetailResult();
            result.setId(productEntity.getId());
            result.setStatus(productEntity.getStatus());
            result.setName(productEntity.getName());
            result.setPrice(productEntity.getPrice());
            result.setDiscountPrice(productEntity.getDiscountPrice());
            result.setVideo(productEntity.getVideo());
            result.setSummary(productEntity.getSummary());
            result.setTryOutEnable(productEntity.getTryOutEnable());
            result.setTryOutButtonValue(productEntity.getTryOutButtonValue());
            result.setCreateTime(productEntity.getCreateTime().getTime());
            result.setVideoCoverUrl(productEntity.getVideoCoverUrl());
            List<PhotoEntity> photoEntities = productIdToPhotoMap.getOrDefault(productEntity.getId(), Lists.newArrayList());
            List<String> headPicsList = Lists.newArrayList();
            List<String> detailPicsList = Lists.newArrayList();
            for (PhotoEntity photoEntity : photoEntities) {
                if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
                    headPicsList.add(photoEntity.getUrl());
                } else if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                    detailPicsList.add(photoEntity.getUrl());
                }
            }
            result.setHeadPics(headPicsList);
            result.setDetailPics(detailPicsList);
            CustomizeFormDataEntity formDataEntity = customizeFormDataEntityMap.get(productEntity.getId());
            if(formDataEntity != null) {
                ProductDetailResult.FormData formData = new ProductDetailResult.FormData();
                formData.setFormId(formDataEntity.getId());
                formData.setFormTitle(formDataEntity.getFormHeadSetting().getTitle());
                formData.setFromName(formDataEntity.getFormHeadSetting().getName());
                result.setFormData(formData);
                result.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
            } else {
                List<HexagonSiteObjectEntity> siteObjectEntities = hexagonSiteToProductMap.get(productEntity.getId());
                if (CollectionUtils.isNotEmpty(siteObjectEntities)) {
                    siteObjectEntities = siteObjectEntities.stream().sorted(Comparator.comparing(HexagonSiteObjectEntity::getCreateTime).reversed())
                            .collect(Collectors.toList());
                    HexagonSiteObjectEntity siteObjectEntity = siteObjectEntities.get(0);
                    HexagonSiteEntity hexagonSiteEntity = hexagonSiteEntityMap.get(siteObjectEntity.getSiteId());
                    if (hexagonSiteEntity != null) {
                        ProductDetailResult.HexagonSiteData hexagonSiteData = new ProductDetailResult.HexagonSiteData();
                        hexagonSiteData.setSiteId(siteObjectEntity.getId());
                        hexagonSiteData.setSiteName(hexagonSiteEntity.getName());
                        result.setHexagonSiteData(hexagonSiteData);
                        result.setBindObjectType(BindObjectType.HEXAGON_SITE.getType());
                    }
                }
            }
            resultList.add(result);
        }
        MaterialDetailResult materialDetailResult = new MaterialDetailResult();
        materialDetailResult.setProductDetailResultList(resultList);
        return Result.newSuccess(materialDetailResult);
    }


    private Result<MaterialQueryResult> queryProduct(String ea, Integer fsUserId, MaterialQueryArg arg) {
        ProductQueryArg productQueryArg = arg.getProductQueryArg();
        if (productQueryArg == null) {
            return Result.newError(SHErrorCode.PRODUCT_QUERY_CANT_NULL);
        }
        ListProductArg listProductArg = new ListProductArg();
        listProductArg.setType(ProductArticleTypeEnum.CORPORATE.getType());
        listProductArg.setStatus(productQueryArg.getStatus());
        listProductArg.setGroupId(productQueryArg.getGroupId());
        listProductArg.setTitle(productQueryArg.getKeyword());
        listProductArg.setPageNum(arg.getPageNum());
        listProductArg.setPageSize(arg.getPageSize());
        Result<ProductListResult> result = productService.listEnterpriseProducts(ea, fsUserId, listProductArg);
        List<ProductResult> resultList = Lists.newArrayList();
        ProductListResult productList = result.getData();
        MaterialQueryResult materialQueryResult = new MaterialQueryResult();
        if (productList != null && CollectionUtils.isNotEmpty(productList.getProductDetailResultList())) {
            materialQueryResult.setTotalCount(productList.getTotalCount());
            List<QueryProductDetailResult> productDetailResultList = productList.getProductDetailResultList();
            for (QueryProductDetailResult queryProductDetailResult : productDetailResultList) {
                resultList.add(BeanUtil.copyProperties(queryProductDetailResult, ProductResult.class));
            }
        }
        materialQueryResult.setProductResultList(resultList);
        return Result.newSuccess(materialQueryResult);
    }

    private Result<CreateUpdateMaterialResult> createOrUpdateProduct(String ea, Integer fsUserId, CreateOrUpdateProductArg arg) {
        if (arg == null) {
            return Result.newError(SHErrorCode.PRODUCT_ARG_CANT_NULL);
        }
        boolean isCreate = StringUtils.isBlank(arg.getProductId());
        if (!isCreate) {
            ProductEntity result = productDAO.getById(arg.getProductId());
            if (result == null || !StringUtils.equalsIgnoreCase(ea, result.getFsEa())) {
                return Result.newError(SHErrorCode.PRODUCT_NOT_FOUND);
            }
        }

        if (!isCreate && arg.getIsUpdatePicture() == null) {
            return Result.newError(SHErrorCode.IS_UPDATE_PICTURE_CANT_NULL);
        }
        // 是否需要处理图片，新建要处理，更新取决与参数
        boolean handlePicture = isCreate || arg.getIsUpdatePicture();
        if (StringUtils.isBlank(arg.getName())) {
            return Result.newError(SHErrorCode.PRODUCT_NAME_CANT_NULL);
        }
        if (StringUtils.isBlank(arg.getSummary())) {
            return Result.newError(SHErrorCode.PRODUCT_SUMMARY_CANT_NULL);
        }
        List<PictureArg> headPictureFiles = arg.getHeadPictures();
        if (handlePicture && CollectionUtils.isEmpty(headPictureFiles)) {
            return Result.newError(SHErrorCode.PRODUCT_HEAD_PICTURE_CANT_NULL);
        }
        if (handlePicture && headPictureFiles.size() > 3) {
            return Result.newError(SHErrorCode.PRODUCT_HEAD_PICTURE_LIMIT);
        }
        List<PictureArg> detailPictureFiles = arg.getDetailPictures();
        if (CollectionUtils.isEmpty(detailPictureFiles)
                && StringUtils.isBlank(arg.getVideoId()) && StringUtils.isBlank(arg.getVideoUrl())) {
            return Result.newError(SHErrorCode.PRODUCT_DETAIL_CANT_NULL);
        }
        if (handlePicture && CollectionUtils.isNotEmpty(detailPictureFiles) && detailPictureFiles.size() > 20) {
            return Result.newError(SHErrorCode.PRODUCT_DETAIL_PICTURE_LIMIT);
        }
        Boolean tryOutEnable = arg.getTryOutEnable();
        if (tryOutEnable != null && tryOutEnable) {
            if (StringUtils.isBlank(arg.getFormId())) {
                return Result.newError(SHErrorCode.TRY_FORM_ID_CANT_NULL);
            }
            if (StringUtils.isBlank(arg.getTryOutButtonValue())) {
                return Result.newError(SHErrorCode.TRY_BUTTON_VALUE_CANT_NULL);
            }
            if (StringUtils.isNotBlank(arg.getTryOutButtonValue()) && arg.getTryOutButtonValue().length() > 6) {
                return Result.newError(SHErrorCode.TRY_BUTTON_VALUE_LENGTH_LIMIT);
            }
        }
        AddProductArg addProductArg = new AddProductArg();
        if (StringUtils.isNotBlank(arg.getVideoUrl()) && !arg.getVideoUrl().contains("qq.com")) {
            return Result.newError(SHErrorCode.VIDEO_URL_NOT_SUPPORT);
        }
        addProductArg.setVideo(arg.getVideoUrl());
        if (StringUtils.isNotBlank(arg.getVideoId())) {
            VideoEntity video = videoDAO.getById(arg.getVideoId());
            if (video == null) {
                return Result.newError(SHErrorCode.VIDEO_NOT_FOUND);
            }
            addProductArg.setVideo(video.getHdUrl());
            addProductArg.setVideoCoverUrl(video.getImage4Web());
        }
        addProductArg.setId(arg.getProductId());
        addProductArg.setDiscountPrice(arg.getDiscountPrice());
        addProductArg.setPrice(arg.getPrice());
        addProductArg.setName(arg.getName());
        addProductArg.setFormId(arg.getFormId());
        addProductArg.setType(2);
        addProductArg.setSummary(arg.getSummary());
        addProductArg.setTryOutEnable(arg.getTryOutEnable());
        addProductArg.setTryOutButtonValue(arg.getTryOutButtonValue());
        addProductArg.setFormId(arg.getFormId());
        addProductArg.setIsOperation(handlePicture ? PicOperationFlagEnum.OPERATION_PIC.getType() : PicOperationFlagEnum.NOT_OPERATION_PIC.getType());
        addProductArg.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
        // 上传封面
        List<String> headPics = Lists.newArrayList();
        if (handlePicture) {
            for (PictureArg headPicture : headPictureFiles) {
                String extension = headPicture.getExtension();
                FileV2Manager.FileManagerPicResult picResult = fileV2Manager.uploadPic(headPicture.getData(), extension);
                headPics.add(picResult.getUrlAPath());
            }
        }
        addProductArg.setHeadPics(headPics);
        List<String> detailPics = Lists.newArrayList();
        // 上传详情图
        if (handlePicture && CollectionUtils.isNotEmpty(detailPictureFiles)) {
            for (PictureArg detailPicture : detailPictureFiles) {
                String extension = detailPicture.getExtension();
                FileV2Manager.FileManagerPicResult picResult = fileV2Manager.uploadPic(detailPicture.getData(), extension);
                detailPics.add(picResult.getUrlAPath());
            }
            addProductArg.setDetailPics(detailPics);
        }
        addProductArg.setDetailPics(detailPics);
        Result<AddEnterpriseProductResult> result = productService.addEnterpriseProduct(ea, fsUserId, addProductArg);
        if (result.isSuccess()) {
            CreateUpdateMaterialResult materialResult = new CreateUpdateMaterialResult();
            materialResult.setId(result.getData().getId());
            return Result.newSuccess(materialResult);
        }
        return Result.newError(result.getErrCode(), result.getErrMsg());
    }
}
