package com.facishare.marketing.provider.manager.marketingactivity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.GetMarketingActivityDetailData;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPreviewArg;
import com.facishare.marketing.api.arg.marketingactivity.cancelSpreadActivity;
import com.facishare.marketing.api.arg.sms.GroupSenderArg;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.MarketingActivityPreviewData;
import com.facishare.marketing.api.result.sms.GroupSendResult;
import com.facishare.marketing.api.result.sms.QuerySMSSendResult;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityActionService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.ExternalConfigVO;
import com.facishare.marketing.api.vo.MarketingActivityExternalConfigVO;
import com.facishare.marketing.api.vo.MarketingActivityGroupSenderDeatilVO;
import com.facishare.marketing.api.vo.MarketingActivityGroupSenderVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.MathUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.dao.ShortUrlConfigDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSignatureDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSignatureEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.manager.MarketingUserGroupManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.sms.SmsTemplateManager;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsParamManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasAddMarketingActivityArg;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: dongzhb
 * @date: 2019/3/8
 * @Description: 短信营销
 */
@Slf4j
@Service("sendGroupSmsManager")
public class SendGroupSmsManager implements MarketingActivityActionService {
    private static Map<Integer, String> groupSmsStatusEnumMap = Maps.newHashMap();

    static {
        groupSmsStatusEnumMap.put(1, String.valueOf(SendStatusEnum.FINISHED.getStatus()));
        groupSmsStatusEnumMap.put(2, String.valueOf(SendStatusEnum.WAIT_SEND.getStatus()));
    }

    @Autowired
    private SendService sendService;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private SmsParamManager smsParamManager;
    @Autowired
    private MwSmsSendDao mwSmsSendDao;
    @Autowired
    private MwSendManager sendManager;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private MwSendManager mwSendManager;
    @Autowired
    private ShortUrlManager shortUrlManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;
    @Autowired
    private SmsTemplateManager smsTemplateManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private ShortUrlConfigDao shortUrlConfigDao;
    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private MwSmsSignatureDao mwSmsSignatureDao;

    @Override
    public AddMarketingActivityResult doAddAction(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        //短信营销业务
        AddMarketingActivityArg.MarketingActivityGroupSenderVO groupSenderVO = addMarketingActivityArg.getMarketingActivityGroupSenderVO();
        if (checkArgIsWrong(groupSenderVO)) {
            AddMarketingActivityResult result = new AddMarketingActivityResult();
            result.setErrorCode(SHErrorCode.PARAMS_ERROR);
            return result;
        }
        GroupSenderArg groupSenderArg = new GroupSenderArg();
        groupSenderArg.setChannelType(ChannelTypeEnum.MARKETING.getType());
        BeanUtils.copyProperties(groupSenderVO, groupSenderArg);
        groupSenderArg.setScheduleTime(DateUtil.fromTimestamp(groupSenderVO.getFixedTime()));
        groupSenderArg.setEa(ea);
        groupSenderArg.setUserId(fsUserId);
        groupSenderArg.setUserGroupIds(groupSenderVO.getMarketingUserGroupIds());
        groupSenderArg.setShowTemplate(true);
        groupSenderArg.setGroupType(groupSenderVO.getSendRange());
        groupSenderArg.setFilterNDaySentUser(groupSenderVO.getFilterNDaySentUser());
        groupSenderArg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        groupSenderArg.setBusinessType(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_SENDGROUPSMSMANAGER_134));
        groupSenderArg.setIgnoreErrorPhone(groupSenderVO.isIgnoreErrorPhone());
        groupSenderArg.setSmsVarArgs(groupSenderVO.getSmsVarArgs());
        if (CollectionUtils.isNotEmpty(groupSenderVO.getMarketingUserGroupIds())) {
            List<MarketingUserGroupEntity> marketingUserGroupEntityList = marketingUserGroupDao.batchGet(ea, groupSenderVO.getMarketingUserGroupIds());
            if (CollectionUtils.isNotEmpty(marketingUserGroupEntityList)) {
                String receiver = marketingUserGroupEntityList.stream().map(MarketingUserGroupEntity::getName).collect(Collectors.joining("、"));
                groupSenderArg.setReceiver(receiver);
            }
        }
        PaasAddMarketingActivityArg arg = new PaasAddMarketingActivityArg();
        arg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        if (SaveOrSendTypeEnum.SEND.getType().equals(groupSenderVO.getEventType())) {
            MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(ea, groupSenderVO.getTemplateId());
            if (templateEntity == null) {
                log.warn("SendGroupSmsManager.doAddAction templateEntity is null ea:{} fsUserId:{} addMarketingActivityArg:{}", ea, fsUserId, addMarketingActivityArg);
                return null;
            }
            arg.setName(templateEntity.getName());
        }
        if (StringUtils.isNotEmpty(groupSenderVO.getTemplateName())){
            arg.setName(groupSenderVO.getTemplateName());
        }

        arg.setSpreadType(String.valueOf(addMarketingActivityArg.getSpreadType()));
        /**1 立即发送  2定时发送*/
        arg.setStatus(groupSmsStatusEnumMap.get(groupSenderArg.getType()));
        if (arg.getStatus() == null){
            arg.setStatus("1");
        }

        //针对需要审核模板的短信
        if (SaveOrSendTypeEnum.SAVE.getType().equals(groupSenderVO.getEventType())) {
            groupSenderArg.setType(MwSendTaskTypeEnum.TO_BE_SEND.getType());
        }
        String marketingActivityId = null;
        Result<GroupSendResult> result;
        try {
            boolean needAudit = marketingActivityAuditManager.isNeedAudit(ea);
            if (needAudit) {
                ObjectData objectData = marketingActivityAuditManager.buildObjectData(ea, addMarketingActivityArg.getMarketingActivityAuditData());
                arg.setObjectData(objectData);
            }
            Result<String> marketingActivityIdResult = marketingActivityCrmManager.addMarketingActivity(ea, fsUserId, arg);
            if (!marketingActivityIdResult.isSuccess()){
                AddMarketingActivityResult crmResult = new AddMarketingActivityResult();
                crmResult.setCrmErrorCode(marketingActivityIdResult.getErrCode());
                crmResult.setCrmErrorMsg(marketingActivityIdResult.getErrMsg());
                return crmResult;
            }
            marketingActivityId = marketingActivityIdResult.getData();
            if (StringUtils.isBlank(marketingActivityId)) {
                return new AddMarketingActivityResult();
            }
            result = sendService.sendGroupSms(groupSenderArg);
            if (!result.isSuccess()) {
                //作废并删除crm营销活动
                crmV2Manager.bulkInvalid(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), ImmutableList.of(marketingActivityId));
                AddMarketingActivityResult addMarketingActivityResult = new AddMarketingActivityResult();
                addMarketingActivityResult.setErrorCode(SHErrorCode.getByCode(result.getErrCode()));
                return addMarketingActivityResult;
            }
        } catch (Exception ex) {
            log.warn("doSendGroupSMS exception : {}", ex);
            arg.setStatus(String.valueOf(SendStatusEnum.FAIL.getStatus()));
            //作废并删除crm营销活动
            crmV2Manager.bulkInvalid(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), ImmutableList.of(marketingActivityId));
            return new AddMarketingActivityResult();
        }

        //组装短信数据
        MarketingActivityExternalConfigVO marketingActivityExternalConfigVO = new MarketingActivityExternalConfigVO();
        marketingActivityExternalConfigVO.setAssociateId(result.getData().getSmsSendId());
        marketingActivityExternalConfigVO.setAssociateIdType(AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType());
        marketingActivityExternalConfigVO.setMarketingActivityId(marketingActivityId);
        ExternalConfigVO externalConfigVO = new ExternalConfigVO();
        MarketingActivityGroupSenderVO marketingActivityGroupSenderVO = BeanUtil.copyByGson(groupSenderVO, MarketingActivityGroupSenderVO.class);
        externalConfigVO.setMarketingActivityGroupSenderVO(marketingActivityGroupSenderVO);
        marketingActivityExternalConfigVO.setExternalConfig(externalConfigVO);
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = BeanUtil.copyByGson(marketingActivityExternalConfigVO, MarketingActivityExternalConfigEntity.class);
        marketingActivityExternalConfigEntity.setId(UUIDUtil.getUUID());
        marketingActivityExternalConfigEntity.setEa(ea);
        marketingActivityExternalConfigEntity.setMarketingActivityType(MarketingActivityTypeEnum.ENTERPRISE.getType());
        marketingActivityExternalConfigEntity.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        marketingActivityExternalConfigDao.insert(marketingActivityExternalConfigEntity);
        log.info("doSendGroupSmsManager groupSenderArg:{} marketingActivityExternalConfigEntity:{} marketingActivityId:{}", groupSenderArg, marketingActivityExternalConfigEntity, marketingActivityId);
        shortUrlManager.setShortUrlConfig(groupSenderVO.getShortUrlMap());
        return new AddMarketingActivityResult(marketingActivityId);
    }

//    private Integer sendRangeToGroupType(String ea, String marketingEventId, Integer sendRange) {
//        if (sendRange != null && SmsGroupTypeEnum.CONFERENCE_ENROLL.getType() == sendRange) {
//            MarketingEventEnum marketingEventEnum = conferenceManager.getMarketingTypeByMarketingEventIdFromDB(ea, marketingEventId);
//            if (MarketingEventEnum.LIVE_MARKETING.equals(marketingEventEnum)) {
//                sendRange = SmsGroupTypeEnum.LIVE_ENROLL.getType();
//            }
//        }
//        return sendRange;
//    }

    private boolean checkArgIsWrong(AddMarketingActivityArg.MarketingActivityGroupSenderVO groupSenderVO) {
        //检查是否定时发送的时间是否正确
        if (SaveOrSendTypeEnum.SEND.getType().equals(groupSenderVO.getEventType()) && MwSendTaskTypeEnum.SCHEDULE_SEND.getType().equals(groupSenderVO.getType())) {
            if (groupSenderVO.getFixedTime() < System.currentTimeMillis()) {
                log.warn("定时任务的时间戳小于当前系统的时间戳，发送时间为过去 groupSenderVO:{}", groupSenderVO);
                return true;
            }
        }
        return false;
    }

    @Override
    public GetMarketingActivityResult doDetailAction(String ea, Integer fsUserId, GetMarketingActivityDetailData getMarketingActivityDetailData) {
        GetMarketingActivityResult getMarketingActivityResult = new GetMarketingActivityResult();
        getMarketingActivityResult.setName(getMarketingActivityDetailData.getName());
        getMarketingActivityResult.setSpreadType(getMarketingActivityDetailData.getSpreadType());
        getMarketingActivityResult.setStatus(getMarketingActivityDetailData.getStatus());
        getMarketingActivityResult.setMarketingEventId(getMarketingActivityDetailData.getMarketingEventId());
        getMarketingActivityResult.setId(getMarketingActivityDetailData.getId());
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(getMarketingActivityDetailData.getId());
        Optional<MarketingActivityExternalConfigEntity> optionalOfConfigEntity = Optional.ofNullable(marketingActivityExternalConfigEntity);
        MarketingActivityGroupSenderDeatilVO marketingActivityGroupSenderDeatilVO = new MarketingActivityGroupSenderDeatilVO();
        Integer sendRange = optionalOfConfigEntity.map(entity -> entity.getExternalConfig()).map(externalConfig -> externalConfig.getMarketingActivityGroupSenderVO()).map(vo -> vo.getSendRange()).orElse(null);
        marketingActivityGroupSenderDeatilVO.setSendRange(sendRange);
        if (sendRange != null && sendRange == SendRangeEnum.USER_GROUP.getType()) {
            marketingActivityGroupSenderDeatilVO.setMarketingUserGroupIds(marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO().getMarketingUserGroupIds());
        }

        if (marketingActivityExternalConfigEntity.getExternalConfig() != null && marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO() != null) {
            marketingActivityGroupSenderDeatilVO.setSmsVarArgs(marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO().getSmsVarArgs());
        }

    //    String smsContent = getMarketingActivityDetailData.getName();
        String smsContent = null;
        if (!StringUtils.isEmpty(marketingActivityExternalConfigEntity.getAssociateId())) {
            Result<QuerySMSSendResult> result = sendService.getSMSSendById(marketingActivityExternalConfigEntity.getAssociateId());
            if (result.getData() != null) {
                QuerySMSSendResult querySMSSendResult = result.getData();
                BeanUtils.copyProperties(querySMSSendResult, marketingActivityGroupSenderDeatilVO);
                /**计算发送失败数 如果是取消发送的状态，则不计算失败人数*/
                if (querySMSSendResult.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType()) && querySMSSendResult.getStatus().equals(MwSendStatusEnum.CANCEL.getStatus())) {
                    marketingActivityGroupSenderDeatilVO.setFaillSenderCount(0);
                } else {
                    marketingActivityGroupSenderDeatilVO.setFaillSenderCount(MathUtil.subtract(querySMSSendResult.getToSenderCount(), querySMSSendResult.getActualSenderCount()));
                }
                smsContent = querySMSSendResult.getContent();
                marketingActivityGroupSenderDeatilVO.setSceneType(querySMSSendResult.getSceneType());

                if (marketingActivityExternalConfigEntity.getExternalConfig().getMarketingActivityGroupSenderVO().getType() != MwSendTaskTypeEnum.SCHEDULE_SEND.getType()){
                    if (querySMSSendResult.getSendTime() != 0){
                        marketingActivityGroupSenderDeatilVO.setSendTime(querySMSSendResult.getSendTime());
                    }else {
                        marketingActivityGroupSenderDeatilVO.setSendTime(querySMSSendResult.getCreateTime());
                    }
                }else {
                    marketingActivityGroupSenderDeatilVO.setSendTime(querySMSSendResult.getScheduleTime());
                }
            }
            /*
            MwSmsSendEntity mwSmsSendEntity = mwSmsSendDao.getSMSSendById(marketingActivityExternalConfigEntity.getAssociateId());
            if (null != mwSmsSendEntity) {
                smsContent = sendManager.buildSmsContentBySignatureId(mwSmsSendEntity.getSignatureId(), smsContent);
            }
            */
        }
        marketingActivityGroupSenderDeatilVO.setContent(smsContent);
        getMarketingActivityResult.setMarketingActivityGroupSenderDeatilVO(marketingActivityGroupSenderDeatilVO);
        //由于梦网统计发送成功与否有缺陷，因此定时任务中，发送成功人数始终为0。同时定时任务支持取消发送
        if (marketingActivityGroupSenderDeatilVO.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType()) && marketingActivityGroupSenderDeatilVO.getStatus().equals(MwSendStatusEnum.SCHEDULE_SENDING.getStatus())) {
            marketingActivityGroupSenderDeatilVO.setActualSenderCount(0);
            getMarketingActivityResult.setSendCancelable(true);
        } else {
            getMarketingActivityResult.setSendCancelable(false);
        }
        if (MwSendStatusEnum.isSending(marketingActivityGroupSenderDeatilVO.getStatus())) {
            marketingActivityGroupSenderDeatilVO.setActualSenderCount(0);
            marketingActivityGroupSenderDeatilVO.setFaillSenderCount(0);
        }
        return getMarketingActivityResult;
    }

    @Override
    public Result<Boolean> doDeleteAction(String ea, Integer fsUserId, String marketingActivityId) {
    /*
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfigEntity == null){
            return Result.newError(SHErrorCode.DELETE_GROUP_SMS_SPREAD_FAILED);
        }

        MwSmsSendEntity smsSendEntity = mwSmsSendDao.getSMSSendById(marketingActivityExternalConfigEntity.getAssociateId());
        if (null == smsSendEntity) {
            log.warn("SendServiceImpl.doDeleteAction smsSendEntity is null ea:{} marketingActivityId:{} smsSendId:{}",
                    ea, marketingActivityId, marketingActivityExternalConfigEntity.getAssociateId());
            return Result.newError(SHErrorCode.DELETE_GROUP_SMS_SPREAD_FAILED);
        }

        sendManager.deleteSend(marketingActivityExternalConfigEntity.getAssociateId());
    */
        return Result.newSuccess();
    }

    @Override
    public AddMarketingActivityResult doUpdateAction(String ea, Integer fsUserId, AddMarketingActivityArg updateMarketingActivityArg) {
        return null;
    }

    @Override
    public AddMarketingActivityArg.MarketingActivityAuditData getMarketingActivityAuditData(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        AddMarketingActivityArg.MarketingActivityAuditData data = new AddMarketingActivityArg.MarketingActivityAuditData();
        //短信营销业务
        AddMarketingActivityArg.MarketingActivityGroupSenderVO groupSenderVO = addMarketingActivityArg.getMarketingActivityGroupSenderVO();
        GroupSenderArg groupSenderArg = new GroupSenderArg();
        groupSenderArg.setChannelType(ChannelTypeEnum.MARKETING.getType());
        BeanUtils.copyProperties(groupSenderVO, groupSenderArg);
        groupSenderArg.setScheduleTime(DateUtil.fromTimestamp(groupSenderVO.getFixedTime()));
        groupSenderArg.setEa(ea);
        groupSenderArg.setUserId(fsUserId);
        groupSenderArg.setUserGroupIds(groupSenderVO.getMarketingUserGroupIds());
        groupSenderArg.setShowTemplate(true);
        groupSenderArg.setGroupType(groupSenderVO.getSendRange());
        groupSenderArg.setFilterNDaySentUser(groupSenderVO.getFilterNDaySentUser());
        groupSenderArg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
        groupSenderArg.setBusinessType(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_SENDGROUPSMSMANAGER_134));
        if (CollectionUtils.isNotEmpty(groupSenderVO.getMarketingUserGroupIds())) {
            List<MarketingUserGroupEntity> marketingUserGroupEntityList = marketingUserGroupDao.batchGet(ea, groupSenderVO.getMarketingUserGroupIds());
            if (CollectionUtils.isNotEmpty(marketingUserGroupEntityList)) {
                String receiver = marketingUserGroupEntityList.stream().map(MarketingUserGroupEntity::getName).collect(Collectors.joining("、"));
                groupSenderArg.setReceiver(receiver);
            }
        }
        if (CollectionUtils.isNotEmpty(groupSenderVO.getMarketingUserGroupIds())) {
            List<MarketingUserGroupEntity> marketingUserGroupEntityList = marketingUserGroupDao.batchGet(ea, groupSenderVO.getMarketingUserGroupIds());
            if (CollectionUtils.isNotEmpty(marketingUserGroupEntityList)) {
                String receiver = marketingUserGroupEntityList.stream().map(MarketingUserGroupEntity::getName).collect(Collectors.joining("、"));
                groupSenderArg.setReceiver(receiver);
            }
        }
        //针对需要审核模板的短信
        if (SaveOrSendTypeEnum.SAVE.getType().equals(groupSenderVO.getEventType())) {
            groupSenderArg.setType(MwSendTaskTypeEnum.TO_BE_SEND.getType());
        }
        Result<Integer> result = sendService.sendGroupSmsCount(groupSenderArg);
        int sendGroupCount = 0;
        if (result != null && result.isSuccess() && result.getData() != null) {
            sendGroupCount = result.getData();
        }
        data.setSendGroupCount(sendGroupCount);
        MwSmsSignatureEntity signatureEntity = mwSmsSignatureDao.getSignatureByEa(ea);
        if (signatureEntity != null) {
            data.setSendAccount(signatureEntity.getSvrName());
        }
        data.setSendContent(groupSenderVO.getTemplateContent());
        return data;
    }

    @Override
    public Result<MarketingActivityPreviewData> getPreviewData(String ea, Integer fsUserId, MarketingActivityPreviewArg arg) {
        return Result.newSuccess();
    }
}