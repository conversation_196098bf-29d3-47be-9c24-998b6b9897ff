package com.facishare.marketing.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.UrlEncoder;
import com.facishare.marketing.api.arg.CompanyWxOfficialAccountsListArg;
import com.facishare.marketing.api.arg.GetWxOfficialAccountsByAppIdArg;
import com.facishare.marketing.api.arg.QueryWxOfficialAccountsQrCodeChannelArg;
import com.facishare.marketing.api.arg.UpsertWxOfficialAccountsQrCodeChannelArg;
import com.facishare.marketing.api.arg.wx.*;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.WxOfficialAccountsService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.common.contstant.CookieConstant;
import com.facishare.marketing.common.contstant.WxApiConstants;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingWxServiceDao;
import com.facishare.marketing.provider.dao.WxOfficialAccountsQrCodeChannelDAO;
import com.facishare.marketing.provider.dao.WxServiceUserMemberBindDao;
import com.facishare.marketing.provider.dao.officialWebsite.WxOfficialAccountsQrCodeDAO;
import com.facishare.marketing.provider.dao.officialWebsite.WxServiceQrCodeOfficialWebsiteRelationDAO;
import com.facishare.marketing.provider.dao.wx.WxMaterialDetailDao;
import com.facishare.marketing.provider.dto.MemberCookieInfo;
import com.facishare.marketing.provider.entity.MarketingWxServiceEntity;
import com.facishare.marketing.provider.entity.WxOfficialAccountsQrCodeChannelEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.officialWebsite.WxOfficialAccountsQrCodeEntity;
import com.facishare.marketing.provider.entity.officialWebsite.WxServiceQrCodeOfficialWebsiteRelationEntity;
import com.facishare.marketing.provider.entity.wx.WxMaterialDetailEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.wxApiArg.BatchGetMaterialArg;
import com.facishare.marketing.provider.manager.wxthirdplatform.wxApiResult.BatchGetMaterialResult;
import com.facishare.marketing.provider.manager.wxthirdplatform.wxApiResult.BatchGetMaterialResult.ItemContentDetail;
import com.facishare.marketing.provider.manager.wxthirdplatform.wxApiResult.BatchGetMaterialResult.NewsItem;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg;
import com.facishare.wechat.dubborestouterapi.data.WeChatMenuFormData;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.union.WeChatCustomerMenuRestService;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.vo.QrCodeVo;
import com.facishare.wechat.proxy.model.wx.IndividualQrResult;
import com.facishare.wechat.proxy.service.QrCodeService;
import com.facishare.wechat.union.core.api.model.result.BatchGetByWxAppIdsResult;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/06/27
 **/
@Service("wxOfficialAccountsService")
@Slf4j
public class WxOfficialAccountsServiceImpl implements WxOfficialAccountsService {

    @Autowired
    private OuterServiceWechatService outerServiceWechatService;

    @Autowired
    private QrCodeService qrCodeService;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;

    @Autowired
    private OpenAppAdminService openAppAdminService;

    @Autowired
    private WxOfficialAccountsQrCodeChannelDAO wxOfficialAccountsQrCodeChannelDAO;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private EIEAConverter eIEAConverter;

    @Autowired
    private WxMaterialDetailDao wxMaterialDetailDao;

    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;

    @Autowired
    private WxOfficialAccountsQrCodeDAO wxOfficialAccountsQrCodeDAO;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private WxServiceQrCodeOfficialWebsiteRelationDAO wxServiceQrCodeOfficialWebsiteRelationDAO;

    @Autowired
    private WechatQrCodeRestService wechatQrCodeRestService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private HttpManager httpManager;
    
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private WxServiceUserMemberBindDao wxServiceUserMemberBindDao;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @ReloadableProperty("marketing_appid")
    private String appId;

    @Value("${host}")
    private String host;

    @ReloadableProperty("cookieDomain")
    private String cookieDomain;

    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;

    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;

    @Autowired
    private WeChatCustomerMenuRestService weChatCustomerMenuRestService;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private IdempotentRecordManager idempotentRecordManager;

    @Override
    public Result<List<AccountDataListResult>> getCompanyWxOfficialAccountsList(CompanyWxOfficialAccountsListArg arg) {
        FsUserVO fsUserVO = new FsUserVO(arg.getEa(), arg.getFsUserId());
        com.facishare.wechat.proxy.common.result.ModelResult<List<OuterServiceResult>> outWechatResult = outerServiceWechatService.queryOuterServiceList(fsUserVO);
        if (!outWechatResult.isSuccess()) {
            log.warn("WxOfficialAccountsServiceImpl.getCompanyWxOfficialAccountsList outWechatResult not success outWechatResult:{}", outWechatResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (CollectionUtils.isEmpty(outWechatResult.getResult())) {
            log.warn("WxOfficialAccountsServiceImpl.getCompanyWxOfficialAccountsList");
            return new Result<>(SHErrorCode.NOT_ENTERPRISE_WX_OFFICIAL_ACCOUNTS);
        }
        List<AccountDataListResult> accountDataListResultList = BeanUtil.copy(outWechatResult.getResult(), AccountDataListResult.class);
        return new Result<>(SHErrorCode.SUCCESS, accountDataListResultList);
    }

    @Override
    public Result<AccountDataListResult> getWxOfficialAccountsByAppId(GetWxOfficialAccountsByAppIdArg arg) {
        // 将appId换取wxAppId
        ModelResult<String> modelResult = outerServiceWechatService.transAppIdToWxAppId(arg.getAppId());
        if (!modelResult.isSuccess()) {
            log.warn("WxOfficialAccountsServiceImpl.getWxOfficialAccountsByAppId transAppIdToWxAppId error modelResult:{}", modelResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        String wxappId = modelResult.getResult();
        // 根据wxAppId获取详情
        ModelResult<List<BatchGetByWxAppIdsResult>> batchGetByEaAndWxAppIdResult = outerServiceWechatService.batchGetByWxAppIds(Lists.newArrayList(wxappId));
        if (!batchGetByEaAndWxAppIdResult.isSuccess() || CollectionUtils.isEmpty(batchGetByEaAndWxAppIdResult.getResult())) {
            log.warn("WxOfficialAccountsServiceImpl.getWxOfficialAccountsByAppId batchGetByEaAndWxAppIds error batchGetByEaAndWxAppIdResult:{}", batchGetByEaAndWxAppIdResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        List<AccountDataListResult> accountDataListResultList = BeanUtil.copy(batchGetByEaAndWxAppIdResult.getResult(), AccountDataListResult.class);
        if (CollectionUtils.isNotEmpty(accountDataListResultList)) {
            AccountDataListResult accountDataListResult = BeanUtil.copy(accountDataListResultList.get(0), AccountDataListResult.class);
            // 设置qrUrl
            QrCodeVo qrCodeVo = new QrCodeVo();
            qrCodeVo.setEnterpriseAccount(arg.getEa());
            qrCodeVo.setExpireSeconds(0);
            qrCodeVo.setWxAppId(accountDataListResult.getWxAppId());
            com.facishare.wechat.proxy.common.result.ModelResult<IndividualQrResult> qrResultModelResult = qrCodeService.getSystemForeverQr(qrCodeVo);
            if (qrResultModelResult.isSuccess() && qrResultModelResult.getResult() != null) {
                accountDataListResult.setWxQrCodeUrl(qrResultModelResult.getResult().getShowUrl());
            }
            return new Result<>(SHErrorCode.SUCCESS, accountDataListResult);
        }
        return new Result<>(SHErrorCode.ENTERPRISE_WX_OFFICIAL_ACCOUNTS_NOT_FOUND);
    }

    @Override
    public Result<List<MarketingWxServiceResult>> getListByObjectInfo(String objectId, Integer objectType, String ea) {
        // 查询物料对应ea
        if (StringUtils.isEmpty(ea)) {
            ea = objectManager.getObjectEa(objectId, objectType);
        }
        if (StringUtils.isBlank(ea)) {
            log.warn("WxOfficialAccountsServiceImpl.getListByObjectInfo ea is null ea:{}", ea);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        BaseResult<List<Integer>> baseResult = openAppAdminService.getAppAdminIds(ea, appId);
        if (!baseResult.isSuccess() || CollectionUtils.isEmpty(baseResult.getResult())) {
            log.warn("WxOfficialAccountsServiceImpl.getListByObjectInfo getAppAdminIds error baseResult:{}", baseResult);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea, baseResult.getResult().get(0), false);
    }

    @Override
    public Result upsertWxOfficialAccountsQrCodeChannel(UpsertWxOfficialAccountsQrCodeChannelArg arg) {
        WxOfficialAccountsQrCodeChannelEntity wxOfficialAccountsQrCodeChannelEntity = new WxOfficialAccountsQrCodeChannelEntity();
        wxOfficialAccountsQrCodeChannelEntity.setId(UUIDUtil.getUUID());
        wxOfficialAccountsQrCodeChannelEntity.setEa(arg.getEa());
        wxOfficialAccountsQrCodeChannelEntity.setWxAppId(arg.getWxAppId());
        wxOfficialAccountsQrCodeChannelEntity.setName(arg.getName());
        wxOfficialAccountsQrCodeChannelEntity.setChannel(arg.getChannel());
        wxOfficialAccountsQrCodeChannelEntity.setQrCodeId(arg.getQrCodeId());
        wxOfficialAccountsQrCodeChannelDAO.upsertWxOfficialAccountsQrCodeChannel(wxOfficialAccountsQrCodeChannelEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<QueryWxOfficialAccountsQrCodeChannelResult> queryWxOfficialAccountsQrCodeChannel(QueryWxOfficialAccountsQrCodeChannelArg arg) {
        QueryWxOfficialAccountsQrCodeChannelResult result = new QueryWxOfficialAccountsQrCodeChannelResult();
        WxOfficialAccountsQrCodeChannelEntity wxOfficialAccountsQrCodeChannel = null;
        if (StringUtils.isNotEmpty(arg.getQrCodeId())) {
            wxOfficialAccountsQrCodeChannel = wxOfficialAccountsQrCodeChannelDAO.getByQrCodeId(arg.getEa(), arg.getWxAppId(), arg.getQrCodeId());
        }
        if (wxOfficialAccountsQrCodeChannel == null && StringUtils.isNotEmpty(arg.getName())) {
            wxOfficialAccountsQrCodeChannel = wxOfficialAccountsQrCodeChannelDAO.getWxOfficialAccountsQrCodeChannel(arg.getEa(), arg.getWxAppId(), arg.getName());
        }
        if (wxOfficialAccountsQrCodeChannel != null) {
            result.setChannel(wxOfficialAccountsQrCodeChannel.getChannel());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<BatchGetWxMaterialResult>> batchGetWxMaterial(BatchGetWxMaterialArg arg) {
        PageResult<BatchGetWxMaterialResult> pageResult = new PageResult<>();
        List<BatchGetWxMaterialResult> batchGetWxMaterialResults = Lists.newArrayList();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(batchGetWxMaterialResults);
        if (StringUtils.isBlank(arg.getName())) {
            // 不通过名称搜索直接查询列表
            BatchGetMaterialResult batchGetMaterialResult = queryMaterialList(arg.getWxAppId(), arg.getEa(), ((arg.getPageNum() - 1) * arg.getPageSize()), arg.getPageSize());
            if (batchGetMaterialResult == null || CollectionUtils.isEmpty(batchGetMaterialResult.getItem())) {
                return Result.newSuccess(pageResult);
            }
            pageResult.setTotalCount(batchGetMaterialResult.getTotalCount());
            conversionMaterialToMarketingResult(batchGetMaterialResult.getItem(), batchGetWxMaterialResults);
        } else {
            // 通过名称查询 素材id -> id下素材详情
            List<WxMaterialDetailEntity> wxMaterialDetailEntities = wxMaterialDetailDao.queryWxMaterialByName(arg.getEa(), arg.getWxAppId(), arg.getName());
            if (CollectionUtils.isEmpty(wxMaterialDetailEntities)) {
                return Result.newSuccess(pageResult);
            }
            Map<String, BatchGetMaterialResult.ItemContentDetail> itemContentDetailMap = Maps.newConcurrentMap();
            CountDownLatch countDownLatch = new CountDownLatch(wxMaterialDetailEntities.size());
            for (WxMaterialDetailEntity wxMaterialDetailEntity : wxMaterialDetailEntities) {
                ThreadPoolUtils.execute(() -> {
                    try {
                        ItemContentDetail itemContentDetail = queryMaterialDetail(wxMaterialDetailEntity.getWxAppId(), wxMaterialDetailEntity.getEa(), wxMaterialDetailEntity.getMediaId());
                        itemContentDetailMap.put(wxMaterialDetailEntity.getMediaId(), itemContentDetail);
                    } catch (Exception e) {
                        log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterial queryMaterialDetail error e:", e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
            try {
                countDownLatch.await(15, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterial error e:", e);
            }
            List<BatchGetMaterialResult.ItemDetail> wxResult = Lists.newArrayList();
            for (Map.Entry<String, BatchGetMaterialResult.ItemContentDetail> entry : itemContentDetailMap.entrySet()) {
                BatchGetMaterialResult.ItemDetail itemDetail = new BatchGetMaterialResult.ItemDetail();
                itemDetail.setMediaId(entry.getKey());
                itemDetail.setContent(entry.getValue());
                wxResult.add(itemDetail);
            }
            conversionMaterialToMarketingResult(wxResult, batchGetWxMaterialResults);
            pageResult.setTotalCount(wxResult.size());
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<BatchGetWxMaterialResult>> batchGetDraft(BatchGetWxMaterialArg arg) {
        PageResult<BatchGetWxMaterialResult> pageResult = new PageResult<>();
        List<BatchGetWxMaterialResult> batchGetWxMaterialResults = Lists.newArrayList();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(batchGetWxMaterialResults);
        if (StringUtils.isBlank(arg.getName())) {
            // 不通过名称搜索直接查询列表
            BatchGetMaterialResult batchGetMaterialResult = queryDraftMaterialList(arg.getWxAppId(), arg.getEa(), ((arg.getPageNum() - 1) * arg.getPageSize()), arg.getPageSize());
            if (batchGetMaterialResult == null || CollectionUtils.isEmpty(batchGetMaterialResult.getItem())) {
                return Result.newSuccess(pageResult);
            }
            pageResult.setTotalCount(batchGetMaterialResult.getTotalCount());
            conversionMaterialToMarketingResult(batchGetMaterialResult.getItem(), batchGetWxMaterialResults);
        } else {
            // 通过名称查询 素材id -> id下素材详情
            List<WxMaterialDetailEntity> wxMaterialDetailEntities = wxMaterialDetailDao.queryWxMaterialByName(arg.getEa(), arg.getWxAppId(), arg.getName());
            if (CollectionUtils.isEmpty(wxMaterialDetailEntities)) {
                return Result.newSuccess(pageResult);
            }
//            wxMaterialDetailEntities = wxMaterialDetailEntities.stream().collect(Collectors.collectingAndThen(
//                    Collectors.toCollection(() ->
//                            new TreeSet<>(Comparator.comparing(WxMaterialDetailEntity::getMediaId))),
//                    ArrayList::new)
//            );
            Map<String, BatchGetMaterialResult.ItemContentDetail> itemContentDetailMap = Maps.newConcurrentMap();
            CountDownLatch countDownLatch = new CountDownLatch(wxMaterialDetailEntities.size());
            for (WxMaterialDetailEntity wxMaterialDetailEntity : wxMaterialDetailEntities) {
                ThreadPoolUtils.execute(() -> {
                    try {
                        ItemContentDetail itemContentDetail = queryDraftDetail(wxMaterialDetailEntity.getWxAppId(), wxMaterialDetailEntity.getEa(), wxMaterialDetailEntity.getMediaId());
                        if(itemContentDetail!=null){
                            itemContentDetailMap.put(wxMaterialDetailEntity.getMediaId(), itemContentDetail);
                        }
                    } catch (Exception e) {
                        log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterial queryMaterialDetail error e:", e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
            try {
                countDownLatch.await(15, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterial error e:", e);
            }
            List<BatchGetMaterialResult.ItemDetail> wxResult = Lists.newArrayList();
            Map<String, BatchGetMaterialResult.ItemContentDetail> sortMap = itemContentDetailMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                            (oleValue, newValue) -> oleValue, LinkedHashMap::new));
            sortMap= sortMap.entrySet().stream().skip((arg.getPageNum() - 1) * arg.getPageSize()).limit(arg.getPageSize()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            for (Map.Entry<String, BatchGetMaterialResult.ItemContentDetail> entry : sortMap.entrySet()) {
                BatchGetMaterialResult.ItemDetail itemDetail = new BatchGetMaterialResult.ItemDetail();
                itemDetail.setMediaId(entry.getKey());
                itemDetail.setContent(entry.getValue());
                wxResult.add(itemDetail);
            }
            conversionMaterialToMarketingResult(wxResult, batchGetWxMaterialResults);
            pageResult.setTotalCount(itemContentDetailMap.size());
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<BatchGetWxMaterialResult>>  batchGetWxMaterialAndDraft(BatchGetWxMaterialArg arg) {
        PageResult<BatchGetWxMaterialResult> pageResult = new PageResult<>();
        List<BatchGetWxMaterialResult> batchGetWxMaterialResults = Lists.newArrayList();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(batchGetWxMaterialResults);
        if (StringUtils.isBlank(arg.getName())) {
            BatchGetMaterialResult batchGetMaterialResult;
            BatchGetMaterialResult batchGetDraftResult;
            BatchGetMaterialResult  batchGetDraftResult1 = queryDraftMaterialList(arg.getWxAppId(), arg.getEa(), 0, arg.getPageSize());
            BatchGetMaterialResult  batchGetMaterialResult1 = queryMaterialList(arg.getWxAppId(), arg.getEa(), 0,10);
            if(batchGetDraftResult1.getTotalCount()>=arg.getPageNum() * arg.getPageSize()){
                batchGetDraftResult = queryDraftMaterialList(arg.getWxAppId(), arg.getEa(), ((arg.getPageNum() - 1) * arg.getPageSize()), arg.getPageSize());
                conversionMaterialToMarketingResult(batchGetDraftResult.getItem(), batchGetWxMaterialResults);
            }else{
                if(batchGetDraftResult1.getTotalCount()>(arg.getPageNum() - 1) * arg.getPageSize()){
                    batchGetDraftResult = queryDraftMaterialList(arg.getWxAppId(), arg.getEa(), ((arg.getPageNum() - 1) * arg.getPageSize()), arg.getPageSize());
                    conversionMaterialToMarketingResult(batchGetDraftResult1.getItem(), batchGetWxMaterialResults);
                    batchGetMaterialResult = queryMaterialList(arg.getWxAppId(), arg.getEa(), 0,arg.getPageNum() * arg.getPageSize()-batchGetDraftResult.getTotalCount());
                    conversionMaterialToMarketingResult(batchGetMaterialResult.getItem(), batchGetWxMaterialResults);
                }else{
                    batchGetMaterialResult = queryMaterialList(arg.getWxAppId(), arg.getEa(),(arg.getPageNum()-1) * arg.getPageSize()-batchGetDraftResult1.getTotalCount() ,arg.getPageSize());
                    conversionMaterialToMarketingResult(batchGetMaterialResult.getItem(), batchGetWxMaterialResults);
                }
            }
            if ((batchGetDraftResult1 == null && batchGetMaterialResult1==null) || ( CollectionUtils.isEmpty(batchGetDraftResult1.getItem())&&CollectionUtils.isEmpty(batchGetMaterialResult1.getItem()))) {
                return Result.newSuccess(pageResult);
            }
            pageResult.setTotalCount(batchGetMaterialResult1.getTotalCount()+batchGetDraftResult1.getTotalCount());
        }
        else {
            // 通过名称查询 素材id -> id下素材详情
            List<WxMaterialDetailEntity> wxMaterialDetailEntities;
                wxMaterialDetailEntities = wxMaterialDetailDao.queryWxMaterialByName(arg.getEa(), arg.getWxAppId(), arg.getName());
            if (CollectionUtils.isEmpty(wxMaterialDetailEntities)) {
                return Result.newSuccess(pageResult);
            }
            Map<String, BatchGetMaterialResult.ItemContentDetail> itemContentDetailMap = Maps.newConcurrentMap();
            CountDownLatch countDownLatch = new CountDownLatch(wxMaterialDetailEntities.size());
            for (WxMaterialDetailEntity wxMaterialDetailEntity : wxMaterialDetailEntities) {
                ThreadPoolUtils.execute(() -> {
                    try {
                        //草稿箱有
                        ItemContentDetail itemContentDraft = queryDraftDetail(wxMaterialDetailEntity.getWxAppId(), wxMaterialDetailEntity.getEa(), wxMaterialDetailEntity.getMediaId());
                        if(itemContentDraft != null){
                            itemContentDetailMap.put(wxMaterialDetailEntity.getMediaId(), itemContentDraft);
                        }
                        //素材箱有
                        ItemContentDetail itemContentDetail = queryMaterialDetail(wxMaterialDetailEntity.getWxAppId(), wxMaterialDetailEntity.getEa(), wxMaterialDetailEntity.getMediaId());
                        if(itemContentDetail != null){
                            itemContentDetailMap.put(wxMaterialDetailEntity.getMediaId(), itemContentDetail);
                        }
                    } catch (Exception e) {
                        log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterialAndDraft queryDraftDetail error e:", e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
            try {
                countDownLatch.await(15, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterialAndDraft error e:", e);
            }
            List<BatchGetMaterialResult.ItemDetail> wxResult = Lists.newArrayList();
            Map<String, BatchGetMaterialResult.ItemContentDetail> sortMap = itemContentDetailMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                            (oleValue, newValue) -> oleValue, LinkedHashMap::new));
            sortMap.entrySet().stream().limit(sortMap.size()).skip((arg.getPageNum()-1)*arg.getPageSize());
            for (Map.Entry<String, BatchGetMaterialResult.ItemContentDetail> entry : sortMap.entrySet()) {
                BatchGetMaterialResult.ItemDetail itemDetail = new BatchGetMaterialResult.ItemDetail();
                itemDetail.setMediaId(entry.getKey());
                itemDetail.setContent(entry.getValue());
                wxResult.add(itemDetail);
            }
            conversionMaterialToMarketingResult(wxResult, batchGetWxMaterialResults);
             pageResult.setTotalCount(wxResult.size());
        }
        return Result.newSuccess(pageResult);
    }


    @Override
    public Result updateWxMaterial(UpdateWxMaterialArg arg) {
        ThreadPoolUtils.execute(() -> {
            List<BatchGetMaterialResult.ItemDetail> allItemDetail = Lists.newArrayList();
            Integer pageSize = 20;
            // 先取第一页
            BatchGetMaterialResult batchGetMaterialResult = queryMaterialList(arg.getWxAppId(), arg.getEa(), 0, pageSize);
            if (batchGetMaterialResult == null || CollectionUtils.isEmpty(batchGetMaterialResult.getItem())) {
                return;
            }
            allItemDetail.addAll(batchGetMaterialResult.getItem());
            if (batchGetMaterialResult.getTotalCount() > pageSize) {
                int pageCount = (batchGetMaterialResult.getTotalCount() + pageSize - 1) / pageSize;
                // 从第二页开始查询
                for (int i = 2; i <= pageCount; i++) {
                    BatchGetMaterialResult tempBatchGetMaterialResult = queryMaterialList(arg.getWxAppId(), arg.getEa(), (i - 1) * pageSize, pageSize);
                    if (tempBatchGetMaterialResult == null || CollectionUtils.isEmpty(tempBatchGetMaterialResult.getItem())) {
                        continue;
                    }
                    allItemDetail.addAll(tempBatchGetMaterialResult.getItem());
                }
            }
            if (CollectionUtils.isEmpty(allItemDetail)) {
                return;
            }
            // 循环更新/插入
            for (BatchGetMaterialResult.ItemDetail itemDetail : allItemDetail) {
                if (StringUtils.isBlank(itemDetail.getMediaId()) || itemDetail.getContent() == null || CollectionUtils.isEmpty(itemDetail.getContent().getNewsItems())) {
                    continue;
                }
                WxMaterialDetailEntity wxMaterialDetailEntity = new WxMaterialDetailEntity();
                wxMaterialDetailEntity.setId(UUIDUtil.getUUID());
                wxMaterialDetailEntity.setEa(arg.getEa());
                wxMaterialDetailEntity.setWxAppId(arg.getWxAppId());
                wxMaterialDetailEntity.setMediaId(itemDetail.getMediaId());
                List<String> names = itemDetail.getContent().getNewsItems().stream().map(NewsItem::getTitle).collect(Collectors.toList());
                wxMaterialDetailEntity.setName(String.join("#", names));
                wxMaterialDetailDao.upsertWxMaterialDetail(wxMaterialDetailEntity);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result updateWxDraft(UpdateWxMaterialArg arg) {
        ThreadPoolUtils.execute(() -> {
            List<BatchGetMaterialResult.ItemDetail> allItemDetail = Lists.newArrayList();
            Integer pageSize = 20;
            // 先取第一页
            //BatchGetMaterialResult batchGetMaterialResult = queryDraftMaterialList("wx2e8a14a356c46c27", "74164", 0, 10);
            BatchGetMaterialResult batchGetMaterialResult = queryDraftMaterialList(arg.getWxAppId(), arg.getEa(), 0, pageSize);
            if (batchGetMaterialResult == null || CollectionUtils.isEmpty(batchGetMaterialResult.getItem())) {
                return;
            }
            allItemDetail.addAll(batchGetMaterialResult.getItem());
            if (batchGetMaterialResult.getTotalCount() > pageSize) {
                int pageCount = (batchGetMaterialResult.getTotalCount() + pageSize - 1) / pageSize;
                // 从第二页开始查询
                for (int i = 2; i <= pageCount; i++) {
                    BatchGetMaterialResult tempBatchGetMaterialResult = queryDraftMaterialList(arg.getWxAppId(), arg.getEa(), (i - 1) * pageSize, pageSize);
                    if (tempBatchGetMaterialResult == null || CollectionUtils.isEmpty(tempBatchGetMaterialResult.getItem())) {
                        continue;
                    }
                    allItemDetail.addAll(tempBatchGetMaterialResult.getItem());
                }
            }
            if (CollectionUtils.isEmpty(allItemDetail)) {
                return;
            }
            // 循环更新/插入
            for (BatchGetMaterialResult.ItemDetail itemDetail : allItemDetail) {
                if (StringUtils.isBlank(itemDetail.getMediaId()) || itemDetail.getContent() == null || CollectionUtils.isEmpty(itemDetail.getContent().getNewsItems())) {
                    continue;
                }
                WxMaterialDetailEntity wxMaterialDetailEntity = new WxMaterialDetailEntity();
                wxMaterialDetailEntity.setId(UUIDUtil.getUUID());
                wxMaterialDetailEntity.setEa(arg.getEa());
                wxMaterialDetailEntity.setWxAppId(arg.getWxAppId());
                wxMaterialDetailEntity.setMediaId(itemDetail.getMediaId());
                List<String> names = itemDetail.getContent().getNewsItems().stream().map(NewsItem::getTitle).collect(Collectors.toList());
                wxMaterialDetailEntity.setName(String.join("#", names));
                wxMaterialDetailDao.upsertWxMaterialDetail(wxMaterialDetailEntity);
            }
            //updateWxMaterial(arg);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }


    @Override
    public Result<BatchGetWxMaterialResult> queryWxMaterialByMediaId(QueryWxMaterialByMediaIdArg arg) {
        String wxAppId = arg.getWxAppId();
        if (StringUtils.isBlank(wxAppId) && StringUtils.isNotEmpty(arg.getAppId())) {
            Optional<String> optional = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(arg.getAppId());
            if (optional.isPresent()) {
                wxAppId = optional.get();
            }
        }
        if (StringUtils.isBlank(wxAppId)) {
            log.warn("WxOfficialAccountsServiceImpl.queryWxMaterialByMediaId wxAppId is null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        BatchGetMaterialResult.ItemContentDetail itemContentDetail = queryMaterialDetail(wxAppId, arg.getEa(), arg.getMediaId());
        if (itemContentDetail == null) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        BatchGetMaterialResult.ItemDetail itemDetail = new BatchGetMaterialResult.ItemDetail();
        itemDetail.setMediaId(arg.getMediaId());
        itemDetail.setContent(itemContentDetail);
        BatchGetWxMaterialResult batchGetWxMaterialResult = conversionMaterialToMarketingResult(itemDetail);
        return Result.newSuccess(batchGetWxMaterialResult);
    }

    @Override
    public Result<BatchGetWxMaterialResult> queryWxDraftlByDraftId(QueryWxMaterialByMediaIdArg arg) {
        String wxAppId = arg.getWxAppId();
        if (StringUtils.isBlank(wxAppId) && StringUtils.isNotEmpty(arg.getAppId())) {
            Optional<String> optional = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(arg.getAppId());
            if (optional.isPresent()) {
                wxAppId = optional.get();
            }
        }
        if (StringUtils.isBlank(wxAppId)) {
            log.warn("WxOfficialAccountsServiceImpl.queryWxMaterialByMediaId wxAppId is null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
       // BatchGetMaterialResult.ItemContentDetail itemContentDetail = queryMaterialDetail(wxAppId, arg.getEa(), arg.getMediaId());
        BatchGetMaterialResult.ItemContentDetail itemContentDetail = queryDraftDetail(wxAppId, arg.getEa(), arg.getMediaId());
        if (itemContentDetail == null) {
            return Result.newError(SHErrorCode.WX_OFFICIAL_NOT_SUPPORT);
        }
        BatchGetMaterialResult.ItemDetail itemDetail = new BatchGetMaterialResult.ItemDetail();
        itemDetail.setMediaId(arg.getMediaId());
        itemDetail.setContent(itemContentDetail);
        BatchGetWxMaterialResult batchGetWxMaterialResult = conversionMaterialToMarketingResult(itemDetail);
        return Result.newSuccess(batchGetWxMaterialResult);
    }

    @Override
    public Result<String> bindOfficialWebsite(BindOfficialWebsiteArg arg) {
        // sceneId调互联接口获取二维码详情
        QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
        queryStoreQrCodeArg.setAppId(arg.getAppId());
        queryStoreQrCodeArg.setSceneId(Long.valueOf(arg.getId()));
        queryStoreQrCodeArg.setEnterpriseAccount(arg.getEa());
        ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
        if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
            log.info("WxOfficialAccountsServiceImpl bindOfficialWebsite wechatQrCodeRestService.queryStoreQrCode fail, pagerModelResult:{}", pagerModelResult);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
        if (qrCodeResult == null) {
            log.info("WxOfficialAccountsServiceImpl bindOfficialWebsite wechatQrCodeRestService.queryStoreQrCode, qrCodeResult is null");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        WxOfficialAccountsQrCodeEntity qrCodeEntity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(String.valueOf(qrCodeResult.getId()));
        if (qrCodeEntity == null) {
            FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(arg.getEa(), arg.getFsUserId());
            if (fsEmployeeMsg == null) {
                log.warn("WxOfficialAccountsServiceImpl bindOfficialWebsite fsEmployeeMsg is null, arg:{}", arg);
                return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
            }
            String doBindFsUserName = org.apache.commons.lang3.StringUtils.isEmpty(fsEmployeeMsg.getFullName()) ? fsEmployeeMsg.getName() : fsEmployeeMsg.getFullName();
            WxOfficialAccountsQrCodeEntity entity = BeanUtil.copy(qrCodeResult, WxOfficialAccountsQrCodeEntity.class);
            entity.setDoBindFsUserId(arg.getFsUserId());
            entity.setEa(arg.getEa());
            entity.setDoBindFsUserName(doBindFsUserName);
            entity.setIsBindWebsite(1);
            entity.setUpdateBindStatusTime(new Date());
            entity.setSceneId(qrCodeResult.getId());
            entity.setId(UUIDUtil.getUUID());
            entity.setScanCount(0);
            entity.setSubscribeCount(0);
            entity.setWxAppId(qrCodeResult.getAppId());
            entity.setPlatformAppId(arg.getAppId());
            wxOfficialAccountsQrCodeDAO.insertWxOfficialAccountsQrCode(entity);
        } else {
            wxOfficialAccountsQrCodeDAO.bindOfficialWebsiteById(arg.getId());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> unbindOfficialWebsite(UnBindOfficialWebsiteArg arg) {
        WxOfficialAccountsQrCodeEntity qrCodeEntity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(arg.getId());
        if (qrCodeEntity == null) {
            log.warn("WxOfficialAccountsServiceImpl unbindOfficialWebsite qrCodeEntity is null, arg:{}", arg);
            return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
        }
        wxOfficialAccountsQrCodeDAO.unBindOfficialWebsiteById(arg.getId());
        return Result.newSuccess();
    }

    @Override
    public Result<List<OfficialWebsiteWxQrCodeResult>> queryOfficialWebsiteWxQrCode(QueryOfficialWebsiteWxQrCodeArg arg) {
        List<OfficialWebsiteWxQrCodeResult> resultList= Lists.newArrayList();
        List<WxOfficialAccountsQrCodeEntity> qrCodeEntities = wxOfficialAccountsQrCodeDAO.queryQrCodeByEaAndBindStatus(arg.getEa());
        if (CollectionUtils.isEmpty(qrCodeEntities)) {
            return Result.newSuccess(resultList);
        }
        qrCodeEntities.forEach(entity -> {
            OfficialWebsiteWxQrCodeResult result = BeanUtil.copy(entity, OfficialWebsiteWxQrCodeResult.class);
            result.setUpdateBindStatusTime(DateUtil.format(entity.getUpdateBindStatusTime(), "yyyy-MM-dd HH:mm"));
            resultList.add(result);
        });
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<OfficialWebsiteWxQrCodeResult> createOfficialWebsiteWxQrCode(CreateOfficialWebsiteWxQrCodeArg arg) {
        if (!arg.checkBrowserUserId()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        WxOfficialAccountsQrCodeEntity qrCodeEntity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(arg.getSceneId());
        if (qrCodeEntity == null) {
            log.warn("WxOfficialAccountsServiceImpl createOfficialWebsiteWxQrCode qrCodeEntity is null, arg:{}", arg);
            return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
        }

        String newSceneId = UUIDUtil.getUUID();
        WxServiceQrCodeOfficialWebsiteRelationEntity entity =  wxServiceQrCodeOfficialWebsiteRelationDAO.getByMainScenIdAndBrowserUserId(qrCodeEntity.getEa(), qrCodeEntity.getId(), arg.getBrowserUserId());
        if (entity == null) {
            WxServiceQrCodeOfficialWebsiteRelationEntity newEntity = new WxServiceQrCodeOfficialWebsiteRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(qrCodeEntity.getEa());
            newEntity.setSceneId(newSceneId);
            newEntity.setBrowserUserId(arg.getBrowserUserId());
            newEntity.setMainSceneId(qrCodeEntity.getId());
            wxServiceQrCodeOfficialWebsiteRelationDAO.relateSceneIdAndBrowserUserId(newEntity);
        }

        // 获取ticket
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        wechatRequestDispatchArg.setEi(eieaConverter.enterpriseAccountToId(qrCodeEntity.getEa()));
        wechatRequestDispatchArg.setWxAppId(qrCodeEntity.getWxAppId());
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/qrcode/create?access_token=${access_token}");
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> scene = new HashMap<>();
        scene.put("scene_str",  newSceneId);
        scene.put("fsEa", qrCodeEntity.getEa());
        scene.put("appId", appId);
        scene.put("qrCodeName", qrCodeEntity.getQrCodeName());
        scene.put("responseMsg", qrCodeEntity.getResponseMsg());
        scene.put("tagNames", qrCodeEntity.getTagNames());
        scene.put("marketingEventId", qrCodeEntity.getMarketingEventId());
        scene.put("creator", qrCodeEntity.getCreator());
        map.put("scene", scene);
        body.put("action_name", "QR_STR_SCENE");
        body.put("action_info", map);
        body.put("expire_seconds", 2592000);
        wechatRequestDispatchArg.setBody(JSON.toJSONString(body));
        Map<String, Object> objectMap = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<HashMap<String, Object>>() {
        });
        if (objectMap == null) {
            return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
        }

        if (entity != null){
            wxServiceQrCodeOfficialWebsiteRelationDAO.updateScenIdById(entity.getEa(), entity.getId(), newSceneId);
        }
        log.info("createOfficialWebsiteWxQrCode wechatAccountManager.dispatch objectMap:{}", objectMap);
        String ticket = UrlEncoder.urlComponentEncode(objectMap.get("ticket"));
        String showUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        OfficialWebsiteWxQrCodeResult result = new OfficialWebsiteWxQrCodeResult();
        result.setFileUrl(showUrl);
        result.setShowUrl(showUrl);
        if (qrCodeEntity.getIsBindWebsiteLogin() != null && qrCodeEntity.getIsBindWebsiteLogin() > 0 ) {
            JSONObject memberInfo = new JSONObject();
            memberInfo.put("ea", qrCodeEntity.getEa());
            memberInfo.put("wxAppId", qrCodeEntity.getWxAppId());
            redisManager.setMemberInfo(arg.getBrowserUserId(), memberInfo);
        }
        if (StringUtils.isNotEmpty(qrCodeEntity.getMarketingEventId())) {
            ObjectData objectData = crmV2Manager.getDetail(qrCodeEntity.getEa(), SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), qrCodeEntity.getMarketingEventId());
            if (objectData != null) {
                arg.setMarketingEventId(qrCodeEntity.getMarketingEventId());
            }
        }
        marketingPromotionSourceArgObjectRelationManager.createEntity(qrCodeEntity.getEa(), newSceneId, arg);
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<OfficialWebsiteWxQrCodeResult>> queryTempOfficialWebsiteWxQrCode(QueryOfficialWebsiteWxQrCodeArg arg) {
        List<OfficialWebsiteWxQrCodeResult> resultList= Lists.newArrayList();
        List<WxOfficialAccountsQrCodeEntity> qrCodeEntities = wxOfficialAccountsQrCodeDAO.queryTempQrCodeByEa(arg.getEa());
        if (CollectionUtils.isEmpty(qrCodeEntities)) {
            return Result.newSuccess(resultList);
        }
        qrCodeEntities.forEach(entity -> {
            OfficialWebsiteWxQrCodeResult result = BeanUtil.copy(entity, OfficialWebsiteWxQrCodeResult.class);
            resultList.add(result);
        });
        return Result.newSuccess(resultList);
    }

    /**
     * @param browserUserId
     * @return
     */
    @Override
    public Result<CustomizeFormDataEnrollResult> getMemberInfo(String browserUserId) {
        JSONObject compositeObject = redisManager.getMemberInfo(browserUserId);
        if (compositeObject != null) {
            String ea = compositeObject.getString("ea");
            String wxAppId = compositeObject.getString("wxAppId");
            String wxOpenId = compositeObject.getString("wxOpenId");
            // 待扫码
            if (StringUtils.isBlank(wxOpenId)) {
                return Result.newError(SHErrorCode.WX_QR_CODE_NOT_SCAN);
            }
            String memberId = wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea, wxAppId, wxOpenId);
            if (StringUtils.isNotEmpty(memberId)) {
                // 非首次登录
                CustomizeFormDataEnrollResult customizeFormDataEnrollResult = doConvertMemberIdToCookieResult(ea, memberId);
                try {
                    ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
                    if (objectData != null) {
                        Object phone = objectData.get("phone");
                        customizeFormDataEnrollResult.setPhone(phone != null ? phone.toString() : null);
                    }
                } catch (Exception ignor) {
                }
                return Result.newSuccess(customizeFormDataEnrollResult);
            } else {
                CustomizeFormDataEnrollResult memberInfo = compositeObject.getObject("memberInfo", CustomizeFormDataEnrollResult.class);
                if (memberInfo == null) {
                    // 未绑定
                    return Result.newError(SHErrorCode.WX_USE_NOT_BIND_PHONE);
                }
            }
        }
        return Result.newError(SHErrorCode.WX_QR_CODE_NOT_FOUND);
    }

    @Override
    public Result<String> bindOfficialWebsiteLogin(BindOfficialWebsiteArg arg) {
        String qrCodeId = null;
        if (StringUtils.isNotEmpty(arg.getAppId()) && StringUtils.isNotEmpty(arg.getId())) {
            // sceneId调互联接口获取二维码详情
            QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
            queryStoreQrCodeArg.setAppId(arg.getAppId());
            queryStoreQrCodeArg.setSceneId(Long.valueOf(arg.getId()));
            queryStoreQrCodeArg.setEnterpriseAccount(arg.getEa());
            ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
            if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
                log.info("WxOfficialAccountsServiceImpl bindOfficialWebsiteLogin wechatQrCodeRestService.queryStoreQrCode fail, pagerModelResult:{}", pagerModelResult);
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
            if (qrCodeResult == null) {
                log.info("WxOfficialAccountsServiceImpl bindOfficialWebsiteLogin wechatQrCodeRestService.queryStoreQrCode, qrCodeResult is null");
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            WxOfficialAccountsQrCodeEntity qrCodeEntity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(String.valueOf(qrCodeResult.getId()));
            if (qrCodeEntity == null) {
                FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(arg.getEa(), arg.getFsUserId());
                if (fsEmployeeMsg == null) {
                    log.warn("WxOfficialAccountsServiceImpl bindOfficialWebsiteLogin fsEmployeeMsg is null, arg:{}", arg);
                    return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
                }
                wxOfficialAccountsQrCodeDAO.unBindOfficialWebsiteLoginById(arg.getEa());
                String doBindFsUserName = org.apache.commons.lang3.StringUtils.isEmpty(fsEmployeeMsg.getFullName()) ? fsEmployeeMsg.getName() : fsEmployeeMsg.getFullName();
                qrCodeEntity = BeanUtil.copy(qrCodeResult, WxOfficialAccountsQrCodeEntity.class);
                qrCodeEntity.setDoBindFsUserId(arg.getFsUserId());
                qrCodeEntity.setEa(arg.getEa());
                qrCodeEntity.setDoBindFsUserName(doBindFsUserName);
                qrCodeEntity.setUpdateBindStatusTime(new Date());
                qrCodeEntity.setSceneId(qrCodeResult.getId());
                qrCodeEntity.setId(UUIDUtil.getUUID());
                qrCodeEntity.setScanCount(0);
                qrCodeEntity.setSubscribeCount(0);
                qrCodeEntity.setWxAppId(qrCodeResult.getAppId());
                qrCodeEntity.setPlatformAppId(arg.getAppId());
                qrCodeEntity.setIsBindWebsiteLogin(1);
                wxOfficialAccountsQrCodeDAO.insertWxOfficialAccountsQrCode(qrCodeEntity);
            }
            qrCodeId = qrCodeEntity.getId();
        }
        // 保存
        JSONObject settingData = wxOfficialAccountsQrCodeDAO.getSettingByEa(arg.getEa());
        if (settingData == null) {
            wxOfficialAccountsQrCodeDAO.saveSetting(UUIDUtil.getUUID(), arg.getEa(), qrCodeId, arg.getPreviewSetting(), arg.getFsUserId());
        } else {
            wxOfficialAccountsQrCodeDAO.updateSettingById(settingData.getString("id"), arg.getEa(), qrCodeId, arg.getPreviewSetting(), arg.getFsUserId());
        }
        return Result.newSuccess();
    }

    @Override
    public Result unBindOfficialWebsiteLogin(String ea, String id) {
        JSONObject settingData = wxOfficialAccountsQrCodeDAO.getSettingByEa(ea);
        if (settingData == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        wxOfficialAccountsQrCodeDAO.unBindSettingById(id);
        return Result.newSuccess();
    }

    @Override
    public Result<OfficialWebsiteWxQrCodeResult> queryOfficialWebsiteLoginWxQrCode(QueryOfficialWebsiteWxQrCodeArg arg) {
        OfficialWebsiteWxQrCodeResult result = new OfficialWebsiteWxQrCodeResult();
        JSONObject settingData = wxOfficialAccountsQrCodeDAO.getSettingByEa(arg.getEa());
        if (settingData != null) {
            String qrCodeId = settingData.getString("qr_code_id");
            if (StringUtils.isNotEmpty(qrCodeId)) {
                WxOfficialAccountsQrCodeEntity entity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(qrCodeId);
                if (entity != null) {
                    result = BeanUtil.copy(entity, OfficialWebsiteWxQrCodeResult.class);
                    result.setUpdateBindStatusTime(DateUtil.format(entity.getUpdateBindStatusTime(), "yyyy-MM-dd HH:mm"));
                    result.setAppId(entity.getPlatformAppId());
                    result.setSceneId(entity.getSceneId());
                }
            }
            result.setPreviewSetting(settingData.getString("preview_setting"));
        } else {
            return Result.newError(SHErrorCode.WX_QR_CODE_SETTING_NOT_FOUND);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<OfficialWebsiteWxQrCodeResult> createOfficialWebsiteLoginWxQrCode(CreateOfficialWebsiteWxQrCodeArg arg) {
        WxOfficialAccountsQrCodeEntity qrCodeEntity = wxOfficialAccountsQrCodeDAO.queryQrCodeById(arg.getSceneId());
        if (qrCodeEntity.getIsBindWebsiteLogin() == null || qrCodeEntity.getIsBindWebsiteLogin() == 0 ) {
            return Result.newError(SHErrorCode.WX_QR_CODE_SETTING_NOT_FOUND);
        }
        return this.createOfficialWebsiteWxQrCode(arg);
    }

    @Override
    public Result<WeChatTemplateMessageData.TemplateMessageDatas> getNearlyExternalConfig(String ea, String weChatOfficialTemplateId) {
        MarketingActivityExternalConfigEntity weChatOfficialTemplate = marketingActivityExternalConfigDao.getNearlyExternalConfigByEaAndWeChatOfficialTemplateId(ea, weChatOfficialTemplateId);
        if(weChatOfficialTemplate!=null && weChatOfficialTemplate.getExternalConfig().getWeChatTemplateMessageVO()!=null&&weChatOfficialTemplate.getExternalConfig().getWeChatTemplateMessageVO().getTemplateMessageDatas()!=null){
            WeChatTemplateMessageData.TemplateMessageDatas templateMessageDatas = weChatOfficialTemplate.getExternalConfig().getWeChatTemplateMessageVO().getTemplateMessageDatas();
            return Result.newSuccess(templateMessageDatas);
        }
        return null;
    }

    private CustomizeFormDataEnrollResult doConvertMemberIdToCookieResult(String ea, String memberId) {
        CustomizeFormDataEnrollResult result = new CustomizeFormDataEnrollResult();
        DateTime dateTime = new DateTime();
        dateTime = dateTime.plusDays(30);
        long feCookieExpireAtSeconds = dateTime.getMillis() / 1000;
        result.setMemberCookieExpireAtMillisecond((int)feCookieExpireAtSeconds);
        dateTime = dateTime.plusHours(1);
        result.setMemberCookieKey(CookieConstant.MEMBER_COOKIE_PREFIX + ea);
        result.setMemberCookie(new MemberCookieInfo(memberId, dateTime.getMillis()).aesEncode());
        result.setMemberCookieDomain(cookieDomain);
        return result;
    }


    private BatchGetMaterialResult queryMaterialList(String wxAppId, String ea, Integer offset, Integer count) {
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/material/batchget_material?access_token=${access_token}");
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setEi(eIEAConverter.enterpriseAccountToId(ea));
        wechatRequestDispatchArg.setWxAppId(wxAppId);
        BatchGetMaterialArg batchGetMaterialArg = new BatchGetMaterialArg();
        batchGetMaterialArg.setType("news");
        batchGetMaterialArg.setCount(count);
        batchGetMaterialArg.setOffset(offset);
        wechatRequestDispatchArg.setBody(GsonUtil.getGson().toJson(batchGetMaterialArg));
        BatchGetMaterialResult batchGetMaterialResult = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<BatchGetMaterialResult>() {});
        if (batchGetMaterialResult == null || CollectionUtils.isEmpty(batchGetMaterialResult.getItem())) {
            return null;
        }
        return batchGetMaterialResult;
    }
   private BatchGetMaterialResult queryDraftMaterialList(String wxAppId, String ea, Integer offset, Integer count) {
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/draft/batchget?access_token=${access_token}");
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setEi(eIEAConverter.enterpriseAccountToId(ea));
        wechatRequestDispatchArg.setWxAppId(wxAppId);
        BatchGetMaterialArg batchGetMaterialArg = new BatchGetMaterialArg();
        batchGetMaterialArg.setCount(count);
        batchGetMaterialArg.setOffset(offset);
        wechatRequestDispatchArg.setBody(GsonUtil.getGson().toJson(batchGetMaterialArg));
        BatchGetMaterialResult batchGetMaterialResult = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<BatchGetMaterialResult>() {});
        if (batchGetMaterialResult == null || CollectionUtils.isEmpty(batchGetMaterialResult.getItem())) {
            return null;
        }
        return batchGetMaterialResult;
    }

    private BatchGetMaterialResult.ItemContentDetail queryDraftDetail(String wxAppId, String ea, String mediaId) {
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        Map<String, Object> requestParam = Maps.newHashMap();
        requestParam.put("media_id", mediaId);
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/draft/get?access_token=${access_token}");
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setEi(eIEAConverter.enterpriseAccountToId(ea));
        wechatRequestDispatchArg.setWxAppId(wxAppId);
        wechatRequestDispatchArg.setBody(GsonUtil.getGson().toJson(requestParam));
        BatchGetMaterialResult.ItemContentDetail itemContentDetail = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<BatchGetMaterialResult.ItemContentDetail>() {
        });
        if (itemContentDetail == null || CollectionUtils.isEmpty(itemContentDetail.getNewsItems())) {
            return null;
        }
        return itemContentDetail;
    }

    private BatchGetMaterialResult.ItemContentDetail queryMaterialDetail(String wxAppId, String ea, String mediaId) {
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        Map<String, Object> requestParam = Maps.newHashMap();
        requestParam.put("media_id", mediaId);
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/material/get_material?access_token=${access_token}");
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setEi(eIEAConverter.enterpriseAccountToId(ea));
        wechatRequestDispatchArg.setWxAppId(wxAppId);
        wechatRequestDispatchArg.setBody(GsonUtil.getGson().toJson(requestParam));
        BatchGetMaterialResult.ItemContentDetail itemContentDetail = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<BatchGetMaterialResult.ItemContentDetail>() {
        });
        if (itemContentDetail == null || CollectionUtils.isEmpty(itemContentDetail.getNewsItems())) {
            return null;
        }
        return itemContentDetail;
    }

    private void conversionMaterialToMarketingResult(List<BatchGetMaterialResult.ItemDetail> wxResult, List<BatchGetWxMaterialResult> batchGetWxMaterialResults) {
        for (BatchGetMaterialResult.ItemDetail wxItemDetail : wxResult) {
            BatchGetWxMaterialResult batchGetWxMaterialResult = new BatchGetWxMaterialResult();
            BatchGetWxMaterialResult.ItemDetail itemDetail = new BatchGetWxMaterialResult.ItemDetail();
            itemDetail.setMediaId(wxItemDetail.getMediaId());
            itemDetail.setUpdateTime(itemDetail.getUpdateTime());
            BatchGetWxMaterialResult.ItemContentDetail itemContentDetail = new BatchGetWxMaterialResult.ItemContentDetail();
            itemContentDetail.setCreateTime(wxItemDetail.getContent().getCreateTime());
            itemContentDetail.setUpdateTime(wxItemDetail.getContent().getUpdateTime());
            List<BatchGetWxMaterialResult.NewsItem> newsItems = BeanUtil.copy(wxItemDetail.getContent().getNewsItems(), BatchGetWxMaterialResult.NewsItem.class);
            itemContentDetail.setNewsItems(newsItems);
            itemDetail.setContent(itemContentDetail);
            batchGetWxMaterialResult.setItemDetail(itemDetail);
            batchGetWxMaterialResults.add(batchGetWxMaterialResult);
        }
    }

    private BatchGetWxMaterialResult conversionMaterialToMarketingResult(BatchGetMaterialResult.ItemDetail wxResult) {
        BatchGetWxMaterialResult batchGetWxMaterialResult = new BatchGetWxMaterialResult();
        BatchGetWxMaterialResult.ItemDetail itemDetail = new BatchGetWxMaterialResult.ItemDetail();
        itemDetail.setMediaId(wxResult.getMediaId());
        itemDetail.setUpdateTime(itemDetail.getUpdateTime());
        BatchGetWxMaterialResult.ItemContentDetail itemContentDetail = new BatchGetWxMaterialResult.ItemContentDetail();
        itemContentDetail.setCreateTime(wxResult.getContent().getCreateTime());
        itemContentDetail.setUpdateTime(wxResult.getContent().getUpdateTime());
        List<BatchGetWxMaterialResult.NewsItem> newsItems = BeanUtil.copy(wxResult.getContent().getNewsItems(), BatchGetWxMaterialResult.NewsItem.class);
        itemContentDetail.setNewsItems(newsItems);
        itemDetail.setContent(itemContentDetail);
        batchGetWxMaterialResult.setItemDetail(itemDetail);
        return batchGetWxMaterialResult;
    }

    @Override
    public Result<Void> registerMenuToSfa(String ea) {
        List<MarketingWxServiceEntity> entityList;
        if (StringUtils.isNotEmpty(ea)) {
            entityList = marketingWxServiceDao.listByEa(ea);
        } else {
            entityList  = marketingWxServiceDao.listAll();
        }
        if (CollectionUtils.isEmpty(entityList)) {
            return Result.newSuccess();
        }
        List<MarketingWxServiceEntity> finalList = entityList;
        ThreadPoolUtils.execute(() -> {
            for (MarketingWxServiceEntity entity : finalList) {
                if (!appVersionManager.isCurrentCloud(entity.getEa())) {
                    // 不是当前云 直接不处理 主要是处理新华三已经迁移到了紫光云，但是纷享云还查出来
                    continue;
                }
               try {
                   FsUserVO fsUserVO = new FsUserVO(entity.getEa(), SuperUserConstants.USER_ID);
                   ModelResult<List<WeChatMenuFormData>> result = weChatCustomerMenuRestService.queryCustomerMenu(fsUserVO, entity.getAppId());
                   log.info("查询公众号菜单成功, entity: {} result: {}", entity, result);
                   if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getResult())) {
                      continue;
                   }
                   for (WeChatMenuFormData weChatMenuFormData : result.getResult()) {
                       registerMenu(entity.getEa(), entity.getAppId(), weChatMenuFormData);
                   }
               } catch (Exception e) {
                   log.error("查询公众号菜单异常, entity: {}", entity, e);
               }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    private void registerMenu(String ea, String appId, WeChatMenuFormData weChatMenuFormData) {
        String key = MD5Util.md5String(weChatMenuFormData.getName());
        // 这里的幂等数据30天前的数据会被删除 30天后会被重复注册  SFA那边没有接口支持查询 重复注册也注册不上，所以这里过滤一下吧
        if (!idempotentRecordManager.existRecord(key) && idempotentRecordManager.insertRecord(key)) {
            integralServiceManager.syncRegisterMaterial(ea, CategoryApiNameConstant.OFFICIAL_ACCOUNT,
                    weChatMenuFormData.getName(), weChatMenuFormData.getName());
            log.info("注册菜单到sfa,appId:{}, menu: {}", appId, weChatMenuFormData.getName());
        }
        List<WeChatMenuFormData> children = weChatMenuFormData.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        for (WeChatMenuFormData child : children) {
            registerMenu(ea, appId, child);
        }
    }
}
