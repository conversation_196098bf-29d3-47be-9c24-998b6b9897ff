/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.eservice.common.utils.SpringContextUtil;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg.WeChatServiceMarketingActivityVO;
import com.facishare.marketing.api.data.usermarketingaccounttag.TagData;
import com.facishare.marketing.api.result.MarketingWxServiceResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.FirstTag;
import com.facishare.marketing.api.result.usermarketingaccounttag.LikeTagByNameResult;
import com.facishare.marketing.api.service.FsBindService;
import com.facishare.marketing.api.service.MigrateService;
import com.facishare.marketing.api.service.WxOfficialAccountsService;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.mail.MailService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.api.service.qywx.QywxUserService;
import com.facishare.marketing.api.service.qywx.QywxStaffService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.api.vo.WeChatTemplateMessageData;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.RoleConstant;
import com.facishare.marketing.common.contstant.TagModelSceneConstants;
import com.facishare.marketing.common.contstant.TagRuleRuleContants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.coupon.VisibilityEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FlexibleJson;
import com.facishare.marketing.common.typehandlers.value.RuleGroup;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.ListUtil;
import com.facishare.marketing.common.util.ReflectionUtil;
import com.facishare.marketing.common.util.ReplaceUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.mail.MailSendReplyDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.marketingflow.MarketingActivityNodeAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowTaskDao;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteEventAttributesDAO;
import com.facishare.marketing.provider.dao.officialWebsite.OfficialWebsiteTrackDAO;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSignatureDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dto.ArticleEntityDTO;
import com.facishare.marketing.provider.dto.ProductEntityDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonOfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.mail.MailSendReplyEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingActivityNodeAdditionalConfigEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowTaskEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEventAttributesEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteTrackEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpEaMappingEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.innerArg.qywx.AyncQywxBindInfoArg;
import com.facishare.marketing.provider.innerData.MigrateTagData;
import com.facishare.marketing.provider.innerData.MigrateTagName;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingHandlerActionManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.manager.baidu.UtmDataManger;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.conference.ResetConferenceManager;
import com.facishare.marketing.provider.manager.coupon.PublicCouponManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.provider.manager.customizeFormData.ResetCustomizeFormDataManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.marketingactivity.WeChatServiceManager;
import com.facishare.marketing.provider.manager.metadata.PublicMetadataManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.marketing.provider.mq.handler.processor.LicenseEventMessageProcessor;
import com.facishare.marketing.provider.remote.*;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.service.wxthirdplatform.WxThirdAuthServiceImpl;
import com.facishare.marketing.provider.util.PageUtil;
import com.facishare.marketing.script.manager.PaasDataManager;
import com.facishare.marketing.statistic.common.util.HashUtil;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.organization.adapter.api.permission.enums.role.SystemRoleEnum;
import com.facishare.organization.adapter.api.permission.model.GetEmployeeIdsByRoleCode;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.model.fscore.RunStatus;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.sender.api.enums.WeChatMsgTypeEnum;
import com.facishare.wechat.sender.api.result.NoticeDetailResult;
import com.facishare.wechat.sender.api.result.NoticeSendRecord;
import com.facishare.wechat.sender.api.service.NoticeService;
import com.facishare.wechat.union.common.result.PageObject;
import com.facishare.wechat.union.core.api.constant.CommonConstant;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.facishare.wechat.union.core.api.model.result.OuterServiceWechatDetailResult;
import com.facishare.wechat.union.core.api.model.vo.OuterServiceWechatVO;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.facishare.wechat.union.core.api.service.WechatUnionService;
import com.fxiaoke.bpmrestapi.arg.GetDefinitionListArg;
import com.fxiaoke.bpmrestapi.arg.IdArg;
import com.fxiaoke.bpmrestapi.arg.UpdateWorkflowDefinitionStatusArg;
import com.fxiaoke.bpmrestapi.result.BpmResult;
import com.fxiaoke.bpmrestapi.result.WorkflowDefinitionListResult;
import com.fxiaoke.bpmrestapi.service.BpmFlowApiService;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.contants.UserMarketingAccountContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.result.ActionBulkInvalidResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.FindDraftAndLayoutResult;
import com.fxiaoke.crmrestapi.result.MemberStatusResult;
import com.fxiaoke.crmrestapi.service.*;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import com.mysql.jdbc.StringUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static com.fxiaoke.crmrestapi.common.data.FieldDescribe.*;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;

@Slf4j
@Component("migrateService")
public class MigrateServiceImpl implements MigrateService {
    @Autowired
    private BpmFlowApiService bpmFlowApiService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private MarketingActivityService marketingActivityService;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private CustomizeFormDataServiceImpl customizeFormDataService;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private WeChatServiceMarketingActivityService weChatServiceMarketingActivityService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private IntegralServiceManager integralServiceManager;
    @ReloadableProperty("template.message.old.data.url")
    private String templateMessageOldDataUrl;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private MarketingActivityNodeAdditionalConfigDao marketingActivityNodeAdditionalConfigDao;
    @Autowired
    private MarketingFlowTaskDao marketingFlowTaskDao;
    @Autowired    private MarketingFlowDraftDao marketingFlowDraftDao;
    @Autowired
    private UserTagManager userTagManager;
    @Autowired
    private CardManager cardManager;
    @Autowired
    private MetadataTagDataService metadataTagDataService;
    @Autowired
    private MetadataTagService metadataTagService;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MarketingBehaviorObjDescribeManager marketingBehaviorObjDescribeManager;

    @Autowired
    private OfficialWebsiteTrackDAO officialWebsiteTrackDAO;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;

    @Autowired
    private MarketingEventRemoteManager marketingEventRemoteManager;

    @Autowired
    private WechatWorkExternalUserObjDescribeManager wechatWorkExternalUserObjDescribeManager;
    @Autowired
    private OtherObjectDescribeManager otherObjectDescribeManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private UserTagDao userTagDao;
    @Autowired
    private OfficialWebsiteManager officialWebsiteManager;
    @Autowired
    private UserMarketingTagService userMarketingTagService;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private HexagonOfficialWebsiteDAO hexagonOfficialWebsiteDAO;
    @Autowired
    private OperatorDao operatorDao;
    @Autowired
    private UserRoleDao userRoleDao;
    @Autowired
    private WxThirdAuthServiceImpl wxThirdAuthService;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private ObjectLayoutService objectLayoutService;
    @Autowired
    private SpreadChannelManager spreadChannelManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CampaignMergeDataResetManager campaignMergeDataResetManager;
    @Autowired
    private MailSendReplyDAO mailSendReplyDAO;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private OfficialWebsiteEventAttributesDAO officialWebsiteEventAttributesDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private WeChatServiceManager weChatServiceManager;
    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;
    @Autowired
    private MemberConfigDao memberConfigDao;
    @Autowired
    private MailService mailService;
    @Autowired
    private MarketingEventAmountStatisticDAO marketingEventAmountStatisticDAO;
    @Autowired
    private MarketingEventAmountStatisticManager marketingEventAmountStatisticManager;
    @Autowired
    private UtmDataManger utmDataManger;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private CrmDataAuthManager crmDataAuthManager;
    @Autowired
    private ResetCustomizeFormDataManager resetCustomizeFormDataManager;
    @Autowired
    private CustomizeMiniAppCardNavbarManager customizeMiniAppCardNavbarManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;
    @Autowired
    private ResetConferenceManager resetConferenceManager;
    @Autowired
    private ResetQywxAddressBookManager resetQywxAddressBookManager;
    @Autowired
    private WechatUnionService wechatUnionService;
    @Autowired
    private LicenseEventMessageProcessor handler;
    @Autowired
    private FsBindService fsBindService;
    @Autowired
    private QywxCorpEaMappingDAO qywxCorpEaMappingDAO;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private WxPublicPlatformAuthorizeComponentDao wxPublicPlatformAuthorizeComponentDao;
    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;
    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;
    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;
    @Autowired
    private WechatGroupUserObjDescribeManager wechatGroupUserObjDescribeManager;
    @Autowired
    private AccountManager accountManager;
    @ReloadableProperty("host")
    private String host;

    @Autowired
    private PaasDataManager paasDataManager;

    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;

    @Autowired
    private WechatAccountGroupStatisticsObjManager wechatAccountGroupStatisticsObjManager;

    @Autowired
    private WechatAccountStatisticsObjManager wechatAccountStatisticsObjManager;

    @Autowired
    private ContentPropagationDetailObjManager contentPropagationDetailObjManager;

    @Autowired
    private EmployeePromoteDetailObjManager employeePromoteDetailObjManager;

    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;

    @Autowired
    private SpreadTaskManager spreadTaskManager;

    @Autowired
    private QywxManager qywxManager;

    @Value("${qywx.crm.appid}")
    private String qywxCrmAppid;
    @Autowired
    private AdDataReturnObjDescribeManager adDataReturnBackObjDescribeManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private MwSmsSignatureDao mwSmsSignatureDao;

    @Autowired
    private SmsSendRecordObjManager smsSendRecordObjManager;

    @Autowired
    private MomentManager momentManager;

    @Autowired
    private QYWXMomentTaskDAO qywxMomentTaskDAO;

    @Autowired
    private MemberDescribeManager memberDescribeManager;
    @Autowired
    private QywxUserService qywxUserService;


    @Autowired
    private AdMarketingHandlerActionManager adMarketingHandlerActionManager;

    @Autowired
    private QywxStaffService qywxStaffService;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private WxOfficialAccountsService wxOfficialAccountsService;

    @Autowired
    private IdempotentRecordManager idempotentRecordManager;

    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;

    @Autowired
    private TencentAdMarketingManager tencentAdMarketingManager;

    @Autowired
    private HeadlinesAdMarketingManager headlinesAdMarketingManager;

    @Autowired
    private ThirdPlatformSecretManager thirdPlatformSecretManager;

    @Autowired
    private PublicMetadataManager publicMetadataManager;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CouponDistributionObjManager couponDistributionObjManager;

    @Autowired
    private CouponObjDescribeManager couponObjDescribeManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private PublicCouponManager publicCouponManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @ReloadableProperty("marketing_gray_list")
    private String marketingGrayList;

    @Override
    public Result<Map<String, Integer>> deleteAllMarketingFlowByEas(MigrateEasArg arg) {
        Map<String, Integer> resultMap = new HashMap<>();
        for (String ea : arg.getEas()) {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            GetDefinitionListArg getDefinitionListArg = new GetDefinitionListArg();
            getDefinitionListArg.setPageSize(200);
            getDefinitionListArg.setPageNumber(1);
            getDefinitionListArg.setSupportFlow("market");
            BpmResult<WorkflowDefinitionListResult> result = bpmFlowApiService.getDefinitionList(ei, -10000, getDefinitionListArg);
            result.getData().getOutlines().stream().forEach(outline -> {
                String id = outline.get("id").toString();
                UpdateWorkflowDefinitionStatusArg updateWorkflowDefinitionStatusArg = new UpdateWorkflowDefinitionStatusArg(id, false);
                updateWorkflowDefinitionStatusArg.setSupportFlow("market");
                bpmFlowApiService.updateDefinitionStatus(ei, -10000, updateWorkflowDefinitionStatusArg);
                bpmFlowApiService.deleteDefinition(ei, -10000, new IdArg(id, "market"));
            });
            resultMap.put(ea, result.getData().getTotalCount());
        }
        return Result.newSuccess(resultMap);
    }

    @Override
    public Result<Void> initMarketingFlowObjects(MigrateEasArg arg) {
        doMigrateEas(arg.getEas(), ea -> marketingCrmManager.initUserMarketingAccount(ea, -10000));
        doMigrateEas(arg.getEas(), ea -> marketingCrmManager.initMarketingProcess(ea, -10000));
        doMigrateEas(arg.getEas(), ea -> marketingCrmManager.initMarketingProcessLatencyResult(ea, -10000));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initUserMarketingAccountObj(MigrateEasArg arg) {
        doMigrateEas(arg.getEas(), ea -> marketingCrmManager.initUserMarketingAccount(ea, -10000));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initMarketingFlowObjectsForProEas() {
        List<String> proEas = listAllProEas().getData();
        doMigrateEas(proEas, (ea) -> marketingCrmManager.initUserMarketingAccount(ea, -10000));
        doMigrateEas(proEas, (ea) -> marketingCrmManager.initMarketingProcess(ea, -10000));
        doMigrateEas(proEas, (ea) -> marketingCrmManager.initMarketingProcessLatencyResult(ea, -10000));
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> listAllProEas() {
        List<String> eas = enterpriseMetaConfigDao.findEaAll();
        List<String> result = eas.stream().parallel().filter(Objects::nonNull).filter(ea -> {
            try {
                String version = appVersionManager.getCurrentAppVersion(ea);
                return VersionEnum.PRO.getVersion().equals(version) || VersionEnum.STREN.getVersion().equals(version);
            } catch (Exception e) {
                log.warn("Error at get version of ea:{}", ea, e);
                return false;
            }
        }).collect(Collectors.toList());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> migrateOldTemplateDataToMarketing(MigrateEasArg arg) {
        Map<Integer, List<String>> errorMap = new HashMap<>();
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        for (String ea : eas) {
            try {
                Integer migrateOldTemplateDataToMarketingStatus = doMigrateOneEaTemplateDataToMarketing(ea);
                errorMap.computeIfAbsent(migrateOldTemplateDataToMarketingStatus, k -> Lists.newArrayList());
                errorMap.get(migrateOldTemplateDataToMarketingStatus).add(ea);
            } catch (Exception e) {
                log.warn("MigrateOneEaTemplateDataToMarketing ea:{} result:fail exception ", ea, e);
                errorMap.get(MigrateOldTemplateDataToMarketingStatusEnum.OTHER_FAIL.getStatus()).add(ea);
            }
        }
        for (Integer integer : errorMap.keySet()) {
            log.warn("migrateOldTemplateDataToMarketing FINISH ,type = {} , eas:{}", integer, GsonUtil.toJson(errorMap.get(integer)));
        }
        return Result.newSuccess();
    }

    private Integer doMigrateOneEaTemplateDataToMarketing(String ea) {
        //初始化营销活动
        boolean isInit = marketingCrmManager.initMarketingActivity(ea);
        if (!isInit) {
            log.info("MigrateOneEaTemplateDataToMarketing ea:{} result:fail initMarketingActivity fail", ea);
            return MigrateOldTemplateDataToMarketingStatusEnum.INIT_MARKETING_ACTIVITY_FAIL.getStatus();
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        GetEmployeeIdsByRoleCode.Argument argument = new GetEmployeeIdsByRoleCode.Argument();
        argument.setEnterpriseId(ei);
        argument.setRoleCode(SystemRoleEnum.SYSTEM_MANAGEMENT.getRoleCode());
        argument.setCurrentEmployeeId(SuperUserConstants.USER_ID);
        GetEmployeeIdsByRoleCode.Result result = permissionService.getEmployeeIdsByRoleCode(argument);
        if (result == null || CollectionUtils.isEmpty(result.getEmployeeIds())) {
            log.info("MigrateOneEaTemplateDataToMarketing result:fail system manager empty,ea:{},result:{} ", ea, result);
            return MigrateOldTemplateDataToMarketingStatusEnum.SYSTEM_MANAGER_NOT_EXISTS.getStatus();
        }
        Integer fsUserId = result.getEmployeeIds().get(0);

        FsUserVO fsUserVO = new FsUserVO();
        fsUserVO.setEnterpriseAccount(ea);
        fsUserVO.setUserId(fsUserId);
        ModelResult<List<OuterServiceResult>> modelResult = outerServiceWechatService.queryOuterServiceList(fsUserVO);
        if (!modelResult.isSuccess()) {
            log.info("MigrateOneEaTemplateDataToMarketing listMarketingWxServiceInfo fail, ea:{} result:{}", ea, modelResult);
            return MigrateOldTemplateDataToMarketingStatusEnum.LIST_OUTER_SERVICE_FAIL.getStatus();
        }
        List<OuterServiceResult> outerServiceWechatVOS = modelResult.getResult();
        for (OuterServiceResult outerServiceWechatVO : outerServiceWechatVOS) {
            ModelResult<String> wxAppIdResult = outerServiceWechatService.transAppIdToWxAppId(outerServiceWechatVO.getAppId());
            if (!wxAppIdResult.isSuccess() || StringUtils.isNullOrEmpty(wxAppIdResult.getResult())) {
                continue;
            }
            String wxAppId = wxAppIdResult.getResult();
            Integer pageNo = 1;
            Integer pageSize = 50;
            com.facishare.wechat.union.common.result.Result<PageObject<NoticeSendRecord>> inOrderToGetTotalResult = noticeService.listSentNotice(outerServiceWechatVO.getAppId(), ei, 1, 1);
            if (!inOrderToGetTotalResult.isSuccess() || inOrderToGetTotalResult.getData() == null || inOrderToGetTotalResult.getData().getTotalCount() == null) {
                log.warn("MigrateOneEaTemplateDataToMarketing listSentNotice inOrderToGetTotal fail ea:{} result:{}}", ea, inOrderToGetTotalResult);
            }
            Integer total = inOrderToGetTotalResult.getData().getTotalCount();
            for (; (pageNo - 1) * pageSize < total; pageNo = pageNo + 1) {
                com.facishare.wechat.union.common.result.Result<PageObject<NoticeSendRecord>> showInWebNoticeSentInfoPageResult = noticeService
                        .listSentNotice(outerServiceWechatVO.getAppId(), ei, pageNo, pageSize);
                if (!showInWebNoticeSentInfoPageResult.isSuccess()) {
                    log.warn("MigrateOneEaTemplateDataToMarketing get NoticeSentListVo fail , ea : {} , appId : {} , pageNo : {} , pageSize :{}  , result : {}", ea, outerServiceWechatVO.getAppId(),
                            pageNo, pageSize, showInWebNoticeSentInfoPageResult);
                    break;
                }
                List<Integer> noticeIds = showInWebNoticeSentInfoPageResult.getData().getResult().stream().filter(val -> val.getValid() == 1).map(NoticeSendRecord::getId).collect(Collectors.toList());
                com.facishare.wechat.union.common.result.Result<List<NoticeDetailResult>> noticeSentInfoPageResult = noticeService
                        .findByIds(ea, outerServiceWechatVO.getAppId(), noticeIds, CommonConstant.VALID);
                if (!noticeSentInfoPageResult.isSuccess()) {
                    log.warn("MigrateOneEaTemplateDataToMarketing noticeService findByIds fail , ea : {} , appId : {} , pageNo : {} , pageSize :{}  , result : {}", ea, outerServiceWechatVO.getAppId(),
                            pageNo, pageSize, noticeSentInfoPageResult);
                }
                if (CollectionUtils.isNotEmpty(noticeSentInfoPageResult.getData())) {
                    for (NoticeDetailResult noticeSentInfoResult : noticeSentInfoPageResult.getData()) {
                        if (noticeSentInfoResult.getMsgType() == WeChatMsgTypeEnum.TEMPLATE_NOTICE.getType()) {
                            if (noticeSentInfoResult.getRedirectType() == null || noticeSentInfoResult.getRedirectType().equals(RedirectTypeEnum.OLD_ARTICLE.getType())) {
                                MarketingActivityExternalConfigEntity entity = marketingActivityExternalConfigDao
                                        .getMarketingActivityExternalConfigEntity(ea, String.valueOf(noticeSentInfoResult.getId()), AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType());
                                if (entity == null) {
                                    //创建营销活动
                                    AddMarketingActivityArg arg = new AddMarketingActivityArg();
                                    arg.setSpreadType(MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType());
                                    arg.setWechatMessageType(WechatMessageTypeEnum.WECHAT_TEMPLATE_MESSAGE.getType());
                                    String redirectUrl = ReplaceUtil.replaceWxAppId(templateMessageOldDataUrl, wxAppId);
                                    redirectUrl = ReplaceUtil.replaceMaterialId(redirectUrl, noticeSentInfoResult.getMaterialMessageId());
                                    redirectUrl = ReplaceUtil.replaceAppId(redirectUrl, noticeSentInfoResult.getAppId());
                                    if (StringUtils.isNullOrEmpty(noticeSentInfoResult.getRedirectUrl())) {
                                        noticeSentInfoResult.setRedirectUrl(redirectUrl);
                                    }
                                    WeChatTemplateMessageData weChatTemplateMessageData = convert2WeChatTemplateMessageData(noticeSentInfoResult);
                                    arg.setWeChatTemplateMessageVO(weChatTemplateMessageData);
                                    marketingActivityService.addMarketingActivity(ea, SuperUserConstants.USER_ID, arg, true);
                                }
                            }
                        } else if (noticeSentInfoResult.getMsgType() == WeChatMsgTypeEnum.TEXT_MSG.getType() || noticeSentInfoResult.getMsgType() == WeChatMsgTypeEnum.PIC_MSG.getType()) {
                            MarketingActivityExternalConfigEntity entity = marketingActivityExternalConfigDao
                                    .getMarketingActivityExternalConfigEntity(ea, String.valueOf(noticeSentInfoResult.getId()), AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType());
                            if (entity == null) {
                                //创建营销活动
                                AddMarketingActivityArg arg = new AddMarketingActivityArg();
                                arg.setSpreadType(MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType());
                                arg.setWechatMessageType(WechatMessageTypeEnum.WECHAT_SERVICE.getType());
                                WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO = this.convert(noticeSentInfoResult);
                                arg.setWeChatServiceMarketingActivityVO(weChatServiceMarketingActivityVO);
                                marketingActivityService.addMarketingActivity(ea, SuperUserConstants.USER_ID, arg, true);
                            }
                        }
                    }
                }
            }
        }
        log.info("MigrateOneEaTemplateDataToMarketing ea:{} result:success", ea);
        return MigrateOldTemplateDataToMarketingStatusEnum.SUCESS.getStatus();
    }

    private WeChatTemplateMessageData convert2WeChatTemplateMessageData(NoticeDetailResult noticeSentInfoResult) {
        WeChatTemplateMessageData weChatTemplateMessageData = new WeChatTemplateMessageData();
        weChatTemplateMessageData.setMsgId(noticeSentInfoResult.getId());
        weChatTemplateMessageData.setAppId(noticeSentInfoResult.getAppId());
        weChatTemplateMessageData.setRedirectType(RedirectTypeEnum.OLD_ARTICLE.getType());
        weChatTemplateMessageData.setSendRange(noticeSentInfoResult.getSendRange());
        weChatTemplateMessageData.setTitle(noticeSentInfoResult.getTitle());
        weChatTemplateMessageData.setType(noticeSentInfoResult.getIsRealTimeSend() ? 1 : 2);
        weChatTemplateMessageData.setWeChatOfficialTemplateId(noticeSentInfoResult.getWeChatOfficialTemplateId());
        weChatTemplateMessageData.setMsgContent(noticeSentInfoResult.getMsgContent());
        weChatTemplateMessageData.setMigration(true);
        weChatTemplateMessageData.setAppName(noticeSentInfoResult.getAppName());
        weChatTemplateMessageData.setContent(noticeSentInfoResult.getContent());
        weChatTemplateMessageData.setMiniAppId(noticeSentInfoResult.getMiniAppId());
        weChatTemplateMessageData.setMiniAppPagePath(noticeSentInfoResult.getMiniAppPagePath());
        weChatTemplateMessageData.setRedirectUrl(noticeSentInfoResult.getRedirectUrl());
        weChatTemplateMessageData.setSummary(noticeSentInfoResult.getSummary());
        weChatTemplateMessageData.setFixedTime(noticeSentInfoResult.getFixedTime());
        weChatTemplateMessageData.setSendTime(noticeSentInfoResult.getSendTime());
        weChatTemplateMessageData.setAuthor(noticeSentInfoResult.getAuthor());
        weChatTemplateMessageData.setFilters(noticeSentInfoResult.getFilters());
        weChatTemplateMessageData.setMarketingUserGroupIds(noticeSentInfoResult.getMarketingUserGroupIds());
        weChatTemplateMessageData.setCustomerCount(noticeSentInfoResult.getCustomerCount());
        weChatTemplateMessageData.setActualCompletedCount(noticeSentInfoResult.getActualCompletedCount());
        weChatTemplateMessageData.setNeedSendCount(noticeSentInfoResult.getNeedSendCount());
        weChatTemplateMessageData.setReadCount(noticeSentInfoResult.getReadCount());
        return weChatTemplateMessageData;
    }

    public WeChatServiceMarketingActivityVO convert(NoticeDetailResult source) {
        if (source == null) {
            return null;
        }
        WeChatServiceMarketingActivityVO result = new WeChatServiceMarketingActivityVO();
        result.setTitle(source.getTitle());
        result.setMsgType(source.getMsgType());
        result.setContent(source.getContent());
        result.setType(source.getType());
        result.setFixedTime(source.getFixedTime());
        result.setSendRange(source.getSendRange());
        result.setMarketingUserGroupIds(source.getMarketingUserGroupIds());
        result.setFilters(source.getFilters());
        result.setCustomerCount(source.getCustomerCount());
        result.setActualCompletedCount(source.getActualCompletedCount());
        result.setNeedSendCount(source.getNeedSendCount());
        result.setAppId(source.getAppId());
        result.setUserId(source.getUserId());
        result.setCorpId(source.getCorpId());
        result.setEnterpriseAccount(source.getEa());
        result.setMsgId(source.getId());
        result.setMigration(true);
        return result;
    }

    @Override
    public Result<Void> removeInCompleteTemplateData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        int pageSize = 100;
        doMigrateEas(eas, (ea) -> {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            List<String> inCompleteMarketingActivityIds = new LinkedList<>();
            for (int i = 0; i < 2000; i = i + pageSize) {
                HeaderObj headerObj = new HeaderObj(ei, -10000);
                SearchQuery searchQuery = new SearchQuery();
                searchQuery.setOffset(i);
                searchQuery.setLimit(pageSize);
                List<OrderBy> orderByList = new ArrayList<>();
                orderByList.add(new OrderBy("_id", true));
                searchQuery.setOrders(orderByList);
                Filter spreadTypeFilter = new Filter();
                spreadTypeFilter.setFieldName("spread_type");
                spreadTypeFilter.setOperator(Filter.OperatorContants.EQ);
                spreadTypeFilter.setFieldValues(Collections.singletonList(MarketingActivityActionEnum.WECHAT_SERVICE.getSpreadType() + ""));
                searchQuery.setFilters(Collections.singletonList(spreadTypeFilter));
                ControllerListArg listArg = new ControllerListArg();
                listArg.setSearchQuery(searchQuery);
                com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> pageResult = metadataControllerService.list(headerObj, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), listArg);
                if (pageResult == null || !pageResult.isSuccess()) {
                    return "Error at list MarketingActivity";
                }
                if (pageResult.getData() == null || pageResult.getData().getDataList() == null || pageResult.getData().getDataList().isEmpty()) {
                    break;
                }
                List<String> marketingActivityIds = pageResult.getData().getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
                List<String> completeMarketingActivityIds = marketingActivityExternalConfigDao.listMarketingActivityIdByMarketingActivityIds(ea, marketingActivityIds);
                marketingActivityIds.removeAll(completeMarketingActivityIds);
                inCompleteMarketingActivityIds.addAll(marketingActivityIds);
                if (pageResult.getData().getDataList().size() < pageSize) {
                    break;
                }
            }
            log.info("Incomplete marketing activity ea:{} list:{}", ea, inCompleteMarketingActivityIds);
            if (!inCompleteMarketingActivityIds.isEmpty()) {
                Integer currentCompleteMarketingActivityIdSize = 0;
                Integer totalCompleteMarketingActivityIdSize = inCompleteMarketingActivityIds.size();
                log.info("bulkInvalid totalCompleteMarketingActivityIdSize = {}", totalCompleteMarketingActivityIdSize);
                for (String inCompleteMarketingActivityId : inCompleteMarketingActivityIds) {
                    log.info("doMigrate ea  = {} , currentCompleteMarketingActivityIdSize/totalCompleteMarketingActivityIdSize : {}/{}", ea, ++currentCompleteMarketingActivityIdSize,
                            totalCompleteMarketingActivityIdSize);
                    HeaderObj headerObj = new HeaderObj(ei, -10000);
                    ActionBulkInvalidArg actionBulkInvalidArg = new ActionBulkInvalidArg(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), Collections.singletonList(inCompleteMarketingActivityId));
                    com.fxiaoke.crmrestapi.common.result.Result<ActionBulkInvalidResult> invalidResult = metadataActionService
                            .bulkInvalid(headerObj, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), actionBulkInvalidArg);
                }
            }
            return "success";
        });
        return Result.newSuccess();
    }

    private Map<String, Object> doMigrateEas(Collection<String> eas, SingleEaRunnable runnable) {
        return doMigrateEas(eas, null, runnable);
    }

    private Map<String, Object> doMigrateEas(Collection<String> eas, String taskName, SingleEaRunnable runnable) {
        Map<String, Object> result = new HashMap<>();
        String trackId = UUIDUtil.getUUID();
        log.info("Start doMigrateEas: trackId:{}", trackId);
        Integer currentEaSize = 0;
        Integer totalEaSize = eas.size();
        log.info("doMigrateEas taskName : {} , totalEaSize = {}", taskName, totalEaSize);
        for (String ea : eas) {
            currentEaSize = currentEaSize + 1;
            log.info("doMigrate taskName : {} , ea : {} , currentEaSize/totalEaSize : {}/{}", taskName, ea, currentEaSize, totalEaSize);
            try {
                Object oneEaResult = runnable.run(ea);
                result.put(ea, oneEaResult);
                log.info("migrate taskName : {} , ea:{} trackId:{} result:{}, currentEaSize/totalEaSize : {}/{}", taskName, ea, trackId, oneEaResult, currentEaSize, totalEaSize);
            } catch (Exception e) {
                log.info("migrate taskName : {} , ea:{} trackId:{}, currentEaSize/totalEaSize : {}/{} exception:", taskName, ea, trackId, currentEaSize, totalEaSize, e);
                result.put(ea, "exception");
            }
        }
        log.info("doMigrateEas result : {}", result);
        return result;
    }

    private Map<String, Object> doMigrateEasByThreadPool(Collection<String> eas, String taskName, SingleEaRunnable runnable) {
        Map<String, Object> result = new HashMap<>();
        String trackId = UUIDUtil.getUUID();
        log.info("Start doMigrateEas: trackId:{}", trackId);
        Integer currentEaSize = 0;
        Integer totalEaSize = eas.size();
        log.info("doMigrateEas taskName : {} , totalEaSize = {}", taskName, totalEaSize);
        for (String ea : eas) {
            currentEaSize = currentEaSize + 1;
            Integer finalCurrentEaSize = currentEaSize;
            ThreadPoolUtils.execute(() -> {
                log.info("doMigrate taskName : {} , ea : {} , currentEaSize/totalEaSize : {}/{}", taskName, ea, finalCurrentEaSize, totalEaSize);
                try {
                    Object oneEaResult = runnable.run(ea);
                    result.put(ea, oneEaResult);
                    log.info("migrate taskName : {} , ea:{} trackId:{} result:{}, currentEaSize/totalEaSize : {}/{}", taskName, ea, trackId, oneEaResult, finalCurrentEaSize, totalEaSize);
                } catch (Exception e) {
                    log.info("migrate taskName : {} , ea:{} trackId:{}, currentEaSize/totalEaSize : {}/{} exception:", taskName, ea, trackId, finalCurrentEaSize, totalEaSize, e);
                    result.put(ea, "exception");
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
        log.info("doMigrateEas result : {}", result);
        return result;
    }

    @Override
    public Result<Void> migrateMarketingUserGroupTags(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = marketingUserGroupDao.listEa();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateMarketingUserGroupTags", (ea) -> {
            List<MarketingUserGroupEntity> marketingUserGroupEntities = marketingUserGroupDao.listByEa(ea);
            if (CollectionUtils.isEmpty(marketingUserGroupEntities)) {
                log.info("marketingUserGroupDao.listByEa is empty , ea : {}", ea);
                return "success";
            }
            Map<String, TagName> allTagIdAngTagNameMap = doGetTagNameAndIdMap(ea, SuperUserConstants.USER_ID, null, null);
            if (MapUtils.isEmpty(allTagIdAngTagNameMap)) {
                return "success";
            }
            for (MarketingUserGroupEntity marketingUserGroupEntity : marketingUserGroupEntities) {
                try {
                    RuleGroupList ruleGroups = marketingUserGroupEntity.getRuleGroupJson();
                    Boolean isUpdate = Boolean.FALSE;
                    if (CollectionUtils.isEmpty(ruleGroups)) {
                        log.info("ruleGroups is empty , ea : {} , marketingUserGroupEntity : {}", ea, marketingUserGroupEntity);
                        continue;
                    }
                    for (RuleGroup ruleGroup : ruleGroups) {
                        if (ruleGroup.getQuery() == null || CollectionUtils.isEmpty(ruleGroup.getQuery().getFilters())) {
                            log.info("ruleGroup is null or query is empty , ea : {} , ruleGroups : {}", ea, ruleGroups);
                            continue;
                        }
                        for (com.facishare.marketing.common.typehandlers.value.Filter filter : ruleGroup.getQuery().getFilters()) {
                            if (TagRuleRuleContants.FIELDNAME.equals(filter.getFieldName())) {
                                List<String> tagIds = filter.getFieldValues();
                                Map<String, TagName> tagIdAndTagNameMap = allTagIdAngTagNameMap.entrySet().stream().filter(val -> tagIds.contains(val.getKey()))
                                        .collect(Collectors.toMap(val -> val.getKey(), val -> val.getValue()));
                                if (MapUtils.isEmpty(tagIdAndTagNameMap)) {
                                    continue;
                                }
                                String operator = filter.getOperator();
                                List<TagName> tagNameList = Lists.newArrayList(tagIdAndTagNameMap.values());
                                ruleGroup.getQuery().setTagOperator(operator);
                                ruleGroup.getQuery().setTagNames(tagNameList);
                                isUpdate = Boolean.TRUE;
                                break;
                            }
                        }
                    }
                    for (RuleGroup ruleGroup : ruleGroups) {
                        List<com.facishare.marketing.common.typehandlers.value.Filter> filterList = ruleGroup.getQuery().getFilters();
                        if (CollectionUtils.isEmpty(filterList)) {
                            ruleGroup.getQuery().setFilters(null);
                        }
                    }
                    if (isUpdate) {
                        marketingUserGroupDao.update(marketingUserGroupEntity);
                    }
                } catch (Exception e) {
                    log.info("update marketingUserGroupEntity fail , ea : {} , marketingUserGroupEntity : {} , Exception : {}", ea, marketingUserGroupEntity, e);
                }
            }
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateUserMarketingAccountTags(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateUserMarketingAccountTags", (ea) -> {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeService.getDescribe(new HeaderObj(ei, -10000), UserMarketingAccountContants.API_NAME);
            if (!result.isSuccess()) {
                log.warn("objectDescribeService.getDescribe warn , code  = {} ,msg = {}", result.getCode(), result.getMessage());
                return "success";
            }
            ControllerListArg controllerListArg = new ControllerListArg();
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(1);
            searchQuery.setOffset(0);
            controllerListArg.setSearchQuery(searchQuery);
            controllerListArg.setObjectDescribeApiName(UserMarketingAccountContants.API_NAME);
            Integer limit = 50;
            Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(ei, SuperUserConstants.USER_ID), UserMarketingAccountContants.API_NAME, controllerListArg);
            Map<String, TagName> allTagIdAngTagNameMap = doGetTagNameAndIdMap(ea, SuperUserConstants.USER_ID, null, null);
            if (MapUtils.isEmpty(allTagIdAngTagNameMap)) {
                return "success";
            }
            for (Integer offset = 0; offset <= total; offset += limit) {
                try {
                    searchQuery.setLimit(limit);
                    searchQuery.setOffset(offset);
                    controllerListArg.setSearchQuery(searchQuery);
                    Page<ObjectData> objectDataPage = metadataControllerServiceManager.list(new HeaderObj(ei, SuperUserConstants.USER_ID), UserMarketingAccountContants.API_NAME, controllerListArg);
                    if (objectDataPage != null && !CollectionUtils.isEmpty(objectDataPage.getDataList())) {
                        Map<String, String> objectIdAndUserMarketingAccountIdMap = objectDataPage.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName));
                        List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataList = this
                                .doGetObjectDataIdAndTagNameListDatasByObjectDataIds(ea, UserMarketingAccountContants.API_NAME, Lists.newArrayList(objectIdAndUserMarketingAccountIdMap.keySet()),
                                        allTagIdAngTagNameMap);
                        if (CollectionUtils.isEmpty(objectDataIdAndTagNameListDataList)) {
                            log.info("doGetObjectDataIdAndTagNameListDatasByObjectDataIds , ea = {},objectIdAndUserMarketingAccountIdMap.keySet = {}", ea,
                                    objectIdAndUserMarketingAccountIdMap.keySet());
                            return "success";
                        }
                        for (ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData : objectDataIdAndTagNameListDataList) {
                            userMarketingAccountService.batchAddTagsToUserMarketings(ea, SuperUserConstants.USER_ID, ChannelEnum.getAllChannelApiName(),
                                    Lists.newArrayList(objectIdAndUserMarketingAccountIdMap.get(objectDataIdAndTagNameListData.getDataId())), objectDataIdAndTagNameListData.getTagNameList());
                        }
                    }
                } catch (Exception e) {
                    log.info("migrateUserMarketingAccountTags fail ,ea :{} , exception :{}", ea, e);
                }
            }
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateFlowTags(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            Set eis = Sets.newHashSet();
            eis.addAll(marketingFlowTaskDao.listEi());
            eis.addAll(marketingActivityNodeAdditionalConfigDao.listEi());
            Map<Integer, String> eiEaMap = eieaConverter.enterpriseIdToAccount(eis);
            eas = Lists.newArrayList(eiEaMap.values());
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateFlowTags", (ea) -> {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            List<MarketingFlowTaskEntity> marketingFlowTaskEntities = marketingFlowTaskDao.listByEi(ei);
            Map<String, TagName> allTagIdAngTagNameMap = doGetTagNameAndIdMap(ea, SuperUserConstants.USER_ID, null, null);
            if (MapUtils.isEmpty(allTagIdAngTagNameMap)) {
                return "success";
            }
            for (MarketingFlowTaskEntity marketingFlowTaskEntity : marketingFlowTaskEntities) {
                if (marketingFlowTaskEntity != null && CollectionUtils.isNotEmpty(marketingFlowTaskEntity.getTagIds())) {
                    Map<String, TagName> tagNameMap = allTagIdAngTagNameMap.entrySet().stream().filter(val -> marketingFlowTaskEntity.getTagIds().contains(val.getKey()))
                            .collect(Collectors.toMap(val -> val.getKey(), val -> val.getValue()));
                    if (MapUtils.isEmpty(tagNameMap)) {
                        continue;
                    }
                    if (!CollectionUtils.isEqualCollection(Sets.newHashSet(marketingFlowTaskEntity.getTagIds()), Sets.newHashSet(tagNameMap.keySet()))) {
                        log.warn("marketingFlowTaskEntity tagId and tagNameMap tagId are not equal , tagIds : {} , tagNameMapIds : {}", marketingFlowTaskEntity.getTagIds(), tagNameMap.keySet());
                    }
                    marketingFlowTaskEntity.setTagNameList(TagNameList.convert(tagNameMap.values()));
                    marketingFlowTaskDao.updateTagNameListByEiAndTaskId(TagNameList.convert(tagNameMap.values()), marketingFlowTaskEntity.getEi(), marketingFlowTaskEntity.getTaskId());
                }
            }
            List<MarketingActivityNodeAdditionalConfigEntity> marketingActivityNodeAdditionalConfigEntities = marketingActivityNodeAdditionalConfigDao.listByEi(ei);
            for (MarketingActivityNodeAdditionalConfigEntity marketingActivityNodeAdditionalConfigEntity : marketingActivityNodeAdditionalConfigEntities) {
                if (marketingActivityNodeAdditionalConfigEntity != null && CollectionUtils.isNotEmpty(marketingActivityNodeAdditionalConfigEntity.getTagIds())) {
                    Map<String, TagName> tagNameMap = allTagIdAngTagNameMap.entrySet().stream().filter(val -> marketingActivityNodeAdditionalConfigEntity.getTagIds().contains(val.getKey()))
                            .collect(Collectors.toMap(val -> val.getKey(), val -> val.getValue()));
                    if (MapUtils.isEmpty(tagNameMap)) {
                        continue;
                    }
                    if (!CollectionUtils.isEqualCollection(Sets.newHashSet(marketingActivityNodeAdditionalConfigEntity.getTagIds()), Sets.newHashSet(tagNameMap.keySet()))) {
                        log.warn("marketingActivityNodeAdditionalConfigEntity tagId and tagNameMap tagId are not equal , tagIds : {} , tagNameMapIds : {}",
                                marketingActivityNodeAdditionalConfigEntity.getTagIds(), tagNameMap.keySet());
                    }
                    marketingActivityNodeAdditionalConfigEntity.setTagNameList(TagNameList.convert(tagNameMap.values()));
                    marketingActivityNodeAdditionalConfigDao
                            .updateTagNameList(TagNameList.convert(tagNameMap.values()), marketingActivityNodeAdditionalConfigEntity.getEi(), marketingActivityNodeAdditionalConfigEntity.getWorkflowId(),
                                    marketingActivityNodeAdditionalConfigEntity.getWorkflowActivityNodeId());
                }
            }

            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateFlowDraftTags(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            List<Integer> eis = marketingFlowDraftDao.listEi();
            Map<Integer, String> eiEaMap = eieaConverter.enterpriseIdToAccount(eis);
            eas = Lists.newArrayList(eiEaMap.values());
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateFlowDraftTags", (ea) -> {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            List<MarketingFlowDraftEntity> marketingFlowDraftEntities = marketingFlowDraftDao.listByEi(ei);
            Map<String, TagName> allTagIdAngTagNameMap = doGetTagNameAndIdMap(ea, SuperUserConstants.USER_ID, null, null);
            if (MapUtils.isEmpty(allTagIdAngTagNameMap)) {
                return "success";
            }
            for (MarketingFlowDraftEntity marketingFlowDraftEntity : marketingFlowDraftEntities) {
                Boolean isUpdate = Boolean.FALSE;
                if (marketingFlowDraftEntity != null && marketingFlowDraftEntity.getDraftDefinition() != null) {
                    FlexibleJson flexibleJson = marketingFlowDraftEntity.getDraftDefinition();
                    JSONObject flexibleJsonObject = JSON.parseObject(GsonUtil.toJson(flexibleJson));
                    JSONArray marketingActivityNodeAdditionalConfigList = flexibleJsonObject.getJSONArray("marketingActivityNodeAdditionalConfigList");
                    if (CollectionUtils.isEmpty(marketingActivityNodeAdditionalConfigList)) {
                        continue;
                    }
                    for (int i = 0; i < marketingActivityNodeAdditionalConfigList.size(); i++) {
                        JSONObject jsonObject = marketingActivityNodeAdditionalConfigList.getJSONObject(i);
                        JSONArray tagIdsJsonArray = jsonObject.getJSONArray("tagIds");
                        if (CollectionUtils.isEmpty(tagIdsJsonArray)) {
                            continue;
                        }
                        List<String> tagIds = Lists.newArrayList();
                        for (int i1 = 0; i1 < tagIdsJsonArray.size(); i1++) {
                            String tagId = tagIdsJsonArray.getString(i1);
                            tagIds.add(tagId);
                        }
                        Map<String, TagName> tagNameMap = allTagIdAngTagNameMap.entrySet().stream().filter(val -> tagIds.contains(val.getKey()))
                                .collect(Collectors.toMap(val -> val.getKey(), val -> val.getValue()));
                        JSONArray tagNameList = JSONObject.parseArray(GsonUtil.toJson(tagNameMap.values()));
                        jsonObject.put("tagNameList", tagNameList);
                        isUpdate = Boolean.TRUE;
                    }
                    if (isUpdate) {
                        FlexibleJson flexibleJson1 = BeanUtil.copyByGson(flexibleJsonObject, FlexibleJson.class);
                        marketingFlowDraftDao.updateFlexibleJson(flexibleJson1, ei, marketingFlowDraftEntity.getId());
                    }
                }
            }

            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateTagsManage(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateTagsManage", (ea) -> {
            Map<String, TagName> allTagIdAngTagNameMap = doGetTagNameAndIdMap(ea, SuperUserConstants.USER_ID, null, null);
            if (MapUtils.isEmpty(allTagIdAngTagNameMap) || CollectionUtils.isEmpty(allTagIdAngTagNameMap.values())) {
                return null;
            }
            List<TagName> tagNameList = Lists.newArrayList(allTagIdAngTagNameMap.values());
            for (String apiName : ChannelEnum.getAllChannelApiName()) {
                for (Integer i = 0; i < tagNameList.size(); i += 50) {
                    if (i + 50 < tagNameList.size()) {
                        metadataTagManager.getOrCreateTagIdsByTagNames(ea, apiName, tagNameList.subList(i, i + 50));
                    } else {
                        metadataTagManager.getOrCreateTagIdsByTagNames(ea, apiName, tagNameList.subList(i, tagNameList.size() - 1));
                    }
                    log.info("process migrate Tag ,apiName = {}, ea = {} , now = {}, size = {} ", apiName, ea, i, tagNameList.size());
                }
            }
            for (Integer i = 0; i < tagNameList.size(); i++) {
                userTagManager.addTagByTagGroupNameAndTagName(ea, ((MigrateTagName) tagNameList.get(i)).getCreator(), tagNameList.get(i).getFirstTagName(), tagNameList.get(i).getSecondTagName());
            }
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateMaterial(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, (ea) -> {
            if (arg.initAll) {
                this.doProcessHexagonIntegral(ea);
                this.doProcessArticleIntegral(ea);
                this.doProcessConferenceIntegral(ea);
                this.doProcessProductIntegral(ea);
                this.doProcessFormIntegral(ea);
                this.doProcessWechatServiceIntegral(ea);
                this.doProcessOfficialWebsiteTrackIntegral(ea);
                wxOfficialAccountsService.registerMenuToSfa(ea);
            } else if (arg.getOtherParams() != null) {
                String types = arg.getOtherParams().get("type");
                for (String type : types.split(",")) {
                    if ("hexagon".equals(type)){
                        this.doProcessHexagonIntegral(ea);
                    } else if ("article".equals(type)) {
                        this.doProcessArticleIntegral(ea);
                    } else if ("conference".equals(type)) {
                        this.doProcessConferenceIntegral(ea);
                    } else if ("product".equals(type)) {
                        this.doProcessProductIntegral(ea);
                    } else if ("form".equals(type)) {
                        this.doProcessFormIntegral(ea);
                    } else if ("wxchat".equals(type)) {
                        this.doProcessWechatServiceIntegral(ea);
                    } else if ("officialWebsite".equals(type)) {
                        this.doProcessOfficialWebsiteTrackIntegral(ea);
                    } else if ("menu".equals(type)) {
                        wxOfficialAccountsService.registerMenuToSfa(ea);
                    }
                }
            }
            return "success";
        });
        return null;
    }

    private void doProcessOfficialWebsiteTrackIntegral(String ea) {
        List<OfficialWebsiteTrackEntity> officialWebsiteTrackEntityList = officialWebsiteTrackDAO.list(ea);
        for (int i = 0; i < officialWebsiteTrackEntityList.size(); i++) {
            integralServiceManager.asyncRegisterMaterial(officialWebsiteTrackEntityList.get(i).getEa(),CategoryApiNameConstant.WEBSITE, officialWebsiteTrackEntityList.get(i).getId(), officialWebsiteTrackEntityList.get(i).getTrackName());
            log.info("migrateMaterial ea : {} , doProcessOfficialWebsiteTrackIntegral , current/total : {}/{} , entity : {}",ea, i, officialWebsiteTrackEntityList.size(), officialWebsiteTrackEntityList.get(i));
        }
    }

    @Override
    public Result<Void> tryInitMarketingBehaviorObject(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, (ea) -> {
            marketingBehaviorObjDescribeManager.getOrCreateMarketingBehaviorObjDescribe(ea);
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initMarketingEventCommonSetting(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }

        for (String ea : eas) {
            try {
                MarketingEventCommonSettingEntity settingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
                if (Objects.isNull(settingEntity)) {
                    continue;
                }
                ActivityTypeMapping activityTypeMappingDetailList = settingEntity.getActivityTypeMapping();
                for (ActivityTypeMapping.ActivityTypeMappingDetail data : activityTypeMappingDetailList) {
                    if (data.getActivityType().equals(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType())) {
                        // 活动营销
                        List<ActivityTypeMapping.MappingDetail> mapping = data.getMapping();
                        if (CollectionUtils.isEmpty(mapping)) {
                            continue;
                        }

                        // 多会场活动
                        List<String> collect = mapping.stream().map(mappingDetail -> {
                            return mappingDetail.getApiName();
                        }).collect(Collectors.toList());
                        if (collect.contains(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType())) {
                            continue;
                        }

                        ActivityTypeMapping.MappingDetail detail = new ActivityTypeMapping.MappingDetail();
                        detail.setApiName(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType());
                        detail.setFieldName("多会场活动");
                        mapping.add(detail);
                        marketingEventCommonSettingDAO.upsertSettingByEa(settingEntity);
                    }
                }
            }catch (Exception e) {
                log.info("MigrateServiceImpl -> initMarketingEventCommonSetting error, ea:{}", ea, e);
            }
        }
        return Result.newSuccess();
    }

    private void doProcessWechatServiceIntegral(String ea) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        GetEmployeeIdsByRoleCode.Argument argument = new GetEmployeeIdsByRoleCode.Argument();
        argument.setEnterpriseId(ei);
        argument.setRoleCode(SystemRoleEnum.SYSTEM_MANAGEMENT.getRoleCode());
        argument.setCurrentEmployeeId(SuperUserConstants.USER_ID);
        GetEmployeeIdsByRoleCode.Result result = permissionService.getEmployeeIdsByRoleCode(argument);
        if (result == null || CollectionUtils.isEmpty(result.getEmployeeIds())) {
            log.info("doProcessWechatServiceIntegral getEmployeeIdsByRoleCode result:fail system manager empty,ea:{},result:{} ", ea, result);
            return ;
        }
        Integer fsUserId = result.getEmployeeIds().get(0);
        Result<List<MarketingWxServiceResult>> modelResult = weChatServiceMarketingActivityService.listMarketingWxServiceInfo(ea,fsUserId, false);
        if (!modelResult.isSuccess()) {
            log.info("doProcessWechatServiceIntegral listMarketingWxServiceInfo fail, ea:{} result:{}", ea, modelResult);
            return ;
        }
        List<MarketingWxServiceResult> outerServiceWechatVOS = modelResult.getData();
        for (MarketingWxServiceResult outerServiceWechatVO : outerServiceWechatVOS) {
            integralServiceManager.asyncRegisterMaterial(ea,CategoryApiNameConstant.OFFICIAL_ACCOUNT,outerServiceWechatVO.getWxAppId(),outerServiceWechatVO.getWxAppName());
        }
    }

    private void doProcessFormIntegral(String ea) {
        List<CustomizeFormDataEntity> customizeFormDataEntities = customizeFormDataDAO.list(ea);
        for (int i = 0; i < customizeFormDataEntities.size(); i++) {
            integralServiceManager.asyncRegisterMaterial(customizeFormDataEntities.get(i).getEa(),CategoryApiNameConstant.FORM, customizeFormDataEntities.get(i).getId(), customizeFormDataEntities.get(i).getFormHeadSetting().getName());
            log.info("migrateMaterial ea : {} , doProcessConferenceIntegral , current/total : {}/{} , entity : {}",ea, i, customizeFormDataEntities.size(), customizeFormDataEntities.get(i));
        }
    }

    private void doProcessProductIntegral(String ea) {
        List<ProductEntityDTO> productEntityDTOS = productDAO.list(ea);
        for (int i = 0; i < productEntityDTOS.size(); i++) {
            integralServiceManager.asyncRegisterMaterial(productEntityDTOS.get(i).getFsEa(),CategoryApiNameConstant.PRODUCT, productEntityDTOS.get(i).getId(), productEntityDTOS.get(i).getName());
            log.info("migrateMaterial ea : {} , doProcessConferenceIntegral , current/total : {}/{} , entity : {}",ea, i, productEntityDTOS.size(), productEntityDTOS.get(i));
        }
    }

    private void doProcessConferenceIntegral(String ea) {
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page();
        page.setPageNo(1);
        page.setPageSize(99999);
        List<ActivityEntity> activityEntities = conferenceDAO.pageActivityEnrollsByEaAndId(ea, null,Lists.newArrayList(ActivityStatusEnum.ENABLED.getStatus(), ActivityStatusEnum.DISABLED.getStatus(), ActivityStatusEnum.UNPUBLISHED.getStatus()), null, page, null);
        for (int i = 0; i < activityEntities.size(); i++) {
            integralServiceManager.asyncRegisterMaterial(activityEntities.get(i).getEa(),CategoryApiNameConstant.MEETING, activityEntities.get(i).getId(), activityEntities.get(i).getTitle());
            log.info("migrateMaterial ea : {} , doProcessConferenceIntegral , current/total : {}/{} , entity : {}",ea, i, activityEntities.size(), activityEntities.get(i));
        }
    }

    private void doProcessArticleIntegral(String ea) {

        List<ArticleEntityDTO> articleEntityDTOS = Lists.newArrayList();
        com.github.mybatis.pagination.Page page2 = new com.github.mybatis.pagination.Page();
        page2.setPageNo(1);
        page2.setPageSize(99999);
        List<ArticleEntityDTO> articleEntityDTOS2 = articleDAO.pageByEa(ea, null, ArticleStatusEnum.START_USING.getStatus(), null, page2);
        com.github.mybatis.pagination.Page page3 = new com.github.mybatis.pagination.Page();
        page3.setPageNo(1);
        page3.setPageSize(99999);
        List<ArticleEntityDTO> articleEntityDTOS3 = articleDAO.pageByEa(ea, null, ArticleStatusEnum.DEACTIVATED.getStatus(), null, page3);
        if (CollectionUtils.isNotEmpty(articleEntityDTOS2)) {
            articleEntityDTOS.addAll(articleEntityDTOS2);
        }
        if (CollectionUtils.isNotEmpty(articleEntityDTOS3)) {
            articleEntityDTOS.addAll(articleEntityDTOS3);
        }
        for (int i = 0; i < articleEntityDTOS.size(); i++) {
            integralServiceManager.asyncRegisterMaterial(articleEntityDTOS.get(i).getFsEa(),CategoryApiNameConstant.ARTICLE, articleEntityDTOS.get(i).getId(), articleEntityDTOS.get(i).getTitle());
            log.info("migrateMaterial ea : {} , doProcessArticleIntegral , current/total : {}/{} , entity : {}",ea, i, articleEntityDTOS.size(), articleEntityDTOS.get(i));
        }
    }

    private void doProcessHexagonIntegral(String ea) {
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page();
        page.setPageNo(1);
        page.setPageSize(99999);
        List<HexagonSiteEntity> hexagonSiteEntities = hexagonSiteDAO.getByEa(ea, page, null, null, false);
        for (int i = 0; i < hexagonSiteEntities.size(); i++) {
            integralServiceManager.asyncRegisterMaterial(hexagonSiteEntities.get(i).getEa(),CategoryApiNameConstant.MICRO_PAGE, hexagonSiteEntities.get(i).getId(), hexagonSiteEntities.get(i).getName());
            log.info("migrateMaterial ea : {} , doProcessHexagonIntegral , current/total : {}/{} , entity : {}",ea , i, hexagonSiteEntities.size(), hexagonSiteEntities.get(i));
        }
    }

    @Deprecated
    private Map<String, TagName> doGetTagNameAndIdMap(String ea, Integer fsUserId, String tagGroupId, String tagName) {
        Map<String, TagName> tagIdAndTagNameMap = Maps.newHashMap();
        List<LikeTagByNameResult> allTagDatas = doLikeTagByName(ea, fsUserId, tagGroupId, tagName);
        if (CollectionUtils.isEmpty(allTagDatas)) {
            return null;
        }
        for (LikeTagByNameResult allTagData : allTagDatas) {
            for (TagData tagData : allTagData.getTagDatas()) {
                MigrateTagData migrateTagData = (MigrateTagData) tagData;
                tagIdAndTagNameMap.put(migrateTagData.getTagId(), new MigrateTagName(allTagData.getTagGroupName(), tagData.getTagName(), migrateTagData.getCreator()));
            }
        }
        log.info("doGetTagNameAndIdMap ea : {},tagGroupId : {} , tagName :{},allTag : {}", ea, tagGroupId, tagName, allTagDatas);
        return tagIdAndTagNameMap;
    }

    @Deprecated
    private List<LikeTagByNameResult> doLikeTagByName(String ea, Integer fsUserId, String tagGroupId, String tagName) {
        if (StringUtils.isNullOrEmpty(tagGroupId)) {
            tagGroupId = null;
        }
        if (StringUtils.isNullOrEmpty(tagName)) {
            tagName = null;
        }
        List<LikeTagByNameResult> resultList = new ArrayList<>();
        Map<String, List<MetadataTagData>> tagGroupId2MetadataTagDataMap = Maps.newHashMap();
        //如果不传tagGroupId，则搜索所有分组
        for (Integer pageNo = 1; pageNo < 1000; pageNo++) {
            try {
                TagPage<MetadataTagData> metadataTagDataList = this.doFindTagsByGroupId(ea, UserMarketingAccountContants.API_NAME, tagGroupId, tagName, pageNo, 50);
                if (CollectionUtils.isEmpty(metadataTagDataList.getData())) {
                    break;
                }
                //此处将结果按照标签组Id分组
                Map<String, List<MetadataTagData>> perTagGroupId2MetadataTagDataMap = metadataTagDataList.getData().stream().collect(Collectors.groupingBy(MetadataTagData::getTagId));
                for (Entry<String, List<MetadataTagData>> entry : perTagGroupId2MetadataTagDataMap.entrySet()) {
                    if (CollectionUtils.isEmpty(tagGroupId2MetadataTagDataMap.get(entry.getKey()))) {
                        tagGroupId2MetadataTagDataMap.put(entry.getKey(), entry.getValue());
                    } else {
                        tagGroupId2MetadataTagDataMap.get(entry.getKey()).addAll(entry.getValue());
                    }
                }
            } catch (Exception e) {
                log.info("this.doFindTagsByGroupId exception :{}", e);
                break;
            }
        }
        if (MapUtils.isEmpty(tagGroupId2MetadataTagDataMap)) {
            return null;
        }
        //获取标签组ID和标签组name的对应关系
        List<String> tagGroupIds = new ArrayList<>(tagGroupId2MetadataTagDataMap.keySet());
        if (CollectionUtils.isEmpty(tagGroupIds)) {
            return null;
        }
        List<MetadataTagGroupData> tagGroupDataList = Lists.newArrayList();
        Integer pageSize = 50;
        for (Integer pageNo = 1; pageNo < PageUtil.getPageNo(tagGroupIds.size(), pageSize) + 1; pageNo++) {
            try {
                List<String> tagIdList = ListUtil.subListByPage(tagGroupIds, pageNo, pageSize);
                tagGroupDataList.addAll(this.doFindGroupTagsByIds(ea, UserMarketingAccountContants.API_NAME, tagIdList));
            } catch (Exception e) {
                log.info("this.doFindTagsByGroupId exception :{}", e);
                break;
            }
        }

        Map<String, String> tagGroupIdNameMap = tagGroupDataList.stream().collect(Collectors.toMap(MetadataTagGroupData::getId, MetadataTagGroupData::getType));
        //标签组要求按照创建时间升序排列
        Collections.sort(tagGroupIds);
        for (String groupId : tagGroupIds) {
            LikeTagByNameResult result = new LikeTagByNameResult();
            result.setTagGroupId(groupId);
            result.setTagGroupName(tagGroupIdNameMap.get(groupId));
            List<TagData> tagDataList = new ArrayList<>();
            for (MetadataTagData metadataTagData : tagGroupId2MetadataTagDataMap.get(groupId)) {
                MigrateTagData tagData = new MigrateTagData();
                tagData.setTagId(metadataTagData.getId());
                tagData.setTagName(metadataTagData.getName());
                tagData.setCreator(metadataTagData.getCreator());
                tagDataList.add(tagData);
            }
            result.setTagDatas(tagDataList);
            resultList.add(result);
        }
        return resultList;
    }

    /**
     * 批量通过分组id查询对应的标签分组
     */
    private List<MetadataTagGroupData> doFindGroupTagsByIds(String ea, String describeApiName, List<String> tagGroupIds) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        TagGroupGetTagGroupByIdsArg arg = new TagGroupGetTagGroupByIdsArg(tenantId, describeApiName, tagGroupIds);
        return metadataTagService.findGroupsTagsByIds(arg).getResult();
    }

    private List<ObjectDataIdAndTagNameListData> doGetObjectDataIdAndTagNameListDatasByObjectDataIds(String ea, String describeApiName, List<String> objectDataIds,
                                                                                                     Map<String, TagName> allTagIdAngTagNameMap) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(objectDataIds)) {
            return null;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        FindAllTagByBulkDataIdArg arg = new FindAllTagByBulkDataIdArg(tenantId, describeApiName, objectDataIds);
        log.info("com.facishare.marketing.provider.remote.restapi.getObjectDataIdAndTagNameListDatasByObjectDataIds, ea:{} , describeApiName : {} , dataIds : {}", ea, describeApiName, objectDataIds);
        MetadataTagResult<List<DataIdAndMetadataTagData>> result = metadataTagManager.doFindAllTagByBulkDataId(arg);
        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
        }
        Set<String> tagIds = Sets.newHashSet();
        List<DataIdAndMetadataTagData> dataIdAndMetadataTagDataList = result.getResult();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dataIdAndMetadataTagDataList)) {
            return null;
        }
        for (DataIdAndMetadataTagData dataIdAndMetadataTagData : dataIdAndMetadataTagDataList) {
            for (MetadataTagData metadataTagData : dataIdAndMetadataTagData.getResultList()) {
                tagIds.add(metadataTagData.getId());
            }
        }
        Map<String, TagName> tagIdAndTagNameMap = allTagIdAngTagNameMap.entrySet().stream().filter(val -> Lists.newArrayList(tagIds).contains(val.getKey()))
                .collect(Collectors.toMap(val -> val.getKey(), val -> val.getValue()));
        if (MapUtils.isEmpty(tagIdAndTagNameMap)) {
            return null;
        }
        List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = Lists.newArrayList();
        for (DataIdAndMetadataTagData dataIdAndMetadataTagData : dataIdAndMetadataTagDataList) {
            String dataId = dataIdAndMetadataTagData.getDataId();
            TagNameList tagNameList = new TagNameList();
            for (MetadataTagData metadataTagData : dataIdAndMetadataTagData.getResultList()) {
                TagName tagName = tagIdAndTagNameMap.get(metadataTagData.getId());
                if (tagName != null) {
                    tagNameList.add(tagName);
                }
            }
            ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData = new ObjectDataIdAndTagNameListData(describeApiName, dataId, tagNameList);
            dataIdAndTagNameListObjectDataList.add(objectDataIdAndTagNameListData);
        }
        return dataIdAndTagNameListObjectDataList;
    }

    /**
     * 数据迁移用，找到该租户下的所有标签分组
     */
    @Deprecated
    public List<MetadataTagGroupData> doFindAllGroups(String ea, String describeApiName, int pageNumber, int pageSize) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        TagGroupFindAllArg arg = new TagGroupFindAllArg(tenantId, describeApiName, pageNumber, pageSize);
        return metadataTagService.findAllGroups(arg).getResult();
    }

    /**
     * 数据迁移用 查询 指定标签分组下的所有子标签。如果不传入标签分组的id，那就会查询该租户下的所有子标签 如果传了tagName，还会根据tagName模糊搜索
     */
    public TagPage<MetadataTagData> doFindTagsByGroupId(String ea, String describeApiName, String tagGroupId, String tagName, int pageNumber, int pageSize) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        TagGroupFindTagsByGroupIdArg arg = new TagGroupFindTagsByGroupIdArg(tenantId, describeApiName, tagGroupId, tagName, pageNumber, pageSize);
        return metadataTagService.findTagsByGroupId(arg).getResult();
    }

    private void checkChangeFields(Object obj1, Object obj2, Set<String> expectChangeFieldNames) {
        try {
            Map<String, List<Object>> changeFieldMap = Maps.newHashMap();
            if (obj1.getClass() != obj2.getClass()) {
                log.warn("type is not same ! obj1 : {} , obj2 : {}", obj1.getClass(), obj2.getClass());
                return;
            }
            List<String> fieldList = ReflectionUtil.listFieldName(obj1.getClass());
            for (String fieldName : fieldList) {
                Object value1 = ReflectionUtil.getFieldValue(fieldName, obj1);
                Object value2 = ReflectionUtil.getFieldValue(fieldName, obj2);
                changeFieldMap.put(fieldName, Lists.newArrayList(value1, value2));
            }
            if (MapUtils.isEmpty(changeFieldMap)) {
                log.info("nothing change!");
                return;
            }
            if (CollectionUtils.isEmpty(expectChangeFieldNames)) {
                log.warn("change value changeFieldMap:{}", changeFieldMap);
            } else {
                Set<String> changeFieldNames = changeFieldMap.keySet();
                changeFieldNames.remove(expectChangeFieldNames);
                if (!CollectionUtils.isEmpty(changeFieldNames)) {
                    log.warn("change value error , expectChangeFieldNames :{} , changeFieldMap {} , error changeFieldNames :{}", expectChangeFieldNames, changeFieldMap, changeFieldNames);
                }
            }
        } catch (Exception e) {
            log.warn("checkChangeFields exception : {}", e);
        }
    }

    @FunctionalInterface
    private interface SingleEaRunnable {
        Object run(String ea);
    }

    @Override
    public Result<Void> updateMarketingActivitySpreadType(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        Iterator<String> iterator = eas.iterator();
        String nextEa;
        while (iterator.hasNext()) {
            nextEa = iterator.next();
            boolean isStop = marketingActivityRemoteManager.enterpriseStop(nextEa);
            // 企业已停用或者没有开通企业微信 直接跳过
            if (isStop) {
                iterator.remove();
            }
        }
        doMigrateEas(eas, "updateMarketingActivitySpreadType", (ea) -> {
            return marketingActivityRemoteManager.addSpreadTypeWxSpreadOption(eieaConverter.enterpriseAccountToId(ea), -10000);
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addCancelStatusOptionToMarketingActivity(MigrateEasArg migrateEasArg) {
        List<String> eas;
        if (BooleanUtils.isTrue(migrateEasArg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = migrateEasArg.getEas();
        }
        doMigrateEas(eas, "addCancelStatusOptionToMarketingActivity", (ea) -> {
            return marketingActivityRemoteManager.addCancelStatusOption(eieaConverter.enterpriseAccountToId(ea), -10000);
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initEnterpriseWxWorkExternalUserData(MigrateEasArg migrateEasArg) {
        doMigrateEas(migrateEasArg.getEas(), (ea) -> {
            wechatWorkExternalUserObjDescribeManager.getOrCreateWxWorkExternalUserObjDescribe(ea);
            userRoleManager.initWechatWorkExternalUserObjPrivilege(ea);
            wechatWorkExternalUserObjManager.initEnterpriseData(ea);
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> migrateAllEaWechatAccountBind() {
        List<String> fails = new LinkedList<>();
        List<QywxMiniappConfigEntity> entities = qywxMiniappConfigDAO.listAll();
        entities.forEach(entity -> {
            int i = eaWechatAccountBindDao.insert(entity.getEa(), MKThirdPlatformConstants.PLATFORM_ID, entity.getAppid());
            if(i < 1){
                fails.add(entity.getEa() + ":" + entity.getAppid() + ":marketing");
            }
        });
        List<String> eas = enterpriseMetaConfigDao.findEaAll();
        eas.forEach(ea -> {
            int i = eaWechatAccountBindDao.insert(ea, MKThirdPlatformConstants.PLATFORM_ID, WxAppInfoEnum.Mankeep.getAppId());
            if(i < 1){
                fails.add(ea + ":" + WxAppInfoEnum.Mankeep.getAppId() + ":mankeep");
            }
        });
        return Result.newSuccess(fails);
    }

    @Override
    public Result<List<String>> migrateTryAppendFieldsToWxWorkExternalUserObj(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            List<QywxCorpAgentConfigEntity> entities = qywxCorpAgentConfigDAO.queryAllQywxCorp();
            eas = entities.stream().map(QywxCorpAgentConfigEntity::getEa).collect(Collectors.toList());
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, ea -> {
            wechatWorkExternalUserObjDescribeManager.tryAppendWxWorkAdderField(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result<List<String>> migrateTryUpdateUnionIdToWxWorkExternalUserObj(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            List<QywxCorpAgentConfigEntity> entities = qywxCorpAgentConfigDAO.queryAllQywxCorp();
            eas = entities.stream().map(QywxCorpAgentConfigEntity::getEa).collect(Collectors.toList());
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, ea -> {
            wechatWorkExternalUserObjDescribeManager.tryUpdateWxWorkWxUnionId(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result<Void> migrateChangeTagGroupName(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = userTagDao.listAllUsedEas();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, ea -> {
            CrmObjectApiNameEnum.getAllUserApiNames().forEach(apiName -> {
                metadataTagManager.doGetOrCreateMarketingTagGroup(ea, apiName);
            });
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateChangeTagGroupName(MigrateEiasArg arg) {
        List<String> eas = arg.getEis().stream().map(ei -> {
            try {
                return eieaConverter.enterpriseIdToAccount(ei);
            }catch (Exception e){
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        doMigrateEas(eas, ea -> {
            CrmObjectApiNameEnum.getAllUserApiNames().forEach(apiName -> {
                metadataTagManager.doGetOrCreateMarketingTagGroup(ea, apiName);
            });
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result migrateLeadsUtmFieldByEas(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.initAll)) {
            eas = hexagonOfficialWebsiteDAO.findWebsiteEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, ea -> {
            officialWebsiteManager.appendLeadSource(ea);
            officialWebsiteManager.appendLeadUtmField(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result migrateLeadsMarketingActivity(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.initAll)) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, ea -> {
            officialWebsiteManager.appendLeadMarketingActivity(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result<Void> migrateTagsToDefaultTagModel(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = userTagDao.listAllUsedEas();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateTagsToDefaultTagModel", ea -> {
            try {
                Result<List<LikeTagByNameResult>> result = userMarketingTagService.listAllTag(ea, -10000);
                if(result.getData() != null && !result.getData().isEmpty()){
                    List<FirstTag> firstTags = result.getData().stream().map(tagGroup -> {
                        FirstTag firstTag = new FirstTag();
                        firstTag.setName(tagGroup.getTagGroupName());
                        if(tagGroup.getTagDatas() != null && !tagGroup.getTagDatas().isEmpty()){
                            List<String> subTags = tagGroup.getTagDatas().stream().map(TagData::getTagName).filter(Objects::nonNull).collect(Collectors.toList());
                            firstTag.setSubTags(subTags);
                        }
                        return firstTag;
                    }).collect(Collectors.toList());
                    userTagManager.createTagModelAndTags(ea, -10000, "默认标签", TagModelTypeEnum.FACT.getType(), "_ALL_", TagModelSceneConstants.DEFAULT, true, 2,  null, firstTags, null, null, "_ALL_", TagModelSourceTypeEnum.USER.getValue());
                }
            }catch (Exception e){
                log.warn("Error at create default tag model:{}", e.getMessage());
            }
            userTagManager.initEnterpriseTagModel(ea);
            return "empty";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migratePaasSecondTagToFirstTags(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = userTagDao.listAllUsedEas();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migratePaasSecondTagToFirstTags", ea -> {
            Result<List<LikeTagByNameResult>> result = userMarketingTagService.listAllTag(ea, -10000);
            if(result.getData() != null && !result.getData().isEmpty()) {
                List<TagName> tagNames = new LinkedList<>();
                for (LikeTagByNameResult datum : result.getData()) {
                    if(datum.getTagDatas() != null && !datum.getTagDatas().isEmpty()){
                        for (TagData tagData : datum.getTagDatas()) {
                            tagNames.add(new TagName(datum.getTagGroupName(), tagData.getTagName()));
                        }
                    }
                }
                CrmObjectApiNameEnum.getAllUserApiNames().stream().forEach(apiName -> {
                    tagNames.forEach(tagName -> {
                        try {
                            metadataTagManager.tryMigrateTagByTagName(ea, apiName, tagName);
                        }catch (Exception e){
                            log.warn("Error at migrate, ea:{}, apiName:{}, tagName:{}", ea, apiName, tagName);
                        }
                    });
                });
            }
            ContextArg contextArg = new ContextArg(eieaConverter.enterpriseAccountToId(ea), -10000);
            metadataTagService.migrateSecondTagToFirstTag(contextArg);
            return 0;
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateRoleUsers(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "migrateRoleUsers", ea -> {
            userRoleManager.initSystemAdminUserRoles(ea);
            List<Integer> operatorIds = operatorDao.getAllFsUserId(ea);
            if (!operatorIds.isEmpty()){
                userRoleDao.batchInsertIgnoreByEmployeeIds(ea, RoleConstant.CORPORATE_DISTRIBUTOR, operatorIds);
                userRoleManager.addToCrmManagerCommonUserRole(ea, operatorIds);
            }
            return true;
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateDefaultIntroductionPageSiteId(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = eaWechatAccountBindDao.listEaWechatAccountBindEa();
        } else {
            eas = arg.getEas();
        }
        Preconditions.checkArgument(eas != null && !eas.isEmpty(), "参数错误，eas的信息为空");
        eas.forEach(ea->{
            Result<Void> result = wxThirdAuthService.setDefaultIntroductionPage(ea);
            if (!result.isSuccess()) {
                log.warn("当前企业设置默认托管小程序失败 ea:{}", ea);
            }
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> presetEnterpriseBoard(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "presetEnterpriseBoard", ea -> {
            return boardManager.initDefaultEnterpriseBoard(ea);
        });
        return Result.newSuccess();
    }

    @Override
    public Result addCampaignMembersObjField(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "addCampaignMembersObjField", ea -> {
            campaignMergeDataManager.addCampaignMembersObjField(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result syncCrmInvalidConference(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "addCampaignMembersObjField", ea -> {
            conferenceManager.syncCrmInvalidConference(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result resetCampaignMergeData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetCampaignMergeData", ea -> {
            campaignMergeDataResetManager.resetAllData(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result syncLiveFormDataToMember(MigrateEasArg arg) {
     //   String formId1 = "f7b7915c540e4fe28e438ca8833be330";  //ceshi112
     //   String formId2 = "a3ffc0716e1947da8a6c737b67597aa3";  //ceshi112
        String formId1 = "13a8a3a091cd4560a86c857a918d93ec";  //foneshare
        String formId2 = "28ea6f4ff30d40ea99133ea0d1cf780b";  //foneshare

        CustomizeFormDataEntity entity1 = customizeFormDataDAO.getCustomizeFormDataById(formId1);
        CustomizeFormDataEntity entity2 = customizeFormDataDAO.getCustomizeFormDataById(formId2);
        if (entity1 != null) {
            entity1.getFormMoreSetting().setSyncToMember(true);
            customizeFormDataDAO.updateFormMoreSetting(entity1);
        }
        if (entity2 != null) {
            entity2.getFormMoreSetting().setSyncToMember(true);
            customizeFormDataDAO.updateFormMoreSetting(entity2);
        }

        return Result.newSuccess();
    }

    @Override
    public Result resetNoSaveCrmData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetNoSaveCrmData", ea -> {
            campaignMergeDataResetManager.resetNoSaveCrmData(ea);
            campaignMergeDataResetManager.saveCrmCampaignData(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result saveCrmCampaignData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "saveCrmCampaignData", ea -> {
            campaignMergeDataResetManager.saveCrmCampaignData(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result reImportDataToCrm(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getIds())) {
            return null;
        }
        doMigrateEas(arg.getIds(), "reImportDataToCrm", id -> {
            customizeFormDataManager.saveFromDataByEnrollids(Lists.newArrayList(id));
            return null;
        });
        return null;
    }

    @Override
    public Result deleteMankeepRedisAccessToken() {
        redisManager.deleteMankeepRedisAccessToken();
        return null;
    }

    @Override
    public Result deleteMankeepProRedisAccessToken() {
        redisManager.deleteMankeepProRedisAccessToken();
        return null;
    }

    @Override
    public Result reflashMlData() {
        doMigrateEas(Lists.newArrayList("ea"), "reflashMlData", ea -> {
            campaignMergeDataResetManager.reflashMlData();
            return null;
        });
        return null;
    }

    @Override
    public Result resetLiveAllData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetLiveAllData", ea -> {
            campaignMergeDataResetManager.resetLiveAllData(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result<Void> statisticAllMemberData(MigrateEasArg arg) {
        final Logger logger = LoggerFactory.getLogger("member-statistic");
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        logger.info("Statistic member data start.");
        eas.forEach(ea -> {
            try {
                Integer ei = eieaConverter.enterpriseAccountToId(ea);
                String enterpriseName = "--";
                GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
                enterpriseDataArg.setEnterpriseAccount(ea);
                enterpriseDataArg.setEnterpriseId(ei);
                GetEnterpriseDataResult getEnterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
                if (getEnterpriseDataResult.getEnterpriseData() != null){
                    enterpriseName = getEnterpriseDataResult.getEnterpriseData().getEnterpriseName();
                }
                MemberStatusResult memberStatusResult = memberManager.isOpenMember(ea, -10000);
                if (memberStatusResult == null){
                    logger.warn("Empty at get member open status:{} enterpriseName:{}", ea, enterpriseName);
                }else if (memberStatusResult.getEnableStatus() == 1){
                    int count = crmMetadataManager.countCrmObjectData(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName());
                    logger.info("Member opened:{} ei:{} memberCount:{} enterpriseName:{}", ea, ei, count, enterpriseName);
                    if (count > 0){
                        ControllerListArg controllerListArg = new ControllerListArg();
                        controllerListArg.setObjectDescribeApiName(CrmObjectApiNameEnum.MEMBER.getName());
                        controllerListArg.setIncludeLayout(false);
                        controllerListArg.setIncludeDescribe(false);
                        controllerListArg.setIncludeButtonInfo(false);
                        SearchQuery searchQuery = new SearchQuery();
                        searchQuery.setOffset(0);
                        searchQuery.setLimit(50);
                        searchQuery.addOrderBy("create_time", false);
                        controllerListArg.setSearchQuery(searchQuery);
                        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> result = metadataControllerService.list(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MEMBER.getName(), controllerListArg);
                        if (result.getData() != null && result.getData().getDataList() != null){
                            for (ObjectData objectData : result.getData().getDataList()) {
                                MemberData crmMemberVo = MemberData.wrap(objectData);
                                logger.info("------ customerName:{} memberNo:{} createTime:{}", crmMemberVo.getName(), crmMemberVo.getCustomerIdName(), crmMemberVo.getCreateTime() == null ? "--" : DateUtil.format(crmMemberVo.getCreateTime()));
                            }
                        }
                    }
                }
            }catch (Exception e){
                logger.warn("Exception:{}", ea);
            }
        });
        logger.info("Statistic member data end");
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateMemberDescribeAndLayout(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        List<String> memberOpenedEas = eas.stream().filter(ea -> {
            try {
                MemberStatusResult memberStatus = memberManager.isOpenMember(ea, -10000);
                return memberStatus != null && memberStatus.getEnableStatus() != null && memberStatus.getEnableStatus() == 1;
            }catch (Exception e){
                log.warn("migrateMemberDescribeAndLayout get open status error, ea:{}", ea);
                return false;
            }
        }).collect(Collectors.toList());
        doMigrateEas(memberOpenedEas, "migrateMemberDescribeAndLayout", ea -> {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            HeaderObj systemHeader = new HeaderObj(ei, -10000);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, -10000), "MemberObj");
            if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null){
                return "MemberObj not existed";
            }

            ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
            Map<String, FieldDescribe> fieldMap = describe.getFields();

            // update name field to not unique
            if (fieldMap.get(CrmMemberFieldEnum.NAME.getApiName()) != null){
                if (BooleanUtils.isTrue(fieldMap.get(CrmMemberFieldEnum.NAME.getApiName()).isUnique())){
                    fieldMap.get(CrmMemberFieldEnum.NAME.getApiName()).put(IS_UNIQUE, false);
                    fieldMap.get(CrmMemberFieldEnum.NAME.getApiName()).put(LABEL, CrmMemberFieldEnum.NAME.getLabel());
                    objectDescribeService.updateField(new HeaderObj(ei, -10000), "MemberObj", CrmMemberFieldEnum.NAME.getApiName(), fieldMap.get(CrmMemberFieldEnum.NAME.getApiName()));
                }
            }

            if (fieldMap.get(CrmMemberFieldEnum.CUSTOMER_ID.getApiName()) != null){
                if (BooleanUtils.isTrue(fieldMap.get(CrmMemberFieldEnum.CUSTOMER_ID.getApiName()).isRequired())){
                    fieldMap.get(CrmMemberFieldEnum.CUSTOMER_ID.getApiName()).put(IS_REQUIRED, false);
                    objectDescribeService.updateField(new HeaderObj(ei, -10000), "MemberObj", CrmMemberFieldEnum.CUSTOMER_ID.getApiName(), fieldMap.get(CrmMemberFieldEnum.CUSTOMER_ID.getApiName()));
                }
            }

            if (fieldMap.get(CrmMemberFieldEnum.GRADE_ID.getApiName()) != null){
                if (BooleanUtils.isTrue(fieldMap.get(CrmMemberFieldEnum.GRADE_ID.getApiName()).isRequired())){
                    fieldMap.get(CrmMemberFieldEnum.GRADE_ID.getApiName()).put(IS_REQUIRED, false);
                    objectDescribeService.updateField(new HeaderObj(ei, -10000), "MemberObj", CrmMemberFieldEnum.GRADE_ID.getApiName(), fieldMap.get(CrmMemberFieldEnum.GRADE_ID.getApiName()));
                }
            }

            if (fieldMap.get(CrmMemberFieldEnum.CARD_NO.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": true,\"label\": \"会员卡号\",\"type\": \"text\",\"is_required\": false,\"api_name\": \"card_no\",\"status\": \"new\"}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"会员卡号\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            if (fieldMap.get(CrmMemberFieldEnum.PHONE.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": true,\"label\": \"手机\",\"type\": \"phone_number\",\"is_required\": false,\"api_name\": \"phone\",\"status\": \"new\"}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"phone_number\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"手机\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            // add email field
            if (fieldMap.get(CrmMemberFieldEnum.EMAIL.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\",\"define_type\":\"package\",\"is_index\": true,\"is_active\": true,\"is_unique\": false,\"label\": \"邮箱\",\"type\": \"email\",\"is_required\": false,\"api_name\": \"email\",\"help_text\": \"\",\"status\": \"new\"}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"email\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"邮箱\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            if (fieldMap.get(CrmMemberFieldEnum.ADD_SOURCE.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\", \"define_type\":\"package\", \"is_index\": true, \"is_active\": true, \"is_unique\": false, \"label\": \"来源\", \"type\": \"select_one\", \"is_required\": false, \"api_name\": \"add_source\", \"help_text\": \"\", \"status\": \"new\", \"options\": [{\"not_usable\": false, \"label\": \"微信\", \"value\": \"wechat\"}, {\"not_usable\": false, \"label\": \"门店\", \"value\": \"store\"}, {\"not_usable\": false, \"label\": \"其他\", \"value\": \"other\"}]}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"来源\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            if (fieldMap.get(CrmMemberFieldEnum.GENDER.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\", \"define_type\":\"package\", \"is_index\": true, \"is_active\": true, \"is_unique\": false, \"label\": \"性别\", \"type\": \"select_one\", \"is_required\": false, \"api_name\": \"gender\", \"help_text\": \"\", \"status\": \"new\", \"options\": [{\"not_usable\": false, \"label\": \"男\", \"value\": \"male\"}, {\"not_usable\": false, \"label\": \"女\", \"value\": \"female\"}]}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"性别\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            if (fieldMap.get(CrmMemberFieldEnum.BIRTHDAY.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\", \"define_type\":\"package\", \"is_index\": true,\"is_active\": true,\"is_unique\": false,\"label\": \"生日\",\"type\": \"date\",\"is_required\": false,\"api_name\": \"birthday\",\"date_format\": \"yyyy-MM-dd\",\"status\": \"new\"}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"date\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"生日\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            if (fieldMap.get(CrmMemberFieldEnum.AVATAR.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\", \"define_type\":\"package\", \"is_index\": false,\"is_active\": true,\"is_unique\": false,\"label\": \"头像\",\"type\": \"image\",\"is_required\": false,\"api_name\": \"avatar\",\"status\": \"new\"}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"image\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"头像\",\"is_default\":true}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }

            if (fieldMap.get(CrmMemberFieldEnum.AREA_LOCATION.getApiName()) == null){
                AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                addDescribeCustomFieldArg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\": \"MemberObj\", \"define_type\":\"package\", \"is_unique\": false, \"group_type\": \"area\", \"type\": \"group\", \"area_country\": null, \"is_required\": false, \"define_type\": \"package\", \"is_extend\": false, \"is_single\": false, \"is_index\": true, \"is_active\": true, \"label\": \"地区定位\", \"area_district\": null, \"is_abstract\": null, \"field_num\": null, \"is_need_convert\": false, \"api_name\": \"area_location\", \"area_province\": null, \"_id\": \"5f9a6eb46070850001ba63d5\", \"fields\": {\"area_country\": \"country\", \"area_location\": \"location\", \"area_detail_address\": \"address\", \"area_city\": \"city\", \"area_province\": \"province\", \"area_district\": \"district\"}, \"is_index_field\": false, \"status\": \"new\"}");
                addDescribeCustomFieldArg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"group\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"头像\",\"is_default\":true}]");
                addDescribeCustomFieldArg.setGroupFields("[{\"type\":\"country\",\"define_type\":\"package\",\"api_name\":\"country\",\"label\":\"国家\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"used_in\":\"component\",\"is_extend\":false},{\"type\":\"province\",\"define_type\":\"package\",\"api_name\":\"province\",\"label\":\"省\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"country\",\"used_in\":\"component\",\"is_extend\":false},{\"type\":\"city\",\"define_type\":\"package\",\"api_name\":\"city\",\"label\":\"市\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"province\",\"used_in\":\"component\",\"is_extend\":false},{\"type\":\"district\",\"define_type\":\"package\",\"api_name\":\"district\",\"label\":\"区\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"cascade_parent_api_name\":\"city\",\"used_in\":\"component\",\"is_extend\":false},{\"type\":\"text\",\"define_type\":\"package\",\"api_name\":\"address\",\"label\":\"详细地址\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":100,\"input_mode\":\"\",\"used_in\":\"component\",\"is_extend\":false},{\"type\":\"location\",\"define_type\":\"package\",\"api_name\":\"location\",\"label\":\"定位\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"resource_bundle_key\":null,\"status\":\"new\",\"is_geo_index\":false,\"used_in\":\"component\",\"is_extend\":false}]");
                objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
            }
            reorderMemberLayoutField(ei);

            return true;
        });
        return null;
    }

    private void reorderMemberLayoutField(Integer tenantId){
        FindDraftAndLayoutArg arg = new FindDraftAndLayoutArg("MemberObj", "Member_customer_default_layout__c");
        InnerResult<FindDraftAndLayoutResult> findResult = objectLayoutService.findDraftAndLayout(new HeaderObj(tenantId, -10000), arg);
        TypeHashMap<String, Object> layout =  findResult.getResult().getLayout();
        if (layout != null){
            List<Map<String, Object>> components = (List<Map<String, Object>>)layout.get("components");
            Map<String, Object> formComponent = null;
            for (Map<String, Object> component : components) {
                if ("form_component".equals(component.get("api_name"))){
                    formComponent = component;
                }
            }
            if (formComponent != null) {
                List<Map<String, Object>> fieldSections = (List<Map<String, Object>>)formComponent.get("field_section");
                Map<String, Object> baseFieldSection = null;
                for (Map<String, Object> fieldSection : fieldSections) {
                    if ("base_field_section__c".equals(fieldSection.get("api_name"))){
                        baseFieldSection = fieldSection;
                    }
                }
                if (baseFieldSection != null) {
                    List<Map<String, Object>> formFields = (List<Map<String, Object>>)baseFieldSection.get("form_fields");
                    List<String> fieldOrder = ImmutableList.of(CrmMemberFieldEnum.NAME.getApiName(), CrmMemberFieldEnum.CARD_NO.getApiName(), CrmMemberFieldEnum.PHONE.getApiName(), CrmMemberFieldEnum.EMAIL.getApiName(),
                        CrmMemberFieldEnum.GENDER.getApiName(), CrmMemberFieldEnum.BIRTHDAY.getApiName(), CrmMemberFieldEnum.ADD_SOURCE.getApiName(), CrmMemberFieldEnum.CUSTOMER_ID.getApiName(),
                        CrmMemberFieldEnum.INTEGRAL_VALUE.getApiName(), CrmMemberFieldEnum.GROWTH_VALUE.getApiName(), CrmMemberFieldEnum.GRADE_ID.getApiName(), CrmMemberFieldEnum.OWNER.getApiName(), CrmMemberFieldEnum.AVATAR.getApiName());
                    formFields.sort((m1, m2) -> {
                        int indexM1 = 10000;
                        for (int i = 0; i < fieldOrder.size(); i++) {
                            if (fieldOrder.get(i).equals(m1.get("field_name"))){
                                indexM1 = i;
                            }
                        }
                        int indexM2 = 10000;
                        for (int i = 0; i < fieldOrder.size(); i++) {
                            if (fieldOrder.get(i).equals(m2.get("field_name"))){
                                indexM2 = i;
                            }
                        }
                        return indexM1 - indexM2;
                    });
                    formFields.forEach(formField -> {
                        if(CrmMemberFieldEnum.CUSTOMER_ID.getApiName().equals(formField.get("field_name")) || CrmMemberFieldEnum.GRADE_ID.getApiName().equals(formField.get("field_name"))){
                            formField.put("is_required", false);
                        }
                    });
                    UpdateLayoutAndUpdateDescribeArg updateLayoutAndUpdateDescribeArg = new UpdateLayoutAndUpdateDescribeArg();
                    updateLayoutAndUpdateDescribeArg.setLayoutData(JsonUtil.toJson(layout));
                    com.fxiaoke.crmrestapi.common.result.Result<Map<String, Object>> describeResult = objectDescribeService.getDescribeAsMap(new HeaderObj(tenantId, -10000), "MemberObj");
                    if (describeResult.isSuccess()){
                        updateLayoutAndUpdateDescribeArg.setDescribeData(JsonUtil.toJson(describeResult.getData().get("describe")));
                    }
                    objectLayoutService.updateLayoutAndUpdateDescribe(new HeaderObj(tenantId, 1000), updateLayoutAndUpdateDescribeArg);
                }
            }
        }
    }

    @Override
    public Result migrateAppendLeadMarketingSpreadUser(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.initAll)) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, ea -> {
            officialWebsiteManager.appendLeadMarketingSpreadUser(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result<Void> enableMarketingPromotionChannel(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, (ea) -> {
            spreadChannelManager.enableMarketingPromotionChannel(ea);
            return "success";
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> migrateIntegrateCategory() {
        List<MailSendReplyEntity> mailSendReplyEntities = mailSendReplyDAO.listAllUsableMailSender();
        log.info("migrateIntegrateCategory emailSender, total count:{}", mailSendReplyEntities.size());
        for (int i = 0; i < mailSendReplyEntities.size(); i++) {
            MailSendReplyEntity mailSender = mailSendReplyEntities.get(i);
            integralServiceManager.syncRegisterMaterial(mailSender.getEa(), CategoryApiNameConstant.MAIL_BOX, mailSender.getId(), mailSender.getAddress());
            log.info("migrateIntegrateCategory emailSender current progress:{}", i + 1);
        }

        List<MailSendTaskEntity> mailSendTaskEntities = mailSendTaskDAO.listAllWithoutBigField();
        log.info("migrateIntegrateCategory email, total count:{}", mailSendTaskEntities.size());
        for (int i = 0; i < mailSendTaskEntities.size(); i++) {
            MailSendTaskEntity task = mailSendTaskEntities.get(i);
            integralServiceManager.syncRegisterMaterial(task.getEa(), CategoryApiNameConstant.MAIL, HashUtil.hash(task.getSubject()), task.getSubject());
            log.info("migrateIntegrateCategory email current progress:{}", i + 1);
        }

        List<OfficialWebsiteEventAttributesEntity> officialWebsiteEventAttributesEntities = officialWebsiteEventAttributesDAO.listAllUsableWebsiteEvent();
        log.info("migrateIntegrateCategory officialWebsiteEvent, total count:{}", officialWebsiteEventAttributesEntities.size());
        for (int i = 0; i < officialWebsiteEventAttributesEntities.size(); i++) {
            OfficialWebsiteEventAttributesEntity event = officialWebsiteEventAttributesEntities.get(i);
            integralServiceManager.syncRegisterMaterial(event.getEa(), CategoryApiNameConstant.WEBSITE_ELEMENT, event.getId(), event.getContent().getName());
            log.info("migrateIntegrateCategory officialWebsiteEvent current progress:{}", i + 1);
        }

        List<MarketingLiveEntity> marketingLives = marketingLiveDAO.listAllMarketingLive();
        log.info("migrateIntegrateCategory live, total count:{}", marketingLives.size());
        for (int i = 0; i < marketingLives.size(); i++) {
            MarketingLiveEntity event = marketingLives.get(i);
            integralServiceManager.syncRegisterMaterial(eieaConverter.enterpriseIdToAccount(event.getCorpId()), CategoryApiNameConstant.LIVE, event.getId(), event.getTitle());
            log.info("migrateIntegrateCategory live current progress:{}", i + 1);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> migrateUserToCrmMemberManagerRole() {
        List<UserRoleEntity> userRoleEntities = userRoleDao.listByRole(RoleConstant.MEMBER_OPERATION);
        for (UserRoleEntity userRole : userRoleEntities) {
            settingManager.addUsersToPaasMemberManager(userRole.getEa(), ImmutableSet.of(userRole.getEmployeeId()));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> tryAppendWechatFanFields(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.initAll)) {
            eas = marketingWxServiceDao.listAllEas();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "tryAppendWechatFanFields", ea -> crmV2Manager.tryAddFieldsToWechatFans(ea));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> markWxFanSpreadUserAsSingle(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.initAll)) {
            eas = marketingWxServiceDao.listAllEas();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "markWxFanSpreadUserAsSingle", ea -> crmV2Manager.markWxFanSpreadUserAsSingle(ea));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncCrmDataToMarketingUserAccount(MigrateEasArg easArg) {
        List<String> eas;
        if (BooleanUtils.isTrue(easArg.initAll)) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = easArg.getEas();
        }
        splitEas(eas, 5).forEach((idx, partEas) -> {
            new Thread(() -> {
                doMigrateEas(partEas, "syncCrmDataToMarketingUserAccount" + idx, ea -> {
                    GetEnterpriseRunStatusArg arg = new GetEnterpriseRunStatusArg();
                    arg.setEnterpriseAccount(ea);
                    arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
                    GetEnterpriseRunStatusResult getEnterpriseRunStatusResult = enterpriseEditionService.getEnterpriseRunStatus(arg);
                    if (getEnterpriseRunStatusResult != null && getEnterpriseRunStatusResult.getRunStatus().equals(RunStatus.RUN_STATUS_STOP)) {
                        return 0;
                    }
                    int totalCount = 0;
                    // CRM 客户数据
                    final int limit = 2000;
                    for (int offset = 0; offset < 3000000; offset= offset + limit) {
                        List<String> objectIds = userMarketingCrmAccountAccountRelationDao.pageListObjectIdByEa(ea, offset, limit);
                        final int batchCrmQuerySize = 200;
                        Iterators.partition(objectIds.iterator(), batchCrmQuerySize).forEachRemaining(partObjectIds -> {
                            try {
                                SearchQuery searchQuery = new SearchQuery();
                                searchQuery.addFilter("_id", new ArrayList<>(partObjectIds), FilterOperatorEnum.IN);
                                searchQuery.setOffset(0);
                                searchQuery.setLimit(batchCrmQuerySize);
                                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), searchQuery, Lists.newArrayList("_id", "name", "email"));
                                if (result != null && result.getDataList() != null && !result.getDataList().isEmpty()){
                                    List<Map<String, String>> dataList = result.getDataList().stream().map(data -> {
                                        Map<String, String> tmp = new HashMap<>(4);
                                        tmp.put("ea", ea);
                                        tmp.put("objectId", data.getId());
                                        tmp.put("userName", data.getName());
                                        tmp.put("email", data.getString("email"));
                                        return tmp;
                                    }).collect(Collectors.toList());
                                    userMarketingCrmAccountAccountRelationDao.batchUpdateNameAndEmail(dataList);
                                }
                            } catch (Exception e){
                                log.warn("syncCrmDataToMarketingUserAccount part error, ea:{}", ea);
                            }
                        });
                        if (objectIds.size() < limit){
                            totalCount = totalCount + offset + objectIds.size();
                            break;
                        }
                    }

                    // CRM 联系人数据
                    for (int offset = 0; offset < 3000000; offset= offset + limit) {
                        List<String> objectIds = userMarketingCrmContactAccountRelationDao.pageListObjectIdByEa(ea, offset, limit);
                        final int batchCrmQuerySize = 200;
                        Iterators.partition(objectIds.iterator(), batchCrmQuerySize).forEachRemaining(partObjectIds -> {
                            try {
                                SearchQuery searchQuery = new SearchQuery();
                                searchQuery.addFilter("_id", new ArrayList<>(partObjectIds), FilterOperatorEnum.IN);
                                searchQuery.setOffset(0);
                                searchQuery.setLimit(batchCrmQuerySize);
                                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.CONTACT.getName(), searchQuery, Lists.newArrayList("_id", "name", "email"));
                                if (result != null && result.getDataList() != null && !result.getDataList().isEmpty()){
                                    List<Map<String, String>> dataList = result.getDataList().stream().map(data -> {
                                        Map<String, String> tmp = new HashMap<>(4);
                                        tmp.put("ea", ea);
                                        tmp.put("objectId", data.getId());
                                        tmp.put("userName", data.getName());
                                        tmp.put("email", data.getString("email"));
                                        return tmp;
                                    }).collect(Collectors.toList());
                                    userMarketingCrmContactAccountRelationDao.batchUpdateNameAndEmail(dataList);
                                }
                            } catch (Exception e){
                                log.warn("syncCrmDataToMarketingUserAccount part error, ea:{}", ea);
                            }
                        });
                        if (objectIds.size() < limit){
                            totalCount = totalCount + offset + objectIds.size();
                            break;
                        }
                    }

                    //CRM 线索数据
                    for (int offset = 0; offset < 3000000; offset= offset + limit) {
                        List<String> objectIds = userMarketingCrmLeadAccountRelationDao.pageListObjectIdByEa(ea, offset, limit);
                        final int batchCrmQuerySize = 200;
                        Iterators.partition(objectIds.iterator(), batchCrmQuerySize).forEachRemaining(partObjectIds -> {
                            try {
                                SearchQuery searchQuery = new SearchQuery();
                                searchQuery.addFilter("_id", new ArrayList<>(partObjectIds), FilterOperatorEnum.IN);
                                searchQuery.setOffset(0);
                                searchQuery.setLimit(batchCrmQuerySize);
                                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.CRM_LEAD.getName(), searchQuery, Lists.newArrayList("_id", "name", "email"));
                                if (result != null && result.getDataList() != null && !result.getDataList().isEmpty()){
                                    List<Map<String, String>> dataList = result.getDataList().stream().map(data -> {
                                        Map<String, String> tmp = new HashMap<>(4);
                                        tmp.put("ea", ea);
                                        tmp.put("objectId", data.getId());
                                        tmp.put("userName", data.getName());
                                        tmp.put("email", data.getString("email"));
                                        return tmp;
                                    }).collect(Collectors.toList());
                                    userMarketingCrmLeadAccountRelationDao.batchUpdateNameAndEmail(dataList);
                                }
                            } catch (Exception e){
                                log.warn("syncCrmDataToMarketingUserAccount part error, ea:{}", ea);
                            }
                        });
                        if (objectIds.size() < limit){
                            totalCount = totalCount + offset + objectIds.size();
                            break;
                        }
                    }

                    // CRM 微信用户数据
                    for (int offset = 0; offset < 3000000; offset= offset + limit) {
                        List<String> objectIds = userMarketingCrmWxUserAccountRelationDao.pageListObjectIdByEa(ea, offset, limit);
                        final int batchCrmQuerySize = 200;
                        Iterators.partition(objectIds.iterator(), batchCrmQuerySize).forEachRemaining(partObjectIds -> {
                            try {
                                SearchQuery searchQuery = new SearchQuery();
                                searchQuery.addFilter("_id", new ArrayList<>(partObjectIds), FilterOperatorEnum.IN);
                                searchQuery.setOffset(0);
                                searchQuery.setLimit(batchCrmQuerySize);
                                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), searchQuery, Lists.newArrayList("_id", "name", "email"));
                                if (result != null && result.getDataList() != null && !result.getDataList().isEmpty()){
                                    List<Map<String, String>> dataList = result.getDataList().stream().map(data -> {
                                        Map<String, String> tmp = new HashMap<>(4);
                                        tmp.put("ea", ea);
                                        tmp.put("objectId", data.getId());
                                        tmp.put("userName", data.getName());
                                        tmp.put("email", data.getString("email"));
                                        return tmp;
                                    }).collect(Collectors.toList());
                                    userMarketingCrmWxUserAccountRelationDao.batchUpdateNameAndEmail(dataList);
                                }
                            } catch (Exception e){
                                log.warn("syncCrmDataToMarketingUserAccount part error, ea:{}", ea);
                            }
                        });
                        if (objectIds.size() < limit){
                            totalCount = totalCount + offset + objectIds.size();
                            break;
                        }
                    }

                    // CRM 企业微信用户
                    for (int offset = 0; offset < 3000000; offset= offset + limit) {
                        List<String> objectIds = userMarketingCrmWxWorkExternalUserRelationDao.pageListObjectIdByEa(ea, offset, limit);
                        final int batchCrmQuerySize = 200;
                        Iterators.partition(objectIds.iterator(), batchCrmQuerySize).forEachRemaining(partObjectIds -> {
                            try {
                                SearchQuery searchQuery = new SearchQuery();
                                searchQuery.addFilter("_id", new ArrayList<>(partObjectIds), FilterOperatorEnum.IN);
                                searchQuery.setOffset(0);
                                searchQuery.setLimit(batchCrmQuerySize);
                                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), searchQuery, Lists.newArrayList("_id", "name", "email"));
                                if (result != null && result.getDataList() != null && !result.getDataList().isEmpty()){
                                    List<Map<String, String>> dataList = result.getDataList().stream().map(data -> {
                                        Map<String, String> tmp = new HashMap<>(4);
                                        tmp.put("ea", ea);
                                        tmp.put("objectId", data.getId());
                                        tmp.put("userName", data.getName());
                                        tmp.put("email", data.getString("email"));
                                        return tmp;
                                    }).collect(Collectors.toList());
                                    userMarketingCrmWxWorkExternalUserRelationDao.batchUpdateNameAndEmail(dataList);
                                }
                            } catch (Exception e){
                                log.warn("syncCrmDataToMarketingUserAccount part error, ea:{}", ea);
                            }
                        });
                        if (objectIds.size() < limit){
                            totalCount = totalCount + offset + objectIds.size();
                            break;
                        }
                    }

                    //CRM会员
                    for (int offset = 0; offset < 3000000; offset= offset + limit) {
                        List<String> objectIds = userMarketingCrmMemberRelationDao.pageListObjectIdByEa(ea, offset, limit);
                        final int batchCrmQuerySize = 200;
                        Iterators.partition(objectIds.iterator(), batchCrmQuerySize).forEachRemaining(partObjectIds -> {
                            try {
                                SearchQuery searchQuery = new SearchQuery();
                                searchQuery.addFilter("_id", new ArrayList<>(partObjectIds), FilterOperatorEnum.IN);
                                searchQuery.setOffset(0);
                                searchQuery.setLimit(batchCrmQuerySize);
                                Page<ObjectData> result = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), searchQuery, Lists.newArrayList("_id", "name", "email"));
                                if (result != null && result.getDataList() != null && !result.getDataList().isEmpty()){
                                    List<Map<String, String>> dataList = result.getDataList().stream().map(data -> {
                                        Map<String, String> tmp = new HashMap<>(4);
                                        tmp.put("ea", ea);
                                        tmp.put("objectId", data.getId());
                                        tmp.put("userName", data.getName());
                                        tmp.put("email", data.getString("email"));
                                        return tmp;
                                    }).collect(Collectors.toList());
                                    userMarketingCrmMemberRelationDao.batchUpdateNameAndEmail(dataList);
                                }
                            } catch (Exception e){
                                log.warn("syncCrmDataToMarketingUserAccount part error, ea:{}", ea);
                            }
                        });
                        if (objectIds.size() < limit){
                            totalCount = totalCount + offset + objectIds.size();
                            break;
                        }
                    }

                    return totalCount;
                });
            }).start();
        });
        return Result.newSuccess();
    }

    private Map<Integer, List<String>> splitEas(List<String> eas, int splitCount){
        Map<Integer, List<String>> splitResult = new HashMap<>(splitCount);
        for (int i = 0; i < eas.size(); i++) {
            int index = (i % splitCount) + 1;
            splitResult.putIfAbsent(index, new LinkedList<>());
            splitResult.get(index).add(eas.get(i));
        }
        return splitResult;
    }

    @Override
    public Result<Void> calculateMarketingUserGroup(MigrateEasArg easArg) {
        List<String> eas;
        if (BooleanUtils.isTrue(easArg.initAll)) {
            eas = marketingUserGroupDao.listAllEa();
        } else {
            eas = easArg.getEas();
        }
        doMigrateEas(eas, ea -> {
            List<String> marketingUserGroupIds = marketingUserGroupDao.listByEa(ea).stream().map(MarketingUserGroupEntity::getId).collect(Collectors.toList());
            for (String marketingUserGroupId : marketingUserGroupIds) {
                marketingUserGroupManager.asyncCalculateUserMarketingAccountData(ea, -10000,  marketingUserGroupId);
            }
            return marketingUserGroupIds.size();
        });
        return Result.newSuccess();
    }


    @Override
    public Result<Integer> migrateAllSystemPages() {
        Set<String> systemSiteIds = new HashSet<>();
        List<MemberConfigEntity> memberConfigEntities = memberConfigDao.listAll();
        for (MemberConfigEntity memberConfigEntity : memberConfigEntities) {
            if (memberConfigEntity != null){
                if(!Strings.isNullOrEmpty(memberConfigEntity.getContentCenterSiteId())){
                    systemSiteIds.add(memberConfigEntity.getContentCenterSiteId());
                }
                if(!Strings.isNullOrEmpty(memberConfigEntity.getLoginSiteId())){
                    systemSiteIds.add(memberConfigEntity.getLoginSiteId());
                }
                if(!Strings.isNullOrEmpty(memberConfigEntity.getRegistrationSiteId())){
                    systemSiteIds.add(memberConfigEntity.getRegistrationSiteId());
                }
            }
        }
        List<HexagonOfficialWebsiteEntity> hexagonOfficialWebsiteEntities = hexagonOfficialWebsiteDAO.listAll();
        for (HexagonOfficialWebsiteEntity hexagonOfficialWebsiteEntity : hexagonOfficialWebsiteEntities) {
            if (hexagonOfficialWebsiteEntity != null && !Strings.isNullOrEmpty(hexagonOfficialWebsiteEntity.getHexagonSiteId())){
                systemSiteIds.add(hexagonOfficialWebsiteEntity.getHexagonSiteId());
            }
        }

        List<String> siteIds = enterpriseMetaConfigDao.listAllIntroduceSiteIds();
        for (String siteId : siteIds) {
            if (!Strings.isNullOrEmpty(siteId)){
                systemSiteIds.add(siteId);
            }
        }

        for (String systemPageSiteId : systemSiteIds) {
            try {
                hexagonSiteDAO.markAsSystemSite(systemPageSiteId);
            }catch (Exception e){
                log.warn("markAsSystemSite failed, siteId:{}", systemPageSiteId);
            }
        }

        return Result.newSuccess(systemSiteIds.size());
    }

    @Override
    public Result resetMarketingNotActivityAndLive(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetMarketingNotActivityAndLive", ea -> {
            campaignMergeDataResetManager.resetMarketingNotActivityAndLive(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result resetSpreadUserZeroData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetSpreadUserZeroData", ea -> {
            campaignMergeDataResetManager.resetSpreadUserZeroData(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result resetCardQrCodeByEa(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        doMigrateEas(arg.getEas(), "resetCardQrCodeByEa", ea -> {
            cardManager.resetCardQrCodeByEa(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result resetCardQrCodeByUid(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getIds())) {
            return Result.newSuccess();
        }
        doMigrateEas(arg.getIds(), "resetCardQrCodeByUid", id -> {
            cardManager.resetCardQrCodeByUid(id);
            return null;
        });
        return null;
    }

    @Override
    public Result resetEmailData() {
//        try {
//            mailService.refreshMailEaStatisticsJob();
//        } catch (Exception e) {
//            log.warn("MigrateServiceImpl.resetEmailData refreshMailEaStatisticsJob error e:{}", e);
//        }
//        try {
//            mailService.refreshMailTaskStatisticsJob(false);
//        } catch (Exception e) {
//            log.warn("MigrateServiceImpl.resetEmailData refreshMailTaskStatisticsJob error e:{}", e);
//        }
        return null;
    }

    @Override
    public Result resetUtmFieldName(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = hexagonOfficialWebsiteDAO.findWebsiteEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetUtmFieldName", ea -> {
            officialWebsiteManager.resetUtmFieldName(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result resetMarketingEventUV(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetMarketingEventUV", ea -> {
            marketingEventAmountStatisticManager.resetUVStatistic(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result setCustomizeFormToMarketingEvent(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "setCustomizeFormToMarketingEvent", ea -> {
            customizeFormDataManager.setCustomizeFormToMarketingEvent(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result syncMarketingActivityLookupRoler(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "syncMarketingActivityLookupRuler", ea -> {
            if (!campaignMergeDataResetManager.enterpriseStop(ea)) {
                marketingActivityRemoteManager.addMarketingEventLookRoler(eieaConverter.enterpriseAccountToId(ea), -10000);
                crmDataAuthManager.updateEntityOpenness(ea, -10000, "MarketingActivityObj", "2", "2");
            }
            return null;
        });
        return null;
    }

    @Override
    public Result resetCustomizeFormData(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetCustomizeFormData", ea -> {
            resetCustomizeFormDataManager.resetCustomizeFormStyle(ea, null);
            return null;
        });
        return null;
    }

    @Override
    public Result resetCustomizeFormDataById(MigrateEasArg easArg) {
        if(CollectionUtils.isEmpty(easArg.getIds())) {
            return null;
        }
        doMigrateEas(easArg.getIds(), "resetCustomizeFormDataById", id -> {
            resetCustomizeFormDataManager.resetCustomizeFormStyle(null, id);
            return null;
        });
        return null;
    }

    @Override
    public Result resetCustomizeMiniAppCardNavbar(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetCustomizeMiniAppCardNavbar", ea -> {
            customizeMiniAppCardNavbarManager.resetCustomizeMiniAppCardNavbar(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result initOldMakeringRoleFunctionPrivilege(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "initOldMakeringRoleFunctionPrivilege", ea -> {
            userRoleManager.initOldMakeringRoleFunctionPrivilege(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result createDefaultConferenceSite(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getIds())) {
            return null;
        }
        doMigrateEas(arg.getIds(), "createDefaultConferenceSite", id -> {
            conferenceManager.createDefaultConferenceSite(id);
            return null;
        });
        return null;
    }

    @Override
    public Result syncContentMarketingEventMaterialRelationEventType(MigrateEasArg arg) {
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                marketingEventManager.syncContentMarketingEventMaterialRelationEventType();
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return null;
    }

    @Override
    public Result syncFileRegisterActionMaterial(MigrateEasArg arg) {
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                List<FileEntity> fileEntityList = fileLibraryDAO.getAllFiles();
                for (FileEntity fileEntity : fileEntityList){
                    integralServiceManager.asyncRegisterMaterial(fileEntity.getEa(), CategoryApiNameConstant.FILE, fileEntity.getId(), fileEntity.getFileName());
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return null;
    }

    @Override
    public Result resetConferenceStyle(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetConferenceStyle", ea -> {
            resetConferenceManager.resetConferenceStyle(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result resetConferenceCustomizeFormDataUser(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "resetConferenceCustomizeFormDataUser", ea -> {
            resetConferenceManager.resetConferenceCustomizeFormDataUser(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result initQywxAddressBook(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = Lists.newArrayList("setAll");
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "initQywxAddressBook", ea -> {
            resetQywxAddressBookManager.initQywxAddressBook(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result refreshCampaignMergeDataFormMarketingEvent(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getIds()) || CollectionUtils.isEmpty(arg.getEas())) {
            return null;
        }
        String ea = arg.getEas().get(0);
        doMigrateEas(arg.getIds(), "refreshCampaignMergeDataFormMarketingEvent", id -> {
            campaignMergeDataResetManager.refreshCampaignMergeDataFormMarketingEvent(ea, id);
            return null;
        });
        return null;
    }

    @Override
    public Result fixQywxEmployeeMarketingActivtyStatData(MigrateEasArg arg) {
        List<String> eas = arg.getEas();
        doMigrateEas(eas, "fixQywxEmployeeMarketingActivtyStatData", ea -> {
            marketingActivityManager.fixQywxEmployeeMarketingActivtyStatData(ea);
            return null;
        });
        return null;
    }


    @Override
    public Result syncCrmInvalidMarketingEventLive(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }

        doMigrateEas(eas, "syncCrmInvalidMarketingEventLive", ea -> {
            liveManager.syncCrmInvalidMarketingEventLive(ea);
            return null;
        });
        return null;
    }

    @Override
    public Result addMarketingActivityObjField(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        doMigrateEas(eas, "addMarketingActivityObjField", ea -> {
            marketingActivityRemoteManager.addMarketingObjField(eieaConverter.enterpriseAccountToId(ea), -10000,ea);
            return null;
        });
        return null;
    }

    @Override
    public Result updateWeChatMarketingActivityWxAppId(MigrateEasArg arg) {
        List<String> eas = null;
        if (arg.initAll) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        if (CollectionUtils.isEmpty(eas)) {
            return null;
        }
        for (String ea : eas) {
            ThreadPoolUtils.execute(() -> {
                List<MarketingActivityExternalConfigEntity> weChatSpreadActivityIdConfig = marketingActivityExternalConfigDao.getWeChatSpreadActivityIdConfig(ea);
                if (null == weChatSpreadActivityIdConfig || weChatSpreadActivityIdConfig.size() == 0) {
                    return ;
                }
                OuterServiceWechatVO outerServiceCrmRoleVO = new OuterServiceWechatVO();
                outerServiceCrmRoleVO.setFsEa(ea);
                Pager<OuterServiceWechatDetailResult> pager = new Pager();
                pager.setPageSize(300);
                ModelResult<Pager<OuterServiceWechatDetailResult>> pagerModelResult;
                Map<String, String> appIdToWxAppIdMap = new HashMap<>();
                int page = 0;
                while (true) {
                    pager.setCurrentPage(page);
                    pagerModelResult = wechatUnionService.queryPageOuterServiceWechatResult(pager, outerServiceCrmRoleVO);
                    if (null != pagerModelResult && pagerModelResult.isSuccess() && null != pagerModelResult.getResult()
                            && null != pagerModelResult.getResult().getData() && pagerModelResult.getResult().getData().size() > 0) {
                        pagerModelResult.getResult().getData().stream().forEach(p -> appIdToWxAppIdMap.put(p.getAppId(), p.getWxAppId()));
                        page++;
                    } else {
                        break;
                    }
                    if (page > 3) {
                        break;
                    }
                }
                for (MarketingActivityExternalConfigEntity temp : weChatSpreadActivityIdConfig) {
                    marketingActivityRemoteManager.updateWeChatMarketingActivityWxAppId(temp, eieaConverter.enterpriseAccountToId(temp.getEa()), appIdToWxAppIdMap);
                }
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        }
        return null;
    }

    @Override
    public Result initMarketingLicense(MigrateEasArg arg) {
        if (arg.getInitAll()){
            return Result.newSuccess();
        }

        if (arg.eas.size() != 2){
            return Result.newSuccess();
        }

        String ea = arg.getEas().get(0);
        String moduleCode = arg.getEas().get(1);
        settingManager.initMarketingData(ea, -10000, moduleCode);
        handler.configAppCenterVisible(ea);
        return Result.newSuccess();
    }


    @Override
    public Result addLeadsObjSpreadChannelField(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        Iterator<String> iterator = eas.iterator();
        String nextEa;
        while (iterator.hasNext()) {
            nextEa = iterator.next();
            boolean isStop = marketingActivityRemoteManager.enterpriseStop(nextEa);
            // 企业已停用或者没有开通企业微信 直接跳过
            if (isStop) {
                iterator.remove();
            }
        }

        doMigrateEas(eas, "addLeadsObjSpreadChannelField", ea -> {
            List<SpreadChannelManager.SpreadChannleOption> spreadChannleOptionList = Lists.newArrayList();
            SpreadChannelManager.SpreadChannleOption spreadChannleOption = new SpreadChannelManager.SpreadChannleOption();
//            spreadChannleOption.setLabel("企业微信");
//            spreadChannleOption.setValue("qywx");
            spreadChannleOption.setLabel("在线客服");
            spreadChannleOption.setValue("onlineservice");
            spreadChannleOptionList.add(spreadChannleOption);
            spreadChannelManager.addChannel(ea, -10000, spreadChannleOptionList, false);
            return null;
        });
        return null;
    }

    public Result initAdMarketingPlugin(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = adAccountManager.getBaiduAdAccountWithOutStatus();
        } else {
            eas = arg.getEas();
        }
        Iterator<String> iterator = eas.iterator();
        String nextEa;
        while (iterator.hasNext()) {
            nextEa = iterator.next();
            boolean isStop = marketingActivityRemoteManager.enterpriseStop(nextEa);
            // 企业已停用或者没有开通企业微信 直接跳过
            if (isStop) {
                iterator.remove();
            }
        }
        doMigrateEas(eas, "initBaiduAdMarketingPlugin", ea -> {
            MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, MarketingPluginTypeEnum.BAIDU_AD.getType());
            if (marketingPluginConfigEntity == null) {
                marketingPluginConfigManager.mergePluginConfig(ea,MarketingPluginTypeEnum.BAIDU_AD.getType(), MarketingPluginTypeEnum.BAIDU_AD.getName(),true);
            } else {
                marketingPluginConfigDAO.updatePluginStatus(ea,MarketingPluginTypeEnum.BAIDU_AD.getType(),true);
            }
            return null;
        });


        return Result.newSuccess();
    }


    public Result initHeadlinesAdMarketingPlugin(MigrateEasArg arg) {
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitAll())) {
            eas = adAccountManager.getHeadlinesAdAccountWithOutStatus();
        } else {
            eas = arg.getEas();
        }
        Iterator<String> iterator = eas.iterator();
        String headlinesNextEa;
        while (iterator.hasNext()) {
            headlinesNextEa = iterator.next();
            boolean isStop = marketingActivityRemoteManager.enterpriseStop(headlinesNextEa);
            // 企业已停用或者没有开通企业微信 直接跳过
            if (isStop) {
                iterator.remove();
            }
        }
        doMigrateEas(eas, "initHeadlinesAdMarketingPlugin", ea -> {
            MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, MarketingPluginTypeEnum.HEADLINES_AD.getType());
            if (marketingPluginConfigEntity == null) {
                marketingPluginConfigManager.mergePluginConfig(ea,MarketingPluginTypeEnum.HEADLINES_AD.getType(), MarketingPluginTypeEnum.HEADLINES_AD.getName(),true);
            } else {
                marketingPluginConfigDAO.updatePluginStatus(ea,MarketingPluginTypeEnum.HEADLINES_AD.getType(),true);
            }
            return null;
        });
        return Result.newSuccess();
    }

    @Override
    public Result updateLeadCreator(MigrateEasArg arg) {
        if (arg.getInitAll()){
            return Result.newSuccess();
        }
        String ea = arg.getEas().get(0);
        ThreadPoolUtils.execute(() -> {
            customizeFormDataManager.updateFormDataLeadCreator(ea);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        //customizeFormDataManager.updateFormDataLeadCreator(ea);
        return null;
    }

    @Override
    public Result initEnterpriseMarketing(MigrateEasArg arg) {
        if (arg.getInitAll()){
            return Result.newSuccess();
        }
        String ea = arg.getEas().get(0);
        String version = arg.getIds().get(0);
        ThreadPoolUtils.execute(() -> {
            settingManager.initMarketingData(ea, -10000, version);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return null;
    }

    @Override
    public Result updateCardCover(MigrateEasArg arg) {
        if (arg.getInitAll()){
            return Result.newSuccess();
        }
        String ea = arg.getEas().get(0);
        List<Integer> userIds = null;
        if (CollectionUtils.isNotEmpty(arg.getIds())){
            userIds = new ArrayList<>();
            List<String> ids = arg.getIds();
            for (String id : ids) {
                userIds.add(Integer.parseInt(id));
            }
        }
        fsBindService.batchUpdateUserCard(ea, 1000, userIds, 0);
        return null;
    }

    @Override
    public Result insertCorpIdMapping(MigrateEasArg arg) {
        if (!arg.getInitAll()){
            return null;
        }

        ThreadPoolUtils.execute(() -> {
            List<QywxCorpAgentConfigEntity> entities = qywxCorpAgentConfigDAO.queryAllQywxCorp();
            if (CollectionUtils.isEmpty(entities)){
                return;
            }

            List<QywxCorpEaMappingEntity> mappingsList = qywxCorpEaMappingDAO.queryAllMapping();
            Set<String> corpIdSet = null;
            if (CollectionUtils.isNotEmpty(mappingsList)){
                corpIdSet = mappingsList.stream().map(QywxCorpEaMappingEntity::getCorpId).collect(Collectors.toSet());
            }

            Set<String> finalCorpIdSet = corpIdSet;
            entities.forEach(entity->{
                if(finalCorpIdSet == null || !finalCorpIdSet.contains(entity.getCorpid())){
                    QywxCorpEaMappingEntity qywxCorpEaMappingEntity = new QywxCorpEaMappingEntity();
                    qywxCorpEaMappingEntity.setId(UUIDUtil.getUUID());
                    qywxCorpEaMappingEntity.setCorpId(entity.getCorpid());
                    qywxCorpEaMappingEntity.setEa(entity.getEa());
                    qywxCorpEaMappingEntity.setHost(host);
                    qywxCorpEaMappingDAO.addMapping(qywxCorpEaMappingEntity);
                }
            });
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return null;
    }

    @Override
    public Result initPubPlatAuthComponent(MigrateEasArg arg) {
        if (!arg.getInitAll()){
            return null;
        }

        ThreadPoolUtils.execute(() -> {

            List<String> list = marketingWxServiceDao.listAllEas();
            if (CollectionUtils.isEmpty(list)){
                return;
            }
            List<WxPublicPlatformAuthorizeComponentEntity> entities = wxPublicPlatformAuthorizeComponentDao.selectall();
            Set<String> eaSet = null;
            if (CollectionUtils.isNotEmpty(entities)){
                eaSet = entities.stream().map(WxPublicPlatformAuthorizeComponentEntity::getEa).collect(Collectors.toSet());
            }
            Set<String> finalEaSet = eaSet;

            list.forEach(string->{
                if(finalEaSet == null || !finalEaSet.contains(string)) {
                    WxPublicPlatformAuthorizeComponentEntity entity = new WxPublicPlatformAuthorizeComponentEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(string);
                    entity.setSavePublicPlatformAuthorizeComponent("0");
                    wxPublicPlatformAuthorizeComponentDao.insert(entity);
                }
            });
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return null;
    }

    @Override
    public Result initWeChatGroup(MigrateEasArg arg) {
        List<String> eas;
        if (arg.initAll) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        //初始化对象和初始化企微群数据
        if (CollectionUtils.isNotEmpty(eas)) {
            eas = eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null).filter(ea -> qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null).collect(Collectors.toList());
            for (String ea : eas) {
                ThreadPoolUtils.execute(() ->{
                    log.info("进行初始化企微群 ea:{}",ea);
                    try {
                        wechatGroupObjDescribeManager.getOrCreateWechatGroupObjDescribe(ea);
                        wechatGroupUserObjDescribeManager.getOrCreateWechatGroupUserObjDescribe(ea);
                        wechatGroupObjDescribeManager.initData(ea);
                        log.info("完成企微群初始化 ea:{}",ea);
                    } catch (Exception e) {
                        log.warn("初始化企微群异常",e);
                    }
                },ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
        }
        return null;
    }

    @Override
    public Result updateMarketingBehavior(MigrateEasArg arg) {
        List<String> eas;
        if (arg.initAll) {
            eas = enterpriseMetaConfigDao.findEaAll();
        } else {
            eas = arg.getEas();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            eas = eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null).collect(Collectors.toList());
            doMigrateEas(eas, "updateMarketingBehavior", (ea) -> {
                try {
                    HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(systemHeader, "MarketingBehaviorObj");
                    if(oldDescribeResult.isSuccess() && oldDescribeResult.getData() != null && oldDescribeResult.getData().getDescribe() != null){
                        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
                        Map<String, FieldDescribe> fieldMap = describe.getFields();
                        if (fieldMap.get("account_id") == null){
                            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                            addDescribeCustomFieldArg.setDescribeAPIName("MarketingBehaviorObj");
                            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\":\"MarketingBehaviorObj\",\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"客户\",\"target_api_name\":\"AccountObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"target_related_list\",\"target_related_list_label\":\"营销动态\",\"action_on_target_delete\":\"set_null\",\"is_required\":false,\"wheres\":[],\"api_name\":\"account_id\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"target_related_list_label\":0,\"wheres\":0,\"api_name\":0,\"is_unique\":0,\"label\":0,\"target_api_name\":0,\"target_related_list_name\":0,\"help_text\":0}},\"help_text\":\"\",\"status\":\"new\"}");
                            objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
                        }
                        if (fieldMap.get("contact_id") == null){
                            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                            addDescribeCustomFieldArg.setDescribeAPIName("MarketingBehaviorObj");
                            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\":\"MarketingBehaviorObj\",\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"联系人\",\"target_api_name\":\"ContactObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"target_related_list\",\"target_related_list_label\":\"营销动态\",\"action_on_target_delete\":\"set_null\",\"is_required\":false,\"wheres\":[],\"api_name\":\"contact_id\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"target_related_list_label\":0,\"wheres\":0,\"api_name\":0,\"is_unique\":0,\"label\":0,\"target_api_name\":0,\"target_related_list_name\":0,\"help_text\":0}},\"help_text\":\"\",\"status\":\"new\"}");
                            objectDescribeCrmService.addDescribeCustomField(systemHeader, addDescribeCustomFieldArg);
                        }
                    }
                } catch (Exception e) {
                    log.warn("updateMarketingBehavior failed: ",e);
                }
                return null;
            });
        }
        return Result.newSuccess();
    }

    /**
     * @param arg initAll=false时，若eas不为空，则初始化eas
     *            initAll=true时，则初始化全部企业
     * @return
     */
    @Override
    public Result appendLeadMarketingPartner(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            doMigrateEas(eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null).collect(Collectors.toList()), "appendLeadMarketingPartner", ea -> {
                officialWebsiteManager.appendLeadMarketingPartner(ea);
                return null;
            });
        }
        return null;
    }

    /**
     * @param arg initAll=false时，若eas不为空，则初始化eas
     *            initAll=true时，则初始化全部企业
     * @return
     */
    @Override
    public Result updateMarketingObjStatus(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "updateMarketingObjStatus",
                            ea -> {
                                officialWebsiteManager.updateMarketingObjStatus(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result updateExternalObjUnionIdField(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            doMigrateEas(eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null).collect(Collectors.toList()), "updateMarketingObjStatus", ea -> {
                officialWebsiteManager.updateMarketingObjStatus(ea);
                return null;
            });
        }
        return null;
    }

    /**
     * @param arg initAll=false时，若eas不为空，则初始化eas
     *            initAll=true时，则初始化全部企业
     * @return
     */
    @Override
    public Result appendCampaignMembersLiveData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "appendCampaignMembersLiveData",
                            ea -> {
                                campaignMergeDataManager.appendCampaignMembersLiveData(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    /**
     * @param arg initAll=false时，若eas不为空，则初始化eas
     *            initAll=true时，则初始化全部企业
     * @return
     */
    @Override
    public Result appendCampaignMembersPayData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "appendCampaignMembersLiveData",
                            ea -> {
                                campaignMergeDataManager.appendCampaignMembersPayData(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result fixCrmObjectData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> (arg.getOtherParams()!=null && arg.getOtherParams().get("noFilter")!=null && "Y".equals(arg.getOtherParams().get("noFilter")))||(!marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null))
                                    .collect(Collectors.toList()),
                            "fixCrmObjectData",
                            ea -> {
                                paasDataManager.fixCrmObjectData(ea, arg.getOtherParams().get("describeApiName"), arg.getOtherParams().get("storeTableName"));
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result initWechatFriendsRecordObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "initWechatFriendsRecordObj",
                            ea -> {
                                wechatFriendsRecordObjDescribeManager.getOrCreateWechatFriendsRecordObjDescribe(ea);
                                userRoleManager.initWechatFriendsRecordObjPrivilege(ea);
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result appendCampaignMembersSpreadData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .filter(ea -> qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null)
                                    .collect(Collectors.toList()),
                            "appendCampaignMembersSpreadData",
                            ea -> {
                                campaignMergeDataManager.appendCampaignMembersSpreadData(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result syncExternalcontactData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .filter(ea -> qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null)
                                    .filter(ea-> arg.getExcludes() == null || !arg.getExcludes().contains(ea))
                                    .collect(Collectors.toList()),
                            "syncExternalcontactData",
                            ea -> {
                                ObjectDescribe objectDescribe = wechatFriendsRecordObjDescribeManager.getOrCreateWechatFriendsRecordObjDescribe(ea);
                                if (objectDescribe != null) {
                                    wechatFriendsRecordObjDescribeManager.syncExternalcontactData(ea);
                                }
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result deleteWechatFriendsRecordObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .filter(ea -> qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null)
                                    .filter(ea-> arg.getExcludes() == null || !arg.getExcludes().contains(ea))
                                    .collect(Collectors.toList()),
                            "deleteWechatFriendsRecordObj",
                            ea -> {
                                paasDataManager.deleteWechatFriendsRecordObjByEa(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result refactorWechatFriendsRecord(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .filter(ea -> qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null)
                                    .filter(ea-> arg.getExcludes() == null || !arg.getExcludes().contains(ea))
                                    .collect(Collectors.toList()),
                            "refactorWechatFriendsRecord",
                            ea -> {
                                ObjectDescribe objectDescribe = wechatFriendsRecordObjDescribeManager.getOrCreateWechatFriendsRecordObjDescribe(ea);
                                if (objectDescribe != null) {
                                    paasDataManager.deleteWechatFriendsRecordObjByEa(ea);
                                    wechatFriendsRecordObjDescribeManager.syncExternalcontactData(ea);
                                }
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result addDefaultEventTypeOptionsToFieldDescribeByEas(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }

        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .filter(ea -> arg.getExcludes() == null || !arg.getExcludes().contains(ea))
                                    .collect(Collectors.toList()),
                            "addDefaultEventTypeOptionsToFieldDescribeByEas",
                            ea -> {
                                try {
                                    String oneResult = marketingEventRemoteManager.addDefaultEventTypeOptionsToFieldDescribe(eieaConverter.enterpriseAccountToId(ea), -10000);
                                } catch (Exception e) {
                                    log.info("addDefaultEventTypeOptionsToFieldDescribeByEas-result: error", e);
                                }
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result add4Obj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "add4Obj",
                            ea -> {
                                if (qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null) {
                                    wechatAccountGroupStatisticsObjManager.getOrCreateObjDescribe(ea);
                                    userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ.getName());
                                    wechatAccountStatisticsObjManager.getOrCreateObjDescribe(ea);
                                    userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.WECHAT_ACCOUNT_STATISTICS_OBJ.getName());
                                }
                                contentPropagationDetailObjManager.getOrCreateObjDescribe(ea);
                                userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());
                                employeePromoteDetailObjManager.getOrCreateObjDescribe(ea);
                                userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());

                                contentPropagationDetailObjManager.tryUpdateCustomFieldLabel(ea);
                                employeePromoteDetailObjManager.tryUpdateCustomFieldLabel(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result updateScrmObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "updateScrmObj",
                            ea -> {
                                wechatFriendsRecordObjDescribeManager.tryUpdateCustomFieldLabel(ea);
                                wechatFriendsRecordObjDescribeManager.tryUpdateTenantScene(ea);
                                wechatGroupObjDescribeManager.tryUpdateCustomFieldLabel(ea);
                                wechatGroupUserObjDescribeManager.tryUpdateCustomFieldLabel(ea);
                                wechatWorkExternalUserObjDescribeManager.tryUpdateCustomFieldLabel(ea);

                                otherObjectDescribeManager.tryAddExternalUserId(ea, CrmObjectApiNameEnum.CRM_LEAD.getName());
                                otherObjectDescribeManager.tryAddExternalUserId(ea, CrmObjectApiNameEnum.CUSTOMER.getName());
                                otherObjectDescribeManager.tryAddExternalUserId(ea, CrmObjectApiNameEnum.CONTACT.getName());

//                                otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
//                                otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
//                                otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
//                                otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName());
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result tryDeleteDescribeCustomField(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
//                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "tryDeleteDescribeCustomField",
                            ea -> {
                                otherObjectDescribeManager.tryDeleteDescribeCustomField(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), CrmObjectApiNameEnum.CRM_LEAD.getName(), "external_user_id");
                                otherObjectDescribeManager.tryDeleteDescribeCustomField(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), CrmObjectApiNameEnum.CUSTOMER.getName(), "external_user_id");
                                otherObjectDescribeManager.tryDeleteDescribeCustomField(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), CrmObjectApiNameEnum.CONTACT.getName(), "external_user_id");
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result outAccountToFsAccountBatchByEas(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }

        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .filter(ea -> arg.getExcludes() == null || !arg.getExcludes().contains(ea))
                                    .collect(Collectors.toList()),
                            "outAccountToFsAccountBatchByEas",
                            ea -> {
                                try {
                                    qyweixinAccountBindManager.outAccountToFsAccountBatchByEas(ea, qywxCrmAppid);
                                    spreadTaskManager.updateSpreadTaskUserIdById(ea);
                                    qyweixinAccountBindManager.updateMarketingActivityEmployeeStatisticEntityUserIdById(ea);
                                    qyweixinAccountBindManager.updateFsBindUserIdById(ea);
                                    qyweixinAccountBindManager.updateMarketingActivityEmployeeDayEntityUserIdById(ea);
                                } catch (Exception e) {
                                    log.info("outAccountToFsAccountBatchByEas-result: error", e);
                                }
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result appendWechatFriendsRecordData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "appendWechatFriendsRecordData",
                            ea -> {
                                wechatFriendsRecordObjDescribeManager.appendWechatFriendsRecordData(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return null;
    }

    @Override
    public Result updateMarketingBehaviorDescribe(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "updateMarketingBehaviorDescribe",
                            ea -> {
                                try {
                                    HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
                                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(systemHeader, "MarketingBehaviorObj");
                                    if(oldDescribeResult.isSuccess() && oldDescribeResult.getData() != null && oldDescribeResult.getData().getDescribe() != null){
                                        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
                                        Map<String, FieldDescribe> fieldMap = describe.getFields();
                                        if (fieldMap.get("account_id") != null){
                                            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                                            addDescribeCustomFieldArg.setDescribeAPIName("MarketingBehaviorObj");
                                            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\":\"MarketingBehaviorObj\",\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"客户\",\"target_api_name\":\"AccountObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"target_related_list_gFJa0__c\",\"target_related_list_label\":\"营销动态\",\"action_on_target_delete\":\"set_null\",\"is_required\":false,\"wheres\":[],\"api_name\":\"account_id\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"target_related_list_label\":0,\"wheres\":0,\"api_name\":0,\"is_unique\":0,\"label\":0,\"target_api_name\":0,\"target_related_list_name\":0,\"help_text\":0}},\"help_text\":\"\",\"status\":\"new\"}");
                                            objectDescribeCrmService.updateCustomFieldDescribe(systemHeader, addDescribeCustomFieldArg);
                                        }
                                        if (fieldMap.get("contact_id") != null){
                                            AddDescribeCustomFieldArg addDescribeCustomFieldArg = new AddDescribeCustomFieldArg();
                                            addDescribeCustomFieldArg.setDescribeAPIName("MarketingBehaviorObj");
                                            addDescribeCustomFieldArg.setFieldDescribe("{\"describe_api_name\":\"MarketingBehaviorObj\",\"is_index\":true,\"is_active\":true,\"is_unique\":false,\"label\":\"联系人\",\"target_api_name\":\"ContactObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"target_related_list_gFJa0__c\",\"target_related_list_label\":\"营销动态\",\"action_on_target_delete\":\"set_null\",\"is_required\":false,\"wheres\":[],\"api_name\":\"contact_id\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"target_related_list_label\":0,\"wheres\":0,\"api_name\":0,\"is_unique\":0,\"label\":0,\"target_api_name\":0,\"target_related_list_name\":0,\"help_text\":0}},\"help_text\":\"\",\"status\":\"new\"}");
                                            objectDescribeCrmService.updateCustomFieldDescribe(systemHeader, addDescribeCustomFieldArg);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.warn("updateMarketingBehaviorDescribe failed: ",e);
                                }
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return null;
    }

    @Override
    public Result initAdDataReturnDetailObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "initAdDataReturnDetailObj",
                            ea -> {
                                adDataReturnBackObjDescribeManager.getOrCreateAdSendObjDescribe(ea);
                                adOCPCUploadManager.initRule(ea);
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result initMktContentMgmtLogObjManager(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "initMktContentMgmtLogObjManager",
                            ea -> {
                                mktContentMgmtLogObjManager.getOrCreateObjDescribe(ea);
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result refreshAdCampaignData(String ea, String adAccountId, String source, int day) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
        campaignDataManager.refreshCampaignData(adAccountEntity, day);
        return Result.newSuccess();
    }

    @Override
    public Result syncMarketingTermServingLinesDataByKeyword(String accountId, int day) {
        ThreadPoolUtils.execute(() -> {
            AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(accountId);
            for (int i = day; i >= 0; i--) {
                try {
                    Date date = DateUtil.minusDay(new Date(), i);
                    String formatDate = DateUtil.format2(date);
                    log.info("关键词投放明细 开始刷取， date: {}", formatDate);
                    long beginTime = System.currentTimeMillis();
                    refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, adAccountEntity.getSource(), formatDate);
                    log.info("关键词投放明细 开始刷取， date: {} 耗时： {}ms", formatDate, System.currentTimeMillis() - beginTime);
                } catch (Exception e) {
                    log.error("关键词投放明细, 出错 ", e);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncMarketingTermServingLinesDataByKeywordByDate(String accountId, String date) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(accountId);
        if (adAccountEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ThreadPoolUtils.execute(() -> {
            log.info("关键词投放明细 开始刷取， date: {}", date);
            long beginTime = System.currentTimeMillis();
            refreshDataManager.syncMarketingTermServingLinesDataByKeyword(adAccountEntity, adAccountEntity.getSource(), date);
            log.info("关键词投放明细 开始刷取， date: {} 耗时： {}ms", date, System.currentTimeMillis() - beginTime);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result initSmsSendRecordObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = mwSmsSignatureDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas, "initSmsSendRecordObj",
                            ea -> {
                                smsSendRecordObjManager.getOrCreateObjDescribe(ea);
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result initQywxMomentTaskStatisticData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = qywxMomentTaskDAO.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas, "initQywxMomentTaskStatisticData",
                            ea -> {
                                momentManager.updateMomentStatisticData(ea);
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result updateMemberObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                            doMigrateEas(
                                    eas.stream()
                                            .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                            .collect(Collectors.toList()),
                                    "updateMemberObj",
                                    ea -> {
                                        memberDescribeManager.tryUpdateCustomFieldLabel(ea);
                                        return null;
                                    }
                            ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result syncUserAccountRelationWxData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "syncUserAccountRelationWxData",
                            ea -> {
                                wechatWorkExternalUserObjManager.syncUserAccountRelationWxData(ea);
                                return Result.newSuccess();
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return Result.newSuccess();
    }

    @Override
    public Result refreshAllAdAccountData(RefreshAllAdAccountData arg) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(arg.getAccountId());
        if (adAccountEntity == null) {
            return Result.newError(-1, "账号不存在");
        }
        ThreadPoolUtils.execute(() -> {
            String value = AdSourceEnum.getValueBySource(adAccountEntity.getSource());
            AdMarketingManager adMarketingManager = adMarketingHandlerActionManager.getAdMarketingActionManager(value);
            adMarketingManager.refreshAllData(arg.getEa(), adAccountEntity.getId(), adAccountEntity.getSource());
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result updateQywxObjectOwnerAsBindFsUser(MigrateEasArg arg) {
        List<String> eas = arg.getEas();
        if (CollectionUtils.isEmpty(eas)){
            return Result.newSuccess();
        }
        ThreadPoolUtils.execute(() ->
                doMigrateEas(
                        eas, "updateQywxObjectOwnerAsBindFsUser",
                        ea -> {
                            qywxUserService.updateQywxObjectOwnerAsBindFsUser(ea);
                            return Result.newSuccess();
                        }
                ), ThreadPoolTypeEnums.HEAVY_BUSINESS
        );
        return Result.newSuccess();
    }

    @Override
    public Result updateMarketingSpreadDataAsBindFsUser(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newSuccess();
        }
        if (CollectionUtils.isEmpty(arg.ids)) {
            return Result.newSuccess();
        }
        String ea = arg.getEas().get(0);
        ThreadPoolUtils.execute((() -> {
            for (int i = 0 ; i < arg.getIds().size(); i++){
                String[] regs = arg.getIds().get(i).split(":");
                int virtualUserId = Integer.parseInt(regs[0]);
                int crmUserId = Integer.parseInt(regs[1]);
                String qywxUserId = regs[2];
                qywxUserManager.qywxUserBindFsUserFromVirtualUser(ea, virtualUserId, crmUserId, qywxUserId);
            }
        }), ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result initQywxActivatedAccount(MigrateEasArg arg) {
        qywxStaffService.initQywxActivatedAccount(arg.getEas());
        return Result.newSuccess();
    }

    @Override
    public Result cleanDuplicateData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = qywxMomentTaskDAO.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() -> doMigrateEas(eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null).collect(Collectors.toList()), "cleanDuplicateData", ea -> {
                try {
                    momentManager.cleanDuplicateData(ea);
                } catch (Exception e) {
                    log.info("cleanDuplicateData-result: error", e);
                }
                return Result.newSuccess();
            }), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result updateCampaignMembersObjSpreadData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "updateCampaignMembersObjSpreadData",
                            ea -> {
                                campaignMergeDataManager.updateCampaignMembersObjSpreadData(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result updateMemberObjData(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream()
                                    .filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "updateMemberObjData",
                            ea -> {
                                memberDescribeManager.updateMemberObjFiled(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS
            );
        }
        return null;
    }

    @Override
    public Result invoke(MigrateEasArg arg) {
        if (StringUtils.isNullOrEmpty(arg.getBeanName()) || StringUtils.isNullOrEmpty(arg.getMethodName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> eas;
        if (BooleanUtils.isTrue(arg.getInitGrayEa())) {
            Set<String> grayEaSet = Arrays.stream(marketingGrayList.split(",")).map(e -> e.replace("\"", "")).collect(Collectors.toSet());
            List<String> allEas = enterpriseMetaConfigDao.findEaAll();
            eas = allEas.stream().filter(grayEaSet::contains).collect(Collectors.toList());
        } else if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else if (BooleanUtils.isFalse(arg.getContainTestEa())) {
            // 不包含测试企业和沙盒企业
            eas = enterpriseMetaConfigDao.findEaWithoutTest();
        }  else if (BooleanUtils.isTrue(arg.getOnlyContainTestEa())) {
            // 不包含测试企业和沙盒企业
            eas = enterpriseMetaConfigDao.findTestEa();
        } else {
            // 包含测试企业和沙盒企业
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() -> {
                String taskName = arg.getBeanName() + "." + arg.getMethodName();
                this.doMigrateEas(
                        eas.stream()
                                .filter(ea -> (arg.getOtherParams()!=null && arg.getOtherParams().get("noFilter")!=null && "Y".equals(arg.getOtherParams().get("noFilter")))||(!marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null))
                                .filter(ea -> arg.getExcludes() == null || !arg.getExcludes().contains(ea))
                                .collect(Collectors.toList()),
                        taskName,
                        ea -> {
                            try {
                                Object bean = SpringContextUtil.getBean(arg.getBeanName());
                                Class clazz = bean.getClass();
                                Method method = clazz.getDeclaredMethod(arg.getMethodName(), String.class);
                                method.invoke(bean, ea);
                            } catch (Exception e) {
                                log.warn("invoke error taskName:{} ea:{}", taskName, ea, e);
                                return "error";
                            }
                            return "success";
                        }
                );
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> refreshCards(MigrateEasArg arg) {
        List<String> ids = arg.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            // 如果不传id，则按ea去更新，建议这里每次传入一个ea，避免不必要的更新
            List<String> eas = arg.getEas();
            if (CollectionUtils.isEmpty(eas)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            for (String ea : eas) {
                cardManager.updateAllCard(ea, 1);
            }
        } else {
            // 如果传了id，则根据id去更新
            for (String id : ids) {
                cardManager.correctCard(id, 1);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addTeamMemberByQywxBindCrmEvent(MigrateEasArg arg) {
        if (MapUtils.isEmpty(arg.getOtherParams())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getOtherParams().get("ea");
        String qywxUserId = arg.getOtherParams().get("qywxUserId");
        String fsUserId = arg.getOtherParams().get("fsUserId");
        if (StringUtils.isNullOrEmpty(ea) || StringUtils.isNullOrEmpty(qywxUserId) || StringUtils.isNullOrEmpty(fsUserId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        wechatGroupUserObjDescribeManager.addTeamMemberByQywxBindCrmEvent(ea, qywxUserId, Integer.parseInt(fsUserId));
        return Result.newSuccess();
    }

    @Override
    public Result initWechatGroupTeamMember(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            List<QywxCorpAgentConfigEntity> entityList = qywxCorpAgentConfigDAO.queryAllQywxCorp();
            if (CollectionUtils.isEmpty(entityList)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            eas = entityList.stream().map(QywxCorpAgentConfigEntity::getEa).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "initWechatGroupTeamMember",
                            ea -> {
                                wechatGroupObjDescribeManager.initTeamMember(Lists.newArrayList(ea));
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();

    }

    @Override
    public Result refreshWechatExternalUserAddSource(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getEas())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        new Thread(() -> {
            for (String ea : arg.getEas()) {
                wechatWorkExternalUserObjManager.refreshAddSource(ea);
            }
        }).start();
        return Result.newSuccess();
    }

    @Override
    public Result<Void> mergeUserMarketingActionStatistic(MigrateEasArg arg) {
        if (CollectionUtils.isEmpty(arg.getEas()) || CollectionUtils.isEmpty(arg.getIds())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        new Thread(() -> {
           String ea = arg.getEas().get(0);
            for (String id : arg.getIds()) {
                String[] arr = id.split("_");
                qywxUserManager.mergerUserMarketingActionStatistics(ea, Integer.parseInt(arr[0]), Integer.parseInt(arr[1]));
            }
        }).start();
        return Result.newSuccess();
    }

    @Override
    public Result initAdvertisingDetailsObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = Lists.newArrayList();
            List<String> adEas = adAccountManager.findAllEa(true);
            eas.addAll(adEas);
            for (Integer pluginType : MarketingPluginTypeEnum.getAdPluginTypeList()) {
               List<String> pluginEas  = marketingPluginConfigDAO.queryEaByPluginType(pluginType);
               if (CollectionUtils.isNotEmpty(pluginEas)) {
                   eas.addAll(pluginEas);
               }
            }

            if (CollectionUtils.isEmpty(eas)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "initAdvertisingDetailsObj",
                            ea -> {
                                advertisingDetailsObjManager.getOrCreateObjDescribe(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result initLeadJoinCompensateStatusField(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = marketingPluginConfigDAO.queryEaByPluginType(MarketingPluginTypeEnum.AD_OCPC.getType());
            if (CollectionUtils.isEmpty(eas)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "initLeadJoinCompensateStatusField",
                            ea -> {
                                otherObjectDescribeManager.tryAddLeadJoinCompensateStatusField(ea);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteIdempotentRecordData(int day) {
        ThreadPoolUtils.execute(() -> idempotentRecordManager.clearData(day), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    public Result<Void> updateWxExternalUserObjSceneAndHideButton(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            List<QywxCorpAgentConfigEntity> configEntities = qywxCorpAgentConfigDAO.queryAllQywxCorp();
            if (CollectionUtils.isEmpty(configEntities)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            eas = configEntities.stream().map(QywxCorpAgentConfigEntity::getEa).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas.stream().filter(ea -> !marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null)
                                    .collect(Collectors.toList()),
                            "updateWechatWorkExternalUserObjScene",
                            ea -> {
                                wechatWorkExternalUserObjDescribeManager.tryUpdateScene(ea);
                                wechatWorkExternalUserObjDescribeManager.tryHideAddImportFormButton(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public void registerMarketingLiveOldMaterial() {
        ThreadPoolUtils.execute(() ->{
            liveManager.registerMarketingLiveOldMaterial();
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    @Override
    public Result<Void> reindexAdvertisingDetailsObj(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else {
            eas = adAccountManager.findAllEa(true);
            if (CollectionUtils.isEmpty(eas)) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
        }
        ThreadPoolUtils.execute(() -> {
            Map<String, String> otherParams = arg.getOtherParams();
            String reindexBaidu = otherParams.get("baidu");
            String reindexTencent = otherParams.get("tencent");
            String reindexToutiao = otherParams.get("toutiao");
            for (String ea : eas) {
                if (!marketingActivityRemoteManager.enterpriseStop(ea) && appVersionManager.getCurrentAppVersion(ea) != null) {
                    long t1 = System.currentTimeMillis();
                    if (Boolean.parseBoolean(reindexBaidu)) {
                        baiduAdMarketingManager.reindexAdvertisingDetailsObj(ea);
                    }
                    long t2 = System.currentTimeMillis();
                    if (Boolean.parseBoolean(reindexTencent)) {
                        tencentAdMarketingManager.reindexAdvertisingDetailsObj(ea);
                    }
                    long t3 = System.currentTimeMillis();
                    if (Boolean.parseBoolean(reindexToutiao)) {
                        headlinesAdMarketingManager.reindexAdvertisingDetailsObj(ea);
                    }
                    long t4 = System.currentTimeMillis();
                    log.info("刷新广告回传明细耗时,百度: {} 腾讯: {} 头条: {}", t2 - t1, t3 - t2, t4 - t3);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return null;
    }

    @Override
    public Result<Void> generateThirdPlatformSecret(MigrateEasArg arg) {
        Map<String, String> otherParams = arg.getOtherParams();
        if (otherParams == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = otherParams.get("ea");
        if (StringUtils.isNullOrEmpty(ea)) {
            return Result.newError(-1, "ea不存在");
        }
        String platform = otherParams.get("platform");
        if (StringUtils.isNullOrEmpty(platform)) {
            return Result.newError(-1, "platform不存在");
        }
        ThirdPlatformEnum thirdPlatformEnum = ThirdPlatformEnum.getByPlatform(platform);
        if (thirdPlatformEnum == null) {
            return Result.newError(-1, "platform值不对");
        }
        ThirdPlatformSecretEntity entity = new ThirdPlatformSecretEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setSecret(UUIDUtil.getUUID());
        entity.setToken(UUIDUtil.getUUID());
        entity.setPlatform(thirdPlatformEnum.getPlatform());
        thirdPlatformSecretManager.batchInsert(Lists.newArrayList(entity));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> createEmployeeCard(MigrateEasArg arg) {
        String uid = arg.getIds().get(0);
        String ea = arg.getEas().get(0);
        if (StringUtils.isNullOrEmpty(uid) || StringUtils.isNullOrEmpty(ea)){
            return Result.newSuccess();
        }
        accountManager.createCardQRCode(uid, ea);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initMarketingLiveChannelAccount() {
        ThreadPoolUtils.execute(() ->{
            liveManager.initMarketingLiveChannelAccount();
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    /**
     * 添加ea到sfa标签key值
     * @param eas
     */
    @Override
    public void addMarketingTagEa(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)) {
            return;
        }
        for (String ea : eas) {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            User user = new User(String.valueOf(ei), "-10000");
            String key = "open_marketing_tag_objects";
            String tenantConfig = configService.findTenantConfig(user, key);
            String objs = JSON.toJSONString(Lists.newArrayList("LeadsObj", "AccountObj", "ContactObj", "WechatFanObj", "MemberObj", "WechatWorkExternalUserObj", "WechatFriendsRecordObj"));
            if (StringUtils.isNullOrEmpty(tenantConfig)) {
                configService.createTenantConfig(user, key, objs, ConfigValueType.JSON);
            } else {
                configService.updateTenantConfig(user, key, objs, ConfigValueType.JSON);
            }
        }
    }

    @Override
    public Result<Void> pullQywxDataByEnterpriseUserIds(MigrateEasArg arg) {
        ThreadPoolUtils.execute(() ->
                wechatWorkExternalUserObjManager.pullQywxDataByEnterpriseUserIds(arg.getEas().get(0), arg.getIds()),
                ThreadPoolTypeEnums.MEDIUM_BUSINESS
        );
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateMiniappDomain(MigrateEasArg arg) {
       List<String> appIds = arg.getIds();
       String platformId = arg.getEas().get(0);
       if (CollectionUtils.isEmpty(appIds)) {
           return Result.newSuccess();
       }
       Set<String> appIdSet = new HashSet<>(appIds);
       ThreadPoolUtils.execute(() ->
           settingManager.updateMiniappDomain(platformId, appIdSet), ThreadPoolTypeEnums.MEDIUM_BUSINESS);

       return Result.newSuccess();
    }

    @Override
    public Result<Void> enablePublicData(MigrateEasArg arg) {
        List<String> eas = arg.getEas();
        String upStreamTenantId = arg.getOtherParams().get("upStreamTenantId");
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas,
                            "enablePublicData",
                            ea -> {
                                publicMetadataManager.enablePublic(ea,upStreamTenantId);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> batchPurgeData(MigrateEasArg arg) {
        List<String> eas = arg.getEas();
        String apiName = arg.getOtherParams().get("describeApiName");
        String[] split = apiName.split(",");
        ArrayList<String> describeApiNames = Lists.newArrayList(split);
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas,
                            "batchPurgeData",
                            ea -> {
                                publicMetadataManager.batchPurge(ea,describeApiNames);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteCouponDistributionObj(MigrateEasArg arg) {
        List<String> eas = arg.getEas();
        String objectId = arg.getOtherParams().get("objectId");
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas,
                            "deleteCouponDistributionObj",
                            ea -> {
                                couponDistributionObjManager.deleteCouponDistributionObj(ea,objectId);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateCouponMaxCoupons(MigrateEasArg arg) {
        List<String> eas = arg.getEas();
        String couponId = arg.getOtherParams().get("couponId");
        String couponMaxCouponsStr = arg.getOtherParams().get("maxCoupons");
        Integer maxCoupons = Integer.parseInt(couponMaxCouponsStr);
        if (CollectionUtils.isNotEmpty(eas)) {
            ThreadPoolUtils.execute(() ->
                    doMigrateEas(
                            eas,
                            "updateCouponMaxCoupons",
                            ea -> {
                                couponObjDescribeManager.updateCouponMaxCoupons(ea,couponId,maxCoupons);
                                return null;
                            }
                    ), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteSessionKey(SessionUidArg arg) {
        if (CollectionUtils.isNotEmpty(arg.getUidList())) {
            for (String uid : arg.getUidList()) {
                redisManager.delSessionByUid(uid);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncQywxBindInfo(MigrateEasArg arg) {
        if (!arg.getInitAll()){
            return Result.newSuccess();
        }
        List<QywxCustomerAppInfoEntity> appInfoEntityList = qywxCustomerAppInfoDAO.selectAll();
        if (CollectionUtils.isEmpty(appInfoEntityList)) {
            return Result.newSuccess();
        }

        List<AyncQywxBindInfoArg.BindInfo> bindInfoList = Lists.newArrayList();
        for (QywxCustomerAppInfoEntity appInfoEntity : appInfoEntityList) {
            AyncQywxBindInfoArg.BindInfo bindInfo  = new AyncQywxBindInfoArg.BindInfo();
            bindInfo.setFsEa(appInfoEntity.getEa());
            bindInfo.setOutEa(appInfoEntity.getCorpId());
            bindInfo.setStatus("0");
            bindInfo.setBusinessType("QYWX_YXT");
            bindInfo.setAppId(appInfoEntity.getSuitId());
            bindInfoList.add(bindInfo);
        }

        AyncQywxBindInfoArg bindInfoArg = new AyncQywxBindInfoArg();
        com.facishare.marketing.common.util.PageUtil pageUtil = new com.facishare.marketing.common.util.PageUtil(bindInfoList, 100);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<AyncQywxBindInfoArg.BindInfo> bindInfos = pageUtil.getPagedList(i);
            bindInfoArg.setBindInfos(bindInfos);
            qyweixinAccountBindManager.syncQywxBindInfo(bindInfoArg);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> createSystemMenuTemplate(MigrateEasArg arg) {
        List<String> eas;
        if (!arg.getInitAll()) {
            eas = arg.getEas();
        } else if (BooleanUtils.isFalse(arg.getContainTestEa())) {
            // 不包含测试企业和沙盒企业
            eas = enterpriseMetaConfigDao.findEaWithoutTest();
        }  else if (BooleanUtils.isTrue(arg.getOnlyContainTestEa())) {
            // 不包含测试企业和沙盒企业
            eas = enterpriseMetaConfigDao.findTestEa();
        } else {
            // 包含测试企业和沙盒企业
            eas = enterpriseMetaConfigDao.findEaAll();
        }
        String type = arg.getOtherParams() == null ? null : arg.getOtherParams().get("type");
        if (CollectionUtils.isEmpty(eas) || StringUtils.isNullOrEmpty(type)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        eas.forEach(e -> ThreadPoolUtils.execute(() -> appMenuTemplateService.createSystemTemplate(e, type), ThreadPoolTypeEnums.HEAVY_BUSINESS));
        return Result.newSuccess();
    }

    @Override
    public Result<Void> queryAccountObjectExist(MigrateEasArg arg) {
        if (MapUtils.isEmpty(arg.getOtherParams())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getOtherParams().get("ea");
        String value = arg.getOtherParams().get("value");
        String accountId = arg.getOtherParams().get("accountId");
        if (StringUtils.isNullOrEmpty(ea) || StringUtils.isNullOrEmpty(value) || StringUtils.isNullOrEmpty(accountId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> condition = publicCouponManager.conformConditionAccount(ea, "CONDITION", value, VisibilityEnum.RECEIVE_SCOPE.getType(), Lists.newArrayList(accountId));
        log.info("queryAccountObjectExist, ea:{}, value:{}, accountId:{}, result:{}", ea, value, accountId, condition);
        return Result.newSuccess();
    }

    @Override
    /**
     * 解除营销用户关联关系
     * 参数eas:操作企业的ea， 取eas[0]
     * 参数otherParams： key:[userMarketingId, CrmObjectApiNameEnum定义的apiname], value:[营销用户id， 对象id]
     */
    public Result<Void> unbindMarketingUserBindInfo(MigrateEasArg arg){
        if (MapUtils.isEmpty(arg.getOtherParams())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEas().get(0);
        Map<String, String> otherParams = arg.getOtherParams();
        userMarketingAccountManager.unbindMarketingUserBindInfo(ea, otherParams);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> fixCampaignMergeData(FixCampaignMergeDataArg arg) {
        String ea = arg.getEa();
        List<String> enrollIds = arg.getEnrollIds();
        String marketingEventId = arg.getMarketingEventId();
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(enrollIds)) {
            List<CustomizeFormDataUserEntity> entities = customizeFormDataUserDAO.queryCustomizeFormDataUserEntityByIds(enrollIds);
            customizeFormDataUserEntities.addAll(entities);
        } else {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(marketingEventId)) {
                // 根据市场活动id，查询报名数据表有记录但是活动成员本地表没有的数据
                List<CustomizeFormDataUserEntity> entities = customizeFormDataUserDAO.queryFailedCampaign(ea, marketingEventId);
                customizeFormDataUserEntities.addAll(entities);
            }
        }

        if (CollectionUtils.isEmpty(customizeFormDataUserEntities)) {
            return Result.newSuccess();
        }
        customizeFormDataUserEntities.forEach(customizeFormDataUserEntity -> {
            try {
                customizeFormDataService.handCampaignDataInner(ea, customizeFormDataUserEntity.getId(), null, null, customizeFormDataUserEntity, null, null, null, false, null);
            } catch (Exception e) {
                log.error("fixCampaignMergeData error, ea:{}, enrollId:{}, marketingEventId:{}", ea, customizeFormDataUserEntity.getId(), marketingEventId, e);
            }
        });
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteMiniAppReleaseRecord(DeleteMiniAppReleaseRecordArg arg) {
        settingManager.deleteMiniAppReleaseRecord(arg.getId());
        return Result.newSuccess();
    }
}


