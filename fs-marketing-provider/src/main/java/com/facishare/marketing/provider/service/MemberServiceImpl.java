/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.service.cta.CtaService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.*;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.qywx.ObjectInfoArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.member.BuildMemberInfoByFormResult;
import com.facishare.marketing.api.result.member.GetMemberMarketingEventCrmConfigResult;
import com.facishare.marketing.api.result.member.MemberEnrollResult;
import com.facishare.marketing.api.result.memberCenter.MiniAppInfoConfigResult;
import com.facishare.marketing.api.result.memberCenter.QueryMemberContentResult;
import com.facishare.marketing.api.result.memberCenter.QueryMemberInfoResult;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.StatisticService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.vo.MemberAccessibleWebsiteVo;
import com.facishare.marketing.api.vo.MemberConfigVO;
import com.facishare.marketing.api.vo.member.GetMemberMarketingEventCrmConfigVO;
import com.facishare.marketing.api.vo.member.UpsertMemberMarketingEventCrmConfigVO;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.dto.IdName;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.TokenUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivitySpreadRecordDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.member.MemberMarketingEventCrmConfigDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.dto.CampaignMarketingEventIdTimeDTO;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivitySpreadRecordEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.member.MemberMarketingEventCrmConfigEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.manager.MemberManager.SaveMemberToCampaignMergeDataResultContainer;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasAddMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryCrmObjectArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.ConfigUtil;
import com.facishare.marketing.provider.util.ConvertUtil;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.fxiaoke.crmrestapi.common.contants.MemberFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.MemberStatusResult;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;

import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: dzb
 * @Date: 2018/12/19
 * @Description:
 */
@Slf4j
@Service("memberService")
public class MemberServiceImpl implements MemberService {
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MemberAccessibleObjectDao memberAccessibleObjectDao;
    @Autowired
    private MemberAccessibleWebsiteDao memberAccessibleWebsiteDao;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private MemberConfigDao memberConfigDao;
    @Autowired
    private WxMiniAppUserMemberBindDao wxMiniAppUserMemberBindDao;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private ObjectTagDAO objectTagDAO;
    @Autowired
    private ActionManager actionManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private MemberMarketingEventCrmConfigDAO memberObjectCrmConfigDAO;
    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private SafetyManagementManager safetyManagementManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private StaffMiniappUserBindManager staffMiniappUserBindManager;
    @Autowired
    private ObjectEnrollJumpSettingDAO objectEnrollJumpSettingDAO;
    @Autowired
    private ActivityEnrollTimeConfigDAO activityEnrollTimeConfigDAO;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;

    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;

    @Autowired
    private MarketingActivitySpreadRecordDAO marketingActivitySpreadRecordDAO;

    @Autowired
    private StatisticService statisticService;

    @Autowired
    private HexagonManager hexagonManager;

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private MemberEnrollMarketingEventDisplayTypeConfigDAO memberEnrollMarketingEventDisplayTypeConfigDAO;
    @Autowired
    private AuthPartnerManager authPartnerManager;
    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    @Autowired
    private CtaService ctaService;

    @Override
    public Result<CrmMemberResult> isOpenMember(String ea, Integer fsUserId) {
        String grayEa = ConfigUtil.grayEa;
        boolean isOpen = ConfigUtil.isOpen;
        if (isOpen) {
            List<String> grayEaList = GsonUtil.getGson().fromJson(grayEa, ArrayList.class);
            //查找企业EI
            int tenantId = eIEAConverter.enterpriseAccountToId(ea);
            if (!grayEaList.contains(String.valueOf(tenantId))) {
                CrmMemberResult crmMemberResult = new CrmMemberResult();
                crmMemberResult.setEnableStatus(SHErrorCode.GRAY_ENTERPRISE.getErrorCode());
                crmMemberResult.setMessage(SHErrorCode.GRAY_ENTERPRISE.getErrorMessage());
                return new Result<>(SHErrorCode.SUCCESS, crmMemberResult);
            }
        }
        MemberStatusResult memberStatusResult = memberManager.isOpenMember(ea, fsUserId);
        CrmMemberResult result = new CrmMemberResult();
        result.setEnableStatus(memberStatusResult.getEnableStatus());
        result.setMessage(memberStatusResult.getMessage());
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PageResult<MemberResult>> listMember(String ea, Integer fsUserId, ListMemberArg arg) {
        ControllerListArg controllerListArg = new ControllerListArg();
        if (arg.getFilterData() == null){
            FilterData filterData = new FilterData();
            filterData.setObjectAPIName(CrmObjectApiNameEnum.MEMBER.getName());
            arg.setFilterData(filterData);
        }
        if (arg.getFilterData().getQuery() == null){
            com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery searchTemplateQuery = new com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery();
            arg.getFilterData().setQuery(searchTemplateQuery);
        }
        String operator = arg.getFilterData().getQuery().getTagOperator();
        List<TagName> tagNames = arg.getFilterData().getQuery().getTagNames();
        if (!CollectionUtils.isEmpty(tagNames) && !org.springframework.util.StringUtils.isEmpty(operator)) {
            Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, CrmObjectApiNameEnum.MEMBER.getName(), tagNames);
            if (CollectionUtils.isEmpty(tagNamesIdMap.values())) {
                return Result.newSuccess(PageResult.newPageResult(arg.getPageNum(), arg.getPageSize(), 0, new ArrayList<>(0)));
            }
            controllerListArg.setTagOperator(arg.getFilterData().getQuery().getTagOperator());
            controllerListArg.setTags(new ArrayList<>(tagNamesIdMap.values()));
        }
        controllerListArg.setSearchTemplateId(arg.getFilterData().getSearchTemplateId());
        controllerListArg.setObjectDescribeApiName(arg.getFilterData().getObjectAPIName());
        SearchQuery searchQuery = ConvertUtil.convert(arg.getFilterData().getQuery(), arg.getOffset(), arg.getLimit());
        if (searchQuery.getOrders() == null || searchQuery.getOrders().isEmpty()){
            searchQuery.setOrders(new ArrayList<>(1));
            searchQuery.addOrderBy("create_time", false);
        }
        controllerListArg.setSearchQuery(searchQuery);
        Page<ObjectData> objectDataPage = metadataControllerServiceManager.list(new HeaderObj(eIEAConverter.enterpriseAccountToId(ea), fsUserId), arg.getFilterData().getObjectAPIName(), controllerListArg);

        List<CrmUserDefineFieldVo> crmUserDefineFieldVos = crmV2Manager.getAllObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MEMBER);
        Map<String, CrmUserDefineFieldVo> fieldToNameMap = crmUserDefineFieldVos.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, v -> v, (v1, v2) -> v1));
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        List<MemberResult> results = objectDataPage.getDataList().stream().map(objectData -> {
            MemberResult memberResult = new MemberResult();
            memberResult.setId(objectData.getId());
            memberResult.setName(objectData.getName());
            memberResult.setCardNo(objectData.getString(CrmMemberFieldEnum.CARD_NO.getApiName()));
            memberResult.setPhone(objectData.getString(CrmMemberFieldEnum.PHONE.getApiName()));
            if (turnOnPhoneNumberSensitive) {
                memberResult.setPhone(safetyManagementManager.phoneNumberStrSensitive(memberResult.getPhone()));
            }
            memberResult.setEmail(objectData.getString(CrmMemberFieldEnum.EMAIL.getApiName()));
            if (fieldToNameMap.get(CrmMemberFieldEnum.GENDER.getApiName()) != null){
                memberResult.setGender(fieldToNameMap.get(CrmMemberFieldEnum.GENDER.getApiName()).getEnumLabel(objectData.getString(CrmMemberFieldEnum.GENDER.getApiName())));
            }
            if (fieldToNameMap.get(CrmMemberFieldEnum.ADD_SOURCE.getApiName()) != null){
                memberResult.setAddSource(fieldToNameMap.get(CrmMemberFieldEnum.ADD_SOURCE.getApiName()).getEnumLabel(objectData.getString(CrmMemberFieldEnum.ADD_SOURCE.getApiName())));
            }
            memberResult.setAvatar((List)objectData.get(CrmMemberFieldEnum.AVATAR.getApiName()));
            memberResult.setBirthday(objectData.getLong(CrmMemberFieldEnum.BIRTHDAY.getApiName()));
            memberResult.setCountry(objectData.getCountryName());
            memberResult.setProvince(objectData.getProvince());
            memberResult.setCity(objectData.getCity());
            memberResult.setDistrict(objectData.getDistrict());
            memberResult.setAddress(objectData.getString(CrmMemberFieldEnum.ADDRESS.getApiName()));
            memberResult.setGrowthValue(objectData.getString(CrmMemberFieldEnum.GROWTH_VALUE.getApiName()) == null ? "0" : objectData.getString(CrmMemberFieldEnum.GROWTH_VALUE.getApiName()));
            memberResult.setIntegralValue(objectData.getString(CrmMemberFieldEnum.INTEGRAL_VALUE.getApiName()) == null ? "0" : objectData.getString(CrmMemberFieldEnum.INTEGRAL_VALUE.getApiName()));
            memberResult.setCustomerId(objectData.getString(CrmMemberFieldEnum.CUSTOMER_ID.getApiName()));
            memberResult.setCustomerName(objectData.getString("customer_id__r"));
            memberResult.setGradeId(objectData.getString(CrmMemberFieldEnum.GRADE_ID.getApiName()));
            memberResult.setGradeName(objectData.getString("grade_id__r"));
            return memberResult;
        }).collect(Collectors.toList());
        if (!results.isEmpty()) {
            List<String> objectIds = results.stream().filter(Objects::nonNull).map(MemberResult::getId).collect(Collectors.toList());
            Map<String, ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataMap = metadataTagManager.getObjectDataIdAndTagNameListDataMapByObjectDataIds(ea, CrmObjectApiNameEnum.MEMBER.getName(), objectIds);
            for (MemberResult result : results) {
                ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData = objectDataIdAndTagNameListDataMap.get(result.getId());
                if (org.springframework.util.StringUtils.isEmpty(objectDataIdAndTagNameListData.getDataId())) {
                    continue;
                }
                result.setTagNameList(objectDataIdAndTagNameListData.getTagNameList());
            }
        }
        PageResult<MemberResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(objectDataPage.getTotal());
        pageResult.setResult(results);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<MemberDetailsResult> getMemberDetails(String ea, Integer fsUserId, String memberId) {
        MemberDetailsResult memberDetailsResult = new MemberDetailsResult();
        ObjectData objectData = crmV2Manager.getDetail(ea, fsUserId, MemberFieldContants.API_NAME, memberId);
        if (objectData != null) {
            MemberData crmMemberVo = MemberData.wrap(objectData);
            memberDetailsResult.setId(crmMemberVo.getId());
            memberDetailsResult.setName(crmMemberVo.getName());
            memberDetailsResult.setGradeName(crmMemberVo.getGradeIdName());
            memberDetailsResult.setCustomerName(crmMemberVo.getCustomerIdName());
            memberDetailsResult.setGrowthValue(crmMemberVo.getGrowthValue());
            memberDetailsResult.setIntegralValue(crmMemberVo.getIntegralValue());
            //获取详情
            if (StringUtils.isNotBlank(crmMemberVo.getCustomerId())) {
                PaasQueryCrmObjectArg customerArg = new PaasQueryCrmObjectArg();
                customerArg.setPageSize(10);
                customerArg.setPageNumber(0);
                if (StringUtils.isNotBlank(crmMemberVo.getCustomerId())) {
                    ObjectData accountObjectData = crmV2Manager.getDetail(ea, fsUserId, AccountFieldContants.API_NAME, crmMemberVo.getCustomerId());
                    AccountData accountData = AccountData.wrap(accountObjectData);
                    memberDetailsResult.setCompany(accountData.getName());
                    memberDetailsResult.setEmail(accountData.getEmail());
                    memberDetailsResult.setPhone(accountData.getTel());
                    memberDetailsResult.setArea(accountData.getArea());
                }
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, memberDetailsResult);
    }

    @Override
    public Result<MemberCountDataResult> getMemberCountData(String ea, Integer fsUserId) {
        //本月新增会员
        int monthNewMemberCount = this.getMonthNewMemberCount(ea);
        //全部会员
        int allMemberCount = crmMetadataManager.countCrmObjectData(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName());
        //可用积分
        double availableIntegralCount = memberManager.countAllAvailableIntegral(ea);

        MemberCountDataResult result = new MemberCountDataResult();
        result.setMonthNewMemberCount(monthNewMemberCount);
        result.setAllMemberCount(allMemberCount);
        result.setAvailableIntegralCount(availableIntegralCount);
        return Result.newSuccess(result);
    }

    @Override
    public Result<MemberConfigVO> getMemberConfig(String ea) {
        MemberConfigEntity memberConfig = memberManager.tryInitMemberConfig(ea);
        MemberConfigVO memberConfigVO = new MemberConfigVO();
        BeanUtils.copyProperties(memberConfig, memberConfigVO);
        return Result.newSuccess(memberConfigVO);
    }
    
    @Override
    public Result<Boolean> updateMemberFieldMapping(String ea, Integer memberFieldMappingType, String recordType, String leadPoolId, FieldMappings fieldMappings) {
        Preconditions.checkArgument(fieldMappings != null);
        MemberConfigEntity memberConfig = memberManager.tryInitMemberConfig(ea);
        if (memberConfig == null){
            throw new IllegalStateException();
        }
        if (MemberFieldMappingTypeEnum.LEAD_TO_MEMBER.getType() == memberFieldMappingType){
            Set<String> notNullCrmMemberFieldNames = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MEMBER).stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull())).map(CrmUserDefineFieldVo::getFieldName).collect(Collectors.toSet());
            boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
            if (!verifyResult){
                return Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR);
            }
            MemberConfigEntity memberConfigEntity = new MemberConfigEntity();
            memberConfigEntity.setMemberRecordType(recordType);
            memberConfigEntity.setLeadToMemberFieldMappings(fieldMappings);
            memberConfigDao.updateLeadToMemberFieldMappings(ea, memberConfigEntity);
            return Result.newSuccess(true);
        }
        if (MemberFieldMappingTypeEnum.MEMBER_TO_LEAD.getType() == memberFieldMappingType){
            Set<String> notNullCrmMemberFieldNames = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD).stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull())).map(CrmUserDefineFieldVo::getFieldName).collect(Collectors.toSet());
            log.info("MEMBER_TO_LEAD ea:{} notNullCrmLeadFieldNames:{}", ea, notNullCrmMemberFieldNames);
            notNullCrmMemberFieldNames.remove("data_own_organization");
            boolean verifyResult = fieldMappings.doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
            if (!verifyResult){
                log.info("updateMemberFieldMapping MEMBER_TO_LEAD failed ea:{} memberFieldMappingType:{}", ea, memberFieldMappingType);
                return Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR);
            }
            MemberConfigEntity memberConfigEntity = new MemberConfigEntity();
            memberConfigEntity.setMemberToLeadFieldMappings(fieldMappings);
            memberConfigEntity.setLeadRecordType(recordType);
            memberConfigEntity.setLeadPoolId(leadPoolId);
            memberConfigDao.updateMemberToLeadFieldMappings(ea, memberConfigEntity);
            return Result.newSuccess(true);
        }
        throw new IllegalArgumentException(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MEMBERSERVICEIMPL_424));
    }
    
    @Override
    public Result<Boolean> addMemberAccessibleHexagonSite(String ea, String hexagonSiteId) {
        memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteId);
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> deleteMemberAccessibleHexagonSite(String ea, String hexagonSiteId) {
        memberAccessibleObjectDao.deleteByEaAndObjectId(ea, hexagonSiteId);
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> updateMemberAccessibleHexagonSite(String ea, String hexagonSiteIdToDelete, String hexagonSiteIdToInsert) {
        if (hexagonSiteIdToDelete.equals(hexagonSiteIdToInsert)){
            return Result.newSuccess(true);
        }
        boolean insertSuccess = memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteIdToInsert) > 0;
        if (!insertSuccess){
            return Result.newError(SHErrorCode.HEXAGON_SITE_ALREADY_IN_ACCESSIBLE_LIST, false);
        }
        memberAccessibleObjectDao.deleteByEaAndObjectId(ea, hexagonSiteIdToDelete);
        return Result.newSuccess();
    }

    @Override
    public Result<List<IdName>> listMemberAccessibleHexagonSite(String ea) {
        return Result.newSuccess(memberAccessibleObjectDao.listAccessibleHexagonSites(ea));
    }

    @Override
    public Result<String> addMemberAccessibleWebsite(String ea, AddMemberAccessibleWebsiteArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getName()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getUrl()));
        String id = UUIDUtil.getUUID();
        memberAccessibleWebsiteDao.insertIgnore(id, ea, arg.getName(), arg.getUrl());
        return Result.newSuccess(id);
    }

    @Override
    public Result<Boolean> deleteMemberAccessibleWebsite(String ea, String id) {
        boolean deleted = memberAccessibleWebsiteDao.deleteById(ea, id) > 0;
        return Result.newSuccess(deleted);
    }

    @Override
    public Result<Boolean> updateMemberAccessibleWebsite(String ea, UpdateMemberAccessibleWebsiteArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getId()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getName()));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getUrl()));
        boolean updated = memberAccessibleWebsiteDao.updateById(arg.getId(), ea, arg.getName(), arg.getUrl()) > 0;
        return Result.newSuccess(updated);
    }

    @Override
    public Result<List<MemberAccessibleWebsiteVo>> listMemberAccessibleWebsite(String ea) {
        List<MemberAccessibleWebsiteEntity> memberAccessibleWebsiteEntities = memberAccessibleWebsiteDao.listAccessibleHexagonSites(ea);
        List<MemberAccessibleWebsiteVo> result = memberAccessibleWebsiteEntities.stream().map(memberAccessibleWebsiteEntity -> {
            MemberAccessibleWebsiteVo vo = new MemberAccessibleWebsiteVo();
            BeanUtils.copyProperties(memberAccessibleWebsiteEntity, vo);
            return vo;
        }).collect(Collectors.toList());
        return Result.newSuccess(result);
    }
    
    @Override
    public Result<MemberConfigVO> getMemberConfig(Integer objectType, String objectId) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        Preconditions.checkArgument(ea != null);
        return this.getMemberConfig(ea);
    }
    
    private int getMonthNewMemberCount(String ea) {
        String monthStartDate = String.valueOf(DateUtil.getTimesThisMonthStartTime().getTime());
        Filter filter = new Filter();
        filter.setFieldName("create_time");
        filter.setOperator("GTE");
        filter.setFieldValues(Lists.newArrayList(monthStartDate));
        return crmMetadataManager.countCrmObjectDataByFilter(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), Lists.newArrayList(filter));
    }
    
    @Override
    public Result<MemberEnrollResult> checkWxMiniAppUserInMarketingEvent(Integer objectType, String objectId, String uid, String marketingEventId) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        memberEnrollResult.setMemberEnroll(false);
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(ea, uid);
        boolean isMember = optionalMemberId.isPresent();
        if(isMember) {
            Result<Boolean> memberEnroll = checkMemberInMarketingEvent(ea, optionalMemberId.get(), marketingEventId);
            if (!memberEnroll.isSuccess()) {
                return Result.newError(memberEnroll.getErrCode(), memberEnroll.getErrMsg());
            }
            if (memberEnroll.getData() != null) {
                memberEnrollResult.setMemberEnroll(memberEnroll.getData());
            }
            memberManager.checkEnrollReviewStatus(ea,marketingEventId,optionalMemberId.get(), memberEnrollResult);
            return Result.newSuccess(memberEnrollResult);
        }
        //根据物料直接查询用户提交的表单数据
        List<CustomizeFormDataUserEntity> formDataUserEntities;
        if (Objects.equals(ObjectTypeEnum.HEXAGON_SITE.getType(),objectType)) {
            List<HexagonPageEntity> hexagonPageEntities = hexagonPageDAO.getByHexagonSiteId(objectId);
            if (CollectionUtils.isEmpty(hexagonPageEntities)) {
                return Result.newError(SHErrorCode.NOT_MEMBER);
            }
            List<String> formIds = hexagonPageEntities.stream().map(HexagonPageEntity::getFormId).collect(Collectors.toList());
            formDataUserEntities = customizeFormDataUserDAO.queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(ea, marketingEventId,null,uid,null,null,formIds);
        } else {
            formDataUserEntities = customizeFormDataUserDAO.queryCustomizeFormDataUserByObjectIdAndUserInfo(ea, null,uid,null,null, objectId, objectType, marketingEventId);
        }
        if (CollectionUtils.isNotEmpty(formDataUserEntities)) {
            memberManager.checkCustomizeFormDataEnrollReview(ea,marketingEventId,formDataUserEntities,null,memberEnrollResult);
            memberEnrollResult.setMemberEnroll(true);
            return Result.newSuccess(memberEnrollResult);
        }
        String userMarketingId = customizeFormDataUserDAO.getMarketingUserAccountByEaAndUid(ea, uid);
        List<String> crmLeadIds = customizeFormDataUserDAO.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (!crmLeadIds.isEmpty()) {
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.checkInMarketingEvent(marketingEventId, crmLeadIds);
            if (customizeFormDataUserEntities != null && !customizeFormDataUserEntities.isEmpty()) {
                memberManager.checkCustomizeFormDataEnrollReview(ea,marketingEventId,customizeFormDataUserEntities,null,memberEnrollResult);
                memberEnrollResult.setMemberEnroll(true);
                return Result.newSuccess(memberEnrollResult);
            }
        }
        memberManager.checkActivityEnrollReview(ea, marketingEventId, memberEnrollResult);
        return Result.newSuccess(memberEnrollResult);
    }
    
    @Override
    public Result<MemberEnrollResult> checkH5UserInMarketingEvent(Integer objectType, String objectId, String fingerPrint, Map<String, String> allMemberCookieInfos, String marketingEventId) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        memberEnrollResult.setMemberEnroll(false);
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember) {
            Result<Boolean> memberEnroll = checkMemberInMarketingEvent(ea, optionalMemberId.get(), marketingEventId);
            if (!memberEnroll.isSuccess()) {
                return Result.newError(memberEnroll.getErrCode(), memberEnroll.getErrMsg());
            }
            if (memberEnroll.getData() != null) {
                memberEnrollResult.setMemberEnroll(memberEnroll.getData());
            }
            memberManager.checkEnrollReviewStatus(ea, marketingEventId, optionalMemberId.get(), memberEnrollResult);
            return Result.newSuccess(memberEnrollResult);
        }
        if (StringUtils.isBlank(fingerPrint)) {
            log.warn("checkH5UserInMarketingEvent fingerPrint is empty objectId:{},objectType:{},marketingEventId:{}", fingerPrint,objectId,marketingEventId);
            return Result.newSuccess(memberEnrollResult);
        }
        //根据物料直接查询用户提交的表单数据
        List<CustomizeFormDataUserEntity> formDataUserEntities;
        if (Objects.equals(ObjectTypeEnum.HEXAGON_SITE.getType(),objectType)) {
            List<HexagonPageEntity> hexagonPageEntities = hexagonPageDAO.getByHexagonSiteId(objectId);
            if (CollectionUtils.isEmpty(hexagonPageEntities)) {
                return Result.newError(SHErrorCode.NOT_MEMBER);
            }
            List<String> formIds = hexagonPageEntities.stream().map(HexagonPageEntity::getFormId).collect(Collectors.toList());
            formDataUserEntities = customizeFormDataUserDAO.queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(ea, marketingEventId,fingerPrint,null,null,null,formIds);
        } else {
            formDataUserEntities = customizeFormDataUserDAO.queryCustomizeFormDataUserByObjectIdAndUserInfo(ea, fingerPrint,null,null,null, objectId, objectType, marketingEventId);
        }
        if (CollectionUtils.isNotEmpty(formDataUserEntities)) {
            memberManager.checkCustomizeFormDataEnrollReview(ea,marketingEventId,formDataUserEntities,null,memberEnrollResult);
            memberEnrollResult.setMemberEnroll(true);
            return Result.newSuccess(memberEnrollResult);
        }
        UserMarketingAccountEntity userMarketingAccountEntity = userMarketingBrowserUserRelationDao.getMarketingUserAccountByEaAndBrowserUserId(ea, fingerPrint);
        if (userMarketingAccountEntity != null && StringUtils.isNotEmpty(userMarketingAccountEntity.getPhone())) {
            List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByPhoneLike(ea, marketingEventId, userMarketingAccountEntity.getPhone(), false);
            if (CollectionUtils.isNotEmpty(campaignMergeDataEntities)) {
                memberManager.checkCustomizeFormDataEnrollReview(ea,marketingEventId,null,campaignMergeDataEntities,memberEnrollResult);
                memberEnrollResult.setMemberEnroll(true);
                return Result.newSuccess(memberEnrollResult);
            }
        }
        memberManager.checkActivityEnrollReview(ea, marketingEventId, memberEnrollResult);
        return Result.newSuccess(memberEnrollResult);
    }
    
    @Override
    public Result<MemberEnrollResult> checkWxServiceUserInMarketingEvent(Integer objectType, String objectId, String wxAppId, String wxOpenId, String marketingEventId) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        memberEnrollResult.setMemberEnroll(false);
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember) {
            Result<Boolean> memberEnroll = checkMemberInMarketingEvent(ea, optionalMemberId.get(), marketingEventId);
            if (!memberEnroll.isSuccess()) {
                return Result.newError(memberEnroll.getErrCode(), memberEnroll.getErrMsg());
            }
            if (memberEnroll.getData() != null) {
                memberEnrollResult.setMemberEnroll(memberEnroll.getData());
            }
            memberManager.checkEnrollReviewStatus(ea, marketingEventId, optionalMemberId.get(), memberEnrollResult);
            return Result.newSuccess(memberEnrollResult);
        }
        //根据物料直接查询用户提交的表单数据
        List<CustomizeFormDataUserEntity> formDataUserEntities;
        if (Objects.equals(ObjectTypeEnum.HEXAGON_SITE.getType(),objectType)) {
            List<HexagonPageEntity> hexagonPageEntities = hexagonPageDAO.getByHexagonSiteId(objectId);
            if (CollectionUtils.isEmpty(hexagonPageEntities)) {
                return Result.newError(SHErrorCode.NOT_MEMBER);
            }
            List<String> formIds = hexagonPageEntities.stream().map(HexagonPageEntity::getFormId).collect(Collectors.toList());
            formDataUserEntities = customizeFormDataUserDAO.queryCustomizeDataUserByEaAndMarketingEventIdAndFormIds(ea, marketingEventId,null,null,wxAppId,wxOpenId,formIds);
        } else {
            formDataUserEntities = customizeFormDataUserDAO.queryCustomizeFormDataUserByObjectIdAndUserInfo(ea, null,null,wxAppId,wxOpenId, objectId, objectType, marketingEventId);
        }
        if (CollectionUtils.isNotEmpty(formDataUserEntities)) {
            memberManager.checkCustomizeFormDataEnrollReview(ea,marketingEventId,formDataUserEntities,null,memberEnrollResult);
            memberEnrollResult.setMemberEnroll(true);
            return Result.newSuccess(memberEnrollResult);
        }
        UserMarketingAccountEntity userMarketingAccountEntity = userMarketingWxServiceAccountRelationDao.getMarketingUserAccountByEaAndWxAppIdAndWxOpenId(ea, wxAppId, wxOpenId);
        if (userMarketingAccountEntity != null && StringUtils.isNotEmpty(userMarketingAccountEntity.getPhone())) {
            List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByPhoneLike(ea, marketingEventId, userMarketingAccountEntity.getPhone(), false);
            if (CollectionUtils.isNotEmpty(campaignMergeDataEntities)) {
                memberManager.checkCustomizeFormDataEnrollReview(ea,marketingEventId,null,campaignMergeDataEntities,memberEnrollResult);
                memberEnrollResult.setMemberEnroll(true);
                return Result.newSuccess(memberEnrollResult);
            }
        }
        memberManager.checkActivityEnrollReview(ea, marketingEventId, memberEnrollResult);
        return Result.newSuccess(memberEnrollResult);
    }
    
    @Override
    public Result<MemberEnrollResult> wxMiniAppMemberEnroll(String uid, MemberEnrollArg memberEnrollArg) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        String ea = objectManager.getObjectEa(memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType());
        if (Strings.isNullOrEmpty(memberEnrollArg.getMarketingEventId()) && !Strings.isNullOrEmpty(memberEnrollArg.getMarketingActivityId())) {
            memberEnrollArg.setMarketingEventId(objectManager.queryMarketingEventId(memberEnrollArg.getMarketingActivityId()));
        }
        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(ea, uid);
        boolean isMember = optionalMemberId.isPresent();
        if (!isMember){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
        if (objectData != null){
            String approvalStatus =(String) objectData.get("approval_status");
            //待审核未审核不允许一键自动报名
            if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                    ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
            }
        }
        // 查询推广人
        if (memberEnrollArg.getSpreadFsUid() == null && StringUtils.isNotBlank(memberEnrollArg.getTargetUid())) {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(memberEnrollArg.getTargetUid());
            if (fsBindEntity != null) {
                memberEnrollArg.setSpreadFsUid(fsBindEntity.getFsUserId());
            }
        }
        Result checkResult = memberEnrollCheck(ea, memberEnrollArg.getMarketingEventId());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        // 设置channelValue
        memberEnrollArg.setChannelValue(memberManager.getSystemPromotionChannelType(memberEnrollArg, null, null, uid));
        memberEnrollArg.setEa(ea);
        memberEnrollArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
        memberEnrollArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObj(memberEnrollArg);
        SaveMemberToCampaignMergeDataResultContainer saveMemberToCampaignMergeDataResultContainer = memberManager.saveMemberToCampaignMergeData(uid, null, null, null, ea, optionalMemberId.get(), memberEnrollArg.getMarketingActivityId(), memberEnrollArg.getMarketingEventId(), memberEnrollArg.getSpreadFsUid(), memberEnrollArg.getChannelValue(), memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType(), true, marketingPromotionSourceId);
        if (saveMemberToCampaignMergeDataResultContainer == null){
            return Result.newError(SHErrorCode.MEMBER_ENROLL_FAIL);
        }
        if (memberEnrollArg.getNeedSignIn() != null && memberEnrollArg.getNeedSignIn()) {
            conferenceManager.signInUserByCampaignId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId(),memberEnrollArg.getTagId());
        }
        ThreadPoolUtils.execute(() -> {
            ActivityEntity activityEntity = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, memberEnrollArg.getMarketingEventId());
            String triggerAction = "memberEnroll";
            if (StringUtils.isNotBlank(saveMemberToCampaignMergeDataResultContainer.getLeadId())) {
                ObjectData memberData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
                String phone = memberData.getString(CrmMemberFieldEnum.PHONE.getApiName());
                userMarketingAccountRelationManager.bindMiniappUserAndLead(ea, uid, saveMemberToCampaignMergeDataResultContainer.getLeadId(), phone, triggerAction);
                // 添加标签
                MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, memberEnrollArg.getMarketingEventId());
                if (memberMarketingEventCrmConfigEntity != null) {
                    objectTagManager.batchAddTagsToUserMarketings(ea, phone,
                        ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), null,
                        memberEnrollArg.getMarketingEventId(), triggerAction);
                } else {
                    // 若为会议且市场活动下没有设置标签取会议设置的标签
                    if (activityEntity != null) {
                        objectTagManager.batchAddTagsToUserMarketings(activityEntity.getEa(), phone,
                            ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, memberEnrollArg.getMarketingEventId(), triggerAction);
                    }
                }
            }
            RecordActionArg recordActionArg = new RecordActionArg();
            recordActionArg.setEa(ea);
            recordActionArg.setMarketingEventId(memberEnrollArg.getMarketingEventId());
            recordActionArg.setMarketingActivityId(memberEnrollArg.getMarketingActivityId());
            recordActionArg.setChannelType(MarketingUserActionChannelType.MANKEEP.getChannelType());
            recordActionArg.setFingerPrint(uid);
            recordActionArg.setSpreadFsUid(memberEnrollArg.getSpreadFsUid());
            if (activityEntity != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                recordActionArg.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                recordActionArg.setObjectId(activityEntity.getId());
            }
            MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), memberEnrollArg.getMarketingEventId());
            if (marketingLive != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                recordActionArg.setActionType(MarketingUserActionType.ENROLL_LIVE.getActionType());
                recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                recordActionArg.setObjectId(marketingLive.getId());
            }
            if (recordActionArg.getActionType() != null){
                actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
            }

        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        //会议审核
        memberManager.checkEnrollReviewStatus(ea,memberEnrollArg.getMarketingEventId(),optionalMemberId.get(), memberEnrollResult);
        memberEnrollResult.setLeadId(saveMemberToCampaignMergeDataResultContainer.getLeadId());
        // 若为会议发送通知
        memberManager.sendActivityNotificationSmsByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendConferenceQywxReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        return Result.newSuccess(memberEnrollResult);
    }
    
    @Override
    public Result<MemberEnrollResult> wxServiceMemberEnroll(String wxAppId, String wxOpenId, MemberEnrollArg memberEnrollArg) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        String ea = objectManager.getObjectEa(memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType());
        if (Strings.isNullOrEmpty(memberEnrollArg.getMarketingEventId()) && !Strings.isNullOrEmpty(memberEnrollArg.getMarketingActivityId())) {
            memberEnrollArg.setMarketingEventId(objectManager.queryMarketingEventId(memberEnrollArg.getMarketingActivityId()));
        }
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        if (!isMember){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
        if (objectData != null){
            String approvalStatus =(String) objectData.get("approval_status");
            //待审核未审核不允许一键自动报名
            if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                    ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
            }
        }
        Result checkResult = memberEnrollCheck(ea, memberEnrollArg.getMarketingEventId());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        // 设置channelValue
        memberEnrollArg.setChannelValue(memberManager.getSystemPromotionChannelType(memberEnrollArg, wxAppId, wxOpenId, null));
        memberEnrollArg.setEa(ea);
        memberEnrollArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
        memberEnrollArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObj(memberEnrollArg);
        SaveMemberToCampaignMergeDataResultContainer saveMemberToCampaignMergeDataResultContainer = memberManager
            .saveMemberToCampaignMergeData(null, wxAppId, wxOpenId, null, ea, optionalMemberId.get(), memberEnrollArg.getMarketingActivityId(), memberEnrollArg.getMarketingEventId(), memberEnrollArg.getSpreadFsUid(),
                memberEnrollArg.getChannelValue(), memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType(), true, marketingPromotionSourceId);
        if (saveMemberToCampaignMergeDataResultContainer == null) {
            return Result.newError(SHErrorCode.MEMBER_ENROLL_FAIL);
        }
        if (memberEnrollArg.getNeedSignIn() != null && memberEnrollArg.getNeedSignIn()) {
            conferenceManager.signInUserByCampaignId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId(),memberEnrollArg.getTagId());
        }
        ThreadPoolUtils.execute(() -> {
            ActivityEntity activityEntity = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, memberEnrollArg.getMarketingEventId());
            String triggerAction = "memberEnroll";
            if (StringUtils.isNotBlank(saveMemberToCampaignMergeDataResultContainer.getLeadId())) {
                ObjectData memberData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
                String phone = memberData.getString(CrmMemberFieldEnum.PHONE.getApiName());
                userMarketingAccountRelationManager.bindWxUserAndLead(ea, wxAppId, wxOpenId, saveMemberToCampaignMergeDataResultContainer.getLeadId(), phone, triggerAction);
                // 添加标签
                MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, memberEnrollArg.getMarketingEventId());
                if (memberMarketingEventCrmConfigEntity != null) {
                    objectTagManager.batchAddTagsToUserMarketings(ea, phone,
                        ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), null,
                        memberEnrollArg.getMarketingEventId(), triggerAction);
                } else {
                    // 若为会议且市场活动下没有设置标签取会议设置的标签
                    if (activityEntity != null) {
                        objectTagManager.batchAddTagsToUserMarketings(activityEntity.getEa(), phone,
                            ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, memberEnrollArg.getMarketingEventId(), triggerAction);
                    }
                }
            }
            RecordActionArg recordActionArg = new RecordActionArg();
            recordActionArg.setEa(ea);
            recordActionArg.setMarketingEventId(memberEnrollArg.getMarketingEventId());
            recordActionArg.setMarketingActivityId(memberEnrollArg.getMarketingActivityId());
            recordActionArg.setChannelType(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType());
            recordActionArg.setWxAppId(wxAppId);
            recordActionArg.setWxOpenId(wxOpenId);
            recordActionArg.setSpreadFsUid(memberEnrollArg.getSpreadFsUid());
            if (activityEntity != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                recordActionArg.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                recordActionArg.setObjectId(activityEntity.getId());
            }
            MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), memberEnrollArg.getMarketingEventId());
            if (marketingLive != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                recordActionArg.setActionType(MarketingUserActionType.ENROLL_LIVE.getActionType());
                recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                recordActionArg.setObjectId(marketingLive.getId());
            }
            if (recordActionArg.getActionType() != null){
                actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        //会议报名审核状态 10.2
        memberManager.checkEnrollReviewStatus(ea,memberEnrollArg.getMarketingEventId(),optionalMemberId.get(),memberEnrollResult);
        memberEnrollResult.setLeadId(saveMemberToCampaignMergeDataResultContainer.getLeadId());
        // 若为会议发送通知
        memberManager.sendActivityNotificationSmsByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendConferenceQywxReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        return Result.newSuccess(memberEnrollResult);
    }
    
    @Override
    public Result<MemberEnrollResult> h5MemberEnroll(Map<String, String> allMemberCookieInfos, MemberEnrollArg memberEnrollArg) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        String ea = objectManager.getObjectEa(memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType());
        if (Strings.isNullOrEmpty(memberEnrollArg.getMarketingEventId()) && !Strings.isNullOrEmpty(memberEnrollArg.getMarketingActivityId())) {
            memberEnrollArg.setMarketingEventId(objectManager.queryMarketingEventId(memberEnrollArg.getMarketingActivityId()));
        }
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean isMember = optionalMemberId.isPresent();
        if (!isMember){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
        if (objectData != null){
            String approvalStatus =(String) objectData.get("approval_status");
            //待审核未审核不允许一键自动报名
            if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                    ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
            }
        }
        Result checkResult = memberEnrollCheck(ea, memberEnrollArg.getMarketingEventId());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        // 设置channelValue
        memberEnrollArg.setChannelValue(memberManager.getSystemPromotionChannelType(memberEnrollArg, null, null, null));
        memberEnrollArg.setEa(ea);
        memberEnrollArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
        memberEnrollArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObj(memberEnrollArg);
        SaveMemberToCampaignMergeDataResultContainer saveMemberToCampaignMergeDataResultContainer = memberManager
            .saveMemberToCampaignMergeData(null, null, null, memberEnrollArg.getFingerPrintId(), ea, optionalMemberId.get(), memberEnrollArg.getMarketingActivityId(), memberEnrollArg.getMarketingEventId(),
                memberEnrollArg.getSpreadFsUid(),
                memberEnrollArg.getChannelValue(), memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType(), true, marketingPromotionSourceId);
        if (saveMemberToCampaignMergeDataResultContainer == null) {
            return Result.newError(SHErrorCode.MEMBER_ENROLL_FAIL);
        }
        if (memberEnrollArg.getNeedSignIn() != null && memberEnrollArg.getNeedSignIn()) {
            conferenceManager.signInUserByCampaignId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId(),memberEnrollArg.getTagId());
        }
        if (!Strings.isNullOrEmpty(memberEnrollArg.getFingerPrintId())){
            ThreadPoolUtils.execute(() -> {
                ActivityEntity activityEntity = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, memberEnrollArg.getMarketingEventId());
                String triggerAction = "memberEnroll";
                if (StringUtils.isNotBlank(saveMemberToCampaignMergeDataResultContainer.getLeadId())) {
                    ObjectData memberData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
                    String phone = memberData.getString(CrmMemberFieldEnum.PHONE.getApiName());
                    log.info("h5MemberEnroll memberEnrollArg:{} leadId:{} phone:{}", memberEnrollArg, saveMemberToCampaignMergeDataResultContainer.getLeadId(), phone);
                    userMarketingAccountRelationManager.bindBrowserUserAndLead(ea, memberEnrollArg.getFingerPrintId(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), Strings.isNullOrEmpty(phone) ? null : phone, triggerAction);
                    // 添加标签
                    MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, memberEnrollArg.getMarketingEventId());
                    if (memberMarketingEventCrmConfigEntity != null) {
                        objectTagManager.batchAddTagsToUserMarketings(ea, phone,
                            ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), null,
                            memberEnrollArg.getMarketingEventId(), triggerAction);
                    } else {
                        // 若为会议且市场活动下没有设置标签取会议设置的标签
                        if (activityEntity != null) {
                            objectTagManager.batchAddTagsToUserMarketings(activityEntity.getEa(), phone,
                                ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, memberEnrollArg.getMarketingEventId(), triggerAction);
                        }
                    }
                }
                RecordActionArg recordActionArg = new RecordActionArg();
                recordActionArg.setEa(ea);
                recordActionArg.setMarketingEventId(memberEnrollArg.getMarketingEventId());
                recordActionArg.setMarketingActivityId(memberEnrollArg.getMarketingActivityId());
                recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                recordActionArg.setFingerPrint(memberEnrollArg.getFingerPrintId());
                recordActionArg.setSpreadFsUid(memberEnrollArg.getSpreadFsUid());
                if (activityEntity != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                    recordActionArg.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                    recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                    recordActionArg.setObjectId(activityEntity.getId());
                }
                MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), memberEnrollArg.getMarketingEventId());
                if (marketingLive != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                    recordActionArg.setActionType(MarketingUserActionType.ENROLL_LIVE.getActionType());
                    recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                    recordActionArg.setObjectId(marketingLive.getId());
                }
                if (recordActionArg.getActionType() != null){
                    actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        //会议报名审核状态 10.2
        memberManager.checkEnrollReviewStatus(ea,memberEnrollArg.getMarketingEventId(),optionalMemberId.get(),memberEnrollResult);
        memberEnrollResult.setLeadId(saveMemberToCampaignMergeDataResultContainer.getLeadId());
        // 若为会议发送通知
        memberManager.sendActivityNotificationSmsByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendConferenceQywxReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        return Result.newSuccess(memberEnrollResult);
    }

    @Override
    public Result<MemberEnrollResult> partnerMemberEnroll(String eRUpstreamEa, String eROuterTenantId, String eROuterUid, MemberEnrollArg memberEnrollArg) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        String ea = objectManager.getObjectEa(memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType());
        if (Strings.isNullOrEmpty(memberEnrollArg.getMarketingEventId()) && !Strings.isNullOrEmpty(memberEnrollArg.getMarketingActivityId())) {
            memberEnrollArg.setMarketingEventId(objectManager.queryMarketingEventId(memberEnrollArg.getMarketingActivityId()));
        }
        Result<QueryMemberInfoResult> memberInfoResult = queryPartnerMemberInfo(memberEnrollArg.getObjectId(),memberEnrollArg.getObjectType(), eRUpstreamEa,eROuterTenantId, eROuterUid);
        if(!memberInfoResult.isSuccess() || memberInfoResult.getData() == null || !memberInfoResult.getData().isCheckIsMember()){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        String memberId = memberInfoResult.getData().getMemberInfo().getMemberId();
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        if (objectData != null){
            String approvalStatus =(String) objectData.get("approval_status");
            //待审核未审核不允许一键自动报名
            if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                    ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
            }
        }
        Result checkResult = memberEnrollCheck(ea, memberEnrollArg.getMarketingEventId());
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        // 设置channelValue
        memberEnrollArg.setChannelValue(memberManager.getSystemPromotionChannelType(memberEnrollArg, null, null, null));
        memberEnrollArg.setEa(ea);
        memberEnrollArg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.FORM.getLeadModule());
        memberEnrollArg.setDataFrom(MarketingPromotionDataFromEnum.YXT.getDataSource());
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObj(memberEnrollArg);
        SaveMemberToCampaignMergeDataResultContainer saveMemberToCampaignMergeDataResultContainer = memberManager
                .saveMemberToCampaignMergeData(null, null, null, memberEnrollArg.getFingerPrintId(), ea, memberId, memberEnrollArg.getMarketingActivityId(), memberEnrollArg.getMarketingEventId(),
                        memberEnrollArg.getSpreadFsUid(),
                        memberEnrollArg.getChannelValue(), memberEnrollArg.getObjectId(), memberEnrollArg.getObjectType(), true, marketingPromotionSourceId);
        if (saveMemberToCampaignMergeDataResultContainer == null) {
            return Result.newError(SHErrorCode.MEMBER_ENROLL_FAIL);
        }
        if (memberEnrollArg.getNeedSignIn() != null && memberEnrollArg.getNeedSignIn()) {
            conferenceManager.signInUserByCampaignId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId(),memberEnrollArg.getTagId());
        }
        if (!Strings.isNullOrEmpty(memberEnrollArg.getFingerPrintId())){
            ThreadPoolUtils.execute(() -> {
                ActivityEntity activityEntity = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, memberEnrollArg.getMarketingEventId());
                String triggerAction = "memberEnroll";
                if (StringUtils.isNotBlank(saveMemberToCampaignMergeDataResultContainer.getLeadId())) {
                    ObjectData memberData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
                    String phone = memberData.getString(CrmMemberFieldEnum.PHONE.getApiName());
                    log.info("h5MemberEnroll memberEnrollArg:{} leadId:{} phone:{}", memberEnrollArg, saveMemberToCampaignMergeDataResultContainer.getLeadId(), phone);
                    userMarketingAccountRelationManager.bindBrowserUserAndLead(ea, memberEnrollArg.getFingerPrintId(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), Strings.isNullOrEmpty(phone) ? null : phone, triggerAction);
                    // 添加标签
                    MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(ea, memberEnrollArg.getMarketingEventId());
                    if (memberMarketingEventCrmConfigEntity != null) {
                        objectTagManager.batchAddTagsToUserMarketings(ea, phone,
                                ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), null,
                                memberEnrollArg.getMarketingEventId(), triggerAction);
                    } else {
                        // 若为会议且市场活动下没有设置标签取会议设置的标签
                        if (activityEntity != null) {
                            objectTagManager.batchAddTagsToUserMarketings(activityEntity.getEa(), phone,
                                    ChannelEnum.CRM_LEAD.getType(), saveMemberToCampaignMergeDataResultContainer.getLeadId(), null, activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), null, memberEnrollArg.getMarketingEventId(), triggerAction);
                        }
                    }
                }
                RecordActionArg recordActionArg = new RecordActionArg();
                recordActionArg.setEa(ea);
                recordActionArg.setMarketingEventId(memberEnrollArg.getMarketingEventId());
                recordActionArg.setMarketingActivityId(memberEnrollArg.getMarketingActivityId());
                recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                recordActionArg.setFingerPrint(memberEnrollArg.getFingerPrintId());
                recordActionArg.setSpreadFsUid(memberEnrollArg.getSpreadFsUid());
                if (activityEntity != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                    recordActionArg.setActionType(MarketingUserActionType.ENROLL_CONFERENCE.getActionType());
                    recordActionArg.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                    recordActionArg.setObjectId(activityEntity.getId());
                }
                MarketingLiveEntity marketingLive = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), memberEnrollArg.getMarketingEventId());
                if (marketingLive != null && contentMarketingEventMaterialRelationDAO.checkIsApplyObject(ea, memberEnrollArg.getMarketingEventId(), memberEnrollArg.getObjectId()) > 0){
                    recordActionArg.setActionType(MarketingUserActionType.ENROLL_LIVE.getActionType());
                    recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                    recordActionArg.setObjectId(marketingLive.getId());
                }
                if (recordActionArg.getActionType() != null){
                    actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        //会议报名审核状态 10.2
        memberManager.checkEnrollReviewStatus(ea,memberEnrollArg.getMarketingEventId(), memberId, memberEnrollResult);
        memberEnrollResult.setLeadId(saveMemberToCampaignMergeDataResultContainer.getLeadId());
        // 若为会议发送通知
        memberManager.sendActivityNotificationSmsByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        memberManager.sendConferenceQywxReviewMessageByCampaignMergeId(saveMemberToCampaignMergeDataResultContainer.getCampaignMergeDataId());
        return Result.newSuccess(memberEnrollResult);
    }

    private Result memberEnrollCheck(String ea, String marketingEventId) {
        if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(ea)) {
            return Result.newSuccess();
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        if (activityEntity != null) {
            if (activityEntity.getEndTime() != null && activityEntity.getEndTime().getTime() < new Date().getTime()) {
                return new Result(SHErrorCode.ACTIVITY_END);
            }
            if (activityEntity.getEnrollEndTime() != null && activityEntity.getEnrollEndTime().getTime() < new Date().getTime()) {
                return new Result(SHErrorCode.ENROLL_STOPPED);
            }
            if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
                return new Result(SHErrorCode.ACTIVITY_DISABLE);
            }
            if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
                return new Result(SHErrorCode.ACTIVITY_DELETED);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> getWxOpenIdByMemberId(String ea, String wxAppId, String memberId) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(wxAppId) || StringUtils.isEmpty(memberId)) {
            log.warn("MemberServiceImpl.getWxOpenIdByMemberId memberId is null ea:{} wxAppId:{} memberId:{}", ea, wxAppId, memberId);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<String> wxOpenIdOption = memberManager.getWxOpenIdByMemberId(ea, wxAppId, memberId);
        if (!wxOpenIdOption.isPresent()) {
            log.warn("MemberServiceImpl.getWxOpenIdByMemberId wxOpenIdOption is empty ea:{} wxAppId:{} memberId:{} wxOpenIdOption：{}", ea, wxAppId, memberId, wxOpenIdOption);
            return Result.newError(SHErrorCode.MEMBER_WXOPENID_FAIL);
        }
        return Result.newSuccess(wxOpenIdOption.get());
    }

    @Override
    public Result<GetMemberMarketingEventCrmConfigResult> getMemberMarketingEventCrmConfig(GetMemberMarketingEventCrmConfigVO vo) {
        MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(vo.getEa(), vo.getMarketingEventId());
        if (memberMarketingEventCrmConfigEntity == null) {
            log.warn("MemberServiceImpl.getMemberMarketingEventCrmConfig memberMarketingEventCrmConfigEntity is null vo:{}", vo);
            return Result.newSuccess();
        }
        GetMemberMarketingEventCrmConfigResult getMemberMarketingEventCrmConfigResult = new GetMemberMarketingEventCrmConfigResult();
        getMemberMarketingEventCrmConfigResult.setMarketingEventId(memberMarketingEventCrmConfigEntity.getMarketingEventId());
        getMemberMarketingEventCrmConfigResult.setId(memberMarketingEventCrmConfigEntity.getId());
        getMemberMarketingEventCrmConfigResult.setLeadPoolId(memberMarketingEventCrmConfigEntity.getLeadPoolId());
        getMemberMarketingEventCrmConfigResult.setLeadRecordType(memberMarketingEventCrmConfigEntity.getCrmRecordType());
        ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(vo.getEa(), memberMarketingEventCrmConfigEntity.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType());
        if (objectTagEntity != null) {
            getMemberMarketingEventCrmConfigResult.setTagNameList(objectTagEntity.getTagNameList());
        }
        getMemberMarketingEventCrmConfigResult.setMemberToLeadFieldMappings(memberMarketingEventCrmConfigEntity.getMemberToLeadFieldMappings());

        //报名跳转地址
        ObjectEnrollJumpSettingEntity objectEnrollJumpSettingEntity = objectEnrollJumpSettingDAO.getByMarketingEventId(vo.getEa(),vo.getMarketingEventId());
        if (objectEnrollJumpSettingEntity != null){
            getMemberMarketingEventCrmConfigResult.setJumpObjectId(objectEnrollJumpSettingEntity.getJumpObjectId());
            getMemberMarketingEventCrmConfigResult.setJumpObjectType(objectEnrollJumpSettingEntity.getJumpObjectType());
            getMemberMarketingEventCrmConfigResult.setJumpUrl(objectEnrollJumpSettingEntity.getJumpUrl());
        }
        //报名截止时间
        ActivityEnrollTimeConfigEntity activityEnrollTimeConfigEntity = activityEnrollTimeConfigDAO.queryEnrollTime(vo.getEa(), vo.getMarketingEventId());
        if (activityEnrollTimeConfigEntity != null) {
            if (activityEnrollTimeConfigEntity.getEnrollTime() != null) {
                getMemberMarketingEventCrmConfigResult.setEnrollTime(DateUtil.format("yyyy-MM-dd HH:mm",activityEnrollTimeConfigEntity.getEnrollTime()));
            }
            getMemberMarketingEventCrmConfigResult.setStatus(activityEnrollTimeConfigEntity.getStatus());
            getMemberMarketingEventCrmConfigResult.setEnrollTip(activityEnrollTimeConfigEntity.getEnrollTip());
        }
        return Result.newSuccess(getMemberMarketingEventCrmConfigResult);
    }

    @Override
    public Result upsertMemberMarketingEventCrmConfig(UpsertMemberMarketingEventCrmConfigVO vo) {
        //从CRM获取线索对象的字段名称
        Set<String> notNullCrmMemberFieldNames = crmV2Manager.getObjectFieldDescribesList(vo.getEa(), CrmObjectApiNameEnum.CRM_LEAD).stream().filter(field -> BooleanUtils.isTrue(field.getIsNotNull())).map(CrmUserDefineFieldVo::getFieldName).collect(Collectors.toSet());
        notNullCrmMemberFieldNames.remove("data_own_organization");
        boolean verifyResult = vo.getFieldMappings().doVerifyCrmNotNullFields(notNullCrmMemberFieldNames);
        if (!verifyResult){
            return Result.newError(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR);
        }
        // 添加/更新
        String id = UUIDUtil.getUUID();
        MemberMarketingEventCrmConfigEntity memberMarketingEventCrmConfigEntity = new MemberMarketingEventCrmConfigEntity();
        memberMarketingEventCrmConfigEntity.setId(id);
        memberMarketingEventCrmConfigEntity.setEa(vo.getEa());
        memberMarketingEventCrmConfigEntity.setMarketingEventId(vo.getMarketingEventId());
        memberMarketingEventCrmConfigEntity.setMemberToLeadFieldMappings(vo.getFieldMappings());
        memberMarketingEventCrmConfigEntity.setLeadPoolId(vo.getLeadPoolId());
        memberMarketingEventCrmConfigEntity.setCrmRecordType(vo.getRecordType());
        memberObjectCrmConfigDAO.upsertMemberMarketingEventCrmConfig(memberMarketingEventCrmConfigEntity);
        //设置报名截止时间
        if (vo.getEnrollTime() != null || vo.getStatus() != null) {
            ActivityEnrollTimeConfigEntity activityEnrollTimeConfigEntity = activityEnrollTimeConfigDAO.queryEnrollTime(vo.getEa(), vo.getMarketingEventId());
            if (activityEnrollTimeConfigEntity == null) {
                //进行新增报名截止时间配置
                ActivityEnrollTimeConfigEntity newActivityEnrollTimeConfigEntity = new ActivityEnrollTimeConfigEntity();
                newActivityEnrollTimeConfigEntity.setId(UUIDUtil.getUUID());
                newActivityEnrollTimeConfigEntity.setEa(vo.getEa());
                newActivityEnrollTimeConfigEntity.setEnrollTime(vo.getEnrollTime());
                newActivityEnrollTimeConfigEntity.setMarketingEventId(vo.getMarketingEventId());
                newActivityEnrollTimeConfigEntity.setObjectId(vo.getObjectId());
                newActivityEnrollTimeConfigEntity.setObjectType(vo.getObjectType());
                newActivityEnrollTimeConfigEntity.setEnrollTip(vo.getEnrollTip());
                newActivityEnrollTimeConfigEntity.setStatus(vo.getStatus());
                activityEnrollTimeConfigDAO.insertActivityEnrollTimeConfig(newActivityEnrollTimeConfigEntity);
            } else {
                //进行更新报名截止时间配置
                activityEnrollTimeConfigDAO.updateEnrollTimeConfig(activityEnrollTimeConfigEntity.getId(),vo.getStatus(),vo.getEnrollTime(),vo.getEnrollTip());
            }
        }
        // 添加标签
        MemberMarketingEventCrmConfigEntity existData = memberObjectCrmConfigDAO.getMemberMarketingEventCrmConfigByEventId(vo.getEa(), vo.getMarketingEventId());
        if (existData != null) {
            if (vo.getObjectType() != null && vo.getObjectType().equals(ObjectTypeEnum.ACTIVITY.getType()) && StringUtils.isNotBlank(vo.getObjectId())) {
                // 会议标签取会议本身的
                ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(vo.getEa(), vo.getObjectId(), vo.getObjectType());
                objectTagManager
                    .addOrUpdateObjectTag(vo.getEa(), vo.getFsUserId(), existData.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(),
                        objectTagEntity != null ? objectTagEntity.getTagNameList() : new TagNameList());
            } else {
                objectTagManager.addOrUpdateObjectTag(vo.getEa(), vo.getFsUserId(), existData.getId(), ObjectTypeEnum.MEMBER_MARKETING_EVENT_CRM_CONFIG.getType(), vo.getTagNameList());
            }
        }

        //设置报名跳转地址
        if ((vo.getJumpObjectId() != null && vo.getJumpObjectType() != null) || vo.getJumpUrl() != null) {
            ObjectEnrollJumpSettingEntity jumpSettingEntity = objectEnrollJumpSettingDAO.getByMarketingEventId(vo.getEa(), vo.getMarketingEventId());
            if (jumpSettingEntity == null) {
                jumpSettingEntity = new ObjectEnrollJumpSettingEntity();
                jumpSettingEntity.setId(UUIDUtil.getUUID());
                jumpSettingEntity.setEa(vo.getEa());
                jumpSettingEntity.setMarketingEventId(vo.getMarketingEventId());
                jumpSettingEntity.setJumpObjectId(vo.getJumpObjectId());
                jumpSettingEntity.setJumpObjectType(vo.getJumpObjectType());
                jumpSettingEntity.setJumpUrl(vo.getJumpUrl());
                objectEnrollJumpSettingDAO.insert(jumpSettingEntity);
            }else {
                jumpSettingEntity.setJumpUrl(vo.getJumpUrl());
                jumpSettingEntity.setJumpObjectType(vo.getJumpObjectType());
                jumpSettingEntity.setJumpObjectId(vo.getJumpObjectId());
                objectEnrollJumpSettingDAO.update(jumpSettingEntity);
            }
        }
        return Result.newSuccess();
    }

    private Result<Boolean> checkMemberInMarketingEvent(String ea, String memberId, String marketingEventId){
        ObjectData memberData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        String phone = memberData.getString(CrmMemberFieldEnum.PHONE.getApiName());
        if (Strings.isNullOrEmpty(phone)) {
            return Result.newError(SHErrorCode.MEMBER_HAVE_NO_PHONE);
        }
        boolean inEvent = !campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, marketingEventId, phone, false).isEmpty();
        return Result.newSuccess(inEvent);
    }
    
    @Override
    public Result<String> checkWxMiniAppUserHaveMemberAuth(Integer objectType, String objectId, String uid, boolean checkObjectAccess) {
        return memberManager.checkWxMiniAppUserHaveMemberAuth(objectType, objectId, uid, checkObjectAccess);
    }

    @Override
    public Result<String> checkWxMiniAppUserHaveMemberAuthReview(String ea, Integer objectType, String objectId, String uid, boolean checkObjectAccess) {
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(objectId, objectType);
        }
        // 先判断企业是否开通会员
        if (!memberManager.isOpenMember(ea)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        if (checkObjectAccess && (StringUtils.isBlank(objectId) || objectType == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(ea, uid);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                        ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                    return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
                }
            }
            return Result.newSuccess(optionalMemberId.get());
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            //判断是否是会员页面，修复会员页面不在member_accessible_object表里的问题
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            if (memberConfig != null && (StringUtils.equals(memberConfig.getContentCenterSiteId(), objectId) || StringUtils.equals(memberConfig.getUpdateInfoSiteId(), objectId))) {
                memberAccessibleObjectDao.insertIgnore(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), objectId);
                return Result.newError(SHErrorCode.NOT_MEMBER);
            }
            return Result.newSuccess();
        }

        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<String> checkWxServiceUserHaveMemberAuthReview(Integer objectType, String objectId, String wxAppId, String wxOpenId, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        if (!memberManager.isOpenMember(ea)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                        ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                    return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
                }
            }
            return Result.newSuccess(optionalMemberId.get());
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<String> checkH5UserHaveMemberAuthReview(String ea, Integer objectType, String objectId, Map<String, String> allMemberCookieInfos, boolean checkObjectAccess) {
        if(StringUtils.isBlank(ea)){
            ea = objectManager.getObjectEa(objectId, objectType);
        }
        if (!memberManager.isOpenMember(ea)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                        ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                    return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
                }
            }
            return Result.newSuccess(optionalMemberId.get());
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<String> checkWxServiceUserHaveMemberAuth(Integer objectType, String objectId, String wxAppId, String wxOpenId, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.EA_NOT_FOUND);
        }
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus) || Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_SUCCESS.getType()){
                    return Result.newSuccess(optionalMemberId.get());
                }
            }
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }
    
    @Override
    public Result<String> checkH5UserHaveMemberAuth(Integer objectType, String objectId, Map<String, String> allMemberCookieInfos, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.EA_NOT_FOUND);
        }
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus) || Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_SUCCESS.getType()){
                    return Result.newSuccess(optionalMemberId.get());
                }
            }
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }
    
    @Override
    public Result<String> checkH5UserIsMember(String ea, Map<String, String> allMemberCookieInfos) {
      return memberManager.checkH5UserIsMember(ea, allMemberCookieInfos);
    }
    

    @Override
    public Result<String> checkWxServiceUserIsMember(String ea,  String wxAppId, String wxOpenId) {
        return memberManager.checkWxServiceUserIsMember(ea, wxAppId, wxOpenId);
    }

    
    @Override
    public Result<Boolean> tryWxMiniAppUserRegisterOrLogin(String ea, String uid, String hexagonSiteId, CustomizeFormDataEnrollArg arg) {
        MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
        if (memberConfig != null) {
            HexagonPageEntity hexagonPage = hexagonPageDAO.getById(hexagonSiteId);
            if (hexagonPage.getHexagonSiteId().equals(memberConfig.getRegistrationSiteId())){
                return doWxMiniAppUserMemberRegister(ea, uid, arg);
            }
            if (hexagonPage.getHexagonSiteId().equals(memberConfig.getLoginSiteId())){
                return doWxMiniAppUserMemberLogin(ea, uid, arg);
            }
            if (hexagonPage.getHexagonSiteId().equals(memberConfig.getUpdateInfoSiteId())) {
                return doWxMiniAppUserMemberUpdateInfo(ea, uid, arg);
            }
            if (hexagonPage.getHexagonSiteId().equals(memberConfig.getForgotPasswordSiteId())) {
                if (Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || Strings.isNullOrEmpty(arg.getSubmitContent().getPassword())) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
                Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
                if (!optionalMember.isPresent()) {
                    return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
                }
                memberManager.saveMemberPassword(ea, optionalMember.get().getId(), AESUtil.encode(AESUtil.randomKeyByPassword(optionalMember.get().getId()), arg.getSubmitContent().getPassword()));
                return Result.newSuccess();
            }
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Boolean> trySyncLeadToMember(String ea, String uid, String customerFormDataUserId) {
        if (memberManager.getWxMiniAppUserBindMemberId(ea, uid).isPresent()){
            return Result.newSuccess(false);
        }
        CustomizeFormDataUserEntity customizeFormDataUser = customizeFormDataUserDAO.getCustomizeFormDataUserById(customerFormDataUserId);
        if (customizeFormDataUser != null && !Strings.isNullOrEmpty(customizeFormDataUser.getLeadId())) {
            CustomizeFormDataEntity customizeFormData = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUser.getFormId());
            if (customizeFormData != null && customizeFormData.getFormMoreSetting().isSyncToMember()) {
                Optional<String> memberOptional = memberManager.saveLeadToMemberAndSpreadData(ea, customizeFormDataUser,null);
                memberOptional.ifPresent(memberId -> wxMiniAppUserMemberBindDao.insertIgnore(ea, uid, memberId));
            }
        }
        return Result.newSuccess(false);
    }

    private Result<Boolean> doWxMiniAppUserMemberLogin(String ea, String uid, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg arg){
        if (com.google.common.base.Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || com.google.common.base.Strings.isNullOrEmpty(uid)){
            return Result.newError(SHErrorCode.PARAMS_ERROR, true);
        }
        if (StringUtils.isBlank(arg.getEa())) {
            arg.setEa(ea);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (!optionalMember.isPresent()){
            MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
            //开启一键注册登陆处理
            if(memberConfig != null && memberConfig.isDirectLoginAndRegistration()){
                Result<String> saveMemberResult = memberManager.saveMemberByLoginFormData(ea, arg, "other", null);
                if (!saveMemberResult.isSuccess()){
                    return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg(), true);
                }
                if(StringUtils.isNotBlank(arg.getCtaId())) {
                    ThreadPoolUtils.execute(() -> {
                        ctaService.recordMemberRegister(MarketingUserActionChannelType.MANKEEP, arg, saveMemberResult.getData());
                        ctaService.recordMemberLogin(MarketingUserActionChannelType.MANKEEP, arg, saveMemberResult.getData());
                    }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }
                ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
                    metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
                }
                memberMarketingManager.handleMemberMarketingConfig(ea, saveMemberResult.getData(), uid, arg.getWxAppId());
                // 将游客身份与员工身份绑定
                boolean bindStaff = staffMiniappUserBindManager.checkAndAddStaffUserBindSync(uid, ea, arg.getSubmitContent().getPhone());
                if(bindStaff){
                    return Result.newSuccess(true);
                }
                return Result.newSuccess(false);
            }
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED, true);
        }
        String boundMemberId = wxMiniAppUserMemberBindDao.getMemberIdByUid(ea, uid);
        if (!com.google.common.base.Strings.isNullOrEmpty(boundMemberId)){
            wxMiniAppUserMemberBindDao.deleteByMiniAppUser(ea, uid);
        }
        String memberId = optionalMember.get().getId();
        String password = arg.getSubmitContent().getPassword();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(password)) {
            if (!memberManager.verifyMemberPassword(ea, optionalMember.get().getId(), AESUtil.encode(AESUtil.randomKeyByPassword(optionalMember.get().getId()), password))) {
                return Result.newError(SHErrorCode.IDENTITY_INVAILED.getErrorCode(), I18nUtil.get("mark.member.password.error", "密码错误"));
            }
        }
        if(StringUtils.isNotBlank(arg.getCtaId())) {
            ThreadPoolUtils.execute(() -> {
                ctaService.recordMemberLogin(MarketingUserActionChannelType.MANKEEP, arg, memberId);
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        wxMiniAppUserMemberBindDao.insertIgnore(ea, uid, memberId);
        // 处理会员营销相关配置信息：绑定user_relation、生成名片
        memberMarketingManager.handleMemberMarketingConfig(ea, memberId, uid, arg.getWxAppId());
        // 将游客身份与员工身份绑定
        boolean bindStaff = staffMiniappUserBindManager.checkAndAddStaffUserBindSync(uid, ea, arg.getSubmitContent().getPhone());
        if (bindStaff) {
            // 需要重新登录
            return Result.newError(SHErrorCode.STAFF_MEMBER_LOGIN_REFRESH_TOKEN, true);
        }
        return Result.newSuccess(true);
    }

    private Result<Boolean> doWxMiniAppUserMemberRegister(String ea, String uid, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg arg){
        if (com.google.common.base.Strings.isNullOrEmpty(arg.getSubmitContent().getPhone()) || com.google.common.base.Strings.isNullOrEmpty(uid)){
            return Result.newError(SHErrorCode.PARAMS_ERROR, true);
        }

        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(ea, arg.getSubmitContent().getPhone());
        if (optionalMember.isPresent()){
            return Result.newError(SHErrorCode.PHONE_HAVE_BEEN_REGISTERED, true);
        }
        if (StringUtils.isBlank(arg.getEa())) {
            arg.setEa(ea);
        }
        String marketingPromotionSourceId = marketingPromotionSourceObjManager.tryGetOrCreateObjByFormEnroll(arg);
        Result<String> saveMemberResult = memberManager.saveMemberByFormData(ea, arg, "other", null, marketingPromotionSourceId);
        if (!saveMemberResult.isSuccess()){
            return Result.newError(saveMemberResult.getErrCode(), saveMemberResult.getErrMsg(), true);
        }

        if(StringUtils.isNotBlank(arg.getCtaId())) {
            ThreadPoolUtils.execute(() -> {
                ctaService.recordMemberRegister(MarketingUserActionChannelType.MANKEEP, arg, saveMemberResult.getData());
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        ObjectTagEntity objectTag = objectTagDAO.getObjectTag(ea, arg.getFormId(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
        if (objectTag != null && objectTag.getTagNameList() != null && !objectTag.getTagNameList().isEmpty()) {
            metadataTagManager.addTagsToObjectDatas(ea, CrmObjectApiNameEnum.MEMBER.getName(), Arrays.asList(saveMemberResult.getData()), objectTag.getTagNameList());
        }

        String boundMemberId = wxMiniAppUserMemberBindDao.getMemberIdByUid(ea, uid);
        if (!com.google.common.base.Strings.isNullOrEmpty(boundMemberId)){
            wxMiniAppUserMemberBindDao.deleteByMiniAppUser(ea, uid);
        }
        String memberId = saveMemberResult.getData();
        wxMiniAppUserMemberBindDao.insertIgnore(ea, uid, memberId);
        // 会员存入线索对象
        Optional<String> leadOpt = memberManager.saveMemberToLead(ea, memberId, null, null,
                null, arg.getChannelValue(), marketingPromotionSourceId, arg.getFormId(),null, arg);
        //绑定身份uid&会员&线索
        if (StringUtils.isNotEmpty(uid)) {
            userMarketingAccountRelationManager.bindMiniappUserAndMember(ea, uid, memberId, arg.getSubmitContent().getPhone(), "WxMiniAppUserRegisterOrLogin");
            if (leadOpt.isPresent()) {
                userMarketingAccountRelationManager.bindMiniappUserAndLead(ea, uid, leadOpt.get(), arg.getSubmitContent().getPhone(), "WxMiniAppUserRegisterOrLogin");
            }
        }

        // 将游客身份与员工身份绑定
        boolean bindStaff = staffMiniappUserBindManager.checkAndAddStaffUserBindSync(uid, ea, arg.getSubmitContent().getPhone());
        if (bindStaff) {
            // 需要重新登录
            return Result.newError(SHErrorCode.STAFF_MEMBER_LOGIN_REFRESH_TOKEN, true);
        }
        String password = arg.getSubmitContent().getPassword();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(password)) {
            memberManager.saveMemberPassword(ea, memberId, AESUtil.encode(AESUtil.randomKeyByPassword(memberId), password));
        }
        return Result.newSuccess(true);
    }

    public Result<Boolean> doWxMiniAppUserMemberUpdateInfo(String ea, String uid, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg arg) {
        // 查询会员数据
        String boundMemberId = wxMiniAppUserMemberBindDao.getMemberIdByUid(ea, uid);
        if (StringUtils.isBlank(boundMemberId)) {
            log.warn("MemberServiceImpl.doWxMiniAppUserMemberUpdateInfo boundMemberId is null uid :{}", uid);
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        Result updateResult = memberManager.updateMemberByFormData(ea, boundMemberId, arg);
        return updateResult.isSuccess() ? Result.newSuccess(true) : Result.newError(updateResult.getErrCode(), updateResult.getErrMsg(), true);
    }

    // 这个接口在10.2全网后删掉，担心灰度和全网的签名不一致导致dubbo有问题

    @Override
    public Result<QueryMemberInfoResult> queryMiniAppMemberInfo(String objectId, Integer objectType, String uid) {
        ObjectInfoArg objectInfoArg = new ObjectInfoArg();
        objectInfoArg.setObjectId(objectId);
        objectInfoArg.setObjectType(objectType);
        objectInfoArg.setUid(uid);
        return queryMiniAppMemberInfo(objectInfoArg);
    }

    @Override
    public Result<QueryMemberInfoResult> queryMiniAppMemberInfo(ObjectInfoArg objectInfoArg) {
        String ea = objectInfoArg.getFsEa();
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(objectInfoArg.getObjectId(), objectInfoArg.getObjectType());
        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryMemberInfoResult queryMemberInfoResult = new QueryMemberInfoResult();
        queryMemberInfoResult.setEa(ea);
        queryMemberInfoResult.setEi(eieaConverter.enterpriseAccountToId(ea));
        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(ea, objectInfoArg.getUid());
        boolean isMember = optionalMemberId.isPresent();
        if (!isMember){
            queryMemberInfoResult.setCheckIsMember(false);
            return Result.newSuccess(queryMemberInfoResult);
        }
        String memberId = optionalMemberId.get();
        //查询会员信息
        Optional<QueryMemberInfoResult.MemberInfo> memberInfoOptional = queryMemberInfo(ea, memberId);
        if (memberInfoOptional.isPresent()){
            queryMemberInfoResult.setCheckIsMember(true);
            queryMemberInfoResult.setMemberInfo(memberInfoOptional.get());
        }
        return Result.newSuccess(queryMemberInfoResult);
    }

    @Override
    public Result<QueryMemberInfoResult> queryWxServiceMemberInfo(String wxAppId, String wxOpenId) {
        QueryMemberInfoResult memberInfoResult = new QueryMemberInfoResult();
        ModelResult<String> bindWxEaResult = outerServiceWechatService.transWxAppIdToFsEa(wxAppId, wxOpenId);
        if (!bindWxEaResult.isSuccess() && bindWxEaResult.getResult() != null){
            log.info("MemberServiceImpl.queryWxServiceMemberInfo failed transWxAppIdToFsEa not success wxAppId:{} wxOpenId:{}", wxAppId, wxOpenId);
            return Result.newSuccess(memberInfoResult);
        }
        String ea = bindWxEaResult.getResult();
        if (!memberManager.isOpenMember(ea)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        memberInfoResult.setEa(ea);
        if (!isMember) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        memberInfoResult.setCheckIsMember(true);
        Optional<QueryMemberInfoResult.MemberInfo> memberInfoOptional = queryMemberInfo(ea, optionalMemberId.get());
        if (memberInfoOptional.isPresent()){
            memberInfoResult.setMemberInfo(memberInfoOptional.get());
        }

        return Result.newSuccess(memberInfoResult);
    }

    @Override
    public Result<QueryMemberInfoResult> queryH5MemberInfo(String objectId, Integer objectType, Map<String, String> allMemberCookieInfos) {
        QueryMemberInfoResult queryMemberInfoResult = new QueryMemberInfoResult();
        String ea = objectManager.getObjectEa(objectId, objectType);
        if (!memberManager.isOpenMember(ea)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean isMember = optionalMemberId.isPresent();
        queryMemberInfoResult.setEa(ea);
        if (!isMember){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }

        //查询会员信息
        Optional<QueryMemberInfoResult.MemberInfo> memberInfoOptional = queryMemberInfo(ea, optionalMemberId.get());
        if (memberInfoOptional.isPresent()){
            queryMemberInfoResult.setCheckIsMember(true);
            queryMemberInfoResult.setMemberInfo(memberInfoOptional.get());
        }

        return Result.newSuccess(queryMemberInfoResult);
    }

    @Override
    public Result<PageResult<QueryMemberContentResult>> queryMiniAppMemberContent(QueryMiniAppMemberContentArg arg) {
        PageResult<QueryMemberContentResult> pageResult = new PageResult<>();
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setPageNum(arg.getPageNum() > 100 ? 100 : arg.getPageNum());
        pageResult.setTotalCount(0);
        String ea  = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());

        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(ea, arg.getUid());
        boolean isMember = optionalMemberId.isPresent();
        if (!isMember){
            log.info("MemberService.queryMiniAppMemberContent return null not member arg:{}", arg);
            return Result.newSuccess(pageResult);
        }

        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), true);
        Optional<List<QueryMemberContentResult>> memberConferenceOpt = queryMemberContent(ea,optionalMemberId.get(), page);
        if (!memberConferenceOpt.isPresent()){
            return Result.newSuccess(pageResult);
        }
        pageResult.setResult(memberConferenceOpt.get());
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<QueryMemberContentResult>> queryWxServiceMemberConference(QueryWxServiceMemberConferenceArg arg) {
        PageResult<QueryMemberContentResult> pageResult = new PageResult<QueryMemberContentResult>();
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), true);

        ModelResult<String> bindWxEaResult = outerServiceWechatService.transWxAppIdToFsEa(arg.getWxAppId(), arg.getWxOpenId());
        if (!bindWxEaResult.isSuccess() && bindWxEaResult.getResult() != null){
            log.info("MemberServiceImpl.queryWxServiceMemberInfo failed transWxAppIdToFsEa not success arg:{}", arg);
            return Result.newSuccess(pageResult);
        }

        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(bindWxEaResult.getResult(), arg.getWxAppId(), arg.getWxOpenId());
        boolean isMember = optionalMemberId.isPresent();
        if (!isMember){
            return  Result.newSuccess(pageResult);
        }

        Optional<List<QueryMemberContentResult>> memberConferenceOpt = queryMemberContent(bindWxEaResult.getResult(),optionalMemberId.get(),page);
        if (!memberConferenceOpt.isPresent()){
            return Result.newSuccess(pageResult);
        }
        pageResult.setResult(memberConferenceOpt.get());
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<MiniAppInfoConfigResult> queryMiniAppInfoConfig(String objectId, Integer objectType) {
        String ea = objectManager.getObjectEa(objectId,objectType);
        Preconditions.checkArgument(ea != null);

        MiniAppInfoConfigResult result = new MiniAppInfoConfigResult();
        Optional<String> accountOpt = wechatAccountManager.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID);
        if (!accountOpt.isPresent()){
            log.info("MiniappServiceImpl.queryCustomizeMiniAppInfo return null ea:{}", ea);
            return Result.newError(SHErrorCode.NOT_BIND_CUSTOMIZE_MINIAPP);
        }

        WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(accountOpt.get());
        if (wechatAccountConfigEntity != null){
            result.setAppId(accountOpt.get());
            result.setUserName(wechatAccountConfigEntity.getUserName());
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<BuildMemberInfoByFormResult> buildWxMemberInfoByForm(String formId, String wxAppId, String wxOpenId) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
        BuildMemberInfoByFormResult buildMemberInfoByFormResult = new BuildMemberInfoByFormResult();
        if (customizeFormDataEntity == null) {
            log.warn("MemberServiceImpl.buildWxMemberInfoByForm customizeFormDataEntity is null formId:{}", formId);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(customizeFormDataEntity.getEa(), wxAppId, wxOpenId);
        if (optionalMemberId == null || !optionalMemberId.isPresent() || StringUtils.isBlank(optionalMemberId.get())) {
            log.warn("MemberServiceImpl.buildWxMemberInfoByForm error optionalMemberId is null");
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        Result<Map<String, Object>> resultMap = memberManager.buildMemberInfoByForm(customizeFormDataEntity, optionalMemberId.get());
        if (!resultMap.isSuccess()) {
            return Result.newError(resultMap.getErrCode(), resultMap.getErrMsg());
        }
        buildMemberInfoByFormResult.setCrmFormFieldMap(customizeFormDataEntity.getCrmFormFieldMapV2());
        buildMemberInfoByFormResult.setResultMap(resultMap.getData());
        return Result.newSuccess(buildMemberInfoByFormResult);
    }

    @Override
    public Result<BuildMemberInfoByFormResult> buildH5MemberInfoByForm(String formId, Map<String, String> allMemberCookieInfos) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
        BuildMemberInfoByFormResult buildMemberInfoByFormResult = new BuildMemberInfoByFormResult();
        if (customizeFormDataEntity == null) {
            log.warn("MemberServiceImpl.buildWxMemberInfoByForm customizeFormDataEntity is null formId:{}", formId);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(customizeFormDataEntity.getEa(), allMemberCookieInfos);
        if (optionalMemberId == null || !optionalMemberId.isPresent() || StringUtils.isBlank(optionalMemberId.get())) {
            log.warn("MemberServiceImpl.buildH5MemberInfoByForm error memberId is null");
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        Result<Map<String, Object>> resultMap = memberManager.buildMemberInfoByForm(customizeFormDataEntity, optionalMemberId.get());
        if (!resultMap.isSuccess()) {
            return Result.newError(resultMap.getErrCode(), resultMap.getErrMsg());
        }
        buildMemberInfoByFormResult.setCrmFormFieldMap(customizeFormDataEntity.getCrmFormFieldMapV2());
        buildMemberInfoByFormResult.setResultMap(resultMap.getData());
        return Result.newSuccess(buildMemberInfoByFormResult);
    }

    @Override
    public Result<BuildMemberInfoByFormResult> buildMiniAppMemberInfoByForm(String formId, String uid) {
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(formId);
        BuildMemberInfoByFormResult buildMemberInfoByFormResult = new BuildMemberInfoByFormResult();
        if (customizeFormDataEntity == null) {
            log.warn("MemberServiceImpl.buildMiniAppMemberInfoByForm customizeFormDataEntity is null formId:{}", formId);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(customizeFormDataEntity.getEa(), uid);
        if (optionalMemberId == null || !optionalMemberId.isPresent() || StringUtils.isBlank(optionalMemberId.get())) {
            log.warn("MemberServiceImpl.buildMiniAppMemberInfoByForm error memberId is null");
            return Result.newError(SHErrorCode.NOT_MEMBER_DENY_ACCESS);
        }
        Result<Map<String, Object>> resultMap = memberManager.buildMemberInfoByForm(customizeFormDataEntity, optionalMemberId.get());
        if (!resultMap.isSuccess()) {
            return Result.newError(resultMap.getErrCode(), resultMap.getErrMsg());
        }
        buildMemberInfoByFormResult.setCrmFormFieldMap(customizeFormDataEntity.getCrmFormFieldMapV2());
        buildMemberInfoByFormResult.setResultMap(resultMap.getData());
        return Result.newSuccess(buildMemberInfoByFormResult);
    }

    @Override
    public Result<Boolean> updateDirectLoginAndRegistration(String ea, boolean directLoginAndRegistration) {
        int i = memberConfigDao.updateDirectLoginAndRegistration(ea, directLoginAndRegistration);
        if(i==1){
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Boolean> updateRegisterReview(String ea, boolean registerReview) {
        int i = memberConfigDao.updateRegisterReview(ea, registerReview);
        if(i==1){
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    private  Optional<QueryMemberInfoResult.MemberInfo> queryMemberInfo(String ea, String memberId){
        QueryMemberInfoResult.MemberInfo memberInfo = null;
        // 判断企业是否开通会员
        if (!memberManager.isOpenMember(ea)) {
            return Optional.empty();
        }
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, MemberFieldContants.API_NAME, memberId);
        if (objectData != null) {
            memberInfo = new QueryMemberInfoResult.MemberInfo();
            MemberData crmMemberVo = MemberData.wrap(objectData);
            memberInfo.setMemberId(crmMemberVo.getId());
            memberInfo.setName(crmMemberVo.getName());
            memberInfo.setGrade(crmMemberVo.getGradeIdName());
            memberInfo.setIntegralValue(crmMemberVo.getIntegralValue());

            Object avatarStr = objectData.get(CrmMemberFieldEnum.AVATAR.getApiName());
            if (avatarStr != null) {
                List<QueryMemberInfoResult.AvatarObject> avatarObjectList = GsonUtil.fromJson(GsonUtil.toJson(avatarStr), new TypeToken<List<QueryMemberInfoResult.AvatarObject>>() {
                }.getType());
                if (CollectionUtils.isNotEmpty(avatarObjectList)) {
                    String path = avatarObjectList.get(0).getPath();
                    memberInfo.setAvatar(fileV2Manager.getUrlByPath(path, ea, false));
                }
            }
        }

        return Optional.ofNullable(memberInfo);
    }

    private Optional<List<QueryMemberContentResult>> queryMemberContent(String ea, String memberId, com.github.mybatis.pagination.Page page){
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        if (objectData == null){
            log.info("queryMiniAppMemberConference getMemberDetails failed ea:{} memberId:{} result:{}", ea, memberId, objectData);
            return Optional.empty();
        }
        // 获取该会员报名的所有市场活动
        List<CampaignMarketingEventIdTimeDTO> campaignMarketingEventIdTimeDTOs = campaignMergeDataDAO.getAllMarketingEventIdByMember(ea, (String) objectData.get(CrmMemberFieldEnum.PHONE.getApiName()));
        if (CollectionUtils.isEmpty(campaignMarketingEventIdTimeDTOs)){
            return Optional.empty();
        }
        //只需要展示配置的市场活动类型，过滤出只包含在marketingEventIds中数据
        List<String> marketingEventIds = campaignMarketingEventIdTimeDTOs.stream().map(CampaignMarketingEventIdTimeDTO::getMarketingEventId).collect(Collectors.toList());
        marketingEventIds = getEnrollMarketingEventIdsByEventType(ea, marketingEventIds);
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return Optional.empty();
        }
        List<String> finalMarketingEventIds = marketingEventIds;
        // 获取可见的市场活动，并且根据创建时间倒叙排序
        List<CampaignMarketingEventIdTimeDTO> visibleMarketingEventList = campaignMarketingEventIdTimeDTOs.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId()) && finalMarketingEventIds.contains(e.getMarketingEventId()))
                .sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(visibleMarketingEventList)) {
            return Optional.empty();
        }
        // 内存分页
        List<CampaignMarketingEventIdTimeDTO> pageMarketingEventData = visibleMarketingEventList.stream().skip(page.getOffset()).limit(page.getLimit()).collect(Collectors.toList());
        page.setTotalNum(visibleMarketingEventList.size());
        if (CollectionUtils.isEmpty(pageMarketingEventData)) {
            return Optional.empty();
        }
        List<String> pageMarketingEventIdList = pageMarketingEventData.stream().map(CampaignMarketingEventIdTimeDTO::getMarketingEventId).collect(Collectors.toList());
        List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventData(ea, -10000, pageMarketingEventIdList);
        if (CollectionUtils.isEmpty(marketingEventDataList)){
            return Optional.empty();
        }
        Optional<List<QueryMemberContentResult>> optional = memberManager.queryMemberContentList(ea, marketingEventDataList, memberId, (String)objectData.get(CrmMemberFieldEnum.PHONE.getApiName()));
        Map<String, CampaignMarketingEventIdTimeDTO> marketingEventIdTimeDTOMap = visibleMarketingEventList.stream().collect(Collectors.toMap(CampaignMarketingEventIdTimeDTO::getMarketingEventId, e -> e));

        optional.ifPresent(queryMemberContentResults -> queryMemberContentResults.forEach(e -> {
            CampaignMarketingEventIdTimeDTO dto = marketingEventIdTimeDTOMap.get(e.getMarketingEventId());
            if (dto != null) {
                e.setSubmitTime(dto.getCreateTime().getTime());
            }
        }));
        return optional;
    }

    private List<String> getEnrollMarketingEventIdsByEventType(String ea, List<String> marketingEventIds){
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return null;
        }

        List<String> marketingEventTypeList = null;
        MemberEnrollMarketingEventDisplayTypeConfigEntity configEntity = memberEnrollMarketingEventDisplayTypeConfigDAO.getByEa(ea);
        if (configEntity != null){
            List<MemberEnrollMarketingEventDisplayTypeConfigItem> configItemList = JSON.parseArray(configEntity.getEventTypeDisplay(), MemberEnrollMarketingEventDisplayTypeConfigItem.class);
            if (CollectionUtils.isNotEmpty(configItemList)){
                marketingEventTypeList = configItemList.stream().map(MemberEnrollMarketingEventDisplayTypeConfigItem::getEventTypeValue).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(marketingEventTypeList)){
            //未初始化的时候，默认展示会议和直播的报名数据
            marketingEventTypeList = Lists.newArrayList("live_marketing", "3");
        }

        int pageSize = 1000;
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
        paasQueryArg.addFilter("event_type", PaasAndCrmOperatorEnum.IN.getCrmOperator(), marketingEventTypeList);
        paasQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), marketingEventIds);
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        queryFilterArg.setSelectFields(selectFields);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            return null;
        }
        int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
        List<String> ids = Lists.newArrayList();
        for(int i = 1; i <= totalPage; i++) {
            InnerPage<ObjectData> pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, i, pageSize);
            if (pageObjects == null || org.apache.commons.collections.CollectionUtils.isEmpty(pageObjects.getDataList())) {
                break;
            }
            ids.addAll(pageObjects.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
        }

        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<MemberSpreadResult> memberSpread(MemberSpreadArg arg) {

        if (StringUtils.isBlank(arg.getEa()) &&  (StringUtils.isBlank(arg.getSpreadObjectId()) || arg.getSpreadObjectType() == null) || StringUtils.isBlank(arg.getMarketingActivityId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(arg.getSpreadObjectId(), arg.getSpreadObjectType());
        }
        String objectName = objectManager.getObjectName(arg.getSpreadObjectId(), arg.getSpreadObjectType());
        if (StringUtils.isBlank(objectName)) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND);
        }
        if (StringUtils.isBlank(ea)) {
            log.info("MemberServiceImpl.memberSpread failed to get ea arg:[{}]", arg);
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MEMBERSERVICEIMPL_1773));
        }
        arg.setEa(ea);
        String memberId = getMemberInfo(arg);
        if (StringUtils.isBlank(memberId)) {
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
        }
        MarketingActivitySpreadRecordEntity existEntity = marketingActivitySpreadRecordDAO.queryByMarketingActivityIdAndMemberId(ea, arg.getMarketingActivityId(), memberId);
        if (existEntity != null) {
            MemberSpreadResult result = new MemberSpreadResult();
            result.setMarketingActivityId(existEntity.getMarketingActivityId());
            return Result.newSuccess(result);
        }
        String marketingActivityId = arg.getMarketingActivityId();
        MarketingActivityObjectRelationEntity entity = new MarketingActivityObjectRelationEntity();
        entity.setMarketingActivityId(marketingActivityId);
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setObjectId(arg.getSpreadObjectId());
        entity.setObjectType(arg.getSpreadObjectType());
        // 这里是没有市场活动的，填充一个假的
        entity.setMarketingEventId("1");
        marketingActivityObjectRelationDAO.insert(entity);

        MarketingActivitySpreadRecordEntity marketingActivitySpreadRecordEntity = new MarketingActivitySpreadRecordEntity();
        marketingActivitySpreadRecordEntity.setId(UUIDUtil.getUUID());
        marketingActivitySpreadRecordEntity.setEa(ea);
        marketingActivitySpreadRecordEntity.setUserId(SuperUserConstants.USER_ID);
        marketingActivitySpreadRecordEntity.setMarketingActivityId(marketingActivityId);
        marketingActivitySpreadRecordEntity.setMemberId(memberId);
        marketingActivitySpreadRecordDAO.addMarketingActivitySpreadRecord(marketingActivitySpreadRecordEntity);
        MemberSpreadResult result = new MemberSpreadResult();
        result.setMarketingActivityId(marketingActivityId);
        return Result.newSuccess(result);
    }

    /**
     * 会员进入物料的时候就会调用这个接口，生成一个营销活动，然后会员在分享之后，前端会调用memberSpread接口，把营销活动ID传过来
     * 为什么不在分享的时候再创建营销活动的，因为前端那边微信分享的api，这个分享连接地址不能监听点击事件，所以要先生成，然后再调用memberSpread接口
     */
    @Override
    public Result<MemberSpreadResult> createMarketingActivity(MemberSpreadArg arg) {
        if (StringUtils.isBlank(arg.getEa()) &&  (StringUtils.isBlank(arg.getSpreadObjectId()) || arg.getSpreadObjectType() == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(arg.getSpreadObjectId(), arg.getSpreadObjectType());
        }
        String objectName = objectManager.getObjectName(arg.getSpreadObjectId(), arg.getSpreadObjectType());
        if (StringUtils.isBlank(objectName)) {
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_BINDING_OBJECT_NOT_FOUND);
        }

        if (StringUtils.isBlank(ea)) {
            log.info("MemberServiceImpl.memberSpread failed to get ea arg:[{}]", arg);
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MEMBERSERVICEIMPL_1773));
        }

        PaasAddMarketingActivityArg paasAddMarketingActivityArg = new PaasAddMarketingActivityArg();
        paasAddMarketingActivityArg.setName(objectName);
        paasAddMarketingActivityArg.setStatus("2");
        paasAddMarketingActivityArg.setSpreadType(String.valueOf(MarketingActivitySpreadTypeEnum.CUSTOMER_SPREAD.getSpreadType()));
        paasAddMarketingActivityArg.setWxAppId(arg.getWxAppId());
        Result<String> marketingActivityIdResult = marketingActivityCrmManager.addMarketingActivity(ea, SuperUserConstants.USER_ID, paasAddMarketingActivityArg);
        if (!marketingActivityIdResult.isSuccess()) {
            return Result.newError(SHErrorCode.KIS_MARKETING_ACTIVITY_ADD_FAILED);
        }
        MemberSpreadResult spreadResult = new MemberSpreadResult();
        spreadResult.setMarketingActivityId(marketingActivityIdResult.getData());
        return Result.newSuccess(spreadResult);
    }

    @Override
    public Result<PageResult<MemberSpreadDetail>> memberSpreadList(MemberSpreadListArg arg) {
        String ea = arg.getEa();
        if (StringUtils.isBlank(ea) && (StringUtils.isBlank(arg.getObjectId()) || arg.getObjectType() == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MEMBERSERVICEIMPL_1773));
        }
        arg.setEa(ea);
        String memberId = getMemberInfo(arg);
        if (StringUtils.isBlank(memberId)) {
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
        }

        com.github.mybatis.pagination.Page<MarketingActivitySpreadRecordEntity> page =
                new com.github.mybatis.pagination.Page<>(arg.getPageNo(), arg.getPageSize(), true);
        List<MarketingActivitySpreadRecordEntity> memberSpreadList = marketingActivitySpreadRecordDAO.queryPageByMemberId(ea, memberId, page);

        PageResult<MemberSpreadDetail> pageResult = new PageResult<>();
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setPageNum(arg.getPageNo());
        pageResult.setPageSize(arg.getPageSize());
        List<MemberSpreadDetail> memberSpreadDetailList = Lists.newArrayList();
        pageResult.setResult(memberSpreadDetailList);

        if (CollectionUtils.isEmpty(memberSpreadList)) {
            return Result.newSuccess(pageResult);
        }
        List<CustomizeFormClueNumDTO> clueNumList = null;
        List<String> marketingActivityIdList = memberSpreadList.stream().map(MarketingActivitySpreadRecordEntity::getMarketingActivityId).collect(Collectors.toList());
        //获取营销用户id
        Result<String> userMarketingAccountResult = userMarketingAccountService.getUserMarketingAccountByMemberId(ea, -10000, memberId);
        if(userMarketingAccountResult.isSuccess() && StringUtils.isNotEmpty(userMarketingAccountResult.getData())){
            clueNumList = customizeFormDataUserDAO.countByMarketingActivityIdsAndFromUserMarketingId(ea, marketingActivityIdList, userMarketingAccountResult.getData());
        }else {
            return Result.newSuccess();
        }

        Map<String, Integer> marketingActivityClueNumMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(clueNumList)) {
            clueNumList.forEach(e -> marketingActivityClueNumMap.put(e.getMarketingActivityId(), e.getCount()));
        }

        Map<String, MarketingActivityObjectRelationEntity> marketingActivityObjectRelationEntityMap = Maps.newHashMap();
        List<MarketingActivityObjectRelationEntity> marketingActivityObjectRelationEntityList = marketingActivityObjectRelationDAO.queryByMarketingActivityIdList(ea, marketingActivityIdList);
        if (CollectionUtils.isNotEmpty(marketingActivityObjectRelationEntityList)) {
            marketingActivityObjectRelationEntityList.forEach(e -> marketingActivityObjectRelationEntityMap.put(e.getMarketingActivityId(), e));
        }
        for (MarketingActivitySpreadRecordEntity entity : memberSpreadList) {
            MemberSpreadDetail memberSpreadDetail = new MemberSpreadDetail();
            memberSpreadDetail.setMarketingActivityId(entity.getMarketingActivityId());
            memberSpreadDetail.setLeadAccumulationCount(marketingActivityClueNumMap.getOrDefault(entity.getMarketingActivityId(), 0));
            MarketingActivityObjectRelationEntity objectRelationEntity = marketingActivityObjectRelationEntityMap.get(entity.getMarketingActivityId());
            if (objectRelationEntity != null) {
                buildObjectResult(ea, objectRelationEntity.getObjectType(), objectRelationEntity.getObjectId(), memberSpreadDetail);
            }
            // 这里值前端需要 不要去掉
            memberSpreadDetail.setDataType(3);
            memberSpreadDetailList.add(memberSpreadDetail);
        }

        //最终查询的物料id
        List<String> resultIds = Lists.newArrayList();
        //海报的物料id
        List<String> qrIds = Lists.newArrayList();
        for (MemberSpreadDetail memberSpreadDetail : memberSpreadDetailList) {
            if(memberSpreadDetail.getObjectType()==ObjectTypeEnum.QR_POSTER.getType()){
                qrIds.add(memberSpreadDetail.getObjectId());
            }else {
                resultIds.add(memberSpreadDetail.getObjectId());
            }
        }
        if(CollectionUtils.isNotEmpty(qrIds)){
            List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(qrIds);
            List<String> qrPostTargetId = qrPosterEntities.stream().filter(o-> StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
            resultIds.addAll(qrPostTargetId);
        }


        BatchObjectUserMarketingStatisticsArg statisticsArg = new BatchObjectUserMarketingStatisticsArg();
        statisticsArg.setEa(ea);
        statisticsArg.setMarketingActivityIdList(marketingActivityIdList);
        statisticsArg.setObjectIds(resultIds);
        statisticsArg.setFromUserMarketingId(userMarketingAccountResult.getData());
        Result<List<ObjectUserMarketingStatisticsResult>> statisticsResult = statisticService.batchGetRadarObjectMarketingActivityStatistics(statisticsArg);


        Map<String, ObjectUserMarketingStatisticsResult> marketingActivityIdToStatisticsMap = Maps.newHashMap();
        if (statisticsResult.isSuccess() && org.apache.commons.collections.CollectionUtils.isNotEmpty(statisticsResult.getData())) {
            statisticsResult.getData().forEach(e -> marketingActivityIdToStatisticsMap.put(e.getMarketingActivityId(), e));
        }

        for (MemberSpreadDetail entity : memberSpreadDetailList) {
            int forwardCount = 0;
            int lookUpCount = 0;
            int forwardUserCount = 0;
            int lookUpUserCount = 0;
            ObjectUserMarketingStatisticsResult statisticsData = marketingActivityIdToStatisticsMap.get(entity.getMarketingActivityId());
            if (statisticsData != null) {
                forwardCount = statisticsData.getForwardCount();
                lookUpCount = statisticsData.getLookUpCount();
                forwardUserCount = statisticsData.getForwardUserCount();
                lookUpUserCount = statisticsData.getLookUpUserCount();
            }
            entity.setForwardCount(forwardCount);
            entity.setLookUpCount(lookUpCount);
            entity.setForwardUserCount(forwardUserCount);
            entity.setLookUpUserCount(lookUpUserCount);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<com.facishare.marketing.api.result.PageResult<UserMarketingActionResult>> pageUserMarketingActionStatistic(MemberUserMarketingActionArg arg) {
        String ea = arg.getEa();
        if (StringUtils.isBlank(ea) && (StringUtils.isBlank(arg.getObjectId()) || arg.getObjectType() == null)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MEMBERSERVICEIMPL_1773));
        }
        arg.setEa(ea);
        String memberId = getMemberInfo(arg);
        if (StringUtils.isBlank(memberId)) {
            return Result.newError(SHErrorCode.MEMBER_ACCOUNT_NOT_EXISTED);
        }
        PageUserMarketingActionStatisticArg actionStatisticArg = new PageUserMarketingActionStatisticArg();
        actionStatisticArg.setSpreadMemberId(memberId);
        actionStatisticArg.setPageNo(arg.getPageNo());
        actionStatisticArg.setPageSize(arg.getPageSize());
        return userMarketingAccountService.pageUserMarketingActionStatistic(ea, SuperUserConstants.USER_ID, actionStatisticArg);
    }

    @Override
    public Result<MemberCheckResult> checkWxServiceUserHaveMemberResultAuth(Integer objectType, String objectId, String wxAppId, String wxOpenId, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = memberManager.getWxServiceUserBindMemberId(ea, wxAppId, wxOpenId);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus) || Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_SUCCESS.getType()){
                    MemberCheckResult memberCheckResult = MemberCheckResult.warp(objectData);
                    return Result.newSuccess(memberCheckResult);
                }
            }
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<MemberCheckResult> checkH5UserHaveMemberResultAuth(Integer objectType, String objectId, Map<String, String> allMemberCookieInfos, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(ea, allMemberCookieInfos);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus) || Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_SUCCESS.getType()){
                    MemberCheckResult memberCheckResult = MemberCheckResult.warp(objectData);
                    return Result.newSuccess(memberCheckResult);
                }
            }
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<MemberCheckResult> checkWxMiniAppUserHaveMemberResultAuth(Integer objectType, String objectId, String uid, boolean checkObjectAccess) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        Optional<String> optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(ea, uid);
        boolean isMember = optionalMemberId.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(Strings.isNullOrEmpty(approvalStatus) || Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_SUCCESS.getType()){
                    MemberCheckResult memberCheckResult = MemberCheckResult.warp(objectData);
                    return Result.newSuccess(memberCheckResult);
                }
            }
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, ea)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<MemberEnrollResult> checkPartnerUserInMarketingEvent(Integer objectType, String objectId, String erUpstreamEa, String erOuterTenantId, String erOuterUid, String marketingEventId) {
        MemberEnrollResult memberEnrollResult = new MemberEnrollResult();
        memberEnrollResult.setMemberEnroll(false);
        //查询互联用户的手机号
        String phone = authPartnerManager.getOuterUserPhone(erUpstreamEa, erOuterUid);
        if (org.apache.commons.lang3.StringUtils.isBlank(phone)) {
            log.warn("authPartnerManager.getOuterUserPhone is null objectType:{}, objectId:{}, erUpstreamEa:{}, erOuterUid:{}, marketingEventId:{}", objectType, objectId, erUpstreamEa, erOuterUid, marketingEventId);
            return Result.newSuccess(memberEnrollResult);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(erUpstreamEa, phone);
        boolean isMember = optionalMember.isPresent();
        if (!isMember) {
            memberManager.checkActivityEnrollReview(erUpstreamEa, marketingEventId,memberEnrollResult);
           return Result.newSuccess(memberEnrollResult);
        }
        Result<Boolean> memberEnroll = checkMemberInMarketingEvent(erUpstreamEa, optionalMember.get().getId(), marketingEventId);
        if (!memberEnroll.isSuccess()) {
            return Result.newError(memberEnroll.getErrCode(), memberEnroll.getErrMsg());
        }
        if (memberEnroll.getData() != null) {
            memberEnrollResult.setMemberEnroll(memberEnroll.getData());
        }
        memberManager.checkEnrollReviewStatus(erUpstreamEa, marketingEventId,optionalMember.get().getId(),memberEnrollResult);
        return Result.newSuccess(memberEnrollResult);

    }

    @Override
    public Result<String> checkPartnerUserHaveMemberAuthReview(Integer objectType, String objectId, String erUpstreamEa, String erOuterTenantId, String erOuterUid, boolean checkObjectAccess) {
        //查询互联用户的手机号
        String phone = authPartnerManager.getOuterUserPhone(erUpstreamEa, erOuterUid);
        if (StringUtils.isBlank(phone)) {
            log.warn("authPartnerManager.getOuterUserPhone is null");
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        if (!memberManager.isOpenMember(erUpstreamEa)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(erUpstreamEa, phone);
        boolean isMember = optionalMember.isPresent();
        if (isMember){
            ObjectData objectData = crmV2Manager.getDetail(erUpstreamEa, -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMember.get().getId());
            if (objectData != null){
                String approvalStatus =(String) objectData.get("approval_status");
                //历史数据+导入数据审核状态为空默认放行
                if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                        ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
                    return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
                }
            }
            return Result.newSuccess(optionalMember.get().getId());
        }
        if(checkObjectAccess && memberManager.isObjectNotNeedMemberAuth(objectType, objectId, erUpstreamEa)){
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.NOT_MEMBER);
    }

    @Override
    public Result<QueryMemberInfoResult> queryPartnerMemberInfo(String objectId, Integer objectType, String erUpstreamEa, String erOuterTenantId, String erOuterUid) {
        QueryMemberInfoResult queryMemberInfoResult = new QueryMemberInfoResult();
        //查询互联用户的手机号
        String phone = authPartnerManager.getOuterUserPhone(erUpstreamEa, erOuterUid);
        if (StringUtils.isBlank(phone)) {
            log.warn("authPartnerManager.getOuterUserPhone is null");
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        if (!memberManager.isOpenMember(erUpstreamEa)) {
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        Optional<ObjectData> optionalMember = memberManager.getMemberByEaAndPhone(erUpstreamEa, phone);
        boolean isMember = optionalMember.isPresent();
        queryMemberInfoResult.setEa(erUpstreamEa);
        if (!isMember){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        //查询会员信息
        Optional<QueryMemberInfoResult.MemberInfo> memberInfoOptional = queryMemberInfo(erUpstreamEa, optionalMember.get().getId());
        if (memberInfoOptional.isPresent()){
            queryMemberInfoResult.setCheckIsMember(true);
            queryMemberInfoResult.setMemberInfo(memberInfoOptional.get());
        }
        return Result.newSuccess(queryMemberInfoResult);
    }

    @Override
    public Result<Boolean> checkMemberLoyaltyPlanEnabled(String ea) {
        return Result.newSuccess(marketingPluginConfigManager.getCurrentPluginStatus(ea, MarketingPluginTypeEnum.MEMBER_LOYALTY_PLAN_PLUGIN.getType()));
    }

    //查询会员登录的加密token,
    @Override
    public Result<String> queryMemberLoginEncryToken(String ea, String uid, String memberId) {
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        if (objectData == null){
            return Result.newError(SHErrorCode.NOT_MEMBER);
        }
        String approvalStatus =(String) objectData.get("approval_status");
        //历史数据+导入数据审核状态为空默认放行
        if(!Strings.isNullOrEmpty(approvalStatus) && (Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.NOT_REVIEW_SUCCESS.getType()
                ||Integer.parseInt(approvalStatus)==MemberApprovalStatusEnum.REVIEW_WAITING.getType())){
            return Result.newError(SHErrorCode.MEMBER_REVIEW_WAITING_OR_FAIL);
        }

        //加密
        Map<String, Object> params = new HashMap<>();
        params.put("ea", ea);
        params.put("memberId", memberId);
        params.put("timestamp", System.currentTimeMillis());
        String token = TokenUtil.generateCommonToken(params);

        return Result.newSuccess(token);
    }

    private String getMemberInfo(MemberBaseArg arg) {
        Optional<String> optionalMemberId = null;
        if (IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.getType() == arg.getIdentityCheckType()) {
            optionalMemberId = memberManager.getWxServiceUserBindMemberId(arg.getEa(), arg.getWxAppId(), arg.getOpenId());
        } else if (IdentityCheckTypeEnum.BROWSER_FINGERPRINT.getType() == arg.getIdentityCheckType()) {
            optionalMemberId = memberManager.getH5LoginMemberId(arg.getEa(), arg.getMemberCookieMap());

        } else {
            optionalMemberId = memberManager.getWxMiniAppUserBindMemberId(arg.getEa(), arg.getUid());
        }
        return optionalMemberId.orElse(null);
    }

    private void buildObjectResult(String ea, Integer objectType, String objectId, MemberSpreadDetail result) {
        result.setObjectId(objectId);
        result.setObjectType(objectType);
        String title = null;
        String coverUrl = null;
        if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
            ProductEntity productEntity = new ProductEntity();
            productEntity.setId(objectId);
            productEntity = objectManager.visit(productEntity);
            title = productEntity.getName();
            coverUrl = productEntity.getHeadImg();
        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
            ArticleEntity articleEntity = new ArticleEntity();
            articleEntity.setId(objectId);
            articleEntity = objectManager.visit(articleEntity);
            title = articleEntity.getTitle();
            coverUrl = articleEntity.getPhotoUrl();
        } else if (objectType == ObjectTypeEnum.QR_POSTER.getType()) {
            QRPosterEntity qrPosterEntity = new QRPosterEntity();
            qrPosterEntity.setId(objectId);
            qrPosterEntity = objectManager.visit(qrPosterEntity);
            title = qrPosterEntity.getTitle();
            coverUrl = fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), null, false);
        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
            Map<String, HexagonBaseInfoDTO> hexagonBaseInfoDTOMap = hexagonManager.getHexagonBaseInfoById(Lists.newArrayList(objectId), ea);
            if (hexagonBaseInfoDTOMap != null && hexagonBaseInfoDTOMap.get(objectId) != null) {
                HexagonBaseInfoDTO hexagonBaseInfoDTO = hexagonBaseInfoDTOMap.get(objectId);
                coverUrl = hexagonBaseInfoDTO.getCoverUrl();
                title = hexagonBaseInfoDTO.getTitle();
            }
        }
        result.setCoverUrl(coverUrl);
        result.setTitle(title);
        result.setObjectTitle(title);
    }
}