package com.facishare.marketing.provider.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

@Data
@Entity
public class AccountEntity implements Serializable {
    private String uid;
    private String openid;
    private String phone;
    private Date createTime;
    private Date lastModifyTime;
    private Integer accountType;
    private String appId;

    public AccountEntity() {
    }

    public AccountEntity(String uid, String openid, String phone) {
        this.uid = uid;
        this.openid = openid;
        this.phone = phone;
    }
}
