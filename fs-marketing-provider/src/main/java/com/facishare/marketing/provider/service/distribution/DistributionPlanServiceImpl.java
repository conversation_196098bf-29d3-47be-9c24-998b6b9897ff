package com.facishare.marketing.provider.service.distribution;

import com.facishare.marketing.api.result.distribution.GetDistributionPlanInfoResult;
import com.facishare.marketing.api.service.distribution.DistributionPlanService;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.DistributionPlanDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.distribution.ClueCountEntity;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorCountEntity;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.distribution.ClueManager;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/05/09
 **/
@Service("distributionPlanService")
@Slf4j
public class DistributionPlanServiceImpl implements DistributionPlanService {

    @Autowired
    private DistributionPlanDAO distributionPlanDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private DistributorDao distributorDao;
    @Autowired
    private ClueDAO clueDAO;
    @Autowired
    private ClueManager clueManager;

    @Override
    public Result<GetDistributionPlanInfoResult> getDistributionInfo(String distributionPlanId) {
        GetDistributionPlanInfoResult result = new GetDistributionPlanInfoResult();
        result.setPhotoList(Lists.newArrayList());
        DistributePlanEntity distributePlanEntity = distributionPlanDAO.getDistributePlanById(distributionPlanId);
        if (distributePlanEntity == null) {
            log.warn("DistributorServiceImpl.getDistributionInfo distributePlanEntity is null distributionPlanId:{}", distributionPlanId);
            return Result.newError(SHErrorCode.DISTRIBUTE_PLAN_ONT_FOUND);
        }
        result.setPlanTitle(distributePlanEntity.getPlanTitle());
        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.DISTRIBUTION_PLAN_DETAIL.getType(), distributionPlanId);
        if (CollectionUtils.isEmpty(photoEntityList)) {
            log.warn("DistributorServiceImpl.getDistributionInfo photoEntityList is empty distributionPlanId:{}", distributionPlanId);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        result.setPhotoList(photoEntityList.stream().map(PhotoEntity::getThumbnailUrl).collect(Collectors.toList()));

        List<String> planIds = Lists.newArrayList(distributionPlanId);
        List<DistributorCountEntity> distributorCountList = distributorDao.getDistributorCount(planIds);
        if (CollectionUtils.isNotEmpty(distributorCountList)) {
            result.setDistributorNum(distributorCountList.get(0).getDistributorCount());
        } else {
            result.setDistributorNum(0);
        }
        List<ClueCountEntity> clueCountList = clueDAO.getClueCount(planIds);
        if (CollectionUtils.isNotEmpty(clueCountList)) {
            result.setClueNum(clueCountList.get(0).getClueCount());
        } else {
            result.setClueNum(0);
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result synDistributePlan() {
        ThreadPoolUtils.execute(() -> {
            clueManager.synDistributePlan();
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }
}
