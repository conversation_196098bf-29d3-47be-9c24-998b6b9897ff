/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.marketingactivity;

import com.facishare.marketing.api.service.cta.CtaService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import static com.facishare.marketing.provider.manager.permission.DataPermissionManager.defaultAllChannel;
import static com.facishare.marketing.provider.util.Constant.A_WAREHOUSE_TYPE;
import static com.facishare.marketing.provider.util.Constant.TEMP_N_WAREHOUSE_TYPE;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.FieldValueResult;
import com.facishare.marketing.api.arg.ListWechatFansArg;
import com.facishare.marketing.api.arg.PubPlatAuthComponentArg;
import com.facishare.marketing.api.arg.SendTemplateMessageArg;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.arg.marketingactivity.GetTagsArg;
import com.facishare.marketing.api.arg.marketingactivity.QueryCanFansCountArg;
import com.facishare.marketing.api.arg.wx.AddWxAutoReplyRuleArg;
import com.facishare.marketing.api.arg.wx.ListWxAutoReplyRuleArg;
import com.facishare.marketing.api.arg.wx.UpdateWxAutoReplyRuleArg;
import com.facishare.marketing.api.arg.wx.UpsertWxFollowAutoReplyRuleArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.marketingactivity.GetTagsResult;
import com.facishare.marketing.api.result.marketingactivity.QueryCanFansCountResult;
import com.facishare.marketing.api.result.wxPublicPlatform.WxPublicPlatformAuthorizeComponentResult;
import com.facishare.marketing.api.result.wxPublicPlatform.WxPublicPlatformAuthorizeUserInfo;
import com.facishare.marketing.api.result.wxPublicPlatform.WxPublicPlatformCheckFocus;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.marketingactivity.WeChatServiceMarketingActivityService;
import com.facishare.marketing.api.vo.WeChatServiceResult;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.MarketingException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldValue;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.ReplaceUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.result.MaterialWxPresentMsg;
import com.facishare.marketing.outapi.service.MaterialService;
import com.facishare.marketing.provider.dao.H5AccessPermissionsSeetingDao;
import com.facishare.marketing.provider.dao.MarketingWxServiceDao;
import com.facishare.marketing.provider.dao.WxPublicPlatformAuthorizeComponentDao;
import com.facishare.marketing.provider.dao.wx.WxAutoReplyRuleDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.MarketingWxServiceEntity;
import com.facishare.marketing.provider.entity.WxPublicPlatformAuthorizeComponentEntity;
import com.facishare.marketing.provider.entity.wx.WxAutoReplyRuleEntity;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerData.ResponseMsg;
import com.facishare.marketing.provider.innerData.ResponseMsgList;
import com.facishare.marketing.provider.manager.FanTagManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.MarketingUserGroupManager;
import com.facishare.marketing.provider.manager.OuterServiceWechatManager;
import com.facishare.marketing.provider.manager.OuterTagSynchronizationManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.manager.WeChatServiceNoticeManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.marketingactivity.WeChatServiceManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.service.marketingplugin.DataPermissionPluginService;
import com.facishare.marketing.provider.util.ConvertUtil;
import com.facishare.open.app.center.api.model.OpenAppDO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.api.service.OpenAppService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.wechat.dubborestouterapi.data.WeChatAuthBaseUserInfo;
import com.facishare.wechat.dubborestouterapi.data.WeChatAuthUserInfo;
import com.facishare.wechat.dubborestouterapi.data.WeChatMenuFormData;
import com.facishare.wechat.dubborestouterapi.result.GetComponentAppIdResult;
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatPlatformConfigRestService;
import com.facishare.wechat.dubborestouterapi.service.union.EaBindInfoRestService;
import com.facishare.wechat.dubborestouterapi.service.union.WeChatCustomerMenuRestService;
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatUserInfoRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.constants.ActionType;
import com.facishare.wechat.proxy.model.vo.BatchTemplateMessageVo;
import com.facishare.wechat.proxy.model.vo.TemplateMessageVo;
import com.facishare.wechat.proxy.service.WechatMessageService;
import com.facishare.wechat.sender.api.vo.NoticeCanSendCustomerCountVo;
import com.facishare.wechat.union.common.enums.SendRangeEnum;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.facishare.wechat.union.core.api.model.vo.FanTagVO;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.WechatFanFieldContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.common.data.WechatFanData;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.MetadataTagDataService;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/19.
 */
@Slf4j
@Service("weChatServiceMarketingActivityService")
public class WeChatServiceMarketingActivityServiceImpl implements WeChatServiceMarketingActivityService {
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private WeChatServiceNoticeManager weChatServiceNoticeManager;
    @Autowired
    private WeChatServiceManager weChatServiceManager;
    @Autowired
    private FanTagManager fanTagManager;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private OpenAppService openAppService;
    @Autowired
    private IntegralServiceManager integralServiceManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private MetadataTagDataService metadataTagDataService;
    @Autowired
    @Qualifier("wechatFanOuterTagSynchronizationManager")
    private OuterTagSynchronizationManager wechatFanOuterTagSynchronizationManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private MemberService memberService;
    @Autowired
    private WechatMessageService wechatMessageService;
    @Autowired
    private WxAutoReplyRuleDao wxAutoReplyRuleDao;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private MaterialService materialService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private WeChatCustomerMenuRestService weChatCustomerMenuRestService;
    @Autowired
    private WxPublicPlatformAuthorizeComponentDao wxPublicPlatformAuthorizeComponentDao;
    @Autowired
    private EaBindInfoRestService eaBindInfoRestService;
    @Autowired
    private WechatUserInfoRestService wechatUserInfoRestService;
    @Autowired
    private WechatPlatformConfigRestService wechatPlatformConfigRestService;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;

    @Autowired
    private H5AccessPermissionsSeetingDao h5AccessPermissionsSeetingDao;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private OpenAppAdminService openAppAdminService;

    @Autowired
    private DataPermissionPluginService dataPermissionPluginService;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    public static final long EXPIRE_TIME = 3L; // 超时时间为3秒钟
    @Autowired
    private CtaService ctaService;

    @Override
    public Result<String> upsertWxFollowAutoReplyRule(String ea, Integer fsUserId, UpsertWxFollowAutoReplyRuleArg arg) {
        if (!arg.isArgValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ResponseMsgList responseMsgList = JsonUtil.fromJson(arg.getResponseMsg(), ResponseMsgList.class);
        if (CollectionUtils.isEmpty(responseMsgList)) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.upsertWxFollowAutoReplyRule responseMsg is error responseMsgList:{} arg:{}", responseMsgList, arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        handleWxAutoReplyImagePath(responseMsgList, ea, fsUserId);
        arg.setResponseMsg(fillMaterialInfoToResponseMsgList(ea, responseMsgList));
        arg.setResponseMsg(ReplaceUtil.replaceWxAppId(arg.getResponseMsg(), arg.getWxAppId()));
        List<WxAutoReplyRuleEntity> wxFollowAutoReplyRule = wxAutoReplyRuleDao.getWxAutoReplyRuleEntitiesByEaAndWxAppIdAndActionTypeAndStatus(ea, arg.getWxAppId(), ActionType.SUBSCRIBE.getTypeCode(), null, null, null);
        if (CollectionUtils.isEmpty(wxFollowAutoReplyRule)) {
            // 如果不存在关注后自动回复则插入数据，防止并发使用分布式锁。并发量较小，不重试
            String id = UUIDUtil.getUUID();
            String lockKey = ea + arg.getWxAppId();
            try {
                boolean lockResult = redisManager.lock(lockKey, EXPIRE_TIME);
                if (!lockResult) {
                    Result.newError(SHErrorCode.OPERATE_DB_FAIL);
                }
                wxAutoReplyRuleDao.insertFollowEntity(id, ea, arg.getWxAppId(), arg.getResponseMsg(), arg.getStatus());
            } catch (Exception exception) {
                log.warn("WeChatServiceMarketingActivityServiceImpl.upsertWxFollowAutoReplyRule lock error:", exception);
                return Result.newError(SHErrorCode.SERVER_BUSY);
            } finally {
                redisManager.unLock(lockKey);
            }
            return Result.newSuccess(id);
        } else {
            WxAutoReplyRuleEntity wxAutoReplyRuleEntity = wxFollowAutoReplyRule.get(0);
            wxAutoReplyRuleEntity.setResponseMsg(arg.getResponseMsg());
            wxAutoReplyRuleEntity.setStatus(arg.getStatus());
            boolean result = wxAutoReplyRuleDao.updateEntityByIdAndEa(wxAutoReplyRuleEntity);
            return result ? Result.newSuccess(wxAutoReplyRuleEntity.getId()) : Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
    }

    @Override
    public Result<String> addWxAutoReplyRule(String ea, Integer fsUserId, AddWxAutoReplyRuleArg arg) {
        if (!arg.isArgValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ResponseMsgList responseMsgList = JsonUtil.fromJson(arg.getResponseMsg(), ResponseMsgList.class);
        if (CollectionUtils.isEmpty(responseMsgList) || !responseMsgList.stream().allMatch(ResponseMsg::isResponseMsgValid)) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.addWxAutoReplyRule responseMsg is error responseMsgList:{} arg:{}", responseMsgList, arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        handleWxAutoReplyImagePath(responseMsgList, ea, fsUserId);
        arg.setResponseMsg(fillMaterialInfoToResponseMsgList(ea, responseMsgList));
        WxAutoReplyRuleEntity entity = new WxAutoReplyRuleEntity();
        BeanUtils.copyProperties(arg, entity, "validFrom", "validTo");
        String id = UUIDUtil.getUUID();
        entity.setId(id);
        entity.setEa(ea);
        entity.setValidFrom(arg.getValidFrom() == null ? null : new Date(arg.getValidFrom()));
        entity.setValidTo(arg.getValidTo() == null ? null : new Date(arg.getValidTo()));
        entity.refreshStatus();
        entity.setResponseMsg(ReplaceUtil.replaceWxAppId(entity.getResponseMsg(), arg.getWxAppId()));
        boolean result = wxAutoReplyRuleDao.insertEntity(entity);
        return (result ? Result.newSuccess(id) : Result.newError(SHErrorCode.OPERATE_DB_FAIL));
    }

    @Override
    public Result<Boolean> deleteWxAutoReplyRule(String ea, Integer fsUserId, String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        boolean result = wxAutoReplyRuleDao.deleteEntityByIdAndEa(id, ea);
        return (result ? Result.newSuccess(true) : Result.newError(SHErrorCode.OPERATE_DB_FAIL));
    }

    @Override
    public Result<Boolean> updateWxAutoReplyRule(String ea, Integer fsUserId, UpdateWxAutoReplyRuleArg arg) {
        if (!arg.isArgValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ResponseMsgList responseMsgList = JsonUtil.fromJson(arg.getResponseMsg(), ResponseMsgList.class);
        if (CollectionUtils.isEmpty(responseMsgList) || !responseMsgList.stream().allMatch(ResponseMsg::isResponseMsgValid)) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.updateWxAutoReplyRule responseMsg is error responseMsgList:{} arg:{}", responseMsgList, arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        handleWxAutoReplyImagePath(responseMsgList, ea, fsUserId);
        arg.setResponseMsg(fillMaterialInfoToResponseMsgList(ea, responseMsgList));
        WxAutoReplyRuleEntity entity = new WxAutoReplyRuleEntity();
        BeanUtils.copyProperties(arg, entity, "validFrom", "validTo");
        entity.setEa(ea);
        entity.setValidFrom(arg.getValidFrom() == null ? null : new Date(arg.getValidFrom()));
        entity.setValidTo(arg.getValidTo() == null ? null : new Date(arg.getValidTo()));
        entity.refreshStatus();
        entity.setResponseMsg(ReplaceUtil.replaceWxAppId(entity.getResponseMsg(), arg.getWxAppId()));
        boolean result = false;
        if (StringUtils.isEmpty(entity.getId())) {
            result = wxAutoReplyRuleDao.updateEntityEaAndWxAppIdAndActionType(entity);
        } else {
            result = wxAutoReplyRuleDao.updateEntityByIdAndEa(entity);
        }
        return (result ? Result.newSuccess(true) : Result.newError(SHErrorCode.OPERATE_DB_FAIL));
    }

    @Override
    public Result<PageResult<WxAutoReplyRuleResult>> listWxAutoReplyRule(String ea, Integer fsUserId, ListWxAutoReplyRuleArg arg) {
        if (!arg.isArgValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page();
        page.setPageNo(arg.getPageNum() == null ? 1 : arg.getPageNum());
        page.setPageSize(arg.getPageSize() == null ? 1 : arg.getPageSize());
        page.setCountTotal(true);
        PageResult<WxAutoReplyRuleResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        Optional<List<WxAutoReplyRuleResult>> optionalOfWxAutoReplyRuleResults = weChatServiceManager.listAllWxAutoReplyRuleResult(ea, arg.getWxAppId(), arg.getActionType(), arg.getRuleName(), page);
        pageResult.setTotalCount(page.getTotalNum());
        if (!optionalOfWxAutoReplyRuleResults.isPresent() || optionalOfWxAutoReplyRuleResults.get() == null) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setResult(optionalOfWxAutoReplyRuleResults.get());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<WxAutoReplyRuleResult> getWxAutoReplyRuleDetail(String ea, Integer fsUserId, String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Optional<WxAutoReplyRuleResult> optionalOfWxAutoReplyRuleDetail = weChatServiceManager.getWxAutoReplyRuleDetail(id, ea);
        return (optionalOfWxAutoReplyRuleDetail.isPresent() ? Result.newSuccess(optionalOfWxAutoReplyRuleDetail.get()) : Result.newError(SHErrorCode.NO_DATA));
    }

    @Override
    public Result<List<WeChatMenuFormResult>> queryCustomerMenu(String ea, Integer fsUserId, String id) {
        FsUserVO fsUserVO = new FsUserVO(ea, -10000, id);
        ModelResult<List<WeChatMenuFormData>> result = weChatCustomerMenuRestService.queryCustomerMenu(fsUserVO, id);
        if (result.isSuccess()) {
            List<WeChatMenuFormData> data = result.getResult();
            List<WeChatMenuFormResult> results = new ArrayList<>(data.size());
            for (WeChatMenuFormData formData : data) {
                WeChatMenuFormResult formResult = BeanUtil.copy(formData, WeChatMenuFormResult.class);
                formResult.setChildren(BeanUtil.copy(formData.getChildren(), WeChatMenuFormResult.class));
                results.add(formResult);
            }
            return Result.newSuccess(results);
        }
        return Result.newError(result.getErrorCode(), result.getErrorMessage());
    }

    @Override
    public Result<String> getEaByWxAppId(String platformId, String appId) {
        List<String> eaByWxAppIds = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(platformId,appId);
        if (CollectionUtils.isEmpty(eaByWxAppIds)){
            log.info("WeChatServiceMarketingActivityServiceImpl.getEaByWxAppId return null platformId:{} appid:{}",platformId, appId);
            return Result.newError(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        return Result.newSuccess(eaByWxAppIds.get(0));
    }

    @Override
    public Result<QueryCanFansCountResult> queryCanFansCount(String ea, Integer fsUserId, QueryCanFansCountArg queryCanFansCountArg) {
        QueryCanFansCountResult queryCanFansCountResult = new QueryCanFansCountResult();
        int count = 0;
        NoticeCanSendCustomerCountVo noticeCanSendCustomerCountVo = new NoticeCanSendCustomerCountVo();
        BeanUtils.copyProperties(queryCanFansCountArg, noticeCanSendCustomerCountVo);
        if (queryCanFansCountArg.getSendRange() == SendRangeEnum.SEND_BY_MARKETING_USER_GROUP.getCode()) {

            ModelResult<String> wxAppIdResult = outerServiceWechatService.transAppIdToWxAppId(queryCanFansCountArg.getAppId());
            String wxAppId = wxAppIdResult.getResult();
            List<WechatMarketingUserResult> wechatMarketingUserResults = marketingUserGroupManager.listWxOpenIdByMarketingUserGroupIds(ea, queryCanFansCountArg.getMarketingUserGroupIds(), wxAppId);
            count = wechatMarketingUserResults.size();
        } else {
            count = weChatServiceNoticeManager.queryCanFansCount(noticeCanSendCustomerCountVo).getCount();
        }
        queryCanFansCountResult.setCount(count);
        return Result.newSuccess(queryCanFansCountResult);
    }

    @Override
    public Result<GetTagsResult> getTags(GetTagsArg getTagsArg) {
        String ea = getTagsArg.getEa();
        String appId = getTagsArg.getAppId();
        List<FanTagVO> fanTagVOS = fanTagManager.queryTags(ea, appId);
        GetTagsResult getTagsResult = new GetTagsResult();
        if (fanTagVOS != null) {
            getTagsResult.setFanTagVOS(BeanUtil.copy(fanTagVOS, GetTagsResult.FanTagVO.class));
        }
        return Result.newSuccess(getTagsResult);
    }

    @Override
    public Result<List<WeChatServiceResult>> listWeChatService(String ea, Integer fsUserId) {
        List<OuterServiceResult> outerServiceResults = outerServiceWechatManager.queryOuterServiceList(ea, fsUserId);
        List<WeChatServiceResult> resultList = Lists.newArrayList();
        boolean isOpenDataOwnOrganization = dataPermissionManager.getDataPermissionSetting(ea);

        if (CollectionUtils.isNotEmpty(outerServiceResults)) {
            for (OuterServiceResult outerServiceResult : outerServiceResults) {
                BaseResult<List<Integer>> baseResult = openAppAdminService.getAppAdminIds(ea, outerServiceResult.getAppId());
                if (isOpenDataOwnOrganization) {
                    if (CollectionUtils.isNotEmpty(baseResult.getResult()) && baseResult.getResult().contains(fsUserId)) {
                        // 只有公众号的管理员才能看到公众号
                        resultList.add(BeanUtil.copy(outerServiceResult, WeChatServiceResult.class));
                    }
                } else {
                    resultList.add(BeanUtil.copy(outerServiceResult, WeChatServiceResult.class));
                }
            }
        }
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<String> updatePubPlatAuthComponent(String ea) {
        WxPublicPlatformAuthorizeComponentEntity entity = wxPublicPlatformAuthorizeComponentDao.selectByEa(ea);
        if (entity==null) {
            return null;
        }
        if (entity.getSavePublicPlatformAuthorizeComponent().equals("0")) {
            wxPublicPlatformAuthorizeComponentDao.updatePubPlatAuthComponentByEa("1",ea);
        }else{
            wxPublicPlatformAuthorizeComponentDao.updatePubPlatAuthComponentByEa("0",ea);
        }
        return Result.newSuccess("success");
    }

    @Override
    public Result<WxPublicPlatformAuthorizeComponentResult> selectPubPlatAuthComponent(String ea) {
        WxPublicPlatformAuthorizeComponentEntity entity = wxPublicPlatformAuthorizeComponentDao.selectByEa(ea);
        WxPublicPlatformAuthorizeComponentResult result = new WxPublicPlatformAuthorizeComponentResult();
        if(entity==null){
            return Result.newSuccess();
        }
        result.setId(entity.getId());
        result.setEa(entity.getEa());
        result.setSavePublicPlatformAuthorizeComponent(entity.getSavePublicPlatformAuthorizeComponent());
        result.setCreateTime(entity.getCreateTime());
        result.setUpdateTime(entity.getUpdateTime());
        return Result.newSuccess(result);
    }

    @Override
    public Result<WxPublicPlatformAuthorizeComponentResult> selectPubPlatAuthComponentByWxappid(String wxappid) {
        com.facishare.wechat.union.common.result.Result<String> result = eaBindInfoRestService.getEaByWxAppId(wxappid);
        if(result.isSuccess()){
            return selectPubPlatAuthComponent(result.getData());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<WxPublicPlatformAuthorizeUserInfo> getAuthorizedUserInformation(PubPlatAuthComponentArg arg) {
        if (Strings.isNullOrEmpty(arg.getScope())) {
            return Result.newError(SHErrorCode.WX_OFFICIAL_CODE_INVALID);
        }
        String wxappid = arg.getWxappid();
        String code = arg.getCode();
        String scope = arg.getScope();
        WxPublicPlatformAuthorizeUserInfo userInfo = new WxPublicPlatformAuthorizeUserInfo();
        if(scope.equals("snsapi_userinfo")){
            //非静默授权
            ModelResult<WeChatAuthUserInfo> authUserInfo = wechatUserInfoRestService.getWeChatAuthUserInfo(code,wxappid,"zh_CN");
            log.info("WeChatServiceMarketingActivityServiceImpl.getAuthorizedUserInformation  authUserInfo{}",authUserInfo);
            if(authUserInfo==null || authUserInfo.getErrorCode()!=0){
                return Result.newError(SHErrorCode.WX_OFFICIAL_CODE_INVALID);
            }
            userInfo = BeanUtil.copy(authUserInfo.getResult(),WxPublicPlatformAuthorizeUserInfo.class);
        }else {
            //静默授权
            ModelResult<WeChatAuthBaseUserInfo> authBaseUserInfo = wechatUserInfoRestService.getWeChatAuthBaseUserInfo(code,wxappid);
            log.info("WeChatServiceMarketingActivityServiceImpl.getAuthorizedUserInformation  authBaseUserInfo{}",authBaseUserInfo);
            if(authBaseUserInfo==null || authBaseUserInfo.getErrorCode()!=0){
                return Result.newError(SHErrorCode.WX_OFFICIAL_CODE_INVALID);
            }
            userInfo = BeanUtil.copy(authBaseUserInfo.getResult(),WxPublicPlatformAuthorizeUserInfo.class);
        }
        com.facishare.wechat.union.common.result.Result<String> result = eaBindInfoRestService.getEaByWxAppId(wxappid);
        String ea = null;
        if(result.isSuccess()){
            ea = result.getData();
        }
        String appid = null;
        ModelResult<String> modelResult = outerServiceWechatService.transWxAppIdAndFsEaToAppId(wxappid, ea);
        if(modelResult.isSuccess()){
            appid = modelResult.getResult();
        }
        userInfo.setEa(ea);
        userInfo.setAppid(appid);
        String finalEa = ea;
        WxPublicPlatformAuthorizeUserInfo finalUserInfo = userInfo;
        ThreadPoolUtils.execute(() -> {
            this.syncDataToCrm(finalEa, wxappid, finalUserInfo);
            handleMarketingPromotionObj(finalEa, wxappid, finalUserInfo.getOpenId(), arg);
            if(!StringUtils.isEmpty(arg.getCtaId())) {
                ctaService.recordGetAuthorizedUserInformation(finalEa, arg, finalUserInfo);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess(userInfo);
    }

    public void handleMarketingPromotionObj(String ea, String wxAppId, String openId, MarketingPromotionSourceArg arg) {
        try {
            if (StringUtils.isEmpty(openId)) {
                return;
            }
            ObjectData wechatFanObj = crmV2Manager.getWechatFanObjByOpenId(ea, wxAppId, openId);
            if (wechatFanObj == null || wechatFanObj.get(CrmWechatFanFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getFieldName()) != null) {
                // 如果微信粉丝已经有了营销推广来源，不会进行更新
                return;
            }
            arg.setLeadModule(MarketingPromotionSourceLeadModuleEnum.WECHAT.getLeadModule());
            arg.setDataFrom(MarketingPromotionDataFromEnum.OFFICIAL_ACCOUNT.getDataSource());
            arg.setEa(ea);
            String id = marketingPromotionSourceObjManager.tryGetOrCreateObj(arg);
            if (StringUtils.isEmpty(id)) {
                return;
            }
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put(CrmWechatFanFieldEnum.ID.getFieldName(), wechatFanObj.getId());
            dataMap.put(CrmWechatFanFieldEnum.MARKETING_PROMOTION_SOURCE_ID.getFieldName(), id);
            crmV2Manager.editWechatFanObj(ea, dataMap);
        } catch (Exception e) {
            log.error("handleMarketingPromotionObj,ea:[{}],wxAppId:[{}],openId:[{}], arg:[{}]", ea, wxAppId, openId, arg, e);
        }
    }

    private void syncDataToCrm(String finalEa, String wxappid, WxPublicPlatformAuthorizeUserInfo finalUserInfo) {
        log.info("WeChatServiceMarketingActivityServiceImpl.syncDataToCrm data:{}", JSON.toJSONString(finalUserInfo));
        try {
            ObjectData data = crmV2Manager.getWechatFanObjByOpenId(finalEa, wxappid, finalUserInfo.getOpenId());
            Map<String, Object> dataMap = new HashMap<>();
            if (!StringUtils.isEmpty(finalUserInfo.getAppid())) {
                dataMap.put("app_id", finalUserInfo.getAppid());
            }
            if (!StringUtils.isEmpty(wxappid)) {
                dataMap.put("wx_app_id", wxappid);
            }
            if (!StringUtils.isEmpty(finalUserInfo.getOpenId())) {
                dataMap.put("wx_open_id", finalUserInfo.getOpenId());
            }
            if (!StringUtils.isEmpty(finalUserInfo.getNickName())) {
                dataMap.put("name", finalUserInfo.getNickName());
            }
            if (!StringUtils.isEmpty(finalUserInfo.getSex())) {
                dataMap.put("sex", finalUserInfo.getSex());
            }
            if (!StringUtils.isEmpty(finalUserInfo.getUnionId())) {
                dataMap.put("wx_union_id", finalUserInfo.getUnionId());
            }
            if (!StringUtils.isEmpty(finalUserInfo.getHeadImgUrl())) {
                String headImgUrl = finalUserInfo.getHeadImgUrl();
                byte[] bytes = fileV2Manager.downloadFileByUrl(headImgUrl, finalEa);
                FileV2Manager.FileManagerPicResult jpg = fileV2Manager.uploadToNPath(bytes, "jpg", finalEa, -10000);
                Map<String, Object> img = new HashMap<>();
                img.put("ext", "jpg");
                img.put("path", jpg.getNPath());
                dataMap.put("wx_head_image", Collections.singletonList(img));
            }
            if (data == null) {
                dataMap.put("attention_status", "no_attention");
                crmV2Manager.addWechatFanObj(finalEa, dataMap);
            } else {
                dataMap.put("_id", data.getId());
                crmV2Manager.editWechatFanObj(finalEa, dataMap);
            }
        } catch (Exception e) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.syncDataToCrm error:", e);
        }
    }

    @Override
    public Result<String> getPubPlatAuthComponentAppid() {
        ModelResult<GetComponentAppIdResult> result = wechatPlatformConfigRestService.getComponentAppId();
        log.info("WeChatServiceMarketingActivityServiceImpl.getPubPlatAuthComponentAppid  result{}",result);
        if(result==null || result.getErrorCode()!=0){
            return Result.newError(SHErrorCode.WX_OFFICIAL_COMPONTAPPID_FILE);
        }
        String componentAppId = result.getResult().getComponentAppId();
        return  Result.newSuccess(componentAppId);
    }

    @Override
    public Result<WxPublicPlatformCheckFocus> checkFocusOn(String wxappid, String openId) {
        com.facishare.wechat.union.common.result.Result<String> result = eaBindInfoRestService.getEaByWxAppId(wxappid);
        log.info("WeChatServiceMarketingActivityServiceImpl.checkFocusOn  result{}",result);
        if(!result.getErrorMessage().equals("OK")){
            return Result.newError(SHErrorCode.WX_OFFICIAL_NOT_FOUND_EA);
        }
        String ea = result.getData();
        WxPublicPlatformCheckFocus checkFocus = new WxPublicPlatformCheckFocus();
        Optional<ObjectData> wechatFan= crmV2Manager.getWechatFanByOpenId(ea, wxappid, openId);
        if (!wechatFan.isPresent()){
            checkFocus.setAttentionStatus(false);
            checkFocus.setName(false);
            checkFocus.setCheckHeadImage(false);
        }else {
            checkFocus.setCheckHeadImage(wechatFan.get().get(CrmWechatFanFieldEnum.WX_HEAD_IMAGE.getFieldName()) != null);
            checkFocus.setAttentionStatus(wechatFan.get().get("attention_status").toString().equals("attention"));
            String name = wechatFan.get().get("name").toString();
            checkFocus.setName(!(name == null || name.equals("暂无")));
        }
        return Result.newSuccess(checkFocus);
    }

    @Override
    public Result<List<MarketingWxServiceResult>> listMarketingWxServiceInfo(String ea, Integer fsUserId, boolean checkPermission) {
        return this.listMarketingWxServiceInfo(ea, fsUserId, checkPermission, true);
    }

    @Override
    public Result<List<MarketingWxServiceResult>> listMarketingWxServiceInfo(String ea, Integer fsUserId, boolean checkPermission, boolean isCountWxFansNumber) {
        List<MarketingWxServiceResult> results = new CopyOnWriteArrayList<>();

        List<OuterServiceResult> outerServiceResults = outerServiceWechatManager.queryOuterServiceList(ea, fsUserId);

        //这个接口取不到停用的服务号
        Map<String, OuterServiceResult> appIdToOuterServiceResultMap = outerServiceResults.stream().collect(Collectors.toMap(OuterServiceResult::getAppId, Function.identity()));
        List<MarketingWxServiceEntity> marketingWxServiceEntityList = marketingWxServiceDao.listByEa(ea);
        if (CollectionUtils.isEmpty(marketingWxServiceEntityList)) {
            return Result.newSuccess(Lists.newArrayList());
        }
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);

        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(Math.min(5, marketingWxServiceEntityList.size()), "getWechanFansCount");
        CountDownLatch countDownLatch = new CountDownLatch(marketingWxServiceEntityList.size());
        for (MarketingWxServiceEntity entity : marketingWxServiceEntityList) {
            executorService.execute(() -> {
                try {
                    if (checkPermission && isOpen) {
                        boolean hasNoAccess = true;
                        List<String> accountIds = dataPermissionManager.findAccessibleOfficialAccountIds(ea, fsUserId);
                        if (CollectionUtils.isNotEmpty(accountIds)) {
                            OuterServiceResult outerServiceResult = appIdToOuterServiceResultMap.get(entity.getAppId());
                            if (outerServiceResult == null) {
                                return;
                            }
                            hasNoAccess = !accountIds.contains(outerServiceResult.getWxAppId()) && !accountIds.contains(defaultAllChannel);
                        }
                        if (hasNoAccess) {
                            // 没权限不看
                            return;
                        }
                    }
                    MarketingWxServiceResult result = BeanUtil.copyProperties(entity, MarketingWxServiceResult.class);

                    //这里取不到停用的服务号
                    OuterServiceResult outerServiceResult = appIdToOuterServiceResultMap.get(entity.getAppId());
                    if (outerServiceResult != null) {
                        result.setAppName(outerServiceResult.getAppName());
                        result.setWxAppId(outerServiceResult.getWxAppId());
                        result.setWxAppName(outerServiceResult.getWxAppName());
                        result.setAppDesc(outerServiceResult.getAppDesc());
                        result.setAppLogoUrl(outerServiceResult.getAppLogoUrl());
                        result.setAppLogoFxiaokeUrl(outerServiceResult.getAppLogoFxiaokeUrl());
                    } else {
                        //如果微联服务号被停用或删除，则从这里去拿信息，但是这里缺少logo属性
                        OpenAppDO openAppDO = openAppService.loadOpenApp(entity.getAppId()).getResult();
                        //如果都没有获取到
                        if (openAppDO == null) {
                            return;
                        }
                        result.setAppName(openAppDO.getAppName());
                        result.setAppDesc(openAppDO.getAppDesc());
                    }
                    //获取微信粉丝数
                    if (isCountWxFansNumber && outerServiceResult != null && outerServiceResult.getAppId() != null) {
                        result.setWxFansNumber(getWechanFansCount(ea, outerServiceResult.getWxAppId()));
                    }
                    results.add(result);
                } catch (Exception e) {
                    log.warn("listMarketingWxServiceInfo error ea:{}", ea, e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("getWechanFansCount timeout", e);
        }
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        return Result.newSuccess(results);
    }

    private int getWechanFansCount(String ea, String appId){
        PaasQueryFilterArg arg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        queryRelation.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(), FilterOperatorEnum.EQ.getValue(), Lists.newArrayList(appId));
        arg.setQuery(queryRelation);
        arg.setObjectAPIName(CrmWechatFanFieldEnum.API_NAME.getFieldName());
        arg.setSelectFields(Lists.newArrayList("_id"));
        return crmV2Manager.countCrmObjectByFilterV3(ea, -10000, arg);
    }

    @Override
    public Result<MarketingWxServiceResult> addMarketingWxServiceAccout(String ea, Integer fsUserId, String appId) {
        MarketingWxServiceEntity marketingWxServiceEntity = marketingWxServiceDao.getByAppId(appId);
        if (marketingWxServiceEntity != null) {
            return Result.newError(SHErrorCode.DUPLICATE_ADD);
        }

        int marketingWxServiceNumber = marketingWxServiceDao.countByEa(ea);
//        String version = appVersionManager.getCurrentAppVersion(ea);
//        if (VersionEnum.STAN.getVersion().equals(version)) {
//            if (marketingWxServiceNumber >= 1) {
//                return Result.newError(SHErrorCode.MARKETING_WE_SERVCE_MAX);
//            }
//        } else if (VersionEnum.PRO.getVersion().equals(version) || VersionEnum.STREN.getVersion().equals(version)) {
//            if (marketingWxServiceNumber >= 5) {
//                return Result.newError(SHErrorCode.MARKETING_WE_SERVCE_MAX);
//            }
//        } else {
//            return Result.newError(SHErrorCode.NOT_BUG);
//        }

        // 调用接口查询一次避免因为缓存导致与平台数据不一致
        List<OuterServiceResult> outerServiceResultList = outerServiceWechatManager.queryOuterServiceListWithoutCache(ea);
        List<String> appIds = outerServiceResultList.stream().map(OuterServiceResult::getAppId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appIds) || !appIds.contains(appId)) {
            return Result.newError(SHErrorCode.MARKETING_WE_SERVCE_NOT_EXISTS);
        }

        MarketingWxServiceEntity entity = new MarketingWxServiceEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setAppId(appId);
        entity.setOrderCode(1);
        entity.setCreateBy(fsUserId);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        int insertStatus = marketingWxServiceDao.insert(entity);
        WxPublicPlatformAuthorizeComponentEntity componentEntity = wxPublicPlatformAuthorizeComponentDao.selectByEa(ea);
        if (componentEntity==null) {
            componentEntity = new WxPublicPlatformAuthorizeComponentEntity();
            componentEntity.setId(UUIDUtil.getUUID());
            componentEntity.setEa(ea);
            componentEntity.setSavePublicPlatformAuthorizeComponent("0");
            wxPublicPlatformAuthorizeComponentDao.insert(componentEntity);
        }
        if (insertStatus > 0) {
            ThreadPoolUtils.execute(() -> {
                crmV2Manager.tryAddFieldsToWechatFans(ea);
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            List<OuterServiceResult> outerServiceResults = outerServiceWechatManager.queryOuterServiceList(ea, fsUserId);
            Map<String, OuterServiceResult> appIdToOuterServiceResultMap = outerServiceResults.stream().collect(Collectors.toMap(OuterServiceResult::getAppId, Function.identity()));
            OuterServiceResult outerServiceResult = appIdToOuterServiceResultMap.get(appId);
            integralServiceManager.asyncRegisterMaterial(ea, CategoryApiNameConstant.OFFICIAL_ACCOUNT, outerServiceResult.getWxAppId(), outerServiceResult.getWxAppName());
            MarketingWxServiceResult result = BeanUtil.copyProperties(outerServiceResult, MarketingWxServiceResult.class);

            result.setId(entity.getId());
            result.setEa(entity.getEa());
            result.setCreateBy(entity.getCreateBy());
            result.setOrderCode(entity.getOrderCode());

            //获取微信粉丝数
            ModelResult<Integer> wechatUserAmountResult = outerServiceWechatService.queryWechatUserAmount(ea, result.getWxAppId());
            if (wechatUserAmountResult.isSuccess() && wechatUserAmountResult.getResult() != null) {
                result.setWxFansNumber(Math.toIntExact(wechatUserAmountResult.getResult()));
            }
            ThreadPoolUtils.execute(() -> {
                wechatFanOuterTagSynchronizationManager.syncToTagModel(ea, appId);
                //第一次绑定公众号，需要设置市场人员的微信粉丝对象功能权限
                if (marketingWxServiceNumber == 0) {
                    //初始化公众号运营角色
                    userRoleManager.initOfficialAccountMarketingconfig(ea);
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            return Result.newSuccess(result);
        }
        return null;
    }

    @Override
    public Result<PageResult<ListWechatFansResult>> listWechatFans(String ea, Integer fsUserId, ListWechatFansArg arg) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<ObjectData> objectDataList = Lists.newArrayList();
        Integer total;
        String operator = arg.getFilterData().getQuery().getTagOperator();
        List<TagName> tagNames = arg.getFilterData().getQuery().getTagNames();
        if (!CollectionUtils.isEmpty(tagNames) && !StringUtils.isEmpty(operator)) {
            Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, WechatFanFieldContants.API_NAME, tagNames);
            if (CollectionUtils.isEmpty(tagNamesIdMap.values())) {
                return Result.newSuccess();
            }
            List<String> tagIds = tagNamesIdMap.values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
            HeaderObj headerObj = new HeaderObj(ei, fsUserId);
            ControllerListArg perCrmListArg = new ControllerListArg();
            FilterData filterData = arg.getFilterData();
            String tagOperator = "IN".equals(filterData.getQuery().getTagOperator()) ? "LIKE" : filterData.getQuery().getTagOperator();
            perCrmListArg.setObjectDescribeApiName(WechatFanFieldContants.API_NAME);
            perCrmListArg.setSearchTemplateId(filterData.getSearchTemplateId());
            perCrmListArg.setIncludeLayout(false);
            perCrmListArg.setIncludeDescribe(false);
            perCrmListArg.setIncludeButtonInfo(false);
            if (!Strings.isNullOrEmpty(tagOperator) && CollectionUtils.isNotEmpty(tagIds)){
                perCrmListArg.setTagOperator(tagOperator);
                perCrmListArg.setTags(tagIds);
            }
            SearchQuery searchQuery = BeanUtil.copyByFastJson(filterData.getQuery(), SearchQuery.class);
            searchQuery.setOffset(arg.getOffset());
            searchQuery.setLimit(arg.getLimit());
            perCrmListArg.setSearchQuery(searchQuery);
            com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> listCrmObjectResult = metadataControllerService.list(headerObj, WechatFanFieldContants.API_NAME, perCrmListArg);
            if (!listCrmObjectResult.isSuccess() || listCrmObjectResult.getData() == null) {
                throw new MarketingException(listCrmObjectResult.getCode(), listCrmObjectResult.getMessage());
            }
            total = listCrmObjectResult.getData().getTotal();
            objectDataList = listCrmObjectResult.getData().getDataList();

            Set<String> accountIds = objectDataList.stream().map(objectData -> objectData.getString("account_id")).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<String, String> accountIdNameMap = new HashMap<>(0);
            if (!accountIds.isEmpty()){
                accountIdNameMap = crmV2Manager.getObjectNameMapByIdsByCache(ea, fsUserId, CrmObjectApiNameEnum.CUSTOMER.getName(), accountIds);
            }

            Set<String> contactIds = objectDataList.stream().map(objectData -> objectData.getString("contact_id")).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<String, String> contactIdNameMap = new HashMap<>(0);
            if (!contactIds.isEmpty()){
                contactIdNameMap = crmV2Manager.getObjectNameMapByIdsByCache(ea, fsUserId, CrmObjectApiNameEnum.CONTACT.getName(), contactIds);
            }

            Set<String> clueIds = objectDataList.stream().map(objectData -> objectData.getString("clue_id")).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<String, String> clueIdNameMap = new HashMap<>(0);
            if (!clueIds.isEmpty()){
                clueIdNameMap = crmV2Manager.getObjectNameMapByIdsByCache(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(), clueIds);
            }
    
            Set<String> marketingEventIds = objectDataList.stream().map(objectData -> objectData.getString("marketing_event_id")).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<String, String> marketingEventIdNameMap = new HashMap<>(0);
            if (!accountIds.isEmpty()){
                marketingEventIdNameMap = crmV2Manager.getObjectNameMapByIdsByCache(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds);
            }
            
            for (ObjectData objectData : objectDataList) {
                objectData.put("account_id_r", accountIdNameMap.get(objectData.getString("account_id")));
                objectData.put("contact_id_r", contactIdNameMap.get(objectData.getString("contact_id")));
                objectData.put("clue_id_r", clueIdNameMap.get(objectData.getString("clue_id")));
                objectData.put("marketing_event_id_r", marketingEventIdNameMap.get(objectData.getString("marketing_event_id")));
            }
        } else {
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setSearchTemplateId(arg.getFilterData().getSearchTemplateId());
            controllerListArg.setObjectDescribeApiName(arg.getFilterData().getObjectAPIName());
            controllerListArg.setSearchQuery(ConvertUtil.convert(arg.getFilterData().getQuery(), arg.getOffset(), arg.getLimit()));
            com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> listCrmObjectResult = metadataControllerService
                .list(new HeaderObj(ei, fsUserId), arg.getFilterData().getObjectAPIName(), controllerListArg);
            if (!listCrmObjectResult.isSuccess() || listCrmObjectResult.getData() == null) {
                throw new MarketingException(listCrmObjectResult.getCode(), listCrmObjectResult.getMessage());
            }
            total = listCrmObjectResult.getData().getTotal();
            objectDataList = listCrmObjectResult.getData().getDataList();
        }
        List<WechatFanData> wechatFanDataList = Lists.newArrayList();
        for (ObjectData objectData : objectDataList) {
            wechatFanDataList.add(WechatFanData.wrap(objectData));
        }
        List<ListWechatFansResult> results = Lists.newArrayList();
        for (WechatFanData wechatFanData : wechatFanDataList) {
            results.add(this.convert(wechatFanData));
        }
        if (CollectionUtils.isNotEmpty(results)) {
            List<String> objectIds = results.stream().filter(Objects::nonNull).map(ListWechatFansResult::getId).collect(Collectors.toList());
            Map<String, ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataMap = metadataTagManager.getObjectDataIdAndTagNameListDataMapByObjectDataIds(ea, WechatFanFieldContants.API_NAME, objectIds);
            for (ListWechatFansResult result : results) {
                ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData = objectDataIdAndTagNameListDataMap.get(result.getId());
                if (objectDataIdAndTagNameListData == null || StringUtils.isEmpty(objectDataIdAndTagNameListData.getDataId())) {
                    continue;
                }
                result.setTagNameList(objectDataIdAndTagNameListData.getTagNameList());
            }
        }
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(total);
        pageResult.setResult(results);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        return Result.newSuccess(pageResult);
    }

    private ListWechatFansResult convert(WechatFanData wechatFanData) {
        if (wechatFanData == null) {
            return null;
        }
        ListWechatFansResult listWechatFansResult = new ListWechatFansResult();
        listWechatFansResult.setAddress(wechatFanData.getAddress());
        listWechatFansResult.setArea(wechatFanData.getArea());
        listWechatFansResult.setAttentionResources(wechatFanData.getAttentionResources());
        listWechatFansResult.setPhone(wechatFanData.getPhone());
        listWechatFansResult.setRemark(wechatFanData.getRemark());
        listWechatFansResult.setSex(wechatFanData.getSex());
        listWechatFansResult.setUsername(wechatFanData.getUsername());
        listWechatFansResult.setWxAppId(wechatFanData.getWxAppId());
        listWechatFansResult.setWxOpenId(wechatFanData.getWxOpenId());
        listWechatFansResult.setWxHeadImage(wechatFanData.getWxHeadImage());
        listWechatFansResult.setId(wechatFanData.getId());
        listWechatFansResult.setName(wechatFanData.getName());
        if (wechatFanData.getName() == null || wechatFanData.getName().equals("暂无")) {
            listWechatFansResult.setName(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_WECHATSERVICEMARKETINGACTIVITYSERVICEIMPL_921)+wechatFanData.getId().substring(wechatFanData.getId().length()-6));
        }
        listWechatFansResult.setAccountId(wechatFanData.getAccountId());
        listWechatFansResult.setAccountIdR(wechatFanData.getAccountIdR());
        listWechatFansResult.setContactId(wechatFanData.getContactId());
        listWechatFansResult.setContactIdR(wechatFanData.getContactIdR());
        listWechatFansResult.setAttentionStatus(wechatFanData.getAttentionStatus());
        listWechatFansResult.setAttentionTime(wechatFanData.getAttentionTime());
        listWechatFansResult.setClueId(wechatFanData.getClueId());
        listWechatFansResult.setClueIdR(wechatFanData.getClueIdR());
        listWechatFansResult.setMarketingEventId(wechatFanData.getString(CrmWechatFanFieldEnum.MARKETING_EVENT_ID.getFieldName()));
        listWechatFansResult.setMarketingEventName(wechatFanData.getString("marketing_event_id__r"));
        Object owner = wechatFanData.get(CrmWechatFanFieldEnum.SPREAD_FS_USER_ID.getFieldName());
        Integer spreadFsUserId = owner instanceof List && ((List)owner).size() > 0 ? Integer.valueOf(((List)owner).get(0).toString()) : null;
        listWechatFansResult.setSpreadFsUserId(spreadFsUserId);
        return listWechatFansResult;
    }

    @Override
    public Result<FieldValueResult> getWechatParamList(String ea, Integer fsUserId, String marketingEventId, Integer type) {
        Map<String, String> paramDescMap = new HashMap<>();
        if (!StringUtils.isEmpty(marketingEventId)) {
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, marketingEventId);
            paramDescMap.putAll(marketingEventData.getParamDescMap());
            // 会议通知场景
            if (Integer.valueOf(1).equals(type)) {
                paramDescMap.putAll(weChatServiceManager.getConferenceParamDescMap());
            }
        }
        paramDescMap.putAll(weChatServiceManager.getWechatParamDescMap());
        FieldValueResult fieldValueResult = new FieldValueResult();
        List<FieldValue> fieldValues = new ArrayList<>();
        fieldValueResult.setFieldValues(fieldValues);
        for (Map.Entry<String, String> entry : paramDescMap.entrySet()) {
            fieldValues.add(new FieldValue(entry.getKey(), entry.getValue()));
        }
        return Result.newSuccess(fieldValueResult);
    }

    @Override
    public Result<List<String>> sendTemplateMessage(SendTemplateMessageArg arg) {
        BatchTemplateMessageVo vo = new BatchTemplateMessageVo();
        BeanUtils.copyProperties(arg, vo);
        if (org.apache.commons.lang.StringUtils.isNotBlank(arg.getMiniAppId()) && org.apache.commons.lang.StringUtils.isNotBlank(arg.getPagePath())) {
            TemplateMessageVo.MiniProgram miniProgram = new TemplateMessageVo.MiniProgram();
            miniProgram.setMiniAppId(arg.getMiniAppId());
            miniProgram.setPagePath(arg.getPagePath());
            vo.setMiniProgram(miniProgram);
        }
        vo.setData(convertData(arg.getData()));
        // 会员id换weOpenId
        String ea = eieaConverter.enterpriseIdToAccount(arg.getCorpId());
        if (org.apache.commons.lang.StringUtils.isEmpty(ea)) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.sendTemplateMessage ea is null arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Result<String> wxOpenId = memberService.getWxOpenIdByMemberId(ea, arg.getWxAppId(), arg.getMemberId());
        if (wxOpenId== null || !wxOpenId.isSuccess() || org.apache.commons.lang.StringUtils.isEmpty(wxOpenId.getData())) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.sendTemplateMessage wxOpenId null arg:{} ea:{}", arg, ea);
            return Result.newError(SHErrorCode.MEMBER_WXOPENID_FAIL);
        }
        List<String> wxOpenIdList = new ArrayList<>();
        wxOpenIdList.add(wxOpenId.getData());
        vo.setWxOpenIds(wxOpenIdList);
        log.info("WeChatServiceMarketingActivityServiceImpl.sendTemplateMessage vo:{}", vo);
        ModelResult<List<String>> listModelResult = wechatMessageService.sendTemplateMessageByOpenIds(vo);
        if (listModelResult == null || !listModelResult.isSuccess() || CollectionUtils.isNotEmpty(listModelResult.getResult())) {
            log.warn("WeChatServiceMarketingActivityServiceImpl.sendTemplateMessage listModelResult is error arg:{} listModelResult:{}", arg, listModelResult);
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        return Result.newSuccess(listModelResult.getResult());
    }

    private TemplateMessageVo.Data convertData(SendTemplateMessageArg.Data data) {
        if (data == null) {
            return null;
        }
        TemplateMessageVo.Data voData = new TemplateMessageVo.Data();
        if (data.getFirst() != null) {
            voData.setFirst(new TemplateMessageVo.First(data.getFirst().getValue(), data.getFirst().getColor()));
        }
        if (CollectionUtils.isNotEmpty(data.getKeyNotes())) {
            List<TemplateMessageVo.KeyNote> keyNotes = data.getKeyNotes().stream().filter(Objects::nonNull).map(element -> new TemplateMessageVo.KeyNote(element.getValue(), element.getColor())).collect(Collectors.toList());
            voData.setKeyNotes(keyNotes);
        }
        if (data.getRemark() != null) {
            voData.setRemark(new TemplateMessageVo.Remark(data.getRemark().getValue(), data.getRemark().getColor()));
        }
        return voData;
    }

    private String fillMaterialInfoToResponseMsgList(String ea, ResponseMsgList responseMsgList) {
        responseMsgList.stream().filter(ResponseMsg::isMaterialType).forEach(responseMsg -> {
            Result<MaterialWxPresentMsg> materialWxPresentMsgResult = materialService.getMaterialWxPresentMsg(ea, responseMsg.getMaterialType(), responseMsg.getMaterialId());
            if (materialWxPresentMsgResult == null || !materialWxPresentMsgResult.isSuccess() || materialWxPresentMsgResult.getData() == null) {
                log.warn("WeChatServiceMarketingActivityServiceImpl.fillMaterialInfoToResponseMsgList materialWxPresentMsgResult is not successful ea:{} responseMsg:{} ", ea, responseMsg);
                return ;
            }
            MaterialWxPresentMsg materialWxPresentMsg = materialWxPresentMsgResult.getData();
            responseMsg.setTitle(materialWxPresentMsg.getTitle());
            responseMsg.setSharePicUrl(materialWxPresentMsg.getSharePicUrl());
            responseMsg.setDescription(materialWxPresentMsg.getDescription());
        });
        String newJson = GsonUtil.toJson(responseMsgList);
        return newJson;
    }

    private void handleWxAutoReplyImagePath(ResponseMsgList responseMsgList, String ea, Integer fsUserId){
        if (CollectionUtils.isEmpty(responseMsgList)){
            return;
        }

        for (ResponseMsg responseMsg : responseMsgList){
            if (!responseMsg.getType().equals(ResponseMsgTypeEnum.IMAGE.getType())){
                continue;
            }
            String ext = "png";
            String[] rex = responseMsg.getNPath().split("\\.");
            if (rex != null && rex.length ==2){
                ext = rex[1];
            }
            String path = responseMsg.getNPath();
            if (responseMsg.getNPath().startsWith(TEMP_N_WAREHOUSE_TYPE)){
                path = fileV2Manager.changeTempFileToPermanent(ea, fsUserId, ext, responseMsg.getNPath());
            }else if (responseMsg.getNPath().startsWith(A_WAREHOUSE_TYPE)){
                byte[] byts = fileV2Manager.downloadAFile(responseMsg.getNPath(), ea);
                FileV2Manager.FileManagerPicResult pathResult = fileV2Manager.uploadToNPath(byts, ext, ea, fsUserId);
                if (pathResult != null && pathResult.getNPath() != null){
                    path = pathResult.getNPath();
                }
            }
            responseMsg.setNPath(path);
        }
    }
}