package com.facishare.marketing.provider.dao.manager;

import com.facishare.marketing.common.enums.CustomizeFormDataStatusEnum;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CustomizeFormDataDAOManager {
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    public int insertCustomizeFormData(CustomizeFormDataEntity customizeFormDataEntity){
        int result =  customizeFormDataDAO.insertCustomizeFormData(customizeFormDataEntity);
        if (1 == result){
            integralServiceManager.asyncRegisterMaterial(customizeFormDataEntity.getEa(),CategoryApiNameConstant.FORM,customizeFormDataEntity.getId(),customizeFormDataEntity.getFormHeadSetting().getName());
        }
        return result;
    }

    public int updateCustomizeFormDataDetail(CustomizeFormDataEntity customizeFormDataEntity){
        int result =  customizeFormDataDAO.updateCustomizeFormDataDetail(customizeFormDataEntity);
        if (1 == result){
            integralServiceManager.asyncRegisterMaterial(customizeFormDataEntity.getEa(),CategoryApiNameConstant.FORM,customizeFormDataEntity.getId(),customizeFormDataEntity.getFormHeadSetting().getName());
        }
        return result;
    }

    public int updateCustomizeFormDataStatus(String ea, String id, Integer fsUserId, Integer status){
        int result =  customizeFormDataDAO.updateCustomizeFormDataStatus(id,fsUserId,status);
        if (1 == result && CustomizeFormDataStatusEnum.DELETE.getValue().equals(status)){
            integralServiceManager.asyncRemoveMaterial(ea,CategoryApiNameConstant.FORM,id);
        }
        return result;
    }

    public int updateCustomizeFormDataStatusBatch(String ea, List<String> idList, Integer fsUserId, Integer status){
        int result =  customizeFormDataDAO.updateCustomizeFormDataStatusBatch(idList,fsUserId,status);
        if (result > 0 && CustomizeFormDataStatusEnum.DELETE.getValue().equals(status)){
            for (String id : idList) {
                integralServiceManager.asyncRemoveMaterial(ea,CategoryApiNameConstant.FORM,id);
            }
        }
        return result;
    }

    public int deleteCustomizeFormData(String ea, String id) {
        int result = customizeFormDataDAO.deleteCustomizeFormData(id);
        if (1 == result) {
            integralServiceManager.asyncRemoveMaterial(ea, CategoryApiNameConstant.FORM, id);
        }
        return result;
    }

}
