package com.facishare.marketing.provider.manager.ai;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.aizhan.SEOPromptCompletionsArg;
import com.facishare.marketing.api.result.ai.PromptCompletionsResult;
import com.facishare.marketing.api.vo.ai.PromptCompletionsArg;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.dao.OutLinkDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.ArticleEntity;
import com.facishare.marketing.provider.entity.OutLinkEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.util.i18n.I18NUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

@Slf4j
@Component
public class PaaSPromptManager {

    @Autowired
    private EIEAConverter converter;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private OutLinkDAO outLinkDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MergeJedisCmd jedisCmd;

    public Result<PromptCompletionsResult> asyncPromptCompletions(String taskId, Supplier<PromptCompletions.Result> supplier) {
        PromptCompletionsResult result = new PromptCompletionsResult();
        if (StringUtils.isNotBlank(taskId)) {
            String key = "SHAREGPT:PROMPTCOMPLETIONS:" + taskId;
            String resultStr = jedisCmd.get(key);
            if (StringUtils.isNotBlank(resultStr)) {
                return Result.newSuccess(JSON.parseObject(resultStr, PromptCompletionsResult.class));
            }
            return Result.newError(SHErrorCode.NO_DATA);
        }
        taskId = UUID.randomUUID().toString();
        String key = "SHAREGPT:PROMPTCOMPLETIONS:" + taskId;
        result.setTaskId(taskId);
        result.setStatus(PromptCompletionsResult.PromptCompletionsResultStatus.PROCESSING.getValue());
        jedisCmd.set(key, JSON.toJSONString(result), SetParams.setParams().ex(300L));
        ThreadPoolUtils.executeWithNewThread(() -> {
            try {
                PromptCompletions.Result completions = supplier.get();
                result.setMessage(completions.getMessage());
                result.setType(completions.getType());
                result.setStatus(PromptCompletionsResult.PromptCompletionsResultStatus.FINISH.getValue());
            } catch (Exception e) {
                log.warn("asyncPromptCompletions fail", e);
                result.setStatus(PromptCompletionsResult.PromptCompletionsResultStatus.FAIL.getValue());
            } finally {
                jedisCmd.set(key, JSON.toJSONString(result), SetParams.setParams().ex(300L));
            }
        });
        return Result.newSuccess(result);
    }

    public PromptCompletions.Result generatePromotional(String ea, Integer fsUserId, PromptCompletionsArg promptCompletionsArg) {
        List<String> imageStrings = new ArrayList<>();
        Map<String, String> sceneVariables = Maps.newHashMap();
        sceneVariables.put("campaignName", promptCompletionsArg.getCampaignName());
        sceneVariables.put("promotionSlogan", promptCompletionsArg.getPromotionSlogan());
        if (CollectionUtils.isNotEmpty(promptCompletionsArg.getMaterialInfos())) {
            imageStrings.addAll(promptCompletionsArg.getMaterialInfos());
        }
        if (promptCompletionsArg.getObjectType() != null && StringUtils.isNotBlank(promptCompletionsArg.getObjectId())) {
            ObjectTypeEnum objectTypeEnum = ObjectTypeEnum.getByType(promptCompletionsArg.getObjectType());
            String objectId = promptCompletionsArg.getObjectId();
            switch (objectTypeEnum) {
                case HEXAGON_SITE:
                    HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getHomePage(objectId);
                    if (hexagonPageEntity == null) {
                        break;
                    }
                    sceneVariables.put("contentName", hexagonPageEntity.getName());
                    sceneVariables.put("promotionTitle", hexagonPageEntity.getShareTitle());
                    sceneVariables.put("promotionDescription", hexagonPageEntity.getShareDesc());
                    break;
                case ARTICLE:
                    ArticleEntity articleEntity = articleDAO.getById(objectId);
                    if (articleEntity == null) {
                        break;
                    }
                    sceneVariables.put("promotionTitle", articleEntity.getTitle());
                    sceneVariables.put("promotionDescription", articleEntity.getSummary());
                    break;
                case PRODUCT:
                    ProductEntity productEntity = productDAO.queryProductDetail(objectId);
                    if (productEntity == null) {
                        break;
                    }
                    sceneVariables.put("contentName", productEntity.getName());
                    sceneVariables.put("promotionDescription", productEntity.getSummary());
                case QR_POSTER:
                    QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(objectId);
                    if (qrPosterEntity == null) {
                        break;
                    }
                    imageStrings.add(qrPosterEntity.getApath());
                case ACTIVITY:
                    ActivityEntity activityEntity = activityDAO.getById(objectId);
                    if (activityEntity == null) {
                        break;
                    }
                    String activityDetailSiteId = activityEntity.getActivityDetailSiteId();
                    if (StringUtils.isNotBlank(activityDetailSiteId)) {
                        HexagonPageEntity activityDetailPage = hexagonPageDAO.getHomePage(objectId);
                        if (activityDetailPage == null) {
                            break;
                        }
                        sceneVariables.put("contentName", activityDetailPage.getName());
                        sceneVariables.put("promotionTitle", activityDetailPage.getShareTitle());
                        sceneVariables.put("promotionDescription", activityDetailPage.getShareDesc());
                    }
                case OUT_LINK:
                    OutLinkEntity outLinkEntity = outLinkDAO.getById(objectId);
                    if (outLinkEntity == null) {
                        break;
                    }
                    sceneVariables.put("contentName", outLinkEntity.getName());
                    sceneVariables.put("promotionTitle", outLinkEntity.getTitle());
                    sceneVariables.put("promotionDescription", outLinkEntity.getDescribe());
            }
        }
        initializeNullKeys(sceneVariables, "");
        BaseArgument context = new BaseArgument();
        context.setTenantId(String.valueOf(converter.enterpriseAccountToId(ea)));
        context.setUserId(String.valueOf(QywxUserConstants.isFsUserId(fsUserId) ? fsUserId : "-10000"));
        context.setLocale(I18NUtil.getLanguage());
        context.setBusiness(promptCompletionsArg.getBusiness());
        PromptCompletions.Arg arg = new PromptCompletions.Arg();
        arg.setApiName("prompt_promotion_slogan");
        // 确保图片为非Cpath
        List<String> newImageStrings = Lists.newArrayList();
        for (String imageString : imageStrings) {
            if (imageString.startsWith("C_")) {
                String apathImage = fileV2Manager.getApathByUrl(fileV2Manager.getUrlByPath(ea, imageString));
                if (StringUtils.isNotBlank(apathImage)) {
                    newImageStrings.add(apathImage);
                }
            } else {
                newImageStrings.add(imageString);
            }
        }
        arg.setImageStrings(newImageStrings);
        arg.setSceneVariables(sceneVariables);
        arg.setSupportImage(true);
        PromptCompletions.Result result = null;
        try {
            result = FsAI.prompt().completions(context, arg);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
        log.info("generatePromotional ea:{} promptCompletionsArg={} result:{}", ea, arg, result);
        return result;
    }

    public static void initializeNullKeys(Map<String, String> map, String defaultValue) {
        map.putIfAbsent("campaignName", "");
        map.putIfAbsent("promotionSlogan", "");
        map.putIfAbsent("contentName", "");
        map.putIfAbsent("promotionTitle", "");
        map.putIfAbsent("promotionDescription", "");
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue() == null) {
                map.put(entry.getKey(), defaultValue);
            }
        }
    }

    public PromptCompletions.Result generateSEOContent(String ea, Integer fsUserId, SEOPromptCompletionsArg SEOPromptCompletionsArg) {
        Map<String, String> sceneVariables = Maps.newHashMap();
        sceneVariables.put("subject", SEOPromptCompletionsArg.getSubject());
        sceneVariables.put("description", SEOPromptCompletionsArg.getDescription());
        sceneVariables.put("keyword", SEOPromptCompletionsArg.getKeyword());
        sceneVariables.put("keywordList", SEOPromptCompletionsArg.getKeywordList());
        BaseArgument context = new BaseArgument();
        context.setTenantId(String.valueOf(converter.enterpriseAccountToId(ea)));
        context.setUserId(String.valueOf(QywxUserConstants.isFsUserId(fsUserId) ? fsUserId : "-10000"));
        context.setLocale(I18NUtil.getLanguage());
        context.setBusiness("marketing_official_website_seo");
        PromptCompletions.Arg arg = new PromptCompletions.Arg();
        arg.setApiName(SEOPromptCompletionsArg.getPromptApiName());
        arg.setSceneVariables(sceneVariables);
        PromptCompletions.Result result = null;
        try {
            result = FsAI.prompt().completions(context, arg);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
        log.info("generateSEOContent ea:{} promptCompletionsArg={} result:{}", ea, arg, result);
        return result;
    }

}
