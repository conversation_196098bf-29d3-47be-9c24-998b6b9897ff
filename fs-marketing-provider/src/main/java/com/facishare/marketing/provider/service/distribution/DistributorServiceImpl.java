package com.facishare.marketing.provider.service.distribution;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.mankeep.api.outService.arg.distribute.ConfirmDistributorNoticeArg;
import com.facishare.mankeep.api.outService.service.OutDistributeService;
import com.facishare.mankeep.common.enums.DistributorSourceTypeEnum;
import com.facishare.mankeep.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.api.arg.distribution.QueryDistributorResultArg;
import com.facishare.marketing.api.arg.distribution.UpdateOperatorArg;
import com.facishare.marketing.api.result.distribution.DistributorResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorApplyResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorGradeResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorInfoResult;
import com.facishare.marketing.api.result.distribution.QueryDistributorRecruitResult;
import com.facishare.marketing.api.service.distribution.DistributorService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.ConfirmDistributorVO;
import com.facishare.marketing.api.vo.QueryDistributorApplyVO;
import com.facishare.marketing.api.vo.QueryDistributorGradeVO;
import com.facishare.marketing.api.vo.QueryDistributorRecruitVO;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.distribution.DistributorStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.OperatorDao;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.ClueOrderDao;
import com.facishare.marketing.provider.dao.distribution.DistributorApplicationDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.distribution.DistributorRightsDao;
import com.facishare.marketing.provider.dao.distribution.OperatorDistributorDAO;
import com.facishare.marketing.provider.dto.distribution.QueryOperatorByDistributorIdDTO;
import com.facishare.marketing.provider.entity.Operator;
import com.facishare.marketing.provider.entity.distribution.ClueDistributorResult;
import com.facishare.marketing.provider.entity.distribution.ClueEntity;
import com.facishare.marketing.provider.entity.distribution.ClueOrderEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorApplicationEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorBaseInfoResult;
import com.facishare.marketing.provider.entity.distribution.DistributorEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorRightsEntity;
import com.facishare.marketing.provider.entity.distribution.OperatorDistributorEntity;
import com.facishare.marketing.provider.entity.distribution.RecruitCountEntity;
import com.facishare.marketing.provider.manager.PushSessionManager;
import com.facishare.marketing.provider.manager.distribution.DistributorManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.contants.SalesOrderFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SalesOrderData;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("distributorService")
@Slf4j
public class DistributorServiceImpl implements DistributorService {
    @Autowired
    private DistributorDao distributorDao;
    @Autowired
    private ClueDAO clueDAO;
    @Autowired
    private OperatorDistributorDAO operatorDistributorDAO;
    @Autowired
    private OperatorDao operatorDao;
    @Autowired
    private DistributorApplicationDAO distributorApplicationDAO;
    @Autowired
    private DistributorManager distributorManager;
    @Autowired
    private OutDistributeService outDistributeService;
    @Autowired
    private ClueOrderDao clueOrderDao;
    @Autowired
    private DistributorRightsDao distributorRightsDao;
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Override
    public Result<PageResult<DistributorResult>> queryDistributorResult(QueryDistributorResultArg vo) {
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<DistributorEntity> distributorEntityList;
        String operatorId = null;
        if (vo.getStatus() == null) {
            vo.setStatus(3);
        }
        if (vo.getIsAppAdmin()) {
            distributorEntityList = distributorDao.adminQueryDistributorResult(vo.getPlanId(), page, vo.getStatus(), vo.getKeyWord());
        } else if (vo.getIsOperator()) {
            distributorEntityList = distributorDao.operatorQueryDistributorResult(vo.getEa(), vo.getPlanId(), vo.getFsUserId(), page, vo.getStatus(), vo.getKeyWord());
            Operator operator = operatorDao.getOperatorByFsUidAndPlanId(vo.getPlanId(), vo.getFsUserId());
            if (null != operator) {
                operatorId = operator.getId();
                if (CollectionUtils.isNotEmpty(distributorEntityList)) {
                    for (DistributorEntity distributorEntity : distributorEntityList) {
                        distributorEntity.setOperatorId(operator.getId());
                    }
                }
            }
        } else {
            distributorEntityList = new ArrayList<>();
        }
        List<DistributorResult> distributorResults = new ArrayList<>();
        PageResult<DistributorResult> pageResult = new PageResult<>();
        pageResult.setResult(distributorResults);
        pageResult.setPageNum(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotalCount(page.getTotalNum());
        if (CollectionUtils.isEmpty(distributorEntityList)) {
            return new Result(SHErrorCode.SUCCESS, pageResult);
        }

        List<String> distributorRecruitIds = new ArrayList<>();
        List<String> distributorIds = distributorEntityList.stream().map(DistributorEntity :: getId).collect(Collectors.toList());
        for (DistributorEntity distributorEntity : distributorEntityList) {
            if (StringUtils.isNotEmpty(distributorEntity.getRecruitId())) {
                distributorRecruitIds.add(distributorEntity.getRecruitId());
            }
        }

        Map<String, DistributorBaseInfoResult> distributorFormDataMap = distributorManager.getDistributorFormDataMap(vo.getPlanId(), distributorRecruitIds, distributorEntityList);

        List<ClueDistributorResult> clueDistributorResults = clueDAO.queryClueDistributorResultList(distributorIds);
        if (clueDistributorResults == null) {
            clueDistributorResults = Lists.newArrayList();
        }
        final Map<String, ClueDistributorResult> clueDistributorResultMap = clueDistributorResults.stream().collect(Collectors.toMap(ClueDistributorResult::getDistributorId, a -> a,(k1,k2)->k1));

        List<QueryOperatorByDistributorIdDTO> operatorDtos = operatorDistributorDAO.queryOperatorByDistributorIds(distributorIds, operatorId);
        if (operatorDtos == null) {
            operatorDtos = Lists.newArrayList();
        }
        final Map<String, QueryOperatorByDistributorIdDTO> operatorDtoMap = operatorDtos.stream().collect(Collectors.toMap(QueryOperatorByDistributorIdDTO :: getDistributorId, v -> v, (k1, k2) -> k1));

        List<RecruitCountEntity> recruitCounts = distributorDao.getRecruitCounts(distributorIds);
        recruitCounts = recruitCounts == null ? Lists.newArrayList() : recruitCounts;
        final Map<String, RecruitCountEntity> recruitCountMap = recruitCounts.stream().collect(Collectors.toMap(RecruitCountEntity :: getRecruitId, v -> v, (k1, k2) -> k1));

        distributorEntityList.forEach(value -> {
            DistributorResult distributorResult = BeanUtil.copy(value, DistributorResult.class);
            ClueDistributorResult clueDistributorResult = clueDistributorResultMap.get(value.getId());
            if (null != clueDistributorResult) {//线索数据
                distributorResult.setCountClue(clueDistributorResult.getCountClue());

            }
            QueryOperatorByDistributorIdDTO operatorByDistributorIdDTO = operatorDtoMap.get(value.getId());
            if (null != operatorByDistributorIdDTO) {//运营数据
                distributorResult.setOperator(operatorByDistributorIdDTO.getName());
                distributorResult.setStatus(operatorByDistributorIdDTO.getStatus());
            }
            DistributorBaseInfoResult baseInfoResult = distributorFormDataMap.get(value.getId());
            distributorResult.setGradeName(baseInfoResult.getGradeName());
            distributorResult.setAvatar(baseInfoResult.getAvatar());
            distributorResult.setAvatarThumbnail(baseInfoResult.getAvatar());
            distributorResult.setDistributorName(baseInfoResult.getName());
            distributorResult.setName(baseInfoResult.getWechatName());
            distributorResult.setPhone(baseInfoResult.getPhone());
            distributorResult.setWechat(baseInfoResult.getWechat());
            distributorResult.setJoinDesc(baseInfoResult.getJoinDesc());
            distributorResult.setIdCardNumber(baseInfoResult.getIdCardNumber());
            distributorResult.setBankCardNo(baseInfoResult.getBankCardNo());
            distributorResult.setBankName(baseInfoResult.getBankName());
            distributorResult.setReward((float) baseInfoResult.getTotalReward());
            distributorResult.setGranted((float) baseInfoResult.getGranted());
            distributorResult.setUngranted((float) (baseInfoResult.getTotalReward() - baseInfoResult.getGranted()));
            distributorResult.setValidClueRewardSum((float) baseInfoResult.getValidClueReward());

            if (StringUtils.isNotEmpty(value.getRecruitId())) {
                DistributorBaseInfoResult baseRecruitInfoResult = distributorFormDataMap.get(value.getRecruitId());
                if (baseRecruitInfoResult != null) {
                    distributorResult.setRecruitName(baseRecruitInfoResult.getName());
                }
            }
            DistributorApplicationEntity applicationEntity = distributorApplicationDAO.getLatestInfoById(value.getId(), value.getOperatorId());
            if (applicationEntity != null) {
                distributorResult.setRefuseDesc(applicationEntity.getRefuseDesc());
            }

            RecruitCountEntity recruitCountEntity = recruitCountMap.get(value.getId());
            if (recruitCountEntity != null) {
                distributorResult.setRecruitCount(recruitCountEntity.getRecruitCount() == null ? 0 : recruitCountEntity.getRecruitCount());
            } else {
                distributorResult.setRecruitCount(0);
            }

            distributorResults.add(distributorResult);
        });

        return new Result(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<Void> exportQueryDistributorResult(QueryDistributorResultArg arg) {

        com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<PageResult<DistributorResult>> distributorResult = queryDistributorResult(arg);
            //Long startTime = arg.getStartDate();
            Long startTime = new Date().getTime();
            StringBuilder title = new StringBuilder(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_406));
            String filename = generateQueryDistributorResultFilename(startTime, title).toString();
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_406));
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = generateExcelTitleList();
            List<List<Object>> distributorResultList = generateExcelDatasList(distributorResult.getData().getResult());
            ExcelUtil.fillContent(xssfSheet, titleList, distributorResultList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, arg.getEa(), arg.getFsUserId());
        }, com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    public StringBuilder generateQueryDistributorResultFilename(Long startTime, StringBuilder title) {
        String startTimeFormat = DateUtil.dateMillis2String(startTime, I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_229));
        //String endTimeFormat = DateUtil.dateMillis2String(endTime, "yyyy年MM月dd日");
        return new StringBuilder(startTimeFormat).append(title).append(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_233) + ".xlsx");
    }

    private List<List<Object>> generateExcelDatasList(List<DistributorResult> distributorResult) {
        List<List<Object>> datasList = new ArrayList<>();
        distributorResult.forEach(value -> {
            List<Object> objList = new ArrayList<>();
            objList.add(value.getDistributorName());
            objList.add(value.getName());
            objList.add(value.getPhone());
            objList.add(value.getGrade());
            objList.add(value.getGradeName());
            objList.add(value.getCountClue());
            objList.add(String.format("%.2f", value.getReward()));
            objList.add(value.getRecruitCount());
            objList.add(value.getRecruitName());
            objList.add(value.getOperator());
            objList.add(getDistributorStatusName(value.getStatus()));
            objList.add(value.getRefuseDesc());
            objList.add(value.getCreateTime());
            datasList.add(objList);
        });
        return datasList;
    }

    private String getDistributorStatusName(int status){
        String resultStr = "";
        if (status == DistributorStatusEnum.UNCONFIRMED.getType()) {
            resultStr = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_259);
        } else if (status == DistributorStatusEnum.TO_BE_PROCESSED.getType()) {
            resultStr = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_261);
        } else {
            resultStr = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_263);
        }
        return resultStr;
    }

    private List<String> generateExcelTitleList() {
        List<String> titleList = new ArrayList<>();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_708));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_273));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_274));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_275));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_276));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_277));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_278));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_279));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1078));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_281));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_282));
        return titleList;
    }

    @Override
    public Result updateOperator(String ea, Integer fsUserId, UpdateOperatorArg vo) {
        List<UpdateOperatorArg.UpdateOperator> arg = vo.getUpdateOperators();
        arg.forEach(value -> {
            OperatorDistributorEntity operatorDistributorEntity = operatorDistributorDAO.getByDistributorId(value.getId(), value.getPreOperatorId());
            if (null != operatorDistributorEntity) {
                DistributorApplicationEntity distributorApplicationEntity = distributorApplicationDAO.getLatestInfoById(operatorDistributorEntity.getDistributorId(), value.getPreOperatorId());
                operatorDistributorDAO.updateOperator(value.getOperatorId(), operatorDistributorEntity.getId(), value.getPreOperatorId());
                operatorDistributorDAO.updateApply(value.getOperatorId(), value.getId(), value.getPreOperatorId(), I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_DISTRIBUTORSERVICEIMPL_294), DistributorStatusEnum.UNCONFIRMED.getType());
                DistributorApplicationEntity saveData = new DistributorApplicationEntity();
                saveData.setId(UUIDUtil.getUUID());
                saveData.setDistributorId(value.getId());
                saveData.setOperatorId(value.getOperatorId());
                saveData.setSourceId(value.getOperatorId());
                saveData.setSourceType(DistributorSourceTypeEnum.OPERATOR.getType());
                saveData.setStatus(distributorApplicationEntity.getStatus());
                saveData.setRefuseDesc(distributorApplicationEntity.getRefuseDesc());
                saveData.setUpdateTime(new Date());
                saveData.setCreateTime(new Date());
                distributorApplicationDAO.addDistributorApplicationInfo(saveData);
            }
        });
        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<QueryDistributorInfoResult> queryDistributeInfoByOperator(String operatorId, String distributorId) {
        return distributorManager.queryDistributeInfoByOperator(operatorId, distributorId);
    }

    @Override
    public Result<Void> confirmDistributor(ConfirmDistributorVO vo) {
        DistributorApplicationEntity applicationEntity = distributorApplicationDAO.getLatestInfoByDistributorId(vo.getDistributorId());
        if (applicationEntity == null || !applicationEntity.getStatus().equals(DistributorStatusEnum.TO_BE_PROCESSED.getType())) {
            return Result.newError(SHErrorCode.DISTRIBUTOR_APPLICATION_NOT_EXIST);
        }
        OperatorDistributorEntity operatorDistributorEntity = operatorDistributorDAO.getByDistributorId(applicationEntity.getDistributorId(), applicationEntity.getOperatorId());
        if (operatorDistributorEntity == null || !operatorDistributorEntity.getStatus().equals(DistributorStatusEnum.TO_BE_PROCESSED.getType())) {
            return Result.newError(SHErrorCode.DISTRIBUTOR_APPLICATION_NOT_EXIST);
        }
        boolean updateResult = updateDistributorStatus(applicationEntity.getId(), operatorDistributorEntity.getId(), vo.getConfirmType(), vo.getRefuseDesc());
        if (updateResult) {
            ThreadPoolUtils.execute(() -> {
                ConfirmDistributorNoticeArg arg = new ConfirmDistributorNoticeArg();
                arg.setDistributorId(vo.getDistributorId());
                arg.setApplicationId(applicationEntity.getId());
                arg.setConfirmType(vo.getConfirmType());
                arg.setRefuseDesc(vo.getRefuseDesc());
                outDistributeService.confirmDistributorNotice(arg);
            });
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.SYSTEM_ERROR);
    }

    @Override
    public Result<PageResult<QueryDistributorRecruitResult>> queryDistributorRecruit(QueryDistributorRecruitVO vo) {
        if (vo == null || StringUtils.isBlank(vo.getDistributorId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return distributorManager.queryDistributorRecruit(vo);
    }

    @Override
    public Result<QueryDistributorGradeResult> queryDistributorGrade(QueryDistributorGradeVO vo) {
        int gradeType = vo.getGradeType() == null ? 0 : vo.getGradeType();

        QueryDistributorGradeResult result = new QueryDistributorGradeResult();
        result.setDistributorGrade(0);
        result.setRecruitGrade(0);
        try {
            ClueEntity clueEntity = null;
            if (StringUtils.isNotEmpty(vo.getOpportunityId())) {
                clueEntity = clueDAO.getClueByOpportunityId(vo.getOpportunityId());
            } else if (StringUtils.isNotEmpty(vo.getNewOpportunityId())) {
                clueEntity = clueDAO.getClueByNewOpportunityId(vo.getNewOpportunityId());
            } else if (StringUtils.isNotEmpty(vo.getOrderId())){
                ClueOrderEntity clueOrderEntity = clueOrderDao.queryClueOrder(vo.getOrderId());
                if (clueOrderEntity != null) {
                    clueEntity = clueDAO.getClueById(clueOrderEntity.getClueId());
                } else if (vo.getTenantId() != null) {
                    ObjectData objectData = crmV2Manager.getDetailByEi(vo.getTenantId(), -1000, SalesOrderFieldContants.API_NAME, vo.getOrderId());
                    if (objectData != null) {
                        SalesOrderData salesOrderVO = SalesOrderData.wrap(objectData);
                        if (StringUtils.isNotEmpty(salesOrderVO.getOpportunityId())) {
                            clueEntity = clueDAO.getClueByOpportunityId(salesOrderVO.getOpportunityId());
                        } else if (StringUtils.isNotEmpty(salesOrderVO.getNewOpportunityId())) {
                            clueEntity = clueDAO.getClueByNewOpportunityId(salesOrderVO.getNewOpportunityId());
                        }
                    }
                }
            }
            if (clueEntity != null) {
                if (gradeType == 1) {
                    DistributorEntity distributorEntity = distributorDao.queryDistributorById(clueEntity.getDistributorId());
                    result.setDistributorGrade(distributorEntity.getGrade());
                    if (StringUtils.isNotEmpty(distributorEntity.getRecruitId())) {
                        DistributorEntity entityRecruit = distributorDao.queryDistributorById(distributorEntity.getRecruitId());
                        result.setRecruitGrade(entityRecruit.getRecruitGrade());
                    }
                } else {
                    if (StringUtils.isNotEmpty(clueEntity.getRightsId())) {
                        DistributorRightsEntity rightsEntity = distributorRightsDao.queryDistributorRights(clueEntity.getRightsId());
                        if (rightsEntity != null) {
                            result.setDistributorGrade(rightsEntity.getGrade());
                        }
                    }
                    if (StringUtils.isNotEmpty(clueEntity.getRecruitRightsId())) {
                        DistributorRightsEntity rightsEntity = distributorRightsDao.queryDistributorRights(clueEntity.getRecruitRightsId());
                        if (rightsEntity != null) {
                            result.setRecruitGrade(rightsEntity.getGrade());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("DistributorServiceImpl queryDistributorGradeByOrderId orderId:{}, exception:{}", vo.getOrderId(), e.fillInStackTrace());
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryDistributorApplyResult> queryDistributorApplyInfo(QueryDistributorApplyVO vo) {
        QueryDistributorApplyResult result = new QueryDistributorApplyResult();
        DistributorApplicationEntity distributorApplicationEntity = distributorApplicationDAO.getApplicationInfoById(vo.getDistributorApplicationId());
        if (distributorApplicationEntity == null) {
            return Result.newError(SHErrorCode.DISTRIBUTOR_APPLICATION_NOT_EXIST);
        }
        DistributorEntity distributorEntity = distributorDao.queryDistributorById(distributorApplicationEntity.getDistributorId());
        List<DistributorEntity> distributors = Lists.newArrayList(distributorEntity);
        Map<String, DistributorBaseInfoResult> distributorFormDataMap = distributorManager.getDistributorFormDataMap(distributorEntity.getPlanId(), null, distributors);
        DistributorBaseInfoResult baseInfoResult = distributorFormDataMap.get(distributorEntity.getId());
        result.setAvatar(baseInfoResult.getAvatar());
        result.setAvatarThumbnail(baseInfoResult.getAvatar());
        result.setBankCardNo(baseInfoResult.getBankCardNo());
        result.setBankName(baseInfoResult.getBankName());
        result.setCompanyName(baseInfoResult.getCompanyName());
        result.setCreateTime(DateUtil.format("yyyy-MM-dd HH:mm", distributorApplicationEntity.getCreateTime()));
        result.setDistributorId(distributorApplicationEntity.getDistributorId());
        result.setIdCardNumber(baseInfoResult.getIdCardNumber());
        result.setJoinDesc(baseInfoResult.getJoinDesc());
        result.setName(baseInfoResult.getName());
        result.setOperatorId(distributorApplicationEntity.getOperatorId());
        result.setPhone(baseInfoResult.getPhone());
        result.setRefuseDesc(distributorApplicationEntity.getRefuseDesc());
        result.setStatus(distributorApplicationEntity.getStatus());
        result.setVocation(baseInfoResult.getVocation());
        result.setWechat(baseInfoResult.getWechat());
        return Result.newSuccess(result);
    }

    private boolean updateDistributorStatus(String applicationId, String operatorDistributorId, Integer status, String refuseDesc){
        try {
            operatorDistributorDAO.updateDistributorStatusById(operatorDistributorId, status);
            distributorApplicationDAO.updateDistributorApplicationStatus(applicationId, refuseDesc, status);
        } catch (Exception e) {
            return false;
        }
        return true;
    }
}