package com.facishare.marketing.provider.dao.qr;

import com.facishare.marketing.provider.entity.qr.QRCodeEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface QRCodeDAO {
    @Select("SELECT * FROM qr_code WHERE id = #{id}")
    QRCodeEntity queryById(@Param("id") Integer id);

    @Select("<script> "
            + "SELECT * FROM qr_code WHERE id IN"
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<QRCodeEntity> queryByIds(@Param("ids") List<Integer> ids);

    @Insert("INSERT INTO qr_code(\"type\", \"value\", \"auth_code\", \"create_time\", \"update_time\", \"appid\") VALUES (#{type}, #{value}, #{authCode}, now(), now(), #{appid}) ON CONFLICT DO NOTHING")
    @Options(useGeneratedKeys=true, keyProperty = "id", keyColumn = "id")
    Integer insert(QRCodeEntity qrCodeEntity);


    @Select("SELECT * FROM qr_code WHERE type = #{type}")
    List<QRCodeEntity> queryByType(@Param("type") Integer type);
}
