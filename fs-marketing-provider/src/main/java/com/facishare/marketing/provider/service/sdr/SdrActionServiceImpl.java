package com.facishare.marketing.provider.service.sdr;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.sdr.SdrActionArg;
import com.facishare.marketing.api.service.sdr.SdrActionService;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.manager.sdr.SdrActionManager;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Service("sdrActionService")
@Slf4j
public class SdrActionServiceImpl implements SdrActionService {

    @Autowired
    private EIEAConverter converter;
    @Autowired
    private SdrActionManager sdrActionManager;

    // 方法映射缓存，提高性能
    private static final Map<String, Method> METHOD_CACHE = new HashMap<>();

    @Override
    public FunctionResult<Map<String, Object>> marketingInsights(SdrActionArg arg) {
        String ea = converter.enterpriseIdToAccount(arg.getTenantId());
        try {
            Map<String, Object> data = Maps.newHashMap();
            data.put("trendsSummary", JSONObject.toJSONString(sdrActionManager.userMarketingTrendsSummary(ea, arg.getVisitorId(), false)));
            data.put("insightOverview", JSONObject.toJSONString(sdrActionManager.profileInsightOverview(ea, arg.getVisitorId())));
            data.put("scoreModelResult", JSONObject.toJSONString(sdrActionManager.scoreModelResult(ea, arg.getVisitorId(), arg.getCustomerSessionId())));
            return FunctionResult.newSuccess(data);
        } catch (ServiceException e) {
            return FunctionResult.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public FunctionResult<Map<String, Object>> marketingInsightsDeepOptimized(SdrActionArg arg) {
        String ea = converter.enterpriseIdToAccount(arg.getTenantId());
        try {
            Map<String, Object> data = sdrActionManager.marketingInsightsDeepOptimized(ea, arg.getVisitorId(), arg.getCustomerSessionId());
            return FunctionResult.newSuccess(data);
        } catch (ServiceException e) {
            return FunctionResult.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public FunctionResult<Map<String, Object>> batchQuerySDRVariableData(SdrActionArg arg) {
        String ea = converter.enterpriseIdToAccount(arg.getTenantId());
        return FunctionResult.newSuccess(Maps.newHashMap(ImmutableMap.of("result", sdrActionManager.batchQuerySDRVariableData(ea, arg.getVisitorId(), arg.getChatRecords()))));
    }

    @Override
    public FunctionResult<Map<String, Object>> batchQuerySDRRelatedScripts(SdrActionArg arg) {
        String ea = converter.enterpriseIdToAccount(arg.getTenantId());
        return FunctionResult.newSuccess(Maps.newHashMap(ImmutableMap.of("result", JSONObject.toJSONString(sdrActionManager.batchQuerySDRRelatedScripts(ea, arg.getCommunicationActions(), arg.getIntentionTypes(), arg.getModelId())))));
    }


}
