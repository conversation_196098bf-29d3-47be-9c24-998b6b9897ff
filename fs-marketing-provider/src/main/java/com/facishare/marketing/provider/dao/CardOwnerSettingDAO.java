package com.facishare.marketing.provider.dao;


import com.facishare.marketing.provider.entity.CardOwnerSettingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface CardOwnerSettingDAO {

    @Select("SELECT * FROM card_owner_setting WHERE uid = #{uid}")
    CardOwnerSettingEntity queryCardSettingByUid(@Param("uid") String uid);

    @Select("SELECT * FROM card_owner_setting WHERE id = #{id}")
    CardOwnerSettingEntity queryCardSettingById(@Param("id") String id);

    @Insert("INSERT INTO card_owner_setting (id, uid, create_time, update_time, card_display) VALUES (#{id}, #{uid}, now(), now(), #{cardDisplay}) ON CONFLICT DO NOTHING")
    int insert(CardOwnerSettingEntity entity);

    @Update("UPDATE card_owner_setting SET card_display = #{cardDisplay} WHERE uid = #{uid}")
    int updateByUid(@Param("uid")String uid,@Param("cardDisplay")Boolean cardDisplay);
}
