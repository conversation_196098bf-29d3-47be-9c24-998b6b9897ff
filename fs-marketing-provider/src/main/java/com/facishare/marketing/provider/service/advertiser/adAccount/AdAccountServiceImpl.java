package com.facishare.marketing.provider.service.advertiser.adAccount;

import com.facishare.marketing.api.result.advertiser.CheckMarketingIntegrationResult;
import com.facishare.marketing.api.result.advertiser.headlines.QueryAdLeadsMappingDataResult;
import com.facishare.marketing.api.result.advertiser.tencent.DeleteAdAccountResult;
import com.facishare.marketing.api.result.baidu.*;
import com.facishare.marketing.api.service.baidu.AdAccountService;
import com.facishare.marketing.api.vo.advertiser.headlines.AdLeadsMappingVO;
import com.facishare.marketing.api.vo.advertiser.headlines.QueryAdLeadsMappingVO;
import com.facishare.marketing.api.vo.baidu.*;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.advertiser.AdMarketingActionEnum;
import com.facishare.marketing.common.enums.baidu.AccountStatusEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.baidu.DataRefreshStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.baidu.GetAccountInfoResultData;
import com.facishare.marketing.provider.baidu.RequestResult;
import com.facishare.marketing.provider.baidu.ResultHeader;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsMappingDataDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduDataStatusDAO;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduDataStatusEntity;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingHandlerActionManager;
import com.facishare.marketing.provider.manager.advertiser.AdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.AccountApiManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by ranluch on 2019/11/26.
 */
@Slf4j
@Service("adAccountService")
public class AdAccountServiceImpl implements AdAccountService {
    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private AccountApiManager accountApiManager;
    @Autowired
    private BaiduDataStatusDAO baiduDataStatusDAO;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private AdMarketingHandlerActionManager adMarketingHandlerActionManager;
    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private AdLeadsMappingDataDAO adLeadsMappingDataDAO;

    @Autowired
    private RedisManager redisManager;

    //TODO 这个接口要改掉
    @Override
    public Result<BindAccountResult> bindAccount(BindAccountVO vo) {
        BindAccountResult bindResult = new BindAccountResult();
        boolean isNew = false;
        RequestResult<GetAccountInfoResultData> requestResult  = accountApiManager.getAccountInfo(vo.getUsername().trim(), vo.getPassword().trim(), vo.getToken().trim(), null);
        if (Objects.isNull(requestResult)) {
            return Result.newError(SHErrorCode.BAIDU_API_REQUEST_ERROR);
        }
        if (requestResult.isSuccess()) {
            List<GetAccountInfoResultData> dataList = requestResult.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                return Result.newError(SHErrorCode.BAIDU_API_REQUEST_DATA_EMPTY);
            }
            GetAccountInfoResultData data = dataList.get(0);
            if (data.getUserId() == null || data.getUserStat() == null) {
                return Result.newError(SHErrorCode.BAIDU_API_REQUEST_DATA_ERROR);
            }

            AdAccountEntity adAccountEntity = adAccountManager.queryAccountByEaAndAccountId(vo.getEa(), data.getUserId(), vo.getSource());
            if (adAccountEntity == null) {
                isNew = true;
                adAccountEntity = new AdAccountEntity();
                adAccountEntity.setEa(vo.getEa());
                adAccountEntity.setId(UUIDUtil.getUUID());
            }
            bindResult.setCheckStatus(requestResult.getHeader().getStatus());
            bindResult.setCheckDesc(requestResult.getHeader().getDesc());
            adAccountEntity.setUsername(vo.getUsername().trim());
            adAccountEntity.setPassword(vo.getPassword().trim());
            adAccountEntity.setAccessKey(vo.getToken().trim());
            adAccountEntity.setAccountId(data.getUserId());
            adAccountEntity.setUserStat(data.getUserStat());
            adAccountEntity.setBalance(data.getBalance());
            adAccountEntity.setPcBalance(data.getPcBalance());
            adAccountEntity.setMobileBalance(data.getMobileBalance());
            adAccountEntity.setBudget(data.getBudget());
            adAccountEntity.setBudgetType(data.getBudgetType());
            adAccountEntity.setCost(data.getCost());
            adAccountEntity.setToken(vo.getToken());
            adAccountEntity.setSource(vo.getSource());
            adAccountEntity.setStatus(AccountStatusEnum.REFRESHING.getStatus());
            if (isNew) {
                boolean dbResult = adAccountManager.addAccount(adAccountEntity);
                if (!dbResult) {
                    return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
                }
                AdAccountEntity accountInfo = adAccountManager.queryAccountByEaAndAccountId(vo.getEa(), data.getUserId(), vo.getSource());
                // 拉取百度广告数据
                ThreadPoolUtils.executeWithTraceContext(() ->
                {
                    baiduAdMarketingManager.refreshAllData(vo.getEa(), accountInfo.getId(), vo.getSource());
                    RefreshDataVO refreshDataVO = new RefreshDataVO();
                    refreshDataVO.setEa(vo.getEa());
                    refreshDataVO.setSource(vo.getSource());
                    refreshDataVO.setAdAccountId(accountInfo.getId());
                    refreshData(refreshDataVO);
                    accountInfo.setStatus(AccountStatusEnum.NORMAL.getStatus());
                    // 拉取成功更新账户状态为正常
                    adAccountManager.updateAdAccountStatus(accountInfo);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
            } else {
                adAccountManager.updateAccount(adAccountEntity);
            }

        } else if (requestResult.getHeader() != null) {
            List<ResultHeader.Failure> failures = requestResult.getHeader().getFailures();
            if (CollectionUtils.isNotEmpty(failures)) {
                bindResult.setCode(failures.get(0).getCode());
                bindResult.setMessage(failures.get(0).getMessage());
            }
            bindResult.setCheckStatus(requestResult.getHeader().getStatus());
            bindResult.setCheckDesc(requestResult.getHeader().getDesc());
            return Result.newError(SHErrorCode.BAIDU_ACCOUNT_AUTHORIZED_DENIED);
        } else {
            return Result.newError(SHErrorCode.BAIDU_API_REQUEST_ERROR);
        }
        return Result.newSuccess(bindResult);
    }




    @Override
    public Result<List<QueryAccountInfoResult>> queryAccountInfo(QueryAccountInfoVO vo) {
        AdMarketingManager adMarketingManager = adMarketingHandlerActionManager.getAdMarketingActionManager(vo.getSource());
        return adMarketingManager.queryAccountInfo(vo);
    }




    @Override
    public Result<RefreshDataResult> refreshData(RefreshDataVO vo) {
        AdAccountEntity prototypeRoomEntity = adAccountManager.getAdPrototypeRoomAccountById(vo.getEa(), vo.getAdAccountId());
        if (prototypeRoomEntity != null) {
            // 样板间账号直接返回
            return Result.newSuccess();
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryEnableAccountById(vo.getAdAccountId());
        if (adAccountEntity == null) {
            return Result.newError(SHErrorCode.BAIDU_ACCOUNT_NOT_BIND);
        }
        if (!adAccountEntity.getStatus().equals(AccountStatusEnum.NORMAL.getStatus()) && !adAccountEntity.getStatus().equals(AccountStatusEnum.REFRESHING.getStatus())) {
            return Result.newError(SHErrorCode.BAIDU_ACCOUNT_STOP_USE);
        }
        boolean lock = redisManager.lockAdRefreshData(vo.getEa(), vo.getAdAccountId());
        if (!lock) {
            log.warn("refreshData get lock fail, arg: {}", vo);
            return Result.newSuccess();
        }
        RefreshDataResult dataResult = new RefreshDataResult();
        try {
            AdMarketingManager adMarketingActionManager = adMarketingHandlerActionManager.getAdMarketingActionManager(vo.getSource());
            SHErrorCode shErrorCode = adMarketingActionManager.isValidAccount(adAccountEntity);
            if (shErrorCode != SHErrorCode.SUCCESS) {
                return Result.newError(shErrorCode);
            }
            BaiduDataStatusEntity refreshStatusEntity = baiduDataStatusDAO.queryRefreshStatus(vo.getEa(), vo.getAdAccountId(), AdSourceEnum.getSourceByValue(vo.getSource()));
            int day = vo.getDay() == null ? 30 : vo.getDay();
            if (refreshStatusEntity == null) {
                refreshStatusEntity = new BaiduDataStatusEntity();
                refreshStatusEntity.setId(UUIDUtil.getUUID());
                refreshStatusEntity.setEa(vo.getEa());
                refreshStatusEntity.setAdAccountId(vo.getAdAccountId());
                refreshStatusEntity.setRefreshTime(new Date());
                refreshStatusEntity.setSource(adAccountEntity.getSource());
                dataResult.setStatus(DataRefreshStatusEnum.REFRESHING.getStatus());
                dataResult.setRefreshTime(System.currentTimeMillis());
                baiduDataStatusDAO.addAdDataStatus(refreshStatusEntity);
                campaignDataManager.refreshByEaAndAdAccountId(vo.getEa(), refreshStatusEntity.getAdAccountId(), refreshStatusEntity.getSource(), day);
            } else if (refreshStatusEntity.getRefreshStatus().equals(DataRefreshStatusEnum.STOP.getStatus()) || refreshStatusEntity.getRefreshStatus().equals(DataRefreshStatusEnum.REFRESHING.getStatus())) {
                dataResult.setStatus(refreshStatusEntity.getRefreshStatus());
                dataResult.setRefreshTime(refreshStatusEntity.getRefreshTime() != null ? refreshStatusEntity.getRefreshTime().getTime() : null);
                dataResult.setRefreshSuccessTime(refreshStatusEntity.getRefreshSuccessTime() != null ? refreshStatusEntity.getRefreshSuccessTime().getTime() : null);
            } else {
                dataResult.setStatus(DataRefreshStatusEnum.REFRESHING.getStatus());
                dataResult.setRefreshTime(System.currentTimeMillis());
                dataResult.setRefreshSuccessTime(refreshStatusEntity.getRefreshSuccessTime() != null ? refreshStatusEntity.getRefreshSuccessTime().getTime() : null);
                campaignDataManager.refreshByEaAndAdAccountId(vo.getEa(), refreshStatusEntity.getAdAccountId(), refreshStatusEntity.getSource(), day);
            }
        } catch (Exception e) {
            log.error("refreshData is error, arg: {}", vo, e);
        } finally {
            redisManager.unLockAdRefreshData(vo.getEa(), vo.getAdAccountId());
        }
        return Result.newSuccess(dataResult);
    }




    @Override
    public Result<GetDataOverviewResult> getDataOverview(GetDataOverviewVO vo) {
        if (!"0".equals(vo.getSource())) {
            AdMarketingManager adMarketingActionManager = adMarketingHandlerActionManager.getAdMarketingActionManager(vo.getSource());
            return adMarketingActionManager.getDataOverview(vo);
        }
        GetDataOverviewResult data = new GetDataOverviewResult();
        vo.setAdAccountId(null);
        GetDataOverviewResult data0 = adMarketingHandlerActionManager.getAdMarketingActionManager(AdMarketingActionEnum.BAIDU_AD_MARKETING.getSource()).getDataOverview(vo).getData();
        GetDataOverviewResult data1 = adMarketingHandlerActionManager.getAdMarketingActionManager(AdMarketingActionEnum.TENCENT_AD_MARKETING.getSource()).getDataOverview(vo).getData();
        GetDataOverviewResult data2 = adMarketingHandlerActionManager.getAdMarketingActionManager(AdMarketingActionEnum.HEADLINES_AD_MARKETING.getSource()).getDataOverview(vo).getData();
        data.setCost(BigDecimal.valueOf((data0.getCost() == null ? 0 : data0.getCost()) + (data1.getCost() == null ? 0 : data1.getCost()) + (data2.getCost() == null ? 0 : data2.getCost())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        data.setPv((data0.getPv() == null ? 0 : data0.getPv()) + (data1.getPv() == null ? 0 : data1.getPv()) + (data2.getPv() == null ? 0 : data2.getPv()));
        data.setClick((data0.getClick() == null ? 0 : data0.getClick()) + (data1.getClick() == null ? 0 : data1.getClick()) + (data2.getClick() == null ? 0 : data2.getClick()));
        data.setLeads((data0.getLeads() == null ? 0 : data0.getLeads()) + (data1.getLeads() == null ? 0 : data1.getLeads()) + (data2.getLeads() == null ? 0 : data2.getLeads()));
        data.setClickPrice(BigDecimal.valueOf((data0.getClickPrice() == null ? 0 : data0.getClickPrice()) + (data1.getClickPrice() == null ? 0 : data1.getClickPrice()) + (data2.getClickPrice() == null ? 0 : data2.getClickPrice())).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        return Result.newSuccess(data);
    }

    @Override
    public Result<UnboundAccountResult> unboundAccount(UnboundAccountVO vo) {
        AdAccountEntity prototypeRoomAccountEntity = adAccountManager.getAdPrototypeRoomAccountById(vo.getEa(), vo.getAdAccountId());
        if (prototypeRoomAccountEntity != null) {
            return Result.newError(SHErrorCode.PROTOTYPE_ACCOUNT_FORBID_UNBIND);
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(vo.getAdAccountId());
        if (adAccountEntity == null || adAccountEntity.getStatus().equals(AccountStatusEnum.STOP.getStatus())) {
            return Result.newError(SHErrorCode.BAIDU_ACCOUNT_NOT_BIND);
        }
        adAccountEntity.setStatus(AccountStatusEnum.STOP.getStatus());
        adAccountManager.updateAdAccountStatus(adAccountEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<RestartAccountResult> restartAccount(RestartAccountVO vo) {
        AdAccountEntity prototypeAccount = adAccountManager.getAdPrototypeRoomAccountById(vo.getEa(), vo.getAdAccountId());
        if (prototypeAccount != null) {
            return Result.newSuccess();
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(vo.getAdAccountId());
        if(adAccountEntity.getStatus().equals(AccountStatusEnum.STOP.getStatus())) {
            adAccountEntity.setStatus(AccountStatusEnum.NORMAL.getStatus());
            adAccountManager.updateAdAccountStatus(adAccountEntity);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<CheckMarketingIntegrationResult> checkMarketingIntegration(String ea) {
        CheckMarketingIntegrationResult result = new CheckMarketingIntegrationResult();
        Boolean res = appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.MARKETING_CRM_INTEGRATING);
        result.setHasMarketingIntegration(res);
        return Result.newSuccess(result);
    }


    @Override
    public Result updateAdLeadsMappingData(AdLeadsMappingVO vo) {
        List<AdLeadsMappingDataEntity> adLeadsMappingDataEntities = adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(vo.getEa(), vo.getSource());
        if (CollectionUtils.isEmpty(adLeadsMappingDataEntities)) {
            adLeadsMappingDataDAO.addAdLeadsMappingData(UUIDUtil.getUUID(), vo.getEa(), vo.getFsUserId(), vo.getCrmRecordType(), vo.getSource(), vo.getCrmPoolId(), vo.getCustomFuncApiName());
        } else {
            for (AdLeadsMappingDataEntity adLeadsMappingDataEntity : adLeadsMappingDataEntities) {
                AdLeadsMappingDataEntity leadsMappingDataEntity = new AdLeadsMappingDataEntity();
                leadsMappingDataEntity.setId(adLeadsMappingDataEntity.getId());
                leadsMappingDataEntity.setEa(adLeadsMappingDataEntity.getEa());
                leadsMappingDataEntity.setCreateBy(vo.getFsUserId());
                leadsMappingDataEntity.setCrmPoolId(vo.getCrmPoolId());
                leadsMappingDataEntity.setCrmRecordType(vo.getCrmRecordType());
                leadsMappingDataEntity.setCustomFuncApiName(vo.getCustomFuncApiName());
                leadsMappingDataEntity.setUpdateTime(new Date());
                leadsMappingDataEntity.setSource(adLeadsMappingDataEntity.getSource());
                adLeadsMappingDataDAO.updateLeadsMappingData(leadsMappingDataEntity);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<QueryAdLeadsMappingDataResult> queryAdLeadsMappingData(QueryAdLeadsMappingVO vo) {
        List<AdLeadsMappingDataEntity> leadsMappingDataEntities = adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(vo.getEa(), vo.getSource());
        if (CollectionUtils.isEmpty(leadsMappingDataEntities)) {
            return Result.newSuccess();
        }
        QueryAdLeadsMappingDataResult result = new QueryAdLeadsMappingDataResult();
        AdLeadsMappingDataEntity mappingDataEntity = leadsMappingDataEntities.get(0);
        result.setEa(mappingDataEntity.getEa());
        result.setCrmPoolId(mappingDataEntity.getCrmPoolId());
        result.setCustomFuncApiName(mappingDataEntity.getCustomFuncApiName());
        result.setCrmRecordType(mappingDataEntity.getCrmRecordType());
        return Result.newSuccess(result);
    }

    @Override
    public Result<DeleteAdAccountResult> deleteAdAccount(String ea, String adAccountId) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
        if (adAccountEntity == null || adAccountEntity.getStatus().equals(AccountStatusEnum.DELETED.getStatus())) {
            return Result.newError(SHErrorCode.BAIDU_ACCOUNT_DELETED);
        }
        adAccountEntity.setStatus(AccountStatusEnum.DELETED.getStatus());
        adAccountManager.updateAdAccountStatus(adAccountEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<List<AdAccountVO>> queryAllAdAccount(String ea, Integer fsUserId) {
        List<AdAccountVO> result = Lists.newArrayList();
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEa(ea, true);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return Result.newSuccess(result);
        }
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            AdAccountVO adAccountVO = new AdAccountVO();
            adAccountVO.setId(adAccountEntity.getId());
            adAccountVO.setName(adAccountEntity.getUsername());
            adAccountVO.setSource(adAccountEntity.getSource());
            adAccountVO.setStatus(adAccountEntity.getStatus());
            result.add(adAccountVO);
        }
        return Result.newSuccess(result);
    }
}
