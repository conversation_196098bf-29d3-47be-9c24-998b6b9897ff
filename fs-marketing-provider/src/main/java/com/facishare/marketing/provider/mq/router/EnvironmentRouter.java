package com.facishare.marketing.provider.mq.router;

import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.mq.config.EnvironmentConfig;
import com.facishare.marketing.provider.mq.sender.MarketingMessageSender;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 环境路由器
 * 负责根据EA和消息类型确定目标环境，并获取对应的消息发送器
 * 注意：只需配置非正式环境，没有匹配到的EA自动走正式环境
 */
@Component
@Slf4j
public class EnvironmentRouter {

    @Autowired
    private Map<String, MarketingMessageSender> senderMap;

    private volatile List<EnvironmentConfig> environmentConfigs;

    @PostConstruct
    public void init() {
        ConfigFactory.getInstance().getConfig("fs-marketing-provider", this::reloadConfig);
    }

    /**
     * 环境配置（包含发送器映射）
     * 格式: [{"name":"gray1","eaList":["ea1“,”ea2"],"defaultSender":"gray1MessageSender","handlerSenderMapping":{"WxUserActionMessageHandler":"gray1LightMessageSender"}}]
     */
    private void reloadConfig(IConfig config) {
        String marketingEnvironmentConfig = config.get("marketing_environment_config");
        if (ObjectUtils.isEmpty(marketingEnvironmentConfig)) {
            return;
        }
        try {
            synchronized (this) {
                environmentConfigs = GsonUtil.getGson().fromJson(marketingEnvironmentConfig,
                        new TypeToken<List<EnvironmentConfig>>() {
                        }.getType());
            }
        } catch (Exception e) {
            log.error("EnvironmentRouter update environmentConfigs config error,config file :{} ", config.getName(), e);
        }
    }

    /**
     * 确定EA对应的目标环境
     *
     * @param ea 企业账号
     * @return 环境名称，如果没有匹配到则返回null表示正式环境
     */
    public String determineEnvironment(String ea) {
        if (StringUtils.isEmpty(ea)) {
            return null; // 正式环境
        }
        try {
            // 如果没有配置环境，直接返回null（正式环境）
            if (CollectionUtils.isEmpty(environmentConfigs)) {
                return null;
            }
            // 按优先级排序，优先级数字越小越优先
            List<EnvironmentConfig> sortedConfigs = environmentConfigs.stream()
                    .filter(EnvironmentConfig::isEnabled)
                    .sorted(Comparator.comparingInt(EnvironmentConfig::getPriority))
                    .collect(Collectors.toList());
            // 遍历环境配置，找到第一个匹配的环境
            for (EnvironmentConfig config : sortedConfigs) {
                if (isEaMatchEnvironment(ea, config)) {
                    log.debug("EA {} matched environment: {}", ea, config.getName());
                    return config.getName();
                }
            }
        } catch (Exception e) {
            log.warn("Failed to determine environment for EA: {}, fallback to production", ea, e);
        }
        return null; // 正式环境
    }

    /**
     * 获取指定环境和Handler对应的消息发送器
     *
     * @param environment 环境名称
     * @param handlerName Handler类名
     * @param tag         消息标签
     * @return 消息发送器，如果找不到则返回null
     */
    public MarketingMessageSender getSender(String environment, String handlerName, String tag) {
        try {
            // 查找对应环境的配置
            EnvironmentConfig envConfig = environmentConfigs.stream()
                    .filter(config -> environment.equals(config.getName()))
                    .findFirst()
                    .orElse(null);
            if (envConfig != null) {
                String senderName = envConfig.getSenderName(handlerName, tag);
                if (StringUtils.isNotEmpty(senderName)) {
                    MarketingMessageSender sender = senderMap.get(senderName);
                    if (sender != null) {
                        return sender;
                    } else {
                        log.warn("Sender bean not found: {} for environment {}", senderName, environment);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get sender for environment {} handler {} tag {}", environment, handlerName, tag, e);
        }
        return null;
    }

    /**
     * 判断EA是否匹配指定环境
     */
    private boolean isEaMatchEnvironment(String ea, EnvironmentConfig config) {
        if (CollectionUtils.isEmpty(config.getEaList())) {
            return false;
        }
        return config.getEaList().stream().map(String::trim).anyMatch(e -> e.equals(ea));
    }
} 