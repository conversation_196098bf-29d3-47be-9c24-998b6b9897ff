package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.AddCustomizeFormDataArg;
import com.facishare.marketing.api.arg.GetCustomizeFormDataByIdArg;
import com.facishare.marketing.api.arg.HexagonhomepageDetailArg;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.result.AddCustomizeFormDataResult;
import com.facishare.marketing.api.result.CustomizeFormDataDetailResult;
import com.facishare.marketing.api.result.LeadPoolResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.hexagon.GetPageDetailResult;
import com.facishare.marketing.api.result.hexagon.GetSiteByEaUnitResult;
import com.facishare.marketing.api.result.hexagon.SitePreviewResult;
import com.facishare.marketing.api.result.live.ChannelsAccountResult;
import com.facishare.marketing.api.service.CrmService;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.hexagon.HexagonPreviewEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.qr.QRCodeTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.ImageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO;
import com.facishare.marketing.provider.dao.FileToHexagonDAO;
import com.facishare.marketing.provider.dao.MarketingObjectActivityStatisticDAO;
import com.facishare.marketing.provider.dao.cta.CtaDAO;
import com.facishare.marketing.provider.dao.hexagon.*;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager;
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao;
import com.facishare.marketing.provider.dto.ObjectStatisticData;
import com.facishare.marketing.provider.dto.TargetPhotoPathDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity;
import com.facishare.marketing.provider.entity.MaterialRelationEntity;
import com.facishare.marketing.provider.entity.FileToHexagonEntity;
import com.facishare.marketing.provider.entity.MemberConfigEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.cta.CtaEntity;
import com.facishare.marketing.provider.entity.cta.CtaRelationEntity;
import com.facishare.marketing.provider.entity.hexagon.*;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.live.ChannelsManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class HexagonManager {
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private CrmService crmService;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private QRCodeManager qrCodeManager;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;
    @Autowired
    private MemberConfigDao memberConfigDao;
    @Autowired
    private MemberAccessibleObjectDao memberAccessibleObjectDao;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MaterialRelationDao materialRelationDao;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private MarketingObjectActivityStatisticDAO marketingObjectActivityStatisticDAO;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private MarketingObjectAmountStatisticDao marketingObjectAmountStatisticDao;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private HexagonSiteDAOManager hexagonSiteDAOManager;
    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;

    @Autowired
    private ChannelsManager channelsManager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private FileToHexagonDAO fileToHexagonDAO;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Autowired
    private CtaDAO ctaDAO;

    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;
    @Autowired
    private PhotoDAO photoDAO;
    @ReloadableProperty("host")
    private String host;
    @ReloadableProperty("qywx.group.message.default.cover")
    private String groupMessageDefaultCoverPath;
    @ReloadableProperty("picture.preview.url.cdn")
    private String cdnSharePath;
    public static final int COPY_FROM_HEXAGON = 1;   //从微页面复制
    public static final int COPY_FROM_TEMPLATE = 2;  //从模板复制

    public Result<Map<String, SitePreviewResult>> batchGetHexagonSiteQRCodeURL(List<String> ids, String ea) {
        Map<String, SitePreviewResult> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(ids)) {
            return Result.newSuccess(resultMap);
        }

        for (String id : ids) {
            Result<SitePreviewResult> getHexagonSiteQRCodeURLResult = getHexagonSiteQRCodeURL(id, ea, null, null);
            if (getHexagonSiteQRCodeURLResult.isSuccess()) {
                resultMap.put(id, getHexagonSiteQRCodeURLResult.getData());
            }
        }

        return Result.newSuccess(resultMap);
    }

    public Map<String, SitePreviewResult> batchGetHexagonSiteH5PreviewQRCodeURL(String ea, List<String>ids){
        List<TargetPhotoPathDTO> photoPathDTOList = photoManager.listPathByTargetTypeAndIds(ids, PhotoTargetTypeEnum.HEXAGON_SITE_H5_QRCODE.getType());
        if (CollectionUtils.isEmpty(photoPathDTOList)){
            return null;
        }

        Map<String, SitePreviewResult> resultMap = new HashMap<>();
        List<String> paths = photoPathDTOList.stream().map(TargetPhotoPathDTO::getPath).collect(Collectors.toList());
        Map<String, TargetPhotoPathDTO> dtoMap = photoPathDTOList.stream().collect(Collectors.toMap(TargetPhotoPathDTO::getTargetId, Function.identity(), (v1,v2)->v2));
        Map<String,String> urlMap = fileV2Manager.batchGetUrlByPath(paths, ea, false);
        if (urlMap == null){
            return null;
        }

        photoPathDTOList.forEach(targetPhotoPathDTO -> {
            if (urlMap.get(targetPhotoPathDTO.getPath()) != null){
                targetPhotoPathDTO.setUrl(urlMap.get(targetPhotoPathDTO.getPath()));
                targetPhotoPathDTO.setThumbnailUrl(targetPhotoPathDTO.getUrl());
            }
        });

        ids.forEach(id ->{
            if (dtoMap.get(id) != null){
                SitePreviewResult sitePreviewResult = new SitePreviewResult();
                sitePreviewResult.setH5QRUrl(dtoMap.get(id).getUrl());
                sitePreviewResult.setH5QRAPath(dtoMap.get(id).getPath());
                resultMap.put(id, sitePreviewResult);
            }
        });

        return resultMap;
    }

    public Result<SitePreviewResult> getHexagonSiteQRCodeURL(String id, String ea, String channelValue, Map<String, String> extraParam) {
        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("type", HexagonPreviewEnum.SITE_PREVIEW.getType());
        valueMap.put("id", id);
        if (channelValue != null){
            valueMap.put("spreadChannel", channelValue);
        }
        if (MapUtils.isNotEmpty(extraParam)) {
            valueMap.putAll(extraParam);
        }
        String value = GsonUtil.toJson(valueMap);

        String h5QRUrl = null;
        String h5QRAPath = null;
        String miniappQRUrl = null;
        String miniappQRAPath = null;
        String bdQRUrl = null;
        String bdQRAPath = null;


        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.H5_HEXAGON_PREVIEW.getType());
            data.setValue(value);
            data.setEa(ea);
            data.setLengthOfSide(0);
            QRCodeManager.CreateQRCodeResult h5CreateQRCodeResult = qrCodeManager.createQRCode(data);
            if (null == h5CreateQRCodeResult) {
                return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
            }

            h5QRUrl = h5CreateQRCodeResult.getQrCodeUrl();
            h5QRAPath = h5CreateQRCodeResult.getQrCodeApath();
            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.HEXAGON_SITE_H5_QRCODE, id, h5CreateQRCodeResult.getQrCodeApath(), h5CreateQRCodeResult.getQrCodeApath())) {
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } catch (Exception e) {
            log.warn("HexagonManager.getHexagonSiteQRCodeURL error e:{}", e);
        }


        // 现小程序可实时更换二维码需重绘
        try {
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.HEXAGON_PREVIEW.getType());
            data.setValue(value);
            data.setEa(ea);
            data.setLengthOfSide(0);
            QRCodeManager.CreateQRCodeResult miniAppcreateQRCodeResult = qrCodeManager.createQRCode(data);
            if (null == miniAppcreateQRCodeResult) {
                return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
            }

            miniappQRUrl = miniAppcreateQRCodeResult.getQrCodeUrl();
            miniappQRAPath = miniAppcreateQRCodeResult.getQrCodeApath();

            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.HEXAGON_SITE_MP_QRCODE, id, miniAppcreateQRCodeResult.getQrCodeApath(), miniAppcreateQRCodeResult.getQrCodeApath())) {
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } catch (Exception e) {
            log.warn("HexagonManager.getHexagonSiteQRCodeURL error e:{}", e);
        }

        try {
            PhotoEntity bdPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_SITE_BD_QRCODE.getType(), id,ea);
            if (null == bdPhotoEntity) {
                String url = "baiduboxapp://swan/********************************/pages/index/index?siteId={siteId}&type={type}";
                url = url.replace("{siteId}", id);
                url = url.replace("{type}", Integer.toString(HexagonPreviewEnum.SITE.getType()));
                QRCodeManager.CreateQRCodeResult createQRCodeResult = qrCodeManager.createCustomQRCode(QRCodeTypeEnum.BD_HEXAGON_SITE.getType(), url, 0, ea);
                if (null == createQRCodeResult) {
                    return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
                }

                bdQRUrl = createQRCodeResult.getQrCodeUrl();
                bdQRAPath = createQRCodeResult.getQrCodeApath();
                if (!photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.HEXAGON_SITE_BD_QRCODE, id, createQRCodeResult.getQrCodeApath(), createQRCodeResult.getQrCodeApath())) {
                    return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
                }
            } else {
                bdQRUrl = bdPhotoEntity.getUrl();
                bdQRAPath = bdPhotoEntity.getPath();
            }
        } catch (Exception e) {
            log.warn("HexagonManager.getHexagonSiteQRCodeURL error e:{}", e);
        }

        SitePreviewResult result = new SitePreviewResult();
        result.setH5QRUrl(h5QRUrl);
        result.setMiniappQRUrl(miniappQRUrl);
        result.setBdQRUrl(bdQRUrl);
        result.setBdQRAPath(bdQRAPath);
        result.setH5QRAPath(h5QRAPath);
        result.setMiniappQRAPath(miniappQRAPath);

        return Result.newSuccess(result);
    }

    public Result<SitePreviewResult> getHexagonTemplateSiteQRCodeURL(String id, String ea) {
        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("type", HexagonPreviewEnum.TEMPLATE_SITE.getType());
        valueMap.put("id", id);
        String value = GsonUtil.toJson(valueMap);

        String h5QRUrl = null;
        String miniappQRUrl = null;
        String bdQRUrl = null;

        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(QRCodeTypeEnum.H5_HEXAGON_TEMPLATE_PREVIEW.getType());
        data.setValue(value);
        data.setLengthOfSide(0);
        data.setEa(ea);
        QRCodeManager.CreateQRCodeResult h5CreateQRCodeResult = qrCodeManager.createQRCode(data);
        if (null == h5CreateQRCodeResult) {
            return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
        }
        h5QRUrl = h5CreateQRCodeResult.getQrCodeUrl();

        if (!photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.HEXAGON_TEMPLATE_SITE_H5_QRCODE, id, h5CreateQRCodeResult.getQrCodeApath(), h5CreateQRCodeResult.getQrCodeApath())) {
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }

        // 现小程序可实时更换二维码需重绘
        data.setType(QRCodeTypeEnum.HEXAGON_TEMPLATE_PREVIEW.getType());
        QRCodeManager.CreateQRCodeResult mpCreateQRCodeResult = qrCodeManager.createQRCode(data);
        if (null == mpCreateQRCodeResult) {
            return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
        }

        miniappQRUrl = mpCreateQRCodeResult.getQrCodeUrl();

        if (!photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.HEXAGON_TEMPLATE_SITE_MP_QRCODE, id, mpCreateQRCodeResult.getQrCodeApath(), mpCreateQRCodeResult.getQrCodeApath())) {
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }


        /*PhotoEntity mpPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_TEMPLATE_SITE_MP_QRCODE.getType(), id);
        if (null == mpPhotoEntity) {
            QRCodeManager.CreateQRCodeResult createQRCodeResult = qrCodeManager.createQRCode(QRCodeTypeEnum.HEXAGON_TEMPLATE_PREVIEW.getType(), value, 0, ea);
            if (null == createQRCodeResult) {
                return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
            }

            miniappQRUrl = createQRCodeResult.getQrCodeUrl();

            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(PhotoTargetTypeEnum.HEXAGON_TEMPLATE_SITE_MP_QRCODE, id, createQRCodeResult.getQrCodeApath(), createQRCodeResult.getQrCodeApath())) {
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else {
            miniappQRUrl = mpPhotoEntity.getUrl();
        }*/

        PhotoEntity bdPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_TEMPLATE_SITE_BD_QRCODE.getType(), id,ea);
        if (null == bdPhotoEntity) {
            String url = "baiduboxapp://swan/********************************/pages/index/index?siteId={siteId}&type={type}";
            url = url.replace("{siteId}", id);
            url = url.replace("{type}", Integer.toString(HexagonPreviewEnum.TEMPLATE_SITE.getType()));
            QRCodeManager.CreateQRCodeResult createQRCodeResult = qrCodeManager.createCustomQRCode(QRCodeTypeEnum.BD_HEXAGON_TEMPLATE_SITE.getType(), url, 0, null);
            if (null == createQRCodeResult) {
                return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
            }

            bdQRUrl = createQRCodeResult.getQrCodeUrl();

            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.HEXAGON_TEMPLATE_SITE_BD_QRCODE, id, createQRCodeResult.getQrCodeApath(), createQRCodeResult.getQrCodeApath())) {
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else {
            bdQRUrl = bdPhotoEntity.getUrl();
        }

        SitePreviewResult result = new SitePreviewResult();
        result.setH5QRUrl(h5QRUrl);
        result.setMiniappQRUrl(miniappQRUrl);
        result.setBdQRUrl(bdQRUrl);

        return Result.newSuccess(result);
    }

    // 获取微页面的封面
    public Map<String, String> getHexagonSiteCoverUrlMap(String ea, List<String> hexagonSiteIdList) {
        Map<String, String> coverApathMap = new HashMap<>();
        List<String> apathList = new ArrayList<>();
        //添加默认的封面图
        apathList.add(groupMessageDefaultCoverPath);
        List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(hexagonSiteIdList);
        if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
            for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteCoverListDTOList) {
                apathList.add(hexagonSiteListDTO.getSharePicH5Apath());
                coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
            }
        }
        Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
        Map<String, String> resultMap = Maps.newHashMap();
        for (String id : hexagonSiteIdList) {
            if (null != coverUrlMap) {
                String apath = coverApathMap.get(id);
                if (StringUtils.isNotBlank(apath)) {
                    resultMap.put(id, coverUrlMap.get(apath));
                } else {
                    resultMap.put(id, coverUrlMap.get(groupMessageDefaultCoverPath));
                }
            }
        }
        return resultMap;
    }

    public List<GetSiteByEaUnitResult> getGetSiteByEaUnitResults(String ea, Integer userId, List<HexagonSiteEntity> hexagonSiteEntityList) {
        List<GetSiteByEaUnitResult> resultList = new ArrayList<>();
        Map<String, Integer> materielMap = Maps.newHashMap();
//        Map<String, String> hexagonSiteObjectMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(hexagonSiteEntityList)) {
            List<Integer> userIds = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            for (HexagonSiteEntity hexagonSiteEntity : hexagonSiteEntityList) {
                ids.add(hexagonSiteEntity.getId());
                userIds.add(hexagonSiteEntity.getCreateBy());
                userIds.add(hexagonSiteEntity.getUpdateBy());
//                // 只是微页面的情况
                materielMap.put(hexagonSiteEntity.getId(), ObjectTypeEnum.HEXAGON_SITE.getType());
            }
            Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> marketingActivityObjectInfoMap = null;
            if (userId != null) {
                marketingActivityObjectInfoMap = marketingActivityManager.getActivityIdsByObject(materielMap, ea, userId);
            }
            Map<String, FileToHexagonEntity> fileToHxagonMap = new HashMap<>();
            List<FileToHexagonEntity> siteIds = fileToHexagonDAO.getBySiteIds(ids);
            if (CollectionUtils.isNotEmpty(siteIds)) {
                fileToHxagonMap = siteIds.stream().collect(Collectors.toMap(FileToHexagonEntity::getHexagonSiteId, Function.identity(), (k1, k2) -> k1));
            }

            Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);

            Map<String, String> coverApathMap = new HashMap<>();
            List<String> apathList = new ArrayList<>();
            //添加默认的封面图
            apathList.add(groupMessageDefaultCoverPath);
            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(ids);
            if (null != hexagonSiteCoverListDTOList && CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteCoverListDTOList) {
                    apathList.add(hexagonSiteListDTO.getSharePicH5Apath());
                    coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
                }
            }

            Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);

            Map<String, String> formMap = new HashMap<>();
            List<String> formIds = new ArrayList<>();
            List<HexagonSiteListDTO> hexagonSiteFormListDTOList = hexagonSiteDAO.getFormBySiteIds(ids);
            if (null != hexagonSiteFormListDTOList && CollectionUtils.isNotEmpty(hexagonSiteFormListDTOList)) {
                for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteFormListDTOList) {
                    if (StringUtils.isNotBlank(hexagonSiteListDTO.getFormId())) {
                        formIds.add(hexagonSiteListDTO.getFormId());
                    }
                    formMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getFormId());
                }
            }

            Map<String, Boolean> formMappingMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(formIds)) {
                formMappingMap = crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(ea, formIds);
            }

            Map<String, Integer> clueMap = customizeFormClueManager.queryHexagonSiteClueCount(ids);

            Map<String, Integer> marketingEventCountMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(ids)) {
                List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntityList = contentMarketingEventMaterialRelationDAO
                    .getContentMarketingByEaAndObjectTypeAndObjectIds(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), ids);
                if (CollectionUtils.isNotEmpty(contentMarketingEventMaterialRelationEntityList)) {
                    List<ContentMarketingEventMaterialRelationEntity> filterDataList = filterInvalidMarketingEventData(ea, contentMarketingEventMaterialRelationEntityList);
                    if (CollectionUtils.isNotEmpty(filterDataList)) {
                        filterDataList.forEach(data -> {
                            marketingEventCountMap.merge(data.getObjectId(), 1, (prev, one) -> prev + one);
                        });
                    }
                }
            }

            List<ObjectStatisticData> objectStatisticData = marketingObjectAmountStatisticDao.listStatisticData(ea, ids);
            Map<String, ObjectStatisticData> marketingObjectActivityStatisticDTOMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(objectStatisticData)) {
                marketingObjectActivityStatisticDTOMap = objectStatisticData.stream().collect(Collectors.toMap(ObjectStatisticData::getObjectId, data -> data));
            }

            List<HexagonPageEntity> hexagonPageList = hexagonPageDAO.getByHexagonSiteIdList(ids);
            List<String> pageIds = hexagonPageList.stream().map(HexagonPageEntity::getId).collect(Collectors.toList());
            List<CtaRelationEntity> ctaRelationList = ctaRelationDaoManager.getByObjectTypeAndObjectIds(ea, ObjectTypeEnum.HEXAGON_PAGE.getType(), pageIds);
            Map<String, Boolean> ctaRelationMap = hexagonPageList.stream().collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, r -> ctaRelationList.stream().anyMatch(x -> r.getId().equals(x.getObjectId())), (v1, v2) -> v1));
            List<CtaEntity> ctaEntityList = null;
            if(CollectionUtils.isNotEmpty(formIds)) {
                ctaEntityList = ctaDAO.getByFormIds(ea, formIds);
            }

            List<HexagonPageEntity> hexagonPageEntities = hexagonPageList.stream().filter(x -> x.getIsHomepage() != null && x.getIsHomepage().equals(1)).collect(Collectors.toList());
            Map<String, HexagonPageEntity> pageEntityMap = hexagonPageEntities.stream().collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, Function.identity(), (v1, v2) -> v2));
            List<String> homePageId = hexagonPageEntities.stream().map(HexagonPageEntity::getId).collect(Collectors.toList());

            Map<String, String> pageIdMap = hexagonPageEntities.stream().collect(Collectors.toMap(HexagonPageEntity::getId, HexagonPageEntity::getHexagonSiteId, (v1, v2) -> v1));
            //小程序图片分享图
            List<PhotoEntity> miniPhotoEntities = photoManager.queryPhotosByTypeAndTargetIdsNoReset(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), homePageId);
            Map<String, PhotoEntity> coverCutMiniAppPhotoMap = miniPhotoEntities.stream().collect(Collectors.toMap(o -> pageIdMap.get(o.getTargetId()), Function.identity(), (v1, v2) -> v1));
            //朋友圈h5图片分享图
            List<PhotoEntity> h5PhotoEntities = photoManager.queryPhotosByTypeAndTargetIdsNoReset(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(),homePageId);
            Map<String, PhotoEntity> coverCutH5PhotoMap = h5PhotoEntities.stream().collect(Collectors.toMap(o -> pageIdMap.get(o.getTargetId()), Function.identity(), (v1, v2) -> v1));
            //900*500分享图
            List<PhotoEntity> ordinaryPhotoEntities = photoManager.queryPhotosByTypeAndTargetIdsNoReset(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(),homePageId);
            Map<String, PhotoEntity> coverCutOrdinaryPhotoMap = ordinaryPhotoEntities.stream().collect(Collectors.toMap(o -> pageIdMap.get(o.getTargetId()), Function.identity(), (v1, v2) -> v1));
            Map<String, Integer> formIdToUsageMap = Maps.newHashMap();
            if (!formMap.isEmpty()) {
                formIdToUsageMap = customizeFormDataManager.batchGetFormUsageByFormIds(formMap.values());
            }
            for (HexagonSiteEntity hexagonSiteEntity : hexagonSiteEntityList) {
                GetSiteByEaUnitResult result = BeanUtil.copy(hexagonSiteEntity, GetSiteByEaUnitResult.class);
                result.setUsedByCta(false);
                if(ctaRelationMap != null) {
                    result.setHasCta(ctaRelationMap.get(hexagonSiteEntity.getId()));
                }
                if (null != fsEmployeeMsgMap) {
                    result.setCreatorBy(hexagonSiteEntity.getCreateBy());
                    FsAddressBookManager.FSEmployeeMsg creator = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
                    if (null != creator) {
                        result.setCreator(creator.getName());
                    }else{
                        result.setCreator(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
                    }

                    FsAddressBookManager.FSEmployeeMsg updater = fsEmployeeMsgMap.get(hexagonSiteEntity.getUpdateBy());
                    if (null != updater) {
                        result.setUpdater(updater.getName());
                    }else{
                        result.setUpdater(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
                    }
                }

                if (null != coverUrlMap) {
                    String apath = coverApathMap.get(hexagonSiteEntity.getId());
                    if (StringUtils.isNotBlank(apath)) {
                        result.setCoverAPath(apath);
                        result.setCoverUrl(coverUrlMap.get(apath));
                    } else {
                        result.setCoverAPath(groupMessageDefaultCoverPath);
                        result.setCoverUrl(coverUrlMap.get(groupMessageDefaultCoverPath));
                    }
                }

                HexagonPageEntity hexagonPageEntity = pageEntityMap.get(hexagonSiteEntity.getId());
                if(Objects.nonNull(hexagonPageEntity)){
                    result.setShareTitle(hexagonPageEntity.getShareTitle());
                    result.setShareDesc(hexagonPageEntity.getShareDesc());
                    if(StringUtils.isNotBlank(hexagonPageEntity.getFormId()) && CollectionUtils.isNotEmpty(ctaEntityList)){
                        result.setUsedByCta(ctaEntityList.stream().anyMatch(x -> x.getFormId().equals(hexagonPageEntity.getFormId())));
                    }
                }

                if (!formMap.isEmpty()) {
                  //  Map<String, Integer> formIdToUsageMap = customizeFormDataManager.batchGetFormUsageByFormIds(formMap.values());
                    String formId = formMap.get(hexagonSiteEntity.getId());
                    if (StringUtils.isNotBlank(formId)) {
                        result.setFormId(formId);
                        result.setFormUsage(FormDataUsage.getByType(formIdToUsageMap.get(formId)).getUsage());
                        if (null != formMappingMap) {
                            result.setHadCrmMapping(formMappingMap.get(formId));
                        } else {
                            result.setHadCrmMapping(false);
                        }
                    }
                }

                if (null != clueMap) {
                    result.setLeadCount(clueMap.get(hexagonSiteEntity.getId()));
                } else {
                    result.setLeadCount(0);
                }

                ObjectStatisticData marketingObjectActivityStatisticDTO = marketingObjectActivityStatisticDTOMap.get(hexagonSiteEntity.getId());
                if (null != marketingObjectActivityStatisticDTO) {
                    result.setAccessCount(marketingObjectActivityStatisticDTO.getLookUpCount());
                } else {
                    result.setAccessCount(0);
                }

                if (null != marketingEventCountMap.get(hexagonSiteEntity.getId())) {
                    result.setMarketingEventCount(marketingEventCountMap.get(hexagonSiteEntity.getId()));
                } else {
                    result.setMarketingEventCount(0);
                }

                //设置营销数据
                if (marketingActivityObjectInfoMap != null) {
                    List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = marketingActivityObjectInfoMap.get(hexagonSiteEntity.getId());
                    if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectInfoDTO)) {
                        if (objectInfoDTO.get(0) != null) {
                            result.setMarketingActivityId(objectInfoDTO.get(0).getId());
                            result.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                            result.setMarketingActivityCount(objectInfoDTO.size());
                        }
                    }
                }
                if(result != null && StringUtils.isNotBlank(result.getId())){
                    // 获取裁剪封面图
                    if(coverCutMiniAppPhotoMap.get(result.getId())!=null&&StringUtils.isNotBlank(coverCutMiniAppPhotoMap.get(result.getId()).getThumbnailUrl())){
                        result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoMap.get(result.getId()).getThumbnailUrl());
                    }
                    if(coverCutH5PhotoMap.get(result.getId())!=null&&StringUtils.isNotBlank(coverCutH5PhotoMap.get(result.getId()).getThumbnailUrl())){
                        result.setSharePicH5CutUrl(coverCutH5PhotoMap.get(result.getId()).getThumbnailUrl());
                    }
                    if(coverCutOrdinaryPhotoMap.get(result.getId())!=null&&StringUtils.isNotBlank(coverCutOrdinaryPhotoMap.get(result.getId()).getThumbnailUrl())){
                        result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoMap.get(result.getId()).getThumbnailUrl());
                        //返回原图
                        result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoMap.get(result.getId()).getUrl());
                    }
                }

                FileToHexagonEntity entity = fileToHxagonMap.get(hexagonSiteEntity.getId());
                if (entity!=null){
                    result.setFileToHexagonStatus(entity.getStatus());
                    result.setFileType(entity.getFileType());
                    result.setFileToHexagonFailReason(entity.getFailReason());
                }

                result.setCreateTime(hexagonSiteEntity.getCreateTime().getTime());
                result.setUpdateTime(hexagonSiteEntity.getUpdateTime().getTime());
                resultList.add(result);
            }
        }
        return resultList;
    }

    private List<ContentMarketingEventMaterialRelationEntity> filterInvalidMarketingEventData(String ea, List<ContentMarketingEventMaterialRelationEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<String> marketingIds = list.stream().map(ContentMarketingEventMaterialRelationEntity::getMarketingEventId).collect(Collectors.toList());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.addFilter("_id", marketingIds, OperatorConstants.IN);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> crmMarketingEventResult = crmV2Manager.getList(ea, -10000, MarketingEventFieldContants.API_NAME, searchQuery);
        if (crmMarketingEventResult == null || CollectionUtils.isEmpty(crmMarketingEventResult.getDataList())) {
            return Lists.newArrayList();
        }
        List<String> ids = crmMarketingEventResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        return list.stream().filter(data -> ids.contains(data.getMarketingEventId())).collect(Collectors.toList());
    }

    private List<String> getCdnUrlsByContent(String content, String ea) {
        String regex = cdnSharePath + "/image/" + ea + "/\\w+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> cdnUrls = Lists.newArrayList();
        while (matcher.find()) {
            String url = matcher.group();
            if (StringUtils.isNotEmpty(url)) {
                cdnUrls.add(url);
            }
        }

        return cdnUrls;
    }

    private Map<String, String> getCopiedNewCdnUrlMap(String fromEa, String toEa, List<String> cdnUrls) {
         if (CollectionUtils.isEmpty(cdnUrls)){
             return null;
         }

         Map<String, String> urlMap = new HashMap<>();
         for (String url : cdnUrls) {
             String cpath = url.substring(url.lastIndexOf("/") + 1);
             Optional<String> newCpathOpt = fileV2Manager.copyCFileToCFile(fromEa, toEa, 1000, cpath,false);
             if (!newCpathOpt.isPresent()) {
                 continue;
             }
             String newUrl = cdnSharePath + "/image/" + toEa + "/" + newCpathOpt.get();
             urlMap.put(url, newUrl);
         }
         return urlMap;
    }

    public String resetContentCpath(String content, String fromEa, String toEa) {
        List<String> cdnUrls = getCdnUrlsByContent(content, fromEa);
        Map<String, String> cdnUrlMap = null;
        if (CollectionUtils.isEmpty(cdnUrls)) {
            return content;
        }

        cdnUrlMap = getCopiedNewCdnUrlMap(fromEa, toEa, cdnUrls);
        if (MapUtils.isEmpty(cdnUrlMap)) {
            return content;
        }
        String replacedContent = content;
        for (Map.Entry<String, String> entry : cdnUrlMap.entrySet()) {
            replacedContent = replacedContent.replace(entry.getKey(), entry.getValue());
        }
        return replacedContent;
    }

    public String resetContentApath(String content, String ea) {
        List<String> urlList = new ArrayList<>();

        String domain = host.replace(".", "\\.");
        // 找出所有原有url
        String regExp = domain + "/fssharehelper/file/getFileBySpliceUrl\\?path=(.+?)(\\.jpg|\\.png|\\.gif|\\.jpeg)";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(content);
        while (m.find()) {
            urlList.add(m.group(0));
        }

        if (CollectionUtils.isNotEmpty(urlList)) {
            Map<String, String> apathMap = new HashMap<>();
            // 重新上传下载
            for (String url : urlList) {
                String apath = fileV2Manager.getApathByUrl(url);
                if (StringUtils.isNotBlank(apath)) {
                    byte[] bytes = fileV2Manager.downloadAFile(apath, null);
                    if (null != bytes && bytes.length > 0) {
                        String newApath = fileV2Manager.uploadToApath(bytes, ImageUtil.getImageType(bytes), ea);
                        if (StringUtils.isNotBlank(newApath)) {
                            apathMap.put(url, newApath);
                        }
                    }
                }
            }

            // 替换原有apath
            Iterator<Map.Entry<String, String>> it = apathMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, String> entry = it.next();
                String newApath = entry.getValue();
                String targetUrl = fileV2Manager.getSpliceUrl(ea, newApath);
                if (StringUtils.isNotBlank(targetUrl)) {
                    content = content.replace(entry.getKey(), targetUrl);
                }
            }
        }

        return content;
    }

    public String resetShareApath(String shareApath, String ea) {
        byte[] bytes = fileV2Manager.downloadAFile(shareApath, ea);
        if (null != bytes && bytes.length > 0) {
            String newApath = fileV2Manager.uploadToApath(bytes, ImageUtil.getImageType(bytes), ea);
            if (StringUtils.isNotBlank(newApath)) {
                return newApath;
            }
        }
        return shareApath;
    }

    /**
     * 获取站点基本信息
     *
     * @param ids
     * @return
     */
    public Map<String, HexagonBaseInfoDTO> getHexagonBaseInfoById(List<String> ids, String ea) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }

        List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.getAllByIds(ids);
        if (CollectionUtils.isEmpty(hexagonSiteEntityList)) {
            log.error("query hexagonSiteEntityList return null site ids:{}", ids);
            return null;
        }

        Map<String, String> coverApathMap = new HashMap<>();
        Map<String, String> shareTitleMap = new HashMap<>();
        Map<String, String> shareDescMap = new HashMap<>();
        List<String> apathList = new ArrayList<>();
        apathList.add(groupMessageDefaultCoverPath);
        List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(ids);
        if (null != hexagonSiteCoverListDTOList && CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
            for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteCoverListDTOList) {
                apathList.add(hexagonSiteListDTO.getSharePicH5Apath());
                coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
                shareTitleMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getShareTitle());
                shareDescMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getShareDesc());
            }
        }

        Map<String, HexagonBaseInfoDTO> resultMap = new HashMap<>();
        Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
        for (HexagonSiteEntity entity : hexagonSiteEntityList) {
            HexagonBaseInfoDTO hexagonBaseInfoDTO = new HexagonBaseInfoDTO();
            hexagonBaseInfoDTO.setId(entity.getId());
            hexagonBaseInfoDTO.setTitle(entity.getName());
            if (null != coverUrlMap) {
                String apath = coverApathMap.get(entity.getId());
                if (StringUtils.isNotBlank(apath)) {
                    hexagonBaseInfoDTO.setCoverUrl(coverUrlMap.get(apath));
                }else {
                    hexagonBaseInfoDTO.setCoverUrl(coverUrlMap.get(groupMessageDefaultCoverPath));
                }
            }
            String shareTitle = shareTitleMap.get(entity.getId());
            if (StringUtils.isNotBlank(shareTitle)) {
                hexagonBaseInfoDTO.setShareTitle(shareTitle);
            }
            String shareDesc = shareDescMap.get(entity.getId());
            if (StringUtils.isNotBlank(shareDesc)) {
                hexagonBaseInfoDTO.setShareDesc(shareDesc);
            }
            resultMap.put(entity.getId(), hexagonBaseInfoDTO);
        }
        return resultMap;
    }

    /**
     * 替换页面跳转ID，解决跳转页面还是旧页面的ID
     *
     * @param preSiteId
     * @param newSiteId
     * @return
     */
    @Transactional
    public List<HexagonPageEntity> resetContentActionId(String preSiteId, String newSiteId) {
        //获取新站点以及旧站点的页面ID
        List<HexagonPageEntity> prePageList = hexagonPageDAO.getByHexagonSiteId(preSiteId);
        List<HexagonPageEntity> newPageList = hexagonPageDAO.getByHexagonSiteId(newSiteId);
        Map<String, String> prePageName = Maps.newHashMap();
        Map<String, String> nextPageName = Maps.newHashMap();
        List<HexagonPageEntity> finalPageEntity = Lists.newArrayList();
        for (int i = 0; i < prePageList.size(); i++) {
            prePageName.put(prePageList.get(i).getName(), prePageList.get(i).getId());
            nextPageName.put(newPageList.get(i).getName(), newPageList.get(i).getId());
        }
        for (HexagonPageEntity newHexagonPageEntity : newPageList) {
//            StringBuilder stringBuilder=new StringBuilder(newHexagonPageEntity.getContent());
            String content = newHexagonPageEntity.getContent();
            Iterator<Map.Entry<String, String>> it = prePageName.entrySet().iterator();
            while (it.hasNext()){
                Map.Entry<String, String> entry = it.next();
               content= content.replace(entry.getValue(), nextPageName.get(entry.getKey()));
            }
            String newContent = UnicodeFormatter.decodeUnicodeString(content);
            newHexagonPageEntity.setContent(newContent);
            finalPageEntity.add(newHexagonPageEntity);
        }
        return finalPageEntity;
    }


    public void updateCopiedLiveHexagon(MarketingLiveEntity liveEntity, String ea,String siteId, String title, String desc, String coverPath, Date startTime, boolean submitHexagonSite){
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(siteId);
        if (null == hexagonSiteEntity) {
            log.warn("HexagonService.updateCopiedLiveHexagon queryHexagonTemplateSiteEntity not found, id={}", siteId);
            return;
        }
        //设置为专属微页面
        hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, siteId, true);
        HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getHomePage(siteId);
        updateLiveHexagonParam(hexagonPageEntity, liveEntity, ea, siteId, title, desc, coverPath, startTime, submitHexagonSite);
    }

    public HexagonPageEntity updateLiveHexagonParam(HexagonPageEntity hexagonPageEntity, MarketingLiveEntity liveEntity, String ea,String siteId, String title, String desc, String coverPath, Date startTime, boolean submitHexagonSite) {
        if (hexagonPageEntity == null){
            return null;
        }


        MemberConfigEntity memberConfigEntity = memberManager.tryInitMemberConfig(ea);
        if (memberConfigEntity == null){
            log.warn("HexagonService.updateCopiedLiveHexagon memberConfigEntity not exist ea:{}", ea);
            return null;
        }

        //替换直播数据
        if (desc == null){
            desc = "";
        }
        String newDesc = desc;
        if (newDesc.length() > 30){
            newDesc = newDesc.substring(0, 30);
        }
        if (newDesc.length() > 1) {
            char lastCha = newDesc.charAt(newDesc.length() - 1);
            if (lastCha == ',' || lastCha == '\'' || lastCha == '\"') {
                newDesc = newDesc.substring(0, newDesc.length() - 1);
            }
        }
        String content = hexagonPageEntity.getContent();
        Gson gson =new GsonBuilder().disableHtmlEscaping().create();
        Map<String, Object> gmap = gson.fromJson(content, Map.class);
        gmap.put("name", title);
        Map<String, Object> shareOptsMap = (Map<String, Object>)gmap.get("shareOpts");
        //StringBuilder sb = new StringBuilder().append(host).append("/fssharehelper/file/getFileBySpliceUrl?").append("path=").append(coverPath);
        StringBuilder sb = new StringBuilder();
        String urlByPath = fileV2Manager.getUrlByPath(ea, coverPath);
        if (StringUtils.isNotBlank(urlByPath)){
            sb.append(urlByPath);
        }
        shareOptsMap.put("imgUrl",sb.toString());
        shareOptsMap.put("title", title);
        shareOptsMap.put("desc", newDesc);
        List<Map<String, Object>> componentsMapList = (List<Map<String, Object>>)gmap.get("components");
        for (Map<String, Object> map : componentsMapList){
            if (map.get("type").equals("image")){
                List<Map<String, String>> urls = (List<Map<String, String>>)map.get("images");
                urls.get(0).put("url", sb.toString());
            }
        }

        String newContent = gson.toJson(gmap);
        //替换报名页面的pageId
        if (submitHexagonSite){
            String formPageId = null;
            List<HexagonPageEntity> pageEntities = hexagonPageDAO.getByHexagonSiteId(siteId);
            for (HexagonPageEntity entity : pageEntities){
                if (StringUtils.equals("预约报名", entity.getName())){
                    formPageId = entity.getId();
                    break;
                }
            }
            if (formPageId == null){
                log.info("updateCopiedHexagon formPageId is null");
            }
            newContent = newContent.replace("{SignupPageId}", formPageId);

            //替换报名预约页中报名成功地址
            updateSubmitSuccessPage(siteId);
        }

        //替换开始时间
        String str = "yyy-MM-dd HH:mm";
        SimpleDateFormat sdf = new SimpleDateFormat(str);
        newContent = newContent.replace("{直播开始时间}", sdf.format(startTime).toString());
        //替换直播标题
        newContent = newContent.replace("{直播标题}", title);
        //替换简介
        desc = desc.replaceAll("(\r\n|\r|\n|\n\r)", "<br>");
        desc = desc.replaceAll("\"", "\\\\\"");
        newContent = newContent.replace("{直播简介}", desc);
        //替换会员注册页面地址
        String registerSiteIdReg = "\\{" + "registerSiteId" + "}";
        String loginSiteIdReg = "\\{" + "loginSiteId" + "}";
        newContent = newContent.replaceAll(registerSiteIdReg, memberConfigEntity.getRegistrationSiteId());
        newContent = newContent.replaceAll(loginSiteIdReg, memberConfigEntity.getLoginSiteId());
        //替换观看地址
        newContent = newContent.replace("{liveUrl}", liveEntity.getShortViewUrl());

        //替换市场活动id
        newContent = newContent.replace("{marketingEventId}", liveEntity.getMarketingEventId());
        //替换直播id
        newContent = newContent.replace("{liveId}", liveEntity.getId());
        if (liveEntity.getPlatform() == LivePlatformEnum.CHANNELS.getType()){
            Result<ChannelsAccountResult> account = channelsManager.getAccount(ea,liveEntity.getAssociatedAccountId());
            ChannelsAccountResult accountEntity = account.getData();
            if(Objects.nonNull(accountEntity)){
                newContent = newContent.replace("{ChannelsAvatar}", accountEntity.getChannelsAvatar());
                newContent = newContent.replace("{ChannelsName}", accountEntity.getChannelsName());
            }
            String cover =  fileV2Manager.getUrlByPath(liveEntity.getCover(),ea,false);
            newContent = newContent.replace("{封面图片}",cover);
            newContent = newContent.replace("{objectId}", liveEntity.getId());
            newContent = newContent.replace("{直播活动名称}", title);
            if(memberManager.isOpenMember(ea)){
                newContent = newContent.replace("\"{会员自动登入}\"", "true");
            }else {
                newContent = newContent.replace("\"{会员自动登入}\"", "false");
            }
        }
        newContent = UnicodeFormatter.decodeUnicodeString(newContent);

        HexagonPageEntity updateEntity = BeanUtil.copy(hexagonPageEntity, HexagonPageEntity.class);
        updateEntity.setId(hexagonPageEntity.getId());
        updateEntity.setName(title);
        updateEntity.setShareTitle(title);
        updateEntity.setContent(newContent);
        updateEntity.setShareDesc(newDesc);
        updateEntity.setSharePicH5Apath(coverPath);
        updateEntity.setSharePicMpApath(coverPath);
        hexagonPageDAO.update(updateEntity);
        return updateEntity;
    }

    private void updateSubmitSuccessPage(String siteId){
        List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getByHexagonSiteId(siteId);
        if (CollectionUtils.isEmpty(hexagonPageEntityList)){
            return;
        }

        HexagonPageEntity submitPage = null;
        HexagonPageEntity successPage = null;
        for (HexagonPageEntity pageEntity : hexagonPageEntityList){
            if (StringUtils.equals("预约报名", pageEntity.getName())){
                submitPage = pageEntity;
            }
            if (StringUtils.equals("提交成功", pageEntity.getName())){
                successPage = pageEntity;
            }
        }
        String content = submitPage.getContent().replace("{successPageId}", successPage.getId());
        hexagonPageDAO.updateContent(submitPage.getId(), content);
    }

    /**
     * 统一复制站点
     *
     * @param ea
     * @param userId
     * @param arg
     * @return
     */
    public Result<CreateSiteResult> copySite(String ea, Integer userId, HexagonCopyArg arg, int copyFrom) {

        //保存之前的站点ID
        String preSiteId = arg.getId();
        CreateSiteResult siteResult = new CreateSiteResult();
        if (StringUtils.isNotBlank(arg.getId())) {
            //修改站点的ID
            String siteId = UUIDUtil.getUUID();
            siteResult.setId(siteId);
            HexagonSiteEntity copyHexagonSiteEntity = new HexagonSiteEntity();
            copyHexagonSiteEntity.setId(siteId);
            copyHexagonSiteEntity.setEa(ea);
            copyHexagonSiteEntity.setCreateBy(userId);
            copyHexagonSiteEntity.setUpdateBy(userId);
            copyHexagonSiteEntity.setCreateTime(new Date());
            copyHexagonSiteEntity.setUpdateTime(new Date());
            copyHexagonSiteEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            copyHexagonSiteEntity.setName(arg.getName());
            boolean updateSiteRedisResult = redisManager.setHexagonSiteToRedis(copyHexagonSiteEntity.getId(), copyHexagonSiteEntity);
            if (!updateSiteRedisResult) {
                log.error("HexagonService.editSite insert site redis failed, entity={}", copyHexagonSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            //插入新站点
            //int insertResult = hexagonSiteDAOManager.insert(copyHexagonSiteEntity);
            int insertResult = hexagonSiteDAO.insert(copyHexagonSiteEntity);

            if (insertResult != 1) {
                log.warn("HexagonService copySite insert site failed,entity={}", copyHexagonSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            if (arg.isRegisterIntegralMaterial()) {
                integralServiceManager.asyncRegisterMaterial(ea, CategoryApiNameConstant.MICRO_PAGE, siteId ,copyHexagonSiteEntity.getName());
            }
            //根据之前的站点ID，查询出之前的该站点preSiteId下的页面
            if (copyFrom == HexagonManager.COPY_FROM_HEXAGON) {
                List<HexagonPageEntity> queryPageEntity = hexagonPageDAO.getByHexagonSiteId(preSiteId);
                if (CollectionUtils.isNotEmpty(queryPageEntity)) {
                    for (HexagonPageEntity pageEntity : queryPageEntity) {
                        //返回首页的ID
                        if (pageEntity.getIsHomepage().intValue() == 1) {
                            String homePageId = copyPage(pageEntity.getId(), pageEntity.getEa(), ea, siteId, userId, copyFrom).getData();
                            siteResult.setPageId(homePageId);
                        } else {
                            copyPage(pageEntity.getId(), pageEntity.getEa(), ea, siteId, userId, copyFrom);
                        }
                    }
                }
            }else {
                List<HexagonTemplatePageEntity> queryPageEntity = hexagonTemplatePageDAO.getBySiteId(preSiteId);
                if (CollectionUtils.isNotEmpty(queryPageEntity)) {
                    for (HexagonTemplatePageEntity pageEntity : queryPageEntity) {
                        //返回首页的ID
                        if (pageEntity.getIsHomepage().intValue() == 1) {
                            String homePageId = copyPage(pageEntity.getId(), pageEntity.getEa(), ea, siteId, userId, copyFrom).getData();
                            siteResult.setPageId(homePageId);
                        } else {
                            copyPage(pageEntity.getId(), pageEntity.getEa(), ea, siteId, userId, copyFrom);
                        }
                    }
                }
            }
            //替换新页面的actionID;
            List<HexagonPageEntity> hexagonPageEntities = this.resetContentActionId(preSiteId, siteId);
            int updateContentActionID = hexagonPageDAO.updatePageActionId(hexagonPageEntities);
            if (updateContentActionID > 0) {
                log.warn("更新页面的跳转ID成功");
            }
            // 清除微页面缓存
            List<String> hexagonPageIds = hexagonPageDAO.getPageIdsBySiteId(siteId);
            if (CollectionUtils.isNotEmpty(hexagonPageIds)) {
                hexagonPageIds.forEach(pageId -> redisManager.deleteHexgonPage(pageId));
            }
        }
        return Result.newSuccess(siteResult);
    }

    private HexagonPageEntity buildCopyPageEntity(String pageId, String fromEa, String toEa, String siteId, Integer userId, int copyFrom){
        if (copyFrom != COPY_FROM_HEXAGON && copyFrom != COPY_FROM_TEMPLATE){
            return null;
        }

        if (copyFrom == COPY_FROM_HEXAGON){
            HexagonPageEntity pageEntity = hexagonPageDAO.getById(pageId);
            HexagonPageEntity hexagonPageEntity = BeanUtil.copy(pageEntity, HexagonPageEntity.class);
            String id = UUIDUtil.getUUID();
            String sharePicH5Apath = pageEntity.getSharePicH5Apath();
            //替换share的页面图片
            String newH5Apath = this.resetShareApath(sharePicH5Apath, toEa);
            hexagonPageEntity.setSharePicH5Apath(newH5Apath);
            hexagonPageEntity.setSharePicMpApath(newH5Apath);
            //替换原有的content里面的url
            String content = pageEntity.getContent();
            String newContent = this.resetContentApath(content, toEa);
            newContent = resetContentCpath(content, fromEa, toEa);  //复制cpath图片
            newContent = UnicodeFormatter.decodeUnicodeString(newContent);
            hexagonPageEntity.setContent(newContent);
            hexagonPageEntity.setHexagonSiteId(siteId);
            hexagonPageEntity.setId(id);
            hexagonPageEntity.setEa(toEa);
            hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            hexagonPageEntity.setCreateBy(userId);
            hexagonPageEntity.setUpdateBy(userId);
            hexagonPageEntity.setFormId(pageEntity.getFormId());
            return hexagonPageEntity;
        }else {
            HexagonTemplatePageEntity hexagonTemplatePageEntity = hexagonTemplatePageDAO.getById(pageId);
            HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
            hexagonPageEntity.setId(UUIDUtil.getUUID());
            hexagonPageEntity.setEa(toEa);
            hexagonPageEntity.setHexagonSiteId(siteId);
            hexagonPageEntity.setName(hexagonTemplatePageEntity.getName());
            hexagonPageEntity.setShareTitle(hexagonTemplatePageEntity.getShareTitle());
            hexagonPageEntity.setShareDesc(hexagonTemplatePageEntity.getShareDesc());
            hexagonPageEntity.setIsHomepage(hexagonTemplatePageEntity.getIsHomepage());
            String sharePicH5Apath = hexagonTemplatePageEntity.getSharePicH5Apath();
            //替换share的页面图片
            String newH5Apath = this.resetShareApath(sharePicH5Apath, toEa);
            hexagonPageEntity.setSharePicH5Apath(newH5Apath);
            hexagonPageEntity.setSharePicMpApath(newH5Apath);
            String content = hexagonTemplatePageEntity .getContent();
            String newContent = this.resetContentApath(content, toEa);
            resetContentCpath(content, fromEa, toEa);  //复制cpath图片
            newContent = UnicodeFormatter.decodeUnicodeString(newContent);
            hexagonPageEntity.setContent(newContent);
            hexagonPageEntity.setHexagonSiteId(siteId);
            hexagonPageEntity.setEa(toEa);
            hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            hexagonPageEntity.setCreateBy(userId);
            hexagonPageEntity.setUpdateBy(userId);
            hexagonPageEntity.setFormId(hexagonTemplatePageEntity.getFormId());
            return hexagonPageEntity;
        }
    }

    /**
     * 复制微页面
     * @param pageId
     * @param fromEa
     * @param toEa
     * @param siteId
     * @param userId
     * @param copyFrom
     * @return
     */
    public Result<String> copyPage(String pageId, String fromEa, String toEa, String siteId, Integer userId, int copyFrom) {
        HexagonPageEntity hexagonPageEntity = buildCopyPageEntity(pageId, fromEa, toEa, siteId, userId, copyFrom);
        if (hexagonPageEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String formID = hexagonPageEntity.getFormId();
        hexagonPageEntity.setFormId(null);
        int insertPageResult = hexagonPageDAO.insert(hexagonPageEntity);
        if (insertPageResult != 1) {
            log.error("HexagonService.editSite insert page failed, entity={}", insertPageResult);
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }

        //根据pageEntity获取formID，更新表单的ID，ObjectID(即页面ID）
        GetCustomizeFormDataByIdArg getCustomizeFormDataByIdArg = new GetCustomizeFormDataByIdArg();
        getCustomizeFormDataByIdArg.setId(formID);
        Result<CustomizeFormDataDetailResult> customizeFormDataResult = customizeFormDataManager.getCustomizeFormDataById(getCustomizeFormDataByIdArg);
        if (customizeFormDataResult.getData() != null && customizeFormDataResult.isSuccess()) {
            //包含TagNameList
            AddCustomizeFormDataArg addCustomizeFormDataArg = BeanUtil.copy(customizeFormDataResult.getData(), AddCustomizeFormDataArg.class);
            addCustomizeFormDataArg.setEa(toEa);
            addCustomizeFormDataArg.setFsUserId(userId);

            //查询是否有该线索池id
            if (StringUtils.isNotBlank(addCustomizeFormDataArg.getCrmPoolId())){
                Result<List<LeadPoolResult>> poolResult =  crmService.listLeadPools(toEa, -10000);
                if (poolResult == null || !poolResult.isSuccess() ||CollectionUtils.isEmpty(poolResult.getData())){
                    addCustomizeFormDataArg.setCrmPoolId(null);
                }else {
                    List<String> poolIds = poolResult.getData().stream().map(item -> item.getId()).collect(Collectors.toList());
                    if (!poolIds.contains(addCustomizeFormDataArg.getCrmPoolId())){
                        addCustomizeFormDataArg.setCrmPoolId(poolIds.get(0));
                    }
                }
            }
            //插入表单数据
            Result<AddCustomizeFormDataResult> addCustomizeFormDataResult = customizeFormDataService.addCustomizeFormData(addCustomizeFormDataArg, null,false);
            if (!addCustomizeFormDataResult.isSuccess()) {
                log.warn("HexagonService copyPage insertCustomizeFormData failed,addCustomizeFormDataArg,{}", addCustomizeFormDataArg);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            //绑定物料 addCustomizeFormDataResult重新生成formID,customizeFormDataObject
            String newFormID = addCustomizeFormDataResult.getData().getId();
            log.info("copy page newFormID:{}", newFormID);
            //绑定
            customizeFormDataManager.bindCustomizeFormDataObject(newFormID, hexagonPageEntity.getId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), toEa, userId, null, null, null);
            //page插入formID
            HexagonPageEntity formPageEntity = BeanUtil.copy(hexagonPageEntity, HexagonPageEntity.class);
            formPageEntity.setFormId(newFormID);
            int updateResult = hexagonPageDAO.update(formPageEntity);
            if (updateResult != 1) {
                log.error("HexagonService.editSite update page form failed, entity={}", updateResult);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }

        ctaRelationDaoManager.copyAddCtaRelation(fromEa, ObjectTypeEnum.HEXAGON_PAGE.getType(), pageId, toEa, ObjectTypeEnum.HEXAGON_PAGE.getType(), hexagonPageEntity.getId());
        String id = hexagonPageEntity.getId();
        HexagonPageEntity updateEntity = hexagonPageDAO.getById(id);
        if (updateEntity != null) {
            boolean insertRedisResult = redisManager.setHexagonPageToRedis(id, updateEntity);
            if (!insertRedisResult) {
                log.error("HexagonService.copySite insert page redis failed, entity={}", updateEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }
        try {
            List<PhotoEntity> photoEntities = photoDAO.listByTargeId(pageId);
            if (CollectionUtils.isNotEmpty(photoEntities)) {
                //photoEntities的主建id重新生成一个uuid，并且targeId设置成复制后的id
                photoEntities.forEach(photoEntity -> {
                    photoEntity.setId(UUIDUtil.getUUID());
                    photoEntity.setTargetId(id);
                });
                photoDAO.batchInsert(photoEntities);
            }
        }catch (Exception e){
            log.warn("HexagonService.copySite insert photo failed, entity={}", e);
            return Result.newSuccess(id);
        }
        return Result.newSuccess(id);
    }

    public Result<CreateSiteResult> hexagonCopySite(String ea, Integer userId, HexagonCopyArg arg, int copyFrom) {
        Result<CreateSiteResult> createSiteResult = this.copySite(ea, userId, arg, copyFrom);
        CreateSiteResult siteResult = BeanUtil.copy(createSiteResult.getData(), CreateSiteResult.class);
        String siteId = siteResult.getId();
        //检验表单映射关系
        Result<CreateSiteResult> formResult = formMapping(siteResult, ea);
        siteResult.setFormId(formResult.getData().getFormId());
        siteResult.setHadCrmMapping(formResult.getData().getHadCrmMapping());

        return Result.newSuccess(siteResult);
    }

    /**
     * 表单映射
     *
     * @param siteResult
     * @param ea
     * @return
     */
    public Result<CreateSiteResult> formMapping(CreateSiteResult siteResult, String ea) {
        //检验表单映射关系
        Map<String, String> formMap = new HashMap<>();
        List<String> formIds = new ArrayList<>();
        List<HexagonSiteListDTO> hexagonSiteFormListDTOList = hexagonSiteDAO.getFormBySiteIds(Arrays.asList(siteResult.getId()));
        if (null != hexagonSiteFormListDTOList && CollectionUtils.isNotEmpty(hexagonSiteFormListDTOList)) {
            for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteFormListDTOList) {
                if (StringUtils.isNotBlank(hexagonSiteListDTO.getFormId())) {
                    formIds.add(hexagonSiteListDTO.getFormId());
                }
                formMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getFormId());
            }
        }
        Map<String, Boolean> formMappingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(formIds)) {
            formMappingMap = crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(ea, formIds);
        }
        if (null != formMap) {
            String formId = formMap.get(siteResult.getId());
            if (StringUtils.isNotBlank(formId)) {
                siteResult.setFormId(formId);
                if (null != formMappingMap) {
                    siteResult.setHadCrmMapping(formMappingMap.get(formId));
                } else {
                    siteResult.setHadCrmMapping(false);
                }
            }
        }
        return Result.newSuccess(siteResult);
    }

    public Result<GetPageDetailResult> getHexagonSiteHomepageDetail(String siteId){
        GetPageDetailResult result = new GetPageDetailResult();
        HexagonSiteEntity hexagonSiteEntity = redisManager.getHexagonSite(siteId);
        if (null == hexagonSiteEntity) {
            hexagonSiteEntity = hexagonSiteDAO.getInclueDeletedById(siteId);
            if (null == hexagonSiteEntity) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
            } else {
                HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(hexagonSiteEntity.getStatus());
                if (hexagonStatusEnum == HexagonStatusEnum.DELETED) {
                    return Result.newError(SHErrorCode.HEXAGON_SITE_DELETED);
                }

                if (hexagonStatusEnum == HexagonStatusEnum.STOPED) {
                    return Result.newError(SHErrorCode.HEXAGON_SITE_STOPED);
                }
                redisManager.setHexagonSiteToRedis(siteId, hexagonSiteEntity);
            }
        }

        HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(hexagonSiteEntity.getStatus());
        if (hexagonStatusEnum == HexagonStatusEnum.STOPED) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_STOPED);
        }

        if (hexagonStatusEnum == HexagonStatusEnum.DELETED) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_DELETED);
        }

        HexagonPageEntity hexagonPageEntity = redisManager.getHexagonHomePage(siteId);
        if (null == hexagonPageEntity) {
            hexagonPageEntity = hexagonPageDAO.getHomePage(siteId);
            if (null == hexagonPageEntity) {
                return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
            } else {
                HexagonStatusEnum pageHexagonStatusEnum = HexagonStatusEnum.getByType(hexagonPageEntity.getStatus());
                if (pageHexagonStatusEnum == HexagonStatusEnum.DELETED) {
                    return Result.newError(SHErrorCode.HEXAGON_PAGE_DELETED);
                }
                redisManager.setHexagonHomePageToRedis(siteId, hexagonPageEntity);
            }
        }

        HexagonStatusEnum pageHexagonStatusEnum = HexagonStatusEnum.getByType(hexagonPageEntity.getStatus());
        if (pageHexagonStatusEnum == HexagonStatusEnum.DELETED) {
            return Result.newError(SHErrorCode.HEXAGON_PAGE_DELETED);
        }

        result = BeanUtil.copy(hexagonPageEntity, GetPageDetailResult.class);
        MaterialRelationEntity relationEntity = materialRelationDao.queryMaterialRelationByObjectId(hexagonPageEntity.getId(), ObjectTypeEnum.HEXAGON_PAGE.getType());
        if(relationEntity!=null&&relationEntity.getSharePosterAPath()!=null){
            result.setSharePosterAPath(relationEntity.getSharePosterAPath());
            result.setSharePosterUrl(fileV2Manager.getUrlByPath(relationEntity.getSharePosterAPath(),hexagonPageEntity.getEa(),false));
        }
        List<String> apathList = new ArrayList<>();
        if (StringUtils.isNotBlank(hexagonPageEntity.getSharePicH5Apath())) {
            apathList.add(hexagonPageEntity.getSharePicH5Apath());
        }

        if (StringUtils.isNotBlank(hexagonPageEntity.getSharePicMpApath())) {
            apathList.add(hexagonPageEntity.getSharePicMpApath());
        }

        Map<String, String> urlMap = null;
        if (CollectionUtils.isNotEmpty(apathList)) {
            urlMap = fileV2Manager.batchGetUrlByPath(apathList, hexagonPageEntity.getEa(), false);
            result.setSharePicH5Url(urlMap.get(hexagonPageEntity.getSharePicH5Apath()));
            result.setSharePicMpUrl(urlMap.get(hexagonPageEntity.getSharePicMpApath()));
            // 获取裁剪封面图
            PhotoEntity coverPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_COVER.getType(), hexagonPageEntity.getId(),hexagonPageEntity.getEa());
            if (coverPhotoEntity != null) {
                result.setSharePicMpCutUrl(coverPhotoEntity.getUrl());
            }
        }

        return Result.newSuccess(result);
    }

    public Result<GetPageDetailResult> getTemplateSiteHomepageDetail(String siteId){
        GetPageDetailResult result = new GetPageDetailResult();
        HexagonTemplateSiteEntity hexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(siteId);
        if (null == hexagonTemplateSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(hexagonTemplateSiteEntity.getStatus());
        if (hexagonStatusEnum == HexagonStatusEnum.STOPED) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_STOPED);
        }

        HexagonTemplatePageEntity hexagonTemplatePageEntity = hexagonTemplatePageDAO.getHomePage(siteId);
        if (null == hexagonTemplatePageEntity) {
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_PAGE_NOT_FOUND);
        }

        result = BeanUtil.copy(hexagonTemplatePageEntity, GetPageDetailResult.class);

        List<String> apathList = new ArrayList<>();
        if (StringUtils.isNotBlank(hexagonTemplatePageEntity.getSharePicH5Apath())) {
            apathList.add(hexagonTemplatePageEntity.getSharePicH5Apath());
        }

        if (StringUtils.isNotBlank(hexagonTemplatePageEntity.getSharePicMpApath())) {
            apathList.add(hexagonTemplatePageEntity.getSharePicMpApath());
        }

        Map<String, String> urlMap = null;
        if (CollectionUtils.isNotEmpty(apathList)) {
            urlMap = fileV2Manager.batchGetUrlByPath(apathList, hexagonTemplatePageEntity.getEa(), false);
            result.setSharePicH5Url(urlMap.get(hexagonTemplatePageEntity.getSharePicH5Apath()));
            result.setSharePicMpUrl(urlMap.get(hexagonTemplatePageEntity.getSharePicMpApath()));
        }

        return Result.newSuccess(result);
    }


    private boolean isUpdateMemberInfoSite(String ea, String siteId) {
        MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
        if (memberConfig != null && siteId.equals(memberConfig.getUpdateInfoSiteId())) {
            return true;
        }
        return false;
    }

    public Result<GetPageDetailResult> getHomepageDetailBySiteId(HexagonhomepageDetailArg arg) {
        GetPageDetailResult result = null;
        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(arg.getType());
        if (hexagonPreviewEnum == HexagonPreviewEnum.SITE || hexagonPreviewEnum == HexagonPreviewEnum.SITE_PREVIEW) {
            result = getHexagonSiteHomepageDetail(arg.getSiteId()).getData();
        } else if (hexagonPreviewEnum == HexagonPreviewEnum.TEMPLATE_SITE) {
            result = getTemplateSiteHomepageDetail(arg.getSiteId()).getData();
        }
        if (result == null ) {
            return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
        }

        if(result.getFormId() != null){
            result.setFormUsage(customizeFormDataManager.batchGetFormUsageByFormIds(ImmutableSet.of(result.getFormId())).get(result.getFormId()));
        }
        result.setUpdateMemberInfoSite(false);
        //微页面专属逻辑
        if(StringUtils.isNotBlank(result.getHexagonSiteId())){
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(result.getHexagonSiteId());
            if (hexagonSiteEntity == null) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
            }
            //是否会员更新站点
            boolean isUpdateMemberInfoSite = isUpdateMemberInfoSite(hexagonSiteEntity.getEa(), result.getHexagonSiteId());
            result.setUpdateMemberInfoSite(isUpdateMemberInfoSite);
            //会员是否审核
            Optional<Integer> memberApprovalStatusOpt = memberManager.getMemberApproveStatus(result.getEa(), result.getHexagonSiteId(), arg.getWxAppId(), arg.getOpenId(), arg.getUid(), arg.getAllMemberCookieInfos(), arg.getIdentityCheckType());
            result.setMemberApprovalStatus(memberApprovalStatusOpt.isPresent() ? memberApprovalStatusOpt.get() : null);
        }
        if(StringUtils.isBlank(result.getSharePicMpCutUrl())){
            //添加默认封面
            result.setSharePicMpCutUrl(fileV2Manager.getUrlByPath(groupMessageDefaultCoverPath,result.getEa(),false));
        }

        // 获取裁剪封面图
        PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), result.getId());
        if (coverCutMiniAppPhotoEntity != null) {
            result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
        }
        PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), result.getId());
        if (coverCutH5PhotoEntity != null) {
            result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
        }
        PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), result.getId());
        if (coverCutOrdinaryPhotoEntity != null) {
            result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
            //返回原图
            result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
        }
        try {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("field_micro_page_id",OperatorConstants.IN,Lists.newArrayList(result.getHexagonSiteId()));
            paasQueryArg.addFilter("field_type",OperatorConstants.IN,Lists.newArrayList("pdf"));
            queryFilterArg.setQuery(paasQueryArg);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(result.getEa(), SuperUserConstants.USER_ID, queryFilterArg, 1, 1);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                result.setScopeType(1);
            }
        }catch (Exception e){
            log.warn("get homepage detail error",e);
        }
        return Result.newSuccess(result);
    }
}