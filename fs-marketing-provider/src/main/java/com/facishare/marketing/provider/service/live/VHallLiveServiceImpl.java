package com.facishare.marketing.provider.service.live;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.GetOrCreateMarketingSceneArg;
import com.facishare.marketing.api.result.live.CreateMarketingLiveResult;
import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.api.vo.live.CreateLiveVO;
import com.facishare.marketing.common.enums.MarketingSceneType;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.LiveManager;
import com.facishare.marketing.provider.manager.SceneTriggerManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2023/12/15
 * @Desc 微吼直播处理
 **/
@Service("vHallLiveService")
@Slf4j
public class VHallLiveServiceImpl extends LiveBaseService {

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;

    @Autowired
    private LiveManager liveManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private ShortUrlManager shortUrlManager;

    @Autowired
    private SceneTriggerManager sceneTriggerManager;

    @Autowired
    private MarketingSceneService marketingSceneService;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ConferenceManager conferenceManager;

    private Gson gson = new Gson();
    @Value("${vhall.h5.view.url}")
    private String liveUserH5Url;
    @Value("${polyv.h5.view.url}")
    private String polyvLiveUserH5Url;
    @Value("${vhall.h5.lecture.view.url}")
    private String liveLectureUrl;
    @Value("${live.default.hexagon.id}")
    private String defaultHexagonIds;

    @ReloadableProperty("channels_transit_url")
    private String channelStransitUrl;
    @ReloadableProperty("channels_transit_hexagon_id")
    private String transitId;
    @ReloadableProperty("default_conference_cover")
    private String defaultLiveCover;
    @Value("${host}")
    private String host;

    @Override
    public Result<CreateMarketingLiveResult> createLive(CreateLiveVO vo) {
        // 校验能否创建
        String xiaoetongLiveId = vo.getXiaoetongLiveId();
        MarketingLiveEntity existsEntity = marketingLiveDAO.getMarketingLiveByXiaoetongId(xiaoetongLiveId);
        if (existsEntity != null) {
            // 该直播已被关联，不可使用
            return Result.newError(SHErrorCode.THIRD_LIVE_RELATION_EXIST);
        }

        // 创建直播市场活动
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventResult = createMarketingEventObj(vo);
        if (createMarketingEventResult == null){
            log.info("VHallLiveServiceImpl -> createLive failed create marketingEvent error return null vo:{}", vo);
            return Result.newError(SHErrorCode.MARKETING_EVENT_CREATE_FAILED);
        }
        if (!createMarketingEventResult.isSuccess()){
            log.info("VHallLiveServiceImpl -> createLive failed create marketingEvent error errcode:{} errMsg:{} vo:{}", createMarketingEventResult.getCode(), createMarketingEventResult.getMessage(), vo);
            return Result.newError(createMarketingEventResult.getCode(), createMarketingEventResult.getMessage());
        }

        String marketingEventId = createMarketingEventResult.getData().getObjectData().getId();
        // 创建直播封面
        String apath = vo.getCoverTaPath();
        if (vo.getCoverTaPath().startsWith("TA_")){
            apath = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(vo.getEa(), vo.getFsUserId(), vo.getExt(), vo.getCoverTaPath(), "fs-marketing-provider");
            if (StringUtils.isBlank(apath)){
                log.info("VHallLiveServiceImpl -> createLive failed create cover error vo:{}", vo);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }

        // 组装实体类
        MarketingLiveEntity entity = buildMarketingLiveEntity4Create(vo, marketingEventId, apath);

        // 创建直播
        String insertId = liveManager.addLiveByLock(entity);
        if (!StringUtils.equals(insertId, entity.getId())){
            //直播先从mq通过过来了
            log.info("sync live from crm ea:{} title:{} insertId:{} entityid:{}", vo.getEa(), vo.getTitle(), insertId, entity.getId());
            entity.setId(insertId);
        }

        String viewUrl = polyvLiveUserH5Url + "?ea=" + vo.getEa() + "&mkId=" + entity.getId()+ "&liveId=" + vo.getXiaoetongLiveId();
        entity.setViewUrl(viewUrl);

        CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
        shortUrlArg.setUrl(entity.getViewUrl());
        Optional<String> shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
        if (shortUrlResult.isPresent()) {
            entity.setShortViewUrl(shortUrlResult.get());
        }
        marketingLiveDAO.updateMarketingLive(entity);

        //增加统计信息
        MarketingLiveStatistics marketingLiveStatistics = new MarketingLiveStatistics();
        marketingLiveStatistics.setId(UUIDUtil.getUUID());
        marketingLiveStatistics.setCorpId(vo.getCorpId());
        marketingLiveStatistics.setTotalAttendeeUsers(0);
        marketingLiveStatistics.setTotalViewUsers(0);
        marketingLiveStatistics.setTotalRecordUsers(0);
        marketingLiveStatistics.setViewTimes(0);
        marketingLiveStatistics.setViewDuration(0);
        marketingLiveStatistics.setRecordTimes(0);
        marketingLiveStatistics.setStatus(LiveStatusEnum.NOT_START.getStatus());
        marketingLiveStatistics.setChatTimes(0);
        marketingLiveStatistics.setTotalChatUser(0);
        marketingLiveStatistics.setType(vo.getLivePlatform());
        marketingLiveStatistics.setXiaoetongLiveId(vo.getXiaoetongLiveId());
        marketingLiveStatisticsDAO.insert(marketingLiveStatistics);

        //预置微页面站点
        String id = entity.getId();
        final String liveCover = apath;
        //ThreadPoolUtils.execute(() -> {
        String formHexagonSiteId = copyHexagon(vo, marketingEventId, liveCover);
        marketingLiveDAO.updateFormHexagonById(id, formHexagonSiteId);
        //}, ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);

        String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + marketingEventId + "&ea=" + vo.getEa() + "&objectType=26&id=" + formHexagonSiteId +"&type=1";
        crmV2Manager.updateMarketingEvenObjLandingPage(vo.getEa(),  marketingEventId, vo.getFsUserId(),eventLandingPage);

        CreateMarketingLiveResult result = new CreateMarketingLiveResult();
        result.setId(entity.getId());
        result.setMarketingEventId(marketingEventId);

        integralServiceManager.asyncRegisterMaterial(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), CategoryApiNameConstant.LIVE,entity.getId(),entity.getTitle());
        marketingSceneService.getOrCreateMarketingScene(vo.getEa(), vo.getFsUserId(), new GetOrCreateMarketingSceneArg(MarketingSceneType.LIVE.getType(), entity.getId()));
        return Result.newSuccess(result);
    }

    @Override
    public Result<CreateMarketingLiveResult> updateLive(CreateLiveVO arg) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(arg.getId());
        if (entity == null) {
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        if (entity.getPlatform() == null) {
            entity.setPlatform(arg.getLivePlatform());
        }
        if (arg.getLivePlatform().intValue() != entity.getPlatform().intValue()) {
            log.warn("VHallLiveServiceImpl -> updateLive failed cannot change platform arg:{}", arg);
            return Result.newError(SHErrorCode.MARKETING_CHANGE_PLATFORM_DENY);
        }

        if (arg.getCoverTaPath() != null && arg.getCoverTaPath().contains(host)) {
            String apath = fileV2Manager.getApathByUrl(arg.getCoverTaPath());
            if (StringUtils.equals(defaultLiveCover, apath)) {
                log.info("VHallLiveServiceImpl -> updateLive failed set live cover not default cover arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_SET_COVER);
            }
        }

        if (!StringUtils.equals(arg.getTitle(), entity.getTitle())) {
            //查询市场活动是否存在
            int marketingEventCount = conferenceManager.queryMarketingEventCountByTitle(arg.getEa(), arg.getTitle());
            if (marketingEventCount != 0) {
                log.info("VHallLiveServiceImpl -> updateLive failed marketingEvent is exists vo:{} conferenceEntity.getTitle:{}", arg, entity.getTitle());
                return Result.newError(SHErrorCode.MARKETING_EVENT_EXIST);
            }
        }

        //同步市场活动数据
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> marketingResult = updateLiveToCrmMarketingEvent(arg.getEa(),
                arg.getFsUserId(), entity.getMarketingEventId(), arg);
        if (!marketingResult.isSuccess()) {
            return Result.newError(marketingResult.getCode(), marketingResult.getMessage());
        }

        //增加统计信息
        List<MarketingLiveStatistics> entitys = marketingLiveStatisticsDAO.getByXiaoetongLiveId(eieaConverter.enterpriseAccountToId(arg.getEa()), arg.getXiaoetongLiveId());
        if (CollectionUtils.isEmpty(entitys)) {
            MarketingLiveStatistics marketingLiveStatistics = new MarketingLiveStatistics();
            marketingLiveStatistics.setId(UUIDUtil.getUUID());
            marketingLiveStatistics.setCorpId(arg.getCorpId());
            marketingLiveStatistics.setTotalAttendeeUsers(0);
            marketingLiveStatistics.setTotalViewUsers(0);
            marketingLiveStatistics.setTotalRecordUsers(0);
            marketingLiveStatistics.setViewTimes(0);
            marketingLiveStatistics.setViewDuration(0);
            marketingLiveStatistics.setRecordTimes(0);
            marketingLiveStatistics.setStatus(LiveStatusEnum.NOT_START.getStatus());
            marketingLiveStatistics.setChatTimes(0);
            marketingLiveStatistics.setTotalChatUser(0);
            marketingLiveStatistics.setType(entity.getPlatform());
            marketingLiveStatistics.setXiaoetongLiveId(arg.getXiaoetongLiveId());
            marketingLiveStatisticsDAO.insert(marketingLiveStatistics);
        }
        String apath = arg.getCoverTaPath();
        if (arg.getCoverTaPath().startsWith("TA_")) {
            apath = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(arg.getEa(), arg.getFsUserId(), arg.getExt(), arg.getCoverTaPath(), "fs-marketing-provider");
            if (StringUtils.isBlank(apath)) {
                log.info("VHallLiveServiceImpl -> updateLive failed tapath to apath error arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }
        if (arg.getCoverTaPath() != null && arg.getCoverTaPath().contains(host)) {
            apath = fileV2Manager.getApathByUrl(arg.getCoverTaPath());
            if (StringUtils.isBlank(apath)) {
                log.info("VHallLiveServiceImpl -> updateLive failed host url to apath error arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }

        //更新本地直播信息
        MarketingLiveEntity updateEntity = buildMarketingLiveEntity4Update(arg, entity, apath);
        int dbRet = marketingLiveDAO.updateMarketingLive(updateEntity);
        if (dbRet < 0) {
            log.info("VHallLiveServiceImpl -> updateLive failed db update failed:{}", arg);
            Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }

        if (!Objects.equals(entity.getStartTime(), updateEntity.getStartTime())) {
            sceneTriggerManager.handleLiveStartTimeChange(arg.getEa(), updateEntity.getId(), entity.getStartTime(), updateEntity.getStartTime());
        }
        if (!Objects.equals(entity.getEndTime(), updateEntity.getEndTime())) {
            sceneTriggerManager.handleLiveEndTimeChange(arg.getEa(), updateEntity.getId(), entity.getStartTime(), updateEntity.getStartTime());
        }

        if (updateEntity.getFormHexagonId() == null && apath != null) {
            String id = entity.getId();
            String coverPath = apath;
            CreateLiveVO vo = BeanUtil.copy(arg, CreateLiveVO.class);
            log.info("VHallLiveServiceImpl -> updateLive arg:{} vo:{} coverPath:{}", arg, vo, coverPath);
            String formHexagonSiteId = copyHexagon(vo, entity.getMarketingEventId(), coverPath);
            marketingLiveDAO.updateFormHexagonById(id, formHexagonSiteId);
            String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + entity.getMarketingEventId() + "&ea=" + arg.getEa() + "&objectType=26&id=" + formHexagonSiteId + "&type=1";
            crmV2Manager.updateMarketingEvenObjLandingPage(arg.getEa(), entity.getMarketingEventId(), arg.getFsUserId(), eventLandingPage);
        }

        integralServiceManager.asyncRegisterMaterial(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), CategoryApiNameConstant.LIVE, entity.getId(), arg.getTitle());
        CreateMarketingLiveResult result = new CreateMarketingLiveResult();
        result.setId(entity.getId());
        return Result.newSuccess(result);
    }
}
