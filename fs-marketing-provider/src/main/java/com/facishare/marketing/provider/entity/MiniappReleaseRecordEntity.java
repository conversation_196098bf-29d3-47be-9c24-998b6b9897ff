package com.facishare.marketing.provider.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序发布记录
 */
@Data
public class MiniappReleaseRecordEntity implements Serializable {
    private Long id;
    /** 微信小程序id */
    private String wxAppId;
    /**
     * @see com.facishare.marketing.common.enums.MiniappReleaseStatusEnum
     */
    private int status;
    /** 代码模板id */
    private String codeTemplateId;
    /** 代码版本 */
    private String codeVersion;
    /** 代码描述 */
    private String codeDescription;
    /** 微信审核id */
    private String auditId;
    /** 发布流程结束理由 */
    private String endReason;
    /**
     * 屏幕截图列表
     */
    private String screenShots;
    /** 提交审核时间 */
    private Date submitAuditTime;
    /** 审核结束时间 */
    private Date auditEndTime;
    private Date createTime;
    private Date updateTime;

    public MiniappReleaseRecordEntity(String wxAppId, String codeTemplateId, String codeVersion, String codeDescription, String endReason) {
        this.wxAppId = wxAppId;
        this.codeTemplateId = codeTemplateId;
        this.codeVersion = codeVersion;
        this.codeDescription = codeDescription;
        this.endReason = endReason;
    }
}
