package com.facishare.marketing.provider.service.advertiser.headlines;

import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.advertiser.headlines.*;
import com.facishare.marketing.api.service.advertiser.headlines.HeadlinesService;
import com.facishare.marketing.api.vo.advertiser.headlines.*;
import com.facishare.marketing.api.vo.baidu.QueryCampaignDetailVO;
import com.facishare.marketing.common.enums.advertiser.headlines.*;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsMappingDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dto.AdMarketingEventToClueCount;
import com.facishare.marketing.provider.dto.NewHeadLineAdListDTO;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdDataEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.advertiser.AdLeadDataManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("headlinesService")
public class HeadlinesServiceImpl implements HeadlinesService {
    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;
    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;
    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;

    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private AdLeadsMappingDataDAO adLeadsMappingDataDAO;

    @Autowired
    private AdLeadDataManager adLeadDataManager;

    private final String PARTTEN = "#.00";

    @ReloadableProperty("headlines.app.id")
    private Long APP_ID;


    @Override
    public Result<PageResult<HeadlinesCampaignInfoResult>> queryCampaignList(QueryHeadlinesCampaignListVO vo) {
        if (vo.getPageSize() == null || vo.getPageNum() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<HeadlinesCampaignInfoResult> headlinesCampaignInfoList = Lists.newArrayList();
        PageResult<HeadlinesCampaignInfoResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<HeadlinesCampaignEntity> headlinesCampaignEntities = headlinesCampaignDAO.pageCampaign(vo.getEa(), vo.getAdAccountId(), vo.getStatus(), vo.getNameKey(), page);
        if (CollectionUtils.isEmpty(headlinesCampaignEntities)) {
            return Result.newSuccess(pageResult);
        }
        for (int i = 0; i < headlinesCampaignEntities.size(); i++) {
            HeadlinesCampaignInfoResult headlinesCampaignInfoResult = new HeadlinesCampaignInfoResult();
            headlinesCampaignInfoResult.setId(headlinesCampaignEntities.get(i).getId());
            headlinesCampaignInfoResult.setCampaignId(headlinesCampaignEntities.get(i).getCampaignId());
            headlinesCampaignInfoResult.setCampaignName(headlinesCampaignEntities.get(i).getCampaignName());
            headlinesCampaignInfoResult.setBudget(headlinesCampaignEntities.get(i).getBudget());
            headlinesCampaignInfoResult.setBudgetMode(BudgetModeEnum.getDescByStatus(headlinesCampaignEntities.get(i).getBudgetMode()));
            headlinesCampaignInfoResult.setDeliveryMode(DeliveryModeEnum.getDescByStatus(headlinesCampaignEntities.get(i).getDeliveryMode()));
            headlinesCampaignInfoResult.setLandingType(LandingTypeEnum.getDescByStatus(headlinesCampaignEntities.get(i).getLandingType()));
            headlinesCampaignInfoResult.setMarketingPurpose(CampaignMarketingPurposeEnum.getDescByStatus(headlinesCampaignEntities.get(0).getMarketingPurpose()));
            headlinesCampaignInfoResult.setStatus(HeadlinesCampaignStatusEnum.getDescByStatus(headlinesCampaignEntities.get(i).getStatus()));
            headlinesCampaignInfoResult.setRefreshTime(headlinesCampaignEntities.get(i).getRefreshTime() == null ? null : headlinesCampaignEntities.get(i).getRefreshTime().getTime());
            headlinesCampaignInfoList.add(headlinesCampaignInfoResult);

        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(headlinesCampaignInfoList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<HeadlinesAdPlanResult>> queryAdPlanList(QueryHeadlinesAdPlanListVO vo) {
        if (vo.getPageNum() == null || vo.getPageSize() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<HeadlinesAdPlanResult> headlinesAdPlanResults = Lists.newArrayList();
        PageResult<HeadlinesAdPlanResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        if (StringUtils.isNotBlank(vo.getGroupNameKey())) {
            List<Long> campaignIdList = headlinesCampaignDAO.pageCampaignIdByName(vo.getEa(), vo.getAdAccountId(), vo.getGroupNameKey());
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return Result.newSuccess(pageResult);
            }
            vo.setCampaignIdList(campaignIdList);
        }
        List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.pageAd(vo.getEa(), vo.getAdAccountId(), vo.getStatus(), vo.getCampaignId(),
                vo.getNameKey(), vo.getStartTime(), vo.getEndTime(), vo.getCampaignIdList(),page);
        if (CollectionUtils.isEmpty(headlinesAdEntityList)) {
            return Result.newSuccess(pageResult);
        }
        List<Long> campaignIdList = headlinesAdEntityList.stream().map(HeadlinesAdEntity::getCampaignId).collect(Collectors.toList());
        Map<Long, HeadlinesCampaignEntity> campaignEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            List<HeadlinesCampaignEntity> campaignEntityList = headlinesCampaignDAO.queryCampaignListByCampaignIds(vo.getEa(), vo.getAdAccountId(),
                    AdSourceEnum.SOURCE_JULIANG.getSource(), campaignIdList, TypeEnum.HEADLINES_CAMPAIGN.getCode());
            if (CollectionUtils.isNotEmpty(campaignEntityList)) {
                campaignEntityMap = campaignEntityList.stream().collect(Collectors.toMap(HeadlinesCampaignEntity::getCampaignId, e -> e, (v1, v2) -> v1));
            }
        }
        List<Long> adIdList = headlinesAdEntityList.stream().map(HeadlinesAdEntity::getAdId).collect(Collectors.toList());
        Map<Long, List<HeadlinesAdDataEntity>> adDataEntityListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adIdList)) {
            List<HeadlinesAdDataEntity> headlinesAdDataEntityList = headlinesAdDataDAO.getByAdIdListAndDate(vo.getEa(), adIdList, vo.getStartTime(), vo.getEndTime());
            if (CollectionUtils.isNotEmpty(headlinesAdDataEntityList)) {
                adDataEntityListMap = headlinesAdDataEntityList.stream().collect(Collectors.groupingBy(HeadlinesAdDataEntity::getAdId));
            }
        }

        for (int i = 0; i < headlinesAdEntityList.size(); i++) {
            HeadlinesAdPlanResult headlinesAdPlanResult = new HeadlinesAdPlanResult();
            HeadlinesAdEntity headlinesAdEntity = headlinesAdEntityList.get(i);
            headlinesAdPlanResult.setId(headlinesAdEntity.getId());
            headlinesAdPlanResult.setAdId(headlinesAdEntity.getAdId());
            headlinesAdPlanResult.setAdName(headlinesAdEntity.getAdName());
            headlinesAdPlanResult.setCampaignId(headlinesAdEntity.getCampaignId());
            headlinesAdPlanResult.setDeliveryRange(DeliveryModeEnum.getDescByStatus(headlinesAdEntity.getDeliveryRange()));
            headlinesAdPlanResult.setInventoryCatalog(headlinesAdEntity.getInventoryCatalog() != null ? InventoryCataLogEnum.getDescByStatus(headlinesAdEntity.getInventoryCatalog()) : null);
            headlinesAdPlanResult.setInventoryType(headlinesAdEntity.getInventoryType() != null ? InventoryTypeEnum.getDescByStrs(headlinesAdEntity.getInventoryType()) : null);
            headlinesAdPlanResult.setOptStatus(HeadlinesOptStatusEnum.getDescByStatus(headlinesAdEntity.getOptStatus()));
            headlinesAdPlanResult.setStatus(HeadlinesAdStatusEnum.getDescByStatus(headlinesAdEntity.getStatus()));
            headlinesAdPlanResult.setMarketingEventId(headlinesAdEntity.getSubMarketingEventId());
            Integer clueCount = headlinesAdDataDAO.queryClueCountByAdId(vo.getEa(), vo.getAdAccountId(), headlinesAdEntity.getAdId());
            if (clueCount == null || clueCount == 0) {
                clueCount = campaignDataManager.getLeadsCountByMarketingEvent(vo.getEa(), Lists.newArrayList(headlinesAdEntity.getSubMarketingEventId()), null, null, null);
            }
            headlinesAdPlanResult.setClue(clueCount == null ? 0 : clueCount);
            headlinesAdPlanResult.setRefreshTime(headlinesAdEntity.getRefreshTime().getTime());

            List<HeadlinesAdDataEntity> adDataEntityList = adDataEntityListMap.get(headlinesAdEntity.getAdId());
            if (CollectionUtils.isNotEmpty(adDataEntityList)) {
                Long show = adDataEntityList.stream().mapToLong(HeadlinesAdDataEntity::getShow).sum();
                Long click = adDataEntityList.stream().mapToLong(HeadlinesAdDataEntity::getClick).sum();
                Double avgClickCost = adDataEntityList.stream().mapToDouble(HeadlinesAdDataEntity::getShow).sum();
                Double cost = adDataEntityList.stream().mapToDouble(HeadlinesAdDataEntity::getCost).sum();
                headlinesAdPlanResult.setShow(show);
                headlinesAdPlanResult.setClick(click);
                headlinesAdPlanResult.setAvgClickCost(avgClickCost);
                headlinesAdPlanResult.setCost(cost);
            }
            HeadlinesCampaignEntity headlinesCampaignEntity = campaignEntityMap.get(headlinesAdEntity.getCampaignId());
            if (headlinesCampaignEntity != null) {
                headlinesAdPlanResult.setGroupName(headlinesCampaignEntity.getCampaignName());
                headlinesAdPlanResult.setGroupMarketingEventId(headlinesCampaignEntity.getMarketingEventId());
            }
            headlinesAdPlanResults.add(headlinesAdPlanResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(headlinesAdPlanResults);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<HeadlinesCampaignDetailResult> queryHeadlinesCampaignDetail(QueryCampaignDetailVO vo) {
        // 广告组详情 暂时不做
        return null;
    }

    @Override
    public Result<Void> relateSubMarketingEvent(RelateSubMarketingEventVO vo) {
        HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.queryBySubMarketingEventId(vo.getEa(), vo.getAdAccountId(), vo.getSubMarketingEventId());
        if (headlinesAdEntity != null) {
            return Result.newError(SHErrorCode.AD_RELATE_SUB_MARKETING_ERROR);
        }

        HeadlinesAdEntity adEntity = headlinesAdDAO.queryAdById(vo.getId());
        if (adEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        try {
            headlinesAdDAO.relateAdSubMarketingEvent(vo.getId(), vo.getSubMarketingEventId(), vo.getMarketingEventId());
        } catch (Exception e) {
            log.info("headlinesServiceImpl relateSubMarketingEvent db exception: {}", e.fillInStackTrace());
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<HeadlinesAdPlanDataResult> queryAdData(QueryHeadlinesAdPlanListVO vo) {
        HeadlinesAdPlanDataResult result = new HeadlinesAdPlanDataResult();
        HeadlinesAdDataEntity headlinesAdDataEntity = headlinesAdDataDAO.queryAdDataByEaAndAdId(vo.getEa(), vo.getAdAccountId(), vo.getAdId(), vo.getStartTime(), vo.getEndTime());
        if (Objects.isNull(headlinesAdDataEntity)) {
            return Result.newSuccess(result);
        }
        List<String> subMarketingEventIds = Lists.newArrayList();
        Integer leadsCount = 0;
        String subMarketingEventId = headlinesAdDAO.querySubMarketingEventIdByAdId(vo.getEa(), vo.getAdAccountId(), vo.getSource(), vo.getAdId());
        if (StringUtils.isNotEmpty(subMarketingEventId)) {
            subMarketingEventIds.add(subMarketingEventId);
            leadsCount = campaignDataManager.getLeadsCountByMarketingEvent(vo.getEa(), subMarketingEventIds, vo.getStartTime().getTime(), vo.getEndTime().getTime(), null);
        }
        result.setAdId(vo.getAdId());
        result.setCampaignId(headlinesAdDataEntity.getCampaignId());
        result.setClick(headlinesAdDataEntity.getClick());
        DecimalFormat decimalFormat = new DecimalFormat(PARTTEN);
        result.setAvgClickCost(Double.valueOf(decimalFormat.format(headlinesAdDataEntity.getAvgClickCost())));
        result.setClue(leadsCount);
        result.setCost(headlinesAdDataEntity.getCost());
        result.setShow(headlinesAdDataEntity.getShow());
        result.setMarketingEventId(headlinesAdDataEntity.getSubMarketingEventId());
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<CluesTrendGraphDataResult>> queryCluesTrendGraphList(ClueTrendGraphDataVo vo) {
        List<CluesTrendGraphDataResult> cluesTrendGraphDataResultList = campaignDataManager.getLeadsDateListByMarketingEventId(vo.getEa(), vo.getMarketingEventId(), vo.getStartTime(), vo.getEndTime(), vo.getSource());
        return Result.newSuccess(cluesTrendGraphDataResultList);
    }

    @Override
    public Result<HeadlinesAdDetailResult> queryAdDetail(QueryHeadlinesAdDetailVO vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.queryAdById(vo.getId());
        if (headlinesAdEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(buildAdDetailRsult(headlinesAdEntity.getEa(), headlinesAdEntity, headlinesAdEntity.getSource(), vo.getStartTime(), vo.getEndTime()));
    }

    @Override
    public Result updateAdLeadsMappingData(AdLeadsMappingVO vo) {
        List<AdLeadsMappingDataEntity> adLeadsMappingDataEntities = adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(vo.getEa(), vo.getSource());
        if (CollectionUtils.isEmpty(adLeadsMappingDataEntities)) {
            adLeadsMappingDataDAO.addAdLeadsMappingData(UUIDUtil.getUUID(), vo.getEa(), vo.getFsUserId(), vo.getCrmRecordType(), vo.getSource(), vo.getCrmPoolId(), vo.getCustomFuncApiName());
        } else {
            for (AdLeadsMappingDataEntity adLeadsMappingDataEntity : adLeadsMappingDataEntities) {
                AdLeadsMappingDataEntity leadsMappingDataEntity = new AdLeadsMappingDataEntity();
                leadsMappingDataEntity.setId(adLeadsMappingDataEntity.getId());
                leadsMappingDataEntity.setEa(adLeadsMappingDataEntity.getEa());
                leadsMappingDataEntity.setAdAccountId(adLeadsMappingDataEntity.getAdAccountId());
                leadsMappingDataEntity.setCreateBy(vo.getFsUserId());
                leadsMappingDataEntity.setCrmPoolId(StringUtils.isNotEmpty(vo.getCrmPoolId()) ? vo.getCrmPoolId() : null);
                leadsMappingDataEntity.setCrmRecordType(vo.getCrmRecordType());
                leadsMappingDataEntity.setCustomFuncApiName(StringUtils.isNotEmpty(vo.getCustomFuncApiName()) ? vo.getCustomFuncApiName() : null);
                leadsMappingDataEntity.setUpdateTime(new Date());
                leadsMappingDataEntity.setSource(adLeadsMappingDataEntity.getSource());
                adLeadsMappingDataDAO.updateLeadsMappingData(leadsMappingDataEntity);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<QueryAdLeadsMappingDataResult> queryAdLeadsMappingData(QueryAdLeadsMappingVO vo) {
        List<AdLeadsMappingDataEntity> leadsMappingDataEntities = adLeadsMappingDataDAO.queryAdLeadsMappingDataByEaAndId(vo.getEa(), vo.getSource());
        if (CollectionUtils.isEmpty(leadsMappingDataEntities)) {
            return Result.newSuccess();
        }
        QueryAdLeadsMappingDataResult result = new QueryAdLeadsMappingDataResult();
        AdLeadsMappingDataEntity mappingDataEntity = leadsMappingDataEntities.get(0);
        result.setEa(mappingDataEntity.getEa());
        result.setCrmPoolId(mappingDataEntity.getCrmPoolId());
        result.setCustomFuncApiName(mappingDataEntity.getCustomFuncApiName());
        result.setCrmRecordType(mappingDataEntity.getCrmRecordType());
        return Result.newSuccess(result);
    }



    @Override
    public Result<QueryHeadlinesAppIdResult> queryHeadlinesAppId(QueryHeadlinesAppIdVO vo) {
        QueryHeadlinesAppIdResult queryHeadlinesAppIdResult = new QueryHeadlinesAppIdResult();
        queryHeadlinesAppIdResult.setAppId(APP_ID);
        return Result.newSuccess(queryHeadlinesAppIdResult);
    }

    @Override
    public Result<PageResult<HeadlinesAdPlanResult>> queryNewHeadlinesCampaignList(QueryHeadlinesAdPlanListVO vo) {
        if (vo.getPageNum() == null || vo.getPageSize() == null || vo.getStartTime() == null || vo.getEndTime() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<HeadlinesAdPlanResult> pageResult = new PageResult<>();
        List<HeadlinesAdPlanResult> list = new ArrayList<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());

        Page<NewHeadLineAdListDTO> page = new Page(vo.getPageNum(), vo.getPageSize(), false);
        Integer totalcount = headlinesCampaignDAO.countHeadLineAdListByCondition(vo.getEa(), vo.getAdAccountId(),vo.getGroupNameKey(),  vo.getNameKey(), null, null, vo.getStatus(), vo.getOptStatus());
        if(totalcount == 0){
            return Result.newSuccess(pageResult);
        }
        List<NewHeadLineAdListDTO> headLinesAdGroupDTOS =
                headlinesCampaignDAO.queryHeadLineAdListByCondition(vo.getEa(), vo.getAdAccountId(),vo.getGroupNameKey(), vo.getNameKey(),  null, null, vo.getStatus(), vo.getOptStatus(),page);
        if (CollectionUtils.isEmpty(headLinesAdGroupDTOS) || headLinesAdGroupDTOS.get(0) == null) {
            return Result.newSuccess(pageResult);
        }
        List<Long> adIds = headLinesAdGroupDTOS.stream().map(NewHeadLineAdListDTO::getAdId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adIds)) {
            return Result.newSuccess(pageResult);
        }
        Map<Long, List<HeadlinesAdDataEntity>> adDataEntityListMap = new HashMap<>();
        List<HeadlinesAdDataEntity> headlinesAdDataEntityList = headlinesAdDataDAO.getByAdIdListAndDate(vo.getEa(), adIds, vo.getStartTime(), vo.getEndTime());
        if (CollectionUtils.isNotEmpty(headlinesAdDataEntityList)) {
            adDataEntityListMap = headlinesAdDataEntityList.stream().collect(Collectors.groupingBy(HeadlinesAdDataEntity::getAdId));
        }
        Map<String, Integer> marketingEventIdToClueCountMap = Maps.newHashMap();
        List<String> subMarketingEventIdList = headLinesAdGroupDTOS.stream().map(NewHeadLineAdListDTO::getSubMarketingEventId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subMarketingEventIdList)) {
            List<AdMarketingEventToClueCount> clueCountList = adLeadDataManager.getMarketingEventIdToClueCount(vo.getEa(), subMarketingEventIdList, vo.getStartTime(), vo.getEndTime());
            clueCountList.forEach(e -> marketingEventIdToClueCountMap.put(e.getMarketingEventId(), e.getClueCount()));
        }
        for (int i = 0; i < headLinesAdGroupDTOS.size(); i++) {
            HeadlinesAdPlanResult headlinesAdPlanResult = new HeadlinesAdPlanResult();
            NewHeadLineAdListDTO headlinesAdEntity = headLinesAdGroupDTOS.get(i);
            headlinesAdPlanResult.setId(headlinesAdEntity.getId());
            headlinesAdPlanResult.setAdId(headlinesAdEntity.getAdId());
            headlinesAdPlanResult.setAdName(headlinesAdEntity.getAdName());
            headlinesAdPlanResult.setCampaignId(headlinesAdEntity.getCampaignId());
            headlinesAdPlanResult.setDeliveryRange(DeliveryModeEnum.getDescByStatus(headlinesAdEntity.getDeliveryRange()));
            headlinesAdPlanResult.setInventoryCatalog(headlinesAdEntity.getInventoryCatalog() != null ? InventoryCataLogEnum.getDescByStatus(headlinesAdEntity.getInventoryCatalog()) : null);
            headlinesAdPlanResult.setInventoryType(headlinesAdEntity.getInventoryType() != null ? InventoryTypeEnum.getDescByStrs(headlinesAdEntity.getInventoryType()) : null);
            headlinesAdPlanResult.setOptStatus(HeadlinesOptStatusEnum.getDescByStatus(headlinesAdEntity.getOptStatus()));
            headlinesAdPlanResult.setStatus(HeadlinesAdStatusEnum.getDescByStatus(headlinesAdEntity.getStatus()));
            headlinesAdPlanResult.setMarketingEventId(headlinesAdEntity.getSubMarketingEventId());
            headlinesAdPlanResult.setRefreshTime(headlinesAdEntity.getRefreshTime().getTime());
            int clueCount = marketingEventIdToClueCountMap.getOrDefault(headlinesAdEntity.getSubMarketingEventId(), 0);
            headlinesAdPlanResult.setClue(clueCount);
            List<HeadlinesAdDataEntity> adDataEntityList = adDataEntityListMap.get(headlinesAdEntity.getAdId());
            if (CollectionUtils.isNotEmpty(adDataEntityList)) {
                Long show = adDataEntityList.stream().mapToLong(HeadlinesAdDataEntity::getShow).sum();
                Long click = adDataEntityList.stream().mapToLong(HeadlinesAdDataEntity::getClick).sum();
                Double cost = adDataEntityList.stream().mapToDouble(HeadlinesAdDataEntity::getCost).sum();
                // 检查 click 是否为零，以避免除以零的情况
                Double avgClickCost;
                if (click == 0) {
                    avgClickCost = 0.0;
                } else {
                    // 使用 BigDecimal 进行精确计算
                    BigDecimal costBigDecimal = new BigDecimal(cost);
                    BigDecimal clickBigDecimal = new BigDecimal(click);
                    BigDecimal avgClickCostBigDecimal = costBigDecimal.divide(clickBigDecimal, 2, RoundingMode.HALF_UP);
                    avgClickCost = avgClickCostBigDecimal.doubleValue();
                }
                headlinesAdPlanResult.setShow(show);
                headlinesAdPlanResult.setClick(click);
                headlinesAdPlanResult.setAvgClickCost(avgClickCost);
                headlinesAdPlanResult.setCost(cost);
            }

            headlinesAdPlanResult.setGroupName(headlinesAdEntity.getCampaignName());
            headlinesAdPlanResult.setGroupMarketingEventId(headlinesAdEntity.getMarketingEventId());
            list.add(headlinesAdPlanResult);
        }
        pageResult.setTotalCount(totalcount);
        pageResult.setResult(list);
        return Result.newSuccess(pageResult);
    }


    private HeadlinesAdDetailResult buildAdDetailRsult(String ea, HeadlinesAdEntity headlinesAdEntity, String source, Date startTime, Date endTime) {
        HeadlinesAdDetailResult detailResult = new HeadlinesAdDetailResult();
        if (headlinesAdEntity == null) {
            return null;
        }
        List<HeadlinesAdDataEntity> headlinesAdDataEntityList = headlinesAdDataDAO.getByAdIdListAndDate(ea, Lists.newArrayList(headlinesAdEntity.getAdId()), startTime, endTime);
        if (CollectionUtils.isNotEmpty(headlinesAdDataEntityList)) {
            Long show = headlinesAdDataEntityList.stream().mapToLong(HeadlinesAdDataEntity::getShow).sum();
            Long click = headlinesAdDataEntityList.stream().mapToLong(HeadlinesAdDataEntity::getClick).sum();
            Double avgClickCost = headlinesAdDataEntityList.stream().mapToDouble(HeadlinesAdDataEntity::getShow).sum();
            Double cost = headlinesAdDataEntityList.stream().mapToDouble(HeadlinesAdDataEntity::getCost).sum();
            detailResult.setShow(show);
            detailResult.setClick(click);
            detailResult.setAvgClickCost(avgClickCost);
            detailResult.setCost(cost);
        }
        HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryCampaignByCampaignId(ea, headlinesAdEntity.getCampaignId());
        detailResult.setCampaignId(headlinesCampaignEntity.getCampaignId());
        detailResult.setCampaignName(headlinesCampaignEntity.getCampaignName());
        detailResult.setId(headlinesAdEntity.getId());
        detailResult.setAdId(headlinesAdEntity.getAdId());
        detailResult.setAdName(headlinesAdEntity.getAdName());
        String subMarketingEventId = headlinesAdEntity.getSubMarketingEventId();
        Map<String, MarketingEventData> eventDataMap = Maps.newHashMap();
        if (StringUtils.isNotEmpty(subMarketingEventId)) {
            List<MarketingEventData> eventDataList = marketingEventManager.listMarketingEventData(ea, -10000, Lists.newArrayList(subMarketingEventId));
            if (CollectionUtils.isNotEmpty(eventDataList)) {
                eventDataMap.putAll(eventDataList.stream().collect(Collectors.toMap(MarketingEventData::getId, v -> v, (k1, k2) -> k1)));
            }
        }

        //通过campaign名称关联线索
        int clueCount = campaignDataManager.getLeadsCountByMarketingEvent(ea, Lists.newArrayList(subMarketingEventId), null, null, null);

        if (StringUtils.isNotBlank(headlinesAdEntity.getSubMarketingEventId()) && eventDataMap.containsKey(headlinesAdEntity.getSubMarketingEventId())) {
            detailResult.setMarketingEventName(eventDataMap.get(headlinesAdEntity.getSubMarketingEventId()).getName());
            detailResult.setMarketingEventId(headlinesAdEntity.getSubMarketingEventId());
            detailResult.setEventType(eventDataMap.get(headlinesAdEntity.getSubMarketingEventId()).getEventType());
        }
        detailResult.setRefreshTime(headlinesAdEntity.getRefreshTime() == null ? null : headlinesAdEntity.getRefreshTime().getTime());
        if (StringUtils.isEmpty(headlinesAdEntity.getSubMarketingEventId())) {
            detailResult.setLeads(0);
        } else {
            detailResult.setLeads(clueCount);
        }
        return detailResult;
    }
}
