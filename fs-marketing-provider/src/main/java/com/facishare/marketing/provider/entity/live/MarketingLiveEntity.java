package com.facishare.marketing.provider.entity.live;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.model.SmsParamObject;
import com.facishare.marketing.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by zhengh on 2020/3/20.
 */
@Data
public class MarketingLiveEntity implements Serializable, SmsParamObject {
    //主键
    private String id;

    //公司id
    private Integer corpId;

    //直播id
    private Integer liveId;

    //市场活动id
    private String marketingEventId;

    //直播标题
    private String title;

    //直播简介
    private String description;

    //直播开始时间
    private Date startTime;

    //直播结束时间
    private Date endTime;

    //直播创建者userid
    private Integer createUserId;

    //讲师名称
    private String lectureName;

    //讲师直播密码
    private String lecturePassword;

    //标签
    private String tags;

    //讲师直播地址
    private String lectureUrl;

    //观众直播地址
    private String viewUrl;

    //直播状态
    private Integer status;

    //直播封面地址
    private String cover;

    //是否启动直播聊天 0:不开启  1：开启
    private Integer chatOn;

    //是否生成回放 0：不生成  1：生成
    private Integer autoRecord;

    // 直播最大运行观看观看次数
    private Integer maxLiveCount;

    //回放id
    private Integer recordId;

    //直播平台 1:纷享合作平台  2：其他 3:小鹅通
    private Integer platform;

    //直播短链
    private String shortViewUrl;

    //讲师短链
    private String shortLectureUrl;

    //报名表单对应微页面id
    private String formHexagonId;

    //外部直播原始链接
    private String otherPlatformUrl;

    //创建按时间
    private Date createTime;

    //更新时间
    private Date updateTime;

    //是否在小程序活动列表展示
    private Boolean showAcitivityList;

    //小鹅通直播id
    private String xiaoetongLiveId;

    //市场活动默认模板id
    private String marketingTemplateId;

    //是否为子活动 1是 0否 当前只有目睹场所活动需要自动创建
    private Integer subEvent = 0;

    // 目睹主活动id（便于和子级场所区分开）
    private String muduParentId;

    //关联的账号id,目前只有视频号需要关联
    private String associatedAccountId;

    @Override
    public Map<String, String> getParamDescMap() {
        Map<String, String> paramDescMap = new HashMap<>();
        paramDescMap.put("title", I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_118));
        paramDescMap.put("startTime", I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_119));
        paramDescMap.put("endTime", I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_120));
        paramDescMap.put("lecture", I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_121));
        paramDescMap.put("viewUrl", I18nUtil.get(I18nKeyEnum.MARK_LIVE_MARKETINGLIVEENTITY_122));
        return paramDescMap;
    }


    @Override
    public Map<String, String> getParamValueMap() {
        Map<String, String> paramValueMap = new HashMap<>();
        paramValueMap.put("title", title);
        paramValueMap.put("startTime", DateUtil.format(startTime).substring(0, 16));
        paramValueMap.put("endTime", DateUtil.format(endTime).substring(0, 16));
        paramValueMap.put("lecture", lectureName);
        paramValueMap.put("viewUrl", shortViewUrl);
        return paramValueMap;
    }
}