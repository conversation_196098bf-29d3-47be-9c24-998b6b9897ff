package com.facishare.marketing.provider.mq.handler;

import com.facishare.marketing.audit.log.context.MarketingAuditLogContext;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.mq.router.EnvironmentRouter;
import com.facishare.marketing.provider.mq.sender.MarketingMessageSender;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configurable(dependencyCheck = true)
public abstract class AbstractMessageHandler<T> implements MessageListenerConcurrently {
    public static final String MESSAGE_HANDLER = "messageHandler";
    public static final String MESSAGE_HANDLER_METHOD = "directHandle";
    public static final String MESSAGE_TYPE = "messageType";

    @ReloadableProperty("marketing_gray_list")
    private String marketingGrayList;

    @Autowired
    protected EnvironmentRouter environmentRouter;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            try {
                // 统一传递traceId
                String traceId = MessageHelper.getTraceId(msg);
                if (StringUtils.isNotBlank(traceId)) {
                    MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                } else {
                    TraceContext.get().setTraceId(UUIDUtil.getUUID());
                }
                T msgObj = getMsgObj(msg);
                String ea = getEa(msgObj);
                MarketingAuditLogContext.putVariable("messageId",msg.getMsgId());
                MarketingAuditLogContext.putVariable("cost9", System.currentTimeMillis() - msg.getBornTimestamp());
                MarketingAuditLogContext.putVariable("ea", ea);
                MarketingAuditLogContext.putVariable("traceId", traceId);
                MarketingAuditLogContext.putVariable("extra9", msg.getReconsumeTimes());

                // 使用新的环境路由器判断目标环境
                String targetEnvironment = environmentRouter.determineEnvironment(ea);
                if (StringUtils.isNotBlank(targetEnvironment)) {
                    forwardHandleToEnvironment(msgObj, msg.getTags(), ea, targetEnvironment);
                } else {
                    directHandle(msgObj);
                }
            } catch (Exception e) {
                log.warn("messageHandler exception messageExt:{} e:", msg.toString(), e);
            } finally {
                TraceContext.remove();
                MarketingAuditLogContext.clearContext();
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 转成具体对象
     *
     * @param msg
     * @return
     */
    protected abstract T getMsgObj(MessageExt msg);

    /**
     * 返回ea
     *
     * @return
     */
    protected abstract String getEa(T msgObj);

    /**
     * 转发处理消息到指定环境
     *
     * @param msgObj 消息对象
     * @param tag 消息标签
     * @param ea 企业账号
     * @param targetEnvironment 目标环境
     */
    protected void forwardHandleToEnvironment(T msgObj, String tag, String ea, String targetEnvironment) {
        try {
            MarketingMessageSender targetSender = environmentRouter.getSender(targetEnvironment, getMessageHandler(), tag);
            if (targetSender != null) {
                targetSender.send(getMessageHandler(), msgObj, tag, ea);
            } else {
                // 找不到对应的发送器，直接当前环境处理
                directHandle(msgObj);
            }
        } catch (Exception e) {
            log.error("Failed to forward message to environment {}, processing locally", targetEnvironment, e);
            directHandle(msgObj);
        }
    }

    private String getMessageHandler() {
        return this.getClass().getSimpleName();
    }

    /**
     * 直接处理消息
     *
     * @param msgObj
     * @return
     */
    protected abstract void directHandle(T msgObj);
}
