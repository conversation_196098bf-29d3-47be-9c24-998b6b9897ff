package com.facishare.marketing.provider.service.material;

import com.facishare.marketing.api.service.marketingplugin.MarketingPluginService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.beust.jcommander.internal.Sets;
import com.facishare.marketing.api.result.MaterialShowResult;
import com.facishare.marketing.api.result.distribution.QueryPlanListResult;
import com.facishare.marketing.api.service.open.material.MaterialShowSettingService;
import com.facishare.marketing.api.vo.material.UpdateMaterialShowSettingVO;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.MemberTypeEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.material.MaterialTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.OperatorDao;
import com.facishare.marketing.provider.dao.material.MaterialShowSettingDAO;
import com.facishare.marketing.provider.entity.material.MaterialShowSettingEntity;
import com.facishare.marketing.provider.manager.MarketingPluginConfigManager;
import com.facishare.marketing.provider.manager.MemberMarketingManager;
import com.facishare.marketing.provider.manager.kis.KisPermissionManager;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("materialShowSettingService")
public class MaterialShowSettingServiceImpl implements MaterialShowSettingService {

    @Autowired
    private MaterialShowSettingDAO materialShowSettingDAO;

    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;

    @Autowired
    private KisPermissionManager kisPermissionManager;

    @Autowired
    private OperatorDao operatorDao;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    @Autowired
    private MarketingPluginService marketingPluginService;

    private static Map<Integer, MaterialShowResult> initMaterialShowMap = Maps.newHashMap();

    // 会员类型为伙伴的可见的菜单
    private static Set<Integer> PARTNER_MEMBER_MATERIAL_SHOW_TYPE = Sets.newHashSet();

    // 会员类型为员工的可见的菜单
    private static Set<Integer> EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE = Sets.newHashSet();

    static {
        initMaterialShowMap.put(MaterialTypeEnum.CARD.getType(), new MaterialShowResult(MaterialTypeEnum.CARD.getType(),MaterialTypeEnum.CARD.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.ARTICLE.getType(), new MaterialShowResult(MaterialTypeEnum.ARTICLE.getType(),MaterialTypeEnum.ARTICLE.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.PRODUCT.getType(), new MaterialShowResult(MaterialTypeEnum.PRODUCT.getType(),MaterialTypeEnum.PRODUCT.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.IMAGE.getType(), new MaterialShowResult(MaterialTypeEnum.IMAGE.getType(),MaterialTypeEnum.IMAGE.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.FILE.getType(), new MaterialShowResult(MaterialTypeEnum.FILE.getType(),MaterialTypeEnum.FILE.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.VIDEO.getType(), new MaterialShowResult(MaterialTypeEnum.VIDEO.getType(),MaterialTypeEnum.VIDEO.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.POSTER.getType(), new MaterialShowResult(MaterialTypeEnum.POSTER.getType(),MaterialTypeEnum.POSTER.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.ACTIVITY.getType(), new MaterialShowResult(MaterialTypeEnum.ACTIVITY.getType(),MaterialTypeEnum.ACTIVITY.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.LIVE.getType(), new MaterialShowResult(MaterialTypeEnum.LIVE.getType(),MaterialTypeEnum.LIVE.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.CONFERENCE.getType(), new MaterialShowResult(MaterialTypeEnum.CONFERENCE.getType(),MaterialTypeEnum.CONFERENCE.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.MICRO_STATION.getType(), new MaterialShowResult(MaterialTypeEnum.MICRO_STATION.getType(),MaterialTypeEnum.MICRO_STATION.getName(),true));
        //initMaterialShowMap.put(MaterialTypeEnum.SOCIAL_DISTRIBUTION.getType(), new MaterialShowResult(MaterialTypeEnum.SOCIAL_DISTRIBUTION.getType(),MaterialTypeEnum.SOCIAL_DISTRIBUTION.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.ENTERPRISE_REPORT.getType(), new MaterialShowResult(MaterialTypeEnum.ENTERPRISE_REPORT.getType(),MaterialTypeEnum.ENTERPRISE_REPORT.getName(),true));
        initMaterialShowMap.put(MaterialTypeEnum.SPREAD_REPORT.getType(), new MaterialShowResult(MaterialTypeEnum.SPREAD_REPORT.getType(),MaterialTypeEnum.SPREAD_REPORT.getName(),true));

        // 会员类型为伙伴的可见的菜单
        PARTNER_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.CARD.getType());
        PARTNER_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.MICRO_STATION.getType());
        PARTNER_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.ARTICLE.getType());
        PARTNER_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.PRODUCT.getType());
        // 会员类型为员工的可见的菜单
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.CARD.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.ARTICLE.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.PRODUCT.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.IMAGE.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.FILE.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.VIDEO.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.POSTER.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.ACTIVITY.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.LIVE.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.CONFERENCE.getType());
        EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE.add(MaterialTypeEnum.MICRO_STATION.getType());
    }



    @Override
    public Result<Void> saveOrUpdateMaterialShowSetting(UpdateMaterialShowSettingVO vo) {
        Preconditions.checkArgument(null != vo.getType(),I18nUtil.get(I18nKeyEnum.MARK_MATERIAL_MATERIALSHOWSETTINGSERVICEIMPL_99));
        MaterialShowSettingEntity showSettingEntity = materialShowSettingDAO.getByTypeAndEa(vo.getEa(), vo.getType());
        if (showSettingEntity == null) {
            //进行新增数据
            MaterialShowSettingEntity materialShowSettingEntity = BeanUtil.copy(vo,MaterialShowSettingEntity.class);
            materialShowSettingEntity.setId(UUIDUtil.getUUID());
            materialShowSettingDAO.saveMaterialShowSetting(materialShowSettingEntity);
            return Result.newSuccess();
        }
        //进行更新操作
        materialShowSettingDAO.updateShowSetting(showSettingEntity.getId(),vo.getShowName(),vo.getShowStatus());
        return Result.newSuccess();
    }

    @Override
    public Result<List<MaterialShowResult>> queryMaterialShowList(String ea,Integer fsUid) {
        List<MaterialShowSettingEntity> materialShowSettingEntities = materialShowSettingDAO.queryMaterialSettingListByEa(ea);
        List<MaterialShowResult> materialShowResults = BeanUtil.copy(materialShowSettingEntities, MaterialShowResult.class);
        Map<Integer,MaterialShowResult> queryMaterialShowMap = Maps.newHashMap();
        List<MaterialShowResult> materialShowResultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(materialShowResults)) {
            queryMaterialShowMap = materialShowResults.stream().collect(Collectors.toMap(MaterialShowResult::getType, Function.identity()));
        }
        Map<Integer, MaterialShowResult> finalQueryMaterialShowMap = queryMaterialShowMap;
        initMaterialShowMap.values().forEach(materialShowResult -> {
            MaterialShowResult materialShowSetting = materialShowResult;
            if (finalQueryMaterialShowMap.containsKey(materialShowResult.getType())) {
                materialShowSetting = finalQueryMaterialShowMap.get(materialShowResult.getType());
            } else {
//                if (MaterialTypeEnum.SOCIAL_DISTRIBUTION.getType().equals(materialShowResult.getType())) {
//                    materialShowSetting.setShowStatus(marketingPluginConfigManager.getCurrentPluginStatus(ea, MarketingPluginTypeEnum.SOCIETY_DISTRIBUTE.getType()));
//                }
                if (MaterialTypeEnum.IMAGE.getType().equals(materialShowResult.getType())) {
                    materialShowSetting.setShowStatus(marketingPluginConfigManager.getCurrentPluginStatus(ea, MarketingPluginTypeEnum.MOBILE_PHOTO_LIBRARY.getType()));
                }
            }
            if (MaterialTypeEnum.ENTERPRISE_REPORT.getType().equals(materialShowResult.getType()) && materialShowSetting.getShowStatus() && fsUid != null) {
                materialShowSetting.setShowStatus(kisPermissionManager.isAppAdmin(ea,fsUid));
            }
//            if (MaterialTypeEnum.SOCIAL_DISTRIBUTION.getType().equals(materialShowResult.getType()) && materialShowSetting.getShowStatus() && fsUid != null) {
//                List<QueryPlanListResult> planList = operatorDao.queryPlanList(ea, fsUid);
//                materialShowSetting.setShowStatus(CollectionUtils.isNotEmpty(planList));
//            }
            materialShowResultList.add(materialShowSetting);
        });
        // 仅fs和测试环境展示AI助手
//        if ("fs".equals(ea) || StringUtils.contains(System.getProperty("process.profile"), "fstest")) {
//            materialShowResultList.add(new MaterialShowResult(MaterialTypeEnum.AI_HELPER.getType(), MaterialTypeEnum.AI_HELPER.getName(), true));
//        }
        // 判断是否有license: marketing_ai_plugin_app
        if (marketingPluginService.checkLicense(ea, VersionEnum.MARKETING_AI_PLUGIN_APP.getVersion()).getData()) {
            materialShowResultList.add(new MaterialShowResult(MaterialTypeEnum.AI_HELPER.getType(), MaterialTypeEnum.AI_HELPER.getName(), true));
        }
        // 如果是会员虚拟员工, 会员员工和正常员工除社会化分销、企业简报、推广简报外，其他和正常员工保持一致，会员伙伴只展示名片，微站，文章，产品
        if (QywxUserConstants.isMemberVirtualUserId(fsUid)) {
            String memberType = memberMarketingManager.getMemberType(ea, fsUid);
            Set<Integer> visibleTypeSet = MemberTypeEnum.PARTNER.getType().equals(memberType) ? PARTNER_MEMBER_MATERIAL_SHOW_TYPE : EMPLOYEE_MEMBER_MATERIAL_SHOW_TYPE;
            materialShowResultList.forEach(e -> {
                if (!visibleTypeSet.contains(e.getType())) {
                    e.setShowStatus(false);
                }
            });
        }
        return Result.newSuccess(materialShowResultList);
    }
}