package com.facishare.marketing.provider.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.AddObjectDescriptionArg;
import com.facishare.marketing.api.arg.QueryObjectDescriptionDetailArg;
import com.facishare.marketing.api.arg.UpdateObjectDescriptionArg;
import com.facishare.marketing.api.result.FieldMappingResult;
import com.facishare.marketing.api.result.ObjectDescriptionDetailResult;
import com.facishare.marketing.api.result.ObjectDescriptionFromResult;
import com.facishare.marketing.api.result.QueryObjectDescriptionDetailResult;
import com.facishare.marketing.api.result.ReturnObjectDescriptionResult;
import com.facishare.marketing.api.service.ObjectDescriptionService;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ObjectDescriptionDAO;
import com.facishare.marketing.provider.dao.ObjectFieldMappingDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.entity.ObjectDescriptionEntity;
import com.facishare.marketing.provider.entity.ObjectFieldMappingEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.manager.FileManager;
import com.facishare.marketing.provider.manager.ObjectFieldMappingManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.google.common.base.Strings;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("objectDescriptionService")
@Slf4j
public class ObjectDescriptionServiceImpl implements ObjectDescriptionService {
    private static final String API_NAME_PRODUCT = "try_out_product_form";
    @Autowired
    private ObjectFieldMappingManager objectFieldMappingManager;
    @Autowired
    private ObjectFieldMappingDAO objectFieldMappingDAO;
    @Autowired
    private ObjectDescriptionDAO objectDescriptionDAO;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;

    @Override
    public Result<QueryObjectDescriptionDetailResult> getObjectDescriptionDetail(QueryObjectDescriptionDetailArg vo) {

        ObjectDescriptionEntity entity = objectDescriptionDAO.getObjectDescriptionDetail(vo.getApiName(), vo.getEa());
        if (entity == null) {
            return new Result<>(SHErrorCode.OBJECT_DESCRIPTION_NOT_EXIST);
        }
        QueryObjectDescriptionDetailResult qresult = new QueryObjectDescriptionDetailResult();
        qresult.setId(entity.getId());
        qresult.setApiName(entity.getApiName());
        qresult.setDisplayName(entity.getDisplayName());
        qresult.setFieldDescribes(entity.getFieldDescribes());
        qresult.setWelcomeMsg(entity.getWelcomeMsg());
        qresult.setSubmitFollowingActionSelect(entity.getSubmitFollowingActionSelect());
        qresult.setButtonContent(entity.getButtonContent());
        qresult.setButtonText(entity.getButtonText());
        qresult.setSuccessPromptMsg(entity.getSuccessPromptMsg());
        ObjectFieldMappingEntity fieldMappingEntity = objectFieldMappingDAO.getBySourceObjectApiNameAndTargetObjectApiName(vo.getEa(), vo.getApiName(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (fieldMappingEntity != null) {
            qresult.setIsEnrollSyncToLeads(fieldMappingEntity.getIsSyncToCrm());
            qresult.setEnrollLeadsFieldMappings(BeanUtil.copy(fieldMappingEntity.getFieldMappingsV2(), FieldMappingResult.class));
            if (qresult.getEnrollLeadsFieldMappings() != null) {
                Iterator<FieldMappingResult> iterator = qresult.getEnrollLeadsFieldMappings().iterator();
                while (iterator.hasNext()) {
                    FieldMappingResult fieldMappingResult = iterator.next();
                    if (CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName().equals(fieldMappingResult.getCrmFieldName())) {
                        qresult.setCrmLeadPoolId(fieldMappingResult.getDefaultValue());
                        iterator.remove();
                    }
                }
            }
            qresult.setEnrollLeadsTargetObjectRecordType(fieldMappingEntity.getTargetRecordType());
        }

        return new Result<>(SHErrorCode.SUCCESS, qresult);
    }

    @Override
    public Result<ObjectDescriptionDetailResult> isExistObjectDescription(String ea, String apiName) {

        ObjectDescriptionEntity entity = objectDescriptionDAO.getObjectDescriptionDetail(apiName, ea);
        if (entity == null) {
            return new Result<>(SHErrorCode.SUCCESS);
        }
        ObjectDescriptionDetailResult qresult = new ObjectDescriptionDetailResult();
        qresult.setApiName(entity.getApiName());
        qresult.setDisplayName(entity.getDisplayName());
        return new Result<>(SHErrorCode.SUCCESS, qresult);
    }

    @Override
    public Result<ObjectDescriptionFromResult> getEaObjectDescriptionForm(String productId) {
        //根据产品ID 查出产品ea
        ProductEntity productEntity = productDAO.queryProductDetail(productId);
        ObjectDescriptionEntity entity = null;
        if (productEntity != null) {
            //根据产ea 和apiName 查出对象信息
            entity = objectDescriptionDAO.getObjectDescriptionDetail(API_NAME_PRODUCT, productEntity.getFsEa());
            if (entity == null) {
                return new Result<>(SHErrorCode.OBJECT_DESCRIPTION_NOT_EXIST);
            }
        }
        ObjectDescriptionFromResult result = BeanUtil.copy(entity, ObjectDescriptionFromResult.class);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    @Transactional
    public Result<ReturnObjectDescriptionResult> addObjectDescription(AddObjectDescriptionArg vo) {
        if (vo.getIsEnrollSyncToLeads()) {
            List<FieldMappings.FieldMapping> fieldMappings = BeanUtil.copy(vo.getEnrollLeadsFieldMappings(), FieldMappings.FieldMapping.class);
            String validateResult = crmV2MappingManager
                .doVerifyMankeepCrmObjectFieldMappingEntities(vo.getEa(), CrmObjectApiNameEnum.CRM_LEAD.getName(), FieldMappings.newInstance(fieldMappings),
                    vo.getFieldDescribes());
            if (!CrmV2MappingManager.FieldValidateMessage.FIELD_SUCCESS.equals(validateResult)) {
                return new Result<>(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR.getErrorCode(), validateResult);
            }
        }
        ObjectDescriptionEntity ode = new ObjectDescriptionEntity();
        ode.setId(UUIDUtil.getUUID());
        ode.setApiName(API_NAME_PRODUCT);//产品统一apiName
        ode.setFieldDescribes(vo.getFieldDescribes());
        ode.setDisplayName(vo.getDisplayName());
        ode.setWelcomeMsg(vo.getWelcomeMsg());
        ode.setEa(vo.getEa());
        ode.setSubmitFollowingActionSelect(vo.getSubmitFollowingActionSelect());
        ode.setButtonText(vo.getButtonText());
        ode.setButtonContent(getButtonContent(vo.getSubmitFollowingActionSelect(),vo.getEa(),vo.getButtonContent()));
        ode.setSuccessPromptMsg(vo.getSuccessPromptMsg());
        objectDescriptionDAO.add(ode);

        if (vo.getIsEnrollSyncToLeads() != null && vo.getIsEnrollSyncToLeads() && !Strings.isNullOrEmpty(vo.getCrmLeadPoolId())) {
            objectFieldMappingManager.mergeFieldMapping(vo.getEa(), ode.getApiName(), CrmObjectApiNameEnum.CRM_LEAD.getName(), vo.getEnrollLeadsTargetObjectRecordType(), vo.getIsEnrollSyncToLeads(),
                addLeadPoolId(vo.getCrmLeadPoolId(), vo.getEnrollLeadsFieldMappings()));
        }

        return new Result<>(SHErrorCode.SUCCESS, new ReturnObjectDescriptionResult(ode.getApiName()));
    }

    @Override
    @Transactional
    public Result<Void> updateObjectDescription(UpdateObjectDescriptionArg vo) {
        if (vo.getIsEnrollSyncToLeads()) {
            List<FieldMappings.FieldMapping> fieldMappings = BeanUtil.copy(vo.getEnrollLeadsFieldMappings(), FieldMappings.FieldMapping.class);
            String validateResult = crmV2MappingManager
                .doVerifyMankeepCrmObjectFieldMappingEntities(vo.getEa(), CrmObjectApiNameEnum.CRM_LEAD.getName(), FieldMappings.newInstance(fieldMappings),
                    vo.getFieldDescribes());
            if (!CrmV2MappingManager.FieldValidateMessage.FIELD_SUCCESS.equals(validateResult)) {
                return new Result<>(SHErrorCode.FIELD_MAPPING_VERIFY_ERROR.getErrorCode(), validateResult);
            }
        }
        ObjectDescriptionEntity ode = new ObjectDescriptionEntity();
        ode.setApiName(vo.getApiName());
        ode.setFieldDescribes(vo.getFieldDescribes());
        ode.setDisplayName(vo.getDisplayName());
        ode.setWelcomeMsg(vo.getWelcomeMsg());
        ode.setEa(vo.getEa());
        ode.setSubmitFollowingActionSelect(vo.getSubmitFollowingActionSelect());
        ode.setButtonText(vo.getButtonText());
        ode.setButtonContent(getButtonContent(vo.getSubmitFollowingActionSelect(),vo.getEa(),vo.getButtonContent()));
        ode.setSuccessPromptMsg(vo.getSuccessPromptMsg());
        objectDescriptionDAO.update(ode);
        if (vo.getIsEnrollSyncToLeads() != null && vo.getIsEnrollSyncToLeads() && !Strings.isNullOrEmpty(vo.getCrmLeadPoolId())) {
            objectFieldMappingManager.mergeFieldMapping(vo.getEa(), vo.getApiName(), CrmObjectApiNameEnum.CRM_LEAD.getName(), vo.getEnrollLeadsTargetObjectRecordType(), vo.getIsEnrollSyncToLeads(),
                addLeadPoolId(vo.getCrmLeadPoolId(), vo.getEnrollLeadsFieldMappings()));
        } else {
            objectFieldMappingManager.disableFieldMapping(vo.getEa(), vo.getApiName(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        }
        return new Result<>(SHErrorCode.SUCCESS);
    }

    private  String getButtonContent(Integer sign,String ea,String buttonContent){
        //如果是公众号，组合json数据
        if(sign==1){
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("ea",ea);
            jsonObject.addProperty("publicAccountAppId",buttonContent);
            return jsonObject.toString();
        }else if(sign==2){//资料查看
            JSONObject json =   JSONObject.parseObject(buttonContent);
            json.put("FileUrl",fileManager.getPreviewUrl(ea,json.get("FilePath").toString()));
            return json.toString();
        }
        return  buttonContent;
    }

    private List<FieldMappingResult> addLeadPoolId(String leadPoolId, List<FieldMappingResult> fieldMappingResults) {
        FieldMappingResult fieldMappingResult = new FieldMappingResult();
        fieldMappingResult.setModifiable(false);
        fieldMappingResult.setDefaultValue(leadPoolId);
        fieldMappingResult.setCrmFieldName(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName());
        if (fieldMappingResults == null) {
            List<FieldMappingResult> list = new ArrayList<>();
            list.add(fieldMappingResult);
            return list;
        }
        for (FieldMappingResult mappingVO : fieldMappingResults) {
            if (CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName().equals(mappingVO.getCrmFieldName())) {
                mappingVO.setMankeepFieldName(null);
                mappingVO.setDefaultValue(leadPoolId);
                mappingVO.setModifiable(false);
                return fieldMappingResults;
            }
        }
        fieldMappingResults.add(fieldMappingResult);
        return fieldMappingResults;
    }
}
