package com.facishare.marketing.provider.dao.qr;

import com.facishare.marketing.provider.dao.param.qrposter.QRPosterQueryParam;
import com.facishare.marketing.provider.dto.qrpost.PosterTargetDTO;
import com.facishare.marketing.provider.dto.qrpost.QRPosterEntityDTO;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface QRPosterDAO {
    @Select("SELECT * FROM qr_poster WHERE id = #{id}")
    QRPosterEntity queryById(@Param("id") String id);

    @Select("SELECT * FROM qr_poster WHERE id = #{id} and ea = #{ea}")
    QRPosterEntity queryByIdAndEa(@Param("id") String id, @Param("ea") String ea);

    @Select("<script>" + "SELECT * FROM qr_poster WHERE status = 1 AND id IN " + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" + "#{item}" + "</foreach>" + "</script>")
    List<QRPosterEntity> getByIds(@Param("ids") List<String> ids);


    @Select("<script>" + "SELECT id,title FROM qr_poster WHERE id IN " + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" + "#{item}" + "</foreach>" + "</script>")
    List<QRPosterEntity> getNameByIds(@Param("ids") List<String> ids);

    @Select("<script>" + "SELECT * FROM qr_poster WHERE id IN " + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" + "#{item}" + "</foreach>" + "</script>")
    List<QRPosterEntity> getAllByIds(@Param("ids") List<String> ids);

    @Select("<script>" + "SELECT * FROM qr_poster WHERE status = 1 AND type = #{type} AND id IN " + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" + "#{item}" + "</foreach>" + "</script>")
    List<QRPosterEntity> getByTypeAndIds(@Param("type") Integer type, @Param("ids") List<String> ids);

    @Select("SELECT * FROM qr_poster WHERE ea=#{ea} AND target_id=#{targetId}")
    List<QRPosterEntity> getByTargetIds(@Param("ea")String ea, @Param("targetId")String targetId);

    @Select("<script> "
            + "SELECT * FROM qr_poster WHERE ea = #{ea} AND status = 1"
            + " <if test=\"title != null and title !='' \">\n"
            + "  AND title LIKE CONCAT('%',#{title},'%')\n"
            + " </if>\n"
            + " <if test=\"marketingEventId != null and marketingEventId !='' \">\n"
            + "  AND marketing_event_id = #{marketingEventId}\n"
            + " </if>\n"
            + " <if test=\"type != null\">\n"
            + "  AND type = #{type}\n"
            + " </if>\n"
            + " order by create_time desc"
            + "</script>")
    List<QRPosterEntity> queryByEa(@Param("ea") String ea, @Param("page") Page page, @Param("marketingEventId") String marketingEventId, @Param("title") String title, @Param("type") Integer type);

    @Select("<script> "
            + "(SELECT * FROM qr_poster WHERE ea = #{ea} AND status = 1 AND target_id = #{targetId}"
            + " <if test=\"type != null\">\n"
            + "  AND type = #{type}\n"
            + " </if>\n"
            + " order by create_time desc)"
            + "</script>")
    List<QRPosterEntity> queryListByTypeAndTargetId(@Param("ea") String ea, @Param("type") Integer type, @Param("targetId") String targetId, @Param("page") Page page);

    @Select("<script> "
            + "(SELECT * FROM qr_poster WHERE ea = #{ea} AND status = 1 AND target_id = #{targetId}"
            + " <if test=\"type != null\">\n"
            + "  AND type = #{type}\n"
            + " </if>\n"
            + " order by create_time desc)"
            + "</script>")
    List<QRPosterEntity> queryListByTypeAndTargetIdNonPage(@Param("ea") String ea, @Param("type") Integer type, @Param("targetId") String targetId);

    @Select("<script> "
            + "(SELECT * FROM qr_poster WHERE ea = #{ea} AND status = 1"
            + " <if test=\"marketingEventId != null\">\n"
            + "  AND marketing_event_id = #{marketingEventId}\n"
            + " </if>\n"
            + "AND forward_type IN "
            + "<foreach collection = 'forwardTypes' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " <if test=\"targetId != null\">\n"
            + "  AND target_id = #{targetId}\n"
            + " </if>\n"
            + " <if test=\"type != null\">\n"
            + "  AND type = #{type}\n"
            + " </if>\n"
            + " order by create_time desc)"
            + "</script>")
    List<QRPosterEntity> queryByForwardTypeAndTargetId(@Param("ea") String ea,@Param("marketingEventId") String marketingEventId, @Param("forwardTypes") List<Integer> forwardTypes, @Param("targetId") String targetId, @Param("page") Page page, @Param("type") Integer type);

    @Select("<script> "
            + "SELECT * FROM qr_poster WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId} AND status = 1 AND forward_type IN"
            + "<foreach collection = 'forwardTypes' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + " order by create_time desc"
            + "</script>")
    List<QRPosterEntity> queryByMarketingEventIdAndForwardTypes(@Param("ea") String ea, @Param("forwardTypes") List<Integer> forwardTypes, @Param("marketingEventId") String marketingEventId);

    @Insert("INSERT INTO qr_poster( \"id\", \"marketing_event_id\", \"forward_type\", \"qr_code_id\", \"external_qr_url\", \"target_id\", \"status\", \"ea\", \"title\", \"bg_apath\", \"bg_thumbnail_apath\", \"apath\", \"thumbnail_apath\", \"qr_style\", \"create_by\", \"create_time\", \"update_time\", \"type\", \"poster_style\", \"is_mobile_display\",\"user_add_settings\",\"failed_operation\") VALUES ( #{id}, #{marketingEventId}, #{forwardType}, #{qrCodeId}, #{externalQrUrl}, #{targetId}, 1, #{ea}, #{title}, #{bgApath}, #{bgThumbnailApath}, #{apath}, #{thumbnailApath}, #{qrStyle}, #{createBy}, now(), now(), #{type}, #{posterStyle}, #{isMobileDisplay}, #{userAddSettings}, #{failedOperation})")
    int insert(QRPosterEntity qrPosterEntity);

    @Update("<script> " +
            "update qr_poster" +
            "        <set>\n" +
            "            <if test=\"marketingEventId != null\">\n" +
            "                \"marketing_event_id\" = #{marketingEventId},\n" +
            "            </if>\n" +
            "            <if test=\"forwardType != null\">\n" +
            "                \"forward_type\" = #{forwardType},\n" +
            "            </if>\n" +
            "            <if test=\"qrCodeId != null\">\n" +
            "                \"qr_code_id\" = #{qrCodeId},\n" +
            "            </if>\n" +
            "            <if test=\"externalQrUrl != null\">\n" +
            "                \"external_qr_url\" = #{externalQrUrl},\n" +
            "            </if>\n" +
            "            <if test=\"targetId != null\">\n" +
            "                \"target_id\" = #{targetId},\n" +
            "            </if>\n" +
            "            <if test=\"title != null\">\n" +
            "                \"title\" = #{title},\n" +
            "            </if>\n" +
            "            <if test=\"bgApath != null\">\n" +
            "                \"bg_apath\" = #{bgApath},\n" +
            "            </if>\n" +
            "            <if test=\"bgThumbnailApath != null\">\n" +
            "                \"bg_thumbnail_apath\" = #{bgThumbnailApath},\n" +
            "            </if>\n" +
            "            <if test=\"apath != null\">\n" +
            "                \"apath\" = #{apath},\n" +
            "            </if>\n" +
            "            <if test=\"thumbnailApath != null\">\n" +
            "                \"thumbnail_apath\" = #{thumbnailApath},\n" +
            "            </if>\n" +
            "            <if test=\"qrStyle != null\">\n" +
            "                \"qr_style\" = #{qrStyle},\n" +
            "            </if>\n" +
            "            <if test=\"type != null\">\n" +
            "                \"type\" = #{type},\n" +
            "            </if>\n" +
            "            <if test=\"posterStyle != null\">\n" +
            "                \"poster_style\" = #{posterStyle},\n" +
            "            </if>\n" +
            "            <if test=\"isMobileDisplay != null\">\n" +
            "                \"is_mobile_display\" = #{isMobileDisplay},\n" +
            "            </if>\n" +
            "            <if test=\"userAddSettings != null\">\n" +
            "                \"user_add_settings\" = #{userAddSettings},\n" +
            "            </if>\n" +
            "            <if test=\"failedOperation != null\">\n" +
            "                \"failed_operation\" = #{failedOperation},\n" +
            "            </if>\n" +
            "            \"update_time\" = now()\n" +
            "        </set>" +
            " where id = #{id}"
            + "</script>")
    int update(QRPosterEntity qrPosterEntity);

    @Update("UPDATE qr_poster SET status = 2 where id = #{id}")
    void deleteById(@Param("id") String id);

    @Update("<script> "
            + "UPDATE qr_poster SET status = 2 where id IN\n"
            + "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +  "#{item}"
            + "</foreach>"
            + "</script>")
    void deleteByIdList(@Param("idList") List<String> idList);

    @Select("SELECT COALESCE(count(*), 0) FROM qr_poster WHERE ea=#{ea} AND marketing_event_id=#{marketingEventId} AND target_id=#{objectId} AND status = 1")
    int countByMarketingEventIdAndObjectId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectId") String objectId);

    @Select("<script> "
            + "select id, marketing_event_id as marketingEventId, target_id as targetId from qr_poster WHERE ea=#{ea} AND marketing_event_id=#{marketingEventId} and status=1 and target_id in\n"
            + "<foreach collection = 'objects' item = 'item' open = '(' separator = ',' close = ')'>"
            +  "#{item}"
            + "</foreach>"
            + "</script>")
   List<PosterTargetDTO> getByMarketingEvenIdAndTargetIds(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objects")List<String> objects);

    @Select("<script>"
            + "select count(*) from ("
            + "SELECT qr_poster.id FROM qr_poster WHERE ea = #{ea} AND status = 1 AND type = 1\n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 24)"
            + "UNION "
            + "SELECT qr_poster.id FROM qr_poster WHERE ea = #{ea} AND create_by = #{userId}  AND status = 1 AND type = 1"
            + " ) ct"
            + "</script>")
    int queryUnGroupAndCreateByMeCount(@Param("ea") String ea,@Param("userId") int userId);

    @Select("SELECT COUNT(*) FROM qr_poster WHERE ea = #{ea} AND create_by = #{userId}  AND status = 1 AND type = 1")
    int queryCountCreateByMe(@Param("ea")String ea, @Param("userId")Integer userId);

    @Select("<script>"
            + "SELECT count(*) FROM qr_poster WHERE ea= #{ea} AND status = 1 AND type = 1\n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 24)"
            + "</script>")
    int queryCountByUnGrouped(@Param("ea")String ea);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT qr_poster.id FROM qr_poster JOIN object_group_relation on  qr_poster.ea = object_group_relation.ea AND qr_poster.id = object_group_relation.object_id"
            + " WHERE qr_poster.ea = #{ea} AND qr_poster.status = 1 AND qr_poster.type = 1\n"
            + " AND object_group_relation.group_id IN\n"
            + "<foreach collection = 'groupIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "UNION"
            + " select qr_poster.id from qr_poster  where ea = #{ea} and status = 1 AND type = 1"
            + " and id not in (select object_id from object_group_relation where ea = #{ea} and object_type = 24)"
            + "UNION "
            + "SELECT qr_poster.id FROM qr_poster WHERE ea = #{ea} AND create_by = #{userId} AND type = 1  AND status = 1"
            + " ) hex"
            + "</script>")
    int queryAccessibleCount(@Param("ea") String ea, @Param("groupIdList") List<String> groupIdList, @Param("userId") int userId);

    @Select(
            "<script>"
                    + "select qr_poster.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qr_poster\n"
                    + "left join object_group_relation on qr_poster.id = object_group_relation.object_id and qr_poster.ea = object_group_relation.ea\n"
                    + "left join object_top on qr_poster.id = object_top.object_id and object_top.ea = qr_poster.ea\n"
                    + "where qr_poster.ea = #{queryParam.ea} and status = 1 \n"
                    + " <if test=\"queryParam.needCheckMobileDisplay != null and queryParam.needCheckMobileDisplay == true \">\n"
                    + "  AND qr_poster.is_mobile_display = true\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.title != null and queryParam.title !='' \">\n"
                    + "  AND qr_poster.title LIKE CONCAT('%',#{queryParam.title},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.marketingEventId != null and queryParam.marketingEventId !='' \">\n"
                    + "  AND qr_poster.marketing_event_id = #{queryParam.marketingEventId}\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.type != null\">\n"
                    + "  AND qr_poster.type = #{queryParam.type}\n"
                    + " </if>\n"
                    + "<if test=\"queryParam.groupIdList != null and queryParam.groupIdList.size != 0\">"
                    + " and object_group_relation.group_id in "
                    + "<foreach collection = 'queryParam.groupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    + "#{queryParam.groupIdList[${index}]}"
                    + "</foreach>"
                    + "</if>"
                    + " and ( "
                    + "qr_poster.create_by = #{queryParam.userId} \n"
                    + "or object_group_relation.group_id is null"
                    + "<if test=\"queryParam.permissionGroupIdList != null and queryParam.permissionGroupIdList.size != 0\">"
                    + "or object_group_relation.group_id in "
                    +     "<foreach collection = 'queryParam.permissionGroupIdList' index='index' item = 'item' open = '(' separator = ',' close = ')'>"
                    +     "#{queryParam.permissionGroupIdList[${index}]}"
                    +     "</foreach>"
                    + "</if>"
                    + ")"
                    + "order by object_top.create_time desc nulls last, qr_poster.create_time desc"
                    + "</script>"
    )
    List<QRPosterEntityDTO> getAccessiblePage(@Param("queryParam") QRPosterQueryParam param, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qr_poster.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qr_poster\n"
                    + "left join object_top on qr_poster.id = object_top.object_id and object_top.ea = qr_poster.ea\n"
                    + "where qr_poster.ea = #{queryParam.ea} and status = 1 \n"
                    + " <if test=\"queryParam.needCheckMobileDisplay != null and queryParam.needCheckMobileDisplay == true \">\n"
                    + "  AND qr_poster.is_mobile_display = true\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.title != null and queryParam.title !='' \">\n"
                    + "  AND qr_poster.title LIKE CONCAT('%',#{queryParam.title},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.marketingEventId != null and queryParam.marketingEventId !='' \">\n"
                    + "  AND qr_poster.marketing_event_id = #{queryParam.marketingEventId}\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.type != null\">\n"
                    + "  AND qr_poster.type = #{queryParam.type}\n"
                    + " </if>\n"
                    + " AND qr_poster.create_by = #{queryParam.userId}"
                    + " order by object_top.create_time desc nulls last, qr_poster.create_time desc"
                    + "</script>"
    )
    List<QRPosterEntityDTO> getCreateByMePage(@Param("queryParam") QRPosterQueryParam queryParam, @Param("page") Page page);

    @Select(
            "<script>"
                    + "select qr_poster.*,CASE WHEN object_top.id IS NULL THEN FALSE ELSE TRUE END AS top from qr_poster\n"
                    + "left join object_group_relation on qr_poster.id = object_group_relation.object_id and qr_poster.ea = object_group_relation.ea\n"
                    + "left join object_top on qr_poster.id = object_top.object_id and object_top.ea = qr_poster.ea\n"
                    + "where qr_poster.ea = #{queryParam.ea} and status = 1 \n"
                    + " <if test=\"queryParam.needCheckMobileDisplay != null and queryParam.needCheckMobileDisplay == true \">\n"
                    + "  AND qr_poster.is_mobile_display = true\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.title != null and queryParam.title !='' \">\n"
                    + "  AND qr_poster.title LIKE CONCAT('%',#{queryParam.title},'%')\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.marketingEventId != null and queryParam.marketingEventId !='' \">\n"
                    + "  AND qr_poster.marketing_event_id = #{queryParam.marketingEventId}\n"
                    + " </if>\n"
                    + " <if test=\"queryParam.type != null\">\n"
                    + "  AND qr_poster.type = #{queryParam.type}\n"
                    + " </if>\n"
                    + " AND ( object_group_relation.group_id is null )"
                    + "order by object_top.create_time desc nulls last, qr_poster.create_time desc"
                    + "</script>"
    )
    List<QRPosterEntityDTO> noGroupPage(@Param("queryParam") QRPosterQueryParam queryParam,@Param("page") Page page);

    @Update("UPDATE qr_poster SET status = 2 where ea = #{ea} and marketing_event_id = #{marketingEventId}")
    void updateQrPosterStatus(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("SELECT * FROM qr_poster WHERE ea=#{ea}")
    List<QRPosterEntity> getListByEa(@Param("ea")String ea);

    @Select("SELECT * FROM qr_poster WHERE ea=#{ea} AND apath=#{apath}")
    QRPosterEntity getByEaAndPath(@Param("ea")String ea, @Param("apath")String apath);

    @Select("<script> "
            + "SELECT * FROM qr_poster WHERE ea = #{ea} "
            + "  AND apath LIKE 'C_%' "
            + "</script>")
    List<QRPosterEntity> getCpathQrPoster(@Param("ea")String ea);
}
