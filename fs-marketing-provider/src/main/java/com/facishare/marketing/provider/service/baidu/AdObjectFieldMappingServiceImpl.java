package com.facishare.marketing.provider.service.baidu;

import com.facishare.marketing.api.result.baidu.AdObjectFieldMappingResult;
import com.facishare.marketing.api.service.baidu.AdObjectFieldMappingService;
import com.facishare.marketing.api.vo.baidu.UpdateAdObjectFieldMappingVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.entity.baidu.AdObjectFieldMappingEntity;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhengh on 2021/2/1.
 */
@Service("adObjectFieldMappingService")
@Slf4j
public class AdObjectFieldMappingServiceImpl implements AdObjectFieldMappingService {
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Override
    public Result<String> updateObjectFieldMapping(UpdateAdObjectFieldMappingVO vo){
        AdObjectFieldMappingEntity entity = adObjectFieldMappingDAO.getByApiName(vo.getEa(), vo.getCrmApiName());
        if (entity == null){
            entity = new AdObjectFieldMappingEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(vo.getEa());
            entity.setCreateBy(vo.getUserId());
            entity.setCrmApiName(vo.getCrmApiName());
            entity.setCrmDataMapping(vo.getCrmDataMapping());
            entity.setCrmRecordType(vo.getCrmRecordType());
            entity.setEnable(0);
            adObjectFieldMappingDAO.insert(entity);
        }else {
            entity.setCreateBy(vo.getUserId());
            entity.setCrmDataMapping(vo.getCrmDataMapping());
            adObjectFieldMappingDAO.updateByApiName(vo.getEa(), vo.getCrmApiName(), vo.getCrmDataMapping(), vo.getUserId());
        }
        return Result.newSuccess(entity.getId());
    }

    @Override
    public Result<AdObjectFieldMappingResult> queryAdObjectFieldMapping(String ea, String crmApiName) {
        AdObjectFieldMappingEntity entity = adObjectFieldMappingDAO.getByApiName(ea, crmApiName);
        if (entity == null){
            return Result.newSuccess();
        }

        AdObjectFieldMappingResult result = new AdObjectFieldMappingResult();
        result.setCrmDataMapping(entity.getCrmDataMapping());
        result.setCrmApiName(entity.getCrmApiName());
        result.setCrmRecordType(entity.getCrmRecordType());
        result.setEnable(entity.getEnable());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateMarketingEvenSyncStatus(String ea, String crmApiName, Integer enable) {
        AdObjectFieldMappingEntity entity = adObjectFieldMappingDAO.getByApiName(ea,crmApiName);
        if (entity == null){
            return Result.newError(SHErrorCode.UTM_SYNC_STATUS_DENY_WITHOUT_MAPPING);
        }

        adObjectFieldMappingDAO.updateStatusById(entity.getId(), enable);
        return Result.newSuccess();
    }
}
