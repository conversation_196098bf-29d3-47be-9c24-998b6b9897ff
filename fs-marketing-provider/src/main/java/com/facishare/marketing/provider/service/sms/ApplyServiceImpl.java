/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.sms;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.FieldValueResult;
import com.facishare.marketing.api.arg.sms.*;
import com.facishare.marketing.api.result.sms.*;
import com.facishare.marketing.api.result.sms.mw.SignatureSettingResult;
import com.facishare.marketing.api.service.sms.ApplyService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.sms.ApplySignatureStatusEnum;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.OrderSourceEnum;
import com.facishare.marketing.common.enums.sms.SmsOrderStatusEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsSceneTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsTemplateSourceEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldValue;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.sms.OrderDAO;
import com.facishare.marketing.provider.dao.sms.QuotaDAO;
import com.facishare.marketing.provider.dao.sms.SmsTrialDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSignatureDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.entity.sms.OrderEntity;
import com.facishare.marketing.provider.entity.sms.QuotaEntity;
import com.facishare.marketing.provider.entity.sms.SmsTrialEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsSignatureEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.MiniProgramAuthManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.SmsSendRecordObjManager;
import com.facishare.marketing.provider.manager.sms.SmsTemplateManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsSettingManager;
import com.facishare.marketing.provider.remote.enterpriserelation.ChannelDomainManager;
import com.facishare.marketing.provider.remote.enterpriserelation.result.ChannelDomainResult;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.fxiaoke.wechat.miniprogramrest.service.MiniProgramAuthService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhengh on 2018/12/20.
 */
@Service(value = "applyService")
@Slf4j
public class ApplyServiceImpl implements ApplyService{
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private SmsSettingManager smsSettingManager;
    @Autowired
    private SmsTrialDao smsTrialDao;
    @Autowired
    private MwSmsSignatureDao signatureDao;
    @Autowired
    private QuotaDAO quotaDAO;
    @Autowired
    private OrderDAO orderDAO;
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private ShortUrlManager shortUrlManager;
    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;
    @Autowired
    private MiniProgramAuthService miniProgramAuthService;
    @Autowired
    private MiniProgramAuthManager miniProgramAuthManager;

    @Autowired
    private SmsSendRecordObjManager smsSendRecordObjManager;

    @Autowired
    private SmsTemplateManager smsTemplateManager;

    @Autowired
    private ChannelDomainManager channelDomainManager;

    @ReloadableProperty("sms.trial.quota")
    private int trialQuota;

    @ReloadableProperty("sms.trial.signatureId")
    private String trialSignatureId;

    @ReloadableProperty("sms.trial.template.json")
    private String smsTrialTemplateJson;
    @ReloadableProperty("sms.trial.template.json.en")
    private String smsTrialTemplateJsonEN;
    /**
     * 查询场景
     */
    @Override
    public Result<FieldValueResult> querySceneType(String ea, Integer fsUserId) {
        FieldValueResult fieldValueResult = new FieldValueResult();
        List<FieldValue> fieldValues = new LinkedList<>();
        List<FieldValue> noticeFieldValues = new LinkedList<>();
        List<FieldValue> marketingFieldValues = new LinkedList<>();
        List<FieldValue> excelFieldValues = new LinkedList<>();
        for (SmsSceneTypeEnum smsSceneTypeEnum : SmsSceneTypeEnum.values()) {
            FieldValue fieldValue = new FieldValue(smsSceneTypeEnum.getName(), String.valueOf(smsSceneTypeEnum.getType()));
            fieldValues.add(fieldValue);
            if(fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.GENERAL.getType())) || fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.CONFERENCE_ENROLL.getType()))
                    || fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.LIVE_ENROLL.getType()))){
                noticeFieldValues.add(fieldValue);
            }
            if(fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.GENERAL_MARKETING.getType())) || fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.CONFERENCE_INVITE.getType()))
                    || fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.LIVE_INVITE.getType()))){
                marketingFieldValues.add(fieldValue);
            }
            if(fieldValue.getValue().equals(String.valueOf(SmsSceneTypeEnum.EXCEL.getType()))){
                excelFieldValues.add(fieldValue);
            }
        }
        fieldValueResult.setFieldValues(fieldValues);
        fieldValueResult.setNoticeFieldValues(noticeFieldValues);
        fieldValueResult.setMarketingFieldValues(marketingFieldValues);
        fieldValueResult.setExcelFieldValues(excelFieldValues);
        return Result.newSuccess(fieldValueResult);
    }

    /**
     * 申请短信签名
     */
    @Override
    public Result<SignatureSettingResult> applySignature(ApplySignatureArg arg) {
        String userName = fsAddressBookManager.getEmployeeInfo(arg.getEa(), arg.getUserId()) == null ? "" : fsAddressBookManager.getEmployeeInfo(arg.getEa(), arg.getUserId()).getFullName();
        Result<SignatureSettingResult> resultResult = smsSettingManager.signatureSetting(arg, userName);
//        try {
//            smsSendRecordObjManager.getOrCreateObjDescribe(arg.getEa());
//        } catch (Exception e) {
//            log.error("创建短信发送记录对象错误,ea:[{}]", arg.getEa(), e);
//        }
        return resultResult;
    }

    /**
     * 申请短信模板
     */
    @Override
    public Result<QueryTemplateResult> applyTemplate(ApplyTemplateVO arg) {
        if (arg == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isEmpty(arg.getName()) || StringUtils.isEmpty(arg.getContent())) {
            log.info("ApplyServiceImpl applyTemplate failed, name or content is null, arg:{}", arg);
            return Result.newError(SHErrorCode.APPLY_TEMPLATE_NULL_FAILED);
        }
        shortUrlManager.setShortUrlConfig(arg.getShortUrlMap());
        String userName;
        if (arg.getUserId() == -10000) {
            userName = "系统";
        } else {
            userName = fsAddressBookManager.getEmployeeInfo(arg.getEa(), arg.getUserId()) == null ? "" : fsAddressBookManager.getEmployeeInfo(arg.getEa(), arg.getUserId()).getFullName();
        }
        // 模板来源定义为用户主动创建
        arg.setSource(SmsTemplateSourceEnum.USER.getType());
        MwSmsTemplateEntity templateEntity = smsSettingManager.templateSetting(arg, userName, true, true);
        if (templateEntity != null){
            QueryTemplateResult result = BeanUtil.copy(templateEntity, QueryTemplateResult.class);
            return Result.newSuccess(result);
        } else {
            return Result.newError(SHErrorCode.APPLY_TEMPLATE_FAILED);
        }
    }

    /**
     * 删除签名
     * @param ea          公司ea
     * @param signatureId 短信签名的id
     */
    @Override
    public Result<Void> deleteSignature(String ea, String signatureId) {
        return smsSettingManager.deleteSignature(ea, signatureId);
    }

    /**
     * 修改签名
     */
    @Override
    public Result<Void> modifySignature(ModifySignatureArg arg) {
        return smsSettingManager.modifySignature(arg.getEa(), arg.getSignatureId(), arg.getTapath(), arg.getSignature(), arg.getRemark());
    }

    /**
     * 删除短信模板
     */
    @Override
    public Result<Void> deleteTemplate(DeleteTemplateVO arg) {
        return smsSettingManager.deleteTemplate(arg);

    }

    /**
     * 移动模板
     */
    @Override
    public Result<Void> moveTemplate(MoveTemplateArg arg) {
        return smsSettingManager.moveTemplate(arg);
    }

    /**
     * 拉取模板列表
     */
    @Override
    public Result<PageResult<QueryTemplateResult>> queryTemplate(QueryTemplateVO arg) {
        return smsSettingManager.queryTemplateList(arg);
    }

    /**
     * 获取签名内容
     */
    @Override
    public Result<QuerySignatureResult> querySignature(QuerySignatureArg arg) {
        return smsSettingManager.querySignature(arg);
    }

    @Override
    public Result<Void> applyTrial(String ea, Integer userId) {
        SmsTrialEntity trialEntity = smsTrialDao.querySmsTrial(ea);
        if (trialEntity != null) {
            log.info("ApplyServiceImpl applyTrial exist, ea={}, userId={}", ea, userId);
            return Result.newSuccess();
        }

        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureByEa(ea);
        if (signatureEntity != null && (signatureEntity.getStatus().equals(ApplySignatureStatusEnum.APPLY_PASS.getStatus()) || signatureEntity.getStatus().equals(ApplySignatureStatusEnum.APPLYING.getStatus()))) {
            return Result.newError(SHErrorCode.SMS_TRIAL_APPLY_FAIL);
        }

        trialEntity = new SmsTrialEntity();
        trialEntity.setId(UUIDUtil.getUUID());
        trialEntity.setEa(ea);
        trialEntity.setCreator(userId);
        trialEntity.setStatus(0);
        String userName = fsAddressBookManager.getEmployeeInfo(ea, userId) == null ? "" : fsAddressBookManager.getEmployeeInfo(ea, userId).getFullName();
        trialEntity.setCreatorName(userName);
        boolean trialSuccess = smsTrialDao.addSmsTrial(trialEntity);
        if(trialSuccess) {
            QuotaEntity quotaEntity = quotaDAO.queryEntityByEa(ea);
            if (quotaEntity != null) {
                quotaDAO.updateQuotaPurchaseCount(ea, trialQuota);
            } else {
                quotaEntity = new QuotaEntity();
                quotaEntity.setId(UUIDUtil.getUUID());
                quotaEntity.setEa(ea);
                quotaEntity.setTotal(trialQuota);
                quotaEntity.setLeft(trialQuota);
                quotaDAO.insertQuota(quotaEntity);
            }
            OrderEntity orderEntity = new OrderEntity();
            orderEntity.setId(UUIDUtil.getUUID());
            orderEntity.setEa(ea);
            orderEntity.setCopies(1);
            orderEntity.setCreator(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
            orderEntity.setOrderPackage(I18nUtil.get(I18nKeyEnum.MARK_SMS_APPLYSERVICEIMPL_255));
            orderEntity.setOrderSource(OrderSourceEnum.SMS_TRIAL.getType());
            orderEntity.setPaymentCount(0);
            orderEntity.setPrice(0);
            orderEntity.setPurchaseCount(trialQuota);
            orderEntity.setStatus(SmsOrderStatusEnum.PAYMENT.getStatus());
            orderEntity.setUserId(userId);
            orderDAO.addOrder(orderEntity);

            String finalSmsTrialTemplateJson = I18nUtil.getSuitedLangText(smsTrialTemplateJson, smsTrialTemplateJsonEN);
            List<Map<String, String>> templateList = GsonUtil.fromJson(finalSmsTrialTemplateJson, ArrayList.class);
            for (Map<String, String> template : templateList) {
                ApplyTemplateVO applyTemplateVO = new ApplyTemplateVO();
                applyTemplateVO.setEa(ea);
                applyTemplateVO.setRemark(template.get("name"));  //现在没有remark
                applyTemplateVO.setContent(template.get("content"));
                applyTemplateVO.setUserId(userId);
                applyTemplateVO.setName(template.get("name"));
                applyTemplateVO.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
                smsSettingManager.templateSetting(applyTemplateVO, userName, true, true);
            }
        } else {
            log.info("ApplyServiceImpl applyTrial failed, ea={}, userId={}", ea, userId);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<QueryTrialResult> queryTrial(String ea, Integer userId) {
        QueryTrialResult result = new QueryTrialResult();
        SmsTrialEntity trialEntity = smsTrialDao.querySmsTrial(ea);
        if (trialEntity == null) {
            log.info("ApplyServiceImpl applyTrial is not exist, ea={}, userId={}", ea, userId);
            return Result.newSuccess(result);
        }
        result.setId(trialEntity.getId());
        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(trialSignatureId);
        if (signatureEntity == null || !signatureEntity.getStatus().equals(ApplySignatureStatusEnum.APPLY_PASS.getStatus())) {
            return Result.newError(SHErrorCode.SMS_SIGNATURE_STATUS_ERROR);
        }
        result.setSignature(signatureEntity.getSvrName());
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<SmsChannelTypeResult>> listChannelType(String ea) {
        List<SmsChannelTypeResult> results = Lists.newArrayList();
        for (ChannelTypeEnum value : ChannelTypeEnum.values()) {
            if (value.isShow()) {
                SmsChannelTypeResult result = new SmsChannelTypeResult();
                result.setChannelType(value.getType());
                result.setChannelName(value.getName());
                results.add(result);
            }
        }
        return Result.newSuccess(results);
    }

    @Override
    public Result<PageResult<QueryTemplateResult>> listSmsTemplate(QueryTemplateVO arg) {
        return smsSettingManager.listSmsTemplate(arg);
    }

    @Override
    public Result<CheckSignatureResult> checkSmsStatus(QuerySignatureArg arg) {
        try {
            String ea = eIEAConverter.enterpriseIdToAccount(arg.getTenantId());
            MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureByEa(ea);
            CheckSignatureResult result = new CheckSignatureResult();
            if (signatureEntity == null || signatureEntity.getStatus().equals(ApplySignatureStatusEnum.DELETE.getStatus())) {
                result.setStatus(ApplySignatureStatusEnum.APPLY_NONE.getStatus());
            } else {
                result.setStatus(signatureEntity.getStatus());
                result.setSignature(signatureEntity.getSvrName());
            }
            return Result.newSuccess(result);
        } catch (Exception e) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<QueryTemplateResult> getTemplateDetail(DeleteTemplateVO arg) {
        Preconditions.checkArgument(arg != null && StringUtils.isNotEmpty(arg.getTemplateId()));
        arg.setTemplateId(smsTemplateManager.objIdConvertToDbId(arg.getEa(), arg.getTemplateId()));
        QueryTemplateResult result = new QueryTemplateResult();
        MwSmsTemplateEntity entity = mwSmsTemplateDao.getVisibleTemplateById(arg.getTemplateId());
        if (entity != null) {
            BeanUtils.copyProperties(entity, result);
            result.setShortUrlMap(StringUtils.isEmpty(entity.getContent()) ? new HashMap<>() : shortUrlManager.getShortUrl2LongUrlMap(entity.getContent()));
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<ListMiniProgramsResult>> listMiniPrograms(ListMiniProgramsVO vo) {
        return miniProgramAuthManager.listMiniPrograms(vo.getEa());
    }

    @Override
    public Result<String> generateScheme(GenerateSchemeVO vo) {
        return miniProgramAuthManager.generateScheme(vo.getWxAppId(), vo.getUpstreamEa());
    }

    @Override
    public Result<List<QueryChannelDomainResult>> queryChannelDomain(QueryChannelDomainArg arg) {
        List<ChannelDomainResult> channelDomainResults = channelDomainManager.listCustomDomainConfigs(arg.getEa());
        List<QueryChannelDomainResult> collect = channelDomainResults.stream().map(channelDomainResult -> BeanUtil.copy(channelDomainResult, QueryChannelDomainResult.class)).collect(Collectors.toList());
        return Result.newSuccess(collect);
    }
}