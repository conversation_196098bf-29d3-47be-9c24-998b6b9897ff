package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.ticket.CheckWxTicketStatusArg;
import com.facishare.marketing.api.arg.ticket.ConsumeWxTicketArg;
import com.facishare.marketing.api.arg.ticket.CreateWxQrCodeArg;
import com.facishare.marketing.api.arg.ticket.DetermineJumpTypeArg;
import com.facishare.marketing.api.arg.ticket.EditMeetingTicketArg;
import com.facishare.marketing.api.arg.ticket.QueryMeetingTicketArg;
import com.facishare.marketing.api.arg.ticket.UpdateMeetingTicketStatusArg;
import com.facishare.marketing.api.result.ticket.CheckWxTicketStatusResult;
import com.facishare.marketing.api.result.ticket.ConsumeTicketResult;
import com.facishare.marketing.api.result.ticket.CreateMeetingTicketResult;
import com.facishare.marketing.api.result.ticket.CreateWxQrCodeResult;
import com.facishare.marketing.api.result.ticket.DetermineJumpTypeResult;
import com.facishare.marketing.api.result.ticket.QueryMeetingTicketResult;
import com.facishare.marketing.api.service.WxTicketService;
import com.facishare.marketing.common.contstant.ParamQrCodeSceneTypeConstants;
import com.facishare.marketing.common.enums.ActivitySignOrEnrollEnum;
import com.facishare.marketing.common.enums.ActivityStatusEnum;
import com.facishare.marketing.common.enums.ticket.TicketTypeEnum;
import com.facishare.marketing.common.enums.ticket.WxTicketCardStatusEnum;
import com.facishare.marketing.common.enums.ticket.WxTicketCheckStatusEnum;
import com.facishare.marketing.common.enums.ticket.WxTicketStatus;
import com.facishare.marketing.common.enums.ticket.WxTicketTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.MeetingTicketData;
import com.facishare.marketing.common.typehandlers.value.WxTicketContent;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.ActivityEnrollDataDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.ticket.WxTicketDAO;
import com.facishare.marketing.provider.dao.ticket.WxTicketReceiveDAO;
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.ticket.WxTicketEntity;
import com.facishare.marketing.provider.entity.ticket.WxTicketReceiveEntity;
import com.facishare.marketing.provider.manager.ActivityManager;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.WxTicketManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.constants.PermissionType;
import com.facishare.wechat.proxy.constants.QrActionType;
import com.facishare.wechat.proxy.model.arg.ConsumeCardCodeArg;
import com.facishare.wechat.proxy.model.arg.CreateMeetingTicketArg;
import com.facishare.wechat.proxy.model.arg.QueryCardCodeArg;
import com.facishare.wechat.proxy.model.result.ConsumeCardCodeResult;
import com.facishare.wechat.proxy.model.result.CreateParamQrCodeResult;
import com.facishare.wechat.proxy.model.result.QueryCardCodeResult;
import com.facishare.wechat.proxy.model.vo.CreateParamQrCodeVO;
import com.facishare.wechat.proxy.service.CardService;
import com.facishare.wechat.proxy.service.QrCodeService;
import com.facishare.wechat.proxy.service.WechatAuthService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/10/28
 **/
@Service("wxTicketService")
@Slf4j
public class WxTicketServiceImpl implements WxTicketService {

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private WxTicketDAO wxTicketDAO;

    @Autowired
    private WxTicketManager wxTicketManager;

    @Autowired
    private QrCodeService qrCodeService;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private CardService wxCardService;

    @Autowired
    private WechatAuthService wechatAuthService;

    @Autowired
    private WxTicketReceiveDAO wxTicketReceiveDAO;

    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Override
    public Result<CreateMeetingTicketResult> createMeetingTicket(EditMeetingTicketArg arg) {
        WxTicketEntity wxTicketEntity = wxTicketDAO.getWxTicketByEaTicketAssociationId(arg.getEa(), WxTicketTypeEnum.MEETING_TICKET.getType(), arg.getAssociationId());
        if (wxTicketEntity != null) {
            log.warn("WxTicketServiceImpl.createMeetingTicket wxTicketEntity is not null arg:{}", arg);
            return new Result<>(SHErrorCode.WX_MEETING_TICKET_FOUND);
        }
        ModelResult<Boolean> haveCardFunctions = wxCardService.checkHaveCardFunctions(arg.getEa(), arg.getWxAppId());
        if (haveCardFunctions == null || !haveCardFunctions.isSuccess()) {
            log.warn("WxTicketServiceImpl.createMeetingTicket checkHaveCardFunctions is not success arg:{}, haveCardFunctions:{},", arg, haveCardFunctions);
            return new Result<>(SHErrorCode.WX_TICKET_CHECK_FUNCTIONS_FAIL);
        }
        if (!haveCardFunctions.getResult()) {
            return new Result<>(SHErrorCode.WX_TICKET_CHECK_NO_FUNCTIONS);
        }
        String logoApath;
        String logoUrl;
        try {
            FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(arg.getTicketDetail().getLogoTApath(), null, null);
            logoApath = fileManagerPicResult.getUrlAPath();
            logoUrl = fileManagerPicResult.getUrl();
        } catch (Exception e) {
            log.info("WxTicketServiceImpl createMeetingTicket fileV2Manager.getApathByTApath failed, arg:{}", arg);
            return Result.newError(SHErrorCode.WX_MEETING_TICKET_LOGO_SAVE_FAIL);
        }
        if (StringUtils.isBlank(logoApath) || StringUtils.isBlank(logoUrl)) {
            log.info("WxTicketServiceImpl createMeetingTicket logoApath or logoUrl is blank, logoApath:{}, logoUrl:{}, arg:{}", logoApath, logoUrl, arg);
            return Result.newError(SHErrorCode.WX_MEETING_TICKET_LOGO_SAVE_FAIL);
        }
        // pJpVPxPFEo1KJ-2_iJDbaMi2jk98
        String cardId = createWxTicket(arg, logoUrl); //调用微信卡券接口
        if (StringUtils.isBlank(cardId)) {
            log.info("WxTicketServiceImpl createMeetingTicket createWxTicket cardId is blank, arg:{}", arg);
            return Result.newError(SHErrorCode.WX_TICKET_CREATE_FAIL);
        }

        wxTicketEntity = new WxTicketEntity();
        wxTicketEntity.setId(UUIDUtil.getUUID());
        wxTicketEntity.setEa(arg.getEa());
        wxTicketEntity.setTicketType(WxTicketTypeEnum.MEETING_TICKET.getType());
        wxTicketEntity.setAssociationId(arg.getAssociationId());
        wxTicketEntity.setCardId(cardId);
        wxTicketEntity.setWxAppId(arg.getWxAppId());
        wxTicketEntity.setCreateBy(arg.getFsUserId());
        wxTicketEntity.setUpdateBy(arg.getFsUserId());

        WxTicketContent ticketContent = new WxTicketContent();
        MeetingTicketData meetingTicketData = BeanUtil.copy(arg.getTicketDetail(), MeetingTicketData.class);
        meetingTicketData.setLogoApath(logoApath);
        ticketContent.setMeetingTicketData(meetingTicketData);
        wxTicketEntity.setTicketContent(ticketContent);
        wxTicketEntity.setStatus(WxTicketStatus.ENABLED.getStatus());
        boolean insertResult = wxTicketDAO.insertWxTicket(wxTicketEntity);
        if (!insertResult) {
            return Result.newError(SHErrorCode.WX_MEETING_TICKET_SAVE_TO_DB_FAIL);
        }
        CreateMeetingTicketResult ticketResult = new CreateMeetingTicketResult();
        ticketResult.setId(wxTicketEntity.getId());
        return Result.newSuccess(ticketResult);
    }

    private String createWxTicket(EditMeetingTicketArg arg, String logoUrl) {
        String cardId = null;
        CreateMeetingTicketArg wxMeetingTicketArg = new CreateMeetingTicketArg();
        CreateMeetingTicketArg.Card card = new CreateMeetingTicketArg.Card();
        wxMeetingTicketArg.setCard(card);
        card.setCardType(WxTicketTypeEnum.MEETING_TICKET.getWxTypeName());
        CreateMeetingTicketArg.MeetingTicket meetingTicket = new CreateMeetingTicketArg.MeetingTicket();
        card.setMeetingTicket(meetingTicket);
        meetingTicket.setMeetingDetail(arg.getTicketDetail().getMeetingDetail());
        CreateMeetingTicketArg.BaseInfo baseInfo = new CreateMeetingTicketArg.BaseInfo();
        meetingTicket.setBaseInfo(baseInfo);
        baseInfo.setLogoUrl(logoUrl);
        baseInfo.setBrandName(arg.getTicketDetail().getBrandName());
        baseInfo.setCodeType("CODE_TYPE_QRCODE");
        baseInfo.setTitle(arg.getTicketDetail().getTitle());
        baseInfo.setColor(arg.getTicketDetail().getColor());
        baseInfo.setNotice(arg.getTicketDetail().getNotice());
        baseInfo.setDescription(arg.getTicketDetail().getDescription());
        CreateMeetingTicketArg.Sku sku = new CreateMeetingTicketArg.Sku();
        sku.setQuantity(100000000);
        baseInfo.setSku(sku);
        CreateMeetingTicketArg.DateInfo dateInfo = new CreateMeetingTicketArg.DateInfo();
        dateInfo.setType(1);
        dateInfo.setBeginTimestamp(arg.getTicketDetail().getBeginTimestamp());
        dateInfo.setEndTimestamp(arg.getTicketDetail().getEndTimestamp());
        baseInfo.setDateInfo(dateInfo);
        if (StringUtils.isNotBlank(arg.getTicketDetail().getServicePhone())) {
            baseInfo.setServicePhone(arg.getTicketDetail().getServicePhone());
        }
        baseInfo.setCustomUrlName(arg.getTicketDetail().getCustomUrlName());
        baseInfo.setCustomUrl(arg.getTicketDetail().getCustomUrl());
        baseInfo.setCustomUrlSubTitle(arg.getTicketDetail().getCustomUrlSubTitle());
        baseInfo.setGetLimit(1);
        baseInfo.setCanGiveFriend(false);
        baseInfo.setCanShare(false);

        ModelResult<String> result = wxCardService.createMeetingCard(arg.getEa(), arg.getWxAppId(), wxMeetingTicketArg);
        if (result.isSuccess()) {
            cardId = result.getResult();
        }

        return cardId;
    }

    @Override
    public Result<QueryMeetingTicketResult> queryMeetingTicketDetail(QueryMeetingTicketArg arg) {
        QueryMeetingTicketResult meetingTicketResult = new QueryMeetingTicketResult();
        WxTicketEntity wxTicketEntity = wxTicketDAO.getWxTicketByEaTicketAssociationId(arg.getEa(), WxTicketTypeEnum.MEETING_TICKET.getType(), arg.getAssociationId());
        if (wxTicketEntity == null) {
            meetingTicketResult.setStatus(WxTicketStatus.UN_OPEN.getStatus());
            return Result.newSuccess(meetingTicketResult);
        }
        meetingTicketResult = BeanUtil.copy(wxTicketEntity.getTicketContent().getMeetingTicketData(), QueryMeetingTicketResult.class);
        meetingTicketResult.setId(wxTicketEntity.getId());
        String logoUrl = fileV2Manager.getUrlByPath(wxTicketEntity.getTicketContent().getMeetingTicketData().getLogoApath(), arg.getEa(), false);
        meetingTicketResult.setLogoUrl(logoUrl);
        meetingTicketResult.setStatus(wxTicketEntity.getStatus());
        return Result.newSuccess(meetingTicketResult);
    }

    @Override
    public Result<DetermineJumpTypeResult> determineJumpType(DetermineJumpTypeArg arg) {
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getFormDataUserId());
        if (customizeFormDataUserEntity == null) {
            log.warn("WxTicketServiceImpl.DetermineJumpType customizeFormDataUserEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId());
        if (customizeFormDataEntity == null) {
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        String activityId = activityManager.getActivityIdByObject(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getMarketingEventId());
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        if (activityEntity == null) {
            log.warn("WxTicketServiceImpl.DetermineJumpType activityEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        WxTicketEntity wxTicketEntity = wxTicketDAO.getWxTicketByEaTicketAssociationId(activityEntity.getEa(), WxTicketTypeEnum.MEETING_TICKET.getType(), activityId);
        DetermineJumpTypeResult determineJumpTypeResult = new DetermineJumpTypeResult();
        determineJumpTypeResult.setJumpType(TicketTypeEnum.CUSTOMIZE_TICKET.getType());
        if (wxTicketEntity != null) {
            // 若存在微信门票跳转微信门票
            determineJumpTypeResult.setJumpType(TicketTypeEnum.WX_TICKET.getType());
        }
        return new Result<>(SHErrorCode.SUCCESS, determineJumpTypeResult);
    }

    @Override
    public Result<CreateWxQrCodeResult> createWxQrCode(CreateWxQrCodeArg arg) {
        CreateWxQrCodeResult createWxQrCodeResult = new CreateWxQrCodeResult();
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getCustomizeFormDataUserById(arg.getFormDataUserId());
        if (customizeFormDataUserEntity == null) {
            log.warn("WxTicketServiceImpl.createWxQrCode customizeFormDataUserEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId());
        if (customizeFormDataEntity == null) {
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        String activityId = activityManager.getActivityIdByObject(customizeFormDataUserEntity.getObjectId(), customizeFormDataUserEntity.getObjectType(), customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getMarketingEventId());
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        if (activityEntity == null) {
            log.warn("WxTicketServiceImpl.createWxQrCode activityEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        WxTicketEntity wxTicketEntity = wxTicketDAO.getWxTicketByEaTicketAssociationId(activityEntity.getEa(), WxTicketTypeEnum.MEETING_TICKET.getType(), activityId);
        CreateParamQrCodeVO createParamQrCodeVO = new CreateParamQrCodeVO();
        createParamQrCodeVO.setWxAppId(wxTicketEntity.getWxAppId());
        createParamQrCodeVO.setEa(wxTicketEntity.getEa());
        Integer fsUserId = wxTicketManager.getEnterpriseAdminInfo(wxTicketEntity.getEa());
        createParamQrCodeVO.setFsUserId(fsUserId);
        createParamQrCodeVO.setActionName(QrActionType.QR_LIMIT_STR_SCENE.name());
        createParamQrCodeVO.setExpireSeconds(WxTicketManager.DEFAULT_QR_CODE_EXPIRE_TIME);
        createParamQrCodeVO.setSceneType(ParamQrCodeSceneTypeConstants.WX_QR_CODE_SCENE_TYPE);
        Map<String, Object> param = Maps.newHashMap();
        param.put("formDataUserId", arg.getFormDataUserId());
        param.put("cardId", wxTicketEntity.getCardId());
        param.put("associationId", activityId);
        // 判断会议是否开启审核
        if (activityEntity.getEnrollReview() == null || activityEntity.getEnrollReview()) {
            param.put("enrollReview", true);
            createWxQrCodeResult.setEnrollReview(true);
        } else {
            param.put("enrollReview", false);
            createWxQrCodeResult.setEnrollReview(false);
        }
        createParamQrCodeVO.setParams(param);
        ModelResult<CreateParamQrCodeResult> qrCodeResultModelResult = qrCodeService.createParamQrCode(createParamQrCodeVO);
        if (!qrCodeResultModelResult.isSuccess() || StringUtils.isBlank(qrCodeResultModelResult.getResult().getTicketUrl())) {
            log.warn("WxTicketServiceImpl.createWxQrCode qrCodeResultModelResult not success createParamQrCodeVO:{}", createParamQrCodeVO);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        createWxQrCodeResult.setQrUrl(qrCodeResultModelResult.getResult().getTicketUrl());
        return new Result<>(SHErrorCode.SUCCESS, createWxQrCodeResult);
    }

    @Override
    public Result<CheckWxTicketStatusResult> checkWxTicketStatus(CheckWxTicketStatusArg arg) {
        CheckWxTicketStatusResult checkResult = new CheckWxTicketStatusResult();
        //检查是否已经授权了卡券功能
        ModelResult<List<Integer>> listResult = wechatAuthService.listAuthorizedPermissions(arg.getEa(), arg.getWxAppId());
        if (!listResult.isSuccess()) {
            log.warn("WxTicketServiceImpl checkWxTicketStatus listResult is not success, arg:{}, listResult:{}", arg, listResult);
            return Result.newError(SHErrorCode.WX_TICKET_STATUS_CHECK_FAIL);
        }
        if (CollectionUtils.isEmpty(listResult.getResult()) || !listResult.getResult().contains(PermissionType.PERMISSION_BONUS)) {
            checkResult.setCheckStatus(WxTicketCheckStatusEnum.NO_TICKET_AUTHORIZED.getStatus());
        } else {
            //检查是否已经开通了卡券功能
            ModelResult<Boolean> haveCardFunctions = wxCardService.checkHaveCardFunctions(arg.getEa(), arg.getWxAppId());
            if (haveCardFunctions == null || !haveCardFunctions.isSuccess()) {
                log.warn("WxTicketServiceImpl.checkWxTicketStatus checkHaveCardFunctions is not success arg:{}, haveCardFunctions:{},", arg, haveCardFunctions);
                return new Result<>(SHErrorCode.WX_TICKET_CHECK_FUNCTIONS_FAIL);
            }
            if (haveCardFunctions.getResult()) {
                checkResult.setCheckStatus(WxTicketCheckStatusEnum.GET_TICKET_FUNCTIONS.getStatus());
            } else {
                checkResult.setCheckStatus(WxTicketCheckStatusEnum.NO_TICKET_FUNCTIONS.getStatus());
            }
        }
        return Result.newSuccess(checkResult);
    }

    @Override
    public Result<ConsumeTicketResult> consumeWxTicket(ConsumeWxTicketArg arg) {
        // 查询会议卡券
        ConsumeTicketResult consumeTicketResult = new ConsumeTicketResult();
        WxTicketEntity wxTicketEntity = wxTicketDAO.getWxTicketByEaTicketAssociationId(arg.getEa(), arg.getTicketType(), arg.getAssociationId());
        if (wxTicketEntity == null) {
            log.warn("WxTicketServiceImpl.consumeWxTicket error wxTicketEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.WX_MEETING_TICKET_NOT_FOUND);
        }
        ActivityEntity activityEntity = activityDAO.getById(arg.getAssociationId());
        // 活动不存在
        if (activityEntity == null) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        // 活动未开始
        /*if (activityEntity.getStartTime().getTime() > new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_START_NOT_ALLOW_SIGN_IN);
        }*/

        // 活动已结束
        if (activityEntity.getEndTime().getTime() < new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_END);
        }

        // 活动停用
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
        }

        // 活动删除
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DELETED);
        }
        // 查询卡券状态
        QueryCardCodeArg queryCardCodeArg = new QueryCardCodeArg();
        queryCardCodeArg.setCardId(wxTicketEntity.getCardId());
        queryCardCodeArg.setCheckConsume(false);
        queryCardCodeArg.setCode(arg.getCode());
        ModelResult<QueryCardCodeResult> queryCardCodeResultModelResult = wxCardService.queryCardCode(wxTicketEntity.getEa(), wxTicketEntity.getWxAppId(), queryCardCodeArg);
        if (!queryCardCodeResultModelResult.isSuccess()) {
            log.warn("WxTicketServiceImpl.consumeWxTicket queryCardCode error arg:{}, queryCardCodeResultModelResult:{}", arg, queryCardCodeResultModelResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        QueryCardCodeResult queryCardCodeResult = queryCardCodeResultModelResult.getResult();
        WxTicketReceiveEntity wxTicketReceiveEntity = wxTicketReceiveDAO.getWxTicketReceiveByCardWxAppOpenId(wxTicketEntity.getCardId(), wxTicketEntity.getWxAppId(), queryCardCodeResult.getOpenid());
        if (wxTicketReceiveEntity == null) {
            log.warn("WxTicketServiceImpl.consumeWxTicket wxTicketReceiveEntity is null arg:{}, queryCardCodeResult:{}", arg, queryCardCodeResult);
            return new Result<>(SHErrorCode.WX_TICKET_RECEIVE_NOT_FOUND);
        }
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(wxTicketReceiveEntity.getFormDataUserId());
        if (campaignMergeDataEntity == null) {
            log.warn("WxTicketServiceImpl.consumeWxTicket campaignMergeDataEntity is null wxTicketReceiveEntity:{}", wxTicketReceiveEntity);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (!queryCardCodeResult.isCanConsume()) {
            log.info("WxTicketServiceImpl.consumeWxTicket cannot consume queryCardCodeResult:{}", queryCardCodeResult);
            WxTicketCardStatusEnum wxTicketCardStatusEnum = WxTicketCardStatusEnum.getStatusByDesc(queryCardCodeResult.getUserCardStatus());
            if (!wxTicketReceiveEntity.getTicketStatus().equals(WxTicketCardStatusEnum.CONSUMED.getType())) {
                // 不可核销且状态不为已核销修改状态
                wxTicketReceiveDAO.updateWxTicketReceiveStatus(wxTicketCardStatusEnum.getType(), wxTicketReceiveEntity.getId());
            }
            return Result.newError(wxTicketCardStatusEnum.getErrorCode());
        } else {
            // 核销卡券
            ConsumeCardCodeArg consumeCardCodeArg = new ConsumeCardCodeArg();
            consumeCardCodeArg.setCode(arg.getCode());
            consumeCardCodeArg.setCardId(wxTicketEntity.getCardId());
            ModelResult<ConsumeCardCodeResult> consumeCardCodeResult = wxCardService.consumeCardCode(wxTicketReceiveEntity.getEa(), wxTicketEntity.getWxAppId(), consumeCardCodeArg);
            if (!consumeCardCodeResult.isSuccess()) {
                log.warn("WxTicketServiceImpl.consumeWxTicket consumeCardCode error consumeCardCodeResult:{}", consumeCardCodeResult);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            // 修改状态
            wxTicketReceiveDAO.updateWxTicketReceiveStatus(WxTicketCardStatusEnum.CONSUMED.getType(), wxTicketReceiveEntity.getId());

            // 修改签到状态
            String formDataUserId = wxTicketReceiveEntity.getFormDataUserId();
            List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(formDataUserId));
            if(CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
                log.warn("WxTicketServiceImpl.consumeWxTicket activityEnrollDataEntityList is empty formDataUserId:{}", formDataUserId);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            List<String> needSetId = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList());
            // 若存在手机号设置则将该会议下所有手机号设为已签到
            if (StringUtils.isNotBlank(campaignMergeDataEntity.getPhone())) {
                List<String> phoneEnrollData = activityEnrollDataDAO.getEnrollDataByActivityIdAndPhone(arg.getAssociationId(), campaignMergeDataEntity.getPhone());
                needSetId.addAll(phoneEnrollData);
            }
            campaignMergeDataManager.updateSignInStatus(needSetId, ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
            consumeTicketResult.setName(campaignMergeDataEntity.getName());
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIdAndObject(formDataUserId, activityEnrollDataEntityList.get(0).getObjectId(), activityEnrollDataEntityList.get(0).getObjectType());
            if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
                consumeTicketResult.setCompanyName(customizeFormDataUserEntityList.get(0).getSubmitContent().getCompanyName());
                consumeTicketResult.setPosition(customizeFormDataUserEntityList.get(0).getSubmitContent().getPosition());
            }
            if (activityEnrollDataEntityList.get(0).getGroupUserId() != null) {
                List<String> groupName = conferenceManager.getGroupNameByIdsStr(activityEnrollDataEntityList.get(0).getGroupUserId());
                consumeTicketResult.setGroupName(groupName);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result updateMeetingTicketStatus(UpdateMeetingTicketStatusArg arg) {
        WxTicketEntity wxTicketEntity = wxTicketDAO.getWxTicketByEaTicketAssociationId(arg.getEa(), WxTicketTypeEnum.MEETING_TICKET.getType(), arg.getAssociationId());
        if (wxTicketEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        wxTicketDAO.updateWxTicketReceiveStatus(wxTicketEntity.getId(), arg.getStatus());
        return Result.newSuccess();
    }
}
