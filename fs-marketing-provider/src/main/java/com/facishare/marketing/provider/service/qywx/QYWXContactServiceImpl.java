package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.qywx.BindQywxQrCodeWithWebsiteArg;
import com.facishare.marketing.api.arg.qywx.wxContact.*;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult;
import com.facishare.marketing.api.result.qywx.customerGroup.DepartmentResult;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeOwnerResult;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeTagResult;
import com.facishare.marketing.api.result.qywx.wxContact.*;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.vo.QueryWebsiteBindFanQrCodeVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.api.vo.qywx.CreateOrUpdateWebSiteFanQrCodeVO;
import com.facishare.marketing.api.vo.qywx.QywxAttachmentsVO;
import com.facishare.marketing.api.vo.qywx.QywxCreateAddFanQrCodeVO;
import com.facishare.marketing.api.vo.qywx.UpdateQywxCustomerRemarkVO;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qywx.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.qywx.QywxAddFanQueryParam;
import com.facishare.marketing.provider.dao.qywx.QYWXContactFollowUpDAO;
import com.facishare.marketing.provider.dao.qywx.QywxContactMeConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendResultDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendTaskDAO;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.dto.QYWXContactFollowUpDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.qywx.QywxAddFanQrCodeDTO;
import com.facishare.marketing.provider.dto.qywx.TemplateBindQrConfigIdDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.qywx.*;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.qywx.ContactMeConfigArg;
import com.facishare.marketing.provider.innerArg.qywx.QywxEmployeeUpdateArg;
import com.facishare.marketing.provider.innerArg.qywx.UpdateContactMeConfigArg;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerData.qywx.QywxScheduleContainer;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.innerResult.qywx.*;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.statistic.outapi.arg.UserMarketingActionStatisticQueryArg;
import com.facishare.marketing.statistic.outapi.result.UserMarketingActionStatisticResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.MetadataTagDataService;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ;
import static com.facishare.marketing.provider.manager.AuthManager.defaultAllDepartment;

/**
 * Created  By zhoux 2020/02/06
 **/
@Slf4j
@Service("qywxContactService")
public class QYWXContactServiceImpl implements QYWXContactService {

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private MetadataTagManager metadataTagManager;

    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAssociationManager;

    @Autowired
    private QYWXContactFollowUpDAO qywxContactFollowUpDAO;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxContactMeConfigDAO qywxContactMeConfigDAO;

    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;

    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private QywxGroupSendTaskDAO qywxGroupSendTaskDAO;

    @Autowired
    private QywxGroupSendResultDAO qywxGroupSendResultDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private MetadataTagDataService metadataTagDataService;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private CustomerGroupManager customerGroupManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;
    @Autowired
    private QywxAddFanQrCodeRelationDAO qywxAddFanQrCodeRelationDAO;
    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;
    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private EmployeeProviderService employeeProviderService;

    @Autowired
    private QywxAddFanQrCodeUserMarketingRelationDAO qywxAddFanQrCodeUserMarketingRelationDAO;

    @Autowired
    private QywxAttachmentsRelationDAO qywxAttachmentsRelationDAO;
    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private QywxActivatedAccountManager qywxActivatedAccountManager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private FsBindManager fsBindManager;
    private Gson gson = new Gson();

    public static final String JIKE_QR_PRE = "JK_";

    @Override
    public Result<PageResult<QueryContactListResult>> queryContactList(QueryContactListArg arg) {
        // 获取accessToken
        PageResult<QueryContactListResult> result = new PageResult();
        List<QueryContactListResult> resultList = Lists.newArrayList();
        result.setTotalCount(0);
        result.setResult(Lists.newArrayList());
        result.setPageSize(arg.getPageSize());
        result.setPageNum(arg.getPageNum());
        result.setResult(resultList);
        // 从【企业微信客户】对象中获取数据
        List<Filter> filterList = Lists.newArrayList();
        List<ObjectData> objectDataList = Lists.newArrayList();
        if (StringUtils.isNotBlank(arg.getUid())) {
            // 小程序需要先查询企业微信客户
            QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getEa());
            if (agentConfig == null) {
                log.warn("QYWXContactServiceImpl.queryContactList error arg:{}", arg);
                return Result.newSuccess();
            }
            String accessToken = qywxManager.getAccessToken(arg.getEa());
            List<String> qywxExternalUserIds = qywxManager.getQyWxustomerCList(accessToken, arg.getQyUserId());
            // 默筛选条件
            qywxExternalUserIds.add("");
            Filter externalUserIdsFilter = new Filter();
            externalUserIdsFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName());
            externalUserIdsFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
            externalUserIdsFilter.setFieldValues(qywxExternalUserIds);
            filterList.add(externalUserIdsFilter);
        }
        if (StringUtils.isNotBlank(arg.getName())) {
            Filter wxNameFilter = new Filter();
            wxNameFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName());
            wxNameFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.LIKE);
            wxNameFilter.setFieldValues(Collections.singletonList(arg.getName()));
            filterList.add(wxNameFilter);
        }

        if (StringUtils.isNotEmpty(arg.getFanQrCodeId())){
            Filter wxFanQrCodeIdFilter = new Filter();
            wxFanQrCodeIdFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName());
            wxFanQrCodeIdFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.EQ);
            wxFanQrCodeIdFilter.setFieldValues(Collections.singletonList(arg.getFanQrCodeId()));
            filterList.add(wxFanQrCodeIdFilter);
            log.info("wxFanQrCodeIdFilter:{}", wxFanQrCodeIdFilter);
        }

        if (arg.getFilterData() == null) {
            arg.setFilterData(new FilterData());
        }
        if (arg.getFilterData().getQuery() == null) {
            arg.getFilterData().setQuery(new SearchTemplateQuery());
            arg.getFilterData().setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            arg.getFilterData().getQuery().setFilters(Lists.newArrayList());
        }
        Filter isDeleteFilter = new Filter();
        isDeleteFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.IS_DELETED.getFieldName());
        isDeleteFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
        isDeleteFilter.setFieldValues(Lists.newArrayList("false"));
        filterList.add(isDeleteFilter);

        Filter recordTypeFilter = new Filter();
        recordTypeFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.RECORD_TYPE.getFieldName());
        recordTypeFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.EQ);
        recordTypeFilter.setFieldValues(Lists.newArrayList("default__c"));
        filterList.add(recordTypeFilter);
        if (CollectionUtils.isNotEmpty(filterList)) {
            arg.getFilterData().getQuery().getFilters().addAll(filterList);
        }
        Integer ei = eieaConverter.enterpriseAccountToId(arg.getEa());
        List<String> tagIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(arg.getTagNames())) {
            Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(arg.getEa(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), arg.getTagNames());
            if (MapUtils.isNotEmpty(tagNamesIdMap)) {
                tagIds = tagNamesIdMap.values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
            } else {
                return Result.newSuccess(result);
            }
        }
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        ControllerListArg perCrmListArg = new ControllerListArg();
        FilterData filterData = arg.getFilterData();
        String tagOperator = "IN".equals(arg.getTagOperator()) ? "LIKE" : arg.getTagOperator();
        Iterator<Filter> iterator = filterData.getQuery().getFilters().iterator();
        Filter tempFilter;
        while(iterator.hasNext()){
            tempFilter = iterator.next();
            if("tag".equals(tempFilter.getFieldName())){
                tempFilter.setValueType(11);
                tempFilter.setFieldName("tag");
                tempFilter.setOperator("IN".equals(tempFilter.getOperator()) ? "LIKE" : tempFilter.getOperator());
                tempFilter.setFieldValues(tempFilter.getFieldValues());
                tempFilter.setValueType(11);
            }
        }

        perCrmListArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        perCrmListArg.setSearchTemplateId(filterData.getSearchTemplateId());
        perCrmListArg.setIncludeLayout(false);
        perCrmListArg.setIncludeDescribe(false);
        perCrmListArg.setIncludeButtonInfo(false);
        if (!Strings.isNullOrEmpty(tagOperator) && CollectionUtils.isNotEmpty(tagIds)){
            perCrmListArg.setTagOperator(tagOperator);
            perCrmListArg.setTags(tagIds);
        }
        SearchQuery searchQuery = BeanUtil.copyByFastJson(filterData.getQuery(), SearchQuery.class);
        searchQuery.addOrderBy(CrmWechatWorkExternalUserFieldEnum.WECHAT_WORK_CREATE_TIME.getFieldName(), false);
        searchQuery.setOffset((arg.getPageNum() - 1) * arg.getPageSize());
        searchQuery.setLimit(arg.getPageSize());
        perCrmListArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> listCrmObjectResult = metadataControllerService.list(headerObj, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), perCrmListArg);
        if (!listCrmObjectResult.isSuccess() || listCrmObjectResult.getData() == null) {
            log.warn("QYWXContactService.queryContactList tagPageMetadataTagResult error result :{}", result);
            return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(), listCrmObjectResult.getMessage());
        }
        objectDataList = listCrmObjectResult.getData().getDataList();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Result.newSuccess(result);
        }
        result.setTotalCount(listCrmObjectResult.getData().getTotal());
        // 批量查询标签
        Map<String, ObjectDataIdAndTagNameListData> tagMap = metadataTagManager
            .getObjectDataIdAndTagNameListDataMapByObjectDataIds(arg.getEa(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),
                objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList()));
        // 跟进记录
        Map<String, QYWXContactFollowUpDTO> qywxContactFollowUpDTOMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(arg.getUid())) {
            List<QYWXContactFollowUpDTO> qywxContactFollowUpDTOList = qywxContactFollowUpDAO
                .queryFollowerContactInfo(arg.getUid(), QywxContactFollowUpTypeEnum.MINI_APP_CONTACT_FOLLOW_UP.getValue(), QywxContactFollowUpActionTypeEnum.MINI_APP_ADD_FOLLOW_UP.getValue());
            qywxContactFollowUpDTOMap = qywxContactFollowUpDTOList.stream().collect(Collectors.toMap(QYWXContactFollowUpDTO::getTargetId, data -> data));
        }
        // 查询负责人
        List<Integer> fsUserOwnerId = Lists.newArrayList();
        List<String> externalUserId = Lists.newArrayList();
        for (ObjectData objectData : objectDataList) {
            if (objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName()) == null) {
                continue;
            }
            List<String> ownerList = GsonUtil
                .fromJson(GsonUtil.getGson().toJson(objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName())), new TypeToken<List<String>>() {
                }.getType());
            fsUserOwnerId.add(Integer.valueOf(ownerList.get(0)));
            externalUserId.add(objectData.getString(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()));
        }
        Map<String, String> externalUserId2userMarketingId = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(externalUserId)) {
            List<UserMarketingWxWorkExternalUserRelationEntity> entities = userMarketingWxWorkExternalUserRelationDao.listByEaAndWxWorkExternalUserIds(arg.getEa(), externalUserId);
            if (CollectionUtils.isNotEmpty(entities)) {
                entities.forEach(e->{
                    externalUserId2userMarketingId.put(e.getWxWorkExternalUserId(), e.getUserMarketingId());
                });
            }
        }
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(arg.getEa(), fsUserOwnerId, true);
        Map<String,String> userMarketingIdsMap = userMarketingAccountManager.getMarketingUserIdByObjects(arg.getEa(),objectDataList,externalUserId2userMarketingId);
        for (ObjectData objectData : objectDataList) {
            QueryContactListResult queryContactListResult = new QueryContactListResult();
            queryContactListResult.setId(objectData.getId());
            queryContactListResult.setWxName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()));
            queryContactListResult.setRemarkName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName()));
            if (objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName()) != null) {
                // 头像
                queryContactListResult.setAvatar(getAvatarFormCrmData(objectData, arg.getEa()));
            }
            if (objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName()) != null) {
                List<String> ownerList = GsonUtil
                    .fromJson(GsonUtil.getGson().toJson(objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName())), new TypeToken<List<String>>() {
                    }.getType());
                if (CollectionUtils.isNotEmpty(ownerList)) {
                    queryContactListResult.setOwner(fsEmployeeMsgMap.get(Integer.valueOf(ownerList.get(0))) != null ? fsEmployeeMsgMap.get(Integer.valueOf(ownerList.get(0))).getName() : null);
                }
            }
            queryContactListResult.setEnterpriseName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.ENTERPRISE_NAME.getFieldName()));
            if (MapUtils.isNotEmpty(tagMap) && tagMap.get(objectData.getId()) != null) {
                // 标签
                queryContactListResult.setTagNameList(tagMap.get(objectData.getId()).getTagNameList());
            }
            queryContactListResult.setWxWorkAdder(objectData.getString(CrmWechatWorkExternalUserFieldEnum.WX_WORK_ADDER.getFieldName()));
            queryContactListResult.setAddStatus(objectData.getString(CrmWechatWorkExternalUserFieldEnum.ADD_STATUS.getFieldName()));
            queryContactListResult.setAddSource(objectData.getString(CrmWechatWorkExternalUserFieldEnum.ADD_SOURCE.getFieldName()));
            queryContactListResult.setAddQrCodeName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_NAME.getFieldName()));
            queryContactListResult.setDesc(objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()).toString() : null);
            queryContactListResult.setCreateTime(objectData.getLong(CrmWechatWorkExternalUserFieldEnum.WECHAT_WORK_CREATE_TIME.getFieldName()));
            if (StringUtils.isNotBlank(arg.getUid())) {
                // 小程序端还需要跟进记录
                queryContactListResult.setFollowUpStatus(QywxContactFollowUpEnum.NOT_FOLLOW_UP.getValue());
                QYWXContactFollowUpDTO qywxContactFollowUpDTO = qywxContactFollowUpDTOMap.get(objectData.getId());
                if (qywxContactFollowUpDTO != null) {
                    queryContactListResult.setNextFollowUpTime(qywxContactFollowUpDTO.getNum());
                    queryContactListResult.setFollowUpStatus(QywxContactFollowUpEnum.FOLLOW_UP.getValue());
                }
            }
            String ExternalUserId = objectData.getString(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName());
            queryContactListResult.setExternalUserId(ExternalUserId);
            queryContactListResult.setUserMarketingId(userMarketingIdsMap.get(objectData.getId()));
            resultList.add(queryContactListResult);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<AssociateContactResult> associateContact(AssociateContactArg arg) {
        ObjectData objectData = crmV2Manager.getDetail(arg.getEa(), -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), arg.getId());
        if (objectData == null) {
            log.warn("QYWXContactServiceImpl.queryContactDetail error objectData is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String externalUserId = objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()).toString() : null;
        if (StringUtils.isBlank(externalUserId)) {
            log.warn("QYWXContactServiceImpl.queryContactDetail externalUserId is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        // 获取营销用户id
        AssociationArg associationArg = new AssociationArg();
        associationArg.setPhone(objectData.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()).toString() : null);
        associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
        associationArg.setAssociationId(objectData.getId());
        associationArg.setEa(arg.getEa());
        associationArg.setAdditionalAssociationId(externalUserId);
        associationArg.setUserName(objectData.getName());
        associationArg.setEmail(objectData.getString("email"));
        associationArg.setTriggerSource(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription());
        associationArg.setTriggerAction("associateContact");
        AssociationResult associationResult = userMarketingAssociationManager.associate(associationArg);
        if (associationResult == null) {
            return Result.newSuccess(null);
        }
        log.warn("QYWXContactServiceImpl.associateContact error associationResult:{}", associationResult);
        AssociateContactResult associateContactResult = BeanUtil.copy(associationResult, AssociateContactResult.class);
        return Result.newSuccess(associateContactResult);
    }

    @Override
    public Result<QueryContactDetailResult> queryContactDetail(QueryContactDetailArg arg) {
        ObjectData objectData = crmV2Manager.getDetail(arg.getFsEa(), -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), arg.getId());
        if (objectData == null) {
            log.warn("QYWXContactServiceImpl.queryContactDetail error objectData is null arg:{}", arg);
            return Result.newError(SHErrorCode.QYWX_CONTACT_NOT_FOUND);
        }
        QueryContactDetailResult queryContactDetailResult = new QueryContactDetailResult();
        queryContactDetailResult.setId(objectData.getId());
        queryContactDetailResult.setWxName(objectData.get(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()).toString() : null);
        queryContactDetailResult.setRemarkName(objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName()).toString() : null);
        if (objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName()) != null) {
            // 头像
            queryContactDetailResult.setAvatar(getAvatarFormCrmData(objectData, arg.getFsEa()));
        }
        if (objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER_R.getFieldName()) != null) {
            // 负责人
            Map<String, Object> ownerMap = GsonUtil
                .fromJson(GsonUtil.getGson().toJson(objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER_R.getFieldName())), new TypeToken<Map<String, String>>() {
                }.getType());
            queryContactDetailResult.setOwner(ownerMap.get("name") != null ? ownerMap.get("name").toString() : null);
        }
        queryContactDetailResult.setEnterpriseName(objectData.get(CrmWechatWorkExternalUserFieldEnum.ENTERPRISE_NAME.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.ENTERPRISE_NAME.getFieldName()).toString() : null);
        // 标签
        Map<String, ObjectDataIdAndTagNameListData> tagMap = metadataTagManager
            .getObjectDataIdAndTagNameListDataMapByObjectDataIds(arg.getFsEa(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),
                Lists.newArrayList(objectData.getId()));
        if (MapUtils.isNotEmpty(tagMap) && tagMap.get(objectData.getId()) != null) {
            queryContactDetailResult.setTagNameList(tagMap.get(objectData.getId()).getTagNameList());
        }
        queryContactDetailResult.setDesc(objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()).toString() : null);
        queryContactDetailResult.setCreateTime(objectData.getLong(CrmWechatWorkExternalUserFieldEnum.WECHAT_WORK_CREATE_TIME.getFieldName()));
        queryContactDetailResult.setExternalUserId(
            objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName())
                .toString() : null);

        UserMarketingWxWorkExternalUserRelationEntity userMarketingWxWorkExternalUserRelationEntity = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(arg.getFsEa(), (String)objectData.get("external_user_id"));
        if (userMarketingWxWorkExternalUserRelationEntity != null){
            queryContactDetailResult.setUserMarketingId(userMarketingWxWorkExternalUserRelationEntity.getUserMarketingId());
        }

        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            log.warn("QYWXContactServiceImpl.queryContactList error arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        String accessToken = qywxManager.getAccessToken(arg.getFsEa());
        List<String> externalUserIdList = Lists.newArrayList();
        externalUserIdList.add((String)objectData.get("external_user_id"));
        List<GetExternalContactDetailResult> contactDetailResult = qywxManager.getExternalContactDetail(arg.getFsEa(), accessToken, externalUserIdList);
        if (CollectionUtils.isNotEmpty(contactDetailResult)){
            GetExternalContactDetailResult contactDetail =  contactDetailResult.get(0);
            if (CollectionUtils.isNotEmpty(contactDetail.getFollowUserList())) {
                for (GetExternalContactDetailResult.FollowUser followUser : contactDetail.getFollowUserList()) {
                    if (StringUtils.isNotEmpty(followUser.getRemark())){
                        queryContactDetailResult.setName(followUser.getRemark());
                        queryContactDetailResult.setPhone(followUser.getMobile());
                        queryContactDetailResult.setCompanyName(followUser.getRemarkCorpName());
                        queryContactDetailResult.setDescription(followUser.getDescription());
                    }
                }
            }
        }

        return Result.newSuccess(queryContactDetailResult);
    }

    @Override
    public Result addFollowUpInfo(AddFollowUpInfoArg arg) {
        // 校验数据有效性
        ObjectData objectData = crmV2Manager.getDetail(arg.getFsEa(), -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), arg.getTargetId());
        if (objectData == null) {
            log.warn("QYWXContactServiceImpl.addFollowUpInfo objectData is null arg:{}", arg);
            return Result.newError(SHErrorCode.QYWX_CONTACT_NOT_FOUND);
        }
        ContactFollowUp contactFollowUp = new ContactFollowUp();
        contactFollowUp.setContentText(arg.getContentText());
        contactFollowUp.setNextFollowUpTime(arg.getNextFollowUpTime());
        QywxContactFollowUpEntity qywxContactFollowUpEntity = new QywxContactFollowUpEntity();
        qywxContactFollowUpEntity.setId(UUIDUtil.getUUID());
        qywxContactFollowUpEntity.setFollowerId(arg.getUid());
        qywxContactFollowUpEntity.setTargetId(arg.getTargetId());
        qywxContactFollowUpEntity.setFollowType(QywxContactFollowUpTypeEnum.MINI_APP_CONTACT_FOLLOW_UP.getValue());
        qywxContactFollowUpEntity.setContent(contactFollowUp);
        qywxContactFollowUpEntity.setActionType(QywxContactFollowUpActionTypeEnum.MINI_APP_ADD_FOLLOW_UP.getValue());
        qywxContactFollowUpEntity.setStatus(QywxContactFollowUpStatusEnum.NORMAL.getValue());
        qywxContactFollowUpDAO.insertQywxContactFollowUp(qywxContactFollowUpEntity);

        // 添加企业微信日程
        try {
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(arg.getFsEa());
            if (agentConfig != null) {
                String name = objectData.get(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()).toString() : null;
                String enterpriseName = objectData.get(CrmWechatWorkExternalUserFieldEnum.ENTERPRISE_NAME.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.ENTERPRISE_NAME.getFieldName()).toString() : null;
                String accessToken = qywxManager.getAccessToken(arg.getFsEa());
                QywxScheduleContainer qywxScheduleContainer = new QywxScheduleContainer();
                QywxScheduleContainer.Schedule schedule = new QywxScheduleContainer.Schedule();
                if (StringUtils.isNotBlank(enterpriseName)) {
                    schedule.setSummary(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXCONTACTSERVICEIMPL_599) + name + " " + enterpriseName);
                } else {
                    schedule.setSummary(I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXCONTACTSERVICEIMPL_599) + name);
                }
                schedule.setStartTime(arg.getNextFollowUpTime() / 1000L);
                schedule.setEndTime(arg.getNextFollowUpTime() / 1000L + 3600 * 24);
                schedule.setOrganizer(arg.getQyUserId());
                schedule.setDescription(arg.getContentText());
                Map<String, String> map = Maps.newHashMap();
                map.put("userid", arg.getQyUserId());
                schedule.setAttendees(Lists.newArrayList(map));
                qywxScheduleContainer.setSchedule(schedule);
                qywxManager.createQywxSchedule(accessToken, qywxScheduleContainer);
            }
        } catch (Exception e) {
            log.warn("QYWXContactServiceImpl.addFollowUpInfo error arg:{}, e:{}", arg, e);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<List<QueryActionAndFollowUpResult>> queryActionAndFollowUp(QueryActionAndFollowUpArg arg) {
        // 查询营销用户行为记录数据
        com.facishare.marketing.statistic.common.model.PageArg<UserMarketingActionStatisticQueryArg> pageArg = new com.facishare.marketing.statistic.common.model.PageArg<>();
        pageArg.setPageNo(1);
        pageArg.setPageSize(1000);
        Map<String, UserMarketingActionResult> userMarketingActionResultMap = Maps.newHashMap();
        List<ActionAndFollowUpContainer> actionContainerList = Lists.newArrayList();
        Map<String, QywxContactFollowUpEntity> qywxContactFollowUpEntityMap = Maps.newHashMap();

        if (StringUtils.isNotBlank(arg.getUserMarketingId())) {
            UserMarketingActionStatisticQueryArg queryArg = new UserMarketingActionStatisticQueryArg();
            queryArg.setEa(arg.getFsEa());
            queryArg.setUserMarketingId(arg.getUserMarketingId());
            pageArg.setQueryArgs(queryArg);
            com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> result = userMarketingStatisticService
                    .pageUserMarketingActionStatistic(pageArg);
            if (result != null && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getData())) {
                com.facishare.marketing.api.result.PageResult<UserMarketingActionResult> pageResult = userMarketingAccountManager
                        .getUserMarketingActionResultPageResult(arg.getFsEa(), arg.getFsUserId(), result);
                if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getData())) {
                    userMarketingActionResultMap = pageResult.getData().stream()
                            .collect(Collectors.toMap(UserMarketingActionResult::getId, data -> data, (v1, v2) -> v1));
                    actionContainerList.addAll(pageResult.getData().stream()
                            .map(data -> new ActionAndFollowUpContainer(QywxContactActionAndFollowUpEnum.ACTION.getValue(), data.getId(), data.getCreateTime())).collect(Collectors.toList()));
                }
            }
        }

        if (StringUtils.isNotBlank(arg.getCrmContactId())) {
            // 查询跟进记录数据
            List<QywxContactFollowUpEntity> qywxContactFollowUpEntityList = qywxContactFollowUpDAO.getByQywxContactFollowUp(arg.getUid(), arg.getCrmContactId(),
                    QywxContactFollowUpTypeEnum.MINI_APP_CONTACT_FOLLOW_UP.getValue(), QywxContactFollowUpActionTypeEnum.MINI_APP_ADD_FOLLOW_UP.getValue());
            if (CollectionUtils.isNotEmpty(qywxContactFollowUpEntityList)) {
                qywxContactFollowUpEntityMap = qywxContactFollowUpEntityList.stream().collect(Collectors.toMap(QywxContactFollowUpEntity::getId, data -> data));
                actionContainerList.addAll(
                        qywxContactFollowUpEntityList.stream().map(data -> new ActionAndFollowUpContainer(QywxContactActionAndFollowUpEnum.FOLLOWUP.getValue(), data.getId(), data.getUpdateTime().getTime())).collect(
                                Collectors.toList()));
            }
        }

        // 按照时间排序
        actionContainerList = actionContainerList.stream().filter(Objects::nonNull).sorted((v1, v2) -> v2.getTime().compareTo(v1.getTime())).collect(Collectors.toList());

        // 获取个人数据
        CardEntity cardEntity = cardDAO.queryCardInfoByUid(arg.getUid());
        String avatar = null;
        if (cardEntity != null) {
            avatar = cardEntity.getAvatar();
        }

        List<QueryActionAndFollowUpResult> queryActionAndFollowUpResultList = Lists.newArrayList();
        for (ActionAndFollowUpContainer actionAndFollowUpContainer : actionContainerList) {
            QueryActionAndFollowUpResult queryActionAndFollowUpResult = new QueryActionAndFollowUpResult();
            if (actionAndFollowUpContainer.getActionAndFollowUpType().equals(QywxContactActionAndFollowUpEnum.ACTION.getValue())) {
                queryActionAndFollowUpResult.setDataType(QywxContactActionAndFollowUpEnum.ACTION.getValue());
                queryActionAndFollowUpResult.setUserMarketingActionResult(userMarketingActionResultMap.get(actionAndFollowUpContainer.getId()));
            } else {
                queryActionAndFollowUpResult.setDataType(QywxContactActionAndFollowUpEnum.FOLLOWUP.getValue());
                queryActionAndFollowUpResult.setContactFollowUpDetailResult(BeanUtil.copy(qywxContactFollowUpEntityMap.get(actionAndFollowUpContainer.getId()), ContactFollowUpDetailResult.class));
                if (queryActionAndFollowUpResult.getContactFollowUpDetailResult() != null) {
                    queryActionAndFollowUpResult.getContactFollowUpDetailResult().setCreateTime(qywxContactFollowUpEntityMap.get(actionAndFollowUpContainer.getId()).getCreateTime().getTime());
                    queryActionAndFollowUpResult.getContactFollowUpDetailResult().setUpdateTime(qywxContactFollowUpEntityMap.get(actionAndFollowUpContainer.getId()).getUpdateTime().getTime());
                    queryActionAndFollowUpResult.getContactFollowUpDetailResult().setAvatar(avatar);
                }
            }
            queryActionAndFollowUpResultList.add(queryActionAndFollowUpResult);
        }
        actionContainerList.clear();
        userMarketingActionResultMap.clear();
        qywxContactFollowUpEntityMap.clear();
        return Result.newSuccess(queryActionAndFollowUpResultList);
    }

    @Override
    public Result<Void> setContactConfigByEmployee(String uid, String fsEa, Integer fsUserId) {
        // 若已经存在配置则直接返回
        QywxContactMeConfigEntity qywxContactMeConfigEntity = qywxContactMeConfigDAO.getByUid(uid);
        if (qywxContactMeConfigEntity != null) {
            return Result.newSuccess();
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(fsEa);
        if (agentConfig == null) {
            log.warn("QYWXContactServiceImpl.setContactConfigByEmployee agentConfig is null ea:{}", fsEa);
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }

        String accessToken = qywxManager.getAccessToken(fsEa);
        String userId = qywxUserManager.getQyUserIdByFsUserInfo(fsEa, fsUserId);
        if (StringUtils.isBlank(userId)) {
            log.warn("QYWXContactServiceImpl.setContactConfigByEmployee userId is null ea:{}, fsUserId:{}", fsEa, fsUserId);
            return Result.newSuccess();
        }
        ContactMeConfigArg arg = new ContactMeConfigArg(userId);
        String configId = qywxManager.setContactMeConfig(accessToken, arg);
        if (StringUtils.isBlank(configId)){
            log.warn("QYWXContactServiceImpl.setContactConfigByEmployee configId is null");
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        QywxContactMeConfigEntity entity = new QywxContactMeConfigEntity();
        entity.setUid(uid);
        entity.setConfigId(configId);
        qywxContactMeConfigDAO.insert(entity);

        return Result.newSuccess();
    }

    @Override
    public Result<QueryWxUserMarketingUserStatusResult> queryWxUserMarketingUserStatus(String ea, String uid, String qywxUserId) {
        UserMarketingMiniappAccountRelationEntity entity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, uid);
        QueryWxUserMarketingUserStatusResult result = new QueryWxUserMarketingUserStatusResult();
        if (entity == null){
            result.setBindMarketingUser(false);
        }else {
            result.setBindMarketingUser(true);
        }
        result.setWxUserId(qywxUserId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryContactMeConfigResult> queryContactMeConfig(String ea, String uid, String qywxUserId, String employeeUserId){
        UserMarketingMiniappAccountRelationEntity accountRelationEntity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, uid);
        QywxContactMeConfigEntity configEntity = qywxContactMeConfigDAO.getByUid(employeeUserId);
        QueryContactMeConfigResult result = new QueryContactMeConfigResult();
        result.setWxUserId(qywxUserId);
        if (accountRelationEntity != null){
            result.setBindMarketingUser(true);
        }else {
            result.setBindMarketingUser(false);
        }

        if (configEntity != null){
             result.setConfigId(configEntity.getConfigId());
        }

        //判断当前用户是否为群成员
        String wxExternalUserId = null;
        if (accountRelationEntity != null){
            List<String> userMarketingIds = Lists.newArrayList();
            userMarketingIds.add(accountRelationEntity.getUserMarketingId());
            List<UserMarketingWxWorkExternalUserRelationEntity> wxAccountList =
                    userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingIds);
            if (CollectionUtils.isNotEmpty(wxAccountList)){
                UserMarketingWxWorkExternalUserRelationEntity relationEntity = wxAccountList.get(0);
                wxExternalUserId = relationEntity.getWxWorkExternalUserId();
            }
        }

        if (StringUtils.isNotBlank(wxExternalUserId) && StringUtils.isNotBlank(wxExternalUserId)){
            QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
            if (agentConfig != null) {
                String accessToken = qywxManager.getAccessToken(ea);
                List<String> wxCustomerList = qywxManager.getQyWxustomerCList(accessToken, employeeUserId);
                if (CollectionUtils.isNotEmpty(wxCustomerList) && wxCustomerList.contains(wxExternalUserId)){
                    result.setMyCusotomer(true);
                }
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryQywxGroupSendFsUserInfoResult> queryQywxGroupSendFsUserInfo(QueryQywxGroupSendFsUserInfoArg arg) {
        // 查询群发id
        QywxGroupSendTaskEntity qywxGroupSendTaskEntity = qywxGroupSendTaskDAO.querySendTaskByMarketingAcivityId(arg.getMarketingActivityId());
        if (qywxGroupSendTaskEntity == null) {
            log.warn("QYWXContactServiceImpl.queryQywxGroupSendFsUserInfo qywxGroupSendTaskEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 查询external_userid
        UserMarketingMiniappAccountRelationEntity accountRelationEntity = userMarketingMiniappAccountRelationDao.getByEaAndUid(qywxGroupSendTaskEntity.getEa(), arg.getUid());
        if (accountRelationEntity == null) {
            log.warn("QYWXContactServiceImpl.queryQywxGroupSendFsUserInfo accountRelationEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        List<String> userMarketingIds = Lists.newArrayList();
        userMarketingIds.add(accountRelationEntity.getUserMarketingId());
        List<UserMarketingWxWorkExternalUserRelationEntity> wxAccountList =
            userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(qywxGroupSendTaskEntity.getEa(), userMarketingIds);
        if (CollectionUtils.isEmpty(wxAccountList)) {
            log.warn("QYWXContactServiceImpl.queryQywxGroupSendFsUserInfo wxAccountList is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String wxExternalUserId = wxAccountList.get(0).getWxWorkExternalUserId();
        // 查询推广人企业微信信息
        List<QywxGroupSendResultEntity> qywxGroupSendResultEntityList = qywxGroupSendResultDAO
            .queryQywxGroupSendResultByMsgId(qywxGroupSendTaskEntity.getMsgid(), wxExternalUserId, QywxGroupSendMsgStatus.SEND.getType());
        if (CollectionUtils.isEmpty(qywxGroupSendResultEntityList)) {
            log.warn("QYWXContactServiceImpl.queryQywxGroupSendFsUserInfo qywxGroupSendResultEntityList is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 查询推广人纷享信息
        Integer fsUserId = qywxUserManager.getFsUserIdByQyWxInfo(qywxGroupSendTaskEntity.getEa(), qywxGroupSendResultEntityList.get(0).getUserid(), true, false);
        if (fsUserId == null) {
            log.warn("QYWXContactServiceImpl.queryQywxGroupSendFsUserInfo error fsUserId is null");
            return Result.newError(SHErrorCode.NO_DATA);
        }
        QueryQywxGroupSendFsUserInfoResult result = new QueryQywxGroupSendFsUserInfoResult();
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByFsEaAndFsUserId(qywxGroupSendTaskEntity.getEa(), fsUserId, AccountTypeEnum.QYWX_MINI_APP.getType(), arg.getAppId());
        if (fsBindEntity != null) {
            result.setUid(fsBindEntity.getUid());
        }
        result.setEa(qywxGroupSendTaskEntity.getEa());
        result.setFsUserId(fsUserId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryQywxGroupSendFsUserInfoResult> queryAllSpreadSendFsUserInfo(QueryQywxGroupSendFsUserInfoArg arg) {
        QueryQywxGroupSendFsUserInfoResult result = new QueryQywxGroupSendFsUserInfoResult();
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity =
                marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getMarketingActivityId());
        if (marketingActivityExternalConfigEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }

        result.setEa(marketingActivityExternalConfigEntity.getEa());
        result.setFsUserId(arg.getFsUserId());
        result.setUid(arg.getUid());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> checkMyCustomer(String uid, String fsUserUid) {
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(fsUserUid);
        if (fsBindEntity == null){
            log.info("checkMyCustomer return false fsBindEntity not exist fsUserUid:{}", fsUserUid);
            return Result.newSuccess(Boolean.FALSE);
        }

        UserMarketingMiniappAccountRelationEntity entity = userMarketingMiniappAccountRelationDao.getByEaAndUid(fsBindEntity.getFsEa(), uid);
        if (entity == null){
            log.info("check my customer return false not find UserMarketingMiniappAccountRelationEntity ea:{} wxUserUid:{}", fsBindEntity.getFsEa(), uid);
            return Result.newSuccess(Boolean.FALSE);
        }

        List<String> userMarketingIds = Lists.newArrayList();
        userMarketingIds.add(entity.getUserMarketingId());
        List<UserMarketingWxWorkExternalUserRelationEntity> userRelationEntities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(fsBindEntity.getFsEa(), userMarketingIds);
        if (CollectionUtils.isEmpty(userRelationEntities)){
            log.info("check my customer return false not find userRelationEntities ea:{} wxUserUid:{} userMarketingIds:{}", fsBindEntity.getFsEa(), uid, userMarketingIds);
            return Result.newSuccess(Boolean.FALSE);
        }

        List<String> externalUserIds = Lists.newArrayList();
        externalUserIds.add(userRelationEntities.get(0).getWxWorkExternalUserId());
        List<GetExternalContactDetailResult> contactDetailResults = customerGroupManager.queryExternalContactDetail(fsBindEntity.getFsEa(), externalUserIds);
        if (CollectionUtils.isEmpty(contactDetailResults)){
            log.info("check my customer return false not find contactDetailResults ea:{} wxUserUid:{} contactDetailResults:{}", fsBindEntity.getFsEa(), uid, contactDetailResults);
            return Result.newSuccess(Boolean.FALSE);
        }

        List<GetExternalContactDetailResult.FollowUser> followUserList = contactDetailResults.get(0).getFollowUserList();
        if (CollectionUtils.isEmpty(followUserList)){
            log.info("check my customer return false not find followUserList ea:{} wxUserUid:{} userMarketingIds:{}", fsBindEntity.getFsEa(), uid, userMarketingIds);
            return Result.newSuccess(Boolean.FALSE);
        }

        String qywxUserId = qywxUserManager.getQyUserIdByFsUserInfo(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId());
        /*if (QywxUserConstants.isVirtualUserId(fsBindEntity.getFsUserId())) {
            qywxUserId = qywxVirtualFsUserDAO.queryQyUserIdByVirtualInfo(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId());
        } else {
            List<String> fsAccountList = Lists.newArrayList();
            String fsAccount = "E." + fsBindEntity.getFsEa() + "." + fsBindEntity.getFsUserId();
            fsAccountList.add(fsAccount);
            com.facishare.open.qywx.accountbind.result.Result<Map<String, String>> accountBindResult =
                qyweixinAccountBindService.fsAccountToOutAccountBatch("qywx", fsAccountList);
            if (!accountBindResult.isSuccess() || accountBindResult.getData() == null){
                log.info("check my customer return false fsAccountToOutAccountBatch failed ea:{} accountBindResult:{}", fsBindEntity.getFsEa(), accountBindResult);
                return Result.newSuccess(Boolean.FALSE);
            }
            qywxUserId = accountBindResult.getData().get(fsAccount);
        }*/
        if (qywxUserId == null){
            log.info("check my customer return false not find qywxUser bind failed ea:{} fsUserId:{}", fsBindEntity.getFsEa(), fsBindEntity.getFsUserId());
            return Result.newSuccess(Boolean.FALSE);
        }

        boolean isCustomer = false;
        for (GetExternalContactDetailResult.FollowUser followUser : followUserList){
            if (StringUtils.equals(qywxUserId, followUser.getUserId())){
                isCustomer = true;
                break;
            }
        }

        return Result.newSuccess(isCustomer);
    }

    @Override
    public Result<String> getQywxObjectIdByQywxExternUserId(String ea, String qywxExternUserId) {
        List<String> externalUserIds = new ArrayList<>();
        externalUserIds.add(qywxExternUserId);
        Map<String, ObjectData> crmWxWorkExternalDataMap = wechatWorkExternalUserObjManager.getObjectDataMap(ea, externalUserIds);
        if (crmWxWorkExternalDataMap == null || crmWxWorkExternalDataMap.get(qywxExternUserId) == null){
            log.info("getQywxObjectIdByUid failed getObjectDataMap data error ea:{} qywxExternUserId:{}", ea, qywxExternUserId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        ObjectData objectData = crmWxWorkExternalDataMap.get(qywxExternUserId);
        return Result.newSuccess(objectData.getString("_id"));
    }

    @Override
    public Result<String> createAddfanQrCode(QywxCreateAddFanQrCodeVO vo) {
        //如果选择部门,则通过部门查询下面所有的员工列表
        List<String> orginUserId = vo.getUserId();
        List<String> userIdList = new ArrayList<>();
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(vo.getEa()) ;
        if (!isOpen) {
            Set<String> userIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(vo.getUserId())) {
                userIds.addAll(vo.getUserId());
            }
            //处理员工标签问题
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getTagIds())) {
                List<String> employeeIds = qywxManager.batchGetEmployeeByTags(vo.getEa(), vo.getTagIds());
                userIds.addAll(employeeIds);
            }
            if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
                List<Integer> qywxCircleIds = new ArrayList<>();
                for (int i = 0; i < vo.getDepartmentIds().size(); i++) {
                    qywxCircleIds.add(i, vo.getDepartmentIds().get(i) - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                }
                List<String> qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(vo.getEa(), qywxCircleIds);
                userIds.addAll(qywxUserIds);
            }
            userIdList.addAll(userIds);
        } else {
            if (vo.isFromCard()) {
                userIdList.addAll(vo.getUserId());
            } else {
                List<Integer> deptIds = dataPermissionManager.filterUserAccessibleQywxDeptIds(vo.getEa(), vo.getCreateUserId(), vo.getDepartmentIds());
                userIdList.addAll(qywxManager.handleQywxEmployeeUserId(vo.getEa(), vo.getUserId(), deptIds, null));
            }
        }
        if (CollectionUtils.isEmpty(userIdList)) {
            return Result.newError(SHErrorCode.NO_DATA_OWN_ORGANIZATION);
        }
        userIdList = qywxManager.fliterUnActiveUser(vo.getEa(),userIdList);
        ContactMeConfigArg arg = new ContactMeConfigArg(vo.getType(), vo.getRemark(), userIdList, vo.getChannelDesc());
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(vo.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }

        if (vo.getSkipVerify() == 0){
            arg.setSkipVerify(true);
        }else {
            arg.setSkipVerify(false);
        }

        //state默认30个字符，扫码的时候企业微信回传 ：企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详情”时会返回该参数值，不超过30个字符
        String state = System.currentTimeMillis() +"_"  + UUIDUtil.generateUID(10);
        if (qywxAddFanQrCodeDAO.getByEaAndState(vo.getEa(), state) != null){
            log.info("createAddfanQrCode failed status is exist ea:{} currentState:{}", vo.getEa(), state);
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        arg.setState(state);

        String accessToken = qywxManager.getAccessToken(vo.getEa());
        String configId = qywxManager.setContactMeConfig(accessToken, arg);
        if (configId == null){
            log.info("createAddfanQrCode failed setContactMeConfig return configId==null vo:{}", vo);
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        GetContactMeResult contactMeResult = qywxManager.getContanctMe(accessToken, configId);
        if (contactMeResult == null || !contactMeResult.isSuccess() || StringUtils.isEmpty(contactMeResult.getContactWay().getQrCode())){
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }

        //图片素材
        if (StringUtils.isNotEmpty(vo.getWelcomeImagePath())){
            byte[] data = fileV2Manager.downloadAFile(vo.getWelcomeImagePath(), vo.getEa());
            UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, vo.getWelcomeImagePath(), "image");
            if (mediaResult != null && mediaResult.getErrcode() == 0) {
                redisManager.setQywxMediaId(vo.getWelcomeImagePath(), mediaResult.getMediaId());
            }
        }

        //小程序素材
        if (StringUtils.isNotEmpty(vo.getWelcomeMiniprogramMediaPath())){
            byte[] data = fileV2Manager.downloadAFile(vo.getWelcomeMiniprogramMediaPath(), vo.getEa());
            UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, vo.getWelcomeMiniprogramMediaPath(), "image");
            if (mediaResult != null && mediaResult.getErrcode() == 0) {
                redisManager.setQywxMediaId(vo.getWelcomeMiniprogramMediaPath(), mediaResult.getMediaId());
            }
        }

        QywxAddFanQrCodeEntity entity = new QywxAddFanQrCodeEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(vo.getEa());
        entity.setQrCodeName(vo.getQrCodeName());
        entity.setRemark(vo.getRemark());
        entity.setChannelDesc(vo.getChannelDesc());
        if (vo.getTag() != null){
            entity.setTag(GsonUtil.toJson(vo.getTag()));
        }
        if (CollectionUtils.isNotEmpty(userIdList)){
            entity.setUserId(GsonUtil.toJson(userIdList));
        }
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())){
            entity.setDepartmentId(GsonUtil.toJson(vo.getDepartmentIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            entity.setTagId(GsonUtil.toJson(vo.getTagIds()));
        }
        if (CollectionUtils.isNotEmpty(orginUserId)) {
            entity.setOrginUserId(GsonUtil.toJson(orginUserId));
        }
        entity.setType(vo.getType());
        entity.setQrCodeUrl(contactMeResult.getContactWay().getQrCode());
        entity.setSkipVerify(vo.getSkipVerify());
        entity.setConfigId(contactMeResult.getContactWay().getConfigId());
        entity.setStatus(QywxFanQrCodeStatusEnum.NORMAL.getStatus());
        entity.setState(state);
        entity.setIsBindWebsite(QrCodeBindWebsiteTypeEnum.NORMAL_QR_TYPE.getType());
        entity.setCustomerCount(0);
        entity.setWelcomeContent(vo.getWelcomeContent());
        entity.setWelcomeImagePath(vo.getWelcomeImagePath());
        entity.setWelcomeImageTitle(vo.getWelcomeImageTitle());
        entity.setWelcomeLinkTitle(vo.getWelcomeLinkTitle());
        entity.setWelcomeLinkUrl(vo.getWelcomeLinkUrl());
        entity.setWelcomeMiniprogramTitle(vo.getWelcomeMiniprogramTitle());
        entity.setWelcomeMiniprogramPage(vo.getWelcomeMinigramPage());
        entity.setWelcomeMiniprogramMediaPath(vo.getWelcomeMiniprogramMediaPath());
        entity.setChannelValue(vo.getChannelValue());
        entity.setMarketingEventId(vo.getMarketingEventId());
        entity.setCreateBy(vo.getCreateUserId());
        entity.setSource(vo.getSource());
        entity.setUserBaseRemark(vo.getUserBaseRemark());
        entity.setUserBaseRemarkStatus(vo.getUserBaseRemarkStatus());

        //企微群发支持多附件
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            for (QywxAttachmentsVO attachmentvo : vo.getQywxAttachmentsVO()) {
                //处理小程序内容
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.MINIPROGRAM.getType() && attachmentvo.getMiniprogram() != null&& attachmentvo.getMiniprogram().getMiniProgramType()==MiniProgramTypeEnum.CONTENT.getType()) {
                    int objectType = objectManager.convertNoticeContentTypeToObjectType(attachmentvo.getMiniprogram().getMaterialType());
                    attachmentvo.getMiniprogram().setObjectType(objectType);
                    if (Strings.isNullOrEmpty(attachmentvo.getMiniprogram().getPicPath())) {
                        List<PhotoEntity> photoEntities = null;
                        String hexagonCover = null;
                        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), attachmentvo.getMiniprogram().getMaterialId());

                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> siteIds = Lists.newArrayList();
                            siteIds.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> ids = Lists.newArrayList();
                            ids.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(ids);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        }
                        if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            attachmentvo.getMiniprogram().setPicPath(hexagonCover);
                        } else {
                            if (CollectionUtils.isNotEmpty(photoEntities)) {
                                attachmentvo.getMiniprogram().setPicPath(photoEntities.get(0).getPath());
                            }
                        }
                    }
                }
            }

        }

        QywxAttachmentsRelationEntity qywxAttachmentsRelationEntity = new QywxAttachmentsRelationEntity();
        qywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
        qywxAttachmentsRelationEntity.setEa(vo.getEa());
        qywxAttachmentsRelationEntity.setTargetId(entity.getId());
        qywxAttachmentsRelationEntity.setTargetType(QywxAttachmentScenesTypeEnum.QYWX_FAN_CODE.getType());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            // 将二维码id拼接到url中, 用于企微活码自动回复的表单提交线索时，将活码上的营销推广来源带到线索中
            appendQrCodeIdToUrl(vo, entity);
            qywxAttachmentsRelationEntity.setAttachments(gson.toJson(vo.getQywxAttachmentsVO()));
        }
        qywxAttachmentsRelationDAO.insert(qywxAttachmentsRelationEntity);
        qywxAddFanQrCodeDAO.insert(entity);
        objectGroupManager.setGroup(vo.getEa(), vo.getCreateUserId(), ObjectTypeEnum.FAN_CODE.getType(), Collections.singletonList(entity.getId()), vo.getGroupId());
        if (StringUtils.isNotBlank(entity.getMarketingEventId()) || StringUtils.isNotBlank(entity.getChannelValue())) {
            // 将营销推广来源参数和二维码的state关联起来，在企微回调的时候，可以通过state获取到营销推广来源参数
            MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();
            marketingPromotionSourceArg.setMarketingEventId(entity.getMarketingEventId());
            marketingPromotionSourceArg.setChannelValue(entity.getChannelValue());
            marketingPromotionSourceArgObjectRelationManager.createEntity(entity.getEa(), state, marketingPromotionSourceArg);
        }
        return Result.newSuccess(contactMeResult.getContactWay().getQrCode());
    }

    @Override
    public Result<String> customizeCreateFanQrCode(Integer tenantId, Integer fsUserId, CreateFanQrCodeArg vo) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        List<String> qyUserIdList = Lists.newArrayList();
        Map<Integer, String> qyUserIdMap = qywxUserManager.getQyUserIdByFsUserInfo(ea, vo.getUserIds());
        if (CollectionUtils.isNotEmpty(qyUserIdMap.values())) {
            qyUserIdList.addAll(qyUserIdMap.values());
        }
        if (CollectionUtils.isEmpty(qyUserIdList)) {
            return Result.newError(SHErrorCode.NO_DATA_OWN_ORGANIZATION);
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        String qrCode = null;
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getByBindInfo(ea, vo.getBindApiName(), vo.getBindObjectId());
        String accessToken = qywxManager.getAccessToken(ea);
        if (entity == null) {
            entity = new QywxAddFanQrCodeEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            ContactMeConfigArg arg = new ContactMeConfigArg(QywxAddFanQrCodeTypeEnum.SINGLE.getType(), null, qyUserIdList, null);
            arg.setSkipVerify(vo.getSkipVerify() == 0);
            //state默认30个字符，扫码的时候企业微信回传 ：企业自定义的state参数，用于区分不同的添加渠道，在调用“获取外部联系人详情”时会返回该参数值，不超过30个字符
            String state = System.currentTimeMillis() +"_"  + UUIDUtil.generateUID(10);
            if (qywxAddFanQrCodeDAO.getByEaAndState(ea, state) != null){
                log.info("customizeCreateFanQrCode failed status is exist ea:{} currentState:{}", ea, state);
                return Result.newError(SHErrorCode.SERVER_BUSY);
            }
            arg.setState(state);
            String configId = qywxManager.setContactMeConfig(accessToken, arg);
            if (configId == null){
                log.info("customizeCreateFanQrCode failed setContactMeConfig return configId==null vo:{}", vo);
                return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
            }
            GetContactMeResult contactMeResult = qywxManager.getContanctMe(accessToken, configId);
            if (contactMeResult == null || !contactMeResult.isSuccess() || StringUtils.isEmpty(contactMeResult.getContactWay().getQrCode())){
                return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
            }
            entity.setQrCodeName(vo.getQrCodeName());
            if (CollectionUtils.isNotEmpty(qyUserIdList)) {
                entity.setUserId(GsonUtil.toJson(qyUserIdList));
            }
            if (CollectionUtils.isNotEmpty(qyUserIdList)) {
                entity.setOrginUserId(GsonUtil.toJson(qyUserIdList));
            }
            entity.setType(QywxAddFanQrCodeTypeEnum.SINGLE.getType());
            qrCode = contactMeResult.getContactWay().getQrCode();
            entity.setQrCodeUrl(qrCode);
            entity.setSkipVerify(vo.getSkipVerify());
            entity.setConfigId(contactMeResult.getContactWay().getConfigId());
            entity.setStatus(QywxFanQrCodeStatusEnum.NORMAL.getStatus());
            entity.setState(state);
            entity.setIsBindWebsite(QrCodeBindWebsiteTypeEnum.NORMAL_QR_TYPE.getType());
            entity.setCustomerCount(0);
            entity.setWelcomeContent(vo.getWelcomeContent());
            entity.setSource(QywxFanQrCodeSourceEnum.CREATE_BY_EMPLOYEE.getSource());
            entity.setBindApiName(vo.getBindApiName());
            entity.setBindObjectId(vo.getBindObjectId());
        } else {
            qrCode = entity.getQrCodeUrl();
            entity.setWelcomeContent(vo.getWelcomeContent());
        }
        qywxAddFanQrCodeDAO.insert(entity);
        objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.FAN_CODE.getType(), Collections.singletonList(entity.getId()), vo.getGroupId());
        return Result.newSuccess(qrCode);
    }

    public void appendQrCodeIdToUrl(QywxCreateAddFanQrCodeVO vo, QywxAddFanQrCodeEntity entity) {
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("qrCodeId", entity.getId());
        paramMap.put("qrCodeCategory", QrCodeCategoryEnum.QYWX.getType());
        for (QywxAttachmentsVO qywxAttachmentsVO : vo.getQywxAttachmentsVO()) {
            QywxAttachmentsVO.Link link = qywxAttachmentsVO.getLink();
            if (link != null) {
               String url = link.getUrl();
                if (StringUtils.isNotBlank(url)) {
                    link.setUrl(HttpUtil.getAppendUrl(url, paramMap));
                }
                String page = link.getPage();
                if (StringUtils.isNotBlank(page)) {
                    link.setPage(HttpUtil.getAppendUrl(page, paramMap));
                }
            }
            QywxAttachmentsVO.Miniprogram miniprogram = qywxAttachmentsVO.getMiniprogram();
            if (miniprogram != null) {
                String url = miniprogram.getUrl();
                if (StringUtils.isNotBlank(url)) {
                    miniprogram.setUrl(HttpUtil.getAppendUrl(url, paramMap));
                }
                String page = miniprogram.getPage();
                if (StringUtils.isNotBlank(page)) {
                    miniprogram.setPage(HttpUtil.getAppendUrl(page, paramMap));
                }
            }
        }
    }

    @Override
    public Result<QueryAddfanQrCodeResult> queryAddfanQrCode(String id) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getById(id);
        if (entity == null){
            log.info("queryAddfanQrCode failed qrCodeEntity is not exist id:{}", id);
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }

        if (entity.getStatus() == QywxFanQrCodeStatusEnum.DELETE.getStatus()){
            log.info("queryAddfanQrCode failed qrCodeEntity is delete id:{}", id);
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_IS_DELETED);
        }

        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(entity.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }

        QueryAddfanQrCodeResult result = new QueryAddfanQrCodeResult();
        result.setId(entity.getId());
        result.setEa(entity.getEa());
        result.setQrCodeName(entity.getQrCodeName());
        result.setRemark(entity.getRemark());
        result.setChannelDesc(entity.getChannelDesc());
        result.setCustomerCount(entity.getCustomerCount());
        result.setUserBaseRemark(entity.getUserBaseRemark());
        result.setUserBaseRemarkStatus(entity.getUserBaseRemarkStatus());
        if (entity.getTag() != null){
            TagNameList tagNameList = GsonUtil.getGson().fromJson(entity.getTag(), TagNameList.class);
            result.setTag(tagNameList);
        }
        List<EmployeeOwnerResult> employeeList = Lists.newArrayList();
        //兼容一下之前老数据问题和当前数据问题,按照更新时间2022-11-30 00:00:00 为时间节点
        List<String> userId = new ArrayList<>();
        if (DateUtil.getTimeDifference(DateUtil.parse("2022-11-30 00:00:00"), entity.getUpdateTime(), TimeTypeEnum.MILLIS.getType()) > 0) {
            userId = GsonUtil.getGson().fromJson(entity.getOrginUserId(), new TypeToken<List<String>>() {}.getType());
        } else {
            userId = GsonUtil.getGson().fromJson(entity.getUserId(), new TypeToken<List<String>>() {}.getType());
        }
        if (CollectionUtils.isNotEmpty(userId)) {
            for (String userid : userId) {
                EmployeeOwnerResult employee = new EmployeeOwnerResult();
                employee.setEmployeeUserId(userid);
                String accessToken = qywxManager.getAccessToken(entity.getEa());
                StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(entity.getEa(), userid, accessToken, true);
                if (staffDetailResult != null) {
                    employee.setEmployeeName(staffDetailResult.getName());
                }
                employeeList.add(employee);
            }
        }
        result.setEmployee(employeeList);
        result.setType(entity.getType());
        result.setQrCodeName(entity.getQrCodeName());
        result.setQrCodeUrl(entity.getQrCodeUrl());
        result.setSkipVerify(entity.getSkipVerify());
        result.setWelcomeContent(entity.getWelcomeContent());
        result.setWelcomeImagePath(entity.getWelcomeImagePath());
        result.setWelcomeImageTitle(entity.getWelcomeImageTitle());
        result.setWelcomeLinkTitle(entity.getWelcomeLinkTitle());
        result.setWelcomeMiniprogramTitle(entity.getWelcomeMiniprogramTitle());
        result.setWelcomeMiniprogramMediaPath(entity.getWelcomeMiniprogramMediaPath());
        result.setWelcomeMinigramPage(entity.getWelcomeMiniprogramPage());
        result.setChannelValue(entity.getChannelValue());
        //获取部门
        List<DepartmentResult> departmentResultList = Lists.newArrayList();
        List<Integer> departmentId = GsonUtil.getGson().fromJson(entity.getDepartmentId(), new TypeToken<List<Integer>>(){}.getType());
        String accessToken = qywxManager.getAccessToken(entity.getEa());
        if (CollectionUtils.isNotEmpty(departmentId)) {
            DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
            Map<Integer, Department> departmentMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, a -> a, (k1, k2) -> k1));
            for (Integer dptId : departmentId) {
                Integer circleId = dptId - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID;
                DepartmentResult departmentResult = new DepartmentResult();
                if (departmentMap.containsKey(circleId)) {
                    departmentResult.setId(dptId);
                    departmentResult.setName(departmentMap.get(circleId).getName());
                    departmentResultList.add(departmentResult);
                }
            }

        }
        result.setDepartmentResults(departmentResultList);
        //获取员工标签
        List<EmployeeTagResult> employeeTagResults = Lists.newArrayList();
        List<Integer> tagIds = GsonUtil.getGson().fromJson(entity.getTagId(), new TypeToken<List<Integer>>(){}.getType());
        if (CollectionUtils.isNotEmpty(tagIds)) {
            List<EmployeeTagResult> tagResultList = qywxManager.queryEmployeeInfo(accessToken);
            Map<Integer, EmployeeTagResult> tagResultMap = tagResultList.stream().collect(Collectors.toMap(EmployeeTagResult::getTagId, Function.identity(), (k1, k2) -> k2));
            tagIds.forEach(tagId -> {
                if (tagResultMap.containsKey(tagId)) {
                    EmployeeTagResult employeeTagResult = tagResultMap.get(tagId);
                    employeeTagResults.add(employeeTagResult);
                }
            });
        }
        QywxAttachmentsRelationEntity attachmentsRelationEntities = qywxAttachmentsRelationDAO.getDetailByTargetId(result.getId(), QywxAttachmentScenesTypeEnum.QYWX_FAN_CODE.getType());
        if(Objects.isNull(attachmentsRelationEntities)){
            List<QywxAttachmentsVO> qywxAttachmentsVOs = Lists.newArrayList();

            //h5
            if (StringUtils.isNotEmpty(entity.getWelcomeLinkUrl())){
                QywxAttachmentsVO qywxAttachmentsVO = new QywxAttachmentsVO();
                qywxAttachmentsVO.setAttachmentType(QywxAttachmentTypeEnum.H5.getType());
                QywxAttachmentsVO.Link link = new QywxAttachmentsVO.Link();
                link.setTitle(entity.getWelcomeLinkTitle());
                link.setPicPath(entity.getWelcomeLinkUrl());
                qywxAttachmentsVO.setLink(link);
                qywxAttachmentsVOs.add(qywxAttachmentsVO);
            }

            //小程序素材
            if (StringUtils.isNotEmpty(entity.getWelcomeMiniprogramMediaPath())){
                QywxAttachmentsVO qywxAttachmentsVO = new QywxAttachmentsVO();
                qywxAttachmentsVO.setAttachmentType(QywxAttachmentTypeEnum.MINIPROGRAM.getType());
                QywxAttachmentsVO.Miniprogram miniprogram = new QywxAttachmentsVO.Miniprogram();
                miniprogram.setTitle(entity.getWelcomeMiniprogramTitle());
                miniprogram.setPicPath(entity.getWelcomeMiniprogramMediaPath());
                miniprogram.setPicUrl(fileV2Manager.getUrlByPath(entity.getEa(),entity.getWelcomeMiniprogramMediaPath()));
                miniprogram.setPage(entity.getWelcomeMiniprogramPage());
                miniprogram.setMiniProgramType(MiniProgramTypeEnum.CONTENT.getType());
                if(StringUtils.isNotBlank(entity.getWelcomeMiniprogramPage())){
                    Pattern pattern = Pattern.compile("objectType=(\\d+)&objectId=([a-f\\d]{32})");
                    Matcher matcher = pattern.matcher(entity.getWelcomeMiniprogramPage());
                    if (matcher.find()) {
                        miniprogram.setObjectType(Integer.valueOf(matcher.group(1)));
                        miniprogram.setMaterialId(matcher.group(2));
                        miniprogram.setMaterialType(ObjectTypeEnum.getByType(Integer.valueOf(matcher.group(1))).toNoticeContentType());
                    }
                }
                if(miniprogram.getObjectType()!=null && StringUtils.isNotBlank(miniprogram.getMaterialId())){
                    miniprogram.setObjectName(objectManager.getObjectName(miniprogram.getMaterialId(),miniprogram.getObjectType()));
                }
                qywxAttachmentsVO.setMiniprogram(miniprogram);
                qywxAttachmentsVOs.add(qywxAttachmentsVO);
            }
            result.setQywxAttachmentsVO(qywxAttachmentsVOs);
        }else {
            result.setQywxAttachmentsVO(JSON.parseArray(attachmentsRelationEntities.getAttachments(), QywxAttachmentsVO.class));
        }
        result.setEmployeeTagResults(employeeTagResults);
        result.setMarketingEventId(entity.getMarketingEventId());
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> updateAddfanQrCode(QywxCreateAddFanQrCodeVO vo) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getById(vo.getId());
        List<String> orginUserId = vo.getUserId();
        if (entity == null){
            log.info("updateAddfanQrCode failed qrCodeEntity is not exist id:{}", vo.getId());
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }

        // 官网引流二维码不应该编辑 防止误调直接返回
        if (StringUtils.isNotEmpty(vo.getMarketingWebsiteUserId())) {
            return Result.newSuccess(entity.getQrCodeUrl());
        }
        //如果选择部门,则通过部门查询下面所有的员工列表
        List<String> userIdList = new ArrayList<>();
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(vo.getEa());
        if (!isOpen) {
            Set<String> userIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(vo.getUserId())) {
                userIds.addAll(vo.getUserId());
            }
            //处理员工标签问题
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getTagIds())) {
                List<String> employeeIds = qywxManager.batchGetEmployeeByTags(vo.getEa(), vo.getTagIds());
                userIds.addAll(employeeIds);
            }
            if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
                List<Integer> qywxCircleIds = new ArrayList<>();
                for (int i = 0; i < vo.getDepartmentIds().size(); i++) {
                    qywxCircleIds.add(i, vo.getDepartmentIds().get(i) - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                }
                List<String> qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(vo.getEa(), qywxCircleIds);
                userIds.addAll(qywxUserIds);
            }
            userIdList.addAll(userIds);
        } else {
            List<Integer> deptIds = dataPermissionManager.filterUserAccessibleQywxDeptIds(vo.getEa(), vo.getCreateUserId(), vo.getDepartmentIds());
            userIdList.addAll(qywxManager.handleQywxEmployeeUserId(vo.getEa(), vo.getUserId(), deptIds, null));
        }
        if (CollectionUtils.isEmpty(userIdList)) {
            return Result.newError(SHErrorCode.NO_DATA_OWN_ORGANIZATION);
        }
        userIdList = qywxManager.fliterUnActiveUser(vo.getEa(),userIdList);
        UpdateContactMeConfigArg arg = new UpdateContactMeConfigArg(vo.getRemark(), userIdList, vo.getChannelDesc());
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(vo.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        arg.setConfigId(entity.getConfigId());
        if (vo.getSkipVerify() == 0){
            arg.setSkipVerify(true);
        }else {
            arg.setSkipVerify(false);
        }
        String accessToken = qywxManager.getAccessToken(vo.getEa());
        arg.setState(entity.getState());
        boolean updateRet = qywxManager.updateContactMeConfig(accessToken, arg);
        if (!updateRet){
            log.info("updateAddfanQrCode failed vo:{}", vo);
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }

        //图片素材
        if (StringUtils.isNotEmpty(vo.getWelcomeImagePath())){
            byte[] data = fileV2Manager.downloadAFile(vo.getWelcomeImagePath(), vo.getEa());
            UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, vo.getWelcomeImagePath(), "image");
            if (mediaResult != null && mediaResult.getErrcode() == 0) {
                redisManager.setQywxMediaId(vo.getWelcomeImagePath(), mediaResult.getMediaId());
            }
        }

        //小程序素材
        if (StringUtils.isNotEmpty(vo.getWelcomeMiniprogramMediaPath())){
            byte[] data = fileV2Manager.downloadAFile(vo.getWelcomeMiniprogramMediaPath(), vo.getEa());
            UploadMediaResult mediaResult = httpManager.uploadFile(accessToken, data, vo.getWelcomeMiniprogramMediaPath(), "image");
            if (mediaResult != null && mediaResult.getErrcode() == 0) {
                redisManager.setQywxMediaId(vo.getWelcomeMiniprogramMediaPath(), mediaResult.getMediaId());
            }
        }

        QywxAddFanQrCodeEntity updateEntity = new QywxAddFanQrCodeEntity();
        updateEntity.setId(entity.getId());
        updateEntity.setEa(vo.getEa());
        updateEntity.setQrCodeName(vo.getQrCodeName());
        updateEntity.setRemark(vo.getRemark());
        updateEntity.setChannelDesc(vo.getChannelDesc());
        if (vo.getTag() != null){
            updateEntity.setTag(GsonUtil.toJson(vo.getTag()));
        }
        if (CollectionUtils.isNotEmpty(userIdList)){
            updateEntity.setUserId(GsonUtil.toJson(userIdList));
        }
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
            updateEntity.setDepartmentId(GsonUtil.toJson(vo.getDepartmentIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            updateEntity.setTagId(GsonUtil.toJson(vo.getTagIds()));
        }
        if (StringUtils.isNotBlank(vo.getMarketingEventId())) {
            updateEntity.setMarketingEventId(vo.getMarketingEventId());
        }
        if (CollectionUtils.isNotEmpty(orginUserId)) {
            updateEntity.setOrginUserId(GsonUtil.toJson(orginUserId));
        }
        updateEntity.setType(vo.getType());
        updateEntity.setQrCodeUrl(entity.getQrCodeUrl());
        updateEntity.setSkipVerify(vo.getSkipVerify());
        updateEntity.setConfigId(entity.getConfigId());
        updateEntity.setStatus(QywxFanQrCodeStatusEnum.NORMAL.getStatus());
        updateEntity.setState(entity.getState());
        updateEntity.setWelcomeContent(vo.getWelcomeContent());
        updateEntity.setWelcomeImagePath(vo.getWelcomeImagePath());
        updateEntity.setWelcomeImageTitle(vo.getWelcomeImageTitle());
        updateEntity.setWelcomeLinkTitle(vo.getWelcomeLinkTitle());
        updateEntity.setWelcomeLinkUrl(vo.getWelcomeLinkUrl());
        updateEntity.setWelcomeMiniprogramTitle(vo.getWelcomeMiniprogramTitle());
        updateEntity.setWelcomeMiniprogramPage(vo.getWelcomeMinigramPage());
        updateEntity.setWelcomeMiniprogramMediaPath(vo.getWelcomeMiniprogramMediaPath());
        updateEntity.setChannelValue(vo.getChannelValue());
        updateEntity.setUserBaseRemark(vo.getUserBaseRemark());
        updateEntity.setUserBaseRemarkStatus(vo.getUserBaseRemarkStatus());

        //企微群发支持多附件
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            for (QywxAttachmentsVO attachmentvo : vo.getQywxAttachmentsVO()) {
                //处理小程序内容
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.MINIPROGRAM.getType() && attachmentvo.getMiniprogram() != null && attachmentvo.getMiniprogram().getMiniProgramType()==MiniProgramTypeEnum.CONTENT.getType()) {
                    int objectType = objectManager.convertNoticeContentTypeToObjectType(attachmentvo.getMiniprogram().getMaterialType());
                    attachmentvo.getMiniprogram().setObjectType(objectType);
                    if (Strings.isNullOrEmpty(attachmentvo.getMiniprogram().getPicPath())) {
                        List<PhotoEntity> photoEntities = null;
                        String hexagonCover = null;
                        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), attachmentvo.getMiniprogram().getMaterialId());

                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> siteIds = Lists.newArrayList();
                            siteIds.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> ids = Lists.newArrayList();
                            ids.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(ids);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        }
                        if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            attachmentvo.getMiniprogram().setPicPath(hexagonCover);
                        } else {
                            if (CollectionUtils.isNotEmpty(photoEntities)) {
                                attachmentvo.getMiniprogram().setPicPath(photoEntities.get(0).getPath());
                            }
                        }
                    }
                }
            }

        }

        QywxAttachmentsRelationEntity attachmentsRelationEntities = qywxAttachmentsRelationDAO.getDetailByTargetId(entity.getId(), QywxAttachmentScenesTypeEnum.QYWX_FAN_CODE.getType());
        if(Objects.isNull(attachmentsRelationEntities)){
            QywxAttachmentsRelationEntity qywxAttachmentsRelationEntity = new QywxAttachmentsRelationEntity();
            qywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
            qywxAttachmentsRelationEntity.setEa(entity.getEa());
            qywxAttachmentsRelationEntity.setTargetId(entity.getId());
            qywxAttachmentsRelationEntity.setTargetType(QywxAttachmentScenesTypeEnum.QYWX_FAN_CODE.getType());
            if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
                qywxAttachmentsRelationEntity.setAttachments(gson.toJson(vo.getQywxAttachmentsVO()));
            }
            qywxAttachmentsRelationDAO.insert(qywxAttachmentsRelationEntity);
        }else {
            qywxAttachmentsRelationDAO.updateByTargetId(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())?JSON.toJSONString(vo.getQywxAttachmentsVO()):null,updateEntity.getId(),QywxAttachmentScenesTypeEnum.QYWX_FAN_CODE.getType());
        }

        qywxAddFanQrCodeDAO.updateById(updateEntity);
        //更新绑定的极客二维码的信息
        updateBindQkQrCode(updateEntity.getEa(), updateEntity.getId());
        handleMarketingPromotionSourceArgObjectRelation(vo.getEa(), updateEntity.getId());
        return Result.newSuccess(entity.getQrCodeUrl());
    }

    private void handleMarketingPromotionSourceArgObjectRelation(String ea, String id) {
        QywxAddFanQrCodeEntity addFanQrCodeEntity = qywxAddFanQrCodeDAO.getById(id);
        String state = addFanQrCodeEntity.getState();
        if (StringUtils.isBlank(addFanQrCodeEntity.getMarketingEventId()) && StringUtils.isBlank(addFanQrCodeEntity.getChannelValue())) {
            marketingPromotionSourceArgObjectRelationManager.deleteByQrCodeIdentifyId(ea, addFanQrCodeEntity.getState());
        } else {
            QrCodeIdentifySpreadSourceRelationEntity qrCodeIdentifySpreadSourceRelationEntity = marketingPromotionSourceArgObjectRelationManager.getByQrCodeIdentifyId(ea, addFanQrCodeEntity.getState());
            // 将营销推广来源参数和二维码的state关联起来，在企微回调的时候，可以通过state获取到营销推广来源参数
            MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();
            marketingPromotionSourceArg.setMarketingEventId(addFanQrCodeEntity.getMarketingEventId());
            marketingPromotionSourceArg.setChannelValue(addFanQrCodeEntity.getChannelValue());
            if (qrCodeIdentifySpreadSourceRelationEntity == null) {
                marketingPromotionSourceArgObjectRelationManager.createEntity(ea, state, marketingPromotionSourceArg);
            } else {
                marketingPromotionSourceArgObjectRelationManager.updateMarketingPromotionSourceArg(ea, state, marketingPromotionSourceArg);
            }
        }
    }

    public void updateBindQkQrCode(String ea, String parentQrCodeId){
        QywxAddFanQrCodeEntity parentEntity = qywxAddFanQrCodeDAO.getById(parentQrCodeId);
        if (parentEntity == null){
            return;
        }

        List<QywxAddFanQrCodeEntity> qrCodeEntities = qywxAddFanQrCodeDAO.queryQrCodeByParentQrCodeId(ea, parentQrCodeId);
        if (CollectionUtils.isEmpty(qrCodeEntities)){
            return;
        }
        //更新关联的极客二维码信息
        for (QywxAddFanQrCodeEntity entity :  qrCodeEntities){
            //海报活码不改名
            if(StringUtils.isNotBlank(entity.getPosterId())){
                entity.setQrCodeName(entity.getQrCodeName());
            }else {
                entity.setQrCodeName(parentEntity.getQrCodeName());
            }
            entity.setRemark(parentEntity.getRemark());
            entity.setChannelDesc(parentEntity.getChannelDesc());
            entity.setTag(parentEntity.getTag());
            entity.setUserId(parentEntity.getUserId());
            entity.setTagId(parentEntity.getTagId());
            entity.setDepartmentId(parentEntity.getDepartmentId());
            entity.setWelcomeContent(parentEntity.getWelcomeContent());
            entity.setWelcomeMiniprogramTitle(parentEntity.getWelcomeMiniprogramTitle());
            entity.setWelcomeMiniprogramMediaPath(parentEntity.getWelcomeMiniprogramMediaPath());
            entity.setWelcomeMiniprogramPage(parentEntity.getWelcomeMiniprogramPage());
            entity.setChannelValue(parentEntity.getChannelValue());
            entity.setMarketingEventId(parentEntity.getMarketingEventId());
            entity.setWelcomeImagePath(parentEntity.getWelcomeImagePath());
            entity.setWelcomeImageTitle(parentEntity.getWelcomeImageTitle());
            entity.setWelcomeLinkTitle(parentEntity.getWelcomeLinkTitle());
            entity.setWelcomeLinkUrl(parentEntity.getWelcomeLinkUrl());
            qywxAddFanQrCodeDAO.updateById(entity);
        }
    }

    @Override
    public Result<Void> deleteAddfanQrCode(String ea, String id) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getById(id);
        if (entity == null){
            log.info("deleteAddfanQrCode failed qrCodeEntity is not exist id:{}", id);
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }

        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = qywxManager.getAccessToken(ea);
        boolean delRet = qywxManager.detelteContactMe(accessToken, entity.getConfigId());
        if (!delRet) {
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_DELETED_FAILED);
        }
        qywxAddFanQrCodeDAO.updateStatusById(id, QywxFanQrCodeStatusEnum.DELETE.getStatus());

        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> updateQywxCustomerRemark(UpdateQywxCustomerRemarkVO vo) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(vo.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        String accessToken = qywxManager.getAccessToken(vo.getEa());
        QywxEmployeeUpdateArg arg = new QywxEmployeeUpdateArg();
        arg.setUserid(vo.getUserId());
        arg.setExternalUserid(vo.getWxExternalUserId());
        arg.setRemarkCompany(vo.getCompanyName());
        arg.setDescription(vo.getDescription());
        arg.setRemark(vo.getName());
        if (vo.getPhone() != null){
            List<String> phones = Lists.newArrayList();
            phones.add(vo.getPhone());
            arg.setRemarkMobiles(phones);
        }
        boolean ret = qywxManager.updateCustomerRemark(accessToken, arg);

        if (ret) {
            // 更新企业微信客户对象
            wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(vo.getEa(), Lists.newArrayList(vo.getWxExternalUserId()), true, null);
        }

        //更新标签到营销用户
        if (vo.getTagNameList() != null) {
            List<String> wxExternalUserIds = Lists.newArrayList();
            wxExternalUserIds.add(vo.getWxExternalUserId());
            List<String> apiNameList = Lists.newArrayList(WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            userMarketingAccountService.batchAddTagsToUserMarketings(vo.getEa(),
                    vo.getFsUserId(), apiNameList, wxExternalUserIds, vo.getTagNameList());
        }

        return Result.newSuccess(ret);
    }

    @Override
    public Result<List<EmployeeOwnerResult>> queryQywxEmployeeBaseInfo(String ea,Integer status,Integer fsUserId) {
        List<EmployeeOwnerResult> result = Lists.newArrayList();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        String accessToken = qywxManager.getAccessToken(ea);
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, false);
        if(CollectionUtils.isNotEmpty(staffInfoList) && dataPermissionManager.getNewDataPermissionSetting(ea)){
            List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(ea, fsUserId,true);
            if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                return Result.newSuccess(result);
            }
            if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                qywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds.stream().map(o -> o-QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                List<Integer> finalQywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds;
                staffInfoList = staffInfoList.stream().filter(staffInfo -> CollectionUtils.containsAny(staffInfo.getDepartment(), finalQywxAccessibleDepartmentIds)).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(staffInfoList)){
            staffInfoList.forEach(staffInfo -> {
                if (status == null){
                    EmployeeOwnerResult employeeOwnerResult = new EmployeeOwnerResult();
                    employeeOwnerResult.setEmployeeName(staffInfo.getName());
                    employeeOwnerResult.setEmployeeUserId(staffInfo.getUserId());
                    result.add(employeeOwnerResult);
                }else {
                    if (status == staffInfo.getStatus()){
                        EmployeeOwnerResult employeeOwnerResult = new EmployeeOwnerResult();
                        employeeOwnerResult.setEmployeeName(staffInfo.getName());
                        employeeOwnerResult.setEmployeeUserId(staffInfo.getUserId());
                        result.add(employeeOwnerResult);
                    }
                }
            });
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<List<String>> queryCrmUserIdByExternalUserid(QueryCrmUserIdArg arg) {
        List<String> qywxExternalUserIds = new ArrayList<>();
        Integer ei = eieaConverter.enterpriseAccountToId(arg.getFsEa());
        qywxExternalUserIds.add(arg.getExtendUserId());
        com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> listCrmObjectResult = wechatWorkExternalUserObjManager.listObjectDataByIdsLimited(ei, qywxExternalUserIds);
        if (!listCrmObjectResult.isSuccess() || listCrmObjectResult.getData() == null) {
            log.warn("QYWXContactService.queryCrmUserIdByExternalUserid listCrmObjectResult error result :{}", listCrmObjectResult);
            return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(), listCrmObjectResult.getMessage());
        }
        List<String> res = new ArrayList<>();
        List<ObjectData> objectDataList = listCrmObjectResult.getData().getDataList();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Result.newSuccess(res);
        }
        res.addAll(objectDataList.stream().map(o -> o.getId()).collect(Collectors.toList()));
        return Result.newSuccess(res);
    }

    @Override
    public Result<String> bindQywxQrCodeWithWebsite(String ea, Integer fsUserId, BindQywxQrCodeWithWebsiteArg vo) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getById(vo.getQywxQrCodeId());
        if (entity == null) {
            log.warn("websiteBindFanQrCode bindQywxQrCodeWithWebsite is null, id:{}, ea:{}", vo.getQywxQrCodeId(), ea);
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }
        // 获取执行绑定操作的员工fs通讯录信息
        FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(ea, fsUserId);
        if (fsEmployeeMsg == null) {
            log.warn("bindQywxQrCodeWithWebsite fsEmployeeMsg is null, vo:{}", vo);
            return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        String doBindFsUserName = StringUtils.isEmpty(fsEmployeeMsg.getFullName()) ? fsEmployeeMsg.getName() : fsEmployeeMsg.getFullName();
        QywxAddFanQrCodeEntity bindNewEntity = BeanUtil.copy(entity, QywxAddFanQrCodeEntity.class);
        bindNewEntity.setId(UUIDUtil.getUUID());
        bindNewEntity.setIsBindWebsite(QrCodeBindWebsiteTypeEnum.WEB_SITE_BASE_QR_TYPE.getType());
        bindNewEntity.setDoBindFsUserId(fsUserId);
        bindNewEntity.setDoBindFsUserName(doBindFsUserName);
        bindNewEntity.setCustomerCount(0);
        String state = JIKE_QR_PRE + System.currentTimeMillis() + "_" + UUIDUtil.generateUID(10);
        bindNewEntity.setState(state);
        bindNewEntity.setUpdateBindStatusTime(new Date());
        bindNewEntity.setConfigId(null);
        bindNewEntity.setParentId(vo.getQywxQrCodeId());
        qywxAddFanQrCodeDAO.insert(bindNewEntity);

        return Result.newSuccess(bindNewEntity.getId());
    }

    @Override
    public Result<String> unbindQywxQrCodeWithWebsite(String ea, Integer fsUserId, BindQywxQrCodeWithWebsiteArg arg) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getById(arg.getQywxQrCodeId());
        if (entity == null) {
            log.warn("websiteBindFanQrCode unbindQywxQrCodeWithWebsite is null, id:{}, ea:{}", arg.getQywxQrCodeId(), ea);
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }
        FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(ea, fsUserId);
        if (fsEmployeeMsg == null) {
            log.warn("bindQywxQrCodeWithWebsite fsEmployeeMsg is null, ea:{} fsUserId:{} vo:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        String userName = StringUtils.isEmpty(fsEmployeeMsg.getFullName()) ? fsEmployeeMsg.getName() : fsEmployeeMsg.getFullName();
        qywxAddFanQrCodeDAO.updateFanQrCodeWebsiteBindStatus(arg.getQywxQrCodeId(), QrCodeBindWebsiteTypeEnum.UNBIND.getType(), fsUserId, userName);
        return Result.newSuccess();
    }

    @Override
    public Result<List<QueryAddfanQrCodeResult>> queryWebsiteBindFanQrCode(QueryWebsiteBindFanQrCodeVO vo) {
        List<QueryAddfanQrCodeResult> resultList = Lists.newArrayList();
        List<QywxAddFanQrCodeEntity> entities = qywxAddFanQrCodeDAO.queryBindWebsiteByEa(vo.getEa());
        if (CollectionUtils.isEmpty(entities)) {
            log.warn("queryWebsiteBindFanQrCode entities is null, ea:{}", vo.getEa());
            return Result.newSuccess();
        }
        resultList = buildQueryAddfanQrCodeResultList(vo.getEa(), entities, resultList);
        return Result.newSuccess(resultList);
    }

    @Override
    public Result<QueryAddfanQrCodeResult> createOrUpdateWebsiteFanQrCode(CreateOrUpdateWebSiteFanQrCodeVO vo) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getBindWebsiteFanQrCodeById(vo.getFanQrCodeId());
        if (entity == null) {
            log.warn("createOrUpdateWebsiteFanQrCode entity is null, fanQrCodeId:{}", vo.getFanQrCodeId());
            return Result.newSuccess();
        }

        QywxQrCodeBrowseUserRelationEntity relationEntity = qywxAddFanQrCodeRelationDAO.queryByBrowseIdAndQrCodeId(entity.getEa(), vo.getMarketingWebsiteUserId(), vo.getFanQrCodeId());
        if (relationEntity != null){
            QueryAddfanQrCodeResult result = BeanUtil.copy(entity, QueryAddfanQrCodeResult.class);
            result.setQrCodeUrl(relationEntity.getQrCode());
            return Result.newSuccess(result);
        }

        //创建集客二维码
        ContactMeConfigArg arg = new ContactMeConfigArg(entity.getType(), entity.getRemark(), GsonUtil.fromJson(entity.getUserId(), List.class), entity.getChannelDesc());
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(entity.getEa());
        if (agentConfig == null) {
            log.info("createOrUpdateWebsiteFanQrCode failed agentConfig=null ea:{}", entity.getEa());
            return Result.newSuccess();
        }

        if (entity.getSkipVerify() == 0){
            arg.setSkipVerify(true);
        }else {
            arg.setSkipVerify(false);
        }

        //创建新的二维码
        String state = JIKE_QR_PRE + System.currentTimeMillis() + "_" + UUIDUtil.generateUID(10);
        arg.setState(state);
        if (qywxAddFanQrCodeDAO.getByEaAndState(entity.getEa(), state) != null){
            log.info("createAddfanQrCode failed status is exist ea:{} currentState:{}", entity.getEa(), state);
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        String accessToken = qywxManager.getAccessToken(entity.getEa());
        String configId = qywxManager.setContactMeConfig(accessToken, arg);
        if (configId == null){
            log.info("createAddfanQrCode failed setContactMeConfig return configId==null vo:{}", vo);
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        GetContactMeResult contactMeResult = qywxManager.getContanctMe(accessToken, configId);
        if (contactMeResult == null || !contactMeResult.isSuccess() || StringUtils.isEmpty(contactMeResult.getContactWay().getQrCode())){
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }

        relationEntity = qywxAddFanQrCodeRelationDAO.queryByBrowseIdAndQrCodeId(entity.getEa(), vo.getMarketingWebsiteUserId(), vo.getFanQrCodeId());
        if (relationEntity != null) {
            QueryAddfanQrCodeResult result = BeanUtil.copy(entity, QueryAddfanQrCodeResult.class);
            result.setQrCodeUrl(relationEntity.getQrCode());
            return Result.newSuccess(result);
        }

        QywxQrCodeBrowseUserRelationEntity browseUserRelationEntity = new QywxQrCodeBrowseUserRelationEntity();
        browseUserRelationEntity.setId(UUIDUtil.getUUID());
        browseUserRelationEntity.setEa(entity.getEa());
        browseUserRelationEntity.setBrowseUserId(vo.getMarketingWebsiteUserId());
        browseUserRelationEntity.setQywxQrCodeId(vo.getFanQrCodeId());
        browseUserRelationEntity.setConfigId(configId);
        browseUserRelationEntity.setState(state);
        browseUserRelationEntity.setQywxQrCodeId(entity.getId());
        browseUserRelationEntity.setQrCode(contactMeResult.getContactWay().getQrCode());
        browseUserRelationEntity.setStatus(QywxQrCodeBrowseUserRelationEnum.NORMAL.getStatus());
        qywxAddFanQrCodeRelationDAO.insert(browseUserRelationEntity);

        QueryAddfanQrCodeResult result = BeanUtil.copy(entity, QueryAddfanQrCodeResult.class);
        result.setQrCodeUrl(contactMeResult.getContactWay().getQrCode());
        if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(entity.getMarketingEventId())) {
            ObjectData objectData = crmV2Manager.getDetail(entity.getEa(), SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), entity.getMarketingEventId());
            if (objectData != null) {
                vo.setMarketingEventId(entity.getMarketingEventId());
            }
        }
        marketingPromotionSourceArgObjectRelationManager.createEntity(entity.getEa(), state, vo);
        return Result.newSuccess(result);
    }

    @Override
    public Result deleteTemplateWebFanQrCodeByDays(Date deletePointDate) {
        ThreadPoolUtils.execute(() ->{
            List<TemplateBindQrConfigIdDTO> configIdDTOS = qywxAddFanQrCodeRelationDAO.getTemplateBindQrConfigIdsByCheckTime(deletePointDate);
            if (CollectionUtils.isEmpty(configIdDTOS)){
                return;
            }

            configIdDTOS.forEach(dto ->{
                QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(dto.getEa());
                if (agentConfig == null) {
                    log.info("createOrUpdateWebsiteFanQrCode failed agentConfig=null ea:{}", dto.getEa());
                    return ;
                }
                String accessToken = qywxManager.getAccessToken(dto.getEa());
                if(qywxManager.detelteContactMe(accessToken,dto.getConfigId())){
                    qywxAddFanQrCodeRelationDAO.deleteByQrCodeId(dto.getEa(), dto.getQrCodeId());
                }

            });
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result deleteHexagonWebFanQrCodeByDays(Date deletePointDate) {
        ThreadPoolUtils.execute(() ->{
            List<TemplateBindQrConfigIdDTO> configIdDTOS = qywxAddFanQrCodeUserMarketingRelationDAO.getHexagonBindQrConfigIdsByCheckTime(deletePointDate);
            if (CollectionUtils.isEmpty(configIdDTOS)){
                return;
            }

            configIdDTOS.forEach(dto ->{
                QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(dto.getEa());
                if (agentConfig == null) {
                    log.info("deleteHexagonWebFanQrCodeByDays failed agentConfig=null ea:{}", dto.getEa());
                    return ;
                }
                String accessToken = qywxManager.getAccessToken(dto.getEa());
                if(qywxManager.detelteContactMe(accessToken,dto.getConfigId())){
                    qywxAddFanQrCodeUserMarketingRelationDAO.deleteByQrCodeId(dto.getEa(), dto.getQrCodeId());
                }

            });
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result<EditObjectGroupResult> editFanCodeGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QYWXContactServiceImpl.editFanCodeGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("QYWXContactServiceImpl.editFanCodeGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.FAN_CODE.getType());
    }

    @Override
    public Result<Void> deleteFanCodeGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QYWXContactServiceImpl.deleteFanCodeGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.FAN_CODE.getType());
    }

    @Override
    public Result<Void> setFanCodeGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("QYWXContactServiceImpl.setFanCodeGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        List<QywxAddFanQrCodeEntity> qywxAddFanQrCodeEntityList = qywxAddFanQrCodeDAO.getByIds(arg.getObjectIdList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(qywxAddFanQrCodeEntityList)) {
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }
        if (qywxAddFanQrCodeEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_FAN_QRCODE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.FAN_CODE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.FAN_CODE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteFanCodeBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        qywxAddFanQrCodeDAO.deleteByIdList(arg.getIdList());
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.FAN_CODE.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.FAN_CODE.getType(), arg.getIdList());
        //删除联系我配置,异步处理
        ThreadPoolUtils.executeWithTraceContext(() ->{
            List<QywxAddFanQrCodeEntity> qrCodeEntities = qywxAddFanQrCodeDAO.getAddFanQrCodeByIds(arg.getIdList());
            String accessToken = qywxManager.getAccessToken(ea);
            for (QywxAddFanQrCodeEntity entity : qrCodeEntities) {
                boolean delRet = qywxManager.detelteContactMe(accessToken, entity.getConfigId());
                if (!delRet) {
                    log.warn("qywxManager.detelteContactMe error ea:{},codeId:{}",ea,entity.getId());
                }
            }
        },ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> topFanCode(String ea, Integer fsUserId, TopMaterialArg arg) {
        QywxAddFanQrCodeEntity entity = qywxAddFanQrCodeDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.FAN_CODE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> cancelTopFanCode(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.FAN_CODE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.FAN_CODE.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addFanCodeGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.FAN_CODE.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listFanCodeGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.FAN_CODE.getType(), null, null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(qywxAddFanQrCodeDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(qywxAddFanQrCodeDAO.queryAccessibleCount(ea, groupIdList, fsUserId));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(qywxAddFanQrCodeDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(qywxAddFanQrCodeDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }

    @Override
    public Result<List<EmployeeTagResult>> queryQywxTagInfo(String fsEa) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(fsEa);
        if (agentConfig == null) {
            log.info("queryQywxTagInfo failed agentConfig=null ea:{}", fsEa);
            return Result.newError(SHErrorCode.QYWX_AGENT_CONFIG_NOT_FOUND);
        }
        String accessToken = qywxManager.getAccessToken(fsEa);
        List<EmployeeTagResult> employeeTagResults = qywxManager.queryEmployeeInfo(accessToken);
        return Result.newSuccess(employeeTagResults);
    }

    public List<QueryAddfanQrCodeResult> buildQueryAddfanQrCodeResultList(String ea, List<QywxAddFanQrCodeEntity> entities, List<QueryAddfanQrCodeResult> resultList) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        if (agentConfig == null) {
            log.warn("buildQueryAddfanQrCodeResultList agentConfig is null, ea:{}", ea);
            return new ArrayList<QueryAddfanQrCodeResult>();
        }
        for (QywxAddFanQrCodeEntity entity : entities){
            QueryAddfanQrCodeResult qrCodeResult = new QueryAddfanQrCodeResult();
            qrCodeResult.setId(entity.getId());
            qrCodeResult.setEa(entity.getEa());
            qrCodeResult.setQrCodeName(entity.getQrCodeName());
            qrCodeResult.setRemark(entity.getRemark());
            qrCodeResult.setChannelDesc(entity.getChannelDesc());
            qrCodeResult.setCustomerCount(entity.getCustomerCount());
            qrCodeResult.setWelcomeContent(entity.getWelcomeContent());
            qrCodeResult.setWelcomeImagePath(entity.getWelcomeImagePath());
            qrCodeResult.setWelcomeImageTitle(entity.getWelcomeImageTitle());
            qrCodeResult.setWelcomeLinkTitle(entity.getWelcomeLinkTitle());
            qrCodeResult.setWelcomeMiniprogramTitle(entity.getWelcomeMiniprogramTitle());
            qrCodeResult.setWelcomeMiniprogramMediaPath(entity.getWelcomeMiniprogramMediaPath());
            qrCodeResult.setWelcomeMinigramPage(entity.getWelcomeMiniprogramPage());
            qrCodeResult.setIsBindWebsite(entity.getIsBindWebsite());
            qrCodeResult.setUpdateBindStatusTime(simpleDateFormat.format(entity.getCreateTime()));
            qrCodeResult.setDoBindFsUserName(entity.getDoBindFsUserName());
            qrCodeResult.setDoBindFsUserId(entity.getDoBindFsUserId());
            if (entity.getTag() != null){
                TagNameList tagNames = GsonUtil.getGson().fromJson(entity.getTag(), TagNameList.class);
                qrCodeResult.setTag(tagNames);
            }
            List<String> userId = GsonUtil.getGson().fromJson(entity.getUserId(), new TypeToken<List<String>>(){}.getType());
            if (CollectionUtils.isNotEmpty(userId)){
                List<EmployeeOwnerResult> employeeResult = Lists.newArrayList();
                for (String userid : userId) {
                    EmployeeOwnerResult employee = new EmployeeOwnerResult();
                    employee.setEmployeeUserId(userid);
                    String accessToken = qywxManager.getAccessToken(ea);
                    StaffDetailResult staffDetailResult =  qywxManager.getStaffDetail(ea, userid, accessToken, true);
                    if (staffDetailResult != null){
                        employee.setEmployeeName(staffDetailResult.getName());
                    }
                    employeeResult.add(employee);
                }
                qrCodeResult.setEmployee(employeeResult);
            }
            qrCodeResult.setType(entity.getType());
            qrCodeResult.setQrCodeName(entity.getQrCodeName());
            qrCodeResult.setQrCodeUrl(entity.getQrCodeUrl());
            qrCodeResult.setSkipVerify(entity.getSkipVerify());
            resultList.add(qrCodeResult);
        }

        //客户数量，从企业微信中拉取
        if (CollectionUtils.isNotEmpty(resultList)) {
            try {
                Integer ei = eieaConverter.enterpriseAccountToId(ea);
                HeaderObj headerObj = new HeaderObj(ei, -10000);
                List<String> fanQrCodeIds = resultList.stream().map(QueryAddfanQrCodeResult::getId).collect(Collectors.toList());
                // 从【企业微信客户】对象中获取数据
                List<Filter> filterList = Lists.newArrayList();
                List<ObjectData> objectDataList = Lists.newArrayList();
                Map<String, Integer> qrCodeFanNum = Maps.newHashMap();
                Map<String, Integer> needUpdateFanNum = Maps.newHashMap();
                Filter wxFanQrCodeIdFilter = new Filter();
                wxFanQrCodeIdFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName());
                wxFanQrCodeIdFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
                wxFanQrCodeIdFilter.setFieldValues(fanQrCodeIds);
                filterList.add(wxFanQrCodeIdFilter);
                Filter isDeleteFilter = new Filter();
                isDeleteFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.IS_DELETED.getFieldName());
                isDeleteFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
                isDeleteFilter.setFieldValues(Lists.newArrayList("false"));
                filterList.add(isDeleteFilter);
                FilterData filterData = new FilterData();
                filterData.setQuery(new SearchTemplateQuery());
                filterData.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                filterData.getQuery().setFilters(filterList);
                ControllerListArg perCrmListArg = new ControllerListArg();
                perCrmListArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                perCrmListArg.setSearchTemplateId(filterData.getSearchTemplateId());
                perCrmListArg.setIncludeLayout(false);
                perCrmListArg.setIncludeDescribe(false);
                perCrmListArg.setIncludeButtonInfo(false);
                SearchQuery searchQuery = BeanUtil.copyByFastJson(filterData.getQuery(), SearchQuery.class);
                searchQuery.addOrderBy(CrmWechatWorkExternalUserFieldEnum.WECHAT_WORK_CREATE_TIME.getFieldName(), false);
                searchQuery.setOffset(0);
                searchQuery.setLimit(10000);
                perCrmListArg.setSearchQuery(searchQuery);
                com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> listCrmObjectResult = metadataControllerService.list(headerObj, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), perCrmListArg);
                if (listCrmObjectResult.isSuccess() && listCrmObjectResult.getData() != null && CollectionUtils.isNotEmpty(listCrmObjectResult.getData().getDataList())) {
                    objectDataList = listCrmObjectResult.getData().getDataList();
                    for (ObjectData objectData : objectDataList) {
                        String qrCodeId = objectData.getString(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName());
                        if (StringUtils.isNotBlank(qrCodeId)) {
                            qrCodeFanNum.merge(qrCodeId, 1, (prev, one) -> prev + one);
                        }
                    }
                    for (QueryAddfanQrCodeResult qrCodeResult : resultList) {
                        Integer fanNum = qrCodeFanNum.get(qrCodeResult.getId());
                        if (fanNum != null && !fanNum.equals(qrCodeResult.getCustomerCount())) {
                            needUpdateFanNum.put(qrCodeResult.getId(), fanNum);
                        }
                        qrCodeResult.setCustomerCount(fanNum != null ? fanNum : 0);
                    }
                }
                ThreadPoolUtils.execute(() -> {
                    for (Map.Entry<String, Integer> entry : needUpdateFanNum.entrySet()) {
                        qywxAddFanQrCodeDAO.syncCustomerCountById(entry.getKey(), entry.getValue());
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            } catch (Exception e) {
                log.error("QYWXContactServiceImpl.queryAddfanQrCodeList vo:{} exception:", e);
            }
        }
        return resultList;
    }

    @Override
    public Result<PageResult<QueryAddfanQrCodeResult>> queryAddfanQrCodeList(String ea, String keyword, Integer pageNum, Integer pageSize,Integer fsUserId,String groupId,String marketingEventId,
                                                                             Integer isBandPoster) {
        PageResult<QueryAddfanQrCodeResult> pageResult = new PageResult();
        List<QueryAddfanQrCodeResult> resultList = Lists.newArrayList();
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        pageResult.setPageSize(pageNum);
        pageResult.setPageNum(pageSize);
        pageResult.setResult(resultList);
        if (StringUtils.isBlank(groupId)) {
            groupId = DefaultObjectGroupEnum.ALL.getId();
        }
        QywxAddFanQueryParam queryParam = new QywxAddFanQueryParam();
        queryParam.setEa(ea);
        queryParam.setKeyword(keyword);
        if(StringUtils.isNotBlank(marketingEventId)){
            queryParam.setMarketingEventId(marketingEventId);
        }
        if(isBandPoster != null){
            queryParam.setIsBandPoster(isBandPoster);
        }
        Page page = new Page(pageNum, pageSize, true);
        List<QywxAddFanQrCodeDTO> entities;
        if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), groupId)) {
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.FAN_CODE.getType());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            queryParam.setPermissionGroupIdList(permissionGroupIdList);
            entities = qywxAddFanQrCodeDAO.getAccessiblePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), groupId)) {
            queryParam.setUserId(fsUserId);
            entities = qywxAddFanQrCodeDAO.getCreateByMePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), groupId)) {
            entities = qywxAddFanQrCodeDAO.noGroupPage(queryParam, page);
        } else {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.FAN_CODE.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            if (!permissionGroupIdSet.contains(groupId)){
                entities = Lists.newArrayList();
            } else {
                queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                queryParam.setUserId(fsUserId);
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.FAN_CODE.getType(), groupId, objectGroupEntityList);
                accessibleSubGroupIdList.add(groupId);
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                entities = qywxAddFanQrCodeDAO.getAccessiblePage(queryParam, page);
            }
        }
        if (CollectionUtils.isEmpty(entities)){
            return Result.newSuccess(pageResult);
        }

        pageResult.setTotalCount(page.getTotalNum());
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        List<String> qrCodeIds = entities.stream().map(QywxAddFanQrCodeDTO::getId).collect(Collectors.toList());
        //查询二维码关联企微加好友记录表
        Map<String, Integer> totalCustomerMap = wechatFriendsRecordObjDescribeManager.getTotalCustomerCount(ea, qrCodeIds);
        for (QywxAddFanQrCodeDTO entity : entities){
            QueryAddfanQrCodeResult qrCodeResult = new QueryAddfanQrCodeResult();
            qrCodeResult.setId(entity.getId());
            qrCodeResult.setEa(entity.getEa());
            qrCodeResult.setTop(entity.isTop());
            qrCodeResult.setQrCodeName(entity.getQrCodeName());
            qrCodeResult.setRemark(entity.getRemark());
            qrCodeResult.setChannelDesc(entity.getChannelDesc());
            qrCodeResult.setCustomerCount(entity.getCustomerCount());
            qrCodeResult.setWelcomeContent(entity.getWelcomeContent());
            qrCodeResult.setWelcomeImagePath(entity.getWelcomeImagePath());
            qrCodeResult.setWelcomeImageTitle(entity.getWelcomeImageTitle());
            qrCodeResult.setWelcomeLinkTitle(entity.getWelcomeLinkTitle());
            qrCodeResult.setWelcomeMiniprogramTitle(entity.getWelcomeMiniprogramTitle());
            qrCodeResult.setWelcomeMiniprogramMediaPath(entity.getWelcomeMiniprogramMediaPath());
            qrCodeResult.setWelcomeMinigramPage(entity.getWelcomeMiniprogramPage());
            if (entity.getTag() != null){
                TagNameList tagNames = GsonUtil.getGson().fromJson(entity.getTag(), TagNameList.class);
                qrCodeResult.setTag(tagNames);
            }
  
            qrCodeResult.setTotalCustomerCount(totalCustomerMap.get(entity.getId()) == null ? 0 : totalCustomerMap.get(entity.getId()));
            qrCodeResult.setType(entity.getType());
            qrCodeResult.setQrCodeName(entity.getQrCodeName());
            qrCodeResult.setQrCodeUrl(entity.getQrCodeUrl());
            qrCodeResult.setSkipVerify(entity.getSkipVerify());
            resultList.add(qrCodeResult);
        }

        //客户数量，从企业微信中拉取
        if (CollectionUtils.isNotEmpty(resultList)) {
            try {
                List<String> fanQrCodeIds = resultList.stream().map(QueryAddfanQrCodeResult::getId).collect(Collectors.toList());
                Map<String, Integer> qrCodeFanNum = Maps.newHashMap();
                Map<String, Integer> needUpdateFanNum = Maps.newHashMap();

                // 从【企业微信客户】对象中获取数据
                List<ObjectData> objectDataList = getWechatCustomersCountByQrCode(ea, fanQrCodeIds);
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    for (ObjectData objectData : objectDataList) {
                        String qrCodeId = objectData.getString(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName());
                        if (StringUtils.isNotBlank(qrCodeId)) {
                            qrCodeFanNum.merge(qrCodeId, 1, (prev, one) -> prev + one);
                        }
                    }
                    for (QueryAddfanQrCodeResult qrCodeResult : resultList) {
                        Integer fanNum = qrCodeFanNum.get(qrCodeResult.getId());
                        if (fanNum != null && !fanNum.equals(qrCodeResult.getCustomerCount())) {
                            needUpdateFanNum.put(qrCodeResult.getId(), fanNum);
                        }
                        qrCodeResult.setCustomerCount(fanNum != null ? fanNum : 0);
                    }
                }
                ThreadPoolUtils.execute(() -> {
                    for (Map.Entry<String, Integer> entry : needUpdateFanNum.entrySet()) {
                        qywxAddFanQrCodeDAO.syncCustomerCountById(entry.getKey(), entry.getValue());
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            } catch (Exception e) {
                log.error("QYWXContactServiceImpl.queryAddfanQrCodeList vo:{} exception:", e);
            }
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result deleteConfig(){
        ThreadPoolUtils.execute(() ->{
            List<String> eas = qywxAddFanQrCodeDAO.queryDeleteConfigEa();
            if (CollectionUtils.isEmpty(eas)){
                return;
            }
            eas.forEach(ea ->{
                QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
                if (agentConfig == null) {
                    log.info("deleteConfig failed agentConfig=null ea:{}", ea);
                    return ;
                }
                List<QywxAddFanQrCodeEntity> codeEntities = qywxAddFanQrCodeDAO.queryDeleteConfig(ea);
                if (CollectionUtils.isEmpty(codeEntities)) {
                    return ;
                }
                String accessToken = qywxManager.getAccessToken(ea);
                if(org.apache.commons.lang.StringUtils.isBlank(accessToken)){
                    log.info("deleteConfig failed accessToken=null ea:{}", ea);
                    return ;
                }
                for (QywxAddFanQrCodeEntity codeEntity : codeEntities) {
                    if(qywxManager.detelteContactMe(accessToken,codeEntity.getConfigId())){
                        qywxAddFanQrCodeDAO.deletePosterConfigById(codeEntity.getId(),codeEntity.getEa());
                    }
                }

            });
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<List<EmployeeOwnerResult>> queryAllQywxEmployeeBaseInfo(String ea, Integer status, Integer fsUserId) {
        List<EmployeeOwnerResult> result = Lists.newArrayList();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        String accessToken = qywxManager.getAccessToken(ea);
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, false);
        if (CollectionUtils.isNotEmpty(staffInfoList)){
            staffInfoList.forEach(staffInfo -> {
                if (status == null){
                    EmployeeOwnerResult employeeOwnerResult = new EmployeeOwnerResult();
                    employeeOwnerResult.setEmployeeName(staffInfo.getName());
                    employeeOwnerResult.setEmployeeUserId(staffInfo.getUserId());
                    result.add(employeeOwnerResult);
                }else {
                    if (status == staffInfo.getStatus()){
                        EmployeeOwnerResult employeeOwnerResult = new EmployeeOwnerResult();
                        employeeOwnerResult.setEmployeeName(staffInfo.getName());
                        employeeOwnerResult.setEmployeeUserId(staffInfo.getUserId());
                        result.add(employeeOwnerResult);
                    }
                }
            });
        }

        return Result.newSuccess(result);
    }

    public List<ObjectData> getWechatCustomersCountByQrCode(String ea, List<String> qrCodeIds){
        if (CollectionUtils.isEmpty(qrCodeIds)) {
            return null;
        }

        PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
        PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
        queryRelation.addFilter(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName(), com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN, qrCodeIds);
        queryRelation.addFilter(CrmWechatWorkExternalUserFieldEnum.IS_DELETED.getFieldName(), com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN, Lists.newArrayList("false"));
        queryRelationFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        queryRelationFilterArg.setSelectFields(Lists.newArrayList(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName()));
        queryRelationFilterArg.setQuery(queryRelation);

        int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
        if (totalRelationCount <= 0){
            return null;
        }

        int currentRelationCount = 0;
        String lastRelationId = null;
        List<ObjectData> objectDataList = Lists.newArrayList();
        while (currentRelationCount < totalRelationCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 2000);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentRelationCount += tempSize;
            lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }

        return objectDataList;
    }



    public String getAvatarFormCrmData(ObjectData objectData, String ea) {
        // 头像
        List<Map<String, Object>> avatarMap = GsonUtil
            .fromJson(GsonUtil.getGson().toJson(objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName())), new TypeToken<List<Map<String, String>>>() {
            }.getType());
        if (CollectionUtils.isNotEmpty(avatarMap)) {
            return fileV2Manager.getUrlByPath(avatarMap.get(0).get("path") + "." + avatarMap.get(0).get("ext"), ea, false);
        }
        return null;
    }

    @Data
    @AllArgsConstructor
    public static class ActionAndFollowUpContainer {

        private Integer actionAndFollowUpType;

        private String id;

        private Long time;

    }
}