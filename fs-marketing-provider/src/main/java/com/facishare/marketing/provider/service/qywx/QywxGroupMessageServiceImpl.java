package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.api.arg.qywx.SendResultScheduleArg;
import com.facishare.marketing.api.service.QywxGroupMessageService;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dto.qywx.SendGroupResultDTO;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

@Service("qywxGroupMessageService")
@Slf4j
public class QywxGroupMessageServiceImpl implements QywxGroupMessageService{
    @Autowired
    private GroupSendMessageManager groupSendMessageManager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    private Long scanAllTaskTime; //当天全量调度时间

    @Override
    public void qywxMessageResultSchedule() {
        //每天全量调度一次所有任务，5分钟调度一次当天任务
        boolean scanAllTask = false;
        Long currentTime = System.currentTimeMillis();
        if (scanAllTaskTime == null){
            scanAllTaskTime = System.currentTimeMillis();
            scanAllTask = true;
        }else {
            if (checkSameDayByTime(currentTime, scanAllTaskTime)){
                scanAllTask = false;
            }else {
                scanAllTask = true;
                scanAllTaskTime = System.currentTimeMillis();
            }
        }

        groupSendMessageManager.getGroupMsgResultSchedule(scanAllTask);
    }

    @Override
    public void groupSendMsgSchedule(String ea) {
        groupSendMessageManager.groupSendMsgSchedule(ea);
    }

    public void groupSendMsgById(String id) {
        groupSendMessageManager.groupSendMsgById(id);
    }

    @Override
    public void handlerSopQywxMsgTaskResult(SendResultScheduleArg arg) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(arg.getEa()) || appVersionManager.getCurrentAppVersion(arg.getEa()) == null) {
            log.info("QywxGroupMessageServiceImpl.handlerSopQywxMsgTaskResult failed enterprise stop or license expire ea:{}", arg.getEa());
            return;
        }
        ThreadPoolUtils.execute(() -> groupSendMessageManager.handlerSopQywxMsgTaskResult(arg.getMsgid(), arg.getEa()), ThreadPoolUtils.ThreadPoolTypeEnums.SOP_QYWX_MSG);
    }

    @Override
    public void handSopQywxGroupMsgTaskResult(SendResultScheduleArg arg) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(arg.getEa()) || appVersionManager.getCurrentAppVersion(arg.getEa()) == null) {
            log.info("QywxGroupMessageServiceImpl.handSopQywxGroupMsgTaskResult failed enterprise stop or license expire ea:{}", arg.getEa());
            return;
        }
        ThreadPoolUtils.execute(() -> groupSendMessageManager.handSopQywxGroupMsgTaskResult(arg.getEa(), arg.getMsgid(), null), ThreadPoolUtils.ThreadPoolTypeEnums.SOP_QYWX_MSG);
    }

    private boolean checkSameDayByTime(long time1, long time2){
        Date date1 = new Date(time1);
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Date date2 = new Date(time2);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1= cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);
        return day1 == day2;
    }
}
