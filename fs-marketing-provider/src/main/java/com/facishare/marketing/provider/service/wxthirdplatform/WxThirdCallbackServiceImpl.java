package com.facishare.marketing.provider.service.wxthirdplatform;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.UnAuthWxAppArg;
import com.facishare.marketing.api.service.SettingService;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdCallbackService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.XmlParser;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatEaBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatThirdPlatformConfigDao;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatThirdPlatformConfigEntity;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager;
import com.facishare.marketing.provider.util.qywx.CryptHelper;
import com.facishare.wechat.proxy.common.aes.Decrypter;
import com.facishare.wechat.proxy.common.utils.BeanUtil;
import com.facishare.wechat.proxy.outer.model.auth.AuthorizationInfo;
import com.fxiaoke.wechatrestapi.arg.CustomTextSendArg;
import com.fxiaoke.wechatrestapi.arg.GetAuthorizationInfoByAuthCodeArg;
import com.fxiaoke.wechatrestapi.common.constants.MsgTypeConstants;
import com.fxiaoke.wechatrestapi.data.GetAuthorizationInfoByAuthCodeResult;
import com.fxiaoke.wechatrestapi.data.MassText;
import com.fxiaoke.wechatrestapi.service.CustomServiceRestService;
import com.fxiaoke.wechatrestapi.service.WechatAuthRestService;
import com.google.gson.reflect.TypeToken;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.FormBody.Builder;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("wxThirdCallbackService")
@Slf4j
public class WxThirdCallbackServiceImpl implements WxThirdCallbackService {
    @Autowired
    private WechatThirdPlatformConfigDao wechatThirdPlatformConfigDao;
    private static final int VERIFY_TICKET_DURATION_SECONDS = 3600 * 24 * 3;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private SettingService settingService;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private WechatAuthRestService wechatAuthRestService;
    @Autowired
    private CustomServiceRestService customServiceRestService;
    @Autowired
    private WechatEaBindDao wechatEaBindDao;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private WxCloudRestManager wxCloudRestManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Override
    public Result<String> authCallback(String platformId, String msgSignature, String encryptType, String timestamp, String nonce, String signature, String encryptXmlStr) {
        WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(platformId);
        EncryptXml encryptXml = XmlParser.fromXml(encryptXmlStr, EncryptXml.class);
        String decryptMsg = CryptHelper
            .decryptMsg(wechatThirdPlatformConfig.getComponentToken(), Base64.decodeBase64(wechatThirdPlatformConfig.getComponentAesKey()), msgSignature, timestamp, nonce, encryptXml.getEncrypt());
        AuthEvent authEvent = XmlParser.fromXml(decryptMsg, AuthEvent.class);
        log.info("authCallback event platformId:{} data:{}", platformId, authEvent);
        if ("component_verify_ticket".equals(authEvent.getInfoType())) {
            wechatThirdPlatformConfigDao.updateVerifyTicket(platformId, authEvent.getComponentVerifyTicket(), DateUtil.getUnixTimeByDurationSecondsFromNow(VERIFY_TICKET_DURATION_SECONDS));
        } else {
            // 更新授权、取消授权 转发至专属云，授权成功不做操作
            if (!"authorized".equals(authEvent.getInfoType())) {
                String ea = wechatEaBindDao.getEaByAppId(authEvent.getAuthorizerAppid());
                if (appVersionManager.isVpnDisconnectCloud()) {
                    wechatAccountManager.handleUnauthorizedEventInVpnDisconnectCloud(platformId, authEvent.getAuthorizerAppid(), authEvent.getInfoType());
                }else {
                    //纷享云转发到其他各云
                    String url = wxCloudRestManager.getCloudDispatchUrl(ea) + "/inner/wxThirdCallback/cloudCallback";
                    Map<String, String> header = new HashMap<>();
                    header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
                    FormBody requestBody = new Builder().add("platformId", platformId).add("authEventStr", GsonUtil.toJson(authEvent)).build();
                    httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, url, new TypeToken<String>() {
                    }, header);
                }
            }
        }
        return Result.newSuccess("success");
    }

    public Result<String> cloudCallback(String platformId, String authEventStr) {
        AuthEvent authEvent = JSON.parseObject(authEventStr, AuthEvent.class);
        log.info("cloudCallback - authEvent:" + authEvent);
        try {
            String authorizerAppId = authEvent.getAuthorizerAppid();
            if ("unauthorized".equals(authEvent.getInfoType()) && !WxAppInfoEnum.isSystemApp(authorizerAppId)) {
                wechatAccountManager.listEaByPlatformIdAndWxAppId(platformId, authorizerAppId).forEach(ea -> {
                    UnAuthWxAppArg unAuthWxAppArg = new UnAuthWxAppArg();
                    unAuthWxAppArg.setPlatformId(platformId);
                    unAuthWxAppArg.setWxAppId(authorizerAppId);
                    settingService.unAuthWxAppId(ea, unAuthWxAppArg);
                });
            }
        } catch (Exception e) {
            log.warn("cloudCallback error - " + authEvent, e);
        }
        return Result.newSuccess("success");
    }

    @Override
    public Result<String> eventCallback(String wxAppId, String msgSignature, String timestamp, String nonce, String encryptXml) {
        WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(MKThirdPlatformConstants.PLATFORM_ID);
        String message = Decrypter.decrypt(wechatThirdPlatformConfig.getComponentToken(), wechatThirdPlatformConfig.getComponentAesKey(), msgSignature, timestamp, nonce,
                XmlParser.fromXml(encryptXml, com.facishare.wechat.proxy.common.xml.EncryptXml.class));
        ChatXml chatXml = XmlParser.fromXml(message, ChatXml.class);
        if ("wxd101a85aa106f53e".equals(wxAppId)) {
            String content = chatXml.getContent();
            if (content.contains("QUERY_AUTH_CODE")) {
                String queryAuthCode = content.replace("QUERY_AUTH_CODE:", "");
                content = queryAuthCode + "_from_api";
                String componentAccessToken = wechatThirdPlatformManager.getThirdPlatformAccessToken(MKThirdPlatformConstants.PLATFORM_ID);
                GetAuthorizationInfoByAuthCodeArg arg = new GetAuthorizationInfoByAuthCodeArg();
                arg.setComponentAppId(wechatThirdPlatformManager.getComponentAppId(MKThirdPlatformConstants.PLATFORM_ID));
                arg.setAuthorizationCode(queryAuthCode);
                GetAuthorizationInfoByAuthCodeResult authorizationInforesult = wechatAuthRestService.getAuthorizationInfoByAuthCode(componentAccessToken, arg);
                if (authorizationInforesult.isSuccess()) {
                    AuthorizationInfo authorizationInfo = BeanUtil.copyByGson(authorizationInforesult.getAuthorizationInfo(), AuthorizationInfo.class);
                    CustomTextSendArg customTextSendArg = new CustomTextSendArg();
                    customTextSendArg.setText(new MassText(content));
                    customTextSendArg.setMsgType(MsgTypeConstants.TEXT);
                    customTextSendArg.setTouser(chatXml.getFromUserName());
                    customServiceRestService.send(authorizationInfo.getAuthorizerAccessToken(), customTextSendArg);
                }
            }
        }
        return Result.newSuccess("success");
    }

    @Getter
    @ToString
    @XStreamAlias("xml")
    public static class EncryptXml {
        @XStreamAlias("Encrypt")
        public String encrypt;
        /**
         * 有可能为corpId，或者suiteId
         */
        @XStreamAlias("ToUserName")
        public String to;
    }

    @Getter
    @ToString
    @XStreamAlias("xml")
    @Setter
    public static class AuthEvent {
        @XStreamAlias("AppId")
        private String appId;
        @XStreamAlias("CreateTime")
        private String createTime;
        @XStreamAlias("InfoType")
        private String infoType;
        @XStreamAlias("ComponentVerifyTicket")
        private String componentVerifyTicket;
        @XStreamAlias("AuthorizerAppid")
        private String authorizerAppid;
        @XStreamAlias("AuthorizationCode")
        private String authorizationCode;
    }

    @Data
    @XStreamAlias("xml")
    public class ChatXml implements Serializable {
        /**
         * 开发者微信号
         */
        @XStreamAlias("ToUserName")
        private String toUserName;
        /**
         * 发送方帐号（openID）
         */
        @XStreamAlias("FromUserName")
        private String fromUserName;
        /**
         * 消息创建时间 （整型）
         */
        @XStreamAlias("CreateTime")
        private long createTime;
        /**
         * text,image，
         */
        @XStreamAlias("MsgType")
        private String msgType;
        /**
         * 文本消息内容
         */
        @XStreamAlias("Content")
        private String content;
        /**
         * 消息id，64位整型
         */
        @XStreamAlias("MsgId")
        private String msgId;
        /**
         * 图片消息 图片链接（由系统生成）
         */
        @XStreamAlias("PicUrl")
        private String picUrl;
        /**
         * 图片消息媒体id/视频消息媒体id，可以调用多媒体文件下载接口拉取数据
         */
        @XStreamAlias("MediaId")
        private String mediaId;
        /* ******图片消息End********/
        /**
         * 语音格式，如amr，speex等
         */
        @XStreamAlias("Format")
        private String format;
        /**
         * 语音识别结果，UTF8编码
         */
        @XStreamAlias("Recognition")
        private String recognition;
        /**
         * 视频消息缩略图的媒体id，可以调用多媒体文件下载接口拉取数据
         */
        @XStreamAlias("ThumbMediaId")
        private String thumbMediaId;
        /**
         * 地理位置维度
         */
        @XStreamAlias("Location_X")
        private String locationX;
        /**
         * 地理位置经度
         */
        @XStreamAlias("Location_Y")
        private String locationY;
        /**
         * 地图缩放大小
         */
        @XStreamAlias("Scale")
        private String scale;
        /**
         * 地理位置信息
         */
        @XStreamAlias("Label")
        private String label;
        /***
         * 消息标题
         */
        private String title;
        /**
         * 消息描述
         */
        private String description;
        /**
         * 消息链接URL
         */
        private String url;
    }
}
