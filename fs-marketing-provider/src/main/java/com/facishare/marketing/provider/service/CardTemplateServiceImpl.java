package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.minAppCardNavbar.GetMiniappCardSettingArg;
import com.facishare.marketing.api.result.AddResult;
import com.facishare.marketing.api.result.GetCardCommonSettingResult;
import com.facishare.marketing.api.result.GetCardTemplateResult;
import com.facishare.marketing.api.result.ListCardTemplateResult;
import com.facishare.marketing.api.result.minAppCardNavbar.GetMiniappCardSettingResult;
import com.facishare.marketing.api.service.CardTemplateService;
import com.facishare.marketing.api.service.CustomizeMiniAppCardNavbarService;
import com.facishare.marketing.api.vo.*;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.card.CardTemplateDao;
import com.facishare.marketing.provider.dao.card.CardTemplateDepartmentDao;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.entity.CardTemplateDepartmentEntity;
import com.facishare.marketing.provider.entity.CardTemplateEntity;
import com.facishare.marketing.provider.manager.AuthManager;
import com.facishare.marketing.provider.manager.CardManager;
import com.facishare.marketing.provider.manager.cardtemplate.CardTemplateManager;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/5/25
 **/
@Service("cardTemplateService")
@Slf4j
public class CardTemplateServiceImpl implements CardTemplateService {

    @Autowired
    private CardTemplateDao cardTemplateDao;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private CardTemplateDepartmentDao cardTemplateDepartmentDao;

    @Autowired
    private AuthManager authManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private CardTemplateManager cardTemplateManager;

    @Autowired
    private CardManager cardManager;

    @Autowired
    private CustomizeMiniAppCardNavbarService customizeMiniAppCardNavbarService;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Override
    public Result<AddResult> createOrUpdate(CreateOrUpdateCardTemplateVo vo) {
        if (vo.getId() == null) {
            // 新增
            String cardTemplateId = UUIDUtil.getUUID();
            CardTemplateEntity entity = new CardTemplateEntity();
            entity.setId(cardTemplateId);
            entity.setName(vo.getName());
            entity.setEa(vo.getEa());
            entity.setIsDefault(0);
            cardTemplateDao.insert(entity);
            // 初始化相关数据
            cardTemplateManager.initCustomizeMiniAppCardSetting(vo.getEa(), cardTemplateId);
            cardTemplateManager.initCustomizeMiniAppCardNavbar(vo.getEa(), cardTemplateId);
            AddResult addResult = new AddResult();
            addResult.setId(cardTemplateId);
            return Result.newSuccess(addResult);
        } else {
            // 更新
            String id = vo.getId();
            CardTemplateEntity cardTemplateEntity = cardTemplateDao.getById(id);
            if (cardTemplateEntity == null) {
                return Result.newError(SHErrorCode.NO_DATA);
            }
            cardTemplateEntity.setName(vo.getName());
            cardTemplateDao.update(cardTemplateEntity);
            AddResult addResult = new AddResult();
            addResult.setId(id);
            return Result.newSuccess(addResult);
        }
    }

    @Override
    public Result<List<ListCardTemplateResult>> list(BaseVO vo) {
        String ea = vo.getEa();
        List<CardTemplateEntity> cardTemplateEntities = cardTemplateDao.getByEa(vo.getEa());
        if (CollectionUtils.isEmpty(cardTemplateEntities)) {
            return Result.newSuccess();
        }

        List<String> cardTemplateIds = cardTemplateEntities.stream().map(CardTemplateEntity::getId).collect(Collectors.toList());

        // 批量查询部门映射
        List<CardTemplateDepartmentEntity> cardTemplateDepartmentEntities = cardTemplateDepartmentDao.batchGetByCardTplIds(cardTemplateIds);
        Map<String, List<CardTemplateDepartmentEntity>> departmentRelationEntityMap = Maps.newHashMap();
        Map<Integer, DepartmentDto> departmentMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cardTemplateDepartmentEntities)) {
            Set<Integer> departmentIds = cardTemplateDepartmentEntities.stream().map(CardTemplateDepartmentEntity::getDepartmentId).collect(Collectors.toSet());
            List<DepartmentDto> departmentDtos = authManager.batchGetByDepartmentIds(eieaConverter.enterpriseAccountToId(ea), Lists.newArrayList(departmentIds));
            departmentMap = departmentDtos.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, e -> e, (v1, v2) -> v1)); // 部门映射
            departmentRelationEntityMap = cardTemplateDepartmentEntities.stream().collect(Collectors.groupingBy(CardTemplateDepartmentEntity::getCardTemplateId));// 模板id和部门集合的映射
        }

        // 填充部门
        List<ListCardTemplateResult> results = Lists.newArrayList();
        for (CardTemplateEntity cardTemplateEntity : cardTemplateEntities) {
            String cardTemplateEntityId = cardTemplateEntity.getId();
            ListCardTemplateResult templateResult = new ListCardTemplateResult();
            templateResult.setId(cardTemplateEntity.getId());
            templateResult.setName(cardTemplateEntity.getName());
            templateResult.setCreateTime(cardTemplateEntity.getCreateTime().getTime());
            templateResult.setUpdateTime(cardTemplateEntity.getUpdateTime().getTime());
            templateResult.setIsDefault(cardTemplateEntity.getIsDefault());
            // 填充部门
            if (cardTemplateEntity.getIsDefault() == 1) {
                templateResult.setDepartmentInfoList(Lists.newArrayList());
            } else {
                List<CardTemplateDepartmentEntity> departmentRelationEntities = departmentRelationEntityMap.getOrDefault(cardTemplateEntityId, Lists.newArrayList());
                List<ListCardTemplateResult.DepartmentInfo> departmentInfoList = Lists.newArrayList();
                if (departmentRelationEntities.stream().anyMatch(entity -> Objects.equals(entity.getDepartmentId(), AuthManager.defaultAllDepartment))) {
                    ListCardTemplateResult.DepartmentInfo departmentInfo = new ListCardTemplateResult.DepartmentInfo();
                    departmentInfo.setId(AuthManager.defaultAllDepartment);
                    departmentInfo.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
                    departmentInfoList.add(departmentInfo);
                } else {
                    for (CardTemplateDepartmentEntity entity : departmentRelationEntities) {
                        DepartmentDto departmentDto = departmentMap.get(entity.getDepartmentId());
                        if (departmentDto != null) {
                            ListCardTemplateResult.DepartmentInfo departmentInfo = new ListCardTemplateResult.DepartmentInfo();
                            departmentInfo.setId(departmentDto.getDepartmentId());
                            departmentInfo.setName(departmentDto.getName());
                            departmentInfoList.add(departmentInfo);
                        }
                    }
                }
                templateResult.setDepartmentInfoList(departmentInfoList);
            }
            // 查询设置信息
            GetMiniappCardSettingArg getMiniappCardSettingArg = new GetMiniappCardSettingArg();
            getMiniappCardSettingArg.setEa(vo.getEa());
            getMiniappCardSettingArg.setCardTemplateId(cardTemplateEntityId);
            Result<GetMiniappCardSettingResult> miniappCardSetting = customizeMiniAppCardNavbarService.getMiniappCardSetting(getMiniappCardSettingArg);
            if (miniappCardSetting.isSuccess()) {
                templateResult.setMiniappCardSetting(miniappCardSetting.getData());
            }
            // 统计人数
            templateResult.setSuitCount(cardDAO.getTotalByCardTplId(cardTemplateEntityId));
            //templateResult.setOpenCount(cardDAO.getTotalByCardTplId(cardTemplateEntityId));

            results.add(templateResult);
        }
        return Result.newSuccess(results);
    }

    @Override
    public Result<GetCardTemplateResult> get(GetCardTemplateVo vo) {
        String id = vo.getId();
        String ea = vo.getEa();
        CardTemplateEntity cardTemplateEntity = cardTemplateDao.getById(id);
        if (cardTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        GetCardTemplateResult result = new GetCardTemplateResult();
        result.setId(cardTemplateEntity.getId());
        result.setName(cardTemplateEntity.getName());
        result.setCreateTime(cardTemplateEntity.getCreateTime().getTime());
        result.setUpdateTime(cardTemplateEntity.getUpdateTime().getTime());

        if (cardTemplateEntity.getIsDefault() == 1) {
            // 默认模板。不返回适用范围
            result.setDepartmentInfos(Lists.newArrayList());
        } else {
            // 查询适用范围
            List<CardTemplateDepartmentEntity> departmentRelationEntityList = cardTemplateDepartmentDao.getByCardTplId(id);
            List<Integer> departmentIds = departmentRelationEntityList.stream().map(CardTemplateDepartmentEntity::getDepartmentId).collect(Collectors.toList());
            List<DepartmentDto> departmentDtos = authManager.batchGetByDepartmentIds(eieaConverter.enterpriseAccountToId(ea), departmentIds);
            if (CollectionUtils.isNotEmpty(departmentDtos)) {
                List<GetCardTemplateResult.DepartmentInfo> departmentInfos = departmentDtos.stream().map(departmentDto -> {
                    GetCardTemplateResult.DepartmentInfo departmentInfo = new GetCardTemplateResult.DepartmentInfo();
                    departmentInfo.setId(departmentDto.getDepartmentId());
                    departmentInfo.setName(departmentDto.getName());
                    return departmentInfo;
                }).collect(Collectors.toList());
                result.setDepartmentInfos(departmentInfos);
            } else {
                result.setDepartmentInfos(Lists.newArrayList());
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> saveSuitable(SaveCardTemplateSuitableVo vo) {
        String id = vo.getId();
        CardTemplateEntity cardTemplateEntity = cardTemplateDao.getById(id);
        if (cardTemplateEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        // 适用部门处理
        cardTemplateDepartmentDao.deleteByCardTplId(id);
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
            List<CardTemplateDepartmentEntity> departmentEntities = vo.getDepartmentIds().stream().map(departmentId -> {
                CardTemplateDepartmentEntity cardTemplateDepartmentEntity = new CardTemplateDepartmentEntity();
                cardTemplateDepartmentEntity.setId(UUIDUtil.getUUID());
                cardTemplateDepartmentEntity.setCardTemplateId(id);
                cardTemplateDepartmentEntity.setDepartmentId(departmentId);
                cardTemplateDepartmentEntity.setEa(vo.getEa());
                return cardTemplateDepartmentEntity;
            }).collect(Collectors.toList());
            cardTemplateDepartmentDao.batchInsert(departmentEntities);
        }

        // 更新名片
        cardTemplateDao.updateTime(id);

        // 触发名片更新
        cardManager.updateAllCard(vo.getEa(), 1);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> delete(DeleteCardTemplateVo vo) {
        CardTemplateEntity cardTemplateEntity = cardTemplateDao.getById(vo.getId());
        if (cardTemplateEntity != null) {
            ctaRelationDaoManager.deleteCtaRelation(cardTemplateEntity.getEa(), ObjectTypeEnum.PRODUCT.getType(), Lists.newArrayList(vo.getId()));
        }
        return cardTemplateManager.deleteCardTemplate(vo.getId());
    }

    @Override
    public Result<List<GetCardCommonSettingResult>> getCardCommonSetting(GetCardCommonSettingVo vo) {
        return cardTemplateManager.getCardCommonSetting(vo.getEa(), vo.getCardTemplateId());
    }

    @Override
    public Result updateCardCommonSetting(UpdateCardCommonSettingVo vo) {
        return cardTemplateManager.updateCardCommonSetting(vo);
    }
}