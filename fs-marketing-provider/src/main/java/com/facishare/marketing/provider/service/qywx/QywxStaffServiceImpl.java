package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Sets;
import com.facishare.marketing.api.arg.qywx.staff.*;
import com.facishare.marketing.api.result.ListEmployeeResult;
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult;
import com.facishare.marketing.api.result.qywx.staff.*;
import com.facishare.marketing.api.service.qywx.QyWxDepartmentService;
import com.facishare.marketing.api.service.qywx.QywxStaffService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.digitalHumans.DigitalHumansStatusEnum;
import com.facishare.marketing.common.enums.qywx.AccountActivateStatusEnum;
import com.facishare.marketing.common.enums.qywx.UserBindCrmStatusEnum;
import com.facishare.marketing.common.enums.qywx.UserCardOpenStatusEnum;
import com.facishare.marketing.common.exception.BusinessException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.FSBindDAO;
import com.facishare.marketing.provider.dao.UserDAO;
import com.facishare.marketing.provider.dao.digitalHumans.DigitalHumansDAO;
import com.facishare.marketing.provider.dao.qywx.*;
import com.facishare.marketing.provider.entity.digitalHumans.DigitalHumansEntity;
import com.facishare.marketing.provider.entity.qywx.*;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult;
import com.facishare.marketing.provider.innerResult.qywx.ListActivatedAccountResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatFriendsRecordObjDescribeManager;
import com.facishare.marketing.provider.manager.digitalHumans.DigitalHumansManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager.CardUserInfo;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager.CardUserStatisticContainer;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import java.text.NumberFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.facishare.marketing.provider.manager.AuthManager.defaultAllDepartment;

/**
 * Created  By zhoux 2020/04/10
 **/
@Slf4j
@Service("qywxStaffService")
public class QywxStaffServiceImpl implements QywxStaffService {

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private QywxMiniAppMessageManager qywxMiniAppMessageManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private ResetQywxAddressBookManager resetQywxAddressBookManager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private QywxActivatedAccountDAO qywxActivatedAccountDAO;

    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;

    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;

    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private QyWxDepartmentService qyWxDepartmentService;


    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;

    @Autowired
    private PushSessionManager pushSessionManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private DigitalHumansManager digitalHumansManager;

    @Autowired
    private DigitalHumansDAO digitalHumansDAO;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Autowired
    private QywxDepartmentManager qywxDepartmentManager;

    private final static List<I18nKeyEnum> TITLE_LIST = new ArrayList<>();
    static {
        TITLE_LIST.add(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70);
        TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_271);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_140);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_141);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_142);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_143);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_144);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_145);
        TITLE_LIST.add(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_146);
    }

    @Override
    @FilterLog
    public Result<List<QueryQywxStaffResult>> queryQywxStaff(QueryQywxStaffaArg arg) {
        if (BooleanUtils.isTrue(arg.getWarmUpData())) {
            ThreadPoolUtils.execute(() -> {
                queryQywxStaffInTime(arg);
                initQywxActivatedAccount(Lists.newArrayList(arg.getFsEa()));
                }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            return Result.newSuccess();
        }
        return queryQywxStaffFromLocal(arg);
    }
    @Override
    public Result<List<QueryQywxStaffResult>> queryMiniAppQywxStaff(QueryQywxStaffaArg arg) {
        if (BooleanUtils.isTrue(arg.getWarmUpData())) {
            ThreadPoolUtils.execute(() -> queryMiniAppQywxStaffInTime(arg), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            return Result.newSuccess();
        }
        return queryQywxStaffInTime(arg);
    }

    private Result<List<QueryQywxStaffResult>> queryQywxStaffFromLocal(QueryQywxStaffaArg arg){
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaffFormDB(arg.getFsEa());
        if (CollectionUtils.isEmpty(staffInfoList)){
            return Result.newSuccess(Lists.newArrayList());
        }
        //权限隔离
        if(dataPermissionManager.getNewDataPermissionSetting(arg.getFsEa())){
            List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(arg.getFsEa(), arg.getFsUserId(),true);
            if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                return Result.newSuccess(Lists.newArrayList());
            }
            if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                qywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds.stream().map(o -> o-QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                List<Integer> finalQywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds;
                staffInfoList = staffInfoList.stream().filter(staffInfo -> CollectionUtils.containsAny(staffInfo.getDepartment(), finalQywxAccessibleDepartmentIds)).collect(Collectors.toList());
            }
        }
        List<QueryQywxStaffResult> resultList = BeanUtil.copy(staffInfoList, QueryQywxStaffResult.class);
        List<String> qyUserIds = staffInfoList.stream().map(DepartmentStaffResult.StaffInfo::getUserId).collect(Collectors.toList());
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxUserManager.batchGetVirtualUserByQyUserIds(arg.getFsEa(), qyUserIds);
        if (CollectionUtils.isEmpty(qywxVirtualFsUserEntityList)){
            return Result.newSuccess(Lists.newArrayList());
        }
        Map<String, Integer> qyUserIdFsUserIdMap = qywxVirtualFsUserEntityList.stream().collect(Collectors.toMap(QywxVirtualFsUserEntity::getQyUserId, QywxVirtualFsUserEntity::getUserId, (v1, v2) -> v2));
        for (QueryQywxStaffResult queryQywxStaffResult : resultList) {
            queryQywxStaffResult.setAvatar(fileV2Manager.replaceUrlToHttps(queryQywxStaffResult.getAvatar()));
            queryQywxStaffResult.setFsUserId(qyUserIdFsUserIdMap.get(queryQywxStaffResult.getUserId()));
        }
        return Result.newSuccess(resultList);
    }

    @FilterLog
    public Result<List<QueryQywxStaffResult>> queryQywxStaffInTime(QueryQywxStaffaArg arg) {
        String ea = arg.getFsEa();
        String lockKey = "MARKETING_SYNC_QYWX_ADDRESS_BOOK_" + ea;
        long start = System.currentTimeMillis();
        try {
            boolean lock = redisManager.lock(lockKey, 60 * 10);
            if (!lock) {
                // 没拿到锁
                log.info("sync qywx addressBook is running, ea:{}", ea);
                return Result.newSuccess(Lists.newArrayList());
            }
            List<QueryQywxStaffResult> resultList=null;
            // 获取accessToken
            QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getFsEa());
            List<QywxCustomerAppInfoEntity> appInfoEntities =
                    qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(),
                            agentConfig.getEa());
            if (agentConfig == null) {
                log.warn("QywxStaffServiceImpl.queryQywxStaff is null arg:{}", arg);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            if (agentConfig.getSecret()!= null||appInfoEntities.size()!=0) {

                String accessToken = qywxManager.getAccessToken(arg.getFsEa());
                if (StringUtils.isBlank(accessToken)) {
                    log.warn("QywxStaffServiceImpl.queryQywxStaff accessToken is null arg:{}", arg);
                    return null;
                }
                // 获取全部员工
                List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(arg.getFsEa(), accessToken, false, BooleanUtils.isTrue(arg.getWarmUpData()));
                resultList = BeanUtil.copy(staffInfoList, QueryQywxStaffResult.class);
                for (QueryQywxStaffResult queryQywxStaffResult : resultList) {
                    queryQywxStaffResult.setAvatar(fileV2Manager.replaceUrlToHttps(queryQywxStaffResult.getAvatar()));
                    queryQywxStaffResult.setFsUserId(qywxUserManager.getUserIdByQyWxInfo(arg.getFsEa(), agentConfig.getCorpid(), queryQywxStaffResult.getUserId(), QywxUserConstants.TRY_TIME));
                }
                qywxDepartmentManager.initQywxDepartment(ea);
            }
            log.info("sync qywx addressBook ea:{}, duration:{}", ea, System.currentTimeMillis() - start);
            return Result.newSuccess(resultList);
        } catch (Exception e){
            log.warn("QywxStaffServiceImpl -> queryQywxStaffInTime error e:{}", e);
            return null;
        } finally {
            redisManager.unLock(lockKey);
        }
    }

    public Result<List<QueryQywxStaffResult>> queryMiniAppQywxStaffInTime(QueryQywxStaffaArg arg) {
        // 获取accessToken
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            log.warn("QywxStaffServiceImpl.queryQywxStaff is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        String accessToken = qywxManager.getMiniAppAccessToken(arg.getFsEa());
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxStaffServiceImpl.queryQywxStaff accessToken is null arg:{}", arg);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        // 获取全部员工
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(arg.getFsEa(), accessToken, false, BooleanUtils.isTrue(arg.getWarmUpData()));
        List<QueryQywxStaffResult> resultList = BeanUtil.copy(staffInfoList, QueryQywxStaffResult.class);
        for (QueryQywxStaffResult queryQywxStaffResult : resultList) {
            queryQywxStaffResult.setAvatar(fileV2Manager.replaceUrlToHttps(queryQywxStaffResult.getAvatar()));
            queryQywxStaffResult.setFsUserId(qywxUserManager.getUserIdByQyWxInfo(arg.getFsEa(), agentConfig.getCorpid(), queryQywxStaffResult.getUserId(), QywxUserConstants.TRY_TIME));
        }
        return Result.newSuccess(resultList);
    }



    @Override
    public Result<UserCardStatisticResult> userCardStatistic(UserCardStatisticArg arg) {
        CardUserStatisticContainer cardUserStatisticContainer = qywxUserManager.getOpenCardUserStatistic(arg.getFsEa());
        UserCardStatisticResult userCardStatisticResult = new UserCardStatisticResult();
        userCardStatisticResult.setBindUserCount(0);
        userCardStatisticResult.setUnOpenUserCount(0);
        userCardStatisticResult.setOpenRate("0%");
        if (cardUserStatisticContainer == null) {
            return Result.newSuccess(userCardStatisticResult);
        }
        if (cardUserStatisticContainer.getTotalUser() != 0 && cardUserStatisticContainer.getTotalUser() >= cardUserStatisticContainer.getOpenCardUser()) {
            userCardStatisticResult.setBindUserCount(cardUserStatisticContainer.getOpenCardUser());
            userCardStatisticResult.setUnOpenUserCount(cardUserStatisticContainer.getNotOpenCardUser());
            NumberFormat numberFormat = NumberFormat.getInstance();
            numberFormat.setMaximumFractionDigits(0);
            String openRate = numberFormat.format((float) cardUserStatisticContainer.getOpenCardUser() / (float) cardUserStatisticContainer.getTotalUser() * 100);
            userCardStatisticResult.setOpenRate(openRate + "%");
        }
        return Result.newSuccess(userCardStatisticResult);
    }

    @Override
    public Result<PageResult<QueryUserCardDetailResult>> queryUserCardDetail(QueryUserCardDetailArg arg) {
        List<QueryUserCardDetailResult> queryUserCardDetailResults = Lists.newArrayList();
        PageResult<QueryUserCardDetailResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(queryUserCardDetailResults);
        pageResult.setTotalCount(0);
        CardUserStatisticContainer cardUserStatisticContainer = qywxUserManager.getOpenCardUserDetail(arg.getFsEa());
        if (cardUserStatisticContainer == null) {
            return Result.newSuccess(pageResult);
        }
        List<CardUserInfo> cardUserInfoList = cardUserStatisticContainer.getCardUserDetailList();
        if (CollectionUtils.isEmpty(cardUserInfoList)) {
            return Result.newSuccess(pageResult);
        }
        if (arg.getIsOpen().equals(UserCardOpenStatusEnum.OPEN.getStatus())) {
            cardUserInfoList = cardUserInfoList.stream().filter(data -> data.getIsOpen().equals(UserCardOpenStatusEnum.OPEN.getStatus())).collect(Collectors.toList());
        } else if (arg.getIsOpen().equals(UserCardOpenStatusEnum.NOT_OPEN.getStatus())) {
            cardUserInfoList = cardUserInfoList.stream().filter(data -> data.getIsOpen().equals(UserCardOpenStatusEnum.NOT_OPEN.getStatus())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(cardUserInfoList) && StringUtils.isNotEmpty(arg.getEmployeeName())) {
            cardUserInfoList = cardUserInfoList.stream().filter(data -> StringUtils.isNotEmpty(data.getUserName()) && data.getUserName().contains(arg.getEmployeeName())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(cardUserInfoList)) {
            return Result.newSuccess(pageResult);
        }
        queryUserCardDetailResults = BeanUtil.copy(cardUserInfoList, QueryUserCardDetailResult.class);
        pageResult.setTotalCount(queryUserCardDetailResults.size());
        // 手动分页
        PageUtil<QueryUserCardDetailResult> pageUtil = new PageUtil<>(queryUserCardDetailResults, arg.getPageSize());
        pageResult.setResult(pageUtil.getPagedList(arg.getPageNum()));

        if(digitalHumansManager.getDigitalHumansSetting(arg.getFsEa()) && CollectionUtils.isNotEmpty(pageResult.getResult())){
            List<Integer> list = pageResult.getResult().stream().map(QueryUserCardDetailResult::getFsUserId).collect(Collectors.toList());
            List<DigitalHumansEntity> digitalHumansEntities = digitalHumansDAO.queryByEaAndUserIdList(arg.getFsEa(), list, DigitalHumansStatusEnum.CHECK_COMPLETED.getStatus());
            List<Integer> digHumUserIds = digitalHumansEntities.stream().map(DigitalHumansEntity::getFsUserId).collect(Collectors.toList());
            pageResult.getResult().forEach(o->o.setDigitalHumansStatus(digHumUserIds.contains(o.getFsUserId())));
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result sendNoticeInvite(SendNoticeInviteArg arg) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(arg.getFsEa()) || appVersionManager.getCurrentAppVersion(arg.getFsEa()) == null){
            log.info("QywxStaffServiceImpl.sendNoticeInvite failed enterprise stop or license expire ea:{}", arg.getFsEa());
            return Result.newSuccess();
        }

        if (CollectionUtils.isEmpty(arg.getQyUserIds())) {
            // 查询所有未开通的员工
            CardUserStatisticContainer cardUserStatisticContainer = qywxUserManager.getOpenCardUserDetail(arg.getFsEa());
            if (cardUserStatisticContainer == null) {
                return Result.newSuccess();
            }
            List<CardUserInfo> cardUserInfoList = cardUserStatisticContainer.getCardUserDetailList();
            if (CollectionUtils.isEmpty(cardUserInfoList)) {
                return Result.newSuccess();
            }
            arg.setQyUserIds(cardUserInfoList.stream().filter(data -> data.getIsOpen().equals(UserCardOpenStatusEnum.NOT_OPEN.getStatus())).map(CardUserInfo::getUserId).collect(Collectors.toList()));
        }
        boolean sendResult = qywxMiniAppMessageManager.sendCardInviteMessage(arg.getFsEa(), arg.getQyUserIds());
        return sendResult ? Result.newSuccess() : Result.newError(SHErrorCode.QYWX_SEND_MINIAPP_MESSAGE_ERROR);
    }

    @Override
    public Result initQywxAddressBook() {
        ThreadPoolUtils.execute(() -> resetQywxAddressBookManager.initQywxAddressBook("setAll"), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> initQywxActivatedAccount(List<String> eaList) {

        ThreadPoolUtils.execute(() -> {
            List<QywxCustomerAppInfoEntity> appInfoEntityList = qywxCustomerAppInfoDAO.getCorpIdAndEa(eaList);
            List<QywxCorpAgentConfigEntity> qywxCorpAgentConfigEntityList = qywxCorpAgentConfigDAO.queryQywxCorpByEaList(eaList);
            Map<String, Set<String>> eaToCorpIdMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(appInfoEntityList)) {
                for (QywxCustomerAppInfoEntity qywxCustomerAppInfoEntity : appInfoEntityList) {
                    Set<String> set = eaToCorpIdMap.computeIfAbsent(qywxCustomerAppInfoEntity.getEa(), k -> Sets.newHashSet());
                    set.add(qywxCustomerAppInfoEntity.getCorpId());
                }

            }
            if (CollectionUtils.isNotEmpty(qywxCorpAgentConfigEntityList)) {
                for (QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity : qywxCorpAgentConfigEntityList) {
                    Set<String> set = eaToCorpIdMap.computeIfAbsent(qywxCorpAgentConfigEntity.getEa(), k -> Sets.newHashSet());
                    set.add(qywxCorpAgentConfigEntity.getCorpid());
                }
            }
            if (eaToCorpIdMap.isEmpty()) {
                return;
            }
            log.info("initQywxActivatedAccount size:[{}], data: {}", eaToCorpIdMap.size(), eaToCorpIdMap);

            for (Map.Entry<String, Set<String>> entrySet : eaToCorpIdMap.entrySet()) {
                try {
                    String ea = entrySet.getKey();
                    long beginTime = System.currentTimeMillis();
                    if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
                        log.info("企业已经停止使用, ea:[{}]", ea);
                        continue;
                    }
                    log.info("开始刷取账号列表,ea:[{}]", ea);
                    qywxActivatedAccountDAO.deleteByEa(ea);
                    for (String corpId : entrySet.getValue()) {
                        boolean hasMore = true;
                        String nextCursor = null;
                        int singleEaMaxCount = 500000;
                        int totalCount = 0;
                        int insertCount = 0;
                        int updateCount = 0;
                        while(hasMore) {
                            ListActivatedAccountResult result = qywxManager.listActivatedAccount(corpId, nextCursor);
                            if (result.getErrcode() != 0) {
                                if (result.getErrcode() == 2000002) {
                                    // 这里就不要打error日志了
                                    log.info("corpId not found,ea:[{}], corpId:[{}], errorCode:[{}], errorMsg:[{}]", ea, corpId, result.getErrcode(), result.getErrmsg());
                                } else {
                                    log.error("获取企微企业的账号列表异常,ea:[{}], corpId:[{}], errorCode:[{}], errorMsg:[{}]", ea, corpId, result.getErrcode(), result.getErrmsg());
                                }
                                break;
                            }
                            List<ListActivatedAccountResult.ListActivatedAccountDetail> accountList = result.getAccountList();
                            nextCursor = result.getNextCursor();
                            hasMore = result.getHasMore() != null && result.getHasMore() == 1;
                            if (CollectionUtils.isNotEmpty(accountList)) {
                                List<String> userIdList = accountList.stream().map(ListActivatedAccountResult.ListActivatedAccountDetail::getUserId).collect(Collectors.toList());
                                List<QywxActivatedAccountEntity> entityList = qywxActivatedAccountDAO.getByUserIdList(ea, userIdList);
                                Map<String, QywxActivatedAccountEntity> userIdEntityMap = new HashMap<>();
                                if (CollectionUtils.isNotEmpty(entityList)) {
                                    entityList.forEach(e -> userIdEntityMap.put(e.getUserId(), e));
                                }
                                for (ListActivatedAccountResult.ListActivatedAccountDetail accountDetail : accountList) {
                                    QywxActivatedAccountEntity accountEntity = userIdEntityMap.get(accountDetail.getUserId());
                                    if (accountEntity != null) {
                                        qywxActivatedAccountDAO.updateAccountTypeAndTime(ea, accountEntity.getId(), accountDetail.getExpireTime(),
                                                accountDetail.getActiveTime(), accountDetail.getType());
                                        updateCount++;
                                    } else {
                                        accountEntity = new QywxActivatedAccountEntity();
                                        accountEntity.setId(UUIDUtil.getUUID());
                                        accountEntity.setUserId(accountDetail.getUserId());
                                        accountEntity.setAccountType(accountDetail.getType());
                                        accountEntity.setActiveTime(accountDetail.getActiveTime());
                                        accountEntity.setExpireTime(accountDetail.getExpireTime());
                                        accountEntity.setEa(ea);
                                        qywxActivatedAccountDAO.insertIgnore(accountEntity);
                                        insertCount++;
                                    }
                                }
                                totalCount += accountList.size();
                            }
                            log.info("开始刷取账号列表结束,ea:[{}],耗时:[{}],账号总数:[{}],更新总数:[{}],插入总数:[{}]", ea,
                                    System.currentTimeMillis() - beginTime, totalCount, updateCount, insertCount);
                            if (totalCount > singleEaMaxCount) {
                                log.error("当前企业的账号列表数量大于五十万,请确认是否正常,ea:[{}], totalCount:[{}]", ea, totalCount);
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("initQywxActivatedAccount error", e);
                }

            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QueryQywxStaffPageResult>> queryQywxStaffPage(QueryQywxStaffPageArg arg) {
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> allQywxUserId = null;
        PageResult<QueryQywxStaffPageResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        List<QueryQywxStaffPageResult> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);

        Integer openCardStatus = arg.getOpenCardStatus();
        Integer bindCrmStatus = arg.getBindCrmStatus();
        // 查询开通名片或者未开通名片
        if (openCardStatus != null && !openCardStatus.equals(UserCardOpenStatusEnum.ALL.getStatus())) {
            allQywxUserId = qywxAddressBookManager.getAllUserId(arg.getFsEa());
            Map<String, Integer> qyUserIdToFsUserIdMap = qywxUserManager.getOpenCardUserIdMap(arg.getFsEa(), allQywxUserId);
            if (openCardStatus.equals(UserCardOpenStatusEnum.OPEN.getStatus())) {
                // 已开通名片的企业微信userId
                allQywxUserId = Lists.newArrayList(qyUserIdToFsUserIdMap.keySet());
            } else {
                // 减掉已开通名片的企业微信userId
                allQywxUserId.removeAll(Lists.newArrayList(qyUserIdToFsUserIdMap.keySet()));
            }
            if (CollectionUtils.isEmpty(allQywxUserId)) {
                return Result.newSuccess(pageResult);
            }
            arg.setUserIdList(allQywxUserId);
        }
        // 查询绑定CRM账号或者未绑定CRM账号
        if (bindCrmStatus != null && bindCrmStatus != UserBindCrmStatusEnum.ALL.getStatus()) {
            // 为什么不是关联qywx_virtual_fs_user呢，因为qywx_virtual_fs_user会存在同个ea，不同corpId,相同企微userId的情况，会造成数据重复
            if (allQywxUserId == null) {
                allQywxUserId = qywxAddressBookManager.getAllUserId(arg.getFsEa());
            }
            // 获取已经绑定crm绑定的企微userId
            List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.queryBindCrmQyUserId(arg.getFsEa(), allQywxUserId);
            if (qywxVirtualFsUserEntityList == null) {
                qywxVirtualFsUserEntityList = Lists.newArrayList();
            }
            List<String> qyUserIdList = qywxVirtualFsUserEntityList.stream().map(QywxVirtualFsUserEntity::getQyUserId).collect(Collectors.toList());
            if (bindCrmStatus == 1) {
                // 绑定了CRM账户
                allQywxUserId = qyUserIdList;
            } else {
                // 未绑定CRM账户
                allQywxUserId.removeAll(qyUserIdList);
            }
            if (CollectionUtils.isEmpty(allQywxUserId)) {
                return Result.newSuccess(pageResult);
            }
            arg.setUserIdList(allQywxUserId);
        }

        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(arg.getFsEa());
        if(isOpen){
            List<String> staffIds = Lists.newArrayList();
            String accessToken = qywxManager.getAccessToken(arg.getFsEa());
            List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(arg.getFsEa(), accessToken, false, false);
            if(CollectionUtils.isNotEmpty(staffInfoList)){
                List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(arg.getFsEa(), arg.getFsUserId(),true);
                if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                    return Result.newSuccess(pageResult);
                }
                if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                    qywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds.stream().map(o -> o-QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                    List<Integer> finalQywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds;
                    staffIds = staffInfoList.stream().filter(staffInfo -> CollectionUtils.containsAny(staffInfo.getDepartment(), finalQywxAccessibleDepartmentIds)).map(DepartmentStaffResult.StaffInfo::getUserId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(allQywxUserId)) {
                        allQywxUserId = staffIds;
                    }else {
                        allQywxUserId = allQywxUserId.stream().filter(staffIds::contains).collect(Collectors.toList());
                    }
                    if (CollectionUtils.isEmpty(allQywxUserId)) {
                        return Result.newSuccess(pageResult);
                    }
                    arg.setUserIdList(allQywxUserId);
                }
            }
        }
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        boolean needConvert = !qywxManager.isNewInstallAgentApp(arg.getFsEa());
        List<QueryQywxStaffPageDTO> queryQywxStaffPageDTOList;
        if (needConvert) {
            // 中大和北科存在两种userId, 通讯录是明文 激活表是密文, 所以根据激活状态筛选有问题 这里只能单独处理
            queryQywxStaffPageDTOList = queryQywxStaffByConverUserId(arg, allQywxUserId, page);
        } else {
            arg.setUserIdList(allQywxUserId);
            queryQywxStaffPageDTOList = qyWxAddressBookDAO.queryQywxStaffPage(arg, page);
        }
        pageResult.setTotalCount(page.getTotalNum());
        resultList.addAll(buildQywxStaffPageResult(queryQywxStaffPageDTOList, arg));
        return Result.newSuccess(pageResult);
    }

    private List<QueryQywxStaffPageDTO> queryQywxStaffByConverUserId(QueryQywxStaffPageArg arg, List<String> queryUserIdList, Page page) {

        List<QueryQywxStaffPageDTO> queryQywxStaffPageDTOList = Lists.newArrayList();
        List<QywxActivatedAccountEntity> qywxActivatedAccountEntityList = qywxActivatedAccountDAO.queryByEa(arg.getFsEa());
        int activatedStatus = arg.getActivatedStatus() == null ? AccountActivateStatusEnum.ALL.getStatus() : arg.getActivatedStatus() ;
        long now = System.currentTimeMillis();
        // 已激活的用户id
        List<String> activatedUserIdList = qywxActivatedAccountEntityList.stream().filter(e -> e.getExpireTime() != null && e.getExpireTime() * 1000 >= now)
                .map(QywxActivatedAccountEntity::getUserId).collect(Collectors.toList());
        if (activatedStatus == AccountActivateStatusEnum.OPEN.getStatus() && CollectionUtils.isEmpty(activatedUserIdList)) {
            // 找不到已激活的用户 直接返回
            return queryQywxStaffPageDTOList;
        }
        // 根据过期时间筛选的用户id
        List<String> expireTimeFilterUserIdList = Lists.newArrayList();
        if (arg.getExpireBeginTime() != null && arg.getExpireEndTime() != null) {
            expireTimeFilterUserIdList = qywxActivatedAccountEntityList.stream()
                    .filter(e -> e.getExpireTime() != null && e.getExpireTime() * 1000 >= arg.getExpireBeginTime() && e.getExpireTime() * 1000 <= arg.getExpireEndTime())
                    .map(QywxActivatedAccountEntity::getUserId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(expireTimeFilterUserIdList)) {
                // 根据过期时间筛选 查不到 也直接返回
                return queryQywxStaffPageDTOList;
            }
        }
        // 密文 --> 明文
        Map<String, String> map = qywxUserManager.getOpenUserIdToUserIdMap(arg.getFsEa());
        // 明文 --> 密文
        Map<String, String> convertMap = map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v1));
        // 密文转成明文
        activatedUserIdList = activatedUserIdList.stream().map(map::get).filter(Objects::nonNull).collect(Collectors.toList());
        expireTimeFilterUserIdList = expireTimeFilterUserIdList.stream().map(map::get).filter(Objects::nonNull).collect(Collectors.toList());
        // 要查询的用户id
        if (CollectionUtils.isEmpty(queryUserIdList)) {
            queryUserIdList = qywxAddressBookManager.getAllUserId(arg.getFsEa());
        }
        if (activatedStatus == AccountActivateStatusEnum.NOT_OPEN.getStatus()) {
            // 查找未激活的 去掉已激活的  有可能员工表 激活表还没有记录 所以直接做减法
            queryUserIdList.removeAll(activatedUserIdList);
        } else if (activatedStatus == AccountActivateStatusEnum.OPEN.getStatus()) {
            // 激活的用户
            queryUserIdList.retainAll(activatedUserIdList);
        }
        if (CollectionUtils.isNotEmpty(expireTimeFilterUserIdList)) {
            // 如果有筛选时间 这里做个交集
            queryUserIdList.retainAll(expireTimeFilterUserIdList);
        }
        if (CollectionUtils.isEmpty(queryUserIdList)) {
            return queryQywxStaffPageDTOList;
        }
        arg.setUserIdList(queryUserIdList);
        queryQywxStaffPageDTOList = qywxAddressBookManager.queryQywxStaffPageWithoutJoin(arg, page);
        if (CollectionUtils.isEmpty(queryQywxStaffPageDTOList)) {
            return queryQywxStaffPageDTOList;
        }
        Map<String, QywxActivatedAccountEntity> userIdToActivatedAccountMap = qywxActivatedAccountEntityList.stream().collect(Collectors.toMap(QywxActivatedAccountEntity::getUserId, e -> e, (v1, v2) -> v1));
        for (QueryQywxStaffPageDTO queryQywxStaffPageDTO : queryQywxStaffPageDTOList) {
            String openId = convertMap.get(queryQywxStaffPageDTO.getUserId());
            if (StringUtils.isBlank(openId)) {
                continue;
            }
            QywxActivatedAccountEntity qywxActivatedAccountEntity = userIdToActivatedAccountMap.get(openId);
            if (qywxActivatedAccountEntity == null) {
                continue;
            }
            queryQywxStaffPageDTO.setActivatedTime(qywxActivatedAccountEntity.getActiveTime());
            queryQywxStaffPageDTO.setExpireTime(qywxActivatedAccountEntity.getExpireTime());
        }
        return queryQywxStaffPageDTOList;
    }
    private List<QueryQywxStaffPageResult> buildQywxStaffPageResult(List<QueryQywxStaffPageDTO> queryQywxStaffPageDTOList, QueryQywxStaffPageArg arg) {
        if (CollectionUtils.isEmpty(queryQywxStaffPageDTOList)) {
            return Lists.newArrayList();
        }
        List<QueryQywxStaffPageResult> resultList = Lists.newArrayList();
        Integer bindCrmStatus = arg.getBindCrmStatus();
        List<String> qywxUserIdList = queryQywxStaffPageDTOList.stream().map(QueryQywxStaffPageDTO::getUserId).collect(Collectors.toList());

        Map<String, Integer> qyUserIdOpenCardMap = qywxUserManager.getOpenCardUserIdMap(arg.getFsEa(), qywxUserIdList);
        Map<String, QywxVirtualFsUserEntity> userIdVirtualFsUserMap = Maps.newHashMap();
        if (bindCrmStatus == null || bindCrmStatus == UserBindCrmStatusEnum.ALL.getStatus() || bindCrmStatus == UserBindCrmStatusEnum.BIND.getStatus()) {
            // 获取已经绑定crm绑定的企微userId
            List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.queryBindCrmQyUserId(arg.getFsEa(), qywxUserIdList);
            if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
                qywxVirtualFsUserEntityList.forEach(e -> userIdVirtualFsUserMap.put(e.getQyUserId(), e));
            }
        }
        List<QywxAddFanQrCodeEntity> qywxAddFanQrCodeEntityList = qywxAddFanQrCodeDAO.queryCardFanQrCodeByUserIdList(arg.getFsEa(), qywxUserIdList);
        Map<String, QywxAddFanQrCodeEntity> userIdToQQrCodeMap = Maps.newHashMap();
        Map<String, Integer> totalCustomerMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxAddFanQrCodeEntityList)) {
            for (QywxAddFanQrCodeEntity qywxAddFanQrCodeEntity : qywxAddFanQrCodeEntityList) {
                String userId = qywxAddFanQrCodeEntity.getUserId();
                List<String> userIdList = JSON.parseArray(userId, String.class);
                userIdToQQrCodeMap.put(userIdList.get(0), qywxAddFanQrCodeEntity);
            }
            List<String> qrCodeIds = qywxAddFanQrCodeEntityList.stream().map(QywxAddFanQrCodeEntity::getId).collect(Collectors.toList());
            //查询二维码关联企微加好友记录表
            totalCustomerMap = wechatFriendsRecordObjDescribeManager.getTotalCustomerCount(arg.getFsEa(), qrCodeIds);
        }

        List<QywxEmployeeResult> qywxEmployeeResults = qywxEmployeeManager.batchByQyUserIds(arg.getFsEa(), qywxUserIdList, false);
        Map<String, QywxEmployeeResult> qywxEmployeeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxEmployeeResults)) {
            qywxEmployeeMap = qywxEmployeeResults.stream().collect(Collectors.toMap(QywxEmployeeResult::getUserId, Function.identity(), (v1, v2) -> v1));
        }
        for (QueryQywxStaffPageDTO dto : queryQywxStaffPageDTOList) {
            QueryQywxStaffPageResult result = new QueryQywxStaffPageResult();
            result.setUserId(dto.getUserId());
            result.setName(dto.getName());
            QywxEmployeeResult qywxEmployeeResult = qywxEmployeeMap.get(result.getUserId());
            if (qywxEmployeeResult != null) {
                result.setDepartment(qywxEmployeeResult.getWechatDepartmentName());
            }
            result.setActivatedTime(dto.getActivatedTime() != null ? dto.getActivatedTime() * 1000 : null);
            Long expireTime = dto.getExpireTime();
            result.setExpireTime(expireTime != null ? expireTime * 1000 : null);
            QywxAddFanQrCodeEntity qywxAddFanQrCodeEntity = userIdToQQrCodeMap.get(dto.getUserId());
            int cardAddFriendCount = 0;
            if (qywxAddFanQrCodeEntity != null) {
                cardAddFriendCount = totalCustomerMap.getOrDefault(qywxAddFanQrCodeEntity.getId(), 0);
                result.setQrCodeId(qywxAddFanQrCodeEntity.getId());
            }
            result.setCardAddFriendCount(cardAddFriendCount);
            if (expireTime == null) {
                result.setActivatedStatus(AccountActivateStatusEnum.NOT_OPEN.getStatus());
            } else if (System.currentTimeMillis() > expireTime * 1000){
                result.setActivatedStatus(AccountActivateStatusEnum.NOT_OPEN.getStatus());
            } else {
                result.setActivatedStatus(AccountActivateStatusEnum.OPEN.getStatus());
            }
            Integer fsUserId = qyUserIdOpenCardMap.get(dto.getUserId());
            result.setOpenCardStatus(fsUserId == null ? UserCardOpenStatusEnum.NOT_OPEN.getStatus() : UserCardOpenStatusEnum.OPEN.getStatus());
            result.setFsUserId(fsUserId);
            if (bindCrmStatus != null && bindCrmStatus == UserBindCrmStatusEnum.NOT_BIND.getStatus()) {
                result.setCrmBindStatus(UserBindCrmStatusEnum.NOT_BIND.getStatus());
            } else if (bindCrmStatus != null && bindCrmStatus == UserBindCrmStatusEnum.BIND.getStatus()) {
                result.setCrmBindStatus(UserBindCrmStatusEnum.BIND.getStatus());
                QywxVirtualFsUserEntity virtualFsUserEntity = userIdVirtualFsUserMap.get(dto.getUserId());
                if (virtualFsUserEntity != null && virtualFsUserEntity.getCrmBindTime() != null) {
                    result.setCrmBindTime(virtualFsUserEntity.getCrmBindTime().getTime());
                }
            } else {
                QywxVirtualFsUserEntity virtualFsUserEntity = userIdVirtualFsUserMap.get(dto.getUserId());
                result.setCrmBindStatus(virtualFsUserEntity != null ? UserBindCrmStatusEnum.BIND.getStatus() : UserBindCrmStatusEnum.NOT_BIND.getStatus());
                if (virtualFsUserEntity != null && virtualFsUserEntity.getCrmBindTime() != null) {
                    result.setCrmBindTime(virtualFsUserEntity.getCrmBindTime().getTime());
                }
            }
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    public Result<Void> exportQywxStaff(QueryQywxStaffPageArg arg) {
        int pageSize = 1000;
        arg.setPageNum(1);
        arg.setPageSize(pageSize);
        if (!arg.checkParam()) {
            throw new BusinessException(SHErrorCode.PARAMS_ERROR);
        }
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<PageResult<QueryQywxStaffPageResult>> result =  queryQywxStaffPage(arg);
            if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData().getResult())) {
                List<QueryQywxStaffPageResult> detailResultList = result.getData().getResult();
                SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
                String sheetName = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_722)
                        + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
                SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
                sxssfSheet.setDefaultColumnWidth(20);
                // 填充标题行
                ExcelUtil.fillTitles(sxssfSheet, TITLE_LIST);
                // 填充第一页的数据
                ExcelUtil.appendContent(sxssfSheet, buildDataList(detailResultList), 1);

                Integer totalCount = result.getData().getTotalCount();
                int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
                if (totalPage > 1) {
                    for (int i = 2; i <= totalPage; i++) {
                        arg.setPageNum(i);
                        result = queryQywxStaffPage(arg);
                        detailResultList = result.getData().getResult();
                        if (CollectionUtils.isEmpty(detailResultList)) {
                            break;
                        }
                        // 追加填充每一页的数据
                        ExcelUtil.appendContent(sxssfSheet,buildDataList(detailResultList), (i - 1) * pageSize + 1);
                    }
                }
                pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, arg.getFsEa(), arg.getFsUserId());
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);


        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncQywxStaff(QueryQywxStaffaArg arg) {
        ThreadPoolUtils.execute(() -> {
            wechatWorkExternalUserObjManager.initQywxUserIdData(arg.getFsEa(), arg.getQywxUserIds());
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    private List<List<Object>> buildDataList(List<QueryQywxStaffPageResult> detailResultList) {
        List<List<Object>> result = Lists.newArrayList();
        for (QueryQywxStaffPageResult detail : detailResultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(StringUtils.defaultString(detail.getName()));
            objectList.add(StringUtils.defaultString(detail.getDepartment()));

            String openCardStatus;
            if (detail.getOpenCardStatus() == null  || detail.getOpenCardStatus().equals(UserCardOpenStatusEnum.NOT_OPEN.getStatus())) {
                openCardStatus = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_762);
            } else {
                openCardStatus = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_764);
            }
            objectList.add(StringUtils.defaultString(openCardStatus));

            int addFriendCount = detail.getCardAddFriendCount() == null ? 0 : detail.getCardAddFriendCount();
            objectList.add(addFriendCount);

            String activatedStatus;
            if (detail.getActivatedStatus() == null  || detail.getActivatedStatus() == AccountActivateStatusEnum.NOT_OPEN.getStatus()) {
                activatedStatus = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_773);
            } else {
                activatedStatus = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_775);
            }
            objectList.add(activatedStatus);

            String activatedTime = "";
            if (detail.getActivatedTime() != null) {
                activatedTime = DateUtil.format("yyyy-MM-dd HH:mm", new Date(detail.getActivatedTime()));
            }
            objectList.add(activatedTime);

            String expireTime = "";
            if (detail.getExpireTime() != null) {
                expireTime = DateUtil.format("yyyy-MM-dd HH:mm", new Date(detail.getExpireTime()));
            }
            objectList.add(expireTime);

            String crmBindStatus;
            if (detail.getCrmBindStatus() == null || detail.getCrmBindStatus() == UserBindCrmStatusEnum.NOT_BIND.getStatus()) {
                crmBindStatus = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_793);
            } else {
                crmBindStatus = I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXSTAFFSERVICEIMPL_795);
            }
            objectList.add(crmBindStatus);

            String crmBindTime = "";
            if (detail.getCrmBindTime() != null) {
                crmBindTime = DateUtil.format("yyyy-MM-dd HH:mm", new Date(detail.getCrmBindTime()));
            }
            objectList.add(crmBindTime);

            result.add(objectList);
        }
        return result;
    }
}