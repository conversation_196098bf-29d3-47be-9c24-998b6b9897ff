package com.facishare.marketing.provider.service.qr;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.UrlEncoder;
import com.facishare.marketing.api.arg.hexagon.CreateHexagonWxQrCodeArg;
import com.facishare.marketing.api.result.HexagonQrCodeResult;
import com.facishare.marketing.api.result.qr.CreateQRCodeResult;
import com.facishare.marketing.api.result.qr.QueryQRCodeResult;
import com.facishare.marketing.api.service.qr.QRCodeService;
import com.facishare.marketing.api.vo.PartnerChannelQrCodeVO;
import com.facishare.marketing.common.contstant.ParamQrCodeSceneTypeConstants;
import com.facishare.marketing.common.contstant.WxApiConstants;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.provider.dao.WxServiceQrCodePartnerRelationDAO;
import com.facishare.marketing.provider.dao.qr.QRCodeDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dto.CommonObjectDTO;
import com.facishare.marketing.provider.entity.WxServiceQrCodePartnerRelationEntity;
import com.facishare.marketing.provider.entity.WxServiceQrCodeUserMarketingRelationEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.innerArg.qywx.ContactMeConfigArg;
import com.facishare.marketing.provider.innerResult.qywx.GetContactMeResult;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.ObjectManager;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.WxTicketManager;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.lock.LockManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.constants.QrActionType;
import com.facishare.wechat.proxy.model.result.CreateParamQrCodeResult;
import com.facishare.wechat.proxy.model.vo.CreateParamQrCodeVO;
import com.facishare.wechat.proxy.service.QrCodeService;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.*;

@Service("qrPosterCodeService")
@Slf4j
public class QRCodeServiceImpl implements QRCodeService {

    @Autowired
    private QRCodeManager qrCodeManager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private com.facishare.marketing.provider.manager.kis.ObjectManager kisObjectManager;
    @Autowired
    private WxTicketManager wxTicketManager;
    @Autowired
    private QrCodeService qrCodeService;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    public static final String POSTER_QR_CODE = "HB_";
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private WxServiceQrCodePartnerRelationDAO wxServiceQrCodePartnerRelationDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private WechatQrCodeRestService wechatQrCodeRestService;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @ReloadableProperty("marketing_appid")
    private String appId;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private LockManager lockManager;

    @Override
    public Result<CreateQRCodeResult> createQRCode(String ea, Integer userId, Integer type, String value, Integer lengthOfSide, String h5Path) {
        if (EmptyUtil.isNullForList(type)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isNotBlank(value) && !TextUtil.isJson(value)) {
            log.warn("QRCodeService.createQRCode params error, value is not json string, value={}", value);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(type);
        data.setValue(value);
        data.setLengthOfSide(lengthOfSide);
        data.setEa(ea);
        data.setH5Path(h5Path);
        QRCodeManager.CreateQRCodeResult createQRCodeResult = qrCodeManager.createQRCode(data);
        if (null == createQRCodeResult) {
            log.error("QRCodeService.createQRCode qrCodeManager.creator failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (createQRCodeResult.getErrorCode() != null && !createQRCodeResult.isSuccess()) {
            log.warn("outQrCode createQRCode failed");
            return Result.newError(createQRCodeResult.getErrorCode(),createQRCodeResult.getErrorMessage());
        }

        CreateQRCodeResult result = new CreateQRCodeResult();
        result.setQrCodeId(createQRCodeResult.getQrCodeId());
        result.setQrCodeUrl(createQRCodeResult.getQrCodeUrl());
        result.setQrUrl(createQRCodeResult.getQrUrl());
        return Result.newSuccess(result);
    }

    @Override
    public Result<CreateQRCodeResult> createQRCodeWithoutIdentity(Integer objectType, String objectId, Integer type, String value, Integer lengthOfSide) {
        CommonObjectDTO commonObjectDTO = objectManager.getCommonObject(null, null, objectId, objectType);
        if (commonObjectDTO == null || commonObjectDTO.getEa() == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return createQRCode(commonObjectDTO.getEa(), -10000, type, value, lengthOfSide, null);
    }

    @Override
    public Result<QueryQRCodeResult> queryQRCode(Integer qrCodeId, String authCode) {
        return qrCodeManager.queryQRCode(qrCodeId, authCode);
    }
    
    @Override
    public Result<String> createWxTemplateQrCodeByChannelQrCode(String ea, String wxAppId, String channelQrCodeId, Integer spreadFsUserId, String marketingEventId, String marketingActivityId) {
        CreateParamQrCodeVO createParamQrCodeVO = new CreateParamQrCodeVO();
        createParamQrCodeVO.setWxAppId(wxAppId);
        createParamQrCodeVO.setEa(ea);
        Integer fsUserId = wxTicketManager.getEnterpriseAdminInfo(ea);
        createParamQrCodeVO.setFsUserId(fsUserId);
        createParamQrCodeVO.setActionName(QrActionType.QR_LIMIT_STR_SCENE.name());
        createParamQrCodeVO.setExpireSeconds(60 * 60 * 24 * 30);
        createParamQrCodeVO.setSceneType(ParamQrCodeSceneTypeConstants.TEMP_CHANNEL_QR_CODE);
        Map<String, Object> param = Maps.newHashMap();
        param.put("channelQrCodeId", channelQrCodeId + "");
        if (Strings.isNullOrEmpty(marketingEventId)){
            marketingEventId = kisObjectManager.queryMarketingEventId(ea, marketingActivityId);
        }
        param.put("marketingEventId", marketingEventId);
        param.put("marketingActivityId", marketingActivityId);
        param.put("spreadFsUserId", spreadFsUserId + "");
        createParamQrCodeVO.setParams(param);
        ModelResult<CreateParamQrCodeResult> qrCodeResultModelResult = qrCodeService.createParamQrCode(createParamQrCodeVO);
        if (!qrCodeResultModelResult.isSuccess()){
            return Result.newError(qrCodeResultModelResult.getErrorCode(), qrCodeResultModelResult.getErrorMessage());
        }
        return Result.newSuccess(qrCodeResultModelResult.getResult().getTicketUrl());
    }

    @Override
    public Result<CreateQRCodeResult> createSpreadUserQywxQRCode(String fsEa, Integer fsUserId, String id) {
        CreateQRCodeResult result = new CreateQRCodeResult();
        QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(id);
        if(Objects.isNull(qrPosterEntity)){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if(!Objects.equals(qrPosterEntity.getForwardType(), QRPosterForwardTypeEnum.QYWX_QR_CODE.getType())||StringUtils.isBlank(qrPosterEntity.getTargetId())){
            log.warn("QRCodeService.createSpreadUserQywxQRCode code type is not QYWX_QR_CODE");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxAddFanQrCodeEntity fatherCodeEntity = qywxAddFanQrCodeDAO.getById(qrPosterEntity.getTargetId());
        if(Objects.isNull(fatherCodeEntity)){
            return Result.newError(SHErrorCode.QYWX_FAN_QRCODE_IS_DELETED);
        }
        String accessToken = qywxManager.getAccessToken(fsEa);
        QywxAddFanQrCodeEntity sonCodeEntity = BeanUtil.copy(fatherCodeEntity, QywxAddFanQrCodeEntity.class);
        QywxAddFanQrCodeEntity code = null;
//        if(qrPosterEntity.getUserAddSettings()==null || qrPosterEntity.getUserAddSettings()==1){
//            if(sonCodeEntity.getType()==2){
//                List<QywxAddFanQrCodeEntity> codeEntities = qywxAddFanQrCodeDAO.queryMultipleQrCodeByParent(fsEa, fatherCodeEntity.getId(), qrPosterEntity.getMarketingEventId());
//                if(CollectionUtils.isNotEmpty(codeEntities)){
//                    code=codeEntities.get(0);
//                }
//            }else {
//                String userId = JSON.parseObject(sonCodeEntity.getUserId(), List.class).get(0).toString();
//                code = qywxAddFanQrCodeDAO.queryQrCodeByParentAndQyUser(fsEa, fatherCodeEntity.getId(),qrPosterEntity.getMarketingEventId() , userId);
//            }
//        }
//        if(qrPosterEntity.getUserAddSettings()==2){
        List<QywxAddFanQrCodeEntity> qywxAddFanQrCodeEntityList = qywxAddFanQrCodeDAO.queryQrCodeByParentAndSpreadUserId(fsEa, fatherCodeEntity.getId(),qrPosterEntity.getMarketingEventId() , fsUserId,id);
        code = CollectionUtils.isNotEmpty(qywxAddFanQrCodeEntityList) ? qywxAddFanQrCodeEntityList.get(0) : null;
        //   }

//        QywxAddFanQrCodeEntity code = qywxAddFanQrCodeDAO.queryQrCodeByParentAndSpreadUserId(fsEa, fatherCodeEntity.getId(),qrPosterEntity.getMarketingEventId() ,spreadUser);
//        QywxAddFanQrCodeEntity code = qywxAddFanQrCodeDAO.queryQrCodeByParentAndQrposterId(fsEa, fatherCodeEntity.getId(),qrPosterEntity.getMarketingEventId() ,id);
        if(Objects.nonNull(code)){
            if(StringUtils.isBlank(code.getConfigId())){
                GetContactMeResult meResult = getGetContactMeResult(code, accessToken);
                //生成过企微活码,但已删除,重新生成,并替换新的configid,stste,qrcodeurl
                if(meResult!=null && meResult.isSuccess()){
                    qywxAddFanQrCodeDAO.updateConfigById(code.getId(),meResult.getContactWay().getConfigId(),meResult.getContactWay().getState(),meResult.getContactWay().getQrCode());
                    result.setQrCodeUrl(meResult.getContactWay().getQrCode());
                    return Result.newSuccess(result);
                }else {
                    //创建失败使用父的
                    result.setQrCodeUrl(fatherCodeEntity.getQrCodeUrl());
                    if(qrPosterEntity.getFailedOperation()!=null && qrPosterEntity.getFailedOperation()==2){
                        result.setFailDesc(I18nUtil.get(I18nKeyEnum.MARK_QR_QRCODESERVICEIMPL_203));
                    }
                    return Result.newSuccess(result);
                }
            }else {
                //已有直接返回
                result.setQrCodeUrl(code.getQrCodeUrl());
                return Result.newSuccess(result);
            }
        }
        String key = String.format("mk:%s:%s:%s:%s:%s", fsEa, fatherCodeEntity.getId(), qrPosterEntity.getMarketingEventId(), fsUserId, id);
        try {
            boolean lock = lockManager.retryGetLock(key, 6, 3, 500);
            if (lock) {
                // 获取到锁了，再查一次，如果没有就创建，否则直接返回
                qywxAddFanQrCodeEntityList = qywxAddFanQrCodeDAO.queryQrCodeByParentAndSpreadUserId(fsEa, fatherCodeEntity.getId(),qrPosterEntity.getMarketingEventId() , fsUserId,id);
                if (CollectionUtils.isEmpty(qywxAddFanQrCodeEntityList)) {
                    return getCreateQRCodeResultResult(fsEa, fsUserId, id, qrPosterEntity, sonCodeEntity, result, fatherCodeEntity, accessToken);
                }
                result.setQrCodeUrl(qywxAddFanQrCodeEntityList.get(0).getQrCodeUrl());
                return Result.newSuccess(result);
            }
        } catch (Exception e) {
            log.error("createSpreadUserQywxQRCode lock error, ea: {} fsUserId: {} id: {}", fsEa, fsUserId, id, e);
        } finally {
            lockManager.unlock(key);
        }
        //获取锁失败使用父码
        result.setQrCodeUrl(fatherCodeEntity.getQrCodeUrl());
        if(qrPosterEntity.getFailedOperation()!=null && qrPosterEntity.getFailedOperation()==2){
            result.setFailDesc(I18nUtil.get(I18nKeyEnum.MARK_QR_QRCODESERVICEIMPL_203));
        }
        return Result.newSuccess(result);
    }


    private Result<CreateQRCodeResult> getCreateQRCodeResultResult(String fsEa, Integer fsUserId, String id, QRPosterEntity qrPosterEntity, QywxAddFanQrCodeEntity sonCodeEntity, CreateQRCodeResult result, QywxAddFanQrCodeEntity fatherCodeEntity, String accessToken) {
        if(qrPosterEntity.getUserAddSettings()==1){
            //多人码
            if(sonCodeEntity.getType()==2){
                sonCodeEntity.setQrCodeName(sonCodeEntity.getQrCodeName()+I18nUtil.get(I18nKeyEnum.MARK_QR_QRCODESERVICEIMPL_217));
            }
            if(sonCodeEntity.getType()==1){
               // QywxVirtualFsUserEntity virtualUserByEaAndQyId = qywxVirtualFsUserDAO.getVirtualUserByEaAndQyId(fsEa, Objects.requireNonNull(JSON.parseObject(sonCodeEntity.getUserId(), List.class)).get(0).toString());
                FsAddressBookManager.FSEmployeeMsg employeeInfo = fsAddressBookManager.getEmployeeInfo(fsEa, fsUserId);
                sonCodeEntity.setQrCodeName(sonCodeEntity.getQrCodeName()+"-"+employeeInfo.getName());
            }
        }
        if(qrPosterEntity.getUserAddSettings()==2){
            sonCodeEntity.setType(1);
            // 和产品确认了，如果是会员员工并且该会员员工没关联企微员工，直接使用父码
            if (QywxUserConstants.isMemberVirtualUserId(fsUserId) || QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
                UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(fsEa, fsUserId);
                if (userRelationEntity == null) {
                    return Result.newError(SHErrorCode.NO_DATA);
                }
                if (StringUtils.isBlank(userRelationEntity.getQywxUserId())) {
                    result.setQrCodeUrl(fatherCodeEntity.getQrCodeUrl());
                    return Result.newSuccess(result);
                }
            }
            QywxVirtualFsUserEntity entity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(fsEa, fsUserId);
            if(Objects.isNull(entity)||entity.getQyUserId()==null){
                return Result.newError(SHErrorCode.NO_DATA);
            }
            sonCodeEntity.setUserId(JSON.toJSONString(Lists.newArrayList(entity.getQyUserId())));
            sonCodeEntity.setOrginUserId(JSON.toJSONString(Lists.newArrayList(entity.getQyUserId())));
            FsAddressBookManager.FSEmployeeMsg employeeInfo = fsAddressBookManager.getEmployeeInfo(fsEa, fsUserId);
            sonCodeEntity.setQrCodeName(sonCodeEntity.getQrCodeName()+"-"+employeeInfo.getName());
        }
        sonCodeEntity.setSpreadUserId(fsUserId);
        sonCodeEntity.setId(UUIDUtil.getUUID());
        sonCodeEntity.setParentId(fatherCodeEntity.getId());
        sonCodeEntity.setCreateTime(null);
        sonCodeEntity.setUpdateTime(null);
        sonCodeEntity.setStatus(0);
        sonCodeEntity.setPosterId(id);
        //创建二维码
        GetContactMeResult contactMeResult = getGetContactMeResult(sonCodeEntity, accessToken);
        if(contactMeResult!=null && contactMeResult.isSuccess()){
            sonCodeEntity.setQrCodeUrl(contactMeResult.getContactWay().getQrCode());
            sonCodeEntity.setConfigId(contactMeResult.getContactWay().getConfigId());
            sonCodeEntity.setState(contactMeResult.getContactWay().getState());
            qywxAddFanQrCodeDAO.insert(sonCodeEntity);
            result.setQrCodeUrl(sonCodeEntity.getQrCodeUrl());
            return Result.newSuccess(result);
        }else {
            //失败使用父码
            result.setQrCodeUrl(fatherCodeEntity.getQrCodeUrl());
            if(qrPosterEntity.getFailedOperation()!=null && qrPosterEntity.getFailedOperation()==2){
                result.setFailDesc(I18nUtil.get(I18nKeyEnum.MARK_QR_QRCODESERVICEIMPL_203));
            }
            return Result.newSuccess(result);
        }
    }

    @Override
    public Result<String> createWxTemplateQrCodeByPartnerChannelQrCode(PartnerChannelQrCodeVO partnerChannelQrCodeVO) {
        String ea = partnerChannelQrCodeVO.getEa();
        String qrCodeId = partnerChannelQrCodeVO.getChannelQrCodeId();
        String wxAppId = partnerChannelQrCodeVO.getWxAppId();
        String outerTenantId = partnerChannelQrCodeVO.getEROuterTenantId();
        String outerUid = partnerChannelQrCodeVO.getEROuterUid();
        String marketingEventId = partnerChannelQrCodeVO.getMarketingEventId();
        String marketingActivityId = partnerChannelQrCodeVO.getMarketingActivityId();
        String newSceneId = UUIDUtil.getUUID();
        if (Strings.isNullOrEmpty(marketingEventId)){
            marketingEventId = kisObjectManager.queryMarketingEventId(ea, marketingActivityId);
        }

        //获取公众号二维码详情
        QrCodeResult qrCodeEntity = getQrCodeResult(wxAppId, qrCodeId, ea);
        if (qrCodeEntity == null) {
            return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
        }
        WxServiceQrCodePartnerRelationEntity entity =  wxServiceQrCodePartnerRelationDAO.getByMainSceneIdAndOutTenantIdAndOutUid(ea, qrCodeId,outerTenantId,outerUid);
        if (entity == null) {
            WxServiceQrCodePartnerRelationEntity newEntity = new WxServiceQrCodePartnerRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setSceneId(newSceneId);
            newEntity.setMainSceneId(qrCodeId);
            newEntity.setOuterTenantId(outerTenantId);
            newEntity.setOuterUid(outerUid);
            newEntity.setMarketingActivityId(marketingActivityId);
            newEntity.setMarketingEventId(marketingEventId);
            wxServiceQrCodePartnerRelationDAO.relateSceneIdAndPartner(newEntity);
        }

        // 获取ticket
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        wechatRequestDispatchArg.setEi(eieaConverter.enterpriseAccountToId(ea));
        wechatRequestDispatchArg.setWxAppId(qrCodeEntity.getAppId());
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/qrcode/create?access_token=${access_token}");
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> scene = new HashMap<>();
        scene.put("scene_str",  newSceneId);
        scene.put("fsEa", ea);
        scene.put("appId", appId);
        scene.put("qrCodeName", qrCodeEntity.getQrCodeName());
        scene.put("responseMsg", qrCodeEntity.getResponseMsg());
        scene.put("tagNames", qrCodeEntity.getTagNames());
        scene.put("marketingEventId",marketingEventId);
        scene.put("marketingActivityId",marketingActivityId);
        scene.put("creator", qrCodeEntity.getCreator());
        map.put("scene", scene);
        body.put("action_name", "QR_STR_SCENE");
        body.put("action_info", map);
        body.put("expire_seconds", 86400);

        wechatRequestDispatchArg.setBody(JSON.toJSONString(body));
        Map<String, Object> objectMap = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<HashMap<String, Object>>() {
        });
        if (objectMap == null) {
            log.info("createWxTemplateQrCodeByPartnerChannelQrCode wechatAccountManager.dispatch return null");
            return Result.newError(SHErrorCode.QRCODE_CREATE_FAILED);
        }
        //如果二维码已经存在,则更新二维码
        if (entity != null){
            wxServiceQrCodePartnerRelationDAO.updateSceneIdById(entity.getEa(), entity.getId(), newSceneId);
        }
        log.info("createWxTemplateQrCodeByPartnerChannelQrCode wechatAccountManager.dispatch objectMap:{}", objectMap);
        String ticket = UrlEncoder.urlComponentEncode(objectMap.get("ticket"));
        String showUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        return Result.newSuccess(showUrl);
    }

    @Nullable
    private GetContactMeResult getGetContactMeResult(QywxAddFanQrCodeEntity sonCodeEntity, String accessToken) {
        ContactMeConfigArg configArg = new ContactMeConfigArg(sonCodeEntity.getType(), sonCodeEntity.getRemark(), GsonUtil.fromJson(sonCodeEntity.getUserId(), List.class), sonCodeEntity.getChannelDesc());
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(sonCodeEntity.getEa());
        if (agentConfig == null) {
            log.info("createHexagonFanQrCode failed agentConfig=null ea:{}", sonCodeEntity.getEa());
        }

        if (sonCodeEntity.getSkipVerify() == 0){
            configArg.setSkipVerify(true);
        }else {
            configArg.setSkipVerify(false);
        }

        //创建新的二维码
        String state = POSTER_QR_CODE + System.currentTimeMillis() + "_" + UUIDUtil.generateUID(10);
        configArg.setState(state);
        if (qywxAddFanQrCodeDAO.getByEaAndState(sonCodeEntity.getEa(), state) != null){
            log.info("createSpreadUserQywxQRCode failed status is exist ea:{} currentState:{}", sonCodeEntity.getEa(), state);
        }
        String configId = qywxManager.setContactMeConfig(accessToken, configArg);
        if (configId == null){
            log.info("createSpreadUserQywxQRCode failed setContactMeConfig return configId==null qywxQrCode:{}", sonCodeEntity);
        }
        GetContactMeResult contactMeResult = qywxManager.getContanctMe(accessToken, configId);
        if (contactMeResult == null || !contactMeResult.isSuccess() || StringUtils.isEmpty(contactMeResult.getContactWay().getQrCode())){
            log.info("createSpreadUserQywxQRCode failed getContanctMe return contactMeResult==null qywxQrCode:{}", sonCodeEntity);
        }
        return contactMeResult;
    }

    private QrCodeResult getQrCodeResult(String wxAppId,String qrCodeId, String ea) {
        String appId = null;
        //转换wxAppId 为 微联号appId
        List<OuterServiceResult> outerServiceResults = outerServiceWechatManager.queryOuterServiceList(ea, 1000);
        if(CollectionUtils.isEmpty(outerServiceResults)){
            log.info("createWxTemplateQrCodeByPartnerChannelQrCode failed, outerServiceResults is empty");
            return null;
        }
        //进行匹配wxAppId ,获取appId
        for (OuterServiceResult outerServiceResult : outerServiceResults) {
            if (Objects.equals(outerServiceResult.getWxAppId(), wxAppId)) {
                appId = outerServiceResult.getAppId();
            }
        }
        if(StringUtils.isBlank(appId)){
            log.info("createWxTemplateQrCodeByPartnerChannelQrCode failed, appId is null");
            return null;
        }
        // sceneId调互联接口获取二维码详情
        QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
        queryStoreQrCodeArg.setAppId(appId);
        queryStoreQrCodeArg.setSceneId(Long.valueOf(qrCodeId));
        queryStoreQrCodeArg.setEnterpriseAccount(ea);
        ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
        if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
            log.info("HexagonServiceImpl createHexagonWxQrCode wechatQrCodeRestService.queryStoreQrCode fail, pagerModelResult:{}", pagerModelResult);
            return null;
        }
        return pagerModelResult.getResult().getData().get(0);
    }
}