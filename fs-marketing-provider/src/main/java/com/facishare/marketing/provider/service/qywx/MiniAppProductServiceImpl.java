package com.facishare.marketing.provider.service.qywx;


import com.facishare.mankeep.api.outService.result.EnterpriseDefaultCard.QueryDefaultProductListResult;
import com.facishare.mankeep.api.outService.result.EnterpriseDefaultCard.QueryDefaultProductResult;
import com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService;
import com.facishare.mankeep.api.vo.ProductNaviStyleQueryVO;
import com.facishare.mankeep.api.vo.QueryProductListVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.marketing.api.arg.ListProductArg;
import com.facishare.marketing.api.arg.qywx.miniappProduct.ProductNaviStyleQueryArg;
import com.facishare.marketing.api.arg.qywx.miniappProduct.QueryProductListArg;
import com.facishare.marketing.api.result.ProductListResult;
import com.facishare.marketing.api.result.qywx.miniappProduct.ProductNaviStyleSettingResult;
import com.facishare.marketing.api.result.qywx.miniappProduct.QueryProductDetailResult;
import com.facishare.marketing.api.service.ProductService;
import com.facishare.marketing.api.service.qywx.MiniAppProductService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.MiniAppConstants;
import com.facishare.marketing.common.enums.ProductArticleTypeEnum;
import com.facishare.marketing.common.enums.ProductStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.google.common.collect.Lists;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service("miniAppProductService")
public class MiniAppProductServiceImpl implements MiniAppProductService {


    @Autowired
    private com.facishare.mankeep.api.service.ProductService miniAppProductService;

    @Autowired
    private ProductService productService;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private OutEnterpriseDefaultCardService outEnterpriseDefaultCardService;

    @Autowired
    private WechatAccountManager wechatAccountManager;


    @Override
    public Result<PageResult<QueryProductDetailResult>> queryProductList(QueryProductListArg arg) {
        List<QueryProductDetailResult> queryProductDetailList;
        PageResult<QueryProductDetailResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setTime(arg.getTime());
        pageResult.setResult(Lists.newArrayList());
        if (arg.getTime() == null) {
            arg.setTime(new Date().getTime());
        }
        if (arg.getDataType().equals(ProductArticleTypeEnum.CORPORATE.getType())) {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getCardUid());
            if (fsBindEntity == null) {
                log.warn("MiniAppProductServiceImpl.queryProductList error fsBindEntity si null arg:{}", arg);
                return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
            }
            ListProductArg vo = new ListProductArg();
            vo.setStatus(ProductStatusEnum.NORMAL.getStatus());
            vo.setConnectMarketingActivity(false);
            vo.setType(ProductArticleTypeEnum.CORPORATE.getType());
            vo.setPageNum(arg.getPageNum());
            vo.setPageSize(arg.getPageSize());
            vo.setTime(arg.getTime());
            Result<ProductListResult> productListResultResult = productService.listEnterpriseProducts(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId(), vo);
            if (productListResultResult == null || !productListResultResult.isSuccess()) {
                log.warn("MiniAppProductServiceImpl.queryProductList productListResultResult error arg:{}, productListResultResult:{}", arg, productListResultResult);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            queryProductDetailList = BeanUtil.copy(productListResultResult.getData().getProductDetailResultList(), QueryProductDetailResult.class);
            pageResult.setTotalCount(productListResultResult.getData().getTotalCount());
            pageResult.setResult(queryProductDetailList);
            return Result.newSuccess(pageResult);
        } else {
            QueryProductListVO vo = BeanUtil.copy(arg, QueryProductListVO.class);
            vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
            ModelResult<PageObject<com.facishare.mankeep.api.result.QueryProductDetailResult>> pageObjectModelResult = miniAppProductService.queryProductList(vo);
            if (!pageObjectModelResult.isSuccess()) {
                log.warn("MiniAppProductServiceImpl.queryProductList error arg:{}, pageObjectModelResult:{}", arg, pageObjectModelResult);
                return Result.newError(pageObjectModelResult.getErrCode(), pageObjectModelResult.getErrMsg());
            }
            if (pageObjectModelResult.getData() == null || CollectionUtils.isEmpty(pageObjectModelResult.getData().getResult())) {
                return Result.newSuccess(pageResult);
            }
            queryProductDetailList = BeanUtil.copy(pageObjectModelResult.getData().getResult(), QueryProductDetailResult.class);
            pageResult.setTotalCount(pageObjectModelResult.getData().getTotalCount());
            pageResult.setResult(queryProductDetailList);
            return Result.newSuccess(pageResult);
        }
    }

    @Override
    public Result<ProductNaviStyleSettingResult> queryProductNaviStyle(ProductNaviStyleQueryArg arg) {
        ProductNaviStyleQueryVO vo = BeanUtil.copy(arg, ProductNaviStyleQueryVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<com.facishare.mankeep.api.result.ProductNaviStyleSettingResult> productNaviStyleSettingResult = miniAppProductService.queryProductNaviStyle(vo);
        if (!productNaviStyleSettingResult.isSuccess()) {
            log.warn("MiniAppProductServiceImpl.queryProductNaviStyle error arg:{}, productNaviStyleSettingResult:{}", arg, productNaviStyleSettingResult);
            return Result.newError(productNaviStyleSettingResult.getErrCode(), productNaviStyleSettingResult.getErrMsg());
        }
        return Result.newSuccess(BeanUtil.copy(productNaviStyleSettingResult.getData(), ProductNaviStyleSettingResult.class));
    }


    @Override
    public Result<PageResult<QueryProductDetailResult>> queryEnterpriseDefaultProduct(com.facishare.marketing.api.vo.product.QueryProductListVO vo) {
        String ea = vo.getEa();
        if (StringUtils.isBlank(ea)) {
            if (StringUtils.isBlank(vo.getCardUid())) {
                Collection<String> eas = wechatAccountManager.listEaByPlatformIdAndWxAppId(MKThirdPlatformConstants.PLATFORM_ID, vo.getAppId());
                if (CollectionUtils.isEmpty(eas)) {
                    return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
                }
                ea = eas.stream().findAny().get();
            } else {
                FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(vo.getCardUid());
                if (fsBindEntity == null) {
                    return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
                }
                ea = fsBindEntity.getFsEa();
            }
        }
        List<QueryProductDetailResult> result = Lists.newArrayList();
        PageResult<QueryProductDetailResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(result);
        com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.QueryDefaultProductListArg mankeepVO = new com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.QueryDefaultProductListArg();
        mankeepVO.setEa(ea);
        mankeepVO.setStatus(2);
        mankeepVO.setPageNum(vo.getPageNum());
        mankeepVO.setPageSize(vo.getPageSize());
        mankeepVO.setKeyWord(vo.getKeyWord());
        ModelResult<QueryDefaultProductListResult> queryDefaultProductList = outEnterpriseDefaultCardService.queryDefaultProductList(mankeepVO);
        if (!queryDefaultProductList.isSuccess()) {
            return new Result<>(queryDefaultProductList.getErrCode(), queryDefaultProductList.getErrMsg());
        }
        for (QueryDefaultProductResult data : queryDefaultProductList.getData().getQueryDefaultProductResultList()) {
            QueryProductDetailResult queryProductDetailResult = BeanUtil.copy(data, QueryProductDetailResult.class);
            result.add(queryProductDetailResult);
        }
        pageResult.setTotalCount(queryDefaultProductList.getData().getTotalCount());
        return Result.newSuccess(pageResult);
    }
}
