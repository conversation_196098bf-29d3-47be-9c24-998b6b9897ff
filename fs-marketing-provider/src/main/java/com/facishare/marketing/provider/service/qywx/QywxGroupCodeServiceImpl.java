package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.qywx.QywxGroupChatList;
import com.facishare.marketing.api.result.qywx.QywxGroupCodeDetailResult;
import com.facishare.marketing.api.result.qywx.QywxGroupCodeResult;
import com.facishare.marketing.api.service.qywx.QywxGroupCodeService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.api.vo.qywx.QywxGroupCodeListVO;
import com.facishare.marketing.api.vo.qywx.QywxGroupCodeVO;
import com.facishare.marketing.api.vo.qywx.UpdateQywxGroupCodeVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qywx.QywxGroupListSourceTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.qywx.QywxGroupCodeQueryParam;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupCodeDAO;
import com.facishare.marketing.provider.dto.qywx.QywxGroupCodeDTO;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.ObjectGroupRelationEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupCodeEntity;
import com.facishare.marketing.provider.innerArg.qywx.QywxGroupContactMeConfigArg;
import com.facishare.marketing.provider.innerArg.qywx.UpdateQywxGroupContactMeConfigArg;
import com.facishare.marketing.provider.innerResult.qywx.GetQywxGroupContactMeResult;
import com.facishare.marketing.provider.manager.ObjectGroupManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupObjDescribeManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13 17:36
 */
@Service("qywxGroupCodeService")
@Slf4j
public class QywxGroupCodeServiceImpl implements QywxGroupCodeService {

    @Autowired
    private QywxGroupCodeDAO qywxGroupCodeDAO;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;

    @Autowired
    private EIEAConverter eieaConverter;


    @Override
    public Result<PageResult<QywxGroupCodeResult>> queryPage(QywxGroupCodeListVO vo) {
        PageResult<QywxGroupCodeResult> pageResult = new PageResult<>();
        List<QywxGroupCodeResult> qywxGroupCodeResult = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        if (StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }
        QywxGroupCodeQueryParam queryParam = new QywxGroupCodeQueryParam();
        queryParam.setEa(vo.getEa());
        queryParam.setName(vo.getGroupCodeName());
        Page page = new Page(vo.getPageNum(),vo.getPageSize(),true);
        List<QywxGroupCodeDTO> qywxGroupCodeEntities;
        if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(vo.getEa(), vo.getUserId(), ObjectTypeEnum.QY_GROUP_CODE.getType());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            queryParam.setPermissionGroupIdList(permissionGroupIdList);
            queryParam.setUserId(vo.getUserId());
            qywxGroupCodeEntities = qywxGroupCodeDAO.getAccessiblePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            queryParam.setUserId(vo.getUserId());
            qywxGroupCodeEntities = qywxGroupCodeDAO.getCreateByMePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            qywxGroupCodeEntities = qywxGroupCodeDAO.noGroupPage(queryParam, page);
        } else {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(vo.getEa(), vo.getUserId(), ObjectTypeEnum.QY_GROUP_CODE.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            if (!permissionGroupIdSet.contains(vo.getGroupId())){
                qywxGroupCodeEntities = Lists.newArrayList();
            } else {
                queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                queryParam.setUserId(vo.getUserId());
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(vo.getEa(), ObjectTypeEnum.QY_GROUP_CODE.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                qywxGroupCodeEntities = qywxGroupCodeDAO.getAccessiblePage(queryParam, page);
            }
        }

        if (CollectionUtils.isEmpty(qywxGroupCodeEntities)) {
            pageResult.setResult(qywxGroupCodeResult);
            return Result.newSuccess(pageResult);
        }
        qywxGroupCodeEntities.forEach(qywxGroupCodeEntity -> {
            QywxGroupCodeResult groupCodeResult = BeanUtil.copy(qywxGroupCodeEntity, QywxGroupCodeResult.class);
            groupCodeResult.setChatIdList(GsonUtil.getGson().fromJson(qywxGroupCodeEntity.getChatIdList(),new TypeToken<List<String>>(){}.getType()));
            groupCodeResult.setMemberCount(calculateGroupMemberCount(qywxGroupCodeEntity.getEa(),groupCodeResult.getChatIdList()));
            qywxGroupCodeResult.add(groupCodeResult);
        });
        pageResult.setResult(qywxGroupCodeResult);
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    private Integer calculateGroupMemberCount(String ea,List<String> chatIdList) {
        Integer memberCount = 0;
        int ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> pageResult = wechatGroupObjDescribeManager.listGroupObjectDataByIdsLimited(ei,chatIdList);
        if (pageResult.isSuccess() && pageResult.getData() != null) {
            List<ObjectData> dataList = pageResult.getData().getDataList();
            if(CollectionUtils.isNotEmpty(dataList)) {
                for (ObjectData objectData : dataList) {
                    memberCount += objectData.getInt("member_count");
                }
            }
        }
        return memberCount;
    }

    @Override
    public Result<Void> saveQywxGroupCode(QywxGroupCodeVO vo) {
        //创建联系我
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(vo.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        QywxGroupContactMeConfigArg arg = BeanUtil.copy(vo,QywxGroupContactMeConfigArg.class);
        //state默认30个字符，扫码的时候企业微信回传 ：企业自定义的state参数，用于区分不同的添加渠道，在调用“获取群成员详情”时会返回该参数值，不超过30个字符
        String state = System.currentTimeMillis() +"_"  + UUIDUtil.generateUID(10);
        if (qywxGroupCodeDAO.getByEaAndState(vo.getEa(), state) != null){
            log.info("saveQywxGroupCode failed status is exist ea:{} currentState:{}", vo.getEa(), state);
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        arg.setState(state);
        String accessToken = qywxManager.getAccessToken(vo.getEa());
        String configId = qywxManager.setQywxGroupContactMeConfig(accessToken, arg);
        if (configId == null){
            log.info("saveQywxGroupCode failed setQywxGroupContactMeConfig return configId==null vo:{}", vo);
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        GetQywxGroupContactMeResult qywxGroupContactMeResult = qywxManager.getQywxGroupContactMe(accessToken, configId);
        if (qywxGroupContactMeResult == null || !qywxGroupContactMeResult.isSuccess() || StringUtils.isEmpty(qywxGroupContactMeResult.getJoinWay().getQrCode())){
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        QywxGroupCodeEntity qywxGroupCodeEntity = BeanUtil.copy(vo,QywxGroupCodeEntity.class);
        qywxGroupCodeEntity.setId(UUIDUtil.getUUID());
        qywxGroupCodeEntity.setConfigId(qywxGroupContactMeResult.getJoinWay().getConfigId());
        qywxGroupCodeEntity.setQrCodeUrl(qywxGroupContactMeResult.getJoinWay().getQrCode());
        qywxGroupCodeEntity.setState(qywxGroupContactMeResult.getJoinWay().getState());
        qywxGroupCodeEntity.setStatus(0);
        qywxGroupCodeEntity.setChatIdList(GsonUtil.getGson().toJson(vo.getChatIdList()));
        qywxGroupCodeEntity.setCreateBy(vo.getUserId());
        qywxGroupCodeDAO.insert(qywxGroupCodeEntity);
        objectGroupManager.setGroup(vo.getEa(), vo.getUserId(), ObjectTypeEnum.QY_GROUP_CODE.getType(), Collections.singletonList(qywxGroupCodeEntity.getId()), vo.getGroupId());
        return Result.newSuccess();
    }

    @Override
    public Result<QywxGroupCodeDetailResult> queryCodeDetail(String id) {
        QywxGroupCodeEntity qywxGroupCodeEntity = qywxGroupCodeDAO.queryById(id);
        if (qywxGroupCodeEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        QywxGroupCodeDetailResult result = BeanUtil.copy(qywxGroupCodeEntity,QywxGroupCodeDetailResult.class);
        result.setChatIdList(GsonUtil.getGson().fromJson(qywxGroupCodeEntity.getChatIdList(),new TypeToken<List<String>>(){}.getType()));
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> deleteQywxGroupCode(String ea,List<String> ids) {
        qywxGroupCodeDAO.deleteGroupCode(ids);
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.QY_GROUP_CODE.getType(), ids);
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QY_GROUP_CODE.getType(), ids);
        //删除群活码配置
        List<QywxGroupCodeEntity> qywxGroupCodeEntities = qywxGroupCodeDAO.getByIds(ids);
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        String accessToken = qywxManager.getAccessToken(ea);
        for (QywxGroupCodeEntity entity : qywxGroupCodeEntities) {
            boolean delRet = qywxManager.deleteQywxGroupContactMe(accessToken, entity.getConfigId());
            if (!delRet) {
                log.warn("qywxManager.deleteQywxGroupContactMe error ea:{},codeId:{}",ea,entity.getId());
            }
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateContactGroupCode(String id) {
        QywxGroupCodeEntity qywxGroupCodeEntity = qywxGroupCodeDAO.queryById(id);
        if (qywxGroupCodeEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(qywxGroupCodeEntity.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        String accessToken = qywxManager.getAccessToken(qywxGroupCodeEntity.getEa());
        boolean deleteQywxGroupContactMe = qywxManager.deleteQywxGroupContactMe(accessToken, qywxGroupCodeEntity.getConfigId());
        if (deleteQywxGroupContactMe) {
            qywxGroupCodeDAO.invalidCode(id);
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
    }

    @Override
    public Result<Void> updateQywxGroupCode(UpdateQywxGroupCodeVO vo) {
        QywxGroupCodeEntity qywxGroupCodeEntity = qywxGroupCodeDAO.queryById(vo.getId());
        if (qywxGroupCodeEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(qywxGroupCodeEntity.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        String accessToken = qywxManager.getAccessToken(qywxGroupCodeEntity.getEa());
        UpdateQywxGroupContactMeConfigArg arg = BeanUtil.copy(vo,UpdateQywxGroupContactMeConfigArg.class);
        arg.setConfigId(qywxGroupCodeEntity.getConfigId());
        if (CollectionUtils.isEmpty(vo.getChatIdList())) {
            arg.setChatIdList(GsonUtil.getGson().fromJson(qywxGroupCodeEntity.getChatIdList(),new TypeToken<List<String>>(){}.getType()));
        }
        if (vo.getScene() == null) {
            arg.setScene(qywxGroupCodeEntity.getScene());
        }
        boolean updateResult = qywxManager.updateQywxGroupContactMeConfig(accessToken, arg);
        if (updateResult) {
            QywxGroupCodeEntity updateEntity = BeanUtil.copy(vo,QywxGroupCodeEntity.class);
            if (CollectionUtils.isNotEmpty(vo.getChatIdList())) {
                updateEntity.setChatIdList(GsonUtil.getGson().toJson(vo.getChatIdList()));
            }
            qywxGroupCodeDAO.updateQywxGroupCode(updateEntity);
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
    }

    @Override
    public Result<EditObjectGroupResult> editQywxGroupCodeGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QywxGroupCodeServiceImpl.editQywxGroupCodeGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("QywxGroupCodeServiceImpl.editQywxGroupCodeGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.QY_GROUP_CODE.getType());
    }

    @Override
    public Result<Void> deleteQywxGroupCodeGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QywxGroupCodeServiceImpl.deleteQywxGroupCodeGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.QY_GROUP_CODE.getType());
    }

    @Override
    public Result<Void> setQywxGroupCodeGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("QywxGroupCodeServiceImpl.setQywxGroupCodeGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        List<QywxGroupCodeEntity> qywxGroupCodeEntityList = qywxGroupCodeDAO.getByIds(arg.getObjectIdList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(qywxGroupCodeEntityList)) {
            return Result.newError(SHErrorCode.QYWX_GROUP_QRCODE_NOT_FOUND);
        }
        if (qywxGroupCodeEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_GROUP_QRCODE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.QY_GROUP_CODE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.QY_GROUP_CODE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        return Result.newSuccess();
    }
    
    @Override
    public Result<Void> topQywxGroupCode(String ea, Integer fsUserId, TopMaterialArg arg) {
        QywxGroupCodeEntity entity = qywxGroupCodeDAO.queryById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.QYWX_GROUP_QRCODE_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.QY_GROUP_CODE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> cancelTopQywxGroupCode(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QY_GROUP_CODE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.QY_GROUP_CODE.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addQywxGroupCodeGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.QY_GROUP_CODE.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listQywxGroupCodeGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.QY_GROUP_CODE.getType(), null, null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(qywxGroupCodeDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(qywxGroupCodeDAO.queryAccessibleCount(ea, groupIdList, fsUserId));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(qywxGroupCodeDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(qywxGroupCodeDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<List<String>> getQywxCodeGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }

    @Override
    public Result<List<QywxGroupChatList>> queryChatListInfo(String ea,String id) {
        List<QywxGroupChatList> qywxGroupChatLists = Lists.newArrayList();
        QywxGroupCodeEntity qywxGroupCodeEntity = qywxGroupCodeDAO.queryById(id);
        if (qywxGroupCodeEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        List<String> chatIdList = GsonUtil.getGson().fromJson(qywxGroupCodeEntity.getChatIdList(),new TypeToken<List<String>>(){}.getType());
        com.fxiaoke.crmrestapi.common.result.Result<com.fxiaoke.crmrestapi.common.data.Page<ObjectData>> pageResult = wechatGroupObjDescribeManager.listGroupObjectDataByIdsLimited(ei,chatIdList);
        if (pageResult.isSuccess() && pageResult.getData() != null) {
            List<ObjectData> dataList = pageResult.getData().getDataList();
            if(CollectionUtils.isNotEmpty(dataList)) {
                for (ObjectData objectData : dataList) {
                    QywxGroupChatList qywxGroupChatList = new QywxGroupChatList();
                    qywxGroupChatList.setChatId(objectData.getString("chat_id"));
                    qywxGroupChatList.setChatName(objectData.getString("name"));
                    qywxGroupChatList.setCount(objectData.getInt("member_count"));
                    qywxGroupChatList.setGroupOwner(objectData.getString("leader_name"));
                    qywxGroupChatList.setCreateTime(objectData.getCreateTime());
                    qywxGroupChatList.setSourceType(QywxGroupListSourceTypeEnum.START_BIND.getType());
                    if (qywxGroupCodeEntity.getAutoCreateRoom() == 1 && qywxGroupChatList.getChatName().startsWith(qywxGroupCodeEntity.getRoomBaseName())) {
                        qywxGroupChatList.setSourceType(QywxGroupListSourceTypeEnum.AUTO_ADD.getType());
                    }
                    qywxGroupChatLists.add(qywxGroupChatList);
                }
            }
        }
        return Result.newSuccess(qywxGroupChatLists);
    }
}