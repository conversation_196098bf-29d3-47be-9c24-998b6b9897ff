package com.facishare.marketing.provider.service.marketingplugin;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.marketingplugin.CouponTemplateResult;
import com.facishare.marketing.api.result.marketingplugin.CouponTemplateStatisticResult;
import com.facishare.marketing.api.result.marketingplugin.StockCouponListResult;
import com.facishare.marketing.api.service.marketingplugin.CouponTemplateService;
import com.facishare.marketing.api.util.AmountUtil;
import com.facishare.marketing.api.vo.marketingplugin.CouponTemplateVO;
import com.facishare.marketing.api.vo.marketingplugin.ListCouponTemplateVO;
import com.facishare.marketing.api.vo.marketingplugin.ListStockTemplateVO;
import com.facishare.marketing.api.vo.marketingplugin.UpdateCouponTemplateVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.CouponTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.marketingplugin.CouponTemplateDAO;
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.ObjectGroupRelationEntity;
import com.facishare.marketing.provider.entity.marketingplugin.CouponTemplateCount;
import com.facishare.marketing.provider.entity.marketingplugin.CouponTemplateEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.manager.CouponTemplateManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.ObjectGroupManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/9/8 16:35
 */
@Service("couponTemplateService")
@Slf4j
public class CouponTemplateServiceImpl implements CouponTemplateService {

    @Autowired
    private CouponTemplateDAO couponTemplateDAO;

    @Autowired
    private CouponTemplateManager couponTemplateManager;

    @Autowired
    private WeChatCouponDAO weChatCouponDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @ReloadableProperty("wx.coupon.ea.list")
    private String eaList;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Override
    public Result<CouponTemplateResult> queryCouponTemplateInfo(String id) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(id),I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_108));
        CouponTemplateEntity couponTemplateEntity = couponTemplateDAO.queryCouponTemplateInfo(id);
        if (couponTemplateEntity == null){
            return Result.newSuccess();
        }
        CouponTemplateResult couponTemplateResult = getCouponTemplateResult(couponTemplateEntity);
        return Result.newSuccess(couponTemplateResult);
    }

    private CouponTemplateResult getCouponTemplateResult(CouponTemplateEntity couponTemplateEntity) {
        CouponTemplateResult couponTemplateResult = BeanUtil.copy(couponTemplateEntity, CouponTemplateResult.class);
        if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(couponTemplateEntity.getStockType())){
            couponTemplateResult.setType(CouponTypeEnum.EXCHANGE_COUPON.getType());
        } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(couponTemplateEntity.getStockType())){
            couponTemplateResult.setType(CouponTypeEnum.DISCOUNT_COUPON.getType());
        } else if (CouponTypeEnum.NORMAL_COUPON.getName().equals(couponTemplateEntity.getStockType())){
            couponTemplateResult.setType(CouponTypeEnum.NORMAL_COUPON.getType());
        }
        couponTemplateResult.setTransactionMinimum(AmountUtil.changeF2Y(couponTemplateEntity.getTransactionMinimum()));
        if (couponTemplateEntity.getDiscountAmount() != null) {
            couponTemplateResult.setDiscountAmount(AmountUtil.changeF2Y(couponTemplateEntity.getDiscountAmount()));
        }
        if (couponTemplateEntity.getExchangePrice() != null) {
            couponTemplateResult.setExchangePrice(AmountUtil.changeF2Y(couponTemplateEntity.getExchangePrice()));
        }
        if (couponTemplateEntity.getDiscountPercent() != null) {
            couponTemplateResult.setDiscountPercent(AmountUtil.changeCount(couponTemplateEntity.getDiscountPercent()));
        }
        if (StringUtils.isNotBlank(couponTemplateEntity.getMerchantLogoUrl())) {
            couponTemplateResult.setMerchantLogoUrl(fileV2Manager.getUrlByPath(couponTemplateEntity.getMerchantLogoUrl(),couponTemplateEntity.getEa(),false));
        }
        if (StringUtils.isNotBlank(couponTemplateEntity.getCouponImageUrl())) {
            couponTemplateResult.setCouponImageUrl(fileV2Manager.getUrlByPath(couponTemplateEntity.getCouponImageUrl(),couponTemplateEntity.getEa(),false));
        }
        return couponTemplateResult;
    }

    @Override
    public Result<PageResult<CouponTemplateResult>> queryCouponTemplatePage(String ea, ListCouponTemplateVO vo) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea),I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_147));
        Preconditions.checkArgument(vo.getPageSize() != null,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_148));
        Preconditions.checkArgument(vo.getPageNum() != null,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_149));
        PageResult<CouponTemplateResult> pageResult = new PageResult<>();
        List<CouponTemplateResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<CouponTemplateEntity> couponTemplateEntities = couponTemplateDAO.queryCouponTemplateListByEa(ea,vo.getStockName(), page);
        if (CollectionUtils.isEmpty(couponTemplateEntities)){
            pageResult.setResult(queryLiveListResult);
            return Result.newSuccess(pageResult);
        }
        //进行数据处理
        for (CouponTemplateEntity couponTemplateEntity : couponTemplateEntities) {
            CouponTemplateResult templateResult = getCouponTemplateResult(couponTemplateEntity);
            queryLiveListResult.add(templateResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(queryLiveListResult);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> saveCouponTemplate(CouponTemplateVO vo) {
        Preconditions.checkArgument(vo != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getEa()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        Preconditions.checkArgument(vo.getType() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_175));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getStockName()), I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_176));
        Preconditions.checkArgument(vo.getTransactionMinimum() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_177));
        String id = couponTemplateManager.mergeCouponTemplate(vo);
        mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(vo.getEa(), Integer.parseInt(vo.getOperator()), ObjectTypeEnum.COUPON_TEMPLATE.getType(), id, OperateTypeEnum.ADD);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteCouponTemplate(String ea, Integer userId, String id, Integer status) {
        Preconditions.checkArgument(status != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_185));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(id), I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_108));
        couponTemplateDAO.deleteCouponTemplate(id,status);
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.COUPON_TEMPLATE.getType(), Collections.singletonList(id));
        mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(ea, userId, ObjectTypeEnum.COUPON_TEMPLATE.getType(), id, OperateTypeEnum.DELETE);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateCouponTemplate(UpdateCouponTemplateVO vo) {
        Preconditions.checkArgument(vo != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getId()), I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_108));
        CouponTemplateEntity couponTemplateEntity = couponTemplateDAO.queryCouponTemplateInfo(vo.getId());
        if (couponTemplateEntity == null){
            return Result.newError(SHErrorCode.NO_COUPON_TEMPLATE);
        }
        if (vo.getTransactionMinimum() != null && Objects.equals(CouponTypeEnum.NORMAL_COUPON.getName(), couponTemplateEntity.getStockType())) {
            Preconditions.checkArgument(vo.getDiscountAmount() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_203));
        } else if (vo.getTransactionMinimum() != null && Objects.equals(CouponTypeEnum.DISCOUNT_COUPON.getName(), couponTemplateEntity.getStockType())) {
            Preconditions.checkArgument(vo.getDiscountPercent() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_205));
        } else if (vo.getTransactionMinimum() != null && Objects.equals(CouponTypeEnum.EXCHANGE_COUPON.getName(), couponTemplateEntity.getStockType())) {
            Preconditions.checkArgument(vo.getExchangePrice() != null, I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_207));
        }
        couponTemplateManager.updateTemplate(vo);
        mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(vo.getEa(), vo.getUserId(), ObjectTypeEnum.COUPON_TEMPLATE.getType(), vo.getId(), OperateTypeEnum.EDIT);
        return Result.newSuccess();
    }

    @Override
    public Result<CouponTemplateStatisticResult> queryStatisticCouponTemplate(String templateId,String ea) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(templateId), I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_108));
        CouponTemplateStatisticResult result = new CouponTemplateStatisticResult();
        result.setReceiveCount(0);
        result.setRelateMarketingCount(0);
        result.setStockCount(0);
        //根据模板id 获取统计数据(市场活动数, 批次数)
        CouponTemplateCount couponTemplateCount = weChatCouponDAO.queryStaticCountByTemplate(templateId);
        if (couponTemplateCount != null) {
            result.setRelateMarketingCount(couponTemplateCount.getRelateMarketingCount());
            result.setStockCount(couponTemplateCount.getStockCount());
        }

        //获取领取数
        int receiveCount = couponTemplateManager.queryReceiveCount(templateId,ea);
        result.setReceiveCount(receiveCount);
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<StockCouponListResult>> queryStockList(ListStockTemplateVO vo, String ea) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea),I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_147));
        Preconditions.checkArgument(vo.getPageSize() != null,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_148));
        Preconditions.checkArgument(vo.getPageNum() != null,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_149));
        PageResult<StockCouponListResult> pageResult = new PageResult<>();
        List<StockCouponListResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<WechatCouponEntity> wechatCouponEntities = weChatCouponDAO.queryStockListByTemplateId(vo.getTemplateId(), page);
        if (CollectionUtils.isEmpty(wechatCouponEntities)){
            pageResult.setResult(queryLiveListResult);
            return Result.newSuccess(pageResult);
        }
        //数据处理
        wechatCouponEntities.forEach(wechatCouponEntity -> {
            StockCouponListResult stockCouponListResult = new StockCouponListResult();
            stockCouponListResult.setChannel(wechatCouponEntity.getChannel());
            stockCouponListResult.setMarketingEventId(wechatCouponEntity.getMarketingEventId());
            stockCouponListResult.setMaxCount(wechatCouponEntity.getMaxCoupons());
            stockCouponListResult.setStatus(wechatCouponEntity.getStatus());
            stockCouponListResult.setMarketingName(couponTemplateManager.getMarketingDetailName(wechatCouponEntity.getMarketingEventId(),ea));
            stockCouponListResult.setReceiveCount(couponTemplateManager.getCouponCount(ea,wechatCouponEntity.getStockId(),1));
            stockCouponListResult.setUsedCount(couponTemplateManager.getCouponCount(ea,wechatCouponEntity.getStockId(),2));
            queryLiveListResult.add(stockCouponListResult);
        });
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(queryLiveListResult);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Boolean> queryEnterCouponEaList(String ea) {
        boolean flag = false;
        String[] split = eaList.split(",");
        List<String> eas = Arrays.asList(split);
        if (eas.contains(ea)) {
            flag = true;
        }
        return Result.newSuccess(flag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<EditObjectGroupResult> editCouponTemplateGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("CouponTemplateServiceImpl.editCouponTemplateGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("CouponTemplateServiceImpl.editCouponTemplateGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.COUPON_TEMPLATE.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteCouponTemplateGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("CouponTemplateServiceImpl.deleteCouponTemplateGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.COUPON_TEMPLATE.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setCouponTemplateGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("CouponTemplateServiceImpl.setHexagonGroupBatch only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        List<CouponTemplateEntity> couponTemplateEntityList = couponTemplateDAO.queryByIdList(ea, arg.getObjectIdList());
        if (CollectionUtils.isEmpty(couponTemplateEntityList)) {
            return Result.newError(SHErrorCode.NO_COUPON_TEMPLATE);
        }
        if (couponTemplateEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_COUPON_TEMPLATE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.COUPON_TEMPLATE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.COUPON_TEMPLATE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteCouponTemplateBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        couponTemplateDAO.deleteCouponTemplateBatch(arg.getIdList());
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.COUPON_TEMPLATE.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.COUPON_TEMPLATE.getType(), arg.getIdList());
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topCouponTemplate(String ea, Integer fsUserId, TopMaterialArg arg) {
        List<CouponTemplateEntity> entityList = couponTemplateDAO.queryByIdList(ea, Collections.singletonList(arg.getObjectId()));
        if (CollectionUtils.isEmpty(entityList)) {
            return Result.newError(SHErrorCode.NO_COUPON_TEMPLATE);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.COUPON_TEMPLATE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopCouponTemplate(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.COUPON_TEMPLATE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.COUPON_TEMPLATE.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addCouponTemplateGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.COUPON_TEMPLATE.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listCouponTemplateGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.COUPON_TEMPLATE.getType(), null, null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.values()) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(couponTemplateDAO.queryUnGroupAndCreateByMeCount(ea, String.valueOf(fsUserId)));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(couponTemplateDAO.queryAccessibleCount(ea, groupIdList, String.valueOf(fsUserId)));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(couponTemplateDAO.queryCountCreateByMe(ea, String.valueOf(fsUserId)));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(couponTemplateDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }
}