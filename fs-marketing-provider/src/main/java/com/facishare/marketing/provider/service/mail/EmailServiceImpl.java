/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.mail;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.mail.CheckDomainConfigResult;
import com.facishare.marketing.api.result.mail.GetCompanyEmailBaseInfoResult;
import com.facishare.marketing.api.result.mail.GetDetailByMarketingActivityIdResult;
import com.facishare.marketing.api.result.mail.GetDomainDetailResult;
import com.facishare.marketing.api.result.mail.GetSendReplyDataByIdResult;
import com.facishare.marketing.api.result.mail.GetTaskDetailByIdResult;
import com.facishare.marketing.api.result.mail.ListMailMarketingResult;
import com.facishare.marketing.api.result.mail.PageQueryMailTemplateResult;
import com.facishare.marketing.api.result.mail.QueryAccountInfo;
import com.facishare.marketing.api.result.mail.QueryClickLinkDetailResult;
import com.facishare.marketing.api.result.mail.QueryFilterAddressResult;
import com.facishare.marketing.api.result.mail.QueryLeadTransforInfoResult;
import com.facishare.marketing.api.result.mail.QueryMailTemplateDetailResult;
import com.facishare.marketing.api.result.mail.QueryMailUserSendDetailResult;
import com.facishare.marketing.api.result.mail.QuerySendDetailsByTypeResult;
import com.facishare.marketing.api.result.mail.QuerySendErrorMailResult;
import com.facishare.marketing.api.result.mail.QuerySendReplyDataResult;
import com.facishare.marketing.api.service.mail.MailService;
import com.facishare.marketing.api.service.shanshan.ShanShanEditService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.mail.*;
import com.facishare.marketing.common.contstant.mail.MailConstant;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.mail.MailApiUserTypeEnum;
import com.facishare.marketing.common.enums.mail.MailDomainStatusEnum;
import com.facishare.marketing.common.enums.mail.MailEaStatisticsDateTypeEnum;
import com.facishare.marketing.common.enums.mail.MailOpenAndClickTrackTypeEnum;
import com.facishare.marketing.common.enums.mail.MailScheduleTypeEnum;
import com.facishare.marketing.common.enums.mail.MailSendReplyTypeEnum;
import com.facishare.marketing.common.enums.mail.MailSendStatusEnum;
import com.facishare.marketing.common.enums.mail.MailSendSubStatusEnum;
import com.facishare.marketing.common.enums.mail.MailSendUserStatusEnum;
import com.facishare.marketing.common.enums.mail.SendReplyStatusEnum;
import com.facishare.marketing.common.enums.mail.TemplateDataTypeEnum;
import com.facishare.marketing.common.enums.mail.WebHookEventEnum;
import com.facishare.marketing.common.exception.BusinessException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.MailLinkContent;
import com.facishare.marketing.common.typehandlers.value.MailWebHookCallBackDetail;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.bo.email.EmailOpenClickStatisticBO;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.mail.*;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.SceneTriggerEntity;
import com.facishare.marketing.provider.entity.TriggerTaskInstanceEntity;
import com.facishare.marketing.provider.entity.mail.*;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity;
import com.facishare.marketing.provider.innerArg.CreateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.innerArg.UpdateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.mail.MailManager.EmailMarketingDetailInfo;
import com.facishare.marketing.provider.manager.mail.SendCloudError;
import com.facishare.marketing.provider.manager.mail.result.*;
import com.facishare.marketing.provider.manager.mail.result.AddApiUserResp.AddApiUserResult;
import com.facishare.marketing.provider.manager.mail.result.AddMailDomainResp.DomainInfo;
import com.facishare.marketing.provider.manager.mail.result.CheckMailDomainResp.CheckMailResult;
import com.facishare.marketing.provider.manager.mail.result.QueryEmailStatusResp.VoList;
import com.facishare.marketing.provider.manager.mail.result.QueryOpenAndClickListResp.OpenAndClickInfo;
import com.facishare.marketing.provider.manager.mail.result.StatDayStatisticsResp.StatDayDetail;
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.common.mq.sender.RequestBufferSender;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.util.ContextUtil;
import com.facishare.marketing.provider.util.MarketingJobUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zhengh on 2020/6/2.
 */
@Service(value = "mailService")
@Slf4j
public class EmailServiceImpl implements MailService {

    @Autowired
    private MailAccountDAO mailAccountDAO;

    @Autowired
    private MailDomainDAO mailDomainDAO;

    @Autowired
    private MailManager mailManager;

    @Autowired
    private MailSendReplyDAO mailSendReplyDAO;

    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;

    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private BrowserUserDao browserUserDao;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private MarketingRecordActionSender marketingRecordActionSender;

    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;

    @Autowired
    private MailTemplateDAO mailTemplateDAO;

    @Autowired
    private MailContentLinkDAO mailContentLinkDAO;

    @Autowired
    private MailWebHookCallBackDAO mailWebHookCallBackDAO;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private MailEaStatisticsDAO mailEaStatisticsDAO;

    @Autowired
    private MailTaskStatisticsDAO mailTaskStatisticsDAO;

    @Autowired
    private MailSendErrorAddressDAO mailSendErrorAddressDAO;

    @Autowired
    private MailFilterDetailDAO mailFilterDetailDAO;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private RequestBufferSender requestBufferSender;


    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MailSendTaskResultDAO mailSendTaskResultDAO;

    @Autowired
    private IdempotentRecordManager idempotentRecordManager;

    @Autowired
    private EmailSendRecordDetailObjManager emailSendRecordDetailObjManager;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Autowired
    private MarketingEventCommonSettingManager marketingEventCommonSettingManager;

    @Autowired
    private ShanShanEditService shanShanEditService;

    @Autowired
    private TriggerInstanceManager triggerInstanceManager;
    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;
    @Autowired
    private SceneTriggerDao sceneTriggerDao;
    @Autowired
    private TriggerTaskInstanceManager triggerTaskInstanceManager;

    @ReloadableProperty("host")
    private String host;

    private final static String DELIVERED_EMAIL_TASK_KEY = "mk:delivered:email:key";
    private final static List<I18nKeyEnum> TITLE_LIST = new ArrayList<>();

    static {
        TITLE_LIST.add(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70);
        TITLE_LIST.add(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_72);
        TITLE_LIST.add(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_256);
        TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_275);
        TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_268);
        TITLE_LIST.add(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_259);
        TITLE_LIST.add(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_269);
        TITLE_LIST.add(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_261);
        TITLE_LIST.add(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_262);
        TITLE_LIST.add(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_263);
        TITLE_LIST.add(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_264);
    }

    @Override
    public Result createEmailDomain(CreateEmailDomainVO vo) {
        BaseMailVO baseMailVO = new BaseMailVO(vo.getApiUser(), vo.getApiKey());
        // 1.校验api_info
        QueryApiUserListResp queryApiUserListResp = mailManager.queryApiUserList(baseMailVO);
        if (queryApiUserListResp == null || CollectionUtils.isEmpty(queryApiUserListResp.getDataList())) {
            log.warn("EmailServiceImpl.createEmailDomain queryApiUserListResp is null");
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        boolean existApiUser = queryApiUserListResp.getDataList().stream().anyMatch(data -> vo.getApiUser().equals(data.getName()));
        if (!existApiUser) {
            log.warn("EmailServiceImpl.createEmailDomain ApiUser not exist vo:{}, queryApiUserListResp.getDataList():{}", vo, queryApiUserListResp.getDataList());
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        // 2.根据默认apiUser创建域名
        baseMailVO.setDomain(vo.getDomain());
        AddMailDomainResp addMailDomainResp = mailManager.addMailDomain(baseMailVO);
        if (addMailDomainResp == null) {
            log.warn("EmailServiceImpl.createEmailDomain addMailDomainResp is null vo:{}", vo);
            return Result.newError(SHErrorCode.ADD_MAIL_DOMAIN_FAILED);
        }
        // 3.保存企业默认api_user
        MailAccountEntity saveData = new MailAccountEntity();
        saveData.setId(UUIDUtil.getUUID());
        saveData.setEa(vo.getEa());
        saveData.setApiUser(vo.getApiUser());
        saveData.setApiKey(vo.getApiKey());
        saveData.setCreator(vo.getUserId());
        saveData.setType(MailApiUserTypeEnum.DEFAULT.getType());
        mailAccountDAO.insert(saveData);
        emailSendRecordDetailObjManager.getOrCreateObjDescribe(vo.getEa());
        // 4.保存企业当前发信域名
        MailDomainEntity mailDomainEntity = new MailDomainEntity();
        mailDomainEntity.setId(UUIDUtil.getUUID());
        mailDomainEntity.setEa(vo.getEa());
        mailDomainEntity.setName(vo.getDomain());
        mailDomainEntity.setStatus(MailDomainStatusEnum.UNAVAILABLE.getStatus());
        mailDomainDAO.insert(mailDomainEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<GetCompanyEmailBaseInfoResult> getCompanyEmailBaseInfo(String ea) {

        GetCompanyEmailBaseInfoResult result = new GetCompanyEmailBaseInfoResult();
        // 默认api_user
        MailAccountEntity defaultAccountEntity = mailManager.getUserAccount(ea, null);
        result.setDefaultApiUser(defaultAccountEntity != null ? defaultAccountEntity.getApiUser() : null);

        // 触发api_user
        MailAccountEntity triggerAccountEntity = mailManager.getUserAccount(ea, MailApiUserTypeEnum.TRIGGER.getType());
        result.setTriggerApiUser(triggerAccountEntity != null ? triggerAccountEntity.getApiUser() : null);

        // 批量api_user
        MailAccountEntity batchAccountEntity = mailManager.getUserAccount(ea, MailApiUserTypeEnum.BATCH.getType());
        result.setBatchApiUser(batchAccountEntity != null ? batchAccountEntity.getApiUser() : null);

        result.setCompleteAllConfig(false);

        if (defaultAccountEntity != null && triggerAccountEntity != null && batchAccountEntity != null) {
            result.setCompleteAllConfig(true);
        }

        // 当前域名(目前只有一个)
        MailDomainEntity mailDomainEntity = mailDomainDAO.getUniqueByEa(ea);
        result.setDomain(mailDomainEntity != null ? mailDomainEntity.getName() : null);
        result.setDomainStatus(mailDomainEntity != null ? mailDomainEntity.getStatus() : null);

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetDomainDetailResult> getDomainDetail(String ea) {
        // 获取当前域名
        MailDomainEntity mailDomainEntity = mailDomainDAO.getUniqueByEa(ea);
        if (mailDomainEntity == null) {
            log.warn("EmailServiceImpl.getDomainDetail error mailDomainEntity is null");
            return Result.newError(SHErrorCode.MAIL_DOMAIN_NOT_EXIST);
        }
        // 查询域名详情
        MailAccountEntity mailAccountEntity = mailManager.getUserAccount(ea, null);
        if (mailAccountEntity == null) {
            log.warn("EmailServiceImpl.getDomainDetail mailAccountEntity is null");
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        QueryDomainListVO queryDomainListVO = new QueryDomainListVO();
        queryDomainListVO.setApiUser(mailAccountEntity.getApiUser());
        queryDomainListVO.setApiKey(mailAccountEntity.getApiKey());
        queryDomainListVO.setDomain(mailDomainEntity.getName());
        List<DomainInfo> domainInfoList = mailManager.queryDomainList(queryDomainListVO);
        if (CollectionUtils.isEmpty(domainInfoList)) {
            log.warn("EmailServiceImpl.getDomainDetail error domainInfoList is empty");
            return Result.newError(SHErrorCode.MAIL_DOMAIN_NOT_EXIST);
        }
        GetDomainDetailResult result = BeanUtil.copy(domainInfoList.get(0), GetDomainDetailResult.class);
        return Result.newSuccess(result);
    }

    @Override
    public Result<CheckDomainConfigResult> checkDomainConfig(String ea) {
        // 获取当前域名
        CheckDomainConfigResult result = new CheckDomainConfigResult();
        MailDomainEntity mailDomainEntity = mailDomainDAO.getUniqueByEa(ea);
        if (mailDomainEntity == null) {
            log.warn("EmailServiceImpl.checkDomainConfig error mailDomainEntity is null");
            return Result.newError(SHErrorCode.MAIL_DOMAIN_NOT_EXIST);
        }
        // 若已通过校验直接返回
        if (mailDomainEntity.getStatus().equals(MailDomainStatusEnum.AVAILABLE.getStatus())) {
            result.setIsConfigSuccess(true);
            result.setDKIM(true);
            result.setMX(true);
            result.setSPF(true);
            result.setDMARC(true);
            return Result.newSuccess(result);
        }
        // 查询域名详情
        MailAccountEntity mailAccountEntity = mailManager.getUserAccount(ea, null);
        if (mailAccountEntity == null) {
            log.warn("EmailServiceImpl.getDomainDetail mailAccountEntity is null");
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        BaseMailVO baseMailVO = new BaseMailVO(mailAccountEntity.getApiUser(), mailAccountEntity.getApiKey());
        baseMailVO.setDomain(mailDomainEntity.getName());
        List<CheckMailResult> checkMailResultList = mailManager.checkMailDomain(baseMailVO);
        if (CollectionUtils.isEmpty(checkMailResultList)) {
            log.warn("EmailServiceImpl.checkDomainConfig error checkMailResultList is empty");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        CheckMailResult checkMailResult = checkMailResultList.get(0);
        result.setIsConfigSuccess(checkMailResult.getIsConfigSuccess());
        result.setDKIM(checkMailResult.getConfigInfo().getDkim());
        result.setMX(checkMailResult.getConfigInfo().getMx());
        result.setSPF(checkMailResult.getConfigInfo().getSpf());
        result.setDMARC(checkMailResult.getConfigInfo().getDmarc());
        if (!mailDomainEntity.getStatus().equals(MailDomainStatusEnum.AVAILABLE.getStatus()) && checkMailResult.getIsConfigSuccess()) {
            // 更新域名状态
            mailDomainDAO.updateDomainInfo(mailDomainEntity.getId(), null, MailDomainStatusEnum.AVAILABLE.getStatus());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result updateDomainConfig(UpdateDomainConfigVO vo) {
        MailDomainEntity mailDomainEntity = mailDomainDAO.getUniqueByEa(vo.getEa());
        if (mailDomainEntity == null) {
            log.warn("EmailServiceImpl.updateDomainConfig error mailDomainEntity is null");
            return Result.newError(SHErrorCode.MAIL_DOMAIN_NOT_EXIST);
        }
        if (mailDomainEntity.getStatus().equals(MailDomainStatusEnum.AVAILABLE.getStatus())) {
            return Result.newError(SHErrorCode.MAIL_DOMAIN_AVAILABLE_NOT_ALLOW_UPDATE);
        }
        // 更新设置
        UpdateMainDomainVO updateMainDomainVO = new UpdateMainDomainVO();
        updateMainDomainVO.setOldDomain(mailDomainEntity.getName());
        updateMainDomainVO.setNewDomain(vo.getNewDomain());
        updateMainDomainVO.setEa(vo.getEa());
        UpdateMainDomainResp updateMainDomainResp = mailManager.updateMainDomain(updateMainDomainVO);
        if (updateMainDomainResp == null) {
            log.warn("EmailServiceImpl.updateDomainConfig error updateMainDomainResp is empty");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        mailDomainDAO.updateDomainInfo(mailDomainEntity.getId(), vo.getNewDomain(), MailDomainStatusEnum.UNAVAILABLE.getStatus());
        return Result.newSuccess();
    }

    @Override
    public Result createApiUser(String ea, Integer fsUser) {
        // 获取当前域名
        MailDomainEntity mailDomainEntity = mailDomainDAO.getUniqueByEa(ea);
        if (mailDomainEntity == null) {
            log.warn("EmailServiceImpl.createApiUser error mailDomainEntity is null");
            return Result.newError(SHErrorCode.MAIL_DOMAIN_NOT_EXIST);
        }
        if (!mailDomainEntity.getStatus().equals(MailDomainStatusEnum.AVAILABLE.getStatus())) {
            return Result.newError(SHErrorCode.MAIL_DOMAIN_CHECK_FAILED);
        }
        MailAccountEntity mailAccountEntity = mailManager.getUserAccount(ea, null);
        if (mailAccountEntity == null) {
            log.warn("EmailServiceImpl.getDomainDetail mailAccountEntity is null");
            return Result.newError(SHErrorCode.API_USER_NOT_FOUND);
        }
        // 使用默认apiUser创建新apiUser
        // 创建触发型
        MailAccountEntity triggerAccount = mailManager.getUserAccount(ea, MailApiUserTypeEnum.TRIGGER.getType());
        if (triggerAccount == null) {
            AddApiUserVO addApiUserVO = new AddApiUserVO();
            StringJoiner triggerName = new StringJoiner("_");
            triggerName.add(MailConstant.API_KEY_BASE);
            triggerName.add(System.currentTimeMillis() + "");
            triggerName.add(MailApiUserTypeEnum.TRIGGER.getType() + "");
            addApiUserVO.setName(triggerName.toString());
            addApiUserVO.setEmailType(MailApiUserTypeEnum.TRIGGER.getType());
            addApiUserVO.setDomain(mailDomainEntity.getName());
            addApiUserVO.setEa(ea);
            AddApiUserResult addApiUserResult = mailManager.addApiUser(addApiUserVO);
            if (addApiUserResult == null) {
                log.warn("EmailServiceImpl.createApiUser  addApiUserResp error trigger type");
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            MailAccountEntity saveData = new MailAccountEntity();
            saveData.setId(UUIDUtil.getUUID());
            saveData.setEa(ea);
            saveData.setApiUser(addApiUserResult.getName());
            saveData.setApiKey(addApiUserResult.getNameKey());
            saveData.setDomain(addApiUserResult.getDomainName());
            saveData.setCreator(fsUser);
            saveData.setType(MailApiUserTypeEnum.TRIGGER.getType());
            mailAccountDAO.insert(saveData);
        }
        // 创建群发型
        MailAccountEntity batchAccount = mailManager.getUserAccount(ea, MailApiUserTypeEnum.BATCH.getType());
        if (batchAccount == null) {
            AddApiUserVO addApiUserVO = new AddApiUserVO();
            StringJoiner batchName = new StringJoiner("_");
            batchName.add(MailConstant.API_KEY_BASE);
            batchName.add(System.currentTimeMillis() + "");
            batchName.add(MailApiUserTypeEnum.BATCH.getType() + "");
            addApiUserVO.setName(batchName.toString());
            addApiUserVO.setEmailType(MailApiUserTypeEnum.BATCH.getType());
            addApiUserVO.setDomain(mailDomainEntity.getName());
            addApiUserVO.setEa(ea);
            AddApiUserResult addApiUserResult = mailManager.addApiUser(addApiUserVO);
            if (addApiUserResult == null) {
                log.warn("EmailServiceImpl.createApiUser addApiUserResp error batch type");
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            MailAccountEntity saveData = new MailAccountEntity();
            saveData.setId(UUIDUtil.getUUID());
            saveData.setEa(ea);
            saveData.setApiUser(addApiUserResult.getName());
            saveData.setApiKey(addApiUserResult.getNameKey());
            saveData.setDomain(addApiUserResult.getDomainName());
            saveData.setCreator(fsUser);
            saveData.setType(MailApiUserTypeEnum.BATCH.getType());
            mailAccountDAO.insert(saveData);
        }
        emailSendRecordDetailObjManager.getOrCreateObjDescribe(ea);
        // 创建webhook
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                createWebHook(ea);
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QuerySendReplyDataResult>> querySendReplyData(QuerySendReplyDataVO vo) {
        PageResult<QuerySendReplyDataResult> pageResult = new PageResult<>();
        List<QuerySendReplyDataResult> querySendReplyDataList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(querySendReplyDataList);

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MailSendReplyEntity> mailSendReplyEntityList = mailSendReplyDAO.querySendReplyByType(vo.getEa(), vo.getType(), vo.getName(), page);
        if (CollectionUtils.isEmpty(mailSendReplyEntityList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        mailSendReplyEntityList.forEach(data -> {
            QuerySendReplyDataResult querySendReplyDataResult = new QuerySendReplyDataResult();
            querySendReplyDataResult.setId(data.getId());
            querySendReplyDataResult.setType(data.getType());
            querySendReplyDataResult.setName(data.getName());
            querySendReplyDataResult.setAddress(data.getAddress());
            querySendReplyDataResult.setStatus(data.getStatus());
            querySendReplyDataResult.setDefaultValue(data.getDefaultValue());
            querySendReplyDataResult.setCreateTime(data.getCreateTime().getTime());
            querySendReplyDataResult.setUpdateTime(data.getUpdateTime().getTime());
            querySendReplyDataList.add(querySendReplyDataResult);
        });
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result addSendReplyData(AddSendReplyDataVO vo) {
        MailSendReplyEntity mailSendReplyEntity = new MailSendReplyEntity();
        mailSendReplyEntity.setId(UUIDUtil.getUUID());
        mailSendReplyEntity.setEa(vo.getEa());
        mailSendReplyEntity.setName(vo.getName());
        mailSendReplyEntity.setType(vo.getType());
        mailSendReplyEntity.setAddress(vo.getAddress());
        mailSendReplyEntity.setStatus(SendReplyStatusEnum.NORMAL.getStatus());
        mailSendReplyEntity.setDefaultValue(false);
        boolean insertSuccess = mailSendReplyDAO.insert(mailSendReplyEntity) > 0;
        if (insertSuccess && MailSendReplyTypeEnum.SENDER.getType().equals(vo.getType())) {
            integralServiceManager.asyncRegisterMaterial(vo.getEa(), CategoryApiNameConstant.MAIL_BOX, mailSendReplyEntity.getId(), vo.getAddress());
        }
        return Result.newSuccess();
    }

    @Override
    public Result updateSendReplyData(UpdateSendReplyDataVO vo) {
        MailSendReplyEntity mailSendReplyEntity = mailSendReplyDAO.getById(vo.getId());
        if (mailSendReplyEntity == null) {
            log.warn("EmailServiceImpl.updateSendReplyData error mailSendReplyEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (vo.getDefaultValue() != null && vo.getDefaultValue()) {
            // 若设置默认值则修改之前的默认值
            MailSendReplyEntity oldDefaultValue = mailSendReplyDAO.getDefaultValueByType(vo.getEa(), mailSendReplyEntity.getType());
            if (oldDefaultValue != null) {
                mailSendReplyDAO.updateById(oldDefaultValue.getId(), null, null, null, false);
            }
        }
        boolean updateSuccess = mailSendReplyDAO.updateById(mailSendReplyEntity.getId(), vo.getName(), vo.getAddress(), vo.getStatus(), vo.getDefaultValue()) > 0;
        if (updateSuccess && MailSendReplyTypeEnum.SENDER.getType().equals(mailSendReplyEntity.getType())) {
            boolean isDeleteAction = vo.getStatus() != null && vo.getStatus() == 99;
            if (isDeleteAction) {
                integralServiceManager.asyncRemoveMaterial(mailSendReplyEntity.getEa(), CategoryApiNameConstant.MAIL_BOX, mailSendReplyEntity.getId());
            } else {
                integralServiceManager.asyncRegisterMaterial(mailSendReplyEntity.getEa(), CategoryApiNameConstant.MAIL_BOX, mailSendReplyEntity.getId(), vo.getAddress());
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<QueryLeadTransforInfoResult> queryLeadTransforInfo(QueryLeadTransforInfoVO vo) {
        // 查询企业全部发送数据
        QueryLeadTransforInfoResult queryLeadTransforInfoResult = new QueryLeadTransforInfoResult();
        List<MailSendTaskEntity> mailSendTaskEntityList = mailSendTaskDAO.getTaskByEaWithoutBigField(vo.getEa());
        if (CollectionUtils.isEmpty(mailSendTaskEntityList)) {
            return Result.newSuccess(queryLeadTransforInfoResult);
        }
        // 计算时间差值
        StatDayStatisticsVO statDayStatisticsVO = new StatDayStatisticsVO();
        statDayStatisticsVO.setEa(vo.getEa());
        statDayStatisticsVO.setAggregate(1);
        statDayStatisticsVO.setLabelIdList(StringUtils.join(mailSendTaskEntityList.stream().filter(data -> data.getLabelId() != null).map(data -> data.getLabelId() + "").collect(Collectors.toList()), ";"));
        Long clickNum = 0L;
        Long deliveredNum = 0L;
        Long openNum = 0L;
        Long clueNum = 0L;
        Long clickUserNum = 0L;
        Long openUserNum = 0L;
        // 兼容多账号
        if (StringUtils.isBlank(statDayStatisticsVO.getLabelIdList())) {
            queryLeadTransforInfoResult.setClickNum(clickNum);
            queryLeadTransforInfoResult.setDeliveredNum(deliveredNum);
            queryLeadTransforInfoResult.setOpenNum(openNum);
            queryLeadTransforInfoResult.setOpenUserNum(openUserNum);
            queryLeadTransforInfoResult.setClickUserNum(clickUserNum);
            queryLeadTransforInfoResult.setLeadNum(clueNum);
            return Result.newSuccess(queryLeadTransforInfoResult);
        }
        Integer resentDay = null;
        if (vo.getDateType().equals(MailEaStatisticsDateTypeEnum.SEVEN_DAY.getType())) {
            resentDay = 7;
        } else if (vo.getDateType().equals(MailEaStatisticsDateTypeEnum.THIRTY_DAY.getType())) {
            resentDay = 30;
        }
        // 计算线索数
        List<String> marketingActivityIds = marketingActivityExternalConfigDao.getActivityIdByEaAndAssociateIdType(vo.getEa(), AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType());
        if (CollectionUtils.isNotEmpty(marketingActivityIds)) {
            marketingActivityIds = marketingActivityIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            Map<String, Integer> clueMap = customizeFormClueManager.batchCountClueNumByMarketingActivityIds(vo.getEa(), marketingActivityIds, resentDay != null, (resentDay != null ? resentDay : 0));
            if (MapUtils.isNotEmpty(clueMap)) {
                for (Map.Entry<String, Integer> entry : clueMap.entrySet()) {
                    clueNum = clueNum + entry.getValue();
                }
            }
        }
        queryLeadTransforInfoResult.setLeadNum(clueNum);
        MailEaStatisticsEntity mailEaStatisticsEntity = mailEaStatisticsDAO.getMailEaStatisticsByType(vo.getEa(), vo.getDateType());
        if (mailEaStatisticsEntity != null) {
            clickNum = mailEaStatisticsEntity.getClickNum();
            deliveredNum = mailEaStatisticsEntity.getDeliveredNum();
            openNum = mailEaStatisticsEntity.getOpenNum();
            openUserNum = mailEaStatisticsEntity.getOpenUserNum();
            clickUserNum = mailEaStatisticsEntity.getClickUserNum();
        }
        queryLeadTransforInfoResult.setClickNum(clickNum);
        queryLeadTransforInfoResult.setDeliveredNum(deliveredNum);
        queryLeadTransforInfoResult.setOpenNum(openNum);
        queryLeadTransforInfoResult.setOpenUserNum(openUserNum);
        queryLeadTransforInfoResult.setClickUserNum(clickUserNum);
        return Result.newSuccess(queryLeadTransforInfoResult);
    }

    @Override
    public Result<PageResult<ListMailMarketingResult>> listMailMarketing(String ea, Integer fsUserId, String keyword, Integer pageNum, Integer pageSize) {
        PageResult<ListMailMarketingResult> pageResult = new PageResult<>();
        List<ListMailMarketingResult> mailListResult = Lists.newArrayList();
        pageResult.setResult(mailListResult);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);

        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(keyword)) {
            marketingActivityArg.setName(keyword);
        }
        marketingActivityArg.setSpreadType(MarketingActivitySpreadTypeEnum.MAIL_GROUP_SEND.getSpreadType());
        //从营销活动对象获取数据
        marketingActivityArg.setPageSize(pageSize);
        marketingActivityArg.setPageNumber((pageNum - 1) * pageSize);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageMarketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (pageMarketingActivityList == null || org.apache.commons.collections.CollectionUtils.isEmpty(pageMarketingActivityList.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        Map<String, ObjectData> idToMarketingActivityDataMap = pageMarketingActivityList.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, Function.identity()));
        pageResult.setTotalCount(pageMarketingActivityList.getTotal());
        List<String> marketingActivityIds = pageMarketingActivityList.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        List<MailSendTaskEntity> pageEntities = mailSendTaskDAO.getPageMailMarketingByMarketingActivityIdsWithoutBigField(ea, marketingActivityIds);
        if (CollectionUtils.isEmpty(pageEntities)) {
            log.info("listMailMarketing.getPageMailMarketingByMarketingActivityIds find 0");
            return Result.newSuccess(pageResult);
        }
        log.info("listMailMarketing.getPageMailMarketingByMarketingActivityIds find size:{}", pageEntities.size());

        // 查询线索数
        Map<String, Integer> clueMap = Maps.newHashMap();
        Map<String, String> taskIdAndActivityMap = Maps.newHashMap();
        Map<String, String> taskIdFileNameMap = Maps.newHashMap();
        List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityList = marketingActivityExternalConfigDao.getByAssociateIds(pageEntities.stream().map(MailSendTaskEntity::getId).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(marketingActivityExternalConfigEntityList)) {
            taskIdAndActivityMap = marketingActivityExternalConfigEntityList.stream().collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getAssociateId,
                    MarketingActivityExternalConfigEntity::getMarketingActivityId, (v1, v2) -> v1));
            List<String> marketingIds = marketingActivityExternalConfigEntityList.stream().map(MarketingActivityExternalConfigEntity::getMarketingActivityId).collect(Collectors.toList());
            taskIdFileNameMap = marketingActivityExternalConfigEntityList.stream().collect(Collectors.toMap(o -> o.getAssociateId(),
                    o -> StringUtils.isNotEmpty(o.getExternalConfig().getMailServiceMarketingActivityVO().getFileName()) ? o.getExternalConfig().getMailServiceMarketingActivityVO().getFileName() : "", (v1, v2) -> v1));
            clueMap = customizeFormClueManager.batchCountClueNumByMarketingActivityIds(ea, marketingIds, false, 0);
        }

        // 查询统计数据
        List<String> taskIds = pageEntities.stream().map(MailSendTaskEntity::getId).collect(Collectors.toList());
        List<String> marketingEventIds = pageEntities.stream().map(MailSendTaskEntity::getMarketingEventId).collect(Collectors.toList());
        List<MarketingEventData> eventDataList = null;
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            eventDataList = marketingEventManager.listMarketingEventData(ea, fsUserId, marketingEventIds);
        }
        Map<String, MarketingEventData> marketingEventDataMap = null;
        if (CollectionUtils.isNotEmpty(eventDataList)) {
            marketingEventDataMap = eventDataList.stream().collect(Collectors.toMap(MarketingEventData::getId, Function.identity(), (k1, k2) -> k1));
        }

        Map<String, String> activityEntityMap = null;
        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIds);
        if (CollectionUtils.isNotEmpty(activityEntityList)) {
            activityEntityMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, ActivityEntity::getId, (k1, k2) -> k1));
        }
        List<MailTaskStatisticsEntity> mailTaskStatisticsEntityList = mailTaskStatisticsDAO.getByEaAndTaskIds(ea, taskIds);
        //批量查询邮件发送的目标人群信息
        Map<String, List<String>> mailGroupUserMap = new HashMap<>();
        Set<String> groupUserSet = new HashSet<>();
        for (MailSendTaskEntity entity : pageEntities) {
            if (mailGroupUserMap.get(entity.getId()) == null) {
                mailGroupUserMap.put(entity.getId(), Lists.newArrayList());
            }
            if (StringUtils.isNotEmpty(entity.getMarketingGroupUserIds())) {
                List<String> marketingGroupUserIds = GsonUtil.getGson().fromJson(entity.getMarketingGroupUserIds(), new TypeToken<ArrayList>() {
                }.getType());
                if (CollectionUtils.isNotEmpty(marketingGroupUserIds)) {
                    mailGroupUserMap.get(entity.getId()).addAll(marketingGroupUserIds);
                    groupUserSet.addAll(marketingGroupUserIds);
                }
            }
        }
        Map<String, ListMailMarketingResult.MailMarketingGroupUser> mailMarketingGroupUserMap = null;
        if (CollectionUtils.isNotEmpty(groupUserSet)) {
            mailMarketingGroupUserMap = mailManager.getMarketingGroupUserMap(ea, Lists.newArrayList(groupUserSet));
        }

        Map<String, MailTaskStatisticsEntity> mailTaskStatisticsEntityMap = mailTaskStatisticsEntityList.stream().collect(Collectors.toMap(MailTaskStatisticsEntity::getTaskId, data -> data, (v1, v2) -> v1));
        for (MailSendTaskEntity entity : pageEntities) {
            ListMailMarketingResult mailResult = new ListMailMarketingResult();
            mailResult.setId(entity.getId());
            mailResult.setMarketingEventId(entity.getMarketingEventId());
            if (marketingEventDataMap != null && marketingEventDataMap.get(entity.getMarketingEventId()) != null) {
                mailResult.setMarketingEventType(marketingEventDataMap.get(entity.getMarketingEventId()).getEventType());
                mailResult.setMarketingEventName(marketingEventDataMap.get(entity.getMarketingEventId()).getName());
            }
            if (activityEntityMap != null && activityEntityMap.get(entity.getMarketingEventId()) != null) {
                mailResult.setMarketingObjectId(activityEntityMap.get(entity.getMarketingEventId()));
            }
            mailResult.setSubject(entity.getSubject());
            mailResult.setStatus(entity.getSendStatus());
            if(MailSendStatusEnum.SEND_FAILED.getStatus()==entity.getSendStatus() && entity.getStatusCode() != null){
                if (entity.getStatusCode() == SHErrorCode.FORBID_SEND_MARKETING_MESSAGE.getErrorCode()) {
                    mailResult.setErrMsg(MarketingJobUtil.getErrorMessage());
                } else {
                    mailResult.setErrMsg(SendCloudError.getErrorMessageByCode(entity.getStatusCode()));
                }
            }
            mailResult.setFsUserId(entity.getFsUserId());
            mailResult.setCreateTime(entity.getCreateTime().getTime());
            mailResult.setSendRange(entity.getSendRange());
            List<String> groupUserIds = mailGroupUserMap.get(entity.getId());
            if (CollectionUtils.isNotEmpty(groupUserIds) && mailMarketingGroupUserMap != null) {
                List<ListMailMarketingResult.MailMarketingGroupUser> marketingGroupUser = Lists.newArrayList();
                for (String marketingGroupUserId : groupUserIds) {
                    if (mailMarketingGroupUserMap.get(marketingGroupUserId) != null) {
                        marketingGroupUser.add(mailMarketingGroupUserMap.get(marketingGroupUserId));
                    }
                }
                mailResult.setMarketingGroupUser(marketingGroupUser);
            }
            if (entity.getScheduleType() == MailScheduleTypeEnum.SCHEDULE_SEND.getType()
                    && entity.getSendStatus() == MailSendStatusEnum.WAIT_FOR_SEND.getStatus()) {
                mailResult.setSendCancelable(true);
            } else {
                mailResult.setSendCancelable(false);
            }
            String activityId = taskIdAndActivityMap.get(entity.getId());
            if (StringUtils.isNotBlank(activityId)) {
                mailResult.setLeadCount(clueMap.get(activityId) != null ? clueMap.get(activityId) : 0);
                mailResult.setMarketingActivityId(activityId);
            }
            if (StringUtils.isNotEmpty(taskIdFileNameMap.get(entity.getId()))) {
                mailResult.setFileName(taskIdFileNameMap.get(entity.getId()));
            }

            Long requestNum = 0L;
            Long deliveredNum = 0L;
            Long openNum = 0L;
            Long clickNum = 0L;
            Long clickUserNum = 0L;
            Long openUserNum = 0L;
            // 设置开始-结束时间
            MailTaskStatisticsEntity mailTaskStatisticsEntity = mailTaskStatisticsEntityMap.get(entity.getId());
            if (mailTaskStatisticsEntity != null) {
                requestNum = mailTaskStatisticsEntity.getSendNum();
                deliveredNum = mailTaskStatisticsEntity.getDeliveredNum();
                openNum = mailTaskStatisticsEntity.getOpenNum();
                clickNum = mailTaskStatisticsEntity.getClickNum();
                clickUserNum = mailTaskStatisticsEntity.getClickUserNum();
                openUserNum = mailTaskStatisticsEntity.getOpenUserNum();
            }
            if (entity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
                mailResult.setSendTime(entity.getCreateTime().getTime());
            } else {
                mailResult.setSendTime(entity.getFixTime());
            }
            mailResult.setClickUserNum(clickUserNum);
            mailResult.setOpenUserNum(openUserNum);
            mailResult.setSendCount(requestNum);
            mailResult.setDeliveryCount(deliveredNum);
            mailResult.setOpenCount(openNum);
            mailResult.setClickNum(clickNum);
            ObjectData objectData = idToMarketingActivityDataMap.get(mailResult.getMarketingActivityId());
            if (objectData != null) {
                mailResult.setAuditStatus(objectData.getLifeStatus());
            }
            mailListResult.add(mailResult);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<ListMailMarketingResult>> listSopMailNotice(String ea, Integer fsUserId, String marketingEventId, String keyword, Integer pageNum, Integer pageSize) {
        PageResult<ListMailMarketingResult> pageResult = new PageResult<>();
        List<ListMailMarketingResult> mailListResult = Lists.newArrayList();
        pageResult.setResult(mailListResult);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        Page<ListMailMarketingResult> page = new Page(pageNum, pageSize, true);
        List<ListMailMarketingResult> sopMailNoticeList = triggerTaskInstanceDao.listSopMailNotice(ea, marketingEventId, keyword, page);
        if (CollectionUtils.isNotEmpty(sopMailNoticeList)) {
            pageResult.setTotalCount(page.getTotalNum());
            for (ListMailMarketingResult sopMailNotice : sopMailNoticeList) {
                ListMailMarketingResult mailResult = new ListMailMarketingResult();
                BeanUtils.copyProperties(sopMailNotice, mailResult);
                mailListResult.add(mailResult);
            }
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<QueryAccountInfo> queryAccountInfo(String ea) {
        MailAccountEntity account = mailManager.getUserAccount(ea, MailApiUserTypeEnum.DEFAULT.getType());
        if (account == null) {
            log.info("EmailServiceImpl.queryAccountInfo failed accout not exist ea:{}", ea);
            return Result.newError(SHErrorCode.ENTERPRSE_NOT_BIND_MAIL_ACCOUNT);
        }

        return mailManager.queryAccoutInfo(account.getApiUser(), account.getApiKey());
    }

    @Override
    public Result<GetSendReplyDataByIdResult> getSendReplyDataById(String id) {
        MailSendReplyEntity mailSendReplyEntity = mailSendReplyDAO.getById(id);
        if (mailSendReplyEntity == null) {
            log.warn("EmailServiceImpl.getSendReplyDataById error mailSendReplyEntity is null id:{}", id);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        GetSendReplyDataByIdResult result = BeanUtil.copy(mailSendReplyEntity, GetSendReplyDataByIdResult.class);
        result.setCreateTime(mailSendReplyEntity.getCreateTime().getTime());
        result.setUpdateTime(mailSendReplyEntity.getUpdateTime().getTime());
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<QueryMailUserSendDetailResult>> queryMailUserSendDetail(QueryMailUserSendDetailVO vo) {
        // 查询task详情
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(vo.getTaskId());
        if (mailSendTaskEntity == null) {
            log.warn("EmailServiceImpl.queryMailUserSendDetail error mailSendTaskEntity vo:{}", vo);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        PageResult<QueryMailUserSendDetailResult> pageResult = new PageResult<>();
        List<QueryMailUserSendDetailResult> queryMailUserSendDetailResults = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(queryMailUserSendDetailResults);
        // 若不存在统计数据则直接返回
        //MailTaskStatisticsEntity mailTaskStatisticsEntity = mailTaskStatisticsDAO.getByEaAndTaskId(vo.getEa(), mailSendTaskEntity.getId());
        if (mailSendTaskEntity.getLabelId() == null) {
            return Result.newSuccess(pageResult);
        }
        // 设置开始-结束时间
        String emailStatusStartTime;
        String emailStatusEndTime;
        Date startDate;
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            startDate = mailSendTaskEntity.getCreateTime();
        } else {
            startDate = new Date(mailSendTaskEntity.getFixTime());
        }
        Date emailStatusEndDate = DateUtil.plusDay(startDate, 2);
        emailStatusStartTime = DateUtil.format2(startDate);
        emailStatusEndTime = DateUtil.format2(emailStatusEndDate);

        Date openAndClickEndDate = DateUtil.plusMonth(startDate, 3);

        // 查询投递回应
        QueryEmailStatusVO queryEmailStatusVO = new QueryEmailStatusVO();
        queryEmailStatusVO.setEa(vo.getEa());
        queryEmailStatusVO.setEmail(vo.getEmail());
        queryEmailStatusVO.setLabelId(mailSendTaskEntity.getLabelId());
        queryEmailStatusVO.setStartDate(emailStatusStartTime);
        queryEmailStatusVO.setEndDate(emailStatusEndTime);
        queryEmailStatusVO.setStart((vo.getPageNum() - 1) * vo.getPageSize());
        queryEmailStatusVO.setLimit(vo.getPageSize());
        if (vo.getStatus() != null) {
            if (vo.getStatus().equals(MailSendUserStatusEnum.SUCCESS.getStatus())) {
                queryEmailStatusVO.setStatus(MailSendUserStatusEnum.getSuccessDesc());
            } else if (vo.getStatus().equals(MailSendUserStatusEnum.FAIL.getStatus())) {
                // FAIL包含了所有subStatus, 所以如果前端传入vo.subStatus非空, 直接使用就行
                if (CollectionUtils.isNotEmpty(vo.getSubStatus())) {
                    queryEmailStatusVO.setSubStatus(vo.getSubStatus().stream().map(data -> data + "").collect(Collectors.joining(";")));
                } else {
                    queryEmailStatusVO.setSubStatus(MailSendUserStatusEnum.getFailCode().stream().map(data -> data + "").collect(Collectors.joining(";")));
                }
            } else if (vo.getStatus().equals(MailSendUserStatusEnum.SENDING.getStatus())) {
                queryEmailStatusVO.setStatus(MailSendUserStatusEnum.getSendingDesc());
            } else if (vo.getStatus().equals(MailSendUserStatusEnum.UNSUBSCRIBE.getStatus())) {
                queryEmailStatusVO.setSubStatus(MailSendUserStatusEnum.getUnsubscribeCode() + "");
            } else if (vo.getStatus().equals(MailSendUserStatusEnum.SPAM.getStatus())) {
                //如果前端传入vo.subStatus非空, 如果其中没有属于SPAM的subStatus, 把所有属于SPAM的subStatus加入queryEmailStatusVO,否则, 只把vo.subStatus中属于SPAM的subStatus加入到queryEmailStatusVO
                List<Integer> spamCode = MailSendUserStatusEnum.getSpamCode();
                if (CollectionUtils.isNotEmpty(vo.getSubStatus())) {
                    List<Integer> spamSubStatus = vo.getSubStatus().stream().filter(spamCode::contains).collect(Collectors.toList());
                    if (spamSubStatus.isEmpty()) {
                        queryEmailStatusVO.setSubStatus(spamCode.stream().map(data -> data + "").collect(Collectors.joining(";")));
                    } else {
                        queryEmailStatusVO.setSubStatus(spamSubStatus.stream().map(data -> data + "").collect(Collectors.joining(";")));
                    }
                } else {
                    queryEmailStatusVO.setSubStatus(spamCode.stream().map(data -> data + "").collect(Collectors.joining(";")));
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(vo.getSubStatus())) {
                queryEmailStatusVO.setSubStatus(vo.getSubStatus().stream().map(data -> data + "").collect(Collectors.joining(";")));
            }
        }
        QueryEmailStatusResp queryEmailStatusResp = mailManager.queryEmailStatus(queryEmailStatusVO);
        if (queryEmailStatusResp == null || CollectionUtils.isEmpty(queryEmailStatusResp.getVoList())) {
            return Result.newSuccess(pageResult);
        }
        // 查询营销用户信息
        Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = Maps.newHashMap();
        Map<String, String> sendAddressByLowerCase = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(queryEmailStatusResp.getVoList())) {
            List<String> emailList = queryEmailStatusResp.getVoList().stream().map(VoList::getRecipients).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(emailList)) {
                sendAddressByLowerCase = mailManager.getSendAddressByLowerCase(emailList);
                emailList.addAll(Lists.newArrayList(sendAddressByLowerCase.values()));
                emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(emailList, vo.getEa(), vo.getFsUserId());
            }
        }
        List<String> recipientList = queryEmailStatusResp.getVoList().stream().map(VoList::getRecipients).distinct().collect(Collectors.toList());
        Map<String, EmailOpenClickStatisticBO> recipientToOpenAndClickMap = emailSendRecordDetailObjManager.getOpenAndClickMap(vo.getEa(), vo.getTaskId(), recipientList);
        for (VoList voList : queryEmailStatusResp.getVoList()) {
            QueryMailUserSendDetailResult queryMailUserSendDetailResult = new QueryMailUserSendDetailResult();
            queryMailUserSendDetailResult.setEmail(voList.getRecipients());
            queryMailUserSendDetailResult.setStatus(voList.getStatus());
            if (StringUtils.isNumeric(voList.getSubStatus())) {
                Integer subStatus = Integer.valueOf(voList.getSubStatus());
                queryMailUserSendDetailResult.setStatusDesc(MailSendSubStatusEnum.getDescByCode(subStatus));
                queryMailUserSendDetailResult.setSpam(MailSendUserStatusEnum.getSpamCode().contains(subStatus));
                queryMailUserSendDetailResult.setUnsubscribe(MailSendUserStatusEnum.getUnsubscribeCode().equals(subStatus));
            }
            queryMailUserSendDetailResult.setSendTime(voList.getModifiedTime());
            EmailOpenClickStatisticBO openClickStatisticBO = recipientToOpenAndClickMap.get(voList.getRecipients().toLowerCase());
            queryMailUserSendDetailResult.setClick(openClickStatisticBO == null ? 0 : openClickStatisticBO.getClick());
            queryMailUserSendDetailResult.setOpen(openClickStatisticBO == null ? 0 : openClickStatisticBO.getOpen());
            String sendAddress = sendAddressByLowerCase.get(voList.getRecipients());
            // 使用原始发送地址来获取营销用户名
            EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(StringUtils.isNotBlank(sendAddress) ? sendAddress : voList.getRecipients());
            if (emailMarketingDetailInfo != null) {
                queryMailUserSendDetailResult.setName(emailMarketingDetailInfo.getName());
                queryMailUserSendDetailResult.setMarketingUserId(emailMarketingDetailInfo.getMarketingUserId());
                queryMailUserSendDetailResult.setPhone(emailMarketingDetailInfo.getPhone());
                queryMailUserSendDetailResult.setCompanyName(emailMarketingDetailInfo.getCompanyName());
            }
            queryMailUserSendDetailResults.add(queryMailUserSendDetailResult);
        }
        pageResult.setTotalCount(queryEmailStatusResp.getTotal());
        return Result.newSuccess(pageResult);
    }


    @Override
    public Result<List<QueryClickLinkDetailResult>> queryClickLinkDetail(QueryClickLinkDetailVO vo) {
        // 查询task详情
        List<QueryClickLinkDetailResult> result = Lists.newArrayList();
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(vo.getTaskId());
        if (mailSendTaskEntity == null) {
            log.warn("EmailServiceImpl.queryClickLinkDetail error mailSendTaskEntity vo:{}", vo);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        String openAndClickStartTime;
        String openAndClickEndTime;
        Date startDate;
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            startDate = mailSendTaskEntity.getCreateTime();
        } else {
            startDate = new Date(mailSendTaskEntity.getFixTime());
        }
        Date openAndClickEndDate = DateUtil.plusMonth(startDate, 3);
        openAndClickStartTime = DateUtil.format2(startDate);
        openAndClickEndTime = DateUtil.format2(openAndClickEndDate);

        QueryOpenAndClickListVO queryOpenAndClickListVO = new QueryOpenAndClickListVO();
        queryOpenAndClickListVO.setEa(vo.getEa());
        queryOpenAndClickListVO.setStartDate(openAndClickStartTime);
        queryOpenAndClickListVO.setEndDate(openAndClickEndTime);
        queryOpenAndClickListVO.setLabelId(mailSendTaskEntity.getLabelId() + "");
        queryOpenAndClickListVO.setTrackType(MailOpenAndClickTrackTypeEnum.CLICK.getType() + "");
        List<OpenAndClickInfo> openAndClickInfoList = mailManager.queryAllOpenAndClickList(queryOpenAndClickListVO);
        Map<String, Integer> clickMap = Maps.newHashMap();
        for (OpenAndClickInfo openAndClickInfo : openAndClickInfoList) {
            clickMap.merge(openAndClickInfo.getUrl(), 1, (a, b) -> a + b);
        }
        if (MapUtils.isEmpty(clickMap)) {
            return Result.newSuccess(result);
        }
        // 查询营销活动
        List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityList = marketingActivityExternalConfigDao.getByAssociateId(mailSendTaskEntity.getId());
        if (CollectionUtils.isEmpty(marketingActivityExternalConfigEntityList)) {
            log.warn("EmailServiceImpl.queryClickLinkDetail marketingActivityExternalConfigEntityList is empty");
            return Result.newSuccess(result);
        }
        String marketingActivityId = marketingActivityExternalConfigEntityList.get(0).getMarketingActivityId();
        // 批量查询官网地址
        Set<String> linkList = clickMap.keySet();
        Map<String, MailContentLinkEntity> mailContentLinkEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(linkList)) {
            List<MailContentLinkEntity> mailContentLinkEntityList = mailContentLinkDAO.getLinkContentByEaTaskAndLinks(vo.getEa(), vo.getTaskId(), Lists.newArrayList(linkList));
            mailContentLinkEntityMap = mailContentLinkEntityList.stream().collect(Collectors.toMap(MailContentLinkEntity::getLink, data -> data, (v1, v2) -> v1));
        }
        for (Map.Entry<String, Integer> map : clickMap.entrySet()) {
            if (StringUtils.isBlank(map.getKey())) {
                continue;
            }
            String objectId = getParam(map.getKey(), "id");
            Integer objectType = null;
            String objectTypeStr = getParam(map.getKey(), "objectType");
            String emailObjectTypeStr = getParam(map.getKey(), "emailObjectType");
            if (objectTypeStr != null) {
                try {
                    objectType = Integer.valueOf(objectTypeStr);
                } catch (Exception e) {
                    log.warn("EmailServiceImpl.queryClickLinkDetail objectType error objectTypeStr:{}", objectTypeStr);
                }
            }
            if (emailObjectTypeStr != null && objectType == null) {
                try {
                    objectType = Integer.valueOf(emailObjectTypeStr);
                } catch (Exception e) {
                    log.warn("EmailServiceImpl.queryClickLinkDetail objectType error emailObjectTypeStr:{}", emailObjectTypeStr);
                }
            }
            QueryClickLinkDetailResult queryClickLinkDetailResult = new QueryClickLinkDetailResult();
            queryClickLinkDetailResult.setLink(map.getKey());
            queryClickLinkDetailResult.setClickNum(map.getValue());
            queryClickLinkDetailResult.setContentId(objectId);
            queryClickLinkDetailResult.setContentType(objectType);
            queryClickLinkDetailResult.setContentName(objectManager.getObjectName(getParam(map.getKey(), "id"), objectType));
            if (StringUtils.isNotBlank(objectId) && objectType != null && objectType.equals(ObjectTypeEnum.HEXAGON_SITE.getType())) {
                objectType = ObjectTypeEnum.HEXAGON_PAGE.getType();
                List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(objectId));
                if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList)) {
                    objectId = hexagonSiteListDTOList.get(0).getHexagonPageId();
                }
            }
            if (StringUtils.isBlank(queryClickLinkDetailResult.getContentName())) {
                MailContentLinkEntity mailContentLinkEntity = mailContentLinkEntityMap.get(map.getKey());
                if (mailContentLinkEntity != null && mailContentLinkEntity.getLinkContent() != null && StringUtils.isNotBlank(mailContentLinkEntity.getLinkContent().getName())) {
                    queryClickLinkDetailResult.setContentName(mailContentLinkEntity.getLinkContent().getName());
                }
            }
            if (StringUtils.isNotBlank(objectId) && objectType != null) {
                // 若为微页面转换为页面id
                if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                    List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(objectId));
                    if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList)) {
                        objectId = hexagonSiteListDTOList.get(0).getHexagonPageId();
                    }
                }
                int clueNum = customizeFormDataUserDAO.getCountByMarketingActivityIdAndObjectId(marketingActivityId, objectId, null);
                queryClickLinkDetailResult.setClueNum(clueNum);
            }
            result.add(queryClickLinkDetailResult);
        }
        openAndClickInfoList.clear();
        clickMap.clear();
        mailContentLinkEntityMap.clear();
        result.sort(Comparator.comparing(QueryClickLinkDetailResult::getClickNum).reversed());
        return Result.newSuccess(result);
    }

    @Override
    public Result callBack(WebHookCallBackVO vo) {
        // 目标人群->营销用户id
        Map<String, String> userHeaderMap = GsonUtil.getGson().fromJson(vo.getUserHeaders(), new TypeToken<Map>() {
        }.getType());
        String eaHeaderKey = userHeaderMap.get(MailConstant.EA_HEADERS_KEY);
        String taskId = userHeaderMap.get(MailConstant.TASK_ID_HEADERS_KEY);
        String triggerTaskInstanceId = userHeaderMap.get(MailConstant.TRIGGER_TASK_INSTANCE_ID_HEADERS_KEY);

        if (StringUtils.isBlank(eaHeaderKey) || StringUtils.isBlank(taskId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(eaHeaderKey)) {
            log.warn("EmailServiceImpl#callBack ea is stop or license expire ea :{}", eaHeaderKey);
            return Result.newSuccess();
        }

        handleEmailSendRecordDetailObjByCallBack(eaHeaderKey, taskId, vo);

        String sendUrl = vo.getRecipient();
        Map<String, String> sendAddressByLowerCase = mailManager.getSendAddressByLowerCase(Lists.newArrayList(vo.getRecipient()));
        if (MapUtils.isNotEmpty(sendAddressByLowerCase) && StringUtils.isNotBlank(sendAddressByLowerCase.get(sendUrl))) {
            sendUrl = sendAddressByLowerCase.get(sendUrl);
        }

        String marketingUserId = null;
        // 查询营销用户信息
        Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(Lists.newArrayList(sendUrl), eaHeaderKey, 1000);
        if (MapUtils.isNotEmpty(emailMarketingDetailInfoMap)) {
            EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(sendUrl);
            if (emailMarketingDetailInfo != null) {
                marketingUserId = emailMarketingDetailInfo.getMarketingUserId();
            }
        }

        Integer actionType = WebHookEventEnum.getTypeByEvent(vo.getEvent());

        String taskType = userHeaderMap.get(MailConstant.TASK_TYPE_HEADERS_KEY);
        if (StringUtils.equals(taskType, "SOP_MAIL_NOTICE") //兼容使用
                || StringUtils.isNotBlank(triggerTaskInstanceId)) {
            if (StringUtils.isBlank(marketingUserId)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            if (StringUtils.equals(taskType, "SOP_MAIL_NOTICE")) {
                triggerTaskInstanceId = taskId;
            }
            TriggerTaskInstanceEntity triggerTaskInstance = triggerTaskInstanceDao.getById(triggerTaskInstanceId);
            if (triggerTaskInstance != null) {
                SceneTriggerEntity sceneTrigger = sceneTriggerDao.getBySceneTypeAndTriggerId(eaHeaderKey, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), triggerTaskInstance.getTriggerId());
                if (sceneTrigger != null) {
                    // 转换 open -> TriggerActionTypeEnum.MAIL_OPEN 1501 -> mail_open
                    String triggerActionType = null;
                    if (MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType() == actionType) {
                        triggerActionType = TriggerActionTypeEnum.MAIL_UNSUBSCRIBE.getTriggerActionType();
                    }
                    if (MarketingUserActionType.MAIL_REPORT_SPAM.getActionType() == actionType) {
                        triggerActionType = TriggerActionTypeEnum.MAIL_REPORT_SPAM.getTriggerActionType();
                    }
                    if (MarketingUserActionType.MAIL_OPEN.getActionType() == actionType) {
                        triggerActionType = TriggerActionTypeEnum.MAIL_OPEN.getTriggerActionType();
                    }
                    if (MarketingUserActionType.MAIL_RESPONSE.getActionType() == actionType) {
                        triggerActionType = TriggerActionTypeEnum.MAIL_RESPONSE.getTriggerActionType();
                    }
                    if (MarketingUserActionType.MAIL_CLICK.getActionType() == actionType) {
                        triggerActionType = TriggerActionTypeEnum.MAIL_CLICK.getTriggerActionType();
                    }
                    if (StringUtils.isNotEmpty(triggerActionType)) {
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(eaHeaderKey, marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(),
                                sceneTrigger.getSceneTargetId(), triggerActionType,Maps.newHashMap(ImmutableMap.of("objectType", ObjectTypeEnum.SOP_MAIL_TASK.getType(), "objectId", triggerTaskInstance.getTriggerTaskSnapshotId())));
                    }
                }
            }
        } else {
            if (actionType == null) {
                log.warn("EmailServiceImpl.callBack actionType is null arg:{}", vo);
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            try {
                if (WebHookEventEnum.needSaveDataBase(vo.getEvent())) {
                    MailWebHookCallBackEntity mailWebHookCallBackEntity = new MailWebHookCallBackEntity();
                    mailWebHookCallBackEntity.setEa(eaHeaderKey);
                    mailWebHookCallBackEntity.setTaskId(taskId);
                    mailWebHookCallBackEntity.setLink(vo.getRecipient());
                    mailWebHookCallBackEntity.setEventType(actionType);
                    MailWebHookCallBackDetail mailWebHookCallBackDetail = new MailWebHookCallBackDetail();
                    mailWebHookCallBackDetail.setLabelId(vo.getLabelId());
                    mailWebHookCallBackDetail.setTimestamp(vo.getTimestamp());
                    mailWebHookCallBackEntity.setEventDetail(mailWebHookCallBackDetail);
                    mailWebHookCallBackDAO.insert(mailWebHookCallBackEntity);
                }
            } catch (Exception e) {
                log.error("EmailServiceImpl.callBack insert mailWebHookCallBackEntity error, data: {}", vo, e);
            }

            // 将问题邮件写入
            if (WebHookEventEnum.sendFailEventType(vo.getEvent(), vo.getSubStat())) {
                MailSendErrorAddressEntity mailSendErrorAddressEntity = new MailSendErrorAddressEntity();
                mailSendErrorAddressEntity.setId(UUIDUtil.getUUID());
                mailSendErrorAddressEntity.setEa(eaHeaderKey);
                mailSendErrorAddressEntity.setTaskId(taskId);
                mailSendErrorAddressEntity.setAddress(sendUrl);
                mailSendErrorAddressEntity.setEventType(actionType);
                mailSendErrorAddressEntity.setSubStat(vo.getSubStat());
                mailSendErrorAddressDAO.insertMailSendErrorAddress(mailSendErrorAddressEntity);
            }

            if (actionType.equals(WebHookEventEnum.INVALID.getMarketingUserType()) || actionType.equals(WebHookEventEnum.SOFT_BOUNCE.getMarketingUserType())) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            if (StringUtils.isBlank(marketingUserId)) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }

        // 查询营销用户是否有绑定无身份
        UserMarketingAccountEntity userMarketingAccountEntity = userMarketingAccountDAO.getById(marketingUserId);
        if (userMarketingAccountEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                .getMarketingActivityExternalConfigEntity(eaHeaderKey, taskId, AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType());
        String browserUserId = buildUserMarketingBrowserUserRelation(marketingUserId, eaHeaderKey);
        MarketingUserActionEvent marketingUserActionEvent = new MarketingUserActionEvent();
        marketingUserActionEvent.setActionType(actionType);
        marketingUserActionEvent.setChannelType(MarketingUserActionChannelType.EMAIL.getChannelType());
        marketingUserActionEvent.setFingerPrint(browserUserId);
        marketingUserActionEvent.setActionTime(System.currentTimeMillis());
        marketingUserActionEvent.setSceneId(vo.getUrl());
        marketingUserActionEvent.setSceneType(MarketingUserActionSceneType.EMAIL_HOOK.getSceneType());
        marketingUserActionEvent.setObjectId(taskId);
        marketingUserActionEvent.setEa(eaHeaderKey);
        marketingUserActionEvent.setObjectType(ObjectTypeEnum.MAIL_TASK.getType());
        String marketingEventId = null;
        if (marketingActivityExternalConfigEntity != null) {
            marketingUserActionEvent.setMarketingActivityId(marketingActivityExternalConfigEntity.getMarketingActivityId());
            marketingEventId = marketingActivityExternalConfigEntity.getMarketingEventId();
        }
        marketingUserActionEvent.setMarketingEventId(marketingEventId);
        Map<String, Object> extensionParams = Maps.newHashMap();
        extensionParams.put(RecordActionArg.SPREAD_CHANNEL_KEY, SpreadChannelEnum.EMAIL.getCode());
        extensionParams.put(RecordActionArg.MARKETING_SCENE_KEY, marketingEventCommonSettingManager.getMarketingScene(eaHeaderKey, marketingEventId, marketingUserActionEvent.getMarketingActivityId(), null));
        marketingUserActionEvent.setExtensionParams(extensionParams);
        marketingRecordActionSender.send(marketingUserActionEvent);

        return Result.newSuccess();
    }

    @Override
    public Result createWebHook(String ea) {
        // 查询账号
        List<MailAccountEntity> mailAccountEntityList = mailAccountDAO.getByEa(ea);
        if (CollectionUtils.isEmpty(mailAccountEntityList)) {
            log.warn("EmailServiceImpl.createWebHook ea:{}", ea);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        for (MailAccountEntity mailAccountEntity : mailAccountEntityList) {
            CreateWebHookVO vo = new CreateWebHookVO();
            vo.setEa(ea);
            vo.setCategoryName(mailAccountEntity.getApiUser());
            // 去掉1和18、4、5，1表示送达，4表示无效邮件 5标识软退信，这些的邮件由定时任务主动去拉，因为送达的邮件太多，会导有大量的回调，导致web层挂掉
            // 送达由定时任务 MailSendStatusHandleJob 来执行
            vo.setEvent("3,10,11,12");
            vo.setType(mailAccountEntity.getType());
            mailManager.createWebHook(vo);
        }
        return Result.newSuccess();
    }

    private String buildUserMarketingBrowserUserRelation(String userMarketingId, String ea) {
        if (StringUtils.isBlank(userMarketingId) || StringUtils.isBlank(ea)) {
            return null;
        }
        UserMarketingBrowserUserRelationEntity userMarketingBrowserUserRelationEntity = userMarketingBrowserUserRelationDao.getOneByMarketingUserId(ea, userMarketingId);
        String browserId;
        if (userMarketingBrowserUserRelationEntity == null) {
            browserId = UUIDUtil.getUUID();
            browserUserDao.insert(browserId, "SYSTEM_MAIL");
            userMarketingBrowserUserRelationDao.insertIgnore(UUIDUtil.getUUID(), ea, userMarketingId, browserId);
        } else {
            browserId = userMarketingBrowserUserRelationEntity.getBrowserUserId();
        }
        return browserId;
    }

    @Override
    public Result<String> addTemplate(AddTemplateVO vo) {
        return mailManager.addMailTemplate(vo);
    }

    @Override
    public Result<Void> deleteTemplate(DeleteMailTemplateVO vo) {
        return mailManager.deleteTemplate(vo);
    }

    @Override
    public Result<PageResult<PageQueryMailTemplateResult>> listPagerTemplate(PageQueryTemplateVO vo) {
        if (vo.getDataType().equals(TemplateDataTypeEnum.CUSTOMIZE_TEMPLATE.getType())) {
            return mailManager.pageQueryMailTemplate(vo);
        } else if (vo.getDataType().equals(TemplateDataTypeEnum.COMMON_TEMPLATE.getType())) {
            return mailManager.pageCommonMailTemplate(vo);
        } else {
            return shanShanEditService.queryShanShanEditEmailListV2(vo);
        }
    }

    @Override
    public Result<QueryMailTemplateDetailResult> queryTemplateDetail(QueryTemplateDetailVO vo) {
        if (vo.getDataType().equals(TemplateDataTypeEnum.CUSTOMIZE_TEMPLATE.getType())) {
            return mailManager.queryTemplateDetail(vo);
        } else {
            return mailManager.queryCommonTemplateDetail(vo);
        }
    }

    @Override
    public Result updateTemplateDetail(UpdateTemplateDetailVO vo) {
        MailTemplateEntity mailTemplateEntity = mailTemplateDAO.getDetailById(vo.getTemplateId());
        if (mailTemplateEntity == null) {
            log.warn("EmailServiceImpl.updateTemplateDetail error args:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        UpdateMailTemplateVO updateMailTemplateVO = new UpdateMailTemplateVO();
        updateMailTemplateVO.setInvokeName(vo.getTemplateId());
        updateMailTemplateVO.setEa(vo.getEa());
        updateMailTemplateVO.setHtml(vo.getHtml());
        updateMailTemplateVO.setName(vo.getName());
        updateMailTemplateVO.setSubject(vo.getName());
        UpdateMailTemplateResp updateMailTemplateResp = mailManager.updateTemplate(updateMailTemplateVO);
        if (updateMailTemplateResp == null) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        mailTemplateDAO.updateTemplateNameById(vo.getName(), vo.getTemplateId());
        return Result.newSuccess();
    }

    @Override
    public Result<GetTaskDetailByIdResult> getTaskDetailById(String id) {
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(id);
        if (mailSendTaskEntity == null) {
            log.warn("EmailServiceImpl.getTaskDetailById error id:{}", id);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        GetTaskDetailByIdResult result = BeanUtil.copy(mailSendTaskEntity, GetTaskDetailByIdResult.class);
        result.setCreateTime(mailSendTaskEntity.getCreateTime().getTime());
        result.setUpdateTime(mailSendTaskEntity.getUpdateTime().getTime());
        result.setSendStatus(mailSendTaskEntity.getSendStatus());
        if (StringUtils.isNotEmpty(mailSendTaskEntity.getMarketingGroupUserIds())) {
            result.setMarketingGroupUser(mailManager.getMarketingGroupUserInfo(mailSendTaskEntity.getEa(), mailSendTaskEntity.getMarketingGroupUserIds()));
        }
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            result.setSendTime(mailSendTaskEntity.getCreateTime().getTime());
        } else {
            result.setSendTime(mailSendTaskEntity.getFixTime());
        }
        // 查询发件人
        MailSendReplyEntity mailSendReplyEntity = null;
        if (StringUtils.isNotBlank(mailSendTaskEntity.getSenderIds())) {
            List<String> senderIds = GsonUtil.getGson().fromJson(mailSendTaskEntity.getSenderIds(), new TypeToken<List>() {
            }.getType());
            if (CollectionUtils.isNotEmpty(senderIds)) {
                mailSendReplyEntity = mailSendReplyDAO.getById(senderIds.get(0));
                result.setSenderUrl(mailSendReplyEntity != null ? mailSendReplyEntity.getAddress() : null);
            }
        }
        // 查询收件人
        if (StringUtils.isNotBlank(mailSendTaskEntity.getReplyIds())) {
            List<String> replyIds = GsonUtil.getGson().fromJson(mailSendTaskEntity.getReplyIds(), new TypeToken<List>() {
            }.getType());
            if (CollectionUtils.isNotEmpty(replyIds)) {
                mailSendReplyEntity = mailSendReplyDAO.getById(replyIds.get(0));
                result.setReplyUrl(mailSendReplyEntity != null ? mailSendReplyEntity.getAddress() : null);
            }
        }

        //查询附件
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(mailSendTaskEntity.getAttachments())) {
            List<MailServiceMarketingActivityVO.MailAttachment> mailAttachments = GsonUtil.getGson().fromJson(mailSendTaskEntity.getAttachments(), new TypeToken<List<MailServiceMarketingActivityVO.MailAttachment>>() {
            }.getType());
            result.setAttachments(mailAttachments);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetDetailByMarketingActivityIdResult> getDetailByMarketingActivityId(GetDetailByMarketingActivityIdVO vo) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                .getByMarketingActivityIdAndAssociateIdType(vo.getMarketingActivityId(), AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType());
        if (marketingActivityExternalConfigEntity == null) {
            log.warn("EmailServiceImpl.getDetailByMarketingActivityId error vo:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(marketingActivityExternalConfigEntity.getAssociateId());
        if (mailSendTaskEntity == null) {
            log.warn("EmailServiceImpl.getDetailByMarketingActivityId error vo:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        GetDetailByMarketingActivityIdResult result = new GetDetailByMarketingActivityIdResult();
        if (StringUtils.isNotEmpty(mailSendTaskEntity.getMarketingGroupUserIds())) {
            result.setMarketingGroupUser(mailManager.getMarketingGroupUserInfo(vo.getEa(), mailSendTaskEntity.getMarketingGroupUserIds()));
        }

        result.setSendRange(mailSendTaskEntity.getSendRange());
        result.setStatus(mailSendTaskEntity.getSendStatus());
        result.setSubject(mailSendTaskEntity.getSubject());
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            result.setSendTime(mailSendTaskEntity.getCreateTime().getTime());
        } else {
            result.setSendTime(mailSendTaskEntity.getFixTime());
        }
        result.setTaskId(mailSendTaskEntity.getId());
        result.setMarketingActivityId(marketingActivityExternalConfigEntity.getMarketingActivityId());
        result.setFsUserId(mailSendTaskEntity.getFsUserId());
        return Result.newSuccess(result);
    }

    @Override
    public Result addMailLinkContent(AddMailLinkContentVO vo) {
        // 查询task是否存在
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(vo.getTaskId());
        if (mailSendTaskEntity == null) {
            log.warn("EmailServiceImpl.addMailLinkContent mailSendTaskEntity is null vo:{}");
            return Result.newError(SHErrorCode.NO_DATA);
        }
        for (Map.Entry<String, String> map : vo.getLinkContent().entrySet()) {
            MailContentLinkEntity mailContentLinkEntity = new MailContentLinkEntity();
            mailContentLinkEntity.setEa(vo.getEa());
            mailContentLinkEntity.setTaskId(vo.getTaskId());
            mailContentLinkEntity.setLink(map.getKey());
            mailContentLinkEntity.setLinkContent(new MailLinkContent(map.getValue()));
            mailContentLinkDAO.insert(mailContentLinkEntity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QuerySendDetailsByTypeResult>> querySendDetailsByType(QuerySendDetailsByTypeVO vo) {
        PageResult<QuerySendDetailsByTypeResult> pageResult = new PageResult<>();
        List<QuerySendDetailsByTypeResult> sendDetailsResult = Lists.newArrayList();
        pageResult.setResult(sendDetailsResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MailWebHookCallBackEntity> mailWebHookCallBackEntityList = mailWebHookCallBackDAO.getDetailByTaskId(vo.getEa(), vo.getTaskId(), vo.getType(), page);
        if (CollectionUtils.isEmpty(mailWebHookCallBackEntityList)) {
            return Result.newSuccess(pageResult);
        }
        List<String> mailLink = mailWebHookCallBackEntityList.stream().map(MailWebHookCallBackEntity::getLink).collect(Collectors.toList());
        Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = Maps.newHashMap();
        Map<String, String> sendAddressByLowerCase = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(mailLink)) {
            sendAddressByLowerCase = mailManager.getSendAddressByLowerCase(mailLink);
            mailLink.addAll(Lists.newArrayList(sendAddressByLowerCase.values()));
            emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(mailLink, vo.getEa(), vo.getFsUserId());
        }
        for (MailWebHookCallBackEntity mailWebHookCallBackEntity : mailWebHookCallBackEntityList) {
            QuerySendDetailsByTypeResult querySendDetailsByTypeResult = new QuerySendDetailsByTypeResult();
            querySendDetailsByTypeResult.setTriggerTime(mailWebHookCallBackEntity.getEventDetail() != null ? mailWebHookCallBackEntity.getEventDetail().getTimestamp() : null);
            String sendAddress = sendAddressByLowerCase.get(mailWebHookCallBackEntity.getLink());
            // 使用原始发送地址来获取营销用户名
            EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(StringUtils.isNotBlank(sendAddress) ? sendAddress : mailWebHookCallBackEntity.getLink());
            querySendDetailsByTypeResult.setTargetName(emailMarketingDetailInfo != null ? emailMarketingDetailInfo.getName() : mailWebHookCallBackEntity.getLink());
            querySendDetailsByTypeResult.setMarketingUserId(emailMarketingDetailInfo != null ? emailMarketingDetailInfo.getMarketingUserId() : null);
            sendDetailsResult.add(querySendDetailsByTypeResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<QuerySendErrorMailResult>> querySendErrorMail(QuerySendErrorMailVO vo) {
        PageResult<QuerySendErrorMailResult> pageResult = new PageResult<>();
        List<QuerySendErrorMailResult> querySendErrorMailResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(querySendErrorMailResultList);
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MailSendErrorAddressEntity> mailSendErrorAddressEntityList = mailSendErrorAddressDAO.queryErrorAddressByEa(vo.getEa(), vo.getEmail(), vo.getEventType(), page);
        if (CollectionUtils.isEmpty(mailSendErrorAddressEntityList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        List<String> emailList = mailSendErrorAddressEntityList.stream().map(MailSendErrorAddressEntity::getAddress).collect(Collectors.toList());
        // 查询营销用户信息
        Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(emailList)) {
            emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(emailList, vo.getEa(), vo.getFsUserId());
        }
        for (MailSendErrorAddressEntity mailSendErrorAddressEntity : mailSendErrorAddressEntityList) {
            QuerySendErrorMailResult result = new QuerySendErrorMailResult();
            result.setEmail(mailSendErrorAddressEntity.getAddress());
            result.setId(mailSendErrorAddressEntity.getId());
            EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(mailSendErrorAddressEntity.getAddress());
            if (emailMarketingDetailInfo != null) {
                result.setMarketingUserId(emailMarketingDetailInfo.getMarketingUserId());
                result.setUserName(emailMarketingDetailInfo.getName());
            }
            if (mailSendErrorAddressEntity.getSubStat() != null) {
                result.setStatus(MailSendSubStatusEnum.getDescByCode(mailSendErrorAddressEntity.getSubStat()));
            }
            if (StringUtils.isBlank(result.getStatus())) {
                result.setStatus(WebHookEventEnum.getDescByType(mailSendErrorAddressEntity.getEventType()));
            }
            querySendErrorMailResultList.add(result);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result deleteSendErrorMail(DeleteSendErrorMailVO vo) {
        PageUtil<String> pageUtil = new PageUtil<>(vo.getIds(), 20);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            mailSendErrorAddressDAO.deleteErrorAddressDataByIds(pageUtil.getPagedList(i));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QueryFilterAddressResult>> queryFilterAddress(QueryFilterAddressVO vo) {
        PageResult<QueryFilterAddressResult> pageResult = new PageResult<>();
        List<QueryFilterAddressResult> queryFilterAddressResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(queryFilterAddressResultList);
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MailFilterDetailEntity> mailFilterDetailDTOList = mailFilterDetailDAO.queryMailFilterByTask(vo.getEa(), vo.getTaskId(), vo.getEmail(), vo.getEventType(), page);
        if (CollectionUtils.isEmpty(mailFilterDetailDTOList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        List<String> emailList = mailFilterDetailDTOList.stream().map(MailFilterDetailEntity::getAddress).collect(Collectors.toList());
        // 查询营销用户信息
        Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(emailList)) {
            emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(emailList, vo.getEa(), vo.getFsUserId());
        }
        for (MailFilterDetailEntity mailFilterDetailEntity : mailFilterDetailDTOList) {
            QueryFilterAddressResult queryFilterAddressResult = new QueryFilterAddressResult();
            queryFilterAddressResult.setEmail(mailFilterDetailEntity.getAddress());
            EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(mailFilterDetailEntity.getAddress());
            if (emailMarketingDetailInfo != null) {
                queryFilterAddressResult.setUserName(emailMarketingDetailInfo.getName());
                queryFilterAddressResult.setMarketingUserId(emailMarketingDetailInfo.getMarketingUserId());
            }
            if (mailFilterDetailEntity.getSubStat() != null) {
                queryFilterAddressResult.setStatus(MailSendSubStatusEnum.getDescByCode(mailFilterDetailEntity.getSubStat()));
            }
            if (StringUtils.isBlank(queryFilterAddressResult.getStatus())) {
                queryFilterAddressResult.setStatus(WebHookEventEnum.getDescByType(mailFilterDetailEntity.getEventType()));
            }
            queryFilterAddressResultList.add(queryFilterAddressResult);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result sendEmailTaskSchedule(String id) {
        try {
            mailManager.sendEmailTaskSchedule(id);
        } catch (Exception e) {
            log.warn("EmailServiceImpl.sendEmailTaskSchedule e:{}", e);
        }
        return Result.newSuccess();
    }

    @Override
    public Result refreshMailEaStatisticsJob(String ea) {
        if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null) {
            log.info("EmailServiceImpl.refreshMailEaStatisticsJob failed enterprise stop or license expire ea:{}", ea);
            return Result.newSuccess();
        }
        MailAccountEntity mailAccountEntity = mailAccountDAO.getAccountByEaAndType(ea, MailApiUserTypeEnum.DEFAULT.getType());
        if (mailAccountEntity == null) {
            return Result.newSuccess();
        }
        boolean lock = redisManager.lockMailEaStatistics(ea);
        if (!lock) {
            log.warn("EmailServiceImpl.refreshMailAllStatisticsJob lock fail");
            Result.newSuccess();
        }
        try {
            // 7天
            Date endTime = new Date();
            Date startTime = DateUtil.minusDay(endTime, 7);
            List<Integer> labelIdList = mailSendTaskDAO.getLabelIdByEaAndCreateTime(ea, DateUtil.getMorningDate(startTime));
            long t1 = System.currentTimeMillis();
            statDayStatisticsByTime(ea, startTime.getTime(), endTime.getTime(), MailEaStatisticsDateTypeEnum.SEVEN_DAY, labelIdList);
            long t2 = System.currentTimeMillis();
            // 30天
            startTime = DateUtil.minusDay(endTime, 30);
            labelIdList = mailSendTaskDAO.getLabelIdByEaAndCreateTime(ea, DateUtil.getMorningDate(startTime));
            statDayStatisticsByTime(ea, startTime.getTime(), endTime.getTime(), MailEaStatisticsDateTypeEnum.THIRTY_DAY, labelIdList);
            long t3 = System.currentTimeMillis();
            log.info("refreshMailEaStatisticsJob seven day cost: {} thirty day cost: {}", t2 - t1, t3 - t2);
        } catch (Exception e) {
            log.error("EmailServiceImpl.refreshMailStatisticsJob error account: {}", mailAccountEntity, e);
        } finally {
            redisManager.unLockMailEaStatistics(ea);
        }
        return Result.newSuccess();
    }

    @Override
    public Result refreshMailTaskStatisticsJob(String ea, String taskId) {

        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getByEaAndId(ea, taskId);
        if (mailSendTaskEntity == null || mailSendTaskEntity.getLabelId() == null) {
            log.info("EmailServiceImpl.refreshMailTaskStatisticsJob error mailSendTaskEntity is null or label is null,ea:{} taskId:{}", ea, taskId);
            return Result.newSuccess();
        }
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null) {
            log.info("EmailServiceImpl.refreshMailTaskStatisticsJob ea is stop,ea:{}", ea);
            return Result.newSuccess();
        }
        boolean lock = redisManager.lockMailTaskStatistics(ea, taskId);
        if (!lock) {
            log.warn("EmailServiceImpl.refreshMailTaskStatisticsJob lock fail, ea:{} taskId:{}", ea, taskId);
            return Result.newSuccess();
        }
        try {
            Date startDate;
            if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
                startDate = mailSendTaskEntity.getCreateTime();
            } else {
                startDate = new Date(mailSendTaskEntity.getFixTime());
            }
            String labelId = String.valueOf(mailSendTaskEntity.getLabelId());
            String startTime = DateUtil.format2(startDate);
            String endTime = DateUtil.format2(new Date());
            StatDayStatisticsVO statdayStatisticsVO = new StatDayStatisticsVO();
            statdayStatisticsVO.setEa(mailSendTaskEntity.getEa());
            statdayStatisticsVO.setStartDate(startTime);
            statdayStatisticsVO.setEndDate(endTime);
            statdayStatisticsVO.setLabelIdList(labelId);
            statdayStatisticsVO.setAggregate(1);
            StatDayStatisticsResp statDayStatisticsResp = mailManager.statDayStatisticsFromSendCloud(statdayStatisticsVO);
            if (statDayStatisticsResp == null || statDayStatisticsResp.getSingleStatistics() == null) {
                log.info("EmailServiceImpl.refreshMailTaskStatisticsJob statDayStatisticsResp is null or dataList is null,ea:{} taskId:{}", ea, taskId);
                return Result.newSuccess();
            }
            StatDayDetail statDayDetail = statDayStatisticsResp.getSingleStatistics();
            long requestNum = statDayDetail.getRequestNum() == null ? 0L : statDayDetail.getRequestNum();
            long deliveredNum = statDayDetail.getDeliveredNum() == null ? 0L : statDayDetail.getDeliveredNum();
            long openNum = statDayDetail.getOpenNum() == null ? 0L : statDayDetail.getOpenNum();
            long clickNum = statDayDetail.getClickNum() == null ? 0L : statDayDetail.getClickNum();
            // 统计打开人数与点击人数
            MailUserStatisticBO userStatisticBO = mailManager.queryOpenAndClickUserNumByLabelId(mailSendTaskEntity.getEa(), labelId, startTime, endTime);
            long clickUserNum = userStatisticBO.getClickUserNum();
            long openUserNum = userStatisticBO.getOpenUserNum();

            MailTaskStatisticsEntity mailTaskStatisticsEntity = new MailTaskStatisticsEntity();
            mailTaskStatisticsEntity.setId(UUIDUtil.getUUID());
            mailTaskStatisticsEntity.setEa(mailSendTaskEntity.getEa());
            mailTaskStatisticsEntity.setTaskId(mailSendTaskEntity.getId());
            mailTaskStatisticsEntity.setSendNum(requestNum);
            mailTaskStatisticsEntity.setDeliveredNum(deliveredNum);
            mailTaskStatisticsEntity.setOpenNum(openNum);
            mailTaskStatisticsEntity.setClickNum(clickNum);
            mailTaskStatisticsEntity.setOpenUserNum(openUserNum);
            mailTaskStatisticsEntity.setClickUserNum(clickUserNum);

            MailTaskStatisticsEntity existStatisticsEntity = mailTaskStatisticsDAO.getByEaAndTaskId(ea, taskId);
            if (existStatisticsEntity == null) {
                mailTaskStatisticsDAO.upsertStatisticsData(mailTaskStatisticsEntity);
                return Result.newSuccess();
            }
            // 只有查出数据才更新，避免sendCloud接口有问题导致返回了0，导致之前的统计数据被刷掉
            if (requestNum > 0 || deliveredNum > 0 || openNum > 0 || clickNum > 0 || clickUserNum > 0 || openUserNum > 0) {
                mailTaskStatisticsEntity.setId(existStatisticsEntity.getId());
                mailTaskStatisticsDAO.update(mailTaskStatisticsEntity);
            }
        } catch (Exception e) {
            log.error("EmailServiceImpl.refreshMailTaskStatisticsJob error ea: {} taskId: {}", ea, taskId, e);
        } finally {
            redisManager.unLockMailTaskStatistics(ea, taskId);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> refreshMailAllStatisticsJob(String ea) {
        ContextUtil.buildNewTraceContext();
        ThreadPoolUtils.execute(() -> refreshMailEaStatisticsJob(ea), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> exportMailUserSendDetail(ExportMailUserSendDetailArg arg) {
        if (StringUtils.isBlank(arg.getTaskId())) {
            throw new BusinessException(SHErrorCode.PARAMS_ERROR);
        }
        Result<GetTaskDetailByIdResult> taskDetailResult = getTaskDetailById(arg.getTaskId());
        if (!taskDetailResult.isSuccess() || taskDetailResult.getData() == null) {
            throw new BusinessException(SHErrorCode.PARAMS_ERROR);
        }
        GetTaskDetailByIdResult taskDetail = taskDetailResult.getData();
        if (taskDetail.getSendStatus() != MailSendStatusEnum.SEND_FAILED.getStatus()
                && taskDetail.getSendStatus() != MailSendStatusEnum.SEND_SUCCESS.getStatus()) {
            throw new BusinessException(SHErrorCode.MAIL_EXPORT_STATUS_ERROR);
        }

        ThreadPoolUtils.executeWithTraceContext(() -> {
            log.info("开始导出邮件发送明细,arg: {}", arg);
            try {
                // sendCloud最多支持100
                int pageSize = 100;
                QueryMailUserSendDetailVO vo = BeanUtil.copy(arg, QueryMailUserSendDetailVO.class);
                vo.setEa(arg.getEa());
                vo.setFsUserId(arg.getFsUserId());
                vo.setPageNum(1);
                vo.setPageSize(pageSize);
                Result<PageResult<QueryMailUserSendDetailResult>> result = queryMailUserSendDetail(vo);
                log.info("queryMailUserSendDetail page: {} result: {}", 1, result);
                if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData().getResult())) {
                    List<QueryMailUserSendDetailResult> detailResultList = result.getData().getResult();
                    SXSSFWorkbook sxssfWorkbook = ExcelUtil.generateSXSSFExcel();
                    String sheetName = I18nUtil.get(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_1800) + taskDetailResult.getData().getSubject() + "-"
                            + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".xlsx";
                    sheetName = ExcelUtil.createSafeSheetName(sheetName);
                    SXSSFSheet sxssfSheet = sxssfWorkbook.createSheet(sheetName);
                    sxssfSheet.setDefaultColumnWidth(20);
                    log.info("queryMailUserSendDetail 创建表格成功 sheetName: {}", sheetName);
                    // 填充标题行
                    ExcelUtil.fillTitles(sxssfSheet, TITLE_LIST);
                    // 填充第一页的数据
                    ExcelUtil.appendContent(sxssfSheet, buildDataList(detailResultList), 1);
                    log.info("queryMailUserSendDetail appendContent success, page: {}", 1);
                    Integer totalCount = result.getData().getTotalCount();
                    int totalPage = totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize) + 1;
                    if (totalPage > 1) {
                        for (int i = 2; i <= totalPage; i++) {
                            vo.setPageNum(i);
                            result = queryMailUserSendDetail(vo);
                            log.info("queryMailUserSendDetail page: {} result: {}", i, result);
                            // 追加填充每一页的数据
                            ExcelUtil.appendContent(sxssfSheet, buildDataList(result.getData().getResult()), (i - 1) * pageSize + 1);
                            log.info("queryMailUserSendDetail appendContent success, page: {}", 2);
                        }
                    }
                    log.info("开始发送表格数据，arg: {},totalCount: {}", arg, totalCount);
                    pushSessionManager.pushExcelToFileAssistant(sxssfWorkbook, sheetName, arg.getEa(), arg.getFsUserId());
                }
            } catch (Exception e) {
                log.error("exportMailUserSendDetail error, arg: {}", arg, e);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);


        return Result.newSuccess();
    }


    private List<List<Object>> buildDataList(List<QueryMailUserSendDetailResult> sendDetailResultList) {
        List<List<Object>> result = Lists.newArrayList();
        for (QueryMailUserSendDetailResult sendDetailResult : sendDetailResultList) {
            List<Object> objectList = Lists.newArrayList();
            objectList.add(StringUtils.defaultString(sendDetailResult.getName()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getPhone()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getEmail()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getCompanyName()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getStatus()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getStatusDesc()));
            objectList.add(StringUtils.defaultString(sendDetailResult.getSendTime()));
            objectList.add(sendDetailResult.getOpen());
            objectList.add(sendDetailResult.getClick());
            objectList.add(StringUtils.defaultString(sendDetailResult.getSpam() ? I18nUtil.get(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_1849) : I18nUtil.get(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_1849_1)));
            objectList.add(StringUtils.defaultString(sendDetailResult.getUnsubscribe() ? I18nUtil.get(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_1849) : I18nUtil.get(I18nKeyEnum.MARK_MAIL_EMAILSERVICEIMPL_1849_1)));
            result.add(objectList);
        }
        return result;
    }

    public void statDayStatisticsByTime(String ea, Long startTime, Long endTime, MailEaStatisticsDateTypeEnum dateType, List<Integer> labelIdList) {
        if (CollectionUtils.isEmpty(labelIdList)) {
            return;
        }
        StatDayStatisticsVO statDayStatisticsVO = new StatDayStatisticsVO();
        statDayStatisticsVO.setEa(ea);
        statDayStatisticsVO.setAggregate(1);
        statDayStatisticsVO.setLabelIdList(StringUtils.join(labelIdList, ";"));
        statDayStatisticsVO.setStartTime(startTime);
        statDayStatisticsVO.setEndTime(endTime);
        MailStatisticsResp mailStatisticsResp = mailManager.statDayStatisticsToJob(statDayStatisticsVO);
        if (mailStatisticsResp == null) {
            return;
        }
        MailEaStatisticsEntity mailEaStatisticsEntity = new MailEaStatisticsEntity();
        mailEaStatisticsEntity.setId(UUIDUtil.getUUID());
        mailEaStatisticsEntity.setEa(ea);
        mailEaStatisticsEntity.setDateType(dateType.getType());
        mailEaStatisticsEntity.setDeliveredNum(mailStatisticsResp.getDeliveredNum());
        mailEaStatisticsEntity.setOpenNum(mailStatisticsResp.getOpenNum());
        mailEaStatisticsEntity.setClickNum(mailStatisticsResp.getClickNum());
        mailEaStatisticsEntity.setOpenUserNum(mailStatisticsResp.getOpenUserNum());
        mailEaStatisticsEntity.setClickUserNum(mailStatisticsResp.getClickUserNum());

        MailEaStatisticsEntity existStatisticsEntity = mailEaStatisticsDAO.getMailEaStatisticsByType(ea, dateType.getType());
        if (existStatisticsEntity == null) {
            mailEaStatisticsDAO.upsertStatisticsData(mailEaStatisticsEntity);
            return;
        }
        // 有效数据才更新，全是0就不更新了，避免查sendCloud出问题了，会数据清空了
        if (mailStatisticsResp.isAvailable()) {
            mailEaStatisticsEntity.setId(existStatisticsEntity.getId());
            mailEaStatisticsDAO.update(mailEaStatisticsEntity);
        }
    }

    private static String getParam(String url, String name) {
        if (StringUtils.isBlank(url) || !url.contains("?")) {
            return null;
        }
        String params = url.substring(url.indexOf("?") + 1, url.length());
        try {
            Map<String, String> split = Splitter.on("&").withKeyValueSeparator("=").split(params);
            return split.get(name);
        } catch (Exception e) {
            log.warn("EmailServiceImpl.getParam error e:{}", e);
        }
        return null;
    }

    @Override
    public void emailSendStatusTaskSchedule(String taskId) {
        String key = DELIVERED_EMAIL_TASK_KEY + ":" + taskId;
        boolean lock = redisManager.lockIgnoreThread(key, 60 * 60 * 12);
        if (!lock) {
            log.info("get lock fail, key: {}", key);
            return;
        }
        try {
            MailSendTaskEntity mailSendTask = mailSendTaskDAO.getById(taskId);
            handleSingleTaskSendStatus(mailSendTask, false);
        } finally {
            boolean unlockResult = redisManager.unLockIgnoreThread(key);
            log.info("unlockResult, key: {} result: {}", key, unlockResult);
        }
    }

    private void handleSingleTaskSendStatus(MailSendTaskEntity taskEntity, boolean onlyHandleObj) {
        try {
            QueryEmailStatusVO queryEmailStatusVO = new QueryEmailStatusVO();
            queryEmailStatusVO.setEa(taskEntity.getEa());
            Date startDate;
            if (taskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
                startDate = taskEntity.getCreateTime();
            } else {
                startDate = new Date(taskEntity.getFixTime());
            }
            Date endDate = DateUtil.plusDay(startDate, 2);
            queryEmailStatusVO.setStartDate(DateUtil.format2(startDate));
            queryEmailStatusVO.setEndDate(DateUtil.format2(endDate));
            if (taskEntity.getMailType().equals(MailApiUserTypeEnum.TRIGGER.getType())) {
                // 目前好像没有这个类型的邮件
                int totalCount = mailSendTaskResultDAO.countMailIdByByTaskId(taskEntity.getId(), taskEntity.getEa());
                int pageSize = 1000;
                int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
                for (int i = 1; i <= totalPage; i++) {
                    Page page = new Page(i, pageSize, false);
                    List<String> emailIdList = mailSendTaskResultDAO.queryEmailIdByTaskId(taskEntity.getId(), taskEntity.getEa(), page);
                    if (CollectionUtils.isEmpty(emailIdList)) {
                        break;
                    }
                    queryEmailStatusVO.setEmailIds(String.join(";", emailIdList));
                    queryEmailStatusVO.setStart(0);
                    queryEmailStatusVO.setLimit(100);
                    QueryEmailStatusResp queryEmailStatusResp = mailManager.queryEmailStatus(queryEmailStatusVO);
                    handleEmailSendStatus(taskEntity.getEa(), taskEntity.getId(), queryEmailStatusResp, onlyHandleObj);
                }
            } else {
                Integer labelId = taskEntity.getLabelId();
                if (labelId == null) {
                    return;
                }
                queryEmailStatusVO.setLabelId(labelId);
                int start = 0;
                int limit = 100;
                int totalCount = 0;
                int maxCount = 500000;
                while (true) {
                    queryEmailStatusVO.setStart(start);
                    queryEmailStatusVO.setLimit(limit);
                    // sendCloud的接口有点问题，有时候totalCount 和 列表不一致，totalCount会多(这个数据是对的)，但是列表会少，
                    QueryEmailStatusResp queryEmailStatusResp = mailManager.queryEmailStatus(queryEmailStatusVO);
                    if (queryEmailStatusResp == null || CollectionUtils.isEmpty(queryEmailStatusResp.getVoList())) {
                        break;
                    }
                    handleEmailSendStatus(taskEntity.getEa(), taskEntity.getId(), queryEmailStatusResp, onlyHandleObj);
                    int size = queryEmailStatusResp.getVoList().size();
                    totalCount += size;
                    if (size < limit || totalCount >= queryEmailStatusResp.getTotal() || totalCount >= maxCount) {
                        break;
                    }
                    start = start + limit;
                }
            }
        } catch (Exception e) {
            log.error("getDeliveredEmailTaskSchedule error, task: {}", taskEntity, e);
        }
    }

    @Override
    public void manualHandleSingleTaskSendStatus(String param) {
        ThreadPoolUtils.execute(() -> {
            log.info("manualHandleSingleTaskSendStatus start, param: {}", param);
            String[] arr = param.split("-");
            String taskId = arr[0];
            boolean onlyHandleObj = Boolean.parseBoolean(arr[1]);
            MailSendTaskEntity task = mailSendTaskDAO.getById(taskId);
            if (task == null) {
                return;
            }
            handleSingleTaskSendStatus(task, onlyHandleObj);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    @Override
    public Result<PageResult<PageQueryMailTemplateResult>> listPagerMergeTemplate(PageQueryTemplateVO vo) {

        PageResult<PageQueryMailTemplateResult> pageResult = new PageResult();
        List<PageQueryMailTemplateResult> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<PagerMergeTemplateEntity> entities = mailTemplateDAO.pagerMergeTemplateList(vo.getEa(), vo.getKeyword(), page);
        if (CollectionUtils.isEmpty(entities)) {
            return Result.newSuccess(pageResult);
        }
        for (PagerMergeTemplateEntity entity : entities) {
            PageQueryMailTemplateResult result = new PageQueryMailTemplateResult();
            result.setId(entity.getId());
            result.setFsUserId(entity.getFsUserId());
            result.setName(entity.getName());
            result.setTemplateType(entity.getTemplateType());
            result.setCreateTime(entity.getCreateTime().getTime());
            result.setUpdateTime(entity.getUpdateTime().getTime());
            result.setContent(entity.getContent());
            result.setEmailId(entity.getEmailId());
            resultList.add(result);
        }
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    private void handleEmailSendStatus(String ea, String taskId, QueryEmailStatusResp queryEmailStatusResp, boolean onlyHandleObj) {

        if (queryEmailStatusResp == null || CollectionUtils.isEmpty(queryEmailStatusResp.getVoList())) {
            return;
        }
        List<VoList> voList = queryEmailStatusResp.getVoList();
        for (VoList vo : voList) {
            // 创建营销动态和问题邮件处理
            if (!onlyHandleObj) {
                createUserMarketingActionAndErrorEmail(ea, taskId, vo);
            }
            // 处理邮件发送明细对象
            handleEmailSendRecordDetailObjByJob(ea, taskId, vo);
        }
    }

    private void handleEmailSendRecordDetailObjByJob(String ea, String taskId, VoList vo) {
        String receiver = vo.getRecipients();
        if (StringUtils.isBlank(receiver)) {
            return;
        }
        receiver = receiver.toLowerCase();
        ObjectData objectData = emailSendRecordDetailObjManager.getByTaskIdAndReceiver(ea, taskId, receiver);
        if (objectData != null) {
            // 更新邮件发送明细对象的发送状态、和失败原因
            updateEmailSendRecordDetailObjByJob(ea, taskId, objectData, vo);
        } else {
            // 创建邮件发送明细对象
            createEmailSendRecordDetailObjByJob(ea, taskId, vo, receiver);
        }

    }

    private void updateEmailSendRecordDetailObjByJob(String ea, String taskId, ObjectData objectData, VoList vo) {
        String sendStatus = getSendStatus(vo);
        String oldSendStatus = objectData.getString("send_status");
        if (StringUtils.equals(sendStatus, oldSendStatus)) {
            // 如果本次从sendCloud查询的状态和之前的一样，就不更新了
            return;
        }
        UpdateEmailSendRecordDetailObjArg arg = new UpdateEmailSendRecordDetailObjArg();
        arg.setSendStatus(sendStatus);
        arg.setStatusCode(getStatusCode(vo));
        if ("un_send".equals(sendStatus)) {
            // 发送失败  status的字段比subStatus的原因更详细一点 这里用status
            arg.setFailReason(vo.getStatus());
            triggerEmailSOPTaskBySendStatus(ea, objectData.getString("receiver"), "fail");
        } else if ("sent".equals(sendStatus)) {
            triggerEmailSOPTaskBySendStatus(ea, objectData.getString("receiver"), "success");
        }
        arg.setEa(ea);
        arg.setId(objectData.getId());
        arg.setTaskId(taskId);
        arg.setReceiver(vo.getRecipients().toLowerCase());
        emailSendRecordDetailObjManager.tryUpdateAndAccumulateCountObj(arg);
    }

    private void triggerEmailSOPTaskBySendStatus(String ea, String receiver, String sendStatus) {
        ThreadPoolUtils.execute(() -> {
            // 查询营销用户信息
            String marketingUserId = null;
            Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(Lists.newArrayList(receiver), ea, 1000);
            if (MapUtils.isNotEmpty(emailMarketingDetailInfoMap)) {
                EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(receiver);
                if (emailMarketingDetailInfo != null) {
                    marketingUserId = emailMarketingDetailInfo.getMarketingUserId();
                }
            }
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("actionType", "MAIL");
            paramMap.put("actionStatus", sendStatus);//? "success" : "fail"
            log.info("startTaskInstanceByMarketingUserId actionType:MAIL receiver:{} marketingUserId:{} paramMap:{}", receiver, marketingUserId, paramMap);
            triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, marketingUserId, paramMap);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    private void createEmailSendRecordDetailObjByJob(String ea, String taskId, VoList vo, String receiver) {
        CreateEmailSendRecordDetailObjArg arg = new CreateEmailSendRecordDetailObjArg();
        arg.setTaskId(taskId);
        MarketingActivityExternalConfigEntity externalConfig = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType(), taskId);
        arg.setMarketingActivityId(externalConfig == null ? null : externalConfig.getMarketingActivityId());

        String sendStatus = getSendStatus(vo);
        arg.setSendStatus(sendStatus);
        arg.setStatusCode(getStatusCode(vo));
        if ("un_send".equals(sendStatus)) {
            // 发送失败  status的字段比subStatus的原因更详细一点 这里用status
            arg.setFailReason(vo.getStatus());
            triggerEmailSOPTaskBySendStatus(ea, receiver, "fail");
        } else if ("sent".equals(sendStatus)) {
            triggerEmailSOPTaskBySendStatus(ea, receiver, "success");
        }
        arg.setReceiver(receiver);
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(taskId);
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            arg.setSendTime(mailSendTaskEntity.getCreateTime());
        } else {
            arg.setSendTime(new Date(mailSendTaskEntity.getFixTime()));
        }
        arg.setPreviewUrl(host + "/XV/UI/Home#/app/marketing/index/=/mail-marketing/preview?runtime=desktop&mailId=" + taskId);
        arg.setEa(ea);
        arg.setFsUserId(mailSendTaskEntity.getFsUserId());
        arg.setMarketingEventId(mailSendTaskEntity.getMarketingEventId());
        emailSendRecordDetailObjManager.tryCreateOrUpdateObj(arg);
    }

    private void createUserMarketingActionAndErrorEmail(String ea, String taskId, VoList vo) {
        String subStatusStr = vo.getSubStatus();
        if (StringUtils.isBlank(subStatusStr)) {
            return;
        }
        int subStatus = Integer.parseInt(subStatusStr);
        // 0为送达 无效邮件对应subStatus为4XX，软退信对应subStatus为5xx， 这里只处理送达、无效邮件、软退信
        if (subStatus != 0 && (subStatus < 400 || subStatus >= 600)) {
            return;
        }
        String emailId = vo.getEmailId();
        boolean isExist = idempotentRecordManager.existRecord(emailId);
        if (isExist) {
            return;
        }
        boolean success = idempotentRecordManager.insertRecord(emailId);
        if (!success) {
            return;
        }
        String recipients = vo.getRecipients();
        Map<String, String> sendAddressByLowerCase = mailManager.getSendAddressByLowerCase(Lists.newArrayList(recipients));
        if (MapUtils.isNotEmpty(sendAddressByLowerCase) && StringUtils.isNotBlank(sendAddressByLowerCase.get(recipients))) {
            recipients = sendAddressByLowerCase.get(recipients);
        }
        if (subStatus != 0) {
            MailSendErrorAddressEntity mailSendErrorAddressEntity = new MailSendErrorAddressEntity();
            mailSendErrorAddressEntity.setId(UUIDUtil.getUUID());
            mailSendErrorAddressEntity.setEa(ea);
            mailSendErrorAddressEntity.setTaskId(taskId);
            mailSendErrorAddressEntity.setAddress(recipients);
            Integer actionType = subStatus < 500 ? WebHookEventEnum.INVALID.getMarketingUserType() : WebHookEventEnum.SOFT_BOUNCE.getMarketingUserType();
            mailSendErrorAddressEntity.setEventType(actionType);
            mailSendErrorAddressEntity.setSubStat(subStatus);
            mailSendErrorAddressDAO.insertMailSendErrorAddress(mailSendErrorAddressEntity);
            return;
        }
        String marketingUserId = null;
        // 查询营销用户信息
        Map<String, EmailMarketingDetailInfo> emailMarketingDetailInfoMap = mailManager.getMarketingActivityUserByEmail(Lists.newArrayList(recipients), ea, 1000);
        if (MapUtils.isNotEmpty(emailMarketingDetailInfoMap)) {
            EmailMarketingDetailInfo emailMarketingDetailInfo = emailMarketingDetailInfoMap.get(recipients);
            if (emailMarketingDetailInfo != null) {
                marketingUserId = emailMarketingDetailInfo.getMarketingUserId();
            }
        }
        if (StringUtils.isBlank(marketingUserId)) {
            return;
        }
        // 查询营销用户是否有绑定无身份
        UserMarketingAccountEntity userMarketingAccountEntity = userMarketingAccountDAO.getById(marketingUserId);
        if (userMarketingAccountEntity == null) {
            return;
        }
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao
                .getMarketingActivityExternalConfigEntity(ea, taskId, AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType());
        String browserUserId = buildUserMarketingBrowserUserRelation(marketingUserId, ea);
        MarketingUserActionEvent marketingUserActionEvent = new MarketingUserActionEvent();
        marketingUserActionEvent.setActionType(MarketingUserActionType.MAIL_DELIVER.getActionType());
        marketingUserActionEvent.setChannelType(MarketingUserActionChannelType.EMAIL.getChannelType());
        marketingUserActionEvent.setFingerPrint(browserUserId);
        long requestTime = getRequestTime(vo.getRequestTime(), taskId);
        marketingUserActionEvent.setActionTime(requestTime);
        marketingUserActionEvent.setSceneType(MarketingUserActionSceneType.EMAIL_HOOK.getSceneType());
        marketingUserActionEvent.setObjectId(taskId);
        marketingUserActionEvent.setEa(ea);
        marketingUserActionEvent.setObjectType(ObjectTypeEnum.MAIL_TASK.getType());
        String marketingEventId = null;
        if (marketingActivityExternalConfigEntity != null) {
            marketingUserActionEvent.setMarketingActivityId(marketingActivityExternalConfigEntity.getMarketingActivityId());
            marketingEventId = marketingActivityExternalConfigEntity.getMarketingEventId();
        }
        marketingUserActionEvent.setMarketingEventId(marketingEventId);
        Map<String, Object> extensionParams = Maps.newHashMap();
        extensionParams.put(RecordActionArg.SPREAD_CHANNEL_KEY, SpreadChannelEnum.EMAIL.getCode());
        extensionParams.put(RecordActionArg.MARKETING_SCENE_KEY, marketingEventCommonSettingManager.getMarketingScene(ea, marketingEventId, marketingUserActionEvent.getMarketingActivityId(), null));
        extensionParams.put("createTime", requestTime);
        marketingUserActionEvent.setExtensionParams(extensionParams);
        marketingRecordActionSender.send(marketingUserActionEvent);
    }


    private long getRequestTime(String requestTime, String taskId) {
        if (StringUtils.isNotBlank(requestTime)) {
            Date date = DateUtil.parse(requestTime);
            if (date != null) {
                return date.getTime();
            }
        }
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(taskId);
        if (mailSendTaskEntity != null) {
            if (mailSendTaskEntity.getFixTime() != null) {
                return mailSendTaskEntity.getFixTime();
            }
            return mailSendTaskEntity.getCreateTime().getTime();
        }
        return System.currentTimeMillis();
    }


    private void handleEmailSendRecordDetailObjByCallBack(String ea, String taskId, WebHookCallBackVO vo) {
        String receiver = vo.getRecipient();
        if (StringUtils.isBlank(receiver)) {
            return;
        }
        receiver = receiver.toLowerCase();
        ObjectData objectData = emailSendRecordDetailObjManager.getByTaskIdAndReceiver(ea, taskId, receiver);
        if (objectData != null) {
            // 更新邮件发送明细对象的点击次数、打开次数、是否取消订阅、是否垃圾举报
            updateEmailSendRecordDetailObjByCallBack(ea, taskId, objectData, vo);
        } else {
            // 创建邮件发送明细对象
            createEmailSendRecordDetailObjByCallBack(ea, taskId, vo, receiver);
        }

    }

    private void updateEmailSendRecordDetailObjByCallBack(String ea, String taskId, ObjectData objectData, WebHookCallBackVO vo) {
        UpdateEmailSendRecordDetailObjArg arg = new UpdateEmailSendRecordDetailObjArg();
        if (WebHookEventEnum.OPEN.getEvent().equals(vo.getEvent())) {
            arg.setOpenCount(1);
        } else if (WebHookEventEnum.CLICK.getEvent().equals(vo.getEvent())) {
            arg.setClickCount(1);
        } else if (WebHookEventEnum.UNSUBSCRIBE.getEvent().equals(vo.getEvent())) {
            arg.setIsUnSubscribe(true);
        } else if (WebHookEventEnum.REPORT_SPAM.getEvent().equals(vo.getEvent())) {
            arg.setSpamReport(true);
        }
        arg.setEa(ea);
        arg.setId(objectData.getId());
        arg.setTaskId(taskId);
        arg.setReceiver(vo.getRecipient().toLowerCase());
        emailSendRecordDetailObjManager.tryUpdateAndAccumulateCountObj(arg);
    }

    private void createEmailSendRecordDetailObjByCallBack(String ea, String taskId, WebHookCallBackVO vo, String receiver) {
        CreateEmailSendRecordDetailObjArg arg = new CreateEmailSendRecordDetailObjArg();
        arg.setTaskId(taskId);
        MarketingActivityExternalConfigEntity externalConfig = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType(), taskId);
        arg.setMarketingActivityId(externalConfig == null ? null : externalConfig.getMarketingActivityId());
        arg.setReceiver(receiver);
        MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(taskId);
        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
            arg.setSendTime(mailSendTaskEntity.getCreateTime());
        } else {
            arg.setSendTime(new Date(mailSendTaskEntity.getFixTime()));
        }
        if (WebHookEventEnum.OPEN.getEvent().equals(vo.getEvent())) {
            arg.setOpenCount(1);
            arg.setSendStatus("sent");
            arg.setStatusCode("1");
        } else if (WebHookEventEnum.CLICK.getEvent().equals(vo.getEvent())) {
            arg.setClickCount(1);
            arg.setSendStatus("sent");
            arg.setStatusCode("1");
        } else if (WebHookEventEnum.UNSUBSCRIBE.getEvent().equals(vo.getEvent())) {
            arg.setIsUnSubscribe(true);
            arg.setSendStatus("sent");
            arg.setStatusCode("1");
        } else if (WebHookEventEnum.REPORT_SPAM.getEvent().equals(vo.getEvent())) {
            arg.setSpamReport(true);
            arg.setSendStatus("sent");
            arg.setStatusCode("1");
        }
        if ("sent".equals(arg.getSendStatus())) {
            triggerEmailSOPTaskBySendStatus(ea, receiver, "success");
        }
        // 其他事件由于不知道具体的原因，等定时任务(emailSendStatusTaskSchedule())来更新对象的sendStatus、statusCode、failReason
        arg.setPreviewUrl(host + "/XV/UI/Home#/app/marketing/index/=/mail-marketing/preview?runtime=desktop&mailId=" + taskId);
        arg.setEa(ea);
        arg.setFsUserId(mailSendTaskEntity.getFsUserId());
        arg.setMarketingEventId(mailSendTaskEntity.getMarketingEventId());
        emailSendRecordDetailObjManager.tryCreateOrUpdateObj(arg);
    }


    private String getSendStatus(VoList vo) {
        String status = vo.getStatus();
        if ("请求中".equals(status)) {
            return "wait_send";
        } else if ("送达".equals(status)) {
            return "sent";
        }
        return "un_send";
    }

    private String getStatusCode(VoList vo) {
        String status = vo.getStatus();
        if ("请求中".equals(status)) {
            return "18";
        } else if ("送达".equals(status)) {
            return "1";
        }
        return vo.getSubStatus();
    }
}