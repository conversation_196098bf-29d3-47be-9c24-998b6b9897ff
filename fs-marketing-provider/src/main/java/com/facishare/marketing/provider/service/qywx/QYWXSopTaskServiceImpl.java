package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.qywx.QywxGroupSendRangeTypeEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.marketing.api.arg.qywx.sop.GetSopTaskDetailArg;
import com.facishare.marketing.api.result.qywx.SopTaskDetailResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactListResult;
import com.facishare.marketing.api.service.qywx.QywxSopTaskService;
import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.TriggerTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.innerArg.qywx.CustomerGroupDetailArg;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupDetailResult;
import com.facishare.marketing.provider.innerResult.qywx.QueryQywxAppDetailResult;
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.TriggerTaskInstanceManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.MomentManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("qywxSopTaskService")
public class QYWXSopTaskServiceImpl implements QywxSopTaskService {

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private MomentManager momentManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private TriggerTaskInstanceDao triggerTaskInstanceDao;
    @Autowired
    private TriggerTaskSnapshotDao triggerTaskSnapshotDao;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private QywxTaskDao qywxTaskDao;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;
    @Autowired
    private TriggerTaskInstanceManager triggerTaskInstanceManager;
    @Autowired
    private TriggerInstanceDao triggerInstanceDao;

    @Override
    public Result<SopTaskDetailResult> getSopTaskDetail(GetSopTaskDetailArg arg) {
        TriggerTaskInstanceEntity taskInstanceEntity = triggerTaskInstanceDao.getById(arg.getTriggerTaskInstanceId());
        if (null == taskInstanceEntity) {
            return Result.newError(SHErrorCode.QYWX_SOP_TASK_ERROR.getErrorCode(), SHErrorCode.QYWX_SOP_TASK_ERROR.getErrorMessage());
        }
        SopTaskDetailResult res = new SopTaskDetailResult();
        TriggerTaskSnapshotEntity taskSnapshotEntity = triggerTaskSnapshotDao.getById(taskInstanceEntity.getTriggerTaskSnapshotId());
        SopTaskDetailResult.QywxMessageContent qywxMessageContent = GsonUtil.fromJson(taskSnapshotEntity.getWxMessageContent(), SopTaskDetailResult.QywxMessageContent.class);
        res.setMessageContent(qywxMessageContent);
        res.setContext(taskSnapshotEntity.getMessageContent());
        res.setAppName(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2704));
        String appName = getAppName(arg.getFsEa());
        if (StringUtils.isNotBlank(appName)) {
            res.setAppName(appName);
        }
        res.setTaskEnd(taskInstanceEntity.getTaskEnd());
        return Result.newSuccess(res);
    }

    @Override
    public Result<SopTaskDetailResult> getSopTaskCustomerList(GetSopTaskDetailArg arg) {
        TriggerTaskInstanceEntity taskInstanceEntity = triggerTaskInstanceDao.getById(arg.getTriggerTaskInstanceId());
        if (null == taskInstanceEntity) {
            return Result.newError(SHErrorCode.QYWX_SOP_TASK_ERROR.getErrorCode(), SHErrorCode.QYWX_SOP_TASK_ERROR.getErrorMessage());
        }
        SopTaskDetailResult sopTaskDetailResult = new SopTaskDetailResult();
        sopTaskDetailResult.setPageSize(arg.getPageSize());
        sopTaskDetailResult.setPageNo(arg.getPageNum());
        sopTaskDetailResult.setTotal(0);
        sopTaskDetailResult.setFinishCount(0);
//        if (org.apache.commons.lang3.StringUtils.isEmpty(taskInstanceEntity.getToUser())) {
//            return Result.newSuccess(sopTaskDetailResult);
//        }
//        if (null == taskInstanceEntity.getToSize() && 0 == taskInstanceEntity.getToSize()) {
//            return Result.newSuccess(sopTaskDetailResult);
//        }
//        sopTaskDetailResult.setTotal(taskInstanceEntity.getToSize());
        List<String> finishExtenalUserId = null;
        //客sop
//        if (null != taskInstanceEntity.getType() && 1 == taskInstanceEntity.getType()) {
        sopTaskDetailResult.setType(1);
        TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getById(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerSnapshotId());
        if (triggerSnapshot.getSendRange() != QywxGroupSendRangeTypeEnum.GROUP.getType()) {
            TriggerSnapshotEntity triggerSnapshotEntity = triggerSnapshotDao.getById(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerSnapshotId());
            if (TriggerTypeEnum.TRIGGER_BY_ACTION.getTriggerType().equals(triggerSnapshotEntity.getTriggerType())) {
                TriggerTaskInstanceEntity triggerTaskInstanceEntity = triggerTaskInstanceDao.getById(arg.getTriggerTaskInstanceId());
                if (null == triggerTaskInstanceEntity) {
                    return Result.newSuccess(sopTaskDetailResult);
                }
                sopTaskDetailResult.setTotal(1);
                List<QueryContactListResult> data = Lists.newArrayList();
                sopTaskDetailResult.setUserList(data);
                TriggerInstanceEntity triggerInstance = triggerInstanceDao.getById(triggerTaskInstanceEntity.getTriggerInstanceId());
                List<UserMarketingWxWorkExternalUserRelationEntity> entities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(taskInstanceEntity.getEa(),
                        Collections.singletonList(triggerInstance.getMarketingUserId()));
                if (CollectionUtil.isEmpty(entities)) {
                    return Result.newSuccess(sopTaskDetailResult);
                }
                List<String> allExtenalUserId = entities.stream().map(UserMarketingWxWorkExternalUserRelationEntity::getWxWorkExternalUserId).collect(Collectors.toList());
                List<ObjectData> objectDataList = userMarketingAccountManager.pageGetObjectData(taskInstanceEntity.getEa(), allExtenalUserId, arg.getPageSize(), arg.getPageNum());
                if (CollectionUtil.isEmpty(objectDataList)) {
                    return Result.newSuccess(sopTaskDetailResult);
                }
                Map<Object, ObjectData> externalUserMap = objectDataList.stream().collect(Collectors.toMap(o -> o.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()), o -> o, (v1, v2) -> v1));
                // 批量查询标签
                Map<String, ObjectDataIdAndTagNameListData> tagMap = metadataTagManager
                        .getObjectDataIdAndTagNameListDataMapByObjectDataIds(taskInstanceEntity.getEa(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),
                                objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList()));
                Map<String, String> userMarketingIdToExternalId = entities.stream().collect(Collectors.toMap(UserMarketingWxWorkExternalUserRelationEntity::getUserMarketingId, UserMarketingWxWorkExternalUserRelationEntity::getWxWorkExternalUserId, (o, n) -> o));
                finishExtenalUserId = qywxTaskDao.getSopTaskCompleteExternalUserId(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerId(), triggerTaskInstanceEntity.getId());
                int  finishCount = finishExtenalUserId.size();
                String externalId = userMarketingIdToExternalId.get(triggerInstance.getMarketingUserId());
                ObjectData objectData = externalUserMap.get(externalId);
                QueryContactListResult queryContactListResult = new QueryContactListResult();
                if (null != objectData) {
                    queryContactListResult.setId(objectData.getId());
                    queryContactListResult.setWxName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()));
                    queryContactListResult.setRemarkName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName()));
                    if (objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName()) != null) {
                        // 头像
                        queryContactListResult.setAvatar(getAvatarFormCrmData(objectData, taskInstanceEntity.getEa()));
                    }
                    if (MapUtils.isNotEmpty(tagMap) && tagMap.get(objectData.getId()) != null) {
                        // 标签
                        queryContactListResult.setTagNameList(tagMap.get(objectData.getId()).getTagNameList());
                    }
                    queryContactListResult.setDesc(objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()).toString() : null);
                }
                queryContactListResult.setFinish(finishExtenalUserId.contains(externalId));
                queryContactListResult.setExternalUserId(externalId);
                queryContactListResult.setTriggerTaskInstanceId(triggerTaskInstanceEntity.getId());
                data.add(queryContactListResult);
                sopTaskDetailResult.setFinishCount(finishCount);
            } else {
                Page<Object> page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
                List<TriggerTaskInstanceAndMarketingUserIdEntity> pageTriggerTaskInstanceEntities = triggerTaskInstanceDao.listSopCustomerByExecuteTime(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerId(), taskInstanceEntity.getTriggerSnapshotId(), taskInstanceEntity.getTriggerTaskSnapshotId(), taskInstanceEntity.getOwnerUserId(), taskInstanceEntity.getBatchId(), page);
                Integer totalCustomer = triggerTaskInstanceDao.countSopCustomer(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerId(), taskInstanceEntity.getTriggerSnapshotId(), taskInstanceEntity.getTriggerTaskSnapshotId(), taskInstanceEntity.getOwnerUserId(), taskInstanceEntity.getBatchId());
                sopTaskDetailResult.setTotal(totalCustomer == null ? 0 : totalCustomer);
                if (CollectionUtil.isEmpty(pageTriggerTaskInstanceEntities)) {
                    return Result.newSuccess(sopTaskDetailResult);
                }
                List<QueryContactListResult> data = Lists.newArrayList();
                sopTaskDetailResult.setUserList(data);
                List<String> toUser = pageTriggerTaskInstanceEntities.stream().map(m -> m.getMarketingUserId()).collect(Collectors.toList());
                List<UserMarketingWxWorkExternalUserRelationEntity> entities = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(taskInstanceEntity.getEa(), toUser);
                List<String> allExtenalUserId = entities.stream().map(e -> e.getWxWorkExternalUserId()).collect(Collectors.toList());
                List<ObjectData> objectDataList = userMarketingAccountManager.pageGetObjectData(taskInstanceEntity.getEa(), allExtenalUserId, arg.getPageSize(), arg.getPageNum());
                if (CollectionUtil.isEmpty(objectDataList)) {
                    return Result.newSuccess(sopTaskDetailResult);
                }
                Map<Object, ObjectData> externalUserMap = objectDataList.stream().collect(Collectors.toMap(o -> o.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()), o -> o, (v1, v2) -> v1));

                // 批量查询标签
                Map<String, ObjectDataIdAndTagNameListData> tagMap = metadataTagManager
                        .getObjectDataIdAndTagNameListDataMapByObjectDataIds(taskInstanceEntity.getEa(), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),
                                objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList()));
                Map<String, String> userMarketingIdToExternalId = entities.stream().collect(Collectors.toMap(UserMarketingWxWorkExternalUserRelationEntity::getUserMarketingId, UserMarketingWxWorkExternalUserRelationEntity::getWxWorkExternalUserId, (o, n) -> o));
                int finishCount = 0;
                for (TriggerTaskInstanceAndMarketingUserIdEntity instanceEntity : pageTriggerTaskInstanceEntities) {
                    finishExtenalUserId = qywxTaskDao.getSopTaskCompleteExternalUserId(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerId(), instanceEntity.getId());
                    finishCount += finishExtenalUserId.size();
                    String externalId = userMarketingIdToExternalId.get(instanceEntity.getMarketingUserId());
                    ObjectData objectData = externalUserMap.get(externalId);
                    QueryContactListResult queryContactListResult = new QueryContactListResult();
                    if (null != objectData) {
                        queryContactListResult.setId(objectData.getId());
                        queryContactListResult.setWxName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName()));
                        queryContactListResult.setRemarkName(objectData.getString(CrmWechatWorkExternalUserFieldEnum.REMARK_NAME.getFieldName()));
                        if (objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName()) != null) {
                            // 头像
                            queryContactListResult.setAvatar(getAvatarFormCrmData(objectData, taskInstanceEntity.getEa()));
                        }
                        if (MapUtils.isNotEmpty(tagMap) && tagMap.get(objectData.getId()) != null) {
                            // 标签
                            queryContactListResult.setTagNameList(tagMap.get(objectData.getId()).getTagNameList());
                        }
                        queryContactListResult.setDesc(objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.REMARK.getFieldName()).toString() : null);
                    }
                    queryContactListResult.setFinish(finishExtenalUserId.contains(externalId));
                    queryContactListResult.setExternalUserId(externalId);
                    queryContactListResult.setTriggerTaskInstanceId(instanceEntity.getId());
                    data.add(queryContactListResult);
                }
                sopTaskDetailResult.setFinishCount(finishCount);
            }
//        } else if (null != taskInstanceEntity.getType() && 2 == taskInstanceEntity.getType()) {
        } else if (triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()) {
            sopTaskDetailResult.setType(2);
            //客群sop
            List<String> toUser = JsonUtil.fromJson(taskInstanceEntity.getToUser(), List.class);

            finishExtenalUserId = qywxTaskDao.getSopTaskCompleteExternalUserId(taskInstanceEntity.getEa(), taskInstanceEntity.getTriggerId(), arg.getTriggerTaskInstanceId());
            sopTaskDetailResult.setFinishCount(finishExtenalUserId.size());
            List<String> pageGroupId = toUser.stream().skip((arg.getPageNum() - 1) * arg.getPageSize()).limit(arg.getPageSize()).collect(Collectors.toList());
            String accessToken = momentManager.getOrCreateAccessToken(taskInstanceEntity.getEa());
            CustomerGroupDetailResult customerGroupDetailResult = null;
            List<SopTaskDetailResult.QywxGroupInfo> groupInfoList = new ArrayList<>();
            for (String groupId : pageGroupId) {
                SopTaskDetailResult.QywxGroupInfo temp = new SopTaskDetailResult.QywxGroupInfo();
                temp.setGroupId(groupId);
                customerGroupDetailResult = queryQywxGroupDetail(accessToken, taskInstanceEntity.getEa(), groupId);
                if (null != customerGroupDetailResult && 0 == customerGroupDetailResult.getErrcode()) {
                    temp.setGroupName(customerGroupDetailResult.getQywxGroupChat().getGroupName());
                    temp.setUserId(customerGroupDetailResult.getQywxGroupChat().getGroupOwner());
                }
                temp.setComplete(finishExtenalUserId.contains(groupId));
                groupInfoList.add(temp);
            }
            sopTaskDetailResult.setGroupList(groupInfoList);
            sopTaskDetailResult.setTriggerTaskInstanceId(taskInstanceEntity.getId());
        }


        return Result.newSuccess(sopTaskDetailResult);
    }

    public CustomerGroupDetailResult queryQywxGroupDetail(String accessToken,String ea, String groupId){
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token=" + accessToken;
        CustomerGroupDetailArg arg = new CustomerGroupDetailArg();
        arg.setGroupId(groupId);
        CustomerGroupDetailResult result = httpManager.executePostHttp(arg, url, new TypeToken<CustomerGroupDetailResult>(){});
        if (result != null && result.getQywxGroupChat() != null && org.apache.commons.lang3.StringUtils.isNotBlank(result.getQywxGroupChat().getGroupOwner())) {
            // 设置群主名
            StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(ea, result.getQywxGroupChat().getGroupOwner(), accessToken, true);
            result.getQywxGroupChat().setOwnerName(staffDetailResult != null ? staffDetailResult.getName() : null);
        }
        return result;
    }

    private String getAvatarFormCrmData(ObjectData objectData, String ea) {
        // 头像
        List<Map<String, Object>> avatarMap = com.facishare.marketing.common.util.GsonUtil
                .fromJson(com.facishare.marketing.common.util.GsonUtil.getGson().toJson(objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName())), new TypeToken<List<Map<String, String>>>() {
                }.getType());
        if (CollectionUtils.isNotEmpty(avatarMap)) {
            return fileV2Manager.getUrlByPath(avatarMap.get(0).get("path") + "." + avatarMap.get(0).get("ext"), ea, false);
        }
        return null;
    }

    private String getAppName(String ea) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (null == agentConfig) {
            return null;
        }
        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), ea);
        QywxCustomerAppInfoEntity authAppInfo = null;
        if (!CollectionUtil.isEmpty(qywxCustomerAppInfoEntities)) {
            authAppInfo = qywxCustomerAppInfoEntities.get(0);
        } else {
            return null;
        }
        String agentAccessToken = qywxManager.getAgentAccessToken(authAppInfo.getCorpId(), authAppInfo.getSuitId(), authAppInfo.getAuthCode());
        String url = "https://qyapi.weixin.qq.com/cgi-bin/agent/get?access_token=" + agentAccessToken + "&agentid=" + qywxCustomerAppInfoEntities.get(0).getAgentId();
        if (StringUtils.isBlank(agentAccessToken)) {
            return null;
        }
        QueryQywxAppDetailResult res = httpManager.executeGetHttp(url, new TypeToken<QueryQywxAppDetailResult>() {
        });
        if (null != res && 0 == res.getErrcode()) {
            return res.getName();
        }
        return null;
    }
}