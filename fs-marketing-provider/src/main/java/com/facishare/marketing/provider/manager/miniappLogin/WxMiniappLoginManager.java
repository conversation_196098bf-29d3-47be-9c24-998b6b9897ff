package com.facishare.marketing.provider.manager.miniappLogin;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.util.TextUtil;
import com.facishare.marketing.api.arg.qywx.miniapp.*;
import com.facishare.marketing.api.result.qywx.miniapp.*;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.account.BindUserAndWxType;
import com.facishare.marketing.common.model.RedisKISApplyInfoEntity;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.AccountDAO;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.StaffMiniappUserBindDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerArg.qywx.GetUserDetailArg;
import com.facishare.marketing.provider.innerData.qywx.WxOpenIdAndUnionIdData;
import com.facishare.marketing.provider.innerResult.qywx.GetQywxUserInfoDetailResult;
import com.facishare.marketing.provider.innerResult.qywx.QywxUserInfoDetailResult;
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Component("wxMiniappLoginManager")
@Slf4j
public class WxMiniappLoginManager implements MiniappLoginManager{
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private AccountDAO accountDAO;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private UserManager userManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private AccountManager accountManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private CardManager cardManager;
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private QYWXContactService qywxContactService;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private StaffMiniappUserBindDAO staffMiniappUserBindDAO;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;

    public static final String MARKETING_WX_LOGIN_LOCK_KEY = "MARKETING_WX_LOGIN_LOCK_KEY";

    @ReloadableProperty("qywx.default.avatar")
    private String qywxDefaultAvatar;
    @ReloadableProperty("dingding.miniapp.appId")
    private String APP_ID;
    @ReloadableProperty("ks.ding.ea")
    private String ksDingEa;

    // 客脉pro明文企业ea
    @ReloadableProperty("qywx.kemaipro.plain.ea")
    private String qywxKemaiProPlainEa;

    // 客脉pro密文企业ea
    @ReloadableProperty("qywx.kemaipro.encrypt.ea")
    private String qywxKemaiProEncryptEa;


    public Result<PLoginResult> pLogin(PLoginArg arg) {
        String wxRes;
        WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(arg.getAppId());
        try {
            String url = null;
            if (wxAppInfoEnum != null) {
                url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + arg.getAppId() + "&secret=" + wxAppInfoEnum.getSecret() + "&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            }
            if(url == null){
                url = "https://api.weixin.qq.com/sns/component/jscode2session?appid=" + arg.getAppId() + "&component_appid="+wechatThirdPlatformManager.getComponentAppId(MKThirdPlatformConstants.PLATFORM_ID)+"&component_access_token="+wechatThirdPlatformManager.getThirdPlatformAccessToken("YXT")+"&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            }
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("WxLoginServiceImpl.pLogin jscode2session exception:", e);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.info("WxLoginServiceImpl.pLogin jscode2session response is null");
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        if (!object.containsKey("openid") || !object.containsKey("session_key")) {
            log.info("WxLoginServiceImpl.pLogin jscode2session response hasn't contains openid or session_key:{}", object);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        String openId = object.getString("openid");
        String sessionKey = object.getString("session_key");
        String wxUnionId = object.getString("unionid");
        String uid;
        String corpId = null;
        UserEntity userEntity = null;
        String ea = null;

        RedisKISApplyInfoEntity redisKISApplyInfoEntity = redisManager.getKISApplyInfoFromRedis(arg.getApplyInfoKey());
        if (redisKISApplyInfoEntity != null) {
            if (StringUtils.isNotBlank(ksDingEa) && Arrays.asList(ksDingEa.split(",")).contains(redisKISApplyInfoEntity.getFsEa())) {
                //处理旷世全员推广
                String bindUid = fsBindManager.queryByFsEaAndUserId(redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId());
                if (StringUtils.isNotBlank(bindUid)) {
                    userEntity = userManager.queryByUid(bindUid);
                }
            } else {
                corpId = redisKISApplyInfoEntity.getQywxCorpId();
                userEntity = userManager.queryUserByOpenIdAppIdCorpId(openId, arg.getAppId(), corpId);
            }
        } else {
            userEntity = userManager.queryByOpenidAndAppid(openId, arg.getAppId());
        }


        if (null == userEntity) {
            userEntity = new UserEntity();
            uid = UUIDUtil.getUUID();
            userEntity.setUid(uid);
            userEntity.setOpenid(openId);
            userEntity.setAppid(arg.getAppId());
            // 只有托管才有unionId
            if (wxAppInfoEnum == null) {
                userEntity.setWxUnionId(wxUnionId);
            }
            userManager.insert(userEntity);
        } else {
            uid = userEntity.getUid();
            if (wxAppInfoEnum == null && StringUtils.isNotBlank(wxUnionId) && !wxUnionId.equals(userEntity.getWxUnionId())) {
                userManager.updateUserUnionId(wxUnionId, uid);
            }
        }

        // 查询当前用户是否绑定了企业(托管小程序)
        String token = null;
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
        boolean corporateEmployees = false;
        if (fsBindEntity != null) {
            String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(fsBindEntity.getFsEa());
            if (StringUtils.isNotBlank(wxAppId) || fsBindEntity.getType().equals(AccountTypeEnum.DING_MINI_APP.getType()) || fsBindEntity.getType().equals(AccountTypeEnum.MEMBER_WECHAT_MINI_APP.getType()) || QywxUserConstants.isPartnerVirtualUserId(fsBindEntity.getFsUserId())) {
                Map<String, Object> map = new HashMap<>();
                buildStaffTokenMap(map, fsBindEntity, sessionKey, arg.getAppId(), openId);
                token = TokenUtil.generateCommonToken(map);
                corporateEmployees = true;
            }
        } else {
            // 校验当前游客是否有绑定员工小程序
            StaffMiniappUserBindEntity staffMiniappUserBindEntity = staffMiniappUserBindDAO.queryStaffMiniappUserBindByVisitorUid(uid);
            if (staffMiniappUserBindEntity != null) {
                fsBindEntity = fsBindManager.queryFSBindByUid(staffMiniappUserBindEntity.getStaffUid());
                if (fsBindEntity != null) {
                    // 将当前uid 转换为绑定企业员工的uid
                    uid = fsBindEntity.getUid();
                    Map<String, Object> map = new HashMap<>();
                    buildStaffTokenMap(map, fsBindEntity, sessionKey, arg.getAppId(), openId);
                    token = TokenUtil.generateCommonToken(map);
                    corporateEmployees = true;
                }
            }
        }
        if (StringUtils.isBlank(token)) {
            token = TokenUtil.generatePMiniappToken(openId, uid, sessionKey, arg.getAppId());
        }
        if (null == token) {
            log.error("WxLoginServiceImpl.pLogin token generate failed, openId={}, sessionKey={}, uid={}", openId, sessionKey, uid);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        PLoginResult result = new PLoginResult();
        UserRelationEntity userRelationEntity = userRelationManager.getByUid(uid);
        if (userRelationEntity != null) {
            if (QywxUserConstants.isMemberVirtualUserId(userRelationEntity.getFsUserId())) {
                // 会员员工这里返回false,前端要区分正常员工和会员员工
                corporateEmployees = false;
                result.setMemberId(userRelationEntity.getMemberId());
            } else if (QywxUserConstants.isPartnerVirtualUserId(userRelationEntity.getFsUserId())) {
                corporateEmployees = false;
            }
        }
        if (!WxAppInfoEnum.isSystemApp(arg.getAppId())){
            List<String> eas = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(MKThirdPlatformConstants.PLATFORM_ID,arg.getAppId());
            if (CollectionUtils.isNotEmpty(eas)) {
                ea = eas.get(0);
            }
        }
        //异步处理企微客户和unionId打通问题
        if (StringUtils.isNotBlank(ea)){
            String finalEa = ea;
            String finalUid = uid;
            ThreadPoolUtils.execute(() -> {
                qywxManager.dealExternalUserIdByUnionId(finalUid,openId,wxUnionId, finalEa);
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        redisManager.setSessionByUid(uid, sessionKey);

        result.setToken(token);
        result.setCorporateEmployees(corporateEmployees);
        UserEntity existUserEntity = userManager.queryByUid(uid);
        if (existUserEntity == null) {
            result.setNeedUpdateUserInfo(false);
        } else {
            Long daysBetween = DateUtil.getDaysBetween(existUserEntity.getLastModifyTime().getTime(), new Date().getTime());
            // 30天内不更新已有信息的游客
            if (StringUtils.isBlank(existUserEntity.getName()) || daysBetween >= 30) {
                result.setNeedUpdateUserInfo(false);
            } else {
                result.setNeedUpdateUserInfo(false);
            }
        }
        result.setUid(uid);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    /**
     * 多企业关联同一个小程序
     * @param arg
     * @return
     */
    public Result<ELoginResult> eLogin(ELoginArg arg) {
        ELoginResult result = new ELoginResult();
        result.setBindFsInfo(false);
        result.setSameCompanyWithSpread(false);
        result.setVisitorInfo(true);
        String appId = arg.getAppId();
        QywxMiniappConfigEntity qywxMiniappConfigEntity = null;
        if (StringUtils.isBlank(arg.getCorpId())) {
            if (WxAppInfoEnum.isMankeepPro(appId) && appVersionManager.isFxCloud()) {
                // 客脉pro，先用指定的明文企业的token去解析
                qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEaAndAppId(qywxKemaiProPlainEa, appId);
            } else {
                List<QywxMiniappConfigEntity> qywxMiniappConfigEntityList = qywxMiniappConfigDAO.getByAppid(arg.getAppId());
                if (CollectionUtils.isNotEmpty(qywxMiniappConfigEntityList)) {
                    qywxMiniappConfigEntity = qywxMiniappConfigEntityList.get(0);
                }
            }
        } else {
            List<QywxMiniappConfigEntity> miniappConfigEntities = qywxMiniappConfigDAO.getByCorpidAndAppId(arg.getCorpId(), arg.getAppId());
            if (CollectionUtils.isNotEmpty(miniappConfigEntities)) {
                qywxMiniappConfigEntity = miniappConfigEntities.get(0);
            } else {
                if (WxAppInfoEnum.isMankeepPro(appId) && appVersionManager.isFxCloud()) {
                    // 客脉pro，并且前端传的corpid在本地表没有找到，则要用密文企业的token去解析
                    qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(qywxKemaiProEncryptEa, appId);
                    log.info("use kemaipro encrypt ea:{}, entity:{}", qywxKemaiProEncryptEa, qywxMiniappConfigEntity);
                }
            }
        }

        if (null == qywxMiniappConfigEntity && StringUtils.isBlank(arg.getCorpId())) {
            return new Result<>(SHErrorCode.APPID_INVAILED);
        }

        String accessToken = null;
        if (qywxMiniappConfigEntity != null) {
            accessToken = qywxManager.getMiniAppAccessToken(qywxMiniappConfigEntity.getEa());
        } else {
            qywxMiniappConfigEntity = qywxMiniappConfigDAO.getAnyOne(arg.getAppId());
            if (qywxMiniappConfigEntity == null) {
                log.warn("WxLoginServiceImpl.eLogin appId invalid appId:{}", arg.getAppId());
                return new Result<>(SHErrorCode.APPID_INVAILED);
            }
            accessToken = qywxManager.getMiniAppAccessToken(qywxMiniappConfigEntity.getEa());
        }
        if (StringUtils.isBlank(accessToken)) {
            return new Result<>(SHErrorCode.QYWX_INNER_ACCESS_TOKEN_NOT_FOUND);
        }

        String wxRes;
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/miniprogram/jscode2session?access_token=" + accessToken + "&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("WxLoginServiceImpl.eLogin jscode2session exception:", e);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.error("WxLoginServiceImpl.eLogin jscode2session response is null");
            return new Result(SHErrorCode.LOGIN_FAILED);
        }
        log.info("WxLoginServiceImpl.eLogin object={}", object.toJSONString());
        if (!object.containsKey("corpid") || !object.containsKey("userid") || !object.containsKey("session_key")) {
            log.warn("WxLoginServiceImpl.eLogin jscode2session response hasn't contains openid or session_key:{}", object);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }


        //判断当前登录企业是否和随机选择解密企业一致, 数据为当前访问者的所属企业的数据，而不是当前小程序所属企业的数据
        String corpid = object.getString("corpid");
        String qyUserId = object.getString("userid");
        String sessionKey = object.getString("session_key");
        boolean bindWxInfoBefore = false;

        if (!StringUtils.equals(corpid, qywxMiniappConfigEntity.getCorpid()) && StringUtils.isBlank(arg.getCorpId())){
            log.info("WxLoginServiceImpl.eLogin current enterprise is not login enterprise, need relogin currentCorpId:{} loginCorpId:{}", qywxMiniappConfigEntity.getCorpid(), corpid);
            if (WxAppInfoEnum.isMankeepPro(appId)){
                result.setCorpId(corpid);
            }else {
                result.setCorpId(qywxMiniappConfigEntity.getCorpid());
            }
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN, result);
        }
        //选corpid的逻辑
        if (WxAppInfoEnum.isMankeepPro(appId) && !StringUtils.equals(qywxMiniappConfigEntity.getCorpid(), corpid)){
            arg.setCorpId(corpid);
            List<QywxMiniappConfigEntity> qywxMiniappConfigEntities = qywxMiniappConfigDAO.getByCorpidAndAppId(corpid, appId);
            if (CollectionUtils.isNotEmpty(qywxMiniappConfigEntities)) {
                qywxMiniappConfigEntity = qywxMiniappConfigEntities.get(0);
            }
        }else{
            arg.setCorpId(qywxMiniappConfigEntity.getCorpid());
        }
        UserEntity userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(arg.getCorpId(), qyUserId, arg.getAppId());
        String uid = null;
        if (null == userEntity) {
            userEntity = new UserEntity();
            uid = UUIDUtil.getUUID();
            userEntity.setUid(uid);
            userEntity.setCorpid(arg.getCorpId());
            userEntity.setQyUserId(qyUserId);
            userEntity.setAppid(arg.getAppId());
            log.info("WxLoginServiceImpl.eLogin userDAO.insert userEntity={}", userEntity);
            userManager.insert(userEntity);
        }else {
            uid = userEntity.getUid();
        }

        // 通过corpId+qyUserId换到fsEa+fsUserId
        String fsEa = null;
        Integer userId = null;
        Integer fsEi = null;
        boolean isVisitor = !StringUtils.equals(corpid, qywxMiniappConfigEntity.getCorpid());    //访问者的企业是否为登录的企业
        if (isVisitor) {
            result.setBindFsInfo(false);
            result.setVisitorInfo(true);
        }else {
            result.setVisitorInfo(false);
            result.setBindFsInfo(false);
            Optional<String> eaOptional = qywxManager.getFsEaByQyWxCorpId(arg.getCorpId(), arg.getAppId());
            if (eaOptional.isPresent()) {
                fsEa = eaOptional.get();
                userId = qywxUserManager.getUserIdByQyWxInfo(fsEa, arg.getCorpId(), qyUserId, QywxUserConstants.TRY_TIME);
                if (userId != null) {
                    result.setBindFsInfo(true);
                }
            }
        }

        log.info("WxLoginServiceImpl.eLogin fsEa={}, fsUserId={}", fsEa, userId);
        if (!isVisitor) {
            fsEi = eIEAConverter.enterpriseAccountToId(fsEa);
            FSBindEntity queryFSBindEntity = fsBindManager.queryFSBindByUid(uid);
            FSBindEntity fsBindEntity = new FSBindEntity();
            fsBindEntity.setUid(uid);
            fsBindEntity.setFsEa(fsEa);
            fsBindEntity.setFsUserId(userId);
            fsBindEntity.setFsCorpId(fsEi);
            fsBindEntity.setType(AccountTypeEnum.QYWX_MINI_APP.getType());
            fsBindEntity.setAppId(arg.getAppId());
            if (null == queryFSBindEntity) {
                fsBindManager.insert(fsBindEntity);
            } else {
                // 判断一下纷享userid是否已经绑定了其他user，如果有，基本就是之前开通的微信名片，此时需要将微信user和企微user合并，并将纷享身份绑定到企微user
                FSBindEntity sourceFsBindEntity = fsBindManager.queryByUserAndAppId(fsEa, userId, arg.getAppId());
                if (sourceFsBindEntity != null && !StringUtils.equals(sourceFsBindEntity.getUid(), uid) && QywxUserConstants.isFsUserId(userId)) {
                    String sourceUid = sourceFsBindEntity.getUid();
                    log.info("eLogin handle diff user start, fsEa:{}, fsUserId:{}, sourceUid:{}, targetUid:{}", fsEa, userId, sourceUid, uid);
                    UserEntity sourceUserEntity = userManager.queryByUid(sourceUid);
                    // 删除旧的user
                    deleteBindFsByUid(sourceUid);
                    if (sourceUserEntity != null) {
                        // 把openid更新到新的user
                        String openid = sourceUserEntity.getOpenid();
                        userManager.updateUserOpenId(openid, uid);
                    }
                    fsBindManager.updateUserIdById(uid, userId);
                } else {
                    // 如果已经是纷享userid,则不更新这个字段
                    if (QywxUserConstants.isFsUserId(queryFSBindEntity.getFsUserId())) {
                        fsBindEntity.setFsUserId(queryFSBindEntity.getFsUserId());
                    }
                    fsBindManager.update(fsBindEntity);
                }
            }
        }

        //String token = TokenUtil.generateEMiniappToken(corpid, qyUserId, uid, sessionKey, arg.getAppId());
        String token = TokenUtil.generateEMiniappToken(arg.getCorpId(), qyUserId, uid, sessionKey, arg.getAppId());
        if (null == token) {
            log.error("WxLoginServiceImpl.eLogin token generate failed, corpid={}, qyUserId={}, sessionKey={}, uid={}, appId={}", arg.getCorpId(), qyUserId, sessionKey, uid, arg.getAppId());
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        redisManager.setSessionByUid(uid, sessionKey);

        // 判断是当前用户与推广用户是否为同一企业
        if (StringUtils.isNotBlank(arg.getTargetUid())) {
            UserEntity targetUserEntity = userManager.queryByUid(arg.getTargetUid());
            if (targetUserEntity != null && StringUtils.isNotBlank(targetUserEntity.getCorpid())) {
                result.setSameCompanyWithSpread(targetUserEntity.getCorpid().equals(arg.getCorpId()));
                result.setVisitorInfo(!targetUserEntity.getCorpid().equals(arg.getCorpId()));
            }
        }
        result.setToken(token);
        if (bindWxInfoBefore) {
            result.setNeedUpdateUserInfo(true);
        } else {
            Boolean needUpdateUserInfo = accountManager.needUpdateUserInfo(uid);
            result.setNeedUpdateUserInfo(needUpdateUserInfo);
        }
        result.setUid(uid);

        //绑定企业微信用户和微信用户的身份,删除相同openid的多个user
        if (StringUtils.isNotEmpty(arg.getWxCode()) && !isVisitor) {
            reloginAsbindQywxUserAndWxUser(uid, arg.getAppId(), arg.getWxCode());
        }

        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    public WxOpenIdAndUnionIdData getWxOpenIdAndUnionIdByCode(String wxAppId, String code) {
        WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(wxAppId);
        String wxRes = null;
        try {
            String url = null;
            if (wxAppInfoEnum != null) {
                url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + wxAppId + "&secret=" + wxAppInfoEnum.getSecret() + "&js_code=" + code+ "&grant_type=authorization_code";
            }
            if(url == null){
                url = "https://api.weixin.qq.com/sns/component/jscode2session?appid=" + wxAppId + "&component_appid="+wechatThirdPlatformManager.getComponentAppId(MKThirdPlatformConstants.PLATFORM_ID)+"&component_access_token="+wechatThirdPlatformManager.getThirdPlatformAccessToken("YXT")+"&js_code=" + code + "&grant_type=authorization_code";
            }
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("WxLoginServiceImpl.getWxOpenIdAndUnionIdByCode jscode2session exception:", e);
            return null;
        }

        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.error("WxLoginServiceImpl.getWxOpenIdAndUnionIdByCode response is null wxAppId={}, code={}", wxAppId, code);
            return null;
        }
        log.info("WxLoginServiceImpl.getWxOpenIdAndUnionIdByCode object={}", object.toJSONString());
        if (!object.containsKey("openid")) {
            log.warn("WxLoginServiceImpl.getWxOpenIdAndUnionIdByCode response hasn't contains openid:{}", object);
            return null;
        }

        //判断当前登录企业是否和随机选择解密企业一致
        String openid = object.getString("openid");
        String unionId = object.getString("unionId");
        return new WxOpenIdAndUnionIdData(openid, unionId);
    }

    //绑定企业微信员工信息&微信身份信息
    @Transactional
    public void reloginAsbindQywxUserAndWxUser(String qywxEmployeeUid, String wxAppId, String code){
        WxOpenIdAndUnionIdData wxOpenIdAndUnionIdData = this.getWxOpenIdAndUnionIdByCode(wxAppId, code);
        if (wxOpenIdAndUnionIdData == null || StringUtils.isEmpty(wxOpenIdAndUnionIdData.getOpenId())){
            return;
        }

        UserEntity qywxUserEntity = userManager.queryByUid(qywxEmployeeUid);
        if (qywxUserEntity == null || StringUtils.equals(qywxUserEntity.getOpenid(), wxOpenIdAndUnionIdData.getOpenId())){
            return;
        }

        UserEntity wxUserEntity = userManager.queryByOpenidAndAppid(wxOpenIdAndUnionIdData.getOpenId(), wxAppId);
        if (wxUserEntity == null){
            userManager.updateUserOpenId(wxOpenIdAndUnionIdData.getOpenId(), qywxEmployeeUid);
            return;
        }

        //需要合并微信用户和企业微信用户的user信息
        mergeQywxEmployeeUserAndWxUserEntity(qywxUserEntity, wxUserEntity, wxOpenIdAndUnionIdData.getOpenId());
    }

    public void mergeQywxEmployeeUserAndWxUserEntity(UserEntity qywxUserEntity, UserEntity wxUserEntity, String openid){
        //处理user表，以企业微信用户为主
        userManager.deleteOldUserByUid(wxUserEntity.getUid());
        userManager.updateUserOpenId(openid, qywxUserEntity.getUid());
        if (StringUtils.isNotEmpty(wxUserEntity.getWxUnionId())){
            userManager.updateUserUnionId(wxUserEntity.getWxUnionId(), qywxUserEntity.getUid());
        }
        fsBindManager.mergerUid(wxUserEntity.getUid(), qywxUserEntity.getUid());
        //更新card表
        CardEntity wsUserCardEntity = cardDAO.queryCardInfoByUid(wxUserEntity.getUid());
        CardEntity qywxUserCardEntity = cardDAO.queryCardInfoByUid(qywxUserEntity.getUid());
        if (wsUserCardEntity != null){
            if (qywxUserCardEntity == null){
                cardDAO.updateUidByOldUid(wsUserCardEntity.getUid(), qywxUserEntity.getUid());
            }else {
                //合并企业微信员工名片和微信用户名片，以个人微信的名片为主
                qywxUserCardEntity.setName(wsUserCardEntity.getName() == null ? qywxUserCardEntity.getName() : wsUserCardEntity.getName());
                qywxUserCardEntity.setAvatar(wsUserCardEntity.getAvatar() == null ? qywxUserCardEntity.getAvatar() : wsUserCardEntity.getAvatar());
                qywxUserCardEntity.setWechat(wsUserCardEntity.getWechat() == null ? qywxUserCardEntity.getWechat() : wsUserCardEntity.getWechat());
                qywxUserCardEntity.setIntroduction(wsUserCardEntity.getIntroduction() == null ? qywxUserCardEntity.getIntroduction() : wsUserCardEntity.getIntroduction());
                qywxUserCardEntity.setEmail(wsUserCardEntity.getEmail() == null ? qywxUserCardEntity.getEmail() : wsUserCardEntity.getEmail());
                qywxUserCardEntity.setGender(wsUserCardEntity.getGender() == null ? qywxUserCardEntity.getGender() : wsUserCardEntity.getGender());
                qywxUserCardEntity.setPhone(wsUserCardEntity.getPhone() == null ? qywxUserCardEntity.getPhone() : wsUserCardEntity.getPhone());
                qywxUserCardEntity.setDepartment(wsUserCardEntity.getDepartment() == null ? qywxUserCardEntity.getDepartment() : wsUserCardEntity.getDepartment());
                qywxUserCardEntity.setCompanyName(wsUserCardEntity.getCompanyName() == null ? qywxUserCardEntity.getCompanyName() : wsUserCardEntity.getCompanyName());
                qywxUserCardEntity.setCompanyAddress(wsUserCardEntity.getCompanyAddress() == null ? qywxUserCardEntity.getCompanyAddress() : wsUserCardEntity.getCompanyAddress());
                qywxUserCardEntity.setVocation(wsUserCardEntity.getVocation() == null ? qywxUserCardEntity.getVocation() : wsUserCardEntity.getVocation());
                qywxUserCardEntity.setTradeCode(wsUserCardEntity.getTradeCode() == null ? qywxUserCardEntity.getTradeCode() : wsUserCardEntity.getTradeCode());
                qywxUserCardEntity.setCardTemplateId(wsUserCardEntity.getCardTemplateId() == null ? qywxUserCardEntity.getCardTemplateId() : wsUserCardEntity.getCardTemplateId());
                qywxUserCardEntity.setVisualRangeString(wsUserCardEntity.getVisualRangeString() == null ? qywxUserCardEntity.getVisualRangeString() : wsUserCardEntity.getVisualRangeString());
                qywxUserCardEntity.setQrUrl(wsUserCardEntity.getQrUrl() == null ? qywxUserCardEntity.getQrUrl() : wsUserCardEntity.getQrUrl());
                qywxUserCardEntity.setAvatarPath(wsUserCardEntity.getAvatarPath() == null ? qywxUserCardEntity.getAvatarPath() : wsUserCardEntity.getAvatarPath());
                qywxUserCardEntity.setAvatarThumbnailPath(wsUserCardEntity.getAvatarThumbnailPath() == null ? qywxUserCardEntity.getAvatarThumbnailPath() : wsUserCardEntity.getAvatarThumbnailPath());
                qywxUserCardEntity.setAvatarThumbnail(wsUserCardEntity.getAvatarThumbnail() == null ? qywxUserCardEntity.getAvatarThumbnail() : wsUserCardEntity.getAvatarThumbnail());
                cardDAO.deleteByUid(wsUserCardEntity.getUid());
                cardDAO.updateById(qywxUserCardEntity);
            }
        }
        //更新account表
        AccountEntity wxAccountEntity = accountDAO.queryAccountByUid(wxUserEntity.getUid());
        AccountEntity qywxEmployeeAccount = accountDAO.queryAccountByUid(qywxUserEntity.getUid());
        if (wxAccountEntity != null){
            if (qywxEmployeeAccount == null){
                accountDAO.updateUidByOldUid(wxAccountEntity.getUid(), qywxUserEntity.getUid());
            }else {
                //合并企业微信员工账号和微信用户账号
                qywxEmployeeAccount.setPhone(qywxEmployeeAccount.getPhone() == null ? wxAccountEntity.getPhone() : qywxEmployeeAccount.getPhone());
                qywxEmployeeAccount.setOpenid(openid);
                accountDAO.deleteByUid(wxUserEntity.getUid());
                accountDAO.update(qywxEmployeeAccount);
            }
        }
    }

    @Transactional
   public void mergeFsBindEntity(FSBindEntity targetBindEntity, FSBindEntity bindEntity){
        if (targetBindEntity == null || bindEntity == null){
            return;
        }
        String targetUid = targetBindEntity.getUid();
        String uid = bindEntity.getUid();
        UserEntity targetUserEntity = userManager.queryByUid(targetUid);
        UserEntity userEntity = userManager.queryByUid(uid);
        if (targetUserEntity == null || userEntity == null){
            return;
        }

        AccountEntity targetAccountEntity = accountDAO.queryAccountByUid(targetUid);
        if (targetAccountEntity == null){
            return;
        }
        CardEntity targetCardEntity = cardDAO.queryCardInfoByUid(targetUid);
        if (targetCardEntity == null){
            return;
        }

       if (targetUserEntity.getOpenid() == null && userEntity.getOpenid() != null){
           targetUserEntity.setOpenid(userEntity.getOpenid());
       }
       if (targetUserEntity.getWxUnionId() == null && userEntity.getWxUnionId() != null){
           targetUserEntity.setWxUnionId(userEntity.getWxUnionId());
       }
       if (targetUserEntity.getQyUserId() == null && userEntity.getQyUserId() != null){
           targetUserEntity.setQyUserId(userEntity.getQyUserId());
       }
       if (targetUserEntity.getCorpid() == null && userEntity.getCorpid() != null){
           targetUserEntity.setCorpid(userEntity.getCorpid());
       }

       deleteBindFsByUid(uid);
       userManager.updateUser(targetUserEntity);
   }

   public void deleteBindFsByUid(String uid){
        if (StringUtils.isEmpty(uid)){
            return;
        }
       fsBindManager.delFSBindByUid(uid);
       userManager.deleteOldUserByUid(uid);
       cardDAO.deleteByUid(uid);
       accountDAO.deleteByUid(uid);
   }

    public Result updatePUserInfo(UpdatePUserInfoArg arg) {
        UserEntity userEntity = userManager.queryByUid(arg.getUid());
        if (null == userEntity) {
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        JSONObject jsonObject;
        try {
            jsonObject = WXInfoDecryptUtil.decryptUserInfo(arg.getUserEncryptedData(), arg.getIv(), arg.getSessionKey(), arg.getUid());
        } catch (Exception e) {
            log.warn("WxLoginServiceImpl.updatePUserInfo exception:", e);
            return new Result(SHErrorCode.DECRYPT_FAILED);
        }

        String avatarUrl = jsonObject.getString("avatarUrl");
        if (org.apache.commons.lang.StringUtils.isNotBlank(avatarUrl)) {
            String sizeFlagStr = avatarUrl.substring(avatarUrl.lastIndexOf("/"));
            if (org.apache.commons.lang.StringUtils.isNotBlank(sizeFlagStr) && !sizeFlagStr.equals("/0")) {
                StringBuilder sb = new StringBuilder(avatarUrl);
                sb.replace(avatarUrl.lastIndexOf("/"), avatarUrl.length(), "/0");
                avatarUrl = sb.toString();
                log.info("avatarUrl={}", avatarUrl);
            }
        }

        userEntity = new UserEntity();
        userEntity.setUid(arg.getUid());
        userEntity.setAvatar(avatarUrl);
        userEntity.setGender(jsonObject.getInteger("gender"));
        userEntity.setName(com.facishare.marketing.common.util.TextUtil.pureSubStr(jsonObject.getString("nickName"), 255));
        userEntity.setCity(TextUtil.subStr(jsonObject.getString("city"), 31));
        userEntity.setProvince(TextUtil.subStr(jsonObject.getString("province"), 31));
        userEntity.setCountry(TextUtil.subStr(jsonObject.getString("country"), 31));
        int result = userManager.updateUser(userEntity);
        if (result != 1) {
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        return new Result(SHErrorCode.SUCCESS);
    }

    public Result<UpdateEUserInfoResult> updateEUserInfo(UpdateEUserInfoArg arg) {
        UserEntity userEntity = userManager.queryByUid(arg.getUid());
        if (null == userEntity) {
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        String name = null;
        Integer gender = null;
        String mobile = null;
        String avatar = arg.getAvatar();
        String qrCode = arg.getQrCode();
        String email = null;

        try {

            if (StringUtils.isNotBlank(arg.getBaseInfoEncryptedData()) && StringUtils.isNotBlank(arg.getBaseInfoIv())) {
                JSONObject baseInfoJsonObject = WXInfoDecryptUtil.decryptUserInfo(arg.getBaseInfoEncryptedData(), arg.getBaseInfoIv(), arg.getSessionKey(), arg.getUid());
                log.info("WxLoginServiceImpl.updateEUserInfo baseInfoJsonObject={}", baseInfoJsonObject);
                name = baseInfoJsonObject.getString("name");
                gender = baseInfoJsonObject.getInteger("gender");
            }

            if (StringUtils.isNotBlank(arg.getMobileEncryptedData()) && StringUtils.isNotBlank(arg.getMobileIv())) {
                JSONObject mobileJsonObject = WXInfoDecryptUtil.decryptUserInfo(arg.getMobileEncryptedData(), arg.getMobileIv(), arg.getSessionKey(), arg.getUid());
                log.info("WxLoginServiceImpl.updateEUserInfo mobileJsonObject={}", mobileJsonObject);
                mobile = mobileJsonObject.getString("mobile");
            }

            if (StringUtils.isNotBlank(arg.getEmailEncryptedData()) && StringUtils.isNotBlank(arg.getEmailIv())) {
                JSONObject emailJsonObject = WXInfoDecryptUtil.decryptUserInfo(arg.getEmailEncryptedData(), arg.getEmailIv(), arg.getSessionKey(), arg.getUid());
                log.info("WxLoginServiceImpl.updateEUserInfo emailJsonObject={}", emailJsonObject);
                email = emailJsonObject.getString("email");
            }

        } catch (Exception e) {
            log.warn("WxLoginServiceImpl.updateEUserInfo exception:", e);
            return new Result(SHErrorCode.DECRYPT_FAILED);
        }

        UpdateEUserInfoResult result = new UpdateEUserInfoResult();
        if (arg.getVisitorInfo()) {
            userEntity = new UserEntity();
            userEntity.setName(name);
            userEntity.setUid(arg.getUid());
            userManager.updateUser(userEntity);
            result.setName(name);
            return new Result<>(SHErrorCode.SUCCESS, result);
        }

        // 手机号从通讯录获取
        if (StringUtils.isNotBlank(arg.getFsEa()) || StringUtils.isNotBlank(arg.getCorpId()) || StringUtils.isNotBlank(arg.getQyUserId())) {
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(arg.getFsEa());
            if (agentConfig != null) {
                String accessToken = qywxManager.getAccessToken(arg.getFsEa());
                if (accessToken != null) {
                    StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(arg.getFsEa(), arg.getQyUserId(), accessToken, false);
                    mobile = staffDetailResult != null ? staffDetailResult.getMobile() : null;
                }
            }
        }

        EmployeeDto employeeDto = new EmployeeDto();
        if (arg.getFsUserId() != null && !QywxUserConstants.isVirtualUserId(arg.getFsUserId())) {
            Integer ei = eieaConverter.enterpriseAccountToId(arg.getFsEa());
            GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
            employeeDtoArg.setEmployeeId(arg.getFsUserId());
            employeeDtoArg.setEnterpriseId(ei);
            GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
            if (null == employeeDtoResult) {
                return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
            }
            employeeDto = employeeDtoResult.getEmployee();
            if (null == employeeDto) {
                return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
            }
        }

        if (StringUtils.isNotBlank(employeeDto.getProfileImage())) {
            byte[] bytes = fileV2Manager.downloadFileByUrl(employeeDto.getProfileImage(), arg.getFsEa());
            String apath = fileV2Manager.uploadToApath(bytes, "jpg", null);
            String newAvatar = fileV2Manager.getUrlByPath(apath, null, false);
            avatar = StringUtils.isNotBlank(newAvatar) ? newAvatar : avatar;
        }
        if (StringUtils.isBlank(avatar)) {
            String cardAvatar = null;
            CardEntity cardEntity = cardDAO.queryCardInfoByUid(arg.getUid());
            if (cardEntity != null) {
                cardAvatar = cardEntity.getAvatar();
            }
            // 若头像为空则取默认头像
            String defaultAvatar = fileV2Manager.getUrlByPath(qywxDefaultAvatar, null, false);
            avatar = StringUtils.isNotBlank(cardAvatar) ? cardAvatar : defaultAvatar;
        }

        //配置联系我
        ThreadPoolUtils.execute(() -> {
            Result<Void> contactConfigResult = qywxContactService.setContactConfigByEmployee(arg.getUid(), arg.getFsEa(), arg.getFsUserId());
            if (!contactConfigResult.isSuccess()) {
                log.warn("set qywx contact me config faield arg:{}  result:{}", arg, contactConfigResult);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);
        boolean createAccountResult = accountManager.createAccount(arg.getUid(), name, gender, avatar, mobile, arg.getFsEa(), arg.getAppId(), AccountTypeEnum.QYWX_MINI_APP.getType());
        if (!createAccountResult) {
            return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        result.setName(name);
        result.setGender(gender);
        result.setMobile(mobile);
        result.setAvatar(avatar);
        result.setQrCode(qrCode);
        result.setEmail(email);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<WxLoginResult> wxLogin(WxLoginArg arg) {
        WxLoginResult result = new WxLoginResult();
        String wxRes;
        WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(arg.getAppId());
        try {
            String url = null;
            if (wxAppInfoEnum != null) {
                url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + arg.getAppId() + "&secret=" + wxAppInfoEnum.getSecret() + "&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            }
            if(url == null){
                url = "https://api.weixin.qq.com/sns/component/jscode2session?appid=" + arg.getAppId() + "&component_appid="+wechatThirdPlatformManager.getComponentAppId(MKThirdPlatformConstants.PLATFORM_ID)+"&component_access_token="+wechatThirdPlatformManager.getThirdPlatformAccessToken("YXT")+"&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            }
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("WxLoginServiceImpl.wxLogin jscode2session exception:", e);
            return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.error("WxLoginServiceImpl.wxLogin jscode2session response is null");
            return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        if (!object.containsKey("openid") || !object.containsKey("session_key")) {
            log.error("WxLoginServiceImpl.wxLogin jscode2session response hasn't contains openid or session_key:{}", object);
            return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        log.info("WxLoginServiceImpl.wxLogin info: {}", object);
        String openId = object.getString("openid");
        String sessionKey = object.getString("session_key");
        String wxUnionId = object.getString("unionid");
        String uid;

        if (StringUtils.isBlank(openId) || StringUtils.isBlank(sessionKey)) {
            log.warn("WxLoginServiceImpl.wxLogin openId or sessionKey is null");
            return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        RedisKISApplyInfoEntity redisKISApplyInfoEntity = redisManager.getKISApplyInfoFromRedis(arg.getApplyInfoKey());
        if (redisKISApplyInfoEntity == null) {
            //特殊处理旷世钉钉链接的applyInfoKey 过期问题
            UserEntity userEntity = userManager.queryByOpenidAndAppid(openId, arg.getAppId());
            if (userEntity != null) {
                FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(userEntity.getUid());
                if (fsBindEntity != null) {
                    result.setUid(userEntity.getUid());
                    return Result.newSuccess(result);
                }
            }
            log.warn("WxLoginServiceImpl.wxLogin redisKISApplyInfoEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        StringJoiner sj = new StringJoiner("_");
        sj.add(MARKETING_WX_LOGIN_LOCK_KEY);
        sj.add(arg.getAppId());
        sj.add(arg.getApplyInfoKey());
        try {
            boolean redisLock = redisManager.lock(sj.toString(), 30);
            if (!redisLock) {
                log.warn("WxLoginServiceImpl.wxLogin error redisLock arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            if (redisKISApplyInfoEntity.isPartner()) {
                Result<WxLoginResult> partnerLoginResult = doPartnerWxLogin(arg.getAppId(), openId, wxUnionId, redisKISApplyInfoEntity);
                log.info("WxLoginServiceImpl.wxLogin arg: {} redisKISApplyInfoEntity: {} partnerLoginResult:{}", arg, redisKISApplyInfoEntity, partnerLoginResult);
                return partnerLoginResult;
            }
            // 兼容专属小程序不能加miniappType
            if (redisKISApplyInfoEntity.getType() != null && redisKISApplyInfoEntity.getType() == BindUserAndWxType.DINGDING_APP.getValue()) {
                return doDingdingWxLogin(arg.getAppId(), openId, wxUnionId, redisKISApplyInfoEntity);
            }

            // 校验企业是否绑定企业微信,从纷享app跳转的场景
            if (redisKISApplyInfoEntity.getType() == null || (redisKISApplyInfoEntity.getType() != null && redisKISApplyInfoEntity.getType() == BindUserAndWxType.FXIAOKE_APP.getValue())) {
                QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = agentConfigDAO.queryAgentByEa(redisKISApplyInfoEntity.getFsEa());
                boolean mustUseFxiaokeAddressBook = fsAddressBookManager.mustUseFxiaokeAddressBook(redisKISApplyInfoEntity.getFsEa());
                if (qywxCorpAgentConfigEntity != null && !mustUseFxiaokeAddressBook) {
                    log.warn("WxLoginServiceImpl.wxLogin bind ea:{}", redisKISApplyInfoEntity.getFsEa());
                    return new Result<>(SHErrorCode.USER_BIND_ACCOUNT_NEED_IN_QYWX);
                }
            }

            UserEntity userEntity = userManager.queryByOpenidAndAppid(openId, arg.getAppId());
            if (null == userEntity) {
                // 如果先开启了企业微信后开通app，且前端错误调用了此接口则直接回写openId且返回旧uid
                String oldUid = qywxUserManager.getUidByFsUserInfo(redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId());
                if (StringUtils.isNotBlank(oldUid)) {
                    UserEntity oldUserInfo = userManager.queryByUid(oldUid);
                    if (oldUserInfo != null && StringUtils.isNotBlank(oldUserInfo.getQyUserId()) && StringUtils.isNotBlank(oldUserInfo.getCorpid()) && StringUtils.isBlank(oldUserInfo.getOpenid())) {
                        userManager.updateUserOpenId(openId, oldUserInfo.getUid());
                        if (StringUtils.isNotBlank(wxUnionId) && !wxUnionId.equals(oldUserInfo.getWxUnionId())) {
                            userManager.updateUserUnionId(wxUnionId, oldUserInfo.getUid());
                        }
                        result.setUid(oldUserInfo.getUid());
                        return Result.newSuccess(result);
                    }
                }

                userEntity = new UserEntity();
                uid = UUIDUtil.getUUID();
                userEntity.setUid(uid);
                userEntity.setOpenid(openId);
                userEntity.setAppid(arg.getAppId());
                userEntity.setCorpid(redisKISApplyInfoEntity.getQywxCorpId());
                userEntity.setQyUserId(redisKISApplyInfoEntity.getQywxUserId());
                if (wxAppInfoEnum == null && StringUtils.isNotBlank(wxUnionId)) {
                    userEntity.setWxUnionId(wxUnionId);
                }
                userManager.insert(userEntity);
            } else {
                // 如果之前绑定过不同企业微信的相同小程序（客脉pro）切换user企业微信身份
                if (StringUtils.isNotBlank(redisKISApplyInfoEntity.getQywxCorpId()) && StringUtils.isNotBlank(redisKISApplyInfoEntity.getQywxUserId())) {
                    UserEntity qyUserEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(redisKISApplyInfoEntity.getQywxCorpId(), redisKISApplyInfoEntity.getQywxUserId(), arg.getAppId());
                    // 防止在企业微信侧没有绑定微信身份并且在微信侧使用了相同小程序的情况
                    if (qyUserEntity != null && StringUtils.isEmpty(qyUserEntity.getOpenid()) && StringUtils.isEmpty(userEntity.getQyUserId())) {
                        //将微信侧名片绑定到企业微信侧
                        userManager.deleteOldUserByUid(qyUserEntity.getUid());
                        fsBindManager.delFSBindByUid(qyUserEntity.getUid());
                        redisManager.delSessionByUid(userEntity.getUid());
                        userManager.updateUserWxInfo(qyUserEntity.getUid(), userEntity.getOpenid(), userEntity.getWxUnionId());
                    }
                    if (qyUserEntity == null) {
                        userManager.addQywxUserInfo(arg.getAppId(), redisKISApplyInfoEntity.getQywxCorpId(), redisKISApplyInfoEntity.getQywxUserId(), userEntity.getUid());
                    }

                }
                uid = userEntity.getUid();
                if (wxAppInfoEnum == null && StringUtils.isNotBlank(wxUnionId) && !wxUnionId.equals(userEntity.getWxUnionId())) {
                    userManager.updateUserUnionId(wxUnionId, uid);
                }
                if (StringUtils.isEmpty(userEntity.getOpenid())){
                    userManager.updateUserOpenId(openId, uid);
                }
            }

            // 查询当前appid与企业appId是否相同
            String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(redisKISApplyInfoEntity.getFsEa());
            if (!arg.getAppId().equals(appId)) {
                log.warn("WxLoginServiceImpl.wxLogin bind appId:{}, front appId:{},", appId, arg.getAppId());
                return new Result<>(SHErrorCode.APP_ID_DIFFERENT_TO_BIND_APP);
            }

            result.setUid(uid);
            String avatar = null;
            EmployeeDto employeeDto = new EmployeeDto();
            if (redisKISApplyInfoEntity.getFsUserId() != null && !QywxUserConstants.isVirtualUserId(redisKISApplyInfoEntity.getFsUserId())) {
                GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
                Integer ei = eieaConverter.enterpriseAccountToId(redisKISApplyInfoEntity.getFsEa());
                employeeDtoArg.setEmployeeId(redisKISApplyInfoEntity.getFsUserId());
                employeeDtoArg.setEnterpriseId(ei);
                GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
                if (null == employeeDtoResult) {
                    return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
                }
                employeeDto = employeeDtoResult.getEmployee();
                if (null == employeeDto) {
                    return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
                }
            }

            if (StringUtils.isNotBlank(employeeDto.getProfileImage())) {
                byte[] bytes = fileV2Manager.downloadFileByUrl(employeeDto.getProfileImage(), redisKISApplyInfoEntity.getFsEa());
                String apath = fileV2Manager.uploadToApath(bytes, "jpg", null);
                String newAvatar = fileV2Manager.getUrlByPath(apath, null, false);
                avatar = StringUtils.isNotBlank(newAvatar) ? newAvatar : null;
            }
            if (StringUtils.isBlank(avatar)) {
                // 若头像为空则取默认头像
                String defaultAvatar = fileV2Manager.getUrlByPath(qywxDefaultAvatar, null, false);
                avatar = StringUtils.isNotBlank(defaultAvatar) ? defaultAvatar : avatar;
            }

            String userName = employeeDto.getName();
            //鑫方盛，名片使用员工的fullname字段。
            if (StringUtils.equals(redisKISApplyInfoEntity.getFsEa(), "xfsjt2022")
                    && StringUtils.isNotEmpty(employeeDto.getFullName())){
                userName = employeeDto.getFullName();
            }
            boolean createAccountResult = accountManager.createWxAccount(uid, userName, avatar, redisKISApplyInfoEntity.getPhone(), redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId(), arg.getAppId());
            if (!createAccountResult) {
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.warn("WxLoginServiceImpl.wxLogin error e:", e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        } finally {
            redisManager.unLock(sj.toString());
        }
    }

    private Result<WxLoginResult> doPartnerWxLogin(String appId, String openId, String wxUnionId, RedisKISApplyInfoEntity redisKISApplyInfoEntity) {
        UserEntity userEntity = userManager.queryByOpenidAndAppid(openId, appId);
        if (userEntity == null) {
            userEntity = new UserEntity();
            String uid = UUIDUtil.getUUID();
            userEntity.setUid(uid);
            userEntity.setOpenid(openId);
            userEntity.setAppid(appId);
            WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(appId);
            if (wxAppInfoEnum == null && StringUtils.isNotBlank(wxUnionId)) {
                userEntity.setWxUnionId(wxUnionId);
            }
            userManager.insert(userEntity);
        }
        String ea = redisKISApplyInfoEntity.getFsEa();
        int fsUserId = redisKISApplyInfoEntity.getFsUserId();
        String uid = userEntity.getUid();
        long outerTenantId = redisKISApplyInfoEntity.getOuterTenantId();
        long outerUid = redisKISApplyInfoEntity.getOuterUid();
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByOutTenantIdAndOuterUid(ea, Lists.newArrayList(outerTenantId), Lists.newArrayList(outerUid));
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            return new Result<>(SHErrorCode.PARTNER_NOT_EXIST_USER_RELATION);
        }
        UserRelationEntity userRelationEntity = userRelationEntityList.get(0);
        if (userRelationEntity.getFsUserId() != fsUserId) {
            return new Result<>(SHErrorCode.PARTNER_USER_RELATION_NOT_EQUAL);
        }
        WxLoginResult result = new WxLoginResult();
        if (StringUtils.isNotBlank(userRelationEntity.getUid())) {
            if (!userRelationEntity.getUid().equals(uid)) {
                log.info("doPartnerWxLogin partner bounded another user, ea: {} uid:{} boundedUid:{} outerTenantId: {} outerUid: {}", ea, uid, userRelationEntity.getUid(), outerTenantId, outerUid);
                return new Result<>(SHErrorCode.PARTNER_BOUNDED_ANOTHER_USER);
            }
            result.setUid(uid);
            return Result.newSuccess(result);
        }
        String userName = userRelationEntity.getUserName();
        boolean createAccountResult = accountManager.createWxAccount(uid, userName, null, redisKISApplyInfoEntity.getPhone(), ea, fsUserId, appId);
        if (!createAccountResult) {
            return new Result<>(SHErrorCode.CREATE_USER_FAIL);
        }
        result.setUid(uid);
        return Result.newSuccess(result);
    }

    public Result<QywxEmployeeH5BindWxUserResult> doQywxWxLogin(String appId, String openId, String unionId, RedisKISApplyInfoEntity redisKISApplyInfoEntity) {
        return null;
    }

    public Result<WxLoginResult> doDingdingWxLogin(String appId, String openId, String unionId, RedisKISApplyInfoEntity redisKISApplyInfoEntity) {
        WxLoginResult result = new WxLoginResult();
        String uid = null;
        String oldUid = getUidByFsUserInfo(redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId());
        if (StringUtils.isEmpty(oldUid)) {
            log.warn("DingMiniappLoginManager.doDingdingWxLogin getUidByFsUserInfo fail, ea:{}, fsUserId:{}", redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId());
            return Result.newError(SHErrorCode.NOT_LOGIN_DINGDING_MINIAPP);
        }

        UserEntity dingUserInfo = userManager.queryByUid(oldUid);
        if (dingUserInfo == null || StringUtils.isBlank(dingUserInfo.getDingUserId()) || StringUtils.isBlank(dingUserInfo.getCorpid())) {
            log.warn("DingMiniappLoginManager.doDingdingWxLogin queryByUid fail, uid:{}", oldUid);
            return Result.newError(SHErrorCode.NOT_LOGIN_DINGDING_MINIAPP);
        }
        uid = dingUserInfo.getUid();
        result.setUid(uid);

        UserEntity userEntity = userManager.queryByOpenidAndAppid(openId, appId);
        if (userEntity == null) {
            log.warn("DingMiniappLoginManager.doDingdingWxLogin queryByOpenidAndAppid is null, openId:{}, appid:{}", openId, appId);
            return Result.newError(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        // 新登陆的微信小程序用户和钉钉绑定时需要先清掉
        if (StringUtils.isEmpty(userEntity.getDingUserId()) && StringUtils.isEmpty(userEntity.getCorpid())) {
            userManager.deleteOldUserByUid(userEntity.getUid());
            userManager.updateUserOpenIdAndAppId(userEntity.getAppid(), userEntity.getOpenid(), unionId, dingUserInfo.getUid());
        } else {
            // 如果之前绑定过不同企业的相同小程序（客脉pro）切换user企业身份
            if (!StringUtils.equals(userEntity.getCorpid(), redisKISApplyInfoEntity.getQywxCorpId())) {
                UserEntity dingUserEntity = userManager.queryByCorpIdAndDingUserIdAndAppid(redisKISApplyInfoEntity.getQywxCorpId(), redisKISApplyInfoEntity.getQywxUserId());
                if (dingUserEntity.getOpenid() == null && dingUserEntity.getAppid() == null) {
                    userManager.updateUserOpenIdAndAppId(userEntity.getAppid(), userEntity.getOpenid(), unionId, dingUserInfo.getUid());
                    userManager.updateUserOpenIdAndAppId(null, null, unionId, userEntity.getUid());
                    // 让切换企业之前员工所在企业的token失效
                    redisManager.delDingSession(userEntity.getUid());
                }
            }
            WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(appId);
            if (wxAppInfoEnum == null && StringUtils.isNotBlank(unionId) && !unionId.equals(userEntity.getWxUnionId())) {
                userManager.updateUserUnionId(unionId, uid);
            }
        }
        // 让微信小程序的token失效 重新调pLogin生成token
        redisManager.delSessionByUid(userEntity.getUid());

        // 更新fs_bind表
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
        if (null != fsBindEntity && !Objects.equals(appId, fsBindEntity.getAppId())) {
            fsBindManager.updateFsBindAppId(appId, fsBindEntity.getUid());
        }
        boolean createAccountResult = accountManager.createAccount(uid, dingUserInfo.getName(), null, dingUserInfo.getAvatar(), redisKISApplyInfoEntity.getPhone(), redisKISApplyInfoEntity.getFsEa(), appId, AccountTypeEnum.DING_MINI_APP.getType());
        if (!createAccountResult) {
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess(result);
    }

    public String hasWxAccount(String corpid, String qyUserId, String appId) {
        if (StringUtils.isBlank(corpid) || StringUtils.isBlank(qyUserId)) {
            return null;
        }
        // 先查询该用户手机号
        QywxCorpAgentConfigEntity agentConfig = qywxManager.getAgentConfigEntityByAppId(corpid, appId);
        if (agentConfig == null) {
            return null;
        }
        String accessToken = qywxManager.getAccessToken(agentConfig.getEa());
        if (accessToken == null) {
            return null;
        }
        StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(agentConfig.getEa(), qyUserId, accessToken, false);
        String mobile = staffDetailResult != null ? staffDetailResult.getMobile() : null;
        if (StringUtils.isBlank(mobile)) {
            return null;
        }
        Integer fsUserId = fsAddressBookManager.getFsUserIdByPhone(agentConfig.getEa(), staffDetailResult.getMobile());
        if (fsUserId == null) {
            return null;
        }
        String uid = qywxUserManager.getUidByFsUserInfo(agentConfig.getEa(), fsUserId);
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        UserEntity userEntity = userManager.queryByUid(uid);
        if(userEntity == null) {
            return null;
        }
        if (StringUtils.isBlank(userEntity.getOpenid())) {
            return null;
        }
        return uid;
    }

    public String hasWxAccount2(String corpid, String qyUserId, String suiteId, String phone) {
        if (StringUtils.isBlank(corpid) || StringUtils.isBlank(qyUserId)) {
            return null;
        }
        // 先查询该用户手机号
        QywxCorpAgentConfigEntity agentConfig = qywxManager.getAgentConfigEntityBySuiteId(corpid, suiteId);
        if (agentConfig == null) {
            return null;
        }
        String accessToken = qywxManager.getAccessToken(agentConfig.getEa());
        if (accessToken == null) {
            return null;
        }
        StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(agentConfig.getEa(), qyUserId, accessToken, false);
        String mobile = staffDetailResult != null ? staffDetailResult.getMobile() : null;
        if (StringUtils.isBlank(mobile)) {
            mobile = phone;
        }
        if (StringUtils.isBlank(mobile)) {
            // 查询虚拟表
            QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getVirtualUserByQywxInfo(corpid, qyUserId, agentConfig.getEa());
            if (qywxVirtualFsUserEntity == null) {
                return null;
            }
            Integer userId = qywxVirtualFsUserEntity.getUserId();
            if (QywxUserConstants.isVirtualUserId(userId)) {
                // 虚拟userId不做处理
                return null;
            }
            // 查询纷享身份绑定的user，这个时候
            String uid = fsBindManager.queryByFsEaAndUserId(agentConfig.getEa(), userId);
            if (StringUtils.isBlank(uid)){
                return null;
            }
            UserEntity userEntity = userManager.queryByUid(uid);
            if (userEntity != null && StringUtils.isNotBlank(userEntity.getOpenid()) && StringUtils.isBlank(userEntity.getQyUserId())) {
                // 说明是微信身份
                return userEntity.getUid();
            }
            return null;
        }
        Integer fsUserId = fsAddressBookManager.getFsUserIdByPhone(agentConfig.getEa(), mobile);
        if (fsUserId == null) {
            return null;
        }
        String uid = qywxUserManager.getUidByFsUserInfo(agentConfig.getEa(), fsUserId);
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        UserEntity userEntity = userManager.queryByUid(uid);
        if(userEntity == null) {
            return null;
        }
        if (StringUtils.isBlank(userEntity.getOpenid())) {
            return null;
        }
        return uid;
    }

    public void buildStaffTokenMap(Map<String, Object> map, FSBindEntity fsBindEntity, String sessionKey, String appId, String openId) {
        map.put("fsEa", fsBindEntity.getFsEa());
        map.put("fsEi", eIEAConverter.enterpriseAccountToId(fsBindEntity.getFsEa()));
        map.put("fsUserId", fsBindEntity.getFsUserId());
        map.put("uid", fsBindEntity.getUid());
        map.put("sessionKey", sessionKey);
        map.put("appId", appId);
        map.put("openid", openId);
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId());
        if (qywxVirtualFsUserEntity != null) {
            map.put("corpId", qywxVirtualFsUserEntity.getCorpId());
            map.put("qyUserId", qywxVirtualFsUserEntity.getQyUserId());
        }
    }

    /**
     * 通过ea fsUserId获取uid
     * @param fsEa              ea
     * @param fsUserId          虚拟/纷享用户id
     * @return                  uid
     */
    private String getUidByFsUserInfo(String fsEa, Integer fsUserId) {
        if (StringUtils.isBlank(fsEa) || fsUserId == null) {
            return null;
        }

        String dingUid = fsBindManager.queryDingUidByFsUserIdAndEa(fsEa, fsUserId, AccountTypeEnum.DING_MINI_APP.getType());;
        return dingUid;
    }


    /**
     * 多企业关联同一个小程序
     * @param
     * @return
     */
    public Result<HLoginResult> hLogin(String code, String corpId,String suitId) {
        HLoginResult result = new HLoginResult();
        result.setCorpId(corpId);
        Optional<String> eaOptional = qywxManager.getFsEaByQyWxCorpId2(corpId, suitId);
        if (!eaOptional.isPresent()) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxManager.getAgentConfigEntityBySuiteId(corpId, suitId);
        if (null == qywxCorpAgentConfigEntity) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        String ea = qywxCorpAgentConfigEntity.getEa();
        //可以获取wxappid
        String appId = eaWechatAccountBindDao.getWxAppIdByEa(ea, "YXT");
        QywxCustomerAppInfoEntity entity = qywxCustomerAppInfoDAO.selectOne(suitId, corpId);
        if (null == entity) {
            return hLogin4Self(code, corpId);
            //return new Result<>(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        String accessToken = qywxManager.getAgentAccessToken(corpId, suitId, entity.getAuthCode());
        if (StringUtils.isBlank(accessToken)) {
            return new Result<>(SHErrorCode.QYWX_INNER_ACCESS_TOKEN_NOT_FOUND);
        }

        String wxRes;
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + accessToken + "&code=" + code;
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("WxLoginServiceImpl.hLogin getuserinfo exception:", e);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.error("WxLoginServiceImpl.hLogin getuserinfo response is null");
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        log.info("WxLoginServiceImpl.hLogin object={}", object.toJSONString());
        if (0 != (int) object.get("errcode")) {
            log.error("WxLoginServiceImpl.hLogin hLogin response error :{}", object);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        String qyUserId = object.getString("UserId");
        String externalUserId = object.getString("external_userid");
        String openId = object.getString("OpenId");
        String uid = "";
        String name = null;
        String avatar = null;
        Integer gender = null;
        String phone = null;
        //获取用户敏感信息
        QywxUserInfoDetailResult qywxUserInfoDetailResult = null;
        if (StringUtils.isNotBlank(qyUserId) && StringUtils.isNotBlank(object.getString("user_ticket"))){
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserdetail?access_token=" + accessToken;
            GetUserDetailArg arg = new GetUserDetailArg();
            arg.setUserTicket(object.getString("user_ticket"));
            qywxUserInfoDetailResult = httpManager.executePostHttp(arg,url,new TypeToken<QywxUserInfoDetailResult>(){});
            log.info("getuserdetail by user_ticket result:{}", GsonUtil.getGson().toJson(qywxUserInfoDetailResult));
        }
        //获取用户姓名
        GetQywxUserInfoDetailResult getQywxUserInfoDetailResult = null;
        if (StringUtils.isNotBlank(qyUserId)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + accessToken + "&userid=" + qyUserId;
            getQywxUserInfoDetailResult = httpManager.executeGetHttp(url,new TypeToken<GetQywxUserInfoDetailResult>(){});
            log.info("get user name result:{}", GsonUtil.getGson().toJson(getQywxUserInfoDetailResult));
        }
        if (qywxUserInfoDetailResult != null) {
            avatar = qywxUserInfoDetailResult.getAvatar();
            phone = qywxUserInfoDetailResult.getMobile();
            if (StringUtils.isNotBlank(qywxUserInfoDetailResult.getGender())) {
                gender = Integer.parseInt(qywxUserInfoDetailResult.getGender());
            }
        }
        if (StringUtils.isNotBlank(qyUserId) && StringUtils.isNotBlank(phone)) {
            qywxAddressBookManager.updateMobile(ea, qyUserId, phone);
        }
        if (getQywxUserInfoDetailResult != null) {
            name = getQywxUserInfoDetailResult.getName();
        }
        boolean bindWxInfoBefore = false;
        if (StringUtils.isNotBlank(appId)) {
            UserEntity userEntity = null;
            if (StringUtils.isNotBlank(qyUserId)) {
                //企微员工
                userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(corpId, qyUserId, appId);
            }
            if (userEntity == null && StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(externalUserId)) {
                //企微客户
                userEntity = userManager.queryByOpenIdAndExternalUserIdAndAppId(openId, externalUserId, appId);
            }
            if (userEntity == null && StringUtils.isNotBlank(openId)) {
                //访客
                userEntity = userManager.queryByOpenidAndAppid(openId, appId);
            }
            if (null == userEntity) {
                // 查询是否开通过微信身份
                String wxUid = hasWxAccount2(corpId, qyUserId, suitId, phone);
                if (StringUtils.isNotBlank(wxUid)) {
                    // 增加企业微信身份
                    userManager.addQywxUserInfo(appId, corpId, qyUserId, wxUid);
                    uid = wxUid;
                    bindWxInfoBefore = true;
                } else {
                    userEntity = new UserEntity();
                    uid = UUIDUtil.getUUID();
                    userEntity.setUid(uid);
                    if (StringUtils.isNotBlank(qyUserId)) {
                        userEntity.setCorpid(corpId);
                        userEntity.setQyUserId(qyUserId);
                        userEntity.setName(name);
                        userEntity.setAvatar(avatar);
                        userEntity.setGender(gender);
                    } else if(StringUtils.isNotBlank(externalUserId)) {
                        userEntity.setExternalUserId(externalUserId);
                        userEntity.setOpenid(openId);
                    } else {
                        userEntity.setOpenid(openId);
                    }
                    userEntity.setAppid(appId);
                    log.info("WxLoginServiceImpl.hLogin userDAO.insert userEntity={}", userEntity);
                    userManager.insert(userEntity);
                }
            } else {
                uid = userEntity.getUid();
                if (StringUtils.isNotBlank(qyUserId)) {
                    userEntity.setQyUserId(qyUserId);
                    userEntity.setName(name);
                    userEntity.setAvatar(avatar);
                    userEntity.setGender(gender);
                } else if(StringUtils.isNotBlank(externalUserId)) {
                    userEntity.setExternalUserId(externalUserId);
                } else {
                    userEntity.setOpenid(openId);
                }
                userEntity.setAppid(appId);
                log.info("WxLoginServiceImpl.hLogin userDAO.update userEntity={}", userEntity);
                userManager.updateUser(userEntity);
            }
            //如果是员工,则进行创建账户
            if (StringUtils.isNotBlank(qyUserId)) {
                boolean account = accountManager.createAccount(uid, name, gender, avatar, phone, ea, appId, AccountTypeEnum.QYWX_MINI_APP.getType());
                if (!account) {
                    log.warn("WxLoginServiceImpl.hLogin createAccount error uid:{},name:{},gender:{},avatar:{},phone:{},ea:{},appId:{}",uid,name,gender,avatar,phone,ea,appId);
                }
            }
        } else {
            uid = UUIDUtil.getUUID();
        }

        result.setQyUserId(qyUserId);
        // 通过corpId+qyUserId换到fsEa+fsUserId
        Integer userId = null;
        if (StringUtils.isNotBlank(qyUserId)) {
            userId = qywxUserManager.getUserIdByQyWxInfo(ea, corpId, qyUserId, QywxUserConstants.TRY_TIME);
        }
        if (QywxUserConstants.isVirtualUserId(userId)) {
            Optional<Integer> fsUserOpt = qywxUserManager.getAssociateFsUserId(ea, qyUserId, phone);
            if (fsUserOpt.isPresent()){
                qywxUserManager.qywxUserBindFsUserFromVirtualUser(ea, userId, fsUserOpt.get(), qyUserId);
                userId = fsUserOpt.get();
            }
        }
        if (QywxUserConstants.isFsUserId(userId)){
            //绑定完纷享身份后，更新对象中负责人
            final Integer fsUserId = userId;
            ThreadPoolUtils.execute(() ->{
                qywxUserManager.updateQywxObjectOwner(ea, qyUserId, fsUserId);
            },ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        Integer fsEi = eIEAConverter.enterpriseAccountToId(ea);
        result.setFsUserId(userId);
        result.setEa(ea);
        String sessionKey = UUIDUtil.getUUID();
        redisManager.setQywxH5ByUid(uid, sessionKey);
        String token = TokenUtil.generateQywxH5Token(corpId, qyUserId, sessionKey, qywxCorpAgentConfigEntity.getAgentid(), ea, userId, uid, fsEi, appId,externalUserId,openId);
        result.setToken(token);
        result.setAgentId(qywxCorpAgentConfigEntity.getAgentid());
        result.setSessionKey(sessionKey);
        result.setUid(uid);

        log.info("WxLoginServiceImpl.hLogin fsEa={}, fsUserId={}", ea, userId);
        if (StringUtils.isNotBlank(ea) && null != userId && !bindWxInfoBefore && StringUtils.isNotBlank(appId)) {
            if (null != fsEi) {
                FSBindEntity queryFSBindEntity = fsBindManager.queryFSBindByUid(uid);

                FSBindEntity fsBindEntity = new FSBindEntity();
                fsBindEntity.setUid(uid);
                fsBindEntity.setFsEa(ea);
                fsBindEntity.setFsUserId(userId);
                fsBindEntity.setFsCorpId(fsEi);
                fsBindEntity.setType(AccountTypeEnum.QYWX_MINI_APP.getType());
                fsBindEntity.setAppId(appId);
                if (qywxUserInfoDetailResult != null) {
                    fsBindEntity.setPhone(qywxUserInfoDetailResult.getMobile());
                }
                if (null == queryFSBindEntity) {
                    fsBindManager.insert(fsBindEntity);
                } else {
                    try {
                        // 新的userid已经绑定，则不予更新
                        if (QywxUserConstants.isFsUserId(userId)) {
                            FSBindEntity oldFsBindEntity = fsBindManager.queryByUserAndAppId(ea, userId, appId);
                            if (oldFsBindEntity != null && !Objects.equals(oldFsBindEntity.getUid(), uid)) {
                                // do nothing
                                log.info("fsBind handle do nothing, ea:{}, userId:{}, newUid:{}, oldUid:{}", ea, userId, uid, oldFsBindEntity.getUid());
                            } else {
                                // 未关联或者uid一致，直接更新
                                fsBindManager.update(fsBindEntity);
                            }
                        } else {
                            fsBindManager.update(fsBindEntity);
                        }
                    } catch (Exception e) {
                        log.warn("hLogin update fsbind error {}", e.getMessage());
                    }
                }
                // 更新名片上的模板
                cardManager.correctCard(uid, 0);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    /**
     * 自建应用H5授权登录
     * @param code
     * @param corpId
     * @return
     */
    public Result<HLoginResult> hLogin4Self(String code, String corpId) {
        HLoginResult result = new HLoginResult();
        result.setCorpId(corpId);
        Optional<String> eaOptional = qywxManager.getFsEaByQyWxCorpId2(corpId, null);
        if (!eaOptional.isPresent()) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxManager.getAgentConfigEntityBySuiteId(corpId, null);
        if (null == qywxCorpAgentConfigEntity) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        String ea = qywxCorpAgentConfigEntity.getEa();
        //可以获取wxappid
        String appId = eaWechatAccountBindDao.getWxAppIdByEa(ea, "YXT");
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            return new Result<>(SHErrorCode.QYWX_INNER_ACCESS_TOKEN_NOT_FOUND);
        }

        String wxRes;
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + accessToken + "&code=" + code;
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("WxLoginServiceImpl.hLogin getuserinfo exception:", e);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.error("WxLoginServiceImpl.hLogin getuserinfo response is null");
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }

        log.info("WxLoginServiceImpl.hLogin object={}", object.toJSONString());
        if (0 != (int) object.get("errcode")) {
            log.error("WxLoginServiceImpl.hLogin hLogin response error :{}", object);
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }
        String qyUserId = object.getString("UserId");
        String externalUserId = object.getString("external_userid");
        String openId = object.getString("OpenId");
        String uid = "";
        String name = null;
        String avatar = null;
        Integer gender = null;
        String phone = null;
        //获取用户敏感信息
        QywxUserInfoDetailResult qywxUserInfoDetailResult = null;
        if (StringUtils.isNotBlank(qyUserId) && StringUtils.isNotBlank(object.getString("user_ticket"))){
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserdetail?access_token=" + accessToken;
            GetUserDetailArg arg = new GetUserDetailArg();
            arg.setUserTicket(object.getString("user_ticket"));
            qywxUserInfoDetailResult = httpManager.executePostHttp(arg,url,new TypeToken<QywxUserInfoDetailResult>(){});
            log.info("getuserdetail by user_ticket result:{}", GsonUtil.getGson().toJson(qywxUserInfoDetailResult));
        }
        //获取用户姓名
        GetQywxUserInfoDetailResult getQywxUserInfoDetailResult = null;
        if (StringUtils.isNotBlank(qyUserId)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + accessToken + "&userid=" + qyUserId;
            getQywxUserInfoDetailResult = httpManager.executeGetHttp(url,new TypeToken<GetQywxUserInfoDetailResult>(){});
            log.info("get user name result:{}", GsonUtil.getGson().toJson(getQywxUserInfoDetailResult));
        }
        if (qywxUserInfoDetailResult != null) {
            avatar = qywxUserInfoDetailResult.getAvatar();
            phone = qywxUserInfoDetailResult.getMobile();
            if (StringUtils.isNotBlank(qywxUserInfoDetailResult.getGender())) {
                gender = Integer.parseInt(qywxUserInfoDetailResult.getGender());
            }
        }
        if (getQywxUserInfoDetailResult != null) {
            name = getQywxUserInfoDetailResult.getName();
        }
        boolean bindWxInfoBefore = false;
        if (StringUtils.isNotBlank(appId)) {
            UserEntity userEntity = null;
            if (StringUtils.isNotBlank(qyUserId)) {
                //企微员工
                userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(corpId, qyUserId, appId);
            } else if(StringUtils.isNotBlank(externalUserId)) {
                //企微客户
                userEntity = userManager.queryByOpenIdAndExternalUserIdAndAppId(openId,externalUserId,appId);
            } else {
                //访客
                userEntity = userManager.queryByOpenidAndAppid(openId,appId);
            }
            if (null == userEntity) {
                // 查询是否开通过微信身份
                String wxUid = hasWxAccount2(corpId, qyUserId, null, phone);
                if (StringUtils.isNotBlank(wxUid)) {
                    // 增加企业微信身份
                    userManager.addQywxUserInfo(appId, corpId, qyUserId, wxUid);
                    uid = wxUid;
                    bindWxInfoBefore = true;
                } else {
                    userEntity = new UserEntity();
                    uid = UUIDUtil.getUUID();
                    userEntity.setUid(uid);
                    if (StringUtils.isNotBlank(qyUserId)) {
                        userEntity.setCorpid(corpId);
                        userEntity.setQyUserId(qyUserId);
                        userEntity.setName(name);
                        userEntity.setAvatar(avatar);
                        userEntity.setGender(gender);
                    } else if(StringUtils.isNotBlank(externalUserId)) {
                        userEntity.setExternalUserId(externalUserId);
                        userEntity.setOpenid(openId);
                    } else {
                        userEntity.setOpenid(openId);
                    }
                    userEntity.setAppid(appId);
                    log.info("WxLoginServiceImpl.hLogin userDAO.insert userEntity={}", userEntity);
                    userManager.insert(userEntity);
                }
            } else {
                uid = userEntity.getUid();
                if (StringUtils.isNotBlank(qyUserId)) {
                    userEntity.setQyUserId(qyUserId);
                    userEntity.setName(name);
                    userEntity.setAvatar(avatar);
                    userEntity.setGender(gender);
                } else if(StringUtils.isNotBlank(externalUserId)) {
                    userEntity.setExternalUserId(externalUserId);
                } else {
                    userEntity.setOpenid(openId);
                }
                userEntity.setAppid(appId);
                log.info("WxLoginServiceImpl.hLogin userDAO.update userEntity={}", userEntity);
                userManager.updateUser(userEntity);
            }
            //如果是员工,则进行创建账户
            if (StringUtils.isNotBlank(qyUserId)) {
                boolean account = accountManager.createAccount(uid, name, gender, avatar, phone, ea, appId, AccountTypeEnum.QYWX_MINI_APP.getType());
                if (!account) {
                    log.warn("WxLoginServiceImpl.hLogin createAccount error uid:{},name:{},gender:{},avatar:{},phone:{},ea:{},appId:{}",uid,name,gender,avatar,phone,ea,appId);
                }
            }
        } else {
            uid = UUIDUtil.getUUID();
        }

        result.setQyUserId(qyUserId);
        // 通过corpId+qyUserId换到fsEa+fsUserId
        Integer userId = null;
        if (StringUtils.isNotBlank(qyUserId)) {
            userId = qywxUserManager.getUserIdByQyWxInfo(ea, corpId, qyUserId, QywxUserConstants.TRY_TIME);
        }
        if (QywxUserConstants.isVirtualUserId(userId)) {
            Optional<Integer> fsUserOpt = qywxUserManager.getAssociateFsUserId(ea, qyUserId, phone);
            if (fsUserOpt.isPresent()){
                qywxUserManager.qywxUserBindFsUserFromVirtualUser(ea, userId, fsUserOpt.get(), qyUserId);
                userId = fsUserOpt.get();
            }
        }
        if (QywxUserConstants.isFsUserId(userId)){
            //绑定完纷享身份后，更新对象中负责人
            final Integer fsUserId = userId;
            ThreadPoolUtils.execute(() ->{
                qywxUserManager.updateQywxObjectOwner(ea, qyUserId, fsUserId);
            },ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        Integer fsEi = eIEAConverter.enterpriseAccountToId(ea);
        result.setFsUserId(userId);
        result.setEa(ea);
        String sessionKey = UUIDUtil.getUUID();
        redisManager.setQywxH5ByUid(uid, sessionKey);
        String token = TokenUtil.generateQywxH5Token(corpId, qyUserId, sessionKey, qywxCorpAgentConfigEntity.getAgentid(), ea, userId, uid, fsEi, appId,externalUserId,openId);
        result.setToken(token);
        result.setAgentId(qywxCorpAgentConfigEntity.getAgentid());
        result.setSessionKey(sessionKey);
        result.setUid(uid);

        log.info("WxLoginServiceImpl.hLogin fsEa={}, fsUserId={}", ea, userId);
        if (StringUtils.isNotBlank(ea) && null != userId && !bindWxInfoBefore && StringUtils.isNotBlank(appId)) {
            if (null != fsEi) {
                FSBindEntity queryFSBindEntity = fsBindManager.queryFSBindByUid(uid);

                FSBindEntity fsBindEntity = new FSBindEntity();
                fsBindEntity.setUid(uid);
                fsBindEntity.setFsEa(ea);
                fsBindEntity.setFsUserId(userId);
                fsBindEntity.setFsCorpId(fsEi);
                fsBindEntity.setType(AccountTypeEnum.QYWX_MINI_APP.getType());
                fsBindEntity.setAppId(appId);
                if (qywxUserInfoDetailResult != null) {
                    fsBindEntity.setPhone(qywxUserInfoDetailResult.getMobile());
                }
                if (null == queryFSBindEntity) {
                    fsBindManager.insert(fsBindEntity);
                } else {
                    try {
                        fsBindManager.update(fsBindEntity);
                    } catch (Exception e) {
                        log.warn("hLogin update fsbind error {}", e.getMessage());
                    }
                }
                // 更新名片上的模板
                cardManager.correctCard(uid, 0);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<IsBindWechatMiniAppResult> isBindWechatMiniApp(IsBindWechatMiniAppArg arg) {
        IsBindWechatMiniAppResult result = new IsBindWechatMiniAppResult();
        if (StringUtils.isEmpty(arg.getUid())){
            result.setIsBindWechatMiniApp(false);
            return Result.newSuccess(result);
        }

        AccountEntity accountEntity = accountDAO.queryAccountByUid(arg.getUid());
        if (accountEntity == null) {
            result.setIsBindWechatMiniApp(false);
            return Result.newSuccess(result);
        }

        result.setIsBindWechatMiniApp(true);
        return Result.newSuccess(result);
    }

    @Override
    public Result<isOpeningResult> isOpening(IsOpeningArg arg) {
        return null;
    }

    @Override
    public Result<GetMarketingVersionsResult> getMarketingVersions(GetMarketingVersionsArg arg) {
        return null;
    }

}
