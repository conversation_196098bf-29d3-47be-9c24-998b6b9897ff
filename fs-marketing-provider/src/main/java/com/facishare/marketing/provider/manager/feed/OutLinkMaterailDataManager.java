package com.facishare.marketing.provider.manager.feed;

import com.facishare.marketing.api.data.material.OutLinkBriefData;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.provider.dao.OutLinkDAO;
import com.facishare.marketing.provider.entity.OutLinkEntity;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class OutLinkMaterailDataManager extends MaterailDataManager<OutLinkBriefData> {

    @Autowired
    private OutLinkDAO outLinkDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Override
    public List<OutLinkBriefData> get(String ea, String... objectIds) {
        List<OutLinkBriefData> datas = Lists.newArrayList();
        List<OutLinkEntity> entities = outLinkDAO.getByIds(Lists.newArrayList(objectIds));
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = null;
        Set<Integer> creatorIds = entities.stream().map(OutLinkEntity::getCreateBy).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(creatorIds)){
            fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, new ArrayList<>(creatorIds), false);
        }
        if (CollectionUtils.isNotEmpty(entities)) {
            for (OutLinkEntity entity : entities) {
                OutLinkBriefData data = new OutLinkBriefData();
                data.setId(entity.getId());
                data.setObjectType(this.getType());
                data.setName(entity.getName());
                data.setUrl(entity.getUrl());
                data.setIsSupportEmbedded(entity.getIsSupportEmbedded());
                data.setTitle(entity.getTitle());
                data.setDescribe(entity.getDescribe());
                data.setCover(entity.getCover());
                data.setCreateTime(entity.getCreateTime().getTime());
                data.setUpdateTime(entity.getLastUpdateTime().getTime());
                data.setCreator(entity.getCreateBy());
                FsAddressBookManager.FSEmployeeMsg employeeInfo = fsEmployeeMsgMap.get(entity.getCreateBy());
                if (employeeInfo != null) {
                    data.setCreatorName(employeeInfo.getName());
                }
                datas.add(data);
            }
        }
        return datas;
    }


    @Override
    public Integer getType() {
        return ObjectTypeEnum.OUT_LINK.getType();
    }

}
