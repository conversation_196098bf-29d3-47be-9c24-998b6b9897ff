package com.facishare.marketing.provider.dao.hexagon;

import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface HexagonTemplatePageDAO {
    @Select("select * from hexagon_template_page where id=#{id} and status!=4")
    HexagonTemplatePageEntity getById(@Param("id") String id);

    @Select("select * from hexagon_template_page where ea=#{ea} and status!=4")
    HexagonTemplatePageEntity getByEa(@Param("ea") String ea);

    @Select("delete from hexagon_template_page where id=#{id}")
    void deleteById(@Param("id") String id);

    @Insert("INSERT INTO hexagon_template_page"
            + "(id,ea,name,hexagon_template_site_id,share_title,share_desc,share_pic_h5_apath,share_pic_mp_apath,content,form_id,is_homepage,status,create_by,create_time,update_time)"
            + " VALUES"
            + " (#{obj.id}, #{obj.ea},#{obj.name},#{obj.hexagonTemplateSiteId},#{obj.shareTitle},#{obj.shareDesc},#{obj.sharePicH5Apath},#{obj.sharePicMpApath}, #{obj.content}, #{obj.formId}, #{obj.isHomepage},1, #{obj.createBy},now(), now())")
    int insert(@Param("obj") HexagonTemplatePageEntity hexagonTemplatePageEntity);


    @Update("<script>"
            + "UPDATE hexagon_template_page"
            + "    <set>"
            + "         <if test=\"name != null\">\n"
            + "             name = #{name},\n"
            + "          </if>\n"
            + "         <if test=\"shareTitle != null\">\n"
            + "             share_title = #{shareTitle},\n"
            + "         </if>\n"
            + "         <if test=\"shareDesc != null\">\n"
            + "             share_desc = #{shareDesc},\n"
            + "         </if>\n"
            + "         <if test=\"sharePicH5Apath != null\">\n"
            + "             share_pic_h5_apath = #{sharePicH5Apath},\n"
            + "         </if>\n"
            + "         <if test=\"sharePicMpApath != null\">\n"
            + "             share_pic_mp_apath = #{sharePicMpApath},\n"
            + "         </if>\n"
            + "         <if test=\"content != null\">\n"
            + "             content = #{content},\n"
            + "         </if>\n"
            + "         <if test=\"status != null\">\n"
            + "             status = #{status},\n"
            + "          </if>\n"
            + "         <if test=\"isHomepage != null\">\n"
            + "             is_homepage = #{isHomepage},\n"
            + "          </if>\n"
            + "         <if test=\"formId != null\">\n"
            + "             form_id = #{formId},\n"
            + "          </if>\n"
            + "         update_time = now()\n"
            + "    </set>"
            + "    WHERE id = #{id}"
            + "</script>")
    int update(HexagonTemplatePageEntity obj);

    @Update("<script>"
            + "UPDATE hexagon_template_page"
            + "    <set>"
            + "         <if test=\"name != null\">\n"
            + "             name = #{name},\n"
            + "          </if>\n"
            + "         <if test=\"shareTitle != null\">\n"
            + "             share_title = #{shareTitle},\n"
            + "         </if>\n"
            + "         <if test=\"shareDesc != null\">\n"
            + "             share_desc = #{shareDesc},\n"
            + "         </if>\n"
            + "         <if test=\"sharePicH5Apath != null\">\n"
            + "             share_pic_h5_apath = #{sharePicH5Apath},\n"
            + "         </if>\n"
            + "         <if test=\"sharePicMpApath != null\">\n"
            + "             share_pic_mp_apath = #{sharePicMpApath},\n"
            + "         </if>\n"
            + "         <if test=\"content != null\">\n"
            + "             content = #{content},\n"
            + "         </if>\n"
            + "         <if test=\"status != null\">\n"
            + "             status = #{status},\n"
            + "          </if>\n"
            + "         <if test=\"isHomepage != null\">\n"
            + "             is_homepage = #{isHomepage},\n"
            + "          </if>\n"
            + "         form_id = #{formId},\n"
            + "         update_time = now()\n"
            + "    </set>"
            + "    WHERE id = #{id}"
            + "</script>")
    int updateV2(HexagonTemplatePageEntity obj);

    @Update("<script>"
            + " UPDATE hexagon_template_page"
            + "    <set>"
            + "      share_pic_h5_apath = #{sharePicH5Apath},\n"
            + "      share_pic_mp_apath = #{sharePicMpApath},\n"
            + "      update_time = now()\n"
            + "    </set>"
            + "    WHERE id = #{id}"
            + "</script>")
    int updatePageSharePic(HexagonTemplatePageEntity hexagonTemplatePageEntity);

    @Update("update hexagon_template_page set status = 4 where hexagon_template_site_id = #{hexagonTemplateSiteId}")
    int deleteBySiteId(@Param("hexagonTemplateSiteId") String hexagonTemplateSiteId);

    @Select("select * from hexagon_template_page where hexagon_template_site_id=#{hexagonSystemSiteId} and status!=4 order by is_homepage ")
    List<HexagonTemplatePageEntity> getBySiteId(@Param("hexagonSystemSiteId") String hexagonSystemSiteId);

    @Select("select * from hexagon_template_page where is_homepage = 1 and hexagon_template_site_id = #{hexagonSiteId} and status != 4")
    HexagonTemplatePageEntity getHomePage(@Param("hexagonSiteId") String hexagonSiteId);

    @Update("update hexagon_template_page set is_homepage=2 where hexagon_template_site_id=#{hexagonSiteId}")
    int removeHomePage(@Param("hexagonSiteId") String hexagonSieId);

    @Update("update hexagon_template_page set is_homepage =1 where id=#{id}")
    int setHomePage(@Param("id") String id);


    @Update("<script>"
            + "UPDATE hexagon_template_page"
            + "<trim prefix=\"set\" suffixOverrides=\",\">"
            + " <trim prefix=\"name =case\" suffix=\"end,\">"
            + " 		<foreach collection=\"pageEntityList\" item=\"i\" index=\"index\">"
            + "<if test=\"i.name!=null\">"
            + "when id=#{i.id} then #{i.name}"
            + "          </if>\n"
            + "</foreach>"
            + " </trim>"
            + " <trim prefix=\"content =case\" suffix=\"end,\">"
            + " 		<foreach collection=\"pageEntityList\" item=\"i\" index=\"index\">"
            + "<if test=\"i.content!=null\">"
            + "when id=#{i.id} then #{i.content}"
            + "          </if>\n"
            + "</foreach>"
            + " </trim>"
            + "     </trim>"
            + "    where\n" +
            "    <foreach collection=\"pageEntityList\" separator=\"or\" item=\"i\" index=\"index\" >\n" +
            "              id=#{i.id}\n" +
            "          </foreach>"
            + "</script>")
    int updatePageActionId(@Param("pageEntityList") List<HexagonTemplatePageEntity> pageEntityList);
}