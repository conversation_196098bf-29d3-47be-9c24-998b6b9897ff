package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.PhotoAssociationEntity;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2019/08/30
 **/
public interface PhotoAssociationDAO {

    @Insert("INSERT INTO photo_association(id, photo_target_type, link_id, create_time) VALUES (#{obj.id}, #{obj.photoTargetType}, #{obj.photoAssociationLinkIds,typeHandler=PhotoAssociationLinkIdsTypeHandler}, now());")
    int insertPhotoAssociation(@Param("obj") PhotoAssociationEntity photoAssociationEntity);

    @Select("SELECT id FROM photo_association WHERE photo_target_type = #{type} AND link_id ->> 'formId' = #{formId} AND link_id ->> 'marketingEventId' = #{marketingEventId} AND link_id ->> 'objectId' = #{objectId} AND link_id ->> 'objectType' = #{objectType} ORDER BY create_time DESC")
    List<String> getDataByTypeFormIdAndMarketingEventId(@Param("type") Integer type, @Param("objectId") String objectId, @Param("objectType") String objectType, @Param("formId") String formId,
        @Param("marketingEventId") String marketingEventId);

}
