package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.provider.entity.distribution.DistributorApplicationEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created  By zhoux 2018/12/28
 **/
public interface DistributorApplicationDAO {


    @Select("<script>"
        + "SELECT * FROM distributor_application\n"
        + "    WHERE distributor_id = #{distributorId}\n"
        + "    AND operator_id = #{operatorId}\n"
        + "    ORDER BY update_time DESC\n"
        + "    LIMIT 1"
        + "</script>")
    DistributorApplicationEntity getLatestInfoById(@Param("distributorId") String distributorId, @Param("operatorId") String operatorId);

    @Select("<script>"
        + "SELECT * FROM distributor_application\n"
        + "    WHERE distributor_id = #{distributorId}\n"
        + "    ORDER BY create_time DESC\n"
        + "    LIMIT 1"
        + "</script>")
    DistributorApplicationEntity getLatestInfoByDistributorId(@Param("distributorId") String distributorId);

    @Insert("INSERT INTO distributor_application(\n"
        + "        \"id\",\n"
        + "            \"distributor_id\",\n"
        + "            \"operator_id\",\n"
        + "            \"source_type\",\n"
        + "            \"source_id\",\n"
        + "            \"status\",\n"
        + "            \"refuse_desc\",\n"
        + "            \"create_time\",\n"
        + "            \"update_time\"\n"
        + "    )\n"
        + "    VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.distributorId},\n"
        + "        #{obj.operatorId},\n"
        + "        #{obj.sourceType},\n"
        + "        #{obj.sourceId},\n"
        + "        #{obj.status},\n"
        + "        #{obj.refuseDesc},\n"
        + "    now(),\n"
        + "    now()\n"
        + "        )")
    int addDistributorApplicationInfo(@Param("obj") DistributorApplicationEntity distributorApplicationEntity);

    @Select("<script>"
            + "   SELECT * FROM distributor_application WHERE distributor_id = #{distributorId} AND source_type = #{sourceType}"
            + "   AND source_id = #{sourceId} ORDER BY update_time DESC LIMIT 1"
            + "</script>")
    DistributorApplicationEntity getLatestInfoByDistributorIdAndSourceId(@Param("distributorId") String distributorId,
                                            @Param("sourceType") Integer sourceType,
                                            @Param("sourceId") String sourceId);

    @Update("<script>"
        + "UPDATE distributor_application\n"
        + "    <set>\n"
        + "        <if test=\"refuseDesc != null\">\n"
        + "            \"refuse_desc\" = #{refuseDesc},\n"
        + "        </if>\n"
        + "        status = #{status},\n"
        + "        update_time = now()\n"
        + "    </set>\n"
        + "    WHERE id = #{id}"
        + "</script>")
    boolean updateDistributorApplicationStatus(@Param("id") String id, @Param("refuseDesc") String refuseDesc, @Param("status") Integer status);


    @Select("<script>"
        + "SELECT * FROM distributor_application\n"
        + "WHERE id = #{id}\n"
        + "</script>")
    DistributorApplicationEntity getApplicationInfoById(@Param("id") String id);
}
