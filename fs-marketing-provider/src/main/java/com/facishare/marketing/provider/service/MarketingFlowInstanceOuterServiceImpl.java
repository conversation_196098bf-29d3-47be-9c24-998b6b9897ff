package com.facishare.marketing.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.ConferenceEnrollOrCheckInEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.outapi.service.MarketingFlowInstanceOuterService;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowTaskDao;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowAdditionalConfigEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowTaskEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.MarketingFlowInstanceManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019-08-06
 */
@Slf4j
@Service("marketingFlowInstanceOuterService")
public class MarketingFlowInstanceOuterServiceImpl implements MarketingFlowInstanceOuterService {


    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingFlowInstanceManager marketingFlowInstanceManager;
    @Autowired
    private MarketingFlowTaskDao marketingFlowTaskDao;

    /**
     * 微联服务号侧
     */
    @Override
    public Result<Boolean> enrollOrCheckInConference(String ea, String wxAppId, String wxOpenId, String phone, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxOpenId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(conferenceId));
        Preconditions.checkArgument(conferenceEnrollOrCheckInEnum != null);

        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(ea);
        associationArg.setWxAppId(wxAppId);
        associationArg.setAssociationId(wxOpenId);
        associationArg.setPhone(phone);
        associationArg.setType(ChannelEnum.WX_SERVICE.getType());
        associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        associationArg.setTriggerAction("enrollOrCheckInConference");
        AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
        if (result != null && StringUtils.isNotEmpty(result.getUserMarketingAccountId())) {
            String userMarketingAccountId = result.getUserMarketingAccountId();
            doFinishConferenceInstance(ea, conferenceId, conferenceEnrollOrCheckInEnum, userMarketingAccountId);
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> enrollOrCheckInConferenceByH5(String ea, String fingerPrintId, String phone, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(fingerPrintId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(conferenceId));
        Preconditions.checkArgument(conferenceEnrollOrCheckInEnum != null);

        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(ea);
        associationArg.setAssociationId(fingerPrintId);
        associationArg.setPhone(phone);
        associationArg.setType(ChannelEnum.BROWSER_USER.getType());
        associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        associationArg.setTriggerAction("enrollOrCheckInConferenceByH5");
        AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
        if(result != null && result.getUserMarketingAccountId() != null){
            doFinishConferenceInstance(ea, conferenceId, conferenceEnrollOrCheckInEnum, result.getUserMarketingAccountId());
        }
        return Result.newSuccess(true);
    }

    private void doFinishConferenceInstance(String ea, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum, String userMarketingAccountId) {
        if (conferenceEnrollOrCheckInEnum == ConferenceEnrollOrCheckInEnum.CHECK_IN){
            this.doStartCheckInConferenceFlowInstances(ea, conferenceId, userMarketingAccountId);
            this.doFinishCheckInConferenceTasks(ea, conferenceId, userMarketingAccountId);
        }else if (conferenceEnrollOrCheckInEnum == ConferenceEnrollOrCheckInEnum.ENROLL){
            this.doStartEnrollConferenceFlowInstances(ea, conferenceId, userMarketingAccountId);
            this.doFinishEnrollConferenceTasks(ea, conferenceId, userMarketingAccountId);
        }
    }

    /**
     * 小程序侧
     */
    @Override
    public Result<Boolean> enrollOrCheckInConference(String ea, String uid, String phone, String conferenceId, ConferenceEnrollOrCheckInEnum conferenceEnrollOrCheckInEnum) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(uid));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(conferenceId));
        Preconditions.checkArgument(conferenceEnrollOrCheckInEnum != null);

        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(ea);
        associationArg.setAssociationId(uid);
        associationArg.setPhone(phone);
        associationArg.setType(ChannelEnum.MINIAPP.getType());
        associationArg.setTriggerAction("enrollOrCheckInConference");
        associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
        if (result != null && StringUtils.isNotEmpty(result.getUserMarketingAccountId())) {
            String userMarketingAccountId = result.getUserMarketingAccountId();
            doFinishConferenceInstance(ea, conferenceId, conferenceEnrollOrCheckInEnum, userMarketingAccountId);
        }
        return Result.newSuccess(true);
    }

    private void doStartCheckInConferenceFlowInstances(String ea, String conferenceId, String userMarketingAccountId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingFlowAdditionalConfigEntity> marketingFlowAdditionalConfigs = marketingFlowAdditionalConfigDao.listMatchedCheckInConferenceFlows(ei, conferenceId);
        if (marketingFlowAdditionalConfigs != null && !marketingFlowAdditionalConfigs.isEmpty()) {
            Set<String> marketingFlowIds = marketingFlowAdditionalConfigs.stream().map(MarketingFlowAdditionalConfigEntity::getBpmFlowId).collect(Collectors.toSet());
            marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), marketingFlowIds);
        }
    }

    private void doFinishCheckInConferenceTasks(String ea, String conferenceId, String userMarketingAccountId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingFlowTaskEntity> tasks = marketingFlowTaskDao.listMatchedCheckInReferenceTasks(ei, userMarketingAccountId, conferenceId);
        if (tasks != null && !tasks.isEmpty()) {
            Set<String> taskIds = tasks.stream().map(MarketingFlowTaskEntity::getTaskId).collect(Collectors.toSet());
            marketingFlowInstanceManager.finishTasks(ei, taskIds, null);
        }
    }
    private void doStartEnrollConferenceFlowInstances(String ea, String conferenceId, String userMarketingAccountId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingFlowAdditionalConfigEntity> marketingFlowAdditionalConfigs = marketingFlowAdditionalConfigDao.listMatchedEnrollConferenceFlows(ei, conferenceId);
        if (marketingFlowAdditionalConfigs != null && !marketingFlowAdditionalConfigs.isEmpty()) {
            Set<String> marketingFlowIds = marketingFlowAdditionalConfigs.stream().map(MarketingFlowAdditionalConfigEntity::getBpmFlowId).collect(Collectors.toSet());
            marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), marketingFlowIds);
        }
    }

    private void doFinishEnrollConferenceTasks(String ea, String conferenceId, String userMarketingAccountId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingFlowTaskEntity> tasks = marketingFlowTaskDao.listMatchedEnrollReferenceTasks(ei, userMarketingAccountId, conferenceId);
        if (tasks != null && !tasks.isEmpty()) {
            Set<String> taskIds = tasks.stream().map(MarketingFlowTaskEntity::getTaskId).collect(Collectors.toSet());
            marketingFlowInstanceManager.finishTasks(ei, taskIds, null);
        }
    }
}
