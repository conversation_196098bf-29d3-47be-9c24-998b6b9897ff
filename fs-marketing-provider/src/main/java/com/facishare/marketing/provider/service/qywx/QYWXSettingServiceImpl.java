package com.facishare.marketing.provider.service.qywx;

import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.arg.QYWXMiniAppConfigArg;
import com.facishare.marketing.api.arg.common.QywxCardUidArg;
import com.facishare.marketing.api.arg.hexagon.GetOfficialWebsiteInfoArg;
import com.facishare.marketing.api.arg.qywx.GetQywxAccessTokenArg;
import com.facishare.marketing.api.arg.qywx.QYWXBaseArg;
import com.facishare.marketing.api.result.GetCardCommonSettingResult;
import com.facishare.marketing.api.result.GetSubscribeMessageTemplateIdsResult;
import com.facishare.marketing.api.result.WechatAccountChannelResult;
import com.facishare.marketing.api.result.qywx.*;
import com.facishare.marketing.api.service.EnterpriseMetaConfigService;
import com.facishare.marketing.api.service.qywx.QYWXSettingService;
import com.facishare.marketing.api.service.wxcallback.QywxSelfBuildAppCallbackService;
import com.facishare.marketing.api.vo.qywx.ConfirmQywxBindVO;
import com.facishare.marketing.api.vo.qywx.GetQywxAuthUrlVO;
import com.facishare.marketing.api.vo.qywx.GetQywxBindStatusVO;
import com.facishare.marketing.api.vo.qywx.config.QYWXConfigVO;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.MiniappIntroductionSiteTypeEnum;
import com.facishare.marketing.common.contstant.MiniappSubscribeMessageEnum;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.CardCommonSettingShowTypeEnum;
import com.facishare.marketing.common.enums.CardCommonSettingTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.qywx.QywxBindStatusEnum;
import com.facishare.marketing.common.enums.qywx.QywxOpenStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.ReflectionUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.HexagonOfficialWebsiteDAO;
import com.facishare.marketing.provider.dao.TagModelDao;
import com.facishare.marketing.provider.dao.qywx.QywxContactMeConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.EnterpriseMetaConfigEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonOfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxContactMeConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.cardtemplate.CardTemplateManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.miniappLogin.MiniappAccessPermissionsManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service("qywxSettingService")
public class QYWXSettingServiceImpl implements QYWXSettingService {
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private WechatWorkExternalUserObjDescribeManager wechatWorkExternalUserObjDescribeManager;
    @Value("${miniapp.app.id}")
    private String miniappId;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private MiniappSubscribeMessageManager miniappSubscribeMessageManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    @Qualifier("wxWorkTagSynchronizationManager")
    private OuterTagSynchronizationManager wxWorkTagSynchronizationManager;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private HexagonOfficialWebsiteDAO hexagonOfficialWebsiteDAO;
    @Autowired
    private EnterpriseMetaConfigService enterpriseMetaConfigService;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private QywxContactMeConfigDAO qywxContactMeConfigDAO;
    @Autowired
    private CardManager cardManager;
    @Autowired
    private CardTemplateManager cardTemplateManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;
    @Autowired
    private QywxSelfBuildAppCallbackService qywxSelfBuildAppCallbackService;
    @Autowired
    private WechatGroupObjDescribeManager wechatGroupObjDescribeManager;
    @Autowired
    private WechatGroupUserObjDescribeManager wechatGroupUserObjDescribeManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MiniappAccessPermissionsManager miniappAccessPermissionsManager;
    @ReloadableProperty("host")
    private String host;
    @ReloadableProperty("center.host")
    private String centerHost;

    @Autowired
    private WechatFriendsRecordObjDescribeManager wechatFriendsRecordObjDescribeManager;

    @Autowired
    private WechatAccountGroupStatisticsObjManager wechatAccountGroupStatisticsObjManager;

    @Autowired
    private WechatAccountStatisticsObjManager wechatAccountStatisticsObjManager;

    @Autowired
    private OtherObjectDescribeManager otherObjectDescribeManager;

    @Autowired
    private TagModelDao tagModelDao;

    @Autowired
    private DisplayOrderManager displayOrderManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;
    @Autowired
    private AppVersionManager appVersionManager;


    @Override
    public Result<QywxOpenStatusResult> queryEnterpriseOpenStatus(QYWXBaseArg arg) {
        QywxOpenStatusResult result = new QywxOpenStatusResult();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            result.setOpenStatus(QywxOpenStatusEnum.CLOSE.getStatus());
        } else {
            result.setOpenStatus(QywxOpenStatusEnum.OPEN.getStatus());
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> setEnterpriseQywxAppAgentConfig(QYWXConfigVO arg) {
        //未使用，无访问入口
        return Result.newSuccess();
    }

    @Override
    public Result<Void> setEnterpriseMiniAppConfig(QYWXMiniAppConfigArg qywxMiniAppConfigArg) {
        Result<Void> setMinAppResult = setMiniAppAgentIdAndSecretKey(qywxMiniAppConfigArg.getFsEa(),
                wechatAccountManager.getNotEmptyWxAppIdByEa(qywxMiniAppConfigArg.getFsEa()),
                qywxMiniAppConfigArg.getQywxCorpId(), qywxMiniAppConfigArg.getMimiAppAgentId(),
                qywxMiniAppConfigArg.getMiniAppSecret());
        if (setMinAppResult.isSuccess()) {
            return Result.newSuccess();
        } else {
            return setMinAppResult;
        }
    }

    private Result<Void> setMiniAppAgentIdAndSecretKey(String ea, String wxAppId, String corpid, String agentid,
                                                       String secret) {
        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wxAppId);
        if (miniappConfigEntity != null) {
            int update = qywxMiniappConfigDAO.updateMiniappConfigByEa(ea, wxAppId, corpid, agentid, secret);
            if (update < 1) {
                return Result.newError(SHErrorCode.QYWX_SETTING_FAILE);
            }
        } else {
            QywxMiniappConfigEntity newMiniAppConfigEntity = new QywxMiniappConfigEntity();
            newMiniAppConfigEntity.setId(UUIDUtil.getUUID());
            newMiniAppConfigEntity.setEa(ea);
            newMiniAppConfigEntity.setSecret(secret);
            newMiniAppConfigEntity.setAgentid(agentid);
            newMiniAppConfigEntity.setCorpid(corpid);
            newMiniAppConfigEntity.setAppid(wxAppId);
            Date now = new Date();
            newMiniAppConfigEntity.setCreateTime(now);
            newMiniAppConfigEntity.setUpdateTime(now);
            qywxMiniappConfigDAO.insert(newMiniAppConfigEntity);
            return Result.newError(SHErrorCode.SUCCESS);
        }

        return Result.newError(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<QueryEnterpriseQywxConfigResult> queryEnterpriseQywxConfig(QYWXBaseArg arg) {
        QueryEnterpriseQywxConfigResult result = new QueryEnterpriseQywxConfigResult();
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(arg.getFsEa());
        if (agentConfig == null) {
            return Result.newSuccess(result);
        }

        result.setQywxCorpId(agentConfig.getCorpid());
        result.setAppName(agentConfig.getAppName());
        result.setAppAgentId(agentConfig.getAgentid());
        result.setAppSecret(agentConfig.getSecret());
        result.setSelfAppEncodingAesKey(agentConfig.getSelfAppEncodingAesKey());
        result.setSelfAppToken(agentConfig.getSelfAppToken());
        result.setCustomerContactSecret(agentConfig.getCustomerContactSecret());
        result.setCustomerAuthUrl(qywxManager.geneCustomerAuthUrl(arg.getFsEa()));
        result.setIsEncrypt(agentConfig.getIsEncrypt());

        QywxMiniappConfigEntity miniappConfigEntity = qywxMiniappConfigDAO.getByEa(arg.getFsEa(),
                wechatAccountManager.getNotEmptyWxAppIdByEa(arg.getFsEa()));
        if (miniappConfigEntity != null) {
            result.setMimiAppAgentId(miniappConfigEntity.getAgentid());
            result.setMiniAppSecret(miniappConfigEntity.getSecret());
        }

        List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities =
                qywxCustomerAppInfoDAO.selectByCorpIdAndEa(agentConfig.getCorpid(), arg.getFsEa());
        if (!CollectionUtil.isEmpty(qywxCustomerAppInfoEntities)) {
            result.setSuitId(qywxCustomerAppInfoEntities.get(0).getSuitId());
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateWxWorkExternalUsersToCrm(String ea, List<String> wxWorkExternalUserIds) {
        ThreadPoolUtils.execute(() -> {
            wechatWorkExternalUserObjManager.mergeWxWorkExternalUserListToCrmLimited(ea, wxWorkExternalUserIds, true, null);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncAllWxWorkExternalUserToCrm(String ea) {
        ThreadPoolUtils.execute(() -> {
            wechatWorkExternalUserObjManager.initEnterpriseData(ea);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        //异步处理企微客户群
        ThreadPoolUtils.execute(() -> {
            wechatGroupObjDescribeManager.initData(ea);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<GetSubscribeMessageTemplateIdsResult> getSubscribeMessageTemplateIds(String wxAppId) {
        if (StringUtils.isBlank(wxAppId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetSubscribeMessageTemplateIdsResult result = new GetSubscribeMessageTemplateIdsResult();
        CountDownLatch countDownLatch = new CountDownLatch(MiniappSubscribeMessageEnum.values().length);
        for (MiniappSubscribeMessageEnum miniappSubscribeMessageEnum : MiniappSubscribeMessageEnum.values()) {
            ThreadPoolUtils.execute(() -> {
                try {
                    String id = miniappSubscribeMessageManager.getOrCreateSubscribeMessageTemplateId(wxAppId,
                            miniappSubscribeMessageEnum).orElse(null);
                    ReflectionUtil.setFieldValueByName(result, miniappSubscribeMessageEnum.getResultIdName(), id);
                } catch (Exception e) {
                    log.warn("QYWXSettingServiceImpl#getSubscribeMessageTemplateIds", e);
                } finally {
                    countDownLatch.countDown();
                }
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        try {
            countDownLatch.await(6, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("QYWXSettingServiceImpl#getSubscribeMessageTemplateIds.await error:", e);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> getMiniappIntroductionPageId(String wxAppId) {
        if (WxAppInfoEnum.getByAppId(wxAppId) != null) {
            return Result.newSuccess();
        }
        List<String> eas = eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(MKThirdPlatformConstants.PLATFORM_ID,
                wxAppId);
        if (!eas.isEmpty()) {
            EnterpriseMetaConfigEntity enterpriseMetaConfig = enterpriseMetaConfigDao.getByEa(eas.get(0));
            if (enterpriseMetaConfig.getMiniappIntroductionSiteType() != null && enterpriseMetaConfig.getMiniappIntroductionSiteType() == MiniappIntroductionSiteTypeEnum.USE_OFFICIAL_WEB_SITE.getType()) {
                List<HexagonOfficialWebsiteEntity> officialWebsiteList =
                        hexagonOfficialWebsiteDAO.getWebsiteHexagonSiteInfoByEa(eas.get(0));
                if (!officialWebsiteList.isEmpty()) {
                    return Result.newSuccess(officialWebsiteList.get(0).getHexagonSiteId());
                }
            }
            if (enterpriseMetaConfig.getMiniappIntroductionSiteType() != null && enterpriseMetaConfig.getMiniappIntroductionSiteType() == MiniappIntroductionSiteTypeEnum.SELF_DEFINE.getType()) {
                return Result.newSuccess(enterpriseMetaConfig.getMiniappIntroductionSiteId());
            }
        }
        return Result.newSuccess();
    }

    /**
     * 获得托管小程序的频道信息,首先根据token信息获得QYWXBaseArg信息，根据QYWXBaseArg信息获得ea信息。由ea信息查询出频道信息
     *
     * @param arg
     * @return
     */
    @Override
    public Result<List<WechatAccountChannelResult>> getWechatAccountChannelList(GetOfficialWebsiteInfoArg arg) {
        String cardUid = arg.getCardUid();
        String ea;
        if (StringUtils.isNotBlank(cardUid)) {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(cardUid);
            if (fsBindEntity == null) {
                log.warn("查询不到fsBindEntity arg:{} carUid:{}", arg, cardUid);
                return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
            }
            ea = fsBindEntity.getFsEa();
        } else {
            log.warn("carUid为空 arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Result<List<WechatAccountChannelResult>> wechatAccountChannelList =
                enterpriseMetaConfigService.getWechatAccountChannelList(ea);
        return wechatAccountChannelList;
    }

    @Override
    public Result<Void> batchSyncAllWxWorkExternalUserToCrm(List<String> eas) {
        if (CollectionUtils.isEmpty(eas)) {
            return Result.newSuccess();
        }

        ThreadPoolUtils.execute(() -> {
            for (String ea : eas) {
                wechatWorkExternalUserObjManager.initEnterpriseData(ea);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<List<GetCardCommonSettingResult>> getCardCommonSetting(QywxCardUidArg arg) {
        String ea = null;
        String uid = null;
        if (StringUtils.isBlank(arg.getCardUid())) {
            Collection<String> eas =
                    wechatAccountManager.listEaByPlatformIdAndWxAppId(MKThirdPlatformConstants.PLATFORM_ID,
                            arg.getAppId());
            if (CollectionUtils.isEmpty(eas)) {
                return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
            }
            ea = eas.stream().findAny().get();
        } else {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getCardUid());
            if (fsBindEntity == null) {
                log.warn("fsBindEntity is null arg:{}", arg);
                return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
            }
            ea = fsBindEntity.getFsEa();
            uid = fsBindEntity.getUid();
        }

        String cardTemplateIdByUid = cardManager.getCardTemplateIdByUidOrEa(arg.getCardUid(), ea);
        Result<List<GetCardCommonSettingResult>> getCardCommonSettingResult = cardTemplateManager.getCardCommonSetting(ea, cardTemplateIdByUid);
        if (!getCardCommonSettingResult.isSuccess()) {
            return getCardCommonSettingResult;
        }
        List<GetCardCommonSettingResult> result = getCardCommonSettingResult.getData();
        if (arg.getCardSettingType() != null) {
            List<GetCardCommonSettingResult> tempResult = Lists.newArrayList();
            for (GetCardCommonSettingResult data : result) {
                if (data.getType().equals(arg.getCardSettingType())) {
                    tempResult.add(data);
                }
            }
            result = tempResult;
        }
        getCardCommonSettingResult.setData(handleUserCardCommonSetting(uid, result));
        return getCardCommonSettingResult;
    }

    private List<GetCardCommonSettingResult> handleUserCardCommonSetting(String uid,
                                                                         List<GetCardCommonSettingResult> getCardCommonSettingResultList) {
        List<GetCardCommonSettingResult> result = Lists.newArrayList();
        for (GetCardCommonSettingResult getCardCommonSettingResult : getCardCommonSettingResultList) {
            if (getCardCommonSettingResult.getType().equals(CardCommonSettingTypeEnum.CUSTOMER_CONTACT.getType()) && StringUtils.isNotBlank(uid)) {
                // 若开启的为联系我校验是否有联系我功能
                QywxContactMeConfigEntity qywxContactMeConfigEntity = qywxContactMeConfigDAO.getByUid(uid);
                FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
                boolean isVirtualUserId = QywxUserConstants.isVirtualUserId(fsBindEntity.getFsUserId());
                GetCardCommonSettingResult customerContactSetting = BeanUtil.copy(getCardCommonSettingResult,
                        GetCardCommonSettingResult.class);
                if (getCardCommonSettingResult.getShowType().equals(CardCommonSettingShowTypeEnum.CONTACT_ME.getType()) && qywxContactMeConfigEntity == null) {
                    customerContactSetting.setShowType(!isVirtualUserId ? CardCommonSettingShowTypeEnum.PERSONAL_COMMUNICATION.getType() : CardCommonSettingShowTypeEnum.CONTACT_ME.getType());
                } else if (getCardCommonSettingResult.getShowType().equals(CardCommonSettingShowTypeEnum.PERSONAL_COMMUNICATION.getType()) && isVirtualUserId) {
                    customerContactSetting.setShowType(qywxContactMeConfigEntity != null ? CardCommonSettingShowTypeEnum.CONTACT_ME.getType() : CardCommonSettingShowTypeEnum.PERSONAL_COMMUNICATION.getType());
                }
                result.add(customerContactSetting);
            } else {
                result.add(getCardCommonSettingResult);
            }
        }
        return result;
    }

    @Override
    public Result<GetQywxAuthUrlResult> getQywxAuthUrl(GetQywxAuthUrlVO vo) {
        GetQywxAuthUrlResult result = new GetQywxAuthUrlResult();
        result.setAuthUrl(qywxManager.geneCustomerAuthUrl(vo.getEa()));

        //纷享云，直接保存
        if(appVersionManager.isFxCloud() || qywxManager.isVpnDisconnectCloud()){
            qywxSelfBuildAppCallbackService.saveQYWXCorpIdAndEa(vo.getEa(), host);
        } else {
            //调用分享云接口存ea和corpid的映射关系
            String url = centerHost + "/marketing/wxThirdCloudInner/saveQYWXCorpIdAndEa";
            FormBody requestBody = new FormBody.Builder()
                .add("id", UUIDUtil.getUUID())
                .add("corpId", "-")
                .add("ea", vo.getEa())
                .add("host", host)
                .build();
            httpManager.executePostByOkHttpClientWithRequestBody(requestBody, url, new TypeToken<Result<String>>() {
            });
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetQywxBindStatusResult> getQywxBindStatus(GetQywxBindStatusVO vo) {
        QywxBindStatusEnum qywxBindStatus = qywxManager.getQywxBindStatus(vo.getEa());
        GetQywxBindStatusResult result = new GetQywxBindStatusResult();
        result.setStatus(qywxBindStatus.getValue());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> confirm(ConfirmQywxBindVO vo) {
        agentConfigDAO.confirm(vo.getEa());
        return Result.newSuccess();
    }

    @Override
    public Result<GetQywxAccessTokenResult> getQywxAccessToken(GetQywxAccessTokenArg arg) {
        String accessToken = qywxManager.getAccessToken(eieaConverter.enterpriseIdToAccount(arg.getEi()));
        GetQywxAccessTokenResult result = new GetQywxAccessTokenResult();
        result.setAccessToken(accessToken);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> isVpnDisconnectCloud() {
        boolean ret = appVersionManager.isVpnDisconnectCloud();
        return Result.newSuccess(ret);
    }

}
