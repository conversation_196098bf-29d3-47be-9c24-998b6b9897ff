package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.api.result.member.MemberEnrollResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.outapi.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.outapi.arg.MemberEnrollArg;
import com.facishare.marketing.outapi.arg.MemberNeedMemberEnrollArg;
import com.facishare.marketing.outapi.arg.QueryMiniAppMemberContentArg;
import com.facishare.marketing.outapi.arg.result.BuildMemberInfoByFormResult;
import com.facishare.marketing.outapi.arg.result.QueryMemberContentResult;
import com.facishare.marketing.outapi.result.MemberCheckResult;
import com.facishare.marketing.outapi.result.MemberConfigVO;
import com.facishare.marketing.outapi.result.QueryMemberInfoResult;
import com.facishare.marketing.outapi.service.MemberService;
import com.facishare.marketing.provider.manager.MemberManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @aut
 * hor doggy
 * Created on 2020-10-30.
 */
@Service("memberOutService")
public class MemberOutServiceImpl implements MemberService {
	@Autowired
	private com.facishare.marketing.api.service.MemberService memberService;

	@Autowired
	private MemberManager memberManager;
	
	@Override
	public Result<String> checkWxMiniAppUserHaveMemberAuth(Integer objectType, String objectId, String uid, boolean checkObjectAccess) {
		return memberService.checkWxMiniAppUserHaveMemberAuth(objectType, objectId, uid, checkObjectAccess);
	}
	
	@Override
	public Result<Boolean> tryWxMiniAppUserRegisterOrLogin(String ea, String uid, String hexagonSiteId, CustomizeFormDataEnrollArg arg) {
		com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg customizeFormDataEnrollArg = BeanUtil.copy(arg, com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg.class);
		customizeFormDataEnrollArg.setSubmitContent(BeanUtil.copy(arg.getSubmitContent(), CustomizeFormDataEnroll.class));
		return memberService.tryWxMiniAppUserRegisterOrLogin(ea, uid, hexagonSiteId, customizeFormDataEnrollArg);
	}
	
	@Override
	public Result<Boolean> trySyncLeadToMember(String ea, String uid, String customerFormDataUserId) {
		return memberService.trySyncLeadToMember(ea, uid, customerFormDataUserId);
	}
	
	@Override
	public Result<MemberConfigVO> getMemberConfig(Integer objectType, String objectId) {
		Result<com.facishare.marketing.api.vo.MemberConfigVO> result = memberService.getMemberConfig(objectType, objectId);
		if (result.getData() != null){
			return Result.newSuccess(BeanUtil.copy(result.getData(), MemberConfigVO.class));
		}
		return Result.newError(result.getErrCode(), result.getErrMsg());
	}
	
	@Override
	public Result<Boolean> checkWxMiniAppUserInMarketingEvent(Integer objectType, String objectId, String uid, String marketingEventId) {
		Result<MemberEnrollResult> memberEnrollResultResult = memberService.checkWxMiniAppUserInMarketingEvent(objectType, objectId, uid, marketingEventId);
		if (!memberEnrollResultResult.isSuccess()) {
			return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
		}
		if (memberEnrollResultResult.getData() == null) {
			return Result.newSuccess();
		}
		return Result.newSuccess(memberEnrollResultResult.getData().getMemberEnroll());
	}
	
	
	@Override
	public Result<String> wxMiniAppMemberEnroll(String uid, MemberEnrollArg memberEnrollArg) {
		com.facishare.marketing.api.arg.MemberEnrollArg arg = BeanUtil.copy(memberEnrollArg, com.facishare.marketing.api.arg.MemberEnrollArg.class);
		Result<MemberEnrollResult> memberEnrollResultResult = memberService.wxMiniAppMemberEnroll(uid, arg);
		if (!memberEnrollResultResult.isSuccess()) {
			return Result.newError(memberEnrollResultResult.getErrCode(), memberEnrollResultResult.getErrMsg());
		}
		if (memberEnrollResultResult.getData() == null) {
			return Result.newSuccess();
		}
		return Result.newSuccess(memberEnrollResultResult.getData().getLeadId());
	}

	@Override
	public Result<QueryMemberInfoResult> queryMiniAppMemberInfo(String objectId, Integer objectType, String uid) {
		return Result.newSuccess();
	}

	@Override
	public Result<PageResult<QueryMemberContentResult>> queryMiniAppMemberContent(QueryMiniAppMemberContentArg outArg) {
		return Result.newSuccess();
	}

	@Override
	public Result<BuildMemberInfoByFormResult> buildMiniAppMemberInfoByForm(String formId, String uid) {
		Result<com.facishare.marketing.api.result.member.BuildMemberInfoByFormResult> buildMemberInfoByFormResult = memberService.buildMiniAppMemberInfoByForm(formId, uid);
		if (!buildMemberInfoByFormResult.isSuccess()) {
			return Result.newError(buildMemberInfoByFormResult.getErrCode(), buildMemberInfoByFormResult.getErrMsg());
		}
		BuildMemberInfoByFormResult result = BeanUtil.copy(buildMemberInfoByFormResult.getData(), BuildMemberInfoByFormResult.class);
		return Result.newSuccess(result);
	}

	@Override
	public Result<Boolean> memberNeedMemberEnroll(MemberNeedMemberEnrollArg arg) {
		boolean needMemberEnroll = false;
		try {
			needMemberEnroll = memberManager.memberNeedMemberEnroll(arg.getEa(), arg.getMarketingEventId(), arg.getMemberId());
		} catch (Exception e) {
			return Result.newSuccess(false);
		}
		return Result.newSuccess(needMemberEnroll);
	}

	@Override
	public Result<MemberCheckResult> checkWxMiniAppUserHaveMemberResultAuth(Integer objectType, String objectId, String uid, boolean checkObjectAccess) {
		Result<com.facishare.marketing.api.result.MemberCheckResult> memberResult = memberService.checkWxMiniAppUserHaveMemberResultAuth(objectType,objectId, uid, checkObjectAccess);
		if (memberResult.isSuccess() && memberResult.getData() != null) {
			MemberCheckResult checkResult = BeanUtil.copy(memberResult.getData(), MemberCheckResult.class);
			return Result.newSuccess(checkResult);
		}
		return Result.newError(memberResult.getErrCode(),memberResult.getErrMsg());
	}
}
