package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.userContent.CreateArticleArg;
import com.facishare.marketing.api.result.cta.CtaRelationInfo;
import com.facishare.marketing.api.service.UserProfileRepositoryService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.outService.arg.article.CrawlerArticleContentArg;
import com.facishare.mankeep.api.outService.arg.cover.CreateLuckyMoneyIconCoverAsyncArg;
import com.facishare.mankeep.api.outService.result.article.CrawlerArticleContentResult;
import com.facishare.mankeep.api.outService.service.OutArticleService;
import com.facishare.mankeep.api.outService.service.OutCoverService;
import com.facishare.mankeep.api.outService.service.OutQRCodeService;
import com.facishare.mankeep.api.vo.PreLoadWebArticleVO;
import com.facishare.mankeep.common.enums.QRCodeTypeEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.util.GsonUtil;
import com.facishare.marketing.api.AddCustomArticlesResult;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.ArticleService;
import com.facishare.marketing.api.service.ObjectSloganRelationService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.ArticleConstants;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.TimeMeasureUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.WebCrawlerUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.manager.ArticleDAOManager;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.manager.MaterialRelationDaoManager;
import com.facishare.marketing.provider.dao.param.article.ArticleQueryParam;
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao;
import com.facishare.marketing.provider.dto.ArticleEntityDTO;
import com.facishare.marketing.provider.dto.CustomizeFormUserDataCountByObjectIdDTO;
import com.facishare.marketing.provider.dto.ObjectStatisticData;
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FileV2Manager.FileManagerPicResult;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.image.ImageDrawer;
import com.facishare.marketing.provider.manager.image.ImageDrawerTypeEnum;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager.CreateQRCodeResult;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.util.statistics.util.ArticleStatisticsUtil;
import com.facishare.marketing.statistic.outapi.arg.ActionDurationTimeObjectArg;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.marketing.statistic.outapi.result.ActionDurationTimeAvgByObjectResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.util.Constant.*;

@Service("articleService")
@Slf4j
public class ArticleServiceImpl implements ArticleService {
    private Gson gs = new Gson();

    private final String TEMP_A_WAREHOUSE_TYPE = "TA";
    //封面图片最大限制
    private final int LIMIT_PHOTO_MAX_LENGTH = 100 * 1024;
    //封面图片最小限制
    private final int LIMIT_PHOTO_MIN_LENGTH = 20 * 1024;

    //企业自建文章
    private final int CUSTOMZIE_ARTICLE = 1;
    //公众号文章
    private final int WX_ARTICLE = 2;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private PictureManager pictureManager;
    @ReloadableProperty("picture.preview.url")
    private String sharePath;
    @ReloadableProperty("picture.fsEa")
    private String ea;
    @Autowired
    private ArticleStatisticsUtil articleStatisticsUtil;
    @Autowired
    private OutCoverService outCoverService;

    @Autowired
    private ImageCreator imageCreator;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private MaterialTagManager materialTagManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private OutQRCodeService outQRCodeService;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private OutArticleService outArticleService;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO marketingEventMaterialRelationDAO;

    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private ArticleDAOManager articleDAOManager;

    @Autowired
    private QRCodeManager qrCodeManager;

    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;

    @Value("${host}")
    private String host;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private ProductDAO productDAO;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private MaterialRelationDao materialRelationDao;

    @Autowired
    private MaterialRelationDaoManager materialRelationDaoManager;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private  CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private MarketingObjectAmountStatisticDao marketingObjectAmountStatisticDao;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectSloganRelationService objectSloganRelationService;
    @Autowired
    private UserProfileRepositoryService userProfileRepositoryService;

    @Autowired
    private SendService sendService;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    //默认文章提前解析宽度
    private final int ARTICLE_LOAD_DEFAULT_WIDTH = 375;

    public static String getValues(List<String> result) {
        String value = "";
        if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
            value = result.get(0).trim();
        }
        return value;
    }

    @Override
    public Result<InitWebCrawlerArticleResult> initWebCrawlerArticle(String ea, Integer fsUserId, String url,Integer scopeType) {
        ArticleEntity articleEntity = null;
        if(scopeType!=1){
            articleEntity = articleDAO.queryArticleByEaAndUrl(ea, url.trim());
        }
        InitWebCrawlerArticleResult result;
        if (articleEntity != null) {
            result = new InitWebCrawlerArticleResult();
            result.setId(articleEntity.getId());
            result.setUrl(articleEntity.getUrl());
            result.setCreateSourceType(articleEntity.getCreateSourceType());
            if(StringUtils.isNotBlank(articleEntity.getArticlePath())){
                byte[] bytes = fileV2Manager.downloadAFile(articleEntity.getArticlePath(), null);
                String content = bytes == null ? "" : new String(bytes);
                result.setWebPage(content);
            }
            result.setTitle(articleEntity.getTitle());
            result.setCreator(articleEntity.getCreator());
            result.setSource(articleEntity.getSource());
            result.setSummary(articleEntity.getSummary());
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId(),ea);
            if(null != photoEntity){
                result.setPhotoApath(photoEntity.getPath());
                result.setPhotoUrl(photoEntity.getUrl());
                result.setThumbApath(photoEntity.getThumbnailPath());
                result.setThumbUrl(photoEntity.getThumbnailUrl());
            }
            // 查询对应挂接表单
            CustomizeFormDataObjectEntity customizeFormDataObjectEntity =  customizeFormDataObjectDAO.getObjectBindingForm(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if(customizeFormDataObjectEntity != null) {
                QueryArticleDetailResult.FormData formData = new QueryArticleDetailResult.FormData();
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataObjectEntity.getFormId());
                formData.setFormId(customizeFormDataObjectEntity.getFormId());
                formData.setFormTitle(customizeFormDataEntity.getFormHeadSetting().getTitle());
                formData.setFormName(customizeFormDataEntity.getFormHeadSetting().getName());
                result.setFormButtonName(customizeFormDataObjectEntity.getFormButtonName());
                result.setFormStyleType(customizeFormDataObjectEntity.getFormStyleType());
                result.setButtonStyle(customizeFormDataObjectEntity.getButtonStyle());
                result.setFormData(formData);
                result.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
            } else {
                // 查询对应挂接微页面   微页面和表单只能挂接一种
                HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
                if (hexagonSiteObjectEntity != null) {
                    HexagonSiteEntity hexagonSiteEntity = hexagonSiteManager.getBindHexagonSiteByObject(hexagonSiteObjectEntity.getSiteId());
                    if (hexagonSiteEntity != null) {
                        InitWebCrawlerArticleResult.HexagonSiteData hexagonSiteData = new InitWebCrawlerArticleResult.HexagonSiteData();
                        hexagonSiteData.setSiteId(hexagonSiteEntity.getId());
                        hexagonSiteData.setSiteName(hexagonSiteEntity.getName());
                        result.setFormButtonName(hexagonSiteObjectEntity.getFormButtonName());
                        result.setFormStyleType(hexagonSiteObjectEntity.getFormStyleType());
                        result.setButtonStyle(hexagonSiteObjectEntity.getButtonStyle());
                        result.setHexagonSiteData(hexagonSiteData);
                        result.setBindObjectType(BindObjectType.HEXAGON_SITE.getType());
                    }
                } else {
                    ActivityBindObjectEntity activityBindObject = articleDAO.getBindObject(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
                    if (activityBindObject != null) {
                        ProductEntity productEntity = productDAO.queryProductDetail(activityBindObject.getBindObjectId());
                        if (productEntity != null) {
                            result.setFormButtonName(activityBindObject.getButtonName());
                            result.setFormStyleType(activityBindObject.getStyleType());
                            result.setButtonStyle(activityBindObject.getButtonStyle());
                            result.setBindObjectId(productEntity.getId());
                            result.setBindObjectName(productEntity.getName());
                            result.setBindObjectType(BindObjectType.PRODUCT.getType());
                        }
                    }
                }
            }
            ObjectTagEntity tagEntity = objectTagManager.queryObjectTag(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if (tagEntity != null) {
                result.setTagNameList(tagEntity.getTagNameList());
            }
            MaterialRelationEntity relationEntity = materialRelationDao.queryMaterialRelationByObjectId(articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if(relationEntity!=null&&relationEntity.getSharePosterAPath()!=null){
                result.setSharePosterAPath(relationEntity.getSharePosterAPath());
                result.setSharePosterUrl(fileV2Manager.getUrlByPath(relationEntity.getSharePosterAPath(),ea,false));
            }
            if(result != null && StringUtils.isNotBlank(result.getId())){
                // 获取裁剪封面图
                PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType(), result.getId());
                if (coverCutMiniAppPhotoEntity != null) {
                    result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType(), result.getId());
                if (coverCutH5PhotoEntity != null) {
                    result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType(), result.getId());
                if (coverCutOrdinaryPhotoEntity != null) {
                    result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                    //返回原图
                    result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                }
            }
            result.setCtaRelationInfos(Lists.newArrayList());
            List<CtaRelationEntityDTO> ctaRelationList = ctaRelationDaoManager.getCtaRelationList(articleEntity.getFsEa(), ObjectTypeEnum.ARTICLE.getType(), Lists.newArrayList(articleEntity.getId()));
            if(CollectionUtils.isNotEmpty(ctaRelationList)){
                ctaRelationList.forEach(ctaRelationEntity -> {
                    CtaRelationInfo ctaRelationInfo = new CtaRelationInfo();
                    ctaRelationInfo.setCtaId(ctaRelationEntity.getCtaId());
                    ctaRelationInfo.setCtaName(ctaRelationEntity.getCtaName());
                    result.getCtaRelationInfos().add(ctaRelationInfo);
                });
            }
            return Result.newSuccess(result);
        } else {
            result = crawlerArticleContent(url);
            if (result != null) {
                return Result.newSuccess(result);
            }
        }

        return Result.newError(SHErrorCode.SYSTEM_ERROR);
    }

    private InitWebCrawlerArticleResult crawlerArticleContent(String url) {
        CrawlerArticleContentArg arg = new CrawlerArticleContentArg();
        arg.setUrl(url.trim());
        ModelResult<CrawlerArticleContentResult> modelResult = outArticleService.crawlerArticleContent(arg);
        if (modelResult.isSuccess() && modelResult.getData() != null) {

            InitWebCrawlerArticleResult result = BeanUtil.copy(modelResult.getData(), InitWebCrawlerArticleResult.class);
            result.setUrl(url.trim());
            result.setCreateSourceType(ArticleCreateSourceTypeEnum.WEB.getType());
            return result;
        }
        return null;
    }

    @Override
    public Result<AddWebCrawlerArticleResult> addWebCrawlerArticle(String ea, Integer fsUserId, AddArticleArg vo) {
        if (vo.getScopeType() !=1 && StringUtils.isBlank(vo.getId()) && null != articleDAO.queryArticleByEaAndUrl(ea, vo.getUrl().trim())) {//已存在
            return new Result(SHErrorCode.ARTICLE_EXIST);
        }
        vo.setFsEa(ea);
        vo.setFsUserId(fsUserId);
        ArticleEntity entity = null;
        Integer type = ProductArticleTypeEnum.CORPORATE.getType();
        vo.setType(type);
        //gerenwuliao处理封面图
        if(vo.getCoverTapath().startsWith("N_")){
            Optional<String> cpath = fileV2Manager.copyAPathToCPath(ea, 1000, vo.getCoverTapath());
            cpath.ifPresent(vo::setCoverTapath);
        }
        if (StringUtils.isBlank(vo.getId())) {
            //文章不能重名
            int count = articleDAO.queryArticleCountByName(ea, vo.getTitle());
            if (count > 0){
                return Result.newError(SHErrorCode.ARTICLE_NAME_EXIST);
            }

            if (StringUtils.isBlank(vo.getContent())) {
                entity = setArticleEntity(vo);
                if (entity == null) {
                    return new Result<>(SHErrorCode.WEB_CRAWLER_FAIL);
                }
                CreateLuckyMoneyIconCoverAsyncArg createLuckyMoneyIconCoverAsyncArg = new CreateLuckyMoneyIconCoverAsyncArg();
                createLuckyMoneyIconCoverAsyncArg.setObjectType(ObjectTypeEnum.ARTICLE.getType());
                createLuckyMoneyIconCoverAsyncArg.setObjectId(entity.getId());
                outCoverService.createLuckyMoneyIconCoverAsync(createLuckyMoneyIconCoverAsyncArg);
            } else {
                if (StringUtils.isBlank(vo.getCoverTapath())) {
                    return new Result<>(SHErrorCode.ADD_NO_COVER);
                }
                entity = createArticleEntity(vo);
            }
            if (null == entity) {
                return new Result<>(SHErrorCode.ADD_FAIL);
            }
            entity.setFsEa(ea);
            entity.setFsUserId(fsUserId);
            entity.setUid("");

            entity.setCreateSourceType(ArticleCreateSourceTypeEnum.WEB.getType());
            if(!Strings.isNullOrEmpty(vo.getSharePosterAPath())){
                entity.setSharePosterAPath(vo.getSharePosterAPath());
            }
            boolean result = articleDAOManager.addArticle(entity);

            if (!result) {
                return new Result(SHErrorCode.ADD_FAIL);
            }

            HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(vo.getFsEa(), entity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if (hexagonSiteObjectEntity != null) { // 表单和微页面切换绑定需要先解绑另一个
                hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(hexagonSiteObjectEntity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
            }
            CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(vo.getFsEa(), entity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if (oldCustomizeFormDataObjectEntity != null) {
                customizeFormDataObjectDAO.deleteCustomizeFormDataObject(vo.getFsEa(), oldCustomizeFormDataObjectEntity.getFormId(), entity.getId(), ObjectTypeEnum.ARTICLE.getType());
            }
            ActivityBindObjectEntity oldBindObject = articleDAO.getBindObject(vo.getFsEa(), entity.getId(), ObjectTypeEnum.PRODUCT.getType());
            if (oldBindObject != null) {
                articleDAO.unBindObject(vo.getFsEa(), oldBindObject.getId());
            }
            // 修改表单关联
            if (BindObjectType.CUSTOMIZE_FORM.getType().equals(vo.getBindObjectType())) {
                customizeFormDataManager.bindCustomizeFormDataObject(vo.getFormId(), entity.getId(), ObjectTypeEnum.ARTICLE.getType(), ea, fsUserId, vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
            } else if (BindObjectType.HEXAGON_SITE.getType().equals(vo.getBindObjectType())) {
                // 修改微页面关联
                hexagonSiteManager.bindHexagonSiteObject(vo.getSiteId(), entity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(), vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
                // 创建产品关联(沿用微页面的物料关联表)
            } else if (BindObjectType.PRODUCT.getType().equals(vo.getBindObjectType())) {
                ActivityBindObjectEntity data = new ActivityBindObjectEntity();
                data.setId(UUIDUtil.getUUID());
                data.setEa(vo.getFsEa());
                data.setArticleId(entity.getId());
                data.setBindObjectId(vo.getBindObjectId());
                data.setBindObjectType(ObjectTypeEnum.PRODUCT.getType());
                data.setStyleType(vo.getFormStyleType());
                data.setButtonName(vo.getFormButtonName());
                data.setButtonStyle(vo.getButtonStyle());
                data.setCreateBy(vo.getFsUserId());
                data.setUpdateBy(vo.getFsUserId());
                articleDAO.bindObject(data);
            }

            //上报神策系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(vo.getFsEa(), vo.getFsUserId(), ObjectTypeEnum.ARTICLE.getType(), ArticleTypeEnum.REPRINT.getType(), entity.getId()));
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), vo.getTitle(), OperateTypeEnum.ADD);
            objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), Collections.singletonList(entity.getId()), vo.getGroupId());
        } else {
            ArticleEntity articleEntity = articleDAO.getById(vo.getId());
            if (articleEntity == null) {
                return new Result(SHErrorCode.ARTICLE_NOT_EXIST);
            }

            //文章不能重名
            if (!StringUtils.equals(vo.getTitle(), articleEntity.getTitle())) {
                int count = articleDAO.queryArticleCountByName(ea, vo.getTitle());
                if (count > 0) {
                    return Result.newError(SHErrorCode.ARTICLE_NAME_EXIST);
                }
            }
            entity = updateArticleEntity(vo, articleEntity);
            if(entity == null){
                return new Result(SHErrorCode.UPDATE_FAIL);
            }
            materialRelationDaoManager.insertOrUpdateMaterialRelationEntity(articleEntity.getId(),ObjectTypeEnum.ARTICLE.getType(),vo.getSharePosterAPath(),ea);
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), vo.getTitle(), OperateTypeEnum.EDIT);
        }
        AddWebCrawlerArticleResult addWebCrawlerArticleResult = new AddWebCrawlerArticleResult();
        addWebCrawlerArticleResult.setId(entity.getId());
        if (entity != null) {
            objectTagManager.addOrUpdateObjectTag(ea, fsUserId, entity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getTagNameList());
        }
        ctaRelationDaoManager.addCtaRelation(ea, vo.getCtaIds(), ObjectTypeEnum.ARTICLE.getType(), entity.getId());
        //预加载文章
        String articleId = entity.getId();
        if(StringUtils.isNotBlank(vo.getOriginalImageAPath()) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
            for (PhotoCutOffset cutOffset:vo.getCutOffsetList()){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER, entity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER, entity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER, entity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
            }
        }
        String content = entity.getContent();
        preLoadArticle(articleId, content);
        //数据写到营销内容对象里
        if(StringUtils.isNotBlank(vo.getContentObjId())){
            //营销内容处理
            String fieldPromotionLink = host + "/proj/page/marketing/"+ea+"#/article/detail?objectId=" + articleId  + "&ea=" + ea + "&objectType=6";
            fieldPromotionLink = sendService.getShortUrl(fieldPromotionLink).getData().getShortUrl();
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("_id", vo.getContentObjId());
            dataMap.put("field_promotion_link",fieldPromotionLink);
            crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName(), dataMap);
        }else if (vo.getScopeType()==1){
            CreateArticleArg arg = new CreateArticleArg();
            arg.setEa(ea);
            arg.setUserId(fsUserId);
            arg.setUrl(vo.getUrl());
            if(StringUtils.isNotBlank(vo.getName())){
                arg.setName(vo.getName());
            }
            arg.setFieldIsStick(vo.getFieldIsStick());
            arg.setFieldMicroPageId(entity.getId());
            String fieldPromotionLink = host + "/proj/page/marketing/"+ea+"#/article/detail?objectId=" + articleId  + "&ea=" + ea + "&objectType=6";
            fieldPromotionLink = sendService.getShortUrl(fieldPromotionLink).getData().getShortUrl();
            arg.setFieldPromotionLink(fieldPromotionLink);
            arg.setCoverPath(vo.getCoverTapath());
            arg.setFieldPromotion(vo.getTitle());
            arg.setFieldShareSummary(vo.getSummary());
            userProfileRepositoryService.createArticle(arg);
        }
        return Result.newSuccess(addWebCrawlerArticleResult);
    }

    //后台解析文章内容并保存
    private void preLoadArticle(String articleId, String srcContent){
        PreLoadWebArticleVO preLoadWebArticleVO = new PreLoadWebArticleVO();
        preLoadWebArticleVO.setContent(srcContent);
        String loadContent = fileV2Manager.parseArticleContent(srcContent, ARTICLE_LOAD_DEFAULT_WIDTH);
        if (StringUtils.isNotBlank(loadContent)) {
            String oldPath = null;
            ArticleEntity articleEntity = articleDAO.getById(articleId);
            if (articleEntity != null){
                oldPath = articleEntity.getParsedContentPath();
            }

            String path = fileV2Manager.uploadToApath(loadContent.getBytes(), "json", null);
            if (path != null) {
                int updateRet = articleDAO.updateArticleParsedContentPath(articleId, path);
                if (updateRet > 0 && StringUtils.isNotBlank(oldPath)){
                    fileV2Manager.deleteFilesByApath(Lists.newArrayList(oldPath));
                }
            }
        }
    }

    @Override
    public Result updateArticlePhoto(String ea, Integer fsUserId, UpdateArticleArg vo) {
        //更换封面
        String photoUrl = vo.getPhotoUrl();
        String thumbnailPhotoUrl = photoUrl;
        if (StringUtils.isNotEmpty(photoUrl) && photoUrl.startsWith(TEMP_A_WAREHOUSE_TYPE)) {
            FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(photoUrl, null, null);
            if (null == fileManagerPicResult) {
                log.warn("fileManagerPicResult is not found, tapath={}", photoUrl);
                return new Result(SHErrorCode.SYSTEM_ERROR);
            }

            photoUrl = fileManagerPicResult.getUrl();
            thumbnailPhotoUrl = fileManagerPicResult.getThumbUrl();

            vo.setPhotoUrl(photoUrl);

            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.ARTICLE_COVER, vo.getId(), fileManagerPicResult.getUrlAPath(), fileManagerPicResult.getThumbUrlApath());

            Map<String, Object> normalImageParams = new HashMap<>();
            normalImageParams.put("coverApath", fileManagerPicResult.getUrlAPath());
            normalImageParams.put("ea", ea);
            ImageDrawer normalImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
            String normalCoverApath = normalImageDrawer.draw(normalImageParams);
            if (StringUtils.isNotBlank(normalCoverApath)) {
                photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, vo.getId(), normalCoverApath, normalCoverApath);
            }
        }else if(photoUrl.startsWith(TEMP_C_WAREHOUSE_TYPE) || photoUrl.startsWith(C_WAREHOUSE_TYPE)) {
            String cpath = photoUrl;
            if(photoUrl.startsWith(TEMP_C_WAREHOUSE_TYPE)){
                cpath = fileV2Manager.changeCWarehouseTempToPermanent(ea, photoUrl);
            }
            if (StringUtils.isBlank(cpath)) {
                log.warn("changeCWarehouseTempToPermanent is not found, tapath={}", photoUrl);
                return new Result(SHErrorCode.SYSTEM_ERROR);
            }

            photoUrl = fileV2Manager.getUrlByCPath(ea,cpath);

            vo.setPhotoUrl(photoUrl);

            photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.ARTICLE_COVER, vo.getId(), cpath, cpath);

            Map<String, Object> normalImageParams = new HashMap<>();
            normalImageParams.put("coverApath", cpath);
            normalImageParams.put("ea", ea);
            ImageDrawer normalImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
            String normalCoverApath = normalImageDrawer.draw(normalImageParams);
            if (StringUtils.isNotBlank(normalCoverApath)) {
                photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, vo.getId(), normalCoverApath, normalCoverApath);
            }
        }
        ArticleEntity entity = BeanUtil.copy(vo, ArticleEntity.class);
        entity.setPhotoThumbnailUrl(thumbnailPhotoUrl);
        return articleDAO.updateArticlePhoto(entity) ? new Result(SHErrorCode.SUCCESS) : new Result(SHErrorCode.UPDATE_FAIL);
    }


    @Override
    @Transactional
    public Result deleteArticle(String ea, Integer fsUserId, DeleteArticleArg vo) {
        if (null == ea) {
            return new Result<>(SHErrorCode.USER_NOT_BIND_EA);
        }
        ArticleEntity entity = articleDAO.queryArticleDetail(vo.getId());
        if (null != entity && null != entity.getStatus() && entity.getStatus() == 1) {//已启用的文章不能删除，请先停用!
            return new Result(SHErrorCode.DEL_ARTICLE_FAIL_TIP);
        }

        Integer userId = fsUserId;
        if (StringUtils.isNotEmpty(ea) && userId != null) {
            articleDAOManager.deleteArticle(ea,vo.getId());
        }
        customizeFormDataManager.unBindCustomizeFormDataObject(vo.getId(), ObjectTypeEnum.ARTICLE.getType(), ea);
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(ObjectTypeEnum.ARTICLE.getType(), vo.getId());
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.ARTICLE.getType(), Collections.singletonList(vo.getId()));
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), entity.getTitle(), OperateTypeEnum.DELETE);
        ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.ARTICLE.getType(), Lists.newArrayList(vo.getId()));
        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<ArticleListResult> listArticles(String fsEa, Integer fsUserId, ListArticleArg vo) {
        Date time = vo.getTime() == null ? null : new Date(vo.getTime());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        //List<ArticleEntityDTO> articleEntities = articleDAO.pageByEa(fsEa, time, vo.getStatus(), vo.getTitle(), page);
        List<ArticleEntityDTO> articleEntities = null;
        if (StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }
        ArticleQueryParam param = new ArticleQueryParam();
        param.setDate(time);
        param.setEa(fsEa);
        param.setStatus(vo.getStatus());
        param.setTitle(vo.getTitle());
        param.setMaterialTagFilter(vo.getMaterialTagFilter());
        if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            param.setUserId(fsUserId);
            articleEntities = articleDAO.createByMePage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            articleEntities = articleDAO.noGroupPage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(fsEa, fsUserId, ObjectTypeEnum.ARTICLE.getType());
            param.setUserId(fsUserId);
            param.setPermissionGroupIdList(Lists.newArrayList(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList())));
            articleEntities = articleDAO.getAccessiblePage(param, page);
        } else {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(fsEa, fsUserId, ObjectTypeEnum.ARTICLE.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            param.setUserId(fsUserId);
            if (!permissionGroupIdSet.contains(vo.getGroupId())) {
                articleEntities = Lists.newArrayList();
            } else {
                param.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(fsEa, ObjectTypeEnum.ARTICLE.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                param.setGroupIdList(accessibleSubGroupIdList);
                articleEntities = articleDAO.getAccessiblePage(param, page);
            }
        }
        //分页返回结果
        ArticleListResult articleListResult = new ArticleListResult();
        articleListResult.setCurrentPage(page.getPageNo());
        articleListResult.setPageSize(page.getPageSize());
        articleListResult.setRecordSize(page.getTotalNum());
        articleListResult.setPageTotal(page.getTotalPage() / page.getPageSize() + 1);
        articleListResult.setTime(vo.getTime());

        if (CollectionUtils.isEmpty(articleEntities)) {
            articleListResult.setArticleDetailResults(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, articleListResult);
        }

        List<String> objectIds = articleEntities.stream().map(ArticleEntityDTO::getId).collect(Collectors.toList());
        //物料平均访问深度
        ActionDurationTimeObjectArg durationTimeObjectArg = new ActionDurationTimeObjectArg();
        durationTimeObjectArg.setEa(fsEa);
        durationTimeObjectArg.setObjectType(ObjectTypeEnum.ARTICLE.getType());
        durationTimeObjectArg.setObjectIdList(objectIds);
        durationTimeObjectArg.setActionType(NewActionTypeEnum.LOOK_UP.getActionType());
        com.facishare.marketing.statistic.common.result.Result<List<ActionDurationTimeAvgByObjectResult>> listActionDurationTimeAvgListByObject = userMarketingStatisticService.listActionDurationTimeAvgListByObject(durationTimeObjectArg);
        Map<String, String> objectAvgTimeMap = Maps.newHashMap();
        Map<String, Integer> marketingObjectActivityLookUpDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(listActionDurationTimeAvgListByObject.getData())){
            objectAvgTimeMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId, o-> TimeMeasureUtil.getSecondTime(Long.valueOf(o.getAvg())),(v1, v2)->v2));
            marketingObjectActivityLookUpDTOMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId,ActionDurationTimeAvgByObjectResult::getCount,(v1, v2)->v2));
        }
        //物料获取的线索数
        List<CustomizeFormUserDataCountByObjectIdDTO> countByObjectIdDTOS = customizeFormDataUserDAO.queryObjectClueCount(objectIds, ObjectTypeEnum.ARTICLE.getType(), null);
        Map<String, Integer> countByObjectIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(countByObjectIdDTOS)){
            countByObjectIdMap = countByObjectIdDTOS.stream().collect(Collectors.toMap(CustomizeFormUserDataCountByObjectIdDTO::getObjectId,CustomizeFormUserDataCountByObjectIdDTO::getCount,(v1, v2)->v2));
        }

        //访问总数
        List<ObjectStatisticData> objectStatisticData = marketingObjectAmountStatisticDao.listStatisticData(fsEa, objectIds);
        Map<String, Integer> marketingObjectActivityAccessDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(objectStatisticData)) {
            marketingObjectActivityAccessDTOMap = objectStatisticData.stream().collect(Collectors.toMap(ObjectStatisticData::getObjectId, ObjectStatisticData :: getLookUpCount,(v1, v2)->v2));
        }

        //关联市场活动数
        Map<String, Integer> marketingEventCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectIds)) {
            List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntityList = contentMarketingEventMaterialRelationDAO
                    .getContentMarketingByEaAndObjectTypeAndObjectIds(fsEa, ObjectTypeEnum.ARTICLE.getType(), objectIds);
            if (CollectionUtils.isNotEmpty(contentMarketingEventMaterialRelationEntityList)) {
                List<ContentMarketingEventMaterialRelationEntity> filterDataList = filterInvalidMarketingEventData(fsEa, contentMarketingEventMaterialRelationEntityList);
                if (CollectionUtils.isNotEmpty(filterDataList)) {
                    filterDataList.forEach(data -> {
                        marketingEventCountMap.merge(data.getObjectId(), 1, (prev, one) -> prev + one);
                    });
                }
            }
        }

        articleEntities = photoManager.resetArticlePhotoUrlByDTO(articleEntities);
        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(objectIds, ObjectTypeEnum.ARTICLE.getType());

        //文章列表
        List<QueryArticleDetailResult> articleDetailResults = Lists.newArrayList();
        List<String> imageUrls = Lists.newArrayList();
        for (ArticleEntityDTO articleEntity : articleEntities) {
            QueryArticleDetailResult queryArticleDetailResult = BeanUtil.copy(articleEntity, QueryArticleDetailResult.class);
            queryArticleDetailResult.setPhotoThumbnailAPath(articleEntity.getPhotoThumbnailApath());
            queryArticleDetailResult.setPhotoUrl(pictureManager.checkArticleImg(queryArticleDetailResult.getPhotoUrl()));
            if (articleEntity.getPhotoThumbnailUrl() == null) {
                queryArticleDetailResult.setPhotoThumbnailUrl(queryArticleDetailResult.getPhotoUrl());
            } else {
                queryArticleDetailResult.setPhotoThumbnailUrl(articleEntity.getPhotoThumbnailUrl());
            }
            queryArticleDetailResult.setCreateTimeStamp(null == articleEntity.getCreateTime() ? null : articleEntity.getCreateTime().getTime());
            queryArticleDetailResult.setLastModifyTimeStamp(null == articleEntity.getLastModifyTime() ? null : articleEntity.getLastModifyTime().getTime());
            queryArticleDetailResult.setChoose(articleEntity.isChoose());
            queryArticleDetailResult.setTop(articleEntity.isTop());
            //平均访问时长
            if (objectAvgTimeMap == null || objectAvgTimeMap.get(queryArticleDetailResult.getId()) == null){
                queryArticleDetailResult.setActionDurationTimeAvg(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ARTICLESERVICEIMPL_703));
            }else {
                queryArticleDetailResult.setActionDurationTimeAvg(objectAvgTimeMap.get(queryArticleDetailResult.getId()));
            }
            //线索总数
            if (countByObjectIdMap == null || countByObjectIdMap.get(queryArticleDetailResult.getId()) == null){
                queryArticleDetailResult.setLeadCount(0);
            }else {
                queryArticleDetailResult.setLeadCount(countByObjectIdMap.get(queryArticleDetailResult.getId()));
            }
            //访问总数
            if (marketingObjectActivityAccessDTOMap == null || marketingObjectActivityAccessDTOMap.get(queryArticleDetailResult.getId()) == null){
                queryArticleDetailResult.setAccessCount(0);
            }else {
                queryArticleDetailResult.setAccessCount(marketingObjectActivityAccessDTOMap.get(queryArticleDetailResult.getId()));
            }

            //鸿曦说访问总数先改成以营销动态记录表为准
            //查看物料访问总数
            if (marketingObjectActivityLookUpDTOMap == null || marketingObjectActivityLookUpDTOMap.get(queryArticleDetailResult.getId()) == null){
                queryArticleDetailResult.setObjectLookUpCount(0);
            }else {
                queryArticleDetailResult.setObjectLookUpCount(marketingObjectActivityLookUpDTOMap.get(queryArticleDetailResult.getId()));
            }
            //关联市场活动总数
            if (marketingEventCountMap == null || marketingEventCountMap.get(queryArticleDetailResult.getId()) == null){
                queryArticleDetailResult.setMarketingEventCount(0);
            }else {
                queryArticleDetailResult.setMarketingEventCount(marketingEventCountMap.get(queryArticleDetailResult.getId()));
            }

            if (null != articleEntity.getFsUserId() && StringUtils.isNotBlank(articleEntity.getFsEa())) {
                queryArticleDetailResult.setBelong(ArticleUserTypeEnum.COMPANY.getType());
            }
            if (StringUtils.isNotBlank(articleEntity.getSummary())) {
                queryArticleDetailResult.setSummary(UnicodeFormatter.decodeUnicodeString(articleEntity.getSummary().replace("\\x0a", " ")));
            }
            if (StringUtils.isNotBlank(articleEntity.getTitle())) {
                queryArticleDetailResult.setTitle(UnicodeFormatter.decodeUnicodeString(articleEntity.getTitle()));
            }
            if(queryArticleDetailResult != null && StringUtils.isNotBlank(queryArticleDetailResult.getId())){
                // 获取裁剪封面图
                PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType(), queryArticleDetailResult.getId());
                if (coverCutMiniAppPhotoEntity != null) {
                    queryArticleDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType(), queryArticleDetailResult.getId());
                if (coverCutH5PhotoEntity != null) {
                    queryArticleDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType(), queryArticleDetailResult.getId());
                if (coverCutOrdinaryPhotoEntity != null) {
                    queryArticleDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                    //返回原图
                    queryArticleDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                }
            }
            if (StringUtils.isNotBlank(queryArticleDetailResult.getPhotoThumbnailAPath())) {
                imageUrls.add(queryArticleDetailResult.getPhotoThumbnailAPath());
            }

            // 内容标签处理
            List<String> materialTags = materialTagMap.get(articleEntity.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                queryArticleDetailResult.setMaterialTags(collect);
            }

            articleDetailResults.add(queryArticleDetailResult);
        }
        //多线程处理图片
        Map<String, Long> coverMap = fileV2Manager.processImageSizes(fsEa, imageUrls);
        articleDetailResults.forEach(data -> {
            if (StringUtils.isNotBlank(data.getPhotoThumbnailAPath()) && coverMap.containsKey(data.getPhotoThumbnailAPath())) {
                data.setCoverSize(coverMap.get(data.getPhotoThumbnailAPath()));
            }
        });
        articleListResult.setArticleDetailResults(articleDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, articleListResult);
    }

    private List<ContentMarketingEventMaterialRelationEntity> filterInvalidMarketingEventData(String ea, List<ContentMarketingEventMaterialRelationEntity> list) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<String> marketingIds = list.stream().map(ContentMarketingEventMaterialRelationEntity::getMarketingEventId).collect(Collectors.toList());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.addFilter("_id", marketingIds, OperatorConstants.IN);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> crmMarketingEventResult = crmV2Manager.getList(ea, -10000, MarketingEventFieldContants.API_NAME, searchQuery);
        if (crmMarketingEventResult == null || org.apache.commons.collections4.CollectionUtils.isEmpty(crmMarketingEventResult.getDataList())) {
            return Lists.newArrayList();
        }
        List<String> ids = crmMarketingEventResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        return list.stream().filter(data -> ids.contains(data.getMarketingEventId())).collect(Collectors.toList());
    }
    
    @Override
    public Result updateArticleStatus(String ea, Integer fsUserId, UpdateArticleArg vo) {
        int updateResult = articleDAO.updateArticleStatus(vo.getId(), vo.getStatus());
        if (updateResult != 1) {
            return new Result(SHErrorCode.UPDATE_FAIL);
        }
        // 若是删除文章需解除表单关联
        if (vo.getStatus().equals(ArticleStatusEnum.DELETE.getStatus())) {
            customizeFormDataManager.unBindCustomizeFormDataObject(vo.getId(), ObjectTypeEnum.ARTICLE.getType(), ea);
        }
        OperateTypeEnum operateTypeEnum = null;
        if (vo.getStatus().equals(ArticleStatusEnum.STOP_USING.getStatus())) {
            operateTypeEnum = OperateTypeEnum.SET_TO_STOP;
        } else if (vo.getStatus().equals(ArticleStatusEnum.START_USING.getStatus())) {
            operateTypeEnum = OperateTypeEnum.SET_TO_START;
        }
        if (operateTypeEnum != null) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), vo.getId(), operateTypeEnum);
        }
        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<AddCustomArticlesResult> addCustomArticles(AddCustomArticlesArg vo) {
        if(StringUtils.isBlank(vo.getId())){
            return doCreateCustomArticles(vo);
        }else{
            ArticleEntity articleEntity = articleDAO.getById(vo.getId());
            if(articleEntity != null){
                return doUpdateCustomArticles(vo, articleEntity);
            }else {
                return new Result(SHErrorCode.UPDATE_FAIL);
            }
        }
    }

    @Override
    public Result<QueryArticleDetailResult> queryArticleDetail(QueryArticleDetailArg vo) {
        ArticleEntity articleEntity = articleDAO.getById(vo.getArticleId());
        if(null == articleEntity){
            log.warn("ArticleServiceImpl.queryArticleDetail articleEntity can't search  vo:{}", vo);
            return new Result<>(SHErrorCode.NO_DATA);
        }

        QueryArticleDetailResult result = BeanUtil.copy(articleEntity, QueryArticleDetailResult.class);

        PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId(),articleEntity.getFsEa());
        if(null == photoEntity){
            log.warn("ArticleServiceImpl.queryArticleDetail coverPhoto can't search type:{}  id:{}", PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId());
            return new Result<>(SHErrorCode.NO_DATA);
        }
        result.setPhotoThumbnailUrl(photoEntity.getThumbnailUrl());
        result.setPhotoUrl(photoEntity.getUrl());
        result.setPhotoThumbnailAPath(photoEntity.getThumbnailPath());
        result.setCtaRelationInfos(Lists.newArrayList());
        if (StringUtils.isNotBlank(articleEntity.getSummary())) {
            result.setSummary(UnicodeFormatter.decodeUnicodeString(articleEntity.getSummary().replace("\\x0a", " ")));
        }

        if (StringUtils.isNotBlank(articleEntity.getTitle())) {
            result.setTitle(UnicodeFormatter.decodeUnicodeString(articleEntity.getTitle()));
        }

        if(StringUtils.isNotBlank(articleEntity.getArticlePath())){
            byte[] bytes = fileV2Manager.downloadAFile(articleEntity.getArticlePath(), null);
            String content = bytes == null ? "" : new String(bytes);
            result.setContent(content);
        }else{
            log.warn("ArticleServiceImpl.queryArticleDetail no articlePath  articleEntity:{}", articleEntity);
            return new Result<>(SHErrorCode.NO_DATA);
        }

        // 查询对应挂接表单
        CustomizeFormDataObjectEntity customizeFormDataObjectEntity =  customizeFormDataObjectDAO.getObjectBindingForm(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        if(customizeFormDataObjectEntity != null) {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataObjectEntity.getFormId());
            QueryArticleDetailResult.FormData formData = new QueryArticleDetailResult.FormData();
            formData.setFormId(customizeFormDataObjectEntity.getFormId());
            formData.setFormTitle(customizeFormDataEntity.getFormHeadSetting().getTitle());
            formData.setFormName(customizeFormDataEntity.getFormHeadSetting().getName());
            formData.setFormButtonName(customizeFormDataObjectEntity.getFormButtonName());
            formData.setFormStyleType(customizeFormDataObjectEntity.getFormStyleType());
            formData.setButtonStyle(customizeFormDataObjectEntity.getButtonStyle());
            result.setFormButtonName(customizeFormDataObjectEntity.getFormButtonName());
            result.setFormStyleType(customizeFormDataObjectEntity.getFormStyleType());
            result.setButtonStyle(customizeFormDataObjectEntity.getButtonStyle());
            result.setFormData(formData);
            result.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
        } else {
            // 查询对应挂接微页面   微页面和表单只能挂接一种
            HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if (hexagonSiteObjectEntity != null) {
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteManager.getBindHexagonSiteByObject(hexagonSiteObjectEntity.getSiteId());
                if (hexagonSiteEntity != null) {
                    QueryArticleDetailResult.HexagonSiteData hexagonSiteData = new QueryArticleDetailResult.HexagonSiteData();
                    hexagonSiteData.setSiteId(hexagonSiteEntity.getId());
                    hexagonSiteData.setSiteName(hexagonSiteEntity.getName());
                    result.setFormButtonName(hexagonSiteObjectEntity.getFormButtonName());
                    result.setFormStyleType(hexagonSiteObjectEntity.getFormStyleType());
                    result.setButtonStyle(hexagonSiteObjectEntity.getButtonStyle());
                    result.setHexagonSiteData(hexagonSiteData);
                    result.setBindObjectType(BindObjectType.HEXAGON_SITE.getType());
                }
            } else {
                ActivityBindObjectEntity activityBindObject = articleDAO.getBindObject(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
                if (activityBindObject != null) {
                    ProductEntity productEntity = productDAO.queryProductDetail(activityBindObject.getBindObjectId());
                    if (productEntity != null) {
                        result.setFormButtonName(activityBindObject.getButtonName());
                        result.setFormStyleType(activityBindObject.getStyleType());
                        result.setButtonStyle(activityBindObject.getButtonStyle());
                        result.setBindObjectId(productEntity.getId());
                        result.setBindObjectName(productEntity.getName());
                        result.setBindObjectType(BindObjectType.PRODUCT.getType());
                    }
                }
            }
        }

        ObjectTagEntity tagEntity = objectTagManager.queryObjectTag(articleEntity.getFsEa(), vo.getArticleId(), ObjectTypeEnum.ARTICLE.getType());
        if (tagEntity != null) {
            result.setTagNameList(tagEntity.getTagNameList());
        }
        MaterialRelationEntity relationEntity = materialRelationDao.queryMaterialRelationByObjectId(vo.getArticleId(), ObjectTypeEnum.ARTICLE.getType());
        if(relationEntity!=null&&relationEntity.getSharePosterAPath()!=null){
            result.setSharePosterAPath(relationEntity.getSharePosterAPath());
            result.setSharePosterUrl(fileV2Manager.getUrlByPath(relationEntity.getSharePosterAPath(),articleEntity.getFsEa(),false));
        }
        if(result != null && StringUtils.isNotBlank(result.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType(), result.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType(), result.getId());
            if (coverCutH5PhotoEntity != null) {
                result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType(), result.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }
        List<CtaRelationEntityDTO> ctaRelationList = ctaRelationDaoManager.getCtaRelationList(articleEntity.getFsEa(), ObjectTypeEnum.ARTICLE.getType(), Lists.newArrayList(vo.getArticleId()));
        if(CollectionUtils.isNotEmpty(ctaRelationList)){
            ctaRelationList.forEach(ctaRelationEntity -> {
                CtaRelationInfo ctaRelationInfo = new CtaRelationInfo();
                ctaRelationInfo.setCtaId(ctaRelationEntity.getCtaId());
                ctaRelationInfo.setCtaName(ctaRelationEntity.getCtaName());
                result.getCtaRelationInfos().add(ctaRelationInfo);
            });
        }
        result.setCreateSourceType(articleEntity.getCreateSourceType());
        SetSloganArg sloganArg = new SetSloganArg();
        sloganArg.setObjectId(articleEntity.getId());
        sloganArg.setObjectType(ObjectTypeEnum.ARTICLE.getType());
        sloganArg.setEa(articleEntity.getFsEa());
        Result<ObjectSloganResult> slogan = objectSloganRelationService.getSlogan(sloganArg);
        result.setSlogan(slogan.getData().getSlogan());
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PreviewArticleResult> previewArticle(PreviewArticleArg vo) {
        if(StringUtils.isBlank(vo.getPreviewArticleId())){
            return doCreatePreviewArticles(vo);
        } else {
            String valueJson = redisManager.getPreviewArticleTempValue(vo.getPreviewArticleId());
            if (StringUtils.isBlank(valueJson)) { //用户在预览界面停留超过了redis过期时间
                if (StringUtils.isNotBlank(vo.getTitle()) && StringUtils.isNotBlank(vo.getCreator()) && StringUtils.isNotBlank(vo.getContent())) {
                    return doCreatePreviewArticles(vo);
                } else {
                    log.warn("ArticleServiceImpl.previewArticle redis not have data PreviewArticleArg:{}", vo);
                    return new Result<>(SHErrorCode.NO_DATA);
                }
            } else {
                return doUpdatePreviewArticles(vo, valueJson);
            }

        }
    }

    @Override
    public Result updateWebCrawlerArticle(UpdateWebCrawlerArticleArg arg) {
        //更换封面
        Result updateResult = updateWebCrawlerArticlePhoto(arg.getId(), arg.getPhotoUrl());
        if(!updateResult.isSuccess()) {
            log.warn("ArticleServiceImpl.updateWebCrawlerArticle updateWebCrawlerArticlePhoto error arg:{}", arg);
            return updateResult;
        }
        // 修改文章摘要
        if (StringUtils.isNotBlank(arg.getSummary())) {
            articleDAO.updateArticleSummary(arg.getSummary(), arg.getId());
        }
        // 更新表单设置
        customizeFormDataManager.updateCustomizeFormDataObject(arg.getFormId(), arg.getId(), ObjectTypeEnum.ARTICLE.getType(), arg.getEa(), arg.getFsUserId(), arg.getFormStyleType(), arg.getFormButtonName(), arg.getButtonStyle());
        HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(arg.getEa(), arg.getId(), ObjectTypeEnum.ARTICLE.getType());
        if (hexagonSiteObjectEntity != null) { // 表单和微页面切换绑定需要先解绑另一个
            hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(hexagonSiteObjectEntity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
        }
        CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(arg.getEa(), arg.getId(), ObjectTypeEnum.ARTICLE.getType());
        if (oldCustomizeFormDataObjectEntity != null) {
            customizeFormDataObjectDAO.deleteCustomizeFormDataObject(arg.getEa(), oldCustomizeFormDataObjectEntity.getFormId(), arg.getId(), ObjectTypeEnum.ARTICLE.getType());
        }
        ActivityBindObjectEntity oldBindObject = articleDAO.getBindObject(arg.getEa(), arg.getId(), ObjectTypeEnum.PRODUCT.getType());
        if (oldBindObject != null) {
            articleDAO.unBindObject(arg.getEa(), oldBindObject.getId());
        }
        // 修改表单关联
        if (BindObjectType.CUSTOMIZE_FORM.getType().equals(arg.getBindObjectType())) {
            customizeFormDataManager.updateCustomizeFormDataObject(arg.getFormId(), arg.getId(), ObjectTypeEnum.ARTICLE.getType(), arg.getEa(), arg.getFsUserId(), arg.getFormStyleType(), arg.getFormButtonName(), arg.getButtonStyle());
        } else if (BindObjectType.HEXAGON_SITE.getType().equals(arg.getBindObjectType())) {
            // 修改微页面关联
            hexagonSiteManager.bindHexagonSiteObject(arg.getSiteId(), arg.getId(), ObjectTypeEnum.ARTICLE.getType(), arg.getEa(), arg.getFsUserId(), arg.getFormStyleType(), arg.getFormButtonName(), arg.getButtonStyle());
        } else if (BindObjectType.PRODUCT.getType().equals(arg.getBindObjectType())) {
            // 创建产品关联(沿用微页面的物料关联表)
            ActivityBindObjectEntity data = new ActivityBindObjectEntity();
            data.setId(UUIDUtil.getUUID());
            data.setEa(arg.getEa());
            data.setArticleId(arg.getId());
            data.setBindObjectId(arg.getBindObjectId());
            data.setBindObjectType(ObjectTypeEnum.PRODUCT.getType());
            data.setStyleType(arg.getFormStyleType());
            data.setButtonName(arg.getFormButtonName());
            data.setButtonStyle(arg.getButtonStyle());
            data.setCreateBy(arg.getFsUserId());
            data.setUpdateBy(arg.getFsUserId());
            articleDAO.bindObject(data);
        }
        ctaRelationDaoManager.addCtaRelation(arg.getEa(), arg.getCtaIds(), ObjectTypeEnum.ARTICLE.getType(), arg.getId());
        return new Result(SHErrorCode.SUCCESS);
    }


    private Result updateWebCrawlerArticlePhoto(String id, String photoUrl) {
        String thumbnailPhotoUrl = photoUrl;
        if (StringUtils.isNotEmpty(photoUrl) && photoUrl.startsWith(TEMP_A_WAREHOUSE_TYPE)) {
            FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(photoUrl, null, null);
            if (null == fileManagerPicResult) {
                log.warn("fileManagerPicResult is not found, tapath={}", photoUrl);
                return new Result(SHErrorCode.SYSTEM_ERROR);
            }
            photoUrl = fileManagerPicResult.getUrl();
            thumbnailPhotoUrl = fileManagerPicResult.getThumbUrl();
            photoManager.addOrUpdatePhotoByPhotoTargetType(null,PhotoTargetTypeEnum.ARTICLE_COVER, id, fileManagerPicResult.getUrlAPath(), fileManagerPicResult.getThumbUrlApath());

            Map<String, Object> normalImageParams = new HashMap<>();
            normalImageParams.put("coverApath", fileManagerPicResult.getUrlAPath());
            ImageDrawer normalImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
            String normalCoverApath = normalImageDrawer.draw(normalImageParams);
            if (StringUtils.isNotBlank(normalCoverApath)) {
                photoManager.addOrUpdatePhotoByPhotoTargetType(null,PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, id, normalCoverApath, normalCoverApath);
            }
            ArticleEntity articleEntity = new ArticleEntity();
            articleEntity.setId(id);
            articleEntity.setPhotoUrl(photoUrl);
            articleEntity.setPhotoThumbnailUrl(thumbnailPhotoUrl);
            return articleDAO.updateArticlePhoto(articleEntity) ? new Result(SHErrorCode.SUCCESS) : new Result(SHErrorCode.UPDATE_FAIL);
        } else {
            return new Result(SHErrorCode.SUCCESS);
        }
    }

    private Result<PreviewArticleResult> doCreatePreviewArticles(PreviewArticleArg vo){

        String previewArticleId = UUIDUtil.getUUID();
        PreviewArticleTempValue value = BeanUtil.copy(vo, PreviewArticleTempValue.class);
        String taPath = fileV2Manager.uploadToTApath(vo.getContent().getBytes(), ".html", null, null);
        if(StringUtils.isBlank(taPath)){
            log.warn("ArticleServiceImpl.doCreatePreviewArticles taPath is empty vo:{}", vo);
            return new Result<>(SHErrorCode.ADD_FAIL);
        }

        value.setContentTapath(taPath);
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("previewArticleId", previewArticleId);
        String valueJson = GsonUtil.getGson().toJson(valueMap, HashMap.class);

        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(QRCodeTypeEnum.PREVIEW_ARTICLE.getType());
        data.setValue(valueJson);
        data.setEa(vo.getEa());
        CreateQRCodeResult createQRCodeResult = qrCodeManager.createQRCode(data);
        if (createQRCodeResult == null) {
            log.warn("ArticleServiceImpl.doCreatePreviewArticles creator failed createQRCodeArg:{}", data);
            return new Result<>(SHErrorCode.PREVIEW_ARTICLE_CARD_QRURL_CREATE_FAIL);
        }
        value.setQrCodeUrl(createQRCodeResult.getQrCodeUrl());
        value.setFormId(vo.getFormId());
        try {
            redisManager.setPreviewArticleTempValue(previewArticleId, value);
        }catch (RuntimeException e){
            log.warn("ArticleServiceImpl.doUpdatePreviewArticles set previewArticle to redis failed  id:{}  value:{}", vo.getPreviewArticleId(), value, e);
            return new Result<>(SHErrorCode.ADD_FAIL);
        }

        PreviewArticleResult result = new PreviewArticleResult();
        result.setPreviewArticleId(previewArticleId);
        result.setQrCodeUrl(createQRCodeResult.getQrCodeUrl());
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    private Result<PreviewArticleResult> doUpdatePreviewArticles(PreviewArticleArg vo, String value){

        PreviewArticleTempValue previewArticleTempValue = gs.fromJson(value, PreviewArticleTempValue.class);
        if(null == previewArticleTempValue){
            log.warn("ArticleServiceImpl.doUpdatePreviewArticles previewArticleTempValue is null vo:{} value:{}", vo, value);
            return new Result<>(SHErrorCode.NO_DATA);
        }

        previewArticleTempValue.setSummary(vo.getSummary());
        previewArticleTempValue.setRecommendation(vo.getRecommendation());

        if(StringUtils.isNotBlank(vo.getTitle())){
            previewArticleTempValue.setTitle(vo.getTitle());
        }
        if(StringUtils.isNotBlank(vo.getCreator())){
            previewArticleTempValue.setCreator(vo.getCreator());
        }
        if (vo.getFormStyleType() != null) {
            previewArticleTempValue.setFormStyleType(vo.getFormStyleType());
        }
        if (StringUtils.isNotBlank(vo.getFormButtonName())) {
            previewArticleTempValue.setFormButtonName(vo.getFormButtonName());
        }
        if (vo.getButtonStyle() != null){
            previewArticleTempValue.setButtonStyle(vo.getButtonStyle());
        }

        if(vo.isUpdateContent() && StringUtils.isNotBlank(vo.getContent())){
            String taPath = fileV2Manager.uploadToTApath(vo.getContent().getBytes(), ".html", null, null);

            if(StringUtils.isBlank(taPath)){
                log.warn("ArticleServiceImpl.doUpdatePreviewArticles taPath is empty vo:{}", vo);
                return new Result<>(SHErrorCode.UPDATE_FAIL);
            }else{
                previewArticleTempValue.setContentTapath(taPath);
            }
        }
        previewArticleTempValue.setFormId(vo.getFormId());
        try {
            redisManager.setPreviewArticleTempValue(vo.getPreviewArticleId(), previewArticleTempValue);
        }catch (RuntimeException e){
            log.warn("ArticleServiceImpl.doUpdatePreviewArticles set previewArticle to redis failed  id:{}  value:{}", vo.getPreviewArticleId(), previewArticleTempValue, e);
            return new Result<>(SHErrorCode.UPDATE_FAIL);
        }

        PreviewArticleResult result = new PreviewArticleResult();
        result.setPreviewArticleId(vo.getPreviewArticleId());
        result.setQrCodeUrl(previewArticleTempValue.getQrCodeUrl());
        result.setFormId(vo.getFormId());
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    private Result<AddCustomArticlesResult> doUpdateCustomArticles(AddCustomArticlesArg vo, ArticleEntity articleEntity){
        //文章不能重名
        if (!StringUtils.equals(articleEntity.getTitle(), vo.getTitle())) {
            int count = articleDAO.queryArticleCountByName(vo.getFsEa(), vo.getTitle());
            if (count > 0) {
                return Result.newError(SHErrorCode.ARTICLE_NAME_EXIST);
            }
        }

        // 更新封面
        if(vo.getCoverTapath().startsWith("TA_") || vo.getCoverTapath().startsWith("A_") || vo.getCoverTapath().startsWith("C_")){
            //如果是apath
            FileManagerPicResult coverPicResult = new FileManagerPicResult();
            coverPicResult.setUrlAPath(vo.getCoverTapath());
            coverPicResult.setThumbUrlApath(vo.getCoverTapath());
            //是tapath
            if(vo.getCoverTapath().startsWith("TA_")) {
                coverPicResult = fileV2Manager.getApathByTApath(vo.getCoverTapath(), vo.getFsEa(), null);
                if (coverPicResult == null) {
                    log.warn("ArticleServiceImpl.doUpdateCustomArticles get coverPicResult failed tapath():{} ea:{}", vo.getCoverTapath(), vo.getFsEa());
                    return new Result(SHErrorCode.UPDATE_COVER_FAIL);
                }
            }
            Map<String, Object> normalImageParams = new HashMap<>();
            normalImageParams.put("coverApath", coverPicResult.getUrlAPath());
            normalImageParams.put("ea", vo.getFsEa());
            ImageDrawer normalImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
            String miniCoverApath = normalImageDrawer.draw(normalImageParams);

            /*Map<String, Object> lmImageParams = new HashMap<>();
            lmImageParams.put("coverApath", coverPicResult.getUrlAPath());
            ImageDrawer lmImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.LuckyMoneyIconCover);
            String luckyCoverApath = lmImageDrawer.draw(lmImageParams);*/
            if(StringUtils.isBlank(miniCoverApath)) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles get coverApath failed UrlAPath:{}", coverPicResult.getUrlAPath());
                return new Result(SHErrorCode.UPDATE_COVER_FAIL);
            }
            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(articleEntity.getFsEa(),PhotoTargetTypeEnum.ARTICLE_COVER, articleEntity.getId(), coverPicResult.getUrlAPath(), coverPicResult.getThumbUrlApath())) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed articleId:{} coverPicResult_origin:{}", articleEntity.getId(), coverPicResult);
                return new Result(SHErrorCode.UPDATE_COVER_FAIL);
            }
            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(articleEntity.getFsEa(),PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, articleEntity.getId(), miniCoverApath, miniCoverApath)) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed articleId:{} miniCoverApath:{}", articleEntity.getId(), miniCoverApath);
                return new Result(SHErrorCode.UPDATE_COVER_FAIL);
            }
            /*if (!photoManager.addOrUpdatePhotoByPhotoTargetType(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM, articleEntity.getId(), luckyCoverApath, luckyCoverApath)) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed articleId:{} luckyCoverApath:{}", articleEntity.getId(), luckyCoverApath);
                return new Result(SHErrorCode.UPDATE_COVER_FAIL);
            }*/
        }
        // 更新文章
        if(vo.isUpdateContent()){
            String articlePath = getArticlePath(vo.getContent(), false);
            if(StringUtils.isBlank(articlePath)){
                log.warn("ArticleServiceImpl.doUpdateCustomArticles get articlePath failed content:{}", vo.getContent());
                return new Result(SHErrorCode.UPDATE_FAIL);
            }
            articleEntity.setArticlePath(articlePath);
        }
        articleEntity.setCreator(vo.getCreator());
        articleEntity.setContent(vo.getContent());
        articleEntity.setTitle(vo.getTitle());
        articleEntity.setRecommendation(vo.getRecommendation());
        articleEntity.setSummary(vo.getSummary());
        articleEntity.setPreArticleContent(vo.getPreArticleContent());
        if(null != vo.getStatus() && Arrays.stream(ArticleStatusEnum.values()).anyMatch(data -> data.getStatus().equals(vo.getStatus()))) {
            articleEntity.setStatus(vo.getStatus());
        }
        if(!articleDAOManager.updateArticle(articleEntity)){
            log.warn("ArticleServiceImpl.doUpdateCustomArticles update articleEntity failed entity:{}", articleEntity);
            return new Result(SHErrorCode.UPDATE_FAIL);
        }
        materialRelationDaoManager.insertOrUpdateMaterialRelationEntity(articleEntity.getId(),ObjectTypeEnum.ARTICLE.getType(),vo.getSharePosterAPath(),vo.getFsEa());
        HexagonSiteObjectEntity entity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(vo.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        if (entity != null) { // 表单和微页面切换绑定需要先解绑另一个
            hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(entity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
        }
        CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(vo.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        if (oldCustomizeFormDataObjectEntity != null) {
            customizeFormDataObjectDAO.deleteCustomizeFormDataObject(vo.getFsEa(), oldCustomizeFormDataObjectEntity.getFormId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        }
        ActivityBindObjectEntity oldBindObject = articleDAO.getBindObject(vo.getFsEa(), articleEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
        if (oldBindObject != null) {
            articleDAO.unBindObject(vo.getFsEa(), oldBindObject.getId());
        }
        // 修改表单关联
        if (BindObjectType.CUSTOMIZE_FORM.getType().equals(vo.getBindObjectType())) {
            customizeFormDataManager.updateCustomizeFormDataObject(vo.getFormId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(), vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
        } else if (BindObjectType.HEXAGON_SITE.getType().equals(vo.getBindObjectType())) {
            // 修改微页面关联
            hexagonSiteManager.bindHexagonSiteObject(vo.getSiteId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(), vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
        } else if (BindObjectType.PRODUCT.getType().equals(vo.getBindObjectType())) {
            // 创建产品关联(沿用微页面的物料关联表)
            ActivityBindObjectEntity data = new ActivityBindObjectEntity();
            data.setId(UUIDUtil.getUUID());
            data.setEa(vo.getFsEa());
            data.setArticleId(articleEntity.getId());
            data.setBindObjectId(vo.getBindObjectId());
            data.setBindObjectType(ObjectTypeEnum.PRODUCT.getType());
            data.setStyleType(vo.getFormStyleType());
            data.setButtonName(vo.getFormButtonName());
            data.setButtonStyle(vo.getButtonStyle());
            data.setCreateBy(vo.getFsUserId());
            data.setUpdateBy(vo.getFsUserId());
            articleDAO.bindObject(data);
        }

        objectTagManager.addOrUpdateObjectTag(vo.getFsEa(), vo.getFsUserId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getTagNameList());
        //预加载文章
        String articleId = articleEntity.getId();
        String content = articleEntity.getContent();
        preLoadArticle(articleId, content);

        AddCustomArticlesResult addCustomArticlesResult = new AddCustomArticlesResult();
        addCustomArticlesResult.setId(articleEntity.getId());
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(vo.getFsEa(), vo.getFsUserId(), ObjectTypeEnum.ARTICLE.getType(), articleEntity.getTitle(), OperateTypeEnum.EDIT);
        if(StringUtils.isNotBlank(vo.getOriginalImageAPath()) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
            for (PhotoCutOffset cutOffset:vo.getCutOffsetList()){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER, articleEntity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER, articleEntity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER, articleEntity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
            }
        }
        ctaRelationDaoManager.addCtaRelation(vo.getFsEa(), vo.getCtaIds(),ObjectTypeEnum.ARTICLE.getType(), articleEntity.getId());
        return Result.newSuccess(addCustomArticlesResult);
    }
    private Result<AddCustomArticlesResult> doCreateCustomArticles(AddCustomArticlesArg vo){
        ArticleEntity articleEntity = BeanUtil.copy(vo, ArticleEntity.class);
        String id = UUIDUtil.getUUID();
        //文章不能重名
        int count = articleDAO.queryArticleCountByName(vo.getFsEa(), vo.getTitle());
        if (count > 0){
            return Result.newError(SHErrorCode.ARTICLE_NAME_EXIST);
        }

        // 添加封面
        if (vo.getCoverTapath().startsWith("TA_") || vo.getCoverTapath().startsWith("A_") || vo.getCoverTapath().startsWith("C_")) {
            //如果是apath
            FileManagerPicResult coverPicResult = new FileManagerPicResult();
            coverPicResult.setUrlAPath(vo.getCoverTapath());
            coverPicResult.setThumbUrlApath(vo.getCoverTapath());
            //是tapath
            if(vo.getCoverTapath().startsWith("TA_")){
                coverPicResult =  fileV2Manager.getApathByTApath(vo.getCoverTapath(), vo.getFsEa(), null);
                if (coverPicResult == null) {
                    log.warn("ArticleServiceImpl.doCreateCustomArticles get coverPicResult failed tapath():{} ea:{}", vo.getCoverTapath(), vo.getFsEa());
                    return new Result(SHErrorCode.ADD_FAIL);
                }
            }

            Map<String, Object> normalImageParams = new HashMap<>();
            normalImageParams.put("coverApath", coverPicResult.getUrlAPath());
            normalImageParams.put("ea", vo.getFsEa());
            ImageDrawer normalImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
            String miniCoverApath = normalImageDrawer.draw(normalImageParams);

            /*Map<String, Object> lmImageParams = new HashMap<>();
            lmImageParams.put("coverApath", coverPicResult.getUrlAPath());
            ImageDrawer lmImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.LuckyMoneyIconCover);
            String luckyCoverApath = lmImageDrawer.draw(lmImageParams);*/
            if(StringUtils.isBlank(miniCoverApath)) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles get coverApath failed UrlAPath:{}", coverPicResult.getUrlAPath());
                return new Result(SHErrorCode.ADD_FAIL);
            }
            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_COVER, id, coverPicResult.getUrlAPath(), coverPicResult.getThumbUrlApath())) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed articleId:{} coverPicResult_origin:{}", id, coverPicResult);
                return new Result(SHErrorCode.ADD_FAIL);
            }
            if (!photoManager.addOrUpdatePhotoByPhotoTargetType(vo.getFsEa(),PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, id, miniCoverApath, miniCoverApath)) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed articleId:{} miniCoverApath:{}", id, miniCoverApath);
                return new Result(SHErrorCode.ADD_FAIL);
            }
            /*if (!photoManager.addOrUpdatePhotoByPhotoTargetType(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM, id, luckyCoverApath, luckyCoverApath)) {
                log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed articleId:{} luckyCoverApath:{}", id, luckyCoverApath);
                return new Result(SHErrorCode.ADD_FAIL);
            }*/
        } else {
            log.warn("ArticleServiceImpl.doCreateCustomArticles addPhoto failed path is no tapath coverApath:{}", vo.getCoverTapath());
            return new Result(SHErrorCode.PARAMS_ERROR);
        }
        // 添加文章
        String articlePath = getArticlePath(vo.getContent(), false);
        if(StringUtils.isBlank(articlePath)){
            log.warn("ArticleServiceImpl.doCreateCustomArticles get articlePath failed content:{}", vo.getContent());
            return new Result(SHErrorCode.ADD_FAIL);
        }

        articleEntity.setArticlePath(articlePath);
        articleEntity.setArticleType(ArticleTypeEnum.ORIGINAL.getType());
        articleEntity.setId(id);
        articleEntity.setSourceType(ArticleSourceTypeEnum.CUSTOM.getSourceType());
        articleEntity.setUrl(host + "/customArticle/" + id);
        if(null == vo.getStatus()){
            articleEntity.setStatus(ArticleStatusEnum.START_USING.getStatus());
        }
        articleEntity.setUid("");
        articleEntity.setCreateSourceType(ArticleCreateSourceTypeEnum.WEB.getType());
        if(!articleDAOManager.addArticle(articleEntity)){
            log.warn("ArticleServiceImpl.doCreateCustomArticles add articleEntity failed entity:{}", articleEntity);
            return new Result(SHErrorCode.ADD_FAIL);
        }

        //上报神策系统:营销物料实例
        marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(vo.getFsEa(), vo.getFsUserId() ,ObjectTypeEnum.ARTICLE.getType(), ArticleTypeEnum.ORIGINAL.getType(), articleEntity.getId()));

        HexagonSiteObjectEntity entity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(vo.getFsEa(), id, ObjectTypeEnum.PRODUCT.getType());
        if (entity != null) { // 表单和微页面切换绑定需要先解绑另一个
            hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(entity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
        }
        CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(vo.getFsEa(), id, ObjectTypeEnum.PRODUCT.getType());
        if (oldCustomizeFormDataObjectEntity != null) {
            customizeFormDataObjectDAO.deleteCustomizeFormDataObject(vo.getFsEa(), oldCustomizeFormDataObjectEntity.getFormId(), id, ObjectTypeEnum.PRODUCT.getType());
        }
        ActivityBindObjectEntity oldBindObject = articleDAO.getBindObject(vo.getFsEa(), id, ObjectTypeEnum.PRODUCT.getType());
        if (oldBindObject != null) {
            articleDAO.unBindObject(vo.getFsEa(), oldBindObject.getId());
        }
        // 创建表单关联
        if (StringUtils.isNotBlank(vo.getFormId()) && BindObjectType.CUSTOMIZE_FORM.getType().equals(vo.getBindObjectType())) {
            customizeFormDataManager.bindCustomizeFormDataObject(vo.getFormId(), id, ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(),vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
        } else if (BindObjectType.HEXAGON_SITE.getType().equals(vo.getBindObjectType())) {
            // 创建微页面关联
            hexagonSiteManager.bindHexagonSiteObject(vo.getSiteId(), id, ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(), vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
        } else if (BindObjectType.PRODUCT.getType().equals(vo.getBindObjectType())) {
            // 创建产品关联(沿用微页面的物料关联表)
            ActivityBindObjectEntity data = new ActivityBindObjectEntity();
            data.setId(UUIDUtil.getUUID());
            data.setEa(vo.getFsEa());
            data.setArticleId(id);
            data.setBindObjectId(vo.getBindObjectId());
            data.setBindObjectType(ObjectTypeEnum.PRODUCT.getType());
            data.setStyleType(vo.getFormStyleType());
            data.setButtonName(vo.getFormButtonName());
            data.setButtonStyle(vo.getButtonStyle());
            data.setCreateBy(vo.getFsUserId());
            data.setUpdateBy(vo.getFsUserId());
            articleDAO.bindObject(data);
        }

        if (vo.getTagNameList() != null) {
            objectTagManager.addOrUpdateObjectTag(vo.getFsEa(), vo.getFsUserId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getTagNameList());
        }

        //预加载文章
        String articleId = articleEntity.getId();
        String content = articleEntity.getContent();
        preLoadArticle(articleId, content);

        AddCustomArticlesResult addCustomArticlesResult = new AddCustomArticlesResult();
        addCustomArticlesResult.setId(id);
        if(StringUtils.isNotBlank(vo.getOriginalImageAPath()) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
            for (PhotoCutOffset cutOffset:vo.getCutOffsetList()){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER, articleEntity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER, articleEntity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER, articleEntity.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
            }
        }
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(vo.getFsEa(), vo.getFsUserId(), ObjectTypeEnum.ARTICLE.getType(), articleEntity.getTitle(), OperateTypeEnum.ADD);
        // 设置分组
        objectGroupManager.setGroup(vo.getFsEa(), vo.getFsUserId(), ObjectTypeEnum.ARTICLE.getType(), Collections.singletonList(id), vo.getGroupId());
        ctaRelationDaoManager.addCtaRelation(vo.getFsEa(), vo.getCtaIds(), ObjectTypeEnum.ARTICLE.getType(), articleEntity.getId());
        return Result.newSuccess(addCustomArticlesResult);
    }

    private ArticleEntity createArticleEntity(AddArticleArg vo) {
        ArticleEntity articleEntity = BeanUtil.copy(vo, ArticleEntity.class);
        String id = UUIDUtil.getUUID();

        // 添加文章
        String articlePath = getArticlePath(vo.getContent(), false);
        if(StringUtils.isBlank(articlePath)){
            log.warn("ArticleServiceImpl.createArticleEntity get articlePath failed content:{}", vo.getContent());
            return null;
        }
        articleEntity.setTitle(UnicodeFormatter.decodeUnicodeString(vo.getTitle()));
        articleEntity.setSummary(UnicodeFormatter.decodeUnicodeString(vo.getSummary()));
        articleEntity.setRecommendation(UnicodeFormatter.decodeUnicodeString(vo.getRecommendation()));

        articleEntity.setArticlePath(articlePath);
        articleEntity.setArticleType(ArticleTypeEnum.REPRINT.getType());
        articleEntity.setId(id);
        articleEntity.setSourceType(ArticleSourceTypeEnum.PUBLIC_ADDRESS.getSourceType());
        articleEntity.setUrl(vo.getUrl());
        articleEntity.setUid("");
        if(null == vo.getStatus()){
            articleEntity.setStatus(ArticleStatusEnum.START_USING.getStatus());
        }
        articleEntity = addOrUpdateArticlePhoto(vo, articleEntity);
        return articleEntity;
    }

    private ArticleEntity updateArticleEntity(AddArticleArg vo, ArticleEntity articleEntity){
        if (vo == null || articleEntity == null) {
            return null;
        }
        if(null != vo.getStatus() && Arrays.stream(ArticleStatusEnum.values()).noneMatch(data -> data.getStatus().equals(vo.getStatus()))) {
            return null;
        }
        // 更新封面
        if(vo.isUpdatePhoto()){
            articleEntity = addOrUpdateArticlePhoto(vo, articleEntity);
            if (articleEntity == null) {
                log.warn("ArticleServiceImpl.updateArticleEntity addOrUpdateArticlePhoto failed vo:{}, articleEntity:{}", vo, articleEntity);
                return null;
            }
        }
        // 更新文章
        if(vo.isUpdateContent()){
            String articlePath = getArticlePath(vo.getContent(), false);
            if(StringUtils.isBlank(articlePath)){
                log.warn("ArticleServiceImpl.updateArticleEntity get articlePath failed content:{}", vo.getContent());
                return null;
            }
            articleEntity.setArticlePath(articlePath);
        }
        articleEntity.setCreator(vo.getCreator());
        articleEntity.setContent(vo.getContent());
        articleEntity.setTitle(UnicodeFormatter.decodeUnicodeString(vo.getTitle()));
        articleEntity.setRecommendation(UnicodeFormatter.decodeUnicodeString(vo.getRecommendation()));
        articleEntity.setSummary(UnicodeFormatter.decodeUnicodeString(vo.getSummary()));
        articleEntity.setSource(vo.getSource());
        if (vo.getStatus() != null) {
            articleEntity.setStatus(vo.getStatus());
        }

        if(!articleDAOManager.updateArticle(articleEntity)){
            log.warn("ArticleServiceImpl.updateArticleEntity update articleEntity failed entity:{}", articleEntity);
            return null;
        }
        HexagonSiteObjectEntity entity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(vo.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        if (entity != null) { // 表单和微页面切换绑定需要先解绑另一个
            hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(entity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
        }
        CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(vo.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        if (oldCustomizeFormDataObjectEntity != null) {
            customizeFormDataObjectDAO.deleteCustomizeFormDataObject(vo.getFsEa(), oldCustomizeFormDataObjectEntity.getFormId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        }
        ActivityBindObjectEntity oldBindObject = articleDAO.getBindObject(vo.getFsEa(), articleEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
        if (oldBindObject != null) {
            articleDAO.unBindObject(vo.getFsEa(), oldBindObject.getId());
        }
        // 修改表单关联
        if (BindObjectType.CUSTOMIZE_FORM.getType().equals(vo.getBindObjectType())) {
            customizeFormDataManager.updateCustomizeFormDataObject(vo.getFormId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(), vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
        } else if (BindObjectType.HEXAGON_SITE.getType().equals(vo.getBindObjectType())) {
            // 修改微页面关联
            hexagonSiteManager.bindHexagonSiteObject(vo.getSiteId(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType(), vo.getFsEa(), vo.getFsUserId(), vo.getFormStyleType(), vo.getFormButtonName(), vo.getButtonStyle());
        } else if (BindObjectType.PRODUCT.getType().equals(vo.getBindObjectType())) {
            // 创建产品关联(沿用微页面的物料关联表)
            ActivityBindObjectEntity data = new ActivityBindObjectEntity();
            data.setId(UUIDUtil.getUUID());
            data.setEa(vo.getFsEa());
            data.setArticleId(articleEntity.getId());
            data.setBindObjectId(vo.getBindObjectId());
            data.setBindObjectType(ObjectTypeEnum.PRODUCT.getType());
            data.setStyleType(vo.getFormStyleType());
            data.setButtonName(vo.getFormButtonName());
            data.setButtonStyle(vo.getButtonStyle());
            data.setCreateBy(vo.getFsUserId());
            data.setUpdateBy(vo.getFsUserId());
            articleDAO.bindObject(data);
        }

        return articleEntity;
    }

    ArticleEntity addOrUpdateArticlePhoto(AddArticleArg vo, ArticleEntity articleEntity) {
        // 添加封面
        String photoApath = null;
        String thumbApath = null;
        if (vo.getCoverTapath().startsWith("TA_")) {
            FileManagerPicResult coverPicResult = fileV2Manager.getApathByTApath(vo.getCoverTapath(), null, null);
            if (coverPicResult == null) {
                log.warn("ArticleServiceImpl.addOrUpdateArticlePhoto get coverPicResult failed tapath():{} ea:{}", vo.getCoverTapath(), vo.getFsEa());
                return null;
            }
            photoApath = coverPicResult.getUrlAPath();
            thumbApath = coverPicResult.getThumbUrlApath();
        } else if (vo.getCoverTapath().startsWith("A_")) {
            photoApath = vo.getCoverTapath();
            thumbApath = vo.getThumbApath();
        } else {
            photoApath = vo.getCoverTapath();
            thumbApath = vo.getThumbApath();
        }

        if (StringUtils.isBlank(thumbApath)) {
            thumbApath = photoApath;
        }

        Map<String, Object> normalImageParams = new HashMap<>();
        normalImageParams.put("coverApath", photoApath);
        normalImageParams.put("ea", vo.getFsEa());
        ImageDrawer normalImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
        String miniCoverApath = normalImageDrawer.draw(normalImageParams);

        /*Map<String, Object> lmImageParams = new HashMap<>();
        lmImageParams.put("coverApath", photoApath);
        ImageDrawer lmImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.LuckyMoneyIconCover);
        String luckyCoverApath = lmImageDrawer.draw(lmImageParams);*/

        if(StringUtils.isBlank(miniCoverApath)) {
            log.warn("ArticleServiceImpl.addOrUpdateArticlePhoto get coverApath failed photoApath:{}", photoApath);
            return null;
        }
        if (!photoManager.addOrUpdatePhotoByPhotoTargetType(articleEntity.getFsEa(),PhotoTargetTypeEnum.ARTICLE_COVER, articleEntity.getId(), photoApath, thumbApath)) {
            log.warn("ArticleServiceImpl.addOrUpdateArticlePhoto addPhoto failed articleId:{} photoApath:{}", articleEntity.getId(), vo.getCoverTapath());
            return null;
        }
        if (!photoManager.addOrUpdatePhotoByPhotoTargetType(articleEntity.getFsEa(),PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, articleEntity.getId(), miniCoverApath, miniCoverApath)) {
            log.warn("ArticleServiceImpl.addOrUpdateArticlePhoto addPhoto failed articleId:{} miniCoverApath:{}", articleEntity.getId(), miniCoverApath);
            return null;
        }
        /*if (!photoManager.addOrUpdatePhotoByPhotoTargetType(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM, articleEntity.getId(), luckyCoverApath, luckyCoverApath)) {
            log.warn("ArticleServiceImpl.addOrUpdateArticlePhoto addPhoto failed articleId:{} luckyCoverApath:{}", articleEntity.getId(), luckyCoverApath);
            return null;
        }*/
        return articleEntity;
    }

    public ArticleEntity setArticleEntity(AddArticleArg vo) {
        try {
            long beginTime = System.currentTimeMillis();
            InitWebCrawlerArticleResult articleContent = crawlerArticleContent(vo.getUrl().trim());
            long endTime = System.currentTimeMillis();
            articleStatisticsUtil.totalSpendTime(endTime - beginTime);
            if (articleContent == null) {
                return null;
            }
            articleStatisticsUtil.articleResolveNum();
            if (vo.getInitArticle() != null && vo.getInitArticle() && StringUtils.isNotBlank(articleContent.getWebPage())) {
                if (host.contains("ceshi112")) {
                    articleContent.setWebPage(ArticleConstants.INIT_ARTICLE_CONTANTS_112);
                } else if (host.contains("fxiaoke")) {
                    articleContent.setWebPage(ArticleConstants.INIT_ARTICLE_CONTANTS_ONLINE);
                }
            }
            ArticleEntity entity = new ArticleEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setUrl(vo.getUrl().trim());
            entity.setContent(articleContent.getWebPage());
            entity.setPhotoUrl(articleContent.getPhotoUrl());
            entity.setPhotoThumbnailUrl(articleContent.getThumbUrl());
            entity.setCardPhotoUrl(articleContent.getCardPhotoUrl());
            entity.setPhotoApath(articleContent.getPhotoApath());
            entity.setPhotoThumbnailApath(articleContent.getThumbApath());
            photoManager.savePhotoByAapath(vo.getFsEa(),PhotoTargetTypeEnum.ARTICLE_COVER, entity.getId(), articleContent.getPhotoApath(), articleContent.getThumbApath());
            photoManager.savePhotoByAapath(vo.getFsEa(),PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, entity.getId(), articleContent.getCardPhotoApath(), articleContent.getCardPhotoApath());

            entity.setTitle(UnicodeFormatter.decodeUnicodeString(articleContent.getTitle()));
            entity.setCreator(articleContent.getCreator());
            entity.setSource(articleContent.getSource());
            //1公众号
            entity.setSourceType(1);
            entity.setArticleType(ArticleTypeEnum.REPRINT.getType());
            //文章路径
            entity.setArticlePath(getArticlePath(articleContent.getWebPage(), false));
            // 状态 -1:未启用 1:已启用 4:已停用
            if (vo.getStatus() != null) {
                entity.setStatus(vo.getStatus());
            } else {
                entity.setStatus(1);
            }
            entity.setSummary(UnicodeFormatter.decodeUnicodeString(articleContent.getSummary()));
            entity.setCreateTime(new Date());
            entity.setPictureJson(null);
            entity.setRecommendation(UnicodeFormatter.decodeUnicodeString(vo.getRecommendation()));

            return entity;
        } catch (Exception e) {
            articleStatisticsUtil.articleFailResolveNum();
            log.error("Error ", e);
            return null;
        } finally {
            articleStatisticsUtil.articleDownloadNum();
        }
    }

    public String getArticlePath(String content, boolean tempFile) {
        byte[] bytes = content.getBytes();
        if (!tempFile) {
            return fileManager.uploadFileToAWarehouse(WebCrawlerUtil.EXT, bytes);
        } else {
            return fileManager.uploadTmpToAWarehouse(bytes, ea);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<EditObjectGroupResult> editArticleGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("ArticleServiceImpl.editArticleGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("ArticleServiceImpl.editArticleGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.ARTICLE.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteArticleGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("ArticleServiceImpl.deleteArticleGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.ARTICLE.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setArticleGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
        List<ArticleEntity> articleEntityList = articleDAO.getByIds(arg.getObjectIdList());
        if (CollectionUtils.isEmpty(articleEntityList)) {
            return Result.newError(SHErrorCode.ARTICLE_NOT_EXIST);
        }
        if (articleEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_COUPON_TEMPLATE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.ARTICLE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.ARTICLE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        for (ArticleEntity articleEntity : articleEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), articleEntity.getTitle(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteArticleBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        List<ArticleEntity> entityList = articleDAO.getByIds(arg.getIdList());
        if (CollectionUtils.isEmpty(entityList)) {
            return new Result<>(SHErrorCode.ARTICLE_NOT_EXIST);
        }

        if (entityList.stream().anyMatch(e -> null != e.getStatus() && e.getStatus() == 1)) {
            //已启用的文章不能删除，请先停用!
            return new Result<>(SHErrorCode.DEL_ARTICLE_FAIL_TIP);
        }
        if (StringUtils.isBlank(ea)) {
            return new Result<>(SHErrorCode.USER_NOT_BIND_EA);
        }
        if (fsUserId != null) {
            //articleDAOManager.deleteArticle(ea, articleEntity.getId());
            articleDAOManager.deleteArticleBatch(ea, entityList.stream().map(ArticleEntity::getId).collect(Collectors.toList()));
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.ARTICLE.getType(), arg.getIdList());
        customizeFormDataManager.unBindCustomizeFormDataObjectBatch(arg.getIdList(), ObjectTypeEnum.ARTICLE.getType(), ea);
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(ObjectTypeEnum.ARTICLE.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.ARTICLE.getType(), arg.getIdList());
        for (ArticleEntity articleEntity : entityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), articleEntity.getTitle(), OperateTypeEnum.DELETE);
        }
        ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.ARTICLE.getType(), arg.getIdList());
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topArticle(String ea, Integer fsUserId, TopMaterialArg arg) {
        ArticleEntity entity = articleDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.ARTICLE_NOT_EXIST);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.ARTICLE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopArticle(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.ARTICLE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addArticleGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.ARTICLE.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listArticleGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.ARTICLE.getType(), arg.getMenuId(), null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(articleDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId, null));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(articleDAO.queryAccessibleCount(ea, groupIdList, fsUserId, null));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(articleDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(articleDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);

        return Result.newSuccess(vo);
    }

    @Override
    public Result<ObjectGroupListResult> listArticleGroup4Outer(String upstreamEA, String outTenantId, String outUserId, ListGroupArg arg) {
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup4Outer(upstreamEA, outTenantId, outUserId, ObjectTypeEnum.ARTICLE.getType());
        return Result.newSuccess(customizeGroupListVO);
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }
}