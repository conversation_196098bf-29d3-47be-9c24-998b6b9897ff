package com.facishare.marketing.provider.manager;

import com.alibaba.fastjson.JSONArray;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendGroupResultDAO;
import com.facishare.marketing.provider.dao.qywx.QywxGroupSendResultDAO;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupFlatResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendGroupResultEntity;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendResultEntity;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AllArgsConstructor
@Slf4j
public class MigrateManager {

    private final QywxGroupSendResultDAO qywxGroupSendResultDAO;
    private final QywxGroupSendGroupResultDAO qywxGroupSendGroupResultDAO;

    /**
     * 更新sendid
     *
     * @param ea
     */
    public void batchInsertSendId(String ea) {
        Page page = new Page<>(1, 1, true);
        qywxGroupSendResultDAO.queryPageByEa(ea, page);
        int totlePage = (page.getTotalNum() - 1) / 100 + 1;
        for (int pageNo = 1; pageNo <= totlePage; pageNo++) {
            page = new Page<>(pageNo, 500);
            List<QywxGroupSendResultEntity> entities = qywxGroupSendResultDAO.queryPageByEa(ea, page);
            if (CollectionUtils.isNotEmpty(entities)) {
                qywxGroupSendResultDAO.batchUpdateSendId(entities);
            }
        }
    }

    /**
     * 扁平化结果表
     *
     * @param ea
     */
    public void batchInsertFlatResult(String ea) {
        Page page = new Page<>(1, 1, true);
        qywxGroupSendGroupResultDAO.queryPageByEa(ea, page);
        int totlePage = (page.getTotalNum() - 1) / 100 + 1;
        for (int pageNo = 1; pageNo <= totlePage; pageNo++) {
            page = new Page<>(pageNo, 500);
            List<QywxGroupSendGroupResultEntity> entities = qywxGroupSendGroupResultDAO.queryPageByEa(ea, page);
            if (CollectionUtils.isNotEmpty(entities)) {
                List<QywxGroupSendGroupFlatResultEntity> flatResultEntities = Lists.newArrayList();
                for (QywxGroupSendGroupResultEntity entity : entities) {
                    String sendGroupIds = entity.getSendGroupIds();
                    QywxGroupSendGroupFlatResultEntity flatResultEntity = new QywxGroupSendGroupFlatResultEntity();
                    flatResultEntity.setId(UUIDUtil.getUUID());
                    flatResultEntity.setSendid(entity.getSendid());
                    flatResultEntity.setSender(entity.getSender());
                    flatResultEntity.setMsgid(entity.getMsgid());
                    flatResultEntity.setCreateTime(entity.getCreateTime());
                    flatResultEntity.setUpdateTime(entity.getUpdateTime());
                    if (StringUtils.isNotBlank(sendGroupIds)) {
                        try {
                            List<String> sendGroupIdList = JSONArray.parseArray(sendGroupIds, String.class);
                            for (String sendGroupId : sendGroupIdList) {
                                flatResultEntity.setStatus(1);
                                flatResultEntity.setSendGroupId(sendGroupId);
                            }
                        } catch (Exception ignor) {
                        }
                    } else {
                        flatResultEntity.setStatus(0);
                        flatResultEntity.setSendGroupId("");
                    }
                    flatResultEntities.add(flatResultEntity);
                }
                qywxGroupSendGroupResultDAO.batchInsertFlatResult(flatResultEntities);
            }
        }
    }

}
