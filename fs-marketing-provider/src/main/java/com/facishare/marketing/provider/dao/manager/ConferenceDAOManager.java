package com.facishare.marketing.provider.dao.manager;

import com.facishare.marketing.common.enums.ActivityStatusEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.manager.MarketingStatLogPersistorManger;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.SceneTriggerManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class ConferenceDAOManager {
    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private RedisManager redisManager;
    
    @Autowired
    private SceneTriggerManager sceneTriggerManager;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;

    public String addConference(ActivityEntity entity){
        String conferenceId = addConferenceByLock(entity);
        if (conferenceId != null){
            integralServiceManager.asyncRegisterMaterial(entity.getEa(),CategoryApiNameConstant.MEETING,entity.getId(),entity.getTitle());
        }
        return conferenceId;
    }

    /**
     * 批量创建会议，单向同步CRM中的历史会议
     * @param conferenceList
     * @return
     */
    public void batchAddConference(List<ActivityEntity> conferenceList){
        if (CollectionUtils.isEmpty(conferenceList)){
            return;
        }

        conferenceDAO.batchInsert(conferenceList);
        for (ActivityEntity entity : conferenceList){
            integralServiceManager.asyncRegisterMaterial(entity.getEa(),CategoryApiNameConstant.MEETING,entity.getId(),entity.getTitle());
        }
    }


    private String addConferenceByLock(ActivityEntity entity){
        try {
            // 尝试获取分布式锁 过期时间为100s
            int retry = 0;
            while(retry++ < 30) {
                boolean redisLock = redisManager.lock(entity.getMarketingEventId(), 10);
                if (!redisLock) {
                    // 未获取到锁执行等待
                    Thread.sleep(100);
                } else {
                    ActivityEntity existEntity = conferenceDAO.getConferenceByMarketingEventId(entity.getMarketingEventId(), entity.getEa());
                    if (existEntity == null){
                        Boolean result = conferenceDAO.addConference(entity);
                        if (result) {
                            //上报神策系统
                            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(entity.getEa(), entity.getCreateBy(), ObjectTypeEnum.ACTIVITY.getType(),null, entity.getId()));
                            return entity.getId();
                        }
                        return null;
                    }
                    return existEntity.getId();
                }
            }
        } catch (Exception e) {
            log.warn("ConferenceDAOManager.addConferenceByLock error e:{}", e);
            return null;
        } finally {
            redisManager.unLock(entity.getMarketingEventId());
        }

        return null;
    }

    public boolean updateConference(ActivityEntity updateEntity){
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(updateEntity.getId());
        
        boolean result =  conferenceDAO.updateConference(updateEntity);
        if (result){
            integralServiceManager.asyncRegisterMaterial(updateEntity.getEa(),CategoryApiNameConstant.MEETING, updateEntity.getId(), updateEntity.getTitle());
            if (updateEntity.getStartTime() != null && !Objects.equals(conferenceEntity.getStartTime(), updateEntity.getStartTime())){
                sceneTriggerManager.handleConferenceStartTimeChange(conferenceEntity.getEa(), conferenceEntity.getId(), conferenceEntity.getStartTime(), updateEntity.getStartTime());
            }
            if (updateEntity.getEndTime() != null && !Objects.equals(conferenceEntity.getEndTime(), updateEntity.getEndTime())){
                sceneTriggerManager.handleConferenceEndTimeChange(conferenceEntity.getEa(), conferenceEntity.getId(), conferenceEntity.getEndTime(), updateEntity.getEndTime());
            }
        }
        return result;
    }

    public boolean updateConferenceStatus(String ea, String id,  Integer status, Integer updateBy){
        boolean result = conferenceDAO.updateConferenceStatus(id,status,updateBy);
        if (result && ActivityStatusEnum.DELETED.getStatus() == status){
            integralServiceManager.asyncRemoveMaterial(ea,CategoryApiNameConstant.MEETING,id);
        }
        return result;
    }
}
