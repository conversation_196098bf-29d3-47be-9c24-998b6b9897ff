/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.usermarketingaccounttag;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.IdArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.*;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.usermarketingaccounttag.TagData;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.usermarketingaccounttag.*;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.TagModelSceneConstants;
import com.facishare.marketing.common.contstant.UserTagConstants;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.NestedId;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dto.TagWithTagModel;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupDepartmentRelationEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.organization.adapter.api.permission.model.*;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 25/03/2019
 */
@Service("userMarketingTagService")
@Slf4j
public class UserMarketingTagServiceImpl implements UserMarketingTagService {
    @Autowired
    private UserTagDao userTagDao;
    @Autowired
    private UserTagManager userTagManager;
    @Autowired
    private TagModelDao tagModelDao;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private MaterialTagManager materialTagManager;
    @Autowired
    private DisplayOrderManager displayOrderManager;
    @Autowired
    private TagModelUserTagRelationDao tagModelUserTagRelationDao;
    @Autowired
    private DisplayOrderDao displayOrderDao;
    @Autowired
    @Qualifier("wechatFanOuterTagSynchronizationManager")
    private OuterTagSynchronizationManager wechatFanOuterTagSynchronizationManager;
    @Autowired
    @Qualifier("wxWorkTagSynchronizationManager")
    private OuterTagSynchronizationManager wxWorkTagSynchronizationManager;
    @ReloadableProperty("tag_model_template_list")
    private String tagModelTemplateList;
    @ReloadableProperty("tag_model_template_list_en")
    private String tagModelTemplateListEN;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private CustomTagSettingDao customTagSettingDao;

    @Autowired
    private OpenAppAdminService openAppAdminService;

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private com.facishare.converter.EIEAConverter eieaConverter;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private AuthManager authManager;

    @Autowired
    private ObjectGroupDepartmentRelationDAO objectGroupDepartmentRelationDAO;

    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;

//    private static final String SYSTEM_NOT_GROUP_TAG_NAME = "系统未分组";
    private static final String SYSTEM_NOT_GROUP_TAG_NAME = I18nKeyStaticEnum.MARK_STATIC_SYSTEMNOTGROUPTAGNAME.getKey();

    @Override
    public Result<List<TagModelTemplateResult>> listTagModelTemplates() {
        String finalTagModelTemplateList = I18nUtil.getSuitedLangText(tagModelTemplateList, tagModelTemplateListEN);
        return Result.newSuccess(GsonUtil.fromJson(finalTagModelTemplateList, new TypeToken<List<TagModelTemplateResult>>(){}.getType()));
    }

    @Override
    @FilterLog
    public Result<ListTagModelResult> listTagModel(String ea, Integer fsUserId, ListTagModelArg arg) {
        // 支持企微好友记录对象打营销用户标签
        if (arg.getCrmObjectDescribeApiName() != null && arg.getCrmObjectDescribeApiName().equals(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName())) {
            arg.setCrmObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        }

        ListTagModelResult result = new ListTagModelResult(new ArrayList<>(0));
        List<TagModelEntity> tagModels = tagModelDao.listTagModelByStateAndTypes(ea, arg.getState(), arg.getTypes(), arg.getSourceType());
        if (CollectionUtils.isNotEmpty(tagModels) && StringUtils.isNotBlank(arg.getCrmObjectDescribeApiName())) {
            tagModels = tagModels.stream().filter(e -> StringUtils.isNotBlank(e.getObjects()))
                    .filter(e -> "_ALL_".equals(e.getObjects()) || e.getObjects().contains(arg.getCrmObjectDescribeApiName()))
                    .collect(Collectors.toList());
        }
        if(tagModels.isEmpty()){
            return Result.newSuccess(result);
        }
        Set<String> userRoleCodeSet = this.getUserRoleCodes(ea, fsUserId);
        List<String> enterpriseAdminList = getEnterpriseAdminList(ea, fsUserId);
        List<TagModelResult> tagModelResults = doGetAndSortTagModelAndTags(enterpriseAdminList, ea, fsUserId, tagModels, true, userRoleCodeSet, arg.getSourceType());
        if(arg.getHideNoPermission()!=null && arg.getHideNoPermission()==1){
            tagModelResults = tagModelResults.stream().filter(o->!o.getIsOnlyRead()).collect(Collectors.toList());
        }
        result.setTagModels(tagModelResults);
        String displayKey = DisplayOrderConstants.TAG_MODEL_DISPLAY_KEY;
        if (Objects.equals(arg.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            displayKey = DisplayOrderConstants.TAG_MODEL_MATERIAL_DISPLAY_KEY;
        }
        DisplayOrderEntity tagModelDisplayOrder = displayOrderDao.getDisplayOrder(ea, displayKey);
        if (tagModelDisplayOrder != null) {
            result.setVersion(tagModelDisplayOrder.getVersion());
        }
        return Result.newSuccess(result);
    }

    /**
     * 判断是否拥有角色权限
     * @param roles
     * @param configRoles
     * @return
     */
    private Boolean hasPrivilege(Set<String> roles, Set<String> configRoles) {
        Set<String> retainAllSet = new HashSet<>(roles);
        retainAllSet.retainAll(configRoles);
        return !retainAllSet.isEmpty();
    }

    /**
     * 获取用户角色
     * @param ea
     * @param fsUserId
     * @return
     */
    private Set<String> getUserRoleCodes(String ea, Integer fsUserId) {
        //查询当前CRM角色
        List<UserRoleVo> userRoleVoList = userRoleManager.queryMarketingUserRole(ea, fsUserId);
        if (userRoleVoList == null) {
            log.info("UserMarketingTagService.getUserRoleCodes failed ea:{} fsUserId:{} userRoleVoList:{} ", ea, fsUserId, userRoleVoList);
            return new HashSet<>();
        }
        return userRoleVoList.stream().map(UserRoleVo::getRoleCode).collect(Collectors.toSet());
    }

    private List<String> getEnterpriseAdminList(String ea, Integer operatorUserId) {
        GetRolesByEmployeeAndAppId.Argument argument = new GetRolesByEmployeeAndAppId.Argument();
        argument.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        argument.setCurrentEmployeeId(operatorUserId);
        argument.setAppId("facishare-system");
        argument.setEmployeeId(operatorUserId);
        List<GetRolesByEmployeeAndAppId.RoleWithDepartment> roles = permissionService.getRolesByEmployeeAndAppId(argument).getRoles();
        if (roles != null && !roles.isEmpty()) {
            return roles.stream()
                    .filter(GetRolesByEmployeeAndAppId.RoleWithDepartment::getIsEnable)
                    .map(GetRolesByEmployeeAndAppId.RoleWithDepartment::getRoleCode).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private List<TagModelResult> doGetAndSortTagModelAndTags(List<String> enterpriseAdminList, String ea, Integer fsUserId,List<TagModelEntity> tagModels, boolean needToSortModel, Set<String> userRoleCodeSet, Integer sourceType) {
        if (CollectionUtils.isEmpty(tagModels)) {
            return Lists.newArrayList();
        }
        // 获取当前用户所在部门
        List<Integer> employeeDepartmentIds = authManager.getDepartmentIdsIncludeUp(eieaConverter.enterpriseAccountToId(ea), fsUserId);
        employeeDepartmentIds.add(AuthManager.defaultAllDepartment); // 999999 表示全集团
        // 标签模型的部门权限表复用分组的权限表  groupId就是标签模型的ID
        List<ObjectGroupDepartmentRelationEntity> departmentRelationEntityList = objectGroupDepartmentRelationDAO.getByGroupIdList(tagModels.stream().map(TagModelEntity::getId).collect(Collectors.toList()));
        Map<String, List<ObjectGroupDepartmentRelationEntity>> tagModelDepartmentMap = departmentRelationEntityList.stream().collect(Collectors.groupingBy(ObjectGroupDepartmentRelationEntity::getGroupId));
        List<TagModelResult> tagModelResults = tagModels.stream().map(tagModel -> {
            TagModelResult result = BeanUtil.copy(tagModel, TagModelResult.class);
            List<ObjectGroupDepartmentRelationEntity> relationEntityList = tagModelDepartmentMap.get(result.getId());
            // 判断是否只读
            String roles = tagModel.getRoles();
            result.setRoles(tagModel.getRoles());
            result.setIsOnlyRead(true);
            if (enterpriseAdminList.contains("99")) { //系统管理员可查看全部
                result.setIsOnlyRead(false);
            } else if ("_ALL_".equals(roles) && CollectionUtils.isEmpty(relationEntityList)){
                // 角色和部门的权限都没有设置
                result.setIsOnlyRead(false);
            } else if (StringUtils.isNotEmpty(roles) && !"_ALL_".equals(roles) && CollectionUtils.isNotEmpty(relationEntityList)) {
                // 角色和部门权限都设置了
                String[] split = roles.split(",");
                Set<String> configRoles = new HashSet<>(Arrays.asList(split));
                boolean hasPrivilege = this.hasPrivilege(userRoleCodeSet, configRoles);
                if (!hasPrivilege) {
                    // 如果没有角色权限，在判断部门权限
                    List<Integer> permissionDepartmentIdList = relationEntityList.stream().map(ObjectGroupDepartmentRelationEntity::getDepartmentId).collect(Collectors.toList());
                    hasPrivilege = permissionDepartmentIdList.stream().anyMatch(employeeDepartmentIds::contains);
                }
                result.setIsOnlyRead(!hasPrivilege);
            } else if (StringUtils.isNotEmpty(roles) && !"_ALL_".equals(roles)) {
                // 只设置角色
                String[] split = roles.split(",");
                Set<String> configRoles = new HashSet<>(Arrays.asList(split));
                result.setIsOnlyRead(!this.hasPrivilege(userRoleCodeSet, configRoles));
            }  else if (CollectionUtils.isNotEmpty(relationEntityList)) {
                // 只设置部门
                List<Integer> permissionDepartmentIdList = relationEntityList.stream().map(ObjectGroupDepartmentRelationEntity::getDepartmentId).collect(Collectors.toList());
                boolean hasPrivilege = permissionDepartmentIdList.stream().anyMatch(employeeDepartmentIds::contains);
                result.setIsOnlyRead(!hasPrivilege);
            }
            if (CollectionUtils.isNotEmpty(relationEntityList)) {
                List<Integer> permissionDepartmentIdList = relationEntityList.stream().map(ObjectGroupDepartmentRelationEntity::getDepartmentId).collect(Collectors.toList());
                result.setDepartmentIds(permissionDepartmentIdList);
            }
            result.setAllVisible("_ALL_".equals(roles) && CollectionUtils.isEmpty(tagModelDepartmentMap.get(tagModel.getId())));
            return result;
        }).collect(Collectors.toList());
        if(needToSortModel){
            String displayKey = DisplayOrderConstants.TAG_MODEL_DISPLAY_KEY;
            if (Objects.equals(sourceType, TagModelSourceTypeEnum.MATERIAL.getValue())) {
                displayKey = DisplayOrderConstants.TAG_MODEL_MATERIAL_DISPLAY_KEY;
            }
            DisplayOrderEntity tagModelDisplayOrder = displayOrderDao.getDisplayOrder(ea, displayKey);
            // 模型排序
            NestedIdList.sortByNestedIds(tagModelDisplayOrder.getDisplayItems(), tagModelResults, TagModelResult::getId);
        }

        // 该块代码将每个模型内部串成 模型-一级标签列表-二级标签列表的结构
        Map<String, TagModelResult> tagModelIdMap = tagModelResults.stream().collect(Collectors.toMap(TagModelResult::getId, v -> v));
        List<TagWithTagModel> tagWithTagModels = tagModelDao.listAllTagsByTagModelIds(ea, tagModelIdMap.keySet());
        Map<String, Map<String, UserTagResult>> tagModelIdToFirstUserTagMap = new HashMap<>(32);
        for (TagWithTagModel tagWithTagModel : tagWithTagModels) {
            // 将一级标签添加到模型列表中，并记录tagModelId -> firstTagId -> firstTagResult的映射关系用于后续快速定位到firstTagResult
            if(UserTagConstants.NO_PARENT_TAG_ID.equals(tagWithTagModel.getParentTagId())){
                UserTagResult firstTagResult = tagModelIdMap.get(tagWithTagModel.getTagModelId()).addUserTag(tagWithTagModel.getTagId(), tagWithTagModel.getTagName(), tagWithTagModel.getOperatorId(), tagWithTagModel.getCreateTime(), tagWithTagModel.getExclusive());
                tagModelIdToFirstUserTagMap.putIfAbsent(tagWithTagModel.getTagModelId(), new HashMap<>(32));
                tagModelIdToFirstUserTagMap.get(tagWithTagModel.getTagModelId()).put(firstTagResult.getId(), firstTagResult);
            }
        }
        for (TagWithTagModel tagWithTagModel : tagWithTagModels) {
            if(!UserTagConstants.NO_PARENT_TAG_ID.equals(tagWithTagModel.getParentTagId())){
                UserTagResult firstUserTagResult = tagModelIdToFirstUserTagMap.get(tagWithTagModel.getTagModelId()).get(tagWithTagModel.getParentTagId());
                if (firstUserTagResult != null) {
                    firstUserTagResult.addSubUserTag(tagWithTagModel.getTagId(), tagWithTagModel.getTagName(), tagWithTagModel.getOperatorId(), tagWithTagModel.getCreateTime(), tagWithTagModel.getExclusive());
                }
            }
        }

        Set<String> displayKeys = tagModelIdMap.keySet().stream().map(DisplayOrderConstants::getTagDisplayKey).collect(Collectors.toSet());
        List<DisplayOrderEntity> tagModelDisplayOrders = displayOrderDao.listDisplayOrders(ea, displayKeys);
        Map<String, DisplayOrderEntity> displayOrderKeyMap = tagModelDisplayOrders.stream().collect(Collectors.toMap(DisplayOrderEntity::getDisplayKey, v -> v));

        // 一级标签排序
        tagModelResults.stream().filter(tagModelResult -> displayOrderKeyMap.get(DisplayOrderConstants.getTagDisplayKey(tagModelResult.getId())) != null).forEach(tagModelResult -> {
            NestedIdList.sortByNestedIds(displayOrderKeyMap.get(DisplayOrderConstants.getTagDisplayKey(tagModelResult.getId())).getDisplayItems(), tagModelResult.getTags(), UserTagResult::getId);
            tagModelResult.setDisplayOrderVersion(displayOrderKeyMap.get(DisplayOrderConstants.getTagDisplayKey(tagModelResult.getId())).getVersion());
        });
        // 二级标签排序
        Map<String, Map<String, List<NestedId>>> tagModelIdToFirstTagIdToSecondNestedIdListMap = new HashMap<>(displayOrderKeyMap.size());
        displayOrderKeyMap.forEach((k, v) -> {
            tagModelIdToFirstTagIdToSecondNestedIdListMap.put(k, v.getDisplayItemAsMap());
        });
        tagModelIdToFirstUserTagMap.forEach((tagModelId, v) -> {
            v.forEach((firstTagId, firstTag) -> {
                NestedIdList.sortByNestedIds(tagModelIdToFirstTagIdToSecondNestedIdListMap.get(DisplayOrderConstants.getTagDisplayKey(tagModelId)).get(firstTagId), firstTag.getSubTags(), UserTagResult::getId);
            });
        });
        return tagModelResults;
    }

    @Override
    public Result<TagModelResult> getTagModel(String ea, Integer fsUserId, String tagModelId) {
        TagModelEntity tagModel = tagModelDao.getById(ea, tagModelId);
        Preconditions.checkArgument(tagModel != null);
        Set<String> userRoleCodeSet = this.getUserRoleCodes(ea, fsUserId);
        List<String> enterpriseAdminList = getEnterpriseAdminList(ea, fsUserId);
        return Result.newSuccess(doGetAndSortTagModelAndTags(enterpriseAdminList, ea, fsUserId, ImmutableList.of(tagModel), false, userRoleCodeSet, tagModel.getSourceType()).get(0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> addModel(String ea, Integer fsUserId, AddTagModelArg arg) {
        if (Objects.equals(arg.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            arg.setType(TagModelTypeEnum.MATERIAL.getType());
        }
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_327));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getName()) || arg.getName().contains(":"), "名称不符合规范");
        TagModelEntity existedTagModel = tagModelDao.getByEaAndName(ea, arg.getName(), arg.getSourceType());
        Preconditions.checkArgument(existedTagModel == null, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_USERTAGMANAGER_252));
        Preconditions.checkArgument(TagModelTypeEnum.isValid(arg.getType()), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MEMBERSERVICEIMPL_424));
        Preconditions.checkArgument(arg.checkTagNames(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_332));
        if(arg.getFirstTagList() != null){
            Set<String> firstTagNameSet = arg.getFirstTagList().stream().map(FirstTag::getName).collect(Collectors.toSet());
            Preconditions.checkArgument(firstTagNameSet.size() == arg.getFirstTagList().size(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_335));
            for (FirstTag firstTag : arg.getFirstTagList()) {
                if(firstTag.getSubTags() != null){
                    Set<String> secondTagNames = new HashSet<>(firstTag.getSubTags());
                    Preconditions.checkArgument(secondTagNames.size() == firstTag.getSubTags().size(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_339));
                }
            }
        }

        String tagModelId = userTagManager.createTagModelAndTags(ea, fsUserId, arg.getName(), arg.getType(), arg.getRoles(), null, false, 2, arg.getDescription(),
                arg.getFirstTagList(), arg.getTemplateId(), arg.getDepartmentIds(), arg.getObjects(), arg.getSourceType());
        return Result.newSuccess(tagModelId);
    }

    @Override
    public Result<String> updateModel(String ea, Integer fsUserId, UpdateTagModelArg arg) {
        if (Objects.equals(arg.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            arg.setType(TagModelTypeEnum.MATERIAL.getType());
        }
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_327));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getName()) || arg.getName().contains(":"), "名称不符合规范");
        Preconditions.checkArgument(TagModelTypeEnum.isValid(arg.getType()), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_356));
        TagModelEntity existedTagModel = tagModelDao.getById(ea, arg.getId());
        if(existedTagModel.getSceneId() != null){
            arg.setType(null);
        }
        TagModelEntity nameTagModel = tagModelDao.getByEaAndName(ea, arg.getName(), arg.getSourceType());
        Preconditions.checkArgument(nameTagModel == null || nameTagModel.getId().equals(arg.getId()), "该名称已存在");
        tagModelDao.updateSimpleInfo(ea, arg.getId(), arg.getName(), arg.getDescription(), arg.getType(), arg.getRoles(), arg.getObjects());
        objectGroupDepartmentRelationDAO.deleteByGroupId(arg.getId());
        if (CollectionUtils.isNotEmpty(arg.getDepartmentIds())) {
            List<ObjectGroupDepartmentRelationEntity> departmentRelationEntities = new ArrayList<>();
            for (Integer departmentId : arg.getDepartmentIds()) {
                ObjectGroupDepartmentRelationEntity entity = new ObjectGroupDepartmentRelationEntity();
                entity.setDepartmentId(departmentId);
                entity.setGroupId(arg.getId());
                entity.setEa(ea);
                entity.setId(UUIDUtil.getUUID());
                entity.setObjectType(ObjectTypeEnum.TAG_MODEL.getType());
                departmentRelationEntities.add(entity);
            }
            objectGroupDepartmentRelationDAO.batchInsert(departmentRelationEntities);
        }
        return Result.newSuccess(arg.getId());
    }

    @Override
    public Result<String> updateModelState(String ea, Integer fsUserId, String tagModelId, Integer state) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_327));
        Preconditions.checkArgument(StateEnum.isValid(state), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_384));
        tagModelDao.updateState(ea, tagModelId, state);
        return Result.newSuccess(tagModelId);
    }

    @Override
    public Result<Boolean> deleteModel(String ea, Integer fsUserId, IdArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getId()));
        TagModelEntity tagModel = tagModelDao.getById(ea, arg.getId());
        Preconditions.checkArgument(tagModel == null || tagModel.getSceneId() == null, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_393));
        if (Objects.equals(tagModel.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            Result checkResult = materialTagManager.checkDeleteTagModel(ea, arg.getId());
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }
        tagModelDao.deleteByEaAndId(ea, arg.getId());
        String displayKey = DisplayOrderConstants.TAG_MODEL_DISPLAY_KEY;
        if (Objects.equals(tagModel.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            displayKey = DisplayOrderConstants.TAG_MODEL_MATERIAL_DISPLAY_KEY;
        }
        displayOrderManager.removeFirstNestedId(ea, displayKey, arg.getId());
        return Result.newSuccess(true);
    }

    @Override
    public Result<NestedId> addFirstTag(String ea, Integer fsUserId, AddFirstTagAndSubTagsArg arg) {
        Preconditions.checkArgument(arg.checkTagName(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_411));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTagModelId()), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_412));
        TagModelEntity tagModel = tagModelDao.getById(ea, arg.getTagModelId());
        Preconditions.checkArgument(tagModel != null);
        if (Objects.equals(arg.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            Result checkResult = materialTagManager.checkCreateTag(ea, arg.getTagName(), null);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }
        NestedId nestedId = doAddFirstTagAndGetNestedId(ea, fsUserId, arg.getTagModelId(), arg.getTagName(), arg.getSubTagNames(), arg.getExclusive(), arg.getSourceType());
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getTagDisplayKey(arg.getTagModelId()), ImmutableList.of(nestedId));
        ThreadPoolUtils.execute(() -> {
            String wxAppId = TagModelSceneConstants.getWxServiceIdBySceneId(tagModel.getSceneId());
            if (wxAppId != null) {
                wechatFanOuterTagSynchronizationManager.createFirstTag(ea, wxAppId, arg.getTagName());
            }
            String wxCorpId = TagModelSceneConstants.getWxWorkCorpIdBySceneId(tagModel.getSceneId());
            if(wxCorpId != null){
                wxWorkTagSynchronizationManager.createSecondTags(ea, wxCorpId, arg.getTagName(), arg.getSubTagNames());
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess(nestedId);
    }

    private NestedId doAddFirstTagAndGetNestedId(String ea, Integer fsUserId, String tagModelId, String firstTagName, List<String> subTagNames, Boolean exclusive, Integer sourceType) {
        String firstTagId = userTagManager.getOrCreateFirstTag(ea, fsUserId, firstTagName, sourceType);
        Preconditions.checkArgument(!tagModelUserTagRelationDao.isTagInModel(ea, tagModelId, firstTagId), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_438));
        tagModelUserTagRelationDao.insertIgnore(ea, tagModelId, UserTagConstants.NO_PARENT_TAG_ID, firstTagId, fsUserId, exclusive);
        NestedId nestedId = new NestedId(firstTagId);
        if(subTagNames != null && !subTagNames.isEmpty()){
            List<String> secondTagIds = userTagManager.getOrCreateSecondTags(ea, fsUserId, firstTagId, subTagNames, sourceType);
            tagModelUserTagRelationDao.batchInsertIgnore(ea, tagModelId, firstTagId, secondTagIds, fsUserId);
            nestedId.pushSubIds(secondTagIds);
        }
        return nestedId;
    }

    @Override
    public Result<NestedId> updateFirstTag(String ea, Integer fsUserId, UpdateFirstTagAndSubTagsArg arg) {
        if (Objects.equals(arg.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            Result checkResult = materialTagManager.checkCreateTag(ea, arg.getTagName(), arg.getTagId());
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            userTagDao.updateTagName(ea, arg.getTagId(), arg.getTagName());
            return Result.newSuccess();
        } else {
            Preconditions.checkArgument(arg.checkTagName(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_332));
            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTagModelId()), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_460));
            Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTagId()), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_461));
            UserTagEntity oldTag = userTagDao.getTagById(ea, arg.getTagId());
            Preconditions.checkArgument(oldTag != null && oldTag.isFirstLevelTag(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_463));
            TagModelEntity tagModel = tagModelDao.getById(ea, arg.getTagModelId());
            Preconditions.checkArgument(tagModel != null);
            // 先把库中存在的二级老标签查出来
            List<TagWithTagModel> existedSecondTags = tagModelDao.listSecondTagsByTagModelIdAndFirstTagId(ea, arg.getTagModelId(), arg.getTagId());
            String parentTagId = userTagManager.getOrCreateFirstTag(ea, fsUserId, arg.getTagName(), arg.getSourceType());
            boolean firstTagNameUpdated = !parentTagId.equals(arg.getTagId());
            // 如果修改了一级标签，则将老的标签删除并走重建逻辑，否则只需要重建子标签
            if(firstTagNameUpdated){
                Preconditions.checkArgument(!tagModelUserTagRelationDao.isTagInModel(ea, arg.getTagModelId(), parentTagId), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_438));
                NestedId nestedId = this.doAddFirstTagAndGetNestedId(ea, fsUserId, arg.getTagModelId(), arg.getTagName(), existedSecondTags.stream().map(TagWithTagModel::getTagName).collect(Collectors.toList()), arg.getExclusive(), arg.getSourceType());
                tagModelUserTagRelationDao.deleteRelationByParentTagId(ea, arg.getTagModelId(), arg.getTagId());
                tagModelUserTagRelationDao.deleteRelationByTagId(ea, arg.getTagModelId(), arg.getTagId());
                displayOrderManager.mergeAndReplaceFirstNestId(ea, DisplayOrderConstants.getTagDisplayKey(arg.getTagModelId()), arg.getTagId(), nestedId);

                ThreadPoolUtils.execute(() -> {
                    UserTagEntity oldUserTag = userTagDao.getTagById(ea, arg.getTagId());
                    Map<String, String> updateParams = new HashMap<>();
                    updateParams.put(oldUserTag.getName(), arg.getTagName());
                    String wxAppId = TagModelSceneConstants.getWxServiceIdBySceneId(tagModel.getSceneId());
                    if (wxAppId != null) {
                        wechatFanOuterTagSynchronizationManager.updateFirstTags(ea, wxAppId, updateParams);
                    }
                    String wxWorkCorpId = TagModelSceneConstants.getWxWorkCorpIdBySceneId(tagModel.getSceneId());
                    if(wxWorkCorpId != null){
                        wxWorkTagSynchronizationManager.updateFirstTags(ea, wxWorkCorpId, updateParams);
                    }
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

                return Result.newSuccess(nestedId);
            }
            return Result.newSuccess();
        }
    }

    @Override
    public Result<String> addSecondTag(String ea, Integer fsUserId, String tagModelId, String parentTagId, String tagName, Integer sourceType) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2155));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(parentTagId), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_501));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(tagModelId), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_502));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(tagName) && !tagName.contains(":") && tagName.length() <= 32, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_503));
        TagModelEntity tagModel = tagModelDao.getById(ea, tagModelId);
        Preconditions.checkArgument(tagModel != null);
        UserTagEntity firstUserTag = userTagDao.getTagById(ea, parentTagId);
        Preconditions.checkArgument(firstUserTag != null && firstUserTag.isFirstLevelTag(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_507));
        Boolean isParentTagInModel = tagModelUserTagRelationDao.isTagInModel(ea, tagModelId, parentTagId);
        Preconditions.checkArgument(BooleanUtils.isTrue(isParentTagInModel), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_509));
        if (Objects.equals(sourceType, TagModelSourceTypeEnum.MATERIAL.getValue())) {
            Result checkResult = materialTagManager.checkCreateTag(ea, tagName, null);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }

        tagModelUserTagRelationDao.insertIgnore(ea, tagModelId, UserTagConstants.NO_PARENT_TAG_ID, parentTagId, fsUserId, false);
        String tagId = userTagManager.getOrCreateSecondTag(ea, fsUserId, parentTagId, tagName, sourceType);
        NestedId nestedId = new NestedId(parentTagId);
        nestedId.pushSubId(tagId);

        tagModelUserTagRelationDao.insertIgnore(ea, tagModelId, parentTagId, tagId, fsUserId, false);
        displayOrderManager.mergeNestedIdToLast(ea, DisplayOrderConstants.getTagDisplayKey(tagModelId), ImmutableList.of(nestedId));
        ThreadPoolUtils.execute(() -> {
            UserTagEntity userTag = userTagDao.getTagById(ea, parentTagId);
            String wxWorkCorpId = TagModelSceneConstants.getWxWorkCorpIdBySceneId(tagModel.getSceneId());
            if(wxWorkCorpId != null){
                wxWorkTagSynchronizationManager.createSecondTags(ea, wxWorkCorpId, userTag.getName(), ImmutableSet.of(tagName));
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess(tagId);
    }

    @Override
    public Result<String> updateSecondTag(String ea, Integer fsUserId, String tagModelId, String oldSecondTagId, String tagName, Integer sourceType) {
        if (Objects.equals(sourceType, TagModelSourceTypeEnum.MATERIAL.getValue())) {
            Result checkResult = materialTagManager.checkCreateTag(ea, tagName, oldSecondTagId);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
            userTagDao.updateTagName(ea, oldSecondTagId, tagName);
            return Result.newSuccess();
        } else {
            TagModelEntity tagModel = tagModelDao.getById(ea, tagModelId);
            Preconditions.checkArgument(tagModel != null);
            Preconditions.checkArgument(tagModelUserTagRelationDao.isTagInModel(ea, tagModelId, oldSecondTagId), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_546));
            UserTagEntity oldUserTag = userTagDao.getTagById(ea, oldSecondTagId);
            Preconditions.checkArgument(oldUserTag.isSecondLevelTag(), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_548));
            if(!oldUserTag.getName().equals(tagName)){
                String firstTagId = oldUserTag.getParentTagId();
                UserTagEntity firstUserTag = userTagDao.getTagById(ea, firstTagId);
                Preconditions.checkArgument(firstUserTag != null && firstUserTag.isFirstLevelTag());
                Preconditions.checkArgument(tagModelUserTagRelationDao.isTagInModel(ea, tagModelId, firstTagId), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_553));
                tagModelUserTagRelationDao.deleteRelationByTagId(ea, tagModelId, oldSecondTagId);
                tagModelUserTagRelationDao.insertIgnore(ea, tagModelId, UserTagConstants.NO_PARENT_TAG_ID, firstTagId, fsUserId, false);
                String secondTagId = userTagManager.getOrCreateSecondTag(ea, fsUserId, firstTagId, tagName, sourceType);
                tagModelUserTagRelationDao.insertIgnore(ea, tagModelId, firstTagId, secondTagId, fsUserId, false);
                displayOrderManager.mergeAndReplaceSecondNestId(ea, DisplayOrderConstants.getTagDisplayKey(tagModelId), firstTagId, oldSecondTagId, secondTagId);

                ThreadPoolUtils.execute(() -> {
                    String wxWorkCorpId = TagModelSceneConstants.getWxWorkCorpIdBySceneId(tagModel.getSceneId());
                    if(wxWorkCorpId != null){
                        Map<String, String> updateTagNameParams = new HashMap<>();
                        updateTagNameParams.put(oldUserTag.getName(), tagName);
                        wxWorkTagSynchronizationManager.updateSecondTags(ea, wxWorkCorpId, firstUserTag.getName(), updateTagNameParams);
                    }
                }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            }
            return Result.newSuccess(oldSecondTagId);
        }
    }

    @Override
    public Result<Boolean> deleteTag(String ea, Integer fsUserId, String tagModelId, String tagId) {
        TagModelEntity tagModel = tagModelDao.getById(ea, tagModelId);
        Preconditions.checkArgument(tagModel != null);
        UserTagEntity tag = userTagDao.getTagById(ea, tagId);
        if (Objects.equals(tag.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            Result checkResult = materialTagManager.checkDeleteTag(ea, tagId);
            if (!checkResult.isSuccess()){
                return checkResult;
            }
        }
        if(tag.isFirstLevelTag()){
            tagModelUserTagRelationDao.deleteRelationByParentTagId(ea, tagModelId, tagId);
            tagModelUserTagRelationDao.deleteRelationByTagId(ea, tagModelId, tagId);
            displayOrderManager.removeFirstNestedId(ea, DisplayOrderConstants.getTagDisplayKey(tagModelId), tagId);
            ThreadPoolUtils.execute(() -> {
                String wxAppId = TagModelSceneConstants.getWxServiceIdBySceneId(tagModel.getSceneId());
                if (wxAppId != null) {
                    wechatFanOuterTagSynchronizationManager.deleteFirstTag(ea, wxAppId, ImmutableSet.of(tag.getName()));
                }
                String wxWorkCorpId = TagModelSceneConstants.getWxWorkCorpIdBySceneId(tagModel.getSceneId());
                if(wxWorkCorpId != null){
                    wxWorkTagSynchronizationManager.deleteFirstTag(ea, wxWorkCorpId, ImmutableSet.of(tag.getName()));
                }
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }else{
            UserTagEntity firstTag = userTagDao.getTagById(ea, tag.getParentTagId());
            tagModelUserTagRelationDao.deleteRelationByTagId(ea, tagModelId, tagId);
            displayOrderManager.removeSecondNestedId(ea, DisplayOrderConstants.getTagDisplayKey(tagModelId), tag.getParentTagId(), tagId);
            ThreadPoolUtils.execute(() -> {
                String wxWorkCorpId = TagModelSceneConstants.getWxWorkCorpIdBySceneId(tagModel.getSceneId());
                if(wxWorkCorpId != null){
                    wxWorkTagSynchronizationManager.deleteSecondTags(ea, wxWorkCorpId, firstTag.getName(), ImmutableSet.of(tag.getName()));
                }
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        if (Objects.equals(tag.getSourceType(), TagModelSourceTypeEnum.MATERIAL.getValue())) {
            materialTagManager.deleteTag(ea, tagId);
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<GetLatestDisplayOrderResult> getLatestDisplayOrder(String ea, Integer fsUserId, String displayKey) {
        GetLatestDisplayOrderResult result = doGetLatestDisplayOrderResult(ea, displayKey);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetLatestDisplayOrderResult> updateDisplayOrder(String ea, Integer fsUserId, UpdateDisplayOrderArg arg) {
        int updateCount = displayOrderDao.updateDisplayItems(ea, arg.getDisplayKey(), arg.getDisplayItems(), arg.getVersion());
        if(updateCount == 1){
            return Result.newSuccess(doGetLatestDisplayOrderResult(ea, arg.getDisplayKey()));
        }
        return Result.newError(SHErrorCode.DISPLAY_ORDER_CHANGED, doGetLatestDisplayOrderResult(ea, arg.getDisplayKey()));
    }

    private GetLatestDisplayOrderResult doGetLatestDisplayOrderResult(String ea, String displayKey) {
        DisplayOrderEntity displayOrder = displayOrderDao.getDisplayOrder(ea, displayKey);
        Preconditions.checkArgument(displayOrder != null, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNTTAG_USERMARKETINGTAGSERVICEIMPL_632));
        GetLatestDisplayOrderResult result = new GetLatestDisplayOrderResult();
        result.setVersion(displayOrder.getVersion());
        result.setDisplayOrder(displayOrder.getDisplayItems());
        return result;
    }

    @Override
    public Result<PageResult<ListTagsByTagGroupResult>> listTagsByTagGroup(String ea, Integer fsUserId, ListTagsByTagGroupArg arg) {
        if (StringUtils.isEmpty(arg.getTagGroupId())) {
            arg.setTagGroupId(null);
        }
        if (StringUtils.isEmpty(arg.getTagName())) {
            arg.setTagName(null);
        }
        Page page = new Page();
        page.setCountTotal(true);
        page.setPageNo(arg.getPageNo());
        page.setPageSize(arg.getPageSize());
        List<UserTagEntity> userTags = userTagDao.pageTagByParentTagIdAndGradeAndName(ea, arg.getTagGroupId(), UserTagConstants.SECOND_GRADE, arg.getTagName(), page);
        List<ListTagsByTagGroupResult> resultList = userTags.stream().map(userTag -> {
            ListTagsByTagGroupResult result = new ListTagsByTagGroupResult();
            result.setTagId(userTag.getId());
            result.setTagGroupId(userTag.getParentTagId());
            result.setTagName(userTag.getName());
            result.setCreator(userTag.getCreator());
            result.setCreateTime(userTag.getCreateTime().getTime());
            return result;
        }).collect(Collectors.toList());

        PageResult<ListTagsByTagGroupResult> pageResult = new PageResult<>();
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setData(resultList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<List<LikeTagByNameResult>> listAllTag(String ea, Integer fsUserId) {
        List<UserTagEntity> firstGradeTags = userTagDao.listTagByGrade(ea, UserTagConstants.FIRST_GRADE);
        Map<String, LikeTagByNameResult> idToFirstTagMap = firstGradeTags.stream().collect(Collectors.toMap(UserTagEntity::getId, tag -> {
            LikeTagByNameResult likeTagByNameResult = new LikeTagByNameResult();
            likeTagByNameResult.setTagGroupId(tag.getId());
            likeTagByNameResult.setTagGroupName(tag.getName());
            likeTagByNameResult.setTagDatas(new LinkedList<>());
            return likeTagByNameResult;
        }, (v1, v2) -> v1));
        List<UserTagEntity> secondGradeTags = userTagDao.listTagByGrade(ea, UserTagConstants.SECOND_GRADE);
        secondGradeTags.forEach(tag -> {
            LikeTagByNameResult likeTagByNameResult = idToFirstTagMap.get(tag.getParentTagId());
            if (likeTagByNameResult != null) {
                TagData tagData = new TagData();
                tagData.setTagId(tag.getId());
                tagData.setTagName(tag.getName());
                likeTagByNameResult.getTagDatas().add(tagData);
            }
        });
        List<LikeTagByNameResult> likeTagByNameResults = new LinkedList<>(idToFirstTagMap.values());
        likeTagByNameResults.add(doGetSystemNotGroupTags(ea));
        return Result.newSuccess(likeTagByNameResults);
    }

    private LikeTagByNameResult doGetSystemNotGroupTags(String ea){
        final Set<String> allSystemNotIdentityTag = ConcurrentHashMap.newKeySet();
        userMarketingAccountManager.buildUserMarketingApiNameListByEa(ea).forEach(objectApiName -> {
            TagName firstTagName = new TagName(I18nUtil.getStaticByKey(SYSTEM_NOT_GROUP_TAG_NAME), null);
            String firstTagId = metadataTagManager.getOrCreateTagIdByTagName(ea, objectApiName, firstTagName);
            Set<String> tagNamesBySupTagId = metadataTagManager.listTagNamesBySupTagId(ea, objectApiName, firstTagId);
            allSystemNotIdentityTag.addAll(tagNamesBySupTagId);
        });
        LikeTagByNameResult likeTagByNameResult = new LikeTagByNameResult();
        likeTagByNameResult.setTagGroupName(I18nUtil.getStaticByKey(SYSTEM_NOT_GROUP_TAG_NAME));
        likeTagByNameResult.setCreator(-10000);
        List<TagData> subTagData = allSystemNotIdentityTag.stream().map(name -> {
            TagData tagData = new TagData();
            tagData.setTagName(name);
            return tagData;
        }).collect(Collectors.toList());
        likeTagByNameResult.setTagDatas(subTagData);
        return likeTagByNameResult;
    }

    @Override
    public Result<List<GetAllTagGroupsResult>> getAllTagGroups(String ea, Integer fsUserId) {
        List<UserTagEntity> userTags = userTagDao.listTagByGrade(ea, UserTagConstants.FIRST_GRADE);
        List<GetAllTagGroupsResult> groupResult = userTags.stream().map(userTag -> {
            GetAllTagGroupsResult result = new GetAllTagGroupsResult();
            result.setTagGroupId(userTag.getId());
            result.setTagGroupName(userTag.getName());
            result.setCountChildTagNumber(userTagDao.countSubTags(ea, userTag.getId()));
            return result;
        }).collect(Collectors.toList());
        return Result.newSuccess(groupResult);
    }

    @Override
    public Result<Integer> syncWxWorkTagsToTagModel(String ea, String corpId) {
        ThreadPoolUtils.execute(() -> {
            log.info("UserMarketingTagServiceImpl.syncWxWorkTagsToTagModel start");
            wxWorkTagSynchronizationManager.syncToTagModel(ea, corpId);
            log.info("UserMarketingTagServiceImpl.syncWxWorkTagsToTagModel end");
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess(1);
    }

    @Override
    public Result<Integer> syncWechatFanTagsToTagModel(String ea, String appId) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
            log.info("UserMarketingTagServiceImpl.syncWechatFanTagsToTagModel failed enterprise stop or license expire ea:{}", ea);
            return Result.newSuccess();
        }
        ThreadPoolUtils.execute(() -> {
            log.info("UserMarketingTagServiceImpl.syncWechatFanTagsToTagModel start");
            wechatFanOuterTagSynchronizationManager.syncToTagModel(ea, appId);
            log.info("UserMarketingTagServiceImpl.syncWechatFanTagsToTagModel end");
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess(1);
    }

    @Override
    public Result<Boolean> setOrCancelExclusive(String ea, Integer fsUserId, SetOrCancelTagExclusiveArg arg) {
        tagModelUserTagRelationDao.updateTagRelation(ea, arg.getTagModelId(), arg.getUserTagId(), fsUserId, arg.getExclusive());
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> setOrCancelAllowAddTag(String ea, Integer fsUserId, SetOrCancelAllowAddTagArg arg) {
        CustomTagSettingEntity entity = customTagSettingDao.querySettingByEa(ea);
        if (null != entity) {
            customTagSettingDao.updateSetting(ea, fsUserId, arg.getIsAllow());
        } else {
            customTagSettingDao.createSetting(ea, fsUserId, arg.getIsAllow());
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> isAllowAddTag(String ea) {
        CustomTagSettingEntity entity = customTagSettingDao.querySettingByEa(ea);
        if (null != entity) {
            return Result.newSuccess(entity.getIsAllow());
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<Void> setMaterialTag(SetMaterialTagArg arg) {
        List<MaterialTagRelationEntity> entities = Lists.newArrayList();
        List<String> objectIds = arg.getObjectIds();
        List<String> tagIds = arg.getTagIds();
        Map<String, Date> dateMap = getObjectMap(arg.getEa(), arg.getObjectIds(), arg.getObjectType());
        for (String objectId : objectIds) {
            for (String tagId : tagIds) {
                MaterialTagRelationEntity entity = new MaterialTagRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(arg.getEa());
                entity.setObjectId(objectId);
                entity.setObjectType(arg.getObjectType());
                entity.setTagId(tagId);
                entity.setObjectCreateTime(dateMap.get(objectId));
                entity.setObjectStatus(1);
                entities.add(entity);
            }
        }
        if (CollectionUtils.isNotEmpty(entities)) {
            materialTagRelationDao.batchInsert(entities);
        }
        return Result.newSuccess();
    }

    private Map<String, Date> getObjectMap(String ea, List<String> objectIds, Integer objectType){
        Map<String, Date> dateMap = Maps.newHashMap();
        List<Integer> needSpecial = Lists.newArrayList(ObjectTypeEnum.ACTIVITY.getType(), ObjectTypeEnum.CONFERENCE.getType(), ObjectTypeEnum.LIVE.getType());
        if (needSpecial.contains(objectType)) {
            List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventData(ea, -10000, objectIds);
            if (CollectionUtils.isNotEmpty(marketingEventDataList)) {
                dateMap = marketingEventDataList.stream().collect(Collectors.toMap(MarketingEventData::getId, marketingEventData -> DateUtil.fromTimestamp(marketingEventData.getCreateTime())));
            }
        }
        return dateMap;
    }

    @Override
    public Result<Void> removeMaterialTag(RemoveMaterialTagArg arg) {
        if (CollectionUtils.isNotEmpty(arg.getObjectIds()) && CollectionUtils.isNotEmpty(arg.getTagIds())) {
            materialTagRelationDao.batchDelete(arg.getEa(), arg.getObjectType(), arg.getObjectIds(), arg.getTagIds());
        }
        return Result.newSuccess();
    }

    /**
     * a、如果没有匹配的模型，允许直接删除
     * b、查到有匹配的一个模型，判断当前用户有没有此模型权限，若有，可以直接删除，若无提示：暂无此标签操作权限。不允许操作
     * c、查到多个匹配的模型 ，只要有其中一个模型的权限就可以直接删除。
     *
     * @param ea
     * @param fsUserId
     * @param tagNameList
     * @return
     */
    @Override
    public TagNameList fillIsOnlyRead(String ea, Integer fsUserId, TagNameList tagNameList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tagNameList)) {
            return tagNameList;
        }

        Map<TagName, List<TagModelEntity>> TagNameToTagModelEntity = Maps.newHashMap();
        List<TagModelEntity> ModelEntities = Lists.newArrayList();
        Map<TagModelEntity, Boolean> TagModelEntityToIsOnlyRead = Maps.newHashMap();
        tagNameList.forEach(e -> {
            List<TagModelEntity> tagModelEntityList = tagModelDao.listByEaAndTagName(ea, e.getFirstTagName(), e.getSecondTagName());
            if (CollectionUtils.isNotEmpty(tagModelEntityList)) {
                TagNameToTagModelEntity.put(e, tagModelEntityList);
                ModelEntities.addAll(tagModelEntityList);
            }
        });
        if (CollectionUtils.isEmpty(TagNameToTagModelEntity.values()) || CollectionUtils.isEmpty(ModelEntities)) {
            return tagNameList;
        }
        Set<String> userRoleCodeSet = this.getUserRoleCodes(ea, fsUserId);
        List<String> enterpriseAdminList = getEnterpriseAdminList(ea, fsUserId);
        // 获取当前用户所在部门
        List<Integer> employeeDepartmentIds = authManager.getDepartmentIdsIncludeUp(eieaConverter.enterpriseAccountToId(ea), fsUserId);
        employeeDepartmentIds.add(AuthManager.defaultAllDepartment); // 999999 表示全集团
        // 标签模型的部门权限表复用分组的权限表  groupId就是标签模型的ID
        List<ObjectGroupDepartmentRelationEntity> departmentRelationEntityList = objectGroupDepartmentRelationDAO.getByGroupIdList(ModelEntities.stream().map(TagModelEntity::getId).collect(Collectors.toList()));
        Map<String, List<ObjectGroupDepartmentRelationEntity>> tagModelDepartmentMap = departmentRelationEntityList.stream().collect(Collectors.groupingBy(ObjectGroupDepartmentRelationEntity::getGroupId));
        ModelEntities.forEach(tagModel -> {
            List<ObjectGroupDepartmentRelationEntity> relationEntityList = tagModelDepartmentMap.get(tagModel.getId());
            // 判断是否只读
            TagModelEntityToIsOnlyRead.put(tagModel, true);
            String roles = tagModel.getRoles();
            // 系统管理员可查看全部
            // 角色和部门的权限都没有设置
            if (enterpriseAdminList.contains("99") || "_ALL_".equals(roles) && org.apache.commons.collections4.CollectionUtils.isEmpty(relationEntityList)) {
                TagModelEntityToIsOnlyRead.put(tagModel, false);
            } else if (StringUtils.isNotEmpty(roles) && !"_ALL_".equals(roles) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(relationEntityList)) {
                // 角色和部门权限都设置了
                String[] split = roles.split(",");
                Set<String> configRoles = new HashSet<>(Arrays.asList(split));
                boolean hasPrivilege = this.hasPrivilege(userRoleCodeSet, configRoles);
                if (!hasPrivilege) {
                    // 如果没有角色权限，在判断部门权限
                    List<Integer> permissionDepartmentIdList = relationEntityList.stream().map(ObjectGroupDepartmentRelationEntity::getDepartmentId).collect(Collectors.toList());
                    hasPrivilege = permissionDepartmentIdList.stream().anyMatch(employeeDepartmentIds::contains);
                }
                TagModelEntityToIsOnlyRead.put(tagModel, !hasPrivilege);
            } else if (StringUtils.isNotEmpty(roles) && !"_ALL_".equals(roles)) {
                // 只设置角色
                String[] split = roles.split(",");
                Set<String> configRoles = new HashSet<>(Arrays.asList(split));
                TagModelEntityToIsOnlyRead.put(tagModel, !this.hasPrivilege(userRoleCodeSet, configRoles));
            } else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(relationEntityList)) {
                // 只设置部门
                List<Integer> permissionDepartmentIdList = relationEntityList.stream().map(ObjectGroupDepartmentRelationEntity::getDepartmentId).collect(Collectors.toList());
                boolean hasPrivilege = permissionDepartmentIdList.stream().anyMatch(employeeDepartmentIds::contains);
                TagModelEntityToIsOnlyRead.put(tagModel, !hasPrivilege);
            }
        });
        tagNameList.forEach(e -> {
            List<TagModelEntity> tagModelEntities = TagNameToTagModelEntity.get(e);
            if (tagModelEntities != null) {
                for (TagModelEntity tagModelEntity : tagModelEntities) {
                    Boolean isOnlyRead = TagModelEntityToIsOnlyRead.get(tagModelEntity);
                    boolean delAllowed = isOnlyRead == null || !isOnlyRead;
                    e.setDelAllowed(delAllowed);
                    if (delAllowed) {
                        break;
                    }
                }
            }
        });
        return tagNameList;
    }
}