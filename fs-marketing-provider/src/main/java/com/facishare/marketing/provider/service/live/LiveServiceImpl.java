/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.live;

import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.permission.DataPermissionResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.permission.DataPermissionService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.appMenu.PaasObjectRuleVO;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.appMenu.AppMenuAccessibleRuleEnum;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.hexagon.MarketingCopyArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.live.*;
import com.facishare.marketing.api.arg.sms.SendVerificationCodeArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.live.*;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountDetailsResult;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.service.kis.KisActionService;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.live.*;
import com.facishare.marketing.common.contstant.CampaignMembersConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonTemplateTypeEnum;
import com.facishare.marketing.common.enums.live.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.outapi.arg.result.AssociateCrmLeadModel;
import com.facishare.marketing.outapi.service.CrmLeadMarketingAccountAssociationService;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO;
import com.facishare.marketing.provider.dao.live.*;
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dto.campaignMergeData.BaseCampaignDTO;
import com.facishare.marketing.provider.dto.campaignMergeData.CampaignStatisticDTO;
import com.facishare.marketing.provider.dto.campaignMergeData.PageCampaignLiveDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.ObjectEnrollJumpSettingEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity;
import com.facishare.marketing.provider.entity.live.*;
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerData.live.mudu.MuduApiGetEventListResult;
import com.facishare.marketing.provider.innerData.live.mudu.MuduApiPasswordLessLoginResult;
import com.facishare.marketing.provider.innerData.live.polyv.PolyvLiveInnerData;
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiGetLiveResult;
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiGetRoleUrlResult;
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiQueryLiveArg;
import com.facishare.marketing.provider.innerData.live.vhall.VHallApiQueryLiveResult;
import com.facishare.marketing.provider.innerResult.CreateMiniAppForwardUrlResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager.WxUserData;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.live.ChannelsManager;
import com.facishare.marketing.provider.manager.sms.VerificationCodeManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.polyv.LiveSignUtil;
import com.facishare.training.outer.api.arg.CreateLiveArg;
import com.facishare.training.outer.api.arg.LiveDefaultRecordArg;
import com.facishare.training.outer.api.arg.UpdateLiveInfoArg;
import com.facishare.training.outer.api.enums.LiveChannelEnum;
import com.facishare.training.outer.api.enums.LiveRecordEnum;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.facishare.training.outer.api.enums.LiveViewLimitEnum;
import com.facishare.training.outer.api.message.LiveMqTrainingMessage;
import com.facishare.training.outer.api.result.live.*;
import com.facishare.training.outer.api.service.live.LiveCommonService;
import com.facishare.wechat.proxy.common.aes.WXBizMsgCrypt;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhengh on 2020/3/19.
 */
@Service("liveService")
@Slf4j
public class LiveServiceImpl implements LiveService {
    private static final String POLYV_VIEW_URL = "https://live.polyv.cn/watch/";
    private static final String VHALL_VIEW_URL = "https://live.vhall.com";
    @Autowired
    private CrmLeadMarketingAccountAssociationService crmLeadMarketingAccountAssociationService;
    @Autowired
    private LiveCommonService liveCommonService;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;
    @Autowired
    private MaterialTagManager materialTagManager;
    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;
    @Autowired
    private MarketingLiveViewLoginDAO marketingLiveViewLoginDAO;
    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;
    @Autowired
    private ShortUrlManager shortUrlManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private VHallManager vHallManager;
    @Autowired
    private IntegralServiceManager integralServiceManager;
    @Autowired
    private KisActionService kisActionService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private MemberService memberService;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private SpreadChannelManager spreadChannelManager;
    @Autowired
    private SceneTriggerManager sceneTriggerManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private XiaoetongManager xiaoetongManager;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private LiveUserAccountRelationDAO liveUserAccountRelationDAO;
    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private ObjectEnrollJumpSettingDAO objectEnrollJumpSettingDAO;
    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;
    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;
    @Autowired
    private HexagonSiteManager hexagonSiteManager;
    @Autowired
    private HexagonSiteDAOManager hexagonSiteDAOManager;
    @Autowired
    private MarketingSceneService marketingSceneService;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private XiaoetongAccountDAO xiaoetongAccountDAO;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private VerificationCodeManager verificationCodeManager;
    @Autowired
    private SendService sendService;
    @Autowired
    private PolyvManager polyvManager;
    @Autowired
    private PolyvAccountDAO polyvAccountDAO;
    @Autowired
    private ThirdLiveAccountDAO thirdLiveAccountDAO;

    @Autowired
    private ChannelsManager channelsManager;

    @Autowired
    private MuduManager muduManager;

    @Autowired
    private EIEAConverter converter;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private CustomizeMiniAuthorizeManager customizeMiniAuthorizeManager;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;

    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private ActionManager actionManager;

    @Autowired
    private LiveProxySerivce liveProxySerivce;

    private Gson gson = new Gson();
    @Value("${vhall.h5.view.url}")
    private String liveUserH5Url;
    @Value("${polyv.h5.view.url}")
    private String polyvLiveUserH5Url;
    @Value("${vhall.h5.lecture.view.url}")
    private String liveLectureUrl;
    @Value("${live.default.hexagon.id}")
    private String defaultHexagonIds;

    @ReloadableProperty("channels_transit_url")
    private String channelStransitUrl;
    @ReloadableProperty("channels_transit_hexagon_id")
    private String transitId;
    @ReloadableProperty("default_conference_cover")
    private String defaultLiveCover;
    @Value("${host}")
    private String host;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private PhotoManager photoManager;

    @ReloadableProperty("picture.preview.url.cdn")
    private String cdnSharePath;

    @ReloadableProperty("vhall.customize.host")
    private String vHallCustomizeHost;

    @Autowired
    private DataPermissionService dataPermissionService;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    private final String  XIAOETONG_CALLBACK_TOKEN = "marketingLiveXcl";
    private final String XIAOETONG_CALLBACK_AESKEY = "marketing7exeEpcepe34gxelrMeier0lKleGpelAdi";
    @Override
    public Result<CreateMarketingLiveResult> createLive(CreateLiveVO vo) {
        Integer livePlatform = vo.getLivePlatform();
        LiveBaseService bean = liveProxySerivce.getBean(livePlatform);
        if (bean != null) {
            return bean.createLive(vo);
        }

        if (Objects.equals(livePlatform, LivePlatformEnum.XIAOETONG.getType()) || Objects.equals(livePlatform, LivePlatformEnum.MUDU.getType())) {
            // 校验能否创建
            String xiaoetongLiveId = vo.getXiaoetongLiveId();
            Optional<MarketingLiveEntity> xiaoetongLive = xiaoetongManager.getMarketingLiveByXiaoetongId(xiaoetongLiveId);
            if (xiaoetongLive.isPresent()) {
                // 该直播已被关联，不可使用
                return Result.newError(SHErrorCode.THIRD_LIVE_RELATION_EXIST);
            }
        }

        //1,创建直播市场活动
        if (Objects.equals(vo.getLivePlatform(), LivePlatformEnum.MUDU.getType()) && !Objects.equals(vo.getSubEvent(), 1)) {
            vo.setTitle(vo.getTitle() + I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_359));
        }
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventResult = createMarketingEventObj(vo);
        if (createMarketingEventResult == null){
            log.info("LiveServiceImpl.createLive failed create marketingEvent error return  null vo:{}", vo);
            return Result.newError(SHErrorCode.MARKETING_EVENT_CREATE_FAILED);
        }
        if (!createMarketingEventResult.isSuccess()){
            log.info("LiveServiceImpl.createLive failed create marketingEvent error errcode:{} errMsg:{} vo:{}", createMarketingEventResult.getCode(), createMarketingEventResult.getMessage(), vo);
            return Result.newError(createMarketingEventResult.getCode(), createMarketingEventResult.getMessage());
        }

        String marketingEventId = (String) createMarketingEventResult.getData().getObjectData().get("_id");
        //2，创建直播封面
        String apath = vo.getCoverTaPath();
        if (vo.getCoverTaPath().startsWith("TA_")){
            apath = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(vo.getEa(), vo.getFsUserId(), vo.getExt(), vo.getCoverTaPath(), "fs-marketing-provider");
            if (StringUtils.isBlank(apath)){
                log.info("LiveServiceImpl.createLive failed create marketingEvent error vo:{}", vo);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }

        // 3 组装实体类
        MarketingLiveEntity entity;
        if (vo.getLivePlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
            // 微吼直播
            Result<CreateLiveResult> vHallive = createVHallive(vo, apath);
            if (!vHallive.isSuccess()) {
                return Result.newError(vHallive.getErrCode(), vHallive.getErrMsg());
            }
            entity = buildFsPlatformMarketingCreateLiveEntity(vo, vHallive.getData(), marketingEventId);
        } else {
            entity = buildOtherPlatformMarketingCreateLiveEntity(vo, marketingEventId, apath);
        }

        // 4 创建直播
        String insertId = liveManager.addLiveByLock(entity);
        if (!StringUtils.equals(insertId, entity.getId())){
            //直播先从mq通过过来了
            log.info("sync live from crm ea:{} title:{} insertId:{} entityid:{}", vo.getEa(), vo.getTitle(), insertId, entity.getId());
            entity.setId(insertId);
            // 处理目睹子活动相关字段
            if (Objects.equals(entity.getPlatform(), LivePlatformEnum.MUDU.getType()) && Objects.equals(vo.getSubEvent(), 1)) {
                entity.setSubEvent(vo.getSubEvent());
                entity.setMuduParentId(vo.getMuduParentId());
                marketingLiveDAO.updateSubEvent(entity);
            } else if (Objects.equals(entity.getPlatform(), LivePlatformEnum.CHANNELS.getType())) {
                entity.setAssociatedAccountId(vo.getAssociatedAccountId());
            }
        }

        String viewUrl = liveUserH5Url + entity.getId();
        entity.setViewUrl(viewUrl);
        if (vo.getLivePlatform().intValue() == LivePlatformEnum.XIAOETONG.getType()){
            viewUrl = vo.getXiaoetongLiveUrl() + "?mkId=" + entity.getId() + "&liveId=" + vo.getXiaoetongLiveId() + "&host=" + host;
        }
        if (vo.getLivePlatform().intValue() == LivePlatformEnum.POLYV.getType()){
            viewUrl = polyvLiveUserH5Url + "?mkId=" + entity.getId()+ "&liveId=" + vo.getXiaoetongLiveId();
        }
        if(vo.getLivePlatform().intValue() == LivePlatformEnum.CHANNELS.getType()){
            //viewUrl = liveUserH5Url + entity.getId();
            String miniprogramPath = "/pages/verifyPage/verifyPage?liveId=" + entity.getId();
            try {
                miniprogramPath = URLEncoder.encode(miniprogramPath, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            viewUrl = channelStransitUrl+"?ea=" + vo.getEa() + "&redirectType=webchatVideo" + "&miniprogramPath=" + miniprogramPath;
        }
        if(vo.getLivePlatform().intValue() == LivePlatformEnum.MUDU.getType()){
            viewUrl = polyvLiveUserH5Url + "?mkId=" + entity.getId()+ "&liveId=" + vo.getXiaoetongLiveId();
            /*if (vo.getSubEvent() == 1) {
                viewUrl = vo.getXiaoetongLiveUrl() + "?mkId=" + entity.getId() + "&liveId=" + vo.getXiaoetongLiveId();
            } else {
                String mainRoomUrl = muduManager.getMainRoomUrl(vo.getEa(), vo.getXiaoetongLiveId());
                viewUrl = mainRoomUrl + "?mkId=" + entity.getId() + "&liveId=" + vo.getXiaoetongLiveId();
            }*/
        }
        entity.setViewUrl(viewUrl);

        CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
        shortUrlArg.setUrl(entity.getViewUrl());
        Optional<String> shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
        if (shortUrlResult.isPresent()) {
            entity.setShortViewUrl(shortUrlResult.get());
        }

        if (vo.getLivePlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
            String shortLectrueUrl = liveLectureUrl + entity.getId();
            shortUrlArg.setUrl(shortLectrueUrl);
            shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
            if (shortUrlResult.isPresent()) {
                entity.setShortLectureUrl(shortUrlResult.get());
            }
        }

        entity.setPlatform(vo.getLivePlatform());
        marketingLiveDAO.updateMarketingLive(entity);
        if (vo.getLivePlatform().intValue() == LivePlatformEnum.POLYV.getType()) {
            //设置观看信息
            polyvManager.setExternalAuthSetting(vo.getEa(), entity.getXiaoetongLiveId(), entity.getShortViewUrl(),host+"/appmarketing/web/live/externalAuth");
        }
        //增加统计信息
        if (vo.getLivePlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()
                || vo.getLivePlatform().intValue() == LivePlatformEnum.XIAOETONG.getType()
                || vo.getLivePlatform().intValue() == LivePlatformEnum.POLYV.getType()
                || vo.getLivePlatform().intValue() == LivePlatformEnum.CHANNELS.getType()
                || vo.getLivePlatform().intValue() == LivePlatformEnum.MUDU.getType()) {
            MarketingLiveStatistics marketingLiveStatistics = new MarketingLiveStatistics();
            marketingLiveStatistics.setId(UUIDUtil.getUUID());
            marketingLiveStatistics.setCorpId(vo.getCorpId());
            marketingLiveStatistics.setTotalAttendeeUsers(0);
            marketingLiveStatistics.setTotalViewUsers(0);
            marketingLiveStatistics.setTotalRecordUsers(0);
            marketingLiveStatistics.setViewTimes(0);
            marketingLiveStatistics.setViewDuration(0);
            marketingLiveStatistics.setRecordTimes(0);
            marketingLiveStatistics.setStatus(LiveStatusEnum.NOT_START.getStatus());
            marketingLiveStatistics.setChatTimes(0);
            marketingLiveStatistics.setTotalChatUser(0);

            marketingLiveStatistics.setType(vo.getLivePlatform());
            if (vo.getLivePlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()){
                marketingLiveStatistics.setLiveId(entity.getLiveId());
            }
            if (vo.getLivePlatform().intValue() == LivePlatformEnum.XIAOETONG.getType()||vo.getLivePlatform().intValue() == LivePlatformEnum.POLYV.getType()||vo.getLivePlatform().intValue() == LivePlatformEnum.MUDU.getType()){
                marketingLiveStatistics.setXiaoetongLiveId(vo.getXiaoetongLiveId());
                List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getByXiaoetongLiveId(vo.getCorpId(), vo.getXiaoetongLiveId());
                if (CollectionUtils.isNotEmpty(marketingLiveStatisticsList)){
                    marketingLiveStatistics.setStatus(marketingLiveStatisticsList.get(0).getStatus());
                    marketingLiveDAO.updateXiaoetongLiveStatus(vo.getCorpId(), vo.getXiaoetongLiveId(), marketingLiveStatisticsList.get(0).getStatus());
                }
            } else if (Objects.equals(LivePlatformEnum.MUDU.getType(), vo.getLivePlatform())) {
                marketingLiveStatistics.setXiaoetongLiveId(vo.getXiaoetongLiveId());
            }
            marketingLiveStatisticsDAO.insert(marketingLiveStatistics);
        }

        //预置微页面站点
        String id = entity.getId();
        final String liveCover = apath;
        if (vo.getLivePlatform()== LivePlatformEnum.CHANNELS.getType()){
            String appUrl = getMinAppUrl(vo,marketingEventId,liveCover);
            entity.setOtherPlatformUrl(appUrl);
            marketingLiveDAO.updateMarketingLive(entity);
        }

        //ThreadPoolUtils.execute(() -> {
        String formHexagonSiteId = copyHexagon(vo, marketingEventId, liveCover, vo.getParentHexagonFromId());
        marketingLiveDAO.updateFormHexagonById(id, formHexagonSiteId);
        //}, ThreadPoolTypeEnums.LIGHT_BUSINESS);

        String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + marketingEventId + "&ea=" + vo.getEa() + "&objectType=26&id=" + formHexagonSiteId +"&type=1";
        crmV2Manager.updateMarketingEvenObjLandingPage(vo.getEa(),  marketingEventId, vo.getFsUserId(),eventLandingPage);

        CreateMarketingLiveResult result = new CreateMarketingLiveResult();
        result.setId(entity.getId());
        result.setMarketingEventId(marketingEventId);

        integralServiceManager.asyncRegisterMaterial(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), CategoryApiNameConstant.LIVE,entity.getId(),entity.getTitle());

        marketingSceneService.getOrCreateMarketingScene(vo.getEa(), vo.getFsUserId(), new GetOrCreateMarketingSceneArg(MarketingSceneType.LIVE.getType(), entity.getId()));

        // 目睹直播，创建场所级活动
        if (Objects.equals(LivePlatformEnum.MUDU.getType(), vo.getLivePlatform()) && !Objects.equals(vo.getSubEvent(), 1)) {
            // 设置活动准入
            String accessToken = muduManager.getAccessToken(vo.getEa());
            muduManager.setAccessConfig(accessToken, entity.getXiaoetongLiveId(), entity.getShortViewUrl());
            ThreadPoolUtils.execute(() -> {
                liveManager.saveMuduSubEvent(vo.getEa(), entity.getXiaoetongLiveId());
            }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        return Result.newSuccess(result);
    }

    /**
     * 创建微吼直播
     * @param vo
     * @param apath
     * @return
     */
    private Result<CreateLiveResult> createVHallive(CreateLiveVO vo, String apath){
        CreateLiveArg arg = new CreateLiveArg();
        arg.setTitle(vo.getTitle());
        arg.setEa(vo.getEa());
        arg.setDesc(vo.getDesc());
        arg.setCorpId((long) vo.getCorpId());
        arg.setLectureName(vo.getLectureUserName());
        arg.setStartTime(vo.getStartTime() / 1000);
        arg.setChatOn(vo.getChatOn());
        arg.setAutoRecord(vo.getAutoRecord());
        arg.setChannel(LiveChannelEnum.MARKETING.getChannel());
        arg.setDesc(vo.getDesc());
        byte[] bytes = fileV2Manager.downloadAFile(apath, arg.getEa());
        if (bytes == null) {
            log.error("LiveServiceImpl.createLive get cover byte failed arg:{} ", arg);
            return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_FAILED);
        }
        if (bytes.length > 1024 * 1024) {
            log.info("LiveServiceImpl.createLive get cover byte beyond max length:{}", bytes.length);
            return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
        }
        arg.setCoverBytes(bytes);
        com.facishare.training.common.result.Result<CreateLiveResult> remoteLiveResult = liveCommonService.createLive(arg);
        if (!remoteLiveResult.isSuccess()) {
            log.warn("LiveServiceImpl.createLive create live failed arg:{} errorMsg:{}", arg, remoteLiveResult.getErrorMessage());
            return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_FAILED);
        }
        return Result.newSuccess(remoteLiveResult.getData());
    }

    private String getMinAppUrl(CreateLiveVO vo,String marketingEventId,String apath) {
        MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(vo.getCorpId(), marketingEventId);
        MarketingCopyArg marketingCopyArg=new MarketingCopyArg();
        marketingCopyArg.setId(transitId);
        marketingCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_576));
        marketingCopyArg.setMarketingEventId(marketingEventId);
        Result<CreateSiteResult> copySiteResult = hexagonService.marketingCopySite(vo.getEa(), vo.getFsUserId(), marketingCopyArg, false);
        if (copySiteResult == null || !copySiteResult.isSuccess()) {
            log.info("copyHexagon failed as create live arg:{}", marketingCopyArg);
        } else {
            hexagonManager.updateCopiedLiveHexagon(liveEntity, vo.getEa(), copySiteResult.getData().getId(),vo.getTitle(), vo.getDesc(), apath, new Date(vo.getStartTime()), false);
            redisManager.deleteHexgonSite(copySiteResult.getData().getId());
            List<String> hexagonPageIds =hexagonPageDAO.getPageIdsBySiteId(copySiteResult.getData().getId());
            if (CollectionUtils.isNotEmpty(hexagonPageIds)) {
                hexagonPageIds.forEach(pageId -> redisManager.deleteHexgonPage(pageId));
            }
        }
        String siteId = copySiteResult.getData().getId();
        String path = "pkgs/pkg-hexagon/pages/detail/hexagon-detail?objectId=" + siteId + "&objectType=26";
        String appUrl = host + "/proj/page/marketing-mplink?ea=" + vo.getEa() + "&miniprogramPath=" + UrlUtils.urlEncode(path);
        return appUrl;
    }

    private String copyHexagon(CreateLiveVO vo, String marketingEventId, String apath, String parentHexagonFromId){
        String ea = vo.getEa();
        MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(vo.getCorpId(), marketingEventId);
        String[] middleIds = StringUtils.split(defaultHexagonIds, ",");
        String[] ids =Arrays.copyOf(middleIds,2);
        if(vo.getLivePlatform().intValue() == LivePlatformEnum.CHANNELS.getType()){
            if(middleIds.length>1){
                ids[0] = middleIds[2];
            }
        }
        String hexagonFromId = null;
        if (ids.length > 0) {
            int i = 0;
            // 若有【市场活动设置】中的模板，则处理“报名预约”微页面站点
            HexagonTemplateSiteEntity marketingTemplate = null;
            if (Strings.isNullOrEmpty(vo.getMarketingTemplateId())) {
                marketingTemplate = hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(ea, HexagonTemplateTypeEnum.LIVE.getType());
            }else{
                marketingTemplate = hexagonTemplateSiteDAO.getById(vo.getMarketingTemplateId());
            }
            if (marketingTemplate != null) {
                String firstSideId = UUIDUtil.getUUID();
                HexagonPageEntity templateFirstPage = hexagonPageDAO.getHomePage(ids[0]);
                HexagonPageArg templateFirstPageArg = BeanUtil.copy(templateFirstPage, HexagonPageArg.class);
                // 拷贝“报名预约”的第一页
                String firstPageId = hexagonService.copyPage(templateFirstPageArg, ea, firstSideId, vo.getFsUserId()).getData();
                HexagonPageEntity firstPage = hexagonPageDAO.getById(firstPageId);
                firstPage = hexagonManager.updateLiveHexagonParam(firstPage, liveEntity, ea, firstSideId, vo.getTitle(), vo.getDesc(), apath, new Date(vo.getStartTime()), false);
                // 拷贝设置中的其他页
                List<HexagonTemplatePageEntity> templatePages = hexagonTemplatePageDAO.getBySiteId(marketingTemplate.getId());
                HexagonSiteListDTO formInfo = hexagonTemplateSiteDAO.getFormByTemplateSiteId(marketingTemplate.getId());
                Map<String, String> buttonInsideAction = new HashMap<>();
                int size = templatePages.size();
                for (int j = 0; j < size; j++) {
                    HexagonTemplatePageEntity templatePage = templatePages.get(j);
                    Map<String, Object> copyResult = hexagonSiteManager.copyPageFromTemplate(ea, templatePage, firstSideId, formInfo, null, buttonInsideAction);
                    // 将首页按钮链接过来
                    if (j == 0) {
                        String firstPageContent = firstPage.getContent();
                        firstPageContent = firstPageContent.replace("{SignupPageId}", (String) copyResult.get("newPageId"));
                        hexagonPageDAO.updateContent(firstPageId, firstPageContent);
                    }
                    if (copyResult != null && copyResult.get("newFormId") != null && copyResult.get("newPageId") != null){
                        String formId = copyResult.get("newFormId") == null ? null : copyResult.get("newFormId").toString();
                        String hexagonPageId = copyResult.get("newPageId") == null ? null : copyResult.get("newPageId").toString();
                        customizeFormDataManager.bindCustomizeFormDataObject(formId , hexagonPageId, ObjectTypeEnum.HEXAGON_PAGE.getType(), vo.getEa(), vo.getFsUserId(), null, null, null);
                    }
                }
                // 页面拷贝完成，生成站点
                HexagonSiteEntity saveSiteData = new HexagonSiteEntity();
                saveSiteData.setId(firstSideId);
                saveSiteData.setEa(ea);
                saveSiteData.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVEBASESERVICE_249));
                saveSiteData.setStatus(HexagonStatusEnum.NORMAL.getType());
                saveSiteData.setCreateBy(vo.getFsUserId());
                saveSiteData.setUpdateBy(vo.getFsUserId());
                saveSiteData.setCreateTime(new Date());
                saveSiteData.setUpdateTime(new Date());
                // saveSiteData.setSystemSite(true);
                int result = hexagonSiteDAO.insert(saveSiteData);
                if (result == 1) {
                    integralServiceManager.asyncRegisterMaterial(ea,CategoryApiNameConstant.MICRO_PAGE, firstSideId, liveEntity.getTitle() + "-" + saveSiteData.getName());
                }
                hexagonService.bindMarketing(ea, vo.getFsUserId(), ObjectTypeEnum.HEXAGON_SITE.getType(), marketingTemplate.getId(), firstSideId, marketingEventId);
                hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, firstSideId, true);
                hexagonFromId = firstSideId;
                contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(hexagonFromId, ObjectTypeEnum.HEXAGON_SITE.getType(), liveEntity.getMarketingEventId(), true);
                // 第二个站点照常
                i = 1;
            }
            for (; i < ids.length; i++) {
                String hexagonId = ids[i];
                MarketingCopyArg marketingCopyArg = new MarketingCopyArg();
                marketingCopyArg.setId(hexagonId);
                marketingCopyArg.setName(vo.getTitle());
                marketingCopyArg.setMarketingEventId(marketingEventId);
                marketingCopyArg.setRegisterIntegralMaterial(false);
                boolean submitHexagonSite = false;
                if (i == 0) {
                    //第一个微页面为报名表单
                    marketingCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVEBASESERVICE_249));
                    submitHexagonSite = true;
                }else {
                    marketingCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVEBASESERVICE_280));
                }
                Result<CreateSiteResult> copySiteResult = hexagonService.marketingCopySite(ea, vo.getFsUserId(), marketingCopyArg, false);
                if (copySiteResult == null || !copySiteResult.isSuccess()) {
                    log.info("copyHexagon failed as create live arg:{}", marketingCopyArg);
                } else {
                    hexagonManager.updateCopiedLiveHexagon(liveEntity, ea, copySiteResult.getData().getId(),vo.getTitle(), vo.getDesc(), apath, new Date(vo.getStartTime()), submitHexagonSite);
                    redisManager.deleteHexgonSite(copySiteResult.getData().getId());
                    List<String> hexagonPageIds = hexagonPageDAO.getPageIdsBySiteId(copySiteResult.getData().getId());
                    integralServiceManager.asyncRegisterMaterial(ea,CategoryApiNameConstant.MICRO_PAGE, copySiteResult.getData().getId(), liveEntity.getTitle() + "-" + marketingCopyArg.getName());
                    if (CollectionUtils.isNotEmpty(hexagonPageIds)) {
                        hexagonPageIds.forEach(pageId -> redisManager.deleteHexgonPage(pageId));
                    }
                }

                if (submitHexagonSite){
                    contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(hexagonFromId, ObjectTypeEnum.HEXAGON_SITE.getType(), liveEntity.getMarketingEventId(), true);
                }
            }
        }
        if (Objects.equals(vo.getSubEvent(), 1)) {
            // 从主活动的微页面进行复制
            HexagonPageEntity parentHomePage = hexagonPageDAO.getHomePage(parentHexagonFromId);
            HexagonPageEntity homePage = hexagonPageDAO.getHomePage(hexagonFromId);
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), parentHomePage.getId(),ea);
            if (photoEntity != null) {
                photoEntity.setTargetId(homePage.getId());
                photoEntity.setId(UUIDUtil.getUUID());
                photoDAO.addPhoto(photoEntity);
            }
            PhotoEntity photoEntity2 = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), parentHomePage.getId(),ea);
            if (photoEntity2 != null) {
                photoEntity2.setTargetId(homePage.getId());
                photoEntity2.setId(UUIDUtil.getUUID());
                photoDAO.addPhoto(photoEntity2);
            }
            PhotoEntity photoEntity3 = photoManager.querySinglePhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), parentHomePage.getId(),ea);
            if (photoEntity3 != null) {
                photoEntity3.setTargetId(homePage.getId());
                photoEntity3.setId(UUIDUtil.getUUID());
                photoDAO.addPhoto(photoEntity3);
            }
        } else {
            if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getOriginalImageAPath()) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(hexagonFromId);
                if(homePage!=null){
                    for (PhotoCutOffset cutOffset : vo.getCutOffsetList()){
                        if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType()){
                            photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ?vo.getOriginalImageAPath() : cutOffset.getPath());
                        }
                        if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()){
                            photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ?vo.getOriginalImageAPath() : cutOffset.getPath());
                        }
                        if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()){
                            photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ?vo.getOriginalImageAPath() : cutOffset.getPath());
                        }
                    }
                }
            }
        }

        return hexagonFromId;
    }

    @Override
    public Result<CreateMarketingLiveResult> updateLive(CreateLiveVO arg) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(arg.getId());
        if (entity == null){
            log.error("LiveServiceImpl.updateLive failed live is not exist id:{}", arg.getId());
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        Integer livePlatform = arg.getLivePlatform();
        LiveBaseService bean = liveProxySerivce.getBean(livePlatform);
        if (bean != null) {
            return bean.updateLive(arg);
        }

        if (entity.getPlatform() == null){
            entity.setPlatform(arg.getLivePlatform());
        }
        if (arg.getLivePlatform().intValue() != entity.getPlatform().intValue()){
            log.warn("LiveServiceImpl.updateLive failed cannot change platform arg:{}", arg);
            return Result.newError(SHErrorCode.MARKETING_CHANGE_PLATFORM_DENY);
        }

        if (arg.getCoverTaPath() != null && arg.getCoverTaPath().contains(host)){
            String apath = fileV2Manager.getApathByUrl(arg.getCoverTaPath());
            if (StringUtils.equals(defaultLiveCover, apath)){
                log.info("LiveServiceImpl.update failed set live cover not default cover arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_SET_COVER);
            }
        }

        if (!StringUtils.equals(arg.getTitle(), entity.getTitle())){
            //查询市场活动是否存在
            int marketingEventCount = conferenceManager.queryMarketingEventCountByTitle(arg.getEa(), arg.getTitle());
            if (marketingEventCount != 0){
                log.info("LiveServiceImpl.update failed marketingEvent is exsit vo:{} conferenceEntity.getTitle:", arg, entity.getTitle());
                return Result.newError(SHErrorCode.MARKETING_EVENT_EXIST);
            }
        }

        //同步市场活动数据
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> marketingResult = updateLiveToCrmMarketingEvent(arg.getEa(),
                                                                       arg.getFsUserId(), entity.getMarketingEventId(), arg);
        if (!marketingResult.isSuccess()){
            return Result.newError(marketingResult.getCode(), marketingResult.getMessage());
        }

        CreateLiveResult createLiveResult = null;
        UpdateResult updateResult = null;
        if (arg.getLivePlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
            Integer liveId = entity.getLiveId();
            if (entity.getLiveId() == null) {
                //创建微吼直播
                byte[] bytes = fileV2Manager.downloadAFile(arg.getCoverTaPath(), arg.getFsUserId(), arg.getEa());
                if (bytes == null) {
                    log.error("LiveServiceImpl.updateLive get cover byte failed arg:{} ", arg);
                    return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_FAILED);
                }
                if (bytes.length > 1024 * 1024) {
                    log.info("LiveServiceImpl.createLive get cover byte beyond max length:{}", bytes.length);
                    return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
                }

                CreateLiveArg createArg = new CreateLiveArg();
                createArg.setTitle(arg.getTitle());
                createArg.setEa(arg.getEa());
                createArg.setDesc(arg.getDesc());
                createArg.setCorpId((long) arg.getCorpId());
                createArg.setLectureName(arg.getLectureUserName());
                createArg.setStartTime(arg.getStartTime() / 1000);
                createArg.setChatOn(arg.getChatOn());
                createArg.setAutoRecord(arg.getAutoRecord());
                createArg.setChannel(LiveChannelEnum.MARKETING.getChannel());
                createArg.setDesc(arg.getDesc());
                createArg.setCoverBytes(bytes);
                com.facishare.training.common.result.Result<CreateLiveResult> remoteLiveResult = liveCommonService.createLive(createArg);
                if (!remoteLiveResult.isSuccess()) {
                    log.error("LiveServiceImpl.createLive create live failed arg:{} errorMsg:{}", arg, remoteLiveResult.getErrorMessage());
                    return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_FAILED);
                }
                createLiveResult = remoteLiveResult.getData();
                liveId = createLiveResult.getLiveId();
            } else {
                //更新微吼直播
                UpdateLiveInfoArg updateLiveInfoArg = new UpdateLiveInfoArg();

                updateLiveInfoArg.setChannel(LiveChannelEnum.MARKETING.getChannel());
                updateLiveInfoArg.setUserId(arg.getFsUserId());
                updateLiveInfoArg.setLiveId(entity.getLiveId());
                updateLiveInfoArg.setTitle(arg.getTitle());
                updateLiveInfoArg.setLectureName(arg.getLectureUserName());
                updateLiveInfoArg.setStartTime(arg.getStartTime() / 1000);
                updateLiveInfoArg.setChatOn(arg.getChatOn());
                updateLiveInfoArg.setAutoRecord(arg.getAutoRecord());
                updateLiveInfoArg.setDesc(arg.getDesc());
                if (arg.getCoverTaPath().startsWith("TA_") || arg.getCoverTaPath().startsWith("A_")) {
                    byte[] bytes = fileV2Manager.downloadAFile(arg.getCoverTaPath() + "." + arg.getExt(), arg.getFsUserId(), arg.getEa());
                    if (bytes == null) {
                        log.error("LiveServiceImpl.createLive get cover byte failed arg:{} ", arg);
                        return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_FAILED);
                    }
                    if (bytes.length > 1024 * 1024) {
                        log.info("LiveServiceImpl.createLivecover byte beyond max arg:{} length:{} ", arg, bytes.length);
                        Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
                    }
                    updateLiveInfoArg.setCoverByte(bytes);
                    com.facishare.training.common.result.Result<UpdateResult> remoteUpdateResult = liveCommonService.updateLiveInfo(updateLiveInfoArg);
                    if (remoteUpdateResult == null || !remoteUpdateResult.isSuccess()) {
                        log.error("LiveServiceImpl.updateLive failed call remote update failed arg:{}", arg);
                        return Result.newError(SHErrorCode.MARKETING_LIVE_UPDATE_FAILED);
                    }
                    updateResult = remoteUpdateResult.getData();
                    liveId = updateResult.getLiveId();
                }
            }

            //增加统计信息微吼
            if (marketingLiveStatisticsDAO.getByLiveId(entity.getLiveId()) == null) {
                MarketingLiveStatistics marketingLiveStatistics = new MarketingLiveStatistics();
                marketingLiveStatistics.setId(UUIDUtil.getUUID());
                marketingLiveStatistics.setCorpId(arg.getCorpId());
                marketingLiveStatistics.setTotalAttendeeUsers(0);
                marketingLiveStatistics.setTotalViewUsers(0);
                marketingLiveStatistics.setTotalRecordUsers(0);
                marketingLiveStatistics.setViewTimes(0);
                marketingLiveStatistics.setViewDuration(0);
                marketingLiveStatistics.setRecordTimes(0);
                marketingLiveStatistics.setStatus(LiveStatusEnum.NOT_START.getStatus());
                marketingLiveStatistics.setChatTimes(0);
                marketingLiveStatistics.setTotalChatUser(0);
                marketingLiveStatistics.setType(entity.getPlatform());
                marketingLiveStatistics.setLiveId(liveId);
                marketingLiveStatisticsDAO.insert(marketingLiveStatistics);
            }
        }
        //增加统计信息 小鹅通和保利威
        if (arg.getLivePlatform() == LivePlatformEnum.POLYV.getType() || arg.getLivePlatform() == LivePlatformEnum.XIAOETONG.getType()) {
            List<MarketingLiveStatistics> entitys = marketingLiveStatisticsDAO.getByXiaoetongLiveId(eieaConverter.enterpriseAccountToId(arg.getEa()), arg.getXiaoetongLiveId());
            if (CollectionUtils.isEmpty(entitys)) {
                MarketingLiveStatistics marketingLiveStatistics = new MarketingLiveStatistics();
                marketingLiveStatistics.setId(UUIDUtil.getUUID());
                marketingLiveStatistics.setCorpId(arg.getCorpId());
                marketingLiveStatistics.setTotalAttendeeUsers(0);
                marketingLiveStatistics.setTotalViewUsers(0);
                marketingLiveStatistics.setTotalRecordUsers(0);
                marketingLiveStatistics.setViewTimes(0);
                marketingLiveStatistics.setViewDuration(0);
                marketingLiveStatistics.setRecordTimes(0);
                marketingLiveStatistics.setStatus(LiveStatusEnum.NOT_START.getStatus());
                marketingLiveStatistics.setChatTimes(0);
                marketingLiveStatistics.setTotalChatUser(0);
                marketingLiveStatistics.setType(entity.getPlatform());
                marketingLiveStatistics.setXiaoetongLiveId(arg.getXiaoetongLiveId());
                marketingLiveStatisticsDAO.insert(marketingLiveStatistics);
            }
        }
        String apath = arg.getCoverTaPath();
        if (arg.getCoverTaPath().startsWith("TA_")) {
            apath = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(arg.getEa(), arg.getFsUserId(), arg.getExt(), arg.getCoverTaPath(), "fs-marketing-provider");
            if (StringUtils.isBlank(apath)) {
                log.info("LiveServiceImpl.createLive failed tapath to apath error arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }
        if (arg.getCoverTaPath() != null && arg.getCoverTaPath().contains(host)) {
            apath = fileV2Manager.getApathByUrl(arg.getCoverTaPath());
            if (StringUtils.isBlank(apath)) {
                log.info("LiveServiceImpl.createLive failed host url to apath error arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }
        if(arg.getCoverTaPath() != null && arg.getCoverTaPath().contains(cdnSharePath)){
            apath = fileV2Manager.getCPathByCdnUrl(arg.getCoverTaPath());
            if (StringUtils.isBlank(apath)) {
                log.info("LiveServiceImpl.createLive failed cdnSharePath cdnurl to apath error arg:{}", arg);
                return Result.newError(SHErrorCode.MARKETING_LIVE_CREATE_COVER_FAILED);
            }
        }


        //更新本地直播信息
        MarketingLiveEntity updateEntity = null;
        if (arg.getLivePlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()){
            updateEntity = buildFsPlatformMarketingUpdateLiveEntity(arg, entity, createLiveResult, updateResult);
        }else {
            updateEntity = buildOtherPlatformMarketingUpdateLiveEntity(arg, entity, apath);
        }
        if (arg.getLivePlatform().intValue() == LivePlatformEnum.XIAOETONG.getType()){
            String viewUrl = updateEntity.getViewUrl();
            if (!viewUrl.contains("mkId=")) {
                viewUrl = viewUrl + "?mkId=" + entity.getId() + "&liveId=" + arg.getXiaoetongLiveId() + "&host=" + host;
                updateEntity.setViewUrl(viewUrl);
                updateEntity.setXiaoetongLiveId(arg.getXiaoetongLiveId());
            }
        }


        int dbRet = marketingLiveDAO.updateMarketingLive(updateEntity);
        if (dbRet < 0){
            log.info("LiveServiceImpl.createLive failed db update failed:{}", arg);
            Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }

        if (arg.getLivePlatform().intValue() == LivePlatformEnum.POLYV.getType()) {
            //设置观看信息
            polyvManager.setExternalAuthSetting(arg.getEa(), updateEntity.getXiaoetongLiveId(), updateEntity.getShortViewUrl(),host+"/appmarketing/web/live/externalAuth");
        }

        if(!Objects.equals(entity.getStartTime(), updateEntity.getStartTime())){
            sceneTriggerManager.handleLiveStartTimeChange(arg.getEa(), updateEntity.getId(), entity.getStartTime(), updateEntity.getStartTime());
        }
        if (!Objects.equals(entity.getEndTime(), updateEntity.getEndTime())){
            sceneTriggerManager.handleLiveEndTimeChange(arg.getEa(), updateEntity.getId(), entity.getStartTime(), updateEntity.getStartTime());
        }

        if (updateEntity.getFormHexagonId() == null && apath != null) {
            String id = entity.getId();
            String coverPath = apath;
            CreateLiveVO vo = BeanUtil.copy(arg, CreateLiveVO.class);
            log.info("updateLive arg:{} vo:{} coverPath:{}", arg, vo);
            if (vo.getLivePlatform()== LivePlatformEnum.CHANNELS.getType()){
                String appUrl = getMinAppUrl(vo,entity.getMarketingEventId(),coverPath);
                updateEntity.setOtherPlatformUrl(appUrl);
                marketingLiveDAO.updateMarketingLive(updateEntity);
            }
            /**
            ThreadPoolUtils.execute(new Runnable() {
                @Override
                public void run() {
                    String formHexagonSiteId = copyHexagon(vo, entity.getMarketingEventId(), coverPath);
                    marketingLiveDAO.updateFormHexagonById(id, formHexagonSiteId);
                }
            });
             **/
            String formHexagonSiteId = copyHexagon(vo, entity.getMarketingEventId(), coverPath, null);
            marketingLiveDAO.updateFormHexagonById(id, formHexagonSiteId);
            String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + entity.getMarketingEventId() + "&ea=" + arg.getEa() + "&objectType=26&id=" + formHexagonSiteId +"&type=1";
            crmV2Manager.updateMarketingEvenObjLandingPage(arg.getEa(),  entity.getMarketingEventId() , arg.getFsUserId(),eventLandingPage);
        }else {
            if(arg.getLivePlatform() == LivePlatformEnum.CHANNELS.getType() ){
                List<String> filterIds = null;
                List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities1 = contentMarketingEventMaterialRelationDAO
                        .listByMarketingEventIdAndObjectType(arg.getEa(), entity.getMarketingEventId(), Lists.newArrayList(26), filterIds);
                List<String> sitelist = contentMarketingEventMaterialRelationEntities1.stream().map(o -> o.getObjectId()).collect(Collectors.toList());
                List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.listByIds(sitelist);
                List<String> list = hexagonSiteEntityList.stream().filter(o -> o.getName().equals("报名预约")).map(o -> o.getId()).collect(Collectors.toList());
                if (list.size() != 0) {
                    HexagonPageEntity homePage = hexagonPageDAO.getHomePage(list.get(0));
                    String[] middleIds = StringUtils.split(defaultHexagonIds, ",");
                    HexagonPageEntity temPage = null;
                    if (middleIds.length>1){
                        temPage  = hexagonPageDAO.getHomePage(middleIds[2]);
                    }
                    if(temPage!=null){
                        homePage.setContent(temPage.getContent());
                    }
                    MarketingLiveEntity liveEntity = marketingLiveDAO.getById(arg.getId());
                    HexagonPageEntity hexagonPageEntity = copyLiveHexagon(homePage, liveEntity, arg.getEa(), list.get(0), arg.getTitle(), arg.getDesc(), apath, new Date(arg.getStartTime()));
                    String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + entity.getMarketingEventId() + "&ea=" + arg.getEa() + "&objectType=26&id=" + hexagonPageEntity.getHexagonSiteId() +"&type=1";
                    crmV2Manager.updateMarketingEvenObjLandingPage(arg.getEa(),  entity.getMarketingEventId() , arg.getFsUserId(),eventLandingPage);
                }
            }
        }
        integralServiceManager.asyncRegisterMaterial(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), CategoryApiNameConstant.LIVE,entity.getId(),arg.getTitle());
        CreateMarketingLiveResult result = new CreateMarketingLiveResult();
        result.setId(entity.getId());
        return Result.newSuccess(result);
    }

    private HexagonPageEntity copyLiveHexagon(HexagonPageEntity hexagonPageEntity, MarketingLiveEntity liveEntity, String ea,String siteId, String title, String desc, String coverPath, Date startTime) {
        if (hexagonPageEntity == null){
            return null;
        }
        MemberConfigEntity memberConfigEntity = memberManager.tryInitMemberConfig(ea);
        if (memberConfigEntity == null){
            log.warn("LiveServiceImpl.copyLiveHexagon memberConfigEntity not exist ea:{}", ea);
            return null;
        }

        //替换直播数据
        if (desc == null){
            desc = "";
        }
        String newDesc = desc;
        if (newDesc.length() > 30){
            newDesc = newDesc.substring(0, 30);
        }
        if (newDesc.length() > 1) {
            char lastCha = newDesc.charAt(newDesc.length() - 1);
            if (lastCha == ',' || lastCha == '\'' || lastCha == '\"') {
                newDesc = newDesc.substring(0, newDesc.length() - 1);
            }
        }
        String content = hexagonPageEntity.getContent();
        Gson gson =new GsonBuilder().disableHtmlEscaping().create();
        Map<String, Object> gmap = gson.fromJson(content, Map.class);
        gmap.put("name", title);
        Map<String, Object> shareOptsMap = (Map<String, Object>)gmap.get("shareOpts");
        //StringBuilder sb = new StringBuilder().append(host).append("/fssharehelper/file/getFileBySpliceUrl?").append("path=").append(coverPath);
        StringBuilder sb = new StringBuilder();
        String urlByPath = fileV2Manager.getUrlByPath(ea, coverPath);
        if (StringUtils.isNotBlank(urlByPath)){
            sb.append(urlByPath);
        }
        shareOptsMap.put("imgUrl",sb.toString());
        shareOptsMap.put("title", title);
        shareOptsMap.put("desc", newDesc);
        List<Map<String, Object>> componentsMapList = (List<Map<String, Object>>)gmap.get("components");
        for (Map<String, Object> map : componentsMapList){
            if (map.get("type").equals("image")){
                List<Map<String, String>> urls = (List<Map<String, String>>)map.get("images");
                urls.get(0).put("url", sb.toString());
            }
        }
        String newContent = gson.toJson(gmap);
        //替换开始时间
        String str = "yyy-MM-dd HH:mm";
        SimpleDateFormat sdf = new SimpleDateFormat(str);
        newContent = newContent.replace("{直播开始时间}", sdf.format(startTime).toString());
        //替换直播标题
        newContent = newContent.replace("{直播标题}", title);
        //替换简介
        desc = desc.replaceAll("(\r\n|\r|\n|\n\r)", "<br>");
        desc = desc.replaceAll("\"", "\\\\\"");
        newContent = newContent.replace("{直播简介}", desc);
        //替换会员注册页面地址
        String registerSiteIdReg = "\\{" + "registerSiteId" + "}";
        String loginSiteIdReg = "\\{" + "loginSiteId" + "}";
        newContent = newContent.replaceAll(registerSiteIdReg, memberConfigEntity.getRegistrationSiteId());
        newContent = newContent.replaceAll(loginSiteIdReg, memberConfigEntity.getLoginSiteId());
        //替换观看地址
        newContent = newContent.replace("{liveUrl}", liveEntity.getShortViewUrl());
        //替换市场活动id
        newContent = newContent.replace("{marketingEventId}", liveEntity.getMarketingEventId());
        //替换直播id
        newContent = newContent.replace("{liveId}", liveEntity.getId());
        if (liveEntity.getPlatform() == LivePlatformEnum.CHANNELS.getType()){
            HexagonTemplateSiteEntity marketingTemplate = hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(ea, HexagonTemplateTypeEnum.LIVE.getType());
            if (marketingTemplate != null) {
                HexagonTemplatePageEntity templatePage = hexagonTemplatePageDAO.getHomePage(marketingTemplate.getId());
                String formPageId = null;
                List<HexagonPageEntity> pageEntities = hexagonPageDAO.getByHexagonSiteId(siteId);
                for (HexagonPageEntity entity : pageEntities){
                    if (org.apache.commons.lang3.StringUtils.equals(templatePage.getName(), entity.getName())){
                        formPageId = entity.getId();
                        break;
                    }
                }
                if (formPageId != null){
                    newContent = newContent.replace("{SignupPageId}", formPageId);
                }
            }
            Result<ChannelsAccountResult> account = channelsManager.getAccount(ea,liveEntity.getAssociatedAccountId());
            ChannelsAccountResult accountEntity = account.getData();
            newContent = newContent.replace("{ChannelsAvatar}", accountEntity.getChannelsAvatar());
            newContent = newContent.replace("{ChannelsName}", accountEntity.getChannelsName());
            String cover =  fileV2Manager.getUrlByPath(liveEntity.getCover(),ea,false);
            newContent = newContent.replace("{封面图片}",cover);
            newContent = newContent.replace("{objectId}", liveEntity.getId());
            newContent = newContent.replace("{直播活动名称}", title);
            if(memberManager.isOpenMember(ea)){
                newContent = newContent.replace("\"{会员自动登入}\"", "true");
            }else {
                newContent = newContent.replace("\"{会员自动登入}\"", "false");
            }
        }
        newContent = UnicodeFormatter.decodeUnicodeString(newContent);

        HexagonPageEntity updateEntity = BeanUtil.copy(hexagonPageEntity, HexagonPageEntity.class);
        updateEntity.setId(hexagonPageEntity.getId());
        updateEntity.setName(title);
        updateEntity.setShareTitle(title);
        updateEntity.setContent(newContent);
        updateEntity.setShareDesc(newDesc);
        updateEntity.setSharePicH5Apath(coverPath);
        updateEntity.setSharePicMpApath(coverPath);
        hexagonPageDAO.update(updateEntity);
        return updateEntity;
    }


    // 现在这里接口只有web端调用了！  移动端的看 appList，该接口标签筛选和其他筛选条件存在互斥
    @Override
    public Result<PageResult<ListResult>> list(ListVO vo) {
        PageResult<ListResult> pageResult = new PageResult<>();
        List<ListResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setResult(queryLiveListResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);

        Integer fsUserId = vo.getFsUserId();
        //按照CRM数据权限拉取
        List<MarketingLiveEntity> marketingLiveEntityList = null;
        int totalNum = 0;
        Map<String, String> lifeStatusMap = Maps.newHashMap();
        if (vo.getMaterialTagFilter() != null && vo.getMaterialTagFilter().checkValid()) {
            // 内容标签过滤处理
            Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
            Integer type = vo.getMaterialTagFilter().getType();
            List<String> materialTagIds = vo.getMaterialTagFilter().getMaterialTagIds();
            List<String> ids;
            if (type == 1) {
                ids = materialTagRelationDao.queryByAnyTags(vo.getEa(), materialTagIds, Lists.newArrayList(ObjectTypeEnum.LIVE.getType()), page);
            } else {
                ids = materialTagRelationDao.queryByAllTags(vo.getEa(), materialTagIds, Lists.newArrayList(ObjectTypeEnum.LIVE.getType()), page);
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(ids)) {
                return Result.newSuccess(pageResult);
            }
            pageResult.setTotalCount(page.getTotalNum());
            ListBriefMarketingEventsArg batchGetMktEventArg = new ListBriefMarketingEventsArg();
            batchGetMktEventArg.setOrderByList(Lists.newArrayList(new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false)));
            batchGetMktEventArg.setMarketingEventIds(ids);
            batchGetMktEventArg.setEventType(MarketingEventEnum.LIVE_MARKETING.getEventType());
            List<MarketingEventsBriefResult> marketingEventsBriefResults = marketingEventManager.listMarketingEventsV2(eieaConverter.enterpriseAccountToId(vo.getEa()), fsUserId, batchGetMktEventArg);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(marketingEventsBriefResults)) {
                return Result.newSuccess(pageResult);
            }
            List<String> marketingEventIds = marketingEventsBriefResults.stream().map(event -> event.getId()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(marketingEventIds)) {
                return Result.newSuccess(pageResult);
            }
            Page page2 = new Page(1, vo.getPageSize(), true);
            marketingLiveEntityList = marketingLiveDAO.pageLiveEnrollsByEaAndStatus(null, null, eieaConverter.enterpriseAccountToId(vo.getEa()), false, page2, marketingEventIds);
        } else {
            if (vo.getIsShowSpread() == null || BooleanUtils.isFalse(vo.getIsShowSpread())){
                ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
                PageArg pageArg = new PageArg(vo.getPageNum(), vo.getPageSize());
                arg.setEventType(MarketingEventEnum.LIVE_MARKETING.getEventType());
                arg.setName(vo.getKeyword());
                ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false);
                arg.setOrderByList(Lists.newArrayList(orderBy));
                arg.setFilterData(vo.getFilterData());
                com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResults =
                        marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(vo.getEa()), fsUserId, arg, pageArg);
                if (marketingEventsBriefResults == null || org.apache.commons.collections4.CollectionUtils.isEmpty(marketingEventsBriefResults.getData())) {
                    return Result.newSuccess(pageResult);
                }
                List<String> marketingEventIds = marketingEventsBriefResults.getData().stream().map(event -> event.getId()).collect(Collectors.toList());
                lifeStatusMap = marketingEventsBriefResults.getData().stream().collect(Collectors.toMap(MarketingEventsBriefResult::getId,MarketingEventsBriefResult::getLifeStatus,(v1,v2)->v1));
                pageResult.setTotalCount(marketingEventsBriefResults.getTotalCount());
                List<MarketingEventsBriefResult> notExistMarketingEventList = Lists.newArrayList();
                List<MarketingLiveEntity> liveEntityList = marketingLiveDAO.getLiveByEaAndMarketingEventIds(eieaConverter.enterpriseAccountToId(vo.getEa()),marketingEventIds);
                Map<String, MarketingLiveEntity> marketingEventIdLiveEntityMap = new HashMap<>();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(liveEntityList)){
                    marketingEventIdLiveEntityMap = liveEntityList.stream().collect(Collectors.toMap(MarketingLiveEntity::getMarketingEventId, v->v, (v1, v2)->v1));
                }
                for (MarketingEventsBriefResult marketingEventsBriefResult : marketingEventsBriefResults.getData()){
                    if (marketingEventIdLiveEntityMap.get(marketingEventsBriefResult.getId()) == null){
                        notExistMarketingEventList.add(marketingEventsBriefResult);
                    }
                }
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(notExistMarketingEventList)) {
                    //初始化市场活动中存在，但是营销通不存在的直播
                    initCrmOldMarketingLive(vo.getEa(), notExistMarketingEventList);
                    //重新拉取会议列表
                    liveEntityList = marketingLiveDAO.getLiveByEaAndMarketingEventIds(eieaConverter.enterpriseAccountToId(vo.getEa()),marketingEventIds);
                    for (MarketingLiveEntity liveEntity : liveEntityList){
                        if (marketingEventIdLiveEntityMap.get(liveEntity.getMarketingEventId()) == null) {
                            marketingEventIdLiveEntityMap.put(liveEntity.getMarketingEventId(), liveEntity);
                        }
                    }
                }
                List<MarketingLiveEntity> newLiveList = Lists.newArrayList();
                for (MarketingEventsBriefResult marketingEventsBriefResult : marketingEventsBriefResults.getData()){
                    newLiveList.add(marketingEventIdLiveEntityMap.get(marketingEventsBriefResult.getId()));
                }
                marketingLiveEntityList = newLiveList;
                totalNum = marketingEventsBriefResults.getTotalCount();
            }else{
                List<MarketingLiveEntity> liveEntityList = Lists.newArrayList();
                Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
                //对象选择器获取市场活动id
                if (vo.getFilterData() != null && vo.getFilterData().getQuery() != null) {
                    //筛选出市场活动id
                    ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
                    arg.setEventType(MarketingEventEnum.LIVE_MARKETING.getEventType());
                    ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.BEGIN_TIME, false);
                    arg.setOrderByList(Lists.newArrayList(orderBy));
                    arg.setFilterData(vo.getFilterData());
                    List<MarketingEventsBriefResult> marketingEventsBriefResults = marketingEventManager.listMarketingEvents(eieaConverter.enterpriseAccountToId(vo.getEa()), fsUserId, arg);
                    if (CollectionUtils.isEmpty(marketingEventsBriefResults)) {
                        return Result.newSuccess(pageResult);
                    }
                    List<String> marketingEventIds = marketingEventsBriefResults.stream().map(event -> event.getId()).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(marketingEventIds)) {
                        return Result.newSuccess(pageResult);
                    }
                    liveEntityList = marketingLiveDAO.pageLiveEnrollsByEaAndStatus(vo.getKeyword(), vo.getStatusList(), eieaConverter.enterpriseAccountToId(vo.getEa()), vo.getIsShowSpread(), page,marketingEventIds);
                } else {
                    liveEntityList = marketingLiveDAO.pageLiveEnrollsByEaAndStatus(vo.getKeyword(), vo.getStatusList(), eieaConverter.enterpriseAccountToId(vo.getEa()), vo.getIsShowSpread(), page,null);
                }
                if (CollectionUtils.isEmpty(liveEntityList)){
                    return Result.newSuccess(pageResult);
                }
                marketingLiveEntityList = liveEntityList;
                totalNum = page.getTotalNum();
            }
            pageResult.setTotalCount(totalNum);
        }

        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return Result.newSuccess(pageResult);
        }

        queryLiveListResult = buildLiveResult(vo, marketingLiveEntityList, lifeStatusMap);
        pageResult.setResult(queryLiveListResult);

        return Result.newSuccess(pageResult);
    }

    private  List<ListResult> buildLiveResult(ListVO vo, List<MarketingLiveEntity> marketingLiveEntityList, Map<String, String> lifeStatusMap) {
        if (CollectionUtils.isEmpty(marketingLiveEntityList)) {
            return Lists.newArrayList();
        }
        List<ListResult> queryLiveListResult = Lists.newArrayList();
        List<String> maketingEventIds = Lists.newArrayList();
        List<String> otherLiveMaketingEventIds = Lists.newArrayList();
        List<Integer> vhallLiveIds = Lists.newArrayList();
        Set<String> xiaoetongLiveIds = Sets.newHashSet();
        Set<String> polyvLiveIds = Sets.newHashSet();
        Map<Integer, ListResult> vhallLiveMap = new HashMap<>();
        Map<String, ListResult> xiaoetongLiveMap = new HashMap<>();
        Map<String, ListResult> polyvLiveMap = new HashMap<>();
        List<String> apath = Lists.newArrayList();
        apath.add(defaultLiveCover);
        List<String> marketingEventIds = marketingLiveEntityList.stream().map(MarketingLiveEntity::getMarketingEventId).collect(Collectors.toList());
        List<String> hexagonIds = marketingLiveEntityList.stream().map(MarketingLiveEntity::getFormHexagonId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, HexagonBaseInfoDTO> hexagonBaseInfoDTOMap = hexagonManager.getHexagonBaseInfoById(hexagonIds, vo.getEa());
        List<CampaignStatisticDTO> queryEnrollCountList = campaignMergeDataDAO.queryCampaignUserCountByMarketingEventIds(vo.getEa(), marketingEventIds);
        Map<String, Integer> enrollMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(queryEnrollCountList)) {
            enrollMap = queryEnrollCountList.stream().collect(Collectors.toMap(CampaignStatisticDTO::getMarketingEventId, CampaignStatisticDTO::getUserCount, (v1, v2) -> v1));
        }
        // 查询标签
        List<String> objectIds = marketingLiveEntityList.stream().map(MarketingLiveEntity::getMarketingEventId).collect(Collectors.toList());
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(objectIds, ObjectTypeEnum.LIVE.getType());
        for (MarketingLiveEntity entity : marketingLiveEntityList){
            ListResult listResult = new ListResult();
            listResult.setId(entity.getId());
            listResult.setMarketingEventId(entity.getMarketingEventId());
            listResult.setTitle(entity.getTitle());
            listResult.setStartTime(entity.getStartTime().getTime());
            listResult.setEndTime(entity.getEndTime().getTime());
            listResult.setPlatform(entity.getPlatform());
            listResult.setViewUrl(entity.getShortViewUrl());
            listResult.setCover(entity.getCover());
            listResult.setDescription(entity.getDescription());
            listResult.setStatus(entity.getStatus());
            listResult.setSubmitHexagonId(entity.getFormHexagonId());
            listResult.setMuduParentId(entity.getMuduParentId());
            listResult.setSubEvent(entity.getSubEvent());
            listResult.setLifeStatus(lifeStatusMap.get(entity.getMarketingEventId()));
            if (hexagonBaseInfoDTOMap != null && hexagonBaseInfoDTOMap.get(entity.getFormHexagonId()) != null){
                listResult.setSubmitHexagonName(hexagonBaseInfoDTOMap.get(entity.getFormHexagonId()).getTitle());
            }
            if (entity.getPlatform() != null &&
                    (entity.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType() || entity.getPlatform() == LivePlatformEnum.XIAOETONG.getType()
                            || entity.getPlatform() == LivePlatformEnum.POLYV.getType()||listResult.getPlatform().intValue() == LivePlatformEnum.CHANNELS.getType()
                            || listResult.getPlatform().intValue() == LivePlatformEnum.MUDU.getType() || listResult.getPlatform().intValue() == LivePlatformEnum.VHALL.getType())){
                apath.add(entity.getCover());
            }
            if (entity.getStatus() != null && entity.getStatus() == LiveStatusEnum.FINISH_WITH_REPLAY.getStatus()){
                listResult.setHasRecord(true);
            }else {
                listResult.setHasRecord(false);
            }

            if (entity.getPlatform() == null || entity.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType()|| entity.getPlatform().intValue() == LivePlatformEnum.CHANNELS.getType()) {
                Long startTime = entity.getStartTime().getTime();
                Long endTime = entity.getEndTime().getTime();
                Long now = System.currentTimeMillis();
                if (startTime > now) {
                    listResult.setStatus(LiveStatusEnum.NOT_START.getStatus());
                } else if (now > startTime && now < endTime) {
                    listResult.setStatus(LiveStatusEnum.PROCESSING.getStatus());
                } else {
                    listResult.setStatus(LiveStatusEnum.FINISH.getStatus());
                }
            }

            maketingEventIds.add(entity.getMarketingEventId());
            Integer enrollCount = enrollMap.get(entity.getMarketingEventId());
            listResult.setEnrollCount(enrollCount != null ? enrollCount : 0);
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(entity.getMarketingEventId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                listResult.setMaterialTags(collect);
            }
            queryLiveListResult.add(listResult);
            if (entity.getPlatform() != null && entity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
                vhallLiveIds.add(entity.getLiveId());
                vhallLiveMap.put(entity.getLiveId(), listResult);
            }
            if (entity.getPlatform() != null && entity.getPlatform() == LivePlatformEnum.XIAOETONG.getType()){
                xiaoetongLiveIds.add(entity.getXiaoetongLiveId());
                xiaoetongLiveMap.putIfAbsent(entity.getXiaoetongLiveId(), listResult);
            }
            if (entity.getPlatform() != null && (entity.getPlatform() == LivePlatformEnum.POLYV.getType()
                    || entity.getPlatform() == LivePlatformEnum.MUDU.getType()
                    || entity.getPlatform() == LivePlatformEnum.VHALL.getType())){
                polyvLiveIds.add(entity.getXiaoetongLiveId());
                polyvLiveMap.putIfAbsent(entity.getXiaoetongLiveId(), listResult);
            }
            if (entity.getPlatform() != null && entity.getPlatform() == LivePlatformEnum.OTHER_PLATFORM.getType()){
                otherLiveMaketingEventIds.add(entity.getMarketingEventId());
            }
        }

        Map<String, Integer> marketingEventIdAndCount = new HashMap<>();
        if (CollectionUtils.isNotEmpty(maketingEventIds)) {
            Map<String, Integer> pvMap = marketingEventManager.calMarketingEventsPV(vo.getEa(), maketingEventIds);
            Map<String, Integer> uvMap = marketingEventManager.batchCalMarketingEventUVStatistic(vo.getEa(), maketingEventIds);
            for (ListResult listResult : queryLiveListResult) {
                if (pvMap != null && pvMap.get(listResult.getMarketingEventId()) != null){
                    Integer pv =  pvMap.get(listResult.getMarketingEventId());
                    listResult.setPv(pv == null ? 0 : pv);
                }
                if (uvMap != null && uvMap.get(listResult.getMarketingEventId()) != null) {
                    Integer uv = uvMap.get(listResult.getMarketingEventId());
                    listResult.setUv(uv == null ? 0 : uv);
                }
            }
            List<ObjectData> campaignMembersObjs = campaignMergeDataManager.getCampaignMembersObjByMaketingEventIdsWithFields(vo.getEa(), otherLiveMaketingEventIds);
            if (campaignMembersObjs != null) {
                Map<String, List<ObjectData>> marketingEventIdAndObj = campaignMembersObjs.stream()
                        .collect(Collectors.groupingBy(e -> String.valueOf(e.get("marketing_event_id"))));
                marketingEventIdAndObj.forEach((k, v) -> marketingEventIdAndCount.put(
                        k,
                        v == null ? 0 : (int) v.stream().filter(e -> "1".equals(e.get("marketing_watch_stauts")) || "1".equals(e.get("marketing_playback_status"))).count()
                ));
            }

        }

        //外部直播封面
        if (CollectionUtils.isNotEmpty(apath)) {
            Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(apath, vo.getEa(), false);
            if (urlMap != null) {
                for (ListResult listResult : queryLiveListResult) {
                    if (listResult.getPlatform() != null &&
                            (listResult.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType() || listResult.getPlatform() == LivePlatformEnum.XIAOETONG.getType()
                                    || listResult.getPlatform() == LivePlatformEnum.POLYV.getType()||listResult.getPlatform().intValue() == LivePlatformEnum.CHANNELS.getType()
                                    || listResult.getPlatform().intValue() == LivePlatformEnum.MUDU.getType() || listResult.getPlatform().intValue() == LivePlatformEnum.VHALL.getType())) {
                        listResult.setCover(urlMap.get(listResult.getCover()));
                    }
                    if (listResult.getCover() == null){
                        listResult.setCover(urlMap.get(defaultLiveCover));
                    }
                    if (listResult.getPlatform() != null && LivePlatformEnum.OTHER_PLATFORM.getType() == listResult.getPlatform()) {
                        Integer totalViewUsers = marketingEventIdAndCount.get(listResult.getMarketingEventId());
                        listResult.setTotalViewUsers(totalViewUsers == null ? 0 : totalViewUsers);
                    }
                }
            }
        }

        //直播统计数据
        if (CollectionUtils.isNotEmpty(vhallLiveIds)) {
            List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getLiveStatByLiveIds(vhallLiveIds);
            if (marketingLiveStatisticsList != null) {
                for (MarketingLiveStatistics statistics : marketingLiveStatisticsList) {
                    ListResult listResult = vhallLiveMap.get(statistics.getLiveId());
                    if (listResult != null) {
                        listResult.setStatus(statistics.getStatus());
                        listResult.setTotalViewUsers(statistics.getTotalViewUsers());
                        listResult.setChatTimes(statistics.getChatTimes());
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(xiaoetongLiveIds)){
            List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getLiveStatByXiaoetongLiveIds(new ArrayList<>(xiaoetongLiveIds));
            for (MarketingLiveStatistics statistics : marketingLiveStatisticsList) {
                ListResult listResult = xiaoetongLiveMap.get(statistics.getXiaoetongLiveId());
                if (listResult != null) {
                    listResult.setStatus(statistics.getStatus());
                    listResult.setTotalViewUsers(statistics.getTotalViewUsers());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(polyvLiveIds)){
            List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getLiveStatByXiaoetongLiveIds(new ArrayList<>(polyvLiveIds));
            for (MarketingLiveStatistics statistics : marketingLiveStatisticsList) {
                ListResult listResult = polyvLiveMap.get(statistics.getXiaoetongLiveId());
                if (listResult != null) {
                    listResult.setStatus(statistics.getStatus());
                    listResult.setTotalViewUsers(statistics.getTotalViewUsers());
                    listResult.setStayCount(statistics.getTotalViewUsers());
                }
            }
        }
        return queryLiveListResult;
    }

    @Override
    public Result<PageResult<ListResult>> appList(ListVO arg) {

        // 当前只支持移动端，筛选条件不互斥
        if (StringUtils.isBlank(arg.getMenuId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String ea = arg.getEa();
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilter("is_mobile_display", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("0"));
        query.addFilter("life_status", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("normal"));
        //添加数据权限筛选条件
        Result<DataPermissionResult> dataPermissionResult = dataPermissionService.getDataPermission(ea, arg.getFsUserId());
        if (dataPermissionResult.isSuccess() && dataPermissionResult.getData() != null && dataPermissionResult.getData().isStatus()) {
            List<Integer> dataDepartments = dataPermissionResult.getData().getDataDepartments();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataDepartments)) {
                query.addFilter("data_own_organization", "IN", dataDepartments.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }
        filterData.setQuery(query);
        arg.setFilterData(filterData);

        Result<PaasObjectRuleVO>  paasObjectRuleResult = appMenuTemplateService.getPaasObjectRule(ea, arg.getMenuId(), ObjectTypeEnum.LIVE.getType());

        PageResult<ListResult> pageResult = new PageResult<>();
        List<ListResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setResult(queryLiveListResult);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        if (!paasObjectRuleResult.isSuccess()) {
            log.info("live listV2 paasObjectRuleResult is not success, arg:{}, result:{}", arg, paasObjectRuleResult);
            return Result.newSuccess(pageResult);
        }

        ListBriefMarketingEventsArg batchGetMktEventArg = new ListBriefMarketingEventsArg();
        batchGetMktEventArg.setOrderByList(Lists.newArrayList(new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false)));
        batchGetMktEventArg.setEventType(MarketingEventEnum.LIVE_MARKETING.getEventType());
        batchGetMktEventArg.setName(arg.getKeyword());
        batchGetMktEventArg.setFilterData(arg.getFilterData());
        arg.setFilterData(filterData);
        // 默认走管理员权限
        int queryFsUserId = SuperUserConstants.USER_ID;
        Integer totalCount = null;
        Page<String> tagPage = null;
        PaasObjectRuleVO paasObjectRuleVO = paasObjectRuleResult.getData();
        if (paasObjectRuleVO.getAppMenuTagVO() != null) {
            AppMenuTagVO appMenuTagVO = paasObjectRuleVO.getAppMenuTagVO();
            if (StringUtils.isBlank(arg.getKeyword()) && CollectionUtils.isEmpty(arg.getStatusList())) {
                tagPage = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
            }
            Integer type = appMenuTagVO.getTagOperator();
            List<String> materialTagIds = appMenuTagVO.getTagIdList();
            List<String> ids;
            if (type == 1) {
                ids = materialTagRelationDao.queryByAnyTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.LIVE.getType()), tagPage);
            } else {
                ids = materialTagRelationDao.queryByAllTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.LIVE.getType()), tagPage);
            }
            if (CollectionUtils.isEmpty(ids)) {
                return Result.newSuccess(pageResult);
            }
            totalCount = tagPage == null ? null : tagPage.getTotalNum();
            batchGetMktEventArg.setMarketingEventIds(ids);
        } else if (AppMenuAccessibleRuleEnum.PAAS_OBJECT_ACCESSIBLE.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            queryFsUserId = arg.getFsUserId();;
        } else if (AppMenuAccessibleRuleEnum.OBJECT_FILTER.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            // 对象条件
            query.getFilters().addAll(paasObjectRuleVO.getFilters());
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        List<MarketingLiveEntity> marketingLiveEntityList;
        if (CollectionUtils.isEmpty(arg.getStatusList())) {
            // 如果没传状态，走paas的分页
            PageArg pageArg;
            if (tagPage != null) {
                // 已经通过标签分页了，这里就分页了
                pageArg = new PageArg(1, arg.getPageSize());
            } else {
                pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
            }
            com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResults =
                    marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(ea), queryFsUserId, batchGetMktEventArg, pageArg);
            if (marketingEventsBriefResults == null || CollectionUtils.isEmpty(marketingEventsBriefResults.getData())) {
                return Result.newSuccess(pageResult);
            }
            totalCount = totalCount == null ? marketingEventsBriefResults.getTotalCount() : totalCount;
            pageResult.setTotalCount(totalCount);
            List<String> marketingEventIdList = marketingEventsBriefResults.getData().stream().map(MarketingEventsBriefResult::getId).collect(Collectors.toList());
            marketingLiveEntityList = marketingLiveDAO.pageLiveEnrollsByEaAndStatus(null, null, ei, true, null, marketingEventIdList);

        } else {
            List<MarketingEventsBriefResult> marketingEventsBriefResults = marketingEventManager.listMarketingEvents(ei, queryFsUserId, batchGetMktEventArg);
            if (CollectionUtils.isEmpty(marketingEventsBriefResults)) {
                return Result.newSuccess(pageResult);
            }
            List<String> marketingEventIdList = marketingEventsBriefResults.stream().map(MarketingEventsBriefResult::getId).collect(Collectors.toList());
            Page<MarketingLiveEntity> page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
            marketingLiveEntityList = marketingLiveDAO.pageLiveEnrollsByEaAndStatus(null, arg.getStatusList(), ei, true, page, marketingEventIdList);
            pageResult.setTotalCount(page.getTotalNum());
        }
        // 按照以前的逻辑，移动端并不会去初始化市场活动中存在，但是营销通不存在的直播
        queryLiveListResult = buildLiveResult(arg, marketingLiveEntityList, Maps.newHashMap());
        pageResult.setResult(queryLiveListResult);

        return Result.newSuccess(pageResult);
    }

    private void initCrmOldMarketingLive(String ea, List<MarketingEventsBriefResult> marketingEventResults){
        if (CollectionUtils.isEmpty(marketingEventResults)){
            return;
        }

        Integer corpId = eieaConverter.enterpriseAccountToId(ea);
        for (MarketingEventsBriefResult marketingEvent : marketingEventResults){
            if (marketingLiveDAO.getByCorpIdAndMarketingEventId(corpId, marketingEvent.getId()) != null){
                continue;
            }
            MarketingLiveEntity entity = new MarketingLiveEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setCorpId(eieaConverter.enterpriseAccountToId(ea));
            entity.setMarketingEventId(marketingEvent.getId());
            entity.setTitle(marketingEvent.getName());
            entity.setCreateUserId(marketingEvent.getCreateBy());
            entity.setStatus(LiveStatusEnum.NOT_START.getStatus());
            entity.setCreateTime(new Date(marketingEvent.getCreateTime()));
            entity.setUpdateTime(new Date(marketingEvent.getCreateTime()));
            entity.setStartTime(marketingEvent.getBeginTime() == null ? DateUtil.now() : DateUtil.fromTimestamp(marketingEvent.getBeginTime()));
            entity.setEndTime(marketingEvent.getEndTime() == null ? DateUtil.now() : DateUtil.fromTimestamp(marketingEvent.getEndTime()));

            if (entity.getStartTime().getTime() < DateUtil.now().getTime()){
                entity.setStatus(LiveStatusEnum.NOT_START.getStatus());
            }else if (entity.getEndTime().getTime() < DateUtil.now().getTime()){
                entity.setStatus(LiveStatusEnum.FINISH.getStatus());
            }else {
                entity.setStatus(LiveStatusEnum.PROCESSING.getStatus());
            }
            entity.setShowAcitivityList(false);
            log.info("add init marketing live entity:{}", entity);
            marketingLiveDAO.addMarketingLive(entity);
        }
    }

    @Override
    public Result<GetLiveDetailResult> getDetail(String enterpriseAccount, String id, int role, Integer identityCheckType, Map<String, String> allMemberCookieInfos, String wxAppId, String openId) {
        MarketingLiveEntity entity = null;
        String ea = enterpriseAccount;
        if (role ==  LiveRoleEnum.VIEW_ROLE.getRole()) {
            entity = marketingLiveDAO.getById(id);
            if (entity == null) {
                log.error("LiveServiceImpl.updateLive failed live is not exist id:{}", id);
                return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
            }
            ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
        }else {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            entity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, id);
        }
        if (entity == null) {
            log.error("LiveServiceImpl.updateLive failed live is not exist id:{}", id);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        GetLiveDetailResult  result = new GetLiveDetailResult();
        result.setId(entity.getId());
        result.setMarketingEventId(entity.getMarketingEventId());
        result.setTitle(entity.getTitle());
        result.setDesc(entity.getDescription());
        if (entity.getStartTime() != null) {
            result.setStartTime(entity.getStartTime().getTime());
        }
        if (entity.getEndTime() != null) {
            result.setEndTime(entity.getEndTime().getTime());
        }
        result.setLectureUserName(entity.getLectureName());
        result.setLecturePassword(entity.getLecturePassword());
        if (entity.getTags() != null){
            TagNameList tag = gson.fromJson(entity.getTags(), TagNameList.class);
            result.setTagNames(tag);
        }
        result.setStatus(entity.getStatus());
        result.setCoverTaPath(entity.getCover());
        result.setChatOn(entity.getChatOn());
        result.setAutoRecord(entity.getAutoRecord());
        result.setMaxLiveCount(entity.getMaxLiveCount());
        result.setLivePlatform(entity.getPlatform());
        result.setLectureUrl(entity.getShortLectureUrl());
        result.setFormHexagonId(entity.getFormHexagonId());
        result.setVhallId(entity.getLiveId());
        result.setSubEvent(entity.getSubEvent());
        result.setAssociatedAccountId(entity.getAssociatedAccountId());
        if (entity.getPlatform() != null && (LivePlatformEnum.userXiaoetongId(entity.getPlatform()))) {
            result.setXiaoetongLiveId(entity.getXiaoetongLiveId());
            result.setXiaoetongUrl(entity.getViewUrl());
        }
        if (entity.getShowAcitivityList() == null){
            result.setShowActivityList(false);
        }else {
            result.setShowActivityList(entity.getShowAcitivityList());
        }
        MarketingLiveStatistics marketingLiveStatistics =  marketingLiveStatisticsDAO.getByLiveId(entity.getLiveId());
        if (marketingLiveStatistics != null){
            result.setStatus(marketingLiveStatistics.getStatus());
        }
        if (entity.getLecturePassword() != null){
            result.setHasLecturePassword(true);
        }

        //其他直播平台才可以更改直播状态
        if (entity.getPlatform() == null || entity.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType() || entity.getPlatform().intValue() == LivePlatformEnum.CHANNELS.getType()){
            Long startTime = entity.getStartTime().getTime();
            Long endTime = entity.getEndTime().getTime();
            Long now = System.currentTimeMillis();
            if (startTime > now){
                result.setStatus(LiveStatusEnum.NOT_START.getStatus());
            }else if (now > startTime && now < endTime){
                result.setStatus(LiveStatusEnum.PROCESSING.getStatus());
            }else {
                result.setStatus(LiveStatusEnum.FINISH.getStatus());
            }
        }

        if (entity.getPlatform() != null
                && (entity.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType() || entity.getPlatform() == LivePlatformEnum.XIAOETONG.getType()
                || entity.getPlatform() == LivePlatformEnum.POLYV.getType() || entity.getPlatform() == LivePlatformEnum.CHANNELS.getType()
                || entity.getPlatform() == LivePlatformEnum.MUDU.getType() || entity.getPlatform() == LivePlatformEnum.VHALL.getType())) {
            List<String> apaths = Lists.newArrayList();
            apaths.add(entity.getCover());
            Integer coprId = entity.getCorpId();
            Map<String, String> pathMap = fileV2Manager.batchGetUrlByPath(apaths, ea, false);
            if (pathMap != null){
                result.setCoverTaPath(pathMap.get(entity.getCover()));
            }
            result.setOtherPlatformLiveUrl(entity.getOtherPlatformUrl());
        }

        //判断是否会员
        if (role ==  LiveRoleEnum.VIEW_ROLE.getRole()) {
            Result<String> checkIsMemberResult = null;
            if (identityCheckType == IdentityCheckTypeEnum.OFFICIAL_ACCOUNTS.type){
                checkIsMemberResult = memberService.checkWxServiceUserIsMember(ea, wxAppId, openId);
            }else if (identityCheckType == IdentityCheckTypeEnum.BROWSER_FINGERPRINT.type){
                checkIsMemberResult = memberService.checkH5UserIsMember(ea, allMemberCookieInfos);
            }

            if (checkIsMemberResult != null && checkIsMemberResult.isSuccess() && checkIsMemberResult.getData() != null) {
                String viewIframeUrl = "https://e.vhall.com/webinar/inituser/" + entity.getLiveId();
                result.setViewUrl(viewIframeUrl);
                ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), checkIsMemberResult.getData());
                if (objectData != null){
                    result.setMemberId(checkIsMemberResult.getData());
                    result.setMemberName((String)objectData.get(CrmMemberFieldEnum.NAME.getApiName()));
                    result.setMemberPhone((String)objectData.get(CrmMemberFieldEnum.PHONE.getApiName()));
                }
            }
        }else {
            result.setViewUrl(entity.getShortViewUrl());
        }

        if (result.getCoverTaPath() == null){
           result.setCoverTaPath(fileV2Manager.getUrlByPath(defaultLiveCover, ea, false));
        }

        if (role == LiveRoleEnum.LECTURE_ROLE.getRole()) {
            ObjectData obj = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), entity.getMarketingEventId());
            if (obj != null && StringUtils.equals("1", obj.getString("lock_status"))) {
                result.setEditable(false);
            }else {
                result.setEditable(true);
            }
        }

        //报名跳转地址
        ObjectEnrollJumpSettingEntity objectEnrollJumpSettingEntity = objectEnrollJumpSettingDAO.getByMarketingEventId(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), entity.getMarketingEventId());
        if (objectEnrollJumpSettingEntity != null){
            result.setJumpObjectId(objectEnrollJumpSettingEntity.getJumpObjectId());
            result.setJumpObjectType(objectEnrollJumpSettingEntity.getJumpObjectType());
            result.setJumpUrl(objectEnrollJumpSettingEntity.getJumpUrl());
        }
        if(result != null && org.apache.commons.lang3.StringUtils.isNotBlank(result.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), result.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), result.getId());
            if (coverCutH5PhotoEntity != null) {
                result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), result.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetLiveDetailResult> getParentDetail(String ea, String id, int role, Integer identityCheckType, Map<String, String> allMemberCookieInfos, String wxAppId, String openId) {
        // 查询父级直播活动
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(id);
        if (marketingLiveEntity == null) {
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        String muduParentId = marketingLiveEntity.getMuduParentId();
        MarketingLiveEntity parentEntity = marketingLiveDAO.getMarketingLiveByXiaoetongId(muduParentId);
        if (parentEntity == null) {
            return Result.newError(SHErrorCode.MUDU_NOT_EXISTS_PARENT);
        }
        return getDetail(ea, parentEntity.getId(), role, identityCheckType, allMemberCookieInfos, wxAppId, openId);
    }

    @Override
    public Result<LiveLectureUrlResult> getLectureUrl(String id, String lecturePassword,Integer type) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(id);
        if (entity == null) {
            log.error("LiveServiceImpl.updateLive failed live is not exist id:{}", id);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        LiveLectureUrlResult result = new LiveLectureUrlResult();
        if (null != type && LivePlatformEnum.POLYV.getType() == type) {
            PolyvLiveInnerData liveDetail = polyvManager.getLiveDetail(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), entity.getXiaoetongLiveId());
            if (null != liveDetail && liveDetail.getCode() == 200) {
                result.setLectureUrl("https://live.polyv.net/web-start/classroom?channelId=" + entity.getXiaoetongLiveId());
                result.setPassword(liveDetail.getData().getChannelPasswd());
            }
        } else if (null != type && LivePlatformEnum.VHALL.getType() == type){
            VHallApiGetRoleUrlResult getRoleUrlResult = vHallManager.getRoleUrl(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), entity.getXiaoetongLiveId());
            if (getRoleUrlResult != null) {
                result.setLectureUrl(getRoleUrlResult.getPageUrl());
            }
        } else {
            if (StringUtils.isNotEmpty(entity.getLecturePassword()) && !StringUtils.equals(entity.getLecturePassword(), lecturePassword)) {
                result.setCode(LiveViewLimitEnum.PASSSWORD_ERROR.getStatus());
                return Result.newSuccess(result);
            }
            result.setLectureUrl(entity.getLectureUrl());
            result.setPassword(entity.getLecturePassword());
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<LiveBriefStatisticsResult> getLiveBriefStatistics(String ea, String marketingEventId, List<Integer> objectTypes) {
        return liveManager.getLiveStatistics(ea, marketingEventId);
    }

    @Override
    @Deprecated
    public Result<PageResult<QueryFormUserDataResult>> getLiveLeadDetail(LiveLeadDetailVO vo) {
        QueryMultipleFormUserDataArg arg = new QueryMultipleFormUserDataArg();
        arg.setEa(vo.getEa());
        arg.setPageNum(vo.getPageNum());
        arg.setPageSize(vo.getPageSize());
        arg.setSourceType(0);
        arg.setSourceId(vo.getMarketingEventId());
        arg.setKeyword(vo.getKeyword());
        arg.setViewLiveStatus(vo.getViewLiveStatus());
        List<String> leadIds = Lists.newArrayList();
        Map<String, QueryFormUserDataResult> userDataResultMap = new HashMap<>();
        List<String> viewPhones = Lists.newArrayList();
        Map<String, List<QueryFormUserDataResult>> phoneUserDataMap = new HashMap<>();
        Result<PageResult<QueryFormUserDataResult>> formUserDataResult = customizeFormDataService.queryMultipleFormUserData(arg);
        if (formUserDataResult == null || !formUserDataResult.isSuccess() || formUserDataResult.getData() == null
                || CollectionUtils.isEmpty(formUserDataResult.getData().getResult())){
            return formUserDataResult;
        }

        for (QueryFormUserDataResult userDataResult : formUserDataResult.getData().getResult()){
            userDataResult.setReviewStatus(LiveCustomizeUserViewStatusEnum.UN_VIEW.getStatus());
            if (StringUtils.isEmpty(userDataResult.getLeadId())){
                continue;
            }

            leadIds.add(userDataResult.getLeadId());
            userDataResultMap.put(userDataResult.getLeadId(), userDataResult);
            if (userDataResult.getSubmitContent() == null || StringUtils.isEmpty(userDataResult.getSubmitContent().getPhone())) {
                continue;
            }

            viewPhones.add(userDataResult.getSubmitContent().getPhone());
            if (phoneUserDataMap.get(userDataResult.getSubmitContent().getPhone()) == null){
                List<QueryFormUserDataResult> userDataList = Lists.newArrayList();
                userDataList.add(userDataResult);
                phoneUserDataMap.put(userDataResult.getSubmitContent().getPhone(), userDataList);
            }else {
                List<QueryFormUserDataResult> userDataList = phoneUserDataMap.get(userDataResult.getSubmitContent().getPhone());
                userDataList.add(userDataResult);
            }
        }

        if (CollectionUtils.isNotEmpty(viewPhones)) {
            List<MarketingLiveViewLoginEntity> viewLoginEntityList = marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(vo.getMarketingEventId(), viewPhones);
            if (CollectionUtils.isNotEmpty(viewLoginEntityList)){
                for (MarketingLiveViewLoginEntity viewLoginEntity : viewLoginEntityList){
                    List<QueryFormUserDataResult> userDataList = phoneUserDataMap.get(viewLoginEntity.getPhone());
                    if (CollectionUtils.isNotEmpty(userDataList)){
                        userDataList.forEach(userData ->userData.setReviewStatus(LiveCustomizeUserViewStatusEnum.VIEWED.getStatus()));
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(leadIds)){
            List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataList = metadataTagManager
                    .getObjectDataIdAndTagNameListDatasByObjectDataIds(vo.getEa(), CrmObjectApiNameEnum.CRM_LEAD.getName(), leadIds);
            Map<String, ObjectDataIdAndTagNameListData> tagNameListDataMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(objectDataIdAndTagNameListDataList)){
                for (ObjectDataIdAndTagNameListData tagNameListData : objectDataIdAndTagNameListDataList){
                    tagNameListDataMap.put(tagNameListData.getDataId(), tagNameListData);
                }
            }
            for (QueryFormUserDataResult userDataResult : formUserDataResult.getData().getResult()){
                ObjectDataIdAndTagNameListData tagNameListData = tagNameListDataMap.get(userDataResult.getLeadId());
                if (tagNameListData != null){
                    userDataResult.setTagNameList(tagNameListData.getTagNameList());
                }
            }
        }

        /**
         * 微吼接口超时太严重
        //查询直播时长
        setLiveViewDuration(vo.getMarketingEventId(), formUserDataResult.getData().getResult(), viewPhones);
        //直播互动次数
        setLiveChatCount(vo.getMarketingEventId(), formUserDataResult.getData().getResult(), viewPhones);
        */

        return formUserDataResult;
    }

    @Override
    public Result<Double> queryLeftFlow(Integer corpId) {
        try {
            com.facishare.training.common.result.Result<Double> result = liveCommonService.queryLeftFlow(corpId, LiveChannelEnum.MARKETING.getChannel());
            if (result == null || !result.isSuccess()) {
                return Result.newSuccess(0.0);
            }
            return Result.newSuccess(result.getData());
        }catch (Exception e){
            return Result.newSuccess(0.0);
        }
    }

    @Override
    public Result<GetViewUrlResult> getViewUrl(GetLiveViewUrlVO arg) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(arg.getId());
        if (marketingLiveEntity == null){
            log.error("LiveServiceImpl.getViewUrl failed live is not exist id:{}", arg.getId());
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        GetViewUrlResult result = new GetViewUrlResult();
        if (marketingLiveEntity.getStatus().intValue() == LiveStatusEnum.DELETE.getStatus()){
            log.error("LiveServiceImpl.getViewUrl failed live is delete arg:{}", arg);
            return Result.newError(SHErrorCode.MARKETING_LIVE_DELETE);
        }

        String ea = eieaConverter.enterpriseIdToAccount(marketingLiveEntity.getCorpId());
        //判断用户是否报名
        List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByPhone(eieaConverter.enterpriseIdToAccount(marketingLiveEntity.getCorpId()), marketingLiveEntity.getMarketingEventId(), arg.getPhone(), false);
        if (campaignMergeDataEntities == null || campaignMergeDataEntities.isEmpty()){
            log.info("LiveServiceImpl.getViewUrl failed user not submit arg:{}",arg);
            result.setCode(GetViewUrlValidEnum.NOT_ENROLL.getStatus());
            return Result.newSuccess(result);
        }
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntities.get(0);
        ObjectData objectData = crmV2Manager.getObjectData(ea, -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
        if (objectData != null && Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
            log.info("LiveServiceImpl.getViewUrl failed approval_status not pass arg:{}",arg);
            result.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
            return Result.newSuccess(result);
        }

        result.setCode(LiveViewLimitEnum.VALID.getStatus());
        String viewIframeUrl = null;
        //检查是否达到最大观看限制
        if (marketingLiveEntity.getPlatform().intValue() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
            MarketingLiveStatistics marketingLiveStatistics = marketingLiveStatisticsDAO.getByLiveId(marketingLiveEntity.getLiveId());
            if (marketingLiveStatistics != null && marketingLiveStatistics.getViewTimes() >= marketingLiveEntity.getMaxLiveCount() ){
                result.setCode(GetViewUrlValidEnum.MAX_LIMIT.getStatus());
                return Result.newSuccess(result);
            }
            viewIframeUrl = "https://e.vhall.com/webinar/inituser/" + marketingLiveEntity.getLiveId();
        }else {
            viewIframeUrl = marketingLiveEntity.getOtherPlatformUrl();
        }
        if (StringUtils.isNotEmpty(viewIframeUrl) && viewIframeUrl.endsWith("/")){
            viewIframeUrl = viewIframeUrl.substring(0,viewIframeUrl.length() - 1);
        }

        if (StringUtils.isNotEmpty(viewIframeUrl)){
            if (StringUtils.isNotEmpty(arg.getPhone())){
                String email = arg.getPhone() + "@fxiaoke.com";
                viewIframeUrl = viewIframeUrl + "?email=" + email;
            }
            if (StringUtils.isNotEmpty(arg.getName())){
                viewIframeUrl = "&name=" + viewIframeUrl;
            }
        }

        if (result.getCode() == LiveViewLimitEnum.VALID.getStatus()){
            result.setViewUrl(viewIframeUrl);
        }

        ThreadPoolUtils.execute(() -> {
            createRecordActionAndUpdateCampaign(arg, ea, marketingLiveEntity, campaignMergeDataEntity);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        //记录用户的手机号
        List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntityList = marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(marketingLiveEntity.getMarketingEventId(), Lists.newArrayList(arg.getPhone()));
        if (CollectionUtils.isNotEmpty(marketingLiveViewLoginEntityList)){
            return Result.newSuccess(result);
        }
        MarketingLiveViewLoginEntity marketingLiveViewLoginEntity = new MarketingLiveViewLoginEntity();
        marketingLiveViewLoginEntity.setId(UUIDUtil.getUUID());
        marketingLiveViewLoginEntity.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
        marketingLiveViewLoginEntity.setPhone(arg.getPhone());
        marketingLiveViewLoginDAO.insert(marketingLiveViewLoginEntity);

        //打标签&行为积分
        List<String> marketingUserIds = Lists.newArrayList();
        if (marketingLiveEntity.getTags() != null && isLiveGoing(marketingLiveEntity)){
            List<String> leadIds = Lists.newArrayList();
            campaignMergeDataEntities.forEach(cm -> {
                if(CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType().equals(cm.getBindCrmObjectType()) && !Strings.isNullOrEmpty(cm.getBindCrmObjectId())){
                    leadIds.add(cm.getBindCrmObjectId());
                }
            });
            if (CollectionUtils.isNotEmpty(leadIds)){
                for (String lead : leadIds){
                    AssociateCrmLeadModel.AssociateCrmLeadArg associateCrmLeadArg = new AssociateCrmLeadModel.AssociateCrmLeadArg();
                    associateCrmLeadArg.setEa(ea);
                    associateCrmLeadArg.setCrmLeadId(lead);
                    associateCrmLeadArg.setPhone(arg.getPhone());
                    Result<AssociateCrmLeadModel.AssociateCrmLeadResult> associateCrmLeadResultResult = crmLeadMarketingAccountAssociationService.associateCrmLead(associateCrmLeadArg);
                    if (associateCrmLeadResultResult.isSuccess() && associateCrmLeadResultResult.getData() != null){
                        marketingUserIds.add(associateCrmLeadResultResult.getData().getUserMarketingId());
                    }
                    //发送行为积分
                //    sendBehaviorEvent(marketingLiveEntity, lead);
                }
            }
            if (CollectionUtils.isNotEmpty(marketingUserIds)) {
               TagNameList tagNames = GsonUtil.getGson().fromJson(marketingLiveEntity.getTags(), new TypeToken<TagNameList>(){}.getType());
                if (tagNames != null) {
                    userMarketingAccountService.batchAddTagsToUserMarketings(eieaConverter.enterpriseIdToAccount(marketingLiveEntity.getCorpId()),
                            null, null, marketingUserIds, tagNames);
                }
            }

            /*
            //发送行为埋点
            sendActionRecord(marketingLiveEntity);
            */
        }
        return Result.newSuccess(result);
    }

    private void createRecordActionAndUpdateCampaign(GetLiveViewUrlVO arg, String ea, MarketingLiveEntity marketingLiveEntity, CampaignMergeDataEntity campaignMergeDataEntity) {
        Map<String, Object> dataMap = new HashMap<>();
        Date now = new Date();
        long time = 20*60*1000;//20分钟
        Optional<String> browserUserFinger = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, arg.getPhone());
        RecordActionArg recordActionArg = new RecordActionArg();
        recordActionArg.setEa(ea);
        recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
        recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
        recordActionArg.setObjectId(marketingLiveEntity.getId());
        recordActionArg.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
        recordActionArg.setFingerPrint(browserUserFinger.get());
        recordActionArg.setActionTime(new Date().getTime());
        if (new Date(marketingLiveEntity.getStartTime().getTime() - time).before(now) && marketingLiveEntity.getEndTime().after(now)) {
            dataMap.put(CampaignMembersConstants.MARKETING_WATCH_STAUTS, "1");
            recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_LIVE.getActionType());
        } else if (marketingLiveEntity.getEndTime().before(now)) {
            dataMap.put(CampaignMembersConstants.MARKETING_PLAYBACK_STATUS, "1");
            recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_RECORD.getActionType());
        }
        campaignMergeDataManager.updateCampaignMembersObj(ea, campaignMergeDataEntity.getCampaignMembersObjId(), dataMap);
        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
    }

    @Override
    public Result<Boolean> checkUserInMarketingLive(String marketingLiveId, String uid) {
        MarketingLiveEntity marketingLive = marketingLiveDAO.getById(marketingLiveId);
        if(marketingLive == null){
            return Result.newSuccess(false);
        }
        String ea = eieaConverter.enterpriseIdToAccount(marketingLive.getCorpId());
        UserMarketingMiniappAccountRelationEntity entity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, uid);
        if(entity == null || Strings.isNullOrEmpty(entity.getUserMarketingId())){
            return Result.newSuccess(false);
        }
        UserMarketingAccountEntity userMarketingAccount = userMarketingAccountDAO.getById(entity.getUserMarketingId());
        if(userMarketingAccount == null || Strings.isNullOrEmpty(userMarketingAccount.getPhone())){
            return Result.newSuccess(false);
        }
        GetLiveViewUrlVO arg = new GetLiveViewUrlVO();
        arg.setId(marketingLiveId);
        arg.setPhone(userMarketingAccount.getPhone());
        Result<GetViewUrlResult> viewUrl = getViewUrl(arg);
        if(viewUrl.getData().getCode() != 0){
            return Result.newSuccess(false);
        }
        return Result.newSuccess(true);
    }

    private boolean isLiveGoing(MarketingLiveEntity marketingLiveEntity){
        if (marketingLiveEntity.getPlatform() == LivePlatformEnum.OTHER_PLATFORM.getType() && marketingLiveEntity.getStartTime().getTime() <= System.currentTimeMillis()){
            return true;
        }else if (marketingLiveEntity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()
                && (marketingLiveEntity.getStatus() == LiveStatusEnum.PROCESSING.getStatus()
                || marketingLiveEntity.getStatus() == LiveStatusEnum.FINISH_WITH_REPLAY.getStatus())){
            return true;
        }else{
            return false;
        }
    }

    @Override
    @Deprecated
    public Result<ExportEnrollsDataResult> exportLiveLead(LiveLeadDetailVO vo) {
        ExportMultipleFormEnrollsDataArg arg = new ExportMultipleFormEnrollsDataArg();
        arg.setSourceId(vo.getMarketingEventId());
        arg.setSourceType(0);
        arg.setEa(vo.getEa());
        arg.setFsUserId(vo.getFsUserId());
        Result<ExportEnrollsDataResult> exportEnrollsDataResultResult = customizeFormDataService.exportMultipleFormEnrollsData(arg);
        return exportEnrollsDataResultResult;
    }

    @Override
    public void handlerLiveMQ(LiveMqTrainingMessage message) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByLiveIdAndCorpId(message.getLiveId(), Integer.parseInt(message.getEi()));
        if (marketingLiveEntity == null){
            log.info("handlerLiveMQ failed marketingLiveEntity not exist liveId:{}", message.getLiveId());
            return;
        }

        if (message.getStatus() != null) {
            marketingLiveDAO.updateStatusByLiveId(marketingLiveEntity.getLiveId(), message.getStatus());
            marketingLiveStatisticsDAO.updateStatus(marketingLiveEntity.getLiveId(), message.getStatus());
        }

        //创建回放
        if ( message.getStatus().intValue() == LiveStatusEnum.FINISH.getStatus()
                && marketingLiveEntity.getAutoRecord() == LiveRecordEnum.AUTO_RECORD.getType()
                && marketingLiveEntity.getRecordId() == null) {
            try {
                Thread.sleep(10 * 1000);   //微吼要求休眠10秒，才能创建回放
            } catch (InterruptedException e) {
                log.error("handlerLiveMQ sleep faied mqTrainingMessage:{} e:", message, e);
            }

            LiveDefaultRecordArg liveDefaultRecordArg = new LiveDefaultRecordArg();
            liveDefaultRecordArg.setLiveId(marketingLiveEntity.getLiveId());
            liveDefaultRecordArg.setTitle(marketingLiveEntity.getTitle());
            com.facishare.training.common.result.Result<Integer> recordResult = liveCommonService.setDefaultRecord(liveDefaultRecordArg);
            if (recordResult == null || !recordResult.isSuccess() || recordResult.getData() == null){
                log.info("handlerLiveMQ set live record failed result:{} arg:{}", recordResult, liveDefaultRecordArg);
            }else {
                marketingLiveDAO.updateLiveRecord(marketingLiveEntity.getLiveId(), recordResult.getData());
            }
        }
    }

    @Override
    public Result<GetLiveViewCheckInfoResult> getViewCheckInfo(String liveKey) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(liveKey);
        if (marketingLiveEntity == null){
            log.info("getViewCheckInfo failed liveEntity is not exist liveKey:{}", liveKey);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        GetLiveViewCheckInfoResult result = new GetLiveViewCheckInfoResult();
        result.setSetName(true);
        result.setSetPhone(true);

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetLectrureCheckInfoResult> getLectrureCheckInfo(String liveKey) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(liveKey);
        if (marketingLiveEntity == null){
            log.info("getViewCheckInfo failed liveEntity is not exist liveKey:{}", liveKey);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        GetLectrureCheckInfoResult result = new GetLectrureCheckInfoResult();
        if (StringUtils.isNotEmpty(marketingLiveEntity.getLecturePassword())){
            result.setCheckPassword(true);
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<Integer> getLiveStatus(String id) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(id);
        if (marketingLiveEntity == null){
            log.info("getLiveStatus failed liveEntity is not exist liveKey:{}", id);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        if (marketingLiveEntity.getPlatform().intValue() == LivePlatformEnum.OTHER_PLATFORM.getType()||marketingLiveEntity.getPlatform().intValue() == LivePlatformEnum.CHANNELS.getType()){
            Long startTime = marketingLiveEntity.getStartTime().getTime();
            Long endTime = marketingLiveEntity.getEndTime().getTime();
            Long now = System.currentTimeMillis();
            if (startTime > now){
                return Result.newSuccess(LiveStatusEnum.NOT_START.getStatus());
            }else if (now > startTime && now < endTime){
                return Result.newSuccess(LiveStatusEnum.PROCESSING.getStatus());
            }else {
                return Result.newSuccess(LiveStatusEnum.FINISH.getStatus());
            }
        }else {
            Integer status = null;
            if (marketingLiveEntity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()){
                MarketingLiveStatistics marketingLiveStatistics = marketingLiveStatisticsDAO.getByLiveId(marketingLiveEntity.getLiveId());
                if (marketingLiveStatistics == null){
                    log.info("getLiveStatus failed marketingLiveStatistics is not exist liveKey:{}", id);
                    return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
                }
                status = marketingLiveStatistics.getStatus();
            }else if (marketingLiveEntity.getPlatform() == LivePlatformEnum.XIAOETONG.getType()||marketingLiveEntity.getPlatform() == LivePlatformEnum.POLYV.getType()
                    || marketingLiveEntity.getPlatform() == LivePlatformEnum.MUDU.getType() || marketingLiveEntity.getPlatform() == LivePlatformEnum.VHALL.getType()){
                List<MarketingLiveStatistics> marketingLiveStatistics = marketingLiveStatisticsDAO.getByXiaoetongLiveId(marketingLiveEntity.getCorpId(), marketingLiveEntity.getXiaoetongLiveId());
                if (CollectionUtils.isEmpty(marketingLiveStatistics)){
                    log.info("getLiveStatus failed xiaoetong marketingLiveStatistics is not exist liveKey:{}", id);
                    return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
                }
                status = marketingLiveStatistics.get(0).getStatus();
            }

            return Result.newSuccess(status);
        }
    }

    @Override
    public Result<PageResult<QueryLiveEnrollListResult>> queryLiveEnrollList(QueryLiveEnrollListVO vo) {
        Integer ei = eieaConverter.enterpriseAccountToId(vo.getEa());
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, vo.getMarketingEventId());
        if (marketingLiveEntity == null) {
            log.warn("LiveServiceImpl.queryLiveEnrollList marketingLiveEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        PageResult<QueryLiveEnrollListResult> pageResult = new PageResult<>();
        List<QueryLiveEnrollListResult> queryLiveEnrollListResultList = Lists.newArrayList();
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setResult(queryLiveEnrollListResultList);
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        // 新创建的小鹅通直播，报名数据和直播数据关联的时候要用outer_user_id，因为有可能没有手机号
        boolean isXiaoetongUpgraded = xiaoetongManager.isXiaoetongUpgradedLive(ei, vo.getMarketingEventId());

        List<String> campaignObjIds = crmV2Manager.getObjIdsByRuleGroupJson(vo.getEa(), vo.getRuleGroupJson());

        List<PageCampaignLiveDTO> pageCampaignLiveDTOList = campaignMergeDataDAO
            .pageCampaignLiveData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getViewLiveStatus(), vo.getLiveReplayStatus(), vo.getInteractiveStatus(),
                    vo.getChannelValue(), vo.getSaveCrmStatus(), page, marketingLiveEntity.getPlatform(), vo.getPayStatus(), isXiaoetongUpgraded ? 1 : 0, campaignObjIds);
        if (CollectionUtils.isEmpty(pageCampaignLiveDTOList)) {
            log.warn("LiveServiceImpl.queryLiveEnrollList pageCampaignLiveDTOList is empty ");
            return Result.newSuccess(pageResult);
        }

        // 计算totalCount
        int allDataSize = campaignMergeDataDAO.countCampaignLiveData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getViewLiveStatus(), vo.getLiveReplayStatus(), vo.getInteractiveStatus(), vo.getChannelValue(), vo.getSaveCrmStatus(), marketingLiveEntity.getPlatform(), vo.getPayStatus());
        pageResult.setTotalCount(allDataSize);

        // 查询业务负责人
        List<String> campaignMembersObjIds = pageCampaignLiveDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
            PageCampaignLiveDTO::getCampaignMembersObjId).collect(
            Collectors.toList());
        List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(vo.getEa(), vo.getMarketingEventId(), campaignMembersObjIds);
        Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(vo.getEa(), objectDataList);
        // 查询姓名（兼容表单无名称，对象有名称场景）
        Map<String, ObjectData> objectDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectDataList)){
            objectDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));
        }
        List<Integer> spreadFsUserId = pageCampaignLiveDTOList.stream().filter(data -> data.getSpreadFsUid() != null).map(PageCampaignLiveDTO::getSpreadFsUid).collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> allFSEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), spreadFsUserId, true);
        Map<Integer, String> outUserNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(vo.getEa(), spreadFsUserId);
        // 拼装物料数据
        Map<String, String> objectNameMap = Maps.newHashMap();
        for (PageCampaignLiveDTO pageCampaignLiveDTO : pageCampaignLiveDTOList) {
            String objectKey = pageCampaignLiveDTO.getEnrollSourceObjectId() + "#" + pageCampaignLiveDTO.getEnrollSourceObjectType();
            if (StringUtils.isBlank(objectNameMap.get(objectKey))) {
                String objectName = objectManager.getObjectName(pageCampaignLiveDTO.getEnrollSourceObjectId(), pageCampaignLiveDTO.getEnrollSourceObjectType());
                if (StringUtils.isNotBlank(objectName)) {
                    objectNameMap.put(objectKey, objectName);
                }
            }
        }
        List<String> campaignIds = pageCampaignLiveDTOList.stream().map(BaseCampaignDTO::getId).collect(Collectors.toList());
        // 查询最新的会员数据
        Map<String, String> accessibleMemberMap = memberManager.getLatestAccessibleMemberIdByCampaignIds(campaignIds);
        // 查询微信用户
        Map<String, List<String>> wxAppOpenIdMap = Maps.newHashMap();
        Map<String, WxUserData> allWxUserData = Maps.newHashMap();
        for (PageCampaignLiveDTO pageCampaignLiveDTO : pageCampaignLiveDTOList) {
            if (StringUtils.isBlank(pageCampaignLiveDTO.getWxAppId()) || StringUtils.isBlank(pageCampaignLiveDTO.getOpenId())) {
                continue;
            }
            wxAppOpenIdMap.computeIfAbsent(pageCampaignLiveDTO.getWxAppId(), data -> Lists.newArrayList()).add(pageCampaignLiveDTO.getOpenId());
        }
        for (Map.Entry<String, List<String>> entry : wxAppOpenIdMap.entrySet()) {
            allWxUserData.putAll(campaignMergeDataManager.queryWxUserInfo(vo.getEa(), entry.getKey(), entry.getValue()));
        }
        Map<String, ObjectData> campaignMembersObjsMap = new HashMap<>();
        List<ObjectData> campaignMembersObjs = campaignMergeDataManager.getCampaignMembersObjByMaketingEventIds(vo.getEa(), Collections.singletonList(vo.getMarketingEventId()));
        if (campaignMembersObjs != null) {
            campaignMembersObjsMap = campaignMembersObjs.stream().collect(Collectors.toMap(ObjectData::getId, Function.identity(), (o, n) -> o));
        }
        Map<String, String> customerMap = new HashMap<>();
        Map<String, String> employeeMap = new HashMap<>();
        List<String> outTenantIdList = pageCampaignLiveDTOList.stream().map(PageCampaignLiveDTO::getOutTenantId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outTenantIdList.isEmpty()) {
            List<ObjectData> customerListByDestOuterTenantIds = customizeFormDataManager.getCustomerListByEnterpriserelationIds(vo.getEa(), outTenantIdList);
            customerListByDestOuterTenantIds.forEach(e -> customerMap.put(String.valueOf(e.get("enterpriserelation_id")), String.valueOf(e.get("name"))));
        }
        List<String> outUserIdList = pageCampaignLiveDTOList.stream().map(PageCampaignLiveDTO::getOutUid).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outUserIdList.isEmpty()) {
            List<ObjectData> employeeListByDestOuterTenantIds = customizeFormDataManager.getEmployeeListByOuterUidIds(vo.getEa(), outUserIdList);
            employeeListByDestOuterTenantIds.forEach(e -> employeeMap.put(String.valueOf(e.get("outer_uid")), String.valueOf(e.get("name"))));
        }

        // 查询关联业务数据
        // 先按绑定对象类型分组，然后再循环批量查询对象数据
        Map<Integer, List<PageCampaignLiveDTO>> bindCrmObjectMap = pageCampaignLiveDTOList.stream()
                .filter(pageCampaignLiveDTO -> StringUtils.isNotBlank(pageCampaignLiveDTO.getBindCrmObjectId()) && pageCampaignLiveDTO.getBindCrmObjectType() != null)
                .collect(Collectors.groupingBy(PageCampaignLiveDTO::getBindCrmObjectType));
        Map<String, String> bindCrmObjectNameMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(bindCrmObjectMap)) {
            for (Map.Entry<Integer, List<PageCampaignLiveDTO>> entry : bindCrmObjectMap.entrySet()) {
                Integer bindCrmObjectType = entry.getKey();
                List<String> bindCrmObjectIds = entry.getValue().stream().map(PageCampaignLiveDTO::getBindCrmObjectId).collect(Collectors.toList());
                String apiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(bindCrmObjectType);
                if (StringUtils.isBlank(apiName)){
                    continue;
                }
                List<ObjectData> objectDatas = crmMetadataManager.batchGetByIdsV3(vo.getEa(), -10000, apiName, Lists.newArrayList("_id", "name"), bindCrmObjectIds);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(objectDatas)) {
                    // 为了避免不同对象出现相同id的情况，拼接上 bindCrmObjectType
                    bindCrmObjectNameMap.putAll(objectDatas.stream().collect(Collectors.toMap(objectData -> objectData.getId() + "_" + bindCrmObjectType, ObjectData::getName)));
                }
            }
        }

        for (PageCampaignLiveDTO pageCampaignLiveDTO : pageCampaignLiveDTOList) {
            QueryLiveEnrollListResult queryLiveEnrollListResult = new QueryLiveEnrollListResult();
            queryLiveEnrollListResult.setId(pageCampaignLiveDTO.getId());
            String userName = pageCampaignLiveDTO.getName();
            if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(pageCampaignLiveDTO.getCampaignMembersObjId())) {
                ObjectData userObjData = objectDataMap.get(pageCampaignLiveDTO.getCampaignMembersObjId());
                userName = userObjData != null ? userObjData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName()) : null;
            }
            queryLiveEnrollListResult.setName(UnicodeFormatter.decodeUnicodeString(userName));
            if (StringUtils.isNotBlank(pageCampaignLiveDTO.getCampaignMembersObjId())) {
                queryLiveEnrollListResult.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                if (pageCampaignLiveDTO.getAddCampaignMember() != null && pageCampaignLiveDTO.getAddCampaignMember()) {
                    queryLiveEnrollListResult.setSaveCrmStatus(SaveCrmStatusEnum.LINKED.getValue());
                }
                if (pageCampaignLiveDTO.getSaveCrmStatus() != null) {
                    queryLiveEnrollListResult.setSaveCrmStatus(pageCampaignLiveDTO.getSaveCrmStatus());
                }
                queryLiveEnrollListResult.setBindCrmObjectId(pageCampaignLiveDTO.getBindCrmObjectId());
                queryLiveEnrollListResult.setBindCrmObjectType(pageCampaignLiveDTO.getBindCrmObjectType());
                String ownerName = ownerNameMap.get(pageCampaignLiveDTO.getCampaignMembersObjId());
                queryLiveEnrollListResult.setOwnerName(ownerName);
            } else {
                queryLiveEnrollListResult.setSaveCrmErrorMessage(pageCampaignLiveDTO.getSaveCrmErrorMessage());
                queryLiveEnrollListResult.setSaveCrmStatus(pageCampaignLiveDTO.getSaveCrmStatus());
            }
            queryLiveEnrollListResult.setChannelValue(pageCampaignLiveDTO.getChannelValue());
            queryLiveEnrollListResult.setCampaignMembersObjId(pageCampaignLiveDTO.getCampaignMembersObjId());
            // 推广人
            if (StringUtils.isNotBlank(pageCampaignLiveDTO.getOutTenantId()) && StringUtils.isNotBlank(pageCampaignLiveDTO.getOutUid())) {
                String outTenant = customerMap.get(pageCampaignLiveDTO.getOutTenantId());
                String outUser = employeeMap.get(pageCampaignLiveDTO.getOutUid());
                if (StringUtils.isNotBlank(outTenant)
                        && StringUtils.isNotBlank(outUser)) {
                    queryLiveEnrollListResult.setSpreadUserName(outTenant + "-" + outUser);
                }
            } else if (pageCampaignLiveDTO.getSpreadFsUid() != null) {
                FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(pageCampaignLiveDTO.getSpreadFsUid());
                queryLiveEnrollListResult.setSpreadUserName(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
                if (StringUtils.isBlank(queryLiveEnrollListResult.getSpreadUserName())) {
                    queryLiveEnrollListResult.setSpreadUserName(outUserNameMap.get(pageCampaignLiveDTO.getSpreadFsUid()));
                }
            }
            //根据业务数据来源来获取关联名称
            String relationDataName = queryLiveEnrollListResult.getName();
            if (StringUtils.isNotBlank(pageCampaignLiveDTO.getBindCrmObjectId()) && pageCampaignLiveDTO.getBindCrmObjectType() != null) {
                relationDataName = bindCrmObjectNameMap.get(pageCampaignLiveDTO.getBindCrmObjectId() + "_" + pageCampaignLiveDTO.getBindCrmObjectType());
                /*String objectName = conferenceManager.getEnrollNameByObjectType(vo.getEa(),pageCampaignLiveDTO.getBindCrmObjectType(),pageCampaignLiveDTO.getBindCrmObjectId());
                if (objectName != null) {
                    relationDataName = objectName;
                }*/
            }
            queryLiveEnrollListResult.setRelationDataName(relationDataName);
            queryLiveEnrollListResult.setEnrollSourceName(objectNameMap.get(pageCampaignLiveDTO.getEnrollSourceObjectId() + "#" + pageCampaignLiveDTO.getEnrollSourceObjectType()));
            queryLiveEnrollListResult.setFormDataUserId(pageCampaignLiveDTO.getFormDataUserId());
            queryLiveEnrollListResult.setMemberAccessibleCampaignId(pageCampaignLiveDTO.getMemberAccessibleCampaignId());
            queryLiveEnrollListResult.setSourceType(pageCampaignLiveDTO.getSourceType());
            ObjectData objectData = campaignMembersObjsMap.get(queryLiveEnrollListResult.getCampaignMembersObjId());
            // 若为微吼平台
            queryLiveEnrollListResult.setMemberId(accessibleMemberMap.get(pageCampaignLiveDTO.getId()));
            Integer platform = marketingLiveEntity.getPlatform();
            if (Objects.equals(platform, LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) || Objects.equals(platform, LivePlatformEnum.XIAOETONG.getType())
                    || Objects.equals(platform, LivePlatformEnum.POLYV.getType()) || Objects.equals(platform, LivePlatformEnum.MUDU.getType()) || Objects.equals(platform, LivePlatformEnum.VHALL.getType())) {
                queryLiveEnrollListResult.setViewLiveStatus(pageCampaignLiveDTO.getViewStatus() != null ? pageCampaignLiveDTO.getViewStatus() : LiveUserStatusEnum.ACTION_NOT_PERFORMED.getStatus());
                queryLiveEnrollListResult.setLiveReplayStatus(pageCampaignLiveDTO.getReplayStatus() != null ? pageCampaignLiveDTO.getReplayStatus() : LiveUserStatusEnum.ACTION_NOT_PERFORMED.getStatus());
                queryLiveEnrollListResult.setViewTime(pageCampaignLiveDTO.getViewTime());
                queryLiveEnrollListResult.setReplayTime(pageCampaignLiveDTO.getReplayTime());
                queryLiveEnrollListResult.setInteractiveCount(pageCampaignLiveDTO.getInteractiveCount() != null ? pageCampaignLiveDTO.getInteractiveCount() : 0);
            } else {
                if (objectData != null) {
                    queryLiveEnrollListResult.setViewLiveStatus(objectData.get("marketing_watch_stauts") == null ? null : Integer.valueOf(objectData.get("marketing_watch_stauts").toString()));
                    queryLiveEnrollListResult.setLiveReplayStatus(objectData.get("marketing_playback_status") == null ? null : Integer.valueOf(objectData.get("marketing_playback_status").toString()));
                }
            }
            if (objectData != null) {
                queryLiveEnrollListResult.setLiveReviewStatus(objectData.get("approval_status") == null ? "" : objectData.get("approval_status").toString());
            }
            if (StringUtils.isNotBlank(pageCampaignLiveDTO.getWxAppId()) && StringUtils.isNotBlank(pageCampaignLiveDTO.getOpenId())) {
                WxUserData wxUserData = allWxUserData.get(pageCampaignLiveDTO.getOpenId());
                if (wxUserData != null) {
                    queryLiveEnrollListResult.setWxUserName(wxUserData.getName());
                    queryLiveEnrollListResult.setWxUserAvatar(wxUserData.getUserAvatar());
                }
            }

            BigDecimal totalAmount = new BigDecimal(pageCampaignLiveDTO.getTotalAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            queryLiveEnrollListResult.setTotalAmount(totalAmount);

            queryLiveEnrollListResultList.add(queryLiveEnrollListResult);
        }
        Map<String, Integer> campaignIdToPayOrderCountMap = campaignMergeDataManager.countPayOrderNumber(vo.getEa(), campaignIds);
        pageResult.getResult().forEach(r -> {
            r.setPayOrderCount(campaignIdToPayOrderCountMap.get(r.getId()) != null ? campaignIdToPayOrderCountMap.get(r.getId()) : 0);
        });
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<QueryLiveEnrollListResult>> queryLiveEnrollListForSync(QueryLiveEnrollListVO vo) {
        Integer ei = eieaConverter.enterpriseAccountToId(vo.getEa());
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, vo.getMarketingEventId());
        if (marketingLiveEntity == null) {
            log.warn("LiveServiceImpl.queryLiveEnrollList marketingLiveEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        PageResult<QueryLiveEnrollListResult> pageResult = new PageResult<>();
        List<QueryLiveEnrollListResult> queryLiveEnrollListResultList = Lists.newArrayList();
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setResult(queryLiveEnrollListResultList);
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        // 新创建的小鹅通直播，报名数据和直播数据关联的时候要用outer_user_id，因为有可能没有手机号
        boolean isXiaoetongUpgraded = xiaoetongManager.isXiaoetongUpgradedLive(ei, vo.getMarketingEventId());

        List<PageCampaignLiveDTO> pageCampaignLiveDTOList = campaignMergeDataDAO
                .pageCampaignLiveData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getViewLiveStatus(), vo.getLiveReplayStatus(), vo.getInteractiveStatus(),
                        vo.getChannelValue(), vo.getSaveCrmStatus(), page, marketingLiveEntity.getPlatform(), vo.getPayStatus(), isXiaoetongUpgraded ? 1 : 0, null);
        if (CollectionUtils.isEmpty(pageCampaignLiveDTOList)) {
            log.warn("LiveServiceImpl.queryLiveEnrollList pageCampaignLiveDTOList is empty ");
            return Result.newSuccess(pageResult);
        }

        // 计算totalCount
        int allDataSize = campaignMergeDataDAO.countCampaignLiveData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getViewLiveStatus(), vo.getLiveReplayStatus(), vo.getInteractiveStatus(), vo.getChannelValue(), vo.getSaveCrmStatus(), marketingLiveEntity.getPlatform(), vo.getPayStatus());
        pageResult.setTotalCount(allDataSize);

        for (PageCampaignLiveDTO pageCampaignLiveDTO : pageCampaignLiveDTOList) {
            QueryLiveEnrollListResult queryLiveEnrollListResult = new QueryLiveEnrollListResult();
            queryLiveEnrollListResult.setId(pageCampaignLiveDTO.getId());
            queryLiveEnrollListResult.setCampaignMembersObjId(pageCampaignLiveDTO.getCampaignMembersObjId());
            // 若为微吼平台
            if (marketingLiveEntity.getPlatform().equals(LivePlatformEnum.FS_COOPERATION_PLATFORM.getType())
                    || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.XIAOETONG.getType())
                    || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.POLYV.getType())
                    || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.MUDU.getType())
                    || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.VHALL.getType())) {
                queryLiveEnrollListResult.setViewTime(pageCampaignLiveDTO.getViewTime());
                queryLiveEnrollListResult.setReplayTime(pageCampaignLiveDTO.getReplayTime());
                queryLiveEnrollListResult.setInteractiveCount(pageCampaignLiveDTO.getInteractiveCount() != null ? pageCampaignLiveDTO.getInteractiveCount() : 0);
            }
            queryLiveEnrollListResultList.add(queryLiveEnrollListResult);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<ExportEnrollsDataResult> exportLiveEnrollList(QueryLiveEnrollListVO vo) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            exportLiveEnrollListInner(vo);
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    // 为什么不复用列表接口？？？？
    private void exportLiveEnrollListInner(QueryLiveEnrollListVO vo) {
        ExportEnrollsDataResult exportEnrollsDataResult = new ExportEnrollsDataResult();
        Integer ei = eieaConverter.enterpriseAccountToId(vo.getEa());
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, vo.getMarketingEventId());
        if (marketingLiveEntity == null) {
            log.warn("LiveServiceImpl.exportLiveEnrollList marketingLiveEntity is null vo:{}", vo);
            return;
        }
        List<PageCampaignLiveDTO> pageCampaignLiveDTOList = campaignMergeDataDAO
                .queryCampaignLiveData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getViewLiveStatus(), vo.getLiveReplayStatus(), vo.getInteractiveStatus(), vo.getChannelValue(), vo.getSaveCrmStatus(), marketingLiveEntity.getPlatform());
        if (CollectionUtils.isEmpty(pageCampaignLiveDTOList)) {
            return;
        }
        //根据市场活动查询直播用户ID
        List<MarketingLiveViewLoginEntity> viewLoginEntities = marketingLiveViewLoginDAO.getBymarketingEventId(vo.getMarketingEventId());
        Map<String, MarketingLiveViewLoginEntity> viewLoginEntityMap = viewLoginEntities.stream().collect(Collectors.toMap(MarketingLiveViewLoginEntity::getPhone, data -> data, (v1, v2) -> v1));

        // 查询小鹅通用户id
        Map<String, LiveUserStatusEntity> liveUserStatusMap = Maps.newHashMap();
        if (Objects.equals(marketingLiveEntity.getPlatform(), LivePlatformEnum.XIAOETONG.getType())) {
            String xiaoetongLiveId = marketingLiveEntity.getXiaoetongLiveId();
            List<LiveUserStatusEntity> liveUserStatusEntities = liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveId(xiaoetongLiveId);
            liveUserStatusMap = liveUserStatusEntities.stream().collect(Collectors.toMap(LiveUserStatusEntity::getPhone, Function.identity(), (v1, v2) -> v1));
        }

        exportEnrollsDataResult.setFileName(marketingLiveEntity.getTitle() + I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2388));
        List<String> titleList = Lists.newArrayList();
        if (marketingLiveEntity.getPlatform().equals(LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.POLYV.getType())) {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2391));
        } else if (Objects.equals(marketingLiveEntity.getPlatform(), LivePlatformEnum.XIAOETONG.getType())) {
            titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2391));
        }
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_275));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_276));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1071));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1074));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1073));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1075));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1076));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_586));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2406));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2407));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2408));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2409));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2410));
        exportEnrollsDataResult.setTitleList(titleList);

        List<List<Object>> enrollInfoList = Lists.newArrayList();
        // 查询业务负责人
        List<String> campaignMembersObjIds = pageCampaignLiveDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
                PageCampaignLiveDTO::getCampaignMembersObjId).collect(
                Collectors.toList());
        List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(vo.getEa(), vo.getMarketingEventId(), campaignMembersObjIds);
        Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(vo.getEa(), objectDataList);
        // 查询姓名（兼容表单无名称，对象有名称场景）
        Map<String, ObjectData> objectDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectDataList)){
            objectDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));
        }
        List<Integer> spreadFsUserId = pageCampaignLiveDTOList.stream().filter(data -> data.getSpreadFsUid() != null).map(PageCampaignLiveDTO::getSpreadFsUid).collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> allFSEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), spreadFsUserId, true);
//            Map<Integer, String> outUserNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(vo.getEa(), spreadFsUserId);
        Map<String, String> customerMap = new HashMap<>();
        Map<String, String> employeeMap = new HashMap<>();
        List<String> outTenantIdList = pageCampaignLiveDTOList.stream().map(BaseCampaignDTO::getOutTenantId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outTenantIdList.isEmpty()) {
            List<ObjectData> customerListByDestOuterTenantIds = customizeFormDataManager.getCustomerListByEnterpriserelationIds(vo.getEa(), outTenantIdList);
            customerListByDestOuterTenantIds.forEach(e -> customerMap.put(String.valueOf(e.get("enterpriserelation_id")), String.valueOf(e.get("name"))));
        }
        List<String> outUserIdList = pageCampaignLiveDTOList.stream().map(BaseCampaignDTO::getOutUid).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outUserIdList.isEmpty()) {
            List<ObjectData> employeeListByDestOuterTenantIds = customizeFormDataManager.getEmployeeListByOuterUidIds(vo.getEa(), outUserIdList);
            employeeListByDestOuterTenantIds.forEach(e -> employeeMap.put(String.valueOf(e.get("outer_uid")), String.valueOf(e.get("name"))));
        }
        // 拼装物料数据
        Map<String, String> objectNameMap = Maps.newHashMap();
        for (PageCampaignLiveDTO campaignLiveDTO : pageCampaignLiveDTOList) {
            String objectKey = campaignLiveDTO.getEnrollSourceObjectId() + "#" + campaignLiveDTO.getEnrollSourceObjectType();
            if (StringUtils.isBlank(objectNameMap.get(objectKey))) {
                String objectName = objectManager.getObjectName(campaignLiveDTO.getEnrollSourceObjectId(), campaignLiveDTO.getEnrollSourceObjectType());
                if (StringUtils.isNotBlank(objectName)) {
                    objectNameMap.put(objectKey, objectName);
                }
            }
        }
        Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(vo.getEa());
        for (PageCampaignLiveDTO pageCampaignLiveDTO : pageCampaignLiveDTOList) {
            List<Object> lineData = Lists.newArrayList();
            //直播用户ID
            String liveUserId = null;
            if (marketingLiveEntity.getPlatform().equals(LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.POLYV.getType())) {
                String phone = pageCampaignLiveDTO.getPhone();
                if (StringUtils.isNotBlank(phone) && viewLoginEntityMap.containsKey(phone)) {
                    liveUserId = viewLoginEntityMap.get(phone).getId();
                }
                lineData.add(liveUserId);
            } else if (Objects.equals(marketingLiveEntity.getPlatform(), LivePlatformEnum.XIAOETONG.getType())) {
                String phone = pageCampaignLiveDTO.getPhone();
                if (StringUtils.isNotBlank(phone) && liveUserStatusMap.containsKey(phone)) {
                    liveUserId = liveUserStatusMap.get(phone).getOuterUserId();
                }
                lineData.add(StringUtils.isNotBlank(liveUserId) ? liveUserId : "");
            }
            // 名称
            String userName = pageCampaignLiveDTO.getName();
            if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(pageCampaignLiveDTO.getCampaignMembersObjId())) {
                ObjectData userObjData = objectDataMap.get(pageCampaignLiveDTO.getCampaignMembersObjId());
                userName = userObjData != null ? userObjData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName()) : null;
            }
            lineData.add(UnicodeFormatter.decodeUnicodeString(userName));
            //手机号
            String liveDTOPhone = pageCampaignLiveDTO.getPhone();
            if (StringUtils.isBlank(liveDTOPhone) && null != pageCampaignLiveDTO.getSubmitContent()) {
                liveDTOPhone = pageCampaignLiveDTO.getSubmitContent().getPhone();
            }
            lineData.add(liveDTOPhone);
            String email = null;
            String companyName = null;
            String position = null;
            if (null != pageCampaignLiveDTO.getSubmitContent()) {
                email = pageCampaignLiveDTO.getSubmitContent().getEmail();
                companyName = pageCampaignLiveDTO.getSubmitContent().getCompanyName();
                position = pageCampaignLiveDTO.getSubmitContent().getPosition();
            }
            //如果是会员,则从会员关联营销用户获取相应的数据
            if (StringUtils.isNotEmpty(pageCampaignLiveDTO.getMemberAccessibleCampaignId())) {
                MemberAccessibleCampaignEntity accessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignDataById(pageCampaignLiveDTO.getMemberAccessibleCampaignId());
                if (accessibleCampaignEntity != null) {
                    //获取营销用户id
                    Result<String> userMarketingAccountResult = userMarketingAccountService.getUserMarketingAccountByMemberId(vo.getEa(), -10000, accessibleCampaignEntity.getMemberId());
                    if (userMarketingAccountResult.isSuccess() && StringUtils.isNotEmpty(userMarketingAccountResult.getData())) {
                        //查询营销用户数据
                        Result<UserMarketingAccountDetailsResult> userMarketingAccountDetails = userMarketingAccountService.getUserMarketingAccountDetails(vo.getEa(), -10000, userMarketingAccountResult.getData());
                        if (userMarketingAccountDetails.isSuccess() &&  null != userMarketingAccountDetails.getData()) {
                            if (StringUtils.isEmpty(email)) {
                                email = userMarketingAccountDetails.getData().getEmail();
                            }
                            if (StringUtils.isEmpty(companyName)) {
                                companyName = userMarketingAccountDetails.getData().getCompanyName();
                            }
                            if (StringUtils.isEmpty(position)) {
                                position = userMarketingAccountDetails.getData().getPosition();
                            }
                        }
                    }

                }
            }
            //邮箱
            lineData.add(email);
            //公司名称
            lineData.add(companyName);
            //职务
            lineData.add(position);
            //成员类型
            String memberType = null;
            if (pageCampaignLiveDTO.getBindCrmObjectType() != null) {
                CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(pageCampaignLiveDTO.getBindCrmObjectType());
                memberType = campaignMergeDataObjectTypeEnum.getDesc();
            }
            lineData.add(memberType);
            // 负责人
            if (StringUtils.isNotBlank(pageCampaignLiveDTO.getCampaignMembersObjId())) {
                lineData.add(ownerNameMap.get(pageCampaignLiveDTO.getCampaignMembersObjId()));
            } else {
                lineData.add(null);
            }
            // 推广人
//                if (pageCampaignLiveDTO.getSpreadFsUid() != null) {
//                    FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(pageCampaignLiveDTO.getSpreadFsUid());
//                    if (null != fsEmployeeMsg) {
//                        lineData.add(fsEmployeeMsg.getName());
//                    } else if (null != outUserNameMap && outUserNameMap.containsKey(pageCampaignLiveDTO.getSpreadFsUid())) {
//                        lineData.add(outUserNameMap.get(pageCampaignLiveDTO.getSpreadFsUid()));
//                    } else {
//                        lineData.add(null);
//                    }
//                } else {
//                    lineData.add(null);
//                }
            if (StringUtils.isNotBlank(pageCampaignLiveDTO.getOutTenantId()) && StringUtils.isNotBlank(pageCampaignLiveDTO.getOutUid())) {
                String outTenant = customerMap.get(pageCampaignLiveDTO.getOutTenantId());
                String outUser = employeeMap.get(pageCampaignLiveDTO.getOutUid());
                if (StringUtils.isNotBlank(outTenant)
                        && StringUtils.isNotBlank(outUser)) {
                    lineData.add(outTenant + "-" + outUser);
                } else {
                    lineData.add(null);
                }
            } else if (pageCampaignLiveDTO.getSpreadFsUid() != null) {
                FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(pageCampaignLiveDTO.getSpreadFsUid());
                lineData.add(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
            } else {
                lineData.add(null);
            }
            lineData.add(objectNameMap.get(pageCampaignLiveDTO.getEnrollSourceObjectId() + "#" + pageCampaignLiveDTO.getEnrollSourceObjectType()));
            // 来源渠道
        /*String channelLabel = channelValueMap.get(pageCampaignLiveDTO.getChannelValue());
        lineData.add(StringUtils.isNotBlank(channelLabel) ? channelLabel : null);*/
            lineData.add(spreadChannelManager.getChannelLabelByChannelValue(vo.getEa(), channelValueMap, pageCampaignLiveDTO.getChannelValue()));
            // 若为微吼平台
            if (marketingLiveEntity.getPlatform().equals(LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.XIAOETONG.getType())
                    || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.POLYV.getType()) || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.CHANNELS.getType())
                    || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.MUDU.getType()) || marketingLiveEntity.getPlatform().equals(LivePlatformEnum.VHALL.getType())) {
                if (pageCampaignLiveDTO.getViewStatus() != null) {
                    lineData.add(pageCampaignLiveDTO.getViewStatus().equals(LiveUserStatusEnum.ACTION_PERFORMED.getStatus()) ? "已观看" : "未观看");
                } else {
                    //lineData.add(StringUtils.isNotBlank(pageCampaignLiveDTO.getPhone()) ? "未观看" : "");
                    lineData.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_642));
                }
                if (pageCampaignLiveDTO.getReplayStatus() != null) {
                    lineData.add(pageCampaignLiveDTO.getReplayStatus().equals(LiveUserStatusEnum.ACTION_PERFORMED.getStatus()) ? "已回放" : "未回放");
                } else {
                    //lineData.add(StringUtils.isNotBlank(pageCampaignLiveDTO.getPhone()) ? "未回放" : "");
                    lineData.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2580));
                }
                if (pageCampaignLiveDTO.getInteractiveCount() != null) {
                    lineData.add(pageCampaignLiveDTO.getInteractiveCount() > 0 ? I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2583) : I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2583_1));
                } else {
                    lineData.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2583_1));
                }
                if (pageCampaignLiveDTO.getViewTime() != null) {
                    lineData.add(pageCampaignLiveDTO.getViewTime() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                } else {
                    lineData.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2590));
                }
                if (pageCampaignLiveDTO.getReplayTime() != null) {
                    lineData.add(pageCampaignLiveDTO.getReplayTime() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                } else {
                    lineData.add(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2590));
                }
                if (pageCampaignLiveDTO.getInteractiveCount() != null) {
                    lineData.add(pageCampaignLiveDTO.getInteractiveCount() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3462));
                } else {
                    lineData.add(0 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3462));
                }
            }
            enrollInfoList.add(lineData);
        }
        exportEnrollsDataResult.setEnrollInfoList(enrollInfoList);

        // ExportEnrollsDataResult dataResult = exportEnrollsDataResult.getData();
        String filename = exportEnrollsDataResult.getFileName() + ".xlsx";
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, exportEnrollsDataResult.getTitleList(), exportEnrollsDataResult.getEnrollInfoList());
        pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, vo.getEa(), vo.getFsUserId());
    }

    @Override
    public Result<Void> syncLiveStatistics(String id, String ea) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getById(id);
        if (marketingLiveEntity == null){
            log.info("SyncLiveStatisticsResult failed liveEntity is not exist liveKey:{}", id);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        if (marketingLiveEntity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()) {
            liveManager.syncVhallLiveTryLock(marketingLiveEntity.getLiveId());
        }
        if (marketingLiveEntity.getPlatform() == LivePlatformEnum.XIAOETONG.getType()){
            liveManager.syncXiaoetongLiveTryLock(ea, marketingLiveEntity.getMarketingEventId(), marketingLiveEntity.getXiaoetongLiveId());
            xiaoetongManager.syncXiaoetongLiveStatusByEa(ea);
            liveManager.syncLiveDataToCrm(marketingLiveEntity);
        }
        if (marketingLiveEntity.getPlatform() == LivePlatformEnum.POLYV.getType()) {
            polyvManager.syncLiveDataById(ea, marketingLiveEntity.getMarketingEventId(), marketingLiveEntity.getXiaoetongLiveId());
            polyvManager.syncLiveStatusByEaAndChannelId(ea, marketingLiveEntity.getXiaoetongLiveId());
            liveManager.syncLiveDataToCrm(marketingLiveEntity);
        }
        // 目睹直播，只处理主活动
        if (marketingLiveEntity.getPlatform() == LivePlatformEnum.MUDU.getType() && !Objects.equals(marketingLiveEntity.getSubEvent(), 1)) {
            liveManager.syncMuduLiveTryLock(ea, marketingLiveEntity.getMarketingEventId(), marketingLiveEntity.getXiaoetongLiveId());
            // 同步直播状态
            muduManager.syncMuduLiveStatus(ea);
        }
        if (marketingLiveEntity.getPlatform() == LivePlatformEnum.VHALL.getType()) {
            vHallManager.syncDataTryLock(ea, marketingLiveEntity.getMarketingEventId(), marketingLiveEntity.getXiaoetongLiveId());
        }
        return Result.newSuccess();
    }

    @Override
    public Result upsertUserStatus(Integer liveId, List<String> phones, LiveUserActionTypeEnum actionTypeEnum) {
        try {
            liveManager.upsertUserStatus(liveId, phones, actionTypeEnum);
        } catch (Exception e) {
            log.warn("LiveServiceImpl.upsertUserStatus error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> queryLiveUserByStatus(Integer liveId, LiveUserActionTypeEnum actionTypeEnum) {
        try {
            List<String> result = liveManager.queryLiveUserByStatus(liveId, actionTypeEnum);
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.warn("LiveServiceImpl.queryLiveUserByStatus error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<Void> syncLiveByTimer() {
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                log.info("Start syncLiveByTimer");
                liveManager.syncVhallLiveStatisticsData();
                liveManager.syncXiaoetongLiveStatisticsData();
                liveManager.syncPolyvLiveStatisticsData();
                liveManager.syncMuduLiveData4Job();
                liveManager.syncVHallLiveData4Job();
                log.info("End syncLiveByTimer");
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result<Void> scheduleSetDefaultRecord() {
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                liveManager.scheduleSetDefaultRecord();
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<XiaoetongLiveListResult>>  xiaoetongLiveList(ListVO vo) {
        return xiaoetongManager.getLiveList(vo);
    }

    @Override
    public Result<PageResult<PolyvLiveListResult>> polyvLiveList(ListVO vo) {
        return  polyvManager.getLiveList(vo);
    }


    @Override
    public Result<PageResult<GetMuduEventListResult>> getMuduEventList(ListMuduEventArg vo) {
        String accessToken = muduManager.getAccessToken(vo.getEa());
        MuduApiGetEventListResult eventListResult = muduManager.getEventList(accessToken, vo);
        if (eventListResult == null) {
            return Result.newError(SHErrorCode.MUDU_GET_EVENT_LIST_ERROR);
        }
        return Result.newSuccess(PageResult.newPageResult(vo.getPageNum(), vo.getPageSize(), eventListResult.getTotal(), BeanUtil.copy(eventListResult.getItems(), GetMuduEventListResult.class)));
    }

    @Override
    public Result<GetMuduEventListResult> getMuduLive(GetMuduLiveArg vo) {
        String accessToken = muduManager.getAccessToken(vo.getEa());
        MuduApiGetEventListResult.EventInfo eventDetail = muduManager.getEventDetail(accessToken, vo.getId());
        if (eventDetail == null) {
            return Result.newSuccess();
        }
        return Result.newSuccess(BeanUtil.copy(eventDetail, GetMuduEventListResult.class));
    }

    @Override
    public Result<PageResult<QueryThirdLiveResult>> queryVHallLive(QueryThirdLiveArg vo) {
        VHallApiQueryLiveArg arg = new VHallApiQueryLiveArg();
        Integer pageNum = vo.getPageNum();
        Integer pageSize = vo.getPageSize();
        Integer pos = (pageNum - 1) * pageSize;
        arg.setPos(String.valueOf(pos));
        arg.setLimit(String.valueOf(pageSize));
        arg.setTitle(vo.getKeyword());
        VHallApiQueryLiveResult vHallApiQueryLiveResult = vHallManager.queryLive(vo.getEa(), arg);
        if (vHallApiQueryLiveResult == null) {
            return Result.newError(SHErrorCode.VHALL_QUERY_LIVE_ERROR);
        }

        List<QueryThirdLiveResult> results = vHallApiQueryLiveResult.getList().stream().map(item -> {
            QueryThirdLiveResult result = new QueryThirdLiveResult();
            result.setEventId(item.getWebinarId() + "");
            result.setEventName(item.getSubject());
            result.setCoverUrl(item.getImgUrl());
            result.setStartTime(item.getStartTime());
            result.setEndTime(item.getEndTime());
            result.setEventStatus(item.getWebinarState());
            return result;
        }).collect(Collectors.toList());

        return Result.newSuccess(PageResult.newPageResult(vo.getPageNum(), vo.getPageSize(), vHallApiQueryLiveResult.getTotal(), results));
    }

    @Override
    public Result<GetThirdLiveResult> getVHallLive(GetThirdLiveArg vo) {
        VHallApiGetLiveResult apiGetLiveResult = vHallManager.getLive(vo.getEa(), vo.getId());
        if (apiGetLiveResult == null) {
            return Result.newError(SHErrorCode.VHALL_GET_LIVE_ERROR);
        }
        GetThirdLiveResult result = new GetThirdLiveResult();
        result.setEventId(apiGetLiveResult.getId() + "");
        result.setEventName(apiGetLiveResult.getSubject());
        result.setCoverUrl(apiGetLiveResult.getImgUrl());
        result.setStartTime(apiGetLiveResult.getStartTime());
        result.setEndTime(apiGetLiveResult.getEndTime());
        result.setEventStatus(apiGetLiveResult.getWebinarState());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> bindXiaoketongAccount(BindXiaoetongAccountVO vo) {
        return xiaoetongManager.bindXiaoketongAccount(vo);
    }

    @Override
    public Result<XiaoetongAccountResult> getXiaoetongAccount(String ea) {
        return xiaoetongManager.getXiaoetongAccount(ea);
    }

    @Override
    public Result<Void> bindPolyvAccount(String ea, String appId, String appSecret, String userId) {
        return polyvManager.bindPolyvAccount(ea, appId, appSecret, userId);
    }

    @Override
    public Result<PolyvAccountResult> bindAccount(String ea) {
        return polyvManager.bindAccount(ea);
    }

    @Override
    public Result<Void> bindChannelsAccount(BindChannelsAccountVo vo) {
        return channelsManager.bindAccount(vo);
    }

    @Override
    public Result<ChannelsAccountResult> getChannelsAccount(String ea,String id) {
        return channelsManager.getAccount(ea,id);
    }

    @Override
    public Result<Void> bindMuduAccount(BindMuduAccountVO vo) {
        return muduManager.bindMuduAccount(vo);
    }

    @Override
    public Result<GetMuduAccountResult> getMuduAccount(String ea) {
        return muduManager.getMuduAccount(ea);
    }

    @Override
    public Result<Void> bindThirdAccount(BindThirdAccountArg vo) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = thirdLiveAccountDAO.getByAppId(vo.getAppId(), vo.getPlatform());
        if (thirdLiveAccountEntity != null && !Objects.equals(thirdLiveAccountEntity.getEa(), vo.getEa())) {
            return Result.newError(SHErrorCode.MUDU_ACCOUNT_EXISTS_BIND);
        }
        ThirdLiveAccountEntity entity = thirdLiveAccountDAO.getByEa(vo.getEa(), vo.getPlatform());
        if (entity != null){
            entity.setAppId(vo.getAppId());
            entity.setSecretKey(vo.getSecretKey());
            thirdLiveAccountDAO.update(entity);
        } else {
            entity = new ThirdLiveAccountEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(vo.getEa());
            entity.setAppId(vo.getAppId());
            entity.setSecretKey(vo.getSecretKey());
            entity.setPlatform(vo.getPlatform());
            thirdLiveAccountDAO.insert(entity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<GetThirdAccountResult> getThirdAccount(GetThirdAccountArg arg) {
        ThirdLiveAccountEntity entity = thirdLiveAccountDAO.getByEa(arg.getEa(), arg.getPlatform());
        GetThirdAccountResult result = new GetThirdAccountResult();
        if (Objects.nonNull(entity)) {
            result.setAppId(entity.getAppId());
            result.setSecretKey(entity.getSecretKey());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<ChannelsAccountResult> getAccountByMaterials(LiveMaterialsVO vo) {
        String ea = objectManager.getObjectEa(vo.getObjectId(), vo.getObjectType());
        if(StringUtils.isBlank(vo.getId())){
            //没有视频号id,先查直播下是否已有视频号id
            if(StringUtils.isNotBlank(vo.getMarketingEventId())){
                MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), vo.getMarketingEventId());
                if(Objects.nonNull(liveEntity) && StringUtils.isNotBlank(liveEntity.getAssociatedAccountId())){
                    return channelsManager.getAccount(ea,vo.getId());
                }
            }
            //都没有去默认视频号
            Result<List<ChannelsAccountResult>> accounts = channelsManager.getAccountsByEa(ea);
            if(accounts!=null&&CollectionUtils.isNotEmpty(accounts.getData())){
                return Result.newSuccess(accounts.getData().get(0));
            }
            return Result.newSuccess();
        }
        return channelsManager.getAccount(ea,vo.getId());
    }

    @Override
    public Result<GetLiveDetailResult> getDetailLive(String marketingLiveId) {
        MarketingLiveEntity marketingLive = marketingLiveDAO.getById(marketingLiveId);
        if (marketingLive==null){
            log.info("LiveServiceImpl.getDetailLive return null marketingLiveId:{}",marketingLiveId);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        GetLiveDetailResult getLiveDetailResult = BeanUtil.copy(marketingLive,GetLiveDetailResult.class);
        getLiveDetailResult.setStartTime(marketingLive.getStartTime().getTime());
        getLiveDetailResult.setEndTime(marketingLive.getEndTime().getTime());
        getLiveDetailResult.setCoverTaPath(marketingLive.getCover());
        String ea = eieaConverter.enterpriseIdToAccount(marketingLive.getCorpId());
        String cover =  fileV2Manager.getUrlByPath(marketingLive.getCover(),ea,false);
        getLiveDetailResult.setCoverTaPath(cover);
        List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities1 = contentMarketingEventMaterialRelationDAO
                .listByMarketingEventIdAndObjectType(ea,marketingLive.getMarketingEventId(), Lists.newArrayList(26), null);
        List<String> sitelist = contentMarketingEventMaterialRelationEntities1.stream().map(o -> o.getObjectId()).collect(Collectors.toList());
        List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.listByIds(sitelist);
        String signUpSiteId = null;
        String transitSiteId = null;
        for (HexagonSiteEntity entity : hexagonSiteEntityList) {
            if(entity.getName().equals("报名预约")){
                signUpSiteId = entity.getId();
            }
            if(entity.getName().equals("视频号中转页面")){
                transitSiteId = entity.getId();
            }
        }
        getLiveDetailResult.setSignUpSiteId(signUpSiteId);
        getLiveDetailResult.setTransitSiteId(transitSiteId);
        //报名跳转地址
        ObjectEnrollJumpSettingEntity objectEnrollJumpSettingEntity = objectEnrollJumpSettingDAO.getByMarketingEventId(ea, marketingLive.getMarketingEventId());
        if (objectEnrollJumpSettingEntity != null){
            getLiveDetailResult.setJumpObjectId(objectEnrollJumpSettingEntity.getJumpObjectId());
            getLiveDetailResult.setJumpObjectType(objectEnrollJumpSettingEntity.getJumpObjectType());
            getLiveDetailResult.setJumpUrl(objectEnrollJumpSettingEntity.getJumpUrl());
        }
        marketingLive.setAssociatedAccountId(marketingLive.getAssociatedAccountId());
        return Result.newSuccess(getLiveDetailResult);
    }

    @Override
    public Result<GetMarketingLiveByXiaoetongIdResult> getMarketingLiveByXiaoetongId(String xiaoetongId) {
        Optional<MarketingLiveEntity> optional = xiaoetongManager.getMarketingLiveByXiaoetongId(xiaoetongId);
        if (optional.isPresent()){
            GetMarketingLiveByXiaoetongIdResult result = new GetMarketingLiveByXiaoetongIdResult();
            result.setMarketingEventId(optional.get().getMarketingEventId());
            result.setMarketingLiveId(optional.get().getId());
            return Result.newSuccess(result);
        }else {
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
    }

    @Override
    public Result<Map<String, String>> getMarketingLiveByPolyvId(String polyvId) {
        Optional<MarketingLiveEntity> polyvLive = polyvManager.getMarketingLiveByPolyvId(polyvId);
        if (polyvLive.isPresent()) {
            MarketingLiveEntity entity = polyvLive.get();
            Map<String, String> res = new HashMap<>();
            res.put("message", I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2937) + entity.getTitle() + I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2937_1));
            res.put("marketingEventId", entity.getMarketingEventId());
            return Result.newSuccess(res);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Map<String, String>> checkRelate(CheckThirdRelateArg arg) {
        if (Objects.equals(arg.getPlatform(), LivePlatformEnum.VHALL.getType())) {
            MarketingLiveEntity entity = marketingLiveDAO.getMarketingLiveByXiaoetongId(arg.getId());
            if (entity != null) {
                Map<String, String> res = new HashMap<>();
                res.put("message", I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2937) + entity.getTitle() + I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVESERVICEIMPL_2937_1));
                res.put("marketingEventId", entity.getMarketingEventId());
                return Result.newSuccess(res);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<CheckUserHaveSubmitResult> checkH5UserHaveSubmit(String liveId, String fingerPrint, Map<String, String> allEnterpriseMemberCookieMap, boolean checkMemberAndForm) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(liveId);
        if (entity == null){
            log.warn("LiveServiceImpl.checkH5UserHaveSubmit live entity is not exist liveId:{}", liveId);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        CheckUserHaveSubmitResult checkUserHaveSubmitResult = new CheckUserHaveSubmitResult();
        checkUserHaveSubmitResult.setCode(GetViewUrlValidEnum.VALID.getStatus());
        Optional<String> optionalMemberId = memberManager.getH5LoginMemberId(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), allEnterpriseMemberCookieMap);
        if (optionalMemberId.isPresent() && org.apache.commons.lang3.StringUtils.isNotBlank(optionalMemberId.get())) {
            ObjectData objectData = crmV2Manager.getDetail(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null && objectData.getId().equals(optionalMemberId.get())){
                //判断会员是否报名了直播
                MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(eieaConverter.enterpriseIdToAccount(entity.getCorpId()),
                        entity.getMarketingEventId(), optionalMemberId.get());
                if (memberAccessibleCampaignEntity != null){
                    checkUserHaveSubmitResult.setSubmit(true);
                    checkUserHaveSubmitResult.setPhone(objectData.getString("phone"));
                }
                if (Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                    checkUserHaveSubmitResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
                }
                if (memberAccessibleCampaignEntity != null || !checkMemberAndForm) {
                    return Result.newSuccess(checkUserHaveSubmitResult);
                }
            }
        }
        //非会员
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList =
                customizeFormDataUserDAO. queryCustomizeFormDataUsersByFingerPrintAndEventId(fingerPrint, entity.getMarketingEventId());
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)){
            log.info("LiveServiceImpl.checkH5UserHaveSubmit user has submit customizeFormDataUserEntityList:{}", customizeFormDataUserEntityList);
            checkUserHaveSubmitResult.setSubmit(true);
            CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntityList.get(0);
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                checkUserHaveSubmitResult.setPhone(customizeFormDataUserEntityList.get(0).getSubmitContent().getPhone());
            }
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(customizeFormDataUserEntity.getCampaignId());
            if (campaignMergeDataEntity != null && campaignMergeDataEntity.getCampaignMembersObjId() != null) {
                ObjectData objectData = crmV2Manager.getObjectData(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
                if (objectData == null || Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                    checkUserHaveSubmitResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
                }
            }
        }else {
            checkUserHaveSubmitResult.setSubmit(false);
        }

        return Result.newSuccess(checkUserHaveSubmitResult);
    }

    @Override
    public Result<CheckUserHaveSubmitResult> checkWxServiceUserHaveSubmit(String id, String wxAppId, String wxOpenId) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(id);
        if (entity == null){
            log.warn("LiveServiceImpl.checkWxServiceUserHaveSubmit live entity is not exist id:{}", id);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        //判断是否是会员
        CheckUserHaveSubmitResult checkUserHaveSubmitResult = new CheckUserHaveSubmitResult();
        checkUserHaveSubmitResult.setCode(GetViewUrlValidEnum.VALID.getStatus());
        Optional<String> optionalMemberId = memberManager.getWxOpenIdByMemberId(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), wxAppId, wxOpenId);
        if (optionalMemberId.isPresent() && org.apache.commons.lang3.StringUtils.isNotBlank(optionalMemberId.get())) {
            ObjectData objectData = crmV2Manager.getDetail(eieaConverter.enterpriseIdToAccount(entity.getCorpId()), -10000, CrmObjectApiNameEnum.MEMBER.getName(), optionalMemberId.get());
            if (objectData != null && objectData.getId().equals(optionalMemberId.get())){
                //判断会员是否报名了直播
                MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(eieaConverter.enterpriseIdToAccount(entity.getCorpId()),
                        entity.getMarketingEventId(), optionalMemberId.get());
                if (memberAccessibleCampaignEntity != null){
                    checkUserHaveSubmitResult.setSubmit(true);
                    checkUserHaveSubmitResult.setPhone(objectData.getString("phone"));
                }

                if (Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                    checkUserHaveSubmitResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
                }

                return Result.newSuccess(checkUserHaveSubmitResult);
            }
        }

        //非会员
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList =
                customizeFormDataUserDAO.queryCustomizeFormDataUsersByOpenIdAndEventId(wxOpenId, wxAppId, entity.getMarketingEventId());
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)){
            log.info("LiveServiceImpl.checkWxServiceUserHaveSubmit user has submit customizeFormDataUserEntityList:{}", customizeFormDataUserEntityList);
            checkUserHaveSubmitResult.setSubmit(true);
            CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntityList.get(0);
            if (customizeFormDataUserEntity.getSubmitContent() != null) {
                checkUserHaveSubmitResult.setPhone(customizeFormDataUserEntityList.get(0).getSubmitContent().getPhone());
            }
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(customizeFormDataUserEntity.getCampaignId());
            if (campaignMergeDataEntity != null && campaignMergeDataEntity.getCampaignMembersObjId() != null) {
                ObjectData objectData = crmV2Manager.getObjectData(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
                if (objectData == null || Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                    checkUserHaveSubmitResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
                }
            }
        }else {
            checkUserHaveSubmitResult.setSubmit(false);
        }

        return Result.newSuccess(checkUserHaveSubmitResult);
    }

    @Override
    public Result<CheckAndSyncUserToXiaoetongResult> checkAndSyncUserToXiaoetong(String id, String phone, String xiaoetongViewUrl) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(id);
        if (entity == null){
            log.warn("LiveServiceImpl.checkWxServiceUserHaveSubmit live entity is not exist liveId:{}", id);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        String redirectUrl = xiaoetongViewUrl == null ? entity.getViewUrl() : xiaoetongViewUrl;
        String ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
        CheckAndSyncUserToXiaoetongResult checkAndSyncUserToXiaoetongResult = new CheckAndSyncUserToXiaoetongResult();
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(eieaConverter.enterpriseIdToAccount(entity.getCorpId()),
                entity.getMarketingEventId(), phone, false);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)){
            checkAndSyncUserToXiaoetongResult.setSubmit(false);
            return Result.newSuccess(checkAndSyncUserToXiaoetongResult);
        }

        log.info("campaignMergeDataEntityList:{}", campaignMergeDataEntityList);
        String userId = null;
        checkAndSyncUserToXiaoetongResult.setSubmit(true);
        LiveUserAccountRelationEntity liveUserAccountRelationEntity = liveUserAccountRelationDAO.getByPhone(ea, LivePlatformEnum.XIAOETONG.getType(), phone);
        log.info("liveUserAccountRelationEntity:{}", liveUserAccountRelationEntity);
        if (liveUserAccountRelationEntity != null){
            userId = liveUserAccountRelationEntity.getOuterUserId();
        }else {
            Optional<String> userOpt = xiaoetongManager.getLoginedUserInfo(ea, phone);
            if (!userOpt.isPresent()){
                Optional<String> userIdOpt = xiaoetongManager.loginXiaoetongUser(ea, phone, campaignMergeDataEntityList.get(0).getName());
                if (!userIdOpt.isPresent()){
                    return Result.newError(SHErrorCode.SYNC_XIAOETONG_LOGIN_FAILED);
                }
                userId = userIdOpt.get();
            }else {
                userId = userOpt.get();
            }

            LiveUserAccountRelationEntity relationEntity = new LiveUserAccountRelationEntity();
            relationEntity.setId(UUIDUtil.getUUID());
            relationEntity.setEa(ea);
            relationEntity.setOuterUserId(userId);
            relationEntity.setPhone(phone);
            relationEntity.setType(LivePlatformEnum.XIAOETONG.getType());
            liveUserAccountRelationDAO.insert(relationEntity);
        }

        Optional<String> redirectOpt = xiaoetongManager.queryUserLoginRedirect(ea, userId, redirectUrl);
        if (!redirectOpt.isPresent()){
            return Result.newError(SHErrorCode.SYNC_XIAOETONG_LOGIN_FAILED);
        }
        checkAndSyncUserToXiaoetongResult.setLoginUrl(redirectOpt.get());
        return Result.newSuccess(checkAndSyncUserToXiaoetongResult);
    }

    @Override
    public void xiaoetongMessageCallback(String appId, String encryptXmlBody) {
        StringReader sr1 = null;
        StringReader sr2 = null;
        String format = "<xml><Encrypt><![CDATA[%1$s]]></Encrypt></xml>";
        try {
            WXBizMsgCrypt pc = new WXBizMsgCrypt(XIAOETONG_CALLBACK_TOKEN, XIAOETONG_CALLBACK_AESKEY, appId);
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            DocumentBuilder db = dbf.newDocumentBuilder();
            sr1 = new StringReader(encryptXmlBody);
            InputSource is = new InputSource(sr1);
            Document document = db.parse(is);

            Element root = document.getDocumentElement();
            NodeList nodelist1 = root.getElementsByTagName("Encrypt");
            NodeList nodelist2 = root.getElementsByTagName("MsgSignature");
            NodeList nodelist3 = root.getElementsByTagName("TimeStamp");
            NodeList nodelist4 = root.getElementsByTagName("Nonce");
            String encrypt = nodelist1.item(0).getTextContent();
            String msgSignature = nodelist2.item(0).getTextContent();
            String timeStamp = nodelist3.item(0).getTextContent();
            String nonce = nodelist4.item(0).getTextContent();

            String fromXML = String.format(format, encrypt);
            String content = pc.decryptMsg(msgSignature, timeStamp, nonce, fromXML);
            sr2 = new StringReader(content);
            is = new InputSource(sr2);
            document = db.parse(is);
            root = document.getDocumentElement();
            NodeList nodelistType = root.getElementsByTagName("type");
            if (nodelistType.item(0).getTextContent().equals("account_merge")){
                NodeList nodeListSourceUser = root.getElementsByTagName("source_user_id");
                NodeList nodeListTargetUser = root.getElementsByTagName("target_user_id");
                String sourceUserId = nodeListSourceUser.item(0).getTextContent();
                String targetUserId = nodeListTargetUser.item(0).getTextContent();
                xiaoetongManager.doHandleUserMergedMessage(appId, sourceUserId, targetUserId);
            }
        }catch (Exception e){
            log.info("xiaoetongMessageCallback error appId:{} encryptXmlBody:{} e:", appId, encryptXmlBody, e);
        }finally {
            sr1.close();
            sr2.close();
        }
    }

    @Override
    public Result<EnrollDataCountResult> countUnSaveLiveEnrollData(String ea, String marketingEventId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
        if (marketingLiveEntity == null) {
            log.warn("LiveServiceImpl.countUnSaveLiveEnrollData marketingLiveEntity is null marketingEventId:{}", marketingEventId);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        List<Integer> saveCrmStatus = new ArrayList<>();
        saveCrmStatus.add(99);
        Integer allDataSize = campaignMergeDataDAO.countCampaignLiveData(ea, marketingEventId, null, null, null, null, null, saveCrmStatus, marketingLiveEntity.getPlatform(), null);
        EnrollDataCountResult res = new EnrollDataCountResult();
        res.setUnSaveCrmCount(allDataSize);
        return Result.newSuccess(res);
    }

    @Override
    public Result<XiaoetongCommonLoginResult> xiaoetongCommonLogin(String appId, String phone, String xiaoetongViewUrl, String phoneVerifyCode) {
        boolean phoneVerified = verificationCodeManager.checkSMCode(phone, phoneVerifyCode).isSuccess();
        if (!phoneVerified){
            return Result.newError(SHErrorCode.PHONE_VERIFY_CODE_ERROR);
        }

        List<XiaoetongAccountEntity> xiaoetongAccountEntityList = xiaoetongAccountDAO.getByAppId(appId);
        if (CollectionUtils.isEmpty(xiaoetongAccountEntityList)){
            return Result.newError(SHErrorCode.UN_BIND_XIAOETONG_ACCOUNT);
        }

        String ea = xiaoetongAccountEntityList.get(0).getEa();
        LiveUserAccountRelationEntity liveUserAccountRelationEntity = liveUserAccountRelationDAO.getByPhone(ea, LivePlatformEnum.XIAOETONG.getType(), phone);
        log.info("xiaoetongCommonLogin.liveUserAccountRelationEntity:{}", liveUserAccountRelationEntity);
        String outerUid = null;
        if (liveUserAccountRelationEntity != null){
            outerUid = liveUserAccountRelationEntity.getOuterUserId();
        }else {
            Optional<String> userOpt = xiaoetongManager.getLoginedUserInfo(ea, phone);
            if (!userOpt.isPresent()){
                Optional<String> userIdOpt = xiaoetongManager.loginXiaoetongUser(ea, phone, null);
                if (!userIdOpt.isPresent()){
                    return Result.newError(SHErrorCode.SYNC_XIAOETONG_LOGIN_FAILED);
                }
                outerUid = userIdOpt.get();
            }else {
                outerUid = userOpt.get();
            }

            LiveUserAccountRelationEntity relationEntity = new LiveUserAccountRelationEntity();
            relationEntity.setId(UUIDUtil.getUUID());
            relationEntity.setEa(ea);
            relationEntity.setOuterUserId(outerUid);
            relationEntity.setPhone(phone);
            relationEntity.setType(LivePlatformEnum.XIAOETONG.getType());
            liveUserAccountRelationDAO.insert(relationEntity);
        }

        Optional<String> redirectOpt = xiaoetongManager.queryUserLoginRedirect(ea, outerUid, xiaoetongViewUrl);
        if (!redirectOpt.isPresent()){
            return Result.newError(SHErrorCode.SYNC_XIAOETONG_LOGIN_FAILED);
        }

        XiaoetongCommonLoginResult result = new XiaoetongCommonLoginResult();
        result.setLoginUrl(redirectOpt.get());
        return Result.newSuccess(result);
    }

    @Override
    public Result sendXiaoetongLoginSms(String phone, String appId) {
        List<XiaoetongAccountEntity> xiaoetongAccountEntityList = xiaoetongAccountDAO.getByAppId(appId);
        if (CollectionUtils.isEmpty(xiaoetongAccountEntityList)){
            return Result.newError(SHErrorCode.UN_BIND_XIAOETONG_ACCOUNT);
        }

        String ea = xiaoetongAccountEntityList.get(0).getEa();
        SendVerificationCodeArg arg = new SendVerificationCodeArg();
        arg.setEa(ea);
        arg.setPhone(phone);
        return sendService.sendVerificationCode(arg);
    }

    @Override
    public Result<CheckAndSyncUserToXiaoetongResult> checkPolyvSubmit(String liveId, String phone) {
        MarketingLiveEntity liveEntity = marketingLiveDAO.getById(liveId);
        if (null == liveEntity) {
            log.warn("LiveServiceImpl.checkPolyvSubmit live entity is not exist liveId:{}", liveId);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }

        if (Objects.equals(liveEntity.getPlatform(), LivePlatformEnum.MUDU.getType())) {
            return checkMuduSubmit(liveId, phone);
        } else if (Objects.equals(liveEntity.getPlatform(), LivePlatformEnum.VHALL.getType())) {
            return checkVHallSubmit(liveId, phone);
        } else {
            return checkPolyvSubmit2(liveId, phone);
        }
    }

    public Result<CheckAndSyncUserToXiaoetongResult> checkPolyvSubmit2(String liveId, String phone) {
        MarketingLiveEntity liveEntity = marketingLiveDAO.getById(liveId);
        if (null == liveEntity) {
            log.warn("LiveServiceImpl.checkPolyvSubmit live entity is not exist liveId:{}", liveId);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        CheckAndSyncUserToXiaoetongResult polyvResult = new CheckAndSyncUserToXiaoetongResult();
        polyvResult.setCode(GetViewUrlValidEnum.VALID.getStatus());
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(eieaConverter.enterpriseIdToAccount(liveEntity.getCorpId()),
                liveEntity.getMarketingEventId(), phone, false);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            polyvResult.setSubmit(false);
            return Result.newSuccess(polyvResult);
        }
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntityList.get(0);
        if (campaignMergeDataEntity.getCampaignMembersObjId() != null) {
            ObjectData objectData = crmV2Manager.getDetail(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
            if (Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                polyvResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
            }
        }
        log.info("campaignMergeDataEntityList:{}", campaignMergeDataEntityList);
        //记录用户的手机号
        String userId;
        List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntityList = marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(liveEntity.getMarketingEventId(), Lists.newArrayList(phone));
        if (CollectionUtils.isNotEmpty(marketingLiveViewLoginEntityList)) {
            userId = marketingLiveViewLoginEntityList.get(0).getId();
        } else {
            MarketingLiveViewLoginEntity marketingLiveViewLoginEntity = new MarketingLiveViewLoginEntity();
            marketingLiveViewLoginEntity.setId(UUIDUtil.getUUID());
            marketingLiveViewLoginEntity.setMarketingEventId(liveEntity.getMarketingEventId());
            marketingLiveViewLoginEntity.setPhone(phone);
            marketingLiveViewLoginDAO.insert(marketingLiveViewLoginEntity);
            userId = marketingLiveViewLoginEntity.getId();
        }
        String ea = eieaConverter.enterpriseIdToAccount(liveEntity.getCorpId());
        PolyvLiveInnerData liveDetail = polyvManager.getLiveDetail(ea, liveEntity.getXiaoetongLiveId());
        String externalKey = null;
        if (null != liveDetail && null != liveDetail.getData() && 200 == liveDetail.getCode()) {
            List<PolyvLiveInnerData.AuthSetting> authSetting = liveDetail.getData().getAuthSettings();
            for (PolyvLiveInnerData.AuthSetting setting : authSetting) {
                if (null != setting && "Y".equals(setting.getEnabled()) && "external".equals(setting.getAuthType())) {
                    externalKey = setting.getExternalKey();
                    break;
                }
            }
        }
        if (StringUtils.isBlank(externalKey)) {
            polyvResult.setSubmit(true);
            polyvResult.setLoginUrl(POLYV_VIEW_URL + liveEntity.getXiaoetongLiveId());
            return Result.newSuccess(polyvResult);
        }
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        String appId = polyvAccountEntity.getAppId();
        String appSecret = polyvAccountEntity.getAppSecret();
        String timestamp = System.currentTimeMillis() + "";
        polyvResult.setSubmit(true);
        String watchUrl = null;
        String signText = externalKey + userId + externalKey + timestamp;
        try {
            String viewSign = LiveSignUtil.md5Hex(signText);
            watchUrl = POLYV_VIEW_URL + liveEntity.getXiaoetongLiveId() + "?userid=" + userId + "&ts=" + timestamp + "&sign=" + viewSign;
        } catch (Exception e) {
            log.info("get viewSign fail {}", e.getMessage());
        }
        if (StringUtils.isBlank(watchUrl)) {
            watchUrl = POLYV_VIEW_URL + liveEntity.getXiaoetongLiveId();
        }
        polyvResult.setLoginUrl(watchUrl);
        return Result.newSuccess(polyvResult);
    }

    public Result<CheckAndSyncUserToXiaoetongResult> checkVHallSubmit(String liveId, String phone) {
        MarketingLiveEntity liveEntity = marketingLiveDAO.getById(liveId);
        if (null == liveEntity) {
            log.warn("LiveServiceImpl.checkVHallSubmit live entity is not exist liveId:{}", liveId);
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        String ea = eieaConverter.enterpriseIdToAccount(liveEntity.getCorpId());
        CheckAndSyncUserToXiaoetongResult polyvResult = new CheckAndSyncUserToXiaoetongResult();
        polyvResult.setCode(GetViewUrlValidEnum.VALID.getStatus());
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(eieaConverter.enterpriseIdToAccount(liveEntity.getCorpId()),
                liveEntity.getMarketingEventId(), phone, false);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            polyvResult.setSubmit(false);
            return Result.newSuccess(polyvResult);
        }
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntityList.get(0);
        if (campaignMergeDataEntity.getCampaignMembersObjId() != null) {
            ObjectData objectData = crmV2Manager.getDetail(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
            if (Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                polyvResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
            }
        }
        log.info("campaignMergeDataEntityList:{}", campaignMergeDataEntityList);
        //记录用户的手机号
        String userId;
        List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntityList = marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(liveEntity.getMarketingEventId(), Lists.newArrayList(phone));
        if (CollectionUtils.isNotEmpty(marketingLiveViewLoginEntityList)) {
            userId = marketingLiveViewLoginEntityList.get(0).getId();
        } else {
            MarketingLiveViewLoginEntity marketingLiveViewLoginEntity = new MarketingLiveViewLoginEntity();
            marketingLiveViewLoginEntity.setId(UUIDUtil.getUUID());
            marketingLiveViewLoginEntity.setMarketingEventId(liveEntity.getMarketingEventId());
            marketingLiveViewLoginEntity.setPhone(phone);
            marketingLiveViewLoginDAO.insert(marketingLiveViewLoginEntity);
            userId = marketingLiveViewLoginEntity.getId();
        }

        polyvResult.setSubmit(true);
        vHallManager.createUser(ea, campaignMergeDataEntity.getName(), campaignMergeDataEntity.getId());
        polyvResult.setLoginUrl(buildVHallViewUrl(ea) + "/v3/lives/embedclientfull/watch/" + liveEntity.getXiaoetongLiveId() + "?" + "third_user_id=" + campaignMergeDataEntity.getId());
        return Result.newSuccess(polyvResult);
    }

    /**
     * 获取企业配置的微吼自定义域名
     * @param ea
     * @return
     */
    private String buildVHallViewUrl(String ea) {
        String defaultViewUrl = VHALL_VIEW_URL;
        if (StringUtils.isBlank(vHallCustomizeHost)) {
            return defaultViewUrl;
        }
        Map<String, String> vHallCustomizedHostMap = GsonUtil.fromJson(vHallCustomizeHost, Map.class);
        log.info("vHallCustomizedHostMap:{}", vHallCustomizedHostMap);
        if (vHallCustomizedHostMap == null || vHallCustomizedHostMap.isEmpty()) {
            return defaultViewUrl;
        }

        if (vHallCustomizedHostMap.containsKey(ea)) {
            return vHallCustomizedHostMap.get(ea);
        }
        return defaultViewUrl;
    }

    public Result<CheckAndSyncUserToXiaoetongResult> checkMuduSubmit(String liveId, String phone) {
        MarketingLiveEntity liveEntity = marketingLiveDAO.getById(liveId);
        if (null == liveEntity) {
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        String ea = eieaConverter.enterpriseIdToAccount(liveEntity.getCorpId());
        CheckAndSyncUserToXiaoetongResult polyvResult = new CheckAndSyncUserToXiaoetongResult();
        polyvResult.setCode(GetViewUrlValidEnum.VALID.getStatus());

        String mkId = liveEntity.getMarketingEventId();
        List<CampaignMergeDataEntity> campaignMergeDataEntityList;
        if (Objects.equals(liveEntity.getSubEvent(), 1)) {
            // 子活动，可通过校验父活动是否报名过
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, liveEntity.getMarketingEventId());
            if (marketingEventData != null) {
                mkId = marketingEventData.getParentId();
                campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, marketingEventData.getParentId(), phone, false);
                if (CollectionUtils.isEmpty(campaignMergeDataEntityList)){
                    log.info("LiveServiceImpl.checkMuduSubmit failed user not submit liveId:{}, phone:{}", liveId, phone);
                    polyvResult.setCode(GetViewUrlValidEnum.NOT_ENROLL.getStatus());
                    polyvResult.setSubmit(false);
                    return Result.newSuccess(polyvResult);
                }
            } else {
                polyvResult.setCode(GetViewUrlValidEnum.NOT_ENROLL.getStatus());
                polyvResult.setSubmit(false);
                return Result.newSuccess(polyvResult);
            }
        } else {
            // 主活动
            campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, liveEntity.getMarketingEventId(), phone, false);
            if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
                polyvResult.setCode(GetViewUrlValidEnum.NOT_ENROLL.getStatus());
                polyvResult.setSubmit(false);
                return Result.newSuccess(polyvResult);
            }
        }

        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntityList.get(0);
        if (campaignMergeDataEntity.getCampaignMembersObjId() != null) {
            ObjectData objectData = crmV2Manager.getDetail(campaignMergeDataEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntity.getCampaignMembersObjId());
            if (Arrays.asList("processing","rejected").contains(String.valueOf(objectData.get("approval_status")))){
                polyvResult.setCode(GetViewUrlValidEnum.NOT_APPROVAL.getStatus());
            }
        }
        log.info("campaignMergeDataEntityList:{}", campaignMergeDataEntityList);
        //记录用户的手机号
        String userId;
        List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntityList = marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(mkId, Lists.newArrayList(phone));
        if (CollectionUtils.isNotEmpty(marketingLiveViewLoginEntityList)) {
            userId = marketingLiveViewLoginEntityList.get(0).getId();
        } else {
            MarketingLiveViewLoginEntity marketingLiveViewLoginEntity = new MarketingLiveViewLoginEntity();
            marketingLiveViewLoginEntity.setId(UUIDUtil.getUUID());
            marketingLiveViewLoginEntity.setMarketingEventId(mkId);
            marketingLiveViewLoginEntity.setPhone(phone);
            marketingLiveViewLoginDAO.insert(marketingLiveViewLoginEntity);
            userId = marketingLiveViewLoginEntity.getId();
        }
        polyvResult.setSubmit(true);
        // 处理目睹直播
        if (Objects.equals(liveEntity.getPlatform(), LivePlatformEnum.MUDU.getType())) {
            String eventId = null;
            if (Objects.equals(liveEntity.getSubEvent(), 1)) {
                eventId = liveEntity.getMuduParentId();
            } else {
                eventId = liveEntity.getXiaoetongLiveId();
            }
            String accessToken = muduManager.getAccessToken(ea);
            MuduApiPasswordLessLoginResult muduApiPasswordLessLoginResult = muduManager.passwordLessLogin(accessToken, eventId, userId, campaignMergeDataEntity.getName(), phone);
            if (muduApiPasswordLessLoginResult != null) {
                MuduApiPasswordLessLoginResult.LoginUrl loginUrl = muduApiPasswordLessLoginResult.getLoginUrl();
                if (!Objects.equals(liveEntity.getSubEvent(), 1)) {
                    polyvResult.setLoginUrl(loginUrl.getEventUrl().getWatchUrl());
                } else {
                    List<MuduApiPasswordLessLoginResult.RoomUrl> roomUrls = loginUrl.getRoomUrls();
                    for (MuduApiPasswordLessLoginResult.RoomUrl roomUrl : roomUrls) {
                        if (Objects.equals(roomUrl.getRoomId(), liveEntity.getXiaoetongLiveId())) {
                            polyvResult.setLoginUrl(roomUrl.getWatchUrl());
                            break;
                        }
                    }
                }
            }
        }
        return Result.newSuccess(polyvResult);
    }

    @Override
    public Map<String, String> externalAuth(String channelId, String userid, Long ts, String token) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(userid));
        Preconditions.checkArgument(null != ts);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(token));
        return polyvManager.externalAuth(channelId,userid,ts,token);
    }

    @Override
    public Result<PolyvLiveListResult> getLiveInfo(String ea, String id) {
        return polyvManager.getLiveInfo(ea,id);
    }

    @Override
    public Result<String> shareXiaoetongAccessToken(Integer tenantId, Integer userId) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        XiaoetongAccountEntity xiaoetongAccountEntity = xiaoetongAccountDAO.getByEa(ea);
        if (xiaoetongAccountEntity == null) {
            return Result.newError(SHErrorCode.GET_XIAOETONG_ACCESS_TOKEN_FAILED);
        }

        Optional<String> accessOpt = xiaoetongManager.getAccessToken(ea, xiaoetongAccountEntity.getAppId(), xiaoetongAccountEntity.getSecretKey(), xiaoetongAccountEntity.getClientId());
        if (!accessOpt.isPresent()) {
            return Result.newError(SHErrorCode.GET_XIAOETONG_ACCESS_TOKEN_FAILED);
        }

        return Result.newSuccess(accessOpt.get());
    }


    @Override
    public Result<CheckMemberSubmitResult> checkMemberSubmit(CheckMemberSubmitArg arg) {
        MarketingLiveEntity entity = marketingLiveDAO.getById(arg.getLiveId());
        if (entity == null){
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_EXIST);
        }
        String ea = eieaConverter.enterpriseIdToAccount(entity.getCorpId());
        ObjectData objectData = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), arg.getMemberId());
        if (objectData == null) {
            return Result.newError(SHErrorCode.MEMBER_NOT_EXIST);
        }
        String phone = objectData.getString("phone");
        //判断会员是否报名了直播
        MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignDAO.getMemberAccessibleCampaignData(eieaConverter.enterpriseIdToAccount(entity.getCorpId()),
                entity.getMarketingEventId(), arg.getMemberId());
        CheckMemberSubmitResult checkMemberSubmitResult = new CheckMemberSubmitResult();
        checkMemberSubmitResult.setSubmit(false);
        if (memberAccessibleCampaignEntity != null) {
            checkMemberSubmitResult.setSubmit(true);
            checkMemberSubmitResult.setPhone(phone);
        } else if (StringUtils.isNotBlank(phone))  {
            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, entity.getMarketingEventId(), phone, false);
            if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                checkMemberSubmitResult.setSubmit(true);
                checkMemberSubmitResult.setPhone(phone);
            }
        }
        return Result.newSuccess(checkMemberSubmitResult);
    }

    @Override
    public Result<List<ChannelsAccountResult>> getChannelsAccountByEa(String ea) {
        return channelsManager.getAccountsByEa(ea);
    }

    @Override
    public Result<Void> setDefaultChannel(String ea, String id) {
        return channelsManager.setDefaultChannel(ea,id);
    }

    @Override
    public Result<Void> deleteChannel(String ea, String id) {
        return channelsManager.deleteChannel(ea,id);
    }
    private String getLivePhoneByEmail(String email) {
        if (StringUtils.isEmpty(email) || !email.endsWith("@fxiaoke.com")) {
            return null;
        }
        int pos = email.indexOf("@fxiaoke.com");
        return StringUtils.substring(email, 0, pos);
    }

    private MarketingLiveEntity buildFsPlatformMarketingCreateLiveEntity(CreateLiveVO vo, com.facishare.training.outer.api.result.live.CreateLiveResult createLiveResult,
                                                               String marketingEventId){
        MarketingLiveEntity entity = new MarketingLiveEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setCorpId(vo.getCorpId());
        entity.setMarketingEventId(marketingEventId);
        entity.setTitle(vo.getTitle());
        entity.setDescription(vo.getDesc());
        entity.setStartTime(new Date(vo.getStartTime()));
        if (vo.getEndTime() != null) {
            entity.setEndTime(new Date(vo.getEndTime()));
        }
        entity.setCreateUserId(vo.getFsUserId());
        entity.setLectureName(vo.getLectureUserName());
        entity.setLecturePassword(vo.getLecturePassword());
        entity.setPlatform(vo.getLivePlatform());
        entity.setLiveId(createLiveResult.getLiveId());
        String viewUrl = "https://live.vhall.com/" + createLiveResult.getLiveId();
        entity.setViewUrl(viewUrl);
        entity.setLectureUrl(createLiveResult.getLectureUrl());
        entity.setCover(createLiveResult.getCover());
        entity.setStatus(createLiveResult.getStatus());
        if (vo.getChatOn() == null){
            vo.setChatOn(0);
        }

        if (vo.getAutoRecord() == null){
            vo.setAutoRecord(LiveRecordEnum.NO_RECORD.getType());
        }
        if (CollectionUtils.isNotEmpty(vo.getTagNames())){
            entity.setTags(gson.toJson(vo.getTagNames()));
        }
        entity.setChatOn(vo.getChatOn());
        entity.setAutoRecord(vo.getAutoRecord());

        entity.setMaxLiveCount(vo.getMaxLiveCount());
        entity.setShowAcitivityList(vo.isShowActivityList());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        return entity;
    }

    private MarketingLiveEntity buildOtherPlatformMarketingCreateLiveEntity(CreateLiveVO vo, String marketingEventId, String cover){
        MarketingLiveEntity entity = new MarketingLiveEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setCorpId(vo.getCorpId());
        entity.setMarketingEventId(marketingEventId);
        entity.setTitle(vo.getTitle());
        entity.setDescription(vo.getDesc());
        entity.setStartTime(new Date(vo.getStartTime()));
        if (vo.getEndTime() != null) {
            entity.setEndTime(new Date(vo.getEndTime()));
        }
        entity.setUpdateTime(new Date(vo.getEndTime()));
        entity.setCreateUserId(vo.getFsUserId());
        entity.setLectureName(vo.getLectureUserName());
        entity.setPlatform(vo.getLivePlatform());
        entity.setCover(cover);
        entity.setViewUrl(vo.getOtherPlatformLiveUrl());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setStatus(LiveStatusEnum.NOT_START.getStatus());
        entity.setOtherPlatformUrl(vo.getOtherPlatformLiveUrl());
        if (vo.getLivePlatform() == LivePlatformEnum.XIAOETONG.getType() || vo.getLivePlatform() == LivePlatformEnum.POLYV.getType() || vo.getLivePlatform() == LivePlatformEnum.MUDU.getType()){
            entity.setXiaoetongLiveId(vo.getXiaoetongLiveId());
        }
        if (CollectionUtils.isNotEmpty(vo.getTagNames())){
            entity.setTags(gson.toJson(vo.getTagNames()));
        }
        entity.setShowAcitivityList(vo.isShowActivityList());
        entity.setMuduParentId(vo.getMuduParentId());
        entity.setSubEvent(vo.getSubEvent());
        if (vo.getLivePlatform().intValue() == LivePlatformEnum.CHANNELS.getType()){
            entity.setAssociatedAccountId(vo.getAssociatedAccountId());
        }
        return entity;
    }

    private MarketingLiveEntity buildFsPlatformMarketingUpdateLiveEntity(CreateLiveVO arg, MarketingLiveEntity oldEntity, CreateLiveResult createLiveResult, UpdateResult updateResult){
        MarketingLiveEntity entity = BeanUtil.copy(oldEntity, MarketingLiveEntity.class);
        if (arg.getChatOn() == null){
            arg.setChatOn(0);
        }
        if (arg.getAutoRecord() == null){
            arg.setAutoRecord(LiveRecordEnum.NO_RECORD.getType());
        }

        entity.setTitle(arg.getTitle());
        entity.setStartTime(new Date(arg.getStartTime()));
        if (arg.getEndTime() != null){
            entity.setEndTime(new Date(arg.getEndTime()));
        }else {
            entity.setEndTime(oldEntity.getEndTime());
        }
        entity.setCreateUserId(arg.getFsUserId());
        entity.setLectureName(arg.getLectureUserName());
        entity.setDescription(arg.getDesc());
        entity.setChatOn(arg.getChatOn());
        entity.setAutoRecord(arg.getAutoRecord());
        entity.setMaxLiveCount(arg.getMaxLiveCount());
        entity.setLecturePassword(arg.getLecturePassword());
        entity.setUpdateTime(new Date());
        entity.setPlatform(arg.getLivePlatform());
        entity.setMaxLiveCount(arg.getMaxLiveCount());
        entity.setShowAcitivityList(arg.isShowActivityList());
        if (createLiveResult != null){
            entity.setLiveId(createLiveResult.getLiveId());
            String viewUrl = "https://live.vhall.com/" + createLiveResult.getLiveId();
            entity.setViewUrl(viewUrl);
            entity.setLectureUrl(createLiveResult.getLectureUrl());
            entity.setCover(createLiveResult.getCover());
            entity.setStatus(createLiveResult.getStatus());
            entity.setLiveId(createLiveResult.getLiveId());
            CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
            String shortViewUrl = liveUserH5Url + entity.getId();
            shortUrlArg.setUrl(shortViewUrl);
            Optional<String> shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
            if (shortUrlResult.isPresent()){
                entity.setShortViewUrl(shortUrlResult.get());
            }

            String shortLectrueUrl = liveLectureUrl + entity.getId();
            shortUrlArg.setUrl(shortLectrueUrl);
            shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
            if (shortUrlResult.isPresent()){
                entity.setShortLectureUrl(shortUrlResult.get());
            }
            entity.setCreateTime(new Date());
        }
        if (updateResult != null){
            if (updateResult.getCover() != null){
                entity.setCover(updateResult.getCover());
            }
        }

        if (CollectionUtils.isNotEmpty(arg.getTagNames())){
            entity.setTags(gson.toJson(arg.getTagNames()));
        }

        return entity;
    }
    private MarketingLiveEntity buildOtherPlatformMarketingUpdateLiveEntity(CreateLiveVO arg, MarketingLiveEntity oldEntity, String otherPlatformCover){
        MarketingLiveEntity entity = BeanUtil.copy(oldEntity, MarketingLiveEntity.class);
        entity.setTitle(arg.getTitle());
        entity.setDescription(arg.getDesc());
        entity.setStartTime(new Date(arg.getStartTime()));
        if (arg.getEndTime() != null){
            entity.setEndTime(new Date(arg.getEndTime()));
        }else {
            entity.setEndTime(oldEntity.getEndTime());
        }
        entity.setCreateUserId(arg.getFsUserId());
        entity.setLectureName(arg.getLectureUserName());
        if (CollectionUtils.isNotEmpty(arg.getTagNames())) {
            entity.setTags(gson.toJson(arg.getTagNames()));
        }
        entity.setPlatform(arg.getLivePlatform());
        entity.setOtherPlatformUrl(arg.getOtherPlatformLiveUrl());
        if (null != arg.getLivePlatform() && arg.getLivePlatform() == LivePlatformEnum.POLYV.getType()) {
            entity.setXiaoetongLiveId(arg.getXiaoetongLiveId());
            String viewUrl = polyvLiveUserH5Url + "?mkId=" + entity.getId() + "&liveId=" + arg.getXiaoetongLiveId();
            entity.setViewUrl(viewUrl);
            CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
            shortUrlArg.setUrl(viewUrl);
            Optional<String> shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
            if (shortUrlResult.isPresent()) {
                entity.setShortViewUrl(shortUrlResult.get());
            }
        }else{
            if (StringUtils.isEmpty(entity.getViewUrl())) {
                String shortViewUrl = liveUserH5Url + entity.getId();
                entity.setViewUrl(shortViewUrl);
                CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
                shortUrlArg.setUrl(shortViewUrl);
                Optional<String> shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
                if (shortUrlResult.isPresent()) {
                    entity.setShortViewUrl(shortUrlResult.get());
                }
            }
        }


        if (otherPlatformCover != null){
            entity.setCover(otherPlatformCover);
        }
        entity.setShowAcitivityList(arg.isShowActivityList());
        entity.setUpdateTime(new Date());

        return entity;
    }

    /**
     * 创建市场活动对象
     * @param vo
     * @return
     */
    private com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventObj(CreateLiveVO vo) {
        Map<String, Object> params = vo.getCreateObjectDataModel().getObjectData();
        List<String> createByList = Lists.newArrayList();
        createByList.add(String.valueOf(vo.getFsUserId()));

        params.put("begin_time", vo.getStartTime());
        params.put("end_time", vo.getEndTime());
        params.put("created_by", createByList);
        params.put("event_type", MarketingEventEnum.LIVE_MARKETING.getEventType());
        params.put("name", vo.getTitle());
        params.put("status", 1);
        if(!Strings.isNullOrEmpty(vo.getCoverTaPath())){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String npath = fileV2Manager.getNpathByApath(vo.getCoverTaPath(), vo.getEa());
                if (StringUtils.isNotBlank(npath)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", npath);
                    headImage.add(fileMap);
                    params.put("cover", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception ,ea={},apath={}",vo.getEa(),vo.getCoverTaPath(),e);
            }
        }
        params.put(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName(), vo.getParentId());

        ObjectData data = ObjectData.convert(params);
        data.setOwner(vo.getFsUserId());
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = null;
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(vo.getCorpId(), vo.getFsUserId()),  CrmObjectApiNameEnum.MARKETING_EVENT.getName(), false, arg);
        } catch (Exception e) {
            log.warn("live createMarketingEventObj failed CrmManager.createData Exception apiName:{}, e:{}", CrmObjectApiNameEnum.MARKETING_EVENT.getName(), e);
            return null;
        }
    }

    public com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> updateLiveToCrmMarketingEvent(String ea, Integer fsUserId, String marketingEventId, CreateLiveVO vo){
        ActionEditArg actionEditArg = new ActionEditArg();
        Map<String, Object> objectMap = vo.getCreateObjectDataModel().getObjectData();
        objectMap.put("object_describe_api_name", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        objectMap.put("object_describe_id", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        objectMap.put(CrmV2MarketingEventFieldEnum.ID.getFieldName(), marketingEventId);
        objectMap.put(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), vo.getTitle());
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        objectMap.put("tenant_id", tenantId);
        if(!Strings.isNullOrEmpty(vo.getCoverTaPath())){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String npath = fileV2Manager.getNpathByApath(vo.getCoverTaPath(), vo.getEa());
                if (StringUtils.isNotBlank(npath)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", npath);
                    headImage.add(fileMap);
                    objectMap.put("cover", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception ,ea={},apath={}",ea,vo.getCoverTaPath(),e);
            }
        }
        objectMap.put(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName(), vo.getStartTime());
        objectMap.put(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName(), vo.getEndTime());

        actionEditArg.setObjectData(ObjectData.convert(objectMap));
        HeaderObj systemHeader = new HeaderObj(tenantId, fsUserId);
        return metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), true, true, actionEditArg);
    }
}