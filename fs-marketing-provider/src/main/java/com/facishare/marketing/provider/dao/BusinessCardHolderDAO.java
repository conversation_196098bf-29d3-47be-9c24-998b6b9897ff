package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.BusinessCardHolderEntity;
import com.github.mybatis.pagination.Page;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2020/12/22
 **/
public interface BusinessCardHolderDAO {

    @Insert("INSERT INTO business_card_holder (\n"
        + "        \"id\",\n"
        + "        \"uid\",\n"
        + "        \"friend_uid\",\n"
        + "        \"type\",\n"
        + "        \"status\",\n"
        + "        \"create_time\",\n"
        + "        \"last_visit_time\"\n"
        + "        )VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.uid},\n"
        + "        #{obj.friendUid},\n"
        + "        #{obj.type},\n"
        + "        #{obj.status},\n"
        + "        now(),\n"
        + "        now()\n"
        + "        ) ON CONFLICT (uid, friend_uid, type) DO UPDATE SET last_visit_time = now();")
    void upsertBusinessCardHolder(@Param("obj") BusinessCardHolderEntity businessCardHolderEntity);

    @Select("<script>"
        + " SELECT A.* FROM business_card_holder AS A LEFT JOIN card AS B ON A.friend_uid = B.uid WHERE A.uid = #{uid} AND A.type = #{type}"
        + "   <if test=\"name != null\">\n"
        + "        AND B.name LIKE CONCAT('%',#{name},'%')\n"
        + "   </if>\n"
        + " ORDER BY A.last_visit_time DESC"
        + "</script>")
    List<BusinessCardHolderEntity> queryBusinessCardHolderByUid(@Param("name") String name, @Param("uid") String uid, @Param("type") Integer type, @Param("page") Page page);


}
