package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.Result.ParaInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryModuleParaArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2020/05/09
 **/
@Slf4j
@Component
public class LicenseManager {

    @Autowired
    private EIEAConverter eieaConverter;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    private final Integer SUCCESS_CODE = 0;

    public List<ProductVersionPojo> listProductVersion(String ea, String appId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(appId)) {
            return Lists.newArrayList();
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        QueryProductArg queryProductArg = new QueryProductArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(appId);
        licenseContext.setTenantId(String.valueOf(ei));
        licenseContext.setUserId(String.valueOf(CrmConstants.SYSTEM_USER));
        queryProductArg.setLicenseContext(licenseContext);
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(queryProductArg);
        if (licenseVersionResult == null || licenseVersionResult.getErrCode() != SUCCESS_CODE) {
            log.warn("LicenseManager.listProductVersion queryProductVersion error licenseVersionResult arg:{} result:{}", queryProductArg, licenseVersionResult);
            return Lists.newArrayList();
        }
        return licenseVersionResult.getResult();
    }

    public List<ModuleInfoPojo> listModule(String ea, String appId) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(appId)) {
            return Lists.newArrayList();
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        QueryModuleArg queryModuleArg = new QueryModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(appId);
        licenseContext.setTenantId(String.valueOf(ei));
        licenseContext.setUserId(String.valueOf(CrmConstants.SYSTEM_USER));
        queryModuleArg.setLicenseContext(licenseContext);
        ModuleInfoResult moduleInfoResult = licenseClient.queryModule(queryModuleArg);
        if (moduleInfoResult == null || moduleInfoResult.getErrCode() != SUCCESS_CODE) {
            log.warn("LicenseManager.listModule queryModule error result:{}", moduleInfoResult);
            return Lists.newArrayList();
        }
        return moduleInfoResult.getResult();
    }

    public List<ModuleParaPojo> queryModulePara(String ea, String appId, String moduleCode, Set<String> paraKeys) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(appId)) {
            return Lists.newArrayList();
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        QueryModuleParaArg queryModuleParaArg = new QueryModuleParaArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(appId);
        licenseContext.setTenantId(String.valueOf(ei));
        licenseContext.setUserId(String.valueOf(CrmConstants.SYSTEM_USER));
        queryModuleParaArg.setContext(licenseContext);
        queryModuleParaArg.setModuleCode(moduleCode);
        queryModuleParaArg.setParaKeys(paraKeys);
        ParaInfoResult paraInfoResult = licenseClient.queryModulePara(queryModuleParaArg);
        if (paraInfoResult == null || paraInfoResult.getErrCode() != SUCCESS_CODE) {
            log.warn("LicenseManager.listModule queryModule error result:{}", paraInfoResult);
            return Lists.newArrayList();
        }
        return paraInfoResult.getResult();
    }

}
