/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.appMenu.AppMenuTemplateTypeEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.PhotoTargetTypeEnum;
import com.facishare.mankeep.common.util.GsonUtil;
import com.facishare.marketing.api.EnterpriseIconResult;
import com.facishare.marketing.api.arg.CommitCodeAndSubmitAuditArg;
import com.facishare.marketing.api.arg.SetEnterpriseInfoArg;
import com.facishare.marketing.api.arg.distribution.OpenDistributeStatusArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.distribution.CommitClueRewardStatusResult;
import com.facishare.marketing.api.result.distribution.QueryClueAuditStatusResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.api.service.MomentPosterService;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.conference.ConferenceParamEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.distribution.ClueAuditStatusEnum;
import com.facishare.marketing.common.enums.distribution.CrmClueTypeEnum;
import com.facishare.marketing.common.enums.distribution.DistributePlanClueRewardStatusEnum;
import com.facishare.marketing.common.enums.distribution.DistributePlanTypeEnum;
import com.facishare.marketing.common.enums.distribution.DistributeStatusEnum;
import com.facishare.marketing.common.enums.live.LiveParamEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.manager.campaignBudget.CampaignBudgetObjManager;
import com.facishare.marketing.provider.manager.cardtemplate.CardTemplateManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.provider.manager.distribution.ClueManager;
import com.facishare.marketing.provider.manager.distribution.OperatorManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.lock.LockManager;
import com.facishare.marketing.provider.manager.socialMedium.SocialMediumManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WxCloudRestManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.MarketingEventRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.RoleAddUsersArg;
import com.fxiaoke.crmrestapi.arg.RoleDeleteUsersArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.service.RoleV2Service;
import com.fxiaoke.wechatrestapi.arg.*;
import com.fxiaoke.enterpriserelation2.arg.DeleteUpstreamEmployeeLinkAppRolesArg;
import com.fxiaoke.enterpriserelation2.arg.ListEmployeesByLinkAppIdAndRoleArg;
import com.fxiaoke.enterpriserelation2.arg.UpdateEmployeeLinkAppRolesArg;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.ListEmployeesByLinkAppIdAndRoleResult;
import com.fxiaoke.enterpriserelation2.service.UpstreamService;
import com.fxiaoke.wechatrestapi.arg.MiniappPrivacySettingArg;
import com.fxiaoke.wechatrestapi.common.constants.ActionEnum;
import com.fxiaoke.wechatrestapi.result.*;
import com.fxiaoke.wechatrestapi.service.miniapp.BaseInfoConfigService;
import com.fxiaoke.wechatrestapi.service.miniapp.CodeManageService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * Auther: dzb
 * Date: 2018/10/15
 * Description: 企业新用户初始化信息
 */
@Slf4j
@Component
public class SettingManager {
    private static final Integer operatorFsUserId = -10000;
    @Autowired
    @Lazy
    private InitDataManger initDataManger;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private DistributeStatusDao distributeStatusDao;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private DistributePlanDao distributePlanDao;
    @Autowired
    private OperatorManager operatorManager;
    @Autowired
    private CardTemplateManager cardTemplateManager;
    @Autowired
    private EnterprseInfoDao enterprseInfoDao;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private WxCloudRestManager wxCloudRestManager;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    //营销通预设置内容标示
    private static final String MARKETING_NAME = "MARKETING_INIT_";
    @Value("${expireTime}")
    private String expireTime;
    @Value("${default.enterprise.icon.path}")
    private String defaultEnterpriseIconPath;
    @Value("${picture.fsEa}")
    private String mankeepDefaultEa;
    @Value("${home.domain}")
    private String homeDomain;
    @Autowired
    private ClueManager clueManager;
    @Autowired
    private ObjectFieldMappingDAO objectFieldMappingDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private MomentPosterService momentPosterService;
    @Autowired
    private MarketingEventRemoteManager marketingEventRemoteManager;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private UserTagManager userTagManager;
    @Autowired
    private OfficialWebsiteManager officialWebsiteManager;
    @Autowired
    private OtherObjectDescribeManager otherObjectDescribeManager;
    @Autowired
    private MarketingBehaviorObjDescribeManager marketingBehaviorObjDescribeManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private SpreadChannelManager spreadChannelManager;
    @Autowired
    private CrmDataAuthManager crmDataAuthManager;
    @Autowired
    private RoleV2Service roleV2Service;
    @Autowired
    private BaseInfoConfigService baseInfoConfigService;
    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private CodeManageService codeManageService;
    @Autowired
    private MiniappReleaseRecordDao miniappReleaseRecordDao;
    @ReloadableProperty("marketing_appid")
    private String marketingAppId;
    @Autowired
    private ResourceManager resourceManager;

    @Autowired
    private UpstreamService upstreamService;
    @Value("${partner.appid}")
    private String partnerAppId;

    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Autowired
    private UserBehaviorRecordObjManager userBehaviorRecordObjManager;
    @Autowired
    private MarketingContentObjManager marketingContentObjManager;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private CampaignBudgetObjManager campaignBudgetObjManager;

    @Autowired
    private SocialMediumManager socialMediumManager;

    private final String PAAS_MEMBER_MANAGER_ROLE_CODE = "00000000000000000000000000000028";

    @FilterLog
    public Integer getMarketingStatus(String ea) {
        EnterpriseMetaConfigEntity entity = enterpriseMetaConfigDao.getByEa(ea);
        if (entity == null) {
            String version = appVersionManager.getCurrentAppVersion(ea);
            if (version != null) {
                settingManager.initMarketingData(ea, -10000, version);
                return MarketingStatusEnum.OPEN.getType();
            }
            return MarketingStatusEnum.CLOSE.getType();
        }
        return MarketingStatusEnum.OPEN.getType();
    }

    public Result<Void> initMarketingData(String ea, Integer fsUserId, String moduleCode) {
        if (StringUtils.isBlank(moduleCode)) {
            return new Result<>();
        }
        if (StringUtils.isBlank(ea)) {
            log.info("initMarketingData failed ea is null");
            return new Result<>(SHErrorCode.SUCCESS);
        }
        String lockKey = ea + ":initMarketingData";
        boolean isLock = lockManager.retryGetLock(lockKey, 3, Long.parseLong(expireTime), 5000);
        if(!isLock) {
            log.error("initMarketingData failed as retryGetLock failure,  ea: {}", ea);
            return new Result<>(SHErrorCode.SUCCESS);
        }
        campaignBudgetObjManager.assignRecordByEa(ea);
        socialMediumManager.assignRecordByEa(ea);
        try {
            for (AppMenuTemplateTypeEnum value : AppMenuTemplateTypeEnum.values()) {
                appMenuTemplateService.createSystemTemplate(ea,value.getType());
            }
        } catch (Exception e) {
            log.error("create app menu system template failed, ea: {}", ea, e);
        }
        try {
            userRoleManager.initSystemAdminUserRoles(ea);
        } catch (Exception e) {
            log.warn("Exception at init system admin user role");
        }

        if (!VersionEnum.isDingDingVersion(moduleCode)) {
            eaWechatAccountBindDao.insert(ea, MKThirdPlatformConstants.PLATFORM_ID, WxAppInfoEnum.getDefaultApp().getAppId());
        }

        try {
            boardManager.initDefaultEnterpriseBoard(ea);
        } catch (Exception e) {
            log.warn("Exception at initDefaultEnterpriseBoard, ea:{}", ea, e);
        }
        try {
            marketingEventRemoteManager.addDefaultEventTypeOptionsToFieldDescribe(eieaConverter.enterpriseAccountToId(ea), -10000);
        } catch (Exception e) {
            log.error("Error ar add event_type field options to field describe, ea:{}", ea, e);
        }

        try {
            initDataManger.initUserMarketingAccount(ea, fsUserId);
        } catch (Exception e) {
            log.warn("Error at init initUserMarketingAccount behavior obj, ea:{}", ea);
        }

        try {
            marketingBehaviorObjDescribeManager.getOrCreateMarketingBehaviorObjDescribe(ea);
            mktContentMgmtLogObjManager.getOrCreateObjDescribe(ea);
            marketingPromotionSourceObjManager.createObjDescAndAddObjField(ea);
            marketingLeadSyncRecordObjManager.getOrCreateObjDescribe(ea);
            userBehaviorRecordObjManager.getOrCreateObjDescribe(ea);
        } catch (Exception e) {
            log.warn("Error at init marketing behavior obj, ea:{}", ea);
        }
        if (StringUtils.equals(moduleCode, VersionEnum.PRO.getVersion()) || StringUtils.equals(moduleCode, VersionEnum.STREN.getVersion())) {
            initDataManger.initMarketingProcess(ea, fsUserId);
            initDataManger.initMarketingProcessLatencyResult(ea, fsUserId);
        }

        userTagManager.initEnterpriseTagModel(ea);
        /*
         * 1.经线上验证，发现初始化文章方法超时，由于有图片的处理,改为异步执行。
         * 2.异步方法不能再同一个方法中， 不能@Async与生命周期回调一起使用,必须单独初始化异步方法
         * **/
        initDataManger.initDataAsyncArticle(ea, fsUserId);
        initDataManger.initDataAsyncPrdouct(ea, fsUserId);
        //初始化营销活动
        marketingActivityCrmManager.initMarketingActivity(ea);
        marketingActivityRemoteManager.addSpreadTypeWxSpreadOption(eieaConverter.enterpriseAccountToId(ea), -10000);
        marketingActivityRemoteManager.addCancelStatusOption(eieaConverter.enterpriseAccountToId(ea), -10000);
        marketingActivityRemoteManager.addMarketingEventLookRoler(eieaConverter.enterpriseAccountToId(ea), -10000);
        crmDataAuthManager.updateEntityOpenness(ea, -10000, "MarketingActivityObj", "2", "2");
        //本地缓存存企业
        redisManager.set(MARKETING_NAME + ea, ea);
        //企业配置信息新增
        enterpriseMetaConfigDao.insertIgnore(UUIDUtil.getUUID(), ea);
        redisManager.setEnterpriseStatus(ea,  StringPool.ONE);
        //初始化朋友圈海报企业素材库
        momentPosterService.initExample(ea);
        //开通企业微信时增加企业微信推广渠道
        List<SpreadChannelManager.SpreadChannleOption> spreadChannleOptionList = Lists.newArrayList();
        SpreadChannelManager.SpreadChannleOption spreadChannleOption = new SpreadChannelManager.SpreadChannleOption();
        spreadChannleOption.setLabel("企业微信");
        spreadChannleOption.setValue("qywx");
        spreadChannleOptionList.add(spreadChannleOption);
        spreadChannelManager.addChannel(ea, -10000, spreadChannleOptionList, false);

            /*
            if (!EnvironmentUtil.isOuterCloud(homeDomain)){
                //初始化营销顾问
                marketingSettingBizService.init(ea);
            }
            */

        // 线索刷入营销活动
        officialWebsiteManager.appendLeadMarketingActivity(ea);
        // 线索刷入推广人
        officialWebsiteManager.appendLeadMarketingSpreadUser(ea);
        // 线索刷入伙伴信息
        officialWebsiteManager.appendLeadMarketingPartner(ea);
        //线索刷入访客id和微信用户id
        otherObjectDescribeManager.tryUpdateCustomLeadsFieldLabel(ea);
        //市场活动新增预设【封面】字段
        otherObjectDescribeManager.tryAddCoverAndPageFileToMarketingEventObj(ea);
        // 活动成员增加字段
        campaignMergeDataManager.addCampaignMembersObjField(ea);
        // 活动成员增加直播数据
        campaignMergeDataManager.appendCampaignMembersLiveData(ea);
        // 活动成员增加推广人字段
        campaignMergeDataManager.appendCampaignMembersSpreadData(ea);
        //活动成员增加数据存入状态字段
        campaignMergeDataManager.appendCampaignMemberDataSaveStatus(ea);
        // 营销活动对象增加状态选项
        officialWebsiteManager.updateMarketingObjStatus(ea);
        // 营销内容关联字段
        try {
            marketingContentObjManager.initOtherObj(ea);
        } catch (Exception e) {
            log.warn("Exception at initOtherObj, ea:{}", ea, e);
        }

//        otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
//        otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
//        otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
//        otherObjectDescribeManager.tryUpdateListLayout(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName());

        //开启推广渠道
        spreadChannelManager.enableMarketingPromotionChannel(ea);

        //初始化相关角色&权限
        try {
            userRoleManager.initCrmFunctionPrivilege(ea);
        }catch (Exception e){
            log.warn("Error at init initCrmFunctionPrivilege behavior obj, ea:{}", ea);
        }
        // 初始化会议/直播微页面模板
        try {
            marketingEventCommonSettingService.getSceneHexagonTemplates(ea);
        } catch (Exception e) {
            log.warn("Exception at getSceneHexagonTemplates, ea:{}", ea, e);
        }
        // 初始化默认名片模板
        try {
            cardTemplateManager.refreshDefaultTemplate(ea);
        } catch (Exception e) {
            log.warn("cardTemplateManager.refreshDefaultTemplate error, ea:{}", ea);
        }
        redisManager.unLock(MARKETING_NAME + ea);

        return new Result<>(SHErrorCode.SUCCESS);
    }

    public Result<Void> brushLibrary(String randomNum) {
        if (!randomNum.equals("fxiaoke14")) {
            return new Result<>(SHErrorCode.SUCCESS);
        }
        //查产品
        List<String> listProduct = enterpriseMetaConfigDao.findProductEaAll();
        if (!listProduct.isEmpty()) {
            listProduct.forEach(value -> initDataManger.initDataAsyncPrdouct(value, null));
        }

        //查文章
        List<String> listArticle = enterpriseMetaConfigDao.findArticleEaAll();
        if (!listArticle.isEmpty()) {
            listArticle.forEach(value -> initDataManger.initDataAsyncArticle(value, null));
        }

        return new Result<>(SHErrorCode.SUCCESS);
    }

    public Result<SocialDistributionDetailResult> getSocialDistributeTableDetail(String ea, String planId) {
        if (StringUtils.isBlank(planId)) {
            log.warn("SettingManager.getSocialDistributeTableDetail planId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        DistributeStatusEntity distributeStatus = distributeStatusDao.getDistributeStatusByFsEa(ea);
        if (null == distributeStatus) {
            return new Result<>(SHErrorCode.NO_DATA);
        }

        DistributePlanEntity distributePlanEntity = distributePlanDao.getDistributePlanByPlanId(planId);

        if (distributePlanEntity == null) {
            return new Result<>(SHErrorCode.NO_DATA);
        }

        List<Photos2ApathResult> photos2ApathResults = new ArrayList<>();
        List<PhotoEntity> photoEntities = photoManager.listByTargeId(distributePlanEntity.getId(),ea);
        if (CollectionUtils.isEmpty(photoEntities)) {
            log.warn(" SettingManager.getSocialDistributeTableDetail one of distribute photos is null --> distributePlanEntity:{}", distributePlanEntity);
        }

        for (PhotoEntity photoEntity : photoEntities) {
            Photos2ApathResult photos2ApathResult = new Photos2ApathResult(photoEntity.getId(), photoEntity.getUrl(), photoEntity.getThumbnailUrl());
            photos2ApathResults.add(photos2ApathResult);
        }
        HashMap<String, String> extraFieldMap = GsonUtil.fromJson(distributePlanEntity.getExtraField(), HashMap.class);
        if (null == extraFieldMap) {
            return new Result<>(SHErrorCode.NO_DATA);
        }
        String targetObjectApiName = CrmObjectApiNameEnum.CRM_LEAD.getName();
        if (distributePlanEntity.getCrmClueType() != null && distributePlanEntity.getCrmClueType().equals(CrmClueTypeEnum.SDR_CLUE.getType())) {
            targetObjectApiName = CrmObjectApiNameEnum.DISTRIBUTION_CLUE.getName();
        }
        // CRM设置项
        ObjectFieldMappingEntity objectFieldMappingEntity = objectFieldMappingDAO
            .getBySourceObjectApiNameAndTargetObjectApiName(ea, ObjectFieldMappingManager.DISTRIBUTION_CLUE + distributePlanEntity.getId(), targetObjectApiName);
        if (objectFieldMappingEntity == null) {
            log.error("SettingManager.getSocialDistributeTableDetail objectFieldMappingEntity is null distributePlanEntity:{}", distributePlanEntity);
            return new Result<>(SHErrorCode.OBJECT_FIELD_MAPPING_ONT_FOUND);
        }
        List<FieldMappingResult> fieldMappingResults = BeanUtil.copy(objectFieldMappingEntity.getFieldMappingsV2(), FieldMappingResult.class);
        SocialDistributionDetailResult socialDistributionDetailResult = new SocialDistributionDetailResult(distributePlanEntity.getId(), distributePlanEntity.getPlanTitle(),
            photos2ApathResults, extraFieldMap.get("BUTTON"), objectFieldMappingEntity.getTargetRecordType(), fieldMappingResults, distributePlanEntity.getCrmLeadPoolId());
        if (CrmClueTypeEnum.SDR_CLUE.getType().equals(distributePlanEntity.getCrmClueType())) {
            socialDistributionDetailResult.setDistributePlanType(DistributePlanTypeEnum.SDR.getType());
        } else if (StringUtils.isNotBlank(distributePlanEntity.getCrmLeadPoolId())){
            socialDistributionDetailResult.setDistributePlanType(DistributePlanTypeEnum.LEADS_POOL.getType());
        } else {
            socialDistributionDetailResult.setDistributePlanType(DistributePlanTypeEnum.LEADS.getType());
        }
        return new Result<>(SHErrorCode.SUCCESS, socialDistributionDetailResult);
    }

    public Result<Void> openDistributeStatus(OpenDistributeStatusArg arg) {
        if (StringUtils.isBlank(arg.getEa())) {
            log.warn("SettingManager.openDistributeStatus don't exist ea-> arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        DistributeStatusEntity distributeStatusEntity = distributeStatusDao.getDistributeStatusByFsEa(arg.getEa());
        if (null != distributeStatusEntity) {
            if (distributeStatusEntity.getStatus() == DistributeStatusEnum.CLOSE.getType()) {
                distributeStatusDao.updatetDistributeStatus(arg.getEa(), arg.getFsUid(), DistributeStatusEnum.OPEN.getType());
            }
        } else {
            distributeStatusDao.insertDistributeStatus(UUIDUtil.getUUID(), arg.getEa(), arg.getFsUid(), DistributeStatusEnum.OPEN.getType(), ClueAuditStatusEnum.CLOSE.getStatus());
        }
        return setDistributePlanInfo(arg, true);
    }

    public Result<Void> updateDistributePlan(OpenDistributeStatusArg arg) {
        if (StringUtils.isBlank(arg.getEa())) {
            log.warn("SettingManager.updateDistributePlan don't exist ea-> arg:{}", arg);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        return setDistributePlanInfo(arg, false);
    }

    public Result<Void> setDistributePlanInfo(OpenDistributeStatusArg arg, boolean isNewPlan) {
        if (StringUtils.isBlank(arg.getEa())) {
            log.warn("SettingManager.resetDistributeStatusInfo don't exist ea-> arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (null == arg.getPlanId() && !isNewPlan) {
            return new Result<>(SHErrorCode.OPERATOR_GET_DISTRIBUTE_PLAN_FAILED);
        }
        if (arg.getPhotoForTaPathAndPhotoUrl().size() > 9) {
            return new Result<>(SHErrorCode.MORE_THAN_NINE_PHOTOS);
        }
        Integer status = distributeStatusDao.getDistributeStatusOfStatusByFsEa(arg.getEa());
        if (!isNewPlan) {
            if (null == status) {
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            if (status == DistributeStatusEnum.CLOSE.getType()) {
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
        }
        HashMap<String, String> extraFieldMap = new HashMap<>();
        extraFieldMap.put("BUTTON", arg.getButtonName());
        String extraFieldString = GsonUtil.toJson(extraFieldMap);
        String id = UUIDUtil.getUUID();
        // 关闭状态下修改信息
        DistributePlanEntity distributePlanEntity = distributePlanDao.getDistributePlanByPlanId(arg.getPlanId());
        if (null == distributePlanEntity) {
            int crmClueType = CrmClueTypeEnum.LEADS_OBJ.getType();
            if (clueManager.isSdrEa(arg.getEa())) {
                crmClueType = CrmClueTypeEnum.SDR_CLUE.getType();
                arg.setCrmLeadPoolId(null);
                arg.setCrmLeadPoolName(null);
            }
            distributePlanDao.insertDistributePlan(id, arg.getEa(), arg.getFsUid(), arg.getPlanTitle(), arg.getCrmLeadPoolId(), extraFieldString, arg.getPlanDesc(), arg.getCrmLeadPoolName(), crmClueType);
            fileManager.addPhotos(arg.getEa(), arg.getPhotoForTaPathAndPhotoUrl(), PhotoTargetTypeEnum.DISTRIBUTION_PLAN_DETAIL.getType(), id);
            //应用管理员自动加入到运营人员
            List<Integer> userIds = Lists.newArrayList();
            userIds.add(arg.getFsUid());
            Result<Void> addOperatorResult = operatorManager.addOperatorByFsUserIds(arg.getEa(), arg.getFsUid(), userIds, id);
            if (addOperatorResult == null || !addOperatorResult.isSuccess()) {
                log.info("SettingManager setDistributePlanInfo add admin to operator failed  arg:{}", arg);
            }
        } else {
            id = distributePlanEntity.getId();
            distributePlanDao.updateDistributePlan(arg.getEa(), arg.getFsUid(), id, arg.getPlanTitle(), arg.getCrmLeadPoolId(), extraFieldString, arg.getPlanDesc(), arg.getCrmLeadPoolName());
            fileManager.handleCardPhoto(arg.getPhotoForTaPathAndPhotoUrl(), PicOperationFlagEnum.OPERATION_PIC.getType(), id, false,arg.getEa());
        }

        // 线索表单保存入CRM
        String objectApiName = CrmObjectApiNameEnum.CRM_LEAD.getName();
        if (clueManager.isSdrEa(arg.getEa())) {
            objectApiName = CrmObjectApiNameEnum.DISTRIBUTION_CLUE.getName();
        }
        Boolean saveCrmResult = clueManager.buildClueFieldMapping(id, arg.getEa(), arg.getLeadsTargetObjectRecordType(), arg.getLeadsFieldMappings(), objectApiName);
        if (!saveCrmResult) {
            log.warn("SettingManager setDistributePlanInfo error");
        }
        return new Result<>(SHErrorCode.SUCCESS);
    }

    public Result<DistributeStatusResult> judgeDistributeStatus(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("SettingManager.judgeDistributeStatus don't exist ea");
            return new Result<>(SHErrorCode.SUCCESS, new DistributeStatusResult(DistributeStatusEnum.CLOSE.getType()));
        }
        Integer status = distributeStatusDao.getDistributeStatusOfStatusByFsEa(ea);
        if (null != status) {
            if (status == DistributeStatusEnum.OPEN.getType()) {
                return new Result<>(SHErrorCode.SUCCESS, new DistributeStatusResult(DistributeStatusEnum.OPEN.getType()));
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, new DistributeStatusResult(DistributeStatusEnum.CLOSE.getType()));
    }

    public Result<Void> setEnterpriseInfo(SetEnterpriseInfoArg arg) {
        EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(arg.getEa());
        String apath = null;
        String thumbnailApath = null;
        if (StringUtils.isNotBlank(arg.getIconPath()) ) {
            apath = arg.getIconPath();
            thumbnailApath = arg.getIconPath();
        }
        boolean result;
        if (enterpriseInfoEntity == null) {
            if(StringUtils.isBlank(arg.getFullName()) || StringUtils.isBlank(apath)){
                log.info("setEnterpriseInfo failed fullName or apath is blank, arg:{}", arg);
                return new Result<>(SHErrorCode.ADD_FAIL);
            }
            result = newEnterpriseInfo(arg.getEa(), arg.getFullName(), arg.getShortName(), apath, thumbnailApath, arg.getUserId(), arg.getDrawQrcodeElogo(),arg.getIconAcrossPath());
            return result ? Result.newSuccess() : new Result<>(SHErrorCode.ADD_FAIL);
        } else {
            String oldIconPath = enterpriseInfoEntity.getIconPath();
            result = enterprseInfoDao.updateEnterprseInfoByEa(arg.getEa(), arg.getFullName(), arg.getShortName(), apath, thumbnailApath, arg.getUserId(), arg.getDrawQrcodeElogo(),arg.getIconAcrossPath());
            if (result && StringUtils.isNotBlank(apath) && !enterpriseInfoEntity.getIconPath().equals(defaultEnterpriseIconPath) && !StringUtils.equals(oldIconPath, apath)) {
                // 更新图片的时候，如果旧图不是默认图，则删除
                fileV2Manager.deleteFilesByApath(Lists.newArrayList(oldIconPath));
            }
            return result ? Result.newSuccess() : new Result<>(SHErrorCode.UPDATE_FAIL);
        }
    }

    private boolean newEnterpriseInfo(String ea, String fullName, String shortName, String apath, String thumbnailApath, Integer userId, Integer drawQrcodeElogo,String iconAcrossPath) {
        if (StringUtils.isBlank(shortName)) {
            shortName = fullName;
        }
        if (StringUtils.isBlank(thumbnailApath)) {
            thumbnailApath = apath;
        }
        Date current = new Date();
        EnterpriseInfoEntity enterpriseInfoEntity = new EnterpriseInfoEntity();
        enterpriseInfoEntity.setId(UUIDUtil.getUUID());
        enterpriseInfoEntity.setEa(ea);
        enterpriseInfoEntity.setCreateTime(current);
        enterpriseInfoEntity.setUpdateTime(current);
        enterpriseInfoEntity.setIconPath(apath);
        enterpriseInfoEntity.setIconAcrossPath(iconAcrossPath);
        enterpriseInfoEntity.setThumbnailPath(thumbnailApath);
        enterpriseInfoEntity.setFullName(fullName);
        enterpriseInfoEntity.setShortName(shortName);
        enterpriseInfoEntity.setUserId(userId);
        enterpriseInfoEntity.setDrawQrcodeElogo(drawQrcodeElogo);
        try {
            return enterprseInfoDao.insertEnterpriseInfo(enterpriseInfoEntity);
        } catch (Exception e) {
            log.warn("newEnterpriseInfo insertEnterpriseInfo fail, enterpriseInfoEntity:{}", enterpriseInfoEntity);
            return false;
        }
    }

    public Result<EnterpriseInfoResult> queryEnterpriseInfo(String ea, Integer userId) {
        EnterpriseInfoResult result = new EnterpriseInfoResult();
        EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(ea);
        if (enterpriseInfoEntity == null) {
            //第一次查看，从纷享获取企业信息
            int ei = eieaConverter.enterpriseAccountToId(ea);

            GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
            enterpriseDataArg.setEnterpriseAccount(ea);
            enterpriseDataArg.setEnterpriseId(ei);
            GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
            if (enterpriseDataResult == null || enterpriseDataResult.getEnterpriseData() == null) {
                log.info("queryEnterpriseInfo failed getEnterpriseData failed ea:{}", ea);
                return Result.newError(SHErrorCode.QUERY_ENTERPRSE_INFO_FAILED);
            }
            String fullName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
            String shortName = enterpriseDataResult.getEnterpriseData().getEnterpriseName(); // 初始化默认使用全称作为简称
            newEnterpriseInfo(ea, fullName, shortName, defaultEnterpriseIconPath, defaultEnterpriseIconPath, userId, 1,resourceManager.getResouceApath(ResourceApathEnum.ACROSS_LOGO));

            String url = fileManager.getPictureShareUrl(mankeepDefaultEa, defaultEnterpriseIconPath, false);
            String iconAcrossUrl = fileManager.getPictureShareUrl(mankeepDefaultEa, resourceManager.getResouceApath(ResourceApathEnum.ACROSS_LOGO), false);
            result.setIconUrl(url);
            result.setThumbnailUrl(url);
            result.setIconAcrossUrl(iconAcrossUrl);
            result.setFullName(fullName);
            result.setShortName(shortName);

            return Result.newSuccess(result);
        } else {
            result.setId(enterpriseInfoEntity.getId());
            result.setFullName(enterpriseInfoEntity.getFullName());
            result.setShortName(enterpriseInfoEntity.getShortName());
            String url = fileManager.getPictureShareUrl(ea, enterpriseInfoEntity.getIconPath(), false);
            String thumbnailUrl = fileManager.getPictureShareUrl(ea, enterpriseInfoEntity.getThumbnailPath(), false);
            if (StringUtils.isBlank(enterpriseInfoEntity.getIconAcrossPath())) {
                enterpriseInfoEntity.setIconAcrossPath(resourceManager.getResouceApath(ResourceApathEnum.ACROSS_LOGO));
            }
            String iconAcrossUrl = fileManager.getPictureShareUrl(ea, enterpriseInfoEntity.getIconAcrossPath(), false);
            result.setIconUrl(url);
            result.setThumbnailUrl(thumbnailUrl);
            result.setIconAcrossUrl(iconAcrossUrl);
            result.setEa(enterpriseInfoEntity.getEa());
            result.setUserId(enterpriseInfoEntity.getUserId());
            result.setCreateTime(enterpriseInfoEntity.getCreateTime().getTime());
            result.setUpdateTime(enterpriseInfoEntity.getUpdateTime().getTime());
            result.setDrawQrcodeElogo(enterpriseInfoEntity.getDrawQrcodeElogo());
            return Result.newSuccess(result);
        }
    }

    public Result<EnterpriseIconResult> getEnterpriseIconAndName(String ea) {
        EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(ea);
        if (enterpriseInfoEntity == null) {
            return Result.newError(SHErrorCode.QUERY_ENTERPRSE_INFO_FAILED);
        }

        EnterpriseIconResult result = new EnterpriseIconResult();
        String url = fileManager.getPictureShareUrl(ea, enterpriseInfoEntity.getIconPath(), false);
        result.setIcon(url);
        result.setName(enterpriseInfoEntity.getFullName());

        return Result.newSuccess(result);
    }

    public Result<Void> setClueAuditStatus(String ea, Integer userId, Integer status){
        if (!ClueAuditStatusEnum.isCorrectStatus(status)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        DistributeStatusEntity distributeStatusEntity = distributeStatusDao.getDistributeStatusByFsEa(ea);
        if (distributeStatusEntity == null) {
            distributeStatusDao.insertDistributeStatus(UUIDUtil.getUUID(), ea, userId, DistributeStatusEnum.OPEN.getType(), status);
        } else {
            distributeStatusDao.updateClueAuditStatus(ea, status);
        }
        return Result.newSuccess();
    }

    public Result<QueryClueAuditStatusResult> queryClueAuditStatus(String ea, Integer userId) {
        QueryClueAuditStatusResult statusResult = new QueryClueAuditStatusResult();
        statusResult.setClueAuditStatus(ClueAuditStatusEnum.CLOSE.getStatus());
        DistributeStatusEntity statusEntity = distributeStatusDao.getDistributeStatusByFsEa(ea);
        if (statusEntity != null) {
            statusResult.setClueAuditStatus(statusEntity.getClueAuditStatus());
        }
        return Result.newSuccess(statusResult);
    }

    public Result<Void> setCommitClueRewardStatus(String planId, Integer status, Double clueReward){
        if (!DistributePlanClueRewardStatusEnum.isCorrectStatus(status)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (DistributePlanClueRewardStatusEnum.OPEN.getStatus().equals(status) && clueReward < 0) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        DistributePlanEntity distributePlanEntity = distributePlanDao.getDistributePlanByPlanId(planId);
        if (distributePlanEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        distributePlanDao.updateDistributePlanClueRewardStatus(planId, status, clueReward);
        return Result.newSuccess();
    }

    public Result<CommitClueRewardStatusResult> getCommitClueRewardStatus(String planId) {
        CommitClueRewardStatusResult statusResult = new CommitClueRewardStatusResult();
        statusResult.setPlanId(planId);
        statusResult.setClueRewardStatus(DistributePlanClueRewardStatusEnum.CLOSE.getStatus());
        statusResult.setValidClueReward(0f);
        DistributePlanEntity distributePlanEntity = distributePlanDao.getDistributePlanByPlanId(planId);
        if (distributePlanEntity != null) {
            statusResult.setClueRewardStatus(distributePlanEntity.getClueRewardStatus());
            statusResult.setValidClueReward(distributePlanEntity.getValidClueReward() == null ? 0f : distributePlanEntity.getValidClueReward());
        }
        return Result.newSuccess(statusResult);
    }

    public void addUsersToPaasMemberManager(String ea, Collection<Integer> fsUserIds){
        int ei = eieaConverter.enterpriseAccountToId(ea);
        RoleAddUsersArg roleAddUsersArg = new RoleAddUsersArg();
        roleAddUsersArg.setRoleCode(PAAS_MEMBER_MANAGER_ROLE_CODE);
        Set<String> toSetFsUserIds = fsUserIds.stream().map(x -> x + "").collect(Collectors.toSet());
        roleAddUsersArg.setUserIds(new ArrayList<>(toSetFsUserIds));
        roleV2Service.addUsers(new HeaderObj(ei, -10000), roleAddUsersArg);
    }

    public void removeUsersFromPaasMemberManager(String ea, Collection<Integer> fsUserIds){
        int ei = eieaConverter.enterpriseAccountToId(ea);
        RoleDeleteUsersArg roleDeleteUsersArg = new RoleDeleteUsersArg();
        roleDeleteUsersArg.setRoleCode(PAAS_MEMBER_MANAGER_ROLE_CODE);
        Set<String> toSetFsUserIds = fsUserIds.stream().map(x -> x + "").collect(Collectors.toSet());
        roleDeleteUsersArg.setUserIds(new ArrayList<>(toSetFsUserIds));
        roleV2Service.deleteUsers(new HeaderObj(ei, -10000), roleDeleteUsersArg);
    }

    public void addUsersToPaasManager(String ea, Collection<Integer> fsUserIds,String roleName){
        int ei = eieaConverter.enterpriseAccountToId(ea);
        RoleAddUsersArg roleAddUsersArg = new RoleAddUsersArg();
        roleAddUsersArg.setRoleCode(roleName);
        Set<String> toSetFsUserIds = fsUserIds.stream().map(x -> x + "").collect(Collectors.toSet());
        roleAddUsersArg.setUserIds(new ArrayList<>(toSetFsUserIds));
        roleV2Service.addUsers(new HeaderObj(ei, -10000), roleAddUsersArg);
    }

    public void removeUsersFromPaasManager(String ea, Collection<Integer> fsUserIds,String roleName){
        int ei = eieaConverter.enterpriseAccountToId(ea);
        RoleDeleteUsersArg roleDeleteUsersArg = new RoleDeleteUsersArg();
        roleDeleteUsersArg.setRoleCode(roleName);
        Set<String> toSetFsUserIds = fsUserIds.stream().map(x -> x + "").collect(Collectors.toSet());
        roleDeleteUsersArg.setUserIds(new ArrayList<>(toSetFsUserIds));
        roleV2Service.deleteUsers(new HeaderObj(ei, -10000), roleDeleteUsersArg);
    }

    public ModifyDomainResult modifyMiniAppDomain(WxThirdPlatformDomainResult platformDomain, String accessToken) {
        ModifyDomainArg modifyDomainArg = new ModifyDomainArg();
        modifyDomainArg.setAction(ActionEnum.SET.getAction());
        modifyDomainArg.setDownloadDomain(strToList(platformDomain.getDownloadDomain()));
        modifyDomainArg.setRequestDomain(strToList(platformDomain.getRequestDomain()));
        modifyDomainArg.setUploadDomain(strToList(platformDomain.getUploadDomain()));
        modifyDomainArg.setWsRequestDomain(strToList(platformDomain.getWsRequestDomain()));
        return baseInfoConfigService.modifyDomain(accessToken, modifyDomainArg);
    }

    public SetWebViewDomainResult setWebviewDomain(String webViewDomain, String accessToken) {
        SetWebViewDomainArg setWebViewDomainArg = new SetWebViewDomainArg();
        setWebViewDomainArg.setAction(ActionEnum.SET.getAction());
        setWebViewDomainArg.setWebViewDomain(strToList(webViewDomain));
        return baseInfoConfigService.setWebviewDomain(accessToken, setWebViewDomainArg);
    }

    public Optional<WechatBaseResult> setPrivacySetting(String ea, Integer userId, String accessToken) {
        String mobile = null;
        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEmployeeId(userId);
        employeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        GetEmployeeDtoResult employeeDtoResult = employeeProviderService.getEmployeeDto(employeeDtoArg);
        if (employeeDtoResult != null && employeeDtoResult.getEmployeeDto() != null) {
            mobile = employeeDtoResult.getEmployeeDto().getMobile();
        }
        if (StringUtils.isBlank(mobile)) {
            //去取应用管理员
            BaseResult<List<Integer>> br = openAppAdminService.getAppAdminIds(ea, marketingAppId);
            if (br.isSuccess() && CollectionUtils.isNotEmpty(br.getResult())){
               FsAddressBookManager.FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(ea, br.getResult().get(0));
                mobile = fsEmployeeMsg == null ? null : fsEmployeeMsg.getMobile();
            }
        }
        if (StringUtils.isBlank(mobile)){
            mobile = "***********";
        }

        MiniappPrivacySettingArg arg = new MiniappPrivacySettingArg();
        arg.setPrivacyVer(2);  //1表示现网版本；2表示开发版
        MiniappPrivacySettingArg.OwnerSetting ownerSetting = new MiniappPrivacySettingArg.OwnerSetting();
        ownerSetting.setNoticeMethod(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_788));
        ownerSetting.setContactPhone(mobile);
        arg.setOwnerSetting(ownerSetting);
        List<MiniappPrivacySettingArg.SettingList> settingList = Lists.newArrayList();
        settingList.add(new MiniappPrivacySettingArg.SettingList("Album", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_792)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("AlbumWriteOnly", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_793)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("Contact", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_794)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("Location", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_795)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("MessageFile", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_796)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("PhoneNumber", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_797)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("UserInfo", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_798)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("Camera", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_799)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("Record", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_800)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("Clipboard", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_801)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("Camera", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_802)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("BlueTooth", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_803)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("CalendarWriteOnly", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_804)));
        settingList.add(new MiniappPrivacySettingArg.SettingList("RunData", I18nUtil.get(I18nKeyEnum.MARK_MANAGER_SETTINGMANAGER_805)));
        arg.setSettingList(settingList);
        WechatBaseResult result = baseInfoConfigService.setPrivacySetting(accessToken, arg);
        log.info("setPrivacySetting ea:{} userId:{} arg:{} result:{}", ea, userId, arg, result);
        return Optional.of(result);
    }

    public void removeAdminRoleForPartnerApp(List<Integer> employeeIdList, String ea) {
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
        headerObj.setAppId(partnerAppId);
        DeleteUpstreamEmployeeLinkAppRolesArg deleteArg = new DeleteUpstreamEmployeeLinkAppRolesArg();
        deleteArg.setFsUserIds(employeeIdList);
        deleteArg.setRole(1);
        deleteArg.setLinkAppIds(Arrays.asList(partnerAppId));
        deleteArg.setUpstreamEa(ea);
        upstreamService.deleteUpstreamEmployeeLinkAppRoles(headerObj, deleteArg);
    }

    public void addAdminRoleForPartnerApp(List<Integer> employeeId, String ea) {
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea));
        headerObj.setAppId(partnerAppId);
        ListEmployeesByLinkAppIdAndRoleArg listArg = new ListEmployeesByLinkAppIdAndRoleArg();
        listArg.setFsUserId(-10000);
        listArg.setLinkAppId(partnerAppId);
        listArg.setRole(1);
        listArg.setUpstreamEa(ea);
        RestResult<List<ListEmployeesByLinkAppIdAndRoleResult>> listRestResult = upstreamService.listEmployeesByLinkAppIdAndRole(headerObj, listArg);
        if(listRestResult.isSuccess()){
            List<ListEmployeesByLinkAppIdAndRoleResult> data = listRestResult.getData();
            for (ListEmployeesByLinkAppIdAndRoleResult datum : data) {
                employeeId.add(datum.getFsUserId());
            }
        }
        List<String> appIds = new ArrayList<>();
        appIds.add(partnerAppId);
        UpdateEmployeeLinkAppRolesArg arg = new UpdateEmployeeLinkAppRolesArg();
        arg.setFsUserIds(employeeId);
        arg.setRole(1);
        arg.setLinkAppIds(appIds);
        arg.setUpstreamEa(ea);
        upstreamService.updateEmployeeLinkAppRoles(headerObj, arg);
    }

    /**
     * 延后提交审核
     * @param arg
     * @param accessToken
     * @param wxAppId
     * @return
     */
    public void submitAuditDelay(CommitCodeAndSubmitAuditArg arg, String accessToken, String wxAppId, boolean privacyApiNotUse) throws InterruptedException {
        int maxRetry = 5;
        try {
            while (maxRetry-- > 0) {
                log.info("submitAuditDelay wait for commiting");
                Thread.sleep(1000 * 60 * 3);
                //查询审核状态
                accessToken = wechatAccountManager.getAccessTokenByWxAppId(wxAppId);   //防止延时后，token失效
                GetCodePrivacyInfoResult getCodePrivacyInfoResult = codeManageService.getCodePrivacyInfo(accessToken);
                log.info("submitAuditDelay getCodePrivacyInfoResult wxAppId::{} getCodePrivacyInfoResult:{}", wxAppId, getCodePrivacyInfoResult);
                if (getCodePrivacyInfoResult.isSuccess()) {
                    break;
                }
            }
        }catch (Exception e){
            log.info("submitAuditDelay failed arg:{} wxAppId:{} e:", arg, wxAppId, e);
        }
        SubmitAuditArg submitAuditArg = new SubmitAuditArg();
        submitAuditArg.setPrivacyApiNotUse(privacyApiNotUse);
        log.info("submitAuditDelay submitAuditArg wxAppId:{} arg:{}", wxAppId, submitAuditArg);
        SubmitAuditResult submitAuditResult = codeManageService.submitAudit(accessToken, submitAuditArg);
        log.info("submitAuditDelay submitAuditResult wxAppId:{} auditid:{} errCode:{}  errMsg:{}", wxAppId, submitAuditResult.getAuditId(), submitAuditResult.getErrCode(), submitAuditResult.getErrMsg());
        MiniappReleaseRecordEntity entity = miniappReleaseRecordDao.getLatestConfigReleasedRecord(wxAppId);
        if (!submitAuditResult.isSuccess()) {
            //审核失败
            if (entity == null) {
                miniappReleaseRecordDao.submitAuditFail(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), submitAuditResult.getErrMsg());
            }else {
                miniappReleaseRecordDao.updateById(entity.getId(), wxAppId, MiniappReleaseStatusEnum.AUDIT_FAIL.getStatus(), entity.getAuditId(), submitAuditResult.getErrMsg());
            }
        } else {
            if (entity != null){
                miniappReleaseRecordDao.updateById(entity.getId(), wxAppId, entity.getStatus(), submitAuditResult.getAuditId(),  submitAuditResult.getErrMsg());
            }else {
                miniappReleaseRecordDao.submitAuditSuccess(wxAppId, arg.getCodeTemplateId(), arg.getCodeVersion(), arg.getCodeDescription(), submitAuditResult.getAuditId());
            }
        }
    }

    /**
     * 查询最新的审核记录，若为审核中，则调用微信接口后更新当前状态
     * @param wxAppId
     * @return
     */
    public MiniappReleaseRecordEntity getLatestReleaseAndUpdateAuditing(String wxAppId) {
        MiniappReleaseRecordEntity latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseAndAuditingRecord(wxAppId);
        if(latestReleaseRecord != null){
            if(latestReleaseRecord.getStatus() ==  MiniappReleaseStatusEnum.AUDITING.getStatus() && !StringUtils.equals("0", latestReleaseRecord.getAuditId())){
                GetAuditStatusArg getAuditStatusArg = new GetAuditStatusArg();
                getAuditStatusArg.setAuditId(latestReleaseRecord.getAuditId());
                log.info("getAuditStatus wxAppId:{} getAuditStatusArg:{}", wxAppId, getAuditStatusArg);
                GetAuditStatusResult getAuditStatusResult = codeManageService.getAuditStatus(wechatAccountManager.getAccessTokenByWxAppId(wxAppId), getAuditStatusArg);
                if(getAuditStatusResult.isSuccess()){
                    log.info("getAuditStatus wxAppId:{} getAuditStatusResult:{}", wxAppId, getAuditStatusResult);
                    if(getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.SUCCESS){
                        miniappReleaseRecordDao.auditSuccess(wxAppId, new Date());
                        latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
                    }
                    if(getAuditStatusResult.getStatus() == GetAuditStatusResult.AuditStatus.REJECTED || getAuditStatusResult.getStatus()  == GetAuditStatusResult.AuditStatus.CANCEL){
                        miniappReleaseRecordDao.auditFail(wxAppId, getAuditStatusResult.getReason(), com.facishare.marketing.common.util.GsonUtil.toJson(strToList(getAuditStatusResult.getScreenShot(), "|")), new Date());
                        latestReleaseRecord = miniappReleaseRecordDao.getLatestReleaseRecord(wxAppId);
                    }
                }
            }
            return latestReleaseRecord;
        }
        return null;
    }

    private static List<String> strToList(String str, String separator){
        if(Strings.isNullOrEmpty(str)){
            return new ArrayList<>(0);
        }
        return Splitter.on(separator).splitToList(str).stream().filter(Objects::nonNull).map(String::trim).filter(s -> !Strings.isNullOrEmpty(s)).collect(Collectors.toList());
    }

    private static List<String> strToList(String str){
        return strToList(str, ",");
    }


    public ListParamsBySceneAndSpreadTypeResult listParamsBySceneAndSpreadType(String ea, Integer spreadType, String sceneType, Integer channel) {
        ListParamsBySceneAndSpreadTypeResult result = new ListParamsBySceneAndSpreadTypeResult();
        List<ListParamsBySceneAndSpreadTypeResult.Field> fields = Lists.newArrayList();
        List<String> params = Lists.newArrayList();
        result.setNameList(params);
        result.setFieldList(fields);
        MarketingChannelTypeEnum marketingChannelTypeEnum = MarketingChannelTypeEnum.getByType(channel);
        if (marketingChannelTypeEnum == null) {
            return result;
        }
        //不是微信公众号群发
        if (!(Objects.equals(channel, MarketingChannelTypeEnum.WECHAT_PUBLIC_ACCOUNT.getType()) && Objects.equals(spreadType, MarketingSpreadTypeEnum.MASS_MAILING.getType()))){
            MarketingSceneType marketingSceneType = MarketingSceneType.fromType(sceneType);
            if (MarketingSceneType.COMMON != marketingSceneType) {
                // 获取活动成员自定义字段
                List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
                if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
                    crmCustomerFields.forEach(e -> {
                        if (2 == e.getFieldProperty()) {
                            params.add(e.getFieldCaption());
                            ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                            field.setName(e.getFieldCaption());
                            field.setType(e.getFieldTypeName());
                            field.setVariableName("${"+e.getFieldCaption()+"}");
                            fields.add(field);
                        }
                    });
                }
            }
        }
        switch (marketingChannelTypeEnum) {
            case EMAIL:
            case SMS:
                handleSmsMarketingChannelType(params, sceneType, spreadType);
                break;
            case WECHAT_PUBLIC_ACCOUNT:
//                handleWechatPublicAccountMarketingNoticeSpreadType(params, sceneType, spreadType);
                handleWechatPublicAccountMarketingNoticeSpreadType1(fields, sceneType, spreadType);
                break;
        }
        return result;
    }

    private void handleSmsMarketingChannelType(List<String> params, String sceneType, Integer spreadType) {
        MarketingSceneType marketingSceneType = MarketingSceneType.fromType(sceneType);
        if(marketingSceneType == null) {
            return;
        }
        MarketingSpreadTypeEnum marketingSpreadTypeEnum = MarketingSpreadTypeEnum.getByType(spreadType);
        if (marketingSpreadTypeEnum == null) {
            return;
        }
        switch (marketingSceneType) {
            case CONFERENCE:
                params.addAll(Arrays.stream(ConferenceParamEnum.values()).map(ConferenceParamEnum::getDesc).collect(Collectors.toList()));
                break;
            case LIVE:
                params.addAll(Arrays.stream(LiveParamEnum.values()).map(LiveParamEnum::getDesc).collect(Collectors.toList()));
                break;
            case MARKETING_EVENT:
                break;
        }
    }

    private void handleWechatPublicAccountMarketingNoticeSpreadType(List<String> params, String sceneType, Integer spreadType) {
        MarketingSceneType marketingSceneType = MarketingSceneType.fromType(sceneType);
        if (marketingSceneType == null) {
            return;
        }
        MarketingSpreadTypeEnum marketingSpreadTypeEnum = MarketingSpreadTypeEnum.getByType(spreadType);
        if (marketingSpreadTypeEnum == null) {
            return;
        }
        switch (marketingSceneType) {
            case CONFERENCE:
                if (marketingSpreadTypeEnum == MarketingSpreadTypeEnum.MASS_MAILING) {
                    params.addAll(MarketingEventParamEnum.queryDescBySceneType(marketingSceneType));
                } else {
                    params.addAll(Arrays.stream(ConferenceParamEnum.values()).map(ConferenceParamEnum::getDesc).collect(Collectors.toList()));
                    params.remove(ConferenceParamEnum.ENROLL_EMAIL.getDesc());
                    params.add(MarketingEventParamEnum.WECHAT_NAME.getDesc());
                }
                break;
            case LIVE:
                if (marketingSpreadTypeEnum == MarketingSpreadTypeEnum.MASS_MAILING) {
                    params.addAll(MarketingEventParamEnum.queryDescBySceneType(marketingSceneType));
                } else {
                    params.addAll(Arrays.stream(LiveParamEnum.values()).map(LiveParamEnum::getDesc).collect(Collectors.toList()));
                    params.add(MarketingEventParamEnum.WECHAT_NAME.getDesc());
                }
                break;
            case MARKETING_EVENT:
            case COMMON:
                params.addAll(MarketingEventParamEnum.queryDescBySceneType(marketingSceneType));
                break;
        }
    }

    private void handleWechatPublicAccountMarketingNoticeSpreadType1(List<ListParamsBySceneAndSpreadTypeResult.Field> params, String sceneType, Integer spreadType) {
        MarketingSceneType marketingSceneType = MarketingSceneType.fromType(sceneType);
        if (marketingSceneType == null) {
            return;
        }
        MarketingSpreadTypeEnum marketingSpreadTypeEnum = MarketingSpreadTypeEnum.getByType(spreadType);
        if (marketingSpreadTypeEnum == null) {
            return;
        }
        switch (marketingSceneType) {
            case CONFERENCE:
                if (marketingSpreadTypeEnum == MarketingSpreadTypeEnum.MASS_MAILING) {
                    for (MarketingEventParamEnum eventParamEnum : MarketingEventParamEnum.queryEnumBySceneType(marketingSceneType)) {
                        ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                        field.setName(eventParamEnum.getDesc());
                        field.setType(eventParamEnum.getFiledType());
                        field.setVariableName("${"+eventParamEnum.getDesc()+"}");
                        params.add(field);
                    }
                } else {
                    for (ConferenceParamEnum value : ConferenceParamEnum.values()) {
                        if(!Objects.equals(ConferenceParamEnum.ENROLL_EMAIL.getAttributes(), value.getAttributes())){
                            if(!Objects.equals(value.getFiledType(), "url")){
                                ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                                field.setName(value.getDesc());
                                field.setType(value.getFiledType());
                                field.setVariableName("${"+value.getDesc()+"}");
                                params.add(field);
                            }
                        }
                    }
                }
                break;
            case LIVE:
                if (marketingSpreadTypeEnum == MarketingSpreadTypeEnum.MASS_MAILING) {
                    for (MarketingEventParamEnum eventParamEnum : MarketingEventParamEnum.queryEnumBySceneType(marketingSceneType)) {
                        ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                        field.setName(eventParamEnum.getDesc());
                        field.setType(eventParamEnum.getFiledType());
                        field.setVariableName("${"+eventParamEnum.getDesc()+"}");
                        params.add(field);
                    }
                } else {
                    for (LiveParamEnum value : LiveParamEnum.values()) {
                        if(!Objects.equals(value.getFiledType(), "url")){
                            ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                            field.setName(value.getDesc());
                            field.setType(value.getFiledType());
                            field.setVariableName("${"+value.getDesc()+"}");
                            params.add(field);
                        }
                    }
                    ListParamsBySceneAndSpreadTypeResult.Field wxfiled = new ListParamsBySceneAndSpreadTypeResult.Field();
                    wxfiled.setName(MarketingEventParamEnum.WECHAT_NAME.getDesc());
                    wxfiled.setType(MarketingEventParamEnum.WECHAT_NAME.getFiledType());
                    wxfiled.setVariableName("${"+MarketingEventParamEnum.WECHAT_NAME.getDesc()+"}");
                    params.add(wxfiled);
                }
                break;
            case MARKETING_EVENT:
            case COMMON:
                for (MarketingEventParamEnum commonParamEnum : MarketingEventParamEnum.queryEnumBySceneType(marketingSceneType)) {
                    ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                    field.setName(commonParamEnum.getDesc());
                    field.setType(commonParamEnum.getFiledType());
                    field.setVariableName("${"+commonParamEnum.getDesc()+"}");
                    params.add(field);
                }
                break;
            case YXZS_ALL_SPREAD_WX_TEMPLATE:
                for (YxzsAllSpreadWxTemplateParamsEnum commonParamEnum : YxzsAllSpreadWxTemplateParamsEnum.queryEnumBySceneType(marketingSceneType)) {
                    ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                    field.setName(commonParamEnum.getDesc());
                    field.setType(commonParamEnum.getFiledType());
                    field.setVariableName("${"+commonParamEnum.getDesc()+"}");
                    params.add(field);
                }
                break;
            case YXZS_UNION_NOTICE_WX_TEMPLATE:
                for (YxzsUnionNoticeWxTemplateParamsEnum commonParamEnum : YxzsUnionNoticeWxTemplateParamsEnum.queryEnumBySceneType(marketingSceneType)) {
                    ListParamsBySceneAndSpreadTypeResult.Field field = new ListParamsBySceneAndSpreadTypeResult.Field();
                    field.setName(commonParamEnum.getDesc());
                    field.setType(commonParamEnum.getFiledType());
                    field.setVariableName("${"+commonParamEnum.getDesc()+"}");
                    params.add(field);
                }
                break;
        }
    }

    public Optional<String> getEnterpriseDomain(String ea){
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(ea);
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        if (result == null || result.getEnterpriseData() == null || StringUtils.isEmpty(result.getEnterpriseData().getDomain())) {
            return Optional.empty();
        }

        return Optional.of(result.getEnterpriseData().getDomain());
    }

    public void updateMiniappDomain(String platformId, Set<String> wxAppIds) {
        if (CollectionUtils.isEmpty(wxAppIds)) {
            return;
        }

        Map<String, WechatAccountConfigEntity> map = wechatAccountManager.batchGetWechatAccountConfig(wxAppIds);
        WxThirdPlatformDomainResult platformDomain = wxCloudRestManager.getDomainByPlatformId(platformId);
        for (String wxAppId : wxAppIds) {
            WechatAccountConfigEntity wechatAccountConfig = map.get(wxAppId);
            String accessToken = wechatAccountConfig.getAccessToken();
            ModifyDomainResult modifyDomainResult = settingManager.modifyMiniAppDomain(platformDomain, accessToken);
            if (!modifyDomainResult.isSuccess()) {
                log.error("updateMiniappDomain.modifyMiniAppDomain failed wxAppId:{}, errMsg:{}", wxAppId, modifyDomainResult.getErrMsg());
                return;
            }

            SetWebViewDomainResult setWebViewDomainResult = settingManager.setWebviewDomain(platformDomain.getWebViewDomain(), accessToken);
            if (!setWebViewDomainResult.isSuccess()) {
                log.error("updateMiniappDomain.setWebviewDomain failed wxAppId:{}, errMsg:{}", wxAppId, setWebViewDomainResult.getErrMsg());
            }
        }
    }

    /**
     * 删除小程序提审记录
     * @param id
     */
    public void deleteMiniAppReleaseRecord(Long id) {
        miniappReleaseRecordDao.deleteById(id);
    }
}