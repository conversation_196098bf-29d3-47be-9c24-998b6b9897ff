package com.facishare.marketing.provider.dao.live;

import com.facishare.marketing.provider.entity.live.MarketingLiveViewLoginEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created by zhengh on 2020/3/31.
 */
public interface MarketingLiveViewLoginDAO {
    @Insert("INSERT INTO marketing_live_view_login(id, marketing_event_id, phone, outer_user_id, create_time, update_time)\n"
    + " VALUES(#{entity.id}, #{entity.marketingEventId}, #{entity.phone}, #{entity.outerUserId}, now(), now())")
    void insert(@Param("entity")MarketingLiveViewLoginEntity entity);

    @Select("SELECT phone FROM marketing_live_view_login WHERE marketing_event_id=#{marketingEventId}")
    List<String> getPhonesByMarketingEventId(@Param("marketingEventId")String marketingEventId);

    @Select("SELECT COUNT(*) FROM marketing_live_view_login WHERE marketing_event_id=#{marketingEventId}")
    int getTotalCountByMarketingEventId(@Param("marketingEventId")String marketingEventId);

    @Select("<script>"
            + "SELECT * FROM marketing_live_view_login WHERE marketing_event_id=#{marketingEventId} AND phone IN"
            +   "<foreach collection = 'phones' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    List<MarketingLiveViewLoginEntity> getByMarketingEventIdAndPhones(@Param("marketingEventId")String marketingEventId, @Param("phones")List<String> phones);



    @Select("SELECT * FROM marketing_live_view_login WHERE id=#{id}")
    MarketingLiveViewLoginEntity getById(@Param("id")String id);

    @Select("SELECT * FROM marketing_live_view_login WHERE marketing_event_id=#{marketingEventId}")
    List<MarketingLiveViewLoginEntity> getBymarketingEventId(@Param("marketingEventId") String marketingEventId);
}
