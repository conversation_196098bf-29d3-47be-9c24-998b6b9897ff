package com.facishare.marketing.provider.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class MarketingEventIdTypeDTO implements Serializable{
    private String marketingEventId;
    private String eventType;
    private Date createTime;
    private Date startTime;
    private Date endTime;
    private String title;
    private List<Map<String,Object>> cover;
}
