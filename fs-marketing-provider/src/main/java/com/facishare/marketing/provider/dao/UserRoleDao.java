package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.UserRoleEntity;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

public interface UserRoleDao {
    @Insert("<script>" +
            "INSERT INTO user_role(ea, employee_id, role_id, create_time, update_time) VALUES <foreach collection='employeeIds' item='item' separator=','>" +
            "(#{ea}, #{item}, #{roleId}, NOW(), NOW())" +
            "</foreach>" +
            " ON CONFLICT DO NOTHING" +
            "</script>")
    Integer batchInsertIgnoreByEmployeeIds(@Param("ea") String ea, @Param("roleId") String roleId, @Param("employeeIds") Collection<Integer> employeeIds);

    @Insert("<script>" +
            "INSERT INTO user_role(ea, employee_id, role_id, create_time, update_time) VALUES <foreach collection='roleIds' item='item' separator=','>" +
            "(#{ea}, #{employeeId}, #{item}, NOW(), NOW())" +
            "</foreach>" +
            " ON CONFLICT DO NOTHING" +
            "</script>")
    Integer batchInsertIgnoreByRoleIds(@Param("ea") String ea, @Param("employeeId")Integer employeeId, @Param("roleIds")Collection<String> roleIds);

    @Delete("DELETE FROM user_role WHERE ea=#{ea} AND employee_id=#{employeeId}")
    Integer deleteByEmployeeId(@Param("ea") String ea, @Param("employeeId")Integer employeeId);

    @Delete("<script>" +
            "DELETE FROM user_role WHERE ea=#{ea} AND role_id=#{roleId} AND employee_id IN <foreach open='(' close=')' separator=',' collection='employeeIds' item='item'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    Integer deleteByRoleAndEmployeeIds(@Param("ea") String ea, @Param("roleId") String roleId, @Param("employeeIds") Collection<Integer> employeeIds);

    @Delete("<script>" +
            "DELETE FROM user_role WHERE ea=#{ea} AND employee_id=#{employeeId} AND role_id IN <foreach open='(' close=')' separator=',' collection='roleIds' item='item'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    Integer deleteByEmployeeIdAndRoleIds(@Param("ea") String ea, @Param("employeeId") Integer employeeId, @Param("roleIds") Collection<String> roleIds);

    @Select("SELECT role_id FROM user_role WHERE ea=#{ea} AND employee_id=#{employeeId}")
    List<String> listByEmployeeId(@Param("ea") String ea, @Param("employeeId")Integer employeeId);

    @Select("SELECT * FROM user_role WHERE ea=#{ea} order by update_time")
    @FilterLog
    List<UserRoleEntity> listByEa(@Param("ea") String ea);
    
    @Select("SELECT * FROM user_role WHERE role_id=#{roleId}")
    List<UserRoleEntity> listByRole(@Param("roleId") String roleId);

    @Select("SELECT * FROM user_role WHERE ea=#{ea} AND employee_id=#{employeeId}")
    List<UserRoleEntity> listByEaAndEmployeeId(@Param("ea") String ea, @Param("employeeId")Integer employeeId);

    @Select("SELECT * FROM user_role WHERE ea=#{ea} and role_id=#{roleId} order by create_time desc")
    List<UserRoleEntity> getListByRole(@Param("ea") String ea, @Param("roleId") String roleId);
}
