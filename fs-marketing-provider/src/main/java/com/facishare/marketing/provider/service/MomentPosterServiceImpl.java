package com.facishare.marketing.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.outService.arg.file.SpliceUrlFromApathArg;
import com.facishare.mankeep.api.outService.result.file.SpliceUrlFromApathResult;
import com.facishare.mankeep.api.outService.service.OutFileService;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.api.arg.momentPoster.AddEnterpriseMaterialArg;
import com.facishare.marketing.api.arg.momentPoster.AddPosterArg;
import com.facishare.marketing.api.result.moment.AddPosterResult;
import com.facishare.marketing.api.result.moment.GetDailyPosterInfoResult;
import com.facishare.marketing.api.result.moment.QueryEnterpriseMaterialListUnitResult;
import com.facishare.marketing.api.result.moment.QueryMostPopularMaterialListUnitResult;
import com.facishare.marketing.api.service.MomentPosterService;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.MomentPosterTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.ImageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.MomentPosterMaterialDAO;
import com.facishare.marketing.provider.dao.MomentPosterUserDAO;
import com.facishare.marketing.provider.dao.kis.DailyPosterDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.kis.DailyPosterEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.MomentPosterManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.util.*;
import java.util.stream.Collectors;

@Service("momentPosterService")
@Slf4j
public class MomentPosterServiceImpl implements MomentPosterService {

    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private DailyPosterDAO dailyPosterDAO;
    @Autowired
    private MomentPosterMaterialDAO momentPosterMaterialDAO;
    @Autowired
    private MomentPosterUserDAO momentPosterUserDAO;

    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private MomentPosterManager momentPosterManager;

    @Autowired
    private OutFileService outFileService;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @ReloadableProperty("picture.fsEa")
    private String mankeepEa;
    @ReloadableProperty("daily_poster_default_bg_apath_2")
    private String dailyPosterDefaultBgApath2;
    @ReloadableProperty("daily_poster_default_content")
    private String dailyPosterDefaultContent;
    @ReloadableProperty("daily_poster_default_author")
    private String dailyPosterDefaultAuthor;
    @Autowired
    private QywxUserManager qywxUserManager;

    @Override
    public Result<GetDailyPosterInfoResult> getDailyPosterInfo(String ea, Integer userId) {
        if (StringUtils.isBlank(ea) || null == userId) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
        if (null == uid) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
        if (null == cardEntity) {
            return new Result<>(SHErrorCode.BUSINESSCARD_USER_NOFOUND);
        }

        List<CardEntity> cardEntityList = new ArrayList<>();
        cardEntityList.add(cardEntity);
        photoManager.writeAvatarUrlToCardEntity(cardEntityList);

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), cardEntity.getUid());
        if (CollectionUtils.isEmpty(photoEntityList)) {
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        PhotoEntity qrCodePhotoEntity = photoEntityList.get(0);

        DailyPosterEntity dailyPosterEntity = dailyPosterDAO.queryDailyPosterByEaAndDate(new Date());

        GetDailyPosterInfoResult result = new GetDailyPosterInfoResult();
        if (null != dailyPosterEntity) {
            if (StringUtils.isNotBlank(dailyPosterEntity.getBgApath())) {
                result.setBgUrl(fileV2Manager.getUrlByPath(dailyPosterEntity.getBgApath(), null, false));
            }
            result.setContent(dailyPosterEntity.getContent());
            result.setAuthor(dailyPosterEntity.getAuthor());
        } else {
            result.setContent(dailyPosterDefaultContent);
            result.setAuthor(dailyPosterDefaultAuthor);
        }

        result.setName(UnicodeFormatter.decodeUnicodeString(cardEntity.getName()));
        result.setCompanyName(UnicodeFormatter.decodeUnicodeString(cardEntity.getCompanyName()));
        result.setAvatar(cardEntity.getAvatar());
        result.setQrUrl(qrCodePhotoEntity.getUrl());
        result.setCardUid(cardEntity.getUid());
        result.setCardId(cardEntity.getId());
        result.setNowTime(new Date().getTime());

        return new Result(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<AddPosterResult> addPoster(String ea, Integer userId, AddPosterArg arg) {
        if (StringUtils.isBlank(ea) || null == userId || StringUtils.isBlank(arg.getPath())) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String apath = null;
        String thumbnailApath = null;
        if (arg.getPath().startsWith("TA_")) {
            FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(arg.getPath(), null, null);
            if (null == fileManagerPicResult || StringUtils.isBlank(fileManagerPicResult.getUrlAPath()) || StringUtils.isBlank(fileManagerPicResult.getThumbUrlApath())) {
                return new Result(SHErrorCode.SYSTEM_ERROR);
            }

            apath = fileManagerPicResult.getUrlAPath();
            thumbnailApath = fileManagerPicResult.getThumbUrlApath();
        } else if (arg.getPath().startsWith("A_") || arg.getPath().startsWith("C_")) {
            apath = arg.getPath();
            thumbnailApath = arg.getPath();
        } else {
            log.error("path invalid, path={}", arg.getPath());
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isNotBlank(arg.getEnterpriseMaterialId())) {
            MomentPosterMaterialEntity momentPosterMaterialEntity = momentPosterMaterialDAO.queryById(arg.getEnterpriseMaterialId());
            if (null == momentPosterMaterialEntity) {
                return new Result(SHErrorCode.MOMENT_POST_MATERIAL_NOT_FOUND);
            }

            if (!momentPosterMaterialEntity.getEa().equals(ea)) {
                return new Result(SHErrorCode.MOMENT_POST_MATERIAL_NOT_FOUND);
            }

            String enterpriseMaterialId = arg.getEnterpriseMaterialId();

            MomentPosterUserEntity momentPosterUserEntity = new MomentPosterUserEntity(UUIDUtil.getUUID(), ea, userId, apath, thumbnailApath, enterpriseMaterialId, arg.getContent(), arg.getContentStyle(), null, null);
            if (momentPosterUserDAO.insert(momentPosterUserEntity) != 1) {
                return new Result(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else {

            if (StringUtils.isBlank(arg.getBgPath())) {
                return new Result(SHErrorCode.PARAMS_ERROR);
            }

            String bgApath = null;
            String bgThumbnailApath = null;
            if (arg.getPath().startsWith("TA_")) {
                FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(arg.getBgPath(), null, null);
                if (null == fileManagerPicResult || StringUtils.isBlank(fileManagerPicResult.getUrlAPath()) || StringUtils.isBlank(fileManagerPicResult.getThumbUrlApath())) {
                    return new Result(SHErrorCode.SYSTEM_ERROR);
                }

                bgApath = fileManagerPicResult.getUrlAPath();
                bgThumbnailApath = fileManagerPicResult.getThumbUrlApath();
            } else if (arg.getBgPath().startsWith("A_") || arg.getPath().startsWith("C_")) {
                bgApath = arg.getBgPath();
                bgThumbnailApath = arg.getBgPath();
            } else {
                log.error("bgApath invalid, path={}", arg.getPath());
                return new Result(SHErrorCode.PARAMS_ERROR);
            }

            final String finalBgApath = bgApath;
            final String finalBgThumbnailApath = bgThumbnailApath;

            ThreadPoolUtils.execute(new Runnable() {
                @Override
                public void run() {

                    String enterpriseMaterialId = momentPosterManager.addPoster(ea, userId, null, finalBgApath, finalBgThumbnailApath, MomentPosterTypeEnum.CUSTOMERIZED);
                    if (StringUtils.isBlank(enterpriseMaterialId)) {
                        log.error("MomentPosterService.addPoster momentPosterManager.addPoster failed, finalBgApath={}, ea={}, userId={}", finalBgApath, ea, userId);
                        return;
                    }

                    MomentPosterUserEntity momentPosterUserEntity = new MomentPosterUserEntity(UUIDUtil.getUUID(), ea, userId, finalBgApath, finalBgThumbnailApath, enterpriseMaterialId, arg.getContent(), arg.getContentStyle(), null, null);
                    if (momentPosterUserDAO.insert(momentPosterUserEntity) != 1) {
                        log.error("momentPosterUserDAO.insert failed, entity={}", momentPosterUserEntity);
                    }
                }
            });
        }

        String imageUrl = fileV2Manager.getUrlByPath(ea, apath);
        if (StringUtils.isBlank(imageUrl)) {
            log.error("MomentPosterService.addPoster getUrlByPath failed, imageUrl is null, ea={}, apath={}", ea, apath);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        AddPosterResult addPosterResult = new AddPosterResult();
        addPosterResult.setUrl(imageUrl);

        return new Result(SHErrorCode.SUCCESS, addPosterResult);
    }

    @Override
    public Result<PageResult<QueryMostPopularMaterialListUnitResult>> queryMostPopularMaterialList(String ea, Integer userId, Integer pageSize, Integer pageNum, Long time) {
        if (StringUtils.isBlank(ea) || null == userId) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        time = (null == time ? new Date().getTime() : time);

        Page page = new Page(pageNum, pageSize, true);
        List<QueryMostPopularMaterialListUnitResult> resultList = new ArrayList<>();

        List<MomentPosterMaterialEntity> momentPosterMaterialEntityList = momentPosterMaterialDAO.queryMostPopularByEa(ea, page);
        Map<String, Integer> momentPosterMaterialUsingCountMap = new HashMap<>();

        Map<String, Set<Integer>> momentPosterMaterialUserIdsMap = new HashMap<>();
        Map<Integer, String> userIdAvatar = new HashMap<>();

        Set<Integer> totalUserIdsSet = new HashSet<>();

        List<String> bgApathList = new ArrayList<>();
        List<String> bgThumbnailApathList = new ArrayList<>();
        for (MomentPosterMaterialEntity momentPosterMaterialEntity : momentPosterMaterialEntityList) {
            List<Integer> userIdList = momentPosterUserDAO.queryByCluster(momentPosterMaterialEntity.getCluster());
            if (CollectionUtils.isEmpty(userIdList)) {
                momentPosterMaterialUsingCountMap.put(momentPosterMaterialEntity.getId(), 0);
            } else {
                momentPosterMaterialUsingCountMap.put(momentPosterMaterialEntity.getId(), userIdList.size());

                Set<Integer> userIdSet = new HashSet<>(userIdList);
                totalUserIdsSet.addAll(userIdSet);
                momentPosterMaterialUserIdsMap.put(momentPosterMaterialEntity.getId(), userIdSet);
            }

            bgApathList.add(momentPosterMaterialEntity.getBgApath());
            bgThumbnailApathList.add(momentPosterMaterialEntity.getBgThumbnailApath());
        }

        if (totalUserIdsSet.size() > 0) {
            Map<String, Integer> uidUserIdMap = new HashMap<>();
            List<FSBindEntity> fsBindEntityList = qywxUserManager.getFsBindByFsUserInfos(ea, new ArrayList<>(totalUserIdsSet));
            Set uidSet = new HashSet();
            for (FSBindEntity fsBindEntity1 : fsBindEntityList) {
                uidSet.add(fsBindEntity1.getUid());
                uidUserIdMap.put(fsBindEntity1.getUid(), fsBindEntity1.getFsUserId());
            }

            if (uidSet.size() > 0) {
                List<CardEntity> cardEntityList = cardDAO.listByUids(new ArrayList<>(uidSet));
                photoManager.writeAvatarUrlToCardEntity(cardEntityList);
                for (CardEntity cardEntity : cardEntityList) {
                    userIdAvatar.put(uidUserIdMap.get(cardEntity.getUid()), cardEntity.getAvatar());
                }
            }
        }

        Map<String, String> bgUrlMap = fileV2Manager.batchGetUrlByPath(bgApathList, ea, false);
        Map<String, String> bgThumbnailUrlMap = fileV2Manager.batchGetUrlByPath(bgThumbnailApathList, ea, false);

        for (MomentPosterMaterialEntity momentPosterMaterialEntity : momentPosterMaterialEntityList) {
            List<String> avatarList = new ArrayList<>();
            Set<Integer> userIdSet = momentPosterMaterialUserIdsMap.get(momentPosterMaterialEntity.getId());
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                List<Integer> userIdList = new ArrayList<>(userIdSet);
                Integer count = 0;
                for (Integer element : userIdList) {
                    String avatar = userIdAvatar.get(element);
                    if (StringUtils.isNotBlank(avatar)) {
                        avatarList.add(avatar);
                    } else {
                        avatarList.add("");
                    }

                    count++;
                    if (count >= 3) break;
                }
            }

            QueryMostPopularMaterialListUnitResult result = new QueryMostPopularMaterialListUnitResult(momentPosterMaterialEntity.getId(), bgUrlMap.get(momentPosterMaterialEntity.getBgApath()), bgThumbnailUrlMap.get(momentPosterMaterialEntity.getBgThumbnailApath()), momentPosterMaterialUsingCountMap.get(momentPosterMaterialEntity.getId()), avatarList);
            resultList.add(result);
        }

        PageResult<QueryMostPopularMaterialListUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(momentPosterMaterialDAO.countQueryMostPopularByEa(ea));

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }


    @Override
    public Result<PageResult<QueryEnterpriseMaterialListUnitResult>> queryEnterpriseMaterialList(String ea, Integer userId, Integer pageSize, Integer pageNum, Long time, String title) {
        if (StringUtils.isBlank(ea) || null == userId) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        time = (time == null ? new Date().getTime() : time);

        Page page = new Page(pageNum, pageSize, true);
        List<QueryEnterpriseMaterialListUnitResult> resultList = new ArrayList<>();

        List<String> bgApathList = new ArrayList<>();
        List<String> bgThumbnailApathList = new ArrayList<>();
        List<MomentPosterMaterialEntity> momentPosterMaterialEntityList = momentPosterMaterialDAO.queryEnterpirseAndExampleByEa(ea, page, title);
        for (MomentPosterMaterialEntity momentPosterMaterialEntity : momentPosterMaterialEntityList) {
            bgApathList.add(momentPosterMaterialEntity.getBgApath());
            bgThumbnailApathList.add(momentPosterMaterialEntity.getBgThumbnailApath());
        }

        Map<String, String> bgUrlMap = fileV2Manager.batchGetUrlByPath(bgApathList, ea, false);
        Map<String, String> bgThumbnailUrlMap = fileV2Manager.batchGetUrlByPath(bgThumbnailApathList, ea, false);

        for (MomentPosterMaterialEntity momentPosterMaterialEntity : momentPosterMaterialEntityList) {
            QueryEnterpriseMaterialListUnitResult result = new QueryEnterpriseMaterialListUnitResult(momentPosterMaterialEntity.getId(), bgUrlMap.get(momentPosterMaterialEntity.getBgApath()), bgThumbnailUrlMap.get(momentPosterMaterialEntity.getBgThumbnailApath()), momentPosterMaterialEntity.getTitle(), momentPosterMaterialEntity.getCreateTime().getTime());
            resultList.add(result);
        }

        PageResult<QueryEnterpriseMaterialListUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result addEnterpriseMaterial(String ea, Integer userId, AddEnterpriseMaterialArg arg) {
        if (StringUtils.isBlank(ea) || null == userId) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        TraceContext context = TraceContext.get();
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                if (context != null ) {
                    TraceContext._set(context);
                }
                String path = null;
                String thumbnailPath = null;
                if(arg.getBgTAPath().startsWith("C_")) {
                    path = arg.getBgTAPath();
                    thumbnailPath = arg.getBgTAPath();
                }else {
                    FileV2Manager.FileManagerPicResult bgFileManagerPicResult = fileV2Manager.getApathByTApath(arg.getBgTAPath(), ea, userId);
                    if (null == bgFileManagerPicResult || StringUtils.isBlank(bgFileManagerPicResult.getUrlAPath()) || StringUtils.isBlank(bgFileManagerPicResult.getThumbUrlApath())) {
                        log.error("MomentPosterService.addEnterpriseMaterial getApathByTApath failed, taPath={}, ea={}, userId={}", arg.getBgTAPath(), ea, userId);
                        return;
                    }
                    path = bgFileManagerPicResult.getUrlAPath();
                    thumbnailPath = bgFileManagerPicResult.getThumbUrlApath();
                }

                momentPosterManager.addPoster(ea, userId, arg.getTitle(), path, thumbnailPath, MomentPosterTypeEnum.ENTERPRISE);
            }
        });

        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result deleteEnterpriseMaterial(String ea, Integer userId, String id) {
        if (StringUtils.isBlank(ea) || null == userId || StringUtils.isBlank(id)) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        MomentPosterMaterialEntity entity = momentPosterMaterialDAO.queryById(id);
        if (entity == null) {
            return new Result(SHErrorCode.NO_DATA);
        }
        if (momentPosterMaterialDAO.deleteById(id) < 1) {
            return new Result(SHErrorCode.OPERATE_DB_FAIL);
        }

        momentPosterUserDAO.deleteByMomentPosterMaterialId(id);

        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.QR_POSTER.getType(), entity.getTitle(), OperateTypeEnum.DELETE);
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result initExample(String ea) {
        momentPosterManager.initExample(ea);
        return new Result<>(SHErrorCode.SUCCESS);
    }

}
