package com.facishare.marketing.provider.service.baidu;

import com.facishare.marketing.api.arg.advertiser.BaiduFeedDetailArg;
import com.facishare.marketing.api.arg.advertiser.BaiduFeedListArg;
import com.facishare.marketing.api.result.baidu.BaiduAdGroupFeedVO;
import com.facishare.marketing.api.service.baidu.BaiduFeedService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.bo.advertise.BaiduAdGroupFeedBO;
import com.facishare.marketing.provider.dao.baidu.BaiduAdGroupFeedDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduAdGroupFeedDataDAO;
import com.facishare.marketing.provider.dao.baidu.BaiduCampaignFeedDAO;
import com.facishare.marketing.provider.entity.baidu.BaiduAdGroupFeedEntity;
import com.facishare.marketing.provider.entity.baidu.BaiduCampaignFeedEntity;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("baiduFeedService")
public class BaiduFeedServiceImpl implements BaiduFeedService {

    @Autowired
    private BaiduAdGroupFeedDAO baiduAdGroupFeedDAO;

    @Autowired
    private BaiduAdGroupFeedDataDAO baiduAdGroupFeedDataDAO;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private BaiduCampaignFeedDAO baiduCampaignFeedDAO;

    @Override
    public Result<PageResult<BaiduAdGroupFeedVO>> queryAdGroupList(BaiduFeedListArg arg) {
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<BaiduAdGroupFeedVO> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        List<BaiduAdGroupFeedVO> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);
        Page<BaiduAdGroupFeedBO> page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
        List<BaiduAdGroupFeedBO> adGroupFeedBOList = baiduAdGroupFeedDAO.pageAdGroupFeed(arg, page);
        if (CollectionUtils.isEmpty(adGroupFeedBOList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        String ea = arg.getEa();
        List<Long> adGroupFeedIdList = Lists.newArrayList();
        List<String> marketingEventIdList = Lists.newArrayList();
        adGroupFeedBOList.forEach(adGroupFeedBO -> {
            adGroupFeedIdList.add(adGroupFeedBO.getAdgroupFeedId());
            if (StringUtils.isNotBlank(adGroupFeedBO.getMarketingEventId())) {
                marketingEventIdList.add(adGroupFeedBO.getMarketingEventId());
            }
        });
        List<BaiduAdGroupFeedBO> adGroupFeedStatisticDataList = baiduAdGroupFeedDataDAO.statisticsDataByFeedIdAndTime(ea, arg.getAdAccountId(), adGroupFeedIdList, arg.getStartTime(), arg.getEndTime());
        Map<Long, BaiduAdGroupFeedBO> adGroupFeedIdToStatisticsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adGroupFeedStatisticDataList)) {
            adGroupFeedStatisticDataList.forEach(adGroupFeedBO -> adGroupFeedIdToStatisticsMap.put(adGroupFeedBO.getAdgroupFeedId(), adGroupFeedBO));
        }
        Map<String, Integer> marketingEventIdToLeadCountMap = marketingEventManager.getMarketingEventIdToLeadCountMap(ea, marketingEventIdList, arg.getStartTime().getTime(), arg.getEndTime().getTime());
        for (BaiduAdGroupFeedBO baiduAdGroupFeedBO : adGroupFeedBOList) {
            BaiduAdGroupFeedVO adGroupFeedVO = BeanUtil.copy(baiduAdGroupFeedBO, BaiduAdGroupFeedVO.class);
            BaiduAdGroupFeedBO statisticData = adGroupFeedIdToStatisticsMap.get(adGroupFeedVO.getAdgroupFeedId());
            fillAdGroupFeedVO(adGroupFeedVO, statisticData, marketingEventIdToLeadCountMap.get(adGroupFeedVO.getMarketingEventId()));
            resultList.add(adGroupFeedVO);
        }
        return Result.newSuccess(pageResult);
    }

    private static void fillAdGroupFeedVO(BaiduAdGroupFeedVO adGroupFeedVO, BaiduAdGroupFeedBO statisticData, Integer leadCount) {
        if (statisticData != null) {
            adGroupFeedVO.setPv(statisticData.getPv());
            adGroupFeedVO.setClick(statisticData.getClick());
            adGroupFeedVO.setCost(statisticData.getCost());
        }
        BigDecimal clickPrice = BigDecimal.ZERO;
        if (adGroupFeedVO.getClick() != null && adGroupFeedVO.getClick() != 0 && adGroupFeedVO.getCost() != null) {
            clickPrice = adGroupFeedVO.getCost().divide(BigDecimal.valueOf(adGroupFeedVO.getClick()), 2, RoundingMode.HALF_UP);
        }
        adGroupFeedVO.setClickPrice(clickPrice);
        adGroupFeedVO.setLeadCount(leadCount == null ? 0 : leadCount);
    }

    @Override
    public Result<BaiduAdGroupFeedVO> queryAdGroupDetail(BaiduFeedDetailArg arg) {
        if (StringUtils.isBlank(arg.getId()) || arg.getStartTime() == null || arg.getEndTime() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        BaiduAdGroupFeedEntity baiduAdGroupFeedEntity = baiduAdGroupFeedDAO.getById(ea, arg.getId());
        if (baiduAdGroupFeedEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        List<BaiduAdGroupFeedBO> adGroupFeedStatisticDataList = baiduAdGroupFeedDataDAO.statisticsDataByFeedIdAndTime(ea, baiduAdGroupFeedEntity.getAdAccountId(), Lists.newArrayList(baiduAdGroupFeedEntity.getAdgroupFeedId()), arg.getStartTime(), arg.getEndTime());
        BaiduAdGroupFeedBO statisticData = CollectionUtils.isEmpty(adGroupFeedStatisticDataList) ? null : adGroupFeedStatisticDataList.get(0);
        Integer leadCount = null;
        if (StringUtils.isNotBlank(baiduAdGroupFeedEntity.getMarketingEventId())) {
            Map<String, Integer> marketingEventIdToLeadCountMap = marketingEventManager.getMarketingEventIdToLeadCountMap(ea, Lists.newArrayList(baiduAdGroupFeedEntity.getMarketingEventId()), arg.getStartTime().getTime(), arg.getEndTime().getTime());
            leadCount = marketingEventIdToLeadCountMap.get(baiduAdGroupFeedEntity.getMarketingEventId());
        }
        BaiduAdGroupFeedVO adGroupFeedVO = new BaiduAdGroupFeedVO();
        fillAdGroupFeedVO(adGroupFeedVO, statisticData, leadCount);
        adGroupFeedVO.setAdgroupFeedName(baiduAdGroupFeedEntity.getAdgroupFeedName());
        List<BaiduCampaignFeedEntity> baiduCampaignFeedEntityList = baiduCampaignFeedDAO.getByCampaignFeedIdList(ea, baiduAdGroupFeedEntity.getAdAccountId(), Lists.newArrayList(baiduAdGroupFeedEntity.getCampaignFeedId()));
        if (CollectionUtils.isNotEmpty(baiduCampaignFeedEntityList)) {
            BaiduCampaignFeedEntity baiduCampaignFeedEntity = baiduCampaignFeedEntityList.get(0);
            adGroupFeedVO.setCampaignFeedName(baiduCampaignFeedEntity.getCampaignFeedName());
        }
        return Result.newSuccess(adGroupFeedVO);
    }
}
