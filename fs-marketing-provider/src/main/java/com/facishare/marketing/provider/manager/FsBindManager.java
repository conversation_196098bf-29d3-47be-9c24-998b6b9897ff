package com.facishare.marketing.provider.manager;

import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.FSBindDAO;
import com.facishare.marketing.provider.dao.QywxContactSettingDAO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/5/31
 **/
@Component
@Slf4j
public class FsBindManager {

    @Autowired
    private FSBindDAO fsBindDAO;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private QywxContactSettingDAO qywxContactSettingDAO;

    @Autowired
    private UserRelationManager userRelationManager;

    /**
     * 查询企业全部名片
     * @param ea
     * @return
     */
    public List<CardEntity> getAllCardByEa(String ea){
        List<FSBindEntity> fsBindEntities = fsBindDAO.getAllFsBindByEa(ea);
        if (CollectionUtils.isNotEmpty(fsBindEntities)) {
            List<String> uids = fsBindEntities.stream().map(FSBindEntity::getUid).collect(Collectors.toList());
            List<CardEntity> cardEntities = cardDAO.listByUids(uids);
            return cardEntities;
        }
        return Lists.newArrayList();
    }

    /**
     * 渠道设置 > 企微 > 设置 > 消息设置 > 是否为纷享通讯录
     * @param ea
     * @return
     */
    //陈川要求删除此逻辑
    public boolean isQywxContactFsSetting(String ea) {
        boolean flag = false;
//        QywxContactSettingEntity qywxContactSettingEntity = qywxContactSettingDAO.queryByEa(ea);
//        if (qywxContactSettingEntity != null && Objects.equals(QywxContactTypeEnum.FX.getType(),qywxContactSettingEntity.getContactType())) {
//            flag = true;
//        }
        return flag;
    }

    public String getEaByUid(String uid) {
        FSBindEntity fsBindEntity = fsBindDAO.queryFSBindByUid(uid);
        return fsBindEntity == null ? null : fsBindEntity.getFsEa();
    }

    public List<FSBindEntity> queryFSBindByEmployeeIds(String ea, List<Integer> employeeIds, List<Integer> typeList, String appId) {
        return fsBindDAO.queryFSBindByEmployeeIds(ea, employeeIds, typeList, appId);
    }

    public int queryFSBindByEmployeeIdsCount(String ea, List<Integer> employeeIds, Integer type, String appId) {
        return fsBindDAO.queryFSBindByEmployeeIdsCount(ea, employeeIds, type, appId);
    }

    public int queryFSBindByEmployeeIdsCountWithoutType(String ea, String appId, List<Integer> employeeIds) {
        return fsBindDAO.queryFSBindByEmployeeIdsCountWithoutType(ea, appId, employeeIds);
    }

    public List<Integer> queryFSUserIdsBindByEmployeeIdsWithoutType(String ea, String appId, List<Integer> employeeIds) {
        return fsBindDAO.queryFSUserIdsBindByEmployeeIdsWithoutType(ea, appId, employeeIds);
    }

    public List<FSBindEntity> queryFSBindByType(String ea, Integer type, String appId) {
        return fsBindDAO.queryFSBindByType(ea, type, appId);
    }

    public List<Integer> queryFSBindByFsUserId(String ea, List<Integer> typeList, String appId) {
        return fsBindDAO.queryFSBindByFsUserId(ea, typeList, appId);
    }

    public String queryUidByFsUserIdAndFsEa(String ea, Integer fsUserId, Integer type, String appId) {
        return fsBindDAO.queryUidByFsUserIdAndFsEa(ea, fsUserId, type, appId);
    }

    public String queryDingUidByFsUserIdAndEa(String ea, Integer fsUserId, Integer type) {
        return fsBindDAO.queryDingUidByFsUserIdAndEa(ea, fsUserId, type);
    }

    public FSBindEntity queryFSBindByUid(String uid) {
        return fsBindDAO.queryFSBindByUid(uid);
    }

    public FSBindEntity queryFSBindByUidAndEa(String uid, String ea) {
        return fsBindDAO.queryFSBindByUidAndEa(uid, ea);
    }

    public FSBindEntity queryFSBindByFsEaAndFsUserId(String ea, Integer fsUserId, Integer type, String appId) {
        return fsBindDAO.queryFSBindByFsEaAndFsUserId(ea, fsUserId, type, appId);
    }

    public int insert(FSBindEntity fsBindEntity) {
        int result = fsBindDAO.insert(fsBindEntity);
        userRelationManager.bindUidToFsUserId(fsBindEntity.getFsEa(), fsBindEntity.getUid(), fsBindEntity.getFsUserId());
        return result;
    }

    public int update(FSBindEntity fsBindEntity) {
        int result = fsBindDAO.update(fsBindEntity);
        userRelationManager.bindUidToFsUserId(fsBindEntity.getFsEa(), fsBindEntity.getUid(), fsBindEntity.getFsUserId());
        return result;
    }

    public Integer queryFSBindDingCount(String ea,  Integer type) {
        return fsBindDAO.queryFSBindDingCount(ea, type);
    }

    public void updateFsBindAppId(String appId, String uid) {
        fsBindDAO.updateFsBindAppId(appId, uid);
    }

    public void updateFsBindFsUserId(Integer fsUserId,String uid) {
        if (fsUserId == null) {
            return;
        }
        FSBindEntity fsBindEntity = queryFSBindByUid(uid);
        if (fsBindEntity == null || fsBindEntity.getFsUserId().equals(fsUserId)) {
            return;
        }
        fsBindDAO.updateFsBindFsUserId(fsUserId, uid);
        userRelationManager.unBindUidAndFsUserRelation(fsBindEntity.getFsEa(), fsBindEntity.getUid(), fsBindEntity.getFsUserId());
        userRelationManager.bindUidToFsUserId(fsBindEntity.getFsEa(), fsBindEntity.getUid(), fsUserId);
    }

    public void updateFsBindFsUserIdIgnoreUserRelation(Integer fsUserId,String uid) {
        if (fsUserId == null) {
            return;
        }
        FSBindEntity fsBindEntity = queryFSBindByUid(uid);
        if (fsBindEntity == null || fsBindEntity.getFsUserId().equals(fsUserId)) {
            return;
        }
        fsBindDAO.updateFsBindFsUserId(fsUserId, uid);
    }

    public void updateFsBindPhone(String phone, String uid) {
        fsBindDAO.updateFsBindPhone(phone, uid);
    }

    public FSBindEntity queryFSBindByFsInfo(String fsEa, Integer fsUserId, Integer fsEi, int type) {
        return fsBindDAO.queryFSBindByFsInfo(fsEa, fsUserId, fsEi, type);
    }

    public int delFSBindByUid(String uid) {
        FSBindEntity fsBindEntity = queryFSBindByUid(uid);
        if (fsBindEntity == null) {
            return 0;
        }
        int result = fsBindDAO.delFSBindByUid(uid);
        userRelationManager.unBindUidAndFsUserRelation(fsBindEntity.getFsEa(), uid, fsBindEntity.getFsUserId());
        return result;
    }

    public int delFSBindByUidIgnoreUserRelation(String uid) {
        return fsBindDAO.delFSBindByUid(uid);
    }

    public int delFSBindByUidIgnoreUseRelation(String uid) {
        FSBindEntity fsBindEntity = queryFSBindByUid(uid);
        if (fsBindEntity == null) {
            return 0;
        }
        return fsBindDAO.delFSBindByUid(uid);
    }

    public String queryByFsEaAndUserId(String ea, Integer fsUserId) {
        return fsBindDAO.queryByFsEaAndUserId(ea, fsUserId);
    }

    public List<FSBindEntity> getFsBindByEa(String ea) {
        return fsBindDAO.getFsBindByEa(ea);
    }

    public List<FSBindEntity> getAllFsBindByEa(@Param("ea")String ea) {
        return fsBindDAO.getAllFsBindByEa(ea);
    }

    public String queryUidByLimitOne(String ea, Integer outerQrcodeSpreadFsUid) {
        return fsBindDAO.queryUidByLimitOne(ea, outerQrcodeSpreadFsUid);
    }

    public FSBindEntity queryByUserAndAppId(String ea, Integer fsUserId, String appId) {
        return fsBindDAO.queryByUserAndAppId(ea, fsUserId, appId);
    }

    public List<FSBindEntity> getByFsUserIdList(String ea, List<Integer> fsUserIdList) {
        return fsBindDAO.getByFsUserIdList(ea, fsUserIdList);
    }

    public int deleteByEaAndFsUserId(String ea, Integer userId) {
        List<FSBindEntity> fsBindEntityList = getByFsUserIdList(ea, Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(fsBindEntityList)) {
            return 0;
        }
        int result = fsBindDAO.deleteByEaAndFsUserId(ea, userId);
        fsBindEntityList.forEach(e -> userRelationManager.unBindUidAndFsUserRelation(ea, e.getUid(), userId));
        return result;
    }

    public List<Integer> getAllFsUserIdByEa(String ea) {
        return fsBindDAO.getAllFsUserIdByEa(ea);
    }

    public void mergerUid(String oldUid, String newUid) {
        FSBindEntity wxUserBindEntity = queryFSBindByUid(oldUid);
        FSBindEntity qywxUserBindEntity = queryFSBindByUid(newUid);
        if (wxUserBindEntity != null) {
            if (qywxUserBindEntity == null){
                fsBindDAO.updateUidByOldUid(wxUserBindEntity.getUid(), newUid);
            }else {
                fsBindDAO.delFSBindByUid(oldUid);
            }
        }
        userRelationManager.mergeUidRelation(oldUid, newUid);
    }

    public void updateUserIdById(String uid, Integer fsUserId) {
        fsBindDAO.updateUserIdById(uid, fsUserId);
    }
}
