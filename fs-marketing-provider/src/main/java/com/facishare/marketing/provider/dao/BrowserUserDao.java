package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;;

public interface BrowserUserDao {
    @FilterLog
    @Insert("INSERT INTO browser_user(id, user_agent, create_time, update_time) values(#{id}, #{userAgent}, NOW(), NOW())")
    int insert(@Param("id")String id, @Param("userAgent") String userAgent);


    @Select("<script>"
            + "select id from browser_user where create_time <![CDATA[ <= ]]>  #{createTime} \n "
            + " <if test=\"lastId != null and lastId != ''\">\n"
            + "     AND id <![CDATA[ > ]]> #{lastId} \n"
            + " </if>\n"
            + " order by id asc limit #{limit}"
            + "</script>"
    )
    List<String> getByPastSpecifiedTimeAndLastId(@Param("createTime") Date createTime, @Param("lastId") String lastId, @Param("limit") int limit);

    @Delete("<script>"
            + "delete FROM browser_user WHERE  id =ANY(ARRAY "
            + "<foreach collection='browserUserIds' item='browserUserId' open='[' close=']' separator=','>#{browserUserId}</foreach> )"
            + "</script>")
    void deleteByIds(@Param("browserUserIds") List<String> browserIdList);
}
