package com.facishare.marketing.provider.service.marketingactivity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.marketingactivity.*;
import com.facishare.marketing.api.data.BatchGetSendNotificationStatisticData;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.result.marketingactivity.*;
import com.facishare.marketing.api.result.sms.QuerySMSSendResult;
import com.facishare.marketing.api.service.BoardService;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityActionService;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.MarketingActivityNoticeSendVO;
import com.facishare.marketing.api.vo.MarketingActivityPartnerNoticeSendVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.mail.MailScheduleTypeEnum;
import com.facishare.marketing.common.enums.marketingactivity.MarketingActivityActionEnum;
import com.facishare.marketing.common.enums.sms.mw.MwSendStatusEnum;
import com.facishare.marketing.common.enums.sms.mw.MwSendTaskTypeEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.AssertUtil;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.ListUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadFactory;
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.MarketingActivityObjectRelationDAO;
import com.facishare.marketing.provider.dao.NoticeDAO;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityStatisticDao;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.data.MarketingActivityNoticeSendData;
import com.facishare.marketing.provider.entity.data.MarketingActivityPartnerNoticeSendData;
import com.facishare.marketing.provider.entity.data.WeChatServiceMarketingActivityData;
import com.facishare.marketing.provider.entity.data.WeChatTemplateMessageData;
import com.facishare.marketing.provider.entity.kis.MarketingActivityStatisticEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.feed.MaterailDataManager;
import com.facishare.marketing.provider.manager.feed.MaterailDataManagerFactory;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingActivityInstanceLogManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.marketingactivity.*;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.manager.qywx.MomentManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.sms.SmsTaskSchedulerManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasUpdateMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.GetMarketingActivityDetailVo;
import com.facishare.marketing.provider.util.TransactionUtils;
import com.facishare.marketing.statistic.outapi.service.MarketingActivityStatisticService;
import com.facishare.wechat.sender.api.enums.NoticeValidEnum;
import com.facishare.wechat.sender.api.result.NoticeDetailResult;
import com.facishare.wechat.sender.api.service.NoticeService;
import com.facishare.wechat.sender.api.vo.NoticeBaseVo;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingActivityFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.MarketingActivityData;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/2/21.
 */
@Service("marketingActivityService")
@Slf4j
public class MarketingActivityServiceImpl implements MarketingActivityService {
    private static final String deafultName = "";
    @Autowired
    private WeChatServiceNoticeManager weChatServiceNoticeManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private SendService sendService;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private MarketingActivityActionManager marketingActivityActionManager;
    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingEventService marketingEventService;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private com.facishare.marketing.api.service.NoticeService noticeSendService;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private com.facishare.marketing.api.service.NoticeService allSpreadNoticeService;
    @Autowired
    private MaterailDataManagerFactory materailDataManagerFactory;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private MarketingActivityStatisticService marketingActivityStatisticService;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private NoticeDAO noticeDAO;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private QywxGroupSendManager qywxGroupSendManager;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;
    @Autowired
    private MarketingActivityStatisticDao marketingActivityStatisticDao;
    @Autowired
    private MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;
    @Autowired
    private BoardService boardService;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private ActivityShowDataConfigDAO activityShowDataConfigDAO;
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private MomentPublishManager momentPublishManager;
    @Autowired
    private MomentManager momentManager;
    @Autowired
    private WeChatServiceManager weChatServiceManager;
    @Autowired
    private com.facishare.marketing.api.service.QywxMomentService qywxMomentService;
    @Autowired
    private GroupSendMessageManager groupSendMessageManager;
    @Autowired
    private SmsTaskSchedulerManager smsTaskSchedulerManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private DataSourceTransactionManager transactionManager;
    @Autowired
    private MarketingActivityInstanceLogManager marketingActivityInstanceLogManager;

    public Result<PageResult<WxMarketingActivityResult>> listWxMarketingActivity(String ea, Integer fsUserId, String name, String appId, Integer pageNum, Integer pageSize) {
        List<MarketingActivityExternalConfigEntity> entityList;
        Page<ObjectData> objectDataPage;
        Map<String, MarketingActivityExternalConfigEntity> marketingActivityIdToEntityMap;
        ControllerListArg arg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setOffset((pageNum - 1) * pageSize);
        searchQuery.setLimit(pageSize);
        if(!Strings.isNullOrEmpty(name)){
            searchQuery.addFilter(ObjectDescribeContants.NAME, Lists.newArrayList(name), FilterOperatorEnum.LIKE);
        }
        searchQuery.addFilter(MarketingActivityFieldContants.SPREAD_TYPE, Lists.newArrayList(String.valueOf(MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType())), FilterOperatorEnum.EQ);
        if (crmV2Manager.checkObjectFieldExist(ea,  MarketingActivityFieldContants.API_NAME, "wx_app_id")){
            Optional<String> wxAppIdRes = outerServiceWechatManager.transWxAppIdByEaAndFsAppId(appId);
            String wxAppId = appId;
            if (wxAppIdRes.isPresent()) {
                wxAppId = wxAppIdRes.get();
            }
            searchQuery.addFilter("wx_app_id",Lists.newArrayList(wxAppId),FilterOperatorEnum.EQ);
        }
        arg.setSearchQuery(searchQuery);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> objectResult = metadataControllerService.list(new HeaderObj(ei, fsUserId), MarketingActivityFieldContants.API_NAME, arg);
        if (!objectResult.isSuccess()) {
            throw new OuterServiceRuntimeException(objectResult.getCode(), objectResult.getMessage());
        }
        objectDataPage = objectResult.getData();
        if (objectDataPage == null || objectDataPage.getTotal() == 0) {
            PageResult<WxMarketingActivityResult> pageResult = new PageResult<>();
            pageResult.setPageNum(pageNum);
            pageResult.setPageSize(pageSize);
            pageResult.setTotalCount(0);
            pageResult.setResult(new ArrayList<>());
            return Result.newSuccess(pageResult);
        }
        List<String> marketingActivityIdsFromCrm = objectDataPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        entityList = marketingActivityExternalConfigDao.getByMarketingActivityIds(marketingActivityIdsFromCrm);
        Integer count = objectDataPage.getTotal();
        marketingActivityIdToEntityMap = entityList.stream().collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getMarketingActivityId, Function.identity()));
        List<MarketingActivityData> marketingActivityDataList = MarketingActivityData.wrap(objectDataPage.getDataList());
        List<Integer> msgIds = entityList.stream().filter(val -> val.getAssociateId() != null).map(val -> Integer.valueOf(val.getAssociateId())).collect(Collectors.toList());
        com.facishare.wechat.union.common.result.Result<List<NoticeDetailResult>> noticeDetailResults = noticeService.findByIds(ea, appId, msgIds, NoticeValidEnum.VALID.getType());
        if (!noticeDetailResults.isSuccess()) {
            log.warn("listWxMarketingActivity noticeService.findByIds fail ,ea = {} , appId = {} , msgIds = {} , result = {}", ea, appId, msgIds, noticeDetailResults);
        }
        List<NoticeDetailResult> noticeDetailResultList = noticeDetailResults.getData();
        Map<String, NoticeDetailResult> noticeDetailResultMap = CollectionUtils.isEmpty(noticeDetailResultList) ? Maps.newHashMap()
                : noticeDetailResultList.stream().filter(val -> val.getMsgId() != null).collect(Collectors.toMap(val -> String.valueOf(val.getMsgId()), val -> val));
        Map<Integer, List<String>> materialTypeAndIdsMap = Maps.newHashMap();
        for (MarketingActivityExternalConfigEntity value : marketingActivityIdToEntityMap.values()) {
            if (value.getExternalConfig() != null && value.getExternalConfig().getWeChatTemplateMessageVO() != null && StringUtils
                    .isNotEmpty(value.getExternalConfig().getWeChatTemplateMessageVO().getMaterialId()) && value.getExternalConfig().getWeChatTemplateMessageVO().getMaterialType() != null) {
                Integer materialType = value.getExternalConfig().getWeChatTemplateMessageVO().getMaterialType();
                String materialId = value.getExternalConfig().getWeChatTemplateMessageVO().getMaterialId();
                materialTypeAndIdsMap.computeIfAbsent(materialType, k -> Lists.newArrayList());
                materialTypeAndIdsMap.get(materialType).add(materialId);
            }
        }
        Map<Integer, Map<String, AbstractMaterialData>> materialTypeAndIdAndMaterialDataMap = Maps.newHashMap();
        materialTypeAndIdsMap.forEach((k, v) -> {
            MaterailDataManager materailDataManager = materailDataManagerFactory.get(k);
            if (materailDataManager != null) {
                List<AbstractMaterialData> materialDataList = materailDataManager.get(ea, ListUtil.toArray(v, String[].class));
                if (materialDataList!=null) {
                    materialDataList.forEach(val -> {
                        materialTypeAndIdAndMaterialDataMap.computeIfAbsent(val.getObjectType(), k1 -> Maps.newHashMap());
                        materialTypeAndIdAndMaterialDataMap.get(val.getObjectType()).put(val.getId(), val);
                    });
                }
            }
        });
        List<WxMarketingActivityResult> data = new ArrayList<>();
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = null;
        Set<Integer> creatorIds = marketingActivityDataList.stream().map(ObjectData::getCreateBy).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(creatorIds)){
            fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, new ArrayList<>(creatorIds), false);
        }
        if (fsEmployeeMsgMap == null) {
            fsEmployeeMsgMap = new HashMap<>();
        }
        FSEmployeeMsg systemFsEmployeeMsg = new FSEmployeeMsg();
        systemFsEmployeeMsg.setEmployeeId(-10000);
        systemFsEmployeeMsg.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
        fsEmployeeMsgMap.put(systemFsEmployeeMsg.getEmployeeId(), systemFsEmployeeMsg);
        for (ObjectData objectData : marketingActivityDataList) {
            MarketingActivityData marketingActivityData = MarketingActivityData.wrap(objectData);
            WxMarketingActivityResult wxMarketingActivityResult = new WxMarketingActivityResult();
            data.add(wxMarketingActivityResult);
            Integer createdBy = marketingActivityData.getCreateBy();
            String creatorName = fsEmployeeMsgMap.get(createdBy) == null ? deafultName : fsEmployeeMsgMap.get(createdBy).getName();
            wxMarketingActivityResult.setCreateById(String.valueOf(createdBy));
            wxMarketingActivityResult.setCreateByName(creatorName);
            wxMarketingActivityResult.setName(marketingActivityData.getName());
            wxMarketingActivityResult.setId(marketingActivityData.getId());
            wxMarketingActivityResult.setStatus(marketingActivityData.getStatus());
            wxMarketingActivityResult.setMarketingEventId(marketingActivityData.getMarketingEventId());
            ActivityEntity conference = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, marketingActivityData.getMarketingEventId());
            if (conference != null) {
                wxMarketingActivityResult.setMarketingObjectId(conference.getId());
            }
            wxMarketingActivityResult.setMarketingEventName(marketingActivityData.getMarketingEventIdName());
            wxMarketingActivityResult.setSpreadType(marketingActivityData.getSpreadType());
            //NeedSendCount
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityIdToEntityMap.get(marketingActivityData.getId());
            if (marketingActivityExternalConfigEntity == null) {
                continue;
            }
            if (SendStatusEnum.isSendActionHandled(marketingActivityData.getStatus()) && marketingActivityExternalConfigEntity != null
                    && marketingActivityExternalConfigEntity.getAssociateId() != null) {
                String wxNoticeId = marketingActivityExternalConfigEntity.getAssociateId();
                NoticeDetailResult noticeDetailResult = noticeDetailResultMap.get(wxNoticeId);
                if (noticeDetailResult != null) {
                    wxMarketingActivityResult.setNeedSendCount(noticeDetailResult.getNeedSendCount());
                }
                if (noticeDetailResult != null) {
                    wxMarketingActivityResult.setReadCount(noticeDetailResult.getReadCount());
                }
                if (noticeDetailResult != null) {
                    wxMarketingActivityResult.setActualCompletedCount(noticeDetailResult.getActualCompletedCount());
                }
                if (noticeDetailResult != null && noticeDetailResult.getNeedSendCount() != null && noticeDetailResult.getActualCompletedCount() != null) {
                    wxMarketingActivityResult.setFailSendCount(noticeDetailResult.getNeedSendCount() - noticeDetailResult.getActualCompletedCount());
                }
            }
            wxMarketingActivityResult.setWechatMessageType(marketingActivityExternalConfigEntity.getAssociateIdType());
            if (marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType()) {
                WeChatServiceMarketingActivityData weChatServiceMarketingActivityData = marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO();
                Long sendTime = weChatServiceMarketingActivityData.getFixedTime();
                if (sendTime == null) {
                    sendTime = marketingActivityData.getCreateTime();
                }
                wxMarketingActivityResult.setCreateTime(marketingActivityData.getCreateTime());
                wxMarketingActivityResult.setSendTime(sendTime);
                wxMarketingActivityResult.setContent(weChatServiceMarketingActivityData.getContent());
                wxMarketingActivityResult.setMsgType(weChatServiceMarketingActivityData.getMsgType());
                wxMarketingActivityResult.setSendRange(weChatServiceMarketingActivityData.getSendRange());
                wxMarketingActivityResult.setAppId(weChatServiceMarketingActivityData.getAppId());
                NoticeDetailResult noticeDetailResult = noticeDetailResultMap.get(marketingActivityExternalConfigEntity.getAssociateId());
                if (noticeDetailResult != null) {
                    wxMarketingActivityResult.setSendCancelable(noticeDetailResult.getStatus() != null && com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus() == noticeDetailResult.getStatus());
                    if(noticeDetailResult.getStatus()==SendStatusEnum.FAIL.getStatus()){
                        if(Strings.isNullOrEmpty(noticeDetailResult.getReason())){
                            wxMarketingActivityResult.setReason(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8));
                        }else{
                            wxMarketingActivityResult.setReason(noticeDetailResult.getReason());
                        }
                    }
                }
                wxMarketingActivityResult.setGraphicMessageTitle(weChatServiceMarketingActivityData.getGraphicMessageTitle());
                wxMarketingActivityResult.setGraphicMessagePic(weChatServiceMarketingActivityData.getGraphicMessagePic());
            } else if (marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType()) {
                WeChatTemplateMessageData weChatTemplateMessageData = marketingActivityExternalConfigEntity.getExternalConfig().getWeChatTemplateMessageVO();
                Long sendTime = weChatTemplateMessageData.getFixedTime();
                if (sendTime == null) {
                    sendTime = marketingActivityData.getCreateTime();
                }
                wxMarketingActivityResult.setCreateTime(marketingActivityData.getCreateTime());
                wxMarketingActivityResult.setSendTime(sendTime);
                wxMarketingActivityResult.setContent(weChatTemplateMessageData.getMsgContent());
                wxMarketingActivityResult.setMaterialId(weChatTemplateMessageData.getMaterialId());
                wxMarketingActivityResult.setMaterialType(weChatTemplateMessageData.getMaterialType());
                wxMarketingActivityResult.setRedirectType(weChatTemplateMessageData.getRedirectType());
                wxMarketingActivityResult.setRedirectUrl(weChatTemplateMessageData.getRedirectUrl());
                wxMarketingActivityResult.setMiniAppId(weChatTemplateMessageData.getMiniAppId());
                wxMarketingActivityResult.setMiniAppPagePath(weChatTemplateMessageData.getMiniAppPagePath());
                wxMarketingActivityResult.setNPath(weChatTemplateMessageData.getNPath());
                wxMarketingActivityResult.setWeChatOfficialTemplateId(weChatTemplateMessageData.getWeChatOfficialTemplateId());
                wxMarketingActivityResult.setSendRange(weChatTemplateMessageData.getSendRange());
                NoticeDetailResult noticeDetailResult = noticeDetailResultMap.get(marketingActivityExternalConfigEntity.getAssociateId());
                if (noticeDetailResult != null) {
                    wxMarketingActivityResult.setContent(noticeDetailResult.getContent());
                    wxMarketingActivityResult.setSendCancelable(noticeDetailResult.getStatus() != null && com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus() == noticeDetailResult.getStatus());
                    if(noticeDetailResult.getStatus()==SendStatusEnum.FAIL.getStatus()){
                        if(Strings.isNullOrEmpty(noticeDetailResult.getReason())){
                            wxMarketingActivityResult.setReason(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8));
                        }else{
                            wxMarketingActivityResult.setReason(noticeDetailResult.getReason());
                        }
                    }
                }
                if (materialTypeAndIdAndMaterialDataMap.get(weChatTemplateMessageData.getMaterialType()) != null
                        && materialTypeAndIdAndMaterialDataMap.get(weChatTemplateMessageData.getMaterialType()).get(weChatTemplateMessageData.getMaterialId()) != null) {
                    wxMarketingActivityResult
                            .setAbstractMaterialData(materialTypeAndIdAndMaterialDataMap.get(weChatTemplateMessageData.getMaterialType()).get(weChatTemplateMessageData.getMaterialId()));
                }
            }
            wxMarketingActivityResult.setAuditStatus(objectData.getLifeStatus());
        }
        Set<String> marketingActivityIds =  data.stream().map(WxMarketingActivityResult::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if(!marketingActivityIds.isEmpty()){
            Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(ea, marketingActivityIds).getData();
            Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(ea, marketingActivityIds).getData();
            Map<String, Integer> leadCountMap = customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(ea, new ArrayList<>(marketingActivityIds), null, null, false).stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getMarketingActivityId, CustomizeFormClueNumDTO::getCount, (v1, v2) -> v1));
            data.forEach(d -> {
                d.setUv(uvMap.get(d.getId()));
                d.setPv(pvMap.get(d.getId()));
                d.setLeadCount(leadCountMap.get(d.getId()));
            });
        }
//        List<WxMarketingActivityResult> marketingList = null;
//        if (CollectionUtils.isNotEmpty(data)){
//            marketingList = data.stream().filter(marketingActivityResult->marketingActivityResult.getSpreadType()!=8).collect(Collectors.toList());
//        }
//        List<WxMarketingActivityResult> resultList = marketingList;
        PageResult<WxMarketingActivityResult> pageResult = new PageResult<>();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(count);
        //pageResult.setResult(resultList);
        pageResult.setResult(data);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<MarketingActivityResult>> listMarketingActivity(String ea, Integer fsUserId, ListMarketingActivityArg arg) {
        List<MarketingActivityResult> marketingActivityResultList = new ArrayList<>();
        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setPageSize(arg.getPageSize());
        //这里pageNumber实际是offset， pageSize是limit
        marketingActivityArg.setPageNumber((arg.getPageNum() - 1) * arg.getPageSize());
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        marketingActivityArg.setName(arg.getName());
        marketingActivityArg.setMarketingEventName(arg.getMarketingEventName());
        marketingActivityArg.setMarketingEventId(arg.getMarketingEventId());
        marketingActivityArg.setStatus(arg.getStatus());
        marketingActivityArg.setSpreadType(arg.getSpreadType());
        marketingActivityArg.setIds(arg.getIds());
        marketingActivityArg.setBeginTime(arg.getBeginTime());
        marketingActivityArg.setEndTime(arg.getEndTime());
        marketingActivityArg.setIsMankeep(arg.getIsMankeep());
        marketingActivityArg.setOwnerIds(arg.getOwnerIds());
        marketingActivityArg.setIsAsc(arg.getIsAsc());
        marketingActivityArg.setSortFieldName(arg.getSortFieldName());
        marketingActivityArg.setSpreadTypes(arg.getSpreadTypes());
        Page<ObjectData> marketingActivityListVo = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (CollectionUtils.isNotEmpty(marketingActivityListVo.getDataList())) {
            marketingActivityResultList = buildResultFromCrmMarketingActivityData(ea, marketingActivityListVo);
        }
        for (MarketingActivityResult result : marketingActivityResultList) {
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity =  marketingActivityExternalConfigDao.getByMarketingActivityId(result.getId());
            if(marketingActivityExternalConfigEntity!=null){
                if (marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType()) {
                    WeChatServiceMarketingActivityData vo1 = marketingActivityExternalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO();
                    result.setGraphicMessagePic(vo1.getGraphicMessagePic());
                    result.setGraphicMessageTitle(vo1.getGraphicMessageTitle());
                }
            }

        }
        PageResult<MarketingActivityResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
     //   pageResult.setResult(resultList);
        pageResult.setResult(marketingActivityResultList);
        pageResult.setTotalCount(marketingActivityListVo.getTotal());
        return Result.newSuccess(pageResult);
    }


    @Override
    public Result<PageResult<MarketingActivityResult>> listMarketingActivityByMarketingUserGroupId(String ea, Integer fsUserId, ListMarketingActivityByMarketingUserGroupIdArg arg) {
        List<MarketingActivityResult> marketingActivityResultList = new ArrayList<>();
        Preconditions.checkArgument(arg.getPageNo() != null && arg.getPageNo() > 0);
        Preconditions.checkArgument(arg.getPageSize() != null && arg.getPageSize() > 0);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getMarketingUserGroupId()));
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNo(), arg.getPageSize(), true);
        List<String> marketingActivityIds = marketingActivityExternalConfigDao.listMarketingActivityIdsByMarketingUserGroupId(ea, arg.getMarketingUserGroupId(), page);

        if (marketingActivityIds != null && !marketingActivityIds.isEmpty()){
            PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
            marketingActivityArg.setPageSize(marketingActivityIds.size());
            //这里pageNumber实际是offset， pageSize是limit
            marketingActivityArg.setPageNumber(0);
            marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
            marketingActivityArg.setIds(marketingActivityIds);
            Page<ObjectData> marketingActivityListVo = marketingCrmManager.listMarketingActivity(ea, -10000, marketingActivityArg);
            if (CollectionUtils.isNotEmpty(marketingActivityListVo.getDataList())) {
                marketingActivityResultList = buildResultFromCrmMarketingActivityData(ea, marketingActivityListVo);
            }
        }
        PageResult<MarketingActivityResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNo());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(marketingActivityResultList);
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }

    private List<MarketingActivityResult> buildResultFromCrmMarketingActivityData(String ea, Page<ObjectData> marketingActivityListVo) {
        List<MarketingActivityResult> marketingActivityResultList = new LinkedList<>();
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = null;
        Set<Integer> creatorIds = marketingActivityListVo.getDataList().stream().map(ObjectData::getCreateBy).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(creatorIds)){
            fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, new ArrayList<>(creatorIds), false);
        }
        if (fsEmployeeMsgMap == null) {
            fsEmployeeMsgMap = new HashMap<>();
        }
        FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
        fsEmployeeMsg.setEmployeeId(-10000);
        fsEmployeeMsg.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
        fsEmployeeMsgMap.put(fsEmployeeMsg.getEmployeeId(), fsEmployeeMsg);
        for (ObjectData objectData : marketingActivityListVo.getDataList()) {
            MarketingActivityData marketActivityData = MarketingActivityData.wrap(objectData);
            MarketingActivityResult marketingActivityResult = new MarketingActivityResult();
            marketingActivityResult.setId(marketActivityData.getId());
            marketingActivityResult.setName(marketActivityData.getName());
            marketingActivityResult.setMarketingEventId(marketActivityData.getMarketingEventId());
            marketingActivityResult.setMarketingEventName(marketActivityData.getMarketingEventIdName());
            marketingActivityResult.setSpreadType(marketActivityData.getSpreadType());
            marketingActivityResult.setStatus(marketActivityData.getStatus());
            marketingActivityResult.setCreateTime(marketActivityData.getCreateTime());
            //目前权限未开通，当前显示操作人的信息
            Integer createdBy = marketActivityData.getCreateBy();
            marketingActivityResult.setCreateByName(fsEmployeeMsgMap.get(createdBy) == null ? deafultName : fsEmployeeMsgMap.get(createdBy).getName());
            marketingActivityResult.setCreateById(String.valueOf(createdBy));
            //影响用户数
            if (SendStatusEnum.isSendActionHandled(marketActivityData.getStatus())) {
                marketingActivityResult.setEffectUserCount(getEffectUserCount(objectData.getId(),ea));
            } else {
                marketingActivityResult.setEffectUserCount(0);
            }
            //默认营销活动为不可取消
            marketingActivityResult.setSendCancelable(false);
            if(marketActivityData.getSpreadType() != null && MarketingActivitySpreadTypeEnum.WECHAT_SERVICE.getSpreadType() == marketActivityData.getSpreadType()){
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketActivityData.getId());
                if (marketingActivityExternalConfigEntity != null){
                    GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByMarketingActivityId(marketingActivityExternalConfigEntity.getMarketingActivityId());
                    if (getNoticeResult != null && getNoticeResult.getStatus() != null && com.facishare.wechat.sender.api.enums.SendStatusEnum.WAIT_FOR_SENDING.getStatus() == getNoticeResult.getStatus()){
                        marketingActivityResult.setSendCancelable(true);
                    }
                }
            }
            //如果为短信营销，判断短信是否可以取消
            if (marketActivityData.getSpreadType() != null && marketActivityData.getSpreadType().equals(MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType())) {
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketActivityData.getId());
                if (marketingActivityExternalConfigEntity != null){
                    QuerySMSSendResult data = sendService.getSMSSendById(marketingActivityExternalConfigEntity.getAssociateId()).getData();
                    if (data != null && MwSendTaskTypeEnum.SCHEDULE_SEND.getType().equals(data.getType()) && MwSendStatusEnum.SCHEDULE_SENDING.getStatus().equals(data.getStatus())) {
                        marketingActivityResult.setSendCancelable(true);
                    }
                }
            }
            //邮件营销
            if (marketActivityData.getSpreadType() != null && marketActivityData.getSpreadType().equals(MarketingActivitySpreadTypeEnum.MAIL_GROUP_SEND.getSpreadType())) {
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketActivityData.getId());
                if (marketingActivityExternalConfigEntity != null) {
                    MailSendTaskEntity mailSendTaskEntity = mailSendTaskDAO.getById(marketingActivityExternalConfigEntity.getAssociateId());
                    if (mailSendTaskEntity != null) {
                        marketingActivityResult.setObjectId(mailSendTaskEntity.getId());
                        marketingActivityResult.setObjectType(ObjectTypeEnum.MAIL_TASK.getType());
                        marketingActivityResult.setSendStatus(mailSendTaskEntity.getSendStatus());
                        if (mailSendTaskEntity.getScheduleType().equals(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType())) {
                            marketingActivityResult.setSendTime(mailSendTaskEntity.getCreateTime().getTime());
                        } else {
                            marketingActivityResult.setSendTime(mailSendTaskEntity.getFixTime());
                        }
                        marketingActivityResult.setSendCancelable(mailManager.checkSendCancelable(marketingActivityExternalConfigEntity.getAssociateId()));
                        if (StringUtils.isNotEmpty(mailSendTaskEntity.getMarketingGroupUserIds())) {
                            marketingActivityResult.setMarketingGroupUser(mailManager.getMarketingGroupUserInfo(ea, mailSendTaskEntity.getMarketingGroupUserIds()));
                        }
                        marketingActivityResult.setSendRange(mailSendTaskEntity.getSendRange());
                    }
                }
            }
            //企业微信群发
            if (marketActivityData.getSpreadType() != null && marketActivityData.getSpreadType().equals(MarketingActivitySpreadTypeEnum.QYWX_GROUP_SEND.getSpreadType())) {
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketActivityData.getId());
                if (marketingActivityExternalConfigEntity != null) {
                    marketingActivityResult.setSendCancelable(qywxGroupSendManager.checkSendCancelable(marketingActivityExternalConfigEntity.getAssociateId()));
                }
            }
            //全员营销
            if (marketActivityData.getSpreadType() != null && marketActivityData.getSpreadType().equals(MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType())) {
                MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketActivityData.getId());
                if (marketingActivityExternalConfigEntity != null) {
                    NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId());
                    if (noticeEntity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus() && noticeEntity.getSendType() == NoticeSendTypeEnum.TIMING.getType()){
                        marketingActivityResult.setSendCancelable(true);
                    }else {
                        marketingActivityResult.setSendCancelable(false);
                    }
                }
            }

            marketingActivityResultList.add(marketingActivityResult);
        }
        return marketingActivityResultList;
    }

    private Integer getEffectUserCount(String marketingActivityId,String ea) {
        Integer effectUserCount = 0;
        MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (externalConfigEntity == null) {
            return effectUserCount;
        }
        switch (AssociateIdTypeEnum.matchOpCode(externalConfigEntity.getAssociateIdType())) {
            case MANKEEP_SPREAD_NOTICE:
                /**全员推广 ->影响用户数=活动推广关联营销物料的访问人数*/
                effectUserCount = getNoticeLookUpUserCount(externalConfigEntity.getMarketingActivityId(),ea);
                break;
            case WECHAT_SERVICE_SEND_MESSAGE:
                /**服务号->影响用户数=服务号群发消息发送成功数*/
                effectUserCount = doGetWechatServiceNoticeCount(externalConfigEntity.getEa(), externalConfigEntity.getExternalConfig().getWeChatServiceMarketingActivityVO().getAppId(),
                        Integer.valueOf(externalConfigEntity.getAssociateId()));
                break;
            case MANKEEP_SEND_MESSAGE:
                /**短信推广->影响用户数=短信发送成功用户数*/
                effectUserCount = getSMSSendSuccessUserCount(externalConfigEntity.getAssociateId());
                break;
            case MANKEEP_PRODUCT:
                /**其它 ->活动推广关联营销物料的访问人数*/
                effectUserCount = getNoticeLookUpUserCount(externalConfigEntity.getMarketingActivityId(),ea);
                break;
            case MANKEEP_ARTICLE:
                /**其它 ->活动推广关联营销物料的访问人数*/
                effectUserCount = getNoticeLookUpUserCount(externalConfigEntity.getMarketingActivityId(),ea);
                break;
            case MANKEEP_ACTIVITY:
                /**其它 ->活动推广关联营销物料的访问人数*/
                effectUserCount = getNoticeLookUpUserCount(externalConfigEntity.getMarketingActivityId(),ea);
                break;
            default:
                break;
        }
        return effectUserCount;
    }

    /***
     * 获取全员营销用户影响数
     * @param marketingActivityId  营销活动ID
     * @return 活动推广关联营销物料的访问人数
     * */
    private Integer getNoticeLookUpUserCount(String marketingActivityId,String ea) {
        MarketingActivityStatisticEntity marketingActivityStatisticEntity = marketingActivityStatisticDao.queryByMarketingActivityIdAndEa(marketingActivityId,ea);
        return marketingActivityStatisticEntity != null ? marketingActivityStatisticEntity.getLookUpUserCount() : new Integer(0);
    }

    /***
     * 获取服务号营销用户影响数
     * @return 发送成功数
     * */
    private Integer doGetWechatServiceNoticeCount(String ea, String wxAppId, Integer wechatNoticeId) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        GetNoticeResult getNoticeResult = weChatServiceNoticeManager.findByIdAndEiAndAppId(wechatNoticeId, ei, wxAppId);
        return getNoticeResult != null && getNoticeResult.getActualCompletedCount() != null ? getNoticeResult.getActualCompletedCount() : new Integer(0);
    }

    /***
     * 获取短信营销用户影响数
     * @param associateId   短信ID
     * @return 发送成功用户数
     * */
    private Integer getSMSSendSuccessUserCount(String associateId) {
        Result<QuerySMSSendResult> querySMSSendResultResult = sendService.getSMSSendById(associateId);
        return querySMSSendResultResult.getData() != null ? querySMSSendResultResult.getData().getActualSenderCount() : 0;
    }

    @Override
    public Result<AddMarketingActivityResult> addMarketingActivity(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg, boolean fromWeb) {
        if (addMarketingActivityArg.getWeChatTemplateMessageVO() != null && addMarketingActivityArg.getWeChatTemplateMessageVO().getMaterialType() != null) {
            addMarketingActivityArg.getWeChatTemplateMessageVO().setMaterialType(NoticeContentTypeEnum.fromType(addMarketingActivityArg.getWeChatTemplateMessageVO().getMaterialType()).toObjectType());
        }

        Preconditions.checkNotNull(addMarketingActivityArg.getSpreadType(), "spreadType");
        Boolean hasMaterialInfos = CollectionUtils.isNotEmpty(addMarketingActivityArg.getMaterialInfos());
        if (hasMaterialInfos) {
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, addMarketingActivityArg.getMarketingEventId());
            AddMaterialsArg addMaterialsArg = new AddMaterialsArg();
            addMaterialsArg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
            addMaterialsArg.setEventType(marketingEventData.getEventType());
            addMaterialsArg.setMaterialInfos(Lists.newArrayList());
            for (AddMarketingActivityArg.MaterialInfo val : addMarketingActivityArg.getMaterialInfos()) {
                Integer contentType = val.getContentType();
                if (contentType == null) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
                Integer objectType = NoticeContentTypeEnum.fromType(contentType).toObjectType();
                addMaterialsArg.getMaterialInfos().add(new AddMaterialsArg.MaterialInfo(objectType, val.getObjectId()));
            }
            marketingEventService.checkAndAddMaterials(ea, fsUserId, addMaterialsArg);
        }

        if (addMarketingActivityArg.getMarketingActivityNoticeSendVO() != null) {
            if (StringUtils.isNotEmpty(addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath())) {
                if (addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath().startsWith("TA_")) {
                    String fileEa = null;
                    Integer fileUserId = null;
                    if (fromWeb) {
                        fileEa = ea;
                        fileUserId = fsUserId;
                    }

                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager
                            .getApathByTApath(addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath(), fileEa, fileUserId);
                    if (!EmptyUtil.isNullForList(fileManagerPicResult, fileManagerPicResult.getUrlAPath())) {
                        addMarketingActivityArg.getMarketingActivityNoticeSendVO().setCoverPath(fileManagerPicResult.getUrlAPath());
                    }
                } else if (addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath().startsWith("N_")) {
                    String apath = fileV2Manager.getApathByNpath(addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath(), ea);
                    if (!EmptyUtil.isNullForList(apath)) {
                        addMarketingActivityArg.getMarketingActivityNoticeSendVO().setCoverPath(apath);
                    }
                } else if (addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath().startsWith("A_")){
                    String apath = fileV2Manager.getApathByApath(addMarketingActivityArg.getMarketingActivityNoticeSendVO().getCoverPath(), ea, ea);
                    if (!EmptyUtil.isNullForList(apath)){
                        addMarketingActivityArg.getMarketingActivityNoticeSendVO().setCoverPath(apath);
                    }
                }
            }
        }

        //通知发送VO
        AddMarketingActivityArg.MarketingActivityPartnerNoticeSendVO partnerNoticeSendVO = addMarketingActivityArg.getMarketingActivityPartnerNoticeSendVO();
        if (partnerNoticeSendVO != null) {
            if (StringUtils.isNotEmpty(partnerNoticeSendVO.getCoverPath())) {
                if (partnerNoticeSendVO.getCoverPath().startsWith("TA_")) {
                    String fileEa = null;
                    Integer fileUserId = null;
                    if (fromWeb) {
                        fileEa = ea;
                        fileUserId = fsUserId;
                    }
                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(partnerNoticeSendVO.getCoverPath(), fileEa, fileUserId);
                    if (!EmptyUtil.isNullForList(fileManagerPicResult, fileManagerPicResult.getUrlAPath())) {
                        partnerNoticeSendVO.setCoverPath(fileManagerPicResult.getUrlAPath());
                    }
                } else if (partnerNoticeSendVO.getCoverPath().startsWith("N_")) {
                    String apath = fileV2Manager.getApathByNpath(partnerNoticeSendVO.getCoverPath(), ea);
                    if (!EmptyUtil.isNullForList(apath)) {
                        partnerNoticeSendVO.setCoverPath(apath);
                    }
                } else if (partnerNoticeSendVO.getCoverPath().startsWith("A_")) {
                    String apath = fileV2Manager.getApathByApath(partnerNoticeSendVO.getCoverPath(), ea, ea);
                    if (!EmptyUtil.isNullForList(apath)) {
                        partnerNoticeSendVO.setCoverPath(apath);
                    }
                }
            }
        }

        MarketingActivityActionService marketingActivityActionService = marketingActivityActionManager.getMarketingActivityAction(MarketingActivityActionEnum.getActionName(addMarketingActivityArg.getSpreadType()));
        if (marketingActivityAuditManager.isNeedAudit(ea)) {
            CountDownLatch countDownLatch = new CountDownLatch(1);
            new NamedThreadFactory("getMarketingActivityAuditData").newThread(() -> {
                AddMarketingActivityArg.MarketingActivityAuditData activityAuditData = addMarketingActivityArg.getMarketingActivityAuditData();
                try {
                    AddMarketingActivityArg.MarketingActivityAuditData marketingActivityAuditData = marketingActivityActionService.getMarketingActivityAuditData(ea, fsUserId, addMarketingActivityArg);
                    if (activityAuditData != null) {
                        BeanUtil.copyPropertiesIgnoreNull(marketingActivityAuditData, activityAuditData);
                        addMarketingActivityArg.setMarketingActivityAuditData(activityAuditData);
                    } else {
                        addMarketingActivityArg.setMarketingActivityAuditData(marketingActivityAuditData);
                    }
                } catch (Exception e) {
                    log.warn("MarketingActivityServiceImpl getMarketingActivityAuditData error params:{}", addMarketingActivityArg);
                } finally {
                    countDownLatch.countDown();
                }
            }).start();
            try {
                countDownLatch.await(25L, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("MarketingActivityServiceImpl getMarketingActivityAuditData out time params:{}", addMarketingActivityArg);
            }
        }
        AddMarketingActivityResult result = marketingActivityActionService.doAddAction(ea, fsUserId, addMarketingActivityArg);
        if (result != null && result.getCrmErrorCode() != null){
            return Result.newError(result.getCrmErrorCode(), result.getCrmErrorMsg());
        }

        if (result != null && result.getErrorCode() != null) {
            return Result.newError(result.getErrorCode());
        }
        if (result == null || StringUtils.isEmpty(result.getMarketingActivityId())) {
            return Result.newError(SHErrorCode.KIS_MARKETING_ACTIVITY_ADD_FAILED);
        }
        String marketingActivityId = result.getMarketingActivityId();
        if (hasMaterialInfos) {
            addMarketingActivityAndMarketingEventAndObjectRelation(ea, addMarketingActivityArg, marketingActivityId);
        }

        //推广发送到神策数据统计
        if (result !=null && StringUtils.isNotEmpty(result.getMarketingActivityId())){
            //神策埋点：营销活动实例
            MarketingActivityInstanceLogManager.MarketingActivityInstanceLog marketingActivityInstanceLog = new MarketingActivityInstanceLogManager.MarketingActivityInstanceLog(ea, fsUserId, addMarketingActivityArg.getMarketingEventId(), result.getMarketingActivityId(), addMarketingActivityArg.getMaterialInfos() == null ? 0 : addMarketingActivityArg.getMaterialInfos().size(), addMarketingActivityArg.getSpreadType());
            marketingActivityInstanceLogManager.sendPersistLog(marketingActivityInstanceLog);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<AddMarketingActivityArg.MarketingActivityAuditData> getMarketingActivityAuditData(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg) {
        if (StringUtils.isEmpty(addMarketingActivityArg.getMarketingEventId()) || addMarketingActivityArg.getSpreadType() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AddMarketingActivityArg.MarketingActivityAuditData marketingActivityAuditData = new AddMarketingActivityArg.MarketingActivityAuditData();
        String lockKey = ea + fsUserId + addMarketingActivityArg.getMarketingEventId() + addMarketingActivityArg.getSpreadType();
        try {
            boolean lock = redisManager.lock(lockKey, 60);
            if (lock) {
                MarketingActivityActionService marketingActivityActionService = marketingActivityActionManager.getMarketingActivityAction(MarketingActivityActionEnum.getActionName(addMarketingActivityArg.getSpreadType()));
                marketingActivityAuditData = marketingActivityActionService.getMarketingActivityAuditData(ea, fsUserId, addMarketingActivityArg);
            } else {
                return Result.newError(SHErrorCode.SERVER_BUSY);
            }
        } catch (Exception e) {
            log.warn("MarketingActivityServiceImpl.getMarketingActivityAuditData error", e);
        } finally {
            redisManager.unLock(lockKey);
        }
        return Result.newSuccess(marketingActivityAuditData);
    }

    private void addMarketingActivityAndMarketingEventAndObjectRelation(String ea, AddMarketingActivityArg addMarketingActivityArg, String marketingActivityId) {
        if(addMarketingActivityArg == null || addMarketingActivityArg.getMaterialInfos() == null || addMarketingActivityArg.getMaterialInfos().isEmpty()){
            return;
        }
        List<ContentMarketingEventMaterialRelationEntity.ObjectInfo> objectInfos = addMarketingActivityArg.getMaterialInfos()
                .stream()
                .map(val ->
                        new ContentMarketingEventMaterialRelationEntity.ObjectInfo(
                                addMarketingActivityArg.getMarketingEventId()
                                , NoticeContentTypeEnum.fromType(val.getContentType()).toObjectType()
                                , val.getObjectId()
                        )
                ).collect(Collectors.toList());
        List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities = contentMarketingEventMaterialRelationDAO.batchGetByEaAndObjectInfos(ea, objectInfos);
        List<MarketingActivityObjectRelationEntity> entities = contentMarketingEventMaterialRelationEntities.stream().map(val ->
                new MarketingActivityObjectRelationEntity(
                        UUIDUtil.getUUID()
                        , ea
                        , val.getMarketingEventId()
                        , marketingActivityId
                        , val.getObjectType()
                        , val.getObjectId()
                )
        ).collect(Collectors.toList());
        if(entities.isEmpty()){
            return;
        }
        marketingActivityObjectRelationDAO.batchAdd(entities);
    }

    @Override
    public Result<GetMarketingActivityResult> getMarketingActivity(String ea, Integer fsUserId, GetMarketingActivityArg getMarketingActivityArg) {
        //从CRM获取营销活动基础数据
        GetMarketingActivityDetailVo getMarketingActivityDetailVo = marketingCrmManager.getByIdMarketingActivity(ea, fsUserId, getMarketingActivityArg.getId());
        AssertUtil.checkNotNull(getMarketingActivityDetailVo, "getMarketingActivityDetailVo    query ");
        MarketingActivityActionService marketingActivityActionService = marketingActivityActionManager
                .getMarketingActivityAction(MarketingActivityActionEnum.getActionName(getMarketingActivityDetailVo.getSpreadType()));
        //营销活动详情
        GetMarketingActivityResult result = marketingActivityActionService.doDetailAction(ea, fsUserId, BeanUtil.copyByGson(getMarketingActivityDetailVo, GetMarketingActivityDetailData.class));

        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        marketingActivityArg.setIds(Lists.newArrayList(result.getId()));
        marketingActivityArg.setPageNumber(0);
        marketingActivityArg.setPageSize(1);
        //从CRM批量查询营销活动数据
        Page<ObjectData> objectDataPage = marketingCrmManager.listMarketingActivity(ea, -10000, marketingActivityArg);
        if (objectDataPage.getDataList() != null && objectDataPage.getDataList().size() != 0 && objectDataPage.getDataList().get(0) != null) {
            MarketingActivityData marketingActivityData = MarketingActivityData.wrap(objectDataPage.getDataList().get(0));
            result.setAuditStatus(marketingActivityData.getLifeStatus());
            if (StringUtils.isNotEmpty(marketingActivityData.getMarketingEventId())) {
                //市场活动数据
                MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, marketingActivityData.getMarketingEventId());
                //市场活动类型
                result.setEventType(marketingEventData != null ? marketingEventData.getEventType() : null);
            }
            result.setMarketingEventId(marketingActivityData.getMarketingEventId());
            result.setMarketingEventName(marketingActivityData.getMarketingEventIdName());
            //创建者id
            Integer createdBy = marketingActivityData.getCreateBy();
            if (createdBy != null){
                Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(createdBy), false);
                result.setCreateByName(fsEmployeeMsgMap.get(createdBy) == null ? deafultName : fsEmployeeMsgMap.get(createdBy).getName());
                result.setCreateById(String.valueOf(createdBy));
            }
        }
        if(result.getId() != null){
            Set<String> marketingActivityIds = Sets.newHashSet(result.getId());
            //uv(访问人数)
            Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(ea, marketingActivityIds).getData();
            //pv(访问次数)
            Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(ea, marketingActivityIds).getData();
            //线索数
            Map<String, Integer> leadCountMap = customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(ea, new ArrayList<>(marketingActivityIds), null, null, false).stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getMarketingActivityId, CustomizeFormClueNumDTO::getCount, (v1, v2) -> v1));
            //未推广员工数
            int uuv = customizeFormDataUserDAO.countMarketingActivityUncompleteTaskEmployeeds(ea, getMarketingActivityArg.getId());
            result.setUv(uvMap.get(result.getId()));
            result.setPv(pvMap.get(result.getId()));
            result.setLeadCount(leadCountMap.get(result.getId()));
            result.setUuv(uuv);
        }
        //公众号高级群发和模板群发特殊处理
        if(!Strings.isNullOrEmpty(getMarketingActivityArg.getAppid())){
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(getMarketingActivityArg.getId());
            if(externalConfigEntity!=null&&externalConfigEntity.getAssociateId()!=null&&(externalConfigEntity.getAssociateIdType()==WechatMessageTypeEnum.WECHAT_SERVICE.getType()||externalConfigEntity.getAssociateIdType()==WechatMessageTypeEnum.WECHAT_TEMPLATE_MESSAGE.getType())){
                NoticeBaseVo noticeBaseVo = new NoticeBaseVo();
                noticeBaseVo.setAppId(getMarketingActivityArg.getAppid());
                noticeBaseVo.setCorpId((long) eieaConverter.enterpriseAccountToId(ea));
                noticeBaseVo.setId(Integer.valueOf(externalConfigEntity.getAssociateId()));
                com.facishare.wechat.union.common.result.Result<NoticeDetailResult> noticeDetailResult = noticeService.findById(noticeBaseVo);
                if (!noticeDetailResult.isSuccess()) {
                    log.warn("getMarketingActivity noticeService.findById fail ,result = {}", noticeDetailResult);
                    if (Strings.isNullOrEmpty(noticeDetailResult.getErrorMessage())) {
                        result.setReason(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8));
                    } else {
                        result.setReason(noticeDetailResult.getErrorMessage());
                    }
                }
            }
        }

        if(MarketingActivityActionEnum.getAction(getMarketingActivityDetailVo.getSpreadType()) == MarketingActivityActionEnum.ALL_SPREAD
                || MarketingActivityActionEnum.getAction(getMarketingActivityDetailVo.getSpreadType()) == MarketingActivityActionEnum.PARTNER_SPREAD
                || MarketingActivityActionEnum.getAction(getMarketingActivityDetailVo.getSpreadType()) == MarketingActivityActionEnum.MEMBER_MARKETING) {
            Long nowDate = new Date().getTime();
            Integer spreadType = getMarketingActivityDetailVo.getSpreadType();
            if (Objects.equals(MarketingActivityActionEnum.ALL_SPREAD.getSpreadType(), spreadType) || Objects.equals(MarketingActivityActionEnum.MEMBER_MARKETING.getSpreadType(), spreadType)) {
                MarketingActivityNoticeSendVO entity = result.getMarketingActivityNoticeSendVO();
                setSpreadStatus(result, entity.getStartTime(), nowDate, entity.getEndTime());
            } else if (Objects.equals(MarketingActivityActionEnum.PARTNER_SPREAD.getSpreadType(), spreadType)) {
                MarketingActivityPartnerNoticeSendVO entity = result.getMarketingActivityPartnerNoticeSendVO();
                setSpreadStatus(result, entity.getStartTime(), nowDate, entity.getEndTime());
            }
        }

        return Result.newSuccess(result);
    }

    private void setSpreadStatus(GetMarketingActivityResult result, Long startTime, Long nowDate, Long endTime) {
        result.setSendRevocable(true);
        if (result.getStatus() == NoticeStatusEnum.UN_SEND.getStatus()) {
            result.setSpreadStatus(SpreadStatusEnum.UN_SEND.getStatus());
        } else if (result.getStatus() == NoticeStatusEnum.SENDING.getStatus()) {
            result.setSpreadStatus(SpreadStatusEnum.SENDING.getStatus());
        } else if (startTime > nowDate && result.getStatus() == NoticeStatusEnum.SUCCESS.getStatus()) {
            result.setSpreadStatus(SpreadStatusEnum.UN_STARTED.getStatus());
        } else if (startTime < nowDate && endTime > nowDate && result.getStatus() != NoticeStatusEnum.REVOCATION.getStatus()) {
            result.setSpreadStatus(SpreadStatusEnum.SUCCESS.getStatus());
        } else if (result.getStatus() == NoticeStatusEnum.REVOCATION.getStatus()) {
            result.setSendRevocable(false);
            result.setSpreadStatus(SpreadStatusEnum.REVOCATION.getStatus());
        } else if (endTime < nowDate && result.getStatus() != NoticeStatusEnum.REVOCATION.getStatus()) {
            result.setSpreadStatus(SpreadStatusEnum.END.getStatus());
        }
    }

    @Override
    public Result<UpdateMarketingActivityResult> updateMarketingActivity(String ea, Integer fsUserId, UpdateMarketingActivityArg updateMarketingActivityArg) {
        GetMarketingActivityDetailVo getMarketingActivityDetailVo = marketingCrmManager.getByIdMarketingActivity(ea, fsUserId, updateMarketingActivityArg.getId());
        PaasUpdateMarketingActivityArg arg = new PaasUpdateMarketingActivityArg();
        arg.setMarketingEventId(updateMarketingActivityArg.getMarketingEventId());
        arg.setId(arg.getMarketingEventId());
        arg.setName(getMarketingActivityDetailVo.getName());
        arg.setSpreadType(String.valueOf(getMarketingActivityDetailVo.getSpreadType()));
        arg.setStatus(String.valueOf(getMarketingActivityDetailVo.getStatus()));
        String marketingActivityId = marketingCrmManager.updateMarketingActivity(ea, fsUserId, arg);
        return Result.newSuccess(new UpdateMarketingActivityResult(marketingActivityId));
    }

    @Override
    public Result<Void> updateBaseInfo(UpdateMarketingActivityBaseInfoArg arg) {
        String marketingActivityId = arg.getMarketingActivityId();
        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (configEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String noticeId = configEntity.getAssociateId();
        // notice表更新
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        noticeEntity.setTitle(arg.getTitle());
        noticeEntity.setDescription(arg.getDescription());
        noticeEntity.setTimingTime(Date.from(Instant.ofEpochMilli(arg.getTimingDate())));
        noticeEntity.setStartTime(Date.from(Instant.ofEpochMilli(arg.getStartTime())));
        noticeEntity.setEndTime(Date.from(Instant.ofEpochMilli(arg.getEndTime())));
        noticeDAO.updateNotice(noticeEntity);
        // marketing_activity_external_config表更新
        int associateIdType = configEntity.getAssociateIdType();
        if (AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() == associateIdType || AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType() == associateIdType) {
            // 员工营销和会员营销
            MarketingActivityNoticeSendData marketingActivityNoticeSendData = configEntity.getExternalConfig().getMarketingActivityNoticeSendVO();
            marketingActivityNoticeSendData.setTitle(arg.getTitle());
            marketingActivityNoticeSendData.setDescription(arg.getDescription());
            marketingActivityNoticeSendData.setTimingDate(arg.getTimingDate());
            marketingActivityNoticeSendData.setStartTime(arg.getStartTime());
            marketingActivityNoticeSendData.setEndTime(arg.getEndTime());
            configEntity.getExternalConfig().setMarketingActivityNoticeSendVO(marketingActivityNoticeSendData);
            marketingActivityExternalConfigDao.update(configEntity);
        } else if (AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType() == associateIdType) {
            // 伙伴营销
            MarketingActivityPartnerNoticeSendData marketingActivityPartnerNoticeSendData = configEntity.getExternalConfig().getMarketingActivityPartnerNoticeSendData();
            marketingActivityPartnerNoticeSendData.setTitle(arg.getTitle());
            marketingActivityPartnerNoticeSendData.setDescription(arg.getDescription());
            marketingActivityPartnerNoticeSendData.setTimingDate(arg.getTimingDate());
            marketingActivityPartnerNoticeSendData.setStartTime(arg.getStartTime());
            marketingActivityPartnerNoticeSendData.setEndTime(arg.getEndTime());
            configEntity.getExternalConfig().setMarketingActivityPartnerNoticeSendData(marketingActivityPartnerNoticeSendData);
            marketingActivityExternalConfigDao.update(configEntity);
        }

        // 更新营销活动对象
        ObjectData objectData = new ObjectData();
        objectData.put("name", arg.getTitle());
        objectData.put("_id", marketingActivityId);
        crmV2Manager.updateMarketingActivityObj(arg.getEa(), objectData, SuperUserConstants.USER_ID);
        return Result.newSuccess();
    }

    @Override
    public Result<Integer> initCrmMarketingActivityByOldEa() {
        List<String> list = enterpriseMetaConfigDao.findEaAll();
        log.info("initCrmMarketingActivityByOldEa  executor  size , {},  ea,{} ", list.size(), list);
        list.forEach(value -> {
            marketingCrmManager.initMarketingActivity(value);
        });
        return Result.newSuccess(list.size());
    }

    @Override
    public Result<Integer> getEaInitCrmMarketingActivity(String ea) {
        marketingCrmManager.initMarketingActivity(ea);
        return Result.newSuccess(1);
    }

    @Override
    public Result<Boolean> cancelSend(String ea, Integer userId,String marketingActivityId) {
        boolean cancelResult = false;
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null){
            return Result.newSuccess(false);
        }
        if (Strings.isNullOrEmpty(marketingActivityExternalConfig.getAssociateId())){
            return Result.newSuccess(false);
        }

        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType() || marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType()){
            com.facishare.wechat.union.common.result.Result<Boolean> result = noticeService.cancelSendNotice(Integer.valueOf(marketingActivityExternalConfig.getAssociateId()));
            cancelResult = BooleanUtils.isTrue(result.getData());
        }
        //取消短信相关
        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType()) {
            Result<Boolean> cancelSendSmsResult = sendService.cancelSendSms(marketingActivityExternalConfig.getAssociateId(), ea);
            cancelResult = BooleanUtils.isTrue(cancelSendSmsResult.getData());
        }
        //取消全员推广 伙伴推广
        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType()||marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType()){
            Result<Boolean>  cancleSpreadResult = allSpreadNoticeService.cancelSendSpread(marketingActivityExternalConfig.getAssociateId(), ea);
            cancelResult = BooleanUtils.isTrue(cancleSpreadResult.getData());
        }
        //取消邮件发送
        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType()){
            Result<Boolean> cancelSendMailResult = mailManager.cancelSend(marketingActivityExternalConfig.getAssociateId(), ea);
            cancelResult = BooleanUtils.isTrue(cancelSendMailResult.getData());
        }
        //企业微信群发
        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType()){
            Result<Boolean> cancelSendQywxGroupMsgResult =  qywxGroupSendManager.cancelSend(marketingActivityExternalConfig.getAssociateId());
            cancelResult = BooleanUtils.isTrue(cancelSendQywxGroupMsgResult.getData());
        }
        //朋友圈发送
        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MOMENT_SEND_ACTIVITY.getType()){
            cancelSpreadActivity activity = new cancelSpreadActivity();
            activity.setMarketingActivityId(marketingActivityId);
            activity.setStatus(SendStatusEnum.CANCELED.getStatus());
            activity.setSpreadType(MarketingActivitySpreadTypeEnum.QYWX_MOMENT_TASK.getSpreadType());
            Result<Boolean> booleanResult = momentPublishManager.doCancelAction(ea, userId, activity);
            return booleanResult;
        }
        if(cancelResult){
            marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, SendStatusEnum.CANCELED.getStatus() + "");
        }

        return Result.newSuccess(cancelResult);
    }

    @Override
    public Result<Boolean> revokeSend(String ea, String marketingActivityId) {
        boolean revokeResult = false;
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null) {
            return Result.newSuccess(false);
        }
        if (Strings.isNullOrEmpty(marketingActivityExternalConfig.getAssociateId())) {
            return Result.newSuccess(false);
        }
        TransactionStatus transaction = TransactionUtils.begin(transactionManager);
        try {
            //撤回全员推广 伙伴推广
            if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() || marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType() || marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType()) {
                Result<Boolean> revokeSpreadResult = allSpreadNoticeService.revokeSendSpread(marketingActivityExternalConfig.getAssociateId(), ea, marketingActivityExternalConfig.getMarketingActivityId());
                revokeResult = BooleanUtils.isTrue(revokeSpreadResult.getData());
            }
            if (revokeResult) {
                marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, SendStatusEnum.REVOCATION.getStatus() + "");
            }
            TransactionUtils.commit(transactionManager, transaction);
        } catch (Exception e) {
            log.warn("revokeSend updateMarketingActivityStatus fail");
            TransactionUtils.rollback(transactionManager, transaction);
            return Result.newError(SHErrorCode.REVOCATION_FAIL);
        }

        return Result.newSuccess(revokeResult);
    }

    @Override
    public Result<Boolean> updateMarketingActivityStatus(String ea, String marketingActivityId, String marketingActivityStatus) {
        marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, marketingActivityStatus);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> invalidMarketingActivityByIds(InvalidMarketingActivityByIdsArg arg) {
        marketingActivityManager.invalidMarketingActivityByIds(arg);
        return Result.newSuccess();
    }
    

    @Override
    @Transactional
    public Result<Void> deleteMarketingActivity(String ea, Integer fsUserId, String marketingActivityId) {
    /*
          GetMarketingActivityDetailVo marketingActivityDetail = marketingCrmManager.getByIdMarketingActivity(ea, fsUserId, marketingActivityId);
        if (marketingActivityDetail == null){
            log.info("MarketingActivityServiceImpl.deleteMarketingActivity getMarketingActivity data not exist ea:{} marketingActivityId:{}", ea, marketingActivityId);
            return Result.newError(SHErrorCode.MARKETING_ACTIVITY_DATA_NOT_EXIST);
        }
        if (marketingActivityDetail.getStatus() == SendStatusEnum.PROCESSING.getStatus()){
            log.info("MarketingActivityServiceImpl.deleteMarketingActivity getMarketingActivity status is processing, cannot be deleted ea:{} marketingActivityId:{}", ea, marketingActivityId);
            return Result.newError(SHErrorCode.PROCESSING_MARKETINGACTIVITY_CANNOT_BE_DELETED);
        }
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null) {
            log.info("MarketingActivityServiceImpl.deleteMarketingActivity marketingActivity data not exist ea:{} marketingActivityId:{}", ea, marketingActivityId);
            return Result.newError(SHErrorCode.MARKETING_ACTIVITY_DATA_NOT_EXIST);
        }

        Integer spreadType = null;
        if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType()) {
            spreadType = MarketingActivityActionEnum.ALL_SPREAD.getSpreadType();
        } else if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE.getType()) {
            spreadType = MarketingActivityActionEnum.SEND_NOTE.getSpreadType();
        } else if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getType()
                || marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE.getType()) {
            spreadType = MarketingActivityActionEnum.WECHAT_SERVICE.getSpreadType();
        } else if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.QYWX_GROUP_SEND_MESSAGE.getType()) {
            spreadType = MarketingActivityActionEnum.QYWX_GROUP_SEND.getSpreadType();
        } else if (marketingActivityExternalConfig.getAssociateIdType() == AssociateIdTypeEnum.MAIL_SEND_ACTIVITY.getType()) {
            spreadType = MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType();
        }
        if (spreadType == null) {
            log.info("MarketingActivityServiceImpl.deleteMarketingActivity marketingActivity type error ea:{} marketingActivityId:{} spreadType:{}", ea, marketingActivityId, marketingActivityExternalConfig.getAssociateIdType());
            return Result.newError(SHErrorCode.MARKETING_ACTIVITY_TYPE_ERROR);
        }

        try {
            crmV2Manager.bulkInvalid(ea, -10000, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), Lists.newArrayList(marketingActivityExternalConfig.getMarketingActivityId()));
        } catch (Exception e) {
            log.info("MarketingActivityServiceImpl.deleteMarketingActivity invalid marketingActivity failed ea:{} marketingActivityId:{}", ea, marketingActivityId);
            return Result.newError(SHErrorCode.INVALID_MARKETING_ACTIVITY_DATA_FAILED);
        }
        MarketingActivityActionService marketingActivityActionService = marketingActivityActionManager
                .getMarketingActivityAction(MarketingActivityActionEnum.getActionName(spreadType));
        Result<Boolean> result = marketingActivityActionService.doDeleteAction(ea, fsUserId, marketingActivityId);
        if (!result.isSuccess()) {
            log.info("MarketingActivityServiceImpl.deleteMarketingActivity doDeleteAction failed ea:{} marketingActivityId:{} spreadType:{}", ea, marketingActivityId, spreadType);
            return Result.newError(SHErrorCode.INVALID_MARKETING_ACTIVITY_DATA_FAILED);
        }

        marketingActivityObjectRelationDAO.deleteByMarketingActivityId(marketingActivityId, ea);
        marketingActivityExternalConfigDao.deleteById(marketingActivityExternalConfig.getId());
        customizeFormDataUserDAO.deleteByMarketingActivityId(marketingActivityId);
        marketingActivityDayStatisticDAO.deleteMarketingActivitySpreadRecordByMarketingActivityId(marketingActivityId, ea);
        marketingActivityDayStatisticDAO.deleteMarketingActivityEmployeeStatisticByMarketingActivityId(marketingActivityId, ea);
        marketingActivityStatisticDao.deleteByMarketingActivityId(marketingActivityId, ea);
        marketingActivityDayStatisticDAO.deleteMarketingActivityEmployeeDayStatisticByMarketingActivityId(marketingActivityId, ea);
        marketingActivityDayStatisticDAO.deleteByMarketingActivityId(marketingActivityId, ea);

    */
        return Result.newSuccess();
    }

    @Override
    public Result<Void> spreadMarketingActivityToSpecialEmployeeAgain(String ea, Integer userId, String marketingActivityId) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null){
            return Result.newSuccess();
        }
        if (marketingActivityExternalConfig.getAssociateIdType() != AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType()){
            log.info("spreadMarketingActivityToSpecialEmployeeAgain suport only spread notice type ea:{} marketingActivityId:{} type:{}", ea, marketingActivityId, marketingActivityExternalConfig.getAssociateIdType());
            return Result.newSuccess();
        }
        return allSpreadNoticeService.spreadMarketingActivityToSpecialEmployeeAgain(ea, userId, marketingActivityExternalConfig.getAssociateId(), marketingActivityId);
    }

    @Override
    public Result<Void> spreadPartnerActivityToSpecialEmployeeAgain(String ea, Integer userId, String marketingActivityId) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null){
            return Result.newSuccess();
        }
        if (marketingActivityExternalConfig.getAssociateIdType() != AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType()){
            log.info("spreadPartnerActivityToSpecialEmployeeAgain suport only spread notice type ea:{} marketingActivityId:{} type:{}", ea, marketingActivityId, marketingActivityExternalConfig.getAssociateIdType());
            return Result.newSuccess();
        }
        try {
            allSpreadNoticeService.partnerNoticeAgain(ea,userId, marketingActivityExternalConfig.getAssociateId(),marketingActivityId);
        } catch (Exception e) {
            log.info("spreadPartnerActivityTbushi oSpecialEmployeeAgain suport only spread notice type ea:{} marketingActivityId:{} type:{}", ea, marketingActivityId, marketingActivityExternalConfig.getAssociateIdType());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> spreadMomentActivityToSpecialEmployeeAgain(String ea, Integer userId, String marketingActivityId) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null){
            return Result.newSuccess();
        }
        if (marketingActivityExternalConfig.getAssociateIdType() != AssociateIdTypeEnum.MOMENT_SEND_ACTIVITY.getType()){
            log.info("spreadMomentActivityToSpecialEmployeeAgain suport only spread notice type ea:{} marketingActivityId:{} type:{}", ea, marketingActivityId, marketingActivityExternalConfig.getAssociateIdType());
            return Result.newSuccess();
        }
        momentManager.noticeEmploy( ea, userId,  marketingActivityId);
        return Result.newSuccess();
    }

    @Override
    public Result<GetSendNotificationStatisticResult> getSendNotificationStatistic(String ea, Integer fsUserId, GetSendNotificationStatisticArg arg) {
        if (!MarketingActivityActionEnum.isValid(arg.getSpreadType()) || CollectionUtils.isEmpty(arg.getCampaignIds())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (MarketingActivityActionEnum.WECHAT_SERVICE.getSpreadType().equals(arg.getSpreadType())) {
            Optional<Map<String, UserMarketingWxServiceAccountRelationEntity>> userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional = userMarketingAccountManager.getUserMarketingWxServiceAccountRelationEntitiesByCampaignIds(ea, arg.getWxAppId(), arg.getCampaignIds());
            if (userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional.isPresent() && MapUtils.isNotEmpty(userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional.get())) {
                Map<String, UserMarketingWxServiceAccountRelationEntity> campaignId2UserMarketingWxServiceAccountRelationEntityMap = userMarketingWxServiceAccountRelationEntitiesByCampaignIdsOptional.get();
                List<String> campaignIds = new LinkedList<>();
                for (Map.Entry<String, UserMarketingWxServiceAccountRelationEntity> entry : campaignId2UserMarketingWxServiceAccountRelationEntityMap.entrySet()) {
                    if (entry.getValue() != null && StringUtils.isNotEmpty(entry.getValue().getWxOpenId())) {
                        campaignIds.add(entry.getKey());
                    }
                }
                GetSendNotificationStatisticResult result = new GetSendNotificationStatisticResult(campaignIds.size(), campaignIds);
                return Result.newSuccess(result);
            }
            log.warn("MarketingActivityServiceImpl.getSendNotificationStatistic optional is empty ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newSuccess(new GetSendNotificationStatisticResult(0, null));
        } else if (MarketingActivityActionEnum.SEND_NOTE.getSpreadType().equals(arg.getSpreadType())) {
            List<CampaignMergeDataEntity> campaignMergeDataByIds = campaignMergeDataDAO.getCampaignMergeDataByIds(arg.getCampaignIds());
            if (CollectionUtils.isNotEmpty(campaignMergeDataByIds)) {
                List<String> campaignIds = new LinkedList<>();
                for (CampaignMergeDataEntity entity : campaignMergeDataByIds) {
                    if (StringUtils.isNotEmpty(entity.getPhone())) {
                        campaignIds.add(entity.getId());
                    }
                }
                GetSendNotificationStatisticResult result = new GetSendNotificationStatisticResult(campaignIds.size(), campaignIds);
                return Result.newSuccess(result);
            }
            log.warn("MarketingActivityServiceImpl.getSendNotificationStatistic campaignMergeDataByIds is empty ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newSuccess(new GetSendNotificationStatisticResult(0, null));
        } else if (MarketingActivityActionEnum.MAIL_SERVICE.getSpreadType().equals(arg.getSpreadType())) {
            List<String> campaignIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(arg.getCampaignIds())) {
                Map<String, String> userMailDataMap = campaignMergeDataManager.getUserEmailByCampaignId(ea, arg.getCampaignIds());
                for (Map.Entry<String, String> entry : userMailDataMap.entrySet()) {
                    if (mailManager.getValidMail(entry.getValue())) {
                        campaignIds.add(entry.getKey());
                    }
                }
            }
            GetSendNotificationStatisticResult result = new GetSendNotificationStatisticResult(campaignIds.size(), campaignIds);
            return Result.newSuccess(result);
        }
        return Result.newError(SHErrorCode.PARAMS_ERROR);
    }

    @Override
    public Result<BatchGetSendNotificationStatisticResult> batchGetSendNotificationStatistic(String ea, Integer fsUserId, BatchGetSendNotificationStatisticArg arg) {
        if (arg == null || !MarketingActivityActionEnum.isValid(arg.getSpreadTypes())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        BatchGetSendNotificationStatisticResult result = new BatchGetSendNotificationStatisticResult();
        List<BatchGetSendNotificationStatisticData> statisticData = new LinkedList<>();
        result.setStatisticData(statisticData);
        for (Integer spreadType : arg.getSpreadTypes()) {
            GetSendNotificationStatisticArg getSendNotificationStatisticArg = new GetSendNotificationStatisticArg(arg.getCampaignIds(), spreadType, arg.getWxAppId());
            BatchGetSendNotificationStatisticData data = new BatchGetSendNotificationStatisticData();
            data.setSpreadType(spreadType);
            Result<GetSendNotificationStatisticResult> sendNotificationStatisticResult = getSendNotificationStatistic(ea, fsUserId, getSendNotificationStatisticArg);
            if (!sendNotificationStatisticResult.isSuccess() || sendNotificationStatisticResult.getData() == null) {
                log.warn("MarketingActivityServiceImpl.batchGetSendNotificationStatistic is error spreadType:{} arg:{}", spreadType, arg);
                data.setTotal(-1);
                data.setCampaignIds(null);
            } else {
                data.setTotal(sendNotificationStatisticResult.getData().getTotal());
                data.setCampaignIds(sendNotificationStatisticResult.getData().getCampaignIds());
            }
            statisticData.add(data);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> getMarketingActivityDescription(String marketingActivityId, boolean partner) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfigEntity != null) {
            ExternalConfig externalConfig = marketingActivityExternalConfigEntity.getExternalConfig();
            if (externalConfig != null) {
                if (!partner) {
                    MarketingActivityNoticeSendData marketingActivityNoticeSendVO = externalConfig.getMarketingActivityNoticeSendVO();
                    if (marketingActivityNoticeSendVO != null) {
                        return Result.newSuccess(marketingActivityNoticeSendVO.getDescription());
                    }
                } else {
                    MarketingActivityPartnerNoticeSendData marketingActivityPartnerNoticeSendData = externalConfig.getMarketingActivityPartnerNoticeSendData();
                    if (marketingActivityPartnerNoticeSendData != null) {
                        return Result.newSuccess(marketingActivityPartnerNoticeSendData.getDescription());
                    }
                }
            }
        } else {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess();
    }


    public Result<AddMarketingActivityResult> updateMarketingActivityDetail(String ea, Integer fsUserId, AddMarketingActivityArg addMarketingActivityArg, boolean fromWeb) {
        if (addMarketingActivityArg.getWeChatTemplateMessageVO() != null && addMarketingActivityArg.getWeChatTemplateMessageVO().getMaterialType() != null) {
            addMarketingActivityArg.getWeChatTemplateMessageVO().setMaterialType(NoticeContentTypeEnum.fromType(addMarketingActivityArg.getWeChatTemplateMessageVO().getMaterialType()).toObjectType());
        }
        Preconditions.checkNotNull(addMarketingActivityArg.getSpreadType(), "spreadType");
        Boolean hasMaterialInfos = CollectionUtils.isNotEmpty(addMarketingActivityArg.getMaterialInfos());
        if (hasMaterialInfos) {
            MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, addMarketingActivityArg.getMarketingEventId());
            AddMaterialsArg addMaterialsArg = new AddMaterialsArg();
            addMaterialsArg.setMarketingEventId(addMarketingActivityArg.getMarketingEventId());
            addMaterialsArg.setEventType(marketingEventData.getEventType());
            addMaterialsArg.setMaterialInfos(Lists.newArrayList());
            addMarketingActivityArg.getMaterialInfos().forEach(val -> {
                Integer objectType = NoticeContentTypeEnum.fromType(val.getContentType()).toObjectType();
                addMaterialsArg.getMaterialInfos().add(new AddMaterialsArg.MaterialInfo(objectType, val.getObjectId()));
            });
            marketingEventService.checkAndAddMaterials(ea, fsUserId, addMaterialsArg);
        }

        MarketingActivityActionService marketingActivityActionService = marketingActivityActionManager
                .getMarketingActivityAction(MarketingActivityActionEnum.getActionName(addMarketingActivityArg.getSpreadType()));
        AddMarketingActivityResult result = marketingActivityActionService.doUpdateAction(ea, fsUserId, addMarketingActivityArg);
        if (result != null && result.getCrmErrorCode() != null){
            return Result.newError(result.getCrmErrorCode(), result.getCrmErrorMsg());
        }

        if (result != null && result.getErrorCode() != null) {
            return Result.newError(result.getErrorCode());
        }
        if (result == null || StringUtils.isEmpty(result.getMarketingActivityId())) {
            return Result.newError(SHErrorCode.KIS_MARKETING_ACTIVITY_UPDATE_FAILED);
        }
        String marketingActivityId = result.getMarketingActivityId();
        if (hasMaterialInfos) {
            addMarketingActivityAndMarketingEventAndObjectRelation(ea, addMarketingActivityArg, marketingActivityId);
        }
        return Result.newSuccess(result);
    }

    @Override
    public void wechatServiceNoticeSchedule() {
        weChatServiceManager.wechatServiceNoticeSchedule();
    }

    @Override
    public Result<Boolean> immediatelySend(String ea, String marketingActivityId) {
        MarketingActivityExternalConfigEntity marketingActivityExternalConfig = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
        if (marketingActivityExternalConfig == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AssociateIdTypeEnum associateIdType = AssociateIdTypeEnum.matchOpCode(marketingActivityExternalConfig.getAssociateIdType());
        String associateId = marketingActivityExternalConfig.getAssociateId();
        String marketingEventId = marketingActivityExternalConfig.getMarketingEventId();
        switch (associateIdType) {
            case MANKEEP_SPREAD_NOTICE://全员推广
            case MEMBER_MARKETING_SPREAD://会员营销
                noticeSendService.sendNoticeById(associateId, marketingActivityId, marketingEventId);
                break;
            case PARTNER_SPREAD_NOTICE://伙伴推广
                noticeSendService.sendPartnerNoticeById(associateId, marketingActivityId);
                break;
            case MOMENT_SEND_ACTIVITY://朋友圈
                qywxMomentService.qywxMomentInvokeTaskById(associateId);
                break;
            case QYWX_GROUP_SEND_MESSAGE://企微群发
                groupSendMessageManager.groupSendMsgById(associateId);
                break;
            case WECHAT_SERVICE_SEND_MESSAGE://企微高级群发
            case WECHAT_SERVICE_SEND_TEMPLATE_MESSAGE://企微模板消息
                weChatServiceManager.handlerSendTask(marketingActivityExternalConfig);
                break;
            case MANKEEP_SEND_MESSAGE://短信
                smsTaskSchedulerManager.mwSmsSendTaskById(associateId);
                break;
            case MAIL_SEND_ACTIVITY://邮件
                MailSendTaskEntity task = mailSendTaskDAO.getById(associateId);
                mailManager.handlerSendTask(task);
                break;
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<MarketingActivityPreviewData> getPreviewData(String ea, Integer fsUserId, MarketingActivityPreviewArg arg) {
        if (arg.getSpreadType() == null ) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        MarketingActivityActionService marketingActivityActionService = marketingActivityActionManager.getMarketingActivityAction(MarketingActivityActionEnum.getActionName(arg.getSpreadType()));
        return marketingActivityActionService.getPreviewData(ea, fsUserId, arg);
    }
}