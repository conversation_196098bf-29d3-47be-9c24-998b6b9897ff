/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.conference;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.data.excel.ScopeContainer;
import com.facishare.marketing.api.result.ImportUserDataResult;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.contstant.ImportConstants;
import com.facishare.marketing.common.enums.ActivitySignOrEnrollEnum;
import com.facishare.marketing.common.enums.AreaTypeEnum;
import com.facishare.marketing.common.enums.CustomizeFormDataUserSourceTypeEnum;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.ImportErrorEnum;
import com.facishare.marketing.common.enums.ImportResultTypeEnum;
import com.facishare.marketing.common.enums.ImportTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollSourceTypeEnum;
import com.facishare.marketing.common.enums.conference.ConferenceInviteNoticeStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceInviteStatusEnum;
import com.facishare.marketing.common.enums.excel.ExcelScopeEnum;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.typehandlers.value.FieldInfo;
import com.facishare.marketing.common.typehandlers.value.FieldInfo.Option;
import com.facishare.marketing.common.typehandlers.value.FieldInfo.Type;
import com.facishare.marketing.common.typehandlers.value.FieldInfoList;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.ActivityEnrollDataDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.ImportDataRecordDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceInviteParticipantDAO;
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.ImportDataRecordEntity;
import com.facishare.marketing.provider.entity.conference.ConferenceInviteParticipantEntity;
import com.facishare.marketing.provider.manager.AreaManager;
import com.facishare.marketing.provider.manager.AreaManager.AreaContainer;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.CustomizeTicketManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.SpreadChannelManager;
import com.facishare.marketing.provider.util.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created  By zhoux 2019/10/14
 **/
@Component
@Slf4j
public class ImportManager {

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private ImportDataRecordDAO importDataRecordDAO;

    @Autowired
    private ConferenceInviteParticipantDAO inviteParticipantDAO;

    @Autowired
    private CustomizeTicketManager customizeTicketManager;

    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private AreaManager areaManager;

    private String regexEmail = "\\w+@\\w+(\\.\\w{2,3})*\\.\\w{2,3}";
    private Pattern emailPattern = Pattern.compile(regexEmail);
    private String regexPhone = "^((13[0-9])|(14[0-9])|(15([0-3]|[5-9]))|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[0-9])|(16[0-9]))\\d{8}$";
    private Pattern phonePattern = Pattern.compile(regexPhone);
    private static int INVITE_NAME_LINE_NUM = 0;
    private static int INVITE_PHONE_LINE_NUM = 1;
    private static int INVITE_EMAIL_LINE_NUM = 2;
    private static int INVITE_COMPANY_LINE_NUM = 3;
    private static int INVITE_POSITION_LINE_NUM = 4;
    private static int INVITE_EMPLOYEE_LINE_NUM = 5;

    private static int INVITE_MAX_COL_LIMIT = 6;


    /**
     * 检验导入数据title
     */
    public List<FieldInfo> checkConferenceImportTitleData(List<String> titleList, CustomizeFormDataEntity customizeFormDataEntity) {

        List<FieldInfo> titleFieldResult = Lists.newArrayList();

        FieldInfoList formBodySetting = customizeFormDataEntity.getFormBodySetting();

        List<String> labelList = formBodySetting.stream().map(FieldInfo::getLabel).collect(Collectors.toList());

        // 求差集（若存在差集则存在数据差异）
        List<String> differenceList = labelList.stream().filter(data -> titleList.stream().noneMatch(title -> title.replaceAll(I18nUtil.getStaticByKey(ImportConstants.REQUIRED), "").equals(data))).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(differenceList) || titleList.size() - ImportConstants.NON_CUSTOMIZE_FIELD != labelList.size()) {
            log.warn("ImportManager.checkConferenceImportTitleData error titleList:{}, customizeFormDataEntity:{}", titleList, customizeFormDataEntity);
            return null;
        }

        Map<String, FieldInfo> labelMap = formBodySetting.stream().collect(Collectors.toMap(FieldInfo::getLabel, data -> data, (v1, v2) -> v1));

        for (String title : titleList) {
            if (labelMap.get(title.replaceAll(I18nUtil.getStaticByKey(ImportConstants.REQUIRED), "")) != null) {
                titleFieldResult.add(labelMap.get(title.replaceAll(I18nUtil.getStaticByKey(ImportConstants.REQUIRED), "")));
            }
        }
        // 自定义字段
        // 分组
        FieldInfo groupUser = new FieldInfo();
        groupUser.setApiName(ImportConstants.IMPORT_GROUP_API_NAME);
        groupUser.setType(ImportConstants.IMPORT_CUSTOMIZE_TYPE);
        groupUser.setIsRequired(false);
        groupUser.setIsVerify(false);
        groupUser.setLabel(I18nUtil.getStaticByKey(ImportConstants.IMPORT_GROUP_LABEL));

        titleFieldResult.add(groupUser);
        return titleFieldResult;

    }

    public List<FieldInfo> checkFormImportTitleData(List<String> titleList, CustomizeFormDataEntity customizeFormDataEntity, List<FieldInfo> customField) {
        List<FieldInfo> titleFieldResult = Lists.newArrayList();
        FieldInfoList formBodySetting = customizeFormDataEntity.getFormBodySetting();
        List<String> labelList = formBodySetting.stream().map(FieldInfo::getLabel).collect(Collectors.toList());
        List<String> differenceList = labelList.stream().filter(data -> titleList.stream().noneMatch(title -> title.replaceAll(I18nUtil.getStaticByKey(ImportConstants.REQUIRED), "").equals(data)))
            .collect(Collectors.toList());
        Integer notFormFieldNum = customField.size() + 1;
        if (CollectionUtils.isNotEmpty(differenceList) || titleList.size() - notFormFieldNum != labelList.size()) {
            log.warn("ImportManager.checkFormImportTitleData error differenceList:{}  titleList:{}, titleSize:{}, notFormFieldNum:{} labelList:{} labelListSize:{} customizeFormDataEntity:{}",
                    differenceList, titleList, titleList.size(), notFormFieldNum, labelList, labelList.size(), customizeFormDataEntity);
            return null;
        }

        titleFieldResult.addAll(formBodySetting);
        titleFieldResult.addAll(customField);
        // 最后校验字段与导入文件字段是否相同(防止重复列)
        if (titleFieldResult.size() + 1 != titleList.size()) {
            log.warn("ImportManager.checkFormImportTitleData error titleFieldResult:{},  titleFieldResultSize:{}  titleList:{}  titleListSize:{}", titleFieldResult, titleFieldResult.size() , titleList, titleList.size());
            return null;
        }
        return titleFieldResult;
    }

    public List<ImportLineResultContainer> saveFormEnrollData(ImportLineArgContainer arg) {
        List<ImportLineResultContainer> importResultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(arg.getExcelData()) && arg.getExcelData().size() > 1) {
            AreaContainer areaContainer = areaManager.buildAreaData();
            // 逐行取出数据
            for (int row = 1; row < arg.getExcelData().size(); row++) {
                Map<String, String> customFieldsMap = Maps.newHashMap();
                ImportErrorEnum importErrorEnum = null;
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                String[] lineDataList = arg.getExcelData().get(row);
                if (isAllFormRowEmpty(row, lineDataList)) {
                    continue;
                }
                ImportLineResultContainer importResultContainer = new ImportLineResultContainer();
                // 遍历每列
                for (int column = 0; column < lineDataList.length - 1; column++) {
                    importErrorEnum = checkLineData(arg.getTitleField().get(column), lineDataList[column], areaContainer);
                    if (importErrorEnum == ImportErrorEnum.SUCCESS) {
                        Object value = buildColumnData(arg.getTitleField().get(column), lineDataList[column], areaContainer);
                        customizeFormDataEnroll.setByFieldName(arg.getTitleField().get(column).getApiName(), value);
                        // 处理自定义字段
                        handlingCustomFields(arg.getEa(), arg.getTitleField().get(column), lineDataList[column], customFieldsMap);
                    } else {
                        break;
                    }
                }
                if (importErrorEnum == ImportErrorEnum.SUCCESS) {
                    saveFormDataAndEnrollData(customizeFormDataEnroll, arg, importResultContainer, customFieldsMap);
                } else {
                    importResultContainer.setImportDesc(importErrorEnum == null ? ImportErrorEnum.SYSTEM_ERROR.getDesc() : importErrorEnum.getDesc());
                }
                importResultList.add(importResultContainer);
            }
            areaContainer = null;
        }
        return importResultList;
    }

    /**
     * 保存会议报名数据
     */
    public List<ImportLineResultContainer> saveConferenceEnrollData(List<FieldInfo> titleField, List<String[]> excelData, String formId, String activityId, String marketingEventId, String ea, Integer fsUserId) {
        List<ImportLineResultContainer> importResultList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(excelData) && excelData.size() > 1) {
            // 取出手机集合
            List<String> phoneList = getAllConferenceEnrollPhone(titleField, excelData, activityId);
            // 逐行将数据取出
            for (int row = 1; row < excelData.size(); row++) {
                Map<String, String> customFieldsMap = Maps.newHashMap();
                ImportErrorEnum importErrorEnum = null;
                CustomizeFormDataEnroll customizeFormDataEnroll = new CustomizeFormDataEnroll();
                String[] lineDataList = excelData.get(row);
                if (isAllRowEmpty(row, lineDataList)) {
                    continue;
                }
                ImportLineResultContainer importResultContainer = new ImportLineResultContainer();
                for (int column = 0; column < lineDataList.length - 1; column++) {
                    // 字段校验
                    importErrorEnum = checkLineData(titleField.get(column), lineDataList[column], phoneList);

                    // 字段转换
                    if (importErrorEnum == ImportErrorEnum.SUCCESS) {
                        Object value = buildColumnData(titleField.get(column), lineDataList[column]);
                        customizeFormDataEnroll.setByFieldName(titleField.get(column).getApiName(), value);
                        // 处理自定义字段
                        handlingCustomFields(ea, titleField.get(column), lineDataList[column], customFieldsMap);
                    } else {
                        break;
                    }
                }
                // 处理每行数据
                if (importErrorEnum == ImportErrorEnum.SUCCESS) {
                    saveFormDataAndEnrollData(customizeFormDataEnroll, formId, activityId, importResultContainer, marketingEventId, ea, fsUserId, customFieldsMap);
                } else {
                    importResultContainer.setImportDesc(importErrorEnum == null ? ImportErrorEnum.SYSTEM_ERROR.getDesc() : importErrorEnum.getDesc());
                }
                importResultList.add(importResultContainer);
            }
        }
        return importResultList;
    }


    private void handlingCustomFields(String ea, FieldInfo fieldInfo, String data, Map<String, String> customFieldsMap) {
        if (fieldInfo == null) {
            return;
        }
        if (fieldInfo.getApiName().equals(ImportConstants.IMPORT_GROUP_API_NAME)) {
            customFieldsMap.put(ImportConstants.IMPORT_GROUP_API_NAME, data);
        } else if (fieldInfo.getApiName().equals(ImportConstants.IMPORT_CHANNEL_API_NAME)) {
            Map<String, String> channelMapData = spreadChannelManager.queryChannelMapData(ea, false);
            String value = channelMapData.get(data);
            if (StringUtils.isBlank(value) && StringUtils.isNotBlank(data)) {
                customFieldsMap.put(ImportConstants.IMPORT_CHANNEL_API_NAME, "other:" + data);
            } else if (StringUtils.isNotBlank(value) && StringUtils.isNotBlank(data)) {
                customFieldsMap.put(ImportConstants.IMPORT_CHANNEL_API_NAME, value);
            }
        }
    }

    private boolean isAllRowEmpty(int row, String[] lineDataList) {
        if (row <= ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION.size()) {
            return Arrays.stream(lineDataList).filter(StringUtils::isBlank).count() == lineDataList.length - 1;
        } else {
            return Arrays.stream(lineDataList).filter(StringUtils::isBlank).count() == lineDataList.length;
        }
    }

    public boolean isAllFormRowEmpty(int row, String[] lineDataList) {
        if (row <= ImportConstants.IMPORT_FORM_ENROLL_DESCRIPTION.size()) {
            return Arrays.stream(lineDataList).filter(StringUtils::isBlank).count() == lineDataList.length - 1;
        } else {
            return Arrays.stream(lineDataList).filter(StringUtils::isBlank).count() == lineDataList.length;
        }
    }

    private List<String> getAllConferenceEnrollPhone(List<FieldInfo> titleField, List<String[]> excelData, String activityId) {
        List<String> phoneList = Lists.newArrayList();
        phoneList.addAll(activityEnrollDataDAO.getEnrollDataPhoneByActivityId(activityId));
        return phoneList.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    /**
     * 保存表单报名数据
     */
    private void saveFormDataAndEnrollData(CustomizeFormDataEnroll customizeFormDataEnroll, String formId, String activityId, ImportLineResultContainer importLineResultContainer,
        String marketingEventId, String ea, Integer fsUserId, Map<String, String> customFieldsMap) {
        String customizeFormDataUserId = UUIDUtil.getUUID();
        try {
            CustomizeFormDataUserEntity customizeFormDataUserEntity = new CustomizeFormDataUserEntity();
            customizeFormDataUserEntity.setEa(ea);
            customizeFormDataUserEntity.setId(customizeFormDataUserId);
            customizeFormDataUserEntity.setUid("");
            customizeFormDataUserEntity.setFormId(formId);
            customizeFormDataUserEntity.setObjectId(activityId);
            customizeFormDataUserEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
            customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            customizeFormDataUserEntity.setSourceType(CustomizeFormDataUserSourceTypeEnum.IMPORT_DATA.getType());
            customizeFormDataUserEntity.setMarketingEventId(marketingEventId);

            customizeFormDataUserDAO.insertCustomizeFormDataUser(customizeFormDataUserEntity);

            String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(customizeFormDataUserId, true);

            ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
            activityEnrollDataEntity.setId(UUIDUtil.getUUID());
            activityEnrollDataEntity.setEnrollSourceType(ConferenceEnrollSourceTypeEnum.MANUAL_IMPORT.getType());
            activityEnrollDataEntity.setUid("");
            activityEnrollDataEntity.setActivityId(activityId);
            activityEnrollDataEntity.setFormDataUserId(campaignId);
            activityEnrollDataEntity.setSpreadFsUserId(null);
            activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
            activityEnrollDataEntity.setSignInTime(null);
            activityEnrollDataEntity.setCreateTime(new Date());
            activityEnrollDataEntity.setEnrollSourceId(null);
            activityEnrollDataEntity.setReviewStatus(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
            activityEnrollDataEntity.setReviewUser(null);
            activityEnrollDataEntity.setReviewTime(null);
            activityEnrollDataEntity.setObjectId(activityId);
            activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
            // 分组
            String groupName = customFieldsMap.get(ImportConstants.IMPORT_GROUP_API_NAME);
            if (StringUtils.isNotBlank(groupName)) {
                String []groupNames = groupName.split("\\|");
                StringJoiner key = new StringJoiner(",");
                for (String s : groupNames) {
                    Integer groupId = conferenceManager.createGroupUserInfo(ea, fsUserId, activityId, s);
                    key.add(groupId.toString());
                }
                activityEnrollDataEntity.setGroupUserId(key.toString());
            }
            conferenceManager.addActivityEnrollData(activityEnrollDataEntity);

            // 生成报名code(绑定参会人员后再生成)
            // customizeTicketManager.createCustomizeTicket(activityId, CustomizeTicketTypeEnum.ACTIVITY_TICKET.getType(), ea, campaignId);
            importLineResultContainer.setImportDesc(ImportErrorEnum.SUCCESS.getDesc());
            importLineResultContainer.setCustomizeFormDataUserEntity(customizeFormDataUserEntity);
        } catch (Exception e) {
            log.warn("ImportManager.saveFormDataAndEnrollData error customizeFormDataEnroll:{}, formId:{}, activityId:{}", customizeFormDataEnroll, formId, activityId);
            importLineResultContainer.setImportDesc(ImportErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }

    private void saveFormDataAndEnrollData(CustomizeFormDataEnroll customizeFormDataEnroll, ImportLineArgContainer argContainer, ImportLineResultContainer importLineResultContainer, Map<String, String> customFieldsMap) {
        String customizeFormDataUserId = UUIDUtil.getUUID();
        try {
            CustomizeFormDataUserEntity customizeFormDataUserEntity = new CustomizeFormDataUserEntity();
            customizeFormDataUserEntity.setEa(argContainer.getEa());
            customizeFormDataUserEntity.setId(customizeFormDataUserId);
            customizeFormDataUserEntity.setUid("");
            customizeFormDataUserEntity.setFormId(argContainer.getFormId());
            customizeFormDataUserEntity.setObjectId(argContainer.getObjectId());
            customizeFormDataUserEntity.setObjectType(argContainer.getObjectType());
            customizeFormDataUserEntity.setSubmitContent(customizeFormDataEnroll);
            customizeFormDataUserEntity.setSourceType(CustomizeFormDataUserSourceTypeEnum.IMPORT_DATA.getType());
            customizeFormDataUserEntity.setMarketingEventId(StringUtils.isNotBlank(argContainer.getMarketingEventId()) ? argContainer.getMarketingEventId() : null);
            customizeFormDataUserEntity.setMarketingActivityId(StringUtils.isNotBlank(argContainer.getMarketingActivity()) ? argContainer.getMarketingActivity() : null);
            // 渠道
            String channelValue = customFieldsMap.get(ImportConstants.IMPORT_CHANNEL_API_NAME);
            if (StringUtils.isNotBlank(channelValue)) {
                customizeFormDataUserEntity.setChannelValue(channelValue);
            }
            customizeFormDataUserDAO.insertCustomizeFormDataUser(customizeFormDataUserEntity);

            if (StringUtils.isNotBlank(argContainer.getMarketingEventId())) {
                String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(customizeFormDataUserId, true);
                ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(argContainer.getMarketingEventId(), argContainer.getEa());
                if (activityEntity != null) {
                    ActivityEnrollDataEntity activityEnrollDataEntity = new ActivityEnrollDataEntity();
                    activityEnrollDataEntity.setId(UUIDUtil.getUUID());
                    activityEnrollDataEntity.setEnrollSourceType(ConferenceEnrollSourceTypeEnum.MANUAL_IMPORT.getType());
                    activityEnrollDataEntity.setUid("");
                    activityEnrollDataEntity.setActivityId(activityEntity.getId());
                    activityEnrollDataEntity.setFormDataUserId(campaignId);
                    activityEnrollDataEntity.setSpreadFsUserId(null);
                    activityEnrollDataEntity.setSignIn(ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType());
                    activityEnrollDataEntity.setSignInTime(null);
                    activityEnrollDataEntity.setCreateTime(new Date());
                    activityEnrollDataEntity.setEnrollSourceId(null);
                    activityEnrollDataEntity.setReviewStatus(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
                    activityEnrollDataEntity.setReviewUser(null);
                    activityEnrollDataEntity.setReviewTime(null);
                    activityEnrollDataEntity.setObjectId(activityEntity.getId());
                    activityEnrollDataEntity.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                    conferenceManager.addActivityEnrollData(activityEnrollDataEntity);
                }
            }
            importLineResultContainer.setImportDesc(ImportErrorEnum.SUCCESS.getDesc());
            importLineResultContainer.setCustomizeFormDataUserEntity(customizeFormDataUserEntity);
        } catch (Exception e) {
            log.warn("ImportManager.saveFormDataAndEnrollData error customizeFormDataEnroll:{}, formId:{}", customizeFormDataEnroll, argContainer.getFormId());
            importLineResultContainer.setImportDesc(ImportErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 校验字段
     */
    private ImportErrorEnum checkLineData(FieldInfo fieldInfo, String lineData, List<String> phoneList) {
        // 必填校验
        if (fieldInfo.getIsRequired() && StringUtils.isBlank(lineData) && !fieldInfo.getType().equals(FieldInfo.Type.IMAGE.getValue())) {
            return ImportErrorEnum.REQUIRED_FIELD_IS_EMPTY;
        }
        // 图片暂时不支持导入
        if (StringUtils.isNotBlank(lineData) && fieldInfo.getType().equals(FieldInfo.Type.IMAGE.getValue())) {
            return ImportErrorEnum.IMAGE_TYPE_DATA_NOT_SUPPORT;
        }
        if (StringUtils.isBlank(lineData)) {
            return ImportErrorEnum.SUCCESS;
        }
        if (fieldInfo.getType().equals("select_one")) {
            // 单选校验
            List<Option> optionList = fieldInfo.getOptions();
            return optionList.stream().noneMatch(data -> data.getLabel().equals(lineData)) ? ImportErrorEnum.RADIO_OPTION_DOES_NOT_EXIST : ImportErrorEnum.SUCCESS;
        } else if (fieldInfo.getType().equals("select_manny")) {
            // 多选校验
            List<String> optionList = fieldInfo.getOptions().stream().map(Option::getLabel).collect(Collectors.toList());
            String[] enterOption = lineData.split("\\|");
            List<String> differenceList = Arrays.stream(enterOption).filter(data -> !optionList.contains(data)).collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(differenceList) ? ImportErrorEnum.MULTIPLE_SELECTION_OPTION_DOES_NOT_EXIST : ImportErrorEnum.SUCCESS;
        } else if (fieldInfo.getType().equals("date_time")) {
            // 时间校验
            try {
                DateUtil.getTimeStampFromStr(lineData);
            } catch (Exception e) {
                return ImportErrorEnum.TIME_FORMAT_ERROR;
            }
        } else if (fieldInfo.getType().equals("number")) {
            // 数值校验
            return NumberUtil.isNumeric(lineData) ? ImportErrorEnum.SUCCESS : ImportErrorEnum.NUMBER_FORMAT_ERROR;
        } else if (fieldInfo.getType().equals("phone_number")) {
            // 校验手机号
            Matcher mPhone = phonePattern.matcher(lineData.trim());
            if (!mPhone.matches()) {
                return ImportErrorEnum.PHONE_FORMAT_ERROR;
            }
            if (phoneList.contains(lineData)) {
                return ImportErrorEnum.PHONE_DATA_EXIST_ERROR;
            } else {
                phoneList.add(lineData);
            }
        }
        return ImportErrorEnum.SUCCESS;
    }

    private ImportErrorEnum checkLineData(FieldInfo fieldInfo, String lineData, AreaContainer areaContainer) {
        // 必填校验
        if (fieldInfo.getIsRequired() && StringUtils.isBlank(lineData)) {
            return ImportErrorEnum.REQUIRED_FIELD_IS_EMPTY;
        }
        // 图片暂时不支持导入
        if (StringUtils.isNotBlank(lineData) && fieldInfo.getType().equals(FieldInfo.Type.IMAGE.getValue())) {
            return ImportErrorEnum.IMAGE_TYPE_DATA_NOT_SUPPORT;
        }
        if (StringUtils.isBlank(lineData)) {
            return ImportErrorEnum.SUCCESS;
        }
        if (fieldInfo.getType().equals("select_one")) {
            // 单选校验
            List<Option> optionList = fieldInfo.getOptions();
            return optionList.stream().noneMatch(data -> data.getLabel().equals(lineData)) ? ImportErrorEnum.RADIO_OPTION_DOES_NOT_EXIST : ImportErrorEnum.SUCCESS;
        } else if (fieldInfo.getType().equals("select_manny")) {
            // 多选校验
            List<String> optionList = fieldInfo.getOptions().stream().map(Option::getLabel).collect(Collectors.toList());
            String[] enterOption = lineData.split("\\|");
            List<String> differenceList = Arrays.stream(enterOption).filter(data -> !optionList.contains(data)).collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(differenceList) ? ImportErrorEnum.MULTIPLE_SELECTION_OPTION_DOES_NOT_EXIST : ImportErrorEnum.SUCCESS;
        } else if (fieldInfo.getType().equals("date_time")) {
            // 时间校验
            try {
                DateUtil.getTimeStampFromStr(lineData);
            } catch (Exception e) {
                return ImportErrorEnum.TIME_FORMAT_ERROR;
            }
        } else if (fieldInfo.getType().equals("number")) {
            // 数值校验
            return NumberUtil.isNumeric(lineData) ? ImportErrorEnum.SUCCESS : ImportErrorEnum.NUMBER_FORMAT_ERROR;
        } else if (fieldInfo.getType().equals("phone_number")) {
            // 校验手机号
            Matcher mPhone = phonePattern.matcher(lineData.trim());
            if (!mPhone.matches()) {
                return ImportErrorEnum.PHONE_FORMAT_ERROR;
            }
        } else if (fieldInfo.getApiName().equals("country")) {
            // 校验国家
            List<String> countryList = areaManager.buildAreaList(Lists.newArrayList(AreaTypeEnum.COUNTRY), areaContainer);
            return countryList.contains(lineData) ? ImportErrorEnum.SUCCESS : ImportErrorEnum.COUNTRY_DATA_ERROR;
        }else if (fieldInfo.getApiName().equals("province")) {
            // 校验省
            List<String> provinceList = areaManager.buildAreaList(Lists.newArrayList(AreaTypeEnum.PROVINCE), areaContainer);
            return provinceList.contains(lineData) ? ImportErrorEnum.SUCCESS : ImportErrorEnum.PROVINCE_DATA_ERROR;
        } else if (fieldInfo.getApiName().equals("city")) {
            // 校验市
            List<String> cityList = areaManager.buildAreaList(Lists.newArrayList(AreaTypeEnum.CITY), areaContainer);
            return cityList.contains(lineData) ? ImportErrorEnum.SUCCESS : ImportErrorEnum.CITY_DATA_ERROR;
        } else if (fieldInfo.getApiName().equals("district")) {
            // 校验区
            List<String> districtList = areaManager.buildAreaList(Lists.newArrayList(AreaTypeEnum.DISTRICT), areaContainer);
            return districtList.contains(lineData) ? ImportErrorEnum.SUCCESS : ImportErrorEnum.DISTRICT_DATA_ERROR;
        }
        return ImportErrorEnum.SUCCESS;
    }


    public Object buildColumnData(FieldInfo fieldInfo, String lineData, AreaContainer areaContainer) {
        if (StringUtils.isBlank(lineData)) {
            return "";
        }
        if (fieldInfo.getType().equals("select_one")) {
            // 单选设置
            List<Option> optionList = fieldInfo.getOptions();
            return optionList.stream().filter(data -> data.getLabel().equals(lineData)).findFirst().get().getValue();
        } else if (fieldInfo.getType().equals("select_manny")) {
            // 多选设置
            String[] enterOption = lineData.split("\\|");
            List<String> selectionList = Lists.newArrayList();
            for (String selection : enterOption) {
                selectionList.add(fieldInfo.getOptions().stream().filter(data -> data.getLabel().equals(selection)).findFirst().get().getValue());
            }
            return selectionList;
        } else if (fieldInfo.getType().equals("date_time")) {
            // 时间设置
            return BigDecimal.valueOf(DateUtil.getTimeStampFromStr(lineData));
        } else if (fieldInfo.getType().equals("number")) {
            // 数值设置
            return NumberUtil.getNumberFromStr(lineData);
        } else if(fieldInfo.getApiName().equals("country")){
            Map<String, String> countryMap = areaManager.buildLabelKeyAreaMap(areaContainer.getCountry());
            return countryMap.get(lineData);
        }else if (fieldInfo.getApiName().equals("province")) {
            Map<String, String> provinceMap = areaManager.buildLabelKeyAreaMap(areaContainer.getProvince());
            return provinceMap.get(lineData);
        } else if (fieldInfo.getApiName().equals("city")) {
            Map<String, String> cityMap = areaManager.buildLabelKeyAreaMap(areaContainer.getCity());
            return cityMap.get(lineData);
        } else if (fieldInfo.getApiName().equals("district")) {
            Map<String, String> districtMap = areaManager.buildLabelKeyAreaMap(areaContainer.getDistrict());
            return districtMap.get(lineData);
        }
        return lineData;
    }

    /**
     * 构建映射字段
     */
    public Object buildColumnData(FieldInfo fieldInfo, String lineData) {
        if (StringUtils.isBlank(lineData)) {
            return "";
        }
        if (fieldInfo.getType().equals("select_one")) {
            // 单选设置
            List<Option> optionList = fieldInfo.getOptions();
            return optionList.stream().filter(data -> data.getLabel().equals(lineData)).findFirst().get().getValue();
        } else if (fieldInfo.getType().equals("select_manny")) {
            // 多选设置
            String[] enterOption = lineData.split("\\|");
            List<String> selectionList = Lists.newArrayList();
            for (String selection : enterOption) {
                selectionList.add(fieldInfo.getOptions().stream().filter(data -> data.getLabel().equals(selection)).findFirst().get().getValue());
            }
            return selectionList;
        } else if (fieldInfo.getType().equals("date_time")) {
            // 时间设置
            return BigDecimal.valueOf(DateUtil.getTimeStampFromStr(lineData));
        } else if (fieldInfo.getType().equals("number")) {
            // 数值设置
            return NumberUtil.getNumberFromStr(lineData);
        }
        return lineData;
    }

    public String buildErrorExcelFile(List<String[]> excelData, List<ImportLineResultContainer> importLineResultContainerList) {

        List<List<Object>> excelContent = Lists.newArrayList();

        // 设置title
        List<String> titleList = Lists.newArrayList(Arrays.asList(excelData.get(0)));
        titleList.add(0, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_625));
        Integer enrollDescriptionIndex = 0;

        // 设置content
        for (int row = 1; row < excelData.size(); row++) {
            if (isAllRowEmpty(row, excelData.get(row))) {
                continue;
            }
            if (ImportErrorEnum.SUCCESS.getDesc().equals(importLineResultContainerList.get(row - 1).getImportDesc())) {
                continue;
            }
            Object[] lineData = excelData.get(row);
            List<Object> lineList = Lists.newArrayList(Arrays.asList(lineData));
            lineList.add(0, importLineResultContainerList.get(row - 1).getImportDesc());
            // 重新设置导入说明
            if (enrollDescriptionIndex < ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION.size()) {
                lineList.set(titleList.size() - 1, ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION.get(enrollDescriptionIndex));
                enrollDescriptionIndex = enrollDescriptionIndex + 1;
            }
            excelContent.add(lineList);
        }

        // 补充导入说明
        if (excelContent.size() < ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION.size()) {
            for (int row = excelContent.size(); row < ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION.size(); row++) {
                List<Object> rowData = Lists.newArrayList();
                for (int column = 0; column < titleList.size(); column++) {
                    rowData.add(column == titleList.size() - 1 ? ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION.get(row) : "");
                }
                excelContent.add(rowData);
            }
        }

        // 组装Excel模板数据
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_662) + ".xlsx");
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, titleList, excelContent);

        String apath = null;
        ByteArrayOutputStream bout = null;
        try {
            bout = new ByteArrayOutputStream();
            xssfWorkbook.write(bout);
            byte[] bytes = bout.toByteArray();
            // 上传至文件服务器
            apath = fileV2Manager.uploadToApath(bytes, "xlsx", null);
            bout.flush();
            bout.close();
        } catch (Exception e) {
            log.warn("ImportManager.buildErrorExcelFile error e:{}", e);
        }
        return apath;
    }

    public XSSFWorkbook buildErrorExcelFileForForm(List<String[]> excelData, List<ImportLineResultContainer> importLineResultContainerList) {
        List<String> titleList = Lists.newArrayList(Arrays.asList(excelData.get(0)));
        titleList.add(0, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_625));
        titleList.remove(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_687));
        List<List<Object>> excelContent = Lists.newArrayList();
        // 设置content
        for (int row = 1; row < excelData.size(); row++) {
            if (isAllRowEmpty(row, excelData.get(row))) {
                continue;
            }
            if (ImportErrorEnum.SUCCESS.getDesc().equals(importLineResultContainerList.get(row - 1).getImportDesc())) {
                continue;
            }
            Object[] lineData = excelData.get(row);
            List<Object> lineList = Lists.newArrayList(Arrays.asList(lineData));
            lineList.add(0, importLineResultContainerList.get(row - 1).getImportDesc());
            excelContent.add(lineList);
        }
        // 组装Excel模板数据
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_706) + ".xlsx");
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, titleList, excelContent);

        return xssfWorkbook;
    }

    public XSSFWorkbook buildExcelFileAndGetPath(String fileName, List<String> titleList, List<List<Object>> excelContent) {
        if (StringUtils.isBlank(fileName) || CollectionUtils.isEmpty(titleList) || CollectionUtils.isEmpty(excelContent)) {
            return null;
        }
        String path = null;
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, fileName);
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "enrollSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, titleList, excelContent);

        return xssfWorkbook;
    }

    /**
     * 检验导入数据title
     */
    public boolean checkImportConferenceInviteDataTitle(List<String> titleList) {
        if (CollectionUtils.isEmpty(titleList)) {
            return false;
        }

        List<String> labelList = Lists.newArrayList("姓名(必填）", "手机号码(必填）", "邮箱(选填）", "公司名称(选填）", "职务(选填）", "邀约员工(必填）");
        if (titleList.size() < labelList.size()) {
            return false;
        }

        for (int i = 0; i < labelList.size(); i++) {
            if (!labelList.get(i).equals(titleList.get(i))) {
                return false;
            }
        }

        return true;
    }

    public ImportUserDataResult saveConferenceInviteData(String ea, String activityId, List<String[]> excelData, Integer userId) {
        ImportUserDataResult importUserDataResult = new ImportUserDataResult();
        List<String> names = Lists.newArrayList();
        List<String> phones = Lists.newArrayList();
        for (int row = 1; row < excelData.size(); row++) {
            if (StringUtils.isNotBlank(excelData.get(row)[INVITE_EMPLOYEE_LINE_NUM])) {
                names.add(excelData.get(row)[INVITE_EMPLOYEE_LINE_NUM].trim());
            }
            if (StringUtils.isNotBlank(excelData.get(row)[INVITE_PHONE_LINE_NUM])) {
                phones.add(excelData.get(row)[INVITE_PHONE_LINE_NUM].trim());
            }
        }
        Map<String, Integer> employeeNameMap = fsAddressBookManager.getEmployeeByNames(ea, names);

        List<String> existPhones = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(phones)) {
            List<ConferenceInviteParticipantEntity> existParticipantList = inviteParticipantDAO.findByPhones(activityId, phones);
            if (CollectionUtils.isNotEmpty(existParticipantList)) {
                existParticipantList.forEach(inviteParticipantEntity -> {
                    existPhones.add(inviteParticipantEntity.getPhone());
                });
            }
        }

        List<ConferenceInviteParticipantEntity> successInviteList = Lists.newArrayList();
        List<List<Object>> errorData = Lists.newArrayList();
        for (int i = 1; i < excelData.size(); i++) {
            String[] line = excelData.get(i);
            boolean allColIsBlank = true;
            for (int col = 0; col < INVITE_MAX_COL_LIMIT; col++) { //过滤数据项都为空的行
                if(StringUtils.isNotBlank(line[col])) {
                    allColIsBlank = false;
                }
            }
            if (allColIsBlank) {
                continue;
            }
            ImportErrorEnum importResult = checkInviteLineData(line, employeeNameMap, existPhones);
            if (importResult.equals(ImportErrorEnum.SUCCESS)) {
                ConferenceInviteParticipantEntity inviteEntity = new ConferenceInviteParticipantEntity();
                inviteEntity.setId(UUIDUtil.getUUID());
                inviteEntity.setEa(ea);
                inviteEntity.setConferenceId(activityId);
                inviteEntity.setInvitorName(line[INVITE_NAME_LINE_NUM].trim());
                inviteEntity.setPhone(line[INVITE_PHONE_LINE_NUM].trim());
                if (StringUtils.isNotBlank(line[INVITE_EMAIL_LINE_NUM])) {
                    inviteEntity.setEmail(line[INVITE_EMAIL_LINE_NUM].trim());
                }
                if (StringUtils.isNotBlank(line[INVITE_COMPANY_LINE_NUM])) {
                    inviteEntity.setCompany(line[INVITE_COMPANY_LINE_NUM].trim());
                }
                if (StringUtils.isNotBlank(line[INVITE_POSITION_LINE_NUM])) {
                    inviteEntity.setPosition(line[INVITE_POSITION_LINE_NUM].trim());
                }
                inviteEntity.setFsUserName(line[INVITE_EMPLOYEE_LINE_NUM].trim());
                inviteEntity.setFsUserId(employeeNameMap.get(line[INVITE_EMPLOYEE_LINE_NUM].trim()));
                inviteEntity.setStatus(ConferenceInviteStatusEnum.UN_INVITE.getStatus());
                inviteEntity.setSendStatus(ConferenceInviteNoticeStatusEnum.UN_NOTICE.getStatus());
                successInviteList.add(inviteEntity);
                existPhones.add(line[INVITE_PHONE_LINE_NUM].trim());
            } else {
                List<Object> errorList = Lists.newArrayList();
                for (int col = 0; col < INVITE_MAX_COL_LIMIT; col++) {
                    errorList.add(line[col]);
                }
                errorList.add(0, importResult.getDesc());
                errorData.add(errorList);
            }
        }
        importUserDataResult.setSuccessNum(successInviteList.size() * 1L);
        importUserDataResult.setFailNum(errorData.size() * 1L);
        if (CollectionUtils.isNotEmpty(errorData)) {
            String errorFileApath = buildInviteErrorFile(errorData);
            importUserDataResult.setErrorFileApath(errorFileApath);
            try {
                ImportDataRecordEntity importDataRecordEntity = new ImportDataRecordEntity();
                importDataRecordEntity.setId(UUIDUtil.getUUID());
                importDataRecordEntity.setEa(ea);
                importDataRecordEntity.setImportType(ImportTypeEnum.CONFERENCE_INVITE.getType());
                importDataRecordEntity.setImportObjectId(activityId);
                importDataRecordEntity.setImportSuccessNum(successInviteList.size() * 1L);
                importDataRecordEntity.setImportFailNum(errorData.size() * 1L);
                importDataRecordEntity.setResultType(ImportResultTypeEnum.A_PATH_FILE.getType());
                importDataRecordEntity.setResultContent(errorFileApath);
                importDataRecordEntity.setImportUser(userId);
                importDataRecordDAO.insertImportDataRecord(importDataRecordEntity);
            } catch (Exception e) {
                log.warn("ImportManager.saveConferenceInviteData insertImportDataRecord error e:{}", e.fillInStackTrace());
            }
        }
        if (CollectionUtils.isNotEmpty(successInviteList)) {
            inviteParticipantDAO.batchInsertInviteParticipant(successInviteList);
        }
        return importUserDataResult;
    }

    private ImportErrorEnum checkInviteLineData(String[] lineData, Map<String, Integer> employeeNameMap, List<String> existPhones) {
        //检查必填项
        if (StringUtils.isBlank(lineData[INVITE_NAME_LINE_NUM]) || StringUtils.isBlank(lineData[INVITE_PHONE_LINE_NUM]) || StringUtils.isBlank(lineData[INVITE_EMPLOYEE_LINE_NUM])) {
            return ImportErrorEnum.REQUIRED_FIELD_IS_EMPTY;
        }

        if (existPhones.contains(lineData[INVITE_PHONE_LINE_NUM].trim())) {
            return ImportErrorEnum.DATA_EXIST_ERROR;
        }

        // 校验手机号码
        Matcher mPhone = phonePattern.matcher(lineData[INVITE_PHONE_LINE_NUM].trim());
        if (!mPhone.matches()) {
            return ImportErrorEnum.PHONE_FORMAT_ERROR;
        }

        // 校验邀约人姓名
        if (!employeeNameMap.containsKey(lineData[INVITE_EMPLOYEE_LINE_NUM].trim())) {
            return ImportErrorEnum.CONFERENCE_INVITE_NAME_ERROR;
        }

        //邮箱地址不为空需校验格式
        if(StringUtils.isNotBlank(lineData[INVITE_EMAIL_LINE_NUM])) {
            Matcher mEmail = emailPattern.matcher(lineData[INVITE_EMAIL_LINE_NUM].trim());
            if (!mEmail.matches()) {
                return ImportErrorEnum.EMAIL_FORMAT_ERROR;
            }
        }

        return ImportErrorEnum.SUCCESS;
    }

    public String buildInviteErrorFile(List<List<Object>> errorData) {
        if (CollectionUtils.isEmpty(errorData)) {
            return null;
        }
        List<String> labelList = Lists.newArrayList("错误原因", "姓名(必填）", "手机号码(必填）", "邮箱(选填）", "公司名称(选填）", "职务(选填）", "邀约员工(必填）");
        // 组装Excel模板数据
        Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
        excelConfigMap.put(ExcelConfigEnum.FILE_NAME, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_891) + ".xlsx");
        excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, "inviteSheet");
        excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
        XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
        xssfSheet.setDefaultColumnWidth(20);
        ExcelUtil.fillContent(xssfSheet, labelList, errorData);

        String apath = null;
        ByteArrayOutputStream bout = new ByteArrayOutputStream();
        try {
            xssfWorkbook.write(bout);
            byte[] bytes = bout.toByteArray();
            // 上传至文件服务器
            apath = fileV2Manager.uploadToApath(bytes, "xlsx", null);
            bout.flush();
        } catch (Exception e) {
            log.warn("ImportManager.buildInviteErrorFile error e:{}", e);
        } finally {
            try {
                bout.close();
            } catch (IOException e) {
                log.warn("ImportManager.buildInviteErrorFile Stream close error, e:{}", e.fillInStackTrace());
            }
        }
        return apath;
    }

    /**
     * 获取导入模板说明（将说明加在最后一列）
     * @param titleSize
     * @param descriptionList
     * @return
     */
    public List<List<Object>> buildImportTemplateDescriptionData(int titleSize, List<String> descriptionList) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (String description : descriptionList) {
            List<Object> rowData = Lists.newArrayList();
            for (int column = 0; column < titleSize; column++) {
                rowData.add(column == titleSize - 1 ? description : "");
            }
            dataList.add(rowData);
        }
        return dataList;
    }

    public ScopeContainer buildChannelScopeContainer(String ea, Integer colNum) {
        Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(ea);
        if (MapUtils.isEmpty(channelValueMap)) {
            return null;
        }
        ScopeContainer scopeContainer = new ScopeContainer();
        scopeContainer.setExcelScopeEnum(ExcelScopeEnum.SELECT_CONSTRAINT);
        scopeContainer.setFirstRow(1);
        scopeContainer.setLastRow(ImportConstants.SCOPE_MAX_LINE);
        scopeContainer.setFirstCol(colNum);
        scopeContainer.setLastCol(colNum);
        scopeContainer.setOptions(Lists.newArrayList(channelValueMap.values()));
        scopeContainer.setScopeName("Channel" + colNum);
        return scopeContainer;
    }

    public List<ScopeContainer> buildSingleChoiceScopeContainer(List<FieldInfo> fieldInfos) {
        List<ScopeContainer> scopeContainerList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fieldInfos)) {
            return scopeContainerList;
        }
        for (int i = 0; i < fieldInfos.size(); i++) {
            if (!fieldInfos.get(i).getType().equals(Type.SELECT_ONE.getValue())) {
                continue;
            }
            ScopeContainer scopeContainer = new ScopeContainer();
            scopeContainer.setExcelScopeEnum(ExcelScopeEnum.SELECT_CONSTRAINT);
            scopeContainer.setFirstRow(1);
            scopeContainer.setLastRow(ImportConstants.SCOPE_MAX_LINE);
            scopeContainer.setFirstCol(i);
            scopeContainer.setLastCol(i);
            scopeContainer.setOptions(fieldInfos.get(i).getOptions().stream().map(Option::getLabel).collect(Collectors.toList()));
            scopeContainer.setScopeName("SingleChoice" + i);
            scopeContainerList.add(scopeContainer);
        }
        return scopeContainerList;
    }

    public List<ScopeContainer> buildAreaScopeContainer(List<FieldInfo> fieldInfos) {
        List<ScopeContainer> scopeContainerList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fieldInfos)) {
            return scopeContainerList;
        }
        AreaContainer areaContainer = areaManager.buildAreaData();
        for (int i = 0; i < fieldInfos.size(); i++) {
            ScopeContainer scopeContainer = new ScopeContainer();
            scopeContainer.setExcelScopeEnum(ExcelScopeEnum.SELECT_CONSTRAINT);
            scopeContainer.setFirstRow(1);
            scopeContainer.setLastRow(ImportConstants.SCOPE_MAX_LINE);
            scopeContainer.setFirstCol(i);
            scopeContainer.setLastCol(i);
            scopeContainer.setScopeName("Area" + i);
            switch (fieldInfos.get(i).getApiName()) {
                case "country": {
                    // 国家选项
                    Map<String, String> countryMap = areaManager.getAreaMapByType(areaContainer, AreaTypeEnum.COUNTRY);
                    scopeContainer.setOptions(Lists.newArrayList(countryMap.values()));
                    scopeContainerList.add(scopeContainer);
                    break;
                }
                case "province": {
                    // 省选项
                    Map<String, String> provinceMap = areaManager.getAreaMapByType(areaContainer, AreaTypeEnum.PROVINCE);
                    scopeContainer.setOptions(Lists.newArrayList(provinceMap.values()));
                    scopeContainerList.add(scopeContainer);
                    break;
                }
                case "city": {
                    // 市选项
                    Map<String, String> provinceMap = areaManager.getAreaMapByType(areaContainer, AreaTypeEnum.CITY);
                    scopeContainer.setOptions(Lists.newArrayList(provinceMap.values()));
                    scopeContainerList.add(scopeContainer);
                    break;
                }
                case "district": {
                    // 区选项
                    Map<String, String> provinceMap = areaManager.getAreaMapByType(areaContainer, AreaTypeEnum.DISTRICT);
                    scopeContainer.setOptions(Lists.newArrayList(provinceMap.values()));
                    scopeContainerList.add(scopeContainer);
                    break;
                }
            }
        }
        areaContainer = null;
        return scopeContainerList;
    }


    @Data
    public static class ImportLineResultContainer implements Serializable {

        // 保存结果描述
        private String importDesc;

        // 报名数据对象
        private CustomizeFormDataUserEntity customizeFormDataUserEntity;

    }

    @Data
    public static class ImportLineArgContainer implements Serializable {
        private String ea;
        private List<FieldInfo> titleField;
        private List<String[]> excelData;
        private String formId;
        private Integer objectType;
        private String objectId;
        private String marketingEventId;
        private String marketingActivity;
    }

}