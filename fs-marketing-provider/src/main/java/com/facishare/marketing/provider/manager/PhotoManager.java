package com.facishare.marketing.provider.manager;

import com.facishare.eservice.common.utils.SpringContextUtil;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dto.ArticleEntityDTO;
import com.facishare.marketing.provider.dto.TargetPhotoPathDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.image.ImageDrawer;
import com.facishare.marketing.provider.manager.image.ImageDrawerTypeEnum;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.api.IConfigFactory;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.util.Constant.C_WAREHOUSE_TYPE;

/**
 * @ClassName PhotoManager
 * @Description
 * <AUTHOR>
 * @Date 2018/12/11 4:53 PM
 */

@Service
@Slf4j
public class PhotoManager {

    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private RedisManager redisManager;

    @ReloadableProperty("picture.fsEa")
    private String ea;

    @ReloadableProperty("default_conference_cover")
    private String defaultConferenceCover;

    private String fileShareSkey = "";

    @Autowired
    private FsBindManager fsBindManager;

    // fileShareSkey
    @PostConstruct
    private void init() {
        IConfigFactory factory = ConfigFactory.getInstance();
        factory.getConfig("fs-fsc-fileshare", (IConfig config) -> {
            fileShareSkey = config.get("skey");
        });
    }

    public String getDefaultCoverApath(){
        return defaultConferenceCover;
    }

    public List<PhotoEntity> listProductPhotos(String targetId) {
        List<PhotoEntity> photoEntityList = photoDAO.listProductPhotos(targetId);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, null);
        }
        return photoEntityList;
    }

    public List<PhotoEntity> listByProductIds(List<String> productIds,String ea) {
        List<PhotoEntity> photoEntityList = photoDAO.listByProductIds(productIds);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, ea);
        }
        return photoEntityList;
    }

    public List<PhotoEntity> listByTargeId(String targetId,String ea) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargeId(targetId);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, ea);
        }
        return photoEntityList;
    }

    public List<PhotoEntity> listByTargetIdAndTargetType(String targetId, int targetType,String ea) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndTargetType(targetId, targetType);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, ea);
        }
        return photoEntityList;
    }

    public void resetPhotoListUrl(List<PhotoEntity> photoList, String ea) {
        if (CollectionUtils.isEmpty(photoList)) {
            return;
        }
        List<String> paths = Lists.newArrayList();
        //兼容cpath
        List<String> cpaths = Lists.newArrayList();
        photoList.forEach(photoEntity -> {
            if (StringUtils.isNotBlank(photoEntity.getPath())) {
                if(photoEntity.getPath().startsWith("C_")){
                    cpaths.add(photoEntity.getPath());
                }else {
                    paths.add(photoEntity.getPath());
                }
            }
            if (StringUtils.isNotBlank(photoEntity.getThumbnailPath())) {
                if(photoEntity.getThumbnailPath().startsWith("C_")){
                    cpaths.add(photoEntity.getThumbnailPath());
                }else {
                    paths.add(photoEntity.getThumbnailPath());
                }
            }
        });

//        if (CollectionUtils.isNotEmpty(cpaths)) {
//            Map<String, String> cmap = fileV2Manager.getUrlMapBycPaths(ea,cpaths);
//            if (cmap != null) {
//                photoList.forEach(photoEntity -> {
//                    String url = cmap.get(photoEntity.getPath());
//                    if (StringUtils.isNotBlank(url)) {
//                        photoEntity.setUrl(url);
//                    }
//                    String thumbnailUrl = photoEntity.getThumbnailPath()==null?null:cmap.get(photoEntity.getThumbnailPath());
//                    if (StringUtils.isNotBlank(thumbnailUrl)) {
//                        photoEntity.setThumbnailUrl(thumbnailUrl);
//                    } else {
//                        photoEntity.setThumbnailUrl(url);
//                    }
//                });
//            }else {
//                log.warn("PhotoManager resetPhotoListUrl fileV2Manager.batchGetUrlByPath cmap is null, photoList={}", photoList);
//            }
//        }
        if (CollectionUtils.isNotEmpty(paths)){
            Map<String, String> map = fileV2Manager.batchGetUrlByPath(paths, ea, false);
            if (map != null) {
                photoList.forEach(photoEntity -> {
                    String url = map.get(photoEntity.getPath());
                    if (StringUtils.isNotBlank(url)){
                        photoEntity.setUrl(url);
                    }
                    String thumbnailUrl = map.get(photoEntity.getThumbnailPath());
                    if (StringUtils.isNotBlank(thumbnailUrl)){
                        photoEntity.setThumbnailUrl(thumbnailUrl);
                    }
                });
            } else {
                log.warn("PhotoManager resetPhotoListUrl fileV2Manager.batchGetUrlByPath map is null, photoList={}", photoList);
            }
        }
    }

    public void resetCardPhotoUrl(List<CardEntity> cardList) {
        if (CollectionUtils.isEmpty(cardList)) {
            return;
        }
        List<String> paths = Lists.newArrayList();
        cardList.forEach(cardEntity -> {
            if (StringUtils.isNotBlank(cardEntity.getAvatarPath())) {
                paths.add(cardEntity.getAvatarPath());
            }
            if (StringUtils.isNotBlank(cardEntity.getAvatarThumbnailPath())) {
                paths.add(cardEntity.getAvatarThumbnailPath());
            }
        });
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(cardList.get(0).getUid());
        String fsEa = fsBindEntity == null ? null : fsBindEntity.getFsEa();
        if (CollectionUtils.isNotEmpty(paths)){
            Map<String, String> map = fileV2Manager.batchGetUrlByPath(paths, fsEa, false);
            if (map != null) {
                cardList.forEach(cardEntity -> {
                    String url = map.get(cardEntity.getAvatarPath());
                    if (StringUtils.isNotBlank(url)){
                        cardEntity.setAvatar(url);
                    }
                    String thumbnailUrl = map.get(cardEntity.getAvatarThumbnailPath());
                    if (StringUtils.isNotBlank(thumbnailUrl)){
                        cardEntity.setAvatarThumbnail(thumbnailUrl);
                    } else {
                        cardEntity.setAvatarThumbnail(url);
                    }
                });
            } else {
                log.warn("PhotoManager resetCardPhotoUrl fileV2Manager.batchGetUrlByPath map is null, cardList={}", cardList);
            }
        }
    }

    public List<PhotoEntity> queryPhoto(int targetType, String targetId) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndTargetType(targetId, targetType);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, null);
        }
        return photoEntityList;
    }

    public List<PhotoEntity> queryPhotoNoReset(int targetType, String targetId) {
        return photoDAO.listByTargetIdsAndTargetType(targetId, targetType);
    }

    public void doSyncCreatePhotoUrl(String fsEa, List<PhotoEntity> photoEntities){
        if (CollectionUtils.isEmpty(photoEntities)){
            log.info("doSyncCreatePhotoUrl photoEntities is null");
            return;
        }

        String currentEa = fsEa;
        if (currentEa == null){
            currentEa = ea;
        }

        Map<String, PhotoEntity> photoMap = photoEntities.stream().filter(o -> !o.getPath().startsWith(C_WAREHOUSE_TYPE)).collect(Collectors.toMap(PhotoEntity::getPath, Function.identity(), (v1, v2)->v2));
        for (Map.Entry<String, PhotoEntity> entry : photoMap.entrySet()){
            String path = entry.getKey();
            PhotoEntity photoEntity = entry.getValue();
            byte[] bytes = fileV2Manager.downloadAFile(path, 1000, currentEa);
            if (bytes != null){
                Optional<FileV2Manager.FileManagerPicResult> picResultOptional = fileV2Manager.uploadToApathWithThumbnail(bytes, "png", currentEa, 1000);
                if (picResultOptional.isPresent()){
                    FileV2Manager.FileManagerPicResult picResult = picResultOptional.get();
                    photoEntity.setPath(picResult.getUrlAPath() == null ? photoEntity.getPath() : picResult.getUrlAPath());
                    photoEntity.setUrl(picResult.getUrl() == null ? photoEntity.getUrl() : picResult.getUrl());
                    photoEntity.setThumbnailPath(picResult.getThumbUrlApath() == null ? photoEntity.getThumbnailPath() : picResult.getThumbUrlApath());
                    photoEntity.setThumbnailUrl(picResult.getThumbUrl() == null ? photoEntity.getThumbnailUrl() : picResult.getThumbUrl());
                }else {
                    log.info("doSyncCreatePhotoUrl.uploadToApathWithThumbnail failed picResultOptional:{}", picResultOptional);
                }
            }else {
                log.info("doSyncCreatePhotoUrl.downloadAFile failed path:{}", path);
            }
            log.info("doSyncCreatePhotoUrl.updatePhoto entity:{}", photoEntities);
            photoDAO.updatePhoto(photoEntity);
        }
    }


    /*
     * @Description 查询一对一图片实体
     * <AUTHOR>
     * @Date 5:07 PM 2018/12/12
     --------------------------
     * @Param [targetType, targetId]
     * @return com.facishare.mankeep.provider.entity.PhotoEntity
     *
     **/
    public PhotoEntity querySinglePhoto(int targetType, String targetId,String ea) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndTargetType(targetId, targetType);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, ea);
            PhotoEntity queryPhotoEntity = photoEntityList.get(0);
            return queryPhotoEntity;
        }
        return null;
    }

    public PhotoEntity querySinglePhotoByEa(int targetType, String targetId, String ea) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndTargetType(targetId, targetType);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, ea);
            return photoEntityList.get(0);
        }
        return null;
    }

    public PhotoEntity querySingleCpathPhoto(int targetType, String targetId) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndTargetType(targetId, targetType);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            PhotoEntity queryPhotoEntity = photoEntityList.get(0);
            return queryPhotoEntity;
        }
        return null;
    }

    public Map<Integer, PhotoEntity> batchQueryPhotoByTargetTypes(List<Integer> targetTypes, String targetId) {
        Map<Integer, PhotoEntity> map = new HashMap<>();
        List<PhotoEntity> photoEntityList = photoDAO.queryPhotosByTypesAndTargetId(targetTypes, targetId);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            for (PhotoEntity photoEntity : photoEntityList) {
                map.put(photoEntity.getTargetType(), photoEntity);
            }
            resetPhotoListUrl(photoEntityList, null);
        }
        return map;
    }

    public Map<String, PhotoEntity> batchQueryPhotoByTargetIds(Integer targetType, List<String> targetIds) {
        Map<String, PhotoEntity> map = new HashMap<>();
        List<PhotoEntity> photoEntityList = photoDAO.queryPhotosByTypeAndTargetIds(targetType, targetIds);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            for (PhotoEntity photoEntity : photoEntityList) {
                map.put(photoEntity.getTargetId(), photoEntity);
            }
            resetPhotoListUrl(photoEntityList, null);
        }
        return map;
    }

    public Map<String, Map<Integer, PhotoEntity>> batchQueryPhotoByTypesAndIds(List<Integer> targetTypes, List<String> targetIds) {
        Map<String, Map<Integer, PhotoEntity>> map = new HashMap<>();
        List<PhotoEntity> photoEntityList = photoDAO.queryPhotosByTypesAndIds(targetTypes, targetIds);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            for (PhotoEntity photoEntity : photoEntityList) {
                Map<Integer, PhotoEntity> unitMap = map.get(photoEntity.getTargetId());
                if (unitMap == null) {
                    unitMap = new HashMap<>();
                    map.put(photoEntity.getTargetId(), unitMap);
                }

                unitMap.put(photoEntity.getTargetType(), photoEntity);
            }
            resetPhotoListUrl(photoEntityList, null);
        }
        return map;
    }

    /*
     * @Description 保存文章封面图片
     * <AUTHOR>
     * @Date 19:39 PM 2018/12/11
     --------------------------
     * @Param [targetId, photoUrl]
     * @return java.util.List<java.lang.String>
     *
     **/
    public boolean savePhotoByAapath(String ea,PhotoTargetTypeEnum typeEnum, String targetId, String apath, String thumbUrlApath) {
        String result = savePhotoByAPath(ea,typeEnum, targetId, apath, thumbUrlApath);
        if (StringUtils.isEmpty(result)){
            return false;
        }
        return true;
    }


    public String savePhotoByAPath(String ea,PhotoTargetTypeEnum typeEnum, String targetId, String apath, String thumbUrlApath) {
        if (StringUtils.isBlank(targetId) || StringUtils.isBlank(apath)) {
            return null;
        }
        if (StringUtils.isBlank(thumbUrlApath)) {
            thumbUrlApath = apath;
        }

        String url = fileV2Manager.getUrlByPath(apath, ea, false);
        String thumbnailUrl = fileV2Manager.getUrlByPath(thumbUrlApath, ea, false);

        PhotoEntity photo = new PhotoEntity();
        photo.setId(UUIDUtil.getUUID());
        photo.setTargetType(typeEnum.getType());
        photo.setTargetId(targetId);
        photo.setPath(apath);
        photo.setThumbnailPath(thumbUrlApath);
        if (StringUtils.isNotBlank(url)) {
            photo.setUrl(url);
        }

        if (StringUtils.isNotBlank(thumbnailUrl)) {
            photo.setThumbnailUrl(thumbnailUrl);
        }
//        else{
//            photo.setThumbnailUrl(url);
//        }
        photo.setSeqNum(0);
        Boolean result = photoDAO.addPhoto(photo);
        if (result){
            return photo.getId();
        }else {
            return null;
        }
    }

    /*
     * @Description 创建或者更新一个实体
     * <AUTHOR>
     * @Date 4:17 PM 2018/12/13
     --------------------------
     * @Param [typeEnum， targetId，urlAPath, thumbUrlApath]
     * @return boolean
     *
     **/
    public boolean addOrUpdatePhotoByPhotoTargetType(String ea,PhotoTargetTypeEnum typeEnum, String targetId, String urlAPath, String thumbUrlApath) {
        if (StringUtils.isBlank(urlAPath)) {
            log.info("addOrUpdatePhotoByPhotoTargetType fail, urlAPath is null");
            return false;
        }
        if(StringUtils.isBlank(thumbUrlApath)) {
            thumbUrlApath = urlAPath;
        }
        List<PhotoEntity> photoEntities = queryPhoto(typeEnum.getType(), targetId);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(photoEntities)) {
            return savePhotoByAapath(ea,typeEnum, targetId, urlAPath, thumbUrlApath);
        } else {
            String url = fileV2Manager.getUrlByPath(urlAPath,ea,false);
            String thumbnailUrl = fileV2Manager.getUrlByPath(thumbUrlApath,ea,false);

            PhotoEntity photoEntity = photoEntities.get(0);
            photoEntity.setPath(urlAPath);
            photoEntity.setThumbnailPath(thumbUrlApath);
            if (StringUtils.isNotBlank(url)) {
                photoEntity.setUrl(url);
            }
            if (StringUtils.isNotBlank(thumbnailUrl)) {
                photoEntity.setThumbnailUrl(thumbnailUrl);
            }
            return photoDAO.updatePhoto(photoEntity);
        }
    }

    public boolean addOrUpdatePhotoByCutOffset(String ea,PhotoTargetTypeEnum typeEnum, String targetId, PhotoCutOffset cutOffset, String originalImageAPath) {
        if (StringUtils.isBlank(originalImageAPath)) {
            log.info("addOrUpdatePhotoByCutOffset fail, originalImageAPath is null");
            return false;
        }

        String cpath = null;
        //cpath直接使用,其他path走缓存(目前只支持apath)
        if(!originalImageAPath.startsWith("C_")){
            cpath = redisManager.getCPathByPath(originalImageAPath,ea);
            if (Strings.isNullOrEmpty(cpath)) {
                cpath = fileV2Manager.getCpathByPath(ea, originalImageAPath);
                redisManager.setCPath(originalImageAPath,ea,cpath);
            }
        }else {
            cpath = originalImageAPath;
        }

        List<PhotoEntity> photoEntities = queryPhoto(typeEnum.getType(), targetId);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(photoEntities)) {
            PhotoEntity photoEntity = new PhotoEntity();
          //  String cpath = fileV2Manager.getCpathByPath(null, originalImageAPath);
            //原图url和缩略图url
            String url = fileV2Manager.getUrlByPath(ea, cpath);
            String thumbnailUrl = fileV2Manager.getUrlByPath(ea, cpath, cutOffset.getLeft(),cutOffset.getTop(),cutOffset.getWidth(), cutOffset.getHeight());
            photoEntity.setId(UUIDUtil.getUUID());
            photoEntity.setTargetType(typeEnum.getType());
            photoEntity.setTargetId(targetId);
            photoEntity.setSeqNum(0);
            photoEntity.setPath(cpath);
            if (StringUtils.isNotBlank(url)) {
                photoEntity.setUrl(url);
            }
            if (StringUtils.isNotBlank(thumbnailUrl)) {
                photoEntity.setThumbnailUrl(thumbnailUrl);
                if(PhotoTargetTypeEnum.checkIsOrdinaryCover(typeEnum)){
                    byte[] bytes = fileV2Manager.downloadFileByUrl(thumbnailUrl, ea);
                    String apath = fileV2Manager.uploadToCpathOrNpath(ea,bytes, true, null);
                    photoEntity.setThumbnailPath(apath);
                }
            }
            return photoDAO.addPhoto(photoEntity);
        } else {
           // String cpath = fileV2Manager.getCpathByPath(null, originalImageAPath);
            //原图url和缩略图url
            String url = fileV2Manager.getUrlByPath(ea, cpath);
            String thumbnailUrl = fileV2Manager.getUrlByPath(ea, cpath, cutOffset.getLeft(),cutOffset.getTop(), cutOffset.getWidth(), cutOffset.getHeight());
            PhotoEntity photoEntity = photoEntities.get(0);
            if(StringUtils.isNotBlank(cpath)){
                photoEntity.setPath(cpath);
            }
            if (StringUtils.isNotBlank(url)) {
                photoEntity.setUrl(url);
            }
            if (StringUtils.isNotBlank(thumbnailUrl)) {
                photoEntity.setThumbnailUrl(thumbnailUrl);
                if(PhotoTargetTypeEnum.checkIsOrdinaryCover(typeEnum)){
                    byte[] bytes = fileV2Manager.downloadFileByUrl(thumbnailUrl, ea);
                    String apath = fileV2Manager.uploadToCpathOrNpath(ea,bytes, true, null);
                    photoEntity.setThumbnailPath(apath);
                }
            }
            return photoDAO.updatePhoto(photoEntity);
        }
    }


    /*
         * @Description 查询文章图片url
         * <AUTHOR>
         * @Date 5:02 PM 2018/12/13
         --------------------------
         * @Param [articleEntity]
         * @return articlePhotoTumbnailApath
         *
    **/
    public String queryArticlePhotoUrl(ArticleEntity articleEntity) {
        if (articleEntity == null) {
            return null;
        }
        String photoApath;
        String photoThumbnailApath;
        List<PhotoEntity> photoEntities = queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId());
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            articleEntity.setPhotoUrl(fileV2Manager.getUrlByPath(photoEntities.get(0).getPath(), articleEntity.getFsEa(), false));
            articleEntity.setPhotoThumbnailUrl(fileV2Manager.getUrlByPath(photoEntities.get(0).getThumbnailPath(), articleEntity.getFsEa(), false));
            photoApath = photoEntities.get(0).getPath();
            photoThumbnailApath = photoEntities.get(0).getThumbnailPath();
        } else {
            photoApath = fileV2Manager.getApathByUrl(articleEntity.getPhotoUrl());

            photoThumbnailApath = fileV2Manager.getApathByUrl(articleEntity.getPhotoThumbnailUrl());

            if (StringUtils.isBlank(photoThumbnailApath)) {
                photoThumbnailApath = photoApath;
            }

            if (StringUtils.isNotBlank(photoApath)) {
                savePhotoByAapath(articleEntity.getFsEa(),PhotoTargetTypeEnum.ARTICLE_COVER, articleEntity.getId(), photoApath, photoThumbnailApath);
                String photoUrl = fileV2Manager.getUrlByPath(photoApath, articleEntity.getFsEa(),false);
                if (StringUtils.isNotBlank(photoUrl)) {
                    articleEntity.setPhotoUrl(photoUrl);
                }

                String photoThumbnailUrl = fileV2Manager.getUrlByPath(photoThumbnailApath, articleEntity.getFsEa(), false);
                if (StringUtils.isNotBlank(photoThumbnailUrl)) {
                    articleEntity.setPhotoThumbnailUrl(photoThumbnailUrl);
                }
            }
        }

        List<PhotoEntity> cardPhotos = queryPhoto(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL.getType(), articleEntity.getId());
        if (CollectionUtils.isNotEmpty(cardPhotos)) {
            String cardPhotoUrl = fileV2Manager.getUrlByPath(cardPhotos.get(0).getPath(), articleEntity.getFsEa(), false);
            if (StringUtils.isNotBlank(cardPhotoUrl)) {
                articleEntity.setCardPhotoUrl(cardPhotoUrl);
            }
        } else {
            String cardPhotoUrl = articleEntity.getCardPhotoUrl() != null ? articleEntity.getCardPhotoUrl() : articleEntity.getPhotoThumbnailUrl();
            String cardPhotoApath = fileV2Manager.getApathByUrl(cardPhotoUrl);
            if (StringUtils.isNotBlank(cardPhotoApath)) {
                savePhotoByAapath(articleEntity.getFsEa(),PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL, articleEntity.getId(), cardPhotoApath, cardPhotoApath);
            }
        }

        /*List<PhotoEntity> lmPhotos = queryPhoto(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM.getType(), articleEntity.getId());
        if (CollectionUtils.isNotEmpty(lmPhotos)) {
            String lmCoverUrl = fileV2Manager.getUrlByPath(lmPhotos.get(0).getPath(), null, false);
            if (StringUtils.isNotBlank(lmCoverUrl)) {
                articleEntity.setLmCoverUrl(lmCoverUrl);
            }
        } else {
            String lmCoverApath = fileV2Manager.getApathByUrl(articleEntity.getLmCoverUrl());
            if (StringUtils.isNotBlank(lmCoverApath)) {
                savePhotoByAapath(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM, articleEntity.getId(), lmCoverApath, lmCoverApath);
            } else if (StringUtils.isNotBlank(photoApath)) {
                Map<String, Object> params = new HashMap<>();
                params.put("coverApath", photoApath);
                ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.LuckyMoneyIconCover);
                lmCoverApath = imageDrawer.draw(params);
                if (StringUtils.isNotBlank(lmCoverApath)) { // 避免多次重复创建
                    savePhotoByAapath(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM, articleEntity.getId(), lmCoverApath, lmCoverApath);
                    String lmCoverUrl = fileV2Manager.getUrlByPath(lmCoverApath, null , false);
                    if (StringUtils.isNotBlank(lmCoverUrl)) {
                        articleEntity.setLmCoverUrl(lmCoverUrl);
                    }
                }
            }
        }*/
        return photoThumbnailApath;
    }

    public List<ArticleEntity> resetArticlePhotoUrl(List<ArticleEntity> articleList){
        List<ArticleEntityDTO> articleDtoList = BeanUtil.copy(articleList, ArticleEntityDTO.class);
        articleDtoList = resetArticlePhotoUrlByDTO(articleDtoList);
        List<ArticleEntity> resultArticleList = BeanUtil.copy(articleDtoList, ArticleEntity.class);
        return resultArticleList;
    }


    //临时支持双写,只支持cpath,ea暂时不使用，后面cdn上再使用
    public boolean addOrUpdatePhotoV2(String ea,PhotoTargetTypeEnum typeEnum, String targetId, String cpath) {
        if (StringUtils.isBlank(cpath)) {
            log.info("addOrUpdatePhotoV2 fail, cpath is null");
            return false;
        }
        if(!cpath.startsWith("C_")){
            log.info("addOrUpdatePhotoV2 fail, cpath is not cpath");
            return false;
        }
        List<PhotoEntity> photoEntities = queryPhoto(typeEnum.getType(), targetId);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(photoEntities)) {
            PhotoEntity photoEntity = new PhotoEntity();
            //原图url和缩略图url
            String url = fileV2Manager.getUrlByPath(ea, cpath);
            photoEntity.setId(UUIDUtil.getUUID());
            photoEntity.setTargetType(typeEnum.getType());
            photoEntity.setTargetId(targetId);
            photoEntity.setSeqNum(0);
            photoEntity.setPath(cpath);
            photoEntity.setThumbnailPath(cpath);
            if (StringUtils.isNotBlank(url)) {
                photoEntity.setUrl(url);
                photoEntity.setThumbnailUrl(url);
            }
            return photoDAO.addPhoto(photoEntity);
        } else {
            //原图url和缩略图url
            String url = fileV2Manager.getUrlByPath(ea, cpath);
            PhotoEntity photoEntity = photoEntities.get(0);
            if(StringUtils.isNotBlank(cpath)){
                photoEntity.setPath(cpath);
                photoEntity.setThumbnailPath(cpath);
            }
            if (StringUtils.isNotBlank(url)) {
                photoEntity.setUrl(url);
                photoEntity.setThumbnailUrl(url);
            }
            return photoDAO.updatePhoto(photoEntity);
        }
    }



    public boolean addOrUpdatePhotoV3(String ea,PhotoTargetTypeEnum typeEnum, String targetId, String cpath) {
        List<PhotoEntity> photoEntities = queryPhoto(typeEnum.getType(), targetId);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(photoEntities)) {
            PhotoEntity photoEntity = new PhotoEntity();
            //原图url和缩略图url
            String url = fileV2Manager.getUrlByPath(ea, cpath);
            photoEntity.setId(UUIDUtil.getUUID());
            photoEntity.setTargetType(typeEnum.getType());
            photoEntity.setTargetId(targetId);
            photoEntity.setSeqNum(0);
            photoEntity.setPath(cpath);
            photoEntity.setThumbnailPath(cpath);
            photoEntity.setUrl(url);
            photoEntity.setThumbnailUrl(url);
            return photoDAO.addPhoto(photoEntity);
        } else {
            if(StringUtils.isBlank(cpath)){
                return photoDAO.deletePhotos(Lists.newArrayList(photoEntities.get(0).getId()));
            }
            //原图url和缩略图url
            PhotoEntity photoEntity = photoEntities.get(0);
            String url = fileV2Manager.getUrlByPath(ea, cpath);
            if(StringUtils.isNotBlank(cpath)){
                photoEntity.setPath(cpath);
                photoEntity.setThumbnailPath(cpath);
            }
            if (StringUtils.isNotBlank(url)) {
                photoEntity.setUrl(url);
                photoEntity.setThumbnailUrl(url);
            }
            return photoDAO.updatePhoto(photoEntity);
        }
    }



    public List<ArticleEntityDTO> resetArticlePhotoUrlByDTO(List<ArticleEntityDTO> articleList){
        List<String> targetIds = articleList.stream().map(articleEntity -> articleEntity.getId()).collect(Collectors.toList());
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndType(targetIds, null);
        if (CollectionUtils.isEmpty(photoEntityList)) {
            return articleList;
        }
        Set<String> eaList = articleList.stream().map(ArticleEntity::getFsEa).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        resetPhotoListUrl(photoEntityList, eaList.isEmpty() ? null : eaList.iterator().next());
        Map<String, PhotoEntity> coverMap = Maps.newHashMap();
        Map<String, PhotoEntity> miniCoverMap = Maps.newHashMap();
        Map<String, PhotoEntity> lmCoverMap = Maps.newHashMap();
        photoEntityList.forEach(photoEntity -> {
            if (photoEntity.getTargetType() == PhotoTargetTypeEnum.ARTICLE_COVER.getType()) {
                coverMap.put(photoEntity.getTargetId(), photoEntity);
            }
            if (photoEntity.getTargetType() == PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL.getType()) {
                miniCoverMap.put(photoEntity.getTargetId(), photoEntity);
            }
            if (photoEntity.getTargetType() == PhotoTargetTypeEnum.MINI_COVER_ARTICLE_LM.getType()) {
                lmCoverMap.put(photoEntity.getTargetId(), photoEntity);
            }
        });

        articleList.forEach(articleEntity -> {
            PhotoEntity coverPhoto = coverMap.get(articleEntity.getId());
            if (coverPhoto != null) {
                articleEntity.setPhotoUrl(coverPhoto.getUrl());
                articleEntity.setPhotoThumbnailUrl(coverPhoto.getThumbnailUrl());
                articleEntity.setPhotoApath(coverPhoto.getPath());
                articleEntity.setPhotoThumbnailApath(coverPhoto.getThumbnailPath());
            }
            PhotoEntity miniPhoto = miniCoverMap.get(articleEntity.getId());
            if (miniPhoto != null) {
                articleEntity.setCardPhotoUrl(miniPhoto.getUrl());
            }

            PhotoEntity lmPhoto = lmCoverMap.get(articleEntity.getId());
            if (lmPhoto != null) {
                articleEntity.setLmCoverUrl(lmPhoto.getUrl());
            }
        });
        return articleList;
    }


    /*
     * @Description 刷新活动图片url
     * <AUTHOR>
     * @Date 5:02 PM 2018/12/13
     --------------------------
     * @Param [activityEntity]
     * @return
     *
     **/
    public String queryActivityPhotoUrl(ActivityEntity activityEntity) {
        if (activityEntity == null) {
            return null;
        }

        String photoThumbnailApath;
        List<PhotoEntity> photoEntities = queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId());
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            resetPhotoListUrl(photoEntities, null);
            String photoUrl = photoEntities.get(0).getUrl();
            if (StringUtils.isNotBlank(photoUrl)) {
                activityEntity.setCoverImageUrl(photoUrl);
            }

            String photoThumbnailUrl = photoEntities.get(0).getThumbnailUrl();
            if (StringUtils.isNotBlank(photoThumbnailUrl)) {
                activityEntity.setCoverImageSmallUrl(photoThumbnailUrl);
            }
            photoThumbnailApath = photoEntities.get(0).getThumbnailPath();
        } else {
            String photoApath = fileV2Manager.getApathByUrl(activityEntity.getCoverImageUrl());

            photoThumbnailApath = fileV2Manager.getApathByUrl(activityEntity.getCoverImageSmallUrl());
            if (StringUtils.isBlank(photoThumbnailApath)) {
                photoThumbnailApath = photoApath;
            }

            if (StringUtils.isNotBlank(photoApath)) {
                savePhotoByAapath(activityEntity.getEa(),PhotoTargetTypeEnum.ACTIVITY_COVER, activityEntity.getId(), photoApath, photoThumbnailApath);
                String photoUrl = fileV2Manager.getUrlByPath(photoApath, activityEntity.getEa(), false);
                if (StringUtils.isNotBlank(photoUrl)) {
                    activityEntity.setCoverImageUrl(photoUrl);
                }

                String photoThumbnailUrl = fileV2Manager.getUrlByPath(photoThumbnailApath, activityEntity.getEa(), false);
                if (StringUtils.isNotBlank(photoThumbnailUrl)) {
                    activityEntity.setCoverImageSmallUrl(photoThumbnailUrl);
                }
            }
        }

        List<PhotoEntity> backgroundPhotos = queryPhoto(PhotoTargetTypeEnum.ACTIVITY_BACKGROUND.getType(), activityEntity.getId());
        if (CollectionUtils.isNotEmpty(backgroundPhotos)) {
            String photoUrl = fileV2Manager.getUrlByPath(backgroundPhotos.get(0).getPath(), null, false);
            if (StringUtils.isNotBlank(photoUrl)) {
                activityEntity.setBackgroundImageUrl(photoUrl);
            }
        } else {
            String photoApath = fileV2Manager.getApathByUrl(activityEntity.getBackgroundImageUrl());
            if (StringUtils.isNotBlank(photoApath)) {
                savePhotoByAapath(activityEntity.getEa(),PhotoTargetTypeEnum.ACTIVITY_BACKGROUND, activityEntity.getId(), photoApath, photoApath);
                String photoUrl = fileV2Manager.getUrlByPath(photoApath, null, false);
                if (StringUtils.isNotBlank(photoUrl)) {
                    activityEntity.setBackgroundImageUrl(photoUrl);
                }
            }
        }

        List<PhotoEntity> qrcodePhotos = queryPhoto(PhotoTargetTypeEnum.ACTIVITY_WX_OFFICIAL_ACCOUNT_QRCODE.getType(), activityEntity.getId());
        if (CollectionUtils.isNotEmpty(qrcodePhotos)) {
            String photoUrl = fileV2Manager.getUrlByPath(qrcodePhotos.get(0).getPath(), null, false);
            if (StringUtils.isNotBlank(photoUrl)) {
                activityEntity.setPublicAccountUrl(photoUrl);
            }
        } else {
            String photoApath = fileV2Manager.getApathByUrl(activityEntity.getPublicAccountUrl());
            if (StringUtils.isNotBlank(photoApath)) {
                savePhotoByAapath(activityEntity.getEa(),PhotoTargetTypeEnum.ACTIVITY_WX_OFFICIAL_ACCOUNT_QRCODE, activityEntity.getId(), photoApath, photoApath);
                String photoUrl = fileV2Manager.getUrlByPath(photoApath, null, false);
                if (StringUtils.isNotBlank(photoUrl)) {
                    activityEntity.setPublicAccountUrl(photoUrl);
                }
            }
        }
        return photoThumbnailApath;
    }

    /*
    * @Description 查询运营人员实体类url
    * <AUTHOR>
    * @Date 3:02 PM 2018/12/14
    --------------------------
    * @Param [operatorEntity]
    * @return
    *
    **/
    public void queryOperatorPhotoUrl(Operator operatorEntity) {
        if (operatorEntity == null) {
            return;
        }

        List<PhotoEntity> qrcodePhotos = queryPhoto(PhotoTargetTypeEnum.DISTRIBUTION_OPERATOR_QRCODE.getType(), operatorEntity.getId());
        if (CollectionUtils.isNotEmpty(qrcodePhotos)) {
            String photoUrl = fileV2Manager.getUrlByPath(qrcodePhotos.get(0).getPath(), operatorEntity.getEa(), false);
            if (StringUtils.isNotBlank(photoUrl)) {
                operatorEntity.setQrUrl(photoUrl);
            }
        } else {
            String photoApath = fileV2Manager.getApathByUrl(operatorEntity.getQrUrl());
            if (StringUtils.isNotBlank(photoApath)) {
                savePhotoByAapath(operatorEntity.getEa(),PhotoTargetTypeEnum.DISTRIBUTION_OPERATOR_QRCODE, operatorEntity.getId(), photoApath, photoApath);
                String photoUrl = fileV2Manager.getUrlByPath(photoApath, operatorEntity.getEa(), false);
                if (StringUtils.isNotBlank(photoUrl)) {
                    operatorEntity.setQrUrl(photoUrl);
                }
            }
        }
    }

    /*
     * @Description 获取名片各类封面图片
     * <AUTHOR>
     * @Date 10:44 PM 2018/12/13
     --------------------------
     * @Param [cardEntity]
     * @return void
     *
     **/
    public void writeAvatarUrlToCardEntity(List<CardEntity> cardEntityList) {
        if (CollectionUtils.isEmpty(cardEntityList)) {
            log.warn("PhotoManager.writeAvatarUrlToCardEntity cardEntityList is null");
            return;
        }

        List<String> targetIds = new ArrayList<>();

        for (CardEntity cardEntity : cardEntityList) {
            targetIds.add(cardEntity.getUid());
        }

        List<String> apathList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(targetIds)) {

            List<Integer> targetTypes = new ArrayList<>();
            targetTypes.add(com.facishare.mankeep.common.enums.PhotoTargetTypeEnum.MINI_COVER_CARD_SHARE.getType());
            targetTypes.add(com.facishare.mankeep.common.enums.PhotoTargetTypeEnum.MINI_COVER_CARD_EXCHANGE.getType());
            targetTypes.add(com.facishare.mankeep.common.enums.PhotoTargetTypeEnum.CARD_QRCODE.getType());

            Map<String, Map<Integer, PhotoEntity>> map = batchQueryPhotoByTypesAndIds(targetTypes, targetIds);

            for (CardEntity cardEntity : cardEntityList) {

                Map<Integer, PhotoEntity> unitMap = map.get(cardEntity.getUid());

                if (null != unitMap) {
                    PhotoEntity cardShareCoverPhotoEntity = unitMap.get(PhotoTargetTypeEnum.MINI_COVER_CARD_SHARE.getType());
                    if (null != cardShareCoverPhotoEntity) {
                        if (StringUtils.isNotBlank(cardShareCoverPhotoEntity.getPath())) {
                            String shareApath = cardShareCoverPhotoEntity.getPath();
                            cardEntity.setSharePath(shareApath);
                            apathList.add(shareApath);
                        }
                    }

                    PhotoEntity cardExchangeCoverPhotoEntity = unitMap.get(PhotoTargetTypeEnum.MINI_COVER_CARD_EXCHANGE.getType());
                    if (null != cardExchangeCoverPhotoEntity) {
                        if (StringUtils.isNotBlank(cardExchangeCoverPhotoEntity.getPath())) {
                            String exchangeApath = cardExchangeCoverPhotoEntity.getPath();
                            cardEntity.setExchangePath(exchangeApath);
                            apathList.add(exchangeApath);
                        }
                    }

                    PhotoEntity cardQRCodePhotoEntity = unitMap.get(PhotoTargetTypeEnum.CARD_QRCODE.getType());
                    if (null != cardQRCodePhotoEntity) {
                        if (StringUtils.isNotBlank(cardQRCodePhotoEntity.getPath())) {
                            String qrCodeApath = cardQRCodePhotoEntity.getPath();
                            cardEntity.setQrUrl(qrCodeApath);
                            apathList.add(qrCodeApath);
                        }
                    }
                }
            }
        }
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(cardEntityList.get(0).getUid());
        String fsEa = fsBindEntity == null ? null : fsBindEntity.getFsEa();
        Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(apathList, fsEa, false);
        if (null == urlMap) {
            return;
        }

        for (CardEntity cardEntity : cardEntityList) {
            String avatarApath = cardEntity.getAvatarPath();
            if (StringUtils.isNotBlank(avatarApath)) {
                String avatarUrl = urlMap.get(avatarApath);
                if (StringUtils.isNotBlank(avatarUrl)) {
                    cardEntity.setAvatar(avatarUrl);
                    cardEntity.setAvatarThumbnail(avatarUrl);
                }
            }

            String shareApath = cardEntity.getSharePath();
            if (StringUtils.isNotBlank(shareApath)) {
                String cardShareCoverUrl = urlMap.get(shareApath);
                if (StringUtils.isNotBlank(cardShareCoverUrl)) {
                    cardEntity.setShareUrl(cardShareCoverUrl);
                }
            }

            String exchangeApath = cardEntity.getExchangePath();
            if (StringUtils.isNotBlank(exchangeApath)) {
                String cardExchangeCoverUrl = urlMap.get(exchangeApath);
                if (StringUtils.isNotBlank(cardExchangeCoverUrl)) {
                    cardEntity.setExchangeUrl(cardExchangeCoverUrl);
                }
            }

            String qrApath = cardEntity.getQrUrl();
            if (StringUtils.isNotBlank(qrApath)) {
                String cardQRCodeUrl = urlMap.get(qrApath);
                if (StringUtils.isNotBlank(cardQRCodeUrl)) {
                    cardEntity.setQrUrl(cardQRCodeUrl);
                }
            }
        }
    }

    public List<TargetPhotoPathDTO> listPathByTargetTypeAndIds(List<String> targetIds, Integer targetType){
        return photoDAO.listPathByTargetTypeAndIds(targetIds, targetType);
    }

    /*
     * 查看指定类型与批量ID photo列表
     **/
    public List<PhotoEntity> queryPhotosByTypeAndTargetIds(int targetType, List<String> targetIds) {
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndType(targetIds, targetType);
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            resetPhotoListUrl(photoEntityList, null);
        }
        return photoEntityList;
    }

    /*
     * 查看指定类型与批量ID photo列表
     **/
    public List<PhotoEntity> queryPhotosByTypeAndTargetIdsNoReset(int targetType, List<String> targetIds) {
        return photoDAO.listByTargetIdsAndType(targetIds, targetType);
    }

    public void addOrUpdateMiniCoverPhoto(String ea,String targetId, PhotoTargetTypeEnum typeEnum, String apath){
        Map<String, Object> params = new HashMap<>();
        params.put("coverApath", apath);
        if (StringUtils.isNotBlank(ea)){
            params.put("ea", ea);
        }
        ImageCreator imageCreator = SpringContextUtil.getBean("imageCreator");
        ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.NormalCover);
        String cardPhotoApath = imageDrawer.draw(params);
        if (StringUtils.isNotBlank(cardPhotoApath)) {
            addOrUpdatePhotoByPhotoTargetType(ea,typeEnum, targetId, cardPhotoApath, cardPhotoApath);
        }
    }

    /**
     * 根据targetId和targetType删除图片
     * @param targetId
     * @param targetType
     */
    public void deleteByTargetIdAndType(String targetId, Integer targetType){
        photoDAO.deletePhotosBytargetIdAndType(targetId, targetType);
    }
    public void deleteByTargetIdAndTypes(String targetId,List<Integer> targetTypes){
        photoDAO.deletePhotosBytargetIdAndTypes(targetId, targetTypes);
    }
}
