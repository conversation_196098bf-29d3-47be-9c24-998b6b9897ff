package com.facishare.marketing.provider.service.card;

import com.facishare.mankeep.common.enums.VideoStatusEnum;
import com.facishare.mankeep.common.enums.VideoTargetTypeEnum;
import com.facishare.marketing.api.result.card.CardTradeInfoResult;
import com.facishare.marketing.api.result.card.EnterpriseDefaultPhotoResult;
import com.facishare.marketing.api.result.card.GetEnterpriseDefaultCardResult;
import com.facishare.marketing.api.service.card.EnterpriseDefaultCardService;
import com.facishare.marketing.api.vo.card.GetEnterpriseDefaultCardVo;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.TradeDao;
import com.facishare.marketing.provider.dao.VideoDAO;
import com.facishare.marketing.provider.dao.card.EnterpriseDefaultCardDao;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.TradeEntity;
import com.facishare.marketing.provider.entity.VideoEntity;
import com.facishare.marketing.provider.entity.enterprisecard.EnterpriseDefaultCardEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/29
 **/
@Service("enterpriseDefaultCardService")
@Slf4j
public class EnterpriseDefaultCardServiceImpl implements EnterpriseDefaultCardService {

    @Autowired
    private EnterpriseDefaultCardDao enterpriseDefaultCardDao;

    @Autowired
    private TradeDao tradeDao;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private VideoDAO videoDAO;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Override
    public Result<GetEnterpriseDefaultCardResult> getEnterpriseDefaultCard(GetEnterpriseDefaultCardVo vo) {
        String fsEa = vo.getEa();
        String cardTplId = vo.getCardTemplateId();
        if (StringUtils.isEmpty(fsEa)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        EnterpriseDefaultCardEntity entity;
        if (StringUtils.isBlank(vo.getCardTemplateId())) {
            // 兼容历史
            entity = enterpriseDefaultCardDao.getByEa(fsEa);
        } else {
            entity = enterpriseDefaultCardDao.queryEnterpriseDefaultCardByEa(fsEa, cardTplId);
        }

        if (null == entity) {
            log.info("未找到名片企业信息设置");
            return Result.newSuccess();
        }
        GetEnterpriseDefaultCardResult result = BeanUtil.copy(entity, GetEnterpriseDefaultCardResult.class);

        if (StringUtils.isNotBlank(entity.getTradeCode())) {
            TradeEntity trade = tradeDao.queryTradeByCode(entity.getTradeCode());

            if (trade != null && StringUtils.isNotBlank(trade.getPTradeCode())) {
                TradeEntity pTrade = tradeDao.queryTradeByCode(trade.getPTradeCode());
                if (pTrade != null) {
                    CardTradeInfoResult cardTradeInfo = new CardTradeInfoResult();
                    cardTradeInfo.setTradeCodeOne(pTrade.getTradeCode());
                    cardTradeInfo.setTradeNameOne(pTrade.getTradeName());
                    cardTradeInfo.setTradeCodeTwo(trade.getTradeCode());
                    cardTradeInfo.setTradeNameTwo(trade.getTradeName());
                    result.setCardTradeInfo(cardTradeInfo);
                } else {
                    log.info("EnterpriseDefaultCardServiceImpl.getEnterpriseDefaultCard queryBaseCardInfo trade info, pTrade is null, tradeCode:{}, trade:{}", entity.getTradeCode(), trade);
                }
            } else {
                log.info("EnterpriseDefaultCardServiceImpl.getEnterpriseDefaultCard queryBaseCardInfo trade info error, tradeCode:{}, trade:{}", entity.getTradeCode(), trade);
            }
        }

        List<PhotoEntity> entities = photoManager.queryPhoto(PhotoTargetTypeEnum.ENTERPRICE_DEFAULT_CARD_DETAIL.getType(), entity.getId());
        List<EnterpriseDefaultPhotoResult> photoResults = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(entities)) {
            for (PhotoEntity photoEntity : entities) {
                EnterpriseDefaultPhotoResult photoResult = new EnterpriseDefaultPhotoResult();
                photoResult.setPhoto(photoEntity.getUrl());
                photoResult.setPhotoThumbnail(photoEntity.getThumbnailUrl());
                photoResults.add(photoResult);
            }
        }
        result.setPhotos(photoResults);
        VideoEntity video = videoDAO.queryVideoByEnterpriseDefaultCardEntityId(VideoTargetTypeEnum.ENTERPRICE_DEFAULT_CARD_DETAIL.getType(), entity.getId());
        if (video != null && video.getStatus() == VideoStatusEnum.SUCCESS.getStatus()) {
            result.setVideo(video.getHdUrl());
            result.setVideoCover(StringUtils.isNotBlank(video.getImage4Web()) ? video.getImage4Web() : video.getImage4H5());
        }
        //获取微页面名称
        if (StringUtils.isNotBlank(result.getCardHexagonId())) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(result.getCardHexagonId());
            if (hexagonSiteEntity != null) {
                result.setCardHexagonName(hexagonSiteEntity.getName());
            }
        }
        return Result.newSuccess(result);
    }
}
