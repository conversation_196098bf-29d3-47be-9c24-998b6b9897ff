package com.facishare.marketing.provider.entity.wxthirdplatform;

import com.google.common.base.Strings;
import java.util.Date;
import lombok.Data;

/**
 * PK(wxAppId)
 */
@Data
public class WechatAccountConfigEntity {
    public static final int ACCESS_TOKEN_AHEAD_EXPIRED_SECONDS = 60 * 10;
    private String wxAppId;
    /**
     * 授权给了哪个第三方平台
     */
    private String thirdPlatformId;
    /**
     * 刷新票据的token
     */
    private String refreshToken;

    /**
     * 访问api的身份票据
     */
    private String accessToken;

    /**
     * accessToken 的过期时间
     */
    private Long accessTokenExpireTime;

    /**
     * 公众号功能列表
     */
    private String funcList;

    /**
     * 功能开通情况
     */
    private String businessInfo;

    /**
     * 授权方昵称
     */
    private String nickName;

    /**
     * 授权方头像
     */
    private String headImg;

    /**
     * 0表示小程序
     */
    private int serviceTypeInfo;

    /**
     * 授权方认证类型，-1代表未认证，0代表微信认证，1代表新浪微博认证，2代表腾讯微博认证，3代表已资质认证通过但还未通过名称认证，
     * 4代表已资质认证通过、还未通过名称认证，但通过了新浪微博认证，5代表已资质认证通过、还未通过名称认证，但通过了腾讯微博认证
     */
    private int verifyTypeInfo;

    /**
     * 授权方公众号的原始ID
     */
    private String userName;

    /**
     * 主体名称
     */
    private String principalName;

    /**
     * 介绍
     */
    private String signature;

    /**
     * 二维码
     */
    private String qrCodeUrl;

    /**
     * 当前代码版本
     */
    private String currentCodeVersion;

    /**
     * 当前代码版本描述
     */
    private String codeDescription;

    /**
     * 代码版本更新时间
     */
    private Date codeVersionReleaseTime;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 最后变更时间
     */
    private Date updateTime;

    public boolean isAccessTokenInvalid(){
        return System.currentTimeMillis() > accessTokenExpireTime;
    }

    public boolean isAccessTokenNeedRefresh(){
        return System.currentTimeMillis() > accessTokenExpireTime - 10 * 60 * 1000;
    }
    
    public boolean isEverReleased(){
        return !Strings.isNullOrEmpty(currentCodeVersion);
    }
}
