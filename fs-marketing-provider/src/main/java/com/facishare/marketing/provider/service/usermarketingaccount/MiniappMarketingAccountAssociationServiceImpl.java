package com.facishare.marketing.provider.service.usermarketingaccount;

import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.outapi.arg.AssociateMiniappAndCustomerArg;
import com.facishare.marketing.outapi.arg.result.AssociateMiniappAndCustomerResult;
import com.facishare.marketing.outapi.arg.result.AssociateMiniappModel.*;
import com.facishare.marketing.outapi.arg.result.DuplicateSearchAndAssociateAccountArg;
import com.facishare.marketing.outapi.service.MiniappMarketingAccountAssociationService;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 12/12/2018
 */
@Service("miniappMarketingAccountAssociationService")
@Slf4j
public class MiniappMarketingAccountAssociationServiceImpl implements MiniappMarketingAccountAssociationService {
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    public Result<AssociateMiniappResult> associateMiniapp(AssociateMiniappArg associateMiniappArg) {
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(associateMiniappArg.getEa());
        associationArg.setPhone(associateMiniappArg.getPhone());
        associationArg.setAssociationId(associateMiniappArg.getUid());
        associationArg.setType(ChannelEnum.MINIAPP.getType());
        associationArg.setTriggerAction("associateMiniapp");
        associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        if (associationResult == null) {
            return Result.newSuccess();
        }
        AssociateMiniappResult result = new AssociateMiniappResult();
        if(associationResult != null) {
            result.setUserMarketingId(associationResult.getUserMarketingAccountId());
            result.setUserMarketingMiniappAccountRelationId(associationResult.getRelationId());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<AssociateMiniappAndContactResult> associateMiniappAndContact(AssociateMiniappAndContactArg associateMiniappAndContactArg) {
        associateMiniappAndContactArg.validate();
        
        Optional<String> optionalUserMarketingId = userMarketingAccountRelationManager.bindMiniappUserAndContact(associateMiniappAndContactArg.getEa(), associateMiniappAndContactArg.getUid(), associateMiniappAndContactArg.getCrmContactId(), associateMiniappAndContactArg.getPhone(), "associateMiniappAndContact");
        AssociateMiniappAndContactResult result = new AssociateMiniappAndContactResult();
        result.setContactUserMarketingId(optionalUserMarketingId.orElse(null));
        result.setMiniappUserMarketingId(optionalUserMarketingId.orElse(null));
        return Result.newSuccess(result);
    }

    @Override
    public Result<AssociateMiniappAndLeadResult> associateMiniappAndLead(AssociateMiniappAndLeadArg associateMiniappAndLeadArg) {
        associateMiniappAndLeadArg.validate();
        Optional<String> optionalUserMarketingId = userMarketingAccountRelationManager.bindMiniappUserAndLead(associateMiniappAndLeadArg.getEa(), associateMiniappAndLeadArg.getUid(), associateMiniappAndLeadArg.getCrmLeadId(), associateMiniappAndLeadArg.getPhone(), "miniappMarketingAccountAssociationService");
        AssociateMiniappAndLeadResult associateMiniappAndLeadResult = new AssociateMiniappAndLeadResult();
        associateMiniappAndLeadResult.setLeadUserMarketingId(optionalUserMarketingId.orElse(null));
        associateMiniappAndLeadResult.setMiniappUserMarketingId(optionalUserMarketingId.orElse(null));
        return Result.newSuccess(associateMiniappAndLeadResult);
    }

    @Override
    public Result<AssociateMiniappAndCustomerResult> associateMiniappAndCustomer(AssociateMiniappAndCustomerArg associateMiniappAndCustomerArg) {
        associateMiniappAndCustomerArg.validate();
        Optional<String> optionalUserMarketingId = userMarketingAccountRelationManager.bindMiniappUserAndCustomer(associateMiniappAndCustomerArg.getEa(), associateMiniappAndCustomerArg.getUid(), associateMiniappAndCustomerArg.getCrmCustomerId(), associateMiniappAndCustomerArg.getPhone(), "associateMiniappAndCustomer");
        AssociateMiniappAndCustomerResult associateMiniappAndCustomerResult = new AssociateMiniappAndCustomerResult();
        associateMiniappAndCustomerResult.setCustomerUserMarketingId(optionalUserMarketingId.orElse(null));
        associateMiniappAndCustomerResult.setMiniappUserMarketingId(optionalUserMarketingId.orElse(null));
        return Result.newSuccess(associateMiniappAndCustomerResult);
    }

    @Override
    public Result duplicateSearchAndAssociateAccount(DuplicateSearchAndAssociateAccountArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getEa()) || StringUtils.isBlank(arg.getApiName()) || MapUtils.isEmpty(arg.getData()) || StringUtils.isBlank(arg.getCustomizeFormDataUserId())) {
            return Result.newSuccess();
        }
        customizeFormDataManager.handleRelatedDuplicateSearchData(arg.getEa(), arg.getApiName(), arg.getData(), arg.getCustomizeFormDataUserId());
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> duplicateSearchAndCheckKeepSave(DuplicateSearchAndAssociateAccountArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getEa()) || StringUtils.isBlank(arg.getApiName()) || MapUtils.isEmpty(arg.getData()) || StringUtils.isBlank(arg.getCustomizeFormDataUserId())) {
            return Result.newSuccess(false);
        }
        boolean keepSave = customizeFormDataManager.handleRelatedDuplicateSearchData(arg.getEa(), arg.getApiName(), arg.getData(), arg.getCustomizeFormDataUserId());
        return Result.newSuccess(keepSave);
    }
}
