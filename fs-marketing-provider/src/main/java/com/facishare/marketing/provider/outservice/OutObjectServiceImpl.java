package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.outapi.service.OutObjectService;

import com.facishare.marketing.provider.manager.kis.ObjectManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service("outObjectService")
public class OutObjectServiceImpl implements OutObjectService {

    @Autowired
    private ObjectManager objectManager;

    @Override
    @FilterLog
    public Result<Map<String, String>> getObjectName(String ea, Map<Integer, List<String>> objectTypeToObjectIdListMap) {
        return Result.newSuccess(objectManager.getObjectName(ea, objectTypeToObjectIdListMap));
    }
}
