package com.facishare.marketing.provider.service.kis;

import static com.facishare.mankeep.common.enums.PhotoTargetTypeEnum.ACTIVITY_SIGN_QR_CODE;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.ListActivitiesArg;
import com.facishare.marketing.api.arg.ListBriefMarketingEventsArg;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.arg.conference.QueryActivityEnrollTimeArg;
import com.facishare.marketing.api.arg.conference.SignInArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityListArg;
import com.facishare.marketing.api.arg.marketingactivity.ActivityVO;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.ActivityListResult;
import com.facishare.marketing.api.result.ActivityResult;
import com.facishare.marketing.api.result.EnterpriseObjectAmountStatisticResult;
import com.facishare.marketing.api.result.FieldMappingResult;
import com.facishare.marketing.api.result.WebActivityListUnitResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.conference.CheckSignInStatusResult;
import com.facishare.marketing.api.result.conference.GetEnrollTimeResult;
import com.facishare.marketing.api.result.conference.SignInResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.kis.ActivityService;
import com.facishare.marketing.api.arg.conference.CheckSignInStatusArg;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.appMenu.PaasObjectRuleVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.appMenu.AppMenuAccessibleRuleEnum;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldValue;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.FormHeadSetting;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.ImageInfo;
import com.facishare.marketing.common.util.ImageInfoHelper;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.ActivityTemplateEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.EnterpriseObjectAmountStatisticEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.KisPermissionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.netty.util.internal.StringUtil;

import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类描述 :  活动邀请服务实现
 *
 * <AUTHOR>
 * @Time 2019/03/08
 */
@Service("kisActivityService")
@Slf4j
public class ActivityServiceImpl implements ActivityService {
    @Autowired
    private EnterpriseObjectAmountStatisticDao enterpriseObjectAmountStatisticDao;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private ActivityTemplateDao activityTemplateDao;
    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private ObjectFieldMappingDAO objectFieldMappingDAO;

    @Autowired
    private MarketingActivityManager marketingActivityManager;

    @Autowired
    private KisPermissionManager kisPermissionManager;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private ActivityManager activityManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;

    @Override
    public Result<ActivityListResult> webListActivities(String ea, Integer fsUserId, Integer ei, ListActivitiesArg vo) {
        Integer pageSize = vo.getPageSize();
        Integer pageNum = vo.getPageNum();
        Integer state = vo.getState();
        String title = vo.getTitle();
        List<WebActivityListUnitResult> webActivityListUnitResults = new ArrayList<>();
        Page page = new Page(pageNum, pageSize, true);
        List<ActivityEntity> activityEntityList = activityDAO.listActivitiesByStatueAndState(ea, title, state, 1, page);

        ActivityListResult result = new ActivityListResult();
        result.setTotalCount(page.getTotalNum());
        result.setTime(new Date().getTime());
        result.setCurrentPage(page.getPageNo());
        result.setPageSize(page.getPageSize());
        result.setRecordSize(page.getTotalNum());
        result.setPageTotal(page.getTotalPage() / page.getPageSize() + 1);
        result.setActivityDetailResults(webActivityListUnitResults);

        String appAdminName = kisPermissionManager.lastestAppAdminName(ea, ei);
        result.setAppAdminName(appAdminName);

        if (activityEntityList.size() == 0) {
            return new Result<>(SHErrorCode.SUCCESS, result);
        }

        List<String> objectIds = Lists.newArrayList();
        activityEntityList.forEach(value -> objectIds.add(value.getId()));
        List<EnterpriseObjectAmountStatisticResult> enterpriseObjectAmountStatisticResultList = getEnterpriseObjectAmountStatisticVOListByEaAndObjectTypeAndObjectIds(ea,
            ObjectTypeEnum.ACTIVITY.getType(), objectIds);


        Map<String, Integer> materielMap = Maps.newHashMap();
        activityEntityList.forEach(value -> materielMap.put(value.getId(), ObjectTypeEnum.ACTIVITY.getType()));
        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> marketingActivityObjectInfoMap = marketingActivityManager.getActivityIdsByObject(materielMap, ea, fsUserId);

        for(int i = 0; i < activityEntityList.size(); i++) {
            ActivityEntity value = activityEntityList.get(i);
            photoManager.queryActivityPhotoUrl(value);
            WebActivityListUnitResult webActivityListUnitResult = BeanUtil.copy(value, WebActivityListUnitResult.class);
            webActivityListUnitResult.setStartTime(value.getStartTime().getTime());
            webActivityListUnitResult.setEndTime(value.getEndTime().getTime());
            if (value.getEnrollEndTime() != null) {
                webActivityListUnitResult.setEnrollEndTime(value.getEnrollEndTime().getTime());
            }
            if (value.getIsNeedSign() == ActivitySignEnum.NEED_SIGN.getType()) {
                List<PhotoEntity> photoEntity = photoDAO.listByTargetIdsAndTargetType(value.getId(), ACTIVITY_SIGN_QR_CODE.getType());
                if (CollectionUtils.isEmpty(photoEntity) || StringUtil.isNullOrEmpty(photoEntity.get(0).getPath())) {
                    log.warn("ActivityServiceImpl.webListActivities need to sign in but havn't qrcode arg ->{}", vo);
                    webActivityListUnitResult.setQrUrl("");
                    webActivityListUnitResult.setQrApath("");
                }
                if (!CollectionUtils.isEmpty(photoEntity) && !StringUtil.isNullOrEmpty(photoEntity.get(0).getPath())) {
                    webActivityListUnitResult.setQrUrl(fileV2Manager.getUrlByPath(photoEntity.get(0).getPath(), null, false));
                    webActivityListUnitResult.setQrApath(photoEntity.get(0).getPath());
                }
            }
            if (value.getIsNeedSign() == ActivitySignEnum.NOT_NEED_SIGN.getType()) {
                webActivityListUnitResult.setQrUrl("");
                webActivityListUnitResult.setQrApath("");
            }
            // 签到人数
            Integer signers = activityEnrollDataDAO.getSignInStatisticalData(webActivityListUnitResult.getId());
            webActivityListUnitResult.setSigner(signers);
            //
            webActivityListUnitResult.setState(judgeState(value.getStartTime(), value.getEndTime()));
            List<EnterpriseObjectAmountStatisticResult> enterpriseObjectAmountStatisticResultListTemp = enterpriseObjectAmountStatisticResultList.stream()
                .filter(enterpriseObjectAmountStatisticResult -> enterpriseObjectAmountStatisticResult.getObjectId().equals(value.getId())).collect(Collectors.toList());
            if (!enterpriseObjectAmountStatisticResultListTemp.isEmpty()) {
                webActivityListUnitResult.setLookUpCount(enterpriseObjectAmountStatisticResultListTemp.get(0).getLookUpCount());
                webActivityListUnitResult.setEmployeeForwardCount(enterpriseObjectAmountStatisticResultListTemp.get(0).getEmployeeForwardCount());
                webActivityListUnitResult.setForwardCount(enterpriseObjectAmountStatisticResultListTemp.get(0).getForwardCount());
            }
            // 设置营销活动
            List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = marketingActivityObjectInfoMap.get(value.getId());
            if(CollectionUtils.isNotEmpty(objectInfoDTO)) {
                webActivityListUnitResult.setMarketingActivityId(objectInfoDTO.get(0).getId());
                webActivityListUnitResult.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                webActivityListUnitResult.setMarketingActivityCount(objectInfoDTO.size());
            }

            // 查询报名人数（为保证数据一致性暂不使用表单字段）
            webActivityListUnitResult.setEnrollCount(customizeFormDataUserDAO.getCustomizeFormDataUserCountByObject(value.getId(), ObjectTypeEnum.ACTIVITY.getType()));

            webActivityListUnitResults.add(webActivityListUnitResult);
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<ActivityResult> getActivityInfoById(String activityId) {
        ActivityEntity activity = activityDAO.getById(activityId);
        photoManager.queryActivityPhotoUrl(activity);
        if (activity == null) {
            log.error("ActivityServiceImpl.getActivityInfoById activity is null");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (ActivityStatusEnum.DELETED.getStatus() == activity.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
        }
        ActivityResult activityResult = new ActivityResult();
        BeanUtils.copyProperties(activity, activityResult);
        // 查询物料关联表单
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(activity.getEa(), activityId, ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity != null) {
            FormHeadSetting formHeadSetting = customizeFormDataEntity.getFormHeadSetting();
            ActivityResult.FormData formData = new ActivityResult.FormData();
            formData.setFormId(customizeFormDataEntity.getId());
            formData.setFormTitle(formHeadSetting.getTitle());
            activityResult.setFormData(formData);
            activityResult.setActivityEnrollDescribe(customizeFormDataEntity.getFormBodySetting());
            activityResult.setFormTitle(formHeadSetting.getTitle());
            activityResult.setWelcomeMsg(formHeadSetting.getIntroduce());
            activityResult.setEnrollLeadsFieldMappings(BeanUtil.copy(customizeFormDataEntity.getCrmFormFieldMapV2(), FieldMappingResult.class));
            activityResult.setEnrollLeadsTargetObjectRecordType(customizeFormDataEntity.getCrmRecordType());
            activityResult.setBackgroundImageUrl(CollectionUtils.isNotEmpty(formHeadSetting.getHeadPhotoPath()) ? fileV2Manager.getUrlByPath(formHeadSetting.getHeadPhotoPath().get(0), null, false) : null);
        }
        activityResult.setStartTimestamp(DateUtil.getTimeStamp(activity.getStartTime()));
        activityResult.setEndTimestamp(DateUtil.getTimeStamp(activity.getEndTime()));
        activityResult.setEnrollEndTimestamp(DateUtil.getTimeStamp(activity.getEnrollEndTime()));
        activityResult.setCreateTimestamp(DateUtil.getTimeStamp(activity.getCreateTime()));
        activityResult.setUpdateTimestamp(DateUtil.getTimeStamp(activity.getUpdateTime()));
        List<ActivityResult.ImageInfo> imageInfoList = Lists.newArrayList();
        activityResult.setImageInfoList(imageInfoList);
        // 获取图片宽高
        if (CollectionUtils.isNotEmpty(activityResult.getActivityDetail())) {
            String url = null;
            for (FieldValue fieldValue : activity.getActivityDetail()) {
                if (fieldValue.getName().equals("imageUrl")) {
                    url = fieldValue.getValue();
                    byte[] bytes = fileV2Manager.downloadFileByUrl(url, null);
                    ImageInfo imageInfo = ImageInfoHelper.getImageInfo(bytes);
                    ActivityResult.ImageInfo imageInfoResult = new  ActivityResult.ImageInfo();
                    imageInfoResult.setImageH(imageInfo == null ? null : imageInfo.getHeight());
                    imageInfoResult.setImageW(imageInfo == null ? null : imageInfo.getWidth());
                    imageInfoList.add(imageInfoResult);
                }
            }
        }
        if (StringUtils.isNotEmpty(activity.getActivityTemplateId())) {
            ActivityTemplateEntity activityTemplateEntity =  activityTemplateDao.get(activity.getActivityTemplateId());
            activityResult.setBackgroundImageUrl(activityTemplateEntity != null ? activityTemplateEntity.getBackgroundImageUrl() : null);
            activityResult.setActivityTemplateDescriptionList(activityTemplateEntity != null ? activityTemplateEntity.getCoverTextDescribe() : null);
            activityResult.setTemplateName(activityTemplateEntity != null ? activityTemplateEntity.getTemplateName() : null);
        }
     //   activityResult.setShareImg(fileV2Manager.zoom(activityResult.getCoverImageUrl(), null, 100));
        // 查询报名人数（为保证数据一致性暂不使用表单字段）
        activityResult.setEnrollCount(customizeFormDataUserDAO.getCustomizeFormDataUserCountByObject(activityId, ObjectTypeEnum.ACTIVITY.getType()));

        return new Result<>(SHErrorCode.SUCCESS, activityResult);
    }

    @Override
    public Result<SignInResult> signIn(SignInArg signInArg) {
        return activityManager.activitySignIn(signInArg);
    }

    @Override
    public Result<CheckSignInStatusResult> checkSignInStatus(CheckSignInStatusArg arg) {
        return activityManager.checkSignInStatus(arg);
    }

    private Integer judgeState(Date startTime, Date endTime) {
        Date now = DateUtil.now();
        if (startTime != null && now.getTime() < startTime.getTime()) {
            return 2; // 即将开始
        } else if (startTime != null && endTime != null && now.getTime() >= startTime.getTime() && now.getTime() <= endTime.getTime()) {
            return 3;  // 进行中
        } else if (endTime != null && now.getTime() > endTime.getTime()) {
            return 4;  // 结束
        } else {
            return 0;
        }
    }

    private List<EnterpriseObjectAmountStatisticResult> getEnterpriseObjectAmountStatisticVOListByEaAndObjectTypeAndObjectIds(String ea, Integer objectType, List<String> objectIds) {
        List<EnterpriseObjectAmountStatisticEntity> enterpriseObjectAmountStatisticEntityList = enterpriseObjectAmountStatisticDao.getByEaAndObjectTypeAndObjectIds(ea, objectType, objectIds);
        List<EnterpriseObjectAmountStatisticResult> enterpriseObjectAmountStatisticResultList = BeanUtil.copy(enterpriseObjectAmountStatisticEntityList, EnterpriseObjectAmountStatisticResult.class);
        return enterpriseObjectAmountStatisticResultList;
    }

    @Override
    public Result<PageResult<ActivityVO>> listActivities(ActivityListArg arg) {
        PageResult<ActivityVO> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        ArrayList<ActivityVO> result = new ArrayList<>();
        pageResult.setResult(result);
        ListBriefMarketingEventsArg paasArg = new ListBriefMarketingEventsArg();
        paasArg.setNlikeEventTypeList(Arrays.asList(MarketingEventEnum.LIVE_MARKETING.getEventType(), MarketingEventEnum.MEETING_SALES.getEventType(), MarketingEventEnum.AD_MARKETING.getEventType()));
        ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false);
        paasArg.setOrderByList(Lists.newArrayList(orderBy));
        paasArg.setFilterData(arg.getFilterData());
        String title = arg.getTitle();
        if (StringUtils.isNotBlank(title)) {
            paasArg.setName(title);
        }
        String ea = arg.getFsEa();
        com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResultPage;
        // 菜单id不为空，走菜单的展示控制
        if (StringUtils.isNotBlank(arg.getMenuId())) {
            marketingEventsBriefResultPage = getResultByMenuId(ea, arg, paasArg);
        } else {
            // 这里应该都不会走到这里了
            PageArg pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
            marketingEventsBriefResultPage = marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(arg.getFsEa()), SuperUserConstants.USER_ID, paasArg, pageArg);
        }
        if (marketingEventsBriefResultPage != null && CollectionUtils.isNotEmpty(marketingEventsBriefResultPage.getData())) {
            marketingEventsBriefResultPage.getData().forEach(e->{
                ActivityVO vo = new ActivityVO();
                BeanUtil.copyPropertiesIgnoreNull(e, vo);
                result.add(vo);
            });
            pageResult.setTotalCount(marketingEventsBriefResultPage.getTotalCount());
        }
        return Result.newSuccess(pageResult);
    }

    private com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> getResultByMenuId(String ea, ActivityListArg arg,  ListBriefMarketingEventsArg paasArg) {

        int ei = eieaConverter.enterpriseAccountToId(ea);

        Result<PaasObjectRuleVO>  paasObjectRuleResult = appMenuTemplateService.getPaasObjectRule(ea, arg.getMenuId(), ObjectTypeEnum.ACTIVITY.getType());
        PaasObjectRuleVO paasObjectRuleVO = paasObjectRuleResult.isSuccess() ? paasObjectRuleResult.getData() : null;

        if (paasObjectRuleVO == null) {
            com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> pageResult = new com.facishare.marketing.api.result.PageResult<>();
            pageResult.setTotalCount(0);
            pageResult.setData(Lists.newArrayList());
            return pageResult;
        }

        com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResultPage = null;

        if (paasObjectRuleVO.getAppMenuTagVO() != null) {
            // 内容标签过滤处理
            AppMenuTagVO appMenuTagVO = paasObjectRuleVO.getAppMenuTagVO();
            Integer type = appMenuTagVO.getTagOperator();
            List<String> materialTagIds = appMenuTagVO.getTagIdList();
            Page<String> page = null;
            PageArg paasPageArg = null;
            // 如果传了关键词，标签不用分页，在paas在分页
            if (StringUtils.isBlank(arg.getTitle())) {
                page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
            } else {
                paasPageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
            }
            List<String> ids;
            if (type == 1) {
                ids = materialTagRelationDao.queryByAnyTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.ACTIVITY.getType()), page);
            } else {
                ids = materialTagRelationDao.queryByAllTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.ACTIVITY.getType()), page);
            }
            if (CollectionUtils.isEmpty(ids)) {
                com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> pageResult = new com.facishare.marketing.api.result.PageResult<>();
                pageResult.setTotalCount(0);
                pageResult.setData(Lists.newArrayList());
                return pageResult;
            }
            paasArg.setMarketingEventIds(ids);
            marketingEventsBriefResultPage = marketingEventManager.listMarketingEvents(ei, SuperUserConstants.USER_ID, paasArg, paasPageArg);
        } else if (AppMenuAccessibleRuleEnum.ALL.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            // 全部
            PageArg pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
            marketingEventsBriefResultPage = marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(arg.getFsEa()), SuperUserConstants.USER_ID, paasArg, pageArg);
        }  else if (AppMenuAccessibleRuleEnum.PAAS_OBJECT_ACCESSIBLE.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            // 遵循市场活动对象权限
            PageArg pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
            marketingEventsBriefResultPage = marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(arg.getFsEa()), arg.getFsUserId(), paasArg, pageArg);
        } else if (AppMenuAccessibleRuleEnum.OBJECT_FILTER.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            // 对象条件
            FilterData filterData = paasArg.getFilterData();
            if (filterData == null) {
                filterData = new FilterData();
            }
            SearchTemplateQuery query = filterData.getQuery();
            if (query == null) {
                query = new SearchTemplateQuery();
            }
            List<Filter> filterList = query.getFilters();
            if (filterList == null) {
                filterList = Lists.newArrayList();
            }
            filterList.addAll(paasObjectRuleVO.getFilters());
            query.setFilters(filterList);
            filterData.setQuery(query);
            PageArg pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
            paasArg.setFilterData(filterData);
            marketingEventsBriefResultPage = marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(arg.getFsEa()), SuperUserConstants.USER_ID, paasArg, pageArg);
        }
        return marketingEventsBriefResultPage;
    }

    @Override
    public Result<GetEnrollTimeResult> queryActivityEnrollEndTime(QueryActivityEnrollTimeArg arg) {
        return activityManager.queryActivityEnrollEndTime(arg);
    }

}
