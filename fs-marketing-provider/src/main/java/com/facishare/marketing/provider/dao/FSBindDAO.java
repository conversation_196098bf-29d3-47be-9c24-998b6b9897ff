package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.FSBindEntity;
import java.util.List;

import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeStatisticEntity;
import org.apache.ibatis.annotations.*;

public interface FSBindDAO {
    @Select("<script>"
            + "SELECT * FROM fs_bind  where  fs_ea =#{ea} and type in  "
            +   "<foreach collection = 'typeList' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "  <foreach collection=\"employeeIds\" open=\" and fs_user_id in(\" close=\")\"\n"
            + "     item=\"id\" separator=\",\">\n" + "     #{id}\n" + "   </foreach>   "
            + "</script>")
    List<FSBindEntity> queryFSBindByEmployeeIds(@Param("ea") String ea,@Param("employeeIds") List<Integer> employeeIds, @Param("typeList") List<Integer> typeList, @Param("appId") String appId);

    @Select("<script>"
            + "SELECT COUNT(*) FROM fs_bind  where  fs_ea =#{ea} and type = #{type} "
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "  <foreach collection=\"employeeIds\" open=\" and fs_user_id in(\" close=\")\"\n"
            + "     item=\"id\" separator=\",\">\n" + "     #{id}\n" + "   </foreach>   "
            + "</script>")
    int queryFSBindByEmployeeIdsCount(@Param("ea") String ea,@Param("employeeIds") List<Integer> employeeIds, @Param("type") Integer type, @Param("appId") String appId);

    @Select("<script>"
            + "SELECT COUNT(*) FROM fs_bind  where  fs_ea =#{ea} "
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "  <foreach collection=\"employeeIds\" open=\" and fs_user_id in(\" close=\")\"\n"
            + "     item=\"id\" separator=\",\">\n" + "     #{id}\n" + "   </foreach>   "
           + "</script>")
    int queryFSBindByEmployeeIdsCountWithoutType(@Param("ea") String ea, @Param("appId") String appId, @Param("employeeIds") List<Integer> employeeIds);

    @Select("<script>"
            + "SELECT fs_user_id FROM fs_bind  where  fs_ea =#{ea} "
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "  <foreach collection=\"employeeIds\" open=\" and fs_user_id in(\" close=\")\"\n"
            + "     item=\"id\" separator=\",\">\n" + "     #{id}\n" + "   </foreach>   "
            + "</script>")
    List<Integer> queryFSUserIdsBindByEmployeeIdsWithoutType(@Param("ea") String ea, @Param("appId") String appId, @Param("employeeIds") List<Integer> employeeIds);
    @Select("<script>"
            + "SELECT * FROM fs_bind  where  fs_ea =#{ea} and type = #{type} "
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "</script>")
    List<FSBindEntity> queryFSBindByType(@Param("ea") String ea, @Param("type") Integer type, @Param("appId") String appId);

    @Select("<script>  "
            + " SELECT fs_user_id  FROM fs_bind  where  fs_ea =#{ea} and type in "
            +   "<foreach collection = 'typeList' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "</script>")
    List<Integer> queryFSBindByFsUserId(@Param("ea") String ea, @Param("typeList") List<Integer> typeList, @Param("appId") String appId);

    @Select("<script>  "
            + " SELECT uid  FROM fs_bind  where  fs_ea =#{ea} and fs_user_id = #{fsUserId} and type = #{type} "
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "</script>")
    String queryUidByFsUserIdAndFsEa(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("type") Integer type, @Param("appId") String appId);


    @Select(" SELECT uid  FROM fs_bind  where  fs_ea =#{ea} and fs_user_id = #{fsUserId} and type = #{type}")
    String queryDingUidByFsUserIdAndEa(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("type") Integer type);


    @FilterLog
    @Select("<script>  "
            + " SELECT * FROM fs_bind where uid =#{uid} "
            + "</script>")
    FSBindEntity queryFSBindByUid(@Param("uid") String uid);

    @Select("<script>  "
            + " SELECT * FROM fs_bind where uid =#{uid} and fs_ea = #{ea}"
            + "</script>")
    FSBindEntity queryFSBindByUidAndEa(@Param("uid") String uid, @Param("ea") String ea);

    @Select("<script>  "
            + " SELECT * FROM fs_bind where fs_ea =#{ea} and fs_user_id = #{fsUserId} and type = #{type}"
            + "         <if test=\"appId != null\">\n"
            + "             AND app_id = #{appId}\n"
            + "          </if>\n"
            + "</script>")
    FSBindEntity queryFSBindByFsEaAndFsUserId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId, @Param("type") Integer type, @Param("appId") String appId);

    @Insert("INSERT INTO fs_bind(\n" +
            "        \"uid\",\n" +
            "        \"phone\",\n" +
            "        \"fs_ea\",\n" +
            "        \"fs_user_id\",\n" +
            "        \"fs_corp_id\",\n" +
            "        \"type\",\n" +
            "        \"app_id\"\n" +
            "        )\n" +
            "        VALUES (\n" +
            "        #{uid},\n" +
            "        #{phone},\n" +
            "        #{fsEa},\n" +
            "        #{fsUserId},\n" +
            "        #{fsCorpId},\n" +
            "        #{type},\n" +
            "        #{appId}\n" +
            "        ) ON CONFLICT DO NOTHING")
    int insert(FSBindEntity fsBindEntity);

    @Update("<script>" +
            "UPDATE fs_bind\n" +
            "        <set>\n" +
            "            <if test=\"phone != null\">\n" +
            "                \"phone\" = #{phone},\n" +
            "            </if>\n" +
            "            <if test=\"fsEa != null\">\n" +
            "                \"fs_ea\" = #{fsEa},\n" +
            "            </if>\n" +
            "            <if test=\"fsUserId != null\">\n" +
            "                \"fs_user_id\" = #{fsUserId},\n" +
            "            </if>\n" +
            "            <if test=\"fsCorpId != null\">\n" +
            "                \"fs_corp_id\" = #{fsCorpId}\n" +
            "            </if>\n" +
            "        </set>\n" +
            "        WHERE uid = #{uid}" +
            "</script>")
    int update(FSBindEntity fsBindEntity);

    @Select("SELECT COUNT(*) FROM fs_bind  where  fs_ea = #{ea} and type = #{type}")
    Integer queryFSBindDingCount(@Param("ea") String ea,  @Param("type") Integer type);

    @Update("UPDATE fs_bind SET app_id=#{appId} WHERE uid=#{uid}")
    void updateFsBindAppId(@Param("appId") String appId, @Param("uid") String uid);

    @Update("UPDATE fs_bind SET fs_user_id=#{fsUserId} WHERE uid=#{uid}")
    void updateFsBindFsUserId(@Param("fsUserId") Integer fsUserId, @Param("uid") String uid);


    @Select("UPDATE fs_bind SET phone=#{phone} WHERE uid=#{uid}")
    void updateFsBindPhone(@Param("phone") String phone, @Param("uid") String uid);

    @Select("SELECT * FROM fs_bind where fs_ea =#{ea} and fs_user_id = #{fsUserId} and fs_corp_id=#{fsCorpId}  and type = #{type}")
    FSBindEntity queryFSBindByFsInfo(@Param("ea") String fsEa,@Param("fsUserId") Integer fsUserId, @Param("fsCorpId") Integer fsEi,@Param("type") int type);

    @Delete("DELETE FROM fs_bind WHERE uid=#{uid}")
    int delFSBindByUid(@Param("uid") String uid);

    @Select("SELECT uid FROM fs_bind where fs_ea =#{ea} and fs_user_id = #{fsUserId} order by create_time desc limit 1 ")
    String queryByFsEaAndUserId(@Param("ea") String ea, @Param("fsUserId") Integer fsUserId);

    @Select("SELECT * FROM fs_bind WHERE fs_ea=#{ea} AND fs_user_id>=100000000")
    List<FSBindEntity> getFsBindByEa(@Param("ea")String ea);

    @Select("SELECT * FROM fs_bind WHERE fs_ea=#{ea}")
    List<FSBindEntity> getAllFsBindByEa(@Param("ea")String ea);

    @Update("UPDATE fs_bind SET fs_user_id=#{userId} WHERE uid=#{uid}")
    int updateUserIdById(@Param("uid") String uid, @Param("userId") Integer userId);

    @Select("SELECT uid FROM fs_bind where fs_ea =#{ea} and fs_user_id = #{fsUserId} order by create_time desc limit 1")
    String queryUidByLimitOne(@Param("ea") String ea, @Param("fsUserId") Integer outerQrcodeSpreadFsUid);

    @Select("SELECT * FROM fs_bind WHERE fs_ea =#{ea} AND fs_user_id = #{fsUserId} AND app_id=#{appId}")
    FSBindEntity queryByUserAndAppId(@Param("ea")String ea, @Param("fsUserId")Integer fsUserId, @Param("appId")String appId);

    @Select("<script>"
            + "SELECT * FROM fs_bind WHERE fs_ea=#{ea} AND fs_user_id in "
            + " <foreach open='(' close=')' separator=',' collection='fsUserIdList' index='idx'>"
            + "     #{fsUserIdList[${idx}]}"
            + "</foreach>"
            + "</script>")
    List<FSBindEntity> getByFsUserIdList(@Param("ea")String ea, @Param("fsUserIdList") List<Integer> fsUserIdList);

    @Delete("DELETE FROM fs_bind WHERE fs_ea=#{ea} and fs_user_id =#{userId}")
    int deleteByEaAndFsUserId(@Param("ea") String ea, @Param("userId") Integer userId);

    @Update("UPDATE fs_bind SET uid=#{newUid}, last_modify_time=now() WHERE uid=#{oldUid}")
    int updateUidByOldUid(@Param("oldUid") String oldUid, @Param("newUid") String newUid);

    @Select("SELECT distinct fs_user_id FROM fs_bind WHERE fs_ea=#{ea}")
    List<Integer> getAllFsUserIdByEa(@Param("ea")String ea);
}
