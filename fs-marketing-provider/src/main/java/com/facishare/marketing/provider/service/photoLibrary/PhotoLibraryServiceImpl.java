package com.facishare.marketing.provider.service.photoLibrary;

import com.facishare.marketing.api.result.photoLibrary.*;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.photoLibrary.*;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.api.result.qr.SyncChuangKeTiePosterResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.photoLibrary.PhotoLibraryService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.api.vo.qywx.MomentMessageVO;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.ObjectGroupMobileStatusEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.photoLibrary.PhotoDefaultGroupEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.DisplayOrderDao;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.PhotoSelectorDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.photolibrary.PhotoLibraryQueryParam;
import com.facishare.marketing.provider.dao.photoLibrary.PhotoLibraryDAO;
import com.facishare.marketing.provider.dto.ContentGroupCountDTO;
import com.facishare.marketing.provider.dto.GroupNameObjectIdDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.MomentManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.util.Constant.C_WAREHOUSE_TYPE;

@Slf4j
@Service("photoLibraryService")
public class PhotoLibraryServiceImpl implements PhotoLibraryService{
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private ObjectGroupManager objectGroupManager;
    @Autowired
    private ObjectGroupDAO objectGroupDAO;
    @Autowired
    private DisplayOrderDao displayOrderDao;
    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;
    @Autowired
    private PhotoLibraryDAO photoLibraryDAO;
    @Autowired
    private PhotoSelectorDAO photoSelectorDAO;

    @Autowired
    private MaterialTagManager materialTagManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private MomentManager momentManager;

    @Autowired
    private ObjectTopManager objectTopManager;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @ReloadableProperty("thumbnail.path.black.eas")
    private String thumbnailPathBlackEas;

    public static final int CUSTOME_FILE = 0;
    public static final int SYSTEM_FILE = 1;
    private static final int QUERY_ALL_GROUP = 0;
    private static final int QUERY_ALL_CUSTOM_GROUP = 1;
    private static final int QUERY_SELECTOR = 2;
    // 单个图片上传限制5M
    private static final Long SINGLE_PIC_LIMIT = 1024L * 1024 * 5;


    @Override
    public Result<Void> uploadPhoto(String ea, Integer fsUserId, UploadPhotoArg arg) {
        if (CollectionUtils.isNotEmpty(photoLibraryDAO.getByPhotoName(ea, Lists.newArrayList(arg.getPhotoName())))){
            log.info("PhotoLibraryServiceImpl.uploadPhoto photo exist ea:{} arg:{}", ea, arg);
            return Result.newError(SHErrorCode.PIC_NAME_EXIST);
        }

        byte[] bytes = fileV2Manager.downloadAFile(arg.getPhotoPath(), fsUserId, ea);
        Optional<FileV2Manager.FileManagerPicResult> picResultOpt = fileV2Manager.uploadToApathWithThumbnail(bytes, "png", ea, fsUserId);
        String apath = null;
        if (picResultOpt.isPresent() && !picResultOpt.get().getUrlAPath().startsWith("A_")) {
            apath = fileV2Manager.getApathByTApath(ea, fsUserId, arg.getPhotoPath(), arg.getExt(), "fs-mankeep");
            if (StringUtils.isEmpty(apath)) {
                log.info("PhotoLibraryServiceImpl.uploadPhoto failed get thmubnail failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return Result.newError(SHErrorCode.PIC_CONVERT_FAILED);
            }
        }else {
            apath = picResultOpt.get().getUrlAPath();
        }
        String thumbnailPath = apath;
        if (picResultOpt.isPresent() && picResultOpt.get() != null && picResultOpt.get().getThumbUrlApath() != null){
            thumbnailPath = picResultOpt.get().getThumbUrlApath();
        }

        String cpath = null;
        String cdnUrl = null;
        /*
        String cpath = fileV2Manager.uploadToCpathOrNpath(ea, bytes, true);
        String cdnUrl = fileV2Manager.getUrlByCPath(ea, cpath);
        */
        String id = null;
        if (arg.getSource() == PhotoLibraryService.PHOTO_LIBRARY_SOURCE) {
            PhotoLibraryEntity entity = new PhotoLibraryEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setPhotoName(arg.getPhotoName());
            entity.setPhotoPath(apath);
            entity.setThumbailPath(thumbnailPath);
            entity.setPhotoSize(arg.getPhotoSize());
            entity.setExt(arg.getExt());
            entity.setType(CUSTOME_FILE);
            entity.setCdnUrl(cdnUrl);
            id = entity.getId();
            photoLibraryDAO.insert(entity);
        }else {
            PhotoSelectorEntity entity = new PhotoSelectorEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setPhotoName(arg.getPhotoName());
            entity.setPhotoPath(apath);
            entity.setThumbailPath(thumbnailPath);
            entity.setPhotoSize(arg.getPhotoSize());
            entity.setExt(arg.getExt());
            entity.setCdnUrl(cdnUrl);
            id = entity.getId();
            photoSelectorDAO.insert(entity);
        }
        
        //上报神策系统
        marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), null, id));
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> uploadAttement(String ea, Integer fsUserId, MomentMessageVO momentMessageVO) {
        List<String> res = new ArrayList<>();
        if (null != momentMessageVO.getMsgType() && 1 == momentMessageVO.getMsgType()) {
            MomentMessageVO.Link linkVo = momentMessageVO.getLink();
            String mediaId = redisManager.getQywxAttachmentMediaIdByEa(linkVo.getPicPath(),ea);
            if (StringUtils.isBlank(mediaId)) {
                byte[] data = fileV2Manager.getByteDataByUrl(linkVo.getPicUrl());
                mediaId = momentManager.uploadAttachment(momentManager.getOrCreateAccessToken(ea), linkVo.getPicPath(), "image", data);
            }
            if (StringUtils.isEmpty(mediaId)) {
                res.add(linkVo.getPicPath());
            }
        }
        Map<String, String> materialIdToQywxId = null;
        if (null != momentMessageVO.getMsgType() && 2 == momentMessageVO.getMsgType()) {
            if (CollectionUtils.isNotEmpty(momentMessageVO.getImage())) {
                materialIdToQywxId = momentManager.dealImages(momentManager.getOrCreateAccessToken(ea),ea, momentMessageVO);
                if (null == materialIdToQywxId || 0 == materialIdToQywxId.size()) {
                    momentMessageVO.getImage().forEach(l -> res.add(l.getImagePath()));
                } else if (materialIdToQywxId.size() != momentMessageVO.getImage().size()) {
                    for (MomentMessageVO.Image image : momentMessageVO.getImage()) {
                        if (materialIdToQywxId.containsKey(image.getImagePath()) && StringUtils.isNotEmpty(materialIdToQywxId.get(image.getImagePath()))) {
                            continue;
                        }
                        res.add(image.getImagePath());
                    }
                }
            }
        }

        return Result.newSuccess(res);
    }


    @Override
    public Result<Void> batchUploadPhotos(String ea, Integer fsUserId, BatchUploadPhotosArg arg) {
        for (BatchUploadPhotosArg.PhotoInfo photoInfo : arg.getPhotoInfos()) {
            if (photoInfo.getPhotoSize() > SINGLE_PIC_LIMIT) {
                log.warn("batchUploadPhotos fail,arg:{}", arg);
                return Result.newError(SHErrorCode.SINGLE_PIC_LIMIT_EXCEEDED);
            }
        }

        List<String> photoNames = arg.getPhotoInfos().stream().map(photoInfo -> photoInfo.getPhotoName()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(photoLibraryDAO.getByPhotoName(ea, photoNames))){
            log.info("PhotoLibraryServiceImpl.batchUploadPhotos photo exist ea:{} arg:{}", ea, arg);
            return Result.newError(SHErrorCode.PIC_NAME_EXIST);
        }

        List<BatchUploadPhotosArg.PhotoInfo> photoInfos = arg.getPhotoInfos();
        List<PhotoLibraryEntity> photoLibraryEntityList = Lists.newCopyOnWriteArrayList();
        List<PhotoSelectorEntity> photoSelectorEntityList = Lists.newCopyOnWriteArrayList();
        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(photoInfos.size(), "marketing_batchUploadPhotosThread");
        CountDownLatch countDownLatch = new CountDownLatch(photoInfos.size());
        TraceContext context = TraceContext.get();
        for (BatchUploadPhotosArg.PhotoInfo photoInfo : photoInfos) {
            executorService.execute(() -> {
                try {
                    if (context != null ) {
                        TraceContext._set(context);
                    }
                    log.info("上传图片,photoInfo:[{}],ea:[{}]", photoInfo, ea);
                    batchUpLoad(photoInfo, ea, fsUserId, photoLibraryEntityList, photoSelectorEntityList, arg);
                } catch (Exception e) {
                    log.info("batchUpLoad fail,photo:[{}]", photoInfo, e);
                } finally {
                    if (context != null ) {
                        TraceContext.remove();
                    }
                }
                countDownLatch.countDown();
            });
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.warn("exception:",  e);
        }
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        // 图片批量存db
        List<String> photoIds = Lists.newArrayList();
        List<String> libraryPhotoIds = Lists.newArrayList();
        List<String> selectorPhotoIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(photoLibraryEntityList)) {
            photoLibraryDAO.batchInsert(photoLibraryEntityList);
            libraryPhotoIds = photoLibraryEntityList.stream().map(PhotoLibraryEntity::getId).collect(Collectors.toList());
            photoIds.addAll(libraryPhotoIds);
        }
        if (CollectionUtils.isNotEmpty(photoSelectorEntityList)) {
            photoSelectorDAO.batchInsert(photoSelectorEntityList);
            selectorPhotoIds = photoSelectorEntityList.stream().map(PhotoSelectorEntity::getId).collect(Collectors.toList());
            photoIds.addAll(selectorPhotoIds);
        }

        //上报神策系统
        if (CollectionUtils.isNotEmpty(photoLibraryEntityList)) {
            photoLibraryEntityList.stream().forEach(photoLibraryEntity -> {
                //上报神策系统
                marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), null, photoLibraryEntity.getId()));
            });

        }
        if (CollectionUtils.isNotEmpty(photoSelectorEntityList)){
            photoSelectorEntityList.stream().forEach(photoSelectorEntity -> {
                marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), null, photoSelectorEntity.getId()));
            });
        }

        // 设置图片分组
        SetPhotoGroupArg setPhotoGroupArg = new SetPhotoGroupArg();
        setPhotoGroupArg.setGroupId(arg.getGroupId());
        setPhotoGroupArg.setPhotoIds(photoIds);
        setPhotoGroup(ea, fsUserId, setPhotoGroupArg);

        return Result.newSuccess();
    }

    @Override
    public Result<List<CPathMetaDataResult>> batchUploadTcPathPhotos(String ea, Integer fsUserId, BatchUploadPhotosArg arg) {
        for (BatchUploadPhotosArg.PhotoInfo photoInfo : arg.getPhotoInfos()) {
            if (photoInfo.getPhotoSize() > SINGLE_PIC_LIMIT) {
                log.warn("batchUploadTcPathPhotos fail,arg:{}", arg);
                return Result.newError(SHErrorCode.SINGLE_PIC_LIMIT_EXCEEDED);
            }
        }

        List<String> photoNames = arg.getPhotoInfos().stream().map(photoInfo -> photoInfo.getPhotoName()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(photoLibraryDAO.getByPhotoName(ea, photoNames))){
            log.info("PhotoLibraryServiceImpl.batchUploadTcPathPhotos photo exist ea:{} arg:{}", ea, arg);
            return Result.newError(SHErrorCode.PIC_NAME_EXIST);
        }

        List<String> tcPaths = arg.getPhotoInfos().stream().filter(e -> !e.getPhotoPath().startsWith("C_")).map(BatchUploadPhotosArg.PhotoInfo::getPhotoPath).collect(Collectors.toList());
        List<String> cPaths = arg.getPhotoInfos().stream().filter(e -> e.getPhotoPath().startsWith("C_")).map(BatchUploadPhotosArg.PhotoInfo::getPhotoPath).collect(Collectors.toList());
        Map<String, String> cPathMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(tcPaths)) {
            cPathMap.putAll(fileV2Manager.changeListCWarehouseTempToPermanent(ea, tcPaths));
        }
        if (CollectionUtils.isNotEmpty(cPaths)) {
            cPaths.forEach(e -> {
                cPathMap.putIfAbsent(e, e);
            });
        }
        if (MapUtils.isEmpty(cPathMap)) {
            log.info("PhotoLibraryServiceImpl.batchUploadTcPathPhotos changeListCWarehouseTempToPermanent failed ea:{} arg:{}", ea, arg);
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }

        List<PhotoLibraryEntity> photoLibraryEntityList = Lists.newCopyOnWriteArrayList();
        List<PhotoSelectorEntity> photoSelectorEntityList = Lists.newCopyOnWriteArrayList();

        builtPhotoEntity(arg, ea, fsUserId, photoLibraryEntityList, photoSelectorEntityList, cPathMap);
        // 图片批量存db
        List<String> photoIds = Lists.newArrayList();
        List<String> libraryPhotoIds = Lists.newArrayList();
        List<String> selectorPhotoIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(photoLibraryEntityList)) {
            photoLibraryDAO.batchInsert(photoLibraryEntityList);
            libraryPhotoIds = photoLibraryEntityList.stream().map(PhotoLibraryEntity::getId).collect(Collectors.toList());
            photoIds.addAll(libraryPhotoIds);
        }
        if (CollectionUtils.isNotEmpty(photoSelectorEntityList)) {
            photoSelectorDAO.batchInsert(photoSelectorEntityList);
            selectorPhotoIds = photoSelectorEntityList.stream().map(PhotoSelectorEntity::getId).collect(Collectors.toList());
            photoIds.addAll(selectorPhotoIds);
        }

        //上报神策系统
        if (CollectionUtils.isNotEmpty(photoLibraryEntityList)) {
            photoLibraryEntityList.stream().forEach(photoLibraryEntity -> {
                //上报神策系统
                marketingStatLogPersistorManger.sendMaterialData(ea, ObjectTypeEnum.IMAGE.getType(), PhotoLibraryService.PHOTO_LIBRARY_SOURCE, photoLibraryEntity.getId());
            });

        }
        if (CollectionUtils.isNotEmpty(photoSelectorEntityList)){
            photoSelectorEntityList.stream().forEach(photoSelectorEntity -> {
                marketingStatLogPersistorManger.sendMaterialData(ea, ObjectTypeEnum.IMAGE.getType(), PhotoLibraryService.PHOTO_SELECTOR_SOURCE, photoSelectorEntity.getId());
            });
        }

        // 设置图片分组
        SetPhotoGroupArg setPhotoGroupArg = new SetPhotoGroupArg();
        setPhotoGroupArg.setGroupId(arg.getGroupId());
        setPhotoGroupArg.setPhotoIds(photoIds);
        setPhotoGroup(ea, fsUserId, setPhotoGroupArg);
        List<CPathMetaDataResult> result = Lists.newArrayList();
        cPathMap.forEach((key, value) -> {
            CPathMetaDataResult cPathMetaDataResult = new CPathMetaDataResult();
            cPathMetaDataResult.setCPath(key);
            cPathMetaDataResult.setUrl(fileV2Manager.getUrlByCPath(ea, value));
            cPathMetaDataResult.setOriginalPath(value);
            result.add(cPathMetaDataResult);
        });
        return Result.newSuccess(result);
    }

    private void builtPhotoEntity(BatchUploadPhotosArg arg, String ea, Integer fsUserId, List<PhotoLibraryEntity> photoLibraryEntityList, List<PhotoSelectorEntity> photoSelectorEntityList, Map<String, String> cPathMap) {
        for (BatchUploadPhotosArg.PhotoInfo photoInfo : arg.getPhotoInfos()) {
            String cpath = cPathMap.get(photoInfo.getPhotoPath());
            if (StringUtils.isEmpty(cpath)) {
                log.info("PhotoLibraryServiceImpl.uploadPhoto builtPhotoEntity failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return;
            }
            String cdnUrl = fileV2Manager.getUrlByCPath(ea,cpath);
            if (arg.getSource() == PhotoLibraryService.PHOTO_LIBRARY_SOURCE) {
                PhotoLibraryEntity entity = new PhotoLibraryEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setCreateBy(fsUserId);
                entity.setPhotoName(photoInfo.getPhotoName());
                entity.setPhotoPath(cpath);
                entity.setThumbailPath(cpath);
                entity.setPhotoSize(photoInfo.getPhotoSize());
                entity.setExt(photoInfo.getExt());
                entity.setType(CUSTOME_FILE);
                entity.setCdnUrl(cdnUrl);
                photoLibraryEntityList.add(entity);
            }else {
                PhotoSelectorEntity entity = new PhotoSelectorEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setCreateBy(fsUserId);
                entity.setPhotoName(photoInfo.getPhotoName());
                entity.setPhotoPath(cpath);
                entity.setThumbailPath(cpath);
                entity.setPhotoSize(photoInfo.getPhotoSize());
                entity.setExt(photoInfo.getExt());
                entity.setCdnUrl(cdnUrl);
                photoSelectorEntityList.add(entity);
            }
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), photoInfo.getPhotoName(), OperateTypeEnum.ADD);
        }
    }

    private void batchUpLoad(BatchUploadPhotosArg.PhotoInfo photoInfo, String ea, Integer fsUserId, List<PhotoLibraryEntity> photoLibraryEntityList, List<PhotoSelectorEntity> photoSelectorEntityList, BatchUploadPhotosArg arg) {

        List<String> thumbnailPathBlackList = JSONObject.parseArray(thumbnailPathBlackEas, String.class);
        String apath = null;
        String thumbnailPath = null;
        if (thumbnailPathBlackList.contains(ea)) {
            apath = fileV2Manager.getApathByTApath(ea, fsUserId, photoInfo.getPhotoPath(), photoInfo.getExt(), "fs-mankeep");
            thumbnailPath = apath;
            if (StringUtils.isEmpty(apath)) {
                log.info("PhotoLibraryServiceImpl.uploadPhoto failed getApathByTApath failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return;
            }
        } else {
            // 下载、上传图片
            byte[] bytes = fileV2Manager.downloadAFile(photoInfo.getPhotoPath(), fsUserId, ea);
            Optional<FileV2Manager.FileManagerPicResult> picResultOpt = fileV2Manager.uploadToApathWithThumbnail(bytes, "png", ea, fsUserId);
            if (!picResultOpt.isPresent()) {
                log.info("PhotoLibraryServiceImpl.uploadPhoto failed uploadToApathWithThumbnail failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
               return;
            }
            if (!picResultOpt.get().getUrlAPath().startsWith("A_")) {
                apath = fileV2Manager.getApathByTApath(ea, fsUserId, photoInfo.getPhotoPath(), photoInfo.getExt(), "fs-mankeep");
            } else {
                apath = picResultOpt.get().getUrlAPath();
            }
            if (StringUtils.isEmpty(apath)) {
                log.info("PhotoLibraryServiceImpl.uploadPhoto failed get thmubnail failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return;
            }
            if (picResultOpt.get().getThumbUrlApath() != null) {
                thumbnailPath = picResultOpt.get().getThumbUrlApath();
            } else {
                thumbnailPath = apath;
            }
        }
        if (StringUtils.isBlank(apath)) {
            log.warn("batchUpLoad fail, path is blank, ea: {} arg: {}", ea, arg);
            return;
        }
        //生成cdn地址
        String cpath = null;
        String cdnUrl = null;
        /*
        String cpath = fileV2Manager.uploadToCpathOrNpath(ea, bytes, true);
        String cdnUrl = fileV2Manager.getUrlByCPath(ea, cpath);
         */
        if (arg.getSource() == PhotoLibraryService.PHOTO_LIBRARY_SOURCE) {
            PhotoLibraryEntity entity = new PhotoLibraryEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setPhotoName(photoInfo.getPhotoName());
            entity.setPhotoPath(apath);
            entity.setThumbailPath(thumbnailPath);
            entity.setPhotoSize(photoInfo.getPhotoSize());
            entity.setExt(photoInfo.getExt());
            entity.setType(CUSTOME_FILE);
            entity.setCdnUrl(cdnUrl);
            photoLibraryEntityList.add(entity);
        }else {
            PhotoSelectorEntity entity = new PhotoSelectorEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setPhotoName(photoInfo.getPhotoName());
            entity.setPhotoPath(apath);
            entity.setThumbailPath(thumbnailPath);
            entity.setPhotoSize(photoInfo.getPhotoSize());
            entity.setExt(photoInfo.getExt());
            entity.setCdnUrl(cdnUrl);
            photoSelectorEntityList.add(entity);
        }
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), photoInfo.getPhotoName(), OperateTypeEnum.ADD);

    }


    @Override
    public Result<Void> renamePhoto(String ea, Integer fsUserId, RenamePhotoArg arg) {
        PhotoLibraryEntity entity = photoLibraryDAO.getById(arg.getId());
        if (entity == null){
            log.info("PhotoLibraryServiceImpl.renameFile failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }

        if (StringUtils.equals(arg.getPhotoName(), entity.getPhotoName())){
            log.info("PhotoLibraryServiceImpl.renamePhoto photo exist ea:{} arg:{}", ea, arg);
            return Result.newError(SHErrorCode.PIC_NAME_EXIST);
        }

        if (CollectionUtils.isNotEmpty(photoLibraryDAO.getByPhotoName(ea, Lists.newArrayList(arg.getPhotoName())))){
            log.info("PhotoLibraryServiceImpl.renamePhoto photo exist ea:{} arg:{}", ea, arg);
            return Result.newError(SHErrorCode.PIC_NAME_EXIST);
        }

        if (entity.getType() == SYSTEM_FILE){
            log.info("FileLibraryServiceImpl.renameFile can not renamePhoto system file ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.CANNOT_RENAME_SYSTEM_FILE);
        }

        photoLibraryDAO.updatePhotoNameById(ea, arg.getId(), arg.getPhotoName());
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), entity.getPhotoName(), OperateTypeEnum.RENAME, arg.getPhotoName());

        return Result.newSuccess();
    }

    @Override
    public Result<EditObjectGroupResult> editPhotoGroup(String ea, Integer fsUserId, EditPhotoGroupArg arg) {
        List<String> defaultNames = PhotoDefaultGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("PhotoLibraryServiceImpl.editPhotoGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }

        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.IMAGE.getType());
    }

    @Override
    @Transactional
    public Result setPhotoGroup(String ea, Integer fsUserId, SetPhotoGroupArg arg) {
        if (CollectionUtils.isEmpty(arg.getPhotoIds())){
            return Result.newSuccess();
        }
        List<PhotoLibraryEntity> photoLibraryEntityList = photoLibraryDAO.getByIds(ea, arg.getPhotoIds());
        if (CollectionUtils.isEmpty(photoLibraryEntityList)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }

        //删除图片当前分组
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.IMAGE.getType(), arg.getPhotoIds());
        //新增分组
        List<ObjectGroupRelationEntity> relationEntityList = Lists.newArrayList();
        for(String photoId : arg.getPhotoIds()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(photoId);
            newEntity.setObjectType(ObjectTypeEnum.IMAGE.getType());
            relationEntityList.add(newEntity);
        }
        if (CollectionUtils.isNotEmpty(relationEntityList)){
            objectGroupRelationDAO.batchInsert(relationEntityList);
        }


        for (PhotoLibraryEntity entity : photoLibraryEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), entity.getPhotoName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ListPhotoGroupResult> listPhotoGroup(String ea, Integer fsUserId, ListPhotoGroupArg arg) {
        fsUserId = fsUserId == null ? QywxUserConstants.BASE_VIRTUAL_USER_ID : fsUserId;
        if (arg.getSource() == PhotoLibraryService.PHOTO_LIBRARY_SOURCE){
            if (arg.getUseType() == QUERY_ALL_GROUP){
                return listAllPhotoGroup(ea, fsUserId, arg);
            }else if (arg.getUseType() == QUERY_ALL_CUSTOM_GROUP){
                return listAllCustomPhotoGroup(ea, fsUserId, arg);
            }else {
                return listAllCustomWithItemFileGroup(ea, fsUserId, arg);
            }
        }else {
            return listSelectorPhotoGroup(ea, fsUserId, arg);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deletePhotoGroup(String ea, Integer fsUserId, DeletePhotoGroupArg arg) {
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.IMAGE.getType());
    }

    @Override
    @Transactional
    public Result deletePhoto(String ea, Integer fsUserId, DeletePhotoArg arg) {
        if (arg.getSource() == PhotoLibraryService.PHOTO_LIBRARY_SOURCE) {
            List<PhotoLibraryEntity> entityList = photoLibraryDAO.getByIds(ea, arg.getIds());
            if (CollectionUtils.isEmpty(entityList)){
                log.info("PhotoLibraryServiceImpl.deletePhoto failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return Result.newError(SHErrorCode.FILE_NOT_FOUND);
            }
            objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.IMAGE.getType(), arg.getIds());
            photoLibraryDAO.deleteByIds(ea, arg.getIds());
            // 删除置顶数据
            objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.IMAGE.getType(), arg.getIds());
            for (PhotoLibraryEntity entity : entityList) {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), entity.getPhotoName(), OperateTypeEnum.DELETE);
            }

        }else {
            List<PhotoSelectorEntity> entityList =  photoSelectorDAO.getByIds(ea, arg.getIds());
            if (CollectionUtils.isEmpty(entityList)){
                log.info("PhotoLibraryServiceImpl.deletePhoto failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return Result.newError(SHErrorCode.FILE_NOT_FOUND);
            }
            photoSelectorDAO.deleteByIds(ea, arg.getIds());
        }

        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<ListPhotoByGroupResult>> listPhotoByGroup(String ea, Integer fsUserId, ListPhotoByGroupArg arg) {
        fsUserId = fsUserId == null ? QywxUserConstants.BASE_VIRTUAL_USER_ID : fsUserId;
        PageResult<ListPhotoByGroupResult> pageResult = new PageResult<>();
        List<ListPhotoByGroupResult> photoListResult = Lists.newArrayList();
        pageResult.setResult(photoListResult);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        if (StringUtils.isBlank(arg.getGroupId())) {
            arg.setGroupId(PhotoDefaultGroupEnum.ALL.getId());
        }
        List<PhotoLibraryEntity> pagePhotoLibraryList = null;
        List<PhotoSelectorEntity> pagePhotoSelectorList = null;
        List<Integer> userIdList = null;
        List<String> photoLibraryIds = null;
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        if (arg.getSource() == PhotoLibraryService.PHOTO_LIBRARY_SOURCE) {

            Result<AppMenuTagVO>  appMenuTagResult = appMenuTemplateService.getMenuTagRule(ea, arg.getMenuId(), ObjectTypeEnum.IMAGE.getType());
            AppMenuTagVO appMenuTagVO = appMenuTagResult.isSuccess() ? appMenuTagResult.getData() : null;
            PhotoLibraryQueryParam queryParam = new PhotoLibraryQueryParam();
            queryParam.setEa(ea);
            queryParam.setKeyword(arg.getKeyword());
            queryParam.setUserId(fsUserId);
            queryParam.setMaterialTagFilter(arg.getMaterialTagFilter());
            queryParam.setStrictCheckGroup(StringUtils.isNotBlank(arg.getMenuId()));
            if (appMenuTagVO != null) {
                MaterialTagFilterArg materialTagFilterArg = queryParam.getMaterialTagFilter();
                if (materialTagFilterArg == null) {
                    materialTagFilterArg = new MaterialTagFilterArg();
                }
                materialTagFilterArg.setMenuType(appMenuTagVO.getTagOperator());
                materialTagFilterArg.setMenuMaterialTagIds(appMenuTagVO.getTagIdList());
                queryParam.setMaterialTagFilter(materialTagFilterArg);
                queryParam.setStrictCheckGroup(true);
                pagePhotoLibraryList = photoLibraryDAO.getAccessiblePage(queryParam, page);
            } else if (arg.getGroupId().equals(PhotoDefaultGroupEnum.ALL.getId())) {
                // 是否需要严格校验分组
                queryParam.setStrictCheckGroup(appMenuTemplateService.needStrictCheckGroup(ea, fsUserId, arg.getMenuId()));
                List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getMenuId());
                List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
                queryParam.setPermissionGroupIdList(permissionGroupIdList);
                //pagePhotoLibraryList = photoLibraryDAO.pageQueryPhotoByAll(ea, arg.getKeyword(), page);
                pagePhotoLibraryList = photoLibraryDAO.getAccessiblePage(queryParam, page);
            } else if (arg.getGroupId().equals(PhotoDefaultGroupEnum.MY_PHOTO.getId())) {
                pagePhotoLibraryList = photoLibraryDAO.pageQueryPhotoCreateByMe(ea, fsUserId, arg.getKeyword(), arg.getMaterialTagFilter(), page);
            } else if (arg.getGroupId().equals(PhotoDefaultGroupEnum.NO_GROUP.getId())) {
                pagePhotoLibraryList = photoLibraryDAO.pageQueryFileByUnGrouped(ea, arg.getKeyword(), ObjectTypeEnum.IMAGE.getType(), arg.getMaterialTagFilter(), page);
            }else if (arg.getGroupId().equals(PhotoDefaultGroupEnum.MY_AUTH_ALL.getId())) {
                List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getMenuId());
                Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(permissionGroupIdSet)){
                    pagePhotoLibraryList = Lists.newArrayList();
                } else {
                    queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                    pagePhotoLibraryList = photoLibraryDAO.getAccessibleGroupPage(queryParam, page);
                }
            }  else {
                List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getMenuId());
                Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
                queryParam.setStrictCheckGroup(true);
                if (!permissionGroupIdSet.contains(arg.getGroupId())){
                    pagePhotoLibraryList = Lists.newArrayList();
                } else {
                    //queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                    queryParam.setUserId(fsUserId);
                    List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.IMAGE.getType(), arg.getGroupId(), objectGroupEntityList);
                    accessibleSubGroupIdList.add(arg.getGroupId());
                    queryParam.setGroupIdList(accessibleSubGroupIdList);
                    pagePhotoLibraryList = photoLibraryDAO.getAccessiblePage(queryParam, page);
                }
                //pagePhotoLibraryList = photoLibraryDAO.getAccessiblePage(queryParam, page);
            }
            if (CollectionUtils.isNotEmpty(pagePhotoLibraryList)) {
                photoLibraryIds = pagePhotoLibraryList.stream().map(PhotoLibraryEntity::getId).collect(Collectors.toList());
            }
        }else {
            if (arg.getGroupId().equals(PhotoDefaultGroupEnum.MY_PHOTO.getId())) {
                pagePhotoSelectorList = photoSelectorDAO.pageQueryPhotoCreateByMe(ea, fsUserId, arg.getKeyword(), arg.getMaterialTagFilter(), page);
            }else if (arg.getGroupId().equals(PhotoDefaultGroupEnum.NO_GROUP.getId())){
                pagePhotoLibraryList = photoLibraryDAO.pageQueryFileByUnGrouped(ea, arg.getKeyword(), ObjectTypeEnum.IMAGE.getType(), arg.getMaterialTagFilter(), page);
                if (CollectionUtils.isNotEmpty(pagePhotoLibraryList)) {
                    photoLibraryIds = pagePhotoLibraryList.stream().map(PhotoLibraryEntity::getId).collect(Collectors.toList());
                }
            }else {
                List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType());
                if (CollectionUtils.isEmpty(objectGroupEntityList)){
                    return Result.newSuccess(pageResult);
                }
                Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
                if (!permissionGroupIdSet.contains(arg.getGroupId())) {
                    return Result.newSuccess(pageResult);
                }
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.IMAGE.getType(), arg.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(arg.getGroupId());
                pagePhotoLibraryList = photoLibraryDAO.pageQueryFileByGroup(ea, arg.getKeyword(), arg.getMaterialTagFilter(), page, accessibleSubGroupIdList);
                if (CollectionUtils.isNotEmpty(pagePhotoLibraryList)) {
                    photoLibraryIds = pagePhotoLibraryList.stream().map(PhotoLibraryEntity::getId).collect(Collectors.toList());
                }
            }
        }

        if (CollectionUtils.isEmpty(pagePhotoLibraryList) && CollectionUtils.isEmpty(pagePhotoSelectorList)){
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());

        Map<String, GroupNameObjectIdDTO> objectIdDTOMap = null;
        if (CollectionUtils.isNotEmpty(photoLibraryIds)) {
            List<GroupNameObjectIdDTO> groupNameObjectIdDTOList = objectGroupRelationDAO.queryGroupNameByObjectIds(ea, ObjectTypeEnum.IMAGE.getType(), photoLibraryIds);
            if (CollectionUtils.isNotEmpty(groupNameObjectIdDTOList)) {
                objectIdDTOMap = groupNameObjectIdDTOList.stream().collect(Collectors.toMap(GroupNameObjectIdDTO::getObjectId, Function.identity(), (v1, v2) -> v2));
            }
            Set<Integer> fsUserIds = pagePhotoLibraryList.stream().map(PhotoLibraryEntity::getCreateBy).collect(Collectors.toSet());
            userIdList = new ArrayList<>(fsUserIds);
        }else {
            Set<Integer> fsUserIds = pagePhotoSelectorList.stream().map(PhotoSelectorEntity::getCreateBy).collect(Collectors.toSet());
            userIdList = new ArrayList<>(fsUserIds);
        }

        List<String> paths = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pagePhotoSelectorList)){
            pagePhotoSelectorList.forEach(photoSelectorEntity -> {
                if (photoSelectorEntity.getPhotoPath() != null) {
                    paths.add(photoSelectorEntity.getPhotoPath());
                }
                if (photoSelectorEntity.getThumbailPath() != null) {
                    paths.add(photoSelectorEntity.getThumbailPath());
                }
            });
        }
        if (CollectionUtils.isNotEmpty(pagePhotoLibraryList)){
            pagePhotoLibraryList.forEach(photoLibraryEntity -> {
                if (photoLibraryEntity.getPhotoPath() != null) {
                    paths.add(photoLibraryEntity.getPhotoPath());
                }
                if (photoLibraryEntity.getThumbailPath() != null) {
                    paths.add(photoLibraryEntity.getThumbailPath());
                }
            });
        }

        //缩略图
        Map<String, String> urlMap = null;
        if (CollectionUtils.isNotEmpty(paths)) {
            urlMap = fileV2Manager.batchGetUrlByPath(paths, ea, false);
        }
        if (CollectionUtils.isNotEmpty(pagePhotoSelectorList)){
            // 查询标签
            List<String> photoIdList = pagePhotoSelectorList.stream().map(PhotoSelectorEntity::getId).collect(Collectors.toList());
            Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(photoIdList, ObjectTypeEnum.IMAGE.getType());
            for (PhotoSelectorEntity entity : pagePhotoSelectorList){
                ListPhotoByGroupResult fileByGroupResult = new ListPhotoByGroupResult();
                fileByGroupResult.setPhotoName(entity.getPhotoName());
                fileByGroupResult.setPhotoPath(entity.getPhotoPath());
                fileByGroupResult.setPhotoSize(entity.getPhotoSize());
                fileByGroupResult.setId(entity.getId());
                fileByGroupResult.setExt(entity.getExt());
                fileByGroupResult.setCdnUrl(entity.getCdnUrl());
                fileByGroupResult.setCreateTime(entity.getCreateTime().getTime());
                fileByGroupResult.setGroupName(PhotoDefaultGroupEnum.MY_PHOTO.getName());
                fileByGroupResult.setPhotoForwardUrl(fileV2Manager.getSpliceUrl(ea, entity.getPhotoPath()));
                if (urlMap != null && urlMap.get(entity.getPhotoPath()) != null) {
                    fileByGroupResult.setUrl(urlMap.get(entity.getPhotoPath()));
                }

                if (urlMap != null && urlMap.get(entity.getThumbailPath()) != null) {
                    fileByGroupResult.setThumbnailUrl(urlMap.get(entity.getThumbailPath()));
                }
                // 内容标签处理
                List<String> materialTags = materialTagMap.get(entity.getId());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                    List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                        MaterialTagResult materialTagResult = new MaterialTagResult();
                        materialTagResult.setName(materialTag);
                        return materialTagResult;
                    }).collect(Collectors.toList());
                    fileByGroupResult.setMaterialTags(collect);
                }
                photoListResult.add(fileByGroupResult);
            }
        }
        if (CollectionUtils.isNotEmpty(pagePhotoLibraryList)){
            List<String> photoIdList = pagePhotoLibraryList.stream().map(PhotoLibraryEntity::getId).collect(Collectors.toList());
            List<ObjectTopEntity> objectTopEntityList = objectTopManager.getByObjectIdList(photoIdList);
            Map<String, ObjectTopEntity> objectTopEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(objectTopEntityList)) {
                objectTopEntityMap = objectTopEntityList.stream().collect(Collectors.toMap(ObjectTopEntity::getObjectId, e -> e, (v1, v2) -> v1));
            }
            // 查询标签
            Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(photoIdList, ObjectTypeEnum.IMAGE.getType());
            for (PhotoLibraryEntity entity : pagePhotoLibraryList) {
                ListPhotoByGroupResult fileByGroupResult = new ListPhotoByGroupResult();
                fileByGroupResult.setPhotoName(entity.getPhotoName());
                fileByGroupResult.setPhotoPath(entity.getPhotoPath());
                fileByGroupResult.setPhotoSize(entity.getPhotoSize());
                fileByGroupResult.setId(entity.getId());
                fileByGroupResult.setExt(entity.getExt());
                fileByGroupResult.setCdnUrl(entity.getCdnUrl());
                fileByGroupResult.setCreateTime(entity.getCreateTime().getTime());
                fileByGroupResult.setPhotoForwardUrl(fileV2Manager.getSpliceUrl(ea, entity.getPhotoPath()));
                fileByGroupResult.setTop(objectTopEntityMap.containsKey(entity.getId()));
                if (objectIdDTOMap == null || objectIdDTOMap.get(entity.getId()) == null) {
                    fileByGroupResult.setGroupName(PhotoDefaultGroupEnum.NO_GROUP.getName());
                } else {
                    fileByGroupResult.setGroupName(objectIdDTOMap.get(entity.getId()).getGroupName());
                }
                if (urlMap != null && urlMap.get(entity.getPhotoPath()) != null) {
                    fileByGroupResult.setUrl(urlMap.get(entity.getPhotoPath()));
                }
                if (urlMap != null && urlMap.get(entity.getThumbailPath()) != null) {
                    fileByGroupResult.setThumbnailUrl(urlMap.get(entity.getThumbailPath()));
                }
                // 内容标签处理
                List<String> materialTags = materialTagMap.get(entity.getId());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                    List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                        MaterialTagResult materialTagResult = new MaterialTagResult();
                        materialTagResult.setName(materialTag);
                        return materialTagResult;
                    }).collect(Collectors.toList());
                    fileByGroupResult.setMaterialTags(collect);
                }
                photoListResult.add(fileByGroupResult);
            }
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result syncChuangKeTiePoster(String ea, Integer fsUserId, String designId, String url, int source, String groupId) {
        SyncChuangKeTiePosterResult result = new SyncChuangKeTiePosterResult();
        byte[] bytes = httpManager.getBytesByUrl(url);
        if (bytes == null){
            log.info("PhotoLibraryServiceImpl.syncChuangKeTiePoster failed download file failed ea:{} designId:{} url:{}", ea, designId, url);
            return Result.newError(SHErrorCode.DOWNLOAD_QRPOSTER_FAILED);
        }
        if (bytes.length > 1024 * 1024 * 10) {
            return Result.newError(SHErrorCode.QR_CODE_TOO_LARGE);
        }
        String apath = null;
        String thumbnailPath = null;
//        Optional<FileV2Manager.FileManagerPicResult> picResultOpt = fileV2Manager.uploadToApathWithThumbnail(bytes, "png", ea, fsUserId);
//        if (picResultOpt.isPresent()){
//            FileV2Manager.FileManagerPicResult picResult =  picResultOpt.get();
//            apath = picResult.getUrlAPath();
//            thumbnailPath = picResult.getThumbUrlApath();
//        }
//
//        if (apath == null){
//            apath = fileV2Manager.uploadToApath(bytes, "png", ea);
//        }
        apath = fileV2Manager.uploadToCpathOrNpath( ea,bytes,true,null);
        if (StringUtils.isEmpty(apath)){
            log.info("QRPosterServiceImpl.syncChuangKeTiePoster failed upload file to warehouse failed ea:{} designId:{} url:{}", ea, designId, url);
            return Result.newError(SHErrorCode.PIC_CONVERT_FAILED);
        }
        thumbnailPath = thumbnailPath == null ? apath : thumbnailPath;
        String uuid = UUIDUtil.getUUID();

        if (source == PHOTO_LIBRARY_SOURCE) {
            PhotoLibraryEntity entity = new PhotoLibraryEntity();
            entity.setId(uuid);
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setPhotoName(I18nUtil.get(I18nKeyEnum.MARK_PHOTOLIBRARY_PHOTOLIBRARYSERVICEIMPL_891));
            entity.setPhotoPath(apath);
            entity.setThumbailPath(thumbnailPath);
            entity.setPhotoSize((long) bytes.length);
            entity.setType(CUSTOME_FILE);
            entity.setExt("png");
            photoLibraryDAO.insert(entity);
            //上报神策系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), null, entity.getId()));
        }else {
            PhotoSelectorEntity entity = new PhotoSelectorEntity();
            entity.setId(uuid);
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setPhotoName(I18nUtil.get(I18nKeyEnum.MARK_PHOTOLIBRARY_PHOTOLIBRARYSERVICEIMPL_891));
            entity.setPhotoPath(apath);
            entity.setThumbailPath(thumbnailPath);
            entity.setPhotoSize((long)bytes.length);
            entity.setExt("png");
            photoSelectorDAO.insert(entity);
            //上报神策系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), null, entity.getId()));
        }
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), I18nUtil.get(I18nKeyEnum.MARK_PHOTOLIBRARY_PHOTOLIBRARYSERVICEIMPL_891), OperateTypeEnum.ADD);
        // 设置图片分组
        SetPhotoGroupArg setPhotoGroupArg = new SetPhotoGroupArg();
        setPhotoGroupArg.setGroupId(groupId);
        setPhotoGroupArg.setPhotoIds(Lists.newArrayList(uuid));
        setPhotoGroup(ea, fsUserId, setPhotoGroupArg);

        return Result.newSuccess(result);
    }

    private Result<ListPhotoGroupResult> listAllCustomWithItemFileGroup(String ea, Integer fsUserId, ListPhotoGroupArg arg){
        ListPhotoGroupResult validGroupResult = new ListPhotoGroupResult();
        List<ListObjectGroupResult> validGroupList = Lists.newArrayList();
        validGroupResult.setObjectGroupList(validGroupList);

        //未分组
        ListObjectGroupResult ungroup = new ListObjectGroupResult();
        ungroup.setGroupId(PhotoDefaultGroupEnum.NO_GROUP.getId());
        ungroup.setGroupName(PhotoDefaultGroupEnum.NO_GROUP.getName());
        ungroup.setSystem(false);
        ungroup.setObjectCount(photoLibraryDAO.queryPhotoCountByUnGrouped(ea, ObjectTypeEnum.IMAGE.getType(), arg.getKeyword()));
        validGroupList.add(ungroup);
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.FILE.getType(), arg.getMenuId(), null);
        List<ListObjectGroupResult> withItemList = customizeGroupListVO.getObjectGroupList().stream()
                .filter(e -> e.getObjectCount() > 0).collect(Collectors.toList());
        validGroupResult.setObjectGroupList(withItemList);
        validGroupResult.setSortVersion(customizeGroupListVO.getSortVersion());
        //List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.IMAGE.getType());
//        List<ObjectGroupEntity> objectGroupEntityList = groupRoleRelationManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType());
//        if (CollectionUtils.isNotEmpty(objectGroupEntityList)){
//            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.PHOTO_GROUP_DISPLAY_KEY);
//            if (templateGroupDisplayOrder != null) {
//                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
//                validGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
//            }
//
//            List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
//            List<ContentGroupCountDTO> contentGroupCountDTOList = objectGroupRelationDAO.queryPhotoEntityDTOByGroups(ea, ObjectTypeEnum.IMAGE.getType(), groupIds);
//            Map<String, Integer> groupCountDTOMap = null;
//            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)){
//                groupCountDTOMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1,v2)->v2));
//            }
//
//            for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList){
//                if (groupCountDTOMap != null && groupCountDTOMap.get(objectGroupEntity.getId()) != null && groupCountDTOMap.get(objectGroupEntity.getId()).intValue() > 0 ){
//                    ListObjectGroupResult objectGroup = new ListObjectGroupResult();
//                    objectGroup.setObjectCount(groupCountDTOMap.get(objectGroupEntity.getId()).intValue());
//                    objectGroup.setGroupName(objectGroupEntity.getName());
//                    objectGroup.setGroupId(objectGroupEntity.getId());
//                    validGroupList.add(objectGroup);
//                }
//            }
//        }

        return Result.newSuccess(validGroupResult);
    }

    private Result<ListPhotoGroupResult>  listAllCustomPhotoGroup(String ea, Integer fsUserId, ListPhotoGroupArg arg){
        ListPhotoGroupResult fileGroupResult = new ListPhotoGroupResult();
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getMenuId(), null);
        fileGroupResult.setSortVersion(customizeGroupListVO.getSortVersion());
        fileGroupResult.setObjectGroupList(customizeGroupListVO.getObjectGroupList());
        //List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.IMAGE.getType());
//        List<ObjectGroupEntity> objectGroupEntityList = groupRoleRelationManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType());
//        if (CollectionUtils.isEmpty(objectGroupEntityList)){
//            return Result.newSuccess(fileGroupResult);
//        }
//
//        List<ListObjectGroupResult> result = Lists.newArrayList();
//        fileGroupResult.setObjectGroupList(result);
//        if (CollectionUtils.isNotEmpty(objectGroupEntityList)) {
//            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.PHOTO_GROUP_DISPLAY_KEY);
//            if (templateGroupDisplayOrder != null) {
//                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
//                fileGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
//            }
//        }
//        List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
//        List<ContentGroupCountDTO> groupCountDTOList = objectGroupRelationDAO.queryEntityByGroups(ea, ObjectTypeEnum.IMAGE.getType(), groupIds);
//        Map<String, ContentGroupCountDTO> groupCountDTOMap = null;
//        if (CollectionUtils.isNotEmpty(groupCountDTOList)){
//            groupCountDTOMap = groupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, Function.identity(), (v1, v2)->v2));
//        }
//
//        for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList){
//            ListObjectGroupResult groupResult = new ListObjectGroupResult();
//            groupResult.setGroupId(objectGroupEntity.getId());
//            groupResult.setGroupName(objectGroupEntity.getName());
//            if (groupCountDTOMap != null && groupCountDTOMap.get(objectGroupEntity.getId()) != null) {
//                groupResult.setObjectCount(groupCountDTOMap.get(objectGroupEntity.getId()).getCount());
//            }
//            result.add(groupResult);
//        }

        return Result.newSuccess(fileGroupResult);
    }

    private Result<ListPhotoGroupResult> listAllPhotoGroup(String ea, Integer fsUserId, ListPhotoGroupArg arg){
        List<ListObjectGroupResult> resultList = Lists.newArrayList();
        if(arg.getRequestSource()==null||arg.getRequestSource()!=1){
            setDefaultGroupList(resultList);
        }else {
            ListObjectGroupResult lastUseGroup = new ListObjectGroupResult();
            lastUseGroup.setGroupId(PhotoDefaultGroupEnum.MY_AUTH_ALL.getId());
            lastUseGroup.setGroupName(PhotoDefaultGroupEnum.MY_AUTH_ALL.getName());
            resultList.add(lastUseGroup);
        }
        ListPhotoGroupResult result = new ListPhotoGroupResult();
        result.setObjectGroupList(resultList);
        ObjectGroupListResult customizeGroupListVO;
        if(arg.getRequestSource()==null||arg.getRequestSource()!=1){
            customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getMenuId(), null);
        }else {
            customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getMenuId(), null);
        }
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        result.setSortVersion(customizeGroupListVO.getSortVersion());
        List<String> groupIds = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
//        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.IMAGE.getType());
//        List<ObjectGroupEntity> objectGroupEntityList = groupRoleRelationManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.IMAGE.getType());
//        if (CollectionUtils.isNotEmpty(objectGroupEntityList)){
//            groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
//            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.PHOTO_GROUP_DISPLAY_KEY);
//            if (templateGroupDisplayOrder != null) {
//                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
//                result.setSortVersion(templateGroupDisplayOrder.getVersion());
//            }
//            objectGroupEntityList.forEach(groupResult ->{
//                ListObjectGroupResult objectGroup = new ListObjectGroupResult();
//                objectGroup.setGroupName(groupResult.getName());
//                objectGroup.setGroupId(groupResult.getId());
//                resultList.add(objectGroup);
//            });
//        }

//        Map<String, Integer> contentGroupCountMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(groupIds)) {
//            List<ContentGroupCountDTO> contentGroupCountDTOList = objectGroupRelationDAO.queryEntityByGroups(ea, ObjectTypeEnum.IMAGE.getType(), groupIds);
//            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)){
//                contentGroupCountMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1, v2)->v2));
//            }
//        }
        for (ListObjectGroupResult objectGroup : resultList){
            if (StringUtils.equals(objectGroup.getGroupId(), PhotoDefaultGroupEnum.ALL.getId())){
                //objectGroup.setObjectCount(photoLibraryDAO.queryPhotoCountByAll(ea, arg.getKeyword()));
                // 如果没有查看任何一个分组的权限，只能看未分组的微页面 + 我创建的
                if (CollectionUtils.isEmpty(groupIds)) {
                    objectGroup.setObjectCount(photoLibraryDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId, arg.getKeyword()));
                } else {
                    // 如果有分组权限，可以查看 有权限的分组 + 我创建的 + 未分类的
                    objectGroup.setObjectCount(photoLibraryDAO.queryAccessibleCount(ea, fsUserId, arg.getKeyword(), groupIds));
                }
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                objectGroup.setSystem(true);
            } else if (StringUtils.equals(objectGroup.getGroupId(), PhotoDefaultGroupEnum.MY_PHOTO.getId())){
                objectGroup.setObjectCount(photoLibraryDAO.queryPhotoCountCreateByMe(ea, fsUserId, arg.getKeyword()));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }else if (StringUtils.equals(objectGroup.getGroupId(), PhotoDefaultGroupEnum.NO_GROUP.getId())){
                objectGroup.setObjectCount(photoLibraryDAO.queryPhotoCountByUnGrouped(ea, ObjectTypeEnum.IMAGE.getType(), arg.getKeyword()));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            } else if (StringUtils.equals(objectGroup.getGroupId(), PhotoDefaultGroupEnum.MY_AUTH_ALL.getId())){
                objectGroup.setGroupName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }
//            else {
//                if (contentGroupCountMap == null || contentGroupCountMap.get(objectGroup.getGroupId()) == null){
//                    objectGroup.setObjectCount(0);
//                }else {
//                    objectGroup.setObjectCount(contentGroupCountMap.get(objectGroup.getGroupId()));
//                }
//            }
        }
        return Result.newSuccess(result);
    }

    public Result<ListPhotoGroupResult> listSelectorPhotoGroup(String ea, Integer fsUserId, ListPhotoGroupArg arg) {
        ListPhotoGroupResult validGroupResult = new ListPhotoGroupResult();
        List<ListObjectGroupResult> validGroupList = Lists.newArrayList();
        validGroupResult.setObjectGroupList(validGroupList);

        //我的图片
        ListObjectGroupResult mygroup = new ListObjectGroupResult();
        mygroup.setGroupId(PhotoDefaultGroupEnum.MY_PHOTO.getId());
        mygroup.setGroupName(PhotoDefaultGroupEnum.MY_PHOTO.getName());
        mygroup.setSystem(false);
        mygroup.setObjectCount(photoSelectorDAO.queryPhotoCountCreateByMe(ea, fsUserId, arg.getKeyword()));
        validGroupList.add(mygroup);

        //未分组
        ListObjectGroupResult ungroup = new ListObjectGroupResult();
        ungroup.setGroupId(PhotoDefaultGroupEnum.NO_GROUP.getId());
        ungroup.setGroupName(PhotoDefaultGroupEnum.NO_GROUP.getName());
        ungroup.setSystem(false);
        ungroup.setObjectCount(photoLibraryDAO.queryPhotoCountByUnGrouped(ea, ObjectTypeEnum.IMAGE.getType(), arg.getKeyword()));
        validGroupList.add(ungroup);

        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.IMAGE.getType());
        if (CollectionUtils.isNotEmpty(objectGroupEntityList)){
            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.PHOTO_GROUP_DISPLAY_KEY);
            if (templateGroupDisplayOrder != null) {
                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
                validGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
            }

            List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            List<ContentGroupCountDTO> contentGroupCountDTOList = objectGroupRelationDAO.queryPhotoEntityDTOByGroups(ea, ObjectTypeEnum.IMAGE.getType(), groupIds);
            Map<String, Integer> groupCountDTOMap = null;
            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)){
                groupCountDTOMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1,v2)->v2));
            }

            for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList){
                if (groupCountDTOMap != null && groupCountDTOMap.get(objectGroupEntity.getId()) != null && groupCountDTOMap.get(objectGroupEntity.getId()).intValue() > 0 ){
                    ListObjectGroupResult objectGroup = new ListObjectGroupResult();
                    objectGroup.setObjectCount(groupCountDTOMap.get(objectGroupEntity.getId()).intValue());
                    objectGroup.setGroupName(objectGroupEntity.getName());
                    objectGroup.setGroupId(objectGroupEntity.getId());
                    validGroupList.add(objectGroup);
                }
            }
        }

        return Result.newSuccess(validGroupResult);
    }
    private void setDefaultGroupList( List<ListObjectGroupResult> groupList){
        //最近使用
        ListObjectGroupResult lastUseGroup = new ListObjectGroupResult();
        lastUseGroup.setGroupId(PhotoDefaultGroupEnum.ALL.getId());
        lastUseGroup.setGroupName(PhotoDefaultGroupEnum.ALL.getName());
        groupList.add(lastUseGroup);

        //我创建的
        ListObjectGroupResult groupCreateByMe = new ListObjectGroupResult();
        groupCreateByMe.setGroupId(PhotoDefaultGroupEnum.MY_PHOTO.getId());
        groupCreateByMe.setGroupName(PhotoDefaultGroupEnum.MY_PHOTO.getName());
        groupList.add(groupCreateByMe);

        ListObjectGroupResult noGroup = new ListObjectGroupResult();
        noGroup.setGroupId(PhotoDefaultGroupEnum.NO_GROUP.getId());
        noGroup.setGroupName(PhotoDefaultGroupEnum.NO_GROUP.getName());
        groupList.add(noGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topPhoto(String ea, Integer fsUserId, TopMaterialArg arg) {
        PhotoLibraryEntity entity = photoLibraryDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.IMAGE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopPhoto(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.IMAGE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.IMAGE.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addPhotoGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.IMAGE.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }

    @Override
    public Result<List<MobilePhotoGroupResult>> listMobilePhotoGroup(String ea,Integer fsUserId,  ListPhotoGroupArg arg) {
        if(arg.getMobileDisplay()==null||arg.getMobileDisplay()==0){
            List<MobilePhotoGroupResult> results = null;
            Result<ListPhotoGroupResult> listPhotoGroupResultResult = listAllPhotoGroup(ea, fsUserId, arg);
            List<MobilePhotoGroupResult> collect = listPhotoGroupResultResult.getData().getObjectGroupList().stream().filter(o -> !Lists.newArrayList(PhotoDefaultGroupEnum.ALL.getId(), PhotoDefaultGroupEnum.MY_PHOTO.getId(), PhotoDefaultGroupEnum.NO_GROUP.getId()).contains(o.getGroupId()))
                    .map(o -> {
                        MobilePhotoGroupResult result = new MobilePhotoGroupResult();
                        result.setId(o.getGroupId());
                        result.setName(o.getGroupName());
                        result.setEa(ea);
                        result.setObjectType(ObjectTypeEnum.IMAGE.getType());
                        return result;
                    }).collect(Collectors.toList());
            return Result.newSuccess(collect);
        }
        List<ObjectGroupEntity> groupEntities = objectGroupManager.listMobileGroupByEa(ea, ObjectTypeEnum.IMAGE.getType(),arg.getMobileDisplay());
        List<MobilePhotoGroupResult> groupResults = BeanUtil.copy(groupEntities, MobilePhotoGroupResult.class);
        return Result.newSuccess(groupResults);
    }

    @Override
    @Transactional
    public Result mobilePhotoGroupSetting(String ea, List<String> groupIds) {
        List<ObjectGroupEntity> objectGroupEntities = objectGroupManager.listMobileGroupByEa(ea, ObjectTypeEnum.IMAGE.getType(), ObjectGroupMobileStatusEnum.DISPLAY.getStatus());
        List<String> oldGroupIds = objectGroupEntities.stream().map(o -> o.getId()).collect(Collectors.toList());
        //获取要设置为显示的id集合
        List<String> displayGroupIds = objectGroupManager.getDifferenceCollection(oldGroupIds,groupIds);
        if(!CollectionUtils.isEmpty(displayGroupIds)){
            objectGroupDAO.updateMobileDisplayByIds(ea,displayGroupIds,true);
        }
        //获取要设置为隐藏的id集合
        List<String> hiddenGroupIds = objectGroupManager.getDifferenceCollection(groupIds,oldGroupIds);
        if(!CollectionUtils.isEmpty(hiddenGroupIds)){
            objectGroupDAO.updateMobileDisplayByIds(ea,hiddenGroupIds,false);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<getCPathUrlResult> getCPathUrl(GetCPathUrlArg arg) {
        getCPathUrlResult result = new getCPathUrlResult();
        if (arg.getOriginalImageAPath() == null){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        List<String> list = new ArrayList<>();
        String cpath = null;
        if(arg.getOriginalImageAPath().startsWith("A_")){
            Optional<String> fopt = fileV2Manager.copyAPathToCPath(arg.getEa(), arg.getFsUserId(), arg.getOriginalImageAPath());
            if (!fopt.isPresent()){
                return Result.newError(SHErrorCode.PIC_CONVERT_FAILED);
            }
            cpath = fopt.get();
            for (PhotoCutOffset offset : arg.getCutOffsetList()) {
                String url = null;
                if(offset==null){
                    url = fileV2Manager.getUrlByCPathNoCheckEa(arg.getEa(), cpath,null,null,null,null);
                }else {
                    url = fileV2Manager.getUrlByCPathNoCheckEa(arg.getEa(), cpath, offset.getLeft(),offset.getTop(),offset.getWidth(), offset.getHeight());
                }
                list.add(url);
            }
        }else if (arg.getOriginalImageAPath().startsWith(C_WAREHOUSE_TYPE)){
            Optional<String> fopt = fileV2Manager.copyCFileToCFile(arg.getEa(),arg.getEa(), arg.getFsUserId(), arg.getOriginalImageAPath(),true);
            if (!fopt.isPresent()){
                return Result.newError(SHErrorCode.PIC_CONVERT_FAILED);
            }
            cpath = fopt.get();
            for (PhotoCutOffset offset : arg.getCutOffsetList()) {
                String url = null;
                if(offset==null){
                    url = fileV2Manager.getUrlByCPathNoCheckEa(arg.getEa(), cpath,null,null,null,null);
                }else {
                    url = fileV2Manager.getUrlByCPathNoCheckEa(arg.getEa(), cpath, offset.getLeft(),offset.getTop(),offset.getWidth(), offset.getHeight());
                }
                list.add(url);
            }
        }
        if(arg.getNeedOriginal()!=null && arg.getNeedOriginal()){
            result.setUrlList(list);
            result.setAPath(cpath);
            return Result.newSuccess(result);
        }

        byte[] bytes = fileV2Manager.downloadFileByUrl(list.get(0), null);
        String cutOffset = fileV2Manager.uploadToCpathOrNpath(arg.getEa(), bytes,true,null);
        result.setUrlList(list);
        result.setAPath(cutOffset);
        return Result.newSuccess(result);
    }

    public void syncAPathToCPath(String ea){
        List<PhotoLibraryEntity> aPathPhotosByEa = photoLibraryDAO.getAPathPhotosByEa(ea);
        List<PhotoSelectorEntity> photoSelectorEntityList = photoSelectorDAO.getAPathPhotosByEa(ea);
        if(CollectionUtils.isEmpty(aPathPhotosByEa) && CollectionUtils.isEmpty(photoSelectorEntityList)){
            return;
        }

        //图片库
        if(CollectionUtils.isNotEmpty(aPathPhotosByEa)){
            for (PhotoLibraryEntity photoLibraryEntity : aPathPhotosByEa) {
                ThreadPoolUtils.execute(() -> {
                    String cpath = null;
                    String url = null;
                    if (StringUtils.isNotBlank(photoLibraryEntity.getCdnUrl())){
                        String[] parts = photoLibraryEntity.getCdnUrl().split("/");
                        String lastPart = parts[parts.length - 1];
                        if (lastPart.startsWith("C_")){
                            cpath = lastPart;
                            url = photoLibraryEntity.getCdnUrl();
                        }
                    }

                    if (StringUtils.isNotBlank(cpath) && StringUtils.isNotBlank(url)){
                        photoLibraryDAO.updateToCpath(photoLibraryEntity.getId(),photoLibraryEntity.getEa(),cpath,url);
                        log.info("图片库更新历史数据,id={},ea={},oldPhotoPath={},oldThumbailPath={},newCpath={}",
                                photoLibraryEntity.getId(),ea,photoLibraryEntity.getPhotoPath(),photoLibraryEntity.getThumbailPath(),cpath);
                        return;
                    }
                    cpath = fileV2Manager.copyAPathToCPath(ea, 1000, photoLibraryEntity.getPhotoPath()).get();
                    if (StringUtils.isBlank(cpath)){
                        return;
                    }
                    url = fileV2Manager.getUrlByCPath(ea, cpath);
                    photoLibraryDAO.updateToCpath(photoLibraryEntity.getId(),photoLibraryEntity.getEa(),cpath,url);
                    log.info("图片库更新历史数据,id={},ea={},oldPhotoPath={},oldThumbailPath={},newCpath={}",
                            photoLibraryEntity.getId(),ea,photoLibraryEntity.getPhotoPath(),photoLibraryEntity.getThumbailPath(),cpath);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            }
        }

        //图片选择器
        if(CollectionUtils.isNotEmpty(photoSelectorEntityList)){
            for (PhotoSelectorEntity photoSelectorEntity : photoSelectorEntityList) {
                ThreadPoolUtils.execute(() -> {

                    String cpath = null;
                    String url = null;
                    if (StringUtils.isNotBlank(photoSelectorEntity.getCdnUrl())){
                        String[] parts = photoSelectorEntity.getCdnUrl().split("/");
                        String lastPart = parts[parts.length - 1];
                        if (lastPart.startsWith("C_")){
                            cpath = lastPart;
                            url = photoSelectorEntity.getCdnUrl();
                        }
                    }

                    if (StringUtils.isNotBlank(cpath) && StringUtils.isNotBlank(url)){
                        photoSelectorDAO.updateToCpath(photoSelectorEntity.getId(),photoSelectorEntity.getEa(),cpath,url);
                        log.info("图片选择器更新历史数据,id={},ea={},oldPhotoPath={},oldThumbailPath={},newCpath={}",
                                photoSelectorEntity.getId(),ea,photoSelectorEntity.getPhotoPath(),photoSelectorEntity.getThumbailPath(),cpath);
                        return;
                    }

                    cpath = fileV2Manager.copyAPathToCPath(ea, 1000, photoSelectorEntity.getPhotoPath()).get();
                    if (StringUtils.isBlank(cpath)){
                        return;
                    }
                    url = fileV2Manager.getUrlByCPath(ea, cpath);
                    photoSelectorDAO.updateToCpath(photoSelectorEntity.getId(),photoSelectorEntity.getEa(),cpath,url);
                    log.info("图片选择器更新历史数据,id={},ea={},oldPhotoPath={},oldThumbailPath={},newCpath={}",
                            photoSelectorEntity.getId(),ea,photoSelectorEntity.getPhotoPath(),photoSelectorEntity.getThumbailPath(),cpath);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            }
        }
    }

}