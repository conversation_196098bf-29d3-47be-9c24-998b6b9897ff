package com.facishare.marketing.provider.service.wxcoupon;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.PartnerNoticeSendArg;
import com.facishare.marketing.api.arg.publicdata.PublicDataRangeArg;
import com.facishare.marketing.api.arg.publicdata.UpdatePublicArg;
import com.facishare.marketing.api.result.EmployeeStatisticResult;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.qywx.SendMomentListResult;
import com.facishare.marketing.api.result.wxcoupon.*;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.api.service.wxcoupon.PublicCouponService;
import com.facishare.marketing.api.service.wxcoupon.WxCouponPayService;
import com.facishare.marketing.api.util.AmountUtil;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.util.XssfExcelUtil;
import com.facishare.marketing.api.vo.wxcoupon.*;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.coupon.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.marketingplugin.MemberCouponBindDAO;
import com.facishare.marketing.provider.dao.marketingplugin.SendDealerCouponRecordDAO;
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO;
import com.facishare.marketing.provider.dao.param.coupon.QueryCouponParam;
import com.facishare.marketing.provider.entity.marketingplugin.MemberCouponBindEntity;
import com.facishare.marketing.provider.entity.marketingplugin.SendDealerCouponRecordEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.entity.pay.MerchantConfigEntity;
import com.facishare.marketing.provider.innerArg.crm.PaasQueryCouponArg;
import com.facishare.marketing.provider.innerArg.crm.PaasQueryUserCouponArg;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.coupon.PublicCouponManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.CouponDistributionObjManager;
import com.facishare.marketing.provider.manager.metadata.PublicMetadataManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.i18n.I18NUtil;
import com.facishare.marketing.provider.util.i18n.MarketingI18NKeyUtil;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.IncrementUpdateArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.BatchGetEaByOuterTenantIdResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.otherrestapi.syncservice.arg.SyncDataByDownStreamTenantIdArg;
import com.fxiaoke.otherrestapi.syncservice.result.SyncResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("publicCouponService")
@Slf4j
public class PublicCouponServiceImpl implements PublicCouponService {

    @Autowired
    private WxCouponPayManager wxCouponPayManager;

    @Autowired
    private WeChatCouponDAO weChatCouponDAO;

    @Autowired
    private PublicMetadataManager publicMetadataManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private SendDealerCouponRecordDAO sendDealerCouponRecordDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private PublicCouponManager publicCouponManager;

    @Autowired
    private WxCouponPayService wxCouponPayService;

    @Autowired
    private CouponTemplateManager couponTemplateManager;

    @Autowired
    private MemberCouponBindDAO memberCouponBindDAO;

    @Autowired
    private FxiaokeAccountService fxiaokeAccountService;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private CouponDistributionObjManager couponDistributionObjManager;

    @Autowired
    private NoticeService noticeService;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private EnterpriseRelationService enterpriseRelationService;

    @Autowired
    private MetadataActionService metadataActionService;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @ReloadableProperty("coupon.top.job.ea")
    private String couponTopJobEa;

    @ReloadableProperty("open.new.coupon.range")
    private boolean openNewCouponRange;

    private static final String COUPON_LOCK_PREFIX = "COUPON_LOCK_";

    private static final String IMPORT = "IMPORT";

    private static final String SELECT_CONDITION = "CONDITION";

    @Override
    public Result<CreateCouponResult> createPublicCoupon(CreateWxCouponVO vo) {
        //生成stockId
        String stockId = vo.getEa() + DateUtil.getTimeStamp(new Date())+ UUIDUtil.getUUID();
        //处理可售客户发送范围
        if (SceneEnum.DEALER.getType() == vo.getScene() && vo.getAccountVisibilityVO() != null && !Objects.equals(vo.getAccountVisibilityVO().getType(),IMPORT)) {
            CreateWxCouponVO.AccountVisibilityVO accountVisibilityVO = vo.getAccountVisibilityVO();
            List<String> outTenantIds = publicCouponManager.getOutTenantIds(vo.getEa(), accountVisibilityVO.getType(), accountVisibilityVO.getValue(),VisibilityEnum.SEND_SCOPE.getType());
            if (CollectionUtils.isEmpty(outTenantIds)) {
                return Result.newError(SHErrorCode.NOT_FOUND_SEND_ACCOUNT);
            }
            if (vo.getPartnerNoticeVisibilityVO() == null) {
                CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = new CreateWxCouponVO.PartnerNoticeVisibilityVO();
                partnerNoticeVisibilityVO.setOuterTenantIds(outTenantIds);
                vo.setPartnerNoticeVisibilityVO(partnerNoticeVisibilityVO);
            }
        }
        //处理门店发送范围
        if (vo.getStoreReceiveVisibilityVO() != null && !Objects.equals(vo.getStoreReceiveVisibilityVO().getType(),IMPORT)) {
            CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = vo.getStoreReceiveVisibilityVO();
            int countStore = publicCouponManager.getStoreAccountIdsCount(vo.getEa(), storeReceiveVisibilityVO.getType(), storeReceiveVisibilityVO.getValue(),VisibilityEnum.RECEIVE_SCOPE.getType());
            if (countStore <= 0) {
                return Result.newError(SHErrorCode.NOT_FOUND_RECEIVE_ACCOUNT);
            }
        }
        //同步数据到CouponObj 公共对象
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> couponAddResult = wxCouponPayManager.createCouponObj(vo,true);
        if (!couponAddResult.isSuccess() || couponAddResult.getData() == null){
            log.warn("public Coupon sync CRM CouponObj fail  result:{}",couponAddResult);
            return Result.newError(SHErrorCode.CRM_BUSINESS_ERROR);
        }
        if (SceneEnum.DEALER.getType() == vo.getScene()) {
            int ei = eieaConverter.enterpriseAccountToId(vo.getEa());
            //更新优惠券方案对象范围
            Set<String> rangeSet = new HashSet<>();
            PublicDataRangeArg rangeArg = new PublicDataRangeArg();
            rangeArg.setEa(vo.getEa());
            rangeArg.setIds(Lists.newArrayList(vo.getPricePolicyId()));
            rangeArg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
            Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(rangeArg);
            if (publicDataRange.containsKey(vo.getPricePolicyId())) {
                rangeSet = publicDataRange.get(vo.getPricePolicyId());
            }
            rangeSet.add(String.valueOf(ei));
            publicCouponManager.updateObjDataRange(vo.getEa(),vo.getPricePolicyId(),rangeSet,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
            publicCouponManager.updateObjDataRange(vo.getEa(),couponAddResult.getData().getObjectData().getId(),Sets.newHashSet(String.valueOf(ei)),CrmObjectApiNameEnum.COUPON_OBJ.getName());
        } else if (SceneEnum.PARTNER.getType() == vo.getScene()) {
            //将制券端所有的上游都添加到可见范围内
            int tenantId = eieaConverter.enterpriseAccountToId(vo.getEa());
            Set<String> tenantIds = Sets.newHashSet();
            Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
            tenantIds.addAll(allUpStreamTenantIds);
            tenantIds.add(String.valueOf(tenantId));
            publicCouponManager.updateObjDataRange(vo.getEa(),couponAddResult.getData().getObjectData().getId(),tenantIds,CrmObjectApiNameEnum.COUPON_OBJ.getName());
            //更新优惠券方案对象范围
            Set<String> rangeSet = new HashSet<>();
            PublicDataRangeArg rangeArg = new PublicDataRangeArg();
            rangeArg.setEa(vo.getEa());
            rangeArg.setIds(Lists.newArrayList(vo.getPricePolicyId()));
            rangeArg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
            Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(rangeArg);
            if (publicDataRange.containsKey(vo.getPricePolicyId())) {
                rangeSet = publicDataRange.get(vo.getPricePolicyId());
            }
            rangeSet.addAll(tenantIds);
            publicCouponManager.updateObjDataRange(vo.getEa(),vo.getPricePolicyId(),rangeSet,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
            Set<String> finalRangeSet = rangeSet;
            ThreadPoolUtils.executeWithTraceContext(()->{
                //处理优惠券对象负责人
                String couponTopEa = publicCouponManager.getCouponTopEa(vo.getEa());
                //修改优惠券负责人
                String couponTopTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(couponTopEa));
                int couponTenantId = eieaConverter.enterpriseAccountToId(vo.getEa());
                Map<String, Integer> couponRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(tenantIds), couponTopEa);
                tenantIds.forEach(rangeTenantId -> {
                    Integer owner = null;
                    //只有不是当前创建券企业,才设置负责人
                    if (!Objects.equals(rangeTenantId, String.valueOf(couponTenantId))) {
                        if (Objects.equals(rangeTenantId, couponTopTenantId)) {
                            if (!Objects.equals(vo.getEa(), couponTopEa)) {
                                owner = publicCouponManager.getAccountOwner(String.valueOf(couponTenantId), couponTopEa);
                            }
                        } else {
                            if (couponRelationOwnerMap.containsKey(rangeTenantId)) {
                                owner = couponRelationOwnerMap.get(rangeTenantId);
                            }
                        }
                        publicCouponManager.updatePublicObjOwner(rangeTenantId,owner,CrmObjectApiNameEnum.COUPON_OBJ.getName(),couponAddResult.getData().getObjectData().getId());
                    }
                });
                //处理优惠券方案对象负责人
                String createEnterprise;
                ObjectData couponPlanObj = crmV2Manager.getDetailIgnoreError(vo.getEa(), -10000, CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName(), vo.getPricePolicyId());
                if (couponPlanObj != null && couponPlanObj.containsKey("create_enterprise")) {
                    String createEnterpriseStr = couponPlanObj.getString("create_enterprise");
                    createEnterprise = createEnterpriseStr.substring(1, createEnterpriseStr.length() - 1);
                } else {
                    createEnterprise = null;
                }
                Map<String, Integer> couponPlanRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(finalRangeSet), couponTopEa);
                finalRangeSet.forEach(rangePlanTenantId -> {
                    //如果是方案创建企业,则不进行更新
                    if (StringUtils.isNotBlank(createEnterprise) && Objects.equals(rangePlanTenantId, createEnterprise)) {
                        return;
                    }
                    //判断上游企业是否为1端top企业
                    Integer owner = null;
                    if (Objects.equals(rangePlanTenantId, couponTopTenantId)) {
                        if (!Objects.equals(vo.getEa(), couponTopEa)) {
                            owner = publicCouponManager.getAccountOwner(String.valueOf(eieaConverter.enterpriseAccountToId(vo.getEa())), couponTopEa);
                        }
                    } else {
                        if (couponPlanRelationOwnerMap.containsKey(rangePlanTenantId)) {
                            owner = couponPlanRelationOwnerMap.get(rangePlanTenantId);
                        }
                    }
                    publicCouponManager.updatePublicObjOwner(rangePlanTenantId,owner,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName(),vo.getPricePolicyId());
                });
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }

        //将优惠券数据保存在本地库
        WechatCouponEntity weChatCouponEntity = BeanUtil.copy(vo,WechatCouponEntity.class);
        weChatCouponEntity.setWechatCouponId(vo.getPricePolicyId());
        weChatCouponEntity.setStockId(stockId);
        weChatCouponEntity.setId(UUIDUtil.getUUID());
        weChatCouponEntity.setOutRequestNo(stockId);
        weChatCouponEntity.setCouponId(couponAddResult.getData().getObjectData().getId());
        weChatCouponEntity.setOperator(vo.getOperator());
        weChatCouponEntity.setCouponNo(couponAddResult.getData().getObjectData().getName());
        //如果scene = 3 设置为未发送状态 0: 未下发  1: 下发
        if (SceneEnum.DEALER.getType() == vo.getScene()) {
            weChatCouponEntity.setSendDownStatus(SendDownStatusEnum.UN_SEND.getType());
        }
        if (vo.getPartnerNoticeVisibilityVO() != null) {
            weChatCouponEntity.setSendScope(GsonUtil.getGson().toJson(vo.getPartnerNoticeVisibilityVO()));
        }
        if (vo.getAccountVisibilityVO() != null) {
            weChatCouponEntity.setAccountScope(GsonUtil.getGson().toJson(vo.getAccountVisibilityVO()));
        }
        if (vo.getStoreReceiveVisibilityVO() != null) {
            weChatCouponEntity.setReceiveScope(GsonUtil.getGson().toJson(vo.getStoreReceiveVisibilityVO()));
        }
        if (vo.getStoreVisibilityVO() != null) {
            weChatCouponEntity.setStoreScope(GsonUtil.getGson().toJson(vo.getStoreVisibilityVO()));
        }
        //解析导入文件数据
        if (vo.getAccountVisibilityVO() != null && Objects.equals(vo.getAccountVisibilityVO().getType(),IMPORT)) {
            CreateWxCouponVO.ImportVisibilityVOFile accountVisibilityVOFile = GsonUtil.getGson().fromJson(vo.getAccountVisibilityVO().getValue(), new TypeToken<CreateWxCouponVO.ImportVisibilityVOFile>() {
            }.getType());
            vo.setPartnerNoticeVisibilityVOFile(accountVisibilityVOFile);
        }
        if (vo.getStoreReceiveVisibilityVO() != null && Objects.equals(vo.getStoreReceiveVisibilityVO().getType(),IMPORT)) {
            CreateWxCouponVO.ImportVisibilityVOFile storeReceiveVisibilityVOFile = GsonUtil.getGson().fromJson(vo.getStoreReceiveVisibilityVO().getValue(), new TypeToken<CreateWxCouponVO.ImportVisibilityVOFile>() {
            }.getType());
            vo.setStoreReceiveVisibilityVOFile(storeReceiveVisibilityVOFile);
        }
        //如果进行发送范围或者门店范围导入，则设置状态为导入中
        if (vo.getStoreReceiveVisibilityVOFile() != null  || vo.getPartnerNoticeVisibilityVOFile() != null){
            weChatCouponEntity.setStatus(CouponStatusEnum.IMPORT.getStatus());
        }
        weChatCouponDAO.saveWeChatCoupon(weChatCouponEntity);
        CreateCouponResult createCouponResult = new CreateCouponResult();
        createCouponResult.setStockId(stockId);
        createCouponResult.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //异步处理领取门店范围数据
        boolean handleReceiveCouponRange = vo.getStoreReceiveVisibilityVO() != null;
        if (openNewCouponRange) {
            handleReceiveCouponRange = vo.getStoreReceiveVisibilityVO() != null && !Objects.equals(vo.getStoreReceiveVisibilityVO().getType(), SELECT_CONDITION);
        }
        if (handleReceiveCouponRange) {
            ThreadPoolUtils.executeWithTraceContext(()->{
                CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = vo.getStoreReceiveVisibilityVO();
                List<String> storeAccountIds = publicCouponManager.getStoreAccountIds(vo.getEa(), storeReceiveVisibilityVO.getType(), storeReceiveVisibilityVO.getValue(),VisibilityEnum.RECEIVE_SCOPE.getType());
                if (CollectionUtils.isNotEmpty(storeAccountIds)) {
                    CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = new CreateWxCouponVO.StoreVisibilityVO();
                    storeVisibilityVO.setAccountIds(storeAccountIds);
                    weChatCouponDAO.updateStoreScope(weChatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        if (SceneEnum.PARTNER.getType() == vo.getScene()) {
            ThreadPoolUtils.executeWithTraceContext(()->{
                int tenantId = eieaConverter.enterpriseAccountToId(vo.getEa());
                Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
                //1端所有上游
                allUpStreamTenantIds.forEach(upStreamTenantId -> {
                    IncrementUpdateArg sendEditArg = new IncrementUpdateArg();
                    ObjectData sendObjectData = new ObjectData();
                    sendObjectData.put("_id",weChatCouponEntity.getCouponId());
                    sendObjectData.put("max_coupons",vo.getMaxCoupons());
                    sendEditArg.setData(sendObjectData);
                    metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(Integer.parseInt(upStreamTenantId),-10000),CrmObjectApiNameEnum.COUPON_OBJ.getName(),sendEditArg);
                });
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        //异步处理发送范围导入,门店范围导入
        if (vo.getPartnerNoticeVisibilityVOFile() != null || vo.getStoreReceiveVisibilityVOFile() != null) {
            ThreadPoolUtils.executeWithTraceContext(()->{
                if (vo.getPartnerNoticeVisibilityVOFile() != null) {
                    publicCouponManager.importSendScope(vo.getEa(), weChatCouponEntity.getId(),vo.getPartnerNoticeVisibilityVOFile().getExt(), vo.getPartnerNoticeVisibilityVOFile().getPath());
                }
                if (vo.getStoreReceiveVisibilityVOFile() != null) {
                    publicCouponManager.importStoreScope(vo.getEa(),weChatCouponEntity.getId(),vo.getStoreReceiveVisibilityVOFile().getExt(), vo.getStoreReceiveVisibilityVOFile().getPath());
                }
                weChatCouponDAO.updateCouponStatus(weChatCouponEntity.getId(),CouponStatusEnum.NORMAL.getStatus());
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess(createCouponResult);
    }

    @Override
    public Result<PageResult<UserCouponListResult>> queryUserCouponList(String ea, QueryUserCouponListVo vo) {
        PageResult<UserCouponListResult> userCouponPageResult = new PageResult<>();
        List<UserCouponListResult> result = Lists.newArrayList();
        userCouponPageResult.setPageNum(vo.getPageNum());
        userCouponPageResult.setPageSize(vo.getPageSize());
        userCouponPageResult.setTotalCount(0);
        PaasQueryUserCouponArg arg = new PaasQueryUserCouponArg();
        if (CouponQueryTypeEnum.MEMBER.getType() == vo.getQueryType()) {
            arg.setMemberId(vo.getMemberId());
        } else if (CouponQueryTypeEnum.PARTNER.getType() == vo.getQueryType()){
            arg.setAccountId(vo.getEROuterUid());
            arg.setPartnerId(vo.getEROuterTenantId());
        }
        arg.setObjectApiName(CrmObjectApiNameEnum.USER_COUPON_OBJ.getName());
        arg.setPageNumber(vo.getPageNum());
        arg.setPageSize(vo.getPageSize());
        if (StringUtils.isNotBlank(vo.getStatus())){
            arg.setStatus(vo.getStatus());
        }
        Page<ObjectData> userCouponList = wxCouponPayManager.listUserCoupon(ea, null, arg);
        if (userCouponList == null || CollectionUtils.isEmpty(userCouponList.getDataList())){
            userCouponPageResult.setResult(result);
            return Result.newSuccess(userCouponPageResult);
        }
        //数据处理
        userCouponList.getDataList().stream().forEach(objectData -> {
            String objectDataStr = GsonUtil.getGson().toJson(objectData);
            UserCouponResult userCouponResult = GsonUtil.getGson().fromJson(objectDataStr, UserCouponResult.class);
            WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryWeChatCouponByStockId(userCouponResult.getCouponId());
            UserCouponListResult userCouponListResult = BeanUtil.copy(wechatCouponEntity, UserCouponListResult.class);
            if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(wechatCouponEntity.getStockType())){
                userCouponListResult.setType(CouponTypeEnum.EXCHANGE_COUPON.getType());
            } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(wechatCouponEntity.getStockType())){
                userCouponListResult.setType(CouponTypeEnum.DISCOUNT_COUPON.getType());
            } else if (CouponTypeEnum.NORMAL_COUPON.getName().equals(wechatCouponEntity.getStockType())){
                userCouponListResult.setType(CouponTypeEnum.NORMAL_COUPON.getType());
            }
            userCouponListResult.setTransactionMinimum(AmountUtil.changeF2Y(wechatCouponEntity.getTransactionMinimum()));
            if (wechatCouponEntity.getDiscountAmount() != null) {
                userCouponListResult.setDiscountAmount(AmountUtil.changeF2Y(wechatCouponEntity.getDiscountAmount()));
            }
            if (wechatCouponEntity.getExchangePrice() != null) {
                userCouponListResult.setExchangePrice(AmountUtil.changeF2Y(wechatCouponEntity.getExchangePrice()));
            }
            if (wechatCouponEntity.getDiscountPercent() != null) {
                userCouponListResult.setDiscountPercent(AmountUtil.changeCount(wechatCouponEntity.getDiscountPercent()));
            }
            userCouponListResult.setObjectId(wechatCouponEntity.getId());
            result.add(userCouponListResult);
        });
        userCouponPageResult.setTotalCount(userCouponList.getTotal());
        userCouponPageResult.setResult(result);
        return Result.newSuccess(userCouponPageResult);
    }

    @Override
    public Result<PageResult<CouponResult>> queryCouponList(String ea, QueryCouponListVo vo) {
        PageResult<CouponResult> couponPageResult = new PageResult<>();
        List<CouponResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        //查优惠券批次对象
        PaasQueryCouponArg couponArg = new PaasQueryCouponArg();
        couponArg.setMarketingEventId(vo.getMarketingEventId());
        couponArg.setScene(vo.getScene());
        couponArg.setPageNumber(vo.getPageNum());
        couponArg.setPageSize(vo.getPageSize());
        InnerPage<ObjectData> dataPage = publicCouponManager.listPageCoupon(ea, -10000, couponArg);
        if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())) {
            return Result.newSuccess(couponPageResult);
        }
        List<ObjectData> couponDataList = dataPage.getDataList();
        Map<String,Integer> maxCouponsMap = couponDataList.stream().collect(Collectors.toMap(ObjectData::getId, e -> e.getInt("max_coupons")));
        List<String> couponIds = couponDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryCouponIds(couponIds);
        //批量获取优惠券方案
        List<String> couponPlanIds = couponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Set<String> eas = couponEntities.stream().map(WechatCouponEntity::getEa).collect(Collectors.toSet());
        Set<String> marketingEventIds = couponEntities.stream().map(WechatCouponEntity::getMarketingEventId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(ea,couponPlanIds);
        //批量获取优惠券使用数量
        List<String> stockIds = couponEntities.stream().map(WechatCouponEntity::getStockId).collect(Collectors.toList());
        Map<String, Long> useCountMap = wxCouponPayManager.getPartnerCouponCount(ea, stockIds, CouponUseStautsEnum.USED.getStatus());
        Map<String, Long> receiveCountMap = wxCouponPayManager.getPartnerCouponCount(ea, stockIds, null);
        //获取市场活动名称
        Map<String, ObjectData> marketingEventObjMap = crmV2Manager.getObjectDataMapByIds(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, Lists.newArrayList("name"));
        //获取所属企业名称
        Map<String,String> companyNameMap = publicCouponManager.queryBelongCompanyName(eas);
        couponEntities.stream().forEach(wechatCouponEntity -> {
            CouponResult couponResult = new CouponResult();
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                couponResult = BeanUtil.copy(wechatCouponEntity, CouponResult.class);
                couponResult.setObjectId(wechatCouponEntity.getId());
                copyPlanInfo(couponPlanInfoResult,couponResult);
            }
            if (StringUtils.isNotBlank(wechatCouponEntity.getMarketingEventId()) && marketingEventObjMap.containsKey(wechatCouponEntity.getMarketingEventId())) {
                couponResult.setMarketingEventName(marketingEventObjMap.get(wechatCouponEntity.getMarketingEventId()).getName());
            }
            if (companyNameMap.containsKey(wechatCouponEntity.getEa())) {
                couponResult.setBelongEnterpriseName(companyNameMap.get(wechatCouponEntity.getEa()));
            }
            couponResult.setReceiveCount((int) (receiveCountMap.get(wechatCouponEntity.getStockId()) == null ? 0L : receiveCountMap.get(wechatCouponEntity.getStockId())));
            couponResult.setUsedCont((int) (useCountMap.get(wechatCouponEntity.getStockId()) == null ? 0L : useCountMap.get(wechatCouponEntity.getStockId())));
            if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                int totalCount = totalCouponCount(wechatCouponEntity.getDealerCount(),wechatCouponEntity.getId(),wechatCouponEntity.getEa());
                if (maxCouponsMap.containsKey(wechatCouponEntity.getCouponId())) {
                    totalCount = maxCouponsMap.get(wechatCouponEntity.getCouponId());
                }
                int remainCount = totalCount - couponResult.getReceiveCount();
                couponResult.setRemainCount(remainCount);
            } else {
                int remainCount = wechatCouponEntity.getMaxCoupons() - couponResult.getReceiveCount();
                couponResult.setRemainCount(remainCount);
            }
            result.add(couponResult);
        });
        couponPageResult.setTotalCount(dataPage.getTotalCount());
        couponPageResult.setResult(result);
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<Void> updateWeChatCoupon(UpdateWeChatCouponVo vo, String ea) {
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (couponEntity == null) {
            log.warn("publicCouponService.updateWeChatCoupon no data objectId={}",vo.getObjectId());
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (vo.getMaxCoupons() != null) {
            int count = memberCouponBindDAO.queryReceiveAndUseCoupon(ea, couponEntity.getStockId());
            if (vo.getMaxCoupons() < count) {
                return Result.newError(SHErrorCode.COUNT_MORE_RECEIVE);
            }
        }
        //处理可售客户发送范围
        if (SceneEnum.DEALER.getType() == couponEntity.getScene() && vo.getAccountVisibilityVO() != null && !Objects.equals(vo.getAccountVisibilityVO().getType(),IMPORT)) {
            UpdateWeChatCouponVo.AccountVisibilityVO accountVisibilityVO = vo.getAccountVisibilityVO();
            List<String> outTenantIds = publicCouponManager.getOutTenantIds(ea, accountVisibilityVO.getType(), accountVisibilityVO.getValue(),VisibilityEnum.SEND_SCOPE.getType());
            if (CollectionUtils.isEmpty(outTenantIds)) {
                return Result.newError(SHErrorCode.NOT_FOUND_SEND_ACCOUNT);
            }
            if (vo.getPartnerNoticeVisibilityVO() == null) {
                UpdateWeChatCouponVo.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = new UpdateWeChatCouponVo.PartnerNoticeVisibilityVO();
                partnerNoticeVisibilityVO.setOuterTenantIds(outTenantIds);
                vo.setPartnerNoticeVisibilityVO(partnerNoticeVisibilityVO);
            }
        }
        //处理门店发送范围
        if (vo.getStoreReceiveVisibilityVO() != null && !Objects.equals(vo.getStoreReceiveVisibilityVO().getType(),IMPORT)) {
            UpdateWeChatCouponVo.StoreReceiveVisibilityVO storeReceiveVisibilityVO = vo.getStoreReceiveVisibilityVO();
            int countStore = publicCouponManager.getStoreAccountIdsCount(ea, storeReceiveVisibilityVO.getType(), storeReceiveVisibilityVO.getValue(),VisibilityEnum.RECEIVE_SCOPE.getType());
            if (countStore <= 0) {
                return Result.newError(SHErrorCode.NOT_FOUND_RECEIVE_ACCOUNT);
            }
        }
        Map<String,Object> updateMap = new HashMap<>();
        WechatCouponEntity weChatCouponEntity = new WechatCouponEntity();
        weChatCouponEntity.setId(vo.getObjectId());
        if (SceneEnum.PARTNER.getType() == couponEntity.getScene() && vo.getMaxCoupons() != null && !vo.getMaxCoupons().equals(couponEntity.getMaxCoupons())) {
            weChatCouponEntity.setMaxCoupons(vo.getMaxCoupons());
            updateMap.put("max_coupons",vo.getMaxCoupons());
            crmV2Manager.updateCrmObj(ea, -10000, couponEntity.getCouponId(),updateMap,CrmObjectApiNameEnum.COUPON_OBJ.getName());
            ThreadPoolUtils.executeWithTraceContext(()->{
                int tenantId = eieaConverter.enterpriseAccountToId(ea);
                Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
                //更新所有上游
                allUpStreamTenantIds.forEach(upStreamTenantId -> {
                    IncrementUpdateArg sendEditArg = new IncrementUpdateArg();
                    ObjectData sendObjectData = new ObjectData();
                    sendObjectData.put("_id",couponEntity.getCouponId());
                    sendObjectData.put("max_coupons",vo.getMaxCoupons());
                    sendEditArg.setData(sendObjectData);
                    metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(Integer.parseInt(upStreamTenantId),-10000),CrmObjectApiNameEnum.COUPON_OBJ.getName(),sendEditArg);
                });
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        if (vo.getPartnerNoticeVisibilityVO() != null) {
            weChatCouponEntity.setSendScope(GsonUtil.getGson().toJson(vo.getPartnerNoticeVisibilityVO()));
        }
        if (vo.getAccountVisibilityVO() != null) {
            weChatCouponEntity.setAccountScope(GsonUtil.getGson().toJson(vo.getAccountVisibilityVO()));
        }
        if (vo.getStoreReceiveVisibilityVO() != null) {
            weChatCouponEntity.setReceiveScope(GsonUtil.getGson().toJson(vo.getStoreReceiveVisibilityVO()));
        }
        if (vo.getStoreVisibilityVO() != null) {
            weChatCouponEntity.setStoreScope(GsonUtil.getGson().toJson(vo.getStoreVisibilityVO()));
        }
        //解析导入文件数据
        if (vo.getAccountVisibilityVO() != null && Objects.equals(vo.getAccountVisibilityVO().getType(),IMPORT)) {
            UpdateWeChatCouponVo.ImportVisibilityVOFile accountVisibilityVOFile = GsonUtil.getGson().fromJson(vo.getAccountVisibilityVO().getValue(), new TypeToken<UpdateWeChatCouponVo.ImportVisibilityVOFile>() {
            }.getType());
            vo.setPartnerNoticeVisibilityVOFile(accountVisibilityVOFile);
        }
        if (vo.getStoreReceiveVisibilityVO() != null && Objects.equals(vo.getStoreReceiveVisibilityVO().getType(),IMPORT)) {
            UpdateWeChatCouponVo.ImportVisibilityVOFile storeReceiveVisibilityVOFile = GsonUtil.getGson().fromJson(vo.getStoreReceiveVisibilityVO().getValue(), new TypeToken<UpdateWeChatCouponVo.ImportVisibilityVOFile>() {
            }.getType());
            vo.setStoreReceiveVisibilityVOFile(storeReceiveVisibilityVOFile);
        }
        //如果进行发送范围或者门店范围导入，则设置状态为导入中
        if (vo.getStoreReceiveVisibilityVOFile() != null  || vo.getPartnerNoticeVisibilityVOFile() != null){
            weChatCouponEntity.setStatus(CouponStatusEnum.IMPORT.getStatus());
        }
        weChatCouponDAO.updateCouponInfo(weChatCouponEntity);
        //异步处理领取门店范围数据
        if (vo.getStoreReceiveVisibilityVO() != null && !Objects.equals(vo.getStoreReceiveVisibilityVO().getType(),SELECT_CONDITION)) {
            ThreadPoolUtils.executeWithTraceContext(()->{
                UpdateWeChatCouponVo.StoreReceiveVisibilityVO storeReceiveVisibilityVO = vo.getStoreReceiveVisibilityVO();
                List<String> storeAccountIds = publicCouponManager.getStoreAccountIds(ea, storeReceiveVisibilityVO.getType(), storeReceiveVisibilityVO.getValue(),VisibilityEnum.RECEIVE_SCOPE.getType());
                if (CollectionUtils.isNotEmpty(storeAccountIds)) {
                    CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = new CreateWxCouponVO.StoreVisibilityVO();
                    storeVisibilityVO.setAccountIds(storeAccountIds);
                    weChatCouponDAO.updateStoreScope(weChatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        //异步处理发送范围导入,门店范围导入
        if (vo.getPartnerNoticeVisibilityVOFile() != null || vo.getStoreReceiveVisibilityVOFile() != null) {
            ThreadPoolUtils.executeWithTraceContext(()->{
                if (vo.getPartnerNoticeVisibilityVOFile() != null) {
                    publicCouponManager.importSendScope(ea, weChatCouponEntity.getId(),vo.getPartnerNoticeVisibilityVOFile().getExt(), vo.getPartnerNoticeVisibilityVOFile().getPath());
                }
                if (vo.getStoreReceiveVisibilityVOFile() != null) {
                    publicCouponManager.importStoreScope(ea,weChatCouponEntity.getId(),vo.getStoreReceiveVisibilityVOFile().getExt(), vo.getStoreReceiveVisibilityVOFile().getPath());
                }
                weChatCouponDAO.updateCouponStatus(weChatCouponEntity.getId(),CouponStatusEnum.NORMAL.getStatus());
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<CouponResult> queryCouponDetail(String ea, QueryCouponDetailVo vo) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getObjectId()),I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_610));
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (couponEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        boolean isDataRangeAvailable = false;
        if (StringUtils.isNotBlank(vo.getERUpstreamEa())) {
            ObjectData objectData = crmV2Manager.getDetail(vo.getERUpstreamEa(), -10000, CrmObjectApiNameEnum.COUPON_OBJ.getName(), couponEntity.getCouponId());
            if (objectData == null) {
                Result.newError(SHErrorCode.NOT_AUTH_LOOKUP_COUPON);
            }
            isDataRangeAvailable = true;
        }
        boolean sendScopeResultSuccess = true;
        boolean storeScopeResultSuccess = true;
        if(StringUtils.isNotBlank(couponEntity.getSendScopeImportResult())) {
            sendScopeResultSuccess = publicCouponManager.checkImportScopeResult(ea,couponEntity.getSendScopeImportResult());
        }
        if (StringUtils.isNotBlank(couponEntity.getStoreScopeImportResult())) {
            storeScopeResultSuccess = publicCouponManager.checkImportScopeResult(ea, couponEntity.getStoreScopeImportResult());
        }
        CouponResult couponResult;
        //纷享券
        couponResult = new CouponResult() ;
        GetCouponPlanInfoVo planVo = new GetCouponPlanInfoVo();
        planVo.setCouponPlanId(couponEntity.getWechatCouponId());
        Result<CouponPlanInfoResult> couponPlanInfoResultResult = wxCouponPayService.queryCouponPlanInfo(planVo, ea);
        if (couponPlanInfoResultResult.isSuccess() && null !=  couponPlanInfoResultResult.getData()) {
            couponResult = BeanUtil.copy(couponEntity, CouponResult.class);
            couponResult.setObjectId(couponEntity.getId());
            if (StringUtils.isNotBlank(couponEntity.getSendScopeImportResult())) {
                couponResult.setSendScopeImportSuccess(sendScopeResultSuccess);
            }
            if (StringUtils.isNotBlank(couponEntity.getStoreScopeImportResult())) {
                couponResult.setStoreScopeImportSuccess(storeScopeResultSuccess);
            }
            copyPlanInfo(couponPlanInfoResultResult.getData(),couponResult);
            if (StringUtils.isNotBlank(couponEntity.getTags())) {
                couponResult.setTags(GsonUtil.getGson().fromJson(couponEntity.getTags(),new TypeToken<List<TagName>>() {}.getType()));
            }
            if (StringUtils.isNotBlank(couponEntity.getSendScope())) {
                couponResult.setPartnerNoticeVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CouponResult.PartnerNoticeVisibilityVO.class));
            }
            if (StringUtils.isNotEmpty(couponEntity.getAccountScope())) {
                couponResult.setAccountVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getAccountScope(), CouponResult.AccountVisibilityVO.class));
            }
            if (StringUtils.isNotEmpty(couponEntity.getReceiveScope())) {
                couponResult.setStoreReceiveVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getReceiveScope(), CouponResult.StoreReceiveVisibilityVO.class));
            }
        }
        if (isDataRangeAvailable) {
            couponResult.setEa(vo.getERUpstreamEa());
        }
        return Result.newSuccess(couponResult);
    }

    @Override
    public Result<CouponResult> queryCouponDetailToCustomer(QueryCouponDetailVo vo) {
        if (StringUtils.isBlank(vo.getObjectId()) && StringUtils.isBlank(vo.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        WechatCouponEntity  wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (wechatCouponEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return  this.queryCouponDetail(wechatCouponEntity.getEa(), vo);
    }

    @Override
    public Result<PageResult<UserCouponListResult>> queryUserCouponListToCustomer(QueryUserCouponListVo vo) {
        PageResult<UserCouponListResult> couponPageResult = new PageResult<>();
        List<UserCouponListResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);

        List<UserCouponListResult> userCouponListResults = Lists.newArrayList();
        List<ObjectData> objectDataList;
        //查询优惠券实例对象
        if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.EXPIRE.getStatus()) || Objects.equals(vo.getStatus(),CouponInstStatusEnum.USE.getStatus())) {
            objectDataList = wxCouponPayManager.queryConponInstObjList(vo.getERUpstreamEa(),vo.getMemberId(),vo.getEROuterTenantId());
        } else {
            objectDataList = Lists.newArrayList();
            PaasQueryUserCouponArg arg = new PaasQueryUserCouponArg();
            arg.setPageNumber(vo.getPageNum());
            arg.setPageSize(vo.getPageSize());
            arg.setPartnerId(vo.getEROuterTenantId());
            arg.setStatus(vo.getStatus());
            InnerPage<ObjectData> couponInstList = publicCouponManager.queryPageCouponInst(vo.getERUpstreamEa(),-10000,arg);
            if (couponInstList != null && CollectionUtils.isNotEmpty(couponInstList.getDataList())) {
                objectDataList = couponInstList.getDataList();
                couponPageResult.setTotalCount(couponInstList.getTotalCount());
            }
        }
        if (CollectionUtils.isEmpty(objectDataList)) {
            couponPageResult.setResult(userCouponListResults);
            return Result.newSuccess(couponPageResult);
        }
        //批量获取优惠券方案
        List<String> couponPlanIds = objectDataList.stream().map(e -> e.getString("coupon_plan_id")).collect(Collectors.toList());
        List<String> stockIds = objectDataList.stream().map(e -> e.getString("coupon_id")).collect(Collectors.toList());
        Map<String,WechatCouponEntity> resultMap;
        List<WechatCouponEntity> wechatCouponEntities = weChatCouponDAO.queryStockIds(stockIds);
        if (CollectionUtils.isEmpty(wechatCouponEntities)) {
            resultMap = new HashMap<>();
        } else {
            resultMap = wechatCouponEntities.stream().collect(Collectors.toMap(WechatCouponEntity::getStockId, Function.identity(), (key1, key2) -> key2));
        }
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getERUpstreamEa(),couponPlanIds);
        objectDataList.forEach(objectData -> {
            String couponPlanId = objectData.getString("coupon_plan_id");
            String couponId = objectData.getString("coupon_id");
            WechatCouponEntity wechatCouponEntity = resultMap.get(couponId);
            UserCouponListResult userCouponListResult = new UserCouponListResult();
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(couponPlanId);
            if (null !=  couponPlanInfoResult) {
                if (wechatCouponEntity != null) {
                    userCouponListResult = BeanUtil.copy(wechatCouponEntity, UserCouponListResult.class);
                    userCouponListResult.setObjectId(wechatCouponEntity.getId());
                    userCouponListResult.setCreateCouponType(wechatCouponEntity.getCreateCouponType());
                }
                copyPlanInfoByH5(couponPlanInfoResult,userCouponListResult);
                userCouponListResult.setCouponInstanceId(objectData.getId());
                if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.EXPIRE.getStatus())) {
                    Long endDate = couponPlanInfoResult.getEndDate();
                    if (endDate < DateUtil.getDayStartTime(new Date())) {
                        userCouponListResults.add(userCouponListResult);
                    }
                } else if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.USE.getStatus())) {
                    Long endDate = couponPlanInfoResult.getEndDate();
                    if (endDate >= DateUtil.getDayStartTime(new Date())) {
                        userCouponListResults.add(userCouponListResult);
                    }
                }
            }
            result.add(userCouponListResult);
        });
        if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.EXPIRE.getStatus()) || Objects.equals(vo.getStatus(),CouponInstStatusEnum.USE.getStatus())) {
            couponPageResult.setTotalCount(userCouponListResults.size());
            // 手动分页
            PageUtil<UserCouponListResult> pageUtil = new PageUtil<>(userCouponListResults, vo.getPageSize());
            if (CollectionUtils.isEmpty(pageUtil.getData())) {
                couponPageResult.setResult(userCouponListResults);
                return Result.newSuccess(couponPageResult);
            }
            couponPageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
            return Result.newSuccess(couponPageResult);
        }
        couponPageResult.setResult(result);
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<CouponDetailStatisticResult> queryCouponStockStatistic(String objectId, String ea) {
        CouponDetailStatisticResult couponDetailStatisticResult = new CouponDetailStatisticResult();
        couponDetailStatisticResult.setMaxCount(0);
        couponDetailStatisticResult.setRemainCount(0);
        couponDetailStatisticResult.setUsedCount(0);
        couponDetailStatisticResult.setReceiveCount(0);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(objectId);
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        //处理多级下发时,数据统计判断
        if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
            //如果未下发,则默认展示0
            if (Objects.equals(wechatCouponEntity.getSendDownStatus(),SendDownStatusEnum.UN_SEND.getType())) {
                return Result.newSuccess(couponDetailStatisticResult);
            }
            //如果没有任何下游确认则默认展示0
            boolean isParticipate = publicCouponManager.queryCountCouponDistribution(ea,wechatCouponEntity.getCouponId());
            if (!isParticipate) {
                return Result.newSuccess(couponDetailStatisticResult);
            }
        }
        ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.COUPON_OBJ.getName(), wechatCouponEntity.getCouponId());
        String stockId = wechatCouponEntity.getStockId();
        if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
            couponDetailStatisticResult.setMaxCount(totalCouponCount(wechatCouponEntity.getDealerCount(),wechatCouponEntity.getId(),ea));
            if (objectData != null && objectData.getInt("max_coupons") != null) {
                couponDetailStatisticResult.setMaxCount(objectData.getInt("max_coupons"));
            }
        }else if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
            couponDetailStatisticResult.setMaxCount(wechatCouponEntity.getMaxCoupons());
        }
        couponDetailStatisticResult.setReceiveCount(publicCouponManager.queryCouponInstCount(eieaConverter.enterpriseAccountToId(ea),stockId,null));
        couponDetailStatisticResult.setUsedCount(publicCouponManager.queryCouponInstCount(eieaConverter.enterpriseAccountToId(ea),stockId,CouponUseStautsEnum.USED.getStatus()));
        couponDetailStatisticResult.setRemainCount(couponDetailStatisticResult.getMaxCount() - couponDetailStatisticResult.getReceiveCount());
        return Result.newSuccess(couponDetailStatisticResult);
    }

    @Override
    public Result<PageResult<PartnerReceiveResult>> queryPartnerReceiveList(String objectId, String ea, Integer pageNo, Integer pageSize) {
        PageResult<PartnerReceiveResult> pageResult = new PageResult<>();
        List<PartnerReceiveResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setPageNum(pageNo);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(pageNo, pageSize, true);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(objectId);
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String companyEa = ea;
        String stockId = wechatCouponEntity.getStockId();
        List<MemberCouponBindEntity> memberCouponBindEntities =  memberCouponBindDAO.queryPartnerList(stockId,page);
        if (CollectionUtils.isNotEmpty(memberCouponBindEntities)) {
            List<String> accountIds = memberCouponBindEntities.stream().map(MemberCouponBindEntity::getAccountId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            //针对上下游的查询企业名称时,需要查制券企业的下游
            if (Objects.equals(wechatCouponEntity.getScene(),SceneEnum.PARTNER.getType())) {
                companyEa = wechatCouponEntity.getEa();
            }
            Map<String, String> companyIdToCompanyMap = wxCouponPayManager.batchGetEnterpriseRelationName(accountIds, companyEa);
            memberCouponBindEntities.forEach(memberCouponBindEntity -> {
                PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
                partnerReceiveResult.setReceiveTime(memberCouponBindEntity.getSendTime());
                partnerReceiveResult.setDepositObject(true);
                partnerReceiveResult.setStatus(memberCouponBindEntity.getStatus());
                partnerReceiveResult.setOutUid(memberCouponBindEntity.getAccountId());
                partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(memberCouponBindEntity.getAccountId()));
                queryLiveListResult.add(partnerReceiveResult);
            });
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(queryLiveListResult);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<CouponListResult>> getAppCouponList(GetAppCouponListVO vo) {
        PageResult<CouponListResult> couponPageResult = new PageResult<>();
        List<CouponListResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        String accountId = null;
        int upEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        HeaderObj headerObj = HeaderObj.newInstance(upEi);
        UpstreamAndDownstreamOuterTenantIdOutArg outArg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        outArg.setDownstreamOuterTenantId(Long.valueOf(vo.getEROuterTenantId()));
        outArg.setUpstreamEa(vo.getERUpstreamEa());
        RestResult<String> mapperResult = enterpriseRelationService.getMapperObjectId(headerObj, outArg);
        if (mapperResult.isSuccess() && StringUtils.isNotBlank(mapperResult.getData())) {
            accountId = mapperResult.getData();
        }
        if (StringUtils.isBlank(accountId)) {
            return Result.newError(SHErrorCode.NOT_FOUND_DOWN_ACCOUNT);
        }
        PaasQueryCouponArg arg = new PaasQueryCouponArg();
        arg.setObjectApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        arg.setStatus(CouponStatusEnum.NORMAL.getStatus());
        InnerPage<ObjectData> dataPage = publicCouponManager.listPageCoupon(vo.getERUpstreamEa(), -10000, arg);
        if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())) {
            return Result.newSuccess(couponPageResult);
        }
        List<ObjectData> couponDataList = dataPage.getDataList();
        List<String> couponIds = couponDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryCouponIds(couponIds);
        if (CollectionUtils.isEmpty(couponEntities)) {
            return Result.newSuccess(couponPageResult);
        }
        List<String> couponPlanIds = couponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getERUpstreamEa(),couponPlanIds);
        for (WechatCouponEntity objectData : couponEntities) {
            boolean flag = true;
            boolean inReceiveRange = publicCouponManager.isInReceiveCouponRange(objectData.getEa(),objectData.getStoreScope(),objectData.getReceiveScope(),accountId);
            //查询制券企业所有上游
            int tenantId = eieaConverter.enterpriseAccountToId(objectData.getEa());
            Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
            //多级下游
            if (Objects.equals(objectData.getScene(),SceneEnum.DEALER.getType())) {
                allUpStreamTenantIds.add(String.valueOf(tenantId));
                if (allUpStreamTenantIds.contains(String.valueOf(upEi))) {
                    flag = false;
                }
            } else if (Objects.equals(objectData.getScene(),SceneEnum.PARTNER.getType())) {
                //上下游
                if (allUpStreamTenantIds.contains(String.valueOf(upEi))) {
                    flag = false;
                }
            }
            if (inReceiveRange && flag) {
                //查询分享券详情
                if (couponPlanInfoResultMap.containsKey(objectData.getWechatCouponId()) && null != couponPlanInfoResultMap.get(objectData.getWechatCouponId())) {
                    CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(objectData.getWechatCouponId());
                    if (couponPlanInfoResult.getEndDate() < DateUtil.getDayStartTime(new Date()) || couponPlanInfoResult.getStartDate() > DateUtil.getDayStartTime(new Date())) {
                        continue;
                    }
                    CouponListResult couponResult = BeanUtil.copy(objectData, CouponListResult.class);
                    couponResult.setObjectId(objectData.getId());
                    copyPlanInfoByApp(couponPlanInfoResultMap.get(objectData.getWechatCouponId()), couponResult);
                    result.add(couponResult);
                }
            }
        }
        couponPageResult.setTotalCount(result.size());
        // 手动分页
        PageUtil<CouponListResult> pageUtil = new PageUtil<>(result, vo.getPageSize());
        if (CollectionUtils.isEmpty(pageUtil.getData())) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        couponPageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<Void> updateCouponStatus(String objectId, String ea, Integer status) {
        return null;
    }

    @Override
    public Result<CouponPlanInfoResult> queryCouponPlanInfo(GetCouponPlanInfoVo vo, String ea) {
        ObjectData objectData = crmV2Manager.getObjectData(ea, -10000, "CouponPlanObj", vo.getCouponPlanId());
        if (objectData == null) {
            return Result.newSuccess();
        }
        String objectDataStr = GsonUtil.getGson().toJson(objectData);
        CouponPlanInfoResult couponPlanInfoResult = GsonUtil.getGson().fromJson(objectDataStr, CouponPlanInfoResult.class);
        return Result.newSuccess(couponPlanInfoResult);
    }

    @Override
    public Result<Void> sendCouponPartner(SendCouponPartnerVo vo) {
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (couponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        CreateWxCouponVO.AccountVisibilityVO accountVisibilityVO = GsonUtil.getGson().fromJson(couponEntity.getAccountScope(), new TypeToken<CreateWxCouponVO.AccountVisibilityVO>() {}.getType());
        List<String> partnerNoticeTenantIds = publicCouponManager.getTenantIds(vo.getEa(), accountVisibilityVO.getType(), accountVisibilityVO.getValue(),couponEntity.getSendScope());
        if (CollectionUtils.isEmpty(partnerNoticeTenantIds)) {
            log.warn("sendCouponPartner partnerNoticeTenantIds is Empty ea:{}",vo.getEa());
            return Result.newError(SHErrorCode.NOT_FOUND_SEND_ACCOUNT);
        }
        //如果是可选择参与,则直接发送伙伴营销通知
        if (Objects.equals(ParticipateEnum.YES.getType(),couponEntity.getIsParticipate())) {
            try {
                //1.下发模板消息
                PartnerNoticeSendArg noticeSendArg = new PartnerNoticeSendArg();
                //设置参数
                CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CreateWxCouponVO.PartnerNoticeVisibilityVO.class);
                PartnerNoticeSendArg.PartnerNoticeVisibilityVO partnerVo = new PartnerNoticeSendArg.PartnerNoticeVisibilityVO();
                partnerVo.setEaList(partnerNoticeVisibilityVO.getOuterTenantIds());
                partnerVo.setTenantGroupIdList(partnerNoticeVisibilityVO.getOuterTenantGroupIds());
                noticeSendArg.setPartnerNoticeVisibilityVO(partnerVo);
                noticeSendArg.setSendType(NoticeSendTypeEnum.NORMAL.getType());
                noticeSendArg.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_2956_1));
                noticeSendArg.setContent(vo.getObjectId());
                noticeSendArg.setContentType(NoticeContentTypeEnum.SEND_COUPON.getType());
                noticeService.sendCouponNotice(vo.getEa(),vo.getFsUserId(),noticeSendArg);
            } catch (Exception e) {
                log.warn("下发模板消息失败:{}",e);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
        }
        ThreadPoolUtils.executeWithTraceContext(()->{
            String couponTopEa = publicCouponManager.getCouponTopEa(vo.getEa());
            //下游及其所有的经销商
            int tenantId = eieaConverter.enterpriseAccountToId(vo.getEa());
            Set<String> sendDownTenantIds = Sets.newHashSet();
            //循环处理每个发送范围内的下游及所有经销商
            partnerNoticeTenantIds.forEach(partnerNoticeTenantId -> {
                sendDownTenantIds.addAll(publicCouponManager.getDownTenantIds(Sets.newHashSet(partnerNoticeTenantId), String.valueOf(partnerNoticeTenantId)));
            });
            Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
            Set<String> downTenantIds = new HashSet<>(sendDownTenantIds);
            downTenantIds.addAll(allUpStreamTenantIds);
            downTenantIds.add(String.valueOf(tenantId));
            // 如果是不可选择参与,则直接修改优惠券批次对象,优惠券方案对象,市场活动对象可见范围(下游及其所有经销商)
            if (Objects.equals(ParticipateEnum.NO.getType(),couponEntity.getIsParticipate())) {
                // downTenantIds isEmpty
                publicCouponManager.updateObjDataRange(vo.getEa(),couponEntity.getCouponId(),downTenantIds,CrmObjectApiNameEnum.COUPON_OBJ.getName());
                //修改优惠券负责人
                String couponTopTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(couponTopEa));
                int couponTenantId = eieaConverter.enterpriseAccountToId(couponEntity.getEa());
                Map<String, Integer> couponRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(downTenantIds), couponTopEa);
                downTenantIds.forEach(rangeTenantId -> {
                    Integer owner = null;
                    //只有不是当前创建券企业,才设置负责人
                    if (!Objects.equals(rangeTenantId, String.valueOf(couponTenantId))) {
                        if (Objects.equals(rangeTenantId, couponTopTenantId)) {
                            if (!Objects.equals(couponEntity.getEa(), couponTopEa)) {
                                owner = publicCouponManager.getAccountOwner(String.valueOf(couponTenantId), couponTopEa);
                            }
                        } else {
                            if (couponRelationOwnerMap.containsKey(rangeTenantId)) {
                                owner = couponRelationOwnerMap.get(rangeTenantId);
                            }
                        }
                        publicCouponManager.updatePublicObjOwner(rangeTenantId,owner,CrmObjectApiNameEnum.COUPON_OBJ.getName(),couponEntity.getCouponId());
                    }

                });
                //修改优惠券方案的可见范围
                Set<String> rangeSet = new HashSet<>();
                PublicDataRangeArg rangeArg = new PublicDataRangeArg();
                rangeArg.setEa(vo.getEa());
                rangeArg.setIds(Lists.newArrayList(couponEntity.getWechatCouponId()));
                rangeArg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
                Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(rangeArg);
                if (publicDataRange.containsKey(couponEntity.getWechatCouponId())) {
                    rangeSet = publicDataRange.get(couponEntity.getWechatCouponId());
                }
                rangeSet.addAll(downTenantIds);
                publicCouponManager.updateObjDataRange(vo.getEa(),couponEntity.getWechatCouponId(),rangeSet,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
                //修改优惠券方案负责人
                String createEnterprise;
                ObjectData couponPlanObj = crmV2Manager.getDetailIgnoreError(vo.getEa(), -10000, CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName(), couponEntity.getWechatCouponId());
                if (couponPlanObj != null && couponPlanObj.containsKey("create_enterprise")) {
                    String createEnterpriseStr = couponPlanObj.getString("create_enterprise");
                    createEnterprise = createEnterpriseStr.substring(1, createEnterpriseStr.length() - 1);
                } else {
                    createEnterprise = null;
                }
                Map<String, Integer> couponPlanRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(rangeSet), couponTopEa);
                rangeSet.forEach(rangePlanTenantId -> {
                    //如果是方案创建企业,则不进行更新
                    if (StringUtils.isNotBlank(createEnterprise) && Objects.equals(rangePlanTenantId, createEnterprise)) {
                        return;
                    }
                    //判断上游企业是否为1端top企业
                    Integer owner = null;
                    if (Objects.equals(rangePlanTenantId, couponTopTenantId)) {
                        if (!Objects.equals(couponEntity.getEa(), couponTopEa)) {
                            owner = publicCouponManager.getAccountOwner(String.valueOf(eieaConverter.enterpriseAccountToId(couponEntity.getEa())), couponTopEa);
                        }
                    } else {
                        if (couponPlanRelationOwnerMap.containsKey(rangePlanTenantId)) {
                            owner = couponPlanRelationOwnerMap.get(rangePlanTenantId);
                        }
                    }
                    publicCouponManager.updatePublicObjOwner(rangePlanTenantId,owner,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName(),couponEntity.getWechatCouponId());
                });
                //publicCouponManager.updateObjDataRange(vo.getEa(),couponEntity.getMarketingEventId(),downTenantIds,CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                //更新1端发送数量
                ObjectData objectData = new ObjectData();
                objectData.put("_id",couponEntity.getCouponId());
                objectData.put("max_coupons",couponEntity.getDealerCount() * (partnerNoticeTenantIds.size()));
                IncrementUpdateArg editArg = new IncrementUpdateArg();
                editArg.setData(objectData);
                com.fxiaoke.crmrestapi.common.result.Result<IncrementUpdateResult> incrementUpdateResultResult = metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(tenantId, -10000), CrmObjectApiNameEnum.COUPON_OBJ.getName(), editArg);
                log.info("edit result success :{}",GsonUtil.getGson().toJson(incrementUpdateResultResult));
                //N端私有数据
                partnerNoticeTenantIds.forEach(sendTenantId -> {
                    IncrementUpdateArg sendEditArg = new IncrementUpdateArg();
                    ObjectData sendObjectData = new ObjectData();
                    sendObjectData.put("_id",couponEntity.getCouponId());
                    sendObjectData.put("max_coupons",couponEntity.getDealerCount());
                    sendEditArg.setData(sendObjectData);
                    metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(Integer.parseInt(sendTenantId),-10000),CrmObjectApiNameEnum.COUPON_OBJ.getName(),sendEditArg);
                });
                //1端所有上游
                allUpStreamTenantIds.forEach(upStreamTenantId -> {
                    IncrementUpdateArg sendEditArg = new IncrementUpdateArg();
                    ObjectData sendObjectData = new ObjectData();
                    sendObjectData.put("_id",couponEntity.getCouponId());
                    sendObjectData.put("max_coupons",couponEntity.getDealerCount() * (partnerNoticeTenantIds.size()));
                    sendEditArg.setData(sendObjectData);
                    metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(Integer.parseInt(upStreamTenantId),-10000),CrmObjectApiNameEnum.COUPON_OBJ.getName(),sendEditArg);
                });
            }
            //处理下发记录
            Set<String> ternetIds = new HashSet<>();
            if (StringUtils.isNotBlank(couponEntity.getSendScope())) {
                CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CreateWxCouponVO.PartnerNoticeVisibilityVO.class);
                ternetIds.addAll(partnerNoticeVisibilityVO.getOuterTenantIds());
            }
            List<Long> outTernatIds = new ArrayList<>();
            ternetIds.forEach(ternatId ->{
                outTernatIds.add(Long.parseLong(ternatId));
            });
            //获取下游企业的ea
            Map<Long,String> outerTenantId2EaMap = new HashMap<>();
            BatchGetEaByOuterTenantIdArg batchTenantIdArg = new BatchGetEaByOuterTenantIdArg();
            batchTenantIdArg.setOuterTenantIds(outTernatIds);
            RestResult<BatchGetEaByOuterTenantIdResult> outerTenantIdResultRestResult = fxiaokeAccountService.batchGetEaByOuterTenantId(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(couponEntity.getEa())), batchTenantIdArg);
            if (outerTenantIdResultRestResult.isSuccess() &&  null != outerTenantIdResultRestResult.getData()) {
                outerTenantId2EaMap = outerTenantIdResultRestResult.getData().getOuterTenantId2EaMap();
            }
            for (String ternetId : ternetIds) {
                if (!outerTenantId2EaMap.containsKey(Long.valueOf(ternetId))) {
                    continue;
                }
                int ei = eieaConverter.enterpriseAccountToId(outerTenantId2EaMap.get(Long.valueOf(ternetId)));
                try {
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
                    if (!getDescribeResultResult.isSuccess()) {
                        continue;
                    }
                } catch (Exception e) {
                    log.warn("objectDescribeService.getDescribe is error", e);
                    continue;
                }
                GetEnterpriseDataArg getEnterpriseDataArg = new GetEnterpriseDataArg();
                getEnterpriseDataArg.setEnterpriseId(ei);
                GetEnterpriseDataResult enterpriseData = enterpriseEditionService.getEnterpriseData(getEnterpriseDataArg);
                //记录下发经销商数据
                SendDealerCouponRecordEntity recordEntity = new SendDealerCouponRecordEntity();
                recordEntity.setId(UUIDUtil.getUUID());
                recordEntity.setEa(couponEntity.getEa());
                recordEntity.setCouponId(couponEntity.getId());
                recordEntity.setDealerCount(couponEntity.getDealerCount());
                recordEntity.setDownStreamEa(outerTenantId2EaMap.get(Long.valueOf(ternetId)));
                recordEntity.setDownStreamTenantId(ternetId);
                if (enterpriseData != null) {
                    recordEntity.setDownStreamName(enterpriseData.getEnterpriseData().getEnterpriseName());
                }
                if (ParticipateEnum.NO.getType() == couponEntity.getIsParticipate()) {
                    recordEntity.setAddCouponActivity(SendDownStatusEnum.SEND.getType());
                } else {
                    //需要下游自己参与的则状态为未参与
                    recordEntity.setAddCouponActivity(SendDownStatusEnum.UN_SEND.getType());
                }
                sendDealerCouponRecordDAO.saveDealerCouponRecord(recordEntity);
                //同时存入对象
                Map<String, Object> dataMap = couponDistributionObjManager.createDataMapByEntity(couponEntity, recordEntity,true);
                String couponDistributionId = couponDistributionObjManager.createCouponDistributionObj(couponEntity.getEa(), dataMap);
                Set<String> updateTenantIds = new HashSet<>(allUpStreamTenantIds);
                updateTenantIds.add(String.valueOf(tenantId));
                updateTenantIds.add(String.valueOf(ei));
                publicCouponManager.updateObjDataRange(couponEntity.getEa(), couponDistributionId, updateTenantIds, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName());
                //处理下发记录对象负责人字段
                Map<String, Integer> enterpriseRelationOwner = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(updateTenantIds), couponTopEa);
                String couponTopTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(couponTopEa));
                updateTenantIds.forEach(upDateTenantId -> {
                    if (Objects.equals(upDateTenantId,couponTopTenantId)) {
                        if (Objects.equals(couponEntity.getEa(),couponTopEa)) {
                            //如果是1端top企业,则负责人为优惠券创建人
                            publicCouponManager.updatePublicObjOwner(upDateTenantId,couponEntity.getOperator(),CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(),couponDistributionId);
                        } else {
                            Integer accountOwner = publicCouponManager.getAccountOwner(String.valueOf(tenantId), couponTopEa);
                            publicCouponManager.updatePublicObjOwner(upDateTenantId, accountOwner, CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), couponDistributionId);
                        }
                    } else {
                        if (enterpriseRelationOwner.containsKey(upDateTenantId)) {
                            publicCouponManager.updatePublicObjOwner(upDateTenantId, enterpriseRelationOwner.get(upDateTenantId), CrmObjectApiNameEnum.COUPON_DISTRIBUTION_OBJ.getName(), couponDistributionId);
                        }
                    }
                });
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        weChatCouponDAO.updateSendStatus(vo.getObjectId(),SendDownStatusEnum.SEND.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<String> receivePartnerCoupon(ReceivePartnerCouponVo vo) {
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (Objects.equals(wechatCouponEntity.getStatus(),CouponStatusEnum.INVALIDATION.getStatus())) {
            return Result.newError(SHErrorCode.COUPON_INVALIDATION_NOT_RECEIVE);
        }
        String couponInstanceId = null;
        String couponId = wechatCouponEntity.getStockId();
        String partnerId = vo.getEROuterTenantId();
        String accountId = null;
        vo.setEa(vo.getERUpstreamEa());
        vo.setCouponPlanId(wechatCouponEntity.getWechatCouponId());
        vo.setCouponStockId(wechatCouponEntity.getCouponId());
        //获取客户id
        int upEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        HeaderObj headerObj = HeaderObj.newInstance(upEi);
        UpstreamAndDownstreamOuterTenantIdOutArg outArg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        outArg.setDownstreamOuterTenantId(Long.valueOf(vo.getEROuterTenantId()));
        outArg.setUpstreamEa(vo.getERUpstreamEa());
        RestResult<String> mapperResult = enterpriseRelationService.getMapperObjectId(headerObj, outArg);
        if (mapperResult.isSuccess() && StringUtils.isNotBlank(mapperResult.getData())) {
            accountId = mapperResult.getData();
        }
        //判断是否在可领取范围内
        boolean flag = publicCouponManager.isInReceiveCouponRange(wechatCouponEntity.getEa(),wechatCouponEntity.getStoreScope(),wechatCouponEntity.getReceiveScope(),accountId);
        if(!flag) {
            return Result.newError(SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorCode(),I18NUtil.get(upEi,MarketingI18NKeyUtil.NOT_RECEIVE_STORE_RANGE,SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorMessage()));
        }
        String couponLockKey = COUPON_LOCK_PREFIX + vo.getERUpstreamEa() + "_"+ wechatCouponEntity.getStockId();
        boolean lockAcquired = wxCouponPayManager.retryGetCouponLock(couponLockKey, 3);
        if (!lockAcquired) {
            // TODO: 2023/11/16 修改文案
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        try {
            Integer directTenantId = null;
            //判断是否有可领优惠券
            if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                int count = memberCouponBindDAO.queryReceiveAndUseCoupon(vo.getERUpstreamEa(),wechatCouponEntity.getStockId());
                if (count >= wechatCouponEntity.getMaxCoupons()) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT);
                }
                //判断单人领取数量
                if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                    int receivePartnerCouponCount = memberCouponBindDAO.queryReceiveCouponCount(vo.getERUpstreamEa(), wechatCouponEntity.getStockId(), null, partnerId, accountId);
                    if (receivePartnerCouponCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                        return Result.newError(SHErrorCode.USER_RECEIVE_COUNT);
                    }
                }
            } else {
                //查询当前优惠券可见范围
                PublicDataRangeArg arg = new PublicDataRangeArg();
                arg.setIds(Lists.newArrayList(wechatCouponEntity.getCouponId()));
                arg.setEa(vo.getERUpstreamEa());
                arg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
                Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(arg);
                Set<String> range = publicDataRange.get(wechatCouponEntity.getCouponId());
                //获取发送范围
                CreateWxCouponVO.AccountVisibilityVO accountVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getAccountScope(), new TypeToken<CreateWxCouponVO.AccountVisibilityVO>() {}.getType());
                List<String> partnerNoticeTenantIds = publicCouponManager.getTenantIds(wechatCouponEntity.getEa(), accountVisibilityVO.getType(), accountVisibilityVO.getValue(),wechatCouponEntity.getSendScope());
                //根据当前上游企业,查询直属发送范围企业
                Set<Integer> upDirectTenantIds = publicCouponManager.getUpDirectTenantIds(Sets.newHashSet(),eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa()),partnerNoticeTenantIds);
                //查询是否存在可领取的直属下发上游
                directTenantId = publicCouponManager.getEnableReceiveTenantId(upDirectTenantIds,wechatCouponEntity.getStockId(),wechatCouponEntity.getDealerCount(),range);
                if (directTenantId == null || !range.contains(String.valueOf(directTenantId))) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT);
                }
                //判断是否达到单人领取数量限制
                if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                    int receiveCount = publicCouponManager.queryAccountIdCouponInstCount(upEi, wechatCouponEntity.getStockId(),accountId,partnerId);
                    if (receiveCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                        return Result.newError(SHErrorCode.USER_RECEIVE_COUNT);
                    }
                }
            }
            //进行存入实例对象
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResultResult = wxCouponPayManager.createCouponInstObj(vo, partnerId, accountId, null, couponId);
            if (!addResultResult.isSuccess() || addResultResult.getData() == null) {
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            couponInstanceId = addResultResult.getData().getObjectData().getId();
            //保存到本地库
            MemberCouponBindEntity memberCouponEntity = BeanUtil.copy(vo,MemberCouponBindEntity.class);
            memberCouponEntity.setEa(vo.getERUpstreamEa());
            memberCouponEntity.setCouponId(wechatCouponEntity.getStockId());
            memberCouponEntity.setId(UUIDUtil.getUUID());
            memberCouponEntity.setPartnerId(partnerId);
            memberCouponEntity.setAccountId(accountId);
            memberCouponEntity.setSendTime(DateUtil.format(DateUtil.now()));
            memberCouponEntity.setStatus(CouponInstStatusEnum.USE.getStatus());
            memberCouponEntity.setCouponInstanceId(addResultResult.getData().getObjectData().getId());
            memberCouponBindDAO.saveMemberCouponBind(memberCouponEntity);
            Integer finalDirectTenantId = directTenantId;
            String finalAccountId = accountId;
            ThreadPoolUtils.executeWithTraceContext(() -> {
                //更新优惠券实例对象的可见范围
                Set<String> upStreamTenantIds = new HashSet<>();
                if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                    Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(upEi);
                    upStreamTenantIds.addAll(allUpStreamTenantIds);
                    upStreamTenantIds.add(String.valueOf(upEi));
                } else {
                    //添加领取的经销商ea -> 直属下发的下发ea
                    upStreamTenantIds = publicCouponManager.getUpStreamTenantIds(Sets.newHashSet(String.valueOf(upEi)), upEi, finalDirectTenantId);
                    //添加所有创建优惠券企业的上游
                    publicCouponManager.getCreateCouponAllUpTenantIds(wechatCouponEntity.getEa(),upStreamTenantIds);
                }
                publicCouponManager.updateObjDataRange(vo.getEa(),addResultResult.getData().getObjectData().getId(),upStreamTenantIds,CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
                //处理优惠券实例负责人
                String couponTopEa = publicCouponManager.getCouponTopEa(wechatCouponEntity.getEa());
                String couponTopTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(couponTopEa));
                String wechatCouponTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(wechatCouponEntity.getEa()));
                Map<String, Integer> couponInstRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(upStreamTenantIds), couponTopEa);
                upStreamTenantIds.forEach(upStreamTenantId -> {
                    Integer owner = null;
                    if (Objects.equals(wechatCouponEntity.getEa(), couponTopEa)) {
                        if (Objects.equals(upStreamTenantId, couponTopTenantId)) {
                            owner = publicCouponManager.getAccountOwner(String.valueOf(upEi), couponTopEa);
                        } else {
                            if (couponInstRelationOwnerMap.containsKey(upStreamTenantId)) {
                                owner = couponInstRelationOwnerMap.get(upStreamTenantId);
                            }
                        }
                    } else {
                        if (Objects.equals(upStreamTenantId, couponTopTenantId)) {
                            //如果是1端企业,获取领券门店上游企业在1端的互联企业的客户负责人
                            owner = publicCouponManager.getAccountOwner(String.valueOf(upEi), couponTopEa);
                        } else if (Objects.equals(upStreamTenantId,wechatCouponTenantId)){
                            if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                                //如果当前租户是创建端,并且是上下游场景, 则负责人为当前领券门店客户负责人
                                ObjectData accountObject = crmV2Manager.getDetailIgnoreError(vo.getERUpstreamEa(), -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), finalAccountId);
                                if (accountObject != null && accountObject.containsKey("owner")) {
                                    owner = accountObject.getOwner();
                                }
                            } else {
                                //如果是发券端,获取领券门店上游企业在发券端的互联企业的客户负责人
                                owner = publicCouponManager.getAccountOwner(String.valueOf(upEi), wechatCouponEntity.getEa());
                            }
                        } else {
                            if (couponInstRelationOwnerMap.containsKey(upStreamTenantId)) {
                                owner = couponInstRelationOwnerMap.get(upStreamTenantId);
                            }
                        }
                    }
                    publicCouponManager.updatePublicObjOwner(upStreamTenantId,owner,CrmObjectApiNameEnum.COUPON_INST_OBJ.getName(),addResultResult.getData().getObjectData().getId());
                });
                //添加优惠券领取营销动态
                String marketingUserId = wxCouponPayManager.getMarketingUserIdByOutTenantId(vo.getEROuterTenantId(), wechatCouponEntity.getEa());
                wxCouponPayManager.syncRecordCouponMarketingActivityData(vo.getEa(),wechatCouponEntity.getId(),wechatCouponEntity.getMarketingEventId(),marketingUserId, MarketingUserActionType.RECEIVE_COUPON.getActionType());
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        } catch (Exception e) {
            //手动处理回滚
            log.warn("receivePartnerCoupon error",e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        } finally {
            redisManager.unLock(couponLockKey);
        }
        return Result.newSuccess(couponInstanceId);
    }

    @Override
    public Result<Void> participateCouponActivity(ParticipateActivityVo vo) {
        //1.修改经销商参与的状态  为已参与
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        //判断优惠券是否已终止
        if (Objects.equals(CouponStatusEnum.INVALIDATION.getStatus(),couponEntity.getStatus())) {
            return Result.newError(SHErrorCode.COUPON_INVALIDATION);
        }
        sendDealerCouponRecordDAO.updateAddStatus(vo.getObjectId(),vo.getEROuterTenantId());
        couponDistributionObjManager.updateCouponDistributionObjStatus(couponEntity.getEa(),couponEntity.getCouponId(),vo.getEROuterTenantId());
        //2.修改优惠券方案对象,市场活动对象,优惠券批次对象可见范围(上游1端,自己,同链路下所有下游)
        GetEaByOuterTenantIdArg outArg = new GetEaByOuterTenantIdArg();
        outArg.setOuterTenantId(Long.parseLong(vo.getEROuterTenantId()));
        int upStreamEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        String downStream = null;
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(upStreamEi);
        RestResult<String> outerTenantIdResult = fxiaokeAccountService.getEaByOuterTenantId(headerObj,outArg);
        if (outerTenantIdResult.isSuccess() && StringUtils.isNotBlank(outerTenantIdResult.getData())) {
            downStream = outerTenantIdResult.getData();
        }
        if (StringUtils.isEmpty(downStream)) {
            return Result.newError(SHErrorCode.NOT_FOUND_DOWN_TENANT);
        }
        int downTenantId = eieaConverter.enterpriseAccountToId(downStream);
        ThreadPoolUtils.executeWithTraceContext(() ->{
            String couponTopEa = publicCouponManager.getCouponTopEa(vo.getERUpstreamEa());
            //下游及其所有的经销商
            Set<String> downTenantIds = publicCouponManager.getDownTenantIds(Sets.newHashSet(String.valueOf(downTenantId)), String.valueOf(downTenantId));
            //发券端添加进去
            downTenantIds.add(String.valueOf(eieaConverter.enterpriseAccountToId(couponEntity.getEa())));
            //同时将发券端的所有上游都添加进去
            Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(eieaConverter.enterpriseAccountToId(couponEntity.getEa()));
            downTenantIds.addAll(allUpStreamTenantIds);
            //更新优惠券批次对象,优惠券方案对象,市场活动对象可见范围
            PublicDataRangeArg arg = new PublicDataRangeArg();
            arg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
            arg.setEa(couponEntity.getEa());
            arg.setIds(Lists.newArrayList(couponEntity.getCouponId()));
            Map<String, Set<String>> publicDataRangeMap = publicMetadataManager.findPublicDataRange(arg);
            Set<String> dataRange = publicDataRangeMap.get(couponEntity.getCouponId());
            dataRange.addAll(downTenantIds);
            publicCouponManager.updateObjDataRange(couponEntity.getEa(),couponEntity.getCouponId(),dataRange,CrmObjectApiNameEnum.COUPON_OBJ.getName());
            String couponTopTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(couponTopEa));
            //修改优惠券负责人
            Map<String, Integer> couponRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(dataRange), couponTopEa);
            int couponTenantId = eieaConverter.enterpriseAccountToId(couponEntity.getEa());
            dataRange.forEach(rangeTenantId -> {
                Integer owner = null;
                if (!Objects.equals(rangeTenantId, String.valueOf(couponTenantId))) {
                    if (Objects.equals(rangeTenantId, couponTopTenantId)) {
                        if (!Objects.equals(couponEntity.getEa(), couponTopEa)) {
                            owner = publicCouponManager.getAccountOwner(String.valueOf(couponTenantId), couponTopEa);
                        }
                    } else {
                        if (couponRelationOwnerMap.containsKey(rangeTenantId)) {
                            owner = couponRelationOwnerMap.get(rangeTenantId);
                        }
                    }
                    publicCouponManager.updatePublicObjOwner(rangeTenantId,owner,CrmObjectApiNameEnum.COUPON_OBJ.getName(),couponEntity.getCouponId());
                }
            });
            //修改优惠券方案的可见范围
            Set<String> rangeSet = new HashSet<>();
            PublicDataRangeArg rangeArg = new PublicDataRangeArg();
            rangeArg.setEa(couponEntity.getEa());
            rangeArg.setIds(Lists.newArrayList(couponEntity.getWechatCouponId()));
            rangeArg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
            Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(rangeArg);
            if (publicDataRange.containsKey(couponEntity.getWechatCouponId())) {
                rangeSet = publicDataRange.get(couponEntity.getWechatCouponId());
            }
            rangeSet.addAll(downTenantIds);
            publicCouponManager.updateObjDataRange(couponEntity.getEa(),couponEntity.getWechatCouponId(),rangeSet,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
            //修改优惠券方案负责人
            String createEnterprise;
            ObjectData couponPlanObj = crmV2Manager.getDetailIgnoreError(couponEntity.getEa(), -10000, CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName(), couponEntity.getWechatCouponId());
            if (couponPlanObj != null && couponPlanObj.containsKey("create_enterprise")) {
                String createEnterpriseStr = couponPlanObj.getString("create_enterprise");
                createEnterprise = createEnterpriseStr.substring(1, createEnterpriseStr.length() - 1);
            } else {
                createEnterprise = null;
            }
            Map<String, Integer> couponPlanRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(rangeSet), couponTopEa);
            rangeSet.forEach(rangePlanTenantId -> {
                //如果是方案创建企业,则不进行更新
                if (StringUtils.isNotBlank(createEnterprise) && Objects.equals(rangePlanTenantId, createEnterprise)) {
                    return;
                }
                //判断上游企业是否为1端top企业
                Integer owner = null;
                if (Objects.equals(rangePlanTenantId, couponTopTenantId)) {
                    if (!Objects.equals(couponEntity.getEa(), couponTopEa)) {
                        owner = publicCouponManager.getAccountOwner(String.valueOf(eieaConverter.enterpriseAccountToId(couponEntity.getEa())), couponTopEa);
                    }
                } else {
                    if (couponPlanRelationOwnerMap.containsKey(rangePlanTenantId)) {
                        owner = couponPlanRelationOwnerMap.get(rangePlanTenantId);
                    }
                }
                publicCouponManager.updatePublicObjOwner(rangePlanTenantId,owner,CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName(),couponEntity.getWechatCouponId());
            });

            //publicCouponManager.updateObjDataRange(couponEntity.getEa(),couponEntity.getMarketingEventId(),downTenantIds,CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            int totalCoupons = couponEntity.getDealerCount() * (publicCouponManager.queryParticipateTenantCount(couponEntity.getEa(), couponEntity.getCouponId()));
            //更新1端 优惠券发放总数
            ObjectData objectData = new ObjectData();
            objectData.put("_id",couponEntity.getCouponId());
            objectData.put("max_coupons",totalCoupons);
            IncrementUpdateArg editArg = new IncrementUpdateArg();
            editArg.setData(objectData);
            metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(couponEntity.getEa()),-10000),CrmObjectApiNameEnum.COUPON_OBJ.getName(),editArg);
            //更新N端私有数据
            ObjectData participateObjectData = new ObjectData();
            participateObjectData.put("_id",couponEntity.getCouponId());
            participateObjectData.put("max_coupons",couponEntity.getDealerCount());
            IncrementUpdateArg participateEditArg = new IncrementUpdateArg();
            participateEditArg.setData(participateObjectData);
            metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(downTenantId, -10000), CrmObjectApiNameEnum.COUPON_OBJ.getName(), participateEditArg);
            //更新所有发券端上游
            allUpStreamTenantIds.forEach(upStreamTenantId -> {
                ObjectData upStreamObjectData = new ObjectData();
                upStreamObjectData.put("_id",couponEntity.getCouponId());
                upStreamObjectData.put("max_coupons",totalCoupons);
                IncrementUpdateArg upStreamEditArg = new IncrementUpdateArg();
                upStreamEditArg.setData(upStreamObjectData);
                metadataActionService.incrementUpdate(com.fxiaoke.crmrestapi.common.data.HeaderObj.newInstance(Integer.parseInt(upStreamTenantId), -10000), CrmObjectApiNameEnum.COUPON_OBJ.getName(), upStreamEditArg);
            });
            //处理营销动态
            String marketingUserId = wxCouponPayManager.getMarketingUserIdByOutTenantId(vo.getEROuterTenantId(), couponEntity.getEa());
            wxCouponPayManager.syncRecordCouponMarketingActivityData(couponEntity.getEa(),couponEntity.getId(),couponEntity.getMarketingEventId(),marketingUserId, MarketingUserActionType.ATTEND_COUPON_ACTIVITY.getActionType());
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<PartnerCouponDetailStatisticResult>> statisticDealerCoupon(String ea, PartnerCouponDetailStatisticVo vo) {
        PageResult<PartnerCouponDetailStatisticResult> pageResult = new PageResult<>();
        List<PartnerCouponDetailStatisticResult> partnerCouponResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        //所有直属下游
        TenantIdCascadeArg arg = new TenantIdCascadeArg();
        arg.setTenantId(tenantId);
        RestResult<Set<Integer>> allDownstreamTenantIdsResult = enterpriseRelationService.listAllDownstreamTenantIds(HeaderObj.newInstance(tenantId), arg);
        if (!allDownstreamTenantIdsResult.isSuccess() || CollectionUtils.isEmpty(allDownstreamTenantIdsResult.getData())) {
            return Result.newSuccess(pageResult);
        }
        Set<Integer> downstreamTenantIdsResultData = allDownstreamTenantIdsResult.getData();
        //可见范围
        PublicDataRangeArg rangeArg = new PublicDataRangeArg();
        rangeArg.setEa(ea);
        rangeArg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        rangeArg.setIds(Lists.newArrayList(couponEntity.getCouponId()));
        Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(rangeArg);
        Set<String> rangeData = publicDataRange.get(couponEntity.getCouponId());
        Set<Integer> rangeTenantIds = new HashSet<>();
        rangeData.forEach(range ->{
            rangeTenantIds.add(Integer.parseInt(range));
        });
        //获取下游
        Set<Integer> downTenantIds = downstreamTenantIdsResultData.stream().filter(rangeTenantIds::contains).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(downTenantIds)) {
            return Result.newSuccess(pageResult);
        }
        //批量获取企业名称
        Map<Integer, String> easMap = eieaConverter.enterpriseIdToAccount(downTenantIds);
        Set<String> eas = Sets.newHashSet(easMap.values());
        Map<String, String> companyNameMap = publicCouponManager.queryBelongCompanyName(eas);
        //根据制券企业获取优惠券实例
        PaasQueryUserCouponArg couponArg = new PaasQueryUserCouponArg();
        couponArg.setCouponId(couponEntity.getStockId());
        List<ObjectData> objectDataList = publicCouponManager.queryAllPublicCouponInst(couponEntity.getEa(), couponArg);
        //获取下游ea
        Map<Integer, String> eaMap = eieaConverter.enterpriseIdToAccount(downTenantIds);
        downTenantIds.forEach(downTenantId ->{
            String downEa = eaMap.get(downTenantId);
            PartnerCouponDetailStatisticResult partnerCouponDetailStatisticResult = new PartnerCouponDetailStatisticResult();
            int receiveCount = 0;
            int useCount = 0;
            for (ObjectData objectData : objectDataList) {
                List<String> dTenantId = (List<String>) objectData.get("d_tenant_id");
                if (dTenantId.contains("-99999") || dTenantId.contains(String.valueOf(downTenantId))) {
                    receiveCount += 1;
                    if (CouponUseStautsEnum.USED.getStatus().equals(objectData.get("use_status"))) {
                        useCount += 1;
                    }
                }
            }
            partnerCouponDetailStatisticResult.setReceiveCount(receiveCount);
            partnerCouponDetailStatisticResult.setUseCount(useCount);
            if (companyNameMap.containsKey(downEa)) {
                partnerCouponDetailStatisticResult.setBusinessName(companyNameMap.get(downEa));
            }
            partnerCouponDetailStatisticResult.setEa(downEa);
            partnerCouponResultList.add(partnerCouponDetailStatisticResult);
        });
        //手动分页
        pageResult.setTotalCount(partnerCouponResultList.size());
        PageUtil<PartnerCouponDetailStatisticResult> pageUtil = new PageUtil<>(partnerCouponResultList,vo.getPageSize());
        pageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<CouponActivityResult>> queryCouponActivityList(CouponActivityVo vo) {
        PageResult<CouponActivityResult> pageResult = new PageResult<>();
        List<CouponActivityResult> couponActivityResults = Lists.newArrayList();
        pageResult.setResult(couponActivityResults);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        List<SendDealerCouponRecordEntity> sendDealerCouponRecordEntities = sendDealerCouponRecordDAO.queryListByDownStreamTenantId(vo.getEROuterTenantId(),page);
        if (CollectionUtils.isEmpty(sendDealerCouponRecordEntities)) {
            return Result.newSuccess(pageResult);
        }
        List<String> couponIds = sendDealerCouponRecordEntities.stream().map(SendDealerCouponRecordEntity::getCouponId).collect(Collectors.toList());
        List<WechatCouponEntity> wechatCouponEntityList = weChatCouponDAO.getByIds(couponIds);
        Set<String> marketingEventIds = wechatCouponEntityList.stream().map(WechatCouponEntity::getMarketingEventId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        List<String> couponPlanIds = wechatCouponEntityList.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String, WechatCouponEntity> couponEntityMap = wechatCouponEntityList.stream().collect(Collectors.toMap(WechatCouponEntity::getId, o -> o));
        Map<String, ObjectData> marketingEventObjMap = crmV2Manager.getObjectDataMapByIds(vo.getERUpstreamEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, Lists.newArrayList("name"));
        List<CouponPlanInfoResult> couponPlanInfoResults = wxCouponPayManager.listCouponPlanInfo(vo.getERUpstreamEa(), -10000, couponPlanIds);
        Map<String, CouponPlanInfoResult> couponPlanInfoResultMap = couponPlanInfoResults.stream().collect(Collectors.toMap(CouponPlanInfoResult::getCouponPlanId, o -> o));
        for (SendDealerCouponRecordEntity sendDealerCouponRecordEntity : sendDealerCouponRecordEntities) {
            if (!couponEntityMap.containsKey(sendDealerCouponRecordEntity.getCouponId())) {
                continue;
            }
            CouponActivityResult couponActivityResult = new CouponActivityResult();
            WechatCouponEntity couponEntity = couponEntityMap.get(sendDealerCouponRecordEntity.getCouponId());
            if (couponPlanInfoResultMap.containsKey(couponEntity.getWechatCouponId())) {
                couponActivityResult = BeanUtil.copy(couponPlanInfoResultMap.get(couponEntity.getWechatCouponId()),couponActivityResult.getClass());
            }
            couponActivityResult.setStockName(couponEntity.getStockName());
            couponActivityResult.setObjectId(sendDealerCouponRecordEntity.getCouponId());
            couponActivityResult.setAddCouponActivity(sendDealerCouponRecordEntity.getAddCouponActivity());
            if (marketingEventObjMap.containsKey(couponEntity.getMarketingEventId())) {
                couponActivityResult.setMarketingEventName(marketingEventObjMap.get(couponEntity.getMarketingEventId()).getName());
            }
            couponActivityResults.add(couponActivityResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(couponActivityResults);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<CouponActivityResult> queryCouponActivityDetail(CouponDetailActivityVo vo) {
        SendDealerCouponRecordEntity recordEntity = sendDealerCouponRecordDAO.queryRecord(vo.getObjectId(),vo.getEROuterTenantId());
        CouponActivityResult couponActivityResult = new CouponActivityResult();
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        GetCouponPlanInfoVo planVo = new GetCouponPlanInfoVo();
        planVo.setCouponPlanId(couponEntity.getWechatCouponId());
        Result<CouponPlanInfoResult> couponPlanInfoResultResult = this.queryCouponPlanInfo(planVo, couponEntity.getEa());
        if (couponPlanInfoResultResult.isSuccess() && null != couponPlanInfoResultResult.getData()) {
            couponActivityResult = BeanUtil.copy(couponPlanInfoResultResult.getData(),couponActivityResult.getClass());
        }
        couponActivityResult.setObjectId(recordEntity.getCouponId());
        couponActivityResult.setAddCouponActivity(recordEntity.getAddCouponActivity());
        couponActivityResult.setStockName(couponEntity.getStockName());
        if (StringUtils.isNotBlank(couponEntity.getMarketingEventId())) {
            ObjectData objectData = crmV2Manager.getObjectData(couponEntity.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), couponEntity.getMarketingEventId());
            if (objectData != null) {
                couponActivityResult.setMarketingEventName(objectData.getName());
            }
        }
        return Result.newSuccess(couponActivityResult);
    }

    @Override
    public Result<PageResult<PartnerReceiveResult>> statisticStoreCoupon(String ea, PartnerCouponDetailStatisticVo vo) {
        PageResult<PartnerReceiveResult> pageResult = new PageResult<>();
        List<PartnerReceiveResult> partnerCouponResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        PaasQueryUserCouponArg arg = new PaasQueryUserCouponArg();
        arg.setCouponId(wechatCouponEntity.getStockId());
        arg.setPageNumber(vo.getPageNum());
        arg.setPageSize(vo.getPageSize());
        InnerPage<ObjectData> instObjListPage = publicCouponManager.queryPageCouponInst(ea,-10000,arg);
        if (instObjListPage == null || CollectionUtils.isEmpty(instObjListPage.getDataList())) {
            pageResult.setResult(partnerCouponResultList);
            return Result.newSuccess(pageResult);
        }
        List<ObjectData> instObjList = instObjListPage.getDataList();
        List<String> partnerIds = instObjList.stream().map(e->e.getString("partner_id_backup")).collect(Collectors.toList());
        Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
        instObjList.forEach(instObj -> {
            PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
            partnerReceiveResult.setReceiveTime(doubleToDateString(instObj.getDouble("send_time")));
            partnerReceiveResult.setDepositObject(true);
            if (Objects.equals(instObj.getString("use_status"),CouponUseStautsEnum.UNUSED.getStatus())) {
                partnerReceiveResult.setStatus(CouponInstStatusEnum.USE.getStatus());
            } else if (Objects.equals(instObj.getString("use_status"),CouponUseStautsEnum.USED.getStatus())) {
                partnerReceiveResult.setStatus(CouponInstStatusEnum.CONFIRM.getStatus());
            }
            partnerReceiveResult.setOutUid(instObj.getString("account_id"));
            partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(instObj.getString("partner_id_backup")));
            partnerCouponResultList.add(partnerReceiveResult);
        });
        pageResult.setTotalCount(instObjListPage.getTotalCount());
        pageResult.setResult(partnerCouponResultList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<List<CouponPlanInfoResult>> queryCouponPlanList(QueryCouponPlanVO vo) {
        return null;
    }

    @Override
    public Result<PageResult<CouponResult>> queryFxCouponList(QueryFxCouponVO vo) {
        PageResult<CouponResult> couponPageResult = new PageResult<>();
        List<CouponResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        PaasQueryCouponArg couponArg = new PaasQueryCouponArg();
        couponArg.setPageSize(vo.getPageSize());
        couponArg.setPageNumber(vo.getPageNum());
        InnerPage<ObjectData> dataPage = publicCouponManager.listPageCoupon(vo.getEa(), -10000, couponArg);
        if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())) {
            return Result.newSuccess(couponPageResult);
        }
        List<ObjectData> couponDataList = dataPage.getDataList();
        List<String> couponIds = couponDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryCouponIds(couponIds);
        if (CollectionUtils.isEmpty(couponEntities)) {
            return  Result.newSuccess(couponPageResult);
        }
        //批量获取优惠券方案
        List<String> couponPlanIds = couponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Set<String> eas = couponEntities.stream().map(WechatCouponEntity::getEa).collect(Collectors.toSet());
        Set<String> marketingEventIds = couponEntities.stream().map(WechatCouponEntity::getMarketingEventId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getEa(),couponPlanIds);
        //批量获取优惠券使用数量
        List<String> stockIds = couponEntities.stream().map(WechatCouponEntity::getStockId).collect(Collectors.toList());
        //获取市场活动名称
        Map<String, ObjectData> marketingEventObjMap = crmV2Manager.getObjectDataMapByIds(vo.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, Lists.newArrayList("name"));
        //获取所属企业名称
        Map<String,String> companyNameMap = publicCouponManager.queryBelongCompanyName(eas);
        Map<String, Long> useCountMap = wxCouponPayManager.getPartnerCouponCount(vo.getEa(), stockIds, CouponUseStautsEnum.USED.getStatus());
        Map<String, Long> receiveCountMap = wxCouponPayManager.getPartnerCouponCount(vo.getEa(), stockIds, null);
        couponEntities.forEach(wechatCouponEntity -> {
            CouponResult couponResult = new CouponResult();
            //如果是分享券，查询分享券详情
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                couponResult = BeanUtil.copy(wechatCouponEntity, CouponResult.class);
                couponResult.setObjectId(wechatCouponEntity.getId());
                copyPlanInfo(couponPlanInfoResult,couponResult);
            }
            if (StringUtils.isNotBlank(wechatCouponEntity.getMarketingEventId()) && marketingEventObjMap.containsKey(wechatCouponEntity.getMarketingEventId())) {
                couponResult.setMarketingEventName(marketingEventObjMap.get(wechatCouponEntity.getMarketingEventId()).getName());
            }
            if (companyNameMap.containsKey(wechatCouponEntity.getEa())) {
                couponResult.setBelongEnterpriseName(companyNameMap.get(wechatCouponEntity.getEa()));
            }
            couponResult.setReceiveCount((int) (receiveCountMap.get(wechatCouponEntity.getStockId()) == null ? 0L : receiveCountMap.get(wechatCouponEntity.getStockId())));
            couponResult.setUsedCont((int) (useCountMap.get(wechatCouponEntity.getStockId()) == null ? 0L : useCountMap.get(wechatCouponEntity.getStockId())));
            if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                int remainCount = totalCouponCount(wechatCouponEntity.getDealerCount(),wechatCouponEntity.getId(),wechatCouponEntity.getEa()) - couponResult.getReceiveCount();
                couponResult.setRemainCount(remainCount);
            } else {
                int remainCount = wechatCouponEntity.getMaxCoupons() - couponResult.getReceiveCount();
                couponResult.setRemainCount(remainCount);
            }
            result.add(couponResult);
        });
        couponPageResult.setTotalCount(dataPage.getTotalCount());
        couponPageResult.setResult(result);
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<Boolean> checkCustomerPendingCoupon(CheckCustomerPendingCouponVO vo) {
        int upEi = eieaConverter.enterpriseAccountToId(vo.getEa());
        List<ObjectData> couponObjectDataList = publicCouponManager.queryPublicCouponList(vo.getEa());
        if (CollectionUtils.isEmpty(couponObjectDataList)) {
            return Result.newSuccess(false);
        }
        List<String> couponIds = couponObjectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<WechatCouponEntity> wechatCouponList = weChatCouponDAO.queryCouponIds(couponIds);
        if (CollectionUtils.isEmpty(wechatCouponList)) {
            return Result.newSuccess(false);
        }
        List<WechatCouponEntity> wechatCouponEntityList = Lists.newArrayList();
        wechatCouponList.forEach(entity -> {
            boolean flag = true;
            boolean inReceiveRange = publicCouponManager.isInReceiveCouponRange(entity.getEa(),entity.getStoreScope(),entity.getReceiveScope(),vo.getCustomerId());
            //查询制券企业所有上游
            int tenantId = eieaConverter.enterpriseAccountToId(entity.getEa());
            Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
            //多级下游
            if (Objects.equals(entity.getScene(),SceneEnum.DEALER.getType())) {
                allUpStreamTenantIds.add(String.valueOf(tenantId));
                if (allUpStreamTenantIds.contains(String.valueOf(upEi))) {
                    flag = false;
                }
            } else if (Objects.equals(entity.getScene(),SceneEnum.PARTNER.getType())) {
                if (allUpStreamTenantIds.contains(String.valueOf(upEi))) {
                    flag = false;
                }
            }
            if (inReceiveRange && flag) {
                wechatCouponEntityList.add(entity);
            }
        });
        if (CollectionUtils.isEmpty(wechatCouponEntityList)) {
            return Result.newSuccess(false);
        }
        //企业所创建的所有优惠券
        Set<String> couponSet = new HashSet<>();
        List<String> couponPlanIds = wechatCouponEntityList.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getEa(),couponPlanIds);
        for (WechatCouponEntity wechatCouponEntity : wechatCouponEntityList) {
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                if (couponPlanInfoResult.getEndDate() < DateUtil.getDayStartTime(new Date()) || couponPlanInfoResult.getStartDate() > DateUtil.getDayStartTime(new Date())) {
                    continue;
                }
                couponSet.add(wechatCouponEntity.getStockId());
            }
        }
        if (CollectionUtils.isEmpty(couponSet)) {
            return Result.newSuccess(false);
        }
        Map<String, WechatCouponEntity> couponEntityMap = wechatCouponEntityList.stream().collect(Collectors.toMap(WechatCouponEntity::getStockId, Function.identity(), (v1, v2) -> v2));
        List<ObjectData> instList = publicCouponManager.queryPublicCouponInstList(vo.getEa(),vo.getCustomerId());
        if (CollectionUtils.isEmpty(instList)) {
            return Result.newSuccess(true);
        }
        //已领取的所有优惠券id
        Set<String> receiveCouponSet = instList.stream().filter(k -> StringUtils.isNotBlank(k.getString("coupon_id"))).map(e->e.getString("coupon_id")).collect(Collectors.toSet());
        //处理已领取的券统计
        Map<String, Long> collectReceiveMap = instList.stream().filter(k -> StringUtils.isNotBlank(k.getString("coupon_id"))).collect(Collectors.groupingBy(e->e.getString("coupon_id"), Collectors.counting()));
        //1.先判断客户是否已领取了当前所有可以领取的券
        boolean flagAll = receiveCouponSet.containsAll(couponSet);
        //2.如果是领取了所有的券,先赋值没有待领取的
        boolean needPending = !flagAll;
        //3.判断是否存在券没有达到领取限制,只要存在有,则直接赋值有待领取的券
        if (flagAll) {
            for (ObjectData objectData : instList) {
                String stockId = objectData.getString("coupon_id");
                WechatCouponEntity wechatCouponEntity = couponEntityMap.get(stockId);
                Long receive = collectReceiveMap.get(stockId);
                if (wechatCouponEntity != null && receive < wechatCouponEntity.getMaxCouponsPerUser()){
                    needPending = true;
                    break;
                }
            }
        }
        return Result.newSuccess(needPending);
    }

    @Override
    public Result<PageResult<QueryCustomerCouponResult>> queryCustomerPendingCouponList(QueryCustomerPendingCouponVO vo) {
        int upEi = eieaConverter.enterpriseAccountToId(vo.getEa());
        PageResult<QueryCustomerCouponResult> couponPageResult = new PageResult<>();
        List<QueryCustomerCouponResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        List<ObjectData> couponObjectDataList = publicCouponManager.queryPublicCouponList(vo.getEa());
        if (CollectionUtils.isEmpty(couponObjectDataList)) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        List<String> objectIds = couponObjectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        List<WechatCouponEntity> wechatCouponList = weChatCouponDAO.queryCouponIds(objectIds);
        if (CollectionUtils.isEmpty(wechatCouponList)) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        List<WechatCouponEntity> wechatCouponEntityList = Lists.newArrayList();
        wechatCouponList.forEach(entity -> {
            boolean flag = true;
            boolean inReceiveRange = publicCouponManager.isInReceiveCouponRange(entity.getEa(),entity.getStoreScope(),entity.getReceiveScope(),vo.getCustomerId());
            //查询制券企业所有上游
            int tenantId = eieaConverter.enterpriseAccountToId(entity.getEa());
            Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(tenantId);
            //多级下游
            if (Objects.equals(entity.getScene(),SceneEnum.DEALER.getType())) {
                allUpStreamTenantIds.add(String.valueOf(tenantId));
                if (allUpStreamTenantIds.contains(String.valueOf(upEi))) {
                    flag = false;
                }
            } else if (Objects.equals(entity.getScene(),SceneEnum.PARTNER.getType())) {
                //上下游场景
                if (allUpStreamTenantIds.contains(String.valueOf(upEi))) {
                    flag = false;
                }
            }
            if (inReceiveRange && flag) {
                wechatCouponEntityList.add(entity);
            }
        });
        if (CollectionUtils.isEmpty(wechatCouponEntityList)) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        //企业所创建的所有优惠券
        Set<String> couponSet = wechatCouponEntityList.stream().map(WechatCouponEntity::getStockId).collect(Collectors.toSet());
        List<String> couponIds = new ArrayList<>(couponSet);
        Map<String, WechatCouponEntity> couponEntityMap = wechatCouponEntityList.stream().collect(Collectors.toMap(WechatCouponEntity::getStockId, Function.identity(), (v1, v2) -> v2));
        List<ObjectData> instList = publicCouponManager.queryPublicCouponInstList(vo.getEa(),vo.getCustomerId());
        if (CollectionUtils.isNotEmpty(instList)) {
            //处理已领取的券统计
            Map<String, Long> collectReceiveMap = instList.stream().filter(k -> StringUtils.isNotBlank(k.getString("coupon_id"))).collect(Collectors.groupingBy(e->e.getString("coupon_id"), Collectors.counting()));
            //已达到领取限制的所有优惠券stockId
            Set<String> receiveCouponSet = new HashSet<>();
            //判断是否达到领取限制
            for (ObjectData objectData : instList) {
                String stockId = objectData.getString("coupon_id");
                if (StringUtils.isNotBlank(stockId)) {
                    WechatCouponEntity wechatCouponEntity = couponEntityMap.get(stockId);
                    Long receive = collectReceiveMap.get(stockId);
                    if (wechatCouponEntity != null && receive >= wechatCouponEntity.getMaxCouponsPerUser()){
                        receiveCouponSet.add(stockId);
                    }   
                }
            }
            //未领取的券stockId
            couponIds.removeAll(receiveCouponSet);
        }
        if (CollectionUtils.isEmpty(couponIds)){
            log.info("queryCustomerPendingCouponList couponIds is null");
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        List<WechatCouponEntity> wechatCouponEntities = weChatCouponDAO.queryStockIds(couponIds);
        List<String> couponPlanIds = wechatCouponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getEa(),couponPlanIds);
        for (WechatCouponEntity wechatCouponEntity : wechatCouponEntities) {
            //如果是分享券，查询分享券详情
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                if (couponPlanInfoResult.getEndDate() < DateUtil.getDayStartTime(new Date()) || couponPlanInfoResult.getStartDate() > DateUtil.getDayStartTime(new Date())) {
                    continue;
                }
                QueryCustomerCouponResult couponResult = BeanUtil.copy(couponPlanInfoResult, QueryCustomerCouponResult.class);
                couponResult.setObjectDataId(wechatCouponEntity.getCouponId());
                result.add(couponResult);
            }
        }
        couponPageResult.setTotalCount(result.size());
        // 手动分页
        PageUtil<QueryCustomerCouponResult> pageUtil = new PageUtil<>(result, vo.getPageSize());
        if (CollectionUtils.isEmpty(pageUtil.getData())) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        couponPageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<String> receiveSingleCoupon(ReceiveSingleCouponVO vo) {
        int ei = eieaConverter.enterpriseAccountToId(vo.getEa());
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryCouponByObjectId(vo.getObjectDataId());
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA.getErrorCode(), I18NUtil.get(ei, MarketingI18NKeyUtil.NO_DATA,SHErrorCode.NO_DATA.getErrorMessage()));
        }
        if (Objects.equals(wechatCouponEntity.getStatus(),CouponStatusEnum.INVALIDATION.getStatus())) {
            return Result.newError(SHErrorCode.COUPON_INVALIDATION_NOT_RECEIVE);
        }
        //判断是否在可领取范围内
        boolean flag = publicCouponManager.isInReceiveCouponRange(wechatCouponEntity.getEa(),wechatCouponEntity.getStoreScope(),wechatCouponEntity.getReceiveScope(),vo.getCustomerId());
        if(!flag) {
            return Result.newError(SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.NOT_RECEIVE_STORE_RANGE,SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorMessage()));
        }
        String couponLockKey = COUPON_LOCK_PREFIX + vo.getEa() + "_"+ wechatCouponEntity.getStockId();
        boolean lockAcquired = wxCouponPayManager.retryGetCouponLock(couponLockKey, 3);
        if (!lockAcquired) {
            return Result.newError(SHErrorCode.SERVER_BUSY.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.SERVER_BUSY,SHErrorCode.SERVER_BUSY.getErrorMessage()));
        }
        try {
            Integer directTenantId = null;
            //判断是否有可领优惠券
            if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                int count = memberCouponBindDAO.queryReceiveAndUseCoupon(vo.getEa(),wechatCouponEntity.getStockId());
                if (count >= wechatCouponEntity.getMaxCoupons()) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.COUPON_NOT_COUNT,SHErrorCode.COUPON_NOT_COUNT.getErrorMessage()));
                }
                //判断单人领取数量
                if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                    int receivePartnerCouponCount = memberCouponBindDAO.queryReceiveCouponCount(vo.getEa(), wechatCouponEntity.getStockId(), null, null, vo.getCustomerId());
                    if (receivePartnerCouponCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                        return Result.newError(SHErrorCode.USER_RECEIVE_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.USER_RECEIVE_COUNT,SHErrorCode.USER_RECEIVE_COUNT.getErrorMessage()));
                    }
                }
            } else {
                //查询当前优惠券可见范围
                PublicDataRangeArg arg = new PublicDataRangeArg();
                arg.setIds(Lists.newArrayList(wechatCouponEntity.getCouponId()));
                arg.setEa(vo.getEa());
                arg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
                Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(arg);
                Set<String> range = publicDataRange.get(wechatCouponEntity.getCouponId());
                //获取发送范围
                CreateWxCouponVO.AccountVisibilityVO accountVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getAccountScope(), new TypeToken<CreateWxCouponVO.AccountVisibilityVO>() {}.getType());
                List<String> partnerNoticeTenantIds = publicCouponManager.getTenantIds(wechatCouponEntity.getEa(), accountVisibilityVO.getType(), accountVisibilityVO.getValue(),wechatCouponEntity.getSendScope());
                //根据当前上游企业,查询直属发送范围企业
                Set<Integer> upDirectTenantIds = publicCouponManager.getUpDirectTenantIds(Sets.newHashSet(),eieaConverter.enterpriseAccountToId(vo.getEa()),partnerNoticeTenantIds);
                //查询是否存在可领取的直属下发上游
                directTenantId = publicCouponManager.getEnableReceiveTenantId(upDirectTenantIds,wechatCouponEntity.getStockId(),wechatCouponEntity.getDealerCount(),range);
                if (directTenantId == null || !range.contains(String.valueOf(directTenantId))) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.COUPON_NOT_COUNT,SHErrorCode.COUPON_NOT_COUNT.getErrorMessage()));
                }
                //判断是否达到单人领取数量限制
                if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                    int receiveCount = publicCouponManager.queryAccountIdCouponInstCount(ei, wechatCouponEntity.getCouponId(), vo.getCustomerId(), null);
                    if (receiveCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                        return Result.newError(SHErrorCode.USER_RECEIVE_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.USER_RECEIVE_COUNT,SHErrorCode.USER_RECEIVE_COUNT.getErrorMessage()));
                    }
                }
            }
            String couponId = wechatCouponEntity.getStockId();
            ReceivePartnerCouponVo receiveVo = new ReceivePartnerCouponVo();
            receiveVo.setEa(vo.getEa());
            receiveVo.setCouponPlanId(wechatCouponEntity.getWechatCouponId());
            receiveVo.setCouponStockId(wechatCouponEntity.getCouponId());
            WxCouponPayManager.DownTenantResult downTenantResult = wxCouponPayManager.getOutTenantIdByAccountId(vo.getCustomerId(), vo.getEa());
            String partnerId = downTenantResult.getOutTenantId();
            //进行存入实例对象
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResultResult = wxCouponPayManager.createCouponInstObj(receiveVo, partnerId, vo.getCustomerId(), null, couponId);
            if (!addResultResult.isSuccess() || addResultResult.getData() == null) {
                return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.SYSTEM_ERROR,SHErrorCode.SYSTEM_ERROR.getErrorMessage()));
            }
            //保存到本地库
            MemberCouponBindEntity memberCouponEntity = BeanUtil.copy(vo,MemberCouponBindEntity.class);
            memberCouponEntity.setEa(vo.getEa());
            memberCouponEntity.setCouponId(wechatCouponEntity.getStockId());
            memberCouponEntity.setId(UUIDUtil.getUUID());
            memberCouponEntity.setPartnerId(partnerId);
            memberCouponEntity.setAccountId(vo.getCustomerId());
            memberCouponEntity.setSendTime(DateUtil.format(DateUtil.now()));
            memberCouponEntity.setStatus(CouponInstStatusEnum.USE.getStatus());
            memberCouponEntity.setCouponInstanceId(addResultResult.getData().getObjectData().getId());
            memberCouponBindDAO.saveMemberCouponBind(memberCouponEntity);
            Integer finalDirectTenantId = directTenantId;
            ThreadPoolUtils.executeWithTraceContext(() -> {
                //修改优惠券实例对象的可见范围
                Set<String> upStreamTenantIds = new HashSet<>();
                if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                    Set<String> allUpStreamTenantIds = publicCouponManager.getAllUpStreamTenantIds(ei);
                    upStreamTenantIds.addAll(allUpStreamTenantIds);
                    upStreamTenantIds.add(String.valueOf(ei));
                } else {
                    //添加领取的经销商ea -> 直属下发的下发ea
                    upStreamTenantIds = publicCouponManager.getUpStreamTenantIds(Sets.newHashSet(String.valueOf(ei)), ei, finalDirectTenantId);
                    //添加所有创建优惠券企业的上游
                    publicCouponManager.getCreateCouponAllUpTenantIds(wechatCouponEntity.getEa(),upStreamTenantIds);
                }
                publicCouponManager.updateObjDataRange(vo.getEa(),addResultResult.getData().getObjectData().getId(),upStreamTenantIds,CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
                String couponTopEa = publicCouponManager.getCouponTopEa(wechatCouponEntity.getEa());
                //处理优惠券实例负责人
                String couponTopTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(couponTopEa));
                String wechatCouponTenantId = String.valueOf(eieaConverter.enterpriseAccountToId(wechatCouponEntity.getEa()));
                Map<String, Integer> couponInstRelationOwnerMap = publicCouponManager.getEnterpriseRelationOwner(Lists.newArrayList(upStreamTenantIds), couponTopEa);
                upStreamTenantIds.forEach(upStreamTenantId -> {
                    Integer owner = null;
                    if (Objects.equals(wechatCouponEntity.getEa(), couponTopEa)) {
                        if (Objects.equals(upStreamTenantId, couponTopTenantId)) {
                            owner = publicCouponManager.getAccountOwner(String.valueOf(ei), couponTopEa);
                        } else {
                            if (couponInstRelationOwnerMap.containsKey(upStreamTenantId)) {
                                owner = couponInstRelationOwnerMap.get(upStreamTenantId);
                            }
                        }
                    } else {
                        if (Objects.equals(upStreamTenantId, couponTopTenantId)) {
                            //如果是1端企业,获取领券门店上游企业在1端的互联企业的客户负责人
                            owner = publicCouponManager.getAccountOwner(String.valueOf(ei), couponTopEa);
                        } else if (Objects.equals(upStreamTenantId,wechatCouponTenantId)){
                            if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                                //如果当前租户是创建端,并且是上下游场景, 则负责人为当前领券门店客户负责人
                                ObjectData accountObject = crmV2Manager.getDetailIgnoreError(vo.getEa(), -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), vo.getCustomerId());
                                if (accountObject != null && accountObject.containsKey("owner")) {
                                    owner = accountObject.getOwner();
                                }
                            } else {
                                //如果是发券端,获取领券门店上游企业在发券端的互联企业的客户负责人
                                owner = publicCouponManager.getAccountOwner(String.valueOf(ei), wechatCouponEntity.getEa());
                            }
                        } else {
                            if (couponInstRelationOwnerMap.containsKey(upStreamTenantId)) {
                                owner = couponInstRelationOwnerMap.get(upStreamTenantId);
                            }
                        }
                    }
                    publicCouponManager.updatePublicObjOwner(upStreamTenantId,owner,CrmObjectApiNameEnum.COUPON_INST_OBJ.getName(),addResultResult.getData().getObjectData().getId());
                });
                //添加优惠券领取营销动态
                ObjectData objectData = crmV2Manager.getDetail(vo.getEa(), -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), vo.getCustomerId());
                List<String> marketingUserIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(vo.getEa(),CrmObjectApiNameEnum.CUSTOMER.getName(),Lists.newArrayList(objectData), false);
                if (CollectionUtils.isNotEmpty(marketingUserIds)) {
                    wxCouponPayManager.syncRecordCouponMarketingActivityData(vo.getEa(),wechatCouponEntity.getId(),wechatCouponEntity.getMarketingEventId(),marketingUserIds.get(0), MarketingUserActionType.RECEIVE_COUPON.getActionType());
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            return Result.newSuccess(addResultResult.getData().getObjectData().getId());
        } catch (Exception e) {
            log.warn("receivePartnerCoupon error",e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.SYSTEM_ERROR,SHErrorCode.SYSTEM_ERROR.getErrorMessage()));
        } finally {
            redisManager.unLock(couponLockKey);
        }
    }

    @Override
    public Result<PageResult<PartnerReceiveResult>> queryDealerReceiveList(QueryDealerListVo vo) {
        PageResult<PartnerReceiveResult> pageResult = new PageResult<>();
        List<PartnerReceiveResult> partnerCouponResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        PaasQueryUserCouponArg arg = new PaasQueryUserCouponArg();
        arg.setCouponId(wechatCouponEntity.getStockId());
        arg.setPageNumber(vo.getPageNum());
        arg.setPageSize(vo.getPageSize());
        arg.setStatus(vo.getStatus());
        InnerPage<ObjectData> instObjListPage = publicCouponManager.queryPageCouponInst(vo.getEa(),-10000,arg);
        if (instObjListPage == null || CollectionUtils.isEmpty(instObjListPage.getDataList())) {
            pageResult.setResult(partnerCouponResultList);
            return Result.newSuccess(pageResult);
        }
        List<ObjectData> instObjList = instObjListPage.getDataList();
        List<String> partnerIds = instObjList.stream().map(e->e.getString("partner_id_backup")).collect(Collectors.toList());
        Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
        instObjList.forEach(instObj -> {
            PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
            partnerReceiveResult.setReceiveTime(doubleToDateString(instObj.getDouble("send_time")));
            partnerReceiveResult.setDepositObject(true);
            if (Objects.equals(instObj.getString("use_status"),CouponUseStautsEnum.UNUSED.getStatus())) {
                partnerReceiveResult.setStatus(CouponInstStatusEnum.USE.getStatus());
            } else if (Objects.equals(instObj.getString("use_status"),CouponUseStautsEnum.USED.getStatus())) {
                partnerReceiveResult.setStatus(CouponInstStatusEnum.CONFIRM.getStatus());
            }
            partnerReceiveResult.setOutUid(instObj.getString("account_id"));
            partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(instObj.getString("partner_id_backup")));
            partnerCouponResultList.add(partnerReceiveResult);
        });
        pageResult.setTotalCount(instObjListPage.getTotalCount());
        pageResult.setResult(partnerCouponResultList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> exportDealerStatistic(String ea,Integer fsUserId,PartnerCouponDetailStatisticVo vo) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            List<PartnerCouponDetailStatisticResult> dealerStatisticResult = queryAllDealerStatisticResult(ea, fsUserId, vo);
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            String filename = I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2097);
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2099));
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = generateDealerExcelTitleList();
            List<List<Object>> dealerStatisticResultList = generateExcelDatasList(dealerStatisticResult);
            ExcelUtil.fillContent(xssfSheet, titleList, dealerStatisticResultList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> exportStoreStatistic(String ea, Integer fsUserId, QueryDealerListVo vo) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            List<PartnerReceiveResult> storeStatisticResult = queryAllStoreStatisticResult(vo);
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            String filename = I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2117);
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2119));
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = generateStoreExcelTitleList();
            List<List<Object>> dealerStatisticResultList = generateStoreExcelDatasList(storeStatisticResult);
            ExcelUtil.fillContent(xssfSheet, titleList, dealerStatisticResultList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public void handleCouponReceiveRange() {
        List<String> topEaList = GsonUtil.getGson().fromJson(couponTopJobEa,ArrayList.class);
        if (CollectionUtils.isEmpty(topEaList)){
            return;
        }
        log.info("PublicCouponServiceImpl -> handleCouponReceiveRange eas:{}", topEaList);
        topEaList.forEach(ea -> {
            ThreadPoolUtils.executeWithTraceContext(() -> publicCouponManager.handleCouponReceiveRangeJob(ea), ThreadPoolUtils.ThreadPoolTypeEnums.HANDLE_COUPON_RECEIVE);
        });
    }

    @Override
    public Result<Boolean> checkCouponCanBeReceived(CheckCouponVO vo) {
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (wechatCouponEntity == null) {
            log.warn("checkCouponCanBeReceived wechatCouponEntity is null");
            return Result.newSuccess(false);
        }
        if (Objects.equals(wechatCouponEntity.getStatus(),CouponStatusEnum.INVALIDATION.getStatus())) {
            log.warn("checkCouponCanBeReceived coupon is invalidation");
            return Result.newSuccess(false);
        }
        String partnerId = vo.getEROuterTenantId();
        String accountId = null;
        //获取客户id
        int upEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        HeaderObj headerObj = HeaderObj.newInstance(upEi);
        UpstreamAndDownstreamOuterTenantIdOutArg outArg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        outArg.setDownstreamOuterTenantId(Long.valueOf(vo.getEROuterTenantId()));
        outArg.setUpstreamEa(vo.getERUpstreamEa());
        RestResult<String> mapperResult = enterpriseRelationService.getMapperObjectId(headerObj, outArg);
        if (mapperResult.isSuccess() && StringUtils.isNotBlank(mapperResult.getData())) {
            accountId = mapperResult.getData();
        }
        //判断是否在可领取范围内
        boolean flag = publicCouponManager.isInReceiveCouponRange(wechatCouponEntity.getEa(),wechatCouponEntity.getStoreScope(),wechatCouponEntity.getReceiveScope(),accountId);
        if(!flag) {
            log.warn("checkCouponCanBeReceived not receive store range");
            return Result.newSuccess(false);
        }
        Integer directTenantId = null;
        //判断是否有可领优惠券
        if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
            int count = memberCouponBindDAO.queryReceiveAndUseCoupon(vo.getERUpstreamEa(),wechatCouponEntity.getStockId());
            if (count >= wechatCouponEntity.getMaxCoupons()) {
                log.warn("checkCouponCanBeReceived PARTNER maxCoupons is limit");
                return Result.newSuccess(false);
            }
            //判断单人领取数量
            if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                int receivePartnerCouponCount = memberCouponBindDAO.queryReceiveCouponCount(vo.getERUpstreamEa(), wechatCouponEntity.getStockId(), null, partnerId, accountId);
                if (receivePartnerCouponCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                    log.warn("checkCouponCanBeReceived PARTNER maxCouponsPerUser is limit");
                    return Result.newSuccess(false);
                }
            }
        } else {
            //查询当前优惠券可见范围
            PublicDataRangeArg arg = new PublicDataRangeArg();
            arg.setIds(Lists.newArrayList(wechatCouponEntity.getCouponId()));
            arg.setEa(vo.getERUpstreamEa());
            arg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
            Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(arg);
            Set<String> range = publicDataRange.get(wechatCouponEntity.getCouponId());
            //获取发送范围
            CreateWxCouponVO.AccountVisibilityVO accountVisibilityVO = GsonUtil.getGson().fromJson(wechatCouponEntity.getAccountScope(), new TypeToken<CreateWxCouponVO.AccountVisibilityVO>() {}.getType());
            List<String> partnerNoticeTenantIds = publicCouponManager.getTenantIds(wechatCouponEntity.getEa(), accountVisibilityVO.getType(), accountVisibilityVO.getValue(),wechatCouponEntity.getSendScope());
            //根据当前上游企业,查询直属发送范围企业
            Set<Integer> upDirectTenantIds = publicCouponManager.getUpDirectTenantIds(Sets.newHashSet(),eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa()),partnerNoticeTenantIds);
            //查询是否存在可领取的直属下发上游
            directTenantId = publicCouponManager.getEnableReceiveTenantId(upDirectTenantIds,wechatCouponEntity.getStockId(),wechatCouponEntity.getDealerCount(),range);
            if (directTenantId == null || !range.contains(String.valueOf(directTenantId))) {
                log.warn("checkCouponCanBeReceived DEALER directTenantId not receive range");
                return Result.newSuccess(false);
            }
            //判断是否达到单人领取数量限制
            if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                int receiveCount = publicCouponManager.queryAccountIdCouponInstCount(upEi, wechatCouponEntity.getStockId(),accountId,partnerId);
                if (receiveCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                    log.warn("checkCouponCanBeReceived DEALER maxCouponsPerUser is limit");
                    return Result.newSuccess(false);
                }
            }
        }
        return Result.newSuccess(true);
    }

    private List<List<Object>> generateStoreExcelDatasList(List<PartnerReceiveResult> storeStatisticResult) {
        List<List<Object>> datasList = new ArrayList<>();
        storeStatisticResult.forEach(value -> {
            List<Object> objList = new ArrayList<>();
            objList.add(value.getBusinessName());
            objList.add(value.getReceiveTime());
            if (Objects.equals(CouponInstStatusEnum.USE.getStatus(),value.getStatus())) {
                objList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2151));
            } else if (Objects.equals(CouponInstStatusEnum.CONFIRM.getStatus(),value.getStatus())) {
                objList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2153));
            }
            datasList.add(objList);
        });
        return datasList;
    }

    private List<String> generateStoreExcelTitleList() {
        List<String> titleList = new ArrayList<>();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2162));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2163));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2164));
        return titleList;
    }

    private List<PartnerReceiveResult> queryAllStoreStatisticResult(QueryDealerListVo vo) {
        List<PartnerReceiveResult> partnerCouponResultList = Lists.newArrayList();
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        PaasQueryUserCouponArg arg = new PaasQueryUserCouponArg();
        arg.setCouponId(wechatCouponEntity.getStockId());
        arg.setStatus(vo.getStatus());
        List<ObjectData> instObjList = publicCouponManager.queryAllPublicCouponInst(vo.getEa(),arg);
        if (CollectionUtils.isEmpty(instObjList)) {
            return partnerCouponResultList;
        }
        List<String> partnerIds = instObjList.stream().map(e->e.getString("partner_id_backup")).collect(Collectors.toList());
        Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
        instObjList.forEach(instObj -> {
            PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
            partnerReceiveResult.setReceiveTime(doubleToDateString(instObj.getDouble("send_time")));
            partnerReceiveResult.setDepositObject(true);
            if (Objects.equals(instObj.getString("use_status"),CouponUseStautsEnum.UNUSED.getStatus())) {
                partnerReceiveResult.setStatus(CouponInstStatusEnum.USE.getStatus());
            } else if (Objects.equals(instObj.getString("use_status"),CouponUseStautsEnum.USED.getStatus())) {
                partnerReceiveResult.setStatus(CouponInstStatusEnum.CONFIRM.getStatus());
            }
            partnerReceiveResult.setOutUid(instObj.getString("account_id"));
            partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(instObj.getString("partner_id_backup")));
            partnerCouponResultList.add(partnerReceiveResult);
        });
        return partnerCouponResultList;
    }

    private List<List<Object>> generateExcelDatasList(List<PartnerCouponDetailStatisticResult> dealerStatisticResult) {
        List<List<Object>> datasList = new ArrayList<>();
        dealerStatisticResult.forEach(value -> {
            List<Object> objList = new ArrayList<>();
            objList.add(value.getBusinessName());
            objList.add(value.getReceiveCount());
            objList.add(value.getUseCount());
            datasList.add(objList);
        });
        return datasList;
    }

    private List<String> generateDealerExcelTitleList() {
        List<String> titleList = new ArrayList<>();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2162));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2211));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_2153));
        return titleList;
    }

    private List<PartnerCouponDetailStatisticResult> queryAllDealerStatisticResult(String ea, Integer fsUserId, PartnerCouponDetailStatisticVo vo) {
        List<PartnerCouponDetailStatisticResult> partnerCouponResultList = Lists.newArrayList();
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        //所有直属下游
        TenantIdCascadeArg arg = new TenantIdCascadeArg();
        arg.setTenantId(tenantId);
        RestResult<Set<Integer>> allDownstreamTenantIdsResult = enterpriseRelationService.listAllDownstreamTenantIds(HeaderObj.newInstance(tenantId), arg);
        if (!allDownstreamTenantIdsResult.isSuccess() || CollectionUtils.isEmpty(allDownstreamTenantIdsResult.getData())) {
            return partnerCouponResultList;
        }
        Set<Integer> downstreamTenantIdsResultData = allDownstreamTenantIdsResult.getData();
        //可见范围
        PublicDataRangeArg rangeArg = new PublicDataRangeArg();
        rangeArg.setEa(ea);
        rangeArg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        rangeArg.setIds(Lists.newArrayList(couponEntity.getCouponId()));
        Map<String, Set<String>> publicDataRange = publicMetadataManager.findPublicDataRange(rangeArg);
        Set<String> rangeData = publicDataRange.get(couponEntity.getCouponId());
        Set<Integer> rangeTenantIds = new HashSet<>();
        rangeData.forEach(range ->{
            rangeTenantIds.add(Integer.parseInt(range));
        });
        //获取下游
        Set<Integer> downTenantIds = downstreamTenantIdsResultData.stream().filter(rangeTenantIds::contains).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(downTenantIds)) {
            return partnerCouponResultList;
        }
        //批量获取企业名称
        Map<Integer, String> easMap = eieaConverter.enterpriseIdToAccount(downTenantIds);
        Set<String> eas = Sets.newHashSet(easMap.values());
        Map<String, String> companyNameMap = publicCouponManager.queryBelongCompanyName(eas);
        downTenantIds.forEach(downTenantId ->{
            PartnerCouponDetailStatisticResult partnerCouponDetailStatisticResult = new PartnerCouponDetailStatisticResult();
            partnerCouponDetailStatisticResult.setReceiveCount(publicCouponManager.queryCouponInstCount(downTenantId, couponEntity.getStockId(), null));
            partnerCouponDetailStatisticResult.setUseCount(publicCouponManager.queryCouponInstCount(downTenantId, couponEntity.getStockId(), CouponUseStautsEnum.USED.getStatus()));
            partnerCouponDetailStatisticResult.setBusinessName(companyNameMap.get(eieaConverter.enterpriseIdToAccount(downTenantId)));
            partnerCouponDetailStatisticResult.setEa(eieaConverter.enterpriseIdToAccount(downTenantId));
            partnerCouponResultList.add(partnerCouponDetailStatisticResult);
        });
        return partnerCouponResultList;
    }

    private void copyPlanInfo(CouponPlanInfoResult data, CouponResult couponResult) {
        couponResult.setName(data.getName());
        couponResult.setPlanType(data.getPlanType());
        couponResult.setRemark(data.getRemark());
        couponResult.setStartDate(data.getStartDate());
        couponResult.setEndDate(data.getEndDate());
        //couponResult.setProductConditionType(data.getProductConditionType());
        couponResult.setProductConditionContent(data.getProductConditionDesc());
        couponResult.setLowerLimit(doubleStrToIntStr(data.getLowerLimit()));
        couponResult.setUseType(data.getUseType());
        couponResult.setAmount(doubleStrToIntStr(data.getAmount()));
    }

    private String doubleStrToIntStr(String str) {
        if(str.contains(".")) {
            if (str.contains(".00")){
                int indexOf = str.indexOf(".");
                str = str.substring(0, indexOf);
            }
        }
        return str;
    }

    private String doubleToDateString(double doubleStr) {
        long timestamp = (long) doubleStr;
        Date date = new Date(timestamp);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    private int totalCouponCount(Integer dealerCount, String couponId,String ea) {
        int dealerSize = 0;
        List<SendDealerCouponRecordEntity> sendDealerCouponRecordEntities = sendDealerCouponRecordDAO.queryList(couponId, ea);
        if (CollectionUtils.isNotEmpty(sendDealerCouponRecordEntities)) {
            dealerSize = sendDealerCouponRecordEntities.size();
        }
        if (dealerCount == null) {
            return 0;
        }
        return dealerCount * dealerSize;
    }

    private void copyPlanInfoByH5(CouponPlanInfoResult data, UserCouponListResult couponResult) {
        couponResult.setName(data.getName());
        couponResult.setPlanType(data.getPlanType());
        couponResult.setRemark(data.getRemark());
        couponResult.setStartDate(data.getStartDate());
        couponResult.setEndDate(data.getEndDate());
        //couponResult.setProductConditionType(data.getProductConditionType());
        couponResult.setProductConditionContent(data.getProductConditionDesc());
        couponResult.setLowerLimit(doubleStrToIntStr(data.getLowerLimit()));
        couponResult.setUseType(data.getUseType());
        couponResult.setAmount(doubleStrToIntStr(data.getAmount()));
    }

    private void copyPlanInfoByApp(CouponPlanInfoResult data, CouponListResult couponResult) {
        couponResult.setName(data.getName());
        couponResult.setPlanType(data.getPlanType());
        couponResult.setRemark(data.getRemark());
        couponResult.setStartDate(data.getStartDate());
        couponResult.setEndDate(data.getEndDate());
        //couponResult.setProductConditionType(data.getProductConditionType());
        couponResult.setProductConditionContent(data.getProductConditionDesc());
        couponResult.setLowerLimit(doubleStrToIntStr(data.getLowerLimit()));
        couponResult.setUseType(data.getUseType());
        couponResult.setAmount(doubleStrToIntStr(data.getAmount()));
    }
}