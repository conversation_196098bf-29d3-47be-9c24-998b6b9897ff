package com.facishare.marketing.provider.service.qywx;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.arg.qywx.GetMomentSendCustomerArg;
import com.facishare.marketing.api.result.qywx.*;
import com.facishare.marketing.api.service.QywxMomentService;
import com.facishare.marketing.api.vo.qywx.ListMomentTaskVO;
import com.facishare.marketing.api.vo.qywx.MomentMessageVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.AssociateIdTypeEnum;
import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.qywx.QYWXMomentSendResultDaO;
import com.facishare.marketing.provider.dao.qywx.QYWXMomentTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dto.qywx.QueryMomentSendResultDTO;
import com.facishare.marketing.provider.entity.data.MomentMessageData;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxMomentTaskEntity;
import com.facishare.marketing.provider.innerArg.qywx.MomentTaskArg;
import com.facishare.marketing.provider.innerResult.qywx.*;
import com.facishare.marketing.provider.manager.ExecuteTaskDetailManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.MarketingJobUtil;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import redis.clients.jedis.params.SetParams;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service("qywxMomentService")
@Slf4j
public class QywxMomentServiceImpl implements QywxMomentService {
    @Autowired
    private MomentManager momentManager;
    @Autowired
    private QYWXMomentTaskDAO qywxMomentTaskDAO;

    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QYWXMomentSendResultDaO qywxMomentSendResultDaO;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MetadataTagManager metadataTagManager;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ExecuteTaskDetailManager executeTaskDetailManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private MergeJedisCmd jedisCmd;
    @Autowired
    private QywxActivatedAccountManager qywxActivatedAccountManager;
    private Long scanAllTaskTime; //当天全量调度时间

    Gson gson = new Gson();

    /**
     * 获取朋友圈结果
     */
    @Override
    public void qywxMomentResultSchedule() {
        //每天全量调度一次所有任务，5分钟调度一次当天任务
        boolean scanAllTask = false;
        Long currentTime = System.currentTimeMillis();
        if (scanAllTaskTime == null){
            scanAllTaskTime = System.currentTimeMillis();
            scanAllTask = true;
        }else {
            if (checkSameDayByTime(currentTime, scanAllTaskTime)){
                scanAllTask = false;
            }else {
                scanAllTask = true;
                scanAllTaskTime = System.currentTimeMillis();
            }
        }
        momentManager.qywxMomentResultSchedule(scanAllTask);
    }

    @Override
    public void qywxMomentTaskSchedule() {
        List<QywxMomentTaskEntity> momentTaskResultTask = qywxMomentTaskDAO.getMomentTaskResultTask();
        if (CollectionUtil.isEmpty(momentTaskResultTask)) {
            return;
        }
        momentTaskResultTask.forEach(m -> {
            //过滤掉停用企业、营销通配额过期企业
            if (marketingActivityRemoteManager.enterpriseStop(m.getEa()) || appVersionManager.getCurrentAppVersion(m.getEa()) == null) {
                log.info("QywxMomentServiceImpl.qywxMomentResultSchedule failed enterprise stop or license expire ea:{}", m.getEa());
                return;
            }
            String accessToken = momentManager.getOrCreateAccessToken(m.getEa());
            ThreadPoolUtils.execute(() -> getTaskResult(m,accessToken), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        });
    }

    private boolean checkSameDayByTime(long time1, long time2){
        Date date1 = new Date(time1);
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Date date2 = new Date(time2);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1= cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);
        return day1 == day2;
    }
    /**
     * 延迟发送
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void qywxMomentDelayTaskSchedule() {
        List<QywxMomentTaskEntity> momentDelayTask = qywxMomentTaskDAO.getMomentDelayTask();
        if (CollectionUtil.isEmpty(momentDelayTask)) {
            return;
        }
        List<QywxMomentTaskEntity> toBeHandleTaskList = Lists.newArrayList();

        Map<String, List<QywxMomentTaskEntity>> eaToMarketingActivityIdMap = momentDelayTask.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingActivityId()))
                .collect(Collectors.groupingBy(QywxMomentTaskEntity::getEa));
        Map<String, Boolean> auditMap = new HashMap<>();
        eaToMarketingActivityIdMap.forEach((ea, taskList) -> {
            List<String> marketingActivityIdList = taskList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingActivityId()))
                    .map(QywxMomentTaskEntity::getMarketingActivityId).collect(Collectors.toList());
            Map<String, Boolean> map = marketingActivityAuditManager.batchCheckAuditStatus(ea, marketingActivityIdList);
            auditMap.putAll(map);
        });

        for (QywxMomentTaskEntity task : momentDelayTask) {
            Boolean isAudit = auditMap.get(task.getMarketingActivityId());
            if (!isAudit) {
                continue;
            }
            if (MarketingJobUtil.isMarketingJobForbidExec(task.getEa())) {
                log.warn("当前时间禁止发送营销消息,朋友圈job:{}", task);
                qywxMomentTaskDAO.updateStatusAndErrorInfoById(SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorCode(), task.getId(), SHErrorCode.FORBID_SEND_MARKETING_MESSAGE.getErrorCode(), MarketingJobUtil.getErrorMessage());
                continue;
            }
            // 先更新一下状态，避免因为线程池阻塞导致任务被多次提交
            int updateResult = qywxMomentTaskDAO.updateStatusByIdWithOldStatus(2, 1, task.getId());
            if (updateResult > 0) {
                toBeHandleTaskList.add(task);
            }
        }
        if (CollectionUtils.isEmpty(toBeHandleTaskList)) {
            return;
        }
        ThreadPoolUtils.execute(() -> toBeHandleTaskList.forEach(this::handlerSendTask), ThreadPoolUtils.ThreadPoolTypeEnums.QYWX_MSG_SEND);
    }

    /**
     * 延迟发送
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void qywxMomentDelayTaskScheduleByEa(String fsEa) {
        List<QywxMomentTaskEntity> momentDelayTask = qywxMomentTaskDAO.getMomentDelayTaskByEa(fsEa);
        if (CollectionUtil.isEmpty(momentDelayTask)) {
            return;
        }
        List<QywxMomentTaskEntity> toBeHandleTaskList = Lists.newArrayList();

        Map<String, List<QywxMomentTaskEntity>> eaToMarketingActivityIdMap = momentDelayTask.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingActivityId()))
                .collect(Collectors.groupingBy(QywxMomentTaskEntity::getEa));
        Map<String, Boolean> auditMap = new HashMap<>();
        eaToMarketingActivityIdMap.forEach((ea, taskList) -> {
            List<String> marketingActivityIdList = taskList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingActivityId()))
                    .map(QywxMomentTaskEntity::getMarketingActivityId).collect(Collectors.toList());
            Map<String, Boolean> map = marketingActivityAuditManager.batchCheckAuditStatus(ea, marketingActivityIdList);
            auditMap.putAll(map);
        });

        for (QywxMomentTaskEntity task : momentDelayTask) {
            Boolean isAudit = auditMap.get(task.getMarketingActivityId());
            if (!isAudit) {
                continue;
            }
            if (MarketingJobUtil.isMarketingJobForbidExec(task.getEa())) {
                log.warn("当前时间禁止发送营销消息,朋友圈job:{}", task);
                qywxMomentTaskDAO.updateStatusAndErrorInfoById(SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorCode(), task.getId(), SHErrorCode.FORBID_SEND_MARKETING_MESSAGE.getErrorCode(), MarketingJobUtil.getErrorMessage());
                continue;
            }
            // 先更新一下状态，避免因为线程池阻塞导致任务被多次提交
            int updateResult = qywxMomentTaskDAO.updateStatusByIdWithOldStatus(2, 1, task.getId());
            if (updateResult > 0) {
                toBeHandleTaskList.add(task);
            }
        }
        if (CollectionUtils.isEmpty(toBeHandleTaskList)) {
            return;
        }
        ThreadPoolUtils.execute(() -> toBeHandleTaskList.forEach(this::handlerSendTask), ThreadPoolUtils.ThreadPoolTypeEnums.QYWX_MSG_SEND);
    }

    public void qywxMomentInvokeTaskById(String taskId) {
        QywxMomentTaskEntity momentDelayTask = qywxMomentTaskDAO.getById(taskId);
        this.handlerSendTask(momentDelayTask);
    }

    private void handlerSendTask(QywxMomentTaskEntity task) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(task.getEa()) || appVersionManager.getCurrentAppVersion(task.getEa()) == null) {
            log.info("QywxMomentServiceImpl.qywxMomentDelayTaskSchedule failed enterprise stop or license expire ea:{}", task.getEa());
            return;
        }
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(task.getEa(), AssociateIdTypeEnum.MOMENT_SEND_ACTIVITY.getType(), task.getId());
        if (marketingActivityExternalConfigEntity == null){
            log.info("QywxMomentServiceImpl.qywxMomentDelayTaskSchedule failed marketingActivityExternalConfigEntity is not exist ea:{} taskId:{}", task.getEa(), task.getId());
            return;
        }
        MomentTaskArg momentTaskArg = new MomentTaskArg();
        MomentTaskArg.Text text = new MomentTaskArg.Text();
        text.setContent(task.getContent());
        momentTaskArg.setText(text);
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(task.getEa()) ;
        MomentTaskArg.SenderList senderList = new MomentTaskArg.SenderList();
        List<String> userIdList = Lists.newArrayList();
        MomentMessageData momentMessageData = marketingActivityExternalConfigEntity.getExternalConfig().getMomentMessageVO();
        if (isOpen) {
            List<Integer> dataPermission = dataPermissionManager.filterUserAccessibleQywxDeptIds(task.getEa(), momentMessageData.getUserId(), momentMessageData.getDepartmentIds());
            userIdList = qywxManager.handleQywxEmployeeUserId(task.getEa(), momentMessageData.getUserIdList(), dataPermission, null);
        } else {
            userIdList = qywxManager.handleQywxEmployeeUserId(task.getEa(), momentMessageData.getUserIdList(), momentMessageData.getDepartmentIds(), momentMessageData.getTagIds());
        }

        if (qywxActivatedAccountManager.shouldFiltering(task.getEa())) {
            List<String> activatedUserIds = qywxActivatedAccountManager.getActivatedUserIds(task.getEa());
            userIdList = userIdList.stream().filter(activatedUserIds::contains).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(userIdList)) {
            log.error("QywxMomentServiceImpl.qywxMomentDelayTaskSchedule failed userIdList is empty ea:{} taskId:{}", task.getEa(), task.getId());
            qywxMomentTaskDAO.updateJobIdAndStatusById(null, 3, task.getId());
            return;
        }
        senderList.setUserList(userIdList);

        MomentTaskArg.VisibleRange visibleRange = new MomentTaskArg.VisibleRange();
        visibleRange.setSenderList(senderList);
        if (StringUtils.isNotBlank(task.getTagIdList())) {
            List<String> tagIdList = gson.fromJson(task.getTagIdList(), ArrayList.class);
            MomentTaskArg.ExternalContactList externalContactList = new MomentTaskArg.ExternalContactList();
            externalContactList.setTagList(tagIdList);
            visibleRange.setExternalContactList(externalContactList);
        }
        momentTaskArg.setVisibleRange(visibleRange);
        String accessToken = momentManager.getOrCreateAccessToken(task.getEa());
        List<MomentTaskArg.Attachments> attachments = new ArrayList<>();
        momentTaskArg.setAttachments(attachments);
        //link
        if (null != task.getMomentType() && 1 == task.getMomentType()) {
            MomentTaskArg.Link link = new MomentTaskArg.Link();
            link.setTitle(task.getLinkTitle());
            String mediaId = redisManager.getQywxAttachmentMediaIdByEa(task.getLinkPicPath(), task.getEa());
            if (StringUtils.isBlank(mediaId)) {
                byte[] data = fileV2Manager.downloadAFile(task.getLinkPicPath(),task.getEa());
                mediaId = momentManager.uploadAttachment(accessToken, task.getLinkPicPath(), "image", data);
            }
            link.setMediaId(mediaId);
            link.setUrl(task.getLinkUrl());
            link.setUrl(ReplaceUtil.replaceMarketingActivityId(link.getUrl(), marketingActivityExternalConfigEntity.getMarketingActivityId()));
            link.setUrl(ReplaceUtil.replaceMarketingEventId(link.getUrl(), ""));
            link.setUrl(ReplaceUtil.replaceWxAppId(link.getUrl(), ""));
            MomentTaskArg.Attachments attachment = new MomentTaskArg.Attachments();
            attachment.setMsgType("link");
            attachment.setLink(link);
            attachments.add(attachment);
        }
        //image
        if (null != task.getMomentType() && 2 == task.getMomentType()) {
            MomentMessageVO momentMessageVO = new MomentMessageVO();
            List<MomentMessageVO.Image> imagesList = gson.fromJson(task.getImageInfo(), new TypeToken<List<MomentMessageVO.Image>>() {
            }.getType());
            momentMessageVO.setImage(imagesList);
            Map<String, String> materialIdToQywxId = momentManager.dealImages(accessToken, task.getEa(),momentMessageVO);
            if (null == materialIdToQywxId) {
                return;
            }
            for (MomentMessageVO.Image image : momentMessageVO.getImage()) {
                MomentTaskArg.Image qyImag = new MomentTaskArg.Image();
                qyImag.setMediaId(materialIdToQywxId.get(image.getImagePath()));
                MomentTaskArg.Attachments attachment = new MomentTaskArg.Attachments();
                attachment.setImage(qyImag);
                attachment.setMsgType("image");
                attachments.add(attachment);
            }
        }
        //video
        if (null != task.getMomentType() && 3 == task.getMomentType()) {
            MomentTaskArg.Video video = new MomentTaskArg.Video();
            video.setMediaId(task.getVideoMediaId());
            MomentTaskArg.Attachments attachment = new MomentTaskArg.Attachments();
            attachment.setVideo(video);
            attachment.setMsgType("video");
            attachments.add(attachment);
        }

        //文件
        if (null != task.getMomentType() && 10 == task.getMomentType()) {
            MomentTaskArg.Link link = new MomentTaskArg.Link();
            link.setTitle(task.getLinkTitle());
            String mediaId = redisManager.getQywxAttachmentMediaIdByEa(task.getLinkPicPath(),task.getEa());
            if (StringUtils.isBlank(mediaId)) {
                byte[] data = fileV2Manager.downloadFileByUrl(task.getLinkPicPath(),task.getEa());
                mediaId = momentManager.uploadAttachment(accessToken, task.getLinkPicPath(), "image", data);
                if(StringUtils.isBlank(mediaId)){
                    return;
                }
                redisManager.setQywxAttachmentMediaIdByEa(task.getLinkPicPath(),task.getEa(),mediaId);
            }
            link.setMediaId(mediaId);
            link.setUrl(task.getLinkUrl());
            link.setUrl(ReplaceUtil.replaceMarketingActivityId(link.getUrl(), marketingActivityExternalConfigEntity.getMarketingActivityId()));
            link.setUrl(ReplaceUtil.replaceMarketingEventId(link.getUrl(), ""));
            link.setUrl(ReplaceUtil.replaceWxAppId(link.getUrl(), ""));
            MomentTaskArg.Attachments attachment = new MomentTaskArg.Attachments();
            attachment.setMsgType("link");
            attachment.setLink(link);
            attachments.add(attachment);
        }

        MomentTaskResult momentTaskResult = momentManager.addMomentTask(accessToken, momentTaskArg);
        if (null != momentTaskResult && 0 == momentTaskResult.getErrcode()) {
            qywxMomentTaskDAO.updateJobIdAndStatusById(momentTaskResult.getJobid(), 2, task.getId());
            momentManager.getTaskResultAfterPublish(task.getEa(), task.getId(), momentTaskResult.getJobid(),accessToken);
        }
    }

    @Override
    public Result<PageResult<ListMomentTaskResult>> listMomentTask(String ea, Integer fsUserId, ListMomentTaskVO vo) {
        return momentManager.listMomentTask(ea, fsUserId, vo);
    }

    @Override
    public Result<Map<String, Integer>> listMomentStaticData(String ea, Integer fsUserId) {
        long start = DateUtil.getTimesThisMonthStartTime().getTime();
        Calendar cal = Calendar.getInstance();
        long end = DateUtil.getEndDayOfMonth(cal.get(1), cal.get(2) + 1).getTime();
        int taskCount = qywxMomentTaskDAO.queryTaskByTimeRange(ea, new Date(start), new Date(end));

        QueryMomentSendResultDTO queryMomentSendResultDTO = qywxMomentSendResultDaO.emplyeeSendAndCusomerByMonth(ea, start, end);
        int canNotSend = qywxMomentSendResultDaO.queryExceedFourMomentByMonth(ea, start, end);
        int total = qywxMomentSendResultDaO.getEmployeeSendTimes(ea, start, end);
        int totalQywxCount = crmMetadataManager.countCrmObjectData(ea, -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        Map<String, Integer> res = new HashMap<>();
        res.put("taskCount", taskCount);   //发表朋友圈任务数
        res.put("canSend", totalQywxCount - canNotSend);
        res.put("receiveTotal", queryMomentSendResultDTO.getCustomerCount());
        res.put("sendTotal", queryMomentSendResultDTO.getCustomerCount());   //累计触达客户数
        res.put("total", total);   //员工累计发表朋友圈总次数
        return Result.newSuccess(res);
    }

    @Override
    public Result<SendMomentCustomerResult> asyncGetMomentSendCustomerData(String ea, Integer fsUserId, GetMomentSendCustomerArg arg) {
        if (StringUtils.isNotBlank(arg.getCalculationTaskId())) {
            String resultStr = jedisCmd.get(arg.getCalculationTaskId());
            if (StringUtils.isNotBlank(resultStr)) {
                return Result.newSuccess(JSONObject.parseObject(resultStr, SendMomentCustomerResult.class));
            } else {
                return Result.newError(SHErrorCode.SERVER_BUSY);
            }
        }
        List<String> userIdList = arg.getUserIdList();
        List<Integer> departmentIds = arg.getDepartmentIds();
        List<Integer> qywxTagIds = arg.getTagIds();
        List<TagName> tags = arg.getTags();
        SendMomentCustomerResult sendMomentCustomerResult = new SendMomentCustomerResult();
        String calculationTaskId = UUIDUtil.getUUID();
        sendMomentCustomerResult.setCalculationTaskId(calculationTaskId);
        ThreadPoolUtils.execute(() -> ThreadPoolUtils.execute(() -> jedisCmd.set(calculationTaskId, JSONObject.toJSONString(this.getMomentSendCustomerData(ea, fsUserId, userIdList, departmentIds, qywxTagIds, tags).getData()), SetParams.setParams().nx().ex(60 * 60L)), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess(sendMomentCustomerResult);
    }

    @Override
    public Result<SendMomentCustomerResult> getMomentSendCustomerData(String ea, Integer fsUserId, List<String> userIdList, List<Integer> departmentIds, List<Integer> qywxTagIds, List<TagName> tags) {
        StopWatch stopWatch = new StopWatch("getMomentSendCustomerData");
        SendMomentCustomerResult result = new SendMomentCustomerResult();
        result.setCustomerCount(0);
        result.setEmployeeCount(0);
        result.setSelectEmployeeCount(0);

        stopWatch.start("setEmployeeCount");
        //获取员工userId
        List<String> userIds = Lists.newArrayList();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
        if (isOpen) {
            userIds = qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(ea, fsUserId, userIdList, departmentIds);
        } else {
            userIds = qywxManager.handleQywxEmployeeUserId(ea, userIdList, departmentIds, qywxTagIds);
        }
        if (qywxActivatedAccountManager.shouldFiltering(ea)) {
            List<String> activatedUserIds = qywxActivatedAccountManager.getActivatedUserIds(ea);
            userIds = userIds.stream().filter(activatedUserIds::contains).collect(Collectors.toList());
        }
        result.setEmployeeCount(userIds.size());
        stopWatch.stop();

        stopWatch.start("setCustomerCount");
        //根据企微客户id 和 查询的标签条件筛选企微客户
        int customerCount = 0;
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        List<PaasQueryArg.Condition> contions = new ArrayList<>();
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        PaasQueryArg queryArg = new PaasQueryArg(0, 1);
        PaasQueryArg.Condition appScopeCondition = new PaasQueryArg.Condition(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()),PaasAndCrmOperatorEnum.IN.getCrmOperator());
        contions.add(appScopeCondition);
        if (CollectionUtils.isNotEmpty(tags)) {
            List<String> tagIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(tags)) {
                Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), tags);
                if (MapUtils.isEmpty(tagNamesIdMap)) {
                    return Result.newSuccess(result);
                }
                tagIds = tagNamesIdMap.values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
                PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag",tagIds,OperatorConstants.HASANYOF);
                condition.setValueType(11);
                contions.add(condition);
            }
        }
        queryArg.setFilters(contions);
        queryArg.addFilter("add_status", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList("normal"));
        queryArg.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
        paasQueryFilterArg.setQuery(queryArg);
        paasQueryFilterArg.setSelectFields(Lists.newArrayList("_id","name"));
        customerCount = crmV2Manager.countCrmObjectByFilterV3(ea, fsUserId, paasQueryFilterArg);
        result.setCustomerCount(customerCount);
        stopWatch.stop();

        log.info(stopWatch.prettyPrint());
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<MomentSendListResult>> queryMomentSendCustomerList(String ea, Integer fsUserId, List<String> userIdList, List<Integer> departmentIds, List<Integer> qywxTagIds, List<TagName> tags, Integer pageSize, Integer pageNum) {
        List<MomentSendListResult> momentSendListResults= Lists.newArrayList();
        PageResult<MomentSendListResult> pageResult = new PageResult<>();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        pageResult.setResult(momentSendListResults);
        //获取员工
        List<String> userIds = Lists.newArrayList();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea) || fsBindManager.isQywxContactFsSetting(ea);
        if (isOpen) {
            userIds = qywxManager.handleQywxEmployeeUserIdWithOpenDataPermission(ea, fsUserId, userIdList, departmentIds);
        } else {
            userIds = qywxManager.handleQywxEmployeeUserId(ea, userIdList, departmentIds, qywxTagIds);
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return Result.newSuccess(pageResult);
        }
        momentManager.getStaffInfoByUserIds(ea, pageSize, pageNum, momentSendListResults, pageResult, userIds);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<SendMomentListResult>> querySendMomentList(String ea, Integer fsUserId, String userId, List<TagName> tags, Integer pageNum, Integer pageSize) {
        List<SendMomentListResult> sendMomentListResults= Lists.newArrayList();
        PageResult<SendMomentListResult> pageResult = new PageResult<>();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        pageResult.setResult(sendMomentListResults);

        //查询企微加好友记录
        List<String> selectFields = Lists.newArrayList("external_user_id","user_id","qywx_user_id","avtar","remark_name","add_emp");
        PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
        paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(userId));
        paasQueryArg.addFilter("friend_status", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList("0"));
        paasQueryArg.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
        paasFilterArg.setQuery(paasQueryArg);
        paasFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        List<ObjectData> dataList = objectDataInnerPage.getDataList();
        List<String> objectIds = dataList.stream().map(data -> data.getString("external_user_id")).collect(Collectors.toList());

        //根据objectIds 和 tags 筛选企微客户
        List<PaasQueryArg.Condition> contions = new ArrayList<>();
        List<String> fileds = Lists.newArrayList("_id","name");
        PaasQueryFilterArg filterArg= new PaasQueryFilterArg();
        filterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        PaasQueryArg queryArg = new PaasQueryArg(0, objectIds.size());
        PaasQueryArg.Condition idCondition = new PaasQueryArg.Condition("_id",objectIds,PaasAndCrmOperatorEnum.IN.getCrmOperator());
        contions.add(idCondition);
        if (CollectionUtils.isNotEmpty(tags)) {
            List<String> tagIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(tags)) {
                Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), tags);
                if (MapUtils.isEmpty(tagNamesIdMap)) {
                    return Result.newSuccess(pageResult);
                }
                tagIds = tagNamesIdMap.values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
                PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag",tagIds,OperatorConstants.HASANYOF);
                condition.setValueType(11);
                contions.add(condition);
            }
        }
        queryArg.setFilters(contions);
        queryArg.addFilter("add_status", PaasAndCrmOperatorEnum.IN.getCrmOperator(), Collections.singletonList("normal"));
        queryArg.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList("default__c"));
//        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
//        if (isOpen) {
//            List<Integer> dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
//            queryArg.addFilter("data_own_organization", OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//        }
        filterArg.setQuery(queryArg);
        filterArg.setSelectFields(fileds);
        InnerPage<ObjectData> innerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, filterArg);
        if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        List<String> filterObjectIds = innerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        dataList.stream().filter(data -> filterObjectIds.contains(data.getString("external_user_id"))).forEach(data ->{
            SendMomentListResult sendMomentListResult = new SendMomentListResult();
            sendMomentListResult.setExternalUserId(data.getString("external_user_id"));
            sendMomentListResult.setAvatar(getAvatarFormCrmData(data,ea));
            sendMomentListResult.setRemarkName(data.getString("remark_name"));
            sendMomentListResult.setWxWorkAdder(data.getString("add_emp"));
            sendMomentListResults.add(sendMomentListResult);
        });
        //手动分页
        pageResult.setTotalCount(sendMomentListResults.size());
        // 手动分页
        PageUtil<SendMomentListResult> pageUtil = new PageUtil<>(sendMomentListResults,pageSize);
        pageResult.setResult(pageUtil.getPagedList(pageNum));
        return Result.newSuccess(pageResult);
    }

    private Integer getCustomerCount(String ea,List<String> objectIds, List<TagName> tags) {
        int customerCount = 0;
        List<List<String>> partitionObjIds = Lists.partition(objectIds, 1000);
        for (List<String> partitionObjId : partitionObjIds) {
            List<String> fileds = Lists.newArrayList("_id","name");
            PaasQueryFilterArg filterArg= new PaasQueryFilterArg();
            filterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            PaasQueryArg queryArg = new PaasQueryArg(0, 100);
            queryArg.addFilter("_id",PaasAndCrmOperatorEnum.IN.getCrmOperator(),partitionObjId);
            if (CollectionUtils.isNotEmpty(tags)) {
                List<String> tagIds = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(tags)) {
                    Map<TagName, String> tagNamesIdMap = metadataTagManager.getTagIdsByTagNames(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), tags);
                    if (MapUtils.isEmpty(tagNamesIdMap)) {
                        return customerCount;
                    }
                    tagIds = tagNamesIdMap.values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
                    PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag",tagIds,OperatorConstants.HASANYOF);
                    condition.setValueType(11);
                    queryArg.setFilters(Lists.newArrayList(condition));
                }
            }
            filterArg.setQuery(queryArg);
            filterArg.setSelectFields(fileds);
            InnerPage<ObjectData> innerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, filterArg);
            if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
                log.warn("getCustomerCount  error result :{}", innerPage);
                continue;
            }
            customerCount += innerPage.getDataList().size();
        }
        return customerCount;
    }

    private boolean checkTagId(BatchGetQywxCustomerResult.SingleFollowInfoDetail followInfoDetail,List<String> qywxTagIdList,List<String> addUserIds) {
        if (CollectionUtils.isEmpty(qywxTagIdList)) {
            return true;
        }
        if (addUserIds.contains(followInfoDetail.getUserId())) {
            return false;
        }
        boolean flag = false;
        for (String tagId : followInfoDetail.getTagId()) {
            if (qywxTagIdList.contains(tagId)) {
                flag = true;
                addUserIds.add(followInfoDetail.getUserId());
                break;
            }
        }
        return flag;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    private void getTaskResult(QywxMomentTaskEntity taskEntity, String accessToken) {
        GetMomentTaskResult momentTaskResult = null;

        try {
            momentTaskResult = momentManager.getMomentTaskResult(accessToken, taskEntity.getJobId());
        } catch (Exception e) {
            log.info("QywxMomentServiceImpl.getTaskResult error jobid {} ", taskEntity.getJobId());
        }

        if (null == momentTaskResult) {
            qywxMomentTaskDAO.updateStatusAndErrorInfoById(SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorCode(), taskEntity.getId(), SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorCode(), SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorMessage());
            return;
        }

        if (0 != momentTaskResult.getErrcode()) {
            qywxMomentTaskDAO.updateStatusAndErrorInfoById(SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorCode(), taskEntity.getId(), SHErrorCode.QYWX_MOMENT_JOB_ERROR.getErrorCode(), momentTaskResult.getErrmsg());
            return;
        }

        if (0 == momentTaskResult.getErrcode() && null != momentTaskResult.getStatus() && 3 == momentTaskResult.getStatus()) {
            qywxMomentTaskDAO.updateStatusAndErrorInfoAndMomentIdById(3, taskEntity.getId(), 0, momentTaskResult.getErrmsg(), momentTaskResult.getResult().getMomentId());
        }


    }

    private String getAvatarFormCrmData(ObjectData objectData, String ea) {
        // 头像
        List<Map<String, Object>> avatarMap = GsonUtil
                .fromJson(GsonUtil.getGson().toJson(objectData.get(CrmWechatWorkExternalUserFieldEnum.AVATAR.getFieldName())), new TypeToken<List<Map<String, String>>>() {
                }.getType());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(avatarMap)) {
            return fileV2Manager.getUrlByPath(avatarMap.get(0).get("path") + "." + avatarMap.get(0).get("ext"), ea, false);
        }
        return null;
    }


}
