/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.conference;

import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.permission.DataPermissionResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.permission.DataPermissionService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.appMenu.PaasObjectRuleVO;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.appMenu.AppMenuAccessibleRuleEnum;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.service.OutFileService;
import com.facishare.mankeep.api.service.ActivityService;
import com.facishare.mankeep.api.vo.SignActivityVo;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.GetCampaignDataArg;
import com.facishare.marketing.api.GetSignInDetailArg;
import com.facishare.marketing.api.GetSignInSuccessSettingArg;
import com.facishare.marketing.api.UpdateSignInSuccessSettingArg;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.conference.CheckSignInStatusArg;
import com.facishare.marketing.api.arg.conference.CreateSignInQrCodeArg;
import com.facishare.marketing.api.arg.conference.QueryInviteCountInfoArg;
import com.facishare.marketing.api.arg.conference.SignInArg;
import com.facishare.marketing.api.arg.hexagon.CreateHexagonWxQrCodeArg;
import com.facishare.marketing.api.arg.sms.ApplyTemplateVO;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.conference.*;
import com.facishare.marketing.api.result.conference.GetAllEnrollDataByCampaignResult.EnrollData;
import com.facishare.marketing.api.result.ticket.ConsumeTicketResult;
import com.facishare.marketing.api.result.ticket.QueryTicketListResult;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.api.service.ShareContentService;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.util.XssfExcelUtil;
import com.facishare.marketing.api.vo.conference.*;
import com.facishare.marketing.api.vo.conference.AddCampaignMembersObjVO.MemberObjDetail;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.contstant.campaign.CampaignConstants;
import com.facishare.marketing.common.enums.ActivitySignOrEnrollEnum;
import com.facishare.marketing.common.enums.ActivityStatusEnum;
import com.facishare.marketing.common.enums.ActivityTypeEnum;
import com.facishare.marketing.common.enums.CrmWechatWorkExternalUserFieldEnum;
import com.facishare.marketing.common.enums.ExecuteTaskDetailTypeEnum;
import com.facishare.marketing.common.enums.ImportErrorEnum;
import com.facishare.marketing.common.enums.ImportResultTypeEnum;
import com.facishare.marketing.common.enums.ImportTypeEnum;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.MarketingSceneType;
import com.facishare.marketing.common.enums.NoticeContentTypeEnum;
import com.facishare.marketing.common.enums.NoticeSendTypeEnum;
import com.facishare.marketing.common.enums.NoticeStatusEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.QueryAllCampaignQueryTypeEnum;
import com.facishare.marketing.common.enums.SaveCrmStatusEnum;
import com.facishare.marketing.common.enums.SignInByEnrollFieldEnum;
import com.facishare.marketing.common.enums.campaign.*;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceInvitationStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceInviteStatusEnum;
import com.facishare.marketing.common.enums.conference.ConferenceSmsType;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2FieldTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.qr.QRCodeTypeEnum;
import com.facishare.marketing.common.enums.sms.mw.SmsSceneTypeEnum;
import com.facishare.marketing.common.enums.ticket.CustomizeTicketTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadFactory;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeStatisticDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.manager.ConferenceDAOManager;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.pay.FsPayOrderDao;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.dto.ObjectIdWithMarketingUserIdAndPhoneDTO;
import com.facishare.marketing.provider.dao.ticket.CustomizeTicketDAO;
import com.facishare.marketing.provider.dto.TargetPhotoPathDTO;
import com.facishare.marketing.provider.dto.campaignEnroll.BaseCampaignEnrollData;
import com.facishare.marketing.provider.dto.campaignMergeData.*;
import com.facishare.marketing.provider.dto.conference.ConferenceIdNameDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceInvitationUserDTO;
import com.facishare.marketing.provider.dto.conference.ConferenceInviteEnrollDTO;
import com.facishare.marketing.provider.dto.conference.QueryOwerViewConferenceEnrollDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.ActivityEnrollDataEntity;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.ConferenceTagEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.ImportDataRecordEntity;
import com.facishare.marketing.provider.entity.MarketingWxServiceEntity;
import com.facishare.marketing.provider.entity.NoticeEntity;
import com.facishare.marketing.provider.entity.ObjectTagEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.conference.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity;
import com.facishare.marketing.provider.entity.pay.FsPayOrder;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.sms.mw.MwSmsTemplateEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity;
import com.facishare.marketing.provider.entity.ticket.CustomizeTicketReceiveEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.innerResult.UserRelationPartnerInfo;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.ActivityManager.ActivityQrCodeContainer;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager.WxUserData;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.conference.ImportManager;
import com.facishare.marketing.provider.manager.conference.ImportManager.ImportLineResultContainer;
import com.facishare.marketing.provider.manager.cusomerDev.CustomerCustomizeFormDataManager;
import com.facishare.marketing.provider.manager.cusomerDev.sbt.SbtFormDataObject;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.sms.mw.SmsSettingManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.remote.*;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.marketing.provider.util.ReadExcelUtil;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.arg.GetAllSubordinateEmployeesDtoArg;
import com.facishare.organization.api.model.employee.result.GetAllSubordinateEmployeesDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created  By zhoux 2019/07/16
 **/
@Slf4j
@Service("conferenceService")
public class ConferenceServiceImpl implements ConferenceService {
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private ActivityEnrollDataDAO activityEnrollDataDAO;
    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private MaterialTagManager materialTagManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private ConferenceInvitationDAO conferenceInvitationDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private ConferenceInviteParticipantDAO conferenceInviteParticipantDAO;
    @Autowired
    private QRCodeManager qrCodeManager;
    @Autowired
    private MwSmsTemplateDao templateDao;
    @Autowired
    private SmsSettingManager smsSettingManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private ConferenceTemplateDAO conferenceTemplateDAO;
    @Autowired
    private ConferenceNotificationSettingDAO notificationSettingDAO;
    @Autowired
    private NoticeDAO noticeDAO;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private ImportManager importManager;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private OutFileService outFileService;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private ConferenceDAOManager conferenceDAOManager;
    @Autowired
    private SpreadTaskManager spreadTaskManager;
    @Autowired
    private FsMessageManager fsMessageManager;
    @Autowired
    private PhotoDAO photoDAO;

    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ImportDataRecordDAO importDataRecordDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private ConferenceReviewEmployeeDAO conferenceReviewEmployeeDAO;
    @Autowired
    private NoticeManager noticeManager;
    @Autowired
    private ActivityService miniAppActivityService;
    @Autowired
    private CustomerCustomizeFormDataManager customerCustomizeFormDataManager;
    @Autowired
    private ConferenceTicketManagerDAO conferenceTicketManagerDAO;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private ConferenceInvitationUserDAO conferenceInvitationUserDAO;
    @Autowired
    MarketingActivityEmployeeStatisticDAO marketingActivityEmployeeStatisticDao;
    @Autowired
    private SpreadChannelManager spreadChannelManager;
    @Autowired
    private ShareContentService shareContentService;
    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private FsPayOrderDao fsPayOrderDao;
    @Autowired
    private ConferenceTagDAO conferenceTagDAO;

    @ReloadableProperty("host")
    private String host;

    @Value("${marketing_appid}")
    private String appId;

    @ReloadableProperty("default_conference_cover")
    private String defaultConferenceCover;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private CustomizeTicketManager customizeTicketManager;
    @Autowired
    private ShortUrlManager shortUrlManager;
    @Autowired
    private ConferenceUserGroupDAO conferenceUserGroupDAO;
    @Autowired
    private MarketingEventRemoteManager marketingEventRemoteManager;
    @Autowired
    private ImageCreator imageCreator;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private ExecuteTaskDetailManager executeTaskDetailManager;
    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private SafetyManagementManager safetyManagementManager;
    @Autowired
    private SceneTriggerManager sceneTriggerManager;
    @Autowired
    private ConferenceSignInJumpSettingDAO conferenceSignInJumpSettingDAO;
    @Autowired
    private ConferenceSignInSuccessSettingDAO conferenceSignInSuccessSettingDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ConferenceInvitationCommonSettingDAO conferenceInvitationCommonSettingDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private MarketingSceneService marketingSceneService;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private DingAuthService dingAuthService;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private CampaignDataManager campaignDataManager;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private HexagonService hexagonService;

    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;

    @Autowired
    private CustomizeTicketDAO customizeTicketDAO;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private DataPermissionService dataPermissionService;
    @Autowired
    private UserRelationManager userRelationManager;

    private final Gson gs = new Gson();
    @Override
    public Result<AddConferenceResult> addConference(AddConferenceVO vo) {
        if (vo == null || StringUtils.isBlank(vo.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try {
            ObjectData objectData = crmV2Manager.getDetail(vo.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), vo.getMarketingEventId());
            if (objectData == null) {
                return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_NOT_FOUND);
            }
            log.info("ConferenceServiceImpl addConference objectData={}", objectData);
            List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getObjectFieldDescribesList(vo.getEa(), CrmObjectApiNameEnum.MARKETING_EVENT);
            ActivityEntity conferenceEntity = new ActivityEntity();
            conferenceEntity.setId(UUIDUtil.getUUID());
            conferenceEntity.setEa(vo.getEa());
            conferenceEntity.setMarketingEventId(vo.getMarketingEventId());
            conferenceEntity.setTitle(objectData.getName());
            Long beginTime = objectData.getLong(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName());
            conferenceEntity.setStartTime(beginTime == null ? DateUtil.now() : DateUtil.fromTimestamp(beginTime));
            Long endTime = objectData.getLong(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName());
            conferenceEntity.setEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
            conferenceEntity.setEnrollEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
            conferenceEntity.setStatus(ActivityStatusEnum.UNPUBLISHED.getStatus());
            conferenceEntity.setLocation(objectData.getString(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName()));
            if (vo.getEa().equals("sbtjt888")){
                conferenceEntity.setLocation(objectData.getString(SbtFormDataObject.MARKETING_EVEN_LOCATION));
            }
            conferenceEntity.setCreateBy(vo.getFsUserId());
            conferenceEntity.setUpdateBy(vo.getFsUserId());
            conferenceEntity.setCreateTime(DateUtil.now());
            conferenceEntity.setUpdateTime(DateUtil.now());
            conferenceEntity.setType(ActivityTypeEnum.OFF_LING.type);

            List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
            Optional<CrmFieldResult> crmFieldResultOptional = crmFieldVOS.stream().filter(data -> data.getFieldName().equals(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName())).findFirst();
            conferenceEntity.setMarketingEventType(getMarketingEventTypeDesc(crmFieldResultOptional, objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName())));
            String conferenceId = conferenceDAOManager.addConference(conferenceEntity);
            if (conferenceId == null) {
                log.info("ConferenceServiceImpl conferenceDAO.create conferenceEntity failed, conferenceEntity:{}", conferenceEntity);
                return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_EXCEPTION);
            }
            AddConferenceResult result = new AddConferenceResult();
            result.setId(conferenceId);
            // 创建默认会议站点
            conferenceManager.createDefaultConferenceSite(conferenceEntity.getId());
            // 创建二维码
            ThreadPoolUtils.execute(new Runnable() {
                @Override
                public void run() {
                    activityManager.createActivityQrCode(conferenceId, conferenceEntity.getEa(), null, null);
                    activityManager.createActivitySignInQrCode(conferenceId, conferenceEntity.getEa(), null);
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.info("ConferenceServiceImpl query marketing_event failed, exception:{}", e.fillInStackTrace());
            return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_EXCEPTION);
        }
    }

    @Override
    public Result<CreateOrUpdateConferenceResult> create(CreateOrUpdateConferenceVO vo) {
        //查询市场活动是否存在
        int marketingEventCount = conferenceManager.queryMarketingEventCountByTitle(vo.getEa(), vo.getTitle());
        if (marketingEventCount != 0){
            log.info("ConferenceServiceImpl.create failed marketingEvent is exsit vo:{}", vo);
            return Result.newError(SHErrorCode.MARKETING_EVENT_EXIST);
        }

        //创建会议市场活动
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventResult = conferenceManager.createMarketingEventObj(vo);
        if (createMarketingEventResult == null || !createMarketingEventResult.isSuccess()){
            log.info("ConferenceServiceImpl.create failed create marketingEvent error return  null vo:{} createMarketingEventResult:{}", vo, createMarketingEventResult);
            return Result.newError(createMarketingEventResult.getCode(), createMarketingEventResult.getMessage());
        }
        String marketingEventId = (String)createMarketingEventResult.getData().getObjectData().get("_id");
        CreateOrUpdateConferenceResult result = new CreateOrUpdateConferenceResult();
        //创建会议
        try {
            ActivityEntity conferenceEntity = conferenceManager.createConference(vo, marketingEventId);
            String conferenceId = conferenceEntity.getId();
            //处理会议封面&二维码
            Result<Void> photoResult = conferenceManager.addConferencePhoto(conferenceId, vo.getCoverImagePath(), vo.getEa(), vo.getFsUserId());
            if (!photoResult.isSuccess()) {
                log.error("ConferenceServiceImpl.create add photo failed vo:{}", vo);
                conferenceManager.updateConferenceStatus(conferenceId, ActivityStatusEnum.DELETED.getStatus(), vo.getEa(), vo.getFsUserId());
                conferenceManager.bulkDelete(vo.getEa(), vo.getFsUserId(), Lists.newArrayList(marketingEventId));
                return Result.newError(photoResult.getErrCode(), photoResult.getErrMsg());
            }

            //会议详情
            conferenceEntity.setConferenceDetails(conferenceManager.getConferencePath(vo.getConferenceDetails()));

            //发布会议
            Result pulishResult = conferenceManager.updateConferenceStatus(conferenceEntity.getId(), ActivityStatusEnum.ENABLED.getStatus(), vo.getEa(), vo.getFsUserId());
            if (!pulishResult.isSuccess()) {
                log.error("ConferenceServiceImpl.updateConferenceStatus failed vo:{}", vo);
                conferenceManager.bulkDelete(vo.getEa(), vo.getFsUserId(), Lists.newArrayList(marketingEventId));
                return pulishResult;
            }
            conferenceDAOManager.updateConference(conferenceEntity);

            // 创建默认会议站点
            conferenceManager.createConferenceSite(conferenceEntity.getId(),vo.getMarketingTemplateId());

            ActivityEntity activityEntity = activityDAO.getById(conferenceId);
            if(activityEntity!=null&&StringUtils.isNotEmpty(activityEntity.getActivityDetailSiteId())){
                String eventLandingPage = host + "/proj/page/marketing-page?" + "marketingEventId=" + marketingEventId + "&ea=" + vo.getEa() + "&objectType=26&id=" + activityEntity.getActivityDetailSiteId()
                        +"&targetObjectType=13&targetObjectId=" + conferenceId + "&type=1";
                crmV2Manager.updateMarketingEvenObjLandingPage(vo.getEa(),  marketingEventId, vo.getFsUserId(),eventLandingPage);
            }

            ActivityEntity conferenceById = conferenceDAO.getConferenceById(conferenceEntity.getId());
            if(conferenceById != null && StringUtils.isNotEmpty(conferenceById.getActivityDetailSiteId())){
                if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getOriginalImageAPath()) && CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
                    HexagonPageEntity homePage = hexagonPageDAO.getHomePage(conferenceById.getActivityDetailSiteId());
                    if(homePage!=null){
                        for (PhotoCutOffset cutOffset : vo.getCutOffsetList()){
                            if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType()){
                                photoManager.addOrUpdatePhotoByCutOffset(vo.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                            }
                            if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()){
                                photoManager.addOrUpdatePhotoByCutOffset(vo.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                            }
                            if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()){
                                photoManager.addOrUpdatePhotoByCutOffset(vo.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                            }
                        }
                    }
                }
            }
            // 创建二维码
            ThreadPoolUtils.execute(() -> {
                activityManager.createActivityQrCode(conferenceId, conferenceEntity.getEa(), null, null);
                activityManager.createActivitySignInQrCode(conferenceId, conferenceEntity.getEa(), null);
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            result.setConferenceId(conferenceId);
            result.setMarketingEventId(marketingEventId);

            marketingSceneService.getOrCreateMarketingScene(vo.getEa(), vo.getFsUserId(), new GetOrCreateMarketingSceneArg(MarketingSceneType.CONFERENCE.getType(), conferenceId));
        } catch (Exception e) {
            log.info("create conference error vo:{} e:", vo, e);
            conferenceManager.bulkDelete(vo.getEa(), vo.getFsUserId(), Lists.newArrayList(marketingEventId));
            return Result.newError(SHErrorCode.CONFERENCE_MARKETING_EVENT_EXCEPTION);
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<CreateOrUpdateConferenceResult> update(CreateOrUpdateConferenceVO vo) {
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (conferenceEntity == null){
            log.error("ConferenceServiceImpl.update failed conference not exist ea:{} conferenceId:{}", vo.getEa(), vo.getConferenceId());
            return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
        }

        if (!StringUtils.equals(vo.getTitle(), conferenceEntity.getTitle())){
            //查询市场活动是否存在
            int marketingEventCount = conferenceManager.queryMarketingEventCountByTitle(vo.getEa(), vo.getTitle());
            if (marketingEventCount != 0){
                log.info("ConferenceServiceImpl.create failed marketingEvent is exsit vo:{} conferenceEntity.getTitle:", vo, conferenceEntity.getTitle());
                return Result.newError(SHErrorCode.MARKETING_EVENT_EXIST);
            }
        }

        //更新市场活动中的会议数据
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult>  updateMarketingEventObjRet = conferenceManager.updateConferenceToCrmMarketingEvent(conferenceEntity.getEa(),vo.getFsUserId(), conferenceEntity.getMarketingEventId(), vo);
        if (!updateMarketingEventObjRet.isSuccess()){
            log.info("ConferenceServiceImpl.update failed sync marketing failed vo:{} errorCode:{} errorMsg:{}", vo, updateMarketingEventObjRet.getCode(), updateMarketingEventObjRet.getMessage());
            return Result.newError(updateMarketingEventObjRet.getCode(), updateMarketingEventObjRet.getMessage());
        }

        //处理会议封面&二维码
        if (vo.isUpdateCoverImage()) {
            Result<Void> photoResult = conferenceManager.addConferencePhoto(vo.getConferenceId(), vo.getCoverImagePath(), vo.getEa(), vo.getFsUserId());
            if (!photoResult.isSuccess()) {
                log.error("ConferenceServiceImpl.update add photo failed vo:{}", vo);
                conferenceManager.updateConferenceStatus(vo.getConferenceId(), ActivityStatusEnum.DELETED.getStatus(), vo.getEa(), vo.getFsUserId());
                return Result.newError(photoResult.getErrCode(), photoResult.getErrMsg());
            }
        }

        if (vo.isUpdateConferenceDetails() && StringUtils.isNotBlank(vo.getConferenceDetails())) {
            // 设置会议详情
            conferenceEntity.setConferenceDetails(conferenceManager.getConferencePath(vo.getConferenceDetails()));
        }

        ActivityEntity updateEntity = conferenceManager.update(vo, conferenceEntity);
        conferenceDAOManager.updateConference(updateEntity);
        if(conferenceEntity != null && StringUtils.isNotEmpty(conferenceEntity.getActivityDetailSiteId())){
            if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getOriginalImageAPath()) && CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
                HexagonPageEntity homePage = hexagonPageDAO.getHomePage(conferenceEntity.getActivityDetailSiteId());
                if(homePage!=null){
                    for (PhotoCutOffset cutOffset : vo.getCutOffsetList()){
                        if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType()){
                            photoManager.addOrUpdatePhotoByCutOffset(vo.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                        }
                        if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()){
                            photoManager.addOrUpdatePhotoByCutOffset(vo.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                        }
                        if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()){
                            photoManager.addOrUpdatePhotoByCutOffset(vo.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                        }
                    }
                }
            }
        }
        CreateOrUpdateConferenceResult result = new CreateOrUpdateConferenceResult();
        result.setConferenceId(vo.getConferenceId());
        result.setMarketingEventId(conferenceEntity.getMarketingEventId());
        conferenceManager.resetConferenceIndexPage(conferenceEntity.getId(), false);
        return Result.newSuccess(result);
    }

    private boolean createQrPhoto(String id, String ea){
        try {
            JSONObject json = new JSONObject();
            json.put("conferenceId", id);
            json.put("byshare", 1); // 该值为前端识别使用
            CreateQRCodeInnerData data = new CreateQRCodeInnerData();
            data.setType(QRCodeTypeEnum.H5_ACTIVITY_DETAIL.getType());
            data.setValue(json.toJSONString());
            data.setEa(ea);
            QRCodeManager.CreateQRCodeResult qrCodeResult = qrCodeManager.createQRCode(data);
            return photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.H5_CONFERENCE_QR_CODE, id, qrCodeResult.getQrCodeApath(), qrCodeResult.getQrCodeApath());
        } catch (Exception e) {
            log.info("ConferenceServiceImpl createQrPhoto failed, id={},exception:{}", id, e.fillInStackTrace());
            return false;
        }
    }

    @Override
    public Result<PageResult<QueryConferenceListResult>> queryConferenceAppList(QueryConferenceListVO arg) {

        // 移动端接口，筛选条件不互斥
        if (StringUtils.isBlank(arg.getMenuId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String ea = arg.getEa();
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilter("is_mobile_display", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("0"));
        query.addFilter("life_status", FilterOperatorEnum.EQ.getValue(), Collections.singletonList("normal"));
        //添加数据权限筛选条件
        Result<DataPermissionResult> dataPermissionResult = dataPermissionService.getDataPermission(ea, arg.getFsUserId());
        if (dataPermissionResult.isSuccess() && dataPermissionResult.getData() != null && dataPermissionResult.getData().isStatus()) {
            List<Integer> dataDepartments = dataPermissionResult.getData().getDataDepartments();
            if (CollectionUtils.isNotEmpty(dataDepartments)) {
                query.addFilter("data_own_organization", "IN", dataDepartments.stream().map(String::valueOf).collect(Collectors.toList()));
            }
        }
        filterData.setQuery(query);

        Result<PaasObjectRuleVO>  paasObjectRuleResult = appMenuTemplateService.getPaasObjectRule(ea, arg.getMenuId(), ObjectTypeEnum.ACTIVITY.getType());

        PageResult<QueryConferenceListResult> pageResult = new PageResult<>();
        List<QueryConferenceListResult> queryConferenceListResult = Lists.newArrayList();
        pageResult.setResult(queryConferenceListResult);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        if (!paasObjectRuleResult.isSuccess()) {
            log.info("ConferenceServiceImpl.queryAppConferenceList failed paasObjectRuleResult not success arg:{} result: {}", arg, paasObjectRuleResult);
            return Result.newSuccess(pageResult);
        }
        int flowStatusSize = CollectionUtils.isNotEmpty(arg.getFlowStatusList()) ? arg.getFlowStatusList().size() : 0;
        ListBriefMarketingEventsArg marketingEventArg = new ListBriefMarketingEventsArg();
        marketingEventArg.setEventType(MarketingEventEnum.MEETING_SALES.getEventType());
        marketingEventArg.setName(arg.getKeyword());
        ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.BEGIN_TIME, false);
        marketingEventArg.setOrderByList(Lists.newArrayList(orderBy));

        //根据移动端的筛选状态，设置会议的查询状态
        if (CollectionUtils.isNotEmpty(arg.getFlowStatusList())) {
            List<String> timeList = Lists.newArrayList(String.valueOf(System.currentTimeMillis()));
            if (arg.getFlowStatusList().get(0) == 2) {
                //未开始
                query.addFilter(MarketingEventFieldContants.BEGIN_TIME, FilterOperatorEnum.GTE.getValue(), timeList);
            } else if (arg.getFlowStatusList().get(0) == 3) {
                //已结束
                query.addFilter(MarketingEventFieldContants.END_TIME, FilterOperatorEnum.LTE.getValue(), timeList);
            } else {
                //进行中
                query.addFilter(MarketingEventFieldContants.BEGIN_TIME, FilterOperatorEnum.LTE.getValue(), timeList);
                query.addFilter(MarketingEventFieldContants.END_TIME, FilterOperatorEnum.GTE.getValue(), timeList);
            }
        }
        // 默认走管理员权限
        int queryFsUserId = SuperUserConstants.USER_ID;
        PaasObjectRuleVO paasObjectRuleVO = paasObjectRuleResult.getData();
        Page<String> tagPage = null;
        Integer totalCount = null;
        if (paasObjectRuleVO.getAppMenuTagVO() != null) {
            // 如果传了关键词和状态，标签不用分页，在paas在分页
            AppMenuTagVO appMenuTagVO = paasObjectRuleVO.getAppMenuTagVO();
            if (StringUtils.isBlank(arg.getKeyword()) && CollectionUtils.isEmpty(arg.getFlowStatusList())) {
                tagPage = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
            }
            List<String> idList;
            int type = appMenuTagVO.getTagOperator();
            if (type == 1) {
                idList = materialTagRelationDao.queryByAnyTags(ea, appMenuTagVO.getTagIdList(), Lists.newArrayList(ObjectTypeEnum.CONFERENCE.getType()), tagPage);
            } else {
                idList = materialTagRelationDao.queryByAllTags(ea, appMenuTagVO.getTagIdList(), Lists.newArrayList(ObjectTypeEnum.CONFERENCE.getType()), tagPage);
            }
            if (CollectionUtils.isEmpty(idList)) {
                return Result.newSuccess(pageResult);
            }
            totalCount = tagPage == null ? null : tagPage.getTotalNum();
            marketingEventArg.setMarketingEventIds(idList);
        } else if (AppMenuAccessibleRuleEnum.PAAS_OBJECT_ACCESSIBLE.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            queryFsUserId = arg.getFsUserId();
        } else if (AppMenuAccessibleRuleEnum.OBJECT_FILTER.getType().equals(paasObjectRuleVO.getObjectAccessibleRule())) {
            // 对象条件
            query.getFilters().addAll(paasObjectRuleVO.getFilters());
        }
        marketingEventArg.setFilterData(filterData);
        PageArg pageArg = null;
        if (tagPage != null) {
            // 已经通过标签分页了，这里就分页了
            pageArg = new PageArg(1, arg.getPageSize());
        } else {
            pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
        }
        com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResults =
                marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(ea), queryFsUserId, marketingEventArg, pageArg);

        if (marketingEventsBriefResults == null || CollectionUtils.isEmpty(marketingEventsBriefResults.getData())) {
            return Result.newSuccess(pageResult);
        }
        List<String> marketingEventIdList = Lists.newArrayList();
        Map<String, String> lifeStatusMap = Maps.newHashMap();

        for (MarketingEventsBriefResult marketingEventsBriefResult : marketingEventsBriefResults.getData()) {
            String id = marketingEventsBriefResult.getId();
            lifeStatusMap.put(id, marketingEventsBriefResult.getLifeStatus());
            marketingEventIdList.add(id);
        }
        List<ActivityEntity> conferenceEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIdList);
        //按照以前的逻辑，只有 flowStatusSize <= 1 才会触发这个 具体原因不详
        if (flowStatusSize <= 1) {
            conferenceEntityList = reGetConferenceIfNotExist(arg, conferenceEntityList, marketingEventsBriefResults);
        }

        queryConferenceListResult = buildConferenceListResult(arg, conferenceEntityList, lifeStatusMap);
        pageResult.setResult(queryConferenceListResult);
        totalCount = totalCount == null ? marketingEventsBriefResults.getTotalCount() : totalCount;
        pageResult.setTotalCount(totalCount);
        return Result.newSuccess(pageResult);
    }

    // 现在这里接口只有web端调用了！  web端搜索的时候，标签和关键词筛选等互斥，而移动端要求不互斥
    @Override
    public Result<PageResult<QueryConferenceListResult>> queryConferenceList(QueryConferenceListVO vo) {
        PageResult<QueryConferenceListResult> pageResult = new PageResult<>();
        List<QueryConferenceListResult> queryConferenceListResult = Lists.newArrayList();
        pageResult.setResult(queryConferenceListResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        if (CollectionUtils.isEmpty(vo.getActivityStatus())) {
            vo.setActivityStatus(null);
        }

        if (vo.getIsShowSpread() == null){
            vo.setIsShowSpread(false);
        }
        Integer fsUserId = vo.getFsUserId();
        List<ActivityEntity> conferenceEntityList = null;
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        Map<String, String> lifeStatusMap = Maps.newHashMap();
        int flowStatusSize = CollectionUtils.isNotEmpty(vo.getFlowStatusList()) ? vo.getFlowStatusList().size() : 0;
        if (vo.getMaterialTagFilter() != null && vo.getMaterialTagFilter().checkValid()) {
            // 内容标签过滤处理
            Integer type = vo.getMaterialTagFilter().getType();
            List<String> materialTagIds = vo.getMaterialTagFilter().getMaterialTagIds();
            List<String> ids;
            if (type == 1) {
                ids = materialTagRelationDao.queryByAnyTags(vo.getEa(), materialTagIds, Lists.newArrayList(ObjectTypeEnum.CONFERENCE.getType()), page);
            } else {
                ids = materialTagRelationDao.queryByAllTags(vo.getEa(), materialTagIds, Lists.newArrayList(ObjectTypeEnum.CONFERENCE.getType()), page);
            }
            if (CollectionUtils.isEmpty(ids)) {
                return Result.newSuccess(pageResult);
            }
            pageResult.setTotalCount(page.getTotalNum());
            ListBriefMarketingEventsArg batchGetMktEventArg = new ListBriefMarketingEventsArg();
            batchGetMktEventArg.setOrderByList(Lists.newArrayList(new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false)));
            batchGetMktEventArg.setMarketingEventIds(ids);
            batchGetMktEventArg.setEventType(MarketingEventEnum.MEETING_SALES.getEventType());
            List<MarketingEventsBriefResult> marketingEventsBriefResults = marketingEventManager.listMarketingEventsV2(eieaConverter.enterpriseAccountToId(vo.getEa()), fsUserId, batchGetMktEventArg);
            if (CollectionUtils.isEmpty(marketingEventsBriefResults)) {
                return Result.newSuccess(pageResult);
            }
            List<String> marketingEventIds = marketingEventsBriefResults.stream().map(event -> event.getId()).collect(Collectors.toList());
            marketingEventIds.add(vo.getMarketingEventId());
            if (CollectionUtils.isEmpty(marketingEventIds)) {
                return Result.newSuccess(pageResult);
            }
            Page page2 = new Page(1, vo.getPageSize(), true);
            conferenceEntityList = conferenceDAO.pageActivityEnrollsByEaAndId(vo.getEa(), null, null, marketingEventIds, page2, null);
        } else {
            if (flowStatusSize <= 1) {
                //web端&移动端会议状态选择一个状态：按照CRM数据权限拉取，支持flowStatusSize > 1是兼容老的小程序版本
                ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
                PageArg pageArg = new PageArg(vo.getPageNum(), vo.getPageSize());
                arg.setEventType(MarketingEventEnum.MEETING_SALES.getEventType());
                arg.setName(vo.getKeyword());
                ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.BEGIN_TIME, false);
                arg.setOrderByList(Lists.newArrayList(orderBy));
                arg.setFilterData(vo.getFilterData());

                //根据移动端的筛选状态，设置会议的查询状态
                if (CollectionUtils.isNotEmpty(vo.getFlowStatusList())){
                    List<String> timeList = Lists.newArrayList(String.valueOf(System.currentTimeMillis()));
                    if (vo.getFlowStatusList().get(0) == 2){
                        //未开始
                        arg.getFilterData().getQuery().addFilter(MarketingEventFieldContants.BEGIN_TIME, FilterOperatorEnum.GTE.getValue(), timeList);
                    }else if (vo.getFlowStatusList().get(0) == 3){
                        //已结束
                        arg.getFilterData().getQuery().addFilter(MarketingEventFieldContants.END_TIME,  FilterOperatorEnum.LTE.getValue(), timeList);
                    }else {
                        //进行中
                        arg.getFilterData().getQuery().addFilter(MarketingEventFieldContants.BEGIN_TIME, FilterOperatorEnum.LTE.getValue(), timeList);
                        arg.getFilterData().getQuery().addFilter(MarketingEventFieldContants.END_TIME,  FilterOperatorEnum.GTE.getValue(), timeList);
                    }
                }
                com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResults =
                        marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(vo.getEa()), fsUserId, arg, pageArg);
                if (marketingEventsBriefResults == null || CollectionUtils.isEmpty(marketingEventsBriefResults.getData())) {
                    return Result.newSuccess(pageResult);
                }
                List<String> marketingEventIds = marketingEventsBriefResults.getData().stream().map(event -> event.getId()).collect(Collectors.toList());
                lifeStatusMap = marketingEventsBriefResults.getData().stream().collect(Collectors.toMap(MarketingEventsBriefResult::getId,MarketingEventsBriefResult::getLifeStatus,(v1,v2)->v1));
                pageResult.setTotalCount(marketingEventsBriefResults.getTotalCount());

                conferenceEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(vo.getEa(),marketingEventIds);
                conferenceEntityList = reGetConferenceIfNotExist(vo, conferenceEntityList, marketingEventsBriefResults);
            }else{
                if (vo.getFilterData() != null && vo.getFilterData().getQuery() != null) {
                    //筛选出市场活动id
                    ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
                    arg.setEventType(MarketingEventEnum.MEETING_SALES.getEventType());
                    ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.BEGIN_TIME, false);
                    arg.setOrderByList(Lists.newArrayList(orderBy));
                    arg.setFilterData(vo.getFilterData());
                    List<MarketingEventsBriefResult> marketingEventsBriefResults = marketingEventManager.listMarketingEvents(eieaConverter.enterpriseAccountToId(vo.getEa()), fsUserId, arg);
                    if (CollectionUtils.isEmpty(marketingEventsBriefResults)) {
                        return Result.newSuccess(pageResult);
                    }
                    List<String> marketingEventIds = marketingEventsBriefResults.stream().map(event -> event.getId()).collect(Collectors.toList());
                    marketingEventIds.add(vo.getMarketingEventId());
                    if (CollectionUtils.isEmpty(marketingEventIds)) {
                        return Result.newSuccess(pageResult);
                    }
                    conferenceEntityList = conferenceDAO.pageActivityEnrollsByEaAndId(vo.getEa(), vo.getKeyword(), vo.getActivityStatus(), marketingEventIds, page, vo.getFlowStatusList());
                } else {
                    List<String> eventIds = null;
                    if (StringUtils.isNotEmpty(vo.getMarketingEventId())) {
                        eventIds = new ArrayList<>();
                        eventIds.add(vo.getMarketingEventId());
                    }
                    conferenceEntityList = conferenceDAO.pageActivityEnrollsByEaAndId(vo.getEa(), vo.getKeyword(), vo.getActivityStatus(), eventIds, page, vo.getFlowStatusList());
                }
                pageResult.setTotalCount(page.getTotalNum());
            }
        }

        if (CollectionUtils.isNotEmpty(conferenceEntityList)){
            queryConferenceListResult = buildConferenceListResult(vo, conferenceEntityList, lifeStatusMap);
            pageResult.setResult(queryConferenceListResult);
        }

        return Result.newSuccess(pageResult);
    }

    private List<ActivityEntity> reGetConferenceIfNotExist(QueryConferenceListVO vo, List<ActivityEntity> conferenceEntityList, com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResults) {
        List<MarketingEventsBriefResult> notExistMarketingEventList = Lists.newArrayList();
        List<String> marketingEventIds = marketingEventsBriefResults.getData().stream().map(MarketingEventsBriefResult::getId).collect(Collectors.toList());
        Map<String, ActivityEntity> marketingEventIdActivityEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(conferenceEntityList)){
            marketingEventIdActivityEntityMap = conferenceEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, v->v, (v1, v2)->v1));
        }
        for (MarketingEventsBriefResult marketingEventsBriefResult : marketingEventsBriefResults.getData()){
            if (marketingEventIdActivityEntityMap.get(marketingEventsBriefResult.getId()) == null){
                notExistMarketingEventList.add(marketingEventsBriefResult);
            }
        }
        if (CollectionUtils.isNotEmpty(notExistMarketingEventList)) {
            initCrmOldConference(vo.getEa(), notExistMarketingEventList);
            //重新拉取会议列表
            conferenceEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(vo.getEa(), marketingEventIds);
            for (ActivityEntity activityEntity : conferenceEntityList){
                marketingEventIdActivityEntityMap.putIfAbsent(activityEntity.getMarketingEventId(), activityEntity);
            }
        }
        List<ActivityEntity> newConferenceList = Lists.newArrayList();
        for (MarketingEventsBriefResult marketingEventsBriefResult : marketingEventsBriefResults.getData()){
            if (marketingEventIdActivityEntityMap.get(marketingEventsBriefResult.getId()) != null) {
                newConferenceList.add(marketingEventIdActivityEntityMap.get(marketingEventsBriefResult.getId()));
            }
        }
        conferenceEntityList = newConferenceList;
        return conferenceEntityList;
    }

    private List<QueryConferenceListResult> buildConferenceListResult(QueryConferenceListVO vo, List<ActivityEntity> conferenceEntityList, Map<String, String> lifeStatusMap) {

        List<QueryConferenceListResult> queryConferenceListResult = Lists.newArrayList();

        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> marketingActivityObjectInfoMap = Maps.newHashMap();
        if (vo.getNeedMarketingActivityResult()) {
            Map<String, Integer> materielMap = Maps.newHashMap();
            conferenceEntityList.forEach(value -> materielMap.put(value.getId(), ObjectTypeEnum.ACTIVITY.getType()));
            marketingActivityObjectInfoMap = marketingActivityManager.getActivityIdsByObject(materielMap, vo.getEa(), vo.getFsUserId());
        }

        List<String> marketingEventIds = conferenceEntityList.stream().filter(Objects::nonNull).map(ActivityEntity::getMarketingEventId).collect(Collectors.toList());
        //     Map<String, CampaignStatisticDTO> campaignStatisticMap = Maps.newHashMap();
        Map<String, EnrollCountDTO> enrollCountDTOMap = null;
        Map<String, SignInCountDTO> signInCountDTOMap = null;
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            List<EnrollCountDTO> enrollCountDTOList = campaignMergeDataDAO.getEnrollCountByMarketingEventIds(vo.getEa(), marketingEventIds);
            List<SignInCountDTO> signInCountDTOList = campaignMergeDataDAO.getSignInCountByMarketingEventIds(vo.getEa(), marketingEventIds);
            if (CollectionUtils.isNotEmpty(enrollCountDTOList)){
                enrollCountDTOMap = enrollCountDTOList.stream().collect(Collectors.toMap(EnrollCountDTO::getMarketingEventId, data -> data, (v1, v2) -> v2));
            }
            if (CollectionUtils.isNotEmpty(signInCountDTOList)){
                signInCountDTOMap = signInCountDTOList.stream().collect(Collectors.toMap(SignInCountDTO::getMarketingEventId, data -> data, (v1, v2) -> v2));
            }
        }

        Map<String, PhotoEntity> shareMiniappCoverEntityMap = null;
        Map<String, PhotoEntity> shareH5CoverEntityMap = null;
        Map<String, PhotoEntity> shareOrdinaryEntityMap = null;
        Map<String, HexagonPageEntity> hexagonPageEntityMap = null;
        Map<String, HexagonPageEntity> hexagonSiteEntityMap = null;
        Map<String, CustomizeFormDataEntity> customizeFormDataEntityMap = customizeFormDataManager.getBindFormDataByObjects(vo.getEa(), conferenceEntityList.stream().map(ActivityEntity::getId).collect(Collectors.toList()));
        List<String> hexagonIds = conferenceEntityList.stream().map(ActivityEntity::getActivityDetailSiteId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hexagonIds)) {
            List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getHomePageByIds(vo.getEa(), hexagonIds);
            hexagonPageEntityMap = hexagonPageEntityList.stream().collect(Collectors.toMap(HexagonPageEntity::getId, v -> v, (v1, v2) -> v1));
            hexagonSiteEntityMap = hexagonPageEntityList.stream().collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, v -> v, (v1, v2) -> v1));
            //批量处理头像
            if (CollectionUtils.isNotEmpty(hexagonPageEntityMap.keySet())) {
                shareMiniappCoverEntityMap = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), Lists.newArrayList(hexagonPageEntityMap.keySet()));
                shareH5CoverEntityMap = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), Lists.newArrayList(hexagonPageEntityMap.keySet()));
                shareOrdinaryEntityMap = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), Lists.newArrayList(hexagonPageEntityMap.keySet()));
            }
        }

        // 查询标签
        List<String> objectIds = conferenceEntityList.stream().map(ActivityEntity::getMarketingEventId).collect(Collectors.toList());
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(objectIds, ObjectTypeEnum.CONFERENCE.getType());
        for (ActivityEntity conferenceEntity : conferenceEntityList) {
            QueryConferenceListResult queryConference = new QueryConferenceListResult();
            queryConference.setConferenceId(conferenceEntity.getId());
            queryConference.setMarketingEventId(conferenceEntity.getMarketingEventId());
            queryConference.setStartTime(conferenceEntity.getStartTime() != null ? conferenceEntity.getStartTime().getTime() : null);
            queryConference.setEndTime(conferenceEntity.getEndTime() != null ? conferenceEntity.getEndTime().getTime() : null);
            queryConference.setType(conferenceEntity.getType());
            queryConference.setLocation(conferenceEntity.getLocation());
            queryConference.setStatus(conferenceEntity.getStatus());
            queryConference.setFlowStatus(conferenceManager.getConferenceTimeFlowStatus(conferenceEntity));
            queryConference.setTitle(conferenceEntity.getTitle());
            queryConference.setActivityDetailSiteId(conferenceEntity.getActivityDetailSiteId());
            queryConference.setShowActivityList(conferenceEntity.getShowAcitivityList());
            queryConference.setLifeStatus(lifeStatusMap.get(conferenceEntity.getMarketingEventId()));
            // 设置营销活动
            List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = marketingActivityObjectInfoMap.get(conferenceEntity.getId());
            if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectInfoDTO)) {
                queryConference.setMarketingActivityId(objectInfoDTO.get(0).getId());
                queryConference.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                queryConference.setMarketingActivityCount(objectInfoDTO.size());
            }
            if (enrollCountDTOMap != null && enrollCountDTOMap.get(conferenceEntity.getMarketingEventId()) != null){
                queryConference.setEnrollCount(enrollCountDTOMap.get(conferenceEntity.getMarketingEventId()).getEnrollCount());
            }
            if (signInCountDTOMap != null && signInCountDTOMap.get(conferenceEntity.getMarketingEventId()) != null){
                queryConference.setSignInCount(signInCountDTOMap.get(conferenceEntity.getMarketingEventId()).getSignInCount());
            }

            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataEntityMap.get(conferenceEntity.getId());
            if (customizeFormDataEntity != null) {
                queryConference.setFormId(customizeFormDataEntity.getId());
            }
            if(queryConference != null && org.apache.commons.lang3.StringUtils.isNotBlank(queryConference.getActivityDetailSiteId()) && hexagonPageEntityMap != null && hexagonSiteEntityMap != null){
                // 获取裁剪封面图
                HexagonPageEntity homePage = hexagonSiteEntityMap.get(queryConference.getActivityDetailSiteId());
                if (shareMiniappCoverEntityMap != null) {
                    PhotoEntity coverCutMiniAppPhotoEntity = shareMiniappCoverEntityMap.get(homePage.getId());
                    if (coverCutMiniAppPhotoEntity != null) {
                        queryConference.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                    }
                }
                if (shareH5CoverEntityMap != null ){
                    PhotoEntity coverCutH5PhotoEntity = shareH5CoverEntityMap.get(homePage.getId());
                    if (coverCutH5PhotoEntity != null) {
                        queryConference.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                    }
                }
                if (shareOrdinaryEntityMap != null) {
                    PhotoEntity coverCutOrdinaryPhotoEntity = shareOrdinaryEntityMap.get(homePage.getId());
                    if (coverCutOrdinaryPhotoEntity != null) {
                        queryConference.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                        //返回原图
                        queryConference.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                    }
                }
                queryConference.setShareTitle(homePage.getShareTitle());
                queryConference.setShareDesc(homePage.getShareDesc());
            }
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(conferenceEntity.getMarketingEventId());
            if (CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                queryConference.setMaterialTags(collect);
            }
            queryConferenceListResult.add(queryConference);
        }

        //设置报名人数&签到人数
        List<String> conferenceIds = conferenceEntityList.stream().map(ActivityEntity::getId).collect(Collectors.toList());

        //设置会议封面
        Map<String, TargetPhotoPathDTO> conferencePhotoPathMap = new HashMap<>();
        Map<String, String> pathUrlMap = null;
        List<TargetPhotoPathDTO> conferencePhotos = photoManager.listPathByTargetTypeAndIds(conferenceIds, PhotoTargetTypeEnum.ACTIVITY_COVER.getType());
        if (CollectionUtils.isNotEmpty(conferencePhotos)){
            Set<String> pathSet = new HashSet<>();
            for (TargetPhotoPathDTO dto : conferencePhotos){
                pathSet.add(dto.getPath());
                pathSet.add(dto.getThumbnailPath());
            }
            pathUrlMap = fileV2Manager.batchGetUrlByPath(new ArrayList<>(pathSet), vo.getEa(), false);
            if (pathUrlMap != null) {
                for (TargetPhotoPathDTO dto : conferencePhotos) {
                    if (StringUtils.isNotBlank(dto.getPath())) {
                        String url = pathUrlMap.get(dto.getPath());
                        dto.setUrl(url);
                    }
                    if (StringUtils.isNotEmpty(dto.getThumbnailPath())) {
                        String thumbnailUrl = pathUrlMap.get(dto.getThumbnailPath());
                        dto.setThumbnailUrl(thumbnailUrl);
                    }
                    conferencePhotoPathMap.putIfAbsent(dto.getTargetId(), dto);
                }
            }
        }
        String defaultUrl = null;
        if (CollectionUtils.isEmpty(conferencePhotos) || conferencePhotos.size() != queryConferenceListResult.size()){
            //需要默认封面
            defaultUrl = fileV2Manager.getUrlByPath(defaultConferenceCover, vo.getEa(), false);
        }
        for (QueryConferenceListResult queryConference : queryConferenceListResult){
            TargetPhotoPathDTO photoDTO = conferencePhotoPathMap.get(queryConference.getConferenceId());
            if (photoDTO != null){
                queryConference.setUrl(photoDTO.getUrl());
                queryConference.setAPath(photoDTO.getPath());
                queryConference.setThumbnailUrl(photoDTO.getThumbnailUrl());
                queryConference.setThumbnailAPath(photoDTO.getThumbnailPath());
            }else {
                queryConference.setAPath(defaultConferenceCover);
                queryConference.setThumbnailAPath(defaultConferenceCover);
                queryConference.setUrl(defaultUrl);
                queryConference.setThumbnailUrl(defaultUrl);
            }
        }
        return queryConferenceListResult;
    }

    private void initCrmOldConference(String ea, List<MarketingEventsBriefResult> marketingEventsBriefResults){
        if (CollectionUtils.isEmpty(marketingEventsBriefResults)){
            return;
        }

        List<ActivityEntity> conferenceList = Lists.newArrayList();
        for (MarketingEventsBriefResult  marketingEventsBriefResult : marketingEventsBriefResults){
            ActivityEntity conferenceEntity = new ActivityEntity();
            conferenceEntity.setId(UUIDUtil.getUUID());
            conferenceEntity.setEa(ea);
            conferenceEntity.setMarketingEventId(marketingEventsBriefResult.getId());
            conferenceEntity.setTitle(marketingEventsBriefResult.getName());
            Long beginTime = marketingEventsBriefResult.getBeginTime();
            conferenceEntity.setStartTime(beginTime == null ? DateUtil.now() : DateUtil.fromTimestamp(beginTime));
            Long endTime = marketingEventsBriefResult.getEndTime();
            conferenceEntity.setEndTime(endTime == null ? DateUtil.now() : DateUtil.fromTimestamp(endTime));
            conferenceEntity.setStatus(ActivityStatusEnum.ENABLED.getStatus());
            conferenceEntity.setLocation(marketingEventsBriefResult.getLocation());
            conferenceEntity.setCreateBy(marketingEventsBriefResult.getCreateBy());
            conferenceEntity.setUpdateBy(marketingEventsBriefResult.getCreateBy());
            Long createTime = marketingEventsBriefResult.getCreateTime();
            conferenceEntity.setCreateTime(createTime == null ? DateUtil.now() : DateUtil.fromTimestamp(createTime));
            conferenceEntity.setUpdateTime(conferenceEntity.getCreateTime());
            conferenceEntity.setType(ActivityTypeEnum.OFF_LING.type);
            conferenceEntity.setMarketingEventType(marketingEventsBriefResult.getEventType());
            conferenceList.add(conferenceEntity);
        }

        if (CollectionUtils.isNotEmpty(conferenceList)){
            log.info("sync old conference from crm count:{} data:{}", conferenceList.size(), conferenceList);
            conferenceDAOManager.batchAddConference(conferenceList);
            // 创建二维码
            ThreadPoolUtils.execute(new Runnable() {
                @Override
                public void run() {
                    for (ActivityEntity conferenceEntity : conferenceList) {
                        activityManager.createActivityQrCode(conferenceEntity.getId(), conferenceEntity.getEa(), null, null);
                        activityManager.createActivitySignInQrCode(conferenceEntity.getId(), conferenceEntity.getEa(), null);
                        conferenceManager.createConferenceSite(conferenceEntity.getId(),null);
                    }
                }
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
    }

    @Override
    public Result updateConferenceDetail(UpdateConferenceDetailVO vo) {
        if (vo.isUpdateCoverImage() && (StringUtils.isBlank(vo.getCoverImageTAPath()) || !vo.getCoverImageTAPath().startsWith("TA_"))) {
            return Result.newError(SHErrorCode.CONFERENCE_IMAGE_PARAMS_ERROR);
        }
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(vo.getId());
        if (conferenceEntity == null) {
            log.info("ConferenceServiceImpl updateConferenceDetail conferenceEntity failed, vo:{}", vo);
            return Result.newError(SHErrorCode.CONFERENCE_UPDATE_NOT_FOUND);
        }

        //更新会议基本信息
        Result updateBaseResult = conferenceManager.updateConferenceBaseInfo(vo,conferenceEntity);
        if (!updateBaseResult.isSuccess()){
            log.error("ConferenceServiceImpl.updateConferenceBaseInfo failed vo:{}", vo);
            return updateBaseResult;
        }

        //更新报名信息
        ConferenceEnrollSettingVO conferenceEnrollSettingVO  = BeanUtil.copy(vo,ConferenceEnrollSettingVO.class);
        Result updateEnrollResult = conferenceManager.updateConferenceEnrollSetting(conferenceEnrollSettingVO, conferenceEntity);
        if (!updateEnrollResult.isSuccess()){
            log.error("ConferenceServiceImpl.updateConferenceEnrollSetting failed vo:{}", vo);
            return updateEnrollResult;
        }

        //发布会议
        Result pulishResult = conferenceManager.updateConferenceStatus(vo.getId(), ActivityStatusEnum.ENABLED.getStatus(), vo.getEa(), vo.getFsUserId());
        if (!pulishResult.isSuccess()){
            log.error("ConferenceServiceImpl.updateConferenceStatus failed vo:{}", vo);
            return pulishResult;
        }

        //更新市场活动中的会议数据
    //    conferenceManager.updateConferenceToCrmMarketingEvent(conferenceEntity.getEa(),vo.getFsUserId(), conferenceEntity.getMarketingEventId(), vo);
        return Result.newSuccess();
    }

    @Override
    public Result updateConferenceStatus(UpdateConferenceStatusVO vo) {
        return conferenceManager.updateConferenceStatus(vo.getId(), vo.getStatus(), vo.getEa(), vo.getFsUserId());
    }

    @Override
    public Result<QueryConferenceDetailResult> queryConferenceDetail(QueryConferenceDetailVO vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(vo.getId());
        if (conferenceEntity == null) {
            // id有可能是营销通的id,也可能是市场活动id，这里需要在查询一次
            conferenceEntity = conferenceDAO.getConferenceByMarketingEventId(vo.getId(), vo.getEa());
            if (conferenceEntity == null) {
                log.info("ConferenceServiceImpl queryConferenceDetail conferenceEntity not found, vo:{}", vo);
                return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
            }
        }
        // 校验市场活动查看权限
        if (vo.isCheckMarketingEvenAuth()) {
            boolean checkResult = activityManager.checkViewMarketingEvenObjectAuth(vo.getEa(), vo.getFsUserId(), conferenceEntity.getMarketingEventId());
            if (!checkResult) {
                return Result.newError(SHErrorCode.CONFERENCE_NOT_MARKETING_VIEW_AUTH);
            }
        }
        QueryConferenceDetailResult detailResult = new QueryConferenceDetailResult();
        detailResult.setId(conferenceEntity.getId());
        detailResult.setMarketingEventId(conferenceEntity.getMarketingEventId());
        detailResult.setTitle(conferenceEntity.getTitle());
        detailResult.setConferenceDetails(StringUtils.isNotBlank(conferenceEntity.getConferenceDetails()) ? conferenceManager.getConferenceDetailsByPath(conferenceEntity.getConferenceDetails()) : null);
        detailResult.setMarketingEventType(conferenceEntity.getMarketingEventType());
        detailResult.setEndTime(conferenceEntity.getEndTime() == null ? null : conferenceEntity.getEndTime().getTime());
        detailResult.setEnrollButton(conferenceEntity.getEnrollButton());
        detailResult.setEnrollEndTime(conferenceEntity.getEnrollEndTime() == null ? null : conferenceEntity.getEnrollEndTime().getTime());
        detailResult.setEnrollReview(conferenceEntity.getEnrollReview());
        detailResult.setStartTime(conferenceEntity.getStartTime() == null ? null : conferenceEntity.getStartTime().getTime());
        detailResult.setLocation(conferenceEntity.getLocation());
        detailResult.setScale(conferenceEntity.getScale());
        detailResult.setStatus(conferenceEntity.getStatus());
        detailResult.setType(conferenceEntity.getType());
        detailResult.setCreator(conferenceEntity.getCreateBy());
        detailResult.setMapAddress(conferenceEntity.getMapAddress());
        detailResult.setMapLocation(conferenceEntity.getMapLocation());
        //如果开启报名审核,则获取审核提示
        if (conferenceEntity.getEnrollReview() != null) {
            detailResult.setEnrollPendingReviewTip(conferenceEntity.getEnrollPendingReviewTip());
            detailResult.setEnrollReviewFailureTip(conferenceEntity.getEnrollReviewFailureTip());
            if (conferenceEntity.getEnrollReview()) {
                detailResult.setEnrollPendingReviewTip(StringUtils.isNotBlank(conferenceEntity.getEnrollPendingReviewTip()) ? conferenceEntity.getEnrollPendingReviewTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_572));
                detailResult.setEnrollReviewFailureTip(StringUtils.isNotBlank(conferenceEntity.getEnrollReviewFailureTip()) ? conferenceEntity.getEnrollReviewFailureTip() : I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_573));
            }
        }
        // 查询会议微页面表单
        detailResult.setConferenceCompleteMapping(true);
        // 获取会议绑定表单
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(conferenceEntity.getEa(), conferenceEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity != null) {
            detailResult.setFormId(customizeFormDataEntity.getId());
            detailResult.setFormName(customizeFormDataEntity.getFormHeadSetting() == null ? null : customizeFormDataEntity.getFormHeadSetting().getName());
            //detailResult.setConferenceSiteBindForm(customizeFormDataEntity.getConferenceSiteForm());
            detailResult.setConferenceCompleteMapping(CollectionUtils.isNotEmpty(customizeFormDataEntity.getCrmFormFieldMapV2()));
        }
        boolean openMember = memberManager.isOpenMember(conferenceEntity.getEa());
        if (openMember) {
            // 查询市场活动是否做了会员映射
            detailResult.setConferenceCompleteMapping((memberManager.marketingEventHasMemberToLeadMapping(conferenceEntity.getEa(), conferenceEntity.getMarketingEventId())) && detailResult.isConferenceCompleteMapping());
        }
        detailResult.setActivityDetailSiteId(conferenceEntity.getActivityDetailSiteId());
        if (conferenceEntity.getShowAcitivityList() == null) {
            detailResult.setShowActivityList(false);
        } else {
            detailResult.setShowActivityList(conferenceEntity.getShowAcitivityList());
        }
        if (conferenceEntity.getEnrollCheckEmployee() != null){
            if (dingManager.isDingAddressbook(conferenceEntity.getEa())) {
                List<String> outUidList = gs.fromJson(conferenceEntity.getEnrollCheckEmployee(), ArrayList.class);
                detailResult.setOutEnrollCheckEmployee(outUidList);
            } else {
                List<Double> enrollCheckEmployeeLong = gs.fromJson(conferenceEntity.getEnrollCheckEmployee(), ArrayList.class);
                if (CollectionUtils.isNotEmpty(enrollCheckEmployeeLong)) {
                    List<Integer> enrollCheckEmployee = enrollCheckEmployeeLong.stream().map(item -> item.intValue()).collect(Collectors.toList());
                    detailResult.setEnrollCheckEmployee(enrollCheckEmployee);
                }
            }
        }
        if (conferenceEntity.getEnrollCheckDepartment() != null){
            List<Double> enrollCheckDepartmentLong = gs.fromJson(conferenceEntity.getEnrollCheckDepartment(), ArrayList.class);
            List<Integer> enrollCheckDepartment = enrollCheckDepartmentLong.stream().map(item -> item.intValue()).collect(Collectors.toList());
            detailResult.setEnrollCheckDepartment(enrollCheckDepartment);
        }
        detailResult.setEnrollNoticePoint(conferenceEntity.getEnrollNoticePoint());

        PhotoEntity photoEntity = photoManager.querySinglePhotoByEa(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), conferenceEntity.getId(), conferenceEntity.getEa());
        if (photoEntity != null) {
            detailResult.setCoverImageUrl(photoEntity.getUrl());
            detailResult.setCoverImageThumbUrl(StringUtils.isBlank(photoEntity.getThumbnailUrl()) ? photoEntity.getUrl() : photoEntity.getThumbnailUrl());
            detailResult.setCoverImagePath(photoEntity.getPath());
        }else {
            String defaultCoverPath = defaultConferenceCover;
            String defaultCoverUrl = fileV2Manager.getUrlByPath(defaultCoverPath, conferenceEntity.getEa(), false);
            detailResult.setCoverImageUrl(defaultCoverUrl);
            detailResult.setCoverImageThumbUrl(defaultCoverUrl);
            detailResult.setCoverImagePath(defaultConferenceCover);
        }

        List<PhotoEntity> photoList = Lists.newArrayList();
        List<PhotoEntity> qrPhotoEntities = photoDAO.listByTargetIdsAndTargetType(conferenceEntity.getId(), PhotoTargetTypeEnum.H5_CONFERENCE_QR_CODE.getType());
        PhotoEntity qrPhoto = null;
        if (CollectionUtils.isNotEmpty(qrPhotoEntities)) {
            qrPhoto = qrPhotoEntities.get(0);
            photoList.add(qrPhoto);

        }

        List<PhotoEntity> cardPhotoEntities = photoDAO.listByTargetIdsAndTargetType(conferenceEntity.getId(), PhotoTargetTypeEnum.MINI_COVER_ACTIVITY_NORMAL.getType());
        PhotoEntity cardPhoto = null;
        if (CollectionUtils.isNotEmpty(cardPhotoEntities)) {
            cardPhoto = cardPhotoEntities.get(0);
            photoList.add(cardPhoto);
        }
        photoManager.resetPhotoListUrl(photoList, null);
        if (qrPhoto != null) {
            detailResult.setQrUrl(qrPhoto.getUrl());
            detailResult.setQrApath(qrPhoto.getPath());
        }
        if (cardPhoto != null) {
            detailResult.setCardPhotoUrl(cardPhoto.getUrl());
        }

        detailResult.setFlowStatus(conferenceManager.getConferenceTimeFlowStatus(conferenceEntity));

        if (StringUtils.isNotBlank(conferenceEntity.getMarketingEventId())) {
            QueryMarketingEventDetailResult marketingEventDetail = buildMarketingEventDetail(conferenceEntity.getEa(), conferenceEntity.getMarketingEventId());
            detailResult.setMarketingEventDetail(marketingEventDetail);
        }
        ObjectTagEntity tagEntity = objectTagManager.queryObjectTag(conferenceEntity.getEa(), conferenceEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (tagEntity != null) {
            detailResult.setTagNameList(tagEntity.getTagNameList());
        }

        Result<ShareContentResult>  shareContentResultResult = shareContentService.getDetail(conferenceEntity.getEa(), vo.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (shareContentResultResult.isSuccess()){
            detailResult.setShareContentResult(shareContentResultResult.getData());
        }
        if(detailResult != null && org.apache.commons.lang3.StringUtils.isNotBlank(detailResult.getActivityDetailSiteId())){
            HexagonPageEntity homePage = hexagonPageDAO.getHomePage(detailResult.getActivityDetailSiteId());
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), homePage.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                detailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), homePage.getId());
            if (coverCutH5PhotoEntity != null) {
                detailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), homePage.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                detailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                detailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }
        detailResult.setDefaultContentMobileDisplay(conferenceEntity.getDefaultContentMobileDisplay());
        return Result.newSuccess(detailResult);
    }

    @Override
    @Transactional
    public Result updateConferenceEnrollSetting(ConferenceEnrollSettingVO vo) {
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(vo.getId());
        if (conferenceEntity == null) {
            log.info("ConferenceServiceImpl updateConferenceEnrollSetting conferenceEntity failed, vo:{}", vo);
            return Result.newError(SHErrorCode.CONFERENCE_UPDATE_NOT_FOUND);
        }

        return conferenceManager.updateConferenceEnrollSetting(vo, conferenceEntity);
    }

    @Override
    public Result<PageResult<QueryConferenceParticipantsResult>> queryConferenceParticipants(QueryConferenceParticipantsVO vo){
        PageResult<QueryConferenceParticipantsResult> pageResult = new PageResult<>();
        List<QueryConferenceParticipantsResult> queryConferenceListResult = Lists.newArrayList();
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setResult(queryConferenceListResult);
        pageResult.setTotalCount(0);

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);

        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.queryConferenceParticipants error activityEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String groupUserId = null;
        if (CollectionUtils.isNotEmpty(vo.getGroupUserId())) {
            groupUserId = vo.getGroupUserId().stream().collect(Collectors.joining(","));
        }

        List<String> campaignObjIds = crmV2Manager.getObjIdsByRuleGroupJson(vo.getEa(), vo.getRuleGroupJson());

        List<PageCampaignParticipantsDTO> pageCampaignParticipantsDTOList = campaignMergeDataDAO.pageCampaignParticipantsData(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getInviteStatus(), vo.getSignStatus(), vo.getKeyword(), vo.getFilterPhoneUser(), vo.getSaveCrmStatus(), vo.getReviewStatus(), vo.getChannelValue(), groupUserId, vo.getPayStatus(), campaignObjIds, page);

        if (CollectionUtils.isEmpty(pageCampaignParticipantsDTOList)) {
            log.warn("ConferenceServiceImpl.queryConferenceParticipants pageCampaignParticipantsDTOList is null vo:{}", vo);
            return Result.newSuccess(pageResult);
        }

        // 计算totalCount
        int allDataSize = campaignMergeDataDAO.countCampaignParticipantsData(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getInviteStatus(), vo.getSignStatus(), vo.getKeyword(), vo.getFilterPhoneUser(), vo.getSaveCrmStatus(), vo.getReviewStatus(), vo.getChannelValue(), groupUserId, vo.getPayStatus());
        pageResult.setTotalCount(allDataSize);

        // 查询负责人
        List<String> campaignMembersObjIds = pageCampaignParticipantsDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
            PageCampaignParticipantsDTO::getCampaignMembersObjId).collect(
            Collectors.toList());
        List<String> campaignMergeDataIds = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getId).collect(Collectors.toList());

        List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(vo.getEa(), activityEntity.getMarketingEventId(), campaignMembersObjIds);
        Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(vo.getEa(), objectDataList);
        // 查询姓名（兼容表单无名称，对象有名称场景）
        Map<String, ObjectData> objectDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, data -> data, (v1, v2) -> v1));

        // 查询邀约人
        List<ConferenceInvitationUserDTO> conferenceInvitationUserDTOList = conferenceInvitationUserDAO
            .queryLatestConferenceInvitationUserByCampaignMergeDataId(vo.getEa(), campaignMergeDataIds, activityEntity.getId());
        Map<Integer, FSEmployeeMsg> allFSEmployeeMsgMap = Maps.newHashMap();
        List<Integer> fsUserIdContainVirtualUser = Lists.newArrayList();
        Map<String, Integer> campaignMergeDataUserMap = conferenceInvitationUserDTOList.stream()
            .collect(Collectors.toMap(ConferenceInvitationUserDTO::getCampaignMergeDataId, ConferenceInvitationUserDTO::getUserId, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(conferenceInvitationUserDTOList)) {
            List<Integer> invitationUserIds = conferenceInvitationUserDTOList.stream().map(ConferenceInvitationUserDTO::getUserId).collect(Collectors.toList());
            List<Integer> fsUserList = invitationUserIds.stream().filter(data -> !QywxUserConstants.isVirtualUserId(data)).filter(Objects::nonNull).collect(Collectors.toList());
            fsUserIdContainVirtualUser.addAll(invitationUserIds.stream().filter(QywxUserConstants::isVirtualUserId).collect(Collectors.toList()));
            allFSEmployeeMsgMap.putAll(fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(vo.getEa(), fsUserList, true));
        }
        // 查询推广人
        fsUserIdContainVirtualUser.addAll(pageCampaignParticipantsDTOList.stream().filter(data -> data.getSpreadFsUserId() != null).map(PageCampaignParticipantsDTO::getSpreadFsUserId).collect(Collectors.toList()));
        allFSEmployeeMsgMap.putAll(fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), fsUserIdContainVirtualUser, true));
        Map<Integer, UserRelationPartnerInfo> fsUserIdToPartnerInfoMap = userRelationManager.getPartnerInfoByFsUserIdList(vo.getEa(), fsUserIdContainVirtualUser);
        // 拼装物料数据
        Map<String, String> objectNameMap = Maps.newHashMap();
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
            String objectKey = pageCampaignParticipantsDTO.getEnrollSourceObjectId() + "#" + pageCampaignParticipantsDTO.getEnrollSourceObjectType();
            if (StringUtils.isBlank(objectNameMap.get(objectKey))) {
                String objectName = objectManager.getObjectName(pageCampaignParticipantsDTO.getEnrollSourceObjectId(), pageCampaignParticipantsDTO.getEnrollSourceObjectType());
                if (StringUtils.isNotBlank(objectName)) {
                    objectNameMap.put(objectKey, objectName);
                }
            }
        }
        // 是否开启手机脱敏
        //boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(vo.getEa());
        List<String> campaignIds = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getId).collect(Collectors.toList());
        //Map<String, CustomizeFormDataUserEntity> enrollMap = campaignMergeDataManager.getLatestActivityEnrollDataByCampaignId(activityEntity.getId(), campaignIds);
        // 查询最新的会员数据
        Map<String, String> accessibleMemberMap = memberManager.getLatestAccessibleMemberIdByCampaignIds(campaignIds);
        // 查询微信用户
        Map<String, List<String>> wxAppOpenIdMap = Maps.newHashMap();
        Map<String, WxUserData> allWxUserData = Maps.newHashMap();
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
            if (StringUtils.isBlank(pageCampaignParticipantsDTO.getWxAppId()) || StringUtils.isBlank(pageCampaignParticipantsDTO.getOpenId())) {
                continue;
            }
            wxAppOpenIdMap.computeIfAbsent(pageCampaignParticipantsDTO.getWxAppId(), data -> Lists.newArrayList()).add(pageCampaignParticipantsDTO.getOpenId());
        }
        for (Map.Entry<String, List<String>> entry : wxAppOpenIdMap.entrySet()) {
            allWxUserData.putAll(campaignMergeDataManager.queryWxUserInfo(vo.getEa(), entry.getKey(), entry.getValue()));
        }
        Map<String, String> customerMap = new HashMap<>();
        Map<String, String> employeeMap = new HashMap<>();
        List<String> outTenantIdList = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getOutTenantId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outTenantIdList.isEmpty()) {
            List<ObjectData> customerListByDestOuterTenantIds = customizeFormDataManager.getCustomerListByEnterpriserelationIds(vo.getEa(), outTenantIdList);
            customerListByDestOuterTenantIds.forEach(e -> customerMap.put(String.valueOf(e.get("enterpriserelation_id")), String.valueOf(e.get("name"))));
        }
        List<String> outUserIdList = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getOutUid).filter(Objects::nonNull).collect(Collectors.toList());
        if (!outUserIdList.isEmpty()) {
            List<ObjectData> employeeListByDestOuterTenantIds = customizeFormDataManager.getEmployeeListByOuterUidIds(vo.getEa(), outUserIdList);
            employeeListByDestOuterTenantIds.forEach(e -> employeeMap.put(String.valueOf(e.get("outer_uid")), String.valueOf(e.get("name"))));
        }
        Map<Integer, List<String>> crmObjectTypeToIdMap = Maps.newHashMap();
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
            if (pageCampaignParticipantsDTO.getBindCrmObjectType() != null && StringUtils.isNotBlank(pageCampaignParticipantsDTO.getBindCrmObjectId())) {
                List<String> crmObjectIdList = crmObjectTypeToIdMap.computeIfAbsent(pageCampaignParticipantsDTO.getBindCrmObjectType(), k -> Lists.newArrayList());
                crmObjectIdList.add(pageCampaignParticipantsDTO.getBindCrmObjectId());
            }
        }
        Map<String, String> crmObjectIdToNameMap = conferenceManager.getEnrollNameByObjectType(vo.getEa(), crmObjectTypeToIdMap);
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
            QueryConferenceParticipantsResult queryConferenceParticipantsResult = new QueryConferenceParticipantsResult();
            queryConferenceParticipantsResult.setId(pageCampaignParticipantsDTO.getId());
            String userName = pageCampaignParticipantsDTO.getName();
            ObjectData userObjData = objectDataMap.get(pageCampaignParticipantsDTO.getCampaignMembersObjId());
            if (StringUtils.isBlank(userName) && StringUtils.isNotBlank(pageCampaignParticipantsDTO.getCampaignMembersObjId())) {
                userName = userObjData != null ? userObjData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName()) : null;
            }
            queryConferenceParticipantsResult.setName(UnicodeFormatter.decodeUnicodeString(userName));
            if (StringUtils.isNotBlank(pageCampaignParticipantsDTO.getCampaignMembersObjId())) {
                queryConferenceParticipantsResult.setBindCrmObjectId(pageCampaignParticipantsDTO.getBindCrmObjectId());
                queryConferenceParticipantsResult.setBindCrmObjectType(pageCampaignParticipantsDTO.getBindCrmObjectType());
                queryConferenceParticipantsResult.setSaveCrmStatus(SaveCrmStatusEnum.SUCCESS.getValue());
                if (pageCampaignParticipantsDTO.getAddCampaignMember() != null && pageCampaignParticipantsDTO.getAddCampaignMember()) {
                    queryConferenceParticipantsResult.setSaveCrmStatus(SaveCrmStatusEnum.LINKED.getValue());
                }
                if (pageCampaignParticipantsDTO.getSaveCrmStatus() != null) {
                    queryConferenceParticipantsResult.setSaveCrmStatus(pageCampaignParticipantsDTO.getSaveCrmStatus());
                }
                String ownerName = ownerNameMap.get(pageCampaignParticipantsDTO.getCampaignMembersObjId());
                queryConferenceParticipantsResult.setOwnerName(ownerName);
            } else {
                //如果已关联，但是创建活动成员失败，展示为已经失败状态
                if (pageCampaignParticipantsDTO.getSaveCrmStatus() != null && pageCampaignParticipantsDTO.getSaveCrmStatus() == SaveCrmStatusEnum.LINKED.getValue() && pageCampaignParticipantsDTO.getBindCrmObjectId() == null) {
                    queryConferenceParticipantsResult.setSaveCrmStatus(SaveCrmStatusEnum.ERROR.getValue());
                    queryConferenceParticipantsResult.setSaveCrmErrorMessage(SHErrorCode.CREATE_CAPMAIGN_MEMBER_FAILD.getErrorMessage());
                }else {
                    queryConferenceParticipantsResult.setSaveCrmStatus(pageCampaignParticipantsDTO.getSaveCrmStatus());
                    queryConferenceParticipantsResult.setSaveCrmErrorMessage(pageCampaignParticipantsDTO.getSaveCrmErrorMessage());
                }
            }

            //根据业务数据来源来获取关联名称
            String relationDataName = queryConferenceParticipantsResult.getName();
            if (StringUtils.isNotBlank(pageCampaignParticipantsDTO.getBindCrmObjectId()) && pageCampaignParticipantsDTO.getBindCrmObjectType() != null) {
                //String objectName = conferenceManager.getEnrollNameByObjectType(vo.getEa(),pageCampaignParticipantsDTO.getBindCrmObjectType(),pageCampaignParticipantsDTO.getBindCrmObjectId());
                String objectName = crmObjectIdToNameMap.get(pageCampaignParticipantsDTO.getBindCrmObjectId());
                if (objectName != null) {
                    relationDataName = objectName;
                }
            }

            queryConferenceParticipantsResult.setRelationDataName(relationDataName);
            queryConferenceParticipantsResult.setChannelValue(pageCampaignParticipantsDTO.getChannelValue());
            queryConferenceParticipantsResult.setInviteStatus(pageCampaignParticipantsDTO.getInviteStatus());
            queryConferenceParticipantsResult.setReviewStatus(pageCampaignParticipantsDTO.getReviewStatus());
            queryConferenceParticipantsResult.setCode(pageCampaignParticipantsDTO.getCode());
            queryConferenceParticipantsResult.setSignStatus(pageCampaignParticipantsDTO.getSignIn());
            queryConferenceParticipantsResult.setSignTime(pageCampaignParticipantsDTO.getSignInTime() != null ? DateUtil.format(pageCampaignParticipantsDTO.getSignInTime()) : null);
            queryConferenceParticipantsResult.setGroupName(conferenceManager.getGroupNameByIdsStr(pageCampaignParticipantsDTO.getGroupUserId()));
            queryConferenceParticipantsResult.setCampaignMembersObjId(pageCampaignParticipantsDTO.getCampaignMembersObjId());
            queryConferenceParticipantsResult.setEnrollReview(activityEntity.getEnrollReview() != null ? activityEntity.getEnrollReview() : false);
            Integer invitationUserId = campaignMergeDataUserMap.get(pageCampaignParticipantsDTO.getId());
            if (invitationUserId != null) {
                FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(invitationUserId);
                queryConferenceParticipantsResult.setInviteUserName(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
            }
            // 推广人
            if (pageCampaignParticipantsDTO.getSpreadFsUserId() != null) {
                String spreadUserName = "";
                if (QywxUserConstants.isPartnerVirtualUserId(pageCampaignParticipantsDTO.getSpreadFsUserId())) {
                    UserRelationPartnerInfo userRelationPartnerInfo = fsUserIdToPartnerInfoMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId());
                    if (userRelationPartnerInfo != null) {
                        String outTenantName = userRelationPartnerInfo.getOuterTenantName() == null ? "" : userRelationPartnerInfo.getOuterTenantName();
                        String outUserName = userRelationPartnerInfo.getOuterUserName() == null ? "" : userRelationPartnerInfo.getOuterUserName();
                        spreadUserName = outTenantName + "-" + outUserName;
                    }
                } else {
                    FSEmployeeMsg fsEmployeeMsg = allFSEmployeeMsgMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId());
                    spreadUserName = fsEmployeeMsg != null ? fsEmployeeMsg.getName() : "";
                }
                queryConferenceParticipantsResult.setSpreadUserName(spreadUserName);
            }
//            if (StringUtils.isBlank(queryConferenceParticipantsResult.getSpreadUserName()) && outUserIdToNameMap.containsKey(pageCampaignParticipantsDTO.getSpreadFsUserId())) {
//                queryConferenceParticipantsResult.setSpreadUserName(outUserIdToNameMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId()));
//            }
            // 伙伴营销推广人
            queryConferenceParticipantsResult.setMemberId(accessibleMemberMap.get(pageCampaignParticipantsDTO.getId()));
            queryConferenceParticipantsResult.setEnrollSourceName(objectNameMap.get(pageCampaignParticipantsDTO.getEnrollSourceObjectId() + "#" + pageCampaignParticipantsDTO.getEnrollSourceObjectType()));
            // 查询会议报名数据
            /*CustomizeFormDataUserEntity customizeFormDataUserEntity = enrollMap.get(pageCampaignParticipantsDTO.getId());
            if (customizeFormDataUserEntity != null && StringUtils.isNotBlank(pageCampaignParticipantsDTO.getId())) {
                if (turnOnPhoneNumberSensitive) {
                    safetyManagementManager.phoneNumberSensitive(customizeFormDataUserEntity);
                }
                queryConferenceParticipantsResult.setConferenceEnrollFormData(customizeFormDataUserEntity.getSubmitContent());
            }*/
            if (StringUtils.isNotBlank(pageCampaignParticipantsDTO.getWxAppId()) && StringUtils.isNotBlank(pageCampaignParticipantsDTO.getOpenId())) {
                WxUserData wxUserData = allWxUserData.get(pageCampaignParticipantsDTO.getOpenId());
                if (wxUserData != null) {
                    queryConferenceParticipantsResult.setWxUserName(wxUserData.getName());
                    queryConferenceParticipantsResult.setWxUserAvatar(wxUserData.getUserAvatar());
                }
            }
            queryConferenceParticipantsResult.setFormDataUserId(pageCampaignParticipantsDTO.getFormDataUserId());

            BigDecimal totalAmount = new BigDecimal(pageCampaignParticipantsDTO.getTotalAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            queryConferenceParticipantsResult.setTotalAmount(totalAmount);

            queryConferenceListResult.add(queryConferenceParticipantsResult);
        }


        // 获取会议表单映射关系
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(vo.getEa(), vo.getConferenceId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity != null) {
            FieldInfoList fieldInfoList = customizeFormDataEntity.getFormBodySetting();
            pageResult.setOtherData(fieldInfoList);
        }
        pageCampaignParticipantsDTOList.clear();

        Map<String, Integer> campaignIdToPayOrderCountMap = campaignMergeDataManager.countPayOrderNumber(vo.getEa(), campaignIds);
        pageResult.getResult().forEach(r -> {
            r.setPayOrderCount(campaignIdToPayOrderCountMap.get(r.getId()) != null ? campaignIdToPayOrderCountMap.get(r.getId()) : 0);
        });
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Boolean> changeConferenceParticipantsSignStatus(ChangeConferenceParticipantsSignStatusVO vo) {
        List<String> conferenceEnrollIds = campaignMergeDataManager.campaignIdToActivityEnrollId(vo.getCampaignIds());
        if (CollectionUtils.isEmpty(conferenceEnrollIds)) {
            return Result.newSuccess();
        }
        campaignMergeDataManager.updateSignInStatus(conferenceEnrollIds, ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
        ThreadPoolUtils.execute(() -> {
            //中力企业管理顾问有限公司会议报名和审核写入自定义对象
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryByConferenceEnrollIds(conferenceEnrollIds, null);
            if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
                for (CustomizeFormDataUserEntity entity : customizeFormDataUserEntityList) {
                    if (StringUtils.isNotEmpty(entity.getExtraDataId())) {
                        customerCustomizeFormDataManager.updateSignInObject(vo.getEa(), entity.getExtraDataId(), ActivitySignOrEnrollEnum.SIGN_IN.getType());
                    }
                }
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> changeConferenceParticipantsReviewStatus(ChangeConferenceParticipantsReviewStatusVO vo) {
        List<String> conferenceEnrollIds = campaignMergeDataManager.campaignIdToActivityEnrollId(vo.getCampaignIds());
        if (CollectionUtils.isEmpty(conferenceEnrollIds)) {
            return Result.newSuccess();
        }

        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByIds(conferenceEnrollIds);

        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            log.warn("ConferenceServiceImpl.changeConferenceParticipantsReviewStatus activityEnrollDataEntityList is null");
            return Result.newError(SHErrorCode.CONFERENCE_PARTICIPATN_NOT_FOUND);
        }

        //activityEnrollDataEntityList = activityEnrollDataEntityList.stream().filter(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            log.warn("ConferenceServiceImpl.changeConferenceParticipantsReviewStatus activityEnrollDataEntityList is null no pendingReview data");
            return Result.newError(SHErrorCode.CONFERENCE_PARTICIPATN_NOT_FOUND);
        }
        conferenceEnrollIds = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList());
        campaignMergeDataManager.updateConferenceReviewStatus(conferenceEnrollIds, vo.getReviewStatus(), vo.getReviewFailedMsg(), false);

        List<String> finalConferenceEnrollIds = conferenceEnrollIds;
        ThreadPoolUtils.execute(() -> {
            Map<String, Long> ticketCodeMap = Maps.newHashMap();
            if (vo.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus())) {
                List<String> campaignIds = campaignMergeDataManager.activityEnrollIdToCampaignId(finalConferenceEnrollIds);
                for (String campaignId : campaignIds) {
                    Long code = customizeTicketManager.createCustomizeTicket(activityEnrollDataEntityList.get(0).getActivityId(), CustomizeTicketTypeEnum.ACTIVITY_TICKET.getType(), vo.getEa(), campaignId, false);
                    ticketCodeMap.put(campaignId, code);
                }
            }

            // 修改审核状态与参会码
            List<String> campaignIds = campaignMergeDataManager.activityEnrollIdToCampaignId(finalConferenceEnrollIds);
            List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
            ConferenceEnrollReviewStatusEnum conferenceEnrollReviewStatusEnum = ConferenceEnrollReviewStatusEnum.getByType(vo.getReviewStatus());
            if (conferenceEnrollReviewStatusEnum != null) {
                List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(campaignIds);
                Set<String> existEnrollDataCampaignIdSet = customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getCampaignId).collect(Collectors.toSet());
                List<String> campaignMemberObjIdList = campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getCampaignMembersObjId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<ObjectData> campaignMemberbjectDataList = crmMetadataManager.batchGetByIdsV3(vo.getEa(), SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), null, campaignMemberObjIdList);
                Map<String, ObjectData> campaignMemberObjectIdToDataMap = campaignMemberbjectDataList.stream().collect(Collectors.toMap(ObjectData::getId, e -> e, (v1, v2) -> v1));
                for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                    if (StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId()) && (vo.getUpdateCampaignMemberObj() == null || BooleanUtils.isTrue(vo.getUpdateCampaignMemberObj()))) {
                        Map<String, Object> editMap = Maps.newHashMap();
                        editMap.put(CampaignConstants.APPROVAL_STATUS_API_NAME, conferenceEnrollReviewStatusEnum.getCampaignMembersObjOptions());
                        String campaignMemberObjId = campaignMergeDataEntity.getCampaignMembersObjId();
                        ObjectData objectData = campaignMemberObjectIdToDataMap.get(campaignMemberObjId);
                        if (ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getCampaignMembersObjOptions().equals(conferenceEnrollReviewStatusEnum.getCampaignMembersObjOptions())) {
                            // 如果是审核状态 = 已审核， 那么参与状态字段由签到状态字段决定，如果签到状态 = 已签到，那么参与状态 = 已签到，否则就是已审核通过
                            String signInStatus = objectData == null ? null : objectData.getString(CampaignConstants.SIGN_IN_STATUS_API_NAME);
                            if (ActivitySignOrEnrollEnum.SIGN_IN.getCampaignMergeDataApiName().equals(signInStatus)) {
                                editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.PARTICIPATE.getValue());
                            } else {
                                editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REVIEW.getValue());
                            }
                        } else if (ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getCampaignMembersObjOptions().equals(conferenceEnrollReviewStatusEnum.getCampaignMembersObjOptions())) {
                            // 如果审核状态未审核失败，如果该活动成员有表单提交记录，则参与状态 = 已报名，如果没有表单提交记录且邀约状态 = 已邀约，那么参与状态 = 已邀约  否则为待邀约
                            boolean existEnroll = existEnrollDataCampaignIdSet.contains(campaignMergeDataEntity.getId());
                            String invitationStatus = objectData == null ? null : objectData.getString(CampaignConstants.INVITATION_STATUS_API_NAME);
                            if (existEnroll) {
                                editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
                            } else if (CampaignMergeDataInviteStatusEnum.INVITED.getCrmOptions().equals(invitationStatus)) {
                                editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.NOTICE.getValue());
                            } else {
                                editMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue());
                            }
                        }
                        Long code = ticketCodeMap.get(campaignMergeDataEntity.getId());
                        if (code != null) {
                            editMap.put(CampaignConstants.PARTICIPANTS_PASSCODE_API_NAME, code.toString());
                        }
                        crmV2Manager.editCampaignMembersObj(campaignMergeDataEntity.getEa(), campaignMemberObjId, editMap);
                    }
                }
            }

            //定制化处理深圳中力企业管理顾问有限公司会议报名和审核写入自定义对象
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.queryByConferenceEnrollIds(finalConferenceEnrollIds, null);
            if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
                for (CustomizeFormDataUserEntity entity : customizeFormDataUserEntityList) {
                    if (StringUtils.isNotEmpty(entity.getExtraDataId())) {
                        customerCustomizeFormDataManager.updateReviewObject(vo.getEa(), entity.getExtraDataId(), vo.getReviewStatus());
                    }
                }
            }

            // 发送审核结果短信
            conferenceManager.sendNotificationByEnrollIds(activityEnrollDataEntityList.get(0).getActivityId(), finalConferenceEnrollIds, vo.getReviewStatus(), vo.getReviewFailedMsg());
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return Result.newSuccess(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> saveConferenceParticipantsToCrm(SaveConferenceParticipantsToCrmVO vo) {
        // 首先查询是否正在执行任务
        String campaignId = vo.getCampaignIds().get(0);
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
        if (campaignMergeDataEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (executeTaskDetailManager.checkTaskAndAddIfNotExist(vo.getEa(), ExecuteTaskDetailTypeEnum.SAVE_CONFERENCE_PARTICIPANTS_TO_CRM, campaignMergeDataEntity.getMarketingEventId())) {
            return Result.newError(SHErrorCode.TASK_IN_PROGRESS);
        }

        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
                asnycSaveConferenceParticipantsToCrm(vo);
                //发送消息到文件助手
                String text = "您于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "提交的重新存入线索任务已经完成。您可以前往重新存入的页面查看线索存入情况。";
                pushSessionManager.pushTextToFileAssistant(text, vo.getEa(), vo.getUserId());
            } catch (Exception e) {
                log.warn("ConferenceServiceImpl.saveConferenceParticipantsToCrm error e:{}", e);
            } finally {
                executeTaskDetailManager.taskComplete(vo.getEa(), ExecuteTaskDetailTypeEnum.SAVE_CONFERENCE_PARTICIPANTS_TO_CRM, campaignMergeDataEntity.getMarketingEventId());
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess(Boolean.TRUE);
    }

    private void asnycSaveConferenceParticipantsToCrm(SaveConferenceParticipantsToCrmVO vo){
        PageUtil<String> campaignIdsPage = new PageUtil<>(vo.getCampaignIds(), 50);
        List<BaseCampaignDTO> baseCampaignDTOList = Lists.newArrayList();
        for (int i = 1; i <= campaignIdsPage.getPageCount(); i++) {
            baseCampaignDTOList.addAll(campaignMergeDataDAO.queryCampaignMergeDataIdAndType(campaignIdsPage.getPagedList(i)));
        }
        List<String> formEnrollData = Lists.newArrayList();
        List<String> memberEnrollData = Lists.newArrayList();
        for (BaseCampaignDTO baseCampaignDTO : baseCampaignDTOList) {
            if (baseCampaignDTO.getSourceType().equals(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType())) {
                formEnrollData.add(baseCampaignDTO.getId());
            } else if (baseCampaignDTO.getSourceType().equals(CampaignMergeDataSourceTypeEnum.MEMBERS_OBJ.getType())) {
                memberEnrollData.add(baseCampaignDTO.getId());
            }
        }

        if (CollectionUtils.isNotEmpty(formEnrollData)) {
            customizeFormDataManager.saveConferenceParticipantsToCrm(vo.getEa(), vo.getCampaignIds());
        }
        if (CollectionUtils.isNotEmpty(memberEnrollData)) {
            memberManager.saveMemberToLeadByCampaignIds(memberEnrollData);
        }
    }

    @Override
    public Result invitationSetting(InvitationSettingVO vo) {
        try {
            ConferenceInvitationEntity invitationEntity;
            if (StringUtils.isBlank(vo.getId())) {
                invitationEntity = new ConferenceInvitationEntity();
                invitationEntity.setId(UUIDUtil.getUUID());
                invitationEntity.setActivityId(vo.getConferenceId());
                invitationEntity.setEa(vo.getEa());
                invitationEntity.setName(vo.getInvitationName());
                invitationEntity.setInvitationDetails(changeInvivationImage(vo.getInvitationDetail()));
                invitationEntity.setInvitationButton(vo.getInvitationButton());
                invitationEntity.setStatus(ConferenceInvitationStatusEnum.ENABLE.getStatus());
                invitationEntity.setCreateBy(vo.getFsUserId());
                invitationEntity.setCreateTime(DateUtil.now());
                invitationEntity.setUpdateBy(vo.getFsUserId());
                invitationEntity.setUpdateTime(DateUtil.now());
                conferenceInvitationDAO.addConferenceInvitation(invitationEntity);
            } else {
                invitationEntity = conferenceInvitationDAO.getInvitationById(vo.getId());
                if (invitationEntity == null) {
                    log.info("ConferenceServiceImpl invitationSetting invitationEntity not found, vo:{}", vo);
                    return Result.newError(SHErrorCode.CONFERENCE_INVITATION_NOT_FOUND);
                }
                if (StringUtils.isNotBlank(vo.getInvitationName())) {
                    invitationEntity.setName(vo.getInvitationName());
                }
                if (vo.getInvitationDetail() != null) {
                    invitationEntity.setInvitationDetails(changeInvivationImage(vo.getInvitationDetail()));
                }

                if (vo.getInvitationButton() != null) {
                    invitationEntity.setInvitationButton(vo.getInvitationButton());
                }
                conferenceInvitationDAO.updateConferenceInvitation(invitationEntity);
            }
            if (vo.isUpdateCoverImage()) {
                try {
                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(vo.getCoverImageTAPath(), null, null);
                    if (null != fileManagerPicResult) {
                        photoManager.addOrUpdatePhotoByPhotoTargetType(vo.getEa(),PhotoTargetTypeEnum.ACTIVITY_INVITATION_COVER, invitationEntity.getId(), fileManagerPicResult.getUrlAPath(), fileManagerPicResult.getThumbUrlApath());
                    }
                } catch (Exception e) {
                    log.info("ConferenceServiceImpl invitationSetting conferenceEntity UpdateCoverImage failed, vo:{}", vo);
                    return Result.newError(SHErrorCode.CONFERENCE_UPDATE_IMAGE_FAIL);
                }
            }
        } catch (Exception e) {
            log.info("ConferenceServiceImpl invitationSetting exception,vo:{},exception:{}", vo, e.fillInStackTrace());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        return Result.newSuccess();
    }

    private FieldValueList changeInvivationImage(FieldValueList invitationDetail) {
        invitationDetail.forEach(value -> {
            if (value.getName() != null && value.getName().equals("imageTAPath") && !Strings.isNullOrEmpty(value.getValue())) {
                List<String> shareUrlList = fileManager.saveTAPathAndGetSharePathList(value.getValue());
                value.setName("imageUrl");
                value.setValue(shareUrlList.get(0));
            }
        });
        return invitationDetail;
    }

    @Override
    public Result updateInvitationStatus(UpdateInvitationStatusVO vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ConferenceInvitationEntity invitationEntity = conferenceInvitationDAO.getInvitationById(vo.getId());
        if (invitationEntity == null) {
            log.info("ConferenceServiceImpl invitationSetting invitationEntity not found, vo:{}", vo);
            return Result.newError(SHErrorCode.CONFERENCE_INVITATION_NOT_FOUND);
        }
        try {
            conferenceInvitationDAO.updateInvitationStatus(vo.getId(), vo.getStatus(), vo.getFsUserId());
        } catch (Exception e) {
            log.info("ConferenceServiceImpl updateInvitationStatus exception,vo:{},exception:{}", vo, e.fillInStackTrace());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QueryInvitationResult>> queryInvitationList(QueryInvitationListVO vo) {
        if (StringUtils.isBlank(vo.getConferenceId()) && StringUtils.isBlank(vo.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isBlank(vo.getConferenceId())) {
            ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(vo.getMarketingEventId(), vo.getEa());
            if (activityEntity == null) {
                return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
            }
            vo.setConferenceId(activityEntity.getId());
        }
        PageResult<QueryInvitationResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTime(vo.getTime());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<ConferenceInvitationEntity> invitationEntityList = conferenceInvitationDAO
            .pageInvitations(vo.getConferenceId(), vo.getStatus(), StringUtils.isEmpty(vo.getSearchKey()) ? null : vo.getSearchKey(), page);
        if (CollectionUtils.isNotEmpty(invitationEntityList)) {
            pageResult.setTotalCount(page.getTotalNum());
            List<QueryInvitationResult> resultList = Lists.newArrayList();
            pageResult.setResult(resultList);
            invitationEntityList.forEach(invitationEntity -> {
                resultList.add(buildInvitationResult(invitationEntity));
            });
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<QueryInvitationResult> queryInvitationInfo(QueryInvitationVO vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ConferenceInvitationEntity invitationEntity = conferenceInvitationDAO.getInvitationById(vo.getId());
        if (invitationEntity == null) {
            log.info("ConferenceServiceImpl queryInvitationInfo invitationEntity not found, vo:{}", vo);
            return Result.newError(SHErrorCode.CONFERENCE_INVITATION_NOT_FOUND);
        }
        // 查询对应活动数据
        ActivityEntity conferenceEntity = conferenceDAO.getConferenceById(invitationEntity.getActivityId());
        if(conferenceEntity == null) {
            log.warn("ConferenceServiceImpl.queryInvitationInfo conferenceEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        QueryInvitationResult queryInvitationResult = buildInvitationResult(invitationEntity);
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(invitationEntity.getEa(), invitationEntity.getActivityId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity != null) {
            queryInvitationResult.setFormId(customizeFormDataEntity.getId());
        }
        // 设置活动数据
        queryInvitationResult.setStartTime(conferenceEntity.getStartTime() != null ? conferenceEntity.getStartTime().getTime() : null);
        queryInvitationResult.setEndTime(conferenceEntity.getEndTime() != null ? conferenceEntity.getEndTime().getTime() : null);
        queryInvitationResult.setEnrollEndTime(conferenceEntity.getEnrollEndTime() != null ? conferenceEntity.getEnrollEndTime().getTime() : null);
        queryInvitationResult.setActivityStatus(conferenceEntity.getStatus());

        return Result.newSuccess(queryInvitationResult);
    }

    private QueryInvitationResult buildInvitationResult(ConferenceInvitationEntity invitationEntity) {
        QueryInvitationResult invitationResult = new QueryInvitationResult();
        invitationResult.setId(invitationEntity.getId());
        invitationResult.setConferenceId(invitationEntity.getActivityId());
        invitationResult.setInvitationName(invitationEntity.getName());
        invitationResult.setInvitationButton(invitationEntity.getInvitationButton());
        invitationResult.setInvitationDetail(invitationEntity.getInvitationDetails());
        invitationResult.setCreateTime(invitationEntity.getCreateTime().getTime());
        PhotoEntity photoEntity = photoManager.querySinglePhotoByEa(PhotoTargetTypeEnum.ACTIVITY_INVITATION_COVER.getType(), invitationEntity.getId(), invitationEntity.getEa());
        if (photoEntity != null) {
            invitationResult.setCoverImageUrl(photoEntity.getUrl());
            invitationResult.setCoverImageThumbUrl(StringUtils.isBlank(photoEntity.getThumbnailUrl()) ? photoEntity.getUrl() : photoEntity.getThumbnailUrl());
        }
        return invitationResult;
    }

    @Override
    public Result notificationSettings(NotificationSettingsVO vo) {
        if (vo == null || vo.getNotificationSettings() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ConferenceNotificationSettingEntity notificationSettingEntity = notificationSettingDAO.getNotificationSettingByEa(vo.getEa());
        if (notificationSettingEntity == null) {
            notificationSettingEntity = new ConferenceNotificationSettingEntity();
            notificationSettingEntity.setId(UUIDUtil.getUUID());
            notificationSettingEntity.setEa(vo.getEa());
            notificationSettingDAO.addNotificationSetting(notificationSettingEntity);
        }
        String userName = fsAddressBookManager.getEmployeeInfo(vo.getEa(), vo.getFsUserId()) == null ? "" : fsAddressBookManager.getEmployeeInfo(vo.getEa(), vo.getFsUserId()).getFullName();
        ActivityNotificationSettings settings = vo.getNotificationSettings();
        if (settings.getEnrollSuccess() && StringUtils.isBlank(settings.getEnrollSuccessTemplateId())) {
            String templateId = createDefaultSmsTemplate(vo.getEa(), vo.getFsUserId(), I18nUtil.getStaticByKey(ConferenceManager.ENROLL_SUCCESS_TEMPLATE_TEXT_DEFAULT),
                I18nUtil.getStaticByKey(ConferenceManager.ENROLL_SUCCESS_TEMPLATE_NAME_DEFAULT), userName);
            settings.setEnrollSuccessTemplateId(templateId);
        }

        if (settings.getEnrollAudit() && StringUtils.isBlank(settings.getEnrollAuditTemplateId())) {
            String templateId = createDefaultSmsTemplate(vo.getEa(), vo.getFsUserId(), I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_TEMPLATE_TEXT_DEFAULT),
                I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_TEMPLATE_NAME_DEFAULT), userName);
            settings.setEnrollAuditTemplateId(templateId);
        }

        if (settings.getEnrollAuditSuccess() && StringUtils.isBlank(settings.getEnrollAuditSuccessTemplateId())) {
            String templateId = createDefaultSmsTemplate(vo.getEa(), vo.getFsUserId(), I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_SUCCESS_TEMPLATE_TEXT_DEFAULT),
                I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_SUCCESS_TEMPLATE_NAME_DEFAULT), userName);
            settings.setEnrollAuditSuccessTemplateId(templateId);
        }

        if (settings.getEnrollAuditFail() && StringUtils.isBlank(settings.getEnrollAuditFailTemplateId())) {
            String templateId = createDefaultSmsTemplate(vo.getEa(), vo.getFsUserId(), I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_FAIL_TEMPLATE_TEXT_DEFAULT),
                I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_FAIL_TEMPLATE_NAME_DEFAULT), userName);
            settings.setEnrollAuditFailTemplateId(templateId);
        }

        notificationSettingEntity.setActivityNotificationSettings(settings);
        notificationSettingDAO.updateNotificationSettings(notificationSettingEntity);
        return Result.newSuccess();
    }

    private String createDefaultSmsTemplate(String ea, Integer fsUserId, String template, String templateName, String creator) {
        ApplyTemplateVO applyTemplateVO = new ApplyTemplateVO();
        applyTemplateVO.setEa(ea);
        applyTemplateVO.setContent(template);
        applyTemplateVO.setUserId(fsUserId);
        applyTemplateVO.setName(templateName);
        applyTemplateVO.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
        MwSmsTemplateEntity templateEntity = smsSettingManager.templateSetting(applyTemplateVO, creator, true, false);
        if (templateEntity == null) {
            return null;
        }
        return templateEntity.getId();
    }

    @Override
    public Result<QueryNotificationSettingsResult> queryNotificationSettings(QueryNotificationSettingsVO vo) {
        if (StringUtils.isBlank(vo.getEa())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ConferenceNotificationSettingEntity settingEntity = notificationSettingDAO.getNotificationSettingByEa(vo.getEa());
        QueryNotificationSettingsResult settingsResult;
        if (settingEntity != null && settingEntity.getActivityNotificationSettings() != null) {
            ActivityNotificationSettings notificationSettings = settingEntity.getActivityNotificationSettings();
            settingsResult = BeanUtil.copy(notificationSettings, QueryNotificationSettingsResult.class);
            if (StringUtils.isNotBlank(notificationSettings.getEnrollSuccessTemplateId())) {
                MwSmsTemplateEntity smsTemplateEntity = templateDao.getTemplateById(notificationSettings.getEnrollSuccessTemplateId());
                settingsResult.setEnrollSuccessTemplateText(smsTemplateEntity.getContent());
            } else {
                settingsResult.setEnrollSuccessTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_SUCCESS_TEMPLATE_TEXT_DEFAULT));
            }

            if (StringUtils.isNotBlank(notificationSettings.getEnrollAuditTemplateId())) {
                MwSmsTemplateEntity smsTemplateEntity = templateDao.getTemplateById(notificationSettings.getEnrollAuditTemplateId());
                settingsResult.setEnrollAuditTemplateText(smsTemplateEntity.getContent());
            } else {
                settingsResult.setEnrollAuditTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_TEMPLATE_TEXT_DEFAULT));
            }

            if (StringUtils.isNotBlank(notificationSettings.getEnrollAuditSuccessTemplateId())) {
                MwSmsTemplateEntity smsTemplateEntity = templateDao.getTemplateById(notificationSettings.getEnrollAuditSuccessTemplateId());
                settingsResult.setEnrollAuditSuccessTemplateText(smsTemplateEntity.getContent());
            } else {
                settingsResult.setEnrollAuditSuccessTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_SUCCESS_TEMPLATE_TEXT_DEFAULT));
            }

            if (StringUtils.isNotBlank(notificationSettings.getEnrollAuditFailTemplateId())) {
                MwSmsTemplateEntity smsTemplateEntity = templateDao.getTemplateById(notificationSettings.getEnrollAuditFailTemplateId());
                settingsResult.setEnrollAuditFailTemplateText(smsTemplateEntity.getContent());
            } else {
                settingsResult.setEnrollAuditFailTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_FAIL_TEMPLATE_TEXT_DEFAULT));
            }
            if (settingsResult.getSmsType() == null) {
                settingsResult.setSmsType(ConferenceSmsType.FS.getType());
            }
        } else {
            settingsResult = new QueryNotificationSettingsResult();
            settingsResult.setSmsType(ConferenceSmsType.FS.getType());
            settingsResult.setEnrollSuccess(false);
            settingsResult.setEnrollSuccessTemplateId(null);
            settingsResult.setEnrollSuccessTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_SUCCESS_TEMPLATE_TEXT_DEFAULT));
            settingsResult.setEnrollAudit(false);
            settingsResult.setEnrollAuditTemplateId(null);
            settingsResult.setEnrollAuditTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_TEMPLATE_TEXT_DEFAULT));
            settingsResult.setEnrollAuditSuccess(false);
            settingsResult.setEnrollAuditSuccessTemplateText(null);
            settingsResult.setEnrollAuditSuccessTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_SUCCESS_TEMPLATE_TEXT_DEFAULT));
            settingsResult.setEnrollAuditFail(false);
            settingsResult.setEnrollAuditFailTemplateId(null);
            settingsResult.setEnrollAuditFailTemplateText(I18nUtil.getStaticByKey(ConferenceManager.ENROLL_AUDIT_FAIL_TEMPLATE_TEXT_DEFAULT));
        }
        return Result.newSuccess(settingsResult);
    }

    @Override
    public Result<QueryConferenceQrCodeResult> queryConferenceQrCode(QueryConferenceQrCodeVO vo) {
        QueryConferenceQrCodeResult qrCodeResult = new QueryConferenceQrCodeResult();
        ActivityQrCodeContainer activityQrCodeContainer = activityManager.createActivityQrCode(vo.getId(), vo.getEa(), vo.getChannelValue(), vo.getExtraParam());
        qrCodeResult.setH5qrUrl(activityQrCodeContainer.getH5IndexUrl());
        qrCodeResult.setH5qrApath(activityQrCodeContainer.getH5IndexPath());
        qrCodeResult.setMiniAppQrUrl(activityQrCodeContainer.getMiniAppIndexUrl());
        qrCodeResult.setMiniAppQrApath(activityQrCodeContainer.getMiniAppIndexPath());
        return Result.newSuccess(qrCodeResult);
    }

    @Override
    public Result<QueryConferenceFormQrCodeResult> queryConferenceFormQrCode(QueryConferenceFormQrCodeVO vo) {
        QueryConferenceFormQrCodeResult qrCodeResult = new QueryConferenceFormQrCodeResult();
        ActivityEntity activityEntity = activityDAO.getById(vo.getId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.queryConferenceFormQrCode activityEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(vo.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity == null) {
            log.warn("ConferenceServiceImpl.queryConferenceFormQrCode customizeFormDataEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }

        ActivityQrCodeContainer activityQrCodeContainer =  activityManager.createActivityFormQrCode(activityEntity, customizeFormDataEntity.getId(), vo.getChannelValue());
        qrCodeResult.setH5qrUrl(activityQrCodeContainer.getH5FormUrl());
        qrCodeResult.setH5qrApath(activityQrCodeContainer.getH5FormPath());
        qrCodeResult.setMiniAppQrUrl(activityQrCodeContainer.getMiniAppFormUrl());
        qrCodeResult.setMiniAppQrApath(activityQrCodeContainer.getMiniAppFormPath());
        CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
        shortUrlArg.setUrl(activityQrCodeContainer.getH5FormUrl());
        Optional<String> shortUrlOpt = shortUrlManager.createShortUrl(shortUrlArg);
        if (!shortUrlOpt.isPresent()){
            log.warn("ConferenceServiceImpl.queryConferenceFormQrCode create short url failed vo:{} url:{}", vo, activityQrCodeContainer.getH5FormUrl());
            return Result.newError(SHErrorCode.CREATE_SHORT_URL_FAILED);
        }
        qrCodeResult.setShortUrl(shortUrlOpt.get());
        return Result.newSuccess(qrCodeResult);
    }

    @Override
    public Result<QueryConferenceQrCodeResult> resetConferenceQrCode(ResetConferenceQrCodeVO vo) {
        if (StringUtils.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QueryConferenceQrCodeResult qrCodeResult = new QueryConferenceQrCodeResult();
        boolean createResult = createQrPhoto(vo.getId(), vo.getEa());
        if (createResult) {
            PhotoEntity qrPhoto = photoManager.querySinglePhoto(PhotoTargetTypeEnum.H5_CONFERENCE_QR_CODE.getType(), vo.getId(),vo.getEa());
            if (qrPhoto == null) {
                return Result.newError(SHErrorCode.CONFERENCE_QRCODE_CREATE_FAIL);
            }
            qrCodeResult.setH5qrUrl(qrPhoto.getUrl());
            return Result.newSuccess(qrCodeResult);
        }
        return Result.newError(SHErrorCode.CONFERENCE_QRCODE_CREATE_FAIL);
    }

    @Override
    public Result<GetSignInQrUrlResult> getSignInQrUrl(GetSignInQrUrlVO vo) {
        GetSignInQrUrlResult result = new GetSignInQrUrlResult();
        ActivityQrCodeContainer activityQrCodeContainer = activityManager.createActivitySignInQrCodeAddTag(vo.getActivityId(), vo.getEa(),vo.getTags(), vo.getH5Path(), vo.getMiniappPath());
        result.setH5QrUrl(activityQrCodeContainer.getH5SignInUrl());
        result.setH5QrPath(activityQrCodeContainer.getH5SignInPath());
        result.setMiniAppQrUrl(activityQrCodeContainer.getMiniAppSignInUrl());
        result.setMiniAppPath(activityQrCodeContainer.getMiniAppSignInPath());
        result.setUrlParamMap(activityQrCodeContainer.getUrlParamMap());
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<GetTemplateDataResult>> getTemplateData() {
        List<GetTemplateDataResult> getTemplateDataResultList = Lists.newArrayList();
        List<ConferenceTemplateEntity> conferenceTemplateEntityList = conferenceTemplateDAO.getAllConferenceTemplateData();
        if (CollectionUtils.isEmpty(conferenceTemplateEntityList)) {
            log.warn("ConferenceServiceImpl.getTemplateData error conferenceTemplateEntityList is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        for (ConferenceTemplateEntity conferenceTemplateEntity : conferenceTemplateEntityList) {
            GetTemplateDataResult getTemplateDataResult = BeanUtil.copy(conferenceTemplateEntity, GetTemplateDataResult.class);
            getTemplateDataResult.setTemplateDetails(StringUtils.isNotBlank(conferenceTemplateEntity.getTemplateDetails()) ? conferenceManager.getConferenceDetailsByPath(conferenceTemplateEntity.getTemplateDetails()) : null);
            getTemplateDataResult.setTemplateCoverUrl(fileV2Manager.getUrlByPath(conferenceTemplateEntity.getTemplateCoverPath(), null, false));
            getTemplateDataResultList.add(getTemplateDataResult);
        }
        return Result.newSuccess(getTemplateDataResultList);
    }

    @Override
    public Result<CheckMarketingEventResult> checkMarketingEvent(String marketingEventId, String ea, Integer fsUserId) {
        CheckMarketingEventResult result = new CheckMarketingEventResult();
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(marketingEventId, ea);
        result.setBindConference(activityEntity != null);
        result.setBindConferenceId(activityEntity != null ? activityEntity.getId() : null);
        if (activityEntity != null) {
            result.setBindConferenceTitle(activityEntity.getTitle());
        }
        // 市场活动类型
        MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, marketingEventId);
        if (marketingEventData == null) {
            log.warn("ConferenceServiceImpl.checkMarketingEvent error marketingEventData is null marketingEventId:{}", marketingEventId);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        result.setMarketingEventType(marketingEventData.getEventType());
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryMarketingEventDetailResult> queryMarketingEventDetail(QueryMarketingEventDetailVO vo) {
        if (StringUtils.isBlank(vo.getConferenceId()) && StringUtils.isBlank(vo.getMarketingEventId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String marketingEventId = vo.getMarketingEventId();
        String ea = vo.getEa();
        if (StringUtils.isBlank(marketingEventId)) {
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
            if (activityEntity == null || StringUtils.isBlank(activityEntity.getMarketingEventId())) {
                return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
            }
            marketingEventId = activityEntity.getMarketingEventId();
            ea = activityEntity.getEa();
        }
        QueryMarketingEventDetailResult result = buildMarketingEventDetail(ea, marketingEventId);

        return Result.newSuccess(result);
    }

    @Override
    public Result<ExportEnrollsDataResult> exportConferenceParticipants(QueryConferenceParticipantsVO vo) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            ExportEnrollsDataResult enrollsDataResult =  conferenceManager.buildExportParticipantsData(vo);
            if (enrollsDataResult != null) {
                XssfExcelUtil exportResult = new XssfExcelUtil(enrollsDataResult, I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_1999));
                XSSFWorkbook xssfWorkbook = exportResult.buildXSSFWorkbook();
                pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, exportResult.getSheetName(), vo.getEa(), vo.getUserId());
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<GetEnrollImportTemplateResult> getEnrollImportTemplate(String conferenceId, Integer importType) {
        GetEnrollImportTemplateResult result = new GetEnrollImportTemplateResult();
        // 查询会议
        ActivityEntity activityEntity = activityDAO.getById(conferenceId);
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.getEnrollImportTemplate error activityEntity is null id:{}", conferenceId);
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        // 查询会议绑定表单
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(activityEntity.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity == null) {
            log.warn("ConferenceServiceImpl.getEnrollImportTemplate customizeFormDataEntity is null customizeFormDataEntity:{}", customizeFormDataEntity);
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }
        // 查询表单报名内容
        List<String> titleList = new ArrayList<>();
        for (FieldInfo fieldInfo : customizeFormDataEntity.getFormBodySetting()) {
            if (fieldInfo.getIsRequired()) {
                titleList.add(fieldInfo.getLabel() + I18nUtil.getStaticByKey(ImportConstants.REQUIRED));
            } else {
                titleList.add(fieldInfo.getLabel());
            }
        }
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1082));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_687));
        result.setDataList(buildEnrollDescriptionData(titleList.size()));
        result.setTitleList(titleList);
        result.setFileName(activityEntity.getTitle());
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    public List<List<Object>> buildEnrollDescriptionData(int titleSize) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (String description : ImportConstants.IMPORT_CONFERENCE_ENROLL_DESCRIPTION) {
            List<Object> rowData = Lists.newArrayList();
            for (int column = 0; column < titleSize; column++) {
                rowData.add(column == titleSize - 1 ? description : "");
            }
            dataList.add(rowData);
        }
        return dataList;
    }

    @Override
    public Result<ImportUserDataResult> importEnrollData(ImportUserDataVO vo) {
        // 会议查询
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getImportObjectId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.importEnrollData activityEntity is null conferenceId:{}", vo.getImportObjectId());
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        // 表单查询
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(vo.getEa(), vo.getImportObjectId(), ObjectTypeEnum.ACTIVITY.getType());
        if (customizeFormDataEntity == null) {
            log.warn("ConferenceServiceImpl.importEnrollData customizeFormDataEntity is null vo:{}", vo);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_NOT_FOUND);
        }

        ImportUserDataResult importUserDataResult = new ImportUserDataResult();
        try {
            // 下载/解析文件
            byte[] excelByte = fileV2Manager.downloadAFile(vo.getTapath(), null);
            if (excelByte == null) {
                log.warn("ConferenceServiceImpl.importEnrollData excelByte vo:{}", vo);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(vo.getTapath(), excelByte);
            if (CollectionUtils.isNotEmpty(excelData)) {
                // 校验title
                List<FieldInfo> titleCheckResult = importManager.checkConferenceImportTitleData(Arrays.asList(excelData.get(0)), customizeFormDataEntity);

                if (CollectionUtils.isEmpty(titleCheckResult)) {
                    return new Result<>(SHErrorCode.EXCEL_HAS_DIFFERENCE_TO_FORM);
                }

                // 导入数据
                List<ImportLineResultContainer> importLineResultContainerList =  importManager.saveConferenceEnrollData(titleCheckResult, excelData, customizeFormDataEntity.getId(), activityEntity.getId(), activityEntity.getMarketingEventId(), vo.getEa(), vo.getUserId());

                // 生成问题文件apath
                Long failNum = importLineResultContainerList.stream().filter(data -> !ImportErrorEnum.SUCCESS.getDesc().equals(data.getImportDesc())).count();
                String errorFileApath = null;
                if (failNum != 0) {
                    errorFileApath = importManager.buildErrorExcelFile(excelData, importLineResultContainerList);
                }

                // 保存导入记录
                String importDataId = UUIDUtil.getUUID();
                ImportDataRecordEntity importDataRecordEntity = new ImportDataRecordEntity();
                importDataRecordEntity.setId(importDataId);
                importDataRecordEntity.setEa(vo.getEa());
                importDataRecordEntity.setImportType(ImportTypeEnum.CONFERENCE_ENROLL.getType());
                importDataRecordEntity.setImportObjectId(activityEntity.getId());
                importDataRecordEntity.setImportSuccessNum(importLineResultContainerList.size() - failNum);
                importDataRecordEntity.setImportFailNum(failNum);
                importDataRecordEntity.setResultType(ImportResultTypeEnum.A_PATH_FILE.getType());
                importDataRecordEntity.setResultContent(errorFileApath);
                importDataRecordEntity.setImportUser(vo.getUserId());
                importDataRecordDAO.insertImportDataRecord(importDataRecordEntity);


                importUserDataResult.setErrorFileApath(errorFileApath);
                importUserDataResult.setSuccessNum(importLineResultContainerList.size() - failNum);
                importUserDataResult.setFailNum(failNum);


                // 异步保存CRM
                ThreadPoolUtils.execute(new Runnable() {
                    @Override
                    public void run() {
                        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = importLineResultContainerList.stream().filter(data -> data.getCustomizeFormDataUserEntity() != null)
                            .map(ImportLineResultContainer::getCustomizeFormDataUserEntity).collect(Collectors.toList());
                        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
                            // 批量查询报名数据
                            customizeFormDataManager.saveEnrollDataToCrm(customizeFormDataEntity, customizeFormDataUserEntity);
                            String campaignId = campaignMergeDataManager.addCampaignMergeDataByUserEnroll(customizeFormDataUserEntity.getId(), false);
                            customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), vo.getImportObjectId(), campaignId, vo.getEa(), null);
                            customerCustomizeFormDataManager.addEnrollObject(customizeFormDataEntity.getEa(), customizeFormDataUserEntity.getId());
                        }
                        customizeFormDataUserEntityList = null;
                    }
                }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
        } catch (Exception e) {
            log.warn("ConferenceServiceImpl.importEnrollData error vo:{}, e:{}", vo, e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        return new Result<>(SHErrorCode.SUCCESS, importUserDataResult);
    }

    @Override
    public Result<ImportUserDataResult> importInviteData(ImportUserDataVO vo) {
        // 会议查询
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getImportObjectId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.importInviteData activityEntity is null conferenceId:{}", vo.getImportObjectId());
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        try {
            // 下载/解析文件
            byte[] excelByte = fileV2Manager.downloadAFile(vo.getTapath(), null);
            if(excelByte == null) {
                log.warn("ConferenceServiceImpl.importInviteData excelByte vo:{}", vo);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(vo.getTapath(), excelByte);
            if (CollectionUtils.isEmpty(excelData)  || excelData.size() < 2) {
                log.warn("ConferenceServiceImpl.importInviteData excelData is empty vo:{}", vo);
                return new Result<>(SHErrorCode.NO_DATA);
            }
            if (!importManager.checkImportConferenceInviteDataTitle(Arrays.asList(excelData.get(0)))) {
                log.warn("ConferenceServiceImpl.importInviteData title is error, vo:{}, excelData title:{}", vo, excelData.get(0));
                return new Result<>(SHErrorCode.CONFERENCE_IMPORT_INVITE_TITLE_ERROR);
            }
            ImportUserDataResult userDataResult = importManager.saveConferenceInviteData(activityEntity.getEa(), vo.getImportObjectId(), excelData, vo.getUserId());
            return Result.newSuccess(userDataResult);
        } catch (Exception e) {
            log.warn("ConferenceServiceImpl.importInviteData error vo:{}, e:{}", vo, e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<ExportEnrollsDataResult> exportInviteData(QueryInviteParticipantVO vo) {
        ExportEnrollsDataResult result = new ExportEnrollsDataResult();
        result.setFileName(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_2175));
        ActivityEntity entity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (entity == null){
            return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
        }
        List<String> titleList = Lists.newArrayList(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70), I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_1), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_276), I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_3), I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797), I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_2180_5), I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_1077));
        result.setTitleList(titleList);

        String fsUserName = vo.getFsUserName();
        if (StringUtils.isBlank(fsUserName)) {
            fsUserName = null;
        }
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<ConferenceInviteParticipantEntity> inviteParticipantList = conferenceInviteParticipantDAO.pageQueryConferenceInviteParticipantList(vo.getConferenceId(), vo.getStatus(), fsUserName, page);
        if (CollectionUtils.isNotEmpty(inviteParticipantList)) {
            List<List<Object>> inviteInfoList = Lists.newArrayList();
            inviteParticipantList.forEach(inviteEntity -> {
                List<Object> inviteInfo = Lists.newArrayList();
                inviteInfo.add(inviteEntity.getInvitorName());
                inviteInfo.add(inviteEntity.getCompany());
                inviteInfo.add(inviteEntity.getPosition());
                inviteInfo.add(inviteEntity.getPhone());
                inviteInfo.add(inviteEntity.getEmail());
                inviteInfo.add(inviteEntity.getFsUserName());
                ConferenceInviteStatusEnum statusEnum = ConferenceInviteStatusEnum.getStatusEnum(inviteEntity.getStatus());
                inviteInfo.add(statusEnum != null ? statusEnum.getDesc() : I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_2200));

                inviteInfoList.add(inviteInfo);
            });
            result.setEnrollInfoList(inviteInfoList);
        } else {
            result.setEnrollInfoList(Lists.newArrayList());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<QueryInviteParticipantResult>> queryInviteParticipants(QueryInviteParticipantVO vo) {
        PageResult<QueryInviteParticipantResult> pageResult = new PageResult<>();
        List<QueryInviteParticipantResult> queryInviteParticipantResults = Lists.newArrayList();
        pageResult.setResult(queryInviteParticipantResults);
        pageResult.setTotalCount(0);

        ActivityEntity entity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (entity == null){
            return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
        }
        String fsUserName = vo.getFsUserName();

        if (StringUtils.isBlank(fsUserName)) {
            fsUserName = null;
        }

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<ConferenceInviteParticipantEntity> conferenceInviteParticipantEntityList = conferenceInviteParticipantDAO.
                pageQueryConferenceInviteParticipantList(vo.getConferenceId(), vo.getStatus(), fsUserName, page);
        if (CollectionUtils.isEmpty(conferenceInviteParticipantEntityList)){
            return Result.newSuccess(pageResult);
        }

        pageResult.setTotalCount(page.getTotalNum());
        for (ConferenceInviteParticipantEntity conferenceInviteParticipantEntity : conferenceInviteParticipantEntityList){
            QueryInviteParticipantResult queryInviteParticipantResult = new QueryInviteParticipantResult();
            queryInviteParticipantResult.setId(conferenceInviteParticipantEntity.getId());
            queryInviteParticipantResult.setConferenceId(conferenceInviteParticipantEntity.getConferenceId());
            queryInviteParticipantResult.setInvitorName(conferenceInviteParticipantEntity.getInvitorName());
            queryInviteParticipantResult.setCompany(conferenceInviteParticipantEntity.getCompany());
            queryInviteParticipantResult.setPhone(conferenceInviteParticipantEntity.getPhone());
            queryInviteParticipantResult.setCompany(conferenceInviteParticipantEntity.getCompany());
            queryInviteParticipantResult.setEmail(conferenceInviteParticipantEntity.getEmail());
            queryInviteParticipantResult.setFsUserId(conferenceInviteParticipantEntity.getFsUserId());
            queryInviteParticipantResult.setFsUserName(conferenceInviteParticipantEntity.getFsUserName());
            queryInviteParticipantResult.setStatus(conferenceInviteParticipantEntity.getStatus());
            queryInviteParticipantResult.setSend_status(conferenceInviteParticipantEntity.getSendStatus());
            queryInviteParticipantResult.setCreateTime(conferenceInviteParticipantEntity.getCreateTime());
            queryInviteParticipantResult.setUpdateTime(conferenceInviteParticipantEntity.getUpdateTime());
            queryInviteParticipantResults.add(queryInviteParticipantResult);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> inviteParticipants(InviteParticipantVO vo) {
        ActivityEntity conference = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (conference == null){
            return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
        }

        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByIds(vo.getCampaignIds());
        // 必须绑定活动成员数据才能邀约
        campaignMergeDataEntityList = campaignMergeDataEntityList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            log.warn("ConferenceServiceImpl.inviteParticipants campaignMergeDataEntityList is empty arg:{}", vo);
            return Result.newSuccess();
        }

        Set<Integer> userIdSet = new HashSet<>();
        List<String> campaignIds = campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
        // 是否过滤已报名数据
        if (vo.getNeedFilterEnrollData()) {
            campaignIds = campaignMergeDataDAO.queryNotEnrollDataIdByCampaignIds(campaignIds);
        }
        if (CollectionUtils.isEmpty(campaignIds)) {
            log.warn("ConferenceServiceImpl.inviteParticipants campaignIds is empty arg:{}", vo);
            return Result.newSuccess();
        }
        Map<String, Integer> ownerMap = Maps.newHashMap();
        if (vo.getType().equals(InviteParticipantTypeEnum.BUSINESS_OWNER.getType())) {
            List<String> objectIdList = campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getCampaignMembersObjId).collect(Collectors.toList());
            // 查询活动成员负责人
            Filter objectIdFilter = new Filter();
            objectIdFilter.setFieldName(CampaignMembersObjApiNameEnum.ID.getApiName());
            objectIdFilter.setOperator(OperatorContants.IN);
            objectIdFilter.setFieldValues(objectIdList);
            List<ObjectData> objectDataList = crmV2Manager.getCampaignMembersObjByFilter(vo.getEa(), -10000, Lists.newArrayList(objectIdFilter), conference.getMarketingEventId(), true);
            if (CollectionUtils.isEmpty(objectDataList)) {
                log.warn("ConferenceServiceImpl.inviteParticipants error objectDataList is null vo:{}", vo);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            List<String> ownerName = Lists.newArrayList();
            Map<String, String> ownerNameObjMap = Maps.newHashMap();
            for (ObjectData objectData : objectDataList) {
                String campaignMembersOwnerName = conferenceManager.getCampaignMembersObjBusinessOwnerByObj(objectData);
                if (StringUtils.isBlank(campaignMembersOwnerName) || "系统".equals(campaignMembersOwnerName)) {
                    continue;
                }
                ownerName.add(campaignMembersOwnerName);
                ownerNameObjMap.put(objectData.getId(), campaignMembersOwnerName);
            }
            if(CollectionUtils.isEmpty(ownerName)) {
                log.warn("ConferenceServiceImpl.inviteParticipants error ownerName is null vo:{}", vo);
                return Result.newSuccess();
            }
            Map<String, Integer> userName = fsAddressBookManager.getEmployeeByNames(vo.getEa(), ownerName);
            for (Map.Entry<String, String> entry : ownerNameObjMap.entrySet()) {
                Integer userId = userName.get(entry.getValue());
                if (userId == null) {
                    continue;
                }
                userIdSet.add(userId);
                ownerMap.put(entry.getKey(), userId);
            }
            objectDataList.clear();
            userName.clear();
        } else {
            userIdSet = Sets.newHashSet(vo.getFsUserIds());
        }

        //发送邀约企信
        String imagePath = null;
        List<NoticeEntity> noticeEntityList = noticeDAO.listNoticeByTypeAndContent(NoticeContentTypeEnum.ACTIVITY.getType(), vo.getConferenceId());
        if (CollectionUtils.isNotEmpty(noticeEntityList)){
            imagePath = noticeEntityList.get(0).getCoverApath();
        }
        if (imagePath == null){
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), vo.getConferenceId(), vo.getEa());
            if (photoEntity != null) {
                imagePath = photoEntity.getPath();
            }
        }
//        SpliceUrlFromApathArg spliceUrlFromApathArg = new SpliceUrlFromApathArg();
//        spliceUrlFromApathArg.setApath(imagePath);
//        ModelResult<SpliceUrlFromApathResult> spliceUrlFromApathResultModelResult = outFileService.spliceUrlFromApath(spliceUrlFromApathArg);
//        if (!spliceUrlFromApathResultModelResult.isSuccess()) {
//            log.error("NoticeService.sendEnterpriseSpreadNotice spliceUrlFromApath failed, spliceUrlFromApathArg={}, result={}", spliceUrlFromApathArg, spliceUrlFromApathResultModelResult);
//            return Result.newError(SHErrorCode.CONFERENCE_INVITE_PARTICIPANT_FAIL);
//        }
        String imageUrl = fileV2Manager.getUrlByPath(imagePath, vo.getEa(), false);
        sendInviteParticipantMessage(vo.getEa(), new ArrayList<>(userIdSet), imageUrl, conference, vo.getSummary(), vo.getStartTime(),
            vo.getEndTime());

        // 保存至邀约人员列表
        if (vo.getType().equals(InviteParticipantTypeEnum.BUSINESS_OWNER.getType())) {
            for (CampaignMergeDataEntity campaignMergeDataEntity : campaignMergeDataEntityList) {
                Integer owner = ownerMap.get(campaignMergeDataEntity.getCampaignMembersObjId());
                if (owner == null) {
                    continue;
                }
                campaignMergeDataManager.createOrUpdateConferenceInvitationUser(vo.getEa(), conference.getId(), owner, campaignMergeDataEntity.getId(), vo.getStartTime(), vo.getEndTime(), vo.getSummary());
            }
        } else {
            for (Integer userId : userIdSet) {
                for (String campaignId : campaignIds) {
                    campaignMergeDataManager.createOrUpdateConferenceInvitationUser(vo.getEa(), conference.getId(), userId, campaignId, vo.getStartTime(), vo.getEndTime(), vo.getSummary());
                }
            }
        }
        noticeEntityList.clear();
        return Result.newSuccess();
    }

    @Override
    public Result<List<MyInvitation>> queryMyInvite(String ea, Integer userId, String conferenceId, Integer queryStatus, String nameKey) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(conferenceId);
        if (activityEntity == null){
            return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
        }
        ConferenceInviteStatusEnum queryStatusEnum = ConferenceInviteStatusEnum.getStatusEnum(queryStatus);
        if (queryStatusEnum == null) {
            queryStatus = null;
        }
        if (StringUtils.isBlank(nameKey)) {
            nameKey = null;
        }

        List<ConferenceInviteParticipantEntity> myInvitors = conferenceInviteParticipantDAO.queryMyInvitors(conferenceId, userId, queryStatus, nameKey);
        if (CollectionUtils.isEmpty(myInvitors)){
            return Result.newSuccess();
        }

        List<String> phoneList = Lists.newArrayList();
        Map<String, ConferenceInviteParticipantEntity> phoneMap = new HashMap<>();
        for (ConferenceInviteParticipantEntity entity : myInvitors){
            phoneList.add(entity.getPhone());
            phoneMap.put(entity.getPhone(), entity);
        }

        if (CollectionUtils.isEmpty(phoneList)){
            log.warn("queryInvitationList phone list is null conferenceId:{}", conferenceId);
            return Result.newSuccess();
        }

        //根据手机号查询报名信息
        List<ConferenceInviteEnrollDTO> enrollDTOList = activityEnrollDataDAO.queryInviteEnrollList(phoneList, conferenceId);
        Map<String, ConferenceInviteEnrollDTO> enrollMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(enrollDTOList)) {
            for (ConferenceInviteEnrollDTO dto : enrollDTOList) {
                enrollMap.put(dto.getPhone(), dto);
            }
        }

        List<MyInvitation>  myInvitations = Lists.newArrayList();
        List<String> signUnParticipantLists = Lists.newArrayList();
        for(Map.Entry<String, ConferenceInviteParticipantEntity> entry : phoneMap.entrySet()){
            String key = entry.getKey();
            ConferenceInviteParticipantEntity value = entry.getValue();
            ConferenceInviteEnrollDTO enrollDTO = enrollMap.get(key);
            Integer status =  value.getStatus();
            if (enrollDTO != null){
                if (enrollDTO.getSignStatus() == null || enrollDTO.getSignStatus() == ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType()) {
                    status = ConferenceInviteStatusEnum.SIGN_UP_UN_PARTICIPATN.getStatus();
                    if (value.getStatus().intValue() != ConferenceInviteStatusEnum.SIGN_UP_UN_PARTICIPATN.getStatus()){
                        signUnParticipantLists.add(value.getId());
                    }
                }
            }
            if (status != null){
                MyInvitation myInvitation = new MyInvitation();
                myInvitation.setStatus(status);
                myInvitation.setConferenceName(activityEntity.getTitle());
                myInvitation.setInviteId(value.getId());
                myInvitation.setInvitorName(value.getInvitorName());
                myInvitation.setCompany(value.getCompany());
                myInvitations.add(myInvitation);
            }
        }
        if (CollectionUtils.isNotEmpty(signUnParticipantLists)){
            conferenceInviteParticipantDAO.updateInviteSendStatus(signUnParticipantLists, ConferenceInviteStatusEnum.SIGN_UP_UN_PARTICIPATN.getStatus());
        }

        return Result.newSuccess(myInvitations);
    }

    @Override
    public Result<PageResult<QueryInviteListByUserResult>> queryInviteListByUser(QueryInviteListByUserVO vo) {
        PageResult<QueryInviteListByUserResult> pageResult = new PageResult<>();
        List<QueryInviteListByUserResult> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.queryInviteListByUser error activityEntity is null conferenceId:{}", vo.getConferenceId());
            return Result.newError(SHErrorCode.NO_DATA);
        }
        List<Integer> allUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getFsUserIds())) {
            allUserIds.addAll(vo.getFsUserIds());
        }
        if (CollectionUtils.isNotEmpty(vo.getCircleIds())) {
            allUserIds.addAll(fsAddressBookManager.getEmployeeIdsByAllCircleIds(vo.getEa(), vo.getCircleIds()));
        }
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<ConferenceInvitationUserDTO> conferenceInvitationUserDTOList = conferenceInvitationUserDAO
            .queryLatestConferenceInvitationUserByUsers(vo.getEa(), allUserIds, vo.getConferenceId(), vo.getNameKey(), page);
        if (CollectionUtils.isEmpty(conferenceInvitationUserDTOList)) {
            return Result.newSuccess(pageResult);
        }
        Integer pageCount = conferenceInvitationUserDAO.countLatestConferenceInvitationUserByUsers(vo.getEa(), allUserIds, vo.getConferenceId(), vo.getNameKey());
        pageResult.setTotalCount(pageCount);
        List<String> ids = conferenceInvitationUserDTOList.stream().map(ConferenceInvitationUserDTO::getCampaignMergeDataId).collect(Collectors.toList());
        List<Integer> inviteFsUsers = conferenceInvitationUserDTOList.stream().map(ConferenceInvitationUserDTO::getUserId).collect(Collectors.toList());
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), inviteFsUsers, true);
        Map<String, Integer> campaignMergeDataUserMap = conferenceInvitationUserDTOList.stream()
            .collect(Collectors.toMap(ConferenceInvitationUserDTO::getCampaignMergeDataId, ConferenceInvitationUserDTO::getUserId, (v1, v2) -> v1));
        Map<String, String> invitationTextMap = Maps.newHashMap();
        for (ConferenceInvitationUserDTO conferenceInvitationUserDTO : conferenceInvitationUserDTOList) {
            if (StringUtils.isNotBlank(conferenceInvitationUserDTO.getInvitationText())) {
                invitationTextMap.put(conferenceInvitationUserDTO.getCampaignMergeDataId(), conferenceInvitationUserDTO.getInvitationText());
            }
        }
        // 查询关联对象数据
        List<PageCampaignParticipantsDTO> pageCampaignParticipantsDTOList = campaignMergeDataDAO.getCampaignMergeDataByObjIds(vo.getEa(), activityEntity.getMarketingEventId(), ids);
        if (CollectionUtils.isEmpty(pageCampaignParticipantsDTOList)) {
            return Result.newSuccess(pageResult);
        }
        List<String> campaignMembersObjIds = pageCampaignParticipantsDTOList.stream().filter(data -> StringUtils.isNotBlank(data.getCampaignMembersObjId())).map(
            PageCampaignParticipantsDTO::getCampaignMembersObjId).collect(
            Collectors.toList());
        List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(vo.getEa(), activityEntity.getMarketingEventId(), campaignMembersObjIds);
        Map<String, String> ownerNameMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(vo.getEa(), objectDataList);
        Map<String, ObjectData> campaignMemberObjIdToDataMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getId, e -> e, (v1, v2) -> v1));
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
            QueryInviteListByUserResult queryInviteListByUserResult = new QueryInviteListByUserResult();
            queryInviteListByUserResult.setId(pageCampaignParticipantsDTO.getId());
            queryInviteListByUserResult.setCampaignMembersObjId(pageCampaignParticipantsDTO.getCampaignMembersObjId());
            queryInviteListByUserResult.setName(pageCampaignParticipantsDTO.getName());
            CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(pageCampaignParticipantsDTO.getBindCrmObjectType());
            queryInviteListByUserResult.setBindApiName(campaignMergeDataObjectTypeEnum != null ? campaignMergeDataObjectTypeEnum.getApiName() : null);
            queryInviteListByUserResult.setBindObjectId(pageCampaignParticipantsDTO.getBindCrmObjectId());
            queryInviteListByUserResult.setHasEnrollData(StringUtils.isNotBlank(pageCampaignParticipantsDTO.getFormDataUserId()));
            queryInviteListByUserResult.setInviteStatus(pageCampaignParticipantsDTO.getInviteStatus());
            queryInviteListByUserResult.setBindCrmObjectType(pageCampaignParticipantsDTO.getBindCrmObjectType());
            queryInviteListByUserResult.setSignStatus(pageCampaignParticipantsDTO.getSignIn());
            queryInviteListByUserResult.setReviewStatus(pageCampaignParticipantsDTO.getReviewStatus());
            Integer fsUserId = campaignMergeDataUserMap.get(pageCampaignParticipantsDTO.getId());
            if (fsUserId != null) {
                FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(fsUserId);
                queryInviteListByUserResult.setInviteUserName(fsEmployeeMsg != null ? fsEmployeeMsg.getName() : null);
            }
            queryInviteListByUserResult.setOwnerName(ownerNameMap.get(pageCampaignParticipantsDTO.getCampaignMembersObjId()));
            queryInviteListByUserResult.setEnrollReview(activityEntity.getEnrollReview() != null ? activityEntity.getEnrollReview() : false);
            queryInviteListByUserResult.setInvitationText(invitationTextMap.get(pageCampaignParticipantsDTO.getId()));
            ObjectData objectData = campaignMemberObjIdToDataMap.get(pageCampaignParticipantsDTO.getCampaignMembersObjId());
            if (objectData != null) {
                queryInviteListByUserResult.setCampaignMembersStatus(objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName()));
            }
            resultList.add(queryInviteListByUserResult);
        }
        campaignMergeDataUserMap.clear();
        fsEmployeeMsgMap.clear();
        invitationTextMap.clear();
        conferenceInvitationUserDTOList.clear();
        pageCampaignParticipantsDTOList.clear();
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<List<QueryEnrollReviewResult>> queryEnrollReviewList(String ea, Integer userId, String conferenceId) {
        List<QueryEnrollReviewResult> reviewResults = Lists.newArrayList();
        List<String> conferenceIds = null;
        if (StringUtils.isEmpty(conferenceId)) {
            conferenceIds = conferenceReviewEmployeeDAO.queryConferenceIdsByEaAndUserId(ea, userId);
            if (CollectionUtils.isEmpty(conferenceIds)) {
                return Result.newSuccess(reviewResults);
            }
        } else {
            conferenceIds = new ArrayList<>();
            conferenceIds.add(conferenceId);
        }
        List<ConferenceIdNameDTO> conferenceIdNameDTOList = conferenceDAO.queryConferenceNameDTO(conferenceIds);
        if (CollectionUtils.isEmpty(conferenceIdNameDTOList)) {
            return Result.newSuccess(reviewResults);
        }
        List<String> marketingEventIds = conferenceIdNameDTOList.stream().map(ConferenceIdNameDTO::getMarketingEventId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingEventIds)) {
            log.warn("ConferenceServiceImpl.queryEnrollReviewList marketingEventIds is null conferenceIds:{}", conferenceIds);
            return Result.newSuccess(reviewResults);
        }
        Map<String, ConferenceIdNameDTO> conferenceIdNameDTOMap = conferenceIdNameDTOList.stream().collect(Collectors.toMap(ConferenceIdNameDTO::getConferenceId, data -> data));
        List<PageCampaignParticipantsDTO> pageCampaignParticipantsDTOList = campaignMergeDataDAO
            .queryCampaignParticipantsDataByMarketingEventIds(ea, marketingEventIds, ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus(), null);
        if (CollectionUtils.isEmpty(pageCampaignParticipantsDTOList)) {
            log.warn("ConferenceServiceImpl.queryEnrollReviewList pageCampaignParticipantsDTOList is null marketingEventIds:{}", marketingEventIds);
            return Result.newSuccess(reviewResults);
        }
    
        Set<Integer> userIds = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getSpreadFsUserId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, String> idToMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<UserRelationEntity> fsUserIdList = userRelationManager.getByFsUserIdList(ea, Lists.newArrayList(userIds));
            idToMap = fsUserIdList.stream().collect(Collectors.toMap(UserRelationEntity::getFsUserId, UserRelationEntity::getUserName, (v1, v2) -> v1));
        }
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        Set<String> campaignIds = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<CustomizeFormDataUserEntity>  customizeFormDataUserEntities = customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignIds(Lists.newArrayList(campaignIds));

        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntities)) {
            // 若开启脱敏
            if (turnOnPhoneNumberSensitive) {
                safetyManagementManager.phoneNumberSensitive(customizeFormDataUserEntities);
            }
            customizeFormDataManager.buildAreaInfoByEnrollData(customizeFormDataUserEntities);
        }
        //参会人员和报名信息的映射
        Map<String, CustomizeFormDataUserEntity> campaignIdToFormDataMap = customizeFormDataUserEntities.stream().collect(Collectors.toMap(CustomizeFormDataUserEntity::getCampaignId, Function.identity(), (v1, v2) -> v1));

        Set<String> formIds = customizeFormDataUserEntities.stream().map(CustomizeFormDataUserEntity::getFormId).collect(Collectors.toSet());
        List<CustomizeFormDataEntity> customizeFormDataEntity = customizeFormDataDAO.queryCustomizeFormDataEntityByIds(Lists.newArrayList(formIds));
        Map<String, CustomizeFormDataEntity> formMap = customizeFormDataEntity.stream().collect(Collectors.toMap(CustomizeFormDataEntity::getId, Function.identity(), (v1, v2) -> v1));

        // 建立 campaignIds 到 customizeFormDataEntity 的映射 map
        Map<String, CustomizeFormDataEntity> campaignIdToCustomizeFormDataMap = customizeFormDataUserEntities.stream().collect(Collectors.toMap(
                        CustomizeFormDataUserEntity::getCampaignId, entity -> formMap.get(entity.getFormId()), (v1, v2) -> v1));

        // 循环设置 owner 和其他字段
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
            QueryEnrollReviewResult queryEnrollReviewResult = new QueryEnrollReviewResult();
            queryEnrollReviewResult.setId(pageCampaignParticipantsDTO.getId());
            queryEnrollReviewResult.setName(pageCampaignParticipantsDTO.getName());
            queryEnrollReviewResult.setReviewStatus(pageCampaignParticipantsDTO.getReviewStatus());
            queryEnrollReviewResult.setConferenceId(pageCampaignParticipantsDTO.getActivityId());
            if (StringUtils.isNotBlank(pageCampaignParticipantsDTO.getActivityId())) {
                ConferenceIdNameDTO conferenceIdNameDTO = conferenceIdNameDTOMap.get(pageCampaignParticipantsDTO.getActivityId());
                queryEnrollReviewResult.setConferenceName(conferenceIdNameDTO != null ? conferenceIdNameDTO.getConferenceName() : null);
            }
            queryEnrollReviewResult.setCreateTime(pageCampaignParticipantsDTO.getCreateTime().getTime());

            // 设置推广人名字
            if (pageCampaignParticipantsDTO.getSpreadFsUserId()!=null) {
                queryEnrollReviewResult.setSpreadUserName(idToMap.get(pageCampaignParticipantsDTO.getSpreadFsUserId()));
            }
            CustomizeFormDataUserEntity formDataUserEntity = campaignIdToFormDataMap.get(pageCampaignParticipantsDTO.getId());
            if(formDataUserEntity!=null){
                queryEnrollReviewResult.setSubmitContent(formDataUserEntity.getSubmitContent());
            }
            CustomizeFormDataEntity formData = campaignIdToCustomizeFormDataMap.get(pageCampaignParticipantsDTO.getId());
            if(formData!=null){
                queryEnrollReviewResult.setFormBodySetting(formData.getFormBodySetting());
            }
            reviewResults.add(queryEnrollReviewResult);
        }
        return Result.newSuccess(reviewResults);
    }

    @Override
    public Result<QueryEnrollReviewDetailResult> queryEnrollReviewDetail(String ea, Integer userId, String campaignId) {
        QueryEnrollReviewDetailResult result = new QueryEnrollReviewDetailResult();
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
        if (campaignMergeDataEntity == null) {
            log.warn("ConferenceServiceImpl.queryEnrollReviewDetail campaignMergeDataEntity is null campaignId:{}", campaignId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        result.setName(campaignMergeDataEntity.getName());
        result.setCreateTime(campaignMergeDataEntity.getCreateTime().getTime());
        result.setCampaignMembersObjId(campaignMergeDataEntity.getCampaignMembersObjId());
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(campaignId));
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            log.warn("ConferenceServiceImpl.queryEnrollReviewDetail activityEnrollDataEntityList is null campaignId:{}", campaignId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO.getLatestCustomizeFormDataUserByCampaignId(campaignId);
        // 若开启脱敏
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        if (customizeFormDataUserEntity != null) {
            if (turnOnPhoneNumberSensitive) {
                safetyManagementManager.phoneNumberSensitive(customizeFormDataUserEntity);
            }
            customizeFormDataManager.buildAreaInfoByEnrollData(Lists.newArrayList(customizeFormDataUserEntity));
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataUserEntity.getFormId());
            result.setFormBodySetting(customizeFormDataEntity.getFormBodySetting());
            result.setSubmitContent(customizeFormDataUserEntity.getSubmitContent());
        } else {
            // 查询会员数据
            Map<String, String> memberMap = memberManager.getLatestAccessibleMemberIdByCampaignIds(Lists.newArrayList(campaignId));
            if (MapUtils.isNotEmpty(memberMap)) {
                String memberId = memberMap.get(campaignId);
                if(StringUtils.isNotBlank(memberId)) {
                    ObjectData memberObject = crmV2Manager.getDetail(ea, -10000,  CrmObjectApiNameEnum.MEMBER.getName(), memberId);
                    result.setName(memberObject.getName());
                    String phone = memberObject.getString("phone");
                    if (turnOnPhoneNumberSensitive && StringUtils.isNotBlank(phone)) {
                        phone = safetyManagementManager.phoneNumberStrSensitive(phone);
                    }
                    result.setPhone(phone);
                    result.setEmail(memberObject.getString("email"));
                }
            }
        }
        result.setBindCrmObjectType(campaignMergeDataEntity.getBindCrmObjectType());
        if (StringUtils.isNotBlank(campaignMergeDataEntity.getCampaignMembersObjId())) {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CampaignMembersObjApiNameEnum.API_NAME.getApiName(), campaignMergeDataEntity.getCampaignMembersObjId());
            if (objectData != null) {
                result.setCompanyName(objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName()));
                if (objectData.getOwner() != null) {
                    Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, Lists.newArrayList(objectData.getOwner()), true);
                    result.setOwner(MapUtils.isNotEmpty(fsEmployeeMsgMap) ? fsEmployeeMsgMap.get(objectData.getOwner()).getName() : null);
                }
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result updateGroupInfo(UpdateGroupInfoVO vo) {
        List<String> activityEnrollIds  = campaignMergeDataManager.campaignIdToActivityEnrollId(vo.getCampaignIds());
        if (CollectionUtils.isEmpty(activityEnrollIds)) {
            return Result.newSuccess();
        }
        ActivityEntity activityEntity = activityDAO.getById(vo.getConferenceId());
        // 活动不存在
        if (activityEntity == null) {
            return new Result (SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        String groupKey = null;
        if (CollectionUtils.isNotEmpty(vo.getGroupNames())) {
            StringJoiner key = new StringJoiner(",");
            for (String groupName : vo.getGroupNames()) {
                Integer groupUserId = conferenceManager.createGroupUserInfo(vo.getEa(), vo.getFsUserId(), vo.getConferenceId(), groupName);
                key.add(groupUserId.toString());
            }
            groupKey = key.toString();
        }
        activityEnrollDataDAO.updateConferenceUserGroup(activityEnrollIds, groupKey);
        campaignMergeDataManager.updateCampaignMergeObjUserGroup(activityEnrollIds, vo.getGroupNames());
        return Result.newSuccess();
    }

    @Override
    @Transactional
    public Result<Void> deleteParticipants(DeleteParticipantVO vo) {
       return conferenceManager.deleteParticipants(vo);
    }

    @Override
    public Result<ActivityAndSignOrReportResult> activityStatusSignOrReport(String activityId, String uid,String tagId) {
        List<TagName> tagNameList = Lists.newArrayList();
        String conferenceHexagonSiteId = null;
        if (StringUtils.isNotBlank(tagId)) {
            ConferenceTagEntity conferenceTagEntity = conferenceTagDAO.queryById(tagId);
            if (conferenceTagEntity != null) {
                tagNameList = GsonUtil.getGson().fromJson(conferenceTagEntity.getTags(), new TypeToken<List<TagName>>(){}.getType());
            }
        }
        SignActivityVo vo = new SignActivityVo();
        vo.setActivityId(activityId);
        vo.setUid(uid);
        vo.setTags(tagNameList);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<com.facishare.mankeep.api.result.ActivityAndSignOrReportResult> result = miniAppActivityService.activityStatusSignOrReport(vo);
        if (!result.isSuccess()) {
            log.warn("ConferenceServiceImpl.activityStatusSignOrReport error result:{}", result);
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }


        ActivityAndSignOrReportResult activityAndSignOrReportResult = BeanUtil.copy(result.getData(), ActivityAndSignOrReportResult.class);
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(activityId);
        if (activityEntity != null) {
            activityAndSignOrReportResult.setConferenceHexagonId(activityEntity.getActivityDetailSiteId());
        }
        PhotoEntity photoEntity = photoManager.querySinglePhotoByEa(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityId, activityEntity.getEa());
        activityAndSignOrReportResult.setCoverImageUrl(photoEntity.getUrl());
        activityAndSignOrReportResult.setCoverImageSmallUrl(StringUtils.isBlank(photoEntity.getThumbnailUrl()) ? photoEntity.getUrl() : photoEntity.getThumbnailUrl());
        return Result.newSuccess(activityAndSignOrReportResult);
    }

    @Override
    public Result activitySign(String activityId, String uid, String phone,String tagId, Boolean delaySingIn,String email) {
        ActivityEntity activityEntity = activityDAO.getById(activityId);
        if (activityEntity == null) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        //因为报名后活动成员是异步生成的，如果直接签到会有概率找不到活动成员
        if (BooleanUtils.isTrue(delaySingIn)) {
            SignInArg signInArg = new SignInArg();
            signInArg.setId(activityId);
            signInArg.setUid(uid);
            signInArg.setPhone( phone);
            signInArg.setEmail(email);
            signInArg.setTagId(tagId);
            signInArg.setFrom("miniApp");
            delayQueueSender.sendByObj(activityEntity.getEa(), signInArg, DelayQueueTagConstants.ACTIVITY_SIGN_IN, RocketMqDelayLevelConstants.THIRTY_SECOND);
            return Result.newSuccess();
        }
        List<TagName> tagNameList = Lists.newArrayList();
        if (StringUtils.isNotBlank(tagId)) {
            ConferenceTagEntity conferenceTagEntity = conferenceTagDAO.queryById(tagId);
            if (conferenceTagEntity != null) {
                tagNameList = GsonUtil.getGson().fromJson(conferenceTagEntity.getTags(), new TypeToken<List<TagName>>(){}.getType());
            }
        }
        SignActivityVo vo = new SignActivityVo();
        vo.setActivityId(activityId);
        vo.setUid(uid);
        vo.setPhone(phone);
        vo.setEmail(email);
        vo.setTags(tagNameList);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult result = miniAppActivityService.activitySign(vo);
        if (!result.isSuccess()) {
            log.warn("ConferenceServiceImpl.activitySign error error result:{}", result);
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional
    public Result<Void> addTicketManger(AddTicketMangerVO vo) {
        ActivityEntity activityEntity = activityDAO.getById(vo.getConferenceId());
        if (activityEntity == null){
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        List<Integer> delList = null;
        List<Integer> addList = null;
        List<ConferenceTicketMangerEntity> exitEntities = conferenceTicketManagerDAO.queryTicketCheckManager(vo.getConferenceId(), vo.getEa());
        if (CollectionUtils.isEmpty(vo.getTicketCheckManagerFsUserIds())) {
            delList = exitEntities.stream().map(ConferenceTicketMangerEntity::getFsUserId).collect(Collectors.toList());
        } else {
            if (CollectionUtils.isEmpty(exitEntities)) {
                addList = vo.getTicketCheckManagerFsUserIds();
            } else {
                List<Integer> existFsUserId = exitEntities.stream().map(ConferenceTicketMangerEntity::getFsUserId).collect(Collectors.toList());
                addList = vo.getTicketCheckManagerFsUserIds().stream().filter(fsUserId -> !existFsUserId.contains(fsUserId)).collect(Collectors.toList());
                delList = existFsUserId.stream().filter(fsUserId -> !vo.getTicketCheckManagerFsUserIds().contains(fsUserId)).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isNotEmpty(addList)){
            List<ConferenceTicketMangerEntity> addEntities = Lists.newArrayList();
            for (Integer addUserId : addList) {
                ConferenceTicketMangerEntity entity = new ConferenceTicketMangerEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setConferenceId(vo.getConferenceId());
                entity.setEa(vo.getEa());
                entity.setFsUserId(addUserId);
                addEntities.add(entity);
            }
            //会议增加验票人员
            conferenceTicketManagerDAO.batchInsert(addEntities);
        }

        if (CollectionUtils.isNotEmpty(delList)){
            conferenceTicketManagerDAO.deleteConferenceTicketManger(vo.getConferenceId(), vo.getEa(), delList);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<QueryTicketManagerResult> queryTicketManager(String ea, String conferenceId) {
        ActivityEntity activityEntity = activityDAO.getByIdAndEa(conferenceId, ea);
        if (activityEntity == null){
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        QueryTicketManagerResult result = new QueryTicketManagerResult();
        List<ConferenceTicketMangerEntity> entities = conferenceTicketManagerDAO.queryTicketCheckManager(conferenceId, ea);
        if (CollectionUtils.isNotEmpty(entities)){
            List<Integer> ticketCheckManagerFsUserIds = entities.stream().map(conferenceTicketMangerEntity -> conferenceTicketMangerEntity.getFsUserId()).collect(Collectors.toList());
            result.setTicketCheckManagerFsUserIds(ticketCheckManagerFsUserIds);
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> queryTicketCheckPoint(String ea, Integer fsUserId, String conferenceId) {
        ActivityEntity activityEntity = activityDAO.getByIdAndEa(conferenceId, ea);
        if (activityEntity == null){
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        Boolean check = false;
        ConferenceTicketMangerEntity entity = conferenceTicketManagerDAO.getTicketByConferenceAndUser(conferenceId, ea, fsUserId);
        if (entity == null){
            check = true;
        }

        return Result.newSuccess(check);
    }

    @Override
    public Result<GetAllEnrollDataByCampaignResult> getAllEnrollDataByCampaign(String ea, String campaignId) {
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(campaignId);
        if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
            log.warn("ConferenceServiceImpl.getAllEnrollDataByCampaign customizeFormDataUserEntityList is null campaignId:{}", campaignId);
            return Result.newSuccess();
        }
        List<String> payOrderIds = customizeFormDataUserEntityList.stream()
                .map(CustomizeFormDataUserEntity::getPayOrderId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<FsPayOrder> fsPayOrders = null;
        if (!payOrderIds.isEmpty()) {
            fsPayOrders = fsPayOrderDao.queryByIds(payOrderIds);
        }
        Map<String, FsPayOrder> fsPayOrderMap = new HashMap<>();
        Map<String, ObjectData> objectDataMap = new HashMap<>();
        if (fsPayOrders != null) {
            fsPayOrderMap = fsPayOrders.stream().collect(Collectors.toMap(FsPayOrder::getId, Function.identity(), (o, n) -> o));
            List<String> crmMemberIds = fsPayOrders.stream()
                    .map(FsPayOrder::getCrmMemberId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(crmMemberIds)) {
                SearchQuery searchQuery = new SearchQuery();
                searchQuery.setLimit(1000);
                searchQuery.addFilter("_id", crmMemberIds, FilterOperatorEnum.IN);
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> list = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MEMBER.getName(), searchQuery);
                if (null != list && null != list.getDataList()) {
                    objectDataMap = list.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, Function.identity(), (o, n) -> o));
                }
            }
        }
        customizeFormDataManager.buildAreaInfoByEnrollData(customizeFormDataUserEntityList);
        GetAllEnrollDataByCampaignResult result = new GetAllEnrollDataByCampaignResult();
        List<EnrollData> enrollDataList = Lists.newArrayList();
        result.setEnrollDataContainer(enrollDataList);
        // 根据表单分组
        List<String> formId = customizeFormDataUserEntityList.stream().map(CustomizeFormDataUserEntity::getFormId).collect(Collectors.toList());
        List<CustomizeFormDataEntity> customizeFormDataEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(formId);
        if (CollectionUtils.isEmpty(customizeFormDataEntityList)) {
            log.warn("ConferenceServiceImpl.getAllEnrollDataByCampaign customizeFor00mDataEntityList is null formId:{}", formId);
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_DELETE);
        }
        Map<String, CustomizeFormDataEntity> customizeFormDataMap = customizeFormDataEntityList.stream().collect(Collectors.toMap(CustomizeFormDataEntity::getId, data -> data, (v1, v2) -> v1));
        Map<String, EnrollData> enrollDataMap = Maps.newHashMap();
        Map<String, String> picMap = customizeFormDataManager.conversionEnrollDataPic(ea, customizeFormDataUserEntityList);
        // 若开启脱敏
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        if (turnOnPhoneNumberSensitive) {
            safetyManagementManager.phoneNumberSensitive(customizeFormDataUserEntityList);
        }
        for (CustomizeFormDataUserEntity customizeFormDataUserEntity : customizeFormDataUserEntityList) {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataMap.get(customizeFormDataUserEntity.getFormId());
            if (customizeFormDataEntity == null) {
                continue;
            }
            EnrollData existData = enrollDataMap.get(customizeFormDataEntity.getId());
            if (existData != null) {
                List<Map<String, Object>> submitContentsContainerList = existData.getSubmitContentsList();
                submitContentsContainerList.add(customizeFormDataManager.generateEnrollData(customizeFormDataUserEntity, customizeFormDataEntity.getFormBodySetting(), picMap, true));
            } else {
                existData = new EnrollData();
                existData.setFormId(customizeFormDataEntity.getId());
                existData.setFormName(customizeFormDataEntity.getFormHeadSetting().getName());
                existData.setFormBodySetting(customizeFormDataEntity.getFormBodySetting());
                Map<String, Object> enrollResult = customizeFormDataManager.generateEnrollData(customizeFormDataUserEntity, customizeFormDataEntity.getFormBodySetting(), picMap, true);
                FsPayOrder fsPayOrder = fsPayOrderMap.get(customizeFormDataUserEntity.getPayOrderId());
                if (fsPayOrder != null) {
                    enrollResult.put("crmMemberId", fsPayOrder.getCrmMemberId());
                    ObjectData objectData = objectDataMap.get(fsPayOrder.getCrmMemberId());
                    if (objectData != null) {
                        enrollResult.put("crmMemberName", objectData.getName());
                    }
                    enrollResult.put("status", fsPayOrder.getStatus());
                    enrollResult.put("goodsName", fsPayOrder.getGoodsName());
                    enrollResult.put("amount", fsPayOrder.getAmount());
                    enrollResult.put("responseTime", fsPayOrder.getResponseTime());
                }
                existData.setSubmitContentsList(Lists.newArrayList(enrollResult));
                enrollDataMap.put(customizeFormDataEntity.getId(), existData);
                enrollDataList.add(existData);
            }
        }
        customizeFormDataEntityList.clear();
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<QueryConferenceUserGroupResult>> queryConferenceUserGroup(String ea, String conferenceId) {
        List<QueryConferenceUserGroupResult> queryConferenceUserGroupResults = Lists.newArrayList();
        List<ConferenceUserGroupEntity> conferenceUserGroupEntityList = conferenceUserGroupDAO.getConferenceUserGroupByEaAndConferenceId(ea, conferenceId);
        if (CollectionUtils.isEmpty(conferenceUserGroupEntityList)) {
            return Result.newSuccess(queryConferenceUserGroupResults);
        }
        for (ConferenceUserGroupEntity conferenceUserGroupEntity : conferenceUserGroupEntityList) {
            QueryConferenceUserGroupResult queryConferenceUserGroupResult = new QueryConferenceUserGroupResult();
            queryConferenceUserGroupResult.setId(conferenceUserGroupEntity.getId());
            queryConferenceUserGroupResult.setGroupName(conferenceUserGroupEntity.getGroupName());
            queryConferenceUserGroupResults.add(queryConferenceUserGroupResult);
        }
        return Result.newSuccess(queryConferenceUserGroupResults);
    }

    @Override
    public Result<AddCampaignMembersObjResult> addCampaignMembersObj(AddCampaignMembersObjVO vo) {
        int successNum = 0;
        int size = vo.getMemberObjDetails().size();
        for (MemberObjDetail memberObjDetail : vo.getMemberObjDetails()) {
            if (size > 10) {
                ThreadPoolUtils.execute(() -> addCampaignMembers(vo, memberObjDetail), ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                successNum++;
            } else {
                successNum += addCampaignMembers(vo, memberObjDetail);
            }
        }
        AddCampaignMembersObjResult addCampaignMembersObjResult = new AddCampaignMembersObjResult();
        addCampaignMembersObjResult.setSuccessNum(successNum);
        addCampaignMembersObjResult.setErrorNum(size - successNum);
        return Result.newSuccess(addCampaignMembersObjResult);
    }

    private int addCampaignMembers(AddCampaignMembersObjVO vo, MemberObjDetail memberObjDetail) {
        try {
            CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByApiName(memberObjDetail.getApiName());
            if (campaignMergeDataObjectTypeEnum == null) {
                return 0;
            }
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), vo.getMarketingEventId());
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), memberObjDetail.getApiName());
            if (StringUtils.isNotBlank(memberObjDetail.getCompanyName())) {
                dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), memberObjDetail.getCompanyName());
            }
            if (StringUtils.isNotBlank(memberObjDetail.getName())) {
                dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), memberObjDetail.getName());
            }
            dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), memberObjDetail.getId());
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), vo.getCampaignMembersStatus());
            String campaignMemberId = crmV2Manager.addCampaignMembersObj(vo.getEa(), dataMap, vo.getFsUserId());
            return StringUtils.isNotEmpty(campaignMemberId) ? 1 : 0;
        } catch (Exception e) {
            log.error("ConferenceServiceImpl.addCampaignMembersObj error", e);
        }
        return 0;
    }

    @Override
    public Result<List<FieldInfo>> getCampaignMembersObjField(String ea) {
        List<FieldInfo> result = Lists.newArrayList();
        List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        List<String> needField = Lists.newArrayList(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName());
        for (CrmUserDefineFieldVo crmUserDefineFieldVo : crmCustomerFields) {
            if (!needField.contains(crmUserDefineFieldVo.getFieldName())) {
                continue;
            }
            FieldInfo crmFieldVO = new FieldInfo();
            crmFieldVO.setApiName(crmUserDefineFieldVo.getFieldName());
            crmFieldVO.setLabel(crmUserDefineFieldVo.getFieldCaption());
            crmFieldVO.setIsRequired(crmUserDefineFieldVo.getIsNotNull());
            crmFieldVO.setType(CrmV2FieldTypeEnum.getName(crmUserDefineFieldVo.getFieldType()));
            if (crmFieldVO.getType().equals(FieldInfo.Type.SELECT_ONE.getValue()) || crmFieldVO.getType().equals(FieldInfo.Type.SELECT_MANY.getValue())) {
                List<EnumDetailResult> enumDetailResultList = crmUserDefineFieldVo.getEnumDetails();
                List<FieldInfo.Option> options = new ArrayList<>(enumDetailResultList.size());
                for (EnumDetailResult enumDetailResult : enumDetailResultList) {
                    FieldInfo.Option option = new FieldInfo.Option();
                    option.setValue(enumDetailResult.getItemCode());
                    option.setLabel(enumDetailResult.getItemName());
                    options.add(option);
                }
                crmFieldVO.setOptions(options);
            }
            result.add(crmFieldVO);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<PersonalSettingDetailsResult> personalSettingDetails(String ea, Integer fsUserId, String conferenceId) {
        PersonalSettingDetailsResult result = new PersonalSettingDetailsResult();
        ActivityEntity activityEntity = activityDAO.getById(conferenceId);
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.conferenceSettingDetails activityEntity is null conferenceId:{}", conferenceId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 是否展示参会人员
        boolean showParticipants = false;
        if (!QywxUserConstants.isVirtualUserId(fsUserId)) {
            boolean checkResult = activityManager.checkViewMarketingEvenObjectAuth(ea, fsUserId, activityEntity.getMarketingEventId());
            if (checkResult) {
                showParticipants = true;
            }
        }
        // 是否展示待审核人员
        boolean showPendingUser = false;
        ConferenceReviewEmployeeEntity conferenceReviewEmployeeEntity = conferenceReviewEmployeeDAO.queryByEaUserIdAndConferenceId(ea, fsUserId, conferenceId);
        if (conferenceReviewEmployeeEntity != null) {
            showPendingUser = true;
        }
        // 是否展示验票
        boolean showCheckTicket = false;
        ConferenceTicketMangerEntity conferenceTicketMangerEntity = conferenceTicketManagerDAO.getTicketByConferenceAndUser(conferenceId, ea, fsUserId);
        if (conferenceTicketMangerEntity != null) {
            showCheckTicket = true;
        }
        result.setShowCheckTicket(showCheckTicket);
        result.setShowParticipants(showParticipants);
        result.setShowPendingUser(showPendingUser);
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryOwerViewConferenceStatisticsDataResult> queryOwerViewConferenceStatisticsData(QueryOwerViewConferenceStatisticsDataVO vo) {
        ActivityEntity activityEntity = activityDAO.getById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.queryOwerViewConferenceStatisticsData activityEntity is null conferenceId:{}", vo.getConferenceId());
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        QueryOwerViewConferenceStatisticsDataResult result = new QueryOwerViewConferenceStatisticsDataResult();
        List<Integer> employeeIds = getOwnViewEmployeeIdsWithDepartment(vo.getEa(),vo.getFsUserId(), vo.getEmployeeRange(), vo.getDepartmentRange());
        if (CollectionUtils.isEmpty(employeeIds)){
            return Result.newSuccess(result);
        }

        GetAllSubordinateEmployeesDtoArg arg = new GetAllSubordinateEmployeesDtoArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(vo.getEa()));
        arg.setEmployeeId(vo.getFsUserId());
        arg.setRunStatus(RunStatus.ACTIVE);
        GetAllSubordinateEmployeesDtoResult allSubordinateEmployeesDtoResult = employeeProviderService.getAllSubordinateEmployees(arg);
        log.info("allSubordinateEmployeesDtoResult:{} arg:{}", allSubordinateEmployeesDtoResult, arg);
        Set<Integer> employeeSet = null;
        if (allSubordinateEmployeesDtoResult != null && CollectionUtils.isNotEmpty(allSubordinateEmployeesDtoResult.getEmployeeDtos())){
            employeeSet = allSubordinateEmployeesDtoResult.getEmployeeDtos().stream().map(employeeDto -> employeeDto.getEmployeeId()).collect(Collectors.toSet());
        }
        if (employeeSet == null){
            employeeSet = new HashSet<>();
        }
        employeeSet.add(vo.getFsUserId());

        Set<Integer> subEmployee = new HashSet<>();
        for (Integer employeeId : employeeIds){
            if (employeeSet.contains(employeeId)){
                subEmployee.add(employeeId);
            }
        }
        employeeIds = new ArrayList<>(subEmployee);
        List<String> marketingActivityIds =  marketingActivityExternalConfigDao.listAllEmployeeActivityIdsByMarketingEventId(vo.getEa(), activityEntity.getMarketingEventId());
        //推广人数
        int spreadCount = 0;
        int enrollCount = 0;
        int signedCount = 0;
        if (CollectionUtils.isNotEmpty(marketingActivityIds) && CollectionUtils.isNotEmpty(employeeIds)) {
            spreadCount = marketingActivityEmployeeStatisticDao.queryMarketingActivitySpreadCountEmployeeIds(vo.getEa(), marketingActivityIds, employeeIds);
        }
        //报名人数
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            enrollCount = activityEnrollDataDAO.getEnrollCountBySpreadUserIds(vo.getConferenceId(), employeeIds);
        }
        //签到人数
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            signedCount = activityEnrollDataDAO.getSignedCountBySpreadUserIds(vo.getConferenceId(), employeeIds);
        }
        result.setSpreadCount(spreadCount);
        result.setEnrollCount(enrollCount);
        result.setSignCount(signedCount);

        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<QueryOwerViewConferenceEnrollResult>> queryOwerViewConferenceEnrollData(QueryOwerViewConferenceEnrollDataVO vo) {
        ActivityEntity activityEntity = activityDAO.getById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.queryOwerViewConferenceStatisticsData activityEntity is null conferenceId:{}", vo.getConferenceId());
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        GetAllSubordinateEmployeesDtoArg arg = new GetAllSubordinateEmployeesDtoArg();
        arg.setEnterpriseId(eieaConverter.enterpriseAccountToId(vo.getEa()));
        arg.setEmployeeId(vo.getFsUserId());
        arg.setRunStatus(RunStatus.ACTIVE);
        GetAllSubordinateEmployeesDtoResult allSubordinateEmployeesDtoResult = employeeProviderService.getAllSubordinateEmployees(arg);
        Set<Integer> allSubordinateUserIds = null;
        if (allSubordinateEmployeesDtoResult != null && CollectionUtils.isNotEmpty(allSubordinateEmployeesDtoResult.getEmployeeDtos())){
            allSubordinateUserIds = allSubordinateEmployeesDtoResult.getEmployeeDtos().stream().map(employeeDto -> employeeDto.getEmployeeId()).collect(Collectors.toSet());
        }
        if (allSubordinateUserIds == null){
            allSubordinateUserIds = new HashSet<>();
        }
        allSubordinateUserIds.add(vo.getFsUserId());

        PageResult<QueryOwerViewConferenceEnrollResult> pageResult = new PageResult<>();
        List<QueryOwerViewConferenceEnrollResult> queryConferenceEnrollResult = Lists.newArrayList();
        pageResult.setResult(queryConferenceEnrollResult);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);

        List<Integer> employeeIds = getOwnViewEmployeeIdsWithDepartment(vo.getEa(),vo.getFsUserId(), vo.getEmployeeRange(), vo.getDepartmentRange());
        if (CollectionUtils.isEmpty(employeeIds)){
            return Result.newSuccess(pageResult);
        }

        HashSet<Integer> ownerSubordinateSetIds = new HashSet<>();
        for (Integer employeeId : employeeIds){
            if (allSubordinateUserIds.contains(employeeId)){
                ownerSubordinateSetIds.add(employeeId);
            }
        }

        log.info("ownerSubordinateSetIds:{}", ownerSubordinateSetIds);
        if (CollectionUtils.isEmpty(ownerSubordinateSetIds)){
            return Result.newSuccess(pageResult);
        }

        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<QueryOwerViewConferenceEnrollDTO> enrollDTOList = activityEnrollDataDAO.getEnrollCampaignDataBySpreadUserIds(vo.getConferenceId(), new ArrayList<>(ownerSubordinateSetIds), vo.getKeyword(), page);
        if (CollectionUtils.isEmpty(enrollDTOList)){
            return Result.newSuccess();
        }

        pageResult.setTotalCount(page.getTotalNum());
        Map<String, String> campaignMemberMap = null;
        Set<Integer> fsUserIdSet = new HashSet<>();
        List<String> campaignMeberIds = enrollDTOList.stream().map(queryOwerViewConferenceEnrollDTO -> queryOwerViewConferenceEnrollDTO.getCampaignMemberId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(campaignMeberIds)) {
            List<ObjectData> objectDataList = conferenceManager.queryCampaignMembersObjByFilter(vo.getEa(), activityEntity.getMarketingEventId(), campaignMeberIds);
            campaignMemberMap = conferenceManager.queryCampaignMembersObjBusinessOwnerMap(vo.getEa(), objectDataList);
        }
        for (QueryOwerViewConferenceEnrollDTO dto : enrollDTOList){
            QueryOwerViewConferenceEnrollResult enrollResult = new QueryOwerViewConferenceEnrollResult();
            enrollResult.setEnrollId(dto.getEnrollId());
            enrollResult.setName(dto.getName());
            enrollResult.setCrmType(dto.getCrmType());
            enrollResult.setCrmId(dto.getCrmId());
            enrollResult.setSpreadUserId(dto.getSpreadUserId());
            enrollResult.setInviteStatus(dto.getInviteStatus());
            enrollResult.setReviewStatus(dto.getReviewStatus());
            enrollResult.setSignStatus(dto.getSignStatus());
            if (dto.getSpreadUserId() != null) {
                fsUserIdSet.add(dto.getSpreadUserId());
            }
            enrollResult.setCampaignMembersObjId(dto.getCampaignMemberId());
            if (campaignMemberMap!= null && campaignMemberMap.get(dto.getCampaignMemberId()) != null){
                String ownerIdString = campaignMemberMap.get(dto.getCampaignMemberId());
                enrollResult.setOwnerName(ownerIdString);
                if (enrollResult.getOwnerId() != null) {
                    fsUserIdSet.add(enrollResult.getOwnerId());
                }
            }
            queryConferenceEnrollResult.add(enrollResult);
        }

        log.info("fsUserIdSet:{}", fsUserIdSet);
        Map<Integer, FSEmployeeMsg> employeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), new ArrayList<>(fsUserIdSet), true);
        if (employeeMsgMap != null && CollectionUtils.isNotEmpty(queryConferenceEnrollResult)){
            for (QueryOwerViewConferenceEnrollResult conferenceEnrollResult : queryConferenceEnrollResult){
                if (conferenceEnrollResult.getOwnerId() != null && employeeMsgMap.get(conferenceEnrollResult.getOwnerId()) != null){
                    conferenceEnrollResult.setOwnerName(employeeMsgMap.get(conferenceEnrollResult.getOwnerId()).getName());
                }
                if (conferenceEnrollResult.getSpreadUserId() != null && employeeMsgMap.get(conferenceEnrollResult.getSpreadUserId()) != null){
                    conferenceEnrollResult.setSpreadName(employeeMsgMap.get(conferenceEnrollResult.getSpreadUserId()).getName());
                }
            }
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<QueryCampaignWxStatisticsResult> queryCampaignWxStatistics(String ea, Integer fsUserId, String marketingEventId) {
        // 查询企业是否绑定公众号
        List<MarketingWxServiceEntity> marketingWxServiceEntityList = marketingWxServiceDao.listByEa(ea);
        if (CollectionUtils.isEmpty(marketingWxServiceEntityList)) {
            log.warn("ConferenceServiceImpl.queryCampaignWxStatistics marketingWxServiceEntityList is null ea:{}", ea);
            QueryCampaignWxStatisticsResult wxResult = new QueryCampaignWxStatisticsResult();
            wxResult.setAddFanNum(0);
            wxResult.setConferenceFanCount(0);
            return Result.newSuccess(wxResult);
        }
        // 查询该市场活动下微信粉丝数量
        Filter marketingEventIdFilter = new Filter();
        marketingEventIdFilter.setFieldName(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName());
        marketingEventIdFilter.setOperator(OperatorContants.EQ);
        marketingEventIdFilter.setFieldValues(Lists.newArrayList(marketingEventId));
        Integer addWxFanNum = crmV2Manager.getObjTotalCountByFilter(ea, -10000, CrmObjectApiNameEnum.WECHAT.getName(), Lists.newArrayList(marketingEventIdFilter));
        // 查询参会人员中多少微信粉丝 目前该字段已不用，暂时注释掉
        //int conferenceFanCount = getConferenceFanCount(ea, marketingEventId);
        int conferenceFanCount = 0;
        QueryCampaignWxStatisticsResult result = new QueryCampaignWxStatisticsResult();
        result.setAddFanNum(addWxFanNum);
        result.setConferenceFanCount(conferenceFanCount);
        return Result.newSuccess(result);
    }

    private int getConferenceFanCount(String ea, String marketingEventId) {
        List<String> campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEvent(ea, marketingEventId);
        if (CollectionUtils.isEmpty(campaignIds)) {
            return 0;
        }
        AtomicInteger conferenceFanCount = new AtomicInteger(0);
        // 分页查询每页100条
        PageUtil<String> campaignIdPage = new PageUtil<>(campaignIds, 1000);
        CountDownLatch countDownLatch = new CountDownLatch(campaignIdPage.getPageCount());
        for (int i = 1; i <= campaignIdPage.getPageCount(); i++) {
            int j = i;
            ThreadPoolUtils.executeWithNewThread(ea + "-queryCampaignWxStatistics", () -> {
                Optional<Map<String, UserMarketingWxServiceAccountRelationEntity>> optional = userMarketingAccountManager
                        .getUserMarketingWxServiceAccountRelationEntitiesByCampaignIds(ea, null, campaignIdPage.getPagedList(j));
                optional.ifPresent(e -> e.values().stream().filter(Objects::nonNull).forEach(value -> conferenceFanCount.incrementAndGet()));
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("ConferenceServiceImpl.queryCampaignWxStatistics InterruptedException error, ea: {} marketingEventId: {}", ea, marketingEventId, e);
        }
        return conferenceFanCount.get();
    }

    @Override
    public Result<List<QueryAllCampaignDataResult>> queryAllCampaignData(QueryAllCampaignDataVO vo) {
        List<QueryAllCampaignDataResult> queryAllCampaignDataResultList = Lists.newArrayList();
        if (vo.getQueryType().equals(QueryAllCampaignQueryTypeEnum.CONFERENCE.getType())) {
            doQueryAllConferenceCampaign(vo, queryAllCampaignDataResultList);
        } else if (vo.getQueryType().equals(QueryAllCampaignQueryTypeEnum.LIVE.getType())) {
            doQueryAllLiveCampaign(vo, queryAllCampaignDataResultList);
        } else if (vo.getQueryType().equals(QueryAllCampaignQueryTypeEnum.MARKETING_ACTIVITY.getType())) {
            doQueryAllMarketingActivityCampaign(vo, queryAllCampaignDataResultList);
        }
        return Result.newSuccess(queryAllCampaignDataResultList);
    }

    private void doQueryAllConferenceCampaign(QueryAllCampaignDataVO vo, List<QueryAllCampaignDataResult> queryAllCampaignDataResultList) {
        String groupUserId = null;
        if (CollectionUtils.isNotEmpty(vo.getGroupUserId())) {
            groupUserId = vo.getGroupUserId().stream().collect(Collectors.joining(","));
        }
        List<PageCampaignParticipantsDTO> campaignParticipantsDTOList = campaignMergeDataDAO
            .queryCampaignParticipantsData(vo.getEa(), vo.getMarketingEventId(), vo.getInviteStatus(), vo.getSignStatus(), vo.getKeyword(), vo.getFilterPhoneUser(),
                vo.getSaveCrmStatus(), vo.getReviewStatus(), vo.getChannelValue(), groupUserId);
        if (CollectionUtils.isEmpty(campaignParticipantsDTOList)) {
            return;
        }
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : campaignParticipantsDTOList) {
            QueryAllCampaignDataResult queryAllCampaignDataResult = new QueryAllCampaignDataResult();
            queryAllCampaignDataResult.setId(pageCampaignParticipantsDTO.getId());
            queryAllCampaignDataResult.setName(pageCampaignParticipantsDTO.getName());
            queryAllCampaignDataResult.setPhone(pageCampaignParticipantsDTO.getPhone());
            queryAllCampaignDataResultList.add(queryAllCampaignDataResult);
        }
    }

    private void doQueryAllLiveCampaign(QueryAllCampaignDataVO vo, List<QueryAllCampaignDataResult> queryAllCampaignDataResultList) {
        Integer ei = eieaConverter.enterpriseAccountToId(vo.getEa());
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, vo.getMarketingEventId());
        if (marketingLiveEntity == null) {
            return;
        }
        List<PageCampaignLiveDTO> pageCampaignLiveDTOList = campaignMergeDataDAO
            .queryCampaignLiveData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getViewLiveStatus(), vo.getLiveReplayStatus(), vo.getInteractiveStatus(), vo.getChannelValue(),
                vo.getSaveCrmStatus(), marketingLiveEntity.getPlatform());
        if (CollectionUtils.isEmpty(pageCampaignLiveDTOList)) {
            return;
        }
        for (PageCampaignLiveDTO pageCampaignLiveDTO : pageCampaignLiveDTOList) {
            QueryAllCampaignDataResult queryAllCampaignDataResult = new QueryAllCampaignDataResult();
            queryAllCampaignDataResult.setId(pageCampaignLiveDTO.getId());
            queryAllCampaignDataResult.setName(pageCampaignLiveDTO.getName());
            queryAllCampaignDataResult.setPhone(pageCampaignLiveDTO.getPhone());
            queryAllCampaignDataResultList.add(queryAllCampaignDataResult);
        }
    }

    private void doQueryAllMarketingActivityCampaign(QueryAllCampaignDataVO vo, List<QueryAllCampaignDataResult> queryAllCampaignDataResultList) {
        List<BaseCampaignDTO> campaignDTOList = campaignMergeDataDAO.queryCampaignEnrollData(vo.getEa(), vo.getMarketingEventId(), vo.getKeyword(), vo.getChannelValue(), vo.getSaveCrmStatus());
        if (CollectionUtils.isEmpty(campaignDTOList)) {
            return;
        }
        for (BaseCampaignDTO baseCampaignDTO : campaignDTOList) {
            QueryAllCampaignDataResult queryAllCampaignDataResult = new QueryAllCampaignDataResult();
            queryAllCampaignDataResult.setId(baseCampaignDTO.getId());
            queryAllCampaignDataResult.setName(baseCampaignDTO.getName());
            queryAllCampaignDataResult.setPhone(baseCampaignDTO.getPhone());
            queryAllCampaignDataResultList.add(queryAllCampaignDataResult);
        }
    }

    @Override
    public Result<BuildCrmObjectByEnrollDataResult> buildCrmObjectByCampaignId(String campaignId, String ea, Integer fsUserId) {
        // 判断数据来源
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(campaignId);
        if (campaignMergeDataEntity == null) {
            log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error campaignMergeDataEntity is null campaignId:{}", campaignId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (campaignMergeDataEntity.getSourceType().equals(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType())) {
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(campaignId);
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error customizeFormDataUserEntityList is null");
                return Result.newError(SHErrorCode.NO_DATA);
            }
            CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntityList.get(0);
           /* if (!customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())) {
                log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error SaveCrmStatus  customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                return Result.newError(SHErrorCode.NO_DATA);
            }*/
            BuildCrmObjectByEnrollDataArg buildCrmObjectByEnrollDataArg = new BuildCrmObjectByEnrollDataArg();
            buildCrmObjectByEnrollDataArg.setEa(ea);
            buildCrmObjectByEnrollDataArg.setFsUserId(fsUserId);
            buildCrmObjectByEnrollDataArg.setEnrollId(customizeFormDataUserEntity.getId());
            return customizeFormDataService.buildCrmObjectByEnrollData(buildCrmObjectByEnrollDataArg);
        } else if (campaignMergeDataEntity.getSourceType().equals(CampaignMergeDataSourceTypeEnum.MEMBERS_OBJ.getType())) {
            List<MemberAccessibleCampaignEntity> memberAccessibleCampaignEntityList = memberAccessibleCampaignDAO.queryMemberAccessibleCampaignByCampaignId(campaignId);
            if (CollectionUtils.isEmpty(memberAccessibleCampaignEntityList)) {
                log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error memberAccessibleCampaignEntityList is null campaignId:{}", campaignId);
                return Result.newError(SHErrorCode.NO_DATA);
            }
            MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignEntityList.get(0);
            /*if (!memberAccessibleCampaignEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())) {
                log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error SaveCrmStatus  memberAccessibleCampaignEntity:{}", memberAccessibleCampaignEntity);
                return Result.newError(SHErrorCode.NO_DATA);
            }*/
            return memberManager.buildCrmObjectByMemberData(ea, memberAccessibleCampaignEntity.getId());
        }
        log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error type campaignId:{}", campaignId);
        return Result.newError(SHErrorCode.SYSTEM_ERROR);
    }

    @Override
    public Result bindCrmObjectByCampaignId(BindCrmObjectByCampaignIdVO vo) {
        // 判断数据来源
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(vo.getCampaignId());
        if (campaignMergeDataEntity == null) {
            log.warn("ConferenceServiceImpl.bindCrmObjectByCampaignId error campaignMergeDataEntity is null campaignId:{}", vo.getCampaignId());
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (campaignMergeDataEntity.getSourceType().equals(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType())) {
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignId(vo.getCampaignId());
            if (CollectionUtils.isEmpty(customizeFormDataUserEntityList)) {
                log.warn("ConferenceServiceImpl.bindCrmObjectByCampaignId error customizeFormDataUserEntityList is null");
                return Result.newError(SHErrorCode.NO_DATA);
            }
            CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntityList.get(0);
            if (!customizeFormDataUserEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())) {
                log.warn("ConferenceServiceImpl.bindCrmObjectByCampaignId error SaveCrmStatus  customizeFormDataUserEntity:{}", customizeFormDataUserEntity);
                return Result.newError(SHErrorCode.NO_DATA);
            }
            BindEnrollDataAndCrmObjectArg arg = new BindEnrollDataAndCrmObjectArg();
            arg.setEa(vo.getEa());
            arg.setFsUserId(vo.getFsUserId());
            arg.setObjectId(vo.getObjectId());
            arg.setObjectApiName(vo.getObjectApiName());
            arg.setEnrollId(customizeFormDataUserEntity.getId());
            return customizeFormDataService.bindEnrollDataAndCrmObject(arg);
        } else if (campaignMergeDataEntity.getSourceType().equals(CampaignMergeDataSourceTypeEnum.MEMBERS_OBJ.getType())) {
            List<MemberAccessibleCampaignEntity> memberAccessibleCampaignEntityList = memberAccessibleCampaignDAO.queryMemberAccessibleCampaignByCampaignId(vo.getCampaignId());
            if (CollectionUtils.isEmpty(memberAccessibleCampaignEntityList)) {
                log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error memberAccessibleCampaignEntityList is null campaignId:{}", vo.getCampaignId());
                return Result.newError(SHErrorCode.NO_DATA);
            }
            MemberAccessibleCampaignEntity memberAccessibleCampaignEntity = memberAccessibleCampaignEntityList.get(0);
            if (!memberAccessibleCampaignEntity.getSaveCrmStatus().equals(SaveCrmStatusEnum.TO_BE_CONFIRMED.getValue())) {
                log.warn("ConferenceServiceImpl.buildCrmObjectByCampaignId error SaveCrmStatus  memberAccessibleCampaignEntity:{}", memberAccessibleCampaignEntity);
                return Result.newError(SHErrorCode.NO_DATA);
            }
            return memberManager.bindMemberAndCrmObj(vo.getEa(), memberAccessibleCampaignEntity.getId(), vo.getObjectId(), vo.getObjectApiName());
        }
        log.warn("ConferenceServiceImpl.bindCrmObjectByCampaignId error type campaignId:{}", vo.getCampaignId());
        return Result.newError(SHErrorCode.SYSTEM_ERROR);
    }

    @Override
    public Result<GetConferenceTimeStatusResult> getConferenceTimeStatus(GetConferenceTimeStatusVO vo) {
        ActivityEntity activityEntity = null;
        if (StringUtils.isNotBlank(vo.getConferenceId())) {
            activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        }
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.getConferenceTimeStatus activityEntity is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        GetConferenceTimeStatusResult result = new GetConferenceTimeStatusResult();
        result.setTimeStatus(conferenceManager.getConferenceTimeFlowStatus(activityEntity));
        return Result.newSuccess(result);
    }

    @Override
    public Result signInByEnrollField(SignInByEnrollFieldVO vo) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceByMarketingEventId(vo.getMarketingEventId(), vo.getEa());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.signInByEnrollField activityEntity is null vo:{}", vo);
            return new Result(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        SignInByEnrollFieldEnum signInByEnrollFieldEnum = SignInByEnrollFieldEnum.getByType(vo.getFieldType());
        if (signInByEnrollFieldEnum == null) {
            log.warn("ConferenceServiceImpl.signInByEnrollField signInByEnrollFieldEnum is null fieldType:{}", vo.getFieldType());
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        // 查询报名数据
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserDAO
            .queryDataByEnrollFieldAndEventId(signInByEnrollFieldEnum.getField(), vo.getEnrollContent(), vo.getMarketingEventId());
        if (customizeFormDataUserEntity == null) {
            log.warn("ConferenceServiceImpl.signInByEnrollField customizeFormDataUserEntity is null");
            return Result.newError(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
        }
        String userPhone = StringUtils.isNotBlank(customizeFormDataUserEntity.getSubmitContent().getPhone()) ? customizeFormDataUserEntity.getSubmitContent().getPhone() : null;
        // 查询所有相同手机的参会人员id
        List<String> needSetCampaignId = activityManager.mergeSamePhoneCampaignIdByEnrollData(activityEntity.getEa(), activityEntity.getMarketingEventId(), Lists.newArrayList(customizeFormDataUserEntity));
        if (CollectionUtils.isEmpty(needSetCampaignId)) {
            log.warn("ConferenceServiceImpl.signInByEnrollField customizeFormDataUserEntityList is null vo:{}", vo);
            return new Result<>(SHErrorCode.CUSTOMIZE_FORM_DATA_USER_NOT_FOUND);
        }
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(needSetCampaignId);
        boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
        if (!reviewResult) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
        }
        boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
        if (allSignIn) {
            return Result.newSuccess();
        }
        campaignMergeDataManager.updateSignInStatus(activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList()), ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
        activityManager.handleActivitySignInOtherProcess(needSetCampaignId, activityEntity, userPhone, customizeFormDataUserEntity.getOpenId(), customizeFormDataUserEntity.getWxAppId(), customizeFormDataUserEntity.getFingerPrint(), customizeFormDataUserEntity.getEnrollUserEa(), customizeFormDataUserEntity.getEnrollUserFsUid());
        return Result.newSuccess();
    }

    @Override
    public Result<ImportUserDataResult> importSignInData(ImportSignInDataVO vo) {
        if (executeTaskDetailManager.checkTaskAndAddIfNotExist(vo.getEa(), ExecuteTaskDetailTypeEnum.IMPORT_CONFERENCE_SIGN_IN_DATA, vo.getMarketingEventId())) {
            return Result.newError(SHErrorCode.TASK_IN_PROGRESS);
        }
        //异步处理数据
        ThreadPoolUtils.executeWithTraceContext(() -> {
            try {
                asnycImportSignInData(vo);
            }catch (Exception e){
                log.warn("ConferenceServiceImpl.importSignInData error e:{}", e);
            }finally {
                executeTaskDetailManager.taskComplete(vo.getEa(), ExecuteTaskDetailTypeEnum.IMPORT_CONFERENCE_SIGN_IN_DATA, vo.getMarketingEventId());
            }
        },ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    private void asnycImportSignInData(ImportSignInDataVO arg) {
        Long successNum = 0L;
        Long failNum = 0L;
        List<String> phoneList = Lists.newArrayList();
        List<String> errorFileTitleList = Lists.newArrayList();
        List<List<Object>> excelContent = Lists.newArrayList();
        errorFileTitleList.add(I18nUtil.get(I18nKeyEnum.MARK_CUSTOMIZEFORMDATAUSERMANAGER_QUERYMULTIPLEFORMUSERDATAMANAGER_594));
        errorFileTitleList.add(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_IMPORTMANAGER_625));
        // 读取文件手机号
        byte[] excelByte = fileV2Manager.downloadAFile(arg.getFilePath(), null);
        if (excelByte == null) {
            log.warn("ConferenceServiceImpl.importSignInData error excelByte is null path:{}", arg.getFilePath());
            return;
        }
        List<String[]> excelData = ReadExcelUtil.readExcelByApathAndExcelByte(arg.getFilePath(), excelByte);
        if (CollectionUtils.isEmpty(excelData)) {
            log.warn("ConferenceServiceImpl.importSignInData error excelData is null path:{}", arg.getFilePath());
            return;
        }
        phoneList = excelData.stream().map(data -> data[0]).collect(Collectors.toList());
        phoneList = phoneList.stream().filter(data -> (StringUtils.isNotBlank(data) && !data.equals("手机号"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneList)) {
            log.warn("ConferenceServiceImpl.importSignInData error phoneList is null path:{}", arg.getFilePath());
            return;
        }
        for (String phone : phoneList) {
            phone = phone.trim();
            Result handleResult = conferenceManager.signInUserByPhone(arg.getEa(), arg.getMarketingEventId(), phone);
            if (handleResult.isSuccess()) {
                successNum = successNum + 1;
            } else {
                // 将失败数据保存
                List<Object> rowData = Lists.newArrayList();
                rowData.add(phone);
                rowData.add(handleResult.getErrMsg());
                excelContent.add(rowData);
                failNum = failNum + 1;
            }
        }

        //发送消息到文件助手
        String text = null;
        if (failNum > 0) {
            text = "您于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "导入更新签到状态数据，更新成功" + successNum + "条，更新失败" + failNum + "条，具体请查看以下存入结果。";
        }else {
            text = "您于" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "导入更新签到状态数据，已全部更新成功，共" + successNum + "条。";
        }
        pushSessionManager.pushTextToFileAssistant(text, arg.getEa(), arg.getFsUserId());
        if (failNum > 0) {
            String formattedDate =  new SimpleDateFormat("yyyyMMdd").format(new Date());
            String sheetName = I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3507) + formattedDate + ".xlsx";
            XSSFWorkbook workbook = importManager.buildExcelFileAndGetPath(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3506), errorFileTitleList, excelContent);
            pushSessionManager.pushExcelToFileAssistant(workbook, sheetName, arg.getEa(), arg.getFsUserId());
        }
    }

    @Override
    public Result<GetSignInSettingResult> getSignInSetting(GetSignInSettingVO vo) {
        return conferenceManager.getSignInSetting(vo.getActivityId());
    }

    @Override
    public Result updateSignInSetting(UpdateSignInSettingVO vo) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getActivityId());
        if (activityEntity == null) {
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        ConferenceSignInJumpSettingEntity conferenceSignInJumpSettingEntity = new ConferenceSignInJumpSettingEntity();
        conferenceSignInJumpSettingEntity.setId(UUIDUtil.getUUID());
        conferenceSignInJumpSettingEntity.setEa(activityEntity.getEa());
        conferenceSignInJumpSettingEntity.setActivityId(activityEntity.getId());
        if (vo.getJumpObjectId() != null && vo.getJumpObjectType() != null) {
            conferenceSignInJumpSettingEntity.setJumpObjectId(vo.getJumpObjectId());
            conferenceSignInJumpSettingEntity.setJumpObjectType(vo.getJumpObjectType());
        }else{
            conferenceSignInJumpSettingEntity.setJumpUrl(vo.getJumpUrl());
        }
        conferenceSignInJumpSettingDAO.upsertConferenceSignInJumpSetting(conferenceSignInJumpSettingEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<GetInvitationCommonSettingResult> getInvitationCommonSetting(GetInvitationCommonSettingVO vo) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.getInvitationCommonSetting error activityEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        ConferenceInvitationCommonSettingEntity conferenceInvitationCommonSettingEntity = conferenceInvitationCommonSettingDAO
            .queryCommonSettingByConferenceId(activityEntity.getEa(), activityEntity.getId());
        GetInvitationCommonSettingResult getInvitationCommonSettingResult = new GetInvitationCommonSettingResult();
        getInvitationCommonSettingResult.setCurrentStaffInInvitees(false);
        if (conferenceInvitationCommonSettingEntity == null) {
            getInvitationCommonSettingResult.setOpenAddInvitees(false);
            getInvitationCommonSettingResult.setOpenInvitationPoster(false);
        } else {
            getInvitationCommonSettingResult.setOpenInvitationPoster(conferenceInvitationCommonSettingEntity.getOpenInvitationPoster());
            getInvitationCommonSettingResult.setOpenAddInvitees(conferenceInvitationCommonSettingEntity.getOpenAddInvitees());
            getInvitationCommonSettingResult.setAddressBookContainer(conferenceInvitationCommonSettingEntity.getAddInviteesStaff());
            getInvitationCommonSettingResult.setCountAllStaff(0);
            HashSet<Integer> set = Sets.newHashSet();
            if (conferenceInvitationCommonSettingEntity.getAddInviteesStaff() != null) {
                if(CollectionUtils.isNotEmpty(conferenceInvitationCommonSettingEntity.getAddInviteesStaff().getFsUserId())){
                    set.addAll(conferenceInvitationCommonSettingEntity.getAddInviteesStaff().getFsUserId());
                }
                List<Integer> departmentList = conferenceInvitationCommonSettingEntity.getAddInviteesStaff().getDepartment();
                if(CollectionUtils.isNotEmpty(departmentList)){
                    List<Integer> employeeIdsByCircleIds = fsAddressBookManager.getEmployeeIdsByCircleIds(activityEntity.getEa(), departmentList);
                    if(CollectionUtils.isNotEmpty(employeeIdsByCircleIds)){
                        set.addAll(employeeIdsByCircleIds);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(set) && set.contains(vo.getFsUserId())) {
                getInvitationCommonSettingResult.setCurrentStaffInInvitees(true);
            }
            getInvitationCommonSettingResult.setCountAllStaff(set.size());
            getInvitationCommonSettingResult.setAllStaff(Lists.newArrayList(set));
        }
        return Result.newSuccess(getInvitationCommonSettingResult);
    }

    @Override
    public Result upsertInvitationCommonSetting(UpsertInvitationCommonSettingVO vo) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.upsertInvitationCommonSetting error activityEntity is null vo:{}", vo);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        ConferenceInvitationCommonSettingEntity conferenceInvitationCommonSettingEntity = new ConferenceInvitationCommonSettingEntity();
        conferenceInvitationCommonSettingEntity.setId(UUIDUtil.getUUID());
        conferenceInvitationCommonSettingEntity.setEa(vo.getEa());
        conferenceInvitationCommonSettingEntity.setOpenInvitationPoster(vo.getOpenInvitationPoster() != null ? vo.getOpenInvitationPoster() : false);
        conferenceInvitationCommonSettingEntity.setOpenAddInvitees(vo.getOpenAddInvitees() != null ? vo.getOpenAddInvitees() : false);
        conferenceInvitationCommonSettingEntity.setConferenceId(vo.getConferenceId());
        conferenceInvitationCommonSettingEntity.setAddInviteesStaff(vo.getAddressBookContainer());
        conferenceInvitationCommonSettingDAO.upsertInvitationCommonSetting(conferenceInvitationCommonSettingEntity);
        return Result.newSuccess();
    }

    @Override
    public Result addInvitationUserByCrmObj(AddInvitationUserByCrmObjVO vo) {
        try {
            CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(vo.getCrmObjectType());
            if (campaignMergeDataObjectTypeEnum == null) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            ObjectData objectData = crmV2Manager.getDetail(vo.getEa(), -10000, campaignMergeDataObjectTypeEnum.getApiName(), vo.getCrmObjectId());
            if (objectData == null) {
                log.warn("ConferenceServiceImpl.addInvitationUserByCrmObj error objectData is null");
                return Result.newError(SHErrorCode.NO_DATA);
            }
            ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getActivityId());
            if (activityEntity == null) {
                log.warn("ConferenceServiceImpl.addInvitationUserByCrmObj error activityEntity is null");
                return Result.newError(SHErrorCode.NO_DATA);
            }
            String campaignMergeDataId = campaignMergeDataManager.queryCampaignMergeDataWithTry(vo.getEa(), activityEntity.getMarketingEventId(), vo.getCrmObjectType(), vo.getCrmObjectId(), 5, 1000L);
            if(StringUtils.isBlank(campaignMergeDataId)) {
                log.warn("ConferenceServiceImpl.addInvitationUserByCrmObj error campaignMergeDataId is null");
                return Result.newError(SHErrorCode.NO_DATA);
            }
            String invitationText = I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3618)+ activityEntity.getTitle() +"\n" +
                                    "会议时间："+ DateFormatUtils.format(activityEntity.getStartTime(), "yyyy-MM-dd HH:mm") +" - "+ DateFormatUtils.format(activityEntity.getEndTime(), "yyyy-MM-dd HH:mm") +"\n" +
                                    I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3620) + activityEntity.getLocation();
            campaignMergeDataManager.createOrUpdateConferenceInvitationUser(vo.getEa(), activityEntity.getId(), vo.getFsUserId(), campaignMergeDataId, new Date().getTime(), new Date().getTime(), invitationText);
        } catch (Exception e) {
            log.warn("ConferenceServiceImpl.addInvitationUserByCrmObj error e:", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    /**
     * 异步添加活动成员
     * --如果添加数量小于3， 同步处理。 如果添加数量大于3， 异步处理。不能超过100个
     * @param vo
     * @return
     */
    @Override
    public Result asynBatchAddInvitationUser(AsynBatchAddInvitationUserByCrmObjVO vo) {
        if (CollectionUtils.isEmpty(vo.getCrmObjectIdList())) {
            return Result.newSuccess();
        }
        if (vo.getCrmObjectIdList().size() > 100) {
            return Result.newError(SHErrorCode.ADD_CAMPAIGN_MEMBER_BEYOND_LIMIT);
        }

        String objectApiName = CampaignMergeDataObjectTypeEnum.getApiNameByType(vo.getCrmObjectType());
        if (objectApiName == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        try {
            if (vo.getCrmObjectIdList().size() <= 3) {
                batchAddCampaignMemberByCrmData(vo);
            } else {
                //异步执行
                new NamedThreadFactory("asynBatchAddInvitationUser").newThread(() -> {
                    batchAddCampaignMemberByCrmData(vo);
                }).start();
            }
        }catch (Exception e) {
            log.error("ConferenceServiceImpl.asynBatchAddInvitationUser vo:{} error e:", vo, e);
        }

        return Result.newSuccess();
    }

    private void batchAddCampaignMemberByCrmData(AsynBatchAddInvitationUserByCrmObjVO vo) {
        if (CollectionUtils.isEmpty(vo.getCrmObjectIdList())) {
            return;
        }
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getActivityId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.addInvitationUserByCrmObj error activityEntity is null");
        }

        for (String crmObjectId : vo.getCrmObjectIdList()) {
            String campaignMergeDataId = campaignMergeDataManager.addCampaignMemberByCrmData(vo.getEa(), vo.getFsUserId(), activityEntity.getMarketingEventId(), vo.getCrmObjectType(), crmObjectId);
            if (StringUtils.isEmpty(campaignMergeDataId)) {
                continue;
            }
            String invitationText = I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3618) + activityEntity.getTitle() + "\n" +
                    "会议时间：" + DateFormatUtils.format(activityEntity.getStartTime(), "yyyy-MM-dd HH:mm") + " - " + DateFormatUtils.format(activityEntity.getEndTime(), "yyyy-MM-dd HH:mm") + "\n" +
                    I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3620) + activityEntity.getLocation();
            campaignMergeDataManager.createOrUpdateConferenceInvitationUser(vo.getEa(), activityEntity.getId(), vo.getFsUserId(), campaignMergeDataId, new Date().getTime(), new Date().getTime(), invitationText);
            conferenceManager.createConferenceEnrollAttachedInfoByCampaignObj(activityEntity.getMarketingEventId(), campaignMergeDataId, CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue(), vo.getEa());
            customizeTicketManager.createConferenceCustomizeTicket(ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getId(), campaignMergeDataId, vo.getEa(), null);
           
        }
    }

    @Override
    public Result changeInvitationStatus(ChangeInvitationStatusVO vo) {
        // 更改邀约状态
        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(vo.getInviteId());
        if (campaignMergeDataEntity != null) {
            campaignMergeDataManager.updateCampaignMergeDataInviteStatus(vo.getStatus(), vo.getInviteId(), true);
        } else {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess();
    }

    @Override
    public Result updateConferenceContent(UpdateConferenceContentVO vo) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (activityEntity == null) {
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        String detailPath = conferenceManager.getConferencePath(vo.getConferenceDetails());
        conferenceDAO.updateConferenceContent(detailPath, activityEntity.getId());
        return Result.newSuccess();
    }

    @Override
    public Result<GetMarketingActivityChannelByIdResult> getMarketingActivityChannelById(String marketingActivityId) {
        String marketingActivityChannel = spreadChannelManager.getChannelByMarketingActivityId(marketingActivityId, null);
        return Result.newSuccess(new GetMarketingActivityChannelByIdResult(marketingActivityChannel));
    }

    @Override
    public Result sendConferenceEnrollNoticeTask() {
       ThreadPoolUtils.execute(() -> conferenceManager.sendConferenceEnrollNoticeTask(), ThreadPoolTypeEnums.HEAVY_BUSINESS);
       return Result.newSuccess();
    }

    private List<Integer> getOwnViewEmployeeIdsWithDepartment(String ea, Integer fsUserId, List<Integer> employeeRange, List<Integer> deparmentIds){
        Set<Integer> targetEmployeeIdSet = new HashSet<>(fsUserId);
        if (CollectionUtils.isNotEmpty(employeeRange) || CollectionUtils.isNotEmpty(deparmentIds) && !deparmentIds.contains(999999)){
            if (CollectionUtils.isNotEmpty(employeeRange)){
                targetEmployeeIdSet.addAll(employeeRange);
            }
            if (CollectionUtils.isNotEmpty(deparmentIds)){
                targetEmployeeIdSet.addAll(fsAddressBookManager.getEmployeeIdsByCircleIds(ea, deparmentIds));
            }
        }

        return new ArrayList<>(targetEmployeeIdSet);
    }

    @Override
    public Result<ConsumeTicketResult> verifyEnrollPhone(VerifyEnrollPhoneVO vo) {
        ConsumeTicketResult consumeTicketResult = new ConsumeTicketResult();
        ActivityEntity activityEntity = activityDAO.getById(vo.getAssociationId());
        // 活动不存在
        if (activityEntity == null) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        // 活动未开始
        /*if (activityEntity.getStartTime().getTime() > new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_START_NOT_ALLOW_SIGN_IN);
        }*/

        // 活动已结束
        if (activityEntity.getEndTime().getTime() < new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_END);
        }

        // 活动停用
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
        }

        // 活动删除
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DELETED);
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = Lists.newArrayList();
        if (vo.getCode().length() == TicketConstants.PHONE_LAST_LENGTH) {
            campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLeftLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getCode(), false);
        } else {
            campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getCode(), false);
        }
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        List<String> campaignMergeIdList = campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(campaignMergeIdList));
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        // 需所有数据审核通过才能签到
        boolean reviewResult = activityEnrollDataEntityList.stream().allMatch(data -> data.getReviewStatus().equals(ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus()));
        if (!reviewResult) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_REVIEW_SUCCESS);
        }
        // 若所有状态均为已签到则返回
        boolean allSignIn = activityEnrollDataEntityList.stream().filter(data -> data.getSignIn() != null).allMatch(data -> ActivitySignOrEnrollEnum.SIGN_IN.getType().equals(data.getSignIn()));
        if (allSignIn) {
            return new Result<>(SHErrorCode.CONFERENCE_USER_HAS_SIGNED);
        }
        List<String> needSetId = activityEnrollDataEntityList.stream().map(ActivityEnrollDataEntity::getId).collect(Collectors.toList());
        campaignMergeDataManager.updateSignInStatus(needSetId, ActivitySignOrEnrollEnum.SIGN_IN.getType(), true);
        List<BaseCampaignEnrollData> baseCampaignEnrollDataList = campaignMergeDataDAO
            .queryCampaignEnrollDataIdentityInfo(activityEntity.getEa(), activityEntity.getMarketingEventId(), campaignMergeDataEntityList.get(0).getId());
        if (CollectionUtils.isNotEmpty(baseCampaignEnrollDataList)) {
            BaseCampaignEnrollData baseCampaignEnrollData = baseCampaignEnrollDataList.get(0);
            activityManager
                .handleActivitySignInOtherProcess(campaignMergeIdList, activityEntity, campaignMergeDataEntityList.get(0).getPhone(), baseCampaignEnrollData.getOpenId(),
                    baseCampaignEnrollData.getWxAppId(),
                    baseCampaignEnrollData.getFingerPrint(), baseCampaignEnrollData.getEnrollUserEa(), baseCampaignEnrollData.getEnrollUserFsUid());
        }
        ObjectData objectData = null;
        String userName = campaignMergeDataEntityList.get(0).getName();
        if (StringUtils.isNotBlank(campaignMergeDataEntityList.get(0).getCampaignMembersObjId())) {
            try {
                objectData = crmV2Manager.getDetail(activityEntity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignMergeDataEntityList.get(0).getCampaignMembersObjId());
            } catch (Exception e) {
                log.warn("CustomizeTicketManager.objectData error e:", e);
            }
        }
        if (StringUtils.isBlank(userName) && objectData != null) {
            userName = objectData.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName());
        }
        consumeTicketResult.setName(userName);
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIdAndObject(campaignMergeDataEntityList.get(0).getId(), activityEnrollDataEntityList.get(0).getObjectId(), activityEnrollDataEntityList.get(0).getObjectType());
        if (CollectionUtils.isNotEmpty(customizeFormDataUserEntityList)) {
            consumeTicketResult.setCompanyName(customizeFormDataUserEntityList.get(0).getSubmitContent().getCompanyName());
            consumeTicketResult.setPosition(customizeFormDataUserEntityList.get(0).getSubmitContent().getPosition());
        }
        if (activityEnrollDataEntityList.get(0).getGroupUserId() != null) {
            List<String> groupName = conferenceManager.getGroupNameByIdsStr(activityEnrollDataEntityList.get(0).getGroupUserId());
            consumeTicketResult.setGroupName(groupName);
        }

        return Result.newSuccess(consumeTicketResult);
    }

    @Override
    public Result<QueryInviteCountInfoResult> queryInviteCountInfo(QueryInviteCountInfoArg vo) {
        QueryInviteCountInfoResult countInfoResult = new QueryInviteCountInfoResult();
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.queryInviteCountInfo error activityEntity is null conferenceId:{}", vo.getConferenceId());
            return Result.newError(SHErrorCode.NO_DATA);
        }
        List<Integer> allUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vo.getFsUserIds())) {
            allUserIds.addAll(vo.getFsUserIds());
        }
        if (CollectionUtils.isNotEmpty(vo.getCircleIds())) {
            allUserIds.addAll(fsAddressBookManager.getEmployeeIdsByAllCircleIds(vo.getEa(), vo.getCircleIds()));
        }
        List<ConferenceInvitationUserDTO> conferenceInvitationUserEntityList = conferenceInvitationUserDAO
            .queryLatestConferenceInvitationUserByUsersWithOutPage(vo.getEa(), allUserIds, vo.getConferenceId(), null);
        if (CollectionUtils.isEmpty(conferenceInvitationUserEntityList)) {
            return Result.newSuccess(countInfoResult);
        }
        List<String> ids = conferenceInvitationUserEntityList.stream().map(ConferenceInvitationUserDTO::getCampaignMergeDataId).collect(Collectors.toList());
        List<PageCampaignParticipantsDTO> pageCampaignParticipantsDTOList = campaignMergeDataDAO.getCampaignMergeDataByObjIds(vo.getEa(), activityEntity.getMarketingEventId(), ids);
        if (CollectionUtils.isEmpty(pageCampaignParticipantsDTOList)) {
            return Result.newSuccess(countInfoResult);
        }
        countInfoResult.setTotalCnt(pageCampaignParticipantsDTOList.size());
//        int unInviteCnt = 0;
        int unEnrollCnt = 0;
        int enrollCnt = 0;
        int signInCnt = 0;
        // 查询活动成功对象  未邀约 数量 = 参与状态为未邀约的活动成员数量
        List<String> campaignMemberObjIdList = pageCampaignParticipantsDTOList.stream().map(PageCampaignParticipantsDTO::getCampaignMembersObjId).distinct().collect(Collectors.toList());
        List<ObjectData> campaignObjectDataList = crmMetadataManager.batchGetByIdsV3(vo.getEa(), SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), Lists.newArrayList(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName()), campaignMemberObjIdList);
        int unInviteCnt = (int) campaignObjectDataList.stream().map(e -> e.getString(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName()))
                .filter(e -> StringUtils.isNotBlank(e) && CampaignMembersObjMemberStatusEnum.TO_BE_INVITED.getValue().equals(e)).count();
        for (PageCampaignParticipantsDTO pageCampaignParticipantsDTO : pageCampaignParticipantsDTOList) {
//            if (pageCampaignParticipantsDTO.getInviteStatus() != null && pageCampaignParticipantsDTO.getInviteStatus().equals(CampaignMergeDataInviteStatusEnum.NOT_INVITED.getType())) {
//                unInviteCnt = unInviteCnt + 1;
//            }
            if (StringUtils.isBlank(pageCampaignParticipantsDTO.getFormDataUserId())) {
                unEnrollCnt = unEnrollCnt + 1;
            } else {
                enrollCnt = enrollCnt + 1;
            }
            if (pageCampaignParticipantsDTO.getSignIn() != null && pageCampaignParticipantsDTO.getSignIn().equals(ActivitySignOrEnrollEnum.SIGN_IN.getType())) {
                signInCnt = signInCnt + 1;
            }
        }
        countInfoResult.setUnInviteCnt(unInviteCnt);
        countInfoResult.setUnEnrollCnt(unEnrollCnt);
        countInfoResult.setEnrollCnt(enrollCnt);
        countInfoResult.setSignInCnt(signInCnt);
        return Result.newSuccess(countInfoResult);
    }

    @Override
    public Result<Void> updateInviteStatus(List<String> inviteIds, Integer status) {
        int ret = conferenceInviteParticipantDAO.updateInviteSendStatus(inviteIds, status);
        if (ret < 1){
            return Result.newError(SHErrorCode.CONFERENCE_UPDATE_INVITE_STATUS_FAILED);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteInvite(String inviteId) {
        int ret = conferenceInviteParticipantDAO.deleteInvite(inviteId);
        if (ret != 1){
            return Result.newError(SHErrorCode.CONFERENCE_DELETE_INVITE_FAILED);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> updateInviteInfo(UpdateInviteInfoVO vo) {
        if (StringUtils.isBlank(vo.getName())){
            vo.setName("");
        }
        if (StringUtils.isBlank(vo.getPhone())){
            vo.setPhone("");
        }
        if (StringUtils.isBlank(vo.getCompany())){
            vo.setCompany("");
        }
        if (StringUtils.isBlank(vo.getEmail())){
            vo.setEmail("");
        }
        int ret = conferenceInviteParticipantDAO.updateInviteInfo(vo.getId(), vo.getName(), vo.getCompany(), vo.getPhone(), vo.getEmail());
        if (ret != 1){
            return Result.newError(SHErrorCode.CONFERENCE_UPDATE_INVITE_INFO_FAILED);
        }
        return Result.newSuccess();
    }

    private boolean sendInviteParticipantMessage(String ea, List<Integer> toUserList, String imageUrl, ActivityEntity activity, String summary, Long startTime, Long endTime) {
        NoticeEntity noticeEntity = new NoticeEntity();
        String noticeId = UUIDUtil.getUUID();
        noticeEntity.setId(noticeId);
        noticeEntity.setTitle(activity.getTitle());
        noticeEntity.setContentType(NoticeContentTypeEnum.CONFERENCE_INVITE.getType());
        noticeEntity.setContent(activity.getId());
        noticeEntity.setSendType(NoticeSendTypeEnum.NORMAL.getType());
        noticeEntity.setCreateTime(new Date());
        noticeEntity.setUpdateTime(new Date());
        noticeEntity.setStatus(NoticeStatusEnum.SUCCESS.getStatus());
        noticeEntity.setFsEa(activity.getEa());
        noticeEntity.setFsUserId(activity.getCreateBy());
        FSEmployeeMsg employeeMsg = fsAddressBookManager.getEmployeeInfo(activity.getEa(), activity.getCreateBy());
        if (employeeMsg != null){
            noticeEntity.setUsername(employeeMsg.getFullName());
        }else {
            noticeEntity.setUsername("");
        }
        NoticeSendArg.NoticeVisibilityVO vo = new NoticeSendArg.NoticeVisibilityVO();
        vo.setUserIds(toUserList);
        noticeEntity.setSendScope(gs.toJson(vo));
        noticeDAO.addNotice(noticeEntity);

        StringBuilder sb = new StringBuilder()
                .append(host)
                .append("/ec/kemai/release/notice.html?_hash=notice&title=参会邀约")
                .append("&noticeId=")
                .append(noticeId)
                .append("&useNoticeCenterPage=true&contentType=")
                .append(NoticeContentTypeEnum.CONFERENCE_INVITE.getType());
        String url = sb.toString();
        String noticeTitle = "您有一个新的邀约任务";
        if (StringUtils.isBlank(summary)) {
            summary = "请邀约自己所负责的客户参与会议";
        }
        String buttonText = "立即去邀请";
        String limitDate = DateUtil.format(new Date(startTime)) + "-" + DateUtil.format(new Date(endTime));

        Map<String, Object> paramMap = new HashMap<>();
        List<Map<String, Object>> imageTextListMap = new ArrayList<>();
        paramMap.put("createTime", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
        paramMap.put("messageId", com.facishare.mankeep.common.util.UUIDUtil.getUUID());

        Map<String, Object> imageTextMap = new HashMap<>();
        imageTextMap.put("summary", noticeTitle);
        imageTextMap.put("internationalSummary", "qx.ot.mark.invitation_task_notice");
        imageTextMap.put("contentUrl", url);
        imageTextMap.put("imageUrl", imageUrl);
        imageTextMap.put("title", noticeTitle);
        imageTextMap.put("internationalTitle", "qx.ot.mark.invitation_task_notice");
        imageTextMap.put("buttonText", buttonText);
        imageTextMap.put("internationalButtonText", "qx.ot.mark.invite_now");
        imageTextMap.put("buttonUrl", url);
        imageTextMap.put("contentType", "1");
        imageTextMap.put("messageType", "1");
        imageTextMap.put("contentTitle", activity.getTitle());
        imageTextMap.put("description", noticeTitle);
        imageTextMap.put("internationalDescription", "qx.ot.mark.invitation_task_notice");
        imageTextMap.put("limitDate", limitDate);
        imageTextListMap.add(imageTextMap);

        List<Map<String, Object>> contentListMap = new ArrayList<>();
        Map<String, Object> conductMap = new HashMap<>();
        conductMap.put("邀约话术", summary);
        contentListMap.add(conductMap);
        Map<String, Object> timeMap = new HashMap<>();
        timeMap.put("邀约时间", limitDate);
        contentListMap.add(timeMap);
        imageTextMap.put("contentListMap", contentListMap);

        List<Map<String, Object>> i18contentListMap = new ArrayList<>();
        Map<String, Object> i18conductMap = new HashMap<>();
        i18conductMap.put("qx.ot.mark.invite.remark", summary);
        i18contentListMap.add(i18conductMap);
        Map<String, Object> i18timeMap = new HashMap<>();
        i18timeMap.put("qx.ot.mark.invite_time", limitDate);
        i18contentListMap.add(i18timeMap);
        imageTextMap.put("internationalContentListMap", i18contentListMap);

        imageTextMap.put("customParamJson", noticeManager.urlDispatcher(noticeEntity.getFsEa(), noticeEntity.getContentType(), noticeEntity.getContent(), noticeEntity.getId(), null));

        paramMap.put("imageTextList", imageTextListMap);
        paramMap.put("internationalDefaultSummary", "qx.ot.mark.invitation_summary");
        return fsMessageManager.sendOpenMessage(appId, ea, toUserList, noticeTitle, summary, paramMap);
    }

    private QueryMarketingEventDetailResult buildMarketingEventDetail(String ea, String marketingEventId){
        try {
            ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            if (objectData == null) {
                return null;
            }
            log.info("ConferenceServiceImpl queryMarketingEventDetail objectData={}", objectData);
            List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MARKETING_EVENT);

            QueryMarketingEventDetailResult result = new QueryMarketingEventDetailResult();
            result.setMarketingEventId(marketingEventId);
            result.setTitle(objectData.getName());
            Long beginTime = objectData.getLong(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName());
            result.setStartTime(beginTime);
            Long endTime = objectData.getLong(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName());
            result.setEndTime(endTime);
            result.setLocation(objectData.getString(CrmV2MarketingEventFieldEnum.LOCATION.getFieldName()));
            List<CrmFieldResult> crmFieldVOS = CrmUserDefineFieldVo.transferCrmUserDefineFieldVoListToCrmFieldVoList(fieldVos);
            Optional<CrmFieldResult> crmFieldResultOptional = crmFieldVOS.stream().filter(data -> data.getFieldName().equals(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName())).findFirst();
            result.setMarketingEventType(getMarketingEventTypeDesc(crmFieldResultOptional, objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName())));
            return result;
        } catch (Exception e) {
            log.warn("buildMarketingEventDetail exception:{}", e.fillInStackTrace());
        }
        return null;
    }

    private String getMarketingEventTypeDesc(Optional<CrmFieldResult> crmFieldResultOptional, String eventType) {
        if (crmFieldResultOptional.isPresent()) {
            // 从描述中获取枚举对应的枚举值
            CrmFieldResult crmFieldResult = crmFieldResultOptional.get();
            List<EnumDetailResult> enumDetailResults = crmFieldResult.getEnumDetails();
            for (EnumDetailResult enumDetailResult : enumDetailResults) {
                if (enumDetailResult.getItemCode().equals(eventType)) {
                    return enumDetailResult.getItemName();
                }
            }
        }
        return null;
    }

    @Override
    public Result<GetConferenceStatisticDataResult> getConferenceStatisticData(GetConferenceStatisticDataVO vo) {
        ActivityEntity activityEntity = activityDAO.getById(vo.getConferenceId());
        if (activityEntity == null) {
            log.warn("ConferenceServiceImpl.getConferenceStatisticData error activityEntity is null id:{}", vo.getConferenceId());
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        return conferenceManager.getConferenceStatistic(activityEntity.getEa(), activityEntity.getMarketingEventId());
    }

    /** 更新会议审核人
     * conferenceId
     * @param ea
     * @param conferenceId
     * @param userIds
     * @param department
     */
    private void updateConferenceReviewEmployee(String ea, String conferenceId, List<Integer> userIds, List<Integer> department){
        Set<Integer> allUserIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)){
            allUserIds.addAll(userIds);
        }
        if (CollectionUtils.isNotEmpty(department)) {
            if (department.contains(Constant.WHOLE_COMPANY_ID)) {
                List<Integer> employeeIdList = fsAddressBookManager.getEmployeeIdsByEa(ea);
                allUserIds.addAll(employeeIdList);
            }else {
                List<Integer> userIdsByDepartmentId = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, department);
                allUserIds.addAll(userIdsByDepartmentId);
            }
        }
        List<ConferenceReviewEmployeeEntity> entities = Lists.newArrayList();
        List<Integer> employees = Lists.newArrayList(allUserIds);
        for (int i = 0; i < employees.size(); i++){
            ConferenceReviewEmployeeEntity conferenceReviewEmployeeEntity = new ConferenceReviewEmployeeEntity();
            conferenceReviewEmployeeEntity.setId(UUIDUtil.getUUID());
            conferenceReviewEmployeeEntity.setEa(ea);
            conferenceReviewEmployeeEntity.setUserId(employees.get(i));
            conferenceReviewEmployeeEntity.setConferenceId(conferenceId);
            entities.add(conferenceReviewEmployeeEntity);
        }
        conferenceReviewEmployeeDAO.deleteByConferenceId(conferenceId);
        if (CollectionUtils.isNotEmpty(entities)){
            conferenceReviewEmployeeDAO.addConferenceReviewEmployees(entities);
        }
    }

    @Override
    public Result<String> getConferenceIdByMarketingEventId(String ea, String marketingEventId) {
        ActivityEntity activity = activityDAO.getActivityEntityByMarketingEventId(ea, marketingEventId);
        if (null == activity) {
            return Result.newError(SHErrorCode.CONFERENCE_NOT_FOUND);
        }
        return Result.newSuccess(activity.getId());
    }

    @Override
    public Result updateSignInSuccessSetting(String ea, UpdateSignInSuccessSettingArg arg) {
        Result signInSuccessSetting = this.getSignInSuccessSetting(new GetSignInSuccessSettingArg(arg.getConferenceId()));
        ConferenceSignInSuccessSetting entity = new ConferenceSignInSuccessSetting();
        if (signInSuccessSetting.isSuccess() && signInSuccessSetting.getData() != null && ((UpdateSignInSuccessSettingArg) signInSuccessSetting.getData()).getId() != null) {
            BeanUtils.copyProperties(arg, entity);
            entity.setId(((UpdateSignInSuccessSettingArg) signInSuccessSetting.getData()).getId());
            entity.setEa(((UpdateSignInSuccessSettingArg) signInSuccessSetting.getData()).getEa());
            entity.setCreateTime(((UpdateSignInSuccessSettingArg) signInSuccessSetting.getData()).getCreateTime());
            entity.setUpdateTime(new Date());
            conferenceSignInSuccessSettingDAO.update(entity);
        } else {
            BeanUtils.copyProperties(arg, entity);
            entity.setId(UUIDUtil.getUUID());
            entity.setUpdateTime(new Date());
            entity.setCreateTime(new Date());
            entity.setEa(ea);
            conferenceSignInSuccessSettingDAO.insert(entity);
        }
        //处理签到跳转推广内容设置
        this.updateSignInSetting(new UpdateSignInSettingVO(arg.getConferenceId(), arg.getJumpObjectType(), arg.getJumpObjectId(), arg.getJumpUrl()));
        return Result.newSuccess();
    }

    @Override
    public Result getSignInSuccessSetting(GetSignInSuccessSettingArg arg) {
        return conferenceManager.getSignInSuccessSetting(arg.getConferenceId());
    }

    @Override
    public Result getObjectCustomFields(String ea) {
        Map<String, String> customFields = new HashMap<>();
        // 获取活动成员自定义字段
        List<CrmUserDefineFieldVo> crmCustomerFields = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ);
        if (crmCustomerFields != null && !crmCustomerFields.isEmpty()) {
            crmCustomerFields.forEach(e -> {
                if (2 == e.getFieldProperty()) {
                    customFields.put(e.getFieldName(), e.getFieldCaption());
                }
            });
        }
        return Result.newSuccess(customFields);
    }

    @Override
    public Result getCampaignData(GetCampaignDataArg arg) {
        List<Map<String, Object>> customFieldsMapList = new ArrayList<>();
        try {
            Result signInSuccessSetting = this.getSignInSuccessSetting(new GetSignInSuccessSettingArg(arg.getConferenceId()));//配置信息 对象字段名:展示字段名
            if (null != signInSuccessSetting && null != signInSuccessSetting.getData()) {
                UpdateSignInSuccessSettingArg entity = (UpdateSignInSuccessSettingArg) signInSuccessSetting.getData();
                String customFieldSettings = entity.getCustomFieldSettings();
                if (StringUtils.isNotBlank(customFieldSettings)) {
                    List<Map<String, Object>> customFields = JSONObject.parseObject(customFieldSettings, ArrayList.class);
                    String campaignId = null;
                    if (StringUtils.isNotBlank(arg.getPhone())) {
                        campaignId = campaignDataManager.getCampaignMembersObjIdByPhone(entity.getEa(), arg.getMarketingEventId(), arg.getPhone());
                    } else {
                        campaignId = campaignDataManager.getCampaignMembersObjId(entity.getEa(), arg.getMarketingEventId(), arg.getUid(), arg.getOpenid(), arg.getAppId(), arg.getFingerPrint());
                    }
                    if (null != customFields) {
                        Map<String, String> objectData = null;
                        if (StringUtils.isNotBlank(campaignId)) {
                            objectData = crmV2Manager.getObjectDataEnTextVal(entity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignId);
                        }
                        for (Map<String, Object> item : customFields) {
                            Map<String, Object> customFieldsValItem = new HashMap<>();
                            ArrayList<String> keys = new ArrayList(item.keySet());
                            ArrayList<String> vals = new ArrayList(item.values());
                            if (null != objectData) {
                                customFieldsValItem.put(vals.get(0), objectData.get(keys.get(0)) == null ? I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996) : objectData.get(keys.get(0)));
                            } else {
                                customFieldsValItem.put(vals.get(0), I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCEMANAGER_996));
                            }
                            customFieldsMapList.add(customFieldsValItem);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("ConferenceServiceImpl getCampaignData error", e);
        }
        return Result.newSuccess(customFieldsMapList);
    }


    @Override
    public Result<QueryCampaignExternalContactStatisticsResult> queryCampaignExternalContactStatistics(String ea, Integer fsUserId, String marketingEventId) {
        QueryCampaignExternalContactStatisticsResult result = new QueryCampaignExternalContactStatisticsResult();
        result.setAddExternalNum(0);
        //根据市场活动id查询员工活码列表
        List<QywxAddFanQrCodeEntity> qywxAddFanQrCodeEntities = qywxAddFanQrCodeDAO.getByMarketingEventId(ea, marketingEventId);
        if (CollectionUtils.isEmpty(qywxAddFanQrCodeEntities)) {
            log.info("qywxAddFanQrCodeDAO.getByMarketingEventId is null");
            return Result.newSuccess(result);
        }
        List<String> qrCodeIds = qywxAddFanQrCodeEntities.stream().map(QywxAddFanQrCodeEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(qrCodeIds)) {
            log.info("qywxAddFanQrCodeDAO.getByMarketingEventId qrCodeIds is empty");
            return Result.newSuccess(result);
        }
        // 查询该市场活动下微信客户数量
        Filter marketingEventIdFilter = new Filter();
        marketingEventIdFilter.setFieldName(CrmWechatWorkExternalUserFieldEnum.ADD_QR_CODE_ID.getFieldName());
        marketingEventIdFilter.setOperator(OperatorContants.IN);
        marketingEventIdFilter.setFieldValues(qrCodeIds);
        Integer addQrCodeNum = crmV2Manager.getObjTotalCountByFilter(ea, -10000, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), Lists.newArrayList(marketingEventIdFilter));
        result.setAddExternalNum(addQrCodeNum);
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<TagName>> getConferenceTag(String conferenceId) {
        ConferenceTagEntity conferenceTagEntity = conferenceTagDAO.queryByConferenceId(conferenceId);
        if (conferenceTagEntity == null) {
            return Result.newSuccess();
        }
        List<TagName> tagNameList = GsonUtil.getGson().fromJson(conferenceTagEntity.getTags(), new TypeToken<List<TagName>>(){}.getType());
        return Result.newSuccess(tagNameList);
    }

    @Override
    public Result<GetSignInDetailResult> getSignInDetail(GetSignInDetailArg arg) {
        Result<GetSignInSettingResult> signInSettingResult = conferenceManager.getSignInSetting(arg.getConferenceId());
        if (!signInSettingResult.isSuccess()) {
            return Result.newError(signInSettingResult.getErrCode(), signInSettingResult.getErrMsg());
        }

        Result<UpdateSignInSuccessSettingArg> signInSuccessSettingResult = conferenceManager.getSignInSuccessSetting(arg.getConferenceId());
        if (!signInSuccessSettingResult.isSuccess()){
            return Result.newError(signInSuccessSettingResult.getErrCode(), signInSuccessSettingResult.getErrMsg());
        }

        CheckSignInStatusArg data = BeanUtil.copy(arg, CheckSignInStatusArg.class);
        data.setId(arg.getConferenceId());
        Result<CheckSignInStatusResult> checkSignInStatusResult = activityManager.checkSignInStatus(data);
        if (!checkSignInStatusResult.isSuccess()) {
            return Result.newError(checkSignInStatusResult.getErrCode(), checkSignInStatusResult.getErrMsg());
        }

        GetSignInDetailResult result = new GetSignInDetailResult();
        BeanUtils.copyProperties(signInSuccessSettingResult.getData(), result);
        result.setConferenceId(arg.getConferenceId());
        result.setJumpObjectId(signInSettingResult.getData().getJumpObjectId());
        result.setJumpObjectType(signInSettingResult.getData().getJumpObjectType());
        result.setJumpUrl(signInSettingResult.getData().getJumpUrl());
        result.setSignInStatus(checkSignInStatusResult.getData().getSignInStatus());
        result.setSignNewStatus(checkSignInStatusResult.getData().getSignNewStatus());
        result.setSignInTime(checkSignInStatusResult.getData().getSignInTime());
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetSimpleConferenceDetail> getSimpleDetail(String conferenceId) {
        return conferenceManager.getSimpleDetail(conferenceId);
    }

    @Override
    public Result<HexagonQrCodeResult> createSignInQrCode(CreateSignInQrCodeArg arg) {
        ActivityEntity activityEntity = conferenceDAO.getConferenceById(arg.getConferenceId());
        if (activityEntity == null) {
            return Result.newError(SHErrorCode.ACTIVITY_NOT_EXIST);
        }
        String ea = activityEntity.getEa();
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        //根据手机号或者邮箱,查询营销用户
        String marketingUserId = null;
        if (StringUtils.isNotBlank(arg.getPhone())) {
            UserMarketingAccountEntity marketingAccountEntity = userMarketingAccountDAO.getByTenantIdAndPhone(tenantId, arg.getPhone());
            if (marketingAccountEntity == null) {
                log.warn("createSignInQrCode.getByTenantIdAndPhone is null, phone:{}, tenantId:{}", arg.getPhone(), tenantId);
                return Result.newError(SHErrorCode.NOT_EXIST_MARKETING_USER);
            }
            marketingUserId = marketingAccountEntity.getId();
        } else if (StringUtils.isNotBlank(arg.getEmail())) {
            List<ObjectIdWithMarketingUserIdAndPhoneDTO> marketingUserIdAndPhoneDTOS = userMarketingAccountManager.queryMarketingUserDTOByEmail(arg.getEmail(), Lists.newArrayList(CrmObjectApiNameEnum.CRM_LEAD.getName(), CrmObjectApiNameEnum.CUSTOMER.getName(), CrmObjectApiNameEnum.CONTACT.getName()), ea);
            if (CollectionUtils.isEmpty(marketingUserIdAndPhoneDTOS) || marketingUserIdAndPhoneDTOS.size() > 1) {
                log.warn("createSignInQrCode.queryMarketingUserDTOByEmail is null or size > 1, email:{}, marketingUserIdAndPhoneDTOS:{}", arg.getEmail(), marketingUserIdAndPhoneDTOS);
                return Result.newSuccess();
            }
            marketingUserId = marketingUserIdAndPhoneDTOS.get(0).getUserMarketingId();
        } else {
            //手机号,邮箱都为空, 则是后面多次进入签到页,根据身份进行获取营销用户
            if (StringUtils.isNotBlank(arg.getWxAppId()) && StringUtils.isNotBlank(arg.getWxAppId())) {
                UserMarketingWxServiceAccountRelationEntity relationEntity = new UserMarketingWxServiceAccountRelationEntity();
                relationEntity.setEa(ea);
                relationEntity.setWxAppId(arg.getWxAppId());
                relationEntity.setWxOpenId(arg.getOpenId());
                UserMarketingWxServiceAccountRelationEntity wxServiceAccountRelationEntity = userMarketingWxServiceAccountRelationDao.getByEaAndWxAppIdAndWxOpenId(relationEntity);
                if (wxServiceAccountRelationEntity != null) {
                    marketingUserId = wxServiceAccountRelationEntity.getUserMarketingId();
                }
            } else if (StringUtils.isNotBlank(arg.getFingerPrint())) {
                UserMarketingBrowserUserRelationEntity browserUserRelation = userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(ea, arg.getFingerPrint());
                if (browserUserRelation != null) {
                    marketingUserId = browserUserRelation.getUserMarketingId();
                }
            } else if (StringUtils.isNotBlank(arg.getUid())) {
                UserMarketingMiniappAccountRelationEntity miniappAccountRelation = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, arg.getUid());
                if (miniappAccountRelation != null) {
                    marketingUserId = miniappAccountRelation.getUserMarketingId();
                }
            }
        }
        if (StringUtils.isBlank(marketingUserId)) {
            log.warn("createSignInQrCode.marketingUserId is blank");
            return Result.newError(SHErrorCode.NOT_EXIST_MARKETING_USER);
        }
        arg.setUserMarketingId(marketingUserId);
        CreateHexagonWxQrCodeArg createHexagonArg = BeanUtil.copy(arg, CreateHexagonWxQrCodeArg.class);
        createHexagonArg.setEa(ea);
        createHexagonArg.setSyncUtmCampaign(true);
        createHexagonArg.setMarketingEventId(activityEntity.getMarketingEventId());
        createHexagonArg.setWxQrCodes(arg.getWxQrCodes());
        createHexagonArg.setQywxQrCodes(arg.getQywxQrCodes());
        return hexagonService.createHexagonQrCode(createHexagonArg);
    }

    @Override
    public Result<List<QueryTicketListResult>> queryTicketCampaignList(VerifyEnrollPhoneVO vo) {
        List<QueryTicketListResult> queryTicketListResultList = Lists.newArrayList();
        ActivityEntity activityEntity = activityDAO.getById(vo.getAssociationId());
        // 活动不存在
        if (activityEntity == null) {
            return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
        }

        // 活动已结束
        if (activityEntity.getEndTime().getTime() < new Date().getTime()) {
            return new Result<>(SHErrorCode.ACTIVITY_END);
        }

        // 活动停用
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
        }

        // 活动删除
        if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
            return new Result<>(SHErrorCode.ACTIVITY_DELETED);
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = Lists.newArrayList();
        if (vo.getCode().length() == TicketConstants.PHONE_LAST_LENGTH) {
            campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLeftLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getCode(), false);
            CustomizeTicketReceiveEntity ticketReceiveEntity = customizeTicketDAO.getTicketByAssociationAndDataUserIdAndCodeLike(activityEntity.getEa(), CustomizeTicketTypeEnum.ACTIVITY_TICKET.getType(), activityEntity.getId(), vo.getCode());
            if (ticketReceiveEntity != null) {
                String formDataUserId = ticketReceiveEntity.getFormDataUserId();
                CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(formDataUserId);
                campaignMergeDataEntityList.add(campaignMergeDataEntity);
            }
        } else {
            campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getCode(), false);
        }
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_ENROLL);
        }
        Map<String,CampaignMergeDataEntity> campaignMergeDataEntityMap = campaignMergeDataEntityList.stream().collect(Collectors.toMap(CampaignMergeDataEntity::getId, Function.identity(), (k1, k2) -> k1));
        List<String> campaignMergeIdList = campaignMergeDataEntityList.stream().map(CampaignMergeDataEntity::getId).collect(Collectors.toList());
        List<ActivityEnrollDataEntity> activityEnrollDataEntityList = activityEnrollDataDAO.getActivityEnrollDataByFormDataUserIds(Lists.newArrayList(campaignMergeIdList));
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return Result.newError(SHErrorCode.MARKETING_LIVE_NOT_ENROLL);
        }
        //只查未签到的数据
        activityEnrollDataEntityList = activityEnrollDataEntityList.stream().filter(e -> Objects.equals(e.getSignIn(), ActivitySignOrEnrollEnum.NOT_SIGN_IN.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityEnrollDataEntityList)) {
            return Result.newError(SHErrorCode.CONFERENCE_USER_HAS_SIGNED);
        }
        List<CustomizeTicketReceiveEntity> customizeTicketReceiveEntityList = customizeTicketDAO.getTicketByAssociationAndDataUserIds(vo.getAssociationId(), campaignMergeIdList);
        Map<String,Long> ticketCodeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(customizeTicketReceiveEntityList)) {
            ticketCodeMap = customizeTicketReceiveEntityList.stream().collect(Collectors.toMap(CustomizeTicketReceiveEntity::getFormDataUserId, CustomizeTicketReceiveEntity::getCode, (v1, v2) -> v2));
        }
        Map<String, Long> finalTicketCodeMap = ticketCodeMap;
        activityEnrollDataEntityList.forEach(activityEnrollDataEntity -> {
            QueryTicketListResult queryTicketListResult = new QueryTicketListResult();
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntityMap.get(activityEnrollDataEntity.getFormDataUserId());
            queryTicketListResult.setTicketCode(finalTicketCodeMap.get(activityEnrollDataEntity.getFormDataUserId()));
            if (campaignMergeDataEntity != null) {
                queryTicketListResult.setName(campaignMergeDataEntity.getName());
                queryTicketListResult.setPhone(campaignMergeDataEntity.getPhone());
            }
            queryTicketListResultList.add(queryTicketListResult);
        });
        return Result.newSuccess(queryTicketListResultList);
    }

    @Override
    public Result<List<Map<String, Object>>> queryCustomizeTicketCampaignData(VerifyEnrollPhoneVO vo) {
        List<Map<String, Object>> customFieldsMapList = new ArrayList<>();
        try {
            ActivityEntity activityEntity = activityDAO.getById(vo.getAssociationId());
            // 活动不存在
            if (activityEntity == null) {
                return new Result<>(SHErrorCode.ACTIVITY_NOT_EXIST);
            }
            // 活动已结束
            if (activityEntity.getEndTime().getTime() < new Date().getTime()) {
                return new Result<>(SHErrorCode.ACTIVITY_END);
            }
            // 活动停用
            if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DISABLED.getStatus()) {
                return new Result<>(SHErrorCode.ACTIVITY_DISABLE);
            }
            // 活动删除
            if (activityEntity.getStatus() != null && activityEntity.getStatus() == ActivityStatusEnum.DELETED.getStatus()) {
                return new Result<>(SHErrorCode.ACTIVITY_DELETED);
            }
            Result signInSuccessSetting = this.getSignInSuccessSetting(new GetSignInSuccessSettingArg(vo.getAssociationId()));//配置信息 对象字段名:展示字段名
            if (null != signInSuccessSetting && null != signInSuccessSetting.getData()) {
                UpdateSignInSuccessSettingArg entity = (UpdateSignInSuccessSettingArg) signInSuccessSetting.getData();
                String customFieldSettings = entity.getCustomFieldSettings();
                if (StringUtils.isNotBlank(customFieldSettings)) {
                    List<Map<String, Object>> customFields = JSONObject.parseObject(customFieldSettings, ArrayList.class);
                    String campaignId = null;
                    if (vo.getCode().length() == TicketConstants.CUSTOMIZE_TICKET_LENGTH) {
                        CustomizeTicketReceiveEntity ticketReceiveEntity = customizeTicketDAO.getTicketByAssociationAndDataUserIdAndCode(entity.getEa(), CustomizeTicketTypeEnum.ACTIVITY_TICKET.getType(), vo.getAssociationId(), Long.valueOf(vo.getCode()));
                        String formDataUserId = ticketReceiveEntity.getFormDataUserId();
                        if (StringUtils.isNotEmpty(formDataUserId)) {
                            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(formDataUserId);
                            if (campaignMergeDataEntity != null) {
                                campaignId = campaignMergeDataEntity.getCampaignMembersObjId();
                            }
                        }
                    } else if (vo.getCode().length() == TicketConstants.PHONE_LAST_LENGTH) {
                        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhoneLeftLike(activityEntity.getEa(), activityEntity.getMarketingEventId(), vo.getCode(), false);
                        if (CollectionUtils.isNotEmpty(campaignMergeDataEntityList)) {
                            campaignId = campaignMergeDataEntityList.get(0).getCampaignMembersObjId();
                        }
                    } else {
                        campaignId = campaignDataManager.getCampaignMembersObjIdByPhone(entity.getEa(), activityEntity.getMarketingEventId(), vo.getCode());
                    }
                    if (null != customFields) {
                        Map<String, String> objectData = null;
                        if (StringUtils.isNotBlank(campaignId)) {
                            objectData = crmV2Manager.getObjectDataEnTextVal(entity.getEa(), -10000, CrmObjectApiNameEnum.CAMPAIGN_MEMBERS_OBJ.getName(), campaignId);
                        }
                        for (Map<String, Object> item : customFields) {
                            Map<String, Object> customFieldsValItem = new HashMap<>();
                            ArrayList<String> keys = new ArrayList(item.keySet());
                            ArrayList<String> vals = new ArrayList(item.values());
                            if (null != objectData) {
                                customFieldsValItem.put(vals.get(0), objectData.get(keys.get(0)) == null ? "暂无" : objectData.get(keys.get(0)));
                            } else {
                                customFieldsValItem.put(vals.get(0), "暂无");
                            }
                            customFieldsMapList.add(customFieldsValItem);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("ConferenceServiceImpl getCampaignData error", e);
        }
        return Result.newSuccess(customFieldsMapList);
    }
}