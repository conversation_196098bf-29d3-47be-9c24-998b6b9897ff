package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.StaffMiniappUserBindDAO;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.StaffMiniappUserBindEntity;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2021/05/27
 **/
@Slf4j
@Component
public class StaffMiniappUserBindManager {

    @Autowired
    private StaffMiniappUserBindDAO staffMiniappUserBindDAO;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;


    public void checkAndAddStaffUserBindAsyn(String visitorUid, String ea, String phone) {
        ThreadPoolUtils.execute(() -> {
            // 查询是否是托管
            String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
            if (WxAppInfoEnum.isSystemApp(wxAppId)) {
                return;
            }
            // 查询该员工是否已有绑定关系
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(visitorUid);
            if (fsBindEntity != null) {
                return;
            }
            // 查询手机号一致的该企业员工
            Integer fsUserId = fsAddressBookManager.getFsUserIdByPhone(ea, phone);
            if (fsUserId == null) {
                return;
            }
            // 查询员工对应的小程序
            fsBindEntity = fsBindManager.queryFSBindByFsEaAndFsUserId(ea, fsUserId, AccountTypeEnum.QYWX_MINI_APP.getType(), wxAppId);
            if (fsBindEntity == null) {
                return;
            }
            StaffMiniappUserBindEntity staffMiniappUserBindEntity = new StaffMiniappUserBindEntity();
            staffMiniappUserBindEntity.setId(UUIDUtil.getUUID());
            staffMiniappUserBindEntity.setVisitorUid(visitorUid);
            staffMiniappUserBindEntity.setStaffUid(fsBindEntity.getUid());
            staffMiniappUserBindDAO.insertStaffMiniappUserBind(staffMiniappUserBindEntity);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
    }

    public boolean checkAndAddStaffUserBindSync(String visitorUid, String ea, String phone) {
        try {
            // 查询是否是托管
            String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
            if (WxAppInfoEnum.isSystemApp(wxAppId)) {
                return false;
            }
            // 查询该员工是否已有绑定关系
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(visitorUid);
            if (fsBindEntity != null) {
                return false;
            }
            // 查询手机号一致的该企业员工
            Integer fsUserId = fsAddressBookManager.getFsUserIdByPhone(ea, phone);
            if (fsUserId == null) {
                return false;
            }
            // 查询员工对应的小程序
            fsBindEntity = fsBindManager.queryFSBindByFsEaAndFsUserId(ea, fsUserId, AccountTypeEnum.QYWX_MINI_APP.getType(), wxAppId);
            if (fsBindEntity == null) {
                return false;
            }
            StaffMiniappUserBindEntity staffMiniappUserBindEntity = new StaffMiniappUserBindEntity();
            staffMiniappUserBindEntity.setId(UUIDUtil.getUUID());
            staffMiniappUserBindEntity.setVisitorUid(visitorUid);
            staffMiniappUserBindEntity.setStaffUid(fsBindEntity.getUid());
            staffMiniappUserBindDAO.insertStaffMiniappUserBind(staffMiniappUserBindEntity);
            return true;
        } catch (Exception e) {
            log.warn("StaffMiniappUserBindManager.checkAndAddStaffUserBindSyn error e:{}", e);
            return false;
        }
    }

}
