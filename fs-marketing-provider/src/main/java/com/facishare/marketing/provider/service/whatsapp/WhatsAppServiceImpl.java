/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.whatsapp;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.api.arg.whatsapp.*;
import com.facishare.marketing.api.service.whatsapp.WhatsAppService;
import com.facishare.marketing.api.vo.whatsapp.*;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.MarketingActivitySpreadTypeEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.whatsapp.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.result.WhatsAppResultCodeEnum;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.bo.whatsapp.SendStatusStatisticBO;
import com.facishare.marketing.provider.entity.whatsapp.WhatsAppSendTaskEntity;
import com.facishare.marketing.provider.manager.crmobjectcreator.WhatsAppSendRecordObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.whatsapp.WhatsAppManager;
import com.facishare.marketing.provider.manager.whatsapp.WhatsAppSendTaskManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.whatsapp.result.AuthorizationResult;
import com.facishare.marketing.provider.remote.whatsapp.result.BusinessPhoneObject;
import com.facishare.marketing.provider.remote.whatsapp.result.SendMessageResult;
import com.facishare.marketing.provider.remote.whatsapp.result.TemplateResult;
import com.facishare.marketing.provider.service.whatsapp.syncdata.IWhatsAppSyncDataProcessor;
import com.facishare.marketing.provider.service.whatsapp.syncdata.WhatsAppSyncDataFactory;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("whatsAppService")
public class WhatsAppServiceImpl implements WhatsAppService {

    @Autowired
    private WhatsAppManager whatsAppManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @Autowired
    private WhatsAppSendRecordObjManager whatsAppSendRecordObjManager;

    @Autowired
    private MarketingCrmManager marketingCrmManager;

    @Autowired
    private WhatsAppSendTaskManager whatsAppSendTaskManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private WhatsAppSyncDataFactory syncDataFactory;

    // 请联系客户经理下单【WhatsApp营销插件】或者升级营销通专业版
    public final static int FORBID_TO_OPEN_WHATSAPP_PLUGIN = 1;
    // 该企业没有授权信息
    public final static int ENTERPRISE_NOT_HAVE_AUTHORIZATION = 2;
    // 允许开通插件
    public final static int ALLOW_OPEN_PLUGIN = 3;

    @Override
    public Result<AuthorizationVO> getAuthorization(String ea) {
        AuthorizationResult result = whatsAppManager.getAuthorization(ea);
        if (result == null) {
            return Result.newSuccess();
        }
        AuthorizationVO vo = BeanUtil.copy(result, AuthorizationVO.class);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<Void> saveAuthorization(SaveAuthorizationArg saveAuthorizationArg) {
        String ea = saveAuthorizationArg.getFsEa();
        Result<AuthorizationVO> getAuthorizationResult = getAuthorization(ea);
        if (getAuthorizationResult.isSuccess() && getAuthorizationResult.getData() != null) {
            return Result.newError(SHErrorCode.ENTERPRISE_IS_BIND_WHATSAPP);
        }
        boolean isSuccess = whatsAppManager.saveAuthorization(saveAuthorizationArg);
        if (!isSuccess) {
            return Result.newError(SHErrorCode.SAVE_AUTHORIZATION_ERROR);
        }
        marketingActivityRemoteManager.addWhatsAppSpreadTypeOptions(ea);
        whatsAppSendRecordObjManager.getOrCreateWhatsAppSendRecordObjDescribe(ea);
        return Result.newSuccess();
    }

    @Override
    public Result<TemplateVO> queryTemplate(QueryTemplateArg queryTemplateArg) {
        if (StringUtils.isBlank(queryTemplateArg.getBusinessPhone())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        com.facishare.marketing.provider.remote.whatsapp.arg.QueryTemplateArg arg = BeanUtil.copy(queryTemplateArg, com.facishare.marketing.provider.remote.whatsapp.arg.QueryTemplateArg.class);
        TemplateResult templateResult = whatsAppManager.queryTemplate(arg);
        if (templateResult == null) {
            return Result.newSuccess();
        }
        TemplateVO templateVO = new TemplateVO();
        if (templateResult.getPaging() != null && templateResult.getPaging().getCursors() != null) {
            TemplateVO.Cursors cursors = new TemplateVO.Cursors();
            cursors.setBefore(templateResult.getPaging().getCursors().getBefore());
            cursors.setAfter(templateResult.getPaging().getCursors().getAfter());
            templateVO.setCursors(cursors);
        }

        List<TemplateVO.Item> itemList = Lists.newArrayList();
        for (TemplateResult.TemplateInfo templateInfo : templateResult.getData()) {
            TemplateVO.Item itemVO = whatsAppManager.buildTemplateVoItem(templateInfo);
            itemList.add(itemVO);
        }
        templateVO.setData(itemList);
        return Result.newSuccess(templateVO);
    }

    @Override
    public Result<List<BusinessPhoneVO>> getBusinessPhone(String ea) {
        Map<String, List<BusinessPhoneObject>> phoneMap = whatsAppManager.queryAllBoundPhone(ea);
        if (MapUtils.isEmpty(phoneMap)) {
            return Result.newSuccess();
        }
        List<BusinessPhoneVO> phoneVOList = Lists.newArrayList();
        for (Map.Entry<String, List<BusinessPhoneObject>> entry : phoneMap.entrySet()) {
            List<BusinessPhoneObject> phoneObjectList = entry.getValue();
            for (BusinessPhoneObject businessPhoneObject : phoneObjectList) {
                String status = businessPhoneObject.getStatus();
                // 已连接或者已标记的手机号才能发送消息
                if (PhoneStatusEnum.CONNECTED.getValue().equals(status) || PhoneStatusEnum.FLAGGED.getValue().equals(status)) {
                    BusinessPhoneVO businessPhoneVO = new BusinessPhoneVO();
                    businessPhoneVO.setPhone(entry.getKey());
                    businessPhoneVO.setName(businessPhoneObject.getVerifiedName());
                    phoneVOList.add(businessPhoneVO);
                    break;
                }
            }
        }
        return Result.newSuccess(phoneVOList);
    }

    /**
     * 直接发送模板消息到whatsapp,不用记录任何业务数据
     */
    @Override
    public Result<Void> directSendTemplateMessage(DirectSendMessageArg directSendMessageArg) {
        if (StringUtils.isBlank(directSendMessageArg.getBusinessPhone()) || StringUtils.isBlank(directSendMessageArg.getTemplateName())
                || StringUtils.isBlank(directSendMessageArg.getTemplateLanguage()) || CollectionUtils.isEmpty(directSendMessageArg.getPhoneList())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = directSendMessageArg.getEa();
        // 根据模板名称和语言获取模板
        TemplateResult.TemplateInfo templateInfo = whatsAppManager.getTemplateByNameAndLanguage(ea, directSendMessageArg.getBusinessPhone(), directSendMessageArg.getTemplateName(), directSendMessageArg.getTemplateLanguage());
        if (templateInfo == null) {
            return Result.newError(SHErrorCode.TEMPLATE_NOT_EXIST);
        }
        // 获取模板的所有参数 校验参数个数是否匹配
        int parameterCount = whatsAppManager.getParameterCount(templateInfo);
        List<String> parameterList = directSendMessageArg.getParameterList();
        if (parameterCount > 0 && (CollectionUtils.isEmpty(parameterList) || parameterList.size() < parameterCount)) {
            return Result.newError(SHErrorCode.PARAMETER_COUNT_NOT_MATCH);
        }
        // 号码去重
        List<String> phoneList = directSendMessageArg.getPhoneList().stream().distinct().collect(Collectors.toList());
        // key --> 号码 value --> 错误信息
        Map<String, String> phoneToErrorMessageMap = Maps.newHashMap();
        for (String phone : phoneList) {
            com.fxiaoke.crmrestapi.common.result.Result<SendMessageResult> result = whatsAppManager.sendTemplateMessageByPhone(ea, directSendMessageArg.getBusinessPhone(), phone, templateInfo, directSendMessageArg.getParameterList());
            if (!result.isSuccess()) {
                WhatsAppResultCodeEnum whatsAppResultCodeEnum = WhatsAppResultCodeEnum.getByCode(result.getCode());
                if (whatsAppResultCodeEnum != null) {
                    phoneToErrorMessageMap.put(phone, whatsAppResultCodeEnum.getMessage());
                } else {
                    phoneToErrorMessageMap.put(phone, result.getMessage());
                }
            }
        }
        if (MapUtils.isEmpty(phoneToErrorMessageMap)) {
            return Result.newSuccess();
        }
        StringBuilder stringBuilder = new StringBuilder();
        phoneToErrorMessageMap.forEach((phone, message) -> stringBuilder.append(phone).append(" -> ").append(message).append(";"));
        String errorMessage = stringBuilder.toString();
        log.info("directSendTemplateMessage arg: {} error: {}", directSendMessageArg, errorMessage);
        if (errorMessage.length() > 100) {
            errorMessage = errorMessage.substring(0, 100);
        }
        return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_WHATSAPP_WHATSAPPSERVICEIMPL_207) + errorMessage);
    }

    @Override
    public Result<PageResult<SpreadListVO>> spreadList(SpreadListArg spreadListArg) {

        PageResult<SpreadListVO> pageResult = new PageResult<>();
        pageResult.setPageNum(spreadListArg.getPageNum());
        pageResult.setPageSize(spreadListArg.getPageSize());
        pageResult.setTotalCount(0);
        List<SpreadListVO> spreadListVOList = Lists.newArrayList();
        pageResult.setResult(spreadListVOList);

        String ea = spreadListArg.getEa();
        int fsUserId = spreadListArg.getFsUserId();
        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (StringUtils.isNotBlank(spreadListArg.getName())) {
            marketingActivityArg.setName(spreadListArg.getName());
        }
        marketingActivityArg.setSpreadType(MarketingActivitySpreadTypeEnum.WHATS_APP_SPREAD.getSpreadType());
        marketingActivityArg.setPageSize(spreadListArg.getPageSize());
        marketingActivityArg.setPageNumber((spreadListArg.getPageNum() - 1) * spreadListArg.getPageSize());
        // 直接查询营销活动
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageMarketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (pageMarketingActivityList == null || CollectionUtils.isEmpty(pageMarketingActivityList.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        List<String> marketingActivityIdList = pageMarketingActivityList.getDataList().stream().map(ObjectData::getId).distinct().collect(Collectors.toList());
        //根据营销活动查询发送task
        List<WhatsAppSendTaskEntity> taskEntityList = whatsAppSendTaskManager.getByMarketingActivityIdList(ea, marketingActivityIdList);
        if (CollectionUtils.isEmpty(taskEntityList)) {
            log.warn("whatsapp spreadList taskEntityList is empty ea: {} marketingActivityIdList: {}", ea, marketingActivityIdList);
            return Result.newSuccess(pageResult);
        }
        // 获取营销活动的负责人名字
        List<Integer> ownerList = pageMarketingActivityList.getDataList().stream().map(ObjectData::getOwner).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, String> ownerIdToOwnerNameMap = getOwnerName(ea, ownerList);

        pageResult.setTotalCount(pageMarketingActivityList.getTotal());
        Map<String, WhatsAppSendTaskEntity> marketingActivityIdToTaskMap = taskEntityList.stream().collect(Collectors.toMap(WhatsAppSendTaskEntity::getMarketingActivityId, e -> e, (v1, v2) -> v1));
        // 查询每个营销活动的发送状态统计
        List<SendStatusStatisticBO> sendStatusStatisticList = whatsAppManager.statisticSendStatusCount(ea, marketingActivityIdList);
        Map<String, SendStatusStatisticBO> marketingActivityIdToStatisticMap = sendStatusStatisticList.stream().collect(Collectors.toMap(SendStatusStatisticBO::getMarketingActivityId, e -> e, (v1, v2) -> v1));
        // 组装返回结果
        for (ObjectData objectData : pageMarketingActivityList.getDataList()) {
            SpreadListVO vo = new SpreadListVO();
            vo.setName(objectData.getName());
            WhatsAppSendTaskEntity taskEntity = marketingActivityIdToTaskMap.get(objectData.getId());
            if (taskEntity == null) {
                log.warn("whatsapp spreadList taskEntity is null ea: {} marketingActivityId: {}", ea, objectData.getId());
                continue;
            }
            vo.setSendTime(taskEntity.getExecuteTime());
            vo.setSendStatus(taskEntity.getSendStatus());
            vo.setAuditStatus(objectData.getLifeStatus());
            vo.setMarketingActivityId(objectData.getId());
            vo.setTaskId(taskEntity.getId());
            SendStatusStatisticBO statisticBO = marketingActivityIdToStatisticMap.getOrDefault(objectData.getId(), new SendStatusStatisticBO());
            vo.setDeliveredCount(statisticBO.getDeliveredCount());
            vo.setReadCount(statisticBO.getReadCount());
            // 列表的发送人数等于所有状态的总和
            vo.setSendCount(statisticBO.getTotalCount());
            vo.setOwnerName(ownerIdToOwnerNameMap.getOrDefault(objectData.getOwner(), I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193)));
            spreadListVOList.add(vo);
        }
        return Result.newSuccess(pageResult);
    }

    private Map<Integer, String> getOwnerName(String ea, List<Integer> ownerList) {
        if (CollectionUtils.isEmpty(ownerList)) {
            return Maps.newHashMap();
        }
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("user_id", OperatorConstants.IN, ownerList.stream().map(String::valueOf).collect(Collectors.toList()));
        List<String> selectFieldList = Lists.newArrayList("_id", "user_id", "name");
        List<ObjectData> personObjDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.PERSONNEL_OBJ.getName(), selectFieldList, paasQueryArg);
        return personObjDataList.stream().collect(Collectors.toMap(e -> Integer.parseInt(e.getString("user_id")), e -> e.getString("name"), (v1, v2) -> v1));
    }

    @Override
    public Result<Void> sendMessageByJob() {
        int limit = 1000;
        List<WhatsAppSendTaskEntity> whatsAppSendTaskEntityList = whatsAppSendTaskManager.getUnSendTask(limit);
        if (CollectionUtils.isEmpty(whatsAppSendTaskEntityList)) {
            return Result.newSuccess();
        }
        whatsAppSendTaskEntityList.forEach(e -> whatsAppManager.sendMessageByTaskId(e.getEa(), e.getId()));
        return null;
    }

    @Override
    public Result<Void> deleteSendTask(DeleteSendTaskArg deleteSendTaskArg) {
        if (StringUtils.isBlank(deleteSendTaskArg.getTaskId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = deleteSendTaskArg.getEa();
        WhatsAppSendTaskEntity whatsAppSendTaskEntity = whatsAppSendTaskManager.getById(ea, deleteSendTaskArg.getTaskId());
        if (whatsAppSendTaskEntity == null) {
            return Result.newError(SHErrorCode.SEND_TASK_NOT_EXIST);
        }
        com.facishare.marketing.common.result.Result<Boolean> result = whatsAppManager.doDeleteAction(ea, deleteSendTaskArg.getFsUserId(), whatsAppSendTaskEntity.getMarketingActivityId());
        if (result.isSuccess()) {
            return Result.newSuccess();
        }
        return Result.newError(result.getErrCode(), result.getErrMsg());
    }

    @Override
    public Result<Void> cancelSendTask(CancelSendTaskArg cancelSendTaskArg) {
        if (StringUtils.isBlank(cancelSendTaskArg.getTaskId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = cancelSendTaskArg.getEa();
        com.facishare.marketing.common.result.Result<Boolean> result = whatsAppManager.cancelSendTask(ea, cancelSendTaskArg.getTaskId());
        if (result.isSuccess()) {
            return Result.newSuccess();
        }
        return Result.newError(result.getErrCode(), result.getErrMsg());
    }

    @Override
    public Result<PageResult<SendDetailVO>> sendDetail(SendDetailArg sendDetailArg) {

        Integer pageNum = sendDetailArg.getPageNum();
        Integer pageSize = sendDetailArg.getPageSize();

        if (pageNum == null || pageSize == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isNotBlank(sendDetailArg.getSendStatus()) && WhatsappMsgStatusEnum.getByCode(sendDetailArg.getSendStatus()) == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        PageResult<SendDetailVO> pageResult = new PageResult<>();
        pageResult.setPageNum(sendDetailArg.getPageNum());
        pageResult.setPageSize(sendDetailArg.getPageSize());
        pageResult.setTotalCount(0);
        List<SendDetailVO> sendDetailVOList = Lists.newArrayList();
        pageResult.setResult(sendDetailVOList);

        String ea = sendDetailArg.getEa();
        // 直接查询whatsapp发送明细对象
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        paasQueryFilterArg.setSelectFields(Lists.newArrayList("marketing_user_name", "marketing_user_id", "to_phone", "send_status", "send_time", "fail_reason", "delivered_time", "read_time"));
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WHATSAPP_SEND_RECORD_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, 1);
        paasQueryFilterArg.setQuery(query);
        query.addFilter("marketing_activity_id", OperatorConstants.EQ, Lists.newArrayList(sendDetailArg.getMarketingActivityId()));
        if (StringUtils.isNotBlank(sendDetailArg.getPhone())) {
            query.addFilter("to_phone", OperatorConstants.LIKE, Lists.newArrayList(sendDetailArg.getPhone()));
        }
        if (StringUtils.isNotBlank(sendDetailArg.getSendStatus())) {
            // 已送达 包含已读
            if (sendDetailArg.getSendStatus().equals(WhatsappMsgStatusEnum.DELIVERED.getCode())) {
                query.addFilter("send_status", OperatorConstants.IN, Lists.newArrayList(sendDetailArg.getSendStatus(), WhatsappMsgStatusEnum.READ.getCode()));
            } else {
                query.addFilter("send_status", OperatorConstants.IN, Lists.newArrayList(sendDetailArg.getSendStatus()));
            }
        }
        InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, sendDetailArg.getPageNum(), sendDetailArg.getPageSize());

        if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(innerPage.getTotalCount());
        for (ObjectData objectData : innerPage.getDataList()) {
            SendDetailVO sendDetailVO = new SendDetailVO();
            sendDetailVO.setUserMarketingName(objectData.getString("marketing_user_name"));
            sendDetailVO.setUserMarketingId(objectData.getString("marketing_user_id"));
            sendDetailVO.setSendStatus(objectData.getString("send_status"));
            Long sendTime = objectData.getLong("send_time");
            if (sendTime != null) {
                sendDetailVO.setSendTime(new Date(sendTime));
            }
            sendDetailVO.setPhone(objectData.getString("to_phone"));
            sendDetailVO.setErrorMsg(objectData.getString("fail_reason"));
            Long deliveredTime = objectData.getLong("delivered_time");
            if (deliveredTime != null) {
                sendDetailVO.setDeliveredTime(new Date(deliveredTime));
            }
            Long readTime = objectData.getLong("read_time");
            if (readTime != null) {
                sendDetailVO.setReadTime(new Date(readTime));
            }
            sendDetailVOList.add(sendDetailVO);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Integer> checkWhatsAppPluginOpenCondition(String ea) {
        String currentVersion = appVersionManager.getCurrentAppVersion(ea);
        Set<String> standardVersionSet = Sets.newHashSet(VersionEnum.STAN.getVersion(), VersionEnum.STAN_DINGTALK_30_APP.getVersion(), VersionEnum.STAN_DINGTALK_100_APP.getVersion(), VersionEnum.STAN_DINGTALK_500_APP.getVersion());
        // 标准版并且没有海外营销插件的客户，不允许开启该插件
        if (standardVersionSet.contains(currentVersion) && !appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.OVERSEAS_PLUGIN)) {
            return Result.newSuccess(FORBID_TO_OPEN_WHATSAPP_PLUGIN);
        }
        AuthorizationResult authorizationResult = whatsAppManager.getAuthorization(ea);
        if (authorizationResult == null) {
            return Result.newSuccess(ENTERPRISE_NOT_HAVE_AUTHORIZATION);
        }
        return Result.newSuccess(ALLOW_OPEN_PLUGIN);
    }

    @Override
    public Result<PLoginDetailVO> pLogin(PLoginArg pLoginArg) {
        PLoginDetailVO vo = new PLoginDetailVO();
        vo.setLoginStatus(0);
        vo.setLicenseStatus(false);
        String currentVersion = appVersionManager.getCurrentAppVersion(pLoginArg.getEa());
        Set<String> whatsappVersionSet = Sets.newHashSet(VersionEnum.STREN.getVersion());
        // 旗舰版或未购买海外营销插件不允许使用whatsapp个人版
        if (!whatsappVersionSet.contains(currentVersion) && !appVersionManager.checkSpecialVersionOrders(pLoginArg.getEa(), VersionEnum.OVERSEAS_PLUGIN)) {
            vo.setLoginStatus(1);
            vo.setErrorMsg(I18nUtil.get("mark.whatsapp.result.nolicense", "很抱歉,您的账号没有使用WhatsApp的权限,请联系管理员进行处理"));
            return Result.newSuccess(vo);
        }
        vo.setLicenseStatus(true);
        String tenantId = String.valueOf(eieaConverter.enterpriseAccountToId(pLoginArg.getEa()));
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        paasQueryFilterArg.setSelectFields(Lists.newArrayList("_id", "user_id", "fx_user_id", "name", "mobile", "life_status", "crm_bind_time"));
        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName());
        PaasQueryArg query = new PaasQueryArg(0, 100);
        paasQueryFilterArg.setQuery(query);
        query.addFilter("user_id", OperatorConstants.EQ, Lists.newArrayList(pLoginArg.getWhatsAppUserId()));
        query.addFilter("record_type", OperatorConstants.EQ, Lists.newArrayList("whatsapp__c"));
        query.addFilter("tenant_id", OperatorConstants.EQ, Lists.newArrayList(tenantId));
        InnerPage<ObjectData> innerPage = crmV2Manager.listCrmObjectByFilterV3(pLoginArg.getEa(), SuperUserConstants.USER_ID, paasQueryFilterArg, 1, 100);

        if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
            String id = UUIDUtil.getUUID();
            Map<String,Object> objectData = Maps.newHashMap();
            objectData.put("user_id", pLoginArg.getWhatsAppUserId());
            objectData.put("fx_user_id", pLoginArg.getFsUserId());
            objectData.put("cipher_user_id", pLoginArg.getWhatsAppUserId());
            objectData.put("tenant_id", pLoginArg.getEa());
            objectData.put("record_type", "whatsapp__c");
            objectData.put("name", pLoginArg.getName());
            objectData.put("mobile", pLoginArg.getMobile());
            objectData.put("_id", id);
            objectData.put("object_describe_api_name", CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName());
            objectData.put("owner", Lists.newArrayList(pLoginArg.getFsUserId()));
            objectData.put("status", "1");
            objectData.put("crm_bind_time", System.currentTimeMillis());
            objectData.put("crm_bind_type", Lists.newArrayList("2"));
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = crmV2Manager.addObjectData(pLoginArg.getEa(), CrmObjectApiNameEnum.WECHAT_EMPLOYEE_OBJ.getName(), -10000, objectData);
            if (!result.isSuccess()) {
                log.warn("WhatsAppService Create WechatEmployeeObj failed, pLoginArg: {}", pLoginArg);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            vo.setImUserId(id);
            vo.setBindFsUserId(pLoginArg.getFsUserId());
            vo.setErrorMsg(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_4));
            vo.setLoginStatus(0);
            return Result.newSuccess(vo);
        }
        String bindFsUserId = innerPage.getDataList().get(0).getString("fx_user_id");
        vo.setBindFsUserId(bindFsUserId);
        vo.setImUserId(innerPage.getDataList().get(0).getString("_id"));
        if(pLoginArg.getFsUserId().equals(bindFsUserId)) {
            vo.setErrorMsg(I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_4));
        } else {
            vo.setLoginStatus(2);
            vo.setErrorMsg(I18nUtil.get("mark.whatsapp.result.account.nomatch","抱歉,当前登录的WhatsApp账号与CRM员工账号不匹配,请切换账号登录或联系管理员进行处理"));
        }
        return Result.newSuccess(vo);
    }

    @Override
    public Result<SyncDataResultVO> syncData(SyncDataArg syncDataArg) {
        if(!syncDataArg.isValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        IWhatsAppSyncDataProcessor syncDataProcessor = syncDataFactory.getSyncDataProcessor(syncDataArg.getObjectApiName());
        if(syncDataProcessor == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return syncDataProcessor.syncData(syncDataArg);
    }

    @Override
    public Result<List<ObjectData>> queryDataByIds(QueryDataByIdsArg queryDataByIdsArg) {
        if(!queryDataByIdsArg.isValid()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return Result.newSuccess();
    }
}