package com.facishare.marketing.provider.service.marketingplugin;

import com.facishare.eservice.common.utils.SpringContextUtil;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/6/23
 **/
@Service
@Slf4j
public class MarketingPluginProxySerivce {

    public MarketingPluginBaseService getBean(Integer type){
        if (Objects.equals(type, MarketingPluginTypeEnum.XIAOETONG_LIVE.getType())) {
            return SpringContextUtil.getBean("xiaoetongPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.VHALL_LIVE.getType())) {
            return SpringContextUtil.getBean("vhallPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.CHANNELS_LIVE.getType())) {
            return SpringContextUtil.getBean("channelsPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.MARKETING_ORDER_INTEGRATION.getType())) {
            return SpringContextUtil.getBean("marketingOrderPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.DATA_PERMISSION.getType())) {
            return SpringContextUtil.getBean("dataPermissionPluginService");
        }else if (Objects.equals(type, MarketingPluginTypeEnum.SERVICE_KNOWLEDGE.getType())) {
            return SpringContextUtil.getBean("serviceKnowledgePluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.WECHAT_COUPON.getType())) {
            return SpringContextUtil.getBean("weChatCouponPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.MARKETING_USER_PLUGIN.getType())) {
            return SpringContextUtil.getBean("marketingUserPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.MUDU_LIVE.getType())) {
            return SpringContextUtil.getBean("muduPluginService");
        }  else if (Objects.equals(type, MarketingPluginTypeEnum.MARKETING_SDR.getType())) {
            return SpringContextUtil.getBean("marketingSDRPluginService");
        } else if (Objects.equals(type, MarketingPluginTypeEnum.MEMBER_MARKETING.getType())) {
            return SpringContextUtil.getBean("memberMarketingPluginService");
        }
        return null;
    }
}
