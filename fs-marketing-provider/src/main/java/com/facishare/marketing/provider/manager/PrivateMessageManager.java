package com.facishare.marketing.provider.manager;

import com.facishare.mankeep.api.vo.ReplyMessageVO;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.privateMessage.FsMessageSettingDAO;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.UserEntity;
import com.facishare.marketing.provider.entity.privateMessage.FsMessageSettingEntity;
import com.facishare.marketing.provider.manager.qywx.QywxMiniAppMessageManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2020/10/10
 **/
@Component
@Slf4j
public class PrivateMessageManager {

    @Autowired
    private FsMessageSettingDAO fsMessageSettingDAO;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private QywxMiniAppMessageManager qywxMiniAppMessageManager;

    @Autowired
    private com.facishare.mankeep.api.service.MessageService mankeepMessageService;

    /**
     * 增加私信同步企信设置
     */
    public void addFsMessageSettingData(String uid) {
        if (StringUtils.isBlank(uid)) {
            return;
        }
        FsMessageSettingEntity fsMessageSettingEntity = fsMessageSettingDAO.getFsMessageSetting(uid);
        if (fsMessageSettingEntity == null) {
            fsMessageSettingEntity = new FsMessageSettingEntity();
            fsMessageSettingEntity.setId(UUIDUtil.getUUID());
            fsMessageSettingEntity.setUid(uid);
            fsMessageSettingEntity.setTurnOnSynchronize(true);
            fsMessageSettingDAO.insertFsMessageSettingData(fsMessageSettingEntity);
        }
    }

    /**
     * 发送订阅消息或企业微信小程序消息
     */
    public void sendMiniAppMessage(String targetUid, String sendUid, String appId) {
        List<UserEntity> userEntityList = userManager.listByUids(Lists.newArrayList(targetUid, sendUid));
        if (CollectionUtils.isEmpty(userEntityList)) {
            return;
        }
        Map<String, UserEntity> userEntityMap = userEntityList.stream().collect(Collectors.toMap(UserEntity::getUid, data -> data, (v1, v2) -> v1));
        if (userEntityMap.get(targetUid) == null || userEntityMap.get(sendUid) == null) {
            return;
        }
        UserEntity targetUser = userEntityMap.get(targetUid);
        UserEntity sendUser = userEntityMap.get(sendUid);
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(targetUid);
        if (fsBindEntity == null) {
            return;
        }
        if (StringUtils.isBlank(sendUser.getName())) {
            return;
        }
        if (StringUtils.isNotBlank(targetUser.getOpenid())) {
            // 发送订阅消息
            ReplyMessageVO vo = new ReplyMessageVO();
            vo.setSendUid(sendUid);
            vo.setAppid(appId);
            vo.setUsername(sendUser.getName());
            vo.setTime(DateUtil.format3(new Date()));
            vo.setToUser(targetUser.getOpenid());
            mankeepMessageService.sendMessage(vo);
        } else if (StringUtils.isNotBlank(targetUser.getQyUserId()) && StringUtils.isNotBlank(targetUser.getCorpid())) {
            // 发送企业微信小程序消息
            qywxMiniAppMessageManager.sendPrivateMessageNotice(targetUid, fsBindEntity.getFsEa(), targetUser.getQyUserId(), sendUser.getName(), new Date().getTime());
        }
    }
}
