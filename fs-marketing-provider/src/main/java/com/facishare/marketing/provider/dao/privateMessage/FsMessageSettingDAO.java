package com.facishare.marketing.provider.dao.privateMessage;

import com.facishare.marketing.provider.entity.privateMessage.FsMessageSettingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2020/10/10
 **/
public interface FsMessageSettingDAO {

    @Select(" SELECT * FROM fs_message_setting WHERE uid = #{uid}")
    FsMessageSettingEntity getFsMessageSetting(@Param("uid") String uid);

    @Insert("<script>"
        + " INSERT INTO fs_message_setting(\n"
        + "        \"id\",\n"
        + "        \"uid\",\n"
        + "        \"turn_on_synchronize\",\n"
        + "        \"create_time\",\n"
        + "        \"update_time\"\n"
        + "        )\n"
        + "        VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.uid},\n"
        + "        #{obj.turnOnSynchronize},\n"
        + "        now(),\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;"
        + "</script>")
    void insertFsMessageSettingData(@Param("obj") FsMessageSettingEntity fsMessageSettingEntity);

}
