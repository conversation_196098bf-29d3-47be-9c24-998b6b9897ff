package com.facishare.marketing.provider.entity.live;

import com.facishare.marketing.common.enums.live.LiveUserActionTypeEnum;
import com.facishare.marketing.common.enums.live.LiveUserStatusEnum;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Created  By zhoux 2020/12/25
 **/
@Data
public class LiveUserStatusEntity implements Serializable {

    private String id;

    private Integer liveId;

    private String phone;

    private Integer viewStatus;

    private Integer replayStatus;

    private Integer viewTime;

    private Integer replayTime;

    private Date lastViewTime;

    private Date lastReplayTime;

    private Integer interactiveCount;

    private Date createTime;

    private Date updateTime;

    private int type;     //LivePlatformEnum
    private String xiaoetongLiveId;

    /**
     * 外部平台用户id,如：小鹅通用户id
     */
    private String outerUserId;

    public void initStatus(List<LiveUserActionTypeEnum> doAction) {
        viewStatus = doAction.contains(LiveUserActionTypeEnum.VIEW) ? LiveUserStatusEnum.ACTION_PERFORMED.getStatus() : LiveUserStatusEnum.ACTION_NOT_PERFORMED.getStatus();
        replayStatus = doAction.contains(LiveUserActionTypeEnum.REPLAY) ? LiveUserStatusEnum.ACTION_PERFORMED.getStatus() : LiveUserStatusEnum.ACTION_NOT_PERFORMED.getStatus();
        viewTime = 0;
        replayTime = 0;
        interactiveCount = 0;
    }

}
