package com.facishare.marketing.provider.service.pay;

import com.facishare.marketing.api.service.pay.PayResultCallbackService;
import com.facishare.marketing.common.contstant.WxPaySignType;
import com.facishare.marketing.common.contstant.pay.PayOrderState;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.WxPaySignUtil;
import com.facishare.marketing.provider.dao.pay.PayOrderDao;
import com.facishare.marketing.provider.entity.pay.PayOrderEntity;
import com.facishare.marketing.provider.manager.MiniappSubscribeMessageManager;
import com.facishare.marketing.provider.manager.pay.MerchantConfigManager;
import com.fxiaoke.wechatrestapi.common.constants.PayResultCodeConstants;
import com.fxiaoke.wechatrestapi.common.constants.PayReturnCodeConstants;
import com.github.zhxing.retrofitspring.common.util.XmlParseUtil;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service("payResultCallbackService")
public class PayResultCallbackServiceImpl implements PayResultCallbackService {
    @Autowired
    private PayOrderDao payOrderDao;
    @Autowired
    private MerchantConfigManager merchantConfigManager;
    @Autowired
    private MiniappSubscribeMessageManager miniappSubscribeMessageManager;

    @Override
    public Result<String> miniAppPaid(String xmlContent) {
        MiniAppPaidResult miniAppPaidResult = XmlParseUtil.fromXml(xmlContent, MiniAppPaidResult.class);
        if(!PayReturnCodeConstants.SUCCESS.equals(miniAppPaidResult.getReturnCode())){
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("FAIL", "return_code not SUCCESS")));
        }
        if(!PayResultCodeConstants.SUCCESS.equals(miniAppPaidResult.getResultCode())){
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("FAIL", "result_code not SUCCESS")));
        }
        PayOrderEntity payOrder = payOrderDao.getById(miniAppPaidResult.getOutTradeNo());
        if(payOrder == null){
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("FAIL", "order not existed")));
        }
        String signType = Strings.isNullOrEmpty(miniAppPaidResult.getSignType()) ? WxPaySignType.HMAC_SHA256.getSignType() : miniAppPaidResult.getSignType();
        if(!WxPaySignUtil.verifySign(miniAppPaidResult, merchantConfigManager.getAppSecretByEa(payOrder.getEa()), signType, miniAppPaidResult.getSign())){
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("FAIL", "signVerifyError")));
        }
        if (!payOrder.getTotalFee().equals(Integer.valueOf(miniAppPaidResult.getTotalFee()))){
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("FAIL", "total_fee not equal")));
        }
        PayOrderEntity oldPayOrder = payOrderDao.getById(miniAppPaidResult.getOutTradeNo());
        if(PayOrderState.SUCCESS.name().equals(oldPayOrder.getState())){
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("SUCCESS", "OK")));
        }
        int updateRow = payOrderDao.updatePayStateAndWxTransactionId(miniAppPaidResult.getOutTradeNo(), PayOrderState.SUCCESS.name(), ImmutableSet.of(PayOrderState.CREATED.name(), PayOrderState.NOT_PAY.name(), PayOrderState.PAY_ERROR.name()), miniAppPaidResult.getTransactionId());
        if (updateRow > 0) {
            miniappSubscribeMessageManager.sendPayOrderPaidMessage(miniAppPaidResult.getOutTradeNo());
        } else{
            return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("FAIL", "update db fail")));
        }
        return Result.newSuccess(XmlParseUtil.toXml(new MiniAppPaidResultHandleResult("SUCCESS", "OK")));
    }

    @Getter
    @XStreamAlias("xml")
    private static final class MiniAppPaidResultHandleResult{
        @XStreamAlias("return_code")
        private String returnCode;
        @XStreamAlias("return_msg")
        private String returnMsg;

        public MiniAppPaidResultHandleResult(String returnCode, String returnMsg) {
            this.returnCode = returnCode;
            this.returnMsg = returnMsg;
        }
    }

    @Getter
    @XStreamAlias("xml")
    private static final class MiniAppPaidResult extends XmlParseUtil.AllPropertiesCollector implements Serializable {
        @XStreamAlias("return_code")
        private String returnCode;
        @XStreamAlias("return_msg")
        private String returnMSg;
        @XStreamAlias("appid")
        private String appId;
        @XStreamAlias("mch_id")
        private String mchId;
        @XStreamAlias("device_info")
        private String deviceInfo;
        @XStreamAlias("nonce_str")
        private String nonceStr;
        @XStreamAlias("sign")
        private String sign;
        @XStreamAlias("sign_type")
        private String signType;
        @XStreamAlias("result_code")
        private String resultCode;
        @XStreamAlias("err_code")
        private String errCode;
        @XStreamAlias("err_code_des")
        private String errCodeDes;
        @XStreamAlias("openid")
        private String openId;
        @XStreamAlias("is_subscribe")
        private String isSubscribe;
        @XStreamAlias("trade_type")
        private String tradeType;
        @XStreamAlias("bank_type")
        private String bankType;
        @XStreamAlias("fee_type")
        private String feeType;
        @XStreamAlias("total_fee")
        private String totalFee;
        @XStreamAlias("settlement_total_fee")
        private String settlementTotalFee;
        @XStreamAlias("cash_fee")
        private String cashFee;
        @XStreamAlias("cash_fee_type")
        private String cashFeeType;
        @XStreamAlias("coupon_fee")
        private String couponFee;
        @XStreamAlias("transaction_id")
        private String transactionId;
        @XStreamAlias("out_trade_no")
        private String outTradeNo;
        @XStreamAlias("attach")
        private String attach;
        @XStreamAlias("time_end")
        private String timeEnd;
    }
}
