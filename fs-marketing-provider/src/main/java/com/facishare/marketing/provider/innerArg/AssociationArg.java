package com.facishare.marketing.provider.innerArg;

import com.facishare.marketing.common.enums.ChannelEnum;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Strings;
import lombok.Data;

import java.io.Serializable;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/3/7.
 */
@Data
public class AssociationArg implements Serializable {
    /**
     * {@link ChannelEnum}
     */
    private int type;
    /**
     * 企业ea
     */
    private String ea;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 微联服务号才用填
     */
    private String wxAppId;
    private String associationId;
    private String additionalAssociationId;
    private String userName;
    private String email;

    /**
     * 自定义对象apiname
     */
    private String objectApiName;

    private ObjectData objectData;

    //营销用户变更记录
    /**
     *源类型区分创建营销用户或关联关系原始类型
     */
    private String triggerSource;
    private String triggerAction;

    public static AssociationArg buildFromCrmObject(String ea, String crmObjectApiName, String crmObjectId, String phone){
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(ea);
        associationArg.setType(ChannelEnum.getByApiName(crmObjectApiName).getType());
        associationArg.setAssociationId(crmObjectId);
        associationArg.setPhone(phone);
        return associationArg;
    }
    
    public boolean isHavePhone(){
        return !Strings.isNullOrEmpty(phone);
    }

}
