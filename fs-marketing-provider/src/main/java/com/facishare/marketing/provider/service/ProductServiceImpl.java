package com.facishare.marketing.provider.service;

import com.facishare.mankeep.api.outService.arg.cover.CreateLuckyMoneyIconCoverAsyncArg;
import com.facishare.mankeep.api.outService.service.OutCoverService;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.cta.CtaRelationInfo;
import com.facishare.marketing.api.service.ObjectSloganRelationService;
import com.facishare.marketing.api.service.ProductService;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.manager.MaterialRelationDaoManager;
import com.facishare.marketing.provider.dao.manager.ProductDAOManager;
import com.facishare.marketing.provider.dao.param.product.ProductQueryParam;
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao;
import com.facishare.marketing.provider.dto.CustomizeFormUserDataCountByObjectIdDTO;
import com.facishare.marketing.provider.dto.ObjectStatisticData;
import com.facishare.marketing.provider.dto.ProductEntityDTO;
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.marketing.statistic.outapi.arg.ActionDurationTimeObjectArg;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.marketing.statistic.outapi.result.ActionDurationTimeAvgByObjectResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Service("productService")
@Slf4j
public class ProductServiceImpl implements ProductService {
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MaterialTagManager materialTagManager;
    @Autowired
    private OutCoverService outCoverService;
    @ReloadableProperty("picture.preview.url")
    private String sharePath;
    @ReloadableProperty("picture.fsEa")
    private String ea;
    @Value("${prdouctData}")
    private String prdouctData;//产品初始化
    @ReloadableProperty("prdouctData.apath")
    private String prdouctDataApath;
    @ReloadableProperty("prdouctData.thumbsPath")
    private String prdouctDataThumbsPath;
    @ReloadableProperty("prdouctData.oldPath")
    private String prdouctDataOldPath;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private ProductDAOManager productDAOManager;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO marketingEventMaterialRelationDAO;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private HexagonSiteManager hexagonSiteManager;
    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;
    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    private static CountDownLatch photoTAToApathProcessLatch;

    @Autowired
    private MaterialRelationDao materialRelationDao;

    @Autowired
    private MaterialRelationDaoManager materialRelationDaoManager;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private  CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MarketingObjectAmountStatisticDao marketingObjectAmountStatisticDao;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectSloganRelationService objectSloganRelationService;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Override
    public Result<AddEnterpriseProductResult> addEnterpriseProduct(String fsEa, Integer fsUserId, AddProductArg vo) {
        ProductEntity productEntity = new ProductEntity();
        productEntity.setType(ProductArticleTypeEnum.CORPORATE.getType());
        productEntity.setDiscountPrice(vo.getDiscountPrice());
        productEntity.setName(vo.getName());
        productEntity.setPrice(vo.getPrice());
        productEntity.setVideo(vo.getVideo());
        productEntity.setSummary(vo.getSummary());
        productEntity.setFsEa(fsEa);
        productEntity.setFsUserId(fsUserId);
        productEntity.setTryOutEnable(vo.getTryOutEnable());
        productEntity.setTryOutFormObjectApiName(vo.getTryOutFormObjectApiName());
        productEntity.setTryOutButtonValue(vo.getTryOutButtonValue());
        productEntity.setVideoCoverUrl(vo.getVideoCoverUrl());
        String id = vo.getId();
        if (StringUtils.isNotEmpty(id)) {
            productEntity.setId(id);

            //修改产品信息
            ProductEntity oldProductEntity = productDAO.queryProductDetail(productEntity.getId());
            if (oldProductEntity == null) {
                log.info("update product failed product is not exist vo:{}", vo);
                return new Result(SHErrorCode.PRODUCT_DETAIL_FAIL);
            }
            if (!StringUtils.equals(vo.getName(), oldProductEntity.getName())){
                int count = productDAO.queryProductCountByName(fsEa, vo.getName());
                if (count > 0){
                    return Result.newError(SHErrorCode.PRODUCT_NAME_EXIST);
                }
            }

            //是否需要对图片进行变动操作
            if (vo.getIsOperation() == PicOperationFlagEnum.OPERATION_PIC.getType()) {
                updateProductPhoto(vo.getId(), vo.getHeadPics(), vo.getDetailPics());
            }

            productEntity.setLastModifyTime(new Date());

            boolean result = productDAOManager.updateProduct(productEntity);
            if (!result) {
                return new Result(SHErrorCode.PRODUCT_UPDATE_FAIL);
            }
            materialRelationDaoManager.insertOrUpdateMaterialRelationEntity(vo.getId(),ObjectTypeEnum.PRODUCT.getType(),vo.getSharePosterAPath(),oldProductEntity.getFsEa());
            CreateLuckyMoneyIconCoverAsyncArg createLuckyMoneyIconCoverAsyncArg = new CreateLuckyMoneyIconCoverAsyncArg();
            createLuckyMoneyIconCoverAsyncArg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
            createLuckyMoneyIconCoverAsyncArg.setObjectId(id);
        //    outCoverService.createLuckyMoneyIconCoverAsync(createLuckyMoneyIconCoverAsyncArg);

            // 更新表单设置
            if (BindObjectType.CUSTOMIZE_FORM.getType().equals(vo.getBindObjectType())) {
                HexagonSiteObjectEntity entity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(fsEa, id, ObjectTypeEnum.PRODUCT.getType());
                if (entity != null) { // 表单和微页面切换绑定需要先解绑另一个
                    hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(entity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
                }
                customizeFormDataManager.updateCustomizeFormDataObject(vo.getFormId(), id, ObjectTypeEnum.PRODUCT.getType(), fsEa, fsUserId, null, null, null);
            }

            // 更新微页面设置
            if (BindObjectType.HEXAGON_SITE.getType().equals(vo.getBindObjectType())) {
                CustomizeFormDataObjectEntity oldCustomizeFormDataObjectEntity = customizeFormDataObjectDAO.getObjectBindingForm(fsEa, id, ObjectTypeEnum.PRODUCT.getType());
                if (oldCustomizeFormDataObjectEntity != null) {
                    customizeFormDataObjectDAO.deleteCustomizeFormDataObject(fsEa, oldCustomizeFormDataObjectEntity.getFormId(), id, ObjectTypeEnum.PRODUCT.getType());
                }
                hexagonSiteManager.bindHexagonSiteObject(vo.getSiteId(), id, ObjectTypeEnum.PRODUCT.getType(), fsEa, fsUserId, null, null, null);
            }
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(fsEa, fsUserId, ObjectTypeEnum.PRODUCT.getType(), productEntity.getName(), OperateTypeEnum.EDIT);
        } else {
            int count = productDAO.queryProductCountByName(fsEa, vo.getName());
            if (count > 0){
                return Result.newError(SHErrorCode.PRODUCT_NAME_EXIST);
            }
            id = UUIDUtil.getUUID();
            productEntity.setId(id);
            productEntity.setStatus(ProductStatusEnum.NORMAL.getStatus());
            ProductEntity minProduct = productDAO.findMinByEaOrder(fsEa, null);
            if (minProduct == null) {
                productEntity.setOrderNum(1000);
            } else {
                productEntity.setOrderNum(minProduct.getOrderNum() - 1);
            }
            boolean result = productDAOManager.addProduct(productEntity);
            if (!result) {
                return new Result(SHErrorCode.PRODUCT_CREATE_FAIL);
            }
            if(!Strings.isNullOrEmpty(vo.getSharePosterAPath())){
                MaterialRelationEntity entity = new MaterialRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(fsEa);
                entity.setObjectType(ObjectTypeEnum.PRODUCT.getType());
                entity.setObjectId(id);
                entity.setSharePosterAPath(vo.getSharePosterAPath());
                materialRelationDaoManager.insertMaterialRelationEntity(entity);
            }
            // 添加产品封面图
            addProductPhoto(fsEa,vo.getHeadPics(), PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), productEntity.getId());
            // 添加产品详情图
            addProductPhoto(fsEa,vo.getDetailPics(), PhotoTargetTypeEnum.PRODUCT_DETAIL.getType(), productEntity.getId());

            CreateLuckyMoneyIconCoverAsyncArg createLuckyMoneyIconCoverAsyncArg = new CreateLuckyMoneyIconCoverAsyncArg();
            createLuckyMoneyIconCoverAsyncArg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
            createLuckyMoneyIconCoverAsyncArg.setObjectId(id);
            outCoverService.createLuckyMoneyIconCoverAsync(createLuckyMoneyIconCoverAsyncArg);

            // 创建表单关联
            if (StringUtils.isNotBlank(vo.getFormId()) && BindObjectType.CUSTOMIZE_FORM.getType().equals(vo.getBindObjectType())) {
                customizeFormDataManager.bindCustomizeFormDataObject(vo.getFormId(), id, ObjectTypeEnum.PRODUCT.getType(), fsEa, fsUserId, null, null, null);
            } else if (BindObjectType.HEXAGON_SITE.getType().equals(vo.getBindObjectType())) {
                // 创建微页面关联
                hexagonSiteManager.bindHexagonSiteObject(vo.getSiteId(), id, ObjectTypeEnum.PRODUCT.getType(), fsEa, fsUserId, null, null, null);
            }

            //上报神策系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(fsEa, fsUserId, ObjectTypeEnum.PRODUCT.getType(), null, id));
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(fsEa, fsUserId, ObjectTypeEnum.PRODUCT.getType(), vo.getName(), OperateTypeEnum.ADD);
            objectGroupManager.setGroup(fsEa, fsUserId, ObjectTypeEnum.PRODUCT.getType(), Collections.singletonList(id), vo.getGroupId());
        }
        ctaRelationDaoManager.addCtaRelation(fsEa, vo.getCtaIds(), ObjectTypeEnum.PRODUCT.getType(), productEntity.getId());
        if(StringUtils.isNotBlank(vo.getOriginalImageAPath()) && CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
            for (PhotoCutOffset cutOffset:vo.getCutOffsetList()){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(fsEa,PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER, id, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(fsEa,PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER, id, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(fsEa,PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER, id, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                }
            }
        }

        AddEnterpriseProductResult result = new AddEnterpriseProductResult();
        result.setId(id);

        return Result.newSuccess(result);
    }

    @Override
    public Result deleteEnterpriseProduct(String ea, Integer fsUserId, String productId) {
        ProductEntity productEntity = productDAO.queryProductDetail(productId);
        if (productEntity.getTryOutEnable() == true) {
            return new Result(SHErrorCode.PRODUCT_CAN_NOT_DELETE, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_PRODUCTSERVICEIMPL_306));
        }
        productDAOManager.updateStatus(ea, fsUserId, productEntity.getId(), ProductStatusEnum.DELETED.getStatus());
        customizeFormDataManager.unBindCustomizeFormDataObject(productId, ObjectTypeEnum.PRODUCT.getType(), ea);
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(ObjectTypeEnum.PRODUCT.getType(), productId);
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.PRODUCT.getType(), Collections.singletonList(productId));
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.PRODUCT.getType(), Collections.singletonList(productId));
        ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.PRODUCT.getType(), Lists.newArrayList(productId));
        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<Void> addInitProductData(String ea, Integer fsUserId, AddProductInitArg arg) {
        // 先判断预置产品是否已经存在
        List<ProductEntity> productEntityList = productDAO.queryProductByName(ea, arg.getName());
        if (CollectionUtils.isNotEmpty(productEntityList)) {
            return new Result(SHErrorCode.PRODUCT_NAME_EXIST);
        }
        String id = UUIDUtil.getUUID();

        ProductEntity productEntity = new ProductEntity();
        productEntity.setType(ProductArticleTypeEnum.CORPORATE.getType());
        productEntity.setDiscountPrice(arg.getDiscountPrice());
        productEntity.setName(arg.getName());
        productEntity.setPrice(arg.getPrice());
        productEntity.setVideo(arg.getVideo());
        productEntity.setSummary(arg.getSummary());
        productEntity.setFsEa(ea);
        productEntity.setFsUserId(fsUserId);
        productEntity.setTryOutEnable(arg.getTryOutEnable());
        productEntity.setTryOutFormObjectApiName(arg.getTryOutFormObjectApiName());
        productEntity.setTryOutButtonValue(arg.getTryOutButtonValue());
        productEntity.setId(id);
        productEntity.setStatus(ProductStatusEnum.NORMAL.getStatus());
        ProductEntity minProduct = productDAO.findMinByEaOrder(ea, null);
        if (minProduct == null) {
            productEntity.setOrderNum(1000);
        } else {
            productEntity.setOrderNum(minProduct.getOrderNum() - 1);
        }
        boolean result = productDAOManager.addProduct(productEntity);
        if (!result) {
            return new Result(SHErrorCode.PRODUCT_CREATE_FAIL);
        }
        addInitProductPhoto(ea,arg.getThumbsPath(), arg.getPath(),  productEntity.getId());

        CreateLuckyMoneyIconCoverAsyncArg createLuckyMoneyIconCoverAsyncArg = new CreateLuckyMoneyIconCoverAsyncArg();
        createLuckyMoneyIconCoverAsyncArg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        createLuckyMoneyIconCoverAsyncArg.setObjectId(id);
        outCoverService.createLuckyMoneyIconCoverAsync(createLuckyMoneyIconCoverAsyncArg);

        return new Result(SHErrorCode.SUCCESS);
    }

    /**
     * 添加产品图片
     *
     * @param tapaths tapaths列表
     * @param type 图片类型 header or detail
     */
    public void addProductPhoto(String ea,List<String> tapaths, int type, String targetId) {
        log.info("addProductPhoto tapaths:{}  type:{}  targetId:{}", tapaths, type, targetId);
        try {
            if (CollectionUtils.isEmpty(tapaths)) {
                return;
            }
            List<PhotoEntity> photoEntities = Lists.newArrayList();
            for (int i = 0; i < tapaths.size(); i++) {
                String tapath = tapaths.get(i);
                PhotoEntity photoDO = new PhotoEntity();
                photoDO.setId(UUIDUtil.getUUID());
                photoDO.setTargetType(type);
                photoDO.setTargetId(targetId);
                photoDO.setSeqNum(i);
                if (tapath.startsWith(Constant.TEMP_A_WAREHOUSE_TYPE)) {
                    Long photoStartMills = new Date().getTime();
                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(tapath, null, null);
                    Long photoEndMills = new Date().getTime();
                    log.info("ProductServiceImpl.addProductPhoto index={}, delay={}", i, photoEndMills-photoStartMills);
                    if (null != fileManagerPicResult) {
                        String apath = fileV2Manager.getCpathByPath(ea, fileManagerPicResult.getUrlAPath());
                        String thumbnailApath = fileV2Manager.getCpathByPath(ea, fileManagerPicResult.getThumbUrlApath());
                        String url = fileV2Manager.getUrlByPath(ea, apath);
                        String thumbnailUrl = fileV2Manager.getUrlByPath(ea, thumbnailApath);
                        if (StringUtils.isBlank(thumbnailUrl)) {
                            thumbnailUrl = url;
                        }
                        if (StringUtils.isBlank(thumbnailApath)) {
                            thumbnailApath = apath;
                        }
                        photoDO.setThumbnailUrl(thumbnailUrl);
                        photoDO.setUrl(url);
                        photoDO.setThumbnailPath(thumbnailApath);
                        photoDO.setPath(apath);
                    }
                } else if(tapath.startsWith("C_")){
                    String cpath = tapath;
                    String url = fileV2Manager.getUrlByPath(cpath, ea, false);
                    photoDO.setThumbnailUrl(url);
                    photoDO.setUrl(url);
                    photoDO.setThumbnailPath(cpath);
                    photoDO.setPath(cpath);
                } else if(tapath.startsWith(Constant.A_WAREHOUSE_TYPE)){
                    String cpath = fileV2Manager.getCpathByPath(ea, tapath);
                    String url = fileV2Manager.getUrlByPath(cpath, ea, false);
                    photoDO.setThumbnailUrl(url);
                    photoDO.setUrl(url);
                    photoDO.setThumbnailPath(cpath);
                    photoDO.setPath(cpath);
                }
                photoEntities.add(photoDO);
            }

            log.info("add photo count:{} type:{}", photoEntities.size(), type);
            if (CollectionUtils.isNotEmpty(photoEntities)) {
                photoDAO.batchInsert(photoEntities);
            }

        } catch (Exception e) {
            log.error("ProductServiceImpl.addProductPhoto exception:", e);
        }
    }

    /**
     * 更新产品封面分享图
     * @param productId
     */
    public void updateProductMiniCover(String productId){
        List<PhotoEntity> photoEntityList = photoManager.listByTargetIdAndTargetType(productId, PhotoTargetTypeEnum.PRODUCT_HEAD.getType(),null);
        ThreadPoolUtils.execute(() -> {
            photoManager.addOrUpdateMiniCoverPhoto(null,productId, PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL, photoEntityList.get(0).getPath());
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
    }

    private void addInitProductPhoto(String ea,List<String> thumbsPathList, List<String> pathList, String targetId) {
        if (CollectionUtils.isEmpty(thumbsPathList) || CollectionUtils.isEmpty(pathList)) {
            return;
        }
        List<PhotoEntity> photoEntities = new ArrayList<>(pathList.size());
        for (int i = 0; i < pathList.size(); i++) {
            String path = pathList.get(i);
            String thumbsPath = thumbsPathList.get(i);
            if (StringUtils.isBlank(path) || StringUtils.isBlank(thumbsPath)) {
                continue;
            }
            String pathUrl = fileV2Manager.getUrlByPath(path, ea, false);
            String thumbsPathUrl = fileV2Manager.getUrlByPath(thumbsPath, ea, false);
            if (StringUtils.isBlank(pathUrl) || StringUtils.isBlank(thumbsPathUrl)) {
                continue;
            }
            PhotoEntity photoDO = new PhotoEntity();
            photoDO.setId(UUIDUtil.getUUID());
            photoDO.setTargetId(targetId);
            photoDO.setThumbnailUrl(thumbsPathUrl);
            photoDO.setUrl(pathUrl);
            photoDO.setSeqNum(i);
            if (i == 0) {
                photoDO.setTargetType(PhotoTargetTypeEnum.PRODUCT_HEAD.getType());
            } else {
                photoDO.setTargetType(PhotoTargetTypeEnum.PRODUCT_DETAIL.getType());
            }
            photoDO.setPath(path);
            photoDO.setThumbnailPath(thumbsPath);
            photoEntities.add(photoDO);
        }
        log.info("add photo count:{}", photoEntities.size());
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoDAO.batchInsert(photoEntities);
        }
    }

    private void addProductPhoto(String ea,List<PhotoEntity> photoList, String targetId) {

        if (CollectionUtils.isEmpty(photoList)) {
            return;
        }
        if (StringUtils.isBlank(ea)){
            ea = this.ea;
        }
        List<PhotoEntity> photoEntities = Lists.newArrayList();
        List<PhotoEntity> syncHandlePhotos = Lists.newArrayList();
        for (PhotoEntity photoDO : photoList) {
            String tapath = photoDO.getPath();
            log.info("addProductPhoto tapath:{}  photoDO:{}", tapath, photoDO);
            if (tapath.startsWith(Constant.TEMP_A_WAREHOUSE_TYPE)) {
                //tapath文件转apath文件
                List<String> paths = fileManager.saveTAFileFromTmp(tapath);
                log.info("add product paths:{}", paths);
                if (CollectionUtils.isNotEmpty(paths)) {
                    String path = fileV2Manager.getCpathByPath(ea, paths.get(0));
                    String photoUrl = fileV2Manager.getUrlByPath(ea, path);
                    photoDO.setThumbnailUrl(photoUrl);
                    photoDO.setUrl(photoUrl);
                    photoDO.setPath(path);
                    photoDO.setThumbnailPath(path);
                }
            } else if (tapath.startsWith(Constant.A_WAREHOUSE_TYPE)){
                String path = fileV2Manager.getCpathByPath(ea, tapath);
                String url = fileV2Manager.getUrlByPath(ea, path);
                photoDO.setThumbnailUrl(url);
                photoDO.setUrl(url);
                photoDO.setPath(path);
                photoDO.setThumbnailPath(path);
            } else if (tapath.startsWith("C_")){
                String path = tapath;
                String url = fileV2Manager.getUrlByPath(ea, path);
                photoDO.setThumbnailUrl(url);
                photoDO.setUrl(url);
                photoDO.setPath(path);
                photoDO.setThumbnailPath(path);
            }
            photoDO.setId(UUIDUtil.getUUID());
            photoDO.setTargetId(targetId);
            photoEntities.add(photoDO);
            syncHandlePhotos.add(photoDO);
        }

        log.info("add photo count:{} type:{}  photoEntities:{}", photoEntities.size(), photoEntities);
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoDAO.batchInsert(photoEntities);
        }
    }

    private void updatePhotosequenceNumber(List<PhotoEntity> updatePicList) {
        if (CollectionUtils.isEmpty(updatePicList)) {
            return;
        }
        //逐条更新
        for (PhotoEntity photoEntity : updatePicList) {
            photoDAO.updatePhotoSequenceNumber(photoEntity.getId(), photoEntity.getSeqNum());
        }
    }

    /**
     * 更新产品封面图和详情图
     * @param productId
     * @param headThumbnailPicList
     * @param detailThumbnailPicList
     */
    public void updateProductPhoto(String productId, List<String> headThumbnailPicList, List<String> detailThumbnailPicList){
        // 处理封面图
        handleHeadPhoto(productId, headThumbnailPicList);
        // 处理详情图
        handleDetailPhoto(productId, detailThumbnailPicList);
    }

    /**
     * 处理产品详情图
     * @param productId
     * @param detailThumbnailPicList
     */
    private void handleDetailPhoto(String productId, List<String> detailThumbnailPicList){
        if (CollectionUtils.isEmpty(detailThumbnailPicList)) {
            return;
        }
        //获取数据库全部数据
//        List<PhotoEntity> photoEntities = photoManager.listProductPhotos(productId);
        List<PhotoEntity> photoEntities = photoDAO.listProductPhotos(productId);

        //添加图片容器
        List<PhotoEntity> addPicList = Lists.newArrayList();

        //更新代码顺序
        List<PhotoEntity> updateDetailPicList = Lists.newArrayList();
        List<String> updateDetailPicUrlList = Lists.newArrayList();

        //删除图片容器
        List<String> deletePicList = Lists.newArrayList();
        List<String> deletePicIdList = Lists.newArrayList();

        //找出需要删除的图片
        for (PhotoEntity photoEntity : photoEntities) {
            //找出需求删除的图片
            if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                if (!detailThumbnailPicList.contains(photoEntity.getThumbnailUrl())) {
                    //保存要删除的详情文件文件apath和ID
                    deletePicList.add(photoEntity.getPath());
                    deletePicIdList.add(photoEntity.getId());
                } else {
                    updateDetailPicList.add(photoEntity);
                    updateDetailPicUrlList.add(photoEntity.getThumbnailUrl());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(deletePicList)) {
            log.info("photoManipulation deletePicList:{}", deletePicList);
            deleteProductPhotos(deletePicList, deletePicIdList);
        }

        //更新顺序
        for (int index = 0; index < detailThumbnailPicList.size(); index++) {
            int picLocation = updateDetailPicUrlList.indexOf(detailThumbnailPicList.get(index));
            if (picLocation != -1) {
                updateDetailPicList.get(picLocation).setSeqNum(index);
            } else {
                PhotoEntity picEntity = new PhotoEntity();
                picEntity.setTargetType(PhotoTargetTypeEnum.PRODUCT_DETAIL.getType());
                picEntity.setPath(detailThumbnailPicList.get(index));
                picEntity.setSeqNum(index);
                addPicList.add(picEntity);
            }
        }

        if (CollectionUtils.isNotEmpty(addPicList)) {
            ProductEntity product = productDAO.getById(productId);
            addProductPhoto(product==null?null:product.getFsEa(), addPicList, productId);
        }

        if (CollectionUtils.isNotEmpty(updateDetailPicList)) {
            updatePhotosequenceNumber(updateDetailPicList);
        }
    }

    /**
     * 处理产品封面图（头图）
     * @param productId
     * @param headThumbnailPicList
     */
    private void handleHeadPhoto(String productId, List<String> headThumbnailPicList){
        if (CollectionUtils.isEmpty(headThumbnailPicList)) {
            return;
        }

        // 查询原有封面图
//        List<PhotoEntity> photoEntities = photoManager.listByTargetIdAndTargetType(productId, PhotoTargetTypeEnum.PRODUCT_HEAD.getType());
        List<PhotoEntity> photoEntities = photoDAO.listByTargetIdsAndTargetType(productId, PhotoTargetTypeEnum.PRODUCT_HEAD.getType());

        // 封面图中有无TA_PATH，如果没有，并且图片数量没有变化，说明封面图没有修改，不用处理
        boolean anyMatch = headThumbnailPicList.stream().anyMatch(s -> s.startsWith(Constant.TEMP_A_WAREHOUSE_TYPE) || s.startsWith(Constant.C_WAREHOUSE_TYPE));
        if (!anyMatch && photoEntities.size() == headThumbnailPicList.size()) {
            return;
        }

        List<PhotoEntity> needAddPicList = Lists.newArrayList();
        for (int i = 0; i < headThumbnailPicList.size(); i++) {
            String headThumbnailPic = headThumbnailPicList.get(i);
            // TA_PATH 新增
            if (headThumbnailPic.startsWith(Constant.TEMP_A_WAREHOUSE_TYPE)||headThumbnailPic.startsWith(Constant.C_WAREHOUSE_TYPE)) {
                PhotoEntity photoEntity = new PhotoEntity();
                photoEntity.setTargetType(PhotoTargetTypeEnum.PRODUCT_HEAD.getType());
                photoEntity.setPath(headThumbnailPic);
                photoEntity.setSeqNum(i);
                needAddPicList.add(photoEntity);
            } else {
                // 说明是要更新的
                List<PhotoEntity> entities = photoDAO.listByTargetIdsAndTargetTypeAndUrl(productId, PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), headThumbnailPic);
                if (CollectionUtils.isNotEmpty(entities)) {
                    PhotoEntity entity = entities.get(0);
                    photoDAO.updatePhotoSequenceNumber(entity.getId(), i);
                }
            }
        }

        // 找出需要删除的封面图
        List<String> deletePicIdList = Lists.newArrayList();
        for (PhotoEntity photoEntity : photoEntities) {
            if (!headThumbnailPicList.contains(photoEntity.getThumbnailUrl())) {
                deletePicIdList.add(photoEntity.getId());
            }
        }

        // 删除封面图
        if (CollectionUtils.isNotEmpty(deletePicIdList)) {
            photoDAO.deletePhotos(deletePicIdList);
        }
        // 插入封面图->轮播图
        ProductEntity product = productDAO.getById(productId);
        addProductPhoto(product==null?null:product.getFsEa(),needAddPicList, productId);
        // 更新封面分享图
//        updateProductMiniCover(productId);
    }

    @Override
    public Result<QueryProductDetailResult> queryProductDetail(String productId) {
        // Refactor by Smallfan on 2018.5.2
        ProductEntity productEntity = productDAO.queryProductDetail(productId);
        if (productEntity == null) {
            return new Result<>(SHErrorCode.PRODUCT_DETAIL_FAIL);
        }

        // TODO 可以优化(调用一次就好)
//        List<PhotoEntity> photoEntityList = photoManager.listProductPhotos(productId);
        List<PhotoEntity> photoEntityList = photoDAO.listProductPhotos(productId);
        QueryProductDetailResult queryProductDetailResult = new QueryProductDetailResult();
        queryProductDetailResult.setId(productEntity.getId());
        queryProductDetailResult.setType(productEntity.getType());
        queryProductDetailResult.setName(productEntity.getName());
        queryProductDetailResult.setPrice(productEntity.getPrice());
        queryProductDetailResult.setDiscountPrice(productEntity.getDiscountPrice());
        queryProductDetailResult.setSummary(productEntity.getSummary());
        queryProductDetailResult.setVideo(productEntity.getVideo());
        queryProductDetailResult.setTryOutEnable(productEntity.getTryOutEnable());
        queryProductDetailResult.setTryOutFormObjectApiName(productEntity.getTryOutFormObjectApiName());
        queryProductDetailResult.setTryOutButtonValue(productEntity.getTryOutButtonValue());
        queryProductDetailResult.setSubmitCount(productEntity.getSubmitCount() == null ? 0 : productEntity.getSubmitCount());
        queryProductDetailResult.setVideoCoverUrl(productEntity.getVideoCoverUrl());
        MaterialRelationEntity relationEntity = materialRelationDao.queryMaterialRelationByObjectId(productId, ObjectTypeEnum.PRODUCT.getType());
        if(relationEntity!=null&&relationEntity.getSharePosterAPath()!=null){
            queryProductDetailResult.setSharePosterAPath(relationEntity.getSharePosterAPath());
            queryProductDetailResult.setSharePosterUrl(fileV2Manager.getUrlByPath(relationEntity.getSharePosterAPath(),productEntity.getFsEa(),false));
        }

        List<String> headPicsList = Lists.newArrayList();
        List<String> detailPicsList = Lists.newArrayList();
        List<String> headPicsThumbList = Lists.newArrayList();
        List<String> detailPicsThumbList = Lists.newArrayList();
        String headPicThumbAPath = null;
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            boolean isNeedCreateMiniCover = true;
            List<PhotoEntity> createMiniCoverList = Lists.newArrayList();
            Map<String, Long> sizeMap = getDetailPhotoTotalSize(photoEntityList);
            for (PhotoEntity photoEntity : photoEntityList) {
                if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
                    headPicsList.add(photoEntity.getUrl());
                    headPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
                    createMiniCoverList.add(photoEntity);
                    headPicThumbAPath = (StringUtils.isNotBlank(headPicThumbAPath) ? headPicThumbAPath : photoEntity.getThumbnailPath());
                }

                if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                    detailPicsList.add(photoEntity.getUrl());
                    Long size = sizeMap.get(photoEntity.getId());
                    // 如果找不到大小，用缩略图，如果原图大小大于等于1M，那么产品详情页展示缩略图 否则展示原图
                    if (size == null || size / 1024.0 / 1024.0 >= 1) {
                        detailPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
                    } else {
                        detailPicsThumbList.add(photoEntity.getUrl());
                    }
                }

                if (photoEntity.getTargetType() == PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL.getType()){
                    queryProductDetailResult.setCardPhotoUrl(photoEntity.getUrl());
                    isNeedCreateMiniCover = false;
                }
            }
            if (isNeedCreateMiniCover && CollectionUtils.isNotEmpty(createMiniCoverList)) {
                ThreadPoolUtils.execute(() -> {
                    photoManager.addOrUpdateMiniCoverPhoto(productEntity.getFsEa(),productId, PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL, createMiniCoverList.get(0).getPath());
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
        }

        queryProductDetailResult.setHeadPics(headPicsList);
        queryProductDetailResult.setDetailPics(detailPicsList);
        queryProductDetailResult.setHeadPicsThumbs(headPicsThumbList);
        queryProductDetailResult.setDetailPicsThumbs(detailPicsThumbList);
        queryProductDetailResult.setHeadPicThumbAPath(headPicThumbAPath);
        queryProductDetailResult.setCtaRelationInfos(Lists.newArrayList());

        // 查询对应挂接表单
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(productEntity.getFsEa(), productId, ObjectTypeEnum.PRODUCT.getType());
        if(customizeFormDataEntity != null) {
            QueryProductDetailResult.FormData formData = new QueryProductDetailResult.FormData();
            formData.setFormId(customizeFormDataEntity.getId());
            formData.setFormTitle(customizeFormDataEntity.getFormHeadSetting().getTitle());
            formData.setFromName(customizeFormDataEntity.getFormHeadSetting().getName());
            queryProductDetailResult.setFormData(formData);
            queryProductDetailResult.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
        } else {
            // 查询对应挂接微页面   微页面和表单只能挂接一种
            HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(productEntity.getFsEa(), productId, ObjectTypeEnum.PRODUCT.getType());
            if (hexagonSiteObjectEntity != null) {
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteManager.getBindHexagonSiteByObject(hexagonSiteObjectEntity.getSiteId());
                if (hexagonSiteEntity != null) {
                    QueryProductDetailResult.HexagonSiteData hexagonSiteData = new QueryProductDetailResult.HexagonSiteData();
                    hexagonSiteData.setSiteId(hexagonSiteEntity.getId());
                    hexagonSiteData.setSiteName(hexagonSiteEntity.getName());
                    queryProductDetailResult.setHexagonSiteData(hexagonSiteData);
                    queryProductDetailResult.setBindObjectType(BindObjectType.HEXAGON_SITE.getType());
                }
            }
        }
        if(queryProductDetailResult != null && StringUtils.isNotBlank(queryProductDetailResult.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType(), queryProductDetailResult.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                queryProductDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType(), queryProductDetailResult.getId());
            if (coverCutH5PhotoEntity != null) {
                queryProductDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType(), queryProductDetailResult.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                queryProductDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                queryProductDetailResult.setHeadPicThumbAPath(coverCutOrdinaryPhotoEntity.getThumbnailPath());
                //返回原图
                queryProductDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }

            List<CtaRelationEntityDTO> ctaRelationList = ctaRelationDaoManager.getCtaRelationList(productEntity.getFsEa(), ObjectTypeEnum.PRODUCT.getType(), Lists.newArrayList(productEntity.getId()));
            if(CollectionUtils.isNotEmpty(ctaRelationList)){
                ctaRelationList.forEach(ctaRelationEntity -> {
                    CtaRelationInfo ctaRelationInfo = new CtaRelationInfo();
                    ctaRelationInfo.setCtaId(ctaRelationEntity.getCtaId());
                    ctaRelationInfo.setCtaName(ctaRelationEntity.getCtaName());
                    queryProductDetailResult.getCtaRelationInfos().add(ctaRelationInfo);
                });
            }
        }
        SetSloganArg sloganArg = new SetSloganArg();
        sloganArg.setObjectId(productEntity.getId());
        sloganArg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        sloganArg.setEa(productEntity.getFsEa());
        Result<ObjectSloganResult> slogan = objectSloganRelationService.getSlogan(sloganArg);
        queryProductDetailResult.setSlogan(slogan.getData().getSlogan());
        return new Result<>(SHErrorCode.SUCCESS, queryProductDetailResult);
    }

    private Map<String, Long> getDetailPhotoTotalSize(List<PhotoEntity> photoEntityList) {
        Map<String, Long> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(photoEntityList)) {
            return result;
        }
        for (PhotoEntity photoEntity : photoEntityList) {
            if (StringUtils.isNotBlank(photoEntity.getPath())
                    && photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                try {
                    String path = photoEntity.getPath().split("\\.")[0];
                    if (path != null && !path.startsWith("C_")) {
                        long size = fileV2Manager.getFileTotalSize(Collections.singletonList(path));
                        if (size > 0) {
                            result.put(photoEntity.getId(), size);
                        }
                    }
                } catch (Exception e) {
                    log.warn("查询详情图片大小出错, entity: [{}]", photoEntity, e);
                }
            }
        }
        return result;
    }

    @Override
    public Result<ProductListResult> listEnterpriseProducts(String ea, Integer fsUserId, ListProductArg vo) {
        Integer status = vo.getStatus();
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        //List<ProductEntityDTO> productEntities = productDAO.listByFsEa(ea, null, ProductArticleTypeEnum.CORPORATE.getType(), status, vo.getTitle(), page);
        if (StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }

        ProductListResult productListResult = new ProductListResult();
        productListResult.setTotalCount(0);
        productListResult.setTime(vo.getTime());

        // 获取菜单的设置详情
        Result<AppMenuTagVO> appMenuTagVOResult = appMenuTemplateService.getMenuTagRule(ea, vo.getMenuId(), ObjectTypeEnum.PRODUCT.getType());
        AppMenuTagVO appMenuTagVO = appMenuTagVOResult.isSuccess() ? appMenuTagVOResult.getData() : null;

        List<ProductEntityDTO> productEntities;
        ProductQueryParam queryParam = new ProductQueryParam();
        queryParam.setEa(ea);
        queryParam.setTitle(vo.getTitle());
        queryParam.setStatus(vo.getStatus());
        queryParam.setType(ProductArticleTypeEnum.CORPORATE.getType());
        queryParam.setUid(null);
        queryParam.setMaterialTagFilter(vo.getMaterialTagFilter());
        if (appMenuTagVO != null) {
            MaterialTagFilterArg materialTagFilterArg = queryParam.getMaterialTagFilter();
            if (materialTagFilterArg == null) {
                materialTagFilterArg = new MaterialTagFilterArg();
            }
            materialTagFilterArg.setMenuType(appMenuTagVO.getTagOperator());
            materialTagFilterArg.setMenuMaterialTagIds(appMenuTagVO.getTagIdList());
            queryParam.setStrictCheckGroup(true);
            queryParam.setMaterialTagFilter(materialTagFilterArg);
            productEntities = productDAO.getAccessiblePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            // 是否需要严格校验分组
            queryParam.setStrictCheckGroup(appMenuTemplateService.needStrictCheckGroup(ea, fsUserId, vo.getMenuId()));
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), vo.getMenuId());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            queryParam.setPermissionGroupIdList(permissionGroupIdList);
            productEntities = productDAO.getAccessiblePage(queryParam, page);
        } else if(StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            queryParam.setUserId(fsUserId);
            productEntities = productDAO.getCreateByMePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            productEntities = productDAO.getUnGroupPage(queryParam, page);
        } else {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), vo.getMenuId());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            queryParam.setStrictCheckGroup(true);
            if (!permissionGroupIdSet.contains(vo.getGroupId())) {
                productEntities = Lists.newArrayList();
            } else {
                //queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                queryParam.setUserId(fsUserId);
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.PRODUCT.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                productEntities = productDAO.getAccessiblePage(queryParam, page);
            }
        }

        int totalCount = page.getTotalNum();
        productListResult.setTotalCount(totalCount);
        productListResult.setTime(vo.getTime());
        if (CollectionUtils.isEmpty(productEntities)) {
            productListResult.setProductDetailResultList(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, productListResult);
        }

        List<String> productIds = Lists.newArrayList();
        productEntities.forEach(productEntity -> productIds.add(productEntity.getId()));

        // photo and product mapping
        Map<String, List<String>> headPicsMap = new HashMap<>();
        Map<String, List<String>> detailPicsMap = new HashMap<>();
        Map<String, List<String>> headPicsThumbMap = new HashMap<>();
        Map<String, List<String>> detailPicsThumbMap = new HashMap<>();
        Map<String, String> headPicThumbAPathMap = new HashMap<>();
        List<PhotoEntity> photoEntities = photoManager.listByProductIds(productIds,ea);
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoEntities.forEach(photoEntity -> {
                String productId = photoEntity.getTargetId();
                int targetType = photoEntity.getTargetType();
                String url = photoEntity.getUrl();
                String thumbUrl = photoEntity.getThumbnailUrl();
                if (targetType == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
                    headPicThumbAPathMap.putIfAbsent(productId, photoEntity.getThumbnailPath());
                    if (headPicsMap.containsKey(productId)) {
                        headPicsMap.get(productId).add(url);
                        headPicsThumbMap.get(productId).add(thumbUrl);
                    } else {
                        headPicsMap.put(productId, Lists.newArrayList(url));
                        headPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
                    }
                } else if (targetType == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                    if (detailPicsMap.containsKey(productId)) {
                        detailPicsMap.get(productId).add(url);
                        detailPicsThumbMap.get(productId).add(thumbUrl);
                    } else {
                        detailPicsMap.put(productId, Lists.newArrayList(url));
                        detailPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
                    }
                }
            });
        }

        //关联营销活动
        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>>  marketingActivityObjectInfoMap = null;
        if (vo.isConnectMarketingActivity()){
            Map<String, Integer> materielMap = Maps.newHashMap();
            for(ProductEntityDTO dto : productEntities){
                materielMap.put(dto.getId(), ObjectTypeEnum.ARTICLE.getType());
            }
            marketingActivityObjectInfoMap = marketingActivityManager.getActivityIdsByObject(materielMap, ea, fsUserId);
        }

        //物料平均访问深度
        List<String> objectIds = productEntities.stream().map(ProductEntityDTO::getId).collect(Collectors.toList());
        ActionDurationTimeObjectArg durationTimeObjectArg = new ActionDurationTimeObjectArg();
        durationTimeObjectArg.setEa(ea);
        durationTimeObjectArg.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        durationTimeObjectArg.setObjectIdList(objectIds);
        durationTimeObjectArg.setActionType(NewActionTypeEnum.LOOK_UP.getActionType());
        com.facishare.marketing.statistic.common.result.Result<List<ActionDurationTimeAvgByObjectResult>> listActionDurationTimeAvgListByObject = userMarketingStatisticService.listActionDurationTimeAvgListByObject(durationTimeObjectArg);
        Map<String, String> objectAvgTimeMap = Maps.newHashMap();
        Map<String, Integer> marketingObjectActivityLookUpDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(listActionDurationTimeAvgListByObject.getData())){
            objectAvgTimeMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId, o-> TimeMeasureUtil.getSecondTime(Long.valueOf(o.getAvg())),(v1, v2)->v2));
            marketingObjectActivityLookUpDTOMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId,ActionDurationTimeAvgByObjectResult::getCount,(v1, v2)->v2));
        }
        Map<String, String> finalObjectAvgTimeMap = objectAvgTimeMap;

        //物料获取的线索数
        List<CustomizeFormUserDataCountByObjectIdDTO> countByObjectIdDTOS = customizeFormDataUserDAO.queryObjectClueCount(objectIds, ObjectTypeEnum.PRODUCT.getType(), null);
        Map<String, Integer> countByObjectIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(countByObjectIdDTOS)){
            countByObjectIdMap = countByObjectIdDTOS.stream().collect(Collectors.toMap(CustomizeFormUserDataCountByObjectIdDTO::getObjectId,CustomizeFormUserDataCountByObjectIdDTO::getCount,(v1, v2)->v2));
        }
        Map<String, Integer> finalCountByObjectIdMap = countByObjectIdMap;
        Map<String, Integer> finalMarketingObjectActivityLookUpDTOMap = marketingObjectActivityLookUpDTOMap;

        //访问总数
        List<ObjectStatisticData> objectStatisticData = marketingObjectAmountStatisticDao.listStatisticData(ea, objectIds);
        Map<String, Integer> marketingObjectActivityAccessDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(objectStatisticData)) {
            marketingObjectActivityAccessDTOMap = objectStatisticData.stream().collect(Collectors.toMap(ObjectStatisticData::getObjectId, ObjectStatisticData :: getLookUpCount,(v1, v2)->v2));
        }
        Map<String, Integer> finalMarketingObjectActivityAccessDTOMap = marketingObjectActivityAccessDTOMap;

        //关联市场活动数
        Map<String, Integer> marketingEventCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectIds)) {
            List<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntityList = contentMarketingEventMaterialRelationDAO
                    .getContentMarketingByEaAndObjectTypeAndObjectIds(ea, ObjectTypeEnum.PRODUCT.getType(), objectIds);
            if (CollectionUtils.isNotEmpty(contentMarketingEventMaterialRelationEntityList)) {
                List<ContentMarketingEventMaterialRelationEntity> filterDataList = filterInvalidMarketingEventData(ea, contentMarketingEventMaterialRelationEntityList);
                if (CollectionUtils.isNotEmpty(filterDataList)) {
                    filterDataList.forEach(data -> {
                        marketingEventCountMap.merge(data.getObjectId(), 1, (prev, one) -> prev + one);
                    });
                }
            }
        }

        // build result
        List<QueryProductDetailResult> productDetailResults = Lists.newArrayList();
        final Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>> finalMarketingActivityObjectInfoMap = marketingActivityObjectInfoMap;
        List<String> imageUrls = Lists.newArrayList();
        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(objectIds, ObjectTypeEnum.PRODUCT.getType());
        productEntities.forEach(productEntity -> {
            String productId = productEntity.getId();
            QueryProductDetailResult queryProductDetailResult = BeanUtil.copy(productEntity, QueryProductDetailResult.class);
            if (headPicsMap.containsKey(productId)) {
                queryProductDetailResult.setHeadPics(headPicsMap.get(productId));
            }
            if (detailPicsMap.containsKey(productId)) {
                queryProductDetailResult.setDetailPics(detailPicsMap.get(productId));
            }
            if (headPicsThumbMap.containsKey(productId)) {
                queryProductDetailResult.setHeadPicsThumbs(headPicsThumbMap.get(productId));
            }
            if (detailPicsThumbMap.containsKey(productId)) {
                queryProductDetailResult.setDetailPicsThumbs(detailPicsThumbMap.get(productId));
            }
            queryProductDetailResult.setChoose(productEntity.isChoose());
            queryProductDetailResult.setTryOutEnable(productEntity.getTryOutEnable());
            queryProductDetailResult.setHeadPicThumbAPath(headPicThumbAPathMap.get(productId));
            queryProductDetailResult.setCreateTime(productEntity.getCreateTime() == null ? null : productEntity.getCreateTime().getTime());
            queryProductDetailResult.setTop(productEntity.isTop());
            if (vo.isConnectMarketingActivity() && finalMarketingActivityObjectInfoMap != null) {
                List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = finalMarketingActivityObjectInfoMap.get(productId);
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectInfoDTO)) {
                    queryProductDetailResult.setMarketingActivityId(objectInfoDTO.get(0).getId());
                    queryProductDetailResult.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                    queryProductDetailResult.setMarketingActivityCount(objectInfoDTO.size());
                }
            }
            //访问平均深度
            if (finalObjectAvgTimeMap == null || finalObjectAvgTimeMap.get(productEntity.getId()) == null){
                queryProductDetailResult.setActionDurationTimeAvg(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ARTICLESERVICEIMPL_703));
            }else {
                queryProductDetailResult.setActionDurationTimeAvg(finalObjectAvgTimeMap.get(productEntity.getId()));
            }
            //物料获取的线索数
            if (finalCountByObjectIdMap == null || finalCountByObjectIdMap.get(queryProductDetailResult.getId()) == null){
                queryProductDetailResult.setLeadCount(0);
            }else {
                queryProductDetailResult.setLeadCount(finalCountByObjectIdMap.get(queryProductDetailResult.getId()));
            }
            //访问总数
            if (finalMarketingObjectActivityAccessDTOMap == null || finalMarketingObjectActivityAccessDTOMap.get(queryProductDetailResult.getId()) == null){
                queryProductDetailResult.setAccessCount(0);
            }else {
                queryProductDetailResult.setAccessCount(finalMarketingObjectActivityAccessDTOMap.get(queryProductDetailResult.getId()));
            }

            //鸿曦说访问总数先改成以营销动态记录表为准
            //查看物料访问总数
            if (finalMarketingObjectActivityLookUpDTOMap == null || finalMarketingObjectActivityLookUpDTOMap.get(queryProductDetailResult.getId()) == null){
                queryProductDetailResult.setObjectLookUpCount(0);
            }else {
                queryProductDetailResult.setObjectLookUpCount(finalMarketingObjectActivityLookUpDTOMap.get(queryProductDetailResult.getId()));
            }
            //关联市场活动总数
            if (marketingEventCountMap == null || marketingEventCountMap.get(queryProductDetailResult.getId()) == null){
                queryProductDetailResult.setMarketingEventCount(0);
            }else {
                queryProductDetailResult.setMarketingEventCount(marketingEventCountMap.get(queryProductDetailResult.getId()));
            }

            if(queryProductDetailResult != null && StringUtils.isNotBlank(queryProductDetailResult.getId())){
                // 获取裁剪封面图
                PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType(), queryProductDetailResult.getId());
                if (coverCutMiniAppPhotoEntity != null) {
                    queryProductDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType(), queryProductDetailResult.getId());
                if (coverCutH5PhotoEntity != null) {
                    queryProductDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType(), queryProductDetailResult.getId());
                if (coverCutOrdinaryPhotoEntity != null) {
                    queryProductDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                    //返回原图
                    queryProductDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                }
            }
            if (StringUtils.isNotBlank(queryProductDetailResult.getHeadPicThumbAPath())) {
                imageUrls.add(queryProductDetailResult.getHeadPicThumbAPath());
            }
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(productEntity.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                queryProductDetailResult.setMaterialTags(collect);
            }
            productDetailResults.add(queryProductDetailResult);
        });
        //多线程处理图片
        Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
        productDetailResults.forEach(data -> {
            if (StringUtils.isNotBlank(data.getHeadPicThumbAPath()) && coverMap.containsKey(data.getHeadPicThumbAPath())) {
                data.setCoverSize(coverMap.get(data.getHeadPicThumbAPath()));
            }
        });
        productListResult.setProductDetailResultList(productDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, productListResult);
    }

    private List<ContentMarketingEventMaterialRelationEntity> filterInvalidMarketingEventData(String ea, List<ContentMarketingEventMaterialRelationEntity> list) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<String> marketingIds = list.stream().map(ContentMarketingEventMaterialRelationEntity::getMarketingEventId).collect(Collectors.toList());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        searchQuery.addFilter("_id", marketingIds, OperatorConstants.IN);
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> crmMarketingEventResult = crmV2Manager.getList(ea, -10000, MarketingEventFieldContants.API_NAME, searchQuery);
        if (crmMarketingEventResult == null || org.apache.commons.collections4.CollectionUtils.isEmpty(crmMarketingEventResult.getDataList())) {
            return Lists.newArrayList();
        }
        List<String> ids = crmMarketingEventResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
        return list.stream().filter(data -> ids.contains(data.getMarketingEventId())).collect(Collectors.toList());
    }

    @Override
    public Result updateStatus(String ea, Integer fsUserId, UpdateProductArg vo) {
        String id = vo.getId();
        Integer status = vo.getStatus();

        productDAOManager.updateStatus(ea, fsUserId, id, status);
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    @Transactional
    public Result moveCompanyProduct(String ea, Integer fsUserId, ProductMoveArg vo) {
        String productId = vo.getProductId();
        Integer type = vo.getType();

        ProductEntity productEntity = productDAO.queryProductDetail(productId);
        if (productEntity == null) {
            return new Result(SHErrorCode.PRODUCT_DETAIL_FAIL);
        }

        Integer order = productEntity.getOrderNum();
        ProductEntity changeProduct = null;
        if (type == MoveTypeEnum.UP_MOVE.getType()) {
            List<ObjectTopEntity> objectTopEntityList = objectTopManager.getByObjectIdList(Collections.singletonList(productId));
            if (CollectionUtils.isNotEmpty(objectTopEntityList)) {
                return Result.newError(SHErrorCode.CANT_MOVE_UP_TOP_PRODUCT);
            }
//            changeProduct = productDAO.findMaxByEaOrder(ea, order);
            changeProduct = productDAO.findMaxByEaOrderWithoutTop(ea, order);
        } else if (type == MoveTypeEnum.DOWN_MOVE.getType()) {
            List<ObjectTopEntity> objectTopEntityList = objectTopManager.getByObjectIdList(Collections.singletonList(productId));
            if (CollectionUtils.isNotEmpty(objectTopEntityList)) {
                return Result.newError(SHErrorCode.CANT_MOVE_DOWN_TOP_PRODUCT);
            }
//            changeProduct = productDAO.findMinByEaOrder(ea, order);
            changeProduct = productDAO.findMinByEaOrderWithoutTop(ea, order);
        } else if (type == MoveTypeEnum.TOP_MOVE.getType()) {
            // get minimum order
            ProductEntity minProduct = productDAO.findMinByEaOrder(ea, null);
            int minOrder = 1000;
            if (minProduct != null) {
                minOrder = minProduct.getOrderNum() - 1;
            }
            productDAO.updateOrderById(productId, minOrder);
            return new Result(SHErrorCode.SUCCESS);
        }

        if (changeProduct == null) {
            return new Result(SHErrorCode.SUCCESS);
        }

        Integer changeOrder = changeProduct.getOrderNum();
        productDAO.updateOrderById(productId, changeOrder);
        productDAO.updateOrderById(changeProduct.getId(), order);

        return new Result(SHErrorCode.SUCCESS);
    }

    /**
     * 删除产品图片
     */
    private void deleteProductPhotos(List<String> aDeletePicUrl, List<String> aDeletePicIds) {

        //公共图片集合获取
        //AddProductInitArg arg = JSON.parseObject(prdouctData, AddProductInitArg.class);
        AddProductInitArg arg = new AddProductInitArg();
        arg.buildProductDefaultArg();
        arg.setPath(GsonUtil.getGson().fromJson(prdouctDataApath, ArrayList.class));
        arg.setThumbsPath(GsonUtil.getGson().fromJson(prdouctDataThumbsPath, ArrayList.class));
        arg.setOldPath(GsonUtil.getGson().fromJson(prdouctDataOldPath, ArrayList.class));
        List<String> commonPicList = Lists.newArrayList();
        commonPicList.addAll(arg.getPath());
        commonPicList.addAll(arg.getOldPath());
        commonPicList.addAll(arg.getThumbsPath());

        if (CollectionUtils.isEmpty(aDeletePicUrl)) {
            return;
        }
        Set<String> deletePathList = Sets.newHashSet();

        log.info("deletePhotoId:{}", aDeletePicIds);
        if (CollectionUtils.isNotEmpty(aDeletePicIds)) {
            photoDAO.deletePhotos(new ArrayList<String>(aDeletePicIds));
        }

        for (String apath : aDeletePicUrl) {
            //包含公共图片的url过滤掉
            if (!commonPicList.contains(apath)) {
                deletePathList.add(apath);
               /* String[] strs = photoUrl.split("fileId=");
                if (strs != null && strs.length == 2) {
                    String fileId = strs[1];
                    String path = fileManager.getPathByShareId(fileId);
                    if (StringUtils.isNotEmpty(path)) {
                        deletePathList.add(path);
                    }
                }*/
            }
        }
         //删除数据库的地址
        log.info("deletePhotoId:{}", aDeletePicIds);
        if (CollectionUtils.isNotEmpty(aDeletePicIds)) {
            photoDAO.deletePhotos(new ArrayList<String>(aDeletePicIds));
        }
        log.info("deletePathList{}",deletePathList);

        //删除文件服务器上图片
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                if (CollectionUtils.isNotEmpty(deletePathList)) {
                    log.info("delete path:{}", deletePathList);
             //       fileManager.deleteFiles(Lists.newArrayList(deletePathList));
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    @Override
    public Result<EditObjectGroupResult> editProductGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("ProductServiceImpl.editProductGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("ProductServiceImpl.editProductGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.PRODUCT.getType());
    }

    @Override
    public Result<Void> deleteProductGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("productServiceImpl.deleteProductGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.PRODUCT.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setProductGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
        List<ProductEntity> productEntityList = productDAO.getByIds(arg.getObjectIdList());
        if (CollectionUtils.isEmpty(productEntityList)) {
            return Result.newError(SHErrorCode.PRODUCT_NOT_FOUND);
        }
        if (productEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_PRODUCT_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.PRODUCT.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.PRODUCT.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        for (ProductEntity productEntity : productEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), productEntity.getName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteEnterpriseProductBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        List<ProductEntity> productEntityList = productDAO.getByIdsIgnoreDeleted(ea, arg.getIdList());
        if (CollectionUtils.isEmpty(productEntityList)) {
            return Result.newError(SHErrorCode.PRODUCT_NOT_FOUND);
        }
        if (productEntityList.stream().anyMatch(ProductEntity::getTryOutEnable)) {
            return new Result<>(SHErrorCode.TRY_PRODUCT_CAN_NOT_DELETE);
        }
        if (productEntityList.stream().anyMatch(e -> ProductStatusEnum.NORMAL.getStatus() == e.getStatus())) {
            return new Result<>(SHErrorCode.CANT_DELETE_NORMAL_PRODUCT);
        }
        List<String> idList = productEntityList.stream().map(ProductEntity::getId).collect(Collectors.toList());
        productDAOManager.updateStatusBatch(ea, fsUserId, idList, ProductStatusEnum.DELETED.getStatus());
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.PRODUCT.getType(), arg.getIdList());
        customizeFormDataManager.unBindCustomizeFormDataObjectBatch(arg.getIdList(), ObjectTypeEnum.PRODUCT.getType(), ea);
        marketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(ObjectTypeEnum.PRODUCT.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.PRODUCT.getType(), arg.getIdList());
        for (ProductEntity productEntity : productEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), productEntity.getName(), OperateTypeEnum.DELETE);

        }
        ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.PRODUCT.getType(), arg.getIdList());
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topProduct(String ea, Integer fsUserId, TopMaterialArg arg) {
        ProductEntity entity = productDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.PRODUCT_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.PRODUCT.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopProduct(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.PRODUCT.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addProductGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.PRODUCT.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listProductGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), arg.getMenuId(), null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(productDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId, ProductArticleTypeEnum.CORPORATE.getType(), null));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(productDAO.queryAccessibleCount(ea, groupIdList, fsUserId, ProductArticleTypeEnum.CORPORATE.getType(), null));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(productDAO.queryCountCreateByMe(ea, fsUserId, ProductArticleTypeEnum.CORPORATE.getType()));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(productDAO.queryCountByUnGrouped(ea, ProductArticleTypeEnum.CORPORATE.getType()));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<ObjectGroupListResult> listProductGroup4Outer(String upstreamEA, String outTenantId, String outUserId, ListGroupArg arg) {
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup4Outer(upstreamEA, outTenantId, outUserId, ObjectTypeEnum.PRODUCT.getType());
        return Result.newSuccess(customizeGroupListVO);
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }
}