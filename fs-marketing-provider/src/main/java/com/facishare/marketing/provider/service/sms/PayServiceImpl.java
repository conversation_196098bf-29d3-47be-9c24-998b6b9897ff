package com.facishare.marketing.provider.service.sms;

import com.facishare.mankeep.api.outService.arg.sensors.AddSensorsDataArg;
import com.facishare.mankeep.api.outService.service.OutSensorsService;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.mankeep.common.enums.ObjectTypeEnum;
import com.facishare.mankeep.common.typehandlers.value.ActionVO;
import com.facishare.marketing.api.arg.sms.GoBuyArg;
import com.facishare.marketing.api.result.sms.GoBuyResult;
import com.facishare.marketing.api.result.sms.mw.SmsOrderDetailResult;
import com.facishare.marketing.api.service.sms.PayService;
import com.facishare.marketing.common.enums.sms.CrmOrderTypeEnum;
import com.facishare.marketing.common.enums.sms.SmsOrderStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.sms.OrderDAO;
import com.facishare.marketing.provider.dao.sms.QuotaDAO;
import com.facishare.marketing.provider.entity.sms.OrderEntity;
import com.facishare.marketing.provider.entity.sms.QuotaEntity;
import com.facishare.marketing.provider.manager.sms.PayManager;
import com.facishare.webhook.api.dto.AddCrmOrderDto;
import com.facishare.webhook.api.model.CrmOrderDetailInfo;
import com.facishare.webhook.api.model.CrmOrderProductInfo;
import com.facishare.webhook.api.service.VersionRegisterService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service("payService")
public class PayServiceImpl implements PayService {
    @Autowired
    private PayManager payManager;
    @Autowired
    private OrderDAO orderDAO;
    @Autowired
    private QuotaDAO quotaDAO;
    @Value("${sms.order1000.product.id}")
    private String smsOrder1000ProductId;
    @Value("${sms.order2500.product.id}")
    private String smsOrder2500ProductId;
    @Value("${sms.order5000.product.id}")
    private String smsOrder5000ProductId;
    @Value("${sms.order10000.product.id}")
    private String smsOrder10000ProductId;

    @Autowired
    private OutSensorsService sensorsService;
    @Autowired
    private VersionRegisterService versionRegisterService;

    @Override
    public Result<GoBuyResult> goBuy(GoBuyArg arg) {
        return payManager.goBuy(arg);
    }

    @Override
    public Integer findByOrderNo(String orderNo) {
        Integer status = null;
        OrderEntity orderEntity = orderDAO.findByOrderNo(orderNo);
        if (null != orderEntity) {
            status = orderEntity.getStatus();
        }
        return status;
    }

    @Override
    @Transactional
    public void callbackUpdate(int status, String orderNo, String payOrderNo) {
        log.info("recv purchase sms callbackUpdate status:{} orderNo:{} payOrderNo:{}", status, orderNo, payOrderNo);
        OrderEntity orderEntity = orderDAO.findByOrderNo(orderNo);
        if (null != orderEntity) {
            if (orderEntity.getStatus() == SmsOrderStatusEnum.PAYMENT.getStatus()){
                log.info("invalid sms callback orderNo:{}  status:{}", orderNo, status);
                return;
            }
            
            orderDAO.callbackUpdate(status, orderNo);
            QuotaEntity queryQuotaEntity = quotaDAO.queryEntityByEa(orderEntity.getEa());
            if (null == queryQuotaEntity) {
                QuotaEntity quotaEntity = new QuotaEntity();
                quotaEntity.setId(UUIDUtil.getUUID());
                quotaEntity.setEa(orderEntity.getEa());
                quotaEntity.setTotal(orderEntity.getPurchaseCount());
                quotaEntity.setLeft(orderEntity.getPurchaseCount());
                quotaDAO.insertQuota(quotaEntity);
            } else {
                log.info("before updateQuotaPurchaseCount ea:{} total:{} left:{} addCount:{}", queryQuotaEntity.getEa(), queryQuotaEntity.getTotal(), queryQuotaEntity.getLeft(), orderEntity.getPurchaseCount());
                quotaDAO.updateQuotaPurchaseCount(orderEntity.getEa(), orderEntity.getPurchaseCount());
                QuotaEntity queryQuotaEntity2 = quotaDAO.queryEntityByEa(orderEntity.getEa());
                log.info("after updateQuotaPurchaseCount ea:{} total:{} left:{} addCount:{}", queryQuotaEntity2.getEa(), queryQuotaEntity2.getTotal(), queryQuotaEntity2.getLeft(), orderEntity.getPurchaseCount());
            }
            try {
                AddSensorsDataArg arg = new AddSensorsDataArg();
                List<ActionVO> vos = Lists.newArrayList();
                for (int i = 0; i < orderEntity.getCopies(); i++) {
                    ActionVO actionVO = new ActionVO();
                    actionVO.setId(UUIDUtil.getUUID());
                    actionVO.setActionType(ActionTypeEnum.SMS_QUOTA_BUY.getAction());
                    actionVO.setObjectType(ObjectTypeEnum.SMS.getType());
                    actionVO.setObjectName(ObjectTypeEnum.SMS.name());
                    actionVO.setObjectId(orderEntity.getId());
                    actionVO.setFsEa(orderEntity.getEa());
                    actionVO.setUserId(orderEntity.getUserId());
                    actionVO.setActionTime(orderEntity.getCreateTime());
                    actionVO.setPayment((int)(orderEntity.getPaymentCount() / orderEntity.getCopies()));
                    actionVO.setAmount(orderEntity.getPurchaseCount() / orderEntity.getCopies());
                    vos.add(actionVO);
                }
                /**
                 * 由支付调用订单
                //同步到CRM订单
                String crmOrderId =  snycOnlineOrderToCRM(orderEntity.getEa(), payOrderNo, orderEntity.getPurchaseCount(), String.valueOf(orderEntity.getPaymentCount() / 100),
                            CrmOrderTypeEnum.COMMON_PURCHASE.getType(), orderNo, orderEntity.getCopies());


                if (crmOrderId != null){
                    orderDAO.updateSmsCrmOrderId(orderEntity.getId(), crmOrderId);
                }
                 **/

                if (!CollectionUtils.isEmpty(vos)) {
                    arg.setVos(vos);
                    sensorsService.addSensorsData(arg);
                }
            } catch (Exception e) {
                log.info("PayServiceImpl callbackUpdate sensorsService addSensorsData, exception: {}", e.fillInStackTrace());
            }
        }
    }

    @Override
    public Result<SmsOrderDetailResult> querySmsOrderById(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            log.warn("PayServiceImpl.querySmsOrderById error orderNo is null");
            Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        OrderEntity orderEntity = orderDAO.findByOrderNo(orderNo);
        if (orderEntity == null) {
            log.warn("PayServiceImpl.querySmsOrderById error orderEntity is null orderNo:{}", orderNo);
            Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(BeanUtil.copy(orderEntity, SmsOrderDetailResult.class));
    }
}
