package com.facishare.marketing.provider.manager;

import com.facishare.common.UidUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.EmployeeMsgResult;
import com.facishare.marketing.api.result.marketingplugin.MarketingPluginOpenResult;
import com.facishare.marketing.api.result.marketingplugin.MarketingPluginResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.plugin.MarketingNewPluginTypeEnum;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.enums.MarketingPluginTypeEnum.SUYUAN_COUPON;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/7 16:48
 */
@Component
@Slf4j
public class MarketingPluginConfigManager {

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private LicenseManager licenseManager;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    private String VERSION_APP_ID = "CRM";

    @ReloadableProperty("social.distribution.ea")
    private String socialDistributionEa;
    @ReloadableProperty("open.yxt.start.time")
    private Long yxtStartTime;

    private static final List<String> SCRM_MODE_CODES = Lists.newArrayList("scrm_wechat_marketing_private_industry","wechat_scrm_private_30_industry","wechat_scrm_private_100_industry","wechat_scrm_private_200_industry","wechat_scrm_private_500_industry","wechat_scrm_private_500plus_industry","scrm_wechat_marketing_service_private_industry");


    public int mergePluginConfig(String ea,Integer pluginType,String pluginName,Boolean status){
        MarketingPluginConfigEntity marketingPluginConfigEntity = new MarketingPluginConfigEntity();
        marketingPluginConfigEntity.setEa(ea);
        marketingPluginConfigEntity.setId(UidUtil.getUid());
        marketingPluginConfigEntity.setPluginType(pluginType);
        marketingPluginConfigEntity.setPluginName(pluginName);
        marketingPluginConfigEntity.setStatus(status);
        return marketingPluginConfigDAO.saveMarketingPluginConfig(marketingPluginConfigEntity);
    }

    @FilterLog
    public Boolean getCurrentPluginStatus(String ea,Integer pluginType){
        Boolean flag = false;
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, pluginType);
        if (marketingPluginConfigEntity != null && marketingPluginConfigEntity.getStatus()){
            flag = true;
        }
        return flag;
    }

    /**
     * 根据插件类型批量获取开启状态
     * @param ea
     * @param types
     * @return
     */
    public List<MarketingPluginConfigEntity> bulkGetByTypes(String ea, List<Integer> types){
        if (CollectionUtils.isEmpty(types)) {
            return Lists.newArrayList();
        }
        List<MarketingPluginConfigEntity> entities = marketingPluginConfigDAO.queryMarketingPluginByTypes(ea, types);
        return entities;
    }

    public void handlePluginConfig(String ea, EmployeeMsgResult result) {
        List<Integer> types = Arrays.stream(MarketingPluginTypeEnum.values()).map(MarketingPluginTypeEnum::getType).collect(Collectors.toList());
        List<MarketingPluginConfigEntity> pluginConfigEntities = marketingPluginConfigDAO.queryMarketingPluginByTypes(ea, types);
        Map<MarketingPluginTypeEnum, MarketingPluginConfigEntity> pluginConfigs = Maps.newHashMap();
        for (MarketingPluginConfigEntity entity : pluginConfigEntities) {
            pluginConfigs.put(MarketingPluginTypeEnum.fromType(entity.getPluginType()), entity);
        }

        for (MarketingPluginTypeEnum pluginType : MarketingPluginTypeEnum.values()) {
            MarketingPluginConfigEntity pluginConfig = pluginConfigs.get(pluginType);
            if (pluginConfig != null) {
                switch (pluginType) {
                    case SUYUAN_COUPON:
                        result.setSourceCouponEnabled(pluginConfig.getStatus());
                        break;
                    case SOCIETY_DISTRIBUTE:
                        result.setSocialDistributeEnabled(pluginConfig.getStatus());
                        break;
                    case PARTNER_MARKETING:
                        result.setPartnerMarketingEnabled(pluginConfig.getStatus());
                        break;
                    case CUSTOMER_SERVICE:
                        result.setShowCustomerServiceEnabled(pluginConfig.getStatus());
                        break;
                    case POLYV_LIVE:
                        result.setBindPolyvEnabled(pluginConfig.getStatus());
                        break;
                    case ZHIHU_CLUE:
                        result.setZhiHuClue(pluginConfig.getStatus());
                        break;
                    case SOGOU_CLUE:
                        result.setSoGouClue(pluginConfig.getStatus());
                        break;
                    case KUAISHOU_CLUE:
                        result.setKuaiShouClue(pluginConfig.getStatus());
                        break;
                    case SHENMA_CLUE:
                        result.setShenMaClue(pluginConfig.getStatus());
                        break;
                    case DIANJING_CLUE:
                        result.setDianJingClue(pluginConfig.getStatus());
                        break;
                    case TENCENT_AD:
                        result.setBindTencentAdEnable(pluginConfig.getStatus());
                        break;
                    case BAIDU_AD:
                        result.setBindBaiduAdEnable(pluginConfig.getStatus());
                        break;
                    case HEADLINES_AD:
                        result.setBindHeadlinesAdEnable(pluginConfig.getStatus());
                        break;
                    case GOOGLE_AD:
                        result.setBindGoogleAdEnable(pluginConfig.getStatus());
                        break;
                    case AD_OCPC:
                        result.setAdDataSendBackEnable(pluginConfig.getStatus());
                        break;
                    case KNOWLEDGE_MANAGEMENT_APP:
                        result.setKnowledgeManagement(pluginConfig.getStatus());
                        break;
                    case OUT_LINK_QR_CODE:
                        result.setOutQrCode(pluginConfig.getStatus());
                        break;
                    case MARKETING_ORDER_INTEGRATION:
                        result.setMarketingOrderIntegration(pluginConfig.getStatus());
                        break;
                    case WALLET:
                        result.setBindWallet(pluginConfig.getStatus());
                        break;
                    case MOBILE_PHOTO_LIBRARY:
                        result.setMobilePhotoLibrary(pluginConfig.getStatus());
                        break;
                    case MARKETING_CUSTOMER_INTEGRATION:
                        result.setMarketingCustomerIntegration(pluginConfig.getStatus());
                        break;
                    case DATA_PERMISSION:
                        result.setIsOpenDataPermission(pluginConfig.getStatus());
                        break;
                    case WECHAT_COUPON:
                        result.setWechatVoucher(pluginConfig.getStatus());
                        break;
                    case SERVICE_KNOWLEDGE:
                        result.setServiceKnowledgeManagement(pluginConfig.getStatus());
                        break;
                    case MARKETING_USER_PLUGIN:
                        result.setMarketingUserPlugin(pluginConfig.getStatus());
                        break;
                    case MARKETING_SDR:
                        result.setMarketingSDREnable(pluginConfig.getStatus());
                        break;
                    case WHATS_APP:
                        result.setWhatsAppPluginEnable(pluginConfig.getStatus());
                        break;
                    case DIGITAL_HUMANS:
                        result.setDigitalHumans(pluginConfig.getStatus());
                        break;
                    case MARKETING_DATA_ISOLATION:
                        result.setMarketingDataIsolation(pluginConfig.getStatus());
                        break;
                    case MARKETING_AI_PLUGIN:
                        result.setMarketingAIPluginEnable(pluginConfig.getStatus());
                        break;
                    case LINKEDIN:
                        result.setLinkedEnable(pluginConfig.getStatus());
                        break;
                    case MEMBER_LOYALTY_PLAN_PLUGIN:
                        result.setMemberLoyaltyPlanEnabled(pluginConfig.getStatus());
                }
            }
        }
    }

    public void handleCouponConfig(String ea,EmployeeMsgResult result){
        if (!result.getSourceCouponEnabled()) {
            Result<ControllerGetDescribeResult> describe = objectDescribeService.getDescribe(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.COUPON_OBJ.getName());
            if (describe.isSuccess() && describe.getData() != null && describe.getData().getDescribe() != null) {
                result.setSourceCouponEnabled(true);
                MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, SUYUAN_COUPON.getType());
                if (marketingPluginConfigEntity == null) {
                    marketingPluginConfigEntity = new MarketingPluginConfigEntity();
                    marketingPluginConfigEntity.setId(UidUtil.getUid());
                    marketingPluginConfigEntity.setPluginType(SUYUAN_COUPON.getType());
                    marketingPluginConfigEntity.setPluginName(SUYUAN_COUPON.getName());
                    marketingPluginConfigEntity.setEa(ea);
                    marketingPluginConfigEntity.setStatus(true);
                    marketingPluginConfigDAO.saveMarketingPluginConfig(marketingPluginConfigEntity);
                } else {
                    marketingPluginConfigDAO.updatePluginStatus(ea,SUYUAN_COUPON.getType(),true);
                }
            }
        }
    }

    public List<MarketingPluginResult.PluginConfig> handleNewVersionPlugin(String ea, String currentVersion) {
        List<MarketingPluginResult.PluginConfig> result = Lists.newArrayList();
        Set<String> marketingVersions = Sets.newHashSet(VersionEnum.PRO.getVersion(),VersionEnum.STREN.getVersion());
        List<Integer> types = Arrays.stream(MarketingNewPluginTypeEnum.values()).map(MarketingNewPluginTypeEnum::getType).collect(Collectors.toList());
        List<MarketingPluginConfigEntity> pluginConfigEntities = marketingPluginConfigDAO.queryMarketingPluginByTypes(ea, types);
        Map<Integer, MarketingPluginConfigEntity> pluginConfigs = Maps.newHashMap();
        for (MarketingPluginConfigEntity entity : pluginConfigEntities) {
            pluginConfigs.put(entity.getPluginType(), entity);
        }
        for (Integer pluginType : types) {
            MarketingPluginConfigEntity pluginEntity = pluginConfigs.get(pluginType);
            if (pluginEntity != null) {
                //1.如果能在pluginConfigEntities查询到对应插件的数据,则直接使用该状态
                MarketingPluginResult.PluginConfig pluginConfig = new MarketingPluginResult.PluginConfig();
                pluginConfig.setType(pluginType);
                pluginConfig.setStatus(pluginEntity.getStatus());
                result.add(pluginConfig);
            } else {
                MarketingPluginResult.PluginConfig pluginConfig = new MarketingPluginResult.PluginConfig();
                pluginConfig.setType(pluginType);
                pluginConfig.setStatus(false);
                //2.如果pluginConfigEntities里没查询到数据, 则根据版本号判断是否需要默认开启
                if (pluginType == MarketingNewPluginTypeEnum.STAFF_PROMOTION_PLUGIN.getType() || pluginType == MarketingNewPluginTypeEnum.OPERATION_PLAN_PLUGIN.getType()
                 || pluginType == MarketingNewPluginTypeEnum.EMAIL_MARKETING_PLUGIN.getType() || pluginType == MarketingNewPluginTypeEnum.SMS_MARKETING_PLUGIN.getType()
                 || pluginType == MarketingNewPluginTypeEnum.OFFICIAL_ACCOUNTS_PLUGIN.getType()
                ) {
                    //如果是全员营销或者运营计划,邮件营销,短信营销 直接默认开启
                    pluginConfig.setStatus(true);
                    result.add(pluginConfig);
                    continue;
                }
                if (pluginType == MarketingNewPluginTypeEnum.SOCIAL_DISTRIBUTION_PLUGIN.getType()) {
                    List<String> socialEas =  GsonUtil.getGson().fromJson(socialDistributionEa, ArrayList.class);
                    if (CollectionUtils.isNotEmpty(socialEas) && socialEas.contains(ea)) {
                        pluginConfig.setStatus(true);
                    }
                    result.add(pluginConfig);
                    continue;
                }
                if (marketingVersions.contains(currentVersion)) {
                    // 2.1旗舰版，专业版默认直接开启
                    pluginConfig.setStatus(true);
                    result.add(pluginConfig);
                    continue;
                }
                if (Objects.equals(currentVersion, VersionEnum.STAN.getVersion())) {
                    //2.2若是标准版，且下单日期12.1前的默认开启
                    List<ProductVersionPojo> productList = licenseManager.listProductVersion(ea, VERSION_APP_ID);
                    if (CollectionUtils.isNotEmpty(productList)) {
                        for (ProductVersionPojo productVersionPojo : productList) {
                            String version = productVersionPojo.getCurrentVersion().trim();
                            if (Objects.equals(version, VersionEnum.STAN.getVersion())) {
                                Long startTime = productVersionPojo.getStartTime();
                                //订单开始时间小于2024-12-01,则认为是以前的订单 1732982400000L
                                //测试时间 2024-10-10 1728489600000L
                                if (startTime != null && startTime < yxtStartTime) {
                                    pluginConfig.setStatus(true);
                                }
                                break;
                            }
                        }
                    }
                    result.add(pluginConfig);
                }
            }
        }
        return result;
    }

    public List<MarketingPluginOpenResult.PluginOpenConfig> handleNewVersionPluginEnableOpen(String ea,String currentVersion,List<Integer> types) {
        List<MarketingPluginOpenResult.PluginOpenConfig> result = Lists.newArrayList();
        Set<String> marketingVersions = Sets.newHashSet(VersionEnum.PRO.getVersion(),VersionEnum.STREN.getVersion());
        List<MarketingNewPluginTypeEnum> marketingNewPluginTypeEnums = MarketingNewPluginTypeEnum.fromType(types);
        for (MarketingNewPluginTypeEnum typeEnum : marketingNewPluginTypeEnums) {
            MarketingPluginOpenResult.PluginOpenConfig pluginOpenConfig = new MarketingPluginOpenResult.PluginOpenConfig();
            pluginOpenConfig.setType(typeEnum.getType());
            switch (typeEnum) {
                case CONFERENCE_PLUGIN:
                    pluginOpenConfig.setIsOpen(marketingVersions.contains(currentVersion) || appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.MARKETING_MEETING_PLUGIN_APP));
                    break;
                case LIVE_PLUGIN:
                    pluginOpenConfig.setIsOpen(marketingVersions.contains(currentVersion) || appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.MARKETING_LIVE_PLUGIN_APP));
                    break;
                case AD_PLUGIN:
                case OFFICIAL_WEBSITE_PLUGIN:
                    pluginOpenConfig.setIsOpen(marketingVersions.contains(currentVersion) || appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.MARKETING_AD_PLUGIN_APP) || appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.OVERSEAS_PLUGIN));
                    break;
                case MP_SUITE_PLUGIN:
                    pluginOpenConfig.setIsOpen(marketingVersions.contains(currentVersion) || appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.MARKETING_WXMINI_PLUGIN_APP));
                    break;
                case STAFF_PROMOTION_PLUGIN:
                case SMS_MARKETING_PLUGIN:
                case OFFICIAL_ACCOUNTS_PLUGIN:
                case EMAIL_MARKETING_PLUGIN:
                case OPERATION_PLAN_PLUGIN:
                    pluginOpenConfig.setIsOpen(true);
                    break;
                case WECHAT_MARKETING_PLUGIN:
                    pluginOpenConfig.setIsOpen(marketingVersions.contains(currentVersion) || appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.MARKETING_WECOM_PLUGIN_APP));
                    break;
                case SOCIAL_DISTRIBUTION_PLUGIN:
                    pluginOpenConfig.setIsOpen(false);
                    break;
            }
            result.add(pluginOpenConfig);
        }
        return result;
    }

    public long getVersionEndTime(String ea,String currentVersion) {
        long endTime = System.currentTimeMillis() + 24L * 60 * 60 * 1000 * 365 * 100;
        List<ProductVersionPojo> productList = licenseManager.listProductVersion(ea, VERSION_APP_ID);
        if (CollectionUtils.isNotEmpty(productList)) {
            for (ProductVersionPojo productVersionPojo : productList) {
                String version = productVersionPojo.getCurrentVersion().trim();
                if (Objects.equals(version, currentVersion)) {
                    endTime = productVersionPojo.getExpiredTime();
                    break;
                }
            }
        }
        return endTime;
    }

    public void handleStanVersionEnterpriseConfig(String enterpriseAccount) {
        //查询所有开通营销通的所有企业
        List<String> allEas;
        if (StringUtils.isEmpty(enterpriseAccount)) {
            allEas = enterpriseMetaConfigDao.findEaAll();
        } else {
            allEas = Lists.newArrayList(enterpriseAccount);
        }
        if (CollectionUtils.isEmpty(allEas)) {
            return ;
        }
        log.info("handleStanVersionEnterpriseConfig allEas size:{}",allEas.size());
        //查询出没有停用,并且版本号是标准版的企业
        List<String> eas = allEas.stream().filter(ea -> Objects.equals(VersionEnum.STAN.getVersion(),appVersionManager.getCurrentAppVersion(ea))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eas)) {
            return ;
        }
        log.info("handleStanVersionEnterpriseConfig stan eas size:{}",eas.size());
        //再筛选出满足条件的企业 标准版下单开始时间是 12-01之前的
        //Tips: 标准版可能不是直接在订单里的,可能是下单的scrm订单,然后包含了营销通标准版套件
        List<String> eaList = eas.stream().filter(ea -> {
            List<ProductVersionPojo> productList = licenseManager.listProductVersion(ea, VERSION_APP_ID);
            //首先看productList是否有营销通标准版订单
            boolean isMarketingStanOrder = productList.stream().anyMatch(productVersionPojo -> VersionEnum.STAN.getVersion().equals(productVersionPojo.getCurrentVersion()));
            if (isMarketingStanOrder) {
                return false;
            }
            //查询Model中是否包含的营销通标准版套件
            List<ModuleInfoPojo> moduleList = licenseManager.listModule(ea, VERSION_APP_ID);
            //判断moduleList 中是否包含有标准版
            boolean isMarketingStanModule = moduleList.stream().anyMatch(moduleInfoPojo -> VersionEnum.STAN.getVersion().equals(moduleInfoPojo.getModuleCode().trim()));
            if (isMarketingStanModule) {
                //筛选出 productList 包含 SCRM_MODE_CODES 的数据
                List<ProductVersionPojo> productVersionPojos = productList.stream().filter(productVersionPojo -> SCRM_MODE_CODES.contains(productVersionPojo.getCurrentVersion())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(productVersionPojos)) {
                    return false;
                }
                return productVersionPojos.stream().anyMatch(productVersionPojo -> productVersionPojo.getStartTime() < yxtStartTime);
            }
            return false;
        }).collect(Collectors.toList());
        log.info("handleStanVersionEnterpriseConfig stan eaList :{}",eaList);
        //插入相关的插件配置  pluginType 为 201,202,203,204,205,209
        List<Integer> types = Arrays.asList(MarketingNewPluginTypeEnum.CONFERENCE_PLUGIN.getType(),MarketingNewPluginTypeEnum.LIVE_PLUGIN.getType(),MarketingNewPluginTypeEnum.AD_PLUGIN.getType(),MarketingNewPluginTypeEnum.OFFICIAL_WEBSITE_PLUGIN.getType(),MarketingNewPluginTypeEnum.MP_SUITE_PLUGIN.getType(),MarketingNewPluginTypeEnum.WECHAT_MARKETING_PLUGIN.getType());
        for (String ea : eaList) {
            //插入每一条数据时,先查询已有的数据
            List<MarketingPluginConfigEntity> configEntities = marketingPluginConfigDAO.queryMarketingPluginByTypes(ea,types);
            List<Integer> insertTypes;
            if (CollectionUtils.isEmpty(configEntities)) {
                insertTypes = types;
            } else {
                //获取需要插入的types
                insertTypes = types.stream().filter(type -> configEntities.stream().noneMatch(configEntity -> configEntity.getPluginType().equals(type))).collect(Collectors.toList());
            }
            for (Integer type : insertTypes) {
                MarketingPluginConfigEntity entity = new MarketingPluginConfigEntity();
                entity.setId(UidUtil.getUid());
                entity.setEa(ea);
                entity.setPluginType(type);
                entity.setPluginName(MarketingNewPluginTypeEnum.ofType(type).isPresent() ? MarketingNewPluginTypeEnum.ofType(type).get().getName() : null);
                entity.setStatus(true);
                marketingPluginConfigDAO.saveMarketingPluginConfig(entity);
            }
        }

    }
}
