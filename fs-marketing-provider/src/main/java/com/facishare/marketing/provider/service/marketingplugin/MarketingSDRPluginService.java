package com.facishare.marketing.provider.service.marketingplugin;

import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.manager.MarketingOrderManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service(value = "marketingSDRPluginService")
public class MarketingSDRPluginService extends MarketingPluginBaseService {

    @Autowired
    private AppVersionManager appVersionManager;

    @Override
    public Result checkEnable(String ea) {
        //判断是否购买了在线客服
        if (!(appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.CUSTOMER_SERVICE_PRO_APP)||appVersionManager.checkSpecialVersionOrders(ea,VersionEnum.CUSTOMER_SERVICE_APP))) {
            return Result.newError(SHErrorCode.NOT_BUG_CUSTOMER_SERVICE);
        }
        return Result.newSuccess();
    }
}
