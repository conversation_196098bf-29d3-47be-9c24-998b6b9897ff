package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

public interface UserMarketingWxWorkExternalUserRelationDao {
    @Select("<script>" +
            "SELECT * FROM user_marketing_wx_work_external_user_relation WHERE ea=#{ea} AND wx_work_external_user_id IN " +
            "<foreach open='(' close=')' separator=',' collection='wxWorkExternalUserIds' item='ite'>#{ite}</foreach>" +
            "</script>")
    List<UserMarketingWxWorkExternalUserRelationEntity> listByEaAndWxWorkExternalUserIds(@Param("ea") String ea, @Param("wxWorkExternalUserIds") Collection<String> wxWorkExternalUserIds);

    @FilterLog
    @Select("SELECT * FROM user_marketing_wx_work_external_user_relation WHERE ea=#{ea} AND wx_work_external_user_id=#{wxWorkExternalUserId}")
    UserMarketingWxWorkExternalUserRelationEntity getByEaAndWxWorkExternalUserId(@Param("ea") String ea, @Param("wxWorkExternalUserId") String wxWorkExternalUserId);

    @Insert("INSERT INTO user_marketing_wx_work_external_user_relation(id, ea, user_marketing_id, wx_work_external_user_id, create_time, update_time ) VALUES (#{id}, #{ea}, #{userMarketingId}, #{wxWorkExternalUserId}, now(), now() ) ON CONFLICT DO NOTHING;")
    int insertIgnore(@Param("id") String id, @Param("ea") String ea, @Param("userMarketingId") String userMarketingId, @Param("wxWorkExternalUserId") String wxWorkExternalUserId);

    @Update("UPDATE user_marketing_wx_work_external_user_relation SET user_marketing_id = #{newUserMarketingId}, update_time = now() WHERE user_marketing_id = #{oldUserMarketingId} AND ea = #{ea}")
    int updateOldUserMarketingIdToNewUserMarketingId(@Param("ea") String ea, @Param("oldUserMarketingId") String oldUserMarketingId, @Param("newUserMarketingId") String newUserMarketingId);

    @Delete("DELETE FROM user_marketing_wx_work_external_user_relation WHERE ea=#{ea} AND wx_work_external_user_id=#{wxWorkExternalUserId}")
    int deleteByEaAndWxWorkExternalUserId(@Param("ea") String ea, @Param("wxWorkExternalUserId") String wxWorkExternalUserId);

    @Select("<script>select * from user_marketing_wx_work_external_user_relation where ea = #{ea} and user_marketing_id IN <foreach collection=\"userMarketingIds\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{userMarketingIds[${idx}]} </foreach> </script>")
    List<UserMarketingWxWorkExternalUserRelationEntity> listByUserMarketingIds(@Param("ea") String ea, @Param("userMarketingIds")List<String> userMarketingIds);

    @Update("UPDATE user_marketing_wx_work_external_user_relation SET wx_work_external_user_id = #{externalUserId}, update_time = now() WHERE id = #{id} AND ea = #{ea}")
    int updateWxWorkExternalUserId(@Param("ea") String ea, @Param("id") String id, @Param("externalUserId") String externalUserId);

    @Delete("<script>"
    + "delete FROM user_marketing_wx_work_external_user_relation WHERE  ea = #{ea} AND user_marketing_id =ANY(ARRAY "
    +   "<foreach collection='userMarketingIds' item='id' open='[' close=']' separator=','>#{id}</foreach> )"
    + "</script>")
    int deleteByUserMarketingIds(@Param("ea") String ea, @Param("userMarketingIds") List<String> userMarketingIds);

}
