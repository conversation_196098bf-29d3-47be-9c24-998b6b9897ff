package com.facishare.marketing.provider.service.distribution;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.outService.arg.card.QueryCardInfoListArg;
import com.facishare.marketing.api.arg.distribution.DistributionOverviewArg;
import com.facishare.marketing.api.arg.distribution.DistributorClueRankArg;
import com.facishare.marketing.api.arg.distribution.QueryDistributorRewardTopArg;
import com.facishare.marketing.api.result.distribution.DistributionOverviewResult;
import com.facishare.marketing.api.result.distribution.DistributorRankingData;
import com.facishare.marketing.api.result.distribution.QueryDistributorRankingTopResult;
import com.facishare.marketing.api.service.distribution.DistributionIndexService;
import com.facishare.marketing.common.enums.distribution.DistributorClueRankEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.OperatorDao;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.distribution.OperatorDistributorDAO;
import com.facishare.marketing.provider.dto.OperatorDistributorEntityCount;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.Operator;
import com.facishare.marketing.provider.entity.UserEntity;
import com.facishare.marketing.provider.entity.data.DistributorRewardTopData;
import com.facishare.marketing.provider.entity.distribution.DistributorEntity;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by ranluch on 2018/11/28.
 */
@Slf4j
@Service("distributionIndexService")
public class DistributionIndexServiceImpl implements DistributionIndexService {

    private final Integer TOPNUM = 5;

    @Autowired
    private ClueDAO clueDAO;
    @Autowired
    private UserManager userManager;

    @Autowired
    private DistributorDao distributorDao;

    @Autowired
    private OperatorDistributorDAO operatorDistributorDAO;

    @Autowired
    private OperatorDao operatorDao;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private PhotoManager photoManager;

//    @ReloadableProperty("distributionGray")
//    private String distributionGray;

    @Override
    public Result gray(String ea) {
        if (StringUtils.isBlank(ea)) {
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

//        if (StringUtils.isNotBlank(distributionGray)) {
//            List<String>grayList = GsonUtil.getGson().fromJson(distributionGray, ArrayList.class);
//            if (CollectionUtils.isNotEmpty(grayList)) {
//                if (grayList.contains(ea)) {
//                    return Result.newSuccess();
//                } else {
//                    return new Result(SHErrorCode.GRAY_DISTRIBUTION_COMPANY_REFUSED);
//                }
//            }
//        }

        return Result.newSuccess();
    }

    @Override
    public Result<DistributionOverviewResult> queryDistributionOverview(DistributionOverviewArg vo) {
        DistributionOverviewResult distributionOverviewResult = null;
        if (vo.isAppAdmin()) {
            distributionOverviewResult = clueDAO.queryDistributionOverview(vo.getPlanId());
        } else {
            distributionOverviewResult = clueDAO.queryDistributionOverviewByUid(vo.getPlanId(), vo.getFsUid());
        }

        if (distributionOverviewResult == null) {
            distributionOverviewResult = new DistributionOverviewResult();
            distributionOverviewResult.setTotalClueCnt(0);
            distributionOverviewResult.setRewardClueCnt(0);
            distributionOverviewResult.setValidClueCnt(0);
            distributionOverviewResult.setTotalReward(0d);
            distributionOverviewResult.setTotalGranted(0d);
            distributionOverviewResult.setTotalRewardLeave(0d);
        } else {
            distributionOverviewResult.setTotalReward(Math.round(distributionOverviewResult.getTotalReward() * 1000d) / 1000d);
            distributionOverviewResult.setTotalGranted(Math.round(distributionOverviewResult.getTotalGranted() * 1000d) / 1000d);
            distributionOverviewResult.setTotalRewardLeave(Math.round(distributionOverviewResult.getTotalRewardLeave() * 1000d) / 1000d);
        }
        if (vo.isAppAdmin()) {
            List<DistributorEntity> distributors = distributorDao.queryDistributorByPlanId(vo.getPlanId());
            if (CollectionUtils.isNotEmpty(distributors)) {
                distributionOverviewResult.setDistributorCnt(distributors.size());
            } else {
                distributionOverviewResult.setDistributorCnt(0);
            }
        } else {
            distributionOverviewResult.setDistributorCnt(0);
            Operator operator = operatorDao.getOperatorByFsUidAndPlanId(vo.getPlanId(), vo.getFsUid());
            if (operator != null) {
                List<OperatorDistributorEntityCount> distributorEntityCounts = operatorDistributorDAO.queryDistributorEntityCountByOperator(Lists.newArrayList(operator.getId()));
                if (CollectionUtils.isNotEmpty(distributorEntityCounts)) {
                    distributionOverviewResult.setDistributorCnt(distributorEntityCounts.get(0).getDistributorCount());
                }
            }
        }
        return Result.newSuccess(distributionOverviewResult);
    }

    @Override
    public Result<QueryDistributorRankingTopResult> queryDistributorClueRank(DistributorClueRankArg vo) {
        if (vo.getType() == null) {
            vo.setType(DistributorClueRankEnum.TOTAL_CLUE.getType());
        }
        QueryDistributorRankingTopResult queryDistributorRankingTopResult = new QueryDistributorRankingTopResult();
        List<DistributorRewardTopData> distributorClueRank = null;
        if (vo.isAppAdmin()) {
            distributorClueRank = clueDAO.queryDistributionClueByPlanId(vo.getPlanId(), vo.getType(), TOPNUM);
        } else {
            distributorClueRank = clueDAO.queryDistributionClueByPlanIdAndFsUserId(vo.getPlanId(), vo.getFsUid(), vo.getType(), TOPNUM);
        }
        buildRankingTopResult(queryDistributorRankingTopResult, distributorClueRank);
        return Result.newSuccess(queryDistributorRankingTopResult);
    }

    @Override
    public Result<QueryDistributorRankingTopResult> queryDistributorRewardTop(QueryDistributorRewardTopArg vo) {
        QueryDistributorRankingTopResult rewardTopResult = new QueryDistributorRankingTopResult();
        List<DistributorRewardTopData> dataList = null;
        if (vo.isAppAdmin()) {
            dataList = distributorDao.queryDistributorRewardTop(vo.getPlanId(), TOPNUM);
        } else {
            dataList = distributorDao.queryDistributorRewardTopByUid(vo.getPlanId(), vo.getFsUid(), TOPNUM);
        }
        buildRankingTopResult(rewardTopResult, dataList);
        return Result.newSuccess(rewardTopResult);
    }

    private void buildRankingTopResult(QueryDistributorRankingTopResult rewardTopResult, List<DistributorRewardTopData> dataList){
        List<DistributorRankingData> rankingDataList = Lists.newArrayList();
        rewardTopResult.setRankingDataList(rankingDataList);
        List<String> uids;
        List<String> noCardUserIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            uids = dataList.stream().map(DistributorRewardTopData :: getDistributorUid).collect(Collectors.toList());
            QueryCardInfoListArg arg = new QueryCardInfoListArg();
            arg.setUids(uids);
            List<CardEntity> cardEntities = cardDAO.listByUids(uids);
            photoManager.resetCardPhotoUrl(cardEntities);
            Map<String,CardEntity> cardInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(cardEntities)) {
                cardEntities.stream().forEach(cardInfo -> {
                    cardInfoMap.put(cardInfo.getUid(), cardInfo);
                });
            }
            List<String> cardUid = new ArrayList<>(cardInfoMap.keySet());
            uids.forEach(uid ->{
               if(!cardUid.contains(uid)){
                   noCardUserIds.add(uid);
               }
            });
            Map<String, UserEntity> uidUserEntityMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(noCardUserIds)) {
                List<UserEntity> userEntities = userManager.listByUids(noCardUserIds);
                if (CollectionUtils.isNotEmpty(userEntities)){
                    userEntities.forEach(userEntity -> uidUserEntityMap.put(userEntity.getUid(), userEntity));
                }
            }

            dataList.stream().filter(data -> data.getTotalCnt() > 0).forEach(data -> {
                DistributorRankingData rankingData = new DistributorRankingData();
                rankingData.setDistributorId(data.getDistributorId());
                double count = Math.round(data.getTotalCnt() * 1000d) / 1000d;
                rankingData.setCount(count);
                CardEntity cardInfo = cardInfoMap.get(data.getDistributorUid());
                if (cardInfo != null) {
                    rankingData.setName(cardInfo.getName());
                    rankingData.setAvatar(cardInfo.getAvatarThumbnail());
                }else {
                    //从user表中取
                    if (uidUserEntityMap.get(data.getDistributorUid()) != null){
                        rankingData.setName(uidUserEntityMap.get(data.getDistributorUid()).getName());
                        rankingData.setAvatar(uidUserEntityMap.get(data.getDistributorUid()).getAvatar());
                    }
                }
                rankingDataList.add(rankingData);
            });


        }
    }
}
