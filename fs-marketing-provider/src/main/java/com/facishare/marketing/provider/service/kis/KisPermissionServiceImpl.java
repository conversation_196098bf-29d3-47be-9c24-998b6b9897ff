package com.facishare.marketing.provider.service.kis;

import com.drew.metadata.exif.PanasonicRawIFD0Descriptor;
import com.facishare.marketing.api.result.kis.GetMiniAppInfoResult;
import com.facishare.marketing.api.result.kis.GetUserStatusResult;
import com.facishare.marketing.api.service.kis.KisPermissionService;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.qywx.MiniAppTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.manager.kis.KisPermissionManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/02/26
 **/
@Slf4j
@Service("kisPermissionService")
public class KisPermissionServiceImpl implements KisPermissionService {

    @Autowired
    private KisPermissionManager kisPermissionManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;

    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private ObjectManager objectManager;

    @Override
    public Result<GetUserStatusResult> getCurrentUserStatus(String ea, Integer fsUid, Integer ei) {
        GetUserStatusResult result = new GetUserStatusResult();
        result.setAppAdmin(kisPermissionManager.isAppAdmin(ea, fsUid));
        result.setLeader(kisPermissionManager.isLeader(fsUid, ei));
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetMiniAppInfoResult> getMiniAppInfo(String ea) {
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        String userName = WxAppInfoEnum.Mankeep.getUserName();
        String miniAppType = MiniAppTypeEnum.KEMAI.getDesc();
        if (StringUtils.isBlank(appId)) {
            log.warn("KisPermissionServiceImpl.getMiniAppInfo error appId is null ea:{}", ea);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (!WxAppInfoEnum.isMankeep(appId)) {
            // 若非客脉appId则重新获取userName
            userName = WxAppInfoEnum.getUserNameByAppId(appId);
            miniAppType = MiniAppTypeEnum.KEMAI_PRO.getDesc();
            if (StringUtils.isBlank(userName)) {
                // 托管小程序
                WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(appId);
                if (wechatAccountConfigEntity == null) {
                    log.warn("KisPermissionServiceImpl.getMiniAppInfo wechatAccountConfigEntity is null ea:{}, appId:{}", ea, appId);
                    return Result.newError(SHErrorCode.SYSTEM_ERROR);
                }
                miniAppType = MiniAppTypeEnum.CUSTOM.getDesc();
                userName = wechatAccountConfigEntity.getUserName();
            }
        }
       /* QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        String appId = WxAppInfoEnum.Mankeep.getAppId();
        String userName = WxAppInfoEnum.Mankeep.getUserName();
        if (qywxMiniappConfigEntity != null) {
            appId = qywxMiniappConfigEntity.getAppid();
            userName = WxAppInfoEnum.getUserNameByAppId(appId);
            if (StringUtils.isBlank(userName)) {
                // 托管小程序
                WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(appId);
                // 若原始id查询失败则返回客脉信息
                userName = wechatAccountConfigEntity != null ? wechatAccountConfigEntity.getUserName() : WxAppInfoEnum.Mankeep.getUserName();
                appId = wechatAccountConfigEntity != null ? wechatAccountConfigEntity.getWxAppId() : WxAppInfoEnum.Mankeep.getAppId();
            }
        }*/
        GetMiniAppInfoResult result = new GetMiniAppInfoResult(appId, userName, miniAppType);
        return Result.newSuccess(result);
    }

    @Override
    public Result updateMiniApp(String ea, String appId) {
        String bindAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (!StringUtils.equals(appId, bindAppId)){
            eaWechatAccountBindDao.updateAppIdByPlatform(ea, appId, "YXT");
        }

        return Result.newSuccess();
    }

    @Override
    public Result<GetMiniAppInfoResult> getMiniAppInfoNew(String objectId, Integer objectType) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        String userName = WxAppInfoEnum.Mankeep.getUserName();
        String miniAppType = MiniAppTypeEnum.KEMAI.getDesc();
        if (StringUtils.isBlank(appId)) {
            log.warn("KisPermissionServiceImpl.getMiniAppInfo error appId is null ea:{}", ea);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        if (!WxAppInfoEnum.isMankeep(appId)) {
            // 若非客脉appId则重新获取userName
            userName = WxAppInfoEnum.getUserNameByAppId(appId);
            miniAppType = MiniAppTypeEnum.KEMAI_PRO.getDesc();
            if (StringUtils.isBlank(userName)) {
                // 托管小程序
                WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(appId);
                if (wechatAccountConfigEntity == null) {
                    log.warn("KisPermissionServiceImpl.getMiniAppInfo wechatAccountConfigEntity is null ea:{}, appId:{}", ea, appId);
                    return Result.newError(SHErrorCode.SYSTEM_ERROR);
                }
                miniAppType = MiniAppTypeEnum.CUSTOM.getDesc();
                userName = wechatAccountConfigEntity.getUserName();
            }
        }
        GetMiniAppInfoResult result = new GetMiniAppInfoResult(appId, userName, miniAppType);
        return Result.newSuccess(result);
    }

    @Override
    public Result updateMiniAppNew(String objectId, Integer objectType, String appId) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        String bindAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (!StringUtils.equals(appId, bindAppId)){
            eaWechatAccountBindDao.updateAppIdByPlatform(ea, appId, "YXT");
        }

        return Result.newSuccess();
    }
}
