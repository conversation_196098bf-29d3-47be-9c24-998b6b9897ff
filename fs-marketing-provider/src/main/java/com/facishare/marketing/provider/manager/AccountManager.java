package com.facishare.marketing.provider.manager;


import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.EnterpriseDefaultCard.AddDefaultCardInfoFromQywxArg;
import com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService;
import com.facishare.mankeep.api.service.KmVideoService;
import com.facishare.mankeep.api.vo.video.VideoDeleteVO;
import com.facishare.mankeep.common.enums.ObjectTypeEnum;
import com.facishare.mankeep.common.enums.QRCodeTypeEnum;
import com.facishare.mankeep.common.enums.VideoStatusEnum;
import com.facishare.mankeep.common.enums.VideoTargetTypeEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.util.GsonUtil;
import com.facishare.mankeep.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.api.result.card.GetEnterpriseDefaultCardResult;
import com.facishare.marketing.api.service.card.EnterpriseDefaultCardService;
import com.facishare.marketing.api.vo.card.GetEnterpriseDefaultCardVo;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: Smallfan
 * @Date: created in 2020-01-10 10:41
 * @Description:
 */
@Service
@Slf4j
public class AccountManager {

    @Autowired
    private AccountDAO accountDAO;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private CoverImageManager coverImageManager;
    @Autowired
    private QRCodeManager qrCodeManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private OutEnterpriseDefaultCardService outEnterpriseDefaultCardService;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private CardManager cardManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private VideoDAO videoDAO;
    @Autowired
    private KmVideoService kmVideoService;
    @Autowired
    private FsAddressBookSettingDAO fsAddressBookSettingDAO;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private DingAuthService dingAuthService;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private EnterpriseDefaultCardService enterpriseDefaultCardService;

    public boolean needUpdateUserInfo(String uid) {
        AccountEntity accountEntity = accountDAO.queryAccountByUid(uid);
        if (null == accountEntity || StringUtils.isBlank(accountEntity.getPhone())) {
            return true;
        }

        CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
        if (null == cardEntity) {
            return true;
        }

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), cardEntity.getUid());
        if (CollectionUtils.isEmpty(photoEntityList)) {
            return true;
        }

        return false;
    }

    public boolean createAccount(String uid, String name, Integer gender, String avatar, String mobile, String ea, String appId, Integer accountType) {
        AccountEntity queryAccountEntity = accountDAO.queryAccountByUid(uid);
        AccountEntity accountEntity = new AccountEntity();
        accountEntity.setUid(uid);
        accountEntity.setPhone(mobile);
        accountEntity.setAccountType(accountType);
        accountEntity.setAppId(appId);

        if (StringUtils.isNotBlank(name)) {
            UserEntity userEntity = new UserEntity();
            userEntity.setUid(uid);
            userEntity.setName(name);
            userManager.updateUser(userEntity);
        }

        if (null == queryAccountEntity) {
            int addAccountResult = accountDAO.insert(accountEntity);
            if (addAccountResult != 1) {
                log.error("AccountManager.createAccount add account failed, accountEntity={}", accountEntity);
                return false;
            }
        } else {
            if (StringUtils.isNotBlank(mobile)) {
                accountDAO.update(accountEntity);
            }
        }

        CardEntity queryCardEntity = cardDAO.queryCardInfoByUid(uid);
        if (null == queryCardEntity) {
            CardEntity cardEntity = new CardEntity();
            cardEntity.setId(UUIDUtil.getUUID());
            cardEntity.setUid(uid);
            cardEntity.setName(name);
            cardEntity.setGender(gender);
            cardEntity.setAvatar(avatar);
            cardEntity.setAvatarThumbnail(avatar);
            cardEntity.setPhone(mobile);
            if (AccountTypeEnum.DING_MINI_APP.getType().equals(accountType)) {
                Result<DingCorpMappingVo> dingCorpMappingVoResult = dingAuthService.queryEnterpriseByEA(ea);
                if (dingCorpMappingVoResult.isSuccess() && dingCorpMappingVoResult.getData() != null) {
                    cardEntity.setCompanyName(dingCorpMappingVoResult.getData().getEnterpriseName());
                }
            }

            // 根据绑定信息填写卡片信息
            buildCardFsBindInfoBuUid(cardEntity, uid);
            int addCardResult = cardDAO.insert(cardEntity);
            if (addCardResult != 1) {
                log.error("AccountManager.createAccount add card failed, cardEntity={}", cardEntity);
                return false;
            }

            // 同步公司名片信息
            ThreadPoolUtils.execute(() -> {
                try {
                    AddDefaultCardInfoFromQywxArg arg = new AddDefaultCardInfoFromQywxArg();
                    arg.setEa(ea);
                    arg.setUid(uid);
                    outEnterpriseDefaultCardService.addDefaultCardInfoFromQywx(arg);
                } catch (Exception e) {
                    log.warn("AccountManager.createAccount error e:{}", e);
                }
            });

            queryCardEntity = cardEntity;
        } else {
            // 更新名片信息
            CardEntity cardEntity = new CardEntity();
            cardEntity.setId(queryCardEntity.getId());
            cardEntity.setAvatar(avatar);
            cardEntity.setName(name);
            cardEntity.setAvatarThumbnail(avatar);
            cardEntity.setPhone(mobile);
            cardEntity.setLastModifyTime(new Date());
            cardDAO.updateById(cardEntity);
        }

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), queryCardEntity.getUid());
        ThreadPoolUtils.execute(() -> {
            if (CollectionUtils.isEmpty(photoEntityList)) {
                createCardQRCode(uid, ea);
                coverImageManager.createCardShareCoverAsync(ea,uid);
            }
        });

        return true;
    }

    public boolean createWxAccount(String uid, String name, String avatar, String mobile, String ea, Integer fsuserId, String appId) {
        // 创建account
        try {
            AccountEntity queryAccountEntity = accountDAO.queryAccountByUid(uid);
            AccountEntity accountEntity = new AccountEntity();
            accountEntity.setUid(uid);
            accountEntity.setPhone(mobile);
            accountEntity.setAccountType(AccountTypeEnum.QYWX_MINI_APP.getType());
            accountEntity.setAppId(appId);
            if (queryAccountEntity == null) {
                int addAccountResult = accountDAO.insert(accountEntity);
                if (addAccountResult != 1) {
                    log.error("AccountManager.createWxAccount add account failed, accountEntity={}", accountEntity);
                    return false;
                }
            } else {
                if (StringUtils.isNotBlank(mobile)) {
                    accountDAO.update(accountEntity);
                }
            }

            // 绑定纷享与微信身份
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            if (fsBindEntity == null) {
                fsBindEntity = new FSBindEntity();
                fsBindEntity.setUid(uid);
                fsBindEntity.setFsEa(ea);
                fsBindEntity.setFsUserId(fsuserId);
                fsBindEntity.setPhone(mobile);
                fsBindEntity.setType(AccountTypeEnum.QYWX_MINI_APP.getType());
                fsBindEntity.setFsCorpId(ei);
                fsBindEntity.setAppId(appId);
                fsBindManager.insert(fsBindEntity);
            } else {
                if (!fsBindEntity.getFsEa().equals(ea) || !fsBindEntity.getFsUserId().equals(fsuserId)) {
                    // 若绑定且换绑
                    fsBindEntity.setUid(uid);
                    fsBindEntity.setFsEa(ea);
                    fsBindEntity.setFsCorpId(ei);
                    fsBindEntity.setFsUserId(fsuserId);
                    fsBindManager.update(fsBindEntity);
                }
            }

            if (StringUtils.isNotBlank(name)) {
                UserEntity userEntity = new UserEntity();
                userEntity.setName(name);
                userEntity.setUid(uid);
                userManager.updateUser(userEntity);
            }

            CardEntity queryCardEntity = cardDAO.queryCardInfoByUid(uid);
            if (queryCardEntity == null) {
                // fs企业开通时需要同步客脉名片数据
                FsAddressBookSettingEntity fsAddressBookSettingEntity = fsAddressBookSettingDAO.getFsAddressBookSettingByEa(ea);
                if (fsAddressBookSettingEntity != null && fsAddressBookSettingEntity.getNeedCoverCardInfo()) {
                    handleKeMaiInitCardInfo(ea, fsuserId, mobile, uid, avatar, name);
                } else {
                    CardEntity cardEntity = new CardEntity();
                    cardEntity.setId(UUIDUtil.getUUID());
                    cardEntity.setUid(uid);
                    cardEntity.setAvatar(avatar);
                    cardEntity.setName(name);
                    cardEntity.setAvatarThumbnail(avatar);
                    cardEntity.setPhone(mobile);
                    buildCardFsBindInfoBuUid(cardEntity, uid);
                    cardDAO.insert(cardEntity);
                }
            } else {
                // 更新名片信息
                CardEntity cardEntity = new CardEntity();
                cardEntity.setId(queryCardEntity.getId());
                cardEntity.setAvatar(avatar);
                cardEntity.setName(name);
                cardEntity.setAvatarThumbnail(avatar);
                cardEntity.setPhone(mobile);
                cardEntity.setLastModifyTime(new Date());
                cardDAO.updateById(cardEntity);

                // 删除名片视频
                List<VideoEntity> videos = videoDAO.queryVideoByTargetType(uid, ObjectTypeEnum.CARD.getType(), VideoStatusEnum.SUCCESS.getStatus());
                if (CollectionUtils.isNotEmpty(videos)) {
                    for (VideoEntity video : videos) {
                        VideoDeleteVO vo = new VideoDeleteVO();
                        vo.setKmToken(video.getToken());
                        vo.setVideoType(video.getVideoType());
                        kmVideoService.deleteVideo(vo);
                    }
                }
            }

            List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), uid);
            ThreadPoolUtils.execute(() -> {
                if (CollectionUtils.isEmpty(photoEntityList)) {
                    createCardQRCode(uid, ea);
                }
            });
            List<PhotoEntity> miniCoverPhotoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.MINI_COVER_CARD_SHARE.getType(), uid);
            ThreadPoolUtils.execute(() -> {
                if (CollectionUtils.isEmpty(miniCoverPhotoEntityList)) {
                    coverImageManager.createCardShareCoverAsync(ea, uid);
                }
            });
        } catch (Exception e) {
            log.error("AccountManager.createWxAccount error ea: {} uid:{} mobile: {}", ea, uid, mobile);
            return false;
        }
        return true;
    }

    private void handleKeMaiInitCardInfo(String ea, Integer fsUserId, String phone, String uid, String avatar, String name) {
        // 查询绑定表
        String oldUid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, fsUserId, AccountTypeEnum.MINI_APP.getType(), null);
        if (StringUtils.isBlank(oldUid)) {
            List<AccountEntity> accountEntityList = accountDAO.queryAccountByPhone(phone, AccountTypeEnum.MINI_APP.getType(), null);
            if (CollectionUtils.isNotEmpty(accountEntityList)) {
                oldUid = accountEntityList.get(0).getUid();
            }
        }
        if (StringUtils.isBlank(oldUid)) {
            // 未找到绑定数据则创建新数据
            CardEntity cardEntity = new CardEntity();
            cardEntity.setId(UUIDUtil.getUUID());
            cardEntity.setUid(uid);
            cardEntity.setAvatar(avatar);
            cardEntity.setName(name);
            cardEntity.setAvatarThumbnail(avatar);
            cardEntity.setPhone(phone);
            cardDAO.insert(cardEntity);
            return;
        }
        // 查询之前是否有名片信息
        CardEntity cardEntity = cardDAO.queryCardInfoByUid(oldUid);
        if (cardEntity == null) {
            cardEntity = new CardEntity();
            cardEntity.setId(UUIDUtil.getUUID());
            cardEntity.setUid(uid);
            cardEntity.setAvatar(avatar);
            cardEntity.setName(name);
            cardEntity.setAvatarThumbnail(avatar);
            cardEntity.setPhone(phone);
            cardDAO.insert(cardEntity);
            return;
        }
        // 拷贝名片信息
        String newCardId = UUIDUtil.getUUID();
        CardEntity saveCardData = BeanUtil.copy(cardEntity, CardEntity.class);
        saveCardData.setId(newCardId);
        saveCardData.setUid(uid);
        saveCardData.setQrUrl(null);
        saveCardData.setShareUrl(null);
        saveCardData.setExchangeUrl(null);
        saveCardData.setSharePath(null);
        saveCardData.setExchangePath(null);
        saveCardData.setVisualRangeString(null);

        cardDAO.insert(saveCardData);

        // 名片图片
        List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndTargetType(cardEntity.getId(), PhotoTargetTypeEnum.CARD_DETAIL.getType());
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            for (PhotoEntity photoEntity : photoEntityList) {
                PhotoEntity photo = BeanUtil.copy(photoEntity, PhotoEntity.class);
                photo.setId(UUIDUtil.getUUID());
                photo.setTargetId(newCardId);
                photoDAO.addPhoto(photo);
            }
        }

        // 视频
        List<VideoEntity> videos = videoDAO.queryVideoByTargetType(uid, ObjectTypeEnum.CARD.getType(), VideoStatusEnum.SUCCESS.getStatus());
        if (CollectionUtils.isNotEmpty(videos)) {
            for (VideoEntity videoEntity : videos) {
                VideoEntity saveVideoData = BeanUtil.copy(videoEntity, VideoEntity.class);
                saveVideoData.setId(UUIDUtil.getUUID());
                saveVideoData.setTargetType(VideoTargetTypeEnum.CARD_DETAIL.getType());
                saveVideoData.setStatus(VideoStatusEnum.SUCCESS.getStatus());
                saveVideoData.setToken(UUIDUtil.getUUID());
                saveVideoData.setUploadUid(uid);
                videoDAO.addVideo(saveVideoData);
            }
        }

    }

    private void buildCardFsBindInfoBuUid(CardEntity cardEntity, String uid) {
        if (cardEntity == null || StringUtils.isBlank(uid)) {
            return;
        }
        try {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            if (fsBindEntity == null) {
                return;
            }
            FSEmployeeMsg fsEmployeeMsg = fsAddressBookManager.getEmployeeInfo(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId());
            if (fsBindEntity == null) {
                return;
            }
            cardEntity.setName(fsEmployeeMsg.getName());
            cardEntity.setDepartment(fsEmployeeMsg.getDepartment());
            cardEntity.setVocation(fsEmployeeMsg.getPost());
            cardEntity.setEmail(fsEmployeeMsg.getEmail());
            GetEnterpriseDefaultCardVo arg = new GetEnterpriseDefaultCardVo();
            arg.setEa(fsBindEntity.getFsEa());
            com.facishare.marketing.common.result.Result<GetEnterpriseDefaultCardResult> result = enterpriseDefaultCardService.getEnterpriseDefaultCard(arg);
            if (result.isSuccess() && result.getData() != null) {
                cardEntity.setCompanyName(result.getData().getCompanyName());
                cardEntity.setCompanyAddress(result.getData().getCompanyAddress());
            }
        } catch (Exception e) {
            log.warn("AccountManager.buildCardFsBindInfoBuUid error e:{}", e);
        }
    }

    public boolean createCardQRCode(String uid, String ea) {
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("uid", uid);
        String valueJson = GsonUtil.getGson().toJson(valueMap, HashMap.class);
        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(QRCodeTypeEnum.CARD.getType());
        data.setValue(valueJson);
        data.setLengthOfSide(0);
        data.setEa(ea);
        QRCodeManager.CreateQRCodeResult qrCode = qrCodeManager.createQRCode(data);
        if (qrCode == null || StringUtils.isBlank(qrCode.getQrCodeApath())) {
            log.warn("AccountManager.createAccount createQRCode qrUrl not found");
            return false;
        }

        boolean result = photoManager.addOrUpdatePhotoByPhotoTargetType(ea,PhotoTargetTypeEnum.CARD_QRCODE, uid, qrCode.getQrCodeApath(), qrCode.getQrCodeApath());
        if (!result) {
            log.error("AccountManager.createAccount fileManagerPicResult failed, uid={}, apath={}", uid, qrCode.getQrCodeApath());
            return false;
        }

        return true;
    }

    public void createMemberMarketingAccount(String uid, String phone) {
        AccountEntity queryAccountEntity = accountDAO.queryAccountByUid(uid);
        if (queryAccountEntity != null) {
            AccountEntity accountEntity = new AccountEntity();
            accountEntity.setPhone(phone);
            accountDAO.update(accountEntity);
            return;
        }
        UserEntity userEntity = userManager.queryByUid(uid);
        AccountEntity accountEntity = new AccountEntity();
        accountEntity.setUid(uid);
        accountEntity.setPhone(phone);
        accountEntity.setAccountType(AccountTypeEnum.MEMBER_WECHAT_MINI_APP.getType());
        accountEntity.setAppId(userEntity.getAppid());
        accountEntity.setOpenid(userEntity.getOpenid());
        int addAccountResult = accountDAO.insert(accountEntity);
        log.info("createMemberMarketingAccount uid: {} phone: {} addAccountResult: {}", uid, phone, addAccountResult);
    }

}
