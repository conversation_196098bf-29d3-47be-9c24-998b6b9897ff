package com.facishare.marketing.provider.manager;

import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.ImmutableSet;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created on 2021-03-02.
 */
@Component
@Slf4j
public class SceneTriggerManager {
	@Autowired
	private SceneTriggerDao sceneTriggerDao;
	@Autowired
	private TriggerSnapshotDao triggerSnapshotDao;
	@Autowired
	private MarketingSceneDao marketingSceneDao;
	@Autowired
	private ConferenceDAO conferenceDAO;
	@Autowired
	private MarketingLiveDAO marketingLiveDAO;
	@Autowired
	private SceneTriggerTimedTaskDao sceneTriggerTimedTaskDao;
	@Autowired
	private CrmV2Manager crmV2Manager;
	@Autowired
	private OuterServiceWechatManager outerServiceWechatManager;
	@Autowired
	private WxTicketManager wxTicketManager;
	@Autowired
	private ActivityDAO activityDAO;
	@Autowired
	private MarketingEventManager marketingEventManager;

	static Gson gson = GsonUtil.getGson();
	
	public boolean resetSceneTriggerTimedTask(String ea, String sceneTriggerId){
		//查询scene_trigger表的数据
		SceneTriggerEntity sceneTrigger = sceneTriggerDao.getById(ea, sceneTriggerId);
		if (SceneTriggerLifeStatus.DELETED.getLifeStatus().equals(sceneTrigger.getLifeStatus())){
			return false;
		}
		//查询触发器快照信息
		TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getCurrentUseSnapshot(ea, sceneTrigger.getTriggerId());
		if (triggerSnapshot == null){
			return false;
		}
		//根据场景id获取场景信息
		MarketingSceneEntity marketingScene = marketingSceneDao.getMarketingSceneById(ea, sceneTrigger.getSceneId());
		//时间触发型任务的执行时间
		Long executeTime = null;
		if (TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType())){
			if (MarketingSceneType.CONFERENCE.getType().equals(marketingScene.getSceneType())){
				ActivityEntity conference = conferenceDAO.getConferenceById(marketingScene.getTargetId());
				if (TriggerTimeTypeEnum.AT_CONFERENCE_START.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = conference.getStartTime() == null ? null : conference.getStartTime().getTime();
				}
				if (TriggerTimeTypeEnum.BEFORE_CONFERENCE_START.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = conference.getStartTime() == null ? null : DateUtil.getExactTime(conference.getStartTime(), triggerSnapshot.getTriggerDayOffset(), triggerSnapshot.getTriggerAtMinutes());
				}
				if (TriggerTimeTypeEnum.AT_CONFERENCE_END.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = conference.getEndTime() == null ? null : conference.getEndTime().getTime();
				}
				if (TriggerTimeTypeEnum.AFTER_CONFERENCE_END.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = conference.getEndTime() == null ? null : DateUtil.getExactTime(conference.getEndTime(), triggerSnapshot.getTriggerDayOffset(), triggerSnapshot.getTriggerAtMinutes());
				}
			}
			if (MarketingSceneType.LIVE.getType().equals(marketingScene.getSceneType())){
				MarketingLiveEntity marketingLive = marketingLiveDAO.getById(marketingScene.getTargetId());
				if (TriggerTimeTypeEnum.AT_LIVE_START.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = marketingLive.getStartTime() == null ? null : marketingLive.getStartTime().getTime();
				}
				if (TriggerTimeTypeEnum.BEFORE_LIVE_START.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = marketingLive.getStartTime() == null ? null : DateUtil.getExactTime(marketingLive.getStartTime(), triggerSnapshot.getTriggerDayOffset(), triggerSnapshot.getTriggerAtMinutes());
				}
				if (TriggerTimeTypeEnum.AT_LIVE_END.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = marketingLive.getEndTime() == null ? null : marketingLive.getEndTime().getTime();
				}
				if (TriggerTimeTypeEnum.AFTER_LIVE_END.getTimeType().equals(triggerSnapshot.getTriggerTimeType())){
					executeTime = marketingLive.getEndTime() == null ? null : DateUtil.getExactTime(marketingLive.getEndTime(), triggerSnapshot.getTriggerDayOffset(), triggerSnapshot.getTriggerAtMinutes());
				}
			}
			// 此处目标人群服用了活动营销的计算时间逻辑
			if (MarketingSceneType.MARKETING_EVENT.getType().equals(marketingScene.getSceneType()) || MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(marketingScene.getSceneType())) {
				ObjectData objectData = crmV2Manager.getDetail(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingScene.getTargetId());
				if (objectData != null) {
					if (!MarketingEventEnum.TARGET_CROWD_OPERATION_PERIODICITY.getEventType().equals(objectData.getString("event_type"))) {
						if (TriggerTimeTypeEnum.BEFORE_MARKETING_EVENT_START.getTimeType().equals(triggerSnapshot.getTriggerTimeType())) {
							executeTime = objectData.getLong(MarketingEventFieldContants.BEGIN_TIME) == null ? null : DateUtil.getExactTime(new Date(objectData.getLong(MarketingEventFieldContants.BEGIN_TIME)), triggerSnapshot.getTriggerDayOffset(), triggerSnapshot.getTriggerAtMinutes());
						}
						if (TriggerTimeTypeEnum.AFTER_MARKETING_EVENT_END.getTimeType().equals(triggerSnapshot.getTriggerTimeType())) {
							executeTime = objectData.getLong(MarketingEventFieldContants.END_TIME) == null ? null : DateUtil.getExactTime(new Date(objectData.getLong(MarketingEventFieldContants.END_TIME)), triggerSnapshot.getTriggerDayOffset(), triggerSnapshot.getTriggerAtMinutes());
						}
					}
				}
			}
			if (MarketingSceneType.QYWX.getType().equals(marketingScene.getSceneType()) ) {
				executeTime = triggerSnapshot.getExecuteTime();
			}
		} else if (TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType())) {
			executeTime = calFirstExecuteTime(triggerSnapshot);
		}

		//取消该触发器下所有状态为todo的任务
		sceneTriggerTimedTaskDao.cancelAllTodoTask(ea, sceneTriggerId);
		
		if (executeTime != null && executeTime > System.currentTimeMillis()){
			//任务的执行时间>当前时间才创建一条定时任务数据
			sceneTriggerTimedTaskDao.insertTimedTask(UUIDUtil.getUUID(), ea, sceneTriggerId, "common", executeTime);
		}
		return true;
	}

	private  Long calFirstExecuteTime(TriggerSnapshotEntity triggerSnapshot) {
		Long repeatRangeStart = triggerSnapshot.getRepeatRangeStart();
		Long repeatRangeEnd = triggerSnapshot.getRepeatRangeEnd();
		Integer repeatType = triggerSnapshot.getRepeatType();
		List<Integer> repeatValue = null;
		Long res = null;
		if (StringUtils.isNotBlank(triggerSnapshot.getRepeatValue())) {
			List<Double> repeatValueDou = gson.fromJson(triggerSnapshot.getRepeatValue(), List.class);
			repeatValue = repeatValueDou.stream().map(item -> item.intValue()).collect(Collectors.toList());
			Collections.sort(repeatValue);
		}
		if ((repeatType == 1 || repeatType == 2) && CollectionUtil.isEmpty(repeatValue)) {
			return null;
		}
		switch (repeatType) {
			case 1:
				res = getFirstTimeByMonth(repeatValue, triggerSnapshot.getTriggerAtMinutes());
				break;
			case 2:
				res = getFirstTimeByWeek(repeatValue, triggerSnapshot.getTriggerAtMinutes());
				break;
			case 3:
				res = getFirstTimeByDay(triggerSnapshot.getTriggerAtMinutes());
			default:
				break;
		}
		if (res > repeatRangeStart && res < repeatRangeEnd) {
			return res;
		}
		return null;
	}



	private  Long getFirstTimeByDay(Integer triggerAtMinutes) {
		Calendar c = Calendar.getInstance();
		int offSetMinute =  c.get(Calendar.MINUTE) +c.get(Calendar.HOUR_OF_DAY)*60;
		Long exactTime = null;
		if (offSetMinute <= triggerAtMinutes) {
			//今天执行
			exactTime = DateUtil.getExactTime(new Date(), 0, triggerAtMinutes);
		} else {
			//明天执行
			exactTime = DateUtil.getExactTime(new Date(), 1, triggerAtMinutes);
		}
		return exactTime;
	}

	private  Long getFirstTimeByWeek(List<Integer> repeatValue, Integer triggerAtMinutes) {
		Calendar c = Calendar.getInstance();
		int currentDayOfWeek = c.get(Calendar.DAY_OF_WEEK);
		if (1 == currentDayOfWeek) {
			currentDayOfWeek = 7;
		} else {
			currentDayOfWeek -= 1;
		}
		int offSetMinute = c.get(Calendar.MINUTE) +c.get(Calendar.HOUR_OF_DAY)*60;
		Long nextTime=null;
		//今天是执行日
		if (repeatValue.contains(currentDayOfWeek) && offSetMinute <= triggerAtMinutes) {
			nextTime = DateUtil.getExactTime(new Date(), 0, triggerAtMinutes);
		} else {
			//寻找下个执行日
			for (Integer nextRepeat : repeatValue) {
				//本周执行
				if (nextRepeat > currentDayOfWeek) {
					nextTime = DateUtil.getExactTime(new Date(), nextRepeat - currentDayOfWeek, triggerAtMinutes);
					break;
				}
			}
			if (null == nextTime) {
				//下周执行
				nextTime = DateUtil.getExactTime(new Date(), 7 - currentDayOfWeek + repeatValue.get(0), triggerAtMinutes);
			}
		}
		return nextTime;
	}

	private  Long getFirstTimeByMonth(List<Integer> repeatValue, Integer triggerAtMinutes) {
		Calendar c = Calendar.getInstance();
		int offSetMinute =  c.get(Calendar.MINUTE) +c.get(Calendar.HOUR_OF_DAY)*60;
		int currentDayOfMonth = c.get(Calendar.DAY_OF_MONTH);
		int lastDayOfMonth = c.getLeastMaximum(Calendar.DAY_OF_MONTH);
		Long nextTime = null;
		if (repeatValue.contains(currentDayOfMonth) && offSetMinute <= triggerAtMinutes) {
			//今天执行
			nextTime = DateUtil.getExactTime(new Date(), 0, triggerAtMinutes);
		} else {
			for (Integer temp : repeatValue) {
				if (temp > currentDayOfMonth) {
					//	本月执行
					if (currentDayOfMonth <= lastDayOfMonth) {
						nextTime = DateUtil.getExactTime(new Date(), temp - currentDayOfMonth, triggerAtMinutes);
						break;
					} else {
						//月底执行
						nextTime = DateUtil.getExactTime(new Date(), lastDayOfMonth - currentDayOfMonth, triggerAtMinutes);
						break;
					}
				}
			}
			//下月执行
			if (null == nextTime) {
				Date firstDayOfMonth = DateUtil.getFirstDayOfMonth(c.get(Calendar.YEAR), c.get(Calendar.YEAR));
				Date firstDayOfNextMonth = DateUtil.plusMonth(firstDayOfMonth, 1);
				Calendar nextMonth = Calendar.getInstance();
				nextMonth.setTime(firstDayOfNextMonth);
				nextTime = DateUtil.getExactTime(new Date(), lastDayOfMonth - currentDayOfMonth + nextMonth.getLeastMaximum(Calendar.DAY_OF_MONTH) < repeatValue.get(0) ? nextMonth.getLeastMaximum(Calendar.DAY_OF_MONTH) : repeatValue.get(0), triggerAtMinutes);
			}
		}
		return nextTime;
	}

	public boolean handleTriggerTimeChange(String ea, String triggerId){
		List<String> sceneTriggerIds = sceneTriggerDao.listByTriggerId(ea, triggerId).stream().map(SceneTriggerEntity::getId).collect(Collectors.toList());
		sceneTriggerIds.forEach(sceneTriggerId -> this.resetSceneTriggerTimedTask(ea, sceneTriggerId));
		return true;
	}

	@FilterLog
	public boolean handleMarketingEventStartTimeChange(String ea, String marketingEventId, Long fromStartTime, Long toStartTime){
		if (fromStartTime == null && toStartTime == null){
			return true;
		}
		if (fromStartTime != null && toStartTime != null && DateUtils.isSameDay(new Date(fromStartTime), new Date(toStartTime))){
			return true;
		}
		log.info("handleMarketingEventStartTimeChange, ea: {} marketingEventId: {} fromStartTime: {} toStartTime: {}", ea, marketingEventId, fromStartTime, toStartTime);
		return handleSceneTriggerTaskReset(ea, marketingEventId, ImmutableSet.of(TriggerTimeTypeEnum.BEFORE_MARKETING_EVENT_START.getTimeType()));
	}
	
	public boolean handleMarketingEventEndTimeChange(String ea, String marketingEventId, Long fromEndTime, Long toEndTime){
		if (fromEndTime == null && toEndTime == null){
			return true;
		}
		if (fromEndTime != null && toEndTime != null && DateUtils.isSameDay(new Date(fromEndTime), new Date(toEndTime))){
			return true;
		}
		log.info("handleMarketingEventEndTimeChange, ea: {} marketingEventId: {} fromEndTime: {} toEndTime: {}", ea, marketingEventId, fromEndTime, toEndTime);
		return handleSceneTriggerTaskReset(ea, marketingEventId, ImmutableSet.of(TriggerTimeTypeEnum.AFTER_MARKETING_EVENT_END.getTimeType()));
	}
	
	public boolean handleLiveStartTimeChange(String ea, String liveId, Date fromStartTime, Date toStartTime){
		return handleSceneTriggerTaskReset(ea, liveId, ImmutableSet.of(TriggerTimeTypeEnum.AT_LIVE_START.getTimeType(), TriggerTimeTypeEnum.BEFORE_LIVE_START.getTimeType()));
	}
	
	
	public boolean handleLiveEndTimeChange(String ea, String liveId, Date fromEndTime, Date toEndTime){
		return handleSceneTriggerTaskReset(ea, liveId, ImmutableSet.of(TriggerTimeTypeEnum.AT_LIVE_END.getTimeType(), TriggerTimeTypeEnum.AFTER_LIVE_END.getTimeType()));
	}
	
	public boolean handleConferenceStartTimeChange(String ea, String conferenceId, Date fromStartTime, Date toStartTime){
		return handleSceneTriggerTaskReset(ea, conferenceId, ImmutableSet.of(TriggerTimeTypeEnum.AT_CONFERENCE_START.getTimeType(), TriggerTimeTypeEnum.BEFORE_CONFERENCE_START.getTimeType()));
	}
	
	public boolean handleConferenceEndTimeChange(String ea, String conferenceId, Date fromEndTime, Date toEndTime){
		return handleSceneTriggerTaskReset(ea, conferenceId, ImmutableSet.of(TriggerTimeTypeEnum.AT_CONFERENCE_END.getTimeType(), TriggerTimeTypeEnum.AFTER_CONFERENCE_END.getTimeType()));
	}

	public String getEventName(TriggerInstanceEntity triggerInstance, String ea) {
		try {
			SceneTriggerEntity sceneTriggerEntity = sceneTriggerDao.getBySceneTargetIdAndTriggerId(ea, triggerInstance.getSceneType(), triggerInstance.getSceneTargetId(), triggerInstance.getTriggerId());
			if (sceneTriggerEntity == null) {
				return null;
			}
			String sceneTargetId = sceneTriggerEntity.getSceneTargetId();
			MarketingSceneType sceneType = MarketingSceneType.fromType(sceneTriggerEntity.getSceneType());
			if (sceneType == null) {
				return null;
			}
			switch (sceneType) {
				case LIVE:
					MarketingLiveEntity liveEntity = marketingLiveDAO.getById(sceneTargetId);
					return liveEntity == null ? null : liveEntity.getTitle();
				case WX_SERVICE_ACCOUNT:
					Integer systemManagementUserId = wxTicketManager.getEnterpriseAdminInfo(ea);
					return outerServiceWechatManager.getOuterServiceNameByWxAppId(ea, systemManagementUserId, sceneTargetId);
				case CONFERENCE:
					return activityDAO.getTitleById(sceneTargetId);
				case MARKETING_EVENT:
					MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, -10000, sceneTargetId);
					return marketingEventData == null ? null : marketingEventData.getName();
			}
		} catch (Exception e) {
			log.warn("SceneTriggerManager.getEventName Error:" + e);
		}
		return null;
	}
	
	private boolean handleSceneTriggerTaskReset(String ea, String sceneTargetId, Set<String> triggerTimeTypeSet) {
		List<SceneTriggerEntity> sceneTriggers = sceneTriggerDao.listBySceneTargetId(ea, sceneTargetId);
		Map<String, SceneTriggerEntity> triggerIdToSceneTriggerMap = sceneTriggers.stream().collect(Collectors.toMap(SceneTriggerEntity::getTriggerId, v -> v, (v1, v2) -> v1));
		if(triggerIdToSceneTriggerMap.keySet().isEmpty()){
			return true;
		}
		List<TriggerSnapshotEntity> triggerSnapshots = triggerSnapshotDao.listNotSnapshotByTriggerIds(ea, triggerIdToSceneTriggerMap.keySet());
		Set<String> triggerIdSet = triggerSnapshots.stream().filter(triggerSnapshot -> TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType()))
			.filter(triggerSnapshot -> triggerTimeTypeSet.contains(triggerSnapshot.getTriggerTimeType()))
			.map(TriggerSnapshotEntity::getTriggerId).collect(Collectors.toSet());
		Set<String> sceneTriggerIdsToResetTimedTask = triggerIdSet.stream().map(triggerIdToSceneTriggerMap::get).filter(Objects::nonNull).map(SceneTriggerEntity::getId).collect(Collectors.toSet());
		sceneTriggerIdsToResetTimedTask.forEach(sceneTriggerId -> this.resetSceneTriggerTimedTask(ea, sceneTriggerId));
		return true;
	}
}
