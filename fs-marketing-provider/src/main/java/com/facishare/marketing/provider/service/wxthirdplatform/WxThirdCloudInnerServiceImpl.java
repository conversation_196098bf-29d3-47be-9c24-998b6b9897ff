package com.facishare.marketing.provider.service.wxthirdplatform;

import com.facishare.marketing.api.arg.CloudSmsReportArg;
import com.facishare.marketing.api.arg.QueryNoticeListArg;
import com.facishare.marketing.api.arg.SendSystemNoticeArg;
import com.facishare.marketing.api.result.WxThirdComponentResult;
import com.facishare.marketing.api.result.WxThirdPlatformDomainResult;
import com.facishare.marketing.api.result.WxThirdPlatformVersionResult;
import com.facishare.marketing.api.result.system.SystemNoticeCloudListResult;
import com.facishare.marketing.api.result.system.SystemNoticeCloudResult;
import com.facishare.marketing.api.result.system.SystemNoticeListResult;
import com.facishare.marketing.api.result.system.SystemNoticeResult;
import com.facishare.marketing.api.service.wxthirdplatform.WxThirdCloudInnerService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatEaBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatThirdPlatformConfigDao;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatThirdPlatformConfigEntity;
import com.facishare.marketing.provider.manager.SystemNoticeManager;
import com.facishare.marketing.provider.manager.sms.mw.MwSendManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import net.bytebuddy.asm.Advice;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("wxThirdCloudInnerService")
public class WxThirdCloudInnerServiceImpl implements WxThirdCloudInnerService {

    @Autowired
    private WechatThirdPlatformConfigDao wechatThirdPlatformConfigDao;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private WechatEaBindDao wechatEaBindDao;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private SystemNoticeManager systemNoticeManager;
    @Autowired
    private MwSendManager mwSendManager;

    @Override
    public Result<WxThirdComponentResult> getComponentAccessTokenAndAppIdByPlatformId(String fsPlatformId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(fsPlatformId));
        WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(fsPlatformId);
        if (wechatThirdPlatformConfig == null) {
            return Result.newError(SHErrorCode.THIRD_PLATFORM_NOT_EXIST);
        }
        WxThirdComponentResult wxThirdComponentResult = new WxThirdComponentResult(wechatThirdPlatformConfig.getComponentAppId(), wechatThirdPlatformManager.getUnexpiredAccessToken(wechatThirdPlatformConfig));
        return Result.newSuccess(wxThirdComponentResult);
    }

    @Override
    public Result<String> getComponentAccessTokenByPlatformId(String fsPlatformId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(fsPlatformId));
        WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(fsPlatformId);
        return Result.newSuccess(wechatThirdPlatformManager.getUnexpiredAccessToken(wechatThirdPlatformConfig));
    }

    @Override
    public Result<String> getComponentAppIdByPlatformId(String fsPlatformId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(fsPlatformId));
        String componentAppId = wechatThirdPlatformConfigDao.getComponentAppIdByPlatformId(fsPlatformId);
        if (StringUtils.isEmpty(componentAppId)) {
            return Result.newError(SHErrorCode.THIRD_PLATFORM_NOT_EXIST);
        }
        return Result.newSuccess(componentAppId);
    }

    @Override
    public Result<String> getComponentAccessTokenByAppId(String componentAppId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(componentAppId));
        WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getByComponentAppId(componentAppId);
        return Result.newSuccess(wechatThirdPlatformManager.getUnexpiredAccessToken(wechatThirdPlatformConfig));
    }

    @Override
    public Result<String> getEaByAppId(String appId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(appId));
        String ea = wechatEaBindDao.getEaByAppId(appId);
        return Result.newSuccess(ea);
    }

    @Override
    public Result<Boolean> bindAppIdAndEa(String appId, String ea) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(ea));
        return Result.newSuccess(wechatEaBindDao.add(appId, ea) == 1);
    }

    @Override
    public Result<Boolean> unbindAppIdAndEa(String appId, String ea) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(ea));
        if (!ea.equals(wechatEaBindDao.getEaByAppId(appId))) {
            return Result.newSuccess(false);
        }
        return Result.newSuccess(wechatEaBindDao.delete(appId) == 1);
    }

    @Override
    public Result<WxThirdPlatformDomainResult> getDomainByPlatformId(String fsPlatformId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(fsPlatformId));
        WechatThirdPlatformConfigEntity wechatThirdPlatformConfig = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(fsPlatformId);
        WxThirdPlatformDomainResult result = new WxThirdPlatformDomainResult();
        if (wechatThirdPlatformConfig != null) {
            BeanUtils.copyProperties(wechatThirdPlatformConfig, result);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<WxThirdPlatformVersionResult> getShowVersionByPlatformId(String fsPlatformId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(fsPlatformId));
        WechatThirdPlatformConfigEntity wechatThirdPlatform = wechatThirdPlatformConfigDao.getWechatThirdPlatformConfig(fsPlatformId);
        if (wechatThirdPlatform == null) {
            return Result.newError(SHErrorCode.THIRD_PLATFORM_NOT_EXIST);
        }
        if (StringUtils.isEmpty(wechatThirdPlatform.getShowCodeTemplateId()) || StringUtils.isEmpty(wechatThirdPlatform.getShowCodeVersion())) {
            return Result.newSuccess();
        }
        WxThirdPlatformVersionResult result = BeanUtil.copy(wechatThirdPlatform, WxThirdPlatformVersionResult.class);
        result.setPlatformId(fsPlatformId);
        return Result.newSuccess(result);
    }



    @Override
    public Result<String> getAccessByMankeeppro(String wxAppId) {
        String accessToken = wechatAccountManager.getAccessTokenByWxAppId(wxAppId);
        if (StringUtils.isBlank(accessToken)) {
            return Result.newError(SHErrorCode.ACCESS_TOKEN_FAILED);
        }
        return Result.newSuccess(accessToken);
    }

    @Override
    public Result<SystemNoticeCloudListResult> queryNoticeList(QueryNoticeListArg arg) {
        Result<SystemNoticeListResult> result = systemNoticeManager.queryNoticeList(null, null, arg);
        if (!result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(result.getData().getSystemNoticeResultList())){
            return Result.newError(result.getErrCode(), result.getErrMsg());
        }

        SystemNoticeCloudListResult cloudListResult = new SystemNoticeCloudListResult();
        List<SystemNoticeCloudResult> systemNoticeResultList = Lists.newArrayList();
        cloudListResult.setSystemNoticeResultList(systemNoticeResultList);
        cloudListResult.setTotalPage(result.getData().getTotalPage());
        result.getData().getSystemNoticeResultList().forEach(notice -> {
            SystemNoticeCloudResult cloudNotice = new SystemNoticeCloudResult();
            cloudNotice.setId(notice.getId());
            cloudNotice.setTitle(notice.getTitle());
            cloudNotice.setContent(notice.getContent());
            cloudNotice.setNoticeType(notice.getNoticeType());
            cloudNotice.setCreateBy(notice.getCreateBy());
            cloudNotice.setUpdateBy(notice.getUpdateBy());
            cloudNotice.setCreateTime(notice.getCreateTime());
            cloudNotice.setUpdateTime(notice.getUpdateTime());
            cloudNotice.setTop(notice.getTop());
            cloudNotice.setReleaseTime(notice.getReleaseTime());
            systemNoticeResultList.add(cloudNotice);
        });

        return Result.newSuccess(cloudListResult);
    }

    @Override
    public Result<SystemNoticeResult> getSystemNoticeDetailFromFoneshareCloud(SendSystemNoticeArg arg) {
        return systemNoticeManager.getSystemNoticeDetail(null, null, arg);
    }

    @Override
    public Result handleCloudSmsReportData(CloudSmsReportArg arg) {
        ThreadPoolUtils.submit(new Runnable() {
            @Override
            public void run() {
                mwSendManager.handleLocalSmsReport(arg.getReportResultList());
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }
}
