package com.facishare.marketing.provider.service.usermarketingaccount;

import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg;
import com.facishare.marketing.api.arg.PageUserMarketingActionStatisticByCrmObjectArg;
import com.facishare.marketing.api.arg.UserMarketingDetailsByAssociationIdArg;
import com.facishare.marketing.api.arg.usermarketingaccount.*;
import com.facishare.marketing.api.data.WeChatAvatarData;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.data.usermarketingaccount.GetInfoByIdentifyVO;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.CheckUserMarketingAccountResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.marketinguser.MarketingUserExcludeApinameResult;
import com.facishare.marketing.api.result.usermarketingaccount.*;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.exception.MarketingException;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.TimeMeasureUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.qywx.AddCorpTagArg;
import com.facishare.marketing.provider.innerArg.qywx.GetCorpTagListArg;
import com.facishare.marketing.provider.innerData.GlobalCRMStatisticalData;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.innerResult.qywx.GetCorpTagListResult;
import com.facishare.marketing.provider.innerResult.qywx.TagGroup;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.util.ConvertUtil;
import com.facishare.marketing.statistic.outapi.arg.MemberUserMarketingActionStatisticQueryArg;
import com.facishare.marketing.statistic.outapi.arg.StaffUserMarketingActionStatisticArg;
import com.facishare.marketing.statistic.outapi.arg.UserMarketingActionStatisticQueryArg;
import com.facishare.marketing.statistic.outapi.arg.UserMarketingLookUpStatisticByObject;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.marketing.statistic.outapi.result.UserMarketingActionStatisticResult;
import com.facishare.marketing.statistic.outapi.result.UserMarketingLookUpDetailByObjectResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ListTagByIdsArg;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.Wheres;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.service.MetadataTagService;
import com.fxiaoke.limit.GuavaLimiter;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.trace.TraceContext;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 28/03/2019
 */
@Service("userMarketingAccountService")
@Slf4j
public class UserMarketingAccountServiceImpl implements UserMarketingAccountService {
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;
    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;
    @Autowired
    private InitDataManger initDataManger;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;
    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao marketingUserGroupCustomizeObjectMappingDao;
    @Autowired
    private SafetyManagementManager safetyManagementManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private MetadataTagService metadataTagService;
    @Autowired
    private TagModelDao tagModelDao;

    @ReloadableProperty("qywx.default.avatar")
    private String qywxDefaultAvatar;
    @Autowired
    private PushSessionManager pushSessionManager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private UserManager userManager;

    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private UserDAO userDAO;
    @Autowired
    private WxWorkTagSynchronizationManager wxWorkTagSynchronizationManager;
    @Autowired
    private UserMarketingTagService userMarketingTagService;

    private static final String ADD_TAG_RATE_LIMIT = "limit-yxt-add-tag";

    private static final String DELETE_TAG_RATE_LIMIT = "limit-yxt-delete-tag";


    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private UserMarketingCrmCustomizeObjectRelationDao userMarketingCrmCustomizeObjectRelationDao;

    @Override
    public Result<UserMarketingAccountDetailsResult> getUserMarketingDetailsByAssociationId(String ea, Integer fsUserId, UserMarketingDetailsByAssociationIdArg arg) {
        String userMarketingId = null;
        if (arg.getType() == ChannelEnum.MINIAPP.getType() || arg.getType() == ChannelEnum.CRM_LEAD.getType()) {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setType(arg.getType());
            associationArg.setEa(ea);
            associationArg.setAssociationId(arg.getAssociationId());
            ChannelEnum channelEnum = ChannelEnum.getByType(arg.getType());
            associationArg.setTriggerSource(channelEnum.getDescription());
            associationArg.setTriggerAction("getUserMarketingDetailsByAssociationId");
            AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
            if (result != null && StringUtils.isNotEmpty(result.getUserMarketingAccountId())) {
                userMarketingId = result.getUserMarketingAccountId();
            }
        }
        /*if (!userMarketingAccountManager.checkUserMarketingAccountPermission(ea, fsUserId, userMarketingId)) {
            return new Result<>(SHErrorCode.NO_PERMISSION_TO_VIEW);
        }*/
        Result<UserMarketingAccountDetailsResult> dataResult = this.getUserMarketingAccountDetails(ea, fsUserId, userMarketingId);

        if (dataResult.isSuccess() && dataResult.getData() != null) {
            /**
             * 头像处理
             */
            if (CollectionUtils.isNotEmpty(dataResult.getData().getWeChatAvatar())) {
                List<Map<String, Object>> list = dataResult.getData().getWeChatAvatar();
                String nPath = (String) list.get(0).get(WeChatAvatarData.WeChatAvatar.PATH);
                String weChatAvatarUrl = fileV2Manager.getUrlByPath(nPath, ea, false);
                dataResult.getData().setWeChatAvatarUrl(weChatAvatarUrl);
            }
            /**
             * 标签处理
             */
            Result<TagNameList> tagsResult = this.getTagsByUserMarketingId(ea, fsUserId, userMarketingId);
            if (tagsResult.isSuccess()) {
                TagNameList tagNameList = tagsResult.getData();
                dataResult.getData().setTagNameList(tagNameList);
            }
        }
        return Result.newSuccess(dataResult.getData());
    }

    @Override
    public Result<List<String>> getChannelObjectIdByUserMarketingId(String ea, Integer fsUserId, String apiName, String userMarketingId) {
        UserMarketingAccountEntity userMarketingAccountEntity = userMarketingAccountDAO.getById(userMarketingId);
        String phone = userMarketingAccountEntity.getPhone();
        ChannelEnum channelEnum = ChannelEnum.getByApiName(apiName);
        Integer channelType = channelEnum.getType();
        List<String> result = new ArrayList<>();
        if (StringUtils.isNotEmpty(phone)) {
            List<String> phones = Lists.newArrayList(phone);
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            paasQueryFilterArg.setObjectAPIName(apiName);
            paasQueryFilterArg.setQuery(new PaasQueryArg(0, 50));
            if (CrmObjectApiNameEnum.CONTACT.getName().equals(apiName)) {
                Wheres telWheres = builWheresByOneFilter(buildFilter(ContactFieldContants.TEL, "LIKE", phones));
                Wheres mobileWheres = builWheresByOneFilter(buildFilter(ContactFieldContants.MOBILE, "LIKE", phones));
                paasQueryFilterArg.getQuery().setWheres(Lists.newArrayList(telWheres, mobileWheres));
            } else if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(apiName)) {
                Wheres telWheres = builWheresByOneFilter(buildFilter(LeadsFieldContants.TEL, "LIKE", phones));
                Wheres mobileWheres = builWheresByOneFilter(buildFilter(LeadsFieldContants.MOBILE, "LIKE", phones));
                paasQueryFilterArg.getQuery().setWheres(Lists.newArrayList(telWheres, mobileWheres));
            } else if (CrmObjectApiNameEnum.CUSTOMER.getName().equals(apiName)) {
                paasQueryFilterArg.getQuery().addFilter(AccountFieldContants.TEL, "LIKE", phones);
            } else if (CrmObjectApiNameEnum.WECHAT.getName().equals(apiName)) {
                paasQueryFilterArg.getQuery().addFilter(WechatFanFieldContants.PHONE, "LIKE", phones);
            }
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilter(ea, -10000, paasQueryFilterArg, 50);
            if (objectDataInnerPage.getDataList() == null) {
                objectDataInnerPage.setDataList(Lists.newArrayList());
            }
            for (ObjectData objectData : objectDataInnerPage.getDataList()) {
                if (channelType != null) {
                    String objectPhone = getPhoneByChannelObject(channelType, objectData);
                    AssociationArg associationArg = new AssociationArg();
                    associationArg.setType(channelType);
                    associationArg.setAssociationId(objectData.getId());
                    associationArg.setPhone(objectPhone);
                    associationArg.setEa(ea);
                    associationArg.setUserName(objectData.getName());
                    associationArg.setEmail(objectData.getString("email"));
                    associationArg.setTriggerAction("getChannelObjectIdByUserMarketingId");
                    associationArg.setTriggerSource(channelEnum.getDescription());
                    userMarketingAccountAssociationManager.associate(associationArg);
                    if (objectPhone.equals(phone)) {
                        result.add(objectData.getId());
                    }
                }
            }
        }
        List<String> datas = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, channelType, Lists.newArrayList(userMarketingId));
        result.addAll(datas);
        return Result.newSuccess(result);
    }

    public static String getPhoneByChannelObject(Integer channelType, ObjectData channelObject) {
        if (ChannelEnum.getByType(channelType) == ChannelEnum.CRM_CONTACT) {
            String mobile = (String) channelObject.get(ContactFieldContants.MOBILE);
            if (StringUtils.isEmpty(mobile)) {
                mobile = (String) channelObject.get(ContactFieldContants.TEL);
            }
            return mobile;
        } else if (ChannelEnum.getByType(channelType) == ChannelEnum.CRM_LEAD) {
            String mobile = (String) channelObject.get(LeadsFieldContants.MOBILE);
            if (StringUtils.isEmpty(mobile)) {
                mobile = (String) channelObject.get(LeadsFieldContants.TEL);
            }
            return mobile;
        } else if (ChannelEnum.getByType(channelType) == ChannelEnum.CRM_ACCOUNT) {
            return (String) channelObject.get(AccountFieldContants.TEL);
        } else if (ChannelEnum.getByType(channelType) == ChannelEnum.CRM_WX_USER) {
            return (String) channelObject.get(WechatFanFieldContants.PHONE);
        } else if (ChannelEnum.getByType(channelType) == ChannelEnum.CRM_MEMBER) {
            return channelObject.getString(CrmMemberFieldEnum.PHONE.getApiName());
        }
        return null;
    }

    private Wheres builWheresByOneFilter(Filter filter) {
        Wheres wheres = new Wheres();
        wheres.setFilters(Lists.newArrayList(filter));
        return wheres;
    }

    private Filter buildFilter(String fieldName, String operator, List<String> fieldValues) {
        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(operator);
        filter.setFieldValues(fieldValues);
        return filter;
    }

    @Override
    public Result<UserMarketingAccountDetailsResult> getUserMarketingAccountDetails(String ea, Integer fsUserId, String userMarketingAccountId) {
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                .getBaseInfosByIds(ea, fsUserId, Lists.newArrayList(userMarketingAccountId), InfoStateEnum.DETAIL);
        List<UserMarketingAccountData> datas = new ArrayList<>(userMarketingAccountDataMap.values());
        if (!CollectionUtils.isEmpty(datas) && datas.size() == 1) {
            UserMarketingAccountDetailsResult result = ConvertUtil.convert(datas.get(0));
            boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
            if (turnOnPhoneNumberSensitive) {
                result.setPhone(safetyManagementManager.phoneNumberStrSensitive(result.getPhone()));
            }
            /**
             * 头像处理
             */
            if (CollectionUtils.isNotEmpty(result.getWeChatAvatar())) {
                List<Map<String, Object>> list = result.getWeChatAvatar();
                String nPath = (String) list.get(0).get(WeChatAvatarData.WeChatAvatar.PATH);
                String weChatAvatarUrl = fileV2Manager.getUrlByPath(nPath, ea, false);
                result.setWeChatAvatarUrl(weChatAvatarUrl);
            }
            return Result.newSuccess(result);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<GetStatisticalDatasResult> getStatisticalDatas(String ea, Integer fsUserId) {
        int crmAccountCount = 0;
        int crmLeadCount = 0;
        int crmContactCount = 0;
        int crmWxUserCount = 0;
        int crmCustomizeObjectDataCount = 0;
        int crmWxWorkExternalUserCount = 0;
        int crmMemberUserCount = 0;

        GlobalCRMStatisticalData crmStatisticalData = null;
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        if (!isOpen) {
            fsUserId = -10000;
        }
        crmStatisticalData = redisManager.getGlobalCRMStatisticalData(ea, fsUserId);
        if (crmStatisticalData == null) {
            Optional<GlobalCRMStatisticalData> crmStatisticalDataOpt = getCRMStatisticData(ea, fsUserId, isOpen);
            if (crmStatisticalDataOpt.isPresent()) {
                redisManager.setGlobalCrmStatisticData(ea, fsUserId, crmStatisticalDataOpt.get());
                crmStatisticalData = crmStatisticalDataOpt.get();
                crmAccountCount = crmStatisticalData.getCrmAccountCount() == null ? 0 : crmStatisticalData.getCrmAccountCount();
                crmLeadCount = crmStatisticalData.getCrmLeadCount() == null ? 0 : crmStatisticalData.getCrmLeadCount();
                crmContactCount = crmStatisticalData.getCrmContactCount() == null ? 0 : crmStatisticalData.getCrmContactCount();
                crmWxUserCount = crmStatisticalData.getCrmWxUserCount() == null ? 0 : crmStatisticalData.getCrmWxUserCount();
                crmCustomizeObjectDataCount = crmStatisticalData.getCrmCustomizeObjectDataCount() == null ? 0 : crmStatisticalData.getCrmCustomizeObjectDataCount();
                crmWxWorkExternalUserCount = crmStatisticalData.getCrmWxWorkExternalUserCount() == null ? 0 : crmStatisticalData.getCrmWxWorkExternalUserCount();
                crmMemberUserCount = crmStatisticalData.getCrmMemberUserCount() == null ? 0 : crmStatisticalData.getCrmMemberUserCount();
            }
        } else {
            crmAccountCount = crmStatisticalData.getCrmAccountCount();
            crmLeadCount = crmStatisticalData.getCrmLeadCount();
            crmContactCount = crmStatisticalData.getCrmContactCount();
            crmWxUserCount = crmStatisticalData.getCrmWxUserCount();
            crmCustomizeObjectDataCount = crmStatisticalData.getCrmCustomizeObjectDataCount();
            crmWxWorkExternalUserCount = crmStatisticalData.getCrmWxWorkExternalUserCount();
            crmMemberUserCount = crmStatisticalData.getCrmMemberUserCount();
        }

        GetStatisticalDatasResult result = new GetStatisticalDatasResult();
        result.setCrmAccountCount(crmAccountCount);
        result.setCrmWxUserCount(crmWxUserCount);
        result.setCrmLeadCount(crmLeadCount);
        result.setCrmContactCount(crmContactCount);
        result.setCrmWxWorkExternalUserCount(crmWxWorkExternalUserCount);
        result.setCrmMemberUserCount(crmMemberUserCount);
        result.setCrmCustomizeObjectDataCount(crmCustomizeObjectDataCount);
        return Result.newSuccess(result);
    }

    private Optional<GlobalCRMStatisticalData> getCRMStatisticData(String ea, Integer fsUserId, boolean openDataPermission) {
        GlobalCRMStatisticalData globalCRMStatisticalData = new GlobalCRMStatisticalData();
        PaasQueryFilterArg baseQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        baseQueryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        baseQueryFilterArg.setSelectFields(selectFields);
        //判断是否开启多组织
        if (openDataPermission) {
            List<Integer> dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
            if (CollectionUtils.isEmpty(dataPermission)) {
                return Optional.of(globalCRMStatisticalData);
            }
            paasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
        }

        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(7, "marketing_CRM_STATISTICS_DATA_thread");
        TraceContext context = TraceContext.get();
        AtomicBoolean atomicStatus = new AtomicBoolean(true);
        CountDownLatch countDownLatch = new CountDownLatch(7);
        Integer finalFsUserId = fsUserId;
        executorService.execute(() -> {
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                //查询线索数据量
                PaasQueryFilterArg leadQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                leadQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                int crmLeadCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, leadQueryFilterArg);
                globalCRMStatisticalData.setCrmLeadCount(crmLeadCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询客户数据量
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg accountQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                accountQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
                int crmAccountCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, accountQueryFilterArg);
                globalCRMStatisticalData.setCrmAccountCount(crmAccountCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.CUSTOMER.getName(), e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询联系人数据量
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg contactQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                contactQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTACT.getName());
                int crmContactCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, contactQueryFilterArg);
                globalCRMStatisticalData.setCrmContactCount(crmContactCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.CONTACT.getName(), e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询微信用户数据量
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg wxUserQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                wxUserQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT.getName());
                int crmWxUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, wxUserQueryFilterArg);
                globalCRMStatisticalData.setCrmWxUserCount(crmWxUserCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.WECHAT.getName(), e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询企业微信客户的数量
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg wxWorkExternalUserQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                wxWorkExternalUserQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                int crmWxWorkExternalUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, wxWorkExternalUserQueryFilterArg);
                globalCRMStatisticalData.setCrmWxWorkExternalUserCount(crmWxWorkExternalUserCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询会员的数量
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg memberQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                memberQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                int crmMemberUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, memberQueryFilterArg);
                globalCRMStatisticalData.setCrmMemberUserCount(crmMemberUserCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.MEMBER.getName(), e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询自定义对象的数量
            if (context != null) {
                TraceContext._set(context);
            }
            try {
                int crmCustomizeObjectDataCount = countCrmCustomizeObjectData(ea, finalFsUserId);
                globalCRMStatisticalData.setCrmCustomizeObjectDataCount(crmCustomizeObjectDataCount);
            } catch (Exception e) {
                log.info("getCRMStatisticData countCrmCustomizeObjectData failed ea:{} e:", ea, e);
                atomicStatus.set(false);
            } finally {
                countDownLatch.countDown();
                if (context != null) {
                    TraceContext.remove();
                }
            }
        });

        try {
            countDownLatch.await(30L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("getCRMStatisticData await failed ea:{} e:", ea, e);
            atomicStatus.set(false);
        } finally {
            if (!executorService.isShutdown()) {
                executorService.shutdown();
            }
        }

        if (atomicStatus.get()) {
            return Optional.of(globalCRMStatisticalData);
        } else {
            return Optional.empty();
        }
    }

    private int countCrmCustomizeObjectData(String ea, Integer fsUserId) {
        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (CollectionUtils.isEmpty(objectMappingEntityList)) {
            return 0;
        }

        List<String> apiNameList = objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList());
        int totalCount = 0;
        List<String> checkInRedisApiNameList = Lists.newArrayList();
        List<String> checkInObjectApiNameList = Lists.newArrayList();
        for (String apiName : apiNameList) {
            String countVal = redisManager.get("marketing_customize_object_count_" + apiName);
            if (StringUtils.isNotEmpty(countVal)) {
                totalCount += Integer.parseInt(countVal);
                checkInRedisApiNameList.add(apiName);
            } else {
                checkInObjectApiNameList.add(apiName);
            }
        }

        //直接从对象查询
        int expireTime = 24 * 3600 * 7;
        Map<String, Integer> objectDataCountMap = new HashMap<>();
        if (checkInObjectApiNameList.size() > 0) {
            CountDownLatch countDownLatch = new CountDownLatch(checkInObjectApiNameList.size());
            for (String apiName : checkInObjectApiNameList) {
                ThreadPoolUtils.execute(() -> {
                    try {
                        objectDataCountMap.put(apiName, crmMetadataManager.countCrmObjectData(ea, fsUserId, apiName));
                    } catch (Exception e) {
                        log.warn("UserMarketingAccountServiceImpl.countCrmCustomizeObjectData error ea:{} fsUserId:{} apiName:{} e:", ea, fsUserId, apiName, e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }

            try {
                countDownLatch.await(15, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("WxOfficialAccountsServiceImpl.batchGetWxMaterial error e:", e);
            }
        }
        if (!objectDataCountMap.isEmpty()) {
            for (Map.Entry<String, Integer> entry : objectDataCountMap.entrySet()) {
                totalCount += entry.getValue();
                redisManager.set("marketing_customize_object_count_" + entry.getKey(), expireTime, String.valueOf(totalCount));
            }
        }

        //更新redis中的数据
        for (String apiName : checkInRedisApiNameList) {
            ThreadPoolUtils.execute(() -> {
                try {
                    int dataCount = crmMetadataManager.countCrmObjectData(ea, fsUserId, apiName);
                    redisManager.set("marketing_customize_object_count_" + apiName, expireTime, String.valueOf(dataCount));
                } catch (Exception e) {
                    log.warn("UserMarketingAccountServiceImpl.countCrmCustomizeObjectData error ea:{} fsUserId:{} apiName:{} e:", ea, fsUserId, apiName, e);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }

        return totalCount;
    }

    @Override
    public Result<List<UserMarketingAccountData>> listUserMarketingAccount(String ea, Integer fsUserId, ListByFilterArg arg) {
        List<FilterData> filterDataList;
        if (CollectionUtils.isNotEmpty(arg.getFilterDatas())) {
            for (FilterData filterData : arg.getFilterDatas()) {
                filterData.getQuery().setOffset(0);
                filterData.getQuery().setLimit(arg.getPageNo() * arg.getPageSize());
            }
            filterDataList = arg.getFilterDatas();
        } else {
            //没有搜索条件的时候执行以下代码
            filterDataList = new LinkedList<>();
            List<String> objectApiNameList = userMarketingAccountManager.buildUserMarketingApiNameListByEa(ea);
            for (String objectApiName : objectApiNameList) {
                filterDataList.add(buildDefaultFilterDataByApiName(objectApiName, arg.getPageNo(), arg.getPageSize()));
            }
        }
        if (!Strings.isNullOrEmpty(arg.getTagOperator()) && arg.getTagNames() != null && !arg.getTagNames().isEmpty()) {
            for (FilterData filterData : filterDataList) {
                filterData.getQuery().setTagConnector(ConnectorEnum.AND.getValue());
                filterData.getQuery().setTagOperator(arg.getTagOperator());
                filterData.getQuery().setTagNames(arg.getTagNames());
            }
        }
        PageArg pageArg = new PageArg(arg.getPageNo(), arg.getPageSize());
        List<String> ids = new ArrayList<>(userMarketingAccountManager.getMarketingUserIdsByFilterDataList(ea, fsUserId, arg.getSearchType(), filterDataList, pageArg, null, true));
        List<UserMarketingAccountData> result = this.doListUserMarketingAccountData(ea, fsUserId, ids, InfoStateEnum.BRIEF);
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        if (turnOnPhoneNumberSensitive) {
            safetyManagementManager.phoneNumberUserMarketingAccountSensitive(result);
        }
        return Result.newSuccess(result);
    }


    private FilterData buildDefaultFilterDataByApiName(String objectApiName, int pageNo, int pageSize) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(pageNo * pageSize);
        query.setOffset(0);

        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(objectApiName);
        filterData.setQuery(query);

        return filterData;
    }

    @Override
    public Result<Boolean> batchMergeUserMarketingAccountsFromTags(String ea, Integer fsUserId, BatchMergeUserMarketingAccountsFromTagsArg arg) {
        if (arg.getTargetObjectApiNames() == null || arg.getTargetObjectApiNames().isEmpty()) {
            arg.setTargetObjectApiNames(Lists.newArrayList(userMarketingAccountManager.buildUserMarketingApiNameListByEa(ea)));
        }
        TagNameList tagNameListByUserMarketingId = this.getTagsByUserMarketingId(ea, fsUserId, arg.getUserMarketingAccountId()).getData();
        Set<TagName> toKeepTagNames = CollectionUtils.isEmpty(tagNameListByUserMarketingId) ? Sets.newHashSet() : Sets.newHashSet(tagNameListByUserMarketingId);
        toKeepTagNames.retainAll(arg.getTagNameList());
        Set<TagName> toDeleteTagNames = CollectionUtils.isEmpty(tagNameListByUserMarketingId) ? Sets.newHashSet() : Sets.newHashSet(tagNameListByUserMarketingId);
        toDeleteTagNames.removeAll(toKeepTagNames);
        Set<TagName> toInsertTagNames = CollectionUtils.isEmpty(arg.getTagNameList()) ? Sets.newHashSet() : Sets.newHashSet(arg.getTagNameList());
        toInsertTagNames.removeAll(toKeepTagNames);
        if (CollectionUtils.isNotEmpty(toDeleteTagNames)) {
            this.batchDeleteTagsFromUserMarketings(ea, fsUserId, arg.getTargetObjectApiNames(), Lists.newArrayList(arg.getUserMarketingAccountId()), TagNameList.convert(toDeleteTagNames));
        }
        if (CollectionUtils.isNotEmpty(toInsertTagNames)) {
            this.batchAddTagsToUserMarketings(ea, fsUserId, arg.getTargetObjectApiNames(), Lists.newArrayList(arg.getUserMarketingAccountId()), TagNameList.convert(toInsertTagNames));
        }
        return Result.newSuccess(true);
    }

    private List<UserMarketingAccountData> doListUserMarketingAccountData(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum infoStateEnum) {
        if (CollectionUtils.isEmpty(userMarketingAccountIds)) {
            return Lists.newArrayList();
        }
        if (userMarketingAccountIds.size() == 1 && userMarketingAccountIds.get(0) == null) {
            return Lists.newArrayList();
        }
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, fsUserId, userMarketingAccountIds, infoStateEnum);
        return Lists.newArrayList(userMarketingAccountDataMap.values());
    }

    @Override
    public Result<Boolean> batchAddTagsToUserMarketings(String ea, Integer fsUserId, List<String> targetObjectApiNames, List<String> userMarketingIds, TagNameList tagNameList) {
        userMarketingAccountManager.batchAddTagsToUserMarketingAccount(ea, targetObjectApiNames, userMarketingIds, tagNameList);
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> batchDeleteTagsFromUserMarketings(String ea, Integer fsUserId, List<String> targetObjectApiNames, List<String> userMarketingIds, TagNameList tagNameList) {
        userMarketingAccountManager.batchDeleteTagsFromUserMarketingAccount(ea, targetObjectApiNames, userMarketingIds, tagNameList);
        return Result.newSuccess();
    }

    @Override
    public Result<TagNameList> getTagsByUserMarketingId(String ea, Integer fsUserId, String userMarketingId) {
        List<String> objectDescribeApiNames = ChannelEnum.getAllChannelApiName();
        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (CollectionUtils.isNotEmpty(objectMappingEntityList)) {
            objectDescribeApiNames.addAll(objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList()));
        }
        Map<String, List<TagName>> userMarketingAccountIdsAndTagNameListMap = userMarketingAccountManager
                .listTagNameListByUserMarketingAccountIds(ea, objectDescribeApiNames, Lists.newArrayList(userMarketingId));
        if (MapUtils.isEmpty(userMarketingAccountIdsAndTagNameListMap)) {
            return Result.newSuccess();
        }
        if (userMarketingAccountIdsAndTagNameListMap.size() > 1) {
            throw new MarketingException(SHErrorCode.DATA_MORE_THAN_ONE);
        }
        TagNameList tagNameList = TagNameList.convert(userMarketingAccountIdsAndTagNameListMap.get(userMarketingId));
        if (tagNameList != null) {
            try {
                tagNameList = userMarketingTagService.fillIsOnlyRead(ea, fsUserId, tagNameList);
            } catch (Exception e) {
                log.warn("fillIsOnlyRead fail e:", e);
            }
        }
        return Result.newSuccess(tagNameList);
    }

    @Override
    public Result<String> getUserMarketingAccountByMemberId(String ea, Integer fsUserId, String memberId) {
        ObjectData objectData = crmV2Manager.getDetail(ea, fsUserId, MemberFieldContants.API_NAME, memberId);
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(ea);
        associationArg.setType(ChannelEnum.CRM_MEMBER.getType());
        associationArg.setAssociationId(memberId);
        associationArg.setPhone(objectData.getString(CrmMemberFieldEnum.PHONE.getApiName()));
        associationArg.setUserName(objectData.getName());
        associationArg.setEmail(objectData.getString("email"));
        associationArg.setTriggerSource(ChannelEnum.CRM_MEMBER.getDescription());
        associationArg.setTriggerAction("getUserMarketingAccountByMemberId");
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        if (associationResult == null || StringUtils.isEmpty(associationResult.getUserMarketingAccountId())) {
            return Result.newSuccess();
        }
        return Result.newSuccess(associationResult.getUserMarketingAccountId());
    }

    @Override
    public Result<UserMarketingAccountIdResult> getUserMarketingAccountByWxUserId(String ea, Integer fsUserId, String crmWxUserId) {
        ObjectData objectData = crmV2Manager.getDetail(ea, fsUserId, WechatFanFieldContants.API_NAME, crmWxUserId);
        WechatFanData wechatFanData = WechatFanData.wrap(objectData);

        AssociationArg wxAssociationArg = new AssociationArg();
        wxAssociationArg.setType(ChannelEnum.CRM_WX_USER.getType());
        wxAssociationArg.setEa(ea);
        wxAssociationArg.setPhone(wechatFanData.getPhone());
        wxAssociationArg.setAssociationId(wechatFanData.getId());
        wxAssociationArg.setWxAppId(wechatFanData.getWxAppId());
        wxAssociationArg.setAdditionalAssociationId(wechatFanData.getWxOpenId());
        wxAssociationArg.setUserName(objectData.getName());
        wxAssociationArg.setEmail(objectData.getString("email"));
        wxAssociationArg.setTriggerSource(ChannelEnum.CRM_WX_USER.getDescription());
        wxAssociationArg.setTriggerAction("getUserMarketingAccountByWxUserId");
        AssociationResult result = userMarketingAccountAssociationManager.associate(wxAssociationArg);
        UserMarketingAccountIdResult userMarketingAccountIdResult = new UserMarketingAccountIdResult();
        if (result != null) {
            userMarketingAccountIdResult.setUserMarketingAccountId(result.getUserMarketingAccountId());
        }
        return Result.newSuccess(userMarketingAccountIdResult);
    }

    public Result<UserMarketingAccountIdResult> getUserMarketingAccountByCrmExternalUserId(String ea, Integer fsUserId, String crmExternalUserId) {
        ObjectData objectData = crmV2Manager.getDetail(ea, fsUserId, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), crmExternalUserId);
        if (objectData == null) {
            log.warn("userMarketingAccountService.getUserMarketingAccountByCrmExternalUserId error objectData is null ea:{} fsUserId:{} crmExternalUserId:{}", ea, fsUserId, crmExternalUserId);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        String externalUserId = objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()).toString() : null;
        // 获取营销用户id
        AssociationArg associationArg = new AssociationArg();
        associationArg.setPhone(objectData.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()).toString() : null);
        associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
        associationArg.setAssociationId(objectData.getId());
        associationArg.setEa(ea);
        associationArg.setAdditionalAssociationId(externalUserId);
        associationArg.setUserName(objectData.getName());
        associationArg.setEmail(objectData.getString("email"));
        associationArg.setTriggerSource(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription());
        associationArg.setTriggerAction("getUserMarketingAccountByCrmExternalUserId");
        AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
        UserMarketingAccountIdResult userMarketingAccountIdResult = new UserMarketingAccountIdResult();
        if (result != null) {
            userMarketingAccountIdResult.setUserMarketingAccountId(result.getUserMarketingAccountId());
        }
        return Result.newSuccess(userMarketingAccountIdResult);
    }

    @Override
    public Result<Void> addTagToCrmData(AddTagToCrmDataArg arg) {
        String ea = eieaConverter.enterpriseIdToAccount(arg.getTenantId());
        TagName tagName = new TagName(arg.getTagGroupName(), arg.getTagName());
        GuavaLimiter.acquire(ADD_TAG_RATE_LIMIT, ea);
        // 是否打标签时自动新建标签到企微原生标签。
        if (arg.getAddTagToQW() == 1) {
            AddCorpTagArg addCorpTagArg = new AddCorpTagArg();
            addCorpTagArg.setGroupName(arg.getTagGroupName());
            addCorpTagArg.setTags(Lists.newArrayList(new AddCorpTagArg.AddTagArg(arg.getTagName())));
            qywxManager.addCorpTag(ea, addCorpTagArg).isSuccess();
        }
        metadataTagManager
                .addTagsToObjectDatas(ea, Lists.newArrayList(new ObjectDataIdAndTagNameListData(arg.getCrmObjectDescribeApiName(), arg.getCrmObjectId(), TagNameList.convert(ImmutableList.of(tagName)))));
        return Result.newSuccess();
    }

    public Optional<List<String>> getExcludeUserMarketingObjectData(String ea, BatchAddOrDeleteTagNamesToCrmDataArg arg) {
        if (!userMarketingAccountManager.isExcludeApiName(ea, arg.getCrmObjectDescribeApiName())) {
            return Optional.empty();
        }
        Integer type = null;
        List<String> excludeCrmObjectIds = Lists.newArrayList();
        if (arg.getCrmObjectDescribeApiName().equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            type = ChannelEnum.CRM_LEAD.getType();
        } else if (arg.getCrmObjectDescribeApiName().equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
            type = ChannelEnum.CRM_ACCOUNT.getType();
        } else if (arg.getCrmObjectDescribeApiName().equals(CrmObjectApiNameEnum.CONTACT.getName())) {
            type = ChannelEnum.CRM_CONTACT.getType();
        } else if (arg.getCrmObjectDescribeApiName().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())) {
            type = ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType();
        }
        for (String crmObjectId : arg.getCrmObjectIds()) {
            AssociationArg getAssociationArg = new AssociationArg();
            getAssociationArg.setEa(ea);
            getAssociationArg.setType(type);
            getAssociationArg.setAssociationId(crmObjectId);
            if (!userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(getAssociationArg)) {
                excludeCrmObjectIds.add(crmObjectId);
            }
        }
        return Optional.ofNullable(excludeCrmObjectIds);
    }


    @Override
    public Result<Void> batchAddTagNamesToCrmData(Integer ei, Integer fsUserId, BatchAddOrDeleteTagNamesToCrmDataArg arg) {
        if (arg == null || CollectionUtils.isEmpty(arg.getCrmObjectIds())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String ea = eieaConverter.enterpriseIdToAccount(ei);
        //排除关联营销用户的对象，独立打标签
        Optional<List<String>> excludeCrmObjectIdsOpt = getExcludeUserMarketingObjectData(ea, arg);
        if (excludeCrmObjectIdsOpt.isPresent() && CollectionUtils.isNotEmpty(excludeCrmObjectIdsOpt.get())) {
            log.info("userMarketingAccountService.batchAddTagNamesToCrmData excludeCrmObjectIds:{}", excludeCrmObjectIdsOpt.get());
            List<String> userMarketingDataId = Lists.newArrayList();
            for (String objectId : arg.getCrmObjectIds()) {
                if (!excludeCrmObjectIdsOpt.get().contains(objectId)) {
                    userMarketingDataId.add(objectId);
                }
            }
            arg.setCrmObjectIds(userMarketingDataId);
            TagNameList tagNames = TagNameList.convert(arg.getTagNames());
            metadataTagManager.batchUpdateCrmDataTag(ea, arg.getCrmObjectDescribeApiName(), excludeCrmObjectIdsOpt.get(), tagNames);
            if (CollectionUtils.isEmpty(arg.getCrmObjectIds())) {
                return Result.newSuccess();
            }
        }

        // 支持企微好友记录对象打营销用户标签
        if (arg.getCrmObjectDescribeApiName().equals(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName())) {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("_id", arg.getCrmObjectIds());
            List<ObjectData> wechatFriendsRecordObjs = crmV2Manager.queryObjectDatas(ea, arg.getCrmObjectDescribeApiName(), paramMap);
            if (CollectionUtils.isNotEmpty(wechatFriendsRecordObjs)) {
                List<String> externalUserIds = wechatFriendsRecordObjs.stream().map(i -> i.getString("external_user_id")).collect(Collectors.toList());
                arg.setCrmObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                arg.setCrmObjectIds(externalUserIds);
            }
        }

        //1,先查询对象关联的有营销用户和标签
        Map<String, List<TagName>> objectIdUserMarketingTagMap = new HashMap<>();
        Map<String, String> objectIdUserMarketingAccountMap = new HashMap<>();
        for (String crmObjectId : arg.getCrmObjectIds()) {
            QueryTagByCrmObjectIdArg queryTagByCrmObjectIdArg = new QueryTagByCrmObjectIdArg();
            queryTagByCrmObjectIdArg.setCrmObjectId(crmObjectId);
            queryTagByCrmObjectIdArg.setCrmObjectApiName(arg.getCrmObjectDescribeApiName());
            try {
                Map<String, List<TagName>> userMarketingTagMap = queryUserMarketingAccountTagMap(ea, fsUserId, queryTagByCrmObjectIdArg);
                String userMarketingId = userMarketingTagMap.keySet().stream().findFirst().orElse(null);
                if (MapUtils.isNotEmpty(userMarketingTagMap)) {
                    objectIdUserMarketingAccountMap.put(crmObjectId, userMarketingId);
                    objectIdUserMarketingTagMap.put(crmObjectId, userMarketingTagMap.get(userMarketingId));
                }
            }catch (Exception e) {
                log.warn("userMarketingAccountService.batchAddTagNamesToCrmData queryUserMarketingAccountTagMap fail arg:{}", queryTagByCrmObjectIdArg, e);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
        }

        //2,先给该对象打标签
        TagNameList tagNames = TagNameList.convert(arg.getTagNames());
        metadataTagManager.batchUpdateCrmDataTag(ea, arg.getCrmObjectDescribeApiName(), arg.getCrmObjectIds(), tagNames);

        //3，再异步给关联营销用户的对象打标签
        ThreadPoolUtils.executeWithTraceContext(() -> {
            for (String crmObjectId : arg.getCrmObjectIds()) {
                QueryTagByCrmObjectIdArg queryTagByCrmObjectIdArg = new QueryTagByCrmObjectIdArg();
                queryTagByCrmObjectIdArg.setCrmObjectId(crmObjectId);
                queryTagByCrmObjectIdArg.setCrmObjectApiName(arg.getCrmObjectDescribeApiName());
                String userMarketingId = objectIdUserMarketingAccountMap.get(crmObjectId);
                try {
                    List<TagName> userMarketingAccountTagMap = objectIdUserMarketingTagMap.get(crmObjectId);
                    List<String> allUserMarketingAccount = Lists.newArrayList(objectIdUserMarketingAccountMap.get(crmObjectId));
                    List<TagName> allTagList = userMarketingAccountTagMap.stream().distinct().collect(Collectors.toList());
                    List<TagName> addTagList = arg.getTagNames().stream().map(tagName -> allTagList.contains(tagName) ? null : tagName).filter(Objects::nonNull).collect(Collectors.toList());
                    List<TagName> delTagList = allTagList.stream().map(tagName -> arg.getTagNames().contains(tagName) ? null : tagName).filter(Objects::nonNull).collect(Collectors.toList());
                    updateUserMarketingTag(ea, allUserMarketingAccount, addTagList, delTagList);
                } catch (Exception e) {
                    log.warn("userMarketingAccountService.batchAddTagNamesToCrmData queryUserMarketingAccountTagMap fail arg:{}", queryTagByCrmObjectIdArg, e);
                }
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    private boolean isUserMarketingAsscociateCrmObjectLimited(String ea, String userMarketingId){
        Map<String, List<String>> objectIdUserMarketingAccountMap = userMarketingAccountManager.getObjectIdsByUserMarketingAccountDataMap(ea, Lists.newArrayList(userMarketingId));
        if (MapUtils.isEmpty(objectIdUserMarketingAccountMap) || CollectionUtils.isEmpty(objectIdUserMarketingAccountMap.get(userMarketingId))){
            return false;
        }
        //统计营销用户关联的对象的数量，如果大于20个(和产品确认，如果关联20个对象，就认为是不正常的关联），则不进行打标签
        int asscociateCrmObjectCount = 0;
        for (Map.Entry<String, List<String>> entry : objectIdUserMarketingAccountMap.entrySet()) {
            List<String> crmObjectIds = entry.getValue();
            asscociateCrmObjectCount += CollectionUtils.isEmpty(crmObjectIds) ? 0 : crmObjectIds.size();
        }
        log.info("userMarketingAccountService.isUserMarketingAsscociateCrmObjectLimited asscociateCrmObjectCount:{}", asscociateCrmObjectCount);
        return asscociateCrmObjectCount > 20;
    }


    private void updateUserMarketingTag(String ea, List<String> userMarketingIds, List<TagName> addTags, List<TagName> deleteTags) {
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(addTags)) {
            try {
                addUserMarketingAccountTag(ea, userMarketingIds, addTags);
            } catch (Exception e) {
                log.warn("userMarketingAccountService.updateUserMarketingTag add  tag failed ea:{} userMarketingAccount:{} tag:{}", ea, userMarketingIds, addTags, e);
                return;
            }
        }

        if (CollectionUtils.isNotEmpty(deleteTags)) {
            try {
                deleteUserMarketingAccountTag(ea, userMarketingIds, deleteTags);
            } catch (Exception e) {
                log.warn("userMarketingAccountService.updateUserMarketingTag delete  tag failed ea:{} userMarketingAccount:{} tag:{}", ea, userMarketingIds, deleteTags, e);
                return;
            }
        }
    }

    private void addUserMarketingAccountTag(String ea, List<String> userMarketingIds, List<TagName> addTags) {
        if (CollectionUtils.isEmpty(userMarketingIds) || CollectionUtils.isEmpty(addTags)) {
            return;
        }
        log.info("userMarketingAccountService.batchAddTagNamesToCrmData add tag ea:{} userMarketingAccountIds:{} tag:{}", ea, userMarketingIds, addTags);
        for (TagName addTag : addTags) {
            TagModelEntity byEaAndTagName = tagModelDao.getByEaAndTagName(ea, addTag.getFirstTagName(), addTag.getSecondTagName());
            String objects = byEaAndTagName.getObjects();
            List<String> targetObjectApiNames = null;
            if (!"_ALL_".equals(objects)) {
                targetObjectApiNames = Arrays.asList(objects.split(","));
            }
            userMarketingAccountManager.batchAddTagsToUserMarketingAccount(ea, targetObjectApiNames, userMarketingIds, TagNameList.convert(Collections.singleton(addTag)));
        }
    }

    private void deleteUserMarketingAccountTag(String ea, List<String> userMarketingIds, List<TagName> deleteTags) {
        if (CollectionUtils.isEmpty(userMarketingIds) || CollectionUtils.isEmpty(deleteTags)) {
            return;
        }
        userMarketingAccountManager.batchDeleteTagsFromUserMarketingAccount(ea, null, Lists.newArrayList(userMarketingIds), TagNameList.convert(deleteTags));
    }


    private Map<String, List<TagName>> queryUserMarketingAccountTagMap(String ea, Integer
            fsUserId, QueryTagByCrmObjectIdArg arg) {
        GetUserMarketingAccountByObjectArg userMarketingAccountByObjectArg = new GetUserMarketingAccountByObjectArg();
        userMarketingAccountByObjectArg.setCrmObjectId(arg.getCrmObjectId());
        userMarketingAccountByObjectArg.setCrmObjectApiName(arg.getCrmObjectApiName());
        Result<UserMarketingAccountIdResult> userMarketingIdResult = getUserMarketingAccountByObject(ea, fsUserId, userMarketingAccountByObjectArg);
        if (!userMarketingIdResult.isSuccess() || userMarketingIdResult.getData() == null) {
            log.info("queryUserMarketingAccountTagMap failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Maps.newHashMap();
        }

        String userMarketingId = userMarketingIdResult.getData().getUserMarketingAccountId();
        List<String> objectDescribeApiNames = ChannelEnum.getAllChannelApiName();
        objectDescribeApiNames.add(arg.getCrmObjectApiName());
        Map<String, List<TagName>> userMarketingAccountIdsAndTagNameListMap = userMarketingAccountManager
                .listTagNameListByUserMarketingAccountIds(ea, objectDescribeApiNames, Lists.newArrayList(userMarketingId));
        if (MapUtils.isEmpty(userMarketingAccountIdsAndTagNameListMap)) {
            return Maps.newHashMap();
        }

        return userMarketingAccountIdsAndTagNameListMap;
    }

    @Override
    public Result<Void> appendAddTagNamesToCrmData(Integer ei, Integer fsUserId, AppendAddTagNamesToCrmDataArg arg) {
        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(arg.getCrmObjectIds().size(), "batch_add_tag_thread");
        CountDownLatch countDownLatch = new CountDownLatch(arg.getCrmObjectIds().size());
        TraceContext context = TraceContext.get();
        for (String crmObjectId : arg.getCrmObjectIds()) {
            executorService.execute(() -> {
                try {
                    if (context != null) {
                        TraceContext._set(context);
                    }
                    QueryTagByCrmObjectIdArg queryTagByCrmObjectIdArg = new QueryTagByCrmObjectIdArg();
                    queryTagByCrmObjectIdArg.setCrmObjectId(crmObjectId);
                    queryTagByCrmObjectIdArg.setCrmObjectApiName(arg.getCrmObjectDescribeApiName());
                    Result<TagNameList> tagNameListResult = this.queryTagByCrmObjectId(eieaConverter.enterpriseIdToAccount(ei), fsUserId, queryTagByCrmObjectIdArg);
                    if (!tagNameListResult.isSuccess()) {
                        log.warn("queryTagByCrmObjectId tagNameListResult error {}", crmObjectId);
                        return;
                    }
                    List<TagName> addTagList = Lists.newArrayList();
                    List<TagName> delTagList = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(tagNameListResult.getData())) {
                        if (CollectionUtils.isEmpty(arg.getTagNames())) {
                            delTagList = tagNameListResult.getData();
                        } else {
                            for (TagName tagName : tagNameListResult.getData()) {
                                if (!arg.getTagNames().contains(tagName)) {
                                    delTagList.add(tagName);
                                }
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(arg.getTagNames())) {
                        if (CollectionUtils.isEmpty(tagNameListResult.getData())) {
                            addTagList = arg.getTagNames();
                        } else {
                            for (TagName tagName : arg.getTagNames()) {
                                if (!tagNameListResult.getData().contains(tagName)) {
                                    addTagList.add(tagName);
                                }
                            }
                        }
                    }

                    GetUserMarketingAccountByObjectArg userMarketingAccountByObjectArg = new GetUserMarketingAccountByObjectArg();
                    userMarketingAccountByObjectArg.setCrmObjectApiName(arg.getCrmObjectDescribeApiName());
                    userMarketingAccountByObjectArg.setCrmObjectId(crmObjectId);
                    Result<UserMarketingAccountIdResult> userMarketingIdResult = getUserMarketingAccountByObject(eieaConverter.enterpriseIdToAccount(ei), fsUserId, userMarketingAccountByObjectArg);

                    if (CollectionUtils.isNotEmpty(addTagList)) {
                        log.info("batchAddTagNamesToCrmData add tag ei:{} tag:{}", ei, addTagList);
                        String ea = eieaConverter.enterpriseIdToAccount(ei);
                        if (userMarketingIdResult.isSuccess()) {
                            addTagList.forEach(addTag -> {
                                TagModelEntity byEaAndTagName = tagModelDao.getByEaAndTagName(ea, addTag.getFirstTagName(), addTag.getSecondTagName());
                                String objects = byEaAndTagName.getObjects();
                                List<String> targetObjectApiNames = null;
                                if (!"_ALL_".equals(objects)) {
                                    targetObjectApiNames = Arrays.asList(objects.split(","));
                                }
                                userMarketingAccountManager.batchAddTagsToUserMarketingAccount(ea, targetObjectApiNames, Lists.newArrayList(userMarketingIdResult.getData().getUserMarketingAccountId()), TagNameList.convert(Collections.singleton(addTag)));
                            });
                        }
                    }
                } catch (Exception e) {
                    log.warn("userMarketingAccountService.batchAddTagNamesToCrmData failed crmObjectId:{} , errormsg {}", crmObjectId, e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(10L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("userMarketingAccountService.batchAddTagNamesToCrmData failed {}", e);
        }
        if (!executorService.isShutdown()) {
            executorService.shutdown();
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> batchAddTagNamesToCrmData(String ea, Integer fsUserId, BatchAddOrDeleteTagNamesToCrmDataArg
            arg) {
        List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = arg.getCrmObjectIds().stream()
                .map(val -> new ObjectDataIdAndTagNameListData(arg.getCrmObjectDescribeApiName(), val, TagNameList.convert(arg.getTagNames()))).collect(Collectors.toList());
        metadataTagManager.addTagsToObjectDatas(ea, dataIdAndTagNameListObjectDataList);
        return Result.newSuccess();
    }


    @Override
    public Result<Void> batchDeleteTagNamesToCrmData(Integer ei, Integer fsUserId, BatchAddOrDeleteTagNamesToCrmDataArg arg) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        GuavaLimiter.acquire(DELETE_TAG_RATE_LIMIT, ea);
        List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = arg.getCrmObjectIds().stream()
                .map(val -> new ObjectDataIdAndTagNameListData(arg.getCrmObjectDescribeApiName(), val, TagNameList.convert(arg.getTagNames()))).collect(Collectors.toList());
        metadataTagManager.deleteTagsFromObjectDatas(ea, dataIdAndTagNameListObjectDataList);
        return Result.newSuccess();
    }

    @Override
    public Result<Map<String, Integer>> initUserMarketingAccountAndTag() {
        int success = 0;
        List<String> list = enterpriseMetaConfigDao.findEaAll();
        for (String enterpriseAccount : list) {
            if (crmV2Manager.isOpenCrm(enterpriseAccount)) {
                initDataManger.initUserMarketingAccount(enterpriseAccount, -10000);
                initDataManger.initTagAndTagGroup(enterpriseAccount, -10000);
                log.info("initUserMarketingAccountAndTag,ea={}", enterpriseAccount);
                success++;
            } else {
                log.info("initUserMarketingAccountAndTag crm is not open,ea={}", enterpriseAccount);
            }
        }
        log.info("count:{}, success:{}, fail:{}", list.size(), success, list.size() - success);
        Map<String, Integer> map = new HashMap<>();
        map.put("count", list.size());
        map.put("success", success);
        map.put("fail", list.size() - success);
        return Result.newSuccess(map);
    }

    @Override
    public Result<Integer> initUserMarketingAccountAndTag(String[] eaList) {
        int success = 0;
        for (String ea : eaList) {
            if (crmV2Manager.isOpenCrm(ea)) {
                initDataManger.initUserMarketingAccount(ea, -10000);
                initDataManger.initTagAndTagGroup(ea, -10000);
                log.info("initUserMarketingAccountAndTag,ea={}", ea);
                success++;
            } else {
                log.info("initUserMarketingAccountAndTag crm is not open,ea={}", ea);
            }
        }
        return Result.newSuccess(success);
    }

    @Override
    public Result<Map<String, Integer>> initAllMarketingProcessObj() {
        int success = 0;
        List<String> list = enterpriseMetaConfigDao.findEaAll();
        for (String enterpriseAccount : list) {
            if (crmV2Manager.isOpenCrm(enterpriseAccount)) {
                initDataManger.initMarketingProcess(enterpriseAccount, -10000);
                log.info("initUserMarketingAccountAndTag,ea={}", enterpriseAccount);
                success++;
            } else {
                log.info("initUserMarketingAccountAndTag crm is not open,ea={}", enterpriseAccount);
            }
        }
        log.info("count:{}, success:{}, fail:{}", list.size(), success, list.size() - success);
        Map<String, Integer> map = new HashMap<>();
        map.put("count", list.size());
        map.put("success", success);
        map.put("fail", list.size() - success);
        return Result.newSuccess(map);
    }

    @Override
    public Result<Map<String, Integer>> initAllMarketingProcessLatencyResultObj() {
        int success = 0;
        List<String> list = enterpriseMetaConfigDao.findEaAll();
        for (String enterpriseAccount : list) {
            if (crmV2Manager.isOpenCrm(enterpriseAccount)) {
                initDataManger.initMarketingProcessLatencyResult(enterpriseAccount, -10000);
                log.info("initMarketingProcessLatencyResult,ea={}", enterpriseAccount);
                success++;
            } else {
                log.info("initMarketingProcessLatencyResult crm is not open,ea={}", enterpriseAccount);
            }
        }
        log.info("count:{}, success:{}, fail:{}", list.size(), success, list.size() - success);
        Map<String, Integer> map = new HashMap<>();
        map.put("count", list.size());
        map.put("success", success);
        map.put("fail", list.size() - success);
        return Result.newSuccess(map);
    }

    @Override
    public Result<CheckUserMarketingAccountResult> checkUserMarketingAccount(String ea, Integer fsUserId, String uid, List<String> needCheckApiName) {
        CheckUserMarketingAccountResult result = new CheckUserMarketingAccountResult();
        UserMarketingMiniappAccountRelationEntity entity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, uid);
        if (entity == null) {
            result.setStatus(CheckUserMarketingAccountEnum.THIS_IS_A_VISITOR.getStatus());
            return Result.newSuccess(result);
        }
        String userMarketingAccountId = entity.getUserMarketingId();
        result.setUserMarketingAccountId(userMarketingAccountId);
        if (!CollectionUtils.isEmpty(needCheckApiName)) {
            Boolean isRelation = userMarketingAccountRelationManager.isRelateCrmObject(ea, userMarketingAccountId, needCheckApiName);
            if (!isRelation) {
                result.setStatus(CheckUserMarketingAccountEnum.THIS_IS_A_VISITOR.getStatus());
                return Result.newSuccess(result);
            }
        }
        if (!userMarketingAccountManager.checkUserMarketingAccountPermission(ea, fsUserId, userMarketingAccountId)) {
            result.setStatus(CheckUserMarketingAccountEnum.NO_PERMISSION.getStatus());
            return Result.newSuccess(result);
        }
        result.setStatus(CheckUserMarketingAccountEnum.HAVE_PERMISSION.getStatus());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Integer> initMarketingProcessObj(String[] eaList) {
        int success = 0;
        for (String ea : eaList) {
            if (crmV2Manager.isOpenCrm(ea)) {
                initDataManger.initMarketingProcess(ea, -10000);
                log.info("initMarketingProcessObj,ea={}", ea);
                success++;
            } else {
                log.info("initMarketingProcessObj crm is not open,ea={}", ea);
            }
        }
        return Result.newSuccess(success);
    }

    @Override
    public Result<Integer> initMarketingProcessLatencyResultObj(String[] eaList) {
        int success = 0;
        for (String ea : eaList) {
            if (crmV2Manager.isOpenCrm(ea)) {
                initDataManger.initMarketingProcessLatencyResult(ea, -10000);
                log.info("initMarketingProcessLatencyResultObj,ea={}", ea);
                success++;
            } else {
                log.info("initMarketingProcessLatencyResultObj crm is not open,ea={}", ea);
            }
        }
        return Result.newSuccess(success);
    }

    @Override
    public Result<PageResult<UserMarketingActionResult>> pageUserMarketingActionStatistic(String ea, Integer fsUserId, PageUserMarketingActionStatisticArg arg) {
        if (arg.getPageNo() == null || arg.getPageNo() <= 0) {
            arg.setPageNo(1);
        }
        if (arg.getPageSize() == null || arg.getPageSize() <= 0) {
            arg.setPageSize(20);
        }
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> result;
        if (StringUtils.isNotEmpty(arg.getUserMarketingId())) {
            com.facishare.marketing.statistic.common.model.PageArg<UserMarketingActionStatisticQueryArg> pageArg = new com.facishare.marketing.statistic.common.model.PageArg<>();
            pageArg.setPageNo(arg.getPageNo());
            pageArg.setPageSize(arg.getPageSize());
            UserMarketingActionStatisticQueryArg queryArg = new UserMarketingActionStatisticQueryArg();
            queryArg.setEa(ea);
            queryArg.setUserMarketingId(arg.getUserMarketingId());
            pageArg.setQueryArgs(queryArg);
            result = userMarketingStatisticService.pageUserMarketingActionStatistic(pageArg);
            if (result.getData() != null) {
                PageResult<UserMarketingActionResult> pr = userMarketingAccountManager.getUserMarketingActionResultPageResult(ea, fsUserId, result, arg.getPhone());
                return Result.newSuccess(pr);
            }
        } else if (StringUtils.isNotBlank(arg.getSpreadMemberId())) {
            com.facishare.marketing.statistic.common.model.PageArg<MemberUserMarketingActionStatisticQueryArg> pageArg = new com.facishare.marketing.statistic.common.model.PageArg<>();
            pageArg.setPageNo(arg.getPageNo());
            pageArg.setPageSize(arg.getPageSize());
            MemberUserMarketingActionStatisticQueryArg queryArg = new MemberUserMarketingActionStatisticQueryArg();
            queryArg.setEa(ea);
            queryArg.setSpreadMemberId(arg.getSpreadMemberId());
            UserMarketingCrmMemberRelationEntity memberRelationEntity = userMarketingAccountRelationManager.getUserMarketingAccountIdByMemberId(ea, arg.getSpreadMemberId());
            if (memberRelationEntity != null) {
                queryArg.setUserMarketingId(memberRelationEntity.getUserMarketingId());
            }
            pageArg.setQueryArgs(queryArg);
            result = userMarketingStatisticService.pageMemberUserMarketingActionStatistic(pageArg);
            if (result.getData() != null) {
                PageResult<UserMarketingActionResult> pr = userMarketingAccountManager.getUserMarketingActionResultPageResult(ea, fsUserId, result, arg.getPhone());
                return Result.newSuccess(pr);
            }
        } else {
            com.facishare.marketing.statistic.common.model.PageArg<StaffUserMarketingActionStatisticArg> pageArg = new com.facishare.marketing.statistic.common.model.PageArg<>();
            pageArg.setPageNo(arg.getPageNo());
            pageArg.setPageSize(arg.getPageSize());
            StaffUserMarketingActionStatisticArg staffQueryArg = new StaffUserMarketingActionStatisticArg();
            staffQueryArg.setEa(ea);
            staffQueryArg.setFsUserId(fsUserId);
            staffQueryArg.setMarketingActivityId(arg.getMarketingActivityId());
            pageArg.setQueryArgs(staffQueryArg);
            result = userMarketingStatisticService.pageStaffUserMarketingActionStatistic(pageArg);
            if (result.getData() != null) {
                PageResult<UserMarketingActionResult> pr = userMarketingAccountManager.getUserMarketingActionAndWeChatAvatarResultPageResult(ea, fsUserId, result, arg.getPhone());
                return Result.newSuccess(pr);
            }
        }

        return Result.newError(SHErrorCode.SERVER_BUSY);
    }

    @Override
    public Result<PageResult<UserMarketingActionResult>> pageUserMarketingActionStatisticByCrmObject(String ea, Integer fsUserId, PageUserMarketingActionStatisticByCrmObjectArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(fsUserId != null);
        String userMarketingId = null;
        String phone = null;
        PageUserMarketingActionStatisticArg pageUserMarketingActionStatisticArg = new PageUserMarketingActionStatisticArg();
        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setIncludeLayout(false);
        controllerDetailArg.setObjectDataId(arg.getObjectId());
        controllerDetailArg.setObjectDescribeApiName(arg.getObjectApiName());
        ObjectData objectData = metadataControllerServiceManager.detail(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), arg.getObjectApiName(), controllerDetailArg);
        // 如果是好友记录对象, 这里查找关联的企微客户, 再通过企微客户查找营销用户
        if (CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName().equals(arg.getObjectApiName())) {
            String wechatWorkExternalUserId = objectData.getString("external_user_id");
            controllerDetailArg.setObjectDataId(wechatWorkExternalUserId);
            controllerDetailArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            ObjectData wechatWorkExternalUserObj = metadataControllerServiceManager.detail(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), arg.getObjectApiName(), controllerDetailArg);
            String externalUserId = wechatWorkExternalUserObj.getString("external_user_id");
            UserMarketingWxWorkExternalUserRelationEntity relationEntity = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(ea, externalUserId);
            if (relationEntity != null) {
                userMarketingId = relationEntity.getUserMarketingId();
            }
        } else {
            Optional<String> userMarketingIdOptional = userMarketingAccountRelationManager.getUserMarketingAccountIdByCrmObjectId(ea, arg.getObjectApiName(), arg.getObjectId());
            ChannelEnum channelEnum = ChannelEnum.getByApiName(arg.getObjectApiName());
            phone = getPhoneByChannelObject(channelEnum.getType(), objectData);
            if (userMarketingIdOptional.isPresent()) {
                userMarketingId = userMarketingIdOptional.get();
            } else {
                if (!Strings.isNullOrEmpty(phone)) {
                    AssociationArg associationArg = AssociationArg.buildFromCrmObject(ea, arg.getObjectApiName(), arg.getObjectId(), phone);
                    associationArg.setUserName(objectData.getName());
                    associationArg.setEmail(objectData.getString("email"));
                    associationArg.setTriggerSource(channelEnum.getDescription());
                    associationArg.setTriggerAction("pageUserMarketingActionStatisticByCrmObject");
                    AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
                    if (associationResult != null && !Strings.isNullOrEmpty(associationResult.getUserMarketingAccountId())) {
                        userMarketingId = associationResult.getUserMarketingAccountId();
                    }
                }
            }
        }
        if (userMarketingId == null) {
            return Result.newSuccess(new PageResult<>(0, new ArrayList<>(0)));
        }
        pageUserMarketingActionStatisticArg.setPhone(phone);
        pageUserMarketingActionStatisticArg.setPageNo(arg.getPageNo());
        pageUserMarketingActionStatisticArg.setPageSize(arg.getPageSize());
        pageUserMarketingActionStatisticArg.setUserMarketingId(userMarketingId);
        return this.pageUserMarketingActionStatistic(ea, fsUserId, pageUserMarketingActionStatisticArg);
    }

    @Override
    public Result<UserMarketingAccountIdResult> getUserMarketingAccountByObject(String ea, Integer
            fsUserId, GetUserMarketingAccountByObjectArg arg) {
        //当前支持6大对象&设置的自定义对象
        Set<String> objectApiNameSet = new HashSet<>(CrmObjectApiNameEnum.getAllUserApiNames());
        MarketingUserGroupCustomizeObjectMappingEntity customizeObjectMappingEntity = null;
        if (!objectApiNameSet.contains(arg.getCrmObjectApiName())) {
            customizeObjectMappingEntity = marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(ea, arg.getCrmObjectApiName());
            if (customizeObjectMappingEntity != null) {
                objectApiNameSet.add(arg.getCrmObjectApiName());
            }
        }
        if (!objectApiNameSet.contains(arg.getCrmObjectApiName())) {
            return Result.newSuccess();
        }

        if (CrmObjectApiNameEnum.WECHAT.getName().equals(arg.getCrmObjectApiName())) {
            return getUserMarketingAccountByWxUserId(ea, fsUserId, arg.getCrmObjectId());
        } else if (CrmObjectApiNameEnum.MEMBER.getName().equals(arg.getCrmObjectApiName())) {
            UserMarketingAccountIdResult userMarketingAccountIdResult = new UserMarketingAccountIdResult();
            Result<String> marketingAccountByMemberId = getUserMarketingAccountByMemberId(ea, fsUserId, arg.getCrmObjectId());
            if (marketingAccountByMemberId.isSuccess() && StringUtils.isNotBlank(marketingAccountByMemberId.getData())) {
                userMarketingAccountIdResult.setUserMarketingAccountId(marketingAccountByMemberId.getData());
            }
            return Result.newSuccess(userMarketingAccountIdResult);
        } else if (CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(arg.getCrmObjectApiName())) {
            return getUserMarketingAccountByCrmExternalUserId(ea, fsUserId, arg.getCrmObjectId());
        } else {
            ObjectData objectData = crmV2Manager.getDetail(ea, fsUserId, arg.getCrmObjectApiName(), arg.getCrmObjectId());
            if (objectData == null) {
                return Result.newSuccess();
            }
            AssociationArg associationArg = new AssociationArg();
            associationArg.setAssociationId(objectData.getId());
            associationArg.setEa(ea);
            associationArg.setUserName(objectData.getName());
            associationArg.setEmail(objectData.getString("email"));
            if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(arg.getCrmObjectApiName())) {
                associationArg.setType(ChannelEnum.CRM_LEAD.getType());
                associationArg.setTriggerSource(ChannelEnum.CRM_LEAD.getDescription());
            } else if (CrmObjectApiNameEnum.CUSTOMER.getName().equals(arg.getCrmObjectApiName())) {
                associationArg.setType(ChannelEnum.CRM_ACCOUNT.getType());
                associationArg.setTriggerSource(ChannelEnum.CRM_ACCOUNT.getDescription());
            } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(arg.getCrmObjectApiName())) {
                associationArg.setType(ChannelEnum.CRM_CONTACT.getType());
                associationArg.setTriggerSource(ChannelEnum.CRM_CONTACT.getDescription());
            } else {
                associationArg.setType(ChannelEnum.CUSTOMIZE_OBJECT.getType());
                associationArg.setObjectApiName(arg.getCrmObjectApiName());
                associationArg.setTriggerSource(ChannelEnum.CUSTOMIZE_OBJECT.getDescription());
            }
            if (CrmObjectApiNameEnum.getAllUserApiNames().contains(arg.getCrmObjectApiName())) {
                String phone = getPhoneByChannelObject(ChannelEnum.getByApiName(arg.getCrmObjectApiName()).getType(), objectData);
                associationArg.setPhone(phone);
            } else {
                if (customizeObjectMappingEntity != null) {
                    String phoneField = customizeObjectMappingEntity.getMappingFieldByMarketingField(MarketingUserGroupCustomizeObjectMappingEnum.MOBILE.getType());
                    if (phoneField != null) {
                        associationArg.setPhone(objectData.get(phoneField) == null ? null : objectData.get(phoneField).toString());
                    }
                }
            }
            associationArg.setTriggerAction("getUserMarketingAccountByObject");
            AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
            UserMarketingAccountIdResult userMarketingAccountIdResult = new UserMarketingAccountIdResult();
            if (result != null) {
                userMarketingAccountIdResult.setUserMarketingAccountId(result.getUserMarketingAccountId());
            }
            return Result.newSuccess(userMarketingAccountIdResult);
        }
    }

    @Override
    public Result<TagNameList> queryTagByCrmObjectId(String ea, Integer fsUserId, QueryTagByCrmObjectIdArg arg) {
        // 支持企微好友记录对象打营销用户标签
        if (arg.getCrmObjectApiName().equals(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName())) {
            ObjectData wechatFriendsRecordObj = crmV2Manager.getDetailIgnoreError(ea, fsUserId, arg.getCrmObjectApiName(), arg.getCrmObjectId());
            if (wechatFriendsRecordObj != null) {
                arg.setCrmObjectApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                arg.setCrmObjectId(wechatFriendsRecordObj.getString("external_user_id"));
            }
        }
        GetUserMarketingAccountByObjectArg userMarketingAccountByObjectArg = new GetUserMarketingAccountByObjectArg();
        userMarketingAccountByObjectArg.setCrmObjectId(arg.getCrmObjectId());
        userMarketingAccountByObjectArg.setCrmObjectApiName(arg.getCrmObjectApiName());
        if (userMarketingAccountManager.isExcludeApiName(ea, arg.getCrmObjectApiName())) {
            Integer type = null;
            if (arg.getCrmObjectApiName().equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
                type = ChannelEnum.CRM_LEAD.getType();
            } else if (arg.getCrmObjectApiName().equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
                type = ChannelEnum.CRM_ACCOUNT.getType();
            } else if (arg.getCrmObjectApiName().equals(CrmObjectApiNameEnum.CONTACT.getName())) {
                type = ChannelEnum.CRM_CONTACT.getType();
            } else if (arg.getCrmObjectApiName().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())) {
                type = ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType();
            }
            AssociationArg getAssociationArg = new AssociationArg();
            getAssociationArg.setEa(ea);
            getAssociationArg.setType(type);
            getAssociationArg.setAssociationId(arg.getCrmObjectId());
            if (!userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(getAssociationArg)) {
                //直接查对象标签
                List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataList = metadataTagManager
                        .getObjectDataIdAndTagNameListDatasByObjectDataIds(ea, arg.getCrmObjectApiName(), Lists.newArrayList(arg.getCrmObjectId()));
                if (CollectionUtils.isNotEmpty(objectDataIdAndTagNameListDataList)) {
                    TagNameList tagNameList = TagNameList.convert(objectDataIdAndTagNameListDataList.get(0).getTagNameList());
                    return Result.newSuccess(tagNameList);
                }
            }
        }
        Result<UserMarketingAccountIdResult> userMarketingIdResult = getUserMarketingAccountByObject(ea, fsUserId, userMarketingAccountByObjectArg);
        if (!userMarketingIdResult.isSuccess() || userMarketingIdResult.getData() == null) {
            log.info("queryTagByCrmObjectId getUserMarketingAccountByObject failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newSuccess();
        }
        String userMarketingId = userMarketingIdResult.getData().getUserMarketingAccountId();
        List<String> objectDescribeApiNames = ChannelEnum.getAllChannelApiName();
        objectDescribeApiNames.add(arg.getCrmObjectApiName());
        Map<String, List<TagName>> userMarketingAccountIdsAndTagNameListMap = userMarketingAccountManager
                .listTagNameListByUserMarketingAccountIds(ea, objectDescribeApiNames, Lists.newArrayList(userMarketingId));
        if (MapUtils.isEmpty(userMarketingAccountIdsAndTagNameListMap)) {
            return Result.newSuccess();
        }
        if (userMarketingAccountIdsAndTagNameListMap.size() > 1) {
            throw new MarketingException(SHErrorCode.DATA_MORE_THAN_ONE);
        }
        TagNameList tagNameList = TagNameList.convert(userMarketingAccountIdsAndTagNameListMap.get(userMarketingId));
        if (tagNameList != null) {
            try {
                tagNameList = userMarketingTagService.fillIsOnlyRead(ea, fsUserId, tagNameList);
            } catch (Exception e) {
                log.warn("fillIsOnlyRead fail e:", e);
            }
        }
        return Result.newSuccess(tagNameList);
    }

    @Override
    public Result<PageResult<UserMarketingLookUpStatisticByObjectResult>> pageUserMarketingLookUpStatisticByObject
            (String ea, UserMarketingLookUpStatisticByObjectArg arg) {
        UserMarketingLookUpStatisticByObject copy = BeanUtil.copy(arg, UserMarketingLookUpStatisticByObject.class);
        copy.setEa(ea);
        copy.setActionType(NewActionTypeEnum.LOOK_UP.getActionType());
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingLookUpDetailByObjectResult>> pageResultResult = userMarketingStatisticService.pageUserMarketingLookUpStatisticByObject(copy);
        log.info("UserMarketingAccountServiceImpl pageUserMarketingLookUpStatisticByObject pageResultResult = {}", pageResultResult);
        List<String> userMarketingIds = pageResultResult.getData().getData().stream().map(o -> o.getUserMarketingId()).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIds(ea, -10000, userMarketingIds, InfoStateEnum.DETAIL);

        // 若头像为空则取默认头像
        String oldAvatarUrl = fileV2Manager.getUrlByPath(qywxDefaultAvatar, null, false);

        if (pageResultResult.getData() != null) {
            List<UserMarketingLookUpStatisticByObjectResult> statisticByObjectResults = pageResultResult.getData().getData().stream().map(o -> {
                UserMarketingLookUpStatisticByObjectResult userMarketingLookUpStatisticByObjectResult = new UserMarketingLookUpStatisticByObjectResult();
                userMarketingLookUpStatisticByObjectResult.setUserMarketingId(o.getUserMarketingId());
                userMarketingLookUpStatisticByObjectResult.setCount(o.getCount());
                userMarketingLookUpStatisticByObjectResult.setLookUpTotalTime(TimeMeasureUtil.getSecondTime(Long.valueOf(o.getSum())));
                String name = StringUtils.isNotEmpty(userMarketingAccountDataMap.get(o.getUserMarketingId()).getName()) ? userMarketingAccountDataMap.get(o.getUserMarketingId()).getName() : I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_563) + o.getUserMarketingId().substring(o.getUserMarketingId().length() - 6);
                userMarketingLookUpStatisticByObjectResult.setUserMarketingName(name);
                if (CollectionUtils.isNotEmpty(userMarketingAccountDataMap.get(o.getUserMarketingId()).getWeChatAvatar())) {
                    String nPath = (String) userMarketingAccountDataMap.get(o.getUserMarketingId()).getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH);
                    String avatarUrl = fileV2Manager.getUrlByPath(nPath, ea, false);
                    userMarketingLookUpStatisticByObjectResult.setUserMarketingAvatar(avatarUrl);
                } else {
                    // 若头像为空则取默认头像
                    userMarketingLookUpStatisticByObjectResult.setUserMarketingAvatar(oldAvatarUrl);
                }
                return userMarketingLookUpStatisticByObjectResult;
            }).collect(Collectors.toList());
            return Result.newSuccess(new PageResult<>(pageResultResult.getData().getTotalCount(), statisticByObjectResults));
        }
        return null;
    }

    @Override
    public void exportUserMarketingLookUpStatisticByObject(String ea, Integer
            fsUserId, UserMarketingLookUpStatisticByObjectArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            List<UserMarketingLookUpStatisticByObjectResult> upStatisticByObjectResults = pageUserMarketingLookUpStatisticByObject(ea, arg).getData().getData();
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            String excelName = I18nUtil.get(I18nKeyStaticEnum.MARK_STATIC_ACCESSDETAILSEXCEL_TITLE) + ".xlsx";
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, excelName);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_1495));
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = generateExcelTitleList();
            List<List<Object>> upStatisticByObjectList = generateExcelDataList(upStatisticByObjectResults);
            ExcelUtil.fillContent(xssfSheet, titleList, upStatisticByObjectList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, excelName, ea, fsUserId);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

    }

    @Override
    public Result<PageResult<pageUserMarketingLookUpDetail>> pageUserMarketingLookUpDetail(String
                                                                                                   ea, UserMarketingLookUpStatisticByObjectArg arg) {
        UserMarketingLookUpStatisticByObject copy = BeanUtil.copy(arg, UserMarketingLookUpStatisticByObject.class);
        copy.setEa(ea);
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> pageResultResult = userMarketingStatisticService.pageUserMarketingLookUpDetailByUserMarketingId(copy);
        log.info("UserMarketingAccountServiceImpl pageUserMarketingLookUpDetail pageResultResult = {}", pageResultResult);
        if (pageResultResult.getData() != null) {
            List<pageUserMarketingLookUpDetail> result = pageResultResult.getData().getData().stream().map(o -> {
                pageUserMarketingLookUpDetail lookUpDetail = new pageUserMarketingLookUpDetail();
                lookUpDetail.setLookUpTime(DateUtil.format(new Date(o.getCreateTime()), "yyyy-MM-dd HH:mm:ss"));
                JSONObject extensionParamsObj = JSON.parseObject(JSON.toJSONString(o.getProperties())).getJSONObject("extensionParams");
                if (extensionParamsObj != null) {
                    lookUpDetail.setLookUpDuration(TimeMeasureUtil.getMillisecondTime(Long.valueOf(extensionParamsObj.getIntValue("actionDurationTime"))));
                } else {
                    lookUpDetail.setLookUpDuration(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ARTICLESERVICEIMPL_703));
                }
                return lookUpDetail;
            }).collect(Collectors.toList());
            return Result.newSuccess(new PageResult<>(pageResultResult.getData().getTotalCount(), result));
        }
        return null;
    }

    private static List<String> generateExcelTitleList() {
        List<String> titleList = Lists.newArrayList();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1170));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISEEMPLOYEESTATISTICSERVICEIMPL_95));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_1536));
        return titleList;
    }

    private static List<List<Object>> generateExcelDataList
            (List<UserMarketingLookUpStatisticByObjectResult> upStatisticByObjectResults) {
        List<List<Object>> dataList = Lists.newArrayList();
        for (UserMarketingLookUpStatisticByObjectResult result : upStatisticByObjectResults) {
            List<Object> objList = Lists.newArrayList();
            objList.add(result.getUserMarketingName());
            objList.add(result.getCount());
            objList.add(result.getLookUpTotalTime());
            dataList.add(objList);
        }
        return dataList;
    }

    @Override
    public Result<Map<String, String>> getOrCreateTagIdsByTagNames(String ea, Integer fsUserId, String
            describeApiName, List<TagName> tagNames) {
        Map<TagName, String> map = metadataTagManager.getOrCreateTagIdsByTagNames(ea, describeApiName, tagNames);
        Map<String, String> resultMap = new HashMap<>();
        for (Map.Entry<TagName, String> entry : map.entrySet()) {
            resultMap.put(entry.getKey().getCombineName(), entry.getValue());
        }
        return Result.newSuccess(resultMap);
    }

    @Override
    public Result<Map<String, TagName>> listTagByIds(String ea, Integer fsUserId, List<String> tagIds) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        ListTagByIdsArg arg = new ListTagByIdsArg();
        arg.setTenantId(String.valueOf(tenantId));
        arg.setSubTagIdList(tagIds);
        MetadataTagResult<List<MetadataTagData>> result = metadataTagService.listTagByIds(arg);
        if (CollectionUtils.isEmpty(result.getResult())) {
            return Result.newSuccess();
        }

        Map<String, TagName> map = result.getResult().stream().filter(tag -> tag.getGrade() == 1).collect(Collectors.toMap(MetadataTagData::getId, metadataTagData -> {
            List<String> sp = Splitter.on(':').splitToList(metadataTagData.getName());
            if (sp.size() == 2) {
                return new TagName(sp.get(0), sp.get(1));
            }
            return new TagName(sp.get(0), null);
        }, (v1, v2) -> v1));
        return Result.newSuccess(map);
    }

    @Override
    public Result<String> getMarketingAccount(GetMarketingAccountArg arg) {
        AssociationArg associationArg = new AssociationArg();
        String ea = arg.getFsEa();
        if (StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getFsUserId() != null) {
            return Result.newSuccess();
        }
        associationArg.setEa(ea);
        associationArg.setTriggerAction("getMarketingAccount");
        if (StringUtils.isNotBlank(arg.getUid())) {
            //是员工直接返回
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getUid());
            if (fsBindEntity != null) {
                return Result.newSuccess();
            }
            associationArg.setAssociationId(arg.getUid());
            associationArg.setType(ChannelEnum.MINIAPP.getType());
            associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        } else if (StringUtils.isNotBlank(arg.getOpenid())) {
            UserEntity userEntity = userManager.queryByOpenidAndAppid(arg.getAppId(), arg.getOpenid());
            if (userEntity == null) {
                //是员工直接返回
                FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getUid());
                if (fsBindEntity != null) {
                    return Result.newSuccess();
                }
            }
            associationArg.setAssociationId(arg.getOpenid());
            associationArg.setWxAppId(arg.getAppId());
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        } else if (StringUtils.isNotBlank(arg.getMemberId())) {
            associationArg.setAssociationId(arg.getMemberId());
            associationArg.setType(ChannelEnum.CRM_MEMBER.getType());
            associationArg.setTriggerSource(ChannelEnum.CRM_MEMBER.getDescription());
            AssociationResult memberAssociationResult = userMarketingAccountAssociationManager.associate(associationArg);
            if (memberAssociationResult == null || StringUtils.isBlank(memberAssociationResult.getUserMarketingAccountId())) {
                return Result.newError(SHErrorCode.NOT_EXIST_OR_CREATING);
            }
            if (StringUtils.isNotBlank(arg.getBrowserId())) {
                AssociationArg browserUserAssociateArg = new AssociationArg();
                browserUserAssociateArg.setAssociationId(arg.getBrowserId());
                browserUserAssociateArg.setEa(ea);
                browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
                browserUserAssociateArg.setTriggerSource(ChannelEnum.CRM_MEMBER.getDescription());
                browserUserAssociateArg.setTriggerAction("getMarketingAccount");
                //绑定会员和访客关系
                String targetUserMarketingId = userMarketingAccountAssociationManager.bind(browserUserAssociateArg, associationArg);
                return Result.newSuccess(targetUserMarketingId);
            }

        } else if (StringUtils.isNotBlank(arg.getBrowserId())) {
            associationArg.setAssociationId(arg.getBrowserId());
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
            associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        }

        String userMarketingId = null;
        if (associationArg.getType() != 0) {
            AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
            if (associationResult == null || StringUtils.isBlank(associationResult.getUserMarketingAccountId())) {
                return Result.newError(SHErrorCode.NOT_EXIST_OR_CREATING);
            }
            userMarketingId = associationResult.getUserMarketingAccountId();
        }
        return Result.newSuccess(userMarketingId);
    }

    @Override
    public Result<Boolean> asyncBatchAddTagsToUserMarketings(String ea, Integer
            fsUserId, List<String> targetObjectApiNames, List<String> userMarketingIds, TagNameList tagNameList) {
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return Result.newSuccess();
        }
        //是否需要走异步同步
        boolean isAsync = true;
        //指定一个关联对象并且只有有一个关联数据才走同步，其他异步
        if(CollectionUtils.isNotEmpty(targetObjectApiNames) && targetObjectApiNames.size() ==1){
            ChannelEnum byApiName = ChannelEnum.getByApiName(targetObjectApiNames.get(0));
            if (byApiName == null){
                byApiName = ChannelEnum.CUSTOMIZE_OBJECT;
            }
            List<String> list = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, byApiName.getType(), userMarketingIds);
            if(CollectionUtils.isEmpty(list)){
                return Result.newSuccess();
            }
            if(list.size() == 1){
                isAsync = false;
            }

        }
        if (!isAsync) {
            return batchAddTagsToUserMarketings(ea, fsUserId, targetObjectApiNames, userMarketingIds, tagNameList);
        } else {
            //防止超时，异步调用标签接口
            ThreadPoolUtils.execute(() -> {
                batchAddTagsToUserMarketings(ea, fsUserId, targetObjectApiNames, userMarketingIds, tagNameList);
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> asyncBatchDeleteTagsFromUserMarketings(String ea, Integer
            fsUserId, List<String> targetObjectApiName, List<String> userMarketingIds, TagNameList tagNameList) {
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return Result.newSuccess();
        }
        //是否需要走异步同步
        boolean isAsync = true;
        //指定一个关联对象并且只有有一个关联数据才走同步，其他异步
        if(CollectionUtils.isNotEmpty(targetObjectApiName) && targetObjectApiName.size() ==1){
            ChannelEnum byApiName = ChannelEnum.getByApiName(targetObjectApiName.get(0));
            if (null != byApiName) {
                List<String> list = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, byApiName.getType(), userMarketingIds);
                if(CollectionUtils.isEmpty(list)){
                    return Result.newSuccess();
                }
                if(list.size() == 1){
                    isAsync = false;
                }
            }
        }
        if (!isAsync) {
            return batchDeleteTagsFromUserMarketings(ea, fsUserId, targetObjectApiName, userMarketingIds, tagNameList);
        } else {
            //防止超时，异步调用标签接口
            ThreadPoolUtils.execute(() -> {
                batchDeleteTagsFromUserMarketings(ea, fsUserId, targetObjectApiName, userMarketingIds, tagNameList);
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<UserMarketingAccountDetailsResult>> queryMarketingUserAccountByObject
            (String ea, Integer fsUserId, GetPageUserMarketingAccountByObjectArg arg) {
        List<UserMarketingAccountDetailsResult> userMarketingAccountResults = Lists.newArrayList();
        com.facishare.marketing.common.result.PageResult<UserMarketingAccountDetailsResult> pageResult = new com.facishare.marketing.common.result.PageResult<>();
        pageResult.setPageNum(arg.getPageNo());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(userMarketingAccountResults);
        List<String> userMarketingIds = Lists.newArrayList();
        List<ObjectData> accountDatas = Lists.newArrayList();
        List<ObjectData> leadDatas = Lists.newArrayList();
        List<ObjectData> contactDatas = Lists.newArrayList();
        if (Objects.equals(arg.getCrmObjectApiName(), "EnterpriseInfoObj")) {
            List<String> objectIds = Lists.newArrayList(arg.getCrmObjectId());
            //获取关联的客户对象
            List<String> accountIds = Lists.newArrayList();
            PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
            paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, objectIds.size());
            paasQueryArg.addFilter("enterprise_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), objectIds);
            paasFilterArg.setQuery(paasQueryArg);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                accountDatas = objectDataInnerPage.getDataList();
                accountIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
            }
            //获取关联的线索对象
            PaasQueryFilterArg paasLeadsFilterArg = new PaasQueryFilterArg();
            paasLeadsFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
            PaasQueryArg paasLeadsQueryArg = new PaasQueryArg(0, objectIds.size());
            paasLeadsQueryArg.addFilter("enterprise_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), objectIds);
            paasLeadsFilterArg.setQuery(paasLeadsQueryArg);
            InnerPage<ObjectData> objectLeadsDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasLeadsFilterArg);
            if (objectLeadsDataInnerPage != null && CollectionUtils.isNotEmpty(objectLeadsDataInnerPage.getDataList())) {
                leadDatas = objectLeadsDataInnerPage.getDataList();
            }
            //根据客户对象关联查询联系人对象
            if (CollectionUtils.isNotEmpty(accountIds)) {
                PaasQueryFilterArg paasContactFilterArg = new PaasQueryFilterArg();
                paasContactFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTACT.getName());
                PaasQueryArg paasContactQueryArg = new PaasQueryArg(0, accountIds.size());
                paasContactQueryArg.addFilter("account_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), accountIds);
                paasContactFilterArg.setQuery(paasContactQueryArg);
                InnerPage<ObjectData> objectContactDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasContactFilterArg);
                if (objectContactDataInnerPage != null && CollectionUtils.isNotEmpty(objectContactDataInnerPage.getDataList())) {
                    contactDatas = objectContactDataInnerPage.getDataList();
                }
            }
        } else if (Objects.equals(arg.getCrmObjectApiName(), CrmObjectApiNameEnum.CUSTOMER.getName())) {
            List<String> objectIds = Lists.newArrayList(arg.getCrmObjectId());
            //获取客户详情
            List<String> accountIds = Lists.newArrayList();
            List<String> relatedLeadsIds = Lists.newArrayList();
            PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
            paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, objectIds.size());
            paasQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), objectIds);
            paasFilterArg.setQuery(paasQueryArg);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
            if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                accountDatas = objectDataInnerPage.getDataList();
                accountIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
                relatedLeadsIds.addAll(objectDataInnerPage.getDataList().stream().filter(objectData -> objectData.getString("leads_id") != null).map(objectData -> objectData.getString("leads_id")).collect(Collectors.toList()));
            }
            //获取关联的线索对象
            if (CollectionUtils.isNotEmpty(relatedLeadsIds)) {
                PaasQueryFilterArg paasLeadsFilterArg = new PaasQueryFilterArg();
                paasLeadsFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                PaasQueryArg paasLeadsQueryArg = new PaasQueryArg(0, objectIds.size());
                paasLeadsQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), relatedLeadsIds);
                paasLeadsFilterArg.setQuery(paasLeadsQueryArg);
                InnerPage<ObjectData> objectLeadsDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasLeadsFilterArg);
                if (objectLeadsDataInnerPage != null && CollectionUtils.isNotEmpty(objectLeadsDataInnerPage.getDataList())) {
                    leadDatas = objectLeadsDataInnerPage.getDataList();
                }
            }
            //根据客户对象关联查询联系人对象
            if (CollectionUtils.isNotEmpty(accountIds)) {
                PaasQueryFilterArg paasContactFilterArg = new PaasQueryFilterArg();
                paasContactFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTACT.getName());
                PaasQueryArg paasContactQueryArg = new PaasQueryArg(0, accountIds.size());
                paasContactQueryArg.addFilter("account_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), accountIds);
                paasContactFilterArg.setQuery(paasContactQueryArg);
                InnerPage<ObjectData> objectContactDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasContactFilterArg);
                if (objectContactDataInnerPage != null && CollectionUtils.isNotEmpty(objectContactDataInnerPage.getDataList())) {
                    contactDatas = objectContactDataInnerPage.getDataList();
                }
            }
        }
        List<String> leadUserMarketingIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), leadDatas, true);
        List<String> contactUserMarketingIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CONTACT.getName(), contactDatas, true);
        List<String> accountUserMarketingIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CUSTOMER.getName(), accountDatas, true);
        //去重
        userMarketingIds = Stream.of(leadUserMarketingIds, contactUserMarketingIds, accountUserMarketingIds)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return Result.newSuccess(pageResult);
        }
        //获取标签
        List<String> objectDescribeApiNames = ChannelEnum.getAllChannelApiName();
        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (CollectionUtils.isNotEmpty(objectMappingEntityList)) {
            objectDescribeApiNames.addAll(objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList()));
        }
        Map<String, List<TagName>> tagNameMap = userMarketingAccountManager.listTagNameListByUserMarketingAccountIds(ea, objectDescribeApiNames, userMarketingIds);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager
                .getBaseInfosByIds(ea, fsUserId, Lists.newArrayList(userMarketingIds), InfoStateEnum.DETAIL);
        List<UserMarketingAccountData> userMarketingAccountList = new ArrayList<>(userMarketingAccountDataMap.values());
        if (!CollectionUtils.isEmpty(userMarketingAccountList)) {
            userMarketingAccountList.forEach(data -> {
                UserMarketingAccountDetailsResult result = ConvertUtil.convert(data);
                boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
                if (turnOnPhoneNumberSensitive) {
                    result.setPhone(safetyManagementManager.phoneNumberStrSensitive(result.getPhone()));
                }
                if (CollectionUtils.isNotEmpty(result.getWeChatAvatar())) {
                    List<Map<String, Object>> list = result.getWeChatAvatar();
                    String nPath = (String) list.get(0).get(WeChatAvatarData.WeChatAvatar.PATH);
                    String weChatAvatarUrl = fileV2Manager.getUrlByPath(nPath, ea, false);
                    result.setWeChatAvatarUrl(weChatAvatarUrl);
                }
                if (tagNameMap.containsKey(data.getUserMarketingAccountId())) {
                    TagNameList tagNameList = TagNameList.convert(tagNameMap.get(data.getUserMarketingAccountId()));
                    result.setTagNameList(tagNameList);
                }
                userMarketingAccountResults.add(result);
            });
        }
        pageResult.setTotalCount(userMarketingAccountResults.size());
        // 手动分页
        PageUtil<UserMarketingAccountDetailsResult> pageUtil = new PageUtil<>(userMarketingAccountResults, arg.getPageSize());
        pageResult.setResult(pageUtil.getPagedList(arg.getPageNo()));
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult> associateExternalMarketingAccountByUnionIdAndOpenId
            (String ea, Integer fsUserId, String apiName, String objectId, String unionId, String openId) {
        ObjectData crmObject = crmMetadataManager.findByIdV3(ea, -10000, apiName, null, objectId);
        if (crmObject == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        ObjectData externalObject = null;
        ObjectData wechatObject = null;
        if (unionId != null) {
            Optional<ObjectData> externalObjectOpt = userMarketingAccountManager.queryExternalObjectByUnionIdAndOpenId(ea, -10000, unionId, openId);
            if (externalObjectOpt.isPresent()) {
                externalObject = externalObjectOpt.get();
            }
        }

        if (openId != null) {
            Optional<ObjectData> wechatObjectOpt = userMarketingAccountManager.queryWechatObjectByOpenId(ea, -10000, openId);
            if (wechatObjectOpt.isPresent()) {
                wechatObject = wechatObjectOpt.get();
            }
        }
        //无法和企业微信客户或者微信粉丝对象进行关联
        if (externalObject == null && wechatObject == null) {
            return Result.newSuccess();
        }

        //将crm对象和企业微信对象绑定在一起
        List<String> crmObjectUserMarketingAccountIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, apiName, Lists.newArrayList(crmObject), true);
        String userMarketingAccountId = crmObjectUserMarketingAccountIds.get(0);
        if (externalObject != null) {
            //将CRM对象和企业微信客户对象绑定在一起
            UserMarketingWxWorkExternalUserRelationEntity externalUserRelationEntity = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(ea, externalObject.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()).toString());
            if (externalUserRelationEntity == null) {
                AssociationArg associationArg = new AssociationArg();
                associationArg.setPhone(externalObject.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()) != null ? externalObject.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()).toString() : null);
                associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
                associationArg.setAssociationId(externalObject.getId());
                associationArg.setEa(ea);
                associationArg.setAdditionalAssociationId((String) externalObject.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()));
                associationArg.setUserName(externalObject.getName());
                associationArg.setEmail(externalObject.getString("email"));
                associationArg.setTriggerSource(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription());
                associationArg.setTriggerAction("associateExternalMarketingAccountByUnionIdAndOpenId");
                userMarketingAccountAssociationManager.doAssociateAccount(associationArg, userMarketingAccountId);
            } else {
                userMarketingAccountAssociationManager.merge(ea, externalUserRelationEntity.getUserMarketingId(), userMarketingAccountId, null);
            }
        }

        if (wechatObject != null) {
            UserMarketingWxServiceAccountRelationEntity wechatRelation = userMarketingWxServiceAccountRelationDao.getByEaAndWxOpenId(ea, openId);
            String wechatUserMarketingId = null;
            AssociationArg wxAssociationArg = new AssociationArg();
            wxAssociationArg.setEa(ea);
            wxAssociationArg.setType(ChannelEnum.CRM_WX_USER.getType());
            wxAssociationArg.setWxAppId((String) wechatObject.get(WechatFanFieldContants.WX_APP_ID));
            wxAssociationArg.setAssociationId(wechatObject.getId());
            wxAssociationArg.setAdditionalAssociationId(openId);
            wxAssociationArg.setTriggerSource(ChannelEnum.CRM_WX_USER.getDescription());
            wxAssociationArg.setTriggerAction("associateExternalMarketingAccountByUnionIdAndOpenId");
            if (wechatRelation == null) {
                //将CRM对象和微信粉丝对象绑定在一起
                AssociationResult associationResult = userMarketingAccountAssociationManager.associate(wxAssociationArg);
                if (associationResult != null && associationResult.getUserMarketingAccountId() != null) {
                    wechatUserMarketingId = associationResult.getUserMarketingAccountId();
                }
            } else {
                wechatUserMarketingId = wechatRelation.getUserMarketingId();
            }
            userMarketingAccountAssociationManager.merge(ea, wechatUserMarketingId, userMarketingAccountId, wxAssociationArg);
        }

        Result<UserMarketingAccountDetailsResult> userMarketingAccountResult = getUserMarketingAccountDetails(ea, fsUserId, userMarketingAccountId);
        List<List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail>> bindObj = Lists.newArrayList();
        if (userMarketingAccountResult != null && userMarketingAccountResult.getData() != null) {
            UserMarketingAccountDetailsResult userMarketingAccountDetails = userMarketingAccountResult.getData();
            //获取联系人对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmContactInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmContactDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmContactInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail(CrmObjectApiNameEnum.CONTACT.getName(), crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmContactDetails.add(detail);
                }
                bindObj.add(crmContactDetails);
            }
            //获取销售线索对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmLeadInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmLeadDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmLeadInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail(CrmObjectApiNameEnum.CRM_LEAD.getName(), crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmLeadDetails.add(detail);
                }
                bindObj.add(crmLeadDetails);
            }
            //获取客户对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmCustomerInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmAccountDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmCustomerInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail(CrmObjectApiNameEnum.CUSTOMER.getName(), crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmAccountDetails.add(detail);
                }
                bindObj.add(crmAccountDetails);
            }
            //获取微信粉丝对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmWxUserInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmWxDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmWxUserInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail(CrmObjectApiNameEnum.WECHAT.getName(), crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmWxDetails.add(detail);
                }
                bindObj.add(crmWxDetails);
            }
            //获取企业微信客户对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmWxWorkExternalInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmExternalDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmWxWorkExternalInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmExternalDetails.add(detail);
                }
                bindObj.add(crmExternalDetails);
            }
            //获取会员对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmMemberInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmMemberDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmMemberInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail(CrmObjectApiNameEnum.MEMBER.getName(), crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmMemberDetails.add(detail);
                }
                bindObj.add(crmMemberDetails);
            }
            //获取自定义对象数据
            if (CollectionUtils.isNotEmpty(userMarketingAccountDetails.getCrmCustomizeObjectInfos())) {
                List<AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail> crmCustomizeObjectDetails = Lists.newArrayList();
                for (UserMarketingAccountData.CrmObjectIdName crmObjectIdName : userMarketingAccountDetails.getCrmCustomizeObjectInfos()) {
                    AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail detail = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult.BindDetail("CUSTOMIZE_OBJECT", crmObjectIdName.getId(), crmObjectIdName.getName());
                    crmCustomizeObjectDetails.add(detail);
                }
                bindObj.add(crmCustomizeObjectDetails);
            }
        }

        AssociateExternalMarketingAccountByUnionIdAndOpenIdResult result = new AssociateExternalMarketingAccountByUnionIdAndOpenIdResult();
        result.setMarketingUserId(userMarketingAccountId);
        result.setBindObj(bindObj);
        return Result.newSuccess(result);
    }


    @Override
    public Result<PageResult<UserMarketingAccountData>> listMiniAppUserMarketingAccount(String ea, Integer
            fsUserId, ListByFilterArg arg) {
        PageResult<UserMarketingAccountData> pageResult = new PageResult<>();
        List<UserMarketingAccountData> result = Lists.newArrayList();
        pageResult.setData(result);
        pageResult.setTotalCount(0);
        Optional<String> appIdOptional = wechatAccountManager.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID);
        if (!appIdOptional.isPresent() || StringUtils.isBlank(appIdOptional.get()) || WxAppInfoEnum.isSystemApp(appIdOptional.get())) {
            return Result.newError(SHErrorCode.NOT_HOSTING_MINIAPP);
        }
        int userCount = userMarketingAccountDAO.countMiniAppUserIdByEaAndAppid(ea, appIdOptional.get(), arg.getFilterMember(), arg.getKeyword());
        if (userCount == 0) {
            return Result.newSuccess(pageResult);
        }
        Page page = new Page(arg.getPageNo(), arg.getPageSize(), false);
        List<String> idList = userMarketingAccountDAO.queryMiniAppUserIdByEaAndAppid(ea, appIdOptional.get(), arg.getFilterMember(), arg.getKeyword(), page);
        if (CollectionUtils.isEmpty(idList)) {
            return Result.newSuccess(pageResult);
        }
        result = this.doListUserMarketingAccountData(ea, fsUserId, Lists.newArrayList(idList), InfoStateEnum.BRIEF);
        // 临时逻辑
        fillMiniappUserInfo(ea, Lists.newArrayList(idList), result, appIdOptional.get());
        // 按照小程序用户的更新时间倒序排序
        result = result.stream().sorted(Comparator.comparing(UserMarketingAccountData::getUpdateTime).reversed()).collect(Collectors.toList());

        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);
        if (turnOnPhoneNumberSensitive) {
            safetyManagementManager.phoneNumberUserMarketingAccountSensitive(result);
        }
        pageResult.setTotalCount(userCount);
        pageResult.setData(result);
        return Result.newSuccess(pageResult);

    }

    @Deprecated
    private void fillMiniappUserInfo(String ea, List<String> userMarketingAccountIdList, List<UserMarketingAccountData> result, String appId) {
        if (CollectionUtils.isEmpty(userMarketingAccountIdList) || CollectionUtils.isEmpty(result)) {
            return;
        }
        // 这里邮箱使用小程序用户的头像昵称手机号
        Map<String, String> marketingUserIdUidMap = Maps.newHashMap();
        Map<String, UserMarketingAccountEntity> marketingUserIdEntityMap = Maps.newHashMap();
        Map<String, UserEntity> uidUserMap = Maps.newHashMap();
        try {
//            List<UserMarketingMiniappAccountRelationEntity> userMarketingMiniappAccountRelationEntities = userMarketingMiniappAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIdList);
            List<UserMarketingMiniappAccountRelationEntity> userMarketingMiniappAccountRelationEntities = userMarketingMiniappAccountRelationDao.listByUserMarketingIdsAndAppId(ea, userMarketingAccountIdList, appId);
            if (CollectionUtils.isNotEmpty(userMarketingMiniappAccountRelationEntities)) {
                marketingUserIdUidMap = userMarketingMiniappAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingMiniappAccountRelationEntity::getUserMarketingId, UserMarketingMiniappAccountRelationEntity::getUid, (e1, e2) -> e1));
                if (!marketingUserIdUidMap.isEmpty()) {
                    List<UserMarketingAccountEntity> userMarketingAccountEntities = userMarketingAccountDAO.batchGet(Lists.newArrayList(marketingUserIdUidMap.keySet()));
                    if (CollectionUtils.isNotEmpty(userMarketingAccountEntities)) {
                        marketingUserIdEntityMap = userMarketingAccountEntities.stream().collect(Collectors.toMap(UserMarketingAccountEntity::getId, Function.identity(), (e1, e2) -> e1));
                    }
                    List<UserEntity> userEntities = userDAO.listByUids(Lists.newArrayList(marketingUserIdUidMap.values()));
                    if (CollectionUtils.isNotEmpty(userEntities)) {
                        uidUserMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getUid, Function.identity(), (e1, e2) -> e1));
                    }
                }
            }
            for (UserMarketingAccountData userMarketingAccountData : result) {
                String userMarketingAccountId = userMarketingAccountData.getUserMarketingAccountId();
                UserMarketingAccountEntity userMarketingAccountEntity = marketingUserIdEntityMap.get(userMarketingAccountId);
                if (userMarketingAccountEntity != null && userMarketingAccountEntity.getPhone() != null) {
                    userMarketingAccountData.setPhone(userMarketingAccountEntity.getPhone());
                }
                String uid = marketingUserIdUidMap.get(userMarketingAccountId);
                UserEntity userEntity = uidUserMap.get(uid);
                if (userEntity != null && StringUtils.isNotBlank(userEntity.getAvatar())) {
                    userMarketingAccountData.setAvatar(userEntity.getAvatar());
                }
                if (userEntity != null && StringUtils.isNotBlank(userEntity.getName())) {
                    userMarketingAccountData.setWeChatName(userEntity.getName());
                }
                // 新增“创建时间”，取小程序用户user表的create_time
                if (userEntity != null && userEntity.getCreateTime() != null) {
                    userMarketingAccountData.setCreateTime(DateUtil.getTimeStamp(userEntity.getCreateTime()));
                }
                // 新增“修改时间”，取小程序用户user表的last_modify_time
                if (userEntity != null && userEntity.getLastModifyTime() != null) {
                    userMarketingAccountData.setUpdateTime(DateUtil.getTimeStamp(userEntity.getLastModifyTime()));
                }
            }
        } catch (Exception e) {
            log.warn("fillMiniappUserInfo error:", e);
        }
    }

    @Override
    public Result<List<MarketingUserExcludeApinameResult>> getExcludeApiNameList(String ea) {
        List<MarketingUserExcludeApinameResult> result = userMarketingAccountRelationManager.getExcludeApiNameList(ea);
        return Result.newSuccess(result);
    }

    @Override
    public Result setExcludeApiName(String ea, Integer fsUserId, String objectApiName, String objectName, Integer
            status) {
        //目前支持客户对象
        if (!objectApiName.equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return userMarketingAccountRelationManager.setExcludeApiName(ea, fsUserId, objectApiName, objectName, status);
    }


    @Override
    public FunctionResult<GetInfoByIdentifyVO> getInfoByIdentify(GetInfoByIdentifyArg arg) {
        if (arg.getIdentifyType() == null || StringUtils.isBlank(arg.getIdentifyValue())) {
            return FunctionResult.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = eieaConverter.enterpriseIdToAccount(arg.getTenantId());
        String userMarketingId = null;
        int identifyType = arg.getIdentifyType();
        if (identifyType == 0) {
            userMarketingId = arg.getIdentifyValue();
        } else if (identifyType == 1) {
            String mobile = arg.getIdentifyValue();
            UserMarketingAccountEntity userMarketingAccountEntity = userMarketingAccountDAO.getByTenantIdAndPhone(arg.getTenantId(), mobile);
            userMarketingId = userMarketingAccountEntity == null ? null : userMarketingAccountEntity.getId();
        } else if (identifyType == 2) {
            String[] arr = arg.getIdentifyValue().split(":");
            if (arr.length != 2) {
                return FunctionResult.newError(SHErrorCode.PARAMS_ERROR);
            }
            String apiName = arr[0];
            String objectId = arr[1];
            ObjectData objectData = crmV2Manager.getOneByList(ea, SuperUserConstants.USER_ID, apiName, objectId);
            if (objectData == null) {
                return new FunctionResult<>(-1, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2073));
            }
            List<String> userMarketingAccountIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, apiName, Collections.singletonList(objectData), true);
            userMarketingId = CollectionUtils.isNotEmpty(userMarketingAccountIds) ? userMarketingAccountIds.get(0) : null;
        }else if (identifyType == 3){
            //根据邮箱查询营销用户，只返回第一个
            String email = arg.getIdentifyValue();
            UserMarketingAccountEntity userMarketingAccountEntity = userMarketingAccountManager.getUserMarketingAccountByEmail(ea, email);
            userMarketingId = userMarketingAccountEntity == null ? null : userMarketingAccountEntity.getId();
        }

        if (StringUtils.isBlank(userMarketingId)) {
            return new FunctionResult<>(-1, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_TRIGGERTASKINSTANCEMANAGER_1134));
        }

        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = userMarketingAccountManager.getBaseInfosByIdsV2(ea, SuperUserConstants.USER_ID, Lists.newArrayList(userMarketingId), InfoStateEnum.DETAIL);
        UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingId);
        if (userMarketingAccountData == null) {
            return new FunctionResult<>(-1, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2086));
        }

        GetInfoByIdentifyVO vo = new GetInfoByIdentifyVO();
        vo.setUserMarketingId(userMarketingId);
        vo.setUserMarketingName(userMarketingAccountData.getName());
        vo.setPhone(UserMarketingAccountAssociationManager.getFirstPhone(userMarketingAccountData.getPhone()));
        vo.setEmail(userMarketingAccountData.getEmail());
        List<GetInfoByIdentifyVO.RelateObj> relateObjList = Lists.newArrayList();
        vo.setRelateObjList(relateObjList);

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmContactInfos())) {
            for (UserMarketingAccountData.CrmObjectIdName crmContactInfo : userMarketingAccountData.getCrmContactInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(CrmObjectApiNameEnum.CONTACT.getName());
                relateObj.setObjectId(crmContactInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmLeadInfos())) {
            for (UserMarketingAccountData.CrmObjectIdName crmLeadInfo : userMarketingAccountData.getCrmLeadInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                relateObj.setObjectId(crmLeadInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmCustomerInfos())) {
            for (UserMarketingAccountData.CrmObjectIdName crmCustomerInfo : userMarketingAccountData.getCrmCustomerInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(CrmObjectApiNameEnum.CUSTOMER.getName());
                relateObj.setObjectId(crmCustomerInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmWxUserInfos())) {
            for (UserMarketingAccountData.CrmObjectIdName crmWxUserInfo : userMarketingAccountData.getCrmWxUserInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(CrmObjectApiNameEnum.WECHAT.getName());
                relateObj.setObjectId(crmWxUserInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmWxWorkExternalInfos())) {
            for (UserMarketingAccountData.CrmObjectIdName crmWxWorkExternalInfo : userMarketingAccountData.getCrmWxWorkExternalInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                relateObj.setObjectId(crmWxWorkExternalInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmMemberInfos())) {
            for (UserMarketingAccountData.CrmObjectIdName crmMemberInfo : userMarketingAccountData.getCrmMemberInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(CrmObjectApiNameEnum.MEMBER.getName());
                relateObj.setObjectId(crmMemberInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountData.getCrmCustomizeObjectInfos())){
            List<UserMarketingCustomizeObjectRelationEntity> crmCustomizeObjectAccountRelationEntities = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountData.getUserMarketingAccountId()));
            Map<String, UserMarketingCustomizeObjectRelationEntity> crmCustomizeObjectAccountRelationEntityMap = crmCustomizeObjectAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCustomizeObjectRelationEntity::getObjectId, Function.identity()));
            for (UserMarketingAccountData.CrmObjectIdName crmCustomizeObjectInfo : userMarketingAccountData.getCrmCustomizeObjectInfos()) {
                GetInfoByIdentifyVO.RelateObj relateObj = new GetInfoByIdentifyVO.RelateObj();
                relateObj.setApiName(crmCustomizeObjectAccountRelationEntityMap.get(crmCustomizeObjectInfo.getId()).getObjectApiName());
                relateObj.setObjectId(crmCustomizeObjectInfo.getId());
                relateObjList.add(relateObj);
            }
        }

        return FunctionResult.newSuccess(vo);
    }

    @Override
    public Result<String> bindBrowserAndOpenId(BindBrowserAndOpenIdArg arg) {
        if (StringUtils.isEmpty(arg.getEa())) {
            return new Result<>(-1, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2155));
        }
        if (StringUtils.isEmpty(arg.getOpenId())) {
            return new Result<>(-1, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2158));
        }
        if (StringUtils.isEmpty(arg.getBrowserUserId())) {
            return new Result<>(-1, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2161));
        }
        if (StringUtils.isEmpty(arg.getWxAppId())) {
            return new Result<>(-1, I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTSERVICEIMPL_2164));
        }
        Optional<String> userMarketingIdOptional = userMarketingAccountRelationManager.bindWxUserAndBrowserUser(arg.getEa(), arg.getWxAppId(), arg.getOpenId(), arg.getBrowserUserId(), null, "bindBrowserAndOpenId");
        return Result.newSuccess(userMarketingIdOptional.orElse(null));
    }

    @Override
    public Result bulkHangTagWithCrmNotice(String ea, Integer fsUserId, BulkHangTagWithCrmNoticeArg arg) {
        boolean isExclude = userMarketingAccountManager.isExcludeApiName(ea, arg.getObjectApiName());
        if (isExclude) {
            //不给关联的营销用户打标签，只给该对象打标签
            metadataTagManager.bulkHangTag(ea, fsUserId, arg.getObjectApiName(), arg.getDataIds(), arg.getTagIds());
            return Result.newSuccess();
        }


        Map<String, List<String>> objectApiNameDataIdsMap = new HashMap<>();
        //给对象关联营销用户打标签-异步执行
        Set<String> userMarketingAccountIds = Sets.newHashSet();
        for (String dataId : arg.getDataIds()) {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(ea);
            associationArg.setObjectApiName(arg.getObjectApiName());
            Optional<String> userMarketingAccountIdOpt = userMarketingAccountRelationManager.getUserMarketingAccountIdByCrmObjectId(ea, arg.getObjectApiName(), dataId);
            if (!userMarketingAccountIdOpt.isPresent()) {
                objectApiNameDataIdsMap.computeIfAbsent(arg.getObjectApiName(), k -> Lists.newArrayList());
                objectApiNameDataIdsMap.get(arg.getObjectApiName()).add(dataId);
            } else {
                userMarketingAccountIds.add(userMarketingAccountIdOpt.get());
            }
        }

        if (CollectionUtils.isNotEmpty(userMarketingAccountIds)) {
            Map<String, List<String>> dataIdsMap = userMarketingAccountManager.getObjectIdsByUserMarketingAccountDataMap(ea, Lists.newArrayList(userMarketingAccountIds));
            if (MapUtils.isNotEmpty(dataIdsMap)) {
                objectApiNameDataIdsMap.putAll(dataIdsMap);
            }
        }

        //同步营销通标签到PAAS对象标签，给当前对象关联的营销用户对象同步底层标签模型
        Map<String, List<String>> objectApiNameTagIdsMap = buildObjectApiNamesMetadataTags(ea, Lists.newArrayList(objectApiNameDataIdsMap.keySet()), arg.getTagNames());
        if (MapUtils.isEmpty(objectApiNameTagIdsMap)) {
            return Result.newSuccess();
        }
        
        if (MapUtils.isNotEmpty(objectApiNameDataIdsMap)) {
            for (String objectApiName : objectApiNameDataIdsMap.keySet()) {
                log.info("bulkHangTagWithCrmNotice objectApiName = {}, dataIds = {} tagIds:{}", objectApiName, objectApiNameDataIdsMap.get(objectApiName), arg.getTagIds());
                if (objectApiNameDataIdsMap.get(objectApiName).size() >= 5000){
                    //PAAS对象标签最多10000个，如果超过10000个，就不再打标签（可能是关联错误）
                    continue;
                }
                //过滤掉删除和作废数据
                List<ObjectData>  objectDataList = crmMetadataManager.batchGetByIdsV3(ea, -10000, objectApiName, Lists.newArrayList("_id"), objectApiNameDataIdsMap.get(objectApiName));
                if (CollectionUtils.isNotEmpty(objectDataList)) {
                    metadataTagManager.bulkHangTag(ea, fsUserId, objectApiName,objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList()), objectApiNameTagIdsMap.get(objectApiName));
                }
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<String> getMarketingAccountByTargetId(MarketingAccountByTargetArg arg) {
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(arg.getEa());
        if(Objects.equals(arg.getTargetType(), ChannelEnum.MINIAPP.getType())){
            associationArg.setAssociationId(arg.getTargetId());
            associationArg.setType(ChannelEnum.MINIAPP.getType());
            associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        }else if(Objects.equals(arg.getTargetType(), ChannelEnum.WX_SERVICE.getType())){
            String[] split = arg.getTargetId().split(":");
            associationArg.setAssociationId(split[1]);
            associationArg.setWxAppId(split[0]);
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        }else if(Objects.equals(arg.getTargetType(), ChannelEnum.BROWSER_USER.getType())){
            associationArg.setAssociationId(arg.getTargetId());
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
            associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        }
        String userMarketingId = null;
        if (associationArg.getType() != 0) {
            AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
            if (associationResult == null || StringUtils.isBlank(associationResult.getUserMarketingAccountId())) {
                return Result.newError(SHErrorCode.NOT_EXIST_OR_CREATING);
            }
            userMarketingId = associationResult.getUserMarketingAccountId();
        }
        return Result.newSuccess(userMarketingId);
    }

    private Map<String, List<String>> buildObjectApiNamesMetadataTags(String ea, List<String> objectApiNames, List<TagName> tagNames) {
        if (CollectionUtils.isEmpty(tagNames) || CollectionUtils.isEmpty(objectApiNames)) {
            return new HashMap<>();
        }

        Map<String, List<String>> objectApiNameTagIdsMap = new HashMap<>();
        for(String objectApiName :objectApiNames) {
            for (TagName tagName : tagNames) {
                String metadataTagId = metadataTagManager.getOrCreateTagIdByTagName(ea, objectApiName, tagName);
                if (metadataTagId != null){
                    objectApiNameTagIdsMap.computeIfAbsent(objectApiName, k -> Lists.newArrayList());
                    objectApiNameTagIdsMap.get(objectApiName).add(metadataTagId);
                }
            }
        }

        return objectApiNameTagIdsMap;
    }

}

