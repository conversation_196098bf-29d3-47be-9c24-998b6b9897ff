package com.facishare.marketing.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.ListActivityResult;
import com.facishare.marketing.api.result.ListContentResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileListResult;
import com.facishare.marketing.api.result.memberCenter.QueryMemberContentResult;
import com.facishare.marketing.api.service.CustomizeContentService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.common.contstant.OperatorEnum;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonDefaultGroupEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.marketingactivity.ActivityOrderTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dto.CampaignMarketingEventIdTimeDTO;
import com.facishare.marketing.provider.dto.ContentCenterDataDTO;
import com.facishare.marketing.provider.dto.MarketingEventIdTypeDTO;
import com.facishare.marketing.provider.dto.file.FileEntityDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("customizeContentService")
public class CustomizeContentServiceImpl implements CustomizeContentService{
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private AuthPartnerManager authPartnerManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private MemberManager memberManager;
    @ReloadableProperty("qywx.group.message.default.cover")
    private String defaultCoverPath;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Override
    public Result<PageResult<ListActivityResult>> listActivityList(String fsEa, ListActivityListArg arg) {
        PageResult<ListActivityResult> pageResult = new PageResult();
        List<ListActivityResult> marketingEventResultList = Lists.newArrayList();
        pageResult.setResult(marketingEventResultList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        Boolean allMarketingEvent = arg.getIsAllActivityType();
        List<String> eventTypeList = arg.getActivityTypes();
        List<String> marketingEventIds = arg.getMarketingEventIds();
        String ea = fsEa;
        if (StringUtils.isEmpty(ea)) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        }
        Preconditions.checkArgument(ea != null);
        List<MarketingEventIdTypeDTO> visibleMarketingEventList = Lists.newArrayList();
        ListBriefMarketingEventsArg paasArg = new ListBriefMarketingEventsArg();
        if(StringUtils.isNotBlank(arg.getKeyword())){
            paasArg.setName(arg.getKeyword());
        }
        PageArg pageArg = new PageArg(arg.getPageNum(), arg.getPageSize());
        if (allMarketingEvent != null && allMarketingEvent && CollectionUtils.isNotEmpty(eventTypeList)){
            paasArg.setEventTypeList(eventTypeList);
            //如果是包含多会场活动,则过滤掉有父级活动
            if(eventTypeList.contains(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType())){
                paasArg.setFilterSonData(true);
            }
        }else{
            if (CollectionUtils.isNotEmpty(marketingEventIds)) {
//                if (arg.getOrderByIds()!=null && arg.getOrderByIds()){
//                    marketingEventIds = marketingEventIds.stream()
//                            .skip((arg.getPageNum() - 1) * arg.getPageSize())
//                            .limit(arg.getPageSize())
//                            .collect(Collectors.toList());
//                }
                paasArg.setMarketingEventIds(marketingEventIds);
            }
        }
        com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResults;
        if(arg.getOrderByIds()!=null && arg.getOrderByIds() && CollectionUtils.isNotEmpty(marketingEventIds)){
            marketingEventsBriefResults = marketingEventManager.listMarketingEvents(eieaConverter.enterpriseAccountToId(ea), -10000, paasArg, null);
            marketingEventsBriefResults.setTotalCount(marketingEventsBriefResults.getTotalCount());
        } else if (arg.getOrderByIds() != null && arg.getOrderByIds() && marketingEventIds != null && marketingEventIds.size() == 0) {
            marketingEventsBriefResults = new com.facishare.marketing.api.result.PageResult<>();
            marketingEventsBriefResults.setTotalCount(0);
        } else {
            if (arg.getMaterialTagFilter() != null && arg.getMaterialTagFilter().checkValid()) {
                // 内容标签过滤处理
                Integer type = arg.getMaterialTagFilter().getType();
                List<String> materialTagIds = arg.getMaterialTagFilter().getMaterialTagIds();
//                Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
                List<String> ids;
                if (type == 1) {
                    ids = materialTagRelationDao.queryListByAnyTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.LIVE.getType(), ObjectTypeEnum.CONFERENCE.getType(), ObjectTypeEnum.ACTIVITY.getType()));
                } else {
                    ids = materialTagRelationDao.queryListByAllTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.LIVE.getType(), ObjectTypeEnum.CONFERENCE.getType(), ObjectTypeEnum.ACTIVITY.getType()));
                }
                if (CollectionUtils.isEmpty(ids)) {
                    return Result.newSuccess(new PageResult());
                }
                //查出多会场活动
                List<String> multivenueIds = Lists.newArrayList();
                ListBriefMarketingEventsArg multivenueIBatchGetMktEventArg = new ListBriefMarketingEventsArg();
                multivenueIBatchGetMktEventArg.setMarketingEventIds(ids);
                multivenueIBatchGetMktEventArg.setEventType("multivenue_marketing");
                com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> multivenueIResult = marketingEventManager.listMarketingEvents(eieaConverter.enterpriseAccountToId(ea), -10000, multivenueIBatchGetMktEventArg, null);
                if(CollectionUtils.isNotEmpty(multivenueIResult.getData())){
                    multivenueIds = multivenueIResult.getData().stream().map(MarketingEventsBriefResult::getId).collect(Collectors.toList());
                }

                //除去多会场活动的子活动剩下的活动
                ListBriefMarketingEventsArg batchGetMktEventArg = new ListBriefMarketingEventsArg();
                ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false);
                if (arg.getOrderType() != null && StringUtils.isNotBlank(ActivityOrderTypeEnum.getValue(arg.getOrderType()))) {
                    orderBy = new ListBriefMarketingEventsArg.OrderBy(ActivityOrderTypeEnum.getValue(arg.getOrderType()), false);
                }
                batchGetMktEventArg.setOrderByList(Lists.newArrayList(orderBy));
                batchGetMktEventArg.setMarketingEventIds(ids);
                if(StringUtils.isNotBlank(arg.getKeyword())){
                    batchGetMktEventArg.setName(arg.getKeyword());
                }
                if (CollectionUtils.isNotEmpty(multivenueIds)){
                    FilterData filterData = new FilterData();
                    SearchTemplateQuery query = new SearchTemplateQuery();
                    query.addFilter("parent_id", OperatorEnum.NHASANYOF.getOperator(), multivenueIds);
                    filterData.setQuery(query);
                    batchGetMktEventArg.setFilterData(filterData);
                }
                com.facishare.marketing.api.result.PageResult<MarketingEventsBriefResult> marketingEventsBriefResultPageResult = marketingEventManager.listMarketingEvents(eieaConverter.enterpriseAccountToId(ea), -10000, batchGetMktEventArg, pageArg);
                marketingEventsBriefResults = new com.facishare.marketing.api.result.PageResult<>();
                marketingEventsBriefResults.setTotalCount(marketingEventsBriefResultPageResult.getTotalCount());
                marketingEventsBriefResults.setData(marketingEventsBriefResultPageResult.getData());
            } else {
                boolean filterSonData = false;
                if(arg.getFilterData()!=null && arg.getFilterData().getQuery()!=null && CollectionUtils.isNotEmpty(arg.getFilterData().getQuery().getFilters())){
                    List<Filter> filters = arg.getFilterData().getQuery().getFilters();
                    for(Filter filter : filters){
                        if(Objects.equals(filter.getFieldName(),"event_type")){
                            if(Objects.equals(filter.getOperator(),OperatorEnum.EQ.getOperator()) && filter.getFieldValues().contains("multivenue_marketing")){
                                filterSonData = true;
                            }
                            if(Objects.equals(filter.getOperator(),OperatorEnum.N.getOperator()) && !filter.getFieldValues().contains("multivenue_marketing")){
                                filterSonData = true;
                            }
                            if(Objects.equals(filter.getOperator(),OperatorEnum.HASANYOF.getOperator()) && filter.getFieldValues().contains("multivenue_marketing")){
                                filterSonData = true;
                            }
                            if(Objects.equals(filter.getOperator(),OperatorEnum.NHASANYOF.getOperator()) && !filter.getFieldValues().contains("multivenue_marketing")){
                                filterSonData = true;
                            }
                            break;
                        }
                    }
                    paasArg.setFilterSonData(filterSonData);
                }
                paasArg.setFilterData(arg.getFilterData());
                ListBriefMarketingEventsArg.OrderBy orderBy = new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false);
                if (arg.getOrderType() != null && StringUtils.isNotBlank(ActivityOrderTypeEnum.getValue(arg.getOrderType()))) {
                    orderBy = new ListBriefMarketingEventsArg.OrderBy(ActivityOrderTypeEnum.getValue(arg.getOrderType()), false);
                }
                paasArg.setOrderByList(Lists.newArrayList(orderBy));
                marketingEventsBriefResults = marketingEventManager.listMarketingEventsByPager(eieaConverter.enterpriseAccountToId(ea), -10000, paasArg, pageArg);
            }
        }
        if (marketingEventsBriefResults != null && com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(marketingEventsBriefResults.getData())) {
            for (MarketingEventsBriefResult result : marketingEventsBriefResults.getData()) {
                MarketingEventIdTypeDTO dto = new MarketingEventIdTypeDTO();
                dto.setMarketingEventId(result.getId());
                dto.setEventType(result.getEventType());
                dto.setCreateTime(new Date(result.getCreateTime()));
                if(result.getBeginTime() != null){
                    dto.setStartTime(new Date(result.getBeginTime()));
                }
                if(result.getEndTime() != null){
                    dto.setEndTime(new Date(result.getEndTime()));
                }
                dto.setTitle(result.getName());
                if (Objects.nonNull(result.getCover())){
                    dto.setCover(result.getCover());
                }
                visibleMarketingEventList.add(dto);
            }
            pageResult.setTotalCount(marketingEventsBriefResults.getTotalCount());
        }

        if (CollectionUtils.isEmpty(visibleMarketingEventList)){
            return Result.newSuccess(pageResult);
        }

        for (MarketingEventIdTypeDTO dto : visibleMarketingEventList) {
            ListActivityResult miniappActivityResult = new ListActivityResult();
            miniappActivityResult.setMarketingEventId(dto.getMarketingEventId());
            miniappActivityResult.setTitle(dto.getTitle());
            if (dto.getStartTime() != null) {
                miniappActivityResult.setStartTime(dto.getStartTime().getTime());
            }
            if (dto.getEndTime() != null) {
                miniappActivityResult.setEndTime(dto.getEndTime().getTime());
            }
            if (Objects.nonNull(dto.getCover())){
                miniappActivityResult.setCover(dto.getCover());
            }
            miniappActivityResult.setEventType(dto.getEventType());
            marketingEventResultList.add(miniappActivityResult);
        }
        configMarketingEventConferenceData(ea, marketingEventResultList);
        configMarketingEventLiveData(ea, marketingEventResultList);
        configOtherMarketingEventData(ea, marketingEventResultList);

        if (arg.getOrderByIds()!=null && arg.getOrderByIds() && CollectionUtils.isNotEmpty(marketingEventIds)){
            // 指定id值的顺序
            List<String> finalMarketingEventIds = marketingEventIds;
            Map<String, Integer> orderMap = marketingEventIds.stream().collect(Collectors.toMap(id -> id, id -> finalMarketingEventIds.indexOf(id)));
            // 使用Stream进行排序
            marketingEventResultList = marketingEventResultList.stream().sorted(Comparator.comparing(obj -> orderMap.get(obj.getMarketingEventId()))).collect(Collectors.toList());
            // 分页
            marketingEventResultList = marketingEventResultList.stream()
                    .skip((arg.getPageNum() - 1) * arg.getPageSize())
                    .limit(arg.getPageSize())
                    .collect(Collectors.toList());
        }

        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(marketingEventResultList);

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<ListContentResult>> listContentList(String fsEa, ListContentListArg arg) {
        PageResult<ListContentResult> pageResult = new PageResult();
        List<ListContentResult> contentResultList = Lists.newArrayList();
        pageResult.setResult(contentResultList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        String ea = fsEa;
        if (StringUtils.isEmpty(ea)) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        }
        List<ContentCenterDataDTO> contentCenterDataDTOList = null;
        //指定活动查,按照list顺序返回list
        if(arg.getOrderByIds()!=null && arg.getOrderByIds() && CollectionUtils.isNotEmpty(arg.getContentObjectIds()) && arg.getGroupId()==null){
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), false);
            if (arg.getContentObjectType() == ObjectTypeEnum.PRODUCT.getType()){
                contentCenterDataDTOList = productDAO.getListOrderByEaAndIds(ea, arg.getContentObjectIds(),arg.getKeyword(), page);
            }else if (arg.getContentObjectType() ==  ObjectTypeEnum.ARTICLE.getType()){
                contentCenterDataDTOList = articleDAO.getListOrderByEaAndIds(ea, arg.getContentObjectIds(),arg.getKeyword(), page);
            }else if (arg.getContentObjectType() ==  ObjectTypeEnum.HEXAGON_SITE.getType()){
                contentCenterDataDTOList = hexagonSiteDAO.getListOrderByEaAndIds(ea, arg.getContentObjectIds(),arg.getKeyword(), page);
                if (CollectionUtils.isNotEmpty(contentCenterDataDTOList)){
                    Map<String, HexagonSiteListDTO> hexagonSiteListDTOMap = null;
                    List<String> hexagonIds = contentCenterDataDTOList.stream().map(ContentCenterDataDTO::getObjectId).collect(Collectors.toList());
                    List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getCoverBySiteIds(hexagonIds);
                    hexagonSiteListDTOMap = hexagonSiteListDTOList.stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, Function.identity(), (k1, k2)->k2));
                    for (ContentCenterDataDTO contentCenterDataDTO : contentCenterDataDTOList){
                        if (hexagonSiteListDTOMap != null && hexagonSiteListDTOMap.get(contentCenterDataDTO.getObjectId()) != null){
                            contentCenterDataDTO.setDescription(hexagonSiteListDTOMap.get(contentCenterDataDTO.getObjectId()).getShareDesc());
                        }
                    }
                }
            }
            pageResult.setTotalCount(arg.getContentObjectIds().size());
        } else if (arg.getOrderByIds()!=null && arg.getOrderByIds() && arg.getContentObjectIds() != null && arg.getContentObjectIds().size() == 0 && arg.getGroupId()==null) {
            pageResult.setTotalCount(0);
        } else {
            com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), true);
            if (arg.getContentObjectType() == ObjectTypeEnum.PRODUCT.getType()){
                contentCenterDataDTOList = productDAO.getListByEaAndIds(ea, arg.getContentObjectIds(), arg.getGroupId(), page, arg.getKeyword(), arg.getMaterialTagFilter());
            }else if (arg.getContentObjectType() ==  ObjectTypeEnum.ARTICLE.getType()){
                contentCenterDataDTOList = articleDAO.getListByEaAndIds(ea, arg.getContentObjectIds(), arg.getGroupId(), page, arg.getKeyword(), arg.getMaterialTagFilter());
            }else if (arg.getContentObjectType() ==  ObjectTypeEnum.HEXAGON_SITE.getType()){
                if (StringUtils.isNotEmpty(arg.getGroupId())){
                    List<HexagonSiteEntity> pageList = null;
                    if (arg.getGroupId().equals(HexagonDefaultGroupEnum.ALL.getId())) {
                        pageList = hexagonSiteDAO.pageQueryHexagonEntityByAllForAdmin(ea, null, arg.getKeyword(), page);
                    }  else if (arg.getGroupId().equals(HexagonDefaultGroupEnum.NO_GROUP.getId())){
                        pageList = hexagonSiteDAO.pageQueryHexagonEntityByUngrouped(ea,null, arg.getKeyword(), ObjectTypeEnum.HEXAGON_SITE.getType(), null, false, page);
                    }else{
                        pageList= hexagonSiteDAO.pageQueryHexagonEntityByGroupId(ea, arg.getGroupId(), null, arg.getKeyword(), page);
                    }

                    if (CollectionUtils.isNotEmpty(pageList)){
                        contentCenterDataDTOList = Lists.newArrayList();
                        for (HexagonSiteEntity hexagonSiteEntity : pageList){
                            ContentCenterDataDTO dto  = new ContentCenterDataDTO();
                            dto.setCreateTime(hexagonSiteEntity.getCreateTime());
                            dto.setObjectId(hexagonSiteEntity.getId());
                            dto.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                            dto.setTitle(hexagonSiteEntity.getName());
                            dto.setOutDisplayName(hexagonSiteEntity.getOutDisplayName());
                            contentCenterDataDTOList.add(dto);
                        }
                    }
                }else {
                    contentCenterDataDTOList = hexagonSiteDAO.getListByEaAndIds(ea, arg.getContentObjectIds(), arg.getKeyword(),page, arg.getMaterialTagFilter());

                }

                if (CollectionUtils.isNotEmpty(contentCenterDataDTOList)){
                    Map<String, HexagonSiteListDTO> hexagonSiteListDTOMap = null;
                    List<String> hexagonIds = contentCenterDataDTOList.stream().map(ContentCenterDataDTO::getObjectId).collect(Collectors.toList());
                    List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getCoverBySiteIds(hexagonIds);
                    hexagonSiteListDTOMap = hexagonSiteListDTOList.stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, Function.identity(), (k1, k2)->k2));
                    for (ContentCenterDataDTO contentCenterDataDTO : contentCenterDataDTOList){
                        if (hexagonSiteListDTOMap != null && hexagonSiteListDTOMap.get(contentCenterDataDTO.getObjectId()) != null){
                            contentCenterDataDTO.setDescription(hexagonSiteListDTOMap.get(contentCenterDataDTO.getObjectId()).getShareDesc());
                        }
                    }
                }
            }
            pageResult.setTotalCount(page.getTotalNum());
        }
        if (CollectionUtils.isEmpty(contentCenterDataDTOList)){
            return Result.newSuccess(pageResult);
        }
        for (ContentCenterDataDTO contentCenterDataDTO : contentCenterDataDTOList){
            ListContentResult contentResult = new ListContentResult();
            contentResult.setObjectId(contentCenterDataDTO.getObjectId());
            contentResult.setObjectType(contentCenterDataDTO.getObjectType());
            contentResult.setTitle(contentCenterDataDTO.getTitle());
            contentResult.setCreateTime(contentCenterDataDTO.getCreateTime().getTime());
            contentResult.setDescription(contentCenterDataDTO.getDescription());
            if (StringUtils.isNotEmpty(contentCenterDataDTO.getOutDisplayName())){
                contentResult.setTitle(contentCenterDataDTO.getOutDisplayName());
            }
            contentResultList.add(contentResult);
        }

        List<String> objectIds = contentCenterDataDTOList.stream().map(ContentCenterDataDTO::getObjectId).collect(Collectors.toList());
        if (arg.getContentObjectType() == ObjectTypeEnum.PRODUCT.getType()){
            //先取封面图,没有封面取轮播图
            List<PhotoEntity> cPhotoEntityList = photoDAO.listByTargetIdsAndType(objectIds, PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType());
            List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndType(objectIds, PhotoTargetTypeEnum.PRODUCT_HEAD.getType());
            photoManager.resetPhotoListUrl(photoEntityList, ea);
            Map<String, PhotoEntity> cphotoEntityMap = cPhotoEntityList.stream().collect(Collectors.toMap(PhotoEntity::getTargetId, Function.identity(), (v1,v2)->v2));
            Map<String, PhotoEntity> photoEntityMap = photoEntityList.stream().collect(Collectors.toMap(PhotoEntity::getTargetId, Function.identity(), (v1,v2)->v2));
            for (ListContentResult contentResult : contentResultList){
                if (cphotoEntityMap != null && cphotoEntityMap.get(contentResult.getObjectId()) != null){
                    contentResult.setUrl(cphotoEntityMap.get(contentResult.getObjectId()).getUrl());
                    contentResult.setThumbnailUrl(cphotoEntityMap.get(contentResult.getObjectId()).getThumbnailUrl());
                }else {
                    if (photoEntityMap != null && photoEntityMap.get(contentResult.getObjectId()) != null){
                        contentResult.setUrl(photoEntityMap.get(contentResult.getObjectId()).getUrl());
                        contentResult.setThumbnailUrl(photoEntityMap.get(contentResult.getObjectId()).getThumbnailUrl());
                    }
                }
            }
        }else if (arg.getContentObjectType() == ObjectTypeEnum.ARTICLE.getType()){
            List<PhotoEntity> photoEntityList = photoDAO.listByTargetIdsAndType(objectIds, PhotoTargetTypeEnum.ARTICLE_COVER.getType());
            photoManager.resetPhotoListUrl(photoEntityList, ea);
            Map<String, PhotoEntity> photoEntityMap = photoEntityList.stream().collect(Collectors.toMap(PhotoEntity::getTargetId, Function.identity(), (v1,v2)->v2));
            for (ListContentResult contentResult : contentResultList){
                if (photoEntityMap != null && photoEntityMap.get(contentResult.getObjectId()) != null){
                    contentResult.setUrl(photoEntityMap.get(contentResult.getObjectId()).getUrl());
                    contentResult.setThumbnailUrl(photoEntityMap.get(contentResult.getObjectId()).getThumbnailUrl());
                }
            }
        } else if (arg.getContentObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType()){
            List<String> apathList = Lists.newArrayList();
            Map<String, String> coverApathMap = Maps.newHashMap();
            List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getCoverBySiteIds(objectIds);
            if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList)) {
                for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteListDTOList) {
                    apathList.add(hexagonSiteListDTO.getSharePicH5Apath());
                    coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
                }
            }

            Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
            for (ListContentResult contentResult : contentResultList){
                String apath = coverApathMap.get(contentResult.getObjectId());
                if (StringUtils.isNotBlank(apath)) {
                    contentResult.setUrl(apath);
                    contentResult.setThumbnailUrl(coverUrlMap.get(apath));
                }
            }
        }


        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<ListFileListResult>> listFileList(String fsEa, ListFileListArg arg) {
        PageResult<ListFileListResult> pageResult = new PageResult<>();
        List<ListFileListResult> contentResultList = Lists.newArrayList();
        pageResult.setResult(contentResultList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        String ea = fsEa;
        if (StringUtils.isEmpty(ea)) {
            ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        }
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<FileEntityDTO> fileEntityDTOList = null;
        //前端传空数组表示没选返回空
        if(arg.getContentObjectIds() != null && arg.getContentObjectIds().isEmpty()){
            return Result.newSuccess(pageResult);
        }
        List<String> groupIds = Lists.newArrayList();
        if(StringUtils.isNotBlank(arg.getGroupId())){
            if(arg.getGroupId().equals("-1")){
                groupIds.add(arg.getGroupId());
            }else{
                List<ObjectGroupEntity> allSubGroupList = objectGroupManager.getAllSubGroupWithoutPermission(ea, Lists.newArrayList(arg.getGroupId()), ObjectTypeEnum.FILE.getType());
                groupIds = allSubGroupList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
                groupIds.add(arg.getGroupId());
            }
        }
        int totalNumFileList = fileLibraryDAO.getTotalNumFileList(ea, arg.getContentObjectIds(), groupIds, arg.getKeyword(), arg.getMaterialTagFilter());
        pageResult.setTotalCount(totalNumFileList);
        if (totalNumFileList == 0) {
            return Result.newSuccess(pageResult);
        }
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(arg.getPageNum(), arg.getPageSize(), false);
        fileEntityDTOList = fileLibraryDAO.listFileList(ea, arg.getContentObjectIds(), groupIds, page, arg.getKeyword(), arg.getMaterialTagFilter(),arg.isOrderByIds());
        List<ListFileListResult> results = BeanUtil.copy(fileEntityDTOList, ListFileListResult.class);
        pageResult.setResult(results);
        return Result.newSuccess(pageResult);
    }



    private void configMarketingEventConferenceData(String ea, List<ListActivityResult> dataList){
        if (CollectionUtils.isEmpty(dataList)){
            return;
        }

        Map<String, ListActivityResult> enntityMap = dataList.stream().filter(listMiniappActivityResult ->
                listMiniappActivityResult.getEventType().equals(MarketingEventEnum.MEETING_SALES.getEventType())).
                collect(Collectors.toMap(ListActivityResult::getMarketingEventId, v -> v, (v1, v2) -> v1));
        if (enntityMap.isEmpty()){
            return;
        }

        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, new ArrayList<>(enntityMap.keySet()));
        if (CollectionUtils.isEmpty(activityEntityList)){
            return;
        }
        Map<String, ActivityEntity> marketingEventDataMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, Function.identity(), (v1, v2)->v2));
        List<String> conferenceIds = activityEntityList.stream().map(ActivityEntity::getId).collect(Collectors.toList());
        List<PhotoEntity> conferencePhotos = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), conferenceIds);
        Map<String, PhotoEntity> conferencePhotoPathMap = null;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(conferencePhotos)){
            conferencePhotoPathMap = conferencePhotos.stream().collect(Collectors.toMap(PhotoEntity::getTargetId, v->v, (v1,v2)->v1));
        }

        for (ListActivityResult listActivityResult : dataList){
            ActivityEntity activityEntity = marketingEventDataMap.get(listActivityResult.getMarketingEventId());
            if (activityEntity != null) {
                listActivityResult.setObjectId(activityEntity.getId());
                listActivityResult.setHexagonSiteId(activityEntity.getActivityDetailSiteId());
                if (conferencePhotoPathMap != null && conferencePhotoPathMap.get(activityEntity.getId()) != null) {
                    listActivityResult.setUrl(conferencePhotoPathMap.get(activityEntity.getId()).getUrl());
                    listActivityResult.setThumbnailUrl(conferencePhotoPathMap.get(activityEntity.getId()).getThumbnailUrl());
                }
            }
        }
    }

    private void configMarketingEventLiveData(String ea, List<ListActivityResult> dataList){
        if (CollectionUtils.isEmpty(dataList)){
            return;
        }

        Map<String, ListActivityResult> enntityMap = dataList.stream().filter(listMiniappActivityResult ->
                listMiniappActivityResult.getEventType().equals(MarketingEventEnum.LIVE_MARKETING.getEventType())).
                collect(Collectors.toMap(ListActivityResult::getMarketingEventId, v -> v, (v1, v2) -> v1));
        if (enntityMap.isEmpty()){
            return;
        }
        List<MarketingLiveEntity> marketingLiveList = marketingLiveDAO.queryMarketingLiveByMarketingEventIds(eieaConverter.enterpriseAccountToId(ea), new ArrayList<>(enntityMap.keySet()));
        if (CollectionUtils.isEmpty(marketingLiveList)){
            return;
        }

        Map<String, MarketingLiveEntity> liveEntityMap = marketingLiveList.stream().collect(Collectors.toMap(MarketingLiveEntity::getMarketingEventId, v -> v, (v1, v2) -> v1));
        List<String> livePaths = marketingLiveList.stream().filter(liveEntity -> liveEntity.getPlatform() != null && liveEntity.getCover() != null).map(MarketingLiveEntity::getCover).collect(Collectors.toList());
        Map<String, String> pathUrlMap = null;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(livePaths)){
            pathUrlMap = fileV2Manager.batchGetUrlByPath(livePaths, ea, false);
        }

        for (ListActivityResult miniappActivityResult : dataList) {
            MarketingLiveEntity liveEntity = liveEntityMap.get(miniappActivityResult.getMarketingEventId());
            if (liveEntity == null){
                continue;
            }
            if (pathUrlMap != null && liveEntity.getPlatform() != null && pathUrlMap.get(liveEntity.getCover()) != null){
                miniappActivityResult.setUrl(pathUrlMap.get(liveEntity.getCover()));
                miniappActivityResult.setThumbnailUrl(pathUrlMap.get(liveEntity.getCover()));
            }
            if (liveEntity.getPlatform() != null && liveEntity.getPlatform() == LivePlatformEnum.FS_COOPERATION_PLATFORM.getType()){
                miniappActivityResult.setUrl(liveEntity.getCover());
                miniappActivityResult.setThumbnailUrl(liveEntity.getCover());
            }
            miniappActivityResult.setObjectId(liveEntity.getId());
            miniappActivityResult.setHexagonSiteId(liveEntity.getFormHexagonId());
        }
    }

    private void configOtherMarketingEventData(String ea, List<ListActivityResult> dataList){
        if (CollectionUtils.isEmpty(dataList)){
            return;
        }

        Map<String, ListActivityResult> enntityMap = dataList.stream().filter(listMiniappActivityResult ->
                        !Objects.equals(MarketingEventEnum.LIVE_MARKETING.getEventType(), listMiniappActivityResult.getEventType()) && !Objects.equals(MarketingEventEnum.MEETING_SALES.getEventType(), listMiniappActivityResult.getEventType())).
                collect(Collectors.toMap(ListActivityResult::getMarketingEventId, v -> v, (v1, v2) -> v1));
        if (enntityMap.isEmpty()){
            return;
        }
        List<String> nPathList = enntityMap.values().stream().filter(o->CollectionUtils.isNotEmpty(o.getCover())).map(o -> (String)o.getCover().get(0).get("path")).collect(Collectors.toList());
        nPathList.add(defaultCoverPath);
        Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(nPathList, ea, false);

        for (ListActivityResult listActivityResult : dataList){
            ListActivityResult activityEntity = enntityMap.get(listActivityResult.getMarketingEventId());
            if (activityEntity != null) {
                if (CollectionUtils.isNotEmpty(activityEntity.getCover())) {
                    listActivityResult.setUrl(urlMap.get((String)activityEntity.getCover().get(0).get("path")));
                    listActivityResult.setThumbnailUrl(urlMap.get((String)activityEntity.getCover().get(0).get("path")));
                }else {
                    listActivityResult.setUrl(urlMap.get(defaultCoverPath));
                    listActivityResult.setThumbnailUrl(urlMap.get(defaultCoverPath));
                }
            }
        }
    }

    @Override
    public Result<PageResult<QueryMemberContentResult>> listMyActivityList(String ea, ListMyActivityListArg arg) {
        String outerUid = arg.getOuterUid();
        if (StringUtils.isBlank(outerUid)) {
            return Result.newSuccess();
        }
        //查询互联用户的手机号
        String phone = authPartnerManager.getOuterUserPhone(ea, outerUid);
        if (StringUtils.isBlank(phone)) {
            return Result.newSuccess();
        }

        PageResult<QueryMemberContentResult> pageResult = new PageResult<QueryMemberContentResult>();
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setPageNum(arg.getPageNum() > 100 ? 100 : arg.getPageNum());
        pageResult.setTotalCount(0);

        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<CampaignMarketingEventIdTimeDTO> campaignMarketingEventIdTimeDTOs = campaignMergeDataDAO.getPageMarketingEventIdByMember(ea, phone, page);
        if (CollectionUtils.isEmpty(campaignMarketingEventIdTimeDTOs)){
            return Result.newSuccess(pageResult);
        }

        List<String> marketingEventIds = campaignMarketingEventIdTimeDTOs.stream().map(CampaignMarketingEventIdTimeDTO::getMarketingEventId).collect(Collectors.toList());
        Map<String, CampaignMarketingEventIdTimeDTO> marketingEventIdDTOmap = campaignMarketingEventIdTimeDTOs.stream().collect(Collectors.toMap(CampaignMarketingEventIdTimeDTO::getMarketingEventId, v->v, (v1,v2)->v1));
        int totalCount = campaignMergeDataDAO.getMarketingEventIdTotalCountByMember(ea, phone);
        page.setTotalNum(totalCount);
        List<MarketingEventData> marketingEventDataList = marketingEventManager.listMarketingEventData(ea, -10000, marketingEventIds);
        if (CollectionUtils.isEmpty(marketingEventDataList)){
            return Result.newSuccess(pageResult);
        }

        Optional<List<QueryMemberContentResult>> contentResultsOpt = memberManager.queryMemberContentList(ea, marketingEventDataList, null, phone);
        if (contentResultsOpt.isPresent()){
            contentResultsOpt.get().forEach(contentResult ->{
                if (marketingEventIdDTOmap.get(contentResult.getMarketingEventId()) != null){
                    contentResult.setSubmitTime(marketingEventIdDTOmap.get(contentResult.getMarketingEventId()).getCreateTime().getTime());
                }
            });
            Collections.sort(contentResultsOpt.get(), (o1, o2) -> o2.getSubmitTime().compareTo(o1.getSubmitTime()));
        }

        Optional<List<QueryMemberContentResult>> memberConferenceOpt = contentResultsOpt;
        if (!memberConferenceOpt.isPresent()){
            return Result.newSuccess(pageResult);
        }
        pageResult.setResult(memberConferenceOpt.get());
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }
}
