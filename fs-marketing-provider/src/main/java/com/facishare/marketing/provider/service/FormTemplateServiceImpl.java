package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.result.FormTemplateListResult;
import com.facishare.marketing.api.result.QueryFormTemplateDetailResult;
import com.facishare.marketing.api.service.FormTemplateService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.FormTemplateDAO;
import com.facishare.marketing.provider.entity.FormTemplateEntity;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: dzb
 * @Date: 2018/9/20
 * @Description:
 */
@Service("formTemplateService")
@Slf4j
public class FormTemplateServiceImpl implements FormTemplateService {
    @Autowired
    private FormTemplateDAO formTemplateDAO;

    @Override
    public Result<List<FormTemplateListResult>> listFormTemplate(QueryFormTemplateDetailResult vo) {
        List<FormTemplateEntity> list = null;
        list = formTemplateDAO.list(vo.getType());
        if (list == null && list.isEmpty()) {
            return new Result<>(SHErrorCode.FORM_TEMPLATE_NOT_EXIST);
        }
        List<FormTemplateListResult> qlist = Lists.newArrayList();
        list.forEach(entity -> {
            FormTemplateListResult qftd = new FormTemplateListResult();
            qftd.setName(entity.getName());
            qftd.setType(entity.getType());
            qlist.add(qftd);
        });

        return new Result<>(SHErrorCode.SUCCESS, qlist);
    }
}
