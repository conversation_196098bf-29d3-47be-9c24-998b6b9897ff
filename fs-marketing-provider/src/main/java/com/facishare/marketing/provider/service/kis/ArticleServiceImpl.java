package com.facishare.marketing.provider.service.kis;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.marketing.api.arg.ListArticleArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.api.result.QueryArticleDetailResult;
import com.facishare.marketing.api.result.cta.CtaRelationInfo;
import com.facishare.marketing.api.result.kis.KisArticleListResult;
import com.facishare.marketing.api.result.kis.ListArticleResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.kis.ArticleService;
import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.param.article.ArticleQueryParam;
import com.facishare.marketing.provider.dto.ArticleEntityDTO;
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.MaterialTagManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.PictureManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.kis.KisPermissionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("kisArticleService")
@Slf4j
public class ArticleServiceImpl implements ArticleService {
    @Autowired
    private ArticleDAO articleDAO;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;

    @Autowired
    private PictureManager pictureManager;

    @Autowired
    private MaterialTagManager materialTagManager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    MarketingActivityManager marketingActivityManager;

    @Autowired
    private KisPermissionManager kisPermissionManager;

    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private ProductDAO productDAO;

    @Autowired
    @Resource(name = "articleService")
    private com.facishare.marketing.api.service.ArticleService webArticleService;

    @Autowired
    private SpreadTaskService spreadTaskService;

    @Autowired
    private MaterialRelationDao materialRelationDao;

    @Autowired
    private AppMenuTemplateService  appMenuTemplateService;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;


    @Override
    public Result<KisArticleListResult> listArticles(String fsEa, Integer fsUserId, Integer ei, ListArticleArg vo) {
        fsUserId = fsUserId == null ? QywxUserConstants.BASE_VIRTUAL_USER_ID : fsUserId;
        Date time = vo.getTime() == null ? null : new Date(vo.getTime());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        //List<ArticleEntityDTO> articleEntities = articleDAO.pageByEa(fsEa, time, vo.getStatus(), vo.getTitle(), page);
        List<ArticleEntityDTO> articleEntities = null;
        if (StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }

        //分页返回结果
        KisArticleListResult articleListResult = new KisArticleListResult();
        articleListResult.setCurrentPage(page.getPageNo());
        articleListResult.setPageSize(page.getPageSize());
        articleListResult.setTime(vo.getTime());

        String appAdminName = kisPermissionManager.lastestAppAdminName(fsEa, ei);
        articleListResult.setAppAdminName(appAdminName);

        ArticleQueryParam param = new ArticleQueryParam();
        param.setDate(time);
        param.setEa(fsEa);
        param.setStatus(vo.getStatus());
        param.setTitle(vo.getTitle());
        param.setMaterialTagFilter(vo.getMaterialTagFilter());

        Result<AppMenuTagVO>  appMenuTagResult = appMenuTemplateService.getMenuTagRule(fsEa, vo.getMenuId(), ObjectTypeEnum.ARTICLE.getType());
        AppMenuTagVO appMenuTagVO = appMenuTagResult.isSuccess() ? appMenuTagResult.getData() : null;

        if (appMenuTagVO != null) {
            MaterialTagFilterArg materialTagFilterArg = param.getMaterialTagFilter();
            if (materialTagFilterArg == null) {
                materialTagFilterArg = new MaterialTagFilterArg();
            }
            materialTagFilterArg.setMenuType(appMenuTagVO.getTagOperator());
            materialTagFilterArg.setMenuMaterialTagIds(appMenuTagVO.getTagIdList());
            param.setMaterialTagFilter(materialTagFilterArg);
            param.setStrictCheckGroup(true);
            articleEntities = articleDAO.getAccessiblePage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            param.setUserId(fsUserId);
            articleEntities = articleDAO.createByMePage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            articleEntities = articleDAO.noGroupPage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            // 是否需要严格校验分组
            param.setStrictCheckGroup(appMenuTemplateService.needStrictCheckGroup(fsEa, fsUserId, vo.getMenuId()));
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(fsEa, fsUserId, ObjectTypeEnum.ARTICLE.getType(), vo.getMenuId());
            List<String> accessibleGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            param.setUserId(fsUserId);
            param.setPermissionGroupIdList(accessibleGroupIdList);
            articleEntities = articleDAO.getAccessiblePage(param, page);
        } else {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(fsEa, fsUserId, ObjectTypeEnum.ARTICLE.getType(), vo.getMenuId());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            param.setStrictCheckGroup(true);
            param.setUserId(fsUserId);
            if (!permissionGroupIdSet.contains(vo.getGroupId())) {
                articleEntities = Lists.newArrayList();
            } else {
              //  param.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(fsEa, ObjectTypeEnum.ARTICLE.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                param.setGroupIdList(accessibleSubGroupIdList);
                articleEntities = articleDAO.getAccessiblePage(param, page);
            }
        }

        if (CollectionUtils.isEmpty(articleEntities)) {
            articleListResult.setArticleDetailResults(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, articleListResult);
        }

        articleListResult.setRecordSize(page.getTotalNum());
        articleListResult.setPageTotal(page.getTotalPage() / page.getPageSize() + 1);
        articleListResult.setTotalCount(page.getTotalNum());

        //文章列表
        List<ListArticleResult> articleDetailResults = Lists.newArrayList();
        Map<String, Integer> materielMap = Maps.newHashMap();
        List<String> articleIds = Lists.newArrayList();

        for(ArticleEntityDTO articleEntity : articleEntities){
            materielMap.put(articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            articleIds.add(articleEntity.getId());
        }
        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>>  marketingActivityObjectInfoMap
                = marketingActivityManager.getActivityIdsByObject(materielMap, fsEa, fsUserId);
        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleIds);
        Map<String, PhotoEntity> photoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v, (k1, k2) -> k1));
        }

        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(articleIds, ObjectTypeEnum.ARTICLE.getType());

        for (int i = 0; i < articleEntities.size(); i++) {
            ArticleEntityDTO articleEntity = articleEntities.get(i);
            ListArticleResult queryArticleDetailResult = BeanUtil.copy(articleEntity, ListArticleResult.class);
            queryArticleDetailResult.setPhotoUrl(pictureManager.checkArticleImg(queryArticleDetailResult.getPhotoUrl()));
            PhotoEntity photoEntity = photoMap.get(articleEntity.getId());
            if (photoEntity != null) {
                queryArticleDetailResult.setPhotoUrl(photoEntity.getUrl());
                queryArticleDetailResult.setPhotoThumbnailUrl(photoEntity.getThumbnailUrl());
            }

            queryArticleDetailResult.setCreateTimeStamp(null == articleEntity.getCreateTime() ? null : articleEntity.getCreateTime().getTime());
            queryArticleDetailResult.setLastModifyTimeStamp(null == articleEntity.getLastModifyTime() ? null : articleEntity.getLastModifyTime().getTime());
            queryArticleDetailResult.setChoose(articleEntity.isChoose());
            queryArticleDetailResult.setTop(articleEntity.isTop());
            if (null != articleEntity.getFsUserId() && StringUtils.isNotBlank(articleEntity.getFsEa())) {
                queryArticleDetailResult.setBelong(ArticleUserTypeEnum.COMPANY.getType());
            }
            // 设置营销活动
            List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = marketingActivityObjectInfoMap.get(articleEntity.getId());
            if(CollectionUtils.isNotEmpty(objectInfoDTO)){
                String id = objectInfoDTO.get(0).getId();
                Result<Boolean> booleanResult = spreadTaskService.spreadTaskIsRevocation(id);
                if (booleanResult == null || !booleanResult.getData()) {
                    queryArticleDetailResult.setMarketingActivityId(id);
                    queryArticleDetailResult.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                    queryArticleDetailResult.setMarketingActivityCount(objectInfoDTO.size());
                }
            }
            if (StringUtils.isNotBlank(articleEntity.getSummary())) {
                queryArticleDetailResult.setSummary(UnicodeFormatter.decodeUnicodeString(articleEntity.getSummary().replace("\\x0a", " ")));
            }

            if (StringUtils.isNotBlank(articleEntity.getTitle())) {
                queryArticleDetailResult.setTitle(UnicodeFormatter.decodeUnicodeString(articleEntity.getTitle()));
            }

            if(queryArticleDetailResult != null && StringUtils.isNotBlank(queryArticleDetailResult.getId())){
                // 获取裁剪封面图
                PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType(), queryArticleDetailResult.getId());
                if (coverCutMiniAppPhotoEntity != null) {
                    queryArticleDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType(), queryArticleDetailResult.getId());
                if (coverCutH5PhotoEntity != null) {
                    queryArticleDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType(), queryArticleDetailResult.getId());
                if (coverCutOrdinaryPhotoEntity != null) {
                    queryArticleDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                    //返回原图
                    queryArticleDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                }
            }
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(articleEntity.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                queryArticleDetailResult.setMaterialTags(collect);
            }
            articleDetailResults.add(queryArticleDetailResult);
        }
        articleListResult.setArticleDetailResults(articleDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, articleListResult);
    }

    @Override
    public Result<KisArticleListResult> listArticles4Outer(String upstreamEA, String outTenantId, String outUserId, ListArticleArg vo) {
        Date time = vo.getTime() == null ? null : new Date(vo.getTime());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<ArticleEntityDTO> articleEntities = null;
        ArticleQueryParam param = new ArticleQueryParam();
        param.setDate(time);
        param.setEa(upstreamEA);
        param.setStatus(vo.getStatus());
        param.setTitle(vo.getTitle());

        // 获取有权限查看的分组
        List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup4Outer(upstreamEA, outTenantId, outUserId, ObjectTypeEnum.ARTICLE.getType());
        Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
        param.setUserId(null);
        if (Objects.equals(vo.getGroupId(), DefaultObjectGroupEnum.ALL.getId())) {
            param.setGroupIdList(Lists.newArrayList(permissionGroupIdSet));
            articleEntities = articleDAO.getAccessiblePage4Outer(param, page);
        } else {
            if (!permissionGroupIdSet.contains(vo.getGroupId())) {
                articleEntities = Lists.newArrayList();
            } else {
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(upstreamEA, ObjectTypeEnum.ARTICLE.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                param.setGroupIdList(accessibleSubGroupIdList);
                articleEntities = articleDAO.getAccessiblePage4Outer(param, page);
            }
        }

        //分页返回结果
        KisArticleListResult articleListResult = new KisArticleListResult();
        articleListResult.setCurrentPage(page.getPageNo());
        articleListResult.setPageSize(page.getPageSize());
        articleListResult.setRecordSize(page.getTotalNum());
        articleListResult.setPageTotal(page.getTotalPage() / page.getPageSize() + 1);
        articleListResult.setTime(vo.getTime());
        articleListResult.setTotalCount(page.getTotalNum());

        if (CollectionUtils.isEmpty(articleEntities)) {
            articleListResult.setArticleDetailResults(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, articleListResult);
        }
        //文章列表
        List<ListArticleResult> articleDetailResults = Lists.newArrayList();
        Map<String, Integer> materielMap = Maps.newHashMap();
        List<String> articleIds = Lists.newArrayList();

        for(ArticleEntityDTO articleEntity : articleEntities){
            materielMap.put(articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            articleIds.add(articleEntity.getId());
        }

        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleIds);
        Map<String, PhotoEntity> photoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v, (k1, k2) -> k1));
        }

        for (int i = 0; i < articleEntities.size(); i++) {
            ArticleEntityDTO articleEntity = articleEntities.get(i);
            ListArticleResult queryArticleDetailResult = BeanUtil.copy(articleEntity, ListArticleResult.class);
            queryArticleDetailResult.setPhotoUrl(pictureManager.checkArticleImg(queryArticleDetailResult.getPhotoUrl()));
            PhotoEntity photoEntity = photoMap.get(articleEntity.getId());
            if (photoEntity != null) {
                queryArticleDetailResult.setPhotoUrl(photoEntity.getUrl());
                queryArticleDetailResult.setPhotoThumbnailUrl(photoEntity.getThumbnailUrl());
            }

            queryArticleDetailResult.setCreateTimeStamp(null == articleEntity.getCreateTime() ? null : articleEntity.getCreateTime().getTime());
            queryArticleDetailResult.setLastModifyTimeStamp(null == articleEntity.getLastModifyTime() ? null : articleEntity.getLastModifyTime().getTime());
            queryArticleDetailResult.setChoose(articleEntity.isChoose());
            queryArticleDetailResult.setTop(articleEntity.isTop());
            if (null != articleEntity.getFsUserId() && StringUtils.isNotBlank(articleEntity.getFsEa())) {
                queryArticleDetailResult.setBelong(ArticleUserTypeEnum.COMPANY.getType());
            }

            if (StringUtils.isNotBlank(articleEntity.getSummary())) {
                queryArticleDetailResult.setSummary(UnicodeFormatter.decodeUnicodeString(articleEntity.getSummary().replace("\\x0a", " ")));
            }

            if (StringUtils.isNotBlank(articleEntity.getTitle())) {
                queryArticleDetailResult.setTitle(UnicodeFormatter.decodeUnicodeString(articleEntity.getTitle()));
            }

            articleDetailResults.add(queryArticleDetailResult);
        }
        articleListResult.setArticleDetailResults(articleDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, articleListResult);
    }

    @Override
    public Result<QueryArticleDetailResult> queryArticleDetail(String articleId) {
        ArticleEntity articleEntity = articleDAO.getById(articleId);
        if(null == articleEntity){
            log.warn("ArticleServiceImpl.queryArticleDetail articleEntity can't search  articleId:{}", articleId);
            return new Result<>(SHErrorCode.NO_DATA);
        }

        if (articleEntity.getStatus().equals(ArticleStatusEnum.DELETE.getStatus())) {
            log.info("ArticleServiceImpl.queryArticleDetail Article delete articleId:{}", articleId);
            return new Result<>(SHErrorCode.ARTICLE_DELETED);
        }

        QueryArticleDetailResult result = BeanUtil.copy(articleEntity, QueryArticleDetailResult.class);
        PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId(),articleEntity.getFsEa());
        if(null == photoEntity){
            log.warn("ArticleServiceImpl.queryArticleDetail coverPhoto can't search type:{}  id:{}", PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId());
            return new Result<>(SHErrorCode.NO_DATA);
        }
        result.setPhotoThumbnailUrl(photoEntity.getThumbnailUrl());
        result.setPhotoUrl(photoEntity.getUrl());
        PhotoEntity coverPhotoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL.getType(), articleEntity.getId(),articleEntity.getFsEa());
        if (coverPhotoEntity != null) {
            result.setCoverPhotoImgUrl(coverPhotoEntity.getUrl());
        }

        if (StringUtils.isNotBlank(articleEntity.getArticlePath())) {
            byte[] bytes = fileV2Manager.downloadAFile(articleEntity.getArticlePath(), null);
            String content = bytes == null ? "" : new String(bytes);
            result.setContent(content);
            result.setPreArticleContent(articleEntity.getPreArticleContent());
        } else {
            log.warn("ArticleServiceImpl.queryArticleDetail no articlePath  articleEntity:{}", articleEntity);
            return new Result<>(SHErrorCode.NO_DATA);
        }
        if (StringUtils.isNotBlank(articleEntity.getSummary())) {
            result.setSummary(UnicodeFormatter.decodeUnicodeString(articleEntity.getSummary().replace("\\x0a", " ")));
        }
        if (StringUtils.isNotBlank(articleEntity.getTitle())) {
            result.setTitle(UnicodeFormatter.decodeUnicodeString(articleEntity.getTitle()));
        }
        // 查询对应挂接表单
        CustomizeFormDataObjectEntity customizeFormDataObjectEntity =  customizeFormDataObjectDAO.getObjectBindingForm(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
        if(customizeFormDataObjectEntity != null) {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(customizeFormDataObjectEntity.getFormId());
            QueryArticleDetailResult.FormData formData = new QueryArticleDetailResult.FormData();
            formData.setFormId(customizeFormDataObjectEntity.getFormId());
            formData.setFormTitle(customizeFormDataEntity.getFormHeadSetting().getTitle());
            formData.setFormName(customizeFormDataEntity.getFormHeadSetting().getName());
            result.setFormStyleType(customizeFormDataObjectEntity.getFormStyleType());
            result.setFormButtonName(customizeFormDataObjectEntity.getFormButtonName());
            result.setButtonStyle(customizeFormDataObjectEntity.getButtonStyle());
            formData.setFormStyleType(customizeFormDataObjectEntity.getFormStyleType());
            formData.setFormButtonName(customizeFormDataObjectEntity.getFormButtonName());
            formData.setButtonStyle(customizeFormDataObjectEntity.getButtonStyle());
            result.setFormData(formData);
            result.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
        } else {
            // 查询对应挂接微页面   微页面和表单只能挂接一种
            HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if (hexagonSiteObjectEntity != null) {
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteManager.getBindHexagonSiteByObject(hexagonSiteObjectEntity.getSiteId());
                if(hexagonSiteEntity != null) {
                    QueryArticleDetailResult.HexagonSiteData hexagonSiteData = new QueryArticleDetailResult.HexagonSiteData();
                    hexagonSiteData.setSiteId(hexagonSiteEntity.getId());
                    hexagonSiteData.setSiteName(hexagonSiteEntity.getName());
                    result.setFormStyleType(hexagonSiteObjectEntity.getFormStyleType());
                    result.setFormButtonName(hexagonSiteObjectEntity.getFormButtonName());
                    result.setButtonStyle(hexagonSiteObjectEntity.getButtonStyle());
                    result.setHexagonSiteData(hexagonSiteData);
                    result.setBindObjectType(BindObjectType.HEXAGON_SITE.getType());
                }
            } else {
                ActivityBindObjectEntity activityBindObject = articleDAO.getBindObject(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
                if (activityBindObject != null) {
                    ProductEntity productEntity = productDAO.queryProductDetail(activityBindObject.getBindObjectId());
                    if (productEntity != null) {
                        result.setFormButtonName(activityBindObject.getButtonName());
                        result.setFormStyleType(activityBindObject.getStyleType());
                        result.setButtonStyle(activityBindObject.getButtonStyle());
                        result.setBindObjectId(productEntity.getId());
                        result.setBindObjectName(productEntity.getName());
                        result.setBindObjectType(BindObjectType.PRODUCT.getType());
                    }
                }
            }
        }
        result.setCtaRelationInfos(Lists.newArrayList());
        List<CtaRelationEntityDTO> ctaRelationList = ctaRelationDaoManager.getCtaRelationList(articleEntity.getFsEa(), ObjectTypeEnum.ARTICLE.getType(), Lists.newArrayList(articleEntity.getId()));
        if(CollectionUtils.isNotEmpty(ctaRelationList)){
            ctaRelationList.forEach(ctaRelationEntity -> {
                CtaRelationInfo ctaRelationInfo = new CtaRelationInfo();
                ctaRelationInfo.setCtaId(ctaRelationEntity.getCtaId());
                ctaRelationInfo.setCtaName(ctaRelationEntity.getCtaName());
                result.getCtaRelationInfos().add(ctaRelationInfo);
            });
        }
        result.setCreateSourceType(articleEntity.getCreateSourceType());
        MaterialRelationEntity relationEntity = materialRelationDao.queryMaterialRelationByObjectId(articleId, ObjectTypeEnum.ARTICLE.getType());
        if(relationEntity!=null&&relationEntity.getSharePosterAPath()!=null){
            result.setSharePosterAPath(relationEntity.getSharePosterAPath());
            result.setSharePosterUrl(fileV2Manager.getUrlByPath(relationEntity.getSharePosterAPath(),articleEntity.getFsEa(),false));
        }
        if(result != null && StringUtils.isNotBlank(result.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_MINIAPP_COVER.getType(), result.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType(), result.getId());
            if (coverCutH5PhotoEntity != null) {
                result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_ORDINARY_COVER.getType(), result.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<ObjectGroupListResult> listArticleGroup(String fsEa, Integer fsUserId, ListGroupArg listArticleArg) {
        fsUserId = fsUserId == null ? QywxUserConstants.BASE_VIRTUAL_USER_ID : fsUserId;
        Result<ObjectGroupListResult> result = webArticleService.listArticleGroup(fsEa, fsUserId, listArticleArg);
        if (result.isSuccess() && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getObjectGroupList())) {
            List<ListObjectGroupResult> objectGroupList = result.getData().getObjectGroupList().stream()
                    .filter(e -> !DefaultObjectGroupEnum.CREATED_BY_ME.getName().equals(e.getGroupName())).collect(Collectors.toList());
            result.getData().setObjectGroupList(objectGroupList);
        }
        return result;
    }

    @Override
    public Result<ObjectGroupListResult> listArticleGroup4Outer(String upstreamEA, String outTenantId, String outUserId, ListGroupArg arg) {
        return webArticleService.listArticleGroup4Outer(upstreamEA, outTenantId, outUserId, arg);
    }
}


