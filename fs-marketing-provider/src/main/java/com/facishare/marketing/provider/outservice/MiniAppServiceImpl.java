package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.outapi.arg.MiniAppInfoArg;
import com.facishare.marketing.outapi.arg.MiniAppListByEaArg;
import com.facishare.marketing.outapi.result.MiniAppBindDetail;
import com.facishare.marketing.outapi.result.MiniAppInfoResult;
import com.facishare.marketing.outapi.service.MiniAppService;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.MiniappReleaseRecordEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.manager.SettingManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("miniAppService")
public class MiniAppServiceImpl implements MiniAppService {
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;

    @Autowired
    private SettingManager settingManager;

    @Override
    public Result<MiniAppInfoResult> getMiniAppInfo(MiniAppInfoArg arg) {
        EaWechatAccountBindEntity entity = eaWechatAccountBindDao.getByEaAndThirdPlatformId(arg.getEa(), arg.getPlatformId());
        if (entity == null){
            return Result.newError(SHErrorCode.MINIAPP_NOT_EXIST);
        }

        WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(entity.getWxAppId());
        if (wechatAccountConfigEntity == null){
            return Result.newError(SHErrorCode.MINIAPP_NOT_EXIST);
        }
        String componentAppId = wechatThirdPlatformManager.getThirdPlatformAccessToken(arg.getPlatformId());
        String componentAccessToken = wechatThirdPlatformManager.getThirdPlatformAccessToken(arg.getPlatformId());

        MiniAppInfoResult result = new MiniAppInfoResult();
        result.setAppId(wechatAccountConfigEntity.getWxAppId());
        result.setUserName(wechatAccountConfigEntity.getUserName());
        result.setPlatformId(wechatAccountConfigEntity.getThirdPlatformId());
        result.setComponentAppid(componentAppId);
        result.setComponentAccessToken(componentAccessToken);
        result.setNickName(wechatAccountConfigEntity.getNickName());
        result.setHeadImg(wechatAccountConfigEntity.getHeadImg());
        result.setQrCodeUrl(wechatAccountConfigEntity.getQrCodeUrl());

        MiniappReleaseRecordEntity latestReleaseRecord = settingManager.getLatestReleaseAndUpdateAuditing(entity.getWxAppId());
        if (latestReleaseRecord != null) {
            result.setAuditStatus(latestReleaseRecord.getStatus());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<List<MiniAppBindDetail>> getMiniAppListByEa(MiniAppListByEaArg arg) {
        List<EaWechatAccountBindEntity> entities = eaWechatAccountBindDao.getByEa(arg.getEa());
        if (CollectionUtils.isEmpty(entities)){
            return Result.newSuccess();
        }

        List<MiniAppBindDetail> bindDetailList = Lists.newArrayList();
        for (EaWechatAccountBindEntity entity : entities){
            WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(entity.getWxAppId());
            if (wechatAccountConfigEntity == null){
                continue;
            }
            MiniAppBindDetail bindDetail = new MiniAppBindDetail();
            bindDetail.setAppId(wechatAccountConfigEntity.getWxAppId());
            bindDetail.setPlatformId(wechatAccountConfigEntity.getThirdPlatformId());
            bindDetail.setNickName(wechatAccountConfigEntity.getNickName());
            bindDetail.setHeadImg(wechatAccountConfigEntity.getHeadImg());
            bindDetail.setQrCode(wechatAccountConfigEntity.getQrCodeUrl());
            bindDetail.setCertificationStatus(wechatAccountConfigEntity.getVerifyTypeInfo());
            bindDetailList.add(bindDetail);
        }

        return Result.newSuccess(bindDetailList);
    }
}
