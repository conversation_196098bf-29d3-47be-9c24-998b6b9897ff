package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.businessCardHolder.BusinessCardHolderStatusEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.BusinessCardHolderDAO;
import com.facishare.marketing.provider.entity.BusinessCardHolderEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created  By zhoux 2020/12/22
 **/
@Component
@Slf4j
public class BusinessCardHolderManager {

    @Autowired
    private BusinessCardHolderDAO businessCardHolderDAO;

    public void addOrUpdateCardHolder(String uid, String friendUid, Integer type) {
        BusinessCardHolderEntity businessCardHolderEntity = new BusinessCardHolderEntity();
        businessCardHolderEntity.setId(UUIDUtil.getUUID());
        businessCardHolderEntity.setUid(uid);
        businessCardHolderEntity.setFriendUid(friendUid);
        businessCardHolderEntity.setType(type);
        businessCardHolderEntity.setStatus(BusinessCardHolderStatusEnum.NORMAL.getStatus());
        businessCardHolderDAO.upsertBusinessCardHolder(businessCardHolderEntity);
    }



}
