package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.dto.TargetPhotoPathDTO;
import com.facishare.marketing.provider.entity.PhotoEntity;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * Created by jason on 2018/4/17.
 */
public interface PhotoDAO {
    @Select("SELECT * FROM photo WHERE (target_type = 2 OR target_type = 3 OR target_type = 6 OR target_type = 22) AND target_id = #{targetId} ORDER BY seq_num")
    List<PhotoEntity> listProductPhotos(@Param("targetId") String targetId);

    @Delete("<script>" + "DELETE FROM photo WHERE id in\n" + "    <foreach collection=\"ids\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" + "      #{item}\n"
        + "    </foreach>" + "</script>")
    boolean deletePhotos(@Param("ids") List<String> ids);

    @Delete("<script>" + "DELETE FROM photo WHERE target_id = #{targetId} and target_type = #{targetType}" + " </script>")
    boolean deletePhotosBytargetIdAndType(@Param("targetId") String targetId, @Param("targetType") int targetType);

    @Delete("<script>" + "DELETE FROM photo WHERE target_id = #{targetId} and target_type in\n" + "    <foreach collection=\"targetTypes\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" + "      #{item}\n"
            + "    </foreach>" + "</script>")
    boolean deletePhotosBytargetIdAndTypes(@Param("targetId") String targetId, @Param("targetTypes") List<Integer> targetTypes);

    @Insert("<script>" + "INSERT INTO photo(\"id\",\"target_type\",\"target_id\",\"thumbnail_url\",\"url\",\"create_time\",\"path\", \"seq_num\", \"update_time\", \"thumbnail_path\") VALUES\n"
        + "    <foreach collection=\"entities\" item=\"entity\" index=\"index\" separator=\",\">\n"
        + "      (#{entity.id}, #{entity.targetType}, #{entity.targetId}, #{entity.thumbnailUrl}, #{entity.url}, now(), #{entity.path}, #{entity.seqNum}, now(), #{entity.thumbnailPath})\n" + "    </foreach>" + "</script>")
    void batchInsert(@Param("entities") Collection<PhotoEntity> entities);

    @Select("<script>" + "SELECT *\n" + "    FROM photo WHERE (target_type = 2 OR target_type = 3 OR target_type = 6 OR target_type = 22) AND target_id IN\n" + "    <foreach collection=\"productIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
        + "      #{item}\n" + "    </foreach>" + " ORDER BY seq_num" + "</script>")
    List<PhotoEntity> listByProductIds(@Param("productIds") List<String> productIds);

    @Update("UPDATE photo SET seq_num = #{seqNum}, update_time = now() WHERE id = #{id}")
    int updatePhotoSequenceNumber(@Param("id") String id, @Param("seqNum") int seqNum);

    @Select("select * from photo where target_id = #{targetId}")
    List<PhotoEntity> listByTargeId(@Param("targetId") String targetId);

    @Select("select * from photo where target_Id = #{targetId} and target_type = #{targetType} ORDER BY seq_num ")
    List<PhotoEntity> listByTargetIdsAndTargetType(@Param("targetId") String targetId, @Param("targetType") int targetType);

    @Select("select * from photo where target_Id = #{targetId} and target_type = #{targetType} and url = #{url} ORDER BY seq_num ")
    List<PhotoEntity> listByTargetIdsAndTargetTypeAndUrl(@Param("targetId") String targetId, @Param("targetType") int targetType, @Param("url") String url);

    @Select("select * from photo where id = #{id} ")
    PhotoEntity getById(@Param("id") String id);

    @Select("<script>"
        + "SELECT * FROM photo "
        + "WHERE "
        + "<if test=\"targetType != null\">\n"
        + "target_type = #{targetType} AND \n"
        + "</if>\n"
        + "target_id in "
        + "    <foreach collection=\"targetIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
        + "      #{item}\n"
        + "    </foreach>"
        + "ORDER BY target_id, target_type, seq_num"
        + "</script>")
    List<PhotoEntity> listByTargetIdsAndType(@Param("targetIds") List<String> targetIds, @Param("targetType") Integer targetType);

    @Update("INSERT INTO photo(\n" +
            "    \"id\",\n" +
            "    \"target_type\",\n" +
            "    \"target_id\",\n" +
            "    \"thumbnail_url\",\n" +
            "    \"url\",\n" +
            "    \"create_time\",\n" +
            "    \"path\",\n" +
            "    \"seq_num\",\n" +
            "    \"thumbnail_path\",\n" +
            "    \"update_time\"\n" +
            "    )\n" +
            "    VALUES (\n" +
            "    #{id},\n" +
            "    #{targetType},\n" +
            "    #{targetId},\n" +
            "    #{thumbnailUrl},\n" +
            "    #{url},\n" +
            "    now(),\n" +
            "    #{path},\n" +
            "    #{seqNum},\n" +
            "    #{thumbnailPath},\n" +
            "    now()\n" +
            "    )")
    boolean addPhoto(PhotoEntity photoEntity);

    @Update("<script>" + "UPDATE photo\n" +
            "    <set>\n" +
            "      <if test=\"url != null\">\n" +
            "        \"url\" = #{url},\n" +
            "      </if>\n" +
            "      <if test=\"thumbnailUrl != null\">\n" +
            "        \"thumbnail_url\" = #{thumbnailUrl},\n" +
            "      </if>\n" +
            "      <if test=\"path != null\">\n" +
            "        \"path\" = #{path},\n" +
            "      </if>\n" +
            "      <if test=\"thumbnailPath != null\">\n" +
            "        \"thumbnail_path\" = #{thumbnailPath},\n" +
            "      </if>\n" +
            "      <if test=\"seqNum != null\">\n" +
            "        \"seq_num\" = #{seqNum},\n" +
            "      </if>\n" +
            "       update_time = now()" +
            "    </set>\n" +
            "    WHERE id = #{id}" + "</script>")
    boolean updatePhoto(PhotoEntity photoEntity);

    @Select("<script>"
        + "select * from photo where target_id in "
        + "    <foreach collection=\"targetIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
        + "      #{item}\n"
        + "    </foreach>"
        + " ORDER BY seq_num "
        + "</script>")
    List<PhotoEntity> listByTargeIds(@Param("targetIds") List<String> targetIds);

    @Select("<script>"
            + "SELECT target_id AS targetId, target_type AS targetType, path AS path, thumbnail_path AS thumbnailPath FROM photo "
            + "WHERE target_type=#{targetType} AND target_id in "
            + "    <foreach collection=\"targetIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
            + "      #{item}\n"
            + "    </foreach>"
            + "</script>")
    List<TargetPhotoPathDTO> listPathByTargetTypeAndIds(@Param("targetIds") List<String> targetIds, @Param("targetType") Integer targetType);

    @Select("<script>"
            + "SELECT *\n" +
            "    FROM photo WHERE target_type = #{targetType} AND target_id IN\n" +
            "    <foreach collection=\"targetIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{item}\n" +
            "    </foreach>"
            + "</script>")
    List<PhotoEntity> queryPhotosByTypeAndTargetIds(@Param("targetType") int targetType, @Param("targetIds") List<String> targetIds);

    @Select("<script>"
            + "SELECT *\n" +
            "    FROM photo WHERE target_id = #{targetId} AND target_type IN\n" +
            "    <foreach collection=\"targetTypes\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{item}\n" +
            "    </foreach>"
            + "</script>")
    List<PhotoEntity> queryPhotosByTypesAndTargetId(@Param("targetTypes") List<Integer> targetTypes, @Param("targetId") String targetId);

    @Select("<script>"
            + "SELECT *\n" +
            "    FROM photo WHERE target_id IN\n" +
            "    <foreach collection=\"targetIds\" index=\"index\" item=\"item2\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{item2}\n" +
            "    </foreach>\n" +
            "    AND target_type IN\n" +
            "    <foreach collection=\"targetTypes\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{item}\n" +
            "    </foreach>"
            + "</script>")
    List<PhotoEntity> queryPhotosByTypesAndIds(@Param("targetTypes") List<Integer> targetTypes, @Param("targetIds") List<String> targetIds);

    @Select("<script>"
            + " select * from photo where target_id in ("+
            "  select content from notice where fs_ea = #{ea} "+
            "  union "+
            "  select id from activity where ea= #{ea} "+
            "  union "+
            "  select id from hexagon_page where ea = #{ea} "+
            "  union "+
            "  select id from product where fs_ea = #{ea} "+
            "  union "+
            "  select id from article where fs_ea = #{ea} "+
            " ) and url like '%a2.fspage.com%' "+
            "</script>")
    List<PhotoEntity> queryCpathUrlList(@Param("ea")String ea);
}
