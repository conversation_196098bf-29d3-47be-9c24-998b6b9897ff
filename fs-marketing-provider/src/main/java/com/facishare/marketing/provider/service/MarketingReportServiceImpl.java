/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.sensors.AddSensorsDataArg;
import com.facishare.mankeep.api.outService.service.OutSensorsService;
import com.facishare.mankeep.api.result.RadarListUnitResult;
import com.facishare.mankeep.api.service.CustomerService;
import com.facishare.mankeep.api.service.IndexService;
import com.facishare.mankeep.api.vo.RadarsVO;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.mankeep.common.enums.ObjectTypeEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.result.PageObject;
import com.facishare.mankeep.common.typehandlers.value.ActionVO;
import com.facishare.marketing.api.arg.ListBriefMarketingEventsArg;
import com.facishare.marketing.api.arg.ListMarketingActivityEnterpriseEmployeeRankingArg;
import com.facishare.marketing.api.arg.ListMarketingActivityRankingArg;
import com.facishare.marketing.api.arg.MarketingReportArg;
import com.facishare.marketing.api.arg.kis.GetActivityRankingArg;
import com.facishare.marketing.api.arg.kis.GetEmployeeRankingArg;
import com.facishare.marketing.api.arg.kis.GetEnterpriseStatisticSumUpArg;
import com.facishare.marketing.api.arg.kis.GetEnterpriseStatisticTrendArg;
import com.facishare.marketing.api.arg.kis.GetRadarsInfoArg;
import com.facishare.marketing.api.data.MarketingReportAddCountData;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.kis.GetActivityRankingResult;
import com.facishare.marketing.api.result.kis.GetEmployeeRankingResult;
import com.facishare.marketing.api.result.kis.GetEnterpriseStatisticSumUpResult;
import com.facishare.marketing.api.result.kis.GetEnterpriseStatisticTrendResult;
import com.facishare.marketing.api.result.kis.GetRadarInfoResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryGroupMemberListResult;
import com.facishare.marketing.api.service.MarketingReportService;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.service.qywx.QYWXCustomerGroupService;
import com.facishare.marketing.api.vo.qywx.customerGroup.QueryGroupMemberListVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.AssociateIdTypeEnum;
import com.facishare.marketing.common.enums.MarketingSceneEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.kis.EnterpriseStatisticSearchTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingEnterpriseDayStatisticDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.entity.MarketingUserGroupCustomizeObjectMappingEntity;
import com.facishare.marketing.provider.entity.UserEntity;
import com.facishare.marketing.provider.entity.kis.GetActivityRankingEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeDayStatisticEntity;
import com.facishare.marketing.provider.entity.kis.MarketingEnterpriseDayStatisticEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.statistic.outapi.arg.CountMarketingSceneArg;
import com.facishare.marketing.statistic.outapi.result.CountMarketingSceneResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2019/02/21
 **/
@Slf4j
@Service("marketingReportService")
public class MarketingReportServiceImpl implements MarketingReportService {
    @Autowired
    private MarketingEnterpriseDayStatisticDAO marketingEnterpriseDayStatisticDAO;
    @Autowired
    private MarketingActivityEmployeeDayStatisticDAO marketingActivityEmployeeDayStatisticDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;
    @Autowired
    private MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;
    @Autowired
    private IndexService indexService;
    @Autowired
    private OutSensorsService sensorsService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QYWXContactService qywxContactService;
    @Autowired
    private QYWXCustomerGroupService qywxCustomerGroupService;
    @Autowired
    private UserManager userManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private ObjectDescribeService objectDescribeService;

    @ReloadableProperty("qywx.default.avatar")
    private String qywxDefaultAvatar;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;
    @ReloadableProperty("marketing_top_case_example")
    private String TopCaseExampleList;
    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao marketingUserGroupCustomizeObjectMappingDao;
    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;
    @Autowired
    private ObjectDao objectDao;

    @ReloadableProperty("marketing_report_send_ea")
    private String marketingReportSendEaList;
    private Gson gs = new Gson();
    @Override
    public Result<GetEnterpriseStatisticSumUpResult> getEnterpriseStatisticSumUp(GetEnterpriseStatisticSumUpArg vo) {
        GetEnterpriseStatisticSumUpResult result = new GetEnterpriseStatisticSumUpResult();
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(vo.getRecentDay() - 1);
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(vo.getEa());
        List<Integer> dataPermission = null;
        if (isOpen) {
            try {
                result.setEnterpriseCount(0);
                result.setSaveToCrmLeadCount(0);
                result.setMarketingEventCount(0);
                dataPermission = dataPermissionManager.getDataPermission(vo.getEa(), vo.getFsUserId());
                if(CollectionUtils.isEmpty(dataPermission)){
                    return Result.newSuccess();
                }
                // 营销活动
                List<String> activityIds = marketingEnterpriseDayStatisticDAO.getActivityIdByEaAndDateSpan(vo.getEa(), startDate.toDate(), endDate.toDate());
                if(CollectionUtils.isNotEmpty(activityIds)){
                    PaasQueryFilterArg activityQueryFilterArg = new PaasQueryFilterArg();
                    PaasQueryArg activityPaasQueryArg = new PaasQueryArg(0, 1);
                    activityPaasQueryArg.addFilter(CrmV2LeadFieldEnum.ID.getNewFieldName(), OperatorConstants.IN, activityIds);
                    activityPaasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                    activityQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                    activityQueryFilterArg.setQuery(activityPaasQueryArg);
                    int enterpriseCount = crmV2Manager.countCrmObjectByFilterV3(vo.getEa(), vo.getFsUserId(), activityQueryFilterArg);
                    result.setEnterpriseCount(enterpriseCount);
                }

                // 获取线索数从表单统计
                List<String> clueIds = customizeFormDataUserDAO.getClueIdByEaWithMarketingActivity(vo.getEa(), startDate.toDate(), endDate.toDate(), true);
                if(CollectionUtils.isNotEmpty(clueIds)){
                    PaasQueryFilterArg clueQueryFilterArg = new PaasQueryFilterArg();
                    PaasQueryArg cluePaasQueryArg = new PaasQueryArg(0, 1);
                    cluePaasQueryArg.addFilter(CrmV2LeadFieldEnum.ID.getNewFieldName(), OperatorConstants.IN, clueIds);
                    cluePaasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                    clueQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                    clueQueryFilterArg.setQuery(cluePaasQueryArg);
                    int leadCount = crmV2Manager.countCrmObjectByFilterV3(vo.getEa(), vo.getFsUserId(), clueQueryFilterArg);
                    result.setSaveToCrmLeadCount(leadCount);
                }

                // 统计市场活动数
                PaasQueryFilterArg evenQueryFilterArg = new PaasQueryFilterArg();
                PaasQueryArg evenPassQueryFilterArg = new PaasQueryArg(0, 1);
                evenPassQueryFilterArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
//                evenPassQueryFilterArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.GTE,Lists.newArrayList(String.valueOf(startDate.toDate().getTime())));
//                evenPassQueryFilterArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.LTE,Lists.newArrayList(String.valueOf(endDate.toDate().getTime())));
                evenPassQueryFilterArg.addFilter(MarketingEventFieldContants.END_TIME, OperatorConstants.GTE, Lists.newArrayList(String.valueOf(startDate.toDate().getTime())));
                evenPassQueryFilterArg.addFilter(MarketingEventFieldContants.BEGIN_TIME, OperatorConstants.LTE, Lists.newArrayList(String.valueOf(endDate.toDate().getTime())));
                evenQueryFilterArg.setQuery(evenPassQueryFilterArg);
                evenQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                int marketingEventCount = crmV2Manager.countCrmObjectByFilterV3(vo.getEa(), vo.getFsUserId(), evenQueryFilterArg);
//                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(vo.getEa(), vo.getFsUserId(), evenQueryFilterArg, 1, 50);

                result.setMarketingEventCount(marketingEventCount);
            } catch (Exception e) {
                log.warn("MarketingReportServiceImpl.getEnterpriseStatisticSumUp listMarketingEvents error vo:{}, e:{}", vo, e);
            }
        }else {
            List<MarketingEnterpriseDayStatisticEntity> entities = marketingEnterpriseDayStatisticDAO.listByEaAndDateSpan(vo.getEa(), startDate.toDate(), endDate.toDate());
            for (MarketingEnterpriseDayStatisticEntity entity : entities) {
                // 暂时将人数换为次数
                result.setLookUpCount(result.getLookUpCount() + entity.getLookUpCount());
                result.setForwardCount(result.getForwardCount() + entity.getForwardCount());
            }
            result.setSpreadEmployeeCount(marketingActivityEmployeeDayStatisticDAO.countSpreadEmployee(vo.getEa(), startDate.toDate(), endDate.toDate()));
            result.setEnterpriseCount(marketingEnterpriseDayStatisticDAO.countByEaAndDateSpan(vo.getEa(), startDate.toDate(), endDate.toDate()));
            // 获取线索数从表单统计
            result.setSaveToCrmLeadCount(customizeFormClueManager.countClueByEaWithMarketingActivity(vo.getEa(), true, vo.getRecentDay()));
            // 统计市场活动数
            ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
            arg.setBegin(startDate.toDate().getTime());
            arg.setEnd(endDate.toDate().getTime());
            try {
                result.setMarketingEventCount(marketingEventManager.getMarketingEventsTotalCount(eieaConverter.enterpriseAccountToId(vo.getEa()), -10000,arg));
            } catch (Exception e) {
                log.warn("MarketingReportServiceImpl.getEnterpriseStatisticSumUp listMarketingEvents error vo:{}, e:{}", vo, e);
                result.setMarketingEventCount(0);
            }
        }
        ThreadPoolUtils.execute(() -> {
            addSensorsData(vo);
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    private void addSensorsData(GetEnterpriseStatisticSumUpArg vo) {
        try {
            AddSensorsDataArg sensorsDataArg = new AddSensorsDataArg();
            List<ActionVO> vos = Lists.newArrayList();
            ActionVO actionVO = new ActionVO();
            actionVO.setId(UUIDUtil.getUUID());
            actionVO.setActionType(ActionTypeEnum.APP_MARKETING_RECORD.getAction());
            actionVO.setObjectType(ObjectTypeEnum.APP_MARKETING.getType());
            actionVO.setObjectName(ObjectTypeEnum.APP_MARKETING.name());
            actionVO.setFsEa(vo.getEa());
            actionVO.setUserId(vo.getFsUserId());
            actionVO.setActionTime(new Date());
            vos.add(actionVO);
            sensorsDataArg.setVos(vos);
            sensorsService.addSensorsData(sensorsDataArg);
        } catch (Exception e) {
            log.info("MarketingReportServiceImpl getEnterpriseStatisticSumUp sensorsService addSensorsData, exception: {}", e.fillInStackTrace());
        }
    }

    @Override
    public Result<GetEnterpriseStatisticTrendResult> getEnterpriseStatisticTrend(GetEnterpriseStatisticTrendArg vo) {
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(vo.getRecentDay() - 1);
        List<MarketingEnterpriseDayStatisticEntity> entities = marketingEnterpriseDayStatisticDAO.listByEaAndDateSpan(vo.getEa(), startDate.toDate(), endDate.toDate());
        GetEnterpriseStatisticTrendResult result = this.buildEnterpriseStatisticTrendResult(entities, startDate, endDate);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetEmployeeRankingResult> getEmployeeRanking(GetEmployeeRankingArg vo) {
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(vo.getRecentDay() - 1);
        GetEmployeeRankingResult result = new GetEmployeeRankingResult();
        List<MarketingActivityEmployeeDayStatisticEntity> dataTop = Lists.newArrayList();
        if (vo.getSearchType().equals(EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue())) {
            // 获取线索数从表单统计
            Map<Integer, Integer> statisticResultMap = customizeFormClueManager.countFsUserIdClueNumByEa(vo.getEa(), true, vo.getRecentDay(), vo.getTopRankingCount());
            for (Map.Entry<Integer, Integer> entry : statisticResultMap.entrySet()) {
                MarketingActivityEmployeeDayStatisticEntity marketingActivityEmployeeDayStatisticEntity = new MarketingActivityEmployeeDayStatisticEntity();
                marketingActivityEmployeeDayStatisticEntity.setFsUserId(entry.getKey());
                marketingActivityEmployeeDayStatisticEntity.setLeadIncrementCount(entry.getValue());
                dataTop.add(marketingActivityEmployeeDayStatisticEntity);
            }
        } else {
            dataTop = marketingActivityEmployeeDayStatisticDAO
                .orderByCountType(vo.getEa(), startDate.toDate(), endDate.toDate(), vo.getSearchType(), vo.getTopRankingCount());
        }
        List<Integer> fsUserIds = dataTop.stream().filter(Objects::nonNull).map(MarketingActivityEmployeeDayStatisticEntity::getFsUserId).collect(Collectors.toList());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsUserMap = fsAddressBookManager.getEmployeeInfoByUserIds(vo.getEa(), fsUserIds, true);
        List<EmployeeRankingData> resultList = dataTop.stream().map(e -> {
            EmployeeRankingData employeeRankingData = new EmployeeRankingData();
            employeeRankingData.setFsUserId(e.getFsUserId());
            if (EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue().equals(vo.getSearchType())) {
                employeeRankingData.setCount(e.getLeadIncrementCount());
            } else if (EnterpriseStatisticSearchTypeEnum.NUMBER_OF_PROMOTION.getValue().equals(vo.getSearchType())) {
                employeeRankingData.setCount(e.getSpreadCount());
            }
            FsAddressBookManager.FSEmployeeMsg msg = fsUserMap.get(e.getFsUserId());
            if (msg != null) {
                employeeRankingData.setDepartment(msg.getDepartment());
                employeeRankingData.setEmployeeName(msg.getName());
            }
            return employeeRankingData;
        }).filter(data -> data.getCount() != 0).sorted((v1, v2) -> v2.getCount().compareTo(v1.getCount())).collect(Collectors.toList());
        result.setDataList(resultList);
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<ListMarketingActivityEnterpriseEmployeeRankingResult>> listMarketingActivityEnterpriseEmployeeRanking(String ea, Integer fsUserId,
        ListMarketingActivityEnterpriseEmployeeRankingArg arg) {
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<ListMarketingActivityEnterpriseEmployeeRankingResult> listMarketingActivityEnterpriseEmployeeRankingResults = Lists.newArrayList();
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(arg.getRecentDay() - 1);
        List<MarketingActivityEmployeeDayStatisticEntity> marketingActivityEmployeeDayStatisticEntityList = null;
        List<Integer> dataPermission = Lists.newArrayList();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        if (arg.getSearchType().equals(EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue())) {
            List<String> activityIds = null;
            List<Integer> employeeIds = null;
            if (isOpen) {
                dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
                if(CollectionUtils.isEmpty(dataPermission)){
                    return Result.newSuccess();
                }
                PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
                queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.GTE,Lists.newArrayList(String.valueOf(startDate.toDate().getTime())));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.LTE,Lists.newArrayList(String.valueOf(endDate.toDate().getTime())));
                queryFilterArg.setQuery(paasQueryArg);
                List<String> selectFields = Lists.newArrayList("_id");
                queryFilterArg.setSelectFields(selectFields);
                int objectCount = crmV2Manager.countCrmObjectByFilterV3(ea, fsUserId, queryFilterArg);
                if(objectCount == 0){
                    return Result.newSuccess();
                }
                InnerPage<ObjectData> objectResult = crmV2Manager.listCrmObjectByFilterV3(ea, fsUserId, queryFilterArg,objectCount);
                activityIds = objectResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
               // employeeIds = qywxManager.handleEmployeeUserIdWithOpenDataPermission(ea, fsUserId, null, dataPermission);
                employeeIds = qywxManager.getCurrentAccountAccessibleFsUserIds(ea, fsUserId);
            }
            List<CustomizeFormClueNumDTO> clueNumDTOS = customizeFormClueManager
                .countFsUidClueNumByEaAndType(ea, true, arg.getRecentDay(), 1, arg.getPageSize(), (arg.getPageNum() - 1) * arg.getPageSize(),activityIds,employeeIds);
            if (CollectionUtils.isNotEmpty(clueNumDTOS)) {
                marketingActivityEmployeeDayStatisticEntityList = Lists.newArrayList();
                for (CustomizeFormClueNumDTO customizeFormClueNumDTO : clueNumDTOS) {
                    MarketingActivityEmployeeDayStatisticEntity statisticEntity = new MarketingActivityEmployeeDayStatisticEntity();
                    statisticEntity.setFsUserId(customizeFormClueNumDTO.getFsUid());
                    statisticEntity.setLeadIncrementCount(customizeFormClueNumDTO.getCount());
                    marketingActivityEmployeeDayStatisticEntityList.add(statisticEntity);
                }
            }
        } else {
            if (isOpen) {
                dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
                if(CollectionUtils.isEmpty(dataPermission)){
                    return Result.newSuccess();
                }
                PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
                queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.GTE,Lists.newArrayList(String.valueOf(startDate.toDate().getTime())));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.LTE,Lists.newArrayList(String.valueOf(endDate.toDate().getTime())));
                queryFilterArg.setQuery(paasQueryArg);
                List<String> selectFields = Lists.newArrayList("_id");
                queryFilterArg.setSelectFields(selectFields);
                int objectCount = crmV2Manager.countCrmObjectByFilterV3(ea, fsUserId, queryFilterArg);
                if(objectCount == 0){
                    return Result.newSuccess();
                }
                InnerPage<ObjectData> objectResult = crmV2Manager.listCrmObjectByFilterV3(ea, fsUserId, queryFilterArg,objectCount);
                List<String> activityIds = objectResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
              //  List<Integer> employeeIds = qywxManager.handleEmployeeUserIdWithOpenDataPermission(ea, fsUserId, null, dataPermission);
                List<Integer> employeeIds = qywxManager.getCurrentAccountAccessibleFsUserIds(ea, fsUserId);
                marketingActivityEmployeeDayStatisticEntityList = marketingActivityEmployeeDayStatisticDAO.listMarketingEmployeeActivityDataPermissionEmployeeRanking(ea, startDate.toDate(), endDate.toDate(), arg.getSearchType(), activityIds, employeeIds,page);
            }else {
                marketingActivityEmployeeDayStatisticEntityList = marketingActivityEmployeeDayStatisticDAO
                        .listMarketingEmployeeActivityEmployeeRanking(ea, startDate.toDate(), endDate.toDate(), arg.getSearchType(), page);
            }
        }

        if (CollectionUtils.isNotEmpty(marketingActivityEmployeeDayStatisticEntityList)) {
            List<Integer> leadFsUserIds = marketingActivityEmployeeDayStatisticEntityList.stream().filter(Objects::nonNull).map(MarketingActivityEmployeeDayStatisticEntity::getFsUserId)
                .collect(Collectors.toList());
            Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, leadFsUserIds, true);
            List<String> npaths = Lists.newArrayList();
            List<FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgList = Lists.newArrayList(fsEmployeeMsgMap.values());
            fsEmployeeMsgList.forEach(data -> {
                if (data.getProfileImage() != null && (data.getProfileImage().startsWith("N_") || data.getProfileImage().startsWith("A_"))) {
                    npaths.add(data.getProfileImage());
                }
            });
            Map<String, String> pathMap = fileV2Manager.batchGetUrlByPath(npaths, ea, false);
            Map<Integer, String> outUIdToNameMap = fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(ea,leadFsUserIds);
            marketingActivityEmployeeDayStatisticEntityList.forEach(entity -> {
                ListMarketingActivityEnterpriseEmployeeRankingResult employeeRankingResult = new ListMarketingActivityEnterpriseEmployeeRankingResult();
                employeeRankingResult.setFsUserId(entity.getFsUserId());
                if (EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue().equals(arg.getSearchType())) {
                    employeeRankingResult.setCount(entity.getLeadIncrementCount());
                }else if(EnterpriseStatisticSearchTypeEnum.NUMBER_OF_VISITS.getValue().equals(arg.getSearchType())){
                    employeeRankingResult.setCount(entity.getLookUpCount());
                } else {
                    employeeRankingResult.setCount(entity.getSpreadCount());
                }
                employeeRankingResult.setEmployeeName(fsEmployeeMsgMap.get(entity.getFsUserId()) == null ? null : fsEmployeeMsgMap.get(entity.getFsUserId()).getName());
                if (fsEmployeeMsgMap.get(entity.getFsUserId()) != null && fsEmployeeMsgMap.get(entity.getFsUserId()).getProfileImage() != null) {
                    if (pathMap != null && (fsEmployeeMsgMap.get(entity.getFsUserId()).getProfileImage().startsWith("N_") || fsEmployeeMsgMap.get(entity.getFsUserId()).getProfileImage().startsWith("A_"))) {
                        employeeRankingResult.setAvatar(pathMap.get(fsEmployeeMsgMap.get(entity.getFsUserId()).getProfileImage()));
                    } else {
                        employeeRankingResult.setAvatar(fsEmployeeMsgMap.get(entity.getFsUserId()).getProfileImage());
                    }
                } else {
                    String defaultAvatar = fileV2Manager.getUrlByPath(qywxDefaultAvatar, null, false);
                    employeeRankingResult.setAvatar(defaultAvatar);
                }
                if (StringUtils.isEmpty(employeeRankingResult.getEmployeeName())) {
                    employeeRankingResult.setEmployeeName(outUIdToNameMap.get(entity.getFsUserId()));
                    if (StringUtils.isEmpty(employeeRankingResult.getEmployeeName())) {
                        employeeRankingResult.setEmployeeName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ENTERPRISESPREADSTATISTICSERVICEIMPL_1629));
                    }
                }
                listMarketingActivityEnterpriseEmployeeRankingResults.add(employeeRankingResult);
            });
        }
        PageResult<ListMarketingActivityEnterpriseEmployeeRankingResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(listMarketingActivityEnterpriseEmployeeRankingResults);
        pageResult.setTotalCount(page.getTotalNum());
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<PageResult<GetRadarInfoResult>> getRadarsInfo(GetRadarsInfoArg vo) {
        List<GetRadarInfoResult> getRadarInfoResultList = Lists.newArrayList();
        PageResult<GetRadarInfoResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(getRadarInfoResultList);
        pageResult.setTotalCount(0);
        pageResult.setTime(vo.getTime());

        String uid;
        if (StringUtils.isNotBlank(vo.getUid())) {
            uid = vo.getUid();
        } else {
            uid = qywxUserManager.getUidByFsUserInfo(vo.getEa(), vo.getFsUserId());
        }
        if (StringUtils.isEmpty(uid)) {
            log.warn("MarketingReportServiceImpl.getRadarsInfo uid is null vo:{}", vo);
            return new Result<>(SHErrorCode.NO_DATA);
        }
        // 调用客脉雷达数据
        RadarsVO radarsVO = new RadarsVO();
        radarsVO.setPageNum(vo.getPageNum());
        radarsVO.setPageSize(vo.getPageSize());
        radarsVO.setTime(vo.getTime());
        radarsVO.setUid(uid);
        radarsVO.setNeedCardAuth(false);
        ModelResult<PageObject<RadarListUnitResult>> radarResult = indexService.radars(radarsVO);
        if (!radarResult.isSuccess()) {
            log.warn("MarketingReportServiceImpl.getRadarsInfo radarResult error vo:{},radarResult:{}", vo, radarResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        PageObject<RadarListUnitResult> radarResultData = radarResult.getData();
        if (radarResultData != null && CollectionUtils.isNotEmpty(radarResultData.getResult())) {
            pageResult.setTotalCount(radarResultData.getTotalCount());
            for (RadarListUnitResult radarListUnitResult : radarResultData.getResult()) {
                if(StringUtils.isNotBlank(radarListUnitResult.getNickName()) && radarListUnitResult.getNickName().contains(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGREPORTSERVICEIMPL_512))) {
                    radarListUnitResult.setNickName(radarListUnitResult.getNickName().replace("客脉访客", "访客"));
                }

            }
            pageResult.setResult(BeanUtil.copy(radarResultData.getResult(), GetRadarInfoResult.class));
        }
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    //等企业微信用户和营销用户后更新雷达
    @Override
    public Result<PageResult<GetRadarInfoResult>> getRadarsInfoForCustomer(GetRadarsInfoArg vo) {
        List<GetRadarInfoResult> getRadarInfoResultList = Lists.newArrayList();
        PageResult<GetRadarInfoResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(getRadarInfoResultList);
        pageResult.setTotalCount(0);
        pageResult.setTime(vo.getTime());

        String uid;
        if (StringUtils.isNotBlank(vo.getUid())) {
            uid = vo.getUid();
        } else {
            uid = qywxUserManager.getUidByFsUserInfo(vo.getEa(), vo.getFsUserId());
        }
        if (StringUtils.isEmpty(uid)) {
            log.warn("MarketingReportServiceImpl.getRadarsInfo uid is null vo:{}", vo);
            return new Result<>(SHErrorCode.NO_DATA);
        }
        // 调用客脉雷达数据
        RadarsVO radarsVO = new RadarsVO();
        radarsVO.setPageNum(vo.getPageNum());
        radarsVO.setPageSize(vo.getPageSize());
        radarsVO.setTime(vo.getTime());
        radarsVO.setUid(uid);
        radarsVO.setNeedCardAuth(false);
        ModelResult<PageObject<RadarListUnitResult>> radarResult = indexService.radars(radarsVO);
        radarsVO.setPageNum(1);
        radarsVO.setPageSize(radarResult.getData().getTotalCount());
        radarResult = indexService.radars(radarsVO);
        if (!radarResult.isSuccess()) {
            log.warn("MarketingReportServiceImpl.getRadarsInfo radarResult error vo:{},radarResult:{}", vo, radarResult);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        PageObject<RadarListUnitResult> radarResultData = radarResult.getData();

//        UserMarketingWxWorkExternalUserRelationEntity byEaAndWxWorkExternalUserId = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(vo.getEa(), vo.getExternalUserId());
//        userMarketingMiniappAccountRelationDao.queryUserMarketingMiniappAccountRelationUnionIdByUid(byEaAndWxWorkExternalUserId.getUserMarketingId())
        if (radarResultData != null && CollectionUtils.isNotEmpty(radarResultData.getResult())) {
            List<RadarListUnitResult> radarListResult = radarResultData.getResult();
            //客户
            if (null != vo.getChatType() && 1 == vo.getChatType()) {
                QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(vo.getEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(vo.getEa()));
                List<UserEntity> userEntities = userManager.queryByQYUserIdAndAppid(vo.getExternalUserId(), qywxMiniappConfigEntity.getAppid());
                List<String> uidList = userEntities.stream().map(UserEntity::getUid).collect(Collectors.toList());
                // UserEntity userEntity = userDAO.queryByCorpIdAndQYUserIdAndAppid(vo.getCorpId(), vo.getExternalUserId(), qywxMiniappConfigEntity.getAppid());
                radarListResult = radarListResult.stream().filter(radarUnitResult -> uidList.contains(radarUnitResult.getFriendUid())).collect(Collectors.toList());
            }
            //群聊
            if (null != vo.getChatType() && 2 == vo.getChatType()) {
                QueryGroupMemberListVO groupMemberVO = new QueryGroupMemberListVO();
                groupMemberVO.setGroupId(vo.getExternalUserId());
                groupMemberVO.setFsEa(vo.getEa());
                Result<List<QueryGroupMemberListResult>> listResult = qywxCustomerGroupService.queryGroupMemberList(groupMemberVO);
                List<QueryGroupMemberListResult> data = listResult.getData();
                List<String> groupMemberUserIds = data.stream().filter(o -> o.getMemberType().equals(2)).map(QueryGroupMemberListResult::getGroupMemberUserId).collect(Collectors.toList());
                QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(vo.getEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(vo.getEa()));
               //外部联系人id的集合换取uid集合
                List<String> uidList = new ArrayList<>();
                groupMemberUserIds.forEach(groupMemberUserId -> {
                    List<UserEntity> userEntities = userManager.queryByQYUserIdAndAppid(groupMemberUserId, qywxMiniappConfigEntity.getAppid());
                  //  Map<String, UserEntity> map = userEntities.stream().collect(Collectors.toMap(UserEntity::getUid, t -> t, (a, b) -> b));
                    userEntities.forEach(userEntity -> {
                      uidList.add(userEntity.getUid());
                    });
                });
                radarListResult = radarListResult.stream().filter(radarUnitResult -> uidList.contains(radarUnitResult.getFriendUid())).collect(Collectors.toList());
            }
            pageResult.setTotalCount(radarListResult.size());
            radarListResult =  radarListResult.stream().limit(vo.getPageSize()).skip((vo.getPageNum()-1)*vo.getPageSize()).collect(Collectors.toList());
            for (RadarListUnitResult radarListUnitResult : radarListResult) {
                if(StringUtils.isNotBlank(radarListUnitResult.getNickName()) && radarListUnitResult.getNickName().contains(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGREPORTSERVICEIMPL_512))) {
                    radarListUnitResult.setNickName(radarListUnitResult.getNickName().replace("客脉访客", "访客"));
                }
            }
            pageResult.setResult(BeanUtil.copy(radarListResult, GetRadarInfoResult.class));
        }
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<GetActivityRankingResult> getActivityRanking(GetActivityRankingArg vo) {
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(vo.getRecentDay() - 1);
        GetActivityRankingResult result = new GetActivityRankingResult();
        List<GetActivityRankingResult.ActivityRankingResult> dataList = Lists.newArrayList();
        result.setDataList(dataList);
        List<GetActivityRankingEntity> getActivityRankingEntityList = Lists.newArrayList();
        if (vo.getSearchType().equals(EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue())) {
            // 获取线索数从表单统计
            Map<String, Integer> statisticResultMap = customizeFormClueManager.countMarketingActivityClueNumByEa(vo.getEa(), true, vo.getRecentDay(), vo.getTopRankingCount());
            for (Map.Entry<String, Integer> entry : statisticResultMap.entrySet()) {
                GetActivityRankingEntity getActivityRankingEntity = new GetActivityRankingEntity();
                getActivityRankingEntity.setMarketingActivityId(entry.getKey());
                getActivityRankingEntity.setCount(entry.getValue());
                getActivityRankingEntityList.add(getActivityRankingEntity);
            }
        } else {
            getActivityRankingEntityList = marketingActivityDayStatisticDAO
                .getActivityRanking(vo.getEa(), startDate.toDate(), endDate.toDate(), vo.getSearchType(), vo.getTopRankingCount());
        }
        if (CollectionUtils.isNotEmpty(getActivityRankingEntityList)) {
            getActivityRankingEntityList = getActivityRankingEntityList.stream().filter(data -> data.getCount() != 0).collect(Collectors.toList());
            List<String> marketingActivityIds = getActivityRankingEntityList.stream().map(GetActivityRankingEntity::getMarketingActivityId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(marketingActivityIds)) {
                return Result.newSuccess(result);
            }
            PaasQueryMarketingActivityArg paasQueryMarketingActivityArg = new PaasQueryMarketingActivityArg();
            paasQueryMarketingActivityArg.setIsMankeep(true);
            paasQueryMarketingActivityArg.setIds(marketingActivityIds);
            paasQueryMarketingActivityArg.setPageSize(marketingActivityIds.size());
            paasQueryMarketingActivityArg.setPageNumber(0);
            // 批量获取活动信息
            com.fxiaoke.crmrestapi.common.data.Page<ObjectData> marketingActivityListVo = marketingActivityCrmManager.listMarketingActivity(vo.getEa(), -10000, paasQueryMarketingActivityArg);
            if (marketingActivityListVo != null && CollectionUtils.isNotEmpty(marketingActivityListVo.getDataList())) {
                Map<String, ObjectData> dataMap = marketingActivityListVo.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, data -> data));
                getActivityRankingEntityList.forEach(data -> {
                    ObjectData marketingActivityData = dataMap.get(data.getMarketingActivityId());
                    if (marketingActivityData != null) {
                        GetActivityRankingResult.ActivityRankingResult activityRankingResult = new GetActivityRankingResult.ActivityRankingResult();
                        activityRankingResult.setActivityId(data.getMarketingActivityId());
                        activityRankingResult.setActivityName(marketingActivityData.getName());
                        activityRankingResult.setCount(data.getCount());
                        dataList.add(activityRankingResult);
                    }
                });
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<ListActivityRankingResult>> listActivityRanking(String ea, Integer fsUserId, ListMarketingActivityRankingArg arg) {
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<ListActivityRankingResult> listActivityRankingResult = Lists.newArrayList();
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(arg.getRecentDay() - 1);
        List<GetActivityRankingEntity> activityRankingList = null;
        List<Integer> dataPermission = Lists.newArrayList();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        if (arg.getSearchType().equals(EnterpriseStatisticSearchTypeEnum.NUMBER_OF_SAVE_CLUE.getValue())) {
            List<String> activityIds = null;
            if (isOpen) {
                dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
                if(CollectionUtils.isEmpty(dataPermission)){
                    return Result.newSuccess();
                }
                PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
                queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.GTE,Lists.newArrayList(String.valueOf(startDate.toDate().getTime())));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.LTE,Lists.newArrayList(String.valueOf(endDate.toDate().getTime())));
                queryFilterArg.setQuery(paasQueryArg);
                List<String> selectFields = Lists.newArrayList("_id");
                queryFilterArg.setSelectFields(selectFields);
                int objectCount = crmV2Manager.countCrmObjectByFilterV3(ea, fsUserId, queryFilterArg);
                if(objectCount == 0){
                    return Result.newSuccess();
                }
                InnerPage<ObjectData> objectResult = crmV2Manager.listCrmObjectByFilterV3(ea, fsUserId, queryFilterArg,objectCount);
                activityIds = objectResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(activityIds)){
                    return new Result<>(SHErrorCode.SUCCESS, new PageResult<>());
                }
            }
            List<CustomizeFormClueNumDTO> formClueNumDTOS = customizeFormClueManager
                .countMarketingActivityClueNumByEaAndType(ea, true, arg.getRecentDay(), 1, arg.getPageSize(), (arg.getPageNum() - 1) * arg.getPageSize(),activityIds);
            if (CollectionUtils.isNotEmpty(formClueNumDTOS)) {
                activityRankingList =  BeanUtil.copy(formClueNumDTOS, GetActivityRankingEntity.class);
            }
        } else {
            if (isOpen) {
                dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
                if(CollectionUtils.isEmpty(dataPermission)){
                    return Result.newSuccess();
                }
                PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
                queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.DataOwnOrganizationName.getNewFieldName(), OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.GTE,Lists.newArrayList(String.valueOf(startDate.toDate().getTime())));
                paasQueryArg.addFilter(CrmV2LeadFieldEnum.CreateTime.getNewFieldName(), OperatorConstants.LTE,Lists.newArrayList(String.valueOf(endDate.toDate().getTime())));
                queryFilterArg.setQuery(paasQueryArg);
                List<String> selectFields = Lists.newArrayList("_id");
                queryFilterArg.setSelectFields(selectFields);
                int objectCount = crmV2Manager.countCrmObjectByFilterV3(ea, fsUserId, queryFilterArg);
                if(objectCount == 0){
                    return Result.newSuccess();
                }
                InnerPage<ObjectData> objectResult = crmV2Manager.listCrmObjectByFilterV3(ea, fsUserId, queryFilterArg,objectCount);
                List<String> activityIds = objectResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(activityIds)){
                    return new Result<>(SHErrorCode.SUCCESS, new PageResult<>());
                }
                activityRankingList = marketingActivityDayStatisticDAO.listDataPermissionActivityRanking(ea, startDate.toDate(), endDate.toDate(), arg.getSearchType(), activityIds, page);
            }else {
                activityRankingList = marketingActivityDayStatisticDAO.listActivityRanking(ea, startDate.toDate(), endDate.toDate(), arg.getSearchType(), page);
            }
            //listActivityRanking(ea, startDate.toDate(), endDate.toDate(), arg.getSearchType(), page);
        }

        PageResult<ListActivityRankingResult> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setResult(listActivityRankingResult);
        pageResult.setTotalCount(page.getTotalNum());
        if (CollectionUtils.isNotEmpty(activityRankingList)) {
            List<String> listMarketingActivityIds = activityRankingList.stream().filter(data -> data.getCount() != 0).map(GetActivityRankingEntity::getMarketingActivityId)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(listMarketingActivityIds)) {
                return new Result<>(SHErrorCode.SUCCESS, pageResult);
            }
            PaasQueryMarketingActivityArg activityArg = new PaasQueryMarketingActivityArg();
            activityArg.setIsMankeep(true);
            activityArg.setIds(listMarketingActivityIds);
            activityArg.setPageNumber(arg.getPageNum() - 1);
            activityArg.setPageSize(arg.getPageSize());
            com.fxiaoke.crmrestapi.common.data.Page<ObjectData> marketingActivityListVo = marketingActivityCrmManager.listMarketingActivity(ea, -10000, activityArg);
            if (CollectionUtils.isNotEmpty(marketingActivityListVo.getDataList())) {
                Map<String, ObjectData> marketingActivityDataMap = marketingActivityListVo.getDataList().stream()
                    .collect(Collectors.toMap(ObjectData::getId, marketingActivityVO -> marketingActivityVO));
                activityRankingList.forEach(entity -> {
                    ListActivityRankingResult activityRankingResult = new ListActivityRankingResult();
                    activityRankingResult.setMarketingActivityId(entity.getMarketingActivityId());
                    activityRankingResult
                        .setMarketingActivityName(marketingActivityDataMap.get(entity.getMarketingActivityId()) == null ? "" : marketingActivityDataMap.get(entity.getMarketingActivityId()).getName());
                    activityRankingResult.setCount(entity.getCount());
                    ObjectData objectData = marketingActivityDataMap.get(entity.getMarketingActivityId());
                    if (objectData != null) {
                        MarketingActivityData marketActivityData = MarketingActivityData.wrap(objectData);
                        activityRankingResult.setSpreadType(marketActivityData.getSpreadType());
                    }
                    listActivityRankingResult.add(activityRankingResult);
                });
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    private GetEnterpriseStatisticTrendResult buildEnterpriseStatisticTrendResult(List<MarketingEnterpriseDayStatisticEntity> entities, DateTime startDate, DateTime endDate) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Map<String, MarketingEnterpriseDayStatisticEntity> dayMap = entities.stream()
            .collect(Collectors.toMap(entity -> dateTimeFormatter.format(DateUtil.dateConvertLocalDateTime(entity.getDate())), e -> e, (v1, v2) -> v1));
        GetEnterpriseStatisticTrendResult result = new GetEnterpriseStatisticTrendResult();
        DateTime currentDate = new DateTime(startDate);
        while (currentDate.getMillis() <= endDate.getMillis()) {
            String currentDateStr = dateTimeFormatter.format(DateUtil.dateConvertLocalDateTime(currentDate.toDate()));
            MarketingEnterpriseDayStatisticEntity entity = dayMap.get(currentDateStr);
            if (entity == null) {
                result.getForwardTrend().add(new DayTrendData(currentDateStr, 0));
                result.getLookUpTrend().add(new DayTrendData(currentDateStr, 0));
                result.getSpreadTrend().add(new DayTrendData(currentDateStr, 0));
                result.getSaveToCrmLeadTrend().add(new DayTrendData(currentDateStr, 0));
                result.getSaveToCrmCustomerTrend().add(new DayTrendData(currentDateStr, 0));
            } else {
                result.getForwardTrend().add(new DayTrendData(currentDateStr, entity.getForwardCount()));
                result.getLookUpTrend().add(new DayTrendData(currentDateStr, entity.getLookUpCount()));
                result.getSpreadTrend().add(new DayTrendData(currentDateStr, entity.getSpreadCount()));
                result.getSaveToCrmLeadTrend().add(new DayTrendData(currentDateStr, entity.getLeadIncrementCount()));
                result.getSaveToCrmCustomerTrend().add(new DayTrendData(currentDateStr, entity.getCustomerIncrementCount()));
            }
            currentDate = currentDate.plusDays(1);
        }
        return result;
    }


    /**
     * 新增数据
     * @param arg
     * @return
     */
    @Override
    public Result<MarketingReportAddCountResult> marketingReportForAddCount(MarketingReportArg arg) {

        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas) || !eas.contains(arg.getEa())){
            log.warn("MarketingReportServiceImpl.marketingReportForAddCount ea is not in marketingReportSendEaList ea:{}", arg.getEa());
            return Result.newSuccess();
        }
        Long startWeekTime = getStartWeekTime(arg.getStartDate());
        String startDay = DateUtil.parse(new Date(startWeekTime), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        String key = "MARKETING_" + arg.getEa() + "_marketingReportForAddCount_" + startDay;
        String string = redisManager.get(key);
        if(StringUtils.isNotBlank(string)){
            MarketingReportAddCountResult marketingReportAddCountResult = gs.fromJson(string, MarketingReportAddCountResult.class);
            return Result.newSuccess(marketingReportAddCountResult);
        }

        MarketingReportAddCountResult result = new MarketingReportAddCountResult();
        //本周期
        MarketingReportAddCountData reportAddCountData = marketingReportForAddCountByTime(arg.getEa(), startWeekTime, getEndWeekTime(arg.getStartDate()));

        LocalDate today = new LocalDate(arg.getStartDate());
        LocalDate lastWeekStart = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY);
        LocalDate lastWeekEnd = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.SUNDAY);
        //上周开始结束时间
        DateTime lastWeekBeginDate = lastWeekStart.toDateTimeAtStartOfDay();
        DateTime lastWeekEndDate = lastWeekEnd.toDateTimeAtStartOfDay().withTime(23, 59, 59, 999);
        //上周期
        MarketingReportAddCountData lastReportAddCountData = marketingReportForAddCountByTime(arg.getEa(), lastWeekBeginDate.getMillis(), lastWeekEndDate.getMillis());

        result.setMarketingActivityCountInfo(new MarketingReportAddCountResult.marketingReportAddCountInfo(reportAddCountData.getMarketingActivityCountByWeek(), lastReportAddCountData.getMarketingActivityCountByWeek()));
        result.setMarketingEvenAddCountInfo(new MarketingReportAddCountResult.marketingReportAddCountInfo(reportAddCountData.getMarketingEvenAddCount(), lastReportAddCountData.getMarketingEvenAddCount()));
        result.setMarketingMaterialAddCountInfo(new MarketingReportAddCountResult.marketingReportAddCountInfo(reportAddCountData.getMarketingMaterialAddCount(), lastReportAddCountData.getMarketingMaterialAddCount()));
        result.setExternalUserCountInfo(new MarketingReportAddCountResult.marketingReportAddCountInfo(reportAddCountData.getExternalUserCountByWeek(), lastReportAddCountData.getExternalUserCountByWeek()));
        result.setLeadsCountInfo(new MarketingReportAddCountResult.marketingReportAddCountInfo(reportAddCountData.getLeadsCountByWeek(), lastReportAddCountData.getLeadsCountByWeek()));
        result.setWechatUserCountInfo(new MarketingReportAddCountResult.marketingReportAddCountInfo(reportAddCountData.getWechatUserCountByWeek(), lastReportAddCountData.getWechatUserCountByWeek()));
        redisManager.set(key, gs.toJson(result),3600*24*10);
        return Result.newSuccess(result);
    }


    /**
     *  营销动态按场景分组
     * @param arg
     * @return
     */
    @Override
    public Result<MarketingStatisticCountBySceneResult>  marketingStatisticInfo(MarketingReportArg arg) {

        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas) || !eas.contains(arg.getEa())){
            log.warn("MarketingReportServiceImpl.marketingReportForAddCount ea is not in marketingReportSendEaList ea:{}", arg.getEa());
            return Result.newSuccess();
        }
        Long startWeekTime = getStartWeekTime(arg.getStartDate());
        String startDay = DateUtil.parse(new Date(startWeekTime), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        String key = "MARKETING_" + arg.getEa() + "_marketingStatisticInfo_" + startDay;
        String string = redisManager.get(key);
        if(StringUtils.isNotBlank(string)){
            MarketingStatisticCountBySceneResult marketingStatisticCountBySceneResult = gs.fromJson(string, MarketingStatisticCountBySceneResult.class);
            return Result.newSuccess(marketingStatisticCountBySceneResult);
        }

        //营销动态：营销动态汇总数据，以及按推广方式分类数据统计
        MarketingStatisticCountBySceneResult marketingStatistic = marketingStatistic(arg.getEa(), startWeekTime,  getEndWeekTime(arg.getStartDate()));

        //mongo查出来的数据为空(报错的时候是空)，返回默认值并且不存入redis
        if(marketingStatistic==null){
            MarketingStatisticCountBySceneResult sceneData = new MarketingStatisticCountBySceneResult();
            List<MarketingStatisticCountBySceneResult.CountMarketingSceneInfo> finalSceneInfoList = Lists.newArrayList();
            for (MarketingSceneEnum value : MarketingSceneEnum.values()) {
                MarketingStatisticCountBySceneResult.CountMarketingSceneInfo info = new MarketingStatisticCountBySceneResult.CountMarketingSceneInfo();
                info.setSceneId(value.getCode());
                info.setCount(0);
                finalSceneInfoList.add(info);
            }
            sceneData.setTotal(0);
            sceneData.setInfoList(finalSceneInfoList);
            return Result.newSuccess(sceneData);
        }

        LocalDate today = new LocalDate(arg.getStartDate());
        LocalDate lastWeekStart = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.MONDAY);
        LocalDate lastWeekEnd = today.minusWeeks(1).withDayOfWeek(DateTimeConstants.SUNDAY);
        //上周开始结束时间
        DateTime lastWeekBeginDate = lastWeekStart.toDateTimeAtStartOfDay();
        DateTime lastWeekEndDate = lastWeekEnd.toDateTimeAtStartOfDay().withTime(23, 59, 59, 999);
        MarketingStatisticCountBySceneResult lastMarketingStatistic = marketingStatistic(arg.getEa(),lastWeekBeginDate.getMillis(), lastWeekEndDate.getMillis());

        //上周mongo查出来的数据为空(报错的时候是空)，返回默认值并且不存入redis
        if(lastMarketingStatistic==null){
            MarketingStatisticCountBySceneResult sceneData = new MarketingStatisticCountBySceneResult();
            List<MarketingStatisticCountBySceneResult.CountMarketingSceneInfo> finalSceneInfoList = Lists.newArrayList();
            for (MarketingSceneEnum value : MarketingSceneEnum.values()) {
                MarketingStatisticCountBySceneResult.CountMarketingSceneInfo info = new MarketingStatisticCountBySceneResult.CountMarketingSceneInfo();
                info.setSceneId(value.getCode());
                info.setCount(0);
                finalSceneInfoList.add(info);
            }
            sceneData.setTotal(0);
            sceneData.setInfoList(finalSceneInfoList);
            return Result.newSuccess(sceneData);
        }

        MarketingStatisticCountBySceneResult result = BeanUtil.copyByGson(marketingStatistic, MarketingStatisticCountBySceneResult.class);
        result.setGrowthRate(TextUtil.getGrowthRate(marketingStatistic.getTotal(),lastMarketingStatistic.getTotal()));

        redisManager.set(key, gs.toJson(result),3600*24*10);
        return Result.newSuccess(result);
    }

    /**
     * 公司资产
     * @param arg
     * @return
     */
    @Override
    public  Result<CompanyAssetsResult> companyAssets(MarketingReportArg arg) {

        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas) || !eas.contains(arg.getEa())){
            log.warn("MarketingReportServiceImpl.marketingReportForAddCount ea is not in marketingReportSendEaList ea:{}", arg.getEa());
            return Result.newSuccess();
        }
        Long startWeekTime = getStartWeekTime(arg.getStartDate());
        String startDay = DateUtil.parse(new Date(startWeekTime), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        String key = "MARKETING_" + arg.getEa() + "_companyAssets_" + startDay;
        String string = redisManager.get(key);
        if(StringUtils.isNotBlank(string)){
            CompanyAssetsResult companyAssetsResult = gs.fromJson(string, CompanyAssetsResult.class);
            return Result.newSuccess(companyAssetsResult);
        }

        CompanyAssetsResult result = new CompanyAssetsResult();
        //本公司营销资产:
        Integer marketingUserCount = marketingUserCount(arg.getEa(),null, getEndWeekTime(arg.getStartDate()));
        Integer marketingTargetUserGroupCount = marketingTargetUserGroupCount(arg.getEa(),null, getEndWeekTime(arg.getStartDate()));
        Integer marketingMaterialCount = getMarketingMaterialAddCount(arg.getEa(),null, getEndWeekTime(arg.getStartDate()));

        result.setMarketingUserCount(marketingUserCount);
        result.setMarketingTargetUserGroupCount(marketingTargetUserGroupCount);
        result.setMarketingMaterialCount(marketingMaterialCount);
        redisManager.set(key, gs.toJson(result),3600*24*10);
        return Result.newSuccess(result);
    }

    /**
     * 年度数据
     * @param arg
     * @return
     */
    @Override
    public Result<CompanyAssetsOtherDataResult>  companyAssetsOtherData(MarketingReportArg arg) {

        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas) || !eas.contains(arg.getEa())){
            log.warn("MarketingReportServiceImpl.marketingReportForAddCount ea is not in marketingReportSendEaList ea:{}", arg.getEa());
            return Result.newSuccess();
        }

        Long startWeekTime = getStartWeekTime(arg.getStartDate());
        String startDay = DateUtil.parse(new Date(startWeekTime), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        String key = "MARKETING_" + arg.getEa() + "_companyAssetsOtherData_" + startDay;
        String string = redisManager.get(key);
        if(StringUtils.isNotBlank(string)){
            CompanyAssetsOtherDataResult companyAssetsOtherDataResult = gs.fromJson(string, CompanyAssetsOtherDataResult.class);
            return Result.newSuccess(companyAssetsOtherDataResult);
        }

        CompanyAssetsOtherDataResult result = new CompanyAssetsOtherDataResult();
        Integer marketingActivityCountByYear = getMarketingActivityCount(arg.getEa(), getStartYearTime(arg.getStartDate()), getEndWeekTime(arg.getStartDate()));
        Integer leadsCountByYear = getLeadsCount(arg.getEa(), getStartYearTime(arg.getStartDate()), getEndWeekTime(arg.getStartDate()));
        result.setLeadsCountByYear(leadsCountByYear);
        result.setMarketingActivityCountByYear(marketingActivityCountByYear);

        redisManager.set(key, gs.toJson(result),3600*24*10);
        return Result.newSuccess(result);
    }


    /**
     * 最佳案例
     * @param ea
     * @return
     */
    @Override
    public Result<List<TopCaseExampleResult>> queryTopCaseExample(String ea) {
        if (StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        List<String> eas = JSON.parseArray(marketingReportSendEaList, String.class);
        if (CollectionUtils.isEmpty(eas) || !eas.contains(ea)){
            log.warn("MarketingReportServiceImpl.marketingReportForAddCount ea is not in marketingReportSendEaList ea:{}", ea);
            return Result.newSuccess();
        }

        List<TopCaseExampleResult> topCaseExampleResult = JSON.parseArray(TopCaseExampleList, TopCaseExampleResult.class);
        String content = I18nUtil.get("marketing.marketing.top.case.example.content", "快来免费搭建企业专属的【品牌云-数字展厅】吧");
        topCaseExampleResult.forEach(x -> x.setContent(content));
        return Result.newSuccess(topCaseExampleResult);
    }



    public MarketingReportAddCountData marketingReportForAddCountByTime(String ea, Long startTime, Long endTime) {
        MarketingReportAddCountData result = new MarketingReportAddCountData();
        //上周获取线索数：创建时间为上周的线索数
        Integer leadsCountByWeek = 0;
        //新增微信用户数：创建时间为上周的微信用户数
        Integer wechatUserCountByWeek = 0;
        //新增企微好友数：创建时间为上周的
        Integer externalUserCountByWeek = 0;
        //上周运营推广次数：【推广方式】不属于【个人推广】的营销活动数
        Integer marketingActivityCountByWeek = 0;
        //上周新增营销物料数：创建时间为上周的营销内容数
        Integer marketingMaterialAddCount = 0;
        //上周新增市场活动数：创建时间为上周的市场活动数（有配映射到营销通的）
        Integer marketingEvenAddCount = 0;

        leadsCountByWeek = getLeadsCount(ea,startTime,endTime);
        wechatUserCountByWeek = getWechatUserCount(ea,startTime,endTime);
        externalUserCountByWeek = getExternalUserCount(ea,startTime,endTime);
        marketingActivityCountByWeek = getMarketingActivityCount(ea,startTime,endTime);
        marketingMaterialAddCount = getMarketingMaterialAddCount(ea,startTime,endTime);
        marketingEvenAddCount = getMarketingEvenAddCount(ea,startTime,endTime);

        result.setLeadsCountByWeek(leadsCountByWeek);
        result.setWechatUserCountByWeek(wechatUserCountByWeek);
        result.setExternalUserCountByWeek(externalUserCountByWeek);
        result.setMarketingActivityCountByWeek(marketingActivityCountByWeek);
        result.setMarketingMaterialAddCount(marketingMaterialAddCount);
        result.setMarketingEvenAddCount(marketingEvenAddCount);

        return result;
    }

    private Integer getLeadsCount(String ea, Long startTime, Long endTime) {
        if (!crmV2Manager.isExistObject(ea, CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            return 0;
        }
        HeaderObj header = new HeaderObj(eieaConverter.enterpriseAccountToId(ea),-10000);
        ControllerListArg params = new ControllerListArg();
        params.setObjectDescribeApiName(CrmObjectApiNameEnum.CRM_LEAD.getName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("create_time",  Lists.newArrayList(String.valueOf(startTime)), FilterOperatorEnum.GTE);
        searchQuery.addFilter("create_time", Lists.newArrayList(String.valueOf(endTime)), FilterOperatorEnum.LTE);
        params.setSearchQuery(searchQuery);
       return metadataControllerServiceManager.getTotal(header,CrmObjectApiNameEnum.CRM_LEAD.getName(),params);
    }

    private Integer getWechatUserCount(String ea, Long startTime, Long endTime) {
        if (!crmV2Manager.isExistObject(ea, CrmObjectApiNameEnum.WECHAT.getName())) {
            return 0;
        }
        HeaderObj header = new HeaderObj(eieaConverter.enterpriseAccountToId(ea),-10000);
        ControllerListArg params = new ControllerListArg();
        params.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT.getName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("create_time",  Lists.newArrayList(String.valueOf(startTime)), FilterOperatorEnum.GTE);
        searchQuery.addFilter("create_time", Lists.newArrayList(String.valueOf(endTime)), FilterOperatorEnum.LTE);
        params.setSearchQuery(searchQuery);
        return metadataControllerServiceManager.getTotal(header,CrmObjectApiNameEnum.WECHAT.getName(),params);
    }

    private Integer getExternalUserCount(String ea, Long startTime, Long endTime) {
        if (!crmV2Manager.isExistObject(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())) {
            return 0;
        }
        HeaderObj header = new HeaderObj(eieaConverter.enterpriseAccountToId(ea),-10000);
        ControllerListArg params = new ControllerListArg();
        params.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("create_time",  Lists.newArrayList(String.valueOf(startTime)), FilterOperatorEnum.GTE);
        searchQuery.addFilter("create_time", Lists.newArrayList(String.valueOf(endTime)), FilterOperatorEnum.LTE);
        params.setSearchQuery(searchQuery);
        return metadataControllerServiceManager.getTotal(header,CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(),params);
    }

    private Integer getMarketingActivityCount(String ea, Long startTime, Long endTime) {
        return marketingActivityExternalConfigDao.countMarketingActivityCountByTime(ea,new Date(startTime),new Date(endTime), AssociateIdTypeEnum.entMarketingMarketingType());
    }

    private Integer getMarketingEvenAddCount(String ea, Long startTime, Long endTime) {
        if (!crmV2Manager.isExistObject(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName())) {
            return 0;
        }
        HeaderObj header = new HeaderObj(eieaConverter.enterpriseAccountToId(ea),-10000);
        ControllerListArg params = new ControllerListArg();
        params.setObjectDescribeApiName(CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("create_time",  Lists.newArrayList(String.valueOf(startTime)), FilterOperatorEnum.GTE);
        searchQuery.addFilter("create_time", Lists.newArrayList(String.valueOf(endTime)), FilterOperatorEnum.LTE);
        params.setSearchQuery(searchQuery);
        return metadataControllerServiceManager.getTotal(header,CrmObjectApiNameEnum.MARKETING_EVENT.getName(),params);
    }

    private Integer getMarketingMaterialAddCount(String ea, Long startTime, Long endTime) {
//        int totalCount = 0;
//        HeaderObj header = new HeaderObj(eieaConverter.enterpriseAccountToId(ea),-10000);
//        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(header, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());
//        if (!getDescribeResultResult.isSuccess()) {
//            return totalCount;
//        }
//
//        PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
//        paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());
//        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
//        if(startTime!=null){
//            paasQueryArg.addFilter("create_time", FilterOperatorEnum.GTE.getValue(),  Lists.newArrayList(String.valueOf(startTime)));
//        }
//        if(endTime!=null){
//            paasQueryArg.addFilter("create_time", FilterOperatorEnum.LTE.getValue(), Lists.newArrayList(String.valueOf(endTime)));
//        }
//        paasFilterArg.setQuery(paasQueryArg);
//        paasFilterArg.setSelectFields(Lists.newArrayList("content_id"));
//        InnerPage<ObjectData> pageResult = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
//        if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getDataList()))  {
//            Set<String> contentIdSet = pageResult.getDataList().stream().map(o -> o.getString("content_id")).collect(Collectors.toSet());
//            totalCount = contentIdSet.size();
//        }
//        return totalCount;
//       return marketingActivityObjectRelationDAO.countMaterialCountByTime(ea,startTime==null?null:new Date(startTime),endTime==null?null:new Date(endTime));
        return objectDao.countAllObject(ea,startTime==null?null:new Date(startTime),endTime==null?null:new Date(endTime));
    }

    private Integer marketingTargetUserGroupCount(String ea, Long startTime, Long endTime) {
        return marketingUserGroupDao.countGroupCountByTime(ea,startTime==null?null:new Date(startTime),endTime==null?null:new Date(endTime));
    }

    private MarketingStatisticCountBySceneResult marketingStatistic(String ea, Long startTime, Long endTime) {
        CountMarketingSceneArg arg = new CountMarketingSceneArg();
        arg.setEa(ea);
        arg.setBeginTime(startTime);
        arg.setEndTime(endTime);
        com.facishare.marketing.statistic.common.result.Result<CountMarketingSceneResult> objectActionFromUserMarketingList = userMarketingStatisticService.getMarketingStatisticCountByScene(arg);
        if(objectActionFromUserMarketingList==null || objectActionFromUserMarketingList.getData() == null ){
//            MarketingStatisticCountBySceneResult sceneData = new MarketingStatisticCountBySceneResult();
//            List<MarketingStatisticCountBySceneResult.CountMarketingSceneInfo> finalSceneInfoList = Lists.newArrayList();
//            for (MarketingSceneEnum value : MarketingSceneEnum.values()) {
//                MarketingStatisticCountBySceneResult.CountMarketingSceneInfo info = new MarketingStatisticCountBySceneResult.CountMarketingSceneInfo();
//                info.setSceneId(value.getCode());
//                info.setCount(0);
//                finalSceneInfoList.add(info);
//            }
//            sceneData.setTotal(0);
//            sceneData.setInfoList(finalSceneInfoList);
//            return sceneData;
            return null;
        }
        return BeanUtil.copyByGson(objectActionFromUserMarketingList.getData(), MarketingStatisticCountBySceneResult.class);
    }


    private Integer marketingUserCount(String ea, Long startTime, Long endTime){
        AtomicInteger count = new AtomicInteger();
        PaasQueryFilterArg baseQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        baseQueryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        baseQueryFilterArg.setSelectFields(selectFields);
        if(startTime!=null){
            paasQueryArg.addFilter("create_time", FilterOperatorEnum.GTE.getValue(),  Lists.newArrayList(String.valueOf(startTime)));
        }
        if(endTime!=null){
            paasQueryArg.addFilter("create_time", FilterOperatorEnum.LTE.getValue(), Lists.newArrayList(String.valueOf(endTime)));
        }
        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(7, "marketing_CRM_STATISTICS_DATA_thread");
        TraceContext context = TraceContext.get();
        AtomicBoolean atomicStatus = new AtomicBoolean(true);
        CountDownLatch countDownLatch = new CountDownLatch(7);
        Integer finalFsUserId = -10000;
        executorService.execute(() -> {
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                //查询线索数据量
                PaasQueryFilterArg leadQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                leadQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                int crmLeadCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, leadQueryFilterArg);
                count.addAndGet(crmLeadCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询客户数据量
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg accountQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                accountQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CUSTOMER.getName());
                int crmAccountCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, accountQueryFilterArg);
                count.addAndGet(crmAccountCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.CUSTOMER.getName(), e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询联系人数据量
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg contactQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                contactQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTACT.getName());
                int crmContactCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, contactQueryFilterArg);
                count.addAndGet(crmContactCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.CONTACT.getName(), e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询微信用户数据量
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg wxUserQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                wxUserQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT.getName());
                int crmWxUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, wxUserQueryFilterArg);
                count.addAndGet(crmWxUserCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.WECHAT.getName(), e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询企业微信客户的数量
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg wxWorkExternalUserQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                wxWorkExternalUserQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                int crmWxWorkExternalUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, wxWorkExternalUserQueryFilterArg);
                count.addAndGet(crmWxWorkExternalUserCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询会员的数量
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                PaasQueryFilterArg memberQueryFilterArg = BeanUtil.copy(baseQueryFilterArg, PaasQueryFilterArg.class);
                memberQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MEMBER.getName());
                int crmMemberUserCount = crmV2Manager.countCrmObjectByFilterV3(ea, finalFsUserId, memberQueryFilterArg);
                count.addAndGet(crmMemberUserCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmObjectByFilterV3 failed ea:{} apiName:{} e:", ea, CrmObjectApiNameEnum.MEMBER.getName(), e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });
        executorService.execute(() -> {
            //查询自定义对象的数量
            if (context != null ) {
                TraceContext._set(context);
            }
            try {
                int crmCustomizeObjectDataCount = countCrmCustomizeObjectData(ea, finalFsUserId,startTime,endTime);
                count.addAndGet(crmCustomizeObjectDataCount);
            }catch (Exception e){
                log.info("marketingUserCount countCrmCustomizeObjectData failed ea:{} e:", ea, e);
                atomicStatus.set(false);
            }finally {
                countDownLatch.countDown();
                if (context != null ) {
                    TraceContext.remove();
                }
            }
        });

        try {
            countDownLatch.await(30L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("marketingUserCount await failed ea:{} e:", ea, e);
            atomicStatus.set(false);
        } finally {
            if (!executorService.isShutdown()) {
                executorService.shutdown();
            }
        }

        if (atomicStatus.get()){
            return count.get();
        }else {
            return 0;
        }
    }

    private int countCrmCustomizeObjectData(String ea, Integer fsUserId, Long startTime, Long endTime){
        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(objectMappingEntityList)){
            return 0;
        }

        List<String> apiNameList = objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList());
        int totalCount = 0;
        List<Filter> filters = Lists.newArrayList();
        if(startTime!=null){
            String startTimeStr = String.valueOf(startTime);
            Filter filter = new Filter();
            filter.setFieldName("create_time");
            filter.setOperator("GTE");
            filter.setFieldValues(Lists.newArrayList(startTimeStr));
            filters.add(filter);
        }
        if(endTime!=null){
            String endTimeStr = String.valueOf(endTime);
            Filter filter = new Filter();
            filter.setFieldName("create_time");
            filter.setOperator("LTE");
            filter.setFieldValues(Lists.newArrayList(endTimeStr));
            filters.add(filter);
        }
        //直接从对象查询
        Map<String, Integer> objectDataCountMap = new HashMap<>();
        if (apiNameList.size() > 0){
            CountDownLatch countDownLatch = new CountDownLatch(apiNameList.size());
            for ( String apiName : apiNameList) {
                ThreadPoolUtils.execute(() -> {
                    try {
                        objectDataCountMap.put(apiName, crmMetadataManager.countCrmObjectDataByFilter(ea, fsUserId, apiName,filters));
                    } catch (Exception e) {
                        log.warn("MarketingReportServiceImpl.countCrmCustomizeObjectData error ea:{} fsUserId:{} apiName:{} e:", ea, fsUserId, apiName, e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }

            try {
                countDownLatch.await(15, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("MarketingReportServiceImpl.countCrmCustomizeObjectData error e:", e);
            }
        }
        if (!objectDataCountMap.isEmpty()) {
            for (Map.Entry<String, Integer> entry : objectDataCountMap.entrySet()) {
                totalCount += entry.getValue();
            }
        }
        return totalCount;
    }
    private Long getStartWeekTime(Long startTime) {
        LocalDate localDate = new LocalDate(startTime);
        LocalDate startWeek = localDate.withDayOfWeek(DateTimeConstants.MONDAY);
        return startWeek.toDateTimeAtStartOfDay().withTime(0, 0, 0, 0).getMillis();
    }

    private Long getEndWeekTime(Long startTime) {
        LocalDate localDate = new LocalDate(startTime);
        LocalDate endWeek = localDate.withDayOfWeek(DateTimeConstants.SUNDAY);
        return endWeek.toDateTimeAtStartOfDay().withTime(23, 59, 59, 999).getMillis();
    }
    private Long getStartYearTime(Long startTime) {
        LocalDate localDate = new LocalDate(startTime);
        LocalDate endYear = localDate.withDayOfYear(1);
        return endYear.toDateTimeAtStartOfDay().withTime(0, 0, 0, 0).getMillis();
    }
}