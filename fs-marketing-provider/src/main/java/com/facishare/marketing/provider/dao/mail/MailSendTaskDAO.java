package com.facishare.marketing.provider.dao.mail;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * Created by zhengh on 2020/6/4.
 */
public interface MailSendTaskDAO {
    @Insert("INSERT INTO mail_send_task(id, ea, fs_user_id, subject, marketing_group_user_ids, marketing_event_id, to_user, html,"
            + "send_range, fix_time, content_summary, attachments, template_invoke_name, send_status, total_send_count, "
            + "schedule_type, mail_type, label_id, sender_ids, reply_ids, filter_nday_sent_user, create_time, update_time, send_object_tags, exclude_object_tags, tag_operator) VALUES(#{entity.id}, #{entity.ea}, #{entity.fsUserId}, #{entity.subject},"
            + " #{entity.marketingGroupUserIds}, #{entity.marketingEventId}, #{entity.toUser}, #{entity.html}, #{entity.sendRange},"
            + " #{entity.fixTime}, #{entity.contentSummary}, #{entity.attachments}, #{entity.templateInvokeName}, #{entity.sendStatus},"
            + " #{entity.totalSendCount}, #{entity.scheduleType}, #{entity.mailType}, #{entity.labelId}, #{entity.senderIds},"
            + " #{entity.replyIds}, #{entity.filterNDaySentUser}, #{entity.createTime}, #{entity.updateTime}, #{entity.sendObjectTags}, #{entity.excludeObjectTags}, #{entity.tagOperator})")
    void insert(@Param("entity")MailSendTaskEntity entity);

    @Update("UPDATE mail_send_task SET send_status=#{status}, update_time=now() WHERE id=#{id}")
    int updateStatusById(@Param("status")Integer status, @Param("id")String id);

    @Update("UPDATE mail_send_task SET mail_type=#{mailType}, update_time=now() WHERE id=#{id}")
    int updateMailTypeById(@Param("mailType")Integer mailType, @Param("id")String id);

    @Update("UPDATE mail_send_task SET send_status=#{updateStatus}, update_time=now() WHERE id=#{id} AND send_status = #{beforeStatus}")
    int updateStatusByIdAndBeforeStatus(@Param("updateStatus") Integer updateStatus, @Param("id") String id, @Param("beforeStatus") Integer beforeStatus);

    @Update("UPDATE mail_send_task SET maillist_task_id=#{maillistTaskId}, update_time=now() WHERE id=#{id}")
    int updateMaillistTaskIdById(@Param("id")String id, @Param("maillistTaskId")Integer maillistTaskId);

    @FilterLog
    @Select("SELECT * FROM mail_send_task WHERE id=#{id}")
    MailSendTaskEntity getById(@Param("id")String id);

    @Update("UPDATE mail_send_task SET total_send_count=#{totalSendCount}, update_time=now() WHERE id=#{id}")
    int updateTotalSendCountById(@Param("id")String id, @Param("totalSendCount")int totalSendCount);

    @Update(" UPDATE mail_send_task SET label_id = #{labelId} WHERE id = #{id}")
    int setLabelIdById(@Param("id") String id, @Param("labelId") Integer labelId);

    @FilterLog
    @Select(" SELECT id, ea, fs_user_id, subject, marketing_group_user_ids, marketing_event_id,send_range, fix_time, content_summary, attachments, template_invoke_name, send_status, total_send_count,schedule_type,mail_type, label_id, sender_ids, reply_ids,filter_nday_sent_user, create_time, update_time FROM mail_send_task WHERE ea=#{ea}")
    List<MailSendTaskEntity> getTaskByEaWithoutBigField(@Param("ea")String ea);

    @FilterLog
    @Select(" SELECT label_id  FROM mail_send_task WHERE ea = #{ea} and label_id is not null and create_time >= #{createTime}")
    List<Integer> getLabelIdByEaAndCreateTime(@Param("ea") String ea, @Param("createTime") Date createTime);

    @FilterLog
    @Select("<script>"
            + "SELECT m.id, m.ea, fs_user_id, subject, marketing_group_user_ids, m.marketing_event_id,send_range, fix_time, content_summary, attachments, template_invoke_name, send_status, total_send_count,schedule_type,mail_type, label_id, sender_ids, reply_ids,filter_nday_sent_user,status_code, m.create_time,  m.update_time FROM mail_send_task m LEFT JOIN marketing_activity_external_config a ON m.id = a.associate_id WHERE m.ea=#{ea} AND a.ea=#{ea}\n"
            + "AND a.marketing_activity_id IN\n"
            +   "<foreach collection = 'marketingActivityIds' item = 'marketingActivityId' open = '(' separator = ',' close = ')'>"
            +       "#{marketingActivityId}"
            +   "</foreach>"
            + " ORDER BY m.create_time DESC"
            + "</script>")
    List<MailSendTaskEntity> getPageMailMarketingByMarketingActivityIdsWithoutBigField(@Param("ea")String ea, @Param("marketingActivityIds") List<String> marketingActivityIds);


    @Select(" SELECT DISTINCT label_id FROM mail_send_task WHERE ea = #{ea} AND label_id IS NOT NULL")
    List<Integer> getLabelIdsByEa(@Param("ea") String ea);

    @Delete("DELETE FROM mail_send_task WHERE id=#{id} AND ea=#{ea}")
    int deleteById(@Param("id")String id, @Param("ea")String ea);

    @Select(" SELECT id, ea, fs_user_id, subject, marketing_group_user_ids, marketing_event_id,send_range, fix_time, content_summary, attachments, template_invoke_name, send_status, total_send_count,schedule_type,mail_type, label_id, sender_ids, reply_ids,filter_nday_sent_user, create_time, update_time FROM mail_send_task where create_time >= (SELECT now( ) - INTERVAL '30 day')")
    @FilterLog
    List<MailSendTaskEntity> listAllWithoutBigField();

    @Update("UPDATE mail_send_task SET send_status=#{status},status_code=#{statusCode}, update_time=now() WHERE id=#{id}")
    int updateStatusAndCodeById(@Param("status")Integer status, @Param("statusCode")Integer statusCode, @Param("id")String id);


    @Select("SELECT DISTINCT ea FROM mail_send_task WHERE create_time between #{startTime} and #{endTime}")
    List<String> getSendTaskEasByCreateTime(@Param("startTime")Date createTime, @Param("endTime")Date endTime);

    @FilterLog
    @Select("<script>"
            +     "SELECT id, ea, fs_user_id, subject, marketing_group_user_ids, marketing_event_id,send_range, fix_time, content_summary, attachments, template_invoke_name, send_status, total_send_count,schedule_type,mail_type, label_id, sender_ids, reply_ids,filter_nday_sent_user, create_time, update_time FROM mail_send_task WHERE id IN "
            +     "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            +"</script>")
    List<MailSendTaskEntity> getByIdListWithoutBigField(@Param("idList") List<String> idList);

    @FilterLog
    @Select("SELECT * FROM mail_send_task WHERE ea = #{ea} and id = #{id}")
    MailSendTaskEntity getByEaAndId(@Param("ea") String ea, @Param("id")String id);
}

