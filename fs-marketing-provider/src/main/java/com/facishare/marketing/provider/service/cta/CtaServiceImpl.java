package com.facishare.marketing.provider.service.cta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.UrlEncoder;
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.api.arg.PubPlatAuthComponentArg;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.cta.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.qywx.miniapp.MiniappAccessPermissionsDataArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.cta.*;
import com.facishare.marketing.api.result.qywx.miniapp.MiniappAccessPermissionsDataResult;
import com.facishare.marketing.api.result.wxPublicPlatform.WxPublicPlatformAuthorizeUserInfo;
import com.facishare.marketing.api.service.cta.CtaService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.WxApiConstants;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.MarketingCommonSettingEnum;
import com.facishare.marketing.common.enums.MarketingUserActionChannelType;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FlexibleJson;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.cta.CtaDAO;
import com.facishare.marketing.provider.dao.cta.CtaQrCodeRelationDAO;
import com.facishare.marketing.provider.dao.cta.CtaRelationDAO;
import com.facishare.marketing.provider.dao.cta.CtaSettingDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.cta.CtaQueryParam;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dto.cta.CtaEntityDTO;
import com.facishare.marketing.provider.dto.cta.CtaRelationCountDTO;
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.cta.CtaEntity;
import com.facishare.marketing.provider.entity.cta.CtaQrCodeRelationEntity;
import com.facishare.marketing.provider.entity.cta.CtaRelationEntity;
import com.facishare.marketing.provider.entity.cta.CtaSettingEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingMiniappAccountRelationEntity;
import com.facishare.marketing.provider.innerArg.crm.AggregateParameterArg;
import com.facishare.marketing.provider.innerArg.qywx.ContactMeConfigArg;
import com.facishare.marketing.provider.innerData.cta.CtaGuideComponent;
import com.facishare.marketing.provider.innerData.cta.CtaTriggerConfig;
import com.facishare.marketing.provider.innerResult.qywx.GetContactMeResult;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.ObjectGroupManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.AggregateQueryResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("ctaService")
public class CtaServiceImpl implements CtaService {
    @Autowired
    private CtaDAO ctaDAO;

    @Autowired
    private CtaRelationDAO ctaRelationDAO;

    @Autowired
    private CtaSettingDAO ctaSettingDAO;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private UserDAO userDAO;

    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private CtaQrCodeRelationDAO ctaQrCodeRelationDAO;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private MarketingRecordActionSender marketingRecordActionSender;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    @ReloadableProperty("fsCustomizeFormDataEnroll_member_enable")
    private String fsCustomizeFormDataEnrollMemberEnable;

    @Override
    public Result<AddCtaResult> addCta(String ea, Integer fsUserId, AddCtaArg vo) {
        if(vo == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String insertedDataId = vo.getId();
        if(Strings.isBlank(insertedDataId)) {
            insertedDataId = UUIDUtil.getUUID();
        }
        CtaEntity sameNameEntity = ctaDAO.queryCtaByName(ea, insertedDataId, vo.getName());
        if(sameNameEntity != null) {
            return Result.newError(SHErrorCode.CTA_NAME_EXISTS);
        }

        Result<CtaEntity> convertResult = convertArg2Entity(vo);
        if(!convertResult.isSuccess()) {
            return Result.newError(convertResult.getErrCode(), convertResult.getErrMsg());
        }
        CtaEntity entity = convertResult.getData();
        entity.setEa(ea);
        entity.setStatus(1);
        entity.setCreateBy(fsUserId);
        entity.setUpdateBy(fsUserId);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setId(insertedDataId);
        boolean success = ctaDAO.addCta(entity);
        if(!success) {
            return Result.newError(SHErrorCode.ADD_FAIL);
        }
        if(vo.getSettings() != null && !vo.getSettings().isEmpty()) {
            saveCtaSetting(ea,fsUserId, insertedDataId, vo.getSettings());
        }
        objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.CTA.getType(), Collections.singletonList(entity.getId()), vo.getGroupId());
        AddCtaResult result = new AddCtaResult();
        result.setId(insertedDataId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> updateCta(String ea, Integer fsUserId, AddCtaArg vo) {
        if(Strings.isBlank(vo.getId())) {
            return  Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        CtaEntity sameNameEntity = ctaDAO.queryCtaByName(ea, vo.getId(), vo.getName());
        if(sameNameEntity != null) {
            return Result.newError(SHErrorCode.CTA_NAME_EXISTS);
        }
        Result<CtaEntity> convertResult = convertArg2Entity(vo);
        if(!convertResult.isSuccess()) {
            return Result.newError(convertResult.getErrCode(), convertResult.getErrMsg());
        }
        CtaEntity entity = convertResult.getData();
        entity.setEa(ea);
        entity.setUpdateBy(fsUserId);
        entity.setUpdateTime(new Date());
        entity.setId(vo.getId());
        boolean success = ctaDAO.updateCta(entity);
        if(!success) {
            return Result.newError(SHErrorCode.UPDATE_FAIL);
        }
        if(vo.getSettings() != null && !vo.getSettings().isEmpty()) {
            saveCtaSetting(ea,fsUserId, vo.getId(), vo.getSettings());
        }
        return Result.newSuccess(true);
    }

    private Result<CtaEntity> convertArg2Entity(AddCtaArg vo) {
        if(vo == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if(StringUtils.isBlank(vo.getTriggerSettings())) {
            return Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE);
        }

        List<CtaTriggerConfig> triggerConfigs = Lists.newArrayList();
        JSONObject jsonObject = JSONObject.parseObject(vo.getTriggerSettings());
        if(jsonObject == null) {
            return Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE);
        }
        JSONObject triggerJsonObject = jsonObject.getJSONObject("onComponentClick");
        if(triggerJsonObject != null) {
            CtaTriggerConfig ctaTriggerConfig = JSON.parseObject(JSON.toJSONString(triggerJsonObject), CtaTriggerConfig.class);
            ctaTriggerConfig.setTriggerType(1);
            triggerConfigs.add(ctaTriggerConfig);
        }
        triggerJsonObject = jsonObject.getJSONObject("onScrollProgress");
        if(triggerJsonObject != null) {
            CtaTriggerConfig ctaTriggerConfig = JSON.parseObject(JSON.toJSONString(triggerJsonObject), CtaTriggerConfig.class);
            ctaTriggerConfig.setTriggerType(2);
            triggerConfigs.add(ctaTriggerConfig);
        }
        triggerJsonObject = jsonObject.getJSONObject("onTimeElapsed");
        if(triggerJsonObject != null) {
            CtaTriggerConfig ctaTriggerConfig = JSON.parseObject(JSON.toJSONString(triggerJsonObject), CtaTriggerConfig.class);
            ctaTriggerConfig.setTriggerType(3);
            triggerConfigs.add(ctaTriggerConfig);
        }
        triggerJsonObject = jsonObject.getJSONObject("onExitIntent");
        if(triggerJsonObject != null) {
            CtaTriggerConfig ctaTriggerConfig = JSON.parseObject(JSON.toJSONString(triggerJsonObject), CtaTriggerConfig.class);
            ctaTriggerConfig.setTriggerType(4);
            triggerConfigs.add(ctaTriggerConfig);
        }

        if(CollectionUtils.isEmpty(triggerConfigs) || triggerConfigs.stream().noneMatch(x -> Boolean.TRUE.equals(x.getEnabled()))) {
            return Result.newError(SHErrorCode.CTA_TRIGGER_TYPE_REQUIRE_AT_LEAST_ONE);
        }

        for(CtaTriggerConfig triggerConfig : triggerConfigs) {
            if(Boolean.TRUE.equals(triggerConfig.getEnabled()) && (2 == triggerConfig.getTriggerType() || 3 == triggerConfig.getTriggerType())
                    && (triggerConfig.getDuration() == null || triggerConfig.getDuration() < 0)) {
                return  Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }

        if(StringUtils.isBlank(vo.getGuideComponents())) {
            return Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE);
        }

        List<CtaGuideComponent> guideComponents = JSON.parseArray(vo.getGuideComponents(), CtaGuideComponent.class);
        if(CollectionUtils.isEmpty(guideComponents)) {
            return Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_REQUIRE_AT_LEAST_ONE);
        }
        if(guideComponents.size() > 3) {
            return Result.newError(SHErrorCode.CTA_GUIDE_COMPONENT_FEWER_THAN_THREE);
        }

        CtaEntity entity = new CtaEntity();
        entity.setName(vo.getName());
        entity.setTriggerSettings(vo.getTriggerSettings());
        entity.setGuideComponents(vo.getGuideComponents());
        Optional<CtaTriggerConfig> op = triggerConfigs.stream().filter(x -> x.getTriggerType() == 1).findFirst();
        if(op.isPresent() && Boolean.TRUE.equals(op.get().getCustomButtonEnabled())) {
            entity.setStandaloneButton(true);
        } else {
            entity.setStandaloneButton(false);
        }
        List<String> types = triggerConfigs.stream().filter(x -> Boolean.TRUE.equals(x.getEnabled())).map(x -> String.valueOf(x.getTriggerType())).collect(Collectors.toList());
        String typesString = String.join(",", types);
        entity.setTriggerTypes(String.format(",%s,", typesString));
        types = guideComponents.stream().map(x -> String.valueOf(x.getComponentType())).collect(Collectors.toList());
        typesString = String.join(",", types);
        entity.setGuideComponentTypes(String.format(",%s,", typesString));
        entity.setFormId(vo.getFormId());
        return Result.newSuccess(entity);
    }

    @Override
    public Result<Boolean> updateCtaStatus(String ea, Integer fsUserId, UpdateCtaStatusArg vo) {
        if(Strings.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        boolean success = ctaDAO.updateCtaStatus(ea, vo.getId(), vo.getStatus());
        if(!success) {
            return Result.newError(SHErrorCode.UPDATE_FAIL);
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> deleteCta(String ea, Integer fsUserId, DeleteCtaArg vo) {
        if(Strings.isBlank(vo.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<CtaRelationEntity> relationEntities = ctaRelationDAO.getByCtaIds(ea, Lists.newArrayList(vo.getId()));
        if(CollectionUtils.isNotEmpty(relationEntities)) {
            return Result.newError(SHErrorCode.CTA_EXISTS_RELATION.getErrorCode(), String.format(SHErrorCode.CTA_EXISTS_RELATION.getErrorMessage(), relationEntities.size()));
        }
        boolean success = ctaDAO.deleteCta(ea, vo.getId());
        if(!success) {
            return Result.newError(SHErrorCode.DEL_FAIL);
        }
        return Result.newSuccess(true);
    }

    @Override
    public Result<QueryCtaDetailResult> queryCtaDetail(String ea, Integer fsUserId, QueryCtaDetailArg vo) {
        CtaEntity ctaEntity = ctaDAO.queryCtaDetail(ea, vo.getId());
        if(ctaEntity == null) {
            return new Result<>(SHErrorCode.CTA_NOT_FOUND);
        }
        QueryCtaDetailResult result = new QueryCtaDetailResult();
        result.setCreateBy(String.valueOf(ctaEntity.getCreateBy()));
        result.setEa(ea);
        result.setCreateTime(ctaEntity.getCreateTime().getTime());
        result.setName(ctaEntity.getName());
        result.setStatus(ctaEntity.getStatus());
        result.setGuideComponents(ctaEntity.getGuideComponents());
        result.setUpdateBy(String.valueOf(ctaEntity.getUpdateBy()));
        result.setTriggerSettings(ctaEntity.getTriggerSettings());
        result.setUpdateTime(ctaEntity.getUpdateTime().getTime());
        result.setId(ctaEntity.getId());
        result.setStandaloneButton(ctaEntity.getStandaloneButton());
        result.setTriggerTypes(ctaEntity.getTriggerTypes());
        result.setGuideComponentTypes(ctaEntity.getGuideComponentTypes());
        CtaSettingEntity ctaSettingEntity = ctaSettingDAO.getByCtaId(ea, vo.getId());
        if(ctaSettingEntity != null) {
            result.setSettings(ctaSettingEntity.getConfigValues());
        }
        int relationCount = ctaRelationDAO.queryCountByCtaId(ea, vo.getId());
        Map<String, Map<String, Integer>> ctaDataSumMap = getUserBehaviorCtaCountMap(ea, Lists.newArrayList(vo.getId()));
        result.setRelationCount(relationCount);
        result.setTriggerCount(0);
        result.setWechatNicknameCount(0);
        result.setWechatPhoneCount(0);
        result.setMemberLoginCount(0);
        result.setMemberRegisterCount(0);
        result.setPublicAccountQrCount(0);
        result.setPublicAccountFansCount(0);
        result.setAddQywxFriendsCount(0);
        result.setSubmitFormCount(0);
        if(ctaDataSumMap != null && ctaDataSumMap.containsKey(vo.getId())) {
            Map<String, Integer> sumDataMap = ctaDataSumMap.get(vo.getId());
            for(Map.Entry<String, Integer> entry : sumDataMap.entrySet()) {
                Integer sumValue = entry.getValue();
                switch (entry.getKey()) {
                    case "authorize_wechat_nickname":
                        result.setWechatNicknameCount(sumValue);
                        break;
                    case "authorize_wechat_phone":
                        result.setWechatPhoneCount(sumValue);
                        break;
                    case "login_member_account":
                        result.setMemberLoginCount(sumValue);
                        break;
                    case "register_member":
                        result.setMemberRegisterCount(sumValue);
                        break;
                    case "scan_wx_service_qr_code":
                        result.setPublicAccountQrCount(sumValue);
                        break;
                    case "follow_wx_service":
                        result.setPublicAccountFansCount(sumValue);
                        break;
                    case "add_qywx_employee":
                        result.setAddQywxFriendsCount(sumValue);
                        break;
                    case "enroll_form_new":
                        result.setSubmitFormCount(sumValue);
                        break;
                    case "trigger_cta_display_lead_generation_page":
                        result.setTriggerCount(sumValue);
                        break;
                }
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryCtaSimpleDetailResult> queryCtaSimpleDetail(String ea, Integer fsUserId, QueryCtaSimpleDetailArg vo) {
        if(StringUtils.isBlank(ea)) {
            ea = objectManager.getObjectEa(vo.getObjectId(), vo.getObjectType());
        }
        CtaEntity ctaEntity = ctaDAO.queryCtaDetail(ea, vo.getId());
        if(ctaEntity == null) {
            return new Result<>(SHErrorCode.CTA_NOT_FOUND);
        }
        QueryCtaSimpleDetailResult result = new QueryCtaSimpleDetailResult();
        result.setCreateBy(String.valueOf(ctaEntity.getCreateBy()));
        result.setEa(ea);
        result.setCreateTime(ctaEntity.getCreateTime().getTime());
        result.setName(ctaEntity.getName());
        result.setStatus(ctaEntity.getStatus());
        result.setGuideComponents(ctaEntity.getGuideComponents());
        result.setUpdateBy(String.valueOf(ctaEntity.getUpdateBy()));
        result.setTriggerSettings(ctaEntity.getTriggerSettings());
        result.setUpdateTime(ctaEntity.getUpdateTime().getTime());
        result.setId(ctaEntity.getId());
        result.setStandaloneButton(ctaEntity.getStandaloneButton());
        result.setTriggerTypes(ctaEntity.getTriggerTypes());
        result.setGuideComponentTypes(ctaEntity.getGuideComponentTypes());
        result.setHasPhone(false);
        result.setHasNickNameAndAvatar(false);
        result.setHasMember(false);
        result.setHasAddQywxExtendUser(false);
        result.setHasFollowOfficialAccount(false);
        result.setHasEnrollForm(false);

        if(StringUtils.isNotBlank(ctaEntity.getFormId())) {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(ctaEntity.getFormId());
            if(customizeFormDataEntity != null) {
                Boolean enrollLimit = customizeFormDataEntity.getFormMoreSetting().isEnrollLimit();
                Integer memberCheckType = customizeFormDataEntity.getFormMoreSetting().getMemberCheckType();
                result.setMemberCheckType(memberCheckType);
                if (enrollLimit) { // 是否开启报名限制
                    long countResult = customizeFormDataUserDAO.countCustomizeFormDataUserByFormId(customizeFormDataEntity.getId());
                    if (countResult >= customizeFormDataEntity.getFormMoreSetting().getEnrollLimitNum()) {
                        result.setHasEnrollForm(true);
                    }
                }

                if(!result.getHasEnrollForm()) {
                    if(StringUtils.isNotBlank(vo.getUid())) {
                        String enrollId = customizeFormDataManager.checkWxAppUserIsEnrolledWithNoHandleActivity(vo.getMarketingEventId(), customizeFormDataEntity, vo.getObjectId(), vo.getObjectType(), vo.getUid(), null);
                        if (StringUtils.isNotBlank(enrollId)) {
                            result.setHasEnrollForm(true);
                        }
                    }
                    Result<CustomizeFormDataUserEntity> enrollCheck = customizeFormDataManager
                            .checkUserIsEnrolledWithNoHandleActivity(vo.getMarketingEventId(), customizeFormDataEntity, vo.getObjectId(), vo.getObjectType(), vo.getWxAppId(), vo.getOpenId(), vo.getBrowserUserId(), ea, vo.getFsUserId(), null);
                    if (!enrollCheck.isSuccess()) {
                        //说明用户已经报过名
                        result.setHasEnrollForm(true);
                    } else {
                        enrollCheck = checkUserIsEnrolled(vo.getMarketingEventId(), customizeFormDataEntity, vo.getObjectId(), vo.getObjectType(), vo.getWxAppId(), vo.getOpenId(), vo.getBrowserUserId(), ea, vo.getFsUserId(), vo.getUid());
                        if (!enrollCheck.isSuccess()) {
                            //说明用户已经报过名
                            result.setHasEnrollForm(true);
                        }
                    }
                }
            } else {
                result.setHasEnrollForm(true);
            }
        } else {
            result.setHasEnrollForm(true);
        }

        String guideComponentTypes = ctaEntity.getGuideComponentTypes();
        if(StringUtils.isBlank(guideComponentTypes)) {
            result.setHasPhone(true);
            result.setHasNickNameAndAvatar(true);
            result.setHasAddQywxExtendUser(true);
            result.setHasFollowOfficialAccount(true);
            return Result.newSuccess(result);
        }

        //引导组件类型 1、提交表单 2、验证会员身份 3、授权微信头像昵称 4、授权微信手机号 5、关注公众号 6、添加企微好友
        if(guideComponentTypes.contains(",3,") || guideComponentTypes.contains(",4,")
                || guideComponentTypes.contains(",5,") || guideComponentTypes.contains(",6,")) {
            String userMarketingId = vo.getUserMarketingId();
            UserEntity userEntity = null;
            if(StringUtils.isNotBlank(vo.getUid())) {
                userEntity = userDAO.queryByUid(vo.getUid());
            } else if(StringUtils.isNotBlank(vo.getOpenId())) {
                userEntity = userDAO.queryByOpenId(vo.getOpenId());
            }

            if(guideComponentTypes.contains(",3,")) {
                Optional<ObjectData> wechatFan= crmV2Manager.getWechatFanByOpenId(ea, vo.getWxAppId(), vo.getOpenId());
                if (wechatFan.isPresent()) {
                    result.setHasNickNameAndAvatar(true);
                }
                if(userEntity != null
                        && (StringUtils.isNotBlank(userEntity.getAvatar()) || StringUtils.isNotBlank(userEntity.getName()))) {
                    result.setHasNickNameAndAvatar(true);
                }
            } else {
                result.setHasNickNameAndAvatar(true);
            }
            if(StringUtils.isBlank(userMarketingId)) {
                if(userEntity != null) {
                    UserMarketingMiniappAccountRelationEntity relationEntity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, userEntity.getUid());
                    if(relationEntity != null) {
                        userMarketingId = relationEntity.getUserMarketingId();
                    }
                } else if(StringUtils.isNotBlank(vo.getBrowserUserId())) {
                    UserMarketingBrowserUserRelationEntity relationEntity = userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(ea, vo.getBrowserUserId());
                    if(relationEntity != null) {
                        userMarketingId = relationEntity.getUserMarketingId();
                    }
                }
            }

            UserMarketingAccountEntity userMarketingAccount = null;
            if(StringUtils.isNotBlank(userMarketingId)) {
                userMarketingAccount = userMarketingAccountDAO.getById(userMarketingId);
            }

            if(guideComponentTypes.contains(",4,")) {
                if(userMarketingAccount != null && StringUtils.isNotBlank(userMarketingAccount.getPhone())) {
                    CtaSettingEntity ctaSettingEntity = ctaSettingDAO.getByCtaId(ea, vo.getId());
                    if(ctaSettingEntity != null && ctaSettingEntity.getConfigValues() != null
                            && ctaSettingEntity.getConfigValues().containsKey(MarketingCommonSettingEnum.MINIAPP_ACCESSPERMISSIONS_SETTING.getName())) {
                        List<ObjectData> dataList = marketingLeadSyncRecordObjManager.getMarketingLeadSyncRecordObjList(ea, vo.getId(), String.valueOf(vo.getObjectType()), vo.getObjectId(), vo.getMarketingEventId());
                        if(CollectionUtils.isNotEmpty(dataList)) {
                            boolean hasLeads = dataList.stream().anyMatch(x -> StringUtils.isNotBlank(x.getString("lead_id")));
                            result.setHasPhone(hasLeads);
                        }
                    } else {
                        result.setHasPhone(true);
                    }
                }
            } else {
                result.setHasPhone(true);
            }

            if(guideComponentTypes.contains(",5,")) {
                if(userMarketingAccount != null) {
                    List<CtaQrCodeRelationEntity> qrCodeRelationEntityList = ctaQrCodeRelationDAO.getByUserMarketingId(ea, "wx_official_account", vo.getId(), vo.getObjectType(), vo.getObjectId(), userMarketingId);
                    if(CollectionUtils.isNotEmpty(qrCodeRelationEntityList)) {
                        result.setHasFollowOfficialAccount(true);
                    }
                }
            } else {
                result.setHasFollowOfficialAccount(true);
            }

            if(guideComponentTypes.contains(",6,")) {
                if(userMarketingAccount != null) {
                    List<CtaQrCodeRelationEntity> qrCodeRelationEntityList = ctaQrCodeRelationDAO.getByUserMarketingId(ea, "qywx_add_external_user", vo.getId(), vo.getObjectType(), vo.getObjectId(), userMarketingId);
                    if(CollectionUtils.isNotEmpty(qrCodeRelationEntityList)) {
                        result.setHasAddQywxExtendUser(true);
                    }
                }
            } else {
                result.setHasAddQywxExtendUser(true);
            }
        } else {
            result.setHasPhone(true);
            result.setHasNickNameAndAvatar(true);
            result.setHasAddQywxExtendUser(true);
            result.setHasFollowOfficialAccount(true);
            return Result.newSuccess(result);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<CtaRelationListResult> queryCtaRelationList(String ea, Integer fsUserId, QueryCtaDetailArg vo) {
        CtaRelationListResult result = new CtaRelationListResult();
        result.setCtaRelationDetailResults(Lists.newArrayList());
        List<CtaRelationEntityDTO> relationEntityDTOList = ctaRelationDAO.getRelationDetailsByCtaId(ea, vo.getId());
        if(CollectionUtils.isEmpty(relationEntityDTOList)) {
            return new Result<CtaRelationListResult>(SHErrorCode.SUCCESS, result);
        }
        for(CtaRelationEntityDTO dto : relationEntityDTOList) {
            QueryCtaRelationDetailResult detailResult = new QueryCtaRelationDetailResult();
            detailResult.setEa(ea);
            detailResult.setId(dto.getId());
            detailResult.setObjectType(dto.getObjectType());
            detailResult.setObjectId(dto.getObjectId());
            detailResult.setObjectName(dto.getObjectName());
            detailResult.setTriggerCount(0);
            detailResult.setWechatNicknameCount(0);
            detailResult.setWechatPhoneCount(0);
            detailResult.setMemberLoginCount(0);
            detailResult.setMemberRegisterCount(0);
            detailResult.setPublicAccountQrCount(0);
            detailResult.setPublicAccountFansCount(0);
            detailResult.setAddQywxFriendsCount(0);
            detailResult.setSubmitFormCount(0);
            result.getCtaRelationDetailResults().add(detailResult);
        }
        List<Map<String, Object>> detailCountList = getUserBehaviorCtaRelationDetailCountMap(ea, vo.getId());
        if(CollectionUtils.isEmpty(detailCountList)) {
            return new Result<CtaRelationListResult>(SHErrorCode.SUCCESS, result);
        }
        for(Map<String, Object> countMap : detailCountList) {
//            String objectTypeString = (String)countMap.get("cta_object_type");
            String objectId = (String)countMap.get("cta_object_id");
            if(StringUtils.isBlank(objectId)) {
                continue;
            }

//            Integer objectTypeTemp = Double.valueOf(objectTypeString).intValue();
//            switch (objectTypeString) {
//                case "hexagon":
//                    objectTypeTemp = ObjectTypeEnum.HEXAGON_PAGE.getType();
//            }
//            final Integer objectType = objectTypeTemp;

//            Optional<QueryCtaRelationDetailResult> opResult = result.getCtaRelationDetailResults().stream()
//                    .filter(x -> x.getObjectType().equals(objectType) && x.getObjectId().equals(objectId)).findFirst();
            Optional<QueryCtaRelationDetailResult> opResult = result.getCtaRelationDetailResults().stream()
                    .filter(x -> objectId.equals(x.getObjectId())).findFirst();
            if(!opResult.isPresent()) {
                continue;
            }
            QueryCtaRelationDetailResult detailResult = opResult.get();
            String actionType = (String)countMap.get("action_type");
            int count = ((Double)countMap.get("sum_action_count")).intValue();
            switch (actionType) {
                case "authorize_wechat_nickname":
                    detailResult.setWechatNicknameCount(count);
                    break;
                case "authorize_wechat_phone":
                    detailResult.setWechatPhoneCount(count);
                    break;
                case "login_member_account":
                    detailResult.setMemberLoginCount(count);
                    break;
                case "register_member":
                    detailResult.setMemberRegisterCount(count);
                    break;
                case "scan_wx_service_qr_code":
                    detailResult.setPublicAccountQrCount(count);
                    break;
                case "follow_wx_service":
                    detailResult.setPublicAccountFansCount(count);
                    break;
                case "add_qywx_employee":
                    detailResult.setAddQywxFriendsCount(count);
                    break;
                case "enroll_form_new":
                    detailResult.setSubmitFormCount(count);
                    break;
                case "trigger_cta_display_lead_generation_page":
                    detailResult.setTriggerCount(count);
                    break;
            }
        }
        return new Result<CtaRelationListResult>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<QueryCtaRelationCountResult> queryCtaRelationCount(String ea, Integer fsUserId, QueryCtaRelationCountArg vo) {
        QueryCtaRelationCountResult result = new QueryCtaRelationCountResult();
        result.setResult(new ObjectData());
        for(String id : vo.getIds()) {
            result.getResult().put(id, 0);
        }
        List<CtaRelationCountDTO> ctaRelationCountList = ctaRelationDAO.getRelationCountByCtaIds(ea, vo.getIds());
        if(CollectionUtils.isEmpty(ctaRelationCountList)) {
            return Result.newSuccess(result);
        }
        for(CtaRelationCountDTO countDTO : ctaRelationCountList) {
            result.getResult().put(countDTO.getCtaId(), countDTO.getCtaRelationCount());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<CtaListResult> listCta(String ea, Integer fsUserId, ListCtaArg vo) {
        Date time = vo.getTime() == null ? null : new Date(vo.getTime());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        if (StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }
        List<CtaEntityDTO> ctaEntities = null;
        CtaQueryParam param = new CtaQueryParam();
        param.setDate(time);
        param.setEa(ea);
        param.setStatus(vo.getStatus());
        param.setName(vo.getName());
        param.setMaterialTagFilter(vo.getMaterialTagFilter());
        param.setStandaloneButton(vo.getStandaloneButton());
        if(StringUtils.isNotBlank(vo.getTriggerTypes())) {
            List<String> triggerTypes = Lists.newArrayList(vo.getTriggerTypes().split(","));
            param.setTriggerTypes(triggerTypes);
        } else {
            param.setTriggerTypes(Lists.newArrayList());
        }
        if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            param.setUserId(fsUserId);
            ctaEntities = ctaDAO.createByMePage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            ctaEntities = ctaDAO.noGroupPage(param, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.CTA.getType());
            param.setUserId(fsUserId);
            param.setPermissionGroupIdList(Lists.newArrayList(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList())));
            ctaEntities = ctaDAO.getAccessiblePage(param, page);
        } else {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.CTA.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            param.setUserId(fsUserId);
            if (!permissionGroupIdSet.contains(vo.getGroupId())) {
                ctaEntities = Lists.newArrayList();
            } else {
                param.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.CTA.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                param.setGroupIdList(accessibleSubGroupIdList);
                ctaEntities = ctaDAO.getAccessiblePage(param, page);
            }
        }

        //分页返回结果
        CtaListResult ctaListResult = new CtaListResult();
        ctaListResult.setCurrentPage(page.getPageNo());
        ctaListResult.setPageSize(page.getPageSize());
        ctaListResult.setRecordSize(page.getTotalNum());
        ctaListResult.setPageTotal(page.getTotalPage() / page.getPageSize() + 1);
        ctaListResult.setTime(vo.getTime());
        ctaListResult.setTotalCount(page.getTotalNum());

        if (CollectionUtils.isEmpty(ctaEntities)) {
            ctaListResult.setCtaDetailResults(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, ctaListResult);
        }

        List<QueryCtaDetailResult> ctaDetailResults = Lists.newArrayList();
        for (CtaEntityDTO ctaEntity : ctaEntities) {
            QueryCtaDetailResult queryCtaDetailResult = BeanUtil.copy(ctaEntity, QueryCtaDetailResult.class);
            queryCtaDetailResult.setCreateTime(null == ctaEntity.getCreateTime() ? null : ctaEntity.getCreateTime().getTime());
            queryCtaDetailResult.setUpdateTime(null == ctaEntity.getUpdateTime() ? null : ctaEntity.getUpdateTime().getTime());
            queryCtaDetailResult.setCreateBy(String.valueOf(ctaEntity.getCreateBy()));
            queryCtaDetailResult.setUpdateBy(String.valueOf(ctaEntity.getUpdateBy()));
            queryCtaDetailResult.setTop(ctaEntity.isTop());
            ctaDetailResults.add(queryCtaDetailResult);
        }
        ctaListResult.setCtaDetailResults(ctaDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, ctaListResult);
    }

    @Override
    public Result<QueryCtaNameResult> queryCtaName(String ea, Integer fsUserId, QueryCtaNameArg vo) {
        Map<String, String> nameMap = Maps.newHashMap();
        QueryCtaNameResult result = new QueryCtaNameResult();
        result.setResult(nameMap);
        if(CollectionUtils.isEmpty(vo.getIds())) {
            return Result.newSuccess(result);
        }
        List<CtaEntity> ctaEntityList = ctaDAO.getByIds(ea, vo.getIds());
        ctaEntityList.forEach(c -> nameMap.put(c.getId(), c.getName()));
        return Result.newSuccess(result);
    }

    @Override
    public Result<ObjectGroupListResult> listCtaGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.CTA.getType(), "", null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(ctaDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(ctaDAO.queryAccessibleCount(ea, groupIdList, fsUserId));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(ctaDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(ctaDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }

    @Override
    public Result<Void> addCtaGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.CTA.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<EditObjectGroupResult> editCtaGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("CtaServiceImpl.editCtaGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("CtaServiceImpl.editCtaGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.CTA.getType());
    }

    @Override
    public Result<Void> deleteCtaGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("CtaServiceImpl.deleteCtaGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.CTA.getType());
    }

    @Override
    public Result<Void> setCtaGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
        List<CtaEntity> entityList = ctaDAO.getByIds(ea, arg.getObjectIdList());
        if (CollectionUtils.isEmpty(entityList)) {
            return Result.newError(SHErrorCode.CTA_NOT_FOUND);
        }
        if (entityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_CTA_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.CTA.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.CTA.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        for (CtaEntity ctaEntity : entityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.CTA.getType(), ctaEntity.getName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<CreateCtaWxQrCodeResult> createWxQrCode(String ea, Integer fsUserId, CreateCtaWxQrCodeArg arg) {
        WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
        wechatRequestDispatchArg.setEi(eieaConverter.enterpriseAccountToId(ea));
        wechatRequestDispatchArg.setWxAppId(arg.getWxAppId());
        wechatRequestDispatchArg.setMethod("POST");
        wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/qrcode/create?access_token=${access_token}");
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> scene = new HashMap<>();
        String state = String.format("CTA_%s", UUIDUtil.getUUID());
        scene.put("scene_str",  state);
        scene.put("fsEa", ea);
        scene.put("appId", arg.getWxAppId());
        scene.put("responseMsg", "");
        scene.put("tagNames", Lists.newArrayList());
        scene.put("ctaId", arg.getCtaId());
        scene.put("objectType", arg.getObjectType());
        scene.put("objectId", arg.getObjectId());
        scene.put("creator", fsUserId);
        map.put("scene", scene);
        body.put("action_name", "QR_STR_SCENE");
        body.put("action_info", map);
        body.put("expire_seconds", 86400);

        wechatRequestDispatchArg.setBody(JSON.toJSONString(body));
        Map<String, Object> objectMap = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<HashMap<String, Object>>() {
        });
        if (objectMap == null) {
            return Result.newSuccess(new CreateCtaWxQrCodeResult());
        }
        String ticket = UrlEncoder.urlComponentEncode(objectMap.get("ticket"));
        String showUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
        String qrCodeId = UUIDUtil.getUUID();
        CtaQrCodeRelationEntity entity = BeanUtil.copy(arg, CtaQrCodeRelationEntity.class);
        entity.setId(qrCodeId);
        entity.setEa(ea);
        entity.setQrCodeType("wx_official_account");
        entity.setState(state);
        entity.setStatus(0);
        entity.setQrCode(showUrl);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        ctaQrCodeRelationDAO.addCtaQrCodeRelation(entity);

        CreateCtaWxQrCodeResult result = new CreateCtaWxQrCodeResult();
        result.setQrCodeUrl(showUrl);
        result.setQrCodeId(qrCodeId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<CreateCtaWxQrCodeResult> createQywxQrCode(String ea, Integer fsUserId, CreateCtaQywxQrCodeArg arg) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.info("createQywxQrCode failed agentConfig=null ea:{}", ea);
            return Result.newSuccess(new CreateCtaWxQrCodeResult());
        }
        ContactMeConfigArg configArg = new ContactMeConfigArg(String.valueOf(arg.getFsUserId()));
        QywxAddFanQrCodeEntity addFanQrCodeEntity = qywxAddFanQrCodeDAO.getById(arg.getSceneId());
        if(addFanQrCodeEntity != null) {
            configArg = new ContactMeConfigArg(addFanQrCodeEntity.getType(), addFanQrCodeEntity.getRemark(), GsonUtil.fromJson(addFanQrCodeEntity.getUserId(), List.class), addFanQrCodeEntity.getChannelDesc());
        }
        configArg.setSkipVerify(true);
        configArg.setScene(2);
        String state = String.format("CTA_%s", UUIDUtil.get24UUID());
        configArg.setState(state);
        String accessToken = qywxManager.getAccessToken(ea);
        String configId = qywxManager.setContactMeConfig(accessToken, configArg);
        if (configId == null){
            log.info("createQywxQrCode failed setContactMeConfig return configId==null CreateCtaQywxQrCodeArg:{}", arg);
            return Result.newSuccess(new CreateCtaWxQrCodeResult());
        }
        GetContactMeResult contactMeResult = qywxManager.getContanctMe(accessToken, configId);
        if (contactMeResult == null || !contactMeResult.isSuccess() || StringUtils.isEmpty(contactMeResult.getContactWay().getQrCode())){
            log.info("createQywxQrCode failed getContanctMe return contactMeResult==null CreateCtaQywxQrCodeArg:{}", arg);
            return Result.newSuccess(new CreateCtaWxQrCodeResult());
        }
        String showUrl = contactMeResult.getContactWay().getQrCode();
        String qrCodeId = UUIDUtil.getUUID();
        CtaQrCodeRelationEntity entity = BeanUtil.copy(arg, CtaQrCodeRelationEntity.class);
        entity.setId(qrCodeId);
        entity.setEa(ea);
        entity.setQrCodeType("qywx_add_external_user");
        entity.setConfigId(configId);
        entity.setState(state);
        entity.setStatus(0);
        entity.setQrCode(showUrl);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        ctaQrCodeRelationDAO.addCtaQrCodeRelation(entity);

        CreateCtaWxQrCodeResult result = new CreateCtaWxQrCodeResult();
        result.setQrCodeUrl(showUrl);
        result.setQrCodeId(qrCodeId);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> pollingWxQrCodeStatus(String ea, Integer fsUserId, PollingQrCodeStatusArg arg) {
        CtaQrCodeRelationEntity ctaQrCodeRelationEntity = ctaQrCodeRelationDAO.getById(ea, arg.getQrCodeId());
        if(ctaQrCodeRelationEntity == null
                || (ctaQrCodeRelationEntity.getStatus() != null && ctaQrCodeRelationEntity.getStatus().equals(1))) {
            return Result.newSuccess(true);
        } else {
            return Result.newSuccess(false);
        }
    }

    @Override
    public Result<Boolean> pollingQywxQrCodeStatus(String ea, Integer fsUserId, PollingQrCodeStatusArg arg) {
        CtaQrCodeRelationEntity ctaQrCodeRelationEntity = ctaQrCodeRelationDAO.getById(ea, arg.getQrCodeId());
        if(ctaQrCodeRelationEntity == null
                || (ctaQrCodeRelationEntity.getStatus() != null && ctaQrCodeRelationEntity.getStatus().equals(1))) {
            return Result.newSuccess(true);
        } else {
            return Result.newSuccess(false);
        }
    }

    @Override
    public void recordUpdateMiniappAccessPermissionsData(MiniappAccessPermissionsDataArg arg, MiniappAccessPermissionsDataResult result) {
        CtaEntity ctaEntity = ctaDAO.queryCtaDetail(arg.getFsEa(), arg.getCtaId());
        if(ctaEntity == null) {
            return;
        }
        MarketingUserActionEvent marketingUserActionEvent = new MarketingUserActionEvent();
        marketingUserActionEvent.setEa(arg.getFsEa());
        marketingUserActionEvent.setChannelType(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType());
        marketingUserActionEvent.setActionTime(System.currentTimeMillis());
        marketingUserActionEvent.setWxAppId(arg.getAppId());
        marketingUserActionEvent.setWxOpenId(arg.getOpenid());
        marketingUserActionEvent.setMarketingEventId(arg.getMarketingEventId());
        Map<String, Object> extensionParams = Maps.newHashMap();
        extensionParams.put("ctaId", ctaEntity.getId());
        extensionParams.put("ctaName", ctaEntity.getName());
        extensionParams.put("ctaObjectType", arg.getObjectType());
        extensionParams.put("ctaObjectId", arg.getObjectId());
        marketingUserActionEvent.setObjectType(10010);
        Optional<ObjectData> wechatFan= crmV2Manager.getWechatFanByOpenId(arg.getFsEa(), arg.getAppId(), arg.getOpenid());
        if(wechatFan.isPresent()) {
            marketingUserActionEvent.setObjectId(wechatFan.get().getId());
            extensionParams.put("objectName", wechatFan.get().getName());
        }
        marketingUserActionEvent.setExtensionParams(extensionParams);
        if(StringUtils.isNotBlank(arg.getName()) || StringUtils.isNotBlank(arg.getAvatar())) {
            marketingUserActionEvent.setActionType(2033);
            marketingRecordActionSender.send(marketingUserActionEvent);
        }
        if(StringUtils.isNotBlank(arg.getPhone())) {
            marketingUserActionEvent.setActionType(2032);
            marketingRecordActionSender.send(marketingUserActionEvent);
        }
    }

    @Override
    public void recordGetAuthorizedUserInformation(String ea, PubPlatAuthComponentArg arg, WxPublicPlatformAuthorizeUserInfo result) {
        CtaEntity ctaEntity = ctaDAO.queryCtaDetail(ea, arg.getCtaId());
        if(ctaEntity == null) {
            return;
        }
        MarketingUserActionEvent marketingUserActionEvent = new MarketingUserActionEvent();
        marketingUserActionEvent.setEa(ea);
        marketingUserActionEvent.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
        marketingUserActionEvent.setActionTime(System.currentTimeMillis());
        marketingUserActionEvent.setWxAppId(arg.getWxappid());
        marketingUserActionEvent.setWxOpenId(arg.getOpenId());
        Map<String, Object> extensionParams = Maps.newHashMap();
        extensionParams.put("ctaId", ctaEntity.getId());
        extensionParams.put("ctaName", ctaEntity.getName());
        extensionParams.put("ctaObjectType", arg.getObjectType());
        extensionParams.put("ctaObjectId", arg.getObjectId());
        marketingUserActionEvent.setObjectType(10010);
        Optional<ObjectData> wechatFan= crmV2Manager.getWechatFanByOpenId(ea, arg.getWxappid(), arg.getOpenId());
        if(wechatFan.isPresent()) {
            marketingUserActionEvent.setObjectId(wechatFan.get().getId());
            extensionParams.put("objectName", wechatFan.get().getName());
        }
        marketingUserActionEvent.setExtensionParams(extensionParams);
        if(StringUtils.isNotBlank(result.getNickName()) || StringUtils.isNotBlank(result.getHeadImgUrl())) {
            marketingUserActionEvent.setActionType(2033);
            marketingRecordActionSender.send(marketingUserActionEvent);
        }
    }

    @Override
    public void recordMemberRegister(MarketingUserActionChannelType channelType, CustomizeFormDataEnrollArg arg, String memberId) {
        MarketingUserActionEvent marketingUserActionEvent = getMarketingUserActionEventByEnroll(channelType, arg);
        if (marketingUserActionEvent == null) {
            return;
        }
        marketingUserActionEvent.setObjectType(10011);
        marketingUserActionEvent.setObjectId(memberId);
        marketingUserActionEvent.setActionType(2030);
        marketingRecordActionSender.send(marketingUserActionEvent);
    }

    @Override
    public void recordMemberLogin(MarketingUserActionChannelType channelType, CustomizeFormDataEnrollArg arg, String memberId) {
        MarketingUserActionEvent marketingUserActionEvent = getMarketingUserActionEventByEnroll(channelType, arg);
        if (marketingUserActionEvent == null) {
            return;
        }
        log.info("recordMemberLogin, marketingUserActionEvent:{}", marketingUserActionEvent);
        marketingUserActionEvent.setObjectType(10011);
        marketingUserActionEvent.setObjectId(memberId);
        marketingUserActionEvent.setActionType(2031);
        try {
            marketingRecordActionSender.send(marketingUserActionEvent); }
        catch (Exception e) {
            log.error("recordMemberLogin, marketingUserActionEvent:{}", marketingUserActionEvent, e);
        }
    }

    @Override
    public void recordCustomerDataEnroll(MarketingUserActionChannelType channelType, CustomizeFormDataEnrollArg arg) {
        MarketingUserActionEvent marketingUserActionEvent = getMarketingUserActionEventByEnroll(channelType, arg);
        if (marketingUserActionEvent == null) {
            return;
        }
        marketingUserActionEvent.setObjectType(arg.getObjectType());
        marketingUserActionEvent.setObjectId(arg.getObjectId());
        marketingUserActionEvent.setActionType(61);
        marketingRecordActionSender.send(marketingUserActionEvent);
    }

    @Override
    public boolean isFsCustomizeFormDataEnrollMemberEnable(String ea) {
        return isAnyMatchEa(fsCustomizeFormDataEnrollMemberEnable, ea);
    }

    @Override
    public Result saveCtaSetting(String ea, Integer fsUserId, AddCtaSettingArg vo) {
        saveCtaSetting(ea,fsUserId, vo.getCtaId(), vo.getSettings());
        return Result.newSuccess(true);
    }

    private boolean saveCtaSetting(String ea, Integer fsUserId, String ctaId, FlexibleJson settings) {
        CtaSettingEntity dbData = ctaSettingDAO.getByCtaId(ea, ctaId);
        CtaSettingEntity ctaSettingEntity = new CtaSettingEntity();
        String id = dbData == null ? UUIDUtil.getUUID() : dbData.getId();
        ctaSettingEntity.setId(id);
        ctaSettingEntity.setCtaId(ctaId);
        ctaSettingEntity.setEa(ea);
        ctaSettingEntity.setConfigValues(settings);
        ctaSettingEntity.setCreateBy(fsUserId);
        ctaSettingEntity.setCreateTime(new Date());
        ctaSettingEntity.setUpdateBy(fsUserId);
        ctaSettingEntity.setUpdateTime(new Date());
        if(dbData == null) {
            ctaSettingDAO.add(ctaSettingEntity);
        } else {
            ctaSettingDAO.update(ctaSettingEntity);
        }
        return true;
    }

    @Override
    public Result getCtaSetting(String ea, Integer fsUserId, QueryCtaDetailArg vo) {
        CtaSettingEntity ctaSettingEntity = ctaSettingDAO.getByCtaId(ea, vo.getId());
        return Result.newSuccess(ctaSettingEntity);
    }

    @Override
    public Result<QueryCtaRelationCountResult> queryCtaStaticData(String ea, Integer fsUserId, QueryCtaStaticDataArg vo) {
        QueryCtaRelationCountResult result = new QueryCtaRelationCountResult();
        result.setResult(new ObjectData());
        List<String> objectIds = getObjectIds(ea, fsUserId, vo.getObjectType(), vo.getObjectId());
        if(CollectionUtils.isEmpty(objectIds)) {
            return Result.newSuccess(result);
        }
        Integer objectType = vo.getObjectType();
        if(objectType.equals(ObjectTypeEnum.HEXAGON_SITE.getType())) {
            objectType = ObjectTypeEnum.HEXAGON_PAGE.getType();
        }
        List<CtaRelationEntityDTO> relationList = ctaRelationDAO.getCtaRelationList(ea, objectType, objectIds);
        if(CollectionUtils.isEmpty(relationList)) {
            return Result.newSuccess(result);
        }
        List<String> catIds = relationList.stream().map(CtaRelationEntityDTO::getCtaId).collect(Collectors.toList());
        List<CtaStaticsData> ctaStaticsDataList = Lists.newArrayList();
        Map<String, Map<String, Integer>> ctaDataSumMap = getCtaStaticDataSumMap(ea, objectIds, catIds);
        for(Map.Entry<String, Map<String, Integer>> ctaSumData : ctaDataSumMap.entrySet()) {
            String ctaId = ctaSumData.getKey();
            CtaRelationEntityDTO ctaRelationEntity = relationList.stream().filter(r -> r.getCtaId().equals(ctaId)).findFirst().orElse(null);
            CtaStaticsData ctaStaticsData = new CtaStaticsData();
            ctaStaticsData.setCtaId(ctaId);
            ctaStaticsData.setCtaName(ctaRelationEntity == null ? "" : ctaRelationEntity.getCtaName());
            ctaStaticsData.setTriggerCount(0);
            ctaStaticsData.setMemberRegisterCount(0);
            ctaStaticsData.setPublicAccountFansCount(0);
            ctaStaticsData.setMemberLoginCount(0);
            ctaStaticsData.setAddQywxFriendsCount(0);
            ctaStaticsData.setSubmitFormCount(0);
            ctaStaticsData.setWechatNicknameCount(0);
            ctaStaticsData.setWechatPhoneCount(0);
            ctaStaticsData.setPublicAccountQrCount(0);
            for(Map.Entry<String, Integer> entry : ctaSumData.getValue().entrySet()) {
                Integer sumValue = entry.getValue();
                switch (entry.getKey()) {
                    case "authorize_wechat_nickname":
                        ctaStaticsData.setWechatNicknameCount(sumValue);
                        break;
                    case "authorize_wechat_phone":
                        ctaStaticsData.setWechatPhoneCount(sumValue);
                        break;
                    case "login_member_account":
                        ctaStaticsData.setMemberLoginCount(sumValue);
                        break;
                    case "register_member":
                        ctaStaticsData.setMemberRegisterCount(sumValue);
                        break;
                    case "scan_wx_service_qr_code":
                        ctaStaticsData.setPublicAccountQrCount(sumValue);
                        break;
                    case "follow_wx_service":
                        ctaStaticsData.setPublicAccountFansCount(sumValue);
                        break;
                    case "add_qywx_employee":
                        ctaStaticsData.setAddQywxFriendsCount(sumValue);
                        break;
                    case "enroll_form_new":
                        ctaStaticsData.setSubmitFormCount(sumValue);
                        break;
                    case "trigger_cta_display_lead_generation_page":
                        ctaStaticsData.setTriggerCount(sumValue);
                        break;
                }
            }
            ctaStaticsDataList.add(ctaStaticsData);
        }
        result.getResult().put("ctaStaticsDataList", ctaStaticsDataList);
        return Result.newSuccess(result);
    }

    @Nullable
    private MarketingUserActionEvent getMarketingUserActionEventByEnroll(MarketingUserActionChannelType channelType, CustomizeFormDataEnrollArg arg) {
        CtaEntity ctaEntity = ctaDAO.queryCtaDetail(arg.getEa(), arg.getCtaId());
        if(ctaEntity == null) {
            return null;
        }
        MarketingUserActionEvent marketingUserActionEvent = BeanUtil.copy(arg, MarketingUserActionEvent.class);
        marketingUserActionEvent.setEa(arg.getEa());
        marketingUserActionEvent.setChannelType(channelType.getChannelType());
        marketingUserActionEvent.setActionTime(System.currentTimeMillis());
        marketingUserActionEvent.setWxAppId(arg.getWxAppId());
        marketingUserActionEvent.setWxOpenId(arg.getOpenId());
        marketingUserActionEvent.setFingerPrint(arg.getFingerPrint());
        marketingUserActionEvent.setUid(arg.getUid());
        Map<String, Object> extensionParams = Maps.newHashMap();
        extensionParams.put("ctaId", ctaEntity.getId());
        extensionParams.put("ctaName", ctaEntity.getName());
        if(arg.getParentObjectType() != null) {
            extensionParams.put("ctaObjectType", arg.getParentObjectType());
        } else {
            extensionParams.put("ctaObjectType", arg.getObjectType());
        }
        if(StringUtils.isNotBlank(arg.getParentObjectId())) {
            extensionParams.put("ctaObjectId", arg.getParentObjectId());
        } else {
            extensionParams.put("ctaObjectId", arg.getObjectId());
        }
        marketingUserActionEvent.setExtensionParams(extensionParams);
        return marketingUserActionEvent;
    }

    public Map<String, Map<String, Integer>> getUserBehaviorCtaCountMap(String ea, List<String> ctaIds) {
        if (CollectionUtils.isEmpty(ctaIds)) {
            return Maps.newHashMap();
        }
        AggregateParameterArg aggregateParameterArg = new AggregateParameterArg();
        AggregateParameterArg.GroupByParameter groupByParameter = new AggregateParameterArg.GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList("cta_id", "action_type"));
        AggregateParameterArg.AggFunction aggFunction = new AggregateParameterArg.AggFunction();
        aggFunction.setAggField("action_count");
        aggFunction.setAggFunction("sum");
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunction));
        aggregateParameterArg.setGroupByParameter(groupByParameter);

        List<com.fxiaoke.crmrestapi.common.data.Filter> filters = Lists.newArrayList();
        com.fxiaoke.crmrestapi.common.data.Filter filter = new com.fxiaoke.crmrestapi.common.data.Filter();
        filter.setFieldName("cta_id");
        filter.setFieldValues(ctaIds);
        filter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
        filters.add(filter);
        aggregateParameterArg.setFilters(filters);
        com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult> result = crmV2Manager.aggregateQuery(ea, CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName(), aggregateParameterArg);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData().getDataList())) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, Integer>> resultMap = Maps.newHashMap();
        for (Map<String, Object> stringObjectMap : result.getData().getDataList()) {
            String ctaId = (String)stringObjectMap.get("cta_id");
            if(!resultMap.containsKey(ctaId)) {
                resultMap.put(ctaId, Maps.newHashMap());
            }
            String actionType = (String)stringObjectMap.get("action_type");
            int count = ((Double)stringObjectMap.get("sum_action_count")).intValue();
            resultMap.get(ctaId).put(actionType, count);
        }
        return resultMap;
    }

    public List<Map<String, Object>> getUserBehaviorCtaRelationDetailCountMap(String ea, String ctaId) {
        AggregateParameterArg aggregateParameterArg = new AggregateParameterArg();
        AggregateParameterArg.GroupByParameter groupByParameter = new AggregateParameterArg.GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList("cta_id", "action_type", "cta_object_id"));
        AggregateParameterArg.AggFunction aggFunction = new AggregateParameterArg.AggFunction();
        aggFunction.setAggField("action_count");
        aggFunction.setAggFunction("sum");
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunction));
        aggregateParameterArg.setGroupByParameter(groupByParameter);

        List<com.fxiaoke.crmrestapi.common.data.Filter> filters = Lists.newArrayList();
        com.fxiaoke.crmrestapi.common.data.Filter filter = new com.fxiaoke.crmrestapi.common.data.Filter();
        filter.setFieldName("cta_id");
        filter.setFieldValues(Lists.newArrayList(ctaId));
        filter.setOperator(Filter.OperatorContants.EQ);
        filters.add(filter);
        aggregateParameterArg.setFilters(filters);
        com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult> result = crmV2Manager.aggregateQuery(ea, CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName(), aggregateParameterArg);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData().getDataList())) {
            return Lists.newArrayList();
        }
        return result.getData().getDataList();
    }

    public Map<String, Map<String, Integer>> getCtaStaticDataSumMap(String ea, List<String> objectIds, List<String> ctaIds) {
        if (CollectionUtils.isEmpty(ctaIds)) {
            return Maps.newHashMap();
        }
        AggregateParameterArg aggregateParameterArg = new AggregateParameterArg();
        AggregateParameterArg.GroupByParameter groupByParameter = new AggregateParameterArg.GroupByParameter();
        groupByParameter.setGroupBy(Lists.newArrayList("cta_id", "action_type"));
        AggregateParameterArg.AggFunction aggFunction = new AggregateParameterArg.AggFunction();
        aggFunction.setAggField("action_count");
        aggFunction.setAggFunction("sum");
        groupByParameter.setAggFunctions(Lists.newArrayList(aggFunction));
        aggregateParameterArg.setGroupByParameter(groupByParameter);

        List<com.fxiaoke.crmrestapi.common.data.Filter> filters = Lists.newArrayList();
        com.fxiaoke.crmrestapi.common.data.Filter filter = new com.fxiaoke.crmrestapi.common.data.Filter();
        filter.setFieldName("cta_id");
        filter.setFieldValues(ctaIds);
        filter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
        filters.add(filter);
        filter = new com.fxiaoke.crmrestapi.common.data.Filter();
        filter.setFieldName("cta_object_id");
        filter.setFieldValues(objectIds);
        filter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
        filters.add(filter);
        aggregateParameterArg.setFilters(filters);
        com.fxiaoke.crmrestapi.common.result.Result<AggregateQueryResult> result = crmV2Manager.aggregateQuery(ea, CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName(), aggregateParameterArg);
        if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData().getDataList())) {
            return Maps.newHashMap();
        }
        Map<String, Map<String, Integer>> resultMap = Maps.newHashMap();
        for (Map<String, Object> stringObjectMap : result.getData().getDataList()) {
            String ctaId = (String)stringObjectMap.get("cta_id");
            if(!resultMap.containsKey(ctaId)) {
                resultMap.put(ctaId, Maps.newHashMap());
            }
            String actionType = (String)stringObjectMap.get("action_type");
            int count = ((Double)stringObjectMap.get("sum_action_count")).intValue();
            resultMap.get(ctaId).put(actionType, count);
        }
        return resultMap;
    }

    private boolean isAnyMatchEa(String eaList, String ea) {
        if (StringUtils.isNotEmpty(eaList)) {
            return Arrays.asList(eaList.split(",")).stream()
                    .anyMatch(e -> e.replace("\"", "").equals(ea));
        }
        return false;
    }

    private Result<CustomizeFormDataUserEntity> checkUserIsEnrolled(String marketingEventId, CustomizeFormDataEntity customizeFormDataEntity, String parentObjectId, Integer parentObjectType, String wxAppId,
                                                                                       String openId, String fingerPrint, String ea, Integer fsUserId, String uid) {
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntityList = null;
        if (StringUtils.isNotBlank(uid)) {
            customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByUidAndParentObjectId(uid, customizeFormDataEntity.getId(), parentObjectId, parentObjectType);
        } else if (StringUtils.isNotBlank(openId) && StringUtils.isNotBlank(wxAppId)) {
            customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByOpenIdAndParentObjectId(openId, wxAppId, customizeFormDataEntity.getId(), parentObjectId, parentObjectType);
        } else if (StringUtils.isNotBlank(fingerPrint)) {
            customizeFormDataUserEntityList = customizeFormDataUserDAO
                    .queryCustomizeFormDataUsersByFingerPrintAndParentObjectIds(marketingEventId, fingerPrint, customizeFormDataEntity.getId(), Lists.newArrayList(parentObjectId), null);
        } else if (StringUtils.isNotBlank(ea) && fsUserId != null) {
            customizeFormDataUserEntityList = customizeFormDataUserDAO.queryCustomizeFormDataUserByFsUserIdAndParentObjectId(marketingEventId, ea, fsUserId, customizeFormDataEntity.getId(), parentObjectId, parentObjectType);
        }
        return CollectionUtils.isNotEmpty(customizeFormDataUserEntityList) ? new Result<>(SHErrorCode.USERS_HAVE_REGISTERED, customizeFormDataUserEntityList.get(0))
                : new Result<>(SHErrorCode.SUCCESS);
    }

    private List<String> getObjectIds (String ea, Integer fsUserId, Integer objectType, String objectId) {
        List<String> ids = Lists.newArrayList();
        switch (objectType) {
            case 4:
            case 6:
            case 27:
                ids.add(objectId);
                break;
            case 26:
                List<HexagonPageEntity> pageList = hexagonPageDAO.getByHexagonSiteId(objectId);
                if(CollectionUtils.isNotEmpty(pageList)) {
                    ids.addAll(pageList.stream().map(HexagonPageEntity::getId).collect(Collectors.toList()));
                }
                break;
        }
        return ids;
    }

//    enum UserBehaviorRecordObjectTypeEnum {
//        ARTICLE(6, "article", "文章"),
//        HEXAGON_SITE(26, "hexagon", "微页面"),
//        HEXAGON_PAGE(27, "hexagon", "微页面"),
//        PRODUCT(4, "product", "产品"),
//        FORM(16, "form", "表单"),
//        LIVE(30, "live", "直播"),
//        CONFERENCE(33, "conference", "会议"),
//        ACTIVITY(13, "activity", "活动"),
//        QYWX_EMPLOYEE(10005, "qywx_employee", "企微员工"),
//        QYWX_GROUP(10006, "qywx_group", "企微群"),
//        OFFICIAL_WEBSITE(28, "official_website", "官网"),
//        KEYWORD(10004, "keyword", "关键词"),
//        EMAIL(31, "email", "邮件"),
//        OFFICIAL_WEBSITE_EVENT(32, "official_website_event", "官网事件"),
//        COUPON(35, "coupon", "优惠券"),
//        LANDING_PAGE(1003, "landing_page", "落地页"),
//        CHAT_ONLINE(10002, "chat_online", "在线客服"),
//        MARKETING_FILE(34, "marketing_file", "文件"),
//        OFFICIAL_ACCOUNT(1000, "official_account", "公众号"),
//        OTHER(-1, "other", "其他"),
//        ;
//    }
}
