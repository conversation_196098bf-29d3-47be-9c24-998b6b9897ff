package com.facishare.marketing.provider.util;

import com.alibaba.fastjson.JSON;

import java.io.InputStream;

/**
 * 从文件中 获取对象json描述
 */
public class ObjDescribeUtil {

    public static String getObjectDescribe(String filePath) {

        try (InputStream inputStream = ObjDescribeUtil.class.getResourceAsStream(filePath)) {

            return JSON.parseObject(inputStream, String.class);

        } catch (Exception e) {
            throw new RuntimeException("Fail to getObjectDescribe", e);
        }
    }

}
