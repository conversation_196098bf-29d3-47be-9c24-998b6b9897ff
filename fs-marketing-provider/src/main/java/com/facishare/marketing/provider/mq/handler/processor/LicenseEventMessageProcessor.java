package com.facishare.marketing.provider.mq.handler.processor;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.MigrateEasArg;
import com.facishare.marketing.api.service.MigrateService;
import com.facishare.marketing.audit.log.MarketingAuditLog;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.sms.SmsOrderPackageEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.entity.sms.OrderEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.ai.AiChatManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.sms.QuotaManager;
import com.facishare.marketing.provider.mq.handler.audit.log.convert.LicenseMqMessage2Convert;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.open.app.center.api.model.AppViewDO;
import com.facishare.open.app.center.api.model.EmployeeRange;
import com.facishare.open.app.center.api.model.enums.AppAccessTypeEnum;
import com.facishare.open.app.center.api.model.enums.AppComponentTypeEnum;
import com.facishare.open.app.center.api.model.vo.UserCanViewListVO;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.AppEaVisibleService;
import com.facishare.open.app.center.api.service.OpenFsUserAppViewService;
import com.facishare.open.app.center.api.service.QueryAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.paas.license.message.LicenseMqMessage2;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LicenseEventMessageProcessor {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MigrateService migrateService;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private AppEaVisibleService appEaVisibleService;
    @Autowired
    private OpenFsUserAppViewService openFsUserAppViewService;
    @Autowired
    private QueryAppAdminService queryAppAdminService;
    @Autowired
    private QuotaManager quotaManager;
    @Autowired
    private CrmV2Manager crmManager;
    @Autowired
    private ContentPropagationDetailObjManager contentPropagationDetailObjManager;
    @Autowired
    private EmployeePromoteDetailObjManager employeePromoteDetailObjManager;
    @Autowired
    private OtherObjectDescribeManager otherObjectDescribeManager;
    @Autowired
    private DailitongHexagonManger dailitongHexagonManger;
    @Value("${marketing_appid}")
    private String appId;
    @Value("${marketing_app_web_component}")
    private String marketingAppWebComponent;
    @ReloadableProperty("sms.order.ea")
    private String orderEa;
    @ReloadableProperty("order.resource.count.api.name")
    private String orderResouceCountApiName;
    @ReloadableProperty("order.resource.money.api.name")
    private String orderResourceMoneyApiName;
    @Autowired
    private RedisManager redisManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private AiChatManager aiChatManager;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    private static final String BEHAVIOR_INTEGRATION_VERSION = "behavior_integral_app";
    private static final String MARKETING_INTEGRATION_VERSION = "marketing_integration_app";
    private final String SMS_LICENSE_MODULE_CODE = "note_service_app";
    private final String RESOURCE_PACKAGE_LICENSE = "extention_package";    //购买资源包的LICENSE VERSION
    private final String SHAREGPT_APP = "sharegpt_app";    //购买资源包的LICENSE VERSION


    @MarketingAuditLog(bizName = "LicenseEventMessageHandler", entityClass = LicenseMqMessage2.class, convertClass = LicenseMqMessage2Convert.class,
            messageId = "#messageId", extra9 = "#extra9", cost9 = "#cost9", ea = "#ea", extra = "#extra")
    public void processMqMessage(LicenseMqMessage2 event) {
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(event.getTenantId()));
        log.info("mq-receive-license, ea:{}, event:{}", ea, event);
        String licenseVersion = event.getLicenseVersion();
        Set<String> moduleCodes = event.getModuleCodes();
        moduleCodes.add(licenseVersion);

        if (StringUtils.isEmpty(licenseVersion)) {
            log.info("handleMessage licenseVersion is null");
            return;
        }

        //购买营销通订单
        if (appVersionManager.isContainMarketingLicense(licenseVersion) || isContainMarketingModules(moduleCodes)) {
            String currentVersion = appVersionManager.getCurrentAppVersion(ea);
            log.info("getCurrentAppVersion is:{} ea:{}", currentVersion, ea);
            //兼容收到licenseMQ, 但是查询license有没查到得情况
            if (StringUtils.isEmpty(currentVersion)) {
                if (appVersionManager.getVersionByModuleCodes(moduleCodes) != null) {
                    currentVersion = appVersionManager.getVersionByModuleCodes(moduleCodes);
                }
            }
            //初始化营销通
            if (appVersionManager.isCurrentCloud(ea)) {
                enterpriseInfoManager.updateCache(ea, true);
                log.info("recv marketing license initMarketingData ea:{} currentVersion:{}", ea, currentVersion);
                settingManager.initMarketingData(ea, -10000, currentVersion);
                configAppCenterVisible(ea);
                contentPropagationDetailObjManager.getOrCreateObjDescribe(ea);
                userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());
                employeePromoteDetailObjManager.getOrCreateObjDescribe(ea);
                userRoleManager.initObjPrivilege(ea, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());

                contentPropagationDetailObjManager.tryUpdateCustomFieldLabel(ea);
                employeePromoteDetailObjManager.tryUpdateCustomFieldLabel(ea);

                otherObjectDescribeManager.tryAddFileToMarketingEventObj(ea);
                otherObjectDescribeManager.tryAddFileToMarketingActivityObj(ea);
            }
        }

        if (licenseVersion.equals(BEHAVIOR_INTEGRATION_VERSION)) {
            if (appVersionManager.isCurrentCloud(ea)) {
                log.info("recv marketing license init BEHAVIOR_INTEGRATION_VERSION  ea:{}", ea);
                MigrateEasArg migrateEasArg = new MigrateEasArg();
                migrateEasArg.setEas(ImmutableList.of(ea));
                migrateService.migrateMaterial(migrateEasArg);
            }
        }
        if (licenseVersion.equals(MARKETING_INTEGRATION_VERSION) || licenseVersion.equals(VersionEnum.MARKETING_AD_PLUGIN_APP.getVersion())) {
            //购买营销一体化或者广告营销插件的
            if (appVersionManager.isCurrentCloud(ea)) {
                log.info("recv marketing license init initMarketingAdObjetPrivilege  ea:{}", ea);
                userRoleManager.initMarketingAdObjetPrivilege(ea);
                otherObjectDescribeManager.tryAddFileToMarketingEventObj(ea);
                otherObjectDescribeManager.tryAddFileToMarketingActivityObj(ea);
                adCommonManager.initAdvertiseConfig(ea);
                marketingPromotionSourceObjManager.addMarketingKeywordField(ea);
                advertisingDetailsObjManager.getOrCreateObjDescribe(ea);
                marketingEventManager.addOCPCLaunchField(ea);
            }
        }
        if (licenseVersion.equals(RESOURCE_PACKAGE_LICENSE) && moduleCodes.contains(SMS_LICENSE_MODULE_CODE)) {
            //购买短信
            // license消息是按照 订单产品 发送消息, 一个订单可能存在多个订单产品
            // 此处处理逻辑直接按照订单查询对象下所有订单产品进行处理
            // 必须保证本地订单数据不可重复
            if (appVersionManager.isCurrentCloud(ea)) {
                log.info("recv marketing license init SMS_LICENSE_MODULE_CODE  ea:{}", ea);
                String key = event.getTenantId() + ":" + event.getOrderNumber();
                // 时间长一点, 避免对象接口抽风
                boolean lock = redisManager.lock(key, 15);
                if (lock) {
                    try {
                        snycCrmOder(event.getOrderNumber(), Integer.parseInt(event.getTenantId()));
                    } catch (Exception e) {
                        log.error("snycCrmOder fail", e);
                    } finally {
                        redisManager.unLock(key);
                    }
                }
            }
        }
        //购买代理通渠道伙伴招募套件&管理
        if (appVersionManager.isCurrentCloud(ea)) {
            log.info("recv marketing license init initChannelPartnerRecruitmentIndustry  ea:{}", ea);
            //复制代理通客户下单渠道伙伴招募套件所需要的微页面
            dailitongHexagonManger.copyHexagonSite(ea, moduleCodes);
        }

        if (licenseVersion.equals(SHAREGPT_APP)) {
            // 收到sharegpt的license，进行默认的业务类型关联
            log.info("recv sharegpt license, assignRecord. ea:{}", ea);
            aiChatManager.assignRecord(ea);
        }
    }

    private boolean isContainMarketingModules(Collection<String> moduleCodes) {
        if (CollectionUtils.isEmpty(moduleCodes)) {
            return false;
        }
        for (String moduleCode : moduleCodes) {
            if (appVersionManager.isContainMarketingLicense(moduleCode)) {
                return true;
            }
        }

        return false;
    }

    public void snycCrmOder(String orderId, Integer tenantId) {
        String orderProdcutApiName = "SalesOrderProductObj";

        if (StringUtils.isEmpty(orderId) || tenantId == null) {
            log.warn("snycCrmOder failed orderId:{} tenantId:{}", orderId, tenantId);
            return;
        }

        //判断该订单是否已经存在
        OrderEntity orderEntity = quotaManager.queryOrderByCrmOrderId(orderId, eieaConverter.enterpriseIdToAccount(tenantId));
        if (orderEntity != null) {
            log.info("sms order is exist crmOrderId:{}", orderId);
            return;
        }

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("order_id", Lists.newArrayList(orderId), "EQ");
        List<ObjectData> crmObjectVoList = crmManager.getList(orderEa, -10000, orderProdcutApiName, searchQuery).getDataList();
        if (crmObjectVoList == null || crmObjectVoList.isEmpty()) {
            log.info("CrmLicenseEventListener.snycCrmOder orderId:{} tenantId:{} SalesOrderProductObj getList null", orderId, tenantId);
            return;
        }

        Integer quantity = 0;          //购买短信份数
        Integer resourceCount = 0;     //购买短息总数
        Float purchaseAmount = 0.0f;   //购买费用
        String orderPackage = "";
        String creatorName = null;
        Integer creatorId = null;
        for (ObjectData objectData : crmObjectVoList) {
            if (!org.apache.commons.lang.StringUtils.equals("note_service_app_limit", objectData.get("UDRef1__c").toString())) {
                continue;
            }

            Integer subOrderResouceCount = Double.valueOf(objectData.get(orderResouceCountApiName).toString()).intValue();
            resourceCount += subOrderResouceCount;
            purchaseAmount += Float.parseFloat(objectData.get(orderResourceMoneyApiName).toString());
            int subOrderQuantity = (Float.valueOf(objectData.get("quantity").toString())).intValue();
            quantity += subOrderQuantity;

            SmsOrderPackageEnum smsOrderPackageEnum = SmsOrderPackageEnum.getPackageByCount(subOrderResouceCount / subOrderQuantity);
            String subOrderPackage = smsOrderPackageEnum.getDescription() + "*" + subOrderQuantity + ",";
            orderPackage = orderPackage + subOrderPackage;
            if (creatorName == null) {
                creatorName = objectData.getCreateByName();
            }
            if (creatorId == null) {
                creatorId = objectData.getCreateBy();
            }
        }
        //兼容creatorId和creatorName为空的情况
        if (creatorId == null) {
            creatorId = -10000;
        }
        if (StringUtils.isEmpty(creatorName)) {
            creatorName = I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193);
        }

        if (!org.apache.commons.lang.StringUtils.isBlank(orderPackage)) {
            orderPackage = orderPackage.substring(0, orderPackage.length() - 1);
        }
        log.info("CrmLicenseEventListener.snycCrmOder orderId:{} tenantId:{} orderPackage:{}", orderId, tenantId, orderPackage);
        //创建订单,企业初始化的时候，会有这个订单
        if (resourceCount == 0) {
            log.warn("SnycCrmOder failed resourceCount:{}", resourceCount);
            return;
        }
        quotaManager.synCrmSmsOrder(tenantId, quantity, resourceCount, purchaseAmount, orderId, orderPackage, creatorId, creatorName);
    }

    public void configAppCenterVisible(String ea) {
        BaseResult result = appEaVisibleService.addVisible(ea, appId);
        if (!result.isSuccess()) {
            log.error("configAppCenterVisible failed addVisible error ea:{}", ea);
            return;
        }

        FsUserVO fsUserVO = new FsUserVO(ea, 1000);
        BaseResult<List<UserCanViewListVO>> userViewResult = openFsUserAppViewService.queryComponentsByFsUser(fsUserVO, AppAccessTypeEnum.WEB);
        if (!userViewResult.isSuccess()) {
            log.error("configAppCenterVisible failed queryComponentsByFsUser error ea:{}", ea);
            return;
        }

        com.facishare.open.common.result.BaseResult<List<FsUserVO>> queryAdminResult = queryAppAdminService.findAppAdminListByAppId(ea, appId);
        if (!queryAdminResult.isSuccess()) {
            log.error("configAppCenterVisible failed findAppAdminListByAppId error ea:{}", ea);
            return;
        }

        if (CollectionUtils.isEmpty(queryAdminResult.getResult())) {
            log.error("configAppCenterVisible failed queryAdminResult.getResult() is empty ea:{}", ea);
            return;
        }

        BaseResult<EmployeeRange> employeeRangeBaseResult = openFsUserAppViewService.loadComponentView(fsUserVO, marketingAppWebComponent);
        if (!employeeRangeBaseResult.isSuccess()) {
            log.error("configAppCenterVisible failed loadComponentView error ea:{}", ea);
            return;
        }
        EmployeeRange employeeRange = employeeRangeBaseResult.getResult();
        if (employeeRange == null) {
            return;
        }
        if (CollectionUtils.isEmpty(employeeRange.getDepartment()) && CollectionUtils.isEmpty(employeeRange.getMember())) {
            AppViewDO appViewDO = new AppViewDO();
            if (CollectionUtils.isNotEmpty(queryAdminResult.getResult())) {
                List<Integer> userIds = queryAdminResult.getResult().stream().map(FsUserVO::getUserId).collect(Collectors.toList());
                Integer[] members = new Integer[userIds.size()];
                for (int i = 0; i < userIds.size(); i++) {
                    members[i] = userIds.get(i);
                }
                appViewDO.setMember(members);
                openFsUserAppViewService.saveFsUserAppViewList(fsUserVO, marketingAppWebComponent, AppComponentTypeEnum.WEB, appViewDO);
            }
        }
    }
}