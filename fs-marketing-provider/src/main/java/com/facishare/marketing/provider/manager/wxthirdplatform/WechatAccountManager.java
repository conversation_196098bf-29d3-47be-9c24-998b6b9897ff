package com.facishare.marketing.provider.manager.wxthirdplatform;

import static com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity.ACCESS_TOKEN_AHEAD_EXPIRED_SECONDS;

import com.facishare.marketing.api.arg.UnAuthWxAppArg;
import com.facishare.marketing.api.result.WxThirdComponentResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatEaBindDao;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatThirdPlatformConfigEntity;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.service.wxthirdplatform.WxThirdCallbackServiceImpl;
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg;
import com.facishare.wechat.dubborestouterapi.result.WechatRequestDispatchResult;
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatRequestDispatchService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.fxiaoke.wechatrestapi.arg.GetAuthorizerInfoArg;
import com.fxiaoke.wechatrestapi.arg.RefreshAuthorizationTokenArg;
import com.fxiaoke.wechatrestapi.data.BusinessInfo;
import com.fxiaoke.wechatrestapi.data.GetAuthorizerInfoResult;
import com.fxiaoke.wechatrestapi.data.RefreshAuthorizationTokenResult;
import com.fxiaoke.wechatrestapi.result.ModifyDomainResult;
import com.fxiaoke.wechatrestapi.service.WechatAuthRestService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import java.io.Serializable;
import java.util.*;

import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WechatAccountManager {
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private WechatAuthRestService wechatAuthRestService;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private WxCloudRestManager wxCloudRestManager;
    @Autowired
    private WechatRequestDispatchService wechatRequestDispatchService;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private WechatEaBindDao wechatEaBindDao;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @ReloadableProperty("host")
    private String host;
    @Value("${enterprise.environment}")
    private String enterpriseEnvironment;

    public String getAccessTokenByWxAppId(String wxAppId){
        //enterpriseEnvironment  1：纷享云
        if (!StringUtils.equals(enterpriseEnvironment, "1") && StringUtils.equals(wxAppId, WxAppInfoEnum.MankeepPro.getAppId())){
             //到纷享云去获取token
            String accessToken = wxCloudRestManager.getMankeepproAccessToken(wxAppId);
            log.info("WechatAccountManager.getAccessTokenByWxAppId ali mankeeppro get accessToken:{} wxAppId:{}", accessToken, wxAppId);
            return accessToken;
        }

        if(WxAppInfoEnum.isSystemApp(wxAppId)){
            try {
                return getSystemAppAccessToken(wxAppId);
            } catch (Exception e) {
                log.warn("Exception", e);
                return null;
            }
        }
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(wxAppId);
        if(wechatAccountConfig.isAccessTokenInvalid()){
            return doGetAndUpdateAccessTokenFromWechat(wechatAccountConfig);
        }
        if(wechatAccountConfig.isAccessTokenNeedRefresh()){
            ThreadPoolUtils.execute(() -> doGetAndUpdateAccessTokenFromWechat(wechatAccountConfig), ThreadPoolTypeEnums.LIGHT_BUSINESS);
        }
        return wechatAccountConfig.getAccessToken();
    }

    public Map<String, String> batchGetAccessToken(Set<String> appIds){
        Map<String, String> appIdAndAccessToken = new HashMap<>();
        try {
            for (String appId : appIds) {
                if(WxAppInfoEnum.isSystemApp(appId)){
                    appIdAndAccessToken.put(appId, getSystemAppAccessToken(appId));
                }
            }
        } catch (Exception e) {
            log.warn("Exception", e);
        }
        List<WechatAccountConfigEntity> configEntities = wechatAccountConfigDao.batchGetYXTConfig(appIds);
        for (WechatAccountConfigEntity wechatAccountConfig : configEntities) {
            if(wechatAccountConfig.isAccessTokenInvalid()){
                appIdAndAccessToken.put(wechatAccountConfig.getWxAppId(), doGetAndUpdateAccessTokenFromWechat(wechatAccountConfig));
                continue;
            }
            if(wechatAccountConfig.isAccessTokenNeedRefresh()){
                ThreadPoolUtils.execute(() -> doGetAndUpdateAccessTokenFromWechat(wechatAccountConfig), ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
            appIdAndAccessToken.put(wechatAccountConfig.getWxAppId(), wechatAccountConfig.getAccessToken());
        }
        return appIdAndAccessToken;
    }

    public Map<String, WechatAccountConfigEntity> batchGetWechatAccountConfig(Set<String> appIds){
        HashMap<String, WechatAccountConfigEntity> wechatAccountConfigMap = new HashMap<>();
        try {
            for (String appId : appIds) {
                if(WxAppInfoEnum.isSystemApp(appId)){
                    WechatAccountConfigEntity entity = new WechatAccountConfigEntity();
                    entity.setWxAppId(appId);
                    entity.setAccessToken(getSystemAppAccessToken(appId));
                    wechatAccountConfigMap.put(appId, entity);
                }
            }
        } catch (Exception e) {
            log.warn("Exception", e);
        }
        List<WechatAccountConfigEntity> configEntities = wechatAccountConfigDao.batchGetYXTConfig(appIds);
        for (WechatAccountConfigEntity wechatAccountConfig : configEntities) {
            if(wechatAccountConfig.isAccessTokenInvalid()){
                wechatAccountConfig.setAccessToken(doGetAndUpdateAccessTokenFromWechat(wechatAccountConfig));
                wechatAccountConfigMap.put(wechatAccountConfig.getWxAppId(), wechatAccountConfig);
                continue;
            }
            if(wechatAccountConfig.isAccessTokenNeedRefresh()){
                ThreadPoolUtils.execute(() -> doGetAndUpdateAccessTokenFromWechat(wechatAccountConfig), ThreadPoolTypeEnums.LIGHT_BUSINESS);
            }
            wechatAccountConfigMap.put(wechatAccountConfig.getWxAppId(), wechatAccountConfig);
        }
        return wechatAccountConfigMap;
    }

    private String getSystemAppAccessToken(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(appId);
        if (wxAppInfoEnum == null) {
            return null;
        }
        String accessToken = null;
        if (wxAppInfoEnum == WxAppInfoEnum.Mankeep) {
            String key = RedisManager.KEMAI_ACCESS_TOKEN;
            accessToken = getSystemAppAccessByKey(wxAppInfoEnum, key);
        } else if (wxAppInfoEnum == WxAppInfoEnum.MankeepPro) {
            String key = RedisManager.KEMAI_PRO_ACCESS_TOKEN;
            accessToken = getSystemAppAccessByKey(wxAppInfoEnum, key);
            log.info("accessToken:{}", accessToken);
        }
        return accessToken;
    }

    public String getSystemAppAccessByKey(WxAppInfoEnum wxAppInfoEnum, String key) {
        String accessToken = null;
        if (host.contains(".ceshi112.com")) {
            String value = redisManager.getValueByKey(key);
            return value;
        }
        // 若非纷享云则不能使用系统小程序
        if (!host.contains("www.fxiaoke.com")) {
            log.warn("WechatAccountManager.getSystemAppAccessByKey not fxiaoke cloud wxAppInfoEnum:{}", wxAppInfoEnum);
            return null;
        }
        try {
            accessToken = redisManager.getValueByKey(key);
            if (StringUtils.isBlank(accessToken)) {
                Map<String, String> getAccessTokenParams = Maps.newHashMap();
                getAccessTokenParams.put("grant_type", "client_credential");
                getAccessTokenParams.put("appid", wxAppInfoEnum.getAppId());
                getAccessTokenParams.put("secret", wxAppInfoEnum.getSecret());
                Map<String, String> map = httpManager.executeGetHttp("https://api.weixin.qq.com/cgi-bin/token?" + httpManager.transformUrlParams(getAccessTokenParams), new TypeToken<Map<String, String>>() {
                });
                if (MapUtils.isEmpty(map) || null == map.get("access_token")) {
                    log.error("getAccessTokenByWxAppId.access_token not found");
                    return null;
                }
                accessToken = map.get("access_token");
                if (StringUtils.isNotBlank(accessToken)) {
                    redisManager.setValueWithExpiredTime(key, accessToken, RedisManager.ACCESS_TOKEN_EXPIRED_TIME);
                }
            }
        } catch (Exception e) {
            log.warn("WechatAccountManager.getSystemAppAccessByKey error e:", e);
        }
        return accessToken;
    }

    public String doGetAndUpdateAccessTokenFromWechat(WechatAccountConfigEntity wechatAccountConfig){
        for (int i = 0; i < 3; i++) {
            RefreshAuthorizationTokenArg arg = new RefreshAuthorizationTokenArg();
            arg.setComponentAppId(wechatThirdPlatformManager.getComponentAppId(wechatAccountConfig.getThirdPlatformId()));
            arg.setAuthorizerRefreshToken(wechatAccountConfig.getRefreshToken());
            arg.setAuthorizerAppId(wechatAccountConfig.getWxAppId());
                RefreshAuthorizationTokenResult refreshTokenInfoResult = wechatAuthRestService.refreshAuthorizationToken(wechatThirdPlatformManager.getThirdPlatformAccessToken(wechatAccountConfig.getThirdPlatformId()), arg);
            if(refreshTokenInfoResult.isSuccess()){
                int updateRow = wechatAccountConfigDao.updateAccessToken(wechatAccountConfig.getWxAppId(), refreshTokenInfoResult.getAuthorizerAccessToken(), DateUtil.getUnixTimeByDurationSecondsFromNow(refreshTokenInfoResult.getExpireTime() - ACCESS_TOKEN_AHEAD_EXPIRED_SECONDS));
                if(updateRow > 0){
                    return refreshTokenInfoResult.getAuthorizerAccessToken();
                }
            }else {
                log.info("doGetAndUpdateAccessTokenFromWechat wechatAccountConfig:{} refreshTokenInfoResult:{} resultErrcode:{} resultErrorMsg:{}", wechatAccountConfig, refreshTokenInfoResult, refreshTokenInfoResult.getErrCode(), refreshTokenInfoResult.getErrMsg());
            }
        }
        throw new RuntimeException("Exception at invoke wechat");
    }

    public Optional<String> getWxAppIdByEa(String ea, String platformId){
        if (StringUtils.isBlank(ea)) {
            return Optional.empty();
        }
        String boundWxAppId = eaWechatAccountBindDao.getWxAppIdByEa(ea, platformId);
        if (!MKThirdPlatformConstants.PLATFORM_ID.equals(platformId) || Strings.isNullOrEmpty(boundWxAppId) || WxAppInfoEnum.getByAppId(boundWxAppId) != null){
            return Optional.ofNullable(boundWxAppId);
        }
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(boundWxAppId);
        if (wechatAccountConfig == null){
            return Optional.empty();
        }
        if (!wechatAccountConfig.isEverReleased()){
            return Optional.of(redisManager.getPreBoundWxAppId(ea));
        }
        return Optional.of(boundWxAppId);
    }

    @FilterLog
    public Collection<String> listEaByPlatformIdAndWxAppId(String platformId, String wxAppId){
        Preconditions.checkArgument(platformId != null);
        Preconditions.checkArgument(wxAppId != null);
        return eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(platformId, wxAppId);
    }

    public String getNotEmptyWxAppIdByEa(String ea){
        Optional<String> wxAppIdOptional = this.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID);
        if(wxAppIdOptional.isPresent()){
            return wxAppIdOptional.get();
        }
        eaWechatAccountBindDao.insert(ea, MKThirdPlatformConstants.PLATFORM_ID, WxAppInfoEnum.getDefaultApp().getAppId());
        Optional<String> wxAppIdOptional2 = this.getWxAppIdByEa(ea, MKThirdPlatformConstants.PLATFORM_ID);
        if(wxAppIdOptional2.isPresent()){
            return wxAppIdOptional2.get();
        }
        throw new IllegalStateException("Ea not bind to wxAppId");
    }

    public void updateWechatAccountInfo(String platformId, String wxAppId){
        WxThirdComponentResult componentResult = wechatThirdPlatformManager.getComponentAccessTokenAndAppId(platformId);
        GetAuthorizerInfoArg arg = new GetAuthorizerInfoArg();
        arg.setAuthorizerAppId(wxAppId);
        arg.setComponentAppId(componentResult.getComponentAppId());
        // 去微信获取企业号的信息,这里只取到关键信息,以后有别的需要进行补充
        GetAuthorizerInfoResult getAuthorizerInfoResult = wechatAuthRestService.getAuthorizerInfo(componentResult.getComponentAccessToken(), arg);
        if (getAuthorizerInfoResult.isSuccess()) {
            WechatAccountConfigEntity wechatAccountConfig = new WechatAccountConfigEntity();
            wechatAccountConfig.setWxAppId(wxAppId);
            wechatAccountConfig.setNickName(getAuthorizerInfoResult.getAppInfo().getNickname());
            wechatAccountConfig.setHeadImg(getAuthorizerInfoResult.getAppInfo().getHeadImg());
            wechatAccountConfig.setServiceTypeInfo(getAuthorizerInfoResult.getAppInfo().getServiceTypeInfo().getId());
            wechatAccountConfig.setVerifyTypeInfo(getAuthorizerInfoResult.getAppInfo().getVerifyTypeInfo().getId());
            wechatAccountConfig.setUserName(getAuthorizerInfoResult.getAppInfo().getUserName());
            wechatAccountConfig.setPrincipalName(getAuthorizerInfoResult.getAppInfo().getPrincipalName());
            wechatAccountConfig.setSignature(getAuthorizerInfoResult.getAppInfo().getSignature());
            wechatAccountConfig.setQrCodeUrl(getAuthorizerInfoResult.getAppInfo().getQrcodeUrl());
            wechatAccountConfig.setBusinessInfo(GsonUtil.toJson(getAuthorizerInfoResult.getAppInfo().getBusinessInfo()));
            wechatAccountConfigDao.updateAppInfo(wechatAccountConfig);
        }
    }

    public boolean bindWxAppIdAndEa(String wxAppId, String ea){
        if (appVersionManager.isVpnDisconnectCloud() || appVersionManager.isFxCloud()) {
            return wechatEaBindDao.add(wxAppId, ea) == 1;
        }else {
            return wxCloudRestManager.bindWxAppIdAndEa(wxAppId, ea);
        }
    }

    public boolean isOpenPay(String wxAppId){
        WechatAccountConfigEntity wechatAccountConfig = wechatAccountConfigDao.getByWxAppId(wxAppId);
        if(!Strings.isNullOrEmpty(wechatAccountConfig.getBusinessInfo())){
            BusinessInfo businessInfo = GsonUtil.fromJson(wechatAccountConfig.getBusinessInfo(), BusinessInfo.class);
            return businessInfo.getOpenPay() != null && businessInfo.getOpenPay() != 0;
        }
        return false;
    }

    public <T> T dispatch(WechatRequestDispatchArg arg, TypeToken typeToken) {
        try {
            ModelResult<WechatRequestDispatchResult> modelResult = wechatRequestDispatchService.dispatch(arg);
            if (modelResult == null || !modelResult.isSuccess() || modelResult.getResult() == null) {
                log.warn("WechatAccountManager.dispatch error modelResult:{}", modelResult);
                return null;
            }
            String responseBody = modelResult.getResult().getBody();
            return GsonUtil.getGson().fromJson(responseBody, typeToken.getType());
        } catch (Exception e) {
            log.warn("WechatAccountManager.dispatch, e:{}", e);
        }
        return null;
    }


    //vpn断网专属云事件回调，目前吃处理
    public void handleUnauthorizedEventInVpnDisconnectCloud(String platformId, String authorizerAppId, String infoType) {
        if (!"unauthorized".equals(infoType) || WxAppInfoEnum.isSystemApp(authorizerAppId)) {
            return;
        }
        log.info("handleUnauthorizedEventInVpnDisconnectCloud, platformId:{}, authorizerAppId:{}", platformId, authorizerAppId);
        Collection<String> eas = listEaByPlatformIdAndWxAppId(platformId, authorizerAppId);
        if (CollectionUtils.isEmpty(eas)) {
            return;
        }
        for (String ea : eas) {
            eaWechatAccountBindDao.delete(ea, platformId, authorizerAppId);
            if (eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(platformId, authorizerAppId).isEmpty()){
                wechatAccountConfigDao.deleteByWxAppId(authorizerAppId);
                if (!ea.equals(wechatEaBindDao.getEaByAppId(authorizerAppId))) {
                    continue;
                }
                wechatEaBindDao.delete(authorizerAppId);
            }
            // 特殊处理营销通的删除逻辑
            if(MKThirdPlatformConstants.PLATFORM_ID.equals(platformId) && !WxAppInfoEnum.isSystemApp(authorizerAppId)){
                qywxMiniappConfigDAO.updateAppIdCorpIdByEa(WxAppInfoEnum.getDefaultApp().getAppId(), ea);
                // 直接切换为默认小程序客脉pro
                eaWechatAccountBindDao.insert(ea, MKThirdPlatformConstants.PLATFORM_ID, WxAppInfoEnum.getDefaultApp().getAppId());
            }
        }
    }

    public boolean unAuthWxAppId(String ea, UnAuthWxAppArg unAuthArg) {
        if(WxAppInfoEnum.getByAppId(unAuthArg.getWxAppId()) != null){
            return false;
        }
        eaWechatAccountBindDao.delete(ea, unAuthArg.getPlatformId(), unAuthArg.getWxAppId());
        if (eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(unAuthArg.getPlatformId(), unAuthArg.getWxAppId()).isEmpty()){
            int deleteCount = wechatAccountConfigDao.deleteByWxAppId(unAuthArg.getWxAppId());
            if (appVersionManager.isVpnDisconnectCloud()) {
                this.unbindWxAppIdAndEa(ea, unAuthArg.getWxAppId());
            }else {
                wxCloudRestManager.unbindWxAppIdAndEa(unAuthArg.getWxAppId(), ea);
            }
        }
        // 特殊处理营销通的删除逻辑
        if(MKThirdPlatformConstants.PLATFORM_ID.equals(unAuthArg.getPlatformId()) && !WxAppInfoEnum.isSystemApp(unAuthArg.getWxAppId())){
            qywxMiniappConfigDAO.updateAppIdCorpIdByEa(WxAppInfoEnum.getDefaultApp().getAppId(), ea);
            // 直接切换为默认小程序客脉pro
            eaWechatAccountBindDao.insert(ea, MKThirdPlatformConstants.PLATFORM_ID, WxAppInfoEnum.getDefaultApp().getAppId());
        }

        return true;
    }

    public boolean unbindWxAppIdAndEa(String ea, String wxAppId) {
        if (appVersionManager.isVpnDisconnectCloud() || appVersionManager.isFxCloud()) {
            Preconditions.checkArgument(StringUtils.isNotEmpty(wxAppId) && StringUtils.isNotEmpty(ea));
            if (!ea.equals(wechatEaBindDao.getEaByAppId(wxAppId))) {
                return false;
            }
            return wechatEaBindDao.delete(wxAppId) == 1;
        }else {
            return wxCloudRestManager.unbindWxAppIdAndEa(wxAppId, ea);
        }
    }

    // 是否绑定了专属小程序
    public boolean isBindSpecialMiniApp(String ea) {
        // 是否有专属小程序
        EaWechatAccountBindEntity eaWechatAccountBindEntity = eaWechatAccountBindDao.getByEaAndThirdPlatformId(ea, MKThirdPlatformConstants.PLATFORM_ID);
        if (eaWechatAccountBindEntity == null) {
            return false;
        }
        return !WxAppInfoEnum.isSystemApp(eaWechatAccountBindEntity.getWxAppId());
    }
}
