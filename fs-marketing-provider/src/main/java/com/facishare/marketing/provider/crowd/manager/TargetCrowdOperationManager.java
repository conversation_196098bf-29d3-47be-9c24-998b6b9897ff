package com.facishare.marketing.provider.crowd.manager;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.api.arg.CreateTriggerInSceneArg;
import com.facishare.marketing.api.arg.GetOrCreateMarketingSceneArg;
import com.facishare.marketing.api.arg.crowd.MarketingCrowdPlanArg;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.data.material.HexagonSiteBriefData;
import com.facishare.marketing.api.result.CreateTriggerInSceneResult;
import com.facishare.marketing.api.result.crowd.MarketingCrowdPlanDetailResult;
import com.facishare.marketing.api.result.crowd.MarketingCrowdPlanStatisticsResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.api.vo.MarketingSceneVO;
import com.facishare.marketing.api.vo.MarketingTriggerVO;
import com.facishare.marketing.api.vo.SceneTriggerVO;
import com.facishare.marketing.api.vo.TriggerTaskVO;
import com.facishare.marketing.api.vo.qywx.ListSceneTriggerVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FlexibleJson;
import com.facishare.marketing.common.typehandlers.value.RuleGroupList;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.crowd.dao.MarketingEventMarketingUserGroupRelationDao;
import com.facishare.marketing.provider.crowd.dao.MarketingEventTimedTaskEntityDao;
import com.facishare.marketing.provider.crowd.entity.MarketingEventMarketingUserGroupRelationEntity;
import com.facishare.marketing.provider.crowd.entity.MarketingEventTimedTaskEntity;
import com.facishare.marketing.provider.crowd.utils.RepeatDateUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity;
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity;
import com.facishare.marketing.provider.entity.TriggerSnapshotEntity;
import com.facishare.marketing.provider.entity.TriggerTaskSnapshotEntity;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.HexagonManager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.feed.MaterailDataManagerFactory;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.statistic.outapi.service.MarketingEventStatisticService;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Component
@AllArgsConstructor
@Slf4j
public class TargetCrowdOperationManager {

    private final MarketingEventMarketingUserGroupRelationDao marketingEventMarketingUserGroupRelationDao;
    private final MarketingEventTimedTaskEntityDao marketingEventTimedTaskEntityDao;
    private final MarketingSceneService marketingSceneService;
    private final MetadataActionService metadataActionService;
    private final EIEAConverter eIEAConverter;
    private final MarketingUserGroupDao marketingUserGroupDao;
    private final MarketingUserGroupToUserRelationDao marketingUserGroupToUserRelationDao;
    private final CrmV2Manager crmV2Manager;
    private final CampaignMergeDataManager campaignMergeDataManager;
    private final MarketingEventStatisticService marketingEventStatisticService;
    private final MarketingEventManager marketingEventManager;
    private final CustomizeFormDataManager customizeFormDataManager;
    private final ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    private final HexagonManager hexagonManager;
    private final SceneTriggerDao sceneTriggerDao;
    private final MaterailDataManagerFactory materailDataManagerFactory;
    private final TriggerSnapshotDao triggerSnapshotDao;
    private final TriggerTaskSnapshotDao triggerTaskSnapshotDao;

    /**
     * 目标人群定时任务处理
     *
     * @param taskId
     */
    public void handleTimedTask(String taskId) {
        MarketingEventTimedTaskEntity task = marketingEventTimedTaskEntityDao.getById(taskId);
        int updateCount = marketingEventTimedTaskEntityDao.updaterResultById(task.getId(), "executing", "todo", null);
        String executeResult = null;
        if (updateCount == 1) {
            try {
                MarketingEventMarketingUserGroupRelationEntity parentEventRelation = marketingEventMarketingUserGroupRelationDao.getByMarketingEventId(task.getMarketingEventId());
                String ea = parentEventRelation.getEa();
                Integer fsUserId = parentEventRelation.getCreateBy();
                String timedTaskSettingJson = task.getTimedTaskSetting();
                MarketingCrowdPlanArg.TimedTaskSetting timedTaskSetting = GsonUtil.fromJson(timedTaskSettingJson, MarketingCrowdPlanArg.TimedTaskSetting.class);
                Long executeTime = RepeatDateUtil.calFirstExecuteTime(timedTaskSetting.getRepeatRangeStart(), timedTaskSetting.getRepeatRangeEnd(), timedTaskSetting.getRepeatType(), timedTaskSetting.getIncludeRepeatValue(), timedTaskSetting.getRepeatValue(), timedTaskSetting.getTriggerAtMinutes());
                ObjectData marketingEvent = crmV2Manager.getDetail(task.getEa(), SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), task.getMarketingEventId());
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = addMarketingEvent(ea, fsUserId, marketingEvent, timedTaskSetting);
                if (addResult == null || !addResult.isSuccess()) {
                    log.warn("addMarketingEvent fail addResult:{}", addResult);
                    executeResult = "addMarketingEvent fail";
                } else {
                    MarketingEventMarketingUserGroupRelationEntity entity = new MarketingEventMarketingUserGroupRelationEntity();
                    entity.setId(UUIDUtil.getUUID());
                    entity.setEa(ea);
                    String marketingEventId = addResult.getData().getObjectData().getId();
                    entity.setMarketingEventId(marketingEventId);
                    String tempMarketingUserGroupId = parentEventRelation.getMarketingUserGroupId();
                    String newMarketingUserGroupId = UUIDUtil.getUUID();
                    boolean addSuccess = addMarketingUserGroup(ea, fsUserId, tempMarketingUserGroupId, newMarketingUserGroupId);
                    if (addSuccess) {
                        entity.setMarketingUserGroupId(newMarketingUserGroupId);
                        entity.setCreateBy(fsUserId);
                        marketingEventMarketingUserGroupRelationDao.insert(entity);
                        String parentMarketingEventId = parentEventRelation.getMarketingEventId();
                        if (executeTime != null && executeTime < timedTaskSetting.getRepeatRangeEnd()) {
                            addTimedTask(ea, parentMarketingEventId, timedTaskSettingJson, executeTime);
                        }
                        Map<String, AbstractMaterialData> replaceMaterial = Maps.newLinkedHashMap();
                        ThreadPoolUtils.execute(() -> batchInsertMarketingUserToNewGroup(ea, fsUserId, tempMarketingUserGroupId, newMarketingUserGroupId, parentMarketingEventId, marketingEventId, timedTaskSetting), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                        ThreadPoolUtils.execute(() -> batchAddMaterialToChildMarketingEvent(ea, fsUserId, parentMarketingEventId, marketingEventId, replaceMaterial), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                        marketingSceneService.getOrCreateMarketingScene(ea, fsUserId, new GetOrCreateMarketingSceneArg(MarketingSceneType.TARGET_CROWD_OPERATION.getType(), marketingEventId));
                        ThreadPoolUtils.execute(() -> {
                            batchAddSopToChildMarketingEvent(ea, fsUserId, parentMarketingEventId, marketingEventId, newMarketingUserGroupId, replaceMaterial);
                            batchUpdateSopRelation(ea, fsUserId, parentMarketingEventId, marketingEventId);
                        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                    } else {
                        executeResult = "addMarketingUserGroup fail";
                    }
                }
            } catch (Exception e) {
                executeResult = e.getMessage();
                log.warn("TargetCrowdOperationManager handleTimedTask error task:{}", task, e);
            } finally {
                marketingEventTimedTaskEntityDao.updaterResultById(task.getId(), "executed", "executing", executeResult);
            }
        }
    }

    public void batchUpdateSopRelation(String ea, Integer fsUserId, String parentMarketingEventId, String marketingEventId) {
        Result<MarketingSceneVO> marketingSceneResult = marketingSceneService.getOrCreateMarketingScene(ea, fsUserId, new GetOrCreateMarketingSceneArg(MarketingSceneType.TARGET_CROWD_OPERATION.getType(), marketingEventId));
        if (marketingSceneResult == null || !marketingSceneResult.isSuccess()) {
            return;
        }
        String sceneId = marketingSceneResult.getData().getId();
        ListSceneTriggerVO vo = new ListSceneTriggerVO();
        vo.setEa(ea);
        vo.setUserId(fsUserId);
        vo.setPageNum(1);
        vo.setPageSize(1);
        vo.setId(sceneId);
        Result<PageResult<SceneTriggerVO>> tmpPageResultResult = marketingSceneService.listSceneTriggerBySceneId(vo);
        if (tmpPageResultResult == null || tmpPageResultResult.getData() == null) {
            return;
        }
        int pageNo = 1;
        int pageSize = 10;
        Integer total = tmpPageResultResult.getData().getTotalCount();
        List<MarketingTriggerVO> marketingTriggerVOS = Lists.newArrayList();
        for (; (pageNo - 1) * pageSize < total; pageNo = pageNo + 1) {
            vo.setPageNum(pageNo);
            vo.setPageSize(pageSize);
            Result<PageResult<SceneTriggerVO>> pageResultResult = marketingSceneService.listSceneTriggerBySceneId(vo);
            if (pageResultResult != null && pageResultResult.getData() != null && CollectionUtils.isNotEmpty(pageResultResult.getData().getResult())) {
                List<SceneTriggerVO> sceneTriggerVOS = pageResultResult.getData().getResult();
                marketingTriggerVOS.addAll(sceneTriggerVOS.stream().map(SceneTriggerVO::getTriggerVO).collect(Collectors.toList()));
            }
        }
        for (MarketingTriggerVO marketingTriggerVO : marketingTriggerVOS) {
            if (!TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(marketingTriggerVO.getSnapshotStatus())) {
                continue;
            }
            try {
                List<TriggerTaskVO> triggerTasks = marketingTriggerVO.getTriggerTasks();
                if (CollectionUtils.isEmpty(triggerTasks)) {
                    continue;
                }
                for (TriggerTaskVO triggerTaskVO : triggerTasks) {
                    //关联更新SOP邮件触发物料
                    if (!"send_email_msg".equals(triggerTaskVO.getTaskType())) {
                        continue;
                    }
                    TriggerTaskSnapshotEntity oldTriggerTaskSnapshotEntity = triggerTaskSnapshotDao.getByMailTitleAndParentMarketingEventId(ea, triggerTaskVO.getEmail().getTitle(), parentMarketingEventId);
                    try {
                        if (oldTriggerTaskSnapshotEntity == null) return;
                        // 邮件触发任务列表
                        List<TriggerSnapshotEntity> triggerSnapshotEntities = triggerSnapshotDao.listByTargetObjectId(ea, oldTriggerTaskSnapshotEntity.getId(), marketingEventId);
                        if (CollectionUtils.isEmpty(triggerSnapshotEntities)) {
                            continue;
                        }
                        for (TriggerSnapshotEntity triggerSnapshotEntity : triggerSnapshotEntities) {
                            triggerSnapshotEntity = JSONObject.parseObject(
                                    StringUtils.replace(JSONObject.toJSONString(triggerSnapshotEntity), oldTriggerTaskSnapshotEntity.getId(), triggerTaskVO.getId()),
                                    TriggerSnapshotEntity.class
                            );
                            FlexibleJson targetObjects = triggerSnapshotEntity.getTargetObjects();
                            targetObjects.remove(oldTriggerTaskSnapshotEntity.getId());
                            targetObjects.put(triggerTaskVO.getId(), 42);
                            triggerSnapshotDao.updateTargetObjectById(triggerSnapshotEntity.getId(), targetObjects, triggerSnapshotEntity.getFrontEndExtensions());
                        }
                    } catch (Exception e) {
                        log.warn("关联更新SOP邮件触发物料失败:", e);
                    }
                }
            } catch (Exception e) {
                log.warn("TargetCrowdOperationManager batchAddSopToChildMarketingEvent batchUpdateSopRelation error {}:{}", parentMarketingEventId, marketingEventId, e);
            }
        }
    }

    private void batchAddSopToChildMarketingEvent(String ea, Integer fsUserId, String parentMarketingEventId, String marketingEventId, String newMarketingUserGroupId, Map<String, AbstractMaterialData> replaceMaterial) {
        ObjectData marketingEvent = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        Result<MarketingSceneVO> marketingSceneResult = marketingSceneService.getOrCreateMarketingScene(ea, fsUserId, new GetOrCreateMarketingSceneArg(MarketingSceneType.TARGET_CROWD_OPERATION.getType(), parentMarketingEventId));
        if (marketingSceneResult != null && marketingSceneResult.isSuccess()) {
            String sceneId = marketingSceneResult.getData().getId();
            ListSceneTriggerVO vo = new ListSceneTriggerVO();
            vo.setEa(ea);
            vo.setUserId(fsUserId);
            vo.setPageNum(1);
            vo.setPageSize(1);
            vo.setId(sceneId);
            Result<PageResult<SceneTriggerVO>> tmpPageResultResult = marketingSceneService.listSceneTriggerBySceneId(vo);
            if (tmpPageResultResult != null && tmpPageResultResult.isSuccess()) {
                int pageNo = 1;
                int pageSize = 10;
                Integer total = tmpPageResultResult.getData().getTotalCount();
                List<MarketingTriggerVO> marketingTriggerVOS = Lists.newArrayList();
                for (; (pageNo - 1) * pageSize < total; pageNo = pageNo + 1) {
                    vo.setPageNum(pageNo);
                    vo.setPageSize(pageSize);
                    Result<PageResult<SceneTriggerVO>> pageResultResult = marketingSceneService.listSceneTriggerBySceneId(vo);
                    if (pageResultResult != null && pageResultResult.getData() != null && CollectionUtils.isNotEmpty(pageResultResult.getData().getResult())) {
                        List<SceneTriggerVO> sceneTriggerVOS = pageResultResult.getData().getResult();
                        // 只复制可用的
                        List<MarketingTriggerVO> collect = sceneTriggerVOS.stream()
                                .filter(e -> TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(e.getLifeStatus())
                                        && TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(e.getTriggerVO().getSnapshotStatus()))
                                .map(SceneTriggerVO::getTriggerVO)
                                .collect(Collectors.toList());
                        marketingTriggerVOS.addAll(collect);
                    }
                }
                Result<MarketingSceneVO> childMarketingSceneResult = marketingSceneService.getOrCreateMarketingScene(ea, fsUserId, new GetOrCreateMarketingSceneArg(MarketingSceneType.TARGET_CROWD_OPERATION.getType(), marketingEventId));
                if (childMarketingSceneResult != null && childMarketingSceneResult.isSuccess()) {
                    String childSceneId = childMarketingSceneResult.getData().getId();
                    CreateTriggerInSceneArg arg = new CreateTriggerInSceneArg();
                    arg.setSceneId(childSceneId);
                    for (MarketingTriggerVO marketingTriggerVO : marketingTriggerVOS) {
                        try {
                            if ("single_timing".equals(marketingTriggerVO.getTriggerType()) && StringUtils.isNotBlank(newMarketingUserGroupId)) {
                                List<String> marketingUserGroupIds = Lists.newArrayList(newMarketingUserGroupId);
                                marketingTriggerVO.setMarketingUserGroupIds(marketingUserGroupIds);
                                marketingTriggerVO.getFrontEndExtensions().put("marketingUserGroupIds", marketingUserGroupIds);
                            }
                            if ("trigger_by_action".equals(marketingTriggerVO.getTriggerType()) && MapUtils.isNotEmpty(replaceMaterial) && marketingTriggerVO.getTargetObjects() != null) {
                                Map<String, Object> oldTargetObjects = marketingTriggerVO.getTargetObjects();
                                oldTargetObjects.forEach((k, v) -> {
                                    AbstractMaterialData newMaterialData = replaceMaterial.get(k);
                                    FlexibleJson newTargetObjects = new FlexibleJson();
                                    newTargetObjects.put(newMaterialData.getId(), newMaterialData.getObjectType());
                                    Map<String, Object> targetObject = new HashMap<>();
                                    targetObject.put("targetObjectId", newMaterialData.getId());
                                    targetObject.put("targetObjectType", newMaterialData.getObjectType());
                                    targetObject.put("targetObjectName", newMaterialData.getTitle());
                                    targetObject.put("targetObjectImage", newMaterialData.getSharePicOrdinaryUrl());
                                    if (marketingTriggerVO.getFrontEndExtensions().get("targetObjectList") != null) {
                                        List<Map<String, Object>> targetObjectList = (List<Map<String, Object>>) marketingTriggerVO.getFrontEndExtensions().get("targetObjectList");
                                        if (targetObjectList != null) {
                                            for (int i = 0; i < targetObjectList.size(); i++) {
                                                if (k.equals(targetObjectList.get(i).get("targetObjectId"))) {
                                                    targetObjectList.set(i, targetObject);
                                                }
                                            }
                                        }
                                    }
                                    marketingTriggerVO.setTargetObjects(newTargetObjects);
                                });
                            }
                            arg.setTriggerVO(marketingTriggerVO);
                            Result<CreateTriggerInSceneResult> triggerInScene = marketingSceneService.createTriggerInScene(ea, fsUserId, arg);
                            if (triggerInScene != null && triggerInScene.getData() != null && StringUtils.isNotBlank(triggerInScene.getData().getSceneTriggerId())) {
                                sceneTriggerDao.updateEffectiveTime(triggerInScene.getData().getSceneTriggerId(), EffectiveTimeType.RANGE.getType(), marketingEvent.getLong(MarketingEventFieldContants.BEGIN_TIME), marketingEvent.getLong(MarketingEventFieldContants.END_TIME));
                            }
                        } catch (Exception e) {
                            log.warn("TargetCrowdOperationManager batchAddSopToChildMarketingEvent createTriggerInScene error CreateTriggerInSceneArg:{}", arg, e);
                        }
                    }
                }
            }
        }
    }

    private void batchAddMaterialToChildMarketingEvent(String ea, Integer fsUserId, String tempMarketingEventId, String newMarketingEventId, Map<String, AbstractMaterialData> replaceMaterial) {
        ArrayList<Integer> objectTypes = Lists.newArrayList(
                ObjectTypeEnum.PRODUCT.getType(), ObjectTypeEnum.ARTICLE.getType(), ObjectTypeEnum.CUSTOMIZE_FORM.getType(), ObjectTypeEnum.HEXAGON_SITE.getType(), ObjectTypeEnum.OUT_LINK.getType());
        List<AbstractMaterialData> allMaterialData = Lists.newArrayList();
        PageResult<AbstractMaterialData> materialDataPageResult = marketingEventManager.getMaterials(ea, objectTypes, tempMarketingEventId, 1, 1, false);
        int pageNo = 1;
        int pageSize = 10;
        Integer total = materialDataPageResult.getTotalCount();
        for (; (pageNo - 1) * pageSize < total; pageNo = pageNo + 1) {
            PageResult<AbstractMaterialData> tmpMaterialDataPageResult = marketingEventManager.getMaterials(ea, objectTypes, tempMarketingEventId, pageNo, pageSize, false);
            if (tmpMaterialDataPageResult != null && CollectionUtils.isNotEmpty(tmpMaterialDataPageResult.getResult())) {
                allMaterialData.addAll(tmpMaterialDataPageResult.getResult());
            }
        }
        for (AbstractMaterialData materialData : allMaterialData) {
            try {
                ContentMarketingEventMaterialRelationEntity entity = new ContentMarketingEventMaterialRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                if (materialData.getObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                    HexagonSiteBriefData hexagonSiteBriefData = (HexagonSiteBriefData) materialData;
                    if (hexagonSiteBriefData.isSystem()) {
                        //调用统一复制站点
                        HexagonCopyArg arg = new HexagonCopyArg();
                        arg.setId(materialData.getId());
                        arg.setName(hexagonSiteBriefData.getName());
                        Result<CreateSiteResult> result = hexagonManager.hexagonCopySite(ea, fsUserId, arg, HexagonManager.COPY_FROM_HEXAGON);
                        if (result != null && result.isSuccess()) {
                            String newId = result.getData().getId();
                            AbstractMaterialData newMaterialData = materailDataManagerFactory.get(ObjectTypeEnum.HEXAGON_SITE.getType()).get(ea, newId);
                            replaceMaterial.put(materialData.getId(), newMaterialData);
                            entity.setObjectId(newId);
                        }
                    } else {
                        entity.setObjectId(materialData.getId());
                    }
                } else {
                    entity.setObjectId(materialData.getId());
                }
                entity.setObjectType(materialData.getObjectType());
                entity.setMarketingEventId(newMarketingEventId);
                entity.setIsMobileDisplay(false);
                contentMarketingEventMaterialRelationDAO.save(entity);
                if (materialData.getObjectType() == ObjectTypeEnum.CUSTOMIZE_FORM.getType()) {
                    // 表单创建二维码
                    customizeFormDataManager.createCustomizeFormDataQRCode(materialData.getId(), newMarketingEventId, ea);
                }
            } catch (Exception e) {
                log.warn("TargetCrowdOperationManager batchAddMaterialToChildMarketingEvent save error tempMarketingEventId:{} newMarketingEventId:{}", tempMarketingEventId, newMarketingEventId, e);
            }
        }
    }

    /**
     * 保存目标人群计划
     *
     * @param ea
     * @param fsUserId
     * @param arg
     * @return
     */
    public Result saveMarketingCrowdPlan(String ea, Integer fsUserId, MarketingCrowdPlanArg arg) {
        // 市场活动与营销人群不能为空
        if (null == arg.getMarketingEvent() || StringUtils.isBlank(arg.getMarketingUserGroupId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Long executeTime = null;
        MarketingCrowdPlanArg.TimedTaskSetting timedTaskSetting = arg.getTimedTaskSetting();
        if ("periodicity".equals(arg.getPlanType())) {
            if (timedTaskSetting == null) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            executeTime = RepeatDateUtil.calFirstExecuteTime(timedTaskSetting.getRepeatRangeStart(), timedTaskSetting.getRepeatRangeEnd(), timedTaskSetting.getRepeatType(), timedTaskSetting.getIncludeRepeatValue(), timedTaskSetting.getRepeatValue(), timedTaskSetting.getTriggerAtMinutes());
            if (executeTime == null) {
                return Result.newError(SHErrorCode.TARGET_CROWD_OPERATION_ILLEGALITY_SETTING);
            }
        }
        String marketingEventId = null;
        MarketingEventMarketingUserGroupRelationEntity entity = null;
        if (StringUtils.isBlank(arg.getMarketingEventId())) {
            // 新增
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = addMarketingEvent(ea, fsUserId, arg.getMarketingEvent(), null);
            if (addResult == null || !addResult.isSuccess()) {
                log.warn("addMarketingEvent fail addResult:{}", GsonUtil.toJson(addResult));
                if (addResult != null && 201112001 == addResult.getCode()) {
                    return Result.newError(SHErrorCode.TARGET_CROWD_OPERATION_CREATE_FAIL.getErrorCode(), "市场活动名称已存在");
                } else {
                    return Result.newError(SHErrorCode.TARGET_CROWD_OPERATION_CREATE_FAIL);
                }
            }
            entity = new MarketingEventMarketingUserGroupRelationEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            marketingEventId = addResult.getData().getObjectData().getId();
            entity.setMarketingEventId(marketingEventId);
            // 仅新增处理
            String tempMarketingUserGroupId = arg.getMarketingUserGroupId();
            if ("once".equals(arg.getPlanType())) {
                String newMarketingUserGroupId = UUIDUtil.getUUID();
                boolean addSuccess = addMarketingUserGroup(ea, fsUserId, tempMarketingUserGroupId, newMarketingUserGroupId);
                if (addSuccess) {
                    entity.setMarketingUserGroupId(newMarketingUserGroupId);
                    ThreadPoolUtils.execute(() -> batchInsertMarketingUserToNewGroup(ea, fsUserId, tempMarketingUserGroupId, newMarketingUserGroupId, null, null, null), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                }
            } else {
                entity.setMarketingUserGroupId(tempMarketingUserGroupId);
                addTimedTask(ea, marketingEventId, GsonUtil.toJson(timedTaskSetting), executeTime);
            }
            entity.setCreateBy(fsUserId);
            marketingEventMarketingUserGroupRelationDao.insert(entity);
            marketingSceneService.getOrCreateMarketingScene(ea, fsUserId, new GetOrCreateMarketingSceneArg(MarketingSceneType.TARGET_CROWD_OPERATION.getType(), marketingEventId));
        } else {
            // 更新
            entity = marketingEventMarketingUserGroupRelationDao.getByMarketingEventId(arg.getMarketingEventId());
            if (null == entity) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
            marketingEventId = entity.getMarketingEventId();
            ObjectData marketingEvent = arg.getMarketingEvent();
            marketingEvent.put("_id", marketingEventId);
            ActionEditArg actionEditArg = new ActionEditArg();
            actionEditArg.setObjectData(marketingEvent);
            com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult = metadataActionService.edit(new HeaderObj(eIEAConverter.enterpriseAccountToId(ea), fsUserId), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), true, true,actionEditArg);
            if (editResult == null || !editResult.isSuccess()) {
                return Result.newError(SHErrorCode.TARGET_CROWD_OPERATION_UPDATE_FAIL);
            }
            if ("periodicity".equals(arg.getPlanType())) {
                marketingEventTimedTaskEntityDao.deleteByMarketingEventIdAndStatus(ea, marketingEventId, "todo");
                int addNum = addTimedTask(ea, marketingEventId, GsonUtil.toJson(timedTaskSetting), executeTime);
                if (addNum == 0) {
                    // 无意义更改,仅用于回显看起来正常
                    MarketingEventTimedTaskEntity lastTask = marketingEventTimedTaskEntityDao.getByMarketingEventId(marketingEventId);
                    marketingEventTimedTaskEntityDao.updaterSettingById(lastTask.getId(), GsonUtil.toJson(timedTaskSetting));
                }
            }
            String finalMarketingEventId = marketingEventId;
            ThreadPoolUtils.execute(() -> batchUpdateEffectiveTime(ea, fsUserId, finalMarketingEventId), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess();
    }

    private void batchUpdateEffectiveTime(String ea, Integer fsUserId, String marketingEventId) {
        ObjectData marketingEvent = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        Result<MarketingSceneVO> marketingSceneResult = marketingSceneService.getOrCreateMarketingScene(ea, fsUserId, new GetOrCreateMarketingSceneArg(MarketingSceneType.TARGET_CROWD_OPERATION.getType(), marketingEventId));
        if (marketingSceneResult != null && marketingSceneResult.isSuccess()) {
            String sceneId = marketingSceneResult.getData().getId();
            ListSceneTriggerVO vo = new ListSceneTriggerVO();
            vo.setEa(ea);
            vo.setUserId(fsUserId);
            vo.setPageNum(1);
            vo.setPageSize(1);
            vo.setId(sceneId);
            Result<PageResult<SceneTriggerVO>> tmpPageResultResult = marketingSceneService.listSceneTriggerBySceneId(vo);
            if (tmpPageResultResult != null && tmpPageResultResult.isSuccess()) {
                int pageNo = 1;
                int pageSize = 10;
                Integer total = tmpPageResultResult.getData().getTotalCount();
                List<SceneTriggerVO> sceneTriggerVOS = Lists.newArrayList();
                for (; (pageNo - 1) * pageSize < total; pageNo = pageNo + 1) {
                    vo.setPageNum(pageNo);
                    vo.setPageSize(pageSize);
                    Result<PageResult<SceneTriggerVO>> pageResultResult = marketingSceneService.listSceneTriggerBySceneId(vo);
                    if (pageResultResult != null && pageResultResult.getData() != null && CollectionUtils.isNotEmpty(pageResultResult.getData().getResult())) {
                        sceneTriggerVOS.addAll(pageResultResult.getData().getResult());
                    }
                }
                for (SceneTriggerVO sceneTriggerVO : sceneTriggerVOS) {
                    try {
                        sceneTriggerDao.updateEffectiveTime(sceneTriggerVO.getId(), EffectiveTimeType.RANGE.getType(), marketingEvent.getLong(MarketingEventFieldContants.BEGIN_TIME), marketingEvent.getLong(MarketingEventFieldContants.END_TIME));
                    } catch (Exception e) {
                        log.warn("TargetCrowdOperationManager batchUpdateEffectiveTime error marketingEventId:{}", marketingEventId, e);
                    }
                }
            }
        }
    }

    private com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addMarketingEvent(String ea, Integer fsUserId, ObjectData marketingEvent, MarketingCrowdPlanArg.TimedTaskSetting timedTaskSetting) {
        log.warn("addMarketingEvent marketingEvent:{} timedTaskSetting:{}", marketingEvent, timedTaskSetting);
        ActionAddArg actionAddArg = new ActionAddArg();
        if (null != timedTaskSetting) {
            ObjectData newMarketingEvent = (ObjectData) marketingEvent.clone();
            newMarketingEvent.put("_id", null);
            newMarketingEvent.put("parent_id", marketingEvent.getId());
            newMarketingEvent.put("event_type", "once");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Date now = new Date();
            String newName = "【" + marketingEvent.getName() + "】" + sdf.format(now) + "期";
            newMarketingEvent.put("name", newName);
            newMarketingEvent.put("begin_time", now.getTime());
            newMarketingEvent.put("end_time", DateUtil.plusDay(now, timedTaskSetting.getSinglePeriod()).getTime());
            newMarketingEvent.put("is_mobile_display", "1");
            actionAddArg.setObjectData(newMarketingEvent);
        } else {
            marketingEvent.put("is_mobile_display", "1");
            actionAddArg.setObjectData(marketingEvent);
        }
        return metadataActionService.add(new HeaderObj(eIEAConverter.enterpriseAccountToId(ea), fsUserId), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), false, actionAddArg);
    }

    private int addTimedTask(String ea, String marketingEventId, String timedTaskSettingJson, Long executeTime) {
        if (executeTime != null) {
            MarketingEventTimedTaskEntity task = new MarketingEventTimedTaskEntity();
            task.setId(UUIDUtil.getUUID());
            task.setEa(ea);
            task.setMarketingEventId(marketingEventId);
            task.setTimedTaskSetting(timedTaskSettingJson);
            task.setExecuteTime(executeTime);
            task.setExecuteStatus("todo");
            return marketingEventTimedTaskEntityDao.insert(task);
        }
        log.warn("TargetCrowdOperationManager addTimedTask fail marketingEventId:{} timedTaskSettingJson:{}", marketingEventId, timedTaskSettingJson);
        return 0;
    }

    private void batchInsertMarketingUserToNewGroup(String ea, Integer fsUserId, String tempMarketingUserGroupId, String newMarketingUserGroupId, String parentMarketingEventId, String marketingEventId, MarketingCrowdPlanArg.TimedTaskSetting timedTaskSetting) {
        int count = 0;
        int limit = 500;
        String lastMarketingUserId = null;
        while (true) {
            List<String> partMarketingUserIds = marketingUserGroupToUserRelationDao.pageListMarketingUserIdsV2(ea, tempMarketingUserGroupId, lastMarketingUserId, limit);
            if (CollectionUtils.isEmpty(partMarketingUserIds)) {
                break;
            }
            lastMarketingUserId = partMarketingUserIds.get(partMarketingUserIds.size() - 1);
            if (StringUtils.isNotBlank(parentMarketingEventId) && timedTaskSetting != null) {
                try {
                    List<ObjectData> marketingEventObjs = Lists.newArrayList();
                    // 查询所有子活动(不包括当前任务新增的),创建时间倒叙
                    PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                    paasQueryArg.addFilter("parent_id", OperatorConstants.EQ, Collections.singletonList(parentMarketingEventId));
                    paasQueryArg.addFilter("_id", OperatorConstants.N, Collections.singletonList(marketingEventId));
                    paasQueryArg.addOrderByAsc("create_time", false);
                    List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "end_time"), paasQueryArg);
                    if (CollectionUtils.isNotEmpty(objectDataList)) {
                        marketingEventObjs = objectDataList;
                    }
                    List<String> marketingUserGroupIds = Lists.newArrayList();
                    if (timedTaskSetting.getJoinLimit() == 1) {
                        // 仅能参加一次
                        Set<String> marketingEventIds = marketingEventObjs.stream().map(ObjectData::getId).collect(Collectors.toSet());
                        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
                            List<MarketingEventMarketingUserGroupRelationEntity> relationEntities = marketingEventMarketingUserGroupRelationDao.getByMarketingEventIds(marketingEventIds);
                            if (CollectionUtils.isNotEmpty(relationEntities)) {
                                marketingUserGroupIds.addAll(relationEntities.stream().map(MarketingEventMarketingUserGroupRelationEntity::getMarketingUserGroupId).collect(Collectors.toList()));
                            }
                        }
                    } else if (timedTaskSetting.getJoinLimit() == 2 && CollectionUtils.isNotEmpty(marketingEventObjs)) {
                        ObjectData objectData = marketingEventObjs.get(0);
                        MarketingEventMarketingUserGroupRelationEntity relationEntity = marketingEventMarketingUserGroupRelationDao.getByMarketingEventId(objectData.getId());
                        // 可参加多次, 间隔n天, 需判断上一个周期的结束时间距离现在是否大于间隔时间
                        Integer limitDay = timedTaskSetting.getLimitDay();
                        if (limitDay != null) {
                            Long nowTime = new Date().getTime();
                            Long endTime = objectData.getLong("end_time") != null ? objectData.getLong("end_time") : nowTime;
                            if (nowTime - endTime < limitDay * 24 * 60 * 60 * 1000) {
                                marketingUserGroupIds.add(relationEntity.getMarketingUserGroupId());
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(marketingUserGroupIds)) {
                        partMarketingUserIds = marketingUserGroupToUserRelationDao.getByFilter(ea, marketingUserGroupIds, partMarketingUserIds);
                    }
                } catch (Exception e) {
                    log.warn("TargetCrowdOperationManager batchInsertMarketingUserToNewGroup filter marketingUserIds error timedTaskSetting:{}", timedTaskSetting, e);
                }
            }
            if (CollectionUtils.isNotEmpty(partMarketingUserIds)) {
                marketingUserGroupToUserRelationDao.batchInsertIgnore(ea, fsUserId, newMarketingUserGroupId, partMarketingUserIds);
            }
            count += partMarketingUserIds.size();
            if (count >= 100000) {
                break;
            }
        }
        if (count > 0) {
            MarketingUserGroupEntity groupEntity = marketingUserGroupDao.getById(newMarketingUserGroupId);
            groupEntity.setUserNumber(count);
            marketingUserGroupDao.update(groupEntity);
        }
    }

    private boolean addMarketingUserGroup(String ea, Integer fsUserId, String tempMarketingUserGroupId, String newMarketingUserGroupId) {
        MarketingUserGroupEntity tempMarketingUserGroup = marketingUserGroupDao.getById(tempMarketingUserGroupId);
        if (tempMarketingUserGroup == null) {
            return false;
        }
        MarketingUserGroupEntity newMarketingUserGroup = new MarketingUserGroupEntity();
        newMarketingUserGroup.setId(newMarketingUserGroupId);
        newMarketingUserGroup.setEa(ea);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String name = "【人群运营】" + tempMarketingUserGroup.getName() + simpleDateFormat.format(new Date());
        newMarketingUserGroup.setName(name);
        newMarketingUserGroup.setDescription(name);
        newMarketingUserGroup.setRuleGroupJson(new RuleGroupList());
        newMarketingUserGroup.setType(MarketingUserGroupTypeEnum.STATIC.getType());
        newMarketingUserGroup.setCalculationPeriod(MarketingUserGroupCalculationPeriodEnum.MANUAL.getCalculationPeriod());
        newMarketingUserGroup.setStatus(MarketingUserGroupStatusEnum.INVISIBLE.getStatus());
        newMarketingUserGroup.setSearchType(1);
        newMarketingUserGroup.setTagOperator("IN");
        newMarketingUserGroup.setExcludeTags(false);
        newMarketingUserGroup.setCreateBy(fsUserId);
        newMarketingUserGroup.setCreateTime(new Date());
        int initStatisticDataCount = 0;
        newMarketingUserGroup.setUserNumber(initStatisticDataCount);
        newMarketingUserGroup.setWxFansNumber(initStatisticDataCount);
        newMarketingUserGroup.setUserPhoneNumber(initStatisticDataCount);
        newMarketingUserGroup.setWxWorkExternalUserNumber(initStatisticDataCount);
        newMarketingUserGroup.setCrmLeadCount(initStatisticDataCount);
        newMarketingUserGroup.setCrmCustomerCount(initStatisticDataCount);
        newMarketingUserGroup.setCrmContactCount(initStatisticDataCount);
        return marketingUserGroupDao.add(newMarketingUserGroup);
    }

    /**
     * 获取详情
     *
     * @param ea
     * @param fsUserId
     * @param marketingEventId
     * @return
     */
    public MarketingCrowdPlanDetailResult getDetail(String ea, Integer fsUserId, String marketingEventId) {
        MarketingCrowdPlanDetailResult result = new MarketingCrowdPlanDetailResult();
        ObjectData detail = crmV2Manager.getDetail(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (detail != null) {
            result.setMarketingEvent(detail);
            MarketingEventMarketingUserGroupRelationEntity relationEntity = marketingEventMarketingUserGroupRelationDao.getByMarketingEventId(marketingEventId);
            if (relationEntity != null) {
                MarketingUserGroupEntity marketingUserGroupEntity = marketingUserGroupDao.getById(relationEntity.getMarketingUserGroupId());
                if (marketingUserGroupEntity != null) {
                    result.setMarketingUserGroup(this.convert2MarketingUserGroupDatas(marketingUserGroupEntity));
                }
            }
            MarketingEventTimedTaskEntity timedTaskEntity = marketingEventTimedTaskEntityDao.getByMarketingEventId(marketingEventId);
            if (timedTaskEntity != null) {
                result.setTimedTaskStatus(timedTaskEntity.getExecuteStatus());
                String timedTaskSettingJson = timedTaskEntity.getTimedTaskSetting();
                try {
                    MarketingCrowdPlanDetailResult.TimedTaskSetting timedTaskSetting = GsonUtil.fromJson(timedTaskSettingJson, MarketingCrowdPlanDetailResult.TimedTaskSetting.class);
                    result.setTimedTaskSetting(timedTaskSetting);
                } catch (Exception e) {
                    log.warn("TargetCrowdOperationManager getDetail paser json error");
                }
            } else {
                result.setTimedTaskStatus("error");
            }
        }
        return result;
    }

    private MarketingUserGroupData convert2MarketingUserGroupDatas(MarketingUserGroupEntity entity) {
        MarketingUserGroupData marketingUserGroupData = new MarketingUserGroupData();
        marketingUserGroupData.setId(entity.getId());
        marketingUserGroupData.setName(entity.getName());
        return marketingUserGroupData;
    }

    /**
     * 数据统计
     *
     * @param ea
     * @param fsUserId
     * @param marketingEventId
     * @return
     */
    public MarketingCrowdPlanStatisticsResult getStatistics(String ea, Integer fsUserId, String marketingEventId) {
        MarketingCrowdPlanStatisticsResult result = new MarketingCrowdPlanStatisticsResult();
        ObjectData detail = crmV2Manager.getDetail(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (detail != null) {
            Set<String> marketingUserGroupIds = Sets.newHashSet();
            Set<String> marketingEventIds = Sets.newHashSet();
            if (detail.get("event_type") != null && MarketingEventEnum.TARGET_CROWD_OPERATION_PERIODICITY.getEventType().equals(detail.get("event_type").toString())) {
                SearchQuery searchQuery = new SearchQuery();
                searchQuery.addFilter("parent_id", Collections.singletonList(marketingEventId), OperatorConstants.EQ);
                Page<ObjectData> objectDataPage = crmV2Manager.getList(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery, Lists.newArrayList("_id"));
                if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
                    marketingEventIds = objectDataPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toSet());
                    List<MarketingEventMarketingUserGroupRelationEntity> relationEntities = marketingEventMarketingUserGroupRelationDao.getByMarketingEventIds(marketingEventIds);
                    if (CollectionUtils.isNotEmpty(relationEntities)) {
                        marketingUserGroupIds.addAll(relationEntities.stream().map(MarketingEventMarketingUserGroupRelationEntity::getMarketingUserGroupId).collect(Collectors.toList()));
                    }
                }
            } else {
                marketingEventIds.add(marketingEventId);
                MarketingEventMarketingUserGroupRelationEntity relationEntity = marketingEventMarketingUserGroupRelationDao.getByMarketingEventId(marketingEventId);
                if (relationEntity != null) {
                    marketingUserGroupIds.add(relationEntity.getMarketingUserGroupId());
                }
            }
            if (CollectionUtils.isNotEmpty(marketingUserGroupIds)) {
                Integer count = marketingUserGroupToUserRelationDao.countByGroupIds(ea, marketingUserGroupIds);
                result.setMarketingGroupUserNum(count);
            }
            com.facishare.marketing.statistic.common.result.Result<Map<String, Integer>> uVMaps = marketingEventStatisticService.getUVs(ea, marketingEventIds);
            if (uVMaps != null && uVMaps.getCode() == 0 && MapUtils.isNotEmpty(uVMaps.getData())) {
                result.setUv(uVMaps.getData().values().stream().mapToInt(Integer::intValue).sum());
            }
            Map<String, Integer> campaignCountMap = campaignMergeDataManager.getCampaignMergeCountByMarketingIds(ea, Lists.newArrayList(marketingEventIds));
            int leadNum = CollectionUtils.isNotEmpty(campaignCountMap.values()) ? campaignCountMap.values().stream().filter(Objects::nonNull).mapToInt(e -> e).sum() : 0;
            result.setLeadNum(leadNum);
        }
        return result;
    }

    public Result<Void> operateTimedTask(String ea, Integer fsUserId, String marketingEventId) {
        MarketingEventTimedTaskEntity timedTaskEntity = marketingEventTimedTaskEntityDao.getByMarketingEventId(marketingEventId);
        if (timedTaskEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        switch (timedTaskEntity.getExecuteStatus()) {
            case "todo":
                marketingEventTimedTaskEntityDao.updaterResultById(timedTaskEntity.getId(), "canceled", "todo", "operator:" + fsUserId);
                break;
            case "executed":
            case "canceled":
                String timedTaskSettingJson = timedTaskEntity.getTimedTaskSetting();
                try {
                    MarketingCrowdPlanDetailResult.TimedTaskSetting timedTaskSetting = GsonUtil.fromJson(timedTaskSettingJson, MarketingCrowdPlanDetailResult.TimedTaskSetting.class);
                    Long executeTime = RepeatDateUtil.calFirstExecuteTime(timedTaskSetting.getRepeatRangeStart(), timedTaskSetting.getRepeatRangeEnd(), timedTaskSetting.getRepeatType(), timedTaskSetting.getIncludeRepeatValue(), timedTaskSetting.getRepeatValue(), timedTaskSetting.getTriggerAtMinutes());
                    if (executeTime == null) {
                        return Result.newError(SHErrorCode.TARGET_CROWD_OPERATION_ILLEGALITY_SETTING);
                    }
                    addTimedTask(ea, marketingEventId, timedTaskSettingJson, executeTime);
                } catch (Exception e) {
                    log.warn("operateTimedTask error");
                }
                break;
            default:
                return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        return Result.newSuccess();
    }

}
