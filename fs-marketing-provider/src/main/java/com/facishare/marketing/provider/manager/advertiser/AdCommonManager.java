/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.advertiser;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.AdThirdSyncDataArg;
import com.facishare.marketing.api.arg.CustomizeFormDataEnrollArg;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.RoleConstant;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDataDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.AdLeadDataEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentCampaignEntity;
import com.facishare.marketing.provider.entity.baidu.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.manager.AuthManager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.AdvertisingDetailsObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.SfaCrmService;
import com.facishare.marketing.provider.remote.arg.LeadTransferArg;
import com.facishare.marketing.provider.remote.arg.MarkLeadToMQLArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.result.RestResult;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("adCommonManager")
@Slf4j
public class AdCommonManager {

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private BaiduCampaignDataDAO baiduCampaignDataDAO;

    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;

    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;

    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Autowired
    private TencentCampaignDAO tencentCampaignDAO;

    @Autowired
    private TencentAdGroupDataDAO tencentAdGroupDataDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private SfaCrmService sfaCrmService;

    @Autowired
    private RefreshDataManager refreshDataManager;

    @Autowired
    private AdLeadDataManager adLeadDataManager;

    @Autowired
    private AdKeywordDAO adKeywordDAO;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private AuthManager authManager;

    @Autowired
    private BaiduProjectFeedDAO baiduProjectFeedDAO;

    @Autowired
    private BaiduCampaignFeedDAO baiduCampaignFeedDAO;

    @Autowired
    private BaiduAdGroupFeedDAO baiduAdGroupFeedDAO;

    @Autowired
    private AdGroupDAO baiduAdGroupDAO;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private AdvertisingDetailsObjManager advertisingDetailsObjManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @ReloadableProperty("ad_keyword_sync_blacklist")
    private String adKeywordSyncBlackList;

    @ReloadableProperty("baidu_ad_group_sync_blacklist")
    private String baiduAdGroupSyncBlackList;

    @ReloadableProperty("keyword_plan_ad_group_dimensionality_black_list")
    private String keywordPlanAdGroupDimensionalityBlackList;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;


    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    private final static String MK_AD_ENABLE_KEY = "mk:ad:enable:%s";

    private final static String MK_AD_MARKETING_EVENT_KEY = "mk:ad:mk:event:%s";

    //中国移动
    private static final String[] CHINA_MOBILE = {
            "134", "135", "136", "137", "138", "139", "150", "151", "152", "157", "158", "159",
            "182", "183", "184", "187", "188", "178", "147", "172", "198"
    };
    //中国联通
    private static final String[] CHINA_UNICOM = {
            "130", "131", "132", "145", "155", "156", "166", "171", "175", "176", "185", "186", "166"
    };
    //中国电信
    private static final String[] CHINA_TELECOME = {
            "133", "149", "153", "173", "177", "180", "181", "189", "199"
    };

    private static final String[] SURNAME = {"赵", "钱", "孙", "李", "周", "吴", "郑", "王", "冯", "陈", "褚", "卫", "蒋", "沈", "韩", "杨", "朱", "秦", "尤", "许",
            "何", "吕", "施", "张", "孔", "曹", "严", "华", "金", "魏", "陶", "姜", "戚", "谢", "邹", "喻", "柏", "水", "窦", "章", "云", "苏", "潘", "葛", "奚", "范", "彭", "郎",
            "鲁", "韦", "昌", "马", "苗", "凤", "花", "方", "俞", "任", "袁", "柳", "酆", "鲍", "史", "唐", "费", "廉", "岑", "薛", "雷", "贺", "倪", "汤", "滕", "殷",
            "罗", "毕", "郝", "邬", "安", "常", "乐", "于", "时", "傅", "皮", "卞", "齐", "康", "伍", "余", "元", "卜", "顾", "孟", "平", "黄", "和",
            "穆", "萧", "尹", "姚", "邵", "湛", "汪", "祁", "毛", "禹", "狄", "米", "贝", "明", "臧", "计", "伏", "成", "戴", "谈", "宋", "茅", "庞", "熊", "纪", "舒",
            "屈", "项", "祝", "董", "梁", "杜", "阮", "蓝", "闵", "席", "季"};
    private static final String GIRL = "秀娟英华慧巧美娜静淑惠珠翠雅芝玉萍红娥玲芬芳燕彩春菊兰凤洁梅琳素云莲真环雪荣爱妹霞香月莺媛艳瑞凡佳嘉琼勤珍贞莉桂娣叶璧璐娅琦晶妍茜秋珊莎锦黛青倩婷姣婉娴瑾颖露瑶怡婵雁蓓纨仪荷丹蓉眉君琴蕊薇菁梦岚苑婕馨瑗琰韵融园艺咏卿聪澜纯毓悦昭冰爽琬茗羽希宁欣飘育滢馥筠柔竹霭凝晓欢霄枫芸菲寒伊亚宜可姬舒影荔枝思丽 ";
    private static final String BOY = "伟刚勇毅俊峰强军平保东文辉力明永健世广志义兴良海山仁波宁贵福生龙元全国胜学祥才发武新利清飞彬富顺信子杰涛昌成康星光天达安岩中茂进林有坚和彪博诚先敬震振壮会思群豪心邦承乐绍功松善厚庆磊民友裕河哲江超浩亮政谦亨奇固之轮翰朗伯宏言若鸣朋斌梁栋维启克伦翔旭鹏泽晨辰士以建家致树炎德行时泰盛雄琛钧冠策腾楠榕风航弘";


    @FilterLog
    public boolean checkMarketingEventIsRelatedAd(String ea, String marketingEventId) {
        if (StringUtils.isBlank(marketingEventId)) {
            return false;
        }
        // 先判断有没有绑定广告账户 这样能大大减小缓存的数量
        String value = redisManager.isBindAdAccount(ea);
        if (StringUtils.isNotEmpty(value) && !Boolean.parseBoolean(value)) {
            // 如果没有广告账户 直接返回
            return false;
        }
        if (StringUtils.isEmpty(value)) {
            List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEa(ea, true);
            boolean isBind = false;
            if (CollectionUtils.isNotEmpty(adAccountEntityList)) {
                redisManager.bindAdAccount(ea);
                isBind = true;
            }
            if (!isBind) {
                return false;
            }
        }
        String marketingEventKey = String.format(MK_AD_MARKETING_EVENT_KEY, marketingEventId);
        String marketingEventValue = redisManager.get(marketingEventKey);
        if (StringUtils.isNotEmpty(marketingEventValue)) {
            // 直接返回缓存
            return Boolean.parseBoolean(marketingEventValue);
        }

        BaiduCampaignEntity baiduCampaignEntity = baiduCampaignDAO.queryCampaignByMarketingEventId(ea, marketingEventId);
        boolean isRelated = baiduCampaignEntity != null;

        if (!isRelated) {
            HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.getBySubMarketingEventId(ea, marketingEventId);
            isRelated = headlinesAdEntity != null;
        }

        if (!isRelated) {
            HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryByMarketingEventId(ea, marketingEventId);
            isRelated = headlinesCampaignEntity != null;
        }

        if (!isRelated) {
            TencentAdGroupEntity tencentAdGroupEntity = tencentAdGroupDAO.queryByMarketingEventIdAndSubId(ea, marketingEventId);
            isRelated = tencentAdGroupEntity != null;
        }
        if (!isRelated) {
            ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            if (objectData != null ) {
                String eventType = objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName());
                if (MarketingEventEnum.AD_MARKETING.getEventType().equals(eventType)) {
                    String adUsername = objectData.getString(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName());
                    if (StringUtils.isNotEmpty(adUsername)) {
                        List<AdAccountEntity> adAccountEntityList = adAccountManager.getByUserName(ea, adUsername);
                        isRelated = CollectionUtils.isNotEmpty(adAccountEntityList);
                    }
                }
            }
        }
        redisManager.set(marketingEventKey, String.valueOf(isRelated), 60 * 60 * 24);
        return isRelated;
    }

    public String getMarketingEventIdByUtmCampaign(String ea, String campaignName) {
        if (StringUtils.isBlank(campaignName)) {
            return null;
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(MarketingEventFieldContants.NAME, Lists.newArrayList(campaignName), FilterOperatorEnum.EQ);
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        Page<ObjectData> page = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
        if (page != null && CollectionUtils.isNotEmpty(page.getDataList())) {
            return page.getDataList().get(0).getId();
        }
        return null;
    }

    // 获取广告相关的所有市场活动
    public List<String> getAllMarketingEventId(String ea) {
        Set<String> marketingEventIdSet = Sets.newHashSet();

        List<String> queryList = baiduCampaignDAO.getAllMarketingEventId(ea);
        if (CollectionUtils.isNotEmpty(queryList)) {
            marketingEventIdSet.addAll(queryList);
        }

        queryList = headlinesAdDAO.getAllMarketingEventId(ea);
        if (CollectionUtils.isNotEmpty(queryList)) {
            marketingEventIdSet.addAll(queryList);
        }

        queryList = headlinesCampaignDAO.getAllMarketingEventId(ea);
        if (CollectionUtils.isNotEmpty(queryList)) {
            marketingEventIdSet.addAll(queryList);
        }


        List<TencentAdGroupEntity> entityList = tencentAdGroupDAO.getAllMarketingEventId(ea);
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (TencentAdGroupEntity tencentAdGroupEntity : entityList) {
                if (StringUtils.isNotEmpty(tencentAdGroupEntity.getSubMarketingEventId())) {
                    marketingEventIdSet.add(tencentAdGroupEntity.getSubMarketingEventId());
                }
                if (StringUtils.isNotEmpty(tencentAdGroupEntity.getMarketingEventId())) {
                    marketingEventIdSet.add(tencentAdGroupEntity.getMarketingEventId());
                }
            }
        }
        return marketingEventIdSet.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    }

    public String getAdAccountIdByMarketingEventId(String ea, String marketingEventId) {
        if (StringUtils.isBlank(marketingEventId)) {
            return null;
        }
        BaiduCampaignEntity baiduCampaignEntity = baiduCampaignDAO.queryCampaignByMarketingEventId(ea, marketingEventId);
        if (baiduCampaignEntity != null) {
            return baiduCampaignEntity.getAdAccountId();
        }
        AdGroupEntity adGroupEntity = baiduAdGroupDAO.queryByMarketingEventId(ea, marketingEventId);
        if (adGroupEntity != null) {
            return adGroupEntity.getAdAccountId();
        }
        HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.getBySubMarketingEventId(ea, marketingEventId);
        if (headlinesAdEntity != null) {
            return headlinesAdEntity.getAdAccountId();
        }

        HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryByMarketingEventId(ea, marketingEventId);
        if (headlinesCampaignEntity != null) {
            return headlinesCampaignEntity.getAdAccountId();
        }

        TencentAdGroupEntity tencentAdGroupEntity = tencentAdGroupDAO.queryByMarketingEventIdAndSubId(ea, marketingEventId);
        if (tencentAdGroupEntity != null) {
            return tencentAdGroupEntity.getAdAccountId();
        }
        // 根据市场活动的广告账户字段获取广告账号
        ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (objectData == null) {
            return null;
        }

        String eventType = objectData.getString(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName());
        if (!MarketingEventEnum.AD_MARKETING.getEventType().equals(eventType)) {
            return null;
        }

        String adUsername = objectData.getString(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName());
        if (StringUtils.isEmpty(adUsername)) {
            return null;
        }

        List<AdAccountEntity> adAccountEntityList = adAccountManager.getByUserName(ea, adUsername);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return null;
        }
        if (adAccountEntityList.size() > 1) {
            log.error("ea: {} 存在多个同个用户名的广告账号, username: {} list: {}", ea, adUsername, adAccountEntityList);
        }
        return adAccountEntityList.get(0).getId();
    }

    // 判断是否购买了广告营销插件
    public boolean isPurchaseAdLicense(String ea) {
        return appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.MARKETING_CRM_INTEGRATING)
                || appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.MARKETING_AD_PLUGIN_APP);
    }

    public static String getRandomName() {
        Random random = new Random();
        int index = random.nextInt(SURNAME.length - 1);
        String name = SURNAME[index]; //获得一个随机的姓氏
        int i = random.nextInt(3);//可以根据这个数设置产生的男女比例
        if (i == 2) {
            int j = random.nextInt(GIRL.length() - 2);
            if (j % 2 == 0) {
                name = name + GIRL.substring(j, j + 2);
            } else {
                name = name + GIRL.substring(j, j + 1);
            }
        } else {
            int j = random.nextInt(BOY.length() - 2);
            if (j % 2 == 0) {
                name = name + BOY.substring(j, j + 2);
            } else {
                name = name + BOY.substring(j, j + 1);
            }
        }
        return name;
    }

    public static String getRandomMobile() {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        String mobile = "";//手机号前三位
        int op = random.nextInt(3);
        switch (op) {
            case 0:
                mobile = CHINA_MOBILE[random.nextInt(CHINA_MOBILE.length)];
                break;
            case 1:
                mobile = CHINA_UNICOM[random.nextInt(CHINA_UNICOM.length)];
                break;
            case 2:
                mobile = CHINA_TELECOME[random.nextInt(CHINA_TELECOME.length)];
                break;
        }
        sb.append(mobile);
        //生成手机号后8位
        for (int i = 0; i < 8; i++) {
            int temp = random.nextInt(10);
            sb.append(temp);
        }
        return sb.toString();
    }

    public static String getRandomEmail(int lMin, int lMax) {
        int length = (int) (Math.random() * (lMax - lMin + 1) + lMin);
        StringBuffer sb = new StringBuffer();
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        String[] email_suffix = "@gmail.com,@yahoo.com,@msn.com,@hotmail.com,@aol.com,@ask.com,@live.com,@qq.com,@0355.net,@163.com,@163.net,@263.net,@3721.net,@yeah.net,@googlemail.com,@126.com,@sina.com,@sohu.com,@yahoo.com.cn".split(",");
        for (int i = 0; i < length; i++) {
            int number = (int) (Math.random() * base.length());
            sb.append(base.charAt(number));
        }
        sb.append(email_suffix[(int) (Math.random() * email_suffix.length)]);
        return sb.toString();
    }

    /**
     * 生成线索数量=每日点击数*(1~10%)
     * 每日也会有线索阶段变更为MQL和SQL，每日MQL数=每日新增线索数*(5~50%)
     * 每日SQL数=每日新增MQL数*(5~50%)，新增SQL后自动创建客户对象
     * 每日商机数=每日新增SQL数*(5~50%)，新增的商机随机关联广告线索
     * 商机转赢单数=每日新增商机数*(5~50%)
     * 商机金额=5000~100000，取整数
     */
    public void createAdPrototypeRoomLeadData(String ea) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAdPrototypeRoomAccount(ea, AdSourceEnum.SOURCE_BAIDU.getSource());
        long totalClick = 0;
        Date actionDate = DateUtil.minusDay(new Date(), 1);
        List<String> allMarketingEventId = Lists.newArrayList();
        Map<String, List<String>> marketingEventIdToKeywordIdMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adAccountEntityList)) {
            List<BaiduCampaignEntity> campaignEntityList = baiduCampaignDAO.queryAllByAdAccountIdList(ea, adAccountEntityList.stream().map(AdAccountEntity::getId).collect(Collectors.toList()));
            for (AdAccountEntity adAccountEntity : adAccountEntityList) {
                Long click = baiduCampaignDataDAO.sumClickByActionDate(ea, adAccountEntity.getId(), actionDate);
                totalClick += (click == null ? 0L : click);
            }
            Map<Long, String> campaignIdToMarketingEventIdMap = campaignEntityList.stream().collect(Collectors.toMap(BaiduCampaignEntity::getCampaignId, BaiduCampaignEntity::getMarketingEventId, (v1, v2) -> v1));
            allMarketingEventId.addAll(campaignIdToMarketingEventIdMap.values());
            List<AdKeywordEntity> adKeywordEntityList = adKeywordDAO.queryByCampaignIdList(ea, Lists.newArrayList(campaignIdToMarketingEventIdMap.keySet()));
            for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
                String marketingEventId = campaignIdToMarketingEventIdMap.get(adKeywordEntity.getCampaignId());
                List<String> keywordIdList = marketingEventIdToKeywordIdMap.computeIfAbsent(marketingEventId, k -> Lists.newArrayList());
                keywordIdList.add(adKeywordEntity.getMarketingKeywordId());
            }
        }

        adAccountEntityList = adAccountManager.getAdPrototypeRoomAccount(ea, AdSourceEnum.SOURCE_JULIANG.getSource());
        if (CollectionUtils.isNotEmpty(adAccountEntityList)) {
            List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryAllByAdAccountIdList(ea, adAccountEntityList.stream().map(AdAccountEntity::getId).collect(Collectors.toList()));
            for (AdAccountEntity adAccountEntity : adAccountEntityList) {
                Long click = headlinesAdDataDAO.sumClickByActionDate(ea, adAccountEntity.getId(), actionDate);
                totalClick += (click == null ? 0L : click);
            }
            headlinesAdEntityList.forEach(e -> allMarketingEventId.add(e.getSubMarketingEventId()));
        }

        adAccountEntityList = adAccountManager.getAdPrototypeRoomAccount(ea, AdSourceEnum.SOURCE_TENCETN.getSource());
        if (CollectionUtils.isNotEmpty(adAccountEntityList)) {
            List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryAllByAdAccountIdList(ea, adAccountEntityList.stream().map(AdAccountEntity::getId).collect(Collectors.toList()));
            for (AdAccountEntity adAccountEntity : adAccountEntityList) {
                Long click = tencentAdGroupDataDAO.sumClickByActionDate(ea, adAccountEntity.getId(), actionDate);
                totalClick += (click == null ? 0L : click);
            }
            tencentAdGroupEntityList.forEach(e -> allMarketingEventId.add(e.getSubMarketingEventId()));
        }
        createAdPrototypeRoomLeadData(ea, allMarketingEventId, totalClick, marketingEventIdToKeywordIdMap);
        // 随机转换客户
        randomTransferLead(ea);
    }

     private void createAdPrototypeRoomLeadData(String ea, List<String> marketingEventIdList, long click, Map<String, List<String>> marketingEventIdToKeywordIdMap) {

        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            log.error("createAdPrototypeRoomCrmObjectData get lead desc error, ea: {} result: {}", ea, getDescribeResultResult);
            return;
        }
        ObjectDescribe leadDescribe = getDescribeResultResult.getData().getDescribe();
        // 生成线索数量 = 每日点击数 * (1 ~ 10%)
        Random random = new Random();
        long leadCount = click * (random.nextInt(10) + 1) / 100;
        Map<String, String> crmMarketingKeywordPlanIdMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(marketingEventIdToKeywordIdMap)) {
            List<String> keywordIdList = Lists.newArrayList();
            marketingEventIdToKeywordIdMap.values().forEach(keywordIdList::addAll);
            Page<ObjectData> objectDataPage = refreshDataManager.queryCrmMarketingKeywordPlan(ea, Lists.newArrayList(marketingEventIdToKeywordIdMap.keySet()), keywordIdList);
            if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
                for (ObjectData objectData : objectDataPage.getDataList()) {
                    String key = objectData.getString("marketing_event_id") + objectData.getString("marketing_keyword_id");
                    crmMarketingKeywordPlanIdMap.put(key, objectData.getId());
                }
            }
        }
        int marketingEventIdSize = marketingEventIdList.size();
        for (int i = 0; i < leadCount; i++) {
            Map<String, Object> leadObjectData = new HashMap<>();
            fillRequiredField(ea, leadObjectData, leadDescribe);
            String leadName = getRandomName();
            String leadMobile = getRandomMobile();
            leadObjectData.put("name", leadName);
            leadObjectData.put("tel", leadMobile);
            leadObjectData.put("mobile", leadMobile);
            int index = random.nextInt(marketingEventIdSize);
            String marketingEventId = marketingEventIdList.get(index);
            leadObjectData.put("marketing_event_id", marketingEventId);
            leadObjectData.put("leads_stage", "Lead");
            List<String> marketingKeywordIdList = marketingEventIdToKeywordIdMap.get(marketingEventId);
            if (CollectionUtils.isNotEmpty(marketingKeywordIdList)) {
                index = random.nextInt(marketingKeywordIdList.size());
                String keywordId = marketingKeywordIdList.get(index);
                leadObjectData.put("keyword_id", keywordId);
                String keywordServingPlanId = crmMarketingKeywordPlanIdMap.get(marketingEventId + keywordId);
                if (StringUtils.isNotEmpty(keywordServingPlanId)) {
                    leadObjectData.put("keyword_serving_plan_id", keywordServingPlanId);
                }
            }
            CreateLeadResult createLeadResult = crmV2Manager.createLead(ea, SuperUserConstants.USER_ID, leadObjectData, false, false);
            log.info("createAdPrototypeRoomCrmObjectData arg: {} createLeadResult {}", leadObjectData, createLeadResult);
        }
    }

    public void randomTransferLead(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        List<AdAccountEntity>  adAccountEntityList = adAccountManager.getAdPrototypeRoomAccountByEa(ea);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return;
        }
        List<String> adAccountIdList = adAccountEntityList.stream().map(AdAccountEntity::getId).collect(Collectors.toList());
        // 待标记为MQL的线索
        List<AdLeadDataEntity> markToMQLSelectionLeadList = getLeadByStage(ea, adAccountIdList, "Lead");
        // 待转换为客户的MQL线索
        List<AdLeadDataEntity> transferSelectionLeadList = getLeadByStage(ea, adAccountIdList, "MQL");

        if (CollectionUtils.isEmpty(markToMQLSelectionLeadList) && CollectionUtils.isEmpty(transferSelectionLeadList)) {
            log.warn("没有待转换的线索数据, ea : {}", ea);
            return;
        }

        Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.CUSTOMER.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            log.error("createAdPrototypeRoomCrmObjectData get customer desc error, ea: {} result: {}", ea, getDescribeResultResult);
            return;
        }
        ObjectDescribe customerDescribe = getDescribeResultResult.getData().getDescribe();

        getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.NEW_OPPORTUNITY.getName());
        if (getDescribeResultResult == null || !getDescribeResultResult.isSuccess() || getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST) {
            log.error("createAdPrototypeRoomCrmObjectData get NewOpportunityObj desc error, ea: {} result: {}", ea, getDescribeResultResult);
            return;
        }
        ObjectDescribe opportunityDescribe = getDescribeResultResult.getData().getDescribe();
        // 将这些线索随机打乱
        Collections.shuffle(markToMQLSelectionLeadList);
        Collections.shuffle(transferSelectionLeadList);

        Random random = new Random();
        Date now = new Date();
        int leadCount = adLeadDataManager.countByLeadCreateTime(ea, DateUtil.getDayStartDate(now), DateUtil.getDayEndDate(now));
        leadCount = Math.min(leadCount, markToMQLSelectionLeadList.size());
        // 每日MQL数 = 每日新增线索数 * (5 ~ 50%)
        long mqlLeadCount = (long) leadCount * (random.nextInt(45) + 5) / 100;
        // 每日SQL数 = 每日新增MQL数 * (5 ~ 50%)
        long sqlLeadCount = mqlLeadCount * (random.nextInt(45) + 5) / 100;
        // 每日商机数 = 每日新增SQL数 * (5 ~ 50%)
        long opportunityCount = sqlLeadCount * (random.nextInt(45) + 5) / 100;
        // 每日商机数 = 每日新增SQL数 * (5 ~ 50%)
        long winOpportunityCount = opportunityCount * (random.nextInt(45) + 5) / 100;
        log.info("randomTransferLead ea: {} leadCount: {} mqlLeadCount: {} sqlLeadCount: {} opportunityCount: {} winOpportunityCount: {}",
                ea, leadCount, mqlLeadCount, sqlLeadCount, opportunityCount, winOpportunityCount);
        for (int i = 0; i < markToMQLSelectionLeadList.size(); i++) {
            if (i < mqlLeadCount) {
                AdLeadDataEntity adLeadDataDTO = markToMQLSelectionLeadList.get(i);
                markLeadToMQL(ea, adLeadDataDTO);
            }
        }
        for (int i = 0; i < transferSelectionLeadList.size(); i++) {
            if (i < sqlLeadCount) {
                AdLeadDataEntity adLeadDataDTO = transferSelectionLeadList.get(i);
                boolean needTransferToOpportunity = i < opportunityCount;
                String opportunityStage = i < winOpportunityCount ? "5" : "1";
                transferLead(ea, adLeadDataDTO.getLeadId(), adLeadDataDTO.getLeadName(), adLeadDataDTO.getMobile(), needTransferToOpportunity, opportunityStage, customerDescribe, opportunityDescribe);
            }
        }

    }

    private List<AdLeadDataEntity> getLeadByStage(String ea, List<String> adAccountIdList, String stage) {
        int pageSize = 1000;
        int maxCount = 10000;
        int totalCount = 0;
        String lastId = null;
        List<AdLeadDataEntity> result = Lists.newArrayList();
        while(totalCount < maxCount) {
            List<AdLeadDataEntity> leadDataList = adLeadDataManager.getByAdLeadByAccountIdAndStage(ea, adAccountIdList, stage, lastId, pageSize);
            if (CollectionUtils.isEmpty(leadDataList)) {
                break;
            }
            int size = leadDataList.size();
            totalCount += size;
            lastId = leadDataList.get(size - 1).getId();
            result.addAll(leadDataList);
        }
        return result;
    }

    private void markLeadToMQL(String ea, AdLeadDataEntity adLeadDataDTO) {
        try {
            MarkLeadToMQLArg markLeadToMQLArg = new MarkLeadToMQLArg();
            markLeadToMQLArg.setObjectDataId(adLeadDataDTO.getLeadId());
            RestResult<Map<String, Object>> result = sfaCrmService.markLeadToMQL(crmV2Manager.buildHeader(ea, SuperUserConstants.USER_ID), markLeadToMQLArg);
            log.info("线索标记为MQL arg： {} result: {}", markLeadToMQLArg, result);
        } catch (Exception e) {
            log.error("线索标记为MQL ea: {} 线索ID：{}", ea, adLeadDataDTO.getLeadId(), e);
        }
    }


    private void transferLead(String ea, String leadId, String leadName, String mobile, boolean needTransferToOpportunity, String opportunityStage, ObjectDescribe customerDescribe, ObjectDescribe opportunityDescribe) {
        LeadTransferArg leadTransferArg = new LeadTransferArg();
        leadTransferArg.setLeadsId(leadId);
        leadTransferArg.setCombineCRMFeed(true);
        leadTransferArg.setSupportDetailObj(true);
        leadTransferArg.setPutTeamMembersIntoCustomer(true);
        Map<String, Map<String, Object>> dataList = new HashMap<>();

        Map<String, Object> customerObjectData = new HashMap<>();
        fillRequiredField(ea, customerObjectData, customerDescribe);
        customerObjectData.put("name", leadName);
        customerObjectData.put("tel", mobile);
        customerObjectData.put("object_describe_id", customerDescribe.getId());
        Map<String, Object> transferCustomerMap = new HashMap<>();
        transferCustomerMap.put("object_data", customerObjectData);
        dataList.put("AccountObj", transferCustomerMap);
        if (needTransferToOpportunity) {
            Map<String, Object> opportunityObjectData = new HashMap<>();
            fillRequiredField(ea, opportunityObjectData, opportunityDescribe);
            opportunityObjectData.put("name", leadName);
            opportunityObjectData.put("account_id__r", leadName);
            opportunityObjectData.put("sales_stage", opportunityStage);
            Random random = new Random();
            opportunityObjectData.put("amount", random.nextInt(95000) + 5000);
            Map<String, Object> transferOpportunityMap = new HashMap<>();
            transferOpportunityMap.put("object_data", opportunityObjectData);
            dataList.put("NewOpportunityObj", transferOpportunityMap);
        }
        leadTransferArg.setDataList(dataList);
        try {
            RestResult<Map<String, Object>> transferResult = sfaCrmService.leadTransfer(crmV2Manager.buildHeader(ea, SuperUserConstants.USER_ID), leadTransferArg);
            log.info("线索转换参数： {} result: {}", JSONObject.toJSONString(leadTransferArg), transferResult);
        } catch (Exception e) {
            log.error("线索转换异常 ea: {} 参数：{}", ea, JSONObject.toJSONString(leadTransferArg), e);
        }
    }

    private void fillRequiredField(String ea, Map<String, Object> objectData, ObjectDescribe leadDescribe) {
        for (Map.Entry<String, FieldDescribe> entry : leadDescribe.getFields().entrySet()) {
            String fieldName = entry.getKey();
            FieldDescribe fieldDescribe = entry.getValue();
            if (fieldDescribe.isRequired() == null || !fieldDescribe.isRequired() || "tenant_id".equals(fieldName) || "object_describe_api_name".equals(fieldName)) {
                continue;
            }
            String type = fieldDescribe.getType();
            if ("text".equals(type) || "html_rich_text".equals(type) || "long_text".equals(type)) {
                objectData.put(fieldName, "CRM管理系统");
            } else if ("select_one".equals(type) || "select_many".equals(type)) {
                objectData.put(fieldName, fieldDescribe.getOptions().get(0).get("value"));
            } else if ("date_time".equals(type) || "date".equals(type)) {
                objectData.put(fieldName, System.currentTimeMillis());
            } else if ("true_or_false".equals(type)) {
                objectData.put(fieldName, true);
            } else if ("employee".equals(type)) {
                objectData.put(fieldName, Lists.newArrayList(String.valueOf(SuperUserConstants.USER_ID)));
            } else if ("object_reference".equals(type)) {
                String targetApiName = fieldDescribe.getTargetApiName();
                PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
                queryFilterArg.setObjectAPIName(targetApiName);
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
                queryFilterArg.setQuery(paasQueryArg);
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, 1, 1);
                if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                    objectData.put(fieldName, objectDataInnerPage.getDataList().get(0).getId());
                }
            } else if ("email".equals(type)) {
                objectData.put(fieldName, getRandomEmail(5, 15));
            }
        }
    }

    public boolean isSyncAdKeyword(String ea) {
        //List<String> blackList = JSONArray.parseArray(adKeywordSyncBlackList, String.class);
        Set<String> blackSet = JSON.parseObject(adKeywordSyncBlackList, new TypeReference<Set<String>>(){});
        return !blackSet.contains(ea);
    }

    public boolean isSyncBaiduAdGroup(String ea) {
        Set<String> blackSet = JSON.parseObject(baiduAdGroupSyncBlackList, new TypeReference<Set<String>>(){});
        return !blackSet.contains(ea);
    }

    public int getAdObjectDataOwner(String ea) {
        //  默认拥有广告运营角色的人
        Integer employeeId = userRoleManager.getActivityEmployeeIdByRole(ea, RoleConstant.ADVERTISING_OPERATION);
        if (employeeId != null) {
            return employeeId;
        }
        // 没有广告运营角色的用营销通管理员
        List<Integer> adminUserIdList = authManager.getAppAdmins(ea);
        if (CollectionUtils.isNotEmpty(adminUserIdList)) {
            return adminUserIdList.get(0);
        }
        return SuperUserConstants.USER_ID;
    }

    // 获取广告计划/广告组的同名数量
    public Map<String, Integer> getAdSameNameCount(String ea, List<String> nameList) {
        Map<String, Integer> nameToCountMap = Maps.newHashMap();
        // 百度
        List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.getByNameList(ea, nameList);
        for (BaiduCampaignEntity baiduCampaignEntity : baiduCampaignEntityList) {
            int count = nameToCountMap.getOrDefault(baiduCampaignEntity.getCampaignName(), 0);
            nameToCountMap.put(baiduCampaignEntity.getCampaignName(), ++count);
        }

        List<AdGroupEntity> adGroupEntityList = baiduAdGroupDAO.getByNameList(ea, nameList);
        for (AdGroupEntity adGroupEntity : adGroupEntityList) {
            int count = nameToCountMap.getOrDefault(adGroupEntity.getAdGroupName(), 0);
            nameToCountMap.put(adGroupEntity.getAdGroupName(), ++count);
        }

        List<BaiduProjectFeedEntity> baiduProjectFeedEntityList = baiduProjectFeedDAO.getByNameList(ea, nameList);
        for (BaiduProjectFeedEntity baiduProjectFeedEntity : baiduProjectFeedEntityList) {
            int count = nameToCountMap.getOrDefault(baiduProjectFeedEntity.getProjectFeedName(), 0);
            nameToCountMap.put(baiduProjectFeedEntity.getProjectFeedName(), ++count);
        }
        List<BaiduCampaignFeedEntity> baiduCampaignFeedEntityList = baiduCampaignFeedDAO.getByNameList(ea, nameList);
        for (BaiduCampaignFeedEntity baiduCampaignFeedEntity : baiduCampaignFeedEntityList) {
            int count = nameToCountMap.getOrDefault(baiduCampaignFeedEntity.getCampaignFeedName(), 0);
            nameToCountMap.put(baiduCampaignFeedEntity.getCampaignFeedName(), ++count);
        }
        List<BaiduAdGroupFeedEntity> baiduAdGroupEntityList = baiduAdGroupFeedDAO.getByNameList(ea, nameList);
        for (BaiduAdGroupFeedEntity baiduAdGroupFeedEntity : baiduAdGroupEntityList) {
            int count = nameToCountMap.getOrDefault(baiduAdGroupFeedEntity.getAdgroupFeedName(), 0);
            nameToCountMap.put(baiduAdGroupFeedEntity.getAdgroupFeedName(), ++count);
        }
        //头条
        List<AdCampaignEntity> headlinesCampaignEntityList = headlinesCampaignDAO.queryByNames(ea, nameList);
        for (AdCampaignEntity adCampaignEntity : headlinesCampaignEntityList) {
            int count = nameToCountMap.getOrDefault(adCampaignEntity.getCampaignName(), 0);
            nameToCountMap.put(adCampaignEntity.getCampaignName(), ++count);
        }
        List<AdvertiserAdEntity> headlinesAdEntityList = headlinesAdDAO.queryByNames(ea, nameList);
        for (AdvertiserAdEntity advertiserAdEntity : headlinesAdEntityList) {
            int count = nameToCountMap.getOrDefault(advertiserAdEntity.getAdName(), 0);
            nameToCountMap.put(advertiserAdEntity.getAdName(), ++count);
        }
        // 腾讯
        List<TencentCampaignEntity> tencentCampaignEntityList = tencentCampaignDAO.getByNameList(ea, nameList);
        for (TencentCampaignEntity tencentCampaignEntity : tencentCampaignEntityList) {
            int count = nameToCountMap.getOrDefault(tencentCampaignEntity.getCampaignName(), 0);
            nameToCountMap.put(tencentCampaignEntity.getCampaignName(), ++count);
        }
        List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryByNameList(ea, nameList);
        for (TencentAdGroupEntity tencentAdGroupEntity : tencentAdGroupEntityList) {
            int count = nameToCountMap.getOrDefault(tencentAdGroupEntity.getAdgroupName(), 0);
            nameToCountMap.put(tencentAdGroupEntity.getAdgroupName(), ++count);
        }
        return nameToCountMap;
    }

    public String getShenhuMarketingEventId(AdThirdSyncDataArg arg) {
        String ea = arg.getEa();
        if (!"kelaike".equals(ea) || StringUtils.isBlank(arg.getPlanName())) {
            return null;
        }
        String userName = arg.getData().getString("account__c");
        if (StringUtils.isBlank(userName)) {
            return null;
        }
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryByName(ea, userName, arg.getAdSource());
        log.info("申沪事务所，userName: {}, account size: {}", userName, adAccountEntityList.size());
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return null;
        }
        try {
            AdAccountEntity adAccountEntity = adAccountEntityList.get(0);
            if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(arg.getAdSource())) {
                List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryByNameAndAdAccountId(ea, adAccountEntity.getId(), Lists.newArrayList(arg.getPlanName()));
                if (CollectionUtils.isNotEmpty(tencentAdGroupEntityList)) {
                    log.info("申沪事务所，planName: {}, ad list: {}", arg.getPlanName(), tencentAdGroupEntityList);
                    return tencentAdGroupEntityList.get(0).getSubMarketingEventId();
                }
            } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(arg.getAdSource())) {
                List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryByNameAndAdAccountId(ea, adAccountEntity.getId(), Lists.newArrayList(arg.getPlanName()));
                if (CollectionUtils.isNotEmpty(headlinesAdEntityList)) {
                    log.info("申沪事务所，planName: {}, ad list: {}", arg.getPlanName(), headlinesAdEntityList);
                    return headlinesAdEntityList.get(0).getSubMarketingEventId();
                }
            } else {
                List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.getByNameAndAdAccountId(ea, adAccountEntity.getId(), Lists.newArrayList(arg.getPlanName()));
                if (CollectionUtils.isNotEmpty(baiduCampaignEntityList)) {
                    log.info("申沪事务所，planName: {}, ad list: {}", arg.getPlanName(), baiduCampaignEntityList);
                    return baiduCampaignEntityList.get(0).getMarketingEventId();
                }
            }
        } catch (Exception e) {
            log.error("getShenhuMarketingEventId error, arg: {}", arg, e);
        }
        return null;
    }

    public String getAdUnitNameByMarketingEventId(String ea, Long accountId, String marketingEventId) {
        if (accountId == null || StringUtils.isBlank(marketingEventId)) {
            return null;
        }
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountByAccountId(ea, accountId);
        if (adAccountEntity == null) {
            return null;
        }
        if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(adAccountEntity.getSource())) {
            List<TencentAdGroupEntity> tencentAdGroupEntitylist = tencentAdGroupDAO.queryBySubMarketingEventIdList(ea, Lists.newArrayList(marketingEventId));
            return CollectionUtils.isEmpty(tencentAdGroupEntitylist) ? null : tencentAdGroupEntitylist.get(0).getAdgroupName();
        } else if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(adAccountEntity.getSource())) {
            AdGroupEntity adGroupEntity = baiduAdGroupDAO.queryByMarketingEventId(ea, marketingEventId);
            return adGroupEntity == null ? null : adGroupEntity.getAdGroupName();
        }
        return null;
    }

    public void handleKeywordPlanDescribe(String ea) {
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName());
            if (!getDescribeResultResult.isSuccess() || getDescribeResultResult.getData() == null || getDescribeResultResult.getData().getDescribe() == null) {
                log.warn("addKeywordServiceSubMarketingEventFieldAndUpdateLabel obj describe not exit ea: {}", ea);
                return;
            }
            ObjectDescribe objectDescribe = getDescribeResultResult.getData().getDescribe();
            String marketingEventIdFieldName = "marketing_event_id";
            FieldDescribe fieldDescribe = objectDescribe.getFields().get(marketingEventIdFieldName);
            if (fieldDescribe != null) {
                String label = fieldDescribe.getLabel();
                boolean needUpdate = false;
                if (!"父级市场活动".equals(label)) {
                    fieldDescribe.put("label", "父级市场活动");
                    needUpdate = true;
                }
                boolean isRequired = fieldDescribe.getBoolean("is_required");
                if (isRequired) {
                    fieldDescribe.put("is_required", false);
                    needUpdate = true;
                }
                if (needUpdate) {
                    com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> describeResult = objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), marketingEventIdFieldName, fieldDescribe);
                    log.info("addKeywordServiceSubMarketingEventFieldAndUpdateLabel ea: {} result: {}", ea, describeResult);
                }
            }
            String subMarketingEventIdFieldName = "sub_marketing_event_id";
            if (!objectDescribe.getFields().containsKey(subMarketingEventIdFieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"KeywordServingPlanObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"子级市场活动\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"label\":\"子级市场活动\",\"target_api_name\":\"MarketingEventObj\",\"target_related_list_name\":\"sub_marketing_event_keyword_serving_plan_list\",\"target_related_list_label\":\"子级关键词投放计划\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"api_name\":\"sub_marketing_event_id\",\"is_index_field\":true,\"status\":\"released\",\"is_extend\":false}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"object_reference\",\"api_name\":\"KeywordServingPlanObj_layout_generate_by_UDObjectServer__c\",\"label\":\"关键词投放计划默认布局\",\"is_default\":true}]");
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addSubMarketingEventIdFiled ea:{} result: {}", ea, result);
            }
            String outPlatformDataIdFieldName = "out_platform_data_id";
            if (!objectDescribe.getFields().containsKey(outPlatformDataIdFieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName());
                arg.setFieldDescribe("{\"describe_api_name\":\"KeywordServingPlanObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"外部id\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"外部id\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"外部id\",\"api_name\":\"out_platform_data_id\",\"is_index_field\":true,\"config\":{},\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}");
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"text\",\"api_name\":\"KeywordServingPlanObj_layout_generate_by_UDObjectServer__c\",\"label\":\"关键词投放计划默认布局\",\"is_default\":true}]");
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("add outPlatformDataIdField ea:{} result: {}", ea, result);
            }
        } catch (Exception e) {
            log.error("addKeywordServiceSubMarketingEventFieldAndUpdateLabel error, ea: {}", ea, e);
        }
    }

    public void addTermServingLinesParentMarketingEventField(String ea) {
        try {
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.TERM_SERVING_LINES.getName());
            if (!getDescribeResultResult.isSuccess() || getDescribeResultResult.getData() == null || getDescribeResultResult.getData().getDescribe() == null) {
                log.warn("addTermServingLinesParentMarketingEventField obj describe not exit ea: {}", ea);
                return;
            }
            ObjectDescribe objectDescribe = getDescribeResultResult.getData().getDescribe();
            String parentMarketingEventFieldName = "parent_marketing_event_id";
            if (!objectDescribe.getFields().containsKey(parentMarketingEventFieldName)) {
                AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
                arg.setDescribeAPIName(CrmObjectApiNameEnum.TERM_SERVING_LINES.getName());
                String describe = "{\"describe_api_name\":\"TermServingLinesObj\",\"is_index\":false,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"quote_field_type\":\"object_reference\",\"remove_mask_roles\":{},\"description\":\"\",\"is_unique\":false,\"label\":\"父级市场活动\",\"type\":\"quote\",\"quote_field\":\"marketing_event_id__r.parent_id\",\"is_required\":false,\"api_name\":\"parent_marketing_event_id\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\",\"is_extend\":false}";
                arg.setFieldDescribe(describe);
                arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"quote\",\"api_name\":\"TermServingLinesObj_layout_generate_by_UDObjectServer__c\",\"label\":\"关键词投放明细默认布局\",\"is_default\":true}]");
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
                log.info("addTermServingLinesParentMarketingEventField ea:{} result: {}", ea, result);
            }
        } catch (Exception e) {
            log.error("addTermServingLinesParentMarketingEventField error, ea: {}", ea, e);
        }
    }

    public void initAdvertiseConfig(String ea) {
        handleKeywordPlanDescribe(ea);
        addTermServingLinesParentMarketingEventField(ea);
        marketingEventManager.addOCPCLaunchField(ea);
        marketingPromotionSourceObjManager.addMarketingKeywordField(ea);
        advertisingDetailsObjManager.getOrCreateObjDescribe(ea);
        marketingEventManager.addAdCampaignId(ea);
    }

    // 同步关键词投放计划和关键词投放明细有计划维度和单元维度，在此黑名单里面，不同步单元维度，只同步以前的计划维度
    @FilterLog
    public boolean isSyncAdGroupDimensionality(String ea) {
        Set<String> blackSet = JSON.parseObject(keywordPlanAdGroupDimensionalityBlackList, new TypeReference<Set<String>>(){});
        return !blackSet.contains(ea);
    }

    @FilterLog
    // 关键词投放计划和关键词投放明细是否同步单元级别
    public boolean isSyncAdGroupDimensionality(TypeEnum typeEnum) {
        return typeEnum == TypeEnum.BAIDU_SEARCH_AD_GROUP;
    }

    // 通过unitId对应的市场活动和ea来判断是否线索关联的关键词投放计划单元维度
    @FilterLog
    public boolean isRelateAdGroupDimensionality(String ea, String marketingEventId) {
        if (StringUtils.isBlank(marketingEventId)) {
            return false;
        }
        ObjectData objectData = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        String value = objectData.getString(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName());
        AdSourceEnum adSourceEnum = AdSourceEnum.getByValue(value);
        // 目前只有百度会同步子级的关键词同步计划
        return isSyncAdGroupDimensionality(ea) && adSourceEnum == AdSourceEnum.SOURCE_BAIDU;
    }

    public String getLandingName(CustomizeFormDataEnrollArg arg) {
        if (StringUtils.isBlank(arg.getLandingUrl())) {
            return null;
        }
        if (ObjectTypeEnum.HEXAGON_PAGE.getType() == arg.getObjectType()) {
            HexagonPageEntity entity = hexagonPageDAO.getInclueDeletedById(arg.getObjectId());
            if (entity != null) {
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getByIdIngnoreStatus(entity.getHexagonSiteId());
                if (hexagonSiteEntity != null) {
                    return hexagonSiteEntity.getName();
                }
            }
        } else if (ObjectTypeEnum.CUSTOMIZE_FORM.getType() == arg.getObjectType()) {
            CustomizeFormDataEntity formDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getObjectId());
            if (formDataEntity != null) {
                return formDataEntity.getFormHeadSetting().getName();
            }
        } else if (ObjectTypeEnum.HEXAGON_SITE.getType() == arg.getObjectType()) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getByIdIngnoreStatus(arg.getObjectId());
            if (hexagonSiteEntity != null) {
                return hexagonSiteEntity.getName();
            }
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(arg.getFormId())) {
            CustomizeFormDataEntity formDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getFormId());
            if (formDataEntity != null) {
                return formDataEntity.getFormHeadSetting().getName();
            }
        }
        if (arg.getLandingUrl().length() > 100) {
            return arg.getLandingUrl().substring(0, 100);
        }
        return arg.getLandingUrl();

    }
}
