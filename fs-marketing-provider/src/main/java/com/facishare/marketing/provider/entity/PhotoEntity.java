package com.facishare.marketing.provider.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

@Data
@Entity
public class PhotoEntity implements Serializable {
    private String id;
    private int targetType;
    private String targetId;
    private String thumbnailUrl;
    private String url;
    private String path; // 保存在文件服务器上的路径
    private Date createTime;
    private int seqNum;
    private String thumbnailPath; // 缩略图的apath
    private Date updateTime;
}
