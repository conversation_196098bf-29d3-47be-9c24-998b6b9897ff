package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity;
import com.github.mybatis.mapper.IPaginationPostgresqlMapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
public interface ContentMarketingEventMaterialRelationDAO extends IPaginationPostgresqlMapper<ContentMarketingEventMaterialRelationEntity> {
    @Select("<script>" + "SELECT " + "id , ea , marketing_event_id , object_type ,object_id , is_apply_object, create_time , update_time, is_mobile_display " + "FROM content_marketing_event_material_relation "
        + "WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId}" + "<if test=\"objectTypes != null\">" + "AND object_type IN <foreach collection=\"objectTypes\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{objectTypes[${idx}]}</foreach>  " + "</if> "
        +   "<if test=\"filterIds != null\">"
        +   " AND object_id NOT IN"
        +     "<foreach collection = 'filterIds' item = 'item' open = '(' separator = ',' close = ')'>"
        +     "#{item}"
        +     "</foreach>"
        +  "</if> "
        + " <if test=\"needCheckMobileDisplay == true \">\n"
        + "  AND is_mobile_display = true\n"
        + " </if>\n"
        + " order by create_time desc " + "LIMIT #{limit} OFFSET #{offset} "
        + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> list(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectTypes") List<Integer> objectTypes, @Param("filterIds") List<String> filterIds,
        @Param("offset") Integer offset, @Param("limit") Integer limit, @Param("needCheckMobileDisplay") boolean needCheckMobileDisplay);


    @Select("<script>" + "SELECT " + "id , ea , marketing_event_id , object_type ,object_id , is_apply_object, create_time , update_time, is_mobile_display " + "FROM content_marketing_event_material_relation "
        + "WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId}" + "<if test=\"objectTypes != null\">" + "AND object_type IN <foreach collection=\"objectTypes\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{objectTypes[${idx}]}</foreach>  " + "</if> "
        +   "<if test=\"filterIds != null\">"
        +   " AND object_id NOT IN"
        +     "<foreach collection = 'filterIds' item = 'item' open = '(' separator = ',' close = ')'>"
        +     "#{item}"
        +     "</foreach>"
        +  "</if> "
        + "order by update_time desc "
        + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> listByMarketingEventIdAndObjectType(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectTypes") List<Integer> objectTypes, @Param("filterIds") List<String> filterIds);

    @Select(
        "<script>" + "SELECT " + "COALESCE(count(*), 0) " + "FROM content_marketing_event_material_relation AS c LEFT JOIN activity AS t ON c.object_id = t.activity_detail_site_id " + "WHERE c.ea = #{ea} " +
                "<if test=\"needCheckMobileDisplay == true \">\n"
                + "  AND c.is_mobile_display = true\n"
                + " </if>\n"
                + "AND c.marketing_event_id = #{marketingEventId} AND t.id is null " + "<if test=\"objectTypes != null\">"
            + " AND c.object_type IN <foreach collection=\"objectTypes\" open=\"(\" close=\")\" index=\"idx\" separator=\",\">  #{objectTypes[${idx}]}</foreach>  " + "</if> " + "</script>")
    Integer count(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectTypes") List<Integer> objectTypes, @Param("needCheckMobileDisplay") boolean needCheckMobileDisplay);

    @Select("<script>"
            + "SELECT "
            + "c.marketing_event_id as id, "
            + "COALESCE(count(*), 0) count "
            + "FROM content_marketing_event_material_relation AS c LEFT JOIN activity AS t ON c.object_id = t.activity_detail_site_id "
            + "WHERE c.ea = #{ea} "
            + "AND c.marketing_event_id IN <foreach collection=\"marketingEventIds\" open=\"(\" close=\")\" index=\"idx\" separator=\",\"> #{marketingEventIds[${idx}]}</foreach> "
            + "AND t.id is null "
            + "<if test=\"objectTypes != null\"> "
            + "AND c.object_type IN <foreach collection=\"objectTypes\" open=\"(\" close=\")\" index=\"idx\" separator=\",\"> #{objectTypes[${idx}]}</foreach> "
            + "</if> "
            + "group by c.marketing_event_id"
            + "</script>")
    List<Map<String, Object>> groupCount(@Param("ea") String ea, @Param("marketingEventIds") List<String> marketingEventIds, @Param("objectTypes") List<Integer> objectTypes);

    @Update("INSERT INTO content_marketing_event_material_relation(id, ea, marketing_event_id, object_type, object_id, create_time, update_time, is_mobile_display) VALUES (#{id}, #{ea}, #{marketingEventId},  #{objectType}, #{objectId}, now(), now(), #{isMobileDisplay}) ON CONFLICT DO NOTHING;")
    void save(ContentMarketingEventMaterialRelationEntity entity);

    @Update("<script>" + "INSERT INTO content_marketing_event_material_relation(id, ea, marketing_event_id, object_type, object_id, create_time, update_time ) VALUES "
        + "<foreach collection='entities' item='ite' separator=','>" + "(#{ite.id}, #{ite.ea}, #{ite.marketingEventId}, #{ite.objectType},#{ite.objectId}, now(), now() ) " + "</foreach>"
        + "</script>")
    void batchAdd(@Param("entities") List<ContentMarketingEventMaterialRelationEntity> entities);

    @Select("<script>"
            + "SELECT * FROM content_marketing_event_material_relation WHERE ea = #{ea} AND "
            + " ( "
            + "<foreach collection='objectInfos' item='ite' separator=' OR '>"
            + " ( "
            + "object_type = #{ite.objectType} "
            + "AND "
            + "object_id = #{ite.objectId} "
            + " <if test=\"ite.marketingEventId != null\">"
            + "  AND marketing_event_id = #{ite.marketingEventId}"
            + " </if> "
            + " ) "
            + "</foreach>"
            + " ) "
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> batchGetByEaAndObjectInfos(@Param("ea") String ea,
        @Param("objectInfos") List<ContentMarketingEventMaterialRelationEntity.ObjectInfo> objectInfos);

    @Select("<script>"
            + "SELECT * FROM content_marketing_event_material_relation WHERE ea = #{ea} AND object_type = #{objectType} AND object_id = #{objectId}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getByEaAndObjectTypeAndObjectId(@Param("ea") String ea,
                                                                                      @Param("objectType") Integer objectType,
                                                                                      @Param("objectId") String objectId,
                                                                                      @Param("page") Page page);

    @Update("UPDATE content_marketing_event_material_relation SET  ea = #{ea}, marketing_event_id = #{marketingEventId}, object_type = #{objectType}, object_id = #{objectId}, create_time = #{createTime}, update_time =  now(), is_mobile_display = #{isMobileDisplay} WHERE id = #{id}")
    void update(ContentMarketingEventMaterialRelationEntity entity);

    @Update("UPDATE content_marketing_event_material_relation SET  h5_qr_code_a_path = #{h5QrCodeAPath},miniapp_qr_code_a_path = #{miniappQrCodeAPath},baidu_qr_code_a_path = #{baiduQrCodeAPath}, update_time =  now() WHERE id = #{id}")
    void updateQrCodeAPath(@Param("id")String id, @Param("h5QrCodeAPath")String h5QrCodeAPath, @Param("miniappQrCodeAPath")String miniappQrCodeAPath, @Param("baiduQrCodeAPath")String baiduQrCodeAPath);

    @Select("<script>" + "SELECT * FROM content_marketing_event_material_relation " + "WHERE ea = #{ea} " + "AND marketing_event_id = #{marketingEventId} " + "AND object_type = #{objectType} "
        + "AND object_id = #{objectId} " + "</script>")
    ContentMarketingEventMaterialRelationEntity getByMarketingEventIdAndObjectTypeAndObjectId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
        @Param("objectType") Integer objectType, @Param("objectId") String objectId);

    @Update("DELETE FROM content_marketing_event_material_relation WHERE object_type = #{objectType} and object_id = #{objectId}")
    int deleteByObjectTypeAndObjectId(@Param("objectType") Integer objectType, @Param("objectId") String objectId);

    @Update("<script>"
            + "DELETE FROM content_marketing_event_material_relation WHERE object_type = #{objectType} and object_id IN\n"
            + "<foreach collection = 'objectIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "</script>")
    int deleteByObjectTypeAndObjectIdList(@Param("objectType") Integer objectType, @Param("objectIdList") List<String> objectIdList);

    @Update("DELETE FROM content_marketing_event_material_relation WHERE ea = #{ea} and marketing_event_id = #{marketingEventId} and object_type = #{objectType} and object_id = #{objectId}")
    int deleteByEaAndMarketingEventIdAndObjectTypeAndObjectId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectType") Integer objectType, @Param("objectId") String objectId);

    @Delete("DELETE FROM content_marketing_event_material_relation WHERE id = #{id}")
    int deleteContentMarketingEventMaterialRelationById(@Param("id") String id);

    @Select("<script>"
            + "SELECT ea,object_type,object_id,count(*) as count "
            + "FROM content_marketing_event_material_relation WHERE ea = #{ea} AND object_type = #{objectType} AND object_id IN"
            + " <foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            +" GROUP BY ea,object_type,object_id"
            +"</script>")
    List<ContentMarketingEventMaterialRelationEntity.ObjectRelationCountInfo> countByEaAndObjectTypeAndObjectId(@Param("ea") String ea, @Param("objectType") Integer objectType, @Param("objectIds") List<String> objectIds);

    @Select("<script>"
        + "SELECT object_id,marketing_event_id "
        + " FROM content_marketing_event_material_relation WHERE ea = #{ea} AND object_type = #{objectType} AND object_id IN"
        + " <foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + "     #{item}"
        + " </foreach>"
        + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getContentMarketingByEaAndObjectTypeAndObjectIds(@Param("ea") String ea, @Param("objectType") Integer objectType,
        @Param("objectIds") List<String> objectIds);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN article a ON c.object_id=a.id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType}\n"
            + "<if test=\"title != null\">"
            +      "AND a.title LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by a.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getArticleContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                         @Param("objectType") Integer objectType, @Param("title")String title,
                                                                                         @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN product p ON c.object_id=p.id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType}\n"
            + "<if test=\"title != null\">"
            +      "AND p.name LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by p.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getProductContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                         @Param("objectType") Integer objectType, @Param("title")String title,
                                                                                         @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN hexagon_site h ON c.object_id=h.id LEFT JOIN activity AS t ON c.object_id = t.activity_detail_site_id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType} AND t.id is null\n"
            + "<if test=\"title != null\">"
            +      "AND h.name LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by h.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getHexagonContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                         @Param("objectType") Integer objectType, @Param("title")String title,
                                                                                         @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN qr_poster q ON c.object_id=q.id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType} AND q.type != 2 \n"
            + "<if test=\"title != null\">"
            +      "AND q.title LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by q.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getQrcodeContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                         @Param("objectType") Integer objectType, @Param("title")String title,
                                                                                         @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN conference_invitation i ON c.object_id=i.id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType}\n"
            + "<if test=\"title != null\">"
            +      "AND i.name LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by i.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getInvitationContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                        @Param("objectType") Integer objectType, @Param("title")String title,
                                                                                        @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN activity a ON c.object_id=a.id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType}\n"
            + "<if test=\"title != null\">"
            +      "AND a.title LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by a.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getActivityContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                            @Param("objectType") Integer objectType, @Param("title")String title,
                                                                                            @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
        + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN customize_form_data a ON c.object_id = a.id WHERE c.ea=#{ea}\n"
        + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType}\n"
        + "<if test=\"title != null\">"
        + "AND a.form_head_setting ->> 'name' LIKE CONCAT('%', #{title}, '%')\n"
        + "</if>"
        + "order by a.create_time desc LIMIT #{limit} OFFSET #{offset}"
        + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getCustomizeFormDataContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
        @Param("objectType") Integer objectType, @Param("title") String title,
        @Param("offset") Integer offset, @Param("limit") Integer limit);
    
    @Update("UPDATE content_marketing_event_material_relation SET is_apply_object=#{isApplyObject} WHERE id=#{id}")
    int updateIsApplyObject(@Param("id") String id, @Param("isApplyObject") boolean isApplyObject);

    @Update("UPDATE content_marketing_event_material_relation SET is_apply_object=#{isApplyObject}, is_mobile_display = true WHERE object_id=#{objectId} AND marketing_event_id=#{marketingEventId} AND object_type=#{objectType}")
    int updateIsApplyObjectByObjectId(@Param("objectId") String objectId, @Param("objectType") int objectType, @Param("marketingEventId")String marketingEventId, @Param("isApplyObject") boolean isApplyObject);
    
    @Select("SELECT count(*) FROM content_marketing_event_material_relation WHERE ea = #{ea} AND object_id = #{objectId} AND marketing_event_id=#{marketingEventId} AND is_apply_object = true")
    int checkIsApplyObject(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("objectId") String objectId);

    @Select("SELECT id FROM content_marketing_event_material_relation WHERE event_type is null")
    List<String> getEventTypeEmptyIds();

    @Select("<script>"
            + "SELECT * FROM content_marketing_event_material_relation WHERE id IN\n"
            + " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getByIds(@Param("ids")List<String> ids);

    @Update("UPDATE content_marketing_event_material_relation SET event_type=#{eventType} WHERE id=#{id}")
    int updateEventType(@Param("id") String id, @Param("eventType")String eventType);

    @Select("SELECT object_id FROM content_marketing_event_material_relation WHERE ea = #{ea} AND marketing_event_id = #{marketingEventId} AND is_apply_object = TRUE ORDER BY create_time")
    List<String> getApplyObjectIdsByMarketingEventId(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId);

    @Select("<script>"
            + "SELECT c.* FROM content_marketing_event_material_relation c LEFT JOIN out_link a ON c.object_id = a.id WHERE c.ea=#{ea}\n"
            + "AND c.marketing_event_id=#{marketingEventId} AND c.object_type=#{objectType}\n"
            + "<if test=\"title != null\">"
            + "AND a.title LIKE CONCAT('%', #{title}, '%')\n"
            + "</if>"
            + "order by a.create_time desc LIMIT #{limit} OFFSET #{offset}"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getOutLinkContentMarketingRelation(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId,
                                                                                         @Param("objectType") Integer objectType, @Param("title") String title,
                                                                                         @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("<script>"
            + "SELECT * "
            + " FROM content_marketing_event_material_relation WHERE ea = #{ea} AND object_type = #{objectType} AND object_id = #{objectId} limit 1"
            + "</script>")
    ContentMarketingEventMaterialRelationEntity getOneObjectTypeAndObjectId(@Param("ea") String ea, @Param("objectType") Integer objectType,
                                                                                                       @Param("objectId") String objectId);

    @Select("<script>"
            + "SELECT * "
            + " FROM content_marketing_event_material_relation WHERE ea = #{ea} AND marketing_event_id IN"
            + " <foreach collection = 'marketingEventIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            + "     #{item}"
            + " </foreach>"
            + "</script>")
    List<ContentMarketingEventMaterialRelationEntity> getByMarketingEventIdList(@Param("ea") String ea,
                                                                                @Param("marketingEventIdList") List<String> marketingEventIdList);

    @Select("select * from content_marketing_event_material_relation where ea = #{ea}")
    List<ContentMarketingEventMaterialRelationEntity> getMaterialRelationList(@Param("ea") String ea);


    @Update("<script>"
            + "UPDATE content_marketing_event_material_relation\n"
            + "    <set>\n"
            + "      <if test=\"h5QrCodeAPath != null\">\n"
            + "        h5_qr_code_a_path = #{h5QrCodeAPath},\n"
            + "      </if>\n"
            + "      <if test=\"miniappQrCodeAPath != null\">\n"
            + "        miniapp_qr_code_a_path = #{miniappQrCodeAPath},\n"
            + "      </if>\n"
            + "      <if test=\"baiduQrCodeAPath != null\">\n"
            + "        baidu_qr_code_a_path = #{baiduQrCodeAPath},\n"
            + "      </if>\n"
            + "    </set>\n"
            + "    WHERE id = #{id}"
            + "</script>")
    void updateAPath(@Param("id") String id, @Param("h5QrCodeAPath") String h5QrCodeAPath, @Param("miniappQrCodeAPath") String miniappQrCodeAPath, @Param("baiduQrCodeAPath") String baiduQrCodeAPath);
}
