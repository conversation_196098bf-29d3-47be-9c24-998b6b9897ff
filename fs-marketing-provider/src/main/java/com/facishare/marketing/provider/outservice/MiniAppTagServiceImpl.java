package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.outapi.arg.BatchAddTagsToUserMarketingsArg;
import com.facishare.marketing.outapi.service.MiniAppTagService;
import com.facishare.marketing.provider.manager.ObjectTagManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by ranluch on 2019/11/4.
 */
@Slf4j
@Service("miniAppTagService")
public class MiniAppTagServiceImpl implements MiniAppTagService {

    @Autowired
    private ObjectTagManager objectTagManager;

    /**
     * 小程序文章、表单、会议*/
    @Override
    public Result<Void> batchAddTagsToUserMarketings(BatchAddTagsToUserMarketingsArg arg) {
        if (arg == null || StringUtils.isBlank(arg.getEa()) || arg.getChannelType() == null || StringUtils.isBlank(arg.getAssociationId())
            || StringUtils.isBlank(arg.getObjectId()) || arg.getObjectType() == null) {
            log.warn("MiniAppTagServiceImpl batchAddTagsToUserMarketings is params error, arg={}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        objectTagManager.batchAddTagsToUserMarketings(arg.getEa(), arg.getPhone(), arg.getChannelType(), arg.getAssociationId(), null,
            arg.getObjectId(), arg.getObjectType(), null, arg.getMarketingEventId(), "batchAddTagsToUserMarketings");
        return Result.newSuccess();
    }
}
