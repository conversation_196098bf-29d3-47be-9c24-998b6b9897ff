package com.facishare.marketing.provider.manager.usermarketingaccount;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeUserMarketingRelationDAO;
import com.facishare.marketing.provider.entity.qywx.QywxQrCodeUserMarketingRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.statistic.common.result.Result;
import com.facishare.marketing.statistic.outapi.service.UserMarketingActionStatisticService;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Optional;

@Component
@Slf4j
public class BrowserUserRelationManager {
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private BrowserUserDao browserUserDao;

    @Autowired
    private QywxAddFanQrCodeUserMarketingRelationDAO qywxAddFanQrCodeUserMarketingRelationDAO;

    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;

    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;

    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;

    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;

    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;

    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;

    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;

    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;

    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;

    @Autowired
    private UserMarketingActionStatisticService userMarketingActionStatisticService;

    public boolean bindPhoneWithBrowserUserId(String ea, String browserUserId, String phone) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(browserUserId));
        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        browserUserAssociateArg.setTriggerAction("bindPhoneWithBrowserUserId");
        userMarketingAccountAssociationManager.associate(browserUserAssociateArg);
        return true;
    }
    
    public Optional<String> getOrCreateBrowserUserIdByPhone(String ea, String phone){
        if (Strings.isNullOrEmpty(ea) || Strings.isNullOrEmpty(phone)){
            return Optional.empty();
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        UserMarketingAccountEntity userMarketingAccount = userMarketingAccountDAO.getByTenantIdAndPhone(tenantId, phone);
        if (userMarketingAccount == null){
            UserMarketingAccountEntity newEntity = new UserMarketingAccountEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setTenantId(tenantId);
            newEntity.setPhone(phone);
            boolean inserted = userMarketingAccountDAO.insert(newEntity) > 0;
            if (!inserted){
                return Optional.empty();
            }
            userMarketingAccountAssociationManager.sendUserMarketingAccountToShence(ea, newEntity.getId());
            userMarketingAccount = newEntity;
        }
        UserMarketingBrowserUserRelationEntity relation = userMarketingBrowserUserRelationDao.getOneByMarketingUserId(ea, userMarketingAccount.getId());
        if (relation != null) {
            return Optional.of(relation.getBrowserUserId());
        }
        String browserUserId = UUIDUtil.getUUID();
        browserUserDao.insert(browserUserId, "YXT-SYSTEM");
        userMarketingBrowserUserRelationDao.insertIgnore(UUIDUtil.getUUID(), eieaConverter.enterpriseIdToAccount(userMarketingAccount.getTenantId()), userMarketingAccount.getId(), browserUserId);
        return Optional.of(browserUserId);
    }

    public String getBrowserUserIdByWxOpenId(String ea, String wxAppId,String wxOpenId) {
        if(StringUtils.isEmpty(ea)||StringUtils.isEmpty(wxAppId)||StringUtils.isEmpty(wxOpenId)){
            return null;
        }
        return userMarketingBrowserUserRelationDao.getBrowserUserIdByWxOpenId(ea, wxAppId, wxOpenId);
    }

    public void deleteUselessBrowseUser(String isPreview) {
        // 是否是预览 预览的话就不删除数据，只打印日志
        boolean preview = Boolean.parseBoolean(isPreview);
        String lastId = null;
        Date lastSixMonth = DateUtil.plusMonth(new Date(), -6);
        int limit = 1000;
        // 获取过去6个月的访客
        List<String> browserIdList = browserUserDao.getByPastSpecifiedTimeAndLastId(lastSixMonth, lastId, limit);

        while (CollectionUtils.isNotEmpty(browserIdList)) {
            List<UserMarketingBrowserUserRelationEntity> userRelationEntityList = userMarketingBrowserUserRelationDao.getByBrowserUserIds(browserIdList);

            Set<String> deleteUserBrowserUserIdSet = Sets.newHashSet();
            Map<String, String> browserUserIdToUserMarketingIdMap = userRelationEntityList.stream().collect(Collectors.toMap(UserMarketingBrowserUserRelationEntity::getBrowserUserId, UserMarketingBrowserUserRelationEntity::getUserMarketingId, (k1, k2) -> k1));
            Map<String, String> userMarketingIdToBrowserUserIdMap = userRelationEntityList.stream().collect(Collectors.toMap(UserMarketingBrowserUserRelationEntity::getUserMarketingId, UserMarketingBrowserUserRelationEntity::getBrowserUserId, (k1, k2) -> k1));
            // 浏览器用户没有关联营销用户，则删除
            browserIdList.stream().filter(e -> !browserUserIdToUserMarketingIdMap.containsKey(e)).forEach(deleteUserBrowserUserIdSet::add);

            // 删除那些只关联的浏览器用户的营销用户
            Map<String, List<UserMarketingBrowserUserRelationEntity>> eaToRelationListMap = userRelationEntityList.stream().collect(Collectors.groupingBy(UserMarketingBrowserUserRelationEntity::getEa));
            for (Map.Entry<String, List<UserMarketingBrowserUserRelationEntity>> entry : eaToRelationListMap.entrySet()) {
                String ea = entry.getKey();
                List<UserMarketingBrowserUserRelationEntity> list = entry.getValue();
                // 要删除的营销用户ID
                Set<String> deleteUserMarketingIdSet = Sets.newHashSet();

                List<String> userMarketingIdList = list.stream().map(UserMarketingBrowserUserRelationEntity::getUserMarketingId).distinct().collect(Collectors.toList());
                // 删除那些营销用户都不存在
                List<UserMarketingAccountEntity> userMarketingAccountEntityList = userMarketingAccountDAO.batchGet(userMarketingIdList);
                Set<String> existUserMarketingIdSet = userMarketingAccountEntityList.stream().map(UserMarketingAccountEntity::getId).collect(Collectors.toSet());
                userMarketingIdList.stream().filter(e -> !existUserMarketingIdSet.contains(e)).forEach(deleteUserMarketingIdSet::add);

                // 二维码的存在就不删除了
                List<QywxQrCodeUserMarketingRelationEntity> qywxQrCodeRelationEntityList = qywxAddFanQrCodeUserMarketingRelationDAO.getByUserMarketingIdList(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, qywxQrCodeRelationEntityList.stream().map(QywxQrCodeUserMarketingRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联线索对象就不删除了
                List<UserMarketingCrmLeadAccountRelationEntity> crmLeadAccountRelationEntityList = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, crmLeadAccountRelationEntityList.stream().map(UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联客户对象就不删除了
                List<UserMarketingCrmAccountAccountRelationEntity> crmAccountAccountRelationEntityList = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, crmAccountAccountRelationEntityList.stream().map(UserMarketingCrmAccountAccountRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联联系人对象就不删除了
                List<UserMarketingCrmContactAccountRelationEntity> crmContactAccountRelationEntityList = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, crmContactAccountRelationEntityList.stream().map(UserMarketingCrmContactAccountRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联会员对象就不删除了
                List<UserMarketingCrmMemberRelationEntity> crmMemberRelationEntityList = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, crmMemberRelationEntityList.stream().map(UserMarketingCrmMemberRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联微信粉丝对象就不删除了
                List<UserMarketingCrmWxUserAccountRelationEntity> crmWxUserAccountRelationEntityList = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, crmWxUserAccountRelationEntityList.stream().map(UserMarketingCrmWxUserAccountRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联微信外部联系人对象就不删除了
                List<UserMarketingCrmWxWorkExternalUserRelationEntity> crmWxWorkExternalUserRelationEntityList = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, crmWxWorkExternalUserRelationEntityList.stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联企业微信外部联系人就不删除了
                List<UserMarketingWxWorkExternalUserRelationEntity> wxWorkExternalUserRelationEntityList = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, wxWorkExternalUserRelationEntityList.stream().map(UserMarketingWxWorkExternalUserRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联小程序用户就不删除了
                List<UserMarketingMiniappAccountRelationEntity> miniappAccountRelationEntityList = userMarketingMiniappAccountRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, miniappAccountRelationEntityList.stream().map(UserMarketingMiniappAccountRelationEntity::getUserMarketingId).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    continue;
                }
                // 关联微信openId就不删除了
                List<UserMarketingWxServiceAccountRelationEntity> wxServiceAccountRelationEntityList = userMarketingWxServiceAccountRelationDao.listByUserMarketingIds(ea, userMarketingIdList);
                removeUserMarketingId(userMarketingIdList, wxServiceAccountRelationEntityList.stream().map(UserMarketingWxServiceAccountRelationEntity::getUserMarketingId).collect(Collectors.toSet()));

                deleteUserMarketingIdSet.addAll(userMarketingIdList);
                userMarketingIdList.stream().filter(userMarketingIdToBrowserUserIdMap::containsKey).forEach(deleteUserBrowserUserIdSet::add);
                log.info("ea: {} 要删除的营销用户 size: {} browser user size: {} isList: {}", ea, deleteUserMarketingIdSet.size(), deleteUserBrowserUserIdSet.size(), deleteUserMarketingIdSet);
                if (preview) {
                    log.info("ea: {} 要删除的营销用户 size: {} browser user size: {} isList: {}, browserIdList: {} userMarketingIdList: {}", ea, deleteUserMarketingIdSet.size(), deleteUserBrowserUserIdSet.size(), deleteUserMarketingIdSet, deleteUserBrowserUserIdSet, deleteUserMarketingIdSet);
                } else {
                    if (CollectionUtils.isNotEmpty(deleteUserBrowserUserIdSet)) {
                        List<String> deleteUserBrowserUserIdList = Lists.newArrayList(deleteUserBrowserUserIdSet);
                        browserUserDao.deleteByIds(deleteUserBrowserUserIdList);
                        userMarketingBrowserUserRelationDao.deleteByEaAndBrowserUserIdList(ea, deleteUserBrowserUserIdList);
                    }
                    if (CollectionUtils.isNotEmpty(deleteUserMarketingIdSet)) {
                        userMarketingAccountDAO.deleteByIdList(Lists.newArrayList(deleteUserMarketingIdSet));
                        deleteUserMarketingActionRecord(ea, deleteUserMarketingIdSet);
                    }
                }
            }
            lastId = browserIdList.get(browserIdList.size() - 1);
            browserIdList = browserUserDao.getByPastSpecifiedTimeAndLastId(lastSixMonth, lastId, limit);
        }
    }

    private void deleteUserMarketingActionRecord(String ea, Set<String> deleteUserMarketingIdSet) {
        try {
            Result<Integer> deleteResult = userMarketingActionStatisticService.deleteByEaAndUserMarketingIdList(ea, Lists.newArrayList(deleteUserMarketingIdSet));
            log.info("deleteUserMarketingActionRecord ea: {} result: {}", ea, deleteResult);
        } catch (Exception e) {
            log.error("deleteUserMarketingActionRecord ea: {} size: {} deleteUserMarketingIdSet: {}", ea, deleteUserMarketingIdSet.size(), deleteUserMarketingIdSet, e);
        }
    }


    private void removeUserMarketingId(List<String> userIdList, Set<String> retainUserMarketingIdSet) {
        userIdList.removeIf(retainUserMarketingIdSet::contains);
    }

}
