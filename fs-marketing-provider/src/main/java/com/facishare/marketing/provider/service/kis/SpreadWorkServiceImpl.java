package com.facishare.marketing.provider.service.kis;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.result.kis.*;
import com.facishare.marketing.api.service.kis.SpreadWorkService;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.kis.DailyPosterDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.CardEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.kis.DailyPosterEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.image.ImageDrawer;
import com.facishare.marketing.provider.manager.image.ImageDrawerTypeEnum;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.util.UnicodeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName SpreadWorkServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2019/2/25 7:17 PM
 */
@Slf4j
@Service("spreadWorkService")
public class SpreadWorkServiceImpl implements SpreadWorkService {

    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private DailyPosterDAO dailyPosterDAO;

    @Autowired
    private SpreadWorkManager spreadWorkManager;
    @Autowired
    private CoverImageManager coverImageManager;
    @Autowired
    private ImageCreator imageCreator;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @ReloadableProperty("picture.fsEa")
    private String mankeepEa;
    @ReloadableProperty("daily_poster_default_bg_apath")
    private String dailyPosterDefaultBgApath;
    @ReloadableProperty("daily_poster_default_content")
    private String dailyPosterDefaultContent;
    @Autowired
    private QywxUserManager qywxUserManager;

    private static String defaultDailyPosterId = "defaultPosterId";

    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private AccountManager accountManager;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Override
    public Result<GetCardPosterInfoResult> getCardPosterInfo(String ea, Integer userId, String uid) {
        String cardUid;
        if (userId != null) {
            UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, userId);
            if (userRelationEntity == null) {
                return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
            }
            cardUid = userRelationEntity.getUid();
        } else if (StringUtils.isNotBlank(ea) && userId != null) {
            cardUid = qywxUserManager.getUidByFsUserInfo(ea, userId);
            if (cardUid == null) {
                return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
            }
        } else if (StringUtils.isNotBlank(uid)) {
            cardUid = uid;
        } else {
            log.warn("SpreadWorkServiceImpl.getCardPosterInfo param error ea:{}, userId:{}, uid:{}", ea, userId, uid);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        CardEntity cardEntity = cardDAO.queryCardInfoByUid(cardUid);
        if (null == cardEntity) {
            return new Result<>(SHErrorCode.BUSINESSCARD_USER_NOFOUND);
        }

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), cardEntity.getUid());
        // 为空时尝试重新创建
        if (CollectionUtils.isEmpty(photoEntityList)) {
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(cardUid);
            if (fsBindEntity != null) {
                accountManager.createCardQRCode(cardUid, fsBindEntity.getFsEa());
            }
            // 再次校验
            photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), cardEntity.getUid());
            if (CollectionUtils.isEmpty(photoEntityList)) {
                log.warn("CoverImageManager.getCardPosterInfo photoEntityList is null, uid={}", cardEntity.getUid());
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
        }

        PhotoEntity qrCodePhotoEntity = photoEntityList.get(0);

        List<PhotoEntity> coverPhotoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.MINI_COVER_CARD_SHARE.getType(), cardEntity.getUid());
        if (CollectionUtils.isEmpty(coverPhotoEntityList)) {
            log.warn("CoverImageManager.getCardPosterInfo coverPhotoEntityList is null, uid={}", cardEntity.getUid());
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        String coverUrl="";
        if(CollectionUtils.isNotEmpty(coverPhotoEntityList)){
            PhotoEntity coverCardPhotoEntity = coverPhotoEntityList.get(0);
            coverUrl = coverCardPhotoEntity.getUrl();
        }


        GetCardPosterInfoResult result = new GetCardPosterInfoResult();
        result.setName(UnicodeFormatter.decodeUnicodeString(cardEntity.getName()));
        result.setAvatar(cardEntity.getAvatar());
        result.setVocation(UnicodeFormatter.decodeUnicodeString(cardEntity.getVocation()));
        result.setCompanyName(UnicodeFormatter.decodeUnicodeString(cardEntity.getCompanyName()));
        result.setQrUrl(qrCodePhotoEntity.getUrl());
        result.setPhone(cardEntity.getPhone());
        result.setCardUid(cardEntity.getUid());
        result.setCoverUrl(coverUrl);
        result.setCardId(cardEntity.getId());

        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                Map<String, Object> normalImageParams = new HashMap<>();
                normalImageParams.put("uid", cardUid);
                ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.CardPoster);
                String tapath = imageDrawer.draw(normalImageParams);
                String url = fileV2Manager.getUrlByPath(tapath, null, false);
                if (StringUtils.isNotBlank(url)) {
                    redisManager.setKISCardPosterToRedis(cardUid, url);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);

        return new Result<>(SHErrorCode.SUCCESS, result);
    }


    @Override
    public Result<GetCardQrCodeWithAvatarResult> getCardQrCodeWithAvatar(String ea, Integer fsUserId, String uid) {
        if (StringUtils.isBlank(uid) && StringUtils.isNotBlank(ea) && fsUserId != null) {
            uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        }
        if (StringUtils.isBlank(uid)) {
            log.warn("SpreadWorkServiceImpl.getCardQrCodeWithAvatar error uid is null");
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        // 绘制图片
        GetCardQrCodeWithAvatarResult result = new GetCardQrCodeWithAvatarResult();
        Map<String, Object> lmImageParams = new HashMap<>();
        lmImageParams.put("uid", uid);
        ImageDrawer lmImageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.CardQrCodeWithAvatar);
        String avatarTapath = lmImageDrawer.draw(lmImageParams);
        String url = null;
        if (StringUtils.isNotBlank(avatarTapath)) {
            url = fileV2Manager.getUrlByPath(avatarTapath, ea, false);
        }
        result.setUrl(url);
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> checkMarketingLicense(String ea) {
        String version = appVersionManager.getCurrentAppVersion(ea);
        if (StringUtils.isBlank(version)) {
            return Result.newSuccess();
        }

        return Result.newSuccess(version);
    }

    @Override
    public Result<CreateWXQRCodeByFeedResult> createWXQRCodeByFeedForPartner(String upstreamEa, String outTenantId, String outUserId, ObjectTypeEnum objectTypeEnum, String objectId, String feedKey, String marketingActivityId, boolean partner, String value) {
        if (EmptyUtil.isNullForList(feedKey, objectTypeEnum, objectId, upstreamEa, outUserId)) {
            log.warn("SpreadWorkServiceImpl.createWXQRCodeByFeedForPartner param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        QRCodeManager.CreateQRCodeResult createQRCodeResult = spreadWorkManager.createWXQRCodeByFeed(upstreamEa, null, null, objectTypeEnum, objectId, null, feedKey, marketingActivityId, value, false);
        if (null == createQRCodeResult || StringUtils.isBlank(createQRCodeResult.getQrCodeApath())) {
            log.warn("SpreadWorkServiceImpl.createWXQRCodeByFeed createWXQRCodeByFeed failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        CreateWXQRCodeByFeedResult result = new CreateWXQRCodeByFeedResult();
        result.setQrCodeId(createQRCodeResult.getQrCodeId());
        result.setQrCodeUrl(createQRCodeResult.getQrCodeUrl());
        result.setQrCodeAPath(createQRCodeResult.getQrCodeApath());
        return new Result(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<GetCardPosterResult> getCardPoster(String ea, Integer userId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("SpreadWorkServiceImpl.getCardPoster ea is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == userId) {
            log.warn("SpreadWorkServiceImpl.getCardPoster userId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
        if (uid == null) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        String url = redisManager.getKISCardPoster(uid);
        if (StringUtils.isBlank(url)) {
            Map<String, Object> normalImageParams = new HashMap<>();
            normalImageParams.put("uid", uid);
            ImageDrawer imageDrawer = imageCreator.getImageDrawer(ImageDrawerTypeEnum.CardPoster);
            String tapath = imageDrawer.draw(normalImageParams);
            url = fileV2Manager.getUrlByPath(tapath, null, false);
        }

        if (StringUtils.isBlank(url)) {
            log.error("SpreadWorkServiceImpl.getCardPoster url is blank, uid={}", uid);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        GetCardPosterResult result = new GetCardPosterResult();
        result.setUrl(url);

        return new Result<>(SHErrorCode.SUCCESS, result);

    }

    @Override
    public Result<GetDailyHeadPosterInfoResult>getDailyHeadPosterInfo(String ea, Integer userId) {
        if (StringUtils.isBlank(ea)) {
            log.warn("SpreadWorkServiceImpl.getDailyHeadPosterInfo ea is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == userId) {
            log.warn("SpreadWorkServiceImpl.getDailyHeadPosterInfo userId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
        if (uid == null) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
        if (null == cardEntity) {
            return new Result<>(SHErrorCode.BUSINESSCARD_USER_NOFOUND);
        }

        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), cardEntity.getUid());
        if (CollectionUtils.isEmpty(photoEntityList)) {
            log.warn("SpreadWorkServiceImpl.getDailyHeadPosterInfo photoEntityList is null, uid={}", cardEntity.getUid());
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        PhotoEntity qrCodePhotoEntity = photoEntityList.get(0);

        List<PhotoEntity> coverPhotoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.MINI_COVER_CARD_SHARE.getType(), cardEntity.getUid());
        if (CollectionUtils.isEmpty(coverPhotoEntityList)) {
            log.warn("CoverImageManager.getDailyHeadPosterInfo coverPhotoEntityList is null, uid={}", cardEntity.getUid());
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        PhotoEntity coverCardPhotoEntity = coverPhotoEntityList.get(0);

        DailyPosterEntity dailyPosterEntity = dailyPosterDAO.queryDailyPosterByEaAndDate(new Date());

        GetDailyHeadPosterInfoResult result = new GetDailyHeadPosterInfoResult();
        List<GetDailyHeadPosterBaseInfoResult>baseInfoList = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();

        if (null != dailyPosterEntity) {
            GetDailyHeadPosterBaseInfoResult getDailyHeadPosterBaseInfoResult = new GetDailyHeadPosterBaseInfoResult();
            getDailyHeadPosterBaseInfoResult.setDailyPosterId(dailyPosterEntity.getId());
            getDailyHeadPosterBaseInfoResult.setBgUrl(fileV2Manager.getUrlByPath(dailyPosterDefaultBgApath, ea, false));
            getDailyHeadPosterBaseInfoResult.setContent(dailyPosterEntity.getContent());
            getDailyHeadPosterBaseInfoResult.setMonth(Integer.valueOf(calendar.get(Calendar.MONTH)+1).toString());
            getDailyHeadPosterBaseInfoResult.setDay(Integer.valueOf(calendar.get(Calendar.DATE)).toString());

            baseInfoList.add(getDailyHeadPosterBaseInfoResult);
        } else {
            GetDailyHeadPosterBaseInfoResult getDailyHeadPosterBaseInfoResult = new GetDailyHeadPosterBaseInfoResult();
            getDailyHeadPosterBaseInfoResult.setDailyPosterId(defaultDailyPosterId);
            getDailyHeadPosterBaseInfoResult.setBgUrl(fileV2Manager.getUrlByPath(dailyPosterDefaultBgApath, ea, false));
            getDailyHeadPosterBaseInfoResult.setContent(dailyPosterDefaultContent);
            getDailyHeadPosterBaseInfoResult.setMonth(Integer.valueOf(calendar.get(Calendar.MONTH) + 1).toString());
            getDailyHeadPosterBaseInfoResult.setDay(Integer.valueOf(calendar.get(Calendar.DATE)).toString());

            baseInfoList.add(getDailyHeadPosterBaseInfoResult);
        }

        /**
         String coverUrl = fileV2Manager.zoom(coverCardPhotoEntity.getUrl(), null, 100);
         if (StringUtils.isBlank(coverUrl)) {
         coverUrl = coverCardPhotoEntity.getUrl();
         }**/
        String coverUrl = coverCardPhotoEntity.getUrl();

        result.setBaseInfoList(baseInfoList);
        result.setName(UnicodeFormatter.decodeUnicodeString(cardEntity.getName()));
        result.setVocation(UnicodeFormatter.decodeUnicodeString(cardEntity.getVocation()));
        result.setAvatar(cardEntity.getAvatar());
        result.setQrUrl(qrCodePhotoEntity.getUrl());
        result.setCardUid(cardEntity.getUid());
        result.setCoverUrl(coverUrl);
        result.setCardId(cardEntity.getId());

        return new Result<>(SHErrorCode.SUCCESS, result);

    }

    @Override
    public Result<GetMaterialPosterResult> getMaterialPoster(String feedKey, ObjectTypeEnum objectTypeEnum, String objectId, String ea, Integer userId, String spreadTaskId, String marketingActivityId) {
        if (EmptyUtil.isNullForList(feedKey, objectTypeEnum, objectId, ea, userId)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (objectTypeEnum != ObjectTypeEnum.ARTICLE && objectTypeEnum != ObjectTypeEnum.ACTIVITY && objectTypeEnum != ObjectTypeEnum.PRODUCT) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster objectTypeEnum is block, objectTypeEnum={}", objectTypeEnum);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
        if (EmptyUtil.isNullForList(uid)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster fsBindEntity is null, ea={}, userId={}", ea, userId);
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        SpreadWorkManager.GetMarketingActivityIdAndSpreadTaskIdResult getMarketingActivityIdAndSpreadTaskIdResult = spreadWorkManager.getMarketingActivityIdAndSpreadTaskId(ea, userId, objectTypeEnum, objectId, spreadTaskId, marketingActivityId, false);
        if (null == getMarketingActivityIdAndSpreadTaskIdResult) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster getMarketingActivityIdAndSpreadTaskId failed, ea={}, userId={}, objectTypeEnum={}, objectId={}, spreadTaskId={}, marketingActivityId={}", ea, userId, objectTypeEnum, objectId, spreadTaskId, marketingActivityId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        spreadTaskId = getMarketingActivityIdAndSpreadTaskIdResult.getSpreadTaskId();
        marketingActivityId = getMarketingActivityIdAndSpreadTaskIdResult.getMarketingActivityId();





        String feedId = spreadWorkManager.addFeed(uid, objectTypeEnum, objectId, feedKey, false);
        if (StringUtils.isBlank(feedId)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster addFeed failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        QRCodeManager.CreateQRCodeResult createQRCodeResult = spreadWorkManager.createWXQRCodeByFeed(ea, userId, uid, objectTypeEnum, objectId, feedId, feedKey, marketingActivityId, null, true);
        if (null == createQRCodeResult || StringUtils.isBlank(createQRCodeResult.getQrCodeApath())) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster addFeed failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        String tapath = coverImageManager.createMaterialPoster(objectTypeEnum, objectId, createQRCodeResult.getQrCodeApath(), uid);
        String url = fileV2Manager.getUrlByPath(tapath, null ,false);
        if (StringUtils.isBlank(url)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster url is blank");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        spreadWorkManager.updateSpreadTaskAndAddPersonalRecord(ea, userId, spreadTaskId, marketingActivityId);

        GetMaterialPosterResult result = new GetMaterialPosterResult();
        result.setMarketingActivityId(marketingActivityId);
        result.setUrl(url);

        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<GetMaterialPosterResult> getMaterialPosterForPartner(String feedKey, ObjectTypeEnum objectTypeEnum, String objectId, String upstreamEa, String outTenantId, String outUserId, String spreadTaskId, String marketingActivityId) {
        if (EmptyUtil.isNullForList(feedKey, objectTypeEnum, objectId, upstreamEa, outTenantId,outUserId)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPosterForPartner param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (objectTypeEnum != ObjectTypeEnum.ARTICLE && objectTypeEnum != ObjectTypeEnum.ACTIVITY && objectTypeEnum != ObjectTypeEnum.PRODUCT) {
            log.warn("SpreadWorkServiceImpl.getMaterialPosterForPartner objectTypeEnum is block, objectTypeEnum={}", objectTypeEnum);
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        SpreadWorkManager.GetMarketingActivityIdAndSpreadTaskIdResult getMarketingActivityIdAndSpreadTaskIdResult = spreadWorkManager.getMarketingActivityIdAndSpreadTaskIdForPartner(outTenantId,upstreamEa, Integer.valueOf(outUserId), objectTypeEnum, objectId, spreadTaskId, marketingActivityId);
        if (null == getMarketingActivityIdAndSpreadTaskIdResult) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster getMarketingActivityIdAndSpreadTaskId failed, ea={}, userId={}, objectTypeEnum={}, objectId={}, spreadTaskId={}, marketingActivityId={}", upstreamEa, outUserId, objectTypeEnum, objectId, spreadTaskId, marketingActivityId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        String uid = "HBYX" + upstreamEa + outTenantId + outUserId;
        spreadTaskId = getMarketingActivityIdAndSpreadTaskIdResult.getSpreadTaskId();
        marketingActivityId = getMarketingActivityIdAndSpreadTaskIdResult.getMarketingActivityId();
        String feedId = spreadWorkManager.addFeed(uid, objectTypeEnum, objectId, feedKey, false);
        if (StringUtils.isBlank(feedId)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPosterForPartner addFeed failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        QRCodeManager.CreateQRCodeResult createQRCodeResult = spreadWorkManager.createWXQRCodeByFeedForPartner(upstreamEa, outTenantId,outUserId, uid, objectTypeEnum, objectId, feedId, feedKey, marketingActivityId, null, true);
        if (null == createQRCodeResult || StringUtils.isBlank(createQRCodeResult.getQrCodeApath())) {
            log.warn("SpreadWorkServiceImpl.getMaterialPosterForPartner addFeed failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        String tapath = coverImageManager.createMaterialPoster(objectTypeEnum, objectId, createQRCodeResult.getQrCodeApath(), uid);
        String url = fileV2Manager.getUrlByPath(tapath, null ,false);
        if (StringUtils.isBlank(url)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPosterForPartner url is blank");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        spreadWorkManager.updateSpreadTaskAndAddPersonalRecordForPartner(upstreamEa,outTenantId, Integer.valueOf(outUserId), spreadTaskId, marketingActivityId);

        GetMaterialPosterResult result = new GetMaterialPosterResult();
        result.setMarketingActivityId(marketingActivityId);
        result.setUrl(url);

        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<AddFeedAndMarketingActivityResult> addFeedAndMarketingActivity(String ea, Integer userId, String feedKey, ObjectTypeEnum objectTypeEnum, String objectId, String marketingActivityId, boolean neverAddFeed, boolean feedVisible,boolean partner) {
        if (EmptyUtil.isNullForList(objectTypeEnum, objectId, ea, userId)) {
            log.warn("SpreadWorkServiceImpl.getMaterialPoster param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
        if (StringUtils.isBlank(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        String feedId = null;
        if (BooleanUtils.isNotTrue(neverAddFeed)) {
            feedId = spreadWorkManager.addFeed(uid, objectTypeEnum, objectId, feedKey, feedVisible);
        }

        String spreadTaskId = null;
        SpreadWorkManager.GetMarketingActivityIdAndSpreadTaskIdResult getMarketingActivityIdAndSpreadTaskIdResult = spreadWorkManager.getMarketingActivityIdAndSpreadTaskId(ea, userId, objectTypeEnum, objectId, spreadTaskId, marketingActivityId, partner);
        if (null == getMarketingActivityIdAndSpreadTaskIdResult) {
            log.warn("SpreadWorkServiceImpl.addFeedAndMarketingActivity getMarketingActivityIdAndSpreadTaskId failed, ea={}, userId={}, objectTypeEnum={}, objectId={}, spreadTaskId={}, marketingActivityId={}", ea, userId, objectTypeEnum, objectId, spreadTaskId, marketingActivityId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        spreadTaskId = getMarketingActivityIdAndSpreadTaskIdResult.getSpreadTaskId();
        marketingActivityId = getMarketingActivityIdAndSpreadTaskIdResult.getMarketingActivityId();

        spreadWorkManager.updateSpreadTaskAndAddPersonalRecord(ea, userId, spreadTaskId, marketingActivityId);

        AddFeedAndMarketingActivityResult result = new AddFeedAndMarketingActivityResult();
        result.setFeedId(feedId);
        result.setFeedKey(feedKey);
        result.setMarketingActivityId(marketingActivityId);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<AddFeedAndMarketingActivityResult> addFeedAndMarketingActivityForPartner(String upstreamEa,String outTenantId, String outUserId, String feedKey, ObjectTypeEnum objectTypeEnum, String objectId, String marketingActivityId, boolean neverAddFeed, boolean feedVisible,boolean partner) {
        if (EmptyUtil.isNullForList(objectTypeEnum, objectId, upstreamEa, outTenantId,outUserId)) {
            log.warn("SpreadWorkServiceImpl.addFeedAndMarketingActivityForPartner param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String feedId = null;
        if (BooleanUtils.isNotTrue(neverAddFeed)) {
            feedId = spreadWorkManager.addFeed( UUIDUtil.getUUID(), objectTypeEnum, objectId, feedKey, feedVisible);
        }

        String spreadTaskId = null;
        SpreadWorkManager.GetMarketingActivityIdAndSpreadTaskIdResult getMarketingActivityIdAndSpreadTaskIdResult = spreadWorkManager.getMarketingActivityIdAndSpreadTaskIdForPartner(outTenantId,upstreamEa, Integer.valueOf(outUserId), objectTypeEnum, objectId, spreadTaskId, marketingActivityId);
        if (null == getMarketingActivityIdAndSpreadTaskIdResult) {
            log.warn("SpreadWorkServiceImpl.addFeedAndMarketingActivity getMarketingActivityIdAndSpreadTaskId failed, upstreamEa={}, outUserId={}, objectTypeEnum={}, objectId={}, spreadTaskId={}, marketingActivityId={}", upstreamEa, outUserId, objectTypeEnum, objectId, spreadTaskId, marketingActivityId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        spreadTaskId = getMarketingActivityIdAndSpreadTaskIdResult.getSpreadTaskId();
        marketingActivityId = getMarketingActivityIdAndSpreadTaskIdResult.getMarketingActivityId();
        spreadWorkManager.updateSpreadTaskAndAddPersonalRecordForPartner(upstreamEa,outTenantId, Integer.valueOf(outUserId), spreadTaskId, marketingActivityId);
        AddFeedAndMarketingActivityResult result = new AddFeedAndMarketingActivityResult();
        result.setFeedId(feedId);
        result.setFeedKey(feedKey);
        result.setMarketingActivityId(marketingActivityId);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<GetMarketingActivityIdResult> getMarketingActivityId(String ea, Integer userId, ObjectTypeEnum objectTypeEnum, String objectId) {
        if (EmptyUtil.isNullForList(objectTypeEnum, objectId, ea, userId)) {
            log.warn("SpreadWorkServiceImpl.getMarketingActivityId param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        String marketingActivityId = spreadWorkManager.getMarketingActivityId(ea, userId, objectTypeEnum, objectId, false);
        if (StringUtils.isBlank(marketingActivityId)) {
            log.warn("SpreadWorkServiceImpl.getMarketingActivityId failed");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        GetMarketingActivityIdResult result = new GetMarketingActivityIdResult();
        result.setMarketingActivityId(marketingActivityId);
        return new Result(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<CreateWXQRCodeByFeedResult> createWXQRCodeByFeed(String ea, Integer userId, ObjectTypeEnum objectTypeEnum, String objectId, String feedKey, String marketingActivityId, boolean fromKis, String value, Boolean needSpreadUser) {
        if (EmptyUtil.isNullForList(feedKey, objectTypeEnum, objectId, ea, userId)) {
            log.warn("SpreadWorkServiceImpl.createWXQRCodeByFeed param error");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
        if (fromKis && StringUtils.isBlank(uid)) {
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }

        String feedId = null;
        if (fromKis) {
            feedId = spreadWorkManager.addFeed(uid, objectTypeEnum, objectId, feedKey, false);
            if (StringUtils.isBlank(feedId)) {
                log.warn("SpreadWorkServiceImpl.createWXQRCodeByFeed addFeed failed");
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
        }

        QRCodeManager.CreateQRCodeResult createQRCodeResult = spreadWorkManager.createWXQRCodeByFeed(ea, userId, uid, objectTypeEnum, objectId, feedId, feedKey, marketingActivityId, value, needSpreadUser);
        if (null == createQRCodeResult || StringUtils.isBlank(createQRCodeResult.getQrCodeApath())) {
            log.warn("SpreadWorkServiceImpl.createWXQRCodeByFeed createWXQRCodeByFeed failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        CreateWXQRCodeByFeedResult result = new CreateWXQRCodeByFeedResult();
        result.setQrCodeId(createQRCodeResult.getQrCodeId());
        result.setQrCodeUrl(createQRCodeResult.getQrCodeUrl());
        result.setQrCodeAPath(createQRCodeResult.getQrCodeApath());
        return new Result(SHErrorCode.SUCCESS, result);
    }

}
