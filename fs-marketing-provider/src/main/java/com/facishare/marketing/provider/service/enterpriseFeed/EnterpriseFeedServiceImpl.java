package com.facishare.marketing.provider.service.enterpriseFeed;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.outService.arg.card.QueryCardInfoListArg;
import com.facishare.mankeep.api.outService.arg.enterpriseFeed.CheckAndSyncFeedByEaArg;
import com.facishare.mankeep.api.outService.result.card.QueryCardInfoListResult;
import com.facishare.mankeep.api.outService.result.card.QueryCardInfoResult;
import com.facishare.mankeep.api.outService.service.OutCardService;
import com.facishare.mankeep.api.outService.service.OutEnterpriseFeedService;
import com.facishare.mankeep.common.enums.PhotoTargetTypeEnum;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.mankeep.common.util.RelativeDateFormat;
import com.facishare.marketing.api.arg.AddEnterpriseFeedArg;
import com.facishare.marketing.api.arg.QueryEnterpriseFeedArg;
import com.facishare.marketing.api.arg.QueryEnterpriseFeedCommentArg;
import com.facishare.marketing.api.result.EnterpriseFeedCommentResult;
import com.facishare.marketing.api.result.EnterpriseFeedResult;
import com.facishare.marketing.api.result.EnterpriseInfoResult;
import com.facishare.marketing.api.service.enterpriseFeed.EnterpriseFeedService;
import com.facishare.marketing.api.vo.EnterpriseFeedVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.enterpriseFeed.EnterpriseFeedDao;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.ArticleEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.entity.enterpriseFeed.EnterpriseFeedCommentEntity;
import com.facishare.marketing.provider.entity.enterpriseFeed.EnterpriseFeedEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.SettingManager;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.facishare.marketing.provider.util.Constant.*;

/**
 * Created by ranluch on 2019/1/15.
 */
@Service("enterpriseFeedService")
@Slf4j
public class EnterpriseFeedServiceImpl implements EnterpriseFeedService {
    @Autowired
    private EnterpriseFeedDao enterpriseFeedDao;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private OutEnterpriseFeedService outEnterpriseFeedService;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private OutCardService outCardService;

    @Override
    public Result<PageResult<EnterpriseFeedResult>> queryEnterpriseFeedResult(QueryEnterpriseFeedArg vo) {
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<EnterpriseFeedEntity> enterpriseFeedEntities = enterpriseFeedDao.queryEnterpriseFeedResult(vo.getEa(), page);
        List<EnterpriseFeedResult> enterpriseFeedResults = new ArrayList<>();

        final String companyAvatar;
        final String companyName;
        Result<EnterpriseInfoResult> enterpriseInfoResult = settingManager.queryEnterpriseInfo(vo.getEa(), vo.getUserId());
        if (enterpriseInfoResult.isSuccess()) {
            companyAvatar = enterpriseInfoResult.getData().getThumbnailUrl();
            companyName = enterpriseInfoResult.getData().getShortName();
        } else {
            companyAvatar = null;
            companyName = null;
        }

        enterpriseFeedEntities.forEach(value -> {
            EnterpriseFeedResult enterpriseFeedResult = BeanUtil.copy(value, EnterpriseFeedResult.class);
            enterpriseFeedResult.setCompanyName(companyName);
            enterpriseFeedResult.setCompanyAvatar(companyAvatar);
            enterpriseFeedResult.setCommentCount(enterpriseFeedDao.countEnterpriseCommentFeedByFeedId(value.getId()));
            enterpriseFeedResult.setCreateTimeStamp(null == value.getCreateTime() ? null : value.getCreateTime().getTime());
            enterpriseFeedResult.setCreateTimeStr(null == value.getCreateTime() ? null : RelativeDateFormat.format(value.getCreateTime()));
            enterpriseFeedResult.setRecommendation(UnicodeFormatter.decodeUnicodeString(value.getRecommendation()));

            EnterpriseFeedVO enterpriseFeedVO = new EnterpriseFeedVO();
            if (value.getObjectType() == ObjectTypeEnum.IMAGE.getType()) {
                List<PhotoEntity> photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.FEED_DETAIL.getType(), value.getObjectId());
                if (CollectionUtils.isNotEmpty(photoEntities)) {
                    List<String> photos = new ArrayList<>();
                    List<String> thumbnailUrls = new ArrayList<>();
                    for (PhotoEntity photoEntity : photoEntities) {
                        photos.add(photoEntity.getUrl());
                        thumbnailUrls.add(photoEntity.getThumbnailUrl());
                    }
                    enterpriseFeedVO.setPhotos(photos);
                    enterpriseFeedVO.setThumbnailUrls(thumbnailUrls);
                }
            } else if (value.getObjectType() == ObjectTypeEnum.ARTICLE.getType()) {
                ArticleEntity articleEntity = articleDAO.queryArticleDetail(value.getObjectId());
                if (null != articleEntity) {
                    photoManager.queryArticlePhotoUrl(articleEntity);
                    enterpriseFeedVO.setName(articleEntity.getTitle());
                    enterpriseFeedVO.setPhotoUrl(articleEntity.getPhotoUrl());
                    enterpriseFeedVO.setSummary(articleEntity.getSummary());
                    enterpriseFeedVO.setUrl(articleEntity.getUrl());
                }
            } else if (value.getObjectType() == ObjectTypeEnum.PRODUCT.getType()) {
                ProductEntity productEntity = productDAO.queryProductDetail(value.getObjectId());
                if (null != productEntity) {
                    enterpriseFeedVO.setName(productEntity.getName());
                    enterpriseFeedVO.setPrice(productEntity.getPrice());
                    enterpriseFeedVO.setDiscountPrice(productEntity.getDiscountPrice());
                    List<PhotoEntity> photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), value.getObjectId());
                    if (CollectionUtils.isNotEmpty(photoEntities)) {
                        enterpriseFeedVO.setPhotoUrl(photoEntities.get(0).getUrl());
                    }

                    enterpriseFeedVO.setSummary(productEntity.getSummary());
                }
            } else if (value.getObjectType() == ObjectTypeEnum.ACTIVITY.getType()) {
                ActivityEntity activityEntity = activityDAO.getById(value.getObjectId());
                if (null != activityEntity) {
                    photoManager.queryActivityPhotoUrl(activityEntity);
                    enterpriseFeedVO.setName(activityEntity.getTitle());
                    enterpriseFeedVO.setPhotoUrl(activityEntity.getCoverImageUrl());
                    enterpriseFeedVO.setStartTimestamp(activityEntity.getStartTime().getTime());
                    enterpriseFeedVO.setEndTimestamp(activityEntity.getEndTime().getTime());
                    enterpriseFeedVO.setLocation(activityEntity.getLocation());
                }
            }

            enterpriseFeedResult.setEnterpriseFeedVO(enterpriseFeedVO);

            enterpriseFeedResults.add(enterpriseFeedResult);
        });

        PageResult<EnterpriseFeedResult> pageResult = new PageResult<>();
        pageResult.setResult(enterpriseFeedResults);
        pageResult.setPageNum(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotalCount(page.getTotalNum());
        return new Result(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result deleteEnterpriseFeed(String id, String ea) {
        enterpriseFeedDao.updateEnterpriseFeed(id);
        //同步到企业下员工的个人feed
        CheckAndSyncFeedByEaArg arg = new CheckAndSyncFeedByEaArg();
        arg.setEa(ea);
        arg.setEnterpriseFeedIds(Lists.newArrayList(id));
        outEnterpriseFeedService.checkAndSyncFeedByEa(arg);
        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<PageResult<EnterpriseFeedCommentResult>> queryEnterpriseFeedCommentResult(QueryEnterpriseFeedCommentArg vo) {
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<EnterpriseFeedCommentEntity> enterpriseFeedCommentEntities = enterpriseFeedDao.queryEnterpriseFeedCommentResult(vo.getId(), page);
        List<EnterpriseFeedCommentResult> enterpriseFeedCommentResults = new ArrayList<>();

        Map<String,QueryCardInfoResult> cardInfoMap = Maps.newHashMap();
        List<String> uids = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(enterpriseFeedCommentEntities)) {
            enterpriseFeedCommentEntities.forEach(enterpriseFeedCommentEntity -> {
                uids.add(enterpriseFeedCommentEntity.getUid());
            });
            QueryCardInfoListArg arg = new QueryCardInfoListArg();
            arg.setUids(uids);
            ModelResult<QueryCardInfoListResult> cardInfoListResult = outCardService.queryCardInfoList(arg);
            List<QueryCardInfoResult> cardInfoList = cardInfoListResult.getData().getCardInfoResults();

            if (CollectionUtils.isNotEmpty(cardInfoList)) {
                cardInfoList.stream().forEach(cardInfo -> {
                    cardInfoMap.put(cardInfo.getUid(), cardInfo);
                });
            }
        }

        enterpriseFeedCommentEntities.forEach(value -> {
            EnterpriseFeedCommentResult enterpriseFeedCommentResult = BeanUtil.copy(value, EnterpriseFeedCommentResult.class);
            QueryCardInfoResult cardInfoResult = cardInfoMap.get(value.getUid());
            if (cardInfoResult != null) {
                enterpriseFeedCommentResult.setName(cardInfoResult.getName());
                enterpriseFeedCommentResult.setAvatar(cardInfoResult.getAvatar());
            }
            enterpriseFeedCommentResults.add(enterpriseFeedCommentResult);
        });

        PageResult<EnterpriseFeedCommentResult> pageResult = new PageResult<>();
        pageResult.setResult(enterpriseFeedCommentResults);
        pageResult.setPageNum(page.getPageNo());
        pageResult.setPageSize(page.getPageSize());
        pageResult.setTotalCount(page.getTotalNum());
        return new Result(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result deleteEnterpriseCommentFeed(String id) {
        enterpriseFeedDao.updateEnterpriseCommentFeed(id);
        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result addEnterpriseFeed(AddEnterpriseFeedArg vo) {
        if (vo.isParamError()) {
            log.warn("EnterpriseFeedServiceImpl.addEnterpriseFeed params error, vo:{}", vo);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        String id = UUIDUtil.getUUID();
        String objectId = vo.getObjectId();
        if (vo.getObjectType() == ObjectTypeEnum.IMAGE.getType() && CollectionUtils.isNotEmpty(vo.getPhotos())) {
            objectId = UUIDUtil.getUUID();//图片需要新生成一个物料id
            List<PhotoEntity> photoEntityList = Lists.newArrayList();
            int index = 0;
            List<PhotoEntity> syncHandlePhotoList = Lists.newArrayList();
            for (String taPath : vo.getPhotos()) {
                if (taPath.startsWith(TEMP_A_WAREHOUSE_TYPE)) {
                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(taPath, null, null);
                    if (null == fileManagerPicResult) {
                        log.warn("EnterpriseFeedServiceImpl.addEnterpriseFeed.fileManagerPicResult is null");
                        return new Result(SHErrorCode.UNKNOWN);
                    }

                    // 新增图片
                    PhotoEntity photoEntity = new PhotoEntity();
                    photoEntity.setId(UUIDUtil.getUUID());
                    photoEntity.setPath(fileManagerPicResult.getUrlAPath());
                    photoEntity.setThumbnailPath(fileManagerPicResult.getThumbUrlApath());
                    photoEntity.setUrl(fileManagerPicResult.getUrl());
                    photoEntity.setThumbnailUrl(fileManagerPicResult.getThumbUrl());
                    photoEntity.setTargetId(objectId);
                    photoEntity.setTargetType(PhotoTargetTypeEnum.FEED_DETAIL.getType());
                    photoEntity.setSeqNum(index);
                    index = index + 1;
                    photoEntityList.add(photoEntity);
                }
                if (taPath.startsWith(A_WAREHOUSE_TYPE)){
                    String url = fileV2Manager.getUrlByPath(taPath, null, false);
                    if (StringUtils.isNotEmpty(url)){
                        PhotoEntity photoEntity = new PhotoEntity();
                        photoEntity.setId(UUIDUtil.getUUID());
                        photoEntity.setPath(taPath);
                        photoEntity.setThumbnailPath(taPath);
                        photoEntity.setUrl(url);
                        photoEntity.setThumbnailUrl(url);
                        photoEntity.setTargetId(objectId);
                        photoEntity.setTargetType(PhotoTargetTypeEnum.FEED_DETAIL.getType());
                        photoEntity.setSeqNum(index);
                        index = index + 1;
                        photoEntityList.add(photoEntity);
                        syncHandlePhotoList.add(photoEntity);
                    }
                }
                if (taPath.startsWith(C_WAREHOUSE_TYPE)){
                    String url = fileV2Manager.getUrlByPath(taPath, vo.getEa(), false);
                    if (StringUtils.isNotEmpty(url)){
                        PhotoEntity photoEntity = new PhotoEntity();
                        photoEntity.setId(UUIDUtil.getUUID());
                        photoEntity.setPath(taPath);
                        photoEntity.setThumbnailPath(taPath);
                        photoEntity.setUrl(url);
                        photoEntity.setThumbnailUrl(url);
                        photoEntity.setTargetId(objectId);
                        photoEntity.setTargetType(PhotoTargetTypeEnum.FEED_DETAIL.getType());
                        photoEntity.setSeqNum(index);
                        index = index + 1;
                        photoEntityList.add(photoEntity);
                        syncHandlePhotoList.add(photoEntity);
                    }
                }
            }
            photoDAO.batchInsert(photoEntityList);
            ThreadPoolUtils.execute(() -> photoManager.doSyncCreatePhotoUrl(vo.getEa(), syncHandlePhotoList), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }

        //新增企业动态
        EnterpriseFeedEntity enterpriseFeedEntity = new EnterpriseFeedEntity();
        enterpriseFeedEntity.setId(id);
        enterpriseFeedEntity.setEa(vo.getEa());
        enterpriseFeedEntity.setObjectId(objectId);
        enterpriseFeedEntity.setObjectType(vo.getObjectType());
        enterpriseFeedEntity.setRecommendation(vo.getRecommendation());
        enterpriseFeedEntity.setUserId(vo.getUserId());
        enterpriseFeedDao.addEnterpriseFeed(enterpriseFeedEntity);

        //同步到企业下员工的个人feed
        try {
            CheckAndSyncFeedByEaArg arg = new CheckAndSyncFeedByEaArg();
            arg.setEa(vo.getEa());
            arg.setEnterpriseFeedIds(Lists.newArrayList(id));
            outEnterpriseFeedService.checkAndSyncFeedByEa(arg);
        } catch (Exception e) {
            log.warn("EnterpriseFeedServiceImpl.addEnterpriseFeed checkAndSyncFeedByEa error e:{}", e);
        }
        return new Result(SHErrorCode.SUCCESS);
    }
}
