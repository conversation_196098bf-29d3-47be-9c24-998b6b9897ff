package com.facishare.marketing.provider.dao.marketingplugin;

import com.facishare.marketing.api.result.FieldMappingResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface MarketingPluginConfigDAO {

    @Insert("INSERT INTO marketing_plugin_config ("
            + "        \"id\",\n"
            + "        \"ea\",\n"
            + "        \"plugin_type\",\n"
            + "        \"plugin_name\",\n"
            + "        \"status\",\n"
            + "        \"create_time\",\n"
            + "        \"update_time\"\n"
            + "        )\n"
            + "        VALUES (\n"
            + "        #{obj.id},\n"
            + "        #{obj.ea},\n"
            + "        #{obj.pluginType},\n"
            + "        #{obj.pluginName},\n"
            + "        #{obj.status},\n"
            + "        now(),\n"
            + "        now()\n"
            + "        ) ON CONFLICT DO NOTHING;")
    int saveMarketingPluginConfig(@Param("obj") MarketingPluginConfigEntity marketingPluginConfigEntity);


    @Select("<script> SELECT * FROM marketing_plugin_config WHERE ea = #{ea} and plugin_type = #{pluginType} </script>")
    @FilterLog
    MarketingPluginConfigEntity queryMarketingPlugin(@Param("ea") String ea,@Param("pluginType") Integer pluginType);

    @Select("<script> " +
            "SELECT * FROM marketing_plugin_config " +
            "WHERE ea = #{ea} and plugin_type in " +
                "<foreach collection = 'pluginTypes' item = 'item' open = '(' separator = ',' close = ')'> " +
                    "#{item}" +
                "</foreach>" +
            "</script>")
    List<MarketingPluginConfigEntity> queryMarketingPluginByTypes(@Param("ea") String ea, @Param("pluginTypes") List<Integer> pluginTypes);

    @Update("<script> UPDATE  marketing_plugin_config SET status = #{status},update_time = now()  WHERE ea = #{ea} and plugin_type = #{pluginType} </script>")
    int updatePluginStatus(@Param("ea") String ea,@Param("pluginType") Integer pluginType,@Param("status") Boolean status);


    @Select("<script> SELECT * FROM marketing_plugin_config WHERE ea = #{ea} </script>")
    List<MarketingPluginConfigEntity> queryMarketingPluginByEa(@Param("ea") String ea);

    @Update("<script>UPDATE marketing_plugin_config SET crm_form_field_map = #{crmFormFieldMap, typeHandler=FieldMappingsTypeHandler}, " +
            "crm_api_name=#{crmApiName}, " +
            "crm_pool_id = #{crmPoolId}, " +
            "crm_record_type = #{crmRecordType}, " +
            "update_time = now()  " +
            "WHERE ea = #{ea} and plugin_type = #{pluginType}" +
            "</script>")
    int updateClueFieldMappingByEaAndType(@Param("ea") String ea, @Param("pluginType") int pluginType, @Param("crmFormFieldMap") FieldMappings fieldMappings, @Param("crmApiName") String crmApiName, @Param("crmPoolId") String crmPoolId, @Param("crmRecordType") String crmRecordType);

    @Select("<script> SELECT * FROM marketing_plugin_config WHERE ea = #{ea} and plugin_type = #{pluginType} and crm_api_name=#{crmApiName} </script>")
    MarketingPluginConfigEntity queryMarketingPluginFieldMap(@Param("ea") String ea,@Param("pluginType") Integer pluginType, @Param("crmApiName") String crmApiName);

    @Select("<script> SELECT distinct ea FROM marketing_plugin_config WHERE plugin_type = #{pluginType} </script>")
    @FilterLog
    List<String> queryEaByPluginType(@Param("pluginType") Integer pluginType);
}
