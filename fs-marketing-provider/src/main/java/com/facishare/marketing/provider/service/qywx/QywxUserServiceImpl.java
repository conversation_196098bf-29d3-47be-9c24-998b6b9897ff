package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.api.result.qywx.miniapp.CustomerAppInfoResult;
import com.facishare.marketing.api.result.qywx.miniapp.GetPhoneNumberByCodeResult;
import com.facishare.marketing.api.result.qywx.miniapp.SelfAppInfoResult;
import com.facishare.marketing.api.service.qywx.QywxUserService;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.manager.qywx.QywxAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2020/04/07
 **/
@Slf4j
@Service("qywxUserService")
public class QywxUserServiceImpl implements QywxUserService {

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;

    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;

    @Override
    public Result<Integer> getUserIdByWxInfo(String ea, String corpId, String qyUserId) {
        Integer userId = qywxUserManager.getUserIdByQyWxInfo(ea, corpId, qyUserId, QywxUserConstants.TRY_TIME);
        return Result.newSuccess(userId);
    }

    @Override
    public Result<String> getFsEaByQyWxCorpId(String corpId, String appId) {
        Optional<String> eaOptional = qywxManager.getFsEaByQyWxCorpId(corpId, appId);
        return Result.newSuccess(eaOptional.orElse(null));
    }

    @Override
    public Result<GetPhoneNumberByCodeResult> queryPhoneNumberByCode(String wxAppId, String code) {
        return qywxManager.queryPhoneNumberByCode(wxAppId, code);
    }

    @Override
    public Result<CustomerAppInfoResult> queryCorpIdAndSuitIdByEa(String ea) {
        return qywxManager.getCorpIdAndSuitId(ea);
    }

    @Override
    public Result<CustomerAppInfoResult> queryAgentIdByCorpIdAndSuitId(String corpId, String suitId) {
        return qywxManager.queryAgentIdByCorpIdAndSuitId(corpId,suitId);
    }

    @Override
    public Result<SelfAppInfoResult> queryAgentIdByEa4Self(String ea) {
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity != null) {
            SelfAppInfoResult selfAppInfoResult = new SelfAppInfoResult();
            selfAppInfoResult.setEa(qywxCorpAgentConfigEntity.getEa());
            selfAppInfoResult.setCorpId(qywxCorpAgentConfigEntity.getCorpid());
            selfAppInfoResult.setAgentId(qywxCorpAgentConfigEntity.getAgentid());
            return Result.newSuccess(selfAppInfoResult);
        }
        return Result.newSuccess();
    }

    @Override
    public Result updateQywxObjectOwnerAsBindFsUser(String ea) {
        if(StringUtils.isEmpty(ea)){
            return Result.newSuccess();
        }

        List<QyWxAddressBookEntity> addressBookEntityList = qywxAddressBookManager.queryByEa(ea);
        if (CollectionUtils.isEmpty(addressBookEntityList)){
            return Result.newSuccess();
        }

        for (QyWxAddressBookEntity addressBookEntity : addressBookEntityList){
            try {
                Integer fsUserId = qywxUserManager.getFsUserIdByQyWxInfo(ea, addressBookEntity.getUserId(), false, false);
                if (fsUserId != null) {
                    qywxUserManager.updateQywxObjectOwner(ea, addressBookEntity.getUserId(), fsUserId);
                }
            }catch (Exception e){
                log.info("updateQywxObjectOwnerAsBindFsUser failed ea:{} e:", ea, e);
            }
        }
        return Result.newSuccess();
    }
}
