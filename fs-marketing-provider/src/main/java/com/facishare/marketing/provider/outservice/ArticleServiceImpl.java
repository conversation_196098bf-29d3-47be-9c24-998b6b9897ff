package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.outapi.result.BriefArticleMsg;
import com.facishare.marketing.outapi.service.ArticleService;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.entity.ArticleEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("articleOutService")
public class ArticleServiceImpl implements ArticleService {
    @Autowired
    private ArticleDAO articleDAO;

    @Override
    public Result<BriefArticleMsg> getBriefMsgById(String ea, String articleId) {
        ArticleEntity articleEntity = articleDAO.getById(articleId);
        if(articleEntity != null){
            BriefArticleMsg briefArticleMsg = new BriefArticleMsg();
            briefArticleMsg.setId(articleId);
            briefArticleMsg.setName(articleEntity.getTitle());
            briefArticleMsg.setLink(articleEntity.getUrl());
            return Result.newSuccess(briefArticleMsg);
        }
        return Result.newSuccess(null);
    }
}
