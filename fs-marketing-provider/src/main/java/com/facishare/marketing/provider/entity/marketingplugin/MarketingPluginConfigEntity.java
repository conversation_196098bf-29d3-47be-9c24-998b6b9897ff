package com.facishare.marketing.provider.entity.marketingplugin;

import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingPluginConfigEntity implements Serializable {

    private String id;

    private String ea;    //企业

    private Integer pluginType;  //营销插件类型  1:溯源优惠券 2:社会化分销

    private String pluginName; // 营销插件名称

    private Boolean status;  // 营销插件状态 true :  开启   false : 关闭

    private Date createTime; // 创建时间

    private Date updateTime; // 更新时间

    private FieldMappings crmFormFieldMap; // 线索映射

    private String crmApiName; // crm对象名称

    private String crmPoolId; // 线索池

    private String crmRecordType; // 业务类型
}
