package com.facishare.marketing.provider.manager.user;

import com.facishare.marketing.provider.dao.UserDAO;
import com.facishare.marketing.provider.entity.UserEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserManager {

    @Autowired
    private UserDAO userDAO;

    @Autowired
    private UserRelationManager userRelationManager;

    public UserEntity queryByUid(String uid) {
        return userDAO.queryByUid(uid);
    }

    public List<UserEntity> listByUids(List<String> uids) {
        return userDAO.listByUids(uids);
    }

    public UserEntity queryByOpenidAndAppid(String openid, String appid) {
        return userDAO.queryByOpenidAndAppid(openid, appid);
    }

    public UserEntity queryUserByOpenIdAppIdCorpId(String openid, String appid, String corpId) {
        return userDAO.queryUserByOpenIdAppIdCorpId(openid, appid, corpId);
    }

    public UserEntity queryByCorpIdAndQYUserIdAndAppid(String corpid, String qyUserId, String appid) {
        return userDAO.queryByCorpIdAndQYUserIdAndAppid(corpid, qyUserId, appid);
    }

    public List<UserEntity> queryByEaAndQyUserIds(String ea, List<String> qyUserIds) {
        return userDAO.queryByEaAndQyUserIds(ea, qyUserIds);
    }

    public List<UserEntity> queryByQYUserIdAndAppid(String qyUserId, String appid) {
        return userDAO.queryByQYUserIdAndAppid(qyUserId, appid);
    }

    public int insert(UserEntity userEntity) {
        int result = userDAO.insert(userEntity);
        userRelationManager.handleUserChange(userEntity.getUid());
        return result;
   }
    public int updateUser(UserEntity userEntity) {
        int result = userDAO.updateUser(userEntity);
        userRelationManager.handleUserChange(userEntity.getUid());
        return result;
    }
    public int addQywxUserInfo(String appId, String corpid, String qyUserId, String uid) {
        int result = userDAO.addQywxUserInfo(appId, corpid, qyUserId, uid);
        userRelationManager.handleUserChange(uid);
        return result;
     }

    public int updateUserWxInfo(String uid, String openId, String wxUnionId) {
        int result = userDAO.updateUserWxInfo(uid, openId, wxUnionId);
        userRelationManager.handleUserChange(uid);
        return result;
    }

    public int addDingUserInfo(String appId, String corpid, String dingUserId, String uid) {
        int result = userDAO.addDingUserInfo(appId, corpid, dingUserId, uid);
        userRelationManager.handleUserChange(uid);
        return result;
    }

    public int updateUserOpenId(String openid, String uid) {
        int result = userDAO.updateUserOpenId(openid, uid);
        userRelationManager.handleUserChange(uid);
        return result;
    }

    public int updateUserOpenIdAndWxUnionId(String openid, String uid, String wxUnionId) {
        int result = userDAO.updateUserOpenIdAndWxUnionId(openid, uid, wxUnionId);
        userRelationManager.handleUserChange(uid);
        return result;
    }
    public int updateUserAppId( String openid, String uid, String wxUnionId, String appId) {
        int result = userDAO.updateUserAppId(openid, uid, wxUnionId, appId);
        userRelationManager.handleUserChange(uid);
        return result;
    }

    public int updateUserUnionId(String wxUnionId, String uid) {
        int result = userDAO.updateUserUnionId(wxUnionId, uid);
        userRelationManager.handleUserChange(uid);
        return result;
    }

    public UserEntity queryByCorpIdAndDingUserIdAndAppid(String corpid, String userId) {
        return userDAO.queryByCorpIdAndDingUserIdAndAppid(corpid, userId);
    }

    public UserEntity queryByCorpIdAndDingUserId(String corpid, String userId) {
        return userDAO.queryByCorpIdAndDingUserId(corpid, userId);
    }

    public int queryUserNumByCorpId(String corpid, String appid) {
        return userDAO.queryUserNumByCorpId(corpid, appid);
    }

    public void deleteOldUserByUid(String uid) {
        userDAO.deleteOldUserByUid(uid);
        userRelationManager.handleUserChange(uid);
    }

    public void deleteOldUserByUidIgnoreUserRelation(String uid) {
        userDAO.deleteOldUserByUid(uid);
    }

    public void updateUserOpenIdAndAppId(String appid, String openid, String wxUnionId, String uid) {
        userDAO.updateUserOpenIdAndAppId(appid, openid, wxUnionId, uid);
        userRelationManager.handleUserChange(uid);
    }

    public void updateUserName(String name, String uid) {
        userDAO.updateUserName(name, uid);
    }

    public List<UserEntity> queryUsersByCorpId(String corpid) {
        return userDAO.queryUsersByCorpId(corpid);
    }

    public UserEntity queryByCorpIdAndUnionId(String corpid, String wxUnionId) {
        return userDAO.queryByCorpIdAndUnionId(corpid, wxUnionId);
    }

    public UserEntity queryByUnionId(String wxUnionId) {
        return userDAO.queryByUnionId(wxUnionId);
    }

    public UserEntity queryByOpenIdAndExternalUserIdAndAppId(String openId, String externalUserId, String appid) {
        return userDAO.queryByOpenIdAndExternalUserIdAndAppId(openId, externalUserId, appid);
    }

    public List<UserEntity> queryUidByQyUserIdList(List<String> qyUserIdList) {
        return userDAO.queryUidByQyUserIdList(qyUserIdList);
    }

    public UserEntity queryByOpenId(String openId) {
        return userDAO.queryByOpenId(openId);
    }

    public List<UserEntity> queryAllByOpenId(String openId) {
        return userDAO.queryAllByOpenId(openId);
    }

    public List<String> queryByWxAppId(String appid) {
        return userDAO.queryByWxAppId(appid);
    }

    public List<UserEntity> queryByExternalUserId(String ea, String externalUserId) {
        return userDAO.queryByExternalUserId(ea, externalUserId);
    }

    public int updateExternalUserId(String uid, String externalUserId) {
        return userDAO.updateExternalUserId(uid, externalUserId);
    }

    public List<UserEntity> queryByEaAndQyUserId(String ea, String qyUserId) {
        return userDAO.queryByEaAndQyUserId(ea, qyUserId);
    }

    public int updateQyUserId(String uid, String qyUserId) {
        int result = userDAO.updateQyUserId(uid, qyUserId);
        userRelationManager.handleUserChange(uid);
        return result;
    }

    public int updateCorpId(String uid, String newCorpId) {
        return userDAO.updateCorpId(uid, newCorpId);
    }

}
