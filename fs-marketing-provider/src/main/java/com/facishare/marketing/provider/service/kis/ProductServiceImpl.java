package com.facishare.marketing.provider.service.kis;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.marketing.api.arg.ListProductArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.api.result.ProductListResult;
import com.facishare.marketing.api.result.QueryProductDetailResult;
import com.facishare.marketing.api.result.cta.CtaRelationInfo;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.kis.ProductService;
import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.BindObjectType;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.ProductArticleTypeEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.ImageInfo;
import com.facishare.marketing.common.util.ImageInfoHelper;
import com.facishare.marketing.provider.dao.ObjectDescriptionDAO;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.param.product.ProductQueryParam;
import com.facishare.marketing.provider.dto.ProductEntityDTO;
import com.facishare.marketing.provider.dto.cta.CtaRelationEntityDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.MaterialTagManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.kis.KisPermissionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("kisProductService")
@Slf4j
public class ProductServiceImpl implements ProductService {
    @Autowired
    private ProductDAO productDAO;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private ObjectDescriptionDAO objectDescriptionDAO;

    @Autowired
    private MarketingActivityManager marketingActivityManager;

    @Autowired
    private MaterialTagManager materialTagManager;

    @Autowired
    private KisPermissionManager kisPermissionManager;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;

    @Autowired
    private PhotoDAO photoDAO;

    @Autowired
    @Resource(name = "productService")
    private com.facishare.marketing.api.service.ProductService webProductService;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private SpreadTaskService spreadTaskService;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Override
    public Result<ProductListResult> listProducts(String ea, Integer fsUserId, Integer ei, ListProductArg vo) {
        Integer status = vo.getStatus();
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        //List<ProductEntityDTO> productEntities = productDAO.listByFsEa(ea, null, ProductArticleTypeEnum.CORPORATE.getType(), status, vo.getTitle(), page);
        if (org.apache.commons.lang3.StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }

        ProductListResult productListResult = new ProductListResult();
        productListResult.setTime(vo.getTime());

        List<ProductEntityDTO> productEntities;
        ProductQueryParam queryParam = new ProductQueryParam();
        queryParam.setEa(ea);
        queryParam.setTitle(vo.getTitle());
        queryParam.setStatus(vo.getStatus());
        queryParam.setType(ProductArticleTypeEnum.CORPORATE.getType());
        queryParam.setUid(null);
        queryParam.setMaterialTagFilter(vo.getMaterialTagFilter());
        // 获取菜单的设置详情
        Result<AppMenuTagVO> appMenuTagVOResult = appMenuTemplateService.getMenuTagRule(ea, vo.getMenuId(), ObjectTypeEnum.PRODUCT.getType());
        AppMenuTagVO appMenuTagVO = appMenuTagVOResult.isSuccess() ? appMenuTagVOResult.getData() : null;

        if (appMenuTagVO != null) {
            MaterialTagFilterArg materialTagFilterArg = queryParam.getMaterialTagFilter();
            if (materialTagFilterArg == null) {
                materialTagFilterArg = new MaterialTagFilterArg();
            }
            materialTagFilterArg.setMenuType(appMenuTagVO.getTagOperator());
            materialTagFilterArg.setMenuMaterialTagIds(appMenuTagVO.getTagIdList());
            queryParam.setStrictCheckGroup(true);
            queryParam.setMaterialTagFilter(materialTagFilterArg);
            productEntities = productDAO.getAccessiblePage(queryParam, page);
        } else if (org.apache.commons.lang3.StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            // 是否需要严格校验分组
            queryParam.setStrictCheckGroup(appMenuTemplateService.needStrictCheckGroup(ea, fsUserId, vo.getMenuId()));
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), vo.getMenuId());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            queryParam.setPermissionGroupIdList(permissionGroupIdList);
            productEntities = productDAO.getAccessiblePage(queryParam, page);
        } else if(org.apache.commons.lang3.StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            queryParam.setUserId(fsUserId);
            productEntities = productDAO.getCreateByMePage(queryParam, page);
        } else if (org.apache.commons.lang3.StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            productEntities = productDAO.getUnGroupPage(queryParam, page);
        } else {
            queryParam.setStrictCheckGroup(true);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.PRODUCT.getType(), vo.getMenuId());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            if (!permissionGroupIdSet.contains(vo.getGroupId())){
                productEntities = Lists.newArrayList();
            } else {
                queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                queryParam.setUserId(fsUserId);
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.PRODUCT.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                productEntities = productDAO.getAccessiblePage(queryParam, page);
            }
        }
        int totalCount = page.getTotalNum();
        productListResult.setTotalCount(totalCount);

        String appAdminName = kisPermissionManager.lastestAppAdminName(ea, ei);
        productListResult.setAppAdminName(appAdminName);

        if (CollectionUtils.isEmpty(productEntities)) {
            productListResult.setProductDetailResultList(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, productListResult);
        }

        List<String> productIds = Lists.newArrayList();
        productEntities.forEach(productEntity -> productIds.add(productEntity.getId()));

        // photo and product mapping
        Map<String, List<String>> headPicsMap = new HashMap<>();
        Map<String, List<String>> detailPicsMap = new HashMap<>();
        Map<String, List<String>> headPicsThumbMap = new HashMap<>();
        Map<String, List<String>> detailPicsThumbMap = new HashMap<>();
        List<PhotoEntity> photoEntities = photoManager.listByProductIds(productIds,ea);


        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoEntities.forEach(photoEntity -> {
                String productId = photoEntity.getTargetId();
                int targetType = photoEntity.getTargetType();
                String url = photoEntity.getUrl();
                String thumbUrl = photoEntity.getThumbnailUrl();
                if (targetType == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
                    if (headPicsMap.containsKey(productId)) {
                        headPicsMap.get(productId).add(url);
                        headPicsThumbMap.get(productId).add(thumbUrl);
                    } else {
                        headPicsMap.put(productId, Lists.newArrayList(url));
                        headPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
                    }
                } else if (targetType == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                    if (detailPicsMap.containsKey(productId)) {
                        detailPicsMap.get(productId).add(url);
                        detailPicsThumbMap.get(productId).add(thumbUrl);
                    } else {
                        detailPicsMap.put(productId, Lists.newArrayList(url));
                        detailPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
                    }
                }
            });
        }

        // build result
        Map<String, Integer> materielMap = Maps.newHashMap();
        productEntities.forEach(value -> materielMap.put(value.getId(), ObjectTypeEnum.PRODUCT.getType()));

        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>>  marketingActivityObjectInfoMap = marketingActivityManager.getActivityIdsByObject(materielMap, ea, fsUserId);

        List<String> objectIds = productEntities.stream().map(ProductEntityDTO::getId).collect(Collectors.toList());
        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(objectIds, ObjectTypeEnum.PRODUCT.getType());
        List<QueryProductDetailResult> productDetailResults = Lists.newArrayList();
        for(int i = 0; i < productEntities.size(); i++) {
            ProductEntityDTO productEntity = productEntities.get(i);
            String productId = productEntity.getId();
            QueryProductDetailResult queryProductDetailResult = BeanUtil.copy(productEntity, QueryProductDetailResult.class);
            queryProductDetailResult.setCreateTime(productEntity.getCreateTime().getTime());
            if (headPicsMap.containsKey(productId)) {
                queryProductDetailResult.setHeadPics(headPicsMap.get(productId));
            }
            if (detailPicsMap.containsKey(productId)) {
                queryProductDetailResult.setDetailPics(detailPicsMap.get(productId));
            }
            if (headPicsThumbMap.containsKey(productId)) {
                queryProductDetailResult.setHeadPicsThumbs(headPicsThumbMap.get(productId));
            }
            if (detailPicsThumbMap.containsKey(productId)) {
                queryProductDetailResult.setDetailPicsThumbs(detailPicsThumbMap.get(productId));
            }
            if (marketingActivityObjectInfoMap != null) {
                List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = marketingActivityObjectInfoMap.get(productId);
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectInfoDTO)) {
                    String id = objectInfoDTO.get(0).getId();
                    Result<Boolean> booleanResult = spreadTaskService.spreadTaskIsRevocation(id);
                    if (booleanResult == null || !booleanResult.getData()) {
                        queryProductDetailResult.setMarketingActivityId(id);
                        queryProductDetailResult.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                        queryProductDetailResult.setMarketingActivityCount(objectInfoDTO.size());
                    }
                }
            }
            queryProductDetailResult.setChoose(productEntity.isChoose());
            queryProductDetailResult.setTryOutEnable(productEntity.getTryOutEnable());
            queryProductDetailResult.setTop(productEntity.isTop());
            if(queryProductDetailResult != null && org.apache.commons.lang3.StringUtils.isNotBlank(queryProductDetailResult.getId())){
                // 获取裁剪封面图
                PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType(), queryProductDetailResult.getId());
                if (coverCutMiniAppPhotoEntity != null) {
                    queryProductDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType(), queryProductDetailResult.getId());
                if (coverCutH5PhotoEntity != null) {
                    queryProductDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType(), queryProductDetailResult.getId());
                if (coverCutOrdinaryPhotoEntity != null) {
                    queryProductDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                    //返回原图
                    queryProductDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                }
            }
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(productEntity.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                queryProductDetailResult.setMaterialTags(collect);
            }
            productDetailResults.add(queryProductDetailResult);
        }

        productListResult.setProductDetailResultList(productDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, productListResult);
    }

    @Override
    public Result<ProductListResult> listProducts4Outer(String upstreamEA, String outTenantId, String outUserId, ListProductArg vo) {
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<ProductEntityDTO> productEntities;
        ProductQueryParam queryParam = new ProductQueryParam();
        queryParam.setEa(upstreamEA);
        queryParam.setTitle(vo.getTitle());
        queryParam.setStatus(vo.getStatus());
        queryParam.setType(ProductArticleTypeEnum.CORPORATE.getType());
        queryParam.setUid(null);

        List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup4Outer(upstreamEA, outTenantId, outUserId, ObjectTypeEnum.PRODUCT.getType());
        Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
        if (Objects.equals(vo.getGroupId(), DefaultObjectGroupEnum.ALL.getId())) {
            // 全部
            queryParam.setGroupIdList(Lists.newArrayList(permissionGroupIdSet));
            productEntities = productDAO.getAccessiblePage4Outer(queryParam, page);
        } else {
            if (!permissionGroupIdSet.contains(vo.getGroupId())){
                productEntities = Lists.newArrayList();
            } else {
                queryParam.setUserId(null);
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(upstreamEA, ObjectTypeEnum.PRODUCT.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                productEntities = productDAO.getAccessiblePage4Outer(queryParam, page);
            }
        }

        int totalCount = page.getTotalNum();
        ProductListResult productListResult = new ProductListResult();
        productListResult.setTotalCount(totalCount);
        productListResult.setTime(vo.getTime());

        if (CollectionUtils.isEmpty(productEntities)) {
            productListResult.setProductDetailResultList(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, productListResult);
        }

        List<String> productIds = Lists.newArrayList();
        productEntities.forEach(productEntity -> productIds.add(productEntity.getId()));

        // photo and product mapping
        Map<String, List<String>> headPicsMap = new HashMap<>();
        Map<String, List<String>> detailPicsMap = new HashMap<>();
        Map<String, List<String>> headPicsThumbMap = new HashMap<>();
        Map<String, List<String>> detailPicsThumbMap = new HashMap<>();
        Map<String, PhotoEntity> shareCoverEntityMap =  new HashMap<>();
        List<PhotoEntity> photoEntities = photoManager.listByProductIds(productIds,upstreamEA);
        shareCoverEntityMap = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType(), Lists.newArrayList(productIds));


        if (CollectionUtils.isNotEmpty(photoEntities)) {
            photoEntities.forEach(photoEntity -> {
                String productId = photoEntity.getTargetId();
                int targetType = photoEntity.getTargetType();
                String url = photoEntity.getUrl();
                String thumbUrl = photoEntity.getThumbnailUrl();
                if (targetType == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
                    if (headPicsMap.containsKey(productId)) {
                        headPicsMap.get(productId).add(url);
                        headPicsThumbMap.get(productId).add(thumbUrl);
                    } else {
                        headPicsMap.put(productId, Lists.newArrayList(url));
                        headPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
                    }
                } else if (targetType == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                    if (detailPicsMap.containsKey(productId)) {
                        detailPicsMap.get(productId).add(url);
                        detailPicsThumbMap.get(productId).add(thumbUrl);
                    } else {
                        detailPicsMap.put(productId, Lists.newArrayList(url));
                        detailPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
                    }
                }
            });
        }

        // build result
        Map<String, Integer> materielMap = Maps.newHashMap();
        productEntities.forEach(value -> materielMap.put(value.getId(), ObjectTypeEnum.PRODUCT.getType()));
        List<QueryProductDetailResult> productDetailResults = Lists.newArrayList();
        for(int i = 0; i < productEntities.size(); i++) {
            ProductEntityDTO productEntity = productEntities.get(i);
            String productId = productEntity.getId();
            QueryProductDetailResult queryProductDetailResult = BeanUtil.copy(productEntity, QueryProductDetailResult.class);
            queryProductDetailResult.setCreateTime(productEntity.getCreateTime().getTime());
            if (headPicsMap.containsKey(productId)) {
                queryProductDetailResult.setHeadPics(headPicsMap.get(productId));
            }
            if (detailPicsMap.containsKey(productId)) {
                queryProductDetailResult.setDetailPics(detailPicsMap.get(productId));
            }
            if (headPicsThumbMap.containsKey(productId)) {
                queryProductDetailResult.setHeadPicsThumbs(headPicsThumbMap.get(productId));
            }
            if (detailPicsThumbMap.containsKey(productId)) {
                queryProductDetailResult.setDetailPicsThumbs(detailPicsThumbMap.get(productId));
            }
            if (shareCoverEntityMap.containsKey(productId)) {
                queryProductDetailResult.setSharePicOrdinaryCutUrl(shareCoverEntityMap.get(productId).getThumbnailUrl());
            }
            queryProductDetailResult.setChoose(productEntity.isChoose());
            queryProductDetailResult.setTryOutEnable(productEntity.getTryOutEnable());
            queryProductDetailResult.setTop(productEntity.isTop());
            productDetailResults.add(queryProductDetailResult);
        }

        productListResult.setProductDetailResultList(productDetailResults);
        return new Result<>(SHErrorCode.SUCCESS, productListResult);
    }

    @Override
    public Result<QueryProductDetailResult> queryProductDetail(String id) {
        ProductEntity productEntity = productDAO.queryProductDetail(id);
        if (productEntity == null) {
            return new Result<>(SHErrorCode.PRODUCT_DETAIL_FAIL);
        }

//        List<PhotoEntity> photoEntityList = photoManager.listProductPhotos(id);
        List<PhotoEntity> photoEntityList = photoDAO.listProductPhotos(id);
        QueryProductDetailResult queryProductDetailResult = new QueryProductDetailResult();
        queryProductDetailResult.setId(productEntity.getId());
        queryProductDetailResult.setType(productEntity.getType());
        queryProductDetailResult.setName(productEntity.getName());
        queryProductDetailResult.setPrice(productEntity.getPrice());
        queryProductDetailResult.setDiscountPrice(productEntity.getDiscountPrice());
        queryProductDetailResult.setSummary(productEntity.getSummary());
        queryProductDetailResult.setVideo(productEntity.getVideo());
        queryProductDetailResult.setTryOutEnable(productEntity.getTryOutEnable());
        queryProductDetailResult.setTryOutFormObjectApiName(productEntity.getTryOutFormObjectApiName());
        queryProductDetailResult.setTryOutButtonValue(productEntity.getTryOutButtonValue());
        queryProductDetailResult.setSubmitCount(productEntity.getSubmitCount() == null ? 0 : productEntity.getSubmitCount());
        queryProductDetailResult.setVideoCoverUrl(productEntity.getVideoCoverUrl());

        List<String> headPicsList = Lists.newArrayList();
        List<String> detailPicsList = Lists.newArrayList();
        List<String> headPicsThumbList = Lists.newArrayList();
        List<String> detailPicsThumbList = Lists.newArrayList();
        List<QueryProductDetailResult.ImageInfo> detailPicInfoList = Lists.newArrayList();
        List<QueryProductDetailResult.ImageInfo> detailPicsThumbsInfoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(photoEntityList)) {
            for (PhotoEntity photoEntity : photoEntityList) {
                if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
               //     queryProductDetailResult.setShareImg(fileV2Manager.zoom(photoEntity.getUrl(), null, 100));
                    headPicsList.add(photoEntity.getUrl());
                    headPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
                }

                if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
                    detailPicsList.add(photoEntity.getUrl());
                    // 计算详情图大小
                    QueryProductDetailResult.ImageInfo detailPicInfo = getImageWidthAndHeight(photoEntity.getPath(), photoEntity.getUrl(), productEntity.getFsEa());
                    if (detailPicInfo != null) {
                        detailPicInfoList.add(detailPicInfo);
                        Long size = detailPicInfo.getSize();
                        // 如果找不到大小，用缩略图，如果原图大小大于等于1M，那么产品详情页展示缩略图 否则展示原图
                        if (size == null || size / 1024 / 1024 >= 1) {
                            detailPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
                        } else {
                            detailPicsThumbList.add(photoEntity.getUrl());
                        }
                    } else {
                        detailPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
                    }
                    QueryProductDetailResult.ImageInfo  detailPicsThumbsInfo = getImageWidthAndHeight(photoEntity.getPath(), StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl(), productEntity.getFsEa());
                    if(detailPicsThumbsInfo != null) {
                        detailPicsThumbsInfoList.add(detailPicsThumbsInfo);
                    }
                }
            }
            queryProductDetailResult.setDetailPicInfoList(detailPicInfoList);
            queryProductDetailResult.setDetailPicsThumbsInfoList(detailPicsThumbsInfoList);
        }

        //根据ea 和apiName 查出对象信息
        /*if (StringUtils.isNotEmpty(productEntity.getFsEa())) {
            entity = objectDescriptionDAO.getObjectDescriptionDetail("try_out_product_form", productEntity.getFsEa());
            if (entity != null) {
                queryProductDetailResult.setFieldDescribes(entity.getFieldDescribes());
                queryProductDetailResult.setWelcomeMsg(entity.getWelcomeMsg());
            }
        }*/
        queryProductDetailResult.setHeadPics(headPicsList);
        queryProductDetailResult.setDetailPics(detailPicsList);
        queryProductDetailResult.setHeadPicsThumbs(headPicsThumbList);
        queryProductDetailResult.setDetailPicsThumbs(detailPicsThumbList);
        // 查询物料对应表单
        CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataManager.getBindFormDataByObject(productEntity.getFsEa(), productEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
        if(customizeFormDataEntity != null) {
            QueryProductDetailResult.FormData formData = new QueryProductDetailResult.FormData();
            formData.setFormId(customizeFormDataEntity.getId());
            formData.setFormTitle(customizeFormDataEntity.getFormHeadSetting().getTitle());
            formData.setFromName(customizeFormDataEntity.getFormHeadSetting().getName());
            queryProductDetailResult.setFormData(formData);
            queryProductDetailResult.setBindObjectType(BindObjectType.CUSTOMIZE_FORM.getType());
            queryProductDetailResult.setFieldDescribes(customizeFormDataEntity.getFormBodySetting());
            queryProductDetailResult.setWelcomeMsg(customizeFormDataEntity.getFormHeadSetting().getIntroduce());
        } else {
            // 查询对应挂接微页面   微页面和表单只能挂接一种
            HexagonSiteObjectEntity hexagonSiteObjectEntity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(productEntity.getFsEa(), productEntity.getId(), ObjectTypeEnum.PRODUCT.getType());
            if (hexagonSiteObjectEntity != null) {
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteManager.getBindHexagonSiteByObject(hexagonSiteObjectEntity.getSiteId());
                if(hexagonSiteEntity != null) {
                    QueryProductDetailResult.HexagonSiteData hexagonSiteData = new QueryProductDetailResult.HexagonSiteData();
                    hexagonSiteData.setSiteId(hexagonSiteEntity.getId());
                    hexagonSiteData.setSiteName(hexagonSiteEntity.getName());
                    queryProductDetailResult.setHexagonSiteData(hexagonSiteData);
                    queryProductDetailResult.setBindObjectType(BindObjectType.HEXAGON_SITE.getType());
                }
            }
        }
        if(queryProductDetailResult != null && org.apache.commons.lang3.StringUtils.isNotBlank(queryProductDetailResult.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_MINIAPP_COVER.getType(), queryProductDetailResult.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                queryProductDetailResult.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType(), queryProductDetailResult.getId());
            if (coverCutH5PhotoEntity != null) {
                queryProductDetailResult.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_ORDINARY_COVER.getType(), queryProductDetailResult.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                queryProductDetailResult.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                queryProductDetailResult.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }
        queryProductDetailResult.setCtaRelationInfos(Lists.newArrayList());
        List<CtaRelationEntityDTO> ctaRelationList = ctaRelationDaoManager.getCtaRelationList(productEntity.getFsEa(), ObjectTypeEnum.PRODUCT.getType(), Lists.newArrayList(productEntity.getId()));
        if(CollectionUtils.isNotEmpty(ctaRelationList)){
            ctaRelationList.forEach(ctaRelationEntity -> {
                CtaRelationInfo ctaRelationInfo = new CtaRelationInfo();
                ctaRelationInfo.setCtaId(ctaRelationEntity.getCtaId());
                ctaRelationInfo.setCtaName(ctaRelationEntity.getCtaName());
                queryProductDetailResult.getCtaRelationInfos().add(ctaRelationInfo);
            });
        }
        return new Result<>(SHErrorCode.SUCCESS, queryProductDetailResult);
    }


    private QueryProductDetailResult.ImageInfo getImageWidthAndHeight(String apath, String url, String ea) {
        if(StringUtils.isBlank(apath)) {
            return null;
        }
        QueryProductDetailResult.ImageInfo result = new QueryProductDetailResult.ImageInfo();
       // byte[] bytes = fileV2Manager.downloadFileByUrl(url, null);
        byte[] bytes = fileV2Manager.downloadAFile(apath, ea);
        ImageInfo imageInfo = ImageInfoHelper.getImageInfo(bytes);
        result.setUrl(url);
        result.setImageH(imageInfo == null ? null : imageInfo.getHeight());
        result.setImageW(imageInfo == null ? null : imageInfo.getWidth());
        result.setSize(imageInfo == null ? 0 : imageInfo.getSize());
        return result;
    }

    @Override
    public Result<ObjectGroupListResult> listProductGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        fsUserId = fsUserId == null ? QywxUserConstants.BASE_VIRTUAL_USER_ID : fsUserId;
        Result<ObjectGroupListResult> result = webProductService.listProductGroup(ea, fsUserId, arg);
        if (result.isSuccess() && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getObjectGroupList())) {
            List<ListObjectGroupResult> objectGroupList = result.getData().getObjectGroupList().stream()
                    .filter(e -> !DefaultObjectGroupEnum.CREATED_BY_ME.getName().equals(e.getGroupName())).collect(Collectors.toList());
            result.getData().setObjectGroupList(objectGroupList);
        }
        return result;
    }

    @Override
    public Result<ObjectGroupListResult> listProductGroup4Outer(String upstreamEA, String outTenantId, String outUserId, ListGroupArg arg) {
        return webProductService.listProductGroup4Outer(upstreamEA, outTenantId, outUserId, arg);
    }
}
