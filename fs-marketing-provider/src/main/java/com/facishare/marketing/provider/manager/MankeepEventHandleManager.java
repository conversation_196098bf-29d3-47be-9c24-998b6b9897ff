package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingObjectResult;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.MarketingUserActionChannelType;
import com.facishare.marketing.common.enums.MarketingUserActionSceneType;
import com.facishare.marketing.common.enums.MarketingUserActionType;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.TriggerActionTypeEnum;
import com.facishare.marketing.common.enums.TriggerSceneEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.OutLinkDAO;
import com.facishare.marketing.provider.dao.UserMarketingBrowserUserRelationDao;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.LiveUserStatusDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowTaskDao;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.ArticleEntity;
import com.facishare.marketing.provider.entity.ObjectTagEntity;
import com.facishare.marketing.provider.entity.OutLinkEntity;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowAdditionalConfigEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowTaskEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingBrowserUserRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerData.BehaviorSendEventData;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.marketingAssistant.YxzsNoticeSendManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.mq.sender.BehaviorSendEventSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.statistic.common.util.HashUtil;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.otherrestapi.integral.constant.ActionApiNameConstant;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class MankeepEventHandleManager {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private MarketingFlowTaskDao marketingFlowTaskDao;
    @Autowired
    private MarketingFlowInstanceManager marketingFlowInstanceManager;
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @Autowired
    private BehaviorSendEventSender behaviorSendEventSender;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;
    @Autowired
    private TriggerInstanceManager triggerInstanceManager;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private OutLinkDAO outLinkDAO;
    @Autowired
    private TriggerTaskInstanceManager triggerTaskInstanceManager;
    @Autowired
    private YxzsNoticeSendManager noticeManager;

    public static Map<Integer, String> userActionTypeAndActionApiNameMap = Maps.newHashMap();
    private static Map<Integer, String> userActionTypeAndCategoryApiNameMap = Maps.newHashMap();
    private static final Set<Integer> LOOK_UP_OBJECT_ACTIONS = ImmutableSet.of(MarketingUserActionType.LOOK_UP_ARTICLE.getActionType(), MarketingUserActionType.LOOK_UP_PRODUCT.getActionType(), MarketingUserActionType.LOOK_UP_HEXAGON_SITE.getActionType(), MarketingUserActionType.LOOK_UP_FORM.getActionType(), MarketingUserActionType.LOOK_UP_FILE.getActionType());
    private static final Set<Integer> EMAIL_TRIGGER_ACTIONS = ImmutableSet.of(MarketingUserActionType.MAIL_OPEN.getActionType(), MarketingUserActionType.MAIL_CLICK.getActionType(), MarketingUserActionType.MAIL_RESPONSE.getActionType(), MarketingUserActionType.MAIL_REPORT_SPAM.getActionType(), MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType());

    private static final Set<Integer> ADD_TAG_OBJECT_ACTIONS = ImmutableSet.of(MarketingUserActionType.LOOK_UP_FILE.getActionType(), MarketingUserActionType.DOWNLOAD_FILE.getActionType(), MarketingUserActionType.LOOK_UP_HEXAGON_SITE.getActionType(), MarketingUserActionType.LOOK_UP_ARTICLE.getActionType()
            , MarketingUserActionType.LOOK_UP_OUT_LINK.getActionType());

    public static final String SMS_CATEGORY_NAME = "sms";
    static {
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_PRODUCT.getActionType(), ActionApiNameConstant.ACCESS);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_ARTICLE.getActionType(), ActionApiNameConstant.ACCESS);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_ACTIVITY.getActionType(), ActionApiNameConstant.ACCESS);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_FORM.getActionType(), ActionApiNameConstant.ACCESS);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_HEXAGON_SITE.getActionType(), ActionApiNameConstant.ACCESS);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.SUBMIT_FORM.getActionType(), ActionApiNameConstant.SUBMIT);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.CONFERENCE_CHECK_IN.getActionType(), ActionApiNameConstant.ATTEND);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.ENROLL_CONFERENCE.getActionType(), ActionApiNameConstant.APPLY);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_WEBSITE.getActionType(), ActionApiNameConstant.ACCESS);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.TRIGGER_WEBSITE_EVENT.getActionType(), ActionApiNameConstant.CLICK);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.ENROLL_LIVE.getActionType(), ActionApiNameConstant.ORDER);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LIVE_CHAT.getActionType(), ActionApiNameConstant.INTERACTIVE);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_LIVE.getActionType(), ActionApiNameConstant.WATCH);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.MAIL_DELIVER.getActionType(), ActionApiNameConstant.RECEIVED);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.MAIL_OPEN.getActionType(), ActionApiNameConstant.OPEN);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.MAIL_RESPONSE.getActionType(), ActionApiNameConstant.REPLY);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.MAIL_REPORT_SPAM.getActionType(), ActionApiNameConstant.REPORT_SPAM);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType(), ActionApiNameConstant.UN_SUBSCRIBE);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_RECORD.getActionType(), ActionApiNameConstant.PLAYBACK);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_FILE.getActionType(), ActionApiNameConstant.VIEW);
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.DOWNLOAD_FILE.getActionType(), ActionApiNameConstant.DOWNLAOD);
        //TODO 将这些字符串常量化  目前没权限
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.CLICK_WX_SERVICE_MENU.getActionType(), "click_wx_service_menu");
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.MAIL_CLICK.getActionType(), "click_mail_link");
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.SMS_RECEIVED.getActionType(), "sms_received");
        userActionTypeAndActionApiNameMap.put(MarketingUserActionType.LOOK_UP_SMS_URL_WITH_PHONE.getActionType(), "click_sms_link");



        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_PRODUCT.getActionType(), CategoryApiNameConstant.PRODUCT);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_ARTICLE.getActionType(), CategoryApiNameConstant.ARTICLE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_ACTIVITY.getActionType(), CategoryApiNameConstant.MEETING);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_FORM.getActionType(), CategoryApiNameConstant.FORM);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_HEXAGON_SITE.getActionType(), CategoryApiNameConstant.MICRO_PAGE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.SUBMIT_FORM.getActionType(), CategoryApiNameConstant.FORM);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.CONFERENCE_CHECK_IN.getActionType(), CategoryApiNameConstant.MEETING);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.ENROLL_CONFERENCE.getActionType(), CategoryApiNameConstant.MEETING);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_WEBSITE.getActionType(), CategoryApiNameConstant.WEBSITE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.TRIGGER_WEBSITE_EVENT.getActionType(), CategoryApiNameConstant.WEBSITE_ELEMENT);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.ENROLL_LIVE.getActionType(), CategoryApiNameConstant.LIVE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LIVE_CHAT.getActionType(), CategoryApiNameConstant.LIVE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_LIVE.getActionType(), CategoryApiNameConstant.LIVE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_RECORD.getActionType(), CategoryApiNameConstant.LIVE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.MAIL_DELIVER.getActionType(), CategoryApiNameConstant.MAIL);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.MAIL_OPEN.getActionType(), CategoryApiNameConstant.MAIL);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.MAIL_RESPONSE.getActionType(), CategoryApiNameConstant.MAIL);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.MAIL_REPORT_SPAM.getActionType(), CategoryApiNameConstant.MAIL_BOX);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType(), CategoryApiNameConstant.MAIL_BOX);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_FILE.getActionType(), CategoryApiNameConstant.FILE);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.DOWNLOAD_FILE.getActionType(), CategoryApiNameConstant.FILE);

        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_OUT_LINK.getActionType(), CategoryApiNameConstant.MICRO_PAGE);

        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.CLICK_WX_SERVICE_MENU.getActionType(), CategoryApiNameConstant.OFFICIAL_ACCOUNT);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.MAIL_CLICK.getActionType(), CategoryApiNameConstant.MAIL);
        //TODO 将这些字符串常量化  目前没权限
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.SMS_RECEIVED.getActionType(), SMS_CATEGORY_NAME);
        userActionTypeAndCategoryApiNameMap.put(MarketingUserActionType.LOOK_UP_SMS_URL_WITH_PHONE.getActionType(), SMS_CATEGORY_NAME);

    }

    public void handle(MarketingUserActionEvent event) {
        ThreadPoolUtils.execute(() -> doProcessIntegral(event), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        ThreadPoolUtils.execute(() -> {
            if (MarketingUserActionType.SUBMIT_FORM.getActionType() == event.getActionType()) {
                String userMarketingAccountId = doGetUserMarketingId(event);
                if (userMarketingAccountId != null) {
                    Integer ei = eieaConverter.enterpriseAccountToId(event.getEa());
                    doStartFlowInstances(ei, userMarketingAccountId, event);
                    doFinishTasks(ei, userMarketingAccountId, event);
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        //营销助手通知
        ThreadPoolUtils.execute(() -> noticeManager.sendUnionMsg(event), ThreadPoolTypeEnums.HEAVY_BUSINESS);
        doAddTagForMarketingUser(event);
        doHandleEmailActionTrigger(event);
        doHandleLookUpActionTrigger(event);
        doHandleLiveActionTrigger(event);
        doHandleFileActionTrigger(event);
        doHandleOfficialWebsiteActionTrigger(event);
        doHandleAllActionTrigger(event);
        try {
            Map<String, Object> dataMap = event.toMap();
            if (dataMap != null) {
                DataPersistor.asyncLog("visitor_action", dataMap);
            }
        }catch (Exception e){
            log.warn("Exception", e);
        }
    }

    /**
     * 避免埋点遗漏触发处理
     *
     * @param event
     */
    private void doHandleAllActionTrigger(MarketingUserActionEvent event) {
        try {
            if (event.getObjectType() != null && !Strings.isNullOrEmpty(event.getObjectId())) {
                // 此处防止重复触发
                if (event.getExtensionParams() != null
                        && (event.getExtensionParams().containsKey("actionDurationTime") || event.getExtensionParams().containsKey("actionProportion"))) {
                    log.info("此处防止重复触发 {}", event.getObjectId());
                    return;
                }
                UserMarketingObjectResult result = noticeManager.doGetUserMarketingId(event);
                if (result == null || StringUtils.isEmpty(result.getMarketingUserId())) {
                    log.warn("没有找到营销助手用户关联关系,event:{}", event);
                    return;
                }
                String marketingUserId = result.getMarketingUserId();
                String ea = event.getEa();
                Map<String, Object> paramMap = event.toMap();
                paramMap.put("targetId", result.getTargetId());
                paramMap.put("targetType", result.getTargetType());
                ThreadPoolUtils.execute(() -> {
                    // 触发监听中的sop任务
                    triggerTaskInstanceManager.startTaskInstanceByMarketingUserId(ea, marketingUserId, paramMap);
                }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }
        } catch (Exception e) {
            log.warn("Exception at handle forward or download action", e);
        }
    }

    private void doAddTagForMarketingUser(MarketingUserActionEvent event){
        if (!ADD_TAG_OBJECT_ACTIONS.contains(event.getActionType())){
            return;
        }
        String marketingUserId = doGetUserMarketingId(event);
        if (StringUtils.isEmpty(marketingUserId)){
            return;
        }

        if (event.getObjectType() == ObjectTypeEnum.FILE_LIBRARY.getType() &&
                (event.getActionType() == MarketingUserActionType.LOOK_UP_FILE.getActionType()|| event.getActionType() == MarketingUserActionType.DOWNLOAD_FILE.getActionType())){
            FileEntity fileEntity = fileLibraryDAO.getById(event.getObjectId());
            if (fileEntity != null && !CollectionUtils.isEmpty(fileEntity.getTagNames())){
                userMarketingAccountManager.batchAddTagsToUserMarketingAccount(fileEntity.getEa(), Lists.newArrayList(CrmObjectApiNameEnum.CRM_LEAD.getName()), Lists.newArrayList(marketingUserId), fileEntity.getTagNames());
            }
        }
        if (event.getObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType() && event.getActionType() == MarketingUserActionType.LOOK_UP_HEXAGON_SITE.getActionType()){
            HexagonSiteEntity entity = hexagonSiteDAO.getById(event.getObjectId());
            if (entity != null){
                ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(entity.getEa(), entity.getId(), ObjectTypeEnum.HEXAGON_SITE.getType());
                if (objectTagEntity != null){
                    userMarketingAccountManager.batchAddTagsToUserMarketingAccount(entity.getEa(), null, Lists.newArrayList(marketingUserId), objectTagEntity.getTagNameList());
                }
            }
        }
        if (event.getObjectType() == ObjectTypeEnum.ARTICLE.getType() && event.getActionType() == MarketingUserActionType.LOOK_UP_ARTICLE.getActionType()){
            ArticleEntity articleEntity = articleDAO.getById(event.getObjectId());
            if (articleEntity == null){
                return;
            }
            ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(articleEntity.getFsEa(), articleEntity.getId(), ObjectTypeEnum.ARTICLE.getType());
            if (objectTagEntity != null){
                userMarketingAccountManager.batchAddTagsToUserMarketingAccount(articleEntity.getFsEa(), null, Lists.newArrayList(marketingUserId), objectTagEntity.getTagNameList());
            }
        }
        if (event.getObjectType() == ObjectTypeEnum.OUT_LINK.getType() && event.getActionType() == MarketingUserActionType.LOOK_UP_OUT_LINK.getActionType()){
            OutLinkEntity outLink = outLinkDAO.getById(event.getObjectId());
            if (outLink == null){
                return;
            }
            ObjectTagEntity objectTagEntity = objectTagManager.queryObjectTag(outLink.getEa(), outLink.getId(), ObjectTypeEnum.OUT_LINK.getType());
            if (objectTagEntity != null){
                userMarketingAccountManager.batchAddTagsToUserMarketingAccount(outLink.getEa(), null, Lists.newArrayList(marketingUserId), objectTagEntity.getTagNameList());
            }
        }
    }

    private void doHandleLookUpActionTrigger(MarketingUserActionEvent event) {
        try {
            // 此处防止重复触发
            if (LOOK_UP_OBJECT_ACTIONS.contains(event.getActionType()) && event.getObjectType() != null && !Strings.isNullOrEmpty(event.getObjectId())){
                if (event.getExtensionParams() != null
                        && (event.getExtensionParams().containsKey("actionDurationTime") || event.getExtensionParams().containsKey("actionProportion"))) {
                    log.info("此处防止重复触发 {}", event.getObjectId());
                    return;
                }
                String marketingUserId = doGetUserMarketingId(event);
                if (Strings.isNullOrEmpty(marketingUserId)){
                    return;
                }
                String ea = event.getEa();
                Map<String, Object> paramMap = event.toMap();
                if (!Strings.isNullOrEmpty(event.getMarketingEventId())) {
                    ActivityEntity conference = conferenceDAO.getConferenceByEaAndMarketingEventId(ea, event.getMarketingEventId());
                    if (conference != null) {
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(ea, marketingUserId, TriggerSceneEnum.CONFERENCE.getTriggerScene(), conference.getId(), TriggerActionTypeEnum.LOOK_UP_OBJECT_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                    }
                    int ei = eieaConverter.enterpriseAccountToId(ea);
                    MarketingLiveEntity live = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, event.getMarketingEventId());
                    if (live != null) {
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(ea, marketingUserId, TriggerSceneEnum.LIVE.getTriggerScene(), live.getId(), TriggerActionTypeEnum.LOOK_UP_OBJECT_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                    }
                    //市场活动触发
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(ea, marketingUserId, TriggerSceneEnum.MARKETING_EVENT.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.LOOK_UP_OBJECT_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                    //目标人群触发
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(ea, marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.LOOK_UP_OBJECT_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
                }
                //企微sop触发
                triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(ea, marketingUserId, TriggerSceneEnum.QYWX.getTriggerScene(), TriggerSceneEnum.QYWX.getTriggerScene(), TriggerActionTypeEnum.LOOK_UP_OBJECT_IN_MARKETING_EVENT.getTriggerActionType(), paramMap);
             }
        }catch (Exception e){
            log.warn("Exception at handle look up action", e);
        }
    }

    private void doHandleEmailActionTrigger(MarketingUserActionEvent event) {
        try {
            if (EMAIL_TRIGGER_ACTIONS.contains(event.getActionType()) && !Strings.isNullOrEmpty(event.getFingerPrint())){
                String marketingUserId = doGetUserMarketingId(event);
                if (Strings.isNullOrEmpty(marketingUserId)){
                    return;
                }
                Map<String, Object> paramMap = event.toMap();
                String mailTaskId = event.getObjectId();
                MailSendTaskEntity mailSendTask = mailSendTaskDAO.getById(mailTaskId);
                paramMap.put("emailSubject", mailSendTask.getSubject());
                if(MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneMsgAndTriggerAction(event.getEa(), marketingUserId, TriggerSceneEnum.EMAIL.getTriggerScene(), "email", TriggerActionTypeEnum.MAIL_UNSUBSCRIBE.getTriggerActionType(), paramMap);
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneMsgAndTriggerAction(event.getEa(), marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.MAIL_UNSUBSCRIBE.getTriggerActionType(), paramMap);
                }
                if(MarketingUserActionType.MAIL_REPORT_SPAM.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneMsgAndTriggerAction(event.getEa(), marketingUserId, TriggerSceneEnum.EMAIL.getTriggerScene(), "email", TriggerActionTypeEnum.MAIL_REPORT_SPAM.getTriggerActionType(), paramMap);
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneMsgAndTriggerAction(event.getEa(), marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.MAIL_REPORT_SPAM.getTriggerActionType(), paramMap);
                }
                if(MarketingUserActionType.MAIL_OPEN.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.EMAIL.getTriggerScene(), "email", TriggerActionTypeEnum.MAIL_OPEN.getTriggerActionType(), paramMap);
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.MAIL_OPEN.getTriggerActionType(), paramMap);
                }
                if(MarketingUserActionType.MAIL_RESPONSE.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.EMAIL.getTriggerScene(), "email", TriggerActionTypeEnum.MAIL_RESPONSE.getTriggerActionType(),  paramMap);
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.MAIL_RESPONSE.getTriggerActionType(),  paramMap);
                }
                if(MarketingUserActionType.MAIL_CLICK.getActionType() == event.getActionType()){
                    if(!Strings.isNullOrEmpty(event.getSceneId())){
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.EMAIL.getTriggerScene(), "email", TriggerActionTypeEnum.MAIL_CLICK.getTriggerActionType(),  paramMap);
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.MAIL_CLICK.getTriggerActionType(),  paramMap);
                    }
                }
            }
        }catch (Exception e){
            log.warn("Exception at handle look up action", e);
        }
    }

    private void doHandleLiveActionTrigger(MarketingUserActionEvent event) {
        try {
            if (event.getActionType() == null || Strings.isNullOrEmpty(event.getEa()) || Strings.isNullOrEmpty(event.getObjectId()) || Strings.isNullOrEmpty(event.getFingerPrint())){
                return;
            }
            if (MarketingUserActionType.LOOK_UP_LIVE.getActionType() == event.getActionType() || MarketingUserActionType.LOOK_UP_RECORD.getActionType() == event.getActionType() || MarketingUserActionType.LIVE_CHAT.getActionType() == event.getActionType()){
                UserMarketingAccountEntity marketingUser = userMarketingBrowserUserRelationDao.getMarketingUserAccountByEaAndBrowserUserId(event.getEa(), event.getFingerPrint());
                if (Strings.isNullOrEmpty(marketingUser.getPhone())){
                    return;
                }
                MarketingLiveEntity marketingLive = marketingLiveDAO.getById(event.getObjectId());
                if (marketingLive == null) {
                    return;
                }
                Map<String, Object> paramMap = event.toMap();
                LiveUserStatusEntity liveUserStatus = null;
                if (marketingLive.getPlatform() != null &&
                        (marketingLive.getPlatform() == LivePlatformEnum.VHALL.getType() || marketingLive.getPlatform() == LivePlatformEnum.POLYV.getType())) {
                    List<LiveUserStatusEntity> liveUserStatusEntities = liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveIdAndPhone(marketingLive.getXiaoetongLiveId(), Lists.newArrayList(marketingUser.getPhone()));
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(liveUserStatusEntities)){
                        liveUserStatus = liveUserStatusEntities.get(0);
                    }
                } else {
                    liveUserStatus = liveUserStatusDAO.getLiveUserStatusByLiveIdAndPhone(marketingLive.getLiveId(), marketingUser.getPhone());
                }
                if (liveUserStatus == null){
                    return;
                }

                if (MarketingUserActionType.LOOK_UP_LIVE.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndActionDurationMinutes(event.getEa(), marketingUser.getId(), TriggerSceneEnum.LIVE.getTriggerScene(), event.getObjectId(), TriggerActionTypeEnum.LIVE_VIEW.getTriggerActionType(), liveUserStatus.getViewTime(),paramMap);
                }
                if (MarketingUserActionType.LOOK_UP_RECORD.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndActionDurationMinutes(event.getEa(), marketingUser.getId(), TriggerSceneEnum.LIVE.getTriggerScene(), event.getObjectId(), TriggerActionTypeEnum.LIVE_REPLAY.getTriggerActionType(), liveUserStatus.getReplayTime(),paramMap);
                }
                if (MarketingUserActionType.LIVE_CHAT.getActionType() == event.getActionType()){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndActionCount(event.getEa(), marketingUser.getId(), TriggerSceneEnum.LIVE.getTriggerScene(), event.getObjectId(), TriggerActionTypeEnum.LIVE_CHAT.getTriggerActionType(), liveUserStatus.getInteractiveCount(),paramMap);
                }
            }
        }catch (Exception e){
            log.warn("Error at trigger trigger, event:{}", event);
        }
    }

    private void doHandleFileActionTrigger(MarketingUserActionEvent event){
        try{
            if (event.getActionType() == null || Strings.isNullOrEmpty(event.getEa()) || Strings.isNullOrEmpty(event.getObjectId())){
                return;
            }

            String marketingUserId = doGetUserMarketingId(event);
            if (marketingUserId == null) {
                return;
            }
            if (event.getObjectType() == ObjectTypeEnum.FILE_LIBRARY.getType() && (
                    MarketingUserActionType.DOWNLOAD_FILE.getActionType() == event.getActionType() || MarketingUserActionType.LOOK_UP_FILE.getActionType() == event.getActionType())) {
                Map<String, Object> paramMap = event.toMap();
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(event.getMarketingEventId())) {
                    MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(event.getEa(), -10000, event.getMarketingEventId());
                    if (marketingEventData == null) {
                        return;
                    }
                    if (marketingEventData.getEventType().equals(MarketingEventEnum.MEETING_SALES.getEventType())) {
                        ActivityEntity activity = conferenceDAO.getConferenceByMarketingEventId(event.getMarketingEventId(), event.getEa());
                        if (activity != null) {
                            triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId,
                                    TriggerSceneEnum.CONFERENCE.getTriggerScene(), activity.getId(), TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType(), paramMap);
                        }
                    } else if (marketingEventData.getEventType().equals(MarketingEventEnum.LIVE_MARKETING.getEventType())) {
                        MarketingLiveEntity live = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(event.getEa()), event.getMarketingEventId());
                        if (live != null) {
                            triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId,
                                    TriggerSceneEnum.LIVE.getTriggerScene(), live.getId(), TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType(), paramMap);
                        }
                    } else {
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId,
                                TriggerSceneEnum.MARKETING_EVENT.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType(), paramMap);
                        triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId,
                                TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(), event.getMarketingEventId(), TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType(), paramMap);
                    }
                }

                if (org.apache.commons.lang3.StringUtils.isNotEmpty(event.getWxAppId()) && org.apache.commons.lang3.StringUtils.isNotEmpty(event.getWxOpenId())){
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId,
                            TriggerSceneEnum.WX_SERVICE_ACCOUNT.getTriggerScene(), event.getWxAppId(), TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType(), paramMap);

                }

                //企微sop触发
                triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUserId, TriggerSceneEnum.QYWX.getTriggerScene(), TriggerSceneEnum.QYWX.getTriggerScene(), TriggerActionTypeEnum.VISIT_FILE.getTriggerActionType(), paramMap);
            }
        }catch (Exception e){
            log.warn("doHandleFileActionTrigger Error at trigger trigger, event:{}", event);
        }
    }

    private void doHandleOfficialWebsiteActionTrigger(MarketingUserActionEvent event) {
        try {
            if (event.getActionType() == null || Strings.isNullOrEmpty(event.getEa()) || Strings.isNullOrEmpty(event.getObjectId()) || Strings.isNullOrEmpty(event.getFingerPrint())){
                return;
            }
            if (MarketingUserActionType.LOOK_UP_WEBSITE.getActionType() == event.getActionType()  ||
                (MarketingUserActionType.SUBMIT_FORM.getActionType() == event.getActionType() &&
                    (MarketingUserActionSceneType.OFFICIAL_WEBSITE.getSceneType() == event.getSceneType() || MarketingUserActionSceneType.HEXAGON_SITE_FORM.getSceneType() == event.getSceneType()))) {
                Map<String, Object> paramMap = event.toMap();
                UserMarketingBrowserUserRelationEntity marketingUser = userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(event.getEa(), event.getFingerPrint());

                if (MarketingUserActionType.LOOK_UP_WEBSITE.getActionType() == event.getActionType()) {
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUser.getUserMarketingId(), TriggerSceneEnum.OFFICIAL_WEBSITE.getTriggerScene(),
                        event.getSceneId(), TriggerActionTypeEnum.LOOK_UP_WEBSITE.getTriggerActionType(), paramMap);
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUser.getUserMarketingId(), TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(),
                            event.getMarketingEventId(), TriggerActionTypeEnum.LOOK_UP_WEBSITE.getTriggerActionType(), paramMap);
                }
                if (MarketingUserActionType.SUBMIT_FORM.getActionType() == event.getActionType() &&
                    (MarketingUserActionSceneType.OFFICIAL_WEBSITE.getSceneType() == event.getSceneType() || MarketingUserActionSceneType.HEXAGON_SITE_FORM.getSceneType() == event.getSceneType())) {
                    if (event.getExtensionParams() != null) {
                        paramMap.put("enrollId", event.getExtensionParams().get("enrollId"));
                    }
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUser.getUserMarketingId(), TriggerSceneEnum.OFFICIAL_WEBSITE.getTriggerScene(),
                            event.getSceneId(), TriggerActionTypeEnum.SUBMIT_WEBSITE_FORM.getTriggerActionType(), paramMap);
                    triggerInstanceManager.startInstanceByMarketingUserIdAndSceneAndTargetObject(event.getEa(), marketingUser.getUserMarketingId(), TriggerSceneEnum.TARGET_CROWD_OPERATION.getTriggerScene(),
                            event.getMarketingEventId(), TriggerActionTypeEnum.SUBMIT_WEBSITE_FORM.getTriggerActionType(), paramMap);
                }
            }
        } catch (Exception e) {
            log.warn("doHandleOfficialWebsiteActionTrigger Error at trigger trigger, event:{}", event);
        }
    }

    private void doProcessIntegral(MarketingUserActionEvent event) {
        if (!event.isUpdate() && (event.getChannelType().equals(MarketingUserActionChannelType.MANKEEP.getChannelType()) ||
                event.getChannelType().equals(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType()) ||
                event.getChannelType().equals(MarketingUserActionChannelType.H5.getChannelType()) ||
                event.getChannelType().equals(MarketingUserActionChannelType.EMAIL.getChannelType()) ||
                event.getChannelType().equals(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType()) ||
                event.getChannelType().equals(MarketingUserActionChannelType.SMS.getChannelType()))) {
            int ei = eieaConverter.enterpriseAccountToId(event.getEa());
            String userMarketingAccountId = doGetUserMarketingId(event);
            log.info("doProcessIntegral event:{} userMarketingAccountId:{}", event, userMarketingAccountId);
            String actionApiName = userActionTypeAndActionApiNameMap.get(event.getActionType());
            String categoryApiName = userActionTypeAndCategoryApiNameMap.get(event.getActionType());
            String materialApiName = null;
            if (MarketingUserActionType.SUBMIT_FORM.getActionType() == event.getActionType() &&  event.getSceneType() != null && MarketingUserActionSceneType.ACTIVITY_FORM.getSceneType() == event.getSceneType()) {
                materialApiName = event.getSceneId();
            } else if(MarketingUserActionType.MAIL_DELIVER.getActionType() == event.getActionType() || MarketingUserActionType.MAIL_OPEN.getActionType() == event.getActionType()
                    || MarketingUserActionType.MAIL_RESPONSE.getActionType() == event.getActionType() || MarketingUserActionType.MAIL_CLICK.getActionType() == event.getActionType()){
                if (!StringUtils.isEmpty(event.getObjectId())) {
                    String taskId = event.getObjectId();
                    MailSendTaskEntity task = mailSendTaskDAO.getById(taskId);
                    if (task != null && !Strings.isNullOrEmpty(task.getSenderIds())){
                        materialApiName = HashUtil.hash(task.getSubject());
                    }
                }
            }else if(MarketingUserActionType.MAIL_REPORT_SPAM.getActionType() == event.getActionType() || MarketingUserActionType.MAIL_UNSUBSCRIBE.getActionType() == event.getActionType()){
                String taskId = event.getObjectId();
                MailSendTaskEntity task = mailSendTaskDAO.getById(taskId);
                if (task != null && !Strings.isNullOrEmpty(task.getSenderIds())){
                    List<String> senderIds = GsonUtil.getGson().fromJson(task.getSenderIds(), new TypeToken<ArrayList<String>>(){}.getType());
                    materialApiName = senderIds.get(0);
                }
            }else {
                materialApiName = event.getObjectId();
            }

            log.info("doProcessIntegral event:{}, userMarketingId:{} ,actionApiName:{},categoryApiName:{},materialApiName:{}", event, userMarketingAccountId, actionApiName, categoryApiName, materialApiName);
            if (StringUtils.isEmpty(actionApiName) || StringUtils.isEmpty(categoryApiName) || StringUtils.isEmpty(materialApiName) || StringUtils.isEmpty(userMarketingAccountId)) {
                log.info("do not process integral event:{}, userMarketingId:{} ,actionApiName:{},categoryApiName:{},materialApiName:{}", event, userMarketingAccountId, actionApiName, categoryApiName, materialApiName);
                return;
            }
            BehaviorSendEventData data = new BehaviorSendEventData();
            data.setActionApiName(actionApiName);
            data.setCategoryApiName(categoryApiName);
            data.setMaterialApiName(materialApiName);
            data.setTenantId(String.valueOf(ei));
            data.setEa(event.getEa());
            data.setTimestamp(System.currentTimeMillis());
            data.setExtensionParams(event.getExtensionParams());
            data.setUserMarketingId(userMarketingAccountId);
            behaviorSendEventSender.sendToDelayMq(data);
        }else {
            log.info("doProcessIntegral do nothing MarketingUserActionEvent:{}",event);

        }

    }

    private void doStartFlowInstances(Integer ei, String userMarketingAccountId, MarketingUserActionEvent event) {
        List<MarketingFlowAdditionalConfigEntity> marketingFlowAdditionalConfigs = marketingFlowAdditionalConfigDao.listMatchedSubmitFormFlows(ei, event.getObjectId());
        if (marketingFlowAdditionalConfigs != null && !marketingFlowAdditionalConfigs.isEmpty()) {
            Set<String> marketingFlowIds = marketingFlowAdditionalConfigs.stream().map(MarketingFlowAdditionalConfigEntity::getBpmFlowId).collect(Collectors.toSet());
            marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), marketingFlowIds);
        }
    }

    private void doFinishTasks(Integer ei, String userMarketingAccountId, MarketingUserActionEvent event) {
        List<MarketingFlowTaskEntity> tasks = marketingFlowTaskDao.listMatchedSubmitFormTasks(ei, userMarketingAccountId, event.getObjectId());
        if (tasks != null && !tasks.isEmpty()) {
            Set<String> taskIds = tasks.stream().map(MarketingFlowTaskEntity::getTaskId).collect(Collectors.toSet());
            marketingFlowInstanceManager.finishTasks(ei, taskIds, null);
        }
    }

    private String doGetUserMarketingId(MarketingUserActionEvent event) {
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(event.getEa());
        associationArg.setPhone(event.getPhone());
        if (event.getChannelType().equals(MarketingUserActionChannelType.MANKEEP.getChannelType())) {
            associationArg.setType(ChannelEnum.MINIAPP.getType());
            associationArg.setAssociationId(event.getUid());
            associationArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        } else if (event.getChannelType().equals(MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType())) {
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setAssociationId(event.getWxOpenId());
            associationArg.setWxAppId(event.getWxAppId());
            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
        } else if (event.getChannelType().equals(MarketingUserActionChannelType.H5.getChannelType()) || event.getChannelType().equals(MarketingUserActionChannelType.EMAIL.getChannelType()) || event.getChannelType().equals(MarketingUserActionChannelType.OFFICIAL_WEB_SITE.getChannelType()) || event.getChannelType().equals(MarketingUserActionChannelType.SMS.getChannelType()) ) {
            associationArg.setAssociationId(event.getFingerPrint());
            associationArg.setType(ChannelEnum.BROWSER_USER.getType());
            associationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        }else {
            return null;
        }
        associationArg.setTriggerAction("MankeepEventHandle");

        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
        if (associationResult == null) {
            return null;
        }
        return associationResult.getUserMarketingAccountId();
    }
}










