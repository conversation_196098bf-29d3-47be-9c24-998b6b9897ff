/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.hexagon;

import com.facishare.marketing.api.service.kis.SpreadTaskService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.api.vo.QueryHexagonBackgroudColorVO;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.enterprise.common.util.UrlEncoder;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.hexagon.*;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.result.hexagon.*;
import com.facishare.marketing.api.service.CrmService;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.api.service.MarketingEventService;
import com.facishare.marketing.api.service.MemberService;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.vo.GetMarketingContentSiteVO;
import com.facishare.marketing.api.vo.SimpleHexagonVO;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.WxApiConstants;
import com.facishare.marketing.common.contstant.material.MaterialTypeEnum;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.hexagon.*;
import com.facishare.marketing.common.enums.qywx.QywxQrCodeBrowseUserRelationEnum;
import com.facishare.marketing.common.result.FileToHexagonApiResult;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.ContentAuthorize;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.*;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager;
import com.facishare.marketing.provider.dao.manager.MaterialRelationDaoManager;
import com.facishare.marketing.provider.dao.officialWebsite.WxOfficialAccountsQrCodeDAO;
import com.facishare.marketing.provider.dao.param.hexagon.HexagonSiteQueryParam;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeUserMarketingRelationDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dto.FileToHexagonDataDTO;
import com.facishare.marketing.provider.dto.GroupNameObjectIdDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.*;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxQrCodeUserMarketingRelationEntity;
import com.facishare.marketing.provider.innerArg.qywx.ContactMeConfigArg;
import com.facishare.marketing.provider.innerData.EnterpriseCommerceData;
import com.facishare.marketing.provider.innerResult.qywx.GetContactMeResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.image.ImageCreator;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.lock.LockManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.statistic.outapi.arg.ActionDurationTimeObjectArg;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.marketing.statistic.outapi.result.ActionDurationTimeAvgByObjectResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.open.common.storage.mysql.dao.Pager;
import com.facishare.wechat.dubborestouterapi.arg.QueryStoreQrCodeArg;
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg;
import com.facishare.wechat.dubborestouterapi.result.QrCodeResult;
import com.facishare.wechat.dubborestouterapi.service.union.WechatQrCodeRestService;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("hexagonService")
public class HexagonServiceImpl implements HexagonService {

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;
    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;

    @Autowired
    private MaterialTagManager materialTagManager;

    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private ImageCreator imageCreator;
    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;

    @Autowired
    private CrmService crmService;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private HexagonSiteDAOManager hexagonSiteDAOManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private HexagonOfficialWebsiteDAO hexagonOfficialWebsiteDAO;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;

    @Autowired
    private ObjectBindDetailDAO objectBindDetailDAO;

    @Autowired
    private CustomizeFormDataService customizeFormDataService;

    @Autowired
    private MarketingEventService marketingEventService;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private MemberConfigDao memberConfigDao;
    @Autowired
    private ObjectGroupManager objectGroupManager;
    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;
    @Autowired
    private ObjectGroupDAO objectGroupDAO;
    @Autowired
    private ObjectAccessAuthorizeDAO accessAuthorizeDAO;
    @Autowired
    private DisplayOrderDao displayOrderDao;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;
    @ReloadableProperty("host")
    private String host;
    @ReloadableProperty("marketing.dns.industry")
    private String dnsIndustry;
    @ReloadableProperty("activity.center.hexagon.id")
    private String activityCenterHexagonId;
    @ReloadableProperty("content.center.hexagon.id")
    private String contentCenterHexagonId;
    @ReloadableProperty("product.spread.hexagon.id")
    private String productSpreadHexagonId;
//    @ReloadableProperty("enterprise.dynamics.hexagon.id")
//    private String enterpriseDynamicsHexagonId;

    @ReloadableProperty("file_to_hexagon_template")
    private String fileToHexagonTemplate;

    @ReloadableProperty("picture_hexagon_template")
    private String pictureHexagonTemplate;

    @ReloadableProperty("fs_document_convert_ip")
    private String fsDocumentConvertBigIp;

    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private MemberAccessibleObjectDao memberAccessibleObjectDao;
    @Autowired
    private HexagonContentCenterDAO hexagonContentCenterDAO;
    @Autowired
    private HexagonProductSpreadDAO hexagonProductSpreadDAO;
    @Autowired
    private HexagonActivityCenterDAO hexagonActivityCenterDAO;
    @Autowired
    private HexagonMiniAppSiteSpreadDao hexagonMiniAppSiteSpreadDao;
    @Autowired
    private ObjectTagManager objectTagManager;
    @Autowired
    private MemberService memberService;
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private UserRoleManager userRoleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private MaterialRelationDao materialRelationDao;

    @Autowired
    private MaterialRelationDaoManager materialRelationDaoManager;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @ReloadableProperty("picture.preview.url.cdn")
    private String cdnSharePath;

    @ReloadableProperty("qywx.group.message.default.cover")
    private String groupMessageDefaultCoverPath;

    @Autowired
    private WxOfficialAccountsQrCodeDAO wxOfficialAccountsQrCodeDAO;

    @Autowired
    private WxServiceQrCodeUserMarketingRelationDAO wxServiceQrCodeUserMarketingRelationDAO;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @ReloadableProperty("marketing_appid")
    private String appId;

    @Autowired
    private WechatQrCodeRestService wechatQrCodeRestService;

    @Autowired
    private MarketingPromotionSourceArgObjectRelationManager marketingPromotionSourceArgObjectRelationManager;

    @Autowired
    private OuterServiceWechatService outerServiceWechatService;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private QywxAddFanQrCodeUserMarketingRelationDAO qywxAddFanQrCodeUserMarketingRelationDAO;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private FileToHexagonDAO fileToHexagonDAO;
    @Autowired
    private HttpManager httpManager;

    @Autowired
    private OutLinkMktParamManager outLinkMktParamManager;

    @Autowired
    private SendService sendService;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Autowired
    private HexagonBackgroudColorSettingDAO hexagonBackgroudColorSettingDAO;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Autowired
    MarketingActivityManager marketingActivityManager;

    @Autowired
    private SpreadTaskService spreadTaskService;

    public static final String HEXAGON_QR_PRE = "HG_";
    @ReloadableProperty("picture.fsEa")
    private String pictureEa;


    @Override
    public Result<CreateSiteResult> editSite(String ea, Integer userId, CreateSiteArg arg) {
        String id = arg.getId();
        String pageId = null;
        if (StringUtils.isNotBlank(arg.getId())) {
            HexagonSiteEntity queryHexagonSiteEntity = hexagonSiteDAO.getById(arg.getId());
            if (null == queryHexagonSiteEntity) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
            }
            int oldStatus = queryHexagonSiteEntity.getStatus();
            if (!queryHexagonSiteEntity.getEa().equals(ea)) {
                return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
            }

            if (!queryHexagonSiteEntity.isSystemSite() && !arg.isSystem()) {
                int count = hexagonSiteDAO.queryHexagonSiteCountByName(ea, arg.getName());
                if (StringUtils.equals(arg.getName(), queryHexagonSiteEntity.getName())){
                    if (count > 1) {
                        log.info("HexagonServiceImpl.editSite name not change failed, hexagon's name is exist arg:{}", arg);
                        return Result.newError(SHErrorCode.HEXAGON_SITE_NAME_EXIST);
                    }
                }else {
                    if (count > 0) {
                        log.info("HexagonServiceImpl.editSite name change failed, hexagon's name is exist arg:{}", arg);
                        return Result.newError(SHErrorCode.HEXAGON_SITE_NAME_EXIST);
                    }
                }
            }

            if (!EmptyUtil.isNullForList(arg.getStatus())) {
                HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
                HexagonStatusEnum queryHexagonStatusEnum = HexagonStatusEnum.getByType(queryHexagonSiteEntity.getStatus());
                if (!HexagonStatusEnum.validityCheck(queryHexagonStatusEnum, hexagonStatusEnum)) {
                    return Result.newError(SHErrorCode.HEXAGON_STATUS_CHANGE_FAILED);
                }
            }

            HexagonSiteEntity hexagonSiteEntity = queryHexagonSiteEntity;
            hexagonSiteEntity.setStatus(arg.getStatus() != null ? arg.getStatus() : queryHexagonSiteEntity.getStatus());
            hexagonSiteEntity.setName(StringUtils.isNotBlank(arg.getName()) ? arg.getName() : queryHexagonSiteEntity.getName());
            hexagonSiteEntity.setUpdateBy(userId);
            hexagonSiteEntity.setUpdateTime(new Date());

            boolean updateRedisResult = redisManager.setHexagonSiteToRedis(hexagonSiteEntity.getId(), hexagonSiteEntity);
            if (!updateRedisResult) {
                log.error("HexagonService.editSite update site redis failed, entity={}", hexagonSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            int updateResult = hexagonSiteDAOManager.update(hexagonSiteEntity);
            if (updateResult != 1) {
                log.error("HexagonService.editSite update site failed, entity={}", hexagonSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            if (oldStatus == HexagonStatusEnum.NOTVISIBLE.getType()) {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteEntity.getName(), OperateTypeEnum.ADD);
            } else {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteEntity.getName(), OperateTypeEnum.EDIT);
            }
        } else {
            id = UUIDUtil.getUUID();

            if (StringUtils.isBlank(arg.getName())) {
                arg.setName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEMINAPPNAVBARMANAGER_20) + id.substring(0, 4));
            }

            HexagonSiteEntity hexagonSiteEntity = BeanUtil.copy(arg, HexagonSiteEntity.class);
            hexagonSiteEntity.setId(id);
            hexagonSiteEntity.setEa(ea);
            hexagonSiteEntity.setCreateBy(userId);
            hexagonSiteEntity.setUpdateBy(userId);
            hexagonSiteEntity.setSystemSite(arg.isSystem());
            hexagonSiteEntity.setCreateTime(new Date());
            hexagonSiteEntity.setUpdateTime(new Date());

            boolean updateSiteRedisResult = redisManager.setHexagonSiteToRedis(hexagonSiteEntity.getId(), hexagonSiteEntity);
            if (!updateSiteRedisResult) {
                log.error("HexagonService.editSite insert site redis failed, entity={}", hexagonSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            if (arg.isSystem()){
                hexagonSiteEntity.setSystemSite(true);
            }
            int insertResult = hexagonSiteDAOManager.insert(hexagonSiteEntity);
            if (insertResult != 1) {
                log.error("HexagonService.editSite insert site failed, entity={}", hexagonSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            pageId = UUIDUtil.getUUID();
            HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
            hexagonPageEntity.setId(pageId);
            hexagonPageEntity.setEa(ea);
            hexagonPageEntity.setIsHomepage(1);
            hexagonPageEntity.setCreateBy(userId);
            hexagonPageEntity.setUpdateBy(userId);
            hexagonPageEntity.setName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEMINIAPPNAVBARSERVICEIMPL_157));
            hexagonPageEntity.setHexagonSiteId(hexagonSiteEntity.getId());
            hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            hexagonPageEntity.setCreateTime(new Date());
            hexagonPageEntity.setUpdateTime(new Date());

            boolean updatePageRedisResult = redisManager.setHexagonPageToRedis(pageId, hexagonPageEntity);
            if (!updatePageRedisResult) {
                log.error("HexagonService.editSite insert page redis failed, entity={}", hexagonPageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            int insertPageResult = hexagonPageDAO.insert(hexagonPageEntity);
            if (insertPageResult != 1) {
                log.error("HexagonService.editSite insert page failed, entity={}", hexagonPageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            if (StringUtils.isNotBlank(arg.getTemplateSiteId())) {
                hexagonTemplateSiteDAO.addTemplateSiteUseCount(arg.getTemplateSiteId());
            }

            if (StringUtils.isNotEmpty(arg.getGroupId())){
                ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
                if (objectGroupEntity != null){
                    ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(ea, id);
                    if (objectGroupRelationEntity == null){
                        ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
                        newEntity.setId(UUIDUtil.getUUID());
                        newEntity.setEa(ea);
                        newEntity.setGroupId(arg.getGroupId());
                        newEntity.setObjectId(hexagonSiteEntity.getId());
                        newEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                        objectGroupRelationDAO.insert(newEntity);
                    }else {
                        objectGroupRelationDAO.updateObjectGroup(ea, arg.getGroupId(), arg.getId(), ObjectTypeEnum.HEXAGON_SITE.getType());
                    }
                }
            }
            //新增微页面-发送到神策统计系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, userId, ObjectTypeEnum.HEXAGON_SITE.getType(), null, id));
        }

        if (arg.isSystem()){
            hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, id, arg.isSystem());
        }

        Map<String, String> formMap = new HashMap<>();
        List<String> formIds = new ArrayList<>();
        List<HexagonSiteListDTO> hexagonSiteFormListDTOList = hexagonSiteDAO.getFormBySiteIds(Arrays.asList(id));
        if (null != hexagonSiteFormListDTOList && CollectionUtils.isNotEmpty(hexagonSiteFormListDTOList)) {
            for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteFormListDTOList) {
                if (StringUtils.isNotBlank(hexagonSiteListDTO.getFormId())) {
                    formIds.add(hexagonSiteListDTO.getFormId());
                }
                formMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getFormId());
            }
        }

        Map<String, Boolean> formMappingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(formIds)) {
            formMappingMap = crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(ea, formIds);
        }

        // 绑定官网
        if (arg.getNeedBindWebSite()) {
            List<HexagonOfficialWebsiteEntity> hexagonOfficialWebsiteEntityList = hexagonOfficialWebsiteDAO.getWebsiteHexagonSiteInfoByEa(ea);
            if (CollectionUtils.isEmpty(hexagonOfficialWebsiteEntityList)) {
                HexagonOfficialWebsiteEntity hexagonOfficialWebsiteEntity = new HexagonOfficialWebsiteEntity();
                hexagonOfficialWebsiteEntity.setId(UUIDUtil.getUUID());
                hexagonOfficialWebsiteEntity.setEa(ea);
                hexagonOfficialWebsiteEntity.setHexagonSiteId(id);
                hexagonOfficialWebsiteEntity.setStatus(HexagonOfficialWebsiteStatusEnum.Enable.getValue());
                hexagonOfficialWebsiteDAO.insertHexagonOfficialWebsite(hexagonOfficialWebsiteEntity);
                hexagonSiteDAO.markAsSystemSite(id);
            }
        }

        CreateSiteResult result = new CreateSiteResult();
        result.setId(id);
        result.setPageId(pageId);

        if (null != formMap) {
            String formId = formMap.get(id);
            if (StringUtils.isNotBlank(formId)) {
                result.setFormId(formId);
                if (null != formMappingMap) {
                    result.setHadCrmMapping(formMappingMap.get(formId));
                } else {
                    result.setHadCrmMapping(false);
                }
            }
        }

        //创建微页面预览二维码
        /*
        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                hexagonManager.getHexagonSiteQRCodeURL(result.getId(), ea, null);
            }
        } );
        */

        return Result.newSuccess(result);
    }

    /**
     * 内容营销下复制站点
     *
     * @param ea
     * @param userId
     * @param arg
     * @return
     */
    @Override
    @Transactional
    public Result<CreateSiteResult> marketingCopySite(String ea, Integer userId, MarketingCopyArg arg, boolean checkName) {
        //保存之前的站点ID
        String preSiteId = arg.getId();

        if (checkName) {
            if (hexagonSiteDAO.queryCountByName(ea, arg.getName()) > 0) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NAME_EXIST);
            }
        }

        //参数转换
        HexagonCopyArg hexagonCopyArg = BeanUtil.copy(arg, HexagonCopyArg.class);
        Result<CreateSiteResult> createSiteResult = hexagonManager.copySite(ea, userId, hexagonCopyArg, HexagonManager.COPY_FROM_HEXAGON);
        CreateSiteResult siteResult = BeanUtil.copy(createSiteResult.getData(), CreateSiteResult.class);
        String siteId = createSiteResult.getData().getId();


        //关联市场活动 传递之前的站点ID
        Result result = bindMarketing(ea, userId, ObjectTypeEnum.HEXAGON_SITE.getType(), preSiteId, siteId, arg.getMarketingEventId());
        if (result.getErrCode() != 0) {
            log.warn("hexagonService copySite bindMarketing failed,result:{}", result);
            return Result.newError(SHErrorCode.HEXAGON_SITE_BIND_MARKETING);
        }

        if (StringUtils.isNotEmpty(arg.getMarketingEventId())){
            hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, siteId, true);
        }
        //检验表单映射关系
        Result<CreateSiteResult> formResult = hexagonManager.formMapping(siteResult, ea);
        siteResult.setFormId(formResult.getData().getFormId());
        siteResult.setHadCrmMapping(formResult.getData().getHadCrmMapping());
        return Result.newSuccess(siteResult);
    }

    /**
     * 微页面复制站点
     *
     * @param ea
     * @param userId
     * @param arg
     * @return
     */
    @Override
    @Transactional
    public Result<CreateSiteResult> hexagonCopySite(String ea, Integer userId, HexagonCopyArg arg) {
        //调用统一复制站点
        HexagonSiteEntity entity = hexagonSiteDAO.getById(arg.getId());
        if (entity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        if (!entity.isSystemSite()) {
            if (hexagonSiteDAO.queryCountByName(ea, arg.getName()) > 0) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NAME_EXIST);
            }
        }
        Result<CreateSiteResult>  result = hexagonManager.hexagonCopySite(ea, userId, arg, HexagonManager.COPY_FROM_HEXAGON);
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_SITE.getType(), entity.getName(),
                OperateTypeEnum.COPY, MaterialTypeEnum.HEXAGON_SITE.getName(), arg.getName());
        if(StringUtils.isNotBlank(arg.getGroupId()) && result.getData()!=null){
            ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
            if (objectGroupEntity != null){
                ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(ea, result.getData().getId());
                if (objectGroupRelationEntity == null){
                    ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
                    newEntity.setId(UUIDUtil.getUUID());
                    newEntity.setEa(ea);
                    newEntity.setGroupId(arg.getGroupId());
                    newEntity.setObjectId(result.getData().getId());
                    newEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                    objectGroupRelationDAO.insert(newEntity);
                }
            }
        }
        return result;
    }









    /**
     * 获取指定物料的关联市场活动
     *
     * @param ea
     * @param userId
     * @param objectType
     * @param preObjectId
     * @param newObjectId
     * @return
     */
    @Override
    public Result bindMarketing(String ea, Integer userId, Integer objectType, String preObjectId, String newObjectId, String marketingId) {
        AddMaterialArg materialArg = new AddMaterialArg();
        materialArg.setMarketingEventId(marketingId);
        materialArg.setObjectId(newObjectId);
        materialArg.setObjectType(objectType);
        Result<String> stringResult = marketingEventService.addMaterial(ea, userId, materialArg);
        if (stringResult.getErrCode() != 0) {
            log.warn("HexagonServiceImpl copySite bindMarketing failed,AddMaterialArg:{}", materialArg);
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<QueryEnterpriseCommerceInfoResult>> queryEnterpriseCommerceInfo(String objectId, Integer objectType, String keyword){
        String url = "http://" + dnsIndustry + "/industry/industryFcpService/getCompanyByName";
        log.info("url:");
        String ea = objectManager.getObjectEa(objectId, objectType);
        if (ea == null){
            return Result.newSuccess();
        }

        Map<String, String> data = new HashMap<>();
        data.put("KeyWord", keyword);
        OkHttpClient client = new OkHttpClient();
        okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(null, GsonUtil.getGson().toJson(data));
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .addHeader("X-fs-Enterprise-Id", String.valueOf(eieaConverter.enterpriseAccountToId(ea)))
                .addHeader("X-fs-Enterprise-Account", ea)
                .addHeader("X-fs-Employee-Id", "-10000")
                .addHeader("Cache-Control", "no-cache")
                .post(requestBody)
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();
            if (response.code() != 200) {
                log.info("queryEnterpriseCommerceInfo error:{}", response.code());
                return Result.newSuccess();
            }

            String strResult = response.body().string();
            log.info("strResult:{}", strResult);
            EnterpriseCommerceData enterpriseCommerceData = GsonUtil.getGson().fromJson(strResult, EnterpriseCommerceData.class);
            if (enterpriseCommerceData == null || CollectionUtils.isEmpty(enterpriseCommerceData.getCompanyEsObjects())){
                return Result.newSuccess();
            }
            List<QueryEnterpriseCommerceInfoResult> resultList = Lists.newArrayList();
            for (EnterpriseCommerceData.EnterpriseCommerce enterpriseCommerce : enterpriseCommerceData.getCompanyEsObjects()){
                //过滤企业名称为无
                if (Objects.equals(enterpriseCommerce.getName(),"无")) {
                    continue;
                }
                QueryEnterpriseCommerceInfoResult info = new QueryEnterpriseCommerceInfoResult();
                info.setCretditCode(enterpriseCommerce.getCretditCode());
                info.setKeyNo(enterpriseCommerce.getKeyNo());
                info.setName(enterpriseCommerce.getName());
                info.setOperName(enterpriseCommerce.getOperName());
                info.setStatus(enterpriseCommerce.getStatus());
                resultList.add(info);
            }
            return Result.newSuccess(resultList);
        } catch (IOException e) {
            log.info("queryEnterpriseCommerceInfo exception ea:{} e:", ea, e);
            return Result.newSuccess();
        }
    }

    @Override
    public Result<GetContentCenterInfoResult> getContentCenterInfo(String ea, Integer fsUserId, boolean sync) {
        GetContentCenterInfoResult result = new GetContentCenterInfoResult();

        List<HexagonContentCenterEntity> hexagonContentCenterEntityList = hexagonContentCenterDAO.getContentCenterHexagonSiteInfoByEa(ea);
        if (CollectionUtils.isNotEmpty(hexagonContentCenterEntityList)){
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonContentCenterEntityList.get(0).getHexagonSiteId());
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
            if (fsEmployeeMsg != null) {
                result.setCreateName(fsEmployeeMsg.getName());
            }
            result.setSiteId(hexagonContentCenterEntityList.get(0).getHexagonSiteId());
            result.setOpenContentCenter(1);
            result.setCreateTime(hexagonContentCenterEntityList.get(0).getCreateTime().getTime());
            return Result.newSuccess(result);
        }

        if (!sync){
            result.setOpenContentCenter(0);
            return Result.newSuccess(result);
        }

        //生成企业内容中心微页面
        String contentCenterHexagonSiteModeId = contentCenterHexagonId;
        HexagonCopyArg arg = new HexagonCopyArg();
        arg.setId(contentCenterHexagonSiteModeId);
        arg.setName(I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_714));
        if (fsUserId == null){
            fsUserId = -10000;
        }
        Result<CreateSiteResult> createSiteResultResult = hexagonCopySite(ea, fsUserId, arg);
        if (!createSiteResultResult.isSuccess()){
            log.warn("HexagonServiceImpl.getContentCenterInfo copy failed");
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_LIST_FAILED);
        }

        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(createSiteResultResult.getData().getId());
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, hexagonSiteEntity.getId(), true);

        HexagonContentCenterEntity contentCenterEntity = new HexagonContentCenterEntity();
        contentCenterEntity.setId(UUIDUtil.getUUID());
        contentCenterEntity.setEa(ea);
        contentCenterEntity.setHexagonSiteId(hexagonSiteEntity.getId());
        contentCenterEntity.setStatus(0);
        hexagonContentCenterDAO.insertHexagonContentCenter(contentCenterEntity);

        result.setSiteId(hexagonSiteEntity.getId());
        result.setOpenContentCenter(1);
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
        FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
        if (fsEmployeeMsg != null) {
            result.setCreateName(fsEmployeeMsg.getName());
        }
        result.setCreateTime(hexagonSiteEntity.getCreateTime().getTime());

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetContentCenterInfoResult> getContentCenterInfoByObjectId(String objectId, Integer objectType) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        if (ea == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        return getContentCenterInfo(ea, null, false);
    }

    @Override
    public Result<GetActivityCenterInfoResult> getActivityCenterInfo(String ea, Integer fsUserId, boolean sync) {
        GetActivityCenterInfoResult result = new GetActivityCenterInfoResult();
        List<HexagonActivityCenterEntity> hexagonActivityCenterEntityList = hexagonActivityCenterDAO.getActivityCenterHexagonSiteInfoByEa(ea);
        if (CollectionUtils.isNotEmpty(hexagonActivityCenterEntityList)){
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonActivityCenterEntityList.get(0).getHexagonSiteId());
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
            if (fsEmployeeMsg != null) {
                result.setCreateName(fsEmployeeMsg.getName());
            }
            result.setSiteId(hexagonActivityCenterEntityList.get(0).getHexagonSiteId());
            result.setOpenContentCenter(1);
            result.setCreateTime(hexagonActivityCenterEntityList.get(0).getCreateTime().getTime());
            return Result.newSuccess(result);
        }

        if (!sync){
            result.setOpenContentCenter(0);
            return Result.newSuccess(result);
        }

        //生成企业内容中心微页面
        HexagonCopyArg arg = new HexagonCopyArg();
        arg.setId(activityCenterHexagonId);
        arg.setName(I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_784));
        if (fsUserId == null){
            fsUserId = -10000;
        }
        Result<CreateSiteResult> createSiteResultResult = hexagonCopySite(ea, fsUserId, arg);
        if (!createSiteResultResult.isSuccess()){
            log.warn("HexagonServiceImpl.getActivityCenterInfo copy failed");
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_LIST_FAILED);
        }

        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(createSiteResultResult.getData().getId());
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, hexagonSiteEntity.getId(), true);
        HexagonActivityCenterEntity activityCenterEntity = new HexagonActivityCenterEntity();
        activityCenterEntity.setId(UUIDUtil.getUUID());
        activityCenterEntity.setEa(ea);
        activityCenterEntity.setHexagonSiteId(hexagonSiteEntity.getId());
        activityCenterEntity.setStatus(0);
        hexagonActivityCenterDAO.insertHexagonActivityCenter(activityCenterEntity);
        
        result.setSiteId(hexagonSiteEntity.getId());
        result.setOpenContentCenter(1);
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
        FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
        if (fsEmployeeMsg != null) {
            result.setCreateName(fsEmployeeMsg.getName());
        }
        result.setCreateTime(hexagonSiteEntity.getCreateTime().getTime());

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetActivityCenterInfoResult> getActivityCenterInfoByObjectId(String objectId, Integer objectType) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        if (ea == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        return getActivityCenterInfo(ea, null, false);
    }

    @Override
    public Result<GetProductSpreadInfoResult> getProductSpreadInfo(String ea, Integer fsUserId, boolean sync) {
        GetProductSpreadInfoResult result = new GetProductSpreadInfoResult();

        List<HexagonProductSpreadEntity> hexagonProductSpreadEntityList = hexagonProductSpreadDAO.getProductSpreadHexagonSiteInfoByEa(ea);
        if (CollectionUtils.isNotEmpty(hexagonProductSpreadEntityList)){
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonProductSpreadEntityList.get(0).getHexagonSiteId());
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
            if (fsEmployeeMsg != null) {
                result.setCreateName(fsEmployeeMsg.getName());
            }
            result.setSiteId(hexagonProductSpreadEntityList.get(0).getHexagonSiteId());
            result.setOpenContentCenter(1);
            result.setCreateTime(hexagonProductSpreadEntityList.get(0).getCreateTime().getTime());
            return Result.newSuccess(result);
        }

        if (!sync){
            result.setOpenContentCenter(0);
            return Result.newSuccess(result);
        }

        //生成企业产品推广微页面
        String contentCenterHexagonSiteModeId = productSpreadHexagonId;
        HexagonCopyArg arg = new HexagonCopyArg();
        arg.setId(contentCenterHexagonSiteModeId);
        arg.setName(I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_855));
        if (fsUserId == null){
            fsUserId = -10000;
        }
        Result<CreateSiteResult> createSiteResultResult = hexagonCopySite(ea, fsUserId, arg);
        if (!createSiteResultResult.isSuccess()){
            log.warn("HexagonServiceImpl -> getProductSpreadConfig copy failed");
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_LIST_FAILED);
        }

        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(createSiteResultResult.getData().getId());
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, hexagonSiteEntity.getId(), true);

        HexagonProductSpreadEntity hexagonProductSpreadEntity = new HexagonProductSpreadEntity();
        hexagonProductSpreadEntity.setId(UUIDUtil.getUUID());
        hexagonProductSpreadEntity.setEa(ea);
        hexagonProductSpreadEntity.setHexagonSiteId(hexagonSiteEntity.getId());
        hexagonProductSpreadEntity.setStatus(0);
        hexagonProductSpreadDAO.insertHexagonProductSpread(hexagonProductSpreadEntity);

        result.setSiteId(hexagonSiteEntity.getId());
        result.setOpenContentCenter(1);
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
        FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
        if (fsEmployeeMsg != null) {
            result.setCreateName(fsEmployeeMsg.getName());
        }
        result.setCreateTime(hexagonSiteEntity.getCreateTime().getTime());

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetProductSpreadInfoResult> getProductSpreadInfoByObjectId(String objectId, Integer objectType) {
        String ea = objectManager.getObjectEa(objectId, objectType);
        if (ea == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        return getProductSpreadInfo(ea, null, false);
    }

    @Override
    public Result addHexagonVisitTag(String ea, Integer fsUserId, AddHexagonVisitTagArg arg) {
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(arg.getHexagonSiteId());
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        objectTagManager.addOrUpdateObjectTag(ea, fsUserId, arg.getHexagonSiteId(), ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getTagNameList());
        if (CollectionUtils.isNotEmpty(arg.getTagNameList())) {
            String tagName = arg.getTagNameList().stream().map(TagName::getCombineName).collect(Collectors.joining("、"));
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteEntity.getName(), OperateTypeEnum.SET_TAG, tagName);

        }
        return Result.newSuccess();
    }

    @Override
    public Result<TagNameList> queryHexagonVisitTag(String ea, Integer fsUserId, String hexagonSiteId) {
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonSiteId);
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        ObjectTagEntity tagEntity = objectTagManager.queryObjectTag(ea, hexagonSiteId, ObjectTypeEnum.HEXAGON_SITE.getType());
        if (tagEntity == null || CollectionUtils.isEmpty(tagEntity.getTagNameList())){
            return Result.newSuccess();
        }

        return Result.newSuccess(tagEntity.getTagNameList());
    }

    @Override
    public Result<GetMiniAppSiteSpreadInfoResult> getMiniAppSiteSpreadInfo(String ea, Integer fsUserId, Integer type, boolean sync) {
        GetMiniAppSiteSpreadInfoResult result = new GetMiniAppSiteSpreadInfoResult();
//        List<HexagonMiniAppSiteSpreadEntity> hexagonMiniAppSiteSpreadEntityList = hexagonMiniAppSiteSpreadDao.getHexagonMiniAppSiteSpreadInfoByEa(ea, type);
//        if (CollectionUtils.isNotEmpty(hexagonMiniAppSiteSpreadEntityList)){
//            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonMiniAppSiteSpreadEntityList.get(0).getHexagonSiteId());
//            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
//            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
//            if (fsEmployeeMsg != null) {
//                result.setCreateName(fsEmployeeMsg.getName());
//            }
//            result.setSiteId(hexagonMiniAppSiteSpreadEntityList.get(0).getHexagonSiteId());
//            result.setOpenContentCenter(1);
//            result.setCreateTime(hexagonMiniAppSiteSpreadEntityList.get(0).getCreateTime().getTime());
//            return Result.newSuccess(result);
//        }
//
//        if (!sync){
//            result.setOpenContentCenter(0);
//            return Result.newSuccess(result);
//        }
//
//        //生成产品推广或公司动态微页面
//        HexagonCopyArg arg = new HexagonCopyArg();
//        arg.setId(Objects.equals(MiniAppSiteSpreadInfoEnum.PRODUCT_SPREAD.getType(), type) ? productSpreadHexagonId : enterpriseDynamicsHexagonId);
//        arg.setName(Objects.equals(MiniAppSiteSpreadInfoEnum.PRODUCT_SPREAD.getType(), type) ? "产品推广" : "公司动态");
//        if (fsUserId == null){
//            fsUserId = -10000;
//        }
//        Result<CreateSiteResult> createSiteResultResult = hexagonCopySite(ea, fsUserId, arg);
//        if (!createSiteResultResult.isSuccess()){
//            log.warn("HexagonServiceImpl.getActivityCenterInfo copy failed");
//            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_LIST_FAILED);
//        }
//
//        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(createSiteResultResult.getData().getId());
//        if (hexagonSiteEntity == null){
//            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
//        }
//        hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, hexagonSiteEntity.getId(), true);
//        HexagonMiniAppSiteSpreadEntity miniappSiteSpreadEntity = new HexagonMiniAppSiteSpreadEntity();
//        miniappSiteSpreadEntity.setId(UUIDUtil.getUUID());
//        miniappSiteSpreadEntity.setEa(ea);
//        miniappSiteSpreadEntity.setHexagonSiteId(hexagonSiteEntity.getId());
//        miniappSiteSpreadEntity.setStatus(0);
//        miniappSiteSpreadEntity.setType(type);
//        hexagonMiniAppSiteSpreadDao.insertHexagonMiniAppSiteSpreadInfo(miniappSiteSpreadEntity);
//
//        result.setSiteId(hexagonSiteEntity.getId());
//        result.setOpenContentCenter(1);
//        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
//        FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
//        if (fsEmployeeMsg != null) {
//            result.setCreateName(fsEmployeeMsg.getName());
//        }
//        result.setCreateTime(hexagonSiteEntity.getCreateTime().getTime());
        return Result.newSuccess(result);
    }

    @Override
    public Result deleteSite(String ea, Integer fsUserId, String id) {
        Preconditions.checkArgument(id != null, I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_992));
        //判断是否为通用介绍页，通用介绍页不允许删除
        EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
        if (enterpriseMetaConfigEntity != null && id.equals(enterpriseMetaConfigEntity.getMiniappIntroductionSiteId())) {
            log.warn("通用介绍页，通用介绍页不允许删除 ea{} fsUserId:{} id:{}", ea, fsUserId, id);
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_997));
        }

        HexagonSiteEntity queryHexagonSiteEntity = hexagonSiteDAO.getById(id);
        if (null == queryHexagonSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        if (!queryHexagonSiteEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        hexagonSiteDAOManager.deleteById(ea, id);
        redisManager.deleteHexgonSite(id);
        objectGroupRelationDAO.deleteObjectFromObjectGroupRelation(ea, id, ObjectTypeEnum.HEXAGON_SITE.getType());
        List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getByHexagonSiteId(id);
        if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
            for (HexagonPageEntity hexagonPageEntity : hexagonPageEntityList) {
                customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(hexagonPageEntity.getId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, fsUserId);
            }
            //删除cta关联关系
            ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.HEXAGON_PAGE.getType(),
                    hexagonPageEntityList.stream().map(HexagonPageEntity::getId).collect(Collectors.toList()));
            hexagonPageDAO.deleteBySiteId(id);
        }
        // 删除对应官网数据
        hexagonOfficialWebsiteDAO.deleteOfficialWebsiteByHexagonSiteIdAndEa(ea, id);
        // 删除官网关联微页面数据
        objectBindDetailDAO.deleteObjectBindDetailByTarget(ea, id, ObjectTypeEnum.HEXAGON_SITE.getType());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.HEXAGON_SITE.getType(), Collections.singletonList(id));
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), queryHexagonSiteEntity.getName(), OperateTypeEnum.DELETE);
        return Result.newSuccess();
    }

    @Override
    public Result changeSiteStatus(String ea, Integer fsUserId, String id, Integer status) {
        HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(status);
        HexagonSiteEntity queryHexagonSiteEntity = hexagonSiteDAO.getById(id);
        if (null == queryHexagonSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        if (!queryHexagonSiteEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        HexagonStatusEnum queryHexagonStatusEnum = HexagonStatusEnum.getByType(queryHexagonSiteEntity.getStatus());
        if (!HexagonStatusEnum.validityCheck(queryHexagonStatusEnum, hexagonStatusEnum)) {
            return Result.newError(SHErrorCode.HEXAGON_STATUS_CHANGE_FAILED);
        }

        queryHexagonSiteEntity.setStatus(status);
        boolean updateRedisResult = redisManager.setHexagonSiteToRedis(queryHexagonSiteEntity.getId(), queryHexagonSiteEntity);
        if (!updateRedisResult) {
            log.error("HexagonService.editSite update site redis failed, entity={}", queryHexagonSiteEntity);
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }

        hexagonSiteDAO.updateStatus(status, id);
        OperateTypeEnum operateType = null;
        if (status == HexagonStatusEnum.STOPED.getType()) {
            operateType = OperateTypeEnum.SET_TO_STOP;
        } else if (status == HexagonStatusEnum.NORMAL.getType()) {
            operateType = OperateTypeEnum.SET_TO_START;
        }
        if (operateType != null) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), queryHexagonSiteEntity.getName(), operateType);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<GetSiteByEaUnitResult>> getSiteByEa(String ea, Integer userId, Integer pageSize, Integer pageNum, Long time, String searchFitter, Integer statusFitter, Boolean excludeSystemPage) {
        time = (time == null ? new Date().getTime() : time);
        Page page = new Page(pageNum, pageSize, true);
        if (excludeSystemPage == null) {
            excludeSystemPage = true;
        }

        List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.getByEa(ea, page, searchFitter, statusFitter, BooleanUtils.isTrue(excludeSystemPage));
        List<GetSiteByEaUnitResult> resultList = hexagonManager.getGetSiteByEaUnitResults(ea, userId, hexagonSiteEntityList);

        PageResult<GetSiteByEaUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<GetSiteByEaUnitResult> getSiteById(String ea, Integer userId, String id) {
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(id);
        if (null == hexagonSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        if (!hexagonSiteEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        List<HexagonSiteEntity> hexagonSiteEntityList = new ArrayList<>();
        hexagonSiteEntityList.add(hexagonSiteEntity);
        List<GetSiteByEaUnitResult> resultList = hexagonManager.getGetSiteByEaUnitResults(ea, userId, hexagonSiteEntityList);
        if (CollectionUtils.isEmpty(resultList)) {
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }

        GetSiteByEaUnitResult result = resultList.get(0);

        //CTA微页面侧滑内容下钻
        ActionDurationTimeObjectArg durationTimeObjectArg = new ActionDurationTimeObjectArg();
        durationTimeObjectArg.setEa(ea);
        durationTimeObjectArg.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
        durationTimeObjectArg.setObjectIdList(Lists.newArrayList(result.getId()));
        durationTimeObjectArg.setActionType(NewActionTypeEnum.LOOK_UP.getActionType());
        com.facishare.marketing.statistic.common.result.Result<List<ActionDurationTimeAvgByObjectResult>> listActionDurationTimeAvgListByObject = userMarketingStatisticService.listActionDurationTimeAvgListByObject(durationTimeObjectArg);
        Map<String, String> objectAvgTimeMap = Maps.newHashMap();
        Map<String, Integer> marketingObjectActivityLookUpDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(listActionDurationTimeAvgListByObject.getData())){
            objectAvgTimeMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId, o-> TimeMeasureUtil.getSecondTime(Long.valueOf(o.getAvg())),(v1, v2)->v2));
            marketingObjectActivityLookUpDTOMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId,ActionDurationTimeAvgByObjectResult::getCount,(v1, v2)->v2));
        }
        if (objectAvgTimeMap == null || objectAvgTimeMap.get(result.getId()) == null){
            result.setActionDurationTimeAvg(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ARTICLESERVICEIMPL_703));
        }else {
            result.setActionDurationTimeAvg(objectAvgTimeMap.get(result.getId()));
        }
        if (marketingObjectActivityLookUpDTOMap == null || marketingObjectActivityLookUpDTOMap.get(result.getId()) == null){
            result.setObjectLookUpCount(0);
        }else {
            result.setObjectLookUpCount(marketingObjectActivityLookUpDTOMap.get(result.getId()));
        }
        result.setUpdateMemberInfoSite(false);
        result.setSystemSite(hexagonSiteEntity.isSystemSite());
        MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
        if (memberConfig != null && result.getId().equals(memberConfig.getUpdateInfoSiteId())) {
            result.setUpdateMemberInfoSite(true);
        }

        return Result.newSuccess(resultList.get(0));
    }

    @Override
    public Result<SitePreviewResult> sitePreview(String ea, SitePreviewArg arg) {
        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(arg.getType());
        switch (hexagonPreviewEnum) {
            case SITE_PREVIEW:
                HexagonSiteEntity queryHexagonSiteEntity = hexagonSiteDAO.getById(arg.getId());
                if (null == queryHexagonSiteEntity) {
                    return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
                }

                if (!queryHexagonSiteEntity.getEa().equals(ea)) {
                    return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
                }

                return hexagonManager.getHexagonSiteQRCodeURL(arg.getId(), ea, arg.getChannelValue(), arg.getExtraParam());
            case TEMPLATE_SITE:
                HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getId());
                if (null == queryHexagonTemplateSiteEntity) {
                    return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
                }

                if (queryHexagonTemplateSiteEntity.getType() == 2) {
                    if (!queryHexagonTemplateSiteEntity.getEa().equals(ea)) {
                        return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
                    }
                }

                return hexagonManager.getHexagonTemplateSiteQRCodeURL(arg.getId(), ea);
        }
        return Result.newError(SHErrorCode.PARAMS_ERROR);
    }

    @Override
    public Result<CreatePageResult> editPage(String ea, Integer userId, CreatePageArg arg) {
        String sharePicH5APath = null;
        String sharePicMpAPath = null;
        HexagonPageEntity hexagonPageEntity = null;
        String siteId = null;
        if (StringUtils.isNotBlank(arg.getShareUrl())) {
            String apath;
            //如果是cdn地址
            if(arg.getShareUrl().contains(cdnSharePath)){
                byte[] bytes = fileV2Manager.downloadFileByUrl(arg.getShareUrl(), null);
//                apath = fileV2Manager.uploadToApath(bytes, "jpg", null);
                apath = fileV2Manager.uploadToCpathOrNpath(ea,bytes, true, null);
            }else {
                apath = fileV2Manager.getApathByUrl(arg.getShareUrl());
                apath = fileV2Manager.copyAPathToCPath(ea,userId,apath).get();
//
//                    if (apath.startsWith("TA_")) {
//                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(apath, ea, userId);
//                    if (null != fileManagerPicResult && StringUtils.isNotBlank(fileManagerPicResult.getUrlAPath())) {
//                        apath = fileManagerPicResult.getUrlAPath();
//                    }
//                }
            }
            sharePicH5APath = apath;
            sharePicMpAPath = apath;
        }

//        String lastTimeHomeFormId = null;
        String id = arg.getId();
        if (StringUtils.isNotBlank(arg.getId())) {
            HexagonPageEntity queryHexagonPageEntity = hexagonPageDAO.getById(arg.getId());
            if (null == queryHexagonPageEntity) {
                return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
            }

            siteId = queryHexagonPageEntity.getHexagonSiteId();

            if (!queryHexagonPageEntity.getEa().equals(ea)) {
                return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
            }

            if (null != arg.getStatus()) {
                HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
                HexagonStatusEnum queryHexagonStatusEnum = HexagonStatusEnum.getByType(queryHexagonPageEntity.getStatus());
                if (null != hexagonStatusEnum && null != queryHexagonStatusEnum) {
                    if (!HexagonStatusEnum.validityCheck(queryHexagonStatusEnum, hexagonStatusEnum)) {
                        return Result.newError(SHErrorCode.HEXAGON_STATUS_CHANGE_FAILED);
                    }
                }
            }

//            List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(queryHexagonPageEntity.getHexagonSiteId()));
//            if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList)) {
//                lastTimeHomeFormId = hexagonSiteListDTOList.get(0).getFormId();
//            }

            hexagonPageEntity = queryHexagonPageEntity;
//            hexagonPageEntity.setName(StringUtils.isNotBlank(arg.getName()) ? arg.getName() : queryHexagonPageEntity.getName());
            //页面标题
            hexagonPageEntity.setName(arg.getName());
            //分享标题
            hexagonPageEntity.setShareTitle(arg.getShareTitle());
            //分享描述
            hexagonPageEntity.setShareDesc(arg.getShareDesc());
            hexagonPageEntity.setSharePicH5Apath(sharePicH5APath);
            hexagonPageEntity.setSharePicMpApath(sharePicMpAPath);
            hexagonPageEntity.setContent(arg.getContent());
            hexagonPageEntity.setIsHomepage(arg.getIsHomepage());
            hexagonPageEntity.setFormId(arg.getFormId()) ;
            if (arg.getStatus() != null) {
                hexagonPageEntity.setStatus(arg.getStatus());
            }
            hexagonPageEntity.setUpdateBy(userId);
            hexagonPageEntity.setUpdateTime(new Date());

            boolean updatePageRedisResult = redisManager.setHexagonPageToRedis(hexagonPageEntity.getId(), hexagonPageEntity);
            if (!updatePageRedisResult) {
                log.error("HexagonService.editPage update redis failed, id={}", id);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            int updateResult = hexagonPageDAO.update(hexagonPageEntity);
            if (updateResult != 1) {
                log.error("HexagonService.editPage update failed, entity={}", hexagonPageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            materialRelationDaoManager.insertOrUpdateMaterialRelationEntity(arg.getId(),ObjectTypeEnum.HEXAGON_PAGE.getType(),arg.getSharePosterAPath(),ea);
            hexagonPageDAO.updatePageSharePic(hexagonPageEntity);
//            mktContentMgmtLogObjManager.createByOperateType(ea, userId, ObjectTypeEnum.HEXAGON_PAGE.getType(), id, OperateTypeEnum.EDIT);
            if (StringUtils.isNotBlank(arg.getFormId())) {
                if (!arg.getFormId().equals(queryHexagonPageEntity.getFormId())) {
                    customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId);
                }
                customizeFormDataManager.bindCustomizeFormDataObject(arg.getFormId(), id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId, null, null, null);
            } else {
                customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId);
            }
            if(StringUtils.isBlank(arg.getShareUrl())){
                photoManager.deleteByTargetIdAndTypes(id,Lists.newArrayList(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()
                ,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()));
            }

            ctaRelationDaoManager.addCtaRelation(ea, arg.getCtaIds(), ObjectTypeEnum.HEXAGON_PAGE.getType(), id);
        } else {

            HexagonSiteEntity queryHexagonSiteEntity = hexagonSiteDAO.getById(arg.getHexagonSiteId());
            if (null == queryHexagonSiteEntity) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
            }

            siteId = arg.getHexagonSiteId();
            id = UUIDUtil.getUUID();

            hexagonPageEntity = BeanUtil.copy(arg, HexagonPageEntity.class);
            hexagonPageEntity.setId(id);
            hexagonPageEntity.setEa(ea);
            hexagonPageEntity.setCreateBy(userId);
            hexagonPageEntity.setUpdateBy(userId);
            hexagonPageEntity.setSharePicH5Apath(sharePicH5APath);
            hexagonPageEntity.setSharePicMpApath(sharePicMpAPath);
            hexagonPageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            hexagonPageEntity.setCreateTime(new Date());
            hexagonPageEntity.setUpdateTime(new Date());

            if (StringUtils.isNotBlank(arg.getTemplateSiteId())) {
                HexagonTemplateSiteEntity hexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getTemplateSiteId());
                if (null == hexagonTemplateSiteEntity) {
                    return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
                }

                hexagonPageEntity.setContent(hexagonManager.resetContentApath(hexagonPageEntity.getContent(), ea));
                String newShareApath = hexagonManager.resetShareApath(sharePicH5APath, ea);
                hexagonPageEntity.setSharePicH5Apath(newShareApath);
                hexagonPageEntity.setSharePicMpApath(newShareApath);
            }

            boolean updatePageRedisResult = redisManager.setHexagonPageToRedis(hexagonPageEntity.getId(), hexagonPageEntity);
            if (!updatePageRedisResult) {
                log.error("HexagonService.editPage insert redis failed, id={}", id);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            int insertResult = hexagonPageDAO.insert(hexagonPageEntity);
            if (insertResult != 1) {
                log.error("HexagonService.editPage insert failed, entity={}", hexagonPageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            if(!Strings.isNullOrEmpty(arg.getSharePosterAPath())){
                MaterialRelationEntity entity = new MaterialRelationEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setObjectType(ObjectTypeEnum.HEXAGON_PAGE.getType());
                entity.setObjectId(id);
                entity.setSharePosterAPath(arg.getSharePosterAPath());
                materialRelationDaoManager.insertMaterialRelationEntity(entity);
            }
//            mktContentMgmtLogObjManager.createByOperateType(ea, userId, ObjectTypeEnum.HEXAGON_PAGE.getType(), id, OperateTypeEnum.ADD);
            if (StringUtils.isNotBlank(arg.getFormId())) {
                customizeFormDataManager.bindCustomizeFormDataObject(arg.getFormId(), id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId, null, null, null);
            }
            ctaRelationDaoManager.addCtaRelation(ea, arg.getCtaIds(), ObjectTypeEnum.HEXAGON_PAGE.getType(), id);
        }
        if (arg.getIsHomepage() != null && arg.getIsHomepage() == 1) {
            hexagonPageDAO.removeHomePage(arg.getHexagonSiteId());

            boolean updateHomePageRedisResult = redisManager.setHexagonHomePageToRedis(siteId, hexagonPageEntity);
            if (!updateHomePageRedisResult) {
                log.error("HexagonService.editPage setHomePage redis failed, id={}", id);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            int setHomePageResult = hexagonPageDAO.setHomePage(id);
            if (setHomePageResult != 1) {
                log.error("HexagonService.editPage setHomePage failed, id={}", id);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }

        CreatePageResult result = new CreatePageResult();
        result.setId(id);

        if(StringUtils.isNotBlank(arg.getOriginalImageAPath()) && CollectionUtils.isNotEmpty(arg.getCutOffsetList())){
            for (PhotoCutOffset cutOffset : arg.getCutOffsetList()){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, id, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? arg.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, id, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? arg.getOriginalImageAPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, id, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? arg.getOriginalImageAPath() : cutOffset.getPath());
                }
            }
        }

        // 重置会议表单
        //conferenceManager.resetConferenceFormByPage(id, lastTimeHomeFormId);
        return Result.newSuccess(result);
    }

    @Override
    public Result deletePage(String ea, Integer fsUserId, String id) {
        HexagonPageEntity queryHexagonPageEntity = hexagonPageDAO.getById(id);
        if (null == queryHexagonPageEntity) {
            return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
        }

        if (!queryHexagonPageEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        Integer isHomePage = queryHexagonPageEntity.getIsHomepage();

        List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getByHexagonSiteId(queryHexagonPageEntity.getHexagonSiteId());
        List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(queryHexagonPageEntity.getHexagonSiteId()));
        String lastTimeHomeFormId = null;
        if (CollectionUtils.isNotEmpty(hexagonSiteListDTOList)) {
            lastTimeHomeFormId = hexagonSiteListDTOList.get(0).getFormId();
        }
        if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
            if (hexagonPageEntityList.size() == 1) {
                return Result.newError(SHErrorCode.HEXAGON_PAGE_AT_LEAST_ONE);
            }

            if (isHomePage == 1) {
                for (HexagonPageEntity hexagonPageEntity : hexagonPageEntityList) {
                    if (!hexagonPageEntity.getId().equals(id)) {
                        HexagonPageEntity newHexagonPageEntity = new HexagonPageEntity();
                        newHexagonPageEntity.setId(hexagonPageEntity.getId());
                        newHexagonPageEntity.setIsHomepage(1);
                        int updateResult = hexagonPageDAO.update(newHexagonPageEntity);
                        if (updateResult == 1) {
                            break;
                        }
                    }
                }
            }

            hexagonPageDAO.deleteById(id);
            // 删除表单关联
            customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, fsUserId);
            // 重置会议表单
            conferenceManager.resetConferenceFormByPage(id, lastTimeHomeFormId);
            ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.HEXAGON_PAGE.getType(), Lists.newArrayList(id));
            redisManager.deleteHexgonPage(id);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<List<GetPagesBySiteIdUnitResult>> getPagesBySiteId(String ea, String siteId) {
        HexagonSiteEntity queryHexagonSiteEntity = hexagonSiteDAO.getByIdIngnoreStatus(siteId);
        if (null == queryHexagonSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        if (!queryHexagonSiteEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        List<GetPagesBySiteIdUnitResult> resultList = new ArrayList<>();
        List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getByHexagonSiteId(siteId);
        if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
            List<String> pageIds = new ArrayList<>();
            for (HexagonPageEntity hexagonPageEntity : hexagonPageEntityList) {
                pageIds.add(hexagonPageEntity.getId());
            }

            List<String> apathList = new ArrayList<>();
            for (HexagonPageEntity hexagonPageEntity : hexagonPageEntityList) {
                if (StringUtils.isNotBlank(hexagonPageEntity.getSharePicH5Apath())) {
                    apathList.add(hexagonPageEntity.getSharePicH5Apath());
                }
                if (StringUtils.isNotBlank(hexagonPageEntity.getSharePicMpApath())) {
                    apathList.add(hexagonPageEntity.getSharePicMpApath());
                }
            }

            Map<String, String> urlMap = null;
            if (CollectionUtils.isNotEmpty(apathList)) {
                urlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
            }

            for (HexagonPageEntity hexagonPageEntity : hexagonPageEntityList) {
                GetPagesBySiteIdUnitResult result = BeanUtil.copy(hexagonPageEntity, GetPagesBySiteIdUnitResult.class);
                if (null != urlMap) {
                    result.setSharePicH5Url(urlMap.get(hexagonPageEntity.getSharePicH5Apath()));
                    result.setSharePicMpUrl(urlMap.get(hexagonPageEntity.getSharePicMpApath()));
                }
                result.setCreateTime(hexagonPageEntity.getCreateTime().getTime());
                MaterialRelationEntity relationEntity = materialRelationDao.queryMaterialRelationByObjectId(hexagonPageEntity.getId(), ObjectTypeEnum.HEXAGON_PAGE.getType());
                if(relationEntity!=null&&relationEntity.getSharePosterAPath()!=null){
                    result.setSharePosterAPath(relationEntity.getSharePosterAPath());
                    result.setSharePosterUrl(fileV2Manager.getUrlByPath(relationEntity.getSharePosterAPath(),ea,false));
                }
                if(result != null && StringUtils.isNotBlank(result.getId())){
                    // 获取裁剪封面图
                    PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), result.getId());
                    if (coverCutMiniAppPhotoEntity != null) {
                        result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                    }
                    PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), result.getId());
                    if (coverCutH5PhotoEntity != null) {
                        result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                    }
                    PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), result.getId());
                    if (coverCutOrdinaryPhotoEntity != null) {
                        result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                        //返回原图
                        result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                    }
                }
                resultList.add(result);
            }
        }

        return Result.newSuccess(resultList);
    }

    @Override
    @FilterLog
    public Result<GetPageDetailResult> getHomepageDetailBySiteId(HexagonhomepageDetailArg arg) {
        return hexagonManager.getHomepageDetailBySiteId(arg);
    }

    @Override
    public Result<GetPageDetailResult> getPageDetail(Integer type, String id) {
        GetPageDetailResult result = null;

        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(type);
        if (hexagonPreviewEnum == HexagonPreviewEnum.SITE || hexagonPreviewEnum == HexagonPreviewEnum.SITE_PREVIEW) {
            HexagonPageEntity hexagonPageEntity = redisManager.getHexagonPage(id);
            if (null == hexagonPageEntity) {
                hexagonPageEntity = hexagonPageDAO.getInclueDeletedById(id);
                if (null == hexagonPageEntity) {
                    return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
                } else {
                    HexagonStatusEnum pageHexagonStatusEnum = HexagonStatusEnum.getByType(hexagonPageEntity.getStatus());
                    if (pageHexagonStatusEnum == HexagonStatusEnum.DELETED) {
                        return Result.newError(SHErrorCode.HEXAGON_PAGE_DELETED);
                    }
                    redisManager.setHexagonPageToRedis(id, hexagonPageEntity);
                }
            }

            HexagonStatusEnum pageHexagonStatusEnum = HexagonStatusEnum.getByType(hexagonPageEntity.getStatus());
            if (pageHexagonStatusEnum == HexagonStatusEnum.DELETED) {
                return Result.newError(SHErrorCode.HEXAGON_PAGE_DELETED);
            }

            result = BeanUtil.copy(hexagonPageEntity, GetPageDetailResult.class);

            List<String> apathList = new ArrayList<>();
            if (StringUtils.isNotBlank(hexagonPageEntity.getSharePicH5Apath())) {
                apathList.add(hexagonPageEntity.getSharePicH5Apath());
            }

            if (StringUtils.isNotBlank(hexagonPageEntity.getSharePicMpApath())) {
                apathList.add(hexagonPageEntity.getSharePicMpApath());
            }

            Map<String, String> urlMap = null;
            if (CollectionUtils.isNotEmpty(apathList)) {
                urlMap = fileV2Manager.batchGetUrlByPath(apathList, hexagonPageEntity.getEa(), false);
                result.setSharePicH5Url(urlMap.get(hexagonPageEntity.getSharePicH5Apath()));
                result.setSharePicMpUrl(urlMap.get(hexagonPageEntity.getSharePicMpApath()));
            }

        } else if (hexagonPreviewEnum == HexagonPreviewEnum.TEMPLATE_SITE) {
            HexagonTemplatePageEntity hexagonTemplatePageEntity = hexagonTemplatePageDAO.getById(id);
            if (null == hexagonTemplatePageEntity) {
                return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_PAGE_NOT_FOUND);
            }

            result = BeanUtil.copy(hexagonTemplatePageEntity, GetPageDetailResult.class);

            List<String> apathList = new ArrayList<>();
            if (StringUtils.isNotBlank(hexagonTemplatePageEntity.getSharePicH5Apath())) {
                apathList.add(hexagonTemplatePageEntity.getSharePicH5Apath());
            }

            if (StringUtils.isNotBlank(hexagonTemplatePageEntity.getSharePicMpApath())) {
                apathList.add(hexagonTemplatePageEntity.getSharePicMpApath());
            }

            Map<String, String> urlMap = null;
            if (CollectionUtils.isNotEmpty(apathList)) {
                urlMap = fileV2Manager.batchGetUrlByPath(apathList, hexagonTemplatePageEntity.getEa(), false);
                result.setSharePicH5Url(urlMap.get(hexagonTemplatePageEntity.getSharePicH5Apath()));
                result.setSharePicMpUrl(urlMap.get(hexagonTemplatePageEntity.getSharePicMpApath()));
            }
        }

        if(result != null && result.getFormId() != null){
            result.setFormUsage(customizeFormDataManager.batchGetFormUsageByFormIds(ImmutableSet.of(result.getFormId())).get(result.getFormId()));
        }
        if(result != null && StringUtils.isNotBlank(result.getId())){
            // 获取裁剪封面图
            PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), result.getId());
            if (coverCutMiniAppPhotoEntity != null) {
                result.setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), result.getId());
            if (coverCutH5PhotoEntity != null) {
                result.setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
            }
            PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), result.getId());
            if (coverCutOrdinaryPhotoEntity != null) {
                result.setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                //返回原图
                result.setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
            }
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<CreateTemplateSiteResult> editTemplateEaSite(String ea, Integer userId, CreateTemplateSiteEaArg arg) {
        if (EmptyUtil.isNullForList(ea, userId, arg)) {
            log.warn("HexagonService.editTemplateSite params error");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String id = arg.getId();
        if (StringUtils.isNoneBlank(arg.getId())) {
            HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getId());
            if (null == queryHexagonTemplateSiteEntity) {
                log.warn("HexagonService.editTemplateSite queryHexagonTemplateSiteEntity not found ,id={}", arg.getId());
                return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
            }
            Integer oldStatus = queryHexagonTemplateSiteEntity.getStatus();
            if (!queryHexagonTemplateSiteEntity.getEa().equals(ea)) {
                log.warn("HexagonService.editTemplateSite ea invaild, ea={}, entity.ea={}", ea, queryHexagonTemplateSiteEntity.getEa());
                return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
            }

            boolean isAppAdmin = objectGroupManager.isAppAdmin(ea, userId);
            if (!isAppAdmin && !userId.equals(queryHexagonTemplateSiteEntity.getCreateBy())){
                log.info("HexagonServiceImpl.editTemplateSite only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, userId, arg);
                return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
            }

            String apath = null;
            if (StringUtils.isNotEmpty(arg.getCoverPath())) {
                if (arg.getCoverPath().startsWith("TA_")){
                    FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(apath, ea, userId);
                    if (null != fileManagerPicResult && StringUtils.isNotBlank(fileManagerPicResult.getUrlAPath())) {
                        apath = fileManagerPicResult.getUrlAPath();
                    }
                }else {
                    apath = arg.getCoverPath();
                }
            }

            HexagonTemplateSiteEntity hexagonTemplateSiteEntity = BeanUtil.copy(arg, HexagonTemplateSiteEntity.class);
            hexagonTemplateSiteEntity.setCoverApath(apath);
            int updateResult = hexagonTemplateSiteDAO.update(hexagonTemplateSiteEntity);
            if (updateResult != 1) {
                log.error("HexagonService.editSite update failed, entity={}", hexagonTemplateSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            if (oldStatus == HexagonStatusEnum.NOTVISIBLE.getType()) {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(),
                        arg.getName(), OperateTypeEnum.ADD);
            } else {
                mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(),
                        arg.getName(), OperateTypeEnum.EDIT);
            }
        } else {
            if (arg.getType() == null) {
                arg.setType(HexagonTemplateTypeEnum.CUSOTM.getType());
            }
            Preconditions.checkArgument(HexagonTemplateTypeEnum.isValid(arg.getType()) && !HexagonTemplateTypeEnum.SYSTEM.getType().equals(arg.getType()), "type非法");
            id = UUIDUtil.getUUID();
            if (StringUtils.isBlank(arg.getName())) {
                arg.setName(I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_1625) + id.substring(0, 5));
            }
            HexagonTemplateSiteEntity hexagonTemplateSiteEntity = BeanUtil.copy(arg, HexagonTemplateSiteEntity.class);
            hexagonTemplateSiteEntity.setEa(ea);
            hexagonTemplateSiteEntity.setId(id);
            hexagonTemplateSiteEntity.setCreateBy(userId);
//            if (HexagonTemplateTypeEnum.CONFERENCE.getType().equals(arg.getType()) || HexagonTemplateTypeEnum.LIVE.getType().equals(arg.getType())) {
//                HexagonTemplateSiteEntity marketingTemplate = hexagonTemplateSiteDAO.getMarketingTemplateByEa(ea, arg.getType());
//                Preconditions.checkArgument(marketingTemplate == null, "营销场景模板已存在");
//            }
            int insertResult = hexagonTemplateSiteDAO.insert(hexagonTemplateSiteEntity);
            if (insertResult != 1) {
                log.error("HexagonService.editTemplateSite insert failed, entity={}", hexagonTemplateSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
//            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(),
//                    hexagonTemplateSiteEntity.getName(), OperateTypeEnum.ADD);

            HexagonTemplatePageEntity hexagonTemplatePageEntity = new HexagonTemplatePageEntity();
            hexagonTemplatePageEntity.setId(UUIDUtil.getUUID());
            hexagonTemplatePageEntity.setEa(ea);
            hexagonTemplatePageEntity.setName(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CUSTOMIZEMINIAPPNAVBARSERVICEIMPL_157));
            hexagonTemplatePageEntity.setContent("");
            hexagonTemplatePageEntity.setHexagonTemplateSiteId(id);
            hexagonTemplatePageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            hexagonTemplatePageEntity.setIsHomepage(1);
            hexagonTemplatePageEntity.setCreateBy(userId);
            hexagonTemplatePageEntity.setCreateTime(new Date());
            hexagonTemplatePageEntity.setUpdateTime(new Date());
            int insertPageResult = hexagonTemplatePageDAO.insert(hexagonTemplatePageEntity);
            if (insertPageResult != 1) {
                log.error("HexagonService.editTemplateEaSite insert page failed, entity={}", hexagonTemplatePageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }

            //设置分组
            if (StringUtils.isNotEmpty(arg.getGroupId())){
                ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
                if (objectGroupEntity != null){
                    ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(ea, id);
                    if (objectGroupRelationEntity == null){
                        ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
                        newEntity.setId(UUIDUtil.getUUID());
                        newEntity.setEa(ea);
                        newEntity.setGroupId(arg.getGroupId());
                        newEntity.setObjectId(hexagonTemplateSiteEntity.getId());
                        newEntity.setObjectType(ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
                        objectGroupRelationDAO.insert(newEntity);
                    }else {
                        objectGroupRelationDAO.updateObjectGroup(ea, arg.getGroupId(), arg.getId(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
                    }
                }
            }
        }


        //生成模板预览url
     //   hexagonManager.getHexagonSiteQRCodeURL(id, ea, null);
        CreateTemplateSiteResult result = new CreateTemplateSiteResult();
        result.setId(id);
        return Result.newSuccess(result);
    }

    @Override
    @Transactional
    public Result deleteTemplateSite(String ea, Integer fsUserId, String id) {
        if (EmptyUtil.isNullForList(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(id);
        if (null == queryHexagonTemplateSiteEntity) {
            log.warn("HexagonService.deleteTemplateSite queryHexagonTemplateSiteEntity not found, id={}", id);
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        boolean isAppAdmin = objectGroupManager.isAppAdmin(ea, fsUserId);
        if (!isAppAdmin && !fsUserId.equals(queryHexagonTemplateSiteEntity.getCreateBy())){
            log.info("HexagonServiceImpl.deleteTemplateSite only allow appAdmin ea:{} fsUserId:{}, id:{}", ea, fsUserId, id);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }

        hexagonTemplateSiteDAO.deleteById(id);
        List<HexagonTemplatePageEntity> hexagonTemplatePageEntities = hexagonTemplatePageDAO.getBySiteId(id);
        if(CollectionUtils.isNotEmpty(hexagonTemplatePageEntities)) {
            ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(),
                    hexagonTemplatePageEntities.stream().map(HexagonTemplatePageEntity::getId).collect(Collectors.toList()));
        }
        hexagonTemplatePageDAO.deleteBySiteId(id);
        objectGroupRelationDAO.deleteObjectFromObjectGroupRelation(ea, id, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(),
                queryHexagonTemplateSiteEntity.getName(), OperateTypeEnum.DELETE);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<GetTemplateSiteResult>> getTemplateSite(String ea, Integer category, Integer pageSize, Integer pageNum, Long time, Integer type) {
        time = (time == null ? new Date().getTime() : time);
        Page page = new Page(pageNum, pageSize, true);
        List<GetTemplateSiteResult> resultList = new ArrayList<>();
        List<HexagonTemplateSiteEntity> hexagonTemplateSiteEntityList = hexagonTemplateSiteDAO.getByCategory(category, ea, page, type);

        if (CollectionUtils.isNotEmpty(hexagonTemplateSiteEntityList)) {
            List<String> apathList = new ArrayList<>();
            Map<String, String> coverApathMap = new HashMap<>();

            List<String> templateIds = hexagonTemplateSiteEntityList.stream().map(HexagonTemplateSiteEntity::getId).collect(Collectors.toList());
            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonTemplateSiteDAO.getCoverByTemplateSiteIds(templateIds);
            Map<String, HexagonSiteListDTO> idHexagonTemplateDTOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)){
                idHexagonTemplateDTOMap = hexagonSiteCoverListDTOList.stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, Function.identity(), (v1, v2)->v2));
            }

            for (HexagonTemplateSiteEntity hexagonTemplateSiteEntity : hexagonTemplateSiteEntityList) {
                if (StringUtils.isNotBlank(hexagonTemplateSiteEntity.getCoverApath())) {
                    apathList.add(hexagonTemplateSiteEntity.getCoverApath());
                    coverApathMap.put(hexagonTemplateSiteEntity.getId(), hexagonTemplateSiteEntity.getCoverApath());
                }else {
                    //如果封面图没有，就取分享图
                    if (null != hexagonSiteCoverListDTOList && CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                        if (idHexagonTemplateDTOMap != null && idHexagonTemplateDTOMap.get(hexagonTemplateSiteEntity.getId()) != null){
                            HexagonSiteListDTO dto = idHexagonTemplateDTOMap.get(hexagonTemplateSiteEntity.getId());
                            if (StringUtils.isNotEmpty(dto.getSharePicH5Apath())){
                                apathList.add(dto.getSharePicH5Apath());
                                coverApathMap.put(hexagonTemplateSiteEntity.getId(),dto.getSharePicH5Apath());
                            }
                        }
                    }
                }
            }

            Map<String, String> urlMap = null;
            if (CollectionUtils.isNotEmpty(apathList)) {
                urlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
            }

            for (HexagonTemplateSiteEntity hexagonTemplateSiteEntity : hexagonTemplateSiteEntityList) {
                GetTemplateSiteResult result = BeanUtil.copy(hexagonTemplateSiteEntity, GetTemplateSiteResult.class);
                if (null != urlMap) {
                    result.setCoverUrl(urlMap.get(coverApathMap.get(hexagonTemplateSiteEntity.getId())));
                }
                resultList.add(result);
            }
        }

        PageResult<GetTemplateSiteResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());
        return Result.newSuccess(pageResult);
    }


    @Override
    public Result changeTemplateSiteStatus(Integer status, String id) {
        if (EmptyUtil.isNullForList(status, id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(status);
        if (hexagonStatusEnum == null || hexagonStatusEnum == HexagonStatusEnum.DELETED) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);

        }

        HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(id);
        if (null == queryHexagonTemplateSiteEntity) {
            log.warn("HexagonService.changeTemplateStatus queryHexagonTemplateSiteEntity not found ,id={}", id);
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        int updateTemplateResut = hexagonTemplateSiteDAO.updateTemplateStatus(status, id);
        if (updateTemplateResut != 1) {
            log.error("HexagonService.editTemplateSiteStatus update failed, entity={}", queryHexagonTemplateSiteEntity);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<CreateTemplatePageResult> editTemplatePage(String ea, Integer userId, CreateTemplatePageArg arg) {
        String sharePicH5APath = null;
        String sharePicMpAPath = null;
        HexagonTemplatePageEntity hexagonTemplatePageEntity = null;
        String siteId = null;
        if (StringUtils.isNotBlank(arg.getShareUrl())) {
            String apath;
            //如果是cdn地址
            if(arg.getShareUrl().contains(cdnSharePath)){
                byte[] bytes = fileV2Manager.downloadFileByUrl(arg.getShareUrl(), null);
                apath = fileV2Manager.uploadToCpathOrNpath(ea,bytes, true, null);
            }else {
                apath = fileV2Manager.getApathByUrl(arg.getShareUrl());
                apath = fileV2Manager.copyAPathToCPath(ea,userId,apath).get();
            }
            sharePicH5APath = apath;
            sharePicMpAPath = apath;
        }

        String id = arg.getId();
        if (StringUtils.isNotBlank(arg.getId())) {
            HexagonTemplatePageEntity queryHexagonTemplatePageEntity = hexagonTemplatePageDAO.getById(arg.getId());
            if (null == queryHexagonTemplatePageEntity) {
                return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
            }

            siteId = queryHexagonTemplatePageEntity.getHexagonTemplateSiteId();
            if (!queryHexagonTemplatePageEntity.getEa().equals(ea)) {
                return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
            }

            boolean isAppAdmin = objectGroupManager.isAppAdmin(ea, userId);
            if (!isAppAdmin && !userId.equals(queryHexagonTemplatePageEntity.getCreateBy())){
                log.info("HexagonServiceImpl.editTemplatePage only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, userId, arg);
                return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
            }

            hexagonTemplatePageEntity = queryHexagonTemplatePageEntity;
            hexagonTemplatePageEntity.setName(StringUtils.isNotBlank(arg.getName()) ? arg.getName() : queryHexagonTemplatePageEntity.getName());
            hexagonTemplatePageEntity.setShareTitle(StringUtils.isNotBlank(arg.getShareTitle()) ? arg.getShareTitle() : queryHexagonTemplatePageEntity.getShareTitle());
            hexagonTemplatePageEntity.setShareDesc(StringUtils.isNotBlank(arg.getShareDesc()) ? arg.getShareDesc() : queryHexagonTemplatePageEntity.getShareDesc());
            hexagonTemplatePageEntity.setSharePicH5Apath(sharePicH5APath);
            hexagonTemplatePageEntity.setSharePicMpApath(sharePicMpAPath);
            hexagonTemplatePageEntity.setContent(StringUtils.isNotBlank(arg.getContent()) ? arg.getContent() : queryHexagonTemplatePageEntity.getContent());
            hexagonTemplatePageEntity.setIsHomepage(null != arg.getIsHomepage() ? arg.getIsHomepage() : queryHexagonTemplatePageEntity.getIsHomepage());
            hexagonTemplatePageEntity.setFormId(arg.getFormId()) ;
            hexagonTemplatePageEntity.setStatus(null != arg.getStatus() ? arg.getStatus() : queryHexagonTemplatePageEntity.getStatus());
            hexagonTemplatePageEntity.setUpdateTime(new Date());
            hexagonTemplatePageEntity.setHexagonTemplateSiteId(siteId);

            int updateResult = hexagonTemplatePageDAO.updateV2(hexagonTemplatePageEntity);
            if (updateResult != 1) {
                log.error("HexagonService.editPage update failed, entity={}", hexagonTemplatePageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            hexagonTemplatePageDAO.updatePageSharePic(hexagonTemplatePageEntity);
            //mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_PAGE.getType(), hexagonTemplatePageEntity.getName(), OperateTypeEnum.EDIT);
            if (StringUtils.isNotBlank(arg.getFormId())) {
                if (!arg.getFormId().equals(queryHexagonTemplatePageEntity.getFormId())) {
                    customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId);
                }
                customizeFormDataManager.bindCustomizeFormDataObject(arg.getFormId(), id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId, null, null, null);
            } else {
                customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId);
            }

            ctaRelationDaoManager.addCtaRelation(ea, arg.getCtaIds(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), id);
        }else {
            HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getHexagonTemplateSiteId());
            if (null == queryHexagonTemplateSiteEntity) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
            }

            siteId = arg.getHexagonTemplateSiteId();
            id = UUIDUtil.getUUID();
            hexagonTemplatePageEntity = BeanUtil.copy(arg, HexagonTemplatePageEntity.class);
            hexagonTemplatePageEntity.setId(id);
            hexagonTemplatePageEntity.setEa(ea);
            hexagonTemplatePageEntity.setCreateBy(userId);
            hexagonTemplatePageEntity.setSharePicH5Apath(sharePicH5APath);
            hexagonTemplatePageEntity.setSharePicMpApath(sharePicMpAPath);
            hexagonTemplatePageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            hexagonTemplatePageEntity.setCreateTime(new Date());
            hexagonTemplatePageEntity.setUpdateTime(new Date());
            hexagonTemplatePageEntity.setHexagonTemplateSiteId(siteId);

            if (StringUtils.isNotBlank(arg.getHexagonTemplateSiteId())) {
                HexagonTemplateSiteEntity hexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getHexagonTemplateSiteId());
                if (null == hexagonTemplateSiteEntity) {
                    return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
                }

                hexagonTemplatePageEntity.setContent(hexagonManager.resetContentApath(hexagonTemplatePageEntity.getContent(), ea));
                String newShareApath = hexagonManager.resetShareApath(sharePicH5APath, ea);
                hexagonTemplatePageEntity.setSharePicH5Apath(newShareApath);
                hexagonTemplatePageEntity.setSharePicMpApath(newShareApath);
            }

            int insertResult = hexagonTemplatePageDAO.insert(hexagonTemplatePageEntity);
            if (insertResult != 1) {
                log.error("HexagonService.editTemplatePage insert failed, entity={}", hexagonTemplatePageEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            //mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, userId, ObjectTypeEnum.HEXAGON_PAGE.getType(), hexagonTemplatePageEntity.getName(), OperateTypeEnum.ADD);
            if (StringUtils.isNotBlank(arg.getFormId())) {
                customizeFormDataManager.bindCustomizeFormDataObject(arg.getFormId(), id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId, null, null, null);
            }
            ctaRelationDaoManager.addCtaRelation(ea, arg.getCtaIds(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), id);
        }

        if (arg.getIsHomepage() != null && arg.getIsHomepage() == 1) {
            hexagonTemplatePageDAO.removeHomePage(arg.getHexagonTemplateSiteId());
            int setHomePageResult = hexagonTemplatePageDAO.setHomePage(id);
            if (setHomePageResult != 1) {
                log.error("HexagonService.editPage setHomePage failed, id={}", id);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }

        CreateTemplatePageResult result = new CreateTemplatePageResult();
        result.setId(id);

        return Result.newSuccess(result);

    }


    @Override
    public Result<List<GetPagesByTemplateSiteIdResult>> getPagesByTemplateSiteId(String ea, String siteId) {
        HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(siteId);
        if (null == queryHexagonTemplateSiteEntity) {
            log.warn("HexagonService.getPagesBySystemSiteId queryHexagonTemplateSiteEntity not found, id={}", siteId);
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        List<GetPagesByTemplateSiteIdResult> resultList = new ArrayList<>();
        List<HexagonTemplatePageEntity> hexagonTemplatePageEntityList = hexagonTemplatePageDAO.getBySiteId(siteId);
        if (CollectionUtils.isNotEmpty(hexagonTemplatePageEntityList)) {
            List<String> apathList = new ArrayList<>();
            for (HexagonTemplatePageEntity hexagonTemplatePageEntity : hexagonTemplatePageEntityList) {
                if (StringUtils.isNotBlank(hexagonTemplatePageEntity.getSharePicH5Apath())) {
                    apathList.add(hexagonTemplatePageEntity.getSharePicH5Apath());
                }
                if (StringUtils.isNotBlank(hexagonTemplatePageEntity.getSharePicMpApath())) {
                    apathList.add(hexagonTemplatePageEntity.getSharePicMpApath());
                }
            }

            Map<String, String> urlMap = null;
            if (CollectionUtils.isNotEmpty(apathList)) {
//                批量转为url  注
                urlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
            }
            for (HexagonTemplatePageEntity hexagonTemplatePageEntity : hexagonTemplatePageEntityList) {
                GetPagesByTemplateSiteIdResult getPagesBySystemSiteIdResult = BeanUtil.copy(hexagonTemplatePageEntity, GetPagesByTemplateSiteIdResult.class);
                if (urlMap != null) {
                    getPagesBySystemSiteIdResult.setSharePicH5url(urlMap.get(hexagonTemplatePageEntity.getSharePicH5Apath()));
                    getPagesBySystemSiteIdResult.setSharePicMpUrl(urlMap.get(hexagonTemplatePageEntity.getSharePicMpApath()));

                }
                resultList.add(getPagesBySystemSiteIdResult);
            }
        }

        return Result.newSuccess(resultList);
    }

    @Override
    public Result<HexagonFilePreviewResult> getFilePreviewUrl(String siteId, String npath, String fileName) {
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getInclueDeletedById(siteId);
        if (null == hexagonSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        HexagonFilePreviewResult result = new HexagonFilePreviewResult();
        String downloadUrl = fileV2Manager.getDownloadFileUrl(hexagonSiteEntity.getEa(), npath, fileName);
        String previewUrl = fileV2Manager.getPreviewUrl(hexagonSiteEntity.getEa(), npath);
        result.setPreviewUrl(previewUrl);
        result.setDownloadUrl(downloadUrl);

        return Result.newSuccess(result);
    }

    @Override
    public Result<HexagonFilePreviewResult> getFilePreviewUrlByObject(GetFilePreviewUrlByObjectArg arg) {
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        HexagonFilePreviewResult result = new HexagonFilePreviewResult();
        String downloadUrl = fileV2Manager.getDownloadFileUrl(ea, arg.getNpath(), arg.getFileName());
        String previewUrl = fileV2Manager.getPreviewUrl(ea, arg.getNpath());
        result.setPreviewUrl(previewUrl);
        result.setDownloadUrl(downloadUrl);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetOfficialWebsiteInfoResult> getOfficialWebsiteInfo(String ea, String cardUid) {
        if (StringUtils.isNotBlank(cardUid)) {
            // 小程序查询
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(cardUid);
            if(fsBindEntity == null) {
                log.warn("HexagonServiceImpl.getOfficialWebsiteInfo error cardUid:{}", cardUid);
                return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
            }
            ea = fsBindEntity.getFsEa();
        }
        // 查询官网信息
        GetOfficialWebsiteInfoResult result = new GetOfficialWebsiteInfoResult();
        List<HexagonOfficialWebsiteEntity> hexagonOfficialWebsiteEntities = hexagonOfficialWebsiteDAO.getWebsiteHexagonSiteInfoByEa(ea);
        result.setOpenWebSite(HexagonOfficialWebsiteStatusEnum.Disable.getValue());
        if (CollectionUtils.isNotEmpty(hexagonOfficialWebsiteEntities)) {
            HexagonOfficialWebsiteEntity hexagonOfficialWebsiteEntity = hexagonOfficialWebsiteEntities.get(0);
            result.setOpenWebSite(hexagonOfficialWebsiteEntity.getStatus());
            result.setSiteId(hexagonOfficialWebsiteEntity.getHexagonSiteId());
            // 查询微页面创建信息
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(hexagonOfficialWebsiteEntity.getHexagonSiteId());
            if (hexagonSiteEntity == null) {
                log.warn("HexagonServiceImpl.getOfficialWebsiteInfo error ea:{}", ea);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            // 查询员工信息
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, Lists.newArrayList(hexagonSiteEntity.getCreateBy()), true);
            FSEmployeeMsg fsEmployeeMsg = fsEmployeeMsgMap.get(hexagonSiteEntity.getCreateBy());
            if (fsEmployeeMsg != null) {
                result.setCreateName(fsEmployeeMsg.getName());
            }
            result.setCreateTime(hexagonSiteEntity.getCreateTime().getTime());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result updateOfficialWebsiteInfo(UpdateOfficialWebsiteInfoArg arg) {
        List<HexagonOfficialWebsiteEntity> hexagonOfficialWebsiteEntityList = hexagonOfficialWebsiteDAO.getWebsiteHexagonSiteInfoByEa(arg.getFsEa());
        if (CollectionUtils.isEmpty(hexagonOfficialWebsiteEntityList)) {
            log.warn("HexagonServiceImpl.updateOfficialWebsiteInfo hexagonOfficialWebsiteEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        hexagonOfficialWebsiteDAO.updateHexagonOfficialWebsiteById(hexagonOfficialWebsiteEntityList.get(0).getId(), arg.getStatus());
        return Result.newSuccess();
    }

    @Override
    public Result<EditObjectGroupResult> editHexagonGroup(String ea, Integer fsUserId, EditHexagonGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonServiceImpl.editHexagonGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }

        List<String> defaultNames = HexagonDefaultGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("HexagonServiceImpl.editHexagonGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }

        return objectGroupManager.editGroup(ea, fsUserId, arg.getGroupId(), arg.getName(), ObjectTypeEnum.HEXAGON_SITE.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteHexagonGroup(String ea, Integer fsUserId, DeleteHexagonGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonServiceImpl.deleteHexagonGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.HEXAGON_SITE.getType());
    }

    private void setDefaultGroupList( List<ListObjectGroupResult> groupList){
        //全部
        ListObjectGroupResult allGroup = new ListObjectGroupResult();
        allGroup.setGroupId(HexagonTemplateDefaultGroupEnum.ALL.getId());
        allGroup.setGroupName(HexagonTemplateDefaultGroupEnum.ALL.getName());
        groupList.add(allGroup);

        //我创建的
        ListObjectGroupResult groupCreateByme = new ListObjectGroupResult();
        groupCreateByme.setGroupId(HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getId());
        groupCreateByme.setGroupName(HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getName());
        groupList.add(groupCreateByme);

        //未分类
        ListObjectGroupResult ungroup = new ListObjectGroupResult();
        ungroup.setGroupId(HexagonTemplateDefaultGroupEnum.NO_GROUP.getId());
        ungroup.setGroupName(HexagonTemplateDefaultGroupEnum.NO_GROUP.getName());
        groupList.add(ungroup);

    }

    @Override
    public Result<ListHexagonTemplateGroupResult> listHexagonGroup(String ea, Integer fsUserId, ListHexagonGroupArg arg) {
        if (arg.getUseType() == ListHexagonGroupUseTypeEnum.MANAGE.getType()){
            ListHexagonTemplateGroupResult result = listHexagonGroupByManage(ea, fsUserId, arg.getKeyword(), arg.getStatus(), arg.getMenuId());
            return Result.newSuccess(result);
        } else{
            ListHexagonTemplateGroupResult result = listHexagonGroupByCustom(ea, fsUserId, arg.getMenuId());
            return Result.newSuccess(result);
        }
    }

    private ListHexagonTemplateGroupResult listHexagonGroupByManage(String ea, Integer fsUserId, String keyword, Integer status, String menuId){
        ListHexagonTemplateGroupResult hexagonTemplateGroupResult = new ListHexagonTemplateGroupResult();
        List<ListObjectGroupResult> resultList = com.beust.jcommander.internal.Lists.newArrayList();
        setDefaultGroupList(resultList);
        //List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.HEXAGON_SITE.getType());
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), menuId, null);
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        hexagonTemplateGroupResult.setSortVersion(customizeGroupListVO.getSortVersion());
        List<String> groupIds = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        //List<ObjectGroupEntity> objectGroupEntityList = groupRoleRelationManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType());
//        if (CollectionUtils.isNotEmpty(objectGroupEntityList)){
//            groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
//            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.HEXAGON_GROUP_DISPLAY_KEY);
//            if (templateGroupDisplayOrder != null) {
//                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
//                hexagonTemplateGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
//            }
//            objectGroupEntityList.forEach(groupResult ->{
//                ListObjectGroupResult objectGroup = new ListObjectGroupResult();
//                objectGroup.setGroupName(groupResult.getName());
//                objectGroup.setGroupId(groupResult.getId());
//                resultList.add(objectGroup);
//            });
//        }

//        Map<String, Integer> contentGroupCountMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(groupIds)) {
//            List<ContentGroupCountDTO> contentGroupCountDTOList = objectGroupRelationDAO.queryHexagonEntityDTOByGroups(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), groupIds);
//            log.info("contentGroupCountDTOList:{}", contentGroupCountDTOList);
//            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)){
//                contentGroupCountMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1, v2)->v2));
//            }
//        }
        for (ListObjectGroupResult objectGroup : resultList){
            if (org.apache.commons.lang.StringUtils.equals(objectGroup.getGroupId(), HexagonTemplateDefaultGroupEnum.ALL.getId())){
                // 如果没有查看任何一个分组的权限，只能看未分组的微页面 + 我创建的
                if (CollectionUtils.isEmpty(groupIds)) {
                    objectGroup.setObjectCount(hexagonSiteDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId, keyword));
                } else {
                    // 如果有分组权限，可以查看 有权限的分组 + 我创建的 + 未分类的
                    objectGroup.setObjectCount(hexagonSiteDAO.queryAccessibleHexagonSiteCount(ea, status, fsUserId, keyword, groupIds));
                }
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }else if (org.apache.commons.lang.StringUtils.equals(objectGroup.getGroupId(), HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getId())){
                objectGroup.setObjectCount(hexagonSiteDAO.queryAllHexagonSiteCountCreateByMe(ea, fsUserId, status, keyword));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }else if (org.apache.commons.lang.StringUtils.equals(objectGroup.getGroupId(), HexagonTemplateDefaultGroupEnum.NO_GROUP.getId())){
                objectGroup.setObjectCount(hexagonSiteDAO.queryHexagonCountByUnGrouped(ea, ObjectTypeEnum.HEXAGON_SITE.getType(),status, keyword));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }
//            else {
//                if (contentGroupCountMap == null || contentGroupCountMap.get(objectGroup.getGroupId()) == null){
//                    objectGroup.setObjectCount(0);
//                }else {
//                    objectGroup.setObjectCount(contentGroupCountMap.get(objectGroup.getGroupId()));
//                }
//            }
        }

        hexagonTemplateGroupResult.setObjectGroupList(resultList);
        return hexagonTemplateGroupResult;
    }

    private ListHexagonTemplateGroupResult listHexagonGroupByCustom(String ea, Integer fsUserId, String menuId){
        ListHexagonTemplateGroupResult hexagonTemplateGroupResult = new ListHexagonTemplateGroupResult();
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), menuId, null);
        hexagonTemplateGroupResult.setObjectGroupList(customizeGroupListVO.getObjectGroupList());
        hexagonTemplateGroupResult.setSortVersion(customizeGroupListVO.getSortVersion());
//        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.HEXAGON_SITE.getType());
//        List<ObjectGroupEntity> objectGroupEntityList = groupRoleRelationManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType());
//        if (CollectionUtils.isEmpty(objectGroupEntityList)){
//            return null;
//        }
//
//        DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.HEXAGON_GROUP_DISPLAY_KEY);
//        if (templateGroupDisplayOrder != null) {
//            NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
//            hexagonTemplateGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
//        }
//        List<ListObjectGroupResult> resultList = Lists.newArrayList();
//        List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
//        List<ContentGroupCountDTO> groupCountDTOList = objectGroupRelationDAO.queryHexagonEntityDTOByGroups(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), groupIds);
//        Map<String, ContentGroupCountDTO> groupCountDTOMap = null;
//        if (CollectionUtils.isNotEmpty(groupCountDTOList)){
//            groupCountDTOMap = groupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, Function.identity(), (v1, v2)->v2));
//        }
//
//        for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList){
//            ListObjectGroupResult groupResult = new ListObjectGroupResult();
//            groupResult.setGroupId(objectGroupEntity.getId());
//            groupResult.setGroupName(objectGroupEntity.getName());
//            if (groupCountDTOMap != null && groupCountDTOMap.get(objectGroupEntity.getId()) != null) {
//                groupResult.setObjectCount(groupCountDTOMap.get(objectGroupEntity.getId()).getCount());
//            }
//            resultList.add(groupResult);
//        }
//
//        hexagonTemplateGroupResult.setObjectGroupList(resultList);
        return hexagonTemplateGroupResult;
    }

    @Override
    @FilterLog
    public Result<PageResult<GetSiteByEaUnitResult>> listHexagonByGroup(String ea, Integer fsUserId, ListHexagonByGroupArg arg) {
        PageResult<GetSiteByEaUnitResult> pageResult = new PageResult<>();
        List<GetSiteByEaUnitResult> hexagonResultList = Lists.newArrayList();
        pageResult.setResult(hexagonResultList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        if (arg.getGroupId() == null){
            arg.setGroupId(HexagonDefaultGroupEnum.ALL.getId());
        }
        List<HexagonSiteEntity> pageList;
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        String groupName = null;
        if (arg.getGroupId() == null){
            arg.setGroupId(HexagonDefaultGroupEnum.ALL.getId());
        }
        HexagonSiteQueryParam queryParam = new HexagonSiteQueryParam();
        queryParam.setEa(ea);
        queryParam.setKeyword(arg.getKeyword());
        queryParam.setStatus(arg.getStatus());
        queryParam.setMaterialTagFilter(arg.getMaterialTagFilter());
        queryParam.setHasFormId(Boolean.TRUE.equals(arg.getHasFormId()));

        if (arg.getGroupId().equals(HexagonDefaultGroupEnum.ALL.getId())) {
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType());
            queryParam.setPermissionGroupIdList(Lists.newArrayList(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList())));
            pageList = hexagonSiteDAO.getAccessiblePage(queryParam, page);
        } else if (arg.getGroupId().equals(HexagonDefaultGroupEnum.CREATED_BY_ME.getId())) {
            pageList = hexagonSiteDAO.pageQueryHexagonEntityByMe(ea, fsUserId, arg.getStatus(), arg.getKeyword(), arg.getMaterialTagFilter(), Boolean.TRUE.equals(arg.getHasFormId()), page);
        } else if (arg.getGroupId().equals(HexagonDefaultGroupEnum.NO_GROUP.getId())){
            pageList = hexagonSiteDAO.pageQueryHexagonEntityByUngrouped(ea, arg.getStatus(), arg.getKeyword(), ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getMaterialTagFilter(), Boolean.TRUE.equals(arg.getHasFormId()), page);
        } else {
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType());
            Set<String> groupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            if (!groupIdSet.contains(arg.getGroupId())){
                pageList = Lists.newArrayList();
            } else {
                queryParam.setPermissionGroupIdList(Lists.newArrayList(groupIdSet));
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(arg.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                pageList = hexagonSiteDAO.getAccessiblePage(queryParam, page);
            }
        }

        if (CollectionUtils.isEmpty(pageList)){
            return Result.newSuccess(pageResult);
        }

        List<String> objectIds = pageList.stream().map(HexagonSiteEntity::getId).collect(Collectors.toList());
        List<GroupNameObjectIdDTO> groupNameObjectIdDTO = objectGroupRelationDAO.queryGroupNameByObjectIds(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), objectIds);
        Map<String, String> objectIdNameMap = null;
        if (CollectionUtils.isNotEmpty(groupNameObjectIdDTO)){
            objectIdNameMap = groupNameObjectIdDTO.stream().collect(Collectors.toMap(GroupNameObjectIdDTO::getObjectId, GroupNameObjectIdDTO::getGroupName, (v1,v2)->v2));
        }

        //物料平均访问深度
        ActionDurationTimeObjectArg durationTimeObjectArg = new ActionDurationTimeObjectArg();
        durationTimeObjectArg.setEa(ea);
        durationTimeObjectArg.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
        durationTimeObjectArg.setObjectIdList(objectIds);
        durationTimeObjectArg.setActionType(NewActionTypeEnum.LOOK_UP.getActionType());
        com.facishare.marketing.statistic.common.result.Result<List<ActionDurationTimeAvgByObjectResult>> listActionDurationTimeAvgListByObject = userMarketingStatisticService.listActionDurationTimeAvgListByObject(durationTimeObjectArg);
        Map<String, String> objectAvgTimeMap = Maps.newHashMap();
        Map<String, Integer> marketingObjectActivityLookUpDTOMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(listActionDurationTimeAvgListByObject.getData())){
            objectAvgTimeMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId, o-> TimeMeasureUtil.getSecondTime(Long.valueOf(o.getAvg())),(v1, v2)->v2));
            marketingObjectActivityLookUpDTOMap = listActionDurationTimeAvgListByObject.getData().stream().collect(Collectors.toMap(ActionDurationTimeAvgByObjectResult::getObjectId,ActionDurationTimeAvgByObjectResult::getCount,(v1, v2)->v2));
        }

        pageResult.setTotalCount(page.getTotalNum());
        List<GetSiteByEaUnitResult> resultList = hexagonManager.getGetSiteByEaUnitResults(ea, fsUserId, pageList);
        if (CollectionUtils.isEmpty(resultList)){
            return Result.newSuccess(pageResult);
        }
        List<ObjectTopEntity> objectTopEntityList = objectTopManager.getByObjectIdList(objectIds);
        Set<String> objectIdTopSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(objectTopEntityList)) {
            objectIdTopSet = objectTopEntityList.stream().map(ObjectTopEntity::getObjectId).collect(Collectors.toSet());
        }
//        List<HexagonPageEntity> hexagonPageEntities = hexagonPageDAO.listHomePageBySiteIds(objectIds);
//        Map<String, HexagonPageEntity> hexagonPageEntityMap = hexagonPageEntities.stream().collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, Function.identity(), (v1, v2) -> v1));
        List<String> imageUrls = resultList.stream().filter(Objects::nonNull)
                .map(GetSiteByEaUnitResult::getCoverAPath)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        //多线程处理图片
        Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);

        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(objectIds, ObjectTypeEnum.HEXAGON_SITE.getType());

        for (GetSiteByEaUnitResult unitResult : resultList){
            unitResult.setGroupName(groupName);
            unitResult.setTop(objectIdTopSet.contains(unitResult.getId()));
            // 获取裁剪封面图
//            HexagonPageEntity homePage = hexagonPageEntityMap.get(unitResult.getId());
//            if (StringUtils.isNotBlank(homePage.getSharePicH5Apath())) {
//                unitResult.setShareImgUrl(fileV2Manager.getUrlByPath(homePage.getSharePicH5Apath(),null,false));
//                unitResult.setShareImgAPath(homePage.getSharePicH5Apath());
//            }
//            if (StringUtils.isNotBlank(homePage.getShareTitle())) {
//                unitResult.setShareTitle(homePage.getShareTitle());
//            }
//            if (StringUtils.isNotBlank(homePage.getShareDesc())) {
//                unitResult.setShareDesc(homePage.getShareDesc());
//            }
            unitResult.setShareImgUrl(unitResult.getCoverUrl());
            unitResult.setShareImgAPath(unitResult.getCoverAPath());
            if (objectIdNameMap == null || objectIdNameMap.get(unitResult.getId()) == null){
                unitResult.setGroupName(I18nUtil.get("mark.hexagon.group.no_group", HexagonDefaultGroupEnum.NO_GROUP.getName()));
            }else {
                unitResult.setGroupName(objectIdNameMap.get(unitResult.getId()));
            }
            if (objectAvgTimeMap == null || objectAvgTimeMap.get(unitResult.getId()) == null){
                unitResult.setActionDurationTimeAvg(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ARTICLESERVICEIMPL_703));
            }else {
                unitResult.setActionDurationTimeAvg(objectAvgTimeMap.get(unitResult.getId()));
            }
            //鸿曦说访问总数先改成以营销动态记录表为准
            //查看物料访问总数
            if (marketingObjectActivityLookUpDTOMap == null || marketingObjectActivityLookUpDTOMap.get(unitResult.getId()) == null){
                unitResult.setObjectLookUpCount(0);
            }else {
                unitResult.setObjectLookUpCount(marketingObjectActivityLookUpDTOMap.get(unitResult.getId()));
            }
            //返回封面图大小
            if (StringUtils.isNotBlank(unitResult.getCoverAPath()) && coverMap.containsKey(unitResult.getCoverAPath())) {
                unitResult.setCoverSize(coverMap.get(unitResult.getCoverAPath()));
            }

            // 内容标签处理
            List<String> materialTags = materialTagMap.get(unitResult.getId());
            if (CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                unitResult.setMaterialTags(collect);
            }
        }
        hexagonResultList.addAll(resultList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result setHexagonGroup(String ea, Integer fsUserId, SetHexagonGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("HexagonServiceImpl.setHexagonGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }

        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(arg.getHexagonId());
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }

        ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(ea, arg.getHexagonId());
        if (objectGroupRelationEntity == null){
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(arg.getHexagonId());
            newEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
            objectGroupRelationDAO.insert(newEntity);
        }else {
            objectGroupRelationDAO.updateObjectGroup(ea, arg.getGroupId(), arg.getHexagonId(), ObjectTypeEnum.HEXAGON_SITE.getType());
        }
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteEntity.getName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        return Result.newSuccess();
    }

    @Override
    public Result setHexagonAuthorize(String ea, Integer fsUserId, SetHexagonAuthorizeArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonServiceImpl.setHexagonAuthorize only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }

        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(arg.getHexagonSiteId());
        if (hexagonSiteEntity == null){
            log.info("HexagonServiceImpl.setHexagonAuthorize hexagon site not exist ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        ObjectAccessAuthorizeEntity objectAccessAuthorizeEntity = accessAuthorizeDAO.queryByObjectId(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getHexagonSiteId());
        if (objectAccessAuthorizeEntity == null){
            ObjectAccessAuthorizeEntity entity = new ObjectAccessAuthorizeEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setObjectId(arg.getHexagonSiteId());
            entity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
            ContentAuthorize contentAuthorize = new ContentAuthorize();
            entity.setAccessAuthorize(contentAuthorize);
            if (arg.isPublic()){
                contentAuthorize.setPublic(true);
            }else {
                Set<Integer> set = new HashSet<>(arg.getShareList());
                contentAuthorize.setUserIds(Lists.newArrayList(set));
            }
            accessAuthorizeDAO.insert(entity);
        }else {
            ContentAuthorize contentAuthorize = objectAccessAuthorizeEntity.getAccessAuthorize();
            if (arg.isPublic()){
                contentAuthorize.setPublic(true);
            }else {
                if (CollectionUtils.isEmpty(contentAuthorize.getUserIds())){
                    contentAuthorize.getUserIds().addAll(arg.getShareList());
                }else {
                    Set<Integer> set = new HashSet<>(arg.getShareList());
                    set.addAll(contentAuthorize.getUserIds());
                    contentAuthorize.setUserIds(Lists.newArrayList(set));
                }
                accessAuthorizeDAO.updateAccessAuthorizeByObjectId(ea, arg.getHexagonSiteId(), ObjectTypeEnum.HEXAGON_SITE.getType());
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result setHexagonSystemStatus(String ea, Integer fsUserId, SetHexagonSystemStatusArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonServiceImpl.setHexagonSystemStatus only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }

        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(arg.getHexagonSiteId());
        if (hexagonSiteEntity == null){
            log.info("HexagonServiceImpl.setHexagonSystemStatus hexagon site not exist ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }

        hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, arg.getHexagonSiteId(), arg.isSystem());
        return Result.newSuccess();
    }

    @Override
    public Result deleteTemplatePage(String ea, Integer fsUserId, String id) {
        HexagonTemplatePageEntity queryHexagonTemplatePageEntity = hexagonTemplatePageDAO.getById(id);
        if (null == queryHexagonTemplatePageEntity) {
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }
        if (!queryHexagonTemplatePageEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        Integer isHomePage = queryHexagonTemplatePageEntity.getIsHomepage();
        List<HexagonTemplatePageEntity> hexagonTemplatePageEntityList = hexagonTemplatePageDAO.getBySiteId(queryHexagonTemplatePageEntity.getHexagonTemplateSiteId());
        if (CollectionUtils.isNotEmpty(hexagonTemplatePageEntityList)) {
            if (hexagonTemplatePageEntityList.size() == 1) {
                return Result.newError(SHErrorCode.HEXAGON_PAGE_AT_LEAST_ONE);
            }

            if (isHomePage == 1) {
                for (HexagonTemplatePageEntity hexagonTemplatePageEntity : hexagonTemplatePageEntityList) {
                    if (!hexagonTemplatePageEntity.getId().equals(id)) {
                        HexagonTemplatePageEntity newHexagonTemplatePageEntity = new HexagonTemplatePageEntity();
                        newHexagonTemplatePageEntity.setId(hexagonTemplatePageEntity.getId());
                        newHexagonTemplatePageEntity.setIsHomepage(1);
                        int updateResult = hexagonTemplatePageDAO.update(newHexagonTemplatePageEntity);
                        if (updateResult == 1) {
                            break;
                        }
                    }
                }
            }

            hexagonTemplatePageDAO.deleteById(id);
            // 删除表单关联
            customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteForm(id, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, fsUserId);
            ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), Lists.newArrayList(id));
            redisManager.deleteHexgonPage(id);
        }

        return Result.newSuccess();
    }

    @Override
    public Result resetConferenceFormBySite(ResetConferenceFormBySiteArg arg) {
        ActivityEntity activityEntity = conferenceDAO.getActivityByDetailSiteId(arg.getSiteId());
        if (activityEntity == null) {
            return Result.newSuccess();
        }
        List<HexagonSiteListDTO> hexagonSiteListDTOList = hexagonSiteDAO.getFormBySiteIds(Lists.newArrayList(arg.getSiteId()));
        CustomizeFormDataEntity conferenceBindForm = customizeFormDataManager.getBindFormDataByObject(activityEntity.getEa(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType());
        if (CollectionUtils.isEmpty(hexagonSiteListDTOList)) {
            // 若会议当前绑定了表单且类型为微页面则解绑，否则不解绑（兼容旧数据绑定的会议自定义表单）
            if (conferenceBindForm != null && conferenceBindForm.getType().equals(CustomizeFormDataTypeEnum.HEXAGON.getValue())) {
                customizeFormDataManager.unBindCustomizeFormDataObject(activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa());
            }
        } else {
            // 若微页面有表单则直接解绑会议表单再绑定
            customizeFormDataManager.bindCustomizeFormDataObject(hexagonSiteListDTOList.get(0).getFormId(), activityEntity.getId(), ObjectTypeEnum.ACTIVITY.getType(), activityEntity.getEa(),
                activityEntity.getCreateBy(), null, null, null);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<GetSiteByEaUnitResult>> getMarketingContentSite(String ea, Integer fsUserId, GetMarketingContentSiteVO arg) {
        // todo
        Long time = (arg.getTime() == null ? new Date().getTime() : arg.getTime());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);

        //List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.getMarketingContentSiteByPage1(ea, page, arg.getSearchFitter(), arg.getStatusFitter(), BooleanUtils.isTrue(arg.getExcludeSystemSite()));

        List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.getMarketingContentSiteByPage(ea, eieaConverter.enterpriseAccountToId(ea), page, arg.getSearchFitter(), arg.getStatusFitter(), BooleanUtils.isTrue(arg.getExcludeSystemSite()));
        List<GetSiteByEaUnitResult> resultList = hexagonManager.getGetSiteByEaUnitResults(ea, fsUserId, hexagonSiteEntityList);

        PageResult<GetSiteByEaUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<String> copyPage(HexagonPageArg pageEntity, String ea, String siteId, Integer userId) {
        HexagonPageEntity entity = BeanUtil.copy(pageEntity, HexagonPageEntity.class);
        return hexagonManager.copyPage(entity.getId(), entity.getEa(), ea, siteId, userId, HexagonManager.COPY_FROM_HEXAGON);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteSiteBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {

        //判断是否为通用介绍页，通用介绍页不允许删除
        EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(ea);
        if (enterpriseMetaConfigEntity != null && arg.getIdList().contains(enterpriseMetaConfigEntity.getMiniappIntroductionSiteId())) {
            log.warn("通用介绍页，通用介绍页不允许删除 ea{} fsUserId:{} id:{}", ea, fsUserId, enterpriseMetaConfigEntity.getMiniappIntroductionSiteId());
            return Result.newError(-1, I18nUtil.get(I18nKeyEnum.MARK_HEXAGON_HEXAGONSERVICEIMPL_997));
        }

        List<HexagonSiteEntity> siteEntityList = hexagonSiteDAO.getByIds(arg.getIdList());
        if (CollectionUtils.isEmpty(siteEntityList)) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        if (siteEntityList.stream().anyMatch(e -> !StringUtils.equalsIgnoreCase(e.getEa(), ea))) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }
        if (siteEntityList.stream().anyMatch(e -> HexagonStatusEnum.STOPED.getType() != e.getStatus())) {
            return Result.newError(SHErrorCode.CANT_DELETE_NORMAL_HEXAGON_SITE);
        }

        List<String> idList = siteEntityList.stream().map(HexagonSiteEntity::getId).collect(Collectors.toList());
        hexagonSiteDAOManager.deleteByIdList(ea, idList);
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getIdList());
        List<HexagonPageEntity> hexagonPageEntityList = hexagonPageDAO.getByHexagonSiteIdList(arg.getIdList());

        if (CollectionUtils.isNotEmpty(hexagonPageEntityList)) {
            List<String> hexagonPageIdList = hexagonPageEntityList.stream().map(HexagonPageEntity::getId).collect(Collectors.toList());
            customizeFormDataManager.unBindCustomizeFormDataObjectAndDeleteFormBatch(hexagonPageIdList, ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, fsUserId);
            hexagonPageDAO.deleteByIds(hexagonPageIdList);
            ctaRelationDaoManager.deleteCtaRelation(ea, ObjectTypeEnum.HEXAGON_PAGE.getType(), hexagonPageIdList);
        }
        // 删除对应官网数据
        hexagonOfficialWebsiteDAO.deleteOfficialWebsiteByHexagonSiteIdListAndEa(ea, arg.getIdList());
        // 删除官网关联微页面数据
        objectBindDetailDAO.deleteObjectBindDetailByTargetIdList(ea, arg.getIdList(), ObjectTypeEnum.HEXAGON_SITE.getType());
        // redis放到最后在删除
        redisManager.deleteHexagonSiteBatch(arg.getIdList());
        for (HexagonSiteEntity hexagonSiteEntity : siteEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(),
                    hexagonSiteEntity.getName(), OperateTypeEnum.DELETE);
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topHexagonSite(String ea, Integer fsUserId, TopMaterialArg arg) {
        HexagonSiteEntity entity = hexagonSiteDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopHexagonSite(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.HEXAGON_SITE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> addHexagonGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.HEXAGON_SITE.getType());
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setHexagonGroupBatch(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("HexagonServiceImpl.setHexagonGroupBatch only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }

        List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.getByIds(arg.getObjectIdList());

        if (CollectionUtils.isEmpty(hexagonSiteEntityList)) {
            return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND);
        }

        if (hexagonSiteEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_HEXAGON_SITE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        // 直接把以前的分组删掉，就不用更新了，直接全部插入
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        for (HexagonSiteEntity hexagonSiteEntity : hexagonSiteEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), hexagonSiteEntity.getName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }

    @Override
    public Result<HexagonQrCodeResult> createHexagonQrCode(CreateHexagonWxQrCodeArg arg) {

        if((CollectionUtils.isEmpty(arg.getWxQrCodes()) && CollectionUtils.isEmpty(arg.getQywxQrCodes()))
                || StringUtils.isEmpty(arg.getUserMarketingId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        if(StringUtils.isBlank(ea)){
            ea = objectManager.getObjectEa(arg.getObjectId(),arg.getObjectType());
        }
        if(StringUtils.isBlank(ea)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        HexagonQrCodeResult hexagonQrCodeResult = new HexagonQrCodeResult();

        //公众号类型二维码
        if(CollectionUtils.isNotEmpty(arg.getWxQrCodes())){
            //获取公众号二维码详情
            List<QrCodeResult> wxQrCodeResults = getQrCodeResults(arg.getWxQrCodes(), ea);
            //构建新的公众号二维码
            List<HexagonQrCodeResult.QrCodeResult> wxQrCodeResultList = createWxQrCode(arg, ea, wxQrCodeResults);
            hexagonQrCodeResult.setWxQrCodeResult(wxQrCodeResultList);
        }

        //企微类型二维码
        if(CollectionUtils.isNotEmpty(arg.getQywxQrCodes())){
            List<String> sceneIds = arg.getQywxQrCodes().stream().map(o -> o.getSceneId()).collect(Collectors.toList());
            List<QywxAddFanQrCodeEntity>  entities = qywxAddFanQrCodeDAO.getByIds(sceneIds);
            if (CollectionUtils.isEmpty(entities)) {
                log.warn("createOrUpdateHexagonFanQrCode entity is null, sceneIds:{}", sceneIds);
                return null;
            }
            //构建新的企微员工活码
            List<HexagonQrCodeResult.QrCodeResult> qywxQrCodeResultList = createQywxQrCode(arg,ea, entities);
            hexagonQrCodeResult.setQywxQrCodeResult(qywxQrCodeResultList);
        }
        return Result.newSuccess(hexagonQrCodeResult);
    }

    @Override
    public Result updateSiteOutDisplayName(String ea, Integer fsUserId, UpdateSiteOutDisplayNameArg arg) {
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(arg.getId());
        if (hexagonSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
        }
        if (StringUtils.equals(arg.getOutDisplayName(), "")){
            arg.setOutDisplayName(null);
        }
        hexagonSiteDAO.updateOutDisplayName(hexagonSiteEntity.getId(), arg.getOutDisplayName(), ea);
        return Result.newSuccess();
    }

    @Override
    public Result<FileToHexagonResult> fileToHexagon(FileToHexagonArg arg) {
        String siteId = null;
        //管理员物料（包括活动下物料和公开物料）
        if(StringUtils.isNotBlank(arg.getSystemSite())){
            siteId = makeHexagonV2(arg);
        }else {
            //个人资料库
            siteId = makeHexagon(arg);
        }
        if (StringUtils.isBlank(siteId)){
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        String url = fsDocumentConvertBigIp +"/api/convert";
        HashMap<String, String> map = Maps.newHashMap();
        map.put("path",arg.getFilePath());
        map.put("ea",arg.getEa());
        int status;
        String taskId = null;
        FileToHexagonApiResult<FileToHexagonDataDTO> fileToHexagonDataDTOResult = httpManager.executePostHttpIgnoreStatusCode(map, url, new TypeToken<FileToHexagonApiResult<FileToHexagonDataDTO>>() {});
        if (fileToHexagonDataDTOResult==null || fileToHexagonDataDTOResult.getData()==null || !fileToHexagonDataDTOResult.isSuccess() || StringUtils.isBlank(fileToHexagonDataDTOResult.getData().getTaskId())){
            status = FileToHexagonStatusEnum.FAILED.getStatus();
        }else {
            status = FileToHexagonStatusEnum.getEnumByFileStatus(fileToHexagonDataDTOResult.getData().getStatus()).getStatus();
            taskId = fileToHexagonDataDTOResult.getData().getTaskId();
        }
        //转码状态处理
        FileToHexagonEntity entity = new FileToHexagonEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(arg.getEa());
        entity.setHexagonSiteId(siteId);
        entity.setFileName(arg.getFileName());
        entity.setFileType(arg.getFileType());
        entity.setFilePath(arg.getFilePath());
        entity.setTaskId(taskId);
        if(status == FileToHexagonStatusEnum.FAILED.getStatus()){
            entity.setFailReason(fileToHexagonDataDTOResult.getMessage());
        }
        entity.setStatus(status);
        int insert = fileToHexagonDAO.insertOrUpdate(entity);
        if (insert != 1) {
            log.warn("HexagonService.fileToHexagon insert failed, entity={}", entity);
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        List<FileToHexagonDataArg> args  = Lists.newArrayList();
        if(status == FileToHexagonStatusEnum.SUCCESS.getStatus() && CollectionUtils.isNotEmpty(fileToHexagonDataDTOResult.getData().getPaths())){
            args = BeanUtil.copy(fileToHexagonDataDTOResult.getData().getPaths(), FileToHexagonDataArg.class);
        }
        if(CollectionUtils.isNotEmpty(args)){
            updateFileToHexagonStatus(siteId,status,fileToHexagonDataDTOResult.getData().getMessage(),args);
        }
        FileToHexagonResult result = new FileToHexagonResult();
        result.setSiteId(siteId);
        return Result.newSuccess(result);
    }

    private String makeHexagon(FileToHexagonArg arg) {
        String siteId = arg.getHexagonSiteId();
        if (StringUtils.isBlank(siteId)){
            //新建
            siteId = UUIDUtil.getUUID();
            HexagonSiteEntity hexagonSiteEntity = new HexagonSiteEntity();
            hexagonSiteEntity.setName(StringUtils.isBlank(arg.getSiteName())?arg.getFileName():arg.getSiteName());
            hexagonSiteEntity.setId(siteId);
            hexagonSiteEntity.setEa(arg.getEa());
            hexagonSiteEntity.setCreateBy(arg.getUserId());
            hexagonSiteEntity.setUpdateBy(arg.getUserId());
            hexagonSiteEntity.setSystemSite(true);
            hexagonSiteEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            int insertSiteResult = hexagonSiteDAOManager.insert(hexagonSiteEntity);
            if (insertSiteResult != 1) {
                log.warn("HexagonService.fileToHexagon insert site failed, entity={}", hexagonSiteEntity);
                return null;
            }
            HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
            String pageId = UUIDUtil.getUUID();
            hexagonPageEntity.setId(pageId);
            hexagonPageEntity.setEa(arg.getEa());
            hexagonPageEntity.setHexagonSiteId(siteId);
            hexagonPageEntity.setName(arg.getFileName());
            hexagonPageEntity.setShareTitle((StringUtils.isBlank(arg.getShareTitle())?arg.getFileName():arg.getShareTitle()));
            if(StringUtils.isNotBlank(arg.getShareDesc())){
                hexagonPageEntity.setShareDesc(arg.getShareDesc());
            }
            if(StringUtils.isNotBlank(arg.getPath())){
                photoManager.addOrUpdatePhotoV2(arg.getEa(), PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, pageId, arg.getPath());
                photoManager.addOrUpdatePhotoV2(arg.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, pageId, arg.getPath());
                photoManager.addOrUpdatePhotoV2(arg.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, pageId, arg.getPath());
                hexagonPageEntity.setSharePicMpApath(arg.getPath());
                hexagonPageEntity.setSharePicH5Apath(arg.getPath());
            }
            hexagonPageEntity.setContent(arg.getPageContent());
            hexagonPageEntity.setIsHomepage(1);
            hexagonPageEntity.setStatus(1);
            hexagonPageEntity.setCreateBy(arg.getUserId());
            hexagonPageEntity.setUpdateBy(arg.getUserId());
            int insertPageResult = hexagonPageDAO.insert(hexagonPageEntity);
            if (insertPageResult != 1) {
                log.warn("HexagonService.fileToHexagon insert page failed, entity={}", hexagonSiteEntity);
                return null;
            }
        }
        return siteId;
    }



    //微页面列表生成pdf微页面systemSite=false
    private String makeHexagonV2(FileToHexagonArg arg) {
        String siteId = arg.getHexagonSiteId();
        String pageId = null;
        if (StringUtils.isBlank(siteId)){
            //新建
            siteId = UUIDUtil.getUUID();
            HexagonSiteEntity hexagonSiteEntity = new HexagonSiteEntity();
            hexagonSiteEntity.setName(StringUtils.isBlank(arg.getSiteName())?arg.getFileName():arg.getSiteName());
            hexagonSiteEntity.setId(siteId);
            hexagonSiteEntity.setEa(arg.getEa());
            hexagonSiteEntity.setCreateBy(arg.getUserId());
            hexagonSiteEntity.setUpdateBy(arg.getUserId());
            hexagonSiteEntity.setSystemSite(Objects.equals("1",arg.getSystemSite()));
            hexagonSiteEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            int insertSiteResult = hexagonSiteDAOManager.insert(hexagonSiteEntity);
            if (insertSiteResult != 1) {
                log.warn("HexagonService.fileToHexagon insert site failed, entity={}", hexagonSiteEntity);
                return null;
            }
            HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
            pageId = UUIDUtil.getUUID();
            hexagonPageEntity.setId(pageId);
            hexagonPageEntity.setEa(arg.getEa());
            hexagonPageEntity.setHexagonSiteId(siteId);
            hexagonPageEntity.setName(arg.getFileName());
            hexagonPageEntity.setShareTitle((StringUtils.isBlank(arg.getShareTitle())?arg.getFileName():arg.getShareTitle()));
            if(StringUtils.isNotBlank(arg.getShareDesc())){
                hexagonPageEntity.setShareDesc(arg.getShareDesc());
            }
            hexagonPageEntity.setSharePicMpApath(arg.getPath());
            hexagonPageEntity.setSharePicH5Apath(arg.getPath());
            hexagonPageEntity.setContent(arg.getPageContent());
            hexagonPageEntity.setIsHomepage(1);
            hexagonPageEntity.setStatus(1);
            hexagonPageEntity.setCreateBy(arg.getUserId());
            hexagonPageEntity.setUpdateBy(arg.getUserId());
            int insertPageResult = hexagonPageDAO.insert(hexagonPageEntity);
            if (insertPageResult != 1) {
                log.warn("HexagonService.fileToHexagon insert page failed, entity={}", hexagonSiteEntity);
                return null;
            }
        }else {
            HexagonPageEntity hexagonPageEntity = hexagonPageDAO.getHomePage(siteId);
            if (hexagonPageEntity == null){
                log.warn("HexagonService.fileToHexagon homePage is null, siteId={}", siteId);
                return null;
            }
            pageId = hexagonPageEntity.getId();
            hexagonPageEntity.setName(arg.getFileName());
            hexagonPageEntity.setShareTitle(arg.getShareTitle());
            if(StringUtils.isNotBlank(arg.getShareDesc())){
                hexagonPageEntity.setShareDesc(arg.getShareDesc());
            }
            hexagonPageEntity.setContent(arg.getPageContent());
            hexagonPageEntity.setSharePicMpApath(arg.getPath());
            hexagonPageEntity.setSharePicH5Apath(arg.getPath());
            hexagonPageEntity.setUpdateBy(arg.getUserId());
            int updatePageResult = hexagonPageDAO.update(hexagonPageEntity);
            if (updatePageResult != 1) {
                log.warn("HexagonService.fileToHexagon update page failed, entity={}", hexagonPageEntity);
                return null;
            }
            HexagonSiteEntity hexagonSiteEntity = new HexagonSiteEntity();
            hexagonSiteEntity.setName(StringUtils.isBlank(arg.getSiteName())?arg.getFileName():arg.getSiteName());
            hexagonSiteEntity.setId(siteId);
            hexagonSiteDAO.update(hexagonSiteEntity);
            if(StringUtils.isBlank(arg.getPath())){
                photoManager.deleteByTargetIdAndTypes(pageId,Lists.newArrayList(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()
                        ,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()));
            }
        }

        if(StringUtils.isNotBlank(arg.getPath()) && CollectionUtils.isNotEmpty(arg.getCutOffsetList())){
            for (PhotoCutOffset cutOffset : arg.getCutOffsetList()){
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(arg.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, pageId, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? arg.getPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(arg.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, pageId, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? arg.getPath() : cutOffset.getPath());
                }
                if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()){
                    photoManager.addOrUpdatePhotoByCutOffset(arg.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, pageId, cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? arg.getPath() : cutOffset.getPath());
                }
            }
        }
        if(CollectionUtils.isNotEmpty(arg.getTagNameList())){
            AddHexagonVisitTagArg tagArg = new AddHexagonVisitTagArg();
            tagArg.setHexagonSiteId(siteId);
            tagArg.setTagNameList(arg.getTagNameList());
            addHexagonVisitTag(arg.getEa(), arg.getUserId(), tagArg);
        }
        if (StringUtils.isNotEmpty(arg.getGroupId())){
            ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(arg.getEa(), arg.getGroupId());
            if (objectGroupEntity != null){
                ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(arg.getEa(), siteId);
                if (objectGroupRelationEntity == null){
                    ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
                    newEntity.setId(UUIDUtil.getUUID());
                    newEntity.setEa(arg.getEa());
                    newEntity.setGroupId(arg.getGroupId());
                    newEntity.setObjectId(siteId);
                    newEntity.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                    objectGroupRelationDAO.insert(newEntity);
                }else {
                    objectGroupRelationDAO.updateObjectGroup(arg.getEa(), arg.getGroupId(), siteId, ObjectTypeEnum.HEXAGON_SITE.getType());
                }
            }
        }
        return siteId;
    }
    @Override
    public void updateFileToHexagonStatus(String siteId, int status, String failReason, List<FileToHexagonDataArg> argList) {
        HexagonPageEntity homePage = hexagonPageDAO.getHomePage(siteId);
        if (homePage == null){
            log.warn("HexagonService.fileToHexagon homePage is null, siteId={}", siteId);
            return;
        }
        String sharePosterUrl = null;
        List<PhotoEntity> photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(), homePage.getId());
        if (CollectionUtils.isNotEmpty(photoEntities)){
            sharePosterUrl =fileV2Manager.getUrlByCPath(homePage.getEa(), photoEntities.get(0).getPath());
        }else {
            sharePosterUrl =fileV2Manager.getUrlByCPath(homePage.getEa(), argList.get(0).getPath());
        }
        //文件转微页面模板
        String content = fileToHexagonTemplate;
        //生成对应的图片组件和留白组件的模板
        String pictureContent = pictureHexagonTemplate;

        int i = fileToHexagonDAO.updateStatus(siteId, status, failReason);
        if (i != 1) {
            log.warn("HexagonService.fileToHexagon update status failed, siteId={}, status={}, failReason={}", siteId, status, failReason);
            return;
        }
        FileToHexagonEntity file = fileToHexagonDAO.getByHexagonSiteId(siteId);
        try {
            StringBuilder stringBuilder = new StringBuilder();
            for (FileToHexagonDataArg arg : argList) {
                String url = fileV2Manager.getUrlByCPath(homePage.getEa(), arg.getPath());
                int height = 0;
                if (arg.getWidth() != 0) {
                    height = (int) Math.ceil(375.0 * arg.getHeight() / arg.getWidth());
                }

                String tempContent = pictureContent;
                //替换对应的图片组件和留白组件id
                tempContent = tempContent.replace("{{imgId}}",UUIDUtil.getUUID()).replace("{{imgUrl}}",url).replace("{{blankId}}",UUIDUtil.getUUID()).replace("{{pageId}}", homePage.getId())
                        ;
                if (height ==0 ){
                    tempContent = tempContent.replace("{{imgHeight}}","auto");
                }else {
                    tempContent = tempContent.replace("\"{{imgHeight}}\"",height+"");
                }
                stringBuilder.append(tempContent).append(",");
            }
            String partContent = "[" + stringBuilder.deleteCharAt(stringBuilder.length() - 1) + "]";
            content = content.replace("\"{{components}}\"", partContent).replace("{{pageId}}", homePage.getId()).replace("{{pageTitle}}", file.getFileName().split("\\.")[0])
                    .replace("{{sharePosterUrl}}", sharePosterUrl);
        }catch (Exception e){
            log.warn("HexagonService.fileToHexagon update status failed, siteId={}, status={}, failReason={}", siteId, status, failReason);
            return;
        }

        String cFile = argList.get(0).getPath();
        HexagonPageEntity hexagonPageEntity = new HexagonPageEntity();
        if(CollectionUtils.isEmpty(photoEntities)){
            photoManager.addOrUpdatePhotoV2(homePage.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, homePage.getId(), cFile);
            photoManager.addOrUpdatePhotoV2(homePage.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, homePage.getId(),  cFile);
            photoManager.addOrUpdatePhotoV2(homePage.getEa(),PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, homePage.getId(),  cFile);
            hexagonPageEntity.setSharePicMpApath(cFile);
            hexagonPageEntity.setSharePicH5Apath(cFile);
        }
        hexagonPageEntity.setId(homePage.getId());
        hexagonPageEntity.setContent(content);
        hexagonPageDAO.update(hexagonPageEntity);
        redisManager.deleteHexgonSite(siteId);
        redisManager.deleteHexgonPage(homePage.getId());
        redisManager.deleteHexagonHomePage(siteId);

        //营销内容处理
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter("field_micro_page_id",OperatorConstants.IN,Lists.newArrayList(siteId));
        paasQueryArg.addFilter("current_status",OperatorConstants.IN,Lists.newArrayList("processing"));
        queryFilterArg.setQuery(paasQueryArg);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectByFilterV3(homePage.getEa(), SuperUserConstants.USER_ID, queryFilterArg, 1, 1);
        //个人物料
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            ObjectData objectData = objectDataInnerPage.getDataList().get(0);
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("_id", objectData.getId());
            dataMap.put("current_status","normal");
            String fieldPromotionLink = host + "/proj/page/marketing-page/"+homePage.getEa()+"?id=" + siteId  + "&ea=" + homePage.getEa() + "&type=1";
            fieldPromotionLink = sendService.getShortUrl(fieldPromotionLink).getData().getShortUrl();
            dataMap.put("field_promotion_link", fieldPromotionLink);
            crmV2Manager.editObjectData(homePage.getEa(), CrmObjectApiNameEnum.MARKETING_CONTENT_OBJ.getName(), dataMap);
        }

    }

    @Override
    public Result<String> genMktParam(String mktParam) {
        return Result.newSuccess(outLinkMktParamManager.genMktParam(mktParam));
    }

    @Override
    public Result<List<String>> queryHexagonBackgroundColorSetting(QueryHexagonBackgroudColorVO vo) {
        List<String> hexagonBackgroudColorSettingList = Lists.newArrayList();
        HexagonBackgroudColorSettingEntity hexagonBackgroudColorSettingEntity = hexagonBackgroudColorSettingDAO.queryByEa(vo.getEa());
        if (hexagonBackgroudColorSettingEntity == null) {
            return Result.newSuccess(hexagonBackgroudColorSettingList);
        }
        String backgroundColor = hexagonBackgroudColorSettingEntity.getBackgroundColor();
        if (StringUtils.isNotBlank(backgroundColor)) {
            List<String> colorList = GsonUtil.getGson().fromJson(backgroundColor, ArrayList.class);
            hexagonBackgroudColorSettingList.addAll(colorList);
        }
        return Result.newSuccess(hexagonBackgroudColorSettingList);
    }

    @Override
    public Result<List<String>> updateHexagonBackgroundColorSetting(String ea, UpdateHexagonBackgroudColorArg arg) {
        //更新之前先查询最新的颜色
        List<UpdateHexagonBackgroudColorArg.ColorConfig> colorConfigs = arg.getColorConfigs();
        //获取新增的颜色
        List<String> addColorList = colorConfigs.stream().filter(colorConfig -> colorConfig.getType() == 1).map(UpdateHexagonBackgroudColorArg.ColorConfig::getColor).collect(Collectors.toList());
        //获取删除的颜色
        List<String> deleteColorList = colorConfigs.stream().filter(colorConfig -> colorConfig.getType() == 0).map(UpdateHexagonBackgroudColorArg.ColorConfig::getColor).collect(Collectors.toList());
        //加把锁,保证获取的是最新的数据
        String lockKey = "HEXAGON_BACK_GROUND_COLOR:" + ea;
        boolean lock = lockManager.retryGetLock(lockKey, 10,60,50);
        if (!lock) {
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        try {
            HexagonBackgroudColorSettingEntity hexagonBackgroudColorSettingEntity = hexagonBackgroudColorSettingDAO.queryByEa(ea);
            if (hexagonBackgroudColorSettingEntity == null) {
                if (CollectionUtils.isEmpty(addColorList)) {
                    //不做处理,因为不存在数据,直接返回
                    return Result.newSuccess(Lists.newArrayList());
                }
                //将addColorList中剔除在deleteColorList中的颜色
                addColorList.removeAll(deleteColorList);
                hexagonBackgroudColorSettingEntity = new HexagonBackgroudColorSettingEntity();
                hexagonBackgroudColorSettingEntity.setId(UUIDUtil.getUUID());
                hexagonBackgroudColorSettingEntity.setEa(ea);
                hexagonBackgroudColorSettingEntity.setBackgroundColor(GsonUtil.getGson().toJson(addColorList));
                hexagonBackgroudColorSettingEntity.setStatus(HexagonColorStatusEnum.NORMAL.getStatus());
                hexagonBackgroudColorSettingDAO.insert(hexagonBackgroudColorSettingEntity);
                return Result.newSuccess(addColorList);
            }
            String backgroundColor = hexagonBackgroudColorSettingEntity.getBackgroundColor();
            List<String> colorList = GsonUtil.getGson().fromJson(backgroundColor, ArrayList.class);
            //新增的需要过滤掉已存在的颜色
            colorList.addAll(addColorList.stream().filter(color -> !colorList.contains(color)).collect(Collectors.toList()));
            //再删除
            colorList.removeAll(deleteColorList);
            hexagonBackgroudColorSettingEntity.setBackgroundColor(GsonUtil.getGson().toJson(colorList));
            hexagonBackgroudColorSettingDAO.updateBackgroundColor(hexagonBackgroudColorSettingEntity);
            return Result.newSuccess(colorList);
        } catch (Exception e) {
            log.warn("updateHexagonBackgroundColorSetting is error", e);
        } finally {
            lockManager.unlock(lockKey);
        }
        return Result.newError(SHErrorCode.SERVER_BUSY);
    }


    private List<HexagonQrCodeResult.QrCodeResult> createQywxQrCode(CreateHexagonWxQrCodeArg arg,String ea, List<QywxAddFanQrCodeEntity> entities) {
        List<HexagonQrCodeResult.QrCodeResult> qywxQrCodeResultList = Lists.newArrayList();
        String accessToken = qywxManager.getAccessToken(ea);
        for (QywxAddFanQrCodeEntity qywxQrCode : entities) {
            HexagonQrCodeResult.QrCodeResult qrCodeResult = new HexagonQrCodeResult.QrCodeResult();
            QywxQrCodeUserMarketingRelationEntity relationEntity = qywxAddFanQrCodeUserMarketingRelationDAO.queryByUserMarketingIdAndQrCodeId(qywxQrCode.getEa(), arg.getUserMarketingId(), qywxQrCode.getId());
            if (relationEntity != null){
                qrCodeResult.setMainSceneId(relationEntity.getQywxQrCodeId());
                qrCodeResult.setSceneUrl(relationEntity.getQrCode());
                qywxQrCodeResultList.add(qrCodeResult);
            }else {
                //创建集客二维码
                ContactMeConfigArg configArg = new ContactMeConfigArg(qywxQrCode.getType(), qywxQrCode.getRemark(), GsonUtil.fromJson(qywxQrCode.getUserId(), List.class), qywxQrCode.getChannelDesc());
                QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(qywxQrCode.getEa());
                if (agentConfig == null) {
                    log.info("createHexagonFanQrCode failed agentConfig=null ea:{}", qywxQrCode.getEa());
                    continue;
                }

                if (qywxQrCode.getSkipVerify() == 0){
                    configArg.setSkipVerify(true);
                }else {
                    configArg.setSkipVerify(false);
                }

                //创建新的二维码
                String state = HEXAGON_QR_PRE + System.currentTimeMillis() + "_" + UUIDUtil.generateUID(10);
                configArg.setState(state);
                if (qywxAddFanQrCodeDAO.getByEaAndState(qywxQrCode.getEa(), state) != null){
                    log.info("createHexagonFanQrCode failed status is exist ea:{} currentState:{}", qywxQrCode.getEa(), state);
                    continue;
                }
                String configId = qywxManager.setContactMeConfig(accessToken, configArg);
                if (configId == null){
                    log.info("createHexagonAddfanQrCode failed setContactMeConfig return configId==null qywxQrCode:{}", qywxQrCode);
                    continue;
                }
                GetContactMeResult contactMeResult = qywxManager.getContanctMe(accessToken, configId);
                if (contactMeResult == null || !contactMeResult.isSuccess() || StringUtils.isEmpty(contactMeResult.getContactWay().getQrCode())){
                    log.info("createHexagonAddfanQrCode failed getContanctMe return contactMeResult==null qywxQrCode:{}", qywxQrCode);
                    continue;
                }

                QywxQrCodeUserMarketingRelationEntity userMarketingRelationEntity = new QywxQrCodeUserMarketingRelationEntity();
                userMarketingRelationEntity.setId(UUIDUtil.getUUID());
                userMarketingRelationEntity.setEa(qywxQrCode.getEa());
                userMarketingRelationEntity.setUserMarketingId(arg.getUserMarketingId());
                userMarketingRelationEntity.setQywxQrCodeId(qywxQrCode.getId());
                userMarketingRelationEntity.setConfigId(configId);
                userMarketingRelationEntity.setState(state);
                userMarketingRelationEntity.setQywxQrCodeId(qywxQrCode.getId());
                userMarketingRelationEntity.setQrCode(contactMeResult.getContactWay().getQrCode());
                userMarketingRelationEntity.setStatus(QywxQrCodeBrowseUserRelationEnum.NORMAL.getStatus());
                qywxAddFanQrCodeUserMarketingRelationDAO.insert(userMarketingRelationEntity);

                qrCodeResult.setMainSceneId(qywxQrCode.getId());
                qrCodeResult.setSceneUrl(contactMeResult.getContactWay().getQrCode());
                qywxQrCodeResultList.add(qrCodeResult);

                if (StringUtils.isNotEmpty(qywxQrCode.getMarketingEventId())) {
                    ObjectData objectData = crmV2Manager.getDetail(qywxQrCode.getEa(), SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), qywxQrCode.getMarketingEventId());
                    if (objectData != null) {
                        arg.setMarketingEventId(qywxQrCode.getMarketingEventId());
                    }
                }
                marketingPromotionSourceArgObjectRelationManager.createEntity(qywxQrCode.getEa(), state, arg);
            }

        }
        return qywxQrCodeResultList;
    }

    private List<HexagonQrCodeResult.QrCodeResult> createWxQrCode(CreateHexagonWxQrCodeArg arg, String ea, List<QrCodeResult> wxQrCodeResults) {
        List<HexagonQrCodeResult.QrCodeResult> wxQrCodeResultList =Lists.newArrayList();
        for (QrCodeResult qrCodeEntity : wxQrCodeResults) {
            String newSceneId = UUIDUtil.getUUID();
            WxServiceQrCodeUserMarketingRelationEntity entity =  wxServiceQrCodeUserMarketingRelationDAO.getByMainScenIdAndUserMarketingId(ea, String.valueOf(qrCodeEntity.getId()), arg.getUserMarketingId());
            if (entity == null) {
                WxServiceQrCodeUserMarketingRelationEntity newEntity = new WxServiceQrCodeUserMarketingRelationEntity();
                newEntity.setId(UUIDUtil.getUUID());
                newEntity.setEa(ea);
                newEntity.setSceneId(newSceneId);
                newEntity.setUserMarketingId(arg.getUserMarketingId());
                newEntity.setMainSceneId(String.valueOf(qrCodeEntity.getId()));
                wxServiceQrCodeUserMarketingRelationDAO.relateSceneIdAndBrowserUserId(newEntity);
            }

            // 获取ticket
            WechatRequestDispatchArg wechatRequestDispatchArg = new WechatRequestDispatchArg();
            wechatRequestDispatchArg.setEi(eieaConverter.enterpriseAccountToId(ea));
            wechatRequestDispatchArg.setWxAppId(qrCodeEntity.getAppId());
            wechatRequestDispatchArg.setMethod("POST");
            wechatRequestDispatchArg.setUrl(WxApiConstants.API_HOST + "cgi-bin/qrcode/create?access_token=${access_token}");
            Map<String, Object> body = new HashMap<>();
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> scene = new HashMap<>();
            scene.put("scene_str",  newSceneId);
            scene.put("fsEa", ea);
            scene.put("appId", appId);
            scene.put("qrCodeName", qrCodeEntity.getQrCodeName());
            scene.put("responseMsg", qrCodeEntity.getResponseMsg());
            scene.put("tagNames", qrCodeEntity.getTagNames());
            scene.put("marketingEventId", qrCodeEntity.getMarketingEventId());
            scene.put("creator", qrCodeEntity.getCreator());
            map.put("scene", scene);
            body.put("action_name", "QR_STR_SCENE");
            body.put("action_info", map);
            body.put("expire_seconds", 86400);

            wechatRequestDispatchArg.setBody(JSON.toJSONString(body));
            Map<String, Object> objectMap = wechatAccountManager.dispatch(wechatRequestDispatchArg, new TypeToken<HashMap<String, Object>>() {
            });
            if (objectMap == null) {
                return null;
            }

            //如果二维码已经存在,则更新二维码
            if (entity != null){
                wxServiceQrCodeUserMarketingRelationDAO.updateScenIdById(entity.getEa(), entity.getId(), newSceneId);
            }
            log.info("createWxQrCode wechatAccountManager.dispatch objectMap:{}", objectMap);
            String ticket = UrlEncoder.urlComponentEncode(objectMap.get("ticket"));
            String showUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=" + ticket;
            HexagonQrCodeResult.QrCodeResult qrCodeResult = new HexagonQrCodeResult.QrCodeResult();
            qrCodeResult.setSceneUrl(showUrl);
            qrCodeResult.setMainSceneId(String.valueOf(qrCodeEntity.getId()));
            wxQrCodeResultList.add(qrCodeResult);

            if (StringUtils.isNotEmpty(qrCodeEntity.getMarketingEventId())) {
                ObjectData objectData = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), qrCodeEntity.getMarketingEventId());
                if (objectData != null) {
                    arg.setMarketingEventId(qrCodeEntity.getMarketingEventId());
                }
            }
            marketingPromotionSourceArgObjectRelationManager.createEntity(ea, newSceneId, arg);
        }
        return wxQrCodeResultList;
    }

    private List<QrCodeResult> getQrCodeResults(List<CreateHexagonWxQrCodeArg.WxQrCode> arg, String ea) {
        //根据二维码id获取二维码详情
        List<QrCodeResult> wxQrCodeResults =Lists.newArrayList();
        for (CreateHexagonWxQrCodeArg.WxQrCode qrcode : arg) {
//            ModelResult<String> modelResult = outerServiceWechatService.transWxAppIdAndFsEaToAppId(qrcode.getWxAppId(), ea);
//            if(!modelResult.isSuccess()){
//                continue;
//            }
            // sceneId调互联接口获取二维码详情
            QueryStoreQrCodeArg queryStoreQrCodeArg = new QueryStoreQrCodeArg();
            queryStoreQrCodeArg.setAppId(qrcode.getAppId());
            queryStoreQrCodeArg.setSceneId(Long.valueOf(qrcode.getSceneId()));
            queryStoreQrCodeArg.setEnterpriseAccount(ea);
            ModelResult<Pager<QrCodeResult>> pagerModelResult = wechatQrCodeRestService.queryStoreQrCode(queryStoreQrCodeArg);
            if (!pagerModelResult.isSuccess() || pagerModelResult.getResult() == null || CollectionUtils.isEmpty(pagerModelResult.getResult().getData())) {
                log.info("HexagonServiceImpl createHexagonWxQrCode wechatQrCodeRestService.queryStoreQrCode fail, pagerModelResult:{}", pagerModelResult);
                continue;
            }
            QrCodeResult qrCodeResult = pagerModelResult.getResult().getData().get(0);
            if (qrCodeResult != null) {
                wxQrCodeResults.add(qrCodeResult);
            }
        }
        return wxQrCodeResults;
    }

    @Override
    public Result<PageResult<SimpleHexagonVO>> simpleHexagonList(SimpleHexagonListArg arg) {
        if (arg.getGroupId() == null){
            arg.setGroupId(HexagonDefaultGroupEnum.ALL.getId());
        }
        PageResult<SimpleHexagonVO> pageResult = new PageResult<>();
        List<SimpleHexagonVO> simpleHexagonList = Lists.newArrayList();
        pageResult.setResult(simpleHexagonList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        String ea = arg.getEa();
        int fsUserId = arg.getFsUserId();
        if (arg.getGroupId() == null){
            arg.setGroupId(HexagonDefaultGroupEnum.ALL.getId());
        }
        List<HexagonSiteEntity> pageList;
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        HexagonSiteQueryParam queryParam = new HexagonSiteQueryParam();
        queryParam.setEa(ea);
        queryParam.setKeyword(arg.getKeyword());
        queryParam.setStatus(arg.getStatus());
        queryParam.setMaterialTagFilter(arg.getMaterialTagFilter());

        Result<AppMenuTagVO>  appMenuTagResult = appMenuTemplateService.getMenuTagRule(ea, arg.getMenuId(), ObjectTypeEnum.HEXAGON_SITE.getType());
        AppMenuTagVO appMenuTagVO = appMenuTagResult.isSuccess() ? appMenuTagResult.getData() : null;
        if (appMenuTagVO != null) {
            MaterialTagFilterArg materialTagFilterArg = queryParam.getMaterialTagFilter();
            if (materialTagFilterArg == null) {
                materialTagFilterArg = new MaterialTagFilterArg();
            }
            materialTagFilterArg.setMenuType(appMenuTagVO.getTagOperator());
            materialTagFilterArg.setMenuMaterialTagIds(appMenuTagVO.getTagIdList());
            queryParam.setStrictCheckGroup(true);
            queryParam.setMaterialTagFilter(materialTagFilterArg);
            pageList = hexagonSiteDAO.getAccessiblePage(queryParam, page);
        } else if (arg.getGroupId().equals(HexagonDefaultGroupEnum.ALL.getId())) {
            queryParam.setUserId(fsUserId);
            // 是否需要严格校验分组
            queryParam.setStrictCheckGroup(appMenuTemplateService.needStrictCheckGroup(ea, fsUserId, arg.getMenuId()));
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getMenuId());
            queryParam.setUserId(fsUserId);
            queryParam.setPermissionGroupIdList(Lists.newArrayList(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList())));
            pageList = hexagonSiteDAO.getAccessiblePage(queryParam, page);
        } else if (arg.getGroupId().equals(HexagonDefaultGroupEnum.CREATED_BY_ME.getId())) {
            pageList = hexagonSiteDAO.pageQueryHexagonEntityByMe(ea, fsUserId, arg.getStatus(), arg.getKeyword(), null, false, page);
        } else if (arg.getGroupId().equals(HexagonDefaultGroupEnum.NO_GROUP.getId())){
            pageList = hexagonSiteDAO.pageQueryHexagonEntityByUngrouped(ea, arg.getStatus(), arg.getKeyword(), ObjectTypeEnum.HEXAGON_SITE.getType(), null, false, page);
        } else {
            queryParam.setUserId(fsUserId);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getMenuId());
            Set<String> groupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            queryParam.setStrictCheckGroup(true);
            if (!groupIdSet.contains(arg.getGroupId())){
                pageList = Lists.newArrayList();
            } else {
               // queryParam.setPermissionGroupIdList(Lists.newArrayList(groupIdSet));
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(arg.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                pageList = hexagonSiteDAO.getAccessiblePage(queryParam, page);
            }
        }
        pageResult.setTotalCount(page.getTotalNum());
        if (CollectionUtils.isEmpty(pageList)){
            return Result.newSuccess(pageResult);
        }
        List<String> objectIds = pageList.stream().map(HexagonSiteEntity::getId).collect(Collectors.toList());
        Map<String, String> idToCoverUrlMap = hexagonManager.getHexagonSiteCoverUrlMap(ea, objectIds);
        List<ObjectTopEntity> objectTopEntityList = objectTopManager.getByObjectIdList(objectIds);
        Set<String> objectIdTopSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(objectTopEntityList)) {
            objectIdTopSet = objectTopEntityList.stream().map(ObjectTopEntity::getObjectId).collect(Collectors.toSet());
        }
        Map<String, Integer> materielMap = Maps.newHashMap();
        for(HexagonSiteEntity hexagonSiteEntity : pageList){
            materielMap.put(hexagonSiteEntity.getId(), ObjectTypeEnum.HEXAGON_SITE.getType());
        }
        Map<String, List<MarketingActivityObjectInfoDTO.ActivityObjectInfo>>  marketingActivityObjectInfoMap = marketingActivityManager.getActivityIdsByObject(materielMap, ea, fsUserId);
        List<HexagonPageEntity> homePageList = hexagonPageDAO.getHomePageByIds(ea, objectIds);
        Map<String, HexagonPageEntity> sitePageMap = homePageList.stream().collect(Collectors.toMap(HexagonPageEntity::getHexagonSiteId, Function.identity(), (v1, v2) -> v1));
        List<String> homePageIds = homePageList.stream().map(HexagonPageEntity::getId).collect(Collectors.toList());
        Map<String, String> pageIdMap = homePageList.stream().collect(Collectors.toMap(HexagonPageEntity::getId, HexagonPageEntity::getHexagonSiteId, (v1, v2) -> v1));
        //小程序图片分享图
        List<PhotoEntity> miniPhotoEntities = photoManager.queryPhotosByTypeAndTargetIdsNoReset(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType(), homePageIds);
        Map<String, PhotoEntity> coverCutMiniAppPhotoMap = miniPhotoEntities.stream().collect(Collectors.toMap(o -> pageIdMap.get(o.getTargetId()), Function.identity(), (v1, v2) -> v1));
        //朋友圈h5图片分享图
        List<PhotoEntity> h5PhotoEntities = photoManager.queryPhotosByTypeAndTargetIdsNoReset(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(),homePageIds);
        Map<String, PhotoEntity> coverCutH5PhotoMap = h5PhotoEntities.stream().collect(Collectors.toMap(o -> pageIdMap.get(o.getTargetId()), Function.identity(), (v1, v2) -> v1));
        //900*500分享图
        List<PhotoEntity> ordinaryPhotoEntities = photoManager.queryPhotosByTypeAndTargetIdsNoReset(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType(),homePageIds);
        Map<String, PhotoEntity> coverCutOrdinaryPhotoMap = ordinaryPhotoEntities.stream().collect(Collectors.toMap(o -> pageIdMap.get(o.getTargetId()), Function.identity(), (v1, v2) -> v1));
        for (HexagonSiteEntity hexagonSiteEntity : pageList) {
            SimpleHexagonVO simpleHexagonVO = new SimpleHexagonVO();
            simpleHexagonVO.setTop(objectIdTopSet.contains(hexagonSiteEntity.getId()));
            simpleHexagonVO.setName(hexagonSiteEntity.getName());
            simpleHexagonVO.setId(hexagonSiteEntity.getId());
            simpleHexagonVO.setCoverUrl(idToCoverUrlMap.get(hexagonSiteEntity.getId()));
            simpleHexagonVO.setCreateTime(hexagonSiteEntity.getCreateTime());
            simpleHexagonVO.setUpdateTime(hexagonSiteEntity.getUpdateTime());
            // 获取裁剪封面图
            PhotoEntity cutMiniApp = coverCutMiniAppPhotoMap.get(simpleHexagonVO.getId());
            if(cutMiniApp != null){
                simpleHexagonVO.setSharePicMiniAppCutUrl(cutMiniApp.getThumbnailUrl());
            }
            PhotoEntity cutH5Photo = coverCutH5PhotoMap.get(simpleHexagonVO.getId());
            if(cutH5Photo != null){
                simpleHexagonVO.setSharePicH5CutUrl(cutH5Photo.getThumbnailUrl());
            }
            PhotoEntity cutOrdinary = coverCutOrdinaryPhotoMap.get(simpleHexagonVO.getId());
            if(cutOrdinary != null){
                simpleHexagonVO.setSharePicOrdinaryCutUrl(cutOrdinary.getThumbnailUrl());
            }
            HexagonPageEntity homePage = sitePageMap.get(hexagonSiteEntity.getId());
            if(homePage != null){
                simpleHexagonVO.setShareTitle(homePage.getShareTitle());
                simpleHexagonVO.setShareDesc(homePage.getShareDesc());
            }
            // 设置营销活动
            List<MarketingActivityObjectInfoDTO.ActivityObjectInfo> objectInfoDTO = marketingActivityObjectInfoMap.get(hexagonSiteEntity.getId());
            if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(objectInfoDTO)){
                String id = objectInfoDTO.get(0).getId();
                Result<Boolean> booleanResult = spreadTaskService.spreadTaskIsRevocation(id);
                if (booleanResult == null || !booleanResult.getData()) {
                    simpleHexagonVO.setMarketingActivityId(id);
                    simpleHexagonVO.setMarketingActivityTitle(objectInfoDTO.get(0).getName());
                }
            }
            simpleHexagonList.add(simpleHexagonVO);
        }
        return Result.newSuccess(pageResult);
    }
}