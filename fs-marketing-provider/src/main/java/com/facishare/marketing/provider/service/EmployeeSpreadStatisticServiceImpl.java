package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.result.BaseUserInfoResult;
import com.facishare.marketing.api.arg.BatchObjectUserMarketingStatisticsArg;
import com.facishare.marketing.api.arg.kis.GetClueContributionListArg;
import com.facishare.marketing.api.arg.kis.GetEmployeeMarketingActivityObjectDetailsArg;
import com.facishare.marketing.api.result.BoardCardResult;
import com.facishare.marketing.api.result.ObjectUserMarketingStatisticsResult;
import com.facishare.marketing.api.result.kis.GetClueContributionListResult;
import com.facishare.marketing.api.result.kis.GetEmployeeMarketingActivityObjectDetailsResult;
import com.facishare.marketing.api.result.kis.GetLastestEmployeeStatisticUnitResult;
import com.facishare.marketing.api.service.EmployeeSpreadStatisticService;
import com.facishare.marketing.api.service.StatisticService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.QywxFileTypeLogoEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivitySpreadRecordDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityStatisticDao;
import com.facishare.marketing.provider.dao.photoLibrary.PhotoLibraryDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dto.SpreadRecordAndBoardDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO;
import com.facishare.marketing.provider.dto.kis.GetActivityDayStatisticDTO;
import com.facishare.marketing.provider.dto.kis.GetFirstNodeStatisticDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeStatisticEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivitySpreadRecordEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivityStatisticEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.GetMarketingActivityDetailVo;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName EmployeeSpreadStatisticServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2019/2/28 3:42 PM
 */
@Service("employeeSpreadStatisticService")
@Slf4j
public class EmployeeSpreadStatisticServiceImpl implements EmployeeSpreadStatisticService {
    @Autowired
    private MarketingActivityEmployeeStatisticDAO marketingActivityEmployeeStatisticDAO;
    @Autowired
    private NoticeDAO noticeDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private OutLinkDAO outLinkDAO;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;
    @Autowired
    private VideoDAO videoDAO;
    @Autowired
    private MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;
    @Autowired
    private PrivateMessageDao privateMessageDao;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private KmUserManager kmUserManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingActivitySpreadRecordDAO marketingActivitySpreadRecordDAO;
    @Autowired
    private MarketingActivityEmployeeDayStatisticDAO marketingActivityEmployeeDayStatisticDAO;
    @Autowired
    private MarketingActivityStatisticDao marketingActivityStatisticDao;
    @Autowired
    private SpreadStatDao spreadStatDao;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private BoardCardDao boardCardDao;
    @Autowired
    private BoardManager boardManager;
    @Autowired
    private PhotoSelectorDAO photoSelectorDAO;
    @Autowired
    private PhotoLibraryDAO photoLibraryDAO;

    @Autowired
    private StatisticService statisticService;

    @Override
    public Result<PageResult<GetLastestEmployeeStatisticUnitResult>> getLastestEmployeeStatistic(String ea, Integer userId, Integer ei, Integer pageSize, Integer pageNum, Long time) {
        if (pageSize == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic pageSize invalid");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (pageNum == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic pageNum invalid");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isBlank(ea)) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic ea is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == userId) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic userId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == ei) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic ei is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (time == null) {
            time = new Date().getTime();
        }

        Page page = new Page(pageNum, pageSize, true);

        PageResult<GetLastestEmployeeStatisticUnitResult> pageResult = new PageResult();
        List<GetLastestEmployeeStatisticUnitResult> resultList = new ArrayList<>();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);

        //List<Integer> userIdsList = new ArrayList<>();
        //userIdsList.add(userId);

        List<SpreadRecordAndBoardDTO> spreadRecordAndBoardDTOList = marketingActivitySpreadRecordDAO.querySpreadRecordAndBoardByEaAndUserId(ea, userId, page);
        if (CollectionUtils.isNotEmpty(spreadRecordAndBoardDTOList)) {
            pageResult.setPageNum(page.getPageNo());
            pageResult.setPageSize(page.getPageSize());
            pageResult.setTotalCount(page.getTotalNum());

            Set<String> marketingActivityIdsSet = new HashSet<>();
            for (SpreadRecordAndBoardDTO spreadRecordAndBoardDTO : spreadRecordAndBoardDTOList) {
                if (StringUtils.isNotBlank(spreadRecordAndBoardDTO.getMarketingActivityId())) {
                    marketingActivityIdsSet.add(spreadRecordAndBoardDTO.getMarketingActivityId());
                }
            }
            List<String> marketingActivityIdsList = new ArrayList<>(marketingActivityIdsSet);

            // 批量查询看板信息
            List<String> boardCardIds = spreadRecordAndBoardDTOList.stream().filter(data -> data.getDataType().equals(LastestEmployeeStatisticDataTypeEnum.BOARD.getType())).map(
                SpreadRecordAndBoardDTO::getId)
                .collect(Collectors.toList());
            //Map<String, BoardCardEntity> boardCardEntityMap = Maps.newHashMap();
            Map<String, BoardCardResult> boardCardResultMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(boardCardIds)) {
                List<BoardCardEntity> boardCardEntityList = boardCardDao.getBoardCardByIds(boardCardIds);
                if (CollectionUtils.isNotEmpty(boardCardEntityList)) {
                    //boardCardEntityMap = boardCardEntityList.stream().collect(Collectors.toMap(BoardCardEntity::getId, data -> data, (v1, v2) -> v1));
                    boardCardResultMap = boardManager.doConvertBoardEntityToResult(boardCardEntityList);
                    boardManager.fillStatisticDataToBoardCardResult(ea, boardCardResultMap.values());
                    boardManager.fillTargetObjectNameToBoardCardResult(ea, boardCardResultMap.values());
                    boardManager.fillLatestBoardCardActivityToBoardCardResult(ea, boardCardResultMap.values());
                    boardManager.fillPrincipalsNameToBoardCardResult(ea, boardCardResultMap.values());
                }
            }
            if (CollectionUtils.isNotEmpty(marketingActivityIdsList)) {

                Map<String, ObjectData> marketingActivityDataMap = new HashMap<>();

                PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
                marketingActivityArg.setPageNumber(0);
                marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                marketingActivityArg.setPageSize(marketingActivityIdsList.size());
                marketingActivityArg.setIds(marketingActivityIdsList);
                marketingActivityArg.setIsMankeep(true);
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> marketingActivityListVo = marketingActivityCrmManager.listMarketingActivity(ea, -10000, marketingActivityArg);
                if (null == marketingActivityListVo || null == marketingActivityListVo.getDataList()) {
                    log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic listMarketingActivity failed, marketingActivityIdsList={}", marketingActivityIdsList);
                    return new Result<>(SHErrorCode.SUCCESS, pageResult);
                }

                List<ObjectData> dataList = marketingActivityListVo.getDataList();
                for (ObjectData data : dataList) {
                    marketingActivityDataMap.put(data.getId(), data);
                }

                List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityList = marketingActivityExternalConfigDao.getByMarketingActivityIds(marketingActivityIdsList);
                Map<String, MarketingActivityExternalConfigEntity> marketingActivityIdMarketingActivityExternalConfigEntityMap = new HashMap<>();

                if (CollectionUtils.isNotEmpty(marketingActivityExternalConfigEntityList)) {
                    Map<String, String> marketingActivityIdMetaralIdMap = new HashMap<>();
                    Map<String, String> marketingActivityIdNoticeIdMap = new HashMap<>();

                    Set<String> noticeIdsSet = new HashSet<>();
                    Set<String> productIdsSet = new HashSet<>();
                    Set<String> articleIdsSet = new HashSet<>();
                    Set<String> activityIdsSet = new HashSet<>();
                    Set<String> cardIdsSet = new HashSet<>();
                    Set<String> qrPosterSet =new HashSet<>();
                    Set<String> hexagonIdSet = new HashSet<>();
                    Set<String> outLinkIdSet = new HashSet<>();
                    Set<String> fileIdSet = new HashSet<>();
                    Set<String> videoIdSet = new HashSet<>();
                    Set<String> cardMarketingActivityIdsSet = new HashSet<>();

                    for (MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity : marketingActivityExternalConfigEntityList) {
                        marketingActivityIdMarketingActivityExternalConfigEntityMap.put(marketingActivityExternalConfigEntity.getMarketingActivityId(), marketingActivityExternalConfigEntity);
                        if (marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() || marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType() || marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType()) {
                            noticeIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            marketingActivityIdNoticeIdMap.put(marketingActivityExternalConfigEntity.getMarketingActivityId(), marketingActivityExternalConfigEntity.getAssociateId());
                        } else {
                            marketingActivityIdMetaralIdMap.put(marketingActivityExternalConfigEntity.getMarketingActivityId(), marketingActivityExternalConfigEntity.getAssociateId());
                            if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.PRODUCT.getType()) {
                                productIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            } else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.ARTICLE.getType()) {
                                articleIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            } else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.ACTIVITY.getType()) {
                                activityIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            } else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.CARD.getType()) {
                                cardIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                                cardMarketingActivityIdsSet.add(marketingActivityExternalConfigEntity.getMarketingActivityId());
                            } else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.QR_POSTER.getType()) {
                                qrPosterSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            }else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.HEXAGON_SITE.getType()){
                                hexagonIdSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            }else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.OUT_LINK.getType()){
                                outLinkIdSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            }else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.FILE.getType()){
                                fileIdSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            }else if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.VIDEO.getType()){
                                videoIdSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            }
                        }
                    }

                    Map<String, Map<String, String>> noticeIdMaterialMap = new HashMap<>();
                    List<String> noticeIdsList = new ArrayList<>(noticeIdsSet);
                    if (CollectionUtils.isNotEmpty(noticeIdsList)) {
                        List<NoticeEntity> noticeEntityList = noticeDAO.queryNoticeByIds(noticeIdsList);
                        for (NoticeEntity noticeEntity : noticeEntityList) {
                            Map<String, String> materialMap = new HashMap<>();
                            if (noticeEntity.getContentType() == NoticeContentTypeEnum.PRODUCT.getType()) {
                                productIdsSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.ARTICLE.getType()) {
                                articleIdsSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.ACTIVITY.getType()) {
                                activityIdsSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.QR_POSTER.getType()) {
                                qrPosterSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.HEXAGON.getType()){
                                hexagonIdSet.add(noticeEntity.getContent());
                            }else if (noticeEntity.getContentType() == ObjectTypeEnum.OUT_LINK.getType()){
                                outLinkIdSet.add(noticeEntity.getContent());
                            }else if (noticeEntity.getContentType() == ObjectTypeEnum.FILE.getType()){
                                fileIdSet.add(noticeEntity.getContent());
                            }else if (noticeEntity.getContentType() == ObjectTypeEnum.VIDEO.getType()){
                                videoIdSet.add(noticeEntity.getContent());
                            }

                            materialMap.put("objectType", Integer.valueOf(NoticeContentTypeEnum.fromType(noticeEntity.getContentType()).toObjectType()).toString());
                            materialMap.put("objectId", noticeEntity.getContent());
                            noticeIdMaterialMap.put(noticeEntity.getId(), materialMap);
                        }
                    }

                    Map<String, ProductEntity> productEntityMap = new HashMap<>();
                    List<String> productIdsList = new ArrayList<>(productIdsSet);
                    Map<String, String> productPicMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(productIdsList)) {
                        List<ProductEntity> productEntityList = productDAO.getByIds(productIdsList);
                        for (ProductEntity productEntity : productEntityList) {
                            productEntityMap.put(productEntity.getId(), productEntity);
                        }
                        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), productIdsList);
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            productPicMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v.getUrl(), (k1, k2) -> k1));
                        }
                    }

                    Map<String, ArticleEntity> articleEntityMap = new HashMap<>();
                    List<String> articleIdsList = new ArrayList<>(articleIdsSet);
                    Map<String, String> articlePicMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(articleIdsList)) {
                        List<ArticleEntity> articleEntityList = articleDAO.getByIds(articleIdsList);
                        for (ArticleEntity articleEntity : articleEntityList) {
                            articleEntityMap.put(articleEntity.getId(), articleEntity);
                        }
                        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleIdsList);
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            articlePicMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v.getUrl(), (k1, k2) -> k1));
                        }
                    }

                    Map<String, ActivityEntity> activityEntityMap = new HashMap<>();
                    List<String> activityIdsList = new ArrayList<>(activityIdsSet);
                    Map<String, String> activityPicMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(activityIdsList)) {
                        List<ActivityEntity> activityEntityList = activityDAO.getByIds(activityIdsList);
                        for (ActivityEntity activityEntity : activityEntityList) {
                            activityEntityMap.put(activityEntity.getId(), activityEntity);
                        }
                        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityIdsList);
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            activityPicMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v.getUrl(), (k1, k2) -> k1));
                        }
                    }

                    Map<String, CardEntity> cardEntityMap = new HashMap<>();
                    Map<String, Integer> messageUserCountMap = new HashMap<>();
                    List<String> cardIdsList = new ArrayList<>(cardIdsSet);
                    if (CollectionUtils.isNotEmpty(cardIdsList)) {
                        List<CardEntity> cardEntityList = cardDAO.queryCardInfoByIds(cardIdsList);
                        for (CardEntity cardEntity : cardEntityList) {
                            cardEntityMap.put(cardEntity.getId(), cardEntity);
                            Integer messageUserCount = privateMessageDao.getPrivateSessionUserCount(cardEntity.getUid(), null, null, false);
                            if (null != messageUserCount) {
                                messageUserCountMap.put(cardEntity.getId(), messageUserCount);
                            }
                        }
                    }

                    Map<String, QRPosterEntity> qrPosterEntityMap = new HashMap<>();
                    List<String> qrPosterIdsList = new ArrayList<>(qrPosterSet);
                    if (CollectionUtils.isNotEmpty(qrPosterIdsList)) {
                        List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getAllByIds(qrPosterIdsList);
                        for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
                            qrPosterEntityMap.put(qrPosterEntity.getId(), qrPosterEntity);
                        }
                    }

                    List<String> hexagonIdList = new ArrayList<>(hexagonIdSet);
                    Map<String, HexagonBaseInfoDTO> hexagonBaseInfoDTOMap = null;
                    if (CollectionUtils.isNotEmpty(hexagonIdList)){
                        hexagonBaseInfoDTOMap = hexagonManager.getHexagonBaseInfoById(hexagonIdList, ea);
                    }

                    Map<String, OutLinkEntity> OutLinkEntityMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(outLinkIdSet)){
                        List<OutLinkEntity> outLinkEntities = outLinkDAO.getByIds(new ArrayList<>(outLinkIdSet));
                        for (OutLinkEntity entity : outLinkEntities) {
                            OutLinkEntityMap.put(entity.getId(), entity);
                        }
                    }
                    //文件
                    Map<String, FileEntity> fileEntityMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(fileIdSet)){
                        List<FileEntity> fileEntities = fileLibraryDAO.getByIdList(new ArrayList<>(fileIdSet));
                        for (FileEntity entity : fileEntities) {
                            fileEntityMap.put(entity.getId(), entity);
                        }
                    }
                    //视频
                    Map<String, VideoEntity> videoEntityMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(videoIdSet)){
                        List<VideoEntity> videoEntities = videoDAO.getByIds(new ArrayList<>(videoIdSet));
                        for (VideoEntity entity : videoEntities) {
                            videoEntityMap.put(entity.getId(), entity);
                        }
                    }

                    //所有物料的id集合
                    Set<String> resultIdsSet = new HashSet<>();
                    resultIdsSet.addAll(productIdsSet);
                    resultIdsSet.addAll(articleIdsSet);
                    resultIdsSet.addAll(activityIdsSet);
                    resultIdsSet.addAll(cardIdsSet);
                    if(CollectionUtils.isNotEmpty(qrPosterSet)){
                        List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getAllByIds(Lists.newArrayList(qrPosterSet));
                        List<String> qrPostTargetId = qrPosterEntities.stream().filter(o-> StringUtils.isNotBlank(o.getTargetId())).map(QRPosterEntity::getTargetId).collect(Collectors.toList());
                        resultIdsSet.addAll(qrPostTargetId);
                    }
                    resultIdsSet.addAll(hexagonIdSet);
                    resultIdsSet.addAll(outLinkIdSet);
                    resultIdsSet.addAll(fileIdSet);
                    resultIdsSet.addAll(videoIdSet);

                    BatchObjectUserMarketingStatisticsArg statisticsArg = new BatchObjectUserMarketingStatisticsArg();
                    statisticsArg.setEa(ea);
                    statisticsArg.setUserId(userId);
                    statisticsArg.setMarketingActivityIdList(marketingActivityIdsList);
                    statisticsArg.setSpreadFsUserIdList(Lists.newArrayList(userId));
                    statisticsArg.setObjectIds(Lists.newArrayList(resultIdsSet));
                    Result<List<ObjectUserMarketingStatisticsResult>> statisticsResult = statisticService.batchGetRadarObjectMarketingActivityStatistics(statisticsArg);
                    Map<String, ObjectUserMarketingStatisticsResult> marketingActivityIdToStatisticsMap = Maps.newHashMap();
                    if (statisticsResult.isSuccess() && org.apache.commons.collections.CollectionUtils.isNotEmpty(statisticsResult.getData())) {
                        statisticsResult.getData().forEach(e -> marketingActivityIdToStatisticsMap.put(e.getMarketingActivityId(), e));
                    }


                    List<MarketingActivityStatisticEntity> marketingActivityStatisticEntityList = null;
                    Map<String, MarketingActivityStatisticEntity> cardMarketingActivityStatisticEntityMap = new HashMap<>();
                    List<String> cardMarketingActivityIdsList = new ArrayList(cardMarketingActivityIdsSet);
                    if (CollectionUtils.isNotEmpty(cardMarketingActivityIdsList)) {
                        marketingActivityStatisticEntityList = marketingActivityStatisticDao.queryByMarketingActivityIds(cardMarketingActivityIdsList);
                        if (CollectionUtils.isNotEmpty(marketingActivityStatisticEntityList)) {
                            for (MarketingActivityStatisticEntity marketingActivityStatisticEntity : marketingActivityStatisticEntityList) {
                                cardMarketingActivityStatisticEntityMap.put(marketingActivityStatisticEntity.getMarketingActivityId(), marketingActivityStatisticEntity);
                            }
                        }
                    }

                    Map<String, Integer> clueCountMap = customizeFormClueManager.batchCountClueNumByFsUserIdAndMarketingActivity(userId, ea, marketingActivityIdsList, false, 0);
                    for (SpreadRecordAndBoardDTO spreadRecordAndBoardDTO : spreadRecordAndBoardDTOList) {

                        GetLastestEmployeeStatisticUnitResult result = new GetLastestEmployeeStatisticUnitResult();
                        if (spreadRecordAndBoardDTO.getDataType().equals(LastestEmployeeStatisticDataTypeEnum.BOARD.getType())) {
                            // 卡片信息
                            buildBoardInfoResult(spreadRecordAndBoardDTO, boardCardResultMap, resultList);
                            continue;
                        }
                        //跟产品和鸣哥对过,后续访问人次人数都以营销动态为准
//                        MarketingActivityEmployeeStatisticEntity marketingActivityEmployeeStatisticEntity = marketingActivityEmployeeStatisticEntityMap
//                            .get(spreadRecordAndBoardDTO.getMarketingActivityId());
                        ObjectUserMarketingStatisticsResult marketingActivityEmployeeStatisticEntity = marketingActivityIdToStatisticsMap.get(spreadRecordAndBoardDTO.getMarketingActivityId());

                        ObjectData marketingActivityData = marketingActivityDataMap.get(spreadRecordAndBoardDTO.getMarketingActivityId());
                        if (null == marketingActivityData) {
                            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic marketingActivityData not found, marketingActivityId={}",
                                spreadRecordAndBoardDTO.getMarketingActivityId());
                        } else {
                            result.setTitle(marketingActivityData.getName());
                        }

                        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityIdMarketingActivityExternalConfigEntityMap
                            .get(spreadRecordAndBoardDTO.getMarketingActivityId());
                        if (null == marketingActivityExternalConfigEntity) {
                            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic marketingActivityExternalConfigEntity not found, marketingActivityId={}",
                                spreadRecordAndBoardDTO.getMarketingActivityId());
                            continue;
                        }

                        result.setSpreadTime(spreadRecordAndBoardDTO.getUpdateTime().getTime());
                        result.setMarketingActivityId(spreadRecordAndBoardDTO.getMarketingActivityId());

                        Integer objectType;
                        String objectId;
                        Integer spreadType = null;

                        if (marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() || marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType() || marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType()) {
                            String noticeId = marketingActivityIdNoticeIdMap.get(spreadRecordAndBoardDTO.getMarketingActivityId());
                            if (StringUtils.isBlank(noticeId)) {
                                log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic noticeId not found, marketingActivityId={}",
                                    spreadRecordAndBoardDTO.getMarketingActivityId());
                                continue;
                            }

                            Map<String, String> material = noticeIdMaterialMap.get(noticeId);
                            if (material == null) {
                                log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic material not found, noticeId={}", noticeId);
                                continue;
                            }

                            objectType = Integer.valueOf(material.get("objectType"));
                            objectId = material.get("objectId");

                            GetMarketingActivityDetailVo vo = marketingActivityCrmManager.getByIdMarketingActivity(ea, -10000, spreadRecordAndBoardDTO.getMarketingActivityId());
                            if (vo != null) {
                                spreadType = vo.getSpreadType();
                            }
                        } else {
                            objectType = marketingActivityExternalConfigEntity.getAssociateIdType();
                            objectId = marketingActivityExternalConfigEntity.getAssociateId();
                            spreadType = MarketingActivitySpreadTypeEnum.PERSONAL_SPREAD.getSpreadType();
                        }

                        if (null == objectType) {
                            log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic objectType not found, marketingActivityExternalConfigEntity={}",
                                marketingActivityExternalConfigEntity);
                            continue;
                        }

                        if (StringUtils.isBlank(objectId)) {
                            log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic objectId not found, marketingActivityExternalConfigEntity={}",
                                marketingActivityExternalConfigEntity);
                            continue;
                        }

                        result.setSpreadType(spreadType);

                        if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            ProductEntity productEntity = productEntityMap.get(objectId);
                            String coverUrl = productPicMap.get(productEntity.getId());

                            result.setObjectType(ObjectTypeEnum.PRODUCT.getType());
                            result.setObjectId(productEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(productEntity.getName());
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            ArticleEntity articleEntity = articleEntityMap.get(objectId);
                            String coverUrl = articlePicMap.get(articleEntity.getId());

                            result.setObjectType(ObjectTypeEnum.ARTICLE.getType());
                            result.setObjectId(articleEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(articleEntity.getTitle());

                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            ActivityEntity activityEntity = activityEntityMap.get(objectId);
                            String coverUrl = activityPicMap.get(activityEntity.getId());

                            result.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                            result.setObjectId(activityEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(activityEntity.getTitle());

                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }

                            result.setActivityStartTime(activityEntity.getStartTime().getTime());
                            result.setActivityEndTime(activityEntity.getEndTime().getTime());
                            result.setActivityDetailSiteId(activityEntity.getActivityDetailSiteId());
                        } else if (objectType == ObjectTypeEnum.CARD.getType()) {
                            CardEntity cardEntity = cardEntityMap.get(objectId);
                            if (null == cardEntity) {
                                log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic cardEntity not found, objectId={}", objectId);
                                continue;
                            }

                            result.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_EMPLOYEESPREADSTATISTICSERVICEIMPL_585));
                            result.setObjectType(ObjectTypeEnum.CARD.getType());
                            result.setObjectId(cardEntity.getId());
                            result.setObjectTitle(cardEntity.getName());
                            result.setCardCompanyName(cardEntity.getCompanyName());
                            result.setCoverUrl(cardEntity.getAvatar());

                            MarketingActivityStatisticEntity marketingActivityStatisticEntity = cardMarketingActivityStatisticEntityMap
                                .get(spreadRecordAndBoardDTO.getMarketingActivityId());

                            if (null != marketingActivityStatisticEntity) {
                                result.setLookUpCount(marketingActivityStatisticEntity.getLookUpCount());
                                result.setSpreadCount(marketingActivityStatisticEntity.getSpreadCount());
                            }

                            Integer messageUserCount = messageUserCountMap.get(objectId);
                            result.setMessageUserCount(messageUserCount);
                        } else if (objectType == ObjectTypeEnum.QR_POSTER.getType()) {
                            QRPosterEntity qrPosterEntity = qrPosterEntityMap.get(objectId);
                            String coverUrl = fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), ea, false);

                            result.setObjectType(ObjectTypeEnum.QR_POSTER.getType());
                            result.setObjectId(qrPosterEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(qrPosterEntity.getTitle());

                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        }else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
                            if (hexagonBaseInfoDTOMap != null && hexagonBaseInfoDTOMap.get(objectId) != null){
                                HexagonBaseInfoDTO hexagonBaseInfoDTO =  hexagonBaseInfoDTOMap.get(objectId);
                                result.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                                result.setObjectId(hexagonBaseInfoDTO.getId());
                                result.setCoverUrl(hexagonBaseInfoDTO.getCoverUrl());
                                result.setObjectTitle(hexagonBaseInfoDTO.getTitle());
                            }
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.OUT_LINK.getType()) {
                            if (OutLinkEntityMap.get(objectId) != null) {
                                OutLinkEntity entity = OutLinkEntityMap.get(objectId);
                                result.setObjectType(ObjectTypeEnum.OUT_LINK.getType());
                                result.setObjectId(entity.getId());
                                result.setCoverUrl(entity.getCover());
                                result.setObjectTitle(entity.getName());
                            }
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.FILE.getType()) {
                            if (fileEntityMap.get(objectId) != null) {
                                FileEntity entity = fileEntityMap.get(objectId);
                                result.setObjectType(ObjectTypeEnum.FILE.getType());
                                result.setObjectId(entity.getId());
                                result.setCoverUrl(QywxFileTypeLogoEnum.getLogoByType(entity.getExt()));
                                result.setObjectTitle(entity.getFileName());
                            }
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.VIDEO.getType()) {
                            if (videoEntityMap.get(objectId) != null) {
                                VideoEntity entity = videoEntityMap.get(objectId);
                                result.setObjectType(ObjectTypeEnum.VIDEO.getType());
                                result.setObjectId(entity.getId());
                                result.setCoverUrl(entity.getImage4H5());
                                result.setObjectTitle(entity.getVideoName());
                            }
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLookUpUserCount(marketingActivityEmployeeStatisticEntity.getLookUpUserCount());
                                result.setForwardUserCount(marketingActivityEmployeeStatisticEntity.getForwardUserCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.IMAGE.getType()) {
                            result.setObjectType(ObjectTypeEnum.IMAGE.getType());
                            result.setObjectId(objectId);
                            PhotoSelectorEntity photoSelectorEntity = photoSelectorDAO.getById(objectId);
                            if (photoSelectorEntity == null) {
                                PhotoLibraryEntity libraryEntity = photoLibraryDAO.getById(objectId);
                                if (libraryEntity != null) {
                                    result.setCoverUrl(fileV2Manager.getUrlByPath(libraryEntity.getPhotoPath(),ea,false));
                                    result.setTitle(libraryEntity.getPhotoName());
                                }
                            } else {
                                result.setCoverUrl(fileV2Manager.getUrlByPath(photoSelectorEntity.getPhotoPath(),ea,false));
                                result.setTitle(photoSelectorEntity.getPhotoName());
                            }
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        }
                        result.setDataType(LastestEmployeeStatisticDataTypeEnum.SPREAD.getType());
                        resultList.add(result);
                    }
                } else {
                    for (SpreadRecordAndBoardDTO spreadRecordAndBoardDTO : spreadRecordAndBoardDTOList) {
                        if (spreadRecordAndBoardDTO.getDataType().equals(LastestEmployeeStatisticDataTypeEnum.BOARD.getType())) {
                            // 卡片信息
                            buildBoardInfoResult(spreadRecordAndBoardDTO, boardCardResultMap, resultList);
                        }
                    }
                }
            } else {
                // 只有看板信息/且看板信息未关联营销活动
                for (SpreadRecordAndBoardDTO spreadRecordAndBoardDTO : spreadRecordAndBoardDTOList) {
                    if (spreadRecordAndBoardDTO.getDataType().equals(LastestEmployeeStatisticDataTypeEnum.BOARD.getType())) {
                        // 卡片信息
                        buildBoardInfoResult(spreadRecordAndBoardDTO, boardCardResultMap, resultList);
                    }
                }
            }
        }

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<PageResult<GetLastestEmployeeStatisticUnitResult>> getLastestEmployeeStatisticForPartner(String outTenantId, int outUserId, String upstreamEa, Integer pageSize, Integer pageNum, Long time) {
        if (pageSize == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic pageSize invalid");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (pageNum == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic pageNum invalid");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isBlank(outTenantId)) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic outTenantId is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (0 == outUserId) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic outUserId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == upstreamEa) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic upstreamEa is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (time == null) {
            time = new Date().getTime();
        }

        Page page = new Page(pageNum, pageSize, true);

        PageResult<GetLastestEmployeeStatisticUnitResult> pageResult = new PageResult();
        List<GetLastestEmployeeStatisticUnitResult> resultList = new ArrayList<>();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);

        List<SpreadRecordAndBoardDTO> spreadRecordAndBoardDTOList = marketingActivitySpreadRecordDAO.querySpreadRecordByEaAndUserIdForPartner(outTenantId, outUserId,upstreamEa, page);
        if (CollectionUtils.isNotEmpty(spreadRecordAndBoardDTOList)) {
            pageResult.setPageNum(page.getPageNo());
            pageResult.setPageSize(page.getPageSize());
            pageResult.setTotalCount(page.getTotalNum());
            Set<String> marketingActivityIdsSet = new HashSet<>();
            for (SpreadRecordAndBoardDTO spreadRecordAndBoardDTO : spreadRecordAndBoardDTOList) {
                if (StringUtils.isNotBlank(spreadRecordAndBoardDTO.getMarketingActivityId())) {
                    marketingActivityIdsSet.add(spreadRecordAndBoardDTO.getMarketingActivityId());
                }
            }
            List<String> marketingActivityIdsList = new ArrayList<>(marketingActivityIdsSet);

            if (CollectionUtils.isNotEmpty(marketingActivityIdsList)) {

                Map<String, ObjectData> marketingActivityDataMap = new HashMap<>();

                PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
                marketingActivityArg.setPageNumber(0);
                marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
                marketingActivityArg.setPageSize(marketingActivityIdsList.size());
                marketingActivityArg.setIds(marketingActivityIdsList);
                marketingActivityArg.setIsMankeep(true);
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> marketingActivityListVo = marketingActivityCrmManager.listMarketingActivity(upstreamEa, -10000, marketingActivityArg);
                if (null == marketingActivityListVo || null == marketingActivityListVo.getDataList()) {
                    log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatisticForPartner listMarketingActivity failed, marketingActivityIdsList={}", marketingActivityIdsList);
                    return new Result<>(SHErrorCode.SUCCESS, pageResult);
                }

                List<ObjectData> dataList = marketingActivityListVo.getDataList();
                for (ObjectData data : dataList) {
                    marketingActivityDataMap.put(data.getId(), data);
                }

                List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntityList = marketingActivityExternalConfigDao.getByMarketingActivityIds(marketingActivityIdsList);
                Map<String, MarketingActivityExternalConfigEntity> marketingActivityIdMarketingActivityExternalConfigEntityMap = new HashMap<>();

                if (CollectionUtils.isNotEmpty(marketingActivityExternalConfigEntityList)) {
                    Map<String, String> marketingActivityIdMetaralIdMap = new HashMap<>();
                    Map<String, String> marketingActivityIdNoticeIdMap = new HashMap<>();

                    Set<String> noticeIdsSet = new HashSet<>();
                    Set<String> productIdsSet = new HashSet<>();
                    Set<String> articleIdsSet = new HashSet<>();
                    Set<String> activityIdsSet = new HashSet<>();
                    Set<String> cardIdsSet = new HashSet<>();
                    Set<String> qrPosterSet =new HashSet<>();
                    Set<String> hexagonIdSet = new HashSet<>();
                    Set<String> cardMarketingActivityIdsSet = new HashSet<>();

                    for (MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity : marketingActivityExternalConfigEntityList) {
                        marketingActivityIdMarketingActivityExternalConfigEntityMap.put(marketingActivityExternalConfigEntity.getMarketingActivityId(), marketingActivityExternalConfigEntity);
                        //伙伴营销
                        if (marketingActivityExternalConfigEntity.getAssociateIdType() == 16) {
                            noticeIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            marketingActivityIdNoticeIdMap.put(marketingActivityExternalConfigEntity.getMarketingActivityId(), marketingActivityExternalConfigEntity.getAssociateId());
                        } else {
                            if (marketingActivityExternalConfigEntity.getAssociateIdType() == AssociateIdTypeEnum.MANKEEP_ARTICLE.getType()) {
                                articleIdsSet.add(marketingActivityExternalConfigEntity.getAssociateId());
                            }
                        }
                    }

                    Map<String, Map<String, String>> noticeIdMaterialMap = new HashMap<>();
                    List<String> noticeIdsList = new ArrayList<>(noticeIdsSet);
                    if (CollectionUtils.isNotEmpty(noticeIdsList)) {
                        List<NoticeEntity> noticeEntityList = noticeDAO.queryNoticeByIds(noticeIdsList);
                        for (NoticeEntity noticeEntity : noticeEntityList) {
                            Map<String, String> materialMap = new HashMap<>();
                            if (noticeEntity.getContentType() == NoticeContentTypeEnum.PRODUCT.getType()) {
                                productIdsSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.ARTICLE.getType()) {
                                articleIdsSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.ACTIVITY.getType()) {
                                activityIdsSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.QR_POSTER.getType()) {
                                qrPosterSet.add(noticeEntity.getContent());
                            } else if (noticeEntity.getContentType() == NoticeContentTypeEnum.HEXAGON.getType()){
                                hexagonIdSet.add(noticeEntity.getContent());
                            }

                            materialMap.put("objectType", Integer.valueOf(NoticeContentTypeEnum.fromType(noticeEntity.getContentType()).toObjectType()).toString());
                            materialMap.put("objectId", noticeEntity.getContent());
                            noticeIdMaterialMap.put(noticeEntity.getId(), materialMap);
                        }
                    }

                    Map<String, ProductEntity> productEntityMap = new HashMap<>();
                    List<String> productIdsList = new ArrayList<>(productIdsSet);
                    Map<String, String> productPicMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(productIdsList)) {
                        List<ProductEntity> productEntityList = productDAO.getByIds(productIdsList);
                        for (ProductEntity productEntity : productEntityList) {
                            productEntityMap.put(productEntity.getId(), productEntity);
                        }
                        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), productIdsList);
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            productPicMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v.getUrl(), (k1, k2) -> k1));
                        }
                    }

                    Map<String, ArticleEntity> articleEntityMap = new HashMap<>();
                    List<String> articleIdsList = new ArrayList<>(articleIdsSet);
                    Map<String, String> articlePicMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(articleIdsList)) {
                        List<ArticleEntity> articleEntityList = articleDAO.getByIds(articleIdsList);
                        for (ArticleEntity articleEntity : articleEntityList) {
                            articleEntityMap.put(articleEntity.getId(), articleEntity);
                        }
                        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleIdsList);
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            articlePicMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v.getUrl(), (k1, k2) -> k1));
                        }
                    }

                    Map<String, ActivityEntity> activityEntityMap = new HashMap<>();
                    List<String> activityIdsList = new ArrayList<>(activityIdsSet);
                    Map<String, String> activityPicMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(activityIdsList)) {
                        List<ActivityEntity> activityEntityList = activityDAO.getByIds(activityIdsList);
                        for (ActivityEntity activityEntity : activityEntityList) {
                            activityEntityMap.put(activityEntity.getId(), activityEntity);
                        }
                        List<PhotoEntity> photoEntities = photoManager.queryPhotosByTypeAndTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityIdsList);
                        if (CollectionUtils.isNotEmpty(photoEntities)) {
                            activityPicMap = photoEntities.stream().collect(Collectors.toMap(PhotoEntity :: getTargetId, v -> v.getUrl(), (k1, k2) -> k1));
                        }
                    }

                    Map<String, CardEntity> cardEntityMap = new HashMap<>();
                    Map<String, Integer> messageUserCountMap = new HashMap<>();
                    List<String> cardIdsList = new ArrayList<>(cardIdsSet);
                    if (CollectionUtils.isNotEmpty(cardIdsList)) {
                        List<CardEntity> cardEntityList = cardDAO.queryCardInfoByIds(cardIdsList);
                        for (CardEntity cardEntity : cardEntityList) {
                            cardEntityMap.put(cardEntity.getId(), cardEntity);
                            Integer messageUserCount = privateMessageDao.getPrivateSessionUserCount(cardEntity.getUid(), null, null, false);
                            if (null != messageUserCount) {
                                messageUserCountMap.put(cardEntity.getId(), messageUserCount);
                            }
                        }
                    }

                    Map<String, QRPosterEntity> qrPosterEntityMap = new HashMap<>();
                    List<String> qrPosterIdsList = new ArrayList<>(qrPosterSet);
                    if (CollectionUtils.isNotEmpty(qrPosterIdsList)) {
                        List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getAllByIds(qrPosterIdsList);
                        for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
                            qrPosterEntityMap.put(qrPosterEntity.getId(), qrPosterEntity);
                        }
                    }

                    List<String> hexagonIdList = new ArrayList<>(hexagonIdSet);
                    Map<String, HexagonBaseInfoDTO> hexagonBaseInfoDTOMap = null;
                    if (CollectionUtils.isNotEmpty(hexagonIdList)){
                        hexagonBaseInfoDTOMap = hexagonManager.getHexagonBaseInfoById(hexagonIdList, upstreamEa);
                    }

                    List<MarketingActivityEmployeeStatisticEntity> marketingActivityEmployeeStatisticEntityList = marketingActivityEmployeeStatisticDAO
                            .queryByMarketingActivityIdsForPartner(outTenantId,upstreamEa, outUserId, marketingActivityIdsList);
                    Map<String, MarketingActivityEmployeeStatisticEntity> marketingActivityEmployeeStatisticEntityMap = new HashMap<>();
                    for (MarketingActivityEmployeeStatisticEntity marketingActivityEmployeeStatisticEntity : marketingActivityEmployeeStatisticEntityList) {
                        marketingActivityEmployeeStatisticEntityMap.put(marketingActivityEmployeeStatisticEntity.getMarketingActivityId(), marketingActivityEmployeeStatisticEntity);
                    }

                    List<MarketingActivityStatisticEntity> marketingActivityStatisticEntityList = null;
                    Map<String, MarketingActivityStatisticEntity> cardMarketingActivityStatisticEntityMap = new HashMap<>();
                    List<String> cardMarketingActivityIdsList = new ArrayList(cardMarketingActivityIdsSet);
                    if (CollectionUtils.isNotEmpty(cardMarketingActivityIdsList)) {
                        marketingActivityStatisticEntityList = marketingActivityStatisticDao.queryByMarketingActivityIds(cardMarketingActivityIdsList);
                        if (CollectionUtils.isNotEmpty(marketingActivityStatisticEntityList)) {
                            for (MarketingActivityStatisticEntity marketingActivityStatisticEntity : marketingActivityStatisticEntityList) {
                                cardMarketingActivityStatisticEntityMap.put(marketingActivityStatisticEntity.getMarketingActivityId(), marketingActivityStatisticEntity);
                            }
                        }
                    }
                    // TODO: 2021/11/16  营销活动的线索数
                    Map<String, Integer> clueCountMap = customizeFormClueManager.batchCountClueNumByForpartner(outUserId, upstreamEa, marketingActivityIdsList);
                    for (SpreadRecordAndBoardDTO spreadRecordAndBoardDTO : spreadRecordAndBoardDTOList) {
                        GetLastestEmployeeStatisticUnitResult result = new GetLastestEmployeeStatisticUnitResult();
                        MarketingActivityEmployeeStatisticEntity marketingActivityEmployeeStatisticEntity = marketingActivityEmployeeStatisticEntityMap
                                .get(spreadRecordAndBoardDTO.getMarketingActivityId());
                        ObjectData marketingActivityData = marketingActivityDataMap.get(spreadRecordAndBoardDTO.getMarketingActivityId());
                        if (null == marketingActivityData) {
                            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic marketingActivityData not found, marketingActivityId={}",
                                    spreadRecordAndBoardDTO.getMarketingActivityId());
                        } else {
                            result.setTitle(marketingActivityData.getName());
                        }
                        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityIdMarketingActivityExternalConfigEntityMap
                                .get(spreadRecordAndBoardDTO.getMarketingActivityId());
                        if (null == marketingActivityExternalConfigEntity) {
                            log.warn("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic marketingActivityExternalConfigEntity not found, marketingActivityId={}",
                                    spreadRecordAndBoardDTO.getMarketingActivityId());
                            continue;
                        }
                        result.setSpreadTime(spreadRecordAndBoardDTO.getUpdateTime().getTime());
                        result.setMarketingActivityId(spreadRecordAndBoardDTO.getMarketingActivityId());

                        Integer objectType;
                        String objectId;
                        Integer spreadType;
                        if (marketingActivityExternalConfigEntity.getAssociateIdType() == 16) {
                            String noticeId = marketingActivityIdNoticeIdMap.get(spreadRecordAndBoardDTO.getMarketingActivityId());
                            if (StringUtils.isBlank(noticeId)) {
                                log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic noticeId not found, marketingActivityId={}",
                                        spreadRecordAndBoardDTO.getMarketingActivityId());
                                continue;
                            }

                            Map<String, String> material = noticeIdMaterialMap.get(noticeId);
                            if (material == null) {
                                log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic material not found, noticeId={}", noticeId);
                                continue;
                            }

                            objectType = Integer.valueOf(material.get("objectType"));
                            objectId = material.get("objectId");

//                            GetMarketingActivityDetailVo vo = marketingActivityCrmManager.getByIdMarketingActivity(upstreamEa, -10000, spreadRecordAndBoardDTO.getMarketingActivityId());
//                            spreadType = vo.getSpreadType();

                        } else {
                            objectType = marketingActivityExternalConfigEntity.getAssociateIdType();
                            objectId = marketingActivityExternalConfigEntity.getAssociateId();
                        }

                        if (null == objectType) {
                            log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic objectType not found, marketingActivityExternalConfigEntity={}",
                                    marketingActivityExternalConfigEntity);
                            continue;
                        }

                        if (StringUtils.isBlank(objectId)) {
                            log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic objectId not found, marketingActivityExternalConfigEntity={}",
                                    marketingActivityExternalConfigEntity);
                            continue;
                        }

//                        result.setSpreadType(spreadType);

                        if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            ProductEntity productEntity = productEntityMap.get(objectId);
                            String coverUrl = productPicMap.get(productEntity.getId());

                            result.setObjectType(ObjectTypeEnum.PRODUCT.getType());
                            result.setObjectId(productEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(productEntity.getName());
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            ArticleEntity articleEntity = articleEntityMap.get(objectId);
                            String coverUrl = articlePicMap.get(articleEntity.getId());

                            result.setObjectType(ObjectTypeEnum.ARTICLE.getType());
                            result.setObjectId(articleEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(articleEntity.getTitle());

                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        } else if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            ActivityEntity activityEntity = activityEntityMap.get(objectId);
                            String coverUrl = activityPicMap.get(activityEntity.getId());

                            result.setObjectType(ObjectTypeEnum.ACTIVITY.getType());
                            result.setObjectId(activityEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(activityEntity.getTitle());

                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }

                            result.setActivityStartTime(activityEntity.getStartTime().getTime());
                            result.setActivityEndTime(activityEntity.getEndTime().getTime());
                            result.setActivityDetailSiteId(activityEntity.getActivityDetailSiteId());
                        } else if (objectType == ObjectTypeEnum.CARD.getType()) {
                            CardEntity cardEntity = cardEntityMap.get(objectId);
                            if (null == cardEntity) {
                                log.error("EmployeeSpreadStatisticServiceImpl.getLastestEmployeeStatistic cardEntity not found, objectId={}", objectId);
                                continue;
                            }

                            result.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_EMPLOYEESPREADSTATISTICSERVICEIMPL_585));
                            result.setObjectType(ObjectTypeEnum.CARD.getType());
                            result.setObjectId(cardEntity.getId());
                            result.setObjectTitle(cardEntity.getName());
                            result.setCardCompanyName(cardEntity.getCompanyName());
                            result.setCoverUrl(cardEntity.getAvatar());

                            MarketingActivityStatisticEntity marketingActivityStatisticEntity = cardMarketingActivityStatisticEntityMap
                                    .get(spreadRecordAndBoardDTO.getMarketingActivityId());

                            if (null != marketingActivityStatisticEntity) {
                                result.setLookUpCount(marketingActivityStatisticEntity.getLookUpCount());
                                result.setSpreadCount(marketingActivityStatisticEntity.getSpreadCount());
                            }

                            Integer messageUserCount = messageUserCountMap.get(objectId);
                            result.setMessageUserCount(messageUserCount);
                        } else if (objectType == ObjectTypeEnum.QR_POSTER.getType()) {
                            QRPosterEntity qrPosterEntity = qrPosterEntityMap.get(objectId);
                            String coverUrl = fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), null, false);

                            result.setObjectType(ObjectTypeEnum.QR_POSTER.getType());
                            result.setObjectId(qrPosterEntity.getId());
                            result.setCoverUrl(coverUrl);
                            result.setObjectTitle(qrPosterEntity.getTitle());

                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        }else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
                            if (hexagonBaseInfoDTOMap != null && hexagonBaseInfoDTOMap.get(objectId) != null){
                                HexagonBaseInfoDTO hexagonBaseInfoDTO =  hexagonBaseInfoDTOMap.get(objectId);
                                result.setObjectType(ObjectTypeEnum.HEXAGON_SITE.getType());
                                result.setObjectId(hexagonBaseInfoDTO.getId());
                                result.setCoverUrl(hexagonBaseInfoDTO.getCoverUrl());
                                result.setObjectTitle(hexagonBaseInfoDTO.getTitle());
                            }
                            if (null != marketingActivityEmployeeStatisticEntity) {
                                result.setLookUpCount(marketingActivityEmployeeStatisticEntity.getLookUpCount());
                                result.setForwardCount(marketingActivityEmployeeStatisticEntity.getForwardCount());
                                result.setLeadAccumulationCount(clueCountMap.get(spreadRecordAndBoardDTO.getMarketingActivityId()));
                            }
                        }
                        result.setDataType(LastestEmployeeStatisticDataTypeEnum.SPREAD.getType());
                        resultList.add(result);
                    }
                }
            }
        }

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<GetEmployeeMarketingActivityObjectDetailsResult> getEmployeeMarketingActivityObjectDetails(GetEmployeeMarketingActivityObjectDetailsArg arg, String ea, Integer userId) {
        GetEmployeeMarketingActivityObjectDetailsResult result = new GetEmployeeMarketingActivityObjectDetailsResult();
        MarketingActivitySpreadRecordEntity marketingActivitySpreadRecordEntity = marketingActivitySpreadRecordDAO
            .queryMarketingActivitySpreadRecordByEaAndUserIdAndMarketingActivityId(ea, userId, arg.getMarketingActivityId());
        if (marketingActivitySpreadRecordEntity == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getEmployeeMarketingActivityObjectDetails marketingActivitySpreadRecordEntity is null arg:{}, ea:{}, userId:{}", arg, ea, userId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        result.setSpreadTime(marketingActivitySpreadRecordEntity.getCreateTime().getTime());
        GetMarketingActivityDetailVo getMarketingActivityDetailVo = marketingActivityCrmManager.getByIdMarketingActivity(ea, -10000, arg.getMarketingActivityId());
        if (getMarketingActivityDetailVo == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getEmployeeMarketingActivityObjectDetails getMarketingActivityDetailVo is null arg:{}, ea:{}, userId:{}", arg, ea, userId);
            return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
        }
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getMarketingActivityId());
        if (marketingActivityExternalConfigEntity == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getEmployeeMarketingActivityObjectDetails marketingActivityExternalConfigEntity is null arg:{}, ea:{}, userId:{}", arg, ea, userId);
            return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
        }
        result.setTitle(getMarketingActivityDetailVo.getName());
        // 设置各物料信息
        int associateIdType = marketingActivityExternalConfigEntity.getAssociateIdType();
        if (associateIdType == AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType() || associateIdType == AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType() || associateIdType == AssociateIdTypeEnum.PARTNER_SPREAD_NOTICE.getType()) {
            // 营销推广
            NoticeEntity noticeEntity = noticeDAO.getNoticeById(marketingActivityExternalConfigEntity.getAssociateId());
            Integer objectType = objectManager.convertNoticeContentTypeToObjectType(noticeEntity.getContentType());
            result = this.buildObjectResult(ea, objectType, noticeEntity.getContent(), result);
        } else {
            result = this.buildObjectResult(ea, marketingActivityExternalConfigEntity.getAssociateIdType(), marketingActivityExternalConfigEntity.getAssociateId(), result);
        }
        // 组装统计数据
        GetActivityDayStatisticDTO getActivityDayStatisticDTO;
        int messageUserCount;
        if (arg.getStartDate() == null||arg.getEndDate()==null) {
            // 名片特殊处理
            if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.CARD.getType()) {
                getActivityDayStatisticDTO = marketingActivityDayStatisticDAO.getActivityDayStatistic(ea, arg.getMarketingActivityId(), null, null, false);
            } else {
                getActivityDayStatisticDTO = marketingActivityEmployeeDayStatisticDAO.getActivityEmployeeDayStatistic(ea, arg.getMarketingActivityId(), userId, null, null, false);
            }
            messageUserCount = StringUtils.isBlank(result.getUid()) ? 0 : privateMessageDao.getPrivateSessionUserCount(result.getUid(), null, null, false);
        } else {
//            DateTime endDate = DateTime.now();
//            DateTime startDate = endDate.minusDays(arg.getRecentDay() - 1);
            if (marketingActivityExternalConfigEntity.getAssociateIdType() == ObjectTypeEnum.CARD.getType()) {
                getActivityDayStatisticDTO = marketingActivityDayStatisticDAO.getActivityDayStatistic(ea, arg.getMarketingActivityId(), new Date(arg.getStartDate()), new Date(arg.getEndDate()), true);
            } else {
                getActivityDayStatisticDTO = marketingActivityEmployeeDayStatisticDAO
                    .getActivityEmployeeDayStatistic(ea, arg.getMarketingActivityId(), userId, new Date(arg.getStartDate()), new Date(arg.getEndDate()), true);
            }
            messageUserCount = StringUtils.isBlank(result.getUid()) ? 0 : privateMessageDao.getPrivateSessionUserCount(result.getUid(), new Date(arg.getStartDate()), new Date(arg.getEndDate()), true);
        }
        result.setMessageUserCount(messageUserCount);
        result.setForwardCount(getActivityDayStatisticDTO == null ? 0 : getActivityDayStatisticDTO.getForwardCount());
        result.setLookUpCount(getActivityDayStatisticDTO == null ? 0 : getActivityDayStatisticDTO.getLookUpCount());
        if (arg.getStartDate() == null||arg.getEndDate()==null) {
            //result.setSaveToCrmLeadCount(customizeFormClueManager.countClueNumByFsUserIdAndMarketingActivityId(ea, userId, arg.getMarketingActivityId(), false, 0,0));
            List<String> ids = customizeFormClueManager.getFormDataUserIdByFsUserIdAndMarketingActivityId(ea, userId, arg.getMarketingActivityId(), false, 0, 0);
            result.setSaveToCrmLeadCount(ids.size());
            result.setSaveToCrmLeadIds(ids);

        } else {
            //result.setSaveToCrmLeadCount(customizeFormClueManager.countClueNumByFsUserIdAndMarketingActivityId(ea, userId, arg.getMarketingActivityId(), true,  arg.getStartDate(), arg.getEndDate()));
            List<String> ids = customizeFormClueManager.getFormDataUserIdByFsUserIdAndMarketingActivityId(ea, userId, arg.getMarketingActivityId(), true,  arg.getStartDate(), arg.getEndDate());
            result.setSaveToCrmLeadCount(ids.size());
            result.setSaveToCrmLeadIds(ids);
        }
        result.setSpreadCount(getActivityDayStatisticDTO == null ? 0 : getActivityDayStatisticDTO.getSpreadCount());
        result = this.buildSpreadLeaderData(ea, userId, arg.getMarketingActivityId(), result, arg.getRecentDay());
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<PageResult<GetClueContributionListResult>> getClueContributionList(GetClueContributionListArg arg) {
        PageResult<GetClueContributionListResult> pageResult = new PageResult<>();
        List<GetClueContributionListResult> getClueContributionListResultList = Lists.newArrayList();
        pageResult.setTime(arg.getTime());
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(getClueContributionListResultList);
        String uid = qywxUserManager.getUidByFsUserInfo(arg.getEa(), arg.getFsUserId());
        if (StringUtils.isEmpty(uid)) {
            log.info("EmployeeSpreadStatisticServiceImpl.getClueContributionList fsBind uid is null ea:{}, fsUid:{}", arg.getEa(), arg.getFsUserId());
            return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        // 根据活动id查询活动对应物料
        MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(arg.getMarketingActivityId());
        if (marketingActivityExternalConfigEntity == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.getEmployeeMarketingActivityObjectDetails marketingActivityExternalConfigEntity is null arg:{}, ea:{}, userId:{}", arg, arg.getEa(),
                arg.getFsUserId());
            return new Result<>(SHErrorCode.KIS_MARKETING_ACTIVITY_NOT_FOUND);
        }
        // 查询所有根节点
        List<SpreadStatEntity> rootNodeData = spreadStatDao.getAllRootNodeByTargetUid(arg.getMarketingActivityId(), arg.getMarketingActivityId(), ObjectTypeEnum.MARKETING_ACTIVITY.getType(), uid);
        if (CollectionUtils.isEmpty(rootNodeData)) {
            log.info("EmployeeSpreadStatisticServiceImpl.buildSpreadLeaderData rootNodeData is null arg:{}, uid:{}", arg, uid);
            return new Result<>(SHErrorCode.SUCCESS, pageResult);
        }
        List<String> parentFeedKeyList = rootNodeData.stream().map(SpreadStatEntity::getFeedKey).collect(Collectors.toList());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<GetFirstNodeStatisticDTO> getFirstNodeStatisticDTOList;
        if (arg.getStartDate() == null || arg.getEndDate() == null) {
            getFirstNodeStatisticDTOList = spreadStatDao
                .getFirstNodeClueNumStatistic(arg.getMarketingActivityId(), arg.getMarketingActivityId(), ObjectTypeEnum.MARKETING_ACTIVITY.getType(), uid, parentFeedKeyList, page, null, null, false);
        } else {
//            DateTime endDate = DateTime.now();
//            DateTime startDate = endDate.minusDays(arg.getRecentDay() - 1);
            getFirstNodeStatisticDTOList = spreadStatDao
                .getFirstNodeClueNumStatistic(arg.getMarketingActivityId(), arg.getMarketingActivityId(), ObjectTypeEnum.MARKETING_ACTIVITY.getType(), uid, parentFeedKeyList, page, new Date(arg.getStartDate()),
                        new Date(arg.getEndDate()), true);
        }
        if (CollectionUtils.isEmpty(getFirstNodeStatisticDTOList)) {
            log.info("EmployeeSpreadStatisticServiceImpl.buildSpreadLeaderData getFirstNodeStatisticDTOList is null arg:{}, uid:{}", arg, uid);
            return new Result<>(SHErrorCode.SUCCESS, pageResult);
        }
        List<String> uids = getFirstNodeStatisticDTOList.stream().map(GetFirstNodeStatisticDTO::getSourceUid).collect(Collectors.toList());
        Map<String, BaseUserInfoResult> baseUserInfoResultMap = kmUserManager.batchGetBaseUserInfo(uids);
        for (GetFirstNodeStatisticDTO getFirstNodeStatisticDTO : getFirstNodeStatisticDTOList) {
            GetClueContributionListResult getClueContributionListResult = new GetClueContributionListResult();
            BaseUserInfoResult baseUserInfoResult = baseUserInfoResultMap.get(getFirstNodeStatisticDTO.getSourceUid());
            getClueContributionListResult.setName(baseUserInfoResult != null ? baseUserInfoResult.getName() : null);
            getClueContributionListResult.setAvatar(baseUserInfoResult != null ? baseUserInfoResult.getAvatar() : null);
            getClueContributionListResult.setClueNum(getFirstNodeStatisticDTO.getClueNumCnt());
            getClueContributionListResultList.add(getClueContributionListResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    private GetEmployeeMarketingActivityObjectDetailsResult buildObjectResult(String ea, Integer objectType, String objectId, GetEmployeeMarketingActivityObjectDetailsResult result) {
        result.setObjectId(objectId);
        result.setObjectType(objectType);
        if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
            ProductEntity productEntity = new ProductEntity();
            productEntity.setId(objectId);
            productEntity = objectManager.visit(productEntity);
            result.setObjectTitle(productEntity.getName());
            result.setCoverUrl(productEntity.getHeadImg());
        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
            ArticleEntity articleEntity = new ArticleEntity();
            articleEntity.setId(objectId);
            articleEntity = objectManager.visit(articleEntity);
            result.setObjectTitle(articleEntity.getTitle());
            result.setCoverUrl(articleEntity.getPhotoUrl());
        } else if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
            ActivityEntity activityEntity = new ActivityEntity();
            activityEntity.setId(objectId);
            activityEntity = objectManager.visit(activityEntity);
            result.setObjectTitle(activityEntity.getTitle());
            result.setCoverUrl(activityEntity.getCoverImageUrl());
            result.setActivityStartTime(activityEntity.getStartTime() != null ? activityEntity.getStartTime().getTime() : null);
            result.setActivityEndTime(activityEntity.getEndTime() != null ? activityEntity.getEndTime().getTime() : null);
        } else if (objectType == ObjectTypeEnum.CARD.getType()) {
            CardEntity cardEntity = new CardEntity();
            cardEntity.setId(objectId);
            cardEntity = objectManager.visit(cardEntity);
            result.setCoverUrl(cardEntity.getAvatar());
            result.setCardName(cardEntity.getName());
            result.setCardCompanyName(cardEntity.getCompanyName());
            result.setUid(cardEntity.getUid());
        } else if (objectType == ObjectTypeEnum.QR_POSTER.getType()) {
            QRPosterEntity qrPosterEntity = new QRPosterEntity();
            qrPosterEntity.setId(objectId);
            qrPosterEntity = objectManager.visit(qrPosterEntity);
            result.setObjectTitle(qrPosterEntity.getTitle());
            result.setCoverUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), ea, false));
        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()){
            Map<String, HexagonBaseInfoDTO> hexagonBaseInfoDTOMap =
                                          hexagonManager.getHexagonBaseInfoById(Lists.newArrayList(objectId), ea);
            if (hexagonBaseInfoDTOMap != null && hexagonBaseInfoDTOMap.get(objectId) != null){
                HexagonBaseInfoDTO hexagonBaseInfoDTO = hexagonBaseInfoDTOMap.get(objectId);
                result.setCoverUrl(hexagonBaseInfoDTO.getCoverUrl());
                result.setObjectTitle(hexagonBaseInfoDTO.getTitle());
            }
        } else if (objectType == ObjectTypeEnum.FILE.getType()){
            FileEntity fileEntity = new FileEntity();
            fileEntity.setId(objectId);
            fileEntity = objectManager.visit(fileEntity);
            result.setObjectTitle(fileEntity.getFileName());
            result.setCoverUrl(QywxFileTypeLogoEnum.getLogoByType(fileEntity.getExt()));
        } else if (objectType == ObjectTypeEnum.VIDEO.getType()){
            VideoEntity videoEntity = new VideoEntity();
            videoEntity.setId(objectId);
            videoEntity = objectManager.visit(videoEntity);
            result.setObjectTitle(videoEntity.getVideoName());
            result.setCoverUrl(videoEntity.getImage4H5());
        }else if (objectType == ObjectTypeEnum.OUT_LINK.getType()){
            OutLinkEntity entity = new OutLinkEntity();
            entity.setId(objectId);
            entity = objectManager.visit(entity);
            result.setObjectTitle(entity.getName());
            result.setCoverUrl(entity.getCover());
        }
        return result;
    }

    private GetEmployeeMarketingActivityObjectDetailsResult buildSpreadLeaderData(String ea, Integer fsUid, String marketingActivityId, GetEmployeeMarketingActivityObjectDetailsResult result,
        Integer recentDay) {
        List<GetEmployeeMarketingActivityObjectDetailsResult.SpreadLeader> spreadLeaderList = Lists.newArrayList();
        result.setSpreadLeaderList(spreadLeaderList);
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUid);
        if (StringUtils.isEmpty(uid)) {
            log.info("EmployeeSpreadStatisticServiceImpl.buildSpreadLeaderData fsBind uid is null ea:{}, fsUid:{}", ea, fsUid);
            return result;
        }
        // 查询所有根节点
        List<SpreadStatEntity> rootNodeData = spreadStatDao.getAllRootNodeByTargetUid(marketingActivityId, marketingActivityId, ObjectTypeEnum.MARKETING_ACTIVITY.getType(), uid);
        if (CollectionUtils.isEmpty(rootNodeData)) {
            log.info("EmployeeSpreadStatisticServiceImpl.buildSpreadLeaderData rootNodeData is null result:{}, uid:{}", result, uid);
            return result;
        }
        List<String> parentFeedKeyList = rootNodeData.stream().map(SpreadStatEntity::getFeedKey).collect(Collectors.toList());
        List<GetFirstNodeStatisticDTO> getFirstNodeStatisticDTOList;
        if (recentDay == null) {
            getFirstNodeStatisticDTOList = spreadStatDao
                .getFirstNodeStatistic(marketingActivityId, marketingActivityId, ObjectTypeEnum.MARKETING_ACTIVITY.getType(), uid, parentFeedKeyList, null, null, false);
        } else {
            DateTime endDate = DateTime.now();
            DateTime startDate = endDate.minusDays(recentDay - 1);
            getFirstNodeStatisticDTOList = spreadStatDao
                .getFirstNodeStatistic(marketingActivityId, marketingActivityId, ObjectTypeEnum.MARKETING_ACTIVITY.getType(), uid, parentFeedKeyList, startDate.toDate(), endDate.toDate(), true);
        }
        if (CollectionUtils.isEmpty(getFirstNodeStatisticDTOList)) {
            log.info("EmployeeSpreadStatisticServiceImpl.buildSpreadLeaderData getFirstNodeStatisticDTOList is null result:{}, uid:{}", result, uid);
            return result;
        }
        getFirstNodeStatisticDTOList = getFirstNodeStatisticDTOList.stream().filter(data -> !(data.getForwardCnt() == 0 && data.getReadCnt() == 0)).collect(Collectors.toList());
        List<String> uids = getFirstNodeStatisticDTOList.stream().map(GetFirstNodeStatisticDTO::getSourceUid).collect(Collectors.toList());
        Map<String, BaseUserInfoResult> baseUserInfoResultMap = kmUserManager.batchGetBaseUserInfo(uids);
        for (GetFirstNodeStatisticDTO getFirstNodeStatisticDTO : getFirstNodeStatisticDTOList) {
            GetEmployeeMarketingActivityObjectDetailsResult.SpreadLeader spreadLeader = new GetEmployeeMarketingActivityObjectDetailsResult.SpreadLeader();
            BaseUserInfoResult baseUserInfoResult = baseUserInfoResultMap.get(getFirstNodeStatisticDTO.getSourceUid());
            spreadLeader.setName(baseUserInfoResult != null ? baseUserInfoResult.getName() : null);
            spreadLeader.setAvatar(baseUserInfoResult != null ? baseUserInfoResult.getAvatar() : null);
            spreadLeader.setLookUpCount(getFirstNodeStatisticDTO.getReadCnt());
            spreadLeader.setSpreadCount(getFirstNodeStatisticDTO.getForwardCnt());
            spreadLeaderList.add(spreadLeader);
        }
        return result;
    }

    private void buildBoardInfoResult(SpreadRecordAndBoardDTO spreadRecordAndBoardDTO, Map<String, BoardCardResult> boardCardResultMap, List<GetLastestEmployeeStatisticUnitResult> results) {
        BoardCardResult boardCardResult = boardCardResultMap.get(spreadRecordAndBoardDTO.getId());
        if (boardCardResult == null) {
            log.warn("EmployeeSpreadStatisticServiceImpl.buildBoardInfoResult boardCardResult is null spreadRecordAndBoardDTO:{}", spreadRecordAndBoardDTO);
            return;
        }
        GetLastestEmployeeStatisticUnitResult getLastestEmployeeStatisticUnitResult = new GetLastestEmployeeStatisticUnitResult();
        getLastestEmployeeStatisticUnitResult.setDataType(LastestEmployeeStatisticDataTypeEnum.BOARD.getType());
        getLastestEmployeeStatisticUnitResult.setBoardCardResult(boardCardResult);
        results.add(getLastestEmployeeStatisticUnitResult);
    }
}