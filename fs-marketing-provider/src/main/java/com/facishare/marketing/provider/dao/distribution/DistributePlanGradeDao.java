package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.provider.entity.distribution.DistributePlanGradeEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created by ranluch on 2019/4/4.
 */
public interface DistributePlanGradeDao {
    @Insert("insert into distribute_plan_grade(id, plan_id, grade, name, money, reward_ratio, distribution_reward_ratio, fs_user_id, type, rule_type, condition1, condition2, rights_id) " +
        "values(#{obj.id}, #{obj.planId}, #{obj.grade}, #{obj.name}, #{obj.money}, #{obj.rewardRatio}, #{obj.distributionRewardRatio}, #{obj.fsUserId}, #{obj.type}, #{obj.ruleType}, #{obj.condition1}, #{obj.condition2}, #{obj.rightsId})")
    boolean insertDistributePlanGrade(@Param("obj") DistributePlanGradeEntity obj);


    @Update("update distribute_plan_grade " +
        "set name = #{name}, " +
        "rule_type = #{ruleType}, "+
        "condition1 = #{condition1}, "+
        "condition2 = #{condition2}, "+
        "rights_id = #{rightsId}, "+
        "update_time = now()" +
        " where id = #{id}")
    Integer updatetDistributePlanGrade(@Param("id") String id, @Param("name") String name, @Param("ruleType") Integer ruleType, @Param("condition1")Integer condition1, @Param("condition2") Integer condition2, @Param("rightsId") String rightsId);

    @Select("SELECT * FROM distribute_plan_grade WHERE plan_id = #{planId} and type = #{type} order by grade desc limit 1")
    DistributePlanGradeEntity getCurrentMaxGrade(@Param("planId") String planId, @Param("type") Integer type);

    @Select("SELECT * FROM distribute_plan_grade WHERE plan_id = #{planId} and grade = #{grade} and type = #{type}")
    DistributePlanGradeEntity getPlanGradeByPlanIdAndGrade(@Param("planId") String planId, @Param("type") Integer type, @Param("grade") Integer grade);

    @Select("SELECT * FROM distribute_plan_grade WHERE id = #{id}")
    DistributePlanGradeEntity getPlanGradeById(@Param("id") String id);

    @Delete("delete FROM distribute_plan_grade WHERE id = #{id}")
    boolean deletePlanGradeById(@Param("id") String id);

    @Select("SELECT * FROM distribute_plan_grade WHERE plan_id = #{planId} and type = #{type}")
    List<DistributePlanGradeEntity> queryPlanGradesByPlanId(@Param("planId") String planId, @Param("type") Integer type);

    @Select("<script>"
        + "SELECT * "
        + "FROM distribute_plan_grade "
        + "WHERE plan_id = #{planId} and type = #{type}"
        + "ORDER BY grade asc "
        + "</script>")
    List<DistributePlanGradeEntity> pagePlanGradesByPlanId(@Param("planId") String planId, @Param("type") Integer type, @Param("page") Page page);

    @Select("SELECT * FROM distribute_plan_grade WHERE plan_id = #{planId} and type = #{type} and condition1 <= #{condition1} ORDER BY grade desc LIMIT 1")
    DistributePlanGradeEntity getPlanGradeByCondition1(@Param("planId") String planId, @Param("type") Integer type, @Param("condition1") double condition1);

    @Select("SELECT * FROM distribute_plan_grade WHERE plan_id = #{planId} and type = #{type} and condition1 <= #{condition1} and condition2 <= #{condition2} ORDER BY grade desc LIMIT 1")
    DistributePlanGradeEntity getPlanGradeByCondition1AndCondition2(@Param("planId") String planId, @Param("type") Integer type, @Param("condition1") double condition1, @Param("condition2") double condition2);
}
