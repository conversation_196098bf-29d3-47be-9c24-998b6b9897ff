package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.api.arg.qywx.media.GetMediaIdAndMiniInfoArg;
import com.facishare.marketing.api.service.qywx.QYWXMediaService;
import com.facishare.marketing.common.enums.qywx.QywxFileTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.innerResult.qywx.UploadMediaResult;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service("qywxMediaService")
public class QYWXMediaServiceImpl implements QYWXMediaService {
    @Autowired
    FileV2Manager fileV2Manager;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @ReloadableProperty("host")
    private String host;

    @Override
    public Result<String> getMediaIdOrMiniAppId(GetMediaIdAndMiniInfoArg arg) {
        String res = null;
        String corpId = arg.getCorpId();
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxManager.getAgentConfigEntityByAppId(corpId, arg.getAppId());
        if (null == qywxCorpAgentConfigEntity) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_BIND_QYWX);
        }
        String ea = qywxCorpAgentConfigEntity.getEa();
        if (QywxFileTypeEnum.sendMiniApp(arg.getType())) {
            //返回绑定小程序id
            //可以获取wxappid
            res = eaWechatAccountBindDao.getWxAppIdByEa(ea, "YXT");
        } else {
            QywxFileTypeEnum typeEnum = QywxFileTypeEnum.getByType(arg.getType());
            if (null == typeEnum) {
                return new Result<>(SHErrorCode.QYWX_UPLOAD_FILE_TYPE_ERROR);
            }
            byte[] data = fileV2Manager.getByteDataByUrl(arg.getDownLoadUrl());
            if (lagerFile(data, typeEnum.getMediaType())) {
                return new Result<>(SHErrorCode.QYWX_UPLOAD_TOO_LAGER_FILE);
            }
            String accessToken = qywxManager.getAccessToken(qywxCorpAgentConfigEntity);
            if (org.apache.commons.lang3.StringUtils.isBlank(accessToken)) {
                return new Result<>(SHErrorCode.QYWX_INNER_ACCESS_TOKEN_NOT_FOUND);
            }
            UploadMediaResult mediaResult = httpManager.uploadFileToQywx(accessToken, data, arg.getName(), typeEnum.getMediaType());
            if (mediaResult != null && mediaResult.getErrcode() == 0) {
                res = mediaResult.getMediaId();
            }
        }
        return new Result<>(SHErrorCode.SUCCESS, res);
    }

    @Override
    public Result<String> getAuthBackHost() {
        return new Result<>(SHErrorCode.SUCCESS,host);
    }

    private boolean lagerFile(byte[] data, String fileType) {
        if (null == data) {
            return true;
        }
        if ("image".equals(fileType)) {
            return data.length > 5 * 1024 * 1024 || data.length < 5;
        } else if ("file".equals(fileType)) {
            return data.length > 20 * 1024 * 1024 || data.length < 5;
        }
        return true;
    }
}
