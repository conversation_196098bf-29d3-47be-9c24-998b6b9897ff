package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.StaffMiniappUserBindEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2021/05/27
 **/
public interface StaffMiniappUserBindDAO {

    @Insert("INSERT INTO staff_miniapp_user_bind (\n"
        + "        \"id\",\n"
        + "        \"staff_uid\",\n"
        + "        \"visitor_uid\",\n"
        + "        \"create_time\"\n"
        + "        )\n"
        + "        VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.staffUid},\n"
        + "        #{obj.visitorUid},\n"
        + "        now()\n"
        + "        ) ON CONFLICT DO NOTHING;")
    int insertStaffMiniappUserBind(@Param("obj") StaffMiniappUserBindEntity staffMiniappUserBindEntity);


    @Select("SELECT * FROM staff_miniapp_user_bind WHERE visitor_uid = #{visitorUid}")
    StaffMiniappUserBindEntity queryStaffMiniappUserBindByVisitorUid(@Param("visitorUid") String visitorUid);

}
