package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.result.live.PolyvAccountResult;
import com.facishare.marketing.api.result.live.PolyvLiveListResult;
import com.facishare.marketing.api.vo.live.ListVO;
import com.facishare.marketing.common.contstant.CampaignMembersConstants;
import com.facishare.marketing.common.enums.AreaTypeEnum;
import com.facishare.marketing.common.enums.MarketingUserActionChannelType;
import com.facishare.marketing.common.enums.MarketingUserActionType;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.live.*;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataUserEntity;
import com.facishare.marketing.provider.entity.live.*;
import com.facishare.marketing.provider.innerArg.live.UpdatePolyvAuthSettingArg;
import com.facishare.marketing.provider.innerData.live.polyv.*;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.util.polyv.LiveSignUtil;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("polyvManager")
@Slf4j
public class PolyvManager {

    private static final String LIST_URL = "http://api.polyv.net/live/v3/channel/management/list-detail";
    private static final String DETAIL_URL = "http://api.polyv.net/live/v3/channel/basic/get";
    private static final String LIVE_SESSION = "http://api.polyv.net/live/v3/channel/session/list?";
    private static final String LIVE_LIKE = "http://api.polyv.net/live/v4/channel/statistics/like/list?";

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private PolyvAccountDAO polyvAccountDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private MarketingLiveViewLoginDAO marketingLiveViewLoginDAO;
    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private AreaManager areaManager;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private ActionManager actionManager;

    @ReloadableProperty("host")
    private String host;

    public Result<Void> bindPolyvAccount(String ea, String appId, String appSecret, String userId) {
        //查询账号信息
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", appId);
        requestMap.put("timestamp", System.currentTimeMillis() + "");
        try {
            requestMap.put("sign", LiveSignUtil.getSign(requestMap, appSecret));
        } catch (Exception e) {
            log.info("bindPolyvAccount get sign error...");
        }
        String url = "http://api.polyv.net/live/v3/user/get-info?" + httpManager.transformUrlParams(requestMap);
        PolyvAccountInfoData data = httpManager.executeGetHttp(url, new TypeToken<PolyvAccountInfoData>() {
        });
        try {
            if (!userId.equals(data.getData().getUserId())) {
                return Result.newError(SHErrorCode.POLYV_BIND_INFO_ERROR);
            }
        } catch (Exception e) {
            return Result.newError(SHErrorCode.POLYV_BIND_INFO_ERROR);
        }

        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity != null) {
            polyvAccountDAO.updateByEa(ea, userId, appId, appSecret);
        } else {
            polyvAccountEntity = new PolyvAccountEntity();
            polyvAccountEntity.setId(UUIDUtil.getUUID());
            polyvAccountEntity.setEa(ea);
            polyvAccountEntity.setAppId(appId);
            polyvAccountEntity.setAppSecret(appSecret);
            polyvAccountEntity.setUserId(userId);
            polyvAccountDAO.insert(polyvAccountEntity);
        }

        return Result.newSuccess();
    }


    public Map<String, String> externalAuth(String channelId, String userid, Long ts, String token) {
        HashMap<String, String> map = new HashMap<>();
        long timeMillis = System.currentTimeMillis();
        long diffTime = Math.abs(timeMillis - ts);
        //1、时间戳判断
        if (diffTime > 5 * 60 * 1000) {
            log.error("时间戳验证错误");
            map.put("status", "0");
            //抛出异常时，如果设置errorUrl，则会跳转到 errorUrl ,如果未返回 errorUrl，则先查询 外部授权参数
            // externalRedirectUri，externalRedirectUri不为空则跳转externalRedirectUri地址，externalRedirectUri为空则跳转保利威默认页面
//            map.put("errorUrl", "https://www.polyv.net");
            return map;
        }
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getMarketingLiveByXiaoetongId(channelId);
        Integer ei = marketingLiveEntity.getCorpId();
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        PolyvLiveInnerData liveDetail = this.getLiveDetail(ea, channelId);
        String externalKey = null;
        if (null != liveDetail && null != liveDetail.getData() && 200 == liveDetail.getCode()) {
            List<PolyvLiveInnerData.AuthSetting> authSetting = liveDetail.getData().getAuthSettings();
            for (PolyvLiveInnerData.AuthSetting setting : authSetting) {
                if (null != setting && "Y".equals(setting.getEnabled()) && "external".equals(setting.getAuthType())) {
                    externalKey = setting.getExternalKey();
                    break;
                }
            }
        }
        //2、签名判断
        String signText = externalKey + userid + externalKey + ts;
        String sign = null;
        try {
            sign = LiveSignUtil.md5Hex(signText);
        } catch (Exception e) {
            log.info("externalAuth get sign error {}", e.getMessage());
        }
        if (sign == null || !sign.equals(token)) {
            log.error("签名验证错误");
            map.put("status", "0");
            return map;
        }
        //TODO 业务逻辑处理，后续需根据具体需求进行数据库操作
        MarketingLiveViewLoginEntity entity = marketingLiveViewLoginDAO.getById(userid);
        if (null == entity) {
            log.error("没找到报名信息");
            map.put("status", "0");
            return map;
        }
        List<CampaignMergeDataEntity> campaignMergeDataEntityList = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea,
                entity.getMarketingEventId(), entity.getPhone(), false);
        if (CollectionUtils.isEmpty(campaignMergeDataEntityList)) {
            log.error("没找到报名信息");
            map.put("status", "0");
            return map;
        }

        //3、正常返回
        map.put("status", "1");
        map.put("userid", userid);
        iflytekPolyvLiveConfig(ea, campaignMergeDataEntityList.get(0), map);
        return map;
    }

    public void iflytekPolyvLiveConfig(String ea, CampaignMergeDataEntity mergeDataEntity, Map<String, String> map){
        if (!StringUtils.equals(ea, "iflytek") && !StringUtils.equals(ea, "83668")){
            return;
        }
        if (mergeDataEntity == null){
            return;
        }

        map.put("nickname", mergeDataEntity.getName());
        map.put("phone", mergeDataEntity.getPhone());
        List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIds(Lists.newArrayList(mergeDataEntity.getId()));
        if (CollectionUtils.isEmpty(customizeFormDataUserEntities)){
            return;
        }
        CustomizeFormDataUserEntity customizeFormDataUserEntity = customizeFormDataUserEntities.get(0);
        if (customizeFormDataUserEntity.getSubmitContent() == null){
            return;
        }
        if (customizeFormDataUserEntities.get(0).getSubmitContent().getCompanyName() != null){
            map.put("company", customizeFormDataUserEntities.get(0).getSubmitContent().getCompanyName());
        }
        String provinceCode = customizeFormDataUserEntities.get(0).getSubmitContent().getProvince();
        if (StringUtils.isEmpty(provinceCode)){
            return;
        }
        Map<String, String> areaMap = areaManager.getAreaNameByValue(ea, Lists.newArrayList(provinceCode), Lists.newArrayList(AreaTypeEnum.PROVINCE));
        if (areaMap.get(provinceCode) != null) {
            map.put("province", areaMap.get(provinceCode));
        }

        return;
    }


    public Result<PageResult<PolyvLiveListResult>> getLiveList(ListVO vo) {
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(0);
        List<PolyvLiveListResult> data = Lists.newArrayList();
        pageResult.setResult(data);
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setPageNum(vo.getPageNum());

        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(vo.getEa());
        if (polyvAccountEntity == null) {
            return Result.newSuccess(pageResult);
        }
        String appId = polyvAccountEntity.getAppId();
        String appSecret = polyvAccountEntity.getAppSecret();
        String time = System.currentTimeMillis() + "";
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", appId);
        requestMap.put("timestamp", time);
        requestMap.put("keyword", vo.getKeyword());
        requestMap.put("page", vo.getPageNum() == null ? "" : vo.getPageNum() + "");
        requestMap.put("pageSize", vo.getPageSize() + "");
        String sign = null;
        try {
            sign = LiveSignUtil.getSign(requestMap, appSecret);
        } catch (Exception e) {
            log.info("PolyvManager getLiveList get sign fail : {}", e.getMessage());
            return Result.newError(SHErrorCode.POLYV_SIGN_FAIL);
        }
        if (StringUtil.isNullOrEmpty(sign)) {
            return Result.newError(SHErrorCode.POLYV_SIGN_FAIL);
        }
        RequestBody requestBody = new FormBody.Builder().add("appId", appId).add("keyword", vo.getKeyword() == null ? "" : vo.getKeyword())
                .add("page", vo.getPageNum() + "").add("pageSize", vo.getPageSize() + "")
                .add("timestamp", time).add("sign", sign).build();
        PolyvLiveListInnerData resp = httpManager.executePostHttpByRequestBody(requestBody, LIST_URL, new TypeToken<PolyvLiveListInnerData>() {
        });

        if (null == resp || 200 != resp.getCode()) {
            log.warn("PolyvManager getLiveList failed vo:{} result:{}", vo, resp);
            return Result.newError(SHErrorCode.LIST_POLYV_LIVE_FAILED);
        }
        if (null == resp.getData() || CollectionUtils.isEmpty(resp.getData().getContents())) {
            return Result.newSuccess(pageResult);
        }
        resp.getData().getContents().forEach(c -> {
            PolyvLiveListResult temp = new PolyvLiveListResult();
            temp.setChannelId(c.getChannelId());
            temp.setWatchState(c.getWatchStatus());
            temp.setSplashImg(c.getSplashImg());
            temp.setWatchUrl(c.getWatchUrl());
            temp.setStartTime(c.getStartTime());
            temp.setName(c.getName());
            data.add(temp);
        });
        pageResult.setTotalCount(resp.getData().getTotalItems());
        pageResult.setPageNum(resp.getData().getPageNumber());
        pageResult.setPageSize(resp.getData().getPageSize());
        return Result.newSuccess(pageResult);
    }

    public PolyvLiveInnerData getLiveDetail(String ea, String channelId) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        String appId = polyvAccountEntity.getAppId();
        String appSecret = polyvAccountEntity.getAppSecret();
        String timestamp = System.currentTimeMillis() + "";
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", appId);
        requestMap.put("timestamp", timestamp);
        requestMap.put("channelId", channelId);
        String sign = null;
        try {
            sign = LiveSignUtil.getSign(requestMap, appSecret);
        } catch (Exception e) {
            log.info("PolyvManager getLiveList get sign fail : {}", e.getMessage());
            return null;
        }
        if (StringUtil.isNullOrEmpty(sign)) {
            return null;
        }
        RequestBody requestBody = new FormBody.Builder().add("appId", appId).add("channelId", channelId)
                .add("timestamp", timestamp).add("sign", sign).build();
        PolyvLiveInnerData resp = httpManager.executePostHttpByRequestBody(requestBody, DETAIL_URL, new TypeToken<PolyvLiveInnerData>() {
        });

        if (null == resp || 200 != resp.getCode()) {
            log.warn("PolyvManager getLiveDetail failed channelid:{} result:{}", channelId, resp);
        }

        return resp;
    }

    public Optional<MarketingLiveEntity> getMarketingLiveByPolyvId(String polyvId) {
        MarketingLiveEntity entity = marketingLiveDAO.getMarketingLiveByXiaoetongId(polyvId);
        if (entity == null) {
            return Optional.empty();
        }
        return Optional.of(entity);
    }


    /**
     * 查询场次
     */
    public List<String> querySessionId(String channalId, String ea) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        String appId = polyvAccountEntity.getAppId();
        String appSecret = polyvAccountEntity.getAppSecret();
        String timestamp = System.currentTimeMillis() + "";
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", appId);
        requestMap.put("timestamp", timestamp);
        requestMap.put("channelId", channalId);
        requestMap.put("page", "1");
        requestMap.put("pageSize", "1000");
        String sign = null;
        try {
            sign = LiveSignUtil.getSign(requestMap, appSecret);
        } catch (Exception e) {
            log.info("PolyvManager querySessionId get sign fail : {}", e.getMessage());
            return null;
        }
        if (StringUtil.isNullOrEmpty(sign)) {
            return null;
        }
        requestMap.put("sign", sign);
        String queryUrl = LIVE_SESSION + httpManager.transformUrlParams(requestMap);
        PolyvLiveSessionData data = httpManager.executeGetHttp(queryUrl, new TypeToken<PolyvLiveSessionData>() {
        });
        List<String> res = new ArrayList<>();
        if (null != data && data.getCode() == 200) {
            for (PolyvLiveSessionData.Contents content : data.getData().getContents()) {
                res.add(content.getSessionId());
            }
        }
        return res;
    }

    /**
     * 频道点赞详情
     */
    public Map<String, Integer> queryLikesDetail(String channelId, String ea) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        Map<String, Integer> res = new HashMap<>();
        String appId = polyvAccountEntity.getAppId();
        String appSecret = polyvAccountEntity.getAppSecret();
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", appId);
        requestMap.put("channelId", channelId);
        requestMap.put("pageSize", "1000");
        int pageNumber = 0;
        while (true) {
            pageNumber++;
            requestMap.remove("sign");
            requestMap.put("pageNumber", pageNumber + "");
            if (!queryLikeLoop(res, pageNumber, requestMap, appSecret)) {
                break;
            }
        }
        return res;
    }

    private boolean queryLikeLoop(Map<String, Integer> res, int pageNumber, Map<String, String> requestMap, String appSecret) {
        requestMap.put("timestamp", System.currentTimeMillis() + "");
        String sign = null;
        try {
            sign = LiveSignUtil.getSign(requestMap, appSecret);
        } catch (Exception e) {
            log.info("PolyvManager querySessionId get sign fail : {}", e.getMessage());
        }
        if (StringUtil.isNullOrEmpty(sign)) {
            return false;
        }
        requestMap.put("sign", sign);
        String likeUrl = LIVE_LIKE + httpManager.transformUrlParams(requestMap);
        PolyvLiveLikeData data = httpManager.executeGetHttp(likeUrl, new TypeToken<PolyvLiveLikeData>() {
        });
        if (null != data && data.getCode() == 200 && "success".equals(data.getStatus())) {
            PolyvLiveLikeData.LikeData likeData = data.getData();
            if (CollectionUtils.isEmpty(likeData.getContents())) {
                return false;
            }
            likeData.getContents().forEach(like -> {
                if (!res.containsKey(like.getUserid())) {
                    res.put(like.getUserid(), like.getCount());
                } else {
                    int current = res.get(like.getUserid());
                    res.put(like.getUserid(), current + like.getCount());
                }
            });
            if (likeData.getPageNumber() < likeData.getTotalPages()) {
                return true;
            }
        }
        return false;
    }


    /**
     * 提问
     */
    public Map<String, Integer> getQuestion(String ea, String channelId) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        String url = "http://api.polyv.net/live/v2/chat/" + channelId + "/getQuestion?";
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", polyvAccountEntity.getAppId());
        requestMap.put("timestamp", System.currentTimeMillis() + "");
        requestMap.put("begin", "0");
        requestMap.put("end", "-1");
        String sign = null;
        try {
            sign = LiveSignUtil.getSign(requestMap, polyvAccountEntity.getAppSecret());
        } catch (Exception e) {
            log.info("PolyvManager querySessionId get sign fail : {}", e.getMessage());
        }
        if (StringUtil.isNullOrEmpty(sign)) {
            return null;
        }
        requestMap.put("sign", sign);
        String finalUrl = url + httpManager.transformUrlParams(requestMap);
        PolyvLiveQuestionData questionData = httpManager.executeGetHttp(finalUrl, new TypeToken<PolyvLiveQuestionData>() {
        });
        Map<String, Integer> res = new HashMap<>();
        if (null != questionData && 200 == questionData.getCode() && CollectionUtils.isNotEmpty(questionData.getData())) {
            List<PolyvLiveQuestionData.QuestionData> collect = questionData.getData().stream().filter(q -> "S_QUESTION".equals(q.getEvent())).collect(Collectors.toList());
            collect.forEach(q -> {
                if (!res.containsKey(q.getUser().getUserId())) {
                    res.put(q.getUser().getUserId(), 1);
                } else {
                    res.put(q.getUser().getUserId(), res.get(q.getUser().getUserId()) + 1);
                }
            });
        }
        return res;
    }

    public void setExternalAuthSetting(String ea, String channelId, String externalRedirectUri, String externalUri) {
        try {
            PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
            if (polyvAccountEntity == null) {
                return;
            }
            String externalKey = RandomStringUtils.random(10, true, true);
            UpdatePolyvAuthSettingArg.AuthSetting arg = new UpdatePolyvAuthSettingArg.AuthSetting();
            arg.setRank(1);
            arg.setAuthType("external");
            arg.setEnabled("Y");
            arg.setExternalKey(externalKey);
            arg.setExternalUri(externalUri);
            arg.setExternalRedirectUri(externalRedirectUri);
            List<UpdatePolyvAuthSettingArg.AuthSetting> list = new ArrayList<>();
            UpdatePolyvAuthSettingArg setting = new UpdatePolyvAuthSettingArg();
            list.add(arg);
            setting.setAuthSettings(list);
            String url = "http://api.polyv.net/live/v3/channel/auth/update?";
            Map<String, String> requestMap = new HashMap<>();
            requestMap.put("appId", polyvAccountEntity.getAppId());
            requestMap.put("timestamp", System.currentTimeMillis() + "");
            requestMap.put("channelId", channelId);
            requestMap.put("sign", LiveSignUtil.getSign(requestMap, polyvAccountEntity.getAppSecret()));
            url = url + httpManager.transformUrlParams(requestMap);
            httpManager.executePostHttpReturnString(setting, url);
        } catch (Exception e) {
            log.info("set authSetting fail {}", e.getMessage());
        }
    }

    /**
     * 聊天
     */
    public Map<String, Integer> getChatList(String ea, String channelId, String startDay, String endDay) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        String appId = polyvAccountEntity.getAppId();
        Map<String, Integer> res = new HashMap<>();
        int pageNum = 0;
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", appId);
        requestMap.put("startDay", startDay);
        requestMap.put("endDay", endDay);
        requestMap.put("channelId", channelId);
        requestMap.put("userType", "slice,student");
        requestMap.put("pageSize", "1000");//1000
        while (true) {
            requestMap.remove("sign");
            pageNum++;
            requestMap.put("page", pageNum + "");
            if (!loopChatList(requestMap, polyvAccountEntity, res)) {
                break;
            }
        }
        return res;
    }

    private boolean loopChatList(Map<String, String> requestMap, PolyvAccountEntity polyvAccountEntity, Map<String, Integer> res) {
        requestMap.put("timestamp", System.currentTimeMillis() + "");
        try {
            requestMap.put("sign", LiveSignUtil.getSign(requestMap, polyvAccountEntity.getAppSecret()));
        } catch (Exception e) {
            log.info("loopChatList fail {}", e.getMessage());
            return false;
        }
        String url = "http://api.polyv.net/live/v3/channel/chat/get-history-page?" + httpManager.transformUrlParams(requestMap);
        PolyvLiveChatData polyvLiveChatData = httpManager.executeGetHttp(url, new TypeToken<PolyvLiveChatData>() {
        });
        if (null != polyvLiveChatData && 200 == polyvLiveChatData.getCode()) {
            PolyvLiveChatData.ChatData data = polyvLiveChatData.getData();
            if (CollectionUtils.isNotEmpty(data.getContents())) {
                data.getContents().forEach(d -> {
                    if (!res.containsKey(d.getUser().getUserId())) {
                        res.put(d.getUser().getUserId(), 1);
                    } else {
                        res.put(d.getUser().getUserId(), res.get(d.getUser().getUserId()) + 1);
                    }
                });

                if (data.getContents().size() == data.getPageSize()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 统计频道观看人次 次数 回放人次 次数
     *
     * @param ea
     * @param channelId
     * @return
     */
    public Map<String, Integer> getLiveChannelStatistics(String ea, String channelId) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        List<String> sessionId = this.querySessionId(channelId, ea);
        if (CollectionUtils.isEmpty(sessionId)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sessionId.forEach(id -> sb.append(id).append(","));
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", polyvAccountEntity.getAppId());
        requestMap.put("timestamp", System.currentTimeMillis() + "");
        requestMap.put("userId", polyvAccountEntity.getUserId());
        requestMap.put("channelId", channelId);
        requestMap.put("sessionIds", sb.toString());
        try {
            requestMap.put("sign", LiveSignUtil.getSign(requestMap, polyvAccountEntity.getAppSecret()));
        } catch (Exception e) {
            log.info("polyvmanager getLiveStatistics sign fail...");
            return null;
        }
        String url = "http://api.polyv.net/live/v3/channel/statistics/get-session-stats?" + httpManager.transformUrlParams(requestMap);
        PolyvLiveChannelStaticsData data = httpManager.executeGetHttp(url, new TypeToken<PolyvLiveChannelStaticsData>() {
        });
        if (null != data && 200 == data.getCode() && CollectionUtils.isNotEmpty(data.getData().getList())) {
            Map<String, Integer> res = new HashMap<>();
            int lpv = data.getData().getList().stream().filter(d -> null != d.getLivePV()).mapToInt(d -> d.getLivePV()).sum();
            int luv = data.getData().getList().stream().filter(d -> null != d.getLiveUV()).mapToInt(d -> d.getLiveUV()).sum();
            int pbpv = data.getData().getList().stream().filter(d -> null != d.getPlaybackPV()).mapToInt(d -> d.getPlaybackPV()).sum();
            int pbuv = data.getData().getList().stream().filter(d -> null != d.getPlaybackUV()).mapToInt(d -> d.getPlaybackUV()).sum();
//            int duration = data.getData().getList().stream().filter(d -> null != d.getDuration()).mapToInt(d -> d.getDuration()).sum();
//            int totalPlayDuration = data.getData().getList().stream().filter(d -> null != d.getTotalPlayDuration()).mapToInt(d -> d.getTotalPlayDuration()).sum();
            res.put("livePV", lpv);
            res.put("liveUV", luv);
            res.put("playbackPV", pbpv);
            res.put("playbackUV", pbuv);
//            res.put("duration", duration);
//            res.put("totalPlayDuration", totalPlayDuration);
            return res;
        }
        return null;
    }

    /**
     * 获取直播观众观看信息
     *
     * @param ea
     * @param channelId
     * @return
     */
    public Map<String, Integer> getLiveViewerStatistics(String ea, String channelId, String startTime, String endTime) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return null;
        }
        int page = 0;
        Map<String, Integer> res = new HashMap<>();
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("appId", polyvAccountEntity.getAppId());
        requestMap.put("pageSize", "5000");
        requestMap.put("startTime", startTime);
        requestMap.put("endTime", endTime);
        requestMap.put("channelId", channelId);
        while (true) {
            requestMap.remove("sign");
            page++;
            requestMap.put("page", page + "");
            if (!loopViewerStatistics(requestMap, channelId, polyvAccountEntity.getAppSecret(), res)) {
                break;
            }
        }
        return res;
    }

    private boolean loopViewerStatistics(Map<String, String> requestMap, String channelId, String appSecret, Map<String, Integer> res) {
        boolean result = false;
        try {
            requestMap.put("timestamp", System.currentTimeMillis() + "");
            requestMap.put("sign", LiveSignUtil.getSign(requestMap, appSecret));
        } catch (Exception e) {
            log.info("getLiveViewerStatistics sign fail...");
            return result;
        }
        String url = "http://api.polyv.net/live/v2/statistics/" + channelId + "/viewlog?" + httpManager.transformUrlParams(requestMap);
        PolyvLiveViewerStaticsData data = httpManager.executeGetHttp(url, new TypeToken<PolyvLiveViewerStaticsData>() {
        });
        if (null != data && 200 == data.getCode() && CollectionUtils.isNotEmpty(data.getData().getContents())) {
            result = !data.getData().getLastPage();
//            int totalViewUsers = 0;
//            int viewTimes = 0;
//            int totalRecordUsers = 0;
//            int recordTimes = 0;
//            Set<String> liveUser = new HashSet<>();
//            Set<String> vodUser = new HashSet<>();
            for (PolyvLiveViewerStaticsData.Contents content : data.getData().getContents()) {
                String playDurationkey = content.getViewId() + "@_@" + content.getType();
                if (!res.containsKey(playDurationkey)) {
                    res.put(playDurationkey, content.getPlayDuration());
                } else {
                    res.put(playDurationkey, res.get(playDurationkey) + content.getPlayDuration());
                }
//                if("vod".equals(content.getType())){
//                    recordTimes++;
//                    vodUser.add(content.getViewId());
//                }
//                if("live".equals(content.getType())){
//                    viewTimes++;
//                    liveUser.add(content.getViewId());
//                }
            }
//            totalViewUsers = liveUser.size();
//            totalRecordUsers = vodUser.size();
//            res.put("totalViewUsers", res.get("totalViewUsers") == null ? totalViewUsers : res.get("totalViewUsers") + totalViewUsers);
//            res.put("viewTimes", res.get("viewTimes") == null ? viewTimes : res.get("viewTimes") + viewTimes);
//            res.put("totalRecordUsers", res.get("totalRecordUsers") == null ? totalRecordUsers : res.get("totalRecordUsers") + totalRecordUsers);
//            res.put("recordTimes", res.get("recordTimes") == null ? recordTimes : res.get("recordTimes") + recordTimes);
//            liveUser.clear();
//            vodUser.clear();
        }
        return result;
    }


    public void syncLiveDataById(String ea, String marketingEventId, String polyvLiveId) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        if (marketingLiveEntity == null) {
            return;
        }
        List<MarketingLiveViewLoginEntity> viewLoginEntities = marketingLiveViewLoginDAO.getBymarketingEventId(marketingEventId);
        List<String> allUserphones = viewLoginEntities.stream().map(MarketingLiveViewLoginEntity::getPhone).collect(Collectors.toList());
        List<LiveUserStatusEntity> syncDataList = null;
        if (CollectionUtils.isNotEmpty(allUserphones)) {
            syncDataList = liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveIdAndPhone(polyvLiveId, allUserphones);
        }
        Map<String, LiveUserStatusEntity> syncphoneMap = null;
        if (CollectionUtils.isNotEmpty(syncDataList)) {
            syncphoneMap = syncDataList.stream().collect(Collectors.toMap(LiveUserStatusEntity::getPhone, Function.identity(), (v1, v2) -> v2));
        }
        Calendar c = Calendar.getInstance();
        c.setTime(marketingLiveEntity.getStartTime());
        String startTime = DateUtil.getFirstDayOfMonth(c.get(Calendar.YEAR), c.get(Calendar.MONTH) + 1).getTime() + "";
        String endTime = DateUtil.getEndDayOfMonth(c.get(Calendar.YEAR), c.get(Calendar.MONTH) + 1).getTime() + "";
        Map<String, Integer> liveViewerStatistics = this.getLiveViewerStatistics(ea, polyvLiveId, startTime, endTime);
        //点赞数据
        Map<String, Integer> likeMap = this.queryLikesDetail(polyvLiveId, ea);
        //提问
        Map<String, Integer> question = this.getQuestion(ea, polyvLiveId);
        String startDay = DateUtil.format(marketingLiveEntity.getStartTime(), "yyyy-MM-dd");
        String endDay = DateUtil.format(DateUtil.plusMonth(marketingLiveEntity.getEndTime(), 1), "yyyy-MM-dd");
        //聊天
        Map<String, Integer> chatList = this.getChatList(ea, polyvLiveId, startDay, endDay);

        List<LiveUserStatusEntity> insertViewUserList = Lists.newArrayList();
        List<LiveUserStatusEntity> updateViewUserList = Lists.newArrayList();
        String liveKey = "%s@_@live";
        String vodKey = "%s@_@vod";
        for (MarketingLiveViewLoginEntity user : viewLoginEntities) {
            String uniqueUserId = StringUtils.isNotBlank(user.getOuterUserId()) ? user.getOuterUserId() : user.getId();
            String live = String.format(liveKey, uniqueUserId);
            String vod = String.format(vodKey, uniqueUserId);
            Integer viewTime = null;
            Integer replyTime = null;
            if (null != liveViewerStatistics) {
                replyTime = liveViewerStatistics.get(vod);
                viewTime = liveViewerStatistics.get(live);
            }
            if (null == viewTime) {
                viewTime = 0;
            } else {
                viewTime = viewTime / 60 == 0 ? 1 : viewTime / 60;
            }
            if (null == replyTime) {
                replyTime = 0;
            } else {
                replyTime = replyTime / 60 == 0 ? 1 : replyTime / 60;
            }
            Integer interactiveCount = 0;
            if (null != likeMap) {
                interactiveCount += likeMap.get(uniqueUserId) == null ? 0 : likeMap.get(uniqueUserId);
            }
            if (question != null) {
                interactiveCount += question.get(uniqueUserId) == null ? 0 : question.get(uniqueUserId);
            }
            if (null != chatList) {
                interactiveCount += chatList.get(uniqueUserId) == null ? 0 : chatList.get(uniqueUserId);
            }

            if (syncphoneMap == null || syncphoneMap.get(user.getPhone()) == null) {
                LiveUserStatusEntity liveUserStatusEntity = new LiveUserStatusEntity();
                liveUserStatusEntity.setId(UUIDUtil.getUUID());
                liveUserStatusEntity.setXiaoetongLiveId(polyvLiveId);
                liveUserStatusEntity.setType(4);
                liveUserStatusEntity.setPhone(user.getPhone());
                liveUserStatusEntity.setViewTime(viewTime);
                liveUserStatusEntity.setViewStatus(viewTime == 0 ? 0 : 1);
                liveUserStatusEntity.setReplayTime(replyTime);
                liveUserStatusEntity.setReplayStatus(replyTime == 0 ? 0 : 1);
                liveUserStatusEntity.setInteractiveCount(interactiveCount);
                liveUserStatusEntity.setLastViewTime(user.getCreateTime());
                insertViewUserList.add(liveUserStatusEntity);
                // 发送直播行为数据
                ThreadPoolUtils.execute(() -> {
                    Optional<String> browserUserFinger = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, user.getPhone());
                    RecordActionArg recordActionArg = new RecordActionArg();
                    recordActionArg.setEa(ea);
                    recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                    recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                    recordActionArg.setObjectId(marketingLiveEntity.getId());
                    recordActionArg.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
                    if (browserUserFinger.isPresent()) {
                        recordActionArg.setFingerPrint(browserUserFinger.get());
                    }
                    if (liveUserStatusEntity.getViewTime() > 0) {
                        recordActionArg.setActionTime(System.currentTimeMillis());
                        recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_LIVE.getActionType());
                        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                    }
                    if (liveUserStatusEntity.getReplayTime() > 0) {
                        recordActionArg.setActionTime(System.currentTimeMillis());
                        recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_RECORD.getActionType());
                        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                    }
                    if (liveUserStatusEntity.getInteractiveCount() > 0) {
                        recordActionArg.setActionTime(System.currentTimeMillis());
                        recordActionArg.setActionType(MarketingUserActionType.LIVE_CHAT.getActionType());
                        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            } else {
                LiveUserStatusEntity liveUserStatusEntity = syncphoneMap.get(user.getPhone());
                if (liveUserStatusEntity.getViewTime() != null && viewTime == liveUserStatusEntity.getViewTime() && replyTime == liveUserStatusEntity.getReplayTime()) {
                    continue;
                }
                liveUserStatusEntity.setViewTime(viewTime);
                liveUserStatusEntity.setViewStatus(viewTime == 0 ? 0 : 1);
                liveUserStatusEntity.setReplayTime(replyTime);
                liveUserStatusEntity.setReplayStatus(replyTime == 0 ? 0 : 1);
                liveUserStatusEntity.setInteractiveCount(interactiveCount);
                updateViewUserList.add(liveUserStatusEntity);
                // 发送直播行为数据
                ThreadPoolUtils.execute(() -> {
                    Optional<String> browserUserFinger = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, user.getPhone());
                    RecordActionArg recordActionArg = new RecordActionArg();
                    recordActionArg.setEa(ea);
                    recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                    recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                    recordActionArg.setObjectId(marketingLiveEntity.getId());
                    recordActionArg.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
                    if (browserUserFinger.isPresent()) {
                        recordActionArg.setFingerPrint(browserUserFinger.get());
                    }
                    if (liveUserStatusEntity.getViewTime() > 0) {
                        recordActionArg.setActionTime(System.currentTimeMillis());
                        recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_LIVE.getActionType());
                        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                    }
                    if (liveUserStatusEntity.getReplayTime() > 0) {
                        recordActionArg.setActionTime(System.currentTimeMillis());
                        recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_RECORD.getActionType());
                        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                    }
                    if (liveUserStatusEntity.getInteractiveCount() > 0) {
                        recordActionArg.setActionTime(System.currentTimeMillis());
                        recordActionArg.setActionType(MarketingUserActionType.LIVE_CHAT.getActionType());
                        actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                    }
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            }
        }

        if (CollectionUtils.isNotEmpty(insertViewUserList)) {
            PageUtil page = new PageUtil(insertViewUserList, 200);
            for (int i = 0; i < page.getPageCount(); i++) {
                List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                liveUserStatusDAO.batchInsert(currentPage);
            }
        }
        if (CollectionUtils.isNotEmpty(updateViewUserList)) {
            PageUtil page = new PageUtil(updateViewUserList, 200);
            for (int i = 0; i < page.getPageCount(); i++) {
                List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                liveUserStatusDAO.batchUpdatePolyvData(currentPage);
            }
        }

        List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getByXiaoetongLiveId(eieaConverter.enterpriseAccountToId(ea), polyvLiveId);
        if (CollectionUtils.isEmpty(marketingLiveStatisticsList)) {
            return;
        }
        MarketingLiveStatistics dbEntry = marketingLiveStatisticsList.get(0);
        Map<String, Integer> liveChannelStatistics = this.getLiveChannelStatistics(ea, polyvLiveId);
        if (null == liveChannelStatistics || liveChannelStatistics.size() == 0) {
            return;
        }
        Integer viewTimes = liveChannelStatistics.get("livePV") == null ? 0 : liveChannelStatistics.get("livePV");//直播观看次数
        Integer totalViewUser = liveChannelStatistics.get("liveUV") == null ? 0 : liveChannelStatistics.get("liveUV");//直播观看用户数
        Integer recordTimes = liveChannelStatistics.get("playbackPV") == null ? 0 : liveChannelStatistics.get("playbackPV");// 回放观看次数
        Integer totalRecordUsers = liveChannelStatistics.get("playbackUV") == null ? 0 : liveChannelStatistics.get("playbackUV");//回放观看用户数

//        Integer viewTimes =liveViewerStatistics.get("viewTimes");//观看直播和回放的总次数
//        Integer recordTimes = liveViewerStatistics.get("recordTimes");//观看回放次数
//        Integer totalViewUser =liveViewerStatistics.get("totalViewUsers");//观看直播人数
//        Integer totalRecordUsers = liveViewerStatistics.get("totalRecordUsers");//观看回放人数
        Set<String> totalChatUserSet = new HashSet<>();
        Integer chatTimes = 0;//互动次数
        if (null != likeMap) {
            totalChatUserSet.addAll(likeMap.keySet());
            chatTimes += sumValue(likeMap);
        }
        if (null != question) {
            totalChatUserSet.addAll(question.keySet());
            chatTimes += sumValue(question);
        }
        if (null != chatList) {
            totalChatUserSet.addAll(chatList.keySet());
            chatTimes += sumValue(chatList);
        }
        Integer totalChatUser = totalChatUserSet.size();//互动人数
        totalChatUserSet.clear();
        marketingLiveStatisticsDAO.updateStatisticsByPolyvId(dbEntry.getId(), totalViewUser, viewTimes, totalRecordUsers, recordTimes, totalChatUser, chatTimes);
    }

    private Integer sumValue(Map<String, Integer> map) {
        return map.values().stream().mapToInt(v -> v).sum();
    }

    public void syncLiveStatusByEaAndChannelId(String ea, String channelId) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return;
        }
        PolyvLiveInnerData liveDetail = this.getLiveDetail(ea, channelId);
        if (null != liveDetail && liveDetail.getCode() == 200 && liveDetail.getData() != null) {
            String statusString = liveDetail.getData().getWatchStatus();
            if (StringUtils.isEmpty(statusString)) {
                return;
            }
            int marketingLiveStatus = LiveStatusEnum.NOT_START.getStatus();
            if ("waiting".equals(statusString)) {
                marketingLiveStatus = LiveStatusEnum.NOT_START.getStatus();
            } else if ("live".equals(statusString)) {
                marketingLiveStatus = LiveStatusEnum.PROCESSING.getStatus();
            } else if ("end".equals(statusString)) {
                marketingLiveStatus = LiveStatusEnum.FINISH.getStatus();
            } else if ("playback".equals(statusString)) {
                marketingLiveStatus = LiveStatusEnum.FINISH_WITH_REPLAY.getStatus();
            }
            marketingLiveDAO.updateXiaoetongLiveStatus(eieaConverter.enterpriseAccountToId(ea), channelId, marketingLiveStatus);
            marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(eieaConverter.enterpriseAccountToId(ea), channelId, marketingLiveStatus);
        }
    }

    public Result<PolyvAccountResult> bindAccount(String ea) {
        PolyvAccountResult result = new PolyvAccountResult();
        PolyvAccountEntity entity = polyvAccountDAO.getByEa(ea);
        result.setIsBind(null != entity);
        if (result.getIsBind()) {
            result.setAppId(entity.getAppId());
            result.setAppSecret(entity.getAppSecret());
            result.setUserId(entity.getUserId());
        }

        return Result.newSuccess(result);
    }

    public Result<PolyvLiveListResult> getLiveInfo(String ea, String id) {
        PolyvAccountEntity polyvAccountEntity = polyvAccountDAO.getByEa(ea);
        if (polyvAccountEntity == null) {
            return Result.newError(SHErrorCode.POLYV_NOT_BIND_ACCOUNT);
        }
        PolyvLiveInnerData liveDetail = this.getLiveDetail(ea, id);
        if (null != liveDetail && 200 == liveDetail.getCode() && null != liveDetail.getData()) {
            PolyvLiveListResult res = new PolyvLiveListResult();
            res.setSplashImg(liveDetail.getData().getSplashImg());
            res.setName(liveDetail.getData().getName());
            res.setStartTime(liveDetail.getData().getStartTime());
            res.setWatchState(liveDetail.getData().getWatchStatus());
            return Result.newSuccess(res);
        }
        return Result.newSuccess();
    }

    /**
     * 处理保利威直播无需报名可观看直播的用户
     * @param marketingEventId
     * @param arg
     * @return
     */
    public String handlePolyvUser(String marketingEventId, CustomizeFormDataEnroll arg){
        if (StringUtils.isBlank(marketingEventId) || arg == null) {
            return null;
        }
        Integer thirdPlatformType = arg.getThirdPlatformType();
        String thirdPlatformUserId = arg.getThirdPlatformUserId();
        String phone = arg.getPhone();
        if (!Objects.equals(thirdPlatformType, LivePlatformEnum.POLYV.getType()) || StringUtils.isBlank(thirdPlatformUserId) || StringUtils.isBlank(phone)) {
            log.info("handlePolyvUser thirdPlatformType:{},thirdPlatformUserId:{},phone:{}", thirdPlatformType, thirdPlatformUserId, phone);
            return null;
        }

        String userId;
        List<MarketingLiveViewLoginEntity> marketingLiveViewLoginEntityList = marketingLiveViewLoginDAO.getByMarketingEventIdAndPhones(marketingEventId, Lists.newArrayList(phone));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(marketingLiveViewLoginEntityList)) {
            userId = marketingLiveViewLoginEntityList.get(0).getId();
        } else {
            MarketingLiveViewLoginEntity marketingLiveViewLoginEntity = new MarketingLiveViewLoginEntity();
            marketingLiveViewLoginEntity.setId(UUIDUtil.getUUID());
            marketingLiveViewLoginEntity.setMarketingEventId(marketingEventId);
            marketingLiveViewLoginEntity.setPhone(phone);
            marketingLiveViewLoginEntity.setOuterUserId(thirdPlatformUserId);
            marketingLiveViewLoginDAO.insert(marketingLiveViewLoginEntity);
            userId = marketingLiveViewLoginEntity.getId();
        }
        return userId;
    }
}
