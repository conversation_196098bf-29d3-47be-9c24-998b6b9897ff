package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.CardEntity;
import java.util.List;

import com.facishare.marketing.provider.entity.UserEntity;
import org.apache.ibatis.annotations.*;
import org.checkerframework.checker.tainting.qual.PolyTainted;
import org.checkerframework.framework.qual.PreconditionAnnotation;

public interface CardDAO {

    @Select("SELECT * FROM card WHERE uid=#{uid}")
    CardEntity queryCardInfoByUid(@Param("uid") String uid);

    @Select("SELECT * FROM card WHERE \"id\"=#{id}")
    CardEntity queryCardInfoById(@Param("id") String id);

    @Select("<script>" + "SELECT * FROM card WHERE id IN "
            + "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CardEntity> queryCardInfoByIds(@Param("ids") List<String> ids);

    @Select("<script>SELECT * FROM card where uid IN <foreach collection=\"uids\" item = 'item' open=\"(\" close=\")\" separator=\",\">  #{item}</foreach></script>")
    List<CardEntity> listByUids(@Param("uids") List<String> uids);

    @Insert("INSERT INTO card(\n" +
            "        \"id\",\n" +
            "        \"uid\",\n" +
            "        \"name\",\n" +
            "        \"avatar\",\n" +
            "        \"avatar_thumbnail\",\n" +
            "        \"gender\",\n" +
            "        \"phone\",\n" +
            "        \"email\",\n" +
            "        \"wechat\",\n" +
            "        \"introduction\",\n" +
            "        \"vocation\",\n" +
            "        \"department\",\n" +
            "        \"company_name\",\n" +
            "        \"company_address\",\n" +
            "        \"qr_url\",\n" +
            "        \"visual_range_string\",\n" +
            "        \"trade_code\",\n" +
            "        \"avatar_path\",\n" +
            "        \"card_template_id\",\n" +
            "        \"avatar_thumbnail_path\"\n" +
            "        )\n" +
            "        VALUES (\n" +
            "        #{id},\n" +
            "        #{uid},\n" +
            "        #{name},\n" +
            "        #{avatar},\n" +
            "        #{avatarThumbnail},\n" +
            "        #{gender},\n" +
            "        #{phone},\n" +
            "        #{email},\n" +
            "        #{wechat},\n" +
            "        #{introduction},\n" +
            "        #{vocation},\n" +
            "        #{department},\n" +
            "        #{companyName},\n" +
            "        #{companyAddress},\n" +
            "        #{qrUrl},\n" +
            "        #{visualRangeString},\n" +
            "        #{tradeCode},\n" +
            "        #{avatarPath},\n" +
            "        #{cardTemplateId},\n" +
            "        #{avatarThumbnailPath}\n" +
            "        ) ON CONFLICT DO NOTHING")
    int insert(CardEntity cardEntity);

    @Update("<script>" +
            " UPDATE card\n" +
            "        <set>\n" +
            "            <if test=\"name != null\">\n" +
            "                \"name\" = #{name},\n" +
            "            </if>\n" +
            "            <if test=\"avatar != null\">\n" +
            "                \"avatar\" = #{avatar},\n" +
            "            </if>\n" +
            "            <if test=\"avatarThumbnail != null\">\n" +
            "                \"avatar_thumbnail\" = #{avatarThumbnail},\n" +
            "            </if>\n" +
            "            <if test=\"avatarPath != null\">\n" +
            "                \"avatar_path\" = #{avatarPath},\n" +
            "            </if>\n" +
            "            <if test='gender != null and gender > 0'>\n" +
            "                \"gender\" = #{gender},\n" +
            "            </if>\n" +
            "            <if test=\"phone != null\">\n" +
            "                \"phone\" = #{phone},\n" +
            "            </if>\n" +
            "            <if test=\"email != null\">\n" +
            "                \"email\" = #{email},\n" +
            "            </if>\n" +
            "            <if test=\"wechat != null\">\n" +
            "                \"wechat\" = #{wechat},\n" +
            "            </if>\n" +
            "            <if test=\"introduction != null\">\n" +
            "                \"introduction\" = #{introduction},\n" +
            "            </if>\n" +
            "            <if test=\"vocation != null\">\n" +
            "                \"vocation\" = #{vocation},\n" +
            "            </if>\n" +
            "            <if test=\"department != null\">\n" +
            "                \"department\" = #{department},\n" +
            "            </if>\n" +
            "            <if test=\"companyName != null\">\n" +
            "                \"company_name\" = #{companyName},\n" +
            "            </if>\n" +
            "            <if test=\"tradeCode != null\">\n" +
            "                \"trade_code\" = #{tradeCode},\n" +
            "            </if>\n" +
            "            <if test=\"companyAddress != null\">\n" +
            "                \"company_address\" = #{companyAddress},\n" +
            "            </if>\n" +
            "            <if test=\"qrUrl != null\">\n" +
            "                \"qr_url\" = #{qrUrl},\n" +
            "            </if>\n" +
            "            <if test=\"lastModifyTime != null\">\n" +
            "                \"last_modify_time\" = #{lastModifyTime},\n" +
            "            </if>\n" +
            "            <if test=\"visualRangeString != null\">\n" +
            "                \"visual_range_string\" = #{visualRangeString},\n" +
            "            </if>\n" +
            "            <if test=\"avatarThumbnailPath != null\">\n" +
            "                \"avatar_thumbnail_path\" = #{avatarThumbnailPath}\n" +
            "            </if>\n" +
            "        </set>\n" +
            "        WHERE id = #{id}"
            + "</script>")
    int updateById(CardEntity cardEntity);

    @Update("update card set card_template_id = #{cardTemplateId} where uid=#{uid}")
    int updateCardTemplateById(@Param("uid") String uid, @Param("cardTemplateId") String cardTemplateId);

    @Update("<script>" +
                "update card set card_template_id = #{cardTemplateId} where uid in " +
                "<foreach collection = 'uids' item = 'item' open = '(' separator = ',' close = ')'>" +
                    "#{item}" +
                "</foreach>" +
            "</script>")
    int batchUpdateCardTemplateByUids(@Param("uids") List<String> uids, @Param("cardTemplateId") String cardTemplateId);

    @Delete("DELETE FROM card WHERE uid=#{uid}")
    int deleteByUid(@Param("uid")String uid);

    @Select("SELECT * FROM card WHERE card_template_id = #{cardTemplateId}")
    List<CardEntity> queryByCardTplId(@Param("cardTemplateId") String cardTemplateId);

    @Select("SELECT count(1) FROM card WHERE card_template_id = #{cardTemplateId}")
    Integer getTotalByCardTplId(@Param("cardTemplateId") String cardTemplateId);

    @Update("UPDATE card SET uid = #{newUid}, last_modify_time = now() WHERE uid = #{oldUid}")
    int updateUidByOldUid(@Param(value = "oldUid") String oldUid, @Param("newUid") String newUid);
}
