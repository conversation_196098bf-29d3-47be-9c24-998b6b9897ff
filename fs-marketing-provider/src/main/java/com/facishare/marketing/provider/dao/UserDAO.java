package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.UserEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * Created by zhengh on 2019/4/16.
 */
public interface UserDAO {
    @Select("SELECT * FROM \"user\" WHERE uid=#{uid}")
    UserEntity queryByUid(String uid);

    @Select("<script>SELECT * FROM \"user\" where uid IN <foreach collection=\"uids\" item = 'item' open=\"(\" close=\")\" separator=\",\">  #{item}</foreach></script>")
    List<UserEntity> listByUids(@Param("uids") List<String> uids);

    @Select("SELECT * FROM \"user\" WHERE openid=#{openid} AND appid=#{appid}")
    UserEntity queryByOpenidAndAppid(@Param("openid")String openid, @Param("appid") String appid);

    @Select("SELECT * FROM \"user\" WHERE openid=#{openid} AND appid=#{appid} AND corpid=#{corpId}")
    UserEntity queryUserByOpenIdAppIdCorpId(@Param("openid")String openid, @Param("appid") String appid, @Param("corpId") String corpId);

    @Select("SELECT * FROM \"user\" WHERE corpid=#{corpid} AND qy_user_id=#{qyUserId} AND appid=#{appid}")
    UserEntity queryByCorpIdAndQYUserIdAndAppid(@Param("corpid")String corpid, @Param("qyUserId")String qyUserId, @Param("appid") String appid);

    @Select("<script>" +
            "select a.qy_user_id, a.avatar from \"user\" a " +
            "left join qywx_corp_agent_config b on a.corpid = b.corpid " +
            "where a.avatar is not null and b.ea = #{ea} and a.qy_user_id in " +
            "<foreach open='(' close=')' separator=',' collection='qyUserIds' index='idx'>#{qyUserIds[${idx}]}</foreach>" +
            "</script>")
    List<UserEntity> queryByEaAndQyUserIds(@Param("ea")String ea, @Param("qyUserIds") List<String> qyUserIds);

    @Select("SELECT * FROM \"user\" WHERE qy_user_id=#{qyUserId} AND appid=#{appid}")
    List<UserEntity> queryByQYUserIdAndAppid(@Param("qyUserId")String qyUserId, @Param("appid") String appid);

    @Insert("INSERT INTO \"user\""
            + "(uid, openid, name, avatar, gender, city, province, country, create_time, last_modify_time, appid, corpid, qy_user_id, wx_union_id, ding_user_id, ding_union_id,external_user_id)"
            + " VALUES"
            + " (#{obj.uid}, #{obj.openid}, #{obj.name}, #{obj.avatar}, #{obj.gender}, #{obj.city}, #{obj.province}, #{obj.country}, now(), now(), #{obj.appid}, #{obj.corpid}, #{obj.qyUserId}, #{obj.wxUnionId}, #{obj.dingUserId}, #{obj.dingUnionId}, #{obj.externalUserId})")
    int insert(@Param("obj") UserEntity userEntity);

    @Update("<script>"
            + "UPDATE \"user\""
            + "    <set>"
            + "         <if test=\"name != null\">\n"
            + "             name = #{name},\n"
            + "          </if>\n"
            + "         <if test=\"avatar != null\">\n"
            + "             avatar = #{avatar},\n"
            + "          </if>\n"
            + "         <if test=\"gender != null\">\n"
            + "             gender = #{gender},\n"
            + "          </if>\n"
            + "         <if test=\"city != null\">\n"
            + "             city = #{city},\n"
            + "          </if>\n"
            + "         <if test=\"province != null\">\n"
            + "             province = #{province},\n"
            + "          </if>\n"
            + "         <if test=\"country != null\">\n"
            + "             country = #{country},\n"
            + "          </if>\n"
            + "         <if test=\"appid != null\">\n"
            + "             appid = #{appid},\n"
            + "          </if>\n"
            + "         <if test=\"corpid != null\">\n"
            + "             corpid = #{corpid},\n"
            + "          </if>\n"
            + "         <if test=\"qyUserId != null\">\n"
            + "             qy_user_id = #{qyUserId},\n"
            + "          </if>\n"
            + "         <if test=\"openid != null\">\n"
            + "             openid = #{openid},\n"
            + "          </if>\n"
            + "         <if test=\"externalUserId != null\">\n"
            + "             external_user_id = #{externalUserId},\n"
            + "          </if>\n"
            + "         <if test=\"wxUnionId != null\">\n"
            + "             wx_union_id = #{wxUnionId},\n"
            + "          </if>\n"
            + "         last_modify_time = now()\n"
            + "    </set>"
            + "    WHERE uid = #{uid}"
            + "</script>")
    int updateUser(UserEntity userEntity);

    @Update(" UPDATE \"user\" SET appid = #{appid}, corpid = #{corpid},  qy_user_id = #{qyUserId} WHERE uid = #{uid} ")
    int addQywxUserInfo(@Param("appid") String appId, @Param("corpid") String corpid, @Param("qyUserId") String qyUserId, @Param("uid") String uid);

    @Update("UPDATE \"user\" SET openid=#{openId}, wx_union_id=#{wxUnionId}, last_modify_time=now() WHERE uid=#{uid}")
    int updateUserWxInfo(@Param("uid")String uid, @Param("openId")String openId, @Param("wxUnionId")String wxUnionId);

    @Update(" UPDATE \"user\" SET appid = #{appid}, corpid = #{corpid},  ding_user_id = #{dingUserId} WHERE uid = #{uid}")
    int addDingUserInfo(@Param("appid") String appId, @Param("corpid") String corpid, @Param("dingUserId") String dingUserId, @Param("uid") String uid);

    @Update(" UPDATE \"user\" SET openid = #{openid} WHERE uid = #{uid}")
    int updateUserOpenId(@Param("openid") String openid, @Param("uid") String uid);

    @Update(" UPDATE \"user\" SET openid = #{openid}, wx_union_id=#{wxUnionId} WHERE uid = #{uid}")
    int updateUserOpenIdAndWxUnionId(@Param("openid") String openid, @Param("uid") String uid, @Param("wxUnionId") String wxUnionId);


    @Update(" UPDATE \"user\" SET openid = #{openid}, wx_union_id=#{wxUnionId}, appid=#{appId} WHERE uid = #{uid}")
    int updateUserAppId(@Param("openid") String openid, @Param("uid") String uid, @Param("wxUnionId") String wxUnionId, @Param("appId") String appId);

    @Update("UPDATE \"user\" SET wx_union_id = #{wxUnionId} WHERE uid = #{uid}")
    int updateUserUnionId(@Param("wxUnionId") String wxUnionId, @Param("uid") String uid);

    @Select("SELECT * FROM \"user\" WHERE corpid=#{corpid} AND ding_user_id=#{userId}")
    UserEntity queryByCorpIdAndDingUserIdAndAppid(@Param("corpid") String corpid, @Param("userId") String userId);

    @Select("SELECT * FROM \"user\" WHERE corpid=#{corpid} AND ding_user_id=#{userId}")
    UserEntity queryByCorpIdAndDingUserId(@Param("corpid") String corpid, @Param("userId") String userId);

    @Select("SELECT count(*) FROM \"user\" WHERE corpid=#{corpid} AND appid=#{appid}")
    int queryUserNumByCorpId(@Param("corpid") String corpid, @Param("appid") String appid);

    @Delete("DELETE FROM \"user\" WHERE uid=#{uid}")
    void deleteOldUserByUid(@Param("uid") String uid);

    @Update("UPDATE \"user\" SET appid = #{appid}, openid = #{openid},  wx_union_id = #{wxUnionId} WHERE uid = #{uid}")
    void updateUserOpenIdAndAppId(@Param("appid") String appid, @Param("openid") String openid, @Param("wxUnionId") String wxUnionId, @Param("uid") String uid);

    @Update("UPDATE \"user\" SET name = #{name} WHERE uid = #{uid}")
    void updateUserName(@Param("name") String name, @Param("uid") String uid);

    @Select("SELECT * FROM \"user\" WHERE corpid=#{corpid} ")
    List<UserEntity> queryUsersByCorpId(@Param("corpid") String corpid);

    @Select("SELECT * FROM \"user\" WHERE corpid=#{corpid} AND wx_union_id=#{wxUnionId}")
    UserEntity queryByCorpIdAndUnionId(@Param("corpid") String corpid, @Param("wxUnionId") String wxUnionId);

    @Select("SELECT * FROM \"user\" WHERE wx_union_id=#{wxUnionId} order by create_time desc limit 1")
    UserEntity queryByUnionId(@Param("wxUnionId")String wxUnionId);

    @Select("SELECT * FROM \"user\" WHERE openid=#{openId} AND external_user_id=#{externalUserId} AND appid=#{appid}")
    UserEntity queryByOpenIdAndExternalUserIdAndAppId(@Param("openId")String openId, @Param("externalUserId")String externalUserId, @Param("appid") String appid);

    @Select("<script>"
            + "SELECT uid,qy_user_id FROM \"user\" WHERE qy_user_id in"
            + " <foreach open='(' close=')' separator=',' collection='qyUserIdList' index='idx'>"
            + "     #{qyUserIdList[${idx}]}"
            + "</foreach>"
            + "</script>")
    List<UserEntity> queryUidByQyUserIdList(@Param("qyUserIdList")List<String> qyUserIdList);

    @Select("SELECT * FROM \"user\" WHERE openid=#{openId} order by create_time desc limit 1")
    UserEntity queryByOpenId(@Param("openId") String openId);

    @Select("SELECT * FROM \"user\" WHERE openid=#{openId}")
    List<UserEntity> queryAllByOpenId(@Param("openId") String openId);

    @Select("SELECT uid FROM \"user\" WHERE appid=#{appid}")
    List<String> queryByWxAppId(@Param("appid") String appid);

    @Select("select a.* from \"user\" a " +
            "left join qywx_corp_agent_config b on a.corpid = b.corpid " +
            "where b.ea = #{ea} and a.external_user_id = #{externalUserId} ")
    List<UserEntity> queryByExternalUserId(@Param("ea")String ea, @Param("externalUserId") String externalUserId);

    @Update("UPDATE \"user\" SET external_user_id = #{externalUserId} WHERE uid = #{uid}")
    int updateExternalUserId(@Param("uid") String uid, @Param("externalUserId") String externalUserId);

    @Select("select a.* from \"user\" a " +
            "left join qywx_corp_agent_config b on a.corpid = b.corpid " +
            "where b.ea = #{ea} and a.qy_user_id = #{qyUserId} ")
    List<UserEntity> queryByEaAndQyUserId(@Param("ea")String ea, @Param("qyUserId")String qyUserId);

    @Update("UPDATE \"user\" SET qy_user_id = #{qyUserId} WHERE uid = #{uid}")
    int updateQyUserId(@Param("uid") String uid, @Param("qyUserId") String qyUserId);

    @Update("UPDATE \"user\" SET corpid = #{newCorpId} WHERE uid = #{uid}")
    int updateCorpId(@Param("uid") String uid, @Param("newCorpId") String newCorpId);
}
