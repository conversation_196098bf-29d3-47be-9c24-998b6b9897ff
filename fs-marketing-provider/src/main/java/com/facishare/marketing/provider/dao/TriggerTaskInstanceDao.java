package com.facishare.marketing.provider.dao;

import com.facishare.marketing.api.result.TriggerTaskStatisticsResult;
import com.facishare.marketing.api.result.mail.ListMailMarketingResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.bo.TriggerTaskInstanceStatisticBO;
import com.facishare.marketing.provider.dto.qywx.SopQywxTaskResultDTO;
import com.facishare.marketing.provider.entity.*;

import java.util.List;
import java.util.Map;

import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;

/**
 * <AUTHOR>
 * Created on 2021-03-05.
 */

public interface TriggerTaskInstanceDao {
	@Insert("<script>" +
		"INSERT INTO trigger_task_instance(id, ea, trigger_instance_id, trigger_id, trigger_snapshot_id, trigger_task_snapshot_id, " +
		"execute_type, execute_time, execute_status, execute_result, create_time, update_time, param_map,type,group_sender,owner,owner_user_id,to_user,to_size,task_end,batch_id) " +
		"VALUES <foreach collection='triggerTaskInstances' item='e' separator=','>" +
		"(#{e.id}, #{e.ea}, #{e.triggerInstanceId}, #{e.triggerId}, #{e.triggerSnapshotId}, #{e.triggerTaskSnapshotId}," +
		"#{e.executeType}, #{e.executeTime}, #{e.executeStatus}, #{e.executeResult}, NOW(), NOW(), #{e.paramMap}, #{e.type}, #{e.groupSender}, #{e.owner},#{e.ownerUserId}, #{e.toUser},#{e.toSize}, #{e.taskEnd},#{e.batchId})" +
		"</foreach>" +
		"</script>")
	int batchInsert(@Param("triggerTaskInstances") Collection<TriggerTaskInstanceEntity> triggerTaskInstance);

	@Update("UPDATE trigger_task_instance SET execute_status=#{executeStatus}, execute_result=#{executeResult}, update_time=NOW() WHERE ea=#{ea} AND id=#{id}")
	int updateExecuteResult(@Param("ea") String ea, @Param("id") String id, @Param("executeStatus") String executeStatus, @Param("executeResult") String executeResult);

	// 仅用于sop企微客户消息发送支持指定员工发送关联使用
	@Insert("insert into sop_qywx_msg_task(id, ea, msg_id) VALUES(#{id}, #{ea}, #{msgId})")
	int insertSopQywxMsgTask(@Param("ea") String ea, @Param("id") String id, @Param("msgId") String msgId);

	@Update("UPDATE trigger_task_instance SET execute_time=#{executeTime}, update_time=NOW() WHERE ea=#{ea} AND id=#{id}")
	int updateExecuteTime(@Param("ea") String ea, @Param("id") String id, @Param("executeTime") Long executeTime);

	@Update("UPDATE trigger_task_instance SET execute_status=#{executeStatus}, update_time=NOW() WHERE ea=#{ea} AND id=#{id} AND execute_status = #{currentStatus}")
	int updateExecuteStatus(@Param("ea") String ea, @Param("id") String id, @Param("executeStatus") String executeStatus, @Param("currentStatus") String currentStatus);

	@Select("SELECT * FROM trigger_task_instance WHERE id=#{id}")
	TriggerTaskInstanceEntity getById(@Param("id") String id);

	@Select("SELECT execute_status FROM trigger_task_instance WHERE ea =#{ea} and owner =#{owner} and trigger_id=#{triggerId} and trigger_snapshot_id=#{triggerSnapshotId} and trigger_task_snapshot_id=#{triggerTaskSnapshotId} and execute_status !='todo' and batch_id=#{batchId}")
	List<String> hasNoticeOwner(@Param("ea") String ea,@Param("triggerId") String triggerId,@Param("triggerSnapshotId") String triggerSnapshotId,@Param("triggerTaskSnapshotId") String triggerTaskSnapshotId,
					   @Param("batchId") Integer batchId,@Param("owner") Integer owner);

	@Select("SELECT trigger_instance_id FROM trigger_task_instance WHERE ea = #{ea} AND trigger_id = #{triggerId} AND trigger_task_snapshot_id = #{triggerTaskSnapshotId}")
	List<String> getTriggerInstanceIdByTriggerIdAndTaskSnapshotId(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId);

	@Select("<script>" +
		"SELECT DISTINCT trigger_instance_id FROM trigger_task_instance WHERE ea = #{ea} AND trigger_id = #{triggerId} AND trigger_task_snapshot_id IN " +
		"<foreach collection='triggerTaskSnapshotIds' item='triggerTaskSnapshotId' open='(' close=')' separator=','>#{triggerTaskSnapshotId}</foreach>" +
		"</script>")
	List<String> getTriggerInstanceIdByTriggerIdAndTaskSnapshotIds(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerTaskSnapshotIds") Collection<String> triggerTaskSnapshotIds);

	@Select("<script>" +
		"SELECT trigger_task_snapshot_id, execute_status, execute_result FROM trigger_task_instance WHERE ea = #{ea} AND trigger_task_snapshot_id IN " +
		"<foreach collection='triggerTaskSnapshotIds' item='triggerTaskSnapshotId' separator=',' open='(' close=')'>#{triggerTaskSnapshotId}</foreach>" +
		"</script>")
	@MapKey("trigger_task_snapshot_id")
	Map<String, Map<String, String>> mapByTaskSnapshotId(@Param("ea") String ea, @Param("triggerTaskSnapshotIds") Collection<String> triggerTaskSnapshotIds);

	@Select("SELECT * FROM trigger_task_instance WHERE ea = #{ea} AND trigger_instance_id = #{triggerInstanceId}")
	List<TriggerTaskInstanceEntity> listByTriggerInstanceId(@Param("ea") String ea, @Param("triggerInstanceId") String triggerInstanceId);

	@Select("SELECT \n" +
			"a.\"id\" AS trigger_task_instance_id,a.trigger_snapshot_id,a.trigger_task_snapshot_id,a.trigger_instance_id,a.execute_status,a.execute_result,b.task_type, c.marketing_user_id  \n" +
			"FROM \n" +
			"trigger_task_instance a \n" +
			"left join trigger_task_snapshot b on a.trigger_task_snapshot_id = b.id\n" +
			"left join trigger_instance c on a.trigger_instance_id = c.id " +
			"WHERE " +
			"a.ea = #{ea} AND a.trigger_id = #{triggerId} limit 10000")
	List<TriggerTaskInstanceAndSnapshotEntity> listTaskInstanceAndSnapshot(@Param("ea") String ea, @Param("triggerId") String triggerId);

	@Select("<script>"
			+  "select task_type, count(*) as count, count(distinct c.marketing_user_id) as userCount \n"
			+ "from trigger_task_instance a left join trigger_task_snapshot b on a.trigger_task_snapshot_id = b.id\n"
			+ "left join trigger_instance c on a.trigger_instance_id = c.id   WHERE a.ea = #{ea} AND a.trigger_id = #{triggerId} and a.execute_status = 'success'\n"
			+ " and b.task_type in <foreach collection='taskTypeList' item='taskType' separator=',' open='(' close=')'>#{taskType}</foreach>"
			+ "group by b.task_type ;"
			+ "</script>")
	List<TriggerTaskInstanceStatisticBO> statisticByTaskType(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("taskTypeList") List<String> taskTypeList);

	@Select("<script>" +
			"SELECT distinct(owner_user_id) " +
			"FROM trigger_task_instance  where ea =#{ea} and owner_user_id is not null and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"and batch_id =#{batchId}"+
			"</if>\n" +
			"</script>")
	List<String> pageRankingSingleSopTaskEmployeeId(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerSnapshotId ,@Param("triggerSnapshotId") String triggerTaskSnapshotId,@Param("batchId") Integer batchId, @Param("page")Page page);



	@Select("<script>" +
			"SELECT owner_user_id , to_user,id " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"and batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	List<TriggerTaskInstanceEntity> queryAllSingleSopTaskEmployee(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerSnapshotId ,@Param("triggerSnapshotId") String triggerTaskSnapshotId,@Param("batchId") Integer batchId);




	@Select("select id from trigger_task_snapshot where  trigger_id =#{triggerId} and ea =#{ea} and trigger_snapshot_id = #{triggerSnapshotId} and (task_type ='qywx_sop_task' or task_type ='qywx_msg' ) order by serial_number asc")
	List<String> getSopTask(@Param("ea") String ea, @Param("triggerId") String triggerId,@Param("triggerSnapshotId") String triggerSnapshotId);



	@Select("<script>" +
			"select sum(COALESCE(to_size,0)) " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"  and batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	Integer countGroupCustomerTotal(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerSnapshotId ,@Param("triggerSnapshotId") String triggerTaskSnapshotId,@Param("batchId") Integer batchId);


	@Select("<script>"+
//			"select  ti.marketing_user_id as marketingUserId ,qgsr.external_userid as externalUserId,qgsr.status as status,qgsr.userid as userId from trigger_instance ti left join trigger_task_instance tti on ti.id = tti.trigger_instance_id " +
//			"left join qywx_group_send_result qgsr on tti.execute_result = qgsr.msgid "+
//			"where ti.trigger_id=#{triggerId} " +
//			"and ti.trigger_snapshot_id = #{triggerSnapshotId} and ti.ea= #{ea} " +
//			"<if test =\"start != null\">\n" +
//			"  and tti.execute_time between #{start} and #{end} "+
//			"</if>\n" +
//			"order by ti.marketing_user_id " +
//			"UNION ALL" +
			"select  ti.marketing_user_id as marketingUserId ,qgsr.external_userid as externalUserId,qgsr.status as status,qgsr.userid as userId from trigger_instance ti left join trigger_task_instance tti on ti.id = tti.trigger_instance_id " +
			"left join sop_qywx_msg_task task on tti.execute_result = task.id "+
			"left join qywx_group_send_result qgsr on task.msg_id = qgsr.msgid "+
			"where ti.trigger_id=#{triggerId} " +
			"and ti.trigger_snapshot_id = #{triggerSnapshotId} and ti.ea= #{ea} and task.msg_id is not null" +
			"<if test =\"start != null\">\n" +
			"  and tti.execute_time between #{start} and #{end} "+
			"</if>\n" +
			"order by ti.marketing_user_id " +
			"</script>")
	List<SopQywxTaskResultDTO> listQywxMsgCustomer(@Param("ea") String ea, @Param("triggerId") String triggerId , @Param("triggerSnapshotId") String triggerSnapshotId , @Param("start") Long start, @Param("end") Long end, @Param("page")Page page);




	@Select("<script>" +
			"SELECT id as triggerTaskInstanceId," +
			"trigger_id," +
			"trigger_snapshot_id," +
			"trigger_task_snapshot_id," +
			"execute_time," +
			"type," +
			"group_sender, " +
			"task_end, " +
			"to_size " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and trigger_snapshot_id = #{triggerSnapshotId} and group_sender is not null "+
			"<if test =\"batchId != null\">\n" +
			"and batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	List<TriggerSopTaskEntity> pageRankingGroupSopTaskEmployee(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerSnapshotId ,@Param("triggerSnapshotId") String triggerTaskSnapshotId,@Param("batchId") Integer batchId, @Param("page")Page page);


	@Select("<script>" +
			"SELECT  " +
			"group_sender, " +
			"to_size " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and trigger_snapshot_id = #{triggerSnapshotId}  "+
			"<if test =\"batchId != null\">\n" +
			"and batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	List<TriggerSopTaskEntity> listGroupSopTaskEmployee(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerSnapshotId ,@Param("triggerSnapshotId") String triggerTaskSnapshotId,@Param("batchId") Integer batchId);



	@Select("<script>" +
			"select count(1) " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"  and batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	int countGroupTotal(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerSnapshotId ,@Param("triggerSnapshotId") String triggerTaskSnapshotId,@Param("batchId") Integer batchId);


	@Select("<script>" +
			"select distinct (owner_user_id ) as ownerUserId , group_sender as groupSender  " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"  and batch_id = #{batchId}  "+
			"</if>\n" +
			"</script>")
	List<TriggerSopTaskEntity> queryAllUser(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerTaskSnapshotId ,@Param("triggerSnapshotId") String triggerSnapshotId,@Param("batchId") Integer batchId);

	@Select("<script>" +
			"select id, type,to_size,owner_user_id,group_sender " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"  and batch_id = #{batchId}  "+
			"</if>\n" +
			"</script>")
	List<TriggerTaskInstanceEntity> queryAllTask(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerTaskSnapshotId ,@Param("triggerSnapshotId") String triggerSnapshotId,@Param("batchId") Integer batchId);



	@Select("<script>" +
			"select id as triggerTaskInstanceId  " +
			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} "+
			"<if test =\"batchId != null\">\n" +
			"  and batch_id = #{batchId} "+
			"</if>\n" +
			"limit 1 " +
			"</script>")
	String queryIdByTime(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerTaskSnapshotId ,@Param("triggerSnapshotId") String triggerSnapshotId,@Param("batchId") Integer batchId);



	@Select("<script>" +
			"select tti.*,ti.marketing_user_id as marketingUserId from trigger_instance ti left join trigger_task_instance tti on ti.id = tti.trigger_instance_id " +
			"where  tti.ea =#{ea} and tti.trigger_id =#{triggerId} and tti.trigger_snapshot_id =#{triggerSnapshotId} " +
			"and tti.trigger_task_snapshot_id =#{triggerTaskSnapshotId} and tti.owner_user_id =#{ownerUserId} " +
			"<if test =\"batchId != null\">\n" +
			"  and tti.batch_id = #{batchId} "+
			"</if>\n" +
			"order by ti.create_time "+
			"</script>")
	List<TriggerTaskInstanceAndMarketingUserIdEntity> listSopCustomerByExecuteTime(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerSnapshotId") String triggerSnapshotId,
																				   @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId, @Param("ownerUserId") String ownerUserId,
																				   @Param("batchId") Integer batchId, @Param("page")Page page);


	@Select("<script>" +
			"select count(distinct (ti.marketing_user_id)) from trigger_instance ti left join trigger_task_instance tti on ti.id = tti.trigger_instance_id " +
			"where  tti.ea =#{ea} and tti.trigger_id =#{triggerId} and tti.trigger_snapshot_id =#{triggerSnapshotId} " +
			"and tti.trigger_task_snapshot_id =#{triggerTaskSnapshotId} and tti.owner_user_id =#{ownerUserId} " +
			"<if test =\"batchId != null\">\n" +
			"  and tti.batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	Integer countSopCustomer(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerSnapshotId") String triggerSnapshotId,
																				   @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId, @Param("ownerUserId") String ownerUserId,
																				   @Param("batchId") Integer batchId);


	@Select("<script>" +
			"select tti.id,tti.group_sender,tti.owner_user_id,tti.owner,ti.marketing_user_id as marketingUserId from trigger_instance ti left join trigger_task_instance tti on ti.id = tti.trigger_instance_id " +
			"where  tti.ea =#{ea} and tti.trigger_id =#{triggerId} and tti.trigger_snapshot_id =#{triggerSnapshotId} and owner_user_id is not null " +
			"and tti.trigger_task_snapshot_id =#{triggerTaskSnapshotId}  " +
			"<if test =\"batchId != null\">\n" +
			"  and tti.batch_id = #{batchId} "+
			"</if>\n" +
			"order by ti.create_time "+
			"</script>")
	List<TriggerTaskInstanceAndMarketingUserIdEntity> listSopAllCustomer(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerSnapshotId") String triggerSnapshotId,
																				   @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId,
																				   @Param("batchId") Integer batchId);



	@Select("<script>" +
//			"select distinct execute_result " +
//			"FROM trigger_task_instance  where ea =#{ea} and trigger_id =#{triggerId} and trigger_task_snapshot_id =#{triggerTaskSnapshotId} and  trigger_snapshot_id = #{triggerSnapshotId} and execute_status ='success'  "+
//			"<if test =\"batchId != null\">\n" +
//			"  and batch_id = #{batchId} "+
//			"</if>\n" +
//			"UNION ALL\n" +
			"select distinct task.msg_id \n" +
			"FROM sop_qywx_msg_task task \n" +
			"left join trigger_task_instance tti on task.id = tti.execute_result\n" +
			"where tti.ea =#{ea} and tti.trigger_id =#{triggerId} and tti.trigger_task_snapshot_id =#{triggerTaskSnapshotId} and tti.trigger_snapshot_id = #{triggerSnapshotId} and tti.execute_status ='success' and task.msg_id is not null \n"+
			"<if test =\"batchId != null\">\n" +
			"  and tti.batch_id = #{batchId} "+
			"</if>\n" +
			"</script>")
	List<String> queryAllQywxMsgTask(@Param("ea") String ea, @Param("triggerId") String triggerId ,@Param("triggerTaskSnapshotId") String triggerTaskSnapshotId ,@Param("triggerSnapshotId") String triggerSnapshotId,@Param("batchId") Integer batchId);


	@Update("<script>" +
			"UPDATE trigger_task_instance SET execute_status='canceled', update_time=NOW() WHERE ea=#{ea} AND trigger_instance_id IN "
			+   "<foreach collection = 'triggerInstanceIds' item = 'triggerInstanceId' open = '(' separator = ',' close = ')'>"
			+       "#{triggerInstanceId}"
			+   "</foreach>"
			+ " AND execute_status='todo' AND execute_type='delayed'" +
			"</script>")
	void cancelTodoTriggerTaskInstanceByTriggerInstanceId(@Param("ea") String ea, @Param("triggerInstanceIds") List<String> triggerInstanceIds);

	@Select("select * from trigger_task_instance where ea = #{ea} and owner_user_id = #{ownerUserId}")
	List<TriggerTaskInstanceEntity> getByOwnerUserId(@Param("ea") String ea, @Param("ownerUserId") String ownerUserId);

	@Update("update trigger_task_instance SET owner_user_id = #{ownerUserId} , update_time = now() where ea = #{ea} and id = #{id}")
	int updateOwnerUserId(@Param("ea") String ea, @Param("id") String id, @Param("ownerUserId") String ownerUserId);


	@Select("<script>" +
			"select a.id, a.email ->> 'title' subject \n" +
			"from trigger_task_snapshot a \n" +
			"left join trigger_snapshot b on a.trigger_snapshot_id = b.id and a.ea = b.ea and a.trigger_id = b.trigger_id \n" +
			"left join scene_trigger c on b.trigger_id = c.trigger_id and b.ea = c.ea \n" +
			"where a.ea = #{ea} and a.task_type = 'send_email_msg' and b.snapshot_status = 'enabled' \n" +
			"and c.scene_target_id = #{marketingEventId}\n" +
			"<if test =\"keyword != null\">" +
			"and a.email ->> 'title' like CONCAT('%',#{keyword},'%')"+
			"</if>\n" +
			"order by a.create_time desc" +
			"</script>")
	List<ListMailMarketingResult> listSopMailNotice(@Param("ea") String ea, @Param("marketingEventId") String marketingEventId, @Param("keyword") String keyword, Page page);

	@Update("update trigger_task_instance SET execute_status = 'canceled', update_time = now() where ea = #{ea} and id = #{id}")
    int cancelDelayTask(@Param("ea") String ea, @Param("id") String id);

	@Update("<script>" +
			"UPDATE trigger_task_instance SET " +
			"execute_status=#{executeStatus}, " +
			"execute_result=#{executeResult} " +
			"WHERE ea=#{ea} and trigger_id = #{triggerId} and trigger_snapshot_id=#{triggerSnapshotId} " +
			"and trigger_task_snapshot_id = #{triggerTaskSnapshot_id} "
			+ "</script>")
	int updateTriggerInstanceStatusByTriggerId(@Param("ea") String ea, @Param("triggerId") String triggerId, @Param("triggerSnapshotId") String triggerSnapshotId,
												@Param("triggerTaskSnapshot_id") String triggerTaskSnapshot_id,
												@Param("executeStatus") String executeStatus, @Param("executeResult") String executeResult);

	@Select("select count(tt.id) from trigger_task_instance tt \n" +
			"left join trigger_instance t on tt.ea = t.ea and tt.trigger_instance_id = t.id\n" +
			"where \n" +
			"tt.execute_status = 'success'\n" +
			"and tt.ea = #{ea}\n" +
			"and tt.trigger_id = #{triggerId}\n" +
			"and tt.trigger_snapshot_id  = #{triggerSnapshotId}\n" +
			"and tt.trigger_task_snapshot_id = #{triggerTaskSnapshotId}\n" +
			"and t.marketing_user_id = #{marketingUserId}\n" +
			"and t.target_object_id = #{targetObjectId}")
    int listByTriggerInfoAndObjectId(@Param("ea") String ea, @Param("triggerId") String triggerId,
									 @Param("triggerSnapshotId") String triggerSnapshotId, @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId,
									 @Param("marketingUserId") String marketingUserId, @Param("targetObjectId") String targetObjectId);

	@FilterLog
	@Select("select\n" +
			"\ttask.*\n" +
			"from\n" +
			"\ttrigger_task_instance task\n" +
			"left join trigger_instance trig on task.ea = trig.ea and task.trigger_id = trig.trigger_id and task.trigger_instance_id = trig.id\n" +
			"where\n" +
			"\ttask.ea = #{ea}\n" +
			"\tand task.execute_status = 'listening'\n" +
			"\tand trig.marketing_user_id = #{marketingUserId}")
    List<TriggerTaskInstanceEntity> listTaskInstanceByUserMarketingId(@Param("ea") String ea, @Param("marketingUserId") String marketingUserId);

	@Select("select\n" +
			"\ttrigger_task_snapshot_id as id, count(*) as total\n" +
			"from\n" +
			"\ttrigger_task_instance\n" +
			"where \n" +
			"\tea = #{ea}\n" +
			"\tand trigger_snapshot_id = #{triggerSnapshotId}\n" +
			"group by \n" +
			"\ttrigger_task_snapshot_id	")
	List<TriggerTaskStatisticsResult.TriggerTaskStatistics> countInstanceByTriggerSnapshotId(@Param("ea") String ea, @Param("triggerSnapshotId") String triggerSnapshotId);

	@Update("<script>" +
			"update\n" +
			"\ttrigger_task_instance\n" +
			"set\n" +
			"\tid = #{triggerTaskInstance.id}\n" +
			"<if test =\"triggerTaskInstance.groupSender != null\">" +
			"\t,group_sender = #{triggerTaskInstance.groupSender}\n" +
			"</if>\n" +
			"<if test =\"triggerTaskInstance.owner != null\">" +
			"\t,\"owner\" = #{triggerTaskInstance.owner}\n" +
			"</if>\n" +
			"<if test =\"triggerTaskInstance.ownerUserId != null\">" +
			"\t,owner_user_id = #{triggerTaskInstance.ownerUserId}\n" +
			"</if>\n" +
			"<if test =\"triggerTaskInstance.toUser != null\">" +
			"\t,to_user = #{triggerTaskInstance.toUser}\n" +
			"</if>\n" +
			"<if test =\"triggerTaskInstance.toSize != null\">" +
			"\t,to_size = #{triggerTaskInstance.toSize}\n" +
			"</if>\n" +
			"where\n" +
			"\tea = #{triggerTaskInstance.ea}\n" +
			"\tand id = #{triggerTaskInstance.id}" +
			"</script>")
	int updateSubsidiaryData(@Param("triggerTaskInstance") TriggerTaskInstanceEntity triggerTaskInstance);

	@Select("<script>" +
			"SELECT * FROM trigger_task_instance WHERE ea = #{ea} " +
			"and trigger_id = #{triggerId} " +
			"and trigger_snapshot_id = #{triggerSnapshotId} " +
			"and trigger_task_snapshot_id = #{triggerTaskSnapshotId} " +
			"and execute_status in ('success', 'failed') " +
			"limit 1" +
			"</script>")
	TriggerTaskInstanceEntity getSuccessOrFailInstanceByTriggerIdAndSnapshotId(@Param("ea") String ea,
													  @Param("triggerId") String triggerId,
													  @Param("triggerSnapshotId") String triggerSnapshotId,
													  @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId);

	@Select("<script>" +
			"SELECT tti.* FROM trigger_task_instance tti " +
			"left join trigger_instance ti on tti.trigger_instance_id = ti.id " +
			"WHERE tti.ea = #{ea} " +
			"and tti.trigger_id = #{triggerId} " +
			"and tti.trigger_snapshot_id = #{triggerSnapshotId} " +
			"and tti.trigger_task_snapshot_id = #{triggerTaskSnapshotId} " +
			"and tti.execute_status in ('success', 'failed') " +
			"and ti.trigger_record_id = #{triggerRecordId} " +
			"limit 1" +
			"</script>")
	TriggerTaskInstanceEntity getSuccessOrFailInstanceByTriggerIdAndSnapshotIdAndRecord(@Param("ea") String ea,
																						@Param("triggerId") String triggerId,
																						@Param("triggerSnapshotId") String triggerSnapshotId,
																						@Param("triggerTaskSnapshotId") String triggerTaskSnapshotId,
																						@Param("triggerRecordId") String triggerRecordId);

	@Select("<script>" +
			"SELECT * FROM trigger_task_instance WHERE ea = #{ea} " +
			"and trigger_id = #{triggerId} " +
			"and trigger_snapshot_id = #{triggerSnapshotId} " +
			"and trigger_task_snapshot_id = #{triggerTaskSnapshotId} " +
			"and execute_status in ('success', 'failed') " +
			"and batch_id = #{batchId} " +
			"limit 1" +
			"</script>")
	TriggerTaskInstanceEntity getSuccessOrFailInstanceByTriggerIdAndSnapshotIdAndBatch(@Param("ea") String ea,
																					   @Param("triggerId") String triggerId,
																					   @Param("triggerSnapshotId") String triggerSnapshotId,
																					   @Param("triggerTaskSnapshotId") String triggerTaskSnapshotId,
																					   @Param("batchId") Integer batchId);
}
