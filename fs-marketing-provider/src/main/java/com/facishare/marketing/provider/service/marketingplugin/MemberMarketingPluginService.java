package com.facishare.marketing.provider.service.marketingplugin;

import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.common.enums.VersionEnum;
import com.facishare.marketing.common.enums.appMenu.AppMenuTemplateTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.manager.MemberManager;
import com.facishare.marketing.provider.manager.MemberMarketingManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;


@Slf4j
@Service(value = "memberMarketingPluginService")
public class MemberMarketingPluginService extends MarketingPluginBaseService {

    @Autowired
    private MemberManager memberManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Override
    public Result update(String ea, Integer pluginType, Boolean status) {
        if (BooleanUtils.isTrue(status)) {
            // 是否开启会员模块
//            MemberStatusResult memberStatusResult = memberManager.isOpenMember(ea, SuperUserConstants.USER_ID);
//            if (memberStatusResult == null || memberStatusResult.getEnableStatus() == null || memberStatusResult.getEnableStatus() != 1) {
//                return Result.newError(SHErrorCode.CAN_NOT_ENABLE_MEMBER_MARKETING);
//            }
            // 是否有专属小程序
            //10.5新分版插件 会员营销去掉专属小程序判断
//            if (!wechatAccountManager.isBindSpecialMiniApp(ea)) {
//                return Result.newError(SHErrorCode.CAN_NOT_ENABLE_MEMBER_MARKETING);
//            }
            //判断是否购买了会员营销插件
            String currentVersion = appVersionManager.getCurrentAppVersion(ea);
            Set<String> standardVersionSet = Sets.newHashSet(VersionEnum.STAN.getVersion(), VersionEnum.STAN_DINGTALK_30_APP.getVersion(), VersionEnum.STAN_DINGTALK_100_APP.getVersion(), VersionEnum.STAN_DINGTALK_500_APP.getVersion());
            // 如果是标准版、并且没有购买海外插件
            if (standardVersionSet.contains(currentVersion) && !appVersionManager.checkSpecialVersionOrders(ea, VersionEnum.MARKETING_MEMBER_PLUGIN_APP)) {
                return Result.newError(SHErrorCode.CAN_NOT_ENABLE_MEMBER_MARKETING);
            }
            memberMarketingManager.addFieldToMemberObj(ea);
            marketingActivityRemoteManager.addMemberMarketingSpreadTypeOptions(ea);
            //初始化会员推广工作台
            appMenuTemplateService.createSystemTemplate(ea, AppMenuTemplateTypeEnum.SYSTEM_MEMBER.getType());
        }
        return super.update(ea, pluginType, status);
    }
}
