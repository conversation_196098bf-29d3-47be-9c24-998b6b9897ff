package com.facishare.marketing.provider.manager.baidu;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.baidu.SyncUtmMarketingEventStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.leads.LeadMarketingSourceNameEnum;
import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.provider.advertiser.adAccount.TencentAdResult;
import com.facishare.marketing.provider.advertiser.tencent.TencentAdGroupResult;
import com.facishare.marketing.provider.baidu.BaiduIdTypeEnum;
import com.facishare.marketing.provider.baidu.RequestResult;
import com.facishare.marketing.provider.baidu.adgroup.AdGroupManager;
import com.facishare.marketing.provider.baidu.adgroup.GetAdGroupResultData;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.baidu.AdGroupDAO;
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO;
import com.facishare.marketing.provider.dao.baidu.AdObjectFieldMappingDAO;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.baidu.*;
import com.facishare.marketing.provider.innerArg.MarketingAdShenCeArg;
import com.facishare.marketing.provider.innerResult.UtmMarketingEventResult;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager;
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.landing.LandingObjCustomizeUserRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.service.MarketingEventStatisticsServiceImpl;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.contstant.tencent.TencentAdvertiserConstants.TENCENT_ADGROUP_ID_FIELD_NAME;

/**
 * Created by zhengh on 2021/3/4.
 */
@Component
@Slf4j
public class UtmDataManger {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private AdKeywordDAO adKeywordDAO;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private LandingObjCustomizeUserRelationManager landingObjCustomizeUserRelationManager;

    @Autowired
    private AdGroupDAO adGroupDAO;
    @Autowired
    private AdGroupManager adGroupManager;
    @Autowired
    private AdTokenManager adTokenManager;
    @Autowired
    private CampaignApiManager campaignApiManager;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;
    @Autowired
    private TencentAdMarketingManager tencentAdMarketingManager;
    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;
    /**
     * 处理utm线索提交后动作
     * @param ea
     * @param userId
     * @param leadId
     * @param arg
     */
    public void syncUtmFormDataToROI(String ea, Integer userId, String leadId, CustomizeFormDataEnroll arg){
        if (StringUtils.isEmpty(leadId) || !arg.isNewSave()){
            return;
        }
        if  (StringUtils.isEmpty(arg.getUtmCampaig()) && StringUtils.isEmpty(arg.getUtmTerm())
                && StringUtils.isEmpty(arg.getKeywordId()) && StringUtils.isEmpty(arg.getUnitId())) {
            return;
        }
        //企业要购买了营销一体化才有该逻辑
        if (!adCommonManager.isPurchaseAdLicense(ea)){
            return;
        }

        String campaignName = arg.getUtmCampaig();
        String keyword = arg.getUtmTerm();
        String source = arg.getMarketingSourceName();
        String formatSource = "other";
        if (LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_BAIDU.getName().equals(LeadMarketingSourceNameEnum.getNameByType(source))){
            formatSource = AdSourceEnum.SOURCE_BAIDU.getSource();
        }else if (LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_TOUTIAO.getName().equals(LeadMarketingSourceNameEnum.getNameByType(source))){
            formatSource = AdSourceEnum.SOURCE_JULIANG.getSource();
        }else if (LeadMarketingSourceNameEnum.MARKETING_SOURCE_NAME_TX_NEWS.getName().equals(LeadMarketingSourceNameEnum.getNameByType(source))){
            formatSource = AdSourceEnum.SOURCE_TENCETN.getSource();
        }
        String marketingEventId = null;
        //同步市场活动
        AdObjectFieldMappingEntity mappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        boolean syncUtmCampaign = isSyncUtmCampaign(ea);
//        Optional<String> marketingEventIdOpt = utmCreateMarketingEvent(ea, campaignName, formatSource, mappingEntity, syncUtmCampaign);
        Long unitId = StringUtils.isBlank(arg.getUnitId()) ? null : Long.parseLong(arg.getUnitId());
        Long accountId = StringUtils.isBlank(arg.getAccountId()) ? null : Long.parseLong(arg.getAccountId());
        Optional<UtmMarketingEventResult> marketingEventIdOpt = getOrCreateMarketingEventObj(ea, accountId, unitId, campaignName, formatSource, mappingEntity, syncUtmCampaign);
        String adName = null;
        if (marketingEventIdOpt.isPresent()){
            marketingEventId = marketingEventIdOpt.get().getMarketingEventId();
            adName = marketingEventIdOpt.get().getAdName();
        }
        // 如果参数有传关键词的id过来，优先使用关键词id参数，如果没有，再用utmTerm参数
        Long keywordId = StringUtils.isBlank(arg.getKeywordId()) ? null : Long.parseLong(arg.getKeywordId());
        ObjectData marketingKeywordObject = getOrCreateKeywordObj(ea, keywordId, arg.getUtmTerm(), marketingEventId, accountId);
        String marketingKeywordId = null;
        if (marketingKeywordObject != null) {
            marketingKeywordId = marketingKeywordObject.getId();
            keyword = marketingKeywordObject.getName() == null ? keyword : marketingKeywordObject.getName();
        }
        log.info("syncUtmFormDataToROI ea:{} userId:{} leadId:{} syncUtmCampaign:{} customizeFormDataEnroll:{}, marketingEventId: {} marketingKeywordObject: {}", ea, userId, leadId, syncUtmCampaign, arg, marketingEventId, marketingKeywordObject);
        if (marketingKeywordObject == null && StringUtils.isBlank(marketingEventId)) {
            return;
        }
        //同步关键词计划
        boolean isRelateAdGroupDimensionality = adCommonManager.isRelateAdGroupDimensionality(ea, marketingEventId);
        String marketingKeywordPlanId = null;
        Optional<String> marketingKeywordPlanOpt = utmCreateMarketingKeywordPlan(ea, marketingEventId, campaignName, marketingKeywordId, keyword, syncUtmCampaign, isRelateAdGroupDimensionality, keywordId);
        if (marketingKeywordPlanOpt.isPresent()){
            marketingKeywordPlanId = marketingKeywordPlanOpt.get();
        }
        log.info("syncUtmFormDataToROI utm create marketingEventKeywordPlan ea:{} leadId:{} customizeFormDataEnroll:{}, marketingEventId: {} keyword: {} marketingKeywordPlanId: {}", ea, leadId, arg, marketingEventId, keyword, marketingKeywordPlanId);
        //线索更新
        Boolean allowDuplicate = arg.isNewSave() ? false : null;
        updateLeadROIData(ea, userId, leadId, marketingKeywordId, marketingKeywordPlanId, marketingEventId, allowDuplicate, adName, keyword);
        sendDataToShenCe(ea, leadId, marketingEventId, keyword, source);
        // 更新线索上的营销推广来源对象
        marketingPromotionSourceObjManager.updateMarketingPromotionSource(ea, leadId, marketingEventId, marketingKeywordId);
        // 更新落地页和线索关联关系表的市场活动
        updateMarketingEventIdToLandingObjCustomizeUserRelation(ea, leadId, marketingEventId);
    }

    private void updateMarketingEventIdToLandingObjCustomizeUserRelation(String ea, String leadId, String marketingEventId) {
        if (StringUtils.isBlank(marketingEventId)) {
            return;
        }
        landingObjCustomizeUserRelationManager.updateMarketingEventId(ea, leadId, marketingEventId);
    }

    public ObjectData getOrCreateKeywordObj(String ea, Long keywordId, String utmTerm, String marketingEventId, Long accountId) {
        if (keywordId == null && StringUtils.isBlank(utmTerm)) {
            return null;
        }
        String marketingKeywordId = null;
        if (keywordId != null) {
            //先看这个关键词是否已经同步到营销通
            AdKeywordEntity adKeywordEntity = adKeywordDAO.queryByKeywordId(ea, keywordId);
            if (adKeywordEntity != null) {
                //如果同步过来了，是否同步成关键词对象，否则同步成关键词对象
                if (StringUtils.isNotBlank(adKeywordEntity.getMarketingKeywordId())) {
                    marketingKeywordId = adKeywordEntity.getMarketingKeywordId();
                }
                // 没同步成关键词对象的，在下面同步成关键词对象
                utmTerm = adKeywordEntity.getKeyword();
            } else if (accountId != null || StringUtils.isNotBlank(marketingEventId)) {
                // 如果该关键词id没同步到营销通,并且有市场活动id，根据市场活动id查询对应的广告账号，并从百度查询对应的关键词信息并同步成关键词对象
                AdAccountEntity adAccountEntity = null;
                if (accountId != null) {
                    adAccountEntity = adAccountManager.queryAccountByAccountId(ea, accountId);
                } else {
                    String adAccountId = adCommonManager.getAdAccountIdByMarketingEventId(ea, marketingEventId);
                    adAccountEntity = adAccountId == null ? null : adAccountManager.queryAccountById(adAccountId);
                }
                if (adAccountEntity != null) {
                    AdKeywordEntity newAdKeywordEntity = null;
                    if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(adAccountEntity.getSource())) {
                        List<AdKeywordEntity> adKeywordEntityList = baiduAdMarketingManager.syncKeywordByKeywordIds(ea, adAccountEntity.getId(), Lists.newArrayList(keywordId));
                        newAdKeywordEntity = CollectionUtils.isEmpty(adKeywordEntityList) ? null : adKeywordEntityList.get(0);
                    } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(adAccountEntity.getSource())) {
                        newAdKeywordEntity = tencentAdMarketingManager.syncKeywordByKeywordId(adAccountEntity, keywordId);
                    }
                    if (newAdKeywordEntity != null) {
                        marketingKeywordId = newAdKeywordEntity.getMarketingKeywordId();
                        utmTerm = newAdKeywordEntity.getKeyword();
                    }
                }
            }
        }
        // 如果根据keywordId没查到关键词对象，再根据utmTerm查询并创建关键词对象
        if (marketingKeywordId == null && StringUtils.isNotBlank(utmTerm)) {
            Optional<String> marketingKeywordOpt = utmCreateMarketingKeyword(ea, utmTerm, true);
            if (marketingKeywordOpt.isPresent()){
                marketingKeywordId = marketingKeywordOpt.get();
            }
        }
        log.info("getOrCreateKeywordObj ea:{} keywordId:{} utmTerm:{}, marketingKeywordId : {}", ea, keywordId, utmTerm, marketingKeywordId);
        ObjectData objectData = new ObjectData();
        objectData.put("_id", marketingKeywordId);
        objectData.put("name", utmTerm);
        return objectData;
    }

    private void sendDataToShenCe(String ea, String leadId, String marketingEventId, String keyword, String adSource) {
        if (StringUtils.isBlank(marketingEventId)) {
            return;
        }
        MarketingAdShenCeArg marketingAdShenCeArg = new MarketingAdShenCeArg();
        marketingAdShenCeArg.setEa(ea);
        marketingAdShenCeArg.setMarketingEventId(marketingEventId);
        marketingAdShenCeArg.setLeadId(leadId);
        marketingAdShenCeArg.setSource(adSource);
        marketingAdShenCeArg.setKeyword(keyword);
        String adAccountId = adCommonManager.getAdAccountIdByMarketingEventId(ea, marketingEventId);
        if (StringUtils.isNotBlank(adAccountId)) {
            AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
            marketingAdShenCeArg.setAdAccountId(adAccountId);
            marketingAdShenCeArg.setAdAccountUserName(adAccountEntity == null ? null : adAccountEntity.getUsername());
        }
        DataPersistor.asyncLog(MarketingAdShenCeArg.LOG_NAME, marketingAdShenCeArg.transferToMap());
    }

    public String syncUtmCampaign(String ea, String utmCampaign, String source) {
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(utmCampaign)){
            return null;
        }
        boolean syncUtmCampaign = isSyncUtmCampaign(ea);
        //企业要购买了营销一体化才有该逻辑
        if (!adCommonManager.isPurchaseAdLicense(ea)){
            return null;
        }
        AdObjectFieldMappingEntity mappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        //同步市场活动
        Optional<String> marketingEventIdOpt = utmCreateMarketingEvent(ea, utmCampaign, source, mappingEntity, syncUtmCampaign);
        return marketingEventIdOpt.orElse(null);
    }

    public boolean isSyncUtmCampaign(String ea){
        AdObjectFieldMappingEntity mappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        return mappingEntity != null && mappingEntity.getEnable() == SyncUtmMarketingEventStatusEnum.ENABLE.getStatus();
    }

    public Optional<String> utmCreateMarketingEvent(String ea, String campaignName, String source, AdObjectFieldMappingEntity mappingEntity, boolean syncUtmCampaign){
        if (StringUtils.isBlank(campaignName)) {
            log.warn("UtmDataManger.utmCreateMarketingEvent campaignName is null ea:{} source:{}", ea, source);
            return Optional.empty();
        }
        String marketingEventId = null;
        //查询市场活动是否存在
        Page<ObjectData> objectDataPage = queryMarketingEventByName(ea, campaignName);
        if (objectDataPage == null){
            log.error("UtmDataManger.utmCreateMarketingEvent query marketingEvent return null ea:{} name:{}", ea, campaignName);
            return Optional.empty();
        }

        if (CollectionUtils.isNotEmpty(objectDataPage.getDataList())){
            marketingEventId = objectDataPage.getDataList().get(0).getId();
        }
        if (marketingEventId == null && syncUtmCampaign){
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> dataCreateResult = refreshDataManager.createMarketingEventObjByCampaign(ea,null, campaignName, source, mappingEntity, null, null, null);
            if (dataCreateResult.isSuccess()){
                marketingEventId = dataCreateResult.getData().getObjectData().getId();
            }
        }

        return Optional.ofNullable(marketingEventId);
    }

    public Optional<UtmMarketingEventResult> getOrCreateMarketingEventObj(String ea, Long accountId, Long unitId, String campaignName, String source, AdObjectFieldMappingEntity mappingEntity, boolean syncUtmCampaign){
        if (unitId == null && StringUtils.isBlank(campaignName)) {
            log.warn("UtmDataManger.utmCreateMarketingEvent campaignName is null ea:{} source:{}", ea, source);
            return Optional.empty();
        }
        if (accountId != null) {
            // 如果该广告账号没有绑定营销通，则不处理utm参数，如果处理，可能会导致线索关联的市场活动对不上，因为这种情况都是实施写函数处理的utm参数关联(客户:国仪量子)
            AdAccountEntity adAccountEntity = adAccountManager.queryAllStatusAccountByAccountId(ea, accountId);
            if (adAccountEntity == null) {
                log.info("广告账号没有绑定，ea: {} accountId: {}", ea, accountId);
                return Optional.empty();
            }
        }
        AdAccountEntity adAccountEntity = null;
        if(accountId != null) {
            adAccountEntity = adAccountManager.queryAccountByAccountId(ea, accountId);
            if(adAccountEntity != null) {
                source = adAccountEntity.getSource();
            }
        }
        UtmMarketingEventResult result = new UtmMarketingEventResult();
        //查询市场活动是否存在
        if (unitId != null) {
            if(source.equals(AdSourceEnum.SOURCE_TENCETN.getSource())) {
                TencentAdGroupEntity tencentAdGroupEntity = getTencentAdGroupEntity(ea, adAccountEntity, unitId);
                if(tencentAdGroupEntity != null && StringUtils.isNotBlank(tencentAdGroupEntity.getSubMarketingEventId())) {
                    result.setAdName(tencentAdGroupEntity.getAdgroupName());
                    result.setMarketingEventId(tencentAdGroupEntity.getSubMarketingEventId());
                    return Optional.of(result);
                }
                if(tencentAdGroupEntity == null && adAccountEntity != null) {
                    Optional<String> accessTokenOpt = adTokenManager.getTencentAccessToken(adAccountEntity);
                    if (accessTokenOpt.isPresent()) {
                        List<String> apiVersions = Lists.newArrayList("v1.3", "v3.0");  //腾讯搜索广告使用v1.3接口获取；展示广告使用v3.0接口
                        for (String v: apiVersions) {
                            TencentAdResult<TencentAdGroupResult> adGroupResultData = campaignApiManager.getTencentAdGroup(accountId, accessTokenOpt.get(), Lists.newArrayList(unitId), TENCENT_ADGROUP_ID_FIELD_NAME, v);
                            if (adGroupResultData != null && adGroupResultData.getData() != null && CollectionUtils.isNotEmpty(adGroupResultData.getData().getList())) {
                                tencentAdMarketingManager.saveTencentAdGroups(ea, adAccountEntity.getId(), adGroupResultData);
                                tencentAdGroupEntity = getTencentAdGroupEntity(ea, adAccountEntity, unitId);
                            }
                        }
                    }
                }
                if(tencentAdGroupEntity != null) {
                    adAccountEntity = adAccountManager.queryAccountById(tencentAdGroupEntity.getAdAccountId());
                }

                if(adAccountEntity != null && tencentAdGroupEntity != null) {
                    List<AdvertiserAdEntity> advertiserAdEntityList = TencentAdMarketingManager.transferAdvertiserAdEntityList(Lists.newArrayList(tencentAdGroupEntity));
                    refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, advertiserAdEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());
                    tencentAdGroupEntity = getTencentAdGroupEntity(ea, adAccountEntity, unitId);
                    if(tencentAdGroupEntity != null && StringUtils.isNotBlank(tencentAdGroupEntity.getSubMarketingEventId())) {
                        result.setAdName(tencentAdGroupEntity.getAdgroupName());
                        result.setMarketingEventId(tencentAdGroupEntity.getSubMarketingEventId());
                        return Optional.of(result);
                    }
                }
            } else if(source.equals(AdSourceEnum.SOURCE_BAIDU.getSource())) {
                AdGroupEntity adGroupEntity = getBaiduAdGroupEntity(ea, adAccountEntity, unitId);
                if(adGroupEntity != null && StringUtils.isNotBlank(adGroupEntity.getMarketingEventId())) {
                    result.setAdName(adGroupEntity.getAdGroupName());
                    result.setMarketingEventId(adGroupEntity.getMarketingEventId());
                    return Optional.of(result);
                }
                if(adGroupEntity == null && adAccountEntity != null) {
                    RequestResult<GetAdGroupResultData> adGroupResultData = adGroupManager.getAdGroupResultDataList(Lists.newArrayList(unitId), BaiduIdTypeEnum.AD_GROUP.getType(), adAccountEntity.getUsername(), adAccountEntity.getPassword(), adAccountEntity.getToken(), adTokenManager.getBaiduAccessToken(adAccountEntity));
                    if (adGroupResultData != null && CollectionUtils.isNotEmpty(adGroupResultData.getData())) {
                        baiduAdMarketingManager.syncAdGroupByCampaignData(ea, adAccountEntity.getId(), adGroupResultData.getData(), source);
                        adGroupEntity = getBaiduAdGroupEntity(ea, adAccountEntity, unitId);
                    }
                }
                if(adGroupEntity != null) {
                    adAccountEntity = adAccountManager.queryAccountById(adGroupEntity.getAdAccountId());
                }

                if(adAccountEntity != null && adGroupEntity != null) {
                    refreshDataManager.batchSyncBaiduAdGroupToMarketingEventObj(ea, adAccountEntity, Lists.newArrayList(adGroupEntity));
                    adGroupEntity = getBaiduAdGroupEntity(ea, adAccountEntity, unitId);
                    if(adGroupEntity != null && StringUtils.isNotBlank(adGroupEntity.getMarketingEventId())) {
                        result.setAdName(adGroupEntity.getAdGroupName());
                        result.setMarketingEventId(adGroupEntity.getMarketingEventId());
                        return Optional.of(result);
                    }
                }
            }
        }
        Optional<String> marketingEventOptional = utmCreateMarketingEvent(ea, campaignName, source,  mappingEntity, syncUtmCampaign);
        if (marketingEventOptional.isPresent()) {
            result.setAdName(campaignName);
            result.setMarketingEventId(marketingEventOptional.get());
            return Optional.of(result);
        }
        return Optional.empty();
    }

    private AdGroupEntity getBaiduAdGroupEntity(String ea, AdAccountEntity adAccountEntity, long unitId) {
        if (adAccountEntity != null) {
            return adGroupDAO.queryAdgroupByAdgroupIdAndAccountId(ea, adAccountEntity.getId(), unitId);
        }
        List<AdGroupEntity> adGroupEntityList = adGroupDAO.queryByAdgroupId(ea, unitId);
        return CollectionUtils.isEmpty(adGroupEntityList) ? null : adGroupEntityList.get(0);
    }

    private TencentAdGroupEntity getTencentAdGroupEntity(String ea, AdAccountEntity adAccountEntity, long unitId) {
        if (adAccountEntity != null) {
            return tencentAdGroupDAO.queryByAdGroupIdAndAccountId(ea, adAccountEntity.getId(), unitId);
        }
        List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryByAdGroupId(ea, unitId);
        return CollectionUtils.isEmpty(tencentAdGroupEntityList) ? null : tencentAdGroupEntityList.get(0);
    }

    public Optional<String> utmCreateMarketingKeyword(String ea, String keyword, boolean syncUtmCampaign){
        if (StringUtils.isBlank(keyword)) {
            return Optional.empty();
        }
        //查询关键词是否存在
        List<String> keywordList = Lists.newArrayList();
        keywordList.add(keyword);
        Page<ObjectData> queryKeywordResult = refreshDataManager.queryCrmMarketingKeyword(ea, keywordList);
        if (queryKeywordResult == null){
            log.error("UtmDataManger.syncUtmDataToROI query marketingKeyword return null ea:{} keyword:{}", ea, keyword);
            return Optional.empty();
        }

        String marketingKeywordId = null;
        if (CollectionUtils.isNotEmpty(queryKeywordResult.getDataList())){
            marketingKeywordId = queryKeywordResult.getDataList().get(0).getId();
        }

        if (marketingKeywordId == null && syncUtmCampaign){
            int owner = adCommonManager.getAdObjectDataOwner(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createKeywordResult = refreshDataManager.createMarketingKeywordObj(ea, keyword, owner);
            if (createKeywordResult.isSuccess()){
                marketingKeywordId = createKeywordResult.getData().getObjectData().getId();
            }
        }

        return Optional.ofNullable(marketingKeywordId);
    }

    public Optional<String> utmCreateMarketingKeywordPlan(String ea, String marketingEventId, String marketingEventName,
                                                          String marketingKeywordId, String marketingKeywordName, boolean syncUtmCampaign, boolean isSyncAdGroupDimensionality, Long keywordId){
        if (StringUtils.isEmpty(marketingKeywordId)) {
            return Optional.empty();
        }
        List<String> ids = Lists.newArrayList();
        ids.add(marketingKeywordId);
        Page<ObjectData> queryPlanResult = refreshDataManager.queryCrmMarketingKeywordPlan(ea, marketingEventId, ids, keywordId, 100, isSyncAdGroupDimensionality);
        if (queryPlanResult == null) {
            log.warn("UtmDataManger.utmCreateMarketingKeyword query marketingKeywordPlan return null ea:{} marketingEventId:{} marketingKeywordId:{}", ea, marketingEventId, marketingEventId);
            return Optional.empty();
        }

        String marketingKeywordPlanId = null;
        if (CollectionUtils.isNotEmpty(queryPlanResult.getDataList())){
            if (keywordId != null) {
                Optional<ObjectData> optional = queryPlanResult.getDataList().stream().filter(e -> StringUtils.isNotBlank(e.getString("out_platform_data_id")) && Long.parseLong(e.getString("out_platform_data_id")) == keywordId).findFirst();
                if (optional.isPresent()) {
                    marketingKeywordPlanId = optional.get().getId();
                }
            }
            if (marketingKeywordPlanId == null) {
                List<ObjectData> objectDataList = queryPlanResult.getDataList().stream().sorted(Comparator.comparing(ObjectData::getCreateTime).reversed()).collect(Collectors.toList());
                marketingKeywordPlanId = objectDataList.get(0).getId();
            }
        }
        if (marketingKeywordPlanId == null && syncUtmCampaign) {
            // 如果utm传的是单元维度的，但是关键词投放计划却不是单元维度的，直接不处理
            if (isSyncAdGroupDimensionality && !adCommonManager.isSyncAdGroupDimensionality(ea)) {
                log.info("关键词投放计划不是单元维度的, ea: {}", ea);
                return Optional.empty();
            }
            String subMarketingEventId = null;
            if (isSyncAdGroupDimensionality) {
                subMarketingEventId = marketingEventId;
                ObjectData objectData = crmV2Manager.getDetail(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), subMarketingEventId);
                if (objectData == null) {
                    log.info("市场活动不存在, ea: {} subMarketingEventId: {}", ea, subMarketingEventId);
                    return Optional.empty();
                }
                marketingEventName = objectData.getName();
                marketingEventId = objectData.getString("parent_id");
            }
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createKeywordResult =
                    refreshDataManager.createCrmMarketingKeywordPlan(ea, null, marketingEventId, subMarketingEventId, marketingEventName, marketingKeywordId, marketingKeywordName, keywordId);
            if (createKeywordResult.isSuccess()){
                marketingKeywordPlanId = createKeywordResult.getData().getObjectData().getId();
            }
        }
        return Optional.ofNullable(marketingKeywordPlanId);
    }

    public Page<ObjectData> queryMarketingEventByName(String ea, String name) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(MarketingEventFieldContants.NAME, Lists.newArrayList(name), FilterOperatorEnum.EQ);
        searchQuery.setLimit(100);
        searchQuery.setOffset(0);
        return crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
    }

    public com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> updateLeadROIData(String ea, Integer fsUserId, String leadId, String marketingKeywordId,
                                                                                        String marketingKeywordPlanId, String marketingEventId, Boolean allowDuplicate, String utmCampaign, String utmTerm){
        if (StringUtils.isBlank(leadId)) {
            return null;
        }
        if (StringUtils.isBlank(marketingKeywordId) && StringUtils.isBlank(marketingEventId) && StringUtils.isBlank(marketingKeywordPlanId)){
            log.info("utm updateLeadROIData failed param null ea:{} fsUserId:{} leadId:{} marketingEventId:{} marketingKeywordId:{} marketingKeywordPlanId:{}",
                    ea, fsUserId, leadId, marketingEventId, marketingKeywordId, marketingKeywordPlanId);
            return null;
        }

        ActionEditArg actionEditArg = new ActionEditArg();
        Map<String, Object> objectMap = new HashMap<>();
        objectMap.put("object_describe_api_name", CrmObjectApiNameEnum.CRM_LEAD.getName());
        objectMap.put("object_describe_id", CrmObjectApiNameEnum.CRM_LEAD.getName());
        objectMap.put("_id", leadId);
        if (StringUtils.isNotBlank(marketingKeywordId)) {
            objectMap.put("keyword_id", marketingKeywordId);
        }
        if (StringUtils.isNotBlank(utmTerm)) {
            objectMap.put("utm_term__c", utmTerm);
        }
        if (StringUtils.isNotBlank(utmCampaign)) {
            objectMap.put("utm_campaign__c", utmCampaign);
        }
        if (StringUtils.isNotBlank(marketingKeywordPlanId)) {
            objectMap.put("keyword_serving_plan_id",marketingKeywordPlanId);
        }
        if (StringUtils.isNotBlank(marketingEventId)) {
            objectMap.put("marketing_event_id", marketingEventId);
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        objectMap.put("tenant_id", ei);

        actionEditArg.setObjectData(ObjectData.convert(objectMap));
        if (allowDuplicate != null) {
            ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
            optionInfo.setIsDuplicateSearch(allowDuplicate);
            actionEditArg.setOptionInfo(optionInfo);
        }
        // 这里直接用 系统用户id 防止没权限导致修改失败
        HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.CRM_LEAD.getName(), true, true, actionEditArg);
        log.info("updateLeadROIData arg: {}, result: {}", objectMap, editResult);
        return editResult;
    }
}
