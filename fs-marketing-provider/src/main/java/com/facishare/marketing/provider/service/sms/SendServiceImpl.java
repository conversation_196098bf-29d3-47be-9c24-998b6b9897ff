package com.facishare.marketing.provider.service.sms;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.sensors.AddSensorsDataArg;
import com.facishare.mankeep.api.outService.service.OutSensorsService;
import com.facishare.mankeep.api.outService.service.OuterPhoneService;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.mankeep.common.enums.ObjectTypeEnum;
import com.facishare.mankeep.common.typehandlers.value.ActionVO;
import com.facishare.marketing.api.arg.sms.*;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.sms.*;
import com.facishare.marketing.api.result.sms.mw.PhoneContentResult;
import com.facishare.marketing.api.result.sms.mw.SmsParamDescResult;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.GetSmsParamListVO;
import com.facishare.marketing.api.vo.sms.GetSmsTemplateVo;
import com.facishare.marketing.api.vo.sms.ListSmsTemplateVo;
import com.facishare.marketing.common.contstant.sms.MwErrorCodeMapConstants;
import com.facishare.marketing.common.enums.InfoStateEnum;
import com.facishare.marketing.common.enums.MarketingActivitySpreadTypeEnum;
import com.facishare.marketing.common.enums.SendStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.sms.ApplySignatureStatusEnum;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.enums.sms.SMSGrayStatusEnum;
import com.facishare.marketing.common.enums.sms.mw.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CustomizeFormDataUserDAO;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.sms.SmsTrialDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSignatureDao;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO;
import com.facishare.marketing.provider.dto.SmsSendDetailDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.MwCodeEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.sms.mw.*;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingAccountEntity;
import com.facishare.marketing.provider.innerArg.mw.CreateSendTaskArg;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.SmsSendRecordObjManager;
import com.facishare.marketing.provider.manager.sms.SmsTemplateManager;
import com.facishare.marketing.provider.manager.sms.VerificationCodeManager;
import com.facishare.marketing.provider.manager.sms.mw.*;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.mq.sender.SmsEventSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.facishare.marketing.provider.remote.smsplatform.SmsPlatformRestManager;
import com.facishare.marketing.provider.remote.smsplatform.result.SpRestGetSmsTemplateResult;
import com.facishare.marketing.statistic.outapi.arg.ObjectActionUserMarketingListArg;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.facishare.marketing.statistic.outapi.result.ObjectActionUserMarketingListResult;
import com.facishare.marketing.statistic.outapi.service.MarketingActivityStatisticService;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.smsplatform.common.enums.SmsTemplateTypeEnum;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.common.enums.AssociateIdTypeEnum.MANKEEP_SEND_MESSAGE;

/**
 * @创建人 zhengliy
 * @创建时间 2018/12/21 16:05
 * @描述
 */
@Slf4j
@Service("sendService")
public class SendServiceImpl implements SendService {
    private static Map<Integer, String> groupSmsStatusEnumMap = Maps.newHashMap();

    static {
        groupSmsStatusEnumMap.put(1, String.valueOf(SendStatusEnum.FINISHED.getStatus()));
        groupSmsStatusEnumMap.put(2, String.valueOf(SendStatusEnum.WAIT_SEND.getStatus()));
    }
    @Autowired
    private MwSmsSignatureDao signatureDao;

    @Autowired
    private MwSmsTemplateDao templateDAO;

    @Autowired
    private MwSendManager sendManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MwSmsSendDao sendDao;

    @Autowired
    private SmsSettingManager smsSettingManager;

    @Autowired
    private SmsPlatformRestManager smsPlatformRestManager;

    @Autowired
    private SmsTemplateManager smsTemplateManager;

    @Autowired
    private VerificationCodeManager verificationCodeManager;

    @Autowired
    private OuterPhoneService outerPhoneService;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private OutSensorsService sensorsService;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private SmsParamManager smsParamManager;

    @Autowired
    private ShortUrlManager shortUrlManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private SmsTrialDao smsTrialDao;

    @Autowired
    private EIEAConverter eIEAConverter;

    @Autowired
    private MarketingActivityStatisticService marketingActivityStatisticService;

    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private MwSendManager mwSendManager;

    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private MwTemplateManager mwTemplateManager;

    @Autowired
    private MarketingCrmManager marketingActivityCrmManager;

    @Autowired
    private MwSmsSendMarketingEventRelationManager mwSmsSendMarketingEventRelationManager;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private MarketingCrmManager marketingCrmManager;

    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private SmsSendRecordObjManager smsSendRecordObjManager;

    @Autowired
    private SmsEventSender smsEventSender;

    @ReloadableProperty("sms.trial.signatureId")
    private String trialSignatureId;

    @ReloadableProperty("sms.verification.template.id")
    private String smsVerificationTemplateId;

    private static Integer DEFAULT_FS_UID = -5000; // 服务通默认指定的

    private  Map<String, String> MW_CODE_MAP = Maps.newConcurrentMap();

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @PostConstruct
    private void init() {
        List<MwCodeEntity> mwCodeEntityList = sendDao.getAllMwCodeList();
        for (MwCodeEntity mwCodeEntity : mwCodeEntityList) {
            MW_CODE_MAP.put(mwCodeEntity.getCode(), mwCodeEntity.getDesc());
        }
    }

    /**
     * @描述 创建群发短信
     * @创建时间 2019/1/15 17:27
     */
    @Override
    public Result<GroupSendResult> sendGroupSms(GroupSenderArg arg) {
        if (checkArgIsWrong(arg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (arg.getGroupType() == null) {
            if (StringUtils.isNotEmpty(arg.getTaPath())) {
                arg.setGroupType(SmsGroupTypeEnum.EXCEL.getType());
            } else if (CollectionUtils.isNotEmpty(arg.getPhones())) {
                arg.setGroupType(SmsGroupTypeEnum.PHONE_LIST.getType());
            } else if (CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
                arg.setGroupType(SmsGroupTypeEnum.USER_GROUP.getType());
            } else if (CollectionUtils.isNotEmpty(arg.getConferenceInviteIds())) {
                arg.setGroupType(SmsGroupTypeEnum.CONFERENCE_INVITE.getType());
            } else {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }
        CreateSendTaskArg taskArg = new CreateSendTaskArg();
        String ea = arg.getEa();
        if (arg.getTenantId() != null && StringUtils.isBlank(ea)) {
            ea = eIEAConverter.enterpriseIdToAccount(arg.getTenantId());
        }

        // 理论上，发送短信走到营销通服务的都是梦网短信通道，但是由于营销通的特殊性，无论何种短信通道，直接调用的还是营销通接口，这就需要在这里进行特殊处理
        if ((ChannelTypeEnum.isMarketingType(arg.getChannelType()) || Objects.equals(arg.getChannelType(), ChannelTypeEnum.VERIFY_SMS_CODE.getType())) && !smsTemplateManager.isMwTemplate(ea, arg.getTemplateId())) {
            return sendGroupSms4SmsPlatform(arg);
        }

        MwSmsTemplateEntity templateEntity = null;
        arg.setTemplateId(smsTemplateManager.objIdConvertToDbId(ea, arg.getTemplateId()));

        // 模板id为空
        if (Strings.isNullOrEmpty(arg.getTemplateId())) {
            if (SaveOrSendTypeEnum.SEND.getType().equals(arg.getEventType()) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
                return Result.newError(SHErrorCode.SMS_TEMPLATE_STATUS_DENY);
            }
        } else {
            // 如果模板id非空，检查传入的模板id是否正确 以及 审核状态是否通过
            Result<MwSmsTemplateEntity> mwSmsTemplateEntityResult = mwTemplateManager.checkTemplateValid(ea, arg.getTemplateId(), arg.isUnionMsg());
            if (!mwSmsTemplateEntityResult.isSuccess()) {
                return Result.newError(mwSmsTemplateEntityResult.getErrCode(), mwSmsTemplateEntityResult.getErrMsg());
            }
            templateEntity = mwSmsTemplateEntityResult.getData();
            ea = (Strings.isNullOrEmpty(ea) ? templateEntity.getEa() : ea);
            taskArg.setTemplateId(templateEntity.getId());
        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String userName = null;
        if (DEFAULT_FS_UID.equals(arg.getUserId())) {
            userName = "admin";
        } else {
            FsAddressBookManager.FSEmployeeMsg employeeInfo = fsAddressBookManager.getEmployeeInfo(ea, arg.getUserId());
            if (employeeInfo != null) {
                userName = StringUtils.isNotEmpty(employeeInfo.getFullName()) ? employeeInfo.getFullName() : employeeInfo.getName();
            } else {
                log.info("SendServiceImpl.sendGroupSms employeeInfo is null arg:{}", arg);
                return Result.newError(SHErrorCode.USER_ACCOUNT_NOT_EXIST);
            }
        }
        MwSmsSignatureEntity signatureEntity = sendManager.getSignatureOrTrial(ea, arg.getSignatureId(), arg.getChannelType());
        if (signatureEntity == null) {
            log.info("SendServiceImpl.sendGroupSms don't exist signatureEntity arg:{}", arg);
            return Result.newError(SHErrorCode.SIGNATURE_NOT_EXIST);
        }
        if (!signatureEntity.getStatus().equals(ApplySignatureStatusEnum.APPLY_PASS.getStatus())) {
            log.info("SendServiceImpl.sendGroupSms signatureEntity status is not APPLY_PASS, arg:{}", arg);
            return Result.newError(SHErrorCode.SMS_SIGNATURE_STATUS_ERROR);
        }
        if (StringUtil.isNullOrEmpty(arg.getTemplateId())) {
            //创建新模板
            if (StringUtils.isEmpty(arg.getTemplateContent())) {
                log.info("SendServiceImpl sendGroupSms failed, apply template failed, templateContent is null,  arg:{}", arg);
                return Result.newError(SHErrorCode.APPLY_TEMPLATE_NULL_FAILED);
            }
            boolean isShowTemplate = arg.isShowTemplate();
            if (StringUtils.isEmpty(arg.getTemplateName())) {
                isShowTemplate = false;
                arg.setTemplateName("");
            }
            if (arg.getGroupType() != null && (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_ENROLL.getType()) || arg.getGroupType().equals(SmsGroupTypeEnum.LIVE_ENROLL.getType()) || arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_INVITE.getType()) || arg.getGroupType().equals(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType()))) {
                isShowTemplate = false;
            }
            ApplyTemplateVO applyTemplateVO = new ApplyTemplateVO();
            applyTemplateVO.setEa(ea);
            applyTemplateVO.setRemark(arg.getTemplateRemark());  //现在没有remark
            applyTemplateVO.setContent(arg.getTemplateContent());
            applyTemplateVO.setUserId(arg.getUserId());
            applyTemplateVO.setName(arg.getTemplateName());
            applyTemplateVO.setChannelType(arg.getChannelType());
            applyTemplateVO.setSignatureId(signatureEntity.getId());
            if (arg.getSceneType() != null) {
                applyTemplateVO.setSceneType(arg.getSceneType());
            } else {
                if (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_ENROLL.getType())) {
                    applyTemplateVO.setSceneType(SmsSceneTypeEnum.CONFERENCE_ENROLL.getType());
                } else if (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_INVITE.getType())) {
                    applyTemplateVO.setSceneType(SmsSceneTypeEnum.CONFERENCE_INVITE.getType());
                } else if (arg.getGroupType().equals(SmsGroupTypeEnum.LIVE_ENROLL.getType())) {
                    applyTemplateVO.setSceneType(SmsGroupTypeEnum.LIVE_ENROLL.getType());
                } else if (arg.getGroupType().equals(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType())) {
                    applyTemplateVO.setSceneType(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType());
                } else {
                    applyTemplateVO.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
                }
            }
            //根据applyTemplateArg创建MwSmsTemplateEntity
            templateEntity = smsSettingManager.templateSetting(applyTemplateVO, userName, false, isShowTemplate); //这里先不保存模板，根据后续结果来决定是否保存到数据库
            if (templateEntity == null) {
                log.info("SendServiceImpl sendGroupSms failed, as apply template failed arg:{}", arg);
                return Result.newError(SHErrorCode.APPLY_TEMPLATE_FAILED);
            }
            taskArg.setTemplateEntity(templateEntity);
        }

        taskArg.setCreatorName(userName);
        if (arg.getTaskCreatorId() != null) {
            taskArg.setCreatorUserId(arg.getTaskCreatorId());
        } else {
            taskArg.setCreatorUserId(arg.getUserId());
        }
        taskArg.setEa(ea);
        taskArg.setSmsSendId(arg.getSmsSendId());
        taskArg.setPhones(arg.getPhones());
        taskArg.setScheduleTime(arg.getScheduleTime());
        taskArg.setTaPath(arg.getTaPath());
        taskArg.setTaskType(arg.getType());
        taskArg.setUserGroupIds(arg.getUserGroupIds());
        taskArg.setChannelType(arg.getChannelType());
        taskArg.setSignatureId(signatureEntity.getId());
        taskArg.setGroupType(arg.getGroupType());
        taskArg.setCampaignIds(arg.getCampaignIds());
        taskArg.setConferenceInviteIds(arg.getConferenceInviteIds());
        //taskArg.setLiveCustomizeFormDataUserIds(arg.getLiveCustomizeFormDataUserIds());
        taskArg.setFilterNDaySentUser(arg.getFilterNDaySentUser());
        taskArg.setMarketingEventId(arg.getMarketingEventId());
        taskArg.setVeryfySms(arg.isReal());
        taskArg.setBusinessType(arg.getBusinessType());
        taskArg.setReceiver(arg.getReceiver());
        taskArg.setSendNode(arg.getSendNode());
        taskArg.setNodeType(arg.getNodeType());
        taskArg.setObjectId(arg.getObjectId());
        taskArg.setIgnoreErrorPhone(arg.isIgnoreErrorPhone());
        taskArg.setSmsVarArgs(arg.getSmsVarArgs());
        taskArg.setFilterReceived(arg.isFilterReceived());
        taskArg.setVerifyCodeSms(arg.isVerifyCodeSms());
        Result<MwSmsSendEntity> taskResult;

        boolean isSyncSend = (SmsGroupTypeEnum.VERIFICATION_CODE.getType() == arg.getGroupType() && ChannelTypeEnum.VERIFY_SMS_CODE.getType().equals(arg.getChannelType()) && SaveOrSendTypeEnum.SEND.getType().equals(arg.getEventType()));
        if (!isSyncSend){
            if (arg.isReal()){
                isSyncSend = true;
            }
        }
        boolean isSendType = SaveOrSendTypeEnum.SEND.getType().equals(arg.getEventType());
        if (StringUtils.isEmpty(taskArg.getSmsSendId())) {
            taskResult = sendManager.createSendTask(taskArg, isSyncSend, isSendType);
//            if (ChannelTypeEnum.MARKETING_FLOW.getType().equals(arg.getChannelType()) && "营销通(SOP)".equals(arg.getBusinessType())) {
                // SOP发出的短信关联活动
                if (taskResult.isSuccess() && taskResult.getData() != null) {
                    boolean saveToDb = mwSendManager.saveSendIdAndMarketingEventToDB(taskResult.getData(), taskResult.getData().getEa(), arg);
                    if (!saveToDb) {
                        log.warn("SendServiceImpl sendGroupSms saveSendIdAndMarketingEventToDB error taskResult:{} ea:{} arg:{}", taskResult, taskResult.getData().getEa(), arg);
                    }
                }
//            }
        } else {
            taskResult = sendManager.updateSendTask(taskArg);
        }

        if (taskResult == null) {
            log.info("SendServiceImpl sendGroupSms taskResult is null, arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (!taskResult.isSuccess()) {
            log.info("SendServiceImpl sendGroupSms taskResult is not success, taskResult:{}", taskResult);
            return Result.newError(taskResult.getErrCode(), taskResult.getErrMsg());
        }
        if (taskResult.getData() == null) {
            log.info("SendServiceImpl sendGroupSms taskResult data is null, taskResult:{}", taskResult);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        if (arg.getEventType().equals(SaveOrSendTypeEnum.SEND.getType()) && isSyncSend) { // 如果是同步发送，则需要扣费
            boolean reduceResult = sendManager.reduceQuotaAndSend(taskResult.getData(), arg.isVerifyCodeSms());
            if (!reduceResult) {
                return Result.newError(SHErrorCode.QUOTA_LESS_THAN_SPEND);
            }
            //发送到MQ
            smsEventSender.send(taskResult.getData());
            taskResult.getData().setStatus(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus());
            try {
                AddSensorsDataArg sensorsDataArg = new AddSensorsDataArg();
                List<ActionVO> vos = Lists.newArrayList();
                ActionVO actionVO = new ActionVO();
                actionVO.setId(UUIDUtil.getUUID());
                actionVO.setActionType(ActionTypeEnum.SMS_QUOTA_COST.getAction());
                actionVO.setObjectType(ObjectTypeEnum.SMS.getType());
                actionVO.setObjectName(ObjectTypeEnum.SMS.name());
                actionVO.setObjectId(taskResult.getData().getId());
                actionVO.setFsEa(taskResult.getData().getEa());
                actionVO.setUserId(taskResult.getData().getCreatorUserId());
                actionVO.setActionTime(new Date());
                actionVO.setAmount(taskResult.getData().getTotalFee());
                vos.add(actionVO);
                sensorsDataArg.setVos(vos);
                sensorsService.addSensorsData(sensorsDataArg);
            } catch (Exception e) {
                log.info("SendServiceImpl sendGroupSms sensorsService addSensorsData, exception: {}", e.fillInStackTrace());
            }
        } else if (ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType()) && SaveOrSendTypeEnum.SAVE.getType().equals(arg.getEventType())) { // 保存为草稿，则保存到send和marketingEvent信息到mw_sms_send_marketing_event_relation表中
            boolean saveToDb = mwSendManager.saveSendIdAndMarketingEventToDB(taskResult.getData(), taskResult.getData().getEa(), arg);
            if (!saveToDb) {
                log.warn("SendServiceImpl sendGroupSms saveSendIdAndMarketingEventToDB error taskResult:{} ea:{} arg:{}", taskResult, taskResult.getData().getEa(), arg);
            }
        }

        GroupSendResult sendResult = new GroupSendResult();
        sendResult.setSmsSendId(taskResult.getData().getId());
        sendResult.setStatus(taskResult.getData().getStatus());
        return Result.newSuccess(sendResult);
    }

    public Result<GroupSendResult> sendGroupSms4SmsPlatform(GroupSenderArg arg) {
        String ea = arg.getEa();
        if (arg.getTenantId() != null && StringUtils.isBlank(ea)) {
            ea = eIEAConverter.enterpriseIdToAccount(arg.getTenantId());
        }

        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String templateId = arg.getTemplateId();
        if (org.apache.commons.lang3.StringUtils.isBlank(templateId)) {
            log.info("SendServiceImpl -> sendGroupSms4SmsPlatform templateId is null, arg:{}", arg);
            return Result.newError(SHErrorCode.SMS_TEMPLATE_NOT_EXIST);
        }

        // 检查模板
        MwSmsTemplateEntity mwSmsTemplateEntity = smsTemplateManager.getSmsTemplate(ea, templateId);
        if (mwSmsTemplateEntity == null) {
            log.info("SendServiceImpl -> sendGroupSms4SmsPlatform template is not exists, arg:{}", arg);
            return Result.newError(SHErrorCode.SMS_TEMPLATE_NOT_EXIST);
        }

        String userName = null;
        if (DEFAULT_FS_UID.equals(arg.getUserId())) {
            userName = "admin";
        } else {
            FsAddressBookManager.FSEmployeeMsg employeeInfo = fsAddressBookManager.getEmployeeInfo(ea, arg.getUserId());
            if (employeeInfo != null) {
                userName = StringUtils.isNotEmpty(employeeInfo.getFullName()) ? employeeInfo.getFullName() : employeeInfo.getName();
            } else {
                log.info("SendServiceImpl -> sendGroupSms4SmsPlatform employeeInfo is null arg:{}", arg);
                return Result.newError(SHErrorCode.USER_ACCOUNT_NOT_EXIST);
            }
        }

        CreateSendTaskArg taskArg = BeanUtil.copy(arg, CreateSendTaskArg.class);
        taskArg.setCreatorName(userName);
        if (arg.getTaskCreatorId() != null) {
            taskArg.setCreatorUserId(arg.getTaskCreatorId());
        } else {
            taskArg.setCreatorUserId(arg.getUserId());
        }
        taskArg.setEa(ea);
        taskArg.setPhones(arg.getPhones());
        taskArg.setTaskType(arg.getType());
        taskArg.setUserGroupIds(arg.getUserGroupIds());
        taskArg.setSignatureId("-");
        taskArg.setCampaignIds(arg.getCampaignIds());
        taskArg.setConferenceInviteIds(arg.getConferenceInviteIds());
        taskArg.setVeryfySms(arg.isReal());
        taskArg.setSmsVarArgs(arg.getSmsVarArgs());
        Result<MwSmsSendEntity> taskResult = null;

        boolean isSyncSend = (SmsGroupTypeEnum.VERIFICATION_CODE.getType() == arg.getGroupType() && ChannelTypeEnum.VERIFY_SMS_CODE.getType().equals(arg.getChannelType()) && SaveOrSendTypeEnum.SEND.getType().equals(arg.getEventType()));
        if (!isSyncSend){
            if (arg.isReal()){
                isSyncSend = true;
            }
        }
        boolean isSendType = SaveOrSendTypeEnum.SEND.getType().equals(arg.getEventType());
        if (StringUtils.isEmpty(taskArg.getSmsSendId())) {
            taskResult = sendManager.createSendTask4SmsPlatform(taskArg, isSyncSend, isSendType);
            // SOP发出的短信关联活动
            if (taskResult.isSuccess() && taskResult.getData() != null) {
                boolean saveToDb = mwSendManager.saveSendIdAndMarketingEventToDB(taskResult.getData(), taskResult.getData().getEa(), arg);
                if (!saveToDb) {
                    log.warn("SendServiceImpl -> sendGroupSms4SmsPlatform saveSendIdAndMarketingEventToDB error taskResult:{} ea:{} arg:{}", taskResult, taskResult.getData().getEa(), arg);
                }
            }
        } else {
            //taskResult = sendManager.updateSendTask(taskArg);
        }

        if (taskResult == null || !taskResult.isSuccess() || taskResult.getData() == null) {
            log.info("SendServiceImpl -> sendGroupSms4SmsPlatform taskResult failed, arg:{}, taskResult:{}", arg, taskResult);
            return Result.newError(SHErrorCode.UNKNOWN);
        }

        if (arg.getEventType().equals(SaveOrSendTypeEnum.SEND.getType()) && isSyncSend) {
            //发送到MQ
            smsEventSender.send(taskResult.getData());
            taskResult.getData().setStatus(MwSendStatusEnum.SEND_SUCCESSFULLY.getStatus());
        } else if (ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType()) && SaveOrSendTypeEnum.SAVE.getType().equals(arg.getEventType())) { // 保存为草稿，则保存到send和marketingEvent信息到mw_sms_send_marketing_event_relation表中
            boolean saveToDb = mwSendManager.saveSendIdAndMarketingEventToDB(taskResult.getData(), taskResult.getData().getEa(), arg);
            if (!saveToDb) {
                log.warn("SendServiceImpl -> sendGroupSms4SmsPlatform saveSendIdAndMarketingEventToDB error taskResult:{} ea:{} arg:{}", taskResult, taskResult.getData().getEa(), arg);
            }
        }

        GroupSendResult sendResult = new GroupSendResult();
        sendResult.setSmsSendId(taskResult.getData().getId());
        sendResult.setStatus(taskResult.getData().getStatus());
        return Result.newSuccess(sendResult);
    }

    @Override
    public Result<Integer> sendGroupSmsCount(GroupSenderArg arg) {
        if (checkArgIsWrong(arg)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getGroupType() == null) {
            if (StringUtils.isNotEmpty(arg.getTaPath())) {
                arg.setGroupType(SmsGroupTypeEnum.EXCEL.getType());
            } else if (CollectionUtils.isNotEmpty(arg.getPhones())) {
                arg.setGroupType(SmsGroupTypeEnum.PHONE_LIST.getType());
            } else if (CollectionUtils.isNotEmpty(arg.getUserGroupIds())) {
                arg.setGroupType(SmsGroupTypeEnum.USER_GROUP.getType());
            } else if (CollectionUtils.isNotEmpty(arg.getConferenceInviteIds())) {
                arg.setGroupType(SmsGroupTypeEnum.CONFERENCE_INVITE.getType());
            } else {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }
        }
        CreateSendTaskArg taskArg = new CreateSendTaskArg();
        String ea = arg.getEa();
        if (arg.getTenantId() != null && StringUtils.isBlank(ea)) {
            ea = eIEAConverter.enterpriseIdToAccount(arg.getTenantId());
        }
        MwSmsTemplateEntity templateEntity = null;
        // 模板id为空
        if (Strings.isNullOrEmpty(arg.getTemplateId())) {
            if (SaveOrSendTypeEnum.SEND.getType().equals(arg.getEventType()) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
                return Result.newError(SHErrorCode.SMS_TEMPLATE_STATUS_DENY);
            }
        } else {
            // 如果模板id非空，检查传入的模板id是否正确 以及 审核状态是否通过
            arg.setTemplateId(smsTemplateManager.objIdConvertToDbId(ea, arg.getTemplateId()));
            Result<MwSmsTemplateEntity> mwSmsTemplateEntityResult = mwTemplateManager.checkTemplateValid(ea, arg.getTemplateId(), arg.isUnionMsg());
            if (!mwSmsTemplateEntityResult.isSuccess()) {
                return Result.newError(mwSmsTemplateEntityResult.getErrCode(), mwSmsTemplateEntityResult.getErrMsg());
            }
            templateEntity = mwSmsTemplateEntityResult.getData();
            ea = (Strings.isNullOrEmpty(ea) ? templateEntity.getEa() : ea);
            taskArg.setTemplateId(templateEntity.getId());
        }
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String userName = null;
        if (DEFAULT_FS_UID.equals(arg.getUserId())) {
            userName = "admin";
        } else {
            FsAddressBookManager.FSEmployeeMsg employeeInfo = fsAddressBookManager.getEmployeeInfo(ea, arg.getUserId());
            if (employeeInfo != null) {
                userName = StringUtils.isNotEmpty(employeeInfo.getFullName()) ? employeeInfo.getFullName() : employeeInfo.getName();
            } else {
                return Result.newError(SHErrorCode.USER_ACCOUNT_NOT_EXIST);
            }
        }
        if (StringUtil.isNullOrEmpty(arg.getTemplateId())) {
            //创建新模板
            if (StringUtils.isEmpty(arg.getTemplateContent())) {
                return Result.newError(SHErrorCode.APPLY_TEMPLATE_NULL_FAILED);
            }
            boolean isShowTemplate = arg.isShowTemplate();
            if (StringUtils.isEmpty(arg.getTemplateName())) {
                isShowTemplate = false;
                arg.setTemplateName("");
            }
            if (arg.getGroupType() != null && (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_ENROLL.getType()) || arg.getGroupType().equals(SmsGroupTypeEnum.LIVE_ENROLL.getType()) || arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_INVITE.getType()) || arg.getGroupType().equals(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType()))) {
                isShowTemplate = false;
            }
            ApplyTemplateVO applyTemplateVO = new ApplyTemplateVO();
            applyTemplateVO.setEa(ea);
            applyTemplateVO.setRemark(arg.getTemplateRemark());  //现在没有remark
            applyTemplateVO.setContent(arg.getTemplateContent());
            applyTemplateVO.setUserId(arg.getUserId());
            applyTemplateVO.setName(arg.getTemplateName());
            applyTemplateVO.setChannelType(arg.getChannelType());
            if (arg.getSceneType() != null) {
                applyTemplateVO.setSceneType(arg.getSceneType());
            } else {
                if (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_ENROLL.getType())) {
                    applyTemplateVO.setSceneType(SmsSceneTypeEnum.CONFERENCE_ENROLL.getType());
                } else if (arg.getGroupType().equals(SmsGroupTypeEnum.CONFERENCE_INVITE.getType())) {
                    applyTemplateVO.setSceneType(SmsSceneTypeEnum.CONFERENCE_INVITE.getType());
                } else if (arg.getGroupType().equals(SmsGroupTypeEnum.LIVE_ENROLL.getType())) {
                    applyTemplateVO.setSceneType(SmsGroupTypeEnum.LIVE_ENROLL.getType());
                } else if (arg.getGroupType().equals(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType())) {
                    applyTemplateVO.setSceneType(SmsGroupTypeEnum.MARKETING_ACTIVITY.getType());
                } else {
                    applyTemplateVO.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
                }
            }
            //根据applyTemplateArg创建MwSmsTemplateEntity
            templateEntity = smsSettingManager.templateSetting(applyTemplateVO, userName, false, isShowTemplate); //这里先不保存模板，根据后续结果来决定是否保存到数据库
            taskArg.setTemplateEntity(templateEntity);
        }

        taskArg.setCreatorName(userName);
        taskArg.setCreatorUserId(arg.getUserId());
        taskArg.setEa(ea);
        taskArg.setSmsSendId(arg.getSmsSendId());
        taskArg.setPhones(arg.getPhones());
        taskArg.setScheduleTime(arg.getScheduleTime());
        taskArg.setTaPath(arg.getTaPath());
        taskArg.setTaskType(arg.getType());
        taskArg.setUserGroupIds(arg.getUserGroupIds());
        taskArg.setChannelType(arg.getChannelType());
        taskArg.setGroupType(arg.getGroupType());
        taskArg.setCampaignIds(arg.getCampaignIds());
        taskArg.setConferenceInviteIds(arg.getConferenceInviteIds());
        taskArg.setFilterNDaySentUser(arg.getFilterNDaySentUser());
        taskArg.setMarketingEventId(arg.getMarketingEventId());
        taskArg.setVeryfySms(arg.isReal());
        taskArg.setBusinessType(arg.getBusinessType());
        taskArg.setReceiver(arg.getReceiver());
        taskArg.setSendNode(arg.getSendNode());
        taskArg.setNodeType(arg.getNodeType());
        taskArg.setObjectId(arg.getObjectId());

        Integer sendDetailCount = sendManager.createSendDetailCount(taskArg);
        return Result.newSuccess(sendDetailCount);
    }

    /**
     * 检验参数
     */
    private boolean checkArgIsWrong(GroupSenderArg arg) {
        if (!ChannelTypeEnum.isValidType(arg.getChannelType())){
            log.info("SendServiceImpl.sendGroupSms channelType error, arg:{}", arg);
            return true;
        }
        if (!SaveOrSendTypeEnum.isValid(arg.getEventType())) {
            log.info("SendServiceImpl.sendGroupSms SendType error, arg:{}", arg);
            return true;
        }
        // 如果是草稿并且为营销通类型，则需要传入marketingEventId
        if (SaveOrSendTypeEnum.SAVE.getType().equals(arg.getEventType()) && ChannelTypeEnum.MARKETING.getType().equals(arg.getChannelType())) {
            if (StringUtils.isEmpty(arg.getMarketingEventId())) {
                log.info("SendServiceImpl.sendGroupSms eventType:{} channelType:{} marketingEventId:{}", arg.getEventType(), arg.getChannelType(), arg.getMarketingEventId());
                return true;
            }
        }
        if (!MwSendTaskTypeEnum.isValid(arg.getType())) {
            log.info("SendServiceImpl.sendGroupSms type is error type:{}", arg.getType());
            return true;
        }
        return false;
    }

    /**
     * 发送验证码短信
     * @param arg
     * @return
     */
    @Override
    public Result<Void> sendVerificationCode(SendVerificationCodeArg arg) {
        //生成验证码
        Result<Integer> verificationCodeResult = verificationCodeManager.getVerifyCode(arg.getPhone());
        if (!verificationCodeResult.isSuccess()){
            return Result.newError(verificationCodeResult.getErrCode(), verificationCodeResult.getErrMsg());
        }

        MwSmsSignatureEntity signatureEntity = null;
        //如果没有租户信息或者租户没有申请签名&签名审核未完成，都是用纷享销客的签名
        if (org.apache.commons.lang.StringUtils.isEmpty(arg.getEa())){
            signatureEntity =  signatureDao.getSignatureById(trialSignatureId);
        }else{
            MwSmsSignatureEntity enterpriseSignatureEntity = signatureDao.getSignatureByEa(arg.getEa());
            if (enterpriseSignatureEntity == null || enterpriseSignatureEntity.getStatus() != ApplySignatureStatusEnum.APPLY_PASS.getStatus()){
                signatureEntity = signatureDao.getSignatureById(trialSignatureId);
            }else {
                signatureEntity = enterpriseSignatureEntity;
            }
        }

        //有签名客户，用客户自己的签名发送短信（验证码模版）
        GroupSenderArg groupSenderArg = new GroupSenderArg();
        groupSenderArg.setGroupType(SmsGroupTypeEnum.VERIFICATION_CODE.getType());
        groupSenderArg.setEventType(SaveOrSendTypeEnum.SEND.getType());
        groupSenderArg.setChannelType(ChannelTypeEnum.MARKETING.getType());
        groupSenderArg.setTemplateId(smsTemplateManager.matchTemplateId(arg.getEa(), arg.getPhone()));
        groupSenderArg.setEa(arg.getEa() == null ? signatureEntity.getEa() : arg.getEa());
        groupSenderArg.setTenantId(eIEAConverter.enterpriseAccountToId(groupSenderArg.getEa()));
        groupSenderArg.setUserId(DEFAULT_FS_UID);
        groupSenderArg.setType(MwSendTaskTypeEnum.IMMEDIATELY_SEND.getType());
        List<PhoneContentResult> phoneContentResults = Lists.newArrayList();

        PhoneContentResult phoneContentResult = new PhoneContentResult();
        phoneContentResult.setPhone(arg.getPhone());
        List<String> params = Lists.newArrayList();
        params.add(verificationCodeResult.getData().toString());
        phoneContentResult.setParams(params);
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("code", verificationCodeResult.getData().toString());
        phoneContentResult.setParamMap(paramMap);
        phoneContentResults.add(phoneContentResult);
        groupSenderArg.setPhones(phoneContentResults);
        groupSenderArg.setShowTemplate(false);
        groupSenderArg.setSignatureId(signatureEntity.getId());
        groupSenderArg.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
        groupSenderArg.setChannelType(ChannelTypeEnum.VERIFY_SMS_CODE.getType());
        groupSenderArg.setBusinessType(ChannelTypeEnum.VERIFY_SMS_CODE.getName());
        groupSenderArg.setVerifyCodeSms(true);
        Result<GroupSendResult> sendResultResult = sendGroupSms(groupSenderArg);
        if (!sendResultResult.isSuccess()){
            log.info("sendVerificationCode failed arg:{}", arg);
            return Result.newError(SHErrorCode.FS_SEND_SMCODE_FAIL);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QuerySMSSendResult>> getSMSSendHistory(String ea, Integer fsUserId, QuerySMSSendArg arg) {
        PageResult<QuerySMSSendResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);

        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(arg.getSearchText())){
            marketingActivityArg.setName(arg.getSearchText());
        }
        if (arg.getStartTime() != null){
            marketingActivityArg.setBeginTime(arg.getStartTime().getTime());
        }
        if (arg.getEndTime() != null){
            marketingActivityArg.setEndTime(arg.getEndTime().getTime());
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(arg.getCreatorUserIds())){
            List<String> owners = arg.getCreatorUserIds().stream().map(userId -> String.valueOf(userId)).collect(Collectors.toList());
            marketingActivityArg.setOwnerIds(owners);
        }
        marketingActivityArg.setSpreadType(MarketingActivitySpreadTypeEnum.SEND_NOTE.getSpreadType());
        //从营销活动对象获取数据
        marketingActivityArg.setPageSize(arg.getPageSize());
        marketingActivityArg.setPageNumber((arg.getPageNum() - 1) * arg.getPageSize());
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageMarketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (pageMarketingActivityList == null || org.apache.commons.collections.CollectionUtils.isEmpty(pageMarketingActivityList.getDataList())){
            return Result.newSuccess(pageResult);
        }
        Map<String, ObjectData> idToMarketingActivityDataMap = pageMarketingActivityList.getDataList().stream().collect(Collectors.toMap(ObjectData::getId, Function.identity()));
        pageResult.setTotalCount(pageMarketingActivityList.getTotal());
        List<ObjectData> marketingActivityList = pageMarketingActivityList.getDataList();
        List<String> marketingActivityIdList = marketingActivityList.stream().map(ObjectData::getId).collect(Collectors.toList());
        Set<String> marketingActivityIds = new HashSet<>(marketingActivityIdList);
        List<SmsSendInfoEntity> smsSendInfoEntities = sendDao.querySmsSendByMarketingActivityIds(ea, marketingActivityIdList);
        if (null == smsSendInfoEntities) {
            log.warn("getSMSSendHistory smsSendInfoEntities is empty, pageResult:{}  arg:{}", pageResult, arg);
            return Result.newSuccess(pageResult);
        }

        //添加短信相关信息
        List<QuerySMSSendResult> querySMSSendResults = buildQuerySMSSendListResult(smsSendInfoEntities, ea, fsUserId);
        //添加市场活动相关，包括线索等
        doFillMarketingActivityIdToSmsSenResults(querySMSSendResults);
        if(!marketingActivityIds.isEmpty()){
            Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(arg.getEa(), marketingActivityIds).getData();
            Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(arg.getEa(), marketingActivityIds).getData();
            Map<String, Integer> leadCountMap = customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(arg.getEa(), new ArrayList<>(marketingActivityIds), null, null, false).stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getMarketingActivityId, CustomizeFormClueNumDTO::getCount, (v1, v2) -> v1));
            querySMSSendResults.forEach(d -> {
                d.setUv(uvMap.get(d.getMarketingActivityId()));
                d.setPv(pvMap.get(d.getMarketingActivityId()));
                d.setLeadCount(leadCountMap.get(d.getMarketingActivityId()));
                ObjectData objectData = idToMarketingActivityDataMap.get(d.getMarketingActivityId());
                if (objectData != null) {
                    d.setAuditStatus(objectData.getLifeStatus());
                }
            });
        }
        pageResult.setResult(querySMSSendResults);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

/*
    public Result<PageResult<QuerySMSSendResult>> getSMSSendHistoryOld(QuerySMSSendArg arg) {
        if (null == arg.getEa()) {
            log.warn("SendServiceImpl.getSMSSendHistory ea is null arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<QuerySMSSendResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        String searchText = null;
        if (StringUtils.isNotEmpty(arg.getSearchText())) {
            searchText = arg.getSearchText();
        }
        List<SmsSendInfoEntity> smsSendInfoEntities = sendDao.querySmsSendInfoList(arg.getEa(), page, searchText, arg.getCreator(), arg.getStartTime(), arg.getEndTime(), arg.getChannelType());
        if (null == smsSendInfoEntities) {
            log.warn("getSMSSendHistory smsSendInfoEntities is empty, pageResult:{}  arg:{}", pageResult, arg);
            return Result.newSuccess(pageResult);
        }
        //添加短信相关信息
        List<QuerySMSSendResult> querySMSSendResults = buildQuerySMSSendListResult(smsSendInfoEntities);
        //添加市场活动相关，包括线索等
        doFillMarketingActivityIdToSmsSenResults(querySMSSendResults);
        Set<String> marketingActivityIds = querySMSSendResults.stream().map(QuerySMSSendResult::getMarketingActivityId).collect(Collectors.toSet());
        if(!marketingActivityIds.isEmpty()){
            Map<String, Integer> uvMap = marketingActivityStatisticService.getUVs(arg.getEa(), marketingActivityIds).getData();
            Map<String, Integer> pvMap = marketingActivityStatisticService.getPVs(arg.getEa(), marketingActivityIds).getData();
            Map<String, Integer> leadCountMap = customizeFormDataUserDAO.batchCountClueNumByMarketingActivityIds(arg.getEa(), new ArrayList<>(marketingActivityIds), null, null, false).stream().collect(Collectors.toMap(CustomizeFormClueNumDTO::getMarketingActivityId, CustomizeFormClueNumDTO::getCount, (v1, v2) -> v1));
            querySMSSendResults.forEach(d -> {
                d.setUv(uvMap.get(d.getMarketingActivityId()));
                d.setPv(pvMap.get(d.getMarketingActivityId()));
                d.setLeadCount(leadCountMap.get(d.getMarketingActivityId()));
            });
        }
        pageResult.setResult(querySMSSendResults);
        pageResult.setTotalCount(page.getTotalNum());
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }
    */

    private void doFillMarketingActivityIdToSmsSenResults(List<QuerySMSSendResult> smsSendResults){
        List<String> smsSendResultIds = smsSendResults.stream().map(QuerySMSSendResult::getId).collect(Collectors.toList());
        if(!smsSendResultIds.isEmpty()){
            List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigEntities = marketingActivityExternalConfigDao.listByAssociateIdTypeAndIds(MANKEEP_SEND_MESSAGE.getType(), smsSendResultIds);
            Map<String,String> smsSendResultIdToMarketingActivityIdMap = marketingActivityExternalConfigEntities.stream().collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getAssociateId, MarketingActivityExternalConfigEntity::getMarketingActivityId, (v1, v2) -> v1));
            smsSendResults.forEach(smsSendResult -> {
                smsSendResult.setMarketingActivityId(smsSendResultIdToMarketingActivityIdMap.get(smsSendResult.getId()));
            });
        }
    }

    private List<QuerySMSSendResult> buildQuerySMSSendListResult(List<SmsSendInfoEntity> smsSendInfoEntities, String ea, Integer fsUserId) {
        List<QuerySMSSendResult> querySMSSendResults = Lists.newArrayList();
        if (CollectionUtils.isEmpty(smsSendInfoEntities)) {
            return querySMSSendResults;
        }
        List<String> marketingEventIds = smsSendInfoEntities.stream().filter(smsSendInfoEntity -> StringUtils.isNotEmpty(smsSendInfoEntity.getMarketingEventId()))
            .map(smsSendInfoEntity -> smsSendInfoEntity.getMarketingEventId()).collect(Collectors.toList());
        List<MarketingEventData> eventDataList = null;
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            eventDataList = marketingEventManager.listMarketingEventData(smsSendInfoEntities.get(0).getEa(), fsUserId, marketingEventIds);
        }
        Map<String, MarketingEventData> marketingEventDataMap = null;
        if (CollectionUtils.isNotEmpty(eventDataList)) {
            marketingEventDataMap = eventDataList.stream().collect(Collectors.toMap(MarketingEventData::getId, Function.identity(), (k1, k2) -> k1));
        }
        if (marketingEventDataMap == null) {
            marketingEventDataMap = Maps.newHashMap();
        }

        Map<String, String> activityEntityMap = null;
        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIds);
        if (CollectionUtils.isNotEmpty(activityEntityList)){
            activityEntityMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, ActivityEntity::getId, (k1, k2)->k1));
        }

        // 找出模板对象id
        List<String> templateObjIds = smsSendInfoEntities.stream().filter(smsSendInfoEntity -> {
            String templateId = smsSendInfoEntity.getTemplateId();
            return org.apache.commons.lang3.StringUtils.isNotBlank(templateId) && templateId.length() == 24;
        }).map(smsSendInfoEntity -> smsSendInfoEntity.getTemplateId()).collect(Collectors.toList());
        List<SpRestGetSmsTemplateResult> spRestGetSmsTemplateResults = smsTemplateManager.batchQueryByObjIds(ea, templateObjIds);
        Map<String, SpRestGetSmsTemplateResult> spRestGetSmsTemplateResultMap = spRestGetSmsTemplateResults.stream().collect(Collectors.toMap(SpRestGetSmsTemplateResult::getId, Function.identity(), (v1, v2) -> v1));

        for (SmsSendInfoEntity smsSendInfo : smsSendInfoEntities) {
            QuerySMSSendResult querySMSSendResult = new QuerySMSSendResult();
            querySMSSendResult.setId(smsSendInfo.getId());
            querySMSSendResult.setCreateTime(smsSendInfo.getCreateTime().getTime());
            querySMSSendResult.setCreator(smsSendInfo.getCreatorName());
            querySMSSendResult.setActualSenderCount(smsSendInfo.getActualSenderCount());
            querySMSSendResult.setToSenderCount(smsSendInfo.getToSenderCount());
            querySMSSendResult.setStatus(smsSendInfo.getStatus());
            querySMSSendResult.setConsumerCount(smsSendInfo.getTotalFee());
            if (smsSendInfo.getStatus() == MwSendStatusEnum.NEW.getStatus() && smsSendInfo.getTemplateStatus() != null && smsSendInfo.getTemplateStatus() >= MwTemplateStatusEnum.INVALID.getStatus()){
                querySMSSendResult.setConsumerCount(0);
                querySMSSendResult.setActualSenderCount(0);
            }
            querySMSSendResult.setTemplateName(smsSendInfo.getName());
            querySMSSendResult.setTemplateId(smsSendInfo.getTemplateId());
            querySMSSendResult.setTemplateStatus(smsSendInfo.getTemplateStatus());
            querySMSSendResult.setType(smsSendInfo.getType());
            querySMSSendResult.setSceneType(smsSendInfo.getSceneType());
            //当且仅当发送状态为定时发送，才能够删除
            if ( smsSendInfo.getType() == MwSendTaskTypeEnum.SCHEDULE_SEND.getType()
                    && querySMSSendResult.getStatus().equals(MwSendStatusEnum.SCHEDULE_SENDING.getStatus())) {
                querySMSSendResult.setSendCancelable(true);
            } else {
                querySMSSendResult.setSendCancelable(false);
            }
            MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendInfo.getSignatureId());
            if (signatureEntity != null) {
                querySMSSendResult.setSignatrure(signatureEntity.getSvrName());
                String content = sendManager.buildSendContent(signatureEntity.getSvrName(), smsParamManager.getShowParamNameTemplate(smsSendInfo.getContent(), smsSendInfo.getParamDetail()), Lists.newArrayList());
                querySMSSendResult.setContent(content);
            } else { // 除非硬删除不然不会出现这样的情况
                querySMSSendResult.setContent(smsSendInfo.getContent());
            }

            if (smsSendInfo.getResultCode() != null) {
                querySMSSendResult.setReply(MwErrorCodeMapConstants.getStringForResultCode(smsSendInfo.getResultCode() + ""));
            }
            if (smsSendInfo.getScheduleTime() != null){
                querySMSSendResult.setScheduleTime(smsSendInfo.getScheduleTime().getTime());
            }

            if (StringUtils.isNotEmpty(smsSendInfo.getMarketingEventId())) {
                querySMSSendResult.setMarketingEventId(smsSendInfo.getMarketingEventId());
                if (marketingEventDataMap.get(smsSendInfo.getMarketingEventId()) != null) {
                    querySMSSendResult.setMarketingEventName(marketingEventDataMap.get(smsSendInfo.getMarketingEventId()).getName());
                    querySMSSendResult.setMarketingEventType(marketingEventDataMap.get(smsSendInfo.getMarketingEventId()).getEventType());
                    if (activityEntityMap != null && activityEntityMap.get(smsSendInfo.getMarketingEventId()) != null) {
                        querySMSSendResult.setMarketingObjectId(activityEntityMap.get(smsSendInfo.getMarketingEventId()));
                    }
                }
            }

            if (smsSendInfo.getExternalConfig() != null && smsSendInfo.getExternalConfig().getMarketingActivityGroupSenderVO() != null) {
                querySMSSendResult.setUserGroupIds(smsSendInfo.getExternalConfig().getMarketingActivityGroupSenderVO().getMarketingUserGroupIds());
                querySMSSendResult.setTaPathName(smsSendInfo.getExternalConfig().getMarketingActivityGroupSenderVO().getTaPathName());
            }

            // 填充短信模板对象数据
            String templateId = querySMSSendResult.getTemplateId();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(templateId) && templateId.length() == 24) {
                SpRestGetSmsTemplateResult spRestGetSmsTemplateResult = spRestGetSmsTemplateResultMap.get(templateId);
                if (spRestGetSmsTemplateResult != null) {
                    querySMSSendResult.setTemplateName(spRestGetSmsTemplateResult.getName());
                    querySMSSendResult.setContent(spRestGetSmsTemplateResult.getContent());
                    querySMSSendResult.setTemplateStatus(smsTemplateManager.objStatusToMwTemplateStatus(spRestGetSmsTemplateResult.getStatus()).getStatus());
                    String tplType = spRestGetSmsTemplateResult.getTplType();
                    if (Objects.equals(tplType, SmsTemplateTypeEnum.PROMOTION.getType())) {
                        querySMSSendResult.setSceneType(SmsSceneTypeEnum.GENERAL_MARKETING.getType());
                    } else {
                        querySMSSendResult.setSceneType(SmsSceneTypeEnum.GENERAL.getType());
                    }
                }
            }

            querySMSSendResults.add(querySMSSendResult);
        }

        return querySMSSendResults;
    }

    private QuerySMSSendResult buildQuerySMSSendResult(MwSmsSendEntity smsSendEntity) {
        QuerySMSSendResult querySMSSendResult = new QuerySMSSendResult();
        querySMSSendResult.setId(smsSendEntity.getId());
        querySMSSendResult.setCreateTime(smsSendEntity.getCreateTime().getTime());
        querySMSSendResult.setCreator(smsSendEntity.getCreatorName());
        querySMSSendResult.setActualSenderCount(smsSendEntity.getActualSenderCount());
        querySMSSendResult.setToSenderCount(smsSendEntity.getToSenderCount());
        querySMSSendResult.setStatus(smsSendEntity.getStatus());
        querySMSSendResult.setConsumerCount(smsSendEntity.getTotalFee());
        if (smsSendEntity.getSendTime() != null){
            querySMSSendResult.setSendTime(smsSendEntity.getSendTime().getTime());
        }
        querySMSSendResult.setType(smsSendEntity.getType());
        if (smsSendEntity.getType() == MwSendTaskTypeEnum.SCHEDULE_SEND.getType()
                && smsSendEntity.getStatus() == MwSendStatusEnum.SCHEDULE_SENDING.getStatus()){
            querySMSSendResult.setSendCancelable(true);
        }else {
            querySMSSendResult.setSendCancelable(false);
        }
        String templateContent = "";
        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(smsSendEntity.getEa(), smsSendEntity.getTemplateId());
        if (templateEntity != null) {
            templateContent = smsParamManager.getShowParamNameTemplate(templateEntity.getContent(), templateEntity.getParamDetail());
            querySMSSendResult.setTemplateName(templateEntity.getName());
            querySMSSendResult.setTemplateId(templateEntity.getId());
            querySMSSendResult.setSceneType(templateEntity.getSceneType());
            if (smsSendEntity.getStatus() ==0 && templateEntity.getStatus() >= MwTemplateStatusEnum.INVALID.getStatus()){
                querySMSSendResult.setConsumerCount(0);
                querySMSSendResult.setActualSenderCount(0);
            }
        }

        MwSmsSignatureEntity signatureEntity = signatureDao.getSignatureById(smsSendEntity.getSignatureId());
        if (signatureEntity != null) {
            querySMSSendResult.setSignatrure(signatureEntity.getSvrName());
            String content = sendManager.buildSendContent(signatureEntity.getSvrName(), templateContent, Lists.newArrayList());
            querySMSSendResult.setContent(content);
        } else { // 除非硬删除不然不会出现这样的情况
            querySMSSendResult.setContent(templateContent);
        }

        if (smsSendEntity.getResultCode() != null) {
            querySMSSendResult.setReply(MwErrorCodeMapConstants.getStringForResultCode(smsSendEntity.getResultCode() + ""));
        }
        querySMSSendResult.setScheduleTime(smsSendEntity.getScheduleTime()==null?0:smsSendEntity.getScheduleTime().getTime());
        return querySMSSendResult;
    }

    private List<SMSSendDetailResult> getSMSSendDetailResultList(List<MwSendDetailEntity> smsSendDetailEntities, MwSmsTemplateEntity templateEntity) {

        List<SMSSendDetailResult> smsSendDetailResults = new ArrayList<>();
        if (CollectionUtils.isEmpty(smsSendDetailEntities)) {
            return smsSendDetailResults;
        }

        for (MwSendDetailEntity smsSendDetailEntity : smsSendDetailEntities) {
            SMSSendDetailResult smsSendDetailResult = new SMSSendDetailResult();
            smsSendDetailResult.setPhone(smsSendDetailEntity.getPhone());
            smsSendDetailResult.setStatus(smsSendDetailEntity.getStatus());
            String reply = smsSendDetailEntity.getErrCodeDesc();
            if (StringUtils.isEmpty(reply)) {
                reply = MwErrorCodeMapConstants.getStringForResultCode(smsSendDetailEntity.getErrCode());
            }
            smsSendDetailResult.setReply(reply);
            smsSendDetailResult.setArriveTime(smsSendDetailEntity.getUpdateTime().getTime());
            int spendSMSCount = smsSendDetailEntity.getFee();
            Integer status = smsSendDetailEntity.getStatus();
            // 对于发送失败的短信，是不扣费的，这里返回的时候处理一下
            if ( status != null && status.equals(MwSendDetailStatusEnum.SEND_FAIL.getStatus())) {
                spendSMSCount = 0;
            }
            smsSendDetailResult.setSpendSMSCount(spendSMSCount);
            smsSendDetailResults.add(smsSendDetailResult);
        }
        return smsSendDetailResults;
    }

    @Override
    public Result<PageResult<SMSSendDetailResult>> getSMSSendDetail(QuerySMSSendDetailArg arg) {
        if (arg.getPageNum() == null || arg.getPageSize() == null || StringUtils.isEmpty(arg.getEa()) || arg.getFsUserId() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<SMSSendDetailResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        MwSmsSendEntity smsSendEntity = sendDao.getSMSSendById(arg.getSmsSendId());
        if (smsSendEntity == null) {
            log.warn("SendServiceImpl.getSMSSendDetail  SMSSendEntity is empty, smsSendId:{}", arg.getSmsSendId());
            return Result.newError(SHErrorCode.NO_DATA);
        }

        List<MwSendDetailEntity> smsSendDetailEntities = sendDao.getSendDetailBySmsSendIdAndStatus2Page(arg.getSmsSendId(), arg.getStatus(), page);
        List<SMSSendDetailResult> smsSendDetailResults;

        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(arg.getEa(), smsSendEntity.getTemplateId());
        smsSendDetailResults = getSMSSendDetailResultList(smsSendDetailEntities, templateEntity);
        //根据手机号查找营销用户信息
        fillUserMarketingAccountInfoToResult(arg.getEa(),arg.getFsUserId(), arg.getMarketingActivityId(), smsSendDetailResults);

        pageResult.setResult(smsSendDetailResults);
        pageResult.setTotalCount(page.getTotalNum());

        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    /**
     * 通过手机号关联营销用户
     */
    private void fillUserMarketingAccountInfoToResult(String ea, Integer fsUserId, String marketingActivityId, List<SMSSendDetailResult> smsSendDetailResults) {
        if (CollectionUtils.isEmpty(smsSendDetailResults)) {
            return ;
        }
        List<String> phones = smsSendDetailResults.stream()
                .filter(sMSSendDetailResult -> sMSSendDetailResult.getPhone() != null)
                .map(SMSSendDetailResult::getPhone)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phones)) {
            return ;
        }
        //通过手机查找营销用户
        int tenantId = eIEAConverter.enterpriseAccountToId(ea);
        List<UserMarketingAccountEntity> userMarketingAccounts = userMarketingAccountDAO.getUserMarketingAccountByPhones(tenantId, phones);
        if (CollectionUtils.isEmpty(userMarketingAccounts)) {
            return ;
        }
        //通过营销用户id查找营销用户数据
        List<String> userMarketingAccountIds = userMarketingAccounts.stream().map(UserMarketingAccountEntity::getId).collect(Collectors.toList());
        //map: 手机号->营销用户
        Map<String, UserMarketingAccountEntity> phoneToUserMarketingAccountEntity = userMarketingAccounts.stream()
                .collect(Collectors.toMap(UserMarketingAccountEntity::getPhone, Function.identity(), (k1, k2) -> k1));
        //map：营销用户->详细信息（姓名）
        Map<String, UserMarketingAccountData> userMarketingAccountIdToData = userMarketingAccountManager.getBaseInfosByIds(ea, fsUserId, userMarketingAccountIds, InfoStateEnum.BRIEF);
        //map:营销用户->访问次数
        Map<String, Long> userMarketingLookUpCountMap = new HashMap<>();
        //map：营销用户->提交线索数
        Map<String, Long> userMarketingClueNumMap = new HashMap<>();
        if (!userMarketingAccountIds.isEmpty()) {
//            List<Map<String, Long>> userMarketingLookUpCountMaps = userMarketingAccountDAO.queyLookUpCountByParams(ea, marketingActivityId, userMarketingAccountIds);
//            if (userMarketingLookUpCountMaps != null) {
//                for (Map<String, Long> map : userMarketingLookUpCountMaps) {
//                    userMarketingLookUpCountMap.put(String.valueOf(map.get("usermarketingid")), map.get("count"));
//                }
//            }
            userMarketingLookUpCountMap.putAll(getUserMarketingLookUpCount(ea, fsUserId, marketingActivityId, userMarketingAccountIds));
            List<Map<String, Long>> userMarketingClueNumMaps = userMarketingAccountDAO.queyClueNumByParams(ea, marketingActivityId, userMarketingAccountIds);
            if (userMarketingClueNumMaps != null) {
                for (Map<String, Long> map : userMarketingClueNumMaps) {
                    userMarketingClueNumMap.put(String.valueOf(map.get("usermarketingid")), map.get("count"));
                }
            }
        }
        //填充phone对应的营销用户信息
        smsSendDetailResults.forEach(smsSendDetailResult -> {
            if (!Strings.isNullOrEmpty(smsSendDetailResult.getPhone())) {
                UserMarketingAccountEntity userMarketingAccountEntity = phoneToUserMarketingAccountEntity.get(smsSendDetailResult.getPhone());
                if (userMarketingAccountEntity != null) {
                    smsSendDetailResult.setUserMarketingAccountId(userMarketingAccountEntity.getId());
                    if (userMarketingAccountIdToData != null && userMarketingAccountIdToData.size() != 0) {
                        UserMarketingAccountData userMarketingAccountData = userMarketingAccountIdToData.get(userMarketingAccountEntity.getId());
                        if (userMarketingAccountData != null) {
                            smsSendDetailResult.setUserMarketingAccountName(userMarketingAccountData.getName());
                        }
                    }
                }
                Long userMarketingLookUpCount = userMarketingLookUpCountMap.get(smsSendDetailResult.getUserMarketingAccountId());
                if (userMarketingLookUpCount != null) {
                    smsSendDetailResult.setLookUpCount(userMarketingLookUpCount);
                }
                Long userMarketingClueNum = userMarketingClueNumMap.get(smsSendDetailResult.getUserMarketingAccountId());
                if (userMarketingClueNum != null) {
                    smsSendDetailResult.setClueNum(userMarketingClueNum);
                }
            }
        });
    }


    private Map<String, Long> getUserMarketingLookUpCount(String ea, Integer fsUserId, String marketingActivityId, List<String> userMarketingIdList) {
        ObjectActionUserMarketingListArg listArg = new ObjectActionUserMarketingListArg();
        listArg.setMarketingActivityId(marketingActivityId);
        listArg.setEa(ea);
        listArg.setUserId(fsUserId);
        listArg.setUserMarketingIdList(userMarketingIdList);
        List<Integer> actionTypeList = Lists.newArrayList(NewActionTypeEnum.LOOK_UP.getActionType());
        listArg.setActionTypeList(actionTypeList);
        listArg.setPageNum(1);
        listArg.setPageSize(userMarketingIdList.size());
        // 查询埋点数据
        com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<ObjectActionUserMarketingListResult>>
                objectActionUserMarketingPage =  userMarketingStatisticService.getObjectActionUserMarketingList(listArg);
        Map<String, Long> userMarketingIdToLookUpCountMap = Maps.newHashMap();
        if (objectActionUserMarketingPage != null && objectActionUserMarketingPage.getData() != null && CollectionUtils.isNotEmpty(objectActionUserMarketingPage.getData().getData())) {
            for (ObjectActionUserMarketingListResult result : objectActionUserMarketingPage.getData().getData()) {
                userMarketingIdToLookUpCountMap.put(result.getUserMarketingId(), Long.valueOf(result.getCount()));
            }
        }
        return userMarketingIdToLookUpCountMap;
    }

    // 发送已存在未发送的短信
    @Override
    public Result<Void> sendDraftTask(SendDraftTaskArg arg) {
        MwSmsSendEntity smsSendEntity = sendDao.getSMSSendById(arg.getSmsSendId());
        if (null == smsSendEntity) {
            log.warn("SendServiceImpl.sendDraftTask smsSendEntity is null arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (!MwSendStatusEnum.NEW.getStatus().equals(smsSendEntity.getStatus())) {
            log.warn("SendServiceImpl.sendDraftTask status error smsSendEntity :{}", smsSendEntity);
            return Result.newError(SHErrorCode.SMS_UPDATE_OPPORTUNITY_ERROR);
        }

        String ea = smsSendEntity.getEa();
        String templateId = smsSendEntity.getTemplateId();
        if (!smsTemplateManager.isMwTemplate(ea, templateId)) {
            boolean checkSmsTemplateStatus = smsTemplateManager.checkSmsTemplateStatus(ea, templateId);
            if (!checkSmsTemplateStatus) {
                return Result.newError(SHErrorCode.SMS_TEMPLATE_STATUS_DENY);
            }
            smsSendEntity.setType(arg.getType());
            smsSendEntity.setScheduleTime(DateUtil.fromTimestamp(arg.getScheduleTime()));
            if (smsSendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType())) {
                smsSendEntity.setStatus(MwSendStatusEnum.SCHEDULE_SENDING.getStatus());
            } else {
                smsSendEntity.setStatus(MwSendStatusEnum.READY.getStatus());
            }
            sendDao.updateSendEntity(smsSendEntity);
            return Result.newSuccess();
        }

        // 只有审核通过的模板才能发送
        Result<MwSmsTemplateEntity> mwSmsTemplateEntityResult = mwTemplateManager.checkTemplateValid(ea, smsSendEntity.getTemplateId(), false);
        if (!mwSmsTemplateEntityResult.isSuccess()) {
            return Result.newError(mwSmsTemplateEntityResult.getErrCode(), mwSmsTemplateEntityResult.getErrMsg());
        }

        // 添加crm营销活动
        /*
        String marketingActivityId = createMarketingActivity(arg);
        if(StringUtils.isEmpty(marketingActivityId)) {
            log.warn("SendServiceImpl.sendDraftTask createMarketingActivity error arg:{}", arg);
            return Result.newError(SHErrorCode.KIS_MARKETING_ACTIVITY_ADD_FAILED);
        }

        boolean createConfigResult = createMarketingActivityExternalConfig(marketingActivityId, arg);
        if (!createConfigResult) {
            // 打印日志但正常返回
            log.warn("SendServiceImpl.sendDraftTask createMarketingActivityExternalConfig fail marketingActivityId:{} ea:{} sendId:{}", marketingActivityId, arg.getEa(), arg.getSmsSendId());
        }
         */
        smsSendEntity.setType(arg.getType());
        smsSendEntity.setScheduleTime(DateUtil.fromTimestamp(arg.getScheduleTime()));
        sendManager.reduceQuotaAndSend(smsSendEntity, false);
        if (!sendDao.updateSendEntity(smsSendEntity)) {
            // 打印日志但正常返回
            log.warn("SendServiceImpl.sendDraftTask updateSendTypeById fail ea:{} sendId:{} type:{}", arg.getEa(), arg.getSmsSendId(), arg.getType());
        }
        return Result.newSuccess();
    }

    @Override
    public String statusCallback(List<StatusCallbackArg> args) {
        log.info("SendServiceImpl statusCallback StatusCallbackArg:{}", args);
        //这是腾讯短信的回调接口，切换到梦网后没有用了
        /*if (args != null && CollectionUtils.isNotEmpty(args)) {
            args.stream().forEach(resultData -> {
                SMSSendDetailEntity sendDetailEntity = sendDao.querySMSSendDetailBySid(resultData.getSid());
                if (sendDetailEntity != null) {
                    sendDetailEntity.setReportStatus(resultData.getReport_status());
                    sendDetailEntity.setReportErrMsg(resultData.getErrmsg());
                    sendDetailEntity.setReportDescription(resultData.getDescription());
                    sendDao.updateSendDetailCallback(sendDetailEntity);
                }
            });

            return "{\"result\":0,\"errmsg\":\"OK\"}";
        }*/
        return "{\"result\":1,\"errmsg\":\"params error\"}";
    }

    @Override
    public Result<Boolean> deleteSMSSend(DeleteSMSArg arg) {
        try {
            MwSmsSendEntity smsSendEntity = sendDao.getSMSSendById(arg.getSmsSendId());
            if (null == smsSendEntity) {
                log.warn("SendServiceImpl.deleteSMSSend smsSendEntity is null arg:{}", arg);
                return Result.newError(SHErrorCode.NO_DATA);
            }
            if (!smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_FAIL.getStatus()) || !smsSendEntity.getStatus().equals(MwSendStatusEnum.NEW.getStatus())
                || !smsSendEntity.getStatus().equals(MwSendStatusEnum.DROP.getStatus()) || !smsSendEntity.getStatus().equals(MwSendStatusEnum.SEND_CANCEL.getStatus())) {
                log.warn("SendServiceImpl.deleteSMSSend status is error smsSendEntity :{}", smsSendEntity);
                return Result.newError(SHErrorCode.DELETE_SMS_STATUS_ERROR);
            }
            sendManager.deleteSend(arg.getSmsSendId());
        } catch (IllegalArgumentException e) {
            log.warn("SendServiceImpl.deleteSMSSend exception e:{} arg:{}", e, arg);
            return new Result<>(SHErrorCode.SUCCESS, false);
        }
        return new Result<>(SHErrorCode.SUCCESS, true);
    }

    @Override
    public Result<QuerySMSSendResult> getSMSSendById(String smsSendId) {
        MwSmsSendEntity smsSendEntity = sendDao.getSMSSendById(smsSendId);
        if (null == smsSendEntity) {
            log.warn("SendServiceImpl.getSmsSendById smsSendEntities is empty  smsSendId:{}", smsSendId);
            return Result.newError(SHErrorCode.NO_DATA);
        }

        QuerySMSSendResult querySMSSendResult = buildQuerySMSSendResult(smsSendEntity);
        if (smsSendEntity.getTemplateId() != null) {
            MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(smsSendEntity.getEa(), smsSendEntity.getTemplateId());
            if (templateEntity != null){
                querySMSSendResult.setTemplateStatus(templateEntity.getStatus());
                querySMSSendResult.setTemplateId(smsTemplateManager.convertToObjId(smsSendEntity.getEa(), templateEntity.getId()));
            }
        }
        return new Result(SHErrorCode.SUCCESS, querySMSSendResult);
    }

    @Override
    public Result<QuerySMSSendByIdForResetResult> getSMSSendHistoryByIdForReset(String smsSendId) {
        MwSmsSendEntity smsSendEntity = sendDao.getSMSSendById(smsSendId);
        if (null == smsSendEntity) {
            log.warn("SendServiceImpl.getSMSSendHistoryByIdForReset smsSendId:{}", smsSendId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        QuerySMSSendByIdForResetResult resetResult = new QuerySMSSendByIdForResetResult();
        MwSmsTemplateEntity templateEntity = smsTemplateManager.getSmsTemplate(smsSendEntity.getEa(), smsSendEntity.getTemplateId());
        if (templateEntity.getStatus() == MwTemplateStatusEnum.VALIDITY.getStatus()) {
            resetResult.setTemplateId(templateEntity.getId());
            resetResult.setType(smsSendEntity.getType());
        }
        if (templateEntity.getStatus() == MwTemplateStatusEnum.INVALID.getStatus()) {
            resetResult.setTemplateContent(templateEntity.getContent());
            resetResult.setTitle(templateEntity.getName());
            resetResult.setType(smsSendEntity.getType());
        }
        resetResult.setScheduleTime(smsSendEntity.getScheduleTime());
        return new Result<>(SHErrorCode.SUCCESS, resetResult);
    }

    @Override
    public Result<SMSGrayResult> gray(String ea) {
        if (StringUtils.isBlank(ea)) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        SMSGrayResult smsGrayResult = new SMSGrayResult();
        smsGrayResult.setStatus(SMSGrayStatusEnum.ACCEPT.getStatus());
        return new Result<>(SHErrorCode.SUCCESS, smsGrayResult);
    }

    @Override
    public Result<ShortUrlResult> getShortUrl(String longUrl) {
        try {
            if (shortUrlManager.isShortUrl(longUrl)){
                Optional<String> longUrlOption = shortUrlManager.getLongUrlByShortUrl(longUrl);
                ShortUrlResult urlResult = new ShortUrlResult();
                urlResult.setShortUrl(longUrl);
                urlResult.setLongUrl(longUrlOption.get());
                return Result.newSuccess(urlResult);
            }

            CreateShortUrlArg arg = new CreateShortUrlArg();
            arg.setUrl(longUrl);
            Optional<String> shortUrlResult = shortUrlManager.createShortUrl(arg);
            if (!shortUrlResult.isPresent()) {
                log.info("SendServiceImpl getShortUrl shortUrlResult is null,longUrl={}, shortUrlResult={}", longUrl, shortUrlResult);
                return Result.newError(SHErrorCode.UNKNOWN);
            }
            ShortUrlResult urlResult = new ShortUrlResult();
            urlResult.setShortUrl(shortUrlResult.get());
            urlResult.setLongUrl(longUrl);
            return Result.newSuccess(urlResult);
        } catch (Exception e) {
            log.info("SendServiceImpl getShortUrl shortUrlResult is exception, longUrl={}, exception={}", longUrl, e.fillInStackTrace());
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<GetSmsParamListResult> getSmsParamList(GetSmsParamListVO vo) {
        if (vo.getSceneType() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        GetSmsParamListResult listResult = new GetSmsParamListResult();
        listResult.setParamDescResults(smsParamManager.getParamDesc(vo.getEa(), vo.getSceneType()));
        return Result.newSuccess(listResult);
    }

    @Override
    public Result<GetSmsParamListResult> getSmsParamListV2(GetSmsParamListV2Arg vo) {
        List<SmsParamDescResult> smsParamDescResultList = smsParamManager.getParamDescByMarketingEventId(vo.getEa(), vo.getMarketingEventId());
        GetSmsParamListResult listResult = new GetSmsParamListResult();
        listResult.setParamDescResults(smsParamDescResultList);
        return Result.newSuccess(listResult);
    }
    @Override
    public Result<PageResult<ListSmsSendDetailResult>> listSmsSendDetail(ListSmsSendDetailArg vo) {
        PageResult<ListSmsSendDetailResult> pageResult = new PageResult();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        if (StringUtils.isEmpty(vo.getKeyStr())) {
            vo.setKeyStr(null);
        }
        List<SmsSendDetailDTO> sendDetail2Page;
        if (StringUtils.isBlank(vo.getKeyStr())) {
            sendDetail2Page = sendDao.getSendDetail2PageV3(vo.getEa(), vo.getStatus(), vo.getChannelType(), vo.getStartTime(), vo.getEndTime(), page);
            if (CollectionUtils.isEmpty(sendDetail2Page)) {
                return Result.newSuccess(pageResult);
            }
            List<String> templateIdList = sendDetail2Page.stream().map(SmsSendDetailDTO::getTemplateId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            Map<String, String> templateIdToNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(templateIdList)) {
                List<MwSmsTemplateEntity> templateEntityList = templateDAO.getTemplateByNameIdList(templateIdList);
                templateEntityList.forEach(e -> templateIdToNameMap.put(e.getId(), e.getName()));
            }

            for (SmsSendDetailDTO smsSendDetailDTO : sendDetail2Page) {
                if (StringUtils.isNotEmpty(templateIdToNameMap.get(smsSendDetailDTO.getTemplateId()))) {
                    smsSendDetailDTO.setTemplateName(templateIdToNameMap.get(smsSendDetailDTO.getTemplateId()));
                }
                if (StringUtils.isNotEmpty(smsSendDetailDTO.getErrCode())) {
                    smsSendDetailDTO.setErrCodeDesc(MW_CODE_MAP.get(smsSendDetailDTO.getErrCode()));
                }
            }
        } else {
            sendDetail2Page = sendDao.getSendDetail2Page(vo.getEa(), vo.getStatus(), vo.getChannelType(), vo.getStartTime(), vo.getEndTime(), vo.getKeyStr(), page);
        }
        if (CollectionUtils.isEmpty(sendDetail2Page)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        List<ListSmsSendDetailResult> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);
        sendDetail2Page.forEach(smsSendDetailDTO -> {
            ListSmsSendDetailResult detailResult = new ListSmsSendDetailResult();
            detailResult.setTemplateName(smsSendDetailDTO.getTemplateName());
            detailResult.setContent(smsSendDetailDTO.getContent());
            detailResult.setPhone(smsSendDetailDTO.getPhone());
            detailResult.setChannelType(smsSendDetailDTO.getChannelType());
            detailResult.setChannelName(ChannelTypeEnum.getChannelType(smsSendDetailDTO.getChannelType()).getName());
            detailResult.setStatus(smsSendDetailDTO.getStatus());
            detailResult.setSendTime(smsSendDetailDTO.getCreateTime().getTime());

            String errCodeDesc = smsSendDetailDTO.getErrCodeDesc();
            if (StringUtils.isEmpty(errCodeDesc)) {
                errCodeDesc = MwErrorCodeMapConstants.getStringForResultCode(smsSendDetailDTO.getErrCode());
            }
            detailResult.setErrCodeDesc(errCodeDesc);
            resultList.add(detailResult);
        });
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Boolean> cancelSendSms(String smsSendId, String ea) {
        if (Strings.isNullOrEmpty(smsSendId)) {
            return Result.newSuccess(false);
        }
        MwSmsSendEntity mwSmsSendEntity = sendDao.getSMSSendById(smsSendId);
        if (mwSmsSendEntity == null) {
            return Result.newSuccess(false);
        }
        //只有定时任务才可以取消发送
        if (!mwSmsSendEntity.getType().equals(MwSendTaskTypeEnum.SCHEDULE_SEND.getType()) || !mwSmsSendEntity.getStatus().equals(MwSendStatusEnum.SCHEDULE_SENDING.getStatus())) {
            return Result.newSuccess(false);
        }
        //取消定时任务
        boolean cancelResult = sendManager.cancelSendSms(smsSendId, ea);
        if (!cancelResult) {
            return Result.newSuccess(false);
        }
        return Result.newSuccess(cancelResult);
    }

    @Override
    public Result<MwSmsTemplateResult> getPresetTemplate(String ea, Integer sceneType) {
        Optional<MwSmsTemplateEntity> presetTemplate = mwTemplateManager.getPresetTemplate(ea, sceneType);
        if (presetTemplate.isPresent()) {
            MwSmsTemplateEntity entity = presetTemplate.get();
            entity.setContent(smsParamManager.getShowParamNameTemplate(entity.getContent(), entity.getParamDetail()));
            return Result.newSuccess(new MwSmsTemplateResult(entity.getId(), entity.getName(), entity.getContent()));
        }
        return Result.newError(SHErrorCode.SMS_TEMPLATE_NOT_EXIST);
    }

    @Override
    public Result<List<ListSmsTemplateResult>> listSmsTemplate(ListSmsTemplateVo vo) {
        String ea = vo.getEa();
        if (vo.getTenantId() != null && StringUtils.isBlank(ea)) {
            ea = eIEAConverter.enterpriseIdToAccount(vo.getTenantId());
        }
        List<MwSmsTemplateEntity> mwSmsTemplateEntities = templateDAO.listSmsTemplate(ea, vo.getApiName(), vo.getFilter());
        List<ListSmsTemplateResult> listSmsTemplateResults = mwSmsTemplateEntities.stream().map(mwSmsTemplateEntity -> {
            ListSmsTemplateResult result = new ListSmsTemplateResult();
            result.setId(mwSmsTemplateEntity.getId());
            result.setName(mwSmsTemplateEntity.getName());
            result.setContent(mwSmsTemplateEntity.getContent());
            result.setRelationApiName(mwSmsTemplateEntity.getRelationApiName());
            result.setRemark(mwSmsTemplateEntity.getRemark());
            result.setSmsContentParam(mwSmsTemplateEntity.getSmsContentParam());
            return result;
        }).collect(Collectors.toList());
        return Result.newSuccess(listSmsTemplateResults);
    }

    @Override
    public Result<GetSmsTemplateResult> getSmsTemplate(GetSmsTemplateVo vo) {
        vo.setTemplateId(smsTemplateManager.objIdConvertToDbId(vo.getEa(), vo.getTemplateId()));
        MwSmsTemplateEntity templateEntity = templateDAO.getTemplateById(vo.getTemplateId());
        if (Objects.isNull(templateEntity)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        GetSmsTemplateResult result = new GetSmsTemplateResult();
        result.setId(templateEntity.getId());
        result.setName(templateEntity.getName());
        result.setContent(templateEntity.getContent());
        result.setRelationApiName(templateEntity.getRelationApiName());
        result.setRemark(templateEntity.getRemark());
        result.setSmsContentParam(templateEntity.getSmsContentParam());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateSmsDetail(UpdateSmsDetailArg arg) {
        String detailId = arg.getDetailId();
        if (org.apache.commons.lang3.StringUtils.isBlank(detailId)) {
            return Result.newSuccess();
        }

        MwSendDetailEntity mwSendDetailEntity = sendDao.getDetailById(detailId);
        if (mwSendDetailEntity == null) {
            return Result.newSuccess();
        }

        Integer status;
        if (org.apache.commons.lang3.StringUtils.equals(arg.getStatus(), "success")) {
            status = MwSendDetailStatusEnum.SEND_SUCCESSFUL.getStatus();
        } else {
            status = MwSendDetailStatusEnum.SEND_FAIL.getStatus();
        }
        sendDao.updateSendDetailById(status, arg.getReply(), detailId);

        if (Objects.equals(status, mwSendDetailEntity.getStatus())) {
            // 状态无变化，无需处理，防止重复消息过来
            return Result.newSuccess();
        }
        if (Objects.equals(status, MwSendDetailStatusEnum.SEND_SUCCESSFUL.getStatus())) {
            sendDao.incrementActualSenderCount(mwSendDetailEntity.getSendId());
        }
        return Result.newSuccess();
    }
}
