package com.facishare.marketing.provider.service.hexagon;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.arg.hexagon.*;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.data.MicrostationDecorationTemplateConfig;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.hexagon.*;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.service.hexagon.HexagonTemplateService;
import com.facishare.marketing.common.contstant.DisplayOrderConstants;
import com.facishare.marketing.common.contstant.material.MaterialTypeEnum;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.CustomizeMiniAppNavbarForwardEnum;
import com.facishare.marketing.common.enums.CustomizeMiniAppNavbarStatusEnum;
import com.facishare.marketing.common.enums.FormDataUsage;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonTemplateDefaultGroupEnum;
import com.facishare.marketing.common.enums.hexagon.ListHexagonGroupUseTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.NestedIdList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.hexagon.HexagonTemplateSiteQueryParam;
import com.facishare.marketing.provider.dto.ContentGroupCountDTO;
import com.facishare.marketing.provider.dto.GroupNameObjectIdDTO;
import com.facishare.marketing.provider.dto.ObjectRelationGroupDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("hexagonTemplateService")
public class HexagonTemplateServiceImpl implements HexagonTemplateService {
    @Autowired
    private ObjectGroupManager objectGroupManager;
    @Autowired
    private ObjectGroupDAO objectGroupDAO;
    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;
    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;
    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private DisplayOrderDao displayOrderDao;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;
    @Autowired
    private MemberConfigDao memberConfigDao;
    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;
    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private CustomizeMiniAppNavbarDAO customizeMiniAppNavbarDAO;
    @Autowired
    private CustomizeMiniAppNavbarConfigDAO customizeMiniAppNavbarConfigDAO;

    @Autowired
    MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    /**
     * 微站装修模板配置
     */
    @ReloadableProperty("microstation.decoration.template.config")
    private String templateConfig;

    //管理端查询所有分组，包括默认分组
    private Result<ListHexagonTemplateGroupResult> listHexagonTemplateGroupByAll(String ea, Integer fsUserId, ListHexagonTemplateGroupArg arg){
        List<ListObjectGroupResult> resultList = Lists.newArrayList();
        setDefaultGroupList(resultList);
        ListHexagonTemplateGroupResult result = new ListHexagonTemplateGroupResult();
        result.setObjectGroupList(resultList);

        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
        List<String> groupIds = null;
        if (CollectionUtils.isNotEmpty(objectGroupEntityList)){
            groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.HEXAGON_TEMPLATE_GROUP_DISPLAY_KEY);
            if (templateGroupDisplayOrder != null) {
                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
                result.setSortVersion(templateGroupDisplayOrder.getVersion());
            }
            objectGroupEntityList.forEach(groupResult ->{
                ListObjectGroupResult objectGroup = new ListObjectGroupResult();
                objectGroup.setGroupName(groupResult.getName());
                objectGroup.setGroupId(groupResult.getId());
                resultList.add(objectGroup);
            });

        }

        Map<String, Integer> contentGroupCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<ContentGroupCountDTO> contentGroupCountDTOList = objectGroupRelationDAO.queryEntityByGroups(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), groupIds);
            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)){
                contentGroupCountMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1, v2)->v2));
            }
        }
        for (ListObjectGroupResult objectGroup : resultList){
            if (StringUtils.equals(objectGroup.getGroupId(), HexagonTemplateDefaultGroupEnum.ALL.getId())){
                objectGroup.setObjectCount(hexagonTemplateSiteDAO.queryHexagonTemplateCountByAll(ea, arg.getStatus(), arg.getKeyword()));
                objectGroup.setSystem(true);
            }else if (StringUtils.equals(objectGroup.getGroupId(), HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getId())){
                objectGroup.setObjectCount(hexagonTemplateSiteDAO.queryHexagonTemplateCountCreateByMe(ea, fsUserId, arg.getStatus(), arg.getKeyword()));
                objectGroup.setSystem(true);
            }else if (StringUtils.equals(objectGroup.getGroupId(), HexagonTemplateDefaultGroupEnum.NO_GROUP.getId())){
                objectGroup.setObjectCount(hexagonTemplateSiteDAO.queryHexagonTemplateCountByUnGrouped(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), arg.getStatus(), arg.getKeyword()));
                objectGroup.setSystem(true);
            }else {
                if (contentGroupCountMap == null || contentGroupCountMap.get(objectGroup.getGroupId()) == null){
                    objectGroup.setObjectCount(0);
                }else {
                    objectGroup.setObjectCount(contentGroupCountMap.get(objectGroup.getGroupId()));
                }
            }
        }

        return Result.newSuccess(result);
    }

    //查询所有分组&包含分组内容
    private Result<ListHexagonTemplateGroupResult>  listHexagonTemplateGroupByContainContent (String ea, Integer fsUserId, ListHexagonTemplateGroupArg arg){
        ListHexagonTemplateGroupResult validGroupResult = new ListHexagonTemplateGroupResult();
        List<ListObjectGroupResult> validGroupList = Lists.newArrayList();
        validGroupResult.setObjectGroupList(validGroupList);
        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
        if (CollectionUtils.isNotEmpty(objectGroupEntityList)){
            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.HEXAGON_TEMPLATE_GROUP_DISPLAY_KEY);
            if (templateGroupDisplayOrder != null) {
                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
                validGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
            }

            List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            List<ContentGroupCountDTO> contentGroupCountDTOList = objectGroupRelationDAO.queryHexagonTemplateEntityDTOByGroups(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), groupIds);
            Map<String, Integer> groupCountDTOMap = null;
            if (CollectionUtils.isNotEmpty(contentGroupCountDTOList)){
                groupCountDTOMap = contentGroupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, ContentGroupCountDTO::getCount, (v1,v2)->v2));
            }

            for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList){
                if (groupCountDTOMap != null && groupCountDTOMap.get(objectGroupEntity.getId()) != null && groupCountDTOMap.get(objectGroupEntity.getId()).intValue() > 0 ){
                    ListObjectGroupResult objectGroup = new ListObjectGroupResult();
                    objectGroup.setObjectCount(groupCountDTOMap.get(objectGroupEntity.getId()).intValue());
                    objectGroup.setGroupName(objectGroupEntity.getName());
                    objectGroup.setGroupId(objectGroupEntity.getId());
                    validGroupList.add(objectGroup);
                }
            }
        }

        //未分组
        ListObjectGroupResult ungroup = new ListObjectGroupResult();
        ungroup.setGroupId(HexagonTemplateDefaultGroupEnum.NO_GROUP.getId());
        ungroup.setGroupName(HexagonTemplateDefaultGroupEnum.NO_GROUP.getName());
        ungroup.setSystem(false);
        ungroup.setObjectCount(hexagonTemplateSiteDAO.queryHexagonTemplateCountByUnGrouped(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), arg.getStatus(), arg.getKeyword()));
        validGroupList.add(ungroup);

        //系统分组
        ListObjectGroupResult systemGroup = new ListObjectGroupResult();
        systemGroup.setGroupId(HexagonTemplateDefaultGroupEnum.SYSTEM.getId());
        systemGroup.setGroupName(HexagonTemplateDefaultGroupEnum.SYSTEM.getName());
        systemGroup.setSystem(true);
        Integer langType = StringUtils.equals(I18nUtil.getLanguage(), I18nUtil.EN) ? 1 : 0;
        systemGroup.setObjectCount(hexagonTemplateSiteDAO.queryHexagonTemplateCountBySystem(arg.getStatus(), arg.getKeyword(), langType));
        validGroupList.add(systemGroup);

        return Result.newSuccess(validGroupResult);
    }

    //查询用户自定义的分组
    private Result<ListHexagonTemplateGroupResult> listHexagonTemplateGroupByCustome(String ea, Integer fsUserId, ListHexagonTemplateGroupArg arg){
        ListHexagonTemplateGroupResult hexagonTemplateGroupResult = new ListHexagonTemplateGroupResult();
        List<ObjectGroupEntity> objectGroupEntityList = objectGroupDAO.listGroupByEa(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
        if (CollectionUtils.isEmpty(objectGroupEntityList)){
            return Result.newSuccess(hexagonTemplateGroupResult);
        }

        List<ListObjectGroupResult> result = Lists.newArrayList();
        hexagonTemplateGroupResult.setObjectGroupList(result);
        if (CollectionUtils.isNotEmpty(objectGroupEntityList)) {
            DisplayOrderEntity templateGroupDisplayOrder = displayOrderDao.getDisplayOrder(ea, DisplayOrderConstants.HEXAGON_TEMPLATE_GROUP_DISPLAY_KEY);
            if (templateGroupDisplayOrder != null) {
                NestedIdList.sortByNestedIds(templateGroupDisplayOrder.getDisplayItems(), objectGroupEntityList, ObjectGroupEntity::getId);
                hexagonTemplateGroupResult.setSortVersion(templateGroupDisplayOrder.getVersion());
            }
        }
        List<String> groupIds = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
        List<ContentGroupCountDTO> groupCountDTOList = objectGroupRelationDAO.queryEntityByGroups(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), groupIds);
        Map<String, ContentGroupCountDTO> groupCountDTOMap = null;
        if (CollectionUtils.isNotEmpty(groupCountDTOList)){
            groupCountDTOMap = groupCountDTOList.stream().collect(Collectors.toMap(ContentGroupCountDTO::getGroupId, Function.identity(), (v1, v2)->v2));
        }

        for (ObjectGroupEntity objectGroupEntity : objectGroupEntityList){
            ListObjectGroupResult groupResult = new ListObjectGroupResult();
            groupResult.setGroupId(objectGroupEntity.getId());
            groupResult.setGroupName(objectGroupEntity.getName());
            if (groupCountDTOMap != null && groupCountDTOMap.get(objectGroupEntity.getId()) != null) {
                groupResult.setObjectCount(groupCountDTOMap.get(objectGroupEntity.getId()).getCount());
            }
            result.add(groupResult);
        }

        return Result.newSuccess(hexagonTemplateGroupResult);
    }

    @Override
    public Result<ListHexagonTemplateGroupResult> listHexagonTemplateGroup(String ea, Integer fsUserId, ListHexagonTemplateGroupArg arg) {
        if (arg.getUseType() == ListHexagonGroupUseTypeEnum.MANAGE.getType()){
            return listHexagonTemplateGroupByAll(ea, fsUserId, arg);
        }else if  (arg.getUseType() == ListHexagonGroupUseTypeEnum.CUSTOME.getType()){
            return listHexagonTemplateGroupByCustome(ea, fsUserId, arg);
        }else {
            return listHexagonTemplateGroupByContainContent(ea, fsUserId, arg);
        }
    }


    @Override
    public Result<PageResult<ListHexagonTemplateByGroupResult>> listHexagonTemplateByGroup(String ea, Integer fsUserId, ListHexagonTemplateByGroupArg arg) {
        PageResult<ListHexagonTemplateByGroupResult> pageResult = new PageResult<>();
        List<ListHexagonTemplateByGroupResult> templateResultList = Lists.newArrayList();
        pageResult.setResult(templateResultList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        if (arg.getGroupId() == null){
            arg.setGroupId(HexagonTemplateDefaultGroupEnum.ALL_GROUP.getId());
        }
        boolean isAdmin = objectGroupManager.isAppAdmin(ea, fsUserId);

        List<HexagonTemplateSiteEntity> pageList = null;
        HexagonTemplateSiteQueryParam param = new HexagonTemplateSiteQueryParam();
        param.setKeyword(arg.getKeyword());
        param.setEa(ea);
        param.setStatus(arg.getStatus());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        if (arg.getGroupId().equals(HexagonTemplateDefaultGroupEnum.ALL.getId())){
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
            param.setUserId(fsUserId);
            param.setPermissionGroupIdList(com.google.common.collect.Lists.newArrayList(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList())));
            pageList = hexagonTemplateSiteDAO.getAccessiblePage(param, page);
        }else if (arg.getGroupId().equals(HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getId())){
            pageList = hexagonTemplateSiteDAO.pageQueryHexagonTemplateCreateByMe(ea, fsUserId, arg.getKeyword(), arg.getStatus(), page);
        }else if (arg.getGroupId().equals(HexagonTemplateDefaultGroupEnum.NO_GROUP.getId())){
            pageList = hexagonTemplateSiteDAO.pageQueryHexagonTemplateByUnGrouped(ea, arg.getKeyword(), fsUserId, arg.getStatus(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), page);
        }else if(arg.getGroupId().equals(HexagonTemplateDefaultGroupEnum.SYSTEM.getId())){
            Integer langType = StringUtils.equals(I18nUtil.getLanguage(), I18nUtil.EN) ? 1 : 0;
            pageList = hexagonTemplateSiteDAO.pageQueryHexagonTemplateBySystem(arg.getKeyword(), arg.getStatus(), langType, page);
        } else if(arg.getGroupId().equals(HexagonTemplateDefaultGroupEnum.ALL_GROUP.getId())){
          //拉所有分类数据
            Integer langType = StringUtils.equals(I18nUtil.getLanguage(), I18nUtil.EN) ? 1 : 0;
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
            param.setUserId(fsUserId);
            param.setPermissionGroupIdList(com.google.common.collect.Lists.newArrayList(objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList())));
            param.setLangType(langType);
            pageList = hexagonTemplateSiteDAO.pageQueryHexagonTemplateByAllGroup(param, page);
        }else {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            param.setUserId(fsUserId);
            if (!permissionGroupIdSet.contains(arg.getGroupId())) {
                pageList = com.google.common.collect.Lists.newArrayList();
            } else {
                param.setPermissionGroupIdList(com.google.common.collect.Lists.newArrayList(permissionGroupIdSet));
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), arg.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(arg.getGroupId());
                param.setGroupIdList(accessibleSubGroupIdList);
                pageList = hexagonTemplateSiteDAO.getAccessiblePage(param, page);
            }
        }
        List<String> hexagonTemplateIds = pageList.stream().map(HexagonTemplateSiteEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hexagonTemplateIds)){
            return Result.newSuccess(pageResult);
        }

        pageResult.setTotalCount(page.getTotalNum());
        List<GroupNameObjectIdDTO> groupNameObjectIdDTOList = objectGroupRelationDAO.queryGroupNameByObjectIds(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), hexagonTemplateIds);
        Map<String, GroupNameObjectIdDTO> objectIdDTOMap = null;
        if (CollectionUtils.isNotEmpty(groupNameObjectIdDTOList)){
            objectIdDTOMap = groupNameObjectIdDTOList.stream().collect(Collectors.toMap(GroupNameObjectIdDTO::getObjectId, Function.identity(), (v1, v2)->v2));
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> allFSEmployeeMsgMap = null;
        List<Integer> userIdList = Lists.newArrayList();
        for (HexagonTemplateSiteEntity hexagonTemplateSiteEntity : pageList){
            if (!(hexagonTemplateSiteEntity.getEa() == null && hexagonTemplateSiteEntity.getType() == 1)){
                userIdList.add(hexagonTemplateSiteEntity.getCreateBy());
            }
        }
        if (CollectionUtils.isNotEmpty(userIdList)) {
            allFSEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIdList, true);
        }
        
        List<HexagonSiteListDTO> hexagonTemplateDTO = hexagonTemplateSiteDAO.getCoverByTemplateSiteIds(hexagonTemplateIds);
        Map<String, String> hexagonTemplateUrlMap = new HashMap<>();
        Map<String, HexagonSiteListDTO> hexagonTempalteDTOMap = null;
        Map<String, String> hexagonTemplateIdApathMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hexagonTemplateDTO)){
            hexagonTempalteDTOMap = hexagonTemplateDTO.stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, Function.identity(), (v1,v2)->v2));
        }

        //系统模板封面
        List<String> systemHexagonTemplateSiteCoverPaths = Lists.newArrayList();
        List<String> customHexagonTemplateSiteCoverPaths = Lists.newArrayList();
        for (HexagonTemplateSiteEntity templateSiteEntity : pageList){
            String coverPath = null;
            if (StringUtils.isNotEmpty(templateSiteEntity.getCoverApath())){
                coverPath = templateSiteEntity.getCoverApath();
            }else if (hexagonTempalteDTOMap != null){
                HexagonSiteListDTO dto = hexagonTempalteDTOMap.get(templateSiteEntity.getId());
                if (dto != null && StringUtils.isNotEmpty(dto.getSharePicH5Apath())) {
                    coverPath = dto.getSharePicH5Apath();
                }
            }

            if (coverPath != null){
                hexagonTemplateIdApathMap.put(templateSiteEntity.getId(), coverPath);
            }
            if (templateSiteEntity.getEa() == null && templateSiteEntity.getType() == 1){
                //系统预置模板
                if (coverPath != null) {
                    systemHexagonTemplateSiteCoverPaths.add(coverPath);
                }
            }else {
                if (coverPath != null) {
                    customHexagonTemplateSiteCoverPaths.add(coverPath);
                }
            }
        }
        Map<String, String> systemUrlMap = fileV2Manager.batchGetUrlByPath(systemHexagonTemplateSiteCoverPaths, ea, false);
        Map<String, String> customUrlMap = fileV2Manager.batchGetUrlByPath(customHexagonTemplateSiteCoverPaths, ea, false);
        for (HexagonTemplateSiteEntity templateSiteEntity : pageList){
            String apath = hexagonTemplateIdApathMap.get(templateSiteEntity.getId());
            if (systemUrlMap != null && systemUrlMap.get(apath) != null){
                hexagonTemplateUrlMap.put(templateSiteEntity.getId(), systemUrlMap.get(apath));
            }else if(customUrlMap != null && customUrlMap.get(apath) != null){
                hexagonTemplateUrlMap.put(templateSiteEntity.getId(), customUrlMap.get(apath));
            }
        }

        Map<String, SitePreviewResult> previewUrlMap = hexagonManager.batchGetHexagonSiteH5PreviewQRCodeURL(ea, hexagonTemplateIds);
        for (HexagonTemplateSiteEntity templateSiteEntity : pageList){
            ListHexagonTemplateByGroupResult item = new ListHexagonTemplateByGroupResult();
            if (isAdmin || templateSiteEntity.getCreateBy().equals(fsUserId)){
                item.setEditable(true);
            }else{
                item.setEditable(false);
            }
            item.setTemplateId(templateSiteEntity.getId());
            item.setTemplateName(templateSiteEntity.getName());
            item.setStatus(templateSiteEntity.getStatus());
            if (templateSiteEntity.getEa() == null){
                item.setGroupName(HexagonTemplateDefaultGroupEnum.SYSTEM.getName());
                item.setGroupId(HexagonTemplateDefaultGroupEnum.SYSTEM.getId());
            }else if (objectIdDTOMap == null || objectIdDTOMap.get(templateSiteEntity.getId()) == null){
                item.setGroupName(HexagonTemplateDefaultGroupEnum.NO_GROUP.getName());
                item.setGroupId(HexagonTemplateDefaultGroupEnum.NO_GROUP.getId());
            }else {
                item.setGroupName(objectIdDTOMap.get(templateSiteEntity.getId()).getGroupName());
                item.setGroupId(objectIdDTOMap.get(templateSiteEntity.getId()).getGroupId());
            }
            item.setCreateTime(templateSiteEntity.getCreateTime().getTime());
            if (hexagonTempalteDTOMap != null && hexagonTempalteDTOMap.get(templateSiteEntity.getId()) != null){
                item.setCoverAPath(hexagonTempalteDTOMap.get(templateSiteEntity.getId()).getSharePicH5Apath());
            }

            if (hexagonTemplateUrlMap != null && hexagonTemplateUrlMap.get(templateSiteEntity.getId()) != null){
                item.setCoverUrl(hexagonTemplateUrlMap.get(templateSiteEntity.getId()));
            }
            if (allFSEmployeeMsgMap != null && allFSEmployeeMsgMap.get(templateSiteEntity.getCreateBy()) != null){
                item.setCreator(allFSEmployeeMsgMap.get(templateSiteEntity.getCreateBy()).getName());
            }
            //预览
            if (previewUrlMap != null && previewUrlMap.get(templateSiteEntity.getId()) != null){
                item.setH5QRUrl(previewUrlMap.get(templateSiteEntity.getId()).getH5QRUrl());
            }else {
                Result<SitePreviewResult> sitePreviewResult = hexagonManager.getHexagonSiteQRCodeURL(templateSiteEntity.getId(), ea, null, null);
                if (sitePreviewResult.isSuccess() && sitePreviewResult.getData() != null){
                    item.setH5QRUrl(sitePreviewResult.getData().getH5QRUrl());
                }
            }
            templateResultList.add(item);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<ListObjectGroupResult>> listHexagonTemplateManageGroup(String ea, Integer fsUserId, ListHexagonTemplateManageGroupArg arg) {
        PageResult<ListObjectGroupResult> pageResult = new PageResult<>();
        List<ListObjectGroupResult> templateResultList = Lists.newArrayList();
        pageResult.setResult(templateResultList);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<ObjectRelationGroupDTO> groupRelationList = objectGroupDAO.pageListRelationGroupEntity(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), page);
        if (CollectionUtils.isEmpty(groupRelationList)){
            return Result.newSuccess(pageResult);
        }

        for (ObjectRelationGroupDTO dto : groupRelationList){
            ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
            objectGroupResult.setGroupId(dto.getGroupId());
            objectGroupResult.setGroupName(dto.getGroupName());
            objectGroupResult.setObjectCount(dto.getObjectCount());
            templateResultList.add(objectGroupResult);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<EditObjectGroupResult> editTemplateGroup(String ea, Integer fsUserId, EditHexagonTemplateGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonTemplateServiceImpl.editTemplateGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = HexagonTemplateDefaultGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("HexagonTemplateServiceImpl.editGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }

         return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
    }


    @Override
    @Transactional
    public Result deleteTemplateGroup(String ea, Integer fsUserId, DeleteHexagonGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonTemplateServiceImpl.deleteTemplateGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
    }

    @Override
    public Result stopTemplate(String ea, Integer fsUserId, String templateId) {
        /*
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonTemplateServiceImpl.stopTemplate only allow appAdmin ea:{} fsUserId:{}, templateId:{}", ea, fsUserId, templateId);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        */

        HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.getById(templateId);
        if (null == queryHexagonTemplateSiteEntity) {
            log.warn("HexagonTemplateService.deleteTemplate faile hexagonTemplateSiteEntity not found, id={}", templateId);
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        hexagonTemplateSiteDAO.updateTemplateStatus(HexagonStatusEnum.STOPED.getType(), templateId);
        return Result.newSuccess();
    }

    @Override
    @Transactional
    public Result deleteTemplate(String ea, Integer fsUserId, String templateId) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("HexagonTemplateServiceImpl.deleteTemplate only allow appAdmin ea:{} fsUserId:{}, templateId:{}", ea, fsUserId, templateId);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }

        HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.getById(templateId);
        if (null == queryHexagonTemplateSiteEntity) {
            log.warn("HexagonTemplateService.deleteTemplate faile hexagonTemplateSiteEntity not found, id={}", templateId);
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        //需要删除文件系统中图片封面
        hexagonTemplateSiteDAO.deleteById(templateId);
        hexagonTemplatePageDAO.deleteBySiteId(templateId);
        objectGroupRelationDAO.deleteObjectFromObjectGroupRelation(ea, templateId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId,
                ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), queryHexagonTemplateSiteEntity.getName(), OperateTypeEnum.DELETE);
        return Result.newSuccess();
    }

    @Override
    public Result setTemplateGroup(String ea, Integer fsUserId, SetTemplateGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("HexagonTemplateServiceImpl.setTemplateGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }

        HexagonTemplateSiteEntity hexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getHexagonTemplateId());
        if (hexagonTemplateSiteEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }

        ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(ea, arg.getHexagonTemplateId());
        if (objectGroupRelationEntity == null){
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(arg.getHexagonTemplateId());
            newEntity.setObjectType(ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
            objectGroupRelationDAO.insert(newEntity);
        }else {
            objectGroupRelationDAO.updateObjectGroup(ea, arg.getGroupId(), arg.getHexagonTemplateId(), ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
        }

        return Result.newSuccess();
    }

    @Override
    @Transactional
    public Result<CreateSiteResult> copyTemplate(String ea, Integer fsUserId, HexagonCopyArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("HexagonTemplateServiceImpl.copyTemplate only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        if (hexagonTemplateSiteDAO.queryCountByName(ea, arg.getName()) > 0) {
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NAME_EXIST);
        }
        Result<CreateSiteResult> result = hexagonSiteManager.copyTemplateToTemplate(ea, fsUserId, arg);
        createMktContentLog(ea, fsUserId, arg);
        return result;
    }

    private void createMktContentLog(String ea, Integer fsUserId, HexagonCopyArg arg) {
        String originName;
        if(arg.getSource() != null && arg.getSource() == 2){
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getByIdIngnoreStatus(arg.getId());
            originName = hexagonSiteEntity == null ? "" : hexagonSiteEntity.getName();
        }else{
            HexagonTemplateSiteEntity hexagonTemplateSiteEntity = hexagonTemplateSiteDAO.getById(arg.getId());
            originName = hexagonTemplateSiteEntity == null ? "" : hexagonTemplateSiteEntity.getName();
        }
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), originName,
                OperateTypeEnum.COPY, MaterialTypeEnum.HEXAGON_SITE_TEMPLATE.getName(), arg.getName());
    }

    @Override
    public Result updateTemplateSiteStatus(String ea, Integer fsUserId, ChangeTemplateSiteArg arg) {
        HexagonStatusEnum hexagonStatusEnum = HexagonStatusEnum.getByType(arg.getStatus());
        if (hexagonStatusEnum == null || hexagonStatusEnum == HexagonStatusEnum.DELETED) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);

        }

        HexagonTemplateSiteEntity queryHexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getId());
        if (null == queryHexagonTemplateSiteEntity) {
            log.warn("HexagonTemplateServiceImpl.updateTemplateSiteStatus queryHexagonTemplateSiteEntity not found ,ea:{} id={}", ea, arg.getId());
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        int updateTemplateResut = hexagonTemplateSiteDAO.updateTemplateStatus(arg.getStatus(), arg.getId());
        if (updateTemplateResut != 1) {
            log.error("HexagonTemplateServiceImpl.updateTemplateSiteStatus update failed, entity={}", queryHexagonTemplateSiteEntity);
        }
        if (arg.getStatus() == 2) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId,
                    ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), queryHexagonTemplateSiteEntity.getName(), OperateTypeEnum.SET_TO_STOP);
        }
        return Result.newSuccess();
    }

    private void setDefaultGroupList( List<ListObjectGroupResult> groupList){
        //全部
        ListObjectGroupResult allGroup = new ListObjectGroupResult();
        allGroup.setGroupId(HexagonTemplateDefaultGroupEnum.ALL.getId());
        allGroup.setGroupName(HexagonTemplateDefaultGroupEnum.ALL.getName());
        groupList.add(allGroup);

        //我创建的
        ListObjectGroupResult groupCreateByme = new ListObjectGroupResult();
        groupCreateByme.setGroupId(HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getId());
        groupCreateByme.setGroupName(HexagonTemplateDefaultGroupEnum.CREATED_BY_ME.getName());
        groupList.add(groupCreateByme);

        //未分类
        ListObjectGroupResult ungroup = new ListObjectGroupResult();
        ungroup.setGroupId(HexagonTemplateDefaultGroupEnum.NO_GROUP.getId());
        ungroup.setGroupName(HexagonTemplateDefaultGroupEnum.NO_GROUP.getName());
        groupList.add(ungroup);
    }

    @Override
    public Result<GetSiteByEaUnitResult> getTemplateByTemplateId(String ea, Integer fsUserId, GetSiteByIdArg arg) {
        HexagonTemplateSiteEntity hexagonTemplateSiteEntity = hexagonTemplateSiteDAO.queryByIdIgnoreStatus(arg.getId());
        if (null == hexagonTemplateSiteEntity) {
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        if (!hexagonTemplateSiteEntity.getEa().equals(ea)) {
            return Result.newError(SHErrorCode.HEXAGON_CANT_MODIFITY_OTHER_EA);
        }

        List<Integer> userIds = Lists.newArrayList();
        userIds.add(hexagonTemplateSiteEntity.getCreateBy());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);

        Map<String, String> coverApathMap = new HashMap<>();
        List<String> apathList = new ArrayList<>();
        if (StringUtils.isNotEmpty(hexagonTemplateSiteEntity.getCoverApath())){
            apathList.add(hexagonTemplateSiteEntity.getCoverApath());
            coverApathMap.put(hexagonTemplateSiteEntity.getId(), hexagonTemplateSiteEntity.getCoverApath());
        }else {
            HexagonSiteListDTO hexagonSiteCoverDTO = hexagonTemplateSiteDAO.getCoverByTemplateSiteId(arg.getId());
            if (null != hexagonSiteCoverDTO) {
                apathList.add(hexagonSiteCoverDTO.getSharePicH5Apath());
                coverApathMap.put(hexagonSiteCoverDTO.getHexagonSiteId(), hexagonSiteCoverDTO.getSharePicH5Apath());
            }
        }

        Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);
        Map<String, String> formMap = new HashMap<>();
        List<String> formIds = new ArrayList<>();
        HexagonSiteListDTO hexagonSiteFormListDTO = hexagonTemplateSiteDAO.getFormByTemplateSiteId(arg.getId());
        if (null != hexagonSiteFormListDTO ) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(hexagonSiteFormListDTO.getFormId())) {
                formIds.add(hexagonSiteFormListDTO.getFormId());
            }
            formMap.put(hexagonSiteFormListDTO.getHexagonSiteId(), hexagonSiteFormListDTO.getFormId());
        }

        Map<String, Boolean> formMappingMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(formIds)) {
            formMappingMap = crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(ea, formIds);
        }


        GetSiteByEaUnitResult result = BeanUtil.copy(hexagonTemplateSiteEntity, GetSiteByEaUnitResult.class);
        if (null != fsEmployeeMsgMap) {
            result.setCreatorBy(hexagonTemplateSiteEntity.getCreateBy());
            FsAddressBookManager.FSEmployeeMsg creator = fsEmployeeMsgMap.get(hexagonTemplateSiteEntity.getCreateBy());
            if (null != creator) {
                result.setCreator(creator.getName());
            }else{
                result.setCreator(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_BOARDMANAGER_193));
            }
        }

        if (null != coverUrlMap) {
            String apath = coverApathMap.get(hexagonTemplateSiteEntity.getId());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(apath)) {
                result.setCoverAPath(apath);
                result.setCoverUrl(coverUrlMap.get(apath));
            }
        }

        if (!formMap.isEmpty()) {
            Map<String, Integer> formIdToUsageMap = customizeFormDataManager.batchGetFormUsageByFormIds(formMap.values());
            String formId = formMap.get(hexagonTemplateSiteEntity.getId());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(formId)) {
                result.setFormId(formId);
                result.setFormUsage(FormDataUsage.getByType(formIdToUsageMap.get(formId)).getUsage());
                if (null != formMappingMap) {
                    result.setHadCrmMapping(formMappingMap.get(formId));
                } else {
                    result.setHadCrmMapping(false);
                }
            }
        }

        MemberConfigEntity memberConfig = memberConfigDao.getByEa(ea);
        if (memberConfig != null && result.getId().equals(memberConfig.getUpdateInfoSiteId())) {
            result.setUpdateMemberInfoSite(true);
        }

        result.setCreateTime(hexagonTemplateSiteEntity.getCreateTime().getTime());
        result.setUpdateTime(hexagonTemplateSiteEntity.getUpdateTime().getTime());
        return Result.newSuccess(result);
    }

    @Override
    public Result<MicrostationDecorationTemplateConfig> queryMicrostationDecorationTemplateList() {
        return Result.newSuccess(GsonUtil.fromJson(templateConfig, MicrostationDecorationTemplateConfig.class));
    }

    @Override
    @Transactional
    public Result createMicrostationDecorationByTemplate(String ea, Integer fsUserId, String classfy) {
        MicrostationDecorationTemplateConfig templateList = GsonUtil.fromJson(templateConfig, MicrostationDecorationTemplateConfig.class);
        MicrostationDecorationTemplateConfig.MicrostationDecorationTemplateItem template = null;
        for (MicrostationDecorationTemplateConfig.MicrostationDecorationTemplateItem item : templateList.getHexagonSiteList()){
            if (item.getClassfy().equals(classfy)){
                template = item;
                log.info("find item classy:{}", classfy);
                break;
            }
        }
        if (template == null){
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        List<CustomizeMiniAppNavbarEntity> oldMiniAppNavbarList = customizeMiniAppNavbarDAO.queryCutomizeMiniappNavbarByTempalteName(ea, classfy);
        List<CustomizeMiniAppNavbarEntity> currentMiniAppNavbarEntityList = customizeMiniAppNavbarDAO.queryMiniAppNavbarByEa(ea);
        if (CollectionUtils.isNotEmpty(currentMiniAppNavbarEntityList)){
            List<String> ids = currentMiniAppNavbarEntityList.stream().map(CustomizeMiniAppNavbarEntity::getId).collect(Collectors.toList());
            customizeMiniAppNavbarDAO.updateStatusByIds(ids, CustomizeMiniAppNavbarStatusEnum.DISABLE.getStatus());
        }

        if (CollectionUtils.isNotEmpty(oldMiniAppNavbarList)){
            List<String> ids = oldMiniAppNavbarList.stream().map(CustomizeMiniAppNavbarEntity::getId).collect(Collectors.toList());
            customizeMiniAppNavbarDAO.updateStatusByIds(ids, CustomizeMiniAppNavbarStatusEnum.ENABLE.getStatus());
        }else {
            for (MicrostationDecorationTemplateConfig.MicrostationDecorationTemplateHexagon hexagon : template.getTemplateHexagonList()) {
                HexagonCopyArg arg = new HexagonCopyArg();
                HexagonTemplateSiteEntity templateSite = hexagonTemplateSiteDAO.getById(hexagon.getHexaonSiteId());
                arg.setId(hexagon.getHexaonSiteId());
                arg.setName(templateSite.getName());
                Result<CreateSiteResult> siteResult = hexagonManager.hexagonCopySite(ea, fsUserId, arg, HexagonManager.COPY_FROM_TEMPLATE);
                if (!siteResult.isSuccess()) {
                    return Result.newError(SHErrorCode.MICROSTATION_CREATE_FAILED);
                }
                CustomizeMiniAppNavbarEntity navbarEntity = new CustomizeMiniAppNavbarEntity();
                navbarEntity.setEa(ea);
                navbarEntity.setTitle(templateSite.getName());
                navbarEntity.setIconTitle(hexagon.getIconTitle());
                navbarEntity.setStatus(CustomizeMiniAppNavbarStatusEnum.ENABLE.getStatus());
                navbarEntity.setForwardType(CustomizeMiniAppNavbarForwardEnum.FORWARD_HEXAGON_SITE.getType());
                navbarEntity.setForwardId(siteResult.getData().getId());
                navbarEntity.setForwardUrl(null);
                navbarEntity.setTemplateName(classfy);
                navbarEntity.setForwardName(templateSite.getName());
                navbarEntity.setId(UUIDUtil.getUUID());
                navbarEntity.setSeq(hexagon.getIndex());
                customizeMiniAppNavbarDAO.insert(navbarEntity);
            }
        }

        //设置选中模板按钮选中颜色
        customizeMiniAppNavbarConfigDAO.updateAllByEa(ea, template.getButtonFontColor(), template.getNavbarLayout(), template.getFontIconColor(), template.getBackgroundColor());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> setHexagonTemplateGroupBatch(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("HexagonServiceImpl.setHexagonGroupBatch only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }

        List<HexagonTemplateSiteEntity> templateSiteEntityList = hexagonTemplateSiteDAO.getByIdList(arg.getObjectIdList());

        if (CollectionUtils.isEmpty(templateSiteEntityList)) {
            return Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        if (templateSiteEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PARTY_HEXAGON_TEMPLATE_SITE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        // 直接把以前的分组删掉，就不用更新了，直接全部插入
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.HEXAGON_TEMPLATE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.HEXAGON_TEMPLATE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);

        return Result.newSuccess();
    }
}