package com.facishare.marketing.provider.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.beust.jcommander.internal.Sets;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.AddMaterialsArg;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.MarketingActivityExternalConfigDao;
import com.facishare.marketing.provider.dao.NoticeDAO;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dao.yxzs.YxzsAllSpreadWxTemplateNoticeSettingDAO;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.NoticeEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsAllSpreadWxTemplateNoticeSettingEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.innerResult.MemberVirtualUserInfo;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.marketingAssistant.YxzsNoticeSendManager;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component("memberMarketingManager")
public class MemberMarketingManager {

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @Autowired
    private AppVersionManager appVersionManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private NoticeDAO noticeDAO;

    @Autowired
    private SpreadTaskManager spreadTaskManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private CardManager cardManager;

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    @Autowired
    private AccountManager accountManager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private YxzsAllSpreadWxTemplateNoticeSettingDAO yxzsAllSpreadWxTemplateNoticeSettingDAO;
    @Autowired
    private YxzsNoticeSendManager yxzsNoticeSendManager;

    @Autowired
    private MemberManager memberManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    private Gson gson = new Gson();

    public void addFieldToMemberObj(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            log.warn("会员描述不存在 ea:{}", ea);
            return;
        }
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        if (!objectDescribe.getFields().containsKey(CrmMemberFieldEnum.MEMBER_TYPE.getApiName())) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MemberObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"推广身份\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"普通会员\",\"value\":\"member\"},{\"label\":\"伙伴\",\"value\":\"partner\"},{\"label\":\"员工\",\"value\":\"employee\"},{\"label\":\"推广会员\",\"value\":\"spread_member\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"推广身份\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"none\",\"label\":\"推广身份\",\"target_api_name\":\"MemberObj\",\"api_name\":\"member_type\",\"is_index_field\":true,\"config\":{},\"help_text\":\"当会员有员工、伙伴、推广会员任意一个推广身份时，可以进入推广工作台主动查找物料推广、接收推广任务、查看客户轨迹\",\"status\":\"released\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"默认客户布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add member_type field ea:{} result: {}", ea, result);
        }

        if (!objectDescribe.getFields().containsKey(CrmMemberFieldEnum.ACCEPT_NOTIFY.getApiName())) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.MEMBER.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"MemberObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"是否接收通知\",\"is_unique\":false,\"type\":\"select_one\",\"default_to_zero\":false,\"is_required\":false,\"options\":[{\"label\":\"是\",\"value\":\"yes\"},{\"label\":\"否\",\"value\":\"no\"}],\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"是否接收通知\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"no\",\"label\":\"是否接收通知\",\"target_api_name\":\"MemberObj\",\"api_name\":\"accept_notify\",\"is_index_field\":true,\"config\":{},\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_one\",\"api_name\":\"Member_customer_default_layout__c\",\"label\":\"默认客户布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add accept_notify field ea:{} result: {}", ea, result);
        }
    }

    public void updateMemberTypeFieldDescribe(String ea) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Result<ControllerGetDescribeResult> oldDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if(!oldDescribeResult.isSuccess() || oldDescribeResult.getData() == null || oldDescribeResult.getData().getDescribe() == null) {
            log.warn("updateMemberTypeFieldDescribe getDescribe fail, ea: {}", ea);
            return;
        }
        ObjectDescribe describe = oldDescribeResult.getData().getDescribe();
        Map<String, FieldDescribe> fieldMap = describe.getFields();
        FieldDescribe fieldDescribe = fieldMap.get(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
        if (fieldDescribe == null){
            log.warn("updateMemberTypeFieldDescribe member_type is null, ea: {}", ea);
            return;
        }
        String defaultValue = fieldDescribe.getString("default_value");
        if (!MemberTypeEnum.MEMBER.getType().equals(defaultValue)) {
            fieldDescribe.put("default_value", MemberTypeEnum.MEMBER.getType());
        }
        fieldDescribe.put("help_text", "当会员有员工、伙伴、推广会员任意一个推广身份时，可以进入推广工作台主动查找物料推广、接收推广任务、查看客户轨迹");
        fieldDescribe.put("label", "推广身份");
        fieldDescribe.put("description", "推广身份");
        List<Map<String, Object>> options = (List<Map<String, Object>>)(fieldDescribe.get("options"));
        boolean hasSpreadMember = false;
        for (Map<String, Object> option : options) {
            String value = (String) option.get("value");
            if (value.equals("spread_member")) {
                hasSpreadMember = true;
                log.info("ea already has spread member, ea: {}", ea);
            }
            if (MemberTypeEnum.MEMBER.getType().equals(value)) {
                option.put("label", "普通会员");
            }
        }
        if (!hasSpreadMember) {
            Map<String, Object> spreadMemberOption = new HashMap<>();
            spreadMemberOption.put("label", "推广会员");
            spreadMemberOption.put("value", "spread_member");
            options.add(spreadMemberOption);
            Result<ControllerGetDescribeResult> result = objectDescribeService.updateField(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName(), CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), fieldDescribe);
            log.info("updateMemberTypeFieldDescribe, ea: {} result: {}", ea, result);
        }
    }

    public void handleMemberChangeEvent(String ea, String memberId) {
        ObjectData memberObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        if (memberObjectData == null) {
            log.info("handleMemberCreateEvent, 会员对象不存在, ea: {} memberObjId: {}", ea, memberId);
            userRelationManager.removeMemberRelation(ea, Lists.newArrayList(memberId));
            appMenuTemplateService.handleMemberChangeEvent(ea, memberId, null);
            return;
        }
        UserRelationEntity userRelationEntity = userRelationManager.getByMemberId(ea, memberId);
        String memberType = memberObjectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
        // 如果会员类型为空 或者 不是员工、伙伴
        if (!MemberTypeEnum.isMemberMarketing(memberType)) {
            if (userRelationEntity != null)  {
                log.info("会员不是员工或者伙伴，删除会员关系，ea: {} memberId: {}", ea, memberId);
                userRelationManager.removeMemberRelation(ea, Lists.newArrayList(memberId));
                appMenuTemplateService.handleMemberChangeEvent(ea, memberId, userRelationEntity.getFsUserId());
            }
            return;
        }
        if (userRelationEntity != null) {
            log.info("handleMemberCreateEvent, 已存在会员数据, userRelationEntity: {}", userRelationEntity);
            userRelationManager.updateUserRelationInfo(ea, Lists.newArrayList(userRelationEntity));
            appMenuTemplateService.handleMemberChangeEvent(ea, memberId, userRelationEntity.getFsUserId());
            return;
        }
        userRelationManager.bindMemberRelation(ea, null, memberObjectData);
        appMenuTemplateService.handleMemberChangeEvent(ea, memberId, null);
    }


    public void sendNotice(String ea, NoticeEntity noticeEntity, String marketingEventId) {
        String marketingActivityId = null;
        try {
            noticeEntity = noticeDAO.getNoticeById(noticeEntity.getId());
            if (noticeEntity.getType() == null || noticeEntity.getType() != SendNoticeTypeEnum.MEMBER_NOTICE.getType()) {
                return;
            }
            if (noticeEntity.getStatus() != NoticeStatusEnum.UN_SEND.getStatus()) {
                log.info("状态不等于待发送 ea:{}, noticeEntity: {}", ea, noticeEntity);
                return;
            }
            MarketingActivityExternalConfigEntity marketingActivityExternalConfigEntity = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.MEMBER_MARKETING_SPREAD.getType(), noticeEntity.getId());
            if (marketingActivityExternalConfigEntity == null) {
                log.warn("找不到营销活动配置, ea: {} notice: {}", ea, noticeEntity);
                return;
            }
            marketingActivityId = marketingActivityExternalConfigEntity.getMarketingActivityId();
            if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null) {
                log.info("MemberMarketingManager.sendNotice failed enterprise stop or license expire ea:{}", ea);
                noticeDAO.updateStatusById(noticeEntity.getId(), NoticeStatusEnum.FAIL.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
            boolean isNormal = marketingActivityAuditManager.checkAudtStatus(ea, marketingActivityId);
            if (!isNormal) {
                log.info("营销活动还未审核, ea: {} marketingActivityId: {}", ea, marketingActivityId);
                return;
            }
            boolean lock = redisManager.lockMemberMarketingOperate(ea, noticeEntity.getId());
            if (!lock) {
                log.info("会员营销处理中, ea: {} noticeId: {}", ea, noticeEntity.getId());
                return;
            }
            NoticeSendArg.NoticeVisibilityVO visibilityVO = JsonUtil.fromJson(noticeEntity.getSendScope(), NoticeSendArg.NoticeVisibilityVO.class);

            if (CollectionUtils.isEmpty(visibilityVO.getFilters())) {
                log.info("找不到会员发送范围, ea: {} notice: {}", ea, noticeEntity);
                noticeDAO.updateStatusById(noticeEntity.getId(), NoticeStatusEnum.FAIL.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
            PaasQueryFilterArg queryFilterArg = getPassQueryFilterArg(visibilityVO.getFilters());
            int totalCount = getSpreadMemberCount(ea, visibilityVO.getFilters());
            if (totalCount <= 0) {
                log.info("查询范围找不到任何会员, ea: {} marketingActivityId: {} queryArg: {}", ea, marketingActivityId, JsonUtil.toJson(queryFilterArg));
                noticeDAO.updateStatusById(noticeEntity.getId(), NoticeStatusEnum.FAIL.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, String.valueOf(SendStatusEnum.FAIL.getStatus()));
                return;
            }
            Set<Integer> userIdSet = Sets.newHashSet();
            int count = 0;
            String lastId = null;
            int pageSize = 100;
            // 会员id --> 是否接受通知
            Map<String, String> memberIdToAcceptNotifyMap = new HashMap<>();
            Map<Integer, String> fsUserIdToMemberIdMap = new HashMap<>();
            while (count < totalCount) {
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
                if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
                List<String> memberObjIdList = Lists.newArrayList();
                for (ObjectData objectData : objectDataList) {
                    String id = objectData.getId();
                    memberObjIdList.add(id);
                    memberIdToAcceptNotifyMap.put(id, objectData.getString(CrmMemberFieldEnum.ACCEPT_NOTIFY.getApiName()));
                }
                List<UserRelationEntity> userRelationEntityList = userRelationManager.getByMemberIdList(ea, memberObjIdList);
                if (CollectionUtils.isNotEmpty(userRelationEntityList)) {
                    for (UserRelationEntity userRelationEntity : userRelationEntityList) {
                        Integer fsUserId = userRelationEntity.getFsUserId();
                        // 如果已经绑定了crm员工，就不要创建推广任务
                        if (QywxUserConstants.isMemberVirtualUserId(fsUserId)) {
                            userIdSet.add(fsUserId);
                            fsUserIdToMemberIdMap.put(fsUserId, userRelationEntity.getMemberId());
                        }
                    }
                }
                count += objectDataList.size();
                lastId = objectDataList.get(objectDataList.size() - 1).getId();
            }
            log.info("会员营销发送会员数量, ea: {}  marketingActivityId: {} member totalCount: {} userIdSet size: {}", ea, marketingActivityId, totalCount, userIdSet.size());
            if (CollectionUtils.isEmpty(userIdSet)) {
                noticeDAO.updateStatusById(noticeEntity.getId(), NoticeStatusEnum.FAIL.getStatus(), new Date());
                return;
            }
            spreadTaskManager.createSpreadTask(ea, marketingActivityId, Lists.newArrayList(userIdSet));
            noticeDAO.updateStatusById(noticeEntity.getId(), NoticeStatusEnum.SUCCESS.getStatus(), new Date());
            marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, String.valueOf(SendStatusEnum.FINISHED.getStatus()));
            // 如果开启了审核，立即发送会变成定时发送，真正执行时更新定时执行时间
            if (noticeEntity.getSendType() == NoticeSendTypeEnum.TIMING.getType() && noticeEntity.getTimingTime() == null) {
                noticeDAO.updateTimingDateById(noticeEntity.getId(), DateUtil.parse(DateUtil.format(new Date())));
            }
            //发模板通知
            YxzsAllSpreadWxTemplateNoticeSettingEntity entity = yxzsAllSpreadWxTemplateNoticeSettingDAO.getByEa(noticeEntity.getFsEa());
            if (entity!=null && entity.getOpenWxNotice() != null && entity.getOpenWxNotice() == 1 && entity.getWxTemplateMsg() != null && StringUtils.isNotEmpty(entity.getWxAppId())) {
                boolean multiple = false;
                String materialInfoList = noticeEntity.getMaterialInfoList();
                if (StringUtils.isNotBlank(materialInfoList)) {
                    List<AddMaterialsArg.MaterialInfo> materialInfos = gson.fromJson(materialInfoList, new TypeToken<List<AddMaterialsArg.MaterialInfo>>() {
                    }.getType());
                    if (CollectionUtils.isNotEmpty(materialInfos)) {
                        multiple = true;
                    }
                }
                // 过滤掉不接受通知的会员
                List<Integer> needNotifyMemberVirtualUserIdList = userIdSet.stream().filter(e -> fsUserIdToMemberIdMap.containsKey(e)
                        && "yes".equals(memberIdToAcceptNotifyMap.get(fsUserIdToMemberIdMap.get(e)))).collect(Collectors.toList());
                yxzsNoticeSendManager.sendAllSpreardWxTemplateMsg(noticeEntity.getTitle(), noticeEntity.getDescription(), noticeEntity, marketingActivityId, multiple, entity, needNotifyMemberVirtualUserIdList, marketingEventId);
            }
        } catch (Exception e) {
            log.error("会员营销发送异常, ea: {} marketingActivityId: {} notice: {}", ea, marketingActivityId, noticeEntity, e);
            noticeDAO.updateStatusById(noticeEntity.getId(), NoticeStatusEnum.FAIL.getStatus(), new Date());
            marketingActivityManager.updateMarketingActivityStatus(ea, marketingActivityId, String.valueOf(SendStatusEnum.FAIL.getStatus()));
        } finally {
            redisManager.unlockMemberMarketingOperate(ea, noticeEntity.getId());
        }
    }

    public Integer getSpreadMemberCount(String ea, List<Filter> filterList) {
        if (CollectionUtils.isEmpty(filterList)) {
            return 0;
        }
        PaasQueryFilterArg paasQueryFilterArg = getPassQueryFilterArg(filterList);
        return crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
    }

    public PaasQueryFilterArg getPassQueryFilterArg(List<Filter> filterList) {
        List<PaasQueryArg.Condition> conditionList = BeanUtil.copyByGson(filterList, PaasQueryArg.Condition.class);
        PaasQueryArg.Condition memberTypeCondition = new PaasQueryArg.Condition(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), MemberTypeEnum.getMemberMarketingType(), OperatorConstants.IN);
        // 这里暂时不加这个条件，通知归通知，第一期没有通知
        //PaasQueryArg.Condition acceptNotifyCondition = new PaasQueryArg.Condition(CrmMemberFieldEnum.ACCEPT_NOTIFY.getApiName(), Lists.newArrayList("yes"), OperatorConstants.EQ);
        conditionList.add(memberTypeCondition);
        //conditionList.add(acceptNotifyCondition);
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.MEMBER.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList("_id", "name", CrmMemberFieldEnum.ACCEPT_NOTIFY.getApiName()));
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.setFilters(conditionList);
        queryFilterArg.setQuery(paasQueryArg);
        return queryFilterArg;
    }

    public Map<Integer, MemberVirtualUserInfo> getMemberInfoByVirtualUserId(String ea, List<Integer> userIdList) {
        Map<Integer, MemberVirtualUserInfo> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(userIdList)) {
            return resultMap;
        }
        userIdList = userIdList.stream().filter(QywxUserConstants::isMemberVirtualUserId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return resultMap;
        }
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByFsUserIdList(ea, userIdList);
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            return resultMap;
        }
        List<String> memberIdList = userRelationEntityList.stream().map(UserRelationEntity::getMemberId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ObjectData> memberObjectDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), Lists.newArrayList(CrmMemberFieldEnum.ID.getApiName(), CrmMemberFieldEnum.NAME.getApiName(), CrmMemberFieldEnum.PHONE.getApiName()), memberIdList);
        if (CollectionUtils.isEmpty(memberObjectDataList)) {
            return resultMap;
        }
        Map<String, ObjectData> memberIdToObjectDataMap = memberObjectDataList.stream().collect(Collectors.toMap(ObjectData::getId, e -> e, (v1, v2) -> v1));
        for (UserRelationEntity userRelationEntity : userRelationEntityList) {
            String memberId = userRelationEntity.getMemberId();
            if (StringUtils.isBlank(memberId)) {
                continue;
            }
            ObjectData objectData = memberIdToObjectDataMap.get(memberId);
            if (objectData == null) {
                continue;
            }
            String phone = objectData.getString(CrmMemberFieldEnum.PHONE.getApiName());
            String name = objectData.getName();
            MemberVirtualUserInfo memberVirtualUserInfo = new MemberVirtualUserInfo();
            memberVirtualUserInfo.setMemberId(memberId);
            memberVirtualUserInfo.setUserId(userRelationEntity.getFsUserId());
            memberVirtualUserInfo.setMobile(phone);
            memberVirtualUserInfo.setName(name);
            memberVirtualUserInfo.setEmail(objectData.getString(CrmMemberFieldEnum.EMAIL.getApiName()));
            resultMap.put(userRelationEntity.getFsUserId(), memberVirtualUserInfo);
        }
        return resultMap;
    }

    public String getMemberType(String ea, Integer fsUserId) {
        if (!QywxUserConstants.isMemberVirtualUserId(fsUserId)) {
            return null;
        }
        UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, fsUserId);
        if (userRelationEntity == null || StringUtils.isBlank(userRelationEntity.getMemberId())) {
            return null;
        }
        ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), userRelationEntity.getMemberId());
        if (objectData == null) {
            return null;
        }
        return objectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
    }


    public String getMemberType(String ea, String memberId) {
        if (StringUtils.isBlank(memberId)) {
            return null;
        }
        ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), memberId);
        if (objectData == null) {
            return null;
        }
        return objectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
    }

    public void handleMemberMarketingConfig(String ea, String boundMemberId, String uid, String appId) {
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPlugin(ea, MarketingPluginTypeEnum.MEMBER_MARKETING.getType());
        // 只要开启过，就写，不管是否开启
        if (marketingPluginConfigEntity == null) {
            log.info("没有配置会员营销插件, ea: {} memberId: {} uid: {}", ea, boundMemberId, uid);
            return;
        }
        ObjectData memberObjectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MEMBER.getName(), boundMemberId);
        if (memberObjectData == null) {
            log.warn("会员数据不存在，ea: {} memberId: {}", ea, boundMemberId);
            return;
        }
        String memberType = memberObjectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
        if (!MemberTypeEnum.isMemberMarketing(memberType)) {
            log.info("会员不是员工或者伙伴，ea: {} memberId: {}", ea, boundMemberId);
            return;
        }
        FSBindEntity existFsBindEntity = fsBindManager.queryFSBindByUidAndEa(uid, ea);
        if (existFsBindEntity != null && !QywxUserConstants.isMemberVirtualUserId(existFsBindEntity.getFsUserId())) {
            log.info("uid already bind to employee, ea: {} existFsBindEntity: {}", ea, existFsBindEntity);
            return;
        }
        userRelationManager.bindMemberRelation(ea, uid, memberObjectData);
        if (existFsBindEntity == null) {
            // 生成绑定
            createFsBindRelation(ea, uid, appId, memberObjectData);
        }
        accountManager.createMemberMarketingAccount(uid, memberObjectData.getString(CrmMemberFieldEnum.PHONE.getApiName()));
        cardManager.asyncCreateMemberCard(ea, boundMemberId, uid);
        List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), uid);
        if (CollectionUtils.isEmpty(photoEntityList)) {
            accountManager.createCardQRCode(uid, ea);
        }
    }

    private void createFsBindRelation(String ea, String uid, String appId, ObjectData memberObjectData) {
        UserRelationEntity userRelationEntity = userRelationManager.getByEaAndUid(ea, uid);
        int fsUserId = userRelationEntity.getFsUserId();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        String phone = memberObjectData.getString(CrmMemberFieldEnum.PHONE.getApiName());
        FSBindEntity fsBindEntity = new FSBindEntity();
        fsBindEntity.setUid(uid);
        fsBindEntity.setFsEa(ea);
        fsBindEntity.setFsUserId(fsUserId);
        fsBindEntity.setPhone(phone);
        fsBindEntity.setType(AccountTypeEnum.MEMBER_WECHAT_MINI_APP.getType());
        fsBindEntity.setFsCorpId(ei);
        fsBindEntity.setAppId(appId);
        fsBindManager.insert(fsBindEntity);
    }

    public List<ObjectData> getAllEmployeeOrPartnerMember(String ea, List<String> fieldNameList) {
        if (!memberManager.isOpenMember(ea)) {
            return Lists.newArrayList();
        }
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
        paasQueryArg.addFilter(CrmMemberFieldEnum.MEMBER_TYPE.getApiName(), PaasAndCrmOperatorEnum.IN.getCrmOperator(), MemberTypeEnum.getMemberMarketingType());
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.MEMBER.getName(), fieldNameList, paasQueryArg);

        List<ObjectData> resultList = Lists.newArrayList();
        for (ObjectData objectData : objectDataList) {
            String memberType = objectData.getString(CrmMemberFieldEnum.MEMBER_TYPE.getApiName());
            if (StringUtils.isBlank(memberType)) {
                continue;
            }
            if (!MemberTypeEnum.isMemberMarketing(memberType)) {
                continue;
            }
            resultList.add(objectData);
        }
        return resultList;
    }
}