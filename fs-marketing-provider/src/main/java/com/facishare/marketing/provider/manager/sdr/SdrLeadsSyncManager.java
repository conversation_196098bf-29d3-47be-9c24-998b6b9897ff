package com.facishare.marketing.provider.manager.sdr;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.enums.SpreadChannelEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.innerArg.CreateOrUpdateMarketingLeadSyncRecordObjArg;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.util.i18n.I18NUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SdrLeadsSyncManager {

    @Autowired
    private EIEAConverter converter;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;
    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    /**
     * 会话记录线索提取
     *
     * @param ea
     * @param chatRecords
     * @return
     */
    public void extractionOfConversationRecordClues(String ea, String chatRecords) {
        if (StringUtils.isBlank(chatRecords)) {
            return;
        }
        try {
            Map<String, String> sceneVariables = Maps.newHashMap();
            sceneVariables.put("chatRecords", chatRecords);
            List<CrmUserDefineFieldVo> objectFieldDescribesList = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
            sceneVariables.put("leadsFields", JSONObject.toJSONString(objectFieldDescribesList));
            BaseArgument context = new BaseArgument();
            context.setTenantId(String.valueOf(converter.enterpriseAccountToId(ea)));
            context.setUserId(String.valueOf(SuperUserConstants.USER_ID));
            context.setLocale(I18NUtil.getLanguage());
            context.setBusiness(SdrActionManager.BIZ_NAME);
            PromptCompletions.Arg promptArg = new PromptCompletions.Arg();
            promptArg.setApiName("prompt_SDR_lead_data_extraction");
            promptArg.setSceneVariables(sceneVariables);
            PromptCompletions.Result promptResult = FsAI.prompt().completions(context, promptArg);
            log.info("extractionOfConversationRecordClues ea:{} promptCompletionsArg={} result:{}", ea, JSONObject.toJSON(promptArg), promptResult);
            String jsonResult = promptResult.getMessage();
            if (StringUtils.isNotBlank(jsonResult)) {
                JSONObject objectData = JSONObject.parseObject(jsonResult);
                this.saveLeadObj(ea, objectData);
            }
        } catch (Exception e) {
            log.warn("SdrLeadsSyncManager extractionOfConversationRecordClues error", e);
        }
    }

    public void saveLeadObj(String fsEa, Map<String, Object> dataMap) {
        if (dataMap == null) {
            return;
        }
        ObjectData objectData = new ObjectData();
        objectData.putAll(dataMap);
        //设置线索来源渠道promotion_channel
        objectData.put("promotion_channel", SpreadChannelEnum.OTHER.getCode());
        objectData.put("from_marketing", true);
        objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
        objectData.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());

        // 获取线索创建人
        Integer createBy = clueDefaultSettingService.getClueCreator(null, fsEa, ClueDefaultSettingTypeEnum.OTHER.getType());
        Map<String, List<String>> orgMap = dataPermissionManager.getDataOwnerOrganizationByUserIds(fsEa, Collections.singletonList(createBy));
        List<String> org = orgMap.get(String.valueOf(objectData.getOwner()));
        if (org != null) {
            objectData.put("data_own_organization", org);
        } else {
            objectData.put("data_own_organization", Lists.newArrayList("999999"));
        }
        // 创建线索
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = crmV2Manager.addObjectData(fsEa, LeadsFieldContants.API_NAME, createBy, objectData);
        if (actionAddResultResult != null && actionAddResultResult.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
            String leadId = actionAddResultResult.getData().getObjectData().getId();
            log.info("SdrLeadsSyncManager saveLeadsToCrm success data:{} leadId:{}", objectData, leadId);
            tryCreateOrUpdateMarketingLeadSyncRecordObj(fsEa, objectData, MarketingLeadSyncRecordObjManager.SUCCESS_STATUS, "同步成功");
        } else {
            String message = CrmStatusMessageConstant.SYSTEM_ERROR;
            if (actionAddResultResult != null && StringUtils.isNotBlank(actionAddResultResult.getMessage())) {
                message = actionAddResultResult.getMessage();
            }
            log.info("SdrLeadsSyncManager saveLeadsToCrm failed data:{} message:{}", objectData, message);
            tryCreateOrUpdateMarketingLeadSyncRecordObj(fsEa, objectData, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
        }
    }

    private void tryCreateOrUpdateMarketingLeadSyncRecordObj(String fsEa, ObjectData objectData, String syncStatus, String remark) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg leadSyncRecordObjArg = new CreateOrUpdateMarketingLeadSyncRecordObjArg();
        leadSyncRecordObjArg.setEa(fsEa);
        leadSyncRecordObjArg.setLeadId(objectData.getId());
        leadSyncRecordObjArg.setLeadName(objectData.getName());
        leadSyncRecordObjArg.setMobile(objectData.getString("mobile"));
        leadSyncRecordObjArg.setSyncData(JsonUtil.toJson(objectData));
        leadSyncRecordObjArg.setSyncStatus(syncStatus);
        leadSyncRecordObjArg.setRemark(remark);
        leadSyncRecordObjArg.setOutPlatformType(0);
        leadSyncRecordObjArg.setOutPlatformName("SDR");
        marketingLeadSyncRecordObjManager.tryCreateOrUpdateMarketingLeadSyncRecordObj(leadSyncRecordObjArg);
    }

}
