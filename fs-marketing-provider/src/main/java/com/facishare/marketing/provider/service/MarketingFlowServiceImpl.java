package com.facishare.marketing.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.CreateMarketingFlowDraftArg;
import com.facishare.marketing.api.arg.ListMarketingFlowArg;
import com.facishare.marketing.api.arg.ListMarketingFlowDraftArg;
import com.facishare.marketing.api.arg.UpdateMarketingFlowDraftArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.DayTrendData;
import com.facishare.marketing.api.result.EnabledFlowCountResult;
import com.facishare.marketing.api.result.MarketingFlowStatisticResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.service.MarketingFlowService;
import com.facishare.marketing.api.vo.*;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Email;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.MapHelper;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.MarketingFlowDraftDao;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.dao.distribution.MarketingFlowDayStatisticDao;
import com.facishare.marketing.provider.dao.marketingflow.ExcludedFlowUserDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingActivityNodeAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowAdditionalConfigDao;
import com.facishare.marketing.provider.dao.marketingflow.MarketingFlowTaskDao;
import com.facishare.marketing.provider.dto.DateTrendData;
import com.facishare.marketing.provider.dto.MarketingFlowInstanceAmountStatisticData;
import com.facishare.marketing.provider.entity.MarketingFlowDraftEntity;
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity;
import com.facishare.marketing.provider.entity.marketingflow.ExcludedFlowUserEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingActivityNodeAdditionalConfigEntity;
import com.facishare.marketing.provider.entity.marketingflow.MarketingFlowAdditionalConfigEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.fxiaoke.bpmrestapi.arg.GetDefinitionListArg;
import com.fxiaoke.bpmrestapi.arg.IdArg;
import com.fxiaoke.bpmrestapi.arg.UpdateWorkflowDefinitionStatusArg;
import com.fxiaoke.bpmrestapi.result.BpmResult;
import com.fxiaoke.bpmrestapi.result.IdResult;
import com.fxiaoke.bpmrestapi.result.WorkflowDefinitionListResult;
import com.fxiaoke.bpmrestapi.service.BpmFlowApiService;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import com.mysql.jdbc.StringUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.facishare.marketing.provider.util.Constant.A_WAREHOUSE_TYPE;
import static com.facishare.marketing.provider.util.Constant.TEMP_N_WAREHOUSE_TYPE;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("marketingFlowService")
public class MarketingFlowServiceImpl implements MarketingFlowService {
    private static final Integer SUPER_USER = -10000;
    @Autowired
    private BpmFlowApiService bpmFlowApiService;
    @Autowired
    private MarketingFlowAdditionalConfigDao marketingFlowAdditionalConfigDao;
    @Autowired
    private MarketingActivityNodeAdditionalConfigDao marketingActivityNodeAdditionalConfigDao;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private MarketingFlowInstanceManager marketingFlowInstanceManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MarketingFlowDayStatisticDao marketingFlowDayStatisticDao;
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private PaasRuleGroupManager paasRuleGroupManager;
    @Autowired
    private MarketingFlowDraftDao marketingFlowDraftDao;
    @Autowired
    private ExcludedFlowUserDao excludedFlowUserDao;
    @Autowired
    private MarketingFlowTaskDao marketingFlowTaskDao;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Override
    public Result<String> create(final Integer ei, final Integer fsUserId, final MarketingFlowVO arg) {
        if (isEnabledFlowReachLimit(ei)) {
            return Result.newError(SHErrorCode.ENABLED_FLOW_REACH_LIMIT);
        }
        doDealCreateCrmLeadsAboutCreateRuleGroupData(ei, fsUserId, arg);
        final BpmFlowIdAndWorkflowId bpmFlowIdAndWorkflowId = doCreateBpmFlow(ei, fsUserId, arg.getRawDefinition());
        doCreateMarketingFlowAdditionalConfig(ei, bpmFlowIdAndWorkflowId, arg.getMarketingFlowAdditionalConfig());
        if (!StringUtils.isNullOrEmpty(arg.getDraftId())) {
            marketingFlowDraftDao.deleteByEiAndId(ei, arg.getDraftId());
        }
        doCreateMarketingActivityNodeAdditionalConfigs(ei, fsUserId, bpmFlowIdAndWorkflowId, arg.getMarketingActivityNodeAdditionalConfigList());
        return Result.newSuccess(bpmFlowIdAndWorkflowId.getBpmFlowId());
    }

    @Override
    public Result<Void> update(final Integer ei, final Integer fsUserId, final MarketingFlowVO arg) {
        doDealCreateCrmLeadsAboutCreateRuleGroupData(ei, fsUserId, arg);
        final BpmFlowIdAndWorkflowId bpmFlowIdAndWorkflowId = doUpdateBpmFlow(ei, fsUserId, arg.getRawDefinition());
        doUpdateMarketingFlowAdditionalConfig(ei, bpmFlowIdAndWorkflowId, arg.getMarketingFlowAdditionalConfig());
        if (!StringUtils.isNullOrEmpty(arg.getDraftId())) {
            marketingFlowDraftDao.deleteByEiAndId(ei, arg.getDraftId());
        }
        doCreateMarketingActivityNodeAdditionalConfigs(ei, fsUserId, bpmFlowIdAndWorkflowId, arg.getMarketingActivityNodeAdditionalConfigList());
        return Result.newSuccess();
    }

    private boolean isEnabledFlowReachLimit(Integer ei) {
        return marketingFlowAdditionalConfigDao.countEnabledFlows(ei) >= 10;
    }

    private void doDealCreateCrmLeadsAboutCreateRuleGroupData(Integer ei, Integer fsUserId, MarketingFlowVO arg) {
        if (arg.getMarketingFlowAdditionalConfig().getStartupType() == MarketingFlowStartupType.CRM_OBJ_CREATE.getType()) {
            MapHelper<String, Object> definitionHelper = MapHelper.fromMap(arg.getRawDefinition());
            String flowName = definitionHelper.getString(BpmFlowField.NAME.getFieldName());
            String workflowId = definitionHelper.getString(BpmFlowField.WORKFLOW_ID.getFieldName());
            String ruleGroupName = flowName + workflowId;
            String ruleCode = paasRuleGroupManager.createPaasRuleCode(ei, fsUserId, ruleGroupName, arg.getMarketingFlowAdditionalConfig().getRuleGroup());
            arg.getMarketingFlowAdditionalConfig().setRuleCode(ruleCode);
        }
    }

    @Override
    public Result<PageResult<MarketingFlowVO>> list(final Integer ei, final Integer fsUserId, final ListMarketingFlowArg arg) {
        GetDefinitionListArg getDefinitionListArg = new GetDefinitionListArg();
        getDefinitionListArg.setKeyWord(arg.getName());
        getDefinitionListArg.setEnabled(arg.getState());
        getDefinitionListArg.setPageNumber(arg.getPageNum());
        getDefinitionListArg.setPageSize(arg.getPageSize());
        getDefinitionListArg.setSupportFlow("market");
        BpmResult<WorkflowDefinitionListResult> bpmResult = bpmFlowApiService.getDefinitionList(ei, SUPER_USER, getDefinitionListArg);
        if (!bpmResult.isSuccess()) {
            throw new OuterServiceRuntimeException(bpmResult.getCode(), bpmResult.getMessage());
        }
        PageResult<MarketingFlowVO> pageResult = new PageResult<>();
        pageResult.setTotalCount(bpmResult.getData().getTotalCount());
        if (bpmResult.getData().getOutlines() == null) {
            pageResult.setData(new ArrayList<>(0));
        } else {
            List<MarketingFlowVO> marketingFlowVOS = bpmResult.getData().getOutlines().stream().map(rawDefinition -> marketingFlowInstanceManager.doConvertRawDefinitionToMarketingFlowVO(ei, rawDefinition))
                    .collect(Collectors.toList());
            pageResult.setData(marketingFlowVOS);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> delete(Integer ei, Integer fsUserId, String id) {
        UpdateWorkflowDefinitionStatusArg updateWorkflowDefinitionStatusArg = new UpdateWorkflowDefinitionStatusArg(id, false);
        updateWorkflowDefinitionStatusArg.setSupportFlow("market");
        bpmFlowApiService.updateDefinitionStatus(ei, -10000, updateWorkflowDefinitionStatusArg);
        bpmFlowApiService.deleteDefinition(ei, -10000, new IdArg(id, "market"));
        return Result.newSuccess();
    }

    @Override
    public Result<MarketingFlowVO> get(Integer ei, Integer fsUserId, String id) {
        return Result.newSuccess(marketingFlowInstanceManager.get(ei, id));
    }

    @Override
    public Result<Void> updateStatus(Integer ei, Integer fsUserId, String id, Integer status) {
        boolean enabled = MarketingFlowStatus.ENABLED.getStatus() == status;
        if (enabled && isEnabledFlowReachLimit(ei)) {
            return Result.newError(SHErrorCode.ENABLED_FLOW_REACH_LIMIT);
        }
        BpmResult<Void> bpmResult = bpmFlowApiService.updateDefinitionStatus(ei, fsUserId, new UpdateWorkflowDefinitionStatusArg(id, enabled));
        if (!bpmResult.isSuccess()) {
            throw new OuterServiceRuntimeException(bpmResult.getCode(), bpmResult.getMessage());
        }
        marketingFlowAdditionalConfigDao.updateCurrentFlowStatusByBpmFlowId(ei, id, status);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> manualStartup(Integer ei, Integer fsUserId, String id) {
        MarketingFlowAdditionalConfigEntity marketingFlowAdditionalConfigEntity = marketingFlowAdditionalConfigDao.getCurrentByBpmFlowId(ei, id);
        Preconditions.checkState(marketingFlowAdditionalConfigEntity.getStartupType() == MarketingFlowStartupType.MANUAL.getType());
        Preconditions.checkState(!StringUtils.isNullOrEmpty(marketingFlowAdditionalConfigEntity.getMarketingUserGroupId()));
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final String marketingUserGroupId = marketingFlowAdditionalConfigEntity.getMarketingUserGroupId();
        ThreadPoolUtils.execute(() -> {
            try {
                Collection<String> userMarketingIds = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(ea, Arrays.asList(marketingUserGroupId));
                marketingFlowInstanceManager.startInstances(ei, userMarketingIds, Arrays.asList(id));
            } catch (Exception e) {
                log.error("Error occurred at start all flow instance, flowId:{}", id, e);
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess();
    }

    private Integer doCreateMarketingFlowAdditionalConfig(Integer ei, BpmFlowIdAndWorkflowId bpmFlowIdAndWorkflowId, MarketingFlowAdditionalConfigVO marketingFlowAdditionalConfigVO) {
        marketingFlowAdditionalConfigDao.updateAllFlowStatusByBpmFlowId(ei, bpmFlowIdAndWorkflowId.getBpmFlowId(), MarketingFlowStatus.SNAPSHOT.getStatus());
        MarketingFlowAdditionalConfigEntity entity = doBuildMarketingFlowAdditionalConfigEntity(ei, bpmFlowIdAndWorkflowId, marketingFlowAdditionalConfigVO);
        int result = marketingFlowAdditionalConfigDao.insert(entity);
        if (1 == result && MarketingFlowStartupType.USER_GROUP_GROW.getType() == entity.getStartupType() && !StringUtils.isNullOrEmpty(entity.getMarketingUserGroupId())) {
            this.doAsyncInitExcludedFlowUser(ei, entity.getBpmFlowId(), entity.getMarketingUserGroupId(), entity.getStartFlowForExistedMarketingGroupUser());
        }
        return result;
    }

    private void doAsyncInitExcludedFlowUser(Integer ei, String bpmFlowId, String marketingUserGroupId, Boolean startFlowForExistedMarketingGroupUser) {
        ThreadPoolUtils.execute(() -> {
            String ea = eieaConverter.enterpriseIdToAccount(ei);
            Collection<String> userMarketingAccountIds = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(ea, Lists.newArrayList(marketingUserGroupId));
            if (CollectionUtils.isNotEmpty(userMarketingAccountIds)) {
                for (String userMarketingAccountId : userMarketingAccountIds) {
                    try {
                        ExcludedFlowUserEntity entity = new ExcludedFlowUserEntity();
                        entity.setId(UUIDUtil.getUUID());
                        entity.setEa(ea);
                        entity.setBpmFlowId(bpmFlowId);
                        entity.setUserMarketingAccountId(userMarketingAccountId);
                        entity.setExcludedReason(ExcludedReasonEnum.INIT_EXCLUDE.getReason());
                        boolean notExisted = excludedFlowUserDao.insertIgnore(entity) == 1;
                        if (BooleanUtils.isTrue(startFlowForExistedMarketingGroupUser) && notExisted) {
                            log.info("doAsyncInitExcludedFlowUser startFlowForExistedMarketingGroupUser true, ea:{}, bpmFlowId:{}, userMarketingAccountId:{}", ei, bpmFlowId, userMarketingAccountId);
                            marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), Sets.newHashSet(bpmFlowId));
                        }
                    } catch (Exception ex) {
                        log.warn("doAsyncInitExcludedFlowUser warn ,ea :{},bpmFlowId :{},userMarketingAccountId :{},excludedReason :{},startFlowForExistedMarketingGroupUser:{} ", ea, bpmFlowId, userMarketingAccountId, ExcludedReasonEnum.INIT_EXCLUDE.getReason(), startFlowForExistedMarketingGroupUser);
                    }
                }
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    private MarketingFlowAdditionalConfigEntity doBuildMarketingFlowAdditionalConfigEntity(Integer ei, BpmFlowIdAndWorkflowId bpmFlowIdAndWorkflowId,
                                                                                           MarketingFlowAdditionalConfigVO marketingFlowAdditionalConfigVO) {
        MarketingFlowAdditionalConfigEntity marketingFlowAdditionalConfigEntity = BeanUtil.copy(marketingFlowAdditionalConfigVO, MarketingFlowAdditionalConfigEntity.class);
        marketingFlowAdditionalConfigEntity.setEi(ei);
        marketingFlowAdditionalConfigEntity.setBpmFlowId(bpmFlowIdAndWorkflowId.getBpmFlowId());
        marketingFlowAdditionalConfigEntity.setWorkflowId(bpmFlowIdAndWorkflowId.getWorkflowId());
        Date now = new Date();
        marketingFlowAdditionalConfigEntity.setCreateTime(now);
        marketingFlowAdditionalConfigEntity.setUpdateTime(now);
        return marketingFlowAdditionalConfigEntity;
    }

    private void doCreateMarketingActivityNodeAdditionalConfigs(Integer ei, Integer fsUserId, BpmFlowIdAndWorkflowId bpmFlowIdAndWorkflowId,
                                                                List<MarketingActivityNodeAdditionalConfigVO> marketingActivityNodeAdditionalConfigList) {
        List<MarketingActivityNodeAdditionalConfigEntity> marketingActivityNodeAdditionalConfigEntities = BeanUtil
                .copy(marketingActivityNodeAdditionalConfigList, MarketingActivityNodeAdditionalConfigEntity.class);
        marketingActivityNodeAdditionalConfigEntities.forEach(entity -> {
            entity.setEi(ei);
            entity.setWorkflowId(bpmFlowIdAndWorkflowId.getWorkflowId());
            if (MarketingActivityNodeType.SEND_WX_MESSAGE.getType() == entity.getMarketingActivityNodeType() && WxCustomerMessageType.IMAGE.getType() == entity.getWxMessageType()) {
                if (entity.getWxMessageContent().startsWith(TEMP_N_WAREHOUSE_TYPE)) {
                    entity.setWxMessageContent(fileV2Manager.convertTNFileToNFile(eieaConverter.enterpriseIdToAccount(ei), fsUserId, entity.getWxMessageContent()));
                }else if (entity.getWxMessageContent().startsWith(A_WAREHOUSE_TYPE)){
                    String ext = "png";
                    String[] rex = entity.getWxMessageContent().split("\\.");
                    if (rex != null && rex.length == 2){
                        ext = rex[1];
                    }
                    byte[] byts = fileV2Manager.downloadAFile(entity.getWxMessageContent(), eieaConverter.enterpriseIdToAccount(ei));
                    FileV2Manager.FileManagerPicResult pathResult = fileV2Manager.uploadToNPath(byts, ext, eieaConverter.enterpriseIdToAccount(ei), fsUserId);
                    if (pathResult != null && pathResult.getNPath() != null){
                        entity.setWxMessageContent(pathResult.getNPath());
                    }
                }
            }
            if (entity.getEmail() != null && CollectionUtils.isNotEmpty(entity.getEmail().getAttachments())) {
                entity.getEmail().getAttachments().forEach(e -> {
                    String ea = eieaConverter.enterpriseIdToAccount(ei);
                    String path = fileV2Manager.getApathByTApath(ea, fsUserId, e.getAPath(), e.getExt(), "fs-mankeep-provider");
                    e.setAPath(path);
                });
            }
            Date now = new Date();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
        });
        marketingActivityNodeAdditionalConfigDao.batchInsert(marketingActivityNodeAdditionalConfigEntities);
    }

    private void doUpdateMarketingFlowAdditionalConfig(Integer ei, BpmFlowIdAndWorkflowId bpmFlowIdAndWorkflowId, MarketingFlowAdditionalConfigVO marketingFlowAdditionalConfigVO) {
        MarketingFlowAdditionalConfigEntity oldAdditionalConfig = marketingFlowAdditionalConfigDao.getCurrentByBpmFlowId(ei, bpmFlowIdAndWorkflowId.getBpmFlowId());
        marketingFlowAdditionalConfigDao.updateAllFlowStatusByBpmFlowId(ei, bpmFlowIdAndWorkflowId.getBpmFlowId(), MarketingFlowStatus.SNAPSHOT.getStatus());
        MarketingFlowAdditionalConfigEntity newMarketingFlowAdditionalConfigEntity = doBuildMarketingFlowAdditionalConfigEntity(ei, bpmFlowIdAndWorkflowId, marketingFlowAdditionalConfigVO);
        newMarketingFlowAdditionalConfigEntity.setStatus(oldAdditionalConfig.getStatus());
        int result = marketingFlowAdditionalConfigDao.insert(newMarketingFlowAdditionalConfigEntity);
        if (1 == result && MarketingFlowStartupType.USER_GROUP_GROW.getType() == newMarketingFlowAdditionalConfigEntity.getStartupType() && !StringUtils.isNullOrEmpty(newMarketingFlowAdditionalConfigEntity.getMarketingUserGroupId())) {
            this.doAsyncInitExcludedFlowUser(ei, newMarketingFlowAdditionalConfigEntity.getBpmFlowId(), newMarketingFlowAdditionalConfigEntity.getMarketingUserGroupId(), newMarketingFlowAdditionalConfigEntity.getStartFlowForExistedMarketingGroupUser());
        }
    }

    private BpmFlowIdAndWorkflowId doCreateBpmFlow(Integer ei, Integer fsUserId, Map<String, Object> rawDefinition) {
        rawDefinition.put(BpmFlowField.SUPPORT_FLOW.getFieldName(), "market");
        BpmResult<IdResult> bpmResult = bpmFlowApiService.createDefinition(ei, fsUserId, rawDefinition);
        if (!bpmResult.isSuccess()) {
            throw new OuterServiceRuntimeException(bpmResult.getCode(), bpmResult.getMessage());
        }
        final String bpmFlowId = bpmResult.getData().getId();
        final String workflowId = doGeWorkflowIdByBpmFlowId(ei, bpmFlowId);
        return new BpmFlowIdAndWorkflowId(bpmFlowId, workflowId);
    }

    private BpmFlowIdAndWorkflowId doUpdateBpmFlow(Integer ei, Integer fsUserId, Map<String, Object> rawDefinition) {
        final String bpmFlowId = new MapHelper<>(rawDefinition).getString(BpmFlowField.ID.getFieldName());
        rawDefinition.put(BpmFlowField.SUPPORT_FLOW.getFieldName(), "market");
        BpmResult<IdResult> bpmResult = bpmFlowApiService.updateDefinition(ei, fsUserId, rawDefinition);
        if (!bpmResult.isSuccess()) {
            throw new OuterServiceRuntimeException(bpmResult.getCode(), bpmResult.getMessage());
        }
        final String workflowId = doGeWorkflowIdByBpmFlowId(ei, bpmFlowId);
        return new BpmFlowIdAndWorkflowId(bpmFlowId, workflowId);
    }

    private String doGeWorkflowIdByBpmFlowId(Integer ei, String bpmFlowId) {
        IdArg idArg = new IdArg(bpmFlowId, "market");
        BpmResult<Map<String, Object>> bpmResult = bpmFlowApiService.getDefinition(ei, SUPER_USER, idArg);
        if (!bpmResult.isSuccess()) {
            throw new OuterServiceRuntimeException(bpmResult.getCode(), bpmResult.getMessage());
        }
        MapHelper<String, Object> outlineHelper = MapHelper.fromMap(bpmResult.getData());
        MapHelper<String, Object> definitionHelper = outlineHelper.getMapHelper("outline");
        return definitionHelper.getString(BpmFlowField.WORKFLOW_ID.getFieldName());
    }

    @Override
    public Result<MarketingFlowStatisticResult> getAllStatisticData(String ea, Date startDate, Date endDate) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        MarketingFlowStatisticResult result = new MarketingFlowStatisticResult();
        List<DateTrendData> startInstanceDateTrendData = marketingFlowDayStatisticDao.listEnterpriseStartInstanceCountDateTrendData(ea, startDate, endDate);
        List<DayTrendData> startInstanceDayTrendData = DateTrendData.dateTrendDataToDayTrendData(startDate, endDate, startInstanceDateTrendData);
        result.setStartInstanceTrends(startInstanceDayTrendData);
        result.setDateRangeStartInstanceCount(DayTrendData.sumAll(startInstanceDayTrendData));

        List<DateTrendData> endInstanceDateTrendData = marketingFlowDayStatisticDao.listEnterpriseEndInstanceCountDateTrendData(ea, startDate, endDate);
        List<DayTrendData> endInstanceDayTrendData = DateTrendData.dateTrendDataToDayTrendData(startDate, endDate, endInstanceDateTrendData);
        result.setEndInstanceTrends(endInstanceDayTrendData);
        result.setDateRangeEndInstanceCount(DayTrendData.sumAll(endInstanceDayTrendData));

        MarketingFlowInstanceAmountStatisticData amountStatisticData = marketingFlowDayStatisticDao.sumInstanceStatisticDataByEa(ea);
        if (amountStatisticData != null) {
            result.setAmountStartInstanceCount(amountStatisticData.getAmountStartInstanceCount());
            result.setAmountEndInstanceCount(amountStatisticData.getAmountEndInstanceCount());
        }

        result.setFlowCount(marketingFlowAdditionalConfigDao.countFlows(ei));
        result.setDraftCount(marketingFlowDraftDao.countDraft(ei));

        return Result.newSuccess(result);
    }

    @Override
    public Result<EnabledFlowCountResult> countEnabledFlow(Integer ei) {
        return Result.newSuccess(new EnabledFlowCountResult(marketingFlowAdditionalConfigDao.countEnabledFlows(ei), 10));
    }

    @Override
    public Result<Void> startBySpecialDate(Integer ei, String id, Long date) {
        MarketingFlowAdditionalConfigEntity marketingFlowAdditionalConfig = marketingFlowAdditionalConfigDao.getCurrentByBpmFlowId(ei, id);
        Preconditions.checkState(marketingFlowAdditionalConfig.getStartupType() == MarketingFlowStartupType.SPECIAL_DATE.getType());
        Preconditions.checkState(!StringUtils.isNullOrEmpty(marketingFlowAdditionalConfig.getMarketingUserGroupId()));
        Preconditions.checkState(!StringUtils.isNullOrEmpty(marketingFlowAdditionalConfig.getCommonObjectApiName()));
        Preconditions.checkState(!StringUtils.isNullOrEmpty(marketingFlowAdditionalConfig.getCommonObjectFieldApiName()));
        Preconditions.checkState(marketingFlowAdditionalConfig.getBeforeSpecialDayStart() != null);
        MarketingUserGroupEntity entity = marketingUserGroupDao.getById(marketingFlowAdditionalConfig.getMarketingUserGroupId());

        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(entity.getEa()) || appVersionManager.getCurrentAppVersion(entity.getEa()) == null){
            log.info("MarketingFlowServiceImpl.startBySpecialDate failed enterprise stop or license expire ea:{}", entity.getEa());
            return Result.newSuccess();
        }
        List<FilterData> filters = entity.getRuleGroupJson().stream().map(val -> BeanUtil.copyByGson(val, FilterData.class)).collect(Collectors.toList());
        List<FilterData> matchedFilterDatas = Lists.newArrayList();
        for (FilterData filterData : filters) {
            if (marketingFlowAdditionalConfig.getCommonObjectApiName().equals(filterData.getObjectAPIName())) {
                matchedFilterDatas.add(filterData);
            }
        }
        if (CollectionUtils.isEmpty(matchedFilterDatas)) {
            return Result.newSuccess();
        }
        for (FilterData matchedFilterData : matchedFilterDatas) {
            Filter dateFilter = new Filter();
            dateFilter.setFieldName(marketingFlowAdditionalConfig.getCommonObjectFieldApiName());
            dateFilter.setOperator("BETWEEN");
            List<String> dateRange = new ArrayList<>();
            DateTime dateTime = new DateTime(date);
            dateTime = dateTime.plusDays(marketingFlowAdditionalConfig.getBeforeSpecialDayStart());
            dateRange.add(dateTime.withTimeAtStartOfDay().getMillis() + "");
            dateRange.add(dateTime.plusDays(1).withTimeAtStartOfDay().getMillis() - 1 + "");
            dateFilter.setFieldValues(dateRange);
            matchedFilterData.getQuery().getFilters().add(dateFilter);
        }
        List<String> userMarketingIds = userMarketingAccountManager.getMarketingUserIdsByFilterDataList(eieaConverter.enterpriseIdToAccount(ei), -10000, null, matchedFilterDatas, null, null);
        if (userMarketingIds != null && !userMarketingIds.isEmpty() && !StringUtils.isNullOrEmpty(id)) {
            marketingFlowInstanceManager.startInstances(ei, userMarketingIds, Arrays.asList(id));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> createDraft(String ea, Integer fsUserId, CreateMarketingFlowDraftArg arg) {
        MarketingFlowDraftEntity entity = new MarketingFlowDraftEntity();
        BeanUtils.copyProperties(arg, entity);
        String id = UUIDUtil.getUUID();
        entity.setId(id);
        entity.setEi(eieaConverter.enterpriseAccountToId(ea));
        entity.setUpdater(fsUserId);
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        marketingFlowDraftDao.insert(entity);
        return Result.newSuccess(id);
    }

    @Override
    public Result<Void> updateDraft(String ea, Integer fsUserId, UpdateMarketingFlowDraftArg arg) {
        marketingFlowDraftDao.updateDraft(eieaConverter.enterpriseAccountToId(ea), fsUserId, arg.getId(), arg.getName(), arg.getDescribe(), arg.getDraftDefinition());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteDraft(String ea, Integer fsUserId, String draftId) {
        marketingFlowDraftDao.deleteByEiAndId(eieaConverter.enterpriseAccountToId(ea), draftId);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<MarketingFlowDraftSimpleVO>> listDraft(String ea, Integer fsUserId, ListMarketingFlowDraftArg arg) {
        Page<MarketingFlowDraftEntity> page = new Page<>(arg.getPageNo(), arg.getPageSize(), true);
        if (StringUtils.isNullOrEmpty(arg.getName())) {
            arg.setName(null);
        }
        List<MarketingFlowDraftEntity> entities = marketingFlowDraftDao.listByName(eieaConverter.enterpriseAccountToId(ea), arg.getName(), page);
        List<MarketingFlowDraftSimpleVO> marketingFlowDraftSimpleVOS = entities.stream().map(entity -> {
            MarketingFlowDraftSimpleVO simpleVO = new MarketingFlowDraftSimpleVO();
            BeanUtils.copyProperties(entity, simpleVO, "updateTime");
            simpleVO.setUpdateTime(entity.getUpdateTime().getTime());
            return simpleVO;
        }).collect(Collectors.toList());
        PageResult<MarketingFlowDraftSimpleVO> pageResult = new PageResult<>(page.getTotalNum(), marketingFlowDraftSimpleVOS);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<MarketingFlowDraftVO> getDraft(String ea, Integer fsUserId, String draftId) {
        MarketingFlowDraftEntity entity = marketingFlowDraftDao.getById(eieaConverter.enterpriseAccountToId(ea), draftId);
        MarketingFlowDraftVO draftVO = new MarketingFlowDraftVO();
        BeanUtils.copyProperties(entity, draftVO, "updateTime");
        draftVO.setUpdateTime(entity.getUpdateTime().getTime());
        return Result.newSuccess(draftVO);
    }

    @Override
    public Result<Void> startByUserGroupGrowFlow(Integer ei, String bpmFlowId, String workflowId) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
            log.info("MarketingFlowServiceImpl.startByUserGroupGrowFlow failed enterprise stop or license expire ea:{}", ea);
            return Result.newSuccess();
        }

        MarketingFlowAdditionalConfigEntity entity = marketingFlowAdditionalConfigDao.getByBpmFlowIdAndWorkflowId(ei, bpmFlowId, workflowId);
        if (entity.getStartupType() == MarketingFlowStartupType.USER_GROUP_GROW.getType()) {
            Collection<String> userMarketingAccountIds = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(ea, Lists.newArrayList(entity.getMarketingUserGroupId()));
            for (String userMarketingAccountId : userMarketingAccountIds) {
                try {
                    ExcludedFlowUserEntity excludedFlowUserEntity = new ExcludedFlowUserEntity();
                    excludedFlowUserEntity.setId(UUIDUtil.getUUID());
                    excludedFlowUserEntity.setEa(ea);
                    excludedFlowUserEntity.setBpmFlowId(bpmFlowId);
                    excludedFlowUserEntity.setUserMarketingAccountId(userMarketingAccountId);
                    excludedFlowUserEntity.setExcludedReason(ExcludedReasonEnum.PROCESS_EXCLUDE.getReason());
                    boolean notExisted = excludedFlowUserDao.insertIgnore(excludedFlowUserEntity) == 1;
                    if (notExisted){
                        marketingFlowInstanceManager.startInstances(ei, Collections.singleton(userMarketingAccountId), Sets.newHashSet(bpmFlowId));
                    }
                } catch (Exception ex) {
                    log.warn("doAsyncInitExcludedFlowUser warn ,ea :{},bpmFlowId :{},userMarketingAccountId :{},excludedReason :{} ", ea, bpmFlowId, userMarketingAccountId, ExcludedReasonEnum.INIT_EXCLUDE.getReason());
                }
            }
        }
        return Result.newSuccess();
    }

    @Getter
    private static class BpmFlowIdAndWorkflowId {
        private final String bpmFlowId;
        private final String workflowId;

        public BpmFlowIdAndWorkflowId(String bpmFlowId, String workflowId) {
            this.bpmFlowId = bpmFlowId;
            this.workflowId = workflowId;
        }
    }
}
