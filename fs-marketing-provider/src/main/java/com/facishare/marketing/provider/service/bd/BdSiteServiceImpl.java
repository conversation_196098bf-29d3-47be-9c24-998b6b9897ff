package com.facishare.marketing.provider.service.bd;

import com.facishare.marketing.api.result.bd.GetBdSiteListResult;
import com.facishare.marketing.api.service.bd.BdSiteService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.dao.bd.BdSiteDAO;
import com.facishare.marketing.provider.entity.bd.BdSiteEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.github.mybatis.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("bdSiteService")
@Slf4j
public class BdSiteServiceImpl implements BdSiteService {

    @Autowired
    private BdSiteDAO bdSiteDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private CampaignDataManager campaignDataManager;

    @Override
    public Result<PageResult<GetBdSiteListResult>> getList(Integer pageSize, Integer pageNum, Long time, String searchFitter) {
        time = (time == null ? new Date().getTime() : time);
        Page page = new Page(pageNum, pageSize, true);

        List<GetBdSiteListResult> resultList = new ArrayList<>();

        List<BdSiteEntity> bdSiteEntityList = bdSiteDAO.getList(page, searchFitter);
        if (CollectionUtils.isNotEmpty(bdSiteEntityList)) {
            List<String> apathList = new ArrayList<>();
            String ea = null;
            for (BdSiteEntity bdSiteEntity : bdSiteEntityList) {
                apathList.add(bdSiteEntity.getIconApath());
            }

            Map<String, String> iconUrlMap = fileV2Manager.batchGetUrlByPath(apathList, ea, false);

            for (BdSiteEntity bdSiteEntity : bdSiteEntityList) {
                GetBdSiteListResult getBdSiteListResult = BeanUtil.copy(bdSiteEntity, GetBdSiteListResult.class);
                if (null != iconUrlMap) {
                    getBdSiteListResult.setIconUrl(iconUrlMap.get(bdSiteEntity.getIconApath()));
                }

                resultList.add(getBdSiteListResult);
            }

        }

        PageResult<GetBdSiteListResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result refreshPeriodically() {
        return Result.newSuccess();
    }

    @Override
    public Result<Void> refreshPeriodicallyByStatusId(String statusId) {
        return campaignDataManager.refreshPeriodicallyByStatusId(statusId);
    }
}
