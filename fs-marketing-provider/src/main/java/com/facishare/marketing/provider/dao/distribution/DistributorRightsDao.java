package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.provider.entity.distribution.DistributorRightsEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created by ranluch on 2019/5/28.
 */
public interface DistributorRightsDao {
    @Insert("INSERT INTO distributor_rights(id, type, grade, value1, value2, remark)\n"
        + "        VALUES ( #{obj.id}, #{obj.type}, #{obj.grade}, #{obj.value1}, #{obj.value2}, #{obj.remark})")
    boolean addDistributorRights(@Param("obj") DistributorRightsEntity distributorRightsEntity);

    @Select("SELECT * FROM distributor_rights WHERE id = #{id}")
    DistributorRightsEntity queryDistributorRights(@Param("id") String id);

    @Select("<script>"
        + "SELECT * FROM distributor_rights\n"
        + "WHERE id in\n"
        +  "<foreach collection=\"rightsIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
        +  "     #{item}\n"
        +  "</foreach>"
        +"</script>")
    List<DistributorRightsEntity> queryDistributorRightsByIds(@Param("rightsIds") List<String> rightsIds);
}
