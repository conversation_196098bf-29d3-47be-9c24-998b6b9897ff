package com.facishare.marketing.provider.service.fileLibrary;

import com.facishare.fsi.proxy.model.warehouse.a.ADeleteFiles;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NClearFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NCreateNoPermissionFile;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.fileLibrary.*;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileByGroupResult;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.fileLibrary.FileLibraryService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTagVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.fileLibrary.FileDefaultGroupEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qywx.QywxFileTypeLogoEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.DisplayOrderDao;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.file.FileQueryParam;
import com.facishare.marketing.provider.dto.GroupNameObjectIdDTO;
import com.facishare.marketing.provider.dto.file.FileEntityDTO;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.ObjectGroupRelationEntity;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.MaterialTagManager;
import com.facishare.marketing.provider.manager.ObjectGroupManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("fileLibraryService")
@Slf4j
public class FileLibraryServiceImpl implements FileLibraryService{
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;
    @Autowired
    private ObjectGroupManager objectGroupManager;
    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;
    @Autowired
    private ObjectGroupDAO objectGroupDAO;
    @Autowired
    private DisplayOrderDao displayOrderDao;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private NFileStorageService nFileStorageService;
    @Autowired
    private AFileStorageService aFileStorageService;
    @Autowired
    private IntegralServiceManager integralServiceManager;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;
    @Autowired
    private MaterialTagManager materialTagManager;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    public static final int CUSTOME_FILE = 0;
    public static final int SYSTEM_FILE = 1;

    private static final int QUERY_ALL_GROUP = 0;
    private static final int QUERY_ALL_CUSTOM_GROUP = 1;
    private static final int QUERY_CUSTOM_GROUP_WITH_ITEM = 2;

    @Override
    public Result<Void> uploadFile(String ea, Integer fsUserId, UploadFileArg arg) {
        Map<String, String> pathMap = new HashMap<>();

        List<String> fileNames = arg.getFiles().stream().map(fileItem -> fileItem.getFileName()).collect(Collectors.toList());
        Set<String> exsitName = fileLibraryDAO.queryFileExistName(ea, fileNames);
        if (CollectionUtils.isNotEmpty(exsitName)){
            return Result.newError(SHErrorCode.FILE_NAME_EXIST);
        }

        for (UploadFileArg.FileItem fileItem : arg.getFiles()){
            String path = null;
            if (!exsitName.contains(fileItem.getFileName())) {
                if (fileItem.getFilePath().startsWith("TN_")) {
                    path = fileV2Manager.changeNWarehouseTempToPermanentBybusiness(ea, fsUserId, fileItem.getExt(), fileItem.getFilePath(), "fs-marketing");
                } else if (fileItem.getFilePath().startsWith("TA_")) {
                    path = fileV2Manager.changeAWarehouseTempToPermanentBybusiness(ea, fsUserId, fileItem.getExt(), fileItem.getFilePath(), "fs-marketing");
                }
                pathMap.putIfAbsent(fileItem.getFilePath(), path);
            }
        }

        List<FileEntity> fileEntities = Lists.newArrayList();
        for (UploadFileArg.FileItem fileItem : arg.getFiles()){
            if (CollectionUtils.isEmpty(exsitName) || !exsitName.contains(fileItem.getFileName())) {
                FileEntity entity = new FileEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(ea);
                entity.setCreateBy(fsUserId);
                entity.setFileName(fileItem.getFileName());
                entity.setFilePath(pathMap.get(fileItem.getFilePath()));
                entity.setExt(fileItem.getExt());
                entity.setFileSize(fileItem.getFileSize());
                entity.setType(CUSTOME_FILE);
                fileEntities.add(entity);
            }
        }

        if (CollectionUtils.isNotEmpty(fileEntities)) {
            fileLibraryDAO.batchInsert(fileEntities);
        }
        
        // 上传文件指定分组
        fileEntities.forEach(fileEntity -> {
            SetFileGroupArg setFileGroupArg = new SetFileGroupArg();
            setFileGroupArg.setFileId(fileEntity.getId());
            setFileGroupArg.setGroupId(arg.getGroupId());
            setFileGroup(ea, fsUserId, setFileGroupArg);

            //上报神策系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.FILE_LIBRARY.getType() , null, fileEntity.getId()));
            //文件注册到积分系统
            integralServiceManager.asyncRegisterMaterial(ea, CategoryApiNameConstant.FILE, fileEntity.getId(), fileEntity.getFileName());
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), fileEntity.getFileName(), OperateTypeEnum.ADD);

        });

        return Result.newSuccess();
    }

    @Override
    public Result<Void> renameFile(String ea, Integer fsUserId, RenameFileArg arg) {
        FileEntity entity = fileLibraryDAO.getById(arg.getId());
        if (entity == null){
            log.info("FileLibraryServiceImpl.renameFile failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        if (entity.getType() == SYSTEM_FILE){
            log.info("FileLibraryServiceImpl.renameFile can not delete system file ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.CANNOT_RENAME_SYSTEM_FILE);
        }

        if (StringUtils.equals(arg.getFileName(), entity.getFileName())){
            return Result.newSuccess();
        }

        if (!StringUtils.equals(arg.getFileName(), entity.getFileName())){
            int count = fileLibraryDAO.queryFileCountByName(ea, arg.getFileName());
            if (count > 0){
                return Result.newError(SHErrorCode.FILE_NAME_EXIST);
            }
        }

        fileLibraryDAO.updateFileNameById(ea, arg.getId(), arg.getFileName());
        //文件注册到积分系统
        integralServiceManager.asyncRegisterMaterial(ea, CategoryApiNameConstant.FILE,entity.getId(),entity.getFileName());
        return Result.newSuccess();
    }

    @Override
    public Result<EditObjectGroupResult> editFileGroup(String ea, Integer fsUserId, EditFileGroupArg arg) {
        List<String> defaultNames = FileDefaultGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("FileLibraryServiceImpl.editFileGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }

        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.FILE.getType());
    }

    @Override
    public Result<ObjectGroupListResult> listFileGroup(String ea, Integer fsUserId, ListFileGroupArg arg) {
        if (arg.getUseType() == QUERY_ALL_GROUP){
            return listAllFileGroup(ea, fsUserId, arg);
        }else if (arg.getUseType() == QUERY_ALL_CUSTOM_GROUP){
            return listAllCustomFileGroup(ea, fsUserId, arg.getMenuId());
        }else {
            return listAllGroupWithItem(ea, fsUserId, arg);
        }
    }

    @Override
    public Result<ObjectGroupListResult> listFileGroup4Outer(String upstreamEA, String outTenantId, String outUserId, ListFileGroupArg arg) {
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup4Outer(upstreamEA, outTenantId, outUserId, ObjectTypeEnum.FILE.getType());
        return Result.newSuccess(customizeGroupListVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteFileGroup(String ea, Integer fsUserId, DeleteFileGroupArg arg) {
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.FILE.getType());
    }

    @Override
    @Transactional
    public Result deleteFile(String ea, Integer fsUserId, DeleteFileArg arg) {
        FileEntity entity = fileLibraryDAO.getById(arg.getId());
        if (entity == null){
            log.info("FileLibraryServiceImpl.deleteFile failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        if (entity.getType() == SYSTEM_FILE){
            log.info("FileLibraryServiceImpl.deleteFile can not delete system file ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.CANNOT_DELETE_SYSTEM_FILE);
        }

        objectGroupRelationDAO.deleteObjectFromObjectGroupRelation(ea, arg.getId(), ObjectTypeEnum.FILE.getType());
        fileLibraryDAO.deleteById(ea, arg.getId());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.FILE.getType(), Collections.singletonList(arg.getId()));
        try {
            if (entity.getFilePath() != null) {
                if (entity.getFilePath().startsWith("A_")) {
//                    ADeleteFiles.Arg fileArg = new ADeleteFiles.Arg();
//                    fileArg.setBusiness("fs-mankeep-provider");
//                    List<String> paths = Lists.newArrayList();
//                    paths.add(entity.getFilePath());
//                    fileArg.setaPathList(paths);
//                    User user = new User();
//                    user.setEmployId(fsUserId);
//                    user.setEnterpriseAccount(ea);
//                    fileArg.setUser(user);
                    //aFileStorageService.deleteFiles(fileArg);
                } else {
//                    NClearFile.Arg fileArg = new NClearFile.Arg();
//                    fileArg.setEa(ea);
//                    fileArg.setNPathList(Collections.singletonList(entity.getFilePath()));
//                    fileArg.setBusiness("fs-mankeep-provider");
                    //nFileStorageService.nClearFile(fileArg, ea);
                }
            }
        }catch (Exception e){
            log.info("FileLibraryServiceImpl.deleteFile delete file from warehouse failed ea:{} fsUserId:{} arg:{} e:", ea, fsUserId, arg);
        }

        //从积分系统删除文件
        integralServiceManager.asyncRemoveMaterial(ea, CategoryApiNameConstant.FILE, entity.getId());
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), entity.getFileName(), OperateTypeEnum.DELETE);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<ListFileByGroupResult>> listFileByGroup(String ea, Integer fsUserId, ListFileByGroupArg arg) {
        if (StringUtils.isBlank(arg.getGroupId())) {
            arg.setGroupId(FileDefaultGroupEnum.ALL.getId());
        }
        PageResult<ListFileByGroupResult> pageResult = new PageResult<>();
        List<ListFileByGroupResult> fileListResult = Lists.newArrayList();
        pageResult.setResult(fileListResult);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        // 获取菜单的设置详情
        Result<AppMenuTagVO> appMenuTagVOResult = appMenuTemplateService.getMenuTagRule(ea, arg.getMenuId(), ObjectTypeEnum.FILE.getType());
        AppMenuTagVO appMenuTagVO = appMenuTagVOResult.isSuccess() ? appMenuTagVOResult.getData() : null;

        FileQueryParam queryParam = new FileQueryParam();
        queryParam.setKeyword(arg.getKeyword());
        queryParam.setEa(ea);
        queryParam.setUserId(fsUserId);
        queryParam.setMaterialTagFilter(arg.getMaterialTagFilter());
        queryParam.setExt(arg.getExt());
        List<FileEntityDTO> pageList = null;
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        if (appMenuTagVO != null) {
            MaterialTagFilterArg materialTagFilterArg = queryParam.getMaterialTagFilter();
            if (materialTagFilterArg == null) {
                materialTagFilterArg = new MaterialTagFilterArg();
            }
            materialTagFilterArg.setMenuType(appMenuTagVO.getTagOperator());
            materialTagFilterArg.setMenuMaterialTagIds(appMenuTagVO.getTagIdList());
            queryParam.setMaterialTagFilter(materialTagFilterArg);
            queryParam.setStrictCheckGroup(StringUtils.isNotBlank(arg.getMenuId()));
            queryParam.setStrictCheckGroup(true);
            pageList = fileLibraryDAO.getAccessiblePage(queryParam, page);
        } else if (arg.getGroupId().equals(FileDefaultGroupEnum.ALL.getId())){
            //pageList = fileLibraryDAO.pageQueryFileByAll(ea,arg.getKeyword(), page);
            // 是否需要严格校验分组
            queryParam.setStrictCheckGroup(appMenuTemplateService.needStrictCheckGroup(ea, fsUserId, arg.getMenuId()));
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.FILE.getType(), arg.getMenuId());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            queryParam.setPermissionGroupIdList(permissionGroupIdList);
            pageList = fileLibraryDAO.getAccessiblePage(queryParam, page);
        }else if (arg.getGroupId().equals(FileDefaultGroupEnum.NO_GROUP.getId())){
            pageList = fileLibraryDAO.pageQueryFileByUnGrouped(ea, arg.getKeyword(),arg.getExt(), ObjectTypeEnum.FILE.getType(), arg.getMaterialTagFilter(), page);
        } else if (FileDefaultGroupEnum.CREATE_BY_ME.getId().equals(arg.getGroupId())) {
            pageList = fileLibraryDAO.pageQueryFileCreatedByMe(ea, fsUserId, arg.getKeyword(),arg.getExt(),  arg.getMaterialTagFilter(), page);
        } else {
            //pageList = fileLibraryDAO.pageQueryFileByGroup(ea, arg.getGroupId(), arg.getKeyword(), page);
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroupByMenuId(ea, fsUserId, ObjectTypeEnum.FILE.getType(), arg.getMenuId());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            queryParam.setStrictCheckGroup(true);
            if (!permissionGroupIdSet.contains(arg.getGroupId())){
                pageList = Lists.newArrayList();
            } else {
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.FILE.getType(), arg.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(arg.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                pageList = fileLibraryDAO.getAccessiblePage(queryParam, page);
            }
        }
        List<String> fileIds = pageList.stream().map(FileEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileIds)){
            return Result.newSuccess(pageResult);
        }

        pageResult.setTotalCount(page.getTotalNum());
        List<GroupNameObjectIdDTO> groupNameObjectIdDTOList = objectGroupRelationDAO.queryGroupNameByObjectIds(ea, ObjectTypeEnum.FILE.getType(), fileIds);
        Map<String, GroupNameObjectIdDTO> objectIdDTOMap = null;
        if (CollectionUtils.isNotEmpty(groupNameObjectIdDTOList)){
            objectIdDTOMap = groupNameObjectIdDTOList.stream().collect(Collectors.toMap(GroupNameObjectIdDTO::getObjectId, Function.identity(), (v1, v2)->v2));
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> allFSEmployeeMsgMap = null;
        List<Integer> userIdList = pageList.stream().map(FileEntity::getCreateBy).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(userIdList)) {
            allFSEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIdList, true);
        }

        List<String> paths = pageList.stream().map(FileEntity::getFilePath).collect(Collectors.toList());
//        Map<String, String> pathFileNameMap = pageList.stream().filter(fileEntity -> fileEntity.getFileName() == null).collect(Collectors.toMap(FileEntity::getFilePath, FileEntity::getFileName, (v1,v2)->v2));
        Map<String, String> pathFileNameMap = Maps.newHashMap();
        for (FileEntityDTO fileEntity : pageList) {
            if (fileEntity.getFileName() == null) {
                pathFileNameMap.put(fileEntity.getFilePath(), null);
            }
        }
        Map<String, String> pathPreviewMap = fileV2Manager.batchGetPreviewUrl(ea, fsUserId, paths);
        Map<String, String> pathDownloadMap = fileV2Manager.batchGetDownloadFileUrl(ea, fsUserId, paths, pathFileNameMap);
    //    Map<String, String> pathNpathMap = fileV2Manager.batchGetNPath(ea, fsUserId, paths);
        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(fileIds, ObjectTypeEnum.FILE.getType());
        for (FileEntityDTO entity : pageList){
            ListFileByGroupResult fileByGroupResult = new ListFileByGroupResult();
            fileByGroupResult.setFileName(entity.getFileName());
            fileByGroupResult.setFilePath(entity.getFilePath());
    //        fileByGroupResult.setFileNPath(pathNpathMap.get(entity.getFilePath()));
            fileByGroupResult.setFileSize(entity.getFileSize());
            fileByGroupResult.setExt(entity.getExt());
            fileByGroupResult.setId(entity.getId());
            fileByGroupResult.setCreateTime(entity.getCreateTime().getTime());
            fileByGroupResult.setTagNames(entity.getTagNames());
            fileByGroupResult.setTop(entity.isTop());
            if (objectIdDTOMap == null || objectIdDTOMap.get(entity.getId()) == null){
                fileByGroupResult.setGroupName(FileDefaultGroupEnum.NO_GROUP.getName());
            }else {
                fileByGroupResult.setGroupName(objectIdDTOMap.get(entity.getId()).getGroupName());
            }
            if (allFSEmployeeMsgMap != null && allFSEmployeeMsgMap.get(entity.getCreateBy()) != null){
                fileByGroupResult.setCreateBy(allFSEmployeeMsgMap.get(entity.getCreateBy()).getName());
            }
            if (pathPreviewMap != null && pathPreviewMap.get(entity.getFilePath()) != null){
                fileByGroupResult.setPreviewUrl(pathPreviewMap.get(entity.getFilePath()));
            }
            if (pathDownloadMap != null && pathDownloadMap.get(entity.getFilePath()) != null){
                fileByGroupResult.setDownloadUrl(pathDownloadMap.get(entity.getFilePath()));
            }
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(entity.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                fileByGroupResult.setMaterialTags(collect);
            }
            fileListResult.add(fileByGroupResult);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<ListFileByGroupResult>> listFileByGroup4Outer(String upstreamEA, String outTenantId, String outUserId, ListFileByGroupArg arg) {
        PageResult<ListFileByGroupResult> pageResult = new PageResult<>();
        List<ListFileByGroupResult> fileListResult = Lists.newArrayList();
        pageResult.setResult(fileListResult);
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);

        List<FileEntityDTO> pageList = null;
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup4Outer(upstreamEA, outTenantId, outUserId, ObjectTypeEnum.FILE.getType());
        Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
        FileQueryParam queryParam = new FileQueryParam();
        queryParam.setKeyword(arg.getKeyword());
        queryParam.setEa(upstreamEA);
        if (Objects.equals(arg.getGroupId(), DefaultObjectGroupEnum.ALL.getId())) {
            queryParam.setGroupIdList(Lists.newArrayList(permissionGroupIdSet));
            pageList = fileLibraryDAO.getAccessiblePage4Outer(queryParam, page);
        } else {
            if (!permissionGroupIdSet.contains(arg.getGroupId())){
                pageList = Lists.newArrayList();
            } else {
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(upstreamEA, ObjectTypeEnum.FILE.getType(), arg.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(arg.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                pageList = fileLibraryDAO.getAccessiblePage4Outer(queryParam, page);
            }
        }

        List<String> fileIds = pageList.stream().map(FileEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fileIds)){
            return Result.newSuccess(pageResult);
        }

        pageResult.setTotalCount(page.getTotalNum());
        List<GroupNameObjectIdDTO> groupNameObjectIdDTOList = objectGroupRelationDAO.queryGroupNameByObjectIds(upstreamEA, ObjectTypeEnum.FILE.getType(), fileIds);
        Map<String, GroupNameObjectIdDTO> objectIdDTOMap = null;
        if (CollectionUtils.isNotEmpty(groupNameObjectIdDTOList)){
            objectIdDTOMap = groupNameObjectIdDTOList.stream().collect(Collectors.toMap(GroupNameObjectIdDTO::getObjectId, Function.identity(), (v1, v2)->v2));
        }

        Map<Integer, FsAddressBookManager.FSEmployeeMsg> allFSEmployeeMsgMap = null;
        List<Integer> userIdList = pageList.stream().map(FileEntity::getCreateBy).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(userIdList)) {
            allFSEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(upstreamEA, userIdList, true);
        }

        List<String> paths = pageList.stream().map(FileEntity::getFilePath).collect(Collectors.toList());
        Map<String, String> pathFileNameMap = pageList.stream().filter(fileEntity -> fileEntity.getFileName() == null).collect(Collectors.toMap(FileEntity::getFilePath, FileEntity::getFileName, (v1,v2)->v2));
        Map<String, String> pathPreviewMap = fileV2Manager.batchGetPreviewUrl(upstreamEA, 1000, paths);
        Map<String, String> pathDownloadMap = fileV2Manager.batchGetDownloadFileUrl(upstreamEA, 1000, paths, pathFileNameMap);
      //  Map<String, String> pathNpathMap = fileV2Manager.batchGetNPath(upstreamEA, 1000, paths);
        for (FileEntityDTO entity : pageList){
            ListFileByGroupResult fileByGroupResult = new ListFileByGroupResult();
            fileByGroupResult.setFileName(entity.getFileName());
            fileByGroupResult.setFilePath(entity.getFilePath());
        //    fileByGroupResult.setFileNPath(pathNpathMap.get(entity.getFilePath()));
            fileByGroupResult.setFileSize(entity.getFileSize());
            fileByGroupResult.setExt(entity.getExt());
            fileByGroupResult.setId(entity.getId());
            fileByGroupResult.setCreateTime(entity.getCreateTime().getTime());
            fileByGroupResult.setTagNames(entity.getTagNames());
            fileByGroupResult.setTop(entity.isTop());
            if (objectIdDTOMap == null || objectIdDTOMap.get(entity.getId()) == null){
                fileByGroupResult.setGroupName(FileDefaultGroupEnum.NO_GROUP.getName());
            }else {
                fileByGroupResult.setGroupName(objectIdDTOMap.get(entity.getId()).getGroupName());
            }
            if (allFSEmployeeMsgMap != null && allFSEmployeeMsgMap.get(entity.getCreateBy()) != null){
                fileByGroupResult.setCreateBy(allFSEmployeeMsgMap.get(entity.getCreateBy()).getName());
            }
            if (pathPreviewMap != null && pathPreviewMap.get(entity.getFilePath()) != null){
                fileByGroupResult.setPreviewUrl(pathPreviewMap.get(entity.getFilePath()));
            }
            if (pathDownloadMap != null && pathDownloadMap.get(entity.getFilePath()) != null){
                fileByGroupResult.setDownloadUrl(pathDownloadMap.get(entity.getFilePath()));
            }
            fileListResult.add(fileByGroupResult);
        }

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result setFileGroup(String ea, Integer fsUserId, SetFileGroupArg arg) {
        FileEntity entity = fileLibraryDAO.getById(arg.getFileId());
        if (entity == null){
            log.info("FileLibraryServiceImpl.setFileGroup failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }

        ObjectGroupRelationEntity objectGroupRelationEntity = objectGroupRelationDAO.getByObjectId(ea, arg.getFileId());
        if (objectGroupRelationEntity == null){
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(arg.getFileId());
            newEntity.setObjectType(ObjectTypeEnum.FILE.getType());
            objectGroupRelationDAO.insert(newEntity);
        }else {
            objectGroupRelationDAO.updateObjectGroup(ea, arg.getGroupId(), arg.getFileId(), ObjectTypeEnum.FILE.getType());
        }
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), entity.getFileName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        return Result.newSuccess();
    }

    @Override
    public Result copyFromNetShareDisk(String ea, Integer fsUserId, CopyFromNetShareDiskArg arg) {
        NCreateNoPermissionFile.Arg fileArg = new NCreateNoPermissionFile.Arg();
        fileArg.setEa(ea);
        fileArg.setBusiness("fs-marketing");
        fileArg.setFileSecurityGroup("XiaoKeNetDisk");
        fileArg.setSourceUser("E." + ea + "." + fsUserId);
        Map<String, String> fileMap = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getFiles())){
            return Result.newSuccess();
        }

        //校验文件重名
        List<String> fileNameWithExtList = Lists.newArrayList();
        for (CopyFromNetShareDiskArg.FileItem item : arg.getFiles()){
            if (item.getExt() == null){
                fileNameWithExtList.add(item.getFileName());
            }else {
                fileNameWithExtList.add(item.getFileName() + "." + item.getExt());
            }
        }
        List<String> fileName = arg.getFiles().stream().map(fileItem -> fileItem.getFileName()).collect(Collectors.toList());
        Set<String> exitFile = fileLibraryDAO.queryFileExistName(ea, fileName);
        if (CollectionUtils.isNotEmpty(exitFile)){
            return Result.newError(SHErrorCode.FILE_NAME_EXIST);
        }

        for (CopyFromNetShareDiskArg.FileItem item : arg.getFiles()){
            fileArg.setnPath(item.getPath());
            NCreateNoPermissionFile.Result result = nFileStorageService.nCreateNoPermissionFile(fileArg, ea);
            if (result == null || StringUtils.isEmpty(result.getnPath())){
                log.info("FileLibraryServiceImpl.copyFromNetShareDisk failed ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
                return Result.newError(SHErrorCode.FILE_UPLOAD_FAILED);
            }
            fileMap.putIfAbsent(item.getPath(), result.getnPath());
        }

        List<FileEntity> fileEntities = Lists.newArrayList();
        for (CopyFromNetShareDiskArg.FileItem item : arg.getFiles()){
            FileEntity entity = new FileEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setFileName(item.getFileName());
            entity.setFilePath(fileMap.get(item.getPath()));
            entity.setFileSize(item.getFileSize());
            entity.setExt(item.getExt());
            entity.setType(CUSTOME_FILE);
            fileEntities.add(entity);
        }
        fileLibraryDAO.batchInsert(fileEntities);
        //文件注册到积分系统
        for (FileEntity fileEntity : fileEntities) {
            integralServiceManager.asyncRegisterMaterial(ea, CategoryApiNameConstant.FILE, fileEntity.getId(), fileEntity.getFileName());
            //上报神策系统
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(ea, fsUserId, ObjectTypeEnum.FILE_LIBRARY.getType(), null, fileEntity.getId()));
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), fileEntity.getFileName(), OperateTypeEnum.ADD);
        }
        objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.FILE.getType(), fileEntities.stream().map(FileEntity::getId).collect(Collectors.toList()), arg.getGroupId());
        return Result.newSuccess();
    }

    @Override
    public Result<String> getPreviewUrl(String ea, Integer fsUserId, GetPreviewUrlArg arg) {
        String url = fileV2Manager.getPreviewUrl(ea, arg.getPath());
        return Result.newSuccess(url);
    }

    @Override
    public Result setFileTag(String ea, Integer fsUserId, SetFileTagArg arg) {
        FileEntity entity = fileLibraryDAO.getById(arg.getFileId());
        if (entity == null){
            log.info("FileLibraryServiceImpl.setFileTag failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }

        fileLibraryDAO.updateTagNameByFileId(ea, arg.getFileId(), arg.getTagNames());
        String tagName = arg.getTagNames().stream().map(TagName::getCombineName).collect(Collectors.joining("、"));
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), entity.getFileName(), OperateTypeEnum.SET_TAG, tagName);
        return Result.newSuccess();
    }

    private Result<ObjectGroupListResult> listAllFileGroup(String ea, Integer fsUserId, ListFileGroupArg arg){
        ObjectGroupListResult groupResult = new ObjectGroupListResult();
        List<ListObjectGroupResult> resultGroupList = Lists.newArrayList();
        groupResult.setObjectGroupList(resultGroupList);
        setDefaultGroupList(resultGroupList, fsUserId);
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.FILE.getType(), arg.getMenuId(), null);
        resultGroupList.addAll(customizeGroupListVO.getObjectGroupList());
        List<String> groupIds = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        for (ListObjectGroupResult objectGroup : resultGroupList){
            if (StringUtils.equals(objectGroup.getGroupId(), FileDefaultGroupEnum.ALL.getId())){
                //objectGroup.setObjectCount(fileLibraryDAO.queryFileCountByAll(ea, arg.getKeyword()));
                // 如果没有查看任何一个分组的权限，只能看未分组的微页面 + 我创建的
                if (CollectionUtils.isEmpty(groupIds)) {
                    objectGroup.setObjectCount(fileLibraryDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId, arg.getKeyword()));
                } else {
                    // 如果有分组权限，可以查看 有权限的分组 + 我创建的 + 未分类的
                    objectGroup.setObjectCount(fileLibraryDAO.queryAccessibleCount(ea, fsUserId, arg.getKeyword(), groupIds));
                }
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }else if(StringUtils.equals(objectGroup.getGroupId(), FileDefaultGroupEnum.CREATE_BY_ME.getId())){
                objectGroup.setObjectCount(fileLibraryDAO.queryFileCountCreateByMe(ea, fsUserId, arg.getKeyword()));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }else if (StringUtils.equals(objectGroup.getGroupId(), FileDefaultGroupEnum.NO_GROUP.getId())){
                objectGroup.setObjectCount(fileLibraryDAO.queryFileCountByUnGrouped(ea, ObjectTypeEnum.FILE.getType(), arg.getKeyword()));
                objectGroup.setSystem(true);
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
            }
        }
        return Result.newSuccess(groupResult);
    }

    private Result<ObjectGroupListResult> listAllCustomFileGroup(String ea, Integer fsUserId, String menuId){
        ObjectGroupListResult fileGroupResult = new ObjectGroupListResult();
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.FILE.getType(), menuId, null);
        fileGroupResult.setObjectGroupList(customizeGroupListVO.getObjectGroupList());
        fileGroupResult.setSortVersion(customizeGroupListVO.getSortVersion());
        return Result.newSuccess(fileGroupResult);
    }

    private Result<ObjectGroupListResult> listAllGroupWithItem(String ea, Integer fsUserId, ListFileGroupArg arg){
        ObjectGroupListResult groupResult = new ObjectGroupListResult();
        List<ListObjectGroupResult> groupList = Lists.newArrayList();
        setDefaultGroupList(groupList, fsUserId);
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.FILE.getType(), arg.getMenuId(), null);
        List<ListObjectGroupResult> withItemList = customizeGroupListVO.getObjectGroupList().stream()
                .filter(e -> e.getObjectCount() > 0).collect(Collectors.toList());
        groupList.addAll(withItemList);
        groupResult.setSortVersion(customizeGroupListVO.getSortVersion());
        groupResult.setObjectGroupList(groupList);
//        List<ListObjectGroupResult> groupList = Lists.newArrayList();
//        groupResult.setObjectGroupList(groupList);
//        List<ObjectGroupEntity> objectGroupEntityList = groupRoleRelationManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.FILE.getType());
        List<String> groupIds = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        //默认分组
//        setDefaultGroupList(groupList);
        for (ListObjectGroupResult objectGroup : groupList){
            if (StringUtils.equals(objectGroup.getGroupId(), FileDefaultGroupEnum.ALL.getId())){
                //objectGroup.setObjectCount(fileLibraryDAO.queryFileCountByAll(ea, arg.getKeyword()));
                // 如果没有查看任何一个分组的权限，只能看未分组的微页面 + 我创建的
                if (CollectionUtils.isEmpty(groupIds)) {
                    objectGroup.setObjectCount(fileLibraryDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId, arg.getKeyword()));
                } else {
                    // 如果有分组权限，可以查看 有权限的分组 + 我创建的 + 未分类的
                    objectGroup.setObjectCount(fileLibraryDAO.queryAccessibleCount(ea, fsUserId, arg.getKeyword(), groupIds));
                }
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                objectGroup.setSystem(true);
            }else if(StringUtils.equals(objectGroup.getGroupId(), FileDefaultGroupEnum.CREATE_BY_ME.getId())){
                objectGroup.setObjectCount(fileLibraryDAO.queryFileCountCreateByMe(ea, fsUserId, arg.getKeyword()));
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                objectGroup.setSystem(true);
            }else if (StringUtils.equals(objectGroup.getGroupId(), FileDefaultGroupEnum.NO_GROUP.getId())) {
                objectGroup.setObjectCount(fileLibraryDAO.queryFileCountByUnGrouped(ea, ObjectTypeEnum.FILE.getType(), arg.getKeyword()));
                objectGroup.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                objectGroup.setSystem(true);
            }
        }
        return Result.newSuccess(groupResult);
    }

    private void setDefaultGroupList( List<ListObjectGroupResult> groupList, Integer userId){
        for (FileDefaultGroupEnum fileDefaultGroupEnum : FileDefaultGroupEnum.getByUserId(userId)) {
            ListObjectGroupResult groupResult = new ListObjectGroupResult();
            groupResult.setGroupId(fileDefaultGroupEnum.getId());
            groupResult.setGroupName(fileDefaultGroupEnum.getName());
            groupList.add(groupResult);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteFileBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        List<FileEntity> entityList = fileLibraryDAO.getByIdList(arg.getIdList());
        if (CollectionUtils.isEmpty(entityList)){
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        if (entityList.stream().anyMatch(e -> e.getType() == SYSTEM_FILE)){
            return Result.newError(SHErrorCode.CANNOT_DELETE_SYSTEM_FILE);
        }

        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.FILE.getType(), arg.getIdList());
        fileLibraryDAO.deleteByIdList(ea, arg.getIdList());
        // 删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.FILE.getType(), arg.getIdList());
        try {
            List<String> apaths = Lists.newArrayList();
            List<String> npaths = Lists.newArrayList();
            for (FileEntity entity : entityList) {
                if (entity.getFilePath() != null) {
                    if (entity.getFilePath().startsWith("A_")) {
                        apaths.add(entity.getFilePath());
                    } else {
                        npaths.add(entity.getFilePath());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(apaths)) {
//                ADeleteFiles.Arg fileArg = new ADeleteFiles.Arg();
//                fileArg.setBusiness("fs-mankeep-provider");
//                fileArg.setaPathList(apaths);
//                User user = new User();
//                user.setEmployId(fsUserId);
//                user.setEnterpriseAccount(ea);
//                fileArg.setUser(user);
//                aFileStorageService.deleteFiles(fileArg);
            }
            if (CollectionUtils.isNotEmpty(npaths)) {
//                NClearFile.Arg fileArg = new NClearFile.Arg();
//                fileArg.setBusiness("fs-mankeep-provider");
//                fileArg.setEa(ea);
//                fileArg.setNPathList(npaths);
//                nFileStorageService.nClearFile(fileArg, ea);
            }
        } catch (Exception e) {
            log.info("FileLibraryServiceImpl.deleteFileBatch delete file from warehouse failed ea:{} fsUserId:{} arg:{} e:", ea, fsUserId, arg, e);
        }
        //从积分系统删除文件
        for (FileEntity fileEntity : entityList) {
            integralServiceManager.asyncRemoveMaterial(ea, CategoryApiNameConstant.FILE, fileEntity.getId());
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), fileEntity.getFileName(), OperateTypeEnum.DELETE);
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topFile(String ea, Integer fsUserId, TopMaterialArg arg) {
        FileEntity entity = fileLibraryDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.FILE.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopFile(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
//        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.FILE.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.FILE.getType(), arg.getObjectId());

        return Result.newSuccess();
    }

    @Override
    public Result<Void> addFileGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.FILE.getType());
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setFileGroupBatch(String ea, Integer fsUserId, SetObjectGroupArg arg) {
        List<FileEntity> fileEntityList = fileLibraryDAO.getByIdList(arg.getObjectIdList());
        if (CollectionUtils.isEmpty(fileEntityList)){
            log.info("FileLibraryServiceImpl.setFileGroupBatch failed file not exist ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        if (fileEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_FILE_NOT_FOUND);
        }
        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }

        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.FILE.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.FILE.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        for (FileEntity fileEntity : fileEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.FILE.getType(), fileEntity.getFileName(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }

    @Override
    public Result<ListFileByGroupResult> getFileDetailById(String id){
        FileEntity fileEntity = fileLibraryDAO.getById(id);
        if(fileEntity==null){
            log.info("FileLibraryServiceImpl.getFileDetailById failed file entity not exist id:{}", id);
            return Result.newError(SHErrorCode.FILE_NOT_FOUND);
        }
        ListFileByGroupResult fileByGroupResult = new ListFileByGroupResult();
        fileByGroupResult.setFileName(fileEntity.getFileName());
        fileByGroupResult.setFilePath(fileEntity.getFilePath());
        fileByGroupResult.setFileSize(fileEntity.getFileSize());
        fileByGroupResult.setExt(fileEntity.getExt());
        fileByGroupResult.setId(fileEntity.getId());
        fileByGroupResult.setCoverUrl(QywxFileTypeLogoEnum.getLogoByType(fileEntity.getExt()));
        if ( fileEntity.getFilePath()!= null){
            fileByGroupResult.setPreviewUrl( fileV2Manager.getPreviewUrl(fileEntity.getEa(), fileEntity.getFilePath()));
            String name = fileEntity.getFileName();
            if (StringUtils.isNotBlank(fileEntity.getExt())) {
                name = name + "." + fileEntity.getExt();
            }
            fileByGroupResult.setDownloadUrl(fileV2Manager.getDownloadFileUrl(fileEntity.getEa(), fileEntity.getFilePath(), name));
        }
        return Result.newSuccess(fileByGroupResult);
    }
}
