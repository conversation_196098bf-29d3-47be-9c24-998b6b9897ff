package com.facishare.marketing.provider.service.qywx;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.qywx.AgentAuthResult;
import com.facishare.marketing.api.result.qywx.GetJsApiSignatureResult;
import com.facishare.marketing.api.result.qywx.inner.CheckFsTokenResult;
import com.facishare.marketing.api.service.qywx.QYWXInnerService;
import com.facishare.marketing.api.vo.qywx.GetJsApiSignatureVO;
import com.facishare.marketing.api.vo.qywx.inner.CheckFsTokenVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.innerResult.qywx.GetUserInfoResult;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * Created by ranluch on 2020/1/3.
 */
@Slf4j
@Service("qywxInnerService")
public class QYWXInnerServiceImpl implements QYWXInnerService {
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private SSOLoginService ssoLoginService;
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
    @Value("${qywx.crm.appid}")
    private String qywxCrmAppid;

    @ReloadableProperty("ssoRedirectUrlFormatStr")
    private String ssoRedirectUrlFormatStr;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Override
    public Result<String> doAgentLogin(String key, String redirectUrl) {
        if (StringUtils.isAnyEmpty(key, redirectUrl)) {
            log.info("QYWXInnerServiceImpl doAgentLogin key or url is empty! key={}, redirectUrl={}", key, redirectUrl);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentById(key);
        if (agentConfig == null) {
            log.info("QYWXInnerServiceImpl doAgentLogin agentConfig is null! key={}, redirectUrl={}", key, redirectUrl);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        redisManager.setAgentAuthRedirectUrl(key, redirectUrl);
        return Result.newSuccess(agentConfig.getCorpid());
    }

    @Override
    public Result<AgentAuthResult> agentAuth(String code, String state) {
        AgentAuthResult authResult = new AgentAuthResult();
        String redirectUrl = redisManager.getAgentAuthRedirectUrl(state);
        if (StringUtils.isBlank(redirectUrl)) {
            return Result.newSuccess(authResult);
        }

        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentById(state);
        if (agentConfig == null) {
            return Result.newSuccess(authResult);
        }

        int tenantId = eIEAConverter.enterpriseAccountToId(agentConfig.getEa());

        String accessToken = qywxManager.getAccessToken(agentConfig);
        GetUserInfoResult userInfo = qywxManager.getUserInfoByCode(code, accessToken, state);
        if (userInfo == null || StringUtils.isBlank(userInfo.getUserId())) {
            return Result.newError(SHErrorCode.QYWX_GET_USER_INFO_FAIL);
        }
        Result<Map<String, String>> fsAccountBatch = qyweixinAccountBindManager
            .outAccountToFsAccountBatch( agentConfig.getEa(), qywxCrmAppid ,Lists.newArrayList(userInfo.getUserId()));
        if (fsAccountBatch.isSuccess() && MapUtils.isNotEmpty(fsAccountBatch.getData())) {
            log.info("QYWXInnerServiceImpl agentAuth outAccountToFsAccountBatch fsAccountBatch={}", fsAccountBatch);
            Map<String, String> userIdMap = fsAccountBatch.getData();
            String fsUserIdStr = userIdMap.get(userInfo.getUserId());
            if (StringUtils.isBlank(fsUserIdStr)) {
                return Result.newSuccess(authResult);
            }
            Integer fsUserId = null;
            String[] split = fsUserIdStr.split("\\.");
            if (split != null && split.length == 3) {
                fsUserId = Integer.parseInt(split[split.length-1]);
            }
            if (fsUserId == null) {
                return Result.newSuccess(authResult);
            }
            CreateUserTokenDto.Argument argument = new CreateUserTokenDto.Argument(new UserTokenDto(tenantId, fsUserId));
            CreateUserTokenDto.Result userToken = ssoLoginService.createUserToken(argument);
            if (userToken == null || StringUtils.isBlank(userToken.getToken())) {
                return Result.newError(SHErrorCode.QYWX_CREATE_USER_TOKEN_FAIL);
            }
            String ssoRedirectUrl = String.format(ssoRedirectUrlFormatStr, userToken.getToken(), redirectUrl);
            authResult.setRedirectLoginUrl(ssoRedirectUrl);
            return Result.newSuccess(authResult);
        }
        return Result.newError(SHErrorCode.QYWX_GET_BIND_INFO_FAIL);
    }

    @Override
    public Result<GetJsApiSignatureResult> getJsApiSignature(GetJsApiSignatureVO vo) {
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentById(vo.getKey());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String accessToken = qywxManager.getAccessToken(agentConfig);
        if (StringUtils.isBlank(accessToken)) {
            return Result.newError(SHErrorCode.QYWX_GET_ACCESS_TOKEN_FAIL);
        }

        String nonceStr = UUIDUtil.getUUID().substring(0, 10);
        Long timestamp = new Date().getTime() / 1000;
        String corpSignature = qywxManager.getJsApiTicketSignature(qywxManager.getCorpJsApiTicket(agentConfig.getCorpid(), accessToken, vo.getKey()), nonceStr, timestamp, vo.getUrl());
        if (StringUtils.isBlank(corpSignature)) {
            return Result.newError(SHErrorCode.QYWX_GET_CORP_TICKET_SIGNATURE_FAIL);
        }

        String agentSignature = qywxManager.getJsApiTicketSignature(qywxManager.getAgentJsApiTicket(agentConfig.getCorpid(), agentConfig.getAgentid(), accessToken, vo.getKey()), nonceStr, timestamp, vo.getUrl());
        if (StringUtils.isBlank(agentSignature)) {
            return Result.newError(SHErrorCode.QYWX_GET_AGENT_TICKET_SIGNATURE_FAIL);
        }

        GetJsApiSignatureResult result = new GetJsApiSignatureResult();
        result.setAgentId(agentConfig.getAgentid());
        result.setCorpId(agentConfig.getCorpid());
        result.setNoncestr(nonceStr);
        result.setTimestamp(timestamp);
        result.setAgentSignature(agentSignature);
        result.setCorpSignature(corpSignature);

        return Result.newSuccess(result);
    }

    @Override
    public Result<GetJsApiSignatureResult> queryJsApiSignature(String corpId, String url,String suitId) {
        QywxCorpAgentConfigEntity agentConfig = qywxManager.getAgentConfigEntityBySuiteId(corpId, suitId);
        if (null == agentConfig) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        QywxCustomerAppInfoEntity entity = qywxCustomerAppInfoDAO.selectOne(suitId, corpId);
        String accessToken = qywxManager.getAgentAccessToken(corpId, suitId, entity.getAuthCode());
        if (StringUtils.isBlank(accessToken)) {
            return Result.newError(SHErrorCode.QYWX_GET_ACCESS_TOKEN_FAIL);
        }

        String nonceStr = UUIDUtil.getUUID().substring(0, 10);
        Long timestamp = new Date().getTime() / 1000;
        String corpCustomerJsapiTicket = qywxManager.getCustomerCorpJsApiTicket(corpId,accessToken,suitId);
        String corpSignature = qywxManager.getJsApiTicketSignature(corpCustomerJsapiTicket, nonceStr, timestamp, url);
        if (StringUtils.isBlank(corpSignature)) {
            return Result.newError(SHErrorCode.QYWX_GET_CORP_TICKET_SIGNATURE_FAIL);
        }
        String corpCustomerAgentJsapiTicket = qywxManager.getCustomerAgentJsApiTicket(corpId, entity.getAgentId(), accessToken, suitId);
        String agentSignature = qywxManager.getJsApiTicketSignature(corpCustomerAgentJsapiTicket, nonceStr, timestamp, url);
        if (StringUtils.isBlank(agentSignature)) {
            return Result.newError(SHErrorCode.QYWX_GET_AGENT_TICKET_SIGNATURE_FAIL);
        }

        GetJsApiSignatureResult result = new GetJsApiSignatureResult();
        result.setAgentId(entity.getAgentId());
        result.setCorpId(corpId);
        result.setNoncestr(nonceStr);
        result.setTimestamp(timestamp);
        result.setAgentSignature(agentSignature);
        result.setCorpSignature(corpSignature);

        return Result.newSuccess(result);
    }

    @Override
    public Result<CheckFsTokenResult> checkFsToken(CheckFsTokenVO vo) {
        CheckFsTokenResult checkResult = new CheckFsTokenResult(1);
        if (StringUtils.isBlank(vo.getKey())) {
            return Result.newSuccess(checkResult);
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentById(vo.getKey());
        if (agentConfig == null) {
            return Result.newSuccess(checkResult);
        }
        CookieToAuth.Argument argument = new CookieToAuth.Argument();
        argument.setCookie(vo.getAuthXC());
        CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
        ValidateStatus validateStatus = result.getValidateStatus();
        if (validateStatus.is(ValidateStatus.NORMAL)) {
            AuthXC authXC = result.getBody();
            String enterpriseAccount = authXC.getEnterpriseAccount();
            if (StringUtils.isNotBlank(enterpriseAccount) && enterpriseAccount.equals(agentConfig.getEa())) {
                checkResult.setCheckResult(0);
            }
        }

        return Result.newSuccess(checkResult);
    }
}
