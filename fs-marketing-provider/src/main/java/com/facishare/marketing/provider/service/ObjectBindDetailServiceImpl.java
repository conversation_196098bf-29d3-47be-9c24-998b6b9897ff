package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.objectBindDetail.BindObjectArg;
import com.facishare.marketing.api.arg.objectBindDetail.UnBindObjectArg;
import com.facishare.marketing.api.service.ObjectBindDetailService;
import com.facishare.marketing.common.enums.FormStyleTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.dao.CustomizeFormDataObjectDAO;
import com.facishare.marketing.provider.dao.ObjectBindDetailDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.manager.CtaRelationDaoManager;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.CustomizeFormDataObjectEntity;
import com.facishare.marketing.provider.entity.ObjectBindDetailEntity;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * Created  By zhoux 2020/05/27
 **/
@Service("objectBindDetailService")
@Slf4j
public class ObjectBindDetailServiceImpl implements ObjectBindDetailService {

    @Autowired
    private ObjectBindDetailDAO objectBindDetailDAO;

    @Autowired
    private CustomizeFormDataObjectDAO customizeFormDataObjectDAO;

    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private CtaRelationDaoManager ctaRelationDaoManager;

    @Override
    public Result bindObject(BindObjectArg arg) {
        try {
            if (arg.getTargetObjectType() == ObjectTypeEnum.CUSTOMIZE_FORM.getType()) {
                // 表单写入表单绑定表
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(arg.getTargetObjectId());
                if (customizeFormDataEntity == null) {
                    log.warn("ObjectBindDetailServiceImpl.bindObject customizeFormDataEntity is null id:{}", arg.getTargetObjectId());
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
                CustomizeFormDataObjectEntity oldData = customizeFormDataObjectDAO.accurateAccessCustomizeFormDataObject(arg.getEa(), arg.getTargetObjectId(), arg.getObjectId(), arg.getObjectType());
                if (oldData != null) {
                    return Result.newSuccess();
                }

                // 校验表单是否被其他官网绑定
                if (Objects.equals(arg.getObjectType(), ObjectTypeEnum.OFFICIAL_WEBSITE.getType())) {
                    List<CustomizeFormDataObjectEntity> dbEntityList = customizeFormDataObjectDAO.getByFormIdAndObjectType(arg.getEa(), arg.getTargetObjectId(), arg.getObjectType());
                    if (CollectionUtils.isNotEmpty(dbEntityList) && !Objects.equals(dbEntityList.get(0).getObjectId(), arg.getObjectId())) {
                        return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_BIND_EXIST);
                    }
                }

                CustomizeFormDataObjectEntity customizeFormDataObjectEntity = new CustomizeFormDataObjectEntity();
                customizeFormDataObjectEntity.setId(UUIDUtil.getUUID());
                customizeFormDataObjectEntity.setEa(arg.getEa());
                customizeFormDataObjectEntity.setFormId(arg.getTargetObjectId());
                customizeFormDataObjectEntity.setObjectId(arg.getObjectId());
                customizeFormDataObjectEntity.setObjectType(arg.getObjectType());
                customizeFormDataObjectEntity.setCreateBy(arg.getFsUserId());
                customizeFormDataObjectEntity.setUpdateBy(arg.getFsUserId());
                customizeFormDataObjectEntity.setFormStyleType(FormStyleTypeEnum.BOTTOM.getType());
                customizeFormDataObjectDAO.insertCustomizeFormDataObject(customizeFormDataObjectEntity);
            } else {
                // 校验表单是否被其他官网绑定
                if (Objects.equals(arg.getObjectType(), ObjectTypeEnum.OFFICIAL_WEBSITE.getType())) {
                    List<ObjectBindDetailEntity> objectBindDetailEntities = objectBindDetailDAO.getByEaAndTargetObjectId(arg.getEa(), arg.getTargetObjectId(), arg.getObjectType());
                    if (CollectionUtils.isNotEmpty(objectBindDetailEntities)) {
                        for (ObjectBindDetailEntity objectBindDetailEntity : objectBindDetailEntities) {
                            if (!Objects.equals(objectBindDetailEntity.getObjectId(), arg.getObjectId())) {
                                return Result.newError(SHErrorCode.OFFICIAL_WEBSITE_BIND_EXIST);
                            }
                        }
                    }
                }

                ObjectBindDetailEntity objectBindDetailEntity = new ObjectBindDetailEntity();
                objectBindDetailEntity.setId(UUIDUtil.getUUID());
                objectBindDetailEntity.setEa(arg.getEa());
                objectBindDetailEntity.setObjectId(arg.getObjectId());
                objectBindDetailEntity.setObjectType(arg.getObjectType());
                objectBindDetailEntity.setTargetObjectId(arg.getTargetObjectId());
                objectBindDetailEntity.setTargetObjectType(arg.getTargetObjectType());
                objectBindDetailEntity.setCreateBy(arg.getFsUserId());
                objectBindDetailDAO.insertObjectBindDetail(objectBindDetailEntity);
            }

            if(arg.getTargetObjectType() == ObjectTypeEnum.CTA.getType()) {
                ctaRelationDaoManager.addCtaRelation(arg.getEa(), Lists.newArrayList(arg.getTargetObjectId()), arg.getObjectType(),arg.getObjectId());
            }
        } catch (Exception e) {
            log.warn("ObjectBindDetailServiceImpl.bindObject error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }

    @Override
    public Result unBindObject(UnBindObjectArg arg) {
        try {
            if (arg.getTargetObjectType() == ObjectTypeEnum.CUSTOMIZE_FORM.getType()) {
                customizeFormDataObjectDAO.deleteCustomizeFormDataObject(arg.getEa(), arg.getTargetObjectId(), arg.getObjectId(), arg.getObjectType());
            } else {
                objectBindDetailDAO.deleteObjectBindDetail(arg.getEa(), arg.getObjectId(), arg.getObjectType(), arg.getTargetObjectId(), arg.getTargetObjectType());
            }
        } catch (Exception e) {
            log.warn("ObjectBindDetailServiceImpl.unBindObject error e:{}", e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess();
    }
}
