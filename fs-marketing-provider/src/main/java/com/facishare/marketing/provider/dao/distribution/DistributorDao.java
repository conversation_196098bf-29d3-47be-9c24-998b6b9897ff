package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.provider.dto.distribution.DistributorStatisticsDTO;
import com.facishare.marketing.provider.dto.distribution.QueryDistributorByOperatorDTO;
import com.facishare.marketing.provider.entity.data.DistributorRewardTopData;
import com.facishare.marketing.provider.entity.distribution.DistributorCountEntity;
import com.facishare.marketing.provider.entity.distribution.DistributorEntity;
import com.facishare.marketing.provider.entity.distribution.RecruitCountEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.List;

public interface DistributorDao {
	@Select("<script>"
		+ "SELECT d.id id,d.uid uid,d.fs_ea fsEa,d.company_name companyName,d.plan_id planId, d.grade grade, d.recruit_id recruitId,od.status,d.create_time createTime,d.update_time updateTime,od.operator_id operatorId,d.clue_reward clueReward,d.recruit_reward recruitReward,d.order_total_money orderTotalMoney \n"
		+ "FROM distributor d \n"
		+" <if test=\"keyWord != null\">\n"
		+"      JOIN distributor_form_submit fs ON d.id = fs.distributor_id\n"
		+" </if>\n"
		+ "JOIN operator_distributor od on od.distributor_id = d.id \n"
		+ "JOIN operator op on op.id = od.operator_id and op.status = 1\n"
		+ "WHERE d.plan_id = #{planId}\n"
		+	"<if test=\"keyWord != null\">"
		+	    "AND fs.submit_content->>'name' like concat(concat('%',#{keyWord}),'%')\n"
		+   "</if>"
		+	"<if test=\"status != 3\">"
		+	    "AND od.status=#{status}\n"
		+   "</if>"
		+ "ORDER BY d.create_time DESC"
		+ "</script>")
	List<DistributorEntity> adminQueryDistributorResult(@Param("planId") String planId, @Param("page") Page page, @Param("status")Integer status, @Param("keyWord")String keyWord);
	
	@Select("<script>"
		+ "SELECT d.* \n"
		+ "from distributor d\n"
		+" <if test=\"keyWord != null\">\n"
		+"      JOIN distributor_form_submit fs ON d.id = fs.distributor_id\n"
		+" </if>\n"
		+ "JOIN operator_distributor od on od.distributor_id = d.id\n"
		+ "JOIN \"operator\" op on op.\"id\" = od.operator_id\n"
		+ "WHERE op.ea = #{ea} and op.fs_user_id = #{fsUserId}\n"
		+ " <if test=\"planId != null\">\n"
		+ "  AND  op.plan_id = #{planId}\n"
		+ " </if>\n"
		+	"<if test=\"keyWord != null\">"
		+	    "AND fs.submit_content->>'name' like concat(concat('%',#{keyWord}),'%')\n"
		+   "</if>"
		+	"<if test=\"status != 3\">"
		+	    "AND od.status=#{status}\n"
		+   "</if>"
		+ "ORDER BY d.create_time DESC"
		+ "</script>")
	List<DistributorEntity> operatorQueryDistributorResult(@Param("ea") String ea, @Param("planId") String planId,
		@Param("fsUserId") Integer fsUserId, @Param("page") Page page,@Param("status")Integer status, @Param("keyWord")String keyWord);
	

	@Select("SELECT d.* FROM distributor d "
		+ "left join operator_distributor od on od.distributor_id = d.id "
		+ "WHERE d.plan_id = #{planId} and od.operator_id is not null")
	List<DistributorEntity> queryDistributorByPlanId(@Param("planId") String planId);

	@Select("SELECT * FROM distributor d "
		+ "WHERE id = #{id}")
	DistributorEntity queryDistributorById(@Param("id") String id);

	@Select("SELECT d.plan_id as planId, d.id AS distributorId, d.recruit_id AS recruitId, o.status, d.create_time as createTime, da.refuse_desc as refuseDesc "
		+ "FROM distributor d "
		+ "join operator_distributor o on d.id = o.distributor_id\n"
		+ "LEFT JOIN distributor_application da ON da.distributor_id=o.distributor_id and da.operator_id = o.operator_id\n"
		+ "WHERE d.id = #{id} and o.operator_id = #{operatorId} \n"
		+ "order by da.create_time desc \n"
		+ "limit 1")
	QueryDistributorByOperatorDTO queryDistributorByIdAndOperorId(@Param("id") String id, @Param("operatorId") String operatorId);

	@Select("<script>"
		+ "SELECT plan_id as planId, count(1) as distributorCount, sum(clue_reward) as totalReward, sum(recruit_reward) as totalRecruitReward , sum(order_total_money) as totalOrderTotalMoney "
		+ "FROM distributor "
		+ "WHERE plan_id  IN "
		+ "<foreach collection = 'planIds' item = 'item' open = '(' separator = ',' close = ')'>"
		+ "#{item}"
		+"</foreach>"
		+" GROUP BY plan_id"
		+"</script>")
	List<DistributorCountEntity> getDistributorCount(@Param("planIds") List<String> planIds);

	@Update("update distributor "
		+ "set grade = #{grade}, "
		+ "update_time = now() " +
		" WHERE id = #{id}")
	boolean updateDistributorGrade(@Param("id") String id, @Param("grade") Integer grade);

	@Select("SELECT * FROM distributor d "
		+ "WHERE d.plan_id = #{planId} and d.grade = #{grade}")
	List<DistributorEntity> queryDistributorByPlanIdAndGrade(@Param("planId") String planId, @Param("grade") Integer grade);

	@Update("<script>"
		+" UPDATE distributor\n"
		+ "       <set>\n"
		+ "            <if test=\"orderTotalMoney != null\">\n"
		+ "                \"order_total_money\" = #{orderTotalMoney},\n"
		+ "            </if>\n"
		+ "            <if test=\"clueReward != null\">\n"
		+ "                \"clue_reward\" = #{clueReward},\n"
		+ "            </if>\n"
		+ "            <if test=\"recruitReward != null\">\n"
		+ "                \"recruit_reward\" = #{recruitReward},\n"
		+ "            </if>\n"
		+ "            <if test=\"validClueReward != null\">\n"
		+ "                \"valid_clue_reward\" = #{validClueReward},\n"
		+ "            </if>\n"
		+ "            <if test=\"recruitGranted != null\">\n"
		+ "                \"granted\" = #{recruitGranted},\n"
		+ "            </if>\n"
		+ "            update_time = now()\n"
		+ "        </set>\n"
		+ "  WHERE \"id\" = #{id}"
		+ "</script>")
	int updateDistributorOrderMoney(@Param("id") String id, @Param("orderTotalMoney") Float orderTotalMoney, @Param("clueReward") Float clueReward, @Param("recruitReward") Float recruitReward, @Param("validClueReward") Float validClueReward, @Param("recruitGranted") Float recruitGranted);

    @Select(" SELECT COUNT(*) FROM distributor WHERE recruit_id=#{distributorId} AND status != 1")
    int queryValidRecruitorsCount(@Param("distributorId") String distributorId);

	@Select("<script>"
		+ "SELECT * "
		+ "FROM distributor "
		+ "WHERE id  IN "
		+ 		"<foreach collection = 'distributorIds' item = 'item' open = '(' separator = ',' close = ')'>"
		+ 			"#{item}"
		+ 		"</foreach>"
		+ "</script>")
	List<DistributorEntity> getDistributorByIds(@Param("distributorIds") List<String> distributorIds);

	@Select("SELECT * FROM distributor WHERE recruit_id = #{distributorId} ORDER BY create_time DESC")
	List<DistributorEntity> queryRecruitorsByDistributorId(@Param("distributorId") String distributorId, @Param("page") Page page);

	@Update("update distributor "
		+ "set recruit_grade = #{recruitGrade}, "
		+ "update_time = now() " +
		" WHERE id = #{id}")
	boolean updateDistributorRecruitGrade(@Param("id") String id, @Param("recruitGrade") Integer recruitGrade);

	@Select("<script>"
		+ "SELECT recruit_id as recruitId, count(1) as recruitCount "
		+ "FROM distributor "
		+ "WHERE recruit_id  IN "
		+ "<foreach collection = 'recruitIds' item = 'item' open = '(' separator = ',' close = ')'>"
		+ "#{item}"
		+"</foreach>"
		+" GROUP BY recruit_id"
		+"</script>")
	List<RecruitCountEntity> getRecruitCounts(@Param("recruitIds") List<String> recruitIds);

	@Select("<script>"
		+ "SELECT d.\"id\" distributorId, d.uid distributorUid, sum(d.clue_reward + d.recruit_reward + d.valid_clue_reward) totalCnt \n"
		+ "FROM distributor d\n"
		+ "join operator_distributor od on od.distributor_id = d.id and od.status = 0\n"
		+ "WHERE d.plan_id = #{planId}\n"
		+ "GROUP BY d.\"id\", d.uid\n"
		+ "ORDER BY totalCnt DESC\n"
		+ "<if test=\"topNum != null\">\n"
		+ "LIMIT #{topNum}\n"
		+ "</if>\n"
		+ "</script>")
	List<DistributorRewardTopData> queryDistributorRewardTop(@Param("planId") String planId, @Param("topNum") Integer topNum);

	@Select("<script>"
		+ "SELECT d.\"id\" distributorId, d.uid distributorUid, sum(d.clue_reward + d.recruit_reward + d.valid_clue_reward) totalCnt \n"
		+ "FROM distributor d\n"
		+ "join operator_distributor od on od.distributor_id = d.id and od.status = 0\n"
		+ "join \"operator\" op on op.id = od.operator_id\n"
		+ "WHERE d.plan_id = #{planId} and op.fs_user_id = #{fsUid}\n"
		+ "GROUP BY d.\"id\", d.uid\n"
		+ "ORDER BY totalCnt DESC"
		+ "<if test=\"topNum != null\">\n"
		+ "LIMIT #{topNum}\n"
		+ "</if>\n"
		+ "</script>")
	List<DistributorRewardTopData> queryDistributorRewardTopByUid(@Param("planId") String planId, @Param("fsUid") Integer fsUid, @Param("topNum") Integer topNum);

	@Select("<script>"
		+ " SELECT B.operator_id,C.plan_title,  sum(CASE WHEN B.status = 2 THEN 1 ELSE 0 END)AS count, MAX(B.create_time) AS application_time FROM distributor AS A JOIN operator_distributor AS B ON A.id = B.distributor_id\n"
		+ " JOIN distribute_plan AS C ON A.plan_id = C.id WHERE B.operator_id IN "
		+ "<foreach collection = 'operatorIds' item = 'item' open = '(' separator = ',' close = ')'>"
		+ "#{item}"
		+"</foreach>"
		+ " AND A.fs_ea = #{ea}\n"
		+ " GROUP BY C.id,C.plan_title,B.operator_id"
		+ "</script>")
	List<DistributorStatisticsDTO> queryDistributiorStatisticsByOperatorId(@Param("operatorIds") List<String> operatorIds, @Param("ea") String ea);

	@Select("<script>" +
			"SELECT plan_id as id, count(*) as count FROM distributor WHERE fs_ea=#{ea} AND plan_id IN " +
			"<foreach open='(' close=')' separator=',' collection='distributePlanIds' item='item'>#{item}</foreach> " +
			"group by plan_id" +
			"</script>")
	List<DataCount> groupCountDistributorByDistributePlanIds(@Param("ea") String ea, @Param("distributePlanIds")Collection<String> distributePlanIds);

}
