package com.facishare.marketing.provider.manager.feed;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.AddCustomizeFormDataArg;
import com.facishare.marketing.api.arg.GetCustomizeFormDataByIdArg;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.data.material.HexagonSiteBriefData;
import com.facishare.marketing.api.result.AddCustomizeFormDataResult;
import com.facishare.marketing.api.result.CustomizeFormDataDetailResult;
import com.facishare.marketing.api.result.LeadPoolResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.hexagon.GetSiteByEaUnitResult;
import com.facishare.marketing.api.service.CrmService;
import com.facishare.marketing.api.service.CustomizeFormDataService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CustomizeFormDataDAO;
import com.facishare.marketing.provider.dao.hexagon.*;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.facishare.marketing.provider.entity.hexagon.*;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.HexagonManager;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HexagonSiteManager extends MaterailDataManager<HexagonSiteBriefData>  {
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;
    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;
    @Autowired
    private CrmService crmService;
    @Autowired
    private CustomizeFormDataService customizeFormDataService;
    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Override
    public List<HexagonSiteBriefData> get(String ea,String... objectIds) {
        List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.listByIds(Lists.newArrayList(objectIds));
        List<GetSiteByEaUnitResult> resultList = hexagonManager.getGetSiteByEaUnitResults(ea, null, hexagonSiteEntityList);
        return this.convert(ea,resultList);
    }

    public List<HexagonSiteBriefData> convert(String ea,List<GetSiteByEaUnitResult> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)){
            return Lists.newArrayList();
        }
        List<String> imageUrls = sourceList.stream().filter(Objects::nonNull)
                .map(GetSiteByEaUnitResult::getCoverAPath)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<HexagonSiteBriefData> briefDataList = sourceList.stream().filter(Objects::nonNull).map(this::convert).collect(Collectors.toList());
        //多线程处理图片
        Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
        briefDataList.forEach(data -> {
            if (StringUtils.isNotBlank(data.getCoverApath()) && coverMap.containsKey(data.getCoverApath())) {
                data.setCoverSize(coverMap.get(data.getCoverApath()));
            }
        });
        return briefDataList;
    }
    public HexagonSiteBriefData convert(GetSiteByEaUnitResult source){
        if(source==null){
            return null;
        }
        HexagonSiteBriefData result = new HexagonSiteBriefData();
        result.setId(source.getId());
        result.setName(source.getName());
        result.setCoverUrl(source.getCoverUrl());
        result.setCoverApath(source.getCoverAPath());
        result.setMarketingEventCount(source.getMarketingEventCount());
        result.setAccessCount(source.getAccessCount());
        result.setLeadCount(source.getLeadCount());
        result.setStatus(source.getStatus());
        result.setCreator(source.getCreatorBy());
        result.setCreateTime(source.getCreateTime());
        result.setUpdater(source.getUpdater());
        result.setUpdateTime(source.getUpdateTime());
        result.setFormId(source.getFormId());
        result.setFormUsage(source.getFormUsage());
        result.setHadCrmMapping(source.getHadCrmMapping());
        result.setObjectType(this.getType());
        result.setTitle(source.getName());
        result.setShareTitle(source.getShareTitle());
        result.setShareDesc(source.getShareDesc());
        result.setFileToHexagonFailReason(source.getFileToHexagonFailReason());
        result.setFileToHexagonStatus(source.getFileToHexagonStatus());
        result.setFileType(source.getFileType());
        result.setSharePicMiniAppCutUrl(source.getSharePicMiniAppCutUrl());
        result.setSharePicH5CutUrl(source.getSharePicH5CutUrl());
        result.setSharePicOrdinaryCutUrl(source.getSharePicOrdinaryCutUrl());
        result.setSharePicOrdinaryUrl(source.getSharePicOrdinaryUrl());
        return result;

    }
    @Override
    public Integer getType() {
        return ObjectTypeEnum.HEXAGON_SITE.getType();
    }

    public Map<String, Object> copyPageFromTemplate(String ea, HexagonTemplatePageEntity templatePage, String newSiteId, HexagonSiteListDTO formInfo, String newFormId, Map<String, String> buttonInsideAction) {
        HexagonPageEntity hexagonPage = BeanUtil.copy(templatePage, HexagonPageEntity.class);
        String newPageId = UUIDUtil.getUUID();
        hexagonPage.setId(newPageId);
        hexagonPage.setHexagonSiteId(newSiteId);
        hexagonPage.setUpdateBy(hexagonPage.getCreateBy());
        if (formInfo != null && templatePage.getId().equals(formInfo.getHexagonPageId())) {
            String copyFormId = customizeFormDataManager.copy(ea, formInfo.getFormId(), -10000);
            if (copyFormId != null) {
                newFormId = copyFormId;
            }
            hexagonPage.setFormId(newFormId);
        }
        hexagonPage.setIsHomepage(2);
        // 处理表单提交后动作，跳转内部页面
        try {
            String pageContent = hexagonPage.getContent();
            JSONObject jsonObject = JSON.parseObject(pageContent);
            putInButtonInsideAction(jsonObject, buttonInsideAction, newPageId);
            /*
            JSONArray components = jsonObject.getJSONArray("components");
            for (Object componentObj : components) {
                JSONObject component = (JSONObject) componentObj;
                if ("container".equals(component.getString("type")) && "form-container".equals(component.getString("key"))) {
                    JSONArray insideComponents = component.getJSONArray("components");
                    for (Object insideComponentObj : insideComponents) {
                        JSONObject insideComponent = (JSONObject) insideComponentObj;
                        if ("button".equals(insideComponent.getString("type"))) {
                            JSONObject action = insideComponent.getJSONObject("action");
                            if (action != null && "inside".equals(action.getString("type"))) {
                                List<String> pair = new ArrayList<>();
                                pair.add(newPageId);
                                pair.add(pageContent);
                                buttonInsideAction.put(action.getString("id"), pair);
                            }
                        }
                    }
                }
            }
             */
            String oldPageIdToReplace = buttonInsideAction.get(templatePage.getId());
            if (StringUtils.isNotEmpty(oldPageIdToReplace)) {
                pageContent = hexagonPageDAO.getContentById(oldPageIdToReplace);
                pageContent = pageContent.replace(templatePage.getId(), newPageId);
                hexagonPageDAO.updateContent(oldPageIdToReplace, pageContent);
            }
        } catch (Exception ignored) {}
        hexagonPageDAO.insert(hexagonPage);
        Map<String, Object> result = new HashMap<>();
        result.put("newPageId", newPageId);
        result.put("newFormId", newFormId);
        return result;
    }

    private void putInButtonInsideAction(JSONObject component, Map<String, String> buttonInsideAction, String newPageId) {
        if (component == null) {
            return;
        }
        JSONObject action = component.getJSONObject("action");
        if (action != null && "inside".equals(action.getString("type"))) {
            buttonInsideAction.put(action.getString("id"), newPageId);
        }
        JSONArray components = component.getJSONArray("components");
        if (components != null) {
            for (Object componentObj : components) {
                putInButtonInsideAction((JSONObject) componentObj, buttonInsideAction, newPageId);
            }
        }
    }

    public Result<CreateSiteResult> copyTemplateToTemplate(String ea, Integer fsUserId, HexagonCopyArg arg) {

        //保存之前的站点ID
        String preSiteId = arg.getId();
        CreateSiteResult siteResult = new CreateSiteResult();
        if (StringUtils.isNotBlank(arg.getId())) {
            //修改站点的ID
            List<HexagonTemplatePageEntity> queryPageEntity;
            if(arg.getSource()!=null && arg.getSource()==2){
                List<HexagonPageEntity> hexagonPageEntity = hexagonPageDAO.getByHexagonSiteId(preSiteId);
                queryPageEntity = BeanUtil.copy(hexagonPageEntity, HexagonTemplatePageEntity.class);
            }else{
                queryPageEntity = hexagonTemplatePageDAO.getBySiteId(preSiteId);
            }

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(queryPageEntity)){
                log.info("copyTemplate return no queryPageEntity arg:{}", arg);
                return Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_EXIST_FOR_COPY);
            }

            String siteId = UUIDUtil.getUUID();
            siteResult.setId(siteId);
            HexagonTemplateSiteEntity templateSiteEntity = new HexagonTemplateSiteEntity();
            templateSiteEntity.setId(siteId);
            templateSiteEntity.setEa(ea);
            templateSiteEntity.setCreateBy(fsUserId);
            templateSiteEntity.setCreateBy(fsUserId);
            templateSiteEntity.setCreateTime(new Date());
            templateSiteEntity.setUpdateTime(new Date());
            templateSiteEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
            templateSiteEntity.setName(arg.getName());
            //插入新站点
            int insertResult = hexagonTemplateSiteDAO.insert(templateSiteEntity);
            if (insertResult != 1) {
                log.warn("HexagonService copySite insert site failed,entity={}", templateSiteEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            if(arg.isCheckMarketingTemplate()){
                hexagonTemplateSiteDAO.updateTemplateTypeAndDefault(hexagonTemplateSiteDAO.getById(preSiteId).getType(), siteId, DefaultTemplateTypeEnum.NORMAL_TEMPLATE.getType());
            }
            //根据之前的站点ID，查询出之前的该模板preSiteId下的页面
            for (HexagonTemplatePageEntity pageEntity : queryPageEntity) {
                //返回首页的ID
                if (pageEntity.getIsHomepage().intValue() == 1) {
                    String homePageId = copyPage(pageEntity, ea, siteId, fsUserId).getData();
                    siteResult.setPageId(homePageId);
                    siteResult.setFormId(pageEntity.getFormId());
                    CustomizeFormDataEntity formDataEntity = customizeFormDataDAO.getCustomizeFormDataById(pageEntity.getFormId());
                    if (formDataEntity != null) {
                        siteResult.setFormUsage(formDataEntity.getFormUsage());
                        siteResult.setHadCrmMapping(formDataEntity.hadCrmMapping());
                    }
                } else {
                    copyPage(pageEntity, ea, siteId, fsUserId);
                }
            }

            //替换新页面的actionID;
            List<HexagonTemplatePageEntity> hexagonTemplatePageEntities = resetContentActionId(preSiteId, siteId, arg.getSource()!=null && arg.getSource()==2 ? 2 : 1);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hexagonTemplatePageEntities)) {
                int updateContentActionID = hexagonTemplatePageDAO.updatePageActionId(hexagonTemplatePageEntities);
                if (updateContentActionID > 0) {
                    log.warn("更新页面的跳转ID成功");
                }
            }
        }

        return Result.newSuccess(siteResult);
    }

    public Result<String> copyPage(HexagonTemplatePageEntity pageEntity, String ea, String siteId, Integer userId) {

        HexagonTemplatePageEntity hexagonTemplatePageEntity = BeanUtil.copy(pageEntity, HexagonTemplatePageEntity.class);
        String id = UUIDUtil.getUUID();
        String sharePicH5Apath = pageEntity.getSharePicH5Apath();
        //替换share的页面图片
        String newH5Apath = hexagonManager.resetShareApath(sharePicH5Apath, ea);
        hexagonTemplatePageEntity.setSharePicH5Apath(newH5Apath);
        hexagonTemplatePageEntity.setSharePicMpApath(newH5Apath);
        //替换原有的content里面的url
        String content = pageEntity.getContent();
        String newContent = hexagonManager.resetContentApath(content, ea);
        newContent = UnicodeFormatter.decodeUnicodeString(newContent);
        hexagonTemplatePageEntity.setContent(newContent);
        hexagonTemplatePageEntity.setHexagonTemplateSiteId(siteId);
        hexagonTemplatePageEntity.setId(id);
        hexagonTemplatePageEntity.setEa(ea);
        hexagonTemplatePageEntity.setStatus(HexagonStatusEnum.NORMAL.getType());
        hexagonTemplatePageEntity.setCreateBy(userId);
        hexagonTemplatePageEntity.setCreateBy(userId);

        int insertPageResult = hexagonTemplatePageDAO.insert(hexagonTemplatePageEntity);
        if (insertPageResult != 1) {
            log.error("HexagonService.editSite insert page failed, entity={}", insertPageResult);
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }

        //根据pageEntity获取formID，更新表单的ID，ObjectID(即页面ID）
        String formID = pageEntity.getFormId();
        GetCustomizeFormDataByIdArg getCustomizeFormDataByIdArg = new GetCustomizeFormDataByIdArg();
        getCustomizeFormDataByIdArg.setId(formID);
        Result<CustomizeFormDataDetailResult> customizeFormDataResult = customizeFormDataManager.getCustomizeFormDataById(getCustomizeFormDataByIdArg);
        if (customizeFormDataResult.getData() != null && customizeFormDataResult.isSuccess()) {
            //包含TagNameList
            AddCustomizeFormDataArg addCustomizeFormDataArg = BeanUtil.copy(customizeFormDataResult.getData(), AddCustomizeFormDataArg.class);
            addCustomizeFormDataArg.setEa(ea);
            addCustomizeFormDataArg.setFsUserId(userId);

            //查询是否有该线索池id
            if (StringUtils.isNotBlank(addCustomizeFormDataArg.getCrmPoolId())){
                Result<List<LeadPoolResult>> poolResult =  crmService.listLeadPools(ea, -10000);
                if (poolResult == null || !poolResult.isSuccess() || org.apache.commons.collections4.CollectionUtils.isEmpty(poolResult.getData())){
                    addCustomizeFormDataArg.setCrmPoolId(null);
                }else {
                    List<String> poolIds = poolResult.getData().stream().map(item -> item.getId()).collect(Collectors.toList());
                    if (!poolIds.contains(addCustomizeFormDataArg.getCrmPoolId())){
                        addCustomizeFormDataArg.setCrmPoolId(poolIds.get(0));
                    }
                }
            }
            //插入表单数据
            Result<AddCustomizeFormDataResult> addCustomizeFormDataResult = customizeFormDataService.addCustomizeFormData(addCustomizeFormDataArg, null,false);
            if (!addCustomizeFormDataResult.isSuccess()) {
                log.warn("HexagonService copyPage insertCustomizeFormData failed,addCustomizeFormDataArg,{}", addCustomizeFormDataArg);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
            //绑定物料 addCustomizeFormDataResult重新生成formID,customizeFormDataObject
            String newFormID = addCustomizeFormDataResult.getData().getId();
            log.info("copy page newFormID:{}", newFormID);
            //绑定
            customizeFormDataManager.bindCustomizeFormDataObject(newFormID, hexagonTemplatePageEntity.getId(), ObjectTypeEnum.HEXAGON_PAGE.getType(), ea, userId, null, null, null);
            //page插入formID
            HexagonTemplatePageEntity formPageEntity = BeanUtil.copy(hexagonTemplatePageEntity, HexagonTemplatePageEntity.class);
            formPageEntity.setFormId(newFormID);
            int updateResult = hexagonTemplatePageDAO.update(formPageEntity);
            if (updateResult != 1) {
                log.error("HexagonService.editSite update page form failed, entity={}", updateResult);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }

        return Result.newSuccess(id);
    }

    public List<HexagonTemplatePageEntity> resetContentActionId(String preSiteId, String newSiteId, Integer source) {
        //获取新站点以及旧站点的页面ID
        List<HexagonTemplatePageEntity> prePageList;
        if(source!=null && source==2){
            List<HexagonPageEntity> hexagonPageEntity = hexagonPageDAO.getByHexagonSiteId(preSiteId);
            prePageList = BeanUtil.copy(hexagonPageEntity, HexagonTemplatePageEntity.class);
        }else{
            prePageList = hexagonTemplatePageDAO.getBySiteId(preSiteId);
        }
        List<HexagonTemplatePageEntity> newPageList = hexagonTemplatePageDAO.getBySiteId(newSiteId);

        Map<String, String> prePageName = Maps.newHashMap();
        Map<String, String> nextPageName = Maps.newHashMap();
        List<HexagonTemplatePageEntity> finalPageEntity = com.google.common.collect.Lists.newArrayList();
        for (int i = 0; i < prePageList.size(); i++) {
            prePageName.put(prePageList.get(i).getName(), prePageList.get(i).getId());
            nextPageName.put(newPageList.get(i).getName(), newPageList.get(i).getId());
        }

        for (HexagonTemplatePageEntity newTemplatePageEntity : newPageList){
            String content = newTemplatePageEntity.getContent();
            Iterator<Entry<String, String>> it = prePageName.entrySet().iterator();
            while (it.hasNext()){
                Map.Entry<String, String> entry = it.next();
                content= content.replace(entry.getValue(), nextPageName.get(entry.getKey()));
            }
            String newContent = UnicodeFormatter.decodeUnicodeString(content);
            newTemplatePageEntity.setContent(newContent);
            finalPageEntity.add(newTemplatePageEntity);
        }
        return finalPageEntity;
    }


    public void bindHexagonSiteObject(String siteId, String objectId, Integer objectType, String ea, Integer fsUserId, Integer formStyleType, String formButtonName, ButtonStyle buttonStyle) {
        HexagonSiteObjectEntity entity = hexagonSiteObjectDAO.getObjectBindingHexagonSite(ea, objectId, objectType);
        if (entity != null) {
            hexagonSiteObjectDAO.updateHexagonSiteObjectStatus(entity.getId(), HexagonSiteObjectEnum.UNBOUND.getType());
        }
        HexagonSiteObjectEntity hexagonSiteObjectEntity = new HexagonSiteObjectEntity();
        hexagonSiteObjectEntity.setId(UUIDUtil.getUUID());
        hexagonSiteObjectEntity.setEa(ea);
        hexagonSiteObjectEntity.setSiteId(siteId);
        hexagonSiteObjectEntity.setObjectId(objectId);
        hexagonSiteObjectEntity.setObjectType(objectType);
        hexagonSiteObjectEntity.setCreateBy(fsUserId);
        hexagonSiteObjectEntity.setUpdateBy(fsUserId);
        if (formStyleType == null) {
            formStyleType = FormStyleTypeEnum.BOTTOM.getType();
        }
        if (buttonStyle == null) {
            buttonStyle = new ButtonStyle();
            buttonStyle.setButtonColor("#FCB058");
            buttonStyle.setFontColor("#FFFFFF");
            buttonStyle.setButtonBorderColor("#FCB058");
        }
        hexagonSiteObjectEntity.setFormStyleType(formStyleType);
        hexagonSiteObjectEntity.setFormButtonName(org.apache.commons.lang3.StringUtils.isEmpty(formButtonName) ? null : formButtonName);
        hexagonSiteObjectEntity.setButtonStyle(buttonStyle);
        hexagonSiteObjectEntity.setStatus(HexagonSiteObjectEnum.USING.getType());
        hexagonSiteObjectDAO.insertHexagonSiteObject(hexagonSiteObjectEntity);
    }

    public HexagonSiteEntity getBindHexagonSiteByObject(String siteId) {
        // 查询对应微页面数据
        HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(siteId);
        if (hexagonSiteEntity == null) {
            log.warn("HexagonSiteManager.getBindHexagonSiteByObject error hexagonSiteEntity is null siteId:{}", siteId);
            return null;
        }
        if (hexagonSiteEntity.getStatus().equals(CustomizeFormDataStatusEnum.DELETE.getValue())) {
            // 若微页面删除则删除物料和微页面的绑定关系
            hexagonSiteDAO.deleteById(hexagonSiteEntity.getId());
            return null;
        }
        return hexagonSiteEntity;
    }
}
