/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.baidu;

import com.facishare.marketing.api.vo.advertiser.headlines.SyncHeadlinesLocalLeadsVO;
import com.facishare.marketing.common.contstant.CrmConstants;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.advertiser.headlines.HeadlinesAdAccountTypeEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.AdThirdSyncDataArg;
import com.facishare.marketing.api.arg.advertiser.QueryAdGroupDetailArg;
import com.facishare.marketing.api.arg.advertiser.QueryAdGroupListArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.arg.marketingSpreadSource.MarketingPromotionSourceArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.baidu.*;
import com.facishare.marketing.api.service.baidu.BaiduCampaignService;
import com.facishare.marketing.api.vo.baidu.QueryCampaignDetailVO;
import com.facishare.marketing.api.vo.baidu.QueryCampaignListVO;
import com.facishare.marketing.api.vo.baidu.RelateMarketingEventVO;
import com.facishare.marketing.api.vo.baidu.TrendGraphDataVO;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.AccountStatusEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.mq.sender.RequestBufferSender;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.bo.advertise.BaiduAdGroupBO;
import com.facishare.marketing.provider.dao.EnterpriseMetaConfigDao;
import com.facishare.marketing.provider.dao.advertiser.clue.AdLeadsDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDataDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.baidu.*;
import com.facishare.marketing.provider.entity.EnterpriseMetaConfigEntity;
import com.facishare.marketing.provider.entity.ThirdPlatformSecretEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.baidu.*;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerArg.CreateOrUpdateMarketingLeadSyncRecordObjArg;
import com.facishare.marketing.provider.innerArg.MarketingAdShenCeArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.innerResult.crm.CreateLeadResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.advertiser.*;
import com.facishare.marketing.provider.manager.advertiser.headlines.AdTokenManager;
import com.facishare.marketing.provider.manager.advertiser.headlines.HeadlinesAdMarketingManager;
import com.facishare.marketing.provider.manager.advertiser.ocpc.AdOCPCUploadManager;
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.facishare.marketing.provider.manager.baidu.RefreshDataManager;
import com.facishare.marketing.provider.manager.baidu.UtmDataManger;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingPromotionSourceObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.mq.sender.AdvertiseCallbackMessageSender;
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.util.ContextUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.cloud.DataPersistor;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by ranluch on 2019/11/26.
 */
@Slf4j
@Service("baiduCampaignService")
public class BaiduCampaignServiceImpl implements BaiduCampaignService {
    @Autowired
    private BaiduCampaignDataDAO campaignDataDAO;
    @Autowired
    private BaiduCampaignDAO campaignDAO;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Resource(name = "httpSupport")
    private OkHttpSupport httpSupport;
    @Autowired
    private RefreshDataManager refreshDataManager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private UtmDataManger utmDataManger;
    @Autowired
    private CampaignDataManager campaignDataManager;
    @Autowired
    private SpreadChannelManager spreadChannelManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;
    @Autowired
    private AdLeadsDAO adLeadsDAO;
    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;
    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private AdMarketingHandlerActionManager adMarketingHandlerActionManager;
    @Autowired
    private HeadlinesAdMarketingManager headlinesAdMarketingManager;
    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Autowired
    private AdOCPCUploadManager adOCPCUploadManager;

    @Autowired
    private RequestBufferSender requestBufferSender;

    @Autowired
    private AdvertiseCallbackMessageSender advertiseCallbackMessageSender;

    @Autowired
    private HeadlinesAdDataDAO headlinesAdDataDAO;

    @Autowired
    private AdTokenManager adTokenManager;

    @Autowired
    private MarketingPromotionSourceObjManager marketingPromotionSourceObjManager;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Autowired
    private MarketingRecordActionSender marketingRecordActionSender;

    @Autowired
    private ThirdPlatformSecretManager thirdPlatformSecretManager;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Autowired
    private AdBigScreenManager adBigScreenManager;

    @Autowired
    private EnterpriseMetaConfigDao enterpriseMetaConfigDao;

    @Autowired
    private HttpManager httpManager;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private AdGroupDAO adGroupDAO;

    @Autowired
    private BaiduAdGroupDataDAO baiduAdGroupDataDAO;

    @Autowired
    private BaiduAdGroupFeedDataDAO baiduAdGroupFeedDataDAO;

    @ReloadableProperty("center.host")
    private String centerHost;

    @ReloadableProperty("skip.refresh.ad.data")
    private boolean skipRefreshAdData;

    private final String AD_SYNC_LOCK_KEY = "mk:ad:sync:lock:%s";

    private final String AD_KEYWORD_SYNC_LOCK_KEY = "mk:ad:kw:sync:lock:%s";

    @Override
    public Result<PageResult<CampaignDetailResult>> queryCampaignList(QueryCampaignListVO vo) {
        if (vo.getPageNum() == null || vo.getPageSize() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<CampaignDetailResult> pageResult = new PageResult();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<BaiduCampaignEntity> campaignList = campaignDAO.pageCampaign(vo.getEa(), vo.getAdAccountId(), vo.getStatus(), StringUtils.isBlank(vo.getNameKey()) ? null : vo.getNameKey(), page);
        if (CollectionUtils.isEmpty(campaignList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());

        List<String> ids = campaignList.stream().map(BaiduCampaignEntity::getId).collect(Collectors.toList());
        List<CampaignDataDtoEntity> campaignDataList = campaignDataDAO.queryCampaignData(ids, vo.getStartTime(), vo.getEndTime());
        pageResult.setResult(buildCampaignDetailResults(vo.getEa(), campaignDataList, vo.getStartTime().getTime(), vo.getEndTime().getTime(), vo.getSource()));

        return Result.newSuccess(pageResult);
    }

    private List<CampaignDetailResult> buildCampaignDetailResults(String ea, List<CampaignDataDtoEntity> campaignDataList, Long startTime, Long endTime, String source) {
        List<CampaignDetailResult> detailResults = Lists.newArrayList();
        if (CollectionUtils.isEmpty(campaignDataList)) {
            return null;
        }
        List<String> marketingEventIds = campaignDataList.stream().filter(campaignData -> StringUtils.isNotBlank(campaignData.getMarketingEventId()))
                .map(campaignData -> campaignData.getMarketingEventId()).collect(Collectors.toList());

        Map<String, MarketingEventData> eventDataMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            List<MarketingEventData> eventDataList = marketingEventManager.listMarketingEventData(ea, -10000, marketingEventIds);
            if (CollectionUtils.isNotEmpty(eventDataList)) {
                eventDataMap.putAll(eventDataList.stream().collect(Collectors.toMap(MarketingEventData :: getId, v -> v, (k1, k2) -> k1)));
            }
        }

        //通过campaign名称关联线索
        Map<String, Integer> marketingEventClueMap = campaignDataManager.getCampaignClueMap(ea, marketingEventIds, startTime, endTime, source);
        for (CampaignDataDtoEntity campaignData :campaignDataList ){
            CampaignDetailResult detailResult = BeanUtil.copy(campaignData, CampaignDetailResult.class);
            if (campaignData.getClick() != null && campaignData.getCost() != null) {
                if (campaignData.getClick() == 0) {
                    detailResult.setClickPrice(0D);
                } else {
                    detailResult.setClickPrice(Math.round(campaignData.getCost() * 100d / campaignData.getClick()) / 100d);
                }
            }
            if (campaignData.getCost() != null) {
                detailResult.setCost(Math.round(campaignData.getCost() * 100d) / 100d);
            }
            if (StringUtils.isNotBlank(campaignData.getMarketingEventId()) && eventDataMap.containsKey(campaignData.getMarketingEventId())) {
                detailResult.setMarketingEventName(eventDataMap.get(campaignData.getMarketingEventId()).getName());
                detailResult.setEventType(eventDataMap.get(campaignData.getMarketingEventId()).getEventType());
            }
            detailResult.setRefreshTime(campaignData.getRefreshTime() == null ? null : campaignData.getRefreshTime().getTime());
            if (StringUtils.isEmpty(campaignData.getMarketingEventId()) || marketingEventClueMap == null) {
                detailResult.setLeads(0);
            } else {
                detailResult.setLeads(marketingEventClueMap.get(campaignData.getMarketingEventId()) == null ? 0 : marketingEventClueMap.get(campaignData.getMarketingEventId()));
            }

            detailResults.add(detailResult);
        }

        return detailResults;
    }

    @Override
    public Result<CampaignDetailResult> queryCampaignDetail(QueryCampaignDetailVO vo) {
        List<CampaignDataDtoEntity> campaignDataList = campaignDataDAO.queryCampaignData(Lists.newArrayList(vo.getId()), vo.getStartTime(), vo.getEndTime());
        if (CollectionUtils.isEmpty(campaignDataList)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        CampaignDetailResult result = new CampaignDetailResult();
        List<CampaignDetailResult> campaignDetailResults = buildCampaignDetailResults(vo.getEa(), campaignDataList, null, null, vo.getSource());
        if (CollectionUtils.isNotEmpty(campaignDetailResults)) {
            log.info("BaiduCampaignServiceImpl.queryCampaignDetail buildCampaignDetailResults campaignDetailResults:{}", campaignDetailResults);
            result = campaignDetailResults.get(0);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Map<String, Integer> getCampaignClueMap(String ea, List<String> marketingEventIds){
        Map<String, Integer> clueMap = new HashMap<>();
        if (CollectionUtils.isEmpty(marketingEventIds)){
            return clueMap;
        }
        for (String marketingEventId : marketingEventIds) {
            int clueCount = crmV2Manager.getCrmObjectEntityTotalCount(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), Lists.newArrayList(marketingEventId), null);
            clueMap.putIfAbsent(marketingEventId, clueCount);
        }
        return clueMap;
    }

    @Override
    public  Result<List<QuerySpreadChannelStatResult>>  querySpreadChannelStatByMarketingEventId(String ea, String marketingEventId, String source, Long startTime, Long endTime) {
        Map<String, String> channelValueMap = spreadChannelManager.queryChannelMapData(ea);
        if (MapUtils.isEmpty(channelValueMap)) {
            log.warn("BaiduCampaignServiceImpl.querySpreadChannelStatByMarketingEventId");
            return Result.newSuccess();
        }
        if (AdSourceEnum.SOURCE_JULIANG.equals(AdSourceEnum.getByValue(source))) {
            source = null;
        }

        int clueCount = campaignDataManager.getLeadsCountByMarketingEvent(ea,  Lists.newArrayList(marketingEventId), startTime, endTime, null);
        Map<String, Integer> clueMap = campaignDataManager.querySpreadChannelStatByMarketingEventId(ea, marketingEventId, channelValueMap);
        List<QuerySpreadChannelStatResult> list = Lists.newArrayList();
        int spreadLeadCount = 0;
        QuerySpreadChannelStatResult otherChannel = null;
        if (clueMap != null){
            for (Map.Entry<String, Integer> entry : clueMap.entrySet()){
                QuerySpreadChannelStatResult spreadChannelStatResult = new QuerySpreadChannelStatResult();
                spreadChannelStatResult.setChannelName(channelValueMap.get(entry.getKey()));
                spreadChannelStatResult.setClueNum(entry.getValue() == null ? 0 : entry.getValue());
                if (spreadChannelStatResult.getChannelName().equals("其他")){
                    otherChannel = spreadChannelStatResult;
                }else {
                    list.add(spreadChannelStatResult);
                }
                spreadLeadCount += spreadChannelStatResult.getClueNum();
            }
            list.add(otherChannel);
        }

        //兼容其他
        if (clueCount > spreadLeadCount){
            if (otherChannel != null){
                otherChannel.setClueNum(otherChannel.getClueNum() + (clueCount - spreadLeadCount));
            }else {
                QuerySpreadChannelStatResult spreadChannelStatResult = new QuerySpreadChannelStatResult();
                spreadChannelStatResult.setChannelName(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_888));
                spreadChannelStatResult.setClueNum(clueCount - spreadLeadCount);
            }
        }

        return Result.newSuccess(list);
    }

    @Override
    public Result<Void> relateMarketingEvent(RelateMarketingEventVO vo) {
        BaiduCampaignEntity campaignEntity = campaignDAO.queryByMarketingEventId(vo.getEa(), vo.getAdAccountId(), vo.getMarketingEventId());
        if (campaignEntity != null) {
            return Result.newError(SHErrorCode.CAMPAIGN_RELATE_MARKETING_EVENT_ERROR);
        }
        BaiduCampaignEntity campaignById = campaignDAO.queryCampaignById(vo.getId());
        if (campaignById == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        try {
            campaignDAO.relateCampaignMarketingEvent(vo.getId(), vo.getMarketingEventId(), 25);
        } catch (Exception e) {
            log.info("BaiduCampaignServiceImpl relateMarketingEvent db exception: {}", e.fillInStackTrace());
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<TrendGraphDataResult> getTrendGraphDataList(TrendGraphDataVO vo) {
        TrendGraphDataResult result = new TrendGraphDataResult();
        List<TrendGraphDataResult.TrendGraphData> trendGraphDataList = Lists.newArrayList();
        result.setTrendGraphDataList(trendGraphDataList);

        List<TrendGraphDataDTOEntity> entityList = null;

        AdSourceEnum adSourceEnum = AdSourceEnum.getBySource(vo.getSource());
        if (AdSourceEnum.SOURCE_BAIDU.equals(adSourceEnum)) {
            if (TypeEnum.BAIDU_SEARCH_AD_GROUP.getCode().equals(vo.getDataType())) {
                entityList = baiduAdGroupDataDAO.queryTrendGraphData(vo.getEa(), vo.getMarketingEventId(), vo.getStartTime(), vo.getEndTime());
            } else if (TypeEnum.BAIDU_FEED_AD_GROUP.getCode().equals(vo.getDataType())) {
                entityList = baiduAdGroupFeedDataDAO.queryTrendGraphData(vo.getEa(), vo.getMarketingEventId(), vo.getStartTime(), vo.getEndTime());
            } else {
                entityList = campaignDataDAO.queryTrendGraphData(vo.getEa(), vo.getMarketingEventId(), vo.getStartTime(), vo.getEndTime(), vo.getSource());
            }
        }
        if (AdSourceEnum.SOURCE_JULIANG.equals(adSourceEnum)) {
            entityList = headlinesAdDataDAO.queryTrendGraphDataByAdData(vo.getEa(), vo.getMarketingEventId(), vo.getStartTime(), vo.getEndTime(), vo.getSource());
        }
        if (AdSourceEnum.SOURCE_TENCETN.equals(adSourceEnum)) {
            // todo 腾讯
        }

        if (CollectionUtils.isNotEmpty(entityList)) {
            entityList.forEach(trendGraphData -> {
                TrendGraphDataResult.TrendGraphData graphData = new TrendGraphDataResult.TrendGraphData();
                graphData.setDate(DateUtil.parse(trendGraphData.getActionDate(), DateUtil.DATE_FORMAT_DAY));
                graphData.setPv(trendGraphData.getPv());
                graphData.setClick(trendGraphData.getClick());
                if (trendGraphData.getCost() != null) {
                    graphData.setCost(Math.round(trendGraphData.getCost() * 100d) / 100d);
                }
                if (trendGraphData.getClick() != null && trendGraphData.getCost() != null) {
                    graphData.setClickPrice(Math.round(trendGraphData.getCost() * 100d / trendGraphData.getClick()) / 100d);
                }
                trendGraphDataList.add(graphData);
            });
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> refreshAllDataByAccountId(String accountId) {
        ContextUtil.buildNewTraceContext();
        ThreadPoolUtils.execute(() -> refreshAllDataByAccountIdInner(accountId), ThreadPoolUtils.ThreadPoolTypeEnums.REFRESH_AD_DATA);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> refreshKeywordByAccountId(String accountId) {
        ContextUtil.buildNewTraceContext();
        ThreadPoolUtils.execute(() -> refreshKeywordByAccountIdInner(accountId), ThreadPoolUtils.ThreadPoolTypeEnums.REFRESH_AD_DATA);
        return Result.newSuccess();
    }

    private void refreshKeywordByAccountIdInner(String accountId) {
        long expireTime = 12 * 60 * 60;
        String key = String.format(AD_KEYWORD_SYNC_LOCK_KEY, accountId);
        boolean lock = redisManager.lock(key, expireTime);
        if (!lock) {
            log.info("广告关键词同步任务已经调度过了, accountId: {}", accountId);
            return;
        }
        try {
            AdAccountEntity accountEntity = adAccountManager.queryAccountById(accountId);
            log.info("开始刷新广告关键词数据, account: {}", accountEntity);
            if (marketingActivityRemoteManager.enterpriseStop(accountEntity.getEa()) || appVersionManager.getCurrentAppVersion(accountEntity.getEa()) == null) {
                log.info("BaiduCampaignServiceImpl.refreshAllData failed enterprise stop or license expire ea:{}", accountEntity.getEa());
                return ;
            }
            if (accountEntity.getStatus() == AccountStatusEnum.STOP.getStatus()) {
                return;
            }
            String value = AdSourceEnum.getValueBySource(accountEntity.getSource());
            AdMarketingManager adMarketingActionManager = adMarketingHandlerActionManager.getAdMarketingActionManager(value);
            adMarketingActionManager.refreshKeywordData(accountEntity.getEa(), accountEntity.getId(), accountEntity.getSource());
        } catch (Exception e) {
            log.error("refreshKeywordByAccountIdInner error, accountId: {}", accountId, e);
        } finally {
            redisManager.unLock(key);
        }
    }


    @Override
    public Result<Void> refreshLocalLeadByAccountId(SyncHeadlinesLocalLeadsVO vo) {
        ContextUtil.buildNewTraceContext();
        List<AdAccountEntity> adAccountEntityList = Lists.newArrayList();
        Date endTime = new Date(vo.getEndTime());
        String lockKey = "";
        if (StringUtils.isNotBlank(vo.getAdAccountId())) {
            AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(vo.getAdAccountId());
            if (adAccountEntity == null || adAccountEntity.getType() == null || adAccountEntity.getType() != HeadlinesAdAccountTypeEnum.LOCAL.getType()) {
                return Result.newSuccess();
            }
            adAccountEntityList.add(adAccountEntity);
            // (这里key之所以区分手动同步和定时任务执行同步线索，是因为手动同步与定时任务加锁时不要相互冲突)
            // 例如手动同步时，如果定时任务对某个广告账户id加了锁，那么会导致手动同步线索就不执行了，所以进行区分
            lockKey += "mk:hmsl:"; // hand:movement:sync:lead 手动同步时要加的分布式锁key
        } else {
            // 查询出所有的未解绑的本地推账户(不区分ea)
            adAccountEntityList = adAccountManager.queryAllLocalAccount(AdSourceEnum.SOURCE_JULIANG.getSource(), HeadlinesAdAccountTypeEnum.LOCAL.getType());
            lockKey += "mk:jsl:";
        }
        String finalLockKey = lockKey;
        for (AdAccountEntity adAccountEntity : adAccountEntityList) {
            String redisKey = finalLockKey + adAccountEntity.getId();
            boolean lock = redisManager.lockIgnoreThread(redisKey, 60 * 60); // 异步线程加锁
            if (!lock) {
                log.warn("refreshLocalLeadByAccountId get lock fail, key: {} adAccount: {}", redisKey, adAccountEntity);
                continue;
            }
            ThreadPoolUtils.executeWithNewThread(() -> {
                try {
                    // startTime为空，说明是定时任务过来的，
                    Date startTime;
                    if (vo.getStartTime() != null) {
                        startTime = new Date(vo.getStartTime());
                    } else {
                        // 获取该账号上一次同步成功的时间，作为本次同步的开始时间, 如果为空，默认是结束时间的当天0点
                        Date lastSyncTime = headlinesAdMarketingManager.getLastLocalClueSyncTime(adAccountEntity.getEa(), adAccountEntity.getId());
                        startTime = lastSyncTime == null ? DateUtil.getDayStartDate(endTime) : DateUtil.minusSecond(lastSyncTime, 120 * 60);
                    }
                    headlinesAdMarketingManager.syncHeadlinesLocalClueDataToClueObj(adAccountEntity, startTime, endTime);
                } catch (Exception e) {
                    log.error("HeadlinesAdMarketingManager syncHeadlinesLocalClueDataToClueObj fail ea:{}, adAccount:{}", adAccountEntity.getEa(), adAccountEntity, e);
                } finally {
                    redisManager.unLockIgnoreThread(redisKey);
                }
            });

        }
        return Result.newSuccess();
    }


    private Result<Void> refreshAllDataByAccountIdInner(String accountId) {
        if (skipRefreshAdData) {
            log.info("跳过广告账号数据刷新");
            return Result.newSuccess();
        }
        long expireTime = 12 * 60 * 60;
        String key = String.format(AD_SYNC_LOCK_KEY, accountId);
        boolean lock = redisManager.lock(key, expireTime);
        if (!lock) {
            log.info("广告同步已经调度过了, accountId: {}", accountId);
            return Result.newSuccess();
        }
        try {
            AdAccountEntity accountEntity = adAccountManager.queryAccountById(accountId);
            log.info("开始刷新广告账号数据, account: {}", accountEntity);
            if (marketingActivityRemoteManager.enterpriseStop(accountEntity.getEa()) || appVersionManager.getCurrentAppVersion(accountEntity.getEa()) == null){
                log.info("BaiduCampaignServiceImpl.refreshAllData failed enterprise stop or license expire ea:{}", accountEntity.getEa());
                return Result.newSuccess();
            }
            // 每天都去获取一次accessToken,以防refreshToken也过期、包括停用的
            tryGetAccessTokenDaily(accountEntity);
            if (accountEntity.getStatus() == AccountStatusEnum.STOP.getStatus()) {
                return Result.newSuccess();
            }
            String value = AdSourceEnum.getValueBySource(accountEntity.getSource());
            AdMarketingManager adMarketingActionManager = adMarketingHandlerActionManager.getAdMarketingActionManager(value);
            adMarketingActionManager.refreshAllData(accountEntity.getEa(), accountEntity.getId(), accountEntity.getSource());
        } catch (Exception e) {
            log.error("refreshAllDataByAccountId error, accountId: {}", accountId, e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        } finally {
            redisManager.unLock(key);
        }
        return Result.newSuccess();
    }

    @Override
    public void refreshAllData() {

    }

    private void tryGetAccessTokenDaily(AdAccountEntity accountEntity) {
        try {
            if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(accountEntity.getSource())) {
                if (StringUtils.isNotBlank(accountEntity.getRefreshToken())) {
                    adTokenManager.getBaiduAccessToken(accountEntity);
                }
            } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(accountEntity.getSource())) {
                adTokenManager.getAccessToken(accountEntity);
            } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(accountEntity.getSource())) {
                adTokenManager.getTencentAccessToken(accountEntity);
            }
        } catch (Exception e) {
            log.error("tryGetAccessToken error, account : [{}]", accountEntity);
        }
    }

    @Override
    public Result<String> syncThirdLeadData(AdThirdSyncDataArg arg) {
            String leadId = null;
            String marketingEventId = null;
            Integer createBy = null;
            try {
                if (arg.getData() == null) {
                    log.warn("广告平台同步数据异常,请检查配置，arg:[{}]", JSON.toJSONString(arg));
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
                log.info("广告平台同步数据，arg:[{}]", JSON.toJSONString(arg));
                EnterpriseMetaConfigEntity enterpriseMetaConfigEntity = enterpriseMetaConfigDao.getByEa(arg.getEa());
                if (arg.getCheckForwardToOtherCloud() && enterpriseMetaConfigEntity == null) {
                    // 在纷享云找不到ea,将请求转发到专属云
                    transferToPrivateCloud(arg);
                    return Result.newSuccess("transferToPrivateCloud");
                }
                refillAdSource(arg);
                // 是否重复推送且推送的数据不一致
                boolean isRepeatAndDiffPush = false;
                //线索是否已经同步，同步过的线索不再同步
                if (StringUtils.isNotEmpty(arg.getClueId()) && StringUtils.isBlank(arg.getMarketingLeadSyncRecordObjId())) {
                    AdLeadsEntity entity = adLeadsDAO.queryLeadsByEaAndSourceLeadIdAndSource(arg.getEa(), arg.getClueId(), arg.getAdSource());
                    if (entity != null) {
                        isRepeatAndDiffPush = isRepeatAndDiffPush(entity, arg);
                        log.info("syncThirdLeadData clue is already sync arg:{} isRepeatAndDiffPush: {}", arg, isRepeatAndDiffPush);
                        if (!isRepeatAndDiffPush) {
                            tryCreateOrUpdateMarketingLeadSyncRecordObj(arg, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_175));
                            return Result.newSuccess();
                        }
                    }
                    if (!isRepeatAndDiffPush) {
                        //这里先保存，防止重复线索 线索创建成功后再更新
                        AdLeadsEntity adLeadsEntity = new AdLeadsEntity();
                        adLeadsEntity.setId(UUIDUtil.getUUID());
                        adLeadsEntity.setEa(arg.getEa());
                        adLeadsEntity.setSource(arg.getAdSource());
                        // 先写clueId吧，下面更新
                        adLeadsEntity.setSource_lead_id(arg.getClueId());
                        adLeadsEntity.setLeadId(arg.getClueId());
                        adLeadsEntity.setSyncArg(JsonUtil.toJson(arg));
                        int result = adLeadsDAO.addAdLeads(adLeadsEntity);
                        if (result <= 0) {
                            log.info("syncThirdLeadData clue is already sync arg:{}", arg);
                            tryCreateOrUpdateMarketingLeadSyncRecordObj(arg, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_HANDLERS_OFFICIALWEBSITETHIRDPLATEFORMEVENTHANDLER_175));
                            return Result.newSuccess();
                        }
                    }
                }
                //设置线索来源渠道promotion_channel
                arg.getData().put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("广告"));
                if (StringUtils.isNotEmpty(arg.getFunApiName())) {
                    Map<String, Object> objectMap = refreshDataManager.syncLeadCallFunc(arg.getEa(), arg.getData(), arg.getFunApiName());
                    log.info("syncLeadCallFunc ea: [{}] funcName:[{}] beforeCallFunc: [{}] afterCallFunc: [{}],", arg.getEa(), arg.getFunApiName(), arg.getData(), objectMap);
                    try {
                        if (objectMap.get("success") != null && Boolean.parseBoolean(objectMap.get("success").toString()) && objectMap.get("functionResult") != null ) {
                            arg.setData(ObjectData.convert((Map) objectMap.get("functionResult")));
                        }
                    } catch (Exception e) {
                        log.error("syncThirdLeadData 解析函数结果异常, objectMap : {}", objectMap, e);
                    }
                }
                if (arg.getData().get("saveLeadFlag") != null && arg.getData().get("saveLeadFlag").equals("0")) {
                    log.info("syncThirdLeadData saveLeadFlag equal 0 ea:{} arg:{}", arg.getEa(), arg.getData());
                    tryCreateOrUpdateMarketingLeadSyncRecordObj(arg, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, "saveLeadFlag equal 0");
                    return Result.newSuccess();
                }
                marketingEventId = getMarketingEventId(arg);
                if (StringUtils.isNotBlank(marketingEventId)) {
                    arg.getData().put("marketing_event_id", marketingEventId);
                }
                createBy = clueDefaultSettingService.getClueCreator(marketingEventId, arg.getEa(), ClueDefaultSettingTypeEnum.AD.getType());
                //重复线索不进入CRM
                arg.getData().put("object_describe_api_name", LeadsFieldContants.API_NAME);
                arg.getData().put("object_describe_id", LeadsFieldContants.API_NAME);
                String landingUrl = arg.getLandingUrl();
                if (StringUtils.isNotBlank(landingUrl) && StringUtils.isNotBlank(arg.getBdVid()) && !landingUrl.contains("bd_vid=")) {
                    if (!landingUrl.contains("?")) {
                        landingUrl += "?bd_vid=" + arg.getBdVid();
                    } else {
                        landingUrl += "&bd_vid=" + arg.getBdVid();
                    }
                }
                String pageName = arg.getPageName() == null ? I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCUPLOADMANAGER_1038) : arg.getPageName();
                String landingObjId = null;
                if (StringUtils.isNotBlank(landingUrl) && HttpUtil.isUrl(landingUrl)) {
                    landingObjId = adOCPCUploadManager.getOrCreateLandingObjId(arg.getEa(), landingUrl, pageName, null);
                }
                if (StringUtils.isNotBlank(landingObjId)) {
                    arg.getData().put("landing_page_id", landingObjId);
                }
                ActionAddArg actionAddArg = new ActionAddArg();
                actionAddArg.setObjectData(arg.getData());
                Map<String, Object> params = arg.getData();
                params.put("from_marketing", true);
                params.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
                params.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());
                ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
                optionInfo.setIsDuplicateSearch(true);
                optionInfo.setCalculateDefaultValue(true);
                actionAddArg.setOptionInfo(optionInfo);
                // 创建营销推广来源
                String marketingPromotionSourceId = createMarketingPromotionSource(arg, marketingEventId, landingUrl);
                if (StringUtils.isNotBlank(marketingPromotionSourceId)) {
                    params.put("marketing_promotion_source_id", marketingPromotionSourceId);
                }
                // 若满足以下条件，线索的【参与赔付状态】字段为【参与基木鱼赔付】 注:目前这个字段只有 参与基木鱼赔付 2023-03-23
                // 1 【销售线索】关联落地页带vid参数 2. 【销售线索】关联的【营销推广来源】关联的【基木鱼线索ID】不为空
                if (StringUtils.isNotBlank(landingUrl) && landingUrl.contains("bd_vid")
                        && StringUtils.isNotBlank(marketingPromotionSourceId) && StringUtils.isNotBlank(arg.getClueId())) {
                    params.put("join_compensate_status", "jmy_compensate");
                }
                CreateLeadResult createLeadResult = new CreateLeadResult();
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = metadataActionService.add(createHeaderObj(arg.getEa(), createBy), LeadsFieldContants.API_NAME, false, actionAddArg);
                if (result != null && result.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
                    leadId = (String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName());
                    log.info("syncThirdLeadData createLead success arg:{} leadId:{}", arg, leadId);
                    tryCreateOrUpdateMarketingLeadSyncRecordObj(arg,  leadId, MarketingLeadSyncRecordObjManager.SUCCESS_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_750));
                } else if (result != null && result.getCode() == CrmConstants.REPEAT_CODE) {
                    log.info("syncThirdLeadData failed duplicate lead arg:{} result: {}", arg, result);
                    tryCreateOrUpdateMarketingLeadSyncRecordObj(arg, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
                    return Result.newSuccess();
                } else {
                    createLeadResult.setMessage(result != null ? result.getMessage() : I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR));
                    log.info("syncThirdLeadData createLead failed arg:{} message:{}", arg, result != null ? result.getMessage() : "metadataActionService.add invoke error");
                    tryCreateOrUpdateMarketingLeadSyncRecordObj(arg, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, createLeadResult.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : createLeadResult.getMessage());
                    return Result.newError(-1, createLeadResult.getMessage());
                }
                if(StringUtils.isNotBlank(arg.getClueId())) {
                    AdLeadsEntity adLeadsEntity = adLeadsDAO.queryByEaAndSourceLeadIdAndLeadId(arg.getEa(), arg.getClueId(), arg.getClueId());
                    if (adLeadsEntity != null && StringUtils.isNotBlank(leadId)) {
                        adLeadsDAO.updateLeadId(arg.getEa(), adLeadsEntity.getId(), leadId);
                    }
                }

                //上报神策系统
                if (StringUtils.equals(arg.getAdSource(), AdSourceEnum.SOURCE_BAIDU.getSource())) {
                    marketingStatLogPersistorManger.sendLeadData(arg.getEa(), leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_BAIDU_SYNC);
                }else if(StringUtils.equals(arg.getAdSource(), AdSourceEnum.SOURCE_JULIANG.getSource())){
                    marketingStatLogPersistorManger.sendLeadData(arg.getEa(), leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_TOUTIAO_SYNC);
                }else {
                    marketingStatLogPersistorManger.sendLeadData(arg.getEa(), leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_OTHER_SYNC);
                }

                //生成活动成员
                if (StringUtils.isNotEmpty(leadId)) {
                    String unitId = arg.getUnitId() == null ? null : String.valueOf(arg.getUnitId());
                    baiduAdMarketingManager.syncCampaignMember(arg.getEa(), leadId, arg.getAdSource(), arg.getPlanName(), arg.getKeyword(), false, unitId, null, arg.getKeywordId(), null);
                    // 绑定线索和营销用户
                    String userMarketingId = associateUserMarketing(arg, leadId);
                    // 绑定落地页和线索、营销用户的关联 并且触发广告回传
                    if (StringUtils.isNotBlank(landingObjId)) {
                        adOCPCUploadManager.bindEnrollDataLandingObj(arg.getEa(), landingObjId, leadId, null, null, landingUrl);
                    }
                    // 创建营销动态
                    tryCreateUserMarketingActionRecord(arg, marketingEventId, landingObjId, userMarketingId, arg.getKeyword());
                    sendDataToShenCe(arg.getEa(), leadId, marketingEventId, arg.getKeyword(), arg.getAdSource());
                }
            } catch (Exception e) {
                log.error("syncThirdLeadData.syncThirdLeadData error arg:{} e", arg, e);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(arg, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, e.getMessage() == null ? I18nUtil.get(I18nKeyEnum.MARK_RESULT_SHERRORCODE_8) : e.getMessage());
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
        return Result.newSuccess();
    }

    private boolean isRepeatAndDiffPush(AdLeadsEntity entity, AdThirdSyncDataArg arg) {
        String syncArg = entity.getSyncArg();
        if (StringUtils.isBlank(syncArg)) {
            return false;
        }
        AdThirdSyncDataArg lastArg = JsonUtil.fromJson(syncArg, AdThirdSyncDataArg.class);
        // 检查本次推送的参数和上次的推送的参数是否一致，一致的话直接返回，不一致查询上次推送是否失败，如果失败，则重新执行同步
        // 这是因为百度那边会短时间内推送两次，可能第一次的数据是不完全的，导致创建失败
        if (!arg.isDiff(lastArg) ) {
            return false;
        }
        List<ObjectData> objectDataList = marketingLeadSyncRecordObjManager.queryByOutPlatformDataId(arg.getEa(), arg.getClueId(), Lists.newArrayList("_id", "sync_status"));
        if (CollectionUtils.isEmpty(objectDataList)) {
            return true;
        }
        return objectDataList.stream().noneMatch(e -> "success".equals(e.getString("sync_status")));
    }


    private String getMarketingEventId(AdThirdSyncDataArg arg) {
        String ea = arg.getEa();
        // 申沪律师事务所特殊逻辑
        String marketingEventId = adCommonManager.getShenhuMarketingEventId(arg);
        if (StringUtils.isNotBlank(marketingEventId)) {
            return marketingEventId;
        }
        if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(arg.getAdSource())) {
            marketingEventId = getOrCreateTencentMarketingEventId(arg);
        } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(arg.getAdSource())) {
            marketingEventId = getOrCreateHeadlineMarketingEventId(arg);
        }
        if (StringUtils.isBlank(marketingEventId)) {
            marketingEventId = adCommonManager.getMarketingEventIdByUtmCampaign(ea, arg.getPlanName());
        }
        return marketingEventId;
    }

    /**
     * 根据头条推送的数据获取对应的市场活动
     *   1. 如果传了广告组id(旧)或者广告id(新),优先使用id查询市场活动，
     *   2. 如果根据id查询不到市场活动，根据广告名字查询市场活动，查询不到市场活动则创建市场活动
     */
    public String getOrCreateHeadlineMarketingEventId(AdThirdSyncDataArg arg) {
        try {
            String marketingEventId = null;
            Object adIdObj = arg.getData().containsKey("ad_id") ? arg.getData().get("ad_id") : arg.getData().get("promotion_id");
            if (adIdObj != null) {
                BigDecimal bigDecimal = new BigDecimal(String.valueOf(adIdObj));
                Long adId = bigDecimal.longValue();
                HeadlinesAdEntity entity = headlinesAdDAO.queryByAdId(arg.getEa(), adId);
                if (entity == null) {
                    log.warn("syncThirdLeadData fail,headlinesAd not sync, arg:{}", arg);
                } else if (StringUtils.isEmpty(entity.getSubMarketingEventId())) {
                    marketingEventId = sycHeadlinesAdToSubMarketingEventObj(arg.getEa(), entity);
                } else {
                    marketingEventId = entity.getSubMarketingEventId();
                }
            }
            if (StringUtils.isNotBlank(marketingEventId)) {
                return marketingEventId;
            }
            String promotionName = arg.getData().getString("promotion_name");
            if (StringUtils.isBlank(marketingEventId) && StringUtils.isNotBlank(promotionName)) {
                marketingEventId = adCommonManager.getMarketingEventIdByUtmCampaign(arg.getEa(), promotionName);
                if (StringUtils.isBlank(marketingEventId)) {
                    AdObjectFieldMappingEntity marketingEventObjMappingEntity = adObjectFieldMappingDAO.getByApiName(arg.getEa(), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
                    com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = refreshDataManager.createMarketingEventObjByCampaign(arg.getEa(), null, promotionName, arg.getAdSource(), marketingEventObjMappingEntity, null, null, null);
                    log.info("headline  create marketingEvent, promotionName {} result:{}", promotionName, addResult);
                    if (addResult != null && addResult.isSuccess() && addResult.getData() != null && addResult.getData().getObjectData() != null) {
                        marketingEventId = addResult.getData().getObjectData().getId();
                    }
                }
            }
            return marketingEventId;
        } catch (Exception e) {
            log.error("getHeadlineMarketingEventId arg: {}", arg, e);
        }
        return null;

    }

    private void transferToPrivateCloud(AdThirdSyncDataArg arg) {
        String ea = arg.getEa();
        String callBackURL = centerHost + "/marketing/adThridCallBack/inner/recvData";
        Map<String, String> header = new HashMap<>();
        header.put("x-fs-ei", String.valueOf(eieaConverter.enterpriseAccountToId(ea)));
        // 不需要检查转发到其他云
        arg.setCheckForwardToOtherCloud(false);
        String json = GsonUtil.toJson(arg);
        RequestBody requestBody = RequestBody.create(null, json);
        log.info("跳转专属云, ea:[{}], request:[{}]", ea, json);
        Result<String> result = httpManager.executePostByOkHttpClientWithRequestBodyAndHeader(requestBody, callBackURL, new TypeToken<Result<String>>() {}, header);
        log.info("跳转专属云, ea:[{}], result:[{}]", ea, result);
    }

    private void sendDataToShenCe(String ea, String leadId, String marketingEventId, String keyword, String adSource) {
        MarketingAdShenCeArg marketingAdShenCeArg = new MarketingAdShenCeArg();
        marketingAdShenCeArg.setEa(ea);
        marketingAdShenCeArg.setMarketingEventId(marketingEventId);
        marketingAdShenCeArg.setLeadId(leadId);
        marketingAdShenCeArg.setSource(adSource);
        marketingAdShenCeArg.setKeyword(keyword);
        String adAccountId = adCommonManager.getAdAccountIdByMarketingEventId(ea, marketingEventId);
        if (StringUtils.isNotBlank(adAccountId)) {
            AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
            marketingAdShenCeArg.setAdAccountId(adAccountId);
            marketingAdShenCeArg.setAdAccountUserName(adAccountEntity == null ? null : adAccountEntity.getUsername());
        }
        DataPersistor.asyncLog(MarketingAdShenCeArg.LOG_NAME, marketingAdShenCeArg.transferToMap());
    }

    /**
     * 根据腾讯推送的数据获取对应的市场活动
     *   1. 如果传了广告组id,优先使用广告组id查询市场活动，
     *   2. 如果根据广告组id查询不到市场活动，根据广告组名字查询市场活动，查询不到市场活动则创建市场活动
     *   3. 如果只传了广告计划名字(即父级市场活动),不创建市场活动，如果同时传了广告组名字和广告计划名字,创建市场活动的时候要关联父子级
     */
    public String getOrCreateTencentMarketingEventId(AdThirdSyncDataArg arg) {
        try {
            String ea = arg.getEa();
            String adGroupIdStr = arg.getData().getString("adgroup_id");
            String subMarketingEventId = null;
            if (StringUtils.isNotBlank(adGroupIdStr)) {
                long adGroupId = Long.parseLong(adGroupIdStr);
                List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryByAdGroupIdList(arg.getEa(), Lists.newArrayList(adGroupId));
                if (CollectionUtils.isNotEmpty(tencentAdGroupEntityList)) {
                    TencentAdGroupEntity entity = tencentAdGroupEntityList.get(0);
                    subMarketingEventId = entity.getSubMarketingEventId();
                    if (StringUtils.isBlank(subMarketingEventId)) {
                        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(entity.getAdAccountId());
                        List<AdvertiserAdEntity> advertiserAdEntityList = TencentAdMarketingManager.transferAdvertiserAdEntityList(tencentAdGroupEntityList);
                        refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, advertiserAdEntityList, AdSourceEnum.SOURCE_TENCETN.getSource());
                        tencentAdGroupEntityList = tencentAdGroupDAO.queryByAdGroupIdList(arg.getEa(), Lists.newArrayList(adGroupId));
                        subMarketingEventId = tencentAdGroupEntityList.get(0).getSubMarketingEventId();
                    }
                }
            }
            if (StringUtils.isNotBlank(subMarketingEventId)) {
                return subMarketingEventId;
            }
            String adGroupName = arg.getData().getString("adgroup_name");
            if (StringUtils.isBlank(adGroupName)) {
                return null;
            }
            subMarketingEventId = adCommonManager.getMarketingEventIdByUtmCampaign(ea, adGroupName);
            if (StringUtils.isNotBlank(subMarketingEventId)) {
                return subMarketingEventId;
            }
            String campaignName = arg.getData().getString("campaign_name");
            String parentMarketingEventId = null;
            AdObjectFieldMappingEntity marketingEventObjMappingEntity = adObjectFieldMappingDAO.getByApiName(arg.getEa(), CrmObjectApiNameEnum.MARKETING_EVENT.getName());
            if (StringUtils.isNotBlank(campaignName)) {
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = refreshDataManager.createMarketingEventObjByCampaign(arg.getEa(), null, campaignName, arg.getAdSource(), marketingEventObjMappingEntity, null, null, null);
                log.info("tencent  create marketingEvent, campaignName {} result:{}", campaignName, addResult);
                if (addResult != null && addResult.isSuccess() && addResult.getData() != null && addResult.getData().getObjectData() != null) {
                    parentMarketingEventId = addResult.getData().getObjectData().getId();
                }
            }
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResult = refreshDataManager.createMarketingEventObjByCampaign(arg.getEa(), null, adGroupName, arg.getAdSource(), marketingEventObjMappingEntity, parentMarketingEventId, null, null);
            log.info("tencent create sub marketingEvent, adGroupName {} result:{}", adGroupName, addResult);
            if (addResult != null && addResult.isSuccess() && addResult.getData() != null && addResult.getData().getObjectData() != null) {
                subMarketingEventId = addResult.getData().getObjectData().getId();
            }
            return subMarketingEventId;
        } catch (Exception e) {
            log.error("getTencentMarketingEventId error, arg: {}", arg, e);
        }
        return null;
    }

    private void refillAdSource(AdThirdSyncDataArg arg) {
        String adSource = arg.getAdSource();
        if (StringUtils.isBlank(adSource)) {
            return;
        }
        if (adSource.equalsIgnoreCase("baidu")) {
            arg.setAdSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        } else if (adSource.equalsIgnoreCase("toutiao")) {
            arg.setAdSource(AdSourceEnum.SOURCE_JULIANG.getSource());
        } else if (adSource.equalsIgnoreCase("tencent")) {
            arg.setAdSource(AdSourceEnum.SOURCE_TENCETN.getSource());
        }
    }

    private void tryCreateUserMarketingActionRecord(AdThirdSyncDataArg arg, String marketingEventId, String landingObjId, String userMarketingId, String keyword) {
        if (StringUtils.isBlank(userMarketingId)) {
            return;
        }
        // 这里要创建三条营销动态：如果带了关键词  那么创建两条营销动态  搜索关键词、点击关键词，还有一条浏览广告落地页动态
        // 营销动态是倒叙 所有创建顺序是  浏览广告落地页 点击关键词 搜索关键词
        try {
            MarketingUserActionEvent recordArg = new MarketingUserActionEvent();
            recordArg.setEa(arg.getEa());
            recordArg.setChannelType(MarketingUserActionChannelType.AD.getChannelType());
            recordArg.setMarketingEventId(marketingEventId);
            Map<String, Object> extensionMap = new HashMap<>();
            extensionMap.put("userMarketingId", userMarketingId);
            extensionMap.put(RecordActionArg.SPREAD_CHANNEL_KEY, SpreadChannelEnum.AD.getCode());
            extensionMap.put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.AD.getCode());
            String adAccountId = adCommonManager.getAdAccountIdByMarketingEventId(arg.getEa(), marketingEventId);
            if (StringUtils.isNotBlank(adAccountId)) {
                AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(adAccountId);
                if (adAccountEntity != null) {
                    extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY, adAccountEntity.getId());
                    String channelAccountPlatform = ChannelAccountPlatformEnum.BAIDU_AD.getPlatform();
                    if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(adAccountEntity.getSource())) {
                        channelAccountPlatform = ChannelAccountPlatformEnum.TENCENT_AD.getPlatform();
                    } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(adAccountEntity.getSource())) {
                        channelAccountPlatform = ChannelAccountPlatformEnum.HEAD_LINE_AD.getPlatform();
                    }
                    extensionMap.put(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY, channelAccountPlatform);
                }
            }
            recordArg.setExtensionParams(extensionMap);
            // 这里会创建三条营销动态 顺序是：搜索关键词、点击关键词、访问广告落地页  所以这里的创建时间要往下传 要不然大概率时间一直  顺序会有问题
            long now = System.currentTimeMillis();
            if (StringUtils.isNotBlank(landingObjId)) {
                // 访问广告落地页
                recordArg.setObjectType(ObjectTypeEnum.LANDING_PAGE.getType());
                recordArg.setObjectId(landingObjId);
                recordArg.setActionType(MarketingUserActionType.LOOK_UP_AD_LANDING_PAGE.getActionType());
                recordArg.setChannelType(MarketingUserActionChannelType.AD.getChannelType());
                recordArg.setActionTime(System.currentTimeMillis());
                extensionMap.put("createTime", now);
                marketingRecordActionSender.send(recordArg);
            }

            Optional<String> marketingKeywordOpt = utmDataManger.utmCreateMarketingKeyword(arg.getEa(), keyword, true);
            if (marketingKeywordOpt.isPresent()) {
                String keywordId = marketingKeywordOpt.get();
                recordArg.setObjectType(ObjectTypeEnum.KEYWORD.getType());
                recordArg.setObjectId(keywordId);
                // 点击关键词
                recordArg.setActionType(MarketingUserActionType.CLICK_KEYWORD.getActionType());
                recordArg.setActionTime(System.currentTimeMillis());
                extensionMap.put("createTime", now + 1000);
                marketingRecordActionSender.send(recordArg);

                // 搜索关键词
                recordArg.setActionType(MarketingUserActionType.SEARCH_KEYWORD.getActionType());
                recordArg.setActionTime(System.currentTimeMillis());
                extensionMap.put("createTime", now + 2000);
                marketingRecordActionSender.send(recordArg);
            }
        } catch (Exception e) {
            log.error("tryCreateUserMarketingAction error,arg: {} landingObjId: {}, userMarketingId: {}", arg, landingObjId, userMarketingId, e);
        }
    }

    private void tryCreateOrUpdateMarketingLeadSyncRecordObj(AdThirdSyncDataArg arg, String leadId, String syncStatus, String remark) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg leadSyncRecordObjArg = buildCreateMarketingLeadSyncRecordObjArg(arg, leadId);
        leadSyncRecordObjArg.setSyncStatus(syncStatus);
        leadSyncRecordObjArg.setRemark(remark);
        leadSyncRecordObjArg.setLeadId(leadId);
        leadSyncRecordObjArg.setMarketingLeadSyncRecordObjId(arg.getMarketingLeadSyncRecordObjId());
        marketingLeadSyncRecordObjManager.tryCreateOrUpdateMarketingLeadSyncRecordObj(leadSyncRecordObjArg);
    }


    private CreateOrUpdateMarketingLeadSyncRecordObjArg buildCreateMarketingLeadSyncRecordObjArg(AdThirdSyncDataArg arg, String leadId) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg marketingLeadSyncRecordObjArg = new CreateOrUpdateMarketingLeadSyncRecordObjArg();
        marketingLeadSyncRecordObjArg.setEa(arg.getEa());
        marketingLeadSyncRecordObjArg.setSyncData(JsonUtil.toJson(arg));
        // 不知道咋配置的 还有配置成other的
        String adSource = arg.getAdSource();
        if (adSource == null || "other".equals(adSource)) {
            marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_888));
        } else if(AdSourceEnum.SOURCE_BAIDU.getSource().equals(adSource)) {
            marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_BAIDU_BAIDUCAMPAIGNSERVICEIMPL_907));
        } else if(AdSourceEnum.SOURCE_JULIANG.getSource().equals(adSource)) {
            marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_BAIDU_BAIDUCAMPAIGNSERVICEIMPL_909));
        } else if(AdSourceEnum.SOURCE_TENCETN.getSource().equals(adSource)) {
            marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_BAIDU_BAIDUCAMPAIGNSERVICEIMPL_911));
        }
        marketingLeadSyncRecordObjArg.setOutPlatformType(MarketingLeadSyncRecordObjManager.AD_PLATFORM);
        // 这里有的没有配置clueId 反而在data里配置了id
        if (StringUtils.isNotBlank(arg.getClueId())) {
            marketingLeadSyncRecordObjArg.setOutPlatformDataId(arg.getClueId());
        } else if (arg.getData().get("id") != null) {
            marketingLeadSyncRecordObjArg.setOutPlatformDataId(arg.getData().get("id").toString());
        }
        String mobile = arg.getData().getString("mobile");
        if (StringUtils.isBlank(mobile)) {
            mobile = arg.getData().getString("tel");
        }
        String name = arg.getData().getName();
        if (StringUtils.isNotBlank(mobile) && !PhoneNumberCheck.isPhoneLegal(mobile)) {
            mobile = null;
        }
        marketingLeadSyncRecordObjArg.setMobile(mobile);
        marketingLeadSyncRecordObjArg.setLeadName(name);
        marketingLeadSyncRecordObjArg.setLeadId(leadId);
        return marketingLeadSyncRecordObjArg;
    }

    private String createMarketingPromotionSource(AdThirdSyncDataArg arg, String marketingEventId, String landingUrl) {
        MarketingPromotionSourceArg marketingPromotionSourceArg = new MarketingPromotionSourceArg();
        marketingPromotionSourceArg.setEa(arg.getEa());
        marketingPromotionSourceArg.setLandingPageUrl(landingUrl);
        marketingPromotionSourceArg.setLandingPageName(arg.getPageName());
        marketingPromotionSourceArg.setUtmTerm(arg.getKeyword());
        if (StringUtils.isNotBlank(marketingEventId)) {
            marketingPromotionSourceArg.setMarketingEventId(marketingEventId);
        } else if (StringUtils.isNotBlank(arg.getPlanName())){
            com.fxiaoke.crmrestapi.common.data.Page<ObjectData> objectDataPage =  utmDataManger.queryMarketingEventByName(arg.getEa(), arg.getPlanName());
            if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())){
                marketingEventId = objectDataPage.getDataList().get(0).getId();
                marketingPromotionSourceArg.setMarketingEventId(marketingEventId);
            }
        }
        String adPlatformName = I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_888);
        String dataForm = MarketingPromotionDataFromEnum.OTHER.getDataSource();
        if (StringUtils.equals(arg.getAdSource(), AdSourceEnum.SOURCE_BAIDU.getSource())) {
            adPlatformName = AdSourceEnum.SOURCE_BAIDU.getSource();
            dataForm = MarketingPromotionDataFromEnum.BAIDU_AD.getDataSource();
        } else if(StringUtils.equals(arg.getAdSource(), AdSourceEnum.SOURCE_JULIANG.getSource())){
            adPlatformName = AdSourceEnum.SOURCE_JULIANG.getSource();
            dataForm = MarketingPromotionDataFromEnum.HEADLINE_AD.getDataSource();
        } else if(StringUtils.equals(arg.getAdSource(), AdSourceEnum.SOURCE_TENCETN.getSource())) {
            adPlatformName = AdSourceEnum.SOURCE_TENCETN.getSource();
            dataForm = MarketingPromotionDataFromEnum.TENCENT_AD.getDataSource();
        }
        marketingPromotionSourceArg.setThirdPlatformName(adPlatformName);
        marketingPromotionSourceArg.setThirdPlatformDataId(arg.getClueId());
        if (arg.getData().containsKey("leadModule")) {
            marketingPromotionSourceArg.setLeadModule(arg.getData().getString("leadModule"));
        }
        marketingPromotionSourceArg.setDataFrom(dataForm);
        return marketingPromotionSourceObjManager.tryGetOrCreateObj(marketingPromotionSourceArg);
    }

    @Override
    public Result<Void> syncThirdLeadDataToMq(AdThirdSyncDataArg arg) {
        log.info("syncThirdLeadDataToMq data:[{}]", JSON.toJSONString(arg));
        //这里是从本地表判断的,专属云的企业到了纷享云的企业会一直返回true
//        if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(arg.getEa())) {
//            log.warn("syncThirdLeadDataToMq ea is stop or license expire ea :{}",arg.getEa());
//            return Result.newSuccess();
//        }
//        requestBufferSender.send(arg.getEa(), arg, RequestBufferTagConstants.THIRD_AD_DATA);
        advertiseCallbackMessageSender.send(arg.getEa(), arg);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> syncTencentLeadDataToMq(ObjectData objectData, String signature, String requestId) {
        log.info("syncTencentLeadDataToMq data:[{}],signature:[{}],requestId:[{}]", objectData, signature, requestId);
        if (objectData == null || MapUtils.isEmpty(objectData)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        if (StringUtils.isBlank(signature)) {
            return Result.newError(SHErrorCode.DO_NOT_HAVE_SIGNATURE);
        }

        String ea = objectData.getString("ea");
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        objectData.remove("ea");

        ThirdPlatformSecretEntity thirdPlatformSecretEntity = thirdPlatformSecretManager.getByEaAndPlatform(ea, ThirdPlatformEnum.TENCENT.getPlatform());
        if(thirdPlatformSecretEntity == null) {
            log.warn("syncTencentLeadDataToMq thirdPlatformSecretEntity is not exist, ea: {}", ea);
            return Result.newError(SHErrorCode.DO_NOT_CONFIG_SECRET);
        }
        String decodeSignature = new String(Base64.getDecoder().decode(signature), StandardCharsets.UTF_8);
        log.info("syncTencentLeadDataToMq signature: [{}] decodeSignature: [{}]", signature, decodeSignature);
        String[] decodeArray = decodeSignature.split(",");
        if (decodeArray.length != 3) {
            log.warn("syncTencentLeadDataToMq signature is error, ea: {} decodeSignature: {}", ea, decodeSignature);
            return Result.newError(SHErrorCode.SIGNATURE_LENGTH_ERROR);
        }
        String token = decodeArray[0];
        if (!thirdPlatformSecretEntity.getToken().equals(token)) {
            log.error("syncTencentLeadDataToMq signature is error, ea: {} decodeSignature: {}", ea, decodeSignature);
            return Result.newError(SHErrorCode.SIGNATURE_NOT_EQUEAL);
        }
        long timeStamp = Long.parseLong(decodeArray[1]);
        long now = System.currentTimeMillis() / 1000;
        if (now - timeStamp > 60 * 5) {
            log.error("syncTencentLeadDataToMq signature is expire, ea: {} now: {} decodeSignature: {}", ea, now, decodeSignature);
            return Result.newError(SHErrorCode.REQUEST_EXPIRE);
        }
        AdThirdSyncDataArg arg = new AdThirdSyncDataArg();
        arg.setAdSource(AdSourceEnum.SOURCE_TENCETN.getSource());
        arg.setEa(ea);
        String landingUrl = objectData.getString("page_url");
        if (StringUtils.isNotBlank(landingUrl)) {
            arg.setLandingUrl(landingUrl);
            objectData.remove("page_url");
        } else {
            landingUrl = objectData.getString("landingUrl");
            arg.setLandingUrl(landingUrl);
            objectData.remove("landingUrl");
        }
        String pageName = objectData.getString("page_name");
        if (StringUtils.isNotBlank(pageName)) {
            arg.setPageName(pageName);
            objectData.remove("page_name");
        } else {
            pageName = objectData.getString("pageName");
            arg.setPageName(pageName);
            objectData.remove("pageName");
        }

        String mobile = objectData.getString("leads_tel");
        if (StringUtils.isNotBlank(mobile)) {
            objectData.put("mobile", mobile);
            objectData.put("tel", mobile);
            objectData.remove("leads_tel");
        }
        String leadId = objectData.getString("leads_id");
        if (StringUtils.isNotBlank(leadId)) {
            arg.setClueId(leadId);
            objectData.remove("leads_id");
        } else {
            leadId = objectData.getString("clueId");
            arg.setClueId(leadId);
            objectData.remove("clueId");
        }
        String funApiName = objectData.getString("funApiName");
        if (StringUtils.isNotBlank(funApiName)) {
            arg.setFunApiName(funApiName);
            objectData.remove("funApiName");
        }
        String email = objectData.getString("leads_email");
        if (StringUtils.isNotBlank(email)) {
            objectData.put("email", email);
            objectData.remove("leads_email");
        }
        String name = objectData.getString("leads_name");
        if (StringUtils.isNotBlank(name)) {
            objectData.put("name", name);
            objectData.remove("leads_name");
        }
        // 表单字段
        Object bundle = objectData.get("bundle");
        if (bundle != null && StringUtils.isNotBlank(bundle.toString())) {
            try {
                ObjectData formData = GsonUtil.fromJson(bundle.toString(), ObjectData.class);
                objectData.putAll(formData);
            } catch (Exception e) {
                log.error("处理表单字段异常，ea: {} data: {}", ea, objectData, e);
            }
        }
        arg.setData(objectData);
        syncThirdLeadDataToMq(arg);
        return Result.newSuccess();
    }

    private String associateUserMarketing(AdThirdSyncDataArg arg, String leadId) {
        try {
            String ea = arg.getEa();
            AssociationArg associationArg = new AssociationArg();
            associationArg.setType(ChannelEnum.CRM_LEAD.getType());
            associationArg.setEa(ea);
            Object mobile = arg.getData().get("mobile");
            associationArg.setPhone(mobile == null ? null : mobile.toString());
            associationArg.setAssociationId(leadId);
            associationArg.setTriggerAction("syncThirdLeadData");
            associationArg.setTriggerSource(ChannelEnum.CRM_LEAD.getDescription());
            AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
            if (associationResult == null || StringUtils.isEmpty(associationResult.getUserMarketingAccountId())){
                return null;
            }
            log.info("绑定线索和营销用户,ea:[{}],leadId:[{}],result:[{}]", ea, leadId, associationResult);
            return associationResult.getUserMarketingAccountId();
        } catch (Exception e) {
            log.error("绑定线索和营销用户出错,ea:[{}], leadId:[{}]", arg.getEa(), leadId, e);
        }
        return null;
    }


    private String sycHeadlinesAdToSubMarketingEventObj(String ea, HeadlinesAdEntity entity) {
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(entity.getAdAccountId());

        // 头条广告计划的父级市场活动为空 需要先同步父级市场活动
        if (StringUtils.isEmpty(entity.getMarketingEventId())) {
            HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignDAO.queryCampaignByCampaignId(ea, entity.getCampaignId());
            refreshDataManager.batchSyncCampaignToMarketingEventObj(ea, adAccountEntity, Lists.newArrayList(headlinesCampaignEntity), adAccountEntity.getSource());
        }

        // 需要重新查一次拿到刚同步的父级市场活动id
        HeadlinesAdEntity headlinesAdEntity = headlinesAdDAO.queryAdById(entity.getId());
        refreshDataManager.batchSyncAdGroupToMarketingEventObj(ea, adAccountEntity, Lists.newArrayList(headlinesAdEntity), adAccountEntity.getSource());
        HeadlinesAdEntity AdEntity = headlinesAdDAO.queryAdById(headlinesAdEntity.getId());
        return AdEntity.getSubMarketingEventId();
    }


    //构建活动成员对象数据
    public Map<String, Object> buildCampaignMemberObjData(String ea, int bindCrmObjectType, String bindCrmObjectId, String marketingEventId) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), bindCrmObjectId);
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), marketingEventId);

        return dataMap;
    }

    protected HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        if (null == tenantId) {
            throw new CrmBusinessException(-1000, "enterpriseAccountToId failed, ea=" + ea);
        }

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

    @Override
    public Result<PageResult<BaiduAdGroupVO>> queryAdGroupList(QueryAdGroupListArg arg) {
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<BaiduAdGroupVO> pageResult = new PageResult<>();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTotalCount(0);
        List<BaiduAdGroupVO> resultList = Lists.newArrayList();
        pageResult.setResult(resultList);

        Page<BaiduAdGroupBO> page = new Page<>(arg.getPageNum(), arg.getPageSize(), true);
        List<BaiduAdGroupBO> adGroupBOList = adGroupDAO.pageAdGroup(arg, page);
        if (CollectionUtils.isEmpty(adGroupBOList)) {
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        String ea = arg.getEa();
        List<Long> adGroupIdList = Lists.newArrayList();
        List<String> marketingEventIdList = Lists.newArrayList();
        adGroupBOList.forEach(adGroupBO -> {
            adGroupIdList.add(adGroupBO.getAdGroupId());
            marketingEventIdList.add(adGroupBO.getMarketingEventId());
        });

        List<BaiduAdGroupBO> adGroupStatisticDataList = baiduAdGroupDataDAO.statisticsDataByAdGroupIdAndTime(ea, arg.getAdAccountId(), adGroupIdList, arg.getStartTime(), arg.getEndTime());
        Map<Long, BaiduAdGroupBO> adGroupIdToStatisticsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(adGroupStatisticDataList)) {
            adGroupStatisticDataList.forEach(adGroupBO -> adGroupIdToStatisticsMap.put(adGroupBO.getAdGroupId(), adGroupBO));
        }
        Map<String, Integer> marketingEventIdToLeadCountMap = marketingEventManager.getMarketingEventIdToLeadCountMap(ea, marketingEventIdList, arg.getStartTime().getTime(), arg.getEndTime().getTime());
        for (BaiduAdGroupBO baiduAdGroupBO : adGroupBOList) {
            BaiduAdGroupVO adGroupVO = BeanUtil.copy(baiduAdGroupBO, BaiduAdGroupVO.class);
            BaiduAdGroupBO statisticData = adGroupIdToStatisticsMap.get(adGroupVO.getAdGroupId());
            fillAdGroupVOResult(adGroupVO, statisticData, marketingEventIdToLeadCountMap.get(adGroupVO.getMarketingEventId()));
            resultList.add(adGroupVO);
        }
        return Result.newSuccess(pageResult);
    }

    private static void fillAdGroupVOResult(BaiduAdGroupVO adGroupVO, BaiduAdGroupBO statisticData, Integer leadCount) {
        if (statisticData != null) {
            adGroupVO.setPv(statisticData.getPv());
            adGroupVO.setClick(statisticData.getClick());
            adGroupVO.setCost(statisticData.getCost());
        }
        BigDecimal clickPrice = BigDecimal.ZERO;
        if (adGroupVO.getClick() != null && adGroupVO.getClick() != 0 && adGroupVO.getCost() != null) {
            clickPrice = adGroupVO.getCost().divide(BigDecimal.valueOf(adGroupVO.getClick()), 2, RoundingMode.HALF_UP);
        }
        adGroupVO.setClickPrice(clickPrice);
        adGroupVO.setLeadCount(leadCount == null ? 0 : leadCount);
    }

    @Override
    public Result<BaiduAdGroupVO> queryAdGroupDetail(QueryAdGroupDetailArg arg) {
        if (StringUtils.isBlank(arg.getId()) || arg.getStartTime() == null || arg.getEndTime() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = arg.getEa();
        AdGroupEntity adGroupEntity = adGroupDAO.getById(ea, arg.getId());
        if (adGroupEntity == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<BaiduAdGroupBO> adGroupStatisticDataList = baiduAdGroupDataDAO.statisticsDataByAdGroupIdAndTime(ea, adGroupEntity.getAdAccountId(), Lists.newArrayList(adGroupEntity.getAdGroupId()), arg.getStartTime(), arg.getEndTime());
        Integer leadCount = null;
        if (StringUtils.isNotBlank(adGroupEntity.getMarketingEventId())) {
            Map<String, Integer> marketingEventIdToLeadCountMap = marketingEventManager.getMarketingEventIdToLeadCountMap(ea, Lists.newArrayList(adGroupEntity.getMarketingEventId()), arg.getStartTime().getTime(), arg.getEndTime().getTime());
            leadCount = marketingEventIdToLeadCountMap.get(adGroupEntity.getMarketingEventId());
        }
        BaiduAdGroupBO statisticData = CollectionUtils.isEmpty(adGroupStatisticDataList) ? null : adGroupStatisticDataList.get(0);
        BaiduAdGroupVO baiduAdGroupVO = new BaiduAdGroupVO();
        fillAdGroupVOResult(baiduAdGroupVO, statisticData, leadCount);
        baiduAdGroupVO.setAdGroupName(adGroupEntity.getAdGroupName());
        BaiduCampaignEntity baiduCampaignEntity = baiduCampaignDAO.queryCampaignByCampaignId(ea, adGroupEntity.getAdAccountId(),  adGroupEntity.getCampaignId());
        if (baiduCampaignEntity != null) {
            baiduAdGroupVO.setCampaignName(baiduCampaignEntity.getCampaignName());
        }
        return Result.newSuccess(baiduAdGroupVO);
    }
}