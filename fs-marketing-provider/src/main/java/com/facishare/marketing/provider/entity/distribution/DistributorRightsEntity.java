package com.facishare.marketing.provider.entity.distribution;

import com.facishare.marketing.common.enums.distribution.DistributorRightsTypeEnum;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/5/27.
 */
@Data
@Entity
public class DistributorRightsEntity  implements Serializable {
    private String id;

    /**
     * 权益类型
     * {@link DistributorRightsTypeEnum}
     * **/
    private Integer type;

    /**
     * 分销等级
     * **/
    private Integer grade;

    private Float value1;

    private Float value2;

    private String remark;

    private Date createTime;

    private Date updateTime;
}
