package com.facishare.marketing.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.arg.hexagon.HexagonStatisticArg;
import com.facishare.marketing.api.arg.kis.ListSpreadRadarMarketingActivityArg;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.data.material.HexagonSiteBriefData;
import com.facishare.marketing.api.result.GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult;
import com.facishare.marketing.api.result.MaterialTagResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.contentmaketing.MarketingContentVO;
import com.facishare.marketing.api.result.contentmaketing.SetContentMobileDisplayArg;
import com.facishare.marketing.api.result.hexagon.SitePreviewResult;
import com.facishare.marketing.api.result.kis.CreateWXQRCodeByFeedResult;
import com.facishare.marketing.api.result.kis.TempListAllMarketingActivityResult;
import com.facishare.marketing.api.service.ContentMarketingEventService;
import com.facishare.marketing.api.service.EnterpriseSpreadStatisticService;
import com.facishare.marketing.api.service.kis.SpreadWorkService;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.qr.QRCodeTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dto.CustomizeFormUserDataCountByObjectIdDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.qrpost.PosterTargetDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteObjectEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.innerArg.CreateQRCodeInnerData;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.qr.QRCodeManager;
import com.facishare.marketing.statistic.outapi.result.CommonStatisticResult;
import com.facishare.marketing.statistic.outapi.service.ContentMarketingEventStatisticService;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("contentMarketingEventService")
public class ContentMarketingEventServiceImpl implements ContentMarketingEventService {
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseSpreadStatisticService enterpriseSpreadStatisticService;
    @Autowired
    private MarketingActivityObjectRelationDAO marketingActivityObjectRelationDAO;
    @Autowired
    private ContentMarketingEventStatisticService contentMarketingEventStatisticService;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private QRCodeManager qrCodeManager;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private MaterialTagManager materialTagManager;
    @Autowired
    private SpreadWorkService spreadWorkService;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private MarketingEventCommonSettingManager marketingEventCommonSettingManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private HexagonSiteObjectDAO hexagonSiteObjectDAO;
    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private ActivityManager activityManager;
    @Autowired
    private ConferenceDAO conferenceDAO;

    @Autowired
    private ConferenceManager conferenceManager;

    @Override
    public Result<PageResult<MarketingEventsBriefResult>> listContentMarketingEvent(String ea, Integer fsUserId, ListContentMarketingEventArg arg) {
        List<String> eventTypeList = Lists.newArrayList();
        if (StringUtils.isBlank(arg.getEventType())) {
            eventTypeList = marketingEventCommonSettingManager.getBindActivityTypeMappingByType(ea, MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType(), true);
        } else {
            eventTypeList.add(arg.getEventType());
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        PageArg pageArg = new PageArg(arg.getPageNo(), arg.getPageSize());
        ListBriefMarketingEventsArg listBriefMarketingEventsArg = new ListBriefMarketingEventsArg();
        listBriefMarketingEventsArg.setBizStatus(arg.getBizStatus());
        listBriefMarketingEventsArg.setName(arg.getName());
        //listBriefMarketingEventsArg.setEventType(arg.getEventType());
        listBriefMarketingEventsArg.setEventTypeList(eventTypeList);
        listBriefMarketingEventsArg.setFilterData(arg.getFilterData());
        listBriefMarketingEventsArg.setOrderByList(Lists.newArrayList(new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false)
                ,new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.BEGIN_TIME, false)));
        PageResult<MarketingEventsBriefResult> marketingEventsBriefResultPageResult;
        if (arg.getMaterialTagFilter() != null && arg.getMaterialTagFilter().checkValid()) {
            // 内容标签过滤处理
            Integer type = arg.getMaterialTagFilter().getType();
            List<String> materialTagIds = arg.getMaterialTagFilter().getMaterialTagIds();
            Page page = new Page(arg.getPageNo(), arg.getPageSize(), true);
            List<String> ids;
            if (type == 1) {
                ids = materialTagRelationDao.queryByAnyTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.ACTIVITY.getType()), page);
            } else {
                ids = materialTagRelationDao.queryByAllTags(ea, materialTagIds, Lists.newArrayList(ObjectTypeEnum.ACTIVITY.getType()), page);
            }
            if (CollectionUtils.isEmpty(ids)) {
                return Result.newSuccess(new PageResult<>(0, null));
            }
            ListBriefMarketingEventsArg batchGetMktEventArg = new ListBriefMarketingEventsArg();
            batchGetMktEventArg.setOrderByList(Lists.newArrayList(new ListBriefMarketingEventsArg.OrderBy(MarketingEventFieldContants.CREATE_TIME, false)));
            batchGetMktEventArg.setMarketingEventIds(ids);
            List<MarketingEventsBriefResult> marketingEventsBriefResults = marketingEventManager.listMarketingEventsV2(ei, fsUserId, batchGetMktEventArg);
            marketingEventsBriefResultPageResult = new PageResult<>();
            marketingEventsBriefResultPageResult.setTotalCount(page.getTotalNum());
            marketingEventsBriefResultPageResult.setData(marketingEventsBriefResults);
        } else {
            marketingEventsBriefResultPageResult = marketingEventManager.listMarketingEvents(ei, fsUserId, listBriefMarketingEventsArg, pageArg);
        }
        List<String> marketingEventIds = marketingEventsBriefResultPageResult.getData().stream().map(MarketingEventsBriefResult::getId).collect(Collectors.toList());
        final Map<String, Integer> pvResultMap = marketingEventManager.calMarketingEventsPV(ea, marketingEventIds);
        Map<String, Integer> campaignCountMap = campaignMergeDataManager.getCampaignMergeCountByMarketingIds(ea, marketingEventIds);
        // 批量查询子活动梳理
        Map<String, Long> subCountMap = marketingEventManager.batchGetSubTotalByParentIds(ea, fsUserId, marketingEventIds);
        // 查询标签
        Map<String, List<String>> materialTagMap = materialTagManager.buildTagName(marketingEventIds, ObjectTypeEnum.ACTIVITY.getType());
        ExecutorService executorService = Executors.newFixedThreadPool(4);
        CountDownLatch countDownLatch = new CountDownLatch(marketingEventsBriefResultPageResult.getData().size());
        marketingEventsBriefResultPageResult.getData().forEach(marketingEventsBriefResult -> {
            executorService.submit(()->{
                try {
                    marketingEventsBriefResult.setPv(pvResultMap.get(marketingEventsBriefResult.getId()));
                    marketingEventsBriefResult.setUv(marketingEventManager.calMarketingEventUVStatistic(ea, marketingEventsBriefResult.getId()));
                    //marketingEventsBriefResult.setLeadNum(marketingEventManager.calMarketingEventLeadNum(marketingEventsBriefResult.getId()));
                    Integer campaignMergeDataCount = campaignCountMap.get(marketingEventsBriefResult.getId());
                    marketingEventsBriefResult.setLeadNum(campaignMergeDataCount != null ? campaignMergeDataCount : 0);
                    Set<Integer> objectTypes = Sets.newHashSet(ObjectTypeEnum.PRODUCT.getType(), ObjectTypeEnum.ARTICLE.getType(), ObjectTypeEnum.HEXAGON_SITE.getType(), ObjectTypeEnum.CUSTOMIZE_FORM.getType());
                    marketingEventsBriefResult.setContentNum(marketingEventManager.calMarketingEventContentNum(ea, marketingEventsBriefResult.getId(), objectTypes));
                    String eventType = marketingEventsBriefResult.getEventType();
                    if (Objects.equals(eventType, MarketingEventEnum.MULTIVENUE_MARKETING.getEventType())) {
                        if (subCountMap.get(marketingEventsBriefResult.getId()) != null) {
                            marketingEventsBriefResult.setRelateSubCount(subCountMap.get(marketingEventsBriefResult.getId()).intValue());
                        } else {
                            marketingEventsBriefResult.setRelateSubCount(0);
                        }
                    } else {
                        marketingEventsBriefResult.setRelateSubCount(0);
                    }
                } catch (Exception e) {
                    log.warn("ContentMarketingEventService listContentMarketingEvent error ea:{} arg:{}", ea, arg);
                } finally {
                    countDownLatch.countDown();
                }
            });
            // 内容标签处理
            List<String> materialTags = materialTagMap.get(marketingEventsBriefResult.getId());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(materialTags)) {
                List<MaterialTagResult> collect = materialTags.stream().map(materialTag -> {
                    MaterialTagResult materialTagResult = new MaterialTagResult();
                    materialTagResult.setName(materialTag);
                    return materialTagResult;
                }).collect(Collectors.toList());
                marketingEventsBriefResult.setMaterialTags(collect);
            }
        });
        try {
            countDownLatch.await(5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("ContentMarketingEventService listContentMarketingEvent timeout ea:{} arg:{}", ea, arg);
        }
        executorService.shutdown();
        return Result.newSuccess(marketingEventsBriefResultPageResult);
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>> listMarketingContent(String ea, Integer fsUserId, String marketingEventId, List<Integer> objectTypes ,Integer pageNo, Integer pageSize) {
        return this.listMarketingContent(ea, fsUserId, marketingEventId, objectTypes, pageNo, pageSize, false);
    }


    @Override
    public Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>> listMarketingContent(String ea, Integer fsUserId, String marketingEventId, List<Integer> objectTypes ,Integer pageNo, Integer pageSize, boolean needCheckMobileDisplay) {
        com.facishare.marketing.common.result.PageResult<AbstractMaterialData> abstractMaterialDataPageResult = marketingEventManager.getMaterials(ea, objectTypes, marketingEventId, pageNo, pageSize, needCheckMobileDisplay);
        if (abstractMaterialDataPageResult == null || CollectionUtils.isEmpty(abstractMaterialDataPageResult.getResult())){
            com.facishare.marketing.common.result.PageResult<MarketingContentVO> pageResult = new com.facishare.marketing.common.result.PageResult<MarketingContentVO>();
            pageResult.setResult(Lists.newArrayList());
            pageResult.setTotalCount(0);
            pageResult.setPageSize(pageSize);
            pageResult.setPageNum(pageNo);
            return Result.newSuccess(pageResult);
        }

        // 目睹子活动，不返回推广内容
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
        if (marketingLiveEntity != null && Objects.equals(marketingLiveEntity.getPlatform(), LivePlatformEnum.MUDU.getType()) && Objects.equals(marketingLiveEntity.getSubEvent(), 1)) {
            return Result.newSuccess();
        }

        List<String> objectIds = abstractMaterialDataPageResult.getResult().stream().map(AbstractMaterialData::getId).collect(Collectors.toList());
        com.facishare.marketing.statistic.common.result.Result<Map<String, CommonStatisticResult>> statisticMapResult = contentMarketingEventStatisticService.listContentMarketingEventObjectsStatistic(ea, marketingEventId, objectIds);
        List<MarketingContentVO> marketingContentVOS = buildMarketingContentData(ea, marketingEventId, abstractMaterialDataPageResult.getResult());
        for (MarketingContentVO vo : marketingContentVOS){
            if(Lists.newArrayList(4,6,16,26).contains(vo.getMaterial().getObjectType())){
                // 获取裁剪封面图
                String photoTargetId = vo.getMaterial().getId();
                if(vo.getMaterial().getObjectType()==26){
                    HexagonPageEntity homePage = hexagonPageDAO.getHomePage(vo.getMaterial().getId());
                    photoTargetId = homePage.getId();
                }
                List<PhotoTargetTypeEnum> PhotoTargetTypeList = PhotoTargetTypeEnum.getByTypeByObjectType(vo.getMaterial().getObjectType());
                PhotoEntity coverCutMiniAppPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(0).getType(), photoTargetId);
                if (coverCutMiniAppPhotoEntity != null) {
                    vo.getMaterial().setSharePicMiniAppCutUrl(coverCutMiniAppPhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(1).getType(), photoTargetId);
                if (coverCutH5PhotoEntity != null) {
                    vo.getMaterial().setSharePicH5CutUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                }
                PhotoEntity coverCutOrdinaryPhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeList.get(2).getType(), photoTargetId);
                if (coverCutOrdinaryPhotoEntity != null) {
                    vo.getMaterial().setSharePicOrdinaryCutUrl(coverCutOrdinaryPhotoEntity.getThumbnailUrl());
                    //返回原图
                    vo.getMaterial().setSharePicOrdinaryUrl(coverCutOrdinaryPhotoEntity.getUrl());
                }
            }
            if (statisticMapResult.getData() != null) {
                CommonStatisticResult commonStatisticResult = statisticMapResult.getData().get(vo.getMaterial().getId());
                if (commonStatisticResult != null) {
                    vo.setPv(commonStatisticResult.getPv());
                    vo.setUv(commonStatisticResult.getUv());
                }
            }
        }
        /*
        List<MarketingContentVO> marketingContentVOS = abstractMaterialDataPageResult.getResult().stream().map(val -> {
            MarketingContentVO vo = new MarketingContentVO();
            vo.setMaterial(val);
            int posterCount = qrPosterDAO.countByMarketingEventIdAndObjectId(ea, marketingEventId, val.getId());
            vo.setPosterNum(posterCount);
            String leadObjectId = val.getId();
            if(val.getObjectType() != null && ObjectTypeEnum.HEXAGON_SITE.getType() == val.getObjectType()){
                List<HexagonSiteListDTO> hexagonSiteList = hexagonSiteDAO.getFormBySiteIds(ImmutableList.of(val.getId()));
                if (hexagonSiteList != null && hexagonSiteList.size() > 0 && !Strings.isNullOrEmpty(hexagonSiteList.get(0).getHexagonPageId())) {
                    leadObjectId = hexagonSiteList.get(0).getHexagonPageId();
                }
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(val.getId());
                if (hexagonSiteEntity != null && hexagonSiteEntity.isSystemSite()){
                    HexagonSiteBriefData hexagonSiteBriefData = (HexagonSiteBriefData)vo.getMaterial();
                    hexagonSiteBriefData.setSystem(true);
                }
            }
            int leadNum = customizeFormDataUserDAO.countLeadByMarketingEventIdAndObjectId(marketingEventId, leadObjectId);
            vo.setLeadNum(leadNum);
            if (statisticMapResult.getData() != null) {
                CommonStatisticResult commonStatisticResult = statisticMapResult.getData().get(val.getId());
                if (commonStatisticResult != null) {
                    vo.setPv(commonStatisticResult.getPv());
                    vo.setUv(commonStatisticResult.getUv());
                }
            }
            return vo;
        }).collect(Collectors.toList());
        */

       // marketingContentVOS = marketingContentVOS.stream().filter(o->!(o.getMaterial().getObjectType()==26&&hexagonSiteDAO.getById(o.getMaterial().getId()).isSystemSite()&&hexagonSiteDAO.getById(o.getMaterial().getId()).getName().equals("视频号中转页面"))).collect(Collectors.toList());
        com.facishare.marketing.common.result.PageResult<MarketingContentVO> pageResult = com.facishare.marketing.common.result.PageResult.newPageResult(abstractMaterialDataPageResult.getPageNum(), abstractMaterialDataPageResult.getPageSize(), abstractMaterialDataPageResult.getTotalCount(), marketingContentVOS);
        return Result.newSuccess(pageResult);
    }

    private List<MarketingContentVO> buildMarketingContentData(String ea, String marketingEventId, List<AbstractMaterialData> materialDataList){
        if (CollectionUtils.isEmpty(materialDataList)){
            return Lists.newArrayList();
        }
        //查询物料关联的海报数量
        List<String> materialIds = materialDataList.stream().map(AbstractMaterialData::getId).collect(Collectors.toList());
        List<PosterTargetDTO> posterTargetDTOList = qrPosterDAO.getByMarketingEvenIdAndTargetIds(ea, marketingEventId, materialIds);
        Map<String, Integer> posterTargetCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(posterTargetDTOList)){
            for (PosterTargetDTO dto : posterTargetDTOList){
                if (posterTargetCountMap.get(dto.getTargetId()) == null){
                    posterTargetCountMap.put(dto.getTargetId(), 1);
                }else {
                    posterTargetCountMap.put(dto.getTargetId(), posterTargetCountMap.get(dto.getTargetId()) + 1);
                }
            }
        }

        //处理物料关联的系统微页面
        List<String> hexagonObjects = Lists.newArrayList();
        for (AbstractMaterialData materialData : materialDataList){
            if(materialData.getObjectType() != null && ObjectTypeEnum.HEXAGON_SITE.getType() == materialData.getObjectType()){
                hexagonObjects.add(materialData.getId());
            }
        }
        Map<String, HexagonSiteEntity> hexagonSiteEntityMap = null;
        if (CollectionUtils.isNotEmpty(hexagonObjects)){
            List<HexagonSiteEntity> hexagonSiteEntityList = hexagonSiteDAO.getByIds(hexagonObjects);
            if (CollectionUtils.isNotEmpty(hexagonSiteEntityList)){
                hexagonSiteEntityMap = hexagonSiteEntityList.stream().collect(Collectors.toMap(HexagonSiteEntity::getId, Function.identity(), (v1, v2)->v2));
            }
        }

        //物料关联的线索
        Map<String, Integer> clueMap = getLeadNumByObject(marketingEventId, materialDataList);
        //查询物料关联的微页面
        List<MarketingContentVO> marketingContentVOS = Lists.newArrayList();
        for (AbstractMaterialData materialData : materialDataList){
            MarketingContentVO marketingContentVO = new MarketingContentVO();
            marketingContentVO.setMaterial(materialData);
            marketingContentVO.setPosterNum(posterTargetCountMap.get(materialData.getId()) == null ? 0 : posterTargetCountMap.get(materialData.getId()));
            marketingContentVO.setLeadNum(clueMap.get(materialData.getId()));
            if (hexagonSiteEntityMap != null && hexagonSiteEntityMap.get(materialData.getId()) != null && hexagonSiteEntityMap.get(materialData.getId()).isSystemSite()){
                HexagonSiteBriefData hexagonSiteBriefData = (HexagonSiteBriefData)marketingContentVO.getMaterial();
                hexagonSiteBriefData.setSystem(true);
            }
            marketingContentVO.setIsMobileDisplay(materialData.getIsMobileDisplay());
            marketingContentVOS.add(marketingContentVO);
        }

        return marketingContentVOS;
    }

    private Map<String, Integer> getLeadNumByObject(String marketingEventId, List<AbstractMaterialData> materialDataList){
        List<String> hexagonObjects = Lists.newArrayList();
        for (AbstractMaterialData materialData : materialDataList){
            if(materialData.getObjectType() != null && ObjectTypeEnum.HEXAGON_SITE.getType() == materialData.getObjectType()){
                hexagonObjects.add(materialData.getId());
            }
        }

        List<String> leadObjectIds = Lists.newArrayList();
        Map<String, HexagonSiteListDTO> hexagonPageLeadObjectMap = new HashMap<>();
        Map<String, HexagonSiteListDTO> hexagonLeadObjectMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hexagonObjects)){
            List<HexagonSiteListDTO> hexagonSiteList = hexagonSiteDAO.getFormBySiteIds(hexagonObjects);
            if (CollectionUtils.isNotEmpty(hexagonSiteList)){
                for (HexagonSiteListDTO dto : hexagonSiteList){
                    hexagonPageLeadObjectMap.put(dto.getHexagonPageId(), dto);
                    hexagonLeadObjectMap.put(dto.getHexagonSiteId(), dto);
                }
            }
        }
        for (AbstractMaterialData materialData : materialDataList){
            if (hexagonLeadObjectMap.get(materialData.getId()) != null){
                leadObjectIds.add(hexagonLeadObjectMap.get(materialData.getId()).getHexagonPageId());
            }
            leadObjectIds.add(materialData.getId());
        }
        List<CustomizeFormUserDataCountByObjectIdDTO>  objectFormUserDataList = customizeFormDataUserDAO.batchCountByMarketingEventIdAndObjectIds(marketingEventId, leadObjectIds);
        List<CustomizeFormUserDataCountByObjectIdDTO>  parentObjectFormUserDataList = customizeFormDataUserDAO.batchCountByMarketingEventIdAndParentObjectIds(marketingEventId, leadObjectIds);
        Map<String, Integer> objectFormUserDataCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectFormUserDataList)){
            for (CustomizeFormUserDataCountByObjectIdDTO dto : objectFormUserDataList){
                if (hexagonPageLeadObjectMap.get(dto.getObjectId()) != null){
                    objectFormUserDataCountMap.put(hexagonPageLeadObjectMap.get(dto.getObjectId()).getHexagonSiteId(), dto.getCount());
                }else {
                    objectFormUserDataCountMap.merge(dto.getObjectId(), dto.getCount(), Integer::sum);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(parentObjectFormUserDataList)){
            for (CustomizeFormUserDataCountByObjectIdDTO dto : objectFormUserDataList){
                if (hexagonPageLeadObjectMap.get(dto.getObjectId()) != null){
                    objectFormUserDataCountMap.put(hexagonPageLeadObjectMap.get(dto.getObjectId()).getHexagonSiteId(), dto.getCount());
                }else {
                    objectFormUserDataCountMap.put(dto.getObjectId(), dto.getCount());
                }
            }
        }

        Map<String, Integer> leadCountMap = new HashMap<>();
        for (AbstractMaterialData materialData : materialDataList){
            int objectLeadNum = objectFormUserDataCountMap.get(materialData.getId()) == null ? 0 : objectFormUserDataCountMap.get(materialData.getId());
            leadCountMap.put(materialData.getId(), objectLeadNum);

        }
        return leadCountMap;
    }

    @Override
    public Result<com.facishare.marketing.common.result.PageResult<TempListAllMarketingActivityResult>> listMarketingActivityInContentMarketingEvent(String ea, Integer fsUserId, ListMarketingActivityInContentMarketingEventArg arg) {
        ListSpreadRadarMarketingActivityArg listSpreadRadarMarketingActivityArg = new ListSpreadRadarMarketingActivityArg();
        if (arg.getObjectType() !=null && StringUtils.isNotEmpty(arg.getObjectId())) {
            List<String> marketingActivityIds = marketingActivityObjectRelationDAO.listMarketingActivityIdsByMarketingEventIdAndObjectTypeAndObjectId(ea, arg.getMarketingEventId(), arg.getObjectType(), arg.getObjectId());
            if (CollectionUtils.isEmpty(marketingActivityIds)){
                return Result.newSuccess(com.facishare.marketing.common.result.PageResult.newPageResult(arg.getPageNum(),arg.getPageSize(),0,Lists.newArrayList()));
            }
            listSpreadRadarMarketingActivityArg.setMarketingActivityIds(marketingActivityIds);
        }
        listSpreadRadarMarketingActivityArg.setMarketingEventId(arg.getMarketingEventId());
        listSpreadRadarMarketingActivityArg.setPageNum(arg.getPageNum());
        listSpreadRadarMarketingActivityArg.setPageSize(arg.getPageSize());
        Result<com.facishare.marketing.common.result.PageResult<TempListAllMarketingActivityResult>> result = enterpriseSpreadStatisticService.listMarketingActivity(ea, fsUserId, listSpreadRadarMarketingActivityArg);
        return result;
    }

    @Override
    public Result<GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult> getOrCreateQrCodeByMarketingEventIdAndObjectInfo(String ea, Integer fsUserId, GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg arg) {
        CreateQRCodeInnerData data = new CreateQRCodeInnerData();
        data.setType(arg.getH5QrCodeType());
        data.setValue(arg.getH5QrCodeValue());
        data.setLengthOfSide(arg.getLengthOfSide());
        data.setEa(ea);
        data.setH5Path(arg.getH5Path());
        //兼容优惠券生成码
        if (ObjectTypeEnum.WX_COUPON.getType() == arg.getObjectType()) {
            QRCodeManager.CreateQRCodeResult createQRCodeResult = qrCodeManager.createQRCode(data);
            String h5QrCodeAPath = createQRCodeResult.getQrCodeApath();
            GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult result = new GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult();
            result.setH5QrCodeAPath(h5QrCodeAPath);
            return Result.newSuccess(result);
        }

        ContentMarketingEventMaterialRelationEntity entity = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(ea, arg.getMarketingEventId(), arg.getObjectType(), arg.getObjectId());
        String baiduQrCodeAPath = entity.getBaiduQrCodeAPath();
        QRCodeManager.CreateQRCodeResult createQRCodeResult = qrCodeManager.createQRCode(data);
        String h5QrCodeAPath = createQRCodeResult.getQrCodeApath();

        // 现小程序可实时更换二维码需重绘
        // 此处不需要推广人
        String miniappQrCodeAPath = null;
        Result<CreateWXQRCodeByFeedResult> createWXQRCodeByFeedResultResult = spreadWorkService
                    .createWXQRCodeByFeed(ea, fsUserId, ObjectTypeEnum.getByType(arg.getObjectType()), arg.getObjectId(), arg.getFeedKey(), arg.getMarketingActivityId(), false, arg.getMiniappQrCodeValue(), false);
        miniappQrCodeAPath = createWXQRCodeByFeedResultResult.getData().getQrCodeAPath();

        if (StringUtils.isEmpty(baiduQrCodeAPath) && arg.getObjectType() == ObjectTypeEnum.HEXAGON_SITE.getType()) {
            Result<SitePreviewResult> sitePreviewResultResult = hexagonManager.getHexagonSiteQRCodeURL(arg.getObjectId(), ea, null, null);
            if (sitePreviewResultResult.isSuccess()) {
                baiduQrCodeAPath = sitePreviewResultResult.getData().getBdQRAPath();
            }
        }
        contentMarketingEventMaterialRelationDAO.updateQrCodeAPath(entity.getId(), h5QrCodeAPath, miniappQrCodeAPath, baiduQrCodeAPath);
        GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult result = new GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult();
        result.setH5QrCodeAPath(h5QrCodeAPath);
        result.setMiniappQrCodeAPath(miniappQrCodeAPath);
        result.setBaiduQrCodeAPath(baiduQrCodeAPath);
        return Result.newSuccess(result);
    }

    @Override
    public Result<MarketingContentVO> getHexagonContent(String ea, Integer fsUserId, HexagonStatisticArg arg) {
        if(StringUtils.isBlank(arg.getSiteId())){
            ActivityEntity activity = conferenceDAO.getConferenceByMarketingEventId(arg.getMarketingEvenId(), ea);
            //表里有数据返回系统繁忙,没修复数据
            if(StringUtils.isNotBlank(activity.getActivityDetailSiteId())){
                return Result.newError(SHErrorCode.SERVER_BUSY);
            }
            conferenceManager.createConferenceSite(activity.getId(),null);
            arg.setSiteId(conferenceDAO.getConferenceByMarketingEventId(arg.getMarketingEvenId(), ea).getActivityDetailSiteId());
        }
        ContentMarketingEventMaterialRelationEntity relation = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(ea, arg.getMarketingEvenId(), ObjectTypeEnum.HEXAGON_SITE.getType(), arg.getSiteId());
        ArrayList<ContentMarketingEventMaterialRelationEntity> contentMarketingEventMaterialRelationEntities = Lists.newArrayList(relation);
        List<AbstractMaterialData> list =marketingEventManager.getContent(ea, arg, contentMarketingEventMaterialRelationEntities);
        Optional<ActivityEntity> entity = activityManager.getActivityEntityByMarketingEventId(ea, arg.getMarketingEvenId());
        com.facishare.marketing.statistic.common.result.Result<Map<String, CommonStatisticResult>> statisticMapResult = contentMarketingEventStatisticService.listContentMarketingEventObjectsStatistic(ea, arg.getMarketingEvenId(),Lists.newArrayList(entity.get().getId()));
        List<MarketingContentVO> marketingContentVOS = buildMarketingContentData(ea, arg.getMarketingEvenId(), list);
        for (MarketingContentVO vo : marketingContentVOS){
            if (statisticMapResult.getData() != null) {
                CommonStatisticResult commonStatisticResult = statisticMapResult.getData().get(entity.get().getId());
                if (commonStatisticResult != null) {
                    vo.setPv(commonStatisticResult.getPv());
                    vo.setUv(commonStatisticResult.getUv());
                }
            }
        }
        //加上会议的获得的线索数
        List<CustomizeFormUserDataCountByObjectIdDTO>  objectFormUserDataList = customizeFormDataUserDAO.batchCountByMarketingEventIdAndObjectIds(arg.getMarketingEvenId(), Lists.newArrayList(entity.get().getId()));
        if(CollectionUtils.isNotEmpty(objectFormUserDataList)){
            marketingContentVOS.get(0).setLeadNum(marketingContentVOS.get(0).getLeadNum()+objectFormUserDataList.get(0).getCount());
        }
        return Result.newSuccess(marketingContentVOS.get(0));
    }

    @Override
    public Result<Void> setContentMobileDisplay(String ea, Integer fsUserId, SetContentMobileDisplayArg arg) {
        if (arg.isWrongParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getObjectType().equals(ObjectTypeEnum.QR_POSTER.getType())) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(arg.getObjectId());
            qrPosterEntity.setIsMobileDisplay(arg.getIsMobileDisplay());
            qrPosterDAO.update(qrPosterEntity);
            return Result.newSuccess();
        }
        ContentMarketingEventMaterialRelationEntity relation = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(ea, arg.getMarketingEventId(), arg.getObjectType(), arg.getObjectId());
        if (relation == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        relation.setIsMobileDisplay(arg.getIsMobileDisplay());
        contentMarketingEventMaterialRelationDAO.update(relation);
        return Result.newSuccess();
    }


}
