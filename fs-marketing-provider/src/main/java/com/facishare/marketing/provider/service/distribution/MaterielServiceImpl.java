package com.facishare.marketing.provider.service.distribution;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.vo.QueryArticleDetailVO;
import com.facishare.mankeep.common.util.UUIDUtil;
import com.facishare.marketing.api.arg.WindowListActivityArg;
import com.facishare.marketing.api.arg.WindowListArticleArg;
import com.facishare.marketing.api.arg.WindowListProductArg;
import com.facishare.marketing.api.arg.distribution.DeleteActivityArg;
import com.facishare.marketing.api.arg.distribution.DeleteArticleArg;
import com.facishare.marketing.api.arg.distribution.DeleteProductArg;
import com.facishare.marketing.api.arg.distribution.ListActivityArg;
import com.facishare.marketing.api.arg.distribution.ListArticleArg;
import com.facishare.marketing.api.arg.distribution.ListProductArg;
import com.facishare.marketing.api.arg.distribution.RelateActivityArg;
import com.facishare.marketing.api.arg.distribution.RelateArticleArg;
import com.facishare.marketing.api.arg.distribution.RelateProductArg;
import com.facishare.marketing.api.result.ActivityResult;
import com.facishare.marketing.api.result.ArticleListResult;
import com.facishare.marketing.api.result.EnterpriseObjectAmountStatisticResult;
import com.facishare.marketing.api.result.ProductListResult;
import com.facishare.marketing.api.result.WebActivityListUnitResult;
import com.facishare.marketing.api.result.distribution.DistributionMaterialInfoResult;
import com.facishare.marketing.api.result.distribution.ListActivityResult;
import com.facishare.marketing.api.result.distribution.ListArticleResult;
import com.facishare.marketing.api.result.distribution.ListProductResult;
import com.facishare.marketing.api.service.ActivityService;
import com.facishare.marketing.api.service.distribution.MaterielService;
import com.facishare.marketing.api.vo.QueryDistributionMaterialVO;
import com.facishare.marketing.common.enums.ArticleUserTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.ProductArticleTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.dao.ActivityDAO;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.dao.EnterpriseObjectAmountStatisticDao;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.distribution.DistributionMaterialDao;
import com.facishare.marketing.provider.dto.ActivityEntityDTO;
import com.facishare.marketing.provider.dto.ArticleEntityDTO;
import com.facishare.marketing.provider.dto.ProductEntityDTO;
import com.facishare.marketing.provider.entity.ActivityEntity;
import com.facishare.marketing.provider.entity.ArticleEntity;
import com.facishare.marketing.provider.entity.EnterpriseObjectAmountStatisticEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.ProductEntity;
import com.facishare.marketing.provider.entity.distribution.DistributionMaterial;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.PictureManager;
import com.facishare.marketing.provider.manager.distribution.MaterielManager;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("materielService")
@Slf4j
public class MaterielServiceImpl implements MaterielService {

	@Autowired
	private DistributionMaterialDao distributionMaterialDao;
	
	@Autowired
	private ActivityService activityService;
	
	@Autowired
	private ProductDAO productDAO;
	
	@Autowired
	private ArticleDAO articleDAO;
	
	@Autowired
	private ActivityDAO activityDAO;
	
	@Autowired
	private PhotoDAO photoDAO;
	
	@Autowired
	private PictureManager pictureManager;
	
	@Autowired
	private EnterpriseObjectAmountStatisticDao enterpriseObjectAmountStatisticDao;

	@Autowired
	private MaterielManager materielManager;

	@Autowired
	private PhotoManager photoManager;
	
	@Override
	public Result<PageResult<ListProductResult>> listProducts(String ea, Integer fsUserId, ListProductArg vo) {
		Integer pageSize = vo.getPageSize();
		Integer pageNum = vo.getPageNum();
		
		List<ListProductResult> productResults = new ArrayList<>();
		Page page = new Page(pageNum, pageSize, true);
		List<DistributionMaterial> distributionMaterialList = distributionMaterialDao.queryDistributionMaterialByEaAndType(ea, ObjectTypeEnum.PRODUCT.getType(), vo.getPlanId(), page);
		PageResult<ListProductResult> pageResult = new PageResult<>();
		pageResult.setResult(productResults);
		pageResult.setPageNum(pageNum);
		pageResult.setPageSize(pageSize);
		pageResult.setTotalCount(page.getTotalNum());

		if (CollectionUtils.isNotEmpty(distributionMaterialList)) {
			List<String> productIds = distributionMaterialList.stream().map(DistributionMaterial :: getObjectId).collect(Collectors.toList());

			List<PhotoEntity> photoEntities = photoDAO.listByProductIds(productIds);
			if (photoEntities == null) {
				photoEntities = Lists.newArrayList();
			}
			photoManager.resetPhotoListUrl(photoEntities, null);
			final Map<String, List<PhotoEntity>> photoGroup = photoEntities.stream().collect(Collectors.groupingBy(PhotoEntity :: getTargetId));

			List<ProductEntity> productEntities = productDAO.getByIds(productIds);
			if (CollectionUtils.isEmpty(productEntities)) {
				productEntities = Lists.newArrayList();
			}
			final Map<String, ProductEntity> productMap = productEntities.stream().collect(Collectors.toMap(ProductEntity :: getId, v -> v, (k1, k2) -> k1));
			distributionMaterialList.forEach(value -> {
				ProductEntity productEntity = productMap.get(value.getObjectId());
				if (productEntity != null) {
					ListProductResult productResult = BeanUtil.copy(productEntity, ListProductResult.class);
					productResult.setTryOutEnable(productEntity.getTryOutEnable().booleanValue());

					List<String> headPicsList = Lists.newArrayList();
					List<String> detailPicsList = Lists.newArrayList();
					List<String> headPicsThumbList = Lists.newArrayList();
					List<String> detailPicsThumbList = Lists.newArrayList();

					List<PhotoEntity> photoList = photoGroup.get(value.getObjectId());
					if (CollectionUtils.isNotEmpty(photoList)) {
						for (PhotoEntity photoEntity : photoList){
							if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
								headPicsList.add(photoEntity.getUrl());
								headPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
							}

							if (photoEntity.getTargetType() == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()){
								detailPicsList.add(photoEntity.getUrl());
								detailPicsThumbList.add(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
							}

							if (photoEntity.getTargetType() == PhotoTargetTypeEnum.MINI_COVER_PRODUCT_NORMAL.getType()){
								productResult.setCardPhotoUrl(photoEntity.getUrl());
							}
						}
					}
					productResult.setHeadPics(headPicsList);
					productResult.setDetailPics(detailPicsList);
					productResult.setHeadPicsThumbs(headPicsThumbList);
					productResult.setDetailPicsThumbs(detailPicsThumbList);
					productResults.add(productResult);
				}
			});
		}

		return new Result(SHErrorCode.SUCCESS, pageResult);
	}
	
	@Override
	public Result<ArticleListResult> windowListArticles(String ea, Integer fsUserId, WindowListArticleArg vo) {
		Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
		List<ArticleEntityDTO> articleEntities = articleDAO.windowListArticles(ea, vo.getPlanId(), page);
		
		ArticleListResult articleListResult = new ArticleListResult();
		articleListResult.setCurrentPage(page.getPageNo());
		articleListResult.setPageSize(page.getPageSize());
		articleListResult.setRecordSize(page.getTotalNum());
		articleListResult.setPageTotal(page.getTotalPage() / page.getPageSize() + 1);
		
		if (CollectionUtils.isEmpty(articleEntities)) {
			articleListResult.setArticleDetailResults(Lists.newArrayList());
			return new Result<>(SHErrorCode.SUCCESS, articleListResult);
		}
		
		//文章列表
		List<com.facishare.marketing.api.result.QueryArticleDetailResult> articleDetailResults = Lists.newArrayList();
		for (ArticleEntityDTO articleEntity : articleEntities) {
			com.facishare.marketing.api.result.QueryArticleDetailResult queryArticleDetailResult = BeanUtil.copy(articleEntity, com.facishare.marketing.api.result.QueryArticleDetailResult.class);
			queryArticleDetailResult.setPhotoUrl(pictureManager.checkArticleImg(queryArticleDetailResult.getPhotoUrl()));
			if (articleEntity.getPhotoThumbnailUrl() == null) {
				queryArticleDetailResult.setPhotoThumbnailUrl(queryArticleDetailResult.getPhotoUrl());
			} else {
				queryArticleDetailResult.setPhotoThumbnailUrl(articleEntity.getPhotoThumbnailUrl());
			}
			queryArticleDetailResult.setCreateTimeStamp(null == articleEntity.getCreateTime() ? null : articleEntity.getCreateTime().getTime());
			queryArticleDetailResult.setLastModifyTimeStamp(null == articleEntity.getLastModifyTime() ? null : articleEntity.getLastModifyTime().getTime());
			queryArticleDetailResult.setChoose(articleEntity.isChoose());
			
			if (null != articleEntity.getFsUserId() && StringUtils.isNotBlank(articleEntity.getFsEa())) {
				queryArticleDetailResult.setBelong(ArticleUserTypeEnum.COMPANY.getType());
			}
			articleDetailResults.add(queryArticleDetailResult);
		}
		articleListResult.setArticleDetailResults(articleDetailResults);
		return new Result<>(SHErrorCode.SUCCESS, articleListResult);
	}
	
	@Override
	public Result<PageResult<WebActivityListUnitResult>> windowListActivities(String ea, Integer fsUserId, WindowListActivityArg vo) {
		Integer pageSize = vo.getPageSize();
		Integer pageNum = vo.getPageNum();
		
		List<WebActivityListUnitResult> webActivityListUnitResults = new ArrayList<>();
		Page page = new Page(pageNum, pageSize, true);
		List<ActivityEntityDTO> activityEntityList = activityDAO.windowListActivities(ea, vo.getPlanId(), page);
		PageResult<WebActivityListUnitResult> pageResult = new PageResult<>();
		pageResult.setResult(webActivityListUnitResults);
		pageResult.setPageNum(pageNum);
		pageResult.setPageSize(pageSize);
		pageResult.setTotalCount(page.getTotalNum());
		if (activityEntityList.size() == 0) {
			return new Result<>(SHErrorCode.SUCCESS, pageResult);
		}
		List<String> objectIds = Lists.newArrayList();
		activityEntityList.forEach(value -> objectIds.add(value.getId()));
		List<EnterpriseObjectAmountStatisticResult> enterpriseObjectAmountStatisticResultList = getEnterpriseObjectAmountStatisticVOListByEaAndObjectTypeAndObjectIds(ea,
			ObjectTypeEnum.ACTIVITY.getType(), objectIds);
		activityEntityList.forEach(value -> {
			WebActivityListUnitResult webActivityListUnitResult = BeanUtil.copy(value, WebActivityListUnitResult.class);
			webActivityListUnitResult.setStartTime(value.getStartTime().getTime());
			webActivityListUnitResult.setEndTime(value.getEndTime().getTime());
			if (value.getEnrollEndTime() != null) {
				log.info("");
				webActivityListUnitResult.setEnrollEndTime(value.getEnrollEndTime().getTime());
			}
			webActivityListUnitResult.setState(judgeState(value.getStartTime(), value.getEndTime()));
			List<EnterpriseObjectAmountStatisticResult> enterpriseObjectAmountStatisticResultListTemp = enterpriseObjectAmountStatisticResultList.stream()
				.filter(enterpriseObjectAmountStatisticResult -> enterpriseObjectAmountStatisticResult.getObjectId().equals(value.getId())).collect(Collectors.toList());
			if (!enterpriseObjectAmountStatisticResultListTemp.isEmpty()) {
				webActivityListUnitResult.setLookUpCount(enterpriseObjectAmountStatisticResultListTemp.get(0).getLookUpCount());
				webActivityListUnitResult.setEmployeeForwardCount(enterpriseObjectAmountStatisticResultListTemp.get(0).getEmployeeForwardCount());
				webActivityListUnitResult.setForwardCount(enterpriseObjectAmountStatisticResultListTemp.get(0).getForwardCount());
			}
			webActivityListUnitResults.add(webActivityListUnitResult);
		});
		return new Result<>(SHErrorCode.SUCCESS, pageResult);
	}

	@Override
	public Result<PageResult<DistributionMaterialInfoResult>> queryDistributionMaterial(QueryDistributionMaterialVO vo) {
		return materielManager.queryDistributionMaterial(vo);
	}

	@Override
	public Result<ProductListResult> windowListProducts(String ea, Integer fsUserId, WindowListProductArg vo) {
		Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
		List<ProductEntityDTO> productEntities = productDAO.windowListProducts(ea, ProductArticleTypeEnum.CORPORATE.getType(), vo.getPlanId(), page);
		int totalCount = page.getTotalNum();
		ProductListResult productListResult = new ProductListResult();
		productListResult.setTotalCount(totalCount);
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(productEntities)) {
			productListResult.setProductDetailResultList(Lists.newArrayList());
			return new Result<>(SHErrorCode.SUCCESS, productListResult);
		}
		
		List<String> productIds = Lists.newArrayList();
		productEntities.forEach(productEntity -> productIds.add(productEntity.getId()));
		
		// photo and product mapping
		Map<String, List<String>> headPicsMap = new HashMap<>();
		Map<String, List<String>> detailPicsMap = new HashMap<>();
		Map<String, List<String>> headPicsThumbMap = new HashMap<>();
		Map<String, List<String>> detailPicsThumbMap = new HashMap<>();
		List<PhotoEntity> photoEntities = photoDAO.listByProductIds(productIds);
		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(photoEntities)) {
			photoEntities.forEach(photoEntity -> {
				String productId = photoEntity.getTargetId();
				int targetType = photoEntity.getTargetType();
				String url = photoEntity.getUrl();
				String thumbUrl = photoEntity.getThumbnailUrl();
				log.info("");
				if (targetType == PhotoTargetTypeEnum.PRODUCT_HEAD.getType()) {
					if (headPicsMap.containsKey(productId)) {
						headPicsMap.get(productId).add(url);
						headPicsThumbMap.get(productId).add(thumbUrl);
					} else {
						log.info("");
						headPicsMap.put(productId, Lists.newArrayList(url));
						headPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
					}
				} else if (targetType == PhotoTargetTypeEnum.PRODUCT_DETAIL.getType()) {
					if (detailPicsMap.containsKey(productId)) {
						log.info("");
						detailPicsMap.get(productId).add(url);
						detailPicsThumbMap.get(productId).add(thumbUrl);
					} else {
						detailPicsMap.put(productId, Lists.newArrayList(url));
						detailPicsThumbMap.put(productId, Lists.newArrayList(thumbUrl));
					}
				}
			});
		}
		
		// build result
		List<com.facishare.marketing.api.result.QueryProductDetailResult> productDetailResults = Lists.newArrayList();
		productEntities.forEach(productEntity -> {
			String productId = productEntity.getId();
			com.facishare.marketing.api.result.QueryProductDetailResult queryProductDetailResult = BeanUtil.copy(productEntity, com.facishare.marketing.api.result.QueryProductDetailResult.class);
			if (headPicsMap.containsKey(productId)) {
				queryProductDetailResult.setHeadPics(headPicsMap.get(productId));
			}
			if (detailPicsMap.containsKey(productId)) {
				queryProductDetailResult.setDetailPics(detailPicsMap.get(productId));
			}
			if (headPicsThumbMap.containsKey(productId)) {
				queryProductDetailResult.setHeadPicsThumbs(headPicsThumbMap.get(productId));
			}
			if (detailPicsThumbMap.containsKey(productId)) {
				queryProductDetailResult.setDetailPicsThumbs(detailPicsThumbMap.get(productId));
			}
			queryProductDetailResult.setChoose(productEntity.isChoose());
			queryProductDetailResult.setTryOutEnable(productEntity.getTryOutEnable());
			productDetailResults.add(queryProductDetailResult);
		});
		
		productListResult.setProductDetailResultList(productDetailResults);
		return new Result<>(SHErrorCode.SUCCESS, productListResult);
	}
	
	@Override
	public Result<PageResult<ListArticleResult>> listArticles(String ea, Integer fsUserId, ListArticleArg vo) {
		Integer pageSize = vo.getPageSize();
		Integer pageNum = vo.getPageNum();
		
		List<ListArticleResult> articleResults = new ArrayList<>();
		Page page = new Page(pageNum, pageSize, true);
		List<DistributionMaterial> distributionMaterialList = distributionMaterialDao.queryDistributionMaterialByEaAndType(ea, ObjectTypeEnum.ARTICLE.getType(), vo.getPlanId(), page);
		PageResult<ListArticleResult> pageResult = new PageResult<>();
		pageResult.setResult(articleResults);
		pageResult.setPageNum(pageNum);
		pageResult.setPageSize(pageSize);
		pageResult.setTotalCount(page.getTotalNum());

		if (CollectionUtils.isNotEmpty(distributionMaterialList)) {
			List<String> articleIds = distributionMaterialList.stream().map(DistributionMaterial :: getObjectId).collect(Collectors.toList());
			List<ArticleEntity> articleEntities = articleDAO.getByIds(articleIds);
			if (articleEntities == null) {
				articleEntities = Lists.newArrayList();
			}
			final Map<String, ArticleEntity> articleEntityMap = articleEntities.stream().collect(Collectors.toMap(ArticleEntity :: getId, v -> v, (k1, k2) -> k1));

			List<PhotoEntity> photoEntities = photoDAO.listByTargeIds(articleIds);
			if (photoEntities == null) {
				photoEntities = Lists.newArrayList();
			}
			photoManager.resetPhotoListUrl(photoEntities, null);
			final Map<String, List<PhotoEntity>> photoGroup = photoEntities.stream().collect(Collectors.groupingBy(PhotoEntity :: getTargetId));

			distributionMaterialList.forEach(value -> {
				QueryArticleDetailVO articleDetailVO = new QueryArticleDetailVO();
				articleDetailVO.setId(value.getObjectId());
				ArticleEntity articleEntity = articleEntityMap.get(value.getObjectId());
				if (articleEntity != null) {
					ListArticleResult articleResult = BeanUtil.copy(articleEntity, ListArticleResult.class);
					articleResult.setCreateTimeStamp(null == articleEntity.getCreateTime() ? null : articleEntity.getCreateTime().getTime());
					articleResult.setLastModifyTimeStamp(null == articleEntity.getLastModifyTime() ? null : articleEntity.getLastModifyTime().getTime());

					List<PhotoEntity> photoList = photoGroup.get(value.getObjectId());
					if (CollectionUtils.isNotEmpty(photoList)) {
						for (PhotoEntity photoEntity : photoList){
							if (photoEntity.getTargetType() == PhotoTargetTypeEnum.ARTICLE_COVER.getType()) {
								articleResult.setPhotoThumbnailUrl(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
								articleResult.setPhotoUrl(StringUtils.isNotEmpty(photoEntity.getUrl()) ? photoEntity.getUrl() : photoEntity.getThumbnailUrl());
							}

							if (photoEntity.getTargetType() == PhotoTargetTypeEnum.MINI_COVER_ARTICLE_NORMAL.getType()){
								articleResult.setCardPhotoUrl(StringUtils.isNotEmpty(photoEntity.getThumbnailUrl()) ? photoEntity.getThumbnailUrl() : photoEntity.getUrl());
							}
						}
					}

					articleResults.add(articleResult);
				}
			});
		}

		return new Result(SHErrorCode.SUCCESS, pageResult);
	}
	
	@Override
	public Result<PageResult<ListActivityResult>> listActivities(String ea, Integer fsUserId, ListActivityArg vo) {
		Integer pageSize = vo.getPageSize();
		Integer pageNum = vo.getPageNum();
		
		List<ListActivityResult> activityResults = new ArrayList<>();
		Page page = new Page(pageNum, pageSize, true);
		List<DistributionMaterial> distributionMaterialList = distributionMaterialDao.queryDistributionMaterialByEaAndType(ea, ObjectTypeEnum.ACTIVITY.getType(), vo.getPlanId(), page);
		PageResult<ListActivityResult> pageResult = new PageResult<>();
		pageResult.setResult(activityResults);
		pageResult.setPageNum(pageNum);
		pageResult.setPageSize(pageSize);
		pageResult.setTotalCount(page.getTotalNum());
		distributionMaterialList.forEach(value -> {
			Result<ActivityResult> result = activityService.getById(ea, fsUserId, value.getObjectId());
			ActivityResult activityResult = result.getData();
			if (activityResult != null) {
				ListActivityResult listActivityResult = BeanUtil.copy(activityResult, ListActivityResult.class);
				activityResults.add(listActivityResult);
			}
		});
		return new Result(SHErrorCode.SUCCESS, pageResult);
	}
	
	@Override
	public Result relateProduct(String ea, Integer fsUserId, RelateProductArg vo) {
		List<String> productIds = vo.getIds();
		List<ProductEntity> productEntities = Lists.newArrayList();
		productIds.forEach(value -> {
			ProductEntity productEntity = productDAO.queryProductDetail(value);
			if (null != productEntity) {
				productEntities.add(productEntity);
			}
		});
		
		if (CollectionUtils.isEmpty(productEntities)) {
			return new Result(SHErrorCode.PRODUCT_DETAIL_FAIL);
		}
		
		List<DistributionMaterial> distributionMaterialList = new ArrayList<>();
		for (ProductEntity productEntity : productEntities) {
			DistributionMaterial existDistributionMaterial = distributionMaterialDao.queryDistributionMaterial(productEntity.getId(), vo.getPlanId(), fsUserId);
			if (null != existDistributionMaterial) {
				continue;
			}
			DistributionMaterial distributionMaterial = new DistributionMaterial();
			distributionMaterial.setId(UUIDUtil.getUUID());
			distributionMaterial.setObjectId(productEntity.getId());
			distributionMaterial.setFsEa(productEntity.getFsEa());
			distributionMaterial.setObjectType(4);
			distributionMaterial.setCreateTime(new Date());
			distributionMaterial.setUpdateTime(new Date());
			distributionMaterial.setPlanId(vo.getPlanId());
			distributionMaterialList.add(distributionMaterial);
		}
		
		distributionMaterialDao.batchInsert(distributionMaterialList);
		return new Result(SHErrorCode.SUCCESS);
	}
	
	@Override
	public Result relateArticle(String ea, Integer fsUserId, RelateArticleArg vo) {
		List<String> articleIds = vo.getIds();
		List<ArticleEntity> articleEntities = Lists.newArrayList();
		articleIds.forEach(value -> {
			ArticleEntity articleEntity = articleDAO.getById(value);
			if (null != articleEntity) {
				articleEntities.add(articleEntity);
			}
		});
		
		if (CollectionUtils.isEmpty(articleEntities)) {
			return new Result(SHErrorCode.ARTICLE_DETAIL_FAIL);
		}
		
		List<DistributionMaterial> distributionMaterialList = new ArrayList<>();
		for (ArticleEntity articleEntity : articleEntities) {
			DistributionMaterial existDistributionMaterial = distributionMaterialDao.queryDistributionMaterial(articleEntity.getId(), vo.getPlanId(), fsUserId);
			if (null != existDistributionMaterial) {
				continue;
			}
			DistributionMaterial distributionMaterial = new DistributionMaterial();
			distributionMaterial.setId(UUIDUtil.getUUID());
			distributionMaterial.setObjectId(articleEntity.getId());
			distributionMaterial.setFsEa(articleEntity.getFsEa());
			distributionMaterial.setObjectType(6);
			distributionMaterial.setCreateTime(new Date());
			distributionMaterial.setUpdateTime(new Date());
			distributionMaterial.setPlanId(vo.getPlanId());
			distributionMaterialList.add(distributionMaterial);
		}
		
		distributionMaterialDao.batchInsert(distributionMaterialList);
		return new Result(SHErrorCode.SUCCESS);
	}
	
	@Override
	public Result relateActivity(String ea, Integer fsUserId, RelateActivityArg vo) {
		List<String> activityIds = vo.getIds();
		List<ActivityEntity> activityEntities = Lists.newArrayList();
		activityIds.forEach(value -> {
			ActivityEntity activityEntity = activityDAO.getById(value);
			if (null != activityEntity) {
				activityEntities.add(activityEntity);
			}
		});
		
		if (CollectionUtils.isEmpty(activityEntities)) {
			return new Result(SHErrorCode.ACTIVITY_DETAIL_FAIL);
		}
		
		List<DistributionMaterial> distributionMaterialList = new ArrayList<>();
		for (ActivityEntity activityEntity : activityEntities) {
			DistributionMaterial existDistributionMaterial = distributionMaterialDao.queryDistributionMaterial(activityEntity.getId(), vo.getPlanId(), fsUserId);
			if (null != existDistributionMaterial) {
				continue;
			}
			DistributionMaterial distributionMaterial = new DistributionMaterial();
			distributionMaterial.setId(UUIDUtil.getUUID());
			distributionMaterial.setObjectId(activityEntity.getId());
			distributionMaterial.setFsEa(ea);
			distributionMaterial.setObjectType(13);
			distributionMaterial.setCreateTime(new Date());
			distributionMaterial.setUpdateTime(new Date());
			distributionMaterial.setPlanId(vo.getPlanId());
			distributionMaterialList.add(distributionMaterial);
		}
		
		distributionMaterialDao.batchInsert(distributionMaterialList);
		return new Result(SHErrorCode.SUCCESS);
	}
	
	@Override
	public Result dissociateProduct(String ea, Integer fsUserId, DeleteProductArg vo) {
		distributionMaterialDao.dissociateRelation(vo.getObjectId(), vo.getPlanId(), fsUserId);
		return new Result(SHErrorCode.SUCCESS);
	}
	
	@Override
	public Result dissociateArticle(String ea, Integer fsUserId, DeleteArticleArg vo) {
		distributionMaterialDao.dissociateRelation(vo.getObjectId(), vo.getPlanId(), fsUserId);
		return new Result(SHErrorCode.SUCCESS);
	}
	
	@Override
	public Result dissociateActivity(String ea, Integer fsUserId, DeleteActivityArg vo) {
		distributionMaterialDao.dissociateRelation(vo.getObjectId(), vo.getPlanId(), fsUserId);
		return new Result(SHErrorCode.SUCCESS);
	}
	
	private Integer judgeState(Date startTime, Date endTime) {
		Date now = DateUtil.now();
		if (startTime != null && now.getTime() < startTime.getTime()) {
			log.info("");
			return 2;
		} else if (startTime != null && endTime != null && now.getTime() >= startTime.getTime() && now.getTime() <= endTime.getTime()) {
			return 3;
		} else if (endTime != null && now.getTime() > endTime.getTime()) {
			log.info("");
			return 4;
		} else {
			return 0;
		}
	}
	
	public List<EnterpriseObjectAmountStatisticResult> getEnterpriseObjectAmountStatisticVOListByEaAndObjectTypeAndObjectIds(String ea, Integer objectType, List<String> objectIds) {
		List<EnterpriseObjectAmountStatisticEntity> enterpriseObjectAmountStatisticEntityList = enterpriseObjectAmountStatisticDao.getByEaAndObjectTypeAndObjectIds(ea, objectType, objectIds);
		List<EnterpriseObjectAmountStatisticResult> enterpriseObjectAmountStatisticResultList = BeanUtil.copy(enterpriseObjectAmountStatisticEntityList, EnterpriseObjectAmountStatisticResult.class);
		return enterpriseObjectAmountStatisticResultList;
	}


}
