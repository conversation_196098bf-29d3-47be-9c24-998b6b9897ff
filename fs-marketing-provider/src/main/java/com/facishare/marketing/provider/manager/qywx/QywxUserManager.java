package com.facishare.marketing.provider.manager.qywx;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.RequestBufferTagConstants;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.WxAppInfoEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.UserCardOpenStatusEnum;
import com.facishare.marketing.common.enums.user.UserTypeEnum;
import com.facishare.marketing.common.mq.sender.RequestBufferSender;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeStatisticDAO;
import com.facishare.marketing.provider.dao.kis.SpreadTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeStatisticEntity;
import com.facishare.marketing.provider.entity.kis.SpreadTaskEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.innerArg.UpdateWechatGroupObjOwnerMqArg;
import com.facishare.marketing.provider.innerArg.qywx.UseridToOpenuseridArg;
import com.facishare.marketing.provider.innerResult.qywx.*;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult.StaffInfo;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.crmobjectcreator.WechatGroupUserObjDescribeManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.fxiaoke.crmrestapi.arg.ActionChangeOwnerArg;
import com.fxiaoke.crmrestapi.arg.GetTeamMemberArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionChangeOwnerResult;
import com.fxiaoke.crmrestapi.result.GetTeamMemberResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created  By zhoux 2020/04/07
 **/
@Component
@Slf4j
public class QywxUserManager {

    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;

    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private FsBindManager fsBindManager;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private CardManager cardManager;

    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;

    @Autowired
    private AccountDAO accountDAO;

    @Autowired
    private UserManager userManager;

    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private VirtualUserManager virtualUserManager;
    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private SpreadTaskDAO spreadTaskDAO;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private MarketingActivityEmployeeStatisticDAO marketingActivityEmployeeStatisticDAO;
    @Autowired
    private MarketingActivityDayStatisticDAO marketingActivityDayStatisticDAO;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private HttpManager httpManager;

    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Autowired
    private RequestBufferSender requestBufferSender;

    @Autowired
    private WechatGroupUserObjDescribeManager wechatGroupUserObjDescribeManager;

    @Value("${qywx.crm.appid}")
    private String qywxCrmAppid;

    @ReloadableProperty("qywx.encode.enterprise.account")
    private String qywxEncodeEnterpriseAccount;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private QywxActivatedAccountManager qywxActivatedAccountManager;

    @Autowired
    private QywxDepartmentManager qywxDepartmentManager;

    private List<String> notRefreshQywxStaffEaList = Lists.newArrayList("sbtjt888");
    /**
     * 获取企业员工对应的虚拟userId (若存在纷享userId则返回纷享userId)
     *
     * @param ea 纷享ea
     * @param corpId 企业微信corpId
     * @param qyUserId 企业微信userId
     * @param tryTime 尝试次数
     */
    public Integer getUserIdByQyWxInfo(String ea, String corpId, String qyUserId, int tryTime) {
        if (tryTime <= 0) {
            log.warn("QywxUserManager.getVirtualUserIdByQyWxInfo reTryTime limit");
            return null;
        }
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(corpId) || StringUtils.isBlank(qyUserId)) {
            return null;
        }
        // 查询虚拟/纷享身份
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = getVirtualUserByQywxInfo(corpId, qyUserId, ea);
        if (qywxVirtualFsUserEntity != null) {
            return qywxVirtualFsUserEntity.getUserId();
        }
        // 首先查询纷享身份
        Integer fsUserId = getFsUserIdByQyWxInfo(ea, qyUserId, false, true);
        if (fsUserId != null) {
            QywxVirtualFsUserEntity saveData =
                QywxVirtualFsUserEntity.builder()
                    .id(UUIDUtil.getUUID())
                    .ea(ea)
                    .userId(fsUserId)
                    .corpId(corpId)
                    .qyUserId(qyUserId)
                    .crmBindTime(new Date())
                    .build();
            qywxVirtualFsUserManager.insert(saveData, UserTypeEnum.QYWX);
            return fsUserId;
        }
        Integer virtualUserId = null;
        Integer maxVirtualUserId = qywxVirtualFsUserManager.getQywxMaxVirtualUserId(ea);
        if (maxVirtualUserId == null) {
            virtualUserId = QywxUserConstants.BASE_VIRTUAL_USER_ID;
        } else {
            virtualUserId = maxVirtualUserId + 1;
        }
        StringJoiner sj = new StringJoiner("_");
        sj.add(QywxUserConstants.VIRTUAL_USER_REDIS_LOCK_KEY);
        sj.add(ea);
        sj.add(virtualUserId.toString());
        try {
            // 尝试获取分布式锁 过期时间为100s
            boolean redisLock = redisManager.lock(sj.toString(), 100);
            if (!redisLock) {
                // 未获取到锁执行重试
                Thread.sleep(100);
                return getUserIdByQyWxInfo(ea, corpId, qyUserId, --tryTime);
            }
            // 再次查询虚拟表是否已经存在，防止并发插入导致唯一键冲突报错
            QywxVirtualFsUserEntity virtualFsUserEntity = getVirtualUserByQywxInfo(corpId, qyUserId, ea);
            if (virtualFsUserEntity != null) {
                return virtualFsUserEntity.getUserId();
            }
            QywxVirtualFsUserEntity saveData =
                QywxVirtualFsUserEntity.builder()
                    .id(UUIDUtil.getUUID())
                    .ea(ea)
                    .userId(virtualUserId)
                    .corpId(corpId)
                    .qyUserId(qyUserId)
                    .build();
            qywxVirtualFsUserManager.insert(saveData, UserTypeEnum.QYWX);
        } catch (Exception e) {
            log.warn("QywxUserManager.getVirtualUserIdByQyWxInfo error ea: {} corpId: {} qyUserId: {} virtualUserId: {}", ea, corpId, qyUserId, virtualUserId, e);
            return null;
        } finally {
            redisManager.unLock(sj.toString());
        }
        return virtualUserId;
    }

    /**
     * 将纷享userId更新为虚拟userId
     * @param entity
     * @param ea
     * @param tryTime
     * @return
     */
    public Integer updateFsUserIdToVirtualUserId(QywxVirtualFsUserEntity entity, String ea, int tryTime) {
        if (tryTime <= 0) {
            log.warn("QywxUserManager -> updateFsUserIdToVirtualUserId reTryTime limit ea:{}, entity:{}", ea, entity);
            return null;
        }
        Integer virtualUserId = null;
        Integer maxVirtualUserId = qywxVirtualFsUserManager.getQywxMaxVirtualUserId(ea);
        if (maxVirtualUserId == null) {
            virtualUserId = QywxUserConstants.BASE_VIRTUAL_USER_ID;
        } else {
            virtualUserId = maxVirtualUserId + 1;
        }
        StringJoiner sj = new StringJoiner("_");
        sj.add(QywxUserConstants.VIRTUAL_USER_REDIS_LOCK_KEY);
        sj.add(ea);
        sj.add(virtualUserId.toString());
        try {
            // 尝试获取分布式锁 过期时间为100s
            boolean redisLock = redisManager.lock(sj.toString(), 100);
            if (!redisLock) {
                // 未获取到锁执行重试
                return updateFsUserIdToVirtualUserId(entity, ea, --tryTime);
            }
            entity.setUserId(virtualUserId);
            qywxVirtualFsUserManager.updateVirtualFsUser(entity, UserTypeEnum.QYWX);
        } catch (Exception e) {
            log.warn("QywxUserManager -> updateFsUserIdToVirtualUserId error e:{}", e);
            return null;
        } finally {
            redisManager.unLock(sj.toString());
        }
        return virtualUserId;
    }

    /**
     * 获取明文和密文映射，例如：{明文:密文}
     * @param ea
     * @param userIds 明文
     * @return
     */
    public Map<String, String> getUserIdToOpenUserIdMap(String ea, List<String> userIds){
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (null == agentConfig) {
            return null;
        }

        Map<String, String> userIdMap = new HashMap<>();
        PageUtil<String> pageUtil = new PageUtil<>(userIds, 1000);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<String> currentPage = pageUtil.getPagedList(i);
            String accessToken = qywxManager.getAccessToken(ea);
            if (accessToken == null) {
                return null;
            }

            String url = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=" + accessToken;
            UseridToOpenuseridArg arg = new UseridToOpenuseridArg();
            arg.setUseridList(currentPage);
            UseridToOpenUseridResult result = httpManager.executePostHttp(arg, url, new TypeToken<UseridToOpenUseridResult>() {
            });
            if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getOpenUseridList())) {
                return null;
            }
            Map<String, String> map = result.getOpenUseridList().stream().collect(Collectors.toMap(UseridToOpenUseridResult.UseridToOpenUseridItem::getUserid, UseridToOpenUseridResult.UseridToOpenUseridItem::getOpenUserid, (v1,v2)->v2));
            userIdMap.putAll(map);
        }
        return userIdMap;
    }

    /**
     * 获取密文和明文的映射，例如：{密文:明文}
     * @param ea
     * @param userIds 明文
     * @return
     */
    private Map<String, String> getOpenUserIdToUserIdMap(String ea, List<String> userIds){
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (null == agentConfig) {
            return null;
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        Map<String, String> userIdMap = new HashMap<>();
        PageUtil<String> pageUtil = new PageUtil<>(userIds, 1000);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<String> currentPage = pageUtil.getPagedList(i);
            String accessToken = qywxManager.getAccessToken(ea);
            if (accessToken == null) {
                return null;
            }

            String url = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=" + accessToken;
            UseridToOpenuseridArg arg = new UseridToOpenuseridArg();
            arg.setUseridList(currentPage);
            UseridToOpenUseridResult result = httpManager.executePostHttp(arg, url, new TypeToken<UseridToOpenUseridResult>() {
            });
            if (!result.isSuccess() || CollectionUtils.isEmpty(result.getOpenUseridList())) {
                return null;
            }
            Map<String, String> map = result.getOpenUseridList().stream().collect(Collectors.toMap(UseridToOpenUseridResult.UseridToOpenUseridItem::getOpenUserid, UseridToOpenUseridResult.UseridToOpenUseridItem::getUserid, (v1,v2)->v2));
            userIdMap.putAll(map);
        }
        return userIdMap;
    }

    // 返回值： 密文 --> 明文
    public Map<String, String> getOpenUserIdToUserIdMap(String ea){
        List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryByEa(ea);
        if (CollectionUtils.isEmpty(qyWxAddressBookEntities)) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        List<String> userIds = Lists.newArrayList();
        for (QyWxAddressBookEntity qyWxAddressBookEntity : qyWxAddressBookEntities) {
            String userId = qyWxAddressBookEntity.getUserId();
            String openUserId = redisManager.getQyexOpenUserIdByUserId(ea, userId);
            if (StringUtils.isNotBlank(openUserId)) {
                result.put(openUserId, userId);
            } else {
                userIds.add(userId);
            }
        }
        Map<String, String> encodeUserIdMap = getOpenUserIdToUserIdMap(ea, userIds);
        if (MapUtils.isNotEmpty(encodeUserIdMap)) {
            encodeUserIdMap.forEach((k, v) -> redisManager.setQyexOpenUserIdByUserId(ea, v, k));
            result.putAll(encodeUserIdMap);
        }
        return result;
    }

    // 明文 --> 密文
    public Map<String, String> getUserIdToOpenUserIdMap(String ea){
        Map<String, String> result = this.getOpenUserIdToUserIdMap(ea);
        return result.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey, (v1, v2) -> v1));
    }

    /**
     * 根据密文查询明文企微userid
     * @param ea
     * @param openUserId
     * @return
     */
    public String getUserIdByOpenUserId(String ea, String openUserId){
        Map<String, String> qywxUserIdConvert = redisManager.getQywxUserIdConvert(ea);
        if (qywxUserIdConvert == null || qywxUserIdConvert.get(openUserId) == null){
            //明文换密文
            Map<String, String> openUserIdToUserIdMap = getOpenUserIdToUserIdMap(ea);
            if (openUserIdToUserIdMap == null) {
                return null;
            }
            redisManager.setQywxUserIdConvert(ea, openUserIdToUserIdMap);
            return openUserIdToUserIdMap.get(openUserId);
        }else {
            return qywxUserIdConvert.get(openUserId);
        }
    }

    /**
     * 判断企业是否需要做明文密文的转换处理
     * @param ea
     * @return
     */
    public boolean isNeedConvert(String ea){
        return qywxEncodeEnterpriseAccount != null && qywxEncodeEnterpriseAccount.contains(ea);
    }

    /**
     * 获取企业员工对应的虚拟userId (若存在纷享userId则返回纷享userId)
     *
     * @param ea 纷享ea
     * @param corpId 钉钉corpId
     * @param dingUserId 钉钉userId
     * @param tryTime 尝试次数
     */
    public Integer getUserIdByDingInfo(String ea, String corpId, String dingUserId, int tryTime) {
        if (tryTime <= 0) {
            log.warn("QywxUserManager.getVirtualUserIdByDingInfo reTryTime limit");
            return null;
        }
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(corpId) || StringUtils.isBlank(dingUserId)) {
            return null;
        }
        // 查询虚拟/纷享身份
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = getVirtualUserByQywxInfo(corpId, dingUserId, ea);
        if (qywxVirtualFsUserEntity != null) {
            return qywxVirtualFsUserEntity.getUserId();
        }

        Integer virtualUserId = null;
        Integer dingMaxVirtualUserId = qywxVirtualFsUserManager.getDingMaxVirtualUserId(ea);
        if (dingMaxVirtualUserId == null) {
            virtualUserId = QywxUserConstants.BASE_DING_VIRTUAL_USER_ID;
        } else {
            virtualUserId = dingMaxVirtualUserId + 1;
        }
        StringJoiner sj = new StringJoiner("_");
        sj.add(QywxUserConstants.VIRTUAL_USER_REDIS_LOCK_KEY);
        sj.add(ea);
        sj.add(virtualUserId.toString());
        try {
            // 尝试获取分布式锁 过期时间为100s
            boolean redisLock = redisManager.lock(sj.toString(), 100);
            if (!redisLock) {
                // 未获取到锁执行重试
                return getUserIdByDingInfo(ea, corpId, dingUserId, --tryTime);
            }
            QywxVirtualFsUserEntity saveData =
                    QywxVirtualFsUserEntity.builder()
                            .id(UUIDUtil.getUUID())
                            .ea(ea)
                            .userId(virtualUserId)
                            .corpId(corpId)
                            .qyUserId(dingUserId)
                            .build();
            qywxVirtualFsUserManager.insert(saveData, UserTypeEnum.DINGDING);
        } catch (Exception e) {
            log.warn("QywxUserManager.getVirtualUserIdByDingInfo error e:{}", e);
            return null;
        } finally {
            redisManager.unLock(sj.toString());
        }
        return virtualUserId;
    }

    /**
     * 根据获取虚拟/纷享企业微信相关消息（若改绑会直接删除之前数据）
     */
    public QywxVirtualFsUserEntity getVirtualUserByQywxInfo(String corpId, String qyUserId, String ea) {
        if (StringUtils.isBlank(corpId) || StringUtils.isBlank(qyUserId)) {
            return null;
        }
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getVirtualUserByQywxInfo(corpId, qyUserId, ea);
        if (qywxVirtualFsUserEntity == null) {
            return null;
        }
        // 查询该企业微信绑定企业与当前身份绑定企业是否相同
        List<QywxCorpAgentConfigEntity> qywxCorpAgentConfigEntities = qywxCorpAgentConfigDAO.listAgentByCorpId(corpId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(qywxCorpAgentConfigEntities)) {
            if (qywxCorpAgentConfigEntities.size() == 1) {
                // 企微单租户
                QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigEntities.get(0);
                if (qywxCorpAgentConfigEntity != null && !qywxVirtualFsUserEntity.getEa().equals(qywxCorpAgentConfigEntity.getEa())) {
                    // 企业微信换绑企业需要删除旧数据
                    qywxVirtualFsUserManager.deleteQywxVirtualFsUserById(qywxVirtualFsUserEntity.getId());
                    return null;
                }
            }
        }
        return qywxVirtualFsUserEntity;
    }

    @FilterLog
    public List<QywxVirtualFsUserEntity> batchGetVirtualUserByQyUserIds(String ea, List<String> qyUserIds){
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(qyUserIds)){
            return null;
        }

        return virtualUserManager.getVirtualUserByEaAndQyIds(ea, qyUserIds);
    }

    public Optional<Integer> getAssociateFsUserId(String ea, String qyUserId, String phone){
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(qyUserId) || StringUtils.isBlank(phone)){
            return Optional.empty();
        }
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getVirtualUserByEaAndQyId(ea, qyUserId);
        if (qywxVirtualFsUserEntity != null && QywxUserConstants.isFsUserId(qywxVirtualFsUserEntity.getUserId())){
            return Optional.of(qywxVirtualFsUserEntity.getUserId());
        }

        List<String> outAccountList = Lists.newArrayList(qyUserId);
        Result<Map<String, String>> outAccountToFsAccountBatchResult = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppid, outAccountList);
        if (outAccountToFsAccountBatchResult.isSuccess() && MapUtils.isNotEmpty(outAccountToFsAccountBatchResult.getData())) {
            Map<String, String> userIdMap = outAccountToFsAccountBatchResult.getData();
            String fsUserId = userIdMap.get(qyUserId);
            if (StringUtils.isNotBlank(fsUserId)) {
                String[] split = fsUserId.split("\\.");
                if (split.length != 0) {
                    return Optional.of(Integer.parseInt(split[split.length - 1]));
                }
            }
        }

        if (StringUtils.isNotEmpty(phone)){
           return Optional.ofNullable(fsAddressBookManager.getFsUserIdByPhone(ea, phone));
        }

        return Optional.empty();
    }
    
    /**
     * 根据企业微信身份换取纷享身份
     * @param ea 企业ea
     * @param qyUserId 外部userId
     * @param searchVirtualData 是否查询虚拟表，会返回虚拟id
     * @param searchBindPhoneData
     * @return
     */
    public Integer getFsUserIdByQyWxInfo(String ea, String qyUserId, boolean searchVirtualData, boolean searchBindPhoneData) {
        if (StringUtils.isBlank(ea) || qyUserId == null) {
            return null;
        }
        if (searchVirtualData) {
            QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyId(ea, qyUserId);
            if (qywxVirtualFsUserEntity != null) {
                return qywxVirtualFsUserEntity.getUserId();
            }
        }
        List<String> outAccountList = Lists.newArrayList(qyUserId);
        Result<Map<String, String>> outAccountToFsAccountBatchResult = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppid, outAccountList);
        if (outAccountToFsAccountBatchResult.isSuccess() && MapUtils.isNotEmpty(outAccountToFsAccountBatchResult.getData())) {
            Map<String, String> userIdMap = outAccountToFsAccountBatchResult.getData();
            String fsUserId = userIdMap.get(qyUserId);
            if (StringUtils.isNotBlank(fsUserId)) {
                String[] split = fsUserId.split("\\.");
                if (split.length != 0) {
                    Integer fsAssociateUserId = Integer.parseInt(split[split.length - 1]);
                    return fsAssociateUserId;
                }
            }
        }
        // 根据手机匹配查询账号
        if (searchBindPhoneData) {
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (agentConfig != null) {
                String accessToken = qywxManager.getAccessToken(ea);
                StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(ea, qyUserId, accessToken, true);
                if (staffDetailResult != null) {
                    return fsAddressBookManager.getFsUserIdByPhone(ea, staffDetailResult.getMobile());
                }
            }
        }
        return null;
    }

    /**
     * 根据企业微信身份换取纷享身份
     *
     * @param ea 企业ea
     * @param qyUserIds 外部userId
     * @param searchVirtualData 是否查询虚拟表，会返回虚拟id
     * @param needCreateVirtualDataIfNull 如果不存在数据是否创建虚拟账号
     */
    public Map<String, Integer> getFsUserIdByQyWxInfo(String ea, List<String> qyUserIds, boolean searchVirtualData, boolean needCreateVirtualDataIfNull) {
        Map<String, Integer> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(qyUserIds)) {
            return resultMap;
        }
        if (searchVirtualData) {
            List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = virtualUserManager.getVirtualUserByEaAndQyIds(ea, qyUserIds);
            if (CollectionUtils.isNotEmpty(qywxVirtualFsUserEntityList)) {
                resultMap.putAll(qywxVirtualFsUserEntityList.stream().collect(Collectors.toMap(QywxVirtualFsUserEntity::getQyUserId, QywxVirtualFsUserEntity::getUserId, (v1, v2) -> v1)));
            }
        }

        Result<Map<String, String>> outAccountToFsAccountBatchResult = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppid, qyUserIds);
        if (outAccountToFsAccountBatchResult.isSuccess() && MapUtils.isNotEmpty(outAccountToFsAccountBatchResult.getData())) {
            for (Map.Entry<String, String> entry : outAccountToFsAccountBatchResult.getData().entrySet()) {
                String[] split = entry.getValue().split("\\.");
                resultMap.put(entry.getKey(), Integer.parseInt(split[split.length - 1]));
            }
        }
        if (needCreateVirtualDataIfNull && MapUtils.isEmpty(resultMap)) {
            QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
            if (agentConfig == null) {
                return resultMap;
            }
            List<String> nullUser = qyUserIds.stream().filter(data -> !resultMap.keySet().contains(data)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(nullUser)) {
                for (String wxUserId : nullUser) {
                    resultMap.put(wxUserId, getUserIdByQyWxInfo(ea, agentConfig.getCorpid(), wxUserId, QywxUserConstants.TRY_TIME));
                }
            }
        }
        return resultMap;
    }

    /**
     * 纷享身份/虚拟身份换取企业微信身份
     */
    public String getQyUserIdByFsUserInfo(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            log.warn("QywxUserManager.getQyUserIdByFsUserInfo param error ea:{}, fsUserId:{}", ea, fsUserId);
            return null;
        }
        String qyUserId = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(ea, fsUserId);
        if (StringUtils.isBlank(qyUserId) && !QywxUserConstants.isVirtualUserId(fsUserId)) {
            String efsUserId = "E." + ea + "." + fsUserId;
            Result<Map<String, String>> outAccount = qyweixinAccountBindManager.fsAccountToOutAccountBatch(qywxCrmAppid, Lists.newArrayList(efsUserId));
            if (outAccount.isSuccess() && MapUtils.isNotEmpty(outAccount.getData())) {
                return outAccount.getData().get(efsUserId);
            }
        }
        return qyUserId;
    }

    /**
     * 纷享身份/虚拟身份换取企业微信身份
     */
    public Map<Integer, String> getQyUserIdByFsUserInfo(String ea, List<Integer> fsUserIds) {
        Map<Integer, String> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(fsUserIds)) {
            log.warn("QywxUserManager.getQyUserIdByFsUserInfo param error");
            return resultMap;
        }
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.queryQyUserByVirtualInfos(ea, fsUserIds);
        if (CollectionUtils.isEmpty(qywxVirtualFsUserEntityList)) {
            qywxVirtualFsUserEntityList = Lists.newArrayList();
        }
        List<Integer> bindUserId = qywxVirtualFsUserEntityList.stream().map(QywxVirtualFsUserEntity::getUserId).collect(Collectors.toList());
        List<Integer> unBindUserId = fsUserIds.stream().filter(data -> !bindUserId.contains(data) && !QywxUserConstants.isVirtualUserId(data)).collect(Collectors.toList());
        for (QywxVirtualFsUserEntity qywxVirtualFsUserEntity : qywxVirtualFsUserEntityList) {
            resultMap.put(qywxVirtualFsUserEntity.getUserId(), qywxVirtualFsUserEntity.getQyUserId());
        }
        if (CollectionUtils.isNotEmpty(unBindUserId)) {
            List<String> fsUserIdstr = Lists.newArrayList();
            for (Integer userId : unBindUserId) {
                fsUserIdstr.add("E." + ea + "." + userId);
            }
            Result<Map<String, String>> outAccount = qyweixinAccountBindManager.fsAccountToOutAccountBatch(qywxCrmAppid, fsUserIdstr);
            if (outAccount.isSuccess() && MapUtils.isNotEmpty(outAccount.getData())) {
                for (Map.Entry<String, String> map : outAccount.getData().entrySet()) {
                    String[] split = map.getKey().split("\\.");
                    resultMap.put(Integer.valueOf(split[split.length - 1]), map.getValue());
                }
            }
        }
        qywxVirtualFsUserEntityList.clear();
        unBindUserId.clear();
        return resultMap;
    }

    /**
     * 查询企业微信绑定的纷享账号，如果个没有绑定纷享账号，返回Null
     * @param ea
     * @param qywxUserId
     * @return
     */
    public Optional<Integer> getBindFsUserIdByQywxUserId(String ea, String qywxUserId){
        Map<String, Integer> resultMap = getFsUserIdByQyWxInfo(ea, Lists.newArrayList(qywxUserId), true, false);
        resultMap = Maps.filterValues(resultMap, data -> QywxUserConstants.isFsUserId(data));
        if (resultMap != null && resultMap.get(qywxUserId) != null){
            return Optional.of(resultMap.get(qywxUserId));
        }

        return Optional.empty();
    }

    public void updateQywxObjectOwner(String ea, String qywxUserId, Integer fsUserId){
        if (StringUtils.isBlank(ea)  || StringUtils.isBlank(qywxUserId) || fsUserId == null){
            return;
        }

        try {
            //更新企业微信群的owner
            updateWechatGroupObjOwner(ea, qywxUserId, fsUserId);
            //更新企业微信添加好友记录的owner
            updateWechatFriendsRecordObjOwner(ea, qywxUserId, fsUserId);
            //更新企业微信客户记录owner
            updateWechatWorkExternalUserObjOwner(ea, qywxUserId, fsUserId);
            //更新企微客户统计
            updateWechatAccountStatisticsObjOwner(ea, qywxUserId, fsUserId);
            //企微客户群统计
            updateWechatAccountGroupStatisticsObjOwner(ea, qywxUserId, fsUserId);
        }catch (Exception e){
            log.info("updateQywxObjectOwner error ea:{} qywxUserId:{} fsUserId:{} e:", ea, qywxUserId, fsUserId, e);
        }
    }

    private void batchUpdateOwnerByIds(String ea, String apiName, List<ObjectData> objectDataList, int fsUserId){
        if (CollectionUtils.isEmpty(objectDataList)){
            return;
        }

        HeaderObj headerObj =  new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        int size = objectDataList.size();
        long pageSize = 1000;
        long totalPage = size % pageSize == 0 ? size / pageSize : size / pageSize + 1;
        for (long i = 0; i < totalPage; i++) {
            List<ObjectData> currentObjectDataList = objectDataList.stream().skip(i * pageSize).limit(pageSize).collect(Collectors.toList());
            List<ActionChangeOwnerArg.ChangeOwnerData> changeOwnerDataList = Lists.newArrayList();
            for (ObjectData objectData : currentObjectDataList) {
                ActionChangeOwnerArg.ChangeOwnerData changeOwnerData = new ActionChangeOwnerArg.ChangeOwnerData();
                changeOwnerData.setObjectDataId(objectData.getId());
                changeOwnerData.setOwnerId(Lists.newArrayList(String.valueOf(fsUserId)));
                changeOwnerDataList.add(changeOwnerData);
            }
            ActionChangeOwnerArg arg = new ActionChangeOwnerArg(null, null);
            arg.setData(changeOwnerDataList);
            com.fxiaoke.crmrestapi.common.result.Result<ActionChangeOwnerResult> result = metadataActionService.changeOwner(headerObj, apiName, arg);
        }
    }

    private void updateWechatAccountGroupStatisticsObjOwner(String ea, String qywxUserId, Integer fsUserId) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter("group_master_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(qywxUserId));
        paasQueryArg.addFilter("owner", PaasAndCrmOperatorEnum.NOT_EQUALS.getCrmOperator(), Lists.newArrayList(String.valueOf(fsUserId)));
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        queryFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData>  pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, 10000);
        if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())){
            return;
        }

        batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_ACCOUNT_GROUP_STATISTICS_OBJ.getName(), pageObjects.getDataList(), fsUserId);
    }

    private void updateWechatAccountStatisticsObjOwner(String ea, String qywxUserId, Integer fsUserId) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_ACCOUNT_STATISTICS_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(qywxUserId));
        paasQueryArg.addFilter("owner", PaasAndCrmOperatorEnum.NOT_EQUALS.getCrmOperator(), Lists.newArrayList(String.valueOf(fsUserId)));
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        queryFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData>  pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, 10000);
        if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())){
            return;
        }

        batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_ACCOUNT_STATISTICS_OBJ.getName(), pageObjects.getDataList(), fsUserId);
    }


    public void updateWechatGroupObjOwner(String ea, String qywxUserId, Integer fsUserId){
        //判断企业是否停用或过期
        if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(ea)) {
            log.warn("updateWechatGroupObjOwner ea is stop or license expire ea :{}",ea);
            return;
        }
        UpdateWechatGroupObjOwnerMqArg updateWechatGroupObjOwnerMqArg = new UpdateWechatGroupObjOwnerMqArg();
        updateWechatGroupObjOwnerMqArg.setEa(ea);
        updateWechatGroupObjOwnerMqArg.setQywxUserId(qywxUserId);
        updateWechatGroupObjOwnerMqArg.setFsUserId(fsUserId);
        requestBufferSender.send(ea, updateWechatGroupObjOwnerMqArg, RequestBufferTagConstants.UPDATE_WECHAT_GROUP_OWNER);
    }

    public void updateWechatGroupObjOwner(UpdateWechatGroupObjOwnerMqArg mqArg){
        String ea = mqArg.getEa();
        String qywxUserId = mqArg.getQywxUserId();
        Integer fsUserId = mqArg.getFsUserId();
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter("leader_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(qywxUserId));
        paasQueryArg.addFilter("owner", PaasAndCrmOperatorEnum.NOT_EQUALS.getCrmOperator(), Lists.newArrayList(String.valueOf(fsUserId)));
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        queryFilterArg.setSelectFields(selectFields);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, -10000, queryFilterArg);
        if (totalCount <= 0){
            return;
        }
        int pageSize = 1000;
        int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
        List<ObjectData> objectDataList = Lists.newArrayList();
        for(int i = 1; i <= totalPage; i++) {
            InnerPage<ObjectData> pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, i, pageSize);
            if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())) {
                break;
            }
            objectDataList.addAll(pageObjects.getDataList());
        }
        //更新群主
        batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), objectDataList, fsUserId);
        for (ObjectData objectData : objectDataList) {
            crmV2Manager.editObjectDataOwnOrganization(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), objectData.getId(), fsUserId);
        }
        //更新群成员
        List<String> wechatGroupIdList = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
        updateWechatGroupUserObjOwner(ea, wechatGroupIdList, fsUserId);
    }

    public void updateWechatGroupUserObjOwner(String ea, List<String> groupChatIds, Integer fsUserId){
        // 一个群可能会有很多群成员，每个群单独处理吧
        for (String wechatGroupId : groupChatIds) {
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
            paasQueryArg.addFilter("qw_group_chat_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(wechatGroupId));
            paasQueryArg.addFilter("owner", PaasAndCrmOperatorEnum.NOT_EQUALS.getCrmOperator(), Lists.newArrayList(String.valueOf(fsUserId)));
            queryFilterArg.setQuery(paasQueryArg);
            List<String> selectFields = Lists.newArrayList("_id");
            queryFilterArg.setSelectFields(selectFields);
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, -10000, queryFilterArg);
            if (totalCount <= 0) {
                continue;
            }
            int pageSize = 1000;
            int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
            List<ObjectData> objectDataList = Lists.newArrayList();
            for(int i = 1; i <= totalPage; i++) {
                InnerPage<ObjectData> pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, i, pageSize);
                if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())) {
                    break;
                }
                objectDataList.addAll(pageObjects.getDataList());
            }
            batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), objectDataList, fsUserId);
            for (ObjectData objectData : objectDataList) {
                crmV2Manager.editObjectDataOwnOrganization(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), objectData.getId(), fsUserId);
            }
        }
    }

    private void updateWechatFriendsRecordObjOwner(String ea, String qywxUserId, Integer fsUserId){
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(qywxUserId));
        paasQueryArg.addFilter("owner", PaasAndCrmOperatorEnum.NOT_EQUALS.getCrmOperator(), Lists.newArrayList(String.valueOf(fsUserId)));
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id");
        queryFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData>  pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, 10000);
        if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())){
            return;
        }

        batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), pageObjects.getDataList(), fsUserId);
    }

    /**
     * 个人客户数上限2w个,因此仅考虑极限2w数量性能支撑
     * 参考官方说明: https://open.work.weixin.qq.com/help2/pc/18176?person_id=1&is_tencent=#%E4%B8%80%E3%80%81%E4%B8%AA%E4%BA%BA%E5%AE%A2%E6%88%B7%E6%95%B0%E6%89%A9%E5%AE%B9
     *
     * @param ea
     * @param qywxUserId
     * @param fsUserId
     */
    private void updateWechatWorkExternalUserObjOwner(String ea, String qywxUserId, Integer fsUserId){
        log.info("QywxUserManager updateWechatWorkExternalUserObjOwner ea:{} qywxUserId:{} fsUserId:{}", ea, qywxUserId, fsUserId);
        // 极端情况20000条好友记录
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 500);
        paasQueryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(qywxUserId));
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("external_user_id");
        queryFilterArg.setSelectFields(selectFields);
        InnerPage<ObjectData>  pageObjects = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, queryFilterArg);
        if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())){
            return;
        }

        //通过企微客户外部联系人id查询企业微信客户对象 极端情况20000条企微客户
        List<ObjectData> externalUserObject = Lists.newArrayList();
        List<String> externalUserIds = pageObjects.getDataList().stream().map(object -> object.getString("external_user_id")).distinct().collect(Collectors.toList());
        log.info("QywxUserManager updateWechatWorkExternalUserObjOwner externalUserIds:{}", externalUserIds);
        List<List<String>> partition = Lists.partition(externalUserIds, 2000);
        for (List<String> part : partition) {
            PaasQueryArg externalUserQuery = new PaasQueryArg(0, 500);
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            externalUserQuery.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), part);
            externalUserQuery.addFilter("owner", PaasAndCrmOperatorEnum.NOT_EQUALS.getCrmOperator(), Lists.newArrayList(String.valueOf(fsUserId)));
            queryFilterArg.setQuery(externalUserQuery);
            selectFields = Lists.newArrayList("_id", "owner");
            queryFilterArg.setSelectFields(selectFields);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, queryFilterArg);
            if (CollectionUtils.isEmpty(objectDataInnerPage.getDataList())){
                continue;
            }
            externalUserObject.addAll(objectDataInnerPage.getDataList());
        }
        if (CollectionUtils.isEmpty(externalUserObject)){
            return;
        }

        // 查询owner添加的好友, 最坏情况查询allOwners.size()次, 最好情况全为-10000
        List<Integer> allOwners = externalUserObject.stream().filter(e -> !e.getOwners().contains(-10000)).map(ObjectData::getOwner).distinct().collect(Collectors.toList());
        Map<Integer, List<String>> ownerToFriendRecordExternalUserIds = Maps.newHashMap();//负责人加的企微客户记录
        Map<Integer, String> ownerToQyUserIdMap = Maps.newHashMap();//负责人的企微员工号
        if (CollectionUtils.isNotEmpty(allOwners)) {
            try {
                List<QywxVirtualFsUserEntity> entities = qywxVirtualFsUserManager.queryQyUserByVirtualInfos(ea, allOwners);
                ownerToQyUserIdMap.putAll(entities.stream().filter(e->!QywxUserConstants.isVirtualUserId(e.getUserId())).collect(Collectors.toMap(QywxVirtualFsUserEntity::getUserId, QywxVirtualFsUserEntity::getQyUserId, (e1, e2) -> e1)));
                //假设该负责人有加过这批客户的记录
                Map<Integer, List<String>> ownerToExternalUserIds = externalUserObject.stream().filter(e -> !e.getOwners().contains(-10000))
                        .collect(Collectors.groupingBy(ObjectData::getOwner, Collectors.mapping(e -> e.get("_id").toString(), Collectors.toList())));
                //查询负责人有无加过这批客户的记录
                for (Integer owner : allOwners) {
                    List<String> friendRecordExternalUserIds = Lists.newArrayList();
                    if (StringUtils.isNotBlank(ownerToQyUserIdMap.get(owner)) && CollectionUtils.isNotEmpty(ownerToExternalUserIds.get(owner))) {
                        List<String> ownerExternalUserIds = ownerToExternalUserIds.get(owner);
                        List<List<String>> ownerExternalUserIdsPartition = Lists.partition(ownerExternalUserIds, 2000);
                        for (List<String> part : ownerExternalUserIdsPartition) {
                            PaasQueryArg queryArg = new PaasQueryArg(0, 500);
                            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                            queryArg.addFilter("qywx_user_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Collections.singletonList(ownerToQyUserIdMap.get(owner)));
                            queryArg.addFilter("external_user_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), part);
                            queryFilterArg.setQuery(queryArg);
                            selectFields = Lists.newArrayList("_id", "external_user_id");
                            queryFilterArg.setSelectFields(selectFields);
                            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, queryFilterArg);
                            if (CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                                friendRecordExternalUserIds.addAll(objectDataInnerPage.getDataList().stream().map(e -> e.get("external_user_id").toString()).distinct().collect(Collectors.toList()));
                            }
                        }
                    }
                    ownerToFriendRecordExternalUserIds.put(owner, friendRecordExternalUserIds);
                }
            } catch (Exception e) {
                log.info("QywxUserManager updateWechatWorkExternalUserObjOwner error:{}", e);
            }
        }
        log.info("QywxUserManager updateWechatWorkExternalUserObjOwner ownerToFriendRecordExternalUserIds:{}", ownerToFriendRecordExternalUserIds);
        log.info("QywxUserManager updateWechatWorkExternalUserObjOwner ownerToQyUserIdMap:{}", ownerToQyUserIdMap);

        List<ObjectData> updateOwnerObjs = Lists.newArrayList();
        List<ObjectData> addRelateTeamObjs = Lists.newArrayList();
        for (ObjectData objectData : externalUserObject) {
            Integer owner = objectData.getOwner();
            /**
             * 逻辑变更:
             * 1.若owner为-10000, 直接更新fsUserId为新负责人; 否则反之.
             * 2.若owner加过该企微客户, 则不变, 蒋fsUserId加入团队成员; 否则反之.
             */
            if (owner == -10000) {
                updateOwnerObjs.add(objectData);
            } else {
                if (ownerToFriendRecordExternalUserIds.get(owner) != null && ownerToFriendRecordExternalUserIds.get(owner).contains(objectData.get("_id").toString())) {
                    addRelateTeamObjs.add(objectData);
                } else {
                    updateOwnerObjs.add(objectData);
                }
            }
        }
        log.info("QywxUserManager updateWechatWorkExternalUserObjOwner updateOwnerObjs:{}", updateOwnerObjs);
        log.info("QywxUserManager updateWechatWorkExternalUserObjOwner addRelateTeamObjs:{}", addRelateTeamObjs);

        HeaderObj headerObj =  new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        //批量更新owner
        if (CollectionUtils.isNotEmpty(updateOwnerObjs)) {
            batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), updateOwnerObjs, fsUserId);
            for (ObjectData objectData : updateOwnerObjs) {
                crmV2Manager.editObjectDataOwnOrganization(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectData.getId(), fsUserId);
            }
        }
        if (CollectionUtils.isNotEmpty(addRelateTeamObjs)){
            for (ObjectData objectData : addRelateTeamObjs) {
                //添加到相关团队
                GetTeamMemberArg getTeamMemberArg = new GetTeamMemberArg();
                getTeamMemberArg.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
                getTeamMemberArg.setDataID(objectData.getId());
                com.fxiaoke.crmrestapi.common.result.Result<GetTeamMemberResult> getTeamMemberResult = metadataControllerService.getTeamMember(headerObj, getTeamMemberArg);
                if (!getTeamMemberResult.isSuccess() || getTeamMemberResult.getData() == null){
                    continue;
                }
                Set<Integer> crmExistedTeamMemberSet = getTeamMemberResult.getData().getAllTeamMember();
                if (CollectionUtils.isEmpty(crmExistedTeamMemberSet) || !crmExistedTeamMemberSet.contains(fsUserId)){
                    crmV2Manager.doAddTeamMemberToCrm(headerObj, Lists.newArrayList(fsUserId), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectData.getId());
                }
            }
        }
    }

    /**
     * 根据虚拟用户信息查询用户信息
     */
    public FSEmployeeMsg getQywxInfoByEaAndUserId(String ea, Integer userId) {
        FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
        if (StringUtils.isBlank(ea) || userId == null) {
            return null;
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.getCardInfoByEaAndUserId agentConfig is null ea:{}", ea);
            return null;
        }
        // 查询绑定关系
        String qywxUserId = this.getQyUserIdByFsUserInfo(ea, userId);
        if (StringUtils.isBlank(qywxUserId)) {
            if (!QywxUserConstants.isVirtualUserId(userId)) {
                Map<Integer, FSEmployeeMsg> searchFsResult = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, Lists.newArrayList(userId), true);
                if (MapUtils.isNotEmpty(searchFsResult) && searchFsResult.get(userId) != null) {
                    return searchFsResult.get(userId);
                }
            }
            log.warn("QywxUserManager.getCardInfoByEaAndUserId qywxUserId is null ea:{}, userId:{}", ea, userId);
            return null;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.getCardInfoByEaAndUserId accessToken is null agentConfig:{}", agentConfig);
            return null;
        }
        StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(ea, qywxUserId, accessToken, false);
        if (staffDetailResult == null) {
            if (!QywxUserConstants.isVirtualUserId(userId)) {
                // 最后查询纷享信息
                Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, Lists.newArrayList(userId), true);
                if (MapUtils.isNotEmpty(fsEmployeeMsgMap) && fsEmployeeMsgMap.get(userId) != null) {
                    return fsEmployeeMsgMap.get(userId);
                }
            }
            log.warn("QywxUserManager.getCardInfoByEaAndUserId staffDetailResult is null ea:{}, qywxUserId:{}", ea, qywxUserId);
            return null;
        }

        Map<Integer, Department> departmentMap = Maps.newHashMap();
        DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
        if (departmentListResult != null && CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
            departmentMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, data -> data, (v1, v2) -> v1));
            fsEmployeeMsg.setDepartmentIds(departmentListResult.getDepartmentList().stream().map(Department::getId).collect(Collectors.toList()));
        }
        fsEmployeeMsg.setName(staffDetailResult.getName());
        fsEmployeeMsg.setFullName(staffDetailResult.getName());
        fsEmployeeMsg.setProfileImage(staffDetailResult.getAvatar());
        fsEmployeeMsg.setEmail(staffDetailResult.getEmail());
        fsEmployeeMsg.setMobile(staffDetailResult.getMobile());
        if (CollectionUtils.isNotEmpty(staffDetailResult.getDepartment()) && departmentMap.get(staffDetailResult.getDepartment().get(0)) != null) {
            fsEmployeeMsg.setDepartment(departmentMap.get(staffDetailResult.getDepartment().get(0)).getName());
        }
        fsEmployeeMsg.setPost(staffDetailResult.getPosition());
        return fsEmployeeMsg;
    }

    /**
     * 根据虚拟用户信息查询用户信息
     */
    public Map<Integer, FSEmployeeMsg> getQywxInfoByEaAndUserId(String ea, List<Integer> userIds) {
        Map<Integer, FSEmployeeMsg> resultMap = Maps.newConcurrentMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(userIds)) {
            return resultMap;
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.getQywxInfoByEaAndUserId agentConfig is null ea:{}", ea);
            return resultMap;
        }
        Map<Integer, String> bindMap = this.getQyUserIdByFsUserInfo(ea, userIds);
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.getCardInfoByEaAndUserId accessToken is null agentConfig:{}", agentConfig);
            return resultMap;
        }
        // 获取部门信息
        Map<Integer, Department> departmentMap = Maps.newHashMap();
        DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
        if (departmentListResult != null && CollectionUtils.isNotEmpty(departmentListResult.getDepartmentList())) {
            departmentMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, data -> data, (v1, v2) -> v1));
        }
        Map<Integer, Department> finalDepartmentMap = departmentMap;
        if (MapUtils.isNotEmpty(bindMap)) {
            Integer userCount = qywxAddressBookManager.countByEa(ea);
            // 若同步过企业微信通讯录则直接往企业微信通讯录取
            if (userCount != 0) {
                List<String> qywxUserIds = Lists.newArrayList(bindMap.values());
                List<QyWxAddressBookEntity> qyWxAddressBookEntityList = qywxAddressBookManager.queryEaAndUserId(ea, qywxUserIds);
                List<UserEntity> userEntities = userManager.queryByEaAndQyUserIds(ea, qywxUserIds);
                Map<String, String> qywxUserIdToAvatar = Maps.newHashMap();
                if (userEntities != null) {
                    qywxUserIdToAvatar = userEntities.stream().filter(e->StringUtils.isNotBlank(e.getQyUserId()) && StringUtils.isNotBlank(e.getAvatar()))
                            .collect(Collectors.toMap(UserEntity::getQyUserId, UserEntity::getAvatar, (e1,e2)->e1));
                }
                if (CollectionUtils.isNotEmpty(qyWxAddressBookEntityList)) {
                    Map<String, QyWxAddressBookEntity> qyWxAddressBookEntityMap = qyWxAddressBookEntityList.stream().collect(Collectors.toMap(QyWxAddressBookEntity::getUserId, data -> data,(v1,v2)->v1));
                    for (Map.Entry<Integer, String> entry : bindMap.entrySet()) {
                        QyWxAddressBookEntity qyWxAddressBookEntity = qyWxAddressBookEntityMap.get(entry.getValue());
                        if (qyWxAddressBookEntity == null) {
                            continue;
                        }
                        FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
                        fsEmployeeMsg.setName(qyWxAddressBookEntity.getName());
                        fsEmployeeMsg.setFullName(qyWxAddressBookEntity.getName());
                        String avatar = qyWxAddressBookEntity.getAvatar();
                        if (StringUtils.isBlank(avatar)) {
                            avatar = qywxUserIdToAvatar.get(qyWxAddressBookEntity.getUserId());
                        }
                        fsEmployeeMsg.setProfileImage(fileV2Manager.replaceUrlToHttps(avatar));
                        fsEmployeeMsg.setMobile(qyWxAddressBookEntity.getMobile());
                        fsEmployeeMsg.setDepartment(qyWxAddressBookEntity.getDepartmentName());
                        resultMap.put(entry.getKey(), fsEmployeeMsg);
                    }
                }
            } else {
                CountDownLatch countDownLatch = new CountDownLatch(bindMap.size());
                for (Map.Entry<Integer, String> map : bindMap.entrySet()) {
                    ThreadPoolUtils.execute(() -> {
                        try {
                            StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(ea, map.getValue(), accessToken, false);
                            if (staffDetailResult != null) {
                                FSEmployeeMsg fsEmployeeMsg = new FSEmployeeMsg();
                                fsEmployeeMsg.setName(staffDetailResult.getName());
                                fsEmployeeMsg.setFullName(staffDetailResult.getName());
                                fsEmployeeMsg.setProfileImage(fileV2Manager.replaceUrlToHttps(staffDetailResult.getAvatar()));
                                fsEmployeeMsg.setMobile(staffDetailResult.getMobile());
                                /*if (CollectionUtils.isNotEmpty(staffDetailResult.getDepartment()) && finalDepartmentMap.get(staffDetailResult.getDepartment().get(0)) != null) {
                                    fsEmployeeMsg.setDepartment(finalDepartmentMap.get(staffDetailResult.getDepartment().get(0)).getName());
                                }*/
                                if (CollectionUtils.isNotEmpty(staffDetailResult.getDepartment())) {
                                    Integer max = Collections.max(staffDetailResult.getDepartment());
                                    fsEmployeeMsg.setDepartment(qywxManager.buildDepartmentFullName(finalDepartmentMap, max));
                                }
                                resultMap.put(map.getKey(), fsEmployeeMsg);
                            }
                        } catch (Exception e) {
                            log.warn("QywxUserManager.getQywxInfoByEaAndUserId getStaffDetail error e:{}", e);
                        } finally {
                            countDownLatch.countDown();
                        }
                    }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
                }
                try {
                    countDownLatch.await(30L, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.warn("QywxUserManager.getQywxInfoByEaAndUserId customerProcessLatch.await error e:{}", e);
                }
            }
        }
        List<Integer> unFindData = userIds.stream().filter(data -> (resultMap.get(data) == null) && !QywxUserConstants.isVirtualUserId(data)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unFindData)) {
            resultMap.putAll(fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, unFindData, true));
        }
        departmentMap.clear();
        finalDepartmentMap.clear();
        bindMap.clear();
        unFindData.clear();
        return resultMap;
    }

    /**
     * 根据企业微信部门id换取纷享userId
     */
    public List<Integer> getFsUserIdByQywxDepartment(String ea, List<Integer> departmentIds) {
        List<Integer> userId = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(departmentIds)) {
            return userId;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.queryQywxStaff is null ea:{}", ea);
            return userId;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.queryQywxStaff accessToken is null ea:{}", ea);
            return userId;
        }
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.getStaffByDepartmentId(accessToken, departmentIds, true, true, ea);
        if (CollectionUtils.isEmpty(staffInfoList)) {
            return userId;
        }
        Map<String, Integer> userMap = getFsUserIdByQyWxInfo(ea, staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList()), true, true);
        userId = Lists.newArrayList(userMap.values());
        return userId;
    }

    /**
     * 获取开通卡片用户id
     */
    private List<String> getOpenCardUserIds(String ea, List<String> qywxUserIds) {
        List<String> userIds = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(qywxUserIds)) {
            return userIds;
        }
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity == null) {
            log.warn("QywxUserManager.getOpenCardUserIds qywxMiniappConfigEntity is null ea:{}", ea);
            return userIds;
        }

        List<List<String>> partition = Lists.partition(qywxUserIds, 5000);
        CountDownLatch countDownLatch = new CountDownLatch(partition.size());
        for (List<String> part : partition) {
            ThreadPoolUtils.executeWithTraceContext(() -> {
                List<String> list = qywxVirtualFsUserManager.queryOpenCardQyuserId(ea, qywxMiniappConfigEntity.getAppid(), part);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
                    userIds.addAll(list);
                }
                countDownLatch.countDown();
            }, ThreadPoolUtils.ThreadPoolTypeEnums.QYWX_ADDRESS_BOOK);
        }
        try {
            countDownLatch.await(5L, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("QywxUserManager.getOpenCardUserIds.await error e:{}", e);
        }

        return userIds;
    }

    public Map<String, Integer> getOpenCardUserIdMap(String ea, List<String> qywxUserIds) {
        Map<String, Integer> resultMap = Maps.newHashMap();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(qywxUserIds)) {
            return resultMap;
        }
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity == null) {
            log.warn("QywxUserManager.getOpenCardUserIdMap qywxMiniappConfigEntity is null ea:{}", ea);
            return resultMap;
        }
        List<QywxVirtualFsUserEntity> qywxVirtualFsUserEntityList = qywxVirtualFsUserManager.queryOpenCardQyAndFsUserId(ea, qywxMiniappConfigEntity.getAppid(), qywxUserIds);
        if (CollectionUtils.isEmpty(qywxVirtualFsUserEntityList)) {
            return resultMap;
        }
        return qywxVirtualFsUserEntityList.stream().collect(Collectors.toMap(QywxVirtualFsUserEntity::getQyUserId, QywxVirtualFsUserEntity::getUserId, (v1, v2) -> v1));
    }

    /**
     * 获取开通名片用户统计(仅数值)
     */
    public CardUserStatisticContainer getOpenCardUserStatistic(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("QywxUserManager.getOpenCardUserStatistic ea is null");
            return null;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.getOpenCardUserStatistic is null ea:{}", ea);
            return null;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.getOpenCardUserStatistic accessToken is null ea:{}", ea);
            return null;
        }
        // 获取全部员工
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, true, false);
        if (CollectionUtils.isEmpty(staffInfoList)) {
            return null;
        }
        CardUserStatisticContainer cardUserStatisticContainer = new CardUserStatisticContainer();
        cardUserStatisticContainer.setOpenCardUser(0);
        cardUserStatisticContainer.setNotOpenCardUser(staffInfoList.size());
        cardUserStatisticContainer.setTotalUser(staffInfoList.size());
        List<String> openCardUserIds = getOpenCardUserIds(ea, staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(openCardUserIds)) {
            return cardUserStatisticContainer;
        }
        cardUserStatisticContainer.setOpenCardUser(openCardUserIds.size());
        cardUserStatisticContainer.setNotOpenCardUser(staffInfoList.size() - openCardUserIds.size());
        staffInfoList.clear();
        openCardUserIds.clear();
        return cardUserStatisticContainer;
    }

    /**
     * 获取开通名片详情
     */
    public CardUserStatisticContainer getOpenCardUserDetail(String ea) {
        if (StringUtils.isBlank(ea)) {
            log.warn("QywxUserManager.getOpenCardUserDetail ea is null");
            return null;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.getOpenCardUserDetail is null ea:{}", ea);
            return null;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.getOpenCardUserDetail accessToken is null ea:{}", ea);
            return null;
        }
        // 获取全部员工
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, false);
        if (CollectionUtils.isEmpty(staffInfoList)) {
            return null;
        }
        List<String> qyUserIds = staffInfoList.stream().map(DepartmentStaffResult.StaffInfo::getUserId).collect(Collectors.toList());
        List<QywxEmployeeResult> qywxEmployeeResults = qywxEmployeeManager.batchByQyUserIds(ea, qyUserIds, false);
        Map<String, QywxEmployeeResult> qywxEmployeeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qywxEmployeeResults)) {
            qywxEmployeeMap = qywxEmployeeResults.stream().collect(Collectors.toMap(QywxEmployeeResult::getUserId, Function.identity(), (v1, v2) -> v1));
        }

        List<CardUserInfo> cardUserInfoList = Lists.newArrayList();
        CardUserStatisticContainer cardUserStatisticContainer = new CardUserStatisticContainer();
        cardUserStatisticContainer.setOpenCardUser(0);
        cardUserStatisticContainer.setNotOpenCardUser(staffInfoList.size());
        cardUserStatisticContainer.setTotalUser(staffInfoList.size());
        cardUserStatisticContainer.setCardUserDetailList(cardUserInfoList);
        Map<String, Integer> openCardUserIdMap = getOpenCardUserIdMap(ea, staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList()));
        for (DepartmentStaffResult.StaffInfo staffInfo : staffInfoList) {
            CardUserInfo cardUserInfo = new CardUserInfo();
            cardUserInfo.setUserName(staffInfo.getName());
            cardUserInfo.setIsOpen(UserCardOpenStatusEnum.NOT_OPEN.getStatus());
            cardUserInfo.setUserId(staffInfo.getUserId());
            Integer fsUserId = openCardUserIdMap.get(staffInfo.getUserId());
            if(fsUserId != null) {
                cardUserInfo.setIsOpen(UserCardOpenStatusEnum.OPEN.getStatus());
                cardUserInfo.setFsUserId(fsUserId);
            }
            QywxEmployeeResult qywxEmployeeResult = qywxEmployeeMap.get(staffInfo.getUserId());
            if (qywxEmployeeResult != null) {
                cardUserInfo.setDepartment(qywxEmployeeResult.getWechatDepartmentName());
            }
            cardUserInfoList.add(cardUserInfo);
        }
        staffInfoList.clear();
        openCardUserIdMap.clear();
        return cardUserStatisticContainer;
    }

    /**
     * 过滤虚拟身份
     */
    public List<Integer> filterVirtualUser(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return userIds;
        }
        return userIds.stream().filter(data -> !QywxUserConstants.isVirtualUserId(data)).collect(Collectors.toList());
    }

    /**
     * 通过纷享/虚拟身份换取uid
     */
    public String getUidByFsUserInfo(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return null;
        }
        String uid;
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (WxAppInfoEnum.isMankeep(appId)) {
            uid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, fsUserId, AccountTypeEnum.MINI_APP.getType(), null);
        } else {
            String qywyAppUid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, fsUserId, AccountTypeEnum.QYWX_MINI_APP.getType(), appId);
            String miniAppUid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, fsUserId, AccountTypeEnum.MINI_APP.getType(), appId);
            uid = qywyAppUid == null ? miniAppUid : qywyAppUid;
            if (StringUtils.isEmpty(uid)) {
                uid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, fsUserId, AccountTypeEnum.DING_MINI_APP.getType(), null);
            }
        }
        if (StringUtils.isBlank(uid)) {
            // 如果还为空，那就去找会员员工/伙伴
            uid = fsBindManager.queryUidByFsUserIdAndFsEa(ea, fsUserId, AccountTypeEnum.MEMBER_WECHAT_MINI_APP.getType(), null);
        }
        return uid;
    }

    /**
     * 通过ea获取企业fsUserId
     */
    public List<Integer> getFsUidByEa(String ea) {
        List<Integer> fsUserId = Lists.newArrayList();
        if (StringUtils.isBlank(ea)) {
            return fsUserId;
        }
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (dingManager.isDingAddressbook(ea)){
            fsUserId = fsBindManager.queryFSBindByFsUserId(ea, Lists.newArrayList(AccountTypeEnum.DING_MINI_APP.getType()), null);
        }else if(WxAppInfoEnum.isMankeep(appId)) {
            fsUserId = fsBindManager.queryFSBindByFsUserId(ea, Lists.newArrayList(AccountTypeEnum.MINI_APP.getType()), null);
        } else {
            fsUserId = fsBindManager.queryFSBindByFsUserId(ea, Lists.newArrayList(AccountTypeEnum.QYWX_MINI_APP.getType(), AccountTypeEnum.MEMBER_WECHAT_MINI_APP.getType()), appId);
        }
        return fsUserId;
    }

    /**
     * 通过纷享/虚拟身份换取uid
     */
    public List<FSBindEntity> getFsBindByFsUserInfos(String ea, List<Integer> fsUserId) {
        List<FSBindEntity> fsBindEntityList = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(fsUserId)) {
            return fsBindEntityList;
        };
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        PageUtil pageUtil = new PageUtil(fsUserId, 5000);
        for (int i = 0; i < pageUtil.getPageCount(); i++) {
            List<Integer> currentPageList = pageUtil.getPagedList(i + 1);
            List<FSBindEntity> currentBindList = null;
            if (dingManager.isDingAddressbook(ea)){
                currentBindList = fsBindManager.queryFSBindByEmployeeIds(ea, currentPageList, Lists.newArrayList(AccountTypeEnum.DING_MINI_APP.getType()), null);
            } else if (WxAppInfoEnum.isMankeep(appId)) {
                currentBindList = fsBindManager.queryFSBindByEmployeeIds(ea, currentPageList, Lists.newArrayList(AccountTypeEnum.MINI_APP.getType()), null);
            } else {
                currentBindList = fsBindManager.queryFSBindByEmployeeIds(ea, currentPageList, Lists.newArrayList(AccountTypeEnum.QYWX_MINI_APP.getType(), AccountTypeEnum.MEMBER_WECHAT_MINI_APP.getType()), appId);
            }
            if (CollectionUtils.isNotEmpty(currentBindList)){
                fsBindEntityList.addAll(currentBindList);
            }
        }
        return fsBindEntityList;
    }

    /**
     * 根据手机号查询企业微信员工
     * @param ea
     * @param phone
     * @return
     */
    public StaffInfo getQywxUserInfoByPhone(String ea, String phone) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(phone)) {
            return null;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.getQywxUserInfoByPhone agentConfig is null");
            return null;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.getQywxUserInfoByPhone accessToken is null ea:{}", ea);
            return null;
        }
        //如果企业微信员工数据量太大，就不要同步查询了，例如：双胞胎
        /*boolean needRefreshData = true;
        if (notRefreshQywxStaffEaList.contains(ea)){
            needRefreshData = false;
        }
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, needRefreshData);
        if (CollectionUtils.isEmpty(staffInfoList)) {
            log.warn("QywxUserManager.getQywxUserInfoByPhone error staffInfoList is null");
            return null;
        }
        for (DepartmentStaffResult.StaffInfo staffInfo : staffInfoList) {
            if (phone.equals(staffInfo.getMobile())) {
                return staffInfo;
            }
        }*/
        List<StaffInfo> staffInfos = qywxManager.queryStaffByEaAndMobile(ea, phone, accessToken);
        if (CollectionUtils.isEmpty(staffInfos)) {
            return null;
        }
        return staffInfos.get(0);
    }

    public AccountEntity getAccountByPhone(String ea, String phone) {
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(phone)) {
            return null;
        }
        AccountEntity accountEntity = null;
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        if (WxAppInfoEnum.isMankeep(appId)) {
            List<AccountEntity> resultList = accountDAO.queryAccountByPhone(phone, AccountTypeEnum.MINI_APP.getType(), null);
            accountEntity = CollectionUtils.isNotEmpty(resultList) ? resultList.get(0) : null;
        } else {
            List<AccountEntity> accountEntities = accountDAO.queryQyWxAccountByPhone(phone, ea, appId);
            if (CollectionUtils.isEmpty(accountEntities)) {
                return null;
            }

            int size = accountEntities.size();
            if (size == 1) {
                accountEntity = accountEntities.get(0);
            } else if (size > 1) {
                // 处理在微信和企业微信存在2个user的问题
                log.info("merge user occur");
                mergeUser(accountEntities.get(0).getUid(), accountEntities.get(1).getUid());
                List<AccountEntity> accountEntities1 = accountDAO.queryQyWxAccountByPhone(phone, ea, appId);
                if (CollectionUtils.isEmpty(accountEntities)) {
                    return null;
                }
                accountEntity = accountEntities1.get(0);
            }
        }
        return accountEntity;
    }

    /**
     * 处理一个手机号在微信和企微都存在user的情况
     * @param firstUid
     * @param secondUid
     */
    private void mergeUser(String firstUid, String secondUid){
        String needRemainUid;
        String needDeleteUid;
        // 已企微名片作为标准，将微信名片的数据合并过来
        UserEntity firstUserEntity = userManager.queryByUid(firstUid);

        if (Objects.nonNull(firstUserEntity) && StringUtils.isNotBlank(firstUserEntity.getQyUserId())) {
            needRemainUid = firstUid;
            needDeleteUid = secondUid;
        } else {
            needRemainUid = secondUid;
            needDeleteUid = firstUid;
        }

        // 合并数据
        UserEntity needDeleteUserEntity = userManager.queryByUid(needDeleteUid);
        fsBindManager.delFSBindByUid(needDeleteUid);
        userManager.deleteOldUserByUid(needDeleteUid);
        accountDAO.deleteByUid(needDeleteUid);
        cardDAO.deleteByUid(needDeleteUid);

        if (StringUtils.isNotBlank(needDeleteUserEntity.getOpenid()) || StringUtils.isNotBlank(needDeleteUserEntity.getWxUnionId())) {
            userManager.updateUserWxInfo(needRemainUid, needDeleteUserEntity.getOpenid(), needDeleteUserEntity.getWxUnionId());
        }
    }

    public void qywxUserBindFsUserFromVirtualUser(String ea, Integer virtualUserId, Integer fsUserId, String qywxUserId){
        if (QywxUserConstants.isFsUserId(fsUserId) && Objects.equals(virtualUserId, fsUserId)){
            //已经绑定纷享身份，解绑后又重新绑定，刷对象负责人身份
            ThreadPoolUtils.execute(() -> {
                wechatGroupUserObjDescribeManager.addTeamMemberByQywxBindCrmEvent(ea, qywxUserId, fsUserId);
                updateQywxObjectOwner(ea, qywxUserId, fsUserId);
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
            return;
        }

        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (qywxCorpAgentConfigEntity == null){
            return;
        }
        //更新拟表账号数据
        QywxVirtualFsUserEntity existFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(ea, fsUserId);
        if (existFsUserEntity != null){
            //纷享账号已经绑定 更新到新的corpId, qywxUserId
            existFsUserEntity.setCorpId(qywxCorpAgentConfigEntity.getCorpid());
            existFsUserEntity.setQyUserId(qywxUserId);

            //防止企业微信userid发生变化,先删除虚拟身份
            QywxVirtualFsUserEntity virtualEntity = qywxVirtualFsUserManager.getVirtualUserByQywxInfo(qywxCorpAgentConfigEntity.getCorpid(), qywxUserId, ea);
            if (virtualEntity != null && QywxUserConstants.isVirtualUserId(virtualEntity.getUserId())){
                qywxVirtualFsUserManager.deleteQywxVirtualFsUserById(virtualEntity.getId());
            }
            qywxVirtualFsUserManager.updateQywxUserIdAndCrmBindTimeById(existFsUserEntity, UserTypeEnum.QYWX);
            qywxVirtualFsUserManager.updateOldUserId(ea, existFsUserEntity.getId(), virtualUserId);
            //删除这个虚拟账号
            if (QywxUserConstants.isVirtualUserId(virtualUserId) ) {
                QywxVirtualFsUserEntity virtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(ea, virtualUserId);
                if (virtualFsUserEntity != null) {
                    qywxVirtualFsUserManager.deleteQywxVirtualFsUserById(virtualFsUserEntity.getId());
                }
            }
        }else {
            qywxVirtualFsUserManager.updateVirtualUserToFsUserId(ea, virtualUserId, fsUserId, qywxUserId, UserTypeEnum.QYWX);
        }

        //更新任务列表
        updateVirtualUserSpreadTask(ea, virtualUserId, fsUserId);
        //更新员工按天&营销活动统计的数据
        updateVirtualUserMarketingActivityDayStatistic(ea, virtualUserId, fsUserId);
        //更新员工按营销活动的统计数据
        updateVirtualListMarketingActivityEmployeeStatistic(ea, virtualUserId, fsUserId);
        updateVirtualUserFsBind(ea, virtualUserId, fsUserId);
        //合并虚拟用户的营销动态数据
        mergerUserMarketingActionStatistics(ea, virtualUserId, fsUserId);
        customizeFormDataUserDAO.updateFsUserIdByVirtrualUserId(ea, virtualUserId, fsUserId);
        //更新企业微信对象负责人
        if (StringUtils.isNotEmpty(qywxUserId)) {
            ThreadPoolUtils.execute(() -> {
                wechatGroupUserObjDescribeManager.addTeamMemberByQywxBindCrmEvent(ea, qywxUserId, fsUserId);
                updateQywxObjectOwner(ea, qywxUserId, fsUserId);
            }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
    }

    public void mergerUserMarketingActionStatistics(String ea, Integer virtualUserId, Integer fsUserId) {
        try {
            long beginTime = System.currentTimeMillis();
            log.info("开始合并营销动态数据, ea: {} virtualUserId: {} fsUseId: {}", ea, virtualUserId, fsUserId);
            userMarketingStatisticService.mergeUserMarketingStatisticByBingCrmEvent(ea, fsUserId, virtualUserId);
            log.info("开始合并营销动态数据成功, ea: {} virtualUserId: {} fsUseId: {} 耗时: {}", ea, virtualUserId, fsUserId, System.currentTimeMillis() - beginTime);
        } catch (Exception e) {
            log.error("mergerUserMarketingActionStatistics error, ea: {} virtualUserId: {} fsUseId: {}", ea, virtualUserId, fsUserId, e);
        }
    }

    private void updateVirtualUserFsBind(String ea, Integer virtualUserId, Integer fsUserId){
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        FSBindEntity virFsBindEntity = fsBindManager.queryByUserAndAppId(ea, virtualUserId, appId);
        if (virFsBindEntity == null){
            return;
        }

        FSBindEntity fsBindEntity = fsBindManager.queryByUserAndAppId(ea, fsUserId, appId);
        if (fsBindEntity == null){
            fsBindManager.updateFsBindFsUserId(fsUserId, virFsBindEntity.getUid());
            cardManager.correctCard(virFsBindEntity.getUid(), 1);
        }else {
            fsBindManager.delFSBindByUid(virFsBindEntity.getUid());
        }
    }

    public void updateVirtualUserMarketingActivityDayStatistic(String ea, Integer virtualUserId, Integer fsUserId){
       List<MarketingActivityEmployeeDayEntity> employeeDayEntityList = marketingActivityDayStatisticDAO.getByEaAndFsUserId(ea, virtualUserId);
       if (CollectionUtils.isEmpty(employeeDayEntityList)){
           return;
       }

       for (MarketingActivityEmployeeDayEntity virtualEntity : employeeDayEntityList){
           MarketingActivityEmployeeDayEntity employeeDayEntity = marketingActivityDayStatisticDAO.
                   getByMarketingActivityIdAndDate(ea, fsUserId, virtualEntity.getMarketingActivityId(), virtualEntity.getDate());
           if (employeeDayEntity == null){
               //更新
               marketingActivityDayStatisticDAO.updateFsUserIdById(ea, virtualEntity.getId(), fsUserId);
           }else {
               //合共后删除
               marketingActivityDayStatisticDAO.updateStatDataById(employeeDayEntity.getId(), employeeDayEntity.getLookUpCount() + virtualEntity.getLookUpCount(),
                       employeeDayEntity.getForwardCount() + virtualEntity.getForwardCount(), employeeDayEntity.getSpreadCount() + virtualEntity.getSpreadCount());
               marketingActivityDayStatisticDAO.deleteById(virtualEntity.getId());
           }
       }
    }

    public void updateVirtualListMarketingActivityEmployeeStatistic(String ea, Integer virtualUserId, Integer fsUserId){
        if (StringUtils.isEmpty(ea) || virtualUserId == null || fsUserId == null){
            return;
        }
        List<MarketingActivityEmployeeStatisticEntity> employeeStatisticEntityList = marketingActivityEmployeeStatisticDAO.queryByFsUserId(ea, virtualUserId);
        if (CollectionUtils.isEmpty(employeeStatisticEntityList)){
            return;
        }
        for (MarketingActivityEmployeeStatisticEntity virtualEmployeeStat : employeeStatisticEntityList){
            List<MarketingActivityEmployeeStatisticEntity> fsUserEmployeeStaList = marketingActivityEmployeeStatisticDAO.queryByMarketingActivityIds(virtualEmployeeStat.getEa(), fsUserId, Lists.newArrayList(virtualEmployeeStat.getMarketingActivityId()));
            MarketingActivityEmployeeStatisticEntity fsUserEmployeeEntity = CollectionUtils.isEmpty(fsUserEmployeeStaList) ? null : fsUserEmployeeStaList.get(0);
            if (fsUserEmployeeEntity == null){
                marketingActivityEmployeeStatisticDAO.updateUserIdById(virtualEmployeeStat.getId(), fsUserId);
            }else {
                marketingActivityEmployeeStatisticDAO.updateById(fsUserEmployeeEntity.getId(), fsUserEmployeeEntity.getEa(), fsUserEmployeeEntity.getSpreadCount() + virtualEmployeeStat.getSpreadCount(), fsUserEmployeeEntity.getForwardCount() + virtualEmployeeStat.getForwardCount(), fsUserEmployeeEntity.getLookUpCount() + virtualEmployeeStat.getLookUpCount());
                marketingActivityEmployeeStatisticDAO.deleteById(virtualEmployeeStat.getEa(), virtualEmployeeStat.getId());
            }
        }
    }

    public void updateVirtualUserSpreadTask(String ea, Integer virtualUserId, Integer fsUserId){
        if (StringUtils.isEmpty(ea) || virtualUserId == null || fsUserId == null){
            return;
        }

        if (!QywxUserConstants.isFsUserId(fsUserId) || !QywxUserConstants.isVirtualUserId(virtualUserId)){
            return;
        }

        List<SpreadTaskEntity>  spreadTaskEntityListByVirtualUser = spreadTaskDAO.querySpreadTaskList(ea, virtualUserId);
        if (CollectionUtils.isEmpty(spreadTaskEntityListByVirtualUser)){
            return;
        }
        for (SpreadTaskEntity spreadTaskEntity : spreadTaskEntityListByVirtualUser){
            SpreadTaskEntity spreadTaskEntityListByFsUser = spreadTaskDAO.getSpreadTaskEntity(spreadTaskEntity.getEa(), fsUserId, spreadTaskEntity.getMarketingActivityId());
            if (spreadTaskEntityListByFsUser != null){
                spreadTaskDAO.deleteById(spreadTaskEntity.getId(), spreadTaskEntity.getEa());
            }else {
                spreadTaskDAO.updateTaskUserById(spreadTaskEntity.getEa(),spreadTaskEntity.getId(), fsUserId);
               // spreadTaskDAO.updateTaskUserId(ea, virtualUserId, fsUserId);
            }
        }
    }

    /**
     * 转换旧userId(兼容迁移数据)
     * @param ea
     * @param userId
     * @return
     */
    public Integer convertOldUserId(String ea, Integer userId) {
        if (!QywxUserConstants.isVirtualUserId(userId) || StringUtils.isBlank(ea)) {
            return userId;
        }
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(ea, userId);
        if (qywxVirtualFsUserEntity == null) {
            return userId;
        }
        // 如果当前使用的是虚拟身份，且数据表中为纷享身份  oldUserId为当前使用的   userId分为虚拟身份和纷享身份
        if (!QywxUserConstants.isVirtualUserId(qywxVirtualFsUserEntity.getUserId())
            && qywxVirtualFsUserEntity.getOldUserId() != null
            && QywxUserConstants.isVirtualUserId(qywxVirtualFsUserEntity.getOldUserId())) {
            return qywxVirtualFsUserEntity.getUserId();
        }
        return userId;
    }

    /**
     * 根据企业微信部门id获取员工
     * 用getQywxUserIdByQywxDepartmentV2()这个方法
     */
    @Deprecated
    public List<String> getQywxUserIdByQywxDepartment(String ea, List<Integer> departmentIds) {
        List<String> userId = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(departmentIds)) {
            return userId;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.queryQywxStaff is null ea:{}", ea);
            return userId;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.queryQywxStaff accessToken is null ea:{}", ea);
            return userId;
        }
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.getStaffByDepartmentId(accessToken, departmentIds, true, true, ea);
        if (CollectionUtils.isEmpty(staffInfoList)) {
            return userId;
        }
        userId = staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
        return userId;
    }

    public List<String> getQywxUserIdByQywxDepartmentV2(String ea, List<Integer> departmentIds, boolean fetchChild) {
        List<String> userId = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(departmentIds)) {
            return userId;
        }
        if (fetchChild) {
            departmentIds = qywxDepartmentManager.getChildDepartmentList(ea, departmentIds, true);
        }
        if (CollectionUtils.isEmpty(departmentIds)) {
            return userId;
        }
        List<String> newDepartmentIdList = departmentIds.stream().map(String::valueOf).collect(Collectors.toList());
        return qyWxAddressBookDAO.queryUserIdByDepartmentIds(ea, newDepartmentIdList);
    }

    /**
     * 根据企业微信部门id获取员工
     */
    public List<String> getQywxUserIdByQywxDepartmentFormDB(String ea, List<Integer> departmentIds) {
        List<String> userId = Lists.newArrayList();
        if (StringUtils.isBlank(ea) || CollectionUtils.isEmpty(departmentIds)) {
            return userId;
        }
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            log.warn("QywxUserManager.queryQywxStaff is null ea:{}", ea);
            return userId;
        }
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            log.warn("QywxUserManager.queryQywxStaff accessToken is null ea:{}", ea);
            return userId;
        }
        List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.getStaffByDepartmentId(accessToken, departmentIds, true, true, ea);
        if (CollectionUtils.isEmpty(staffInfoList)) {
            return userId;
        }
        userId = staffInfoList.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
        return userId;
    }

    private void flushMarketingActivityStatData(String ea, Integer virtualUserId, Integer fsUserId){
        List<MarketingActivityEmployeeStatisticEntity> oldFsUserStat = marketingActivityEmployeeStatisticDAO.queryByFsUserId(ea, virtualUserId);
        List<MarketingActivityEmployeeStatisticEntity> newFsUserStat = marketingActivityEmployeeStatisticDAO.queryByFsUserId(ea, fsUserId);
        if (CollectionUtils.isEmpty(newFsUserStat) && CollectionUtils.isNotEmpty(oldFsUserStat)){
            for (MarketingActivityEmployeeStatisticEntity entity : oldFsUserStat){
                marketingActivityEmployeeStatisticDAO.replaceFsUserId(ea, entity.getId(), fsUserId);
            }
        }else if (CollectionUtils.isNotEmpty(newFsUserStat) && CollectionUtils.isNotEmpty(oldFsUserStat)) {
            for (MarketingActivityEmployeeStatisticEntity entity : oldFsUserStat){
                List<MarketingActivityEmployeeStatisticEntity> newEntityList = marketingActivityEmployeeStatisticDAO.listMarketingActivityEmployeeRankingByEmployeeIds(ea, entity.getMarketingActivityId(), Lists.newArrayList(fsUserId));
                if (CollectionUtils.isNotEmpty(newEntityList)){
                    MarketingActivityEmployeeStatisticEntity newEntity = newEntityList.get(0);
                    int forwardCount = newEntity.getForwardCount() == null ? 0 : newEntity.getForwardCount();
                    int lookupCount = newEntity.getLookUpCount() == null ? 0 : newEntity.getLookUpCount();
                    int spreadCount = newEntity.getSpreadCount() == null ? 0 : newEntity.getSpreadCount();

                    int virturalUserForwardCount =  entity.getForwardCount() == null ? 0 :  entity.getForwardCount();
                    int virturalUserLookUpCount = entity.getLookUpCount() == null ? 0 :entity.getLookUpCount();
                    int virturalSpreadCount = entity.getSpreadCount() == null ? 0 : entity.getSpreadCount();
                    newEntity.setForwardCount(forwardCount + virturalUserForwardCount);
                    newEntity.setLookUpCount(lookupCount + virturalUserLookUpCount);
                    newEntity.setSpreadCount(spreadCount + virturalSpreadCount);
                    marketingActivityEmployeeStatisticDAO.updateById(newEntity.getId(), ea, newEntity.getSpreadCount(), newEntity.getForwardCount(),newEntity.getLookUpCount());
                }else {
                    marketingActivityEmployeeStatisticDAO.replaceFsUserId(ea, entity.getId(), fsUserId);
                }


            }
        }
    }

    public void updateQywxObjectOwnerAsFsUser(String ea) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        PaasQueryArg externalUserQuery = new PaasQueryArg(0, 500);
        externalUserQuery.addFilter("owner", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("-10000"));
        queryFilterArg.setQuery(externalUserQuery);
        queryFilterArg.setSelectFields(Lists.newArrayList("_id"));
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, queryFilterArg);
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            int fsUserId = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea);
            batchUpdateOwnerByIds(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectDataInnerPage.getDataList(), fsUserId);
            for (ObjectData objectData : objectDataInnerPage.getDataList()) {
                crmV2Manager.editObjectDataOwnOrganization(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectData.getId(), fsUserId);
            }
        }
    }

    // 获取企微员工的部门， 子级部门在前，父级在后
    public List<Integer> getUserMainDepartmentsOrderly(String ea, String qywxUserId) {
        String accessToken = qywxManager.getAccessToken(ea);
        if (StringUtils.isBlank(accessToken)) {
            return Lists.newArrayList();
        }
        ObjectData qywxEmployeeObjetData = qywxEmployeeManager.findByQyUserId(ea, qywxUserId);
        if (qywxEmployeeObjetData == null || !qywxEmployeeObjetData.containsKey("main_department")) {
            return Lists.newArrayList();
        }
        int qywxDepartmentId = qywxEmployeeObjetData.getInt("main_department");
        DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
        if (departmentListResult == null || CollectionUtils.isEmpty(departmentListResult.getDepartmentList())) {
            return Lists.newArrayList(qywxDepartmentId);
        }
        List<Integer> departmentIds = Lists.newArrayList();
        Map<Integer, Department> departmentMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, Function.identity()));
        Department department = departmentMap.get(qywxDepartmentId);
        while (department != null) {
            departmentIds.add(department.getId());
            Integer parentId = department.getParentId();
            if (parentId == null) {
                break;
            }
            if (parentId != 1) {
                department = departmentMap.get(parentId);
            } else {
                department = null;
                departmentIds.add(1);
            }
        }
        return departmentIds;
    }
    @Data
    public class CardUserStatisticContainer implements Serializable {

        // 开启名片人数
        private Integer openCardUser;

        // 未开启名片人数
        private Integer notOpenCardUser;

        // 企业总人数
        private Integer totalUser;

        // 开通卡片详情
        private List<CardUserInfo> cardUserDetailList;
    }

    @Data
    public class CardUserInfo implements Serializable {

        private String userId;

        private String userName;

        private String department;

        private Integer isOpen;

        private Integer fsUserId;
    }
}
