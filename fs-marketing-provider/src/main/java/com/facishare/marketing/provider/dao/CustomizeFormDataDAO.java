package com.facishare.marketing.provider.dao;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.dto.ProductEntityDTO;
import com.facishare.marketing.provider.entity.CustomizeFormDataEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created  By zhoux 2019/04/04
 **/
public interface CustomizeFormDataDAO {

    @Insert("INSERT INTO customize_form_data(id, ea, type, form_usage, form_head_setting, form_body_setting, form_foot_setting, form_success_setting, form_more_setting,crm_form_field_map_v2, crm_pool_id, crm_api_name, crm_record_type, create_by, create_time, update_by, update_time, status, content_style,campaign_member_map, customize_apiname_mapping, member_to_form_mapping) VALUES (#{obj.id}, #{obj.ea}, #{obj.type}, #{obj.formUsage}, #{obj.formHeadSetting,typeHandler=FormHeadSettingTypeHandler}, #{obj.formBodySetting, typeHandler=FieldInfoListTypeHandler}, #{obj.formFootSetting, typeHandler=FormFootSettingTypeHandler}, #{obj.formSuccessSetting, typeHandler=FormSuccessSettingTypeHandler},#{obj.formMoreSetting, typeHandler=FormMoreSettingTypeHandler},#{obj.crmFormFieldMapV2, typeHandler=FieldMappingsTypeHandler}, #{obj.crmPoolId}, #{obj.crmApiName}, #{obj.crmRecordType}, #{obj.createBy}, now(), #{obj.updateBy}, now(), #{obj.status}, #{obj.contentStyle}, #{obj.campaignMemberMap, typeHandler=FieldMappingsTypeHandler}, #{obj.customizeApinameMapping, typeHandler=FlexibleJsonTypeHandler}, #{obj.memberToFormMapping, typeHandler=FieldMappingsTypeHandler});")
    int insertCustomizeFormData(@Param("obj") CustomizeFormDataEntity customizeFormDataEntity);


    @Update("update customize_form_data set form_head_setting = #{obj.formHeadSetting,typeHandler=FormHeadSettingTypeHandler}, "
        + "form_body_setting = #{obj.formBodySetting, typeHandler=FieldInfoListTypeHandler}, form_foot_setting = #{obj.formFootSetting, typeHandler=FormFootSettingTypeHandler}, " +
            "form_success_setting = #{obj.formSuccessSetting, typeHandler=FormSuccessSettingTypeHandler}, form_more_setting = #{obj.formMoreSetting, typeHandler=FormMoreSettingTypeHandler}, " +
            "crm_form_field_map_v2 = #{obj.crmFormFieldMapV2, typeHandler=FieldMappingsTypeHandler}, crm_pool_id =  #{obj.crmPoolId}, crm_api_name = #{obj.crmApiName}, crm_record_type = #{obj.crmRecordType}, " +
            "update_by =#{obj.updateBy}, update_time = now(),  content_style = #{obj.contentStyle}, campaign_member_map = #{obj.campaignMemberMap, typeHandler=FieldMappingsTypeHandler}, " +
            "customize_apiname_mapping=#{obj.customizeApinameMapping, typeHandler=FlexibleJsonTypeHandler}, member_to_form_mapping=#{obj.memberToFormMapping, typeHandler=FieldMappingsTypeHandler} " +
            "WHERE id = #{obj.id}")
    int updateCustomizeFormDataDetail(@Param("obj") CustomizeFormDataEntity customizeFormDataEntity);


    @Update("update customize_form_data set status = #{status}, update_by = #{fsUserId}, update_time = now() WHERE id = #{id}")
    int updateCustomizeFormDataStatus(@Param("id") String id, @Param("fsUserId") Integer fsUserId,  @Param("status") Integer status);

    @Update("<script>"
            + " update customize_form_data set status = #{status}, update_by = #{fsUserId}, update_time = now() WHERE id IN "
            +     "<foreach collection = 'idList' item = 'item' open = '(' separator = ',' close = ')'>"
            +     "#{item}"
            +     "</foreach>"
            + "</script>")
    int updateCustomizeFormDataStatusBatch(@Param("idList") List<String> idList, @Param("fsUserId") Integer fsUserId,  @Param("status") Integer status);

    @Select("<script>" +
            "      SELECT * from customize_form_data WHERE\n" +
            "      ea = #{ea}\n" +
            "      <if test='includeHexagonForm'>" +
            "      AND type IN  (1,2)\n"+
            "      </if>" +
            "      <if test='!includeHexagonForm'>" +
            "      AND type = 1\n"+
            "      </if>" +
            "      <if test=\"name != null\">\n" +
            "         AND form_head_setting ->> 'name' LIKE CONCAT('%', #{name}, '%')\n" +
            "      </if>\n" +
            "      <if test=\"status != null\">\n" +
            "         AND status = #{status}\n" +
            "      </if>\n" +
            "       AND status != 2\n" +
            "      ORDER BY create_time DESC" +
            "</script>")
    @FilterLog
    List<CustomizeFormDataEntity> queryCustomizeFormData(@Param("ea") String ea, @Param("name") String name, @Param("status") Integer status, @Param("includeHexagonForm") boolean includeHexagonForm, @Param("page") Page page);

    @Select("SELECT * FROM customize_form_data WHERE ea = #{ea} AND status != 2")
    List<CustomizeFormDataEntity> list(@Param("ea") String ea);

    @Select("SELECT * from customize_form_data WHERE type = 0 AND status = 0")
    List<CustomizeFormDataEntity> queryTemplateCustomizeFormData();


    @Select("SELECT * from customize_form_data WHERE id = #{id}")
    @FilterLog
    CustomizeFormDataEntity getCustomizeFormDataById(@Param("id") String id);

    @Select("SELECT * from customize_form_data WHERE id = #{id} and ea = #{ea}")
    CustomizeFormDataEntity getCustomizeFormDataByIdAndEa(@Param("id") String id, @Param("ea") String ea);

    @Select("<script>"
          + "SELECT * FROM customize_form_data WHERE status != 2 AND id IN"
          +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
          +       "#{item}"
          +   "</foreach>"
          + "</script>")
    @FilterLog
    List<CustomizeFormDataEntity> queryCustomizeFormDataEntityListByIds(@Param("ids")List<String> ids);

    @Update("UPDATE customize_form_data SET form_more_setting = #{obj.formMoreSetting, typeHandler=FormMoreSettingTypeHandler}, update_time = now() WHERE id=#{obj.id}")
    int updateFormMoreSetting(@Param("obj") CustomizeFormDataEntity customizeFormDataEntity);

    @Select("<script>"
        + "SELECT * FROM customize_form_data WHERE"
        + " <if test='ea!=null'>"
        + "  ea = #{ea} AND "
        + " </if>"
        + " type = #{type}"
        + "</script>")
    List<CustomizeFormDataEntity> queryCustomizeFormDataByType(@Param("type") Integer type, @Param("ea") String ea);

    @Update(" UPDATE customize_form_data SET content_style = #{contentStyle} WHERE id = #{id}")
    int updateCustomizeFormDataContentStyle(@Param("id") String id, @Param("contentStyle") String contentStyle);

    @Delete("DELETE FROM customize_form_data WHERE id = #{id}")
    int deleteCustomizeFormData(@Param("id") String id);

    @Select("SELECT * FROM customize_form_data WHERE ea = #{ea} AND type = #{type} AND status = 0 ORDER BY create_time LIMIT 1")
    CustomizeFormDataEntity getEaSignUpTemplate(@Param("ea") String ea, @Param("type") Integer type);

    @Insert("INSERT INTO customize_form_data(\"id\", ea, \"type\", form_usage, form_head_setting, form_body_setting, form_foot_setting, form_success_setting, form_more_setting,crm_form_field_map_v2, crm_pool_id, crm_api_name, crm_record_type, create_by, create_time, update_by, update_time, status, content_style, campaign_member_map, customize_apiname_mapping) "
        + "SELECT #{newId}, ea, \"type\", form_usage, form_head_setting, form_body_setting, form_foot_setting, form_success_setting, form_more_setting,crm_form_field_map_v2, crm_pool_id, crm_api_name, crm_record_type, #{newCreator}, NOW(), #{newCreator}, NOW(), status, content_style,campaign_member_map, customize_apiname_mapping, FROM customize_form_data WHERE \"id\" = #{oldId}")
    int copy(@Param("oldId") String oldId, @Param("newId") String newId, @Param("newCreator") int newCreator);

    @Select("SELECT COUNT(*) FROM customize_form_data WHERE ea=#{ea} AND form_head_setting->>'name' = #{name} AND type = 1 AND status IN(0, 1)")
    int queryCustomizeFormCountByName(@Param("ea")String ea, @Param("name")String name);

    @Select("select count(*) from customize_form_data where ea=#{ea} and form_head_setting->>'name' = #{name} AND type = 1 AND status = #{status}")
    int getEnableCustomizeFormByName(@Param("ea")String ea, @Param("status")int status, @Param("name")String name);

    @Select("<script>"
            + "select count(*) from ("
            + "SELECT customize_form_data.id FROM customize_form_data WHERE ea = #{ea} AND status != 2\n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 16)"
            + "UNION "
            + "SELECT customize_form_data.id FROM customize_form_data WHERE ea = #{ea} AND create_by = #{userId}  AND status != 2"
            + " ) ct"
            + "</script>")
    int queryUnGroupAndCreateByMeCount(@Param("ea") String ea,@Param("userId") int userId);

    @Select("SELECT COUNT(*) FROM customize_form_data WHERE ea = #{ea} AND create_by = #{userId}  AND status != 2")
    int queryCountCreateByMe(@Param("ea")String ea, @Param("userId")Integer userId);

    @Select("<script>"
            + "SELECT count(*) FROM customize_form_data WHERE ea= #{ea} AND status != 2\n"
            + "AND id NOT IN(SELECT object_id FROM object_group_relation WHERE ea = #{ea} AND object_type= 16)"
            + "</script>")
    int queryCountByUnGrouped(@Param("ea")String ea);

    @Select("<script>"
            + "SELECT COUNT(*) FROM ("
            + "SELECT customize_form_data.id FROM customize_form_data JOIN object_group_relation on  customize_form_data.ea = object_group_relation.ea AND customize_form_data.id = object_group_relation.object_id"
            + " WHERE customize_form_data.ea = #{ea} AND customize_form_data.status != 2\n"
            + " AND object_group_relation.group_id IN\n"
            + "<foreach collection = 'groupIdList' item = 'item' open = '(' separator = ',' close = ')'>"
            +   "#{item}"
            + "</foreach>"
            + "UNION"
            + " select customize_form_data.id from customize_form_data  where ea = #{ea} and status != 2 "
            + " and id not in (select object_id from object_group_relation where ea = #{ea} and object_type = 16)"
            + "UNION "
            + "SELECT customize_form_data.id FROM customize_form_data WHERE ea = #{ea} AND create_by = #{userId}  AND status != 2"
            + " ) hex"
            + "</script>")
    int queryAccessibleCount(@Param("ea") String ea, @Param("groupIdList") List<String> groupIdList, @Param("userId") int userId);


    @Select("<script>"
            + "SELECT * FROM customize_form_data WHERE id IN"
            +   "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
            +       "#{item}"
            +   "</foreach>"
            + "</script>")
    @FilterLog
    List<CustomizeFormDataEntity> queryCustomizeFormDataEntityByIds(@Param("ids")List<String> ids);
}
