package com.facishare.marketing.provider.outservice;

import com.facishare.marketing.api.data.ArticleData;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.outapi.result.MaterialWxPresentMsg;
import com.facishare.marketing.outapi.service.MaterialService;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.dao.PhotoDAO;
import com.facishare.marketing.provider.dao.ProductDAO;
import com.facishare.marketing.provider.dao.ShareContentDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dto.TargetPhotoPathDTO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.manager.FileManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("materialOutService")
public class MaterialServiceImpl implements MaterialService {
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileManager fileManager;
    @Autowired
    private PhotoDAO photoDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private ShareContentDAO shareContentDAO;

    @Value("${qywx.group.message.default.cover}")
    private String groupMessageDefaultCoverPath;

    @Override
    public Result<MaterialWxPresentMsg> getMaterialWxPresentMsg(String ea, Integer materialType, String materialId) {
        if(StringUtils.isEmpty(ea) || materialType == null || StringUtils.isEmpty(materialId)){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if(ObjectTypeEnum.ARTICLE.getType() == materialType){
            ArticleEntity article = articleDAO.getById(materialId);
            if(article == null){
                return Result.newError(SHErrorCode.MATERIAL_NOT_FOUND);
            }
            MaterialWxPresentMsg materialWxPresentMsg = new MaterialWxPresentMsg();
            materialWxPresentMsg.setTitle(article.getTitle());
            materialWxPresentMsg.setDescription(article.getSummary());
            PhotoEntity photoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.ARTICLE_SHARE_H5_COVER.getType(), article.getId());
            if(Objects.nonNull(photoEntity)){
                materialWxPresentMsg.setSharePicUrl(photoEntity.getThumbnailUrl());
            }
            if(StringUtils.isBlank(materialWxPresentMsg.getSharePicUrl())){
                String thumbnailAPath = photoManager.queryArticlePhotoUrl(article);
                if(!StringUtils.isEmpty(thumbnailAPath)){
                    String sharePicUrl = fileManager.getPictureShareUrl(ea, thumbnailAPath, false);
                    materialWxPresentMsg.setSharePicUrl(sharePicUrl);
                }
            }
            return Result.newSuccess(materialWxPresentMsg);
        }

        if(ObjectTypeEnum.PRODUCT.getType() == materialType){
            ProductEntity product = productDAO.getById(materialId);
            if(product == null){
                return Result.newError(SHErrorCode.MATERIAL_NOT_FOUND);
            }
            MaterialWxPresentMsg materialWxPresentMsg = new MaterialWxPresentMsg();
            materialWxPresentMsg.setTitle(product.getName());
            materialWxPresentMsg.setDescription(product.getSummary());
            PhotoEntity photoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.PRODUCT_SHARE_H5_COVER.getType(), product.getId());
            if(Objects.nonNull(photoEntity)){
                materialWxPresentMsg.setSharePicUrl(photoEntity.getThumbnailUrl());
            }
            if(StringUtils.isBlank(materialWxPresentMsg.getSharePicUrl())){
                List<PhotoEntity> photos = photoManager.listByProductIds(Collections.singletonList(materialId),ea);
                if (photos != null) {
                    for (PhotoEntity photo : photos) {
                        int targetType = photo.getTargetType();
                        if (targetType == PhotoTargetTypeEnum.PRODUCT_HEAD.getType() && !StringUtils.isEmpty(photo.getThumbnailPath())) {
                            String sharePicUrl = fileManager.getPictureShareUrl(ea, photo.getThumbnailPath(), false);
                            materialWxPresentMsg.setSharePicUrl(sharePicUrl);
                            break;
                        }
                    }
                }
            }
            return Result.newSuccess(materialWxPresentMsg);
        }

        if(ObjectTypeEnum.HEXAGON_SITE.getType() == materialType){
            MaterialWxPresentMsg materialWxPresentMsg = new MaterialWxPresentMsg();
            HexagonSiteEntity hexagonSite = hexagonSiteDAO.getById(materialId);
            if(hexagonSite == null){
                return Result.newError(SHErrorCode.MATERIAL_NOT_FOUND);
            }
            materialWxPresentMsg.setTitle(hexagonSite.getName());
            HexagonPageEntity hexagonPage = hexagonPageDAO.getHomePage(materialId);
            if (hexagonPage != null) {
                if(StringUtils.isNotEmpty(hexagonPage.getShareTitle())){
                    materialWxPresentMsg.setTitle(hexagonPage.getShareTitle());
                }
                if(hexagonPage.getShareDesc() != null){
                    materialWxPresentMsg.setDescription(hexagonPage.getShareDesc());
                }
            }
            PhotoEntity photoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), hexagonPage.getId());
            if(Objects.nonNull(photoEntity)){
                materialWxPresentMsg.setSharePicUrl(photoEntity.getThumbnailUrl());
            }
            if(StringUtils.isBlank(materialWxPresentMsg.getSharePicUrl())){
                List<HexagonSiteListDTO> coverMsgList = hexagonSiteDAO.getCoverBySiteIds(ImmutableList.of(materialId));
                List<String> coverAPathList = null;
                if (coverMsgList != null && !coverMsgList.isEmpty() && !Strings.isNullOrEmpty(coverMsgList.get(0).getSharePicH5Apath())) {
                    coverAPathList = coverMsgList.stream().map(HexagonSiteListDTO::getSharePicH5Apath).limit(1).collect(Collectors.toList());
                }
                if(coverAPathList == null || coverAPathList.isEmpty()){
                    coverAPathList = Lists.newArrayList(groupMessageDefaultCoverPath);
                }
                Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(coverAPathList, ea, false);
                if (urlMap != null && urlMap.get(coverAPathList.get(0)) != null) {
                    materialWxPresentMsg.setSharePicUrl(urlMap.get(coverAPathList.get(0)));
                }
            }
            return Result.newSuccess(materialWxPresentMsg);
        }

        if(ObjectTypeEnum.ACTIVITY.getType() == materialType) {
            MaterialWxPresentMsg materialWxPresentMsg = new MaterialWxPresentMsg();
            ActivityEntity conference = conferenceDAO.getConferenceById(materialId);
            if (conference == null) {
                return Result.newError(SHErrorCode.MATERIAL_NOT_FOUND);
            }
            ShareContentEntity shareContent = shareContentDAO.getDetailByObjectId(ea, materialId, ObjectTypeEnum.ACTIVITY.getType());
            if (shareContent != null){
                materialWxPresentMsg.setTitle(shareContent.getTitle());
                materialWxPresentMsg.setDescription(shareContent.getDescription());
                if (!Strings.isNullOrEmpty(shareContent.getImage())) {
                    String sharePicUrl = fileV2Manager.getUrlByPath(shareContent.getImage(), ea, false);
                    if (!Strings.isNullOrEmpty(sharePicUrl)){
                        materialWxPresentMsg.setSharePicUrl(sharePicUrl);
                    }
                }
            } else{
                //优先取报名页面的分享信息,没有则取会议的
                HexagonPageEntity hexagonPage = hexagonPageDAO.getHomePage(conference.getActivityDetailSiteId());
                materialWxPresentMsg.setTitle(StringUtils.isNotBlank(hexagonPage.getShareTitle())?hexagonPage.getShareTitle():conference.getTitle());
                materialWxPresentMsg.setDescription(hexagonPage.getShareDesc());
                PhotoEntity photoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), hexagonPage.getId());
                if(Objects.nonNull(photoEntity)){
                    materialWxPresentMsg.setSharePicUrl(photoEntity.getThumbnailUrl());
                }
                if(StringUtils.isBlank(materialWxPresentMsg.getSharePicUrl())){
                    PhotoEntity coverCutH5PhotoEntity = photoManager.querySingleCpathPhoto(PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType(), hexagonPage.getId());
                    if(coverCutH5PhotoEntity != null && StringUtils.isNotBlank(coverCutH5PhotoEntity.getThumbnailUrl())){
                        materialWxPresentMsg.setSharePicUrl(coverCutH5PhotoEntity.getThumbnailUrl());
                    } else {
                        List<TargetPhotoPathDTO> conferencePhotos = photoManager.listPathByTargetTypeAndIds(ImmutableList.of(materialId), PhotoTargetTypeEnum.ACTIVITY_COVER.getType());
                        if (conferencePhotos != null && !conferencePhotos.isEmpty() && !Strings.isNullOrEmpty(conferencePhotos.get(0).getPath())) {
                            String sharePicUrl = fileV2Manager.getUrlByPath(conferencePhotos.get(0).getPath(), ea, false);
                            if (!Strings.isNullOrEmpty(sharePicUrl)){
                                materialWxPresentMsg.setSharePicUrl(sharePicUrl);
                            }
                        }
                    }
                }
            }
            return Result.newSuccess(materialWxPresentMsg);
        }
        return Result.newError(SHErrorCode.PARAMS_ERROR);
    }
}
