package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.mankeep.api.result.BaseCardInfoResult;
import com.facishare.mankeep.api.result.QueryTradeListResult;
import com.facishare.mankeep.api.vo.AddCardFileVo;
import com.facishare.mankeep.api.vo.AddCardVO;
import com.facishare.mankeep.api.vo.QueryCardVO;
import com.facishare.mankeep.api.vo.trade.QueryTradeListVO;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.arg.minAppCardNavbar.GetMiniappCardSettingArg;
import com.facishare.marketing.api.arg.qywx.card.AddCardInfoArg;
import com.facishare.marketing.api.arg.qywx.card.QueryCardArg;
import com.facishare.marketing.api.arg.qywx.card.QueryCardHolderInfoVO;
import com.facishare.marketing.api.result.minAppCardNavbar.GetMiniappCardSettingResult;
import com.facishare.marketing.api.result.qywx.card.*;
import com.facishare.marketing.api.service.CustomizeMiniAppCardNavbarService;
import com.facishare.marketing.api.service.qywx.CardService;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.vo.UpdateEnterpriseDefaultProductVO;
import com.facishare.marketing.api.vo.qywx.QywxCreateAddFanQrCodeVO;
import com.facishare.marketing.common.contstant.MiniAppConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.businessCardHolder.BusinessCardHolderTypeEnum;
import com.facishare.marketing.common.enums.qywx.QywxFanQrCodeSourceEnum;
import com.facishare.marketing.common.enums.user.UserTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.card.EnterpriseDefaultCardDao;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxAddFanQrCodeEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.AccountManager;
import com.facishare.marketing.provider.manager.BusinessCardHolderManager;
import com.facishare.marketing.provider.manager.CardManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.PrivateMessageManager;
import com.facishare.marketing.provider.manager.qywx.QywxAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.github.mybatis.pagination.Page;
import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2020/01/14
 **/
@Slf4j
@Service("qywxCardServiceImpl")
public class CardServiceImpl implements CardService {

    @Autowired
    private com.facishare.mankeep.api.service.CardService cardService;

    @Autowired
    private AccountManager accountManager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private StaffMiniappUserBindDAO staffMiniappUserBindDAO;
    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private PrivateMessageManager privateMessageManager;

    @Autowired
    private BusinessCardHolderManager businessCardHolderManager;

    @Autowired
    private BusinessCardHolderDAO businessCardHolderDAO;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private CardManager cardManager;

    @Autowired
    private ProductDAO productDAO;

    @Autowired
    private CustomizeMiniappCardSettingDAO customizeMiniappCardSettingDAO;
    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private QYWXContactService qywxContactService;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private CardOwnerSettingDAO cardOwnerSettingDao;

    @Autowired
    private EnterpriseDefaultCardDao enterpriseDefaultCardDao;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private CustomizeMiniAppCardNavbarService customizeMiniAppCardNavbarService;

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    private static String color = "rgba(0, 102, 255, 0.4)-rgba(0, 102, 255, 0)-rgba(0, 50, 255, 1)";

    @Override
    public Result<CardInfoResult> queryMyBaseCardInfo(QueryCardArg arg) {
        cardManager.correctCard(arg.getCardUid(), 1);
        QueryCardVO queryCardVO = new QueryCardVO();
        queryCardVO.setCardUid(arg.getUid());
        queryCardVO.setUid(arg.getUid());
        queryCardVO.setNeedFeedMsg(arg.getNeedFeedMsg());
        queryCardVO.setNeedProductMsg(arg.getNeedProductMsg());
        queryCardVO.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<BaseCardInfoResult> modelResult = cardService.queryMyBaseCardInfo(queryCardVO);
        if (!modelResult.isSuccess() || modelResult.getData() == null) {
            log.warn("CardServiceImpl.queryMyBaseCardInfo error arg:{}, result:{}", arg, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        CardInfoResult result = BeanUtil.copy(modelResult.getData(), CardInfoResult.class);
        if (StringUtils.isNotBlank(result.getName())) {
            result.setName(UnicodeFormatter.decodeUnicodeString(result.getName()));
        }
        result.setMiniappOpenId(arg.getOpenid());
        //获取微页面名称
        if (StringUtils.isNotBlank(result.getCardHexagonId())) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(result.getCardHexagonId());
            if (hexagonSiteEntity != null) {
                result.setCardHexagonName(hexagonSiteEntity.getName());
            }
        }
        result.setCardVideoInfo(BeanUtil.copy(modelResult.getData().getCardVideoInfo(), CardVideoInfoResult.class));
        result.setCardTradeInfo(BeanUtil.copy(modelResult.getData().getCardTradeInfo(), CardTradeInfoResult.class));
        ThreadPoolUtils.execute(() -> {
            privateMessageManager.addFsMessageSettingData(arg.getUid());
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        result.setUid(arg.getCardUid());
        //获取设置的背景色
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getCardUid());
        if (fsBindEntity == null) {
            result.setBackgroundColor(color);
        } else {
            String fsEa = fsBindEntity.getFsEa();
            String cardTemplateIdByUid = cardManager.getCardTemplateIdByUid(arg.getCardUid());
            CustomizeMiniappCardSettingEntity cardSettingEntity = customizeMiniappCardSettingDAO.queryByEaAndTemplateId(fsEa, cardTemplateIdByUid);
            if (cardSettingEntity != null)  {
                result.setBackgroundColor(StringUtils.isNotBlank(cardSettingEntity.getBackgroundColor()) ? cardSettingEntity.getBackgroundColor() : color);
            }
        }
        //获取名片展示设置
        CardOwnerSettingEntity cardOwnerSettingEntity = cardOwnerSettingDao.queryCardSettingByUid(arg.getCardUid());
        //员工名片在物料上的展示,默认打开
        result.setCardDisplay(cardOwnerSettingEntity!= null && cardOwnerSettingEntity.getCardDisplay()!=null && !cardOwnerSettingEntity.getCardDisplay() ? false : true);
        String avatar = result.getAvatar();
        // 如果名片没有头像，去名片模板对应的默认头像
        if (StringUtils.isBlank(avatar)) {
            GetMiniappCardSettingArg getMiniappCardSettingArg = new GetMiniappCardSettingArg();
            getMiniappCardSettingArg.setCardUid(arg.getUid());
            getMiniappCardSettingArg.setEa(arg.getFsEa());
            Result<GetMiniappCardSettingResult> getMiniappCardSettingResult = customizeMiniAppCardNavbarService.getMiniappCardSetting(getMiniappCardSettingArg);
            if (getMiniappCardSettingResult.isSuccess() && getMiniappCardSettingResult.getData() != null) {
                result.setAvatar(getMiniappCardSettingResult.getData().getCardAvatarPath());
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<CardInfoResult> queryMyBaseCardInfoWithFsInfo(String ea, Integer fsUserId) {
        String uid = qywxUserManager.getUidByFsUserInfo(ea, fsUserId);
        if (StringUtils.isBlank(uid)) {
            log.warn("CardServiceImpl.queryMyBaseCardInfoWithFsInfo uid is null ea:{}, fsUserId:{}", ea, fsUserId);
            return Result.newError(SHErrorCode.FSBIND_USER_NOFOUND);
        }
        cardManager.correctCard(uid, 1);
        QueryCardVO queryCardVO = new QueryCardVO();
        queryCardVO.setCardUid(uid);
        queryCardVO.setUid(uid);
        queryCardVO.setNeedFeedMsg(false);
        queryCardVO.setNeedProductMsg(true);
        queryCardVO.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<BaseCardInfoResult> modelResult = cardService.queryMyBaseCardInfo(queryCardVO);
        if (!modelResult.isSuccess() || modelResult.getData() == null) {
            log.warn("CardServiceImpl.queryMyBaseCardInfoWithFsInfo error uid:{}, result:{}", uid, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        CardInfoResult result = BeanUtil.copy(modelResult.getData(), CardInfoResult.class);
        if (StringUtils.isNotBlank(result.getName())) {
            result.setName(UnicodeFormatter.decodeUnicodeString(result.getName()));
        }
        //获取微页面名称
        if (StringUtils.isNotBlank(result.getCardHexagonId())) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(result.getCardHexagonId());
            if (hexagonSiteEntity != null) {
                result.setCardHexagonName(hexagonSiteEntity.getName());
            }
        }
        result.setCardVideoInfo(BeanUtil.copy(modelResult.getData().getCardVideoInfo(), CardVideoInfoResult.class));
        result.setCardTradeInfo(BeanUtil.copy(modelResult.getData().getCardTradeInfo(), CardTradeInfoResult.class));
        // 图片
        ModelResult<com.facishare.mankeep.api.result.QueryPhotoListResult> photoModelResult = cardService.queryPhotoList(queryCardVO);
        if (!photoModelResult.isSuccess() || photoModelResult.getData() == null) {
            log.warn("CardServiceImpl.queryMyBaseCardInfoWithFsInfo queryPhotoList error uid:{}, result:{}", uid, photoModelResult);
            return Result.newError(photoModelResult.getErrCode(), photoModelResult.getErrMsg());
        }
        result.setPhotoList(BeanUtil.copy(photoModelResult.getData().getPhotoList(), PhotoListUnitResult.class));
        ThreadPoolUtils.execute(() -> {
            privateMessageManager.addFsMessageSettingData(uid);
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        return Result.newSuccess(result);
    }

    @Override
    public Result<CardInfoResult> queryBaseCardInfo(QueryCardArg arg) {
        QueryCardVO vo = BeanUtil.copy(arg, QueryCardVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<BaseCardInfoResult> modelResult = cardService.queryBaseCardInfo(vo);
        if (!modelResult.isSuccess() || modelResult.getData() == null) {
            log.warn("CardServiceImpl.queryBaseCardInfo error arg:{}, result:{}", arg, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        CardInfoResult result = BeanUtil.copy(modelResult.getData(), CardInfoResult.class);
        if (StringUtils.isNotBlank(result.getName())) {
            result.setName(UnicodeFormatter.decodeUnicodeString(result.getName()));
        }
        //获取微页面名称
        if (StringUtils.isNotBlank(result.getCardHexagonId())) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(result.getCardHexagonId());
            if (hexagonSiteEntity != null) {
                result.setCardHexagonName(hexagonSiteEntity.getName());
            }
        }
        result.setCardVideoInfo(BeanUtil.copy(modelResult.getData().getCardVideoInfo(), CardVideoInfoResult.class));
        result.setCardTradeInfo(BeanUtil.copy(modelResult.getData().getCardTradeInfo(), CardTradeInfoResult.class));
        ThreadPoolUtils.execute(() -> {
            // 增加名片夹数据
            if (StringUtils.isNotBlank(arg.getCardUid())) {
                businessCardHolderManager.addOrUpdateCardHolder(arg.getUid(), arg.getCardUid(), BusinessCardHolderTypeEnum.SEEN_CARD_HOLDER.getType());
                privateMessageManager.addFsMessageSettingData(vo.getCardUid());
            }
        }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
        //获取设置的背景色
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getCardUid());
        if (fsBindEntity == null) {
            result.setBackgroundColor(color);
        } else {
            String fsEa = fsBindEntity.getFsEa();
            String cardTemplateIdByUid = cardManager.getCardTemplateIdByUid(arg.getCardUid());
            CustomizeMiniappCardSettingEntity cardSettingEntity = customizeMiniappCardSettingDAO.queryByEaAndTemplateId(fsEa, cardTemplateIdByUid);
            if (cardSettingEntity != null)  {
                result.setBackgroundColor(StringUtils.isNotBlank(cardSettingEntity.getBackgroundColor()) ? cardSettingEntity.getBackgroundColor() : color);
            }
            // 根据企微员工ID查询员工活码
            QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(fsEa, fsBindEntity.getFsUserId());
            if (qywxVirtualFsUserEntity != null) {
                String qyUserId = qywxVirtualFsUserEntity.getQyUserId();
                QywxAddFanQrCodeEntity qywxAddFanQrCodeEntity = qywxAddFanQrCodeDAO.queryByUserId(fsEa, qyUserId);
                if (qywxAddFanQrCodeEntity != null) {
                    result.setQrCodeUrl(qywxAddFanQrCodeEntity.getQrCodeUrl());
                } else {
                    QyWxAddressBookEntity qyWxAddressBookEntity = qywxAddressBookManager.queryByEaAndUserId(fsEa, qyUserId);
                    if (qyWxAddressBookEntity != null) {
                        String qyUserName = qyWxAddressBookEntity.getName();
                        result.setQrCodeUrl(this.createPrivateQrCode(fsEa, qyUserId, qyUserName));
                    }
                }
            }
        }
        // 填充员工名片对应的员工类型
        result.setUserType(fillUserType(arg.getCardUid()));
        //获取名片展示设置
        CardOwnerSettingEntity cardOwnerSettingEntity = cardOwnerSettingDao.queryCardSettingByUid(arg.getCardUid());
        //员工名片在物料上的展示,默认打开
        result.setCardDisplay(cardOwnerSettingEntity!= null && cardOwnerSettingEntity.getCardDisplay()!=null && !cardOwnerSettingEntity.getCardDisplay() ? false : true);
        return Result.newSuccess(result);
    }

    private String fillUserType(String cardUid) {
        UserRelationEntity userRelationEntity = userRelationManager.getByUid(cardUid);
        if (userRelationEntity == null) {
            return null;
        }
        String userType = null;
        Integer fsUserId = userRelationEntity.getFsUserId();
        if (QywxUserConstants.isFsUserId(fsUserId)) {
            userType = UserTypeEnum.CRM.getCode();
        } else if (QywxUserConstants.isMemberVirtualUserId(fsUserId)) {
            String memberType = memberMarketingManager.getMemberType(userRelationEntity.getEa(), fsUserId);
            if (MemberTypeEnum.PARTNER.getType().equals(memberType)) {
                userType = UserTypeEnum.PARTNER_MEMBER.getCode();
            } else if (MemberTypeEnum.EMPLOYEE.getType().equals(memberType)) {
                userType = UserTypeEnum.EMPLOYEE_MEMBER.getCode();
            } else if (MemberTypeEnum.SPREAD_EMPLOYEE.getType().equals(memberType)) {
                userType = UserTypeEnum.SPREAD_MEMBER.getCode();
            }
        } else if (QywxUserConstants.isPartnerVirtualUserId(fsUserId)) {
            userType = UserTypeEnum.PARTNER.getCode();
        }
        return userType;
    }

    private String createPrivateQrCode(String ea, String qyUserId, String qyUserName) {
        QywxCreateAddFanQrCodeVO createAddFanQrCodeVO = new QywxCreateAddFanQrCodeVO();
        String name = I18nUtil.get(I18nKeyEnum.MARK_QYWX_CARDSERVICEIMPL_342);
        if (qyUserName != null) {
            name = qyUserName + name;
        }
        createAddFanQrCodeVO.setQrCodeName(name);
        createAddFanQrCodeVO.setType(1);
        createAddFanQrCodeVO.setUserId(Collections.singletonList(qyUserId));
        createAddFanQrCodeVO.setSkipVerify(0);
        //createAddFanQrCodeVO.setWelcomeContent("你好你好");
        createAddFanQrCodeVO.setEa(ea);
        createAddFanQrCodeVO.setChannelValue("qywx");
        createAddFanQrCodeVO.setSource(QywxFanQrCodeSourceEnum.CREATE_BY_CARD.getSource());
        createAddFanQrCodeVO.setFromCard(true);
        Result<String> addfanQrCode = qywxContactService.createAddfanQrCode(createAddFanQrCodeVO);
        if (addfanQrCode != null && addfanQrCode.isSuccess() && StringUtils.isNotEmpty(addfanQrCode.getData())) {
            return addfanQrCode.getData();
        }
        return "";
    }

    @Override
    public Result<QueryProductsAndFeedResult> queryProductsAndFeed(QueryCardArg arg) {
        QueryCardVO vo = BeanUtil.copy(arg, QueryCardVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<com.facishare.mankeep.api.result.QueryProductsAndFeedResult> modelResult = cardService.queryProductsAndFeed(vo);
        if (!modelResult.isSuccess() || modelResult.getData() == null) {
            log.warn("CardServiceImpl.queryProductsAndFeed error arg:{}, result:{}", arg, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        QueryProductsAndFeedResult result = new QueryProductsAndFeedResult();
        result.setProductList(BeanUtil.copy(modelResult.getData().getProductList(), ProductListUnitResult.class));
        result.setLastFeed(new FeedDetailResult());
        if (modelResult.getData().getLastFeed() != null) {
            result.setLastFeed(BeanUtil.copy(modelResult.getData().getLastFeed(), FeedDetailResult.class));
            if (CollectionUtils.isNotEmpty(modelResult.getData().getLastFeed().getFeedCommentUnitResults())) {
                result.getLastFeed().setFeedCommentUnitResults(BeanUtil.copy(modelResult.getData().getLastFeed().getFeedCommentUnitResults(), FeedCommentUnitResult.class));
            }
            result.getLastFeed().setFeedActivityVO(BeanUtil.copy(modelResult.getData().getLastFeed().getFeedActivityVO(), FeedActivityVO.class));
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryPhotoListResult> queryPhotoList(QueryCardArg arg) {
        QueryCardVO vo = BeanUtil.copy(arg, QueryCardVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<com.facishare.mankeep.api.result.QueryPhotoListResult> modelResult = cardService.queryPhotoList(vo);
        if (!modelResult.isSuccess() || modelResult.getData() == null) {
            log.warn("CardServiceImpl.queryPhotoList error arg:{}, result:{}", arg, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        QueryPhotoListResult result =  new QueryPhotoListResult();
        result.setPhotoList(BeanUtil.copy(modelResult.getData().getPhotoList(), PhotoListUnitResult.class));
        return Result.newSuccess(result);
    }

    @Override
    public Result<QueryCardResult> queryCardInfo(QueryCardArg arg) {
        QueryCardVO vo = BeanUtil.copy(arg, QueryCardVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<com.facishare.mankeep.api.result.QueryCardResult> modelResult = cardService.queryCardInfo(vo);
        if (!modelResult.isSuccess()) {
            log.warn("CardServiceImpl.queryCardInfo error arg:{}, result:{}", arg, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        QueryCardResult result = BeanUtil.copy(modelResult.getData(), QueryCardResult.class);
        if (StringUtils.isNotBlank(result.getName())) {
            result.setName(UnicodeFormatter.decodeUnicodeString(result.getName()));
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<AddCardResult> addCardInfo(AddCardInfoArg arg) {
        resetAddCardInfo(arg);
        AddCardVO vo = BeanUtil.copy(arg, AddCardVO.class);
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        vo.setAddCardFileList(BeanUtil.copy(arg.getAddCardFileList(), AddCardFileVo.class));
        if (CollectionUtils.isNotEmpty(vo.getPhotoList())) {
            vo.setPhotoList(vo.getPhotoList().stream().parallel().map(e -> {
                if (e.startsWith("C_")) {
                    FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getUid());
                    String ea = null;
                    if (fsBindEntity != null) {
                        ea = fsBindEntity.getFsEa();
                    }
                    e = fileV2Manager.uploadToApath(fileV2Manager.downloadFileByUrl(fileV2Manager.getUrlByPath(ea, e), null), "jpg", ea);
                }
                return e;
            }).collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(arg.getAvatar())) {
                if (arg.getAvatar().startsWith("C_")) {
                    FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getUid());
                    String ea = null;
                    if (fsBindEntity != null) {
                        ea = fsBindEntity.getFsEa();
                    }
                    vo.setAvatar(fileV2Manager.uploadToTApath(fileV2Manager.downloadFileByUrl(fileV2Manager.getUrlByPath(ea, arg.getAvatar()), null), "jpg", null,null));
                }
        }
        ModelResult<com.facishare.mankeep.api.result.AddCardResult> modelResult = cardService.addCardInfo(vo);
        if (!modelResult.isSuccess()) {
            log.warn("CardServiceImpl.addCardInfo error arg:{}, result:{}", arg, modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        AddCardResult result = new AddCardResult();
        //名片展示设置
        if(arg.getCardDisplay()!=null){
            CardOwnerSettingEntity cardOwnerSettingEntity = cardOwnerSettingDao.queryCardSettingByUid(modelResult.getData().getCardUid());
            if(cardOwnerSettingEntity==null){
                CardOwnerSettingEntity entity = new CardOwnerSettingEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setUid(modelResult.getData().getCardUid());
                entity.setCardDisplay(arg.getCardDisplay());
                cardOwnerSettingDao.insert(entity);
            }else {
                cardOwnerSettingDao.updateByUid(modelResult.getData().getCardUid(),arg.getCardDisplay());
            }
        }
        result.setCardUid(modelResult.getData().getCardUid());
        ThreadPoolUtils.execute(() -> {
            List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.CARD_QRCODE.getType(), arg.getUid());
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getUid());
            if (CollectionUtils.isEmpty(photoEntityList) && fsBindEntity != null) {
                accountManager.createCardQRCode(arg.getUid(), fsBindEntity.getFsEa());
            }
        }, ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return Result.newSuccess(result);
    }

    @Override
    public Result<TradeListResult> queryTradeList() {
        QueryTradeListVO vo = new QueryTradeListVO();
        vo.setMiniAppVersion(MiniAppConstants.MINIAPP_VERSION);
        ModelResult<QueryTradeListResult> modelResult = cardService.queryTradeList(vo);
        if (!modelResult.isSuccess() || modelResult.getData() == null) {
            log.warn("CardServiceImpl.queryTradeList error result:{}", modelResult);
            return Result.newError(modelResult.getErrCode(), modelResult.getErrMsg());
        }
        TradeListResult result = new TradeListResult();
        result.setQueryTradeList(BeanUtil.copy(modelResult.getData().getQueryTradeList(), TradeListResult.QueryTrade.class));
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<QueryCardHolderInfoResult>> queryCardHolderInfo(QueryCardHolderInfoVO vo) {
        List<QueryCardHolderInfoResult> queryCardHolderInfoResultList = Lists.newArrayList();
        PageResult<QueryCardHolderInfoResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(queryCardHolderInfoResultList);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        // 查询名片夹信息
        List<BusinessCardHolderEntity> businessCardHolderEntityList = businessCardHolderDAO.queryBusinessCardHolderByUid(vo.getName(), vo.getUid(), BusinessCardHolderTypeEnum.SEEN_CARD_HOLDER.getType(), page);
        if (CollectionUtils.isEmpty(businessCardHolderEntityList)) {
            return Result.newSuccess(pageResult);
        }
        List<String> friendUid = businessCardHolderEntityList.stream().map(BusinessCardHolderEntity::getFriendUid).collect(Collectors.toList());
        List<CardEntity> cardEntityList = cardDAO.listByUids(friendUid);
        pageResult.setTotalCount(page.getTotalNum());
        for (CardEntity cardEntity : cardEntityList) {
            cardManager.resetCardInfoByVisualRange(cardEntity);
            QueryCardHolderInfoResult queryCardHolderInfoResult = new QueryCardHolderInfoResult();
            queryCardHolderInfoResult.setId(cardEntity.getId());
            queryCardHolderInfoResult.setUid(cardEntity.getUid());
            queryCardHolderInfoResult.setName(cardEntity.getName());
            queryCardHolderInfoResult.setVocation(cardEntity.getVocation());
            queryCardHolderInfoResult.setCompanyName(cardEntity.getCompanyName());
            queryCardHolderInfoResult.setPhone(cardEntity.getPhone());
            queryCardHolderInfoResult.setEmail(cardEntity.getEmail());
            queryCardHolderInfoResult.setGender(cardEntity.getGender());
            queryCardHolderInfoResult.setVisualRange(cardEntity.getVisualRangeString());
            queryCardHolderInfoResult.setAvatar(cardEntity.getAvatar());
            queryCardHolderInfoResultList.add(queryCardHolderInfoResult);
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result updateEnterpriseDefaultProduct(UpdateEnterpriseDefaultProductVO vo) {
        productDAO.updateAllProductEnterpriseDefault(EnterpriseDefaultIsDefaultEnum.FALSE.getIsEnterpriseDefault(), vo.getEa());
        if (CollectionUtils.isNotEmpty(vo.getProductIds())) {
            List<String> productIds = vo.getProductIds();
            Date date = new Date();
            for (int i = 0; i < productIds.size(); i++) {
                productDAO.updateProductEnterpriseDefaultById(productIds.get(i), EnterpriseDefaultIsDefaultEnum.TRUE.getIsEnterpriseDefault(), DateUtil.minusSecond(date, i));
            }
        }
        return Result.newSuccess();
    }

    public void resetAddCardInfo(AddCardInfoArg arg) {
        if (StringUtils.isBlank(arg.getDepartment())) {
            arg.setDepartment("");
        }
        if (StringUtils.isBlank(arg.getEmail())) {
            arg.setEmail("");
        }
        if (StringUtils.isBlank(arg.getCompanyAddress())) {
            arg.setCompanyAddress("");
        }
        if (StringUtils.isBlank(arg.getWechat())) {
            arg.setWechat("");
        }
        if (StringUtils.isBlank(arg.getIntroduction())) {
            arg.setIntroduction("");
        }
    }

    @Override
    public Result<List<String>> changePhotosType(List<String> photos,String ea) {
        List<String> newPhotos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(photos)) {
            List<String> collect = photos.stream().parallel().map(e -> {
                if (e.startsWith("C_")) {
                    e = fileV2Manager.uploadToApath(fileV2Manager.downloadFileByUrl(fileV2Manager.getUrlByPath(ea, e), null), "jpg", ea);
                }
                return e;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                newPhotos.addAll(collect);
            }
        }
        return Result.newSuccess(newPhotos);
    }

}