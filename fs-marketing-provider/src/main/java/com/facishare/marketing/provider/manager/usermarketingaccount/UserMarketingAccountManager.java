/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.usermarketingaccount;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.common.enums.ActionTypeEnum;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.data.SpreadChannelOptionData;
import com.facishare.marketing.api.data.WeChatAvatarData;
import com.facishare.marketing.api.data.crmData.*;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.data.usermarketingaccount.MiniappData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.data.usermarketingaccount.WxServiceData;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactListResult;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.contstant.*;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.typehandlers.value.*;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.OrderBy;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.live.ChannelsAccountDAO;
import com.facishare.marketing.provider.dao.live.LiveUserStatusDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.marketingUserGroup.UserMarketingExcludeObjectDAO;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.dao.member.MemberAccessibleCampaignDAO;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dto.CommonObjectDTO;
import com.facishare.marketing.provider.dto.ObjectIdWithMarketingUserIdAndPhoneDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.member.MemberAccessibleCampaignEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerData.ObjectDataIdAndTagNameListData;
import com.facishare.marketing.provider.innerData.ObjectIdAndDescribeData;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxAddressBookManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.CrmObjectResult;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.statistic.common.model.Properties;
import com.facishare.marketing.statistic.outapi.result.UserMarketingActionStatisticResult;
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.wechat.union.core.api.model.result.OuterServiceResult;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.data.Wheres;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.limit.GuavaLimiter;
import com.github.trace.TraceContext;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.*;

import java.util.*;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.facishare.marketing.provider.manager.AuthManager.defaultAllDepartment;
import static com.facishare.marketing.provider.manager.permission.DataPermissionManager.defaultAllChannel;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 28/03/2019
 */
@Component
@Slf4j
public class UserMarketingAccountManager {
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private CardDAO cardDao;
    @Autowired
    private FillCrmDataManager fillCrmDataManager;
    @Autowired
    private UserMarketingExcludeObjectDAO marketingUserGroupExcludeObjectDAO;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingCrmCustomizeObjectRelationDao userMarketingCrmCustomizeObjectRelationDao;
    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private MarketingEnterpriseCommonSettingDAO marketingEnterpriseCommonSettingDao;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private OuterServiceWechatManager outerServiceWechatManager;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserMarketingTagService userMarketingTagService;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private OfficialWebsiteManager officialWebsiteManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MemberAccessibleCampaignDAO memberAccessibleCampaignDAO;
    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao marketingUserGroupCustomizeObjectMappingDao;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private UserTagDao userTagDao;
    @Autowired
    private TagModelDao tagModelDao;

    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private UserMarketingStatisticService userMarketingStatisticService;

    @Autowired
    private UserMarketingCustomizeActionDAO userMarketingCustomizeActionDAO;

    @Autowired
    private MarketingGroupUserActionDAO marketingGroupUserActionDAO;

    @Autowired
    private SpreadChannelManager spreadChannelManager;

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private ChannelsAccountDAO channelsAccountDAO;

    @Autowired
    private MarketingActivityManager marketingActivityManager;

    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;

    @Autowired
    private FileLibraryDAO fileLibraryDAO;

    @Autowired
    private VideoDAO videoDAO;
    @Autowired
    private TagModelUserTagRelationDao tagModelUserTagRelationDao;

    @Autowired
    private DepartmentProviderService departmentProviderService;
    @Autowired
    private MarketingEnterpriseCommonSettingManager marketingEnterpriseCommonSettingManager;

    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;

    @Autowired
    private UserDAO userDAO;

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;

    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    private static final int BATCH_GET_SIZE = 1000;
    private static final int CRM_NO_PERMISSION_ERROR_CODE = *********;
    public static final String CRM_OBJ_QUERY_RATE_LIMIT_KEY = "limit-yxt-obj-query-init";

    private static final List<String> CONTACT_SELECT_FIELDS = Lists.newArrayList(CrmContactFieldEnum.ID.getFieldName(), CrmContactFieldEnum.TENANT_ID.getFieldName(),
            CrmContactFieldEnum.NAME.getFieldName(), CrmContactFieldEnum.ADD.getFieldName(), CrmContactFieldEnum.MOBILE.getFieldName(),
            CrmContactFieldEnum.ACCOUNT_ID.getFieldName(), CrmContactFieldEnum.ACCOUNT_ID.getFieldName() + "__r", CrmContactFieldEnum.COMPANY.getFieldName(),
            CrmContactFieldEnum.GENDER.getFieldName(), CrmContactFieldEnum.DEPARTMENT.getFieldName(), CrmContactFieldEnum.EMAIL.getFieldName(), CrmContactFieldEnum.REMARK.getFieldName(),
            CrmContactFieldEnum.TEL.getFieldName(), CrmContactFieldEnum.JOB_TITLE.getFieldName(), CrmContactFieldEnum.OWNER.getFieldName());

    private static final List<String> CUSTOMER_SELECT_FIELDS = Lists.newArrayList(CrmCustomerFieldEnum.ID.getFieldName(), CrmCustomerFieldEnum.TENANT_ID.getFieldName(), CrmCustomerFieldEnum.NAME.getFieldName(),
            CrmCustomerFieldEnum.ADD.getFieldName(), CrmCustomerFieldEnum.TEL.getFieldName(), CrmCustomerFieldEnum.EMAIL.getFieldName(), CrmCustomerFieldEnum.REMARK.getFieldName(),
            CrmCustomerFieldEnum.TEL.getFieldName(), CrmCustomerFieldEnum.OWNER.getFieldName());

    private static final List<String> LEAD_SELECT_FIELDS = Lists.newArrayList(CrmLeadFieldEnum.ID.getFieldName(), CrmLeadFieldEnum.TENANT_ID.getFieldName(), CrmLeadFieldEnum.NAME.getFieldName(),
            CrmLeadFieldEnum.ADDRESS.getFieldName(), CrmLeadFieldEnum.MOBILE.getFieldName(), CrmLeadFieldEnum.COMPANY.getFieldName(), CrmLeadFieldEnum.DEPARTMENT.getFieldName(),
            CrmLeadFieldEnum.EMAIL.getFieldName(), CrmLeadFieldEnum.REMARK.getFieldName(), CrmLeadFieldEnum.SALES_CLUE_POOL_ID.getFieldName(), CrmLeadFieldEnum.TEL.getFieldName(),
            CrmLeadFieldEnum.JOB_TITLE.getFieldName(), CrmLeadFieldEnum.OWNER.getFieldName(), CrmLeadFieldEnum.COLLECTED_TO.getFieldName());

    private static final List<String> MEMBER_SELECT_FIELDS = Lists.newArrayList(CrmMemberFieldEnum.ID.getApiName(), CrmMemberFieldEnum.NAME.getApiName(), CrmMemberFieldEnum.GRADE_ID.getApiName(),
            CrmMemberFieldEnum.GROWTH_VALUE.getApiName(), CrmMemberFieldEnum.INTEGRAL_VALUE.getApiName(), CrmMemberFieldEnum.PHONE.getApiName(), CrmMemberFieldEnum.EMAIL.getApiName(),
            CrmMemberFieldEnum.AVATAR.getApiName());

    private static final List<String> WX_EXTERNAL_USER_SELECT_FIELDS = Lists.newArrayList(CrmWechatWorkExternalUserFieldEnum.ID.getFieldName(), CrmWechatWorkExternalUserFieldEnum.NAME.getFieldName(),
            CrmWechatWorkExternalUserFieldEnum.ENTERPRISE_NAME.getFieldName(), CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName(), CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName());

    private static final List<String> WECHAT_SELECT_FIELDS = Lists.newArrayList(CrmCustomerFieldEnum.ID.getFieldName(), CrmCustomerFieldEnum.TENANT_ID.getFieldName(), CrmCustomerFieldEnum.NAME.getFieldName(),
            CrmWechatFanFieldEnum.ADDRESS.getFieldName(), CrmWechatFanFieldEnum.PHONE.getFieldName(), CrmWechatFanFieldEnum.ACCOUNT_ID.getFieldName(),
            CrmWechatFanFieldEnum.REMARK.getFieldName(), CrmWechatFanFieldEnum.AREA.getFieldName(), CrmWechatFanFieldEnum.WX_HEAD_IMAGE.getFieldName(),
            CrmWechatFanFieldEnum.USERNAME.getFieldName(), CrmWechatFanFieldEnum.OWNER.getFieldName(), CrmWechatFanFieldEnum.WX_APP_ID.getFieldName());

    public static final Set<String> FIELD_NAMES_REQUIRED_TO_ASSOCIATION = ImmutableSet.of(
            ObjectDescribeContants.DESCRIBE_API_NAME, ObjectDescribeContants.ID,
            CrmLeadFieldEnum.MOBILE.getFieldName(), CrmLeadFieldEnum.TEL.getFieldName(),
            CrmCustomerFieldEnum.TEL.getFieldName(),
            CrmContactFieldEnum.MOBILE.getFieldName(), CrmContactFieldEnum.TEL.getFieldName(),
            CrmWechatFanFieldEnum.PHONE.getFieldName(),
            CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName(), CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName(), CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName(),
            "name","email", CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(), CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName(), "leads_id");

    private static final List<String> NAME_BLACK_LIST = Lists.newArrayList("暂无", "无", "微信用户", "会员", "测试", "test", "Test");

    /**
     * 根据参会人员id查找营销用户id
     * @param ea
     * @param campaignIds
     * @param filter 营销用户过滤条件, 无需过滤为null
     * @return 返回参会人员ID对应的营销用户Id的map   campaignId2UserMarketingAccountEntityMap
     */
    public Optional<Map<String, String>> getMarketingAccountIdsByCampaignIds(String ea, Collection<String> campaignIds, Predicate<UserMarketingAccountEntity> filter) {
        if (StringUtils.isEmpty(ea) || CollectionUtils.isEmpty(campaignIds)) {
            log.warn("UserMarketingAccountManager.getMarketingAccountEntityByCampaignIds ea:{} campaignIds:{}", ea, campaignIds);
            return Optional.empty();
        }
        filter = (filter == null ? userMarketingAccountEntity -> true : filter);
        Map<String, String> campaignId2UserMarketingAccountIDMap = new HashMap<>(campaignIds.size());
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByIds(campaignIds);
        /** 根据手机查找营销用户 */
        Map<String, String> campaignId2Phone = campaignMergeDataEntities.stream().filter(CampaignMergeDataEntity::phoneNotNull).collect(Collectors.toMap(CampaignMergeDataEntity::getId, CampaignMergeDataEntity::getPhone, (v1, v2) -> v1));
        if (MapUtils.isNotEmpty(campaignId2Phone)) {
            List<UserMarketingAccountEntity> userMarketingAccountByPhones = userMarketingAccountDAO.getUserMarketingAccountByPhones(ei, campaignId2Phone.values());
            Map<String, String> phone2UserMarketingAccountIdMap =  userMarketingAccountByPhones.stream().filter(filter).collect(Collectors.toMap(UserMarketingAccountEntity::getPhone, UserMarketingAccountEntity::getId, (v1, v2)->v1));
            if (MapUtils.isNotEmpty(phone2UserMarketingAccountIdMap)) {
                for (Map.Entry<String, String> campaignId2PhoneEntry : campaignId2Phone.entrySet()) {
                    campaignId2UserMarketingAccountIDMap.put(campaignId2PhoneEntry.getKey(), phone2UserMarketingAccountIdMap.get(campaignId2PhoneEntry.getValue()));
                }
            }
        }
        /** 根据关联对象查找营销用户 */
        Map<String, String> campaignId2LeadId = campaignMergeDataEntities.stream()
                .filter(CampaignMergeDataEntity::bindCrmObjectTypeAndBindCrmObjectIdValid)
                .filter(ele -> CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType().equals(ele.getBindCrmObjectType()))
                .collect(Collectors.toMap(CampaignMergeDataEntity::getId, CampaignMergeDataEntity::getBindCrmObjectId, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(campaignId2LeadId.values())) {
            List<UserMarketingCrmLeadAccountRelationEntity> userMarketingCrmLeadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao.batchGetByEaAndCrmLeadIds(ea, campaignId2LeadId.values());
            Map<String, String> leadId2UserMarketingAccountId = userMarketingCrmLeadAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId, UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId, (v1, v2)->v1));
            for (Map.Entry<String, String> entry : campaignId2LeadId.entrySet()) {
                campaignId2UserMarketingAccountIDMap.putIfAbsent(entry.getKey(), leadId2UserMarketingAccountId.get(entry.getValue()));
            }
        }


        Map<String, String> campaignId2ContactId = campaignMergeDataEntities.stream()
                .filter(CampaignMergeDataEntity::bindCrmObjectTypeAndBindCrmObjectIdValid)
                .filter(ele -> CampaignMergeDataObjectTypeEnum.CONTACT_OBJ.getType().equals(ele.getBindCrmObjectType()))
                .collect(Collectors.toMap(CampaignMergeDataEntity::getId, CampaignMergeDataEntity::getBindCrmObjectId, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(campaignId2ContactId.values())) {
            List<UserMarketingCrmContactAccountRelationEntity> userMarketingCrmContactAccountRelationEntities = userMarketingCrmContactAccountRelationDao.listUserMarketingCrmContactAccountRelationEntityByContactIds(ea, campaignId2ContactId.values());
            Map<String, String> contactId2UserMarketingAccountId = userMarketingCrmContactAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmContactAccountRelationEntity::getCrmContactId, UserMarketingCrmContactAccountRelationEntity::getUserMarketingId, (v1, v2)->v1));
            for (Map.Entry<String, String> entry : campaignId2ContactId.entrySet()) {
                campaignId2UserMarketingAccountIDMap.putIfAbsent(entry.getKey(), contactId2UserMarketingAccountId.get(entry.getValue()));
            }
        }

        Map<String, String> campaignId2AccountId = campaignMergeDataEntities.stream()
                .filter(CampaignMergeDataEntity::bindCrmObjectTypeAndBindCrmObjectIdValid)
                .filter(ele -> CampaignMergeDataObjectTypeEnum.ACCOUNT_OBJ.getType().equals(ele.getBindCrmObjectType()))
                .collect(Collectors.toMap(CampaignMergeDataEntity::getId, CampaignMergeDataEntity::getBindCrmObjectId, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(campaignId2AccountId.values())) {
            List<UserMarketingCrmAccountAccountRelationEntity> userMarketingCrmAccountAccountRelationEntities = userMarketingCrmAccountAccountRelationDao.listUserMarketingCrmAccountAccountRelationEntityByAccountIds(ea, campaignId2AccountId.values());
            Map<String, String> accountId2UserMarketingAccountId = userMarketingCrmAccountAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmAccountAccountRelationEntity::getCrmAccountId, UserMarketingCrmAccountAccountRelationEntity::getUserMarketingId, (v1, v2)->v1));
            for (Map.Entry<String, String> entry : campaignId2AccountId.entrySet()) {
                campaignId2UserMarketingAccountIDMap.putIfAbsent(entry.getKey(), accountId2UserMarketingAccountId.get(entry.getValue()));
            }
        }

        return Optional.ofNullable(campaignId2UserMarketingAccountIDMap);
    }

    /**
     * 根据参会人员id返回关联微信公众号信息, 已提供批量查询，分批最大数为1000
     * @param ea ea企业
     * @param wxAppId 微信APPId，查询企业下的所有wxAppId则传null
     * @param campaignIdsList 参会人员id
     * @return  返回参会人员id对应的UserMarketingWxServiceAccountRelationEntity对象，若参会人员id对应的entity为空，则value为null
     */
    public Optional<Map<String, UserMarketingWxServiceAccountRelationEntity>> getUserMarketingWxServiceAccountRelationEntitiesByCampaignIds(String ea, String wxAppId, List<String> campaignIdsList) {
        if (StringUtils.isEmpty(ea) || CollectionUtils.isEmpty(campaignIdsList)) {
            log.warn("UserMarketingAccountManager.getUserMarketingWxServiceAccountRelationEntitiesByCampaignIds ea:{} wxAppId:{} campaignIdsList:{}", ea, wxAppId, campaignIdsList);
            return Optional.empty();
        }
        List<List<String>> campaignIdsPartition = Lists.partition(campaignIdsList, BATCH_GET_SIZE);
        Map<String, UserMarketingWxServiceAccountRelationEntity> result = new HashMap<>(campaignIdsList.size() * 2);
        for (List<String> campaignIds : campaignIdsPartition) {
            // 1、优先查找表单信息
            List<CustomizeFormDataUserEntity> customizeFormDataUserEntities = customizeFormDataUserDAO.getCustomizeFormDataUserByCampaignIdsAndWxAppId(wxAppId, campaignIds);
            Map<String, String> campaignId2OpenId = customizeFormDataUserEntities.stream().filter(CustomizeFormDataUserEntity::openIdNotNull).collect(Collectors.toMap(CustomizeFormDataUserEntity::getCampaignId, CustomizeFormDataUserEntity::getOpenId, (v1, v2) -> v1));
            // 2、查询会员
            List<MemberAccessibleCampaignEntity> memberAccessibleCampaignEntities = memberAccessibleCampaignDAO.listMemberAccessibleCampaignEntitiesByEaAndCampaignIds(ea, campaignIds, wxAppId);
            campaignId2OpenId.putAll(memberAccessibleCampaignEntities.stream().filter(MemberAccessibleCampaignEntity::openIdNotNull).collect(Collectors.toMap(MemberAccessibleCampaignEntity::getCampaignId, MemberAccessibleCampaignEntity::getOpenId, (v1, v2) -> v1)));
            if (MapUtils.isNotEmpty(campaignId2OpenId)) {
                Map<String, UserMarketingWxServiceAccountRelationEntity> openId2UserMarketingWxServiceAccountRelationEntity = userMarketingWxServiceAccountRelationDao.listByEaAndWxAppIdAndWxOpenId(ea, wxAppId, campaignId2OpenId.values())
                        .stream().collect(Collectors.toMap(UserMarketingWxServiceAccountRelationEntity::getWxOpenId, Function.identity(), (v1, v2) -> v1));
                for (Map.Entry<String, String> entry : campaignId2OpenId.entrySet()) {
                    if (openId2UserMarketingWxServiceAccountRelationEntity.containsKey(entry.getValue())) {
                        result.put(entry.getKey(), openId2UserMarketingWxServiceAccountRelationEntity.get(entry.getValue()));
                    }
                }
            }
            // 3、查找营销用户信息
            Optional<Map<String, String>> marketingAccountIdsByCampaignIdsOptional = getMarketingAccountIdsByCampaignIds(ea, campaignIds, null);
            if (marketingAccountIdsByCampaignIdsOptional.isPresent() && MapUtils.isNotEmpty(marketingAccountIdsByCampaignIdsOptional.get())) {
                Map<String, String> campaignId2UserMarketingAccountIDMap = marketingAccountIdsByCampaignIdsOptional.get();
                List<String> userMarketingAccountIds = campaignId2UserMarketingAccountIDMap.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userMarketingAccountIds)) {
                    List<UserMarketingWxServiceAccountRelationEntity> userMarketingWxServiceAccountRelationEntities = userMarketingWxServiceAccountRelationDao
                        .listByIdAndWxAppId(ea, userMarketingAccountIds, wxAppId);
                    Map<String, UserMarketingWxServiceAccountRelationEntity> id2UserMarketingWxServiceAccountRelationEntity = userMarketingWxServiceAccountRelationEntities.stream()
                        .collect(Collectors.toMap(UserMarketingWxServiceAccountRelationEntity::getUserMarketingId, Function.identity(), (v1, v2) -> v1));
                    for (Map.Entry<String, String> entry : campaignId2UserMarketingAccountIDMap.entrySet()) {
                        result.putIfAbsent(entry.getKey(), id2UserMarketingWxServiceAccountRelationEntity.get(entry.getValue()));
                    }
                }
            }
        }
        return Optional.of(result);
    }

    public Map<String, String> getLeadAndWeChatAvatarUrlMap(String ea, Integer fsUserId, List<String> leadsIds) {
        Map<String, WeChatAvatarData> leadWeChatAvatarMap = getLeadWeChatAvatarMap(ea, fsUserId, leadsIds);
        if (MapUtils.isEmpty(leadWeChatAvatarMap)) {
            leadWeChatAvatarMap = Maps.newHashMap();
        }
        Map<String, WeChatAvatarData> finalLeadWeChatAvatarMap = leadWeChatAvatarMap;
        Map<String, String> result = leadsIds.stream().collect(Collectors.toMap(val -> val, val -> {
            WeChatAvatarData data = finalLeadWeChatAvatarMap.get(val);
            if (data == null || (StringUtils.isEmpty(data.getFirstUrl()) && StringUtils.isEmpty(data.getFirstPath()))) {
                return "";
            }
            if (StringUtils.isNotEmpty(data.getFirstUrl())) {
                return data.getFirstUrl();
            } else {
                return fileV2Manager.getUrlByPath(data.getFirstPath() + "." + data.getFirstExt(), ea, false);
            }
        },(v1,v2)->v1));
        return result;
    }

    public Map<String, WeChatAvatarData> getLeadWeChatAvatarMap(String ea, Integer fsUserId, List<String> leadsIds) {
        List<UserMarketingCrmLeadAccountRelationEntity> entities = userMarketingCrmLeadAccountRelationDao.batchGetByEaAndCrmLeadIds(ea, leadsIds);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }
        List<String> userMarketingAccountIds = entities.stream().map(UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId).collect(Collectors.toList());
        List<UserMarketingCrmWxUserAccountRelationEntity> userMarketingCrmWxUserAccountRelationEntities = userMarketingCrmWxUserAccountRelationDao
            .listByUserMarketingIds(ea, userMarketingAccountIds);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userMarketingCrmWxUserAccountRelationEntities)){
            return null;
        }

        Map<String, String> wxUserIdsAndUserMarketingAccountIdMap = userMarketingCrmWxUserAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmWxUserAccountRelationEntity::getCrmWxUserId, UserMarketingCrmWxUserAccountRelationEntity::getUserMarketingId));
        Map<String, WeChatAvatarData> wxUserWeChatAvatarDataMap = getWxUserWeChatAvatarDataMap(ea, fsUserId, wxUserIdsAndUserMarketingAccountIdMap.keySet());
        if (MapUtils.isEmpty(wxUserWeChatAvatarDataMap)) {
            return null;
        }

        Map<String, String> userMarkringAccountIdAndLeadIdRelation = entities.stream()
            .collect(Collectors.toMap(UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId, UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId, (v1,v2) -> v1));
        Map<String, String> wxUserIdAndUserMarketingIdRelation = wxUserWeChatAvatarDataMap.keySet().stream()
            .collect(Collectors.toMap(val->val, val-> wxUserIdsAndUserMarketingAccountIdMap.get(val)));
        Map<String, String> wxUserIdAndLeadsIdRelation = getWxUserIdAndLeadsIdRelation(wxUserIdAndUserMarketingIdRelation, userMarkringAccountIdAndLeadIdRelation);
        Map<String, WeChatAvatarData> leadWeChatAvatarDataMap = convertWxUserIdWeChatAvatarMap2LeadsWeChatAvatarMap(wxUserWeChatAvatarDataMap, wxUserIdAndLeadsIdRelation);
        return leadWeChatAvatarDataMap;
    }

    private Map<String, WeChatAvatarData> getWxUserWeChatAvatarDataMap(String ea, Integer fsUserId, Set<String> wxUserIds) {
        if (CollectionUtils.isEmpty(wxUserIds)) {
            return null;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        String apiName = WechatFanFieldContants.API_NAME;
        ControllerListArg arg = new ControllerListArg();
        arg.setObjectDescribeApiName(apiName);
        SearchQuery query = new SearchQuery();
        query.addFilter(ObjectDescribeContants.ID, Lists.newArrayList(wxUserIds), OperatorConstants.IN);
        arg.setSearchQuery(query);
        Page<ObjectData> objectDataPage = metadataControllerServiceManager.list(new HeaderObj(ei, fsUserId), apiName, arg);
        /** wxUserId,微信头像 */
        Map<String, WeChatAvatarData> weChatAvatarDataMap = Maps.newHashMap();
        for (ObjectData objectData : objectDataPage.getDataList()) {
            WechatFanData wechatFanData = WechatFanData.wrap(objectData);
            List<Map<String, String>> wxHeadImageList = wechatFanData.getWxHeadImage();
            if (wxHeadImageList != null) {
                WeChatAvatarData weChatAvatarData = new WeChatAvatarData();
                for (Map<String, String> data : wxHeadImageList) {
                    weChatAvatarData.add(data);
                }
                weChatAvatarDataMap.put(objectData.getId(), weChatAvatarData);
            }
        }
        return weChatAvatarDataMap;
    }

    private Map<String, WeChatAvatarData> convertWxUserIdWeChatAvatarMap2LeadsWeChatAvatarMap(Map<String, WeChatAvatarData> wxUserIdWeChatAvatarMap, Map<String, String> wxUserIdAndLeadsIdRelation) {
        Map<String, WeChatAvatarData> leadIdAndWeChatAvatarMap = Maps.newHashMap();
        wxUserIdWeChatAvatarMap.forEach((k1, v1) -> {
            if (wxUserIdAndLeadsIdRelation.get(k1) == null) {
                return;
            }
            leadIdAndWeChatAvatarMap.put(wxUserIdAndLeadsIdRelation.get(k1), v1);
        });
        return leadIdAndWeChatAvatarMap;
    }

    private Map<String, String> getWxUserIdAndLeadsIdRelation(Map<String, String> wxUserIdAndUserMarkringAccountRelation, Map<String, String> userMarkringAccountAndLeadIdRelation) {
        Map<String, String> dataMap = Maps.newHashMap();
        wxUserIdAndUserMarkringAccountRelation.forEach((k1, v1) -> {
            if (userMarkringAccountAndLeadIdRelation.get(v1) == null) {
                return;
            }
            dataMap.put(k1, userMarkringAccountAndLeadIdRelation.get(v1));
        });
        return dataMap;
    }

    private List<MiniappData> doListMiniappDataByUids(List<String> miniappIds) {
        if (CollectionUtils.isEmpty(miniappIds)) {
            return new ArrayList<>();
        }
//        List<CardEntity> cardEntities = cardDao.listByUids(miniappIds);
        // 这里改为查询小程序用户和营销用户数据
        List<UserEntity> userEntities = userDAO.listByUids(miniappIds);

        return BeanUtil.copyByGson(userEntities, MiniappData.class);
    }

    public Map<String, UserMarketingAccountData> merge(List<Map<String, UserMarketingAccountData>> userMarketingAccountIdAnduserMarketingAccountDataMapList) {
        Map<String, UserMarketingAccountData> result = Maps.newHashMap();
        for (Map<String, UserMarketingAccountData> userMarketingAccountDataMap : userMarketingAccountIdAnduserMarketingAccountDataMapList) {
            if (MapUtils.isEmpty(userMarketingAccountDataMap)) {
                continue;
            }
            result = this.merge(userMarketingAccountDataMap, result);
        }
        return result;
    }

    private Map<String, UserMarketingAccountData> merge(Map<String, UserMarketingAccountData> source, Map<String, UserMarketingAccountData> target) {
        Map<String, UserMarketingAccountData> result = Maps.newHashMap();
        if (MapUtils.isEmpty(source) && MapUtils.isEmpty(target)) {
            return result;
        } else if (!MapUtils.isEmpty(source) && !MapUtils.isEmpty(target)) {
            for (Entry<String, UserMarketingAccountData> userMarketingAccountDataEntry : target.entrySet()) {
                String userMarketingAccountId = userMarketingAccountDataEntry.getKey();
                UserMarketingAccountData dataTarget = userMarketingAccountDataEntry.getValue();
                UserMarketingAccountData dataSource = source.get(userMarketingAccountId);
                UserMarketingAccountData finalData = this.merge(dataSource, dataTarget);
                result.put(userMarketingAccountId, finalData);
            }
            List<String> onlyOnSourceUserMarketingAccountIds = Lists.newArrayList(source.keySet());
            onlyOnSourceUserMarketingAccountIds.removeAll(target.keySet());
            for (String onlyOnSourceUserMarketingAccountId : onlyOnSourceUserMarketingAccountIds) {
                UserMarketingAccountData finalData = this.merge(source.get(onlyOnSourceUserMarketingAccountId), result.get(onlyOnSourceUserMarketingAccountId));
                result.put(onlyOnSourceUserMarketingAccountId, finalData);
            }
            List<String> onlyOnTargetUserMarketingAccountIds = Lists.newArrayList(target.keySet());
            onlyOnTargetUserMarketingAccountIds.removeAll(source.keySet());
            for (String onlyOnTargetUserMarketingAccountId : onlyOnTargetUserMarketingAccountIds) {
                UserMarketingAccountData finalData = this.merge(target.get(onlyOnTargetUserMarketingAccountId), result.get(onlyOnTargetUserMarketingAccountId));
                result.put(onlyOnTargetUserMarketingAccountId, finalData);
            }
        } else if (MapUtils.isEmpty(source) && !MapUtils.isEmpty(target)) {
            result.putAll(target);
        } else if (!MapUtils.isEmpty(source) && MapUtils.isEmpty(target)) {
            result.putAll(source);
        }
        return result;
    }

    public UserMarketingAccountData convert(UserMarketingAccountData source) {
        if (source == null) {
            return null;
        }
        UserMarketingAccountData result = new UserMarketingAccountData();
        result.setUserMarketingAccountId(source.getUserMarketingAccountId());
        result.setWeChatAvatar(source.getWeChatAvatar());
        result.setWeChatName(source.getWeChatName());
        result.setName(source.getName());
        result.setPhone(source.getPhone());
        result.setEmail(source.getEmail());
        result.setCompanyName(source.getCompanyName());
        result.setOwners(source.getOwners());
        result.setDepartment(source.getDepartment());
        result.setPosition(source.getPosition());
        result.setAddress(source.getAddress());
        result.setTagNameList(source.getTagNameList());
        result.setCreateTime(source.getCreateTime());
        result.setEa(source.getEa());
        result.setSex(source.getSex());
        result.setCrmContactName(source.getCrmContactName());
        result.setCrmLeadName(source.getCrmLeadName());
        result.setCrmCustomerName(source.getCrmCustomerName());
        result.setCrmPartnerName(source.getCrmPartnerName());
        result.setCrmWxUserName(source.getCrmWxUserName());
        result.setArea(source.getArea());
        result.setSource(source.getSource());
        result.setWxServiceDatas(source.getWxServiceDatas());
        result.setCrmContactId(source.getCrmContactId());
        result.setCrmLeadId(source.getCrmLeadId());
        result.setCrmCustomerId(source.getCrmCustomerId());
        result.setCrmWxUserId(source.getCrmWxUserId());
        result.setCrmPartnerId(source.getCrmPartnerId());
        result.setBeWxApp(source.isBeWxApp());
        result.setBeCustomer(source.isBeCustomer());
        result.setBePartner(source.isBePartner());
        result.setBeContact(source.isBeContact());
        result.setBeLead(source.isBeLead());
        result.setBeMember(source.isBeMember());
        result.setMemberCardId(source.getMemberCardId());
        result.setGradeName(source.getGradeName());
        result.setGradeId(source.getGradeId());
        result.setGrowthValue(source.getGrowthValue());
        result.setIntegralValue(source.getIntegralValue());
        result.setCrmContactInfos(source.getCrmContactInfos());
        result.setCrmCustomerInfos(source.getCrmCustomerInfos());
        result.setCrmWxUserInfos(source.getCrmWxUserInfos());
        result.setCrmLeadInfos(source.getCrmLeadInfos());
        result.setCrmWxWorkExternalInfos(source.getCrmWxWorkExternalInfos());
        result.setCrmMemberInfos(source.getCrmMemberInfos());
        return result;
    }

    public UserMarketingAccountData merge(UserMarketingAccountData source, UserMarketingAccountData target) {
        UserMarketingAccountData result = this.convert(target);
        if (result == null) {
            result = new UserMarketingAccountData();
        }
        if (source == null) {
            return result;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getUserMarketingAccountId())) {
            result.setUserMarketingAccountId(source.getUserMarketingAccountId());
        }
        if (CollectionUtils.isEmpty(result.getWeChatAvatar())) {
            result.setWeChatAvatar(source.getWeChatAvatar());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getWeChatName())) {
            result.setWeChatName(source.getWeChatName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getName())) {
            result.setName(source.getName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getPhone())) {
            result.setPhone(source.getPhone());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getEmail())) {
            result.setEmail(source.getEmail());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCompanyName())) {
            result.setCompanyName(source.getCompanyName());
        }
        if (CollectionUtils.isEmpty(result.getOwners())) {
            result.setOwners(source.getOwners());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getDepartment())) {
            result.setDepartment(source.getDepartment());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getPosition())) {
            result.setPosition(source.getPosition());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getAddress())) {
            result.setAddress(source.getAddress());
        }
        if (CollectionUtils.isEmpty(result.getTagNameList())) {
            result.setTagNameList(source.getTagNameList());
        }
        if (result.getCreateTime() == null) {
            result.setCreateTime(source.getCreateTime());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getEa())) {
            result.setEa(source.getEa());
        }
        if (result.getSex() == null) {
            result.setSex(source.getSex());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmContactName())) {
            result.setCrmContactName(source.getCrmContactName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmLeadName())) {
            result.setCrmLeadName(source.getCrmLeadName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmCustomerName())) {
            result.setCrmCustomerName(source.getCrmCustomerName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmPartnerName())) {
            result.setCrmPartnerName(source.getCrmPartnerName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmWxUserName())) {
            result.setCrmWxUserName(source.getCrmWxUserName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCustomizeObjectDataName())){
            result.setCustomizeObjectDataName(source.getCustomizeObjectDataName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getArea())) {
            result.setArea(source.getArea());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getSource())) {
            result.setSource(source.getSource());
        }
        if (CollectionUtils.isEmpty(result.getWxServiceDatas())) {
            result.setWxServiceDatas(source.getWxServiceDatas());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmContactId())) {
            result.setCrmContactId(source.getCrmContactId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmLeadId())) {
            result.setCrmLeadId(source.getCrmLeadId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmCustomerId())) {
            result.setCrmCustomerId(source.getCrmCustomerId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmWxUserId())) {
            result.setCrmWxUserId(source.getCrmWxUserId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCrmPartnerId())) {
            result.setCrmPartnerId(source.getCrmPartnerId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getCustomizeObjectDataId())){
            result.setCustomizeObjectDataId(source.getCustomizeObjectDataId());
        }
        if (!result.isBeWxApp()) {
            result.setBeWxApp(source.isBeWxApp());
        }
        if (!result.isBeCustomer()) {
            result.setBeCustomer(source.isBeCustomer());
        }
        if (!result.isBePartner()) {
            result.setBePartner(source.isBePartner());
        }
        if (!result.isBeContact()) {
            result.setBeContact(source.isBeContact());
        }
        if (!result.isBeLead()) {
            result.setBeLead(source.isBeLead());
        }
        if (!result.isBeMember()) {
            result.setBeMember(source.isBeMember());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getMemberCardId())) {
            result.setMemberCardId(source.getMemberCardId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getGradeName())) {
            result.setGradeName(source.getGradeName());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getGradeId())) {
            result.setGradeId(source.getGradeId());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getGrowthValue())) {
            result.setGrowthValue(source.getGrowthValue());
        }
        if (org.apache.commons.lang.StringUtils.isBlank(result.getIntegralValue())) {
            result.setIntegralValue(source.getIntegralValue());
        }
        if(result.getCrmContactInfos() == null || result.getCrmContactInfos().isEmpty()){
            result.setCrmContactInfos(source.getCrmContactInfos());
        }
        if(result.getCrmCustomerInfos() == null || result.getCrmCustomerInfos().isEmpty()){
            result.setCrmCustomerInfos(source.getCrmCustomerInfos());
        }
        if(result.getCrmLeadInfos() == null || result.getCrmLeadInfos().isEmpty()){
            result.setCrmLeadInfos(source.getCrmLeadInfos());
        }
        if(result.getCrmWxUserInfos() == null || result.getCrmWxUserInfos().isEmpty()){
            result.setCrmWxUserInfos(source.getCrmWxUserInfos());
        }
        if(result.getCrmWxWorkExternalInfos() == null || result.getCrmWxWorkExternalInfos().isEmpty()){
            result.setCrmWxWorkExternalInfos(source.getCrmWxWorkExternalInfos());
        }
        if(result.getCrmMemberInfos() == null || result.getCrmMemberInfos().isEmpty()){
            result.setCrmMemberInfos(source.getCrmMemberInfos());
        }
        if (result.getCrmCustomizeObjectInfos() == null || result.getCrmCustomizeObjectInfos().isEmpty()){
            result.setCrmCustomizeObjectInfos(source.getCrmCustomizeObjectInfos());
        }
        if (result.getExternalUserId() == null || result.getExternalUserId().isEmpty()){
            result.setExternalUserId(source.getExternalUserId());
        }
        if(org.apache.commons.lang.StringUtils.isBlank(result.getAvatar())) {
            result.setAvatar(source.getAvatar());
        }
        return result;
    }

    public Map<String, UserMarketingAccountData> getBaseInfosByIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        if (userMarketingAccountIds == null || userMarketingAccountIds.isEmpty()){
            return new HashMap<>(0);
        }
        Map<String, UserMarketingAccountData> initUserMarketingAccountDataMap = userMarketingAccountIds.stream()
            .collect(Collectors.toMap(val -> val, UserMarketingAccountData::new, (v1, v2) -> v2, LinkedHashMap::new));
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromContactDataMap = this.listContactDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromAccountDataMap = this.listAccountDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromLeadDataMap = this.listLeadDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromMemberDataMap = this.listMemberDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromWxWorkExternalUserDataMap = this.listWxWorkExternalUserDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromWxUserDataMap = this.listWxUserDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromMiniappDataMap = this.listMiniappDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromCustomizeObjectDataMap = this.listCustomizeObjectDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = this.merge(Lists.newArrayList(initUserMarketingAccountDataMap, userMarketingAccountDataMapFromContactDataMap, userMarketingAccountDataMapFromAccountDataMap, userMarketingAccountDataMapFromLeadDataMap,
                userMarketingAccountDataMapFromWxWorkExternalUserDataMap, userMarketingAccountDataMapFromMemberDataMap, userMarketingAccountDataMapFromWxUserDataMap, userMarketingAccountDataMapFromMiniappDataMap, userMarketingAccountDataMapFromCustomizeObjectDataMap));
        if (InfoStateEnum.BRIEF.getState().equals(state.getState())) {
            //详情不需要组装标签信息，从单独的接口获取
            this.doListAndFillTags(ea, userMarketingAccountDataMap);
        }
        this.doOwnerIds2OwnerNames(ea, fsUserId, userMarketingAccountDataMap);
        this.fillCreateTime(userMarketingAccountDataMap);
        userMarketingAccountDataMap.values().forEach(userMarketingAccountData -> {
            if (userMarketingAccountData != null){
                userMarketingAccountData.setBeMember(userMarketingAccountData.getCrmMemberInfos() != null && !userMarketingAccountData.getCrmMemberInfos().isEmpty());
            }
        });
        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> getBaseInfosByIdsV2(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        if (userMarketingAccountIds == null || userMarketingAccountIds.isEmpty()){
            return new HashMap<>(0);
        }
        Map<String, UserMarketingAccountData> initUserMarketingAccountDataMap = userMarketingAccountIds.stream()
                .collect(Collectors.toMap(val -> val, UserMarketingAccountData::new, (v1, v2) -> v2, LinkedHashMap::new));
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromContactDataMap = this.listContactDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);

        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromAccountDataMap = this.listAccountDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);

        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromLeadDataMap = this.listLeadDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);

        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromMemberDataMap = this.listMemberDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);

        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromWxWorkExternalUserDataMap = this.listWxWorkExternalUserDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);

        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromWxUserDataMap = this.listWxUserDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);

        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromMiniappDataMap = this.listMiniappDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMapFromCustomizeObjectDataMap = this.listCustomizeObjectDataByUserMarketingAccountIds(ea, fsUserId, userMarketingAccountIds, state);
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = this.merge(Lists.newArrayList(initUserMarketingAccountDataMap, userMarketingAccountDataMapFromContactDataMap, userMarketingAccountDataMapFromAccountDataMap, userMarketingAccountDataMapFromLeadDataMap,
                userMarketingAccountDataMapFromWxWorkExternalUserDataMap, userMarketingAccountDataMapFromMemberDataMap, userMarketingAccountDataMapFromWxUserDataMap, userMarketingAccountDataMapFromMiniappDataMap, userMarketingAccountDataMapFromCustomizeObjectDataMap));
        if (InfoStateEnum.BRIEF.getState().equals(state.getState())) {
            //详情不需要组装标签信息，从单独的接口获取
            this.doListAndFillTags(ea, userMarketingAccountDataMap);
        }
        this.doOwnerIds2OwnerNames(ea, fsUserId, userMarketingAccountDataMap);
        this.fillCreateTime(userMarketingAccountDataMap);
        userMarketingAccountDataMap.values().forEach(userMarketingAccountData -> {
            if (userMarketingAccountData != null){
                userMarketingAccountData.setBeMember(userMarketingAccountData.getCrmMemberInfos() != null && !userMarketingAccountData.getCrmMemberInfos().isEmpty());
            }
        });
        return userMarketingAccountDataMap;
    }

    public List<UserMarketingAccountEntity> batchGet(List<String> ids){
        List<UserMarketingAccountEntity> result = new LinkedList<>();
        Set<String> idSet = new HashSet<>(ids);
        for (List<String> idList : Lists.partition(new ArrayList<>(idSet), BATCH_GET_SIZE)) {
            if(!idList.isEmpty()){
                result.addAll(userMarketingAccountDAO.batchGet(idList));
            }
        }
        return result;
    }

    public void fillCreateTime(Map<String, UserMarketingAccountData> userMarketingAccountDataMap) {
        List<String> userMarketingIds = new ArrayList<>(userMarketingAccountDataMap.keySet());
        if (userMarketingIds.size() == 0) {
            return;
        }
        List<UserMarketingAccountEntity> userMarketingAccountEntityList = this.batchGet(userMarketingIds);
        for (UserMarketingAccountEntity entity : userMarketingAccountEntityList) {
            UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(entity.getId());
            userMarketingAccountData.setCreateTime(DateUtil.getTimeStamp(entity.getCreateTime()));
        }
    }

    public void doOwnerIds2OwnerNames(String ea, Integer fsUserId, Map<String, UserMarketingAccountData> userMarketingAccountDataMap) {
        List<Integer> ownerIds = Lists.newArrayList();
        for (UserMarketingAccountData userMarketingAccountData : userMarketingAccountDataMap.values()) {
            List<OwnerData> ownerDatas = userMarketingAccountData.getOwners();
            if (CollectionUtils.isEmpty(ownerDatas)) {
                continue;
            }
            for (OwnerData ownerData : ownerDatas) {
                if (!Strings.isNullOrEmpty(ownerData.getId())) {
                    ownerIds.add(Integer.valueOf(ownerData.getId()));
                }
            }
        }
        if (CollectionUtils.isEmpty(ownerIds)) {
            return;
        }
        Map<Integer, FSEmployeeMsg> ownerInfos = fsAddressBookManager.getEmployeeInfoByUserIds(ea, ownerIds, false);
        for (UserMarketingAccountData userMarketingAccountData : userMarketingAccountDataMap.values()) {
            List<OwnerData> ownerDatas = userMarketingAccountData.getOwners();
            if (CollectionUtils.isEmpty(ownerDatas)) {
                continue;
            }
            for (OwnerData ownerData : ownerDatas) {
                if (Strings.isNullOrEmpty(ownerData.getId())) {
                    continue;
                }
                FSEmployeeMsg ownerInfo = ownerInfos.get(Integer.parseInt(ownerData.getId()));
                if (ownerInfo != null) {
                    ownerData.setOwnerName(ownerInfo.getName());
                }
            }
        }
    }

    public void doListAndFillTags(String ea, Map<String, UserMarketingAccountData> userMarketingAccountDataMap) {
        List<String> userMarketingIds = Lists.newArrayList(userMarketingAccountDataMap.keySet());
        Map<String, List<TagName>> userMarketingAccountIdAmdtagNameListMap = this.listTagNameListByUserMarketingAccountIds(ea, ChannelEnum.getAllChannelApiName(), userMarketingIds);
        if (MapUtils.isEmpty(userMarketingAccountIdAmdtagNameListMap)) {
            return;
        }
        for (String userMarketingId : userMarketingIds) {
            UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingId);
            List<TagName> tagNameList = userMarketingAccountIdAmdtagNameListMap.get(userMarketingId);
            userMarketingAccountData.setTagNameList(TagNameList.convert(tagNameList));
        }
    }

    private Boolean checkWxUserPermision(String ea, Integer fsUserId, String userMarketingAccountId) {
        UserMarketingCrmWxUserAccountRelationEntity entity = userMarketingCrmWxUserAccountRelationDao.getEachDataFirstByUserMarketingId(ea, userMarketingAccountId);
        if (entity == null) {
            return false;
        }
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDescribeApiName(WechatFanFieldContants.API_NAME);
        arg.setObjectDataId(entity.getCrmWxUserId());
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = metadataControllerServiceManager
            .detailSimpleResult(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), WechatFanFieldContants.API_NAME, arg);
        if (!result.isSuccess() && this.isNoPermissionOrElseError(result.getCode()) == 1) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        } else {
            return (result.isSuccess() || this.isNoPermissionOrElseError(result.getCode()) != 2) && result.getData().getData() != null;
        }
    }

    public Integer isNoPermissionOrElseError(Integer errorCode) {
        if (CrmErrorCode.NO_OPERATION_PERMISSION.equals(errorCode) || CrmErrorCode.NO_PERMISSION.equals(errorCode) || CrmErrorCode.HAS_DELETED.equals(errorCode)) {
            return 1;
        }
        return 2;
    }

    private Boolean checkAccountPermision(String ea, Integer fsUserId, String userMarketingAccountId) {
        UserMarketingCrmAccountAccountRelationEntity entity = userMarketingCrmAccountAccountRelationDao.getEachDataFirstByUserMarketingId(ea, userMarketingAccountId);
        if (entity == null) {
            return false;
        }
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDescribeApiName(AccountFieldContants.API_NAME);
        arg.setObjectDataId(entity.getCrmAccountId());
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = metadataControllerServiceManager
            .detailSimpleResult(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), AccountFieldContants.API_NAME, arg);
        if (!result.isSuccess() && this.isNoPermissionOrElseError(result.getCode()) == 1) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        } else {
            return (result.isSuccess() || (this.isNoPermissionOrElseError(result.getCode()) != 2)) && result.getData().getData() != null;
        }
    }

    private Boolean checkContactPermision(String ea, Integer fsUserId, String userMarketingAccountId) {
        UserMarketingCrmContactAccountRelationEntity entity = userMarketingCrmContactAccountRelationDao.getEachDataFirstByUserMarketingId(ea, userMarketingAccountId);
        if (entity == null) {
            return false;
        }
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDescribeApiName(ContactFieldContants.API_NAME);
        arg.setObjectDataId(entity.getCrmContactId());
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = metadataControllerServiceManager
            .detailSimpleResult(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), ContactFieldContants.API_NAME, arg);
        if (!result.isSuccess() && this.isNoPermissionOrElseError(result.getCode()) == 1) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        } else {
            return (result.isSuccess() || (this.isNoPermissionOrElseError(result.getCode()) != 2)) && result.getData().getData() != null;
        }
    }

    public Boolean checkUserMarketingAccountPermission(String ea, Integer fsUserId, String userMarketingAccountId) {
        return this.checkLeadPermision(ea, fsUserId, userMarketingAccountId) || this.checkContactPermision(ea, fsUserId, userMarketingAccountId) || this
            .checkAccountPermision(ea, fsUserId, userMarketingAccountId) || this.checkWxUserPermision(ea, fsUserId, userMarketingAccountId);
    }

    public Boolean checkLeadPermision(String ea, Integer fsUserId, String userMarketingAccountId) {
        UserMarketingCrmLeadAccountRelationEntity entity = userMarketingCrmLeadAccountRelationDao.getEachDataFirstByUserMarketingId(ea, userMarketingAccountId);
        if (entity == null) {
            return false;
        }
        ControllerDetailArg arg = new ControllerDetailArg();
        arg.setObjectDescribeApiName(LeadsFieldContants.API_NAME);
        arg.setObjectDataId(entity.getCrmLeadId());
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = metadataControllerServiceManager
            .detailSimpleResult(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), LeadsFieldContants.API_NAME, arg);
        if (!result.isSuccess() && this.isNoPermissionOrElseError(result.getCode()) == 1) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        } else {
            return (result.isSuccess() || this.isNoPermissionOrElseError(result.getCode()) != 2) && result.getData().getData() != null;
        }
    }

    public Map<String, UserMarketingAccountData> listLeadDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        List<UserMarketingCrmLeadAccountRelationEntity> crmLeadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isEmpty(crmLeadAccountRelationEntities)) {
            return userMarketingAccountDataMap;
        }

//        Map<String, String> crmLeadAndUserMarketingAccountIdMaps = crmLeadAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId,UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId));
//        Set<String> crmLeadIds =crmLeadAndUserMarketingAccountIdMaps.keySet();
//        if (CollectionUtils.isEmpty(crmLeadIds)) {
//            return null;
//        }

//        List<CrmLeadData> crmLeadDatas = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(),
//                        LEAD_SELECT_FIELDS, Lists.newArrayList(crmLeadIds)).stream()
//            .map(val -> fillCrmDataManager.getCrmLeadData(val)).collect(Collectors.toList());
//        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
//        crmLeadDatas.stream().filter(Objects::nonNull).forEach(crmLeadData -> {
//            String userMarketingAccountId = crmLeadAndUserMarketingAccountIdMaps.get(crmLeadData.getId());
//            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
//                UserMarketingAccountData userMarketingAccountData = this.convertLeadData(crmLeadData, userMarketingAccountId, state);
//                userMarketingAccountDataMap.putIfAbsent(userMarketingAccountId, userMarketingAccountData);
//            }
//            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
//                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmLeadInfo(crmLeadData.getId(), crmLeadData.getName());
//            }
//        });

        Map<String, List<String>> accountLeadsMap = Maps.newHashMap();
        Set<String> crmLeadIds = Sets.newHashSet();
        crmLeadAccountRelationEntities.forEach(x -> {
            crmLeadIds.add(x.getCrmLeadId());
            if(!accountLeadsMap.containsKey(x.getUserMarketingId())) {
                accountLeadsMap.put(x.getUserMarketingId(), Lists.newArrayList());
            }
            accountLeadsMap.get(x.getUserMarketingId()).add(x.getCrmLeadId());
        });
        List<CrmLeadData> crmLeadDatas = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(), LEAD_SELECT_FIELDS, Lists.newArrayList(crmLeadIds))
                .stream().map(val -> fillCrmDataManager.getCrmLeadData(val)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(crmLeadDatas)) {
            return userMarketingAccountDataMap;
        }
        //处理归集线索逻辑 优先取关联营销用户的线索中归集到的线索并且关联到营销用户的线索数据，如果无取最新的线索数据
        for(Entry<String, List<String>> item : accountLeadsMap.entrySet()) {
            List<CrmLeadData> leadDataList = crmLeadDatas.stream().filter(x -> item.getValue().contains(x.getId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(leadDataList)) {
                continue;
            }
            CrmLeadData crmLeadData = leadDataList.get(0);
            List<CrmLeadData> collectedLeadList = leadDataList.stream().filter(x -> !Strings.isNullOrEmpty(x.getCollectedTo())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collectedLeadList)) {
                for(CrmLeadData leadData : collectedLeadList) {
                    String collectedLeadId = leadData.getCollectedTo();
                    Optional<CrmLeadData> optionalCrmLeadData = leadDataList.stream().filter(x -> x.getId().equals(collectedLeadId)).findFirst();
                    if(optionalCrmLeadData.isPresent()) {
                        crmLeadData = optionalCrmLeadData.get();
                        break;
                    }
                }
            }
            String leadId = crmLeadData.getId();
            UserMarketingAccountData userMarketingAccountData = this.convertLeadData(crmLeadData, item.getKey(), state);
            leadDataList.forEach(x -> {
                if(!x.getId().equals(leadId)) {
                    if(Strings.isNullOrEmpty(userMarketingAccountData.getCompanyName())
                            && !Strings.isNullOrEmpty(x.getCompany())) {
                        userMarketingAccountData.setCompanyName(x.getCompany());
                    }
                    if(Strings.isNullOrEmpty(userMarketingAccountData.getDepartment())
                            && !Strings.isNullOrEmpty(x.getDepartment())) {
                        userMarketingAccountData.setDepartment(x.getDepartment());
                    }
                    if(Strings.isNullOrEmpty(userMarketingAccountData.getEmail())
                            && !Strings.isNullOrEmpty(x.getEmail())) {
                        userMarketingAccountData.setEmail(x.getEmail());
                    }
                    if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                            && (!Strings.isNullOrEmpty(x.getMobile()) || !Strings.isNullOrEmpty(x.getTel()))) {
                        userMarketingAccountData.setPhone(Strings.isNullOrEmpty(x.getMobile()) ? x.getTel(): x.getMobile());
                    }
                    userMarketingAccountData.pushCrmLeadInfo(x.getId(), x.getName());
                } else {
                    userMarketingAccountData.pushCrmLeadInfo(x.getId(), x.getName());
                }
            });
            userMarketingAccountDataMap.put(item.getKey(), userMarketingAccountData);
        }

        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listCustomizeObjectDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userMarketingAccountIds)){
            return null;
        }
        List<UserMarketingCustomizeObjectRelationEntity> crmCustomizeObjectAccountRelationEntities = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> crmCustomizeObjectIdAndUserMarketingAccountIdMaps = crmCustomizeObjectAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCustomizeObjectRelationEntity::getObjectId,UserMarketingCustomizeObjectRelationEntity::getUserMarketingId));
        if (CollectionUtils.isEmpty(crmCustomizeObjectIdAndUserMarketingAccountIdMaps.keySet())) {
            return null;
        }
        Map<String, Set<String>> objectApiNameIdsMap = new HashMap<>();
        crmCustomizeObjectAccountRelationEntities.forEach(entity -> {
            if (objectApiNameIdsMap.get(entity.getObjectApiName()) == null){
                objectApiNameIdsMap.put(entity.getObjectApiName(), new HashSet<>());
            }
            objectApiNameIdsMap.get(entity.getObjectApiName()).add(entity.getObjectId());
        });

        List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(mappingEntities)){
            return null;
        }
        Map<String, MarketingUserGroupCustomizeObjectMappingEntity> objectApiNameMapping = mappingEntities.stream().collect(Collectors.toMap(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName, Function.identity(), (v1, v2)->v2));
        List<CrmCustomizeObjectData> crmCustomizeObjectData = Lists.newArrayList();
        for (Map.Entry<String, Set<String>> entry : objectApiNameIdsMap.entrySet()){
            List<ObjectData> objectDataList = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, entry.getKey(), Lists.newArrayList(),Lists.newArrayList(entry.getValue()));
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(objectDataList)){
                crmCustomizeObjectData.addAll(objectDataList.stream().map(val -> fillCrmDataManager.getCrmCustomizeObjectData(ea, val, objectApiNameMapping)).collect(Collectors.toList()));
            }
        }

        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        crmCustomizeObjectData.stream().filter(Objects::nonNull).forEach(crmCutomizeData -> {
            String userMarketingAccountId = crmCustomizeObjectIdAndUserMarketingAccountIdMaps.get(crmCutomizeData.getId());
            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
                UserMarketingAccountData userMarketingAccountData = this.convertCustomizeObjectData(crmCutomizeData, userMarketingAccountId, state);
                userMarketingAccountDataMap.putIfAbsent(userMarketingAccountId, userMarketingAccountData);
            }
            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingAccountId);
                if(Strings.isNullOrEmpty(userMarketingAccountData.getEmail())
                        && !Strings.isNullOrEmpty(crmCutomizeData.getEmail())) {
                    userMarketingAccountData.setEmail(crmCutomizeData.getEmail());
                }
                if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                        && !Strings.isNullOrEmpty(crmCutomizeData.getMobile())) {
                    userMarketingAccountData.setPhone(crmCutomizeData.getMobile());
                }
                if((Strings.isNullOrEmpty(userMarketingAccountData.getName()) || NAME_BLACK_LIST.contains(userMarketingAccountData.getName()))
                        && (!Strings.isNullOrEmpty(crmCutomizeData.getName()) && !NAME_BLACK_LIST.contains(crmCutomizeData.getName()))) {
                    userMarketingAccountData.setName(crmCutomizeData.getName());
                }
                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmCustomizeObjectInfo(crmCutomizeData.getId(), crmCutomizeData.getName());
            }
        });

        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listContactDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        List<UserMarketingCrmContactAccountRelationEntity> crmContactAccountRelationEntities = userMarketingCrmContactAccountRelationDao
            .listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> crmContactAndUserMarketingAccountIdMaps = crmContactAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmContactAccountRelationEntity::getCrmContactId,UserMarketingCrmContactAccountRelationEntity::getUserMarketingId));
        List<String> crmContactIds = new ArrayList<>(crmContactAndUserMarketingAccountIdMaps.keySet());
        if (CollectionUtils.isEmpty(crmContactIds)) {
            return null;
        }

        List<CrmContactData> crmContactDatas = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.CONTACT.getName(), CONTACT_SELECT_FIELDS,crmContactIds).stream()
            .map(val -> fillCrmDataManager.getCrmContactData(ea, val)).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        crmContactDatas.stream().filter(Objects::nonNull).forEach(crmContactData -> {
            String userMarketingAccountId = crmContactAndUserMarketingAccountIdMaps.get(crmContactData.getId());
            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
                UserMarketingAccountData userMarketingAccountData = this.convertContactData(crmContactData, userMarketingAccountId, state);
                userMarketingAccountDataMap.put(userMarketingAccountId, userMarketingAccountData);
            }
            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingAccountId);
                if(Strings.isNullOrEmpty(userMarketingAccountData.getCompanyName())
                        && !Strings.isNullOrEmpty(crmContactData.getCompany())) {
                    userMarketingAccountData.setCompanyName(crmContactData.getCompany());
                }
                if(Strings.isNullOrEmpty(userMarketingAccountData.getDepartment())
                        && !Strings.isNullOrEmpty(crmContactData.getDepartment())) {
                    userMarketingAccountData.setDepartment(crmContactData.getDepartment());
                }
                if(Strings.isNullOrEmpty(userMarketingAccountData.getEmail())
                        && !Strings.isNullOrEmpty(crmContactData.getEmail())) {
                    userMarketingAccountData.setEmail(crmContactData.getEmail());
                }
                if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                        && !Strings.isNullOrEmpty(crmContactData.getMobile())) {
                    userMarketingAccountData.setPhone(crmContactData.getMobile());
                }
                if(userMarketingAccountData.getSex() == null
                        && crmContactData.getSex() != null) {
                    userMarketingAccountData.setSex(crmContactData.getSex());
                }
                if((Strings.isNullOrEmpty(userMarketingAccountData.getName()) || NAME_BLACK_LIST.contains(userMarketingAccountData.getName()))
                        && (!Strings.isNullOrEmpty(crmContactData.getName()) && !NAME_BLACK_LIST.contains(crmContactData.getName()))) {
                    userMarketingAccountData.setName(crmContactData.getName());
                }
                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmContactInfo(crmContactData.getId(), crmContactData.getName());
            }
        });
        List<String> accountIds = new ArrayList<>();
        for (CrmContactData crmContactData : crmContactDatas) {
            if (!Strings.isNullOrEmpty(crmContactData.getAccountId())) {
                accountIds.add(crmContactData.getAccountId());
            }
        }
        List<CrmAccountData> crmAccountData = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.CUSTOMER.getName(), CONTACT_SELECT_FIELDS, accountIds).stream()
            .map(val -> fillCrmDataManager.getCrmAccountData(ea, val)).collect(Collectors.toList());
        Map<String, String> idToNameMap = crmAccountData.stream().filter(data -> data.getName() != null).collect(Collectors.toMap(CrmAccountData::getId, CrmAccountData::getName, (v1, v2) -> v1));
        for (UserMarketingAccountData data : userMarketingAccountDataMap.values()) {
            String customerName = idToNameMap.get(data.getCrmCustomerId());
            if (!Strings.isNullOrEmpty(customerName) && Strings.isNullOrEmpty(data.getCrmCustomerName())) {
                data.setCrmCustomerName(customerName);
                //联系人公司名称使用客户名称填充
                data.setCompanyName(customerName);
            }
        }
        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listAccountDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        List<UserMarketingCrmAccountAccountRelationEntity> crmAccountAccountRelationEntities = userMarketingCrmAccountAccountRelationDao
            .listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> crmAccountAndUserMarketingAccountIdMaps = crmAccountAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmAccountAccountRelationEntity::getCrmAccountId, UserMarketingCrmAccountAccountRelationEntity::getUserMarketingId));
        List<String> crmAccountIds = crmAccountAccountRelationEntities.stream().map(UserMarketingCrmAccountAccountRelationEntity::getCrmAccountId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmAccountIds)) {
            return null;
        }

        List<CrmAccountData> crmCustomerDatas = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.CUSTOMER.getName(), CUSTOMER_SELECT_FIELDS,crmAccountIds).stream()
            .map(val -> fillCrmDataManager.getCrmAccountData(ea, val)).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        crmCustomerDatas.stream().filter(Objects::nonNull).forEach(crmAccountData -> {
            String userMarketingAccountId = crmAccountAndUserMarketingAccountIdMaps.get(crmAccountData.getId());
            log.info("userMarketingAccountId:{}", userMarketingAccountId);
            log.info("userMarketingAccountDataMap:{}",userMarketingAccountDataMap.toString());
            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
                UserMarketingAccountData userMarketingAccountData = this.convertAccountData(crmAccountData, userMarketingAccountId, state);
                userMarketingAccountDataMap.putIfAbsent(userMarketingAccountId, userMarketingAccountData);
            }
            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingAccountId);
                if(Strings.isNullOrEmpty(userMarketingAccountData.getEmail())
                        && !Strings.isNullOrEmpty(crmAccountData.getEmail())) {
                    userMarketingAccountData.setEmail(crmAccountData.getEmail());
                }
                if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                        && !Strings.isNullOrEmpty(crmAccountData.getMobile())) {
                    userMarketingAccountData.setPhone(crmAccountData.getMobile());
                }
                if((Strings.isNullOrEmpty(userMarketingAccountData.getName()) || NAME_BLACK_LIST.contains(userMarketingAccountData.getName()))
                        && (!Strings.isNullOrEmpty(crmAccountData.getName()) && !NAME_BLACK_LIST.contains(crmAccountData.getName()))) {
                    userMarketingAccountData.setName(crmAccountData.getName());
                }
                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmCustomerInfo(crmAccountData.getId(), crmAccountData.getName());
            }
        });
        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listMemberDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        List<UserMarketingCrmMemberRelationEntity> crmMemberMarketingUserRelations = userMarketingCrmMemberRelationDao
                .listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> crmMemberToUserMarketingAccountIdMaps = crmMemberMarketingUserRelations.stream().collect(Collectors.toMap(UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId, UserMarketingCrmMemberRelationEntity::getUserMarketingId));
        List<String> crmMemberIds = crmMemberMarketingUserRelations.stream().map(UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmMemberIds)) {
            return null;
        }

        List<CrmMemberData> crmMemberDataList = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.MEMBER.getName(), MEMBER_SELECT_FIELDS,crmMemberIds).stream()
                .map(val -> fillCrmDataManager.getCrmMemberData(ea, val)).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        crmMemberDataList.stream().filter(Objects::nonNull).forEach(crmMemberData -> {
            String userMarketingAccountId = crmMemberToUserMarketingAccountIdMaps.get(crmMemberData.getId());
            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
                UserMarketingAccountData userMarketingAccountData = this.convertMemberData(crmMemberData, userMarketingAccountId, state);
                userMarketingAccountDataMap.put(userMarketingAccountId, userMarketingAccountData);
            }
            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingAccountId);
                if(Strings.isNullOrEmpty(userMarketingAccountData.getEmail())
                        && !Strings.isNullOrEmpty(crmMemberData.getEmail())) {
                    userMarketingAccountData.setEmail(crmMemberData.getEmail());
                }
                if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                        && !Strings.isNullOrEmpty(crmMemberData.getPhone())) {
                    userMarketingAccountData.setPhone(crmMemberData.getPhone());
                }
                if((Strings.isNullOrEmpty(userMarketingAccountData.getName()) || NAME_BLACK_LIST.contains(userMarketingAccountData.getName()))
                        && (!Strings.isNullOrEmpty(crmMemberData.getName()) && !NAME_BLACK_LIST.contains(crmMemberData.getName()))) {
                    userMarketingAccountData.setName(crmMemberData.getName());
                }
                if(CollectionUtils.isEmpty(userMarketingAccountData.getWeChatAvatar())
                        && CollectionUtils.isNotEmpty(crmMemberData.getAvatar())) {
                    userMarketingAccountData.setWeChatAvatar(crmMemberData.getAvatar());
                }
                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmMemberInfos(crmMemberData.getId(), crmMemberData.getName());
            }
        });
        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listWxWorkExternalUserDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        List<UserMarketingCrmWxWorkExternalUserRelationEntity> crmWxWorkExternalUserMarketingUserRelations = userMarketingCrmWxWorkExternalUserRelationDao
                .listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> crmWxWorkExternalUserAndUserMarketingAccountIdMaps = crmWxWorkExternalUserMarketingUserRelations.stream().collect(Collectors.toMap(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId, UserMarketingCrmWxWorkExternalUserRelationEntity::getUserMarketingId));
        List<String> crmWxWorkExternalUserId = crmWxWorkExternalUserMarketingUserRelations.stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmWxWorkExternalUserId)) {
            return null;
        }

        List<CrmWxWorkExternalUserData> crmWxWorkExternalUserDataList = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), WX_EXTERNAL_USER_SELECT_FIELDS, crmWxWorkExternalUserId).stream()
                .map(val -> fillCrmDataManager.getCrmWxWorkExternalUserData(ea, val)).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        crmWxWorkExternalUserDataList.stream().filter(Objects::nonNull).forEach(crmWxWorkExternalUserData -> {
            String userMarketingAccountId = crmWxWorkExternalUserAndUserMarketingAccountIdMaps.get(crmWxWorkExternalUserData.getId());
            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
                UserMarketingAccountData userMarketingAccountData = this.convertWxWorkExternalUserData(crmWxWorkExternalUserData, userMarketingAccountId, state);
                userMarketingAccountDataMap.put(userMarketingAccountId, userMarketingAccountData);
            }
            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingAccountId);
                if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                        && !Strings.isNullOrEmpty(crmWxWorkExternalUserData.getPhone())) {
                    userMarketingAccountData.setPhone(crmWxWorkExternalUserData.getPhone());
                }
                if((Strings.isNullOrEmpty(userMarketingAccountData.getName()) || NAME_BLACK_LIST.contains(userMarketingAccountData.getName()))
                        && (!Strings.isNullOrEmpty(crmWxWorkExternalUserData.getName()) && !NAME_BLACK_LIST.contains(crmWxWorkExternalUserData.getName()))) {
                    userMarketingAccountData.setName(crmWxWorkExternalUserData.getName());
                }
                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmWxWorkExternalUserInfos(crmWxWorkExternalUserData.getId(), crmWxWorkExternalUserData.getName());
            }
        });
        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listMiniappDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        List<UserMarketingMiniappAccountRelationEntity> miniappAccountRelationEntities = userMarketingMiniappAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> miniappAndUserMarketingAccountIdMaps = miniappAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingMiniappAccountRelationEntity::getUid,UserMarketingMiniappAccountRelationEntity::getUserMarketingId));
        List<String> uids = miniappAccountRelationEntities.stream().map(UserMarketingMiniappAccountRelationEntity::getUid).collect(Collectors.toList());
        // todo ??? 取值错误
        List<MiniappData> miniappDatas = this.doListMiniappDataByUids(uids);
        if (CollectionUtils.isEmpty(miniappDatas)) {
            return null;
        }
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();
        miniappDatas.forEach(miniappData -> {
            String userMarketingAccountId = miniappAndUserMarketingAccountIdMaps.get(miniappData.getUid());
            UserMarketingAccountData data = this.convertMiniappData(miniappData, userMarketingAccountId, state);
            userMarketingAccountDataMap.putIfAbsent(userMarketingAccountId, data);
        });
        return userMarketingAccountDataMap;
    }

    public Map<String, UserMarketingAccountData> listWxUserDataByUserMarketingAccountIds(String ea, Integer fsUserId, List<String> userMarketingAccountIds, InfoStateEnum state) {
        List<UserMarketingCrmWxUserAccountRelationEntity> crmWxUserAccountRelationEntities = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        Map<String, String> crmWxUserAndUserMarketingAccountIdMaps = crmWxUserAccountRelationEntities.stream().collect(Collectors.toMap(UserMarketingCrmWxUserAccountRelationEntity::getCrmWxUserId, UserMarketingCrmWxUserAccountRelationEntity::getUserMarketingId));
        List<String> crmWxUserIds = crmWxUserAccountRelationEntities.stream().map(UserMarketingCrmWxUserAccountRelationEntity::getCrmWxUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmWxUserIds)) {
            return null;
        }

        Map<String, OuterServiceResult> wxServiceInfoMap = new HashMap<>();
        List<OuterServiceResult> outerServiceResults = outerServiceWechatManager.queryOuterServiceList(ea, fsUserId);
        if (outerServiceResults != null){
            for (OuterServiceResult wxServiceInfo : outerServiceResults) {
                wxServiceInfoMap.put(wxServiceInfo.getWxAppId(), wxServiceInfo);
            }
        }

        List<CrmWxUserData> crmWxUserDatas = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.WECHAT.getName(), WECHAT_SELECT_FIELDS, crmWxUserIds).stream().filter(MapUtils::isNotEmpty)
            .map(val -> fillCrmDataManager.getCrmWxUserData(WechatFanData.wrap(val))).collect(Collectors.toList());
        Map<String, UserMarketingAccountData> userMarketingAccountDataMap = Maps.newLinkedHashMap();

        crmWxUserDatas.stream().filter(Objects::nonNull).forEach(crmWxUserData -> {
            String userMarketingAccountId = crmWxUserAndUserMarketingAccountIdMaps.get(crmWxUserData.getId());
            if(!userMarketingAccountDataMap.containsKey(userMarketingAccountId)){
                UserMarketingAccountData userMarketingAccountData = this.convertWxUserData(wxServiceInfoMap, crmWxUserData, userMarketingAccountId, state);
                userMarketingAccountDataMap.put(userMarketingAccountId, userMarketingAccountData);
            }
            if(userMarketingAccountDataMap.get(userMarketingAccountId) != null){
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(userMarketingAccountId);
                if(Strings.isNullOrEmpty(userMarketingAccountData.getPhone())
                        && !Strings.isNullOrEmpty(crmWxUserData.getPhone())) {
                    userMarketingAccountData.setPhone(crmWxUserData.getPhone());
                }
                if((Strings.isNullOrEmpty(userMarketingAccountData.getName()) || NAME_BLACK_LIST.contains(userMarketingAccountData.getName()))
                        && (!Strings.isNullOrEmpty(crmWxUserData.getName()) && !NAME_BLACK_LIST.contains(crmWxUserData.getName()))) {
                    userMarketingAccountData.setName(crmWxUserData.getName());
                }
                if(CollectionUtils.isEmpty(userMarketingAccountData.getWeChatAvatar())
                        && CollectionUtils.isNotEmpty(crmWxUserData.getWxHeadImages())) {
                    userMarketingAccountData.setWeChatAvatar(crmWxUserData.getWxHeadImages());
                }
                userMarketingAccountDataMap.get(userMarketingAccountId).pushCrmWxUserInfo(crmWxUserData.getId(), crmWxUserData.getName());
            }
        });
        List<String> accountIds = new ArrayList<>();
        for (UserMarketingAccountData data : userMarketingAccountDataMap.values()) {
            if (!Strings.isNullOrEmpty(data.getCrmCustomerId())) {
                accountIds.add(data.getCrmCustomerId());
            }
        }

        List<CrmAccountData> crmCustomerData = crmMetadataManager.batchGetByIdsV3(ea, fsUserId, CrmObjectApiNameEnum.CUSTOMER.getName(), CUSTOMER_SELECT_FIELDS, accountIds).stream()
            .map(val -> fillCrmDataManager.getCrmAccountData(ea, val)).collect(Collectors.toList());
        Map<String, String> idToNameMap = crmCustomerData.stream().collect(Collectors.toMap(CrmAccountData::getId, CrmAccountData::getName));
        for (UserMarketingAccountData data : userMarketingAccountDataMap.values()) {
            String customerName = idToNameMap.get(data.getCrmCustomerId());
            if (!Strings.isNullOrEmpty(customerName) && Strings.isNullOrEmpty(data.getCrmCustomerName())) {
                data.setCrmCustomerName(customerName);
            }
            if (!Strings.isNullOrEmpty(customerName) && Strings.isNullOrEmpty(data.getCompanyName())) {
                data.setCompanyName(customerName);
            }
        }
        return userMarketingAccountDataMap;
    }

    private UserMarketingAccountData convertMiniappData(MiniappData miniappData, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (miniappData == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setName(miniappData.getName());
        data.setPhone(miniappData.getPhone());
        data.setEmail(miniappData.getEmail());
        data.setCompanyName(miniappData.getCompanyName());
        data.setDepartment(miniappData.getDepartment());
        data.setPosition(miniappData.getVocation());
        data.setAddress(miniappData.getCompanyAddress());
        if (InfoStateEnum.DETAIL == infoStateEnum) {
            data.setSex(miniappData.getGender());
        }
        data.setAvatar(miniappData.getAvatar());
        return data;
    }

    public List<String> buildUserMarketingApiNameListByEa(String ea) {
        List<String> objectApiNameList = new ArrayList<>();
        objectApiNameList.add(CrmObjectApiNameEnum.CONTACT.getName());
        objectApiNameList.add(CrmObjectApiNameEnum.CUSTOMER.getName());
        objectApiNameList.add(CrmObjectApiNameEnum.CRM_LEAD.getName());
        if(marketingWxServiceDao.countByEa(ea) > 0){
            objectApiNameList.add(CrmObjectApiNameEnum.WECHAT.getName());
        }
        if(qywxCorpAgentConfigDAO.queryAgentByEa(ea) != null){
            objectApiNameList.add(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        }
        if (memberManager.isOpenMember(ea)){
            objectApiNameList.add(CrmObjectApiNameEnum.MEMBER.getName());
        }

        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(objectMappingEntityList)){
            objectApiNameList.addAll(objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList()));
        }
        return objectApiNameList;
    }

    public List<FilterData> buildFilterDataListByTag(String ea, String tagOperator, List<TagName> tagNames, Boolean excludeTags, List<TagName> excludeTagNames, Integer pageNo, Integer pageSize){
        List<FilterData> filterDataList = new LinkedList<>();
        Preconditions.checkArgument(!Strings.isNullOrEmpty(tagOperator));
        Preconditions.checkArgument(tagNames != null && !tagNames.isEmpty());
        for (String objectApiName : this.buildUserMarketingApiNameListByEa(ea)) {
            filterDataList.add(doBuildTagFilterData(objectApiName, tagOperator, tagNames, excludeTags, excludeTagNames, pageNo, pageSize));
        }
        return filterDataList;
    }


    public List<String> getObjectEmailsByTags(String ea, TagNameList tagNames, String tagOperator, boolean excludeTags, TagNameList excludeTagNames) {
        List<String> selectedFileds = ImmutableList.of("_id", "email");
        List<FilterData> filterDataList = buildFilterDataListByTag(ea, tagOperator, tagNames, excludeTags, excludeTagNames, 0, 0);
        Set<String> allEmails = new HashSet<>();
        List<String> emailByFilter = null;
        for (FilterData filterData : filterDataList) {
            PaasQueryFilterArg paasQueryFilterArg = buildPassQueryFilterArg(ea, filterData);
            if (paasQueryFilterArg != null) {
                paasQueryFilterArg.setSelectFields(selectedFileds);
                emailByFilter = getObjectEmailsByPaasTagFilter(ea, paasQueryFilterArg);
            }
            if (CollectionUtils.isNotEmpty(emailByFilter)) {
                allEmails.addAll(emailByFilter);
            }
        }

        return Lists.newArrayList(allEmails);
    }

    public List<String> getObjectEmailsByPaasTagFilter(String ea, PaasQueryFilterArg paasQueryFilterArg) {
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
        if (totalCount <= 0) {
            return null;
        }

        int currentCount = 0;
        String lastId = null;
        List<ObjectData> objectDataList = Lists.newArrayList();
        while (currentCount < totalCount) {
            GuavaLimiter.acquire(CRM_OBJ_QUERY_RATE_LIMIT_KEY, ea);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg, lastId, 500);
            if (objectDataInnerPage == null || org.apache.commons.collections4.CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            objectDataList.addAll(objectDataInnerPage.getDataList());
            int tempSize = objectDataInnerPage.getDataList().size();
            currentCount += tempSize;
            lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
        }

        return objectDataList.stream().map(objectData -> objectData.getString("email")).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

//    @NotNull
    private FilterData buildFilterDataByEnterpriseInfoIdList(String objectName, List<String> partEnterpriseInfoIdList, Integer pageNo, Integer pageSize) {
        FilterData leadFilterData = new FilterData();
        leadFilterData.setObjectAPIName(objectName);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilter("enterprise_id", OperatorConstants.IN, partEnterpriseInfoIdList);
        if (pageNo == null || pageNo == 0 || pageSize == null || pageSize == 0){
            searchTemplateQuery.setOffset(0);
            searchTemplateQuery.setLimit(0);
        }else{
            searchTemplateQuery.setOffset(0);
            searchTemplateQuery.setLimit(pageNo * pageSize);
        }
        leadFilterData.setQuery(searchTemplateQuery);
        return leadFilterData;
    }
    
    public int countRelationByEnterpriseInfoIds(String ea, Integer fsUserId, List<String> enterpriseInfoIds){
        com.fxiaoke.crmrestapi.common.data.Filter enterpriseIdInFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
        enterpriseIdInFilter.setFieldName("enterprise_id");
        enterpriseIdInFilter.setOperator(OperatorConstants.IN);
        enterpriseIdInFilter.setFieldValues(enterpriseInfoIds);
        int leadRelationCount = crmV2Manager.countByFilters(ea, fsUserId, CrmObjectApiNameEnum.CRM_LEAD.getName(), ImmutableList.of(enterpriseIdInFilter));
        int customerRelationCount = crmV2Manager.countByFilters(ea, fsUserId, CrmObjectApiNameEnum.CUSTOMER.getName(), ImmutableList.of(enterpriseIdInFilter));
        return leadRelationCount + customerRelationCount;
    }

    private FilterData doBuildTagFilterData(String objectApiName, String tagOperator, List<TagName> tagNames, Boolean excludeTags, List<TagName> excludeTagNames, int pageNo, int pageSize){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(pageNo * pageSize);
        query.setOffset(0);
        query.setTagOperator(tagOperator);
        query.setTagNames(tagNames);
        query.setExcludeTags(excludeTags);
        query.setExcludeTagNames(excludeTagNames);
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(objectApiName);
        filterData.setQuery(query);
        return filterData;
    }

    private UserMarketingAccountData convertLeadData(CrmLeadData crmLeadData, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (crmLeadData == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }

        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setOwners(CollectionUtils.isEmpty(crmLeadData.getOwners()) ? null : crmLeadData.getOwners().stream().map(OwnerData::new).collect(Collectors.toList()));
        data.setSource(CrmObjectApiNameEnum.CRM_LEAD.getLabel());
        data.setName(crmLeadData.getName());
        data.setPhone(crmLeadData.getMobile() == null ? crmLeadData.getTel(): crmLeadData.getMobile());
        data.setEmail(crmLeadData.getEmail());
        data.setCompanyName(crmLeadData.getCompany());
        data.setDepartment(crmLeadData.getDepartment());
        data.setPosition(crmLeadData.getJobTitle());
        data.setAddress(crmLeadData.getAddress());
        if (InfoStateEnum.DETAIL == infoStateEnum) {
            data.setCrmLeadId(crmLeadData.getId());
            data.setCrmLeadName(crmLeadData.getName());
            data.setEa(crmLeadData.getEa());
        }

        return data;
    }

    private UserMarketingAccountData convertCustomizeObjectData(CrmCustomizeObjectData crmCustomizeData, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (crmCustomizeData == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setOwners(CollectionUtils.isEmpty(crmCustomizeData.getOwners()) ? null : crmCustomizeData.getOwners().stream().map(OwnerData::new).collect(Collectors.toList()));
        data.setSource("自定义对象");
        data.setName(crmCustomizeData.getName());
        data.setPhone(crmCustomizeData.getMobile());
        data.setEmail(crmCustomizeData.getEmail());
        data.setCustomizeObjectDataId(crmCustomizeData.getId());
        data.setCustomizeObjectDataName(crmCustomizeData.getName());
        data.setEa(crmCustomizeData.getEa());
        return data;
    }

    private UserMarketingAccountData convertContactData(CrmContactData crmContactData, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (crmContactData == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setOwners(CollectionUtils.isEmpty(crmContactData.getOwners()) ? null : crmContactData.getOwners().stream().map(OwnerData::new).collect(Collectors.toList()));
        data.setSource(CrmObjectApiNameEnum.CONTACT.getLabel());
        data.setName(crmContactData.getName());
        data.setCrmCustomerName(crmContactData.getAccountName());
        data.setCrmCustomerId(crmContactData.getAccountId());
        data.setPhone(crmContactData.getMobile());
        data.setEmail(crmContactData.getEmail());
        data.setCompanyName(crmContactData.getCompany());
        data.setDepartment(crmContactData.getDepartment());
        data.setPosition(crmContactData.getJobTitle());
        data.setAddress(crmContactData.getAdd());
        if (InfoStateEnum.DETAIL == infoStateEnum) {
            data.setCrmContactId(crmContactData.getId());
            data.setCrmContactName(crmContactData.getName());
            data.setEa(crmContactData.getEa());
        }
        return data;
    }

    private UserMarketingAccountData convertAccountData(CrmAccountData source, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (source == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setOwners(CollectionUtils.isEmpty(source.getOwners()) ? null : source.getOwners().stream().map(OwnerData::new).collect(Collectors.toList()));
        data.setSource(CrmObjectApiNameEnum.CUSTOMER.getLabel());
        data.setPhone(source.getMobile());
        data.setEmail(source.getEmail());
        data.setCrmCustomerName(source.getName());
        //按照产品需求，客户对象的公司名使用客户名称填充
        data.setCompanyName(source.getName());
        //如果到这里没有取到姓名，则使用客户名称填充姓名
        data.setName(source.getName());
        data.setAddress(source.getAdd());
        data.setCrmCustomerId(source.getId());
        data.setEa(source.getEa());
        return data;
    }

    private UserMarketingAccountData convertWxWorkExternalUserData(CrmWxWorkExternalUserData source, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (source == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setSource(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getLabel());
        data.setPhone(source.getPhone());
        data.setName(source.getName());
        data.setCompanyName(source.getEnterpriseName());
        data.setPosition(source.getPosition());
        data.setEa(source.getEa());
        data.setExternalUserId(source.getExternalUserId());
        return data;
    }

    private UserMarketingAccountData convertMemberData(CrmMemberData source, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (source == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setUserMarketingAccountId(userMarketingAccountId);
        data.setSource(CrmObjectApiNameEnum.MEMBER.getLabel());
        data.setName(source.getName());
        data.setPhone(source.getPhone());
        data.setEmail(source.getEmail());
        data.setIntegralValue(source.getIntegralValue());
        data.setGrowthValue(source.getGrowthValue());
        data.setGradeId(source.getGradeId());
        data.setEa(source.getEa());
        data.setWeChatAvatar(source.getAvatar());
        return data;
    }

    private UserMarketingAccountData convertWxUserData(Map<String, OuterServiceResult> wxServiceInfoMap, CrmWxUserData crmWxUserData, String userMarketingAccountId, InfoStateEnum infoStateEnum) {
        if (crmWxUserData == null || StringUtils.isEmpty(userMarketingAccountId)) {
            return null;
        }
        UserMarketingAccountData data = new UserMarketingAccountData();
        data.setOwners(CollectionUtils.isEmpty(crmWxUserData.getOwners()) ? null : crmWxUserData.getOwners().stream().map(OwnerData::new).collect(Collectors.toList()));
        data.setSource(CrmObjectApiNameEnum.WECHAT.getLabel());
        data.setWeChatName(crmWxUserData.getName());
        data.setWeChatAvatar(crmWxUserData.getWxHeadImages());
        data.setName(StringUtils.isEmpty(crmWxUserData.getUsername()) ? crmWxUserData.getName() : crmWxUserData.getUsername());
        data.setPhone(crmWxUserData.getPhone());
        data.setCrmCustomerId(crmWxUserData.getAccountId());
        data.setAddress(crmWxUserData.getArea());
        data.setCrmWxUserId(crmWxUserData.getId());
        data.setCrmWxUserName(crmWxUserData.getName());
        data.setEa(crmWxUserData.getEa());
        if (!Strings.isNullOrEmpty(crmWxUserData.getWxAppId())) {
            OuterServiceResult outerServiceResult = wxServiceInfoMap.get(crmWxUserData.getWxAppId());
            if (outerServiceResult != null && StringUtils.isNotEmpty(outerServiceResult.getWxAppName())) {
                String wxAppName = outerServiceResult.getWxAppName();
                WxServiceData wxServiceData = new WxServiceData();
                wxServiceData.setWxAppId(crmWxUserData.getWxAppId());
                wxServiceData.setWxAppName(wxAppName);
                if (data.getWxServiceDatas() == null) {
                    data.setWxServiceDatas(Lists.newArrayList(wxServiceData));
                } else {
                    if (!isWxAppIdDuplicate(data.getWxServiceDatas(), wxServiceData.getWxAppId())) {
                        data.getWxServiceDatas().add(wxServiceData);
                    }
                }
            }
        }

        return data;
    }

    private Boolean isWxAppIdDuplicate(List<WxServiceData> wxServiceDatas, String wxAppId) {
        for (WxServiceData wxServiceDataItem : wxServiceDatas) {
            if (wxServiceDataItem.getWxAppId().equals(wxAppId)) {
                return true;
            }
        }
        return false;
    }

    public List<String> getMarketingUserIdsByFilterDataList(String ea, Integer fsUserId, Integer marketingUserSearchType, List<FilterData> filterDataList, PageArg page, List<Integer> dataRangeCodeList) {
        return this.getMarketingUserIdsByFilterDataList(ea, fsUserId, marketingUserSearchType, filterDataList, page, dataRangeCodeList, false);
    }

    // 这里最多返回10万条
    public int countMarketingUserIdsCountByFilterDataListV2(String ea, Integer fsUserId, Integer marketingUserSearchType, List<FilterData> filterDataList, List<Integer> dataRangeCodeList, String marketingUserGroupId) {
        int totalCount = 0;
        addCustomizeFilter(ea, fsUserId, filterDataList,marketingUserGroupId);
        boolean searchByEnterpriseLibrary = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_ENTERPRISE_LIBRARY.getSearchType() == marketingUserSearchType;
        boolean searchByUserBehaviorRecord = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_ENTERPRISE_LIBRARY.getSearchType() == marketingUserSearchType;

        if (searchByEnterpriseLibrary) {
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> CrmObjectApiNameEnum.ENTERPRISE_INFO_OBJ.getName().equals(filterData.getObjectAPIName())).collect(Collectors.toList());
        } else if(searchByUserBehaviorRecord) {
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName().equals(filterData.getObjectAPIName())).collect(Collectors.toList());
        }else {
            //兼容已经设置的自定义对象
            Set<String> objectApiNameSet = new HashSet<>(CrmObjectApiNameEnum.getAllUserApiNames());
            List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
            if (CollectionUtils.isNotEmpty(mappingEntities)) {
                objectApiNameSet.addAll(mappingEntities.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toSet()));
            }
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> objectApiNameSet.contains(filterData.getObjectAPIName())).collect(Collectors.toList());
        }
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
        for (FilterData filterData : filterDataList) {
            // 根据filterData组装查询pass v3参数 如果为空 直接处理下一个
            PaasQueryFilterArg queryFilterArg = buildPassQueryFilterArg(ea, filterData);
            if (queryFilterArg == null) {
                continue;
            }
            if(filterData.getObjectAPIName().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName()) && isOpen) {
                // 深复制一下PaasQueryFilterArg, 这里只需要查询_id字段即可
                PaasQueryFilterArg newQueryFilterArg = JsonUtil.fromJson(JsonUtil.toJson(queryFilterArg), PaasQueryFilterArg.class);
                newQueryFilterArg.setSelectFields(Lists.newArrayList("_id"));
                InnerPage<ObjectData> crmObjectResult = crmV2Manager.syncListCrmObjectByFilterV4(ea, SuperUserConstants.USER_ID, newQueryFilterArg);
                if (crmObjectResult == null || CollectionUtils.isEmpty(crmObjectResult.getDataList())) {
                    log.info(" query wechat work external user obj is empty, ea: {} arg: {}", ea, queryFilterArg);
                    continue;
                }
                List<String> qywxCustomerIds = crmObjectResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
                for (List<String> part : Lists.partition(qywxCustomerIds, 1000)) {
                    //好友记录替换企微客户对象
                    SearchTemplateQuery query = new SearchTemplateQuery();
                    query.addFilter("external_user_id", OperatorConstants.IN, part);
                    FilterData friRecFilter = new FilterData();
                    friRecFilter.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                    friRecFilter.setQuery(query);
                    ArrayList<FilterData> filterData1 = Lists.newArrayList(friRecFilter);
                    addCustomizeFilter(ea, fsUserId, filterData1,marketingUserGroupId);
                    PaasQueryFilterArg paasQueryFilterArg = buildPassQueryFilterArg(ea, filterData1.get(0));
                    paasQueryFilterArg.setSelectFields(Lists.newArrayList("_id"));
                    int total = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
                    if (total <= 0) {
                        continue;
                    }
                    totalCount += total;
                }
                continue;
            }
            int total = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            if (total <= 0) {
                continue;
            }
            totalCount += total;
        }
        return totalCount;
    }

    public List<FilterData> buildFilterData(String ea, Integer fsUserId, Integer marketingUserSearchType, List<FilterData> filterDataList, List<Integer> dataRangeCodeList,String marketingUserGroupId) {
        addCustomizeFilter(ea, fsUserId, filterDataList,marketingUserGroupId);

        boolean searchByEnterpriseLibrary = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_ENTERPRISE_LIBRARY.getSearchType() == marketingUserSearchType;
        boolean searchByUserBehaviorRecord = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_USER_BEHAVIOR_RECORD.getSearchType() == marketingUserSearchType;

        if (searchByEnterpriseLibrary){
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> CrmObjectApiNameEnum.ENTERPRISE_INFO_OBJ.getName().equals(filterData.getObjectAPIName())).collect(Collectors.toList());
        } else if (searchByUserBehaviorRecord){
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName().equals(filterData.getObjectAPIName())).collect(Collectors.toList());
        } else {
            //兼容已经设置的自定义对象
            Set<String> objectApiNameSet = new HashSet<>(CrmObjectApiNameEnum.getAllUserApiNames());
            List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
            if (CollectionUtils.isNotEmpty(mappingEntities)){
                objectApiNameSet.addAll(mappingEntities.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toSet()));
            }
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> objectApiNameSet.contains(filterData.getObjectAPIName())).collect(Collectors.toList());
        }

        return filterDataList;
    }

    public Set<String> getMarketingUserIdsByObjectDataList(String ea, Integer marketingUserSearchType, List<Integer> dataRangeCodeList, String objectApiName, List<ObjectData> objectDataList){
        List<String> userMarketingIds;
        Set<String> marketingUserIdSet = new HashSet<>();
        boolean searchByEnterpriseLibrary = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_ENTERPRISE_LIBRARY.getSearchType() == marketingUserSearchType;
        boolean searchByUserBehaviorRecord = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_USER_BEHAVIOR_RECORD.getSearchType() == marketingUserSearchType;

        if(searchByUserBehaviorRecord){
            Set<String> tempUserMarketingIdSet = objectDataList.stream().filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x.getString("user_marketing_id")))
                    .map(x -> x.getString("user_marketing_id")).collect(Collectors.toSet());
            marketingUserIdSet.addAll(tempUserMarketingIdSet);
            return marketingUserIdSet;
        }
        if (!searchByEnterpriseLibrary) {
            if (marketingUserSearchType != null && marketingUserSearchType == MarketingUserSearchTypeEnum.BY_CUSTOMER_RELATION.getSearchType()
                    && CollectionUtils.isNotEmpty(dataRangeCodeList)) {
                userMarketingIds = getByCustomerRelation(ea, dataRangeCodeList, objectDataList);
            } else {
                userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, objectApiName, objectDataList, false);
            }
        } else {
            // 处理企业库关联的对象数据
            // 企业库的id集合
            Set<String> tempEnterpriseIdSet = objectDataList.stream().map(ObjectData::getId).collect(Collectors.toSet());
            // 根据企业库id查询线索
            List<ObjectData> crmLeadDataList = queryListCrmDataByEnterpriseId(ea, CrmObjectApiNameEnum.CRM_LEAD, tempEnterpriseIdSet);
            userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), crmLeadDataList, false);
            userMarketingIds.stream().filter(StringUtils::isNotEmpty).forEach(marketingUserIdSet::add);
            // 根据企业库id查询客户
            List<ObjectData> crmCustomerDataList = queryListCrmDataByEnterpriseId(ea, CrmObjectApiNameEnum.CUSTOMER, tempEnterpriseIdSet);
            userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CUSTOMER.getName(), crmCustomerDataList, false);
            userMarketingIds.stream().filter(StringUtils::isNotEmpty).forEach(marketingUserIdSet::add);
            // 根据客户查询联系人
            List<String> customerIdList = crmCustomerDataList.stream().map(ObjectData::getId).collect(Collectors.toList());
            // 查询客户的联系人
            List<ObjectData> listCrmContactResult = getCustomerContactData(ea, customerIdList);
            userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CONTACT.getName(), listCrmContactResult, false);
        }
        userMarketingIds.stream().filter(StringUtils::isNotEmpty).forEach(marketingUserIdSet::add);

        return marketingUserIdSet;
    }


    // 这里最多只会返回10万条数据
    public List<String> getMarketingUserIdsByFilterDataListV2(String ea, Integer fsUserId, Integer marketingUserSearchType, List<FilterData> filterDataList, List<Integer> dataRangeCodeList, String marketingUserGroupId) {
        // 这里增加一些特定的filter
        List<FilterData> buildFilterDataList = buildFilterData(ea, fsUserId, marketingUserSearchType, filterDataList, dataRangeCodeList,marketingUserGroupId);
        Set<String> marketingUserIdSet = new HashSet<>(100);
        int i = 0;
        long updateProgressStartTime = System.currentTimeMillis();
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
        for (FilterData filterData : buildFilterDataList) {
            // 根据filterData组装查询pass v3参数 如果为空 直接处理下一个
            PaasQueryFilterArg queryFilterArg = buildPassQueryFilterArg(ea, filterData);
            i++;
            if (queryFilterArg == null) {
                log.info("getMarketingUserIdsByFilterDataListV2 queryFilterArg is null, ea: {} filterDataList: {}", ea, filterDataList);
                continue;
            }
            if(filterData.getObjectAPIName().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName()) && isOpen) {
                // 深复制一下PaasQueryFilterArg, 这里只需要查询_id字段即可
                PaasQueryFilterArg newQueryFilterArg = JsonUtil.fromJson(JsonUtil.toJson(queryFilterArg), PaasQueryFilterArg.class);
                newQueryFilterArg.setSelectFields(Lists.newArrayList("_id"));
                InnerPage<ObjectData> crmObjectResult = crmV2Manager.syncListCrmObjectByFilterV4(ea, -10000, newQueryFilterArg);
                if (crmObjectResult == null || CollectionUtils.isEmpty(crmObjectResult.getDataList())) {
                    log.info("getMarketingUserIdsByFilterDataListV2 external user is null, ea: {} arg: {}", ea, queryFilterArg);
                    continue;
                }
                log.info("getMarketingUserIdsByFilterDataListV2 external user size: {}", crmObjectResult.getDataList().size());
                List<String> qywxCustomerIds = crmObjectResult.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
                //好友记录替换企微客户对象  直接在这里分页查询，要不然查询的外部联系人太多会导致有问题
                for (List<String> part : Lists.partition(qywxCustomerIds, 1000)) {
                    SearchTemplateQuery query = new SearchTemplateQuery();
                    query.addFilter("external_user_id",OperatorConstants.IN, part);
                    FilterData friRecFilter = new FilterData();
                    friRecFilter.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                    friRecFilter.setQuery(query);
                    ArrayList<FilterData> filterData1 = Lists.newArrayList(friRecFilter);
                    addCustomizeFilter(ea, fsUserId, filterData1, marketingUserGroupId);
                    queryFilterArg = buildPassQueryFilterArg(ea, filterData1.get(0));
                    queryFilterArg.setSelectFields(Lists.newArrayList("_id","external_user_id"));
                    InnerPage<ObjectData> innerPage = crmV2Manager.syncListCrmObjectByFilterV4(ea, SuperUserConstants.USER_ID, queryFilterArg);
                    if (innerPage == null || CollectionUtils.isEmpty(innerPage.getDataList())) {
                        log.info("getMarketingUserIdsByFilterDataListV2 friends record is null, ea: {} arg: {}", ea, queryFilterArg);
                        continue;
                    }
                    log.info("getMarketingUserIdsByFilterDataListV2 friends record ea: {} size: {}", ea, innerPage.getDataList().size());
                    Set<String> objectIds = innerPage.getDataList().stream().map(o -> String.valueOf(o.get("external_user_id"))).filter(Objects::nonNull).collect(Collectors.toSet());
                    List<ObjectIdWithMarketingUserIdAndPhoneDTO> marketingUserIds = userMarketingCrmWxWorkExternalUserRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
                    if (CollectionUtils.isNotEmpty(marketingUserIds)) {
                        marketingUserIds.stream().map(ObjectIdWithMarketingUserIdAndPhoneDTO::getUserMarketingId).filter(Objects::nonNull).forEach(marketingUserIdSet::add);
                    }
                    GuavaLimiter.acquire(CRM_OBJ_QUERY_RATE_LIMIT_KEY, ea);
                }
                continue;
            }
            int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
            log.info("目标人群计算,ea: {} userGroupId: {}, filterList.size: {} 第: {} 个filter totalCount: {}", ea, marketingUserGroupId, buildFilterDataList.size(), i, totalCount);
            if (totalCount <= 0) {
                continue;
            }

            int currentCount = 0;
            String lastId = null;
            while(currentCount < totalCount) {
                long t1 = System.currentTimeMillis();
                GuavaLimiter.acquire(CRM_OBJ_QUERY_RATE_LIMIT_KEY, ea);
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 500);
                long t2 = System.currentTimeMillis();
                if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                int tempSize = objectDataInnerPage.getDataList().size();
                currentCount += tempSize;
                lastId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
                long progress = currentCount / totalCount * 100L;
                log.info("目标人群计算,ea: {} userGroupId: {}, filterList.size: {} 第: {} 个filter 查询对象耗时: {} currentCount: {}, 进度：{} %",
                        ea, marketingUserGroupId, buildFilterDataList.size(), i, t2 - t1, currentCount, progress);
                long t3 = System.currentTimeMillis();
                if(filterData.getObjectAPIName().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName()) && isOpen) {
                    //此时对象的数据是好友记录的
                    Set<String> objectIds = objectDataInnerPage.getDataList().stream().map(o -> String.valueOf(o.get("external_user_id"))).collect(Collectors.toSet());
                    List<ObjectIdWithMarketingUserIdAndPhoneDTO> marketingUserIds = userMarketingCrmWxWorkExternalUserRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
                    marketingUserIds.stream().map(ObjectIdWithMarketingUserIdAndPhoneDTO::getUserMarketingId).forEach(marketingUserIdSet::add);
                }else {
                    marketingUserIdSet.addAll(getMarketingUserIdsByObjectDataList(ea, marketingUserSearchType, dataRangeCodeList, filterData.getObjectAPIName(), objectDataInnerPage.getDataList()));
                }
                long t4 = System.currentTimeMillis();
                log.info("目标人群计算,ea: {} userGroupId: {}, filterList.size: {} 第: {} 个filter 获取营销用户耗时:{}  ,去重后当前营销用户数量：{}, 进度： {}%",
                        ea, marketingUserGroupId, buildFilterDataList.size(), i, t4 - t3, marketingUserIdSet.size(), progress);
                if (t4 - updateProgressStartTime >= 10*1000){
                    // 超过10秒就更新一次进度
                    updateMarketingGroupUserCalculationProgress(ea, marketingUserGroupId, null, currentCount);
                    updateProgressStartTime = t4;
                }
                // 跳出里层循环
                if (marketingUserIdSet.size() >= MarketingUserGroupManager.MAX_MAX_MARKETING_USER_IN_GROUP) {
                    // 人群最多的数量是十万 达到就返回
                    break;
                }
            }
            // 跳出外层循环
            if (marketingUserIdSet.size() >= MarketingUserGroupManager.MAX_MAX_MARKETING_USER_IN_GROUP) {
                // 人群最多的数量是十万 达到就返回
                break;
            }
        }
        if (marketingUserIdSet.size() > MarketingUserGroupManager.MAX_MAX_MARKETING_USER_IN_GROUP) {
            marketingUserIdSet = marketingUserIdSet.stream().limit(MarketingUserGroupManager.MAX_MAX_MARKETING_USER_IN_GROUP).collect(Collectors.toSet());
        }
        //更新进度
        updateMarketingGroupUserCalculationProgress(ea, marketingUserGroupId, marketingUserIdSet.size(), marketingUserIdSet.size());
        return Lists.newArrayList(marketingUserIdSet);
    }

    /**
     * 更新人群计算进度
     * @param ea
     * @param marketingUserGroupId
     * @param totalCount
     * @param currentCount
     */
    public void updateMarketingGroupUserCalculationProgress(String ea, String marketingUserGroupId, Integer totalCount, Integer currentCount){
        MarketingGroupUserActionEntity userActionEntity = marketingGroupUserActionDAO.getByMarketingGroupId(ea, marketingUserGroupId);
        if (userActionEntity == null){
            return;
        }
        marketingGroupUserActionDAO.updateProgressById(ea, userActionEntity.getId(), currentCount, totalCount);
    }

    public PaasQueryFilterArg buildPassQueryFilterArg(String ea, FilterData filterData) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(filterData.getObjectAPIName());
        boolean searchByEnterpriseLibrary = CrmObjectApiNameEnum.ENTERPRISE_INFO_OBJ.getName().equals(filterData.getObjectAPIName());
        boolean searchByUserBehaviorRecord = CrmObjectApiNameEnum.USER_BEHAVIOR_RECORDS_OBJ.getName().equals(filterData.getObjectAPIName());

        // 要查询的字段
        List<String> selectFields = Lists.newArrayList();
        if (searchByEnterpriseLibrary) {
            // 企业库只需要查id
            selectFields.add("_id");
        } else if (searchByUserBehaviorRecord) {
            // 企业库只需要查id
            selectFields.add("_id");
            selectFields.add("user_marketing_id");
        } else {
            Set<String> filedProjectionSet = new HashSet<>(FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
            List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
            if (CollectionUtils.isNotEmpty(mappingEntities)){
                mappingEntities.forEach(mappingEntity ->
                        filedProjectionSet.addAll(mappingEntity.getFieldDataCommonMapping().stream().map(CustomizeObjectMappings.CustomizeObjectMapping::getCrmFieldApiName).collect(Collectors.toList())));
            }
            selectFields.addAll(filedProjectionSet);
        }
        queryFilterArg.setSelectFields(selectFields);

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        // 将筛选的条件解析出来
        List<PaasQueryArg.Condition> conditionList = getConditionListByFilterData(filterData.getQuery().getFilters());
        paasQueryArg.setFilters(conditionList);
        if(CollectionUtils.isNotEmpty(filterData.getQuery().getWheres())){
            paasQueryArg.setWheres(BeanUtil.copy(filterData.getQuery().getWheres(), Wheres.class));
        }
        queryFilterArg.setQuery(paasQueryArg);

        // 根据标签名查询标签ID
        List<String> tagIdList = getTagIdListByFilterData(ea, filterData.getObjectAPIName(), filterData.getQuery().getTagNames());
        // 提前筛选一次
        String tagOperator = "IN".equals(filterData.getQuery().getTagOperator()) ? "LIKE" : filterData.getQuery().getTagOperator();
        if ("HASANYOF".equals(tagOperator) && CollectionUtils.isEmpty(tagIdList)) {
            return null;
        }
        // 包含
        Set<TagName> tagNameSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(filterData.getQuery().getTagNames())) {
            tagNameSet.addAll(filterData.getQuery().getTagNames());
        }
        if ("LIKE".equals(tagOperator) && tagIdList.size() != tagNameSet.size()){
            return null;
        }
        if (CollectionUtils.isNotEmpty(tagIdList) && StringUtils.isNotEmpty(tagOperator)) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag", tagIdList, tagOperator);
            condition.setValueType(11);
            conditionList.add(condition);
        }
        // 处理 不包含任意标签
        if (BooleanUtils.isTrue(filterData.getQuery().getExcludeTags()) &&  CollectionUtils.isNotEmpty(filterData.getQuery().getExcludeTagNames())) {
            tagNameSet = Sets.newHashSet(filterData.getQuery().getExcludeTagNames());
            List<String> excludeTagIds = getTagIdListByFilterData(ea, filterData.getObjectAPIName(), Lists.newArrayList(tagNameSet));
            if (CollectionUtils.isNotEmpty(excludeTagIds)) {
                PaasQueryArg.Condition condition = new PaasQueryArg.Condition("tag", excludeTagIds, "NHASANYOF");
                condition.setValueType(11);
                conditionList.add(condition);
            }
        }
        // 如果是企微相关对象，只查询营销通的数据
        if (CrmObjectApiNameEnum.isQywxObject(filterData.getObjectAPIName())) {
            PaasQueryArg.Condition condition = new PaasQueryArg.Condition(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Lists.newArrayList(AppScopeEnum.MARKETING.getValue()), PaasAndCrmOperatorEnum.IN.getCrmOperator());
            conditionList.add(condition);
        }
        return queryFilterArg;
    }

    private List<String> getTagIdListByFilterData(String ea, String objectApiName, List<TagName> tagNameListFilter) {
        List<String> tagIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tagNameListFilter)) {
            return tagIds;
        }
        Set<TagName> tagNameSet = new HashSet<>(tagNameListFilter);
        List<String> tempTagIds = metadataTagManager.getTagIdsByTagNames(ea, objectApiName, tagNameSet).values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tempTagIds)) {
            tagIds.addAll(tempTagIds);
        }
        return tagIds;
    }
    private List<PaasQueryArg.Condition> getConditionListByFilterData(List<Filter> filterList) {
        List<PaasQueryArg.Condition> conditionList = Lists.newArrayList();
        for (Filter filter : filterList) {
            PaasQueryArg.Condition condition;
            // tag的查询需要特殊处理
            if ("tag".equals(filter.getFieldName())) {
                String tagOperator = "IN".equals(filter.getOperator()) ? "LIKE" : filter.getOperator();
                condition = new PaasQueryArg.Condition("tag", filter.getFieldValues(), tagOperator);
                condition.setValueType(11);
            } else {
                condition = JsonUtil.fromJson(JsonUtil.toJson(filter), PaasQueryArg.Condition.class);
            }
            if ("38".equals(filter.getFieldType()) || "46".equals(filter.getFieldType())) {
                String[] fieldNameArr = filter.getFieldName().split("\\.");
                // 搜索查找关联对象时,如果查找的是非name字段,则需要将isMasterField设置为true
                if (fieldNameArr.length == 2 && !"name".equals(fieldNameArr[1])) {
                    condition.setIsMasterField(true);
                }
            }
            conditionList.add(condition);
        }
        return conditionList;
    }

    public void addCustomizeFilter(String ea, Integer fsUserId, List<FilterData> filterDataList,String marketingUserGroupId) {
        // 预处理数据
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
        filterDataList.forEach(e -> {
            if (CrmObjectApiNameEnum.WECHAT.getName().equals(e.getObjectAPIName())) {
                SearchTemplateQuery query = e.getQuery();
                query.addFilter("attention_status", "N", Collections.singletonList("no_attention"));
                // 渠道账号过滤
                if (isOpen) {
                    List<String> wxAppIds = dataPermissionManager.findAccessibleOfficialAccountIds(ea, fsUserId);
                    if(wxAppIds.contains(defaultAllChannel)){
                        return;
                    }
                    query.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(), FilterOperatorEnum.EQ.getValue(), wxAppIds);
                    return;
                }
            }
            // 数据权限过滤添加
            List<Integer> dataPermission = null;
            if (isOpen && StringUtils.isNotEmpty(marketingUserGroupId) && !CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(e.getObjectAPIName())){
                MarketingUserGroupEntity marketingUserGroupEntity = marketingUserGroupDao.getById(marketingUserGroupId);
                if(marketingUserGroupEntity!=null && marketingUserGroupEntity.getPermissionRange()!=null){
                    dataPermission = JSONObject.parseArray(marketingUserGroupEntity.getPermissionRange(), Integer.class);
                }
                if(CollectionUtils.isEmpty(dataPermission)){
                    return;
                }
                if (!dataPermission.contains(defaultAllDepartment)) {
                    // 过滤部门和组织并且构建filter
                    dataPermissionManager.builtDepartmentFilter(ea, e, dataPermission);
                }
            }
        });
    }



    public List<String> getMarketingUserIdsByFilterDataList(String ea, Integer fsUserId, Integer marketingUserSearchType, List<FilterData> filterDataList, PageArg page, List<Integer> dataRangeCodeList, boolean isList) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        // 预处理数据
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        List<Integer> dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
        filterDataList.forEach(e -> {
            if (CrmObjectApiNameEnum.WECHAT.getName().equals(e.getObjectAPIName())) {
                SearchTemplateQuery query = e.getQuery();
                query.addFilter("attention_status", "N", Collections.singletonList("no_attention"));
            }
            // 数据权限过滤添加
            if (isOpen && !isList) {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dataPermission)) {
                    e.getQuery().addFilter("data_own_organization", OperatorConstants.IN, dataPermission.stream().map(String::valueOf).collect(Collectors.toList()));
                }
            }
        });
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        boolean searchByEnterpriseLibrary = marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_ENTERPRISE_LIBRARY.getSearchType() == marketingUserSearchType;
        if (searchByEnterpriseLibrary){
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> CrmObjectApiNameEnum.ENTERPRISE_INFO_OBJ.getName().equals(filterData.getObjectAPIName())).collect(Collectors.toList());
        }else{
            //兼容已经设置的自定义对象
            Set<String> objectApiNameSet = new HashSet<>(CrmObjectApiNameEnum.getAllUserApiNames());
            List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(mappingEntities)){
                objectApiNameSet.addAll(mappingEntities.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toSet()));
            }
            filterDataList = filterDataList.stream().filter(Objects::nonNull).filter(filterData -> objectApiNameSet.contains(filterData.getObjectAPIName())).collect(Collectors.toList());
        }
    
        Set<String> marketingUserIdSet = new HashSet<>(100);
        List<String> marketingUserIdResult = new LinkedList<>();
        final int maxCrmQueryTime = 1000;
        //是否不分页，默认分页，page信息传null则不分页
        boolean isPage = false;
        if (page != null && page.getPageNo() != null && page.getPageSize() != null) {
            isPage = true;
        }
        
        int crmBatchGetLimit = 100;
        if (isPage && page.getPageNo() * page.getPageSize() < crmBatchGetLimit){
            crmBatchGetLimit = page.getPageNo() * page.getPageSize();
        }
        
        if (!searchByEnterpriseLibrary){
            for (FilterData filterData : filterDataList) {
                List<TagName> tagNameListFilter = filterData.getQuery().getTagNames();
                String tagOperator = "IN".equals(filterData.getQuery().getTagOperator()) ? "LIKE" : filterData.getQuery().getTagOperator();
                List<String> tagIds = null;
                if (tagNameListFilter != null && !tagNameListFilter.isEmpty()){
                    Set<TagName> tagNameSet = new HashSet<>(tagNameListFilter);
                    tagIds = metadataTagManager.getTagIdsByTagNames(ea, filterData.getObjectAPIName(), tagNameSet).values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
                    if ("HASANYOF".equals(tagOperator) && tagIds.isEmpty()){
                        continue;
                    }
                    // 包含
                    if ("LIKE".equals(tagOperator) && tagIds.size() != tagNameSet.size()){
                        continue;
                    }
                }
                List<com.fxiaoke.crmrestapi.common.data.Filter> specialTagFilter = null;
                if(marketingUserSearchType != null && MarketingUserSearchTypeEnum.BY_TAG.getSearchType() != marketingUserSearchType){
                    specialTagFilter = new ArrayList<>();
                    Iterator<Filter> iterator = filterData.getQuery().getFilters().iterator();
                    Filter tempFilter;
                    while(iterator.hasNext()){
                        tempFilter = iterator.next();
                        if("tag".equals(tempFilter.getFieldName())){
                            com.fxiaoke.crmrestapi.common.data.Filter queryFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
                            queryFilter.setFieldName("tag");
                            queryFilter.setOperator("IN".equals(tempFilter.getOperator()) ? "LIKE" : tempFilter.getOperator());
                            queryFilter.setFieldValues(tempFilter.getFieldValues());
                            queryFilter.setValueType(11);
                            specialTagFilter.add(queryFilter);
                            iterator.remove();
                        }
                    }
                }
                List<String> excludeTagIds = null;
                if (BooleanUtils.isTrue(filterData.getQuery().getExcludeTags()) && filterData.getQuery().getExcludeTagNames() != null && !filterData.getQuery().getExcludeTagNames().isEmpty()){
                    Set<TagName> tagNameSet = new HashSet<>(filterData.getQuery().getExcludeTagNames());
                    excludeTagIds = metadataTagManager.getTagIdsByTagNames(ea, filterData.getObjectAPIName(), tagNameSet).values().stream().filter(tagId -> !Strings.isNullOrEmpty(tagId)).collect(Collectors.toList());
                }

                Set<String> filedProjectionSet = new HashSet<>(FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
                List<MarketingUserGroupCustomizeObjectMappingEntity> mappingEntities = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(mappingEntities)){
                    mappingEntities.forEach(mappingEntity -> {
                        filedProjectionSet.addAll(mappingEntity.getFieldDataCommonMapping().stream().map(CustomizeObjectMappings.CustomizeObjectMapping::getCrmFieldApiName).collect(Collectors.toList()));
                    });
                }

                for (int crmBatchGetOffset = 0; crmBatchGetOffset < maxCrmQueryTime * crmBatchGetLimit; crmBatchGetOffset += crmBatchGetLimit) {
                    ControllerListArg perCrmListArg = new ControllerListArg();
                    perCrmListArg.setObjectDescribeApiName(filterData.getObjectAPIName());
                    perCrmListArg.setSearchTemplateId(filterData.getSearchTemplateId());
                    doMarkNotGetUnNesscessaryData(perCrmListArg);
                    perCrmListArg.setFieldProjection(new ArrayList<>(filedProjectionSet));
                    SearchQuery searchQuery = BeanUtil.copyByFastJson(filterData.getQuery(), SearchQuery.class);
                    searchQuery.addOrderBy("last_modified_time", false);
                    searchQuery.setOffset(crmBatchGetOffset);
                    searchQuery.setLimit(crmBatchGetLimit);
                    if (!Strings.isNullOrEmpty(tagOperator) && tagIds != null && !tagIds.isEmpty()){
                        com.fxiaoke.crmrestapi.common.data.Filter tagFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
                        tagFilter.setFieldName("tag");
                        tagFilter.setOperator(tagOperator);
                        tagFilter.setFieldValues(tagIds);
                        tagFilter.setValueType(11);
                        searchQuery.getFilters().add(tagFilter);
                    }
                    if (BooleanUtils.isTrue(filterData.getQuery().getExcludeTags()) && excludeTagIds != null && !excludeTagIds.isEmpty()){
                        com.fxiaoke.crmrestapi.common.data.Filter tagFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
                        tagFilter.setFieldName("tag");
                        tagFilter.setOperator("NHASANYOF");
                        tagFilter.setFieldValues(excludeTagIds);
                        tagFilter.setValueType(11);
                        searchQuery.getFilters().add(tagFilter);
                    }
                    if (null != specialTagFilter && specialTagFilter.size() > 0) {
                        searchQuery.getFilters().addAll(specialTagFilter);
                    }
                    perCrmListArg.setSearchQuery(searchQuery);
                    Result<Page<ObjectData>> listCrmObjectResult = metadataControllerService.list(headerObj, filterData.getObjectAPIName(), perCrmListArg);
                    if (listCrmObjectResult.getCode() == CRM_NO_PERMISSION_ERROR_CODE){
                        break;
                    }
                    Preconditions.checkState(listCrmObjectResult.isSuccess(), "CRM exception detected.");
                    
                    if (listCrmObjectResult.getData() == null || listCrmObjectResult.getData().getDataList() == null || listCrmObjectResult.getData().getDataList().isEmpty()){
                        break;
                    }
                    List<String> userMarketingIds;
                    if (marketingUserSearchType != null && marketingUserSearchType == MarketingUserSearchTypeEnum.BY_CUSTOMER_RELATION.getSearchType()
                            && CollectionUtils.isNotEmpty(dataRangeCodeList)) {
                        userMarketingIds = getByCustomerRelation(ea, dataRangeCodeList, listCrmObjectResult.getData().getDataList());
                    } else {
                        userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, filterData.getObjectAPIName(), listCrmObjectResult.getData().getDataList(), true);
                    }
                    for (String marketingUserId : userMarketingIds) {
                        if (!Strings.isNullOrEmpty(marketingUserId) && !marketingUserIdSet.contains(marketingUserId)){
                            marketingUserIdSet.add(marketingUserId);
                            marketingUserIdResult.add(marketingUserId);
                        }
                    }
                    if (listCrmObjectResult.getData().getDataList().size() < crmBatchGetLimit){
                        break;
                    }
                    if (isPage && marketingUserIdSet.size() >= page.getPageNo() * page.getPageSize()){
                        return ListUtil.subListByPage(marketingUserIdResult, page.getPageNo(), page.getPageSize());
                    }
                }
            }
        } else{
            for (FilterData enterpriseLibraryFilter : filterDataList) {
                List<com.fxiaoke.crmrestapi.common.data.Filter> specialTagFilter = new ArrayList<>();
                Iterator<Filter> iterator = enterpriseLibraryFilter.getQuery().getFilters().iterator();
                Filter tempFilter;
                while(iterator.hasNext()){
                    tempFilter = iterator.next();
                    if("tag".equals(tempFilter.getFieldName())){
                        com.fxiaoke.crmrestapi.common.data.Filter queryFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
                        queryFilter.setFieldName("tag");
                        queryFilter.setOperator("IN".equals(tempFilter.getOperator()) ? "LIKE" : tempFilter.getOperator());
                        queryFilter.setFieldValues(tempFilter.getFieldValues());
                        queryFilter.setValueType(11);
                        specialTagFilter.add(queryFilter);
                        iterator.remove();
                    }
                }
                for (int crmBatchGetOffset = 0; crmBatchGetOffset < maxCrmQueryTime * crmBatchGetLimit; crmBatchGetOffset += crmBatchGetLimit) {
                    ControllerListArg enterpriseLibraryCrmListArg = new ControllerListArg();
                    enterpriseLibraryCrmListArg.setObjectDescribeApiName(enterpriseLibraryFilter.getObjectAPIName());
                    enterpriseLibraryCrmListArg.setSearchTemplateId(enterpriseLibraryFilter.getSearchTemplateId());
                    doMarkNotGetUnNesscessaryData(enterpriseLibraryCrmListArg);
                    enterpriseLibraryCrmListArg.setFieldProjection(Lists.newArrayList("_id"));
                    SearchQuery searchQuery = BeanUtil.copyByFastJson(enterpriseLibraryFilter.getQuery(), SearchQuery.class);
                    searchQuery.addOrderBy("last_modified_time", false);
                    searchQuery.setOffset(crmBatchGetOffset);
                    searchQuery.setLimit(crmBatchGetLimit);
                    searchQuery.setSearchSource("es");
                    searchQuery.getFilters().addAll(specialTagFilter);
                    enterpriseLibraryCrmListArg.setSearchQuery(searchQuery);
                    Result<Page<ObjectData>> listEnterpriseInfoResult = metadataControllerService.list(headerObj, enterpriseLibraryFilter.getObjectAPIName(), enterpriseLibraryCrmListArg);
                    if (listEnterpriseInfoResult.getCode() == CRM_NO_PERMISSION_ERROR_CODE){
                        break;
                    }
                    Preconditions.checkState(listEnterpriseInfoResult.isSuccess(), "CRM exception detected.");
                    if (listEnterpriseInfoResult.getData() == null || listEnterpriseInfoResult.getData().getDataList() == null || listEnterpriseInfoResult.getData().getDataList().isEmpty()){
                        break;
                    }
                    Set<String> tempEnterpriseIdSet = listEnterpriseInfoResult.getData().getDataList().stream().map(ObjectData::getId).collect(Collectors.toSet());
                    for (int crmLeadBatchGetOffset = 0; crmLeadBatchGetOffset < maxCrmQueryTime * crmBatchGetLimit; crmLeadBatchGetOffset+=crmBatchGetLimit) {
                        ControllerListArg listCrmLeadByEnterpriseInfoIdsArg = buildListCrmDataByEnterpriseIdArg(CrmObjectApiNameEnum.CRM_LEAD, tempEnterpriseIdSet, crmLeadBatchGetOffset, crmBatchGetLimit);
                        Result<Page<ObjectData>> listCrmLeadResult = metadataControllerService.list(headerObj, CrmObjectApiNameEnum.CRM_LEAD.getName(), listCrmLeadByEnterpriseInfoIdsArg);
                        if (listCrmLeadResult.getCode() == CRM_NO_PERMISSION_ERROR_CODE){
                            break;
                        }
                        Preconditions.checkState(listCrmLeadResult.isSuccess(), "CRM exception detected.");
                        if (listCrmLeadResult.getData() == null || listCrmLeadResult.getData().getDataList() == null || listCrmLeadResult.getData().getDataList().isEmpty()){
                            break;
                        }
                        List<String> userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), listCrmLeadResult.getData().getDataList(), true);
                        for (String marketingUserId : userMarketingIds) {
                            if (!Strings.isNullOrEmpty(marketingUserId) && !marketingUserIdSet.contains(marketingUserId)){
                                marketingUserIdSet.add(marketingUserId);
                                marketingUserIdResult.add(marketingUserId);
                            }
                        }
                        if (listCrmLeadResult.getData().getDataList().size() < crmBatchGetLimit){
                            break;
                        }
                        if (isPage && marketingUserIdSet.size() >= page.getPageNo() * page.getPageSize()){
                            return ListUtil.subListByPage(marketingUserIdResult, page.getPageNo(), page.getPageSize());
                        }
                    }
                    for (int crmCustomerBatchGetOffset = 0; crmCustomerBatchGetOffset < maxCrmQueryTime * crmBatchGetLimit; crmCustomerBatchGetOffset+=crmBatchGetLimit) {
                        ControllerListArg listCrmCustomerByEnterpriseInfoIdsArg = buildListCrmDataByEnterpriseIdArg(CrmObjectApiNameEnum.CUSTOMER, tempEnterpriseIdSet, crmCustomerBatchGetOffset, crmBatchGetLimit);
                        Result<Page<ObjectData>> listCrmCustomerResult = metadataControllerService.list(headerObj, CrmObjectApiNameEnum.CUSTOMER.getName(), listCrmCustomerByEnterpriseInfoIdsArg);
                        if (listCrmCustomerResult.getCode() == CRM_NO_PERMISSION_ERROR_CODE){
                            break;
                        }
                        Preconditions.checkState(listCrmCustomerResult.isSuccess(), "CRM exception detected.");
                        if (listCrmCustomerResult.getData() == null || listCrmCustomerResult.getData().getDataList() == null || listCrmCustomerResult.getData().getDataList().isEmpty()){
                            break;
                        }
                        List<String> userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CUSTOMER.getName(), listCrmCustomerResult.getData().getDataList(), true);
                        for (String marketingUserId : userMarketingIds) {
                            if (!Strings.isNullOrEmpty(marketingUserId) && !marketingUserIdSet.contains(marketingUserId)){
                                marketingUserIdSet.add(marketingUserId);
                                marketingUserIdResult.add(marketingUserId);
                            }
                        }

                        List<String> customerIdList = listCrmCustomerResult.getData().getDataList().stream().map(ObjectData::getId).collect(Collectors.toList());
                        // 查询客户的联系人
                        List<ObjectData> listCrmContactResult = getCustomerContactData(ea, customerIdList);
                        userMarketingIds = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CONTACT.getName(), listCrmContactResult, true);
                        for (String marketingUserId : userMarketingIds) {
                            if (!Strings.isNullOrEmpty(marketingUserId) && !marketingUserIdSet.contains(marketingUserId)){
                                marketingUserIdSet.add(marketingUserId);
                                marketingUserIdResult.add(marketingUserId);
                            }
                        }

                        if (listCrmCustomerResult.getData().getDataList().size() < crmBatchGetLimit){
                            break;
                        }
                        if (isPage && marketingUserIdSet.size() >= page.getPageNo() * page.getPageSize()){
                            return ListUtil.subListByPage(marketingUserIdResult, page.getPageNo(), page.getPageSize());
                        }
                    }
                }
            }
        }
        if (isPage) {
            return ListUtil.subListByPage(marketingUserIdResult, page.getPageNo(), page.getPageSize());
        }
        return marketingUserIdResult;
    }

    public List<ObjectData> getCustomerContactData(String ea, List<String> customerIdList) {
        if (CollectionUtils.isEmpty(customerIdList)) {
            return Lists.newArrayList();
        }
        com.fxiaoke.crmrestapi.common.data.Filter accountIdFilter = new com.fxiaoke.crmrestapi.common.data.Filter();
        accountIdFilter.setFieldName("account_id");
        accountIdFilter.setOperator(com.fxiaoke.crmrestapi.common.data.Filter.OperatorContants.IN);
        accountIdFilter.setFieldValues(customerIdList);
        int totalCount  = crmV2Manager.getObjTotalCountByFilter(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.CONTACT.getName(), Lists.newArrayList(accountIdFilter));
        if (totalCount <= 0) {
            return null;
        }

        int pageSize = 2;
        int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
        List<ObjectData> objectDataList = Lists.newArrayList();

        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CONTACT.getName());
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, pageSize);
        paasQueryArg.addFilter("account_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), customerIdList);
        queryFilterArg.setQuery(paasQueryArg);
        queryFilterArg.setSelectFields(Lists.newArrayList(FIELD_NAMES_REQUIRED_TO_ASSOCIATION));

        for(int i = 1; i <= totalPage; i++) {
            InnerPage<ObjectData> pageObjects = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, i, pageSize);
            if (pageObjects == null || CollectionUtils.isEmpty(pageObjects.getDataList())) {
                break;
            }
            objectDataList.addAll(pageObjects.getDataList());
        }
        return objectDataList;
    }

    private List<String> getByCustomerRelation(String ea, List<Integer> dataRangeCodeList, List<ObjectData> objectDataList) {
        Set<String> userMarketingIdSet = new HashSet<>();
        for (Integer dataRangeCode : dataRangeCodeList) {
            if (dataRangeCode == MarketingUserGroupDataRangeEnum.CUSTOMER.getCode()) {
                List<String> tempIdList = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CUSTOMER.getName(), objectDataList, true);
                if (CollectionUtils.isNotEmpty(tempIdList)) {
                    userMarketingIdSet.addAll(tempIdList);
                }
            } else if (dataRangeCode == MarketingUserGroupDataRangeEnum.LEAD.getCode()) {
                List<String> accountIdList = Lists.newArrayList();
                for (ObjectData objectData : objectDataList) {
                    accountIdList.add(objectData.getId());
                }
                if (CollectionUtils.isNotEmpty(accountIdList)) {
                    int pageSize = 100;
                    int maxPage = 500;
                    for (int i = 0; i < maxPage; i++) {
                        PaasQueryArg paasQueryArg = new PaasQueryArg(i * pageSize, pageSize);
                        paasQueryArg.addFilter(ContactFieldContants.ACCOUNT_ID, PaasAndCrmOperatorEnum.IN.getCrmOperator(), accountIdList);
                        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                        findByQueryV3Arg.setDescribeApiName("LeadsTransferLogObj");
                        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
                        InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, SuperUserConstants.USER_ID, findByQueryV3Arg);
                        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getDataList())) {
                            break;
                        }
                        List<String> ids = pageResult.getDataList().stream().filter(e -> Objects.nonNull(e.get("leads_id"))).map(e -> String.valueOf(e.get("leads_id"))).collect(Collectors.toList());
                        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
                        paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.CRM_LEAD.getName());
                        PaasQueryArg query = new PaasQueryArg(0, 1);
                        query.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), ids);
                        paasQueryFilterArg.setQuery(query);
                        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, paasQueryFilterArg);
                        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                            break;
                        }
                        List<String> tempIdList = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CRM_LEAD.getName(), objectDataInnerPage.getDataList(), true);
                        userMarketingIdSet.addAll(tempIdList);
                    }
                }
            } else if (dataRangeCode == MarketingUserGroupDataRangeEnum.CONTACT.getCode()) {
                List<String> accountIdList = Lists.newArrayList();
                for (ObjectData objectData : objectDataList) {
                    accountIdList.add(objectData.getId());
                }
                if (CollectionUtils.isNotEmpty(accountIdList)) {
                    int pageSize = 100;
                    int maxPage = 500;
                    for (int i = 0; i < maxPage; i++) {
                        PaasQueryArg paasQueryArg = new PaasQueryArg(i * pageSize, pageSize);
                        paasQueryArg.addFilter(ContactFieldContants.ACCOUNT_ID, PaasAndCrmOperatorEnum.IN.getCrmOperator(), accountIdList);
                        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                        findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.CONTACT.getName());
                        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
                        InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, SuperUserConstants.USER_ID, findByQueryV3Arg);
                        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getDataList())) {
                            break;
                        }
                        List<String> tempIdList = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.CONTACT.getName(), pageResult.getDataList(), true);
                        userMarketingIdSet.addAll(tempIdList);
                    }
                }
            } else if (dataRangeCode == MarketingUserGroupDataRangeEnum.WECHATWORKEXTERNALUSER.getCode()) {
                // 客户->企微好友记录->企微客户
                List<String> accountIdList = Lists.newArrayList();
                for (ObjectData objectData : objectDataList) {
                    accountIdList.add(objectData.getId());
                }
                if (CollectionUtils.isNotEmpty(accountIdList)) {
                    int pageSize = 100;
                    int maxPage = 500;
                    for (int i = 0; i < maxPage; i++) {
                        PaasQueryArg paasQueryArg = new PaasQueryArg(i * pageSize, pageSize);
                        paasQueryArg.addFilter("custom_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), accountIdList);
                        // 这里不再进行额外过滤, 营销通的企微好友记录才关联客户
                        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
                        findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
                        findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryArg));
                        InnerPage<ObjectData> pageResult = crmMetadataManager.listV3(ea, SuperUserConstants.USER_ID, findByQueryV3Arg);
                        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getDataList())) {
                            break;
                        }
                        List<String> wechatWorkextErnalUserIdList = pageResult.getDataList().stream()
                                .map(e -> e.getString("external_user_id"))
                                .filter(StringUtils::isNotEmpty)
                                .collect(Collectors.toList());
                        PaasQueryArg finalPaasQueryArg = new PaasQueryArg(0, 1);
                        finalPaasQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), wechatWorkextErnalUserIdList);
                        List<ObjectData> wechatWorkExternalUserObjList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), null, finalPaasQueryArg);
                        if (CollectionUtils.isEmpty(wechatWorkExternalUserObjList)) {
                            continue;
                        }
                        List<String> tempIdList = this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), wechatWorkExternalUserObjList, true);
                        userMarketingIdSet.addAll(tempIdList);
                    }
                }
            }
        }
        return Lists.newArrayList(userMarketingIdSet);
    }

    // 根据企业库id查看关联的对象数据
    private List<ObjectData> queryListCrmDataByEnterpriseId(String ea, CrmObjectApiNameEnum crmObjectApiNameEnum, Set<String> enterpriseIdSet) {

        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(crmObjectApiNameEnum.getName());
        queryFilterArg.setSelectFields(Lists.newArrayList(FIELD_NAMES_REQUIRED_TO_ASSOCIATION));

        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        paasQueryArg.addFilter("enterprise_id", FilterOperatorEnum.IN.getValue(), Lists.newArrayList(enterpriseIdSet));
        queryFilterArg.setQuery(paasQueryArg);

        List<ObjectData> resultList = Lists.newArrayList();
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            return resultList;
        }
        int count = 0;
        String lastId = null;
        while(count < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, 1000);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            resultList.addAll(objectDataList);
            count += objectDataList.size();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
        }
        return resultList;
    }

    //    @NotNull
    private ControllerListArg buildListCrmDataByEnterpriseIdArg(CrmObjectApiNameEnum crmObjectApiNameEnum, Set<String> enterpriseIdSet, int offset, int limit) {
        ControllerListArg listCrmLeadByEnterpriseInfoIdsArg = new ControllerListArg();
        listCrmLeadByEnterpriseInfoIdsArg.setObjectDescribeApiName(crmObjectApiNameEnum.getName());
        doMarkNotGetUnNesscessaryData(listCrmLeadByEnterpriseInfoIdsArg);
        listCrmLeadByEnterpriseInfoIdsArg.setFieldProjection(new ArrayList<>(FIELD_NAMES_REQUIRED_TO_ASSOCIATION));
        SearchQuery searchQueryLeadByEnterpriseInfoIds = new SearchQuery();
        searchQueryLeadByEnterpriseInfoIds.addOrderBy("last_modified_time", false);
        searchQueryLeadByEnterpriseInfoIds.addFilter("enterprise_id", new ArrayList<>(enterpriseIdSet), FilterOperatorEnum.IN);
        searchQueryLeadByEnterpriseInfoIds.setOffset(offset);
        searchQueryLeadByEnterpriseInfoIds.setLimit(limit);
        listCrmLeadByEnterpriseInfoIdsArg.setSearchQuery(searchQueryLeadByEnterpriseInfoIds);
        return listCrmLeadByEnterpriseInfoIdsArg;
    }
    
    private void doMarkNotGetUnNesscessaryData(ControllerListArg listCrmLeadByEnterpriseInfoIdsArg) {
        listCrmLeadByEnterpriseInfoIdsArg.setIncludeLayout(false);
        listCrmLeadByEnterpriseInfoIdsArg.setIncludeDescribe(false);
        listCrmLeadByEnterpriseInfoIdsArg.setIncludeButtonInfo(false);
    }
    
    private Filter buildChannelDataFilter(List<String> finalChannelDataIds) {
        Filter channelDataIdsFilter = new Filter();
        channelDataIdsFilter.setFieldName(ObjectDescribeContants.ID);
        channelDataIdsFilter.setOperator(OperatorConstants.IN);
        channelDataIdsFilter.setFieldValues(finalChannelDataIds);
        return channelDataIdsFilter;
    }

    //获取标签以外的其他筛选条件
    private List<Filter> getFilterByNotIsTagFilter(List<Filter> filters) {
        List<Filter> result = new ArrayList<>();
        for (Filter filter : filters) {
            //如果有多个标签筛选条件，先默认只取第一个
            if (!TagRuleRuleContants.FIELDNAME.equals(filter.getFieldName())) {
                result.add(filter);
            }
        }
        return result;
    }

    //根据邮箱查询营销用户id
    public List<ObjectIdWithMarketingUserIdAndPhoneDTO> queryMarketingUserDTOByEmail(String email,List<String> objectApiNames,String ea) {
        //查询顺序 线索 -> 客户 -> 联系人
        List<ObjectIdWithMarketingUserIdAndPhoneDTO> marketingUserDTOList = new ArrayList<>();
        Map<String,List<String>> objectApiNameToIdsMap = new HashMap<>();
        //根据邮箱查询对象数据id
        for (String objectApiName : objectApiNames) {
            List<String> objectIds = new ArrayList<>();
            PaasQueryFilterArg queryRelationFilterArg = new PaasQueryFilterArg();
            PaasQueryArg queryRelation = new PaasQueryArg(0, 1);
            queryRelation.addFilter("email", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(),Lists.newArrayList(email));
            queryRelationFilterArg.setQuery(queryRelation);
            queryRelationFilterArg.setObjectAPIName(objectApiName);
            queryRelationFilterArg.setSelectFields(Lists.newArrayList("_id"));
            int totalRelationCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg);
            if (totalRelationCount <= 0) {
                continue;
            }
            int currentRelationCount = 0;
            String lastRelationId = null;
            while (currentRelationCount < totalRelationCount) {
                InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryRelationFilterArg, lastRelationId, 500);
                if (objectDataInnerPage == null || org.apache.commons.collections4.CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                    break;
                }
                objectIds.addAll(objectDataInnerPage.getDataList().stream().map(ObjectData::getId).collect(Collectors.toList()));
                int tempSize = objectDataInnerPage.getDataList().size();
                currentRelationCount += tempSize;
                lastRelationId = objectDataInnerPage.getDataList().get(tempSize - 1).getId();
            }
            objectApiNameToIdsMap.putIfAbsent(objectApiName, objectIds);
        }
        //根据对象数据id,查询营销用户id
        for (String objectApiName : objectApiNames) {
            List<String> objectIds = objectApiNameToIdsMap.get(objectApiName);
            if (CollectionUtils.isEmpty(objectIds)) {
                continue;
            }
            List<ObjectIdWithMarketingUserIdAndPhoneDTO> objectIdWithMarketingUserIdAndPhoneList = Lists.newArrayList();
            if(CrmObjectApiNameEnum.CUSTOMER.getName().equals(objectApiName)){
                objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmAccountAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
            } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(objectApiName)){
                objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmContactAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
            } else if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(objectApiName)){
                objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmLeadAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
            }
            if (CollectionUtils.isEmpty(objectIdWithMarketingUserIdAndPhoneList)) {
                continue;
            }
            marketingUserDTOList.addAll(objectIdWithMarketingUserIdAndPhoneList);
        }
        if (CollectionUtils.isEmpty(marketingUserDTOList)) {
            return marketingUserDTOList;
        }
        //将营销活动id去重
        Map<String, ObjectIdWithMarketingUserIdAndPhoneDTO> uniqueMap = marketingUserDTOList.stream()
                .collect(Collectors.toMap(ObjectIdWithMarketingUserIdAndPhoneDTO::getUserMarketingId, dto -> dto,
                        (existing, replacement) -> existing));
        return new ArrayList<>(uniqueMap.values());
    }

    public List<String> associateAncGetUserMarketingAccountIdsByCrmObjectDatas(String ea, String crmObjectApiName, List<ObjectData> objectDatas, boolean isAsync) {
        if (CollectionUtils.isEmpty(objectDatas)) {
            return Lists.newArrayList();
        }
        long startTime = System.currentTimeMillis();
        Set<String> objectIds = objectDatas.stream().map(ObjectData::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (objectIds.isEmpty()){
            return new ArrayList<>(0);
        }
        boolean isCustomizeObject = false;
        List<ObjectIdWithMarketingUserIdAndPhoneDTO> objectIdWithMarketingUserIdAndPhoneList = new ArrayList<>(0);
        for (List<String> partObjectIds : Lists.partition(Lists.newArrayList(objectIds), 1000)) {
            if(CrmObjectApiNameEnum.CUSTOMER.getName().equals(crmObjectApiName)){
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmAccountAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, partObjectIds));
            } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(crmObjectApiName)){
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmContactAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, partObjectIds));
            } else if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(crmObjectApiName)){
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmLeadAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, partObjectIds));
            } else if (CrmObjectApiNameEnum.MEMBER.getName().equals(crmObjectApiName)){
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmMemberRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, partObjectIds));
            } else if (CrmObjectApiNameEnum.WECHAT.getName().equals(crmObjectApiName)){
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmWxUserAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, partObjectIds));
            } else if (CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(crmObjectApiName)){
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmWxWorkExternalUserRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, partObjectIds));
            }else {
                //支持自定义对象
                isCustomizeObject = true;
                objectIdWithMarketingUserIdAndPhoneList.addAll(userMarketingCrmCustomizeObjectRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, crmObjectApiName, partObjectIds));
            }
        }

        Map<String, ObjectIdWithMarketingUserIdAndPhoneDTO> objectIdToMarketingUserIdAndPhoneMap = objectIdWithMarketingUserIdAndPhoneList.stream().collect(Collectors.toMap(ObjectIdWithMarketingUserIdAndPhoneDTO::getObjectId, d -> d, (v1, v2) -> v1));

        List<String> userMarketingIds = new LinkedList<>();
        List<ObjectData> objectDataNeedToAssociate = new LinkedList<>();
        MarketingUserGroupCustomizeObjectMappingEntity customizeObjectMappingEntity = marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(ea, crmObjectApiName);
        for (ObjectData objectData : objectDatas) {
            String objectPhone = getPhoneFromObjectData(objectData, customizeObjectMappingEntity);
            ObjectIdWithMarketingUserIdAndPhoneDTO objectIdWithMarketingUserIdAndPhone = objectIdToMarketingUserIdAndPhoneMap.get(objectData.getId());
            if (objectIdWithMarketingUserIdAndPhone != null && (Strings.isNullOrEmpty(objectPhone) || objectPhone.equals(objectIdWithMarketingUserIdAndPhone.getPhone()))){
                userMarketingIds.add(objectIdWithMarketingUserIdAndPhone.getUserMarketingId());
            }else{
                objectDataNeedToAssociate.add(objectData);
            }
        }

        if (CollectionUtils.isNotEmpty(objectDataNeedToAssociate)) {
            List<String> newUserMarketingIds = Lists.newCopyOnWriteArrayList();
            boolean finalIsCustomizeObject = isCustomizeObject;
            if (isAsync) {
                ExecutorService threadPools = Executors.newFixedThreadPool(Math.min(objectDataNeedToAssociate.size(), 5));
                CountDownLatch countDownLatch = new CountDownLatch(objectDataNeedToAssociate.size());
                TraceContext context = TraceContext.get();
                for (Map<String, Object> data : objectDataNeedToAssociate) {
                    threadPools.submit(() -> {
                        TraceContext._set(context);
                        String userMarketingId = getUserMarketingByObjectData(ea, data, finalIsCustomizeObject, customizeObjectMappingEntity);
                        if (StringUtils.isNotEmpty(userMarketingId)) {
                            newUserMarketingIds.add(userMarketingId);
                        }
                        countDownLatch.countDown();
                    });
                }
                try {
                    countDownLatch.await(3, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.warn("associateAncGetUserMarketingAccountIdsByCrmObjectDatas objectDataNeedToAssociate timeout");
                }
                if (!threadPools.isShutdown()) {
                    threadPools.shutdown();
                }
            } else {
                for (Map<String, Object> data : objectDataNeedToAssociate) {
                    String userMarketingId = getUserMarketingByObjectData(ea, data, finalIsCustomizeObject, customizeObjectMappingEntity);
                    if (StringUtils.isNotEmpty(userMarketingId)) {
                        newUserMarketingIds.add(userMarketingId);
                    }
                }
            }
            userMarketingIds.addAll(newUserMarketingIds);
        }
        userMarketingIds = userMarketingIds.stream().distinct().collect(Collectors.toList());
        log.info("associateAncGetUserMarketingAccountIdsByCrmObjectDatas cost:{} ea:{} dataSize:{}", System.currentTimeMillis() - startTime, ea, objectDatas.size());
        return userMarketingIds;
    }

    private String getUserMarketingByObjectData(String ea, Map<String, Object> data, boolean finalIsCustomizeObject, MarketingUserGroupCustomizeObjectMappingEntity customizeObjectMappingEntity) {
        AssociationArg associationArg = new AssociationArg();
        associationArg.setAssociationId((String) data.get(ObjectDescribeContants.ID));
        associationArg.setEa(ea);
        String apiName = (String) data.get(ObjectDescribeContants.DESCRIBE_API_NAME);
        associationArg.setObjectApiName(apiName);
        associationArg.setTriggerAction("associateAncGetUserMarketingAccountIdsByCrmObjectDatas");
        if (apiName.equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            String phone = (String) data.get(CrmLeadFieldEnum.MOBILE.getFieldName());
            if (StringUtils.isEmpty(phone)) {
                phone = (String) data.get(CrmLeadFieldEnum.TEL.getFieldName());
            }
            associationArg.setPhone(phone);
            associationArg.setType(ChannelEnum.CRM_LEAD.getType());
            associationArg.setTriggerSource(ChannelEnum.CRM_LEAD.getDescription());
        } else if (apiName.equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
            associationArg.setPhone((String) data.get(CrmCustomerFieldEnum.TEL.getFieldName()));
            associationArg.setType(ChannelEnum.CRM_ACCOUNT.getType());
            associationArg.setTriggerSource(ChannelEnum.CRM_ACCOUNT.getDescription());
        } else if (apiName.equals(CrmObjectApiNameEnum.CONTACT.getName())) {
            String phone = (String) data.get(CrmContactFieldEnum.MOBILE.getFieldName());
            if (StringUtils.isEmpty(phone)) {
                phone = (String) data.get(CrmContactFieldEnum.TEL.getFieldName());
            }
            associationArg.setPhone(phone);
            associationArg.setType(ChannelEnum.CRM_CONTACT.getType());
            associationArg.setTriggerSource(ChannelEnum.CRM_CONTACT.getDescription());
        } else if (apiName.equals(CrmObjectApiNameEnum.WECHAT.getName())) {
            associationArg.setType(ChannelEnum.CRM_WX_USER.getType());
            associationArg.setWxAppId((String) data.get(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()));
            associationArg.setAdditionalAssociationId((String) data.get(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
            associationArg.setTriggerSource(ChannelEnum.CRM_WX_USER.getDescription());
        } else if (apiName.equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())) {
            associationArg.setPhone((String) data.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()));
            associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
            associationArg.setAdditionalAssociationId((String) data.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()));
            associationArg.setTriggerSource(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription());
        } else if (apiName.equals(CrmObjectApiNameEnum.MEMBER.getName())) {
            associationArg.setPhone((String) data.get(CrmMemberFieldEnum.PHONE.getApiName()));
            associationArg.setType(ChannelEnum.CRM_MEMBER.getType());
            associationArg.setTriggerSource(ChannelEnum.CRM_MEMBER.getDescription());
        }

        if (finalIsCustomizeObject) {
            //从自定义对象中获取手机号
            associationArg.setType(ChannelEnum.CUSTOMIZE_OBJECT.getType());
            if (customizeObjectMappingEntity != null) {
                String crmPhoneField = customizeObjectMappingEntity.getMappingFieldByMarketingField(MarketingUserGroupCustomizeObjectMappingEnum.MOBILE.getType());
                String crmNameField = customizeObjectMappingEntity.getMappingFieldByMarketingField(MarketingUserGroupCustomizeObjectMappingEnum.NAME.getType());
                String crmMailField = customizeObjectMappingEntity.getMappingFieldByMarketingField(MarketingUserGroupCustomizeObjectMappingEnum.MAIL.getType());
                if (crmPhoneField != null) {
                    associationArg.setPhone((String) data.get(crmPhoneField));
                }
                if (crmNameField != null) {
                    associationArg.setUserName((String) data.get(crmNameField));
                }
                if (crmMailField != null) {
                    associationArg.setEmail((String) data.get(crmMailField));
                }
            }
        } else {
            associationArg.setUserName((String) data.get("name"));
            associationArg.setEmail((String) data.get("email"));
        }
        try {
            AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
            if (result != null) {
                return result.getUserMarketingAccountId();
            }
        } catch (Exception ex) {
            log.error("associate fail, associationArg: {}", associationArg, ex);
        }
        return null;
    }

    public Map<String, String> associateAncGetObjectIdToMarketingUserIdMap(String ea, String crmObjectApiName, List<ObjectData> objectDatas) {
        long startTime = System.currentTimeMillis();
        Set<String> objectIds = objectDatas.stream().map(ObjectData::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (objectIds.isEmpty()){
            return new HashMap<>(0);
        }
        Map<String, String> resultMap = new HashMap<>(objectDatas.size());
        List<ObjectIdWithMarketingUserIdAndPhoneDTO> objectIdWithMarketingUserIdAndPhoneList = new ArrayList<>(0);
        if(CrmObjectApiNameEnum.CUSTOMER.getName().equals(crmObjectApiName)){
            objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmAccountAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
        } else if (CrmObjectApiNameEnum.CONTACT.getName().equals(crmObjectApiName)){
            objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmContactAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
        } else if (CrmObjectApiNameEnum.CRM_LEAD.getName().equals(crmObjectApiName)){
            objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmLeadAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
        } else if (CrmObjectApiNameEnum.MEMBER.getName().equals(crmObjectApiName)){
            objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmMemberRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
        } else if (CrmObjectApiNameEnum.WECHAT.getName().equals(crmObjectApiName)){
            objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmWxUserAccountRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
        } else if (CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(crmObjectApiName)){
            objectIdWithMarketingUserIdAndPhoneList = userMarketingCrmWxWorkExternalUserRelationDao.listMarketingUserIdAndPhoneByObjectIds(ea, objectIds);
        }
        Map<String, ObjectIdWithMarketingUserIdAndPhoneDTO> objectIdToMarketingUserIdAndPhoneMap = objectIdWithMarketingUserIdAndPhoneList.stream().collect(Collectors.toMap(ObjectIdWithMarketingUserIdAndPhoneDTO::getObjectId, d -> d, (v1, v2) -> v1));
        
        List<ObjectData> objectDataNeedToAssociate = new LinkedList<>();
        MarketingUserGroupCustomizeObjectMappingEntity customizeObjectMappingEntity = marketingUserGroupCustomizeObjectMappingDao.getByObjectApiName(ea, crmObjectApiName);
        for (ObjectData objectData : objectDatas) {
            String objectPhone = getPhoneFromObjectData(objectData, customizeObjectMappingEntity);
            ObjectIdWithMarketingUserIdAndPhoneDTO objectIdWithMarketingUserIdAndPhone = objectIdToMarketingUserIdAndPhoneMap.get(objectData.getId());
            if (objectIdWithMarketingUserIdAndPhone != null && (Strings.isNullOrEmpty(objectPhone) || objectPhone.equals(objectIdWithMarketingUserIdAndPhone.getPhone()))){
                resultMap.put(objectData.getId(), objectIdWithMarketingUserIdAndPhone.getUserMarketingId());
            }else{
                objectDataNeedToAssociate.add(objectData);
            }
        }
        
        for (Map<String, Object> data : objectDataNeedToAssociate) {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setAssociationId((String) data.get(ObjectDescribeContants.ID));
            associationArg.setEa(ea);
            associationArg.setObjectApiName(crmObjectApiName);
            String apiName = (String) data.get(ObjectDescribeContants.DESCRIBE_API_NAME);
            if (apiName.equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
                String phone = (String) data.get(CrmLeadFieldEnum.MOBILE.getFieldName());
                if (StringUtils.isEmpty(phone)) {
                    phone = (String) data.get(CrmLeadFieldEnum.TEL.getFieldName());
                }
                associationArg.setPhone(phone);
                associationArg.setType(ChannelEnum.CRM_LEAD.getType());
            } else if (apiName.equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
                associationArg.setPhone((String) data.get(CrmCustomerFieldEnum.TEL.getFieldName()));
                associationArg.setType(ChannelEnum.CRM_ACCOUNT.getType());
            } else if (apiName.equals(CrmObjectApiNameEnum.CONTACT.getName())) {
                String phone = (String) data.get(CrmContactFieldEnum.MOBILE.getFieldName());
                if (StringUtils.isEmpty(phone)) {
                    phone = (String) data.get(CrmContactFieldEnum.TEL.getFieldName());
                }
                associationArg.setPhone(phone);
                associationArg.setType(ChannelEnum.CRM_CONTACT.getType());
            } else if (apiName.equals(CrmObjectApiNameEnum.WECHAT.getName())) {
                associationArg.setType(ChannelEnum.CRM_WX_USER.getType());
                associationArg.setWxAppId((String)data.get(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()));
                associationArg.setAdditionalAssociationId((String)data.get(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
            } else if(apiName.equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())){
                associationArg.setPhone((String) data.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()));
                associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
                associationArg.setAdditionalAssociationId((String) data.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()));
            } else if (apiName.equals(CrmObjectApiNameEnum.MEMBER.getName())){
                associationArg.setPhone((String) data.get(CrmMemberFieldEnum.PHONE.getApiName()));
                associationArg.setType(ChannelEnum.CRM_MEMBER.getType());
            }
            associationArg.setUserName((String)data.get("name"));
            associationArg.setEmail((String)data.get("email"));
            try {
                AssociationResult result = userMarketingAccountAssociationManager.associate(associationArg);
                if (result != null) {
                    resultMap.put((String) data.get(ObjectDescribeContants.ID), result.getUserMarketingAccountId());
                }
            } catch (Exception ex) {
                log.error("associate fail,ex:{}", ex);
            }
        }
        log.info("associateAncGetObjectIdToMarketingUserIdMap cost:{} ea:{} dataSize:{}", System.currentTimeMillis() - startTime, ea, objectDatas.size());
        return resultMap;
    }
    
    public String getPhoneFromObjectData(Map<String, Object> data, MarketingUserGroupCustomizeObjectMappingEntity customizeObjectMappingEntity){
        String phone = null;
        String apiName = (String) data.get(ObjectDescribeContants.DESCRIBE_API_NAME);
        if (apiName.equals(CrmObjectApiNameEnum.CRM_LEAD.getName())) {
            phone = (String) data.get(CrmLeadFieldEnum.MOBILE.getFieldName());
            if (StringUtils.isEmpty(phone)) {
                phone = (String) data.get(CrmLeadFieldEnum.TEL.getFieldName());
            }
        } else if (apiName.equals(CrmObjectApiNameEnum.CUSTOMER.getName())) {
            phone = (String) data.get(CrmCustomerFieldEnum.TEL.getFieldName());
        } else if (apiName.equals(CrmObjectApiNameEnum.CONTACT.getName())) {
            phone = (String) data.get(CrmContactFieldEnum.MOBILE.getFieldName());
            if (StringUtils.isEmpty(phone)) {
                phone = (String) data.get(CrmContactFieldEnum.TEL.getFieldName());
            }
        } else if (apiName.equals(CrmObjectApiNameEnum.WECHAT.getName())) {
            phone = (String) data.get(CrmWechatFanFieldEnum.PHONE.getFieldName());
        } else if(apiName.equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())){
            phone = (String) data.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName());
        } else if (apiName.equals(CrmObjectApiNameEnum.MEMBER.getName())){
            phone = (String) data.get(CrmMemberFieldEnum.PHONE.getApiName());
        }else if (customizeObjectMappingEntity != null){
            //从自定义对象中获取手机号
            phone = customizeObjectMappingEntity.getMappingFieldByMarketingField(MarketingUserGroupCustomizeObjectMappingEnum.MOBILE.getType());
        }
        return UserMarketingAccountAssociationManager.getFirstPhone(phone);
    }

    /**
     * 同步批量的营销用户，到CRM营销用户对象中
     *
     * @param ea 企业账号
     * @param userMarketingIds 批量的本地营销用户id
     * @return 本地营销用户id和crm营销用户id的映射关系, key为本地id，value为crmId
     */
    public Map<String, String> syncUserMarketingListToCrm(String ea, Collection<String> userMarketingIds) {
        for (String userMarketingId : userMarketingIds) {
            syncUserMarketingToCrm(ea, userMarketingId);
        }
        return batchGetCrmUserMarketingIdsByUserMarketingIds(ea, userMarketingIds);
    }

    /**
     * @param ea 企业账号
     * @param userMarketingId 本地营销用户id
     * @return crmUserMarketingId
     */
    public String getCrmUserMarketingIdByUserMarketingId(String ea, String userMarketingId) {
        Map<String, String> datas = batchGetCrmUserMarketingIdsByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (MapUtils.isEmpty(datas)) {
            return null;
        }
        return datas.get(userMarketingId);
    }



    /**
     * 通过本地营销用户ID批量获取crm营销用户对象的id
     *
     * @param ea 企业账号
     * @param userMarketingIds 批量的本地营销用户id
     * @return 本地营销用户id和crm营销用户id的映射关系, key为本地id，value为crmId
     */
    public Map<String, String> batchGetCrmUserMarketingIdsByUserMarketingIds(String ea, Collection<String> userMarketingIds) {
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        List<String> usefulUserMarketingIds = userMarketingAccountDAO.batchGetWithHasSyncToCrm(ei, Lists.newArrayList(userMarketingIds));
        if (CollectionUtils.isEmpty(usefulUserMarketingIds)) {
            return null;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(usefulUserMarketingIds.size());
        searchTemplateQuery.setOffset(0);
        Filter filter = new Filter();
        filter.setFieldName(UserMarketingAccountContants.NAME);
        filter.setFieldValues(new ArrayList<>(usefulUserMarketingIds));
        filter.setOperator(OperatorConstants.IN);
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName(UserMarketingAccountContants.API_NAME);
        filterData.setQuery(searchTemplateQuery);
        List<ObjectData> crmUserMarketingList = crmMetadataManager.listCrmObjectsByFilter(ea, -10000, filterData).getDataList();
        Map<String, String> idToCrmIdMap = new HashMap<>();
        for (Map<String, Object> crmUserMarketing : crmUserMarketingList) {
            idToCrmIdMap.put((String) crmUserMarketing.get(UserMarketingAccountContants.NAME), (String) crmUserMarketing.get(ObjectDescribeContants.ID));
        }
        return idToCrmIdMap;
    }

    /**
     * 同步营销用户，到CRM营销用户对象中
     */
    public void syncUserMarketingToCrm(String ea, String userMarketingAccountId) {
        ObjectData objectData = new ObjectData(UserMarketingAccountContants.API_NAME);
        objectData.put(UserMarketingAccountContants.NAME, userMarketingAccountId);
        objectData.put(ObjectDescribeContants.OWNER, Lists.newArrayList("-10000"));
        String crmUserMarketingId = getCrmUserMarketingIdByUserMarketingId(ea, userMarketingAccountId);
        if (StringUtils.isEmpty(crmUserMarketingId)) {
            crmMetadataManager.addMetadata(ea, -10000, UserMarketingAccountContants.API_NAME, objectData);
        }
        UserMarketingAccountEntity entity = userMarketingAccountDAO.getById(userMarketingAccountId);
        entity.setIsSyncToCrm(true);
        userMarketingAccountDAO.update(entity);
    }

    public List<ObjectData> pageGetObjectData(String ea, List<String> externalUser, int limit, int offset) {
        List<QueryContactListResult> res = new ArrayList<>();
        Filter channelDataIdsFilter = new Filter();
        channelDataIdsFilter.setFieldName("external_user_id");
        channelDataIdsFilter.setOperator(OperatorConstants.IN);
        channelDataIdsFilter.setFieldValues(externalUser);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(Lists.newArrayList(channelDataIdsFilter));
        List<OrderBy> orderByList = new ArrayList<>();
        orderByList.add(new OrderBy("_id", true));
        searchTemplateQuery.setOrders(orderByList);
        FilterData filterData = new FilterData();
        filterData.setQuery(searchTemplateQuery);
        filterData.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        CrmObjectResult crmObjectResult = crmMetadataManager.listLargeCrmObjectsByFilter(ea, -10000, filterData, FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
        List<Map<String, Object>> objectDatas = crmObjectResult.getDataList();
        List<ObjectData> objectDataList = objectDatas.stream().map(m -> {
            ObjectData od = new ObjectData();
            od.putAll(m);
            return od;
        }).collect(Collectors.toList());
        return objectDataList;
    }

    public Integer getOwner(String ea, String externalUser) {
        Filter channelDataIdsFilter = new Filter();
        channelDataIdsFilter.setFieldName("external_user_id");
        channelDataIdsFilter.setOperator(OperatorConstants.IN);
        channelDataIdsFilter.setFieldValues(Arrays.asList(externalUser));
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(Lists.newArrayList(channelDataIdsFilter));
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(10000);
        FilterData filterData = new FilterData();
        filterData.setQuery(searchTemplateQuery);
        filterData.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        CrmObjectResult crmObjectResult = crmMetadataManager.listLargeCrmObjectsByFilter(ea, -10000, filterData, FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
        List<Map<String, Object>> objectDatas = crmObjectResult.getDataList();
        for (Map<String, Object> objectData : objectDatas) {
            if (null == objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) || null == objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName())) {
                continue;
            }
            List<String> ownerList = (List<String>) objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName());
            if(CollectionUtils.isNotEmpty(ownerList)){
                return Integer.valueOf(ownerList.get(0));
            }
        }
        return null;
    }

    public Map<Integer,List<String>> getOwnerToExtenalUserMap(String ea,List<String> externalUser){
        Map<Integer,List<String>> res = new HashMap<>();
        if (CollectionUtils.isEmpty(externalUser)) {
            return res;
        }
        for (List<String> partExternalUser : Lists.partition(externalUser, 1000)) {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("external_user_id", OperatorConstants.IN, partExternalUser);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            paasQueryFilterArg.setSelectFields(Lists.newArrayList(FIELD_NAMES_REQUIRED_TO_ASSOCIATION));
            crmV2Manager.listCrmObjectScanByIdAndHandle(ea, -10000, paasQueryFilterArg,1000, objectData -> {
                if (null == objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()) || null == objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName())) {
                    return;
                }
                int owner = Integer.valueOf(((List<String>) objectData.get(CrmWechatWorkExternalUserFieldEnum.OWNER.getFieldName())).get(0));
                if(!res.containsKey(owner)){
                    List<String> temp = new ArrayList<>();
                    temp.add(String.valueOf(objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName())));
                    res.put(owner,temp);
                    return;
                }
                res.get(owner).add(String.valueOf(objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName())));
            });
        }

        return res;
    }

    public Map<Integer,List<String>> getOwnerToExtenalUserMapV2(String ea,List<String> externalUser){
        Map<Integer,List<String>> res = new HashMap<>();
        Filter channelDataIdsFilter = new Filter();
        channelDataIdsFilter.setFieldName("external_user_id");
        channelDataIdsFilter.setOperator(OperatorConstants.IN);
        channelDataIdsFilter.setFieldValues(externalUser);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(Lists.newArrayList(channelDataIdsFilter));
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(10000);
        FilterData filterData = new FilterData();
        filterData.setQuery(searchTemplateQuery);
        filterData.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        CrmObjectResult crmObjectResult = crmMetadataManager.listLargeCrmObjectsByFilter(ea, -10000, filterData, FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
        List<Map<String, Object>> externalUserObjectDatas = crmObjectResult.getDataList();

        Map<String, String> objIdToObj = new HashMap<>();
        for (Map<String, Object> objectData : externalUserObjectDatas) {
            objIdToObj.put(String.valueOf(objectData.get(CrmWechatWorkExternalUserFieldEnum.ID.getFieldName())), String.valueOf(objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName())));
        }

        if (!objIdToObj.isEmpty()) {
            List<Map<String, Object>> objectDatas = this.getWechatFriendsRecordObj(ea, new ArrayList<>(objIdToObj.keySet()));
            for (Map<String, Object> objectData : objectDatas) {
                Object objId = objectData.get(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName());
                if (null == objId || null == objIdToObj.get(String.valueOf(objId)) || null == objectData.get("user_id")) {
                    continue;
                }
                int owner = Integer.valueOf(((List<String>) objectData.get("user_id")).get(0));
                if (!res.containsKey(owner)) {
                    List<String> temp = new ArrayList<>();
                    temp.add(objIdToObj.get(String.valueOf(objId)));
                    res.put(owner, temp);
                    continue;
                }
                res.get(owner).add(objIdToObj.get(objIdToObj.get(String.valueOf(objId))));
            }
        }
        return res;
    }

    private List<Map<String, Object>> getWechatFriendsRecordObj(String ea,List<String> objIds) {
        Filter channelDataIdsFilter = new Filter();
        channelDataIdsFilter.setFieldName("external_user_id");
        channelDataIdsFilter.setOperator(OperatorConstants.IN);
        channelDataIdsFilter.setFieldValues(objIds);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(Lists.newArrayList(channelDataIdsFilter));
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(10000);
        FilterData filterData = new FilterData();
        filterData.setQuery(searchTemplateQuery);
        filterData.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
        CrmObjectResult crmObjectResult = crmMetadataManager.listLargeCrmObjectsByFilter(ea, -10000, filterData, FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
        return crmObjectResult.getDataList();
    }

    public List<String> listUserMarketingAccountIdsByTagNameList(String ea, Integer fsUserId, List<String> objectDescribeApiNames, String operator, List<TagName> tagNameList, int offset, int limit) {
        if (CollectionUtils.isEmpty(objectDescribeApiNames) || (CollectionUtils.isEmpty(tagNameList)) && StringUtils.isEmpty(operator)) {
            return null;
        }
        List<String> finalUserMarketingAccountIds = Lists.newArrayList();
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        List<Integer> dataPermission = null;
        if (isOpen) {
            dataPermission = dataPermissionManager.getDataPermission(ea, fsUserId);
        }
        for (String objectDescribeApiName : objectDescribeApiNames) {
            List<String> dataIds = metadataTagManager.getObjectDataIdsByTagNameList(ea, fsUserId, objectDescribeApiName, operator, tagNameList, offset, limit);
            if (CollectionUtils.isEmpty(dataIds)) {
                continue;
            }
            for (List<String> tmp : Lists.partition(dataIds, 1000)) {
                Filter channelDataIdsFilter = buildChannelDataFilter(tmp);
                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                ArrayList<Filter> filters = Lists.newArrayList(channelDataIdsFilter);
                if (isOpen) {
                    filters.add(new Filter("data_own_organization", "IN", dataPermission.stream().map(String::valueOf).collect(Collectors.toList())));
                }
                searchTemplateQuery.setFilters(filters);
                searchTemplateQuery.setOffset(offset);
                searchTemplateQuery.setLimit(limit);
                FilterData filterData = new FilterData();
                filterData.setQuery(searchTemplateQuery);
                filterData.setObjectAPIName(objectDescribeApiName);
                CrmObjectResult crmObjectResult = crmMetadataManager.listLargeCrmObjectsByFilter(ea, fsUserId, filterData, FIELD_NAMES_REQUIRED_TO_ASSOCIATION);
                List<Map<String, Object>> objectDatas = crmObjectResult.getDataList();
                List<ObjectData> objectDataList = objectDatas.stream().map(m -> {
                    ObjectData od = new ObjectData();
                    od.putAll(m);
                    return od;
                }).collect(Collectors.toList());
                finalUserMarketingAccountIds.addAll(this.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, objectDescribeApiName, objectDataList, true));
            }
        }
        return finalUserMarketingAccountIds;
    }

    /**
     * 营销用户Id和标签组映射
     */
    public Map<String, List<TagName>> listTagNameListByUserMarketingAccountIds(String ea, List<String> objectDescribeApiNames, Collection<String> userMarketingAccountIds) {
        if (CollectionUtils.isEmpty(objectDescribeApiNames) || CollectionUtils.isEmpty(userMarketingAccountIds)) {
            return null;
        }
        Map<String, Set<TagName>> result = Maps.newHashMap();
        for (String objectDescribeApiName : objectDescribeApiNames) {
            Map<String, String> dataIdAndUserMarketingAccountIdMap = Maps.newHashMap();
            if (Objects.equals(ChannelEnum.CRM_LEAD.getApiName(), objectDescribeApiName)) {
                List<UserMarketingCrmLeadAccountRelationEntity> relationEntities = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(
                    relationEntities.stream().collect(Collectors.toMap(UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId, UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId)));
            } else if (Objects.equals(ChannelEnum.CRM_ACCOUNT.getApiName(), objectDescribeApiName)) {
                List<UserMarketingCrmAccountAccountRelationEntity> relationEntities = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(relationEntities.stream()
                    .collect(Collectors.toMap(UserMarketingCrmAccountAccountRelationEntity::getCrmAccountId, UserMarketingCrmAccountAccountRelationEntity::getUserMarketingId)));
            } else if (Objects.equals(ChannelEnum.CRM_CONTACT.getApiName(), objectDescribeApiName)) {
                List<UserMarketingCrmContactAccountRelationEntity> relationEntities = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(relationEntities.stream()
                    .collect(Collectors.toMap(UserMarketingCrmContactAccountRelationEntity::getCrmContactId, UserMarketingCrmContactAccountRelationEntity::getUserMarketingId)));
            } else if (Objects.equals(ChannelEnum.CRM_WX_USER.getApiName(), objectDescribeApiName)) {
                List<UserMarketingCrmWxUserAccountRelationEntity> relationEntities = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(
                    relationEntities.stream().collect(Collectors.toMap(UserMarketingCrmWxUserAccountRelationEntity::getCrmWxUserId, UserMarketingCrmWxUserAccountRelationEntity::getUserMarketingId)));
            } else if(Objects.equals(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getApiName(), objectDescribeApiName)){
                List<UserMarketingCrmWxWorkExternalUserRelationEntity> relationEntities = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(
                        relationEntities.stream().collect(Collectors.toMap(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId, UserMarketingCrmWxWorkExternalUserRelationEntity::getUserMarketingId)));
            } else if(Objects.equals(ChannelEnum.CRM_MEMBER.getApiName(), objectDescribeApiName)){
                List<UserMarketingCrmMemberRelationEntity> relationEntities = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(
                        relationEntities.stream().collect(Collectors.toMap(UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId, UserMarketingCrmMemberRelationEntity::getUserMarketingId)));
            } else{
                //自定义对象
                List<UserMarketingCustomizeObjectRelationEntity> relationEntities = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
                if (CollectionUtils.isEmpty(relationEntities)) {
                    continue;
                }
                dataIdAndUserMarketingAccountIdMap.putAll(
                        relationEntities.stream().collect(Collectors.toMap(UserMarketingCustomizeObjectRelationEntity::getObjectId, UserMarketingCustomizeObjectRelationEntity::getUserMarketingId)));
            }

            if (MapUtils.isEmpty(dataIdAndUserMarketingAccountIdMap)) {
                continue;
            }
            List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataList = metadataTagManager
                .getObjectDataIdAndTagNameListDatasByObjectDataIds(ea, objectDescribeApiName, Lists.newArrayList(dataIdAndUserMarketingAccountIdMap.keySet()));
            if (CollectionUtils.isEmpty(objectDataIdAndTagNameListDataList)) {
                continue;
            }
            for (ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData : objectDataIdAndTagNameListDataList) {
                String userMarketingAccountId = dataIdAndUserMarketingAccountIdMap.get(objectDataIdAndTagNameListData.getDataId());
                result.computeIfAbsent(userMarketingAccountId, newValue -> Sets.newHashSet());
                result.get(userMarketingAccountId).addAll(objectDataIdAndTagNameListData.getTagNameList());
            }
        }
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        return result.entrySet().stream().collect(Collectors.toMap(Entry::getKey, val -> Lists.newArrayList(val.getValue())));
    }

    public void batchAddTagsToUserMarketingAccount(String ea, List<String> targetObjectApiNames, List<String> userMarketingIds, TagNameList tagNameList) {
        if (targetObjectApiNames == null || targetObjectApiNames.isEmpty()) {
            targetObjectApiNames = new ArrayList<>(this.buildUserMarketingApiNameListByEa(ea));
        }
        targetObjectApiNames.forEach(targetObjectApiName -> {
            Integer channelType = null;
            if (ChannelEnum.getByApiName(targetObjectApiName) == null){
                channelType = ChannelEnum.CUSTOMIZE_OBJECT.getType();
            }else {
                channelType = ChannelEnum.getByApiName(targetObjectApiName).getType();
            }
            List<String> dataIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, channelType, userMarketingIds);
            if (!dataIds.isEmpty()) {
                log.info("batchAddTagsToUserMarketingAccount ea:{}  userMarketingIds:{} dataIds:{} tagNameList:{}", ea, userMarketingIds, dataIds, tagNameList);
                if (dataIds.size() > 20) {
                    log.info("batchAddTagsToUserMarketingAccount return ea:{} targetObjectApiName:{} userMarketingIds:{} dataIds size {}", ea, targetObjectApiName,userMarketingIds, dataIds.size());
                    return;
                }
                List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = dataIds.stream().map(val -> new ObjectDataIdAndTagNameListData(targetObjectApiName, val, tagNameList))
                        .collect(Collectors.toList());
                metadataTagManager.addTagsToObjectDatas(ea, dataIdAndTagNameListObjectDataList);
            }
        });
    }

    public List<ObjectDataIdAndTagNameListData> filterTagByTagModuleAllow(String ea, List<ObjectDataIdAndTagNameListData> tagNameListData){
        //1，根据标签名称找到标签模型，筛选出能给该对象打的标签
        List<ObjectDataIdAndTagNameListData> filterTagList = Lists.newArrayList();
        for (ObjectDataIdAndTagNameListData tagData : tagNameListData) {
            TagNameList tagNameList = new TagNameList();
             for (TagName tagName : tagData.getTagNameList()) {
                 List<String> tagModeIds = tagModelDao.getTagModelIdsByEaAndTagName(ea, tagName.getFirstTagName(), tagName.getSecondTagName(), TagModelSourceTypeEnum.USER.getValue());
                 if (CollectionUtils.isEmpty(tagModeIds)) {
                     continue;
                 }
                 List<TagModelEntity> tagWithTagModels = tagModelDao.getByIds(ea, tagModeIds);
                 tagWithTagModels.forEach(tagWithTagModel -> {
                     if (tagWithTagModel.getObjects().contains(tagData.getApiName())) {
                         tagNameList.add(tagName);
                     }
                 });

             }
             if (CollectionUtils.isNotEmpty(tagNameList)) {
                 ObjectDataIdAndTagNameListData objectDataIdAndTagNameListData = new ObjectDataIdAndTagNameListData(tagData.getApiName(), tagData.getDataId(), tagNameList);
                 filterTagList.add(objectDataIdAndTagNameListData);
             }
        }

        return filterTagList;
    }

    //将营销用户的聚合标签同步关联的每个对象上
    public void syncMarketingTagsToCrmMetadataTag(String ea, String userMarketingId){
        Optional<String> optionalValue = marketingEnterpriseCommonSettingManager.getConfigValue(ea, MarketingCommonSettingEnum.SYNC_OBJECT_HISTORY_TAGS.getName());
        if (!optionalValue.isPresent() || Integer.parseInt(optionalValue.get()) != SyncObjectTagStatusEnum.ENABLE.getValue()){
            return;
        }


        //将标签同步到营销用户管理的每个对象上--追加打标签
        List<String> objectDescribeApiNames = ChannelEnum.getAllChannelApiName();
        List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(objectMappingEntityList)) {
            objectDescribeApiNames.addAll(objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList()));
        }

        Map<String, List<TagName>> userMarketingAccountIdsAndTagNameListMap = this
                .listTagNameListByUserMarketingAccountIds(ea, objectDescribeApiNames, Lists.newArrayList(userMarketingId));
        if (org.apache.commons.collections.MapUtils.isEmpty(userMarketingAccountIdsAndTagNameListMap) || CollectionUtils.isEmpty(userMarketingAccountIdsAndTagNameListMap.get(userMarketingId))) {
            return;
        }

        TagNameList tagNameList = TagNameList.convert(userMarketingAccountIdsAndTagNameListMap.get(userMarketingId));
        batchAddTagsToUserMarketingAccount(ea, objectDescribeApiNames, Lists.newArrayList(userMarketingId), tagNameList);
    }

    //将标签同步到营销用户管理的每个对象上
    public void asyncMarketingTagsToCrmMetadataTag(String ea, String userMarketingId) {
        try {
            Optional<String> optionalValue = marketingEnterpriseCommonSettingManager.getConfigValue(ea, MarketingCommonSettingEnum.SYNC_OBJECT_HISTORY_TAGS.getName());
            if (!optionalValue.isPresent() || Integer.parseInt(optionalValue.get()) != SyncObjectTagStatusEnum.ENABLE.getValue()) {
                return;
            }
            List<String> objectApiNames = ChannelEnum.getAllChannelApiName();
            List<MarketingUserGroupCustomizeObjectMappingEntity> objectMappingEntityList = marketingUserGroupCustomizeObjectMappingDao.getByEa(ea);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(objectMappingEntityList)) {
                objectApiNames.addAll(objectMappingEntityList.stream().map(MarketingUserGroupCustomizeObjectMappingEntity::getObjectApiName).collect(Collectors.toList()));
            }
            // 营销用户关联的所有对象
            List<String> allDataIds = Lists.newArrayList();
            Map<String, List<String>> apiNameToDataIdsMap = new HashMap<>();
            Map<String, String> dataIdToApiNameMap = new HashMap<>();
            objectApiNames.forEach(objectApiName -> {
                Integer channelType = null;
                if (ChannelEnum.getByApiName(objectApiName) == null) {
                    channelType = ChannelEnum.CUSTOMIZE_OBJECT.getType();
                } else {
                    channelType = ChannelEnum.getByApiName(objectApiName).getType();
                }
                List<String> dataIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, channelType, Lists.newArrayList(userMarketingId));
                if (CollectionUtils.isNotEmpty(dataIds)) {
                    apiNameToDataIdsMap.put(objectApiName, dataIds);
                    allDataIds.addAll(dataIds);
                    dataIds.forEach(dataId -> dataIdToApiNameMap.put(dataId, objectApiName));
                }
            });
            if (allDataIds.size() == 1) {
                return;
            }
            // 异步处理：
            // 查询所有对象的所有标签并去重
            // 追加打标签
            ThreadPoolUtils.executeWithTraceContext(() -> {
                try {
                    List<TagName> tagNames = new ArrayList<>();
                    // 保存每个对象的原始标签数据，用于后续比较
                    Map<String, TagNameList> dataIdToOriginalTagsMap = new HashMap<>();
                    apiNameToDataIdsMap.forEach((objectApiName, dataIds) -> {
                        List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListDataList = metadataTagManager.getObjectDataIdAndTagNameListDatasByObjectDataIds(ea, objectApiName, dataIds);
                        if (CollectionUtils.isNotEmpty(objectDataIdAndTagNameListDataList)) {
                            objectDataIdAndTagNameListDataList.forEach(objectDataIdAndTagNameListData -> {
                                TagNameList tagNameList = objectDataIdAndTagNameListData.getTagNameList();
                                if (CollectionUtils.isNotEmpty(tagNameList)) {
                                    tagNames.addAll(tagNameList);
                                    // 保存每个dataId的原始标签
                                    dataIdToOriginalTagsMap.put(objectDataIdAndTagNameListData.getDataId(), tagNameList);
                                }
                            });
                        }
                    });
                    if (CollectionUtils.isNotEmpty(tagNames)) {
                        TagNameList tagNameList = TagNameList.convert(tagNames).distinct();
                        // 需要分别执行打标签到对象和企微
                        List<ObjectDataIdAndTagNameListData> objectDataIdAndTagNameListData = Lists.newArrayList();
                        // 只需要执行同步到企微
                        List<ObjectDataIdAndTagNameListData> onlyInvokeQywxObjectDataIdAndTagNameListData = Lists.newArrayList();
                        allDataIds.forEach(dataId -> {
                            ObjectDataIdAndTagNameListData dataIdAndTagNameListData = new ObjectDataIdAndTagNameListData(dataIdToApiNameMap.get(dataId), dataId, tagNameList);
                            // 如果原始标签为空，需要添加
                            if (!dataIdToOriginalTagsMap.containsKey(dataId) || CollectionUtils.isEmpty(dataIdToOriginalTagsMap.get(dataId))) {
                                objectDataIdAndTagNameListData.add(dataIdAndTagNameListData);
                                return;
                            }
                            // 如果原始标签和合并后的标签不一致, 需要添加
                            TagNameList originalTags = dataIdToOriginalTagsMap.get(dataId);
                            if (!originalTags.containsAll(tagNameList) || !tagNameList.containsAll(originalTags)) {
                                log.info("dataId:{}, originalTags size:{} tagNameList size:{}", dataId, originalTags.size(), tagNameList.size());
                                objectDataIdAndTagNameListData.add(dataIdAndTagNameListData);
                                return;
                            }
                            onlyInvokeQywxObjectDataIdAndTagNameListData.add(dataIdAndTagNameListData);
                        });
                        if (CollectionUtils.isNotEmpty(objectDataIdAndTagNameListData)) {
                            metadataTagManager.addTagsToObjectDatas(ea, objectDataIdAndTagNameListData);
                        }
                        if (CollectionUtils.isNotEmpty(onlyInvokeQywxObjectDataIdAndTagNameListData)) {
                            Map<String, Map<TagNameList, List<String>>> apiNameAndTagNameListAndObjectIdsMap = onlyInvokeQywxObjectDataIdAndTagNameListData.stream()
                                    .filter(ObjectDataIdAndTagNameListData::valid).collect(Collectors
                                            .groupingBy(ObjectDataIdAndTagNameListData::getApiName,
                                                    Collectors.groupingBy(ObjectDataIdAndTagNameListData::getTagNameList, Collectors.mapping(ObjectDataIdAndTagNameListData::getDataId, Collectors.toList()))));
                            apiNameAndTagNameListAndObjectIdsMap.forEach((describeApiName, tagNameListAndObjectIdsMap) -> {
                                if (tagNameListAndObjectIdsMap == null) {
                                    return;
                                }
                                tagNameListAndObjectIdsMap.forEach((tagNameLists, objectIds) -> {
                                    log.info("addTagsToObjectDatas inner ea:{} describeApiName:{} objectIds:{} tagNames:{}", ea, describeApiName, objectIds, tagNameList);
                                    if (objectIds == null || objectIds.isEmpty()) {
                                        return;
                                    }
                                    Iterators.partition(objectIds.iterator(), 20).forEachRemaining(subObjectIds -> {
                                        // 标签打到企微
                                        Map<String, Set<TagName>> objectIdToReallyAddToOuterTagNames = new HashMap<>();
                                        subObjectIds.forEach(subObjectId -> objectIdToReallyAddToOuterTagNames.put(subObjectId, new HashSet<>(tagNameList)));
                                        if ((CrmObjectApiNameEnum.WECHAT.getName().equals(describeApiName) || CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName().equals(describeApiName)) && !objectIdToReallyAddToOuterTagNames.isEmpty()) {
                                            metadataTagManager.asyncAddOrRemoveDataTagToOuter(ea, describeApiName, objectIdToReallyAddToOuterTagNames, null);
                                        }
                                    });
                                });
                            });
                        }
                    }
                } catch (Exception e) {
                    log.warn("异步将标签同步到营销用户管理的每个对象上失败，ea:{}, userMarketingId:{}, e:", ea, userMarketingId, e);
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.ASYNC_ADD_TAGS_TO_OBJ);
        } catch (Exception ex) {
            log.warn("将标签同步到营销用户管理的每个对象上失败，ea:{}, userMarketingId:{}, e:", ea, userMarketingId, ex);
        }
    }

    private List<DataIdAndMetadataTagData> getMetadataTagsByUserMarketingAccountIds(String ea, String userMarketingId) {
        List<ObjectIdAndDescribeData> objectDescribeApiNames = userMarketingAccountRelationManager.listDescribeApiNameByUserMarketingId(ea, userMarketingId);
        if (CollectionUtils.isEmpty(objectDescribeApiNames)) {
            return null;
        }
        Map<String, List<String>> objectDescribeObjectIdsMap = new HashMap<>();
        for (ObjectIdAndDescribeData objectIdAndDescribeData : objectDescribeApiNames) {
            objectDescribeObjectIdsMap.computeIfAbsent(objectIdAndDescribeData.getDescribeApiName(), val -> new ArrayList<>());
            objectDescribeObjectIdsMap.get(objectIdAndDescribeData.getDescribeApiName()).add(objectIdAndDescribeData.getObjectId());
        }
        List<DataIdAndMetadataTagData> metadataTagData = Lists.newArrayList();
        for (Map.Entry<String, List<String>> entry : objectDescribeObjectIdsMap.entrySet()) {
            FindAllTagByBulkDataIdArg arg = new FindAllTagByBulkDataIdArg(eieaConverter.enterpriseAccountToId(ea), entry.getKey(), entry.getValue());
            MetadataTagResult<List<DataIdAndMetadataTagData>> result = metadataTagManager.doFindAllTagByBulkDataId(arg);
            if (!result.isSuccess()) {
                return null;
            }
            metadataTagData.addAll(result.getResult());
        }

        return metadataTagData;
    }

    public void batchDeleteTagsFromUserMarketingAccount(String ea, List<String> targetObjectApiNames, List<String> userMarketingIds, TagNameList tagNameList) {
        if (targetObjectApiNames == null || targetObjectApiNames.isEmpty()) {
            targetObjectApiNames = new ArrayList<>(this.buildUserMarketingApiNameListByEa(ea));
        }
        targetObjectApiNames.forEach(targetObjectApiName -> {
            ChannelEnum byApiName = ChannelEnum.getByApiName(targetObjectApiName);
            if (null != byApiName) {
                List<String> dataIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, byApiName.getType(), userMarketingIds);
                if (!dataIds.isEmpty()) {
                    List<ObjectDataIdAndTagNameListData> dataIdAndTagNameListObjectDataList = dataIds.stream().map(val -> new ObjectDataIdAndTagNameListData(targetObjectApiName, val, tagNameList))
                            .collect(Collectors.toList());
                    metadataTagManager.deleteTagsFromObjectDatas(ea, dataIdAndTagNameListObjectDataList);
                }
            }
        });
    }

    public PageResult<UserMarketingActionResult> getUserMarketingActionResultPageResult(String ea, Integer fsUserId, com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> result, String phone) {
        Map<String, CommonObjectDTO> idToMessageMap = new HashMap<>();
        PageResult<UserMarketingActionResult> pr = new PageResult<>();
        pr.setTotalCount(result.getData().getTotalCount());
        Map<Integer, String> actionEnumMap = fromActionEnum();
        if (result.getData().getData() != null) {
            Set<String> marketingEvenIdSet = new HashSet<>();
            Set<String> userMarketingIds = new HashSet<>();
            // 客户自定义的actionName
            Map<Integer, UserMarketingCustomizeActionEntity> customizeActionTypeToActionNameMap = getCustomizeActionTypeMap(ea, result);
            // 推广渠道  渠道value -> 渠道label
            Map<String, String> spreadChannelLabelMap = getSpreadChannelMap(ea);
            Map<String, String> spreadTypeLabelMap = marketingActivityManager.getSpreadTypeLabelMap(ea);
            List<UserMarketingActionResult> results = result.getData().getData().stream().filter(d -> d.getProperties() != null).map(d -> {
                Properties properties = d.getProperties();
                UserMarketingActionResult actionResult = BeanUtil.copyByGson(properties, UserMarketingActionResult.class);
                actionResult.setId(d.getId());
                if (StringUtils.isNotEmpty(actionResult.getMarketingEventId())) {
                    marketingEvenIdSet.add(actionResult.getMarketingEventId());
                }
                //拿头像和名称
                userMarketingIds.add(d.getUserMarketingId());
                actionResult.setUserMarketingId(d.getUserMarketingId());
                actionResult.setCount(d.getCount());
                actionResult.setCreateTime(d.getCreateTime());
                String actionName;
                JSONObject propertiesObj = JSON.parseObject(JSON.toJSONString(properties));
                JSONObject extensionParamsObj = propertiesObj.getJSONObject("extensionParams");
                UserMarketingCustomizeActionEntity customizeActionEntity = customizeActionTypeToActionNameMap.get(actionResult.getActionType());
                if (customizeActionEntity != null) {
                    actionName = customizeActionEntity.getActionName();
                } else if (extensionParamsObj != null && actionResult.getActionType() == ActionTypeEnum.CUSTOMIZE_ACTION.getAction()) {
                    actionName = extensionParamsObj.getString("actionDescription");
                } else {
                    actionName = actionResult.getActionType() < 1000000 ? actionEnumMap.get(actionResult.getActionType() + 1000000)
                            : actionEnumMap.get(actionResult.getActionType());
                }
                //默认写入actionname
                actionResult.setActionName(actionName);
                this.doFillDisplayMessage(ea, fsUserId, actionResult, idToMessageMap, phone);
                if(extensionParamsObj != null) {
                    int actionDurationTime = extensionParamsObj.getIntValue("actionDurationTime");
                    if (actionDurationTime / 1000 > 0) {
                        if (actionDurationTime / 1000 >= 60){
                            if (actionDurationTime / 1000 / 60 > 0) {
                                actionResult.setObjectName(actionResult.getObjectName() + " " + actionDurationTime / 1000 / 60 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                                actionResult.setActionDurationTime(actionDurationTime / 1000 / 60 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                            } else {
                                actionResult.setObjectName(actionResult.getObjectName() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3062));
                                actionResult.setActionDurationTime(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3062));
                            }
                        } else {
                            actionResult.setObjectName(actionResult.getObjectName() + " " + actionDurationTime / 1000 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065));
                            actionResult.setActionDurationTime(actionDurationTime / 1000 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065));
                        }
                    }
                    if (extensionParamsObj.getDoubleValue("actionProportion") > 0) {
                        double actionProportion = extensionParamsObj.getDoubleValue("actionProportion");
                        actionResult.setObjectName(actionResult.getObjectName() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3070) + (int) actionProportion + "%");
                        actionResult.setActionProportion((int) actionProportion + "%");
                    }
                    //客服会话拼接秒速,在线客服不传objectName,前端展示逻辑要求
                    if(ActionTypeEnum.CHAT_ONLINE.getAction()+1000000 == actionResult.getActionType()){
                        actionResult.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3074));
                        long longValue = extensionParamsObj.getLongValue("actionDurationTime");
                        if(longValue / 1000 > 0){
                            actionResult.setActionName(actionResult.getActionName()+" "+TimeMeasureUtil.getSecondTime(longValue));
                        }
                        actionResult.setObjectName(null);
                    }
                    fillExtensionProperties(ea, fsUserId, spreadChannelLabelMap, spreadTypeLabelMap, actionResult, extensionParamsObj);
                }
                return actionResult;
            }).collect(Collectors.toList());
            // 填充转发的营销用户名字
            List<String> fromUserMarketingIdList = results.stream().filter(e -> StringUtils.isNotEmpty(e.getFromUserMarketingId())).map(UserMarketingActionResult::getFromUserMarketingId).collect(Collectors.toList());
            Set<Integer> spreadFsUidSet = results.stream().filter(e -> e.getSpreadFsUid() != null).map(UserMarketingActionResult::getSpreadFsUid).collect(Collectors.toSet());
            Map<Integer, String> fsUidToNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(spreadFsUidSet)) {
                Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIdsWithoutQywx(ea, Lists.newArrayList(spreadFsUidSet), true);
                fsEmployeeMsgMap.forEach((k, v) -> fsUidToNameMap.put(k, v.getName()));
            }
            Map<String, String> fromUserMarketingNameMap = getMarketingUserNames(ea, fromUserMarketingIdList);
            for (UserMarketingActionResult userMarketingActionResult : results) {
                if (StringUtils.isNotEmpty(userMarketingActionResult.getFromUserMarketingId())) {
                    userMarketingActionResult.setFromUserMarketingName(fromUserMarketingNameMap.get(userMarketingActionResult.getFromUserMarketingId()));
                }
                if (userMarketingActionResult.getSpreadFsUid() != null) {
                    String name = fsUidToNameMap.get(userMarketingActionResult.getSpreadFsUid());
                    userMarketingActionResult.setSpreadFsUserName(name);
                }
                if (userMarketingActionResult.getActionType() == ActionTypeEnum.WX_SERVICE_TEMPLATE_MESSAGE.getAction() + 1000000 || userMarketingActionResult.getActionType() == ActionTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getAction() + 1000000) {
                    String objectName = getWechatMessageMarketingUserObjectName(userMarketingActionResult);
                    userMarketingActionResult.setObjectName(objectName);
                    userMarketingActionResult.setSpreadType(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3105));
                    userMarketingActionResult.setSpreadChannel(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3106));
                }
                if (userMarketingActionResult.getActionType() == ActionTypeEnum.RECV_QYWX_MSG.getAction() + 1000000) {
                    String objectName = getQywxMessageMarketingUserObjectName(userMarketingActionResult);
                    userMarketingActionResult.setObjectName(objectName);
                    userMarketingActionResult.setSpreadType(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3111));
                    userMarketingActionResult.setSpreadChannel(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3112));
                }
                if (userMarketingActionResult.getActionType() == ActionTypeEnum.RECV_QYWX_MOMENT.getAction() + 1000000) {
                    String objectName = getQywxMomentMarketingUserObjectName(userMarketingActionResult);
                    userMarketingActionResult.setObjectName(objectName);
                    userMarketingActionResult.setSpreadType(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3117));
                    userMarketingActionResult.setSpreadChannel(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3112));
                }
            }
            Map<String, String> map = crmMetadataManager.batchGetByIdsV3(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "name"), Lists.newArrayList(marketingEvenIdSet)).stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));
            results = results.stream().map(o -> {
                if (StringUtils.isNotEmpty(o.getMarketingEventId())) {
                    o.setMarketingEventName(map.get(o.getMarketingEventId()));
                    if(ActionTypeEnum.LOOK_UP_LIVE_CHECK_PAGE.getAction()+1000000 == o.getActionType()){
                        o.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3126)+o.getMarketingEventName()+I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3126_1));
                    }
                }
                return o;
            }).collect(Collectors.toList());
            pr.setData(results);
        }
        return pr;
    }

    private String getWechatMessageMarketingUserObjectName(UserMarketingActionResult userMarketingActionResult) {
        if (userMarketingActionResult.getActionType() != ActionTypeEnum.WX_SERVICE_TEMPLATE_MESSAGE.getAction() + 1000000 && userMarketingActionResult.getActionType() != ActionTypeEnum.WECHAT_SERVICE_SEND_MESSAGE.getAction() + 1000000) {
            return null;
        }
        if (userMarketingActionResult.getChannelType() != MarketingUserActionChannelType.WECHAT_SERVICE.getChannelType()) {
            return null;
        }

        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(userMarketingActionResult.getMarketingActivityId());
        if (configEntity == null || configEntity.getExternalConfig() == null || configEntity.getExternalConfig().getWeChatTemplateMessageVO() == null
                || configEntity.getExternalConfig().getWeChatTemplateMessageVO().getTemplateMessageDatas() == null) {
            return null;
        }

        return configEntity.getExternalConfig().getWeChatTemplateMessageVO().getTitle();
    }

    private String getQywxMessageMarketingUserObjectName(UserMarketingActionResult userMarketingActionResult) {
        if (userMarketingActionResult.getActionType() != ActionTypeEnum.RECV_QYWX_MSG.getAction() + 1000000) {
            return null;
        }
        if (userMarketingActionResult.getChannelType() != MarketingUserActionChannelType.QYWX.getChannelType()) {
            return null;
        }
        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(userMarketingActionResult.getMarketingActivityId());
        if (configEntity == null || configEntity.getExternalConfig() == null || configEntity.getExternalConfig().getQywxGroupSendMessageVO() == null) {
            return null;
        }

        return configEntity.getExternalConfig().getQywxGroupSendMessageVO().getTitle();
    }

    private String getQywxMomentMarketingUserObjectName(UserMarketingActionResult userMarketingActionResult){
        if (userMarketingActionResult.getActionType() != ActionTypeEnum.RECV_QYWX_MOMENT.getAction() + 1000000) {
            return null;
        }
        if (userMarketingActionResult.getChannelType() != MarketingUserActionChannelType.QYWX.getChannelType()) {
            return null;
        }
        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(userMarketingActionResult.getMarketingActivityId());
        if (configEntity == null || configEntity.getExternalConfig() == null || configEntity.getExternalConfig().getMomentMessageVO() == null) {
            return null;
        }

        return configEntity.getExternalConfig().getMomentMessageVO().getName();
    }
    private void fillExtensionProperties(String ea, Integer fsUserId, Map<String, String> spreadChannelLabelMap, Map<String, String> spreadTypeLabelMap, UserMarketingActionResult actionResult, JSONObject extensionParamsObj) {
        // 营销场景
        Integer marketingScene = extensionParamsObj.getInteger(RecordActionArg.MARKETING_SCENE_KEY);
        MarketingSceneEnum marketingSceneEnum = MarketingSceneEnum.getByCode(marketingScene);
        actionResult.setMarketingScene(marketingSceneEnum == null ? "" : marketingSceneEnum.getDesc());
        // 互动渠道
        String spreadChannel = extensionParamsObj.getString(RecordActionArg.SPREAD_CHANNEL_KEY);
        String dataSource = extensionParamsObj.getString("dataSource");
        String channelAccountName = extensionParamsObj.getString(RecordActionArg.CHANNEL_ACCOUNT_NAME_KEY);
        String channelAccountPlatform = extensionParamsObj.getString(RecordActionArg.CHANNEL_ACCOUNT_PLATFORM_KEY);
        if (StringUtils.isBlank(dataSource) || UserMarketingActionDataSourceEnum.YXT.getCode().equals(dataSource)) {
            // 如果在营销通找不到推广渠道 那可能是用户自定义创建的营销动态 直接用原来的值
            String spreadChannelLabel = spreadChannelLabelMap.get(spreadChannel);
            actionResult.setSpreadChannel(spreadChannelLabel);
            actionResult.setDataSource(UserMarketingActionDataSourceEnum.YXT.getDesc());
            // 营销通的营销动态
            ChannelAccountPlatformEnum channelAccountPlatformEnum;
            if (StringUtils.isNotEmpty(channelAccountPlatform) && (channelAccountPlatformEnum = ChannelAccountPlatformEnum.getByPlatform(channelAccountPlatform)) != null) {
                actionResult.setChannelAccountPlatform(channelAccountPlatformEnum.getDesc());
                if (StringUtils.isNotEmpty(channelAccountName)) {
                    if (channelAccountPlatformEnum.getPlatform().contains("Ad")) {
                        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById(channelAccountName);
                        actionResult.setChannelAccountName(adAccountEntity == null ? "" : adAccountEntity.getUsername());
                    } else if (channelAccountPlatformEnum == ChannelAccountPlatformEnum.OFFICIAL_ACCOUNT) {
                        List<OuterServiceResult> outerServiceResults = outerServiceWechatManager.queryOuterServiceList(ea, fsUserId);
                        Optional<OuterServiceResult> optional = outerServiceResults.stream().filter(e -> channelAccountName.equals(e.getWxAppId())).findFirst();
                        actionResult.setChannelAccountName(optional.isPresent() ? optional.get().getWxAppName() : "");
                    } else if (channelAccountPlatformEnum == ChannelAccountPlatformEnum.CHANNELS_LIVE) {
                        // 直播的目前只有视频号有这个名称
                        ChannelsAccountEntity channelsAccountEntity = channelsAccountDAO.getById(ea, channelAccountName);
                        actionResult.setChannelAccountName(channelsAccountEntity == null ? "" : channelsAccountEntity.getChannelsName());
                    } else if (channelAccountPlatformEnum == ChannelAccountPlatformEnum.OFFICIAL_WEBSITE) {
                        OfficialWebsiteEntity officialWebsiteEntity = officialWebsiteManager.getById(channelAccountName);
                        actionResult.setChannelAccountName(officialWebsiteEntity == null ? "" : officialWebsiteEntity.getWebsiteName());
                    }
                }
            }
        } else {
            // 非营销通的营销动态
            actionResult.setChannelAccountName(channelAccountName);
            actionResult.setChannelAccountPlatform(channelAccountPlatform);
            actionResult.setSpreadChannel(spreadChannel);
            UserMarketingActionDataSourceEnum sourceEnum = UserMarketingActionDataSourceEnum.getByCode(dataSource);
            if (sourceEnum != null) {
                actionResult.setDataSource(sourceEnum.getDesc());
            }
            if (UserMarketingActionDataSourceEnum.CUSTOMIZE.getCode().equals(dataSource)) {
                // 推广方式
                String objectName = extensionParamsObj.getString("objectName");
                if (StringUtils.isNotEmpty(objectName)) {
                    actionResult.setObjectName(objectName);
                }
            }
        }
        // 推广方式
        String spreadType = extensionParamsObj.getString("spreadType");
        String spreadTypeLabel = spreadTypeLabelMap.get(spreadType);
        actionResult.setSpreadType(spreadTypeLabel);
        // 客户端
        Integer client = extensionParamsObj.getInteger("client");
        if (client != null) {
            UserMarketingActionClientEnum clientEnum = UserMarketingActionClientEnum.getByCode(client);
            actionResult.setClient(clientEnum == null ? "" : clientEnum.getDesc());
        }
        // 浏览器
        String browser = extensionParamsObj.getString("browser");
        actionResult.setBrowser(browser);
        // IP归属地
        String ipQCellCore = extensionParamsObj.getString("ipQCellCore");
        actionResult.setIpQCellCore(ipQCellCore);
        //  操作系统
        String operateSystem = extensionParamsObj.getString("operateSystem");
        actionResult.setOperateSystem(operateSystem);
        // 转发的营销用户
        String fromUserMarketingId = extensionParamsObj.getString("fromUserMarketingId");
        String eventDescription = extensionParamsObj.getString("eventDescription");
        Integer displayActionType = actionResult.getActionType();
        if (displayActionType != null && StringUtils.isNotEmpty(eventDescription)) {
            int realActionType =  displayActionType > 1000000 ? displayActionType - 1000000 : displayActionType;
            if(MarketingUserActionType.TRIGGER_WEBSITE_EVENT.getActionType() == realActionType ){
                actionResult.setActionName(eventDescription);
                actionResult.setObjectName("");
            }
        }

        actionResult.setFromUserMarketingId(fromUserMarketingId);
    }

    private Map<String, String> getSpreadChannelMap(String ea) {
        com.facishare.marketing.common.result.Result<List<SpreadChannelOptionData>> spreadChannelResult = spreadChannelManager.querySpreadChannelList(ea, SuperUserConstants.USER_ID);
        Map<String, String> spreadChannelLabelMap = Maps.newHashMap();
        if (spreadChannelResult.isSuccess() && CollectionUtils.isNotEmpty(spreadChannelResult.getData())) {
            spreadChannelResult.getData().forEach(e -> spreadChannelLabelMap.put(e.getValue(), e.getLabel()));
        }
        for (SpreadChannelEnum value : SpreadChannelEnum.values()) {
            spreadChannelLabelMap.put(value.getCode(), value.getDesc());
        }
        return spreadChannelLabelMap;
    }

    public PageResult<UserMarketingActionResult> getUserMarketingActionAndWeChatAvatarResultPageResult(String ea, Integer fsUserId, com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> result, String phone) {
        Map<String, CommonObjectDTO> idToMessageMap = new HashMap<>();
        PageResult<UserMarketingActionResult> pr = new PageResult<>();
        pr.setTotalCount(result.getData().getTotalCount());
        Map<Integer, String> actionEnumMap = fromActionEnum();
        if (result.getData().getData() != null) {

            Map<Integer, UserMarketingCustomizeActionEntity> customizeActionTypeToActionNameMap = getCustomizeActionTypeMap(ea, result);

            Set<String> marketingEvenIdSet = new HashSet<>();
            Set<String> userMarketingIds = new HashSet<>();
            // 推广渠道  渠道value -> 渠道label
            Map<String, String> spreadChannelLabelMap = getSpreadChannelMap(ea);
            Map<String, String> spreadTypeLabelMap = marketingActivityManager.getSpreadTypeLabelMap(ea);
            List<UserMarketingActionResult> results = result.getData().getData().stream().filter(d -> d.getProperties() != null).map(d -> {
                Properties properties = d.getProperties();
                UserMarketingActionResult actionResult = BeanUtil.copyByGson(properties, UserMarketingActionResult.class);
                actionResult.setId(d.getId());
                if (StringUtils.isNotEmpty(actionResult.getMarketingEventId())) {
                    marketingEvenIdSet.add(actionResult.getMarketingEventId());
                }
                //拿头像和名称
                userMarketingIds.add(d.getUserMarketingId());
                actionResult.setUserMarketingId(d.getUserMarketingId());
                actionResult.setCount(d.getCount());
                actionResult.setCreateTime(d.getCreateTime());
                String actionName;
                UserMarketingCustomizeActionEntity customizeActionEntity = customizeActionTypeToActionNameMap.get(actionResult.getActionType());
                if (customizeActionEntity != null) {
                    actionName = customizeActionEntity.getActionName();
                } else {
                    actionName = actionResult.getActionType() < 1000000 ? actionEnumMap.get(actionResult.getActionType() + 1000000)
                            : actionEnumMap.get(actionResult.getActionType());
                }
                actionResult.setActionName(actionName);
                this.doFillDisplayMessage(ea, fsUserId, actionResult, idToMessageMap, phone);
                JSONObject propertiesObj = JSON.parseObject(JSON.toJSONString(properties));
                JSONObject extensionParamsObj = propertiesObj.getJSONObject("extensionParams");
                if(extensionParamsObj != null) {
                    int actionDurationTime = extensionParamsObj.getIntValue("actionDurationTime");
                    if (actionDurationTime / 1000 > 0) {
                        if (actionDurationTime / 1000 >= 60){
                            if (actionDurationTime / 1000 / 60 > 0) {
                                actionResult.setObjectName(actionResult.getObjectName() + " " + actionDurationTime / 1000 / 60 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                                actionResult.setActionDurationTime(actionDurationTime / 1000 / 60 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                            } else {
                                actionResult.setObjectName(actionResult.getObjectName() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3062));
                                actionResult.setActionDurationTime(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3062));
                            }
                        } else {
                            actionResult.setObjectName(actionResult.getObjectName() + " " + actionDurationTime / 1000 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065));
                            actionResult.setActionDurationTime(actionDurationTime / 1000 + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3065));
                        }
                    }
                    if (extensionParamsObj.getDoubleValue("actionProportion") > 0) {
                        double actionProportion = extensionParamsObj.getDoubleValue("actionProportion");
                        actionResult.setObjectName(actionResult.getObjectName() + I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3070) + (int) actionProportion + "%");
                        actionResult.setActionProportion((int) actionProportion + "%");
                    }
                    fillExtensionProperties(ea, fsUserId, spreadChannelLabelMap, spreadTypeLabelMap, actionResult, extensionParamsObj);
                }
                return actionResult;
            }).collect(Collectors.toList());
            // 填充转发的营销用户名字
            List<String> fromUserMarketingIdList = results.stream().filter(e -> StringUtils.isNotEmpty(e.getFromUserMarketingId())).map(UserMarketingActionResult::getFromUserMarketingId).collect(Collectors.toList());
            Map<String, String> fromUserMarketingNameMap = getMarketingUserNames(ea, fromUserMarketingIdList);
            for (UserMarketingActionResult userMarketingActionResult : results) {
                if (StringUtils.isNotEmpty(userMarketingActionResult.getFromUserMarketingId())) {
                    userMarketingActionResult.setFromUserMarketingName(fromUserMarketingNameMap.get(userMarketingActionResult.getFromUserMarketingId()));
                }
            }
            Map<String, UserMarketingAccountData> userMarketingAccountDataMap = getBaseInfosByIds(ea, -10000, Lists.newArrayList(userMarketingIds), InfoStateEnum.DETAIL);
            Map<String, String> map = crmMetadataManager.batchGetByIdsV3(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "name"), Lists.newArrayList(marketingEvenIdSet)).stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));
            results = results.stream().map(o -> {
                if (StringUtils.isNotEmpty(o.getMarketingEventId())) {
                    o.setMarketingEventName(map.get(o.getMarketingEventId()));
                    if(ActionTypeEnum.LOOK_UP_LIVE_CHECK_PAGE.getAction()+1000000 == o.getActionType()){
                        o.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3126)+o.getMarketingEventName()+I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3126_1));
                    }
                }
                if(StringUtils.isNotEmpty(userMarketingAccountDataMap.get(o.getUserMarketingId()).getName())){
                    o.setUserName(userMarketingAccountDataMap.get(o.getUserMarketingId()).getName());
                }
                UserMarketingAccountData userMarketingAccountData = userMarketingAccountDataMap.get(o.getUserMarketingId());
                if (userMarketingAccountData != null) {
                    if(CollectionUtils.isNotEmpty(userMarketingAccountData.getWeChatAvatar())){
                        String nPath = (String) userMarketingAccountDataMap.get(o.getUserMarketingId()).getWeChatAvatar().get(0).get(WeChatAvatarData.WeChatAvatar.PATH);
                        String weChatAvatarUrl = fileV2Manager.getUrlByPath(nPath, ea, false);
                        o.setWeChatAvatarUrl(weChatAvatarUrl);
                    } else {
                        o.setWeChatAvatarUrl(userMarketingAccountData.getAvatar());
                    }
                }
                return o;
            }).collect(Collectors.toList());
            pr.setData(results);
        }
        return pr;
    }

    // 获取用户自定的action  这个action是客户要求生成的自定义营销动态, action是由我们营销通注册的
    private Map<Integer, UserMarketingCustomizeActionEntity> getCustomizeActionTypeMap(String ea, com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> result) {
        List<Integer> actionTypeList = result.getData().getData().stream().filter(e -> e.getProperties() != null && e.getProperties().containsKey("actionType"))
                .map(e -> Integer.parseInt(e.getProperties().get("actionType").toString())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(actionTypeList)) {
            return Maps.newHashMap();
        }
        List<UserMarketingCustomizeActionEntity> customizeActionEntityList = userMarketingCustomizeActionDAO.getByActionTypeList(ea, actionTypeList);
        if (CollectionUtils.isEmpty(customizeActionEntityList)) {
            return Maps.newHashMap();
        }
        return customizeActionEntityList.stream().collect(Collectors.toMap(UserMarketingCustomizeActionEntity::getActionType, e -> e));

    }

    public PageResult<UserMarketingActionResult> getUserMarketingActionResultPageResult(String ea, Integer fsUserId, com.facishare.marketing.statistic.common.result.Result<com.facishare.marketing.statistic.common.model.PageResult<UserMarketingActionStatisticResult>> result) {
        return getUserMarketingActionResultPageResult(ea, fsUserId, result, null);
    }

    public void doFillDisplayMessage(String ea, Integer fsUserId, UserMarketingActionResult result, Map<String, CommonObjectDTO> idToMessageMap, String phone){
        Integer displayActionType = result.getActionType();
        if(displayActionType != null && displayActionType > 1000000){
            int realActionType = displayActionType - 1000000;
            if (MarketingUserActionType.isMailActionType(realActionType)) {
                mailManager.buildUserActionResult(realActionType, result);
            }
            officialWebsiteManager.buildUserActionResult(realActionType, result);
            if(MarketingUserActionType.isPureWxAccountAction(realActionType)){
                if(MarketingUserActionType.SCAN_WX_SERVICE_QR_CODE.getActionType() != realActionType){
                    result.setObjectName(result.getObjectId());
                }
            }else{
                if (result.getObjectId() != null){
                    CommonObjectDTO commonObjectDTO = idToMessageMap.get(result.getObjectId());
                    if (commonObjectDTO == null) {
                        commonObjectDTO = objectManager.getCommonObject(ea, fsUserId, result.getObjectId(), result.getObjectType());
                        idToMessageMap.put(result.getObjectId(), commonObjectDTO);
                    }
                    if (StringUtils.isBlank(result.getObjectName())) {
                        result.setObjectName(commonObjectDTO.getObjectName());
                    }
                    if (StringUtils.isBlank(result.getObjectId()) || result.getObjectType() == ObjectTypeEnum.LANDING_PAGE.getType()) {
                        result.setObjectLink(commonObjectDTO.getObjectLink());
                    }
                }
                if (result.getSceneId() != null && MarketingUserActionSceneType.toObjectType(result.getSceneType()) != null){
                    CommonObjectDTO commonObjectDTO = idToMessageMap.get(result.getSceneId());
                    if (commonObjectDTO == null) {
                        commonObjectDTO = objectManager.getCommonObject(ea, fsUserId, result.getSceneId(), MarketingUserActionSceneType.toObjectType(result.getSceneType()).getType());
                        idToMessageMap.put(result.getSceneId(), commonObjectDTO);
                    }
                    result.setSceneName(commonObjectDTO.getObjectName());
                }
            }
            if (!Strings.isNullOrEmpty(result.getWxAppId())){
                CommonObjectDTO commonObjectDTO = idToMessageMap.get(result.getWxAppId());
                if (commonObjectDTO == null) {
                    commonObjectDTO = objectManager.getCommonObject(ea, fsUserId, result.getWxAppId(), ObjectTypeEnum.WX_SERVICE_ACCOUNT.getType());
                    idToMessageMap.put(result.getWxAppId(), commonObjectDTO);
                }
                result.setWxServiceAccountName(commonObjectDTO.getObjectName());
                result.setWxServiceAccountId(commonObjectDTO.getExternalMessage());
            }
            if(MarketingUserActionType.LOOK_UP_LIVE.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3437));
                if (StringUtils.isNotEmpty(phone)) {
                    LiveUserStatusEntity entity = liveUserStatusDAO.getLiveUserStatusEntityByObjectInfo(ea, result.getUserMarketingId(), result.getObjectId(), displayActionType.toString(), phone);
                    if (null != entity) {
                        result.setObjectName(result.getObjectName() + " " + entity.getViewTime() +I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                    }
                }
            }
            if(MarketingUserActionType.ENROLL_LIVE.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3446));
            }
            if(MarketingUserActionType.LOOK_UP_RECORD.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3449));
                if (StringUtils.isNotEmpty(phone)) {
                    LiveUserStatusEntity entity = liveUserStatusDAO.getLiveUserStatusEntityByObjectInfo(ea, result.getUserMarketingId(), result.getObjectId(), displayActionType.toString(),phone);
                    if (null != entity) {
                        result.setObjectName(result.getObjectName() + " " + entity.getReplayTime() +I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3060));
                    }
                }
            }
            if (MarketingUserActionType.LIVE_CHAT.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3458));
                if (StringUtils.isNotEmpty(phone)) {
                    LiveUserStatusEntity entity = liveUserStatusDAO.getLiveUserStatusEntityByObjectInfo(ea, result.getUserMarketingId(), result.getObjectId(), displayActionType.toString(),phone);
                    if (null != entity) {
                        result.setObjectName(result.getObjectName() + " " + entity.getInteractiveCount() +I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3462));
                    }
                }
            }
            if(MarketingUserActionType.ENROLL_CONFERENCE.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3467));
            }
            if(MarketingUserActionType.CONFERENCE_CHECK_IN.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3470));
            }
            if(MarketingUserActionType.TRIGGER_WEBSITE_EVENT.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3473));
            }
            if (MarketingUserActionType.MAIL_RESPONSE.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3476));
            }
            if (MarketingUserActionType.SMS_RECEIVED.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3479));
            }
            if (MarketingUserActionType.LOOK_UP_FILE.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3482));
            }
            if (MarketingUserActionType.DOWNLOAD_FILE.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3485));
            }
            if (MarketingUserActionType.FORWARD_FORM_NEW.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3488));
            }
            if (MarketingUserActionType.LOOKUP_CARD.getActionType() == realActionType){
                result.setActionName(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTMANAGER_3491));
                CardEntity cardEntity = cardDao.queryCardInfoByUid(result.getObjectId());
                result.setObjectName(cardEntity == null ? "" : cardEntity.getName());
            }
            if (result.getObjectType() != null && result.getObjectType() == ObjectTypeEnum.CARD.getType() && StringUtils.isBlank(result.getObjectName())) {
                CardEntity cardEntity = cardDao.queryCardInfoByUid(result.getObjectId());
                result.setObjectName(cardEntity == null ? "" : cardEntity.getName());
            }
            if (MarketingUserActionType.LOOK_UP_AD_LANDING_PAGE.getActionType() == realActionType){
                ObjectData landingObject = crmMetadataManager.getById(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.LANDING_PAGE_OBJ.getName(), result.getObjectId());
                String name;
                // 广告落地页 可能是落地页对象 也有可能是微页面
                if (landingObject != null) {
                    name = landingObject.getName();
                } else {
                    CommonObjectDTO commonObjectDTO = idToMessageMap.get(result.getObjectId());
                    name = commonObjectDTO == null ? "" : commonObjectDTO.getObjectName() ;
                }
                result.setObjectName(name);
            } else if (MarketingUserActionType.CLICK_KEYWORD.getActionType() == realActionType || MarketingUserActionType.SEARCH_KEYWORD.getActionType() == realActionType){
                ObjectData keywordObject = crmMetadataManager.getById(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_KEYWORD.getName(), result.getObjectId());
                result.setObjectName(keywordObject == null ? "" : keywordObject.getName());
            } else if (MarketingUserActionType.ADD_QYWX_EMPLOYEE.getActionType() == realActionType || MarketingUserActionType.DEL_QYWX_EMPLOYEE.getActionType() == realActionType){
                if (StringUtils.isNotEmpty(result.getObjectId())) {
                    QyWxAddressBookEntity qyWxAddressBookEntity = qywxAddressBookManager.queryByEaAndUserId(ea, result.getObjectId());
                    result.setObjectName(qyWxAddressBookEntity == null ? "" : qyWxAddressBookEntity.getName());
                }
            } else if (MarketingUserActionType.JOIN_WECHAT_GROUP.getActionType() == realActionType || MarketingUserActionType.QUIT_WECHAT_GROUP.getActionType() == realActionType){
                ObjectData wechatGroupObject = crmMetadataManager.getById(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), result.getObjectId());
                result.setObjectName(wechatGroupObject == null ? "" : wechatGroupObject.getName());
            } else if (MarketingUserActionType.LOOK_UP_FILE.getActionType() == realActionType || MarketingUserActionType.FORWARD_FILE.getActionType() == realActionType
                    || MarketingUserActionType.DOWNLOAD_FILE.getActionType() == realActionType){
                FileEntity fileEntity = fileLibraryDAO.getById(result.getObjectId());
                result.setObjectName(fileEntity == null ? "" : fileEntity.getFileName());
            } else if (MarketingUserActionType.LOOK_UP_VIDEO.getActionType() == realActionType || MarketingUserActionType.FORWARD_VIDEO.getActionType() == realActionType){
                VideoEntity videoEntity = videoDAO.getById(result.getObjectId());
                result.setObjectName(videoEntity == null ? "" : videoEntity.getVideoName());
            }
        }
        result.setObjectName(result.getObjectName() == null ? "" : result.getObjectName());
        result.setPureObjectName(result.getObjectName());
    }

    // 使用 getMarketingUserPhonesV2
    @Deprecated
    public Map<String, String> getMarketingUserPhones(String ea, List<String> marketingUserIds){
        List<UserMarketingAccountEntity> entities = this.batchGet(marketingUserIds);
        return entities.stream().collect(Collectors.toMap(UserMarketingAccountEntity::getId, UserMarketingAccountEntity::getPhone, (v1, v2) -> v1));
    }


    public Map<String, String> getMarketingUserPhonesV2(String ea, List<String> marketingUserIds){
        Map<String, String> resultMap = Maps.newHashMap();
        List<List<String>> partitionList = Lists.partition(marketingUserIds, 200);
        List<String> checkBaseInfoIdList = Lists.newArrayList();
        // 先试着从数据库或者号码
        for (List<String> partition : partitionList) {
            List<UserMarketingAccountEntity> accountEntityList = this.batchGet(partition);
            Map<String, String> idToPhoneMap = accountEntityList.stream().filter(e -> StringUtils.isNotEmpty(e.getPhone())).collect(Collectors.toMap(UserMarketingAccountEntity::getId, UserMarketingAccountEntity::getPhone, (v1, v2) -> v1));
            for (String id : partition) {
                String phone = idToPhoneMap.get(id);
                if (StringUtils.isNotEmpty(phone)) {
                    resultMap.put(id, phone);
                } else {
                    checkBaseInfoIdList.add(id);
                }
            }
        }
        // 数据库没有号码的，在从基础信息中获取
        partitionList = Lists.partition(checkBaseInfoIdList, 200);
        for (List<String> partition : partitionList) {
            Map<String, UserMarketingAccountData> map = getBaseInfosByIdsV2(ea, SuperUserConstants.USER_ID, partition, InfoStateEnum.DETAIL);
            map.forEach((id, account) -> {
                if (StringUtils.isNotEmpty(account.getPhone())) {
                    resultMap.put(id, account.getPhone());
                }
            });
        }
        return resultMap;
    }
    
    
    public Map<String, String> getMarketingUserNames(String ea, List<String> marketingUserIds){
        if (marketingUserIds == null || marketingUserIds.isEmpty()){
            return new HashMap<>();
        }
        Map<String, String> marketingUserIdToNameMap = new HashMap<>(marketingUserIds.size());
        // 联系人客户线索会员企业微信客户微信
        List<UserMarketingCrmContactAccountRelationEntity> crmContactAccountRelations = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmContactAccountRelations.forEach(r -> marketingUserIdToNameMap.putIfAbsent(r.getUserMarketingId(), r.getUserName()));
        List<UserMarketingCrmAccountAccountRelationEntity> crmCustomerAccountRelations = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmCustomerAccountRelations.forEach(r -> marketingUserIdToNameMap.putIfAbsent(r.getUserMarketingId(), r.getUserName()));
        List<UserMarketingCrmLeadAccountRelationEntity> crmLeadAccountRelations = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmLeadAccountRelations.forEach(r -> marketingUserIdToNameMap.putIfAbsent(r.getUserMarketingId(), r.getUserName()));
        List<UserMarketingCrmMemberRelationEntity> crmMemberAccountRelations = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmMemberAccountRelations.forEach(r -> marketingUserIdToNameMap.putIfAbsent(r.getUserMarketingId(), r.getUserName()));
        List<UserMarketingCrmWxWorkExternalUserRelationEntity> crmWxWorkExternalUserAccountRelations = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmWxWorkExternalUserAccountRelations.forEach(r -> marketingUserIdToNameMap.putIfAbsent(r.getUserMarketingId(), r.getUserName()));
        List<UserMarketingCrmWxUserAccountRelationEntity> crmWxUserAccountRelations = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmWxUserAccountRelations.forEach(r -> marketingUserIdToNameMap.putIfAbsent(r.getUserMarketingId(), r.getUserName()));
        return marketingUserIdToNameMap;
    }
    
    public Map<String, String> getMarketingUserEmails(String ea, List<String> marketingUserIds){
        if (marketingUserIds == null || marketingUserIds.isEmpty()){
            return new HashMap<>();
        }
        Map<String, String> marketingUserIdToEmailMap = new HashMap<>(marketingUserIds.size());
        // 联系人客户线索会员企业微信客户微信
        List<UserMarketingCrmContactAccountRelationEntity> crmContactAccountRelations = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmContactAccountRelations.forEach(r -> marketingUserIdToEmailMap.putIfAbsent(r.getUserMarketingId(), r.getEmail()));
        List<UserMarketingCrmAccountAccountRelationEntity> crmCustomerAccountRelations = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmCustomerAccountRelations.forEach(r -> marketingUserIdToEmailMap.putIfAbsent(r.getUserMarketingId(), r.getEmail()));
        List<UserMarketingCrmLeadAccountRelationEntity> crmLeadAccountRelations = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmLeadAccountRelations.forEach(r -> marketingUserIdToEmailMap.putIfAbsent(r.getUserMarketingId(), r.getEmail()));
        List<UserMarketingCrmMemberRelationEntity> crmMemberAccountRelations = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmMemberAccountRelations.forEach(r -> marketingUserIdToEmailMap.putIfAbsent(r.getUserMarketingId(), r.getEmail()));
        List<UserMarketingCrmWxWorkExternalUserRelationEntity> crmWxWorkExternalUserAccountRelations = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmWxWorkExternalUserAccountRelations.forEach(r -> marketingUserIdToEmailMap.putIfAbsent(r.getUserMarketingId(), r.getEmail()));
        List<UserMarketingCrmWxUserAccountRelationEntity> crmWxUserAccountRelations = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, marketingUserIds);
        crmWxUserAccountRelations.forEach(r -> marketingUserIdToEmailMap.putIfAbsent(r.getUserMarketingId(), r.getEmail()));
        return marketingUserIdToEmailMap;
    }
    
    public String buildNameListByMarketingUserIds(String ea, List<String> marketingUserIds) {
        Map<String, String> marketingUserIdToNameMap = this.getMarketingUserNames(ea, marketingUserIds);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < marketingUserIds.size(); i++) {
            if (!Strings.isNullOrEmpty(marketingUserIdToNameMap.get(marketingUserIds.get(i)))){
                sb.append(marketingUserIdToNameMap.get(marketingUserIds.get(i)));
                if (i != marketingUserIds.size() - 1){
                    sb.append("，");
                }
            }
        }
        return sb.toString();
    }

    //更新直播会议会议报名actiontype
    public void updateEnrollActionType(String ea) {
        try {
            userMarketingStatisticService.updateEnrollActionType(ea);
        } catch (Exception e) {
            log.error("updateEnrollActionType error, ea: {}", ea);
        }
    }

    //用户行为值枚举加入map
    public Map<Integer, String> fromActionEnum() {
        Map<Integer, String> enumActionMap = new HashMap();
        for (com.facishare.marketing.common.enums.ActionTypeEnum actionType : com.facishare.marketing.common.enums.ActionTypeEnum.values()) {
            if (StringUtils.isNotEmpty(actionType.getActionCName())) {//过滤名称为空
                enumActionMap.put(actionType.getAction()+1000000, actionType.getActionCName());
            }
        }
        return enumActionMap;
    }

    public Map<Integer, String> getI8nKeyFromActionEnum() {
        Map<Integer, String> enumActionMap = new HashMap();
        for (com.facishare.marketing.common.enums.ActionTypeEnum actionType : com.facishare.marketing.common.enums.ActionTypeEnum.values()) {
            if (StringUtils.isNotEmpty(actionType.getActionI18nKey())) {//过滤名称为空
                enumActionMap.put(actionType.getAction()+1000000, actionType.getActionI18nKey());
            }
        }
        return enumActionMap;
    }

    public Map<String, String> getMarketingUserIdByObjects(String ea, List<ObjectData> objectDataList,Map<String, String> externalMap) {
        Map<String,String> userMarketingIdMaps = Maps.newHashMap();
        List<ObjectData> needObjectDataList = Lists.newArrayList();
        // 查营销用户ID
        objectDataList.forEach(e -> {
            String ExternalUserId = e.getString(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName());
            String userMarketingId = externalMap.get(ExternalUserId);
            if (StringUtils.isEmpty(userMarketingId)) {
                needObjectDataList.add(e);
            } else {
                userMarketingIdMaps.put(e.getId(),userMarketingId);
            }
        });
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(needObjectDataList)) {
            return userMarketingIdMaps;
        }
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        CountDownLatch countDownLatch = new CountDownLatch(needObjectDataList.size());
        for (ObjectData objectData : needObjectDataList) {
            executorService.submit(() ->{
                try {
                    AssociationArg associationArg = new AssociationArg();
                    associationArg.setPhone(objectData.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()) != null ? objectData.get(CrmWechatWorkExternalUserFieldEnum.PHONE.getFieldName()).toString() : null);
                    associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
                    associationArg.setAssociationId(objectData.getId());
                    associationArg.setEa(ea);
                    associationArg.setAdditionalAssociationId(objectData.getString(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName()));
                    associationArg.setUserName(objectData.getName());
                    associationArg.setEmail(objectData.getString("email"));
                    AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
                    if (associationResult != null && StringUtils.isNotEmpty(associationResult.getUserMarketingAccountId())) {
                        userMarketingIdMaps.put(objectData.getId(), associationResult.getUserMarketingAccountId());
                    }
                } catch (Exception e) {
                    log.warn("getMarketingUserIdByObjects error arg:{}", JSON.toJSONString(objectDataList));
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await(5L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("getMarketingUserIdByObjects timeout ea:{} objectData:{}", ea, objectDataList);
        }
        executorService.shutdown();
        return userMarketingIdMaps;
    }

    // 更新营销用户关联的对象的标签，注意：这是全量替换
    public void updateTagToObjectByUserMarketingId(String ea, String userMarketingId, List<TagName> tagNameList) {
        List<String> objectApiNameList = buildUserMarketingApiNameListByEa(ea);
        List<String> userMarketingIdList = Lists.newArrayList(userMarketingId);
        for (String objectApiName : objectApiNameList) {
            ChannelEnum byApiName = ChannelEnum.getByApiName(objectApiName);
            if (null == byApiName) {
                continue;
            }
            List<String> dataIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, byApiName.getType(), userMarketingIdList);
            if (CollectionUtils.isEmpty(dataIds)) {
                continue;
            }
            if (dataIds.size() > 20) {
                log.info("updateTagToObjectByUserMarketingId return ea:{}  userMarketingIds:{} dataIds size {}", ea, userMarketingId, dataIds.size());
                return;
            }
            metadataTagManager.updateTagsToObjectDatas(ea, objectApiName, dataIds, Lists.newArrayList(tagNameList));
        }
    }

    // 追加营销用户关联的对象的标签，注意：这是追加打
    public void appendTagToObjectByUserMarketingId(String ea, String userMarketingId, List<TagName> tagNameList) {
        List<String> objectApiNameList = buildUserMarketingApiNameListByEa(ea);
        List<String> userMarketingIdList = Lists.newArrayList(userMarketingId);
        for (String objectApiName : objectApiNameList) {
            ChannelEnum byApiName = ChannelEnum.getByApiName(objectApiName);
            if (null == byApiName) {
                continue;
            }
            List<String> dataIds = userMarketingAccountRelationManager.listDataIdsByUserMarketingAccountIds(ea, byApiName.getType(), userMarketingIdList);
            if (CollectionUtils.isEmpty(dataIds)) {
                continue;
            }
            metadataTagManager.addTagsToObjectDatas(ea, objectApiName, dataIds, Lists.newArrayList(tagNameList));
        }
    }

    public boolean isExcludeApiName(String ea, String objectApiName){
        UserMarketingExcludeObjectEntity entity = marketingUserGroupExcludeObjectDAO.getByObjectApiName(ea,  objectApiName);
        return entity != null;
    }

    public Optional<ObjectData> queryExternalObjectByUnionIdAndOpenId(String ea, Integer fsUserId, String
            unionId, String openId) {
        try {
            //根据unionid查询企业微信客户对象
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
            PaasQueryArg queryArg = new PaasQueryArg(0, 1);
            queryArg.addFilter(CrmWechatWorkExternalUserFieldEnum.WX_UNION_ID.getFieldName(), PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(unionId));
            queryFilterArg.setQuery(queryArg);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, fsUserId, queryFilterArg, null, 1);
            if (objectDataInnerPage != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                return Optional.of(objectDataInnerPage.getDataList().get(0));
            }

            //调用企业微信接口换取企业微信客户外部联系人id
            if (unionId != null && openId != null) {
                String accessToken = qywxManager.getAccessToken(ea);
                Optional<String> externalUsrIdOpt = qywxManager.getExternalUsrIdByUnionId(accessToken, unionId, openId);
                if (!externalUsrIdOpt.isPresent()) {
                    return Optional.empty();
                }

                Optional<ObjectData> externalObjectOpt = queryExternalObjectByExternalUserId(ea, fsUserId, externalUsrIdOpt.get());
                if (externalObjectOpt.isPresent()) {
                    //更新企业微信客户对象的unionid
                    HashMap<String, Object> dataMap = new HashMap<>();
                    dataMap.put("_id", externalObjectOpt.get().getId());
                    dataMap.put(CrmWechatWorkExternalUserFieldEnum.WX_UNION_ID.getFieldName(), unionId);
                    crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), dataMap);
                    return externalObjectOpt;
                }
            }

            return Optional.empty();
        } catch (Exception e) {
            log.warn("UserMarketingAccountManager -> queryExternalObjectByUnionIdAndOpenId error", e);
            return Optional.empty();
        }
    }

    private Optional<ObjectData> queryExternalObjectByExternalUserId(String ea, Integer fsUserId, String
            externalUserId) {
        //根据unionid查询企业微信客户对象
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        PaasQueryArg queryArg = new PaasQueryArg(0, 1);
        queryArg.addFilter(CrmWechatWorkExternalUserFieldEnum.EXTERNAL_USER_ID.getFieldName(), PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(externalUserId));
        queryFilterArg.setQuery(queryArg);
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, fsUserId, queryFilterArg, null, 1);
        if (objectDataInnerPage != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            return Optional.of(objectDataInnerPage.getDataList().get(0));
        }

        return Optional.empty();
    }

    public Optional<ObjectData> queryWechatObjectByOpenId(String ea, Integer fsUserId, String openId) {
        try {
            boolean existObject = crmV2Manager.isExistObject(ea, CrmObjectApiNameEnum.WECHAT.getName());
            if (!existObject) {
                return Optional.empty();
            }
            PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
            queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT.getName());
            PaasQueryArg queryArg = new PaasQueryArg(0, 1);
            queryArg.addFilter(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName(), PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(openId));
            queryFilterArg.setQuery(queryArg);
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, fsUserId, queryFilterArg, null, 1);
            if (objectDataInnerPage != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
                return Optional.of(objectDataInnerPage.getDataList().get(0));
            }

            return Optional.empty();
        } catch (Exception e) {
            log.warn("UserMarketingAccountManager -> queryWechatObjectByOpenId error", e);
            return Optional.empty();
        }
    }

    public Map<String, List<String>> getObjectIdsByUserMarketingAccountDataMap(String ea, List<String> userMarketingAccountIds) {
        Map<String, List<String>> objectMap = new HashMap<>();
        List<UserMarketingCrmContactAccountRelationEntity> crmContactAccountRelationEntities = userMarketingCrmContactAccountRelationDao
                .listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmContactAccountRelationEntities)){
            List<String> objectIds = crmContactAccountRelationEntities.stream().map(UserMarketingCrmContactAccountRelationEntity::getCrmContactId).collect(Collectors.toList());
            objectMap.put(CrmObjectApiNameEnum.CONTACT.getName(), objectIds);
        }

        List<UserMarketingCrmAccountAccountRelationEntity> crmAccountAccountRelationEntities = userMarketingCrmAccountAccountRelationDao
                .listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmAccountAccountRelationEntities)){
            List<String> objectIds = crmAccountAccountRelationEntities.stream().map(UserMarketingCrmAccountAccountRelationEntity::getCrmAccountId).collect(Collectors.toList());
            objectMap.put(CrmObjectApiNameEnum.CUSTOMER.getName(), objectIds);
        }

        List<UserMarketingCrmLeadAccountRelationEntity> crmLeadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmLeadAccountRelationEntities)){
            List<String> objectIds = crmLeadAccountRelationEntities.stream().map(UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId).collect(Collectors.toList());
            objectMap.put(CrmObjectApiNameEnum.CRM_LEAD.getName(), objectIds);
        }

        List<UserMarketingCrmMemberRelationEntity> crmMemberMarketingUserRelations = userMarketingCrmMemberRelationDao
                .listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmMemberMarketingUserRelations)){
            List<String> objectIds = crmMemberMarketingUserRelations.stream().map(UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId).collect(Collectors.toList());
            objectMap.put(CrmObjectApiNameEnum.MEMBER.getName(), objectIds);
        }
        List<UserMarketingCrmWxWorkExternalUserRelationEntity> crmWxWorkExternalUserMarketingUserRelations = userMarketingCrmWxWorkExternalUserRelationDao
                .listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmWxWorkExternalUserMarketingUserRelations)){
            List<String> objectIds = crmWxWorkExternalUserMarketingUserRelations.stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId).collect(Collectors.toList());
            objectMap.put(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName(), objectIds);
        }

        List<UserMarketingCrmWxUserAccountRelationEntity> crmWxUserAccountRelationEntities = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmWxUserAccountRelationEntities)){
            List<String> objectIds = crmWxUserAccountRelationEntities.stream().map(UserMarketingCrmWxUserAccountRelationEntity::getCrmWxUserId).collect(Collectors.toList());
            objectMap.put(CrmObjectApiNameEnum.WECHAT.getName(), objectIds);
        }
        List<UserMarketingCustomizeObjectRelationEntity> crmCustomizeObjectAccountRelationEntities = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds);
        if (CollectionUtils.isNotEmpty(crmCustomizeObjectAccountRelationEntities)){
            for (UserMarketingCustomizeObjectRelationEntity entity : crmCustomizeObjectAccountRelationEntities) {
                if (objectMap.get(entity.getObjectApiName()) == null) {
                    objectMap.put(entity.getObjectApiName(), Lists.newArrayList());
                }
                objectMap.get(entity.getObjectApiName()).add(entity.getObjectId());
            }
        }

        return objectMap;
    }

    //解绑营销用户,otherParams包含id类型和id
    public void unbindMarketingUserBindInfo(String ea, Map<String, String> map) {        
         Set<String> userMarketingAccountIds = Sets.newHashSet();
         for (Map.Entry<String, String> entry : map.entrySet()) {
             String key = entry.getKey();
             String value = entry.getValue();

             //如果key是"userMarketingId", 直接从value获取营销用户id；其他情况，value是对象id列表
             if ("userMarketingId".equals(key)) {
                 userMarketingAccountIds.add(value);
             } else {
                 List<String> userMarketingAccountValueIds = userMarketingAccountRelationManager.listUserMarketingAccountIdsByDataIds(ea, ChannelEnum.getByApiName(key).getType(), Lists.newArrayList(value));
                 if (CollectionUtils.isNotEmpty(userMarketingAccountValueIds)) {
                     userMarketingAccountIds.addAll(userMarketingAccountValueIds);
                 }
             }
             if (CollectionUtils.isEmpty(userMarketingAccountIds)) {
                 return;
             }

             deleteUserMarketingAccountRelation(ea, Lists.newArrayList(userMarketingAccountIds));
         }
    }

    //根据营销用户id接触关联关系
    @Transactional
    public void deleteUserMarketingAccountRelation(String ea, List<String> userMarketingAccountIds) {
        //根据ea,删除各个DAO中的关联关系，同时生成各个DAO中删除语句
        int ret1 = userMarketingCrmAccountAccountRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret2 = userMarketingCrmContactAccountRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret3 = userMarketingCrmLeadAccountRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret4 = userMarketingCrmMemberRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret5 = userMarketingCrmWxUserAccountRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret6 = userMarketingCrmWxWorkExternalUserRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret7 = userMarketingBrowserUserRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret8 = userMarketingWxServiceAccountRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret9 = userMarketingWxWorkExternalUserRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret10 = userMarketingCrmCustomizeObjectRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        int ret11 = userMarketingMiniappAccountRelationDao.deleteByUserMarketingIds(ea, userMarketingAccountIds);
        userMarketingAccountDAO.deleteByIdList(userMarketingAccountIds);
        log.info("userMarketingId:{} ret1:{} ret2:{} ret3:{} ret4:{} ret5:{} ret6:{} ret7:{} ret8:{} ret9:{} ret10:{} ret11:{}", userMarketingAccountIds,ret1, ret2, ret3, ret4, ret5, ret6, ret7, ret8, ret9, ret10, ret11);
    }

    public UserMarketingAccountEntity getUserMarketingAccountByEmail(String ea, String email) {
        // 从各个关联表中查询营销用户ID
        Set<String> userMarketingIds = new HashSet<>();
        
        // 查询联系人关联表
        List<UserMarketingCrmContactAccountRelationEntity> contactRelations = userMarketingCrmContactAccountRelationDao.listByEmail(ea, email);
        if (CollectionUtils.isNotEmpty(contactRelations)) {
            userMarketingIds.addAll(contactRelations.stream()
                .map(UserMarketingCrmContactAccountRelationEntity::getUserMarketingId)
                .collect(Collectors.toList()));
        }
        
        // 查询客户关联表
        List<UserMarketingCrmAccountAccountRelationEntity> accountRelations = userMarketingCrmAccountAccountRelationDao.listByEmail(ea, email);
        if (CollectionUtils.isNotEmpty(accountRelations)) {
            userMarketingIds.addAll(accountRelations.stream()
                .map(UserMarketingCrmAccountAccountRelationEntity::getUserMarketingId)
                .collect(Collectors.toList()));
        }
        
        // 查询线索关联表
        List<UserMarketingCrmLeadAccountRelationEntity> leadRelations = userMarketingCrmLeadAccountRelationDao.listByEmail(ea, email);
        if (CollectionUtils.isNotEmpty(leadRelations)) {
            userMarketingIds.addAll(leadRelations.stream()
                .map(UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId)
                .collect(Collectors.toList()));
        }
        
        // 查询会员关联表
        List<UserMarketingCrmMemberRelationEntity> memberRelations = userMarketingCrmMemberRelationDao.listByEmail(ea, email);
        if (CollectionUtils.isNotEmpty(memberRelations)) {
            userMarketingIds.addAll(memberRelations.stream()
                .map(UserMarketingCrmMemberRelationEntity::getUserMarketingId)
                .collect(Collectors.toList()));
        }

        //自定义对象
        List<UserMarketingCustomizeObjectRelationEntity> customizeObjectRelations = userMarketingCrmCustomizeObjectRelationDao.listByEmail(ea, email);
        if (CollectionUtils.isNotEmpty(customizeObjectRelations)) {
            userMarketingIds.addAll(customizeObjectRelations.stream()
                .map(UserMarketingCustomizeObjectRelationEntity::getUserMarketingId)
                .collect(Collectors.toList()));
        }
        
        if (CollectionUtils.isEmpty(userMarketingIds)) {
            return null;
        }
        
        // 按创建时间倒序查询营销用户表
        return userMarketingAccountDAO.getLatestIdByIds(eieaConverter.enterpriseAccountToId(ea), Lists.newArrayList(userMarketingIds));
    }
}
