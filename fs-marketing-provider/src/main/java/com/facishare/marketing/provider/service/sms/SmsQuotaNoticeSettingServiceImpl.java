package com.facishare.marketing.provider.service.sms;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.arg.sms.AddSmsQuotaNoticeSettingArg;
import com.facishare.marketing.api.result.sms.SmsQuotaNoticeSettingResult;
import com.facishare.marketing.api.service.sms.SmsQuotaNoticeSettingService;
import com.facishare.marketing.common.enums.SmsQuotaNoticeStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.sms.QuotaDAO;
import com.facishare.marketing.provider.dao.sms.SmsQuotaNoticeSettingDao;
import com.facishare.marketing.provider.entity.sms.QuotaEntity;
import com.facishare.marketing.provider.entity.sms.SmsQuotaNoticeSettingEntity;
import com.facishare.marketing.provider.manager.CrmPaasOrgDataManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.UserRoleManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.api.CRMNotifyService;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.RemindRecordItem;
import com.fxiaoke.model.crmNotify.AddRemindRecordArg;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.facishare.converter.EIEAConverter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component("smsQuotaNoticeSettingService")
public class SmsQuotaNoticeSettingServiceImpl implements SmsQuotaNoticeSettingService {
    @Autowired
    private SmsQuotaNoticeSettingDao smsQuotaNoticeSettingDao;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @ReloadableProperty("host")
    private String host;
    @Autowired
    private CRMNotifyService crmNotifyService;
    @Autowired
    private QuotaDAO quotaDAO;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CrmPaasOrgDataManager crmPaasOrgDataManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private UserRoleManager userRoleManager;

    @Override
    public Result<SmsQuotaNoticeSettingResult> getSetting(String ea) {
        SmsQuotaNoticeSettingEntity entity = smsQuotaNoticeSettingDao.querySmsQuotaNoticeSetting(ea);
        if(entity==null){
            //返回空是否打开配置,为空表示没开启
            SmsQuotaNoticeSettingResult result = new SmsQuotaNoticeSettingResult();
            result.setStatus(SmsQuotaNoticeStatusEnum.CLOSE.getStatus());
            return Result.newSuccess(result);
        }
        SmsQuotaNoticeSettingResult result = BeanUtil.copy(entity,SmsQuotaNoticeSettingResult.class);
        NoticeSendArg.NoticeVisibilityVO context = JSON.parseObject(entity.getContext(), NoticeSendArg.NoticeVisibilityVO.class);
        result.setContext(context);
        return  Result.newSuccess(result);
    }

    @Override
    public Result<Boolean> addOrUpdateSmsQuotaNoticeSetting(AddSmsQuotaNoticeSettingArg arg,String ea) {
        String context = JSON.toJSONString(arg.getContext());
        List<Integer> userIds = getUserIds(arg.getContext(), ea);
        String userIdList = JSON.toJSONString(userIds);
        SmsQuotaNoticeSettingEntity entity = smsQuotaNoticeSettingDao.querySmsQuotaNoticeSetting(ea);
        if(entity==null){
            SmsQuotaNoticeSettingEntity addEntity = new SmsQuotaNoticeSettingEntity();
            addEntity.setId(UUIDUtil.getUUID());
            addEntity.setEa(ea);
            addEntity.setQuotaNotice(arg.getQuotaNotice());
            addEntity.setStatus(SmsQuotaNoticeStatusEnum.OPEN.getStatus());
            addEntity.setUserIdList(userIdList);
            addEntity.setContext(context);
            int i = smsQuotaNoticeSettingDao.insertSmsQuotaNoticeSetting(addEntity);
            if(i==1){
                return Result.newSuccess(true);
            }
            return Result.newSuccess(false);
        }
        int i = smsQuotaNoticeSettingDao.updateSmsQuotaNoticeSetting(ea, arg.getQuotaNotice(), userIdList, context,arg.getStatus());
        if(i==1){
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Boolean> updateQuotaNoticeStatus(String ea, Integer status) {
        int i = smsQuotaNoticeSettingDao.updateNoticeSettingStatus(ea, status);
        if(i==1){
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    @Override
    public Result<Boolean> updateSmsQuotaNoticeSetting(AddSmsQuotaNoticeSettingArg arg,String ea) {
        String context = JSON.toJSONString(arg.getContext());
        List<Integer> userIds = getUserIds(arg.getContext(), ea);
        String userIdList = JSON.toJSONString(userIds);
        int i = smsQuotaNoticeSettingDao.updateSmsQuotaNoticeSetting(ea, arg.getQuotaNotice(), userIdList, context,arg.getStatus());
        if(i==1){
            return Result.newSuccess(true);
        }
        return Result.newSuccess(false);
    }

    //定时任务
    @Override
    public Result<Boolean> syncSmsQuotaNoticeTask(){
        List<SmsQuotaNoticeSettingEntity> noticeTaskList = smsQuotaNoticeSettingDao.getEaToSyncSmsQuotaNoticeTask();
        if(CollectionUtils.isEmpty(noticeTaskList)){
            log.info("syncSmsQuotaNoticeTask is over, eas size is zero");
            return Result.newSuccess(true);
        }
        noticeTaskList.forEach(m -> {
            //过滤掉停用企业、营销通配额过期企业
            if (marketingActivityRemoteManager.enterpriseStop(m.getEa()) || appVersionManager.getCurrentAppVersion(m.getEa()) == null) {
                log.info("SmsQuotaNoticeSettingServiceImpl.syncSmsQuotaNoticeTask failed enterprise stop or license expire ea:{}", m.getEa());
                return;
            }
            ThreadPoolUtils.execute(() -> SendCrmNotice(m), ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        });
        return Result.newSuccess(true);
    }

    public void SendCrmNotice(SmsQuotaNoticeSettingEntity entity){
        AddRemindRecordArg arg = new AddRemindRecordArg();
        RemindRecordItem remindRecordItem = new RemindRecordItem();
        remindRecordItem.setSourceId("DXTZ"+ UUIDUtil.getUUID());
        //发送人
        remindRecordItem.setSenderId(-10000);
        //通知消息类型,92自定义消息
        remindRecordItem.setType(92);
        List<Integer> userIdList = JSON.parseObject(entity.getUserIdList(), List.class);
        //接收人
        remindRecordItem.setReceiverIDs(userIdList);
        if(userIdList.size()==0){
            return;
        }
        //标题
        remindRecordItem.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SMS_SMSQUOTANOTICESETTINGSERVICEIMPL_163));
        //  remindRecordItem.setTitleInfo(new InternationalItem());
        QuotaEntity quotaEntity = quotaDAO.queryEntityByEa(entity.getEa());
        //文本信息
        remindRecordItem.setFullContent(I18nUtil.get(I18nKeyEnum.MARK_SMS_SMSQUOTANOTICESETTINGSERVICEIMPL_167)+quotaEntity.getLeft()+I18nUtil.get(I18nKeyEnum.MARK_SMS_SMSQUOTANOTICESETTINGSERVICEIMPL_167_1));
        //  remindRecordItem.setFullContentInfo(new InternationalItem());
        //跳转url类型
        remindRecordItem.setUrlType(0);
        //todo pass平台暂不知支持该种类型跳转.二期实现
//        remindRecordItem.setInnerPlatformWebUrl(host+"/XV/Home/Index#crmmanage/=/module-smsmanage");
//        remindRecordItem.setInnerPlatformMobileUrl("");
//        remindRecordItem.setOutPlatformUrl("");
//        remindRecordItem.setExtraChannelList(Lists.newArrayList());
//        remindRecordItem.setExtraDataMap(Maps.newHashMap());
//        remindRecordItem.setTemplateIdKeyMap(Maps.newHashMap());
//        remindRecordItem.setTemplateIdKeyListMap(Maps.newHashMap());
        arg.setRemindRecordItem(remindRecordItem);
        arg.setUuid(UUIDUtil.getUUID());
        arg.setEi(eieaConverter.enterpriseAccountToId(entity.getEa()));
        MessageResponse record = null;
        try {
            record = crmNotifyService.addRemindRecord(arg);
        } catch (FRestClientException e) {
            throw new RuntimeException(e);
        }
    }

    //获取纷享员工id
    private List<Integer> getUserIds(NoticeSendArg.NoticeVisibilityVO vo,String ea){
        Set<Integer> userIdSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(vo.getUserIds())){
            userIdSet.addAll(vo.getUserIds());
        }
        List<Integer> userIdByGroups = null;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getUserGroups())){
            List<String> userGroupIds = vo.getUserGroups().stream().map(group -> group.getUserGroupId()).collect(Collectors.toList());
            userIdByGroups = crmPaasOrgDataManager.queryUserIdsByUserGroups(ea, userGroupIds);
        }
        if (!CollectionUtils.isEmpty(userIdByGroups)){
            userIdSet.addAll(userIdByGroups);
        }
        if (!CollectionUtils.isEmpty(vo.getDepartmentIds())) {
            List<Integer> userIdsByDepartmentIds = null;
            if (vo.getDepartmentIds().contains(Constant.WHOLE_COMPANY_ID)) {
                userIdsByDepartmentIds = fsAddressBookManager.getEmployeeIdsByEa(ea);
            } else {
                userIdsByDepartmentIds = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, vo.getDepartmentIds());
            }
            if (!CollectionUtils.isEmpty(userIdsByDepartmentIds)) {
                userIdSet.addAll(userIdsByDepartmentIds);
            }
        }
        List<String> roleCodes = null;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getRoles())){
            roleCodes = vo.getRoles().stream().map(roleItem -> roleItem.getRoleCode()).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(roleCodes)) {
            List<Integer> userIdsByRoles = userRoleManager.getEmployeeIdsByRoles(ea, roleCodes);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userIdsByRoles)) {
                userIdSet.addAll(userIdsByRoles);
            }
        }
        if (CollectionUtils.isEmpty(userIdSet)) {
            log.info("create smsQuota task userIdSet size is zero, ea:{}",  ea);
            return null;
        }
        ArrayList<Integer> userList = new ArrayList<>(userIdSet);
        return userList;
    }
}