package com.facishare.marketing.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.DayTrendData;
import com.facishare.marketing.api.result.EmployeeRankingData;
import com.facishare.marketing.api.result.EmployeeRankingResult;
import com.facishare.marketing.api.result.EnterpriseStatisticSumUpResult;
import com.facishare.marketing.api.result.EnterpriseStatisticTrendResult;
import com.facishare.marketing.api.result.UserStatisticResult;
import com.facishare.marketing.api.service.EnterpriseStatisticService;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.CountTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.EnterpriseAmountStatisticDao;
import com.facishare.marketing.provider.dao.EnterpriseDayStatisticDao;
import com.facishare.marketing.provider.dao.EnterpriseEmployeeDayStatisticDao;
import com.facishare.marketing.provider.dao.EnterpriseObjectAmountStatisticDao;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.entity.EnterpriseAmountStatisticEntity;
import com.facishare.marketing.provider.entity.EnterpriseDayStatisticEntity;
import com.facishare.marketing.provider.entity.EnterpriseEmployeeDayStatisticEntity;
import com.facishare.marketing.provider.manager.AuthManager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.organization.api.model.employee.EmployeeDto;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("enterpriseStatisticService")
public class EnterpriseStatisticServiceImpl implements EnterpriseStatisticService {
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AuthManager authManager;
    @Autowired
    private EnterpriseAmountStatisticDao enterpriseAmountStatisticDao;
    @Autowired
    private EnterpriseDayStatisticDao enterpriseDayStatisticDao;
    @Autowired
    private EnterpriseObjectAmountStatisticDao enterpriseObjectAmountStatisticDao;
    @Autowired
    private EnterpriseEmployeeDayStatisticDao enterpriseEmployeeDayStatisticDao;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private DingManager dingManager;

    private static EnterpriseStatisticTrendResult doGetEnterpriseTrendFromDayStatisticList(List<EnterpriseDayStatisticEntity> entities, DateTime startDate, DateTime endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, EnterpriseDayStatisticEntity> dayMap = entities.stream().collect(Collectors.toMap(entity -> sdf.format(entity.getDate()), e -> e, (v1, v2) -> v1));
        EnterpriseStatisticTrendResult result = new EnterpriseStatisticTrendResult();
        DateTime currentDate = new DateTime(startDate);
        while (currentDate.getMillis() <= endDate.getMillis()) {
            String currentDateStr = sdf.format(currentDate.toDate());
            EnterpriseDayStatisticEntity entity = dayMap.get(currentDateStr);
            if (entity == null) {
                result.getActiveTrend().add(new DayTrendData(currentDateStr, 0));
                result.getForwardTrend().add(new DayTrendData(currentDateStr, 0));
                result.getLookUpTrend().add(new DayTrendData(currentDateStr, 0));
                result.getSpreadTrend().add(new DayTrendData(currentDateStr, 0));
                result.getSaveToCrmLeadTrend().add(new DayTrendData(currentDateStr, 0));
                result.getSaveToCrmCustomerTrend().add(new DayTrendData(currentDateStr, 0));
            } else {
                result.getActiveTrend().add(new DayTrendData(currentDateStr, entity.getActiveCount()));
                result.getForwardTrend().add(new DayTrendData(currentDateStr, entity.getForwardCount()));
                result.getLookUpTrend().add(new DayTrendData(currentDateStr, entity.getLookUpCount()));
                result.getSpreadTrend().add(new DayTrendData(currentDateStr, entity.getSpreadCount()));
                result.getSaveToCrmLeadTrend().add(new DayTrendData(currentDateStr, entity.getCrmLeadIncrementCount()));
                result.getSaveToCrmCustomerTrend().add(new DayTrendData(currentDateStr, entity.getCrmCustomerIncrementCount()));
            }
            currentDate = currentDate.plusDays(1);
        }
        return result;
    }

    @Override
    public Result<UserStatisticResult> userStatistic(String ea, Integer fsUserId) {
        int bindUserCount = 0;
        int totalCount = 0;
        if (dingManager.isDingAddressbook(ea)){
            List<EmployeeDto> employeeDtoList = dingManager.getCountEmployeeByEa(ea);
            if (CollectionUtils.isEmpty(employeeDtoList)) {
                log.warn("employeeDtoList is null, ea={}, fsUserId={}", ea, fsUserId);
                return new Result<>(SHErrorCode.BOOK_USER_NOT_EXIST);
            }
            totalCount = employeeDtoList.size();
            bindUserCount = fsBindManager.queryFSBindDingCount(ea,AccountTypeEnum.DING_MINI_APP.getType());
        } else {
            List<Integer> employeeIdList = authManager.getIsolationAllEmployeeIdList(ea, fsUserId, null);
           //employeeDtoList = authManager.getIsolationAllEmployee(ea, fsUserId, null);
            if (CollectionUtils.isEmpty(employeeIdList)) {
                log.warn("employeeDtoList is null, ea={}, fsUserId={}", ea, fsUserId);
                return new Result<>(SHErrorCode.BOOK_USER_NOT_EXIST);
            }
            employeeIdList = employeeIdList.stream().distinct().collect(Collectors.toList());
            totalCount = employeeIdList.size();
            log.info("employeeIdsList.size={}", employeeIdList.size());
            String wxAppId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
            com.facishare.marketing.common.util.PageUtil<Integer> pageUtil = new com.facishare.marketing.common.util.PageUtil<>(employeeIdList, 5000);
            for (int i = 0; i < pageUtil.getPageCount(); i++){
                List<Integer> currentPage = pageUtil.getPagedList(i + 1);
                int fsBindEntityCount = 0;
                if (dingManager.isDingAddressbook(ea)){
                    fsBindEntityCount = fsBindManager.queryFSBindByEmployeeIdsCount(ea, currentPage, AccountTypeEnum.DING_MINI_APP.getType(), null);
                } else  {
                    fsBindEntityCount = fsBindManager.queryFSBindByEmployeeIdsCountWithoutType(ea, wxAppId, currentPage);
                }
                bindUserCount += fsBindEntityCount;
            }
        }
        log.info("fsBindEntityCount={}", bindUserCount);

        UserStatisticResult userStatisticResult = new UserStatisticResult();


        userStatisticResult.setBindUserCount(bindUserCount);//已经开通
        userStatisticResult.setUnOpenUserCount(Math.max(totalCount - bindUserCount, 0));//未开通的
        String openRate = "0";
        if (totalCount > 0 && bindUserCount > 0) {
            // 创建一个数值格式化对象
            NumberFormat numberFormat = NumberFormat.getInstance();
            // 设置精确到小数点后2位
            numberFormat.setMaximumFractionDigits(0);
            openRate = numberFormat.format((float) bindUserCount / (float) totalCount * 100);
        }
        userStatisticResult.setOpenRate(openRate + "%");
        return new Result<>(SHErrorCode.SUCCESS, userStatisticResult);
    }

    @Override
    public Result<EnterpriseStatisticSumUpResult> getEnterpriseStatisticSumUpResult(String ea, Integer recentDay, Boolean listAll) {
        EnterpriseStatisticSumUpResult result = new EnterpriseStatisticSumUpResult();
        if (listAll) {
            EnterpriseAmountStatisticEntity amountStatisticEntity = enterpriseAmountStatisticDao.getByEa(ea);
            if (amountStatisticEntity != null) {
                result.setActiveCount(amountStatisticEntity.getActiveCount());
                result.setForwardCount(amountStatisticEntity.getForwardCount());
                result.setLookUpCount(amountStatisticEntity.getLookUpCount());
                result.setSpreadCount(amountStatisticEntity.getSpreadCount());
                result.setSaveToCrmLeadCount(amountStatisticEntity.getCrmLeadAccumulationCount());
                result.setSaveToCrmCustomerCount(amountStatisticEntity.getCrmLeadAccumulationCount());
            }
            result.setSpreadEmployeeCount(enterpriseEmployeeDayStatisticDao.countSpreadEmployee(ea, null, null));
            result.setEmployeeSpreadCount(enterpriseEmployeeDayStatisticDao.countEmployeeSpread(ea, null, null));
        } else {
            DateTime endDate = DateTime.now();
            DateTime startDate = endDate.minusDays(recentDay - 1);
            List<EnterpriseDayStatisticEntity> entities = enterpriseDayStatisticDao.listByEaAndDateSpan(ea, startDate.toDate(), endDate.toDate());
            entities.forEach(entity -> {
                result.setActiveCount(result.getActiveCount() + entity.getActiveCount());
                result.setForwardCount(result.getForwardCount() + entity.getForwardCount());
                result.setLookUpCount(result.getLookUpCount() + entity.getLookUpCount());
                result.setSpreadCount(result.getSpreadCount() + entity.getSpreadCount());
                result.setSaveToCrmLeadCount(result.getSaveToCrmLeadCount() + entity.getCrmLeadIncrementCount());
                result.setSaveToCrmCustomerCount(result.getSaveToCrmCustomerCount() + entity.getCrmCustomerIncrementCount());
            });
            result.setSpreadEmployeeCount(enterpriseEmployeeDayStatisticDao.countSpreadEmployee(ea, startDate.toDate(), endDate.toDate()));
            result.setEmployeeSpreadCount(enterpriseEmployeeDayStatisticDao.countEmployeeSpread(ea, startDate.toDate(), endDate.toDate()));
        }
        result.setSpreadObjectCount(enterpriseObjectAmountStatisticDao.countSpreadObject(ea));
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<EnterpriseStatisticTrendResult> getEnterpriseStatisticTrendResult(String ea, Integer recentDay) {
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(recentDay - 1);
        List<EnterpriseDayStatisticEntity> entities = enterpriseDayStatisticDao.listByEaAndDateSpan(ea, startDate.toDate(), endDate.toDate());
        EnterpriseStatisticTrendResult result = doGetEnterpriseTrendFromDayStatisticList(entities, startDate, endDate);
        return Result.newSuccess(result);
    }

    @Override
    public Result<EmployeeRankingResult> getEmployeeRankingResult(String ea, Integer opId, Integer recentDay, Integer topRankingCount) {
        DateTime endDate = DateTime.now();
        DateTime startDate = endDate.minusDays(recentDay - 1);
        List<EnterpriseEmployeeDayStatisticEntity> leadTop = enterpriseEmployeeDayStatisticDao
            .orderByCountType(ea, startDate.toDate(), endDate.toDate(), CountTypeEnum.CRM_LEAD_INCREMENT.getType(), topRankingCount);
        List<Integer> leadFsUserIds = leadTop.stream().filter(Objects::nonNull).map(EnterpriseEmployeeDayStatisticEntity::getFsUserId).collect(Collectors.toList());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> leadFsUserMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, leadFsUserIds, true);
        //crm员工线索数
        List<EmployeeRankingData> leadRanking = leadTop.stream().map(e -> {
            EmployeeRankingData employeeRankingData = new EmployeeRankingData();
            employeeRankingData.setFsUserId(e.getFsUserId());
            employeeRankingData.setCount(e.getCrmLeadIncrementCount());
            FsAddressBookManager.FSEmployeeMsg msg = leadFsUserMap.get(e.getFsUserId());
            if (msg != null) {
                employeeRankingData.setDepartment(msg.getDepartment());
                employeeRankingData.setEmployeeName(msg.getName());
            }
            return employeeRankingData;
        }).sorted((v1, v2) -> v2.getCount().compareTo(v1.getCount())).collect(Collectors.toList());

        List<EnterpriseEmployeeDayStatisticEntity> spreadTop = enterpriseEmployeeDayStatisticDao
            .orderByCountType(ea, startDate.toDate(), endDate.toDate(), CountTypeEnum.SPREAD_COUNT.getType(), topRankingCount);
        List<Integer> customerFsUserIds = spreadTop.stream().filter(Objects::nonNull).map(EnterpriseEmployeeDayStatisticEntity::getFsUserId).collect(Collectors.toList());
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> spreadFsUserMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, customerFsUserIds, true);
        //crm员工推广次数
        List<EmployeeRankingData> spreadRanking = spreadTop.stream().map(e -> {
            EmployeeRankingData employeeRankingData = new EmployeeRankingData();
            employeeRankingData.setFsUserId(e.getFsUserId());
            employeeRankingData.setCount(e.getSpreadCount());
            FsAddressBookManager.FSEmployeeMsg msg = spreadFsUserMap.get(e.getFsUserId());
            if (msg != null) {
                employeeRankingData.setDepartment(msg.getDepartment());
                employeeRankingData.setEmployeeName(msg.getName());
            }
            return employeeRankingData;
        }).sorted((v1, v2) -> v2.getCount().compareTo(v1.getCount())).collect(Collectors.toList());
        EmployeeRankingResult result = new EmployeeRankingResult();
        result.setSaveToCrmLeadEmployeeRankings(leadRanking);
        result.setSaveToSpreadCountRankings(spreadRanking);
        return Result.newSuccess(result);
    }

    @Override
    public Result<Integer> getEnterpriseEmployeeLeadsWeeklyStatistics(Date startDate, Date endDate) {
        Integer count = enterpriseEmployeeDayStatisticDao.getEnterpriseEmployeeLeadsWeeklyStatistics(startDate, endDate);
        return new Result<>(SHErrorCode.SUCCESS, count);
    }

}
