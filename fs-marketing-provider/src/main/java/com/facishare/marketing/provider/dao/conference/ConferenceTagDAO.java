package com.facishare.marketing.provider.dao.conference;

import com.facishare.marketing.provider.entity.ConferenceTagEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface ConferenceTagDAO {
    @Insert("INSERT INTO conference_tag(id, conference_id, tags, create_time, update_time)\n"
            + "        VALUES (#{id}, #{conferenceId}, #{tags}, now(), now() )")
    int addConferenceTag(ConferenceTagEntity entity);

    @Select("select * from conference_tag where id = #{id}")
    ConferenceTagEntity queryById(@Param("id") String id);

    @Select("select * from conference_tag where conference_id = #{conferenceId} order by create_time desc limit 1")
    ConferenceTagEntity queryByConferenceId(@Param("conferenceId") String conferenceId);
}
