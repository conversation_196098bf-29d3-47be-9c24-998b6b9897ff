package com.facishare.marketing.provider.service.partner;

import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.service.PartnerService;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.user.UserRelationTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.enterpriserelation2.arg.GetDownstreamEmployeeInfoArg;
import com.fxiaoke.enterpriserelation2.arg.GetEnterpriseShortNameArg;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("partnerService")
public class PartnerServiceImpl implements PartnerService {

    @Autowired
    private UserRelationManager userRelationManager;

    @Autowired
    private PublicEmployeeService publicEmployeeService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Override
    public Result<Void> checkAppAccessible(String ea, long outerTenantId, long outerUid) {
        // 获取互联用户详情
        int ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(ei);
        GetDownstreamEmployeeInfoArg getDownstreamEmployeeInfoArg = new GetDownstreamEmployeeInfoArg();
        getDownstreamEmployeeInfoArg.setUpstreamTenantId(ei);
        getDownstreamEmployeeInfoArg.setOuterUid(outerUid);
        RestResult<GetDownstreamEmployeeInfoResult> publicEmployeeInfoResult = publicEmployeeService.getDownstreamEmployeeInfo(headerObj, getDownstreamEmployeeInfoArg);
        if (!publicEmployeeInfoResult.isSuccess() || publicEmployeeInfoResult.getData() == null) {
            log.error("getDownstreamEmployeeInfo failed, ea: {}, outerUid: {}, outerTenantId: {} result: {}", ea, outerUid, outerTenantId, publicEmployeeInfoResult);
            return Result.newError(SHErrorCode.PARTNER_NOT_EXIST);
        }
        //获取该互联用户的使用成员身份
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByOutTenantIdAndOuterUid(ea, Lists.newArrayList(outerTenantId), Lists.newArrayList(outerUid));
        if (CollectionUtils.isNotEmpty(userRelationEntityList)) {
            UserRelationEntity existUserRelationEntity = userRelationEntityList.get(0);
            // 如果该互联用户已经关联了上游的纷享员工，那么不允许访问伙伴营销
            if (UserRelationTypeEnum.EMPLOYEE.getCode().equals(existUserRelationEntity.getType())) {
                GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
                enterpriseDataArg.setEnterpriseAccount(ea);
                enterpriseDataArg.setEnterpriseId(ei);
                GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
                if (null == enterpriseDataResult || enterpriseDataResult.getEnterpriseData() == null) {
                    return new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND);
                }
                String enterpriseName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
                return new Result<>(SHErrorCode.MOBILE_BOUND_UPSTREAM_EA.getErrorCode(), I18nUtil.get(I18nKeyEnum.MOBILE_BOUND_UPSTREAM_EA) + enterpriseName);
            }
            // 如果使用成员的类型不是员工，那么只能是伙伴和会员，由于互联伙伴的优先级比推广会员要高，那么到了这里肯定就是伙伴了
            return Result.newSuccess();
        }
        String mobile = publicEmployeeInfoResult.getData().getMobile();
        if (StringUtils.isNotBlank(mobile)) {
            // 通过该互联用户的号码查询伙伴的使用成员
            List<UserRelationEntity> mobileUserRelationEntityList = userRelationManager.getPartnerRelationByMobileList(ea, Lists.newArrayList(mobile));
            if (CollectionUtils.isNotEmpty(mobileUserRelationEntityList)) {
                // 如果该互联用户的号码已经已经被其他互联企业的互联用户绑定到使用成员了，禁止访问伙伴营销
                long boundOuterTenantId = mobileUserRelationEntityList.get(0).getOuterTenantId();
                ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.ENTERPRISE_RELATION_OBJ.getName(), String.valueOf(boundOuterTenantId));
                String enterpriseName = objectData == null ? "" : objectData.getString("name");
                return new Result<>(SHErrorCode.MOBILE_BOUND_UPSTREAM_EA.getErrorCode(), I18nUtil.get(I18nKeyEnum.MOBILE_BOUND_OTHER_PUBLIC_ENTERPRISE) + enterpriseName);
            }
        }
        log.error("checkAppAccessible do not have user relation, ea: {}, outerUid: {}, outerTenantId: {}", ea, outerUid, outerTenantId);
        return Result.newError(SHErrorCode.PARTNER_NOT_EXIST_USER_RELATION);
    }
}
