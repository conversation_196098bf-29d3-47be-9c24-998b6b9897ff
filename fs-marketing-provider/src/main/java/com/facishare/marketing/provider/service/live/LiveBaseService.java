package com.facishare.marketing.provider.service.live;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.HexagonPageArg;
import com.facishare.marketing.api.arg.PhotoCutOffset;
import com.facishare.marketing.api.arg.hexagon.MarketingCopyArg;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.live.CreateMarketingLiveResult;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.vo.live.CreateLiveVO;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonTemplateTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.remote.IntegralServiceManager;
import com.facishare.marketing.provider.remote.rest.ShortUrlManager;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.otherrestapi.integral.constant.CategoryApiNameConstant;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/12/15
 **/
@Slf4j
public abstract class LiveBaseService {

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;

    @Autowired
    private HexagonManager hexagonManager;

    @Autowired
    private HexagonSiteManager hexagonSiteManager;

    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;

    @Autowired
    private IntegralServiceManager integralServiceManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private PhotoManager photoManager;

    @Autowired
    private ShortUrlManager shortUrlManager;

    @Autowired
    private HexagonService hexagonService;

    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private MetadataActionService metadataActionService;

    @Autowired
    private EIEAConverter eieaConverter;

    private Gson gson = new Gson();
    @Value("${vhall.h5.view.url}")
    private String liveUserH5Url;
    @Value("${polyv.h5.view.url}")
    private String polyvLiveUserH5Url;
    @Value("${vhall.h5.lecture.view.url}")
    private String liveLectureUrl;
    @Value("${live.default.hexagon.id}")
    private String defaultHexagonIds;

    @ReloadableProperty("channels_transit_url")
    private String channelStransitUrl;
    @ReloadableProperty("channels_transit_hexagon_id")
    private String transitId;
    @ReloadableProperty("default_conference_cover")
    private String defaultLiveCover;
    @Value("${host}")
    private String host;

    /**
     * 创建直播
     * @param vo
     * @return
     */
    abstract Result<CreateMarketingLiveResult> createLive(CreateLiveVO vo);

    /**
     * 更新直播
     * @param vo
     * @return
     */
    abstract Result<CreateMarketingLiveResult> updateLive(CreateLiveVO vo);

    /**
     * 创建市场活动对象
     * @param vo
     * @return
     */
    protected com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventObj(CreateLiveVO vo) {
        Map<String, Object> params = vo.getCreateObjectDataModel().getObjectData();
        List<String> createByList = Lists.newArrayList();
        createByList.add(String.valueOf(vo.getFsUserId()));

        params.put("begin_time", vo.getStartTime());
        params.put("end_time", vo.getEndTime());
        params.put("created_by", createByList);
        params.put("event_type", MarketingEventEnum.LIVE_MARKETING.getEventType());
        params.put("name", vo.getTitle());
        params.put("status", 1);
        if(!Strings.isNullOrEmpty(vo.getCoverTaPath())){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String npath = fileV2Manager.getNpathByApath(vo.getCoverTaPath(), vo.getEa());
                if (StringUtils.isNotBlank(npath)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", npath);
                    headImage.add(fileMap);
                    params.put("cover", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception ,ea={},apath={}",vo.getEa(),vo.getCoverTaPath(),e);
            }
        }
        params.put(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName(), vo.getParentId());

        ObjectData data = ObjectData.convert(params);
        data.setOwner(vo.getFsUserId());
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = null;
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(vo.getCorpId(), vo.getFsUserId()),  CrmObjectApiNameEnum.MARKETING_EVENT.getName(), false, arg);
        } catch (Exception e) {
            log.warn("live createMarketingEventObj failed CrmManager.createData Exception apiName:{}, e:{}", CrmObjectApiNameEnum.MARKETING_EVENT.getName(), e);
            return null;
        }
    }

    /**
     * 复制微页面
     * @param vo
     * @param marketingEventId
     * @param apath
     * @return
     */
    protected String copyHexagon(CreateLiveVO vo, String marketingEventId, String apath){
        String ea = vo.getEa();
        MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(vo.getCorpId(), marketingEventId);
        String[] middleIds = StringUtils.split(defaultHexagonIds, ",");
        String[] ids = Arrays.copyOf(middleIds,2);//defaultHexagonIds中的第三个id只有视频号直播再用，其他直播只用到前两个就行
        String hexagonFromId = null;
        if (ids.length > 0) {
            int i = 0;
            // 若有【市场活动设置】中的模板，则处理“报名预约”微页面站点
            HexagonTemplateSiteEntity marketingTemplate = null;
            if (Strings.isNullOrEmpty(vo.getMarketingTemplateId())) {
                marketingTemplate = hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(ea, HexagonTemplateTypeEnum.LIVE.getType());
            }else{
                marketingTemplate = hexagonTemplateSiteDAO.getById(vo.getMarketingTemplateId());
            }
            if (marketingTemplate != null) {
                String firstSideId = UUIDUtil.getUUID();
                HexagonPageEntity templateFirstPage = hexagonPageDAO.getHomePage(ids[0]);
                HexagonPageArg templateFirstPageArg = BeanUtil.copy(templateFirstPage, HexagonPageArg.class);
                // 拷贝“报名预约”的第一页
                String firstPageId = hexagonService.copyPage(templateFirstPageArg, ea, firstSideId, vo.getFsUserId()).getData();
                HexagonPageEntity firstPage = hexagonPageDAO.getById(firstPageId);
                firstPage = hexagonManager.updateLiveHexagonParam(firstPage, liveEntity, ea, firstSideId, vo.getTitle(), vo.getDesc(), apath, new Date(vo.getStartTime()), false);
                // 拷贝设置中的其他页
                List<HexagonTemplatePageEntity> templatePages = hexagonTemplatePageDAO.getBySiteId(marketingTemplate.getId());
                HexagonSiteListDTO formInfo = hexagonTemplateSiteDAO.getFormByTemplateSiteId(marketingTemplate.getId());
                Map<String, String> buttonInsideAction = new HashMap<>();
                int size = templatePages.size();
                for (int j = 0; j < size; j++) {
                    HexagonTemplatePageEntity templatePage = templatePages.get(j);
                    Map<String, Object> copyResult = hexagonSiteManager.copyPageFromTemplate(ea, templatePage, firstSideId, formInfo, null, buttonInsideAction);
                    // 将首页按钮链接过来
                    if (j == 0) {
                        String firstPageContent = firstPage.getContent();
                        firstPageContent = firstPageContent.replace("{SignupPageId}", (String) copyResult.get("newPageId"));
                        hexagonPageDAO.updateContent(firstPageId, firstPageContent);
                    }
                    if (copyResult != null && copyResult.get("newFormId") != null && copyResult.get("newPageId") != null){
                        String formId = copyResult.get("newFormId") == null ? null : copyResult.get("newFormId").toString();
                        String hexagonPageId = copyResult.get("newPageId") == null ? null : copyResult.get("newPageId").toString();
                        customizeFormDataManager.bindCustomizeFormDataObject(formId , hexagonPageId, ObjectTypeEnum.HEXAGON_PAGE.getType(), vo.getEa(), vo.getFsUserId(), null, null, null);
                    }
                }
                // 页面拷贝完成，生成站点
                HexagonSiteEntity saveSiteData = new HexagonSiteEntity();
                saveSiteData.setId(firstSideId);
                saveSiteData.setEa(ea);
                saveSiteData.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVEBASESERVICE_249));
                saveSiteData.setStatus(HexagonStatusEnum.NORMAL.getType());
                saveSiteData.setCreateBy(vo.getFsUserId());
                saveSiteData.setUpdateBy(vo.getFsUserId());
                saveSiteData.setCreateTime(new Date());
                saveSiteData.setUpdateTime(new Date());
                // saveSiteData.setSystemSite(true);
                int result = hexagonSiteDAO.insert(saveSiteData);
                if (result == 1) {
                    integralServiceManager.asyncRegisterMaterial(ea, CategoryApiNameConstant.MICRO_PAGE, firstSideId, liveEntity.getTitle() + "-" + saveSiteData.getName());
                }
                hexagonService.bindMarketing(ea, vo.getFsUserId(), ObjectTypeEnum.HEXAGON_SITE.getType(), marketingTemplate.getId(), firstSideId, marketingEventId);
                hexagonSiteDAO.updateHexagonSiteSystemStatus(ea, firstSideId, true);
                hexagonFromId = firstSideId;
                contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(hexagonFromId, ObjectTypeEnum.HEXAGON_SITE.getType(), liveEntity.getMarketingEventId(), true);
                // 第二个站点照常
                i = 1;
            }
            for (; i < ids.length; i++) {
                String hexagonId = ids[i];
                MarketingCopyArg marketingCopyArg = new MarketingCopyArg();
                marketingCopyArg.setId(hexagonId);
                marketingCopyArg.setName(vo.getTitle());
                marketingCopyArg.setMarketingEventId(marketingEventId);
                marketingCopyArg.setRegisterIntegralMaterial(false);
                boolean submitHexagonSite = false;
                if (i == 0) {
                    //第一个微页面为报名表单
                    marketingCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVEBASESERVICE_249));
                    submitHexagonSite = true;
                }else {
                    marketingCopyArg.setName(I18nUtil.get(I18nKeyEnum.MARK_LIVE_LIVEBASESERVICE_280));
                }
                Result<CreateSiteResult> copySiteResult = hexagonService.marketingCopySite(ea, vo.getFsUserId(), marketingCopyArg, false);
                if (copySiteResult == null || !copySiteResult.isSuccess()) {
                    log.info("copyHexagon failed as create live arg:{}", marketingCopyArg);
                } else {
                    hexagonManager.updateCopiedLiveHexagon(liveEntity, ea, copySiteResult.getData().getId(),vo.getTitle(), vo.getDesc(), apath, new Date(vo.getStartTime()), submitHexagonSite);
                    redisManager.deleteHexgonSite(copySiteResult.getData().getId());
                    List<String> hexagonPageIds = hexagonPageDAO.getPageIdsBySiteId(copySiteResult.getData().getId());
                    integralServiceManager.asyncRegisterMaterial(ea,CategoryApiNameConstant.MICRO_PAGE, copySiteResult.getData().getId(), liveEntity.getTitle() + "-" + marketingCopyArg.getName());
                    if (CollectionUtils.isNotEmpty(hexagonPageIds)) {
                        hexagonPageIds.forEach(pageId -> redisManager.deleteHexgonPage(pageId));
                    }
                }

                if (submitHexagonSite){
                    contentMarketingEventMaterialRelationDAO.updateIsApplyObjectByObjectId(hexagonFromId, ObjectTypeEnum.HEXAGON_SITE.getType(), liveEntity.getMarketingEventId(), true);
                }
            }
        }

        if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getOriginalImageAPath()) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(vo.getCutOffsetList())){
            HexagonPageEntity homePage = hexagonPageDAO.getHomePage(hexagonFromId);
            if(homePage!=null){
                for (PhotoCutOffset cutOffset : vo.getCutOffsetList()){
                    if(cutOffset.getPhotoTargetType()== PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER.getType()){
                        photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_MINIAPP_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                    }
                    if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER.getType()){
                        photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_H5_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                    }
                    if(cutOffset.getPhotoTargetType()==PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER.getType()){
                        photoManager.addOrUpdatePhotoByCutOffset(ea,PhotoTargetTypeEnum.HEXAGON_PAGE_SHARE_ORDINARY_COVER, homePage.getId(), cutOffset, StringUtils.isBlank(cutOffset.getPath()) ? vo.getOriginalImageAPath() : cutOffset.getPath());
                    }
                }
            }
        }

        return hexagonFromId;
    }

    protected com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> updateLiveToCrmMarketingEvent(String ea, Integer fsUserId, String marketingEventId, CreateLiveVO vo){
        ActionEditArg actionEditArg = new ActionEditArg();
        Map<String, Object> objectMap = vo.getCreateObjectDataModel().getObjectData();
        objectMap.put("object_describe_api_name", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        objectMap.put("object_describe_id", CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        objectMap.put(CrmV2MarketingEventFieldEnum.ID.getFieldName(), marketingEventId);
        objectMap.put(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), vo.getTitle());
        int tenantId = eieaConverter.enterpriseAccountToId(ea);
        objectMap.put("tenant_id", tenantId);
        if(!Strings.isNullOrEmpty(vo.getCoverTaPath())){
            try {
                List<Map<String, Object>> headImage = new ArrayList<>(1);
                String npath = fileV2Manager.getNpathByApath(vo.getCoverTaPath(), vo.getEa());
                if (StringUtils.isNotBlank(npath)) {
                    Map<String, Object> fileMap = new HashMap<>();
                    fileMap.put("ext", "jpg");
                    fileMap.put("path", npath);
                    headImage.add(fileMap);
                    objectMap.put("cover", headImage);
                }
            } catch (Exception e) {
                log.warn("upload head exception ,ea={},apath={}",ea,vo.getCoverTaPath(),e);
            }
        }
        objectMap.put(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName(), vo.getStartTime());
        objectMap.put(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName(), vo.getEndTime());

        actionEditArg.setObjectData(ObjectData.convert(objectMap));
        HeaderObj systemHeader = new HeaderObj(tenantId, fsUserId);
        return metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), true, true, actionEditArg);
    }

    protected MarketingLiveEntity buildMarketingLiveEntity4Create(CreateLiveVO vo, String marketingEventId, String cover){
        MarketingLiveEntity entity = new MarketingLiveEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setCorpId(vo.getCorpId());
        entity.setMarketingEventId(marketingEventId);
        entity.setTitle(vo.getTitle());
        entity.setDescription(vo.getDesc());
        entity.setStartTime(new Date(vo.getStartTime()));
        if (vo.getEndTime() != null) {
            entity.setEndTime(new Date(vo.getEndTime()));
        }
        entity.setUpdateTime(new Date(vo.getEndTime()));
        entity.setCreateUserId(vo.getFsUserId());
        entity.setLectureName(vo.getLectureUserName());
        entity.setPlatform(vo.getLivePlatform());
        entity.setCover(cover);
        entity.setViewUrl(vo.getOtherPlatformLiveUrl());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setStatus(LiveStatusEnum.NOT_START.getStatus());
        entity.setOtherPlatformUrl(vo.getOtherPlatformLiveUrl());
        entity.setXiaoetongLiveId(vo.getXiaoetongLiveId());

        if (CollectionUtils.isNotEmpty(vo.getTagNames())){
            entity.setTags(gson.toJson(vo.getTagNames()));
        }
        entity.setShowAcitivityList(vo.isShowActivityList());
        return entity;
    }

    protected MarketingLiveEntity buildMarketingLiveEntity4Update(CreateLiveVO arg, MarketingLiveEntity oldEntity, String otherPlatformCover){
        MarketingLiveEntity entity = BeanUtil.copy(oldEntity, MarketingLiveEntity.class);
        entity.setTitle(arg.getTitle());
        entity.setDescription(arg.getDesc());
        entity.setStartTime(new Date(arg.getStartTime()));
        if (arg.getEndTime() != null){
            entity.setEndTime(new Date(arg.getEndTime()));
        }else {
            entity.setEndTime(oldEntity.getEndTime());
        }
        entity.setCreateUserId(arg.getFsUserId());
        entity.setLectureName(arg.getLectureUserName());
        if (CollectionUtils.isNotEmpty(arg.getTagNames())) {
            entity.setTags(gson.toJson(arg.getTagNames()));
        }
        entity.setPlatform(arg.getLivePlatform());
        entity.setOtherPlatformUrl(arg.getOtherPlatformLiveUrl());
        if (oldEntity.getXiaoetongLiveId() == null) {
            entity.setXiaoetongLiveId(arg.getXiaoetongLiveId());
        }
        if (StringUtils.isEmpty(entity.getViewUrl())) {
            String viewUrl = polyvLiveUserH5Url + "?ea=" + arg.getEa() + "&mkId=" + entity.getId()+ "&liveId=" + arg.getXiaoetongLiveId();
            entity.setViewUrl(viewUrl);
            CreateShortUrlArg shortUrlArg = new CreateShortUrlArg();
            shortUrlArg.setUrl(viewUrl);
            Optional<String> shortUrlResult = shortUrlManager.createShortUrl(shortUrlArg);
            if (shortUrlResult.isPresent()) {
                entity.setShortViewUrl(shortUrlResult.get());
            }
        }

        if (otherPlatformCover != null){
            entity.setCover(otherPlatformCover);
        }
        entity.setShowAcitivityList(arg.isShowActivityList());
        entity.setUpdateTime(new Date());
        return entity;
    }
}