package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.AccountEntity;
import com.facishare.marketing.provider.entity.UserEntity;
import java.util.List;

import org.apache.ibatis.annotations.*;

public interface AccountDAO {

    @Select("<script>  "
        + " SELECT * FROM account where phone =#{phone} AND  account_type = #{accountType}"
        + "         <if test=\"appId != null\">\n"
        + "             AND app_id = #{appId}\n"
        + "          </if>\n"
        + "</script>")
    List<AccountEntity> queryAccountByPhone(@Param("phone") String phone, @Param("accountType") Integer accountType, @Param("appId") String appId);


    @Select(" SELECT A.* FROM account AS A JOIN fs_bind AS B ON A.uid = B.uid where A.phone = #{phone} AND A.account_type = 1 AND B.fs_ea = #{ea} AND A.app_id = #{appId} ")
    List<AccountEntity> queryQyWxAccountByPhone(@Param("phone") String phone, @Param("ea") String ea, @Param("appId") String appId);

    @Select("SELECT phone FROM account where uid=#{uid}")
    String getPhoneByUid(@Param("uid") String uid);

    @Select("<script>  "
            + " SELECT * FROM account where uid =#{uid} "
            + "</script>")
    AccountEntity queryAccountByUid(@Param("uid") String uid);

    @Insert("INSERT INTO account"
        + "(uid, openid, phone, create_time, last_modify_time, account_type, app_id)"
        + " VALUES"
        + " (#{obj.uid}, #{obj.openid}, #{obj.phone}, now(), now(), #{obj.accountType}, #{obj.appId})")
    int insert(@Param("obj") AccountEntity accountEntity);

    @Update("<script>"
            + "UPDATE account"
            + "    <set>"
            + "         <if test=\"phone != null\">\n"
            + "             phone = #{phone},\n"
            + "          </if>\n"
            + "         <if test=\"openid != null\">\n"
            + "             openid = #{openid},\n"
            + "          </if>\n"
            + "         last_modify_time = now()\n"
            + "    </set>"
            + "    WHERE uid = #{uid}"
            + "</script>")
    int update(AccountEntity accountEntity);

    @Delete("DELETE FROM account WHERE uid=#{uid}")
    int deleteByUid(@Param("uid")String uid);

    @Update("UPDATE account SET uid = #{newUid}, last_modify_time = now() WHERE uid = #{oldUid}")
    int updateUidByOldUid(@Param("oldUid") String oldUid, @Param("newUid")String newUid);

    @Update("UPDATE account SET account_type = #{newAccountType}, last_modify_time = now() WHERE uid = #{uid} and account_type = #{oldAccountType}")
    int updateAccountType(@Param("uid") String uid, @Param("oldAccountType") Integer oldAccountType, @Param("newAccountType") Integer newAccountType);
}
