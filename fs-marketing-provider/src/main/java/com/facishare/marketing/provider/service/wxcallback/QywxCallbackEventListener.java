package com.facishare.marketing.provider.service.wxcallback;

import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.UserMarketingActionStatisticUpgradeRecordDao;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxIdUpgradingCallbackTempDataDAO;
import com.facishare.marketing.provider.entity.UserMarketingActionStatisticUpgradeRecord;
import com.facishare.marketing.provider.entity.qywx.QywxIdUpgradingCallbackTempDataEntity;
import com.facishare.marketing.provider.innerData.qywx.*;
import com.facishare.marketing.provider.manager.QywxDepartmentManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.DeadEvent;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class QywxCallbackEventListener {

    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxEventCallBackManager qywxEventCallBackManager;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @Autowired
    private QywxUserManager qywxUserManager;

    // 这是之前营销动态升级的表，现在已经不用了，这次升级企微的ID，复用这张表吧，反正也就跑几次
    @Autowired
    private UserMarketingActionStatisticUpgradeRecordDao userMarketingActionStatisticUpgradeRecordDao;

    @Autowired
    private QywxIdUpgradingCallbackTempDataDAO qywxIdUpgradingCallbackTempDataDAO;

    @Autowired
    private QywxDepartmentManager qywxDepartmentManager;

    /**
     * 推送suite_ticket
     *
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handler(SuiteTicketEventMsg eventMsg) {
        log.info("推送suite_ticket: {}", eventMsg);
        int type = eventMsg.getEventType();
        if (type == ExternalUserMagCallBackBufferRequestData.AGENT_APP || type == ExternalUserMagCallBackBufferRequestData.OLD_AGNENT_APP) {
            qywxManager.saveOrUpdateSuitTicket(eventMsg.getSuiteId(), eventMsg.getSuiteTicket());
        }
    }

    /**
     * 授权相关事件
     *
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handler(AuthEventMsg eventMsg) {
        log.info("授权相关事件: {}", eventMsg);
        int type = eventMsg.getEventType();
        if (type == ExternalUserMagCallBackBufferRequestData.AGENT_APP) {
            if (eventMsg.isAgentCreateAuthEvent()) {
                // 授权事件
                qywxManager.qywxAuthorize(eventMsg.getMsgSignature(), eventMsg.getTimestamp(), eventMsg.getNonce(), eventMsg.getEncryptXmlBody(), eventMsg.getAuthCode(), eventMsg.getSuiteId());
            } else if (eventMsg.isAgentCancelAuthEvent()) {
                qywxManager.qywxCancelAuthEvent(eventMsg.getMsgSignature(), eventMsg.getTimestamp(), eventMsg.getNonce(), eventMsg.getEncryptXmlBody(), eventMsg.getSuiteId(), eventMsg.getAuthCorpId());
            }
        } else if (type == ExternalUserMagCallBackBufferRequestData.OLD_AGNENT_APP) {
            //客户授权事件
            if (eventMsg.isAgentCreateAuthEvent()) {
                //临时授权码换去永久授权码
                qywxManager.savePermanentCode(eventMsg.getAuthCode(), eventMsg.getSuiteId());
            } else if (eventMsg.isAgentCancelAuthEvent()) {
                //删除token
                redisManager.deleteQywxAccessToken(eventMsg.getAuthCorpId(), eventMsg.getSuiteId());
                redisManager.delQywxSuitAccessToken(eventMsg.getSuiteId());
                qywxManager.delAuthInfo(eventMsg.getAuthCorpId(), eventMsg.getSuiteId());

            }
        }
    }

    /**
     * 客户相关事件
     *
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handler(ChangeExternalContactEventMsg eventMsg) {
        log.info("客户相关事件: {}", eventMsg);
        int type = eventMsg.getEventType();
        String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(type, eventMsg.getToUserName(), eventMsg.getSuitId());
        if (isSaveToLocalTable(ea)) {
            saveEventToLocalTable(ea, QywxIdUpgradingCallbackTempDataEntity.EXTERNAL_CONTACT_DATA_TYPE, JsonUtil.toJson(eventMsg));
            log.info("企微客户回调事件写入到临时表中，ea: {}", ea);
            return;
        }
        qywxEventCallBackManager.handleExternalUserEventCallback(ea, eventMsg);
    }

    /**
     * 客户群相关事件
     *
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handler(ChangeExternalChatEventMsg eventMsg) {
        log.info("客户群相关事件: {}", eventMsg);
        int type = eventMsg.getEventType();
        String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(type, eventMsg.getToUserName(), eventMsg.getSuitId());
        if (isSaveToLocalTable(ea)) {
            saveEventToLocalTable(ea, QywxIdUpgradingCallbackTempDataEntity.WECHAT_GROUP_DATA_TYPE, JsonUtil.toJson(eventMsg));
            log.info("企微群回调事件写入到临时表中，ea: {}", ea);
            return;
        }
        qywxEventCallBackManager.postPrcoessWeChatGroupEvent(ea, eventMsg);
    }

    /**
     * 客户标签相关事件
     *
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handler(ChangeExternalTagEventMsg eventMsg) {
        log.info("客户标签相关事件: {}", eventMsg);
        // todo...
    }

    /**
     * 通讯录相关事件
     *
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handler(ChangeContactEventMsg eventMsg) {
        log.info("通讯录相关事件: {}", eventMsg);
        int type = eventMsg.getEventType();
        String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(type, eventMsg.getToUserName(), eventMsg.getSuitId());
        if (isSaveToLocalTable(ea)) {
            saveEventToLocalTable(ea, QywxIdUpgradingCallbackTempDataEntity.WECHAT_ADDRESS_BOOK_DATA_TYPE, JsonUtil.toJson(eventMsg));
            log.info("企微通讯录回调事件写入到临时表中，ea: {}", ea);
            return;
        }
        // 处理部门变更
        qywxDepartmentManager.handleDepartmentChange(ea, eventMsg);
        // 发送mq
        qywxEventCallBackManager.sendContactChangeToMq(ea, eventMsg);
        // 存入对象
        qywxEmployeeManager.handleContactChangeEvent(ea, eventMsg);
    }

    /**
     * 关注和取消关注事件
     * @param eventMsg
     */
    @Subscribe
    @AllowConcurrentEvents
    public void handle(SubscribeEventMsg eventMsg) {
        log.info("可见范围相关事件: {}", eventMsg);
        int type = eventMsg.getEventType();
        String ea = qywxEventCallBackManager.getEaBySuitIdAndCorpId(type, eventMsg.getToUserName(), eventMsg.getSuitId());
        if (isSaveToLocalTable(ea)) {
            saveEventToLocalTable(ea, QywxIdUpgradingCallbackTempDataEntity.WECHAT_VISIBLE_DATA_TYPE, JsonUtil.toJson(eventMsg));
            log.info("企微可见范围回调事件写入到临时表中，ea: {}", ea);
            return;
        }
        qywxEmployeeManager.handleSubscribeEvent(ea, eventMsg);
    }

    @Subscribe
    public void handle(DeadEvent event) {
        log.warn("事件无法处理：{}", event);
    }

    private boolean isSaveToLocalTable(String ea) {
        if (StringUtils.isBlank(ea)) {
            return false;
        }
        if (qywxManager.isNewInstallAgentApp(ea)) {
            return false;
        }
        UserMarketingActionStatisticUpgradeRecord record = userMarketingActionStatisticUpgradeRecordDao.getByEa(ea);
        if (record == null) {
            return false;
        }
        // 只有升级成功的才不需要保存
        return record.getStatus() != QywxUpgradeIdManager.UPGRADE_SUCCESS_STATUS;
    }

    private void saveEventToLocalTable(String ea, String dataType, String data) {
        QywxIdUpgradingCallbackTempDataEntity entity = new QywxIdUpgradingCallbackTempDataEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setStatus(QywxIdUpgradingCallbackTempDataEntity.UN_CONSUMED_STATUS);
        entity.setDataType(dataType);
        entity.setData(data);
        qywxIdUpgradingCallbackTempDataDAO.insert(entity);
    }

}
