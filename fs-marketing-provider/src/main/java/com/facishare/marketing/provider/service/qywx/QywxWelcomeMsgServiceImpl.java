package com.facishare.marketing.provider.service.qywx;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.qywx.QywxWelcomeMsgResult;
import com.facishare.marketing.api.result.qywx.customerGroup.DepartmentResult;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeOwnerResult;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeTagResult;
import com.facishare.marketing.api.service.qywx.QywxWelcomeMsgService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.api.vo.qywx.*;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qywx.*;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.QywxAttachmentsRelationDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.qywx.QywxWelcomeMsgQueryParam;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxWelcomeMsgDAO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.dto.qywx.QywxWelcomeMsgDTO;
import com.facishare.marketing.provider.entity.ObjectGroupEntity;
import com.facishare.marketing.provider.entity.ObjectGroupRelationEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.entity.QywxAttachmentsRelationEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxWelcomeMsgEntity;
import com.facishare.marketing.provider.innerResult.qywx.Department;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentListResult;
import com.facishare.marketing.provider.innerResult.qywx.StaffDetailResult;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.ObjectGroupManager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.BatchGetDepartmentDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/7 14:20
 */
@Service("qywxWelcomeMsgService")
@Slf4j
public class QywxWelcomeMsgServiceImpl implements QywxWelcomeMsgService {

    @Autowired
    private QywxWelcomeMsgDAO qywxWelcomeMsgDAO;

    @Autowired
    private QywxManager qywxManager;

    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private EmployeeProviderService employeeProviderService;
    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private DepartmentProviderService departmentProviderService;

    @Autowired
    private QywxAttachmentsRelationDAO qywxAttachmentsRelationDAO;

    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private PhotoManager photoManager;
    private Gson gson = new Gson();
    @Autowired
    private FileV2Manager fileV2Manager;

    @Override
    public Result<Void> saveWelcomeMsg(QywxWelcomeMsgVO vo) {
        QywxWelcomeMsgEntity qywxWelcomeEntity = BeanUtil.copy(vo,QywxWelcomeMsgEntity.class);
        qywxWelcomeEntity.setId(UUIDUtil.getUUID());
        qywxWelcomeEntity.setCreateTime(DateUtil.now());
        qywxWelcomeEntity.setUpdateTime(DateUtil.now());
        //如果选择部门,则通过部门查询下面所有的员工列表
        Set<String> userIds = new HashSet<>();
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(vo.getEa());
        if (!isOpen) {
            //处理员工标签问题
            if (CollectionUtils.isNotEmpty(vo.getUserIdList())) {
                qywxWelcomeEntity.setUserIdList(GsonUtil.getGson().toJson(vo.getUserIdList()));
                userIds.addAll(vo.getUserIdList());
            }
            if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
                List<String> employeeIds = qywxManager.batchGetEmployeeByTags(vo.getEa(), vo.getTagIds());
                userIds.addAll(employeeIds);
            }
            if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
                List<Integer> qywxCircleIds = new ArrayList<>();
                List<Integer> departmentIds = vo.getDepartmentIds();
                for (int i = 0; i < departmentIds.size(); i++) {
                    qywxCircleIds.add(i, departmentIds.get(i) - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                }
                List<String> qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(vo.getEa(), qywxCircleIds);
                //将两个集合数据去重合并
                userIds.addAll(qywxUserIds);
            }
        } else {
            if (CollectionUtils.isNotEmpty(vo.getUserIdList())) {
                qywxWelcomeEntity.setUserIdList(GsonUtil.getGson().toJson(vo.getUserIdList()));
            }
            List<Integer> deptIds = dataPermissionManager.filterUserAccessibleQywxDeptIds(vo.getEa(), vo.getOperator(), vo.getDepartmentIds());
            userIds.addAll(qywxManager.handleQywxEmployeeUserId(vo.getEa(), vo.getUserIdList(), deptIds, null));
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return Result.newError(SHErrorCode.NO_DATA_OWN_ORGANIZATION);
        }
        qywxWelcomeEntity.setUserIdMerge(GsonUtil.getGson().toJson(userIds));
        if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
            qywxWelcomeEntity.setDepartmentId(GsonUtil.getGson().toJson(vo.getDepartmentIds()));
        }
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            qywxWelcomeEntity.setTagId(GsonUtil.getGson().toJson(vo.getTagIds()));
        }
        qywxWelcomeEntity.setStatus(QywxWelComeMsgStatusEnum.NORMAL.getStatus());


        //企微群发支持多附件
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            for (QywxAttachmentsVO attachmentvo : vo.getQywxAttachmentsVO()) {
                //处理小程序内容
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.MINIPROGRAM.getType() && attachmentvo.getMiniprogram() != null&& attachmentvo.getMiniprogram().getMiniProgramType()==MiniProgramTypeEnum.CONTENT.getType()) {
                    int objectType = objectManager.convertNoticeContentTypeToObjectType(attachmentvo.getMiniprogram().getMaterialType());
                    attachmentvo.getMiniprogram().setObjectType(objectType);
                    if (Strings.isNullOrEmpty(attachmentvo.getMiniprogram().getPicPath())) {
                        List<PhotoEntity> photoEntities = null;
                        String hexagonCover = null;
                        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), attachmentvo.getMiniprogram().getMaterialId());

                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> siteIds = Lists.newArrayList();
                            siteIds.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> ids = Lists.newArrayList();
                            ids.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(ids);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        }
                        if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            attachmentvo.getMiniprogram().setPicPath(hexagonCover);
                        } else {
                            if (CollectionUtils.isNotEmpty(photoEntities)) {
                                attachmentvo.getMiniprogram().setPicPath(photoEntities.get(0).getPath());
                            }
                        }
                    }
                }
            }
        }
        QywxAttachmentsRelationEntity qywxAttachmentsRelationEntity = new QywxAttachmentsRelationEntity();
        qywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
        qywxAttachmentsRelationEntity.setEa(qywxWelcomeEntity.getEa());
        qywxAttachmentsRelationEntity.setTargetId(qywxWelcomeEntity.getId());
        qywxAttachmentsRelationEntity.setTargetType(QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            qywxAttachmentsRelationEntity.setAttachments(gson.toJson(vo.getQywxAttachmentsVO()));
        }
        qywxAttachmentsRelationDAO.insert(qywxAttachmentsRelationEntity);

        qywxWelcomeMsgDAO.saveWelcomeMsg(qywxWelcomeEntity);
        objectGroupManager.setGroup(vo.getEa(), vo.getOperator(), ObjectTypeEnum.QY_WELCOME_MSG.getType(), Collections.singletonList(qywxWelcomeEntity.getId()), vo.getGroupId());
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<QywxWelcomeMsgResult>> queryMsgList(QueryMsgListVO vo) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getEa()),I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_147));
        Preconditions.checkArgument(vo.getPageSize() != null,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_148));
        Preconditions.checkArgument(vo.getPageNum() != null,I18nUtil.get(I18nKeyEnum.MARK_MARKETINGPLUGIN_COUPONTEMPLATESERVICEIMPL_149));
        PageResult<QywxWelcomeMsgResult> pageResult = new PageResult<>();
        List<QywxWelcomeMsgResult> qywxWelcomeMsgResults = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        if (StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }
        QywxWelcomeMsgQueryParam queryParam = new QywxWelcomeMsgQueryParam();
        queryParam.setEa(vo.getEa());
        List<QywxWelcomeMsgDTO> qywxWelcomeMsgDTOS;
        if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(vo.getEa(), vo.getUserId(), ObjectTypeEnum.QY_WELCOME_MSG.getType());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            queryParam.setPermissionGroupIdList(permissionGroupIdList);
            queryParam.setUserId(vo.getUserId());
            qywxWelcomeMsgDTOS = qywxWelcomeMsgDAO.getAccessiblePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            queryParam.setUserId(vo.getUserId());
            qywxWelcomeMsgDTOS = qywxWelcomeMsgDAO.getCreateByMePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            qywxWelcomeMsgDTOS = qywxWelcomeMsgDAO.noGroupPage(queryParam, page);
        } else {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(vo.getEa(), vo.getUserId(), ObjectTypeEnum.QY_WELCOME_MSG.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            if (!permissionGroupIdSet.contains(vo.getGroupId())){
                qywxWelcomeMsgDTOS = Lists.newArrayList();
            } else {
                queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                queryParam.setUserId(vo.getUserId());
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(vo.getEa(), ObjectTypeEnum.QY_WELCOME_MSG.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                qywxWelcomeMsgDTOS = qywxWelcomeMsgDAO.getAccessiblePage(queryParam, page);
            }
        }

        if (CollectionUtils.isEmpty(qywxWelcomeMsgDTOS)) {
            pageResult.setResult(qywxWelcomeMsgResults);
            return Result.newSuccess(pageResult);
        }
        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(vo.getEa());
        if (agentConfig == null) {
            return Result.newError(SHErrorCode.QYWX_CONTACT_ME_FAILED);
        }
        List<String> msgIds = qywxWelcomeMsgDTOS.stream().map(QywxWelcomeMsgDTO::getId).collect(Collectors.toList());
        List<QywxAttachmentsRelationEntity> attachmentsRelationEntities = qywxAttachmentsRelationDAO.getDetailByTargetIds(msgIds, QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
        Map<String, QywxAttachmentsRelationEntity> relationEntityMap = attachmentsRelationEntities.stream().collect(Collectors.toMap(QywxAttachmentsRelationEntity::getTargetId, Function.identity(), (k1, k2) -> k1));
        qywxWelcomeMsgDTOS.stream().forEach(qywxWelcomeMsgEntity -> {
            QywxWelcomeMsgResult qywxWelcomeMsgResult = BeanUtil.copy(qywxWelcomeMsgEntity,QywxWelcomeMsgResult.class);
            if (StringUtils.isNotBlank(qywxWelcomeMsgEntity.getUserIdList())) {
                List<String> userId = GsonUtil.getGson().fromJson(qywxWelcomeMsgEntity.getUserIdList(), new TypeToken<List<String>>() {
                }.getType());
                if (CollectionUtils.isNotEmpty(userId)){
                    List<EmployeeOwnerResult> employeeResult = Lists.newArrayList();
                    String accessToken = qywxManager.getAccessToken(vo.getEa());
                    for (String userid : userId) {
                        EmployeeOwnerResult employee = new EmployeeOwnerResult();
                        employee.setEmployeeUserId(userid);
                        StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(vo.getEa(), userid, accessToken, true);
                        if (staffDetailResult != null) {
                            employee.setEmployeeName(staffDetailResult.getName());
                        }
                        employeeResult.add(employee);
                    }

                    qywxWelcomeMsgResult.setEmployee(employeeResult);
                }
            }
            //获取部门
            List<DepartmentResult> departmentResultList = Lists.newArrayList();
            List<Integer> departmentId = GsonUtil.getGson().fromJson(qywxWelcomeMsgEntity.getDepartmentId(), new TypeToken<List<Integer>>(){}.getType());
            String accessToken = qywxManager.getAccessToken(vo.getEa());
            if (CollectionUtils.isNotEmpty(departmentId)) {
                DepartmentListResult departmentListResult = qywxManager.queryDepartment(accessToken);
                Map<Integer, Department> departmentMap = departmentListResult.getDepartmentList().stream().collect(Collectors.toMap(Department::getId, a -> a, (k1, k2) -> k1));
                for (Integer dptId : departmentId) {
                    Integer circleId = dptId - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID;
                    DepartmentResult departmentResult = new DepartmentResult();
                    if (departmentMap.containsKey(circleId)) {
                        departmentResult.setId(dptId);
                        departmentResult.setName(departmentMap.get(circleId).getName());
                        departmentResultList.add(departmentResult);
                    }
                }
            }
            qywxWelcomeMsgResult.setDepartmentResults(departmentResultList);
            //获取员工标签
            List<EmployeeTagResult> employeeTagResults = Lists.newArrayList();
            List<Integer> tagIds = GsonUtil.getGson().fromJson(qywxWelcomeMsgEntity.getTagId(), new TypeToken<List<Integer>>(){}.getType());
            if (CollectionUtils.isNotEmpty(tagIds)) {
                List<EmployeeTagResult> tagResultList = qywxManager.queryEmployeeInfo(accessToken);
                Map<Integer, EmployeeTagResult> tagResultMap = tagResultList.stream().collect(Collectors.toMap(EmployeeTagResult::getTagId, Function.identity(), (k1, k2) -> k2));
                tagIds.forEach(tagId -> {
                    if (tagResultMap.containsKey(tagId)) {
                        EmployeeTagResult employeeTagResult = tagResultMap.get(tagId);
                        employeeTagResults.add(employeeTagResult);
                    }
                });
            }
            qywxWelcomeMsgResult.setEmployeeTagResults(employeeTagResults);
            //旧数据
            if(Objects.isNull(relationEntityMap.get(qywxWelcomeMsgEntity.getId()))){
                if(qywxWelcomeMsgEntity.getContentType()!=0){
                    QywxAttachmentsVO qywxAttachmentsVO = new QywxAttachmentsVO();
                    //小程序
                    if(qywxWelcomeMsgEntity.getContentType()==QywxGroupSendMsgTypeEnum.MINIPROGRAM.getType()){
                        qywxAttachmentsVO.setAttachmentType(QywxAttachmentTypeEnum.MINIPROGRAM.getType());
                        QywxAttachmentsVO.Miniprogram miniprogram = new QywxAttachmentsVO.Miniprogram();
                        miniprogram.setTitle(qywxWelcomeMsgEntity.getMiniTitle());
                        miniprogram.setPicPath(qywxWelcomeMsgEntity.getMiniPicPath());
                        miniprogram.setPicUrl(qywxWelcomeMsgEntity.getMiniPicUrl());
                        miniprogram.setAppId(qywxWelcomeMsgEntity.getMiniAppId());
                        miniprogram.setMiniProgramType(qywxWelcomeMsgEntity.getMiniProgramType());
                        miniprogram.setPage(qywxWelcomeMsgEntity.getMiniPage());
                        qywxAttachmentsVO.setMiniprogram(miniprogram);
                        if(StringUtils.isNotBlank(qywxWelcomeMsgEntity.getMiniPage())){
                            Pattern pattern = Pattern.compile("objectType=(\\d+)");
                            Matcher matcher = pattern.matcher(qywxWelcomeMsgEntity.getMiniPage());
                            if (matcher.find()) {
                                miniprogram.setObjectType(Integer.valueOf(matcher.group(1)));
                                miniprogram.setMaterialType(ObjectTypeEnum.getByType(Integer.valueOf(matcher.group(1))).toNoticeContentType());
                            }
                            Pattern pattern2 = Pattern.compile("objectId=([a-f\\d]{32})");
                            Matcher matcher2 = pattern2.matcher(qywxWelcomeMsgEntity.getMiniPage());
                            if (matcher2.find()) {
                                miniprogram.setMaterialId(matcher2.group(1));
                            }
                            if(miniprogram.getObjectType()!=null && StringUtils.isNotBlank(miniprogram.getMaterialId())){
                                miniprogram.setObjectName(objectManager.getObjectName(miniprogram.getMaterialId(),miniprogram.getObjectType()));
                            }
                        }
                        qywxWelcomeMsgResult.setQywxAttachmentsVO(Lists.newArrayList(qywxAttachmentsVO));
                    }
                    //h5
                    if(qywxWelcomeMsgEntity.getContentType()==QywxGroupSendMsgTypeEnum.LINK.getType()){
                        qywxAttachmentsVO.setAttachmentType(QywxAttachmentTypeEnum.H5.getType());
                        QywxAttachmentsVO.Link link = new QywxAttachmentsVO.Link();
                        link.setTitle(qywxWelcomeMsgEntity.getLinkTitle());
                        link.setPicPath(qywxWelcomeMsgEntity.getLinkCoverPath());
                        link.setPicUrl(qywxWelcomeMsgEntity.getLinkCover());
                        link.setUrl(qywxWelcomeMsgEntity.getLinkContentUrl());
                        qywxAttachmentsVO.setLink(link);
                        qywxWelcomeMsgResult.setQywxAttachmentsVO(Lists.newArrayList(qywxAttachmentsVO));
                    }
                }
            } else {
                qywxWelcomeMsgResult.setQywxAttachmentsVO(JSON.parseArray(relationEntityMap.get(qywxWelcomeMsgEntity.getId()).getAttachments(),QywxAttachmentsVO.class));
            }
            qywxWelcomeMsgResults.add(qywxWelcomeMsgResult);
        });

        //查询通讯录，返回创建人的姓名
        if (CollectionUtils.isNotEmpty(qywxWelcomeMsgResults)) {
            Set<Integer> userIdSet = qywxWelcomeMsgDTOS.stream().map(QywxWelcomeMsgEntity::getOperator).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
                batchGetEmployeeDtoArg.setEmployeeIds(userIdSet);
                batchGetEmployeeDtoArg.setRunStatus(RunStatus.ALL);
                batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(vo.getEa()));
                BatchGetEmployeeDtoResult employeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
                Map<Integer, String> userMap = null;
                if (CollectionUtils.isNotEmpty(employeeDtoResult.getEmployeeDtos())) {
                    userMap = employeeDtoResult.getEmployeeDtos().stream().collect(Collectors.toMap(EmployeeDto::getEmployeeId, EmployeeDto::getName, (v1, v2) -> v2));
                }
                if (CollectionUtils.isNotEmpty(qywxWelcomeMsgResults) && userMap != null) {
                    for (QywxWelcomeMsgResult msg : qywxWelcomeMsgResults) {
                        msg.setOperatorName(userMap.get(msg.getOperator()));
                    }
                }
            }
        }

        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(qywxWelcomeMsgResults);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<Void> updateWelcomeMsg(UpdateWelcomeMsgVO vo) {
        Preconditions.checkArgument(vo != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getId()), I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXWELCOMEMSGSERVICEIMPL_445));
        QywxWelcomeMsgEntity qywxWelcomeMsgEntity = qywxWelcomeMsgDAO.queryWelcomeMsgDetail(vo.getId());
        if (qywxWelcomeMsgEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }

        QywxWelcomeMsgEntity qywxMsgEntity = BeanUtil.copy(vo,QywxWelcomeMsgEntity.class);
        //如果选择部门,则通过部门查询下面所有的员工列表
        //处理员工标签问题
        Set<String> userIds = new HashSet<>();
        boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(vo.getEa());
        if (!isOpen) {
            if (CollectionUtils.isNotEmpty(vo.getUserIdList())) {
                qywxMsgEntity.setUserIdList(GsonUtil.getGson().toJson(vo.getUserIdList()));
                userIds.addAll(vo.getUserIdList());
            }
            if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
                List<String> employeeIds = qywxManager.batchGetEmployeeByTags(qywxWelcomeMsgEntity.getEa(), vo.getTagIds());
                userIds.addAll(employeeIds);
            }
            if (CollectionUtils.isNotEmpty(vo.getDepartmentIds())) {
                List<Integer> qywxCircleIds = new ArrayList<>();
                List<Integer> departmentIds = vo.getDepartmentIds();
                for (int i = 0; i < departmentIds.size(); i++) {
                    qywxCircleIds.add(i, departmentIds.get(i) - QywxUserConstants.BASE_QYWX_DEPARTMENT_ID);
                }
                List<String> qywxUserIds = qywxUserManager.getQywxUserIdByQywxDepartment(qywxWelcomeMsgEntity.getEa(), qywxCircleIds);
                //将两个集合数据去重合并
                userIds.addAll(qywxUserIds);
            }
        } else {
            if (CollectionUtils.isNotEmpty(vo.getUserIdList())) {
                qywxMsgEntity.setUserIdList(GsonUtil.getGson().toJson(vo.getUserIdList()));
            }
            List<Integer> deptIds = dataPermissionManager.filterUserAccessibleQywxDeptIds(vo.getEa(), vo.getOperator(), vo.getDepartmentIds());
            userIds.addAll(qywxManager.handleQywxEmployeeUserId(vo.getEa(), vo.getUserIdList(), deptIds, null));
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return Result.newError(SHErrorCode.NO_DATA_OWN_ORGANIZATION);
        }
        qywxMsgEntity.setUserIdMerge(GsonUtil.getGson().toJson(userIds));
        qywxMsgEntity.setDepartmentId(GsonUtil.getGson().toJson(vo.getDepartmentIds()));
        qywxMsgEntity.setTagId(GsonUtil.getGson().toJson(vo.getTagIds()));
        qywxMsgEntity.setUpdateTime(DateUtil.now());

        //企微群发支持多附件
        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
            for (QywxAttachmentsVO attachmentvo : vo.getQywxAttachmentsVO()) {
                //处理小程序内容
                if (attachmentvo.getAttachmentType()  == QywxAttachmentTypeEnum.MINIPROGRAM.getType() && attachmentvo.getMiniprogram() != null&& attachmentvo.getMiniprogram().getMiniProgramType()==MiniProgramTypeEnum.CONTENT.getType()) {
                    int objectType = objectManager.convertNoticeContentTypeToObjectType(attachmentvo.getMiniprogram().getMaterialType());
                    attachmentvo.getMiniprogram().setObjectType(objectType);
                    if (Strings.isNullOrEmpty(attachmentvo.getMiniprogram().getPicPath())) {
                        List<PhotoEntity> photoEntities = null;
                        String hexagonCover = null;
                        if (objectType == ObjectTypeEnum.ACTIVITY.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.ARTICLE.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), attachmentvo.getMiniprogram().getMaterialId());
                        } else if (objectType == ObjectTypeEnum.PRODUCT.getType()) {
                            photoEntities = photoManager.queryPhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), attachmentvo.getMiniprogram().getMaterialId());

                        } else if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            List<String> siteIds = Lists.newArrayList();
                            siteIds.add(attachmentvo.getMiniprogram().getMaterialId());
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(siteIds);
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                hexagonCover = hexagonSiteCoverListDTOList.get(0).getSharePicH5Apath();
                            }
                        }
                        if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                            attachmentvo.getMiniprogram().setPicPath(hexagonCover);
                        } else {
                            if (CollectionUtils.isNotEmpty(photoEntities)) {
                                attachmentvo.getMiniprogram().setPicPath(photoEntities.get(0).getPath());
                            }
                        }
                    }
                }
            }
        }
        QywxAttachmentsRelationEntity attachmentsRelationEntities = qywxAttachmentsRelationDAO.getDetailByTargetId(qywxMsgEntity.getId(), QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
        if(Objects.isNull(attachmentsRelationEntities)){
            QywxAttachmentsRelationEntity qywxAttachmentsRelationEntity = new QywxAttachmentsRelationEntity();
            qywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
            qywxAttachmentsRelationEntity.setEa(qywxMsgEntity.getEa());
            qywxAttachmentsRelationEntity.setTargetId(qywxMsgEntity.getId());
            qywxAttachmentsRelationEntity.setTargetType(QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
            if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
                qywxAttachmentsRelationEntity.setAttachments(gson.toJson(vo.getQywxAttachmentsVO()));
            }
            qywxAttachmentsRelationDAO.insert(qywxAttachmentsRelationEntity);
        }else {
            qywxAttachmentsRelationDAO.updateByTargetId(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())?JSON.toJSONString(vo.getQywxAttachmentsVO()):null,qywxMsgEntity.getId(),QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
        }

//        if(CollectionUtils.isNotEmpty(vo.getQywxAttachmentsVO())){
//            qywxAttachmentsRelationDAO.updateByTargetId(JSON.toJSONString(vo.getQywxAttachmentsVO()),qywxMsgEntity.getId(),QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
//        }else {
//            qywxAttachmentsRelationDAO.updateByTargetId(null,qywxMsgEntity.getId(),QywxAttachmentScenesTypeEnum.QYWX_WELCOME_MSG.getType());
//        }

        qywxWelcomeMsgDAO.updateQywxWelcomeMsgById(qywxMsgEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteWelcomeMsg(DeleteWelcomeMsgVO vo) {
        Preconditions.checkArgument(vo != null, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_BOARDSERVICEIMPL_580));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getId()), I18nUtil.get(I18nKeyEnum.MARK_QYWX_QYWXWELCOMEMSGSERVICEIMPL_445));
        QywxWelcomeMsgEntity qywxWelcomeMsgEntity = qywxWelcomeMsgDAO.queryWelcomeMsgDetail(vo.getId());
        if (qywxWelcomeMsgEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        qywxWelcomeMsgDAO.deleteWelcomeMsg(vo.getId());
        //删除分组数据
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(qywxWelcomeMsgEntity.getEa(), ObjectTypeEnum.QY_WELCOME_MSG.getType(), Lists.newArrayList(vo.getId()));
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType(), Lists.newArrayList(vo.getId()));
        return Result.newSuccess();
    }

    @Override
    public Result<EditObjectGroupResult> editQywxWelcomeMsgGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QywxWelcomeMsgServiceImpl.editQywxWelcomeMsgGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("QywxWelcomeMsgServiceImpl.editQywxWelcomeMsgGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.QY_WELCOME_MSG.getType());
    }

    @Override
    public Result<Void> deleteQywxWelcomeMsgGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QywxWelcomeMsgServiceImpl.deleteQywxWelcomeMsgGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.QY_WELCOME_MSG.getType());
    }

    @Override
    public Result<Void> setQywxWelcomeMsgGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("QywxWelcomeMsgServiceImpl.setQywxWelcomeMsgGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        List<QywxWelcomeMsgEntity> qywxWelcomeMsgEntityList = qywxWelcomeMsgDAO.getByIds(arg.getObjectIdList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(qywxWelcomeMsgEntityList)) {
            return Result.newError(SHErrorCode.QYWX_WELCOME_MSG_NOT_FOUND);
        }
        if (qywxWelcomeMsgEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_WELCOME_MSG_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.QY_WELCOME_MSG.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> topQywxWelcomeMsg(String ea, Integer fsUserId, TopMaterialArg arg) {
        QywxWelcomeMsgEntity entity = qywxWelcomeMsgDAO.getById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.QYWX_WELCOME_MSG_IS_DELETED);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.QY_WELCOME_MSG.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> cancelTopQywxWelcomeMsg(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.QY_WELCOME_MSG.getType(), arg.getObjectId());
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addQywxWelcomeMsgGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.QY_WELCOME_MSG.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listQywxWelcomeMsgGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.QY_WELCOME_MSG.getType(), null, null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(qywxWelcomeMsgDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(qywxWelcomeMsgDAO.queryAccessibleCount(ea, groupIdList, fsUserId));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(qywxWelcomeMsgDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(qywxWelcomeMsgDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<List<String>> getQywxWelcomeMsgGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }
}