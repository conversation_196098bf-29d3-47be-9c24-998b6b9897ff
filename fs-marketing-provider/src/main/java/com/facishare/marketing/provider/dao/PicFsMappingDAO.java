package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.PicFsMappingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface PicFsMappingDAO {
    @Insert("INSERT INTO pic_fs_mapping(id, raw_pic_url, fs_path, size, create_time) values(#{entity.id}, #{entity.rawPicUrl}, #{entity.fsPath}, #{entity.size}, now()) ON CONFLICT DO NOTHING")
    int insert(@Param("entity") PicFsMappingEntity entity);

    @Select("SELECT fs_path FROM pic_fs_mapping WHERE raw_pic_url=#{rawPicUrl}")
    String getFsPathByRawPicUrl(@Param("rawPicUrl")String rawPicUrl);
}
