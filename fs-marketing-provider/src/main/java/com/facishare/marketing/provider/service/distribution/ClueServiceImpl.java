package com.facishare.marketing.provider.service.distribution;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.arg.card.QueryCardInfoListArg;
import com.facishare.mankeep.api.outService.result.card.QueryCardInfoListResult;
import com.facishare.mankeep.api.outService.result.card.QueryCardInfoResult;
import com.facishare.mankeep.api.outService.service.OutCardService;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.arg.distribution.*;
import com.facishare.marketing.api.result.distribution.*;
import com.facishare.marketing.api.service.distribution.ClueService;
import com.facishare.marketing.api.util.ExcelUtil;
import com.facishare.marketing.api.vo.QueryDistributeClueVO;
import com.facishare.marketing.common.enums.ExcelConfigEnum;
import com.facishare.marketing.common.enums.LeadSaveStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.distribution.ClueNameMappingEnum;
import com.facishare.marketing.common.enums.distribution.ClueQueryResultStatusEnum;
import com.facishare.marketing.common.enums.distribution.ConfirmClueValidEnum;
import com.facishare.marketing.common.enums.distribution.DistributionClueStatusEnum;
import com.facishare.marketing.common.enums.leads.LeadsSendTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.DistributePlanDao;
import com.facishare.marketing.provider.dao.OperatorDao;
import com.facishare.marketing.provider.dao.SaveClueFailNoticeConfigDAO;
import com.facishare.marketing.provider.dao.distribution.ClueDAO;
import com.facishare.marketing.provider.dao.distribution.DistributorDao;
import com.facishare.marketing.provider.dao.distribution.DistributorFormSubmitDao;
import com.facishare.marketing.provider.dao.distribution.OperatorDistributorDAO;
import com.facishare.marketing.provider.dto.distribution.DistributorSubmitDTO;
import com.facishare.marketing.provider.dto.distribution.QueryOperatorByDistributorIdDTO;
import com.facishare.marketing.provider.entity.Operator;
import com.facishare.marketing.provider.entity.SaveClueFailNoticeConfigEntity;
import com.facishare.marketing.provider.entity.distribution.*;
import com.facishare.marketing.provider.manager.ObjectFieldMappingManager;
import com.facishare.marketing.provider.manager.PushSessionManager;
import com.facishare.marketing.provider.manager.SafetyManagementManager;
import com.facishare.marketing.provider.manager.SettingManager;
import com.facishare.marketing.provider.manager.distribution.ClueManager;
import com.facishare.marketing.provider.manager.distribution.DistributorManager;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created  By zhoux 2018/11/27
 **/
@Slf4j
@Service("clueService")
public class ClueServiceImpl implements ClueService {
    @Autowired
    private OperatorDao operatorDao;
    @Autowired
    private OperatorDistributorDAO operatorDistributorDAO;
    @Autowired
    private DistributorFormSubmitDao distributorFormSubmitDao;
    @Autowired
    private ClueDAO clueDAO;
    @Autowired
    private OutCardService outCardService;
    @Autowired
    private DistributePlanDao distributePlanDao;
    @Autowired
    private ObjectFieldMappingManager objectFieldMappingManager;
    @Autowired
    private ClueManager clueManager;
    @Autowired
    private SettingManager settingManager;
    @Autowired
    private DistributorDao distributorDao;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private SafetyManagementManager safetyManagementManager;
    @Autowired
    private SaveClueFailNoticeConfigDAO saveClueFailNoticeConfigDAO;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Autowired
    private DistributorManager distributorManager;

    @Override
    public Result<PageResult<PageClueResult>> clueData2Excel(PageClueArg arg) {
        PageResult<PageClueResult> pageResult = new PageResult();
        List<ClueEntity> clueEntityList = clueDAO.queryClueByEaAndOrderByCreateTime(arg.getFsEa());
        List<PageClueResult> clueResultList = BeanUtil.copy(clueEntityList, PageClueResult.class);
        clueResultList = this.buildClueResult(arg.getFsEa(), clueResultList, arg.getPlanId());
        pageResult.setResult(clueResultList);
        pageResult.setTotalCount(clueResultList.size());
        pageResult.setPageSize(clueResultList.size());
        pageResult.setPageNum(1);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<Void> grantedClueReward(String clueId, Float grantedReward, Float grantRecruitReward) {
        if (grantedReward == null && grantRecruitReward == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ClueEntity clueEntity = clueDAO.getClueById(clueId);
        if (clueEntity == null) {
            log.warn("ClueServiceImpl.grantClueReward clueEntity is null clueId:{}", clueId);
            return Result.newError(SHErrorCode.CLUE_NOT_EXIST_IN_DISTRIBUTE);
        }

        if (grantedReward != null && grantedReward > 0) {
            float value = clueEntity.getGranted() + grantedReward;
            if (Math.round(value * 1000d) / 1000d > Math.round((clueEntity.getReward() + clueEntity.getValidClueReward()) * 1000d) / 1000d) {
                log.info("ClueServiceImpl.grantClueReward granted total beyond reward id:{} grantedReward:{} total:{}", clueId, grantedReward, clueEntity.getGranted() + grantedReward);
                return Result.newError(SHErrorCode.TOTAL_GRANTED_BEYOND_REWARD);
            }

            int saveResult = clueDAO.updateGrantedReward(clueId, grantedReward);
            if (saveResult != 1) {
                log.warn("ClueServiceImpl.updateClueInfo updateGrantedReward error clueId:{} grantReward:{}", clueId, grantedReward);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
        }

        if (grantRecruitReward != null && grantRecruitReward > 0) {
            int saveResult = clueDAO.updateRecruitGrantedReward(clueId, grantRecruitReward);
            if (saveResult != 1) {
                log.warn("ClueServiceImpl.updateClueInfo updateRecruitGrantedReward error clueId:{} grantRecruitReward:{}", clueId, grantRecruitReward);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
        }

        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<QueryClueDetailResult> queryDetail(String clueId) {

        QueryClueDetailResult result = clueManager.queryDetail(clueId);
        if (result == null) {
            return Result.newError(SHErrorCode.CLUE_NOT_EXIST_IN_DISTRIBUTE);
        }

        Integer ei = eieaConverter.enterpriseAccountToId(result.getSubmitEa());
        if (ei == null) {
            log.info("queryEnterpriseInfo failed eieaConverter failed ea:{}", result.getSubmitEa());
            return Result.newError(SHErrorCode.QUERY_ENTERPRSE_INFO_FAILED);
        }

        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(result.getSubmitEa());
        enterpriseDataArg.setEnterpriseId(ei);
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        if (enterpriseDataResult != null && enterpriseDataResult.getEnterpriseData() != null) {
            result.setDisCompanyName(enterpriseDataResult.getEnterpriseData().getEnterpriseName());
        }

        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<QueryDistributeClueResult>> queryDistributeClue(QueryDistributeClueVO vo) {
        return clueManager.queryDistributeClue(vo);
    }

    @Override
    public Result<QueryIsSdrClueResult> queryIsSdrClue(String ea) {
        QueryIsSdrClueResult result = new QueryIsSdrClueResult();
        result.setResultType(0);
        if (clueManager.isSdrEa(ea)) {
            result.setResultType(1);
            result.setObjectApiName(CrmObjectApiNameEnum.DISTRIBUTION_CLUE.getName());
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<PageClueResult>> pageClue(PageClueArg arg) {
        PageResult<PageClueResult> pageResult = new PageResult();
        pageResult.setPageNum(arg.getPageNum());
        pageResult.setPageSize(arg.getPageSize());
        pageResult.setTime(arg.getTime());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(arg.getPageNum(), arg.getPageSize(), true);
        List<Integer> statusList =  ClueQueryResultStatusEnum.getCrmStatusListFromType(arg.getType());
        if (CollectionUtils.isEmpty(statusList)) {
            statusList = null;
        }
        String keyWord = null;
        if (StringUtils.isNotEmpty(arg.getKeyWord())) {
            keyWord = arg.getKeyWord();
        }

        //查询提交线索激励开关是否开启
        Result<CommitClueRewardStatusResult> commitClueRewardStatusResultResult =  settingManager.getCommitClueRewardStatus(arg.getPlanId());
        if (commitClueRewardStatusResultResult.isSuccess()){
            pageResult.setOtherData(commitClueRewardStatusResultResult.getData().getClueRewardStatus());
        }

        List<PageClueResult> clueResultList;
        List<ClueEntity> clueEntityList = null;

        if (arg.isAppAdmin()) {
            // 应用管理员查询全量数据
            clueEntityList = clueDAO.pageClueByPlanId(arg.getPlanId(), page, keyWord, statusList);
        } else if (arg.isOperator()) {
            // 查找运营人员
            Operator operatorEntity = operatorDao.getOperatorByFsUidAndPlanId(arg.getPlanId(), arg.getFsUid());
            if (operatorEntity == null) {
                log.warn("ClueServiceImpl.pageClue operatorEntity is null arg:{}", arg);
                return new Result<>(SHErrorCode.OPERATOR_ONT_FOUND);
            } else {
                // 查询运营人员下所有分销人员
                List<OperatorDistributorEntity> dataList = operatorDistributorDAO.queryByOperatorIdOrDistributorId(operatorEntity.getId(), null);
                if (CollectionUtils.isNotEmpty(dataList)) {
                    List<String> distributorIds = dataList.stream().map(OperatorDistributorEntity::getDistributorId).collect(Collectors.toList());
                    // 查询分销员线索
                    clueEntityList = clueDAO.pageClueByDistributorId(distributorIds, page, keyWord, statusList);
                }
            }
        } else {
            return new Result<>(SHErrorCode.USER_DOSE_NOT_HAVE_PERMISSION);
        }
        if (CollectionUtils.isNotEmpty(clueEntityList)) {
            clueResultList = BeanUtil.copy(clueEntityList, PageClueResult.class);
            for (int i = 0; i < clueResultList.size(); i++) {
                PageClueResult pageClueResult = clueResultList.get(i);
                float balance = pageClueResult.getReward() + pageClueResult.getValidClueReward() - pageClueResult.getGranted();
                if (balance < 0) {
                    balance = 0;
                }
                pageClueResult.setBalance((float) (Math.round(balance * 1000d) / 1000d));
                pageClueResult.setValidClueReward(pageClueResult.getValidClueReward());
            }
            clueResultList = this.buildClueResult(arg.getFsEa(), clueResultList, arg.getPlanId());
            pageResult.setResult(clueResultList);
            pageResult.setTotalCount(page.getTotalNum());
        } else {
            pageResult.setResult(Lists.newArrayList());
            pageResult.setTotalCount(0);
        }
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    private List<PageClueResult> buildClueResult(String ea, List<PageClueResult> clueResultList, String planId) {
        if (CollectionUtils.isEmpty(clueResultList)) {
            return clueResultList;
        }

        List<String> distributorUids = clueResultList.stream().map(PageClueResult :: getDistributorUid).collect(Collectors.toList());
        List<String> distributorRecruitIds = clueResultList.stream().map(PageClueResult :: getRecruitId).collect(Collectors.toList());
        List<String> distributorIds = clueResultList.stream().map(PageClueResult :: getDistributorId).distinct().collect(Collectors.toList());
        List<DistributorEntity> recruitEntities = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(distributorRecruitIds)) {
            recruitEntities = distributorDao.getDistributorByIds(distributorRecruitIds);
        }
        List<QueryOperatorByDistributorIdDTO> operatorDtos = operatorDistributorDAO.queryOperatorByDistributorIds(distributorIds, null);
        if (operatorDtos == null) {
            operatorDtos = Lists.newArrayList();
        }
        Map<String, QueryOperatorByDistributorIdDTO> operatorDtoMap = operatorDtos.stream().collect(Collectors.toMap(QueryOperatorByDistributorIdDTO :: getDistributorId, v -> v, (k1, k2) -> k1));

        Map<String, DistributorBaseInfoResult> distributorBaseInfoResultMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(distributorIds)) {
            distributorBaseInfoResultMap = distributorManager.getDistributorFormDataMap(planId, distributorIds, null);
        }
        for (DistributorEntity distributorEntity : recruitEntities) {
            distributorUids.add(distributorEntity.getUid());
        }
        Map<String, DistributorEntity> recruitMap = recruitEntities.stream().collect(Collectors.toMap(DistributorEntity :: getId, v -> v, (k1, k2) -> k1));
        // 是否开启手机脱敏
        boolean turnOnPhoneNumberSensitive = safetyManagementManager.turnOnPhoneNumberSensitive(ea);

        QueryCardInfoListArg cardInfoListArg = new QueryCardInfoListArg();
        cardInfoListArg.setUids(distributorUids);
        ModelResult<QueryCardInfoListResult> resultModelResult = outCardService.queryCardInfoList(cardInfoListArg);
        List<QueryCardInfoResult> cardInfoList = Lists.newArrayList();
        if (resultModelResult != null && resultModelResult.getData() != null){
            cardInfoList = resultModelResult.getData().getCardInfoResults();
        }
        final Map<String, QueryCardInfoResult> cardInfoResultMap = cardInfoList.stream().collect(Collectors.toMap(QueryCardInfoResult::getUid, a -> a,(k1,k2)->k1));
        List<String> cardUids = cardInfoList.stream().map(QueryCardInfoResult::getUid).collect(Collectors.toList());
        List<String> withoutCardUids = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(distributorUids)){
            if (CollectionUtils.isEmpty(cardUids)){
                withoutCardUids = distributorUids;
            }else {
                for (String uid : distributorUids){
                    if (!cardUids.contains(uid)){
                        withoutCardUids.add(uid);
                    }
                }
            }
        }

        Map<String, DistributorSubmitDTO> uidDistributorFormSubmitEntityMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(withoutCardUids)) {
            Optional<List<DistributorSubmitDTO>>  distributorFormSubmitEntityListOpt = getDistributorSubmitByUids (withoutCardUids);
            if (distributorFormSubmitEntityListOpt.isPresent()){
                distributorFormSubmitEntityListOpt.get().stream().map(distributorFormSubmitEntityDTO ->
                    uidDistributorFormSubmitEntityMap.put(distributorFormSubmitEntityDTO.getUid(), distributorFormSubmitEntityDTO));
            }
        }
        for (PageClueResult clueResult : clueResultList) {
            DistributorBaseInfoResult distributorBaseInfoResult = distributorBaseInfoResultMap.get(clueResult.getDistributorId());
            if (distributorBaseInfoResult != null) {
                clueResult.setDistributorName(distributorBaseInfoResult.getName());
            } else {
                QueryCardInfoResult queryCardInfoResult = cardInfoResultMap.get(clueResult.getDistributorUid());
                if (queryCardInfoResult != null) {
                    clueResult.setDistributorName(queryCardInfoResult != null ? queryCardInfoResult.getName() : null);
                }else {
                    DistributorSubmitDTO distributorSubmitDTO = uidDistributorFormSubmitEntityMap.get(clueResult.getDistributorUid());
                    if (distributorSubmitDTO != null){
                        clueResult.setDistributorName(distributorSubmitDTO != null ? distributorSubmitDTO.getSubmitContent().getName() : null);
                    }
                }
            }
            if (turnOnPhoneNumberSensitive) {
                clueResult.setPhone(safetyManagementManager.phoneNumberStrSensitive(clueResult.getPhone()));
            }
            // 设置状态
            clueResult.setStatus(ClueQueryResultStatusEnum.getStatusFromCrmStatus(clueResult.getStatus()));
            QueryOperatorByDistributorIdDTO operatorByDistributorIdDTO = operatorDtoMap.get(clueResult.getDistributorId());
            if (operatorByDistributorIdDTO != null) {
                clueResult.setRecruitName(operatorByDistributorIdDTO.getName());
            }
//            if (StringUtils.isNotEmpty(clueResult.getRecruitId())) {
//                DistributorEntity distributorEntity = recruitMap.get(clueResult.getRecruitId());
//                if (distributorEntity != null) {
//                    QueryCardInfoResult recruitCardInfoResult = cardInfoResultMap.get(distributorEntity.getUid());
//                    if (recruitCardInfoResult != null) {
//                        clueResult.setRecruitName(recruitCardInfoResult != null ? recruitCardInfoResult.getName() : null);
//                    }else {
//                        DistributorSubmitDTO distributorSubmitDTO = uidDistributorFormSubmitEntityMap.get(clueResult.getDistributorUid());
//                        if (distributorSubmitDTO != null) {
//                            clueResult.setRecruitName(distributorSubmitDTO != null ? distributorSubmitDTO.getSubmitContent().getName() : null);
//                        }
//                    }
//                }
//            }
        }
        return clueResultList;
    }

    private Optional<List<DistributorSubmitDTO>> getDistributorSubmitByUids(List<String> uids){
         if (CollectionUtils.isNotEmpty(uids)){
             return Optional.ofNullable(distributorFormSubmitDao.querySubmitListByUids(uids));
         }else {
             return Optional.empty();
         }
    }


    @Override
    public Result<Void> exportClue(PageClueArg arg) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            Result<PageResult<PageClueResult>> clubResultResult = pageClue(arg);
            String filename = I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_383) + ".xlsx";
            Map<ExcelConfigEnum, Object> excelConfigMap = new LinkedHashMap<>();
            excelConfigMap.put(ExcelConfigEnum.FILE_NAME, filename);
            excelConfigMap.put(ExcelConfigEnum.SHEET_NAME, I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_384));
            excelConfigMap.put(ExcelConfigEnum.CONTENT_TYPE, "application/octet-stream");
            XSSFWorkbook xssfWorkbook = ExcelUtil.generateXSSFExcel();
            XSSFSheet xssfSheet = xssfWorkbook.createSheet(excelConfigMap.get(ExcelConfigEnum.SHEET_NAME).toString());
            xssfSheet.setDefaultColumnWidth(20);
            List<String> titleList = generateExcelTitleList();
            List<List<Object>> distributorResultList = generateExcelDatasList(clubResultResult.getData().getResult());
            ExcelUtil.fillContent(xssfSheet, titleList, distributorResultList);
            pushSessionManager.pushExcelToFileAssistant(xssfWorkbook, filename, arg.getFsEa(), arg.getFsUid());
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    List<String> generateExcelTitleList() {
        List<String> titleList = new ArrayList<>();
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_CAMPAIGNMERGEDATASERVICEIMPL_275));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_401));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_402));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_USERMARKETINGACCOUNT_USERMARKETINGACCOUNTASSOCIATIONMANAGER_797));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_404));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_CUSTOMIZEFORMDATAMANAGER_1750));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_406));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_407));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_408));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_409));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_410));
        titleList.add(I18nUtil.get(I18nKeyEnum.MARK_DISTRIBUTION_CLUESERVICEIMPL_411));
        return titleList;
    }

    List<List<Object>> generateExcelDatasList(List clueResults) {
        List<PageClueResult> clueResultsForValue = BeanUtil.copy(clueResults, PageClueResult.class);
        List<List<Object>> datasList = new ArrayList<>();
        clueResultsForValue.forEach(value -> {
            List<Object> objList = new ArrayList<>();
            objList.add(value.getName());
            objList.add(value.getCompanyName());
            objList.add(value.getPhone());
            objList.add(value.getVocation());
            objList.add(value.getEmail());
            objList.add(value.getDetail());
            objList.add(value.getCreateTime());
            objList.add(value.getDistributorName());
            String statusName = "";
            if(null != value.getStatus()) {
                statusName = ClueQueryResultStatusEnum.getNameByType(value.getStatus());
                if(null == statusName){
                    statusName = "";
                }
            }
            objList.add(String.format("%.2f", value.getReward()));
            objList.add(String.format("%.2f", value.getRecruitReward()));
            objList.add(String.format("%.2f", value.getValidClueReward()));
            objList.add(statusName);
            objList.add(value.getInvalidDesc());
            datasList.add(objList);
        });
        return datasList;
    }

    @Override
    public Result updateClueRewardInfo(UpdateClueInfoArg arg) {
        ClueEntity clueEntity = clueDAO.getClueById(arg.getId());
        if (clueEntity == null) {
            log.warn("ClueServiceImpl.updateClueInfo clueEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (DistributionClueStatusEnum.isValid(clueEntity.getStatus()) || DistributionClueStatusEnum.isWinOrder(clueEntity.getStatus())) {
            Float reward = null;
            Float recruitReward = null;
            if (arg.getReward() != null && arg.getReward() >= 0) {
                reward = arg.getReward();
            }

            if (arg.getRecruitReward() != null && arg.getRecruitReward() >= 0) {
                recruitReward = arg.getRecruitReward();
            }

            int saveResult = clueDAO.updateClueManualRewardInfo(clueEntity.getId(), reward, recruitReward);
            if (saveResult != 1) {
                log.warn("ClueServiceImpl.updateClueInfo updateClueManualRewardInfo error arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            return new Result<>(SHErrorCode.SUCCESS);
        }
        return new Result<>(SHErrorCode.MANUAL_REWARD_SET_FAIL);
    }

    @Override
    public Result confirmClueValid(ConfirmClueValidArg arg) {
        ClueEntity clueEntity = clueDAO.getClueById(arg.getId());
        if (clueEntity == null) {
            log.warn("ClueServiceImpl.confirmClueValid clueEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        if (arg.getStatus() == ConfirmClueValidEnum.INVALID.getType()) {
            // 无效线索
            clueEntity.setStatus(DistributionClueStatusEnum.INVALID.getType());
            clueEntity.setInvalidDesc(arg.getInvalidDesc());
            int saveResult = clueDAO.updateClueInfo(clueEntity);
            if (saveResult != 1) {
                log.error("ClueServiceImpl.confirmClueValid saveResult error arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
        } else if (arg.getStatus() == ConfirmClueValidEnum.VALID.getType()) {
            // 有效线索
            // 查询分销计划id
            DistributorEntity distributorEntity = distributorDao.queryDistributorById(clueEntity.getDistributorId());
            if (distributorEntity == null) {
                log.error("ClueServiceImpl.confirmClueValid distributorEntity is null arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
            DistributePlanEntity distributePlanEntity = distributePlanDao.getDistributePlanByPlanId(distributorEntity.getPlanId());
            if (distributePlanEntity == null) {
                log.error("ClueServiceImpl.confirmClueValid distributePlanEntity is null arg:{}", arg);
                return new Result<>(SHErrorCode.OPERATOR_GET_DISTRIBUTE_PLAN_FAILED);
            }

            Integer operatorFsUserId = -10000;
            OperatorDistributorEntity operatorDistributorEntity = operatorDistributorDAO.getOperatorIdByDistributorId(clueEntity.getDistributorId());
            if (operatorDistributorEntity != null) {
                Operator operatorEntity = operatorDao.getById(operatorDistributorEntity.getOperatorId());
                operatorFsUserId = operatorEntity != null ? operatorEntity.getFsUserId() : null;
            }

            // 保存线索至CRM
            clueEntity.setLeadSaveStatus(LeadSaveStatusEnum.UN_SAVE.getStatus());
            clueEntity.setDuplicateData(false);
            objectFieldMappingManager
                .saveCrmLeadByLeadEntity(arg.getEa(), operatorFsUserId, distributePlanEntity.getCrmLeadPoolId(), ObjectFieldMappingManager.DISTRIBUTION_CLUE + distributePlanEntity.getId(), clueEntity,
                    false, null, null);
            if (clueEntity.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() && clueEntity.isDuplicateData()) {
                // 重复数据
                log.warn("ClueServiceImpl.confirmClueValid duplicate data  arg:{} clueEntity:{}", arg, clueEntity);
                return new Result<>(SHErrorCode.LEAD_HAS_EXIST_IN_CRM);
            }
            if (clueEntity.getLeadSaveStatus() == LeadSaveStatusEnum.SAVED_FAIL.getStatus() || clueEntity.getLeadSaveStatus() == LeadSaveStatusEnum.UN_SAVE.getStatus()) {
                log.warn("ClueServiceImpl.confirmClueValid save crm lead error arg:{} clueEntity:{}", arg, clueEntity);
                return new Result<>(SHErrorCode.SAVE_CRM_LEAD_ERROR);
            }
            clueEntity.setCrmClueId(clueEntity.getLeadId());
            clueEntity.setPoolId(distributePlanEntity.getCrmLeadPoolId());
            // 默认为跟进中
            clueEntity.setStatus(DistributionClueStatusEnum.VALID.getType());
            log.info("before save entity:{}", distributePlanEntity);
            clueEntity.setValidClueReward(distributePlanEntity.getValidClueReward());
            log.info("after save entity:{}", distributePlanEntity);
            int saveResult = clueDAO.updateClueInfo(clueEntity);
            if (saveResult != 1) {
                log.error("ClueServiceImpl.confirmClueValid saveResult error arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }
        }
        return new Result<>(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<ListAllClueConfigResult> listAllClueConfig(ListAllClueConfigArg listAllClueConfigArg) {
        try {
            ListAllClueConfigResult result = new ListAllClueConfigResult();
            result.setDataList(Lists.newArrayList());
            Arrays.stream(ClueNameMappingEnum.values()).forEach(data -> {
                ListAllClueConfigResult.ClueField clueField = new ListAllClueConfigResult.ClueField();
                clueField.setFieldCaption(data.getFieldCaption());
                clueField.setFieldName(data.getFieldName());
                result.getDataList().add(clueField);
            });
            return new Result<>(SHErrorCode.SUCCESS, result);
        } catch (Exception e) {
            log.error("ClueServiceImpl.confirmClueValid error", e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
    }

    @Override
    public Result<List<SaveClueFailNoticeConfigResult>> querySaveClueFailNoticeConfig(String ea, Integer clueType) {
        if (StringUtils.isEmpty(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm");
        List<SaveClueFailNoticeConfigResult> resultList = Lists.newArrayList();
        List<SaveClueFailNoticeConfigEntity> configEntities = saveClueFailNoticeConfigDAO.querySaveClueFailNoticeConfigByEa(ea);
        if (CollectionUtils.isEmpty(configEntities)) {
            return Result.newSuccess(resultList);
        }
        configEntities.forEach(entity -> {
            SaveClueFailNoticeConfigResult result = new SaveClueFailNoticeConfigResult();
            result.setClueType(entity.getClueType());
            result.setReceiverType(entity.getReceiverType());
            result.setSendScope(entity.getSendScope());
            result.setSendType(entity.getSendType());
            if (entity.getTimingTime() != null) {
                result.setTimingTime(dateFormat.format(entity.getTimingTime()));
            }
            resultList.add(result);
        });
        return Result.newSuccess(resultList);
    }





    @Override
    public Result<Void> addOrUpdateSaveClueFailNoticeConfig(AddOrUpdateSaveClueFailNoticeConfigArg arg) {
        if (arg == null || CollectionUtils.isEmpty(arg.getSaveClueFailNoticeConfigDataList())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        for (AddOrUpdateSaveClueFailNoticeConfigArg.SaveClueFailNoticeConfigData saveClueFailNoticeConfigData : arg.getSaveClueFailNoticeConfigDataList()) {
            if (saveClueFailNoticeConfigData.getSendType() != null && saveClueFailNoticeConfigData.getSendType() == 2) {
                if (StringUtils.isBlank(saveClueFailNoticeConfigData.getTimingTime())) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
            }
            if (saveClueFailNoticeConfigData.getReceiverType() != null && saveClueFailNoticeConfigData.getReceiverType() == 1) {
                if (StringUtils.isBlank(saveClueFailNoticeConfigData.getSendScope())) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
            }
        }
        List<AddOrUpdateSaveClueFailNoticeConfigArg.SaveClueFailNoticeConfigData> saveClueFailNoticeConfigDataList = arg.getSaveClueFailNoticeConfigDataList();
        List<SaveClueFailNoticeConfigEntity> configEntities = saveClueFailNoticeConfigDAO.querySaveClueFailNoticeConfigByEa(arg.getEa());
        List<SaveClueFailNoticeConfigEntity> configEntityList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(configEntities)) {
            for (AddOrUpdateSaveClueFailNoticeConfigArg.SaveClueFailNoticeConfigData data : saveClueFailNoticeConfigDataList) {
                SaveClueFailNoticeConfigEntity configEntity = new SaveClueFailNoticeConfigEntity();
                configEntity.setId(UUIDUtil.getUUID());
                configEntity.setEa(arg.getEa());
                configEntity.setClueType(data.getClueType());
                configEntity.setSendType(data.getSendType());
                configEntity.setTimingTime(DateUtil.hHmmConvert2DateTime(data.getTimingTime()));
                configEntity.setReceiverType(data.getReceiverType());
                configEntity.setSendScope(data.getSendScope());
                configEntityList.add(configEntity);
            }
            saveClueFailNoticeConfigDAO.batchInsertSaveClueFailNoticeConfig(configEntityList);
            return Result.newSuccess();
        }

        for (AddOrUpdateSaveClueFailNoticeConfigArg.SaveClueFailNoticeConfigData data : saveClueFailNoticeConfigDataList) {
            for (SaveClueFailNoticeConfigEntity entity : configEntities) {
                SaveClueFailNoticeConfigEntity configEntity = new SaveClueFailNoticeConfigEntity();
                if (!Objects.equals(entity.getClueType(), data.getClueType())) {
                    continue;
                }
                configEntity.setId(entity.getId());
                configEntity.setEa(arg.getEa());
                configEntity.setClueType(data.getClueType());
                configEntity.setSendType(data.getSendType());
                boolean isDelay = LeadsSendTypeEnum.DELAY.getType().equals(data.getSendType());
                configEntity.setTimingTime(isDelay ? DateUtil.hHmmConvert2DateTime(data.getTimingTime()) : null);
                configEntity.setReceiverType(data.getReceiverType());
                configEntity.setSendScope(data.getSendScope());
                saveClueFailNoticeConfigDAO.updateSaveClueFailNoticeConfig(configEntity);
            }
        }
        return Result.newSuccess();
    }
}