package com.facishare.marketing.provider.dao;

import com.facishare.marketing.api.result.MaterialTagDTO;
import com.facishare.marketing.provider.entity.MaterialRelationEntity;
import com.facishare.marketing.provider.entity.MaterialTagRelationEntity;
import com.facishare.marketing.provider.entity.ObjectGroupRelationEntity;
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;
import com.github.mybatis.pagination.Page;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/16
 **/
public interface MaterialTagRelationDao {

    @Insert("INSERT INTO material_tag_relation(id, ea, object_id, object_type, tag_id, create_time, update_time) " +
            "VALUES (#{obj.id}, #{obj.ea}, #{obj.objectId}, #{obj.objectType}, #{obj.tagId}, now(), now()) ON CONFLICT DO NOTHING ")
    int insert(@Param("obj") MaterialTagRelationEntity obj);

    @Insert("<script>" +
                "INSERT INTO material_tag_relation(id, ea, object_id, object_type, tag_id, create_time, update_time, object_create_time, object_status) VALUES "
                + "<foreach collection = 'objs' item = 'item' separator = ','>"
                +      "(#{item.id}, #{item.ea}, #{item.objectId}, #{item.objectType}, #{item.tagId}, now(), now(), #{item.objectCreateTime}, #{item.objectStatus})"
                + "</foreach>" +
                " ON CONFLICT DO NOTHING " +
            "</script>")
    int batchInsert(@Param("objs") List<MaterialTagRelationEntity> objs);

    @Delete("<script>"
            + "DELETE FROM material_tag_relation WHERE ea=#{ea} AND object_type=#{objectType} " +
            " AND object_id IN "
                + "<foreach collection = 'objectIds' item = 'item' open = '(' separator = ',' close = ')'>"
                +     "#{item}"
                + "</foreach>" +
            " AND tag_id in "
                + "<foreach collection = 'tagIds' item = 'item' open = '(' separator = ',' close = ')'>"
                +     "#{item}"
                + "</foreach>"
            + "</script>")
    int batchDelete(@Param("ea") String ea, @Param("objectType")Integer objectType, @Param("objectIds") List<String> objectIds, @Param("tagIds") List<String> tagIds);

    @Select("<script>" +
            " SELECT t.object_id as objectId, Array_agg(ut.name order by t.create_time desc) as tagNames " +
            " FROM material_tag_relation t join user_tag ut on ut.id = t.tag_id " +
            " WHERE t.object_type = #{objectType} AND t.object_id in " +
                "<foreach collection = 'objectIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>" +
                    "#{item}" +
                "</foreach>" +
            " group by t.object_id " +
            "</script>")
    List<MaterialTagDTO> queryMaterialRelation(@Param("objectIds") List<String> objectIds, @Param("objectType") Integer objectType);

    @Select("<script>" +
            " SELECT * from material_tag_relation WHERE ea = #{ea} AND tag_id in " +
            "<foreach collection = 'tagIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>" +
                "#{item}" +
            "</foreach>" +
            "</script>")
    List<MaterialTagRelationEntity> queryByTagIds(@Param("ea") String ea, @Param("tagIds") List<String> tagIds);

    @Update("<script>"
            + " UPDATE material_tag_relation SET object_status = 2 where ea = #{ea} and object_id = #{objectId} "
            + "</script>")
    void invalid(@Param("ea") String ea, @Param("objectId") String objectId);

    @Delete("<script>"
            + " delete from material_tag_relation where ea = #{ea} and object_id = #{objectId} "
            + "</script>")
    void deleteByObjectId(@Param("ea") String ea, @Param("objectId") String objectId);

    @Delete("<script>"
            + " delete from material_tag_relation where ea = #{ea} and tag_id = #{tagId} "
            + "</script>")
    void deleteByTagId(@Param("ea") String ea, @Param("tagId") String tagId);

    @Select("<script> " +
            " select * from (" +
                " SELECT mtr.object_id as objectId, max(mtr.object_create_time) as objCreateTime FROM material_tag_relation mtr " +
                " WHERE mtr.ea = #{ea} and mtr.object_type in " +
                    "<foreach collection = 'objectTypes' item = 'item' index='index' open = '(' separator = ',' close = ')'>" +
                        "#{objectTypes[${index}]}" +
                    "</foreach>" +
                " AND mtr.tag_id in " +
                    "<foreach collection = 'tagIds' item = 'item' index='index' open = '(' separator = ',' close = ')'>" +
                        "#{tagIds[${index}]}" +
                    "</foreach>" +
                " group by mtr.object_id " +
            " ) tt order by tt.objCreateTime desc " +
            "</script>")
    List<String> queryByAnyTags(@Param("ea") String ea, @Param("tagIds") List<String> tagIds, @Param("objectTypes") List<Integer> objectTypes, @Param("page") Page page);

    @Select("<script> " +
            " select * from (" +
                " SELECT mtr.object_id as objectId, max(mtr.object_create_time) as objCreateTime FROM material_tag_relation mtr " +
                " WHERE mtr.ea = #{ea} and mtr.object_type in " +
                    "<foreach collection = 'objectTypes' item = 'item' index='index' open = '(' separator = ',' close = ')'>" +
                        "#{objectTypes[${index}]}" +
                    "</foreach>" +
                " group by mtr.object_id having ARRAY_AGG(mtr.tag_id) <![CDATA[ @> ]]> " +
                " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='tagIds'> #{tagIds[${index}]} </foreach>::varchar[] " +
            " ) tt order by tt.objCreateTime desc " +
            "</script>")
    List<String> queryByAllTags(@Param("ea") String ea, @Param("tagIds") List<String> tagIds, @Param("objectTypes") List<Integer> objectTypes, @Param("page") Page page);

    @Select("<script> " +
            " select * from (" +
            " SELECT mtr.object_id as objectId, max(mtr.object_create_time) as objCreateTime FROM material_tag_relation mtr " +
            " WHERE mtr.ea = #{ea} and mtr.object_type in " +
            "<foreach collection = 'objectTypes' item = 'item' index='index' open = '(' separator = ',' close = ')'>" +
            "#{objectTypes[${index}]}" +
            "</foreach>" +
            " AND mtr.tag_id in " +
            "<foreach collection = 'tagIds' item = 'item' index='index' open = '(' separator = ',' close = ')'>" +
            "#{tagIds[${index}]}" +
            "</foreach>" +
            " group by mtr.object_id " +
            " ) tt order by tt.objCreateTime desc " +
            "</script>")
    List<String> queryListByAnyTags(@Param("ea") String ea, @Param("tagIds") List<String> tagIds, @Param("objectTypes") List<Integer> objectTypes);

    @Select("<script> " +
            " select * from (" +
            " SELECT mtr.object_id as objectId, max(mtr.object_create_time) as objCreateTime FROM material_tag_relation mtr " +
            " WHERE mtr.ea = #{ea} and mtr.object_type in " +
            "<foreach collection = 'objectTypes' item = 'item' index='index' open = '(' separator = ',' close = ')'>" +
            "#{objectTypes[${index}]}" +
            "</foreach>" +
            " group by mtr.object_id having ARRAY_AGG(mtr.tag_id) <![CDATA[ @> ]]> " +
            " ARRAY<foreach open='[' close=']' separator=',' item='item' index='index' collection='tagIds'> #{tagIds[${index}]} </foreach>::varchar[] " +
            " ) tt order by tt.objCreateTime desc " +
            "</script>")
    List<String> queryListByAllTags(@Param("ea") String ea, @Param("tagIds") List<String> tagIds, @Param("objectTypes") List<Integer> objectTypes);
}
