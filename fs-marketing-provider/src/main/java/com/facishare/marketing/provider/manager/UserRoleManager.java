/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.contstant.RoleConstant;
import com.facishare.marketing.common.enums.crm.CrmFuncCodeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.dao.MarketingWxServiceDao;
import com.facishare.marketing.provider.dao.UserRoleDao;
import com.facishare.marketing.provider.entity.UserRoleEntity;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.ContentPropagationDetailObjManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.EmployeePromoteDetailObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.open.app.center.api.result.BaseResult;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.privilege.api.FunctionPrivilegeRestService;
import com.facishare.privilege.api.RolePrivilegeRestService;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.function.BatchAddFunctionVo;
import com.facishare.privilege.api.module.function.IncrementUpdateFuncAccessByRoleCodeVo;
import com.facishare.privilege.api.module.role.AddRoleVo;
import com.facishare.privilege.api.module.role.BatchGetRoleByRoleCodesVo;
import com.facishare.privilege.api.module.role.QueryAllRoleInfoByRoleCodesVo;
import com.facishare.privilege.api.module.role.RoleVo;
import com.facishare.privilege.api.module.user.*;
import com.facishare.privilege.define.RoleType;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.ListObjectDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.paasauthrestapi.arg.BatchCreateEntityShareArg;
import com.fxiaoke.paasauthrestapi.common.data.PaasAuthContextData;
import com.fxiaoke.paasauthrestapi.common.data.ShareRuleData;
import com.fxiaoke.paasauthrestapi.service.PaasShareRuleService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.facishare.marketing.common.contstant.RoleConstant.SYSTEM_ADMIN_ROLE_ID;

@Component
@Slf4j
public class UserRoleManager {
    @Autowired
    private UserRoleDao userRoleDao;
    @ReloadableProperty("marketing_appid")
    private String appId;
    @Autowired
    private OpenAppAdminService openAppAdminService;
    @Autowired
    private FunctionPrivilegeRestService functionPrivilegeRestService;
    @Autowired
    private RolePrivilegeRestService rolePrivilegeRestService;
    @Autowired
    private UserPrivilegeRestService userPrivilegeRestService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CampaignMergeDataResetManager campaignMergeDataResetManager;
    @Autowired
    private PaasShareRuleService paasShareRuleService;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private MemberManager memberManager;
    @Autowired
    private MarketingWxServiceDao marketingWxServiceDao;

    @Autowired
    private ContentPropagationDetailObjManager contentPropagationDetailObjManager;

    @Autowired
    private EmployeePromoteDetailObjManager employeePromoteDetailObjManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private EmployeeProviderService employeeProviderService;
    private final static String OFFICIAL_ACCOUNT_ROLE_NAME = "公众号运营";
    public final static String MARKETING_MANAGER_ROLE_CODE = "00000000000000000000000000000031";  //角色：市场管理者
    //    public final static String CRM_MANAGER_ROLE_CODE = "00000000000000000000000000000006";  //角色：CRM管理员
    public final static String MARKETING_USER_ROLE_CODE = "00000000000000000000000000000032";     //角色: 市场人员
    public final static String SALE_USER_ROLE_CODE = "00000000000000000000000000000015";     //角色: 销售人员
    public final static String OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE = "official_account_marketing_role";   //角色：公众号运营
    private final static String QYWX_ACCOUNT_ROLE_NAME = "企微运营管理员";
    public final static String QYWX_ACCOUNT_MARKETING_ROLE_CODE = "qywx_account_marketing_role";   //角色：企微运营管理员

    private final static String AD_ACCOUNT_ROLE_NAME = "广告运营管理员";
    public final static String AD_ACCOUNT_MARKETING_ROLE_CODE = "ad_account_marketing_role";   //角色：广告运营管理员

    private final List<String> marketingRoleCodes = ImmutableList.of(MARKETING_MANAGER_ROLE_CODE, MARKETING_USER_ROLE_CODE, OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE, QYWX_ACCOUNT_MARKETING_ROLE_CODE, AD_ACCOUNT_MARKETING_ROLE_CODE);

    //默认预设的对象权限
    //(
    //查看详情,View
    //新建,Add
    //编辑,Edit
    //查看全部,ViewAll
    //编辑全部,EditAll
    //作废,Abolish
    //恢复,Recover
    //删除,Delete
    //导入,Import
    //导出,Export
    //更好负责人,ChangeOwner
    //编辑相关团队成员,EditTeamMember)
    private final List<String> DEFAULT_FUNCTION_PRIVILEGES = ImmutableList.of(CrmFuncCodeEnum.VIEW.getCode(), CrmFuncCodeEnum.ADD.getCode(), CrmFuncCodeEnum.EDIT.getCode(), CrmFuncCodeEnum.VIEW_ALL.getCode(), CrmFuncCodeEnum.EDIT_ALL.getCode(), CrmFuncCodeEnum.ABOLISH.getCode(), CrmFuncCodeEnum.RECOVER.getCode(), CrmFuncCodeEnum.DELETE.getCode(), CrmFuncCodeEnum.IMPORT.getCode(), CrmFuncCodeEnum.EXPORT.getCode(), CrmFuncCodeEnum.CHANGE_OWNER.getCode(), CrmFuncCodeEnum.EDIT_TEAM_MEMBER.getCode());

    public void initSystemAdminUserRoles(String ea) {
        Set<Integer> employeeIds = new HashSet<>();
        BaseResult<List<Integer>> br = openAppAdminService.getAppAdminIds(ea, appId);
        if (br.isSuccess() && br.getResult() != null && !br.getResult().isEmpty()) {
            employeeIds.addAll(br.getResult());
        } else {
            employeeIds.add(1000);
        }
        userRoleDao.batchInsertIgnoreByEmployeeIds(ea, SYSTEM_ADMIN_ROLE_ID, employeeIds);
        addToCrmManagerManagerUserRole(ea, Lists.newArrayList(employeeIds));
    }

    /**
     * 给角色分配权限
     *
     * @param ea
     * @param fsUserId
     * @param roleCode
     * @param addFuncCodes
     * @param delFuncCodes
     */
    public void configRoleObjectFunctionPrivilege(String ea, Integer fsUserId, String roleCode, List<String> addFuncCodes, List<String> delFuncCodes) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(fsUserId).build();

        IncrementUpdateFuncAccessByRoleCodeVo.Argument argument = new IncrementUpdateFuncAccessByRoleCodeVo.Argument();
        argument.setRoleCode(roleCode);
        argument.setAddFuncCodes(addFuncCodes);
        argument.setDelFuncCodes(delFuncCodes);
        IncrementUpdateFuncAccessByRoleCodeVo.Result result = functionPrivilegeRestService.asyncIncrementUpdateFuncAccessByRoleCode(crm, argument);
        log.info("UserRoleManager.configRoleObjectFunctionPrivilege ea:{} roleCode:{} addFuncCodes:{} delFuncCodes:{} result:{}", ea, roleCode, addFuncCodes, delFuncCodes, result);
    }

    /**
     * 将用户添加到市场管理员角色
     *
     * @param ea
     * @param fsUserIds
     */
    public void addToCrmManagerManagerUserRole(String ea, List<Integer> fsUserIds) {
        addMarketingUserToCrmUserByRole(ea, MARKETING_MANAGER_ROLE_CODE, fsUserIds);
    }

    /**
     * 将用户添加到市场人员员角色
     *
     * @param ea
     * @param fsUserIds
     */
    public void addToCrmManagerCommonUserRole(String ea, List<Integer> fsUserIds) {
        addMarketingUserToCrmUserByRole(ea, MARKETING_USER_ROLE_CODE, fsUserIds);
    }

    /**
     * 将用户添加到公众号运营角色
     *
     * @param ea
     * @param fsUserIds
     */
    public void addToCrmOfficialAccountRole(String ea, List<Integer> fsUserIds) {
        addMarketingUserToCrmUserByRole(ea, OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE, fsUserIds);
    }

    /**
     * 将用户添加到企微运营角色
     *
     * @param ea
     * @param fsUserIds
     */
    public void addToCrmQywxAccountRole(String ea, List<Integer> fsUserIds) {

        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();

        //预设企微运营角色，先查询这个企业是否有这个角色
        QueryAllRoleInfoByRoleCodesVo.Argument queryArgument = new QueryAllRoleInfoByRoleCodesVo.Argument();
        Set<String> roleCodes = new HashSet<>();
        roleCodes.add(QYWX_ACCOUNT_MARKETING_ROLE_CODE);
        queryArgument.setRoleCodes(roleCodes);
        List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, queryArgument);
        if (CollectionUtils.isEmpty(roleVoList)) {
            return;
        }
        addMarketingUserToCrmUserByRole(ea, QYWX_ACCOUNT_MARKETING_ROLE_CODE, fsUserIds);
    }

    /**
     * 将用户添加到广告运营角色
     *
     * @param ea
     * @param fsUserIds
     */
    public void addToCrmAdAccountRole(String ea, List<Integer> fsUserIds) {

        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();

        //预设广告运营角色，先查询这个企业是否有这个角色
        QueryAllRoleInfoByRoleCodesVo.Argument queryArgument = new QueryAllRoleInfoByRoleCodesVo.Argument();
        Set<String> roleCodes = new HashSet<>();
        roleCodes.add(AD_ACCOUNT_MARKETING_ROLE_CODE);
        queryArgument.setRoleCodes(roleCodes);
        List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, queryArgument);
        if (CollectionUtils.isEmpty(roleVoList)) {
            return;
        }
        addMarketingUserToCrmUserByRole(ea, AD_ACCOUNT_MARKETING_ROLE_CODE, fsUserIds);
    }

    /**
     * 更新员工分配的角色
     *
     * @param ea
     * @param currentMarketingRoles
     * @param fsUserId
     */
    public void updateMarketingUserRole(String ea, List<String> currentMarketingRoles, Integer fsUserId) {
        if (CollectionUtils.isEmpty(currentMarketingRoles)) {
            return;
        }

        //查询当前CRM角色
        List<UserRoleVo> userRoleVoList = queryMarketingUserRole(ea, fsUserId);
        if (userRoleVoList == null) {
            log.info("UserRoleManager.queryMarketingUserRole failed ea:{} fsUserId:{} userRoleVoList:{} ", ea, fsUserId, userRoleVoList);
            return;
        }
        Set<String> existCrmRoleCodeSet = userRoleVoList.stream().map(UserRoleVo::getRoleCode).collect(Collectors.toSet());
        log.info("userRoleVoList:{}", userRoleVoList);
        String majarRoleCode = null;
        List<String> batchAddRoleCodeList = new ArrayList<>();
        List<String> batchDelRoleCodeList = new ArrayList<>();
        Set<String> currentRoleSet = new HashSet<>(currentMarketingRoles);

        if (isContainSystemAdiminRoleId(currentRoleSet)) {
            //给当前员工添加市场管理员的角色
            if (!existCrmRoleCodeSet.contains(MARKETING_MANAGER_ROLE_CODE) && !batchAddRoleCodeList.contains(MARKETING_MANAGER_ROLE_CODE)) {
                batchAddRoleCodeList.add(MARKETING_MANAGER_ROLE_CODE);
            }
        } else {
            //添加角色中没有市场管理人员，需要删除当前员工CRM中市场管理员角色
            if (existCrmRoleCodeSet.contains(MARKETING_MANAGER_ROLE_CODE) && !batchDelRoleCodeList.contains(MARKETING_MANAGER_ROLE_CODE)) {
                batchDelRoleCodeList.add(MARKETING_MANAGER_ROLE_CODE);
            }
            //如果移除市场管理员角色，且用户当前没有公众号运营权限，则移除掉公众号运营角色
            if (!currentRoleSet.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)
                    && !batchDelRoleCodeList.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)
                    && existCrmRoleCodeSet.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)) {
                batchDelRoleCodeList.add(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
            }

        }

        if (isContainMarketingCommonUser(currentRoleSet)) {
            if (!existCrmRoleCodeSet.contains(MARKETING_USER_ROLE_CODE) && !batchAddRoleCodeList.contains(MARKETING_USER_ROLE_CODE)) {
                batchAddRoleCodeList.add(MARKETING_USER_ROLE_CODE);
            }
        } else {
            if (existCrmRoleCodeSet.contains(MARKETING_USER_ROLE_CODE) && !batchDelRoleCodeList.contains(MARKETING_USER_ROLE_CODE)) {
                batchDelRoleCodeList.add(MARKETING_USER_ROLE_CODE);
            }
        }

        if (isContainWechatOperation(currentRoleSet)) {
            if (!existCrmRoleCodeSet.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)) {
                batchAddRoleCodeList.add(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
            }
        } else {
            if (existCrmRoleCodeSet.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE) && !batchDelRoleCodeList.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)) {
                batchDelRoleCodeList.add(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
            }
        }

        //获取主角色
        if (userRoleVoList.size() == 0) {
            majarRoleCode = batchAddRoleCodeList.get(0);
        } else {
            for (UserRoleVo vo : userRoleVoList) {
                if (vo.getMajorRole()) {
                    majarRoleCode = vo.getRoleCode();
                }
                break;
            }
        }


        //可能暂时还没有公众号运营角色，就不用设置权限了
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        if (batchAddRoleCodeList.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)) {
            QueryAllRoleInfoByRoleCodesVo.Argument argument = new QueryAllRoleInfoByRoleCodesVo.Argument();
            argument.setRoleCodes(Sets.newHashSet(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE));
            List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, argument);
            log.info("queryAllRoleInfoByRoleCodes:{}", roleVoList);
            if (CollectionUtils.isEmpty(roleVoList)) {
                batchAddRoleCodeList.remove(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
            }
        }

        if (CollectionUtils.isNotEmpty(batchAddRoleCodeList)) {
            log.info("batchAddUserRole ea:{} fsUserId:{} batchAddRoleCodeList:{}", ea, fsUserId, batchAddRoleCodeList);
            BatchAddUserRoleVo.Argument batchAddArgument = new BatchAddUserRoleVo.Argument();
            batchAddArgument.setUserIds(Lists.newArrayList(String.valueOf(fsUserId)));
            batchAddArgument.setMajorRole(majarRoleCode);
            batchAddArgument.setRoleCodes(batchAddRoleCodeList);
            userPrivilegeRestService.batchAddUserRole(privilegeContext, batchAddArgument);
        }

        if (CollectionUtils.isNotEmpty(batchDelRoleCodeList)) {
            log.info("batchDelUserRole ea:{} fsUserId:{} batchDelRoleCodeList:{}", ea, fsUserId, batchDelRoleCodeList);
            for (String delRoleCode : batchDelRoleCodeList) {
                DeleteByUserIdsVo.Argument deleteArgument = new DeleteByUserIdsVo.Argument();
                deleteArgument.setRoleCode(delRoleCode);
                deleteArgument.setUserIds(Lists.newArrayList(String.valueOf(fsUserId)));
                log.info("batchDelUserRole delete privilegeContext:{} deleteArgument:{}", privilegeContext, deleteArgument);
                userPrivilegeRestService.deleteByUserIds(privilegeContext, deleteArgument);
            }
        }

        return;
    }

    /**
     * 移除员工的角色
     *
     * @param ea
     * @param fsUserId
     */
    public void removeMarketingRoleByUser(String ea, List<String> roleCodes, Integer fsUserId) {
        if (CollectionUtils.isEmpty(roleCodes)) {
            return;
        }

        //查询当前角色
        List<UserRoleVo> userRoleVoList = queryMarketingUserRole(ea, fsUserId);
        if (userRoleVoList == null) {
            log.info("UserRoleManager.queryMarketingUserRole failed ea:{} fsUserId:{} userRoleVoList:{} ", ea, fsUserId, userRoleVoList);
            return;
        }
        log.info("removeMarketingRoleByUser remove role:{} currentCrmUser:{}", roleCodes, userRoleVoList);
        List<String> allCrmRole = userRoleVoList.stream().map(UserRoleVo::getRoleCode).collect(Collectors.toList());

        //过滤掉CRM中没有的角色
        List<String> removeCrmRoles = roleCodes.stream().filter(roleCode -> allCrmRole.contains(roleCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(removeCrmRoles)) {
            return;
        }

        //如果移除市场管理员角色，且用户当前没有公众号,广告,企微运营权限，则移除掉公众号,广告,企微运营角色
        if (removeCrmRoles.contains(MARKETING_MANAGER_ROLE_CODE)) {
            List<String> currentRoleIds = userRoleDao.listByEmployeeId(ea, fsUserId);
            if (CollectionUtils.isNotEmpty(currentRoleIds) && !currentRoleIds.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE) && !removeCrmRoles.contains(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)) {
                removeCrmRoles.add(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
            }
            if (CollectionUtils.isNotEmpty(currentRoleIds) && !currentRoleIds.contains(QYWX_ACCOUNT_MARKETING_ROLE_CODE) && !removeCrmRoles.contains(QYWX_ACCOUNT_MARKETING_ROLE_CODE)) {
                removeCrmRoles.add(QYWX_ACCOUNT_MARKETING_ROLE_CODE);
            }
            if (CollectionUtils.isNotEmpty(currentRoleIds) && !currentRoleIds.contains(AD_ACCOUNT_MARKETING_ROLE_CODE) && !removeCrmRoles.contains(AD_ACCOUNT_MARKETING_ROLE_CODE)) {
                removeCrmRoles.add(AD_ACCOUNT_MARKETING_ROLE_CODE);
            }
        }

        //删除角色
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        for (String delRoleCode : removeCrmRoles) {
            DeleteByUserIdsVo.Argument deleteArgument = new DeleteByUserIdsVo.Argument();
            deleteArgument.setRoleCode(delRoleCode);
            deleteArgument.setUserIds(Lists.newArrayList(String.valueOf(fsUserId)));
            log.info("removeMarketingRoleByUser delete privilegeContext:{} deleteArgument:{}", privilegeContext, deleteArgument);
            userPrivilegeRestService.deleteByUserIds(privilegeContext, deleteArgument);
        }
    }

    public void addMarketingUserToCrmUserByRole(String ea, String roleCode, List<Integer> fsUserIds) {
        if (CollectionUtils.isEmpty(fsUserIds)) {
            return;
        }

        //查询员工已有的角色
        List<String> employeeIds = fsUserIds.stream().map(userId -> String.valueOf(userId)).collect(Collectors.toList());
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        GetRolesByUsersVo.Argument getRolesByUserVoArg = new GetRolesByUsersVo.Argument();
        getRolesByUserVoArg.setUserIds(employeeIds);
        List<UserRoleVo> userRoleVoList = userPrivilegeRestService.getRolesByUsers(privilegeContext, getRolesByUserVoArg);
        if (CollectionUtils.isEmpty(userRoleVoList)) {
            log.info("addMarketingUserToCrmUserByRole failed userPrivilegeRestService.getRolesByUsers return null ea:{} roleCode:{} fsUserI:{}", ea, roleCode, fsUserIds);
            return;
        }
        Map<String, List<String>> userRoleMap = new HashMap<>();
        for (UserRoleVo userRoleVo : userRoleVoList) {
            if (userRoleMap.get(userRoleVo.getUserId()) == null) {
                userRoleMap.put(userRoleVo.getUserId(), Lists.newArrayList());
            }
            userRoleMap.get(userRoleVo.getUserId()).add(userRoleVo.getRoleCode());

        }

        //过滤出需要设置角色的员工
        List<String> setCrmRoleUserId = employeeIds.stream().filter(userId -> userRoleMap.get(userId) != null && !userRoleMap.get(userId).contains(roleCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(setCrmRoleUserId)) {
            return;
        }

        //查询主角色
        BatchGetMajorRoleCodeByUserIdsVo.Argument queryMajarRoleArgument = new BatchGetMajorRoleCodeByUserIdsVo.Argument();
        queryMajarRoleArgument.setUserIds(setCrmRoleUserId);
        Map<String, String> majorRoleMap = userPrivilegeRestService.batchGetMajorRoleCodeByUserIds(privilegeContext, queryMajarRoleArgument);
        if (majorRoleMap == null) {
            log.error("UserRoleManager.addMarketingUserToCrmUser get major role failed ea:{}, fsUserIds:{}", ea, fsUserIds);
            return;
        }

        for (String userId : setCrmRoleUserId) {
            BatchAddUserRoleVo.Argument arg = new BatchAddUserRoleVo.Argument();
            arg.setRoleCodes(Lists.newArrayList(roleCode));
            arg.setUserIds(Lists.newArrayList(userId));
            arg.setMajorRole(majorRoleMap.get(userId) == null ? roleCode : majorRoleMap.get(userId));
            arg.setUpdateMajorRole(false);
            log.info("addMarketingUserToCrmUserByRole add role:{} userId:{} ", roleCode, userId);
            userPrivilegeRestService.batchAddUserRole(privilegeContext, arg);
        }
    }

    public List<UserRoleVo> queryMarketingUserRole(String ea, Integer fsUserId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        GetRolesByUsersVo.Argument arg = new GetRolesByUsersVo.Argument();
        List<String> employeeIds = Lists.newArrayList();
        employeeIds.add(String.valueOf(fsUserId));
        arg.setUserIds(employeeIds);

        return userPrivilegeRestService.getRolesByUsers(privilegeContext, arg);
    }


    /**
     * 开通营销通时，初始化相关角色及权限
     *
     * @param ea
     */
    public void initCrmFunctionPrivilege(String ea) {
        initFunctionPrivilegeForMarketingManager(ea);
        initFunctionPrivilegeForMarketingCommonUser(ea);
    }

    /**
     * 开通营销通时---设置市场管理员的对象功能权限
     * CRM出场预置了部分权限，需要独立设置会员的权限
     *
     * @param ea
     */
    private void initFunctionPrivilegeForMarketingManager(String ea) {
        List<String> addFuncCodes = Lists.newArrayList();
        //给管理员设置会员对象的查看列表和查看详情的权限
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            addFuncCodes.add("MemberObj");
            addFuncCodes.add("MemberObj||View");
        }

        //设置【企业微信用户】查看列表、查看详情
        getDescribeResultResult =
                objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            addFuncCodes.add("WechatWorkExternalUserObj");
            addFuncCodes.add("WechatWorkExternalUserObj||View");
        }

        //设置【微信用户】查看列表、查看详情
        getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            addFuncCodes.add("WechatFanObj");
            addFuncCodes.add("WechatFanObj||View");
        }

        //设置【企微客户群】查看列表、查看详情
        addFuncCodes.add("WechatGroupObj");
        addFuncCodes.add("WechatGroupObj||View");

        //设置【企微客户群成员】查看列表、查看详情
        addFuncCodes.add("WechatGroupUserObj");
        addFuncCodes.add("WechatGroupUserObj||View");

        if (CollectionUtils.isNotEmpty(addFuncCodes)) {
            configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
        }
    }

    /**
     * 开通营销通时---设置化市场人员的对象功能权限
     *
     * @param ea
     */
    public void initFunctionPrivilegeForMarketingCommonUser(String ea) {
        List<String> addFuncCodes = Lists.newArrayList();
        //市场人员设置【会员】的查看列表、查看详情的权限
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            addFuncCodes.add("MemberObj");
            addFuncCodes.add("MemberObj||View");
        }

        //市场人员设置【营销动态】查看列表、查看详情的权限
        addFuncCodes.add("MarketingBehaviorObj");
        addFuncCodes.add("MarketingBehaviorObj||View");

        //市场人员设置【营销活动】查看列表、查看详情、新建的权限
        addFuncCodes.add("MarketingActivityObj");
        addFuncCodes.add("MarketingActivityObj||View");
        addFuncCodes.add("MarketingActivityObj||Add");

        //市场人员设置【企微群】【企微群成员】查看列表、查看详情、新建的权限
        addFuncCodes.add("WechatGroupObj");
        addFuncCodes.add("WechatGroupObj||View");


        //设置【微信用户】查看列表、查看详情
        getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            addFuncCodes.add("WechatFanObj");
            addFuncCodes.add("WechatFanObj||View");
        }

        //市场人员设置【企业微信客户】查看列表、查看详情
        //设置【企业微信客户】查看列表、查看详情
        getDescribeResultResult =
                objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            addFuncCodes.add("WechatWorkExternalUserObj");
            addFuncCodes.add("WechatWorkExternalUserObj||View");
        }

        //【营销关键词】查看列表、查看详情
        if (adCommonManager.isPurchaseAdLicense(ea)) {
            addFuncCodes.add("MarketingKeywordObj");
            addFuncCodes.add("MarketingKeywordObj||View");

            //【关键词投放计划】查看列表、查看详情
            addFuncCodes.add("KeywordServingPlanObj");
            addFuncCodes.add("KeywordServingPlanObj||View");

            //【关键词投放明细】查看列表、查看详情
            addFuncCodes.add("TermServingLinesObj");
            addFuncCodes.add("TermServingLinesObj||View");
        }

        //设置【企微客户群】查看列表、查看详情
        addFuncCodes.add("WechatGroupObj");
        addFuncCodes.add("WechatGroupObj||View");

        //设置【企微客户群成员】查看列表、查看详情
        addFuncCodes.add("WechatGroupUserObj");
        addFuncCodes.add("WechatGroupUserObj||View");

        if (CollectionUtils.isNotEmpty(addFuncCodes)) {
            configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
        }
    }


    /**
     * 绑定公众号时：
     * 1，创建公众号运营角色
     * 2，设置角色的功能权限
     * 3，将营销通公众号运营人员刷到该角色
     * 4，配置微信用户的共享权限
     *
     * @param ea
     */
    public void initOfficialAccountMarketingconfig(String ea) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();

        //预设公众号运营角色，先查询这个企业是否有这个角色
        QueryAllRoleInfoByRoleCodesVo.Argument queryArgument = new QueryAllRoleInfoByRoleCodesVo.Argument();
        Set<String> roleCodes = new HashSet<>();
        roleCodes.add(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
        queryArgument.setRoleCodes(roleCodes);
        List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, queryArgument);
        if (CollectionUtils.isEmpty(roleVoList)) {
            //角色不存在，就创建角色
            AddRoleVo.Argument addArgument = new AddRoleVo.Argument();
            addArgument.setRoleName(OFFICIAL_ACCOUNT_ROLE_NAME);
            addArgument.setDescription(OFFICIAL_ACCOUNT_ROLE_NAME);
            addArgument.setRoleCode(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);
            addArgument.setRoleType(RoleType.DEFAULT);
            rolePrivilegeRestService.add(privilegeContext, addArgument);
        }

        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT.getName());
        if (getDescribeResultResult.getCode() == CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            log.info("initOfficialAccountMarketingconfig WechatFanObj is not exist ea{}", ea);
            return;
        }

        //配置角色权限 【微信用户】查看列表、查看详情
        List<String> addFuncCodes = Lists.newArrayList();
        addFuncCodes.add("WechatFanObj");
        addFuncCodes.add("WechatFanObj||View");
        configRoleObjectFunctionPrivilege(ea, -10000, OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE, addFuncCodes, null);
        configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
        configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);

        //设置营销通管理员&公众号运营到公众号运营角色
        List<UserRoleEntity> userRoleEntityList = userRoleDao.listByEa(ea);
        if (CollectionUtils.isNotEmpty(userRoleEntityList)) {
            Set<Integer> fsUserIdSet = new HashSet<>();
            for (UserRoleEntity userRoleEntity : userRoleEntityList) {
                String roleId = userRoleEntity.getRoleId();
                if (roleId.equals(RoleConstant.SYSTEM_ADMIN_ROLE_ID) || roleId.equals(RoleConstant.WECHAT_OPERATION)) {
                    fsUserIdSet.add(userRoleEntity.getEmployeeId());
                }
            }
            if (CollectionUtils.isNotEmpty(fsUserIdSet)) {
                addMarketingUserToCrmUserByRole(ea, OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE, new ArrayList<>(fsUserIdSet));
            }
        }

        //配置共享规则
        createWechatShareRule(ea);
    }

    /**
     * 初始现有企业的各类权限和角色
     *
     * @param ea
     */
    public void initOldMakeringRoleFunctionPrivilege(String ea) {
        if (campaignMergeDataResetManager.enterpriseStop(ea)) {
            log.info("initOldMarketingRoleToCrmRole skip stop enterprise ea:{}", ea);
            return;
        }
        initOldMarketingRoleToCrmRole(ea);   //营销通角色->CRM角色
        initMarketingAdObjetPrivilege(ea);   //设置市场人员的营销一体化对象的权限
        initMemberObjPrivilege(ea);          //开通会员企业，市场角色对会员对象的权限
        initWechatWorkExternalUserObjPrivilege(ea);   //开通企业微信客户对象时，初始化角色的功能权限
        if (marketingWxServiceDao.countByEa(ea) > 0) {   //微信用户对象
            initOfficialAccountMarketingconfig(ea);   //预置公账号角色，并设置权限
        }

    }

    /**
     * 将历史营销通角色刷到CRM角色中
     *
     * @param ea
     */
    private void initOldMarketingRoleToCrmRole(String ea) {
        List<UserRoleEntity> userRoleEntityList = userRoleDao.listByEa(ea);
        if (CollectionUtils.isEmpty(userRoleEntityList)) {
            return;
        }

        Map<String, Set<Integer>> userRoleMap = new HashMap<>();
        for (UserRoleEntity userRoleEntity : userRoleEntityList) {
            if (userRoleEntity.getRoleId() == null) {
                log.info("initOldMarketingRoleToCrmRole user role null ea:{} userRoleEntity:{}", userRoleEntity);
                continue;
            }

            if (userRoleEntity.getRoleId().equals(SYSTEM_ADMIN_ROLE_ID)) {
                if (userRoleMap.get(MARKETING_MANAGER_ROLE_CODE) == null) {
                    userRoleMap.putIfAbsent(MARKETING_MANAGER_ROLE_CODE, new HashSet<>());
                }
                userRoleMap.get(MARKETING_MANAGER_ROLE_CODE).add(userRoleEntity.getEmployeeId());
            } else if (userRoleEntity.getRoleId().equals(RoleConstant.WECHAT_OPERATION)) {
                if (userRoleMap.get(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE) == null) {
                    userRoleMap.putIfAbsent(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE, new HashSet<>());
                }
                userRoleMap.get(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE).add(userRoleEntity.getEmployeeId());

                if (userRoleMap.get(MARKETING_USER_ROLE_CODE) == null) {
                    userRoleMap.putIfAbsent(MARKETING_USER_ROLE_CODE, new HashSet<>());
                }
                userRoleMap.get(MARKETING_USER_ROLE_CODE).add(userRoleEntity.getEmployeeId());
            } else {
                if (userRoleMap.get(MARKETING_USER_ROLE_CODE) == null) {
                    userRoleMap.putIfAbsent(MARKETING_USER_ROLE_CODE, new HashSet<>());
                }
                userRoleMap.get(MARKETING_USER_ROLE_CODE).add(userRoleEntity.getEmployeeId());
            }
        }

        for (Map.Entry<String, Set<Integer>> entry : userRoleMap.entrySet()) {
            String roleCode = entry.getKey();
            Set<Integer> fsUserIds = entry.getValue();
            if (roleCode.equals(MARKETING_MANAGER_ROLE_CODE)) {
                addToCrmManagerManagerUserRole(ea, new ArrayList<>(fsUserIds));
            }

            if (roleCode.equals(MARKETING_USER_ROLE_CODE)) {
                addToCrmManagerCommonUserRole(ea, new ArrayList<>(fsUserIds));
            }

            if (roleCode.equals(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE)) {
                addToCrmOfficialAccountRole(ea, new ArrayList<>(fsUserIds));
            }
        }
    }

    /**
     * 开通企业微信客户对象时，初始化角色的功能权限
     *
     * @param ea
     */
    public void initWechatWorkExternalUserObjPrivilege(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            List<String> addFuncCodes = Lists.newArrayList();
            addFuncCodes.add("WechatWorkExternalUserObj");
            addFuncCodes.add("WechatWorkExternalUserObj||View");

            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
            }
        }
    }

    /**
     * 开通企业微信添加客户记录对象时，初始化角色的功能权限
     *
     * @param ea
     */
    public void initWechatFriendsRecordObjPrivilege(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            List<String> addFuncCodes = Lists.newArrayList();
            addFuncCodes.add(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
            addFuncCodes.add(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName() + "||View");
            addFuncCodes.add(CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName() + "||Edit");

            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
//                configRoleObjectFunctionPrivilege(ea, -10000, CRM_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
        }
    }

    public void initObjPrivilege(String ea, String crmObjectApiName) {
        if (this.isExistObject(ea, crmObjectApiName)) {
            List<String> addFuncCodes = Lists.newArrayList();
            addFuncCodes.add(crmObjectApiName);
            addFuncCodes.add(crmObjectApiName + "||View");
            addFuncCodes.add(crmObjectApiName + "||Edit");

            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
            }
        }
    }

    public boolean isExistObject(String ea, String crmObjectApiName) {
        try {
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult =
                    objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), crmObjectApiName);
            return getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess();
        } catch (Exception e) {
            log.warn("UserRoleManager isExistObject error", e);
        }
        return false;
    }

    /**
     * 开通会员对象时，初始化角色的功能权限
     *
     * @param ea
     */
    public void initMemberObjPrivilege(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.MEMBER.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            List<String> addFuncCodes = Lists.newArrayList();
            addFuncCodes.add("MemberObj");
            addFuncCodes.add("MemberObj||View");
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
            }
        }
    }

    /**
     * 开通企微客户群对象时，初始化角色的功能权限
     *
     * @param ea
     */
    public void initWechatGroupObjPrivilege(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            List<String> addFuncCodes = Lists.newArrayList();
            addFuncCodes.add("WechatGroupObj");
            addFuncCodes.add("WechatGroupObj||View");

            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
            }
        }
    }

    /**
     * 开通企微客户群成员对象时，初始化角色的功能权限
     *
     * @param ea
     */
    public void initWechatGroupUserObjPrivilege(String ea) {
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName());
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && getDescribeResultResult.isSuccess()) {
            List<String> addFuncCodes = Lists.newArrayList();
            addFuncCodes.add("WechatGroupUserObj");
            addFuncCodes.add("WechatGroupUserObj||View");

            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
            }
            if (CollectionUtils.isNotEmpty(addFuncCodes)) {
                configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
            }
        }
    }

    /**
     * 开通营销一体化时，初始化角色的功能权限
     *
     * @param ea
     */
    public void initMarketingAdObjetPrivilege(String ea) {
        if (!adCommonManager.isPurchaseAdLicense(ea)) {
            return;
        }
        List<String> addFuncCodes = Lists.newArrayList();
        addFuncCodes.add("MarketingKeywordObj");
        addFuncCodes.add("MarketingKeywordObj||View");

        //【关键词投放计划】查看列表、查看详情
        addFuncCodes.add("KeywordServingPlanObj");
        addFuncCodes.add("KeywordServingPlanObj||View");

        //【关键词投放明细】查看列表、查看详情
        addFuncCodes.add("TermServingLinesObj");
        addFuncCodes.add("TermServingLinesObj||View");

        if (CollectionUtils.isNotEmpty(addFuncCodes)) {
            configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
        }

        contentPropagationDetailObjManager.getOrCreateObjDescribe(ea);
        this.initObjPrivilege(ea, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());
        employeePromoteDetailObjManager.getOrCreateObjDescribe(ea);
        this.initObjPrivilege(ea, CrmObjectApiNameEnum.CONTENT_PROPAGATION_DETAIL_OBJ.getName());

        contentPropagationDetailObjManager.tryUpdateCustomFieldLabel(ea);
        employeePromoteDetailObjManager.tryUpdateCustomFieldLabel(ea);
    }

    private void createWechatShareRule(String ea) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.paasauthrestapi.common.data.HeaderObj systemHeader = com.fxiaoke.paasauthrestapi.common.data.HeaderObj.newInstance(tenantId);
        systemHeader.put("x-fs-userInfo", -10000);
        BatchCreateEntityShareArg arg = new BatchCreateEntityShareArg();
        PaasAuthContextData contextData = new PaasAuthContextData();
        contextData.setAppId("CRM");
        contextData.setTenantId(String.valueOf(tenantId));
        contextData.setUserId("-10000");
        arg.setContext(contextData);
        ShareRuleData shareRule = new ShareRuleData();
        shareRule.setEntityId(CrmObjectApiNameEnum.WECHAT.getName());
        shareRule.setEntityShareType(0);   //共享规则类型 0内部共享规则 1外部共享规则
        shareRule.setPermission(1);     //权限类型 1只读 2读写
        shareRule.setShareType(2);      //用户:0 用户组:1 部门:2 角色:4
        shareRule.setShareId("999999"); //全公司
        shareRule.setReceiveType(4);    //共享规则目标方类型 用户:0 用户组:1 部门:2 角色:4 下游企业:5 下游企业组:6 全部下游企业:7
        shareRule.setReceiveId(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE);   //目标方Id 全部下游企业传-*********
        shareRule.setStatus(1);   //1开启 0关闭
        arg.setShareList(Arrays.asList(shareRule));
        com.fxiaoke.paasauthrestapi.common.result.Result<Void> createResult = paasShareRuleService.batchCreateEntityShare(systemHeader, arg);
        log.info("httpConfigDataShareRule createResult:{}", createResult);
    }

    /**
     * 通过角色查找员工id
     *
     * @param ea
     * @param roleCodes
     * @return
     */
    public List<Integer> getEmployeeIdsByRoles(String ea, List<String> roleCodes) {
        if (CollectionUtils.isEmpty(roleCodes)) {
            return null;
        }

        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        GetUsersByRoleCodesVo.Argument argument = new GetUsersByRoleCodesVo.Argument();
        argument.setRoleCodes(roleCodes);
        List<UserRoleVo> userRoleVoList = userPrivilegeRestService.getUsersByRoleCodes(privilegeContext, argument);
        if (CollectionUtils.isEmpty(userRoleVoList)) {
            return null;
        }

        List<Integer> employeeIds = userRoleVoList.stream().map(userRoleVo -> Integer.parseInt(userRoleVo.getUserId())).collect(Collectors.toList());
        return employeeIds;
    }

    public boolean isContainSystemAdiminRoleId(Set<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }

        if (roleIds.contains(SYSTEM_ADMIN_ROLE_ID)) {
            return true;
        }
        return false;
    }

    public boolean isContainWechatOperation(Set<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }

        if (roleIds.contains(RoleConstant.WECHAT_OPERATION) || roleIds.contains(SYSTEM_ADMIN_ROLE_ID)) {
            return true;
        }
        return false;
    }

    public boolean isContainMarketingCommonUser(Set<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }

        if (!(roleIds.size() == 1 && roleIds.contains(SYSTEM_ADMIN_ROLE_ID))) {
            return true;
        }

        return false;
    }

    public Set<String> getUserRoleCodes(String ea, Integer fsUserId) {
        List<UserRoleVo> userRoleVoList = queryMarketingUserRole(ea, fsUserId);
        if (userRoleVoList == null) {
            log.info("getUserRoleCodes failed ea:{} fsUserId:{}", ea, fsUserId);
            return new HashSet<>();
        }
        return userRoleVoList.stream().map(UserRoleVo::getRoleCode).collect(Collectors.toSet());
    }

    public List<RoleVo> getRolesByRoleCodeList(String ea, List<String> roleCodeList) {
        if (CollectionUtils.isEmpty(roleCodeList)) {
            return Lists.newArrayList();
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder()
                .appId("CRM").tenantId(tenantId).operatorId(-10000).build();
        BatchGetRoleByRoleCodesVo.Argument argument = new BatchGetRoleByRoleCodesVo.Argument();
        argument.setRoleCodes(roleCodeList);
        return rolePrivilegeRestService.batchGetRoleByRoleCodes(privilegeContext, argument);
    }
    private List<String> buildObjectFunctionPrivilege(String apiName, List<String> functionCodes) {
        List<String> resultList = Lists.newArrayList();
        resultList.add(apiName);
        for (String functionCode : functionCodes) {
            resultList.add(apiName + "||" + functionCode);
        }
        return resultList;
    }

    /**
     * 新增企微角色
     * 0, 判断是否有这些对象,有才处理下面
     * 1，创建企微运营管理员
     * 2，设置角色的功能权限
     * 3，将营销通企业微信运营人员和超管刷到该角色
     *
     * @param ea
     */
    public void initQywxAccountMarketingConfig(String ea) {

        List<String> apiNames = Lists.newArrayList(CrmObjectApiNameEnum.getAllQywxApiNames());
        Result<ListObjectDescribeResult> list = objectDescribeService.list(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), false, Lists.newArrayList(apiNames));
        //角色对应的对象都没有直接返回,不配置权限,也不同步角色
        if (!list.isSuccess() && list.getData() == null) {
            log.info("UserRoleManager initQywxAccountMarketingConfig not objs ea:{} ", ea);
            return;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();

        //预设角色，先查询这个企业是否有这个角色
        QueryAllRoleInfoByRoleCodesVo.Argument queryArgument = new QueryAllRoleInfoByRoleCodesVo.Argument();
        Set<String> roleCodes = new HashSet<>();
        roleCodes.add(QYWX_ACCOUNT_MARKETING_ROLE_CODE);
        queryArgument.setRoleCodes(roleCodes);
        List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, queryArgument);
        if (CollectionUtils.isEmpty(roleVoList)) {
            //角色不存在，就创建角色
            AddRoleVo.Argument addArgument = new AddRoleVo.Argument();
            addArgument.setRoleName(QYWX_ACCOUNT_ROLE_NAME);
            addArgument.setDescription(QYWX_ACCOUNT_ROLE_NAME);
            addArgument.setRoleCode(QYWX_ACCOUNT_MARKETING_ROLE_CODE);
            addArgument.setRoleType(RoleType.DEFAULT);
            rolePrivilegeRestService.add(privilegeContext, addArgument);
        }

        apiNames = list.getData().getDescribe().stream().map(ObjectDescribe::getApiName).collect(Collectors.toList());
        List<String> addFuncCodes = getFunctionPrivilegeString(apiNames);
        configRoleObjectFunctionPrivilege(ea, -10000, QYWX_ACCOUNT_MARKETING_ROLE_CODE, addFuncCodes, null);

        //设置营销通管理员&企微运营到企微运营角色
        List<UserRoleEntity> userRoleEntityList = userRoleDao.listByEa(ea);
        if (CollectionUtils.isNotEmpty(userRoleEntityList)) {
            Set<Integer> fsUserIdSet = new HashSet<>();
            for (UserRoleEntity userRoleEntity : userRoleEntityList) {
                String roleId = userRoleEntity.getRoleId();
                if (roleId.equals(RoleConstant.SYSTEM_ADMIN_ROLE_ID) || roleId.equals(RoleConstant.WECHAT_WORK_OPERATION)) {
                    fsUserIdSet.add(userRoleEntity.getEmployeeId());
                }
            }
            if (CollectionUtils.isNotEmpty(fsUserIdSet)) {
                addMarketingUserToCrmUserByRole(ea, QYWX_ACCOUNT_MARKETING_ROLE_CODE, new ArrayList<>(fsUserIdSet));
            }
        }
    }

    /**
     * 新增广告角色
     * 0, 判断是否有这些对象,有才处理下面
     * 1，创建广告运营管理员
     * 2，设置角色的功能权限
     * 3，将营销通广告运营人员和超管刷到该角色
     *
     * @param ea
     */
    public void initAdAccountMarketingConfig(String ea) {

        List<String> apiNames = Lists.newArrayList(CrmObjectApiNameEnum.getAllAdApiNames());
        Result<ListObjectDescribeResult> list = objectDescribeService.list(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), false, Lists.newArrayList(apiNames));
        //角色对应的对象都没有直接返回,不配置权限,也不同步角色
        if (!list.isSuccess() && list.getData() == null) {
            log.info("UserRoleManager initAdAccountMarketingConfig not objs ea:{} ", ea);
            return;
        }
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext privilegeContext = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(-10000).build();

        //预设角色，先查询这个企业是否有这个角色
        QueryAllRoleInfoByRoleCodesVo.Argument queryArgument = new QueryAllRoleInfoByRoleCodesVo.Argument();
        Set<String> roleCodes = new HashSet<>();
        roleCodes.add(AD_ACCOUNT_MARKETING_ROLE_CODE);
        queryArgument.setRoleCodes(roleCodes);
        List<RoleVo> roleVoList = rolePrivilegeRestService.queryAllRoleInfoByRoleCodes(privilegeContext, queryArgument);
        if (CollectionUtils.isEmpty(roleVoList)) {
            //角色不存在，就创建角色
            AddRoleVo.Argument addArgument = new AddRoleVo.Argument();
            addArgument.setRoleName(AD_ACCOUNT_ROLE_NAME);
            addArgument.setDescription(AD_ACCOUNT_ROLE_NAME);
            addArgument.setRoleCode(AD_ACCOUNT_MARKETING_ROLE_CODE);
            addArgument.setRoleType(RoleType.DEFAULT);
            rolePrivilegeRestService.add(privilegeContext, addArgument);
        }

        apiNames = list.getData().getDescribe().stream().map(ObjectDescribe::getApiName).collect(Collectors.toList());
        List<String> addFuncCodes = getFunctionPrivilegeString(apiNames);
        configRoleObjectFunctionPrivilege(ea, -10000, AD_ACCOUNT_MARKETING_ROLE_CODE, addFuncCodes, null);


        //设置营销通管理员&广告运营到广告运营角色
        List<UserRoleEntity> userRoleEntityList = userRoleDao.listByEa(ea);
        if (CollectionUtils.isNotEmpty(userRoleEntityList)) {
            Set<Integer> fsUserIdSet = new HashSet<>();
            for (UserRoleEntity userRoleEntity : userRoleEntityList) {
                String roleId = userRoleEntity.getRoleId();
                if (roleId.equals(RoleConstant.SYSTEM_ADMIN_ROLE_ID) || roleId.equals(RoleConstant.ADVERTISING_OPERATION)) {
                    fsUserIdSet.add(userRoleEntity.getEmployeeId());
                }
            }
            if (CollectionUtils.isNotEmpty(fsUserIdSet)) {
                addMarketingUserToCrmUserByRole(ea, AD_ACCOUNT_MARKETING_ROLE_CODE, new ArrayList<>(fsUserIdSet));
            }
        }
    }

    public void initRoleMarketingConfig(String ea) {
        try {
            initQywxAccountMarketingConfig(ea);
            initAdAccountMarketingConfig(ea);
        } catch (Exception e) {
            log.error("updateEnrollActionType error, ea: {}  ", ea, e);
        }
    }


    //产品想要,sfa不让刷
//
//    /**
//     * 刷市场管理员功能权限
//     * @param ea
//     */
//    public void initMarketingManagerRoleToCrmRole(String ea){
//        List<RoleVo> roles = getRolesByRoleCodeList(ea, Lists.newArrayList(MARKETING_MANAGER_ROLE_CODE));
//        if(CollectionUtils.isEmpty(roles)){
//            return;
//        }
//        List<String> apiNames = Lists.newArrayList(CrmObjectApiNameEnum.getAllMarketingManagerRoleApiNames());
//        Result<ListObjectDescribeResult> list = objectDescribeService.list(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), false, Lists.newArrayList(apiNames));
//        if (!list.isSuccess() && list.getData()==null){
//            return;
//        }
//        apiNames = list.getData().getDescribe().stream().map(ObjectDescribe::getApiName).collect(Collectors.toList());
//        //配置角色权限
//        List<String> addFuncCodes = getFunctionPrivilegeString(apiNames);
//        if (CollectionUtils.isNotEmpty(addFuncCodes)){
//            configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_MANAGER_ROLE_CODE, addFuncCodes, null);
//            System.out.println("11");
//        }
//    }
//
//    /**
//     * 刷会员管理员功能权限
//     * @param ea
//     */
//    public void initMemberRoleToCrmRole(String ea){
//        List<RoleVo> roles = getRolesByRoleCodeList(ea, Lists.newArrayList(PAAS_MEMBER_MANAGER_ROLE_CODE));
//        if(CollectionUtils.isEmpty(roles)){
//            return;
//        }
//        //配置角色权限
//        List<String> apiNames = Lists.newArrayList(CrmObjectApiNameEnum.getAllMemberApiNames());
//        Result<ListObjectDescribeResult> list = objectDescribeService.list(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), false, Lists.newArrayList(apiNames));
//        if (!list.isSuccess() && list.getData()==null){
//            return;
//        }
//        apiNames = list.getData().getDescribe().stream().map(ObjectDescribe::getApiName).collect(Collectors.toList());
//        List<String> addFuncCodes = getFunctionPrivilegeString(apiNames);
//
//
//        List<String> addFuncCodes = getFunctionPrivilegeString(Lists.newArrayList(CrmObjectApiNameEnum.getAllMemberApiNames()));
//        if (CollectionUtils.isNotEmpty(addFuncCodes)){
//            configRoleObjectFunctionPrivilege(ea, -10000, PAAS_MEMBER_MANAGER_ROLE_CODE, addFuncCodes, null);
//        }
//    }
//
//    /**
//     * 刷公众号管理员功能权限
//     * @param ea
//     */
//    public void initMarketingUserRoleManagerRoleToCrmRole(String ea){
//        List<RoleVo> roles = getRolesByRoleCodeList(ea, Lists.newArrayList(OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE));
//        if(CollectionUtils.isEmpty(roles)){
//            return;
//        }
//        //配置角色权限
//        List<String> apiNames = Lists.newArrayList(CrmObjectApiNameEnum.getAllWechatApiNames());
//        Result<ListObjectDescribeResult> list = objectDescribeService.list(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), false, Lists.newArrayList(apiNames));
//        if (!list.isSuccess() && list.getData()==null){
//            return;
//        }
//        apiNames = list.getData().getDescribe().stream().map(ObjectDescribe::getApiName).collect(Collectors.toList());
//        List<String> addFuncCodes = getFunctionPrivilegeString(apiNames);
//        if (CollectionUtils.isNotEmpty(addFuncCodes)){
//            configRoleObjectFunctionPrivilege(ea, -10000, OFFICIAL_ACCOUNT_MARKETING_ROLE_CODE, addFuncCodes, null);
//        }
//    }
//
//
//    /**
//     * 刷市场人员功能权限
//     * @param ea
//     */
//    public void initWechatManagerRoleToCrmRole(String ea){
//        List<RoleVo> roles = getRolesByRoleCodeList(ea, Lists.newArrayList(MARKETING_USER_ROLE_CODE));
//        if(CollectionUtils.isEmpty(roles)){
//            return;
//        }
//
//        //配置角色权限
//        List<String> apiNames = Lists.newArrayList(CrmObjectApiNameEnum.getAllUniversalRoleApiNames());
//        Result<ListObjectDescribeResult> list = objectDescribeService.list(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), false, Lists.newArrayList(apiNames));
//        if (!list.isSuccess() && list.getData()==null){
//            return;
//        }
//        apiNames = list.getData().getDescribe().stream().map(ObjectDescribe::getApiName).collect(Collectors.toList());
//        List<String> addFuncCodes = getFunctionPrivilegeString(apiNames);
//        if (CollectionUtils.isNotEmpty(addFuncCodes)){
//            configRoleObjectFunctionPrivilege(ea, -10000, MARKETING_USER_ROLE_CODE, addFuncCodes, null);
//        }
//    }
//
//
//
//    /**
//     * 将历史营销通企微运营人员和广告运营人员角色刷到CRM角色中
//     * @param ea
//     */
//    public void initOldQywxAndAdMarketingRoleToCrmRole(String ea){
//        List<UserRoleEntity> userRoleEntityList = userRoleDao.listByEa(ea);
//        if (CollectionUtils.isEmpty(userRoleEntityList)){
//            return;
//        }
//
//        Map<String, Set<Integer>> userRoleMap = new HashMap<>();
//        for (UserRoleEntity userRoleEntity : userRoleEntityList){
//            if (userRoleEntity.getRoleId() == null){
//                log.info("initOldQywxAndAdMarketingRoleToCrmRole user role null ea:{} userRoleEntity:{}", userRoleEntity);
//                continue;
//            }
//
//            if (userRoleEntity.getRoleId().equals(RoleConstant.ADVERTISING_OPERATION)){
//                if (userRoleMap.get(AD_ACCOUNT_MARKETING_ROLE_CODE) == null){
//                    userRoleMap.putIfAbsent(AD_ACCOUNT_MARKETING_ROLE_CODE, new HashSet<>());
//                }
//                userRoleMap.get(AD_ACCOUNT_MARKETING_ROLE_CODE).add(userRoleEntity.getEmployeeId());
//
//            }else if (userRoleEntity.getRoleId().equals(RoleConstant.WECHAT_WORK_OPERATION)){
//                if (userRoleMap.get(QYWX_ACCOUNT_MARKETING_ROLE_CODE) == null){
//                    userRoleMap.putIfAbsent(QYWX_ACCOUNT_MARKETING_ROLE_CODE, new HashSet<>());
//                }
//                userRoleMap.get(QYWX_ACCOUNT_MARKETING_ROLE_CODE).add(userRoleEntity.getEmployeeId());
//            }
//        }
//
//        for (Map.Entry<String, Set<Integer>> entry : userRoleMap.entrySet()){
//            String roleCode = entry.getKey();
//            Set<Integer> fsUserIds = entry.getValue();
//            if (roleCode.equals(AD_ACCOUNT_MARKETING_ROLE_CODE)){
//                addToCrmAdAccountRole(ea,new ArrayList<>(fsUserIds));
//            }
//
//            if (roleCode.equals(QYWX_ACCOUNT_MARKETING_ROLE_CODE)){
//                addToCrmQywxAccountRole(ea,new ArrayList<>(fsUserIds));
//            }
//        }
//    }


    private List<String> getFunctionPrivilegeString(List<String> apiNames) {
        if (CollectionUtils.isEmpty(apiNames)) {
            return null;
        }
        List<String> resultList = Lists.newArrayList();
        for (String apiName : apiNames) {
            resultList.add(apiName);
            for (String defaulFunctionPrivilege : DEFAULT_FUNCTION_PRIVILEGES) {
                resultList.add(apiName + "||" + defaulFunctionPrivilege);
            }
        }
        return resultList;
    }

    public Integer getActivityEmployeeIdByRole(String ea, String roleCode) {
        List<UserRoleEntity> userRoleEntityList = userRoleDao.getListByRole(ea, roleCode);
        if (CollectionUtils.isEmpty(userRoleEntityList)) {
            return null;
        }

        BatchGetEmployeeDtoArg batchGetEmployeeDtoArg = new BatchGetEmployeeDtoArg();
        List<Integer> employeeIds = userRoleEntityList.stream().map(UserRoleEntity::getEmployeeId).collect(Collectors.toList());
        batchGetEmployeeDtoArg.setEmployeeIds(employeeIds);
        batchGetEmployeeDtoArg.setRunStatus(RunStatus.ACTIVE);
        batchGetEmployeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(ea));
        BatchGetEmployeeDtoResult employeeDtoResult = employeeProviderService.batchGetEmployeeDto(batchGetEmployeeDtoArg);
        if (employeeDtoResult == null || CollectionUtils.isEmpty(employeeDtoResult.getEmployeeDtos())) {
            return null;
        }

        return employeeDtoResult.getEmployeeDtos().get(0).getEmployeeId();
    }

    // 给角色设置对象基础权限
    public void addBasicFunctionPrivilege(String ea, String apiName, String roleCode) {
        List<String> resultList = Lists.newArrayList();
        resultList.add(apiName);
        for (String defaultFunctionPrivilege : DEFAULT_FUNCTION_PRIVILEGES) {
            resultList.add(apiName + "||" + defaultFunctionPrivilege);
        }
        configRoleObjectFunctionPrivilege(ea, SuperUserConstants.USER_ID, roleCode, resultList, null);
    }

    public void batchAddSystemFuncCode(String ea, Integer fsUserId, List<BatchAddFunctionVo.FunctionVo> functionList) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        final PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(tenantId).operatorId(fsUserId).build();
        BatchAddFunctionVo.Argument argument = new BatchAddFunctionVo.Argument();
        argument.setFunctionVos(functionList);
        BatchAddFunctionVo.Result result = functionPrivilegeRestService.batchAdd(crm, argument);
        log.info("UserRoleManager batchAddSystemFuncCode ea:{}  functionList: {} result:{}", ea, functionList, result);
    }
}
