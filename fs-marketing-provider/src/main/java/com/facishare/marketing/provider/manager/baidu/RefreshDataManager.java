/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.baidu;

import com.beust.jcommander.internal.Sets;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.CustomizeFunctionConstants;
import com.facishare.marketing.common.enums.AdvertisingTypeEnum;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.advertiser.headlines.HeadlinesAdTypeEnum;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmV2MarketingEventFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.advertiser.adAccount.TencentAdResult;
import com.facishare.marketing.provider.advertiser.tencent.TencentKeywordDataResult;
import com.facishare.marketing.provider.baidu.report.ReportApiManager;
import com.facishare.marketing.provider.bo.advertise.AdSyncMarketingEventObjBO;
import com.facishare.marketing.provider.bo.advertise.SyncKeywordServicePlanBO;
import com.facishare.marketing.provider.bo.advertise.SyncTermServiceLineBO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesAdDAO;
import com.facishare.marketing.provider.dao.advertiser.headlines.HeadlinesCampaignDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentCampaignDAO;
import com.facishare.marketing.provider.dao.baidu.*;
import com.facishare.marketing.provider.dto.AdReportDataDTO;
import com.facishare.marketing.provider.entity.advertiser.AdCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.AdvertiserAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.AdLeadsMappingDataEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesAdEntity;
import com.facishare.marketing.provider.entity.advertiser.headlines.HeadlinesCampaignEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.entity.baidu.*;
import com.facishare.marketing.provider.innerArg.AdMarketingEventNameArg;
import com.facishare.marketing.provider.innerArg.crm.ExecuteCustomizeFunctionArg;
import com.facishare.marketing.provider.manager.CustomizeFunctionManager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.SpreadChannelManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.rateLimiter.YxtRRateLimiter;
import com.facishare.marketing.provider.manager.rateLimiter.YxtRRateLimiterBuilder;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.vo.base.CrmDuplicateSearchVo;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.NumberUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.*;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.*;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.limit.GuavaLimiter;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhengh on 2021/2/23.
 */
@Component
@Slf4j
public class RefreshDataManager {

    @Autowired
    private AdAccountManager adAccountManager;

    @Autowired
    private BaiduCampaignDAO baiduCampaignDAO;

    @Autowired
    private TencentCampaignDAO tencentCampaignDAO;

    @Autowired
    private MetadataActionService metadataActionService;

    @Autowired
    private AdKeywordDAO adKeywordDAO;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ReportApiManager reportApiManager;

    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private HeadlinesCampaignDAO headlinesCampaignDAO;

    @Autowired
    private HeadlinesAdDAO headlinesAdDAO;

    @Autowired
    private CustomizeFunctionManager customizeFunctionManager;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;

    @ReloadableProperty("crm.url")
    private String crmUrl;

    @ReloadableProperty("keyword.permits.perSecond")
    private Long keywordPermitsPerSecond;

    @Autowired
    private CrmMetadataManager crmMetadataManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private AdObjectFieldMappingDAO adObjectFieldMappingDAO;
    @Autowired
    private CampaignApiManager campaignApiManager;

    @Autowired
    private BaiduProjectFeedDAO baiduProjectFeedDAO;

    @Autowired
    private BaiduCampaignFeedDAO baiduCampaignFeedDAO;

    @Autowired
    private BaiduAdGroupFeedDAO baiduAdGroupFeedDAO;

    @Autowired
    private AdGroupDAO adGroupDAO;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;

    @Autowired
    private MarketingEventManager marketingEventManager;

    private static final String KEYWORD_SERVING_RATE_LIMIT_KEY = "limit-yxt-keyword-serving";

    private final int batchCreateKeywordServingSize = 10;


    public int queryCrmObjectTotalDataCountByDate(String ea, String marketingApiName, Date date) {
        ControllerListArg arg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        searchQuery.addFilter("launch_date", Lists.newArrayList(String.valueOf(date.getTime())), FilterOperatorEnum.EQ);
        arg.setObjectDescribeApiName(marketingApiName);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        arg.setIncludeButtonInfo(false);

        arg.setSearchQuery(searchQuery);

        int ei = eieaConverter.enterpriseAccountToId(ea);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(ei, -10000), marketingApiName, arg);
        return total == null ? 0 : total;
    }

    /**
     * 调用自定义函数 将第三方线索数据映射成crm线索对象对应的字段
     *
     * @param ea          企业账号
     * @param objectData  第三方线索数据
     * @param funcApiName 自定义函数名称
     * @return 映射好的线索数据
     */
    public Map<String, Object> syncLeadCallFunc(String ea, ObjectData objectData, String funcApiName) {
        ExecuteCustomizeFunctionArg executeCustomizeFunctionArg = new ExecuteCustomizeFunctionArg();
        List<ExecuteCustomizeFunctionArg.Parameters> parametersList = Lists.newArrayList();
        executeCustomizeFunctionArg.setApiName(funcApiName);
        executeCustomizeFunctionArg.setParameters(parametersList);
        ExecuteCustomizeFunctionArg.Parameters parameters = new ExecuteCustomizeFunctionArg.Parameters();
        parameters.setName(CustomizeFunctionConstants.ENROLL_DATA_NAME);
        parameters.setType(CustomizeFunctionConstants.PARAMETERS_TYPE.get("Map"));
        parameters.setValue(objectData);
        parametersList.add(parameters);

        Object result = customizeFunctionManager.executeCustomizeFunction(GsonUtil.getGson().toJson(executeCustomizeFunctionArg), ea, -10000);
        if (result == null) {
            log.warn("CustomizeFormDataServiceImpl.executeEnrollCustomizeFunction result is null arg:{}", executeCustomizeFunctionArg);
            return null;
        }
        return GsonUtil.getGson().fromJson(GsonUtil.getGson().toJson(result), new TypeToken<Map>() {
        }.getType());
    }

    public com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingEventObjByCampaign(String ea,
                                                                                                          String userName,
                                                                                                          String campaignName,
                                                                                                          String source,
                                                                                                          AdObjectFieldMappingEntity marketingEventObjMappingEntity,
                                                                                                          String parentMarketingEventId, Long campaignId, String advertisingType) {
        Map<String, Object> params = new HashMap<>();
        List<String> createByList = Lists.newArrayList();
        AdSourceEnum sourceEnum = AdSourceEnum.getBySource(source);
        if (sourceEnum == null) {
            return null;
        }

        Date date = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, 1);// 把日期往后增加一年.
        date = calendar.getTime();
        Long endTime = date.getTime();

        createByList.add("-10000");
        int ei = eieaConverter.enterpriseAccountToId(ea);
        params.put(CrmV2MarketingEventFieldEnum.BEGIN_TIME.getFieldName(), System.currentTimeMillis());
        params.put(CrmV2MarketingEventFieldEnum.END_TIME.getFieldName(), endTime);
        params.put(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), sourceEnum.getValue());
        params.put(CrmV2MarketingEventFieldEnum.EVENT_TYPE.getFieldName(), MarketingEventEnum.AD_MARKETING.getEventType());
        if (StringUtils.isNotEmpty(userName)) {
            params.put(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName(), userName);
        }
        if (StringUtils.isNotBlank(parentMarketingEventId)) {
            params.put(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName(), parentMarketingEventId);
        }
        if (StringUtils.isNotBlank(advertisingType)) {
            params.put(CrmV2MarketingEventFieldEnum.ADVERTISING_TYPE.getFieldName(), advertisingType);
        }
        if (campaignId != null) {
            params.put("ad_campaign_id", String.valueOf(campaignId));
        }
        params.put("created_by", createByList);
        params.put("status", 1);
        if (marketingEventObjMappingEntity != null) {
            for (FieldMappings.FieldMapping fieldMapping : marketingEventObjMappingEntity.getCrmDataMapping()) {
                if (StringUtils.equals(fieldMapping.getMankeepFieldName(), "campaignName")) {
                    params.put(fieldMapping.getCrmFieldName(), campaignName);
                } else {
                    params.put(fieldMapping.getCrmFieldName(), fieldMapping.getDefaultValue());
                }
            }
        } else {
            params.put(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), campaignName);
        }

        ActionAddArg arg = new ActionAddArg();
        ObjectData data = ObjectData.convert(params);
        data.setOwner(-10000);
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), false, arg);
        } catch (Exception e) {
            log.error("createMarketingEventObjByCampaign Exception ea: {} arg:{}", ea, arg, e);
            return null;
        }
    }


    public Page<ObjectData> queryCrmMarketingKeyword(String ea, List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return null;
        }
        names = names.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(names)) {
            return null;
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("name", Lists.newArrayList(names), FilterOperatorEnum.IN);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        return crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_KEYWORD.getName(), searchQuery);
    }

    public com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createMarketingKeywordObj(String ea, String keyword, int owner) {
        Map<String, Object> params = new HashMap<>();
        List<String> createByList = Lists.newArrayList();
        createByList.add(String.valueOf(SuperUserConstants.USER_ID));
        int ei = eieaConverter.enterpriseAccountToId(ea);
        params.put(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), keyword);
        params.put("created_by", createByList);
        params.put("status", 1);
        ActionAddArg arg = new ActionAddArg();
        ObjectData data = ObjectData.convert(params);
        data.setOwner(owner);
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_KEYWORD.getName(), false, arg);
        } catch (Exception e) {
            log.warn("CrmManager.createData Exception ea: {} keyword:{}, e:", ea, keyword, e);
            return null;
        }
    }

    public Page<ObjectData> queryCrmMarketingKeywordPlan(String ea, String marketingEventId, List<String> marketingKeywordIds, Long keywordId, int size, boolean isSyncAdGroupDimensionality) {
        if (CollectionUtils.isEmpty(marketingKeywordIds) || StringUtils.isEmpty(marketingEventId)) {
            log.info("queryCrmMarketingKeywordPlan retun null marketingEventId:{} marketingEventId:{}", marketingEventId, marketingKeywordIds);
            return null;
        }
        SearchQuery searchQuery = new SearchQuery();
        if (isSyncAdGroupDimensionality) {
            searchQuery.addFilter("sub_marketing_event_id", Lists.newArrayList(marketingEventId), FilterOperatorEnum.EQ);
            searchQuery.addFilter("marketing_keyword_id", marketingKeywordIds, FilterOperatorEnum.IN);
        } else {
            searchQuery.addFilter("marketing_event_id", Lists.newArrayList(marketingEventId), FilterOperatorEnum.EQ);
            searchQuery.addFilter("sub_marketing_event_id", Lists.newArrayList(""), FilterOperatorEnum.IS);
            searchQuery.addFilter("marketing_keyword_id", marketingKeywordIds, FilterOperatorEnum.IN);
        }
        if (keywordId != null) {
            searchQuery.addFilter("out_platform_data_id", Lists.newArrayList(String.valueOf(keywordId)), FilterOperatorEnum.EQ);
        }
        searchQuery.setLimit(size);
        searchQuery.setOffset(0);
        return crmV2Manager.getList(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), searchQuery);
    }

    public Page<ObjectData> queryCrmMarketingKeywordPlan(String ea, List<String> marketingEventIdList, List<String> marketingKeywordIds) {
        if (CollectionUtils.isEmpty(marketingKeywordIds) || CollectionUtils.isEmpty(marketingEventIdList)) {
            log.info("queryCrmMarketingKeywordPlan retun null marketingEventId:{} marketingEventId:{}", marketingEventIdList, marketingKeywordIds);
            return null;
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("marketing_event_id", marketingEventIdList, FilterOperatorEnum.IN);
        searchQuery.addFilter("marketing_keyword_id", marketingKeywordIds, FilterOperatorEnum.IN);
        searchQuery.setLimit(marketingEventIdList.size() + marketingKeywordIds.size());
        searchQuery.setOffset(0);
        return crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), searchQuery);
    }


    public com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createCrmMarketingKeywordPlan(String ea, String username, String marketingEventId, String subMarketingEventId,
                                                                                                      String marketingEventName, String marketingKeywordId, String marketingKeywordName,
                                                                                                      Long keywordId) {
        Map<String, Object> params = new HashMap<>();
        List<String> createByList = Lists.newArrayList();
        createByList.add(String.valueOf(SuperUserConstants.USER_ID));
        int ei = eieaConverter.enterpriseAccountToId(ea);
        params.put("name", marketingEventName + "-" + marketingKeywordName);
        if (StringUtils.isNotBlank(marketingEventId)) {
            params.put("marketing_event_id", marketingEventId);
        }
        if (StringUtils.isNotBlank(subMarketingEventId)) {
            params.put("sub_marketing_event_id", subMarketingEventId);
        }
        params.put("marketing_keyword_id", marketingKeywordId);
        params.put("created_by", createByList);
        params.put("status", 1);
        if (StringUtils.isNotEmpty(username)) {
            params.put("advertising_account", username);
        }
        if (keywordId != null) {
            params.put("out_platform_data_id", String.valueOf(keywordId));
        }
        ActionAddArg arg = new ActionAddArg();
        ObjectData data = ObjectData.convert(params);
        data.setOwner(adCommonManager.getAdObjectDataOwner(ea));
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), false, arg);
        } catch (Exception e) {
            log.warn("CrmManager.createData Exception , ea: {} marketingEventId: {} keywordId: {}", ea, marketingEventId, marketingKeywordId, e);
            return null;
        }
    }

    public Map<String, ObjectData> queryCrmMarketingEventByNames(String ea, List<String> nameList, String source) {
        Map<String, ObjectData> marketingEventNameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(nameList)) {
            return marketingEventNameMap;
        }
        AdSourceEnum sourceEnum = AdSourceEnum.getBySource(source);
        if (sourceEnum == null) {
            return marketingEventNameMap;
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(MarketingEventFieldContants.EVENT_TYPE, Lists.newArrayList(MarketingEventEnum.AD_MARKETING.getEventType()), FilterOperatorEnum.EQ);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), Lists.newArrayList(sourceEnum.getValue()), FilterOperatorEnum.EQ);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), nameList, FilterOperatorEnum.IN);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        Page<ObjectData> objectDataPage = crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
        if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
            List<ObjectData> objectDataList = objectDataPage.getDataList();
            marketingEventNameMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getName, e -> e, (v1, v2) -> v2));
        }
        return marketingEventNameMap;
    }

    public boolean checkIsExistMarketingName(String ea, String name) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), Lists.newArrayList(name), FilterOperatorEnum.EQ);
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        Page<ObjectData> page = crmV2Manager.getList(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
        return page != null && CollectionUtils.isNotEmpty(page.getDataList());
    }


    public Page<ObjectData> queryCrmMarketingEventByNames(List<AdCampaignEntity> campaignData, String source) {
        if (CollectionUtils.isEmpty(campaignData)) {
            return null;
        }

        AdSourceEnum sourceEnum = AdSourceEnum.getBySource(source);
        if (sourceEnum == null) {
            return null;
        }

        SearchQuery searchQuery = new SearchQuery();
        List<String> names = campaignData.stream().map(AdCampaignEntity::getCampaignName).collect(Collectors.toList());
        searchQuery.addFilter(MarketingEventFieldContants.EVENT_TYPE, Lists.newArrayList(MarketingEventEnum.AD_MARKETING.getEventType()), FilterOperatorEnum.EQ);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), Lists.newArrayList(sourceEnum.getValue()), FilterOperatorEnum.EQ);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), names, FilterOperatorEnum.IN);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        return crmV2Manager.getList(campaignData.get(0).getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
    }

    public List<ObjectData> queryCrmMarketingEventByNames(String ea, List<String> nameList) {
        if (CollectionUtils.isEmpty(nameList)) {
            return Lists.newArrayList();
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), nameList, FilterOperatorEnum.IN);
        searchQuery.setLimit(nameList.size());
        searchQuery.setOffset(0);
        Page<ObjectData> page = crmV2Manager.getList(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
        return page != null && page.getDataList() != null ? page.getDataList() : Lists.newArrayList();
    }

    @FilterLog
    public void batchSyncCampaignToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<AdCampaignEntity> campaignDataList, String source) {
        if (CollectionUtils.isEmpty(campaignDataList)) {
            log.warn("campaign list is empty, ea: {} source: {} campaignList: {}", ea, source, campaignDataList);
            return;
        }
        List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList = Lists.newArrayList();
        for (AdCampaignEntity adCampaignEntity : campaignDataList) {
            AdSyncMarketingEventObjBO adSyncMarketingEventObjBO = BeanUtil.copy(adCampaignEntity, AdSyncMarketingEventObjBO.class);
            adSyncMarketingEventObjBO.setAdDataId(adCampaignEntity.getCampaignId());
            adSyncMarketingEventObjBO.setAdDataName(adCampaignEntity.getCampaignName());
            adSyncMarketingEventObjList.add(adSyncMarketingEventObjBO);
            if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source) || AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
                adSyncMarketingEventObjBO.setAdvertisingType(AdvertisingTypeEnum.SO_AD.getValue());
            } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source) && StringUtils.isNotBlank(adCampaignEntity.getAdType())) {
                String adType = adCampaignEntity.getAdType();
                String advertisingType = HeadlinesAdTypeEnum.SEARCH.getValue().equals(adType) ? AdvertisingTypeEnum.SO_AD.getValue() : AdvertisingTypeEnum.ALL_AD.getValue();
                adSyncMarketingEventObjBO.setAdvertisingType(advertisingType);
            }
        }
        List<AdSyncMarketingEventObjBO> needUpdateDbAdDataList = batchSyncAdToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, source);
        log.info("batchSyncCampaignToMarketingEventObjV2, ea: {} campaignDataList.size: {} needUpdateDbAdDataList.size: {}", ea, campaignDataList.size(), needUpdateDbAdDataList.size());
        if (CollectionUtils.isEmpty(needUpdateDbAdDataList)) {
            return;
        }
        List<AdCampaignEntity> updateDbAdCampaignEntityList = Lists.newArrayList();
        for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
            AdCampaignEntity adCampaignEntity = new AdCampaignEntity();
            adCampaignEntity.setId(adSyncMarketingEventObjBO.getId());
            adCampaignEntity.setMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
            updateDbAdCampaignEntityList.add(adCampaignEntity);
        }
        if (StringUtils.isNotEmpty(source) && StringUtils.equals(AdSourceEnum.SOURCE_JULIANG.getSource(), source)) {
            headlinesCampaignDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), updateDbAdCampaignEntityList);
        } else if (StringUtils.isNotEmpty(source) && StringUtils.equals(AdSourceEnum.SOURCE_BAIDU.getSource(), source)) {
            baiduCampaignDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), updateDbAdCampaignEntityList);
        } else if (StringUtils.isNotEmpty(source) && StringUtils.equals(AdSourceEnum.SOURCE_TENCETN.getSource(), source)) {
            tencentCampaignDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), updateDbAdCampaignEntityList);
        }
    }

    /**
     * 将广告计划/广告组同步到市场活动对象
     * 1. 如果广告计划或者广告组有重名的，将广告平台的id拼接到名字上，在创建市场活动
     * 2. 如果拼接了市场活动还有重名的，在拼接上广告账号的名字
     *
     * @return 需要更新市场活动id到本地数据库的数据
     */
    public List<AdSyncMarketingEventObjBO> batchSyncAdToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList, String source) {
        log.info("开始同步广告数据到市场活动，ea: {} source: {} adAccountEntity: {} adSyncMarketingEventObjList size: {}", ea, source, adAccountEntity, adSyncMarketingEventObjList.size());
        // 找出所有广告平台同名的广告计划/广告组数量
        List<String> adDataNameList = adSyncMarketingEventObjList.stream().map(AdSyncMarketingEventObjBO::getAdDataName).distinct().collect(Collectors.toList());
        Map<String, Integer> nameToCountMap = adCommonManager.getAdSameNameCount(ea, adDataNameList);
        log.info("重名数量, ea: {} nameToCountMap: {}", ea, nameToCountMap);
        // 需要同步到市场活动的广告数据
        List<AdSyncMarketingEventObjBO> needSyncAdDataList = Lists.newArrayList();
        // 需要更新市场活动对象的数据
        List<AdSyncMarketingEventObjBO> needUpdateMarketingEventObjList = Lists.newArrayList();
        Set<String> existMarketingEventId = Sets.newHashSet();
        for (AdSyncMarketingEventObjBO adSyncMarketingEventObj : adSyncMarketingEventObjList) {
            String name = adSyncMarketingEventObj.getAdDataName();
            int count = nameToCountMap.getOrDefault(name, 1);
            if (count <= 1) {
                if (StringUtils.isBlank(adSyncMarketingEventObj.getMarketingEventId())) {
                    // 没有市场活动id的，需要新建市场活动id
                    needSyncAdDataList.add(adSyncMarketingEventObj);
                } else {
                    // 这里去更新市场活动
                    needUpdateMarketingEventObjList.add(adSyncMarketingEventObj);
                }
                continue;
            }
            // 有重名的，将名字改写,用于更新和创建市场活动名字
            String marketingEventId = adSyncMarketingEventObj.getMarketingEventId();
            adSyncMarketingEventObj.setAdDataName(adSyncMarketingEventObj.getAdDataName() + "-" + adSyncMarketingEventObj.getAdDataId());
            if (StringUtils.isBlank(marketingEventId)) {
                // 没有市场活动id的，需要新建市场活动id
                needSyncAdDataList.add(adSyncMarketingEventObj);
            } else {
                if (existMarketingEventId.contains(marketingEventId)) {
                    // 如果已经包含key,说明一个市场活动对应多个广告计划,是脏数据,这里一并处理掉
                    needSyncAdDataList.add(adSyncMarketingEventObj);
                    log.info("一个市场活动对应多个广告数据, ea: {} marketingEventId: {} adSyncMarketingEventObj: {}", ea, marketingEventId, adSyncMarketingEventObj);
                } else {
                    existMarketingEventId.add(marketingEventId);
                    needUpdateMarketingEventObjList.add(adSyncMarketingEventObj);
                }
            }
        }
        // 根据名字再去查一遍paas,虽然上面有重复的广告计划/广告组已经修改过名字了, 这里还需要手动去查一下paas,以防万一！
        List<String> checkPaasSameNameList = Lists.newArrayList();
        needSyncAdDataList.forEach(e -> checkPaasSameNameList.add(e.getAdDataName()));
        needUpdateMarketingEventObjList.forEach(e -> checkPaasSameNameList.add(e.getAdDataName()));
        List<ObjectData> alreadyExistPaasMarketingEventNameList = queryCrmMarketingEventByNames(ea, checkPaasSameNameList);
        Map<String, ObjectData> alreadyExistPaasMarketingEventNameMap = alreadyExistPaasMarketingEventNameList.stream().collect(Collectors.toMap(ObjectData::getName, e -> e, (v1, v2) -> v1));
        // 开始创建新的市场活动
        List<AdSyncMarketingEventObjBO> updateDbAdDataList = Lists.newArrayList();
        AdObjectFieldMappingEntity marketingEventObjMappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        log.info("需要同步的广告数据, ea: {} size: {}", ea, needSyncAdDataList.size());
        for (AdSyncMarketingEventObjBO needSyncAdData : needSyncAdDataList) {
            String adDataName = needSyncAdData.getAdDataName();
            ObjectData existMarketingEventObj = alreadyExistPaasMarketingEventNameMap.get(adDataName);
            if (existMarketingEventObj != null) {
                // 如果已经有重名的市场活动，判断该市场活动是否属于该广告数据
                String adCampaignId = existMarketingEventObj.getString("ad_campaign_id");
                String adAccount = existMarketingEventObj.getString(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName());
                if ((StringUtils.isNotBlank(adCampaignId) && adCampaignId.equals(needSyncAdData.getAdDataId().toString())) || (StringUtils.isNotBlank(adAccount) && adAccount.equals(adAccountEntity.getUsername()))) {
                    log.info("pass已经存在该市场活动，关联，ea: {} marketingEventId: {} needSyncAdData: {}", ea, existMarketingEventObj.getId(), needSyncAdData);
                    needSyncAdData.setMarketingEventId(existMarketingEventObj.getId());
                    updateDbAdDataList.add(needSyncAdData);
                    continue;
                }
                log.info("paas已经有重名, 修改名字, ea: {} needSyncAdData: {}", ea, needSyncAdData);
                // 如果paas已经有重名了，修改名字，如果paas的名字包含campaignId(几乎没概率吧),那就在拼装账号的名字
                String newName = adDataName.contains(String.valueOf(needSyncAdData.getAdDataId())) ? adDataName + "-" + adAccountEntity.getUsername() : adDataName + "-" + needSyncAdData.getAdDataId();
                needSyncAdData.setAdDataName(newName);
                adDataName = newName;
            }
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = createMarketingEventObjByCampaign(ea, adAccountEntity.getUsername(), adDataName, source, marketingEventObjMappingEntity, needSyncAdData.getParentMarketingEventId(), needSyncAdData.getAdDataId(), needSyncAdData.getAdvertisingType());
            log.info("同步广告计划到市场活动, needSyncAdData : {} result:{}", needSyncAdData, result);
            if (result != null && result.isSuccess() && result.getData() != null && result.getData().getObjectData() != null) {
                String marketingEventId = result.getData().getObjectData().getId();
                needSyncAdData.setMarketingEventId(marketingEventId);
                updateDbAdDataList.add(needSyncAdData);
            }
        }
        // 开始更新市场活动
        log.info("需要更新的广告计划, ea: {} size: {}", ea, needUpdateMarketingEventObjList.size());
        for (AdSyncMarketingEventObjBO needUpdateMarketingEventObj : needUpdateMarketingEventObjList) {
            String adDataName = needUpdateMarketingEventObj.getAdDataName();
            ObjectData objectData = alreadyExistPaasMarketingEventNameMap.get(adDataName);
            if (objectData != null && !objectData.getId().equals(needUpdateMarketingEventObj.getMarketingEventId())) {
                log.info("更新时paas已经有重名且id不等, ea: {} needUpdateMarketingEventObj: {}", ea, needUpdateMarketingEventObj);
                // 更新的时候，如果paas已经有重名且id不等，修改名字，如果paas的名字包含campaignId(几乎没概率吧),那就在拼装账号的名字
                String newName = adDataName.contains(String.valueOf(needUpdateMarketingEventObj.getAdDataId())) ? adDataName + "-" + adAccountEntity.getUsername() : adDataName + "-" + needUpdateMarketingEventObj.getAdDataId();
                needUpdateMarketingEventObj.setAdDataName(newName);
            }
            updateMarketingEventObj(ea, needUpdateMarketingEventObj, adAccountEntity);
        }
        return updateDbAdDataList;
    }

    @FilterLog
    public void batchSyncAdGroupToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<AdvertiserAdEntity> advertiserAdEntityList, String source) {
        if (CollectionUtils.isEmpty(advertiserAdEntityList)) {
            return;
        }
        Map<Long, HeadlinesCampaignEntity> headlinesCampaignIdToEntityMap = Maps.newHashMap();
        if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source)) {
            List<Long> headlinesCampaignIdList = advertiserAdEntityList.stream().map(AdvertiserAdEntity::getCampaignId).distinct().collect(Collectors.toList());
            List<HeadlinesCampaignEntity> headlinesCampaignEntityList = headlinesCampaignDAO.queryCampaignListByCampaignIds(ea, adAccountEntity.getId(), source, headlinesCampaignIdList, TypeEnum.HEADLINES_PROJECT.getCode());
            headlinesCampaignEntityList.forEach(e -> headlinesCampaignIdToEntityMap.put(e.getCampaignId(), e));
        }
        List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList = Lists.newArrayList();
        for (AdvertiserAdEntity advertiserAdEntity : advertiserAdEntityList) {
            AdSyncMarketingEventObjBO adSyncMarketingEventObjBO = BeanUtil.copy(advertiserAdEntity, AdSyncMarketingEventObjBO.class);
            adSyncMarketingEventObjBO.setAdDataId(advertiserAdEntity.getAdId());
            adSyncMarketingEventObjBO.setAdDataName(advertiserAdEntity.getAdName());
            adSyncMarketingEventObjBO.setMarketingEventId(advertiserAdEntity.getSubMarketingEventId());
            adSyncMarketingEventObjBO.setSource(source);
            adSyncMarketingEventObjBO.setParentAdDataId(advertiserAdEntity.getCampaignId());
            adSyncMarketingEventObjBO.setParentMarketingEventId(advertiserAdEntity.getMarketingEventId());
            if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
                Long campaignId = advertiserAdEntity.getCampaignId();
                String advertisingType = campaignId != null && campaignId == 0 ? AdvertisingTypeEnum.SHOW_AD.getValue() : AdvertisingTypeEnum.SO_AD.getValue();
                adSyncMarketingEventObjBO.setAdvertisingType(advertisingType);
            } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source)) {
                HeadlinesCampaignEntity headlinesCampaignEntity = headlinesCampaignIdToEntityMap.get(advertiserAdEntity.getCampaignId());
                if (headlinesCampaignEntity != null && StringUtils.isNotBlank(headlinesCampaignEntity.getAdType())) {
                    String adType = headlinesCampaignEntity.getAdType();
                    String advertisingType = HeadlinesAdTypeEnum.SEARCH.getValue().equals(adType) ? AdvertisingTypeEnum.SO_AD.getValue() : AdvertisingTypeEnum.ALL_AD.getValue();
                    adSyncMarketingEventObjBO.setAdvertisingType(advertisingType);
                }
            }
            adSyncMarketingEventObjList.add(adSyncMarketingEventObjBO);
        }
        batchSyncSubAdDataToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, null);
    }


    /**
     * typeEnum 为同步的广告的数据类型，目前只有百度的用到 其他的还没用到
     */
    public void batchSyncSubAdDataToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList, TypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(adSyncMarketingEventObjList)) {
            return;
        }
        String source = adAccountEntity.getSource();
        Map<Long, String> parentIdToParentMarketingEventIdMap = getParentAdIdToParentMarketingEventIdMap(ea, adSyncMarketingEventObjList, adAccountEntity, typeEnum);
        for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : adSyncMarketingEventObjList) {
            if (StringUtils.isNotBlank(adSyncMarketingEventObjBO.getParentMarketingEventId())) {
                continue;
            }
            adSyncMarketingEventObjBO.setParentMarketingEventId(parentIdToParentMarketingEventIdMap.get(adSyncMarketingEventObjBO.getParentAdDataId()));
        }
        // 同步广告组到市场活动
        List<AdSyncMarketingEventObjBO> needUpdateDbAdDataList = batchSyncAdToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, adAccountEntity.getSource());
        log.info("batchSyncSubAdDataToMarketingEventObj, ea: {} adSyncMarketingEventObjList.size: {} needUpdateDbAdDataList.size: {}", ea, adSyncMarketingEventObjList.size(), needUpdateDbAdDataList.size());
        Map<String, String> subMarketingEventToParentMarketingEventMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(needUpdateDbAdDataList)) {
            // 更新市场活动到本地数据库、同时更新市场活动的父级市场活动
            Map<Long, Long> adDataIdToParentAdDataIdMap = adSyncMarketingEventObjList.stream().collect(Collectors.toMap(AdSyncMarketingEventObjBO::getAdDataId, AdSyncMarketingEventObjBO::getParentAdDataId, (v1, v2) -> v1));
            if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source)) {
                List<AdvertiserAdEntity> updateDbAdEntityList = Lists.newArrayList();
                for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
                    AdvertiserAdEntity advertiserAdEntity = new AdvertiserAdEntity();
                    advertiserAdEntity.setId(adSyncMarketingEventObjBO.getId());
                    Long parentAdDataId = adDataIdToParentAdDataIdMap.get(adSyncMarketingEventObjBO.getAdDataId());
                    String marketingEventId = parentIdToParentMarketingEventIdMap.get(parentAdDataId);
                    if (StringUtils.isBlank(marketingEventId)) {
                        log.warn("父级市场活动没有同步成功,请关注， ea: {} adSyncMarketingEventObjBO: {}", ea, adSyncMarketingEventObjBO);
                    }
                    advertiserAdEntity.setMarketingEventId(marketingEventId);
                    advertiserAdEntity.setSubMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
                    updateDbAdEntityList.add(advertiserAdEntity);
                    subMarketingEventToParentMarketingEventMap.put(adSyncMarketingEventObjBO.getMarketingEventId(), marketingEventId);
                }
                headlinesAdDAO.batchUpdateSubMarketingEventById(ea, adAccountEntity.getId(), updateDbAdEntityList);
            } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
                List<TencentAdGroupEntity> updateSubMarketingEventIdList = Lists.newArrayList();
                for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
                    TencentAdGroupEntity tencentAdGroupEntity = new TencentAdGroupEntity();
                    tencentAdGroupEntity.setId(adSyncMarketingEventObjBO.getId());
                    Long parentId = adDataIdToParentAdDataIdMap.get(adSyncMarketingEventObjBO.getAdDataId());
                    String marketingEventId = parentIdToParentMarketingEventIdMap.get(parentId);
                    if (StringUtils.isBlank(marketingEventId) && adSyncMarketingEventObjBO.getParentAdDataId() != null && adSyncMarketingEventObjBO.getParentAdDataId() != 0L) {
                        log.warn("父级市场活动没有同步成功,请关注， ea: {} adSyncMarketingEventObjBO: {}", ea, adSyncMarketingEventObjBO);
                    }
                    tencentAdGroupEntity.setMarketingEventId(marketingEventId);
                    tencentAdGroupEntity.setSubMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
                    updateSubMarketingEventIdList.add(tencentAdGroupEntity);
                    subMarketingEventToParentMarketingEventMap.put(adSyncMarketingEventObjBO.getMarketingEventId(), marketingEventId);
                }
                tencentAdGroupDAO.batchUpdateSubMarketingEventById(ea, adAccountEntity.getId(), updateSubMarketingEventIdList);
            } else if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source)) {
                if (typeEnum == TypeEnum.BAIDU_FEED_CAMPAIGN) {
                    List<BaiduCampaignFeedEntity> updateMarketingEventIdList = Lists.newArrayList();
                    for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
                        BaiduCampaignFeedEntity baiduCampaignFeedEntity = new BaiduCampaignFeedEntity();
                        baiduCampaignFeedEntity.setId(adSyncMarketingEventObjBO.getId());
                        baiduCampaignFeedEntity.setEa(ea);
                        baiduCampaignFeedEntity.setMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
                        updateMarketingEventIdList.add(baiduCampaignFeedEntity);
                    }
                    baiduCampaignFeedDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), updateMarketingEventIdList);
                } else if (typeEnum == TypeEnum.BAIDU_FEED_AD_GROUP) {
                    List<BaiduAdGroupFeedEntity> updateMarketingEventIdList = Lists.newArrayList();
                    for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
                        BaiduAdGroupFeedEntity baiduAdGroupFeedEntity = new BaiduAdGroupFeedEntity();
                        baiduAdGroupFeedEntity.setId(adSyncMarketingEventObjBO.getId());
                        baiduAdGroupFeedEntity.setEa(ea);
                        baiduAdGroupFeedEntity.setMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
                        updateMarketingEventIdList.add(baiduAdGroupFeedEntity);
                    }
                    baiduAdGroupFeedDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), updateMarketingEventIdList);
                }  else if (typeEnum == TypeEnum.BAIDU_SEARCH_AD_GROUP) {
                    List<AdGroupEntity> adGroupEntityList = Lists.newArrayList();
                    for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
                        AdGroupEntity adGroupEntity = new AdGroupEntity();
                        adGroupEntity.setId(adSyncMarketingEventObjBO.getId());
                        adGroupEntity.setEa(ea);
                        adGroupEntity.setMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
                        adGroupEntityList.add(adGroupEntity);
                    }
                    adGroupDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), adGroupEntityList);
                }
            }
        }
        // 这里检查一下父级市场活动是否正常，不正常就更新
        Set<Long> alreadyUpdateAdIdSet = needUpdateDbAdDataList.stream().map(AdSyncMarketingEventObjBO::getAdDataId).collect(Collectors.toSet());
        // 需要检查父级市场活动的数据
        List<AdSyncMarketingEventObjBO> needCheckParentList = adSyncMarketingEventObjList.stream().filter(e -> !alreadyUpdateAdIdSet.contains(e.getAdDataId())).collect(Collectors.toList());
        List<Long> adIdList = needCheckParentList.stream().map(AdSyncMarketingEventObjBO::getAdDataId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adIdList)) {
            return;
        }
        List<String> marketingEventIdList = Lists.newArrayList();
        Map<String, TencentAdGroupEntity> marketingEventIdToTencentAdGroupEntity = Maps.newHashMap();
        if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source)) {
            List<HeadlinesAdEntity> headlinesAdEntityList = headlinesAdDAO.queryAdListByAdIds(ea, adAccountEntity.getId(), source, adIdList);
            headlinesAdEntityList.forEach(e -> {
                if (StringUtils.isNotBlank(e.getSubMarketingEventId())) {
                    subMarketingEventToParentMarketingEventMap.put(e.getSubMarketingEventId(), parentIdToParentMarketingEventIdMap.get(e.getCampaignId()));
                    marketingEventIdList.add(e.getSubMarketingEventId());
                }
            });
        } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
            List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryTencentAdGroupList(ea, adAccountEntity.getId(), adIdList);
            tencentAdGroupEntityList.forEach(e -> {
                if (StringUtils.isNotBlank(e.getSubMarketingEventId())) {
                    subMarketingEventToParentMarketingEventMap.put(e.getSubMarketingEventId(), parentIdToParentMarketingEventIdMap.get(e.getCampaignId()));
                    marketingEventIdToTencentAdGroupEntity.put(e.getSubMarketingEventId(), e);
                    marketingEventIdList.add(e.getSubMarketingEventId());
                }
            });
        }
        if (CollectionUtils.isEmpty(marketingEventIdList)) {
            return;
        }
        List<ObjectData> marketingEventObjList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "parent_id"), marketingEventIdList);
        List<ObjectData> needUpdateParentIdObjList = marketingEventObjList.stream().filter(e -> StringUtils.isBlank(e.getString("parent_id"))).collect(Collectors.toList());
        for (ObjectData objectData : needUpdateParentIdObjList) {
            String parentId = subMarketingEventToParentMarketingEventMap.get(objectData.getId());
            tryUpdateParentId(ea, objectData, parentId);
            if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
                // 补刷一下腾讯没有父市场活动的数据
                TencentAdGroupEntity tencentAdGroupEntity = marketingEventIdToTencentAdGroupEntity.get(objectData.getId());
                if (tencentAdGroupEntity != null && StringUtils.isNotBlank(parentId) && !parentId.equals(tencentAdGroupEntity.getMarketingEventId())) {
                    tencentAdGroupDAO.updateMarketingEventId(ea, tencentAdGroupEntity.getId(), parentId);
                }
            }
        }
    }

    private Map<Long, String> getParentAdIdToParentMarketingEventIdMap(String ea, List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList, AdAccountEntity adAccountEntity, TypeEnum typeEnum) {
        List<Long> parentAdIdList = adSyncMarketingEventObjList.stream().map(AdSyncMarketingEventObjBO::getParentAdDataId).distinct().collect(Collectors.toList());
        Map<Long, String> parentAdDataIdToMarketingEventIdMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(parentAdIdList)) {
            return parentAdDataIdToMarketingEventIdMap;
        }
        String source = adAccountEntity.getSource();
        // 获取父级市场活动
        if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
            List<AdCampaignEntity> tencentCampaignEntityList = tencentCampaignDAO.queryCampaignNamesByCampaignIds(ea, parentAdIdList);
            tencentCampaignEntityList.forEach(e -> parentAdDataIdToMarketingEventIdMap.put(e.getCampaignId(), e.getMarketingEventId()));
        } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source)) {
            List<AdCampaignEntity> headlinesAdCampaignEntityList = headlinesCampaignDAO.queryCampaignNamesByCampaignIds(ea, parentAdIdList, source);
            headlinesAdCampaignEntityList.forEach(e -> parentAdDataIdToMarketingEventIdMap.put(e.getCampaignId(), e.getMarketingEventId()));
        } else if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source)) {
            if (typeEnum == TypeEnum.BAIDU_SEARCH_AD_GROUP) {
                List<BaiduCampaignEntity>  baiduCampaignEntityList = baiduCampaignDAO.queryMarketingEventIdNotEmptyCampaignByCampaignByIds(ea, adAccountEntity.getId(), adAccountEntity.getSource(), parentAdIdList);
                baiduCampaignEntityList.forEach(e -> parentAdDataIdToMarketingEventIdMap.put(e.getCampaignId(), e.getMarketingEventId()));
            } else if (typeEnum == TypeEnum.BAIDU_FEED_CAMPAIGN) {
                List<BaiduProjectFeedEntity> baiduProjectFeedEntityList = baiduProjectFeedDAO.getByProjectFeedIdList(ea, adAccountEntity.getId(), parentAdIdList);
                baiduProjectFeedEntityList.forEach(e -> parentAdDataIdToMarketingEventIdMap.put(e.getProjectFeedId(), e.getMarketingEventId()));
            } else if (typeEnum == TypeEnum.BAIDU_FEED_AD_GROUP) {
                List<BaiduCampaignFeedEntity> baiduCampaignFeedEntityList = baiduCampaignFeedDAO.getByCampaignFeedIdList(ea, adAccountEntity.getId(), parentAdIdList);
                baiduCampaignFeedEntityList.forEach(e -> parentAdDataIdToMarketingEventIdMap.put(e.getCampaignFeedId(), e.getMarketingEventId()));
            }
        }
        return parentAdDataIdToMarketingEventIdMap;
    }


    private void tryUpdateParentId(String ea, ObjectData objectData, String latestParentId) {
        String id = objectData.getId();
        String currentParentId = objectData.getString("parent_id");
        if (StringUtils.isBlank(latestParentId) || latestParentId.equals(currentParentId)) {
            return;
        }
        Map<String, Object> forUpdate = Maps.newHashMap();
        forUpdate.put("_id", id);
        forUpdate.put("parent_id", latestParentId);
        Result<ActionEditResult> editResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), forUpdate);
        log.info("更新父级市场活动, ea: {} id: {} arg: {} result: {}", ea, id, forUpdate, editResult);
    }

    private void updateMarketingEventObj(String ea, AdSyncMarketingEventObjBO adSyncMarketingEventObj, AdAccountEntity adAccountEntity) {
        String id = adSyncMarketingEventObj.getMarketingEventId();
        ObjectData marketingEventObj = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), id);
        if (marketingEventObj == null) {
            log.warn("找不到市场活动, ea: {} id: {} adSyncMarketingEventObj: {}", ea, id, adSyncMarketingEventObj);
            return;
        }
        // 校验市场活动的名字
        String name = marketingEventObj.getName();
        Map<String, Object> forUpdate = Maps.newHashMap();
        if (!adSyncMarketingEventObj.getAdDataName().equals(name)) {
            boolean isExists = checkIsExistMarketingName(ea, adSyncMarketingEventObj.getAdDataName());
            String newName = isExists ? adSyncMarketingEventObj.getAdDataName() + "-" + adSyncMarketingEventObj.getAdDataId() : adSyncMarketingEventObj.getAdDataName();
            forUpdate.put("name", newName);
        }
        // 广告的id
        String currentParentId = marketingEventObj.getString(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName());
        String newMarketingEventId = adSyncMarketingEventObj.getParentMarketingEventId();
        if (StringUtils.isNotBlank(newMarketingEventId) && !newMarketingEventId.equals(currentParentId)) {
            forUpdate.put(CrmV2MarketingEventFieldEnum.PARENT_ID.getFieldName(), newMarketingEventId);
        }
        // 广告的id
        String adCampaignId = marketingEventObj.getString("ad_campaign_id");
        if (StringUtils.isBlank(adCampaignId) || !adCampaignId.equals(adSyncMarketingEventObj.getAdDataId().toString())) {
            forUpdate.put("ad_campaign_id", String.valueOf(adSyncMarketingEventObj.getAdDataId()));
        }
        // 广告渠道字段
        String originSourceValue = String.valueOf(marketingEventObj.get(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName()));
        String newSourceValue = AdSourceEnum.getValueBySource(adSyncMarketingEventObj.getSource());
        if (StringUtils.isBlank(originSourceValue) || !originSourceValue.equals(newSourceValue)) {
            forUpdate.put(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), newSourceValue);
        }
        // 广告账号字段
        String adAccount = marketingEventObj.getString(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName());
        if (StringUtils.isBlank(adAccount) || !adAccount.equals(adAccountEntity.getUsername())) {
            forUpdate.put(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName(), adAccountEntity.getUsername());
        }
        String advertisingType = marketingEventObj.getString(CrmV2MarketingEventFieldEnum.ADVERTISING_TYPE.getFieldName());
        if (StringUtils.isNotBlank(adSyncMarketingEventObj.getAdvertisingType()) && !adSyncMarketingEventObj.getAdvertisingType().equals(advertisingType)) {
            forUpdate.put(CrmV2MarketingEventFieldEnum.ADVERTISING_TYPE.getFieldName(), adSyncMarketingEventObj.getAdvertisingType());
        }
        Result<ActionEditResult> updateResult = null;
        if (MapUtils.isNotEmpty(forUpdate)) {
            forUpdate.put("_id", marketingEventObj.getId());
            updateResult = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), forUpdate, false, false);
            log.info("更新市场活动， ea: {} adSyncMarketingEventObj: {} arg: {} result: {}", ea, adSyncMarketingEventObj, forUpdate, updateResult);
        }
    }

    public void syncAdKeywordToMarketingKeywordObj(String ea, String adAccountId, String source) {
        int pageSize = 100;
        String lastId;
        List<AdKeywordEntity> adKeywordEntityList = adKeywordDAO.scanNotSyncToObjData(ea, adAccountId, source, null, pageSize);
        while (CollectionUtils.isNotEmpty(adKeywordEntityList)) {
            if (!adCommonManager.isSyncAdKeyword(ea)) {
                return;
            }
            batchSyncKeywordToMarketingKeywordObj(ea, adAccountId, adKeywordEntityList);
            lastId = adKeywordEntityList.get(adKeywordEntityList.size() - 1).getId();
            adKeywordEntityList = adKeywordDAO.scanNotSyncToObjData(ea, adAccountId, source, lastId, pageSize);
        }
    }


    public void batchSyncKeywordToMarketingKeywordObj(String ea, String adAccountId, List<AdKeywordEntity> adKeywordEntityList) {
        if (CollectionUtils.isEmpty(adKeywordEntityList)) {
            return;
        }

        Set<String> names = adKeywordEntityList.stream().map(AdKeywordEntity::getKeyword).collect(Collectors.toSet());
        Page<ObjectData> objectDataPage = queryCrmMarketingKeyword(ea, new ArrayList<>(names));
        Map<String, String> marketingKeywordNameMap = null;
        if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
            List<ObjectData> objectDataList = objectDataPage.getDataList();
            marketingKeywordNameMap = objectDataList.stream().collect(Collectors.toMap(ObjectData::getName, ObjectData::getId, (v1, v2) -> v2));
        }
        if (marketingKeywordNameMap == null) {
            marketingKeywordNameMap = new HashMap<>();
        }
        List<AdKeywordEntity> syncList = Lists.newArrayList();
        Set<String> currentWords = new HashSet<>();
        for (AdKeywordEntity keywordEntity : adKeywordEntityList) {
            if (keywordEntity.getMarketingKeywordId() != null) {
                continue;
            }
            if (marketingKeywordNameMap == null || marketingKeywordNameMap.get(keywordEntity.getKeyword()) == null) {
                if (!currentWords.contains(keywordEntity.getKeyword())) {
                    currentWords.add(keywordEntity.getKeyword());
                    syncList.add(keywordEntity);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(syncList)) {
            int owner = adCommonManager.getAdObjectDataOwner(ea);
            for (AdKeywordEntity keywordEntity : syncList) {
                if (!adCommonManager.isSyncAdKeyword(ea)) {
                    break;
                }
                getKeywordRRateLimiter(ea).acquire();
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = createMarketingKeywordObj(ea, keywordEntity.getKeyword(), owner);
                if (result != null && result.isSuccess() && result.getData() != null && result.getData().getObjectData() != null) {
                    String marketingKeywordId = (String) result.getData().getObjectData().get("_id");
                    marketingKeywordNameMap.put(keywordEntity.getKeyword(), marketingKeywordId);
                } else {
                    log.info("createMarketingKeywordObj error keywordEntity:{}, result: {}", keywordEntity, result);
                }
            }
        }

        for (AdKeywordEntity keywordEntity : adKeywordEntityList) {
            if (marketingKeywordNameMap.get(keywordEntity.getKeyword()) != null) {
                keywordEntity.setMarketingKeywordId(marketingKeywordNameMap.get(keywordEntity.getKeyword()));
            }
        }
        //更新本地marketingKeywordId
        adKeywordDAO.batchUpdateMarketingKeywordId(ea, adAccountId, adKeywordEntityList);
    }

    public int queryCrmObjectTotalDataCount(String ea, String marketingApiName) {
        ControllerListArg arg = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        arg.setObjectDescribeApiName(marketingApiName);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        arg.setIncludeButtonInfo(false);
        arg.setSearchQuery(searchQuery);
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(ei, -10000), marketingApiName, arg);
        return total == null ? 0 : total;
    }

    /**
     * 通过关键词来同步关键词投放明细
     *
     * @param adAccountEntity 广告账户信息
     * @param source          来源
     * @param date            日期
     */
    public void syncMarketingTermServingLinesDataByKeyword(AdAccountEntity adAccountEntity, String source, String date) {
        int pageSize = 1000;
        Long lastKeywordId = null;
        String ea = adAccountEntity.getEa();
        while (true) {
            if (!adCommonManager.isSyncAdKeyword(ea)) {
                return;
            }
            List<AdKeywordEntity> adKeywordEntities = adKeywordDAO.pageQueryRefreshServicePlanKeyword(ea, adAccountEntity.getId(), lastKeywordId, adAccountEntity.getSource(), pageSize);
            if (CollectionUtils.isEmpty(adKeywordEntities)) {
                break;
            }
            syncTermServingLinesByKeywordV2(adAccountEntity, source, adKeywordEntities, date);
            int size = adKeywordEntities.size();
            lastKeywordId = adKeywordEntities.get(size - 1).getKeywordId();
        }
    }


    public List<TencentKeywordDataResult.TencentKeywordData> getTencentKeywordData(AdAccountEntity adAccountEntity, Map<Long, Set<Long>> adGroupKeywordMap, String date) {
        if (MapUtils.isEmpty(adGroupKeywordMap)) {
            return Lists.newArrayList();
        }
        List<Long> adGroupIdList = Lists.newArrayList(adGroupKeywordMap.keySet());
        List<TencentAdGroupEntity> tencentAdGroupEntityList = tencentAdGroupDAO.queryTencentAdGroupList(adAccountEntity.getEa(), adAccountEntity.getId(), adGroupIdList);
        Map<Long, TencentAdGroupEntity> adGroupIdToEntityMap = tencentAdGroupEntityList.stream().collect(Collectors.toMap(TencentAdGroupEntity::getAdgroupId, e -> e, (v1, v2) -> v1));
        List<TencentKeywordDataResult.TencentKeywordData> keywordDataList = Lists.newArrayList();
        for(Map.Entry<Long, Set<Long>> item : adGroupKeywordMap.entrySet()) {
            List<Long> keywordIds = Lists.newArrayList(item.getValue());
            PageUtil<Long> keywordPage = new PageUtil<>(keywordIds, 100);
            for (int i = 0; i < keywordPage.getPageCount(); i++) {
                List<Long> pageKeywordIds = keywordPage.getPagedList(i + 1);
                Long adGroupId = item.getKey();
                TencentAdGroupEntity tencentAdGroupEntity = adGroupIdToEntityMap.get(adGroupId);
                if (tencentAdGroupEntity == null) {
                    break;
                }
                TencentAdResult<TencentKeywordDataResult> tencentKeywordDataResult;
                Long campaignId = tencentAdGroupEntity.getCampaignId();
                // 腾讯的报表数据新版旧版是互相隔离的，得单独去查
                if (campaignId == null || campaignId <= 0L) {
                    tencentKeywordDataResult = campaignApiManager.getTencentBidWordDataV3(adAccountEntity.getAccountId(), adGroupId, pageKeywordIds, adAccountEntity.getToken(), 1, 100, date, date);
                } else {
                    tencentKeywordDataResult = campaignApiManager.getTencentBidWordDataV1(adAccountEntity.getAccountId(), adGroupId, pageKeywordIds, adAccountEntity.getToken(), 1, 100, date, date);
                }
                if (tencentKeywordDataResult == null || !tencentKeywordDataResult.isSuccess() || tencentKeywordDataResult.getData() == null || CollectionUtils.isEmpty(tencentKeywordDataResult.getData().getList())) {
                    continue;
                }
                keywordDataList.addAll(tencentKeywordDataResult.getData().getList());
            }
        }
        // 去这里去重一下，谨防腾讯新旧接口又融合了
        List<TencentKeywordDataResult.TencentKeywordData> finalKeywordDataList = Lists.newArrayList();
        Set<Long> keywordIdSet = Sets.newHashSet();
        for (TencentKeywordDataResult.TencentKeywordData tencentKeywordData : keywordDataList) {
            if (keywordIdSet.contains(tencentKeywordData.getBidword_id())) {
                continue;
            }
            keywordIdSet.add(tencentKeywordData.getBidword_id());
            finalKeywordDataList.add(tencentKeywordData);
        }
        return finalKeywordDataList;
    }

    /**
     * 分页同步关键词投放明细
     *
     * @param adAccountEntity   广告账户信息
     * @param source            广告数据来源
     * @param adKeywordEntities 关键词
     * @param date              日期
     */
    public void syncTermServingLinesByKeyword(AdAccountEntity adAccountEntity, String source, List<AdKeywordEntity> adKeywordEntities, String date) {
        List<Long> campaignIds = adKeywordEntities.stream().filter(adKeywordEntity -> adKeywordEntity.getCampaignId() != null).map(AdKeywordEntity::getCampaignId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            return;
        }
        List<BaiduCampaignEntity> campaignEntityList = baiduCampaignDAO.queryMarketingEventIdNotEmptyCampaignByCampaignByIds(adAccountEntity.getEa(), adAccountEntity.getId(), source, campaignIds);
        if (CollectionUtils.isEmpty(campaignEntityList)) {
            return;
        }
        Date launchDate = null;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            launchDate = format.parse(date);
        } catch (Exception e) {
            log.info("syncTermServingLinesByKeyword format date failed date:{}", date);
        }
        List<Long> keywordIds = adKeywordEntities.stream().map(AdKeywordEntity::getKeywordId).collect(Collectors.toList());
        // 获取date的关键词数据
        List<ReportApiManager.KeywordDataUnit> keywordDataDataUnit = getKeywordDataUnit(adAccountEntity, keywordIds, date);
        if (CollectionUtils.isEmpty(keywordDataDataUnit)) {
            log.info("syncTermServingLinesByKeyword getKeywordDataUnit keywordDataDataUnit is null, adAccountEntity:{}", adAccountEntity);
            return;
        }
        List<ReportApiManager.KeywordDataUnit> filterKeywordDataDataUnit = keywordDataDataUnit.stream().filter(keywordDataUnit -> keywordDataUnit.getClick() != 0 || keywordDataUnit.getCost() != 0.0 || keywordDataUnit.getImpression() != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterKeywordDataDataUnit)) {
            log.info("syncTermServingLinesByKeyword filterKeywordDataDataUnit is null, keywordDataDataUnit:{}, date:{}", keywordDataDataUnit, date);
            return;
        }
        List<Long> filterKeywordIds = filterKeywordDataDataUnit.stream().map(ReportApiManager.KeywordDataUnit::getKeywordId).collect(Collectors.toList());
        List<AdKeywordEntity> filterAdKeywordEntities = adKeywordEntities.stream().filter(adKeywordEntity -> filterKeywordIds.contains(adKeywordEntity.getKeywordId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterAdKeywordEntities)) {
            log.info("syncTermServingLinesByKeyword filterAdKeywordEntities is null, adKeywordEntities:{}, date:{}", keywordDataDataUnit, date);
            return;
        }
        //Map<Long, String> campaignIdMarketingKeywordIdMap = filterAdKeywordEntities.stream().collect(Collectors.toMap(AdKeywordEntity::getCampaignId, AdKeywordEntity::getMarketingKeywordId, (v1, v2) -> v2));
        Map<Long, List<String>> campaignIdMarketingKeywordIdListMap = filterAdKeywordEntities.stream()
                .collect(Collectors.groupingBy(AdKeywordEntity::getCampaignId, Collectors.mapping(AdKeywordEntity::getMarketingKeywordId, Collectors.toList())));
        Map<Long, String> campaignIdMarketingEventIdMap = campaignEntityList.stream().collect(Collectors.toMap(BaiduCampaignEntity::getCampaignId, BaiduCampaignEntity::getMarketingEventId, (v1, v2) -> v2));
        Map<String, List<String>> marketingEventIdMarketingKeywordIdsMap = new HashMap<>();
        for (Long campaignId : campaignIds) {
            if (campaignIdMarketingEventIdMap.get(campaignId) != null) {
                String marketingEventId = campaignIdMarketingEventIdMap.get(campaignId);
                if (StringUtils.isNotEmpty(marketingEventId)) {
                    marketingEventIdMarketingKeywordIdsMap.computeIfAbsent(marketingEventId, k -> new ArrayList<>());
                    List<String> marketingKeywordIdList = campaignIdMarketingKeywordIdListMap.get(campaignId);
                    if (CollectionUtils.isNotEmpty(marketingKeywordIdList)) {
                        marketingEventIdMarketingKeywordIdsMap.get(marketingEventId).addAll(marketingKeywordIdList);
                    }
                }
            }
        }
        if (marketingEventIdMarketingKeywordIdsMap.isEmpty()) {
            log.info("syncTermServingLinesByKeyword marketingEventIdMarketingKeywordIdsMap is null, adAccountEntity:{}, date:{}", adAccountEntity, date);
            return;
        }
        Date finalLaunchDate = launchDate;
        List<ObjectData> keywordPlanDataList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : marketingEventIdMarketingKeywordIdsMap.entrySet()) {
            String key = entry.getKey();
            List<String> value = entry.getValue();
            Page<ObjectData> pageMarketingKeywordPlanDataPage = queryCrmMarketingKeywordPlanByUserName(adAccountEntity.getEa(), adAccountEntity.getUsername(), key, value, value.size());
            if (pageMarketingKeywordPlanDataPage == null) {
                log.info("syncTermServingLinesByKeyword queryCrmMarketingKeywordPlanByUserName return null ea:{}", adAccountEntity.getEa());
                continue;
            }
            keywordPlanDataList.addAll(pageMarketingKeywordPlanDataPage.getDataList());
            Page<ObjectData> termServingLinesDataPage = queryTermServingLinesByDay(adAccountEntity, key, value, finalLaunchDate);
            if (termServingLinesDataPage != null && CollectionUtils.isNotEmpty(termServingLinesDataPage.getDataList())) {
                // 已同步到关键词投放明细的关键词投放计划id
                List<Object> keywordPlanIdInTermServingDataList = termServingLinesDataPage.getDataList().stream().map(objectData -> objectData.get("keyword_serving_plan_id")).collect(Collectors.toList());
                keywordPlanDataList = keywordPlanDataList.stream().filter(objectData -> !keywordPlanIdInTermServingDataList.contains(objectData.get("_id"))).distinct().collect(Collectors.toList());
            }
        }
        //构建要发送的数据对象
        List<AdReportDataDTO> reportDataList = buildTermServingLinesData(campaignEntityList, filterKeywordDataDataUnit, filterAdKeywordEntities, keywordPlanDataList, launchDate);
        if (CollectionUtils.isEmpty(reportDataList)) {
            log.info("syncTermServingLinesByPlan buildTermServingLinesData return null ea:{} source:{} campaignEntityList:{} keywordDataUnitList:{} adKeywordEntityList:{} launchDate:{}",
                    adAccountEntity.getEa(), source, campaignEntityList, filterKeywordDataDataUnit, filterAdKeywordEntities, launchDate);
            return;
        }
        //发送广告明细数据
        int ei = eieaConverter.enterpriseAccountToId(adAccountEntity.getEa());
        for (AdReportDataDTO reportDataDTO : reportDataList) {
            getKeywordRRateLimiter(adAccountEntity.getEa()).acquire();
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createResult = createTermServingLinesObj(ei, reportDataDTO, adAccountEntity.getUsername());
            if (createResult == null || !createResult.isSuccess()) {
                log.info("create ad TermServingLinesObj failed ea:{} marketingEventId:{} keywordServingPlanObj:{} marketingKeyordId:{} date:{}", adAccountEntity.getEa(), reportDataDTO.getMarketingEventId(), reportDataDTO.getKeywordServingPlanId(), reportDataDTO.getMarketingKeywordId(), date);
            }
        }
    }

    private List<AdReportDataDTO> buildTermServingLinesData(List<BaiduCampaignEntity> campaignEntityList,
                                                            List<ReportApiManager.KeywordDataUnit> keywordDataUnitList,
                                                            List<AdKeywordEntity> adKeywordEntityList,
                                                            List<ObjectData> planDataList,
                                                            Date launchDate
    ) {
        if (CollectionUtils.isEmpty(campaignEntityList) || CollectionUtils.isEmpty(adKeywordEntityList)) {
            return null;
        }

        Map<Long, List<Long>> campaignIdKeywordIdListMap = new HashMap<>();
        for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
            if (campaignIdKeywordIdListMap.get(adKeywordEntity.getCampaignId()) == null) {
                campaignIdKeywordIdListMap.put(adKeywordEntity.getCampaignId(), new ArrayList<>());
            }
            campaignIdKeywordIdListMap.get(adKeywordEntity.getCampaignId()).add(adKeywordEntity.getKeywordId());
        }

        Map<Long, ReportApiManager.KeywordDataUnit> keywordIdReportMap = null;
        if (CollectionUtils.isNotEmpty(keywordDataUnitList)) {
            keywordIdReportMap = keywordDataUnitList.stream().collect(Collectors.toMap(ReportApiManager.KeywordDataUnit::getKeywordId, Function.identity(), (v1, v2) -> v2));
        }
        Map<Long, String> keywordIdMarketingKeywordMap = adKeywordEntityList.stream().collect(Collectors.toMap(AdKeywordEntity::getKeywordId, AdKeywordEntity::getMarketingKeywordId, (v1, v2) -> v2));
        Map<String, ReportApiManager.KeywordDataUnit> reportMap = new HashMap<>();

        for (BaiduCampaignEntity baiduCampaignEntity : campaignEntityList) {
            List<Long> keywordIds = campaignIdKeywordIdListMap.get(baiduCampaignEntity.getCampaignId());
            if (CollectionUtils.isEmpty(keywordIds)) {
                log.info("buildTermServingLinesData keywordIds is null ea:{} baiduCampaignEntity:{}", baiduCampaignEntity.getEa(), baiduCampaignEntity);
                continue;
            }
            for (Long keywordId : keywordIds) {
                String marketingKeywordId = keywordIdMarketingKeywordMap.get(keywordId);
                String key = baiduCampaignEntity.getMarketingEventId() + "-" + marketingKeywordId;
                ReportApiManager.KeywordDataUnit existKeywordData = reportMap.get(key);
                if (existKeywordData == null) {
                    if (keywordIdReportMap == null || keywordIdReportMap.get(keywordId) == null) {
                        ReportApiManager.KeywordDataUnit dataUnit = new ReportApiManager.KeywordDataUnit(keywordId, launchDate.toString());
                        dataUnit.setKeywordId(keywordId);
                        dataUnit.setClick(0L);
                        dataUnit.setImpression(0L);
                        dataUnit.setCost(0.0);
                        reportMap.put(key, dataUnit);
                    } else {
                        reportMap.put(key, keywordIdReportMap.get(keywordId));
                    }
                } else {
                    if (keywordIdReportMap != null && keywordIdReportMap.get(keywordId) != null) {
                        ReportApiManager.KeywordDataUnit keywordDataUnit = keywordIdReportMap.get(keywordId);
                        existKeywordData.setCost(existKeywordData.getCost() + keywordDataUnit.getCost());
                        existKeywordData.setImpression(existKeywordData.getImpression() + keywordDataUnit.getImpression());
                        existKeywordData.setClick(existKeywordData.getClick() + keywordDataUnit.getClick());
                    }
                }
            }
        }

        List<AdReportDataDTO> reportDataList = Lists.newArrayList();
        for (ObjectData objectData : planDataList) {
            AdReportDataDTO dataDTO = new AdReportDataDTO();
            dataDTO.setMarketingKeywordId(objectData.getString("marketing_keyword_id"));
            dataDTO.setMarketingEventId(objectData.getString("marketing_event_id"));
            dataDTO.setKeywordServingPlanId(objectData.getId());
            dataDTO.setLaunchDate(launchDate);
            dataDTO.setClick(0L);
            dataDTO.setImpression(0L);
            dataDTO.setCost(0.0);
            String key = dataDTO.getMarketingEventId() + "-" + dataDTO.getMarketingKeywordId();
            if (reportMap.get(key) != null) {
                ReportApiManager.KeywordDataUnit existKeywordData = reportMap.get(key);
                dataDTO.setClick(existKeywordData.getClick());
                dataDTO.setImpression(existKeywordData.getImpression());
                dataDTO.setCost(existKeywordData.getCost());
            }
            reportDataList.add(dataDTO);
        }

        return reportDataList;
    }


    public com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> createTermServingLinesObj(int ei, AdReportDataDTO reportData, String username) {
        log.info("createTermServingLinesObj start ei:{} data:{}", ei, reportData);
        Map<String, Object> params = new HashMap<>();
        List<String> createByList = Lists.newArrayList();
        createByList.add("-10000");
        params.put("launch_date", reportData.getLaunchDate().getTime());
        params.put("keyword_serving_plan_id", reportData.getKeywordServingPlanId());
        params.put("marketing_event_id", reportData.getMarketingEventId());
        params.put("marketing_keyword_id", reportData.getMarketingKeywordId());
        params.put("advertising_account", username);
        params.put("show_content", reportData.getImpression());
        params.put("click_count", reportData.getClick());
        params.put("advertising_costs", reportData.getCost());
        params.put("created_by", createByList);
        params.put("status", 1);
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        ActionAddArg arg = new ActionAddArg();
        ObjectData data = ObjectData.convert(params);
        data.setOwner(adCommonManager.getAdObjectDataOwner(ea));
        arg.setObjectData(data);
        try {
            return metadataActionService.add(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), false, arg);
        } catch (Exception e) {
            log.warn("CrmManager.createData Exception apiName:{} ei:{} e:", CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), ei, e);
            return null;
        }
    }

    private Page<ObjectData> queryTermServingLinesByDay(AdAccountEntity adAccountEntity, String marketingEventId, List<String> marketingKeywordIds, Date date) {
        if (CollectionUtils.isEmpty(marketingKeywordIds) || StringUtils.isEmpty(marketingEventId)) {
            return null;
        }

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("marketing_event_id", Lists.newArrayList(marketingEventId), FilterOperatorEnum.EQ);
        searchQuery.addFilter("marketing_keyword_id", marketingKeywordIds, FilterOperatorEnum.IN);
        searchQuery.addFilter("launch_date", Lists.newArrayList(String.valueOf(date.getTime())), FilterOperatorEnum.EQ);
        searchQuery.addFilter("advertising_account", Lists.newArrayList(adAccountEntity.getUsername()), FilterOperatorEnum.EQ);
        searchQuery.setLimit(marketingKeywordIds.size());
        searchQuery.setOffset(0);
        return crmV2Manager.getList(adAccountEntity.getEa(), -10000, CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), searchQuery);
    }

    public Page<ObjectData> queryCrmMarketingKeywordPlanByUserName(String ea, String userName, String marketingEventId, List<String> marketingKeywordIds, int size) {
        if (CollectionUtils.isEmpty(marketingKeywordIds) || StringUtils.isEmpty(marketingEventId)) {
            log.info("queryCrmMarketingKeywordPlan retun null marketingEventId:{} marketingEventId:{}", marketingEventId, marketingKeywordIds);
            return null;
        }
        List<String> finalMarketingKeywordIds = marketingKeywordIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalMarketingKeywordIds)) {
            log.info("过滤后的marketingKeywordIds为空:{}", marketingKeywordIds);
            return null;
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("marketing_event_id", Lists.newArrayList(marketingEventId), FilterOperatorEnum.EQ);
        searchQuery.addFilter("marketing_keyword_id", finalMarketingKeywordIds, FilterOperatorEnum.IN);
        searchQuery.addFilter("advertising_account", Lists.newArrayList(userName), FilterOperatorEnum.IN);
        searchQuery.setLimit(size);
        searchQuery.setOffset(0);
        return crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), searchQuery);
    }


    /**
     * 获取关键词实时数据
     *
     * @param adAccountEntity
     * @param keywordIds
     * @param date
     * @return KeywordDataUnit
     */
    public List<ReportApiManager.KeywordDataUnit> getKeywordDataUnit(AdAccountEntity adAccountEntity, List<Long> keywordIds, String date) {
        List<ReportApiManager.KeywordDataUnit> keywordDataUnitList = Lists.newArrayList();
        PageUtil<Long> keywordPage = new PageUtil<>(keywordIds, 500);
        for (int i = 0; i < keywordPage.getPageCount(); i++) {
            List<Long> pageKeywordId = keywordPage.getPagedList(i + 1);
            Long[] keywordArray = pageKeywordId.toArray(new Long[pageKeywordId.size()]);
            List<ReportApiManager.KeywordDataUnit> pageKeywordDataUnitList = reportApiManager.getRealTimeKeywordReportData(adAccountEntity, keywordArray, date);
            if (CollectionUtils.isNotEmpty(pageKeywordDataUnitList)) {
                keywordDataUnitList.addAll(pageKeywordDataUnitList);
            }
        }
        return keywordDataUnitList;
    }

    public void updateParentMarketingEventObj(String ea, String marketingEventId, String parentMarketingEventId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        ObjectData forUpdate = new ObjectData();
        forUpdate.put("parent_id", parentMarketingEventId);
        forUpdate.put("_id", marketingEventId);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(forUpdate);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result = metadataActionService.edit(new HeaderObj(ei, -10000), CrmObjectApiNameEnum.MARKETING_EVENT.getName(), false, false, arg);
        log.info("updateParentMarketingEventObj edit arg {} result:{}", forUpdate, result);
    }

    /**
     * 通过推广计划名称查询市场活动
     *
     * @param ea       企业账号
     * @param username 广告账户名称
     * @param names    推广计划名称/广告名称
     * @param source   广告类型
     * @return 市场活动详情
     */
    public Page<ObjectData> queryCrmMarketingEventByNames(String ea, String username, List<String> names, String source) {
        AdSourceEnum sourceEnum = AdSourceEnum.getBySource(source);
        if (CollectionUtils.isEmpty(names) || sourceEnum == null) {
            return null;
        }
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter(MarketingEventFieldContants.EVENT_TYPE, Lists.newArrayList(MarketingEventEnum.AD_MARKETING.getEventType()), FilterOperatorEnum.EQ);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), Lists.newArrayList(sourceEnum.getValue()), FilterOperatorEnum.EQ);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.NAME.getFieldName(), names, FilterOperatorEnum.IN);
        searchQuery.addFilter(CrmV2MarketingEventFieldEnum.AD_ACCOUNT.getFieldName(), Lists.newArrayList(username), FilterOperatorEnum.IN);
        searchQuery.setLimit(1000);
        searchQuery.setOffset(0);
        return crmV2Manager.getList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), searchQuery);
    }


    /**
     * 同步线索到CRM的销售线索对象
     * 由于crm的add接口没有提供batchAdd,该接口每次同步一条，若批量同步需多次调用
     *
     * @param ea                       企业账户
     * @param leadsId                  第三方平台的线索id
     * @param source                   广告来源
     * @param adLeadsMappingDataEntity 线索映射设置
     * @param param                    线索数据
     * @param marketingEventId         线索对应的子级市场活动
     * @return 同步结果
     */
    public com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> syncClueDataToCrmObj(String ea, String leadsId, String source, AdLeadsMappingDataEntity adLeadsMappingDataEntity, Map<String, Object> param, String marketingEventId) {
        List<String> createByList = Lists.newArrayList();
        Integer createBy = clueDefaultSettingService.getClueCreator(marketingEventId, ea, ClueDefaultSettingTypeEnum.AD.getType());
        createByList.add(String.valueOf(createBy));
        param.put("created_by", createByList);
        param.put("status", 1);
        param.put("marketing_event_id", marketingEventId);
        param.put("record_type", adLeadsMappingDataEntity.getCrmRecordType());
        param.put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("广告"));
        param.put(CrmV2MarketingEventFieldEnum.AD_SOURCE.getFieldName(), source);
        param.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
        param.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (StringUtils.isNotEmpty(adLeadsMappingDataEntity.getCrmPoolId())) {
            param.put("leads_pool_id", adLeadsMappingDataEntity.getCrmPoolId());
        }
        ObjectData objectData = ObjectData.convert(param);
        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(objectData);
        ActionAddArg.OptionInfo optionInfo = new ActionAddArg.OptionInfo();
        optionInfo.setIsDuplicateSearch(true);
        optionInfo.setCalculateDefaultValue(true);
        actionAddArg.setOptionInfo(optionInfo);
        return metadataActionService.addBySpecifyTime(createHeaderObj(ea, -10000), CrmObjectApiNameEnum.CRM_LEAD.getName(), actionAddArg);
    }

    public HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        if (null == tenantId) {
            throw new CrmBusinessException(-1000, "enterpriseAccountToId failed, ea=" + ea);
        }

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

    // 如果市场活动的名字和广告计划的名字不一样 要更新市场活动的名字
    public void checkAndUpdateMarketingEventName(String ea, String adSource, List<AdMarketingEventNameArg> adMarketingEventNameArgList) {
        if (CollectionUtils.isEmpty(adMarketingEventNameArgList)) {
            return;
        }
        List<String> marketingEventIdList = adMarketingEventNameArgList.stream().map(AdMarketingEventNameArg::getMarketingEventId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingEventIdList)) {
            return;
        }

        List<ObjectData> marketingEventList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "name"), marketingEventIdList);
        if (CollectionUtils.isEmpty(marketingEventList)) {
            return;
        }
        Map<String, String> idToNameMap = adMarketingEventNameArgList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId()) && StringUtils.isNotBlank(e.getLatestName())).collect(Collectors.toMap(AdMarketingEventNameArg::getMarketingEventId, AdMarketingEventNameArg::getLatestName, (v1, v2) -> v1));

        for (ObjectData objectData : marketingEventList) {
            String id = objectData.getId();
            String currentName = objectData.getName();
            String latestName = idToNameMap.get(id);
            boolean checkNameDiffFlag;
            if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(adSource)) {
                checkNameDiffFlag = !StringUtils.equals(currentName, latestName) && !StringUtils.equals(currentName, latestName + "(广告计划)") && !StringUtils.equals(currentName, latestName + "(广告组)");
            } else {
                checkNameDiffFlag = !StringUtils.equals(currentName, latestName);
            }
            if (checkNameDiffFlag) {
                int ei = eieaConverter.enterpriseAccountToId(ea);
                HeaderObj systemHeader = new HeaderObj(ei, SuperUserConstants.USER_ID);
                ActionEditArg actionEditArg = new ActionEditArg();
                ObjectData updateMarketingEventData = new ObjectData();
                updateMarketingEventData.setName(latestName);
                updateMarketingEventData.put("_id", id);
                actionEditArg.setObjectData(updateMarketingEventData);
                com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> editResult =
                        metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), true, true, actionEditArg);
                log.info("广告名称和市场活动名称不一致,ea: {} id: {} oldName: {} latestName {}, result: {}", ea, id, currentName, latestName, editResult);
            }
        }
    }

    public YxtRRateLimiter getKeywordRRateLimiter(String ea) {
        return YxtRRateLimiterBuilder.getYxtRRateLimiter(ea + "keyword:mk:rateLimiter", keywordPermitsPerSecond, 1, true, 60 * 60 * 24 * 7, TimeUnit.SECONDS);
    }

    public void fixBaiduCampaignMarketingEventId(String ea) {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAllByEa(ea);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            log.info("企业没有广告账号,ea: {}", ea);
            return;
        }
        Map<String, AdAccountEntity> adAccountIdToEntityMap = adAccountEntityList.stream().collect(Collectors.toMap(AdAccountEntity::getId, e -> e, (v1, v2) -> v1));
        List<BaiduCampaignEntity> baiduCampaignEntityList = baiduCampaignDAO.queryCampaignByEa(ea, AdSourceEnum.SOURCE_BAIDU.getSource());
        if (CollectionUtils.isEmpty(baiduCampaignEntityList)) {
            return;
        }
        AdObjectFieldMappingEntity marketingEventObjMappingEntity = adObjectFieldMappingDAO.getByApiName(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName());
        // 名字重复的广告计划
        Map<String, List<BaiduCampaignEntity>> nameToCampaignListMap = baiduCampaignEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId())).collect(Collectors.groupingBy(BaiduCampaignEntity::getCampaignName));
        for (Map.Entry<String, List<BaiduCampaignEntity>> entry : nameToCampaignListMap.entrySet()) {
            String campaignName = entry.getKey();
            List<BaiduCampaignEntity> sameNameBaiduCampaignEntityList = entry.getValue();
            if (sameNameBaiduCampaignEntityList.size() <= 1) {
                log.info("该广告计划不重名, ea: {} campaignName: {}", ea, campaignName);
                continue;
            }
            long marketingEventCount = sameNameBaiduCampaignEntityList.stream().map(BaiduCampaignEntity::getMarketingEventId).distinct().count();
            if (marketingEventCount >= sameNameBaiduCampaignEntityList.size()) {
                log.info("市场活动的数量大于等于计划数量，已经修复过了, ea: {} campaignName: {} campaignSize: {} marketingEventCount: {}", ea, campaignName, sameNameBaiduCampaignEntityList.size(), marketingEventCount);
                continue;
            }
            for (int i = 0; i < sameNameBaiduCampaignEntityList.size(); i++) {
                BaiduCampaignEntity baiduCampaignEntity = sameNameBaiduCampaignEntityList.get(i);
                String newMarketingEventName = baiduCampaignEntity.getCampaignName() + "-" + baiduCampaignEntity.getCampaignId();
                if (i == 0) {
                    // 第一条 保留原有的市场活动，编辑市场活动名字即可
                    Map<String, Object> forUpdateMap = Maps.newHashMap();
                    forUpdateMap.put("name", newMarketingEventName);
                    forUpdateMap.put("_id", baiduCampaignEntity.getMarketingEventId());
                    forUpdateMap.put("ad_campaign_id", String.valueOf(baiduCampaignEntity.getCampaignId()));
                    crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), forUpdateMap, false, false);
                    continue;
                }
                // 其余的重名的广告计划都重新生成新的市场活动
                AdAccountEntity adAccountEntity = adAccountIdToEntityMap.get(baiduCampaignEntity.getAdAccountId());
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = createMarketingEventObjByCampaign(ea, adAccountEntity.getUsername(), newMarketingEventName, baiduCampaignEntity.getSource(), marketingEventObjMappingEntity, null, baiduCampaignEntity.getCampaignId(), null);
                if (result == null || !result.isSuccess() || result.getData() == null || result.getData().getObjectData() == null) {
                    log.error("创建市场活动失败, ea: {} campaign: {}, result: {}", ea, baiduCampaignEntity, result);
                    continue;
                }
                String marketingEventId = result.getData().getObjectData().getId();
                int updateCount = baiduCampaignDAO.updateMarketingEventIdById(ea, baiduCampaignEntity.getId(), marketingEventId);
                log.info("更新市场活动, ea: {} id: {} marketingEventId: {} updateCount: {}", ea, baiduCampaignEntity.getId(), marketingEventId, updateCount);
            }
        }
    }


    @FilterLog
    public void batchSyncProjectFeedToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<BaiduProjectFeedEntity> projectFeedEntityList) {
        if (CollectionUtils.isEmpty(projectFeedEntityList)) {
            log.info("batchSyncProjectFeedToMarketingEventObj is empty, ea: {} adAccountId: {}", ea, adAccountEntity.getId());
            return;
        }
        log.info("batchSyncProjectFeedToMarketingEventObj, ea: {} adAccountId: {} projectFeedEntityList size: {}", ea, adAccountEntity.getId(), projectFeedEntityList.size());
        List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList = Lists.newArrayList();
        for (BaiduProjectFeedEntity projectFeedEntity : projectFeedEntityList) {
            AdSyncMarketingEventObjBO adSyncMarketingEventObjBO = BeanUtil.copy(projectFeedEntity, AdSyncMarketingEventObjBO.class);
            adSyncMarketingEventObjBO.setAdDataId(projectFeedEntity.getProjectFeedId());
            adSyncMarketingEventObjBO.setAdDataName(projectFeedEntity.getProjectFeedName());
            adSyncMarketingEventObjBO.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
            adSyncMarketingEventObjBO.setAdvertisingType(AdvertisingTypeEnum.SHOW_AD.getValue());
            adSyncMarketingEventObjList.add(adSyncMarketingEventObjBO);
        }
        List<AdSyncMarketingEventObjBO> needUpdateDbAdDataList = batchSyncAdToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, AdSourceEnum.SOURCE_BAIDU.getSource());
        log.info("batchSyncProjectFeedToMarketingEventObj, ea: {} batchSyncProjectFeedToMarketingEventObj.size: {} needUpdateDbAdDataList.size: {}", ea, projectFeedEntityList.size(), needUpdateDbAdDataList.size());
        if (CollectionUtils.isEmpty(needUpdateDbAdDataList)) {
            return;
        }
        List<BaiduProjectFeedEntity> needUpdateMarketingEventIdList = Lists.newArrayList();
        for (AdSyncMarketingEventObjBO adSyncMarketingEventObjBO : needUpdateDbAdDataList) {
            BaiduProjectFeedEntity baiduProjectFeedEntity = new BaiduProjectFeedEntity();
            baiduProjectFeedEntity.setId(adSyncMarketingEventObjBO.getId());
            baiduProjectFeedEntity.setMarketingEventId(adSyncMarketingEventObjBO.getMarketingEventId());
            baiduProjectFeedEntity.setEa(ea);
            needUpdateMarketingEventIdList.add(baiduProjectFeedEntity);
        }
        baiduProjectFeedDAO.batchUpdateMarketingEventById(ea, adAccountEntity.getId(), needUpdateMarketingEventIdList);
    }

    public void batchSyncBaiduCampaignFeedToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<BaiduCampaignFeedEntity> campaignFeedEntityList) {
        if (CollectionUtils.isEmpty(campaignFeedEntityList)) {
            log.info("batchSyncCampaignFeedToMarketingEventObj is empty, ea: {} adAccountId: {}", ea, adAccountEntity.getId());
            return;
        }
        log.info("batchSyncCampaignFeedToMarketingEventObj, ea: {} adAccountId: {} campaignFeedEntityList size: {}", ea, adAccountEntity.getId(), campaignFeedEntityList.size());

        List<Long> projectFeedIdList = campaignFeedEntityList.stream().map(BaiduCampaignFeedEntity::getProjectFeedId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, BaiduProjectFeedEntity> campaignFeedIdToProjectFeedMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(projectFeedIdList)) {
            List<BaiduProjectFeedEntity> baiduProjectFeedEntityList = baiduProjectFeedDAO.getByProjectFeedIdList(ea, adAccountEntity.getId(), projectFeedIdList);
            campaignFeedIdToProjectFeedMap = baiduProjectFeedEntityList.stream().collect(Collectors.toMap(BaiduProjectFeedEntity::getProjectFeedId, Function.identity()));
        }
        List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList = Lists.newArrayList();
        for (BaiduCampaignFeedEntity campaignFeedEntity : campaignFeedEntityList) {
            AdSyncMarketingEventObjBO adSyncMarketingEventObjBO = BeanUtil.copy(campaignFeedEntity, AdSyncMarketingEventObjBO.class);
            adSyncMarketingEventObjBO.setAdDataId(campaignFeedEntity.getCampaignFeedId());
            adSyncMarketingEventObjBO.setAdDataName(campaignFeedEntity.getCampaignFeedName());
            adSyncMarketingEventObjBO.setMarketingEventId(campaignFeedEntity.getMarketingEventId());
            adSyncMarketingEventObjBO.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
            adSyncMarketingEventObjBO.setParentAdDataId(campaignFeedEntity.getProjectFeedId());
            adSyncMarketingEventObjBO.setAdvertisingType(AdvertisingTypeEnum.SHOW_AD.getValue());
            BaiduProjectFeedEntity baiduProjectFeedEntity = campaignFeedIdToProjectFeedMap.get(campaignFeedEntity.getProjectFeedId());
            if (baiduProjectFeedEntity != null) {
                adSyncMarketingEventObjBO.setParentMarketingEventId(baiduProjectFeedEntity.getMarketingEventId());
            }
            adSyncMarketingEventObjList.add(adSyncMarketingEventObjBO);
        }
        batchSyncSubAdDataToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, TypeEnum.BAIDU_FEED_CAMPAIGN);
    }

    public void batchSyncBaiduAdGroupFeedToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<BaiduAdGroupFeedEntity> baiduAdGroupFeedEntityList) {
        if (CollectionUtils.isEmpty(baiduAdGroupFeedEntityList)) {
            log.info("batchSyncBaiduAdGroupFeedToMarketingEventObj is empty, ea: {} adAccountId: {}", ea, adAccountEntity.getId());
            return;
        }
        log.info("batchSyncBaiduAdGroupFeedToMarketingEventObj, ea: {} adAccountId: {} baiduAdGroupFeedEntityList size: {}", ea, adAccountEntity.getId(), baiduAdGroupFeedEntityList.size());

        List<Long> campaignFeedIdList = baiduAdGroupFeedEntityList.stream().map(BaiduAdGroupFeedEntity::getCampaignFeedId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, BaiduCampaignFeedEntity> campaignFeedIdToEntitiyMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(campaignFeedIdList)) {
            List<BaiduCampaignFeedEntity> baiduCampaignFeedEntityList = baiduCampaignFeedDAO.getByCampaignFeedIdList(ea, adAccountEntity.getId(), campaignFeedIdList);
            campaignFeedIdToEntitiyMap = baiduCampaignFeedEntityList.stream().collect(Collectors.toMap(BaiduCampaignFeedEntity::getCampaignFeedId, Function.identity()));
        }
        List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList = Lists.newArrayList();
        for (BaiduAdGroupFeedEntity baiduAdGroupFeedEntity : baiduAdGroupFeedEntityList) {
            AdSyncMarketingEventObjBO adSyncMarketingEventObjBO = BeanUtil.copy(baiduAdGroupFeedEntity, AdSyncMarketingEventObjBO.class);
            adSyncMarketingEventObjBO.setAdDataId(baiduAdGroupFeedEntity.getAdgroupFeedId());
            adSyncMarketingEventObjBO.setAdDataName(baiduAdGroupFeedEntity.getAdgroupFeedName());
            adSyncMarketingEventObjBO.setMarketingEventId(baiduAdGroupFeedEntity.getMarketingEventId());
            adSyncMarketingEventObjBO.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
            adSyncMarketingEventObjBO.setParentAdDataId(baiduAdGroupFeedEntity.getCampaignFeedId());
            adSyncMarketingEventObjBO.setAdvertisingType(AdvertisingTypeEnum.SHOW_AD.getValue());
            BaiduCampaignFeedEntity baiduCampaignFeedEntity = campaignFeedIdToEntitiyMap.get(baiduAdGroupFeedEntity.getCampaignFeedId());
            if (baiduCampaignFeedEntity != null) {
                adSyncMarketingEventObjBO.setParentMarketingEventId(baiduCampaignFeedEntity.getMarketingEventId());
            }
            adSyncMarketingEventObjList.add(adSyncMarketingEventObjBO);
        }
        batchSyncSubAdDataToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, TypeEnum.BAIDU_FEED_AD_GROUP);
    }

    public void batchSyncBaiduAdGroupToMarketingEventObj(String ea, AdAccountEntity adAccountEntity, List<AdGroupEntity> adGroupEntityList) {
        if (CollectionUtils.isEmpty(adGroupEntityList)) {
            return;
        }
        List<Long> campaignIdList = adGroupEntityList.stream().map(AdGroupEntity::getCampaignId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, BaiduCampaignEntity> campaignIdToEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            List<BaiduCampaignEntity> campaignEntityList = baiduCampaignDAO.queryCampaignByCampaignByIds(ea, adAccountEntity.getId(), adAccountEntity.getSource(), campaignIdList);
            campaignIdToEntityMap = campaignEntityList.stream().collect(Collectors.toMap(BaiduCampaignEntity::getCampaignId, Function.identity()));
        }
        List<AdSyncMarketingEventObjBO> adSyncMarketingEventObjList = Lists.newArrayList();
        for (AdGroupEntity adGroupEntity : adGroupEntityList) {
            AdSyncMarketingEventObjBO adSyncMarketingEventObjBO = BeanUtil.copy(adGroupEntity, AdSyncMarketingEventObjBO.class);
            adSyncMarketingEventObjBO.setAdDataId(adGroupEntity.getAdGroupId());
            adSyncMarketingEventObjBO.setAdDataName(adGroupEntity.getAdGroupName());
            adSyncMarketingEventObjBO.setMarketingEventId(adGroupEntity.getMarketingEventId());
            adSyncMarketingEventObjBO.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
            adSyncMarketingEventObjBO.setParentAdDataId(adGroupEntity.getCampaignId());
            adSyncMarketingEventObjBO.setAdvertisingType(AdvertisingTypeEnum.SO_AD.getValue());
            BaiduCampaignEntity baiduCampaignEntity = campaignIdToEntityMap.get(adGroupEntity.getCampaignId());
            if (baiduCampaignEntity != null) {
                adSyncMarketingEventObjBO.setParentMarketingEventId(baiduCampaignEntity.getMarketingEventId());
            }
            adSyncMarketingEventObjList.add(adSyncMarketingEventObjBO);
        }
        batchSyncSubAdDataToMarketingEventObj(ea, adAccountEntity, adSyncMarketingEventObjList, TypeEnum.BAIDU_SEARCH_AD_GROUP);
    }

    public void getEachPlatformLeadCount(String param) {
        String[] params = param.split(",");
        String beginTimeStr = params[0];
        String endTimeStr = params[1];
        Date beginTime = DateUtil.parse(beginTimeStr);
        Date endTime = DateUtil.parse(endTimeStr);
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAllAdAccount(false);
        Map<String, List<AdAccountEntity>> eaToAccountListMap = adAccountEntityList.stream().collect(Collectors.groupingBy(AdAccountEntity::getEa));
        Map<String, Map<String, Integer>> resultMap = Maps.newHashMap();
        for (Map.Entry<String, List<AdAccountEntity>> entry : eaToAccountListMap.entrySet()) {
            String ea = entry.getKey();
            List<AdAccountEntity> accountList = entry.getValue();
            if (marketingActivityRemoteManager.enterpriseStop(ea)) {
                continue;
            }

            try {
                Map<String, Integer> sourceToLeadCountMap = Maps.newHashMap();
                Map<String, List<AdAccountEntity>> sourceToAccountList = accountList.stream().collect(Collectors.groupingBy(AdAccountEntity::getSource));
                for (Map.Entry<String, List<AdAccountEntity>> sourceEntry : sourceToAccountList.entrySet()) {
                    String source = sourceEntry.getKey();
                    List<AdAccountEntity> sourceAccountList = sourceEntry.getValue();
                    Set<String> marketingEventIdSet = Sets.newHashSet();
                    List<String>  accountIdList = sourceAccountList.stream().map(AdAccountEntity::getId).collect(Collectors.toList());
                    if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source)) {
                        List<String> marketingEventIdList = baiduCampaignDAO.queryMarketingEventIdByEaAndAccountIdList(ea, accountIdList);
                        if (CollectionUtils.isNotEmpty(marketingEventIdList)) {
                            marketingEventIdList.stream().filter(Objects::nonNull).forEach(marketingEventIdSet::add);
                        }
                    } else if (AdSourceEnum.SOURCE_JULIANG.getSource().equals(source)) {
                        List<String> marketingEventIdList = headlinesCampaignDAO.queryMarketingEventIdByEaAndAccountIdList(ea, accountIdList);
                        if (CollectionUtils.isNotEmpty(marketingEventIdList)) {
                            marketingEventIdList.stream().filter(Objects::nonNull).forEach(marketingEventIdSet::add);
                        }
                        marketingEventIdList = headlinesAdDAO.queryMarketingEventIdByEaAndAccountIdList(ea, accountIdList);
                        if (CollectionUtils.isNotEmpty(marketingEventIdList)) {
                            marketingEventIdList.stream().filter(Objects::nonNull).forEach(marketingEventIdSet::add);
                        }
                    } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
                        List<String> marketingEventIdList = tencentCampaignDAO.queryMarketingEventIdByEaAndAccountIdList(ea, accountIdList);
                        if (CollectionUtils.isNotEmpty(marketingEventIdList)) {
                            marketingEventIdList.stream().filter(Objects::nonNull).forEach(marketingEventIdSet::add);
                        }
                        marketingEventIdList = tencentAdGroupDAO.queryMarketingEventIdByEaAndAccountIdList(ea, accountIdList);
                        if (CollectionUtils.isNotEmpty(marketingEventIdList)) {
                            marketingEventIdList.stream().filter(Objects::nonNull).forEach(marketingEventIdSet::add);
                        }
                    }
                    List<List<String>> partitionList = Lists.partition(Lists.newArrayList(marketingEventIdSet), 100);
                    Map<String, Integer> marketingEventIdToLeadCountMap = Maps.newHashMap();
                    for (List<String> partition : partitionList) {
                        Map<String, Integer> tempMap = marketingEventManager.getMarketingEventIdToLeadCountMap(ea, partition, beginTime.getTime(), endTime.getTime());
                        marketingEventIdToLeadCountMap.putAll(tempMap);
                    }
                    if (MapUtils.isNotEmpty(marketingEventIdToLeadCountMap)) {
                        int totalCount = marketingEventIdToLeadCountMap.values().stream().mapToInt(Integer::intValue).sum();
                        sourceToLeadCountMap.put(source, totalCount);
                        log.info("ea: {} source: {} leadCount: {}", ea, source, totalCount);
                    }
                }
                resultMap.put(ea, sourceToLeadCountMap);
            } catch (Exception e) {
             log.error("getEachPlatformLeadCount error ea: {} accountList: {}", ea, accountList, e);
            }
        }
        for (Map.Entry<String, Map<String, Integer>> entry : resultMap.entrySet()) {
            String ea = entry.getKey();
            Map<String, Integer> sourceToLeadCountMap = entry.getValue();
            log.info("单个企业线索数据： {}\t{}\t{}\t{}", ea, sourceToLeadCountMap.getOrDefault(AdSourceEnum.SOURCE_BAIDU.getSource(), 0), sourceToLeadCountMap.getOrDefault(AdSourceEnum.SOURCE_TENCETN.getSource(), 0), sourceToLeadCountMap.getOrDefault(AdSourceEnum.SOURCE_JULIANG.getSource(), 0));
        }
        log.info("结果汇总： {}", JsonUtil.toJson(resultMap));
    }

    public void syncKeywordServingPlanV2(String ea, String adAccountId, String source) {
        String lastId = null;
        int pageSize = 100;
        List<AdKeywordEntity> adKeywordEntityList =  adKeywordDAO.scanMarketingKeywordObjIdById(ea, adAccountId, null, pageSize);
        // 是否同步单元维度的关键词投放计划和关键词投放明细
        boolean isSyncAdGroupDimensionality = adCommonManager.isSyncAdGroupDimensionality(ea);
        int totalCount = 0;
        while(CollectionUtils.isNotEmpty(adKeywordEntityList)) {
            if (!adCommonManager.isSyncAdKeyword(ea)) {
                return;
            }
            totalCount += adKeywordEntityList.size();
            List<SyncKeywordServicePlanBO> syncKeywordServicePlanList = buildSyncKeywordServicePlanList(ea, adAccountId, source, adKeywordEntityList, isSyncAdGroupDimensionality);
            // 开始同步关键词投放计划
            syncKeywordServingPlanByBo(ea, adAccountId, source, syncKeywordServicePlanList);
            lastId = adKeywordEntityList.get(adKeywordEntityList.size() - 1).getId();
            adKeywordEntityList =  adKeywordDAO.scanMarketingKeywordObjIdById(ea, adAccountId, lastId, pageSize);
        }
        log.info("syncKeywordServingPlanV2 ea: {} totalCount: {}", ea, totalCount);
    }

    private List<SyncKeywordServicePlanBO> buildSyncKeywordServicePlanList(String ea, String adAccountId, String source, List<AdKeywordEntity> adKeywordEntityList, boolean isSyncAdGroupDimensionality) {
        if (CollectionUtils.isEmpty(adKeywordEntityList)) {
            return Lists.newArrayList();
        }
        Set<Long> campaignIdSet = Sets.newHashSet();
        Set<Long> adGroupIdSet = Sets.newHashSet();
        for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
            if (adKeywordEntity.getCampaignId() != null) {
                campaignIdSet.add(adKeywordEntity.getCampaignId());
            }
            if (adKeywordEntity.getAdgroupId() != null) {
                adGroupIdSet.add(adKeywordEntity.getAdgroupId());
            }
        }
        Map<Long, String> campaignIdToMarketingEventIdMap = Maps.newHashMap();
        Map<Long, String> adGroupIdToMarketingEventIdMap = Maps.newHashMap();
        if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source)) {
            if (CollectionUtils.isNotEmpty(campaignIdSet)) {
                List<BaiduCampaignEntity> campaignEntityList = baiduCampaignDAO.queryMarketingEventIdNotEmptyCampaignByCampaignByIds(ea, adAccountId, source, Lists.newArrayList(campaignIdSet));
                campaignEntityList.forEach(e -> campaignIdToMarketingEventIdMap.put(e.getCampaignId(), e.getMarketingEventId()));
            }
            if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
                List<AdGroupEntity> adGroupEntityList = adGroupDAO.queryByAdgroupIdList(ea, adAccountId, Lists.newArrayList(adGroupIdSet));
                adGroupEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId())).forEach(e -> adGroupIdToMarketingEventIdMap.put(e.getAdGroupId(), e.getMarketingEventId()));
            }
        } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
            List<TencentAdGroupEntity> adGroupEntityList = tencentAdGroupDAO.queryTencentAdGroupList(ea, adAccountId, Lists.newArrayList(adGroupIdSet));
            adGroupEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getSubMarketingEventId())).forEach(e -> adGroupIdToMarketingEventIdMap.put(e.getAdgroupId(), e.getSubMarketingEventId()));
        }
        List<SyncKeywordServicePlanBO> syncKeywordServicePlanList = Lists.newArrayList();
        for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
            SyncKeywordServicePlanBO syncKeywordServicePlan = new SyncKeywordServicePlanBO();
            syncKeywordServicePlan.setKeywordObjId(adKeywordEntity.getMarketingKeywordId());
            syncKeywordServicePlan.setKeyword(adKeywordEntity.getKeyword());
            syncKeywordServicePlan.setKeywordId(adKeywordEntity.getKeywordId());
            if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source)) {
                String subMarketingEventId = null;
                Long adGroupId = adKeywordEntity.getAdgroupId();
                TypeEnum syncDimensionality;
                // 百度有两个维度，一个单元+关键词维度，一个是计划+关键词维度，根据黑名单来控制走的是哪个维度,如果关键词直接挂在计划下面，直接走计划维度
                String marketingEventId = campaignIdToMarketingEventIdMap.get(adKeywordEntity.getCampaignId());
                if (!isSyncAdGroupDimensionality || adGroupId == null || adGroupId <= 0) {
                    // 走计划维度
                    syncDimensionality = TypeEnum.BAIDU_SEARCH_CAMPAIGN;
                } else {
                    // 走单元维度
                    subMarketingEventId = adGroupIdToMarketingEventIdMap.get(adGroupId);
                    syncDimensionality = TypeEnum.BAIDU_SEARCH_AD_GROUP;
                }
                syncKeywordServicePlan.setSyncDimensionality(syncDimensionality);
                syncKeywordServicePlan.setMarketingEventId(marketingEventId);
                syncKeywordServicePlan.setSubMarketingEventId(subMarketingEventId);
                syncKeywordServicePlanList.add(syncKeywordServicePlan);
            } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
                // 腾讯只同步 单元 + 关键词 一个维度
                if (adKeywordEntity.getAdgroupId() != null && StringUtils.isNotBlank(adGroupIdToMarketingEventIdMap.get(adKeywordEntity.getAdgroupId()))) {
                    syncKeywordServicePlan.setMarketingEventId(adGroupIdToMarketingEventIdMap.get(adKeywordEntity.getAdgroupId()));
                    syncKeywordServicePlan.setSyncDimensionality(TypeEnum.TENCENT_SEARCH_AD_GROUP);
                    syncKeywordServicePlanList.add(syncKeywordServicePlan);
                }
            }
        }
        return syncKeywordServicePlanList;
    }

    //TODO 在全网之后，将这里的兼容历史数据的逻辑去掉
    public void syncKeywordServingPlanByBo(String ea, String adAccountId, String source, List<SyncKeywordServicePlanBO> syncKeywordServicePlanList) {
        int beforeFilterSize = syncKeywordServicePlanList.size();
        syncKeywordServicePlanList = syncKeywordServicePlanList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordObjId()) && e.getKeywordId() != null && (StringUtils.isNotBlank(e.getMarketingEventId()) || StringUtils.isNotBlank(e.getSubMarketingEventId()))).collect(Collectors.toList());
        int afterFilterSize = syncKeywordServicePlanList.size();
        log.info("syncKeywordServingPlanByBo ea: {} beforeFilterSize: {} afterFilterSize: {} ", ea, beforeFilterSize, afterFilterSize);
        if (CollectionUtils.isEmpty(syncKeywordServicePlanList)) {
            return;
        }
        int partitionSize = 200;
        String uniqueKeyFormat = "%s-%s";
        String keywordServicePlanNameFormat = "%s-%s";
        List<SyncKeywordServicePlanBO> createKeywordPlanList = Lists.newArrayList();
        String outPlatformDataIdFieldName = "out_platform_data_id";
        for (List<SyncKeywordServicePlanBO> partition : Lists.partition(syncKeywordServicePlanList, partitionSize)) {
            // 这里一定要new一个新的list,否则newPartition.addAll会导致死循环
            List<SyncKeywordServicePlanBO> newPartition = Lists.newArrayList(partition);
            // 本次已经处理了的关键词投放计划的旧唯一标识key,用来了判断是否同一个广告组下同一个关键词重复关键词id又不同的情况，为了兼容历史数据
            Set<String> handleKeywordServicePlanOldUniqueKeySet = Sets.newHashSet();
            // 获取该账户下相同的关键词对象的id一起同步，主要是因为同一个广告组下面的关键词一样，关键词id不一样的场景
            List<SyncKeywordServicePlanBO> sameKeywordList = getSameKeywordObjIdKeywordServicePlanList(ea, adAccountId, source, newPartition);
            newPartition.addAll(sameKeywordList);
            // 根据同步的维度分组
            for (Map.Entry<TypeEnum, List<SyncKeywordServicePlanBO>> entry : newPartition.stream().collect(Collectors.groupingBy(SyncKeywordServicePlanBO::getSyncDimensionality)).entrySet()) {
                TypeEnum syncDimensionality = entry.getKey();
                List<SyncKeywordServicePlanBO> sameSyncKeywordServicePlanList = entry.getValue();
                Set<String> marketingEventIdSet = Sets.newHashSet();
                Set<String> keywordObjIdSet = Sets.newHashSet();
                Set<Long> keywordIdSet = Sets.newHashSet();
                for (SyncKeywordServicePlanBO syncKeywordServicePlanBO : sameSyncKeywordServicePlanList) {
                    // 单元维度的取单元对应的市场活动
                    if (adCommonManager.isSyncAdGroupDimensionality(syncDimensionality)) {
                        marketingEventIdSet.add(syncKeywordServicePlanBO.getSubMarketingEventId());
                    } else {
                        marketingEventIdSet.add(syncKeywordServicePlanBO.getMarketingEventId());
                    }
                    keywordObjIdSet.add(syncKeywordServicePlanBO.getKeywordObjId());
                    keywordIdSet.add(syncKeywordServicePlanBO.getKeywordId());
                }
                // 获取市场活动的名称
                List<String> marketingEventIdList = Lists.newArrayList(marketingEventIdSet);
                List<ObjectData> marketingEventObjDataList = crmMetadataManager.batchGetByIdsV3(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), Lists.newArrayList("_id", "name"), marketingEventIdList);
                Map<String, String> marketingEventIdToNameMap = marketingEventObjDataList.stream().collect(Collectors.toMap(ObjectData::getId, ObjectData::getName, (v1, v2) -> v1));
                // 获取已存在的关键词投放计划
                Map<String, ObjectData> existingKeywordServingPlanMap = getExistKeywordServingPlanMap(ea, syncDimensionality, marketingEventIdList, Lists.newArrayList(keywordObjIdSet), Lists.newArrayList(keywordIdSet));
                log.info("syncKeywordServingPlanByBo marketingEventObjDataList ea: {} size: {} existingKeywordServingPlanMap: {}", ea, marketingEventObjDataList.size(), existingKeywordServingPlanMap.size());
                for (SyncKeywordServicePlanBO syncKeywordServicePlan : sameSyncKeywordServicePlanList) {
                    String keywordId = syncKeywordServicePlan.getKeywordObjId();
                    String marketingEventId = adCommonManager.isSyncAdGroupDimensionality(syncDimensionality) ? syncKeywordServicePlan.getSubMarketingEventId() : syncKeywordServicePlan.getMarketingEventId();

                    ObjectData existObjectData = existingKeywordServingPlanMap.get(String.valueOf(syncKeywordServicePlan.getKeywordId()));
                    String oldUniqueKey = String.format(uniqueKeyFormat, marketingEventId, keywordId);
                    // 如果通过关键词id获取不到，再用一次老的唯一key获取，如果老的唯一key已经处理过了，那么可直接新建新的关键词投放计划
                    if (existObjectData == null && !handleKeywordServicePlanOldUniqueKeySet.contains(oldUniqueKey)) {
                        existObjectData = existingKeywordServingPlanMap.get(oldUniqueKey);
                        handleKeywordServicePlanOldUniqueKeySet.add(oldUniqueKey);
                    }
                    if (existObjectData != null) {
                        // 如果已经存在了关键词投放计划，判断信息是否有变化
                        String oldName = existObjectData.getName();
                        String latestMarketingEventName = marketingEventIdToNameMap.get(marketingEventId);
                        log.info("syncKeywordServingPlanByBo already exist, ea: {}, syncKeywordServicePlan: {}", ea, syncKeywordServicePlan);
                        if (StringUtils.isBlank(latestMarketingEventName)) {
                            log.warn("syncKeywordServingPlanV2 update name, latestMarketingEventName is blank, syncKeywordServicePlan: {}", syncKeywordServicePlan);
                            continue;
                        }
                        Map<String, Object> forUpdate = Maps.newHashMap();
                        String newName = String.format(keywordServicePlanNameFormat, latestMarketingEventName, syncKeywordServicePlan.getKeyword());
                        if (!newName.equals(oldName)) {
                            forUpdate.put("name", newName);
                        }
                        String outPlatformDataId = existObjectData.getString(outPlatformDataIdFieldName);
                        if (StringUtils.isBlank(outPlatformDataId) || !outPlatformDataId.equals(String.valueOf(syncKeywordServicePlan.getKeywordId()))) {
                            forUpdate.put(outPlatformDataIdFieldName, String.valueOf(syncKeywordServicePlan.getKeywordId()));
                        }
                        if (adCommonManager.isSyncAdGroupDimensionality(syncDimensionality)) {
                            // 判断父级市场活动是否有变化
                            String oldMarketingEventId = existObjectData.getString("marketing_event_id");
                            if (StringUtils.isNotBlank(syncKeywordServicePlan.getMarketingEventId()) && (StringUtils.isBlank(oldMarketingEventId) || !syncKeywordServicePlan.getMarketingEventId().equals(oldMarketingEventId))) {
                                forUpdate.put("marketing_event_id", syncKeywordServicePlan.getMarketingEventId());
                            }
                        }
                        if (MapUtils.isNotEmpty(forUpdate)) {
                            forUpdate.put("_id", existObjectData.getId());
                            try {
                                Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), forUpdate);
                                log.info("update keyword service plan, ea: {} forUpdate: {} result: {}", ea, forUpdate, result);
                            } catch (Exception e) {
                                log.error("syncKeywordServingPlanV2 update error, ea: {} syncKeywordServicePlan: {} forUpdate: {}", ea, syncKeywordServicePlan, forUpdate, e);
                            }
                        }
                        continue;
                    }
                    // 如果不存在关键词投放计划，创建关键词投放计划
                    syncKeywordServicePlan.setMarketingEventName(marketingEventIdToNameMap.get(syncKeywordServicePlan.getMarketingEventId()));
                    syncKeywordServicePlan.setSubMarketingEventName(marketingEventIdToNameMap.get(syncKeywordServicePlan.getSubMarketingEventId()));
                    createKeywordPlanList.add(syncKeywordServicePlan);
                }
            }
            if (CollectionUtils.isEmpty(createKeywordPlanList)) {
                continue;
            }
            log.info("create keyword service plan, ea: {} size: {}", ea, createKeywordPlanList.size());
            List<ObjectData> forInsertList = Lists.newArrayList();
            int owner = adCommonManager.getAdObjectDataOwner(ea);
            for (SyncKeywordServicePlanBO syncKeywordServicePlan : createKeywordPlanList) {
                ObjectData objectData = new ObjectData();
                String finalMarketingEventName =  adCommonManager.isSyncAdGroupDimensionality(syncKeywordServicePlan.getSyncDimensionality()) ? syncKeywordServicePlan.getSubMarketingEventName() : syncKeywordServicePlan.getMarketingEventName();
                if (StringUtils.isBlank(finalMarketingEventName)) {
                    log.warn("syncKeywordServingPlanV2 create, finalMarketingEventName is blank, syncKeywordServicePlan: {}", syncKeywordServicePlan);
                    continue;
                }
                objectData.put("name", finalMarketingEventName + "-" + syncKeywordServicePlan.getKeyword());
                if (StringUtils.isNotBlank(syncKeywordServicePlan.getSubMarketingEventId())) {
                    objectData.put("sub_marketing_event_id", syncKeywordServicePlan.getSubMarketingEventId());
                }
                if (StringUtils.isNotBlank(syncKeywordServicePlan.getMarketingEventId())) {
                    objectData.put("marketing_event_id", syncKeywordServicePlan.getMarketingEventId());
                }
                objectData.put("marketing_keyword_id", syncKeywordServicePlan.getKeywordObjId());
                objectData.put("created_by", Lists.newArrayList(String.valueOf(SuperUserConstants.USER_ID)));
                objectData.put("status", 1);
                objectData.put(outPlatformDataIdFieldName, String.valueOf(syncKeywordServicePlan.getKeywordId()));
                objectData.setOwner(owner);
                forInsertList.add(objectData);
            }
            for (List<ObjectData> objectDataList : Lists.partition(forInsertList, batchCreateKeywordServingSize)) {
                try {
                    log.info("开始创建新的关键词投放计划，ea: {} size: {}", ea, objectDataList.size());
                    GuavaLimiter.acquire(KEYWORD_SERVING_RATE_LIMIT_KEY, ea);
                    Result<ActionBulkCreateResult> result = crmV2Manager.bulkCreate(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), objectDataList, false, false, true);
                    log.info("create keyword service plan, ea: {} result: {}", ea, result);
                } catch (Exception e) {
                    log.error("syncKeywordServingPlanV2 create error, ea: {} objectDataList: {}", ea, objectDataList, e);
                }
            }
        }

    }

    private List<SyncKeywordServicePlanBO> getSameKeywordObjIdKeywordServicePlanList(String ea, String adAccountId, String source, List<SyncKeywordServicePlanBO> partition) {
        Set<Long> recheckKeywordIdSet = Sets.newHashSet();
        Set<String> requesyKeywordObjIdSet = Sets.newHashSet();
        for (SyncKeywordServicePlanBO syncKeywordServicePlanBO : partition) {
            recheckKeywordIdSet.add(syncKeywordServicePlanBO.getKeywordId());
            requesyKeywordObjIdSet.add(syncKeywordServicePlanBO.getKeywordObjId());
        }
        List<AdKeywordEntity> adKeywordEntityList = adKeywordDAO.queryByMarketingKeywordIdList(ea, adAccountId, Lists.newArrayList(requesyKeywordObjIdSet));
        boolean isSyncAdGroupDimensionality = adCommonManager.isSyncAdGroupDimensionality(ea);
        adKeywordEntityList = adKeywordEntityList.stream().filter(e -> !recheckKeywordIdSet.contains(e.getKeywordId())).collect(Collectors.toList());
        return buildSyncKeywordServicePlanList(ea, adAccountId, source, adKeywordEntityList, isSyncAdGroupDimensionality);
    }


    public void syncTermServingLinesByKeywordV2(AdAccountEntity adAccountEntity, String source, List<AdKeywordEntity> adKeywordEntityList, String date) {
        adKeywordEntityList = adKeywordEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingKeywordId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adKeywordEntityList)) {
            return;
        }
        String ea = adAccountEntity.getEa();
        List<SyncTermServiceLineBO> syncTermServiceLineList = Lists.newArrayList();
        if (AdSourceEnum.SOURCE_BAIDU.getSource().equals(source)) {
            boolean isSyncAdGroupDimensionality = adCommonManager.isSyncAdGroupDimensionality(ea);
            List<Long> keywordIds = adKeywordEntityList.stream().map(AdKeywordEntity::getKeywordId).collect(Collectors.toList());
            // 获取百度平台的关键词展点消
            List<ReportApiManager.KeywordDataUnit> keywordDataDataUnit = getKeywordDataUnit(adAccountEntity, keywordIds, date);
            if (CollectionUtils.isEmpty(keywordDataDataUnit)) {
                log.info("syncTermServingLinesByKeyword getKeywordDataUnit keywordDataDataUnit is null, adAccountEntity:{}", adAccountEntity);
                return;
            }
            List<ReportApiManager.KeywordDataUnit> filterKeywordDataDataUnit = keywordDataDataUnit.stream().filter(keywordDataUnit -> keywordDataUnit.getClick() != 0 || keywordDataUnit.getCost() != 0.0 || keywordDataUnit.getImpression() != 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterKeywordDataDataUnit)) {
                log.info("syncTermServingLinesByKeyword filterKeywordDataDataUnit is null, keywordDataDataUnit:{}, date:{}", keywordDataDataUnit, date);
                return;
            }
            Map<Long, ReportApiManager.KeywordDataUnit> keywordIdToDataMap = filterKeywordDataDataUnit.stream().collect(Collectors.toMap(ReportApiManager.KeywordDataUnit::getKeywordId, Function.identity(), (v1, v2) -> v1));
            Set<Long> campaignIdSet = Sets.newHashSet();
            Set<Long> adGroupIdSet = Sets.newHashSet();
            for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
                if (adKeywordEntity.getCampaignId() != null) {
                    campaignIdSet.add(adKeywordEntity.getCampaignId());
                }
                if (adKeywordEntity.getAdgroupId() != null) {
                    adGroupIdSet.add(adKeywordEntity.getAdgroupId());
                }
            }
            Map<Long, String> campaignIdToMarketingEventIdMap = Maps.newHashMap();
            Map<Long, String> adGroupIdToMarketingEventIdMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(campaignIdSet)) {
                List<BaiduCampaignEntity> campaignEntityList = baiduCampaignDAO.queryMarketingEventIdNotEmptyCampaignByCampaignByIds(ea, adAccountEntity.getId(), source, Lists.newArrayList(campaignIdSet));
                campaignEntityList.forEach(e -> campaignIdToMarketingEventIdMap.put(e.getCampaignId(), e.getMarketingEventId()));
            }
            if (CollectionUtils.isNotEmpty(adGroupIdSet)) {
                List<AdGroupEntity> adGroupEntityList = adGroupDAO.queryByAdgroupIdList(ea, adAccountEntity.getId(), Lists.newArrayList(adGroupIdSet));
                adGroupEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getMarketingEventId())).forEach(e -> adGroupIdToMarketingEventIdMap.put(e.getAdGroupId(), e.getMarketingEventId()));
            }
            Map<TypeEnum, List<AdKeywordEntity>> marketingEventTypeToHavDataAdKeyword = Maps.newHashMap();
            for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
                ReportApiManager.KeywordDataUnit keywordDataUnit = keywordIdToDataMap.get(adKeywordEntity.getKeywordId());
                if (keywordDataUnit == null) {
                    continue;
                }
                // 如果该企业不需要同步单元维度或者关键词直接挂在计划下面
                TypeEnum marketingEventType = (isSyncAdGroupDimensionality &&  NumberUtil.isPositive(adKeywordEntity.getAdgroupId())) ? TypeEnum.BAIDU_SEARCH_AD_GROUP :  TypeEnum.BAIDU_SEARCH_CAMPAIGN;
                List<AdKeywordEntity> hasDataAdKeywordList = marketingEventTypeToHavDataAdKeyword.computeIfAbsent(marketingEventType, k -> Lists.newArrayList());
                hasDataAdKeywordList.add(adKeywordEntity);
            }
            marketingEventTypeToHavDataAdKeyword.forEach((marketingEventType, hasDataAdKeywordList) -> {
                List<String> marketingKeywordIdList = Lists.newArrayList();
                Map<Long, String> keywordIdToMarketingEventIdMap = Maps.newHashMap();
                for (AdKeywordEntity adKeywordEntity : hasDataAdKeywordList) {
                    Long adGroupId = adKeywordEntity.getAdgroupId();
                    // 如果该企业不需要同步单元维度或者关键词直接挂在计划下面
                    String marketingEventId = marketingEventType == TypeEnum.BAIDU_SEARCH_CAMPAIGN ? campaignIdToMarketingEventIdMap.get(adKeywordEntity.getCampaignId()) : adGroupIdToMarketingEventIdMap.get(adGroupId);
                    if (StringUtils.isNotBlank(marketingEventId)) {
                        keywordIdToMarketingEventIdMap.put(adKeywordEntity.getKeywordId(), marketingEventId);
                    }
                    marketingKeywordIdList.add(adKeywordEntity.getMarketingKeywordId());
                }
                Map<String, ObjectData> existingKeywordServingPlanMap = getExistKeywordServingPlanMap(ea, marketingEventType, Lists.newArrayList(keywordIdToMarketingEventIdMap.values()), marketingKeywordIdList, Lists.newArrayList(keywordIdToMarketingEventIdMap.keySet()));
                for (AdKeywordEntity adKeywordEntity : hasDataAdKeywordList) {
                    ReportApiManager.KeywordDataUnit keywordDataUnit = keywordIdToDataMap.get(adKeywordEntity.getKeywordId());
                    if (keywordDataUnit == null) {
                        continue;
                    }
                    String marketingEventId = keywordIdToMarketingEventIdMap.get(adKeywordEntity.getKeywordId());
                    if (StringUtils.isBlank(marketingEventId)) {
                        log.info("marketingEventId is null, adKeywordEntity: {}", adKeywordEntity);
                        continue;
                    }
                    SyncTermServiceLineBO adGroupTermServiceLine = new SyncTermServiceLineBO();
                    ObjectData existKeywordServingPlan  = existingKeywordServingPlanMap.get(String.valueOf(adKeywordEntity.getKeywordId()));
                    if (existKeywordServingPlan == null) {
                        String key = String.format("%s-%s", marketingEventId, adKeywordEntity.getKeywordId());
                        existKeywordServingPlan = existingKeywordServingPlanMap.get(key);
                    }
                    if (existKeywordServingPlan == null) {
                        log.info("existKeywordServingPlan is null, marketingEventId: {} adKeywordEntity: {}", marketingEventId, adKeywordEntity);
                        continue;
                    }
                    adGroupTermServiceLine.setActionDate(date);
                    adGroupTermServiceLine.setKeywordObjId(adKeywordEntity.getMarketingKeywordId());
                    adGroupTermServiceLine.setMarketingEventId(marketingEventId);
                    adGroupTermServiceLine.setImpression(keywordDataUnit.getImpression());
                    adGroupTermServiceLine.setClick(keywordDataUnit.getClick());
                    adGroupTermServiceLine.setCost(keywordDataUnit.getCost());
                    adGroupTermServiceLine.setKeywordServingPlanId(existKeywordServingPlan.getId());
                    syncTermServiceLineList.add(adGroupTermServiceLine);
                }
            });
        } else if (AdSourceEnum.SOURCE_TENCETN.getSource().equals(source)) {
            List<Long> adIds = adKeywordEntityList.stream().map(AdKeywordEntity::getAdgroupId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(adIds)) {
                return;
            }
            List<TencentAdGroupEntity> adGroupEntityList = tencentAdGroupDAO.queryByAdGroupIdList(adAccountEntity.getEa(), adIds);
            if (CollectionUtils.isEmpty(adGroupEntityList)) {
                return;
            }
            Map<Long, String> adGroupIdToMarketingEventIdMap = adGroupEntityList.stream().filter(e -> StringUtils.isNotBlank(e.getSubMarketingEventId())).collect(Collectors.toMap(TencentAdGroupEntity::getAdgroupId, TencentAdGroupEntity::getSubMarketingEventId, (v1, v2) -> v1));
            Map<Long, Set<Long>> adGroupKeywordMap = new HashMap<>();
            Map<Long, String> keywordIdToObjIdMap = Maps.newHashMap();
            for (AdKeywordEntity adKeywordEntity : adKeywordEntityList) {
                Long adId = adKeywordEntity.getAdgroupId();
                if (adId < 1) {
                    continue;
                }
                Set<Long> set = adGroupKeywordMap.computeIfAbsent(adId, k -> new HashSet<>());
                set.add(adKeywordEntity.getKeywordId());
                keywordIdToObjIdMap.put(adKeywordEntity.getKeywordId(), adKeywordEntity.getMarketingKeywordId());
            }
            // 获取腾讯的关键词数据
            List<TencentKeywordDataResult.TencentKeywordData> keywordDataDataList = getTencentKeywordData(adAccountEntity, adGroupKeywordMap, date);
            if (CollectionUtils.isEmpty(keywordDataDataList)) {
                log.info("syncTermServingLinesByKeyword getTencentKeywordData keywordDataDataList is null, adAccountEntity:{}", adAccountEntity);
                return;
            }
            List<TencentKeywordDataResult.TencentKeywordData> filterKeywordDataList = keywordDataDataList.stream().filter(keywordData -> adIds.contains(keywordData.getAdgroup_id()) && (keywordData.getValid_click_count() != 0.0 || keywordData.getView_count() != 0.0)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterKeywordDataList)) {
                log.info("syncTermServingLinesByKeyword filterKeywordDataList is null, adAccountEntity:{}, date:{}", adAccountEntity, date);
                return;
            }
            List<String> marketingEventIdList = Lists.newArrayList();
            List<String> marketingKeywordIdList = Lists.newArrayList();
            List<Long> keywordIdList = Lists.newArrayList();
            for (TencentKeywordDataResult.TencentKeywordData tencentKeywordData : filterKeywordDataList) {
                String marketingEventId = adGroupIdToMarketingEventIdMap.get(tencentKeywordData.getAdgroup_id());
                String marketingKeywordObjId = keywordIdToObjIdMap.get(tencentKeywordData.getBidword_id());
                if (StringUtils.isNotBlank(marketingEventId)) {
                    marketingEventIdList.add(marketingEventId);
                }
                if (StringUtils.isNotBlank(marketingKeywordObjId)) {
                    marketingKeywordIdList.add(marketingKeywordObjId);
                }
                keywordIdList.add(tencentKeywordData.getBidword_id());
            }
            Map<String, ObjectData> existsKeywordServingPlanMap = getExistKeywordServingPlanMap(ea, TypeEnum.TENCENT_SEARCH_AD_GROUP, marketingEventIdList, marketingKeywordIdList, keywordIdList);
            for (TencentKeywordDataResult.TencentKeywordData tencentKeywordData : filterKeywordDataList) {
                String marketingEventId = adGroupIdToMarketingEventIdMap.get(tencentKeywordData.getAdgroup_id());
                String marketingKeywordObjId = keywordIdToObjIdMap.get(tencentKeywordData.getBidword_id());
                if (StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(marketingKeywordObjId)) {
                    log.info("marketingEventId or marketingKeywordObjId is null, tencentKeywordData: {}", tencentKeywordData);
                    continue;
                }
                ObjectData existKeywordServingPlan = existsKeywordServingPlanMap.get(String.valueOf(tencentKeywordData.getBidword_id()));
                if (existKeywordServingPlan == null) {
                    String key = String.format("%s-%s", marketingEventId, marketingKeywordObjId);
                    existKeywordServingPlan = existsKeywordServingPlanMap.get(key);
                }
                if (existKeywordServingPlan == null) {
                    log.info("existKeywordServingPlan is null, tencentKeywordData: {}", tencentKeywordData);
                    continue;
                }
                SyncTermServiceLineBO termServiceLine = new SyncTermServiceLineBO();
                termServiceLine.setKeywordObjId(marketingKeywordObjId);
                termServiceLine.setMarketingEventId(marketingEventId);
                termServiceLine.setActionDate(tencentKeywordData.getDate());
                termServiceLine.setImpression(tencentKeywordData.getView_count() == null ? 0L : Long.parseLong(tencentKeywordData.getView_count().toString()));
                termServiceLine.setClick(tencentKeywordData.getValid_click_count() == null ? 0L : Long.parseLong(tencentKeywordData.getValid_click_count().toString()));
                Double cost = tencentKeywordData.getCost();
                BigDecimal costBigDecimal = BigDecimal.ZERO;
                if (cost != null && cost > 0) {
                    costBigDecimal = new BigDecimal(Double.toString(cost)).divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);
                }
                termServiceLine.setCost(costBigDecimal.doubleValue());
                termServiceLine.setKeywordServingPlanId(existKeywordServingPlan.getId());
                syncTermServiceLineList.add(termServiceLine);
            }
        }
        syncTermServingLinesByBO(ea, syncTermServiceLineList);
    }

    public void syncTermServingLinesByBO(String ea, List<SyncTermServiceLineBO> syncTermServiceLineList) {
        syncTermServiceLineList = syncTermServiceLineList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordObjId()) && (StringUtils.isNotBlank(e.getMarketingEventId())) && (e.getImpression() > 0 || e.getClick() > 0 || e.getCost() > 0D)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncTermServiceLineList)) {
            return;
        }
        String termServingLineUniqueKeyFormat = "%s-%s";
        for (List<SyncTermServiceLineBO> partition : Lists.partition(syncTermServiceLineList, 100)) {
            // 查询投放日期对应的关键词投放明细是否已经存在
            List<ObjectData> existTermServingLineList = getExistsTermServingLine(ea, partition);
            Map<String, ObjectData> existTermServingLineMap = Maps.newHashMap();
            for (ObjectData objectData : existTermServingLineList) {
                String keywordServingPlanId = objectData.getString("keyword_serving_plan_id");
                Long launchDateTime = objectData.getLong("launch_date");
                String launchDate = DateUtil.format2(new Date(launchDateTime));
                String uniqueKey = String.format(termServingLineUniqueKeyFormat, keywordServingPlanId, launchDate);
                existTermServingLineMap.put(uniqueKey, objectData);
            }
            List<SyncTermServiceLineBO> createList = Lists.newArrayList();
            for (SyncTermServiceLineBO syncTermServiceLine : partition) {
                String launchDate = syncTermServiceLine.getActionDate();
                String uniqueKey = String.format(termServingLineUniqueKeyFormat, syncTermServiceLine.getKeywordServingPlanId(), launchDate);
                ObjectData existTermServingLine = existTermServingLineMap.get(uniqueKey);
                if (existTermServingLine != null) {
                    // 如果已存在 试着更新
                    tryUpdateTermServingLine(ea, syncTermServiceLine, existTermServingLine);
                    continue;
                }
                createList.add(syncTermServiceLine);
            }
            createTermServingLinesObj(ea, createList);
        }
    }

    private void createTermServingLinesObj(String ea, List<SyncTermServiceLineBO> syncTermServiceLineBOList) {
        if (CollectionUtils.isEmpty(syncTermServiceLineBOList)) {
            return;
        }
        int owner = adCommonManager.getAdObjectDataOwner(ea);
        List<ObjectData> createList = Lists.newArrayList();
        for (SyncTermServiceLineBO syncTermServiceLine : syncTermServiceLineBOList) {
            ObjectData params = new ObjectData();
            params.setOwner(owner);
            params.put("launch_date", DateUtil.parse3(syncTermServiceLine.getActionDate()).getTime());
            String marketingEventId = syncTermServiceLine.getMarketingEventId();
            String marketingKeywordId = syncTermServiceLine.getKeywordObjId();
            params.put("marketing_event_id", marketingEventId);
            params.put("marketing_keyword_id", marketingKeywordId);
            params.put("keyword_serving_plan_id", syncTermServiceLine.getKeywordServingPlanId());
            params.put("show_content", syncTermServiceLine.getImpression() == null ? 0L : syncTermServiceLine.getImpression());
            params.put("click_count", syncTermServiceLine.getClick() == null ? 0L : syncTermServiceLine.getClick());
            params.put("advertising_costs", syncTermServiceLine.getCost() == null ? 0D : syncTermServiceLine.getCost());
            params.put("status", 1);
            createList.add(params);
        }
        for (List<ObjectData> objectDataList : Lists.partition(createList, batchCreateKeywordServingSize)) {
            try {
                log.info("开始创建新的关键词明细，ea: {} size: {}", ea, objectDataList.size());
                GuavaLimiter.acquire(KEYWORD_SERVING_RATE_LIMIT_KEY, ea);
                Result<ActionBulkCreateResult> result = crmV2Manager.bulkCreate(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), objectDataList, false, false, true);
                log.info("create term service line, ea: {} result: {}", ea, result);
            } catch (Exception e) {
                log.error("createTermServingLinesObj create error, ea: {} objectDataList: {}", ea, objectDataList, e);
            }
        }
    }

    private void tryUpdateTermServingLine(String ea, SyncTermServiceLineBO syncTermServiceLine, ObjectData existTermServingLine) {
        // 如果当天的投放已经有数据，如果展点消有变化，进行更新
        BigDecimal oldShow = existTermServingLine.getBigDecimal("show_content") == null ? BigDecimal.ZERO : existTermServingLine.getBigDecimal("show_content");
        BigDecimal oldClick = existTermServingLine.getBigDecimal("click_count") == null ? BigDecimal.ZERO : existTermServingLine.getBigDecimal("click_count");
        BigDecimal oldCost = existTermServingLine.getBigDecimal("advertising_costs") == null ? BigDecimal.ZERO : existTermServingLine.getBigDecimal("advertising_costs");

        BigDecimal newShow = syncTermServiceLine.getImpression() == null ? BigDecimal.ZERO : BigDecimal.valueOf(syncTermServiceLine.getImpression());
        BigDecimal newClick = syncTermServiceLine.getClick() == null ? BigDecimal.ZERO : BigDecimal.valueOf(syncTermServiceLine.getClick());
        BigDecimal newCost = syncTermServiceLine.getCost() == null ? BigDecimal.ZERO : BigDecimal.valueOf(syncTermServiceLine.getCost());

        Map<String, Object> forUpdate = Maps.newHashMap();
        if (newShow.compareTo(oldShow) != 0) {
            forUpdate.put("show_content", newShow.longValue());
        }
        if (newClick.compareTo(oldClick) != 0) {
            forUpdate.put("click_count", newClick.longValue());
        }
        if (newCost.compareTo(oldCost) != 0) {
            forUpdate.put("advertising_costs", newCost.doubleValue());
        }
        if (MapUtils.isNotEmpty(forUpdate)) {
            forUpdate.put("_id", existTermServingLine.getId());
            try {
                Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), forUpdate);
                log.info("update term service line, ea: {} forUpdate: {} result: {}", ea, forUpdate, result);
            } catch (Exception e) {
                log.error("syncTermServingLinesByBO update error, ea: {} syncTermServiceLine: {} forUpdate: {}", ea, syncTermServiceLine, forUpdate, e);
            }
        }
    }

    /**
     *
     * @param ea
     * @param synDimensionality 同步的市场活动维度
     * @param marketingEventIdList 市场活动id
     * @param marketingKeywordIdList 关键词对象id
     * @param keywordIdList 广告平台的关键词id
     * @return key: 如果关键词投放计划存在外部id字段，key就是这个外部id字段，否则还是沿用老的唯一标识：市场活动对象id-关键词对象id
     */
    private Map<String, ObjectData> getExistKeywordServingPlanMap(String ea, TypeEnum synDimensionality, List<String> marketingEventIdList, List<String> marketingKeywordIdList, List<Long> keywordIdList) {
        marketingEventIdList = marketingEventIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        marketingKeywordIdList = marketingKeywordIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketingEventIdList) || CollectionUtils.isEmpty(marketingKeywordIdList) || CollectionUtils.isEmpty(keywordIdList)) {
            return Maps.newHashMap();
        }
        String keywordServingPlanUniqueKeyFormat = "%s-%s";
        Map<String, ObjectData> keywordServingPlanMap = Maps.newHashMap();
        List<String> selectFieldList = Lists.newArrayList("_id", "name", "marketing_keyword_id", "sub_marketing_event_id", "marketing_event_id", "out_platform_data_id");
        PaasQueryArg queryArg = new PaasQueryArg(0, 1);
        if (adCommonManager.isSyncAdGroupDimensionality(synDimensionality)) {
            queryArg.addFilter("sub_marketing_event_id", FilterOperatorEnum.IN.getValue(), marketingEventIdList);
        } else {
            queryArg.addFilter("marketing_event_id", FilterOperatorEnum.IN.getValue(), marketingEventIdList);
            queryArg.addFilter("sub_marketing_event_id", FilterOperatorEnum.IS.getValue(), Lists.newArrayList(""));
        }
        queryArg.addFilter("marketing_keyword_id", FilterOperatorEnum.IN.getValue(), marketingKeywordIdList);
        String outPlatformDataIdFieldName = "out_platform_data_id";
//TODO 全网之后去掉兼容历史逻辑时，要打开该注释        if (crmV2Manager.checkObjectFieldExist(ea, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), outPlatformDataIdFieldName)) {
//            queryArg.addFilter(outPlatformDataIdFieldName, FilterOperatorEnum.IN.getValue(), keywordIdList.stream().map(String::valueOf).collect(Collectors.toList()));
//        }
        List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.MARKETING_KEYWORD_PLAN.getName(), selectFieldList, queryArg);
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            for (ObjectData objectData : objectDataList) {
                // 如果对象有外部id，直接用这个字段作为key
                String outPlatformDataId = objectData.getString(outPlatformDataIdFieldName);
                if (StringUtils.isNotBlank(outPlatformDataId)) {
                    keywordServingPlanMap.put(outPlatformDataId, objectData);
                    continue;
                }
                String marketingEventId = adCommonManager.isSyncAdGroupDimensionality(synDimensionality) ? objectData.getString("sub_marketing_event_id") : objectData.getString("marketing_event_id");
                String marketingKeywordId = objectData.getString("marketing_keyword_id");
                String uniqueKey = String.format(keywordServingPlanUniqueKeyFormat, marketingEventId, marketingKeywordId);
                keywordServingPlanMap.put(uniqueKey, objectData);
            }
        }
       return keywordServingPlanMap;
    }

    private List<ObjectData> getExistsTermServingLine(String ea, List<SyncTermServiceLineBO> syncTermServiceLineList) {
        Set<String> keywordServingPlanIdSet = Sets.newHashSet();
        Set<String> launchDateSet = Sets.newHashSet();
        for (SyncTermServiceLineBO syncTermServiceLineBO : syncTermServiceLineList) {
            keywordServingPlanIdSet.add(syncTermServiceLineBO.getKeywordServingPlanId());
            launchDateSet.add(String.valueOf(DateUtil.parse3(syncTermServiceLineBO.getActionDate()).getTime()));
        }
        List<ObjectData> resultList = Lists.newArrayList();
        // 日期不能用in查询，理论上launchDateSet也只会有一个
        for (String launchDate : launchDateSet) {
            PaasQueryArg queryArg = new PaasQueryArg(0, 1);
            queryArg.addFilter("keyword_serving_plan_id", FilterOperatorEnum.IN.getValue(), Lists.newArrayList(keywordServingPlanIdSet));
            queryArg.addFilter("launch_date", FilterOperatorEnum.EQ.getValue(), Lists.newArrayList(launchDate));
            List<String> selectFieldList = Lists.newArrayList("_id", "name", "marketing_event_id", "marketing_keyword_id", "launch_date", "show_content", "click_count", "advertising_costs", "keyword_serving_plan_id");
            List<ObjectData> objectDataList = crmV2Manager.getAllObjByQueryArg(ea, CrmObjectApiNameEnum.TERM_SERVING_LINES.getName(), selectFieldList,  queryArg);
            if (CollectionUtils.isNotEmpty(objectDataList)) {
                resultList.addAll(objectDataList);
            }
        }
        return resultList;
    }

}
