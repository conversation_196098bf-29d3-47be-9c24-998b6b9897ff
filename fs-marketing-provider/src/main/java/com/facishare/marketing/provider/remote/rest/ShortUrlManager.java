package com.facishare.marketing.provider.remote.rest;

import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.dao.ShortUrlConfigDao;
import com.facishare.marketing.provider.entity.ShortUrlConfigEntity;
import com.facishare.marketing.provider.innerResult.BatchShortUrlResult;
import com.facishare.marketing.provider.remote.rest.arg.BatchCreateShortUrlsArg;
import com.facishare.marketing.provider.remote.rest.arg.CreateShortUrlArg;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by zhengh on 2020/6/11.
 */
@Component
@Slf4j
public class ShortUrlManager {
    @Autowired
    private ShortUrlConfigDao shortUrlConfigDao;
    @Value("${short.link.rest.url}")
    private String shortLinkUrl;
    //短链rest返回的域名地址,用于正则匹配短链并转回长链。测试环境为https://fs8.ceshi112.com
    @Value("${short.link.domain}")
    private String shortLinkDomain;

    @ReloadableProperty("short.link.rest.url.v2")
    private String shortLinkUrlV2;

    private static final OkHttpClient okHttpClient = new OkHttpClient();

    private static final String RANDOM_REGEX = "/......";

    public boolean isShortUrl(String url){
        if (StringUtils.isEmpty(url)){
            return false;
        }

        return url.startsWith(shortLinkDomain) || url.startsWith(shortLinkDomain.replace("https://", "").replace("http://", ""));
    }
    /**
     * 得到content内容中的所有短链
     * @param content 输入内容
     * @return 返回短链的list
     */
    public List<String> getShortUrlsByRegex(String content) {
        if (Strings.isNullOrEmpty(content)) {
            return new LinkedList<>();
        }
        String shortLinkDomainWithoutProtocol = shortLinkDomain.replace("https://", "").replace("http://", "") + RANDOM_REGEX;
        String regex = "(https?:\\/\\/)?" + shortLinkDomainWithoutProtocol;
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(content);
        List<String> shortUrls = new LinkedList<>();
        while (m.find()) {
            shortUrls.add(m.group(0));
        }
        regex = "https://wxaurl.cn/.............";
        p = Pattern.compile(regex);
        m = p.matcher(content);
        while (m.find()) {
            shortUrls.add(m.group(0));
        }
        return shortUrls;
    }

    public Map<String, String> getShortUrl2LongUrlMap(String content) {
        List<String> shortUrls = getShortUrlsByRegex(content);
        if (CollectionUtils.isEmpty(shortUrls)) {
            return new HashMap<>();
        }
        Map<String, Map<String, String>> dbMap = shortUrlConfigDao.batchGetLongUrl(shortUrls);
        Map<String, String> result = new HashMap<>();
        for (String shortUrl : shortUrls) {
            if (shortUrl.startsWith(shortLinkDomain) || shortUrl.startsWith(shortLinkDomain.replace("https://", "").replace("http://", ""))) {
                Optional<String> longUrlOption = shortUrlRandom2LongUrlV2(shortUrl.substring(shortLinkDomain.length() + 1));
                longUrlOption.ifPresent(s -> result.put(shortUrl, s));
                continue;
            }
            if (shortUrl.startsWith("https://wxaurl.cn")) {
                Map<String, String> map = dbMap.get(shortUrl);
                if (map != null) {
                    result.put(shortUrl, map.get("long_url"));
                }
            }
        }
        return result;
    }

    public Map<String, String> getShortUrl2LongUrlMapV2(String content) {
        List<String> shortUrls = getShortUrlsByRegex(content);
        if (CollectionUtils.isEmpty(shortUrls)) {
            return new HashMap<>();
        }
        Map<String, Map<String, String>> dbMap = shortUrlConfigDao.batchGetLongUrl(shortUrls);
        Map<String, String> result = new HashMap<>();
        for (String shortUrl : shortUrls) {
            if (shortUrl.startsWith(shortLinkDomain) || shortUrl.startsWith(shortLinkDomain.replace("https://", "").replace("http://", ""))) {
                Optional<String> longUrlOption = shortUrlRandom2LongUrlV2(shortUrl.substring(shortLinkDomain.length() + 1));
                longUrlOption.ifPresent(s -> result.put(shortUrl, s));
                continue;
            }
            if (shortUrl.startsWith("https://wxaurl.cn")) {
                Map<String, String> map = dbMap.get(shortUrl);
                if (map != null) {
                    result.put(shortUrl, map.get("long_url"));
                }
            }
        }
        return result;
    }

    /**
     * @param longPath 完整小程序路径，含参数，如：/pages/ab?c=12&d=ef
     * @return 若曾经生成过，返回14天内生成的URL，若不是14天内或从未生成过则返回null
     */
    public String getGeneratedWxAppUrl(String longPath) {
        if (StringUtils.isEmpty(longPath)) {
            return null;
        }
        ShortUrlConfigEntity urlConfigEntity = shortUrlConfigDao.getLastByLongUrl(longPath);
        // 14 × 24 × 60 × 60 × 1000 = 1209600000
        if (urlConfigEntity == null || System.currentTimeMillis() > urlConfigEntity.getUpdateTime().getTime() + 1209600000) {
            return null;
        }
        return urlConfigEntity.getShortUrl();
    }

    /**
     * 批量获取微信小程序14天内生成的短链
     * @param longPaths 完整小程序路径的集合，含参数，如：/pages/ab?c=12&d=ef
     * @return k长链接 - v若曾经生成过，返回14天内生成的URL
     */
    public Map<String, String> batchGetGeneratedWxAppUrl(Collection<String> longPaths) {
        if (longPaths == null || longPaths.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, Map<String, Object>> map = shortUrlConfigDao.batchGetLastByLongUrl(longPaths);
        if (map == null || map.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, String> result = new HashMap<>();
        for (Entry<String, Map<String, Object>> entry : map.entrySet()) {
            Map<String, Object> valueMap = entry.getValue();
            // 14 × 24 × 60 × 60 × 1000 = 1209600000
            if (System.currentTimeMillis() < ((Date) valueMap.get("update_time")).getTime() + 1209600000) {
                result.put(entry.getKey(), (String) valueMap.get("short_url"));
            }
        }
        return result;
    }

    public Optional<String> getLongUrlByShortUrl(String shortUrl){
        if (StringUtils.isEmpty(shortUrl)){
            return Optional.empty();
        }
        return shortUrlRandom2LongUrlV2(shortUrl.substring(shortLinkDomain.length() + 1));
    }

    /**
     * 短链服务rest接口调用，创建短链
     */
    public Optional<String> createShortUrl(CreateShortUrlArg arg){
        return createShortUrlV2(arg);
    }

    public Optional<String> createShortUrlV2(CreateShortUrlArg arg){
        String url = shortLinkUrlV2 + "/api/v2/private-short-urls";
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        Result<LongUrlToShortUrlData> result = null;
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(mediaType, JsonUtil.toJson(arg)))
                .build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == 200 && response.body() != null){
                String body = response.body().string();
                result = JsonUtil.fromJson(body, new TypeToken<Result<LongUrlToShortUrlData>>() {}.getType());
            }
        } catch (Exception e){
            log.error("ShortUrlManager.createShortUrlV2 exception arg:{} e:", arg, e);
        }
        String shortUrl = null;
        if (result != null && result.getData() != null && result.getCode() == 200) {
            shortUrl = result.getData().getShortUrl();
        }
        log.info("CreateShortUrlManager.createShortUrlV2 arg: {}, result:{}", arg, result);
        return Optional.ofNullable(shortUrl);
    }

    public BatchShortUrlResult batchCreateShortUrl(BatchCreateShortUrlsArg arg){
        return batchCreateShortUrlV2(arg);
    }

    public BatchShortUrlResult batchCreateShortUrlV2(BatchCreateShortUrlsArg arg){
        String url = shortLinkUrlV2 + "/api/v2/private-short-urls/batch";
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        String content = GsonUtil.toJson(arg);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(mediaType, content))
                .build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == 200 && response.body() != null){
                String body = response.body().string();
                Result<BatchLongUrlToShortUrlData> result = JsonUtil.fromJson(body, new TypeToken<Result<BatchLongUrlToShortUrlData>>() {}.getType());
                if (result != null && result.getCode() == 200 && result.getData() != null && result.getData().getResult() != null) {
                    BatchShortUrlResult batchShortUrlResult = new BatchShortUrlResult();
                    Map<String, String> shortUrlMapping = Maps.newHashMap();
                    for (Entry<String, LongUrlToShortUrlData> entry : result.getData().getResult().entrySet()) {
                        String longUrl = entry.getKey();
                        String shortUrl = entry.getValue().getShortUrl();
                        shortUrlMapping.put(longUrl, shortUrl);
                    }
                    batchShortUrlResult.setShortUrlMapping(shortUrlMapping);
                    return batchShortUrlResult;
                }
            }
        }catch (Exception e){
            log.error("ShortUrlManager.createShortUrlV2 exception arg:{} e:",arg, e);
        }
        return null;
    }

    public Optional<String> createShortUrl(String url){
        CreateShortUrlArg arg = new CreateShortUrlArg();
        arg.setUrl(url);
        return createShortUrlV2(arg);
    }

    public Optional<String> shortUrlRandom2LongUrlV2(String shortUrlRandom) {
        if (StringUtils.isEmpty(shortUrlRandom)) {
            return Optional.empty();
        }
        String requestUrl = shortLinkUrlV2 + "/api/v2/private-short-urls/" + shortUrlRandom;
        Request request = new Request.Builder()
                .url(requestUrl)
                .addHeader("Content-Type", "text/plain")
                .get()
                .build();
        String longUrl = null;
        try {
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() == 200 && response.body() != null){
                String body = response.body().string();
                Result<ShortCodeToLongUrlData> result = JsonUtil.fromJson(body, new TypeToken<Result<ShortCodeToLongUrlData>>() {}.getType());
                if (result != null && result.getData() != null && result.getCode() == 200) {
                    longUrl = result.getData().getOriginalUrl();
                }
            }
        }catch (Exception e){
            log.error("ShortUrlManager.shortUrl2LongUrl exception shortUrl:{} e:", shortUrlRandom, e);
        }

        return Optional.ofNullable(longUrl);
    }

    public void setShortUrlConfig(Map<String, String> shortUrlMap) {
        if (shortUrlMap != null && !shortUrlMap.isEmpty()) {
            Set<String> shortUrls = shortUrlMap.keySet();
            Map<String, Map<String, String>> map = shortUrlConfigDao.batchGetLongUrl(shortUrls);
            for (Entry<String, String> shortAndLong : shortUrlMap.entrySet()) {
                String shortUrl = shortAndLong.getKey();
                String longUrl = shortAndLong.getValue();
                Map<String, String> dbMap = map.get(shortUrl);
                if (dbMap == null) {
                    shortUrlConfigDao.add(shortUrl, longUrl);
                    continue;
                }
                if (!longUrl.equals(dbMap.get("long_url"))) {
                    shortUrlConfigDao.update(shortUrl, longUrl);
                }
            }
        }
    }

    @Data
    public static class Result<T> {
        private Integer code;

        private String message;

        private T data;
    }

    @Data
    public static class  LongUrlToShortUrlData {
        private String shortUrl;
        private String code;
    }

    @Data
    public static class  BatchLongUrlToShortUrlData {
        private Map<String, LongUrlToShortUrlData> result;
    }

    @Data
    public static class  ShortCodeToLongUrlData {
        private String originalUrl;
    }
}
