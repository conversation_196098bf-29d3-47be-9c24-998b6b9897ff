package com.facishare.marketing.provider.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.service.OuterPhoneService;
import com.facishare.mankeep.common.result.ModelResult;
import com.facishare.marketing.api.arg.AccountIsApplyForKISArg;
import com.facishare.marketing.api.arg.appMenu.GetShowAppMenuTemplateArg;
import com.facishare.marketing.api.arg.sms.SendVerificationCodeArg;
import com.facishare.marketing.api.result.account.*;
import com.facishare.marketing.api.result.qywx.QywxBaseInfoResult;
import com.facishare.marketing.api.result.qywx.ResetAppUserDataResult;
import com.facishare.marketing.api.service.AccountService;
import com.facishare.marketing.api.service.appMenu.AppMenuTemplateService;
import com.facishare.marketing.api.service.sms.SendService;
import com.facishare.marketing.api.vo.appMenu.AppMenuTemplateDetailVO;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.account.ApplyBindStatusForKISEnum;
import com.facishare.marketing.common.enums.account.BindUserAndWxType;
import com.facishare.marketing.common.enums.appMenu.MiniAppVisitTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.FsEnterpriseBindTypeEnum;
import com.facishare.marketing.common.enums.qywx.QywxEmployeeBindWxUserStatus;
import com.facishare.marketing.common.enums.qywx.ResetDataStatusEnum;
import com.facishare.marketing.common.enums.qywx.ResetDataStatusTypeEnum;
import com.facishare.marketing.common.enums.user.UserRelationTypeEnum;
import com.facishare.marketing.common.enums.user.UserTypeEnum;
import com.facishare.marketing.common.model.RedisKISApplyInfoEntity;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.util.MD5Util;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.distribution.DistributionPlanDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerAppInfoDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.qywx.ResetDataStatusDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.distribution.DistributePlanEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerAppInfoEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult.StaffInfo;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.MemberMarketingManager;
import com.facishare.marketing.provider.manager.FsBindManager;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.miniappLogin.WxMiniappLoginManager;
import com.facishare.marketing.provider.manager.qywx.QywxBindAppUserManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.wechat.union.core.api.service.OuterServiceWechatService;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.enterpriserelation2.arg.GetDownstreamEmployeeInfoArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName AccountServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2019/2/25 3:08 PM
 */

@Slf4j
@Service("accountService")
public class AccountServiceImpl implements AccountService {

    @Autowired
    private AccountDAO accountDAO;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private QywxBindAppUserManager qywxBindAppUserManager;
    @Autowired
    private ResetDataStatusDAO resetDataStatusDAO;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private WechatAccountConfigDao wechatAccountConfigDao;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private SendService sendService;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private DistributionPlanDAO distributePlanDAO;
    @Autowired
    private OuterPhoneService outerPhoneService;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;
    @Autowired
    private ObjectManager objectManager;
    @Autowired
    private OuterServiceWechatService outerServiceWechatService;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private WxMiniappLoginManager wxMiniappLoginManager;

    @Autowired
    private UserRelationManager userRelationManager;

    @Value("${qywx.crm.appid}")
    private String qywxCrmAppid;

    @Autowired
    private PublicEmployeeService publicEmployeeService;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;
    @Autowired
    private QywxCustomerAppInfoDAO qywxCustomerAppInfoDAO;

    @Value("${partner.appid}")
    private String partnerAppId;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    @Autowired
    private AppMenuTemplateService appMenuTemplateService;

    @Override
    public Result<AccountIsApplyForKISResult> isApplyForKIS(AccountIsApplyForKISArg arg) {
        if (arg == null) {
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        // 如果是在伙伴营销中调用
        if (arg.isPartner()) {
            return isApplyForPartner(arg.getUpstreamEa(), arg.getOuterTenantId(), arg.getOuterUid());
        }
        String ea = arg.getEa();
        Integer userId = arg.getFsUserId();
        if (StringUtils.isBlank(ea)) {
            log.warn("AccountServiceImpl.isApplyForKIS ea is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        if (null == userId) {
            log.warn("AccountServiceImpl.isApplyForKIS userId is null");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }

        int ei = eieaConverter.enterpriseAccountToId(ea);
        // 纷享企业员工信息接口数据
        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEmployeeId(userId);
        employeeDtoArg.setEnterpriseId(ei);
        GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
        if (null == employeeDtoResult) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        EmployeeDto employeeDto = employeeDtoResult.getEmployee();
        if (null == employeeDto) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }

        AccountIsApplyForKISResult accountIsApplyForFSResult = new AccountIsApplyForKISResult();
        accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
        FsEnterpriseBindTypeEnum fsEnterpriseBindTypeEnum = qywxManager.fsEnterpriseBindType(ea);
        String applyInfoKey = MD5Util.md5String(userId + ea + employeeDto.getMobile());
        if (!fsEnterpriseBindTypeEnum.equals(FsEnterpriseBindTypeEnum.BIND_QYWX_AND_MINIAPPPRO)) {
            RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea, userId, ei, employeeDto.getMobile());
            redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
        }

        // 判断绑定类型
        if (fsEnterpriseBindTypeEnum.equals(FsEnterpriseBindTypeEnum.BIND_QYWX_AND_MINIAPPPRO)) {
            QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
            return applyForKisQywxEa(ea, qywxMiniappConfigEntity.getCorpid(), qywxMiniappConfigEntity.getAppid(), employeeDto, accountIsApplyForFSResult, ei);
        } else if (fsEnterpriseBindTypeEnum.equals(FsEnterpriseBindTypeEnum.NOT_BIND_QYWX_BIND_MINIAPPPRO)) {
            return applyForKisNotBindQywx(ea, employeeDto, accountIsApplyForFSResult, applyInfoKey);
        }

        //下面这些应该是走不到了
        AccountEntity accountEntity = qywxUserManager.getAccountByPhone(ea, employeeDto.getMobile());
        if (accountEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }

        accountIsApplyForFSResult.setUid(accountEntity.getUid());

        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(accountEntity.getUid());
        if (fsBindEntity == null) {
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPPYED_NOBIND.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }

        // 纷享企业信息接口数据
        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(fsBindEntity.getFsEa());
        enterpriseDataArg.setEnterpriseId(fsBindEntity.getFsCorpId());
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        if (null == enterpriseDataResult) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND);
        }
        EnterpriseData enterpriseData = enterpriseDataResult.getEnterpriseData();
        if (null == enterpriseData) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND);
        }

        if (fsBindEntity.getFsEa().equals(ea) && fsBindEntity.getFsUserId().equals(userId
        ) && fsBindEntity.getFsCorpId().equals(ei)) {
            accountIsApplyForFSResult.setBindCompanyName(enterpriseData.getEnterpriseName());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }

        accountIsApplyForFSResult.setBindCompanyName(enterpriseData.getEnterpriseName());
        accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPPYED_BINDED_OTHER_EA.getType());
        return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
    }

    private Result<AccountIsApplyForKISResult> isApplyForPartner(String upstreamEa, String outerTenantIdStr, String outerUidStr) {
        if (StringUtils.isBlank(upstreamEa) || StringUtils.isBlank(outerTenantIdStr) || StringUtils.isBlank(outerUidStr)) {
            log.warn("AccountServiceImpl.isApplyForPartner upstreamEa or outerTenantId or outerUid is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        long outerTenantId = Long.parseLong(outerTenantIdStr);
        long outerUid = Long.parseLong(outerUidStr);
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByOutTenantIdAndOuterUid(upstreamEa, Lists.newArrayList(outerTenantId), Lists.newArrayList(outerUid));
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        UserRelationEntity userRelationEntity = userRelationEntityList.get(0);

        AccountIsApplyForKISResult accountIsApplyForFSResult = new AccountIsApplyForKISResult();
        accountIsApplyForFSResult.setPhone(userRelationEntity.getMobile());

        int ei = eieaConverter.enterpriseAccountToId(upstreamEa);
        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(upstreamEa);
        enterpriseDataArg.setEnterpriseId(ei);
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        if (null == enterpriseDataResult || enterpriseDataResult.getEnterpriseData() == null) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND);
        }
        String enterpriseName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
        int fsUserId = userRelationEntity.getFsUserId();
        String userType = userRelationEntity.getType();
        // 如果该用户的类型不是伙伴的，禁止在app端的伙伴营销开通名片
        if (UserRelationTypeEnum.EMPLOYEE.getCode().equals(userType)) {
            return new Result<>(SHErrorCode.EMPLOYEE_TYPE_FORBID_BIND_PARTNER_CARD.getErrorCode(), I18nUtil.get(I18nKeyEnum.EMPLOYEE_TYPE_FORBID_BIND_PARTNER_CARD) + enterpriseName);
        }
        if (UserRelationTypeEnum.MEMBER.getCode().equals(userType)) {
            return new Result<>(SHErrorCode.MEMBER_TYPE_FORBID_BIND_PARTNER_CARD.getErrorCode(), I18nUtil.get(I18nKeyEnum.MEMBER_TYPE_FORBID_BIND_PARTNER_CARD) + enterpriseName);
        }
        // 如果该互联用户找到了绑定关系，那么意味着已经绑定了，只有能够进入app-伙伴营销应用的才有资格开通名片
        String uid = fsBindManager.queryByFsEaAndUserId(upstreamEa, fsUserId);
        if (StringUtils.isNotBlank(uid)) {
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
            accountIsApplyForFSResult.setBindCompanyName(enterpriseName);
            accountIsApplyForFSResult.setUid(uid);
            return Result.newSuccess(accountIsApplyForFSResult);
        }
        // 这里返回未开通名片,返回applyInfoKey，通过调用wxLogin接口开通名片，wxLogin会用到applyInfoKey来解析出开通名片的用户
        String applyInfoKey = MD5Util.md5String(fsUserId + upstreamEa + outerTenantId + outerUid);
        RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(upstreamEa, fsUserId, outerTenantId, outerUid);
        redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
        accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
        accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
        return Result.newSuccess(accountIsApplyForFSResult);
    }

    @Override
    public Result<AccountIsApplyForKISResult> isApplyForQyWxKIS(String ea, Integer userId, String uid, String appId) {
        if (StringUtils.isBlank(uid)) {
            log.warn("AccountServiceImpl.isApplyForQyWxKIS uid is blank");
            return new Result<>(SHErrorCode.PARAMS_ERROR);
        }
        String miniAppVisitType = getMenuTemplateMiniAppVisitType(ea, userId);
        AccountIsApplyForKISResult accountIsApplyForFSResult = new AccountIsApplyForKISResult();
        accountIsApplyForFSResult.setMiniappVisitType(miniAppVisitType);
        if (StringUtils.isBlank(ea) || userId == null) {
            // 没有绑定纷享账号的游客
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPPYED_NOBIND.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        } else {
            UserRelationEntity userRelationEntity = userRelationManager.getByEaAndUid(ea, uid);
            if (userRelationEntity != null) {
                // 返回该用户的类型
                accountIsApplyForFSResult.setUserType(userRelationEntity.getType());
            }
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            /*GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
            employeeDtoArg.setEmployeeId(userId);
            employeeDtoArg.setEnterpriseId(ei);
            GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
            if (null == employeeDtoResult) {
                return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
            }
            EmployeeDto employeeDto = employeeDtoResult.getEmployee();
            if (null == employeeDto) {
                return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
            }*/
            AccountEntity accountEntity = accountDAO.queryAccountByUid(uid);
            if (accountEntity == null) {
                accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NOAPPPY_NOBIND.getType());
                return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
            }
            accountIsApplyForFSResult.setPhone(accountEntity.getPhone());
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            // 判断是不是会员营销，如果是会员员工/伙伴 直接返回
            if (fsBindEntity != null && QywxUserConstants.isMemberVirtualUserId(fsBindEntity.getFsUserId())) {
                String memberType = memberMarketingManager.getMemberType(ea, fsBindEntity.getFsUserId());
                int status;
                if (MemberTypeEnum.PARTNER.getType().equals(memberType)) {
                    status = ApplyBindStatusForKISEnum.MEMBER_PARTNER.getType();
                } else if (MemberTypeEnum.EMPLOYEE.getType().equals(memberType) || MemberTypeEnum.SPREAD_EMPLOYEE.getType().equals(memberType)) {
                    status = ApplyBindStatusForKISEnum.MEMBER_EMPLOYEE.getType();
                } else {
                    status = ApplyBindStatusForKISEnum.WITHOUT_MARKETING_LICENSE.getType();
                }
                accountIsApplyForFSResult.setStatus(status);
                return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
            }
            if (fsBindEntity == null) {
                // 已注册但之前未绑定（主动绑定）
                fsBindEntity = new FSBindEntity();
                fsBindEntity.setUid(uid);
                fsBindEntity.setFsEa(ea);
                fsBindEntity.setFsUserId(userId);
                fsBindEntity.setFsCorpId(ei);
                fsBindEntity.setType(AccountTypeEnum.QYWX_MINI_APP.getType());
                fsBindEntity.setAppId(appId);
                fsBindManager.insert(fsBindEntity);
            } else {
                if (!fsBindEntity.getFsEa().equals(ea) || !fsBindEntity.getFsUserId().equals(userId
                ) || !fsBindEntity.getFsCorpId().equals(ei)) {
                    // 若绑定且换绑
                    fsBindEntity.setUid(uid);
                    fsBindEntity.setFsEa(ea);
                    fsBindEntity.setFsUserId(userId);
                    fsBindEntity.setFsCorpId(ei);
                    fsBindManager.update(fsBindEntity);
                }
            }
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
    }

    private String getMenuTemplateMiniAppVisitType(String ea, Integer userId) {
        String miniAppVisitType = MiniAppVisitTypeEnum.VISIT_MAIN_PAGE.getCode();
        try {
            GetShowAppMenuTemplateArg getShowAppMenuTemplateArg = new GetShowAppMenuTemplateArg();
            getShowAppMenuTemplateArg.setEa(ea);
            getShowAppMenuTemplateArg.setFsUserId(userId);
            Result<AppMenuTemplateDetailVO> showTemplateResult = appMenuTemplateService.getShowAppMenuTemplate(getShowAppMenuTemplateArg);
            if (showTemplateResult.isSuccess() && StringUtils.isNotBlank(showTemplateResult.getData().getMiniappVisitType())) {
                miniAppVisitType = showTemplateResult.getData().getMiniappVisitType();
            }
        } catch (Exception e) {
            log.error("isApply getMenuTemplateMiniAppVisitType error, ea: {} userId: {}", ea, userId, e);
        }
        return miniAppVisitType;
    }

    @Override
    public Result<GetFsUserInfoResult> getFsUserInfo(String ea, Integer fsUserId, String uid) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            log.warn("AccountServiceImpl.getFsUserInfo ea or fsUserId is null");
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        if (null == ei) {
            log.warn("AccountServiceImpl.getFsUserInfo ei is null");
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        GetFsUserInfoResult getFsUserInfoResult = new GetFsUserInfoResult();
        getFsUserInfoResult.setEa(ea);
        getFsUserInfoResult.setUid(uid);
        if (QywxUserConstants.isPartnerVirtualUserId(fsUserId) || QywxUserConstants.isMemberVirtualUserId(fsUserId)) {
            getFsUserInfoResult.setFsUserId(fsUserId);
            UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, fsUserId);
            if (userRelationEntity != null) {
                getFsUserInfoResult.setUid(userRelationEntity.getUid());
            }
            return Result.newSuccess(getFsUserInfoResult);
        } else if (QywxUserConstants.isVirtualUserId(fsUserId)){
            String qywxUserId = qywxVirtualFsUserManager.queryQyUserIdByVirtualInfo(ea, fsUserId);
            if (qywxUserId == null) {
                log.warn("AccountServiceImpl.getFsUserInfo queryQyUserIdByVirtualInfo return failed ea:{} fsUserId:{}", ea, fsUserId);
                getFsUserInfoResult.setFsUserId(fsUserId);
                return Result.newSuccess(getFsUserInfoResult);
            }

            List<String> outAccountList = Lists.newArrayList(qywxUserId);
            Result<Map<String, String>> outAccountToFsAccountBatchResult = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppid, outAccountList);
            if (!outAccountToFsAccountBatchResult.isSuccess() || MapUtils.isEmpty(outAccountToFsAccountBatchResult.getData())){
                getFsUserInfoResult.setFsUserId(fsUserId);
                return Result.newSuccess(getFsUserInfoResult);
            }

            Map<String, String> userIdMap = outAccountToFsAccountBatchResult.getData();
            String fsUserIdString = userIdMap.get(qywxUserId);
            if (StringUtils.isBlank(fsUserIdString)){
                getFsUserInfoResult.setFsUserId(fsUserId);
                return Result.newSuccess(getFsUserInfoResult);
            }
            String[] split = fsUserIdString.split("\\.");
            Integer newFsUserId = Integer.parseInt(split[split.length - 1]);
            //更新虚拟表&绑定表
            qywxUserManager.qywxUserBindFsUserFromVirtualUser(ea, fsUserId, newFsUserId, qywxUserId);
            getFsUserInfoResult.setFsUserId(newFsUserId);
            return Result.newSuccess(getFsUserInfoResult);
        }

        // 纷享企业员工信息接口数据
        GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
        employeeDtoArg.setEmployeeId(fsUserId);
        employeeDtoArg.setEnterpriseId(ei);
        GetEmployeeDtoResult employeeDtoResult = employeeService.getEmployeeDto(employeeDtoArg);
        if (null == employeeDtoResult) {
            log.warn("AccountServiceImpl.getFsUserInfo employeeDtoResult is null ea:{}, fsUserId:{}", ea, fsUserId);
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        EmployeeDto employeeDto = employeeDtoResult.getEmployee();
        if (null == employeeDto) {
            log.warn("AccountServiceImpl.getFsUserInfo employeeDto is null ea:{}, fsUserId:{}", ea, fsUserId);
            return new Result<>(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }

        getFsUserInfoResult.setEa(ea);
        getFsUserInfoResult.setFsUserId(fsUserId);
        getFsUserInfoResult.setUserName(employeeDto.getName());
        getFsUserInfoResult.setUid(uid);
        return new Result<>(SHErrorCode.SUCCESS, getFsUserInfoResult);
    }

    @Override
    public Result sendSMCode(String phone, Integer objectType, String objectId){
        SendVerificationCodeArg arg = new SendVerificationCodeArg();
        arg.setPhone(phone);
        if (objectType == null || StringUtils.isEmpty(objectId)){
            arg.setEa(null);
        }else {
            String ea = null;
            if (objectType == ObjectTypeEnum.HEXAGON_SITE.getType()) {
                HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(objectId);
                ea = hexagonSiteEntity == null ? null : hexagonSiteEntity.getEa();
            } else if (objectType == ObjectTypeEnum.CUSTOMIZE_FORM.getType()) {
                CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(objectId);
                ea = customizeFormDataEntity == null ? null : customizeFormDataEntity.getEa();
            } else if (objectType == ObjectTypeEnum.DISTRIBUTION.getType()) {
                DistributePlanEntity distributePlanEntity = distributePlanDAO.getDistributePlanById(objectId);
                ea = distributePlanEntity == null ? null : distributePlanEntity.getFsEa();
            }else if (objectType == com.facishare.mankeep.common.enums.ObjectTypeEnum.PRODUCT.getType()){
                ProductEntity productEntity = productDAO.queryProductDetail(objectId);
                ea = productEntity == null ? null : productEntity.getFsEa();
            }
            arg.setEa(ea);
        }

        return sendService.sendVerificationCode(arg);
    }

    @Override
    public Result checkSMCode(String phone, String verifyCode) {
        ModelResult modelResult = outerPhoneService.checkVerifyNormalSMCode(phone, verifyCode);
        if (modelResult.isSuccess()) {
            return new Result<>(SHErrorCode.SUCCESS);
        } else {
            return new Result<>(modelResult.getErrCode(), modelResult.getErrMsg());
        }
    }

    @Override
    public Result<String> bindToWxWorkExternalUser(String ea, String uid, String wxWorkExternalUserId) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(uid));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxWorkExternalUserId));
        return Result.newSuccess(userMarketingAccountRelationManager.bindMiniappUserAndWxWorkExternalUser(ea, uid, wxWorkExternalUserId,"bindToWxWorkExternalUser").orElse(null));
    }

    @Override
    public Result<ResetAppUserDataResult> resetAppUserData(String ea, Integer userId) {
        ResetAppUserDataResult result = new ResetAppUserDataResult();
        ResetDataStatusEntity resetDataStatusEntity = resetDataStatusDAO.getResetDataStatusDataByUser(ea, userId, ResetDataStatusTypeEnum.QYWX_VIRTUAL_DATA.getType());
        String updateId = UUIDUtil.getUUID();
        if (resetDataStatusEntity != null) {
            result.setStatus(resetDataStatusEntity.getStatus());
            return Result.newSuccess(result);
        } else {
            // 添加数据刷新记录
            ResetDataStatusEntity saveData = new ResetDataStatusEntity();
            saveData.setId(updateId);
            saveData.setEa(ea);
            saveData.setFsUserId(userId);
            saveData.setType(ResetDataStatusTypeEnum.QYWX_VIRTUAL_DATA.getType());
            saveData.setStatus(ResetDataStatusEnum.UPDATING.getStatus());
            resetDataStatusDAO.insert(saveData);
        }
        // 开始同步数据
        Result bindResult = qywxBindAppUserManager.bindAppUser(ea, userId, updateId);
        if (bindResult.isSuccess()) {
            result.setStatus(ResetDataStatusEnum.UPDATING.getStatus());
            return Result.newSuccess(result);
        } else {
            resetDataStatusDAO.deleteDataById(updateId);
            return bindResult;
        }
    }

    @Override
    public Result<GetQywxBaseInfoFromWxResult> getQywxBaseInfoFromWx(String targetUid) {
        GetQywxBaseInfoFromWxResult getQywxBaseInfoFromWxResult = new GetQywxBaseInfoFromWxResult();
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(targetUid);
        if (fsBindEntity == null) {
            log.warn("AccountServiceImpl.getQywxBaseInfoFromWx fsBindEntity is null arg:{}", targetUid);
            return Result.newError(SHErrorCode.USER_NOT_BIND_EA);
        }
        QywxCorpAgentConfigEntity qywxCorpAgentConfigEntity = qywxCorpAgentConfigDAO.queryAgentByEa(fsBindEntity.getFsEa());
        if (qywxCorpAgentConfigEntity == null) {
            getQywxBaseInfoFromWxResult.setBindQywx(false);
        } else {
            getQywxBaseInfoFromWxResult.setBindQywx(true);
        }
        return Result.newSuccess(getQywxBaseInfoFromWxResult);
    }

    @Override
    public Result<String> getApplyInfoKeyForWx(String ea, Integer fsUserId) {
        boolean flag = false;
        String uid = fsBindManager.queryByFsEaAndUserId(ea, fsUserId);
        if (StringUtils.isNotBlank(uid)) {
            UserEntity userEntity = userManager.queryByUid(uid);
            if (userEntity != null){
                flag = true;
            }
        }
        if (!flag) {
            String applyInfoKey = MD5Util.md5String(fsUserId + ea);
            RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea,fsUserId, eieaConverter.enterpriseAccountToId(ea), null);
            redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
            return Result.newSuccess(applyInfoKey);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<GetDownstreamEmployeeInfoResult> getDownstreamEmployeeInfo(String erUpstreamEa, String erOuterTenantId, String erOuterUid) {
        GetDownstreamEmployeeInfoResult result = new GetDownstreamEmployeeInfoResult();
        HeaderObj headerMap = HeaderObj.newInstance(99);
        headerMap.setAppId(partnerAppId);

        GetDownstreamEmployeeInfoArg arg = new GetDownstreamEmployeeInfoArg();
        arg.setUpstreamTenantId(eieaConverter.enterpriseAccountToId(erUpstreamEa));
        try {
            arg.setOuterUid(Long.valueOf(erOuterUid));
        }catch (Exception e){
            log.error("AccountServiceImpl.getDownstreamEmployeeInfo  erOuterUid convert to long fail, erOuterUid is :{}", erOuterUid,e);
            return Result.newSuccess();
        }
        arg.setLinkAppId(partnerAppId);

        RestResult<com.fxiaoke.enterpriserelation2.result.GetDownstreamEmployeeInfoResult> downstreamEmployeeInfo = publicEmployeeService.getDownstreamEmployeeInfo(headerMap, arg);
        if(!downstreamEmployeeInfo.isSuccess() || downstreamEmployeeInfo.getData()==null){
            log.warn("AccountServiceImpl.getDownstreamEmployeeInfo getDownstreamEmployeeInfo fail, erUpstreamEa:{}, erUpstreamEa:{}, erOuterUid:{}, downstreamEmployeeInfo:{}",erUpstreamEa,  erUpstreamEa,  erOuterUid,downstreamEmployeeInfo);
            return Result.newSuccess();
        }
        result.setEmployeeName(downstreamEmployeeInfo.getData().getEmployeeName());
        result.setDownstreamEnterpriseName(downstreamEmployeeInfo.getData().getDownstreamEnterpriseName());
        result.setMobile(downstreamEmployeeInfo.getData().getMobile());
        return Result.newSuccess(result);
    }

    @Override
    public Result<QywxEmployeeBindWxUserResult>  queryQywxH5UserBindWxUserInfo(String ea, Integer fsUserId, String qywxCorpId, String qywxUserId, String appId) {
        QywxEmployeeBindWxUserResult result = new QywxEmployeeBindWxUserResult();
        result.setQywxUserId(qywxUserId);
        result.setQywxCorpId(qywxCorpId);
        result.setEa(ea);
        result.setFsUserId(fsUserId);
        result.setBindStatus(QywxEmployeeBindWxUserStatus.UN_BINDED.getStatus());

        UserEntity userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(qywxCorpId, qywxUserId, appId);
        if (userEntity == null) {
            return Result.newSuccess(result);
        }
        result.setUid(userEntity.getUid());
        if (StringUtils.isNotEmpty(userEntity.getOpenid())) {
            result.setBindStatus(QywxEmployeeBindWxUserStatus.BINDED.getStatus());
            return Result.newSuccess(result);
        }else{
            //有企业微信H5 user, 但是没有和个人微信openid做绑定, 需要跳转个微做绑定
            result.setBindStatus(QywxEmployeeBindWxUserStatus.UN_BINDED.getStatus());
            String applyInfoKey = MD5Util.md5String(ea + qywxCorpId + qywxUserId);
            RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea, fsUserId, qywxCorpId, qywxUserId, BindUserAndWxType.QYWX_APP.getValue());
            redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
            result.setApplyInfoKey(applyInfoKey);
            return Result.newSuccess(result);
        }
    }

    @Override
    public Result<GetFsUserInfoResult> queryEmployeeByOfficeAccountOpenId(String officeAccountAppId, String officeAccountOpenId) {
        //通过公众号appid查询企业的ea
        com.facishare.wechat.proxy.common.result.ModelResult<String> bindWxEaResult = outerServiceWechatService.transWxAppIdToFsEa(officeAccountAppId, officeAccountOpenId);
        if (!bindWxEaResult.isSuccess() && bindWxEaResult.getResult() != null){
            log.info("accountService.queryEmployeeByOfficeAccountOpenId failed transWxAppIdToFsEa not success officeAccountAppId:{} officeAccountOpenId:{}", officeAccountAppId, officeAccountOpenId);
            return Result.newError(SHErrorCode.WX_OFFICIAL_NOT_FOUND_EA);
        }

        ObjectData objectData = crmV2Manager.getWechatFanObjByOpenId(bindWxEaResult.getResult(), officeAccountAppId, officeAccountOpenId);
        if (objectData == null || objectData.get("wx_union_id") == null) {
            log.info("accountService.queryEmployeeByOfficeAccountOpenId failed getWechatFanObjByOpenId not success officeAccountAppId:{} officeAccountOpenId:{}", officeAccountAppId, officeAccountOpenId);
            return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        String wxUnionId = (String)objectData.get("wx_union_id");
        //通过unionid查询用户信息
        UserEntity userEntity = userManager.queryByUnionId(wxUnionId);
        if (userEntity == null) {
            log.info("accountService.queryEmployeeByOfficeAccountOpenId queryByUnionId failed user not found wxUnionId:{}", wxUnionId);
            return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(userEntity.getUid());
        if (fsBindEntity == null) {
            log.info("accountService.queryEmployeeByOfficeAccountOpenId queryFSBindByUid failed user not found uid:{}", userEntity.getUid());
            return Result.newError(SHErrorCode.EMPLOYEE_NOT_FOUND);
        }

        GetFsUserInfoResult result = new GetFsUserInfoResult();
        result.setEa(fsBindEntity.getFsEa());
        result.setUid(userEntity.getUid());
        result.setFsUserId(fsBindEntity.getFsUserId());

        return Result.newSuccess(result);

    }

    @Override
    public Result<EmployeeRelationInfo> getEmployeeRelationInfoByFsUserId(String ea, Integer fsUserId) {
        EmployeeRelationInfo employeeRelationInfo = new EmployeeRelationInfo();
        UserRelationEntity userRelationEntity = userRelationManager.getByFsUserId(ea, fsUserId);
        if (userRelationEntity != null) {
            employeeRelationInfo.setUid(userRelationEntity.getUid());
            List<EaWechatAccountBindEntity> eaWechatAccountBindEntities = eaWechatAccountBindDao.getByEa(ea);
            if (CollectionUtils.isNotEmpty(eaWechatAccountBindEntities)) {
                employeeRelationInfo.setAppId(eaWechatAccountBindEntities.get(0).getWxAppId());
            }
            QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentByEa(ea);
            if (agentConfig != null) {
                String corpid = agentConfig.getCorpid();
                employeeRelationInfo.setCorpId(corpid);
                List<QywxCustomerAppInfoEntity> qywxCustomerAppInfoEntities = qywxCustomerAppInfoDAO.selectByCorpIdAndEa(corpid, ea);
                if (CollectionUtils.isNotEmpty(qywxCustomerAppInfoEntities)) {
                    employeeRelationInfo.setAgentId(qywxCustomerAppInfoEntities.get(0).getAgentId());
                }
            }
            employeeRelationInfo.setQyUserId(userRelationEntity.getQywxUserId());
            employeeRelationInfo.setOpenid(userRelationEntity.getOpenId());
            employeeRelationInfo.setDingUserId(userRelationEntity.getDingUserId());
        }
        return Result.newSuccess(employeeRelationInfo);
    }

    public Result<AccountIsApplyForKISResult> applyForKisQywxEa(String ea, String corpId, String appId, EmployeeDto employeeDto, AccountIsApplyForKISResult accountIsApplyForFSResult, Integer ei) {
        // 特殊企业，需能够在绑定企业微信同时在纷享app跳转微信开通账号
        if (fsAddressBookManager.mustUseFxiaokeAddressBook(ea)) {
            return applyForKisQywxSpecialEnterprise(ea, corpId, employeeDto, accountIsApplyForFSResult, ei);
        }
        Result<AccountIsApplyForKISResult> applyForKisQywxEaV2Result = applyForKisQywxEaV2(ea, corpId, appId, employeeDto, accountIsApplyForFSResult, ei);
        log.info("applyForKisQywxEaV2 result:{}", applyForKisQywxEaV2Result);
        if (applyForKisQywxEaV2Result != null && applyForKisQywxEaV2Result.getData() != null) {
            int status = applyForKisQywxEaV2Result.getData().getStatus();
            if (status == ApplyBindStatusForKISEnum.NOAPPPY_NOBIND.getType() || status == ApplyBindStatusForKISEnum.NOT_HAS_SAME_PHONE_WITH_QYWX.getType()) {
                // 绑定了企微，允许在纷享app跳转微信开通微信名片
                int employeeId = employeeDto.getEmployeeId();
                String mobile = employeeDto.getMobile();
                RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea, employeeId, ei, mobile);
                redisFSApplyInfoEntity.setType(BindUserAndWxType.QYWX_JUMP_WX_APP.getValue());
                String applyInfoKey = MD5Util.md5String(employeeId + ea + mobile);
                redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
                return applyForKisNotBindQywx(ea, employeeDto, accountIsApplyForFSResult, applyInfoKey);
            }
        }
        return applyForKisQywxEaV2Result;
    }

    public Result<AccountIsApplyForKISResult> applyForKisQywxEaV2(String ea, String corpId, String appId, EmployeeDto employeeDto, AccountIsApplyForKISResult accountIsApplyForFSResult, Integer ei) {
        // 1.查询是否具有相同手机号(若当前使用纷享身份跳过手机号查询)
        RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea, employeeDto.getEmployeeId(), ei, employeeDto.getMobile());
        String applyInfoKey = MD5Util.md5String(employeeDto.getEmployeeId()+ea+employeeDto.getMobile());
        redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
        String qywxUserId = null;
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(ea, employeeDto.getEmployeeId());
        if (qywxVirtualFsUserEntity == null) {
            StaffInfo staffInfo = qywxUserManager.getQywxUserInfoByPhone(ea, employeeDto.getMobile());
            if (staffInfo == null) {
                accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
                accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NOT_HAS_SAME_PHONE_WITH_QYWX.getType());
                return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
            }
            qywxUserId = staffInfo.getUserId();
        } else {
            qywxUserId = qywxVirtualFsUserEntity.getQyUserId();
        }
        // 2.查询是否具有小程序账号
        qywxVirtualFsUserEntity = qywxUserManager.getVirtualUserByQywxInfo(corpId, qywxUserId, ea);
        if (qywxVirtualFsUserEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NOAPPPY_NOBIND.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        // 校验是否具有账号名片等信息
        UserEntity userEntity = userManager.queryByCorpIdAndQYUserIdAndAppid(corpId, qywxUserId, appId);
        if (userEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NOAPPPY_NOBIND.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        AccountEntity accountEntity = accountDAO.queryAccountByUid(userEntity.getUid());
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(userEntity.getUid());
        if (accountEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NOAPPPY_NOBIND.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        if (fsBindEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPPYED_NOBIND.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        // 纷享企业信息接口数据
        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(fsBindEntity.getFsEa());
        enterpriseDataArg.setEnterpriseId(fsBindEntity.getFsCorpId());
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        if (null == enterpriseDataResult) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND);
        }
        EnterpriseData enterpriseData = enterpriseDataResult.getEnterpriseData();
        if (null == enterpriseData) {
            return new Result<>(SHErrorCode.ENTERPRISE_NOT_FOUND);
        }
        if (!fsBindEntity.getFsEa().equals(ea)) {
            accountIsApplyForFSResult.setBindCompanyName(enterpriseData.getEnterpriseName());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPPYED_BINDED_OTHER_EA.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        if (QywxUserConstants.isVirtualUserId(qywxVirtualFsUserEntity.getUserId())) {
            // 当前还是使用虚拟身份需要刷库
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_RESET_USER_DATA.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        accountIsApplyForFSResult.setBindCompanyName(enterpriseData.getEnterpriseName());
        accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
        return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
    }


    public Result<AccountIsApplyForKISResult> applyForKisQywxSpecialEnterprise(String ea, String corpId, EmployeeDto employeeDto, AccountIsApplyForKISResult accountIsApplyForFSResult, Integer ei) {
        QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(ea, employeeDto.getEmployeeId());
        String qywxUserId = null;
        if (qywxVirtualFsUserEntity == null) {
            // 查询企业微信是否有相同手机号账号
            StaffInfo staffInfo = qywxUserManager.getQywxUserInfoByPhone(ea, employeeDto.getMobile());
            if(staffInfo == null) {
                String applyInfoKey = setApplyRedisData(ea, employeeDto.getEmployeeId(), ei, employeeDto.getMobile(), null, null);
                return applyForKisNotBindQywx(ea, employeeDto, accountIsApplyForFSResult, applyInfoKey);
            }
            qywxUserId = staffInfo.getUserId();
        } else {
            qywxUserId = qywxVirtualFsUserEntity.getQyUserId();
        }
        // 在企业微信绑定表是否有数据
        qywxVirtualFsUserEntity = qywxUserManager.getVirtualUserByQywxInfo(corpId, qywxUserId, ea);
        if (qywxVirtualFsUserEntity == null) {
            String finalQywxUserId = qywxUserId;
            ThreadPoolUtils.execute(() -> {
                // 插入数据绑定表数据
                QywxVirtualFsUserEntity saveData =
                        QywxVirtualFsUserEntity.builder()
                                .id(UUIDUtil.getUUID())
                                .ea(ea)
                                .userId(employeeDto.getEmployeeId())
                                .corpId(corpId)
                                .qyUserId(finalQywxUserId)
                                .build();
                qywxVirtualFsUserManager.insert(saveData, UserTypeEnum.QYWX);
            }, ThreadPoolTypeEnums.LIGHT_BUSINESS);
            String applyInfoKey = setApplyRedisData(ea, employeeDto.getEmployeeId(), ei, employeeDto.getMobile(), corpId, qywxUserId);
            return applyForKisNotBindQywx(ea, employeeDto, accountIsApplyForFSResult, applyInfoKey);
        }
        String applyInfoKey = setApplyRedisData(ea, employeeDto.getEmployeeId(), ei, employeeDto.getMobile(), qywxVirtualFsUserEntity.getCorpId(), qywxVirtualFsUserEntity.getQyUserId());
        AccountEntity accountEntity = qywxUserManager.getAccountByPhone(ea, employeeDto.getMobile());
        if (accountEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(accountEntity.getUid());
        if (fsBindEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        if (!ea.equals(fsBindEntity.getFsEa())) {
            // 绑定企业不一样跳转重新绑定
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
        accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
        return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
    }

    public String setApplyRedisData(String ea, Integer fsUserId, Integer ei, String phone, String qywxCorpId, String qywxUserId) {
        RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea, fsUserId, ei, phone, qywxCorpId, qywxUserId);
        String applyInfoKey = MD5Util.md5String(fsUserId+ea+phone);
        redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
        return applyInfoKey;
    }

    public Result<AccountIsApplyForKISResult> applyForKisNotBindQywx(String ea, EmployeeDto employeeDto, AccountIsApplyForKISResult accountIsApplyForFSResult, String applyInfoKey) {
        if (StringUtils.isBlank(employeeDto.getMobile())) {
            return applyForKisNotBindQywxWithoutPhone(ea, employeeDto, accountIsApplyForFSResult, applyInfoKey);
        }
        AccountEntity accountEntity = qywxUserManager.getAccountByPhone(ea, employeeDto.getMobile());
        if (accountEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(accountEntity.getUid());
        if (fsBindEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }

        // 判断绑定表的userId账号状态是否正常，如果停用了，删除掉对应的数据
        if (QywxUserConstants.isFsUserId(fsBindEntity.getFsUserId())) {
            EmployeeDto dto = fsAddressBookManager.getFxEmployeeByUserId(ea, fsBindEntity.getFsUserId());
            if (dto != null && dto.getStatus() == EmployeeEntityStatus.STOP) {
                // 账号停用，删除历史名片信息
                String uid = fsBindEntity.getUid();
                wxMiniappLoginManager.deleteBindFsByUid(uid);
                /*accountEntity.setPhone(employeeDto.getMobile() + "_stop");
                accountDAO.update(accountEntity);*/
                accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
                accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
                accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
                return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
            }
        }

        // 如果绑定的是虚拟userid,很可能是解绑企微导致的，此时需要判断当前纷享userid是否已经绑定user，如果没有，把绑定表的虚拟userid更新为真实的纷享userid
        if (QywxUserConstants.isVirtualUserId(fsBindEntity.getFsUserId())) {
            FSBindEntity realFsUserBindEntity = fsBindManager.queryByUserAndAppId(ea, employeeDto.getEmployeeId(), wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
            if (realFsUserBindEntity == null) {
                fsBindManager.updateFsBindFsUserId(employeeDto.getEmployeeId(), fsBindEntity.getUid());
            }
        }

        if (!ea.equals(fsBindEntity.getFsEa())) {
            // 绑定企业不一样跳转重新绑定
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setPhone(employeeDto.getMobile());
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
        accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
        return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
    }

    private Result<AccountIsApplyForKISResult> applyForKisNotBindQywxWithoutPhone(String ea, EmployeeDto employeeDto, AccountIsApplyForKISResult accountIsApplyForFSResult, String applyInfoKey) {
        log.info("AccountServiceImpl.applyForKisNotBindQywxWithoutPhone ea:{}, employeeDto:{}", ea, employeeDto);
        int employeeId = employeeDto.getEmployeeId();
        String appId = wechatAccountManager.getNotEmptyWxAppIdByEa(ea);
        FSBindEntity fsBindEntity = fsBindManager.queryByUserAndAppId(ea, employeeId, appId);
        if (fsBindEntity == null) {
            accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
            accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.NEED_JUMP_WX_LOGIN.getType());
            return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
        }
        accountIsApplyForFSResult.setApplyInfoKey(applyInfoKey);
        accountIsApplyForFSResult.setStatus(ApplyBindStatusForKISEnum.APPLYED_BINDED_SAME_EA.getType());
        return new Result<>(SHErrorCode.SUCCESS, accountIsApplyForFSResult);
    }

    @Override
    public Result<QywxBaseInfoResult> qywxBaseInfo(String ea, String appId) {
        // 查询企业当前绑定小程序
        QywxBaseInfoResult qywxBaseInfoResult = new QywxBaseInfoResult();
        QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(ea, wechatAccountManager.getNotEmptyWxAppIdByEa(ea));
        if (qywxMiniappConfigEntity == null) {
            return Result.newError(SHErrorCode.EA_NOT_BIND_TO_MINIAPP);
        }
        qywxBaseInfoResult.setBindSameAppId(false);
        qywxBaseInfoResult.setAppId(qywxMiniappConfigEntity.getAppid());
        if (qywxMiniappConfigEntity.getAppid().equals(appId)) {
            qywxBaseInfoResult.setBindSameAppId(true);
        }
        if (!qywxBaseInfoResult.isBindSameAppId()) {
            // 小程序名
            WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(qywxMiniappConfigEntity.getAppid());
            if (wxAppInfoEnum != null) {
                qywxBaseInfoResult.setMiniAppName(wxAppInfoEnum.getAppName());
            } else {
                WechatAccountConfigEntity wechatAccountConfigEntity = wechatAccountConfigDao.getByWxAppId(qywxMiniappConfigEntity.getAppid());
                qywxBaseInfoResult.setMiniAppName(wechatAccountConfigEntity != null ? wechatAccountConfigEntity.getNickName() : null);
            }
        }

        // 查询企业当前版本
        qywxBaseInfoResult.setCurrentAppVersion(appVersionManager.getCurrentAppVersion(ea));
        return Result.newSuccess(qywxBaseInfoResult);
    }

    @Override
    public Result<Integer> checkEnterpriseRelationHasCrmAccount(String ea, String outerUid, String outerTenantId) {
        ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.ENTERPRISE_RELATION_OBJ.getName(), outerTenantId);
        if (objectData == null) {
            return Result.newSuccess(2);
        }
        Boolean hasFsAccount = objectData.getBoolean("has_fs_account");
        if (hasFsAccount == null) {
            return Result.newSuccess(2);
        }
        return Result.newSuccess(hasFsAccount ? 1 : 2);
    }
}
