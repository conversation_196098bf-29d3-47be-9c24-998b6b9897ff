package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.provider.dto.OperatorDistributorEntityCount;
import com.facishare.marketing.provider.dto.distribution.QueryOperatorByDistributorIdDTO;
import com.facishare.marketing.provider.entity.distribution.OperatorDistributorEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Created  By zhoux 2018/11/27
 **/
public interface OperatorDistributorDAO {

    @Select("<script>"
        + "SELECT * FROM\n"
        + "      operator_distributor\n"
        + "      WHERE 1 = 1\n"
        + "      <if test=\"operatorId != null\">\n"
        + "        AND operator_id = #{operatorId}\n"
        + "      </if>\n"
        + "      <if test=\"distributorId != null\">\n"
        + "        AND distributor_id = #{distributorId}\n"
        + "      </if>"
        + "</script>")
    List<OperatorDistributorEntity> queryByOperatorIdOrDistributorId(@Param("operatorId") String operatorId, @Param("distributorId") String distributorId);

    @Select("<script>"
            +" SELECT operator_id AS operatorId, COUNT(*) AS distributorCount FROM operator_distributor\n"
            + "      <where>\n"
            + "        <foreach collection=\"operatorIds\" open=\" and operator_id in(\" close=\")\"\n"
            + "          item=\"id\" separator=\",\">\n"
            + "          #{id}\n"
            + "        </foreach>\n"
            + "      </where>\n"
            + "GROUP BY operator_id\n"
            + "</script>")
    List<OperatorDistributorEntityCount> queryDistributorEntityCountByOperator(@Param("operatorIds")List<String> operatorIds);
    
    
    @Select("<script>"
        + "SELECT * FROM operator_distributor WHERE distributor_id = #{distributorId} and operator_id = #{operatorId}"
        + "</script>")
    OperatorDistributorEntity getByDistributorId(@Param("distributorId") String distributorId, @Param("operatorId") String operatorId);
    
    @Update("UPDATE operator_distributor SET operator_id = #{operatorId} WHERE id = #{id} and operator_id = #{preOperatorId} ")
    int updateOperator(@Param("operatorId") String operatorId, @Param("id") String id, @Param("preOperatorId") String preOperatorId);

    @Update("UPDATE distributor_application \n" + "SET operator_id = #{operatorId},\n" + "refuse_desc = #{refuseDesc},\n" + "status = #{status},\n" + "update_time = now()\n"
        + "WHERE id = (SELECT id FROM distributor_application WHERE distributor_id = #{distributorId} AND operator_id = #{preOperatorId} \n" + "ORDER BY update_time DESC LIMIT 1)")
    int updateApply(@Param("operatorId") String operatorId, @Param("distributorId") String distributorId, @Param("preOperatorId") String preOperatorId, @Param("refuseDesc") String refuseDesc, @Param("status") Integer status);
    
    @Insert("INSERT INTO operator_distributor(\n" +
        "        \"id\",\n" +
        "        \"operator_id\",\n" +
        "        \"distributor_id\",\n" +
        "        \"create_time\",\n" +
        "        \"update_time\"\n" +
        "        )\n" +
        "        VALUES (\n" +
        "        #{obj.id},\n" +
        "        #{obj.operatorId},\n" +
        "        #{obj.distributorId},\n" +
        "        now(),\n" +
        "        now()\n" +
        "        )")
    int addOperator(@Param("obj") OperatorDistributorEntity operatorDistributorEntity);

   @Select("SELECT * FROM operator_distributor WHERE operator_id = #{operatorId}")
    List<OperatorDistributorEntity> getDistributorIdsByOperatorId(@Param("operatorId") String operatorId);


    @Select(" SELECT * FROM operator_distributor WHERE distributor_id = #{distributorId}\n"
        + "      AND status = 0")
    OperatorDistributorEntity getOperatorIdByDistributorId(@Param("distributorId") String distributorId);

    @Update("UPDATE operator_distributor SET status = #{status}, update_time = now() WHERE id = #{id}")
    boolean updateDistributorStatusById(@Param("id") String id, @Param("status") Integer status);

    @Select("<script>"
        + " SELECT od.distributor_id as distributorId, op.id as operatorId, op.fs_user_id as fsUserId, op.phone, op.name, op.qr_url as qrUrl, od.status, od.create_time\n"
        + " FROM operator_distributor od\n"
        + " JOIN operator op on op.id = od.operator_id \n"
        + " where od.distributor_id in \n"
        +  "<foreach collection=\"distributorIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n"
        +  "     #{item}\n"
        +  "</foreach>"
        + "      <if test=\"operatorId != null\">\n"
        + "        AND op.id = #{operatorId}\n"
        + "      </if>\n"
        +"</script>")
    List<QueryOperatorByDistributorIdDTO> queryOperatorByDistributorIds(@Param("distributorIds") List<String> distributorIds, @Param("operatorId") String operatorId);
}
