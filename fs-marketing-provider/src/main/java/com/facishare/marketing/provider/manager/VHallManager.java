/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.contstant.CustomizeFormDataConstants;
import com.facishare.marketing.common.contstant.live.VHallApiConstant;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.MarketingUserActionChannelType;
import com.facishare.marketing.common.enums.MarketingUserActionType;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.crm.CrmV2FieldTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmV2LeadFieldEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.live.LiveUserStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.PhoneNumberCheck;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.live.LiveUserStatusDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO;
import com.facishare.marketing.provider.dao.live.ThirdLiveAccountDAO;
import com.facishare.marketing.provider.dao.marketingplugin.MarketingPluginConfigDAO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics;
import com.facishare.marketing.provider.entity.live.ThirdLiveAccountEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MarketingPluginConfigEntity;
import com.facishare.marketing.provider.innerArg.CreateOrUpdateMarketingLeadSyncRecordObjArg;
import com.facishare.marketing.provider.innerData.live.vhall.*;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.BrowserUserRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.remote.restapi.MetadataActionServiceManager;
import com.facishare.marketing.provider.util.live.VhallSignUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.exception.CrmBusinessException;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/5/6
 **/
@Component
@Slf4j
public class VHallManager {

    public static final String SYNC_DATA_KEY = "marketing_vhall_liveId_";

    @Autowired
    private ThirdLiveAccountDAO thirdLiveAccountDAO;

    @Autowired
    private HttpManager httpManager;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private BrowserUserRelationManager browserUserRelationManager;

    @Autowired
    private ActionManager actionManager;

    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;

    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private LiveManager liveManager;

    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;

    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    @Autowired
    private MarketingStatLogPersistorManger marketingStatLogPersistorManger;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private MarketingPluginConfigDAO marketingPluginConfigDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private MetadataActionServiceManager metadataActionServiceManager;

    /**
     * 执行post请求
     *
     * @param paramStr
     * @param url
     * @param typeToken
     * @param <T>
     * @return
     */
    public <T> T executePost(String paramStr, String url, TypeToken typeToken) {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody requestBody = RequestBody.create(mediaType, paramStr);
        Map<String, String> headerMap = new HashMap();
        headerMap.put("platform", "15");
        return httpManager.executePostHttpWithRequestBodyAndHeaderV3(requestBody, url, typeToken, headerMap);
    }

    /**
     * 执行get请求
     *
     * @param paramStr
     * @param url
     * @param typeToken
     * @param <T>
     * @return
     */
    public <T> T executeGet(String paramStr, String url, TypeToken typeToken) {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("platform", "15");
        String finalUrl = url + "?" + paramStr;
        return httpManager.executeGetHttpWithHeaderV3(finalUrl, typeToken, headerMap);
    }

    /**
     * 获取配置的第三方账号
     *
     * @param ea
     * @return
     */
    public ThirdLiveAccountEntity getThirdAccount(String ea) {
        return thirdLiveAccountDAO.getByEa(ea, LivePlatformEnum.VHALL.getType());
    }

    /**
     * 构建公共参数
     *
     * @param entity
     * @return
     */
    public Map buildCommonParam(ThirdLiveAccountEntity entity) {
        // 时间戳
        long signedAt = System.currentTimeMillis() / 1000;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("app_key", entity.getAppId());
        paramMap.put("sign_type", 0); //签名方式：0 - md5（默认）、1 - RSA
        paramMap.put("signed_at", signedAt);
        return paramMap;
    }

    /**
     * 构建参数字符串
     *
     * @param paramMap
     * @param sign
     * @return
     */
    private String buildParamStr(Map<String, Object> paramMap, String sign) {
        if (MapUtils.isEmpty(paramMap)) {
            return null;
        }

        String result = paramMap.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        return result + "&sign=" + sign;
    }

    /**
     * 排除掉空参数
     *
     * @param paramMap
     */
    private void excludeNullParam(Map<String, Object> paramMap) {
        paramMap.entrySet().removeIf(entry -> ObjectUtils.isEmpty(entry.getValue()));
    }

    // 测试微吼账号是否配置正确
    public Result testApi(String ea) {
        VHallApiQueryLiveArg arg = new VHallApiQueryLiveArg();
        arg.setLimit("1");
        arg.setPos("0");
        VHallApiQueryLiveResult result = queryLive(ea, arg);
        if (result == null) {
            return Result.newError(SHErrorCode.VHALL_NOT_BIND_ACCOUNT);
        }
        return Result.newSuccess();
    }

    /**
     * 查询活动列表
     *
     * @param ea
     * @param arg
     * @return
     */
    public VHallApiQueryLiveResult queryLive(String ea, VHallApiQueryLiveArg arg) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = getThirdAccount(ea);
        if (thirdLiveAccountEntity == null) {
            log.info("VHallManager -> queryLive error,thirdLiveAccountEntity is null,ea:{}", ea);
            return null;
        }

        // 公共参数（从控制台获取）
        Map<String, Object> paramMap = buildCommonParam(thirdLiveAccountEntity);
        // 业务参数
        paramMap.put("pos", arg.getPos());
        paramMap.put("limit", arg.getLimit());
        paramMap.put("title", arg.getTitle());
        excludeNullParam(paramMap);

        String sign = VhallSignUtil.getSign(thirdLiveAccountEntity.getSecretKey(), paramMap);
        String paramStr = buildParamStr(paramMap, sign);
        VHallApiBaseResult<VHallApiQueryLiveResult> apiBaseResult = executePost(paramStr, VHallApiConstant.LIVE_LIST, new TypeToken<VHallApiBaseResult<VHallApiQueryLiveResult>>() {
        });
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("VHallManager -> queryLive failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 获取活动详情
     *
     * @param ea
     * @param webinarId
     * @return
     */
    public VHallApiGetLiveResult getLive(String ea, String webinarId) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = getThirdAccount(ea);
        if (thirdLiveAccountEntity == null) {
            log.info("VHallManager -> getLive error,thirdLiveAccountEntity is null,ea:{}", ea);
            return null;
        }

        // 公共参数（从控制台获取）
        Map<String, Object> paramMap = buildCommonParam(thirdLiveAccountEntity);
        // 业务参数
        paramMap.put("webinar_id", webinarId);
        excludeNullParam(paramMap);

        String sign = VhallSignUtil.getSign(thirdLiveAccountEntity.getSecretKey(), paramMap);
        String paramStr = buildParamStr(paramMap, sign);
        VHallApiBaseResult<VHallApiGetLiveResult> apiBaseResult = executePost(paramStr, VHallApiConstant.LIVE_GET, new TypeToken<VHallApiBaseResult<VHallApiGetLiveResult>>() {
        });
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("VHallManager -> getLive failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 获取直播统计信息
     *
     * @param ea
     * @param webinarId
     * @return
     */
    public VHallApiGetLiveStatisticsResult getLiveStatistics(String ea, String webinarId) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = getThirdAccount(ea);
        if (thirdLiveAccountEntity == null) {
            log.info("VHallManager -> getLiveStatistics error,thirdLiveAccountEntity is null,ea:{}", ea);
            return null;
        }

        VHallApiGetLiveResult apiGetLiveResult = getLive(ea, webinarId);
        if (apiGetLiveResult == null) {
            log.info("VHallManager -> getLiveStatistics error,apiGetLiveResult is null,webinarId:{}", webinarId);
            return null;
        }

        Date startTime = DateUtil.parse2(apiGetLiveResult.getStartTime());
        String startTimeFormat = DateUtil.format2(startTime);

        Date afterDate = getAfterDate(startTime, 365);
        String afterDateFormat = DateUtil.format2(afterDate);

        // 公共参数（从控制台获取）
        Map<String, Object> paramMap = buildCommonParam(thirdLiveAccountEntity);
        // 业务参数
        paramMap.put("webinar_id", webinarId);
        paramMap.put("start_time", startTimeFormat);
        paramMap.put("end_time", afterDateFormat);
        paramMap.put("switch_id", 0);
        excludeNullParam(paramMap);

        String sign = VhallSignUtil.getSign(thirdLiveAccountEntity.getSecretKey(), paramMap);
        String paramStr = buildParamStr(paramMap, sign);
        VHallApiBaseResult<VHallApiGetLiveStatisticsResult> apiBaseResult = executeGet(paramStr, VHallApiConstant.LIVE_STATISTICS, new TypeToken<VHallApiBaseResult<VHallApiGetLiveStatisticsResult>>() {
        });
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("VHallManager -> getLiveStatistics failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 根据指定时间，计算指定天数后的时间，如果计算后的时间超过了现在，则返回现在的时间
     *
     * @param date
     * @param day
     * @return
     */
    private Date getAfterDate(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        Date afterDate = calendar.getTime();
        Date now = new Date();
        if (afterDate.after(now)) {
            return now;
        }
        return afterDate;
    }


    // 取直播用户统计信息（观看直播和观看回放需要分开查询，根据type区分）
    private VHallApiGetLiveUserStatisticsResult getLiveUserStatistics(String ea, String webinarId, String startTime, String endTime, String pos, String limit, String type) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = getThirdAccount(ea);
        if (thirdLiveAccountEntity == null) {
            log.info("VHallManager -> getLiveUserStatistics error,thirdLiveAccountEntity is null,ea:{}", ea);
            return null;
        }

        // 公共参数（从控制台获取）
        Map<String, Object> paramMap = buildCommonParam(thirdLiveAccountEntity);
        // 业务参数
        paramMap.put("webinar_id", webinarId);
        paramMap.put("start_time", startTime);
        paramMap.put("end_time", endTime);
        paramMap.put("switch_id", "0");
        paramMap.put("service_names", type);
        paramMap.put("merge_type", "1");
        paramMap.put("pos", pos);
        paramMap.put("limit", limit);
        excludeNullParam(paramMap);

        String sign = VhallSignUtil.getSign(thirdLiveAccountEntity.getSecretKey(), paramMap);
        String paramStr = buildParamStr(paramMap, sign);
        VHallApiBaseResult<VHallApiGetLiveUserStatisticsResult> apiBaseResult = executeGet(paramStr, VHallApiConstant.LIVE_USER_STATISTICS, new TypeToken<VHallApiBaseResult<VHallApiGetLiveUserStatisticsResult>>() {
        });
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("VHallManager -> getLiveUserStatistics failed, result:{}", apiBaseResult);
            return null;
        }
    }

    // 递归获取用户数据
    private List<VHallLiveUserStatisticsMergeResult> getDuration(String ea, String webinarId, String type) {
        List<VHallLiveUserStatisticsMergeResult> result = Lists.newArrayList();
        VHallApiGetLiveResult apiGetLiveResult = getLive(ea, webinarId);
        if (apiGetLiveResult == null) {
            log.info("VHallManager -> getDuration error,apiGetLiveResult is null,webinarId:{}", webinarId);
            return result;
        }

        Date startTime = DateUtil.parse2(apiGetLiveResult.getStartTime());
        String startTimeFormat = DateUtil.format2(startTime);

        Date afterDate = getAfterDate(startTime, 365);
        String afterDateFormat = DateUtil.format2(afterDate);
        // 查询直播
        Integer pos = 0;
        Integer limit = 300;
        for (int i = 0; i < 50; i++) {
            VHallApiGetLiveUserStatisticsResult liveUserStatistics = getLiveUserStatistics(ea, webinarId, startTimeFormat, afterDateFormat, String.valueOf(pos), String.valueOf(limit), type);
            if (liveUserStatistics == null) {
                break;
            }

            List<VHallApiGetLiveUserStatisticsResult.Item> list = liveUserStatistics.getList();
            List<VHallLiveUserStatisticsMergeResult> collect = list.stream().map(item -> {
                VHallLiveUserStatisticsMergeResult mergeResult = BeanUtil.copy(item, VHallLiveUserStatisticsMergeResult.class);
                if (Objects.equals("1", type)) {
                    mergeResult.setLiveWatchDuration(item.getWatchDuration());
                } else {
                    mergeResult.setRecordWatchDuration(item.getWatchDuration());
                }
                return mergeResult;
            }).collect(Collectors.toList());
            result.addAll(collect);
            // 如果返回条数小于300，说明后面没有数据了
            if (list.size() < limit) {
                break;
            }
            pos += limit;
        }
        return result;
    }

    // 获取直播用户统计信息（合并直播和回放数据）
    public List<VHallLiveUserStatisticsMergeResult> getLiveUserStatistics(String ea, String webinarId) {
        // 查询直播
        List<VHallLiveUserStatisticsMergeResult> liveDuration = getDuration(ea, webinarId, "1");
        Map<String, VHallLiveUserStatisticsMergeResult> liveMap = liveDuration.stream().collect(Collectors.toMap(VHallLiveUserStatisticsMergeResult::getUid, Function.identity(), (k1, k2) -> k1));
        // 查询回放
        List<VHallLiveUserStatisticsMergeResult> recordDuration = getDuration(ea, webinarId, "2");

        List<VHallLiveUserStatisticsMergeResult> mergedList = new ArrayList<>(liveDuration);
        for (VHallLiveUserStatisticsMergeResult recordResult : recordDuration) {
            VHallLiveUserStatisticsMergeResult liveWatchDuration = liveMap.get(recordResult.getUid());
            if (liveWatchDuration != null) {
                liveWatchDuration.setRecordWatchDuration(recordResult.getRecordWatchDuration());
            } else {
                mergedList.add(recordResult);
            }
        }

        // 批量通过第三方id查询参会人员数据，并填充手机号
        if (CollectionUtils.isNotEmpty(mergedList)) {
            List<String> thirdAccountIds = mergedList.stream()
                    .map(VHallLiveUserStatisticsMergeResult::getThirdAccountId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(thirdAccountIds)) {
                List<CampaignMergeDataEntity> campaignMergeDataByIds = campaignMergeDataDAO.getCampaignMergeDataByIds(thirdAccountIds);
                Map<String, CampaignMergeDataEntity> campaignMergeDataEntityMap = campaignMergeDataByIds.stream().collect(Collectors.toMap(CampaignMergeDataEntity::getId, Function.identity(), (k1, k2) -> k1));
                mergedList.forEach(item -> {
                    String thirdAccountId = item.getThirdAccountId();
                    if (campaignMergeDataEntityMap.containsKey(thirdAccountId)) {
                        CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntityMap.get(thirdAccountId);
                        if (campaignMergeDataEntity != null) {
                            item.setPhone(campaignMergeDataEntity.getPhone());
                        }
                    }
                });
            }
        }
        log.info("VHallManager -> getLiveUserStatistics success,ea:{},webinarId:{},mergedList:{}", ea, webinarId, JsonUtil.toJson(mergedList));
        return mergedList;
    }

    // 同步第三方数据
    public void syncDataTryLock(String ea, String marketingEventId, String liveId) {
        String key = SYNC_DATA_KEY + liveId;
        try {
            boolean redisLock = redisManager.lock(key, 60);
            if (!redisLock) {
                return;
            }
            syncLiveStatus(ea, liveId);
            syncLiveStatistics(ea, liveId);
            MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(eieaConverter.enterpriseAccountToId(ea), marketingEventId);
            liveManager.syncLiveDataToCrm(marketingLiveEntity);
        } catch (Exception e) {
            log.error("VHallManager -> syncDataTryLock error,ea:{},marketingEventId:{},liveId:{}", ea, marketingEventId, liveId, e);
            return;
        } finally {
            redisManager.unLock(key);
        }
    }

    // 同步直播状态
    private void syncLiveStatus(String ea, String xiaoetongLiveId) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getMarketingLiveByXiaoetongId(xiaoetongLiveId);
        if (marketingLiveEntity == null) {
            log.info("VHallManager -> syncLiveStatus error,marketingLiveEntity is null,xiaoetongLiveId:{}", xiaoetongLiveId);
            return;
        }

        int ei = eieaConverter.enterpriseAccountToId(ea);
        // 查询直播状态
        VHallApiGetLiveResult apiGetLiveResult = getLive(ea, xiaoetongLiveId);
        if (apiGetLiveResult != null) {
            Integer webinarState = apiGetLiveResult.getWebinarState();
            Integer status = LiveStatusEnum.NOT_START.getStatus();
            if (Objects.equals(webinarState, 2)) {
                status = LiveStatusEnum.NOT_START.getStatus();
            } else if (Objects.equals(webinarState, 1)) {
                status = LiveStatusEnum.PROCESSING.getStatus();
            } else if (Objects.equals(webinarState, 3)) {
                status = LiveStatusEnum.FINISH.getStatus();
            } else if (Objects.equals(webinarState, 5)) {
                status = LiveStatusEnum.FINISH.getStatus();
            } else if (Objects.equals(webinarState, 4)) {
                status = LiveStatusEnum.FINISH.getStatus();
            }
            marketingLiveDAO.updateXiaoetongLiveStatus(ei, xiaoetongLiveId, status);
            marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(ei, xiaoetongLiveId, status);
        }
    }

    // 同步直播统计数据
    private void syncLiveStatistics(String ea, String xiaoetongLiveId) {
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getMarketingLiveByXiaoetongId(xiaoetongLiveId);
        if (marketingLiveEntity == null) {
            log.info("VHallManager -> syncLiveStatistics error,marketingLiveEntity is null,xiaoetongLiveId:{}", xiaoetongLiveId);
            return;
        }

        int ei = eieaConverter.enterpriseAccountToId(ea);
        String marketingEventId = marketingLiveEntity.getMarketingEventId();
        // 查询直播用户统计信息
        List<VHallLiveUserStatisticsMergeResult> apiGetLiveUserStatistics = getLiveUserStatistics(ea, xiaoetongLiveId);
        VHallApiGetLiveStatisticsResult apiGetLiveStatistics = getLiveStatistics(ea, xiaoetongLiveId);

        // 过滤掉没有手机号的数据
        List<VHallLiveUserStatisticsMergeResult> liveUserStatisticsIncludePhone = apiGetLiveUserStatistics.stream().filter(item -> StringUtils.isNotBlank(item.getPhone())).collect(Collectors.toList());

        // 查询已有观看数据
        List<LiveUserStatusEntity> oldLiveUserStatusList = liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveId(xiaoetongLiveId);
        Map<String, LiveUserStatusEntity> oldLiveUserStatusMap = oldLiveUserStatusList.stream().collect(Collectors.toMap(LiveUserStatusEntity::getPhone, Function.identity(), (v1, v2) -> v1));

        // 查询已有报名数据
        List<CampaignMergeDataEntity> campaignMergeDataEntities = campaignMergeDataDAO.getCampaignMergeDataByMarketingEventIdAndEa(marketingEventId, ea);
        Map<String, CampaignMergeDataEntity> campaignMergeDataEntityMap = campaignMergeDataEntities.stream().collect(Collectors.toMap(CampaignMergeDataEntity::getPhone, Function.identity(), (v1, v2) -> v1));

        List<LiveUserStatusEntity> insertViewUserList = Lists.newArrayList();
        List<LiveUserStatusEntity> updateViewUserList = Lists.newArrayList();
        for (VHallLiveUserStatisticsMergeResult statisticsMergeResult : liveUserStatisticsIncludePhone) {
            String phone = statisticsMergeResult.getPhone();
            Integer liveWatchDuration = statisticsMergeResult.getLiveWatchDuration() == null ? 0 : statisticsMergeResult.getLiveWatchDuration();
            Integer recordWatchDuration = statisticsMergeResult.getRecordWatchDuration() == null ? 0 : statisticsMergeResult.getRecordWatchDuration();

            // 判断活动成员是否存在
            if (campaignMergeDataEntityMap.containsKey(phone)) {
                // 存在，无需处理
            } else {
                // 不存在，生成线索和活动成员
                ThreadPoolUtils.execute(() -> {
                    saveLeadsToCrm(statisticsMergeResult, ea, marketingEventId);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
            }

            // 判断直播用户表是否存在
            Integer oldLiveWatchDuration = 0;
            LiveUserStatusEntity liveUserStatusEntity;
            if (oldLiveUserStatusMap.containsKey(phone)) {
                // 更新
                liveUserStatusEntity = oldLiveUserStatusMap.get(phone);
                oldLiveWatchDuration = liveUserStatusEntity.getViewTime() == null ? 0 : liveUserStatusEntity.getViewTime();
                // 直播观看时长
                liveUserStatusEntity.setViewTime(liveWatchDuration);
                if (liveWatchDuration > 0) {
                    liveUserStatusEntity.setViewStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                }
                // 点播观看时长
                liveUserStatusEntity.setReplayTime(recordWatchDuration);
                if (recordWatchDuration > 0) {
                    liveUserStatusEntity.setReplayStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                }
                updateViewUserList.add(liveUserStatusEntity);
            } else {
                // 新增
                liveUserStatusEntity = new LiveUserStatusEntity();
                liveUserStatusEntity.setId(UUIDUtil.getUUID());
                liveUserStatusEntity.initStatus(Lists.newArrayList());
                if (liveWatchDuration > 0) {
                    liveUserStatusEntity.setViewStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                }
                if (recordWatchDuration > 0) {
                    liveUserStatusEntity.setReplayStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                }
                liveUserStatusEntity.setViewTime(liveWatchDuration);
                liveUserStatusEntity.setReplayTime(recordWatchDuration);
                liveUserStatusEntity.setXiaoetongLiveId(xiaoetongLiveId);
                liveUserStatusEntity.setType(LivePlatformEnum.VHALL.getType());
                liveUserStatusEntity.setPhone(phone);
                insertViewUserList.add(liveUserStatusEntity);
            }
            // 发送直播行为数据（观看时长有变化时，才需要触发）
            if (liveWatchDuration.compareTo(oldLiveWatchDuration) > 0) {
                ThreadPoolUtils.execute(() -> {
                    Optional<String> browserUserFinger = browserUserRelationManager.getOrCreateBrowserUserIdByPhone(ea, phone);
                    RecordActionArg recordActionArg = new RecordActionArg();
                    recordActionArg.setEa(ea);
                    recordActionArg.setChannelType(MarketingUserActionChannelType.H5.getChannelType());
                    recordActionArg.setObjectType(ObjectTypeEnum.LIVE.getType());
                    recordActionArg.setObjectId(marketingLiveEntity.getId());
                    recordActionArg.setMarketingEventId(marketingLiveEntity.getMarketingEventId());
                    recordActionArg.setFingerPrint(browserUserFinger.get());
                    recordActionArg.setActionType(MarketingUserActionType.LOOK_UP_LIVE.getActionType());
                    recordActionArg.setActionTime(System.currentTimeMillis());
                    actionManager.sendMarketingActivityActionToMq(ea, recordActionArg);
                }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            }
        }

        if (CollectionUtils.isNotEmpty(insertViewUserList)) {
            Lists.partition(insertViewUserList, 200).forEach(item -> {
                liveUserStatusDAO.batchInsert(item);
            });
        }
        if (CollectionUtils.isNotEmpty(updateViewUserList)) {
            Lists.partition(updateViewUserList, 200).forEach(item -> {
                liveUserStatusDAO.batchUpdatePolyvData(item);
            });
        }

        // 统计表数据更新
        List<MarketingLiveStatistics> marketingLiveStatisticsList = marketingLiveStatisticsDAO.getByXiaoetongLiveIdV2(eieaConverter.enterpriseAccountToId(ea), xiaoetongLiveId);
        if (CollectionUtils.isEmpty(marketingLiveStatisticsList)) {
            return;
        }
        if (apiGetLiveStatistics != null) {
            MarketingLiveStatistics marketingLiveStatistics = marketingLiveStatisticsList.get(0);
            marketingLiveStatistics.setViewDuration(apiGetLiveStatistics.getWatchDuration());
            marketingLiveStatistics.setTotalAttendeeUsers(apiGetLiveStatistics.getWatchNumber());
            marketingLiveStatistics.setPerViewDuration(apiGetLiveStatistics.getWatchDurationAvg() == null ? 0 : apiGetLiveStatistics.getWatchDurationAvg().intValue());
            marketingLiveStatistics.setViewTimes(apiGetLiveStatistics.getWatchTimes());
            marketingLiveStatistics.setTotalViewUsers(apiGetLiveStatistics.getLiveWatchNumber());
            marketingLiveStatistics.setTotalRecordUsers(apiGetLiveStatistics.getVodWatchNumber());
            marketingLiveStatisticsDAO.update(marketingLiveStatistics);
        }
    }

    private void saveLeadsToCrm(VHallLiveUserStatisticsMergeResult data, String ea, String marketingEventId) {
        log.info("VHallManager -> saveLeadsToCrm original data 原始数据:{}", data);
        String phone = data.getPhone();
        String leadId = null;
        Integer createBy = null;
        try {
            data.setEa(ea);
            data.setMarketingEventId(marketingEventId);
            // 组装crm线索对象
            Map<String, Object> params = buildLeadsFieldData(data, ea);
            if (params == null) {
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_707));
                return;
            }
            //设置线索来源渠道promotion_channel
            params.put("promotion_channel", SpreadChannelManager.promotionChannelMap.get("其他"));
            params.put("from_marketing", true);
            // 设置市场活动id
            params.put(CrmV2LeadFieldEnum.MarketingEventId.getNewFieldName(), marketingEventId);

            ObjectData objectData = new ObjectData();
            objectData.putAll(params);
            objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, CrmObjectApiNameEnum.CRM_LEAD.getName());
            objectData.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, CrmObjectApiNameEnum.CRM_LEAD.getName());

            //重复线索不进入CRM
            Result<CrmV2Manager.LeadDuplicateSearchResult> leadDuplicateSearchResultResult = crmV2Manager.leadDuplicateSearchByObjectV2(ea, objectData);
            log.info("VHallManager -> saveLeadsToCrm 查重返回结果，result:{}", leadDuplicateSearchResultResult);
            if (!leadDuplicateSearchResultResult.isSuccess()) {
                // 查询失败
                log.info("VHallManager -> saveLeadsToCrm 查重校验失败，result:{}", leadDuplicateSearchResultResult);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_727));
                return;
            }
            CrmV2Manager.LeadDuplicateSearchResult duplicateSearchResult = leadDuplicateSearchResultResult.getData();
            if (!duplicateSearchResult.isDuplicate() && StringUtils.isNotBlank(duplicateSearchResult.getLeadId())) {
                log.info("VHallManager -> saveLeadsToCrm clue is exist 线索重复, data:{}", data);
                leadId = duplicateSearchResult.getLeadId();
                if (StringUtils.isBlank(phone)) {
                    return;
                }
                syncCampaignMember(ea, leadId, marketingEventId, null, true, phone);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_738));
                return;
            }

            // 获取线索创建人
            createBy = clueDefaultSettingService.getClueCreator(marketingEventId, ea, ClueDefaultSettingTypeEnum.OTHER.getType());
            // 处理开启多组织，线索归属组织字段存入
            fillOrganizationData(objectData, ea, marketingEventId, createBy);
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> result = crmV2Manager.addObjectData(ea, LeadsFieldContants.API_NAME, createBy, objectData);
            if (result != null && result.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
                leadId = (String) result.getData().getObjectData().get(CrmV2LeadFieldEnum.ID.getNewFieldName());
                log.info("VHallManager -> saveLeadsToCrm success data:{} leadId:{}", data, leadId);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, leadId, MarketingLeadSyncRecordObjManager.SUCCESS_STATUS, I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_750));
            } else {
                String message = I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR);
                if (result != null && StringUtils.isNotBlank(result.getMessage())) {
                    message = result.getMessage();
                }
                log.info("VHallManager -> saveLeadsToCrm failed data:{} message:{}", data, message);
                tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
                return;
            }

            if (org.apache.commons.lang.StringUtils.isNotEmpty(leadId)) {
                //上报神策系统
                marketingStatLogPersistorManger.sendLeadData(ea, leadId, null, MarketingStatLogPersistorManger.CHANNEL_AD_BAIDU_SYNC);
                //生成活动成员
                syncCampaignMember(ea, leadId, marketingEventId, null, false, phone);
            }
        } catch (Exception e) {
            log.warn("VHallManager -> saveLeadsToCrm error data:{}, e:{}", data, e);
            String message = StringUtils.isBlank(e.getMessage()) ? I18nUtil.getStaticByKey(CrmStatusMessageConstant.SYSTEM_ERROR) : e.getMessage();
            tryCreateOrUpdateMarketingLeadSyncRecordObj(data, null, MarketingLeadSyncRecordObjManager.FAIL_STATUS, message);
            return;
        }
    }

    // 处理开启多组织，线索归属组织字段存入
    public Map<String, Object> fillOrganizationData(Map<String, Object> data, String ea, String marketingEventId, Integer createBy) {
        boolean isOpen = dataPermissionManager.isOpenDataOwnOrganization(ea);
        if (!isOpen) {
            return data;
        }
        //获取线索创建人的归属组织
        if (Objects.equals(createBy, SuperUserConstants.USER_ID)) {
            if (StringUtils.isEmpty(marketingEventId)) {
                return data;
            }
            //获取市场活动的负责人
            ObjectData objectData = crmV2Manager.getOneByList(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
            if (objectData == null) {
                return data;
            }
            if (Objects.equals(objectData.getOwner(), SuperUserConstants.USER_ID)) {
                return data;
            }
            createBy = objectData.getOwner();
        }
        ObjectData personnelObj = crmV2Manager.getObjectData(ea, -10000, "PersonnelObj", String.valueOf(createBy));

        if (personnelObj == null) {
            return data;
        }

        Object data_own_organization = personnelObj.get(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION);
        if (data_own_organization == null) {
            return data;
        }

        List<String> dataOwnOrganization = (List) data_own_organization;
        if (CollectionUtils.isNotEmpty(dataOwnOrganization) && (!data.containsKey(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION) || data.get(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION) == null)) {
            data.put(CustomizeFormDataConstants.CRM_OWN_ORGANIZATION, dataOwnOrganization);
        }
        return data;
    }

    private Map<String, Object> buildLeadsFieldData(VHallLiveUserStatisticsMergeResult data, String ea) {
        // 查询线索映射关系
        MarketingPluginConfigEntity marketingPluginConfigEntity = marketingPluginConfigDAO.queryMarketingPluginFieldMap(ea, MarketingPluginTypeEnum.VHALL_LIVE.getType(), CrmObjectApiNameEnum.CRM_LEAD.getName());
        if (marketingPluginConfigEntity == null || marketingPluginConfigEntity.getCrmFormFieldMap() == null) {
            log.info("VHallManager -> buildLeadsFieldData error 未设置线索映射 ea:{}", ea);
            return null;
        }
        FieldMappings crmFormFieldMap = marketingPluginConfigEntity.getCrmFormFieldMap();
        Map<String, Object> param = createObjectDataToCrmLeadFieldDataMap(ea, data, crmFormFieldMap, marketingPluginConfigEntity.getCrmPoolId(), marketingPluginConfigEntity.getCrmRecordType());
        return param;
    }

    private Map<String, Object> createObjectDataToCrmLeadFieldDataMap(String ea, VHallLiveUserStatisticsMergeResult data, FieldMappings crmFormFieldMap, String crmPoolId, String crmRecordType) {
        Map<String, Object> crmData = new HashMap<>();
        // 获取线索描述
        Map<String, CrmUserDefineFieldVo> fieldTypeMap = Maps.newHashMap();
        try {
            List<CrmUserDefineFieldVo> crmUserDefineFieldVoList = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
            if (CollectionUtils.isNotEmpty(crmUserDefineFieldVoList)) {
                fieldTypeMap = crmUserDefineFieldVoList.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, Function.identity(), (v1, v2) -> v1));
            }
        } catch (Exception e) {
            log.warn("VHallManager -> createObjectDataToCrmLeadFieldDataMap error e:{}", e);
            return null;
        }

        Map<String, CrmUserDefineFieldVo> finalFieldTypeMap = fieldTypeMap;
        for (FieldMappings.FieldMapping fieldMapping : crmFormFieldMap) {
            Object fieldValue = data.getFieldValueByName(fieldMapping.getMankeepFieldName());
            if (fieldValue == null) {
                String fieldType = finalFieldTypeMap.get(fieldMapping.getCrmFieldName()) == null ? null : finalFieldTypeMap.get(fieldMapping.getCrmFieldName()).getFieldTypeName();
                if (StringUtils.isNotBlank(fieldType) && fieldType.equals(CrmV2FieldTypeEnum.SelectMany.getName())) {
                    fieldValue = Lists.newArrayList(fieldMapping.getDefaultValue());
                } else {
                    fieldValue = fieldMapping.getDefaultValue();
                }
            }

            log.info("this fieldMapping:{},  fieldValue:{}", fieldMapping, fieldValue);
            if (!Strings.isNullOrEmpty(fieldMapping.getCrmFieldName())) {
                crmData.put(fieldMapping.getCrmFieldName(), fieldValue);
            }
        }
        if (StringUtils.isNotBlank(crmPoolId)) {
            crmData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), crmPoolId);
        }
        crmData.put(CrmV2LeadFieldEnum.SalesCluePoolID.getNewFieldName(), crmPoolId);
        crmData.put(CrmV2LeadFieldEnum.RecordType.getNewFieldName(), crmRecordType);
        log.info("VHallManager -> createObjectDataToCrmLeadFieldDataMap crmData:{}", crmData);
        return crmData;
    }


    private void tryCreateOrUpdateMarketingLeadSyncRecordObj(VHallLiveUserStatisticsMergeResult arg, String leadId, String syncStatus, String remark) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg leadSyncRecordObjArg = buildCreateMarketingLeadSyncRecordObjArg(arg, leadId);
        leadSyncRecordObjArg.setSyncStatus(syncStatus);
        leadSyncRecordObjArg.setRemark(remark);
        leadSyncRecordObjArg.setLeadId(leadId);
        leadSyncRecordObjArg.setMarketingLeadSyncRecordObjId(arg.getMarketingLeadSyncRecordObjId());
        marketingLeadSyncRecordObjManager.tryCreateOrUpdateMarketingLeadSyncRecordObj(leadSyncRecordObjArg);
    }

    private CreateOrUpdateMarketingLeadSyncRecordObjArg buildCreateMarketingLeadSyncRecordObjArg(VHallLiveUserStatisticsMergeResult arg, String leadId) {
        CreateOrUpdateMarketingLeadSyncRecordObjArg marketingLeadSyncRecordObjArg = new CreateOrUpdateMarketingLeadSyncRecordObjArg();
        marketingLeadSyncRecordObjArg.setEa(arg.getEa());
        marketingLeadSyncRecordObjArg.setSyncData(JsonUtil.toJson(arg));
        // 不知道咋配置的 还有配置成other的
        marketingLeadSyncRecordObjArg.setOutPlatformName(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_VHALLMANAGER_888));
        marketingLeadSyncRecordObjArg.setOutPlatformType(MarketingLeadSyncRecordObjManager.VHALL_PLATFORM);
        // 这里有的没有配置clueId 反而在data里配置了id
        if (StringUtils.isNotBlank(arg.getUid())) {
            marketingLeadSyncRecordObjArg.setOutPlatformDataId(arg.getUid());
        }
        String mobile = arg.getPhone();
        String name = arg.getNickName();
        if (StringUtils.isNotBlank(mobile) && !PhoneNumberCheck.isPhoneLegal(mobile)) {
            mobile = null;
        }
        marketingLeadSyncRecordObjArg.setMobile(mobile);
        marketingLeadSyncRecordObjArg.setLeadName(name);
        marketingLeadSyncRecordObjArg.setLeadId(leadId);
        return marketingLeadSyncRecordObjArg;
    }

    private String syncCampaignMember(String ea, String leadId, String marketingEventId, String outerUserId, Boolean addCampaignMember, String phone) {
        // 判断当前手机号有无报名数据
        List<CampaignMergeDataEntity> campaignMergeDataEntites = campaignMergeDataDAO.getCampaignMergeDataByPhoneOrderCreateTime(ea, marketingEventId, phone);
        if (CollectionUtils.isNotEmpty(campaignMergeDataEntites)) {
            // 已经报名
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataEntites.get(0);
            return campaignMergeDataEntity.getId();
        } else {
            // 未报名，生成活动成员
            CampaignMergeDataEntity campaignMergeDataEntity = buildCampaignMemberObjData(ea, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId, outerUserId, addCampaignMember);
            if (campaignMergeDataEntity == null) {
                return null;
            }

            Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, campaignMergeDataEntity);
            String campaignMemberObjId = crmV2Manager.addCampaignMembersObjByLock(ea, dataMap, CampaignMergeDataObjectTypeEnum.LEADS_OBJ.getType(), leadId, marketingEventId);
            if (campaignMemberObjId != null) {
                campaignMergeDataEntity.setCampaignMembersObjId(campaignMemberObjId);
                campaignMergeDataManager.addCampaignDataOnlyUnLock(campaignMergeDataEntity);
                log.info("VHallManager -> syncCampaignMember success, data:{}", campaignMergeDataEntity);
            }

            String campaignMemberId = campaignMergeDataEntity.getId();
            if (StringUtils.isNotBlank(campaignMemberId)) {
                campaignMergeDataDAO.updateCampaignMergeDataOuterUserId(outerUserId, campaignMemberId);
            }
            return campaignMemberId;
        }
    }

    private CampaignMergeDataEntity buildCampaignMemberObjData(String ea, Integer bindCrmObjectType, String bindCrmObjectId, String marketingEventId, String outerUserId, Boolean addCampaignMember) {
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(bindCrmObjectType);
        if (campaignMergeDataObjectTypeEnum == null) {
            return null;
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), bindCrmObjectId);
        } catch (Exception e) {
            log.warn("BaiduCampaignService.buildCampaignMemberObjData error e:{}", e);
        }
        if (objectData == null) {
            return null;
        }

        CampaignMergeDataEntity campaignMergeDataEntity = new CampaignMergeDataEntity();
        String phone = campaignMergeDataManager.getPhoneByObject(objectData);
        String name = objectData.getName();
        String campaignId = UUIDUtil.getUUID();
        campaignMergeDataEntity.setId(campaignId);
        campaignMergeDataEntity.setEa(ea);
        campaignMergeDataEntity.setMarketingEventId(marketingEventId);
        campaignMergeDataEntity.setBindCrmObjectId(bindCrmObjectId);
        campaignMergeDataEntity.setBindCrmObjectType(bindCrmObjectType);
        campaignMergeDataEntity.setName(name);
        campaignMergeDataEntity.setPhone(phone);
        campaignMergeDataEntity.setCreateTime(new Date());
        campaignMergeDataEntity.setUpdateTime(new Date());
        campaignMergeDataEntity.setSourceType(CampaignMergeDataSourceTypeEnum.FORM_OR_CAMPAIGN_MEMBERS_OBJ.getType());
        campaignMergeDataEntity.setOuterUserId(outerUserId);
        campaignMergeDataEntity.setAddCampaignMember(addCampaignMember);

        return campaignMergeDataEntity;
    }

    public Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("CampaignMergeDataManager.campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        String name = objectData.getName();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }

    protected HeaderObj createHeaderObj(String ea, Integer userId) {
        Integer tenantId = eieaConverter.enterpriseAccountToId(ea);
        if (null == tenantId) {
            throw new CrmBusinessException(-1000, "enterpriseAccountToId failed, ea=" + ea);
        }

        if (null == userId) {
            userId = -10000;
        }

        return new HeaderObj(tenantId, userId);
    }

    // 获取讲师链接
    public VHallApiGetRoleUrlResult getRoleUrl(String ea, String webinarId) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = getThirdAccount(ea);
        if (thirdLiveAccountEntity == null) {
            log.info("VHallManager -> getRoleUrl error,thirdLiveAccountEntity is null,ea:{}", ea);
            return null;
        }

        // 公共参数（从控制台获取）
        Map<String, Object> paramMap = buildCommonParam(thirdLiveAccountEntity);
        // 业务参数
        paramMap.put("webinar_id", webinarId);
        paramMap.put("type", "1");
        excludeNullParam(paramMap);

        String sign = VhallSignUtil.getSign(thirdLiveAccountEntity.getSecretKey(), paramMap);
        String paramStr = buildParamStr(paramMap, sign);
        VHallApiBaseResult<VHallApiGetRoleUrlResult> apiBaseResult = executeGet(paramStr, VHallApiConstant.LIVE_GET_ROLE_URL, new TypeToken<VHallApiBaseResult<VHallApiGetRoleUrlResult>>() {
        });
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("VHallManager -> getRoleUrl failed, result:{}", apiBaseResult);
            return null;
        }
    }

    // 创建第三方平台用户
    public VHallApiCreateUserResult createUser(String ea, String userName, String userId) {
        ThirdLiveAccountEntity thirdLiveAccountEntity = getThirdAccount(ea);
        if (thirdLiveAccountEntity == null) {
            log.info("VHallManager -> createUser error,thirdLiveAccountEntity is null,ea:{}", ea);
            return null;
        }

        // 公共参数（从控制台获取）
        Map<String, Object> paramMap = buildCommonParam(thirdLiveAccountEntity);
        // 业务参数
        paramMap.put("third_user_id", userId);
        paramMap.put("pass", "111111");
        paramMap.put("nick_name", userName);
        excludeNullParam(paramMap);

        String sign = VhallSignUtil.getSign(thirdLiveAccountEntity.getSecretKey(), paramMap);
        String paramStr = buildParamStr(paramMap, sign);
        VHallApiBaseResult<VHallApiCreateUserResult> apiBaseResult = executePost(paramStr, VHallApiConstant.CREATE_USER_URL, new TypeToken<VHallApiBaseResult<VHallApiCreateUserResult>>() {
        });
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("VHallManager -> createUser failed, result:{}", apiBaseResult);
            return null;
        }
    }
}