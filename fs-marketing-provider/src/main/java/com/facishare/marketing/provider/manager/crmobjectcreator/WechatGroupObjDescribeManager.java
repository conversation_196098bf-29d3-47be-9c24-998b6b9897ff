/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.manager.crmobjectcreator;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.result.qywx.QywxEmployeeResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryCustomerGroupListResult;
import com.facishare.marketing.api.result.qywx.customerGroup.QueryGroupOwnerListResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.DelayQueueTagConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.contstant.RocketMqDelayLevelConstants;
import com.facishare.marketing.common.enums.PaasAndCrmOperatorEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.qywx.AppScopeEnum;
import com.facishare.marketing.common.enums.qywx.GroupMemberTypeEnum;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity;
import com.facishare.marketing.provider.entity.qywx.QywxCorpAgentConfigEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.innerArg.qywx.ExternalChatEvent;
import com.facishare.marketing.provider.innerData.qywx.ChangeExternalChatEventMsg;
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupDetailResult;
import com.facishare.marketing.provider.innerResult.qywx.CustomerGroupListResult;
import com.facishare.marketing.provider.innerResult.qywx.DepartmentStaffResult;
import com.facishare.marketing.provider.innerResult.qywx.QywxEventDelayMqArg;
import com.facishare.marketing.provider.manager.ObjectServiceManager;
import com.facishare.marketing.provider.manager.QywxVirtualFsUserManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.*;
import com.facishare.marketing.provider.manager.qywx.CustomerGroupManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.mq.sender.DelayQueueSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.WechatGroupObjDescribeUtil;
import com.facishare.open.common.result.exception.BizException;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.*;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.InnerResult;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.result.CreateObjectResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.marketing.provider.manager.AuthManager.defaultAllDepartment;
import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/13 16:06
 */
@Component
@Slf4j
public class WechatGroupObjDescribeManager {

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDescribeService objectDescribeService;
    @Autowired
    private ObjectServiceManager objectServiceManager;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private CustomerGroupManager customerGroupManager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private WechatGroupUserObjDescribeManager wechatGroupUserObjDescribeManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Autowired
    private QyWxAddressBookDAO qyWxAddressBookDAO;
    @Autowired
    private QywxAddressBookManager qywxAddressBookManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;

    @Autowired
    private DelayQueueSender delayQueueSender;

    @Autowired
    private QywxEmployeeManager qywxEmployeeManager;

    @ReloadableProperty("qywx.crm.appid")
    private String qywxCrmAppId;

    @Autowired
    private AppVersionManager appVersionManager;

    private static final  String HANDLE_CHAT_GROUP_LOCK_KEY = "create_group_%s_%s";

    /**
     * 创建or获取企微客户群对象数据ID
     */
    public ObjectDescribe getOrCreateWechatGroupObjDescribe(String fsEa) {
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), "WechatGroupObj");
        if (getDescribeResultResult.getCode() != CrmErrorCode.OBJECT_NOT_EXIST && !getDescribeResultResult.isSuccess()) {
            throw new BizException(getDescribeResultResult.getCode(), getDescribeResultResult.getMessage());
        } else if (getDescribeResultResult.isSuccess()) {
            return getDescribeResultResult.getData().getDescribe();
        }
        CreateObjectArg crmCreateObjectVO = new CreateObjectArg();
        crmCreateObjectVO.setActive(true);
        crmCreateObjectVO.setIncludeLayout(true);
        crmCreateObjectVO.setLayoutType("detail");
        crmCreateObjectVO.setJsonData(WechatGroupObjDescribeUtil.getWechatGroupObjJsonData());
        crmCreateObjectVO.setJsonLayout(WechatGroupObjDescribeUtil.getWechatGroupObjJsonLayout());
        crmCreateObjectVO.setJsonListLayout(WechatGroupObjDescribeUtil.getWechatGroupObjJsonListLayout());
        InnerResult<CreateObjectResult> result = objectServiceManager.createObject(ei, SuperUserConstants.USER_ID, crmCreateObjectVO);
        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getErrCode(), result.getErrMessage());
        }
        return result.getResult().getObjectDescribe();
    }

    public int initData(String ea) {
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return -1;
        }
        String key = "MARKETING_SYNC_QYWX_GROUP_" + ea;
        if (redisManager.get(key) != null){
            log.info("initData task is running ea:{}", ea);
            return 0;
        }

        int addCount = 0;
        try {
            redisManager.set(key,  60 * 10, key);
            int offset = 0;
            int size = 100;
            String accessToken = qywxManager.getAccessToken(ea);
            List<DepartmentStaffResult.StaffInfo> staffInfos = qywxManager.queryAllStaff(ea, accessToken, true, true);
            List<String> userIds = null;
            if (CollectionUtils.isNotEmpty(staffInfos)){
                userIds = staffInfos.stream().map(DepartmentStaffResult.StaffInfo::getUserId).collect(Collectors.toList());
                log.warn("queryCustomerListNew all userIds:{}", userIds);
            }
            if (userIds != null) {
                PageUtil pageUtil = new PageUtil(userIds, 100);
                for (int i = 1; i <= pageUtil.getPageCount(); i++) {
                    List<String> tmp = pageUtil.getPagedList(i);
                    log.warn("queryCustomerListNew page:{} userIds:{}", i, tmp);
                    CustomerGroupListResult result = customerGroupManager.queryCustomerListNew(ea, tmp, size);
                    if (result == null || !result.isSuccess()) {
                        log.warn(" wechat group faild call queryCustomerList api return error ea:{}", ea);
                        continue;
                    }
                    if (CollectionUtils.isEmpty(result.getGroupList())) {
                        continue;
                    }
                    List<String> syncGroupIds = Lists.newArrayList();
                    Map<String, CustomerGroupListResult.GroupItem> groupResultMap ;
                    groupResultMap = result.getGroupList().stream().collect(Collectors.toMap(CustomerGroupListResult.GroupItem::getGroupId, Function.identity()));
                    for (CustomerGroupListResult.GroupItem groupItem : result.getGroupList()) {
                        syncGroupIds.add(groupItem.getGroupId());
                    }
                    if (CollectionUtils.isNotEmpty(syncGroupIds)){
//                        CountDownLatch countDownLatch = new CountDownLatch(syncGroupIds.size());
                        for (String groupId : syncGroupIds) {
//                            ThreadPoolUtils.execute(() -> {
                                try{
                                    mergeWeChatGroupListToCrmLimited(ea,ImmutableList.of(groupId),groupResultMap.get(groupId).getStatus(),true);
                                }catch (Exception e){
                                    log.warn("init wechat group error e:{}", e);
                                }finally {
//                                    countDownLatch.countDown();
                                }
//                            }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
                        }
//                        try {
//                            countDownLatch.await(30L, TimeUnit.SECONDS);
//                        } catch (Exception e) {
//                            log.warn("QYWXCustomerGroupServiceImpl.queryCustomerGroupList await error e:{}", e);
//                        }
                    }
                }
            } else {
                CustomerGroupListResult result = customerGroupManager.queryCustomerListNew(ea, null, size);
                if (result == null || !result.isSuccess()) {
                    log.warn(" wechat group faild call queryCustomerList api return error ea:{}", ea);
                    return -1;
                }
                if (CollectionUtils.isEmpty(result.getGroupList())) {
                    return 0;
                }
                List<String> syncGroupIds = Lists.newArrayList();
                Map<String, CustomerGroupListResult.GroupItem> groupResultMap ;
                groupResultMap = result.getGroupList().stream().collect(Collectors.toMap(CustomerGroupListResult.GroupItem::getGroupId, Function.identity()));
                for (CustomerGroupListResult.GroupItem groupItem : result.getGroupList()) {
                    syncGroupIds.add(groupItem.getGroupId());
                }
                if (CollectionUtils.isNotEmpty(syncGroupIds)){
//                    CountDownLatch countDownLatch = new CountDownLatch(syncGroupIds.size());
                    for (String groupId : syncGroupIds) {
//                        ThreadPoolUtils.execute(() -> {
                        try{
                            mergeWeChatGroupListToCrmLimited(ea,ImmutableList.of(groupId),groupResultMap.get(groupId).getStatus(),true);
                        }catch (Exception e){
                            log.warn("init wechat group error e:{}", e);
                        }finally {
//                            countDownLatch.countDown();
                        }
//                        }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
                    }
//                    try {
//                        countDownLatch.await(30L, TimeUnit.SECONDS);
//                    } catch (Exception e) {
//                        log.warn("QYWXCustomerGroupServiceImpl.queryCustomerGroupList await error e:{}", e);
//                    }
                }
            }
        }finally {
            redisManager.delete(key);
        }
        return addCount;
    }


    public int mergeWeChatGroupListToCrmLimited(String ea, List<String> syncGroupIds, Integer status, boolean triggerUpdate) {
        QywxCorpAgentConfigEntity agentConfig = qywxCorpAgentConfigDAO.queryAgentByEa(ea);
        if (agentConfig == null) {
            return 0;
        }
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        Map<String, ObjectData> getObjectDataMap = getObjectDataMap(ea,syncGroupIds);
        Set<String> existedGroupIdSet = getObjectDataMap.keySet();
        int incrementCount = 0;
        boolean addGroupStatus = true;
        for (String groupId : syncGroupIds) {
            if (!existedGroupIdSet.contains(groupId)){
                try {
                    addGroupStatus = false;
                    CustomerGroupDetailResult groupDetailResult = customerGroupManager.queryQywxGroupDetail(ea, groupId);
                    incrementCount += doAddWechatGroupToCrm(ei,status, AppScopeEnum.MARKETING, groupDetailResult) ? 1 : 0;
                } catch(Exception e){
                    log.warn("Exception, groupId:{}", groupId, e);
                }
            }
        }
        if(triggerUpdate && addGroupStatus){
            for (Map.Entry<String, ObjectData> stringObjectDataEntry : getObjectDataMap.entrySet()) {
                ObjectData objectData = stringObjectDataEntry.getValue();
                try {
                    CustomerGroupDetailResult groupDetailResult = customerGroupManager.queryQywxGroupDetail(ea, stringObjectDataEntry.getKey());
                    if (groupDetailResult != null && groupDetailResult.getQywxGroupChat() != null) {
                        Integer owner = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea, groupDetailResult.getQywxGroupChat().getGroupOwner());
                        incrementCount += doUpdateWechatGroupToCrm(ei, status, objectData, owner, AppScopeEnum.MARKETING, groupDetailResult) ? 1 : 0;
                    }
                }catch (Exception e){
                    log.warn("Exception, update group objectData:{}", objectData, e);
                }
            }

        }
        return incrementCount;
    }

    public boolean doUpdateWechatGroupToCrm(Integer ei, Integer status, ObjectData objectData, Integer owner, AppScopeEnum appScopeEnum, CustomerGroupDetailResult groupDetailResult) {
        HeaderObj systemHeader = new HeaderObj(ei, -10000);
        if (groupDetailResult == null || groupDetailResult.getQywxGroupChat() == null){
            return false;
        }
        String chatId = groupDetailResult.getQywxGroupChat().getGroupId();
        String lockKey = String.format(HANDLE_CHAT_GROUP_LOCK_KEY, ei, groupDetailResult.getQywxGroupChat().getGroupId());
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        boolean dataUpdated = false;
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("doUpdateWechatGroupToCrm ei: {} chatId: {} lock: {} appScope: {}", ei, chatId, lockSuccess, appScopeEnum);
            if(!lockSuccess) {
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setChatId(chatId);
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.UPDATE_EVENT);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.GROUP_OBJECT_TYPE);
                    qywxEventDelayMqArg.setGroupDetailResult(groupDetailResult);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    qywxEventDelayMqArg.setOwnerId(owner);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return false;
            }
            ObjectData currentObjectData = wechatGroupDetailToMergeObjectDataFields(ei, groupDetailResult, status);
            for (Map.Entry<String, Object> currentKeyValueEntry : currentObjectData.entrySet()) {
                if(currentKeyValueEntry.getKey() != null && !Objects.equals(currentKeyValueEntry.getValue(), objectData.get(currentKeyValueEntry.getKey()))){
                    objectData.put(currentKeyValueEntry.getKey(), currentKeyValueEntry.getValue());
                    dataUpdated = true;
                }
            }
            boolean appScopeIsChange = WechatWorkExternalUserObjManager.fillAppScope(objectData, appScopeEnum);
            if (appScopeIsChange) {
                dataUpdated = true;
            }
            //更新企微群
            if(dataUpdated){
                ActionEditArg actionEditArg = new ActionEditArg();
                actionEditArg.setObjectData(objectData);
                owner = owner == null ? SuperUserConstants.USER_ID : owner;
                objectData.setOwner(owner);
                Result<ActionEditResult> updateResult = metadataActionService.edit(systemHeader, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), true, true, actionEditArg);
                dataUpdated = updateResult.isSuccess();
                log.info("doUpdateWechatGroupToCrm ei: {} result: {} data: {}", ei, updateResult, objectData);
            }
            //更新企微群成员
            dataUpdated = wechatGroupUserObjDescribeManager.doUpdateGroupUserToCrm(ei,groupDetailResult,objectData.getId(), owner, appScopeEnum);
        } catch (Exception e) {
            log.error("doUpdateWechatGroupToCrm error ei: {} appScope: {} data: {}", ei, appScopeEnum, groupDetailResult);
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }


        return dataUpdated;
    }


    public Map<String, ObjectData> getObjectDataMap(String ea, Collection<String> groupIdToDisplay) {
        Map<String, ObjectData> weChatGroupDataMap = new HashMap<>(groupIdToDisplay.size());
        Iterator<List<String>> ite = Iterables.partition(groupIdToDisplay, 100).iterator();
        ite.forEachRemaining(groupIds -> {
            com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> r = this.listGroupObjectDataByIdsLimited(eieaConverter.enterpriseAccountToId(ea), groupIds);
            if(r.getData() != null && r.getData().getDataList() != null){
                r.getData().getDataList().forEach(objectData -> {
                    weChatGroupDataMap.put(objectData.getString("chat_id"), objectData);
                });
            }
        });
        return weChatGroupDataMap;
    }

    public Result<Page<ObjectData>> listGroupObjectDataByIdsLimited(Integer ei, List<String> syncGroupIds) {
        HeaderObj searchHeader = new HeaderObj(ei, -10000);
        Filter filter = new Filter();
        filter.setFieldName("chat_id");
        filter.setOperator(Filter.OperatorContants.IN);
        filter.setFieldValues(syncGroupIds);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(100);
        searchQuery.setFilters(Collections.singletonList(filter));
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);
        return metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), controllerListArg);
    }


    public boolean doAddWechatGroupToCrm(Integer ei, Integer status, AppScopeEnum appScopeEnum, CustomerGroupDetailResult groupDetailResult) {

        if (groupDetailResult == null || groupDetailResult.getQywxGroupChat() == null) {
            log.warn("doAddWechatGroupToCrm group detail is null ei: {} appScope: {}", ei, appScopeEnum);
            return false;
        }
        CustomerGroupDetailResult.QywxGroupChat groupChat = groupDetailResult.getQywxGroupChat();
        boolean addResult = false;
        String chatId = groupChat.getGroupId();
        String lockKey = String.format(HANDLE_CHAT_GROUP_LOCK_KEY, ei, chatId);
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("doAddWechatGroupToCrm ei: {} chatId: {} lock: {} appScope: {}", ei, chatId, lockSuccess, appScopeEnum);
            Integer owner = groupChat.getFsUserId();
            if (appScopeEnum == AppScopeEnum.MARKETING) {
                owner = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea, groupChat.getGroupOwner());
            }
            owner = owner == null ? wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea) : owner;
            if (!lockSuccess) {
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setChatId(chatId);
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.ADD_EVENT);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.GROUP_OBJECT_TYPE);
                    qywxEventDelayMqArg.setGroupDetailResult(groupDetailResult);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    qywxEventDelayMqArg.setOwnerId(owner);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return false;
            }

            Result<Page<ObjectData>> result = listGroupObjectDataByIdsLimited(ei, Lists.newArrayList(chatId));
            if (result.isSuccess() && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getDataList())) {
                // 重新获取最新的群详情，而不是使用延迟消息中的旧数据
                CustomerGroupDetailResult latestGroupDetail = customerGroupManager.queryQywxGroupDetail(ea, chatId);
                if (latestGroupDetail != null) {
                    doUpdateWechatGroupToCrm(ei, status, result.getData().getDataList().get(0), owner, appScopeEnum, latestGroupDetail);
                }
                return false;
            }
            QueryCustomerGroupListResult groupListResult = new QueryCustomerGroupListResult();
            groupListResult.setGroupId(groupChat.getGroupId());
            groupListResult.setGroupName(groupChat.getGroupName());
            groupListResult.setGroupOwnerId(groupChat.getGroupOwner());
            groupListResult.setGroupOwner(StringUtils.isNotBlank(groupChat.getOwnerName()) ? groupChat.getOwnerName() : groupChat.getGroupOwner());
            groupListResult.setCreateTime(groupChat.getCreateTime());
            if (CollectionUtils.isNotEmpty(groupChat.getQywxGroupMembers())) {
                groupListResult.setGroupMemberCount(groupChat.getQywxGroupMembers().size());
            }
            //写到crm对象
            HeaderObj addHeader = new HeaderObj(ei, -10000);
            ObjectData objectData = wechatGroupDetailToMergeObjectDataFields(ei, groupDetailResult,status);
            WechatWorkExternalUserObjManager.fillAppScope(objectData, appScopeEnum);
            objectData.setOwner(owner);
            ActionAddArg actionAddArg = new ActionAddArg();
            actionAddArg.setObjectData(objectData);
            Result<ActionAddResult> addGroupResult = metadataActionService.add(addHeader, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), false, actionAddArg);
            log.info("doAddWechatGroupToCrm result: {} ea: {} data: {}", addGroupResult, ea, objectData);
            if (addGroupResult.isSuccess()) {
                //处理群成员数据
                String wechatGroupId = addGroupResult.getData().getObjectData().getId();
                boolean addGroupUserResult = wechatGroupUserObjDescribeManager.batchAddWechatGroupUserToCrm(ei,groupDetailResult,wechatGroupId, owner, appScopeEnum);
                if (addGroupUserResult) {
                    addResult = true;
                }
            }
        } catch (Exception e){
            log.error("init wechat group error ei: {} appScope: {} data: {} e: ", ei, appScopeEnum, groupDetailResult, e);
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }
        return addResult;
    }


    private ObjectData wechatGroupDetailToMergeObjectDataFields(Integer ei, CustomerGroupDetailResult customerGroupDetailResult,Integer status) {
        CustomerGroupDetailResult.QywxGroupChat qywxGroupChat = customerGroupDetailResult.getQywxGroupChat();
        ObjectData objectData = new ObjectData(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        objectData.setTenantId(ei);
        objectData.put("name", qywxGroupChat.getGroupName());
        if (StringUtils.isBlank(qywxGroupChat.getGroupName())) {
            if (qywxGroupChat.getQywxGroupMembers().size() == 1){
                objectData.put("name", "群聊");
            } else {
                String chatName = getChatName(qywxGroupChat.getQywxGroupMembers());
                objectData.put("name", chatName);
            }
        }
        objectData.put("chat_id", qywxGroupChat.getGroupId());
        objectData.put("notice", qywxGroupChat.getNotice());
        objectData.put("follow_status", String.valueOf(status));
        objectData.put("member_count", qywxGroupChat.getQywxGroupMembers().size());
        objectData.put("leader_id", qywxGroupChat.getGroupOwner());
        objectData.put("leader_name", qywxGroupChat.getOwnerName());
        //默认正常状态
        objectData.put("status", "0");
        //默认创建群
        objectData.put("update_detail", "0");
        if (qywxGroupChat.getCreateTime() != null) {
            objectData.put("chat_create_time", qywxGroupChat.getCreateTime() * 1000L);
        }
        return objectData;
    }

    private String getChatName(List<CustomerGroupDetailResult.QywxGroupMember> qywxGroupMembers) {
        String chatName = "";
        List<String> members;
        if (qywxGroupMembers.size() == 2) {
            members = qywxGroupMembers.stream().map(CustomerGroupDetailResult.QywxGroupMember::getMemberName).collect(Collectors.toList());
        } else {
            members = qywxGroupMembers.subList(0,3).stream().map(CustomerGroupDetailResult.QywxGroupMember::getMemberName).collect(Collectors.toList());
        }
        chatName = String.join("、", members);
        return chatName;
    }

    public ObjectData getGroupObject(int ei, String chatId) {
        HeaderObj searchHeader = new HeaderObj(ei, -10000);
        Filter filter = new Filter();
        filter.setFieldName("chat_id");
        filter.setOperator(Filter.OperatorContants.IN);
        filter.setFieldValues(ImmutableList.of(chatId));
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(100);
        searchQuery.setFilters(Collections.singletonList(filter));
        ControllerListArg controllerListArg = new ControllerListArg();
        controllerListArg.setIncludeDescribe(false);
        controllerListArg.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> objectResult = metadataControllerService.list(searchHeader, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), controllerListArg);
        if (objectResult.isSuccess() && objectResult.getData() != null && CollectionUtils.isNotEmpty(objectResult.getData().getDataList())) {
            return objectResult.getData().getDataList().get(0);
        }
        return null;
    }

    public boolean changeGroupUpdateDetail(int ei, String chatId, String updateDetail,Integer memberCount,CustomerGroupDetailResult customerGroupResult) {
        ObjectData objectData = this.getGroupObject(ei,chatId);
        HeaderObj addHeader = new HeaderObj(ei, -10000);

        String ea = eieaConverter.enterpriseIdToAccount(ei);
//        Integer fsUserId = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea, customerGroupResult.getQywxGroupChat().getGroupOwner());
//        qywxUserManager.updateWechatGroupObjOwner(ea, customerGroupResult.getQywxGroupChat().getGroupOwner(), fsUserId);

        ActionEditArg actionEditArg = new ActionEditArg();
        objectData.setTenantId(ei);
        objectData.put("update_detail",updateDetail);
        objectData.put("member_count",memberCount);
        if (StringUtils.isBlank(customerGroupResult.getQywxGroupChat().getGroupName())) {
            if (customerGroupResult.getQywxGroupChat().getQywxGroupMembers().size() == 1){
                objectData.put("name", "群聊");
            } else {
                String chatName = getChatName(customerGroupResult.getQywxGroupChat().getQywxGroupMembers());
                objectData.put("name", chatName);
            }
        }
        List<CustomerGroupDetailResult.Invitor> adminList = customerGroupResult.getQywxGroupChat().getAdminList();
        if (adminList != null) {
            List<String> userids = adminList.stream().map(CustomerGroupDetailResult.Invitor::getUserId).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userids)) {
                List<QyWxAddressBookEntity> qyWxAddressBookEntities = qywxAddressBookManager.queryEaAndUserId(ea, userids);
                if (qyWxAddressBookEntities != null) {
                    List<String> names = qyWxAddressBookEntities.stream().map(QyWxAddressBookEntity::getName).collect(Collectors.toList());
                    objectData.put("chat_manager", String.join(",", names));
                }
                List<QywxVirtualFsUserEntity> currentVirtualUserByEaAndQyIds = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(ea, userids);
                if (currentVirtualUserByEaAndQyIds != null) {
                    List<Integer> userIds = currentVirtualUserByEaAndQyIds.stream().map(QywxVirtualFsUserEntity::getUserId).collect(Collectors.toList());
                    crmV2Manager.doAddTeamMemberToCrm(new HeaderObj(ei, -10000), userIds, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), objectData.getId());
                }
            }
        }
        actionEditArg.setObjectData(objectData);
        return metadataActionService.edit(addHeader, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), true, true, actionEditArg).isSuccess();
    }

    @FilterLog
    public List<QueryGroupOwnerListResult> getAllChatGroup(String ea, Integer fsUserId) {
        List<QueryGroupOwnerListResult> queryGroupOwnerListResults = Lists.newArrayList();

        try {
            PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
            paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
            PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1);
            paasQueryArg.addFilter("status", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("0"));
            paasQueryArg.addFilter(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, PaasAndCrmOperatorEnum.IN.getCrmOperator(), Lists.newArrayList(AppScopeEnum.MARKETING.getValue()));
            paasQueryArg.addFilter("record_type", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList("default__c"));
            boolean isOpen = dataPermissionManager.getNewDataPermissionSetting(ea);
            if (isOpen && fsUserId != null) {
                List<String> staffIds = Lists.newArrayList();
                String accessToken = qywxManager.getAccessToken(ea);
                List<DepartmentStaffResult.StaffInfo> staffInfoList = qywxManager.queryAllStaff(ea, accessToken, false, false);
                if(CollectionUtils.isEmpty(staffInfoList)){
                    log.warn("getAllChatGroup staffInfoList is null, ea:{}", ea);
                    return queryGroupOwnerListResults;
                }
                List<Integer> qywxAccessibleDepartmentIds = dataPermissionManager.getQywxAccessibleDepartmentIdsByUserId(ea, fsUserId,true);
                if(CollectionUtils.isEmpty(qywxAccessibleDepartmentIds)){
                    return queryGroupOwnerListResults;
                }
                if (!qywxAccessibleDepartmentIds.contains(defaultAllDepartment)){
                    qywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds.stream().map(o -> o-QywxUserConstants.BASE_QYWX_DEPARTMENT_ID).collect(Collectors.toList());
                    List<Integer> finalQywxAccessibleDepartmentIds = qywxAccessibleDepartmentIds;
                    staffIds = staffInfoList.stream().filter(staffInfo -> CollectionUtils.containsAny(staffInfo.getDepartment(), finalQywxAccessibleDepartmentIds)).map(staffInfo -> staffInfo.getUserId()).collect(Collectors.toList());
                    paasQueryArg.addFilter("leader_id", "IN", staffIds.stream().map(String::valueOf).collect(Collectors.toList()));
                }
            }
            paasFilterArg.setQuery(paasQueryArg);
            InnerPage<ObjectData> result = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
            if (result == null || CollectionUtils.isEmpty(result.getDataList())) {
                return queryGroupOwnerListResults;
            }

            List<ObjectData> groupObjectDataList = result.getDataList();
            String leadIdFieldName = "leader_id";
            List<String> leaderIdList = groupObjectDataList.stream().filter(e -> e.containsKey(leadIdFieldName)).map(objectData -> objectData.getString(leadIdFieldName)).distinct().collect(Collectors.toList());

            List<QywxEmployeeResult> qywxEmployeeResultList = qywxEmployeeManager.batchByQyUserIds(ea, leaderIdList, false);
            Map<String, String> userIdNameMap = qywxEmployeeResultList.stream().collect(Collectors.toMap(QywxEmployeeResult::getUserId, QywxEmployeeResult::getName, (v1, v2) -> v1));

            result.getDataList().forEach(objectData -> {
                String leaderId = objectData.getString(leadIdFieldName);
                QueryGroupOwnerListResult queryGroupOwnerListResult = new QueryGroupOwnerListResult();
                String leaderName = userIdNameMap.containsKey(leaderId) ? userIdNameMap.get(leaderId) : objectData.getString("leader_name");
                queryGroupOwnerListResult.setGroupOwnerUserName(leaderName);
                queryGroupOwnerListResult.setGroupOwnerUserId(leaderId);
                queryGroupOwnerListResults.add(queryGroupOwnerListResult);
            });
        } catch (Exception e) {
            log.error("query WechatGroupObj list error ea:{}", ea, e);
        }
        return queryGroupOwnerListResults;
    }

    public List<QueryGroupOwnerListResult> getChatGroupByOwnerIdList(List<String> ownerIdList, String ea) {
        if (CollectionUtils.isEmpty(ownerIdList)) {
            return Lists.newArrayList();
        }
        List<QueryGroupOwnerListResult> queryGroupOwnerListResults = Lists.newArrayList();
        ControllerListArg params = new ControllerListArg();
        params.setObjectDescribeApiName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("leader_id", ownerIdList, FilterOperatorEnum.IN);
        searchQuery.addFilter("record_type", Lists.newArrayList("default__c"), FilterOperatorEnum.EQ);
        params.setSearchQuery(searchQuery);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), params);
        if (total == null || total <= 0) {
            return Lists.newArrayList();
        }
        searchQuery.setLimit(total);
        searchQuery.setOffset(0);
        Result<Page<ObjectData>> result = metadataControllerServiceManager.listResults(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000), CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), params);
        if (result.isSuccess() && result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getDataList())) {
            Page<ObjectData> dataPage = result.getData();
            dataPage.getDataList().forEach(objectData -> {
                QueryGroupOwnerListResult queryGroupOwnerListResult = new QueryGroupOwnerListResult();
                queryGroupOwnerListResult.setGroupOwnerUserName(objectData.getString("leader_name"));
                queryGroupOwnerListResult.setGroupOwnerUserId(objectData.getString("leader_id"));
                queryGroupOwnerListResults.add(queryGroupOwnerListResult);
            });
        }
        return queryGroupOwnerListResults.stream().distinct().collect(Collectors.toList());
    }

    public void tryUpdateCustomFieldLabel(String ea){
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), -10000);
        ObjectDescribe objectDescribe = getOrCreateWechatGroupObjDescribe(ea);
        if(objectDescribe == null){
            log.warn("Ea:{} have not WechatGroupObj", ea);
            return;
        }
        //chat_delete_time
        if(!objectDescribe.getFields().containsKey("chat_delete_time")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
            arg.setFieldDescribe("{\"type\":\"date_time\",\"define_type\":\"package\",\"api_name\":\"chat_delete_time\",\"label\":\"群解散时间\",\"help_text\":\"\",\"is_required\":false,\"is_extend\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"time_zone\":\"GMT+8\",\"date_format\":\"yyyy-MM-dd HH:mm\"}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
        //chat_manager
        if(!objectDescribe.getFields().containsKey("chat_manager")){
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
            arg.setFieldDescribe("{\"type\":\"text\",\"define_type\":\"package\",\"api_name\":\"chat_manager\",\"label\":\"群管理员\",\"help_text\":\"\",\"is_required\":false,\"is_extend\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"default_value\":\"\",\"default_is_expression\":false,\"default_to_zero\":false,\"max_length\":4000}");
            objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
        }
    }

    public List<String> filterGroupId(List<String> externalUserIds,String ea, List<String> groupIds) {
        if (CollectionUtils.isEmpty(externalUserIds)){
            return externalUserIds;
        }
        List<String> validList = Lists.newArrayList();
        List<String> existGroupUserIds = wechatGroupUserObjDescribeManager.queryGroupUserObjectUserListByV3(ea,groupIds, AppScopeEnum.MARKETING);
        PageUtil pageUtil = new PageUtil(externalUserIds, 1000);
        for (int i = 1; i <= pageUtil.getPageCount(); i++) {
            List<String> currentList = pageUtil.getPagedList(i);
            if (CollectionUtils.isEmpty(existGroupUserIds)) {
                validList.addAll(currentList);
            }else {
                List<String> filers = currentList.stream().filter(a -> !existGroupUserIds.contains(a)).collect(Collectors.toList());
                validList.addAll(filers);
            }
        }

        return validList;
    }
    //如果群成员绑定了纷享账户，将其加入到对应的群的相关团队
    public void initTeamMember(List<String> eas) {
        for (String ea : eas) {
            try {
                long beginTime = System.currentTimeMillis();
                PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
                queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
                PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
                queryFilterArg.setQuery(paasQueryArg);
                List<String> selectFields = Lists.newArrayList("_id", "name");
                queryFilterArg.setSelectFields(selectFields);
                int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
                if (totalCount <= 0) {
                    log.info("wechat group initTeamMember totalCount is zero, ea: {}", ea);
                    return;
                }
                int pageSize = 1000;
                int totalPage = totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
                for(int i = 1; i <= totalPage; i++) {
                    InnerPage<ObjectData> wechatGroupDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, -10000, queryFilterArg, i, pageSize);
                    if (wechatGroupDataPage == null || CollectionUtils.isEmpty(wechatGroupDataPage.getDataList())) {
                        break;
                    }
                    for (ObjectData objectData : wechatGroupDataPage.getDataList()) {
                        PaasQueryFilterArg wechatGroupUserFilter = new PaasQueryFilterArg();
                        wechatGroupUserFilter.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName());
                        PaasQueryArg wechatGroupUserPaasQueryArg = new PaasQueryArg(0, 1000);
                        wechatGroupUserPaasQueryArg.addFilter("qw_group_chat_id", PaasAndCrmOperatorEnum.EQUALS.getCrmOperator(), Lists.newArrayList(objectData.getId()));
                        wechatGroupUserFilter.setQuery(wechatGroupUserPaasQueryArg);
                        wechatGroupUserFilter.setSelectFields(Lists.newArrayList("user_id"));
                        int wechatGroupUserTotalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, wechatGroupUserFilter);
                        if (wechatGroupUserTotalCount <= 0) {
                            continue;
                        }
                        InnerPage<ObjectData> wechatGroupUserDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, wechatGroupUserFilter, 1, wechatGroupUserTotalCount);
                        if (wechatGroupUserDataPage == null || CollectionUtils.isEmpty(wechatGroupUserDataPage.getDataList())) {
                            continue;
                        }
                        Set<String> qywxUserIdSet = wechatGroupUserDataPage.getDataList().stream().map(e -> e.getString("user_id")).collect(toSet());
                        List<QywxVirtualFsUserEntity> virtualFsUserEntityList = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(ea, Lists.newArrayList(qywxUserIdSet));
                        if (CollectionUtils.isEmpty(virtualFsUserEntityList)) {
                            log.info("刷取相关团队 群成员在虚拟表中没记录,ea: {} 群名称: {} 群id: {}", ea, objectData.getName(), objectData.getId());
                            continue;
                        }
                        List<Integer> fsUserIdList = virtualFsUserEntityList.stream().filter(e -> e.getUserId() != null && !QywxUserConstants.isVirtualUserId(e.getUserId()))
                                .map(QywxVirtualFsUserEntity::getUserId).collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(fsUserIdList)) {
                            log.info("刷取相关团队 群成员都没有绑定CRM账号,ea: {} 群名称: {} 群id: {}", ea, objectData.getName(), objectData.getId());
                            continue;
                        }
                        Result<Void> result = crmV2Manager.doAddTeamMemberToCrm(ea, SuperUserConstants.USER_ID, fsUserIdList, Lists.newArrayList(objectData.getId()),
                                CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), Lists.newArrayList("4"), "1");
                        log.info("刷取相关团队 群更新相关团队成功,ea: {} 群名称: {} 群id: {} result: {} fsUserId: {}", ea, objectData.getName(), objectData.getId(), result, fsUserIdList);
                    }
                }
                log.info("刷取相关团队 ea：{} 刷取企微客户群相关团队结束，耗时: {}", ea, System.currentTimeMillis() - beginTime);
            } catch (Exception e) {
                log.error("企微客户群刷相关团队失败, ea: {}", ea, e);
            }
        }
    }

    public CustomerGroupDetailResult handleAddMemberEvent(String ea, String chatId, AppScopeEnum appScopeEnum, CustomerGroupDetailResult customerGroupDetailResult) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        String lockKey = String.format(HANDLE_CHAT_GROUP_LOCK_KEY, ei, chatId);
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("企微群添加成员事件， ea: {} chatId: {} appScopeEnum: {} lock: {} data: {}", ea, chatId, appScopeEnum, lockSuccess, customerGroupDetailResult);
            if(!lockSuccess) {
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费一分钟
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setChatId(chatId);
                    qywxEventDelayMqArg.setGroupDetailResult(customerGroupDetailResult);
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.ADD_MEMBER_EVENT);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.GROUP_OBJECT_TYPE);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return customerGroupDetailResult;
            }
            ObjectData groupObject = getGroupObject(ei, chatId);
            if (groupObject == null) {
                log.warn("handleAddMemberEvent groupObject is null, ei: {} chatId: {}", ei, chatId);
                return customerGroupDetailResult;
            }
            if (appScopeEnum == AppScopeEnum.MARKETING) {
                customerGroupDetailResult = customerGroupManager.queryQywxGroupDetail(ea, chatId);
            }
            if (customerGroupDetailResult == null || customerGroupDetailResult.getQywxGroupChat() == null) {
                log.warn("handleAddMemberEvent group result is null, ea: {} chatId: {} appScope: {}", ea, chatId, appScopeEnum);
                return customerGroupDetailResult;
            }
            List<CustomerGroupDetailResult.QywxGroupMember> qywxGroupMembers = customerGroupDetailResult.getQywxGroupChat().getQywxGroupMembers();
            int memberCount = qywxGroupMembers.size();
            List<String> selectFields = Lists.newArrayList("_id", "user_id", "status", "app_scope");
            List<ObjectData> groupUserDataList = wechatGroupUserObjDescribeManager.queryGroupUserByGroupId(ea, groupObject.getId(), selectFields);
            Map<String, ObjectData> groupUserIdToDataMap = groupUserDataList.stream().collect(Collectors.toMap(e -> e.getString("user_id"), e -> e, (v1, v2) -> v1));
            List<CustomerGroupDetailResult.QywxGroupMember> addMemberList = Lists.newArrayList();
            for (CustomerGroupDetailResult.QywxGroupMember member : qywxGroupMembers) {
                ObjectData objectData = groupUserIdToDataMap.get(member.getGroupMemberUserId());
                if (objectData == null || objectData.get("status") == null || !"0".equals(String.valueOf(objectData.get("status")))) {
                    addMemberList.add(member);
                } else {
                    Object appScopeObj = objectData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
                    List<String> appScopeList = appScopeObj == null ? Lists.newArrayList() : (List<String>) appScopeObj;
                    if (!appScopeList.contains(appScopeEnum.getValue())) {
                        appScopeList.add(appScopeEnum.getValue());
                        Map<String, Object> updateMap = Maps.newHashMap();
                        updateMap.put("_id", objectData.getId());
                        updateMap.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, appScopeList);
                        Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_USER_OBJ.getName(), updateMap);
                        log.info("update group user app scope result: {} ea: {} data: {}", result, ea, updateMap);
                    }
                }
            }
            Integer owner = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea, customerGroupDetailResult.getQywxGroupChat().getGroupOwner());
            wechatGroupUserObjDescribeManager.pageAddWechatGroupUserToCrm(ei, addMemberList, groupObject.getId(), owner, appScopeEnum);
            // 企微群成员若有CRM账号，则加到群对象相关团队
            wechatGroupUserObjDescribeManager.addWechatGroupTeamMember(ea, groupObject.getId(), addMemberList, appScopeEnum);
            //变更企微群状态
            boolean addmember = changeGroupUpdateDetail(ei, chatId, "addmember", memberCount, customerGroupDetailResult);
            log.info("add_member ea: {} chatId: {} result:{}", ea, chatId, addmember);
        } catch (Exception e) {
            log.error("handleAddMemberEvent error ea: {} chatId: {} appScopeEnum: {} data: {}", ea, chatId, appScopeEnum, customerGroupDetailResult, e);
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }
        return customerGroupDetailResult;
    }

    public List<String> handleDeleteMemberEvent(String ea, String chatId, AppScopeEnum appScopeEnum, CustomerGroupDetailResult customerGroupResult) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        String lockKey = String.format(HANDLE_CHAT_GROUP_LOCK_KEY, ei, chatId);
        String lockValue = appScopeEnum.getValue() + "-" + RandomStringUtils.randomAscii(10);
        try {
            boolean lockSuccess = redisManager.lock(lockKey, lockValue, 60);
            log.info("企微群删除成员事件， ea: {} chatId: {} appScopeEnum: {} lock: {} data: {}", ea, chatId, appScopeEnum, lockSuccess, customerGroupResult);
            if(!lockSuccess) {
                // 如果获取不到锁 并且获取到锁的应用不是本次应用的通知 延迟消费一分钟
                String existLockValue = redisManager.get(lockKey);
                if (StringUtils.isNotBlank(existLockValue) && !existLockValue.contains(appScopeEnum.getValue())) {
                    QywxEventDelayMqArg qywxEventDelayMqArg = new QywxEventDelayMqArg();
                    qywxEventDelayMqArg.setEi(ei);
                    qywxEventDelayMqArg.setChatId(chatId);
                    qywxEventDelayMqArg.setGroupDetailResult(customerGroupResult);
                    qywxEventDelayMqArg.setEvent(QywxEventDelayMqArg.DELETE_MEMBER_EVENT);
                    qywxEventDelayMqArg.setChangeObjectType(QywxEventDelayMqArg.GROUP_OBJECT_TYPE);
                    qywxEventDelayMqArg.setAppScopeEnum(appScopeEnum);
                    delayQueueSender.sendByObj(eieaConverter.enterpriseIdToAccount(ei), qywxEventDelayMqArg, DelayQueueTagConstants.QYWX_CHANGE_EVENT, RocketMqDelayLevelConstants.TEN_SECOND);
                }
                return Lists.newArrayList();
            }
            if (appScopeEnum == AppScopeEnum.MARKETING) {
                customerGroupResult = customerGroupManager.queryQywxGroupDetail(ea, chatId);
            }
            if (customerGroupResult == null || customerGroupResult.getQywxGroupChat() == null) {
                log.warn("handleDeleteMemberEvent group result is null, ea: {} chatId: {} appScope: {}", ea, chatId, appScopeEnum);
                return Lists.newArrayList();
            }
            List<CustomerGroupDetailResult.QywxGroupMember> qywxGroupMemberList = customerGroupResult.getQywxGroupChat().getQywxGroupMembers();
            int memberNumbs = qywxGroupMemberList.size();
            List<String> groupUserIds = new ArrayList<>();
            ObjectData objectDataDel = getGroupObject(ei, chatId);
            if (objectDataDel == null) {
                log.warn("handleDeleteMemberEvent object is null, ei: {} chatId: {}", ei, chatId);
                return Lists.newArrayList();
            }
            if (CollectionUtils.isNotEmpty(qywxGroupMemberList)) {
                for (CustomerGroupDetailResult.QywxGroupMember member : qywxGroupMemberList) {
                    groupUserIds.add(member.getGroupMemberUserId());
                }
            }
            List<String> userList = wechatGroupUserObjDescribeManager.queryGroupUserObjectUserList(ei, objectDataDel.getId());
            if (CollectionUtils.isNotEmpty(userList)) {
                userList = userList.stream().filter(userId -> !groupUserIds.contains(userId)).collect(Collectors.toList());
            }
            log.info("need del member ea: {} chatId: {} userId:{}", ea, chatId, GsonUtil.getGson().toJson(userList));
            Integer owner = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea, customerGroupResult.getQywxGroupChat().getGroupOwner());
            wechatGroupUserObjDescribeManager.pageDelWechatGroupUserCrm(ei,userList,objectDataDel.getId(), owner, appScopeEnum);
            // 移除企微客户群相关团队
            if (CollectionUtils.isNotEmpty(userList)) {
                List<Integer> removeUserIdList = Lists.newArrayList();
                List<QywxVirtualFsUserEntity> currentVirtualUserByEaAndQyIds = qywxVirtualFsUserManager.getCurrentVirtualUserByEaAndQyIds(ea, userList);
                if (CollectionUtils.isNotEmpty(currentVirtualUserByEaAndQyIds)) {
                    List<Integer> fsUserIdList = currentVirtualUserByEaAndQyIds.stream().map(QywxVirtualFsUserEntity::getUserId).filter(Objects::nonNull).filter(e -> !QywxUserConstants.isVirtualUserId(e)).collect(Collectors.toList());
                    removeUserIdList.addAll(fsUserIdList);
                }
                try {
                    com.facishare.marketing.common.result.Result<Map<String, String>> result = qyweixinAccountBindManager.outAccountToFsAccountBatch(ea, qywxCrmAppId, userList);
                    Map<String, String> qyexUserIdToFsUserIdMap = result.getData() == null ? Maps.newHashMap() : result.getData();
                    qyexUserIdToFsUserIdMap.forEach((k, v) -> {
                        Integer fsUserId = QyweixinAccountBindManager.parseFsUserId(v);
                        if (fsUserId != null) {
                            removeUserIdList.add(fsUserId);
                        }});
                } catch (Exception e) {
                    log.error("handleDeleteMemberEvent get outAccountToFsAccountBatch error, ea: {} userId: {}", ea, userList);
                }
                // 移除企微客户群相关团队
                wechatGroupUserObjDescribeManager.delWechatGroupUserTeamMember(ea, removeUserIdList, objectDataDel.getId());
            }
            //变更企微群的状态
            boolean delmember = changeGroupUpdateDetail(ei, chatId, "delmember", memberNumbs, customerGroupResult);
            log.info("del_member ea: {} chatId: {} result:{}", ea, chatId, delmember);
            return userList;
        } catch (Exception e) {
            log.error("handleDeleteMemberEvent error, ea: {} chatId: {} appScope: {} data: {} ",ea ,chatId, appScopeEnum, customerGroupResult);
        } finally {
            redisManager.unLock(lockKey, lockValue);
        }
        return Lists.newArrayList();
    }

    public void handleChangeOwnerEvent(String ea, String chatId, AppScopeEnum appScopeEnum, CustomerGroupDetailResult customerGroup) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        if (appScopeEnum == AppScopeEnum.MARKETING) {
            customerGroup = customerGroupManager.queryQywxGroupDetail(ea, chatId);
        }
        ObjectData objectData = getGroupObject(ei, chatId);
        if (objectData == null) {
            log.warn("handleChangeOwnerEvent object is null, ei: {} chatId: {}", ei, chatId);
            return;
        }
        if (customerGroup == null || customerGroup.getQywxGroupChat() == null) {
            log.warn("handleChangeOwnerEvent group result is null, ea: {} chatId: {} appScope: {}", ea, chatId, appScopeEnum);
            return;
        }
        fillGroupOwnerName(ea, customerGroup);
        HeaderObj addHeader = new HeaderObj(ei, -10000);
        ActionEditArg actionEditArg = new ActionEditArg();
        objectData.setTenantId(ei);
        objectData.put("leader_id", customerGroup.getQywxGroupChat().getGroupOwner());
        objectData.put("leader_name", customerGroup.getQywxGroupChat().getOwnerName());
        objectData.put("update_detail", "changeowner");
        Object appScopeObj = objectData.get(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD);
        if (appScopeObj != null) {
            List<String> appScopeList = (List<String>) appScopeObj;
            if (!appScopeList.contains(appScopeEnum.getValue())) {
                appScopeList.add(appScopeEnum.getValue());
                objectData.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, appScopeList);
            }
        }
        actionEditArg.setObjectData(objectData);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> resultResultOwner = metadataActionService.edit(addHeader, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), true, true, actionEditArg);
        log.info("change owner ea: {} chatId: {} result:{}", ea, chatId, GsonUtil.getGson().toJson(resultResultOwner));
        Integer fsUserId = wechatWorkExternalUserObjManager.queryFsUserIdByRoles(ea, customerGroup.getQywxGroupChat().getGroupOwner());
        qywxUserManager.updateWechatGroupObjOwner(ea, customerGroup.getQywxGroupChat().getGroupOwner(), fsUserId);
    }

    public void handleChangeNameEvent(String ea, String chatId, AppScopeEnum appScopeEnum, CustomerGroupDetailResult customerGroupDetailResult) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        if (appScopeEnum == AppScopeEnum.MARKETING) {
            customerGroupDetailResult = customerGroupManager.queryQywxGroupDetail(ea, chatId);
        }
        if (customerGroupDetailResult == null || customerGroupDetailResult.getQywxGroupChat() == null) {
            log.warn("handleChangeNameEvent group result is null, ea: {} chatId: {} appScope: {}", ea, chatId, appScopeEnum);
            return;
        }
        //CustomerGroupDetailResult customerChangeName = customerGroupManager.queryQywxGroupDetail(ea, chatId);
        ObjectData objectDataDetail = getGroupObject(ei, chatId);
        if (objectDataDetail == null) {
            log.warn("handleChangeNameEvent object is null, ei: {} chatId: {}", ei, chatId);
            return;
        }
        HeaderObj header = new HeaderObj(ei, -10000);
        ActionEditArg actionEdit = new ActionEditArg();
        objectDataDetail.setTenantId(ei);
        objectDataDetail.put("name", customerGroupDetailResult.getQywxGroupChat().getGroupName());
        objectDataDetail.put("update_detail", "changename");
        actionEdit.setObjectData(objectDataDetail);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> resultResult = metadataActionService.edit(header, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), true, true, actionEdit);
        log.info("change name ea: {} chatId: {} result:{}", ea, chatId, GsonUtil.getGson().toJson(resultResult));
    }

    public void handleChangeNoticeEvent(String ea, String chatId, AppScopeEnum appScopeEnum, CustomerGroupDetailResult customerGroupDetailResult) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        if (appScopeEnum == AppScopeEnum.MARKETING) {
            customerGroupDetailResult = customerGroupManager.queryQywxGroupDetail(ea, chatId);
        }
        if (customerGroupDetailResult == null || customerGroupDetailResult.getQywxGroupChat() == null) {
            log.warn("handleChangeNoticeEvent group result is null, ea: {} chatId: {} appScope: {}", ea, chatId, appScopeEnum);
            return;
        }
        ObjectData objectDataNotice = getGroupObject(ei, chatId);
        if (objectDataNotice == null) {
            log.warn("handleChangeNoticeEvent object is null, ei: {} chatId: {}", ei, chatId);
            return;
        }
        HeaderObj headerObj = new HeaderObj(ei, -10000);
        ActionEditArg actionEditNotice = new ActionEditArg();
        objectDataNotice.setTenantId(ei);
        objectDataNotice.put("notice", customerGroupDetailResult.getQywxGroupChat().getNotice());
        objectDataNotice.put("update_detail", "changenotice");
        actionEditNotice.setObjectData(objectDataNotice);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> resultNotice =metadataActionService.edit(headerObj, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), true, true, actionEditNotice);
        log.info("change notice ea: {} chatId: {} result:{}", ea, chatId, GsonUtil.getGson().toJson(resultNotice));
    }

    public void addAppSourceField(String ea) {
        HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
        Integer ei = eieaConverter.enterpriseAccountToId(ea);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
            return;
        }
        ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
        if (!objectDescribe.getFields().containsKey("app_scope")) {
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"WechatGroupObj\",\"type\":\"select_many\",\"define_type\":\"package\",\"api_name\":\"app_scope\",\"label\":\"应用范围\",\"help_text\":\"\",\"is_required\":false,\"is_unique\":false,\"is_active\":true,\"is_index\":true,\"status\":\"new\",\"is_extend\":false,\"default_value\":\"\",\"options\":[{\"not_usable\":false,\"label\":\"CRM\",\"value\":\"CRM\"},{\"not_usable\":false,\"label\":\"营销通\",\"value\":\"Marketing\"}]}");
            arg.setLayoutList("[{\"is_show\":true,\"is_required\":false,\"is_readonly\":false,\"render_type\":\"select_many\",\"api_name\":\"WechatGroupObj_detail_layout\",\"label\":\"默认布局\",\"is_default\":true}]");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("add app scope Filed ea:{} result: {}", ea, result);
        }
    }

    public void handleScrmStockWechatGroupData(String ea, CustomerGroupDetailResult customerGroupDetailResult) {
        if (customerGroupDetailResult == null || customerGroupDetailResult.getQywxGroupChat() == null) {
            return;
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        String chatId = customerGroupDetailResult.getQywxGroupChat().getGroupId();
        String leadId = customerGroupDetailResult.getQywxGroupChat().getGroupOwner();
        List<QywxEmployeeResult> qywxEmployeeResultList = qywxEmployeeManager.batchByQyUserIds(ea, Lists.newArrayList(leadId), false);
        if (CollectionUtils.isNotEmpty(qywxEmployeeResultList)) {
            customerGroupDetailResult.getQywxGroupChat().setOwnerName(qywxEmployeeResultList.get(0).getName());
        }
        Map<String, ObjectData> existGroupDataMap = getObjectDataMap(ea, Lists.newArrayList(chatId));
        ObjectData objectData = existGroupDataMap.get(chatId);
        if (objectData != null) {
            doUpdateWechatGroupToCrm(ei, 0, objectData, customerGroupDetailResult.getQywxGroupChat().getFsUserId(), AppScopeEnum.CRM, customerGroupDetailResult);
        } else {
            doAddWechatGroupToCrm(ei, 0, AppScopeEnum.CRM, customerGroupDetailResult);
        }
    }

    public void handleScrmWechatGroupChangeEvent(ExternalChatEvent externalChatEvent) {
        String changeType = externalChatEvent.getChangeType();
        String updateDetail = externalChatEvent.getUpdateDetail();
        String ea = externalChatEvent.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(ea);
        CustomerGroupDetailResult customerGroupDetailResult = JsonUtil.fromJson(externalChatEvent.getExternalChatDetail(), CustomerGroupDetailResult.class);

        CustomerGroupDetailResult.QywxGroupChat qywxGroupChat = customerGroupDetailResult.getQywxGroupChat();
        String ownerId = qywxGroupChat.getGroupOwner();
        // 目前只有分享云做这个判断，平台那边不能全网
        if (appVersionManager.isFxCloud() && (MapUtils.isEmpty(externalChatEvent.getUserIdMap()) || externalChatEvent.getUserIdMap().get(ownerId) == null)) {
            log.info("chat group owner is not bind to fs employee，msg: {}", externalChatEvent);
            return;
        }
        // 填充企微员工对应的纷享员工id
        fillFsUserId(ea, customerGroupDetailResult, externalChatEvent.getCorpId());
        fillGroupOwnerName(ea, customerGroupDetailResult);
        String chatId = externalChatEvent.getChatId();
        Map<String, ObjectData> existGroupDataMap = getObjectDataMap(ea, Lists.newArrayList(chatId));
        ObjectData existObjectData = existGroupDataMap.get(chatId);
        // 如果找不到这个客户群直接创建了，不管他是不是创建事件
        if (existObjectData == null || "create".equals(changeType)) {
           if (existObjectData != null) {
                doUpdateWechatGroupToCrm(ei, 0, existObjectData, customerGroupDetailResult.getQywxGroupChat().getFsUserId(), AppScopeEnum.CRM, customerGroupDetailResult);
            } else {
                doAddWechatGroupToCrm(ei, 0, AppScopeEnum.CRM, customerGroupDetailResult);
            }
        } else if ("update".equals(changeType)) {
            switch (updateDetail) {
                case "add_member":
                    handleAddMemberEvent(ea, chatId, AppScopeEnum.CRM, customerGroupDetailResult);
                    break;
                case "del_member":
                    handleDeleteMemberEvent(ea, chatId, AppScopeEnum.CRM, customerGroupDetailResult);
                    break;
                case "change_owner":
                    handleChangeOwnerEvent(ea, chatId, AppScopeEnum.CRM, customerGroupDetailResult);
                    break;
                case "change_name":
                    handleChangeNameEvent(ea, chatId, AppScopeEnum.CRM, customerGroupDetailResult);
                    break;
                case "change_notice":
                    handleChangeNoticeEvent(ea, chatId, AppScopeEnum.CRM, customerGroupDetailResult);
                    break;
            }
        }
    }

    private void fillFsUserId(String ea, CustomerGroupDetailResult customerGroupDetailResult, String corpId) {
        List<String> qywxUserIdList = Lists.newArrayList();
        CustomerGroupDetailResult.QywxGroupChat qywxGroupChat = customerGroupDetailResult.getQywxGroupChat();
        qywxUserIdList.add(qywxGroupChat.getGroupOwner());
        if (CollectionUtils.isNotEmpty(qywxGroupChat.getQywxGroupMembers())) {
            qywxGroupChat.getQywxGroupMembers().stream().filter(e -> e.getGroupMemberUserType() == GroupMemberTypeEnum.EMPLOYEE.getType()).forEach(e -> qywxUserIdList.add(e.getGroupMemberUserId()));
        }
        Map<String, Integer> result = qyweixinAccountBindManager.batchGetOutEmployeeBindFsUser(ea, corpId, qywxCrmAppId, qywxUserIdList);
        if (CollectionUtils.isNotEmpty(qywxGroupChat.getQywxGroupMembers())) {
            qywxGroupChat.getQywxGroupMembers().forEach(e -> e.setFsUserId(result.get(e.getGroupMemberUserId())));
        }
        qywxGroupChat.setFsUserId(result.get(qywxGroupChat.getGroupOwner()));
    }

    public void handleScrmWechatGroupDeleteEvent(ExternalChatEvent externalChatEvent) {
        dismissWeChatGroup(externalChatEvent.getFsEa(), externalChatEvent.getChatId(), System.currentTimeMillis(), AppScopeEnum.CRM);
    }

    public void dismissWeChatGroup(String ea, String chatId, long deleteTime, AppScopeEnum appScopeEnum) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        ObjectData objectData = getGroupObject(ei, chatId);
        if (objectData == null) {
            log.warn("dismissWeChatGroup object is null, ei: {} chatId: {}", ei, chatId);
            return;
        }
        HeaderObj headerObj = new HeaderObj(ei, SuperUserConstants.USER_ID);
        ActionEditArg actionEditNotice = new ActionEditArg();
        objectData.setTenantId(ei);
        objectData.put("status","1");
        objectData.put("update_detail","1");
        objectData.put("chat_delete_time", deleteTime);
        objectData.put(WechatWorkExternalUserObjManager.APP_SCOPE_FIELD, Lists.newArrayList());
        actionEditNotice.setObjectData(objectData);
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> result = metadataActionService.edit(headerObj, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), true, true, actionEditNotice);
        log.info("group dismiss ea: {} chatId: {} updateArg: {} result:{}", ea, chatId, objectData, GsonUtil.getGson().toJson(result));
    }

    private void fillGroupOwnerName(String ea, CustomerGroupDetailResult customerGroupDetailResult) {
        if (customerGroupDetailResult == null || customerGroupDetailResult.getQywxGroupChat() == null) {
            return;
        }
        CustomerGroupDetailResult.QywxGroupChat qywxGroupChat = customerGroupDetailResult.getQywxGroupChat();
        if (StringUtils.isNotBlank(customerGroupDetailResult.getQywxGroupChat().getOwnerName())) {
            // 已经有值了，直接返回
            return;
        }
        List<QywxEmployeeResult> qywxEmployeeResultList = qywxEmployeeManager.batchByQyUserIds(ea, Lists.newArrayList(qywxGroupChat.getGroupOwner()), false);
        if (CollectionUtils.isEmpty(qywxEmployeeResultList)) {
            return;
        }
        qywxGroupChat.setOwnerName(qywxEmployeeResultList.get(0).getName());
    }

    public void fixGroupOwnerName(String ea) {
        PaasQueryArg query = new PaasQueryArg(0, 1);
        List<String> selectFieldList = Lists.newArrayList("_id", "leader_id", "leader_name");
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName());
        queryFilterArg.setSelectFields(selectFieldList);
        queryFilterArg.setQuery(query);
        int totalCount = crmV2Manager.countCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg);
        if (totalCount <= 0) {
            log.info("fixGroupOwnerName wechatGroupDataList is empty ea: {}", ea);
            return;
        }
        int count = 0;
        int pageSize = 9;
        String lastId = null;
        while (count < totalCount) {
            InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.listCrmObjectScanByIdV3(ea, SuperUserConstants.USER_ID, queryFilterArg, lastId, pageSize);
            if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
                break;
            }
            List<ObjectData> objectDataList = objectDataInnerPage.getDataList();
            count += objectDataList.size();
            lastId = objectDataList.get(objectDataList.size() - 1).getId();
            List<String> leadIdList = objectDataList.stream().filter(e -> e.containsKey("leader_id")).map(e -> e.getString("leader_id")).distinct().collect(Collectors.toList());
            List<QywxEmployeeResult> qywxEmployeeResultList = qywxEmployeeManager.batchByQyUserIds(ea, leadIdList, false);
            Map<String, String> userIdToNameMap = qywxEmployeeResultList.stream().collect(Collectors.toMap(QywxEmployeeResult::getUserId, QywxEmployeeResult::getName, (v1, v2) -> v1));
            for (ObjectData objectData : objectDataList) {
                String oldLeaderName = objectData.getString("leader_name");
                String leaderId = objectData.getString("leader_id");
                if (StringUtils.isBlank(leaderId)) {
                    continue;
                }
                String newLeaderName = userIdToNameMap.get(leaderId);
                if (StringUtils.isBlank(newLeaderName)) {
                    continue;
                }
                if (!newLeaderName.equals(oldLeaderName)) {
                    ObjectData forUpdate = new ObjectData();
                    forUpdate.put("_id", objectData.getId());
                    forUpdate.put("leader_name", newLeaderName);
                    Result<ActionEditResult> result = crmV2Manager.editObjectData(ea, CrmObjectApiNameEnum.WECHAT_GROUP_OBJ.getName(), forUpdate);
                    log.info("更新群主名称，ea: {} arg: {} result: {}", ea, forUpdate, result);
                }
            }
        }
    }
}
