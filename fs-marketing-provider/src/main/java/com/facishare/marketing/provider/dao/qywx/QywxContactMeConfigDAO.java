package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.provider.entity.qywx.QywxContactMeConfigEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created by zhengh on 2020/3/11.
 */
public interface QywxContactMeConfigDAO {
    @Insert("INSERT INTO qywx_contact_config(uid, config_id, create_time, update_time) VALUES(#{entity.uid}, #{entity.configId}, now(), now()) ON CONFLICT DO NOTHING")
    int insert(@Param("entity") QywxContactMeConfigEntity entity);

    @Select("SELECT * FROM qywx_contact_config WHERE uid=#{uid}")
    QywxContactMeConfigEntity getByUid(@Param("uid")String uid);
}
