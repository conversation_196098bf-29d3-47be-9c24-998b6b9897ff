/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.whatsapp.syncdata;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.whatsapp.QueryDataByIdsArg;
import com.facishare.marketing.api.arg.whatsapp.SyncDataArg;
import com.facishare.marketing.api.vo.whatsapp.SyncDataResultVO;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.mongo.WhatsAppMessageMongoDao;
import com.facishare.marketing.provider.entity.whatsapp.WhatsAppMessageMongoEntity;
import com.facishare.marketing.provider.entity.whatsapp.WhatsAppMessageToEntity;
import com.facishare.marketing.statistic.common.util.DateUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.limit.GuavaLimiter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WhatsAppChatMessageSyncDataProcessor extends AbstractWhatsAppSyncDataProcessor {
    @Autowired
    private WhatsAppMessageMongoDao whatsAppMessageMongoDao;

    @Override
    public String getObjectApiName() {
        return CrmObjectApiNameEnum.WHATSAPP_CHAT_MESSAGE_OBJ.getName();
    }

    @Override
    public com.facishare.marketing.common.result.Result<SyncDataResultVO> syncData(SyncDataArg syncDataArg) {
        SyncDataResultVO result = new SyncDataResultVO();
        result.setObjectId("");
        ThreadPoolUtils.execute(() -> {
            innerSyncData(syncDataArg);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.WHATS_APP);
        return com.facishare.marketing.common.result.Result.newSuccess(result);
    }

    private void innerSyncData(SyncDataArg syncDataArg) {
        try {
            List<WhatsAppMessageMongoEntity> objectDataList = convert2MongoDataList(syncDataArg.getObjectDataList());
            Set<String> dataIds = objectDataList.stream().map(WhatsAppMessageMongoEntity::getMessageId).collect(Collectors.toSet());
            List<WhatsAppMessageMongoEntity> dbDataList = whatsAppMessageMongoDao.getByMessageIds(syncDataArg.getEa(), dataIds);
            List<String> existsIds = dbDataList.stream().map(WhatsAppMessageMongoEntity::getMessageId).collect(Collectors.toList());
            List<WhatsAppMessageMongoEntity> insertDataList = Lists.newArrayList(objectDataList);
            insertDataList.removeIf(x -> existsIds.contains(x.getMessageId()));
            if (CollectionUtils.isNotEmpty(insertDataList)) {
                insertDataList.forEach(objectData -> {
                    objectData.setEa(syncDataArg.getEa());
                    String lockKey = String.format("mk:whatsapp:%s:%s:%s", syncDataArg.getEa(), objectData.getObjectDescribeApiName(), objectData.getMessageId());
                    boolean redisLock = tryLockKey(lockKey);
                    //防止多次同步相同数据重复插入数据，
                    if (redisLock) {
                        GuavaLimiter.acquire(WHATSAPP_SYNC_DATA_LIMIT_KEY, syncDataArg.getEa());
                        whatsAppMessageMongoDao.insert(objectData);
                    }
                });
            }
        } catch (Exception e) {
            log.error("WhatsAppSyncDataProcessor syncData error, ea: {}, syncDataArg: {}", syncDataArg.getEa(), syncDataArg, e);
        }
    }

    @Override
    public com.facishare.marketing.common.result.Result<List<ObjectData>> queryDataByIds(QueryDataByIdsArg queryDataByIdsArg) {
        List<WhatsAppMessageMongoEntity> dbDataList = whatsAppMessageMongoDao.getByMessageIds(queryDataByIdsArg.getEa(), queryDataByIdsArg.getObjectDataIds());
        List<ObjectData> resultDataList = Lists.newArrayList();
        dbDataList.forEach(item -> {
            ObjectData objectData = getObjectData(item);
            resultDataList.add(objectData);
        });
        return com.facishare.marketing.common.result.Result.newSuccess(resultDataList);
    }

    private List<WhatsAppMessageMongoEntity> convert2MongoDataList(List<ObjectData> sourceDataList) {
        List<WhatsAppMessageMongoEntity> resultList = Lists.newArrayList();
        sourceDataList.forEach(sourceData -> {
            WhatsAppMessageMongoEntity mongoData = new WhatsAppMessageMongoEntity();
            mongoData.setContent(sourceData.getString("body"));
            mongoData.setDeprecatedMms3Url(sourceData.getString("deprecatedMms3Url"));
            mongoData.setRowId(sourceData.getString("rowId"));
            mongoData.setChatId(sourceData.getString("chatId"));
            mongoData.setInternalId(sourceData.getString("internalId"));
            mongoData.setMessageType(sourceData.getString("type"));
            mongoData.setRecordType(WHATSAPP_RECORD_TYPE);
            mongoData.setObjectDescribeApiName(CrmObjectApiNameEnum.WHATSAPP_CHAT_MESSAGE_OBJ.getName());
            mongoData.set_id(UUIDUtil.getUUID());
            mongoData.setOriginMessageId(sourceData.getId());
            mongoData.setMessageTime(sourceData.getLong("t"));
            String messageDateTimeStr = com.facishare.marketing.common.util.DateUtil.convertTimeToString(mongoData.getMessageTime() == null ? 0L : mongoData.getMessageTime() * 1000);
            mongoData.setMessageTimeStr(messageDateTimeStr);
            String id = sourceData.getId();
            if (StringUtils.isNotBlank(id)) {
                id = id.substring(id.indexOf("_") + 1);
                if(StringUtils.isBlank(mongoData.getChatId())) {
                    String chatId = id.substring(0, id.indexOf("_"));
                    mongoData.setChatId(chatId);
                }
            }
            mongoData.setMessageId(id);
            mongoData.setCreateTime(new Date());
            mongoData.setUpdateTime(new Date());
            mongoData.setCreateTimeStr(DateUtil.formatDate(new Date(), "yyyy-MM-dd"));
            mongoData.setFrom(sourceData.getString("from"));
            String toStr = sourceData.getString("to");
//            WhatsAppMessageToEntity toEntity = JSON.parseObject(toStr, WhatsAppMessageToEntity.class);
            mongoData.setTo(toStr);
            mongoData.setAck(sourceData.getLong("ack"));
            mongoData.setInvis(sourceData.getBoolean("invis"));
            mongoData.setIsMediaMsg(sourceData.getBoolean("isMediaMsg"));
            mongoData.setMimetype(sourceData.getString("mimetype"));
            mongoData.setDuration(sourceData.getString("duration"));
            mongoData.setFilehash(sourceData.getString("filehash"));
            mongoData.setSize(sourceData.getLong("size"));
            mongoData.setMediaKey(sourceData.getString("mediaKey"));
            mongoData.setMediaKeyTimestamp(sourceData.getLong("mediaKeyTimestamp"));
            mongoData.setIsViewOnce(sourceData.getBoolean("isViewOnce"));
            mongoData.setBroadcast(sourceData.getBoolean("broadcast"));
            mongoData.setWidth(sourceData.getLong("width"));
            mongoData.setHeight(sourceData.getLong("height"));
            mongoData.setMessageRangeIndex(sourceData.getString("messageRangeIndex"));
            mongoData.setEncFilehash(sourceData.getString("encFilehash"));
            mongoData.setViewMode(sourceData.getString("viewMode"));
            mongoData.setHasReaction(sourceData.getBoolean("hasReaction"));
            mongoData.setEventInvalidated(sourceData.getBoolean("eventInvalidated"));
            mongoData.setIsFromTemplate(sourceData.getBoolean("isFromTemplate"));
            mongoData.setPollInvalidated(sourceData.getBoolean("pollInvalidated"));
            mongoData.setStaticUrl(sourceData.getString("staticUrl"));
            resultList.add(mongoData);
        });
        return resultList;
    }

    private Long parseDateTimeStamp(String dateTimeStr) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("aK:mm, yyyy年MM月dd日", Locale.CHINA);
            // 将字符串转换为Date对象
            Date date = dateFormat.parse(dateTimeStr);
            return date.getTime();
        } catch (Exception e) {
            log.error("WhatsAppChatMessageSyncDataProcessor parseDateTimeStamp error:{}, dataTimeStr: {}", e.getMessage(), dateTimeStr);
        }
        return null;
    }

    private Long parseDateTimeStamp(String dateTimeStr, String timeZone) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("ahh:mm, yyyy年MM月dd日");
            sdf.setTimeZone(TimeZone.getTimeZone(timeZone)); // 设置为北京时间（UTC+8）
            // 将字符串转换为Date对象
            Date date = sdf.parse(dateTimeStr);
            return date.getTime();
        } catch (Exception e) {
            log.error("WhatsAppChatMessageSyncDataProcessor parseDateTimeStamp error:{}, dataTimeStr: {}, timeZone: {}", e.getMessage(), dateTimeStr, timeZone);
        }
        return null;
    }

    private ObjectData getObjectData(WhatsAppMessageMongoEntity whatsAppMessageMongoEntity) {
        ObjectData objectData = new ObjectData();
        return objectData;
    }
}
