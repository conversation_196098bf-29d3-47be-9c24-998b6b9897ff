package com.facishare.marketing.provider.manager.miniappLogin;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.internal.util.codec.Base64;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.qywx.miniapp.*;
import com.facishare.marketing.api.result.qywx.miniapp.*;
import com.facishare.marketing.common.contstant.MKThirdPlatformConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.dingding.MiniAppTypeEnum;
import com.facishare.marketing.common.enums.user.UserTypeEnum;
import com.facishare.marketing.common.model.RedisKISApplyInfoEntity;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.MD5Util;
import com.facishare.marketing.common.util.TokenUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.dingding.DingAddressBookDAO;
import com.facishare.marketing.provider.dao.kis.SpreadTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QywxCorpAgentConfigDAO;
import com.facishare.marketing.provider.entity.DingAddressBookEntity;
import com.facishare.marketing.provider.entity.FSBindEntity;
import com.facishare.marketing.provider.entity.UserEntity;
import com.facishare.marketing.provider.entity.kis.SpreadTaskEntity;
import com.facishare.marketing.provider.entity.qywx.QywxVirtualFsUserEntity;
import com.facishare.marketing.provider.innerArg.ding.GetDingUserDetailInfoArg;
import com.facishare.marketing.provider.innerArg.ding.GetDingUserInfoArg;
import com.facishare.marketing.provider.innerResult.ding.GetDingUserDetailInfoResult;
import com.facishare.marketing.provider.innerResult.ding.GetDingUserInfoResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatThirdPlatformManager;
import com.facishare.open.ding.api.result.DingOutUserResult;
import com.facishare.open.ding.api.result.DingPersonResult;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

@Component("dingMiniappLoginManager")
@Slf4j
public class DingMiniappLoginManager implements MiniappLoginManager{
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private AccountManager accountManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private DingAuthService dingAuthService;
    @Autowired
    private QywxVirtualFsUserManager qywxVirtualFsUserManager;
    @Autowired
    private DingAddressBookDAO dingAddressBookDAO;
    @Autowired
    private QywxCorpAgentConfigDAO qywxCorpAgentConfigDAO;
    @Autowired
    private WechatThirdPlatformManager wechatThirdPlatformManager;
    @Autowired
    private SpreadTaskDAO spreadTaskDAO;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private QywxCorpAgentConfigDAO agentConfigDAO;

    @Autowired
    private UserRelationManager userRelationManager;

    @ReloadableProperty("qywx.default.avatar")
    private String qywxDefaultAvatar;
    @ReloadableProperty("dingding.miniapp.suiteId")
    private String SUITE_ID;
    @ReloadableProperty("dingding.miniapp.appId")
    private String APP_ID;
    public static final String MARKETING_DING_WX_LOGIN_LOCK_KEY = "MARKETING_DING_WX_LOGIN_LOCK_KEY";

    @Override
    public Result<PLoginResult> pLogin(PLoginArg arg) {
        if (!Objects.equals(MiniAppTypeEnum.DINGDING.getType(), arg.getMiniappType())) {
            log.info("DingMiniappLoginmanager pLogin arg miniappType error miniappType:{}", arg.getMiniappType());
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PLoginResult result = new PLoginResult();
        result.setIsBindWechatMiniApp(false);
        result.setIsMeetUserNumLimit(false);
        result.setDingVersionIsExpire(false);

        // 免登流程,获取dingUserId fsEa fsUserId
        com.facishare.open.ding.common.result.Result<DingOutUserResult> dingOutUserResult = dingAuthService.queryUserByCode(arg.getCode(), APP_ID, arg.getCorpId());
        if (!dingOutUserResult.isSuccess()) {
            log.warn("dingAuthService queryUserByCode fail,corpId:{}", arg.getCorpId());
            return Result.newError(SHErrorCode.LOGIN_GET_USER_INFO_DETAIL_FAILED);
        }
        DingOutUserResult dingUserData = dingOutUserResult.getData();
        String userId = dingUserData.getDingEmployeeId();
        // 通过corpId+dingUserId换到fsEa+fsUserId
        Integer fsEi = dingUserData.getEi();
        String fsEa = eieaConverter.enterpriseIdToAccount(fsEi);
        Integer fsUserId = null;
        String applyInfoKey = null;
        // 购买的版本是否到期
        boolean isExpire = dingVersionIsExpire(fsEa);
        if (isExpire) {
            log.info("dingVersionIsExpire is true , ea:{}, corpId:{}", fsEa, arg.getCorpId());
            result.setDingVersionIsExpire(true);
            return Result.newSuccess(result);
        }

        // 用户信息记录or更新
        String uid = null;
        UserEntity userEntity = userManager.queryByCorpIdAndDingUserIdAndAppid(arg.getCorpId(), userId);
        if (userEntity == null) {
            String key = "marketing_dingMiniappLogin_" + arg.getCorpId() + "_" + APP_ID;
            try {
                int retry = 0;
                while(retry++ < 10) {
                    boolean redisLock = redisManager.lock(key, 30);
                    if (!redisLock) {
                        Thread.sleep(100);
                    } else {
                        // 登录人数限制
                        boolean res = isMeetLimit(arg.getCorpId(), fsEa);
                        if (res) {
                            result.setIsMeetUserNumLimit(true);
                            return Result.newSuccess(result);
                        }
                        // TODO 查询是否开通过微信身份
                        userEntity = new UserEntity();
                        uid = UUIDUtil.getUUID();
                        userEntity.setUid(uid);
                        userEntity.setCorpid(arg.getCorpId());
                        userEntity.setName(dingUserData.getEmployeeName());
                        userEntity.setDingUserId(userId);
                        userManager.insert(userEntity);
                        break;
                    }
                }
            } catch (Exception e) {
                log.warn("DingMiniappLoginManager.pLogin error e:{}", e);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            } finally {
                redisManager.unLock(key);
            }
        } else {
            uid = userEntity.getUid();
            userManager.updateUserName(dingUserData.getEmployeeName(), userEntity.getUid());
            if (!StringUtils.isEmpty(userEntity.getOpenid())) {
                result.setIsBindWechatMiniApp(true);
            }
        }

        // 购买了纷享的用户上游会传fsUserId，为null的话生成虚拟的fsUserId
        if (dingUserData.getFsEmployeeId() != null) {
            fsUserId = dingUserData.getFsEmployeeId();
            updateVirtualFsUser(fsUserId, userId, fsEa, arg.getCorpId());
        } else {
            fsUserId = qywxUserManager.getUserIdByDingInfo(fsEa, arg.getCorpId(), userId, QywxUserConstants.TRY_TIME);
        }

        // 判断是否需要更新用户信息
        Boolean needUpdateUserInfo = accountManager.needUpdateUserInfo(uid);
        result.setNeedUpdateUserInfo(needUpdateUserInfo);
        if (StringUtils.isNotBlank(fsEa) && null != fsUserId) {
            // 插入绑定表fs_bind
            FSBindEntity queryFSBindEntity = fsBindManager.queryFSBindByFsInfo(fsEa, fsUserId, fsEi, AccountTypeEnum.DING_MINI_APP.getType());
            FSBindEntity fsBindEntity = new FSBindEntity();
            fsBindEntity.setUid(uid);
            fsBindEntity.setFsEa(fsEa);
            fsBindEntity.setFsUserId(fsUserId);
            fsBindEntity.setFsCorpId(fsEi);
            fsBindEntity.setType(AccountTypeEnum.DING_MINI_APP.getType());
            if (null == queryFSBindEntity) {
                fsBindManager.insert(fsBindEntity);
            } else {
                applyInfoKey = MD5Util.md5String(fsUserId+fsEa+queryFSBindEntity.getPhone());
                RedisKISApplyInfoEntity redisKISApplyInfoEntity = redisManager.getKISApplyInfoFromRedis(applyInfoKey);
                if (redisKISApplyInfoEntity == null) {
                    RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(fsEa, fsUserId, fsEi, queryFSBindEntity.getPhone(), arg.getCorpId(), userId);
                    redisFSApplyInfoEntity.setType(1);
                    redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
                }
            }
        }

        updateSpreadTaskOutUserId(fsEa, fsUserId, arg.getCorpId());
        String sessionKey = MD5Util.md5String(arg.getCorpId()+userId+uid+new Date());
        String token = TokenUtil.generateDingMiniappToken(arg.getCorpId(), userId, uid, sessionKey, APP_ID, arg.getMiniappType());
        if (null == token) {
            log.error("DingMiniappLoginManager.pLogin token generate failed, corpid={}, dingUserId={}, sessionKey={}, uid={}, appId={}", arg.getCorpId(), userId, arg.getCorpId()+"_"+userId, uid, APP_ID);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }
        redisManager.setSessionByDingUid(uid, sessionKey);
        result.setUid(uid);
        result.setToken(token);
        result.setApplyInfoKey(applyInfoKey);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    /**
     * 购买的钉钉营销通版本是否到期
     * @param ea                    企业ea
     * @return                      true/false
     */
    private boolean dingVersionIsExpire(String ea) {
        String currentAppVersion = appVersionManager.getCurrentAppVersion(ea);
        if (StringUtils.isEmpty(currentAppVersion)) {
            return true;
        }
        return false;
    }

    /**
     * 是否达到使用人数限制
     * @param corpId                钉钉侧的企业id
     * @param ea                    企业ea
     * @return                      true/false
     */
    private boolean isMeetLimit(String corpId, String ea) {
        String currentAppVersion = appVersionManager.getCurrentAppVersion(ea);
        if (VersionEnum.STREN.getVersion().equals(currentAppVersion) || VersionEnum.PRO.getVersion().equals(currentAppVersion) || VersionEnum.STAN.getVersion().equals(currentAppVersion)){
            return false;
        }

        int userNum = userManager.queryUserNumByCorpId(corpId, APP_ID);
        int limit = getVersionLimit(currentAppVersion);
        if (userNum >= limit) {
            log.info("userNum meet limit, userNum:{}, limit:{}", userNum, limit);
            return true;
        }
        return false;
    }

    /**
     * 获取当前购买版本的人数限制
     * @param currentAppVersion     当前购买的版本
     * @return                      当前购买版本允许的使用人数
     */
    private int getVersionLimit(String currentAppVersion) {
        if (StringUtils.isEmpty(currentAppVersion)) {
            log.error("currentAppVersion is null");
            return 0;
        }

        if (currentAppVersion.equals(VersionEnum.STAN_DINGTALK_30_APP.getVersion()) || currentAppVersion.equals(VersionEnum.PRO_DINGTALK_30_APP.getVersion())) {
            return 30;
        }
        if (currentAppVersion.equals(VersionEnum.STAN_DINGTALK_100_APP.getVersion()) || currentAppVersion.equals(VersionEnum.PRO_DINGTALK_100_APP.getVersion())) {
            return 100;
        }
        if (currentAppVersion.equals(VersionEnum.STAN_DINGTALK_500_APP.getVersion()) || currentAppVersion.equals(VersionEnum.PRO_DINGTALK_500_APP.getVersion())) {
            return 500;
        }

        if (currentAppVersion.equals(VersionEnum.DING_FREE_APP.getVersion())){
            return 3;
        }
        return 0;
    }

//    public String hasWxAccount(String corpid, String qyUserId) {
//        if (StringUtils.isBlank(corpid) || StringUtils.isBlank(qyUserId)) {
//            return null;
//        }
//        // 先查询该用户手机号
//        QywxCorpAgentConfigEntity agentConfig = agentConfigDAO.queryAgentBycorpId(corpid);
//        if (agentConfig == null) {
//            return null;
//        }
//        String accessToken = qywxManager.getAccessToken(agentConfig.getCorpid(), agentConfig.getAgentid(), agentConfig.getSecret());
//        if (accessToken == null) {
//            return null;
//        }
//        StaffDetailResult staffDetailResult = qywxManager.getStaffDetail(agentConfig.getEa(), qyUserId, accessToken, false);
//        String mobile = staffDetailResult != null ? staffDetailResult.getMobile() : null;
//        if (StringUtils.isBlank(mobile)) {
//            return null;
//        }
//        Integer fsUserId = fsAddressBookManager.getFsUserIdByPhone(agentConfig.getEa(), staffDetailResult.getMobile());
//        if (fsUserId == null) {
//            return null;
//        }
//        String uid = qywxUserManager.getUidByFsUserInfo(agentConfig.getEa(), fsUserId);
//        if (StringUtils.isBlank(uid)) {
//            return null;
//        }
//        UserEntity userEntity = userDAO.queryByUid(uid);
//        if(userEntity == null) {
//            return null;
//        }
//        if (StringUtils.isBlank(userEntity.getOpenid())) {
//            return null;
//        }
//        return uid;
//    }


    /**
     * 更新消息通知任务表fsUserId
     * @param fsEa                 ea
     * @param fsUserId             fsUserId
     * @param corpId               钉钉企业id
     */
    private void updateSpreadTaskOutUserId(String fsEa, Integer fsUserId, String corpId) {
        List<SpreadTaskEntity> spreadTaskEntities = spreadTaskDAO.querySpreadTaskListByEaAndOutUserId(fsEa, corpId);
        if (CollectionUtils.isNotEmpty(spreadTaskEntities)) {
            spreadTaskDAO.batchUpdateFsUserIdById(spreadTaskEntities, fsUserId);
        }
    }


    /**
     * 更新虚拟身份表
     * @param fsUserId              fsUserId
     * @param userId                钉钉用户id
     * @param fsEa                  ea
     * @param corpId                钉钉企业id
     */
    private void updateVirtualFsUser(Integer fsUserId, String userId, String fsEa, String corpId) {
        QywxVirtualFsUserEntity virtualFsUserEntity = qywxVirtualFsUserManager.getVirtualUserByEaAndQyId(fsEa, userId);
        if (virtualFsUserEntity == null) {
            QywxVirtualFsUserEntity saveData = QywxVirtualFsUserEntity.builder()
                    .id(UUIDUtil.getUUID())
                    .ea(fsEa)
                    .userId(fsUserId)
                    .corpId(corpId)
                    .qyUserId(userId)
                    .build();
            qywxVirtualFsUserManager.insert(saveData, UserTypeEnum.DINGDING);
        } else {
            QywxVirtualFsUserEntity qywxVirtualFsUserEntity = qywxVirtualFsUserManager.queryQyUserIdByUserIdAndEa(fsEa, fsUserId);
            if (qywxVirtualFsUserEntity == null) {
                virtualFsUserEntity.setUserId(fsUserId);
                virtualFsUserEntity.setQyUserId(userId);
                virtualFsUserEntity.setCorpId(corpId);
                qywxVirtualFsUserManager.updateVirtualFsUser(virtualFsUserEntity, UserTypeEnum.DINGDING);
            }
        }
    }


    /**
     * 计算签名
     * @param timestamp     时间戳
     * @param suiteTicket   钉钉推送的suite_ticket
     * @param suiteSecret   应用凭证的suiteSecret
     * @return              签名
     */
    public String generateSignature(Long timestamp, String suiteTicket, String suiteSecret) {
        byte[] signData = null;
        try {
            String stringToSign = timestamp+"\n"+suiteTicket;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(suiteSecret.getBytes("UTF-8"), "HmacSHA256"));
            signData = mac.doFinal(stringToSign.getBytes("UTF-8"));

        } catch (Exception e) {
            log.info("MiniappLoginServiceImpl.generateSignature error timestamp:{}", timestamp);
            log.warn("exception:",  e);
        }
        return new String(Base64.encodeBase64(signData));

    }


    /**
     * 将生成的签名加密转换
     * @param value     上面生成的签名
     * @param encoding  使用utf-8
     * @return          加密后的签名
     */
    public static String urlEncode(String value, String encoding) {
        if (value == null) {
            return "";
        }
        try {
            String encoded = URLEncoder.encode(value, encoding);
            return encoded.replace("+", "%20").replace("*", "%2A")
                    .replace("~", "%7E").replace("/", "%2F");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException("FailedToEncodeUri", e);
        }
    }


    @Override
    public Result<ELoginResult> eLogin(ELoginArg arg) {return Result.newSuccess();}

    @Override
    public Result updatePUserInfo(UpdatePUserInfoArg arg) {
        UserEntity userEntity = userManager.queryByUid(arg.getUid());
        if (null == userEntity) {
            return new Result(SHErrorCode.LOGIN_NEED_RELOGIN);
        }


        // 接口获取钉钉用户个人信息
        com.facishare.open.ding.common.result.Result<DingPersonResult> dingPersonResultResult = dingAuthService.queryUserInfoByAuth(arg.getAuthCode(), arg.getCorpId(), SUITE_ID);
        if (!dingPersonResultResult.isSuccess()) {
            log.warn("DingMiniappLoginManager.updatePUserInfo.queryUserInfoByAuth fail,corpId:{}, suiteId:{}", arg.getCorpId(), SUITE_ID);
            return Result.newError(SHErrorCode.LOGIN_GET_USER_ID_FAILED);
        }
        DingPersonResult dingPersonResult = dingPersonResultResult.getData();
        String name = dingPersonResult.getNick();
        String mobile = dingPersonResult.getMobile();
        String avatar = dingPersonResult.getAvatarUrl();
        String email = dingPersonResult.getEmail();
        String unionId = dingPersonResult.getUnionId();
        Integer gender = null;

        // 更新user表 只有这里才能获取到头像和unionId
        userEntity.setAvatar(avatar);
        userEntity.setDingUnionId(unionId);
        userEntity.setName(name);
        userManager.updateUser(userEntity);

        // 更新fs_bind表
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(userEntity.getUid());
        if (null != fsBindEntity) {
            fsBindManager.updateFsBindPhone(mobile, fsBindEntity.getUid());
        }

        // 更新通讯录
        DingAddressBookEntity addressBookEntity = dingAddressBookDAO.queryUserByEaAndDingUserId(arg.getFsEa(), arg.getDingUserId());
        if (addressBookEntity != null) {
            addressBookEntity.setMobile(mobile);
            addressBookEntity.setAvatar(avatar);
            addressBookEntity.setUnionid(unionId);
            dingAddressBookDAO.updateUserInfo(addressBookEntity);
        } else {
            DingAddressBookEntity dingAddressBookEntity = new DingAddressBookEntity();
            dingAddressBookEntity.setId(UUIDUtil.getUUID());
            dingAddressBookEntity.setEa(arg.getFsEa());
            dingAddressBookEntity.setUserId(arg.getDingUserId());
            dingAddressBookEntity.setName(name);
            dingAddressBookEntity.setUnionid(unionId);
            dingAddressBookEntity.setAvatar(avatar);
            dingAddressBookEntity.setMobile(mobile);
            dingAddressBookDAO.addDingAddressBookEntity(dingAddressBookEntity);
            userRelationManager.bindOuterQyUserRelation(arg.getFsEa(), null, arg.getDingUserId(), UserTypeEnum.DINGDING);
        }

        // 钉钉身份存redis 调用wxLogin绑定微信身份时会去拿
        int fsEi = eieaConverter.enterpriseAccountToId(arg.getFsEa());
        RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(arg.getFsEa(), arg.getFsUserId(), fsEi, mobile, arg.getCorpId(), userEntity.getDingUserId());
        redisFSApplyInfoEntity.setType(1);
        String applyInfoKey = MD5Util.md5String(arg.getFsUserId()+arg.getFsEa()+mobile);
        redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);

        UpdateEUserInfoResult result = new UpdateEUserInfoResult();
//        boolean createAccountResult = accountManager.createAccount(arg.getUid(), name, gender, avatar, mobile, arg.getFsEa(), APP_ID, AccountTypeEnum.DING_MINI_APP.getType());
//        if (!createAccountResult) {
//            return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
//        }

        result.setName(name);
        result.setGender(gender);
        result.setMobile(mobile);
        result.setAvatar(avatar);
        result.setEmail(email);
        result.setApplyInfoKey(applyInfoKey);
        return new Result<>(SHErrorCode.SUCCESS, result);
    }

    @Override
    public Result<UpdateEUserInfoResult> updateEUserInfo(UpdateEUserInfoArg arg)  {return Result.newSuccess();}

    /**
     * 微信侧独有的 绑定微信身份
     * @param arg
     * @return
     */
    @Override
    public Result<WxLoginResult> wxLogin(WxLoginArg arg) {
        WxLoginResult result = new WxLoginResult();
        String wxRes;
        WxAppInfoEnum wxAppInfoEnum = WxAppInfoEnum.getByAppId(arg.getAppId());
        try {
            String url = null;
            if (wxAppInfoEnum != null) {
                url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + arg.getAppId() + "&secret=" + wxAppInfoEnum.getSecret() + "&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            }
            if(url == null){
                url = "https://api.weixin.qq.com/sns/component/jscode2session?appid=" + arg.getAppId() + "&component_appid="+wechatThirdPlatformManager.getComponentAppId(MKThirdPlatformConstants.PLATFORM_ID)+"&component_access_token="+wechatThirdPlatformManager.getThirdPlatformAccessToken("YXT")+"&js_code=" + arg.getCode() + "&grant_type=authorization_code";
            }
            wxRes = httpManager.executeGetHttpReturnString(url);
        } catch (Exception e) {
            log.error("DingMiniappLoginManager.wxLogin jscode2session exception:", e);
            return new Result<>(SHErrorCode.GET_OPENID_FAIL);
        }
        JSONObject object;
        if (TextUtils.isEmpty(wxRes) || ((object = JSONObject.parseObject(wxRes)) == null)) {
            log.error("DingMiniappLoginManager.wxLogin jscode2session response is null");
            return new Result<>(SHErrorCode.GET_OPENID_FAIL);
        }
        if (!object.containsKey("openid") || !object.containsKey("session_key")) {
            log.error("DingMiniappLoginManager.wxLogin jscode2session response hasn't contains openid or session_key:{}", object);
            return new Result<>(SHErrorCode.GET_OPENID_FAIL);
        }

        log.info("DingMiniappLoginManager.wxLogin info: {}", object);
        String openId = object.getString("openid");
        String sessionKey = object.getString("session_key");
        String wxUnionId = object.getString("unionid");
        String uid = null;
        if (StringUtils.isBlank(openId) || StringUtils.isBlank(sessionKey)) {
            log.warn("DingMiniappLoginManager.wxLogin openId or sessionKey is null");
            return new Result<>(SHErrorCode.GET_OPENID_FAIL);
        }

        RedisKISApplyInfoEntity redisKISApplyInfoEntity = redisManager.getKISApplyInfoFromRedis(arg.getApplyInfoKey());
        if (redisKISApplyInfoEntity == null) {
            log.warn("DingMiniappLoginManager.wxLogin redisKISApplyInfoEntity is null arg:{}", arg);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }


        StringJoiner sj = new StringJoiner("_");
        sj.add(MARKETING_DING_WX_LOGIN_LOCK_KEY);
        sj.add(arg.getAppId());
        sj.add(arg.getApplyInfoKey());
        try {
            boolean redisLock = redisManager.lock(sj.toString(), 30);
            if (!redisLock) {
                log.warn("WxLoginServiceImpl.wxLogin error redisLock arg:{}", arg);
                return new Result<>(SHErrorCode.SYSTEM_ERROR);
            }

            String oldUid = getUidByFsUserInfo(redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId());
            if (StringUtils.isEmpty(oldUid)) {
                log.warn("DingMiniappLoginManager.wxLogin getUidByFsUserInfo fail, ea:{}, fsUserId:{}", redisKISApplyInfoEntity.getFsEa(), redisKISApplyInfoEntity.getFsUserId());
                return Result.newError(SHErrorCode.NOT_LOGIN_DINGDING_MINIAPP);
            }

            UserEntity dingUserInfo = userManager.queryByUid(oldUid);
            if (dingUserInfo == null || StringUtils.isBlank(dingUserInfo.getDingUserId()) || StringUtils.isBlank(dingUserInfo.getCorpid())) {
                log.warn("DingMiniappLoginManager.wxLogin queryByUid fail, uid:{}", oldUid);
                return Result.newError(SHErrorCode.NOT_LOGIN_DINGDING_MINIAPP);
            }
            uid = dingUserInfo.getUid();
            result.setUid(uid);

            UserEntity userEntity = userManager.queryByOpenidAndAppid(openId, arg.getAppId());
            if (userEntity == null) {
                log.warn("DingMiniappLoginManager.wxLogin queryByOpenidAndAppid is null, openId:{}, appid:{}", openId, arg.getAppId());
                return Result.newError(SHErrorCode.LOGIN_NEED_RELOGIN);
            }

            // 新登陆的微信小程序用户和钉钉绑定时需要先清掉
            if (StringUtils.isEmpty(userEntity.getDingUserId()) && StringUtils.isEmpty(userEntity.getCorpid())) {
                userManager.deleteOldUserByUid(userEntity.getUid());
                userManager.updateUserOpenIdAndAppId(userEntity.getAppid(), userEntity.getOpenid(), wxUnionId, dingUserInfo.getUid());
            } else {
                // 如果之前绑定过不同企业的相同小程序（客脉pro）切换user企业身份
                if (!StringUtils.equals(userEntity.getCorpid(), redisKISApplyInfoEntity.getQywxCorpId())) {
                    UserEntity dingUserEntity = userManager.queryByCorpIdAndDingUserIdAndAppid(redisKISApplyInfoEntity.getQywxCorpId(), redisKISApplyInfoEntity.getQywxUserId());
                    if (dingUserEntity.getOpenid() == null && dingUserEntity.getAppid() == null) {
                        userManager.updateUserOpenIdAndAppId(userEntity.getAppid(), userEntity.getOpenid(), wxUnionId, dingUserInfo.getUid());
                        userManager.updateUserOpenIdAndAppId(null, null, wxUnionId, userEntity.getUid());
                        // 让切换企业之前员工所在企业的token失效
                        redisManager.delDingSession(userEntity.getUid());
                    }
                }
                if (wxAppInfoEnum == null && StringUtils.isNotBlank(wxUnionId) && !wxUnionId.equals(userEntity.getWxUnionId())) {
                    userManager.updateUserUnionId(wxUnionId, uid);
                }
            }
            // 让微信小程序的token失效 重新调pLogin生成token
            redisManager.delSessionByUid(userEntity.getUid());

            // 更新fs_bind表
            FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(uid);
            if (null != fsBindEntity && !Objects.equals(arg.getAppId(), fsBindEntity.getAppId())) {
                fsBindManager.updateFsBindAppId(arg.getAppId(), fsBindEntity.getUid());
            }
            boolean createAccountResult = accountManager.createAccount(uid, dingUserInfo.getName(), null, dingUserInfo.getAvatar(), redisKISApplyInfoEntity.getPhone(), redisKISApplyInfoEntity.getFsEa(), arg.getAppId(), AccountTypeEnum.DING_MINI_APP.getType());

            if (!createAccountResult) {
                return new Result<>(SHErrorCode.LOGIN_NEED_RELOGIN);
            }
            return Result.newSuccess(result);
        } catch (Exception e) {
            log.warn("DingMiniappLoginManager.wxLogin error e:{}", e);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        } finally {
            redisManager.unLock(sj.toString());
        }
    }

    @Override
    public Result<HLoginResult> hLogin(String code, String corpId,String suitId) {
        return null;
    }

    @Override
    public Result<IsBindWechatMiniAppResult> isBindWechatMiniApp(IsBindWechatMiniAppArg arg) {
        UserEntity userEntity = userManager.queryByCorpIdAndDingUserIdAndAppid(arg.getCorpId(), arg.getDingUserId());
        IsBindWechatMiniAppResult result = new IsBindWechatMiniAppResult();
        if (userEntity != null && !StringUtils.isEmpty(userEntity.getOpenid())) {
            result.setIsBindWechatMiniApp(true);
            return Result.newSuccess(result);
        }
        // 未绑定微信身份
        FSBindEntity fsBindEntity = fsBindManager.queryFSBindByUid(arg.getUid());
        if (fsBindEntity != null) {
            String applyInfoKey = MD5Util.md5String(fsBindEntity.getFsUserId() + fsBindEntity.getFsEa() + fsBindEntity.getPhone());
            RedisKISApplyInfoEntity redisKISApplyInfoEntity = redisManager.getKISApplyInfoFromRedis(applyInfoKey);
            if (redisKISApplyInfoEntity == null) {
                RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(fsBindEntity.getFsEa(), fsBindEntity.getFsUserId(), eieaConverter.enterpriseAccountToId(fsBindEntity.getFsEa()), fsBindEntity.getPhone(), arg.getCorpId(), arg.getDingUserId());
                redisFSApplyInfoEntity.setType(1);
                redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
            }
        }else {
            result.setIsBindWechatMiniApp(false);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<isOpeningResult> isOpening(IsOpeningArg arg) {
        arg.setMiniappType(MiniappLoginHandlerEnum.DING_MINIAPP.getType());
        if (!Objects.equals(MiniAppTypeEnum.DINGDING.getType(), arg.getMiniappType())) {
            log.info("DingMiniappLoginManager isOpening arg miniappType error miniappType:{}", arg.getMiniappType());
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        boolean isOpening = false;
        isOpeningResult result = new isOpeningResult();
        if (StringUtils.isEmpty(arg.getCorpId())) {
            log.info("DingMiniappLoginManager.isOpening corpId is null, arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        // 前端会轮询这个接口corpId换ea  换不到ea证明处于开通中
        try {
            com.facishare.open.ding.common.result.Result<String> eaResult = dingAuthService.dingCorpIDtoEA(arg.getCorpId());
            if (eaResult == null || !eaResult.isSuccess() || StringUtils.isEmpty(eaResult.getData())) {
                isOpening = true;
                result.setIsOpening(isOpening);
                return Result.newSuccess(result);
            }
            // 若是新企业前端会轮询这个接口查版本号 新企业查不到license证明处于开通中
            List<UserEntity> userEntities = userManager.queryUsersByCorpId(arg.getCorpId());
            if (CollectionUtils.isEmpty(userEntities)) {
                String currentAppVersion = appVersionManager.getCurrentAppVersion(eaResult.getData());
                if (StringUtils.isEmpty(currentAppVersion)) {
                    isOpening = true;
                    result.setIsOpening(isOpening);
                    return Result.newSuccess(result);
                }
            }
        } catch (Exception e) {
            isOpening = true;
            result.setIsOpening(isOpening);
            return Result.newSuccess(result);
        }

        result.setIsOpening(isOpening);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetMarketingVersionsResult> getMarketingVersions(GetMarketingVersionsArg arg) {
        GetMarketingVersionsResult result = new GetMarketingVersionsResult();
        com.facishare.open.ding.common.result.Result<String> eaResult = dingAuthService.dingCorpIDtoEA(arg.getCorpId());
        if (eaResult == null || !eaResult.isSuccess() || StringUtils.isEmpty(eaResult.getData())) {
            log.info("DingMiniappLoginManager.getMarketingVersions dingCorpIDtoEA fail, eaResult:{}", eaResult);
            return Result.newSuccess(result);
        }
        String ea = eaResult.getData();
        List<String> versions = appVersionManager.getAllMarketingVersions(ea);
        result.setVersions(versions);
        return Result.newSuccess(result);
    }


    /**
     * 通过ea fsUserId获取uid
     * @param fsEa              ea
     * @param fsUserId          虚拟/纷享用户id
     * @return                  uid
     */
    private String getUidByFsUserInfo(String fsEa, Integer fsUserId) {
        if (StringUtils.isBlank(fsEa) || fsUserId == null) {
            return null;
        }

        String dingUid = fsBindManager.queryDingUidByFsUserIdAndEa(fsEa, fsUserId, AccountTypeEnum.DING_MINI_APP.getType());;
        return dingUid;
    }

//    /**
//     * 获取钉钉小程序的accessToken
//     * @param appId
//     * @return
//     */
//    public Optional<String> getDingAccessToken(String appId, String authCorpId){
//        if (StringUtils.isEmpty(appId)) {
//            return Optional.empty();
//        }
//
//        String accessToken = redisManager.getDingMiniappAccessToken(appId, authCorpId);
//        if (StringUtils.isNotEmpty(accessToken)){
//            return Optional.of(accessToken);
//        }
//
//        Long timestamp = System.currentTimeMillis();
//        GetAccessTokenArg arg = new GetAccessTokenArg();
//        arg.setAuthCorpid(authCorpId);
//        String suiteTicket = redisManager.getDingSuiteTicket(SUITE_KEY);
//        String sign = generateSignature(timestamp, suiteTicket, SUITE_SECRET);
//        String signature = urlEncode(sign, "utf-8");
//        String url = "https://oapi.dingtalk.com/service/get_corp_token?signature=" + signature + "&timestamp=" + timestamp +"&accessKey=" + SUITE_KEY + "&suiteTicket=" + suiteTicket;
//        DingAccessTokenResult accessTokenResult = httpManager.executePostHttp(arg, url, new TypeToken<DingAccessTokenResult>(){});
//        if (!accessTokenResult.isSuccess()){
//            log.error("DingMiniappLoginManager.getDingAccessToken failed appId:{} result{}", appId, accessTokenResult);
//            return Optional.empty();
//        }
//
//        accessToken = accessTokenResult.getAccess_token();
//        redisManager.setDingMiniappAccessToken(appId, authCorpId, accessToken, 7200 - 100);
//        return Optional.of(accessToken);
//    }

    /**
     * 在第三方企业应用免登和企业内部应用免登场景中，开发者需要使用本接口通过access_token和免登接口中获取的code来获取用户userid。
     * @param accessToken
     * @param code
     * @return
     */
    public Optional<GetDingUserInfoResult.UserGetByCodeResponse> getDingUserInfo(String accessToken, String code){
         if (StringUtils.isEmpty(accessToken) || StringUtils.isEmpty(code)){
             return Optional.empty();
         }

         String url = "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo" + "?access_token=" +accessToken;
         GetDingUserInfoArg arg = new GetDingUserInfoArg(code);
         GetDingUserInfoResult userInfoResult = httpManager.executePostHttp(arg, url, new TypeToken<GetDingUserInfoResult>(){});
         if (!userInfoResult.isSuccess()){
             log.error("DingMiniappLoginManager.getDingUserInfo failed accessToken:{} code:{} result{}", accessToken, code, userInfoResult);
             return Optional.empty();
         }

         return Optional.of(userInfoResult.getResult());
    }

    public Optional<GetDingUserDetailInfoResult.UserGetResponse> getDingUserDetailInfo(String accessToken, String userId){
        if (StringUtils.isEmpty(accessToken) || StringUtils.isEmpty(userId)){
            return Optional.empty();
        }

        String url = "https://oapi.dingtalk.com/topapi/v2/user/get" + "?access_token=" +accessToken;
        GetDingUserDetailInfoArg arg = new GetDingUserDetailInfoArg(userId);
        GetDingUserDetailInfoResult userDetailInfoResult = httpManager.executePostHttp(arg, url, new TypeToken<GetDingUserDetailInfoResult>(){});
        if (!userDetailInfoResult.isSuccess()){
            log.error("DingMiniappLoginManager.getDingUserDetailInfo failed accessToken:{} userId:{} result{}", accessToken, userId, userDetailInfoResult);
            return Optional.empty();
        }

        return Optional.of(userDetailInfoResult.getResult());
    }
}
