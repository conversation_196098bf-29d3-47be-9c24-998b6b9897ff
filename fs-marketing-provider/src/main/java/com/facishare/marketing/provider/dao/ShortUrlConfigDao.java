package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.ShortUrlConfigEntity;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface ShortUrlConfigDao {

    @Insert("INSERT INTO short_url_config VALUES (#{shortUrl}, #{longUrl}, NOW(), NOW())")
    Integer add(@Param("shortUrl") String shortUrl, @Param("longUrl") String longUrl);

    @Insert("INSERT INTO short_url_config VALUES (#{shortUrl}, #{longUrl}, #{time}, #{time})")
    Integer addWithTime(@Param("shortUrl") String shortUrl, @Param("longUrl") String longUrl, @Param("time") Date time);

    @Select("<script>"
        + "SELECT short_url, long_url FROM short_url_config WHERE short_url IN <foreach open='(' close=')' separator=',' collection='shortUrls' item='shortUrl'>#{shortUrl}</foreach> "
        + "</script>")
    @MapKey("short_url")
    Map<String, Map<String, String>> batchGetLongUrl(@Param("shortUrls") Collection<String> shortUrls);

    @Update("UPDATE short_url_config SET long_url = #{longUrl}, update_time = NOW() WHERE short_url = #{shortUrl}")
    Integer update(@Param("shortUrl") String shortUrl, @Param("longUrl") String longUrl);

    @Select("SELECT * FROM short_url_config WHERE long_url = #{longUrl} ORDER BY update_time DESC LIMIT 1")
    ShortUrlConfigEntity getLastByLongUrl(@Param("longUrl") String longUrl);

    @Select("<script>"
        + "SELECT long_url, short_url, update_time FROM ("
        + "SELECT long_url, short_url, update_time, ROW_NUMBER() OVER(PARTITION BY long_url ORDER BY update_time DESC) AS rid FROM short_url_config "
        + "WHERE long_url IN <foreach open='(' close=')' separator=',' collection='longUrls' item='longUrl'>#{longUrl}</foreach>"
        + ") AS t1 WHERE rid = 1"
        + "</script>")
    @MapKey("long_url")
    Map<String, Map<String, Object>> batchGetLastByLongUrl(@Param("longUrls") Collection<String> longUrls);
}
