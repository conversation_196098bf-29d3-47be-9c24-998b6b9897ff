package com.facishare.marketing.provider.service.hexagon;

import com.facishare.marketing.api.arg.HexagonhomepageDetailArg;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonPreviewEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.outapi.arg.GetFilePreviewUrlByObjectArg;
import com.facishare.marketing.outapi.result.*;
import com.facishare.marketing.outapi.service.OutHexagonService;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("outHexagonService")
public class OutHexagonServiceImpl implements OutHexagonService {

    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private LiveService liveService;
    @Autowired
    private ObjectManager objectManager;

    @Override
    public Result<GetPageDetailResult> getHomepageDetailBySiteId(Integer type, String siteId) {
        if (null == type) {
            type = 1;
        }

        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(type);
        if (null == hexagonPreviewEnum) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        HexagonhomepageDetailArg detailArg = new HexagonhomepageDetailArg();
        detailArg.setType(type);
        detailArg.setSiteId(siteId);
        Result<com.facishare.marketing.api.result.hexagon.GetPageDetailResult> result = hexagonService.getHomepageDetailBySiteId(detailArg);
        if (!result.isSuccess()) {
            return new Result<>(result.getErrCode(), result.getErrMsg());
        }

        GetPageDetailResult getPageDetailResult = BeanUtil.copy(result.getData(), GetPageDetailResult.class);
        return new Result<>(SHErrorCode.SUCCESS, getPageDetailResult);
    }

    @Override
    public Result<GetPageDetailResult> getPageDetail(Integer type, String id) {
        if (null == type) {
            type = 1;
        }

        HexagonPreviewEnum hexagonPreviewEnum = HexagonPreviewEnum.getByType(type);
        if (null == hexagonPreviewEnum) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        Result<com.facishare.marketing.api.result.hexagon.GetPageDetailResult> result = hexagonService.getPageDetail(type, id);
        if (!result.isSuccess()) {
            return new Result<>(result.getErrCode(), result.getErrMsg());
        }

        GetPageDetailResult getPageDetailResult = BeanUtil.copy(result.getData(), GetPageDetailResult.class);
        return new Result<>(SHErrorCode.SUCCESS, getPageDetailResult);
    }

    @Override
    public Result<HexagonFilePreviewResult> getFilePreviewUrl(String siteId, String npath, String fileName) {
        Result<com.facishare.marketing.api.result.hexagon.HexagonFilePreviewResult> result = hexagonService.getFilePreviewUrl(siteId, npath, fileName);
        if (!result.isSuccess()) {
            return new Result<>(result.getErrCode(), result.getErrMsg());
        }

        HexagonFilePreviewResult hexagonFilePreviewResult = BeanUtil.copy(result.getData(), HexagonFilePreviewResult.class);
        return new Result<>(SHErrorCode.SUCCESS, hexagonFilePreviewResult);
    }

    @Override
    public Result<HexagonFilePreviewResult> getFilePreviewUrlByObject(GetFilePreviewUrlByObjectArg arg) {
        com.facishare.marketing.api.arg.hexagon.GetFilePreviewUrlByObjectArg getFilePreviewUrlByObjectArg = BeanUtil
            .copy(arg, com.facishare.marketing.api.arg.hexagon.GetFilePreviewUrlByObjectArg.class);
        Result<com.facishare.marketing.api.result.hexagon.HexagonFilePreviewResult> result = hexagonService.getFilePreviewUrlByObject(getFilePreviewUrlByObjectArg);
        if (!result.isSuccess()) {
            return new Result<>(result.getErrCode(), result.getErrMsg());
        }
        HexagonFilePreviewResult hexagonFilePreviewResult = BeanUtil.copy(result.getData(), HexagonFilePreviewResult.class);
        return new Result<>(SHErrorCode.SUCCESS, hexagonFilePreviewResult);
    }

    @Override
    public Result<Integer> getLiveStatus(String id) {
        return liveService.getLiveStatus(id);
    }

    @Override
    public Result<List<QueryEnterpriseCommerceInfoResult>> queryEnterpriseCommerceInfo(String objectId, Integer objectType, String keyword) {
        Result<List<com.facishare.marketing.api.result.hexagon.QueryEnterpriseCommerceInfoResult>> result = hexagonService.queryEnterpriseCommerceInfo(objectId, objectType, keyword);
        if (result == null || CollectionUtils.isEmpty(result.getData())){
            return Result.newSuccess();
        }

        List<QueryEnterpriseCommerceInfoResult> list = Lists.newArrayList();
        for (com.facishare.marketing.api.result.hexagon.QueryEnterpriseCommerceInfoResult commerceInfoResult : result.getData()){
            QueryEnterpriseCommerceInfoResult newResult = BeanUtil.copy(commerceInfoResult, QueryEnterpriseCommerceInfoResult.class);
            list.add(newResult);
        }
        return Result.newSuccess(list);
    }

    @Override
    public Result<GetContentCenterInfoResult> getContentCenterInfo(String ea, Integer fsUserId, boolean sync) {
        Result<com.facishare.marketing.api.result.hexagon.GetContentCenterInfoResult> result = hexagonService.getContentCenterInfo(ea, fsUserId, sync);
        GetContentCenterInfoResult outResult = null;
        if (result.isSuccess() && result.getData() != null){
            outResult = BeanUtil.copy(result.getData(), GetContentCenterInfoResult.class);
        }
        return Result.newSuccess(outResult);
    }

    @Override
    public Result<GetActivityCenterInfoResult> getActivityCenterInfo(String ea, Integer fsUserId, boolean sync) {
        Result<com.facishare.marketing.api.result.hexagon.GetActivityCenterInfoResult> result = hexagonService.getActivityCenterInfo(ea, fsUserId, sync);
        GetActivityCenterInfoResult outResult = null;
        if (result.isSuccess() && result.getData() != null){
            outResult = BeanUtil.copy(result.getData(), GetActivityCenterInfoResult.class);
        }
        return Result.newSuccess(outResult);
    }

    @Override
    public Result<String> getEaByHexagonPageId(String id) {
        if (StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        String ea = objectManager.getObjectEa(id, ObjectTypeEnum.HEXAGON_SITE.getType());
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(ea);
    }

}
