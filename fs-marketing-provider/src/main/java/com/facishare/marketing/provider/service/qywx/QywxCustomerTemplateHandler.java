package com.facishare.marketing.provider.service.qywx;


import com.facishare.marketing.api.service.qywx.AuthService;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.qywx.QywxCustomerTemplateInfoDAO;
import com.facishare.marketing.provider.entity.qywx.QywxCustomerTemplateInfoEntity;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * Created by zhengh on 2020/1/2.
 */
@Slf4j
@Service
public class QywxCustomerTemplateHandler implements AuthService {

    @ReloadableProperty("qywx.customer.suitid")
    private String qywxCustomerSuitId;
    @ReloadableProperty("qywx.customer.secret")
    private String qywxCustomerSecret;

    @Autowired
    private QywxCustomerTemplateInfoDAO qywxCustomerTemplateInfoDAO;

    @PostConstruct
    public void init() {
        QywxCustomerTemplateInfoEntity qywxCustomerTemplateInfoEntity = qywxCustomerTemplateInfoDAO.selectOne(qywxCustomerSuitId);
        if (null == qywxCustomerTemplateInfoEntity) {
            QywxCustomerTemplateInfoEntity entity = new QywxCustomerTemplateInfoEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setSuitId(qywxCustomerSuitId);
            entity.setSecret(qywxCustomerSecret);
            qywxCustomerTemplateInfoDAO.insert(entity);
        }

    }
}
