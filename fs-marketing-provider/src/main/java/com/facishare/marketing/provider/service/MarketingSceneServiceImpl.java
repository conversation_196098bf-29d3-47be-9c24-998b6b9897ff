package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.qywx.QywxAttachmentScenesTypeEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.CreateTriggerInSceneResult;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.service.MarketingSceneService;
import com.facishare.marketing.api.service.MarketingTriggerService;
import com.facishare.marketing.api.vo.MarketingSceneVO;
import com.facishare.marketing.api.vo.MarketingTriggerVO;
import com.facishare.marketing.api.vo.SceneTriggerVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.api.vo.qywx.ListSceneTriggerVO;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.conference.ConferenceEnrollReviewStatusEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qywx.QywxGroupSendRangeTypeEnum;
import com.facishare.marketing.common.enums.taskCenter.TaskTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.param.qywx.QywxSceneTriggerQueryParam;
import com.facishare.marketing.provider.dto.TriggerUserVO;
import com.facishare.marketing.provider.dto.qywx.QywxSceneTriggerDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxWorkExternalUserRelationEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * Created on 2021-02-26.
 */
@Slf4j
@Service("marketingSceneService")
public class MarketingSceneServiceImpl implements MarketingSceneService {
	@Autowired
	private MarketingSceneDao marketingSceneDao;
	@Autowired
	private MarketingTriggerDao marketingTriggerDao;
	@Autowired
	private SceneTriggerDao sceneTriggerDao;
	@Autowired
	private MarketingTriggerManager marketingTriggerManager;
	@Autowired
	private MarketingTriggerService marketingTriggerService;
	@Autowired
	private SceneTriggerManager sceneTriggerManager;
	@Autowired
	private SceneTriggerTimedTaskDao sceneTriggerTimedTaskDao;
	@Autowired
	private TriggerSnapshotDao triggerSnapshotDao;
	@Autowired
	private ConferenceDAO conferenceDAO;
	@Autowired
	private MarketingLiveDAO marketingLiveDAO;
	@Autowired
	private CampaignMergeDataDAO campaignMergeDataDAO;
	@Autowired
	private CampaignMergeDataManager campaignMergeDataManager;
	@Autowired
	private TriggerInstanceManager triggerInstanceManager;
	@Autowired
	private TriggerTaskInstanceManager triggerTaskInstanceManager;
	@Autowired
	private TriggerTaskSnapshotDao triggerTaskSnapshotDao;
	@Autowired
	private MarketingActivityRemoteManager marketingActivityRemoteManager;
	@Autowired
	private AppVersionManager appVersionManager;
	@Autowired
	private CrmV2Manager crmV2Manager;
	@Autowired
	private MarketingUserGroupManager marketingUserGroupManager;
	@Autowired
	private UserMarketingAccountManager userMarketingAccountManager;
	@Autowired
	private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
	@Autowired
	private DataPermissionManager dataPermissionManager;

	@Autowired
	private ObjectGroupManager objectGroupManager;

	@Autowired
	private ObjectGroupDAO objectGroupDAO;

	@Autowired
	private ObjectGroupRelationDAO objectGroupRelationDAO;

	@Autowired
	private ObjectTopManager objectTopManager;

	@Autowired
	private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

	@Autowired
	private GroupSendMessageManager groupSendMessageManager;

	@Autowired
	private QywxAttachmentsRelationDAO qywxAttachmentsRelationDAO;

	Gson gson = GsonUtil.getGson();

	private Gson adapterGson = new GsonBuilder().setNumberToNumberStrategy(ToNumberPolicy.BIG_DECIMAL).setObjectToNumberStrategy(ToNumberPolicy.BIG_DECIMAL).create();

	@ReloadableProperty("sync_trigger_to_crm_list")
	private String syncTriggerToCrmList;

	@Override
	public Result<MarketingSceneVO> getOrCreateMarketingScene(String ea, Integer fsUserId, GetOrCreateMarketingSceneArg arg) {
		Preconditions.checkArgument(MarketingSceneType.isValid(arg.getSceneType()));
		Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getSceneTargetId()));
		MarketingSceneVO result = new MarketingSceneVO();
		MarketingSceneEntity marketingSceneInDB = marketingSceneDao.getMarketingSceneByTargetId(ea, arg.getSceneType(), arg.getSceneTargetId());
		//如果数据库中已经存在，则直接返回
		if (marketingSceneInDB != null){
			BeanUtils.copyProperties(marketingSceneInDB, result);
			return Result.newSuccess(result);
		}

		MarketingSceneEntity toAddScene = new MarketingSceneEntity();
		toAddScene.setId(UUIDUtil.getUUID());
		toAddScene.setEa(ea);
		toAddScene.setSceneType(arg.getSceneType());
		toAddScene.setTargetId(arg.getSceneTargetId());
		//创建场景
		marketingSceneDao.insertScene(toAddScene);
		//查询场景信息
		marketingSceneInDB = marketingSceneDao.getMarketingSceneByTargetId(ea, arg.getSceneType(), arg.getSceneTargetId());

		// 若有该场景的模板触发器，从模板创建专属触发器
		List<String> templateIds = marketingTriggerDao.listIdBySceneAndType(ea, arg.getSceneType(), 1);
		for (String templateId : templateIds) {
			String triggerId = UUIDUtil.getUUID();
			marketingTriggerDao.copy(templateId, triggerId, TriggerUsageRangeEnum.PRIVATE.getUsageRange());
			String templateTriggerSnapshotId = triggerSnapshotDao.getUsefulIdByTriggerId(templateId, ea);
			String triggerSnapshotId = UUIDUtil.getUUID();
			triggerSnapshotDao.copy(templateTriggerSnapshotId, triggerSnapshotId, triggerId);
			List<TriggerTaskSnapshotEntity> triggerTaskSnapshotEntities = triggerTaskSnapshotDao.listByTriggerSnapshotId(ea, templateTriggerSnapshotId);
			for (TriggerTaskSnapshotEntity triggerTaskSnapshotEntity : triggerTaskSnapshotEntities) {
				String triggerTaskSnapshotId = UUIDUtil.getUUID();
				triggerTaskSnapshotDao.copy(triggerTaskSnapshotEntity.getId(), triggerTaskSnapshotId, triggerId, triggerSnapshotId);

				//企微支持多附件
				if (triggerTaskSnapshotEntity.getTaskType().equals(TriggerTaskTypeEnum.SEND_WORK_WX_MSG.getTriggerTaskType())) {
					QywxAttachmentsRelationEntity newQywxAttachmentsRelationEntity = qywxAttachmentsRelationDAO.getDetailByTargetId(triggerTaskSnapshotEntity.getId(), QywxAttachmentScenesTypeEnum.QYWX_SOP.getType());
					newQywxAttachmentsRelationEntity.setId(UUIDUtil.getUUID());
					newQywxAttachmentsRelationEntity.setTargetId(triggerTaskSnapshotId);
					qywxAttachmentsRelationDAO.insert(newQywxAttachmentsRelationEntity);
				}
			}
			String sceneTriggerId = doCreateSceneTrigger(ea, marketingSceneInDB, triggerId,fsUserId);
			if (sceneTriggerId != null){
				sceneTriggerManager.resetSceneTriggerTimedTask(ea, sceneTriggerId);
			}
		}

		BeanUtils.copyProperties(marketingSceneInDB, result);
		return Result.newSuccess(result);
	}

	@Override
	public Result<String> addTriggerToScene(String ea, Integer fsUserId, AddTriggerToSceneArg arg) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getSceneId()));
		Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTriggerId()));
		//根据场景id得到场景信息
		MarketingSceneEntity marketingSceneInDB = marketingSceneDao.getMarketingSceneById(ea, arg.getSceneId());
		Preconditions.checkArgument(marketingSceneInDB != null);
		//根据触发器id得到触发器定义信息
		MarketingTriggerEntity marketingTriggerInDB = marketingTriggerDao.getById(arg.getTriggerId(), ea);
		Preconditions.checkArgument(marketingTriggerInDB != null);
		Preconditions.checkArgument(BooleanUtils.isNotTrue(marketingTriggerInDB.getDeleted()));
		Preconditions.checkArgument(marketingSceneInDB.getSceneType().equals(marketingTriggerInDB.getTriggerScene()));
		//根据场景id和触发器id查询是否已经存在scene_trigger表的数据
		SceneTriggerEntity sceneTriggerInDB = sceneTriggerDao.getBySceneIdAndTriggerId(ea, arg.getSceneId(), arg.getTriggerId());
		if (sceneTriggerInDB != null){
			//如果该场景下的触发器已经存在,但是是删除状态
			if (SceneTriggerLifeStatus.DELETED.getLifeStatus().equals(sceneTriggerInDB.getLifeStatus())){
				sceneTriggerManager.resetSceneTriggerTimedTask(ea, sceneTriggerInDB.getId());
				//更新scene_trigger表的状态为可用状态
				sceneTriggerDao.updateSceneTriggerLifeStatus(sceneTriggerInDB.getId(), SceneTriggerLifeStatus.ENABLED.getLifeStatus());
			}
			//返回scene_trigger_id
			return Result.newSuccess(sceneTriggerInDB.getId());
		}

		//入库scene_trigger表
		String sceneTriggerId = doCreateSceneTrigger(ea, marketingSceneInDB, arg.getTriggerId(),fsUserId);
		if (sceneTriggerId != null){
			//创建定时任务
			sceneTriggerManager.resetSceneTriggerTimedTask(ea, sceneTriggerId);
			return Result.newSuccess(sceneTriggerId);
		}
		sceneTriggerInDB = sceneTriggerDao.getBySceneIdAndTriggerId(ea, arg.getSceneId(), arg.getTriggerId());
		return Result.newSuccess(sceneTriggerInDB.getId());
	}

	@Override
	public Result<PageResult<SceneTriggerVO>> listSceneTriggerBySceneId(ListSceneTriggerVO vo) {
		//List<SceneTriggerEntity> sceneTriggers = sceneTriggerDao.listBySceneId(ea, sceneId);
		PageResult<SceneTriggerVO> pageResult = new PageResult<>();
		List<SceneTriggerVO> sceneTriggerVOS = Lists.newArrayList();
		pageResult.setPageNum(vo.getPageNum());
		pageResult.setPageSize(vo.getPageSize());
		pageResult.setTotalCount(0);
		if (StringUtils.isBlank(vo.getGroupId())) {
			vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
		}
		QywxSceneTriggerQueryParam queryParam = new QywxSceneTriggerQueryParam();
		queryParam.setEa(vo.getEa());
		queryParam.setSceneId(vo.getId());
		queryParam.setType(vo.getType());
		com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(),vo.getPageSize(),true);
		List<QywxSceneTriggerDTO> sceneTriggers;
		if (StringUtils.isNotBlank(vo.getGroupId())) {
			int type = ObjectTypeEnum.QY_SOP.getType();
			if (null != vo.getType() && 4 == vo.getType()) {
				type = ObjectTypeEnum.QY_GROUP_SOP.getType();
			}
			if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
				List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(vo.getEa(), vo.getUserId(), type);
				List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
				queryParam.setPermissionGroupIdList(permissionGroupIdList);
				queryParam.setUserId(vo.getUserId());
				sceneTriggers = sceneTriggerDao.getAccessiblePage(queryParam, page);
			} else if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
				queryParam.setUserId(vo.getUserId());
				sceneTriggers = sceneTriggerDao.getCreateByMePage(queryParam, page);
			} else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
				sceneTriggers = sceneTriggerDao.noGroupPage(queryParam, page);
			} else {
				List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(vo.getEa(), vo.getUserId(), type);
				Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
				if (!permissionGroupIdSet.contains(vo.getGroupId())) {
					sceneTriggers = Lists.newArrayList();
				} else {
					queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
					queryParam.setUserId(vo.getUserId());
					List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(vo.getEa(), type, vo.getGroupId(), objectGroupEntityList);
					accessibleSubGroupIdList.add(vo.getGroupId());
					queryParam.setGroupIdList(accessibleSubGroupIdList);
					sceneTriggers = sceneTriggerDao.getAccessiblePage(queryParam, page);
				}
			}
		} else {
			sceneTriggers = sceneTriggerDao.getAccessiblePage(queryParam, page);
		}
		if (CollectionUtils.isEmpty(sceneTriggers)) {
			pageResult.setResult(sceneTriggerVOS);
			return Result.newSuccess(pageResult);
		}
		sceneTriggerVOS = sceneTriggers.stream().map(entity -> {
			SceneTriggerVO sceneTriggerVO = new SceneTriggerVO();
			BeanUtils.copyProperties(entity, sceneTriggerVO);
			return sceneTriggerVO;
		}).collect(Collectors.toList());
		Set<String> triggerIds = sceneTriggerVOS.stream().map(SceneTriggerVO::getTriggerId).collect(Collectors.toSet());
		Map<String, MarketingTriggerVO> triggerIdToVOMap = marketingTriggerManager.getTriggerByIds(vo.getEa(), triggerIds);
		sceneTriggerVOS.forEach(sceneTriggerVO -> {
			sceneTriggerVO.setTriggerVO(triggerIdToVOMap.get(sceneTriggerVO.getTriggerId()));
		});
		pageResult.setResult(sceneTriggerVOS);
		pageResult.setTotalCount(page.getTotalNum());
		return Result.newSuccess(pageResult);
	}

	private String doCreateSceneTrigger(String ea, MarketingSceneEntity marketingSceneInDB, String triggerId,Integer fsUserId) {
		SceneTriggerEntity sceneTriggerToAdd = new SceneTriggerEntity();
		sceneTriggerToAdd.setId(UUIDUtil.getUUID());
		sceneTriggerToAdd.setEa(ea);
		sceneTriggerToAdd.setSceneId(marketingSceneInDB.getId());
		sceneTriggerToAdd.setSceneType(marketingSceneInDB.getSceneType());
		sceneTriggerToAdd.setSceneTargetId(marketingSceneInDB.getTargetId());
		sceneTriggerToAdd.setTriggerId(triggerId);
		sceneTriggerToAdd.setEffectiveTimeType(EffectiveTimeType.FOREVER.getType());
		sceneTriggerToAdd.setCreateBy(fsUserId);
		boolean insertSuccess = sceneTriggerDao.insertSceneTriggerIgnore(sceneTriggerToAdd) == 1;
		if (insertSuccess){
			return sceneTriggerToAdd.getId();
		}
		return null;
	}

	@Override
	public Result<CreateTriggerInSceneResult> createTriggerInScene(String ea, Integer fsUserId, CreateTriggerInSceneArg arg) {
		CreateTriggerInSceneResult result = new CreateTriggerInSceneResult();
		Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getSceneId()));
		String verifyErrorMsg = arg.getTriggerVO().verifyErrorMsg();
		Preconditions.checkArgument(Strings.isNullOrEmpty(verifyErrorMsg), verifyErrorMsg);

		//获取场景信息
		MarketingSceneEntity marketingSceneInDB = marketingSceneDao.getMarketingSceneById(ea, arg.getSceneId());
		Preconditions.checkArgument(marketingSceneInDB != null);
		Preconditions.checkArgument(arg.getTriggerVO() != null);

		// 定时类型客户群发SOP不支持分支判断
		if ((TriggerTypeEnum.TRIGGER_BY_OUT.getTriggerType().equals(arg.getTriggerVO().getTriggerType()) || TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(arg.getTriggerVO().getTriggerType()) || TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(arg.getTriggerVO().getTriggerType()))) {
			boolean hasSensWorkWXMsg = arg.getTriggerVO().getTriggerTasks().stream().anyMatch(e -> TriggerTaskTypeEnum.SEND_WORK_WX_MSG.getTriggerTaskType().equals(e.getTaskType()));
			boolean hasBranchJudgment = arg.getTriggerVO().getTriggerTasks().stream().anyMatch(e -> TriggerTaskTypeEnum.BRANCH_JUDGMENT.getTriggerTaskType().equals(e.getTaskType()));
			if (hasSensWorkWXMsg && hasBranchJudgment) {
				return Result.newError(SHErrorCode.NO_AUTH);
			}
		}

		//创建触发器定义，触发器快照，触发器任务快照
		Result<String> createTriggerResult = marketingTriggerService.createTrigger(ea, fsUserId, arg.getTriggerVO());
		if (!createTriggerResult.isSuccess()){
			return Result.newError(createTriggerResult.getErrCode(), createTriggerResult.getErrMsg());
		}
		result.setTriggerId(createTriggerResult.getData());

		//入库scene_trigger表
		String sceneTriggerId = doCreateSceneTrigger(ea, marketingSceneInDB, createTriggerResult.getData(),fsUserId);
		if (sceneTriggerId != null){
			//处理分组
			if (4 == arg.getType()) {
				objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.QY_GROUP_SOP.getType(), Collections.singletonList(sceneTriggerId), arg.getGroupId());
			} else {
				objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.QY_SOP.getType(), Collections.singletonList(sceneTriggerId), arg.getGroupId());
			}
			//创建定时任务
			sceneTriggerManager.resetSceneTriggerTimedTask(ea, sceneTriggerId);
			result.setSceneTriggerId(sceneTriggerId);

			// 同步到自定义对象
			ThreadPoolUtils.execute(() -> {
				if (syncTriggerToCrmList != null && Arrays.asList(syncTriggerToCrmList.split(",")).contains(ea)) {
					// 企微场景 && 外部触发
					if (TriggerSceneEnum.QYWX.getTriggerScene().equals(arg.getTriggerVO().getTriggerScene()) && TriggerTypeEnum.TRIGGER_BY_OUT.getTriggerType().equals(arg.getTriggerVO().getTriggerType())) {
						boolean isExist = crmV2Manager.isExistObject(ea, "object_sop_comparison_table__c");
						if (isExist) {
							HashMap<String, Object> dataMap = new HashMap<>();
							dataMap.put("field_sop_id__c", sceneTriggerId);
							dataMap.put("field_sop_name__c", arg.getTriggerVO().getName());
							dataMap.put("create_time", new Date().getTime());
							crmV2Manager.addObjectData(ea, "object_sop_comparison_table__c", fsUserId, dataMap);
							log.warn("同步到object_sop_comparison_table__c成功, data:{}", dataMap);
						}
					}
				}
			}, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);

			return Result.newSuccess(result);
		}
		//查询scene_trigger表
		SceneTriggerEntity sceneTriggerInDB = sceneTriggerDao.getBySceneIdAndTriggerId(ea, arg.getSceneId(), createTriggerResult.getData());
		result.setSceneTriggerId(sceneTriggerInDB.getId());
		return Result.newSuccess(result);
	}

	@Override
	public Result<Void> updateSceneTriggerLifeStatus(String ea, Integer fsUserId, UpdateSceneTriggerStatusArg arg) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getSceneTriggerId()));
		Preconditions.checkArgument(SceneTriggerLifeStatus.isValid(arg.getSceneTriggerLifeStatus()));
		sceneTriggerDao.updateSceneTriggerLifeStatus(arg.getSceneTriggerId(), arg.getSceneTriggerLifeStatus());

		if (SceneTriggerLifeStatus.DELETED.getLifeStatus().equals(arg.getSceneTriggerLifeStatus())) {
			//处理分组数据
			objectGroupRelationDAO.deleteByObjectId(ea, arg.getSceneTriggerId());
			//删除置顶数据
			objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QY_SOP.getType(), Lists.newArrayList(arg.getSceneTriggerId()));
		}
		return Result.newSuccess();
	}

	@Override
	public Result<Void> updateSceneTriggerEffectiveTime(String ea, Integer fsUserId, UpdateSceneTriggerEffectiveTimeArg arg) {
		Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getSceneTriggerId()));
		Preconditions.checkArgument(EffectiveTimeType.isValid(arg.getEffectiveTimeType()));
		if (EffectiveTimeType.RANGE.getType().equals(arg.getEffectiveTimeType())){
			Preconditions.checkArgument(arg.getStartEffectiveTime() != null && arg.getEndEffectiveTime() != null && arg.getEndEffectiveTime() > arg.getStartEffectiveTime());
		}
		sceneTriggerDao.updateEffectiveTime(arg.getSceneTriggerId(), arg.getEffectiveTimeType(), arg.getStartEffectiveTime(), arg.getEndEffectiveTime());
		return Result.newSuccess();
	}

	@Override
	public Result<Integer> startSceneTrigger(String sceneTriggerTimedTaskId) {
		SceneTriggerTimedTaskEntity sceneTriggerTimedTask = sceneTriggerTimedTaskDao.getById(sceneTriggerTimedTaskId);
		String ea = sceneTriggerTimedTask.getEa();

		//过滤掉停用企业、营销通配额过期企业
		if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
			log.info("MarketingSceneServiceImpl.startSceneTrigger failed enterprise stop or license expire ea:{}", ea);
			return Result.newSuccess(0);
		}

		SceneTriggerEntity sceneTrigger = sceneTriggerDao.getById(ea, sceneTriggerTimedTask.getSceneTriggerId());
		if(sceneTrigger == null || !SceneTriggerLifeStatus.ENABLED.getLifeStatus().equals(sceneTrigger.getLifeStatus())){
			sceneTriggerTimedTaskDao.updateTimedTaskStatus(ea, sceneTriggerTimedTaskId, SceneTriggerTimedTaskExecuteStatus.CANCELED.getExecuteStatus());
			return Result.newSuccess(0);
		}
		if(EffectiveTimeType.RANGE.getType().equals(sceneTrigger.getEffectiveTimeType()) && (sceneTrigger.getStartEffectiveTime() > System.currentTimeMillis() || sceneTrigger.getEndEffectiveTime() < System.currentTimeMillis())){
			sceneTriggerTimedTaskDao.updateTimedTaskStatus(ea, sceneTriggerTimedTaskId, SceneTriggerTimedTaskExecuteStatus.CANCELED.getExecuteStatus());
			return Result.newSuccess(0);
		}
		TriggerSnapshotEntity triggerSnapshot = triggerSnapshotDao.getCurrentUseSnapshot(ea, sceneTrigger.getTriggerId());
		if (triggerSnapshot == null || !TriggerSnapshotStatusEnum.ENABLED.getSnapshotStatus().equals(triggerSnapshot.getSnapshotStatus())){
			sceneTriggerTimedTaskDao.updateTimedTaskStatus(ea, sceneTriggerTimedTaskId, SceneTriggerTimedTaskExecuteStatus.CANCELED.getExecuteStatus());
			return Result.newSuccess(0);
		}
		sceneTriggerTimedTask = sceneTriggerTimedTaskDao.getById(sceneTriggerTimedTaskId);
		if (SceneTriggerTimedTaskExecuteStatus.TODO.getExecuteStatus().equals(sceneTriggerTimedTask.getExecuteStatus())){
			sceneTriggerTimedTaskDao.updateTimedTaskStatus(ea, sceneTriggerTimedTaskId, SceneTriggerTimedTaskExecuteStatus.EXECUTING.getExecuteStatus());
			try {
				/**
				 * 定时类型
				 * 先获取活动成员ID:
				 * 活动营销
				 * 直播营销
				 * 会议营销
				 */
				List<String> campaignIds = new ArrayList<>(0);
				if (MarketingSceneType.CONFERENCE.getType().equals(sceneTrigger.getSceneType())){
					ActivityEntity conference = conferenceDAO.getConferenceById(sceneTrigger.getSceneTargetId());
					if (TriggerTargetUserTypeEnum.ALL_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEvent(ea, conference.getMarketingEventId());
					}
					if (TriggerTargetUserTypeEnum.PENDING_REVIEW_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndReviewStatus(ea, conference.getMarketingEventId(), ConferenceEnrollReviewStatusEnum.PENDING_REVIEW.getStatus());
					}
					if (TriggerTargetUserTypeEnum.REVIEW_SUCCESS_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndReviewStatus(ea, conference.getMarketingEventId(), ConferenceEnrollReviewStatusEnum.REVIEW_SUCCESS.getStatus());
					}
					if (TriggerTargetUserTypeEnum.REVIEW_FAILURE_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndReviewStatus(ea, conference.getMarketingEventId(), ConferenceEnrollReviewStatusEnum.REVIEW_FAILURE.getStatus());
					}
					if (TriggerTargetUserTypeEnum.CHECKED_IN_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndCheckInStatus(ea, conference.getMarketingEventId(), 2);
					}
					if (TriggerTargetUserTypeEnum.NOT_CHECKED_IN_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndCheckInStatus(ea, conference.getMarketingEventId(), 3);
					}
				}
				if(MarketingSceneType.LIVE.getType().equals(sceneTrigger.getSceneType())){
					MarketingLiveEntity live = marketingLiveDAO.getById(sceneTrigger.getSceneTargetId());
					if (TriggerTargetUserTypeEnum.ALL_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())){
						campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEvent(ea, live.getMarketingEventId());
					}

					// LivePlatformEnum
					if (live.getLiveId() != null || !Strings.isNullOrEmpty(live.getXiaoetongLiveId())) {
						if (TriggerTargetUserTypeEnum.VIEW_LIVE_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())) {
							campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndLiveViewed(ea, live.getMarketingEventId(),
									Lists.newArrayList(3, 4, 6, 7).contains(live.getPlatform()) ? live.getXiaoetongLiveId() : live.getLiveId(), live.getPlatform());
						}
						if (TriggerTargetUserTypeEnum.REPLAY_LIVE_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())) {
							campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndLiveReplayed(ea, live.getMarketingEventId(),
									Lists.newArrayList(3, 4, 6, 7).contains(live.getPlatform()) ? live.getXiaoetongLiveId() : live.getLiveId(), live.getPlatform());
						}
						if (TriggerTargetUserTypeEnum.CHAT_LIVE_CAMPAIGN.getTargetUserType().equals(triggerSnapshot.getTriggerTargetUserType())) {
							campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEventAndLiveChatted(ea, live.getMarketingEventId(),
									Lists.newArrayList(3, 4, 6, 7).contains(live.getPlatform()) ? live.getXiaoetongLiveId() : live.getLiveId(), live.getPlatform());
						}
					}
				}
				if (MarketingSceneType.MARKETING_EVENT.getType().equals(sceneTrigger.getSceneType())){
					campaignIds = campaignMergeDataDAO.getCampaignMergeDataIdByMarketingEvent(ea, sceneTrigger.getSceneTargetId());
				}

				Set<TriggerUserVO> triggerUserVOs;
				if (MarketingSceneType.QYWX.getType().equals(sceneTrigger.getSceneType())) {
					if(triggerSnapshot.getSendRange() == QywxGroupSendRangeTypeEnum.GROUP.getType()) {
						//找群主
						List<String> groupUserIds = gson.fromJson(triggerSnapshot.getGroupMsgSenderIds(), List.class);
						if (CollectionUtils.isNotEmpty(groupUserIds)) {
							triggerUserVOs = new HashSet<>(groupUserIds.size());
							groupUserIds.forEach(u -> triggerUserVOs.add(new TriggerUserVO(u, null)));
						} else {
							List<Map<String, Object>> chatGroupFilters = adapterGson.fromJson(triggerSnapshot.getChatGroupFilters(), new TypeToken<List<Map<String, Object>>>() {
							}.getType());
							Map<String, List<String>> chatGroupIdListMap = groupSendMessageManager.getChatGroupIdListByFilters(ea, chatGroupFilters,null);
							List<String> leaderIds = Lists.newArrayList(chatGroupIdListMap.keySet());
							triggerUserVOs = new HashSet<>(leaderIds.size());
							leaderIds.forEach(u -> triggerUserVOs.add(new TriggerUserVO(u, null,chatGroupIdListMap.get(u))));
						}
					} else {
						//企微消息 转换成营销用户传递
						Set<String> userIds = triggerTaskInstanceManager.getMarketingAccountUserIds(ea, triggerSnapshot, null);
						triggerUserVOs = new HashSet<>(userIds.size());
						userIds.forEach(u -> triggerUserVOs.add(new TriggerUserVO(u, null)));
					}
				} else if (MarketingSceneType.TARGET_CROWD_OPERATION.getType().equals(sceneTrigger.getSceneType())) {
					List<String> marketingUserGroupIds = gson.fromJson(triggerSnapshot.getMarketingUserGroupIds(), List.class);
					Collection<String> marketingUserIds = marketingUserGroupManager.batchGetMarketingUserIdByMarketingUserGroupIds(ea, marketingUserGroupIds);
					triggerUserVOs = new HashSet<>(marketingUserIds.size());
					marketingUserIds.forEach(marketingUserId -> triggerUserVOs.add(new TriggerUserVO(marketingUserId, null)));
				} else {
					Map<String, String> campaignIdToMarketingUserIdMap = campaignMergeDataManager.getMarketingUserIdByCampaignIds(ea, campaignIds);
					triggerUserVOs = new HashSet<>(campaignIdToMarketingUserIdMap.size());
					campaignIdToMarketingUserIdMap.forEach((campaignId, marketingUserId) -> {
						triggerUserVOs.add(new TriggerUserVO(marketingUserId, campaignId));
					});
				}
				triggerInstanceManager.startInstanceByTriggerSnapshotAndSceneAsync(ea, sceneTrigger.getId(), triggerSnapshot.getId(), triggerUserVOs, null);
				return Result.newSuccess(triggerUserVOs.size());
			}catch (Exception e){
				log.warn("Exception", e);
			}finally {
				if (TriggerTypeEnum.SINGLE_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType())) {
					sceneTriggerTimedTaskDao.updateTimedTaskStatus(ea, sceneTriggerTimedTaskId, SceneTriggerTimedTaskExecuteStatus.EXECUTED.getExecuteStatus());
				}
				if (TriggerTypeEnum.REPEAT_TIMING.getTriggerType().equals(triggerSnapshot.getTriggerType())) {
					Long repeatRangeEnd = triggerSnapshot.getRepeatRangeEnd();
					Long nextStartTime = nextTime(triggerSnapshot.getRepeatType(), triggerSnapshot.getRepeatValue(), triggerSnapshot.getTriggerAtMinutes());
						//停止周期触发
						if (nextStartTime == null || nextStartTime > repeatRangeEnd) {
							sceneTriggerTimedTaskDao.updateTimedTaskStatus(ea, sceneTriggerTimedTaskId, SceneTriggerTimedTaskExecuteStatus.EXECUTED.getExecuteStatus());
						} else {
							sceneTriggerTimedTaskDao.updateRepeatTimedTaskStatusAndExecuetime(ea, sceneTriggerTimedTaskId, nextStartTime);
						}
				}
			}
		}
		return Result.newSuccess(0);
	}

	private Long nextTime(Integer repeatType, String repeatValue, int triggerAtMinute) {
		if (3 == repeatType) {
			return DateUtil.getExactTime(new Date(), 1, triggerAtMinute);
		}
		List<Double> repeatValueDou = gson.fromJson(repeatValue, List.class);
		List<Integer> values = repeatValueDou.stream().map(item -> item.intValue()).collect(Collectors.toList());
		Collections.sort(values);
		Calendar c = Calendar.getInstance();

		if (2 == repeatType) {
			int currentDayOfWeek = c.get(Calendar.DAY_OF_WEEK);
			if (1 == currentDayOfWeek) {
				currentDayOfWeek = 7;
			} else {
				currentDayOfWeek -= 1;
			}
			for (Integer nextRepeat : values) {
				//本周执行
				if (nextRepeat > currentDayOfWeek) {
					return DateUtil.getExactTime(new Date(), nextRepeat - currentDayOfWeek, triggerAtMinute);
				}
			}
			//下周执行
			return DateUtil.getExactTime(new Date(), 7 + values.get(0) - currentDayOfWeek, triggerAtMinute);
		}
		if (1 == repeatType) {
			int currentDayOfMonth = c.get(Calendar.DAY_OF_MONTH);
			int lastDayOfMonth = c.getLeastMaximum(Calendar.DAY_OF_MONTH);
			//当月最后一天执行，下次就是下个月
			if (currentDayOfMonth == lastDayOfMonth) {
				int offSetDay = values.get(0);
				Date nextMonth = DateUtil.plusMonth(DateUtil.getFirstDayOfMonth(c.get(Calendar.YEAR), c.get(Calendar.MONTH)), 1);
				Calendar nextMonthCalendar = Calendar.getInstance();
				nextMonthCalendar.setTime(nextMonth);
				int nextMonthLastDay = nextMonthCalendar.getLeastMaximum(Calendar.DAY_OF_MONTH);
				return DateUtil.getExactTime(new Date(), nextMonthLastDay <= offSetDay ? nextMonthLastDay : offSetDay, triggerAtMinute);
			} else {
				for (Integer next : values) {
					if (currentDayOfMonth < next) {
						if (next <= lastDayOfMonth) {
							return DateUtil.getExactTime(new Date(), next - currentDayOfMonth, triggerAtMinute);
						} else {
							return DateUtil.getExactTime(new Date(), lastDayOfMonth - currentDayOfMonth, triggerAtMinute);
						}
					}
				}
			}
		}
		return null;
	}

	@Override
	public Result<Boolean> finishTaskInstance(String taskInstanceId) {
		return Result.newSuccess(triggerTaskInstanceManager.finishTriggerTask(taskInstanceId));
	}

	@Override
	public Result<EditObjectGroupResult> editQywxSceneTriggerGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
		if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
			log.info("MarketingSceneServiceImpl.editQywxSceneTriggerGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
			return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
		}
		List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
		if (defaultNames.contains(arg.getName())){
			log.info("MarketingSceneServiceImpl.editQywxSceneTriggerGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
			return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
		}
		int type = ObjectTypeEnum.QY_SOP.getType();
		if (4 == arg.getType()) {
			type = ObjectTypeEnum.QY_GROUP_SOP.getType();
		}
		return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), type);
	}

	@Override
	public Result<Void> deleteQywxSceneTriggerGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
		if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
			log.info("MarketingSceneServiceImpl.deleteQywxSceneTriggerGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
			return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
		}
		int type = ObjectTypeEnum.QY_SOP.getType();
		if (4 == arg.getType()) {
			type = ObjectTypeEnum.QY_GROUP_SOP.getType();
		}
		return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), type);
	}

	@Override
	public Result<Void> setQywxSceneTriggerGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//		if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//			log.info("MarketingSceneServiceImpl.setQywxSceneTriggerGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//			return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//		}
		List<SceneTriggerEntity> sceneTriggerEntities = sceneTriggerDao.getByIds(arg.getObjectIdList());
		if (org.apache.commons.collections.CollectionUtils.isEmpty(sceneTriggerEntities)) {
			return Result.newError(SHErrorCode.QYWX_SOP_NOT_FOUND);
		}
		if (sceneTriggerEntities.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
			return Result.newError(SHErrorCode.PART_QYWX_SOP_NOT_FOUND);
		}

		ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
		if (objectGroupEntity == null){
			return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
		}
		int type = ObjectTypeEnum.QY_SOP.getType();
		if (4 == arg.getType()) {
			type = ObjectTypeEnum.QY_GROUP_SOP.getType();
		}
		objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, type, arg.getObjectIdList());
		List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
		for (String objectId : arg.getObjectIdList()) {
			ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
			newEntity.setId(UUIDUtil.getUUID());
			newEntity.setEa(ea);
			newEntity.setGroupId(arg.getGroupId());
			newEntity.setObjectId(objectId);
			newEntity.setObjectType(type);
			insertList.add(newEntity);
		}
		objectGroupRelationDAO.batchInsert(insertList);
		return Result.newSuccess();
	}

	@Override
	public Result<Void> topQywxSceneTrigger(String ea, Integer fsUserId, TopMaterialArg arg) {
		SceneTriggerEntity entity = sceneTriggerDao.queryById(arg.getObjectId());
		if (entity == null) {
			return Result.newError(SHErrorCode.QYWX_SOP_IS_DELETED);
		}
		ObjectTopEntity objectTopEntity = new ObjectTopEntity();
		objectTopEntity.setId(UUIDUtil.getUUID());
		objectTopEntity.setEa(ea);
		objectTopEntity.setObjectId(arg.getObjectId());
		objectTopEntity.setObjectType(ObjectTypeEnum.QY_SOP.getType());
		objectTopManager.insert(objectTopEntity, fsUserId);
		return Result.newSuccess();
	}

	@Override
	public Result<Void> cancelTopQywxSceneTrigger(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
		objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.QY_SOP.getType(), arg.getObjectId());
		return Result.newSuccess();
	}

	@Override
	public Result<Void> addQywxSceneTriggerGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
		if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
			return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
		}
		objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.QY_SOP.getType());
		return Result.newSuccess();
	}

	@Override
	public Result<ObjectGroupListResult> listQywxSceneTriggerGroup(String ea, Integer fsUserId, ListGroupSceneTriggerArg arg) {
		List<ListObjectGroupResult> resultList = new ArrayList<>();
		// 获取客户自定义分组的信息,只会返回有权限查看的分组
		int type = ObjectTypeEnum.QY_SOP.getType();
		if (4 == arg.getType()) {
			type = ObjectTypeEnum.QY_GROUP_SOP.getType();
		}
		ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, type, null, null);
		List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
		if (arg.getUseType() != null && arg.getUseType() == 0) {
			// 统计系统分组的数量
			for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
				ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
				objectGroupResult.setSystem(true);
				objectGroupResult.setGroupId(objectGroup.getId());
				objectGroupResult.setGroupName(objectGroup.getName());
				objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
				if (objectGroup == DefaultObjectGroupEnum.ALL){
					// 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
					if (org.apache.commons.collections4.CollectionUtils.isEmpty(groupIdList)) {
						objectGroupResult.setObjectCount(sceneTriggerDao.queryUnGroupAndCreateByMeCount(ea, fsUserId,arg.getSceneId(), type));
					} else {
						// 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
						objectGroupResult.setObjectCount(sceneTriggerDao.queryAccessibleCount(ea, groupIdList, fsUserId,arg.getSceneId(), type));
					}
				} else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
					objectGroupResult.setObjectCount(sceneTriggerDao.queryCountCreateByMe(ea, fsUserId,arg.getSceneId(), type));
				} else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
					objectGroupResult.setObjectCount(sceneTriggerDao.queryCountByUnGrouped(ea,arg.getSceneId(), type));
				}
				resultList.add(objectGroupResult);
			}
		}
		ObjectGroupListResult vo = new ObjectGroupListResult();
		vo.setSortVersion(customizeGroupListVO.getSortVersion());
		resultList.addAll(customizeGroupListVO.getObjectGroupList());
		vo.setObjectGroupList(resultList);
		return Result.newSuccess(vo);
	}

	@Override
	public Result<List<String>> getSceneTriggerGroupRole(String groupId) {
		List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
		List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
		return Result.newSuccess(roleIdList);
	}
}