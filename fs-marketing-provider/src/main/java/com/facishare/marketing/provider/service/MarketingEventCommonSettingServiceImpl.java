/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.LeadStageOptionVO;
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg;
import com.facishare.marketing.api.arg.marketingEvent.UpdateMarketingEventAnalysisSettingArg;
import com.facishare.marketing.api.arg.marketingEventCommonSetting.UpdateMarketingEventCommonSettingArg;
import com.facishare.marketing.api.result.EnumDetailResult;
import com.facishare.marketing.api.result.HexagonSimpleResult;
import com.facishare.marketing.api.result.MemberEnrollMarketingEventDisplayTypeConfigItem;
import com.facishare.marketing.api.result.TriggerSimpleResult;
import com.facishare.marketing.api.result.hexagon.CreateSiteResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.GetMarketingEventCommonSettingResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.GetMarketingEventTypeFieldResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.SceneHexagonTemplateResult;
import com.facishare.marketing.api.result.marketingEventCommonSetting.SceneTriggerTemplateSimpleResult;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.api.vo.marketingevent.MarketingEventAnalysisSettingVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonTemplateTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping.MappingDetail;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO;
import com.facishare.marketing.provider.dto.TriggerTaskSnapshotDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity;
import com.facishare.marketing.provider.entity.marketingEvent.MarketingEventAnalysisSettingEntity;
import com.facishare.marketing.provider.manager.TriggerInstanceManager;
import com.facishare.marketing.provider.manager.TriggerTaskInstanceManager;
import com.facishare.marketing.provider.manager.advertiser.AdBigScreenManager;
import com.facishare.marketing.provider.manager.feed.HexagonSiteManager;
import com.facishare.marketing.provider.manager.marketingEvent.MarketingEventAnalysisSettingManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.CrmV2MappingManager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created  By zhoux 2021/03/08
 **/
@Service("marketingEventCommonSettingService")
@Slf4j
public class MarketingEventCommonSettingServiceImpl implements MarketingEventCommonSettingService {

    @Autowired
    private MarketingEventCommonSettingDAO marketingEventCommonSettingDAO;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingTriggerDao marketingTriggerDao;
    @Autowired
    private TriggerSnapshotDao triggerSnapshotDao;
    @Autowired
    private TriggerTaskSnapshotDao triggerTaskSnapshotDao;
    @Autowired
    private TriggerInstanceManager triggerInstanceManager;
    @Autowired
    private TriggerTaskInstanceManager triggerTaskInstanceManager;
    @Autowired
    private HexagonTemplateSiteDAO hexagonTemplateSiteDAO;
    @Autowired
    private HexagonSiteManager hexagonSiteManager;
    @Autowired
    private HexagonTemplatePageDAO hexagonTemplatePageDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private MemberEnrollMarketingEventDisplayTypeConfigDAO memberEnrollMarketingEventDisplayTypeConfigDAO;
    @Autowired
    private CrmV2MappingManager crmV2MappingManager;

    @Autowired
    private MarketingEventAnalysisSettingManager marketingEventAnalysisSettingManager;

    @Autowired
    private AdBigScreenManager adBigScreenManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @ReloadableProperty("hexagonDefaultTemplateSideIdMap")
    private String hexagonDefaultTemplateSideId;

    @Override
    public Result<GetMarketingEventCommonSettingResult> getMarketingEventCommonSetting(Integer type, String ea) {
        GetMarketingEventCommonSettingResult result = new GetMarketingEventCommonSettingResult();
        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(ea);
        if (marketingEventCommonSettingEntity == null) {
            marketingEventCommonSettingEntity = new MarketingEventCommonSettingEntity();
            marketingEventCommonSettingEntity.initActivityTypeMapping();
        }
        if (type != null) {
            ActivityTypeMapping activityTypeMapping = new ActivityTypeMapping();
            List<ActivityTypeMapping.ActivityTypeMappingDetail> activityTypeMappingDetailList = marketingEventCommonSettingEntity.getActivityTypeMapping();
            for (ActivityTypeMapping.ActivityTypeMappingDetail data : activityTypeMappingDetailList) {
                if (data.getActivityType().equals(type)) {
                    activityTypeMapping.add(data);
                }
            }
            marketingEventCommonSettingEntity.setActivityTypeMapping(activityTypeMapping);
        }
        // 查询市场活动类型描述，获取字段名
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MARKETING_EVENT);
        if (CollectionUtils.isEmpty(fieldVos)) {
            log.warn("MarketingEventCommonSettingServiceImpl.getMarketingEventCommonSetting fieldVos is empty");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        Map<String, CrmUserDefineFieldVo> fieldMap = fieldVos.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, data -> data, (v1, v2) -> v1));
        CrmUserDefineFieldVo crmUserDefineFieldVo = fieldMap.get("event_type");
        List<EnumDetailResult> enumDetailResultList = crmUserDefineFieldVo.getEnumDetails();
        Map<String, String> labelMap = enumDetailResultList.stream().collect(Collectors.toMap(EnumDetailResult::getItemCode, EnumDetailResult::getItemName, (v1, v2) -> v1));
        for (ActivityTypeMapping.ActivityTypeMappingDetail activityTypeMappingDetail : marketingEventCommonSettingEntity.getActivityTypeMapping()) {
            for (ActivityTypeMapping.MappingDetail mappingDetail : activityTypeMappingDetail.getMapping()) {
                String fieldName = labelMap.get(mappingDetail.getApiName());
                if (StringUtils.isNotBlank(fieldName)) {
                    mappingDetail.setFieldName(fieldName);
                }
            }
        }
        result.setActivityTypeMapping(marketingEventCommonSettingEntity.getActivityTypeMapping());
        result.setOpenMergePhone(marketingEventCommonSettingEntity.getOpenMergePhone() == null ? false : marketingEventCommonSettingEntity.getOpenMergePhone());
        result.setMarketingEventAudit(marketingEventCommonSettingEntity.getMarketingEventAudit());
        result.setMarketingActivityAudit(marketingEventCommonSettingEntity.getMarketingActivityAudit());
        return Result.newSuccess(result);
    }

    @Override
    public Result updateMarketingEventCommonSetting(UpdateMarketingEventCommonSettingArg arg) {
        // 校验参数
        ActivityTypeMapping activityTypeMappingDetails = arg.getActivityTypeMapping();
        for (ActivityTypeMapping.ActivityTypeMappingDetail activityTypeMappingDetail : activityTypeMappingDetails) {
            if (activityTypeMappingDetail.getActivityType().equals(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType())) {
                List<ActivityTypeMapping.MappingDetail> mappingDetail = activityTypeMappingDetail.getMapping();
                List<String> mappingDetailApiname = mappingDetail.stream().map(MappingDetail::getApiName).collect(Collectors.toList());
                if (!mappingDetailApiname.contains(MarketingEventEnum.CONTENT_MARKETING.getEventType())) {
                    log.warn("MarketingEventCommonSettingServiceImpl.updateMarketingEventCommonSetting error content_marketing not exist arg:{}", arg);
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
            }
        }
        // 添加多会场活动类型，防止在对象上禁用了多会场活动类型，导致映射关系被删掉
        if (CollectionUtils.isNotEmpty(arg.getActivityTypeMapping())) {
            for (ActivityTypeMapping.ActivityTypeMappingDetail data : arg.getActivityTypeMapping()) {
                if (data.getActivityType().equals(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType())) {
                    // 活动营销
                    List<ActivityTypeMapping.MappingDetail> mapping = data.getMapping();
                    if (CollectionUtils.isEmpty(mapping)) {
                        continue;
                    }
                    // 多会场活动
                    List<String> collect = mapping.stream().map(mappingDetail -> mappingDetail.getApiName()).collect(Collectors.toList());
                    if (collect.contains(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType())) {
                        continue;
                    }
                    ActivityTypeMapping.MappingDetail detail = new ActivityTypeMapping.MappingDetail();
                    detail.setApiName(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType());
                    detail.setFieldName(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_MARKETINGEVENTCOMMONSETTINGENTITY_42));
                    mapping.add(detail);
                }
            }
        }

        MarketingEventCommonSettingEntity marketingEventCommonSettingEntity = marketingEventCommonSettingDAO.getSettingByEa(arg.getEa());
        if (marketingEventCommonSettingEntity == null) {
            marketingEventCommonSettingEntity = new MarketingEventCommonSettingEntity();
            marketingEventCommonSettingEntity.setEa(arg.getEa());
            marketingEventCommonSettingEntity.setActivityTypeMapping(arg.getActivityTypeMapping());
            marketingEventCommonSettingEntity.setCreateBy(arg.getFsUserId());
            marketingEventCommonSettingEntity.setOpenMergePhone(arg.getOpenMergePhone());
        } else {
            marketingEventCommonSettingEntity.setActivityTypeMapping(arg.getActivityTypeMapping());
            if (arg.getOpenMergePhone() != null) {
                marketingEventCommonSettingEntity.setOpenMergePhone(arg.getOpenMergePhone());
            }
        }
        if (arg.getMarketingEventAudit() != null) {
            marketingEventCommonSettingEntity.setMarketingEventAudit(arg.getMarketingEventAudit());
        } else {
            marketingEventCommonSettingEntity.setMarketingEventAudit(false);
        }
        if (arg.getMarketingActivityAudit() != null) {
            marketingEventCommonSettingEntity.setMarketingActivityAudit(arg.getMarketingActivityAudit());
        } else {
            marketingEventCommonSettingEntity.setMarketingActivityAudit(false);
        }

        marketingEventCommonSettingDAO.upsertSettingByEa(marketingEventCommonSettingEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<List<GetMarketingEventTypeFieldResult>> getMarketingEventTypeField(String ea) {
        List<CrmUserDefineFieldVo> fieldVos = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.MARKETING_EVENT);
        if (CollectionUtils.isEmpty(fieldVos)) {
            log.warn("MarketingEventCommonSettingServiceImpl.getMarketingEventTypeField fieldVos is empty");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        List<GetMarketingEventTypeFieldResult> results = Lists.newArrayList();
        Map<String, CrmUserDefineFieldVo> fieldMap = fieldVos.stream().collect(Collectors.toMap(CrmUserDefineFieldVo::getFieldName, data -> data, (v1, v2) -> v1));
        CrmUserDefineFieldVo crmUserDefineFieldVo = fieldMap.get("event_type");
        List<EnumDetailResult> enumDetailResultList = crmUserDefineFieldVo.getEnumDetails();
        for (EnumDetailResult enumDetailResult : enumDetailResultList) {
            GetMarketingEventTypeFieldResult getMarketingEventTypeFieldResult = new GetMarketingEventTypeFieldResult();
            getMarketingEventTypeFieldResult.setApiName(enumDetailResult.getItemCode());
            getMarketingEventTypeFieldResult.setLabelName(enumDetailResult.getItemName());
            results.add(getMarketingEventTypeFieldResult);
        }
        return Result.newSuccess(results);
    }

    @Override
    public Result<SceneTriggerTemplateSimpleResult> getSceneTriggerTemplates(String ea) {
        SceneTriggerTemplateSimpleResult result = new SceneTriggerTemplateSimpleResult();
        Map<String, List<TriggerSimpleResult>> map = new HashMap<>();
        result.setSimpleResult(map);
        for (TriggerSceneEnum value : TriggerSceneEnum.values()) {
            List<MarketingTriggerEntity> triggers = marketingTriggerDao.listBySceneAndType(ea, value.getTriggerScene(), 1);
            List<TriggerSimpleResult> triggerListInScene = new ArrayList<>(triggers.size());
            Map<String, TriggerSimpleResult> resultMap = new HashMap<>(triggers.size());
            for (MarketingTriggerEntity trigger : triggers) {
                TriggerSimpleResult triggerSimpleResult = new TriggerSimpleResult();
                triggerSimpleResult.setTriggerId(trigger.getId());
                triggerSimpleResult.setName(trigger.getName());
                triggerListInScene.add(triggerSimpleResult);
                resultMap.put(trigger.getId(), triggerSimpleResult);
            }
            if (!resultMap.isEmpty()) {
                Set<String> triggerIds = resultMap.keySet();
                List<TriggerSnapshotEntity> snapshotEntities = triggerSnapshotDao.listNotSnapshotByTriggerIds(ea, triggerIds);
                Map<String, String> actionTypeNameMap = new HashMap<>();
                Set<String> triggerSnapshotIds = new HashSet<>();
                for (TriggerSnapshotEntity snapshotEntity : snapshotEntities) {
                    String actionTypeName = "在" + triggerTaskInstanceManager.getTriggerActionName(snapshotEntity).replace("活动", value.getName());
                    if (!actionTypeName.endsWith(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_260)) && !actionTypeName.endsWith(I18nUtil.get(I18nKeyEnum.MARK_MARKETINGACTIVITY_QYWXGROUPSENDMANAGER_1167))) {
                        actionTypeName = actionTypeName + I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_260);
                    }
                    actionTypeName = actionTypeName + " ";
                    actionTypeNameMap.put(snapshotEntity.getTriggerId(), actionTypeName);
                    triggerSnapshotIds.add(snapshotEntity.getId());

                }
                if (!triggerSnapshotIds.isEmpty()) {
                    List<TriggerTaskSnapshotDTO> snapshotDTOs = triggerTaskSnapshotDao.getTaskTypes(ea, triggerSnapshotIds);
                    for (TriggerTaskSnapshotDTO snapshotDTO : snapshotDTOs) {
                        String taskTypesStr = snapshotDTO.getTaskTypes();
                        String[] taskTypes = StringUtils.split(taskTypesStr, ',');
                        String actionTypeName = actionTypeNameMap.get(snapshotDTO.getTriggerId());
                        TriggerSimpleResult triggerSimpleResult = resultMap.get(snapshotDTO.getTriggerId());
                        triggerSimpleResult.setActionTypeName(appendTriggerTaskSortName(actionTypeName, taskTypes));
                    }
                }
            }
            map.put(value.getTriggerScene(), triggerListInScene);
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result<SceneHexagonTemplateResult> getSceneHexagonTemplates(String ea) {
        HashMap<String, List<HexagonSimpleResult>> simpleResult = new HashMap<>(HexagonTemplateTypeEnum.SCENE_TYPE.size());
        Map<String, String> defaultTemplateSideIdMap = JSON.parseObject(hexagonDefaultTemplateSideId, new TypeReference<Map<String, String>>() {
        });
        for (HexagonTemplateTypeEnum type : HexagonTemplateTypeEnum.SCENE_TYPE) {
            HexagonTemplateSiteEntity template = null;
            List<HexagonTemplateSiteEntity> templateList = hexagonTemplateSiteDAO.getMarketingTemplatesByEa(ea, type.getType());
            if (templateList.size()!=0) {
                HexagonTemplateSiteEntity defaultTemplate = hexagonTemplateSiteDAO.getMarketingDefaultTemplateByEa(ea, type.getType());
                if(defaultTemplate==null){
                    hexagonTemplateSiteDAO.setDefaultTemplate(ea, type.getType());
                }
                templateList = hexagonTemplateSiteDAO.getMarketingTemplatesByEa(ea, type.getType());
                List<HexagonSimpleResult> hexagonSimpleResultList = templateList.stream().map(o -> dealHexagonSimpleResult(o)).collect(Collectors.toList());
                //查询所有表单映射情况
                List<String> formIds = hexagonSimpleResultList.stream().map(HexagonSimpleResult::getFormId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                Map<String, Boolean> formMappingMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(formIds)) {
                    formMappingMap = crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(ea, formIds);
                }
                for (HexagonSimpleResult hexagonSimpleResult : hexagonSimpleResultList) {
                    if (StringUtils.isNotBlank(hexagonSimpleResult.getFormId()) && formMappingMap.containsKey(hexagonSimpleResult.getFormId())) {
                        hexagonSimpleResult.setHadCrmMapping(formMappingMap.get(hexagonSimpleResult.getFormId()));
                    }
                }
                simpleResult.put(type.getFieldName(),hexagonSimpleResultList);
                continue;
            }
            String defaultTemplateSideId = defaultTemplateSideIdMap.get(type.getFieldName());
            if (StringUtils.isEmpty(defaultTemplateSideId)) {
                continue;
            }
            template = hexagonTemplateSiteDAO.getById(defaultTemplateSideId);
            if (template == null) {
                log.warn("微页面模板不存在id：" + defaultTemplateSideId);
                continue;
            }
            HexagonCopyArg hexagonCopyArg = new HexagonCopyArg();
            hexagonCopyArg.setId(defaultTemplateSideId);
            hexagonCopyArg.setName(template.getName());
            Result<CreateSiteResult> copyResult = hexagonSiteManager.copyTemplateToTemplate(ea, -10000, hexagonCopyArg);
            if (!copyResult.isSuccess()) {
                log.warn(ea + "企业getSceneHexagonTemplates拷贝微页面模板" + defaultTemplateSideId + "时发送错误，result：" +copyResult);
                continue;
            }
            hexagonTemplateSiteDAO.updateTemplateTypeAndDefault(type.getType(), copyResult.getData().getId(),DefaultTemplateTypeEnum.DEFAULT_TEMPLATE.getType());
            simpleResult.put(type.getFieldName(), Lists.newArrayList(new HexagonSimpleResult(copyResult.getData().getId(), hexagonCopyArg.getName(), copyResult.getData().getFormId(), copyResult.getData().getHadCrmMapping(),DefaultTemplateTypeEnum.DEFAULT_TEMPLATE.getType())) );
        }
        SceneHexagonTemplateResult result = new SceneHexagonTemplateResult();
        result.setSimpleResult(simpleResult);
        return Result.newSuccess(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> setDefaultTemplate(String ea, String templateId) {
        HexagonTemplateSiteEntity templateSite = hexagonTemplateSiteDAO.getById(templateId);
        if (templateSite == null) {
            log.warn("MarketingEventCommonSettingServiceImpl.setDefaultTemplate templateId is not exist  hexagonTemplateSiteId:{}",templateId);
            return Result.newError(SHErrorCode.FORM_TEMPLATE_NOT_EXIST);
        }
        hexagonTemplateSiteDAO.unsetDefaultTemplate(ea,templateSite.getType());
        hexagonTemplateSiteDAO.updateDefaultById(templateId, DefaultTemplateTypeEnum.DEFAULT_TEMPLATE.getType());
        return Result.newSuccess(true);
    }

    @Override
    public Result<List<MemberEnrollMarketingEventDisplayTypeConfigItem>> getMemberEnrollMarketingEventDisplayTypeConfig(String ea, Integer fsUserId) {
        MemberEnrollMarketingEventDisplayTypeConfigEntity entity = memberEnrollMarketingEventDisplayTypeConfigDAO.getByEa(ea);
        if (entity == null){
            //初始化直播和会议
            initMemberEnrollMarketingEvent(ea,fsUserId);
            entity = memberEnrollMarketingEventDisplayTypeConfigDAO.getByEa(ea);
        }

        return Result.newSuccess(JSON.parseArray(entity.getEventTypeDisplay(), MemberEnrollMarketingEventDisplayTypeConfigItem.class));
    }

    private void initMemberEnrollMarketingEvent(String ea, Integer fsUserId) {
        MemberEnrollMarketingEventDisplayTypeConfigEntity entity = new MemberEnrollMarketingEventDisplayTypeConfigEntity();
        entity.setId(UUIDUtil.getUUID());
        entity.setEa(ea);
        entity.setCreateBy(fsUserId);
        List<MemberEnrollMarketingEventDisplayTypeConfigItem> configItemList = Lists.newArrayList();
        MemberEnrollMarketingEventDisplayTypeConfigItem item1 = new MemberEnrollMarketingEventDisplayTypeConfigItem(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_369), "3");
        MemberEnrollMarketingEventDisplayTypeConfigItem item2 = new MemberEnrollMarketingEventDisplayTypeConfigItem(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_370), "live_marketing");
        configItemList.add(item2);
        configItemList.add(item1);
        entity.setEventTypeDisplay(JSON.toJSONString(configItemList));
        memberEnrollMarketingEventDisplayTypeConfigDAO.insert(entity);
    }

    @Override
    public Result setMemberEnrollMarketingEventDisplayTypeConfig(String ea, Integer fsUserId, List<MemberEnrollMarketingEventDisplayTypeConfigItem> configItemList) {
        MemberEnrollMarketingEventDisplayTypeConfigEntity entity = memberEnrollMarketingEventDisplayTypeConfigDAO.getByEa(ea);
        if (entity == null){
            entity = new MemberEnrollMarketingEventDisplayTypeConfigEntity();
            entity.setEa(ea);
            entity.setCreateBy(fsUserId);
            entity.setEventTypeDisplay(JSON.toJSONString(configItemList));
            entity.setId(UUIDUtil.getUUID());
            memberEnrollMarketingEventDisplayTypeConfigDAO.insert(entity);
        }else {
            memberEnrollMarketingEventDisplayTypeConfigDAO.updateByEa(ea, fsUserId, JSON.toJSONString(configItemList));
        }

        return Result.newSuccess();
    }

    /**
     * 根据微页面模板id找到对应的表单
     * @param template  微页面模板
     * @return          HexagonSimpleResult
     */
    private HexagonSimpleResult dealHexagonSimpleResult(HexagonTemplateSiteEntity template) {
        HexagonSimpleResult hexagonSimpleResult = new HexagonSimpleResult(template.getId(), template.getName());
        hexagonSimpleResult.setIsDefault(template.getIsDefault());
        HexagonTemplatePageEntity homePage = hexagonTemplatePageDAO.getHomePage(template.getId());
        if (homePage != null) {
            CustomizeFormDataEntity formDataEntity = customizeFormDataDAO.getCustomizeFormDataById(homePage.getFormId());
            if (formDataEntity != null) {
                hexagonSimpleResult.setFormId(formDataEntity.getId());
                hexagonSimpleResult.setHadCrmMapping(formDataEntity.hadCrmMapping());
                hexagonSimpleResult.setFormUsage(formDataEntity.getFormUsage());
            }
        }
        return hexagonSimpleResult;
    }

    private static String appendTriggerTaskSortName(String begin, String[] taskTypes) {
        if (taskTypes == null || taskTypes.length == 0) {
            return begin;
        }
        StringBuilder send = new StringBuilder(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_418));
        StringBuilder add = new StringBuilder(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGEVENTCOMMONSETTINGSERVICEIMPL_419));
        StringBuilder create = new StringBuilder(I18nUtil.get(I18nKeyEnum.MARK_OCPC_ADOCPCUPLOADMANAGER_822));
        boolean hasWx = false;
        for (String taskType : taskTypes) {
            TriggerTaskTypeEnum type = TriggerTaskTypeEnum.getByTriggerTaskType(taskType);
            if (TriggerTaskTypeEnum.SEND_TYPE.contains(type)) {
                if (TriggerTaskTypeEnum.SEND_WX_TYPE.contains(type)) {
                    if (hasWx) {
                        continue;
                    } else {
                        hasWx = true;
                    }
                }
                send.append(type.getSortName());
            } else if (TriggerTaskTypeEnum.ADD_TYPE.contains(type)) {
                add.append(type.getSortName());
            } else if (TriggerTaskTypeEnum.CREATE_TYPE.contains(type)) {
                create.append(type.getSortName());
            }
        }
        if (send.length() > 2) {
            begin = begin + send + " ";
        }
        if (add.length() > 2) {
            begin = begin + add + " ";
        }
        if (create.length() > 2) {
            begin = begin + create;
        }
        return begin;
    }

    @Override
    public Result<Void> updateAnalysisSetting(UpdateMarketingEventAnalysisSettingArg arg) {
        if (StringUtils.isBlank(arg.getEa()) || CollectionUtils.isEmpty(arg.getSqlDefinition()) || CollectionUtils.isEmpty(arg.getMqlDefinition())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (arg.getMqlDefinition().stream().allMatch(e -> BooleanUtils.isFalse(e.getSelected()))) {
            return Result.newError(SHErrorCode.MQL_DEFINITION_MUST_SELECTED);
        }
        if (arg.getSqlDefinition().stream().allMatch(e -> BooleanUtils.isFalse(e.getSelected()))) {
            return Result.newError(SHErrorCode.SQL_DEFINITION_MUST_SELECTED);
        }

        // 获取线索阶段的描述
        String ea = arg.getEa();
        List<LeadStageOptionVO> leadStageList = adBigScreenManager.getLeadStageOption(ea);
        if (CollectionUtils.isEmpty(leadStageList)) {
            return Result.newError(SHErrorCode.LEAD_STAGE_DESCRIBE_NOT_EXIST);
        }
        // 判断线索阶段的value是否存在
        Set<String> leadStageValueSet = leadStageList.stream().map(LeadStageOptionVO::getValue).collect(Collectors.toSet());

        if (arg.getMqlDefinition().stream().anyMatch(e -> !leadStageValueSet.contains(e.getValue()))) {
            return Result.newError(SHErrorCode.LEAD_STAGE_OPTION_NOT_EXIST);
        }
        if (arg.getSqlDefinition().stream().anyMatch(e -> !leadStageValueSet.contains(e.getValue()))) {
            return Result.newError(SHErrorCode.LEAD_STAGE_OPTION_NOT_EXIST);
        }
        // 校验通过,写入db
        arg.setEa(null);
        if (marketingEventAnalysisSettingManager.getByEa(ea) == null) {
            MarketingEventAnalysisSettingEntity entity = new MarketingEventAnalysisSettingEntity();
            entity.setEa(ea);
            entity.setSetting(JsonUtil.toJson(arg));
            entity.setId(UUIDUtil.getUUID());
            marketingEventAnalysisSettingManager.batchInsert(Lists.newArrayList(entity));
        } else {
            marketingEventAnalysisSettingManager.updateSetting(ea, JsonUtil.toJson(arg));
        }
        return Result.newSuccess();
    }

    @Override
    public Result<MarketingEventAnalysisSettingVO> getAnalysisSetting(String ea) {

        MarketingEventAnalysisSettingEntity entity = marketingEventAnalysisSettingManager.getByEa(ea);
        if (entity == null || StringUtils.isBlank(entity.getSetting())) {
            // 如果设置不存在，获取默认的设置
            return Result.newSuccess(getDefaultMarketingEventAnalysisSetting(ea));
        }
        MarketingEventAnalysisSettingVO vo = JsonUtil.fromJson(entity.getSetting(), MarketingEventAnalysisSettingVO.class);
        List<LeadStageOptionVO> leadStageList = adBigScreenManager.getLeadStageOption(ea);
        // 换成最新的label
        Map<String, String> valueToLabelMap = leadStageList.stream().collect(Collectors.toMap(LeadStageOptionVO::getValue, LeadStageOptionVO::getLabel, (v1, v2) -> v1));
        vo.getMqlDefinition().forEach(e -> e.setLabel(valueToLabelMap.getOrDefault(e.getValue(), e.getLabel())));
        vo.getSqlDefinition().forEach(e -> e.setLabel(valueToLabelMap.getOrDefault(e.getValue(), e.getLabel())));
        return Result.newSuccess(vo);
    }

    private MarketingEventAnalysisSettingVO getDefaultMarketingEventAnalysisSetting(String ea) {
        List<LeadStageOptionVO> leadStageList = adBigScreenManager.getLeadStageOption(ea);
        MarketingEventAnalysisSettingVO vo = new MarketingEventAnalysisSettingVO();
        List<LeadStageOptionVO> mqlDefinition = BeanUtil.copy(leadStageList, LeadStageOptionVO.class);
        List<LeadStageOptionVO> sqlDefinition = BeanUtil.copy(leadStageList, LeadStageOptionVO.class);
        mqlDefinition.forEach(e -> {
            if (AdBigScreenManager.MQL.equals(e.getValue()) || AdBigScreenManager.SQL.equals(e.getValue()) || AdBigScreenManager.OPP.equals(e.getValue())) {
                e.setSelected(true);
            }
        } );

        sqlDefinition.forEach(e -> {
            if (AdBigScreenManager.SQL.equals(e.getValue()) || AdBigScreenManager.OPP.equals(e.getValue())) {
                e.setSelected(true);
            }
        } );
        vo.setMqlDefinition(mqlDefinition);
        vo.setSqlDefinition(sqlDefinition);
        return vo;
    }

}