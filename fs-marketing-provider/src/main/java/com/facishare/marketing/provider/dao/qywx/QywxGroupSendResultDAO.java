package com.facishare.marketing.provider.dao.qywx;

import com.facishare.marketing.api.result.OutSopQywxGroupSendResult;
import com.facishare.marketing.api.result.QywxGroupSendEmployeeRankingDataResult;
import com.facishare.marketing.api.result.qywx.ListEmployeeQywxGroupSendDetailResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.dto.ListEmployeeQywxGroupSendDetailDto;
import com.facishare.marketing.provider.dto.qywx.QueryUserGroupByMsgIdsDTO;
import com.facishare.marketing.provider.dto.qywx.SendGroupMsgByStatusDTO;
import com.facishare.marketing.provider.dto.qywx.SopQywxTaskResultDTO;
import com.facishare.marketing.provider.entity.qywx.QywxGroupSendResultEntity;
import com.github.mybatis.mapper.ICrudMapper;
import com.github.mybatis.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 16:07 2020/2/17
 * @ModifyBy
 */
public interface QywxGroupSendResultDAO extends ICrudMapper<QywxGroupSendResultEntity> {
    @Select("SELECT COUNT(DISTINCT(userid)) FROM qywx_group_send_result WHERE msgid = #{msgid} AND status != 0")
    int queryConfirmCount(@Param("msgid") String msgid);

    @Select("SELECT COUNT(DISTINCT(userid)) FROM qywx_group_send_result WHERE msgid = #{msgid} AND status=0")
    int queryUnConfirmCount(@Param("msgid") String msgid);

    @Select("SELECT count(*) FROM qywx_group_send_result WHERE msgid = #{msgid}")
    int queryTotalCustomer(@Param("msgid") String msgid);

    @Select("SELECT count(*) FROM qywx_group_send_result WHERE msgid = #{msgid} AND status != 0")
    int querySendedCustomer(@Param("msgid") String msgid);

    @Insert("INSERT INTO qywx_group_send_result(id, msgid, external_userid, userid, status, send_time, create_time, update_time)\n"
            + " VALUES(#{entity.id}, #{entity.msgid}, #{entity.externalUserid}, #{entity.userid}, #{entity.status}, #{entity.sendTime}, now(), now())")
    int insert(@Param("entity")QywxGroupSendResultEntity entity);

    @Insert(
            "<script>"
            + "INSERT INTO qywx_group_send_result(id, msgid, external_userid, userid, status, send_time, create_time, update_time, sendid)\n"
            + " VALUES "
            + "<foreach collection=\"entities\" item=\"entity\" index=\"index\" separator=\",\"> "
            + "(#{entity.id}, #{entity.msgid}, #{entity.externalUserid}, #{entity.userid}, #{entity.status}, #{entity.sendTime}, now(), now(), #{entity.sendid})"
            + "</foreach>"
            + "</script>"
            )
    int batchInsert(@Param("entities") List<QywxGroupSendResultEntity> entities);

    @Select("SELECT * FROM qywx_group_send_result WHERE msgid=#{msgid} AND external_userid=#{externalUserid}")
    QywxGroupSendResultEntity queryByMsgidAndexternalUserid(@Param("msgid")String msgid, @Param("externalUserid")String externalUserid);

    @Select("SELECT * FROM qywx_group_send_result WHERE msgid=#{msgid}")
    List<QywxGroupSendResultEntity> queryListByMsgid(@Param("msgid")String msgid);

    @Select("DELETE FROM qywx_group_send_result WHERE id=#{id} AND msgid=#{msgid}")
    void deleteByIdAndMsgid(@Param("id")String id, @Param("msgid")String msgid);

    @Select("SELECT DISTINCT(msgid) FROM qywx_group_send_result")
    List<String> getTaskMsgids();

    @Select("SELECT r.userid AS qywxUserId, count(*) AS msgCount FROM qywx_group_send_task t LEFT JOIN qywx_group_send_result r on t.msgid = r.msgid\n"
            +" WHERE t.marketing_activity_id=#{marketingActivityId} AND r.status=#{status} GROUP BY r.userid")
    List<SendGroupMsgByStatusDTO> querySendMsgByStatus(@Param("marketingActivityId")String marketingActivityId, @Param("status") int status);

    @Select("<script> SELECT r.* FROM (SELECT marketing_activity_id,msgid,unnest(string_to_array(msgid,',')) as singleMsgId FROM qywx_group_send_task WHERE ea = #{ea} and marketing_activity_id=#{marketingActivityId}) t "
            + "JOIN qywx_group_send_result r on t.singleMsgId = r.msgid\n"
            + " WHERE t.marketing_activity_id=#{marketingActivityId} AND t.msgid is not NULL \n"
            + "<if test =\"status != null\">\n"
            + "and r.status = #{status} "
            + "</if>\n"
            + "<if test =\"userIdList != null and userIdList.size != 0\">\n"
                + "and userid in "
                +   "<foreach collection = 'userIdList' item = 'item' index='idx' open = '(' separator = ',' close = ')'>"
                +        "#{userIdList[${idx}]}"
                +   "</foreach>"
            + "</if>\n"
            + " ORDER BY create_time DESC </script>")
    List<QywxGroupSendResultEntity> queryDetailByPager(@Param("ea")String ea, @Param("marketingActivityId")String marketingActivityId, @Param("userIdList") List<String> userIdList , @Param("status") Integer status, @Param("Page")Page page);

    @Select("<script>"
            +" SELECT msgid AS msgid, userid AS userId, status, count(*) AS count FROM qywx_group_send_result WHERE msgid IN\n"
            +   "<foreach collection = 'msgIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{item}"
            +   "</foreach>"
            + "GROUP BY msgid, userid, status"
            + "</script>")
    List<QueryUserGroupByMsgIdsDTO> queryUserGroupByMsgIds(@Param("msgIds")List<String> msgIds);

    @Update("<script>"
            + "UPDATE qywx_group_send_result SET status=#{status}, userid=#{userid},\n"
            + "<if test = \"status == 1\">send_time = #{sendTime},</if>"
            +" update_time = now() WHERE id=#{id}"
            + "</script>")
    int updateStatusById(@Param("id")String id, @Param("status")Integer status, @Param("userid")String userid, @Param("sendTime")Integer sendTime);

    @Update("<script>" +
            "UPDATE qywx_group_send_result as a " +
            "SET status = tmp.status, userid = tmp.userid, " +
            "<choose>" +
            "<when test='updateSendTime'>send_time = tmp.sendTime,</when>" +
            "</choose>" +
            "update_time = now() " +
            "FROM (VALUES " +
            "<foreach collection='updateList' item='item' separator=','>" +
            "(#{item.id}, #{item.status}, #{item.userid}, #{item.sendTime})" +
            "</foreach>" +
            ") as tmp(id, status, userid, sendTime) " +
            "WHERE a.id = tmp.id" +
            "</script>")
    int batchUpdateStatus(@Param("updateList") List<UpdateBatchItem> updateList, @Param("updateSendTime") boolean updateSendTime);

    // 批量更新使用的内部类
    @Data
    @AllArgsConstructor
    class UpdateBatchItem {
        private String id;
        private String ea;
        private Integer status;
        private String userid;
        private Integer sendTime;
    }

    @Select("SELECT * FROM qywx_group_send_result WHERE msgid = #{msgId} AND external_userid=#{externalUserId}  AND  status = #{status} ORDER BY create_time DESC")
    List<QywxGroupSendResultEntity> queryQywxGroupSendResultByMsgId(@Param("msgId") String msgId, @Param("externalUserId") String externalUserId,  @Param("status") Integer status);

    @Delete("DELETE FROM qywx_group_send_result WHERE msgid=#{msgId}")
    int deleteByMsgId(@Param("msgId")String msgId);



    @Select("<script>"+
           "select\n" +
            "\tuserid as userId ,\n" +
            "\tsum(case when status = 0 then total else 0 end) as unComplete ,\n" +
            "\tsum(case when status = 1 then total else 0 end) as complete\n" +
            "from\n" +
            "\t(\n" +
            "\tselect\n" +
            "\t\tre.userid,\n" +
            "\t\tre.status,\n" +
            "\t\tcount(distinct re.external_userid) as total\n" +
            "\tfrom\n" +
            "\t\tqywx_group_send_result re\n" +
            "\tleft join sop_qywx_msg_task task on\n" +
            "\t\tre.msgid = task.msg_id\n" +
            "\twhere\n" +
            "\t\ttask.msg_id is not null\n" +
            "\t\tand task.id in (\n" +
            "\t\tselect\n" +
            "\t\t\texecute_result\n" +
            "\t\tfrom\n" +
            "\t\t\ttrigger_task_instance\n" +
            "\t\twhere\n" +
            "\t\t\tea = #{ea}\n" +
            "\t\t\tand trigger_id = #{triggerId}\n" +
            "\t\t\tand trigger_snapshot_id = #{triggerSnapshotId}\n" +
            "\t\t\tand trigger_task_snapshot_id = #{triggerTaskSnapshotId}\n" +
            "<if test ='batchId != null'>" +
            "\t\t\tand batch_id = #{batchId}\n" +
            "</if>" +
            "\t)\n" +
            "\tgroup by\n" +
            "\t\tuserid,\n" +
            "\t\tstatus\n" +
            ") m\n" +
            "group by\n" +
            "\tuserId\n" +
            "order by\n" +
            "\tcomplete desc,\n" +
            "\tunComplete desc\n" +
            "</script>")
    List<SopQywxTaskResultDTO> listQywxSopTaskEmployee(@Param("ea")String ea, @Param("triggerId")String triggerId, @Param("triggerSnapshotId")String triggerSnapshotId,
                                                       @Param("triggerTaskSnapshotId")String triggerTaskSnapshotId, @Param("batchId")Integer batchId, @Param("page")Page page);


    @Select("<script>"+
            "select\n" +
            "\tcount(distinct userid)\n" +
            "from\n" +
            "\t(\n" +
            "\tselect\n" +
            "\t\tre.userid,\n" +
            "\t\tre.status,\n" +
            "\t\tcount(distinct re.external_userid) as total\n" +
            "\tfrom\n" +
            "\t\tqywx_group_send_result re\n" +
            "\tleft join sop_qywx_msg_task task on\n" +
            "\t\tre.msgid = task.msg_id\n" +
            "\twhere\n" +
            "\t\ttask.msg_id is not null\n" +
            "\t\tand task.id in (\n" +
            "\t\tselect\n" +
            "\t\t\texecute_result\n" +
            "\t\tfrom\n" +
            "\t\t\ttrigger_task_instance\n" +
            "\t\twhere\n" +
            "\t\t\tea = #{ea}\n" +
            "\t\t\tand trigger_id = #{triggerId}\n" +
            "\t\t\tand trigger_snapshot_id = #{triggerSnapshotId}\n" +
            "\t\t\tand trigger_task_snapshot_id = #{triggerTaskSnapshotId}\n" +
            "<if test ='batchId != null'>" +
            "\t\t\tand batch_id = #{batchId}\n" +
            "</if>" +
            "\t)\n" +
            "\tgroup by\n" +
            "\t\tuserid,\n" +
            "\t\tstatus\n" +
            ") m\n" +
            "</script>")
    int countQywxSopTaskEmployee(@Param("ea")String ea, @Param("triggerId")String triggerId, @Param("triggerSnapshotId")String triggerSnapshotId,
                                 @Param("triggerTaskSnapshotId")String triggerTaskSnapshotId, @Param("batchId")Integer batchId);

    @Select("<script>" +
            "select\n" +
            "\tsender as userId,\n" +
            "\tsum(unsend_count) as unComplete,\n" +
            "\tsum(success_count) as complete\n" +
            "from\n" +
            "\t(\n" +
            "\tselect\n" +
            "\t\tre.sender,\n" +
            "\t\tre.unsend_count,\n" +
            "\t\tre.success_count\n" +
            "\tfrom\n" +
            "\t\tqywx_group_send_group_result re\n" +
            "\tleft join trigger_task_instance tti on re.msgid = tti.execute_result\n" +
            "\twhere\n" +
            "\t\ttti.execute_result is not null\n" +
            "\t\tand tti.trigger_snapshot_id = #{triggerSnapshotId}\n" +
            "\t\tand tti.trigger_task_snapshot_id = #{triggerTaskSnapshotId}\n" +
            "\t\tand tti.trigger_id = #{triggerId}\n" +
            "\t\tand tti.ea = #{ea}\n" +
            "<if test ='batchId != null'>" +
            "\t\tand batch_id = #{batchId}\n" +
            "</if>" +
            "\tgroup by\n" +
            "\t\tsender,unsend_count, success_count ) m\n" +
            "group by\n" +
            "\tsender\n" +
            "order by\n" +
            "\tcomplete desc,unComplete desc" +
            "</script>")
    List<SopQywxTaskResultDTO> listQywxGroupSopTaskEmployee(@Param("ea")String ea, @Param("triggerId")String triggerId, @Param("triggerSnapshotId")String triggerSnapshotId,
                                                            @Param("triggerTaskSnapshotId")String triggerTaskSnapshotId, @Param("batchId")Integer batchId, @Param("page")Page page);

    @Select("<script>" +
            "select\n" +
            "\tcount(*)\n" +
            "from\n" +
            "\t(\n" +
            "\tselect\n" +
            "\t\tre.sender,\n" +
            "\t\tre.unsend_count,\n" +
            "\t\tre.success_count\n" +
            "\tfrom\n" +
            "\t\tqywx_group_send_group_result re\n" +
            "\tleft join trigger_task_instance tti on re.msgid = tti.execute_result\n" +
            "\twhere\n" +
            "\t\ttti.execute_result is not null\n" +
            "\t\tand tti.trigger_snapshot_id = #{triggerSnapshotId}\n" +
            "\t\tand tti.trigger_task_snapshot_id = #{triggerTaskSnapshotId}\n" +
            "\t\tand tti.trigger_id = #{triggerId}\n" +
            "\t\tand tti.ea = #{ea}\n" +
            "<if test ='batchId != null'>" +
            "\t\tand batch_id = #{batchId}\n" +
            "</if>" +
            "\tgroup by\n" +
            "\t\tsender,unsend_count, success_count ) m\n" +
            "</script>")
    int countQywxGroupSopTaskEmployee(@Param("ea")String ea, @Param("triggerId")String triggerId, @Param("triggerSnapshotId")String triggerSnapshotId,
                                      @Param("triggerTaskSnapshotId")String triggerTaskSnapshotId, @Param("batchId")Integer batchId);

    @Select("<script>"
            +" SELECT msgid AS msgid, userid AS userId, count(*) AS count FROM qywx_group_send_result WHERE status =0 and msgid IN\n"
            +   "<foreach collection = 'msgIds' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{item}"
            +   "</foreach>"
            + "GROUP BY msgid, userid, status"
            + "</script>")
    List<QueryUserGroupByMsgIdsDTO> queryUnCompleteUserGroupByMsgIds(@Param("msgIds")List<String> msgIds);

    @Select("<script>"
            + "SELECT * FROM qywx_group_send_result WHERE msgid= #{msgid} AND external_userid in "
            +   "<foreach collection = 'externalUseridList' item = 'item' index='num' open = '(' separator = ',' close = ')'>"
            +        "#{item}"
            +   "</foreach>"
            + "</script>")
    @FilterLog
    List<QywxGroupSendResultEntity> queryByMsgidAndexternalUseridList(@Param("msgid") String msgid, @Param("externalUseridList") List<String> externalUseridList);

    @Select("<script>"
            + "select * from (\n"
            + "SELECT userid as employeeId, MIN ( send_time * 1000 ) as sendTime, CASE WHEN MAX ( r.status )>= 1 THEN 1 ELSE 0 END as status,\n"
            + "SUM ( CASE WHEN r.status = 0 THEN 1 ELSE 0 END ) as unSendCount,\n"
            + "SUM ( CASE WHEN r.status = 1 THEN 1 ELSE 0 END ) as sentCount,\n"
            + "SUM ( CASE WHEN r.status = 2 THEN 1 ELSE 0 END ) as notFriendRelationCount,\n"
            + "SUM ( CASE WHEN r.status = 3 THEN 1 ELSE 0 END ) as outOfLimitCount\n"
            + "FROM (SELECT marketing_activity_id,msgid,unnest(string_to_array(msgid,',')) as singleMsgId FROM qywx_group_send_task WHERE ea = #{ea} and marketing_activity_id=#{marketingActivityId}) t "
            + " JOIN qywx_group_send_result r on t.singleMsgId = r.msgid\n"
            + "WHERE t.marketing_activity_id=#{marketingActivityId} AND t.msgid is not NULL \n"
            + "<if test =\"userIdList != null and userIdList.size != 0\">\n"
            + "and userid in "
            +   "<foreach collection = 'userIdList' item = 'item' index='idx' open = '(' separator = ',' close = ')'>"
            +        "#{userIdList[${idx}]}"
            +   "</foreach>"
            + "</if>\n"
            + "group by userid ORDER BY sendTime DESC\n"
            + ") tt\n"
            + "<if test =\"status != null\">"
            + "where tt.status = #{status} "
            + "</if>"
            + "</script>")
    List<ListEmployeeQywxGroupSendDetailDto> queryQywxGroupSendDetailByEmployee(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId,
                                                                                @Param("userIdList") List<String> userIdList, @Param("status") Integer status, @Param("page") Page<ListEmployeeQywxGroupSendDetailResult> page);

    @Select("<script> select count(*) from ( SELECT userid as employeeId, MIN ( send_time * 1000 ) as sendTime, CASE WHEN MAX ( r.status )>= 1 THEN 1 ELSE 0 END as status,\n"
            + "SUM ( CASE WHEN r.status = 0 THEN 1 ELSE 0 END ) as unSendCount,\n"
            + "SUM ( CASE WHEN r.status = 1 THEN 1 ELSE 0 END ) as sentCount,\n"
            + "SUM ( CASE WHEN r.status = 2 THEN 1 ELSE 0 END ) as notFriendRelationCount,\n"
            + "SUM ( CASE WHEN r.status = 3 THEN 1 ELSE 0 END ) as outOfLimitCount\n"
            + "FROM (SELECT marketing_activity_id,msgid,unnest(string_to_array(msgid,',')) as singleMsgId FROM qywx_group_send_task WHERE ea = #{ea} and marketing_activity_id=#{marketingActivityId}) t "
            + " JOIN qywx_group_send_result r on t.singleMsgId = r.msgid\n"
            + "WHERE t.marketing_activity_id=#{marketingActivityId} AND t.msgid is not NULL \n"
            + "<if test =\"status != null\">\n"
            + "and r.status = #{status} "
            + "</if>\n"
            + "<if test =\"userIdList != null and userIdList.size != 0\">\n"
            + "and userid in "
            +   "<foreach collection = 'userIdList' item = 'item' index='idx' open = '(' separator = ',' close = ')'>"
            +        "#{userIdList[${idx}]}"
            +   "</foreach>"
            + "</if>\n"
            + " group by userid ) as t </script>")
    int countQywxGroupSendDetailByEmployee(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId,
                                                                                @Param("userIdList") List<String> userIdList, @Param("status") Integer status);

    @Select("<script> SELECT r.external_userid "
            + "FROM (SELECT marketing_activity_id,msgid,unnest(string_to_array(msgid,',')) as singleMsgId FROM qywx_group_send_task"
            + " WHERE ea = #{ea} and marketing_activity_id=#{marketingActivityId}) t  JOIN qywx_group_send_result r on t.singleMsgId = r.msgid\n"
            + "WHERE t.marketing_activity_id=#{marketingActivityId} AND t.msgid is not NULL \n"
            + "<if test =\"status != null\">\n"
            + "and r.status = #{status} "
            + "</if>\n"
            + "<if test =\"employeeId != null\">\n"
            + "and userid = #{employeeId} "
            + "</if>\n"
            + "ORDER BY send_time DESC </script>")
    List<String> listQywxGroupSendUserMarketing(@Param("ea") String ea, @Param("marketingActivityId") String marketingActivityId,
                                                                                @Param("employeeId") String employeeId, @Param("status") Integer status, @Param("page") Page<ListEmployeeQywxGroupSendDetailResult> page);

    @Select("<script>" +
            "select * from (\n" +
            "select t.userid qywxUserId, count(*) receiveCount\n" +
            "from (\n" +
            "select distinct a.userid, a.msgid\n" +
            "from qywx_group_send_result a \n" +
            "where\n" +
            "a.msgid is not null and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "<if test =\"qywxStaticsticsVisitUserRange != null and qywxStaticsticsVisitUserRange.size > 0\">\n" +
            " and a.userid=ANY(ARRAY<foreach collection = 'qywxStaticsticsVisitUserRange' index='idx' open = '[' separator = ',' close = ']'>#{qywxStaticsticsVisitUserRange[${idx}]}</foreach>)\n" +
            "</if>" +
            "union\n" +
            "select distinct a.sender userid, a.msgid\n" +
            "from qywx_group_send_group_flat_result a \n" +
            "where\n" +
            "a.msgid is not null and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "<if test =\"qywxStaticsticsVisitUserRange != null and qywxStaticsticsVisitUserRange.size > 0\">\n" +
            " and a.sender=ANY(ARRAY<foreach collection = 'qywxStaticsticsVisitUserRange' index='idx' open = '[' separator = ',' close = ']'>#{qywxStaticsticsVisitUserRange[${idx}]}</foreach>)\n" +
            "</if>" +
            ") t\n" +
            "group by t.userid\n" +
            ") tt\n" +
            "order by receiveCount desc" +
            "</script>")
    List<QywxGroupSendEmployeeRankingDataResult> queryPageByMarketingActivityIdsAndEmployeeds(@Param("sendIds") List<String> sendIds, @Param("qywxStaticsticsVisitUserRange") List<String> qywxStaticsticsVisitUserRange, Page page);

    @Select("<script>" +
            "select count(distinct t.userid) from (\n" +
            "select distinct a.sendid, a.userid, a.external_userid cus\n" +
            "from qywx_group_send_result a \n" +
            "where a.msgid is not null and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "union \n" +
            "select distinct a.sendid, a.sender userid, a.send_group_id cus\n" +
            "from qywx_group_send_group_flat_result a \n" +
            "where a.msgid is not null and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            ") t" +
            "</script>")
    int calculateReceiveCountBySendIds(@Param("sendIds") List<String> sendIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "select count(distinct t.userid) from (\n" +
            "select distinct a.sendid, a.userid, a.external_userid cus\n" +
            "from qywx_group_send_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "union \n" +
            "select distinct a.sendid, a.sender userid, a.send_group_id cus\n" +
            "from qywx_group_send_group_flat_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            ") t" +
            "</script>")
    int calculateConfirmCountBySendIds(@Param("sendIds") List<String> sendIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("<script>" +
            "select count(distinct t.cus) from (\n" +
            "select distinct a.sendid, a.userid, a.external_userid cus\n" +
            "from qywx_group_send_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            "union \n" +
            "select distinct a.sendid, a.sender userid, a.send_group_id cus\n" +
            "from qywx_group_send_group_flat_result a \n" +
            "where a.status = 1 and a.update_time between #{startDate} and #{endDate}\n" +
            "and a.sendid = ANY(ARRAY<foreach collection = 'sendIds' index='idx' open = '[' separator = ',' close = ']'>#{sendIds[${idx}]}</foreach>)\n" +
            ") t" +
            "</script>")
    int calculateDeliveryCountBySendIds(@Param("sendIds") List<String> sendIds, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("select\n" +
            "\tdistinct\n" +
            "\ta.msgid,\n" +
            "\tb.id sendid\n" +
            "from\n" +
            "\tqywx_group_send_result a \n" +
            "left join qywx_group_send_task b on a.msgid = any(string_to_array(b.msgid, ','))\n" +
            "where b.id is not null and b.ea = #{ea}")
    List<QywxGroupSendResultEntity> queryPageByEa(@Param("ea") String ea, Page page);

    @Update("<script>" +
            "UPDATE qywx_group_send_result as a\n" +
            "SET sendid=tmp.sendid\n" +
            "FROM (values" +
            "<foreach separator=',' collection='entities' item='item'>" +
            "(#{item.msgid}, #{item.sendid})" +
            "</foreach>" +
            ") as tmp(msgid, sendid) WHERE a.msgid = tmp.msgid" +
            "</script>")
    int batchUpdateSendId(@Param("entities") List<QywxGroupSendResultEntity> entities);

    @Select("<script>"
            +"SELECT COALESCE(sum(count),0)\n" +
                "FROM (\n" +
                "         SELECT msgid, COUNT(DISTINCT userid) AS count\n" +
                "         FROM qywx_group_send_result\n" +
                "         WHERE msgid = ANY(ARRAY<foreach collection = 'msgIds' index='idx' open = '[' separator = ',' close = ']'>#{msgIds[${idx}]}</foreach>) AND status != 0\n" +
                "         GROUP BY msgid\n" +
                "     ) AS c;"
            + "</script>")
    int queryConfirmCountByMsgIds(@Param("msgIds")List<String> msgIds);

    @Select("<script>"
            +"SELECT COALESCE(sum(count),0)\n" +
            "FROM (\n" +
            "         SELECT msgid, COUNT(DISTINCT userid) AS count, CASE WHEN MAX (status )>= 1 THEN 1 ELSE 0 END as status\n" +
            "         FROM qywx_group_send_result\n" +
            "         WHERE msgid = ANY(ARRAY<foreach collection = 'msgIds' index='idx' open = '[' separator = ',' close = ']'>#{msgIds[${idx}]}</foreach>) \n" +
            "         GROUP BY msgid\n" +
            "     ) AS c\n" +
            "where c.status = 0"
            + "</script>")
    int queryUnConfirmCountByMsgIds(@Param("msgIds")List<String> msgIds);


    @Select("<script>"
            +"SELECT count(*) FROM qywx_group_send_result WHERE msgid = ANY(ARRAY<foreach collection = 'msgIds' index='idx' open = '[' separator = ',' close = ']'>#{msgIds[${idx}]}</foreach>)\n"
            +"</script>")
    int queryTotalCustomerByMsgIds(@Param("msgIds")List<String> msgIds);

    @Select("<script>"
            +"SELECT count(*) FROM qywx_group_send_result WHERE msgid = ANY(ARRAY<foreach collection = 'msgIds' index='idx' open = '[' separator = ',' close = ']'>#{msgIds[${idx}]}</foreach>) AND status != 0\n"
            +"</script>")
    int querySendedCustomerByMsgIds(@Param("msgIds")List<String> msgIds);
    @Select("SELECT * FROM qywx_group_send_result WHERE  external_userid=#{externalUserid}")
    List<QywxGroupSendResultEntity> queryExternalUserId(@Param("externalUserid")String externalUserid);

    @Update("update qywx_group_send_result SET external_userid = #{externalUserId}  WHERE id = #{id}")
    int updateExternalUserId(@Param("id") String id, @Param("externalUserId")String externalUserId);

    @Select("select re.userid, external_userid,status\n" +
            "from qywx_group_send_result re \n" +
            "left join sop_qywx_msg_task task on re.msgid = task.msg_id \n" +
            "where task.id in (\n" +
            "select tti.execute_result \n" +
            "from trigger_task_instance tti \n" +
            "left join trigger_instance ti on tti.trigger_instance_id = ti.id \n" +
            "where ti.ea = #{ea} and ti.trigger_record_id = #{triggerRecordId}\n" +
            ")")
    List<OutSopQywxGroupSendResult> getOutSopQywxGroupSendResult(@Param("ea") String ea, @Param("triggerRecordId") String triggerRecordId);
}
