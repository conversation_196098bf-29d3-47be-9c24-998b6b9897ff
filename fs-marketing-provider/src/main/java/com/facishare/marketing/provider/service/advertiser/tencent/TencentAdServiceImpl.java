package com.facishare.marketing.provider.service.advertiser.tencent;

import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.result.advertiser.tencent.*;
import com.facishare.marketing.api.result.baidu.TrendGraphDataResult;
import com.facishare.marketing.api.service.advertiser.TencentAdService;
import com.facishare.marketing.api.vo.advertiser.QueryTencentAdGroupListVO;
import com.facishare.marketing.common.enums.advertiser.tencent.TencentAdStatusEnum;
import com.facishare.marketing.common.enums.advertiser.tencent.TencentBidModEnum;
import com.facishare.marketing.common.enums.advertiser.tencent.TencentSiteSetEnum;
import com.facishare.marketing.common.enums.baidu.AccountStatusEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dto.TencentAdGroupDTO;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.entity.baidu.TrendGraphDataDTOEntity;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.advertiser.AdCommonManager;
import com.facishare.marketing.provider.manager.advertiser.tencent.TencentAdMarketingManager;
import com.facishare.marketing.provider.manager.baidu.CampaignDataManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("tencentAdService")
public class TencentAdServiceImpl implements TencentAdService {
    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;
    @Autowired
    private TencentAdMarketingManager tencentAdMarketingManager;
    @Autowired
    private AdAccountManager adAccountManager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @ReloadableProperty("tencent.ad.client.id")
    private String clientId;

    @Override
    public Result<PageResult<TencentAdGroupResult>> queryTencentAdGroupList(QueryTencentAdGroupListVO vo) {
        PageResult<TencentAdGroupResult> pageResult = new PageResult<>();
        List<TencentAdGroupResult> list = new ArrayList<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        pageResult.setResult(Lists.newArrayList());
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<TencentAdGroupDTO> tencentAdGroupDTOS = tencentAdGroupDAO.queryTencentAdGroupListByCondition(vo.getEa(), vo.getAdAccountId(), vo.getCampaignName(), vo.getAdGroupName(), null, null, vo.getStatus(), page, vo.getDataType());
        if (CollectionUtils.isEmpty(tencentAdGroupDTOS) || tencentAdGroupDTOS.get(0) == null) {
            return Result.newSuccess(pageResult);
        }
        List<Long> adGroupIds = tencentAdGroupDTOS.stream().map(TencentAdGroupDTO::getAdGroupId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adGroupIds)) {
            return Result.newSuccess(pageResult);
        }
        List<TencentAdGroupDTO> adGroupDataDTOS = tencentAdGroupDAO.queryTencentAdGroupDatas(vo.getEa(), vo.getAdAccountId(), adGroupIds, vo.getStartTime(), vo.getEndTime());
        List<String> marketingEventIds = adGroupDataDTOS.stream().filter(tencentAdGroupDTO -> StringUtils.isNotBlank(tencentAdGroupDTO.getSubMarketingEventId())).map(TencentAdGroupDTO::getSubMarketingEventId).collect(Collectors.toList());
        Long startTime = vo.getStartTime() == null ? null : vo.getStartTime().getTime();
        Long endTime = vo.getEndTime() == null ? null : vo.getEndTime().getTime();
        Map<String, Integer> marketingEventIdToLeadCountMap = marketingEventManager.getMarketingEventIdToLeadCountMap(vo.getEa(), marketingEventIds, startTime, endTime);
        tencentAdGroupDTOS.forEach(tencentAdGroupDTO -> {
            TencentAdGroupResult tencentAdGroupResult = BeanUtil.copy(tencentAdGroupDTO, TencentAdGroupResult.class);
            String marketingEventId = StringUtils.isEmpty(tencentAdGroupDTO.getSubMarketingEventId()) ? "" : tencentAdGroupDTO.getSubMarketingEventId();
            tencentAdGroupResult.setLeads(marketingEventIdToLeadCountMap.getOrDefault(marketingEventId, 0));
            tencentAdGroupResult.setBidMode(TencentBidModEnum.getDescByType(tencentAdGroupDTO.getBidMode()));
            tencentAdGroupResult.setStatus(TencentAdStatusEnum.getDescByType(tencentAdGroupDTO.getStatus()));
            tencentAdGroupResult.setSiteSet(TencentSiteSetEnum.getDescListByNames(tencentAdGroupDTO.getSiteSet()));
            tencentAdGroupResult.setDailyBudget(tencentAdGroupDTO.getDailyBudget() == null ? 0 : Math.round(tencentAdGroupDTO.getDailyBudget() ) / 100d);
            tencentAdGroupResult.setBidAmount(tencentAdGroupDTO.getBidAmount() == null ? 0 : Math.round(tencentAdGroupDTO.getBidAmount()) / 100d);
            if (CollectionUtils.isNotEmpty(adGroupDataDTOS)) {
                adGroupDataDTOS.forEach(adGroupDataDTO -> {
                    if (Objects.equals(adGroupDataDTO.getAdGroupId(),tencentAdGroupDTO.getAdGroupId())) {
                        tencentAdGroupResult.setPv(adGroupDataDTO.getPv() == null ? 0 : adGroupDataDTO.getPv());
                        tencentAdGroupResult.setClick(adGroupDataDTO.getClick() == null ? 0 : adGroupDataDTO.getClick());
                        tencentAdGroupResult.setCost(adGroupDataDTO.getCost() == null ? 0 : Math.round(adGroupDataDTO.getCost() ) / 100d);
                        tencentAdGroupResult.setClickPrice(adGroupDataDTO.getClick() == null || adGroupDataDTO.getClick() == 0 || adGroupDataDTO.getCost() == null || adGroupDataDTO.getCost() == 0 ? 0 : Math.round(adGroupDataDTO.getCost()/adGroupDataDTO.getClick()) / 100d);
                    }
                });
            }
            list.add(tencentAdGroupResult);
        });
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(list);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<TencentAdGroupDetailResult> queryTencentAdGroupDetail(String id, Long startTime, Long endTime) {
        Date startDate = startTime == null ? null : new Date(startTime);
        Date endDate = endTime == null ? null : new Date(endTime);
        TencentAdGroupDTO tencentAdGroupDTO = tencentAdGroupDAO.queryTencentAdGroupDetail(id);
        if (tencentAdGroupDTO == null) {
            return Result.newSuccess();
        }
        TencentAdGroupDetailResult result = BeanUtil.copy(tencentAdGroupDTO, TencentAdGroupDetailResult.class);
        TencentAdGroupDTO tencentAdGroupDetailData = tencentAdGroupDAO.queryTencentAdGroupDetailData(tencentAdGroupDTO.getAdAccountId(), tencentAdGroupDTO.getAdGroupId(), startDate, endDate);
        result.setPv((tencentAdGroupDetailData == null || tencentAdGroupDetailData.getPv() == null) ? 0 : tencentAdGroupDetailData.getPv());
        result.setClick((tencentAdGroupDetailData == null || tencentAdGroupDetailData.getClick() == null) ? 0 : tencentAdGroupDetailData.getClick());
        result.setCost((tencentAdGroupDetailData == null || tencentAdGroupDetailData.getCost() == null) ? 0 : Math.round(tencentAdGroupDetailData.getCost() ) / 100d);
        result.setClickPrice((tencentAdGroupDetailData== null || tencentAdGroupDetailData.getClick() == null || tencentAdGroupDetailData.getClick() == 0) ? 0 : Math.round(tencentAdGroupDetailData.getCost()/tencentAdGroupDetailData.getClick()) / 100d);
        List<MarketingEventData> eventDataList = marketingEventManager.listMarketingEventData(tencentAdGroupDTO.getEa(), -10000, Lists.newArrayList(tencentAdGroupDTO.getSubMarketingEventId()));
        if (CollectionUtils.isNotEmpty(eventDataList)) {
            result.setEventType(eventDataList.get(0).getEventType());
        }
        return Result.newSuccess(result);
    }


    @Override
    public Result<TrendGraphDataResult> getTrendGraphDataList(String ea, String id, Long startTime, Long endTime) {
        List<TrendGraphDataDTOEntity> trendGraphDataDTOEntityList = tencentAdGroupDAO.getTrendGraphDataList(ea ,id, new Date(startTime), new Date(endTime));
        if (CollectionUtils.isEmpty(trendGraphDataDTOEntityList) || trendGraphDataDTOEntityList.get(0) == null) {
            return Result.newSuccess();
        }
        List<TrendGraphDataResult.TrendGraphData> trendGraphDataList = new ArrayList<>();
        trendGraphDataDTOEntityList.forEach(entity -> {
            TrendGraphDataResult.TrendGraphData data = new TrendGraphDataResult.TrendGraphData();
            data.setDate(DateUtil.parse(entity.getActionDate(), DateUtil.DATE_FORMAT_DAY));
            data.setPv(entity.getPv() == null ? 0 : entity.getPv());
            data.setClick(entity.getClick() == null ? 0 : entity.getClick());
            data.setCost(entity.getCost() == null ? 0 : Math.round(entity.getCost() ) / 100d);
            data.setClickPrice(entity.getClick() == null || entity.getClick() == 0 ? 0 : Math.round(data.getCost()/data.getClick()) / 100d);
            trendGraphDataList.add(data);
        });
        TrendGraphDataResult result = new TrendGraphDataResult();
        result.setTrendGraphDataList(trendGraphDataList);
        return Result.newSuccess(result);
    }

    @Override
    public Result<RefreshAdDataResult> refreshAdData(String ea, List<String> adAccountIds) {
        adAccountIds = adAccountIds.stream().filter(e -> !adAccountManager.isPrototypeRoomAccount(ea, e)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adAccountIds)) {
            return Result.newSuccess();
        }
        List<AdAccountEntity> adAccountEntities = adAccountManager.queryAccountByIds(adAccountIds);
        if (CollectionUtils.isEmpty(adAccountEntities)) {
            return Result.newSuccess();
        }
        RefreshAdDataResult result = new RefreshAdDataResult();
        this.asyncRefreshAdDataByAdAccount(ea, adAccountEntities);
        result.setIsSuccess(true);
        return Result.newSuccess(result);
    }


    public void asyncRefreshAdDataByAdAccount(String ea, List<AdAccountEntity> adAccountEntities) {
        for (AdAccountEntity adAccountEntity : adAccountEntities) {
            AdAccountEntity prototypeRoomAccountEntity = adAccountManager.getAdPrototypeRoomAccountById(adAccountEntity.getEa(), adAccountEntity.getId());
            if (prototypeRoomAccountEntity != null) {
                continue;
            }
            adAccountManager.updateAdAccountStatusById(AccountStatusEnum.REFRESHING.getStatus(), adAccountEntity.getId());
            ThreadPoolUtils.execute(() -> {
                try {
                    tencentAdMarketingManager.refreshAccountInfo(adAccountEntity);
                    tencentAdMarketingManager.refreshTencentCampaign(ea, adAccountEntity);
                    tencentAdMarketingManager.refreshTencentAdGroup(ea, adAccountEntity, null);
                    tencentAdMarketingManager.refreshTencentAdGroupData(ea, adAccountEntity, adAccountEntity.getId(), null, null);
                    tencentAdMarketingManager.syncTencentCampaignToMarketingEventObj(ea, adAccountEntity.getAccountId(), adAccountEntity.getId());
                    tencentAdMarketingManager.syncTencentAdGroupToSubMarketingEventObj(ea, adAccountEntity.getId());
                    this.asyncRefreshAdLeadsByAdAccount(ea, adAccountEntity);
                    adAccountManager.updateAdAccountStatusById(AccountStatusEnum.NORMAL.getStatus(), adAccountEntity.getId());
                } catch (Exception e) {
                    log.info("TencentAdServiceImpl.refreshAdDataByAdAccount fail, ea:{}", ea, e);
                    adAccountManager.updateAdAccountStatusById(AccountStatusEnum.FAIL.getStatus(), adAccountEntity.getId());
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        }
    }

    @Override
    public Result<RefreshAdLeadsResult> refreshAdLeads(String ea, List<String> adAccountIds) {
        return Result.newSuccess();
//        adAccountIds = adAccountIds.stream().filter(e -> !adAccountManager.isPrototypeRoomAccount(ea, e)).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(adAccountIds)) {
//            return Result.newSuccess();
//        }
//        List<AdAccountEntity> adAccountEntities = adAccountManager.queryAccountByIds(adAccountIds);
//        if (CollectionUtils.isEmpty(adAccountEntities)) {
//            return Result.newSuccess();
//        }
//        RefreshAdLeadsResult result = new RefreshAdLeadsResult();
//        for (AdAccountEntity adAccountEntity : adAccountEntities) {
//            this.asyncRefreshAdLeadsByAdAccount(ea, adAccountEntity);
//        }
//        result.setIsSuccess(true);
//        return Result.newSuccess(result);
    }

    public void asyncRefreshAdLeadsByAdAccount(String ea, AdAccountEntity adAccountEntity) {
        List<Long> timeList = DateUtil.initMonthTimeList(DateUtil.getLastYearFromTime(System.currentTimeMillis()), 3);
        for (int i = 0; i < timeList.size()-1; i++) {
            tencentAdMarketingManager.syncTencentClueDataToCrm(ea, adAccountEntity, adAccountEntity.getId(), timeList.get(i), timeList.get(i+1));
        }
    }

    @Override
    public Result<GetTencentClientIdResult> getTencentClientId(String ea) {
        GetTencentClientIdResult result = new GetTencentClientIdResult();
        result.setClientId(clientId);
        return Result.newSuccess(result);
    }


}
