package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.WxOfficialAccountsQrCodeChannelEntity;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationCommonSettingEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Created  By zhoux 2021/06/25
 **/
public interface WxOfficialAccountsQrCodeChannelDAO {

    @Insert("<script>"
        + "INSERT INTO wx_official_accounts_qr_code_channel (\n"
        + "        \"id\",\n"
        + "        \"ea\",\n"
        + "        \"wx_app_id\",\n"
        + "        \"name\",\n"
        + "        \"channel\",\n"
        + "        \"qr_code_id\",\n"
        + "        \"create_time\",\n"
        + "        \"update_time\"\n"
        + "        )VALUES (\n"
        + "        #{obj.id},\n"
        + "        #{obj.ea},\n"
        + "        #{obj.wxAppId},\n"
        + "        #{obj.name},\n"
        + "        #{obj.channel},\n"
        + "        #{obj.qrCodeId},\n"
        + "        now(),\n"
        + "        now()\n"
        + "        ) ON CONFLICT (ea, wx_app_id, qr_code_id) DO UPDATE SET channel = #{obj.channel}, update_time = now();"
        + "</script>")
    void upsertWxOfficialAccountsQrCodeChannel(@Param("obj") WxOfficialAccountsQrCodeChannelEntity wxOfficialAccountsQrCodeChannelEntity);


    @Select(" SELECT * FROM wx_official_accounts_qr_code_channel WHERE ea = #{ea} AND wx_app_id = #{wxAppId} AND name = #{name} limit 1")
    WxOfficialAccountsQrCodeChannelEntity getWxOfficialAccountsQrCodeChannel(@Param("ea") String ea, @Param("wxAppId") String wxAppId, @Param("name") String name);

    @Select(" SELECT * FROM wx_official_accounts_qr_code_channel WHERE ea = #{ea} AND wx_app_id = #{wxAppId} AND qr_code_id = #{qrCodeId} ")
    WxOfficialAccountsQrCodeChannelEntity getByQrCodeId(@Param("ea") String ea, @Param("wxAppId") String wxAppId, @Param("qrCodeId") String qrCodeId);


}
