package com.facishare.marketing.provider.entity;

import lombok.Data;
import org.apache.http.util.TextUtils;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class UserEntity implements Serializable {
    private String uid;
    private String openid;
    private String name;
    private String avatar;
    private Integer gender;
    private String city;
    private String province;
    private String country;
    private Date createTime;
    private Date lastModifyTime;

    private String appid;
    private String corpid;
    private String qyUserId;
    private String wxUnionId;
    private String dingUserId;
    private String dingUnionId;
    private String externalUserId;

    public String getArea(){
        StringBuilder sb = new StringBuilder();
        if (!TextUtils.isEmpty(province)) {
            sb.append(province);
        }
        if (!TextUtils.isEmpty(city)){
            if (TextUtils.isEmpty(sb.toString())) {
                sb.append(city);
            } else {
                sb.append(" ");
                sb.append(city);
            }
        }
        return sb.toString();
    }
}
