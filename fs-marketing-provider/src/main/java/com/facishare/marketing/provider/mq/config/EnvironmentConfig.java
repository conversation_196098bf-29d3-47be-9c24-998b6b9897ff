package com.facishare.marketing.provider.mq.config;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 环境配置数据模型
 */
@Data
public class EnvironmentConfig {

    /**
     * 环境名称，如: production, gray1, gray2, test
     */
    private String name;

    /**
     * 该环境包含的EA列表
     */
    private List<String> eaList;

    /**
     * 环境优先级，数字越小优先级越高
     */
    private int priority = 100;

    /**
     * 环境是否启用
     */
    private boolean enabled = true;

    /**
     * 默认发送器名称
     */
    private String defaultSender;

    /**
     * 特定Handler的发送器映射
     * key: MessageHandler类名 或 MessageHandler$Tag格式
     * value: 发送器Bean名称
     */
    private Map<String, String> handlerSenderMapping;

    /**
     * 获取指定Handler和Tag的发送器名称
     *
     * @param handlerName Handler类名
     * @param tag         消息标签
     * @return 发送器Bean名称
     */
    public String getSenderName(String handlerName, String tag) {
        if (handlerSenderMapping != null) {
            // 先查找带Tag的精确匹配
            if (tag != null) {
                String senderName = handlerSenderMapping.get(handlerName + "$" + tag);
                if (senderName != null) {
                    return senderName;
                }
            }

            // 再查找Handler级别的匹配
            String senderName = handlerSenderMapping.get(handlerName);
            if (senderName != null) {
                return senderName;
            }
        }

        // 返回默认发送器
        return defaultSender;
    }
} 