package com.facishare.marketing.provider.entity;

import com.facishare.marketing.common.typehandlers.value.CardCommonSettingList;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Entity;

import com.facishare.marketing.common.typehandlers.value.WechatAccountChannelList;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Entity
public class EnterpriseMetaConfigEntity implements Serializable {
    private String id;
    /** 企业ea */
    private String ea;
    /** 是否支持将客户保存到CRM */
    private boolean isSaveCustomerToCrmEnabled;
    /** 是否支持将人脉保存到CRM */
    private boolean isSaveMankeepToCrmEnabled;
    /**
     * 市场活动配色
     */
    private FieldValueList marketingEventTypeColor;
    /**
     * 1/null：未设置
     * 2：使用官网频道
     * 3：自己设置的主页
     */
    private Integer miniappIntroductionSiteType;
    /**
     * 自己设置的主页id
     */
    private String miniappIntroductionSiteId;
    /**
     * 托管小程序的频道显示与频道的名称、位置
     */
    private WechatAccountChannelList wechatAccountChannelList;

    /**
     * 名片通用设置
     */
    private CardCommonSettingList cardCommonSetting;

}
