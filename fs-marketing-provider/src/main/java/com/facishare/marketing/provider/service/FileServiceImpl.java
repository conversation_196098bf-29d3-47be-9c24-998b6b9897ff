/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.GetDownLoadUrlArg;
import com.facishare.marketing.api.arg.GetPreviewUrlArg;
import com.facishare.marketing.api.arg.SendFileMailArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.result.FilePreviewResult;
import com.facishare.marketing.api.result.GetDownLoadResult;
import com.facishare.marketing.api.vo.mail.MailServiceMarketingActivityVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.mail.*;
import com.facishare.marketing.common.typehandlers.value.Email;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.service.OutFileService;
import com.facishare.marketing.api.arg.file.BatchDownloadPicToZipArg;
import com.facishare.marketing.api.arg.file.BatchGetUrlByPathArg;
import com.facishare.marketing.api.arg.file.UploadFileArg;
import com.facishare.marketing.api.arg.photoLibrary.CPathMetaDataByTcpathArg;
import com.facishare.marketing.api.result.CdnUrlByCpathResult;
import com.facishare.marketing.api.result.file.BatchGetUrlByPathResult;
import com.facishare.marketing.api.result.file.CreateTNFileFromAFileArgResult;
import com.facishare.marketing.api.result.file.GenerateUploadFileOmitResult;
import com.facishare.marketing.api.result.file.GetUrlByPathUnitResult;
import com.facishare.marketing.api.result.UploadFileResult;
import com.facishare.marketing.api.result.photoLibrary.CPathMetaDataResult;
import com.facishare.marketing.api.service.FileService;
import com.facishare.marketing.api.vo.GetFileBySpliceUrlVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.ReplaceUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.EnterprseInfoDao;
import com.facishare.marketing.provider.dao.FileLibrary.FileLibraryDAO;
import com.facishare.marketing.provider.dao.PhotoSelectorDAO;
import com.facishare.marketing.provider.dao.PicFsMappingDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskResultDAO;
import com.facishare.marketing.provider.dao.photoLibrary.PhotoLibraryDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dto.MarketingUserWithEmail;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskEntity;
import com.facishare.marketing.provider.entity.mail.MailSendTaskResultEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.innerArg.CreateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.innerArg.mail.SendEmailArg;
import com.facishare.marketing.provider.innerResult.mail.SendEmailResult;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FileV2Manager.FileManagerPicResult;
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager;
import com.facishare.marketing.provider.manager.kis.ObjectManager;
import com.facishare.marketing.provider.manager.mail.MailManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.mq.sender.MarketingRecordActionSender;
import com.facishare.marketing.provider.mq.sender.MarketingUserActionEvent;
import com.facishare.stone.sdk.response.StoneSaveFileFromTempFileResponse;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;

import java.io.*;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.github.mybatis.util.UnicodeFormatter;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service("fileService")
@Slf4j
public class FileServiceImpl implements FileService {

    @Autowired
    private FileManager fileManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private OutFileService outFileService;

    @Autowired
    private ObjectManager objectManager;

    @Autowired
    private HttpManager httpManager;
    @Autowired
    private QRPosterDAO qrPosterDAO;

    @Autowired
    private PicFsMappingDAO picFsMappingDAO;
    @ReloadableProperty("picture.fsEa")
    private String pictureFsEa;
    @ReloadableProperty("host")
    private String host;

    @Autowired
    private PushSessionManager pushSessionManager;
    @Autowired
    private PhotoLibraryDAO photoLibraryDAO;
    @Autowired
    private PhotoSelectorDAO photoSelectorDAO;
    @Autowired
    private DownloadQrCodeManager downloadQrCodeManager;
    @Autowired
    private EnterprseInfoDao enterprseInfoDao;
    @Autowired
    private MailManager mailManager;
    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;
    @Autowired
    private MailSendTaskResultDAO mailSendTaskResultDAO;
    @Autowired
    private FileLibraryDAO fileLibraryDAO;
    private final int generateUploadFileOmitTime = 3600;  //上传文件签名超时时间

    @Autowired
    private EmailSendRecordDetailObjManager emailSendRecordDetailObjManager;

    @Autowired
    private MarketingRecordActionSender marketingRecordActionSender;
    @Override
    public Result<Void> ensureKmFolder(String ea, Integer fsUserId) {
        fileManager.ensureKmFolder(false, FileBelongEnum.COMPANY.getType(), ea, eieaConverter.enterpriseAccountToId(ea), fsUserId);
        return Result.newSuccess();
    }

    @Override
    public byte[] transferUrl(String path) {
        if (path.startsWith(host)) {
            return null;
        }

        return fileV2Manager.getImageFromNetByUrl(path);
    }

    @Override
    public Result<UploadFileResult> uploadFile(UploadFileArg arg) {
        String path = null;
        String url = null;
        if (BooleanUtils.isTrue(arg.getNeedApath())) {
            path = fileV2Manager.uploadToApath(arg.getFileBytes(), arg.getExt(), null);
        } else {
            path = fileV2Manager.uploadToTApath(arg.getFileBytes(), arg.getExt(), null, null);
        }

        if (!path.contains(".")) {
            path = path + "." + arg.getExt();
        }

        url = fileV2Manager.getUrlByPath(path, null, false);
        if (StringUtils.isBlank(path) || StringUtils.isBlank(url)) {
            return new Result<>(SHErrorCode.FILE_UPLOAD_FAILED);
        }

        if (BooleanUtils.isTrue(arg.getNeedPermanent())) {
            String targetUrl = fileV2Manager.getSpliceUrl(arg.getEa(), path);
            if (StringUtils.isNotBlank(targetUrl)) {
                url = targetUrl;
            }
        }

        UploadFileResult uploadFileResult = new UploadFileResult();
        uploadFileResult.setPath(path);
        uploadFileResult.setUrl(url);
        uploadFileResult.setFileName(arg.getFileName());
        return Result.newSuccess(uploadFileResult);
    }

    @Override
    public Result<BatchGetUrlByPathResult> batchGetUrlByPath(BatchGetUrlByPathArg arg) {
        List<String> pathList = arg.getPathList();

        Map<String, GetUrlByPathUnitResult> resultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(pathList)) {
            List<String> taPathList = new ArrayList<>();
            List<String> aPathList = new ArrayList<>();

            for (String path : pathList) {
                if (path.startsWith("TA_")) {
                    taPathList.add(path);
                } else if (path.startsWith("A_")) {
                    aPathList.add(path);
                }
            }

            if (CollectionUtils.isNotEmpty(taPathList)) {
                if (BooleanUtils.isTrue(arg.getNeedApath())) {
                    for (String tapath : taPathList) {
                        FileV2Manager.FileManagerPicResult fileManagerPicResult = fileV2Manager.getApathByTApath(tapath, arg.getEa(), arg.getUserId());
                        if (null != fileManagerPicResult && StringUtils.isNotBlank(fileManagerPicResult.getUrlAPath()) && StringUtils.isNotBlank(fileManagerPicResult.getUrl())) {
                            String url = fileManagerPicResult.getUrl();

                            if (BooleanUtils.isTrue(arg.getNeedPermanent())) {
                                String targetUrl = fileV2Manager.getSpliceUrl(arg.getEa(), fileManagerPicResult.getUrlAPath());
                                if (StringUtils.isNotBlank(targetUrl)) {
                                    url = targetUrl;
                                }
                            }

                            GetUrlByPathUnitResult unitResult = new GetUrlByPathUnitResult();
                            unitResult.setPath(tapath);
                            unitResult.setApath(fileManagerPicResult.getUrlAPath());
                            unitResult.setUrl(url);
                            resultMap.put(tapath, unitResult);
                        }
                    }
                } else {
                    batchGetUrlbyPath(taPathList, null, resultMap, arg.getNeedPermanent());
                }
            }

            if (CollectionUtils.isNotEmpty(aPathList)) {
                batchGetUrlbyPath(aPathList, null, resultMap, arg.getNeedPermanent());
            }
        }

        BatchGetUrlByPathResult result = new BatchGetUrlByPathResult();
        result.setUrlMap(resultMap);
        return new Result(SHErrorCode.SUCCESS, result);

    }

    @Override
    public byte[] getFileBySpliceUrl(GetFileBySpliceUrlVO vo) {
        try {
            return fileV2Manager.getFileByApathAndParam(vo);
        } catch (Exception e) {
            log.error("FileServiceImpl.getFileBySpliceUrl error apath:{}", vo.getPath(), e);
            return null;
        }
    }

    @Override
    public byte[] pathDownLoad(String path, String ea) {
        if (StringUtils.isBlank(path)) {
            return null;
        }
        byte[] bytes = null;
        if (path.startsWith("N_")) {
            bytes = fileV2Manager.downloadNFile(path, ea, false);
        }
        return bytes;
    }

    @Override
    public Result<String> getFileUrlBySpliceUrl(GetFileBySpliceUrlVO vo) {
        String url = fileV2Manager.getUrlByPath(vo.getPath(), vo.getEa(), false);
        if (StringUtils.isBlank(url)) {
            log.warn("FileV2Manager.getFileByApathAndParam url is null path:{}", vo.getPath());
            return Result.newSuccess();
        }
        // 拼接参数
        if (StringUtils.isNotBlank(vo.getParam())) {
            url = url + vo.getParam();
        }

        return Result.newSuccess(url);
    }
    
    @Override
    public Result<UploadFileResult> uploadNFileByObject(UploadFileArg arg) {
        String ea = objectManager.getObjectEa(arg.getObjectId(), arg.getObjectType());
        if (StringUtils.isBlank(ea)) {
            log.warn("FileServiceImpl.uploadNFileByObject error ea is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        FileManagerPicResult fileManagerPicResult = fileV2Manager.uploadToNPath(arg.getFileBytes(), arg.getExt(), ea, null);
        if (fileManagerPicResult == null) {
            log.warn("FileServiceImpl.uploadNFileByObject error fileManagerPicResult is null");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        // 将path去除后缀
        UploadFileResult uploadFileResult = new UploadFileResult();
        uploadFileResult.setPath(fileV2Manager.getFileNameNoEx(fileManagerPicResult.getNPath()));
        uploadFileResult.setExt(arg.getExt());
        uploadFileResult.setSize(fileManagerPicResult.getSize());
        return Result.newSuccess(uploadFileResult);
    }

    @Override
    public Result<String> getNapathByApath(String ea, Integer fsUserId, String apath) {
        byte[] data = fileV2Manager.downloadAFile(apath, fsUserId, ea);
        if (data == null){
            return Result.newError(SHErrorCode.PIC_CONVERT_FAILED);
        }
        String npath = fileV2Manager.doUploadTempNFile(data, ea, fsUserId);
        return Result.newSuccess(npath);
    }

    @Override
    public byte[] redirectDownload(String path) {
        if (path.contains(host)){
            log.info("redirectDownload inner cloud path:{}", path);
            return fileV2Manager.downloadFileByUrl(path, null);
        }
        log.info("redirectDownload outer cloud path:{}", path);
        path = UnicodeFormatter.decodeUnicodeString(path);
        //从本地数据库中查找
        String fsPath = picFsMappingDAO.getFsPathByRawPicUrl(path);
        if (fsPath != null){
            //从文件服务下载图片&返回图片数据
            return fileManager.downloadAFile(pictureFsEa, fsPath);
        }

        byte[] picBytes = fileV2Manager.getByteDataByUrl(path);
        if (picBytes != null){
            if (picBytes.length > 10485760) {
                return new byte[0];
            }
            //上传文件服务器
            String ext = "jpg";
            if (path.contains("wx_fmt=gif")){
                ext = "gif";
            }else if (path.contains("wx_fmt=jpeg")){
                ext = "jpeg";
            }else if (path.contains("wx_fmt=png")){
                ext = "png";
            }
            fsPath = fileManager.uploadFileToAWarehouse(ext, picBytes);
            if (fsPath != null){
                PicFsMappingEntity entity = new PicFsMappingEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setRawPicUrl(path);
                entity.setFsPath(fsPath);
                entity.setSize(picBytes.length);
                picFsMappingDAO.insert(entity);
            }
        }
        return picBytes;
    }

    private void batchGetUrlbyPath(List<String> pathList, String ea, Map<String, GetUrlByPathUnitResult> resultMap, Boolean needPermanent) {
        Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(pathList, ea, false);
        if (null != urlMap) {
            Set<Map.Entry<String, String>> entry = urlMap.entrySet();
            Iterator<Map.Entry<String, String>> it = entry.iterator();
            while (it.hasNext()) {
                String key = it.next().getKey();
                String value = urlMap.get(key);

                if (BooleanUtils.isTrue(needPermanent)) {
                    String targetUrl = fileV2Manager.getSpliceUrl(ea, key);
                    if (StringUtils.isNotBlank(targetUrl)) {
                        value = targetUrl;
                    }
                }

                GetUrlByPathUnitResult unitResult = new GetUrlByPathUnitResult();
                unitResult.setPath(key);
                unitResult.setUrl(value);
                resultMap.put(key, unitResult);
            }
        }
    }

    @Override
    public void batchDownloadPicToZip(String dataListStr, String ea, Integer userId) {
        ThreadPoolUtils.executeWithTraceContext(() -> {
            List<BatchDownloadPicToZipArg> picInfoList;
            try {
                Type listType = new TypeToken<List<BatchDownloadPicToZipArg>>() {
                }.getType();
                picInfoList = GsonUtil.fromJson(dataListStr, listType);
            } catch (Exception e) {
                log.warn("FileController.batchDownloadPicToZip error e:{}", e);
                return;
            }
            if (CollectionUtils.isEmpty(picInfoList)) {
                return;
            }
            if (picInfoList.size() > 40) {
                log.warn("FileController.batchDownloadPicToZip error batchDownloadPicToZipArgs more than 40");
                return;
            }
            String logoPath = null;
            EnterpriseInfoEntity enterpriseInfoEntity = enterprseInfoDao.queryEnterpriseInfoByEa(ea);
            if (enterpriseInfoEntity != null && enterpriseInfoEntity.getDrawQrcodeElogo() != null && enterpriseInfoEntity.getDrawQrcodeElogo() == 1) {
                logoPath = enterpriseInfoEntity.getIconPath();
            }
            String downloadFilename = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_FILESERVICEIMPL_362) + ".zip";
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ZipOutputStream zos = null;
            try {
                zos = new ZipOutputStream(outputStream);
                for (BatchDownloadPicToZipArg arg : picInfoList) {
                    byte[] bytes;
                    if (StringUtils.isNotEmpty(logoPath)) {
                        bytes = downloadQrCodeManager.drawQrCode(arg.getPath(),logoPath,ea);
                    } else {
                        bytes = pathDownLoad(arg.getPath(), ea);
                    }
                    if (bytes != null) {
                        zos.putNextEntry(new ZipEntry(arg.getFileName() + ".jpg"));
                        zos.write(bytes);
                    }
                }
            } catch (Exception e) {
                log.warn("FileController.batchDownloadPicToZip error e:{}", e);
            } finally {
                if (zos != null) {
                    try {
                        zos.flush();
                        outputStream.flush();
                        zos.close();
                        pushSessionManager.pushFileToFileAssistant(outputStream, downloadFilename, ea, userId, "zip");
                    } catch (Exception e) {
                        log.warn("FileController.batchDownloadPicToZip error e:{}", e);
                    }
                }
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    @Override
    public Result<CreateTNFileFromAFileArgResult> createTNFileFromAFile(String ea, Integer fsUserId, String apath) {
        Optional<String> tnPathOpt = fileV2Manager.CreateTNFileFromAFile(ea, fsUserId, apath);
        if (!tnPathOpt.isPresent()){
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        CreateTNFileFromAFileArgResult result = new CreateTNFileFromAFileArgResult();
        String[] regs = StringUtils.split(apath, "\\." );
        result.setTnpath(tnPathOpt.get());
        result.setExt(regs[1]);
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> getCdnUrlBypath(String ea, Integer fsUserId, String path) {
        PhotoLibraryEntity photoLibraryEntity = photoLibraryDAO.getByPhotoPath(ea, path);
        if (photoLibraryEntity != null){
            if (photoLibraryEntity.getCdnUrl() != null){
                return Result.newSuccess(photoLibraryEntity.getCdnUrl());
            }else {
                //上传图片到cdn
                String cpath = fileV2Manager.getCpathByPath(ea, path);
                String url = null;
                if (cpath != null){
                    url = fileV2Manager.getUrlByCPath(ea, cpath);
                    photoLibraryDAO.updateCdnUrlById(ea, photoLibraryEntity.getId(), url);
                }
                return Result.newSuccess(url);
            }
        }

        PhotoSelectorEntity photoSelectorEntity = photoSelectorDAO.getByPhotoPath(ea, path);
        if (photoSelectorEntity != null){
            if (photoSelectorEntity.getCdnUrl() != null){
                return Result.newSuccess(photoSelectorEntity.getCdnUrl());
            }else {
                //上传图片到cdn
                String cpath = fileV2Manager.getCpathByPath(ea, path);
                String url = null;
                if (cpath != null){
                    url = fileV2Manager.getUrlByCPath(ea, cpath);
                    photoSelectorDAO.updateCdnUrlById(ea, photoSelectorEntity.getId(), url);
                }
                return Result.newSuccess(url);
            }
        }

        //针对海报
        QRPosterEntity entity = qrPosterDAO.getByEaAndPath(ea, path);
        if (entity != null){
            String cpath = fileV2Manager.getCpathByPath(ea, path);
            String url = fileV2Manager.getUrlByCPath(ea, cpath);
            return Result.newSuccess(url);
        }

        return Result.newError(SHErrorCode.FILE_NOT_FOUND);
    }

    @Override
    public Result<String> getCdnUrlByTcpath(String ea, String tcpath) {
        Map<String, String>  pathMap = fileV2Manager.changeListCWarehouseTempToPermanent(ea, Lists.newArrayList(tcpath));
        if (pathMap == null || pathMap.isEmpty()){
            return Result.newSuccess() ;
        }

        String url = fileV2Manager.getUrlByCPath(ea, pathMap.get(tcpath));
        return Result.newSuccess(url);
    }

    @Override
    public Result<UploadFileResult> uploadToCFile(UploadFileArg arg) {
        String path = null;
        String url = null;
        String ea = arg.getEa();
        if(StringUtils.isBlank(ea)){
            ea = objectManager.getObjectEa(arg.getObjectId(),arg.getObjectType());
        }
        if(StringUtils.isBlank(ea)){
            ea = this.pictureFsEa;
        }
        if (arg.getNeedCpath()!=null && arg.getNeedCpath()==1) {
            path = fileV2Manager.uploadToCpathOrNpath(ea, arg.getFileBytes(), true,arg.getFileName());
        } else {
            path = fileV2Manager.uploadToTCpathOrTNpath(ea, arg.getFileBytes(), true,arg.getFileName());
        }

        url = fileV2Manager.getUrlByCPath(ea,path);
        if (StringUtils.isBlank(path) || StringUtils.isBlank(url)) {
            return new Result<>(SHErrorCode.FILE_UPLOAD_FAILED);
        }

        UploadFileResult uploadFileResult = new UploadFileResult();
        uploadFileResult.setPath(path);
        uploadFileResult.setUrl(url);
        uploadFileResult.setExt(arg.getExt());
        uploadFileResult.setSize((long) arg.getFileBytes().length);
        uploadFileResult.setFileName(arg.getFileName());
        return Result.newSuccess(uploadFileResult);
    }

    @Override
    public Result<GenerateUploadFileOmitResult> generateUploadFileOmit(String ea, String resourceType, String filename, String extension, int fileSize) {

        Optional<GenerateUploadFileOmitResult> uploadFileOmitOpt = fileV2Manager.generateUploadFileOmit(ea, generateUploadFileOmitTime, resourceType, filename, extension, fileSize);
        if (!uploadFileOmitOpt.isPresent()) {
            return Result.newError(SHErrorCode.PIC_CONVERT_FAILED);
        }

        if ("hsykjt".equals(ea)) {
            // 何氏眼科对外域名替换
            uploadFileOmitOpt.get().setUrl(uploadFileOmitOpt.get().getUrl().replace("img-crm.hevision.com", "s-crm.hevision.com:37363"));
        }

        return Result.newSuccess(uploadFileOmitOpt.get());
    }

    @Override
    public Result<CPathMetaDataResult> getCPathMetaDataByTcpath(CPathMetaDataByTcpathArg arg) {
        StoneSaveFileFromTempFileResponse temp = fileV2Manager.getCPathInfoForTemp(arg.getEa(), arg.getTcpath());
        if (Objects.isNull(temp)) {
            return Result.newSuccess() ;
        }
        CPathMetaDataResult result = new CPathMetaDataResult();
        result.setCPath(temp.getPath());
        result.setEa(arg.getEa());
        result.setUrl(fileV2Manager.getUrlByCPath(arg.getEa(),temp.getPath()));
        result.setExt(temp.getExtensionName());
        result.setSize(temp.getSize());
        return Result.newSuccess(result);
    }

    @Override
    public Result<CdnUrlByCpathResult> getCurrentEaCpathByCpath(String ea, Integer fsUserId, String path) {
        Optional<Boolean> b = fileV2Manager.fileExistInCurrentTenant(ea, path);
        CdnUrlByCpathResult result = new CdnUrlByCpathResult();
        boolean flag = b.isPresent() && b.get();
        result.setStatus(flag ? 1 : 0);
        result.setUrl(fileV2Manager.getUrlByCPath(flag?ea:pictureFsEa, path));
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> sendFileMail(SendFileMailArg arg) {

        FileEntity file = fileLibraryDAO.getById(arg.getFileId());
        String name = file.getFileName();
        String ext = file.getExt();
        if (!name.endsWith(ext)) {
            name = file.getFileName() + "." + ext;
        }
        try {
            name = URLEncoder.encode(name,"UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String url = fileV2Manager.getDownloadFileUrlV2(file.getEa(), file.getFilePath(),name ,15);
        SendEmailArg sendEmailArg = new SendEmailArg();
        sendEmailArg.setEa(file.getEa());
        sendEmailArg.setAccountType(MailApiUserTypeEnum.TRIGGER.getType());
        sendEmailArg.setSenderIds(arg.getSenderIds());
        sendEmailArg.setTitle(arg.getTitle());
        String content = arg.getContent();
        content = ReplaceUtil.replaceFileDownLoadUrl(content, url);
        content = ReplaceUtil.replaceFileName(content, file.getFileName());
        sendEmailArg.setContent(content);
        sendEmailArg.setMailList(arg.getMailList());
        sendEmailArg.setTaskId(UUIDUtil.getUUID());
        Result<SendEmailResult> result = mailManager.sendEmailWithOutSaveDetailV2(sendEmailArg);
        if (result.isSuccess()) {
            String taskId = saveEmailSendRecord(arg.getEa(), file.getFileName(), arg.getMailList(), result.getData());
//            //新增埋点
//            if(StringUtils.isNotBlank(arg.getUserMarketingId())){
//                createRecord(taskId, file.getEa(),arg.getUserMarketingId());
//            }
        }
        return Result.newSuccess();
    }

//    private void createRecord(String taskId, String ea,String userMarketingId) {
//        MarketingUserActionEvent marketingUserActionEvent = new MarketingUserActionEvent();
//        marketingUserActionEvent.setActionType(ActionTypeEnum.SEND_FILE_MAIL.getAction());
//        marketingUserActionEvent.setChannelType(MarketingUserActionChannelType.EMAIL.getChannelType());
//        marketingUserActionEvent.setActionTime(System.currentTimeMillis());
//        marketingUserActionEvent.setObjectId(taskId);
//        marketingUserActionEvent.setEa(ea);
//        marketingUserActionEvent.setObjectType(ObjectTypeEnum.MAIL_TASK.getType());
//        Map<String, Object> extensionParams = Maps.newHashMap();
//        extensionParams.put(RecordActionArg.SPREAD_CHANNEL_KEY, SpreadChannelEnum.EMAIL.getCode());
//        extensionParams.put(RecordActionArg.MARKETING_SCENE_KEY, MarketingSceneEnum.OTHER.getCode());
//        extensionParams.put("userMarketingId", userMarketingId);
//        marketingUserActionEvent.setExtensionParams(extensionParams);
//        marketingRecordActionSender.send(marketingUserActionEvent);
//    }


    public String saveEmailSendRecord(String ea,  String fileName, List<String> mailList, SendEmailResult result) {
        MailSendTaskEntity taskEntity = result.getTaskEntity();
        taskEntity.setSendRange(MailSendRangeEnum.MARKETING_USER_GROUP.getType());
        taskEntity.setScheduleType(MailScheduleTypeEnum.IMMEDIATELY_SEND.getType());
        taskEntity.setFixTime(new Date().getTime());
        taskEntity.setSendStatus(MailSendStatusEnum.SEND_SUCCESS.getStatus());
        taskEntity.setMailType(MailApiUserTypeEnum.TRIGGER.getType());
        taskEntity.setCreateTime(new Date());
        taskEntity.setUpdateTime(new Date());
        taskEntity.setLabelId(result.getLabelId());
        taskEntity.setFsUserId(-10000);
        taskEntity.setTotalSendCount(mailList.size());
        taskEntity.setMarketingEventId("");
        taskEntity.setToUser(mailList.get(0));
        mailSendTaskDAO.insert(taskEntity);
        ThreadPoolUtils.execute(() -> {
            saveEmailSendRecordTask(ea, fileName, mailList, result, taskEntity);
        }, ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
        return taskEntity.getId();
    }

    private void saveEmailSendRecordTask(String ea, String fileName, List<String> mailList, SendEmailResult result, MailSendTaskEntity taskEntity) {
        //保存发送结果
        if (CollectionUtils.isNotEmpty(result.getMailIdList())) {
            List<MailSendTaskResultEntity> entities = Lists.newArrayList();
            for (String mailId : result.getMailIdList()) {
                MailSendTaskResultEntity entity = new MailSendTaskResultEntity();
                entity.setId(UUIDUtil.getUUID());
                entity.setEa(taskEntity.getEa());
                entity.setTaskId(taskEntity.getId());
                entity.setMailId(mailId);
                String[] mailRex = StringUtils.split(mailId, "$");
                if (mailRex != null && mailRex.length == 2) {
                    entity.setMail(mailRex[1]);
                }
                entity.setType(MailSendTypeEnum.SEND_BY_XSMTPAPI.getType());
                Date now = new Date();
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
                entities.add(entity);
            }
            mailSendTaskResultDAO.batchInsert(entities);
        }

        for (String receiver : mailList) {
            CreateEmailSendRecordDetailObjArg createArg = new CreateEmailSendRecordDetailObjArg();
            createArg.setEa(ea);
            createArg.setTaskId(taskEntity.getId());
            createArg.setReceiver(receiver);
            createArg.setSendTime(new Date());
            createArg.setPreviewUrl(host + "/XV/UI/Home#/app/marketing/index/=/mail-marketing/preview?runtime=desktop&mailId=" + taskEntity.getId());
            createArg.setEa(createArg.getEa());
            createArg.setFsUserId(taskEntity.getFsUserId());
            createArg.setBusinessType("文件中心");
            createArg.setSendObject(fileName);
            createArg.setSendNode(taskEntity.getSubject());
            emailSendRecordDetailObjManager.tryCreateOrUpdateObj(createArg);
        }
    }


    @Override
    public Result<FilePreviewResult> getPreviewUrlV2(GetPreviewUrlArg arg) {
        FileEntity file = fileLibraryDAO.getById(arg.getId());
        String url = fileV2Manager.getPreviewUrl(file.getEa(), file.getFilePath());
        FilePreviewResult result = new FilePreviewResult();
        result.setPreviewUrl(url);
        return Result.newSuccess(result);
    }

    @Override
    public Result<GetDownLoadResult> getDownLoadUrlV2(GetDownLoadUrlArg arg) {
        FileEntity file = fileLibraryDAO.getById(arg.getId());
        String name = file.getFileName();
        String ext = file.getExt();
        if (!name.endsWith(ext)) {
            name = file.getFileName() + "." + ext;
        }
        try {
            name = URLEncoder.encode(name,"UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String url = fileV2Manager.getDownloadFileUrlV2(file.getEa(), file.getFilePath(), name,15);
        GetDownLoadResult result = new GetDownLoadResult();
        result.setDownLoadUrl(url);
        return Result.newSuccess(result);
    }
}