package com.facishare.marketing.provider.service.sms;

import com.facishare.marketing.api.arg.sms.CalcQuotaForSpendingQuotaArg;
import com.facishare.marketing.api.arg.sms.QueryConsumptionInfoArg;
import com.facishare.marketing.api.arg.sms.QueryPurchaseRecordArg;
import com.facishare.marketing.api.arg.sms.QuerySpendingQuotaInfoArg;
import com.facishare.marketing.api.result.sms.CalcForSpendingQuotaResult;
import com.facishare.marketing.api.result.sms.QueryConsumptionInfoResult;
import com.facishare.marketing.api.result.sms.QueryPurchaseRecordResult;
import com.facishare.marketing.api.result.sms.QuotaStatisticsResult;
import com.facishare.marketing.api.service.sms.QuotaService;
import com.facishare.marketing.common.enums.sms.ChannelTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsSendDao;
import com.facishare.marketing.provider.entity.sms.mw.SmsFeeStatisticEntity;
import com.facishare.marketing.provider.innerArg.mw.CreateSendTaskArg;
import com.facishare.marketing.provider.manager.sms.QuotaManager;
import com.facishare.marketing.provider.manager.sms.SmsTemplateManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Created by zhengh on 2018/12/21.
 */
@Service("quotaService")
@Slf4j
public class QuotaServiceImpl implements QuotaService{
    @Autowired
    private QuotaManager quotaManager;

    @Autowired
    private MwSmsSendDao smsSendDao;

    @Autowired
    private SmsTemplateManager smsTemplateManager;

    @Override
    public Result<QuotaStatisticsResult> queryStatistics(String ea) {
        return quotaManager.queryStatistics(ea);
    }

    @Override
    public Result<PageResult<QueryPurchaseRecordResult>> queryPurchaseRecord(QueryPurchaseRecordArg arg) {
        return quotaManager.queryPurchaseRecord(arg);
    }


    @Override
    public Result<CalcForSpendingQuotaResult> calcForSpendingQuota(CalcQuotaForSpendingQuotaArg arg) {
        if(arg.isParamError()){
            log.warn("QuotaServiceImpl.calcForSpendingQuota tapath and smsSendId  wrong,  arg:{}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        CreateSendTaskArg taskArg = new CreateSendTaskArg();
        taskArg.setEa(arg.getEa());
        taskArg.setTemplateId(arg.getTemplateId());
        taskArg.setTemplateId(smsTemplateManager.objIdConvertToDbId(arg.getEa(), arg.getTemplateId()));
        taskArg.setPhones(arg.getPhones());
        taskArg.setTaPath(arg.getTaPath());
        taskArg.setTemplateContent(arg.getTemplateContent());
        taskArg.setSmsSendId(arg.getSmsSendId());
        taskArg.setUserGroupIds(arg.getUserGroupIds());
        taskArg.setCampaignIds(arg.getCampaignIds());
        taskArg.setConferenceInviteIds(arg.getConferenceInviteIds());
        taskArg.setFilterNDaySentUser(arg.getFilterNDaySentUser());
        taskArg.setChannelType(ChannelTypeEnum.MARKETING.getType());
        taskArg.setMarketingEventId(arg.getMarketingEventId());
        taskArg.setIgnoreErrorPhone(arg.isIgnoreErrorPhone());
        taskArg.setFilterReceived(arg.isFilterReceived());
        return quotaManager.calcSpendingQuotaInfo(taskArg);
    }

    @Override
    public Result<CalcForSpendingQuotaResult> querySpendingQuotaInfo(QuerySpendingQuotaInfoArg arg) {
        if (StringUtils.isEmpty(arg)) {
            log.info("QuotaServiceImpl querySpendingQuotaInfo arg is error, arg={}", arg);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        return quotaManager.querySpendingQuotaInfo(arg.getSmsSendId());
    }

    @Override
    public Result<QueryConsumptionInfoResult> queryConsumptionInfo(QueryConsumptionInfoArg arg) {
        QueryConsumptionInfoResult result = new QueryConsumptionInfoResult();
        List<SmsFeeStatisticEntity> statisticEntities = smsSendDao.querySmsFeeStatisticV2(arg.getEa(), arg.getStartDate(), arg.getEndDate());
        if (CollectionUtils.isEmpty(statisticEntities)) {
            result.setConsumeTotal(0);
        } else {
            int total = 0;
            List<QueryConsumptionInfoResult.ConsumptionStat> consumeList = Lists.newArrayList();
            QueryConsumptionInfoResult.ConsumptionStat marketingStat = new QueryConsumptionInfoResult.ConsumptionStat(); //营销通的数据统计需要汇总
            marketingStat.setConsumeCnt(0);
            marketingStat.setChannelType(ChannelTypeEnum.MARKETING.getType());
            marketingStat.setChannelName(ChannelTypeEnum.MARKETING.getName());
            consumeList.add(marketingStat);
            for (SmsFeeStatisticEntity entity : statisticEntities) {
                total += entity.getTotalFee();
                if (ChannelTypeEnum.isMarketingType(entity.getChannelType())) {
                    marketingStat.setConsumeCnt(marketingStat.getConsumeCnt() + entity.getTotalFee());
                } else {
                    QueryConsumptionInfoResult.ConsumptionStat consumptionStat = new QueryConsumptionInfoResult.ConsumptionStat();
                    consumptionStat.setChannelType(entity.getChannelType());
                    consumptionStat.setConsumeCnt(entity.getTotalFee());
                    consumptionStat.setChannelName(ChannelTypeEnum.getChannelType(entity.getChannelType()).getName());
                    consumeList.add(consumptionStat);
                }
            }
            result.setConsumeTotal(total);
            result.setConsumeList(consumeList);
        }
        return Result.newSuccess(result);
    }

}
