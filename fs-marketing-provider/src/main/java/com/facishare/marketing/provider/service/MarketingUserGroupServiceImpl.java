package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountDataInUserGroup;
import com.facishare.marketing.api.result.*;
import com.facishare.marketing.api.service.MarketingUserGroupService;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.MarketingUserGroupDataRangeEnum;
import com.facishare.marketing.common.contstant.MarketingUserGroupOperationTypeEnum;
import com.facishare.marketing.common.contstant.MarketingUserSearchTypeEnum;
import com.facishare.marketing.common.dto.IdName;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dto.GroupNameObjectIdDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: dongzhb
 * @date: 2019/1/31
 * @Description:
 */
@Slf4j
@Service("marketingUserGroupService")
public class MarketingUserGroupServiceImpl implements MarketingUserGroupService {
    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;
    @Autowired
    private MarketingUserGroupManager marketingUserGroupManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingUserGroupToUserRelationDao marketingUserGroupToUserRelationDao;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private BoardToMarketingUserGroupRelationDao boardToMarketingUserGroupRelationDao;
    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @ReloadableProperty("max_auto_calculate_group")
    private Integer maxAutoCalculateGroup;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private MarketingGroupUserActionDAO marketingGroupUserActionDAO;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private DataPermissionManager dataPermissionManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private LicenseManager licenseManager;

    @ReloadableProperty("recheck_user_group_ea")
    private String recheckUserGroupEa;

    private final String DYNAMIC_TARGET_NUMBER_APP = "marketing_strategy_dynamic_target_number_app";

    @Override
    public Result<PageResult<MarketingUserGroupResult>> listMarketingUserGroup(String ea, Integer fsUserId, ListMarketingUserGroupArg vo) {
        Preconditions.checkNotNull(ea, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        if (StringUtils.isEmpty(vo.getNameKey())) {
            vo.setNameKey(null);
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(vo.getGroupId())) {
            vo.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        }
        List<MarketingUserGroupEntity> marketingUserGroupEntityList = Lists.newArrayList();
        if (StringUtils.isEquals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), vo.getGroupId())) {
            marketingUserGroupEntityList = marketingUserGroupDao.createByMePage(ea, fsUserId, page, vo.getNameKey(), vo.getStatus());
        } else if (StringUtils.isEquals(DefaultObjectGroupEnum.NO_GROUP.getId(), vo.getGroupId())) {
            marketingUserGroupEntityList = marketingUserGroupDao.noGroupPage(ea, page, vo.getNameKey(), vo.getStatus());
        } else if (StringUtils.isEquals(DefaultObjectGroupEnum.ALL.getId(), vo.getGroupId())) {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.USER_MARKETING_GROUP.getType());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            marketingUserGroupEntityList = marketingUserGroupDao.getAccessiblePage(ea, fsUserId, permissionGroupIdList, null, page, vo.getNameKey(), vo.getStatus());
        } else {
            // 获取有权限查看的分组
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, fsUserId, ObjectTypeEnum.USER_MARKETING_GROUP.getType());
            List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(permissionGroupIdList) && permissionGroupIdList.contains(vo.getGroupId())) {
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.USER_MARKETING_GROUP.getType(), vo.getGroupId(), objectGroupEntityList);
                accessibleSubGroupIdList.add(vo.getGroupId());
                marketingUserGroupEntityList = marketingUserGroupDao.getAccessiblePage(ea, fsUserId,
                        permissionGroupIdList, accessibleSubGroupIdList, page, vo.getNameKey(), vo.getStatus());
            }
        }
        //List<MarketingUserGroupEntity> marketingUserGroupEntityList = marketingUserGroupDao.listMarketingUserGroup(ea, page, vo.getNameKey());
        List<MarketingUserGroupResult> marketingUserGroupResults = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(marketingUserGroupEntityList)) {
            Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = null;
            Set<Integer> creatorIds = marketingUserGroupEntityList.stream().map(MarketingUserGroupEntity::getCreateBy).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(creatorIds)){
                fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, new ArrayList<>(creatorIds), false);
            }
            if (fsEmployeeMsgMap == null) {
                fsEmployeeMsgMap = new HashMap<>();
            }
            for (MarketingUserGroupEntity entity : marketingUserGroupEntityList){
                MarketingUserGroupResult marketingUserGroupResult = BeanUtil.copy(entity, MarketingUserGroupResult.class);
                marketingUserGroupResult
                        .setCreateByName(fsEmployeeMsgMap.get(marketingUserGroupResult.getCreateBy()) == null ? "" : fsEmployeeMsgMap.get(marketingUserGroupResult.getCreateBy()).getName());
                marketingUserGroupResult.setRuleGroupJson(entity.getRuleGroupJson());
                marketingUserGroupResult.setCreateTime(DateUtil.getTimeStamp(entity.getCreateTime()));
                marketingUserGroupResult.setCalculationTime(DateUtil.getTimeStamp(entity.getCalculationTime()));
                marketingUserGroupResult.markCanManualUpdate();
                marketingUserGroupResults.add(marketingUserGroupResult);
            }
        }
        PageResult<MarketingUserGroupResult> pageResult = new PageResult<>();
        pageResult.setResult(marketingUserGroupResults);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(page.getTotalNum());
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<List<MarketingUserGroupResultV2>> listAllMarketingUserGroupV2(String ea, Integer fsUserId, Boolean includeNotEnabled) {

        Integer status = null;
        if (includeNotEnabled != null && !includeNotEnabled) {
            status = 1;
        }
        int count = marketingUserGroupDao.queryCountByStatus(ea, status);
        List<MarketingUserGroupResultV2> resultList = Lists.newArrayList();
        if (count <= 0) {
            return Result.newSuccess(resultList);
        }

        ListMarketingUserGroupArg arg = new ListMarketingUserGroupArg();
        arg.setPageNum(1);
        arg.setPageSize(count);
        arg.setStatus(status);
        arg.setGroupId(DefaultObjectGroupEnum.ALL.getId());
        Result<PageResult<MarketingUserGroupResult>> pageResult = listMarketingUserGroup(ea, fsUserId, arg);
        List<MarketingUserGroupResult> marketingUserGroupResultList = pageResult.getData().getResult();
        if (CollectionUtils.isEmpty(marketingUserGroupResultList)) {
            return Result.newSuccess(resultList);
        }
        List<String> marketingUserGroupIdList = marketingUserGroupResultList.stream().map(MarketingUserGroupResult::getId).collect(Collectors.toList());
        Map<String, GroupNameObjectIdDTO> objectIdDTOMap = Maps.newHashMap();
        Map<String, List<GroupNameObjectIdDTO>> groupIdDTOMap = Maps.newHashMap();
        List<GroupNameObjectIdDTO> groupNameObjectIdDTOList = objectGroupRelationDAO.queryGroupNameByObjectIds(ea, ObjectTypeEnum.USER_MARKETING_GROUP.getType(), marketingUserGroupIdList);

        if (CollectionUtils.isNotEmpty(groupNameObjectIdDTOList)) {
            groupNameObjectIdDTOList.forEach(e -> objectIdDTOMap.put(e.getObjectId(), e));
            groupIdDTOMap = groupNameObjectIdDTOList.stream().collect(Collectors.groupingBy(GroupNameObjectIdDTO::getGroupId));
        }

        for (DefaultObjectGroupEnum value : DefaultObjectGroupEnum.values()) {
            MarketingUserGroupResultV2 resultV2 = new MarketingUserGroupResultV2();
            resultV2.setGroupName(value.getName());
            resultV2.setGroupId(value.getId());
            resultV2.setIsVisible(1);
            List<MarketingUserGroupResult> dataList = Lists.newArrayList();
            if (value == DefaultObjectGroupEnum.ALL) {
                continue;
            } else if (value == DefaultObjectGroupEnum.CREATED_BY_ME) {
                List<MarketingUserGroupResult> tempList = marketingUserGroupResultList.stream().filter(e -> e.getCreateBy().equals(fsUserId)).collect(Collectors.toList());
                dataList.addAll(tempList);
            } else {
                List<MarketingUserGroupResult> tempList = marketingUserGroupResultList.stream().filter(e -> !objectIdDTOMap.containsKey(e.getId())).collect(Collectors.toList());
                dataList.addAll(tempList);
            }
            resultV2.setDataList(dataList);
            resultList.add(resultV2);
        }
        Map<String, MarketingUserGroupResult> marketingUserGroupIdMap = marketingUserGroupResultList.stream().collect(Collectors.toMap(MarketingUserGroupResult::getId, e -> e, (v1, v2) -> v1));

        ObjectGroupListResult objectGroupListResult = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.USER_MARKETING_GROUP.getType(), null, null);
        List<ListObjectGroupResult> accessibleObjectGroupList = objectGroupListResult.getObjectGroupList();
        if (CollectionUtils.isNotEmpty(accessibleObjectGroupList)) {
            for (ListObjectGroupResult result : accessibleObjectGroupList) {
                MarketingUserGroupResultV2 resultV2 = new MarketingUserGroupResultV2();
                resultV2.setGroupName(result.getGroupName());
                resultV2.setGroupId(result.getGroupId());
                resultV2.setParentId(result.getParentId());
                resultV2.setIsVisible(result.getIsVisible());
                resultV2.setLevel(result.getLevel());
                resultV2.setSeqNo(result.getSeqNo());
                List<GroupNameObjectIdDTO> dtoList = groupIdDTOMap.get(result.getGroupId());
                List<MarketingUserGroupResult> dataList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(dtoList)) {
                    dtoList.forEach(e -> {
                        if (marketingUserGroupIdMap.containsKey(e.getObjectId())) {
                            dataList.add(marketingUserGroupIdMap.get(e.getObjectId()));
                        }
                    });
                }
                resultV2.setDataList(dataList);
                resultList.add(resultV2);
            }
        }
        return Result.newSuccess(resultList);
    }


    @Override
    public Result<List<MarketingUserGroupResult>> listAllMarketingUserGroup(String ea, Integer fsUserId, Boolean includeNotEnabled) {
        Preconditions.checkNotNull(ea, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        List<MarketingUserGroupEntity> marketingUserGroupEntityList;
        if (BooleanUtils.isTrue(includeNotEnabled)){
            marketingUserGroupEntityList = marketingUserGroupDao.listAllMarketingUserGroupByEa(ea);
        }else {
            marketingUserGroupEntityList = marketingUserGroupDao.listMarketingUserGroupByEaAndStatus(ea, MarketingUserGroupStatusEnum.OPEN.getStatus());
        }
        List<MarketingUserGroupResult> marketingUserGroupResults = BeanUtil.copy(marketingUserGroupEntityList, MarketingUserGroupResult.class);
        marketingUserGroupResults.forEach(MarketingUserGroupResult::markCanManualUpdate);
        return Result.newSuccess(marketingUserGroupResults);
    }

    @Override
    public Result<GetMarketingUserGroupDeatilResult> getMarketingUserGroupDeatil(String ea, Integer fsUserId, String id) {
        MarketingUserGroupEntity entity = marketingUserGroupDao.getMarketingUserGroupDeatail(ea, id);
        GetMarketingUserGroupDeatilResult getMarketingUserGroupDeatilResult = BeanUtil.copy(entity, GetMarketingUserGroupDeatilResult.class);
        if (StringUtils.isNotEmpty(entity.getDataRange())) {
            List<Integer> dataRange = JSON.parseArray(entity.getDataRange(), Integer.class);
            getMarketingUserGroupDeatilResult.setDataRange(dataRange);
        }
        List<Integer> fsUserIds = Lists.newArrayList();
        fsUserIds.add(entity.getCreateBy());
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, false);
        getMarketingUserGroupDeatilResult
            .setCreateByName(fsEmployeeMsgMap.get(getMarketingUserGroupDeatilResult.getCreateBy()) == null ? "" : fsEmployeeMsgMap.get(getMarketingUserGroupDeatilResult.getCreateBy()).getName());
        getMarketingUserGroupDeatilResult.setCreateTime(DateUtil.getTimeStamp(entity.getCreateTime()));
        getMarketingUserGroupDeatilResult.setCalculationTime(DateUtil.getTimeStamp(entity.getCalculationTime()));
        getMarketingUserGroupDeatilResult.markCanManualUpdate();
        List<BoardEntity> boardEntities = boardToMarketingUserGroupRelationDao.listBoardByMarketingUserGroupIdAndFsUserId(ea, fsUserId, id);
        List<IdName> boards = boardEntities.stream().map(board -> {
            IdName idName = new IdName();
            idName.setId(board.getId());
            idName.setName(board.getName());
            return idName;
        }).collect(Collectors.toList());
        getMarketingUserGroupDeatilResult.setBoards(boards);
        return new Result<>(SHErrorCode.SUCCESS, getMarketingUserGroupDeatilResult);
    }

    private Map<Integer, FSEmployeeMsg> getEmployeeInfoMap(String ea, Integer fsUserId) {
        return fsAddressBookManager.getEmployeeInfoByEa(ea);
    }

    @Override
    public Result<GetMarketingUserGroupStatisticsResult> getMarketingUserGroupStatistics(String ea, Integer fsUserId, String id) {
        MarketingUserGroupEntity entity = marketingUserGroupDao.getMarketingUserGroupDeatail(ea, id);
        GetMarketingUserGroupStatisticsResult getMarketingUserGroupStatisticsResult = BeanUtil.copy(entity, GetMarketingUserGroupStatisticsResult.class);
        return new Result<>(SHErrorCode.SUCCESS, getMarketingUserGroupStatisticsResult);
    }

    @Override
    public Result<AddMarketingUserGroupResult> addMarketingUserGroup(String ea, Integer fsUserId, AddMarketingUserGroupArg arg) {
        Preconditions.checkArgument(MarketingUserGroupTypeEnum.isValid(arg.getType()));
        if (MarketingUserGroupTypeEnum.DYNAMIC.getType().equals(arg.getType())){
            Preconditions.checkArgument(MarketingUserGroupCalculationPeriodEnum.isValid(arg.getCalculationPeriod()));
            Preconditions.checkArgument(MarketingUserSearchTypeEnum.isValid(arg.getSearchType()));
            if (MarketingUserSearchTypeEnum.BY_TAG.getSearchType() == arg.getSearchType()){
                Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getTagOperator()) && arg.getTagNames() != null && !arg.getTagNames().isEmpty());
                if (BooleanUtils.isTrue(arg.getExcludeTags())){
                    Preconditions.checkArgument(arg.getExcludeTagNames() != null && !arg.getExcludeTagNames().isEmpty());
                }
            } else if (MarketingUserSearchTypeEnum.BY_CUSTOMER_RELATION.getSearchType() == arg.getSearchType()) {
                List<Integer> dataRange = arg.getDataRange();
                if (CollectionUtils.isEmpty(dataRange)) {
                    return Result.newError(SHErrorCode.DATA_RANGE_CANT_NULL);
                }
                for (Integer code : dataRange) {
                    if (MarketingUserGroupDataRangeEnum.getByCode(code) == null) {
                        return Result.newError(SHErrorCode.DATA_RANGE_PARAM_ERROR);
                    }
                }
            } else {
                List<FilterData> filters = arg.getRuleGroupJson().stream().map(val -> BeanUtil.copyByGson(val, FilterData.class)).collect(Collectors.toList());
                Preconditions.checkArgument(this.checkFilterDatas(filters));
            }
            if (!MarketingUserGroupCalculationPeriodEnum.MANUAL.getCalculationPeriod().equals(arg.getCalculationPeriod())){
                Integer count = marketingUserGroupDao.countAutoCalculateMarketingUserGroupByEa(ea, null);
                if (count >= getQuota(ea)){
                    return Result.newError(SHErrorCode.AUTO_CALCULATE_GROUP_REACH_LIMIT);
                }
            }
        }
        boolean nameExisted = !marketingUserGroupDao.listByEaAndName(ea, arg.getName()).isEmpty();
        if(nameExisted){
            return Result.newError(SHErrorCode.USER_GROUP_NAME_DUPLICATED);
        }

        MarketingUserGroupEntity entity = new MarketingUserGroupEntity();
        BeanUtils.copyProperties(arg, entity);
        entity.setId(UUIDUtil.getUUID());
        entity.setRuleGroupJson(arg.getRuleGroupJson());
        entity.setEa(ea);
        entity.setCreateBy(fsUserId);
        entity.setCreateTime(new Date());
        int initStatisticDataCount = 0;
        if (MarketingUserGroupTypeEnum.DYNAMIC.getType().equals(arg.getType())){
            entity.setCalculationStatus(MarketingUserGroupCalculationStatusEnum.CALCULATING.getType());
            initStatisticDataCount = -1;
        } else{
            entity.setCalculationStatus(MarketingUserGroupCalculationStatusEnum.CALCULATION_SUCCESSFUL.getType());
            entity.setCalculationFailReason("success");
            entity.setCalculationTime(new Date());
        }
        entity.setUserNumber(initStatisticDataCount);
        entity.setWxFansNumber(initStatisticDataCount);
        entity.setUserPhoneNumber(initStatisticDataCount);
        entity.setWxWorkExternalUserNumber(initStatisticDataCount);
        entity.setCrmLeadCount(initStatisticDataCount);
        entity.setCrmCustomerCount(initStatisticDataCount);
        entity.setCrmContactCount(initStatisticDataCount);
        entity.setDataRange(GsonUtil.toJson(arg.getDataRange()));
        entity.setStatus(MarketingUserGroupStatusEnum.OPEN.getStatus());
        if(dataPermissionManager.getNewDataPermissionSetting(ea)){
            List<Integer> fsAccessibleDepartmentIds = dataPermissionManager.getFsAccessibleDepartmentIds(ea, fsUserId, false);
            entity.setPermissionRange(GsonUtil.toJson(fsAccessibleDepartmentIds));
        }
        boolean addSuccess = marketingUserGroupDao.add(entity);
        if (addSuccess){
            marketingUserGroupManager.insertOperationRecord(ea, entity.getId(), fsUserId, MarketingUserGroupOperationTypeEnum.CREATE.getOperationType(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_372));
            //人群计算监控
            initMarketingGroupUserActionRecord(ea, fsUserId, entity.getId(), MarketingGroupUserActionTriggerTypeEnum.TRIGGER_BY_EMPLOYEE.getType());
        }
        log.info("MarketingUserGroupEntity[{}] ,status[{}]", entity, addSuccess);
        //营销用户群组创建成功,调用群组计算规则。
        if (addSuccess && MarketingUserGroupTypeEnum.DYNAMIC.getType().equals(arg.getType())) {
            try {
                marketingUserGroupManager.asyncCalculateUserMarketingAccountData(ea, fsUserId, entity.getId());
            } catch (Exception ex) {
                log.error("calculateUserMarketingAccountData error :{}", ex);
                marketingGroupUserActionDAO.updateStatusByMarketingGroupId(ea, entity.getId(), MarketingUserGroupCalculationStatusEnum.CALCULATION_FAILED.getType());
            }
        }

        objectGroupManager.setGroup(ea, fsUserId, ObjectTypeEnum.USER_MARKETING_GROUP.getType(), Collections.singletonList(entity.getId()), arg.getGroupId());
        return Result.newSuccess(new AddMarketingUserGroupResult(addSuccess, entity.getId()));
    }

    public void initMarketingGroupUserActionRecord(String ea, Integer fsUserId, String marketingGroupId, int triggerType) {
        MarketingGroupUserActionEntity marketingGroupUserActionEntity = marketingGroupUserActionDAO.getByMarketingGroupId(ea, marketingGroupId);
        if (marketingGroupUserActionEntity != null) {
            marketingGroupUserActionEntity.setStatus(MarketingUserGroupCalculationStatusEnum.CALCULATING.getType());
            marketingGroupUserActionEntity.setTriggerType(triggerType);
            marketingGroupUserActionEntity.setCurrentCalculateUser(0);
            marketingGroupUserActionEntity.setTotalMarketingUser(0);
            marketingGroupUserActionDAO.updateById(marketingGroupUserActionEntity);
        }else {
            marketingGroupUserActionEntity = new MarketingGroupUserActionEntity();
            marketingGroupUserActionEntity.setId(UUIDUtil.getUUID());
            marketingGroupUserActionEntity.setMarketingGroupId(marketingGroupId);
            marketingGroupUserActionEntity.setEa(ea);
            marketingGroupUserActionEntity.setStatus(MarketingUserGroupCalculationStatusEnum.CALCULATING.getType());
            marketingGroupUserActionEntity.setTriggerType(triggerType);
            marketingGroupUserActionEntity.setUserId(fsUserId);
            marketingGroupUserActionDAO.insert(marketingGroupUserActionEntity);
        }
    }

    private boolean checkFilterDatas(List< FilterData > filters) {
        if (filters == null || filters.isEmpty()){
            return false;
        }
        for (FilterData filterData : filters) {
            if (filterData.getQuery().getFilters() == null || filterData.getQuery().getFilters().isEmpty()){
                return false;
            }
            for (Filter filter : filterData.getQuery().getFilters()) {
                if (Strings.isNullOrEmpty(filter.getFieldName()) || com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(filter.getFieldValues()) || Strings.isNullOrEmpty(filter.getOperator())) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public Result<UpdateMarketingUserGroupResult> updateMarketingUserGroup(String ea, Integer fsUserId, UpdateMarketingUserGroupArg arg) {
        MarketingUserGroupEntity marketingUserGroup = marketingUserGroupDao.getById(arg.getId());
        Preconditions.checkArgument(marketingUserGroup != null);
        if (!MarketingUserGroupTypeEnum.STATIC.getType().equals(marketingUserGroup.getType())){
            Preconditions.checkArgument(MarketingUserGroupCalculationPeriodEnum.isValid(arg.getCalculationPeriod()));
            Preconditions.checkArgument(MarketingUserSearchTypeEnum.isValid(arg.getSearchType()));
            if (MarketingUserSearchTypeEnum.BY_TAG.getSearchType() == arg.getSearchType()){
                Preconditions.checkArgument(arg.getTagNames() != null && !arg.getTagNames().isEmpty());
            } else if (MarketingUserSearchTypeEnum.BY_CUSTOMER_RELATION.getSearchType() == arg.getSearchType()) {
                List<Integer> dataRange = arg.getDataRange();
                if (CollectionUtils.isEmpty(dataRange)) {
                    return Result.newError(SHErrorCode.DATA_RANGE_CANT_NULL);
                }
                for (Integer code : dataRange) {
                    if (MarketingUserGroupDataRangeEnum.getByCode(code) == null) {
                        return Result.newError(SHErrorCode.DATA_RANGE_PARAM_ERROR);
                    }
                }
            } else {
                List<FilterData> filters = arg.getRuleGroupJson().stream().map(val -> BeanUtil.copyByGson(val, FilterData.class)).collect(Collectors.toList());
                Preconditions.checkArgument(this.checkFilterDatas(filters));
            }
            if (MarketingUserGroupStatusEnum.OPEN.getStatus().equals(marketingUserGroup.getStatus()) && !MarketingUserGroupCalculationPeriodEnum.MANUAL.getCalculationPeriod().equals(arg.getCalculationPeriod())){
                Integer count = marketingUserGroupDao.countAutoCalculateMarketingUserGroupByEa(ea, arg.getId());
                if (count >= getQuota(ea)){
                    return Result.newError(SHErrorCode.AUTO_CALCULATE_GROUP_REACH_LIMIT);
                }
            }
        }

        MarketingUserGroupEntity entity = BeanUtil.copy(arg, MarketingUserGroupEntity.class);
        entity.setRuleGroupJson(arg.getRuleGroupJson());
        entity.setEa(ea);
        entity.setType(marketingUserGroup.getType());
        entity.setDataRange(GsonUtil.toJson(arg.getDataRange()));
        boolean updateSuccess = marketingUserGroupDao.update(entity);
        if (updateSuccess){
            marketingUserGroupManager.insertOperationRecord(ea, arg.getId(), fsUserId, MarketingUserGroupOperationTypeEnum.EDIT.getOperationType(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_466));
        }
        //营销用户群组创建成功,调用群组计算规则。
        if (updateSuccess && !MarketingUserGroupTypeEnum.STATIC.getType().equals(marketingUserGroup.getType())) {
            try {
                marketingUserGroupManager.asyncCalculateUserMarketingAccountData(ea, fsUserId, entity.getId());
            } catch (Exception ex) {
                log.error("calculateUserMarketingAccountData error :{}", ex);
            }
        }
        return Result.newSuccess(new UpdateMarketingUserGroupResult(updateSuccess, entity.getId()));
    }

    @Override
    public Result<Boolean> updateMarketingUserGroupStatus(String ea, Integer fsUserId, UpdateMarketingUserGroupStatusArg arg) {
        MarketingUserGroupEntity dbEntity = marketingUserGroupDao.getById(arg.getId());
        MarketingUserGroupEntity entity = BeanUtil.copy(arg, MarketingUserGroupEntity.class);
        if (!MarketingUserGroupTypeEnum.STATIC.getType().equals(dbEntity.getType()) && !MarketingUserGroupCalculationPeriodEnum.MANUAL.getCalculationPeriod().equals(dbEntity.getCalculationPeriod())
            && MarketingUserGroupStatusEnum.OPEN.getStatus().equals(arg.getStatus()) && marketingUserGroupDao.countAutoCalculateMarketingUserGroupByEa(ea, arg.getId()) >= getQuota(ea)){
            return Result.newError(SHErrorCode.AUTO_CALCULATE_GROUP_REACH_LIMIT);
        }
        boolean isUpdate = marketingUserGroupDao.updateStatus(entity);
        if (isUpdate){
            if (MarketingUserGroupStatusEnum.OPEN.getStatus().equals(arg.getStatus())){
                marketingUserGroupManager.insertOperationRecord(ea, entity.getId(), fsUserId, MarketingUserGroupOperationTypeEnum.ENABLE.getOperationType(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_490));
            }
            if (MarketingUserGroupStatusEnum.CLOSE.getStatus().equals(arg.getStatus())){
                marketingUserGroupManager.insertOperationRecord(ea, entity.getId(), fsUserId, MarketingUserGroupOperationTypeEnum.DISABLE.getOperationType(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_493));
            }
        }
        return new Result(SHErrorCode.SUCCESS, isUpdate);
    }

    @Override
    public Result<Boolean> deleteMarketingUserGroup(String ea, Integer fsUserId, String id) {
        MarketingUserGroupEntity entity = marketingUserGroupDao.getMarketingUserGroupDeatail(ea, id);
        if(!MarketingUserGroupStatusEnum.CLOSE.getStatus().equals(entity.getStatus())){
            return Result.newError(SHErrorCode.OPEN_USER_GROUP_NOT_CAN_DELETE);
        }
        MarketingUserGroupEntity updateEntity = new MarketingUserGroupEntity();
        updateEntity.setId(id);
        updateEntity.setStatus(MarketingUserGroupStatusEnum.DELETED.getStatus());
        boolean isDelete = marketingUserGroupDao.updateStatus(updateEntity);
        objectGroupRelationDAO.deleteObjectFromObjectGroupRelation(ea, id, ObjectTypeEnum.USER_MARKETING_GROUP.getType());
        if (isDelete){
            marketingUserGroupManager.insertOperationRecord(ea, entity.getId(), fsUserId, MarketingUserGroupOperationTypeEnum.DELETE.getOperationType(), I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_511));
        }
        return Result.newSuccess(isDelete);
    }

    @Override
    public Result<List<MarketingUserGroupSimpleResult>> listAllMarketingUserGroupResult(int offset, int limit) {
        List<MarketingUserGroupEntity> marketingUserGroupEntities = marketingUserGroupDao.listAutoCalculateMarketingUserGroup(offset, limit);
        return Result.newSuccess(BeanUtil.copy(marketingUserGroupEntities, MarketingUserGroupSimpleResult.class));
    }

    @Override
    public Result<List<MarketingUserGroupSimpleResult>> getZeroUserNumberGroup() {
        if (StringUtils.isEmpty(recheckUserGroupEa)) {
          return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<String> eaList;
        if (recheckUserGroupEa.contains("all")) {
            eaList = null;
        } else {
            String[] arr = recheckUserGroupEa.split(",");
            eaList = Lists.newArrayList(arr);
        }
        List<MarketingUserGroupEntity> marketingUserGroupEntities = marketingUserGroupDao.getZeroUserNumGroupList(eaList);
        return Result.newSuccess(BeanUtil.copy(marketingUserGroupEntities, MarketingUserGroupSimpleResult.class));
    }

    @Override
    public Result<Boolean> calculateUserMarketingAccountData(String ea, Integer fsUserId, String marketingUserGroupId) {
        //过滤掉停用企业、营销通配额过期企业
        if (marketingActivityRemoteManager.enterpriseStop(ea) || appVersionManager.getCurrentAppVersion(ea) == null){
            log.info("MarketingUserGroupServiceImpl.calculateUserMarketingAccountData failed enterprise stop or license expire ea:{}", ea);
            return Result.newSuccess(false);
        }
        initMarketingGroupUserActionRecord(ea, fsUserId, marketingUserGroupId, MarketingGroupUserActionTriggerTypeEnum.TRIGGER_BY_TIMMER.getType());
        marketingUserGroupManager.asyncCalculateUserMarketingAccountData(ea, fsUserId, marketingUserGroupId);
        return Result.newSuccess(true);
    }

    @Override
    public Result<Integer> getUserMarketingAccountWeeklyStatistics(Date startDate, Date endDate) {
        Integer count  =userMarketingAccountDAO.getUserMarketingAccountWeeklyStatistics(startDate, endDate);
        return  Result.newSuccess(count);
    }

    @Override
    public Result<List<MarketingUserGroupDetailResult>> getMarketingUserGroupDetailByIds(String ea, Integer fsUserId, List<String> marketingUserGroupIds) {
        return marketingUserGroupManager.getMarketingUserGroupDetailByIds(ea, marketingUserGroupIds);
    }
    
    @Override
    public Result<Void> addMarketingUserToGroup(String ea, Integer fsUserId, AddMarketingUserToGroupArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getMarketingUserGroupId()));
        if (AddMarketingUserToGroupTypeEnum.BY_CRM_OBJECT.getAddType().equals(arg.getAddType())){
            Preconditions.checkArgument(arg.getObjectData() != null && !arg.getObjectData().isEmpty());
            for (AddMarketingUserToGroupArg.ObjectList objectDatum : arg.getObjectData()) {
                Preconditions.checkArgument(objectDatum.getCrmObjectIds() != null && !objectDatum.getCrmObjectIds().isEmpty() && !objectDatum.getCrmObjectApiName().isEmpty());
            }
        } else if (AddMarketingUserToGroupTypeEnum.BY_CUSTOMER_RELATION.getAddType().equals(arg.getAddType())) {
            List<Integer> dataRange = arg.getDataRange();
            if (CollectionUtils.isEmpty(dataRange)) {
                return Result.newError(SHErrorCode.DATA_RANGE_CANT_NULL);
            }
            for (Integer code : dataRange) {
                if (MarketingUserGroupDataRangeEnum.getByCode(code) == null) {
                    return Result.newError(SHErrorCode.DATA_RANGE_PARAM_ERROR);
                }
            }
        } else {
            Preconditions.checkArgument(MarketingUserSearchTypeEnum.isValid(arg.getAddType()));
        }
        boolean addResult = marketingUserGroupManager.asyncAddMarketingUserToGroupByCondition(ea, fsUserId, arg);
        if (!addResult){
            return Result.newError(SHErrorCode.EXISTED_ADD_USER_TO_GROUP_TASK);
        }
        return Result.newSuccess();
    }
    
    @Override
    public Result<Void> manualCalculateMarketingUserGroup(String ea, Integer fsUserId, ManualCalculateMarketingUserGroupArg arg) {
        Preconditions.checkArgument(arg.getMarketingUserGroupIds() != null && !arg.getMarketingUserGroupIds().isEmpty());
        for (String marketingUserGroupId : arg.getMarketingUserGroupIds()) {
            //人群计算监控
            initMarketingGroupUserActionRecord(ea, fsUserId, marketingUserGroupId, MarketingGroupUserActionTriggerTypeEnum.TRIGGER_BY_EMPLOYEE.getType());
            marketingUserGroupManager.asyncCalculateUserMarketingAccountData(ea, fsUserId, marketingUserGroupId);
        }
        return Result.newSuccess();
    }
    
    @Override
    public Result<Integer> deleteMarketingUserFromGroup(String ea, Integer fsUserId, DeleteMarketingUserFromGroupArg deleteMarketingUserFromGroupArg) {
        MarketingUserGroupEntity marketingUserGroup = marketingUserGroupDao.getById(deleteMarketingUserFromGroupArg.getMarketingUserGroupId());
        Preconditions.checkArgument(marketingUserGroup != null && MarketingUserGroupTypeEnum.STATIC.getType().equals(marketingUserGroup.getType()));
        Preconditions.checkArgument(deleteMarketingUserFromGroupArg.getMarketingUserIds() != null && !deleteMarketingUserFromGroupArg.getMarketingUserIds().isEmpty());
        int deleteCount = marketingUserGroupManager.batchDeleteMarketingUserFromGroup(ea, fsUserId, deleteMarketingUserFromGroupArg.getMarketingUserGroupId(), new HashSet<>(deleteMarketingUserFromGroupArg.getMarketingUserIds()));
        String nameList = userMarketingAccountManager.buildNameListByMarketingUserIds(ea, deleteMarketingUserFromGroupArg.getMarketingUserIds());
        String deleteDescription = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_MARKETINGUSERGROUPSERVICEIMPL_591) + TextUtil.pureSubStr(nameList, 800);
        marketingUserGroupManager.insertOperationRecord(ea, deleteMarketingUserFromGroupArg.getMarketingUserGroupId(), fsUserId, MarketingUserGroupOperationTypeEnum.REMOVE_USER.getOperationType(), deleteDescription);
        marketingUserGroupManager.updateMarketingUserGroupStatisticData(ea, deleteMarketingUserFromGroupArg.getMarketingUserGroupId());
        return Result.newSuccess(deleteCount);
    }
    
    @Override
    public Result<PageResult<UserMarketingAccountDataInUserGroup>> pageListMarketingUserInGroup(String ea, Integer fsUserId, PageListMarketingUserInGroupArg arg) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(arg.getMarketingUserGroupId()));
        Preconditions.checkArgument(arg.getPageNo() != null && arg.getPageNo() > 0);
        Preconditions.checkArgument(arg.getPageSize() != null && arg.getPageSize() > 0);
        arg.setUserName(Strings.isNullOrEmpty(arg.getUserName()) ? null : arg.getUserName());
        Page page = new Page(arg.getPageNo(), arg.getPageSize(), true);
        List<MarketingUserGroupToUserRelationEntity> relations = marketingUserGroupToUserRelationDao.pageListMarketingUserIdInMarketingUserGroup(ea, arg.getMarketingUserGroupId(), arg.getUserName(), page);
        Map<String, UserMarketingAccountData> dataMap = userMarketingAccountManager.getBaseInfosByIds(ea, fsUserId, relations.stream().map(MarketingUserGroupToUserRelationEntity::getMarketingUserId).collect(Collectors.toList()), InfoStateEnum.BRIEF);
        List<UserMarketingAccountDataInUserGroup> resultList = relations.stream().filter(r -> dataMap.get(r.getMarketingUserId()) != null).map(r -> {
            UserMarketingAccountDataInUserGroup data = new UserMarketingAccountDataInUserGroup();
            BeanUtils.copyProperties(dataMap.get(r.getMarketingUserId()), data);
            data.setAddTime(r.getAddTime());
            data.setAdder(r.getAdder());
            return data;
        }).collect(Collectors.toList());
        return Result.newSuccess(PageResult.newPageResult(arg.getPageNo(), arg.getPageSize(), page.getTotalNum(), resultList));
    }
    
    @Override
    public Result<CountAutoCalculateMarketingUserGroupResult> countAutoCalculateMarketingUserGroup(String ea, String excludeMarketingUserGroupId) {
        Integer count = marketingUserGroupDao.countAutoCalculateMarketingUserGroupByEa(ea, excludeMarketingUserGroupId);
        return Result.newSuccess(new CountAutoCalculateMarketingUserGroupResult(count, getQuota(ea)));
    }

    @Override
    public Result<List<GetMarketingUserGroupNameResult>> getMarketingUserGroupName(String ea, Integer fsUserId, List<String> ids) {
        List<GetMarketingUserGroupNameResult> list = Lists.newArrayList();
        List<MarketingUserGroupEntity> entityList = marketingUserGroupDao.batchGet(ea, ids);
        if (CollectionUtils.isNotEmpty(entityList)){
            for (MarketingUserGroupEntity entity : entityList){
                GetMarketingUserGroupNameResult groupNameResult = new GetMarketingUserGroupNameResult();
                groupNameResult.setId(entity.getId());
                groupNameResult.setName(entity.getName());
                list.add(groupNameResult);
            }
        }

        return Result.newSuccess(list);
    }

    @Override
    public Result<GetDynamicUserMarketingGroupQuotaResult> getDynamicUserMarketingGroupQuota(String ea, Integer fsUserId) {
        int count = marketingUserGroupDao.countAutoCalculateMarketingUserGroupByEa(ea, null);
        GetDynamicUserMarketingGroupQuotaResult result = new GetDynamicUserMarketingGroupQuotaResult();
        result.setQuota(getQuota(ea));
        result.setUsed(count);
        return Result.newSuccess(result);
    }

    private int getQuota(String ea) {
        int quota = maxAutoCalculateGroup;
        List<ModuleParaPojo> crm = licenseManager.queryModulePara(ea, "CRM", DYNAMIC_TARGET_NUMBER_APP,
                Sets.newHashSet("marketing_strategy_dynamic_target_number_limit"));
        if (CollectionUtils.isNotEmpty(crm)) {
            int sum = crm.stream()
                    .map(ModuleParaPojo::getParaValue)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::valueOf)
                    .sum();
            quota += sum * 10;
        }
        return quota;
    }

    @Override
    public Result<List<MarketingUserGroupResult>> listMarketingUserGroupTop5(String ea, Integer fsUserId) {
        List<MarketingUserGroupResult> result = listMarketingUserGroupTop5V2(ea, fsUserId);
        return Result.newSuccess(result);
    }

    private List<MarketingUserGroupResult>  listMarketingUserGroupTop5V2(String ea, Integer fsUserId) {
        String key;
        // 这里的多组织开关营销通的多组织开关 可以重复开关，所以这里key不一样，防止从缓存查的时候能查到多组织权限以外的数据
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
        if (isOpen) {
            key = "MARKETING_USERGROUP_TOP_DATA_PERMISSION_" + ea;
        } else {
            key = "MARKETING_USERGROUP_TOP_" + ea;
        }
        List<MarketingUserGroupResult> result = getFromRedis(key);
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        List<String> activityIds = Lists.newArrayList();
        if (isOpen) {
            List<String> idList = dataPermissionManager.getObjectIdsByDataPermission(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
            if (CollectionUtils.isEmpty(idList)) {
                return Lists.newArrayList();
            }
            activityIds.addAll(idList);
        }
        result = marketingUserGroupDao.queryMarketingUserGroupTop5(ea,activityIds);
        if (CollectionUtils.isNotEmpty(result)) {
            redisManager.set(key, JSON.toJSONString(result), 60 * 60 * 2);
        }
        return result;
    }

    private List<MarketingUserGroupResult> getFromRedis(String key) {
        String val = redisManager.get(key);
        if (StringUtils.isBlank(val)) {
            return Lists.newArrayList();
        }
        try {
            return JSON.parseArray(val, MarketingUserGroupResult.class);
        } catch (Exception e) {
            log.error("listMarketingUserGroupTop5 get from redis error, key: {} ", key);
        }
        return Lists.newArrayList();
    }
}