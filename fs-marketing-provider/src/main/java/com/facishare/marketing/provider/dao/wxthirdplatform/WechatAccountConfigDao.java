package com.facishare.marketing.provider.dao.wxthirdplatform;

import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity;
import com.facishare.marketing.provider.entity.wxthirdplatform.WxMiniAppEntity;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.*;

public interface WechatAccountConfigDao {
    @Insert("INSERT INTO wechat_account_config(wx_app_id, third_platform_id, refresh_token, access_token, access_token_expire_time, func_list, create_time, update_time) " +
            " VALUES(#{wxAppId}, #{platformId}, #{refreshToken}, #{accessToken}, #{accessTokenExpireTime}, #{funcList}, NOW(), NOW()) " +
            " ON CONFLICT(wx_app_id) DO UPDATE SET " +
            " refresh_token=#{refreshToken}, access_token=#{accessToken}, access_token_expire_time=#{accessTokenExpireTime}, func_list=#{funcList}, update_time=NOW()")
    int mergeBaseInfo(@Param("wxAppId")String wxAppId, @Param("platformId") String platformId, @Param("refreshToken") String refreshToken, @Param("accessToken") String accessToken, @Param("accessTokenExpireTime") Long accessTokenExpireTime, @Param("funcList") String funcList);

    @Update("UPDATE wechat_account_config SET nick_name=#{info.nickName}, head_img=#{info.headImg}, service_type_info=#{info.serviceTypeInfo},business_info=#{info.businessInfo}," +
            " verify_type_info=#{info.verifyTypeInfo}, user_name=#{info.userName}, principal_name=#{info.principalName},signature=#{info.signature},qr_code_url=#{info.qrCodeUrl}," +
            " update_time=NOW() WHERE wx_app_id=#{info.wxAppId}")
    int updateAppInfo(@Param("info") WechatAccountConfigEntity wechatAccountConfigEntity);

    @Select("SELECT * FROM wechat_account_config WHERE wx_app_id=#{wxAppId}")
    WechatAccountConfigEntity getByWxAppId(@Param("wxAppId") String wxAppId);

    @Select("<script>"
        + "SELECT * FROM wechat_account_config "
        + "WHERE wx_app_id IN <foreach open='(' close=')' separator=',' collection='wxAppIds' item='wxAppId'>#{wxAppId}</foreach>"
        + "AND third_platform_id = 'YXT'</script>")
    List<WechatAccountConfigEntity> batchGetYXTConfig(@Param("wxAppIds") Collection<String> wxAppIds);

    @Update("UPDATE wechat_account_config SET access_token=#{accessToken}, access_token_expire_time=#{accessTokenExpireTime}, update_time=NOW() WHERE wx_app_id=#{wxAppId}")
    int updateAccessToken(@Param("wxAppId") String wxAppId, @Param("accessToken") String accessToken, @Param("accessTokenExpireTime") Long accessTokenExpireTime);

    @Update("UPDATE wechat_account_config SET current_code_version=#{currentCodeVersion}, code_description=#{codeDescription}, code_version_release_time=NOW() WHERE wx_app_id=#{wxAppId}")
    int releaseVersion(@Param("wxAppId") String wxAppId, @Param("currentCodeVersion") String currentCodeVersion, @Param("codeDescription") String codeDescription);
    
    @Delete("DELETE FROM wechat_account_config WHERE wx_app_id=#{wxAppId}")
    int deleteByWxAppId(@Param("wxAppId") String wxAppId);

    @Select("<script> " +
                "SELECT t1.wx_app_id, nick_name mini_app_name, ea, current_code_version, code_version_release_time, access_token, access_token_expire_time " +
                "FROM wechat_account_config t1, wechat_ea_bind t2 " +
                "WHERE t1.wx_app_id = t2.wx_app_id AND third_platform_id = #{thirdPlatformId}" +
                "<if test = 'keyword != null and keyword != &apos;&apos;'>" +
                    "<if test = 'keywordType == 1'>" +
                        " AND ea LIKE CONCAT('%',#{keyword},'%')" +
                    "</if>" +
                    "<if test = 'keywordType == 2'>" +
                        " AND nick_name LIKE CONCAT('%',#{keyword},'%')" +
                    "</if>" +
                    "<if test = 'keywordType == 3'>" +
                        " AND current_code_version LIKE CONCAT('%',#{keyword},'%')" +
                    "</if>" +
                "</if>" +
            "</script>")
    List<WxMiniAppEntity> listWxMiniApp(@Param("thirdPlatformId") String platformId, @Param("keywordType") String keywordType, @Param("keyword") String keyword);
}
