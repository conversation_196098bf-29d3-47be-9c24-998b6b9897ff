package com.facishare.marketing.provider.entity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.enums.MarketingEventEnum;
import com.facishare.marketing.common.enums.MarketingEventMappingTypeEnum;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * Created  By zhoux 2021/03/08
 **/
@Data
public class MarketingEventCommonSettingEntity implements Serializable {

    private String ea;

    private ActivityTypeMapping activityTypeMapping;

    private Integer createBy;

    private Date createTime;

    private Date updateTime;

    private Boolean openMergePhone;

    private Boolean marketingEventAudit;

    private Boolean marketingActivityAudit;

    public void initActivityTypeMapping() {
        ActivityTypeMapping activityTypeMapping = new ActivityTypeMapping();
        ActivityTypeMapping.ActivityTypeMappingDetail contentMarketingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
        contentMarketingDetail.setActivityType(MarketingEventMappingTypeEnum.CONTENT_MARKETING.getType());
        ActivityTypeMapping.MappingDetail mappingDetail = new ActivityTypeMapping.MappingDetail();
        mappingDetail.setApiName(MarketingEventEnum.CONTENT_MARKETING.getEventType());
        mappingDetail.setFieldName(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_MARKETINGEVENTCOMMONSETTINGENTITY_39));
        ActivityTypeMapping.MappingDetail mappingDetail2 = new ActivityTypeMapping.MappingDetail();
        mappingDetail2.setApiName(MarketingEventEnum.MULTIVENUE_MARKETING.getEventType());
        mappingDetail2.setFieldName(I18nUtil.get(I18nKeyEnum.MARK_ENTITY_MARKETINGEVENTCOMMONSETTINGENTITY_42));
        contentMarketingDetail.setMapping(Lists.newArrayList(mappingDetail, mappingDetail2));
        activityTypeMapping.add(contentMarketingDetail);
        this.activityTypeMapping = activityTypeMapping;
        this.openMergePhone = false;
        this.marketingEventAudit = false;
        this.marketingActivityAudit = false;
    }

}