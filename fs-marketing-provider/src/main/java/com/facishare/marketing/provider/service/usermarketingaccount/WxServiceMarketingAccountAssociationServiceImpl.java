package com.facishare.marketing.provider.service.usermarketingaccount;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.CrmWechatFanFieldEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.outapi.arg.result.AssociateCrmWxUserModel;
import com.facishare.marketing.outapi.arg.result.AssociateWxServiceModel;
import com.facishare.marketing.outapi.arg.result.AssociateWxServiceModel.AssociateWxServiceArg;
import com.facishare.marketing.outapi.arg.result.AssociateWxServiceModel.AssociateWxServiceResult;
import com.facishare.marketing.outapi.service.CrmWxUserMarketingAccountAssociationService;
import com.facishare.marketing.outapi.service.WxServiceMarketingAccountAssociationService;
import com.facishare.marketing.provider.dao.UserMarketingAccountDAO;
import com.facishare.marketing.provider.dao.UserMarketingCrmWxUserAccountRelationDao;
import com.facishare.marketing.provider.dao.UserMarketingWxServiceAccountRelationDao;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingCrmWxUserAccountRelationEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.UserMarketingWxServiceAccountRelationEntity;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.EnterpriseInfoManager;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 12/12/2018
 */
@Service("wxServiceMarketingAccountAssociationService")
@Slf4j
public class WxServiceMarketingAccountAssociationServiceImpl implements WxServiceMarketingAccountAssociationService {
    @Autowired
    private CrmWxUserMarketingAccountAssociationService crmWxUserMarketingAccountAssociationService;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private EnterpriseInfoManager enterpriseInfoManager;

    @Override
    public Result<AssociateWxServiceResult> associateWxService(AssociateWxServiceArg associateWxServiceArg) {
        if (enterpriseInfoManager.isEnterpriseStopAndVersionExpired(associateWxServiceArg.getEa())) {
            return Result.newSuccess();
        }

        String lockKey = associateWxServiceArg.getWxAppId() + "_" + associateWxServiceArg.getWxOpenId();
        for (int i = 0; i < 5; i++) {
            boolean isLock = redisManager.lock(lockKey, 3000);
            if (!isLock) {
                try {
                    Thread.sleep(300);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                break;
            }
        }
        try {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setEa(associateWxServiceArg.getEa());
            associationArg.setPhone(associateWxServiceArg.getPhone());
            associationArg.setAssociationId(associateWxServiceArg.getWxOpenId());
            associationArg.setWxAppId(associateWxServiceArg.getWxAppId());
            associationArg.setType(ChannelEnum.WX_SERVICE.getType());
            associationArg.setTriggerAction("associateWxService");
            associationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());
            AssociationResult associationResult = userMarketingAccountAssociationManager.associate(associationArg);
            if (associationResult == null){
                return Result.newSuccess();
            }

            AssociateWxServiceResult associateWxServiceResult = new AssociateWxServiceResult();
            associateWxServiceResult.setUserMarketingId(associationResult.getUserMarketingAccountId());
            associateWxServiceResult.setUserMarketingWxServiceAccountRelationId(associationResult.getRelationId());
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName(), Lists.newArrayList(associateWxServiceArg.getWxAppId()), Filter.OperatorContants.EQ);
            searchQuery.addFilter(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName(), Lists.newArrayList(associateWxServiceArg.getWxOpenId()), Filter.OperatorContants.EQ);
            searchQuery.setLimit(1);
            searchQuery.setOffset(0);
            ControllerListArg controllerListArg = new ControllerListArg();
            controllerListArg.setSearchQuery(searchQuery);
            HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(associateWxServiceArg.getEa()), -10000);
            com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> crmResult = metadataControllerService.list(systemHeader, CrmObjectApiNameEnum.WECHAT.getName(), controllerListArg);
            if(crmResult.isSuccess() && crmResult.getData() != null && crmResult.getData().getDataList() != null && !crmResult.getData().getDataList().isEmpty()){
                ObjectData objectData = crmResult.getData().getDataList().get(0);
                if(!Strings.isNullOrEmpty(objectData.getId())){
                    AssociationArg crmAssociationArg = new AssociationArg();
                    crmAssociationArg.setType(ChannelEnum.CRM_WX_USER.getType());
                    crmAssociationArg.setEa(associateWxServiceArg.getEa());
                    crmAssociationArg.setAssociationId(objectData.getId());
                    crmAssociationArg.setWxAppId(objectData.getString(CrmWechatFanFieldEnum.WX_APP_ID.getFieldName()));
                    crmAssociationArg.setAdditionalAssociationId(objectData.getString(CrmWechatFanFieldEnum.WX_OPEN_ID.getFieldName()));
                    crmAssociationArg.setUserName(objectData.getName());
                    crmAssociationArg.setEmail(objectData.getString("email"));
                    crmAssociationArg.setTriggerAction("associateWxService");
                    crmAssociationArg.setTriggerSource(ChannelEnum.CRM_WX_USER.getDescription());
                    userMarketingAccountAssociationManager.associate(crmAssociationArg);
                }
            }
            return Result.newSuccess(associateWxServiceResult);
        } finally {
            redisManager.unLock(lockKey);
        }
    }

    @Override
    public void batchAssociateWxService(AssociateWxServiceModel.BatchAssociateWxServiceArg batchAssociateWxServiceArg) {
        Result validateResult = batchAssociateWxServiceArg.validate();
        if (!validateResult.isSuccess()) {
            log.warn("WxServiceMarketingAccountAssociationServiceImpl.batchAssociateWxService error params:{}", JSON.toJSONString(batchAssociateWxServiceArg));
        }
        ThreadPoolUtils.execute(() -> batchAssociateWxServiceArg.getWxOpenIds().forEach(val -> {
            if (!Strings.isNullOrEmpty(val)) {
                AssociateWxServiceArg associateWxServiceArg = BeanUtil.copy(batchAssociateWxServiceArg, AssociateWxServiceArg.class);
                associateWxServiceArg.setWxOpenId(val);
                this.associateWxService(associateWxServiceArg);
            }
        }), ThreadPoolUtils.ThreadPoolTypeEnums.HEAVY_BUSINESS);
    }

    @Override
    public Result<AssociateWxServiceModel.AssociateWxServiceAndCrmWxUserResult> associateWxServiceAndCrmWxUser(AssociateWxServiceModel.AssociateWxServiceAndCrmWxUserArg associateWxServiceAndCrmWxUserArg) {
        associateWxServiceAndCrmWxUserArg.validate();
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(associateWxServiceAndCrmWxUserArg.getEa());
        associationArg.setType(ChannelEnum.CRM_WX_USER.getType());
        associationArg.setAssociationId(associateWxServiceAndCrmWxUserArg.getCrmObjectId());
        associationArg.setPhone(associateWxServiceAndCrmWxUserArg.getPhone());
        associationArg.setWxAppId(associateWxServiceAndCrmWxUserArg.getWxAppId());
        associationArg.setAdditionalAssociationId(associateWxServiceAndCrmWxUserArg.getWxOpenId());
        associationArg.setTriggerAction("associateWxServiceAndCrmWxUser");
        associationArg.setTriggerSource(ChannelEnum.CRM_WX_USER.getDescription());

        AssociationResult ar = userMarketingAccountAssociationManager.associate(associationArg);
        if (ar == null || StringUtils.isEmpty(ar.getUserMarketingAccountId())){
            return Result.newSuccess();
        }
        AssociateWxServiceModel.AssociateWxServiceAndCrmWxUserResult associateWxServiceAndCrmWxUserResult = new AssociateWxServiceModel.AssociateWxServiceAndCrmWxUserResult();
        associateWxServiceAndCrmWxUserResult.setCrmWxUserMarketingId(ar.getUserMarketingAccountId());
        associateWxServiceAndCrmWxUserResult.setWxServiceUserMarketingId(ar.getUserMarketingAccountId());
        return Result.newSuccess(associateWxServiceAndCrmWxUserResult);
    }
}