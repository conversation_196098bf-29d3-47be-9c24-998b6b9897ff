package com.facishare.marketing.provider.manager.usermarketingaccount;

import com.facishare.marketing.api.result.marketinguser.MarketingUserExcludeApinameResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingObjectResult;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.MarketingUserActionChannelType;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.marketingUserGroup.UserMarketingExcludeObjectDAO;
import com.facishare.marketing.provider.entity.MarketingUserGroupToUserRelationEntity;
import com.facishare.marketing.provider.entity.TriggerInstanceEntity;
import com.facishare.marketing.provider.entity.UserMarketingExcludeObjectEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerData.ObjectIdAndDescribeData;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.EnterpriseSpreadRecordManager;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.facishare.marketing.provider.dao.marketingflow.ExcludedFlowUserDao ;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/3/7.
 */
@Slf4j
@Component
public class UserMarketingAccountRelationManager {
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private UserMarketingMiniappAccountRelationDao userMarketingMiniappAccountRelationDao;
    @Autowired
    private UserMarketingExcludeObjectDAO userMarketingExcludeObjectDAO;
    @Autowired
    private UserMarketingWxServiceAccountRelationDao userMarketingWxServiceAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;
    @Autowired
    private UserMarketingBrowserUserRelationDao userMarketingBrowserUserRelationDao;
    @Autowired
    private UserMarketingAccountDAO userMarketingAccountDAO;
    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingWxWorkExternalUserRelationDao userMarketingWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;
    @Autowired
    private MarketingUserGroupToUserRelationDao marketingUserGroupToUserRelationDao;
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;
    @Autowired
    private UserMarketingCrmCustomizeObjectRelationDao userMarketingCrmCustomizeObjectRelationDao;
    @Autowired
    private ExcludedFlowUserDao excludedFlowUserDao;
    @Autowired
    private TriggerInstanceDao triggerInstanceDao;
    @Autowired
    private UserMarketingAccountChangeLogManager userMarketingAccountChangeLogManager;
    @Autowired
    private EnterpriseSpreadRecordManager enterpriseSpreadRecordManager;
    private static final int USER_MARKETING_RELATION_EXCLUDE_OBJECT_STATUS = 1;   //营销用户关联排除了该对象

    public Optional<String> bindBrowserUserAndLead(String ea, String browserUserId, String crmLeadId, String phone, String triggerAction) {
        log.info("bindBrowserUserAndLead ea:{} browserUserId:{} crmLeadId:{}  phone:{}", ea, browserUserId, crmLeadId, phone);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(browserUserId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(crmLeadId));
        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setTriggerAction(triggerAction);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());

        AssociationArg crmLeadAssociateArg = new AssociationArg();
        crmLeadAssociateArg.setAssociationId(crmLeadId);
        crmLeadAssociateArg.setEa(ea);
        crmLeadAssociateArg.setType(ChannelEnum.CRM_LEAD.getType());
        crmLeadAssociateArg.setPhone(phone);
        crmLeadAssociateArg.setTriggerAction(triggerAction);
        crmLeadAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());

        //将线索生成的营销用户绑定到访客身份上，以便做访客&线索&微信粉丝身份打通
        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(browserUserAssociateArg, crmLeadAssociateArg));
    }

    public Optional<String> bindBrowserUserAndCustomer(String ea, String browserUserId, String customerId, String phone, String triggerAction) {
        log.info("bindBrowserUserAndCustomer ea:{} browserUserId:{} customerId:{}  phone:{}", ea, browserUserId, customerId, phone);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(customerId));
        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setTriggerAction(triggerAction);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());

        AssociationArg crmLeadAssociateArg = new AssociationArg();
        crmLeadAssociateArg.setAssociationId(customerId);
        crmLeadAssociateArg.setEa(ea);
        crmLeadAssociateArg.setType(ChannelEnum.CRM_ACCOUNT.getType());
        crmLeadAssociateArg.setPhone(phone);
        crmLeadAssociateArg.setTriggerAction(triggerAction);
        crmLeadAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());

        if (StringUtils.isBlank(browserUserId)) {
            AssociationResult result = userMarketingAccountAssociationManager.associate(crmLeadAssociateArg);
            if (result != null) {
                return Optional.of(result.getUserMarketingAccountId());
            }
            return Optional.empty();
        }

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(browserUserAssociateArg, crmLeadAssociateArg));
    }

    public Optional<String> bindBrowserUserAndContact(String ea, String browserUserId, String contactId, String phone, String triggerAction) {
        log.info("bindBrowserUserAndLead ea:{} browserUserId:{} contactId:{}  phone:{}", ea, browserUserId, contactId, phone);
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(contactId));
        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setTriggerAction(triggerAction);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());


        AssociationArg crmLeadAssociateArg = new AssociationArg();
        crmLeadAssociateArg.setAssociationId(contactId);
        crmLeadAssociateArg.setEa(ea);
        crmLeadAssociateArg.setType(ChannelEnum.CRM_CONTACT.getType());
        crmLeadAssociateArg.setPhone(phone);
        crmLeadAssociateArg.setTriggerAction(triggerAction);
        crmLeadAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());

        if (StringUtils.isBlank(browserUserId)) {
            AssociationResult result = userMarketingAccountAssociationManager.associate(crmLeadAssociateArg);
            if (result != null) {
                return Optional.of(result.getUserMarketingAccountId());
            }
            return Optional.empty();
        }

        //将线索生成的营销用户绑定到访客身份上，以便做访客&线索&微信粉丝身份打通
        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(browserUserAssociateArg, crmLeadAssociateArg));
    }

    public Optional<String> bindBrowserUserAndMember(String ea, String browserUserId, String memberId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(browserUserId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(memberId));
        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setTriggerAction(triggerAction);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());

        AssociationArg memberAssociateArg = new AssociationArg();
        memberAssociateArg.setAssociationId(memberId);
        memberAssociateArg.setEa(ea);
        memberAssociateArg.setType(ChannelEnum.CRM_MEMBER.getType());
        memberAssociateArg.setPhone(phone);
        memberAssociateArg.setTriggerAction(triggerAction);
        memberAssociateArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(browserUserAssociateArg, memberAssociateArg));
    }

    public Optional<String> bindLoginIdentityAndCrmObject(String ea, AssociationArg associationArg, ChannelEnum crmObjectType, String crmObjectId, String phone, String triggerAction) {
        if (StringUtils.isBlank(ea) || associationArg == null || crmObjectType == null || StringUtils.isBlank(crmObjectId)) {
            return Optional.empty();
        }
        AssociationArg crmObjectAssociateArg = new AssociationArg();
        crmObjectAssociateArg.setAssociationId(crmObjectId);
        crmObjectAssociateArg.setEa(ea);
        crmObjectAssociateArg.setType(crmObjectType.getType());
        crmObjectAssociateArg.setPhone(phone);
        crmObjectAssociateArg.setTriggerAction(triggerAction);
        crmObjectAssociateArg.setTriggerSource(crmObjectType.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(crmObjectAssociateArg, associationArg));
    }

    public Optional<String> bindWxUserAndLead(String ea, String wxAppId, String wxOpenId, String crmLeadId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxOpenId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(crmLeadId));
        AssociationArg wxUserAssociationArg = new AssociationArg();
        wxUserAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxUserAssociationArg.setEa(ea);
        wxUserAssociationArg.setWxAppId(wxAppId);
        wxUserAssociationArg.setAssociationId(wxOpenId);
        wxUserAssociationArg.setPhone(phone);
        wxUserAssociationArg.setTriggerAction(triggerAction);
        wxUserAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        AssociationArg crmLeadAssociateArg = new AssociationArg();
        crmLeadAssociateArg.setAssociationId(crmLeadId);
        crmLeadAssociateArg.setEa(ea);
        crmLeadAssociateArg.setType(ChannelEnum.CRM_LEAD.getType());
        crmLeadAssociateArg.setPhone(phone);
        crmLeadAssociateArg.setTriggerAction(triggerAction);
        crmLeadAssociateArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(crmLeadAssociateArg, wxUserAssociationArg));
    }

    public Optional<String> bindWxUserAndCustomer(String ea, String wxAppId, String wxOpenId, String customerId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxOpenId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(customerId));
        AssociationArg wxUserAssociationArg = new AssociationArg();
        wxUserAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxUserAssociationArg.setEa(ea);
        wxUserAssociationArg.setWxAppId(wxAppId);
        wxUserAssociationArg.setAssociationId(wxOpenId);
        wxUserAssociationArg.setPhone(phone);
        wxUserAssociationArg.setTriggerAction(triggerAction);
        wxUserAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        AssociationArg crmLeadAssociateArg = new AssociationArg();
        crmLeadAssociateArg.setAssociationId(customerId);
        crmLeadAssociateArg.setEa(ea);
        crmLeadAssociateArg.setType(ChannelEnum.CRM_ACCOUNT.getType());
        crmLeadAssociateArg.setPhone(phone);
        crmLeadAssociateArg.setTriggerAction(triggerAction);
        crmLeadAssociateArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(crmLeadAssociateArg, wxUserAssociationArg));
    }

    public Optional<String> bindWxUserAndContact(String ea, String wxAppId, String wxOpenId, String contactId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxOpenId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(contactId));
        AssociationArg wxUserAssociationArg = new AssociationArg();
        wxUserAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxUserAssociationArg.setEa(ea);
        wxUserAssociationArg.setWxAppId(wxAppId);
        wxUserAssociationArg.setAssociationId(wxOpenId);
        wxUserAssociationArg.setPhone(phone);
        wxUserAssociationArg.setTriggerAction(triggerAction);
        wxUserAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        AssociationArg crmLeadAssociateArg = new AssociationArg();
        crmLeadAssociateArg.setAssociationId(contactId);
        crmLeadAssociateArg.setEa(ea);
        crmLeadAssociateArg.setType(ChannelEnum.CRM_CONTACT.getType());
        crmLeadAssociateArg.setPhone(phone);
        crmLeadAssociateArg.setTriggerAction(triggerAction);
        crmLeadAssociateArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(crmLeadAssociateArg, wxUserAssociationArg));
    }

    public Optional<String> bindWxUserAndMember(String ea, String wxAppId, String wxOpenId, String memberId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxOpenId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(memberId));
        AssociationArg wxUserAssociationArg = new AssociationArg();
        wxUserAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxUserAssociationArg.setEa(ea);
        wxUserAssociationArg.setWxAppId(wxAppId);
        wxUserAssociationArg.setAssociationId(wxOpenId);
        wxUserAssociationArg.setPhone(phone);
        wxUserAssociationArg.setTriggerAction(triggerAction);
        wxUserAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        AssociationArg memberAssociateArg = new AssociationArg();
        memberAssociateArg.setAssociationId(memberId);
        memberAssociateArg.setEa(ea);
        memberAssociateArg.setType(ChannelEnum.CRM_MEMBER.getType());
        memberAssociateArg.setPhone(phone);
        memberAssociateArg.setTriggerAction(triggerAction);
        memberAssociateArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(memberAssociateArg, wxUserAssociationArg));
    }

    public Optional<String> bindMiniappUserAndLead(String ea, String uid, String leadId, String phone, String triggerAction) {
        AssociationArg miniappAssociateArg = new AssociationArg();
        miniappAssociateArg.setType(ChannelEnum.MINIAPP.getType());
        miniappAssociateArg.setAssociationId(uid);
        miniappAssociateArg.setEa(ea);
        miniappAssociateArg.setPhone(phone);
        miniappAssociateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        miniappAssociateArg.setTriggerAction(triggerAction);

        AssociationArg leadAssociateArg = new AssociationArg();
        leadAssociateArg.setAssociationId(leadId);
        leadAssociateArg.setEa(ea);
        leadAssociateArg.setType(ChannelEnum.CRM_LEAD.getType());
        leadAssociateArg.setPhone(phone);
        leadAssociateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        leadAssociateArg.setTriggerAction(triggerAction);

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(miniappAssociateArg, leadAssociateArg));
    }

    public Optional<String> bindMiniappUserAndMember(String ea, String uid, String memberId, String phone, String triggerAction) {
        AssociationArg miniappAssociateArg = new AssociationArg();
        miniappAssociateArg.setType(ChannelEnum.MINIAPP.getType());
        miniappAssociateArg.setAssociationId(uid);
        miniappAssociateArg.setEa(ea);
        miniappAssociateArg.setPhone(phone);
        miniappAssociateArg.setTriggerAction(triggerAction);
        miniappAssociateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        AssociationArg memberAssociateArg = new AssociationArg();
        memberAssociateArg.setAssociationId(memberId);
        memberAssociateArg.setEa(ea);
        memberAssociateArg.setType(ChannelEnum.CRM_MEMBER.getType());
        memberAssociateArg.setPhone(phone);
        memberAssociateArg.setTriggerAction(triggerAction);
        memberAssociateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(miniappAssociateArg, memberAssociateArg));
    }

    public Optional<String> bindMiniappUserAndCustomer(String ea, String uid, String customerId, String phone, String triggerAction) {
        AssociationArg miniappAssociateArg = new AssociationArg();
        miniappAssociateArg.setType(ChannelEnum.MINIAPP.getType());
        miniappAssociateArg.setAssociationId(uid);
        miniappAssociateArg.setEa(ea);
        miniappAssociateArg.setPhone(phone);
        miniappAssociateArg.setTriggerAction(triggerAction);
        miniappAssociateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        AssociationArg leadAssociateArg = new AssociationArg();
        leadAssociateArg.setAssociationId(customerId);
        leadAssociateArg.setEa(ea);
        leadAssociateArg.setType(ChannelEnum.CRM_ACCOUNT.getType());
        leadAssociateArg.setPhone(phone);
        leadAssociateArg.setTriggerAction(triggerAction);
        leadAssociateArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(miniappAssociateArg, leadAssociateArg));
    }

    public Optional<String> bindMiniappUserAndWxWorkExternalUser(String ea, String uid, String externalUserId, String triggerAction) {
        AssociationArg associationMiniappArg = new AssociationArg();
        associationMiniappArg.setType(ChannelEnum.MINIAPP.getType());
        associationMiniappArg.setEa(ea);
        associationMiniappArg.setAssociationId(uid);
        associationMiniappArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        associationMiniappArg.setTriggerAction(triggerAction);

        AssociationArg associationExternalUserIdArg = new AssociationArg();
        associationExternalUserIdArg.setType(ChannelEnum.WX_WORK_EXTERNAL_USER.getType());
        associationExternalUserIdArg.setEa(ea);
        associationExternalUserIdArg.setAssociationId(externalUserId);
        associationExternalUserIdArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());
        associationExternalUserIdArg.setTriggerAction(triggerAction);

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(associationMiniappArg, associationExternalUserIdArg));

    }

    public Optional<String> bindBrowserUserIdAndWxWorkExternalUser(String ea, String browserUserId, String externalUserId) {
        AssociationArg associationMiniappArg = new AssociationArg();
        associationMiniappArg.setType(ChannelEnum.BROWSER_USER.getType());
        associationMiniappArg.setEa(ea);
        associationMiniappArg.setAssociationId(browserUserId);

        AssociationArg associationExternalUserIdArg = new AssociationArg();
        associationExternalUserIdArg.setType(ChannelEnum.WX_WORK_EXTERNAL_USER.getType());
        associationExternalUserIdArg.setEa(ea);
        associationExternalUserIdArg.setAssociationId(externalUserId);

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(associationMiniappArg, associationExternalUserIdArg));

    }

    public Optional<String> bindWxUserAndBrowserUser(String ea, String wxAppId, String wxOpenId, String browserUserId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxAppId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(wxOpenId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(browserUserId));

        AssociationArg wxUserAssociationArg = new AssociationArg();
        wxUserAssociationArg.setType(ChannelEnum.WX_SERVICE.getType());
        wxUserAssociationArg.setEa(ea);
        wxUserAssociationArg.setWxAppId(wxAppId);
        wxUserAssociationArg.setAssociationId(wxOpenId);
        wxUserAssociationArg.setPhone(phone);
        wxUserAssociationArg.setTriggerAction(triggerAction);
        wxUserAssociationArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setTriggerAction(triggerAction);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.WX_SERVICE.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(browserUserAssociateArg, wxUserAssociationArg));
    }

    public Optional<String> bindQywxExternalUserAndBrowserUser(String ea, String qywxCrmObjectId, String browserUserId, String externUserId, String phone, String triggerAction) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(qywxCrmObjectId));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(browserUserId));

        AssociationArg associationArg = new AssociationArg();
        associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
        associationArg.setAssociationId(qywxCrmObjectId);
        associationArg.setAdditionalAssociationId(externUserId);
        associationArg.setEa(ea);
        associationArg.setPhone(phone);
        associationArg.setTriggerAction(triggerAction);
        associationArg.setTriggerSource(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription());

        AssociationArg browserUserAssociateArg = new AssociationArg();
        browserUserAssociateArg.setAssociationId(browserUserId);
        browserUserAssociateArg.setEa(ea);
        browserUserAssociateArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserUserAssociateArg.setPhone(phone);
        browserUserAssociateArg.setTriggerAction(triggerAction);
        browserUserAssociateArg.setTriggerSource(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(browserUserAssociateArg, associationArg));
    }

    public Optional<String> bindMiniappUserAndContact(String ea, String uid, String crmContactId, String phone, String triggerAction) {
        AssociationArg associationMiniappArg = new AssociationArg();
        associationMiniappArg.setType(ChannelEnum.MINIAPP.getType());
        associationMiniappArg.setEa(ea);
        associationMiniappArg.setAssociationId(uid);
        associationMiniappArg.setPhone(phone);
        associationMiniappArg.setTriggerAction(triggerAction);
        associationMiniappArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        AssociationArg associationContactArg = new AssociationArg();
        associationContactArg.setType(ChannelEnum.CRM_CONTACT.getType());
        associationContactArg.setEa(ea);
        associationContactArg.setAssociationId(crmContactId);
        associationContactArg.setPhone(phone);
        associationContactArg.setTriggerAction(triggerAction);
        associationContactArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(associationMiniappArg, associationContactArg));
    }

    public Optional<String> bindMinappUserAndWxUser(String ea, String uid, String crmWxUserId, String miniAppPhone, String crmWxUserPhone, String triggerAction) {
        AssociationArg associationMiniappArg = new AssociationArg();
        associationMiniappArg.setType(ChannelEnum.MINIAPP.getType());
        associationMiniappArg.setEa(ea);
        associationMiniappArg.setAssociationId(uid);
        associationMiniappArg.setPhone(miniAppPhone);
        associationMiniappArg.setTriggerAction(triggerAction);
        associationMiniappArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        AssociationArg associationContactArg = new AssociationArg();
        associationContactArg.setType(ChannelEnum.CRM_WX_USER.getType());
        associationContactArg.setEa(ea);
        associationContactArg.setAssociationId(crmWxUserId);
        associationContactArg.setPhone(crmWxUserPhone);
        associationContactArg.setTriggerAction(triggerAction);
        associationContactArg.setTriggerSource(ChannelEnum.MINIAPP.getDescription());

        return Optional.ofNullable(userMarketingAccountAssociationManager.bind(associationMiniappArg, associationContactArg));
    }

    public Optional<String> getUserMarketingAccountIdByCrmObjectId(String ea, String objectApiName, String objectId) {
        AssociationArg associationArg = new AssociationArg();
        associationArg.setEa(ea);
        associationArg.setType(ChannelEnum.getByApiName(objectApiName).getType());
        associationArg.setAssociationId(objectId);
        AssociationResult associationResult = getByEaAndKeyProperties(associationArg);
        return associationResult == null || Strings.isNullOrEmpty(associationResult.getUserMarketingAccountId()) ? Optional.empty() : Optional.of(associationResult.getUserMarketingAccountId());
    }

    @FilterLog
    public AssociationResult getByEaAndKeyProperties(AssociationArg associationArg) {
        AssociationResult associationResult = new AssociationResult();
        if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.MINIAPP) {
            UserMarketingMiniappAccountRelationEntity userMarketingMiniappAccountRelationEntity = this.doGetMiniappAccountRelationByEaAndUid(associationArg);
            if (userMarketingMiniappAccountRelationEntity != null) {
                associationResult.setRelationId(userMarketingMiniappAccountRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingMiniappAccountRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_SERVICE || ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID) {
            UserMarketingWxServiceAccountRelationEntity userMarketingWxServiceAccountRelationEntity = this.doGetWxServiceAccountRelationByEaAndWxAppIdAndWxOpenId(associationArg);
            if (userMarketingWxServiceAccountRelationEntity != null) {
                associationResult.setRelationId(userMarketingWxServiceAccountRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingWxServiceAccountRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_CONTACT) {
            UserMarketingCrmContactAccountRelationEntity userMarketingCrmContactAccountRelationEntity = this.doGetCrmContactAccountRelationByEaAndCrmContactId(associationArg);
            if (userMarketingCrmContactAccountRelationEntity != null) {
                associationResult.setRelationId(userMarketingCrmContactAccountRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingCrmContactAccountRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_LEAD) {
            UserMarketingCrmLeadAccountRelationEntity userMarketingCrmLeadAccountRelationEntity = this.doGetCrmLeadAccountRelationByEaAndCrmLeadId(associationArg);
            if (userMarketingCrmLeadAccountRelationEntity != null) {
                associationResult.setRelationId(userMarketingCrmLeadAccountRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingCrmLeadAccountRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_ACCOUNT) {
            UserMarketingCrmAccountAccountRelationEntity userMarketingCrmAccountAccountRelationEntity = this.doGetCrmAccountAccountRelationByEaAndCrmAccountId(associationArg);
            if (userMarketingCrmAccountAccountRelationEntity != null) {
                associationResult.setRelationId(userMarketingCrmAccountAccountRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingCrmAccountAccountRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER) {
            UserMarketingCrmWxUserAccountRelationEntity userMarketingCrmWxUserAccountRelationEntity = this.doGetCrmWxUserAccountRelationByEaAndCrmWxUserId(associationArg);
            if (userMarketingCrmWxUserAccountRelationEntity != null) {
                associationResult.setRelationId(userMarketingCrmWxUserAccountRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingCrmWxUserAccountRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.BROWSER_USER) {
            UserMarketingBrowserUserRelationEntity userMarketingBrowserUserRelationEntity = this.doGetBrowserUserRelationByEaAndBrowserUserId(associationArg.getEa(), associationArg.getAssociationId());
            if (userMarketingBrowserUserRelationEntity != null) {
                associationResult.setRelationId(userMarketingBrowserUserRelationEntity.getId());
                associationResult.setUserMarketingAccountId(userMarketingBrowserUserRelationEntity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_WORK_EXTERNAL_USER) {
            UserMarketingWxWorkExternalUserRelationEntity entity = this.doGetUserMarketingWxWorkExternalUserRelation(associationArg);
            if (entity != null) {
                associationResult.setRelationId(entity.getId());
                associationResult.setUserMarketingAccountId(entity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_WORK_EXTERNAL_USER) {
            UserMarketingCrmWxWorkExternalUserRelationEntity entity = this.doGetUserMarketingCrmWxWorkExternalUserRelation(associationArg);
            if (entity != null) {
                associationResult.setRelationId(entity.getId());
                associationResult.setUserMarketingAccountId(entity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_MEMBER) {
            UserMarketingCrmMemberRelationEntity entity = this.doGetUserMarketingCrmMemberRelation(associationArg);
            if (entity != null) {
                associationResult.setRelationId(entity.getId());
                associationResult.setUserMarketingAccountId(entity.getUserMarketingId());
            }
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CUSTOMIZE_OBJECT) {
            //自定义对象
            UserMarketingCustomizeObjectRelationEntity entity = this.doGetCrmCustomizeObjectRelationByEaAndObjectdId(associationArg);
            if (entity != null) {
                associationResult.setRelationId(entity.getId());
                associationResult.setUserMarketingAccountId(entity.getUserMarketingId());
            }
        }
        return associationResult;
    }

    public Boolean isExistsByEaAndKeyProperties(AssociationArg associationArg) {
        Object object = null;
        if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.MINIAPP) {
            object = this.doGetMiniappAccountRelationByEaAndUid(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_SERVICE) {
            object = this.doGetWxServiceAccountRelationByEaAndWxAppIdAndWxOpenId(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_CONTACT) {
            object = this.doGetCrmContactAccountRelationByEaAndCrmContactId(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_LEAD) {
            object = this.doGetCrmLeadAccountRelationByEaAndCrmLeadId(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_ACCOUNT) {
            object = this.doGetCrmAccountAccountRelationByEaAndCrmAccountId(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER) {
            object = this.doGetCrmWxUserAccountRelationByEaAndCrmWxUserId(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER_WITH_WX_APPID_AND_WX_OPENID) {
            object = this.doGetWxServiceAccountRelationByEaAndWxAppIdAndWxOpenId(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.BROWSER_USER) {
            object = this.doGetBrowserUserRelationByEaAndBrowserUserId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_WORK_EXTERNAL_USER) {
            object = this.doGetUserMarketingWxWorkExternalUserRelation(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_WORK_EXTERNAL_USER) {
            object = this.doGetUserMarketingCrmWxWorkExternalUserRelation(associationArg);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_MEMBER) {
            object = this.doGetUserMarketingCrmMemberRelation(associationArg);
        }
        return object != null;
    }

    public UserMarketingCrmMemberRelationEntity doGetUserMarketingCrmMemberRelation(AssociationArg associationArg) {
        return userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(associationArg.getEa(), associationArg.getAssociationId());
    }

    private UserMarketingCrmWxWorkExternalUserRelationEntity doGetUserMarketingCrmWxWorkExternalUserRelation(AssociationArg associationArg) {
        return userMarketingCrmWxWorkExternalUserRelationDao.getByEaAndCrmObjectId(associationArg.getEa(), associationArg.getAssociationId());
    }

    private UserMarketingWxWorkExternalUserRelationEntity doGetUserMarketingWxWorkExternalUserRelation(AssociationArg associationArg) {
        return userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(associationArg.getEa(), associationArg.getAssociationId());
    }

    private UserMarketingCrmWxUserAccountRelationEntity doGetCrmWxUserAccountRelationByEaAndCrmWxUserId(AssociationArg associationArg) {
        UserMarketingCrmWxUserAccountRelationEntity userMarketingCrmWxUserAccountRelationEntity = BeanUtil.copy(associationArg, UserMarketingCrmWxUserAccountRelationEntity.class);
        userMarketingCrmWxUserAccountRelationEntity.setCrmWxUserId(associationArg.getAssociationId());
        return userMarketingCrmWxUserAccountRelationDao.getByEaAndCrmWxUserId(userMarketingCrmWxUserAccountRelationEntity);
    }

    private UserMarketingCrmLeadAccountRelationEntity doGetCrmLeadAccountRelationByEaAndCrmLeadId(AssociationArg associationArg) {
        return userMarketingCrmLeadAccountRelationDao.getByEaAndCrmLeadId(associationArg.getEa(), associationArg.getAssociationId());
    }

    private UserMarketingCustomizeObjectRelationEntity doGetCrmCustomizeObjectRelationByEaAndObjectdId(AssociationArg associationArg) {
        return userMarketingCrmCustomizeObjectRelationDao.getByEaAndCustomizeObjectId(associationArg.getEa(), associationArg.getObjectApiName(), associationArg.getAssociationId());
    }

    private UserMarketingCrmAccountAccountRelationEntity doGetCrmAccountAccountRelationByEaAndCrmAccountId(AssociationArg associationArg) {
        UserMarketingCrmAccountAccountRelationEntity userMarketingCrmAccountAccountRelationEntity = BeanUtil.copy(associationArg, UserMarketingCrmAccountAccountRelationEntity.class);
        userMarketingCrmAccountAccountRelationEntity.setCrmAccountId(associationArg.getAssociationId());
        return userMarketingCrmAccountAccountRelationDao.getByEaAndCrmAccountId(userMarketingCrmAccountAccountRelationEntity.getEa(), userMarketingCrmAccountAccountRelationEntity.getCrmAccountId());
    }

    private UserMarketingCrmContactAccountRelationEntity doGetCrmContactAccountRelationByEaAndCrmContactId(AssociationArg associationArg) {
        UserMarketingCrmContactAccountRelationEntity userMarketingCrmContactAccountRelationEntity = BeanUtil.copy(associationArg, UserMarketingCrmContactAccountRelationEntity.class);
        userMarketingCrmContactAccountRelationEntity.setCrmContactId(associationArg.getAssociationId());
        return userMarketingCrmContactAccountRelationDao.getByEaAndCrmContactId(userMarketingCrmContactAccountRelationEntity);
    }

    private UserMarketingWxServiceAccountRelationEntity doGetWxServiceAccountRelationByEaAndWxAppIdAndWxOpenId(AssociationArg associationArg) {
        UserMarketingWxServiceAccountRelationEntity userMarketingWxServiceAccountRelationEntity = BeanUtil.copy(associationArg, UserMarketingWxServiceAccountRelationEntity.class);
        userMarketingWxServiceAccountRelationEntity.setWxOpenId(associationArg.getAssociationId());
        return userMarketingWxServiceAccountRelationDao.getByEaAndWxAppIdAndWxOpenId(userMarketingWxServiceAccountRelationEntity);
    }

    private UserMarketingMiniappAccountRelationEntity doGetMiniappAccountRelationByEaAndUid(AssociationArg associationArg) {
        UserMarketingMiniappAccountRelationEntity userMarketingMiniappAccountRelationEntity = BeanUtil.copy(associationArg, UserMarketingMiniappAccountRelationEntity.class);
        userMarketingMiniappAccountRelationEntity.setUid(associationArg.getAssociationId());
        return userMarketingMiniappAccountRelationDao.getByEaAndUid(userMarketingMiniappAccountRelationEntity.getEa(), userMarketingMiniappAccountRelationEntity.getUid());
    }

    private UserMarketingBrowserUserRelationEntity doGetBrowserUserRelationByEaAndBrowserUserId(String ea, String browserUserId) {
        return userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(ea, browserUserId);
    }

    public String getPhoneByEaAndKeyProperties(AssociationArg associationArg) {
        String phone = null;
        if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.MINIAPP) {
            UserMarketingMiniappAccountRelationEntity entity = this.doGetMiniappAccountRelationByEaAndUid(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_SERVICE) {
            UserMarketingWxServiceAccountRelationEntity entity = this.doGetWxServiceAccountRelationByEaAndWxAppIdAndWxOpenId(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_CONTACT) {
            UserMarketingCrmContactAccountRelationEntity entity = this.doGetCrmContactAccountRelationByEaAndCrmContactId(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_LEAD) {
            UserMarketingCrmLeadAccountRelationEntity entity = this.doGetCrmLeadAccountRelationByEaAndCrmLeadId(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_ACCOUNT) {
            UserMarketingCrmAccountAccountRelationEntity entity = this.doGetCrmAccountAccountRelationByEaAndCrmAccountId(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER) {
            UserMarketingCrmWxUserAccountRelationEntity entity = this.doGetCrmWxUserAccountRelationByEaAndCrmWxUserId(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.BROWSER_USER) {
            UserMarketingBrowserUserRelationEntity entity = this.doGetBrowserUserRelationByEaAndBrowserUserId(associationArg.getEa(), associationArg.getAssociationId());
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_WORK_EXTERNAL_USER) {
            UserMarketingWxWorkExternalUserRelationEntity entity = this.doGetUserMarketingWxWorkExternalUserRelation(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_WORK_EXTERNAL_USER) {
            UserMarketingCrmWxWorkExternalUserRelationEntity entity = this.doGetUserMarketingCrmWxWorkExternalUserRelation(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_MEMBER) {
            UserMarketingCrmMemberRelationEntity entity = this.doGetUserMarketingCrmMemberRelation(associationArg);
            phone = Objects.isNull(entity) ? null : userMarketingAccountDAO.getById(entity.getUserMarketingId()).getPhone();
        }
        return phone;
    }

    public void changeUserMarketingId(String ea, String oldUserMarketingId, String newUserMarketingId, AssociationArg associationArg) {
        if (StringUtils.isEmpty(oldUserMarketingId) || StringUtils.isEmpty(newUserMarketingId)) {
            log.warn("changeUserMarketingId empty,ea={}.oldUserMarketingId={},newUserMarketingId={}", ea, oldUserMarketingId, newUserMarketingId);
            return;
        }
        if (oldUserMarketingId.equals(newUserMarketingId)) {
            log.warn("changeUserMarketingId empty,ea={}.oldUserMarketingId=newUserMarketingId={}", ea, newUserMarketingId);
            return;
        }
        try {
            addUserMarketingAccountChangLog(ea, oldUserMarketingId, newUserMarketingId, associationArg);
            userMarketingMiniappAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingWxServiceAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmContactAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmLeadAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmWxUserAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmAccountAccountRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingBrowserUserRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingWxWorkExternalUserRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmWxWorkExternalUserRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmMemberRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            triggerInstanceDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            userMarketingCrmCustomizeObjectRelationDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
//            excludedFlowUserDao.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
            enterpriseSpreadRecordManager.updateOldUserMarketingIdToNewUserMarketingId(ea, oldUserMarketingId, newUserMarketingId);
        } catch (Exception e) {
            log.warn("Exception", e);
        }
        try {
            List<MarketingUserGroupToUserRelationEntity> relations = marketingUserGroupToUserRelationDao.listAllByMarketingUserId(ea, oldUserMarketingId);
            if (!relations.isEmpty()) {
                for (MarketingUserGroupToUserRelationEntity relation : relations) {
                    relation.setMarketingUserId(newUserMarketingId);
                }
                marketingUserGroupToUserRelationDao.batchInsertEntitiesIgnore(relations);
            }
            marketingUserGroupToUserRelationDao.deleteByMarketingUserId(ea, oldUserMarketingId);
        } catch (Exception e) {
            log.warn("Exception", e);
        }
    }

    private void addUserMarketingAccountChangLog(String ea, String oldUserMarketingId, String newUserMarketingId, AssociationArg associationArg) {
        if(!userMarketingAccountChangeLogManager.isGrayEa(ea)){
            return;
        }
        int maxSize = 1000;
        List<String> dataIds = userMarketingMiniappAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingMiniappAccountRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.MINIAPP.getDescription(), dataIds);
        }

        dataIds = userMarketingWxServiceAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingWxServiceAccountRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.WX_SERVICE.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCrmContactAccountRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CRM_CONTACT.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCrmLeadAccountRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CRM_LEAD.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCrmWxUserAccountRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CRM_WX_USER.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCrmAccountAccountRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CRM_ACCOUNT.getDescription(), dataIds);
        }

        dataIds = userMarketingBrowserUserRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingBrowserUserRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.BROWSER_USER.getDescription(), dataIds);
        }

        dataIds = userMarketingWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingWxWorkExternalUserRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.WX_WORK_EXTERNAL_USER.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCrmMemberRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CRM_MEMBER.getDescription(), dataIds);
        }

        dataIds = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(UserMarketingCustomizeObjectRelationEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, ChannelEnum.CUSTOMIZE_OBJECT.getDescription(), dataIds);
        }

        dataIds = triggerInstanceDao.listByUserMarketingIds(ea, Lists.newArrayList(oldUserMarketingId))
                .stream().map(TriggerInstanceEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dataIds)) {
            if(dataIds.size() > maxSize) {
                dataIds = dataIds.subList(0, maxSize);
            }
            userMarketingAccountChangeLogManager.logUpdateMarketingAccountRelation(newUserMarketingId, oldUserMarketingId, associationArg, "TRIGGER_INSTANCE", dataIds);
        }
    }

    public void delete(AssociationArg associationArg) {
        if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.MINIAPP) {
            userMarketingMiniappAccountRelationDao.deleteByEaAndUid(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_SERVICE) {
            userMarketingWxServiceAccountRelationDao.deleteByEaAndWxAppIdAndWxOpenId(associationArg.getEa(), associationArg.getWxAppId(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_CONTACT) {
            userMarketingCrmContactAccountRelationDao.deleteByEaAndCrmContactId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_LEAD) {
            userMarketingCrmLeadAccountRelationDao.deleteByEaAndCrmLeadId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_ACCOUNT) {
            userMarketingCrmAccountAccountRelationDao.deleteByEaAndCrmAccountId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER) {
            userMarketingCrmWxUserAccountRelationDao.deleteByEaAndCrmWxUserId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.BROWSER_USER) {
            userMarketingBrowserUserRelationDao.deleteByEaAndBrowserUserId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_WORK_EXTERNAL_USER) {
            userMarketingWxWorkExternalUserRelationDao.deleteByEaAndWxWorkExternalUserId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_WORK_EXTERNAL_USER) {
            userMarketingCrmWxWorkExternalUserRelationDao.deleteByEaAndCrmObjectId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_MEMBER) {
            userMarketingCrmMemberRelationDao.deleteByEaAndCrmObjectId(associationArg.getEa(), associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CUSTOMIZE_OBJECT) {
            userMarketingCrmCustomizeObjectRelationDao.deleteByEaAndCrmObjectId(associationArg.getEa(), associationArg.getObjectApiName(), associationArg.getAssociationId());
        }
    }

    public void insert(AssociationArg associationArg, String userMarketingId) {
        log.info("生成营销用户关联关系, arg: {} userMarketingId: {}", associationArg, userMarketingId);
        if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.MINIAPP) {
            UserMarketingMiniappAccountRelationEntity entity = new UserMarketingMiniappAccountRelationEntity();
            entity.setEa(associationArg.getEa());
            entity.setUid(associationArg.getAssociationId());
            entity.setUserMarketingId(userMarketingId);
            entity.setId(UUIDUtil.getUUID());
            userMarketingMiniappAccountRelationDao.insert(entity);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_SERVICE) {
            UserMarketingWxServiceAccountRelationEntity entity = new UserMarketingWxServiceAccountRelationEntity();
            entity.setEa(associationArg.getEa());
            entity.setWxOpenId(associationArg.getAssociationId());
            entity.setWxAppId(associationArg.getWxAppId());
            entity.setUserMarketingId(userMarketingId);
            entity.setId(UUIDUtil.getUUID());
            userMarketingWxServiceAccountRelationDao.insert(entity);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_CONTACT) {
            UserMarketingCrmContactAccountRelationEntity entity = new UserMarketingCrmContactAccountRelationEntity();
            entity.setEa(associationArg.getEa());
            entity.setCrmContactId(associationArg.getAssociationId());
            entity.setUserMarketingId(userMarketingId);
            entity.setId(UUIDUtil.getUUID());
            entity.setUserName(associationArg.getUserName());
            entity.setEmail(associationArg.getEmail());
            userMarketingCrmContactAccountRelationDao.insert(entity);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_LEAD) {
            UserMarketingCrmLeadAccountRelationEntity entity = new UserMarketingCrmLeadAccountRelationEntity();
            entity.setEa(associationArg.getEa());
            entity.setCrmLeadId(associationArg.getAssociationId());
            entity.setUserMarketingId(userMarketingId);
            entity.setId(UUIDUtil.getUUID());
            entity.setUserName(associationArg.getUserName());
            entity.setEmail(associationArg.getEmail());
            userMarketingCrmLeadAccountRelationDao.insert(entity);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_ACCOUNT) {
            UserMarketingCrmAccountAccountRelationEntity entity = new UserMarketingCrmAccountAccountRelationEntity();
            entity.setEa(associationArg.getEa());
            entity.setCrmAccountId(associationArg.getAssociationId());
            entity.setUserMarketingId(userMarketingId);
            entity.setId(UUIDUtil.getUUID());
            entity.setUserName(associationArg.getUserName());
            entity.setEmail(associationArg.getEmail());
            userMarketingCrmAccountAccountRelationDao.insert(entity);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_USER) {
            UserMarketingCrmWxUserAccountRelationEntity entity = new UserMarketingCrmWxUserAccountRelationEntity();
            entity.setEa(associationArg.getEa());
            entity.setCrmWxUserId(associationArg.getAssociationId());
            entity.setUserMarketingId(userMarketingId);
            entity.setId(UUIDUtil.getUUID());
            entity.setUserName(associationArg.getUserName());
            entity.setEmail(associationArg.getEmail());
            userMarketingCrmWxUserAccountRelationDao.insert(entity);
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.BROWSER_USER) {
            userMarketingBrowserUserRelationDao.insertIgnore(UUIDUtil.getUUID(), associationArg.getEa(), userMarketingId, associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.WX_WORK_EXTERNAL_USER) {
            userMarketingWxWorkExternalUserRelationDao.insertIgnore(UUIDUtil.getUUID(), associationArg.getEa(), userMarketingId, associationArg.getAssociationId());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_WX_WORK_EXTERNAL_USER) {
            userMarketingCrmWxWorkExternalUserRelationDao.insertIgnore(UUIDUtil.getUUID(), associationArg.getEa(), userMarketingId, associationArg.getAssociationId(), associationArg.getUserName(), associationArg.getEmail());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CRM_MEMBER) {
            userMarketingCrmMemberRelationDao.insertIgnore(UUIDUtil.getUUID(), associationArg.getEa(), userMarketingId, associationArg.getAssociationId(), associationArg.getUserName(), associationArg.getEmail());
        } else if (ChannelEnum.getByType(associationArg.getType()) == ChannelEnum.CUSTOMIZE_OBJECT) {
            //营销用户关联自定义对象
            if (StringUtils.isNotBlank(associationArg.getObjectApiName())) {
                UserMarketingCustomizeObjectRelationEntity entity = new UserMarketingCustomizeObjectRelationEntity();
                entity.setEa(associationArg.getEa());
                entity.setObjectId(associationArg.getAssociationId());
                entity.setObjectApiName(associationArg.getObjectApiName());
                entity.setUserMarketingId(userMarketingId);
                entity.setId(UUIDUtil.getUUID());
                entity.setUserName(associationArg.getUserName());
                entity.setEmail(associationArg.getEmail());
                userMarketingCrmCustomizeObjectRelationDao.insert(entity);
            }
        }
    }

    public List<String> listByUserMarketingIds(String ea, Integer type, List<String> userMarketingAccountIds) {
        List<String> relationIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userMarketingAccountIds)) {
            return null;
        }
        if (ChannelEnum.CRM_LEAD.getType().equals(type)) {
            relationIds = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmLeadId()).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_ACCOUNT.getType().equals(type)) {
            relationIds = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmAccountId())
                    .collect(Collectors.toList());
        } else if (ChannelEnum.CRM_CONTACT.getType().equals(type)) {
            relationIds = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmContactId())
                    .collect(Collectors.toList());
        } else if (ChannelEnum.CRM_WX_USER.getType().equals(type)) {
            relationIds = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmWxUserId())
                    .collect(Collectors.toList());
        } else if (ChannelEnum.MINIAPP.getType().equals(type)) {
            relationIds = userMarketingMiniappAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getUid()).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType().equals(type)) {
            relationIds = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_MEMBER.getType().equals(type)) {
            relationIds = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId).collect(Collectors.toList());
        }
        return relationIds;
    }

    public Map<String, String> getRelationIdMapByMarketingUserIds(String ea, Integer type, List<String> userMarketingAccountIds) {
        if (CollectionUtils.isEmpty(userMarketingAccountIds)) {
            return new HashMap<>(0);
        }
        if (ChannelEnum.CRM_LEAD.getType().equals(type)) {
            return userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingCrmLeadAccountRelationEntity::getUserMarketingId, UserMarketingCrmLeadAccountRelationEntity::getCrmLeadId, (v1, v2) -> v1));
        } else if (ChannelEnum.CRM_ACCOUNT.getType().equals(type)) {
            return userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingCrmAccountAccountRelationEntity::getUserMarketingId, UserMarketingCrmAccountAccountRelationEntity::getCrmAccountId, (v1, v2) -> v1));
        } else if (ChannelEnum.CRM_CONTACT.getType().equals(type)) {
            return userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingCrmContactAccountRelationEntity::getUserMarketingId, UserMarketingCrmContactAccountRelationEntity::getCrmContactId, (v1, v2) -> v1));
        } else if (ChannelEnum.CRM_WX_USER.getType().equals(type)) {
            return userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingCrmWxUserAccountRelationEntity::getUserMarketingId, UserMarketingCrmWxUserAccountRelationEntity::getCrmWxUserId, (v1, v2) -> v1));
        } else if (ChannelEnum.MINIAPP.getType().equals(type)) {
            return userMarketingMiniappAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingMiniappAccountRelationEntity::getUserMarketingId, UserMarketingMiniappAccountRelationEntity::getUid, (v1, v2) -> v1));
        } else if (ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType().equals(type)) {
            return userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingCrmWxWorkExternalUserRelationEntity::getUserMarketingId, UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId, (v1, v2) -> v1));
        } else if (ChannelEnum.CRM_MEMBER.getType().equals(type)) {
            return userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().collect(Collectors.toMap(UserMarketingCrmMemberRelationEntity::getUserMarketingId, UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId, (v1, v2) -> v1));
        }
        return new HashMap<>(0);
    }

    public List<String> listDataIdsByUserMarketingAccountIds(String ea, Integer type, List<String> userMarketingAccountIds) {
        if (CollectionUtils.isEmpty(userMarketingAccountIds)) {
            return null;
        }
        List<String> dataIds = Lists.newArrayList();
        if (ChannelEnum.CRM_LEAD.getType().equals(type)) {
            dataIds = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmLeadId()).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_ACCOUNT.getType().equals(type)) {
            dataIds = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmAccountId()).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_CONTACT.getType().equals(type)) {
            dataIds = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmContactId()).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_WX_USER.getType().equals(type)) {
            dataIds = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(val -> val.getCrmWxUserId()).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType().equals(type)) {
            dataIds = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(UserMarketingCrmWxWorkExternalUserRelationEntity::getCrmWxWorkExternalUserObjectId).collect(Collectors.toList());
        } else if (ChannelEnum.CRM_MEMBER.getType().equals(type)) {
            dataIds = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(UserMarketingCrmMemberRelationEntity::getCrmMemberObjectId).collect(Collectors.toList());
        } else if (ChannelEnum.CUSTOMIZE_OBJECT.getType().equals(type)) {
            dataIds = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, userMarketingAccountIds).stream().map(UserMarketingCustomizeObjectRelationEntity::getObjectId).collect(Collectors.toList());
        }

        return dataIds;
    }

    public List<String> listUserMarketingAccountIdsByDataIds(String ea, Integer type, List<String> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        List<String> uerMarketingAccountIds = null;
        if (Objects.equals(ChannelEnum.CRM_LEAD.getType(), type)) {
            uerMarketingAccountIds = userMarketingCrmLeadAccountRelationDao.listByLeadIds(ea, dataIds);
        } else if (Objects.equals(ChannelEnum.CRM_ACCOUNT.getType(), type)) {
            uerMarketingAccountIds = userMarketingCrmAccountAccountRelationDao.listByAccountIds(ea, dataIds);
        } else if (Objects.equals(ChannelEnum.CRM_CONTACT.getType(), type)) {
            uerMarketingAccountIds = userMarketingCrmContactAccountRelationDao.listByContactIds(ea, dataIds);
        } else if (Objects.equals(ChannelEnum.CRM_WX_USER.getType(), type)) {
            uerMarketingAccountIds = userMarketingCrmWxUserAccountRelationDao.listByCrmWxUserIds(ea, dataIds);
        } else if (Objects.equals(ChannelEnum.MINIAPP.getType(), type)) {
            uerMarketingAccountIds = userMarketingMiniappAccountRelationDao.listByUids(ea, dataIds);
        } else if (Objects.equals(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType(), type)) {
            uerMarketingAccountIds = userMarketingCrmWxWorkExternalUserRelationDao.listByCrmWxWorkExternalUserIds(ea, dataIds);
        }
        return uerMarketingAccountIds;
    }

    public List<ObjectIdAndDescribeData> listDescribeApiNameByUserMarketingId(String ea, String userMarketingId) {
        List<ObjectIdAndDescribeData> describeApiNames = new ArrayList<>();
        List<UserMarketingCrmLeadAccountRelationEntity> userMarketingCrmLeadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(userMarketingCrmLeadAccountRelationEntities)) {
            userMarketingCrmLeadAccountRelationEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), ChannelEnum.CRM_LEAD.getApiName())).forEach(describeApiNames::add);
        }
        List<UserMarketingCrmAccountAccountRelationEntity> crmAccountAccountRelationEnEntities  = userMarketingCrmAccountAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(crmAccountAccountRelationEnEntities)) {
            crmAccountAccountRelationEnEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), ChannelEnum.CRM_ACCOUNT.getApiName())).forEach(describeApiNames::add);
        }
        List<UserMarketingCrmContactAccountRelationEntity> userMarketingCrmContactAccountRelationEntities = userMarketingCrmContactAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(userMarketingCrmContactAccountRelationEntities)) {
            userMarketingCrmContactAccountRelationEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), ChannelEnum.CRM_CONTACT.getApiName())).forEach(describeApiNames::add);
        }
        List<UserMarketingCrmWxUserAccountRelationEntity> crmWxUserAccountRelationEntities = userMarketingCrmWxUserAccountRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(crmWxUserAccountRelationEntities)) {
            crmWxUserAccountRelationEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), ChannelEnum.CRM_WX_USER.getApiName())).forEach(describeApiNames::add);
        }
        List<UserMarketingCrmWxWorkExternalUserRelationEntity> crmWxWorkExternalUserRelationEntities = userMarketingCrmWxWorkExternalUserRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(crmWxWorkExternalUserRelationEntities)) {
            crmWxWorkExternalUserRelationEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getApiName())).forEach(describeApiNames::add);
        }
        List<UserMarketingCrmMemberRelationEntity> crmMemberRelationEntities = userMarketingCrmMemberRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(crmMemberRelationEntities)) {
            crmMemberRelationEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), ChannelEnum.CRM_MEMBER.getApiName())).forEach(describeApiNames::add);
        }
        List<UserMarketingCustomizeObjectRelationEntity> customizeObjectRelationEntities = userMarketingCrmCustomizeObjectRelationDao.listByUserMarketingIds(ea, Lists.newArrayList(userMarketingId));
        if (CollectionUtils.isNotEmpty(customizeObjectRelationEntities)) {
            customizeObjectRelationEntities.stream().map(entity -> new ObjectIdAndDescribeData(entity.getId(), entity.getObjectApiName())).forEach(describeApiNames::add);
        }

        return describeApiNames;
    }

    public Boolean isRelateCrmObject(String ea, String userMarketingAccountId, List<String> needCheckApiName) {
        for (String apiName : needCheckApiName) {
            if (Objects.equals(ChannelEnum.CRM_LEAD.getApiName(), apiName)) {
                List<UserMarketingCrmLeadAccountRelationEntity> userMarketingCrmLeadAccountRelationEntities = userMarketingCrmLeadAccountRelationDao
                        .listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountId));
                if (CollectionUtils.isNotEmpty(userMarketingCrmLeadAccountRelationEntities)) {
                    return true;
                }
            } else if (Objects.equals(ChannelEnum.CRM_ACCOUNT.getApiName(), apiName)) {
                List<UserMarketingCrmAccountAccountRelationEntity> userMarketingCrmAccountAccountRelationEntities = userMarketingCrmAccountAccountRelationDao
                        .listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountId));
                if (CollectionUtils.isNotEmpty(userMarketingCrmAccountAccountRelationEntities)) {
                    return true;
                }
            } else if (Objects.equals(ChannelEnum.CRM_CONTACT.getApiName(), apiName)) {
                List<UserMarketingCrmContactAccountRelationEntity> userMarketingCrmContactAccountRelationEntities = userMarketingCrmContactAccountRelationDao
                        .listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountId));
                if (CollectionUtils.isNotEmpty(userMarketingCrmContactAccountRelationEntities)) {
                    return true;
                }
            } else if (Objects.equals(ChannelEnum.CRM_WX_USER.getApiName(), apiName)) {
                List<UserMarketingCrmWxUserAccountRelationEntity> userMarketingCrmWxUserAccountRelationEntities = userMarketingCrmWxUserAccountRelationDao
                        .listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountId));
                if (CollectionUtils.isNotEmpty(userMarketingCrmWxUserAccountRelationEntities)) {
                    return true;
                }
            } else if (Objects.equals(ChannelEnum.CRM_MEMBER.getApiName(), apiName)) {
                List<UserMarketingCrmMemberRelationEntity> userMarketingCrmMemberRelations = userMarketingCrmMemberRelationDao
                        .listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountId));
                if (CollectionUtils.isNotEmpty(userMarketingCrmMemberRelations)) {
                    return true;
                }
            } else if (Objects.equals(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getApiName(), apiName)) {
                List<UserMarketingWxWorkExternalUserRelationEntity> userMarketingWxWorkExternalUserRelations = userMarketingWxWorkExternalUserRelationDao
                        .listByUserMarketingIds(ea, Lists.newArrayList(userMarketingAccountId));
                if (CollectionUtils.isNotEmpty(userMarketingWxWorkExternalUserRelations)) {
                    return true;
                }
            }
        }
        return false;
    }

    public UserMarketingCrmAccountAccountRelationEntity getByEaAndCrmAccountId(String ea, String crmAccountId) {
        return userMarketingCrmAccountAccountRelationDao.getByEaAndCrmAccountId(ea, crmAccountId);
    }

    public List<UserMarketingCrmAccountAccountRelationEntity> getByEaAndCrmAccountId(String ea, List<String> crmAccountIds) {
        return userMarketingCrmAccountAccountRelationDao.listUserMarketingCrmAccountAccountRelationEntityByAccountIds(ea, crmAccountIds);
    }

    public String getUserMarketingByOuterUserIdentifyId(String ea, String outerUserIdentifyId, int type) {

        if (ChannelEnum.BROWSER_USER.getType().equals(type)) {
            UserMarketingBrowserUserRelationEntity browserUserRelationEntity = userMarketingBrowserUserRelationDao.getByEaAndBrowserUserId(ea, outerUserIdentifyId);
            if (browserUserRelationEntity != null) {
                return browserUserRelationEntity.getUserMarketingId();
            }
        } else if (ChannelEnum.CRM_MEMBER.getType().equals(type)) {
            UserMarketingCrmMemberRelationEntity memberRelationEntity = userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(ea, outerUserIdentifyId);
            if (memberRelationEntity != null) {
                return memberRelationEntity.getUserMarketingId();
            }
        } else if (ChannelEnum.WX_SERVICE.getType().equals(type)) {
            UserMarketingWxServiceAccountRelationEntity wxServiceAccountRelationEntity = userMarketingWxServiceAccountRelationDao.getByEaAndWxOpenId(ea, outerUserIdentifyId);
            if (wxServiceAccountRelationEntity != null) {
                return wxServiceAccountRelationEntity.getUserMarketingId();
            }
        } else if (ChannelEnum.MINIAPP.getType().equals(type)) {
            UserMarketingMiniappAccountRelationEntity miniappAccountRelationEntity = userMarketingMiniappAccountRelationDao.getByEaAndUid(ea, outerUserIdentifyId);
            if (miniappAccountRelationEntity != null) {
                return miniappAccountRelationEntity.getUserMarketingId();
            }
        } else if (ChannelEnum.WX_WORK_EXTERNAL_USER.getType().equals(type)) {
            UserMarketingWxWorkExternalUserRelationEntity externalUserRelationEntity = userMarketingWxWorkExternalUserRelationDao.getByEaAndWxWorkExternalUserId(ea, outerUserIdentifyId);
            if (externalUserRelationEntity != null) {
                return externalUserRelationEntity.getUserMarketingId();
            }
        }
        return null;
    }

    public UserMarketingCrmMemberRelationEntity getUserMarketingAccountIdByMemberId(String ea, String memberId) {
        return userMarketingCrmMemberRelationDao.getByEaAndCrmObjectId(ea, memberId);
    }

    public UserMarketingCrmMemberRelationEntity getByEaAndUserMarketingId(String ea, String userMarketingId) {
        if (StringUtils.isBlank(ea)) {
            return userMarketingCrmMemberRelationDao.getByUserMarketingId(userMarketingId);
        }
        return userMarketingCrmMemberRelationDao.getByEaAndUserMarketingId(ea, userMarketingId);
    }

    public List<MarketingUserExcludeApinameResult> getExcludeApiNameList(String ea) {
        List<UserMarketingExcludeObjectEntity> entityList = userMarketingExcludeObjectDAO.getByEa(ea);
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>(0);
        }

        List<MarketingUserExcludeApinameResult> results = new ArrayList<>();
        //将entityList构造成MarketingUserGroupExcludeApinameResult对象，然后返回
        for (UserMarketingExcludeObjectEntity entity : entityList) {
            MarketingUserExcludeApinameResult result = new MarketingUserExcludeApinameResult();
            result.setId(entity.getId());
            result.setEa(ea);
            result.setObjectApiName(entity.getObjectApiName());
            result.setObjectName(entity.getObjectName());
            results.add(result);
        }

        return results;
    }

    public Result setExcludeApiName(String ea, Integer fsUserId, String objectApiName, String objectName, Integer status) {
        UserMarketingExcludeObjectEntity entity = userMarketingExcludeObjectDAO.getByObjectApiName(ea, objectApiName);
        if (entity == null && USER_MARKETING_RELATION_EXCLUDE_OBJECT_STATUS == status) {
            //新增
            entity = new UserMarketingExcludeObjectEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setObjectApiName(objectApiName);
            entity.setObjectName(objectName);
            entity.setCreateBy(fsUserId);
            userMarketingExcludeObjectDAO.insert(entity);
        }

        if (entity != null && USER_MARKETING_RELATION_EXCLUDE_OBJECT_STATUS != status) {
            //删除
            userMarketingExcludeObjectDAO.deleteByObjectApiName(ea, objectApiName);
        }
        return Result.newSuccess();
    }

    public boolean isExcludeApiName(String ea, String objectApiName) {
        //默认先支持客户对象
        if (StringUtils.isBlank(objectApiName)) {
            objectApiName = CrmObjectApiNameEnum.CUSTOMER.getName();
        }
        UserMarketingExcludeObjectEntity entity = userMarketingExcludeObjectDAO.getByObjectApiName(ea, objectApiName);
        return entity != null;
    }

    @FilterLog
    public boolean isExcludeApiNameByChannel(String ea, Integer channel) {
        if (ChannelEnum.CRM_ACCOUNT.getType().equals(channel)) {
            UserMarketingExcludeObjectEntity entity = userMarketingExcludeObjectDAO.getByObjectApiName(ea, CrmObjectApiNameEnum.CUSTOMER.getName());
            return entity != null;
        }

        //目前只针对客户对象
        return false;
    }


    public UserMarketingObjectResult doGetRelationFromUserMarketingId(String ea, String marketingUserId){
        UserMarketingObjectResult result = new UserMarketingObjectResult();
        result.setMarketingUserId(marketingUserId);
        String uid = userMarketingMiniappAccountRelationDao.getNewUidByUserMarketingId(ea, marketingUserId);
        if(StringUtils.isNotBlank(uid)){
            result.setTargetId(uid);
            result.setTargetType(ChannelEnum.MINIAPP.getType());
            return result;
        }
        UserMarketingWxServiceAccountRelationEntity openIdByUserMarketingId = userMarketingWxServiceAccountRelationDao.getNewWxOpenIdByUserMarketingId(ea, marketingUserId);
        if(openIdByUserMarketingId != null){
            result.setTargetId(openIdByUserMarketingId.getWxAppId() + ":"+openIdByUserMarketingId.getWxOpenId());
            result.setTargetType(ChannelEnum.WX_SERVICE.getType());
            return result;
        }
        String fingerPrint = userMarketingBrowserUserRelationDao.getNewFingerPrintByUserMarketingId(ea, marketingUserId);
        if(StringUtils.isNotBlank(fingerPrint)){
            result.setTargetId(fingerPrint);
            result.setTargetType(ChannelEnum.BROWSER_USER.getType());
            return result;
        }
        return result;
    }
}
