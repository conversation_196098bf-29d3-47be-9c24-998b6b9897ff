package com.facishare.marketing.provider.manager.usermarketingaccount;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.usermarketingaccount.*;
import com.facishare.marketing.api.result.usermarketingaccount.BatchUpdateObjectTagResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountIdResult;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.service.usermarketingaccounttag.UserMarketingTagService;
import com.facishare.marketing.common.contstant.UserTagConstants;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.TagModelSourceTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.marketingUserGroup.UserMarketingExcludeObjectDAO;
import com.facishare.marketing.provider.dto.TagWithTagModel;
import com.facishare.marketing.provider.entity.MarketingUserGroupCustomizeObjectMappingEntity;
import com.facishare.marketing.provider.entity.TagModelEntity;
import com.facishare.marketing.provider.entity.UserTagEntity;
import com.facishare.marketing.provider.entity.usermarketingaccount.*;
import com.facishare.marketing.provider.innerArg.qywx.AddCorpTagArg;
import com.facishare.marketing.provider.innerResult.qywx.Tag;
import com.facishare.marketing.provider.manager.UserTagManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.fxiaoke.crmrestapi.arg.BulkHangTagForDataByIdArg;
import com.fxiaoke.crmrestapi.service.MetadataTagDataService;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量修改标签管理器
 * 
 * <AUTHOR>
 * @date 2024-12-17
 */
@Slf4j
@Component
public class BatchUpdateObjectTagManager {

    @Autowired
    private TagModelDao tagModelDao;
    
    @Autowired
    private UserTagDao userTagDao;
    
    @Autowired
    private TagModelUserTagRelationDao tagModelUserTagRelationDao;
    
    @Autowired
    private MaterialTagRelationDao materialTagRelationDao;
    
    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;
    @Autowired
    private UserTagManager userTagManager;


    @Autowired
    private MetadataTagManager metadataTagManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private MetadataTagDataService metadataTagDataService;
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private MarketingUserGroupCustomizeObjectMappingDao marketingUserGroupCustomizeObjectMappingDao;
    @Autowired
    private UserMarketingCrmLeadAccountRelationDao userMarketingCrmLeadAccountRelationDao;
    @Autowired
    private UserMarketingCrmWxUserAccountRelationDao userMarketingCrmWxUserAccountRelationDao;
    @Autowired
    private UserMarketingCrmContactAccountRelationDao userMarketingCrmContactAccountRelationDao;
    @Autowired
    private UserMarketingCrmAccountAccountRelationDao userMarketingCrmAccountAccountRelationDao;

    @Autowired
    private UserMarketingCrmWxWorkExternalUserRelationDao userMarketingCrmWxWorkExternalUserRelationDao;
    @Autowired
    private UserMarketingCrmMemberRelationDao userMarketingCrmMemberRelationDao;
    @Autowired
    private UserMarketingCrmCustomizeObjectRelationDao userMarketingCrmCustomizeObjectRelationDao;




    public Result<BatchUpdateObjectTagResult> batchUpdateObjectTag(String ea,  BatchUpdateObjectTagArg arg) {
        try {
            log.info("batchUpdateObjectTag start, ea: {},  arg: {}", ea, arg);
            
            // 1. 参数校验
            Result<BatchUpdateObjectTagResult> validationResult = validateParameters(arg);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }
            
            // 2. 查询营销用户
            Optional<String> userMarketingIdOptional = userMarketingAccountRelationManager
                    .getUserMarketingAccountIdByCrmObjectId(ea, arg.getObjectApiName(), arg.getObjectId());
            if (!userMarketingIdOptional.isPresent()) {
                return Result.newSuccess(BatchUpdateObjectTagResult.failure("失败，没有查询到营销用户"));
            }
            String userMarketingId = userMarketingIdOptional.get();
            
            // 3. 查询标签模型
            TagModelEntity tagModel = tagModelDao.getByEaAndName(ea, arg.getModelName(),0);
            if (tagModel == null) {
                return Result.newSuccess(BatchUpdateObjectTagResult.failure("失败，没有查询到标签模型"));
            }
            
            // 4. 查询标签组
            UserTagEntity tagGroup = userTagDao.getByTagName(ea, UserTagConstants.NO_PARENT_TAG_ID, arg.getTagGroupName(), 0);
            if (tagGroup == null) {
                return Result.newSuccess(BatchUpdateObjectTagResult.failure("失败，没有查询到标签组"));
            }

            // 5. 移除分组标签
            BatchAddOrDeleteTagNamesToCrmDataArg deleteTagArg = new BatchAddOrDeleteTagNamesToCrmDataArg();
            deleteTagArg.setCrmObjectDescribeApiName(arg.getObjectApiName());
            deleteTagArg.setCrmObjectIds(Lists.newArrayList(arg.getObjectId()));
            List<UserTagEntity> deletaTagEntityList = userTagDao.listByParentTagId(ea, tagGroup.getId());
            List<TagName> deleteTagList = deletaTagEntityList.stream().map(o -> new TagName(arg.getTagGroupName(), o.getName())).collect(Collectors.toList());
            deleteTagArg.setTagNames(deleteTagList);
            userMarketingAccountService.batchDeleteTagNamesToCrmData(eieaConverter.enterpriseAccountToId(ea), -10000,deleteTagArg);

            if(CollectionUtils.isEmpty(arg.getTagNames())){
                return Result.newSuccess(BatchUpdateObjectTagResult.success());
            }
            // 6. 验证和处理标签
            List<TagWithTagModel> secondTagEntity = processTargetTags(ea, arg, tagGroup,tagModel.getId());
            if (CollectionUtils.isEmpty(secondTagEntity)) {
                return Result.newSuccess(BatchUpdateObjectTagResult.failure("标签不存在，请在标签库中新建后再尝试"));
            }

            //打赏新标枪
            BatchAddOrDeleteTagNamesToCrmDataArg addTagArg = new BatchAddOrDeleteTagNamesToCrmDataArg();
            addTagArg.setCrmObjectDescribeApiName(arg.getObjectApiName());
            addTagArg.setCrmObjectIds(Lists.newArrayList(arg.getObjectId()));
            List<TagName> addTagList = secondTagEntity.stream().map(o -> new TagName(arg.getTagGroupName(), o.getTagName())).collect(Collectors.toList());
            addTagArg.setTagNames(addTagList);
            userMarketingAccountService.batchAddTagNamesToCrmData(ea,-10000,addTagArg);

            log.info("batchUpdateObjectTag success, ea: {}, objectId: {}, tagCount: {}", 
                    ea, arg.getObjectId(), secondTagEntity.size());
            
            return Result.newSuccess(BatchUpdateObjectTagResult.success());
            
        } catch (Exception e) {
            log.error("batchUpdateObjectTag error, ea: {}, arg: {}", ea, arg, e);
            return Result.newSuccess(BatchUpdateObjectTagResult.failure("系统异常，请稍后重试"));
        }
    }

    /**
     * 参数校验
     */
    private Result<BatchUpdateObjectTagResult> validateParameters(BatchUpdateObjectTagArg arg) {
        if (!CrmObjectApiNameEnum.getAllUserApiNames().contains(arg.getObjectApiName())) {
            return Result.newSuccess(BatchUpdateObjectTagResult.failure("不支持的对象类型"));
        }
        
        if (CollectionUtils.isEmpty(arg.getTagNames())) {
            return Result.newSuccess(BatchUpdateObjectTagResult.failure("标签名称不能为空"));
        }
        
        return Result.newSuccess(null);
    }
    @Autowired
    private UserMarketingTagService userMarketingTagService;
    /**
     * 处理目标标签
     */
    private List<TagWithTagModel> processTargetTags(String ea, BatchUpdateObjectTagArg arg, UserTagEntity tagGroup,String tagModelId) {
        List<TagWithTagModel> targetTags = new ArrayList<>();
        List<TagWithTagModel> secondTags = tagModelDao.listSecondTagsByTagModelIdAndFirstTagId(ea, tagModelId, tagGroup.getId());
        Map<String, TagWithTagModel> tagMap = secondTags.stream().collect(Collectors.toMap(TagWithTagModel::getTagName, e -> e));
        for (String tagName : arg.getTagNames()) {
            TagWithTagModel tag = tagMap.get(tagName);
            if (tag == null) {
                if (!arg.getAddTag()) {
                    // 不允许新建标签，直接返回失败
                    return null;
                } else {
                    // 创建新标签
                    Result<String> stringResult = userMarketingTagService.addSecondTag(ea, -10000, tagModelId, tagGroup.getId(), tagName, TagModelSourceTypeEnum.USER.getValue());
                    if (!stringResult.isSuccess()||stringResult.getData()==null) {
                        return null;
                    }
                    tag = new TagWithTagModel();
                    tag.setTagName(tagName);
                    tag.setParentTagId(tagGroup.getId());
                    tag.setTagModelId(tagModelId);
                    tag.setTagId(stringResult.getData());

//                    if(arg.getObjectApiName().equals(CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName())){
//                        AddCorpTagArg addCorpTagArg = new AddCorpTagArg();
//                        addCorpTagArg.setGroupName(arg.getTagGroupName());
//                        addCorpTagArg.setTags(arg.getTagNames().stream().map(AddCorpTagArg.AddTagArg::new).collect(Collectors.toList());;
//                        qywxManager.addCorpTag(ea, addCorpTagArg).isSuccess();
//                    }
                }
            }
            
            targetTags.add(tag);
        }
        
        return targetTags;
    }

}
