package com.facishare.marketing.provider.service;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.beust.jcommander.internal.Lists;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg;
import com.facishare.marketing.api.arg.usermarketingaction.OutQueryUserMarketingActionArg;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.service.OutUserMarketingActionService;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.api.vo.usermarketingaction.OutUserMarketingActionVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("outUserMarketingActionService")
public class OutUserMarketingActionServiceImpl implements OutUserMarketingActionService {

    @Autowired
    private UserMarketingAccountService userMarketingAccountService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Override
    public FunctionResult<PageResult<OutUserMarketingActionVO>> query(OutQueryUserMarketingActionArg arg) {

        String ea = eieaConverter.enterpriseIdToAccount(arg.getTenantId());

        PageResult<OutUserMarketingActionVO> pageResult = new PageResult<>();
        pageResult.setData(Lists.newArrayList());

        if (StringUtils.isBlank(arg.getObjectApiName()) || StringUtils.isBlank(arg.getObjectId())) {
            return new FunctionResult<>(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_OUTUSERMARKETINGACTIONSERVICEIMPL_55), pageResult);

        }

        ObjectData objectData = crmV2Manager.getDetailIgnoreError(ea, SuperUserConstants.USER_ID, arg.getObjectApiName(), arg.getObjectId());
        if (objectData == null) {
            return new FunctionResult<>(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_OUTUSERMARKETINGACTIONSERVICEIMPL_61), pageResult);
        }

        String userMarketingId = getUserMarketingIdByObjectData(ea, arg.getObjectApiName(), objectData);
        if (StringUtils.isBlank(userMarketingId)) {
            return new FunctionResult<>(-1, I18nUtil.get(I18nKeyEnum.MARK_SERVICE_OUTUSERMARKETINGACTIONSERVICEIMPL_66), pageResult);
        }

        Map<String, String> userMarketingIdToNameMap = userMarketingAccountManager.getMarketingUserNames(ea, Lists.newArrayList(userMarketingId));

        PageUserMarketingActionStatisticArg pageUserMarketingActionStatisticArg = new PageUserMarketingActionStatisticArg();
        pageUserMarketingActionStatisticArg.setUserMarketingId(userMarketingId);
        int pageSize = arg.getPageSize() == null ? 20 : arg.getPageSize();
        pageUserMarketingActionStatisticArg.setPageSize(pageSize);
        int pageNum = arg.getPageNum() == null ? 1 : arg.getPageNum();
        pageUserMarketingActionStatisticArg.setPageNo(pageNum);
        Result<PageResult<UserMarketingActionResult>> userMarketingActionPage = userMarketingAccountService.pageUserMarketingActionStatistic(ea, arg.getFsUserId(), pageUserMarketingActionStatisticArg);
        if (userMarketingActionPage == null || userMarketingActionPage.getData() == null || CollectionUtils.isEmpty(userMarketingActionPage.getData().getData())) {
            return new FunctionResult<>(SHErrorCode.SUCCESS.getErrorCode(), SHErrorCode.SUCCESS.getErrorMessage(), pageResult);
        }
        pageResult.setTotalCount(userMarketingActionPage.getData().getTotalCount());
        List<UserMarketingActionResult> userMarketingActionResultList = userMarketingActionPage.getData().getData();
        List<OutUserMarketingActionVO> resultList = BeanUtil.copy(userMarketingActionResultList, OutUserMarketingActionVO.class);
        for (OutUserMarketingActionVO outUserMarketingActionVO : resultList) {
            outUserMarketingActionVO.setUserName(userMarketingIdToNameMap.get(outUserMarketingActionVO.getUserMarketingId()));
            ObjectTypeEnum objectTypeEnum = ObjectTypeEnum.getByType(outUserMarketingActionVO.getObjectType());
            if (objectTypeEnum != null) {
                outUserMarketingActionVO.setObjectTypeName(objectTypeEnum.getName());
            }
        }
        pageResult.setData(resultList);
        return new FunctionResult<>(SHErrorCode.SUCCESS.getErrorCode(), SHErrorCode.SUCCESS.getErrorMessage(), pageResult);
    }

    private String getUserMarketingIdByObjectData(String ea, String crmObjectApiName, ObjectData objectData) {

        List<String> userMarketingAccountIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(ea, crmObjectApiName, Collections.singletonList(objectData), true);
        if (CollectionUtils.isEmpty(userMarketingAccountIds)) {
            log.warn("OutUserMarketingActionServiceImpl getUserMarketingIdByObjectData error, ea: {} objectData:{}", ea, objectData);
            return null;
        }
        return userMarketingAccountIds.get(0);
    }
}