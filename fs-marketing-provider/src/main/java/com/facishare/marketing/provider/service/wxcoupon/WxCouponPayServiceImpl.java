package com.facishare.marketing.provider.service.wxcoupon;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.PartnerNoticeSendArg;
import com.facishare.marketing.api.arg.kis.RecordActionArg;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.qywx.staff.QueryUserCardDetailResult;
import com.facishare.marketing.api.result.wxcoupon.*;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.api.service.wxcoupon.PublicCouponService;
import com.facishare.marketing.api.service.wxcoupon.WxCouponPayService;
import com.facishare.marketing.api.util.AmountUtil;
import com.facishare.marketing.api.vo.wxcoupon.*;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.coupon.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.exception.BusinessException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.util.AESUtil;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.PageUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.WxServiceUserMemberBindDao;
import com.facishare.marketing.provider.dao.marketingplugin.CouponTemplateDAO;
import com.facishare.marketing.provider.dao.marketingplugin.MemberCouponBindDAO;
import com.facishare.marketing.provider.dao.marketingplugin.SendDealerCouponRecordDAO;
import com.facishare.marketing.provider.dao.marketingplugin.WeChatCouponDAO;
import com.facishare.marketing.provider.dao.param.coupon.QueryCouponParam;
import com.facishare.marketing.provider.dao.pay.MerchantConfigDao;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.entity.marketingplugin.CouponTemplateEntity;
import com.facishare.marketing.provider.entity.marketingplugin.MemberCouponBindEntity;
import com.facishare.marketing.provider.entity.marketingplugin.SendDealerCouponRecordEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.entity.pay.MerchantConfigEntity;
import com.facishare.marketing.provider.innerArg.crm.PaasQueryCouponArg;
import com.facishare.marketing.provider.innerArg.crm.PaasQueryUserCouponArg;
import com.facishare.marketing.provider.innerData.SpreadContentEventData;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.coupon.PublicCouponManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.CouponDistributionObjManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.metadata.PublicMetadataManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.i18n.I18NUtil;
import com.facishare.marketing.provider.util.i18n.MarketingI18NKeyUtil;
import com.facishare.marketing.provider.util.wxpay.AesUtil;
import com.facishare.marketing.provider.util.wxpay.AuthSignUtils;
import com.facishare.marketing.provider.util.wxpay.SignUtil;
import com.facishare.marketing.provider.util.wxpay.TimeUtil;
import com.facishare.marketing.statistic.outapi.constants.NewActionTypeEnum;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.fxiaoke.enterpriserelation2.arg.BatchGetEaByOuterTenantIdArg;
import com.fxiaoke.enterpriserelation2.arg.BatchGetEnterpriseShortNamesArg;
import com.fxiaoke.enterpriserelation2.arg.GetEaByOuterTenantIdArg;
import com.fxiaoke.enterpriserelation2.arg.UpstreamAndDownstreamOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.arg.WxAppIdAndOpenIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.BatchGetEaByOuterTenantIdResult;
import com.fxiaoke.enterpriserelation2.result.BatchGetEnterpriseShortNamesResult;
import com.fxiaoke.enterpriserelation2.result.EnterpriseSimpleVo;
import com.fxiaoke.enterpriserelation2.result.OuterAccountResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.UnionAccountService;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.otherrestapi.syncservice.arg.SyncDataByDownStreamTenantIdArg;
import com.fxiaoke.otherrestapi.syncservice.result.SyncResult;
import com.fxiaoke.otherrestapi.syncservice.service.SyncService;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/9/14 17:30
 */
@Service("wxCouponPayService")
@Slf4j
public class WxCouponPayServiceImpl implements WxCouponPayService {

    @Autowired
    private WxCouponPayManager wxCouponPayManager;

    @Autowired
    private MerchantConfigDao merchantConfigDao;

    @Autowired
    private HttpManager httpManager;

    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;

    @Autowired
    private WeChatCouponDAO weChatCouponDAO;

    @Autowired
    private MemberCouponBindDAO memberCouponBindDAO;

    @Autowired
    private AesUtil aesUtil;

    @Autowired
    private WxServiceUserMemberBindDao wxServiceUserMemberBindDao;

    @Autowired
    private UnionAccountService unionAccountService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private CouponTemplateManager couponTemplateManager;

    @Autowired
    private FsAddressBookManager fsAddressBookManager;

    @Autowired
    private CouponTemplateDAO couponTemplateDAO;

    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private NoticeService noticeService;

    @Autowired
    private SendDealerCouponRecordDAO sendDealerCouponRecordDAO;

    @Autowired
    private EnterpriseRelationService enterpriseRelationService;

    @Autowired
    private SyncService syncService;

    @Autowired
    private FxiaokeAccountService fxiaokeAccountService;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;

    @Autowired
    private CouponDistributionObjManager couponDistributionObjManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private PublicMetadataManager publicMetadataManager;

    @Autowired
    private PublicCouponService publicCouponService;

    @Autowired
    private PublicCouponManager publicCouponManager;


    @Value("${partner.appid}")
    private String partnerAppId;

    private static final String MERCHANT_CONFIG_AES_KEY = "oU9g_pS6AS0WAgZgpO8-Sw";

    private static final String COUPON_LOCK_PREFIX = "COUPON_LOCK_";

    @Override
    public Result<CreateCouponResult> createWxCouponStock(CreateWxCouponVO vo) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getTemplateId()),I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_215));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getMarketingEventId()),I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_216));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getAvailableBeginTime()),I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_217));
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getAvailableEndTime()),I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_WXCOUPONPAYSERVICEIMPL_218));
        //处理时间
        vo.setAvailableBeginTime(TimeUtil.longTime2Format(vo.getAvailableBeginTime(),"yyyy-MM-dd'T'HH:mm:ssXXX"));
        vo.setAvailableEndTime(TimeUtil.longTime2Format(vo.getAvailableEndTime(),"yyyy-MM-dd'T'HH:mm:ssXXX"));
        CreateCouponResult createCouponResult;
        try {
            //根据模板保存的ea信息,查询商户平台配置信息
            MerchantConfigEntity merchantConfigEntity = merchantConfigDao.getMerchantSimpleInfoByEa(vo.getEa());
            if (merchantConfigEntity ==null){
                return Result.newError(SHErrorCode.MERCHANT_NOT_CONFIG_YET);
            }
            CouponTemplateEntity couponTemplateEntity = couponTemplateDAO.queryCouponTemplateInfo(vo.getTemplateId());

            //生成商户请求单号
            String outRequestNo = merchantConfigEntity.getMerchantId()+ DateUtil.getTimeStamp(new Date())+ UUIDUtil.getUUID();

            //获取商户号,证书序列号,秘钥
            String merchantId = merchantConfigEntity.getMerchantId();
            String serialNo = merchantConfigEntity.getSerialNo();
            String privateKeyStr = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfigEntity.getApiclientKey());
            String apv3Key = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfigEntity.getAppV3Secret());

            //上传图片
            if (StringUtils.isNotBlank(vo.getMerchantLogoUrl())) {
                if (!vo.getMerchantLogoUrl().startsWith("A_")) {
                    vo.setMerchantLogoUrl(couponTemplateEntity.getMerchantLogoUrl());
                }
                vo.setMerchantLogoUrl(wxCouponPayManager.uploadImage(vo.getMerchantLogoUrl(),merchantConfigEntity.getEa(),merchantId, serialNo, privateKeyStr, apv3Key));
            }

            if (StringUtils.isNotBlank(vo.getCouponImageUrl())) {
                if (!vo.getCouponImageUrl().startsWith("A_")) {
                    vo.setCouponImageUrl(couponTemplateEntity.getCouponImageUrl());
                }
                vo.setCouponImageUrl(wxCouponPayManager.uploadImage(vo.getCouponImageUrl(),merchantConfigEntity.getEa(), merchantId, serialNo, privateKeyStr, apv3Key));
            }

            log.info("deal with after request vo ={}",GsonUtil.getGson().toJson(vo));
            //组装请求参数
            PostCouponBody postCouponBody = buildRequestBody(vo,merchantConfigEntity,outRequestNo);
            String createCouponUrl = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/stocks";
            Map<String, String> headerMap = new HashMap<>(16);
            String body = postCouponBody.getPostData();

            //生成签名
            String auth = AuthSignUtils.getToken(merchantId, serialNo, "POST", createCouponUrl, body, privateKeyStr);
            headerMap.put("Authorization",auth);
            headerMap.put("accept", "*/*");
            headerMap.put("connection", "Keep-Alive");
            headerMap.put("user-agent","Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

            log.info("request weChat API body={}",GsonUtil.getGson().toJson(postCouponBody));
            //请求微信API(创建商家券)
            createCouponResult = httpManager.executePostHttpWithRequestBodyAndHeader(postCouponBody.getFormBody(), createCouponUrl, new TypeToken<CreateCouponResult>() {}, headerMap);
            if (createCouponResult == null){
                return Result.newError(SHErrorCode.WX_REQUEST_API_ERROR);
            }
            String stockId = createCouponResult.getStockId();
            log.info("create weChat stock success :stockId={}",stockId);

            //创建商家券成功,将商家券数据同步到CRM WechatCouponObj
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResultResult =  wxCouponPayManager.createWeChatCouponObj(vo,stockId,outRequestNo,merchantId);
            if (!addResultResult.isSuccess() || addResultResult.getData() == null){
                log.warn("sync CRM weChatCouponObj fail result:{}",addResultResult);
                return Result.newError(SHErrorCode.CRM_BUSINESS_ERROR);
            }
            log.info("sync CRM weChatCouponObj success");

            //同时同步CRM 优惠券 CouponObj
            ActionAddResult actionAddResult = addResultResult.getData();
            String wechatCouponId = actionAddResult.getObjectData().getId();

            //将优惠券数据保存在本地库
            WechatCouponEntity weChatCouponEntity = BeanUtil.copy(vo,WechatCouponEntity.class);
            weChatCouponEntity.setWechatCouponId(wechatCouponId);
            weChatCouponEntity.setStockId(stockId);
            weChatCouponEntity.setId(UUIDUtil.getUUID());
            weChatCouponEntity.setOutRequestNo(outRequestNo);
            weChatCouponEntity.setBelongMerchant(merchantConfigEntity.getMerchantId());
            weChatCouponEntity.setCouponId(wechatCouponId);
            weChatCouponEntity.setOperator(vo.getOperator());
            if (vo.getPartnerNoticeVisibilityVO() != null) {
                weChatCouponEntity.setSendScope(GsonUtil.getGson().toJson(vo.getPartnerNoticeVisibilityVO()));
            }
            int saveResult = weChatCouponDAO.saveWeChatCoupon(weChatCouponEntity);
            log.info("save local table success result={}",saveResult);

            //上报神策埋点
            marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(vo.getEa(), vo.getOperator(), ObjectTypeEnum.WX_COUPON.getType(), null, weChatCouponEntity.getId()));
        } catch (Exception e) {
            log.warn("WxCouponPayServiceImpl.createWxCouponStock error",e);
            throw new BusinessException(SHErrorCode.SYSTEM_ERROR);
        }

        return Result.newSuccess(createCouponResult);
    }

    @Override
    public Result<CreateCouponResult> createPartnerCoupon(CreateWxCouponVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.createPublicCoupon(vo);
        }
        //生成stockId
        String stockId = vo.getEa() + DateUtil.getTimeStamp(new Date())+ UUIDUtil.getUUID();
        //处理发送范围
        if (SceneEnum.DEALER.getType() == vo.getScene() && vo.getAccountVisibilityVO() != null) {
            CreateWxCouponVO.AccountVisibilityVO accountVisibilityVO = vo.getAccountVisibilityVO();
            List<String> outerTenantIds = wxCouponPayManager.getOutTenantIds(vo.getEa(), accountVisibilityVO.getType(), accountVisibilityVO.getValue());
            if (CollectionUtils.isEmpty(outerTenantIds)) {
                return Result.newError(SHErrorCode.NOT_FOUND_SEND_ACCOUNT);
            }
            if (vo.getPartnerNoticeVisibilityVO() == null) {
                CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = new CreateWxCouponVO.PartnerNoticeVisibilityVO();
                partnerNoticeVisibilityVO.setOuterTenantIds(outerTenantIds);
                vo.setPartnerNoticeVisibilityVO(partnerNoticeVisibilityVO);
            }
        }
        //处理门店领取范围
        if (vo.getStoreReceiveVisibilityVO() != null) {
            CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = vo.getStoreReceiveVisibilityVO();
            int countStore = publicCouponManager.getStoreAccountIdsCount(vo.getEa(), storeReceiveVisibilityVO.getType(), storeReceiveVisibilityVO.getValue(),VisibilityEnum.RECEIVE_SCOPE.getType());
            if (countStore <= 0) {
                return Result.newError(SHErrorCode.NOT_FOUND_RECEIVE_ACCOUNT);
            }
        }

        //同步数据到couponObj 对象
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> couponAddResult = wxCouponPayManager.createCouponObj(vo,false);
        if (!couponAddResult.isSuccess() || couponAddResult.getData() == null){
            log.warn("partner Coupon sync CRM CouponObj fail  result:{}",couponAddResult);
            return Result.newError(SHErrorCode.CRM_BUSINESS_ERROR);
        }
        //将优惠券数据保存在本地库
        WechatCouponEntity weChatCouponEntity = BeanUtil.copy(vo,WechatCouponEntity.class);
        weChatCouponEntity.setWechatCouponId(vo.getPricePolicyId());
        weChatCouponEntity.setStockId(stockId);
        weChatCouponEntity.setId(UUIDUtil.getUUID());
        weChatCouponEntity.setOutRequestNo(stockId);
        weChatCouponEntity.setCouponId(couponAddResult.getData().getObjectData().getId());
        weChatCouponEntity.setOperator(vo.getOperator());
        weChatCouponEntity.setCouponNo(couponAddResult.getData().getObjectData().getName());
        //如果scene = 3 设置为未发送状态 0: 未下发  1: 下发
        if (SceneEnum.DEALER.getType() == vo.getScene()) {
            weChatCouponEntity.setSendDownStatus(SendDownStatusEnum.UN_SEND.getType());
        }
        if (vo.getPartnerNoticeVisibilityVO() != null) {
            weChatCouponEntity.setSendScope(GsonUtil.getGson().toJson(vo.getPartnerNoticeVisibilityVO()));
        }
        if (vo.getAccountVisibilityVO() != null) {
            weChatCouponEntity.setAccountScope(GsonUtil.getGson().toJson(vo.getAccountVisibilityVO()));
        }
        if (vo.getStoreReceiveVisibilityVO() != null) {
            weChatCouponEntity.setReceiveScope(GsonUtil.getGson().toJson(vo.getStoreReceiveVisibilityVO()));
        }
        if (vo.getStoreVisibilityVO() != null) {
            weChatCouponEntity.setStoreScope(GsonUtil.getGson().toJson(vo.getStoreVisibilityVO()));
        }
        weChatCouponDAO.saveWeChatCoupon(weChatCouponEntity);
        CreateCouponResult createCouponResult = new CreateCouponResult();
        createCouponResult.setStockId(stockId);
        createCouponResult.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //异步处理领取门店范围数据
        if (vo.getStoreReceiveVisibilityVO() != null) {
            ThreadPoolUtils.executeWithTraceContext(()->{
                CreateWxCouponVO.StoreReceiveVisibilityVO storeReceiveVisibilityVO = vo.getStoreReceiveVisibilityVO();
                List<String> storeAccountIds = publicCouponManager.getStoreAccountIds(vo.getEa(), storeReceiveVisibilityVO.getType(), storeReceiveVisibilityVO.getValue(),VisibilityEnum.RECEIVE_SCOPE.getType());
                CreateWxCouponVO.StoreVisibilityVO storeVisibilityVO = new CreateWxCouponVO.StoreVisibilityVO();
                storeVisibilityVO.setAccountIds(storeAccountIds);
                weChatCouponDAO.updateStoreScope(weChatCouponEntity.getId(),GsonUtil.getGson().toJson(storeVisibilityVO));
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        }
        return Result.newSuccess(createCouponResult);
    }

    @Override
    public Result<Void> stockCouponsReceiveCallback(String ea,String params, String wechatpaySerial, String wechatpaySignature, String wechatpayTimestamp, String wechatpayNonce) {
        //通过ea,获取商户平台信息
        MerchantConfigEntity merchantConfigEntity = merchantConfigDao.getMerchantSimpleInfoByEa(ea);
        if (merchantConfigEntity == null){
            return Result.newError(SHErrorCode.NO_COUPON_TEMPLATE);
        }
        String merchantId = merchantConfigEntity.getMerchantId();
        String serialNo = merchantConfigEntity.getSerialNo();
        String privateKeyStr = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfigEntity.getApiclientKey());
        String apv3Key = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfigEntity.getAppV3Secret());

        CallbackParams callbackParams = GsonUtil.getGson().fromJson(params, new TypeToken<CallbackParams>(){}.getType());
        //对响应验签，和应答签名做比较，使用微信平台证书.
//        boolean verify = aesUtil.verifySign(params, wechatpaySerial, wechatpaySignature, wechatpayTimestamp, wechatpayNonce, merchantId, serialNo, privateKeyStr, apv3Key);
//        if(!verify){
//            //签名验证失败
//            return Result.newError(SHErrorCode.VERIFY_SIGN_ERROR);
//        }
        String eventType = callbackParams.getEventType();
        if (!Objects.equals(eventType, CallBackTypeEnum.RECEIVE_COUPON.getName())) {
            log.error("weChat callback type is error,no handle");
            //回调类型错误
            return Result.newError(SHErrorCode.CALLBACK_TYPE_ERROR);
        }

        //解密
        String data = aesUtil.decrypt(callbackParams,apv3Key);

        StockCouponReceiveConsumeRequest consumeData = GsonUtil.getGson().fromJson(data, new TypeToken<StockCouponReceiveConsumeRequest>(){}.getType());

        log.info("解密后数据: consumeData={}",GsonUtil.getGson().toJson(consumeData));
        //领券回调解密后,将数据同步到CRM UserCouponObj

        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryWeChatCouponByStockId(consumeData.getStockId());
        Integer isMember = wechatCouponEntity.getIsMember();

        String memberId = null;
        String partnerId = null;
        String accountId = null;
        if (MemberLimitEnum.NO.getType() == isMember){
            //所有人可领取  先查伙伴营销身份, 如果没有再查会员身份
            //1.先根据 openid 和 appid 查询 下游企业id
            int ei = eieaConverter.enterpriseAccountToId(ea);
            WxAppIdAndOpenIdOutArg wxOutArg = new WxAppIdAndOpenIdOutArg();
            wxOutArg.setWxOpenId(consumeData.getOpenid());
            wxOutArg.setWxAppId(merchantConfigEntity.getAppId());
            wxOutArg.setIdentityType(2);
            com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(ei);
            headerObj.setAppId(merchantConfigEntity.getAppId());
            RestResult<OuterAccountResult> erIdentifierByWxServiceMsg = unionAccountService.getERIdentifierByWxServiceMsg(headerObj, wxOutArg);
            log.info("all 互联获取下游企业id result={}",erIdentifierByWxServiceMsg);
            if (erIdentifierByWxServiceMsg.isSuccess() && erIdentifierByWxServiceMsg.getData() != null) {
                partnerId = String.valueOf(erIdentifierByWxServiceMsg.getData().getOuterTenantId());
                accountId = String.valueOf(erIdentifierByWxServiceMsg.getData().getOuterUid());
                log.info("all 获取的下游企业id partnerId = {}",partnerId);
                log.info("all 获取的伙伴 outUid = {}",accountId);
            } else {
                //2.然后再根据openid,appid 查询 会员信息
                memberId = wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea,merchantConfigEntity.getAppId(), consumeData.getOpenid());
                log.info("会员id memberId={}",memberId);
            }
        }
        else if (MemberLimitEnum.MEMBER.getType() == isMember) {
            //仅会员可领取  根据openid,appid 查询 会员信息
            memberId = wxServiceUserMemberBindDao.getMemberIdByWxUserInfo(ea,merchantConfigEntity.getAppId(), consumeData.getOpenid());
        } else if (MemberLimitEnum.PARTNER.getType() == isMember) {
            //仅伙伴营销可领取 根据 openid 和 appid 查询 下游企业id
            int ei = eieaConverter.enterpriseAccountToId(ea);
            WxAppIdAndOpenIdOutArg wxOutArg = new WxAppIdAndOpenIdOutArg();
            wxOutArg.setWxOpenId(consumeData.getOpenid());
            wxOutArg.setWxAppId(merchantConfigEntity.getAppId());
            wxOutArg.setIdentityType(2);
            com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(ei);
            headerObj.setAppId(merchantConfigEntity.getAppId());
            RestResult<OuterAccountResult> erIdentifierByWxServiceMsg = unionAccountService.getERIdentifierByWxServiceMsg(headerObj, wxOutArg);
            log.info("partner 请求互联获取下游企业id result={}",erIdentifierByWxServiceMsg);
            if (erIdentifierByWxServiceMsg.isSuccess() && erIdentifierByWxServiceMsg.getData() != null) {
                partnerId = String.valueOf(erIdentifierByWxServiceMsg.getData().getOuterTenantId());
                accountId = String.valueOf(erIdentifierByWxServiceMsg.getData().getOuterUid());
                log.info("partner 获取的下游企业id partnerId = {}",partnerId);
                log.info("partner 获取的伙伴 outUid = {}",accountId);
            }
        }
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> userCouponAddResult = wxCouponPayManager.createUserCouponObj(consumeData,memberId,ea,partnerId,accountId,merchantConfigEntity.getAppId());
        if (!userCouponAddResult.isSuccess() || userCouponAddResult.getData() == null){
            log.warn("sync CRM UserCouponObj fail  result:{}",userCouponAddResult);
            return Result.newError(SHErrorCode.CRM_BUSINESS_ERROR);
        }
        //保存到本地库
        MemberCouponBindEntity memberCouponEntity = BeanUtil.copy(consumeData,MemberCouponBindEntity.class);
        memberCouponEntity.setEa(ea);
        memberCouponEntity.setCouponId(wechatCouponEntity.getStockId());
        memberCouponEntity.setId(UUIDUtil.getUUID());
        memberCouponEntity.setMemberId(memberId);
        memberCouponEntity.setAppid(merchantConfigEntity.getAppId());
        memberCouponEntity.setPartnerId(partnerId);
        memberCouponEntity.setAccountId(accountId);
        memberCouponEntity.setStatus("1");
        memberCouponBindDAO.saveMemberCouponBind(memberCouponEntity);
        return Result.newSuccess();
    }

    @Override
    public Result<String> uploadImageV3(String ea, String filePath) {
        //通过ea,获取商户平台信息
        MerchantConfigEntity merchantConfig = merchantConfigDao.getMerchantSimpleInfoByEa(ea);
        if (merchantConfig == null){
            return Result.newError(SHErrorCode.MERCHANT_NOT_CONFIG_YET);
        }
        String merchantId = merchantConfig.getMerchantId();
        String serialNo = merchantConfig.getSerialNo();
        String privateKeyStr = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfig.getApiclientKey());
        String apv3Key = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfig.getAppV3Secret());

        //上传图片
        String uploadImageUrl = wxCouponPayManager.uploadImage(filePath,merchantConfig.getEa(),merchantId, serialNo, privateKeyStr, apv3Key);
        return Result.newSuccess(uploadImageUrl);
    }

    @Override
    public Result<CallBackNotifyResult> configureCallBackUrl(String notifyUrl, String ea) {
        CallBackNotifyResult callBackNotifyResult;
        try {
            //获取商户平台信息
            MerchantConfigEntity merchantConfig = merchantConfigDao.getMerchantSimpleInfoByEa(ea);
            if (merchantConfig == null){
                return Result.newError(SHErrorCode.MERCHANT_NOT_CONFIG_YET);
            }
            String merchantId = merchantConfig.getMerchantId();
            String serialNo = merchantConfig.getSerialNo();
            String privateKeyStr = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfig.getApiclientKey());

            //请求API url
            String callBackUrl = "https://api.mch.weixin.qq.com/v3/marketing/busifavor/callbacks";

            //封装参数
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("mchid",merchantId);
            jsonObject.put("notify_url",notifyUrl+"/"+ea);
            String postData = jsonObject.toJSONString();

            //生成签名
            String auth = AuthSignUtils.getToken(merchantId, serialNo, "POST", callBackUrl, postData, privateKeyStr);

            //生成请求体
            RequestBody requestBody = RequestBody.create(null, postData);

            Map<String,String> headerMap = new HashMap<>(16);
            headerMap.put("Authorization",auth);
            headerMap.put("accept", "*/*");
            headerMap.put("connection", "Keep-Alive");
            headerMap.put("user-agent","Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            //请求微信API
            callBackNotifyResult = httpManager.executePostHttpWithRequestBodyAndHeader(requestBody,callBackUrl,new TypeToken<CallBackNotifyResult>(){},headerMap);
            if (callBackNotifyResult == null){
                return Result.newError(SHErrorCode.WX_REQUEST_API_ERROR);
            }
        } catch (Exception e) {
            log.warn("WxCouponPayServiceImpl.configureCallBackUrl error",e);
            throw new BusinessException(SHErrorCode.SYSTEM_ERROR);
        }
        return Result.newSuccess(callBackNotifyResult);
    }

    @Override
    public Result<PageResult<UserCouponListResult>> queryUserCouponList(String ea, QueryUserCouponListVo vo) {
        PageResult<UserCouponListResult> userCouponPageResult = new PageResult<>();
        List<UserCouponListResult> result = Lists.newArrayList();
        userCouponPageResult.setPageNum(vo.getPageNum());
        userCouponPageResult.setPageSize(vo.getPageSize());
        userCouponPageResult.setTotalCount(0);
        PaasQueryUserCouponArg arg = new PaasQueryUserCouponArg();
        if (CouponQueryTypeEnum.MEMBER.getType() == vo.getQueryType()) {
            arg.setMemberId(vo.getMemberId());
        } else if (CouponQueryTypeEnum.PARTNER.getType() == vo.getQueryType()){
            arg.setAccountId(vo.getEROuterUid());
            arg.setPartnerId(vo.getEROuterTenantId());
        }
        arg.setObjectApiName(CrmObjectApiNameEnum.USER_COUPON_OBJ.getName());
        arg.setPageNumber(vo.getPageNum());
        arg.setPageSize(vo.getPageSize());
        if (StringUtils.isNotBlank(vo.getStatus())){
            arg.setStatus(vo.getStatus());
        }
        Page<ObjectData> userCouponList = wxCouponPayManager.listUserCoupon(ea, null, arg);
        if (userCouponList == null || CollectionUtils.isEmpty(userCouponList.getDataList())){
            userCouponPageResult.setResult(result);
            return Result.newSuccess(userCouponPageResult);
        }
        //数据处理
        userCouponList.getDataList().stream().forEach(objectData -> {
            String objectDataStr = GsonUtil.getGson().toJson(objectData);
            UserCouponResult userCouponResult = GsonUtil.getGson().fromJson(objectDataStr, UserCouponResult.class);
            WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryWeChatCouponByStockId(userCouponResult.getCouponId());
            UserCouponListResult userCouponListResult = BeanUtil.copy(wechatCouponEntity, UserCouponListResult.class);
            if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(wechatCouponEntity.getStockType())){
                userCouponListResult.setType(CouponTypeEnum.EXCHANGE_COUPON.getType());
            } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(wechatCouponEntity.getStockType())){
                userCouponListResult.setType(CouponTypeEnum.DISCOUNT_COUPON.getType());
            } else if (CouponTypeEnum.NORMAL_COUPON.getName().equals(wechatCouponEntity.getStockType())){
                userCouponListResult.setType(CouponTypeEnum.NORMAL_COUPON.getType());
            }
            userCouponListResult.setTransactionMinimum(AmountUtil.changeF2Y(wechatCouponEntity.getTransactionMinimum()));
            if (wechatCouponEntity.getDiscountAmount() != null) {
                userCouponListResult.setDiscountAmount(AmountUtil.changeF2Y(wechatCouponEntity.getDiscountAmount()));
            }
            if (wechatCouponEntity.getExchangePrice() != null) {
                userCouponListResult.setExchangePrice(AmountUtil.changeF2Y(wechatCouponEntity.getExchangePrice()));
            }
            if (wechatCouponEntity.getDiscountPercent() != null) {
                userCouponListResult.setDiscountPercent(AmountUtil.changeCount(wechatCouponEntity.getDiscountPercent()));
            }
            userCouponListResult.setObjectId(wechatCouponEntity.getId());
            result.add(userCouponListResult);
        });
        userCouponPageResult.setTotalCount(userCouponList.getTotal());
        userCouponPageResult.setResult(result);
        return Result.newSuccess(userCouponPageResult);
    }

    @Override
    public Result<PageResult<CouponResult>> queryCouponList(String ea, QueryCouponListVo vo) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryCouponList(ea,vo);
        }
        PageResult<CouponResult> couponPageResult = new PageResult<>();
        List<CouponResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        MerchantConfigEntity configEntity = merchantConfigDao.getMerchantSimpleInfoByEa(ea);
        //查询本地数据库中的数据
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryList(vo.getMarketingEventId(),ea,vo.getScene(),page);
        if (CollectionUtils.isEmpty(couponEntities)) {
            return  Result.newSuccess(couponPageResult);
        }
        //批量获取优惠券方案
        List<String> couponPlanIds = couponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(ea,couponPlanIds);
        //批量获取优惠券使用数量
        List<String> stockIds = couponEntities.stream().map(WechatCouponEntity::getStockId).collect(Collectors.toList());
        Map<String, Long> useCountMap = wxCouponPayManager.getPartnerCouponCount(ea, stockIds, CouponUseStautsEnum.USED.getStatus());
        Set<String> eas = couponEntities.stream().map(WechatCouponEntity::getEa).collect(Collectors.toSet());
        Set<String> marketingEventIds = couponEntities.stream().map(WechatCouponEntity::getMarketingEventId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        //获取市场活动名称
        Map<String, ObjectData> marketingEventObjMap = crmV2Manager.getObjectDataMapByIds(ea, -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, Lists.newArrayList("name"));
        //获取所属企业名称
        Map<String,String> companyNameMap = publicCouponManager.queryBelongCompanyName(eas);
        couponEntities.stream().forEach(wechatCouponEntity -> {
            CouponResult couponResult = new CouponResult();
            //如果是分享券，查询分享券详情
            if (wechatCouponEntity.getCreateCouponType() != null && CreateCouponTypeEnum.PRICE_POLICY.getType() == wechatCouponEntity.getCreateCouponType()) {
                CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
                if (null !=  couponPlanInfoResult) {
                    couponResult = BeanUtil.copy(wechatCouponEntity, CouponResult.class);
                    couponResult.setObjectId(wechatCouponEntity.getId());
                    copyPlanInfo(couponPlanInfoResult,couponResult);
                }
                if (StringUtils.isNotBlank(wechatCouponEntity.getMarketingEventId()) && marketingEventObjMap.containsKey(wechatCouponEntity.getMarketingEventId())) {
                    couponResult.setMarketingEventName(marketingEventObjMap.get(wechatCouponEntity.getMarketingEventId()).getName());
                }
                if (companyNameMap.containsKey(wechatCouponEntity.getEa())) {
                    couponResult.setBelongEnterpriseName(companyNameMap.get(wechatCouponEntity.getEa()));
                }
                if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                    couponResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(null,wechatCouponEntity.getStockId(),null));
                    couponResult.setUsedCont(wxCouponPayManager.getFxCouponCount(null,wechatCouponEntity.getStockId(),"2"));
                    int remainCount = totalCouponCount(wechatCouponEntity.getDealerCount(),wechatCouponEntity.getId(),wechatCouponEntity.getEa()) - couponResult.getReceiveCount();
                    couponResult.setRemainCount(remainCount);
                } else {
                    couponResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(ea,wechatCouponEntity.getStockId(),null));
                    couponResult.setUsedCont((int) (useCountMap.get(wechatCouponEntity.getStockId()) == null ? 0L : useCountMap.get(wechatCouponEntity.getStockId())));
                    if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene() && "-10000".equals(wechatCouponEntity.getSendScope())) {
                        int remainCount = wechatCouponEntity.getDealerCount() - couponResult.getReceiveCount();
                        couponResult.setRemainCount(remainCount);
                    } else {
                        int remainCount = wechatCouponEntity.getMaxCoupons() - couponResult.getReceiveCount();
                        couponResult.setRemainCount(remainCount);
                    }
                }

            } else {
                ObjectData objectData = wxCouponPayManager.queryCouponDetail(ea,wechatCouponEntity.getWechatCouponId());
                String objectDataStr = GsonUtil.getGson().toJson(objectData);
                CouponOrgResult couponOrgResult = GsonUtil.getGson().fromJson(objectDataStr, CouponOrgResult.class);
                if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(couponOrgResult.getStockType())){
                    couponOrgResult.setType(CouponTypeEnum.EXCHANGE_COUPON.getType());
                } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(couponOrgResult.getStockType())){
                    couponOrgResult.setType(CouponTypeEnum.DISCOUNT_COUPON.getType());
                } else if (CouponTypeEnum.NORMAL_COUPON.getName().equals(couponOrgResult.getStockType())){
                    couponOrgResult.setType(CouponTypeEnum.NORMAL_COUPON.getType());
                }
                couponResult = BeanUtil.copy(couponOrgResult, CouponResult.class);
                couponResult.setTransactionMinimum(AmountUtil.changeF2Y(couponOrgResult.getTransactionMinimum()));
                if (couponOrgResult.getDiscountAmount() != null) {
                    couponResult.setDiscountAmount(AmountUtil.changeF2Y(couponOrgResult.getDiscountAmount()));
                }
                if (couponOrgResult.getExchangePrice() != null) {
                    couponResult.setExchangePrice(AmountUtil.changeF2Y(couponOrgResult.getExchangePrice()));
                }
                if (couponOrgResult.getDiscountPercent() != null) {
                    couponResult.setDiscountPercent(AmountUtil.changeCount(couponOrgResult.getDiscountPercent()));
                }
                couponResult.setObjectId(wechatCouponEntity.getId());
                couponResult.setIsParticipate(wechatCouponEntity.getIsParticipate());
                couponResult.setScene(wechatCouponEntity.getScene());
                couponResult.setDealerCount(wechatCouponEntity.getDealerCount());
                couponResult.setCreateCouponType(wechatCouponEntity.getCreateCouponType());
                couponResult.setReceiveCount(couponTemplateManager.getCouponCount(ea,couponResult.getStockId(),1));
                couponResult.setUsedCont(couponTemplateManager.getCouponCount(ea,couponResult.getStockId(),2));
                int remainCount = couponResult.getMaxCoupons() - couponResult.getReceiveCount();
                couponResult.setRemainCount(remainCount);
                couponResult.setCouponNo(wechatCouponEntity.getCouponNo());
            }
            if (configEntity != null) {
                couponResult.setAppId(configEntity.getAppId());
            }
            result.add(couponResult);
        });
        couponPageResult.setTotalCount(page.getTotalNum());
        couponPageResult.setResult(result);
        return Result.newSuccess(couponPageResult);
    }

    private int totalCouponCount(Integer dealerCount, String couponId,String ea) {
        int dealerSize = 0;
        List<SendDealerCouponRecordEntity> sendDealerCouponRecordEntities = sendDealerCouponRecordDAO.queryList(couponId, ea);
        if (CollectionUtils.isNotEmpty(sendDealerCouponRecordEntities)) {
            dealerSize = sendDealerCouponRecordEntities.size();
        }
        if (dealerCount == null) {
            return 0;
        }
        return dealerCount * dealerSize;
    }

    private void copyPlanInfo(CouponPlanInfoResult data, CouponResult couponResult) {
        couponResult.setName(data.getName());
        couponResult.setPlanType(data.getPlanType());
        couponResult.setRemark(data.getRemark());
        couponResult.setStartDate(data.getStartDate());
        couponResult.setEndDate(data.getEndDate());
        //couponResult.setProductConditionType(data.getProductConditionType());
        couponResult.setProductConditionContent(data.getProductConditionDesc());
        couponResult.setLowerLimit(doubleStrToIntStr(data.getLowerLimit()));
        couponResult.setUseType(data.getUseType());
        couponResult.setAmount(doubleStrToIntStr(data.getAmount()));
    }

    private void copyPlanInfoByApp(CouponPlanInfoResult data, CouponListResult couponResult) {
        couponResult.setName(data.getName());
        couponResult.setPlanType(data.getPlanType());
        couponResult.setRemark(data.getRemark());
        couponResult.setStartDate(data.getStartDate());
        couponResult.setEndDate(data.getEndDate());
        //couponResult.setProductConditionType(data.getProductConditionType());
        couponResult.setProductConditionContent(data.getProductConditionDesc());
        couponResult.setLowerLimit(doubleStrToIntStr(data.getLowerLimit()));
        couponResult.setUseType(data.getUseType());
        couponResult.setAmount(doubleStrToIntStr(data.getAmount()));
    }

    private void copyPlanInfoByH5(CouponPlanInfoResult data, UserCouponListResult couponResult) {
        couponResult.setName(data.getName());
        couponResult.setPlanType(data.getPlanType());
        couponResult.setRemark(data.getRemark());
        couponResult.setStartDate(data.getStartDate());
        couponResult.setEndDate(data.getEndDate());
        //couponResult.setProductConditionType(data.getProductConditionType());
        couponResult.setProductConditionContent(data.getProductConditionDesc());
        couponResult.setLowerLimit(doubleStrToIntStr(data.getLowerLimit()));
        couponResult.setUseType(data.getUseType());
        couponResult.setAmount(doubleStrToIntStr(data.getAmount()));
    }

    private String doubleStrToIntStr(String str) {
        if(str.contains(".")) {
            if (str.contains(".00")){
                int indexOf = str.indexOf(".");
                str = str.substring(0, indexOf);
            }
        }
        return str;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateWeChatCoupon(UpdateWeChatCouponVo vo, String ea) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(ea),I18nUtil.get(I18nKeyEnum.MARK_SERVICE_ACTIVITYSERVICEIMPL_155));
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.updateWeChatCoupon(vo,ea);
        }
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (wechatCouponEntity == null) {
            log.info("weChatCouponDAO.getById no data objectId={}",vo.getObjectId());
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (CreateCouponTypeEnum.PRICE_POLICY.getType() == wechatCouponEntity.getCreateCouponType()) {
            if (vo.getMaxCoupons() != null) {
                int count = memberCouponBindDAO.queryReceiveAndUseCoupon(ea, wechatCouponEntity.getStockId());
                if (vo.getMaxCoupons() < count) {
                    return Result.newError(SHErrorCode.COUNT_MORE_RECEIVE);
                }
            }
            //1.进行本地库更新
            wxCouponPayManager.updateLocalCoupon(vo);
            CouponBaseInfoBody couponBaseInfoBody = buildRequestCouponBaseInfoBody(vo);
            //2.修改CouponObj
            com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> couponResult = wxCouponPayManager.updateCouponObj(ea, wechatCouponEntity.getCouponId(),couponBaseInfoBody.getCouponData());
            if (!couponResult.isSuccess()){
                log.info("update CRM CouponObj fail");
                throw new BusinessException(SHErrorCode.CRM_BUSINESS_ERROR);
            }
            return Result.newSuccess();
        }

        //处理图片是否更新
        if (!couponTemplateManager.isUpdateImageUrl(vo.getCouponImageUrl())){
            vo.setCouponImageUrl(null);
        }
        vo.setModifyBudgetRequestNo(wechatCouponEntity.getOutRequestNo());
        //获取商户平台信息
        MerchantConfigEntity merchantConfig = merchantConfigDao.getMerchantSimpleInfoByEa(ea);
        if (merchantConfig == null){
            throw new BusinessException(SHErrorCode.MERCHANT_NOT_CONFIG_YET);
        }
        String merchantId = merchantConfig.getMerchantId();
        String serialNo = merchantConfig.getSerialNo();
        String privateKeyStr = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfig.getApiclientKey());
        String apv3Key = AESUtil.decode(MERCHANT_CONFIG_AES_KEY,merchantConfig.getAppV3Secret());

        if (StringUtils.isNotBlank(vo.getCouponImageUrl())){
            vo.setCouponImageUrl(wxCouponPayManager.uploadImage(vo.getCouponImageUrl(),merchantConfig.getEa(),merchantId, serialNo, privateKeyStr, apv3Key));
        }

        //1.进行本地库更新
        wxCouponPayManager.updateLocalCoupon(vo);

        //2.进行微信数据更新
        if (null != vo.getTargetMaxCoupons() && null != vo.getCurrentMaxCoupons()){
            UpdateBudgetVo budgetVo = new UpdateBudgetVo();
            budgetVo.setStockId(wechatCouponEntity.getStockId());
            budgetVo.setCurrentMaxCoupons(vo.getCurrentMaxCoupons());
            budgetVo.setTargetMaxCoupons(vo.getTargetMaxCoupons());
            budgetVo.setModifyBudgetRequestNo(wechatCouponEntity.getOutRequestNo());
            //2.1 修改批次预算
            UpdateBudgetResult updateBudgetResult = wxCouponPayManager.updateCouponBudget(budgetVo,merchantId,serialNo,privateKeyStr);
            if (updateBudgetResult == null){
                log.info("wx api update budget fail");
                throw new BusinessException(SHErrorCode.WX_REQUEST_API_ERROR);
            }
        }
        //2.2 修改券基本信息
        CouponBaseInfoBody couponBaseInfoBody = buildRequestCouponBaseInfoBody(vo);
        String result = wxCouponPayManager.updateCouponBaseInfo(wechatCouponEntity.getStockId(),couponBaseInfoBody.getPostData(),merchantId,serialNo,privateKeyStr);
        if (StringUtils.isBlank(result) || !"OK".equals(result)){
            log.info("wx api update base info fail");
            throw new BusinessException(SHErrorCode.WX_REQUEST_API_ERROR);
        }
        // 3.同步修改CRM WechatCouponObj
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> actionEditResultResult = wxCouponPayManager.updateWeChatCouponObj(couponBaseInfoBody.getObjectData(),  ea, wechatCouponEntity.getWechatCouponId());
        if (!actionEditResultResult.isSuccess()){
            log.info("update CRM WeChatCouponObj fail");
            throw new BusinessException(SHErrorCode.CRM_BUSINESS_ERROR);
        }
        //4. 修改 CouponObj
        com.fxiaoke.crmrestapi.common.result.Result<ActionEditResult> couponResult = wxCouponPayManager.updateCouponObj(ea, wechatCouponEntity.getCouponId(),couponBaseInfoBody.getCouponData());
        if (!couponResult.isSuccess()){
            log.info("update CRM CouponObj fail");
            throw new BusinessException(SHErrorCode.CRM_BUSINESS_ERROR);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<CouponResult> queryCouponDetail(String ea, QueryCouponDetailVo vo) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(vo.getObjectId()),I18nUtil.get(I18nKeyEnum.MARK_WXCOUPON_PUBLICCOUPONSERVICEIMPL_610));
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryCouponDetail(ea,vo);
        }
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (couponEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        CouponResult couponResult;
        if (couponEntity.getCreateCouponType() != null && CreateCouponTypeEnum.PRICE_POLICY.getType() == couponEntity.getCreateCouponType()) {
            //纷享券
            couponResult = new CouponResult() ;
            GetCouponPlanInfoVo planVo = new GetCouponPlanInfoVo();
            planVo.setCouponPlanId(couponEntity.getWechatCouponId());
            Result<CouponPlanInfoResult> couponPlanInfoResultResult = this.queryCouponPlanInfo(planVo, ea);
            if (couponPlanInfoResultResult.isSuccess() && null !=  couponPlanInfoResultResult.getData()) {
                couponResult = BeanUtil.copy(couponEntity, CouponResult.class);
                couponResult.setObjectId(couponEntity.getId());
                copyPlanInfo(couponPlanInfoResultResult.getData(),couponResult);
                if (StringUtils.isNotBlank(couponEntity.getTags())) {
                    couponResult.setTags(GsonUtil.getGson().fromJson(couponEntity.getTags(),new TypeToken<List<TagName>>() {}.getType()));
                }
                if (StringUtils.isNotBlank(couponEntity.getSendScope()) && !"-10000".equals(couponEntity.getSendScope())) {
                    couponResult.setPartnerNoticeVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CouponResult.PartnerNoticeVisibilityVO.class));
                }
                if (StringUtils.isNotEmpty(couponEntity.getAccountScope())) {
                    couponResult.setAccountVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getAccountScope(), CouponResult.AccountVisibilityVO.class));
                }
                if (StringUtils.isNotEmpty(couponEntity.getReceiveScope())) {
                    couponResult.setStoreReceiveVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getReceiveScope(), CouponResult.StoreReceiveVisibilityVO.class));
                }
            }
            return Result.newSuccess(couponResult);
        }
        ObjectData objectData = wxCouponPayManager.queryCouponDetail(ea, couponEntity.getWechatCouponId());
        if (objectData == null){
            couponResult = null;
        } else {
            String objectDataStr = GsonUtil.getGson().toJson(objectData);
            CouponOrgResult couponOrgResult = GsonUtil.getGson().fromJson(objectDataStr, CouponOrgResult.class);
            if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(couponOrgResult.getStockType())){
                couponOrgResult.setType(CouponTypeEnum.EXCHANGE_COUPON.getType());
            } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(couponOrgResult.getStockType())){
                couponOrgResult.setType(CouponTypeEnum.DISCOUNT_COUPON.getType());
            } else if (CouponTypeEnum.NORMAL_COUPON.getName().equals(couponOrgResult.getStockType())){
                couponOrgResult.setType(CouponTypeEnum.NORMAL_COUPON.getType());
            }
            couponResult = BeanUtil.copy(couponOrgResult, CouponResult.class);
            couponResult.setTransactionMinimum(AmountUtil.changeF2Y(couponOrgResult.getTransactionMinimum()));
            if (couponOrgResult.getDiscountAmount() != null) {
                couponResult.setDiscountAmount(AmountUtil.changeF2Y(couponOrgResult.getDiscountAmount()));
            }
            if (couponOrgResult.getExchangePrice() != null) {
                couponResult.setExchangePrice(AmountUtil.changeF2Y(couponOrgResult.getExchangePrice()));
            }
            if (couponOrgResult.getDiscountPercent() != null) {
                couponResult.setDiscountPercent(AmountUtil.changeCount(couponOrgResult.getDiscountPercent()));
            }
            if (StringUtils.isNotBlank(couponOrgResult.getTags())) {
                couponResult.setTags(GsonUtil.getGson().fromJson(couponOrgResult.getTags(),new TypeToken<List<TagName>>() {}.getType()));
            }
            if (StringUtils.isNotBlank(couponEntity.getSendScope())) {
                couponResult.setPartnerNoticeVisibilityVO(GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CouponResult.PartnerNoticeVisibilityVO.class));
            }
            MerchantConfigEntity merchantConfigEntity = merchantConfigDao.getMerchantSimpleInfoByEa(ea);
            if (merchantConfigEntity != null) {
                couponResult.setAppId(merchantConfigEntity.getAppId());
            }
            couponResult.setEa(ea);
            couponResult.setOperator(couponEntity.getOperator());
            couponResult.setUpdateTime(couponEntity.getUpdateTime());
            couponResult.setObjectId(couponEntity.getId());
            couponResult.setIsParticipate(couponEntity.getIsParticipate());
            couponResult.setScene(couponEntity.getScene());
            couponResult.setDealerCount(couponEntity.getDealerCount());
            couponResult.setCreateCouponType(couponEntity.getCreateCouponType());
        }
        return Result.newSuccess(couponResult);
    }

    @Override
    public Result<CouponResult> queryCouponDetailToCustomer(QueryCouponDetailVo vo) {
        if (StringUtils.isBlank(vo.getObjectId()) && StringUtils.isBlank(vo.getObjectId())){
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        WechatCouponEntity  wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (wechatCouponEntity == null){
                return Result.newError(SHErrorCode.NO_DATA);
        }
        return  this.queryCouponDetail(wechatCouponEntity.getEa(), vo);
    }

    @Override
    public Result<PageResult<UserCouponListResult>> queryUserCouponListToCustomer(QueryUserCouponListVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryUserCouponListToCustomer(vo);
        }
        PageResult<UserCouponListResult> couponPageResult = new PageResult<>();
        List<UserCouponListResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);

        List<UserCouponListResult> userCouponListResults = Lists.newArrayList();
        String accountId = null;
        List<ObjectData> objectDataList;
        Map<String,WechatCouponEntity> resultMap;
        List<WechatCouponEntity> wechatCouponEntities = weChatCouponDAO.queryByEa(vo.getERUpstreamEa());
        if (CollectionUtils.isEmpty(wechatCouponEntities)) {
            resultMap = new HashMap<>();
        } else {
            resultMap = wechatCouponEntities.stream().collect(Collectors.toMap(WechatCouponEntity::getStockId, Function.identity(), (key1, key2) -> key2));
        }
        //查询优惠券实例对象
        if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.EXPIRE.getStatus()) || Objects.equals(vo.getStatus(),CouponInstStatusEnum.USE.getStatus())) {
            objectDataList = wxCouponPayManager.queryConponInstObjList(vo.getERUpstreamEa(),vo.getMemberId(),vo.getEROuterTenantId());
        } else {
            Page<ObjectData> couponInstList = wxCouponPayManager.queryCouponInstObjListPage(vo.getERUpstreamEa(),vo.getPageNum(),vo.getPageSize(),vo.getMemberId(),accountId,vo.getEROuterTenantId(),vo.getStatus());
            objectDataList = couponInstList.getDataList();
            couponPageResult.setTotalCount(couponInstList.getTotal());
        }
        //批量获取优惠券方案
        List<String> couponPlanIds = objectDataList.stream().map(e -> e.getString("coupon_plan_id")).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getERUpstreamEa(),couponPlanIds);
        objectDataList.forEach(objectData -> {
            String couponPlanId = objectData.getString("coupon_plan_id");
            String couponId = objectData.getString("coupon_id");
            WechatCouponEntity wechatCouponEntity = resultMap.get(couponId);
            UserCouponListResult userCouponListResult = new UserCouponListResult();
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(couponPlanId);
            if (null !=  couponPlanInfoResult) {
                if (wechatCouponEntity != null) {
                    userCouponListResult = BeanUtil.copy(wechatCouponEntity, UserCouponListResult.class);
                    userCouponListResult.setObjectId(wechatCouponEntity.getId());
                    userCouponListResult.setCreateCouponType(wechatCouponEntity.getCreateCouponType());
                }
                copyPlanInfoByH5(couponPlanInfoResult,userCouponListResult);
                userCouponListResult.setCouponInstanceId(objectData.getId());
                if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.EXPIRE.getStatus())) {
                    Long endDate = couponPlanInfoResult.getEndDate();
                    if (endDate < DateUtil.getDayStartTime(new Date())) {
                        userCouponListResults.add(userCouponListResult);
                    }
                } else if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.USE.getStatus())) {
                    Long endDate = couponPlanInfoResult.getEndDate();
                    if (endDate >= DateUtil.getDayStartTime(new Date())) {
                        userCouponListResults.add(userCouponListResult);
                    }
                }
            }
            result.add(userCouponListResult);
        });
        if (Objects.equals(vo.getStatus(),CouponInstStatusEnum.EXPIRE.getStatus()) || Objects.equals(vo.getStatus(),CouponInstStatusEnum.USE.getStatus())) {
            couponPageResult.setTotalCount(userCouponListResults.size());
            // 手动分页
            PageUtil<UserCouponListResult> pageUtil = new PageUtil<>(userCouponListResults, vo.getPageSize());
            if (CollectionUtils.isEmpty(pageUtil.getData())) {
                couponPageResult.setResult(userCouponListResults);
                return Result.newSuccess(couponPageResult);
            }
            couponPageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
            return Result.newSuccess(couponPageResult);
        }
        couponPageResult.setResult(result);
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<SignResult> getH5Sign(GetH5SignVo vo) {
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryWeChatCouponByStockId(vo.getStockId());
        if (wechatCouponEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String belongMerchant = wechatCouponEntity.getBelongMerchant();
        MerchantConfigEntity merchantSimpleInfoByEa = merchantConfigDao.getMerchantSimpleInfoByEa(wechatCouponEntity.getEa());
        if (merchantSimpleInfoByEa == null){
            return Result.newError(SHErrorCode.NO_COUPON_TEMPLATE);
        }
        String appKey = AESUtil.decode(MERCHANT_CONFIG_AES_KEY, merchantSimpleInfoByEa.getAppSecret());
        Map<String, String> parms = new HashMap<>(16);
        parms.put("open_id",vo.getOpenid());
        parms.put("out_request_no",vo.getOutRequestNo());
        parms.put("send_coupon_merchant",belongMerchant);
        parms.put("stock_id",vo.getStockId());
        //生成签名sign
        String sign = SignUtil.paySignDesposit(parms,appKey);
        SignResult signResult = new SignResult();
        signResult.setOpenid(vo.getOpenid());
        signResult.setOutRequestNo(vo.getOutRequestNo());
        signResult.setSendCouponMerchant(belongMerchant);
        signResult.setSign(sign);
        signResult.setStockId(vo.getStockId());
        return Result.newSuccess(signResult);
    }

    @Override
    public Result<SignResult> getWxAppSign(GetWXAppSignVo vo) {
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryWeChatCouponByStockId(vo.getStockId());
        if (wechatCouponEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String belongMerchant = wechatCouponEntity.getBelongMerchant();
        MerchantConfigEntity merchantSimpleInfoByEa = merchantConfigDao.getMerchantSimpleInfoByEa(wechatCouponEntity.getEa());
        if (merchantSimpleInfoByEa == null){
            return Result.newError(SHErrorCode.NO_COUPON_TEMPLATE);
        }
        String appKey = AESUtil.decode(MERCHANT_CONFIG_AES_KEY, merchantSimpleInfoByEa.getAppSecret());
        Map<String, String> parms = new HashMap<>(16);
        parms.put("out_request_no",vo.getOutRequestNo());
        parms.put("send_coupon_merchant",belongMerchant);
        parms.put("stock_id",vo.getStockId());
        //生成签名sign
        String sign = SignUtil.paySignDesposit(parms,appKey);
        SignResult signResult = new SignResult();
        signResult.setOutRequestNo(vo.getOutRequestNo());
        signResult.setSendCouponMerchant(belongMerchant);
        signResult.setSign(sign);
        signResult.setStockId(vo.getStockId());
        return Result.newSuccess(signResult);
    }

    @Override
    public Result<String> queryEaByStockId(String weChatCouponId) {
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryWeChatCoupon(weChatCouponId);
        if (wechatCouponEntity == null){
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(wechatCouponEntity.getEa());
    }

    @Override
    public Result<CouponDetailStatisticResult> queryCouponStockStatistic(String objectId, String ea) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryCouponStockStatistic(objectId,ea);
        }
        CouponDetailStatisticResult couponDetailStatisticResult = new CouponDetailStatisticResult();
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(objectId);
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String stockId = wechatCouponEntity.getStockId();
        if (CreateCouponTypeEnum.PRICE_POLICY.getType() == wechatCouponEntity.getCreateCouponType()) {
            if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                couponDetailStatisticResult.setMaxCount(totalCouponCount(wechatCouponEntity.getDealerCount(),wechatCouponEntity.getId(),ea));
                couponDetailStatisticResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(null,stockId,null));
                couponDetailStatisticResult.setUsedCount(wxCouponPayManager.getFxCouponCount(null,stockId,"2"));
            }else if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene()) {
                if ("-10000".equals(wechatCouponEntity.getSendScope())) {
                    couponDetailStatisticResult.setMaxCount(wechatCouponEntity.getDealerCount());
                } else {
                    couponDetailStatisticResult.setMaxCount(wechatCouponEntity.getMaxCoupons());
                }
                couponDetailStatisticResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(ea,stockId,null));
                couponDetailStatisticResult.setUsedCount(wxCouponPayManager.getFxCouponCount(ea,stockId,"2"));
            }
            return Result.newSuccess(couponDetailStatisticResult);
        }
        couponDetailStatisticResult.setMaxCount(wechatCouponEntity.getMaxCoupons());
        couponDetailStatisticResult.setReceiveCount(couponTemplateManager.getCouponCount(ea,stockId,1));
        couponDetailStatisticResult.setUsedCount(couponTemplateManager.getCouponCount(ea,stockId,2));
        return Result.newSuccess(couponDetailStatisticResult);
    }

    @Override
    public Result<PageResult<MemberReceiveResult>> queryMemberReceiveList(String objectId, String ea,Integer pageNo,Integer pageSize) {
        PageResult<MemberReceiveResult> pageResult = new PageResult<>();
        List<MemberReceiveResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setPageNum(pageNo);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(pageNo, pageSize, true);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(objectId);
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String stockId = wechatCouponEntity.getStockId();
        List<MemberCouponBindEntity> memberCouponBindEntities =  memberCouponBindDAO.queryListByStockId(stockId,page);
        memberCouponBindEntities.forEach(memberCouponBindEntity -> {
            ObjectData objectData = wxCouponPayManager.queryMemberDetail(memberCouponBindEntity.getMemberId(),ea);
            if (objectData != null) {
                String objectDataStr = GsonUtil.getGson().toJson(objectData);
                MemberReceiveResult memberReceiveResult = GsonUtil.getGson().fromJson(objectDataStr, MemberReceiveResult.class);
                memberReceiveResult.setSubmitTime(DateUtil.format(DateUtil.parse(memberCouponBindEntity.getSendTime(),"yyyy-MM-dd'T'HH:mm:ssXXX"),"yyyy-MM-dd HH:mm:ss"));
                memberReceiveResult.setDepositObject(true);
                queryLiveListResult.add(memberReceiveResult);
            }
        });
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(queryLiveListResult);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<PartnerReceiveResult>> queryPartnerReceiveList(String objectId, String ea, Integer pageNo, Integer pageSize) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryPartnerReceiveList(objectId,ea,pageNo,pageSize);
        }
        PageResult<PartnerReceiveResult> pageResult = new PageResult<>();
        List<PartnerReceiveResult> queryLiveListResult = Lists.newArrayList();
        pageResult.setPageNum(pageNo);
        pageResult.setPageSize(pageSize);
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(pageNo, pageSize, true);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(objectId);
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String stockId = wechatCouponEntity.getStockId();
        List<MemberCouponBindEntity> memberCouponBindEntities =  memberCouponBindDAO.queryPartnerList(stockId,page);
        if (CollectionUtils.isNotEmpty(memberCouponBindEntities)) {
//            List<String> partnerIds = memberCouponBindEntities.stream().map(MemberCouponBindEntity::getPartnerId).collect(Collectors.toList());
//            Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
            List<String> accountIds = memberCouponBindEntities.stream().map(MemberCouponBindEntity::getAccountId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            Map<String, String> companyIdToCompanyMap = wxCouponPayManager.batchGetEnterpriseRelationName(accountIds, ea);
            memberCouponBindEntities.forEach(memberCouponBindEntity -> {
                PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
                if (CreateCouponTypeEnum.WX_COUPON.getType() == wechatCouponEntity.getCreateCouponType()) {
                    partnerReceiveResult.setReceiveTime(DateUtil.format(DateUtil.parse(memberCouponBindEntity.getSendTime(),"yyyy-MM-dd'T'HH:mm:ssXXX"),"yyyy-MM-dd HH:mm:ss"));
                } else if (CreateCouponTypeEnum.PRICE_POLICY.getType() == wechatCouponEntity.getCreateCouponType()) {
                    partnerReceiveResult.setReceiveTime(memberCouponBindEntity.getSendTime());
                }
                partnerReceiveResult.setDepositObject(true);
                partnerReceiveResult.setStatus(memberCouponBindEntity.getStatus());
                partnerReceiveResult.setOutUid(memberCouponBindEntity.getAccountId());
                partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(memberCouponBindEntity.getAccountId()));
                queryLiveListResult.add(partnerReceiveResult);
            });
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(queryLiveListResult);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<CouponListResult>> getAppCouponList(GetAppCouponListVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.getAppCouponList(vo);
        }
        PageResult<CouponListResult> couponPageResult = new PageResult<>();
        List<CouponListResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        MerchantConfigEntity merchantConfigEntity = merchantConfigDao.getMerchantSimpleInfoByEa(vo.getERUpstreamEa());
        //查询本地数据库中的数据
        String accountId = null;
        int upEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        HeaderObj headerObj = HeaderObj.newInstance(upEi);
        UpstreamAndDownstreamOuterTenantIdOutArg outArg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        outArg.setDownstreamOuterTenantId(Long.valueOf(vo.getEROuterTenantId()));
        outArg.setUpstreamEa(vo.getERUpstreamEa());
        RestResult<String> mapperResult = enterpriseRelationService.getMapperObjectId(headerObj, outArg);
        if (mapperResult.isSuccess() && StringUtils.isNotBlank(mapperResult.getData())) {
            accountId = mapperResult.getData();
        }
        if (StringUtils.isBlank(accountId)) {
            return Result.newError(SHErrorCode.NOT_FOUND_DOWN_ACCOUNT);
        }
//        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(),vo.getPageSize(),true);
//        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryByEaAndScene(vo.getERUpstreamEa(),SceneEnum.PARTNER.getType(),page);
        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryListByEaAndScene(vo.getERUpstreamEa(),SceneEnum.PARTNER.getType());
        if (CollectionUtils.isNotEmpty(couponEntities)) {
            List<String> couponPlanIds = couponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
           Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getERUpstreamEa(),couponPlanIds);
            for (WechatCouponEntity objectData : couponEntities) {
                boolean inReceiveRange = publicCouponManager.isInReceiveCouponRange(objectData.getEa(),objectData.getStoreScope(),objectData.getReceiveScope(),accountId);
                if (inReceiveRange) {
                    //如果是分享券，查询分享券详情
                    if (objectData.getCreateCouponType() != null && CreateCouponTypeEnum.PRICE_POLICY.getType() == objectData.getCreateCouponType()) {
                        if (couponPlanInfoResultMap.containsKey(objectData.getWechatCouponId()) && null != couponPlanInfoResultMap.get(objectData.getWechatCouponId())) {
                            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(objectData.getWechatCouponId());
                            if (couponPlanInfoResult.getEndDate() < DateUtil.getDayStartTime(new Date()) || couponPlanInfoResult.getStartDate() > DateUtil.getDayStartTime(new Date())) {
                                continue;
                            }
                            CouponListResult couponResult = BeanUtil.copy(objectData, CouponListResult.class);
                            couponResult.setObjectId(objectData.getId());
                            copyPlanInfoByApp(couponPlanInfoResultMap.get(objectData.getWechatCouponId()), couponResult);
                            result.add(couponResult);
                        }
                    } else {
                        CouponListResult couponResult;
                        CouponOrgResult couponOrgResult = BeanUtil.copy(objectData, CouponOrgResult.class);
                        if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(couponOrgResult.getStockType())) {
                            couponOrgResult.setType(CouponTypeEnum.EXCHANGE_COUPON.getType());
                        } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(couponOrgResult.getStockType())) {
                            couponOrgResult.setType(CouponTypeEnum.DISCOUNT_COUPON.getType());
                        } else if (CouponTypeEnum.NORMAL_COUPON.getName().equals(couponOrgResult.getStockType())) {
                            couponOrgResult.setType(CouponTypeEnum.NORMAL_COUPON.getType());
                        }
                        couponResult = BeanUtil.copy(couponOrgResult, CouponListResult.class);
                        couponResult.setTransactionMinimum(AmountUtil.changeF2Y(couponOrgResult.getTransactionMinimum()));
                        if (couponOrgResult.getDiscountAmount() != null) {
                            couponResult.setDiscountAmount(AmountUtil.changeF2Y(couponOrgResult.getDiscountAmount()));
                        }
                        if (couponOrgResult.getExchangePrice() != null) {
                            couponResult.setExchangePrice(AmountUtil.changeF2Y(couponOrgResult.getExchangePrice()));
                        }
                        if (couponOrgResult.getDiscountPercent() != null) {
                            couponResult.setDiscountPercent(AmountUtil.changeCount(couponOrgResult.getDiscountPercent()));
                        }
                        couponResult.setAvailableBeginTime(objectData.getAvailableBeginTime());
                        couponResult.setAvailableEndTime(objectData.getAvailableEndTime());
                        couponResult.setObjectId(objectData.getId());
                        couponResult.setIsParticipate(objectData.getIsParticipate());
                        couponResult.setScene(objectData.getScene());
                        couponResult.setDealerCount(objectData.getDealerCount());
                        couponResult.setCreateCouponType(objectData.getCreateCouponType());
                        if (merchantConfigEntity != null) {
                            couponResult.setAppId(merchantConfigEntity.getAppId());
                        }
                        result.add(couponResult);
                    }

                }
            }
        }
        couponPageResult.setTotalCount(result.size());
        // 手动分页
        PageUtil<CouponListResult> pageUtil = new PageUtil<>(result, vo.getPageSize());
        if (CollectionUtils.isEmpty(pageUtil.getData())) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        couponPageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<Void> updateCouponStatus(String objectId, String ea, Integer status) {
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(objectId);
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        String couponId = wechatCouponEntity.getCouponId();
        //修改本地库数据
        weChatCouponDAO.updateCouponStatus(objectId,status);

        //修改crm CouponObj 对象
        wxCouponPayManager.updateCouponCrm(couponId,ea,status);

        return Result.newSuccess();
    }

    @Override
    public Result<CouponPlanInfoResult> queryCouponPlanInfo(GetCouponPlanInfoVo vo, String ea) {
        ObjectData objectData = crmV2Manager.getObjectData(ea, -10000, "CouponPlanObj", vo.getCouponPlanId());
        if (objectData == null) {
            return Result.newSuccess();
        }
        String objectDataStr = GsonUtil.getGson().toJson(objectData);
        CouponPlanInfoResult couponPlanInfoResult = GsonUtil.getGson().fromJson(objectDataStr, CouponPlanInfoResult.class);
        return Result.newSuccess(couponPlanInfoResult);
    }


    @Override
    public Result<Void> sendCouponPartner(SendCouponPartnerVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.sendCouponPartner(vo);
        }
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (couponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        try {
            //1.下发模板消息
            PartnerNoticeSendArg noticeSendArg = new PartnerNoticeSendArg();
            //设置参数
            CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CreateWxCouponVO.PartnerNoticeVisibilityVO.class);
            PartnerNoticeSendArg.PartnerNoticeVisibilityVO partnerVo = new PartnerNoticeSendArg.PartnerNoticeVisibilityVO();
            partnerVo.setEaList(partnerNoticeVisibilityVO.getOuterTenantIds());
            partnerVo.setTenantGroupIdList(partnerNoticeVisibilityVO.getOuterTenantGroupIds());
            noticeSendArg.setPartnerNoticeVisibilityVO(partnerVo);
            noticeSendArg.setSendType(NoticeSendTypeEnum.NORMAL.getType());
            noticeSendArg.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_2956_1));
            noticeSendArg.setContent(vo.getObjectId());
            noticeSendArg.setContentType(NoticeContentTypeEnum.SEND_COUPON.getType());
            noticeService.sendCouponNotice(vo.getEa(),vo.getFsUserId(),noticeSendArg);
        } catch (UnsupportedEncodingException e) {
            log.warn("下发模板消息失败:{}",e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        weChatCouponDAO.updateSendStatus(vo.getObjectId(),1);
        //2.判断会否需要直接进行DSS数据同步   经销商是否可选择参与 1:可选择参与 2:不可选择
        //触发DSS 发送范围内的所有企业
        //配合测试, 默认保存海洋集团的优惠券本地数据
        Set<String> ternetIds = new HashSet<>();
        if (StringUtils.isNotBlank(couponEntity.getSendScope()) && !"-10000".equals(couponEntity.getSendScope())) {
            CreateWxCouponVO.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = GsonUtil.getGson().fromJson(couponEntity.getSendScope(), CreateWxCouponVO.PartnerNoticeVisibilityVO.class);
            ternetIds = wxCouponPayManager.getTernetIdsFromGroupId(partnerNoticeVisibilityVO.getOuterTenantGroupIds(),couponEntity.getEa());
            ternetIds.addAll(partnerNoticeVisibilityVO.getOuterTenantIds());
        }
        List<Long> outTernatIds = new ArrayList<>();
        ternetIds.forEach(ternatId ->{
            outTernatIds.add(Long.parseLong(ternatId));
        });
        //获取下游企业的ea
        Map<Long,String> outerTenantId2EaMap = new HashMap<>();
        BatchGetEaByOuterTenantIdArg batchTenantIdArg = new BatchGetEaByOuterTenantIdArg();
        batchTenantIdArg.setOuterTenantIds(outTernatIds);
        RestResult<BatchGetEaByOuterTenantIdResult> outerTenantIdResultRestResult = fxiaokeAccountService.batchGetEaByOuterTenantId(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(couponEntity.getEa())), batchTenantIdArg);
        if (outerTenantIdResultRestResult.isSuccess() &&  null != outerTenantIdResultRestResult.getData()) {
            outerTenantId2EaMap = outerTenantIdResultRestResult.getData().getOuterTenantId2EaMap();
        }

        for (String ternetId : ternetIds) {
            if (!outerTenantId2EaMap.containsKey(Long.valueOf(ternetId))) {
                continue;
            }
            int ei = eieaConverter.enterpriseAccountToId(outerTenantId2EaMap.get(Long.valueOf(ternetId)));
            try {
                com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> getDescribeResultResult = objectDescribeService.getDescribe(new com.fxiaoke.crmrestapi.common.data.HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
                if (!getDescribeResultResult.isSuccess()){
                    continue;
                }
            } catch (Exception e) {
                log.warn("objectDescribeService.getDescribe is error",e);
                continue;
            }
            if (ParticipateEnum.NO.getType() == couponEntity.getIsParticipate()) {
                //保存下游企业数据到本地库
                WechatCouponEntity couponCopyEntity = BeanUtil.copy(couponEntity,WechatCouponEntity.class);
                couponCopyEntity.setEa(outerTenantId2EaMap.get(Long.valueOf(ternetId)));
                couponCopyEntity.setId(UUIDUtil.getUUID());
                //默认设置发送范围为 -10000
                couponCopyEntity.setSendScope("-10000");
                couponCopyEntity.setSendDownStatus(1);
                couponCopyEntity.setScene(SceneEnum.PARTNER.getType());
                weChatCouponDAO.saveWeChatCoupon(couponCopyEntity);
            }
            //记录下发经销商数据
            SendDealerCouponRecordEntity recordEntity = new SendDealerCouponRecordEntity();
            recordEntity.setId(UUIDUtil.getUUID());
            recordEntity.setEa(couponEntity.getEa());
            recordEntity.setCouponId(couponEntity.getId());
            recordEntity.setDealerCount(couponEntity.getDealerCount());
            recordEntity.setDownStreamEa(outerTenantId2EaMap.get(Long.valueOf(ternetId)));
            recordEntity.setDownStreamTenantId(ternetId);
            if (ParticipateEnum.NO.getType() == couponEntity.getIsParticipate()) {
                recordEntity.setAddCouponActivity(1);
            } else {
                //需要下游自己参与的则状态为未参与
                recordEntity.setAddCouponActivity(0);
            }
            sendDealerCouponRecordDAO.saveDealerCouponRecord(recordEntity);
            //同时存入对象
            Map<String, Object> dataMap = couponDistributionObjManager.createDataMapByEntity(couponEntity,recordEntity,false);
            couponDistributionObjManager.createCouponDistributionObj(couponEntity.getEa(),dataMap);

        }
        return Result.newSuccess();
    }

    @Override
    public Result<String> receivePartnerCoupon(ReceivePartnerCouponVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.receivePartnerCoupon(vo);
        }
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        if (Objects.equals(wechatCouponEntity.getStatus(),CouponStatusEnum.INVALIDATION.getStatus())) {
            return Result.newError(SHErrorCode.COUPON_INVALIDATION_NOT_RECEIVE);
        }
        String couponInstanceId = null;
        String partnerId = vo.getEROuterTenantId();
        String accountId = null;
        int upEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        HeaderObj headerObj = HeaderObj.newInstance(upEi);
        UpstreamAndDownstreamOuterTenantIdOutArg outArg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        outArg.setDownstreamOuterTenantId(Long.valueOf(vo.getEROuterTenantId()));
        outArg.setUpstreamEa(vo.getERUpstreamEa());
        RestResult<String> mapperResult = enterpriseRelationService.getMapperObjectId(headerObj, outArg);
        if (mapperResult.isSuccess() && StringUtils.isNotBlank(mapperResult.getData())) {
            accountId = mapperResult.getData();
        }
        //判断是否在可领取范围内
        boolean flag = publicCouponManager.isInReceiveCouponRange(wechatCouponEntity.getEa(),wechatCouponEntity.getStoreScope(),wechatCouponEntity.getReceiveScope(),accountId);
        if(!flag) {
            return Result.newError(SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorCode(),I18NUtil.get(upEi,MarketingI18NKeyUtil.NOT_RECEIVE_STORE_RANGE,SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorMessage()));
        }
        String couponLockKey = COUPON_LOCK_PREFIX + vo.getERUpstreamEa() + "_"+ wechatCouponEntity.getStockId();
        boolean lockAcquired = wxCouponPayManager.retryGetCouponLock(couponLockKey, 3);
        if (!lockAcquired) {
            return Result.newError(SHErrorCode.SERVER_BUSY);
        }
        try {
            int count = memberCouponBindDAO.queryReceiveAndUseCoupon(vo.getERUpstreamEa(),wechatCouponEntity.getStockId());
            if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                if (count >= wechatCouponEntity.getDealerCount()) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT);
                }
            } else {
                if (count >= wechatCouponEntity.getMaxCoupons()) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT);
                }
            }
            String couponId = wechatCouponEntity.getStockId();
            vo.setEa(wechatCouponEntity.getEa());
            vo.setCouponPlanId(wechatCouponEntity.getWechatCouponId());
            vo.setCouponStockId(wechatCouponEntity.getCouponId());
            //判断是否达到单人领取数量限制
            if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                int receivePartnerCouponCount = memberCouponBindDAO.queryReceiveCouponCount(vo.getERUpstreamEa(), wechatCouponEntity.getStockId(), null, partnerId, accountId);
                if (receivePartnerCouponCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                    return Result.newError(SHErrorCode.USER_RECEIVE_COUNT);
                }
            }
            //进行存入实例对象
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> couponInstObjResult = wxCouponPayManager.createCouponInstObj(vo, partnerId, accountId, null, couponId);
            if (!couponInstObjResult.isSuccess() || couponInstObjResult.getData() == null) {
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            couponInstanceId = couponInstObjResult.getData().getObjectData().getId();
            //保存到本地库
            MemberCouponBindEntity memberCouponEntity = BeanUtil.copy(vo,MemberCouponBindEntity.class);
            memberCouponEntity.setEa(wechatCouponEntity.getEa());
            memberCouponEntity.setCouponId(wechatCouponEntity.getStockId());
            memberCouponEntity.setId(UUIDUtil.getUUID());
            memberCouponEntity.setPartnerId(partnerId);
            memberCouponEntity.setAccountId(accountId);
            memberCouponEntity.setSendTime(DateUtil.format(DateUtil.now()));
            memberCouponEntity.setStatus("1");
            memberCouponBindDAO.saveMemberCouponBind(memberCouponEntity);
            ThreadPoolUtils.executeWithTraceContext(() -> {
                //添加优惠券领取营销动态
                String marketingUserId = wxCouponPayManager.getMarketingUserIdByOutTenantId(vo.getEROuterTenantId(), wechatCouponEntity.getEa());
                wxCouponPayManager.syncRecordCouponMarketingActivityData(wechatCouponEntity.getEa(),wechatCouponEntity.getId(),wechatCouponEntity.getMarketingEventId(),marketingUserId, MarketingUserActionType.RECEIVE_COUPON.getActionType());
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        } catch (Exception e) {
            log.warn("receivePartnerCoupon error",e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        } finally {
            redisManager.unLock(couponLockKey);
        }
        return Result.newSuccess(couponInstanceId);
    }

    @Override
    public Result<Void> participateCouponActivity(ParticipateActivityVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.participateCouponActivity(vo);
        }
        //1.修改经销商参与的状态  为已参与
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        //判断优惠券是否已终止
        if (Objects.equals(CouponStatusEnum.INVALIDATION.getStatus(),couponEntity.getStatus())) {
            return Result.newError(SHErrorCode.COUPON_INVALIDATION);
        }
        sendDealerCouponRecordDAO.updateAddStatus(vo.getObjectId(),vo.getEROuterTenantId());
        ThreadPoolUtils.executeWithTraceContext(() ->{
            couponDistributionObjManager.updateCouponDistributionObjStatus(couponEntity.getEa(),couponEntity.getCouponId(),vo.getEROuterTenantId());
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        GetEaByOuterTenantIdArg outArg = new GetEaByOuterTenantIdArg();
        outArg.setOuterTenantId(Long.parseLong(vo.getEROuterTenantId()));
        int upStreamEi = eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa());
        String downStream = null;
        com.fxiaoke.enterpriserelation2.common.HeaderObj headerObj = com.fxiaoke.enterpriserelation2.common.HeaderObj.newInstance(upStreamEi);
        RestResult<String> outerTenantIdResult = fxiaokeAccountService.getEaByOuterTenantId(headerObj,outArg);
        if (outerTenantIdResult.isSuccess() && StringUtils.isNotBlank(outerTenantIdResult.getData())) {
            downStream = outerTenantIdResult.getData();
        }

        //1.5 同步保存本地优惠券数据
        //保存下游企业数据到本地库
        WechatCouponEntity couponCopyEntity = BeanUtil.copy(couponEntity,WechatCouponEntity.class);
        couponCopyEntity.setEa(downStream);
        couponCopyEntity.setId(UUIDUtil.getUUID());
        //默认设置发送范围为 -10000
        couponCopyEntity.setSendScope("-10000");
        couponCopyEntity.setSendDownStatus(1);
        couponCopyEntity.setScene(SceneEnum.PARTNER.getType());
        weChatCouponDAO.saveWeChatCoupon(couponCopyEntity);

        //2.触发DSS数据同步
        SyncDataByDownStreamTenantIdArg syncArg = new SyncDataByDownStreamTenantIdArg();
        syncArg.setDataIdList(Lists.newArrayList(couponEntity.getCouponId()));
        syncArg.setDestTenantId(String.valueOf(eieaConverter.enterpriseAccountToId(downStream)));
        syncArg.setObjectApiName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        syncArg.setUpstreamTenantId(String.valueOf(eieaConverter.enterpriseAccountToId(vo.getERUpstreamEa())));
        SyncResult<Void> voidSyncResult = syncService.syncDataByDownStreamTenantId(syncArg);
        log.info("syncResult:{}",voidSyncResult);
        //处理营销动态
        ThreadPoolUtils.executeWithTraceContext(() ->{
            String marketingUserId = wxCouponPayManager.getMarketingUserIdByOutTenantId(vo.getEROuterTenantId(), couponEntity.getEa());
            wxCouponPayManager.syncRecordCouponMarketingActivityData(couponEntity.getEa(),couponEntity.getId(),couponEntity.getMarketingEventId(),marketingUserId, MarketingUserActionType.ATTEND_COUPON_ACTIVITY.getActionType());
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<PartnerCouponDetailStatisticResult>> statisticDealerCoupon(String ea, PartnerCouponDetailStatisticVo vo) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.statisticDealerCoupon(ea,vo);
        }
        PageResult<PartnerCouponDetailStatisticResult> pageResult = new PageResult<>();
        List<PartnerCouponDetailStatisticResult> partnerCouponResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        List<SendDealerCouponRecordEntity> recordEntities = sendDealerCouponRecordDAO.queryRecordList(vo.getObjectId(), ea, page);
        if (CollectionUtils.isNotEmpty(recordEntities)) {
            List<String> partnerIds = recordEntities.stream().map(SendDealerCouponRecordEntity::getDownStreamTenantId).collect(Collectors.toList());
            Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
            recordEntities.forEach(recordEntity -> {
                WechatCouponEntity couponEntity = weChatCouponDAO.getById(recordEntity.getCouponId());
                PartnerCouponDetailStatisticResult partnerCouponDetailStatisticResult = new PartnerCouponDetailStatisticResult();
                partnerCouponDetailStatisticResult.setBusinessName(companyIdToCompanyMap.get(recordEntity.getDownStreamTenantId()));
                partnerCouponDetailStatisticResult.setEa(recordEntity.getDownStreamEa());
                partnerCouponDetailStatisticResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(recordEntity.getDownStreamEa(),couponEntity.getStockId(),null));
                partnerCouponDetailStatisticResult.setUseCount(wxCouponPayManager.getFxCouponCount(recordEntity.getDownStreamEa(),couponEntity.getStockId(),"2"));
                partnerCouponResultList.add(partnerCouponDetailStatisticResult);
            });
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(partnerCouponResultList);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<PageResult<CouponActivityResult>> queryCouponActivityList(CouponActivityVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryCouponActivityList(vo);
        }
        PageResult<CouponActivityResult> pageResult = new PageResult<>();
        List<CouponActivityResult> couponActivityResults = Lists.newArrayList();
        pageResult.setResult(couponActivityResults);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        List<SendDealerCouponRecordEntity> sendDealerCouponRecordEntities = sendDealerCouponRecordDAO.queryListByDownStreamTenantId(vo.getEROuterTenantId(),page);
        if (CollectionUtils.isEmpty(sendDealerCouponRecordEntities)) {
            return Result.newSuccess(pageResult);
        }
        List<String> couponIds = sendDealerCouponRecordEntities.stream().map(SendDealerCouponRecordEntity::getCouponId).collect(Collectors.toList());
        List<WechatCouponEntity> wechatCouponEntityList = weChatCouponDAO.getByIds(couponIds);
        Set<String> marketingEventIds = wechatCouponEntityList.stream().map(WechatCouponEntity::getMarketingEventId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        List<String> couponPlanIds = wechatCouponEntityList.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String, WechatCouponEntity> couponEntityMap = wechatCouponEntityList.stream().collect(Collectors.toMap(WechatCouponEntity::getId, o -> o));
        Map<String, ObjectData> marketingEventObjMap = crmV2Manager.getObjectDataMapByIds(vo.getERUpstreamEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, Lists.newArrayList("name"));
        List<CouponPlanInfoResult> couponPlanInfoResults = wxCouponPayManager.listCouponPlanInfo(vo.getERUpstreamEa(), -10000, couponPlanIds);
        Map<String, CouponPlanInfoResult> couponPlanInfoResultMap = couponPlanInfoResults.stream().collect(Collectors.toMap(CouponPlanInfoResult::getCouponPlanId, o -> o));
        for (SendDealerCouponRecordEntity sendDealerCouponRecordEntity : sendDealerCouponRecordEntities) {
            if (!couponEntityMap.containsKey(sendDealerCouponRecordEntity.getCouponId())) {
                continue;
            }
            CouponActivityResult couponActivityResult = new CouponActivityResult();
            WechatCouponEntity couponEntity = couponEntityMap.get(sendDealerCouponRecordEntity.getCouponId());
            if (couponPlanInfoResultMap.containsKey(couponEntity.getWechatCouponId())) {
                couponActivityResult = BeanUtil.copy(couponPlanInfoResultMap.get(couponEntity.getWechatCouponId()),couponActivityResult.getClass());
            }
            couponActivityResult.setStockName(couponEntity.getStockName());
            couponActivityResult.setObjectId(sendDealerCouponRecordEntity.getCouponId());
            couponActivityResult.setAddCouponActivity(sendDealerCouponRecordEntity.getAddCouponActivity());
            if (marketingEventObjMap.containsKey(couponEntity.getMarketingEventId())) {
                couponActivityResult.setMarketingEventName(marketingEventObjMap.get(couponEntity.getMarketingEventId()).getName());
            }
            couponActivityResults.add(couponActivityResult);
        }
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(couponActivityResults);
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<CouponActivityResult> queryCouponActivityDetail(CouponDetailActivityVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryCouponActivityDetail(vo);
        }
        SendDealerCouponRecordEntity recordEntity = sendDealerCouponRecordDAO.queryRecord(vo.getObjectId(),vo.getEROuterTenantId());
        CouponActivityResult couponActivityResult = new CouponActivityResult();
        WechatCouponEntity couponEntity = weChatCouponDAO.getById(vo.getObjectId());
        GetCouponPlanInfoVo planVo = new GetCouponPlanInfoVo();
        planVo.setCouponPlanId(couponEntity.getWechatCouponId());
        Result<CouponPlanInfoResult> couponPlanInfoResultResult = this.queryCouponPlanInfo(planVo, couponEntity.getEa());
        if (couponPlanInfoResultResult.isSuccess() && null != couponPlanInfoResultResult.getData()) {
            couponActivityResult = BeanUtil.copy(couponPlanInfoResultResult.getData(),couponActivityResult.getClass());
        }
        couponActivityResult.setObjectId(recordEntity.getCouponId());
        couponActivityResult.setAddCouponActivity(recordEntity.getAddCouponActivity());
        couponActivityResult.setStockName(couponEntity.getStockName());
        ObjectData objectData = crmV2Manager.getObjectData(couponEntity.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), couponEntity.getMarketingEventId());
        if (objectData != null) {
            couponActivityResult.setMarketingEventName(objectData.getName());
        }
        return Result.newSuccess(couponActivityResult);
    }

    @Override
    public Result<PageResult<PartnerReceiveResult>> statisticStoreCoupon(String ea, PartnerCouponDetailStatisticVo vo) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.statisticStoreCoupon(ea,vo);
        }
        PageResult<PartnerReceiveResult> pageResult = new PageResult<>();
        List<PartnerReceiveResult> partnerCouponResultList = Lists.newArrayList();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setTotalCount(0);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        List<SendDealerCouponRecordEntity> recordEntities = sendDealerCouponRecordDAO.queryList(vo.getObjectId(), ea);
        if (CollectionUtils.isNotEmpty(recordEntities)) {
            List<String> dealerEas = recordEntities.stream().map(SendDealerCouponRecordEntity::getDownStreamEa).collect(Collectors.toList());
            List<MemberCouponBindEntity> memberCouponBindEntities = memberCouponBindDAO.queryStoreCoupon(dealerEas,wechatCouponEntity.getStockId());
            if (CollectionUtils.isNotEmpty(memberCouponBindEntities)) {
                List<String> partnerIds = memberCouponBindEntities.stream().map(MemberCouponBindEntity::getPartnerId).collect(Collectors.toList());
                Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
                memberCouponBindEntities.forEach(memberCouponBindEntity -> {
                    PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
                    partnerReceiveResult.setReceiveTime(memberCouponBindEntity.getSendTime());
                    partnerReceiveResult.setDepositObject(true);
                    partnerReceiveResult.setStatus(memberCouponBindEntity.getStatus());
                    partnerReceiveResult.setOutUid(memberCouponBindEntity.getAccountId());
                    partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(memberCouponBindEntity.getPartnerId()));
                    partnerCouponResultList.add(partnerReceiveResult);
                });
            }
        }
        pageResult.setTotalCount(partnerCouponResultList.size());
        // 手动分页
        PageUtil<PartnerReceiveResult> pageUtil = new PageUtil<>(partnerCouponResultList, vo.getPageSize());
        if (CollectionUtils.isEmpty(pageUtil.getData())) {
            pageResult.setResult(partnerCouponResultList);
            return Result.newSuccess(pageResult);
        }
        pageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<String> queryPartnerAppId() {
        if (Strings.isNullOrEmpty(partnerAppId)) {
            log.warn("partnerAppId is reload failed :{}",partnerAppId);
            return Result.newError(SHErrorCode.NO_DATA);
        }
        return Result.newSuccess(partnerAppId);
    }

    @Override
    public Result<List<CouponPlanInfoResult>> queryCouponPlanList(QueryCouponPlanVO vo) {
        List<CouponPlanInfoResult> couponPlanInfoResults = Lists.newArrayList();
        Integer tenantId = vo.getTenantId();
        if (tenantId == null) {
            log.warn("queryCouponPlanList tenantId is null vo:{}",vo);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (CollectionUtils.isEmpty(vo.getCouponNos())) {
            return Result.newSuccess(couponPlanInfoResults);
        }
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        if (StringUtils.isBlank(ea)) {
            log.warn("eieaConverter.enterpriseIdToAccount tenantId is error vo:{}",vo);
        }
        PaasQueryFilterArg paasFilterArg = new PaasQueryFilterArg();
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, vo.getCouponNos().size());
        paasQueryArg.addFilter("name", PaasAndCrmOperatorEnum.IN.getCrmOperator(), vo.getCouponNos());
        paasFilterArg.setQuery(paasQueryArg);
        paasFilterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_OBJ.getName());
        paasFilterArg.setSelectFields(Lists.newArrayList("_id", "coupon_plan_id","name"));
        InnerPage<ObjectData> objectDataInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasFilterArg);
        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
            return Result.newSuccess(couponPlanInfoResults);
        }
        List<String> pricePolicyIds = objectDataInnerPage.getDataList().stream().map(data -> data.getString("coupon_plan_id")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pricePolicyIds)) {
            log.warn("queryCouponPlanList pricePolicyIds is null vo:{}",vo);
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        //将objectDataInnerPage转换成map, key 为name,value为coupon_plan_id
        Map<String, String> collectMap = objectDataInnerPage.getDataList().stream().collect(Collectors.toMap(data -> data.getString("name"), data -> data.getString("coupon_plan_id")));
        PaasQueryFilterArg paasPlanFilterArg = new PaasQueryFilterArg();
        PaasQueryArg paasPlanQueryArg = new PaasQueryArg(0, pricePolicyIds.size());
        paasPlanQueryArg.addFilter("_id", PaasAndCrmOperatorEnum.IN.getCrmOperator(), pricePolicyIds);
        paasPlanFilterArg.setQuery(paasPlanQueryArg);
        paasPlanFilterArg.setObjectAPIName(CrmObjectApiNameEnum.COUPON_PLAN_OBJ.getName());
        InnerPage<ObjectData> objectDataPlanInnerPage = crmV2Manager.concurrentListCrmObjectByFilterV3(ea, -10000, paasPlanFilterArg);
        if (objectDataPlanInnerPage != null && CollectionUtils.isNotEmpty(objectDataPlanInnerPage.getDataList())) {
            Map<String, ObjectData> objectDataMap = objectDataPlanInnerPage.getDataList().stream().collect(Collectors.toMap(data -> data.getId(), Function.identity()));
            vo.getCouponNos().forEach(couponNo -> {
                if (collectMap.containsKey(couponNo)) {
                    String couponPlanId = collectMap.get(couponNo);
                    if (objectDataMap.containsKey(couponPlanId)) {
                        ObjectData data = objectDataMap.get(couponPlanId);
                        String str = GsonUtil.getGson().toJson(data);
                        CouponPlanInfoResult planInfoResult = GsonUtil.getGson().fromJson(str, CouponPlanInfoResult.class);
                        planInfoResult.setCouponNo(couponNo);
                        couponPlanInfoResults.add(planInfoResult);
                    }
                }
            });
        }
        return Result.newSuccess(couponPlanInfoResults);
    }

    @Override
    public Result<PageResult<CouponResult>> queryFxCouponList(QueryFxCouponVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryFxCouponList(vo);
        }
        PageResult<CouponResult> couponPageResult = new PageResult<>();
        List<CouponResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        MerchantConfigEntity configEntity = merchantConfigDao.getMerchantSimpleInfoByEa(vo.getEa());
        //查询本地数据库中的数据
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        QueryCouponParam param = new QueryCouponParam();
        param.setEa(vo.getEa());
        param.setStockName(vo.getStockName());
        List<WechatCouponEntity> couponEntities = weChatCouponDAO.queryFxCouponList(param,page);
        if (CollectionUtils.isEmpty(couponEntities)) {
            return  Result.newSuccess(couponPageResult);
        }
        //批量获取优惠券方案
        List<String> couponPlanIds = couponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getEa(),couponPlanIds);
        //批量获取优惠券使用数量
        List<String> stockIds = couponEntities.stream().map(WechatCouponEntity::getStockId).collect(Collectors.toList());
        Set<String> eas = couponEntities.stream().map(WechatCouponEntity::getEa).collect(Collectors.toSet());
        Set<String> marketingEventIds = couponEntities.stream().map(WechatCouponEntity::getMarketingEventId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        Map<String, Long> useCountMap = wxCouponPayManager.getPartnerCouponCount(vo.getEa(), stockIds, CouponUseStautsEnum.USED.getStatus());
        //获取市场活动名称
        Map<String, ObjectData> marketingEventObjMap = crmV2Manager.getObjectDataMapByIds(vo.getEa(), -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventIds, Lists.newArrayList("name"));
        //获取所属企业名称
        Map<String,String> companyNameMap = publicCouponManager.queryBelongCompanyName(eas);
        couponEntities.forEach(wechatCouponEntity -> {
            CouponResult couponResult = new CouponResult();
            //如果是分享券，查询分享券详情
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                couponResult = BeanUtil.copy(wechatCouponEntity, CouponResult.class);
                couponResult.setObjectId(wechatCouponEntity.getId());
                copyPlanInfo(couponPlanInfoResult,couponResult);
            }
            if (StringUtils.isNotBlank(wechatCouponEntity.getMarketingEventId()) && marketingEventObjMap.containsKey(wechatCouponEntity.getMarketingEventId())) {
                couponResult.setMarketingEventName(marketingEventObjMap.get(wechatCouponEntity.getMarketingEventId()).getName());
            }
            if (companyNameMap.containsKey(wechatCouponEntity.getEa())) {
                couponResult.setBelongEnterpriseName(companyNameMap.get(wechatCouponEntity.getEa()));
            }
            if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                couponResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(null,wechatCouponEntity.getStockId(),null));
                couponResult.setUsedCont(wxCouponPayManager.getFxCouponCount(null,wechatCouponEntity.getStockId(),"2"));
                int remainCount = totalCouponCount(wechatCouponEntity.getDealerCount(),wechatCouponEntity.getId(),wechatCouponEntity.getEa()) - couponResult.getReceiveCount();
                couponResult.setRemainCount(remainCount);
            } else {
                couponResult.setReceiveCount(wxCouponPayManager.getFxCouponCount(vo.getEa(),wechatCouponEntity.getStockId(),null));
                couponResult.setUsedCont((int) (useCountMap.get(wechatCouponEntity.getStockId()) == null ? 0L : useCountMap.get(wechatCouponEntity.getStockId())));
                if (SceneEnum.PARTNER.getType() == wechatCouponEntity.getScene() && "-10000".equals(wechatCouponEntity.getSendScope())) {
                    int remainCount = wechatCouponEntity.getDealerCount() - couponResult.getReceiveCount();
                    couponResult.setRemainCount(remainCount);
                } else {
                    int remainCount = wechatCouponEntity.getMaxCoupons() - couponResult.getReceiveCount();
                    couponResult.setRemainCount(remainCount);
                }
            }
            if (configEntity != null) {
                couponResult.setAppId(configEntity.getAppId());
            }
            result.add(couponResult);
        });
        couponPageResult.setTotalCount(page.getTotalNum());
        couponPageResult.setResult(result);
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<Boolean> checkCustomerPendingCoupon(CheckCustomerPendingCouponVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.checkCustomerPendingCoupon(vo);
        }
        List<WechatCouponEntity> wechatCouponList = weChatCouponDAO.queryListByEaAndScene(vo.getEa(),SceneEnum.PARTNER.getType());
        List<WechatCouponEntity> wechatCouponEntityList = Lists.newArrayList();
        wechatCouponList.forEach(entity -> {
            boolean inReceiveRange = publicCouponManager.isInReceiveCouponRange(entity.getEa(),entity.getStoreScope(),entity.getReceiveScope(),vo.getCustomerId());
            if (inReceiveRange) {
                wechatCouponEntityList.add(entity);
            }
        });
        if (CollectionUtils.isEmpty(wechatCouponEntityList)) {
            return Result.newSuccess(false);
        }
        //企业所创建的所有优惠券
        Set<String> couponSet = new HashSet<>();
        List<String> couponPlanIds = wechatCouponEntityList.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getEa(),couponPlanIds);
        for (WechatCouponEntity wechatCouponEntity : wechatCouponEntityList) {
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                if (couponPlanInfoResult.getEndDate() < DateUtil.getDayStartTime(new Date()) || couponPlanInfoResult.getStartDate() > DateUtil.getDayStartTime(new Date())) {
                    continue;
                }
                couponSet.add(wechatCouponEntity.getStockId());
            }
        }
        if (CollectionUtils.isEmpty(couponSet)) {
            return Result.newSuccess(false);
        }
        Map<String, WechatCouponEntity> couponEntityMap = wechatCouponEntityList.stream().collect(Collectors.toMap(WechatCouponEntity::getStockId, Function.identity(), (v1, v2) -> v2));
        List<MemberCouponBindEntity> memberCouponBindEntities = memberCouponBindDAO.queryByAccountId(vo.getEa(),vo.getCustomerId());
        if (CollectionUtils.isEmpty(memberCouponBindEntities)) {
            return Result.newSuccess(true);
        }
        //已领取的所有优惠券id
        Set<String> receiveCouponSet = memberCouponBindEntities.stream().map(MemberCouponBindEntity::getCouponId).collect(Collectors.toSet());
        //处理已领取的券统计
        Map<String, Long> collectReceiveMap = memberCouponBindEntities.stream().collect(Collectors.groupingBy(MemberCouponBindEntity::getCouponId, Collectors.counting()));
        //1.先判断客户是否已领取了当前所有可以领取的券
        boolean flagAll = receiveCouponSet.containsAll(couponSet);
        //2.如果是领取了所有的券,先赋值没有待领取的
        boolean needPending = !flagAll;
        //3.判断是否存在券没有达到领取限制,只要存在有,则直接赋值有待领取的券
        if (flagAll) {
            for (MemberCouponBindEntity memberCouponBindEntity : memberCouponBindEntities) {
                String stockId = memberCouponBindEntity.getCouponId();
                WechatCouponEntity wechatCouponEntity = couponEntityMap.get(stockId);
                Long receive = collectReceiveMap.get(stockId);
                if (wechatCouponEntity != null && receive < wechatCouponEntity.getMaxCouponsPerUser()){
                    needPending = true;
                    break;
                }
            }
        }
        return Result.newSuccess(needPending);
    }

    @Override
    public Result<PageResult<QueryCustomerCouponResult>> queryCustomerPendingCouponList(QueryCustomerPendingCouponVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryCustomerPendingCouponList(vo);
        }
        PageResult<QueryCustomerCouponResult> couponPageResult = new PageResult<>();
        List<QueryCustomerCouponResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        List<WechatCouponEntity> wechatCouponList = weChatCouponDAO.queryListByEaAndScene(vo.getEa(),SceneEnum.PARTNER.getType());
        List<WechatCouponEntity> wechatCouponEntityList = Lists.newArrayList();
        wechatCouponList.forEach(entity -> {
            boolean inReceiveRange = publicCouponManager.isInReceiveCouponRange(entity.getEa(),entity.getStoreScope(),entity.getReceiveScope(),vo.getCustomerId());
            if (inReceiveRange) {
                wechatCouponEntityList.add(entity);
            }
        });
        if (CollectionUtils.isEmpty(wechatCouponEntityList)) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        //企业所创建的所有优惠券
        Set<String> couponSet = wechatCouponEntityList.stream().map(WechatCouponEntity::getStockId).collect(Collectors.toSet());
        List<String> couponIds = new ArrayList<>(couponSet);
        Map<String, WechatCouponEntity> couponEntityMap = wechatCouponEntityList.stream().collect(Collectors.toMap(WechatCouponEntity::getStockId, Function.identity(), (v1, v2) -> v2));
        List<MemberCouponBindEntity> memberCouponBindEntities = memberCouponBindDAO.queryByAccountId(vo.getEa(),vo.getCustomerId());
        if (CollectionUtils.isNotEmpty(memberCouponBindEntities)) {
            //处理已领取的券统计
            Map<String, Long> collectReceiveMap = memberCouponBindEntities.stream().collect(Collectors.groupingBy(MemberCouponBindEntity::getCouponId, Collectors.counting()));
            //已达到领取限制的所有优惠券stockId
            Set<String> receiveCouponSet = new HashSet<>();
            //判断是否达到领取限制
            for (MemberCouponBindEntity memberCouponBindEntity : memberCouponBindEntities) {
                String stockId = memberCouponBindEntity.getCouponId();
                WechatCouponEntity wechatCouponEntity = couponEntityMap.get(stockId);
                Long receive = collectReceiveMap.get(stockId);
                if (wechatCouponEntity != null && receive >= wechatCouponEntity.getMaxCouponsPerUser()){
                    receiveCouponSet.add(stockId);
                }
            }
            //未领取的券stockId
            couponIds.removeAll(receiveCouponSet);
        }
        if (CollectionUtils.isEmpty(couponIds)){
            log.info("queryCustomerPendingCouponList couponIds is null");
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        List<WechatCouponEntity> wechatCouponEntities = weChatCouponDAO.queryByEaAndStockIds(vo.getEa(),couponIds);
        List<String> couponPlanIds = wechatCouponEntities.stream().map(WechatCouponEntity::getWechatCouponId).collect(Collectors.toList());
        Map<String,CouponPlanInfoResult> couponPlanInfoResultMap  = wxCouponPayManager.getCouponPlanList(vo.getEa(),couponPlanIds);
        for (WechatCouponEntity wechatCouponEntity : wechatCouponEntities) {
            //如果是分享券，查询分享券详情
            CouponPlanInfoResult couponPlanInfoResult = couponPlanInfoResultMap.get(wechatCouponEntity.getWechatCouponId());
            if (null !=  couponPlanInfoResult) {
                if (couponPlanInfoResult.getEndDate() < DateUtil.getDayStartTime(new Date()) || couponPlanInfoResult.getStartDate() > DateUtil.getDayStartTime(new Date())) {
                    continue;
                }
                QueryCustomerCouponResult couponResult = BeanUtil.copy(couponPlanInfoResult, QueryCustomerCouponResult.class);
                couponResult.setObjectDataId(wechatCouponEntity.getCouponId());
                result.add(couponResult);
            }
        }
        couponPageResult.setTotalCount(result.size());
        // 手动分页
        PageUtil<QueryCustomerCouponResult> pageUtil = new PageUtil<>(result, vo.getPageSize());
        if (CollectionUtils.isEmpty(pageUtil.getData())) {
            couponPageResult.setResult(result);
            return Result.newSuccess(couponPageResult);
        }
        couponPageResult.setResult(pageUtil.getPagedList(vo.getPageNum()));
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<String> receiveSingleCoupon(ReceiveSingleCouponVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.receiveSingleCoupon(vo);
        }
        int ei = eieaConverter.enterpriseAccountToId(vo.getEa());
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.queryCouponDetailByObjectId(vo.getEa(),vo.getObjectDataId());
        if (wechatCouponEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA.getErrorCode(), I18NUtil.get(ei,MarketingI18NKeyUtil.NO_DATA,SHErrorCode.NO_DATA.getErrorMessage()));
        }
        if (Objects.equals(wechatCouponEntity.getStatus(),CouponStatusEnum.INVALIDATION.getStatus())) {
            return Result.newError(SHErrorCode.COUPON_INVALIDATION_NOT_RECEIVE.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.COUPON_INVALIDATION_NOT_RECEIVE,SHErrorCode.COUPON_INVALIDATION_NOT_RECEIVE.getErrorMessage()));
        }
        boolean flag = publicCouponManager.isInReceiveCouponRange(wechatCouponEntity.getEa(),wechatCouponEntity.getStoreScope(),wechatCouponEntity.getReceiveScope(),vo.getCustomerId());
        if (!flag) {
            return Result.newError(SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.NOT_RECEIVE_STORE_RANGE,SHErrorCode.NOT_RECEIVE_STORE_RANGE.getErrorMessage()));
        }
        String couponLockKey = COUPON_LOCK_PREFIX + vo.getEa() + "_"+ wechatCouponEntity.getStockId();
        boolean lockAcquired = wxCouponPayManager.retryGetCouponLock(couponLockKey, 3);
        if (!lockAcquired) {
            return Result.newError(SHErrorCode.SERVER_BUSY.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.SERVER_BUSY,SHErrorCode.SERVER_BUSY.getErrorMessage()));
        }
        try {
            int count = memberCouponBindDAO.queryReceiveAndUseCoupon(vo.getEa(),wechatCouponEntity.getStockId());
            if (SceneEnum.DEALER.getType() == wechatCouponEntity.getScene()) {
                if (count >= wechatCouponEntity.getDealerCount()) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.COUPON_NOT_COUNT,SHErrorCode.COUPON_NOT_COUNT.getErrorMessage()));
                }
            } else {
                if (count >= wechatCouponEntity.getMaxCoupons()) {
                    return Result.newError(SHErrorCode.COUPON_NOT_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.COUPON_NOT_COUNT,SHErrorCode.COUPON_NOT_COUNT.getErrorMessage()));
                }
            }
            String couponId = wechatCouponEntity.getStockId();
            ReceivePartnerCouponVo receiveVo = new ReceivePartnerCouponVo();
            receiveVo.setEa(wechatCouponEntity.getEa());
            receiveVo.setCouponPlanId(wechatCouponEntity.getWechatCouponId());
            receiveVo.setCouponStockId(wechatCouponEntity.getCouponId());
            //判断是否达到单人领取数量限制
            if (wechatCouponEntity.getMaxCouponsPerUser() != null) {
                int receivePartnerCouponCount = memberCouponBindDAO.queryReceiveCouponCount(wechatCouponEntity.getEa(), wechatCouponEntity.getStockId(), null, null, vo.getCustomerId());
                if (receivePartnerCouponCount >= wechatCouponEntity.getMaxCouponsPerUser()) {
                    return Result.newError(SHErrorCode.USER_RECEIVE_COUNT.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.USER_RECEIVE_COUNT,SHErrorCode.USER_RECEIVE_COUNT.getErrorMessage()));
                }
            }
            WxCouponPayManager.DownTenantResult downTenantIdResult = wxCouponPayManager.getOutTenantIdByAccountId(vo.getCustomerId(), vo.getEa());
            String partnerId = downTenantIdResult.getOutTenantId();
            //进行存入实例对象
            com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResultResult = wxCouponPayManager.createCouponInstObj(receiveVo, partnerId, vo.getCustomerId(), null, couponId);
            if (!addResultResult.isSuccess() || addResultResult.getData() == null) {
                return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.SYSTEM_ERROR,SHErrorCode.SYSTEM_ERROR.getErrorMessage()));
            }
            //保存到本地库
            MemberCouponBindEntity memberCouponEntity = BeanUtil.copy(vo,MemberCouponBindEntity.class);
            memberCouponEntity.setEa(wechatCouponEntity.getEa());
            memberCouponEntity.setCouponId(wechatCouponEntity.getStockId());
            memberCouponEntity.setId(UUIDUtil.getUUID());
            memberCouponEntity.setPartnerId(partnerId);
            memberCouponEntity.setAccountId(vo.getCustomerId());
            memberCouponEntity.setMemberId(null);
            memberCouponEntity.setSendTime(DateUtil.format(DateUtil.now()));
            memberCouponEntity.setStatus("1");
            memberCouponBindDAO.saveMemberCouponBind(memberCouponEntity);
            ThreadPoolUtils.executeWithTraceContext(() -> {
                //添加优惠券领取营销动态
                ObjectData objectData = crmV2Manager.getDetail(vo.getEa(), -10000, CrmObjectApiNameEnum.CUSTOMER.getName(), vo.getCustomerId());
                List<String> marketingUserIds = userMarketingAccountManager.associateAncGetUserMarketingAccountIdsByCrmObjectDatas(vo.getEa(),CrmObjectApiNameEnum.CUSTOMER.getName(),Lists.newArrayList(objectData), false);
                if (CollectionUtils.isNotEmpty(marketingUserIds)) {
                    wxCouponPayManager.syncRecordCouponMarketingActivityData(wechatCouponEntity.getEa(),wechatCouponEntity.getId(),wechatCouponEntity.getMarketingEventId(),marketingUserIds.get(0), MarketingUserActionType.RECEIVE_COUPON.getActionType());
                }
            }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            return Result.newSuccess(addResultResult.getData().getObjectData().getId());
        } catch (Exception e) {
            log.warn("receivePartnerCoupon error",e);
            return Result.newError(SHErrorCode.SYSTEM_ERROR.getErrorCode(),I18NUtil.get(ei,MarketingI18NKeyUtil.SYSTEM_ERROR,SHErrorCode.SYSTEM_ERROR.getErrorMessage()));
        } finally {
            redisManager.unLock(couponLockKey);
        }
    }

    @Override
    public Result<PageResult<PartnerReceiveResult>> queryDealerReceiveList(QueryDealerListVo vo) {
        if (publicMetadataManager.isPublicObject(vo.getEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.queryDealerReceiveList(vo);
        }
        PageResult<PartnerReceiveResult> couponPageResult = new PageResult<>();
        List<PartnerReceiveResult> result = Lists.newArrayList();
        couponPageResult.setPageNum(vo.getPageNum());
        couponPageResult.setPageSize(vo.getPageSize());
        couponPageResult.setTotalCount(0);
        couponPageResult.setResult(result);
        WechatCouponEntity wechatCouponEntity = weChatCouponDAO.getById(vo.getObjectId());
        com.github.mybatis.pagination.Page page = new com.github.mybatis.pagination.Page(vo.getPageNum(), vo.getPageSize(), true);
        List<MemberCouponBindEntity> memberCouponBindEntities = memberCouponBindDAO.queryPageMemberCouponList(vo.getEa(),wechatCouponEntity.getStockId(),vo.getStatus(),page);
        if (CollectionUtils.isNotEmpty(memberCouponBindEntities)) {
            couponPageResult.setTotalCount(page.getTotalNum());
            List<String> partnerIds = memberCouponBindEntities.stream().map(MemberCouponBindEntity::getPartnerId).collect(Collectors.toList());
            Map<String, String> companyIdToCompanyMap = fsAddressBookManager.getCompanyIdToCompanyMap(partnerIds);
            memberCouponBindEntities.forEach(memberCouponBindEntity -> {
                PartnerReceiveResult partnerReceiveResult = new PartnerReceiveResult();
                partnerReceiveResult.setReceiveTime(memberCouponBindEntity.getSendTime());
                partnerReceiveResult.setDepositObject(true);
                partnerReceiveResult.setStatus(memberCouponBindEntity.getStatus());
                partnerReceiveResult.setOutUid(memberCouponBindEntity.getAccountId());
                partnerReceiveResult.setBusinessName(companyIdToCompanyMap.get(memberCouponBindEntity.getPartnerId()));
                result.add(partnerReceiveResult);
            });
        }
        return Result.newSuccess(couponPageResult);
    }

    @Override
    public Result<Void> exportDealerStatistic(String ea,Integer fsUserId,PartnerCouponDetailStatisticVo vo) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.exportDealerStatistic(ea,fsUserId,vo);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> exportStoreStatistic(String ea, Integer fsUserId, QueryDealerListVo vo) {
        if (publicMetadataManager.isPublicObject(ea,CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.exportStoreStatistic(ea,fsUserId,vo);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Boolean> checkCouponCanBeReceived(CheckCouponVO vo) {
        if (publicMetadataManager.isPublicObject(vo.getERUpstreamEa(),CrmObjectApiNameEnum.COUPON_OBJ.getName())) {
            return publicCouponService.checkCouponCanBeReceived(vo);
        }
        //针对非公共对象,暂时默认返回true
        return Result.newSuccess(true);
    }

    private CouponBaseInfoBody buildRequestCouponBaseInfoBody(UpdateWeChatCouponVo vo) {
        CouponBaseInfoBody couponBaseInfoBody = new CouponBaseInfoBody();
        ObjectData objectData = new ObjectData();
        ObjectData couponData = new ObjectData();
        JSONObject postData = new JSONObject();
        if (StringUtils.isNotBlank(vo.getComment())){
            postData.put("comment",vo.getComment());
            objectData.put("comment",vo.getComment());
        }
        if (vo.getHideLink() != null){
            objectData.put("hide_link",vo.getHideLink());
        }
        if (vo.getExpiredTip() != null){
            objectData.put("expired_tip",vo.getExpiredTip());
        }
        if (vo.getExpiredDays() != null){
            objectData.put("expired_days",vo.getExpiredDays());
        }
        if (vo.getIsMember() != null){
            objectData.put("is_member",vo.getIsMember());
            couponData.put("is_member",String.valueOf(vo.getIsMember()));
        }
        if (StringUtils.isNotBlank(vo.getTags())){
            objectData.put("tags",vo.getTags());
            couponData.put("tags",vo.getTags());
        }
        if (StringUtils.isNotBlank(vo.getChannel())){
            objectData.put("channel",vo.getChannel());
            couponData.put("channel",vo.getChannel());
        }
        if (vo.getPartnerNoticeVisibilityVO() != null){
            //objectData.put("outer_tenant_range",GsonUtil.getGson().toJson(vo.getPartnerNoticeVisibilityVO()));
            couponData.put("outer_tenant_range",GsonUtil.getGson().toJson(vo.getPartnerNoticeVisibilityVO()));
        }
        if (StringUtils.isNotBlank(vo.getGoodsName())){
            postData.put("goods_name",vo.getGoodsName());
            objectData.put("goods_name",vo.getGoodsName());
        }
        if (StringUtils.isNotBlank(vo.getBackgroundColor()) || StringUtils.isNotBlank(vo.getDescription()) || StringUtils.isNotBlank(vo.getCouponImageUrl())){
            JSONObject displayPatternInfoObject = new JSONObject();
            if (StringUtils.isNotBlank(vo.getBackgroundColor())){
                displayPatternInfoObject.put("background_color",vo.getBackgroundColor());
                objectData.put("background_color",vo.getBackgroundColor());
            }
            if (StringUtils.isNotBlank(vo.getDescription())){
                displayPatternInfoObject.put("description",vo.getDescription());
                objectData.put("description",vo.getDescription());
            }
            if (StringUtils.isNotBlank(vo.getCouponImageUrl())){
                displayPatternInfoObject.put("coupon_image_url",vo.getCouponImageUrl());
                objectData.put("coupon_image_url",vo.getCouponImageUrl());
            }
            postData.put("display_pattern_info",displayPatternInfoObject);
        }

        if (StringUtils.isNotBlank(vo.getUseMethod()) || StringUtils.isNotBlank(vo.getMiniProgramsAppid()) || StringUtils.isNotBlank(vo.getMiniProgramsPath())){
            JSONObject couponUseObject = new JSONObject();
            if (StringUtils.isNotBlank(vo.getUseMethod())){
                couponUseObject.put("use_method",vo.getUseMethod());
                objectData.put("use_method",vo.getUseMethod());
            }
            if (StringUtils.isNotBlank(vo.getMiniProgramsAppid())){
                couponUseObject.put("mini_programs_appid",vo.getMiniProgramsAppid());
            }
            if (StringUtils.isNotBlank(vo.getMiniProgramsPath())){
                couponUseObject.put("mini_programs_path",vo.getMiniProgramsPath());
            }
            postData.put("coupon_use_rule",couponUseObject);
        }

        if (vo.getTargetMaxCoupons() != null){
            objectData.put("max_coupons",vo.getTargetMaxCoupons());
            couponData.put("max_coupons",String.valueOf(vo.getTargetMaxCoupons()));
        }

        if (vo.getMaxCoupons() != null){
            couponData.put("max_coupons",vo.getMaxCoupons());
        }

        postData.put("out_request_no",vo.getModifyBudgetRequestNo());
        couponBaseInfoBody.setPostData(postData.toJSONString());
        couponBaseInfoBody.setObjectData(objectData);
        couponBaseInfoBody.setCouponData(couponData);
        return couponBaseInfoBody;
    }

    private PostCouponBody buildRequestBody(CreateWxCouponVO vo, MerchantConfigEntity merchantConfigEntity, String outRequestNo) {
        PostCouponBody postCouponBody = new PostCouponBody();
        JSONObject postData = new JSONObject();

        //1.封装发放规则
        JSONObject stockUseRuleObject = new JSONObject();
        stockUseRuleObject.put("max_coupons",vo.getMaxCoupons());
        stockUseRuleObject.put("max_coupons_per_user",vo.getMaxCouponsPerUser());

        //2.封装核销规则
        JSONObject couponUseRuleObject = new JSONObject();

        //2.1使用时间
        JSONObject couponAvailableTimeObject = new JSONObject();
        couponAvailableTimeObject.put("available_begin_time",vo.getAvailableBeginTime());
        couponAvailableTimeObject.put("available_end_time",vo.getAvailableEndTime());
        if (vo.getAvailableDayAfterReceive() != null){
            couponAvailableTimeObject.put("available_day_after_receive",vo.getAvailableDayAfterReceive());
        }
        if (vo.getWaitDaysAfterReceive() != null){
            couponAvailableTimeObject.put("wait_days_after_receive",vo.getWaitDaysAfterReceive());
        }

        //2.1.1固定时间
        if (StringUtils.isNotBlank(vo.getAvailableDayTime())){
            JSONObject availableWeekObject = new JSONObject();
            String[] split = vo.getWeekDay().split(",");
            int[] weekDays = new int[split.length];
            for (int i = 0; i < split.length; i++) {
                weekDays[i]=Integer.parseInt(split[i]);
            }
            availableWeekObject.put("week_day",weekDays);
            String[] availables = TimeUtil.timeStrToSecond(vo.getAvailableDayTime()).split(",");
            JSONObject[] jsonObjects = new JSONObject[availables.length];
            ArrayList<JSONObject> jsonObjectList = new ArrayList<>();
            for (String available : availables) {
                JSONObject availableTimeObject = new JSONObject();
                availableTimeObject.put("begin_time",Integer.parseInt(available.split("-")[0]));
                availableTimeObject.put("end_time",Integer.parseInt(available.split("-")[1]));
                jsonObjectList.add(availableTimeObject);
            }
            JSONObject[] jsonObjects1 = jsonObjectList.toArray(jsonObjects);
            availableWeekObject.put("available_day_time",jsonObjects1);
            couponAvailableTimeObject.put("available_week",availableWeekObject);
        } else {
            if (StringUtils.isNotBlank(vo.getWeekDay())){
                JSONObject availableWeekObject = new JSONObject();
                String[] split = vo.getWeekDay().split(",");
                int[] weekDays = new int[split.length];
                for (int i = 0; i < split.length; i++) {
                    weekDays[i]=Integer.parseInt(split[i]);
                }
                availableWeekObject.put("week_day",weekDays);
                couponAvailableTimeObject.put("available_week",availableWeekObject);
            }
        }

        couponUseRuleObject.put("coupon_available_time",couponAvailableTimeObject);

        //2.2使用规则
        JSONObject fixedNormalCouponObject = new JSONObject();
        fixedNormalCouponObject.put("transaction_minimum", vo.getTransactionMinimum());
        if (CouponTypeEnum.NORMAL_COUPON.getName().equals(vo.getStockType())){
            fixedNormalCouponObject.put("discount_amount", vo.getDiscountAmount());
            couponUseRuleObject.put("fixed_normal_coupon",fixedNormalCouponObject);
        } else if (CouponTypeEnum.DISCOUNT_COUPON.getName().equals(vo.getStockType())){
            fixedNormalCouponObject.put("discount_percent", vo.getDiscountPercent());
            couponUseRuleObject.put("discount_coupon",fixedNormalCouponObject);
        } else if (CouponTypeEnum.EXCHANGE_COUPON.getName().equals(vo.getStockType())){
            fixedNormalCouponObject.put("exchange_price", vo.getExchangePrice());
            couponUseRuleObject.put("exchange_coupon",fixedNormalCouponObject);
        }

        //券使用方式:
        couponUseRuleObject.put("use_method",vo.getUseMethod());

        //3.样式信息
        JSONObject displayPatternInfoObject = new JSONObject();
        displayPatternInfoObject.put("description",vo.getDescription());
        displayPatternInfoObject.put("merchant_logo_url",vo.getMerchantLogoUrl());
        displayPatternInfoObject.put("merchant_name",vo.getMerchantName());
        displayPatternInfoObject.put("background_color",vo.getBackgroundColor());
        displayPatternInfoObject.put("coupon_image_url",vo.getCouponImageUrl());

        JSONObject notifyConfigObject  = new JSONObject();
        notifyConfigObject.put("notify_appid",merchantConfigEntity.getAppId());

        postData.put("stock_name", vo.getStockName());
        postData.put("belong_merchant", merchantConfigEntity.getMerchantId());
        if (StringUtils.isNotBlank(vo.getComment())) {
            postData.put("comment", vo.getComment());
        }
        postData.put("goods_name",vo.getGoodsName());
        postData.put("stock_type",vo.getStockType());
        postData.put("out_request_no",outRequestNo);
        postData.put("coupon_code_mode",vo.getCouponCodeMode());
        postData.put("stock_send_rule",stockUseRuleObject);
        postData.put("coupon_use_rule",couponUseRuleObject);
        postData.put("display_pattern_info",displayPatternInfoObject);
        postData.put("notify_config",notifyConfigObject);

        postCouponBody.setPostData(postData.toJSONString());
        RequestBody requestBody = RequestBody.create(null, postData.toJSONString());
        postCouponBody.setFormBody(requestBody);
        return postCouponBody;
    }



}