package com.facishare.marketing.provider.dao.wxthirdplatform;

import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.provider.entity.wxthirdplatform.EaWechatAccountBindEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 限定：一个ea在一个平台下只能绑定一个wxAppId
 */
public interface EaWechatAccountBindDao {
    @Select("Select * FROM ea_wechat_account_bind WHERE ea=#{ea} AND third_platform_id=#{platformId}")
    EaWechatAccountBindEntity getByEaAndThirdPlatformId(@Param("ea") String ea, @Param("platformId") String platformId);

    @Select("Select * FROM ea_wechat_account_bind WHERE ea=#{ea}")
    List<EaWechatAccountBindEntity> getByEa(@Param("ea") String ea);

    @Select("Select wx_app_id FROM ea_wechat_account_bind WHERE ea=#{ea} AND third_platform_id=#{platformId}")
    String getWxAppIdByEa(@Param("ea") String ea, @Param("platformId") String platformId);
    
    @Select("DELETE FROM ea_wechat_account_bind WHERE ea=#{ea} AND third_platform_id=#{platformId}")
    Integer deleteByEaAndPlatformId(@Param("ea") String ea, @Param("platformId") String platformId);

    @Insert("INSERT INTO ea_wechat_account_bind(ea, third_platform_id, wx_app_id, create_time, update_time) VALUES(#{ea}, #{platformId}, #{wxAppId}, NOW(), NOW()) ON CONFLICT DO NOTHING")
    int insert(@Param("ea") String ea, @Param("platformId") String platformId, @Param("wxAppId") String wxAppId);

    @Select("Select ea FROM ea_wechat_account_bind WHERE third_platform_id=#{platformId} AND wx_app_id=#{wxAppId}")
    @FilterLog
    List<String> listEaByPlatformIdAndWxAppId(@Param("platformId") String platformId,  @Param("wxAppId") String wxAppId);

    @Select("DELETE FROM ea_wechat_account_bind WHERE ea=#{ea} AND third_platform_id=#{platformId} AND wx_app_id=#{wxAppId}")
    Integer delete(@Param("ea") String ea, @Param("platformId") String platformId, @Param("wxAppId") String wxAppId);

    @Select("SELECT * FROM ea_wechat_account_bind WHERE third_platform_id=#{platformId} AND wx_app_id=#{wxAppId}")
    EaWechatAccountBindEntity getCustomizedByPlatformIdAndWxAppId( @Param("platformId") String platformId, @Param("wxAppId") String wxAppId);

    @Select("SELECT ea FROM ea_wechat_account_bind")
    List<String> listEaWechatAccountBindEa();

    @Update("UPDATE ea_wechat_account_bind SET wx_app_id=#{appId}, update_time=now() WHERE ea=#{ea} AND third_platform_id=#{platformId}")
    int updateAppIdByPlatform(@Param("ea")String ea, @Param("appId")String appId, @Param("platformId")String platformId);

    @Select("<script>\n"
            + "SELECT ea FROM ea_wechat_account_bind WHERE third_platform_id='YXT' AND wx_app_id NOT IN('wxdf2d0fe00d61af56', 'wx9b9390b6c48a3c81')\n"
            + "</script>")
    List<String> getEaByThirdPlatformIdAndWxAppId();
}
