package com.facishare.marketing.provider.service.qr;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.enums.UserMarketingActionClientEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.mankeep.api.outService.service.OutFileService;
import com.facishare.marketing.api.arg.SaveObjectGroupVisibleArg;
import com.facishare.marketing.api.arg.CancelMaterialTopArg;
import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.ListObjectGroupResult;
import com.facishare.marketing.api.result.qr.QueryCreateChuangKeTieJsSdkOptionResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterByEaListUnitResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterByTypeAndTargetIdUnitResult;
import com.facishare.marketing.api.result.qr.QueryQRPosterDetailResult;
import com.facishare.marketing.api.result.qr.SpreadQRPosterResult;
import com.facishare.marketing.api.result.qr.SyncChuangKeTiePosterResult;
import com.facishare.marketing.api.service.qr.QRPosterService;
import com.facishare.marketing.api.vo.CreateQRPosterVO;
import com.facishare.marketing.api.vo.QueryCreateChuangKeTieJsSdkOptionVO;
import com.facishare.marketing.api.vo.objectgroup.ObjectGroupListResult;
import com.facishare.marketing.common.contstant.material.OperateTypeEnum;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataInviteStatusEnum;
import com.facishare.marketing.common.enums.objectgroup.DefaultObjectGroupEnum;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.MD5Util;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.CardDAO;
import com.facishare.marketing.provider.dao.ObjectGroupRelationDAO;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.hexagon.ObjectGroupDAO;
import com.facishare.marketing.provider.dao.param.qrposter.QRPosterQueryParam;
import com.facishare.marketing.provider.dao.qr.AdvancePosterDAO;
import com.facishare.marketing.provider.dao.qr.QRCodeDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterUserDAO;
import com.facishare.marketing.provider.dto.qrpost.QRPosterEntityDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.objectgrouprelation.ObjectGroupRoleRelationEntity;
import com.facishare.marketing.provider.entity.objecttop.ObjectTopEntity;
import com.facishare.marketing.provider.entity.qr.AdvancePosterEntity;
import com.facishare.marketing.provider.entity.qr.QRCodeEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterUserEntity;
import com.facishare.marketing.provider.innerArg.CreateQRPosterArg;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.MktContentMgmtLogObjManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.logPersistor.MarketingMaterialInstanceLogManager;
import com.facishare.marketing.provider.manager.objectgrouprolerelation.ObjectGroupRelationVisibleManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.manager.qr.QRPosterManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.mybatis.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("qrPosterService")
@Slf4j
public class QRPosterServiceImpl implements QRPosterService {

    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private QRCodeDAO qrCodeDAO;
    @Autowired
    private QRPosterUserDAO qrPosterUserDAO;
    @Autowired
    private CardDAO cardDAO;
    @Autowired
    private QywxUserManager qywxUserManager;
    @Autowired
    private QRPosterManager qrPosterManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private AdvancePosterDAO advancePosterDAO;
    @Autowired
    private SpreadWorkManager spreadWorkManager;
    @Autowired
    private ConferenceManager conferenceManager;
    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private OutFileService outFileService;
    @Autowired
    private HttpManager httpManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private MarketingMaterialInstanceLogManager marketingMaterialInstanceLogManager;
    @ReloadableProperty("chuangeketie.appid")
    private String chuangketieAppId;
    @ReloadableProperty("chuangeketie.secret")
    private String chuangketieSecret;

    @Autowired
    private ObjectGroupManager objectGroupManager;

    @Autowired
    private ObjectGroupDAO objectGroupDAO;

    @Autowired
    private ObjectGroupRelationDAO objectGroupRelationDAO;

    @Autowired
    private ObjectTopManager objectTopManager;

    @Autowired
    private ObjectGroupRelationVisibleManager objectGroupRelationVisibleManager;

    @Autowired
    private MktContentMgmtLogObjManager mktContentMgmtLogObjManager;

    @Override
    public Result<QueryQRPosterByEaListUnitResult> createQRPoster(CreateQRPosterVO vo) {
       String bgPath = vo.getBgTAPath();
        String finalPath = vo.getFinalTAPath();
        if (EmptyUtil.isNullForList(vo.getMarketingEventId(), bgPath, finalPath)) {
            log.warn("QRPosterService.createQRPoster params error");
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        String bgApath = null;
        String bgThumbnailApath = null;
        if (bgPath.startsWith("TA_")) {
            FileV2Manager.FileManagerPicResult bgResult = fileV2Manager.getApathByTApath(bgPath, vo.getEa(), vo.getUserId());
            if (EmptyUtil.isNullForList(bgResult, bgResult.getUrlAPath(), bgResult.getThumbUrlApath())) {
                log.error("QRPosterService.createQRPoster get bg apath failed, bgPath={}, ea={}, userId={}", bgPath, vo.getEa(), vo.getUserId());
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            bgApath = fileV2Manager.getCpathByPath(vo.getEa(), bgResult.getUrlAPath());
            bgThumbnailApath = fileV2Manager.getCpathByPath(vo.getEa(), bgResult.getThumbUrlApath());
        } else if (bgPath.startsWith("A_") || bgPath.startsWith("TC_")) {
            bgApath = fileV2Manager.getCpathByPath(vo.getEa(), bgPath);
            bgThumbnailApath = bgApath;
        } else {
            log.warn("path invalid, path={}", bgPath);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        String finalApath = null;
        String finalThumbnailApath = null;
        if (finalPath.startsWith("TA_")) {
            FileV2Manager.FileManagerPicResult finalResult = fileV2Manager.getApathByTApath(finalPath, vo.getEa(), vo.getUserId());
            if (EmptyUtil.isNullForList(finalResult, finalResult.getUrlAPath(), finalResult.getThumbUrlApath())) {
                log.error("QRPosterService.createQRPoster get final apath failed, finalPath={}, ea={}, userId={}", finalPath,  vo.getEa(), vo.getUserId());
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            finalApath = fileV2Manager.getCpathByPath(vo.getEa(), finalResult.getUrlAPath());
            finalThumbnailApath = fileV2Manager.getCpathByPath(vo.getEa(), finalResult.getThumbUrlApath());
        } else if (finalPath.startsWith("A_") || finalPath.startsWith("TC_")) {
            finalApath = fileV2Manager.getCpathByPath(vo.getEa(),finalPath);
            finalThumbnailApath = finalApath;
        } else {
            log.warn("path invalid, path={}", finalPath);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        if (vo.getType() == QRPosterTypeEnum.INVITATION.getType() && StringUtils.isBlank(vo.getQrPosterId())) {
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.queryListByTypeAndTargetIdNonPage(vo.getEa(), vo.getType(), vo.getTargetId());
            if (CollectionUtils.isNotEmpty(qrPosterEntityList)) {
                return new Result<>(SHErrorCode.QRPOSTER_INVATE_EXIST);
            }
        }
        CreateQRPosterArg createQRPosterArg = new CreateQRPosterArg();
        createQRPosterArg.setQrPostId(vo.getQrPosterId());
        createQRPosterArg.setMarketingEventId(vo.getMarketingEventId());
        createQRPosterArg.setEa(vo.getEa());
        createQRPosterArg.setUserId(vo.getUserId());
        createQRPosterArg.setForwardType(QRPosterForwardTypeEnum.getByType(vo.getForwardType()));
        createQRPosterArg.setQrCodeId(vo.getQrCodeId());
        createQRPosterArg.setQrCodeUrl(vo.getQrCodeUrl());
        createQRPosterArg.setTargetId(vo.getTargetId());
        createQRPosterArg.setWxAppId(vo.getWxAppId());
        createQRPosterArg.setTitle(vo.getTitle());
        createQRPosterArg.setBgAPath(bgApath);
        createQRPosterArg.setBgThumbnailApath(bgThumbnailApath);
        createQRPosterArg.setFinalAPath(finalApath);
        createQRPosterArg.setFinalThumbnailApath(finalThumbnailApath);
        createQRPosterArg.setQrStyle(vo.getQrStyle());
        createQRPosterArg.setType(QRPosterTypeEnum.getByType(vo.getType()));
        createQRPosterArg.setPosterStyle(vo.getPosterStyle());
        createQRPosterArg.setUserAddSettings(vo.getUserAddSettings());
        createQRPosterArg.setFailedOperation(vo.getFailedOperation());
        QRPosterEntity qrPosterEntity = qrPosterManager.createQRPoster(createQRPosterArg);
        if (null == qrPosterEntity) {
            log.warn("QRPosterService.createQRPoster qrPosterManager.createQRPoster failed");
            return Result.newError(SHErrorCode.SYSTEM_ERROR);
        }
        qrPosterManager.batchQueryMarketingEventInfo(vo.getEa(), Arrays.asList(qrPosterEntity.getMarketingEventId()));
        qrPosterManager.batchGetMaterialByIds(vo.getEa(), Arrays.asList(qrPosterEntity));

        QueryQRPosterByEaListUnitResult result = new QueryQRPosterByEaListUnitResult();
        qrPosterManager.setQueryQRPosterByEaListNormalResult(qrPosterEntity, result);
        qrPosterManager.setQueryQRPosterByEaListForwardResult(qrPosterEntity, result);

        //上报神策埋点
        marketingMaterialInstanceLogManager.sendPersistLog(new MarketingMaterialInstanceLogManager.MarketingMaterialInstanceLog(vo.getEa(), vo.getUserId(),  ObjectTypeEnum.QR_POSTER.getType(), null, qrPosterEntity.getId()));
        mktContentMgmtLogObjManager.createByOperateTypeWithObjName(vo.getEa(), vo.getUserId(),
                ObjectTypeEnum.QR_POSTER.getType(), qrPosterEntity.getTitle(), OperateTypeEnum.ADD);
        objectGroupManager.setGroup(vo.getEa(), vo.getUserId(), ObjectTypeEnum.QR_POSTER.getType(), Collections.singletonList(qrPosterEntity.getId()), vo.getGroupId());
        return Result.newSuccess(result);
    }

    @Override
    public Result deleteQRPoster(String ea, Integer userId, String qrPosterId) {
        if (!qrPosterManager.deleteQRPoster(qrPosterId)) {
            return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.QR_POSTER.getType(), Collections.singletonList(qrPosterId));
        mktContentMgmtLogObjManager.createByOperateTypeWithObjectId(ea, userId, ObjectTypeEnum.QR_POSTER.getType(), qrPosterId, OperateTypeEnum.DELETE);
        return Result.newSuccess();
    }

    @Override
    public Result<SpreadQRPosterResult> spreadQRPoster(String qrPosterId, Integer qrCodeId, String ea, Integer userId, String finalPath, String marketingActivityId, String spreadTaskId, String inviteId, String posterEa) {
        String finalApath = null;
        String finalThumbnailApath = null;
        if (finalPath.startsWith("TA_")) {
            FileV2Manager.FileManagerPicResult finalResult = fileV2Manager.getApathByTApath(finalPath, null, null);
            if (EmptyUtil.isNullForList(finalResult, finalResult.getUrlAPath(), finalResult.getThumbUrlApath())) {
                log.error("QRPosterService.spreadQRPoster get final apath failed, finalPath={}, ea={}, userId={}", finalPath, ea, userId);
                return Result.newError(SHErrorCode.SYSTEM_ERROR);
            }
            finalApath = finalResult.getUrlAPath();
            finalThumbnailApath = finalResult.getThumbUrlApath();
        } else if (finalPath.startsWith("A_")||finalPath.startsWith("C_")) {
            finalApath = finalPath;
            finalThumbnailApath = finalPath;
        }else {
            log.warn("path invalid, path={}", finalPath);
            return new Result(SHErrorCode.PARAMS_ERROR);
        }

        if (EmptyUtil.isNullForList(finalPath)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        QRPosterEntity queryQRPosterEntity = qrPosterDAO.queryById(qrPosterId);
        if (null == queryQRPosterEntity) {
            log.warn("QRPosterService.spreadQRPoster qrPosterDAO.queryById failed, qrPosterId={}", qrPosterId);
            return Result.newError(SHErrorCode.QRPOSTER_NOT_FOUND);
        }

        if (QRPosterForwardTypeEnum.getByType(queryQRPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT && QRPosterForwardTypeEnum.getByType(queryQRPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT_CHANNEL_QR_CODE && QRPosterForwardTypeEnum.getByType(queryQRPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.EMPTY_POSTER
                && QRPosterForwardTypeEnum.getByType(queryQRPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.QYWX_QR_CODE) {
            QRCodeEntity qrCodeEntity = qrCodeDAO.queryById(qrCodeId);
            if (null == qrCodeEntity) {
                log.warn("QRPosterService.spreadQRPoster qrCodeDAO.queryById failed, qrCodeId={}", qrCodeId);
                return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
            }
        } else {
            qrCodeId = 0;
        }

        QRPosterUserEntity queryQRPosterUserEntity = qrPosterUserDAO.queryByQRPosterIdAndEaAndUserId(qrPosterId, ea, userId, inviteId);
        if (null != queryQRPosterUserEntity) {
            QRPosterUserEntity qrPosterUserEntity = new QRPosterUserEntity();
            qrPosterUserEntity.setId(queryQRPosterUserEntity.getId());
            qrPosterUserEntity.setApath(finalApath);
            qrPosterUserEntity.setThumbnailApath(finalThumbnailApath);
            qrPosterUserEntity.setQrCodeId(qrCodeId);
            if (qrPosterUserDAO.update(qrPosterUserEntity) != 1) {
                log.error("QRPosterManager.spreadQRPoster qrPosterUserDAO.update failed, entity={}", qrPosterUserEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        } else {
            QRPosterUserEntity qrPosterUserEntity = new QRPosterUserEntity();
            qrPosterUserEntity.setId(UUIDUtil.getUUID());
            qrPosterUserEntity.setQrPosterId(qrPosterId);
            qrPosterUserEntity.setQrCodeId(qrCodeId);
            qrPosterUserEntity.setEa(ea);
            qrPosterUserEntity.setUserId(userId);
            qrPosterUserEntity.setApath(finalApath);
            qrPosterUserEntity.setThumbnailApath(finalThumbnailApath);
            qrPosterUserEntity.setContent(inviteId);
            if (qrPosterUserDAO.insert(qrPosterUserEntity) != 1) {
                log.error("QRPosterManager.spreadQRPoster qrPosterUserDAO.insert failed, entity={}", qrPosterUserEntity);
                return Result.newError(SHErrorCode.OPERATE_DB_FAIL);
            }
        }

        String posterPathEa = posterEa == null ? queryQRPosterEntity.getEa() : posterEa;
        String imageUrl = fileV2Manager.getUrlByPath(posterPathEa, finalApath);
        if (StringUtils.isBlank(imageUrl)) {
            log.error("QRPosterManager.spreadQRPoster getUrlByPath finalApath, imageUrl is null, ea={}, finalApath={}", queryQRPosterEntity.getEa(), finalApath);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        SpreadWorkManager.GetMarketingActivityIdAndSpreadTaskIdResult getMarketingActivityIdAndSpreadTaskIdResult = spreadWorkManager.getMarketingActivityIdAndSpreadTaskId(ea, userId, ObjectTypeEnum.QR_POSTER, qrPosterId, spreadTaskId, marketingActivityId, false);
        if (null == getMarketingActivityIdAndSpreadTaskIdResult) {
            log.warn("QRPosterManager.spreadQRPoster getMarketingActivityIdAndSpreadTaskId failed, ea={}, userId={}, objectId={}, marketingActivityId={}", ea, userId, qrPosterId, marketingActivityId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        marketingActivityId = getMarketingActivityIdAndSpreadTaskIdResult.getMarketingActivityId();
        spreadTaskId = getMarketingActivityIdAndSpreadTaskIdResult.getSpreadTaskId();

        boolean updateResult = spreadWorkManager.updateSpreadTaskAndAddPersonalRecord(ea, userId, spreadTaskId, marketingActivityId);
        if (!updateResult) {
            log.warn("QRPosterManager.spreadQRPoster updateSpreadTaskAndAddPersonalRecord failed, ea={}, userId={}, marketingActivityId={}, spreadTaskId={}", ea, userId, marketingActivityId, spreadTaskId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        if (StringUtils.isNotBlank(inviteId)) {
            // 更改邀约状态
            CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataDAO.getCampaignMergeDataById(inviteId);
            if (campaignMergeDataEntity != null) {
                campaignMergeDataManager.updateCampaignMergeDataInviteStatus(CampaignMergeDataInviteStatusEnum.INVITED.getType(), inviteId, true);
            }
        }

        SpreadQRPosterResult result = new SpreadQRPosterResult();
        result.setPosterFinalApath(finalApath);
        result.setPosterFinalUrl(imageUrl);
        return Result.newSuccess(result);
    }

    @Override
    public Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByEa(String ea, Integer userId, Integer pageSize,
                                                                             Integer pageNum, Long time, String marketingEventId,
                                                                             String title, boolean needDetail, Integer type, String groupId) {
        return this.queryListByEa(ea, userId, pageSize, pageNum, time, marketingEventId, title, needDetail, type, groupId, false);
    }

    @Override
    public Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByEa(String ea, Integer userId, Integer pageSize,
                                                                             Integer pageNum, Long time, String marketingEventId,
                                                                             String title, boolean needDetail, Integer type, String groupId, boolean needCheckMobileDisplay) {
        if (EmptyUtil.isNullForList(ea, pageSize, pageNum)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.isBlank(groupId)) {
            groupId = DefaultObjectGroupEnum.ALL.getId();
        }
        if (pageSize == null || pageSize == 0){
            pageSize = 1;
        }
        time = (time == null ? new Date().getTime() : time);
        QRPosterQueryParam queryParam = new QRPosterQueryParam();
        queryParam.setEa(ea);
        queryParam.setMarketingEventId(marketingEventId);
        queryParam.setTitle(title);
        queryParam.setType(type);
        if (needCheckMobileDisplay) {
            queryParam.setNeedCheckMobileDisplay(needCheckMobileDisplay);
        }
        Page page = new Page(pageNum, pageSize, true);
        List<QueryQRPosterByEaListUnitResult> resultList = new ArrayList<>();

        //List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.queryByEa(ea, page, marketingEventId, title, type);
        List<QRPosterEntityDTO> qrPosterEntityList;
        if (StringUtils.equals(DefaultObjectGroupEnum.ALL.getId(), groupId)) {
            queryParam.setUserId(userId);
           List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, userId, ObjectTypeEnum.QR_POSTER.getType());
           List<String> permissionGroupIdList = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toList());
           queryParam.setPermissionGroupIdList(permissionGroupIdList);
           qrPosterEntityList = qrPosterDAO.getAccessiblePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.CREATED_BY_ME.getId(), groupId)) {
            queryParam.setUserId(userId);
           qrPosterEntityList = qrPosterDAO.getCreateByMePage(queryParam, page);
        } else if (StringUtils.equals(DefaultObjectGroupEnum.NO_GROUP.getId(), groupId)) {
            qrPosterEntityList = qrPosterDAO.noGroupPage(queryParam, page);
        } else {
            List<ObjectGroupEntity> objectGroupEntityList = objectGroupRelationVisibleManager.getAccessibleGroup(ea, userId, ObjectTypeEnum.QR_POSTER.getType());
            Set<String> permissionGroupIdSet = objectGroupEntityList.stream().map(ObjectGroupEntity::getId).collect(Collectors.toSet());
            if (!permissionGroupIdSet.contains(groupId)){
                qrPosterEntityList = Lists.newArrayList();
            } else {
                queryParam.setPermissionGroupIdList(Lists.newArrayList(permissionGroupIdSet));
                queryParam.setUserId(userId);
                List<String> accessibleSubGroupIdList = objectGroupRelationVisibleManager.getAccessibleSubGroupIdList(ea, ObjectTypeEnum.QR_POSTER.getType(), groupId, objectGroupEntityList);
                accessibleSubGroupIdList.add(groupId);
                queryParam.setGroupIdList(accessibleSubGroupIdList);
                qrPosterEntityList = qrPosterDAO.getAccessiblePage(queryParam, page);
            }
        }

        if (CollectionUtils.isNotEmpty(qrPosterEntityList) && needDetail) {
            // 根据海报ID批量查询市场活动标题
            qrPosterManager.batchQueryMarketingEventInfo(ea, qrPosterEntityList.stream().map(QRPosterEntity::getMarketingEventId).collect(Collectors.toList()));
            // 批量查询各物料详情
            qrPosterManager.batchGetMaterialByIds(ea, qrPosterEntityList);
        }

        List<String> imageUrls = Lists.newArrayList();
        for (QRPosterEntityDTO qrPosterEntity : qrPosterEntityList) {
            QueryQRPosterByEaListUnitResult result = new QueryQRPosterByEaListUnitResult();
            qrPosterManager.setQueryQRPosterByEaListNormalResult(qrPosterEntity, result);
            if (needDetail) {
                qrPosterManager.setQueryQRPosterByEaListForwardResult(qrPosterEntity, result);
            }
            if (StringUtils.isNotEmpty(result.getQrPosterApath())) {
                imageUrls.add(result.getQrPosterApath());
            }
            result.setTop(qrPosterEntity.isTop());
            resultList.add(result);
        }
        Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
        resultList.forEach(data -> {
            if (coverMap.containsKey(data.getQrPosterApath())) {
                data.setPhotoSize(coverMap.get(data.getQrPosterApath()));
            }
        });
        PageResult<QueryQRPosterByEaListUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    public Result<PageResult<QueryQRPosterByTypeAndTargetIdUnitResult>> queryListByTypeAndTargetId(String ea, Integer type, String targetId, Integer pageSize, Integer pageNum, Long time) {
        if (EmptyUtil.isNullForList(ea, pageSize, pageNum)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        time = (time == null ? new Date().getTime() : time);

        Page page = new Page(pageNum, pageSize, true);
        List<QueryQRPosterByTypeAndTargetIdUnitResult> resultList = new ArrayList<>();

        List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.queryListByTypeAndTargetId(ea, type, targetId, page);
        for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
            QueryQRPosterByTypeAndTargetIdUnitResult result = new QueryQRPosterByTypeAndTargetIdUnitResult();
            result.setQrPosterId(qrPosterEntity.getId());
            result.setForwardType(qrPosterEntity.getForwardType());
            result.setType(qrPosterEntity.getType());
            result.setQrPosterUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), null, false));
            resultList.add(result);
        }

        PageResult<QueryQRPosterByTypeAndTargetIdUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<QueryQRPosterDetailResult> queryDetail(String ea, Integer userId, String qrPosterId, String inviteId, boolean needAvatar) {
        if (needAvatar && EmptyUtil.isNullForList(ea, userId, qrPosterId)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(qrPosterId);
        if (null == qrPosterEntity) {
            return Result.newError(SHErrorCode.QRPOSTER_NOT_FOUND);
        }

        QueryQRPosterDetailResult result = new QueryQRPosterDetailResult();

        if (QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT &&  QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT_CHANNEL_QR_CODE && QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.EMPTY_POSTER
                && QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType()) != QRPosterForwardTypeEnum.QYWX_QR_CODE) {
            QRCodeEntity qrCodeEntity = qrCodeDAO.queryById(qrPosterEntity.getQrCodeId());
            if (null == qrCodeEntity) {
                return Result.newError(SHErrorCode.QRCODE_NO_FOUND);
            }

            result.setQrCodeAuthCode(qrCodeEntity.getAuthCode());
        }
        
        if (QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType()) == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT_CHANNEL_QR_CODE && !Strings.isNullOrEmpty(qrPosterEntity.getTargetId())){
            List<String> splitIdList = Splitter.on(",").splitToList(qrPosterEntity.getTargetId());
            if (splitIdList.size() == 2){
                result.setWxAppId(splitIdList.get(0));
                result.setTargetId(splitIdList.get(1));
            }
        }

        if (needAvatar) {
            String uid = qywxUserManager.getUidByFsUserInfo(ea, userId);
            if (StringUtils.isEmpty(uid)) {
                return new Result<>(SHErrorCode.FSBIND_USER_NOFOUND);
            }

            CardEntity cardEntity = cardDAO.queryCardInfoByUid(uid);
            if (null == cardEntity) {
                return new Result<>(SHErrorCode.BUSINESSCARD_USER_NOFOUND);
            }

            result.setAvatar(cardEntity.getAvatar());
        }

        result.setQrPosterId(qrPosterEntity.getId());
        result.setForwardType(qrPosterEntity.getForwardType());
        result.setQrCodeId(qrPosterEntity.getQrCodeId());
        result.setTitle(qrPosterEntity.getTitle());
        result.setBgUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getBgApath(), qrPosterEntity.getEa(), false));
        result.setQrPosterUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), qrPosterEntity.getEa(), false));
        result.setQrPosterThumbnailUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getThumbnailApath(), qrPosterEntity.getEa(), false));
        result.setQrPosterApath(qrPosterEntity.getApath());
        result.setQrStyle(qrPosterEntity.getQrStyle());
        result.setPosterStyle(qrPosterEntity.getPosterStyle());
        result.setIsMobileDisplay(qrPosterEntity.getIsMobileDisplay());

        ObjectTypeEnum objectTypeEnum = QRPosterForwardTypeEnum.toObjectTypeEnum(qrPosterEntity.getForwardType());
        if (null != objectTypeEnum) {
            result.setObjectType(objectTypeEnum.getType());
            result.setObjectId(qrPosterEntity.getTargetId());
        }

        result.setCreateTime(qrPosterEntity.getCreateTime().getTime());

        if (StringUtils.isNotBlank(ea) && userId != null) {
            QRPosterUserEntity queryQRPosterUserEntity = qrPosterUserDAO.queryByQRPosterIdAndEaAndUserId(qrPosterId, ea, userId, inviteId);
            if (null != queryQRPosterUserEntity) {
                result.setCreated(true);
            }
        }

        return Result.newSuccess(result);
    }



    @Override
    public Result<PageResult<QueryQRPosterByEaListUnitResult>> queryListByForwardTypeAndTargetId(String ea, Integer userId, Integer pageSize, Integer pageNum, Long time,String marketingEventId, List<Integer> forwardTypes, String targetId, Integer type, boolean needDetail) {
        if (EmptyUtil.isNullForList(ea, pageSize, pageNum, forwardTypes)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        for (Integer forwardType : forwardTypes) {
            QRPosterForwardTypeEnum qrPosterForwardTypeEnum = QRPosterForwardTypeEnum.getByType(forwardType);
            if (null == qrPosterForwardTypeEnum) {
                return Result.newError(SHErrorCode.PARAMS_ERROR);
            }

            if (QRPosterForwardTypeEnum.hasTargetId(qrPosterForwardTypeEnum)) {
                if (EmptyUtil.isNull(targetId)) {
                    return Result.newError(SHErrorCode.PARAMS_ERROR);
                }
            }
        }

        time = (time == null ? new Date().getTime() : time);

        Page page = new Page(pageNum, pageSize, true);
        List<QueryQRPosterByEaListUnitResult> resultList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(forwardTypes)) {
            List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.queryByForwardTypeAndTargetId(ea,marketingEventId, forwardTypes, targetId, page, type);
            if (CollectionUtils.isNotEmpty(qrPosterEntityList) && needDetail) {
                // 根据海报ID批量查询市场活动标题
                qrPosterManager.batchQueryMarketingEventInfo(ea, qrPosterEntityList.stream().map(QRPosterEntity::getMarketingEventId).collect(Collectors.toList()));
                // 批量查询各物料详情
                qrPosterManager.batchGetMaterialByIds(ea, qrPosterEntityList);
            }

            for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
                QueryQRPosterByEaListUnitResult result = new QueryQRPosterByEaListUnitResult();
                qrPosterManager.setQueryQRPosterByEaListNormalResult(qrPosterEntity, result);

                if (needDetail) {
                    qrPosterManager.setQueryQRPosterByEaListForwardResult(qrPosterEntity, result);
                }

                resultList.add(result);
            }
        }

        PageResult<QueryQRPosterByEaListUnitResult> pageResult = new PageResult();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTime(time);
        pageResult.setResult(resultList);
        pageResult.setTotalCount(page.getTotalNum());

        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<QueryCreateChuangKeTieJsSdkOptionResult> queryChuangKeTieJsSdkOption(QueryCreateChuangKeTieJsSdkOptionVO vo) {
        QueryCreateChuangKeTieJsSdkOptionResult result = new QueryCreateChuangKeTieJsSdkOptionResult();
        Map<String, Object> optionMap = new HashMap<>();
        result.setOptionMap(optionMap);
        int hasLicense = 0;
        if (appVersionManager.checkChuangketieOrder(vo.getEa())){
            hasLicense = 1;
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("app_id", chuangketieAppId);
        paramMap.put("expire_time", System.currentTimeMillis() + 3600 * 1000 * 24);
        paramMap.put("user_flag", vo.getEa());
        paramMap.put("device_type", 1);  //终端类型（1: pc端；2: m移动端）
        paramMap.put("charge_type", 0);
        if (vo.getKindId() != null) {
            paramMap.put("kind_id", vo.getKindId());
        }
        if (StringUtils.isNotEmpty(vo.getDesignId())){
            paramMap.put("design_id", vo.getDesignId());
        }
        paramMap.put("enable_authorize", 1);   //是否开启授权书发放（1：开启；0: 不开启）
        if (hasLicense == 1) {
            paramMap.put("coop_material_limit", 2);
            paramMap.put("coop_font_limit", 2);
            paramMap.put("charging_template_limit", 2);
        }else {
            paramMap.put("coop_material_limit", 1);
            paramMap.put("coop_font_limit", 1);
            paramMap.put("charging_template_limit", 1);
        }

        List<String> list = new ArrayList<String>();
        list.addAll(paramMap.keySet());
        Collections.sort(list);
        String combineParamString = "";
        for (int i = 0; i < list.size(); i++){
            String paramName = list.get(i);
            Object paramValue = paramMap.get(paramName);
            combineParamString += paramName;
            combineParamString += "=";
            combineParamString += paramValue;
            if (i != list.size() - 1){
                combineParamString += "&";
            }
        }
        combineParamString += chuangketieSecret;
        log.info("chuangketie combineParamString:{}",combineParamString);
        String sign = MD5Util.md5String(combineParamString).toUpperCase();

        optionMap.putAll(paramMap);
        optionMap.put("sign", sign);
        return Result.newSuccess(result);
    }

    @Override
    @Transactional
    public Result<SyncChuangKeTiePosterResult> syncChuangKeTiePoster(String ea, Integer userId, String designId, String url) {
        SyncChuangKeTiePosterResult result = new SyncChuangKeTiePosterResult();
        byte[] bytes = httpManager.getBytesByUrl(url);
        if (bytes == null){
            log.info("QRPosterServiceImpl.syncChuangKeTiePoster failed download file failed ea:{} designId:{} url:{}", ea, designId, url);
            return Result.newError(SHErrorCode.DOWNLOAD_QRPOSTER_FAILED);
        }
        if (bytes.length > 1024 * 1024 * 10) {
            return Result.newError(SHErrorCode.QR_CODE_TOO_LARGE);
        }
        String apath = fileV2Manager.uploadToApath(bytes, "png", ea);
        if (StringUtils.isEmpty(apath)){
            log.info("QRPosterServiceImpl.syncChuangKeTiePoster failed upload file to warehouse failed ea:{} designId:{} url:{}", ea, designId, url);
            return Result.newError(SHErrorCode.UPLOAD_QRPOSTER_FAILED);
        }

        AdvancePosterEntity entity = advancePosterDAO.queryByDesignId(ea, designId);
        if (entity != null){
            log.info("QRPosterServiceImpl.syncChuangKeTiePoster success designId is exist");
            if (StringUtils.isNotEmpty(entity.getApath())) {
                fileV2Manager.deleteFilesByApath(Lists.newArrayList(entity.getApath()));
            }
            advancePosterDAO.updateByDesignId(ea, apath, designId);
        }else {
            entity = new AdvancePosterEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(ea);
            entity.setFsUserId(userId);
            entity.setApath(apath);
            entity.setDesignId(designId);
            advancePosterDAO.insert(entity);
        }
        result.setApath(apath);
        result.setUrl(fileV2Manager.getUrlByPath(apath, ea, false));
        return Result.newSuccess(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<EditObjectGroupResult> editQRPosterGroup(String ea, Integer fsUserId, EditObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QRPosterServiceImpl.editQRPosterGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        List<String> defaultNames = DefaultObjectGroupEnum.getDefaultNames();
        if (defaultNames.contains(arg.getName())){
            log.info("QRPosterServiceImpl.editQRPosterGroup cannot use default name ea:{} fsUserId:{} arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.HEXAGON_GROUP_FORBIT_USE_DEFAULT_NAME);
        }
        return objectGroupManager.editGroup(ea, fsUserId, arg.getId(), arg.getName(), ObjectTypeEnum.QR_POSTER.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteQRPosterGroup(String ea, Integer fsUserId, DeleteObjectGroupArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            log.info("QRPosterServiceImpl.deleteQRPosterGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        return objectGroupManager.deleteGroup(ea, fsUserId, arg.getId(), ObjectTypeEnum.QR_POSTER.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> setQRPosterGroup(String ea, Integer fsUserId, SetObjectGroupArg arg) {
//        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
//            log.info("QRPosterServiceImpl.setQRPosterGroup only allow appAdmin ea:{} fsUserId:{}, arg:{}", ea, fsUserId, arg);
//            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
//        }
        List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByIds(arg.getObjectIdList());
        if (CollectionUtils.isEmpty(qrPosterEntityList)) {
            return Result.newError(SHErrorCode.QRPOSTER_NOT_FOUND);
        }
        if (qrPosterEntityList.size() < Sets.newHashSet(arg.getObjectIdList()).size()){
            return Result.newError(SHErrorCode.PART_QRPOSTER_NOT_FOUND);
        }

        ObjectGroupEntity objectGroupEntity = objectGroupDAO.getById(ea, arg.getGroupId());
        if (objectGroupEntity == null){
            return Result.newError(SHErrorCode.HEXAGON_GROUP_NOT_EXIST);
        }
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.QR_POSTER.getType(), arg.getObjectIdList());
        List<ObjectGroupRelationEntity> insertList = new ArrayList<>();
        for (String objectId : arg.getObjectIdList()) {
            ObjectGroupRelationEntity newEntity = new ObjectGroupRelationEntity();
            newEntity.setId(UUIDUtil.getUUID());
            newEntity.setEa(ea);
            newEntity.setGroupId(arg.getGroupId());
            newEntity.setObjectId(objectId);
            newEntity.setObjectType(ObjectTypeEnum.QR_POSTER.getType());
            insertList.add(newEntity);
        }
        objectGroupRelationDAO.batchInsert(insertList);
        for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.QR_POSTER.getType(), qrPosterEntity.getTitle(), OperateTypeEnum.SET_GROUP, objectGroupEntity.getName());
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteQRPosterBatch(String ea, Integer fsUserId, DeleteMaterialArg arg) {
        List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByIds(arg.getIdList());
        if (CollectionUtils.isEmpty(qrPosterEntityList)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        qrPosterManager.deleteQRPosterBatch(arg.getIdList());
        objectGroupRelationDAO.batchDeleteObjectFromObjectGroupRelation(ea, ObjectTypeEnum.QR_POSTER.getType(), arg.getIdList());
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QR_POSTER.getType(), arg.getIdList());
        for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
            mktContentMgmtLogObjManager.createByOperateTypeWithObjName(ea, fsUserId, ObjectTypeEnum.QR_POSTER.getType(), qrPosterEntity.getTitle(), OperateTypeEnum.DELETE);
        }
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> topQRPoster(String ea, Integer fsUserId, TopMaterialArg arg) {
        QRPosterEntity entity = qrPosterDAO.queryById(arg.getObjectId());
        if (entity == null) {
            return Result.newError(SHErrorCode.QRPOSTER_NOT_FOUND);
        }
        ObjectTopEntity objectTopEntity = new ObjectTopEntity();
        objectTopEntity.setId(UUIDUtil.getUUID());
        objectTopEntity.setEa(ea);
        objectTopEntity.setObjectId(arg.getObjectId());
        objectTopEntity.setObjectType(ObjectTypeEnum.QR_POSTER.getType());
        objectTopManager.insert(objectTopEntity, fsUserId);
        return Result.newSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> cancelTopQRPoster(String ea, Integer fsUserId, CancelMaterialTopArg arg) {
        //objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QR_POSTER.getType(), Collections.singletonList(arg.getObjectId()));
        objectTopManager.cancelTop(ea, fsUserId, ObjectTypeEnum.QR_POSTER.getType(), arg.getObjectId());

        return Result.newSuccess();
    }

    @Override
    public Result<Void> addQRPosterGroupRole(String ea, Integer fsUserId, SaveObjectGroupVisibleArg arg) {
        if (!objectGroupManager.isAppAdmin(ea, fsUserId)){
            return Result.newError(SHErrorCode.ONNY_ALLOW_APP_ADMIN);
        }
        objectGroupRelationVisibleManager.saveGroupRelation(ea, arg, ObjectTypeEnum.QR_POSTER.getType());
        return Result.newSuccess();
    }

    @Override
    public Result<ObjectGroupListResult> listQRPosterGroup(String ea, Integer fsUserId, ListGroupArg arg) {
        List<ListObjectGroupResult> resultList = new ArrayList<>();
        // 获取客户自定义分组的信息,只会返回有权限查看的分组
        ObjectGroupListResult customizeGroupListVO = objectGroupManager.getShowGroup(ea, fsUserId, ObjectTypeEnum.QR_POSTER.getType(), null, null);
        List<String> groupIdList = customizeGroupListVO.getObjectGroupList().stream().map(ListObjectGroupResult::getGroupId).collect(Collectors.toList());
        if (arg.getUseType() != null && arg.getUseType() == 0) {
            // 统计系统分组的数量
            for (DefaultObjectGroupEnum objectGroup : DefaultObjectGroupEnum.getByUserId(fsUserId)) {
                ListObjectGroupResult objectGroupResult = new ListObjectGroupResult();
                objectGroupResult.setSystem(true);
                objectGroupResult.setGroupId(objectGroup.getId());
                objectGroupResult.setGroupName(objectGroup.getName());
                objectGroupResult.setRoleNameList(Collections.singletonList(I18nUtil.get(I18nKeyEnum.MARK_MANAGER_OBJECTGROUPMANAGER_491)));
                if (objectGroup == DefaultObjectGroupEnum.ALL){
                    // 如果没有查看任何一个分组的权限，只能看未分类的 + 我创建的
                    if (CollectionUtils.isEmpty(groupIdList)) {
                        objectGroupResult.setObjectCount(qrPosterDAO.queryUnGroupAndCreateByMeCount(ea, fsUserId));
                    } else {
                        // 如果有分组权限，可以查看 未分类 + 有权限的分组 + 我创建的
                        objectGroupResult.setObjectCount(qrPosterDAO.queryAccessibleCount(ea, groupIdList, fsUserId));
                    }
                } else if (objectGroup == DefaultObjectGroupEnum.CREATED_BY_ME){
                    objectGroupResult.setObjectCount(qrPosterDAO.queryCountCreateByMe(ea, fsUserId));
                } else if (objectGroup == DefaultObjectGroupEnum.NO_GROUP){
                    objectGroupResult.setObjectCount(qrPosterDAO.queryCountByUnGrouped(ea));
                }
                resultList.add(objectGroupResult);
            }
        }
        ObjectGroupListResult vo = new ObjectGroupListResult();
        vo.setSortVersion(customizeGroupListVO.getSortVersion());
        resultList.addAll(customizeGroupListVO.getObjectGroupList());
        vo.setObjectGroupList(resultList);
        return Result.newSuccess(vo);
    }

    @Override
    public Result<List<String>> getGroupRole(String groupId) {
        List<ObjectGroupRoleRelationEntity> roleRelationEntityList = objectGroupRelationVisibleManager.getRoleRelationByGroupId(groupId);
        List<String> roleIdList = roleRelationEntityList.stream().map(ObjectGroupRoleRelationEntity::getRoleId).collect(Collectors.toList());
        return Result.newSuccess(roleIdList);
    }
}