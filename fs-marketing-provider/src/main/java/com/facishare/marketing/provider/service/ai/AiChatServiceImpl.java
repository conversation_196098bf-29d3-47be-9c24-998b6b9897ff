package com.facishare.marketing.provider.service.ai;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.AiChatRecordArg;
import com.facishare.marketing.api.result.ai.*;
import com.facishare.marketing.api.service.ai.AiChatService;
import com.facishare.marketing.api.vo.ai.*;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.contstant.QywxUserConstants;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.threadpool.NamedThreadPool;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.FsAddressBookManager;
import com.facishare.marketing.provider.manager.ai.*;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.remote.CrmMetadataManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.sharegpt.store.chat.manager.AIChatObjManager;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatRecordObj;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatSessionObj;
import com.facishare.marketing.provider.sharegpt.store.chat.obj.AIChatShareObj;
import com.facishare.marketing.provider.sharegpt.utils.ObjectDataUtil;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Wheres;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectLayoutService;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.params.SetParams;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service("aiChatService")
public class AiChatServiceImpl implements AiChatService {

    @Autowired
    private AiChatManager aiChatManager;
    @Autowired
    private CrmMetadataManager crmMetadataManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private AIChatObjManager aiChatObjManager;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private EIEAConverter converter;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private PaaSAgentManager paaSAgentManager;
    @Autowired
    private ObjectLayoutService objectLayoutService;
    @Autowired
    private UserPrivilegeRestService userPrivilegeRestService;
    @Autowired
    private IATAuthManager iatAuthManager;
    @Autowired
    private StreamingAuthManager streamingAuthManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private PaaSPromptManager paaSPromptManager;
    @Autowired
    private MergeJedisCmd jedisCmd;

    private static final int DEFAULT_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 4;
    public static ExecutorService executor = NamedThreadPool.newFixedThreadPool(DEFAULT_POOL_SIZE, "PROMPT_COMPLETE_POOL");

    @Override
    public Result<String> assembleRequestUrl(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        return Result.newSuccess(iatAuthManager.assembleRequestUrl());
    }

    @Override
    public Result<String> streamingRequestUrl(String ea, Integer fsUserId) {
        return Result.newSuccess(streamingAuthManager.streamingRequestUrl(ea, fsUserId));
    }

    @Override
    public Result matchScene(String ea, Integer fsUserId, MatchSceneVO vo) {
        if (StringUtils.isEmpty(vo.getUrl()) || StringUtils.isEmpty(vo.getClient())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("url", vo.getUrl());
        paramMap.put("client", vo.getClient());
        ObjectData objectData = crmV2Manager.queryObjectData(ea, "AISceneObj", paramMap);
        if (objectData != null) {
            AiSceneResult aiSceneResult = new AiSceneResult();
            aiSceneResult.setId(objectData.getId());
            aiSceneResult.setName(objectData.getName());
            aiSceneResult.setParams(objectData.getString("params"));
            aiSceneResult.setCallback(objectData.getString("callback"));
            List<String> promptIds = objectData.get("prompts") != null ? (List<String>) (objectData.get("prompts")) : null;
            if (CollectionUtils.isNotEmpty(promptIds)) {
                List<ObjectData> promptObjs = crmV2Manager.queryObjectDatas(ea, "PromptObj", Maps.newHashMap(ImmutableMap.of("_id", promptIds)));
                aiSceneResult.setPrompts(promptObjs);
            }
            return Result.newSuccess(aiSceneResult);
        }
        return Result.newSuccess();
    }

    @Override
    public Result createPrompt(String ea, Integer fsUserId, CreatePromptVO obj) {
        ObjectData dataMap = new ObjectData();
        dataMap.put("title", obj.getTitle());
        dataMap.put("category", obj.getCategory());
        dataMap.put("content", obj.getPrompt());
        dataMap.put("scope_type", obj.getScopeType());
        dataMap.setOwner(fsUserId);
        ActionAddArg arg = new ActionAddArg();
        arg.setObjectData(dataMap);
        return Result.newSuccess(metadataActionService.add(new HeaderObj(converter.enterpriseAccountToId(ea), fsUserId), "PromptObj", false, arg));
    }

    @Override
    public Result editPrompt(String ea, Integer fsUserId, CreatePromptVO obj) {
        ObjectData dataMap = new ObjectData();
        dataMap.put("_id", obj.getId());
        dataMap.put("title", obj.getTitle());
        dataMap.put("category", obj.getCategory());
        dataMap.put("content", obj.getPrompt());
        dataMap.put("scope_type", obj.getScopeType());
        dataMap.setOwner(fsUserId);
        ActionEditArg arg = new ActionEditArg();
        arg.setObjectData(dataMap);
        return Result.newSuccess(metadataActionService.edit(new HeaderObj(converter.enterpriseAccountToId(ea), fsUserId), "PromptObj", false, false, arg));
    }

    @Override
    public Result<PageResult> pageQueryPrompt(String ea, Integer fsUserId, PageQueryPromptVO vo) {
        PageResult pageResult = new PageResult();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        PaasQueryArg arg = new PaasQueryArg((vo.getPageNum() - 1) * vo.getPageSize(), vo.getPageSize());
        BiFunction<String, List<PaasQueryArg.Condition>, Wheres> genWhere = (connector, filters) -> {
            Wheres wheres = new Wheres();
            wheres.setConnector(connector);
            filters.forEach(e -> wheres.addFilter(e.getFieldName(), e.getFieldValues(), e.getOperator()));
            return wheres;
        };
        List<Wheres> wheresList = Lists.newArrayList();
        switch (vo.getCategory()) {
            case "ALL"://全部
                wheresList.add(genWhere.apply("OR", Lists.newArrayList(new PaasQueryArg.Condition("scope_type", Lists.newArrayList("ALL"), OperatorConstants.EQ))));
                wheresList.add(genWhere.apply("OR", Lists.newArrayList(new PaasQueryArg.Condition("scope_type", Lists.newArrayList(), OperatorConstants.IS))));
                wheresList.add(genWhere.apply("OR", Lists.newArrayList(new PaasQueryArg.Condition("created_by", Lists.newArrayList(String.valueOf(fsUserId)), OperatorConstants.EQ))));
                arg.setWheres(wheresList);
                break;
            case "CREATION"://我创建的
                arg = new PaasQueryArg((vo.getPageNum() - 1) * vo.getPageSize(), vo.getPageSize());
                arg.addFilter("created_by", OperatorConstants.EQ, Lists.newArrayList(String.valueOf(fsUserId)));
                break;
            case "COLLECTION"://我收藏的
                // 查询收藏夹
                PaasQueryArg queryCollectionArg = new PaasQueryArg(0, 1000);//正常应该不会超出这个数吧?
                queryCollectionArg.addFilter("created_by", OperatorConstants.EQ, Lists.newArrayList(String.valueOf(fsUserId)));
                FindByQueryV3Arg queryCollectionFindByQueryV3Arg = new FindByQueryV3Arg();
                queryCollectionFindByQueryV3Arg.setSearchQueryInfoJson(GsonUtil.toJson(queryCollectionArg));
                queryCollectionFindByQueryV3Arg.setSelectFields(Collections.singletonList("prompt_id"));
                queryCollectionFindByQueryV3Arg.setDescribeApiName("PromptBookmarkObj");
                InnerPage<ObjectData> collectionPage = crmMetadataManager.listV3(ea, fsUserId, queryCollectionFindByQueryV3Arg);
                List<String> ids = null;
                if (collectionPage != null && CollectionUtils.isNotEmpty(collectionPage.getDataList())) {
                    ids = collectionPage.getDataList().stream().map(e -> e.getString("prompt_id")).collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(ids)) {
                    return Result.newSuccess(pageResult);
                }
                arg.addFilter("_id", OperatorConstants.IN, ids);
                break;
            default:
                arg.addFilter("category", OperatorConstants.EQ, Lists.newArrayList(vo.getCategory()));
                wheresList.add(genWhere.apply("OR", Lists.newArrayList(new PaasQueryArg.Condition("scope_type", Lists.newArrayList("ALL"), OperatorConstants.EQ))));
                wheresList.add(genWhere.apply("OR", Lists.newArrayList(new PaasQueryArg.Condition("scope_type", Lists.newArrayList(), OperatorConstants.IS))));
                wheresList.add(genWhere.apply("OR", Lists.newArrayList(new PaasQueryArg.Condition("created_by", Lists.newArrayList(String.valueOf(fsUserId)), OperatorConstants.EQ))));
                arg.setWheres(wheresList);
        }
        arg.addFilter("status", OperatorConstants.EQ, Lists.newArrayList("1"));
        arg.addFilter("parent_prompt", OperatorConstants.IS, Lists.newArrayList());
        if (StringUtils.isNotBlank(vo.getKeyword())) {
            arg.addFilter("title", OperatorConstants.LIKE, Lists.newArrayList(vo.getKeyword()));
        }
        FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
        findByQueryV3Arg.setSearchQueryInfoJson(GsonUtil.toJson(arg));
        findByQueryV3Arg.setSelectFields(vo.getSelectFields());
        findByQueryV3Arg.setDescribeApiName("PromptObj");
        InnerPage<ObjectData> objectDataInnerPage = crmMetadataManager.listV3(ea, fsUserId, findByQueryV3Arg);
        if (objectDataInnerPage == null || CollectionUtils.isEmpty(objectDataInnerPage.getDataList())) {
            return Result.newSuccess(pageResult);
        }
        List<ObjectData> dataList = objectDataInnerPage.getDataList();

        List<CompletableFuture<Void>> tasks = Lists.newArrayList();
        ExecutorService executorService = NamedThreadPool.newFixedThreadPool(2, "batch_add_tag_thread");
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = Maps.newHashMap();
        Map<String, String> pathMap = Maps.newHashMap();
        Map<String, ObjectData> collectionMap = Maps.newHashMap();
        // 批量查询头像数据
        tasks.add(CompletableFuture.runAsync(() -> {
            List<Integer> fsUserIds = dataList.stream().map(ObjectData::getCreateBy).filter(Objects::nonNull).collect(Collectors.toList());
            fsEmployeeMsgMap.putAll(fsAddressBookManager.getEmployeeInfoByUserIds(ea, fsUserIds, true));
            List<String> npaths = Lists.newArrayList();
            List<FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgList = Lists.newArrayList(fsEmployeeMsgMap.values());
            fsEmployeeMsgList.forEach(data -> {
                if (data.getProfileImage() != null && (data.getProfileImage().startsWith("N_") || data.getProfileImage().startsWith("A_"))) {
                    npaths.add(data.getProfileImage());
                }
            });
            Map<String, String> urlMap = fileV2Manager.batchGetUrlByPath(npaths, ea, false);
            if (null != urlMap) {
                pathMap.putAll(urlMap);
            }
        }, executorService));
        // 批量查询是否被收藏
        if (!"COLLECTION".equals(vo.getCategory())) {
            tasks.add(CompletableFuture.runAsync(()->{
                List<String> promptIds = dataList.stream().map(ObjectData::getId).collect(Collectors.toList());
                PaasQueryArg queryCollectionArg = new PaasQueryArg(0, 1000);
                queryCollectionArg.addFilter("prompt_id", OperatorConstants.IN, promptIds);
                queryCollectionArg.addFilter("created_by", OperatorConstants.EQ, Lists.newArrayList(String.valueOf(fsUserId)));
                FindByQueryV3Arg queryCollectionFindByQueryV3Arg = new FindByQueryV3Arg();
                queryCollectionFindByQueryV3Arg.setSearchQueryInfoJson(GsonUtil.toJson(queryCollectionArg));
                queryCollectionFindByQueryV3Arg.setDescribeApiName("PromptBookmarkObj");
                InnerPage<ObjectData> collectionPage = crmMetadataManager.listV3(ea, fsUserId, queryCollectionFindByQueryV3Arg);
                if (collectionPage != null && CollectionUtils.isNotEmpty(collectionPage.getDataList())) {
                    List<ObjectData> objectDatas = collectionPage.getDataList();
                    objectDatas.forEach(data -> {
                        collectionMap.put(data.getString("prompt_id"), data);
                    });
                }
            }));
        }
        tasks.forEach(CompletableFuture::join);
        List<PageQueryPromptResult> pageQueryPromptResults = dataList.stream()
                .map(e -> {
                    PageQueryPromptResult pageQueryPromptResult = new PageQueryPromptResult();
                    pageQueryPromptResult.setObjectData(e);
                    //挂个头像
                    if (fsEmployeeMsgMap.get(e.getCreateBy()) != null && fsEmployeeMsgMap.get(e.getCreateBy()).getProfileImage() != null) {
                        if (fsEmployeeMsgMap.get(e.getCreateBy()).getProfileImage().startsWith("N_") || fsEmployeeMsgMap.get(e.getCreateBy()).getProfileImage().startsWith("A_")) {
                            pageQueryPromptResult.setAvatar(pathMap.get(fsEmployeeMsgMap.get(e.getCreateBy()).getProfileImage()));
                        } else {
                            pageQueryPromptResult.setAvatar(fsEmployeeMsgMap.get(e.getCreateBy()).getProfileImage());
                        }
                    }
                    //是否被收藏
                    if ("COLLECTION".equals(vo.getCategory())) {
                        pageQueryPromptResult.setIsMark(true);
                    } else if (collectionMap.get(e.getId()) != null) {
                        pageQueryPromptResult.setIsMark(true);
                    }
                    //用户名称
                    if (fsEmployeeMsgMap.get(e.getCreateBy()) != null) {
                        pageQueryPromptResult.setUserName(fsEmployeeMsgMap.get(e.getCreateBy()).getName());
                    }
                    if (fsUserId.equals(e.getCreateBy())) {
                        pageQueryPromptResult.setIsMine(true);
                    }
                    return pageQueryPromptResult;
                })
                .collect(Collectors.toList());
        pageResult.setResult(pageQueryPromptResults);
        pageResult.setTotalCount(objectDataInnerPage.getTotalCount());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result markPrompt(String ea, Integer fsUserId, String id) {
        if (StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ThreadPoolUtils.executeWithNewThread("markPrompt", () -> {
            PaasQueryArg queryCollectionArg = new PaasQueryArg(0, 1);
            queryCollectionArg.addFilter("prompt_id", OperatorConstants.EQ, Lists.newArrayList(id));
            queryCollectionArg.addFilter("created_by", OperatorConstants.EQ, Lists.newArrayList(String.valueOf(fsUserId)));
            FindByQueryV3Arg queryCollectionFindByQueryV3Arg = new FindByQueryV3Arg();
            queryCollectionFindByQueryV3Arg.setSearchQueryInfoJson(GsonUtil.toJson(queryCollectionArg));
            queryCollectionFindByQueryV3Arg.setDescribeApiName("PromptBookmarkObj");
            InnerPage<ObjectData> collectionPage = crmMetadataManager.listV3(ea, fsUserId, queryCollectionFindByQueryV3Arg);
            if (collectionPage != null && CollectionUtils.isNotEmpty(collectionPage.getDataList())) {
                ObjectData collection = collectionPage.getDataList().get(0);
                crmV2Manager.bulkInvalid(ea, fsUserId, "PromptBookmarkObj", Lists.newArrayList(collection.getId()));
                log.info("bulkInvalid ea:{} id:{}", ea, collection.getId());
            } else if (collectionPage != null) {
                ObjectData collection = new ObjectData();
                collection.put("prompt_id", id);
                collection.setOwner(fsUserId);
                ActionAddArg arg = new ActionAddArg();
                arg.setObjectData(collection);
                com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> addResultResult = metadataActionService.add(new HeaderObj(converter.enterpriseAccountToId(ea), fsUserId), "PromptBookmarkObj", false, arg);
                log.info("addResultResult={}", addResultResult);
            }
        });
        return Result.newSuccess();
    }

    @Override
    public Result<String> createShare(String ea, Integer fsUserId, AiChatShareVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isEmpty(vo.getSessionId()) || StringUtils.isEmpty(vo.getRecordIds())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        ObjectData objectData = aiChatObjManager.add(ea, fsUserId, AIChatShareObj.OBJECT_API_NAME, new AIChatShareObj()
                .setSessionId(vo.getSessionId())
                .setRecordIds(vo.getRecordIds())
                .setSharerId(String.valueOf(fsUserId))
        );
        return Result.newSuccess(objectData.getId());
    }

    @Override
    public Result<AiChatShareDetailResult> getShareDetail(String ea, Integer fsUserId, String id) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AiChatShareDetailResult result = new AiChatShareDetailResult();
        AIChatShareObj aiChatShareObj = AIChatShareObj.wrap(aiChatObjManager.getById(ea, AIChatShareObj.OBJECT_API_NAME, null, id));
        if (aiChatShareObj.isEmpty()) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        result.setSessionId(aiChatShareObj.getSessionId());
        List<String> recordIdList = aiChatShareObj.getRecordIds();
        if (CollectionUtils.isEmpty(recordIdList)) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("_id", OperatorConstants.IN, recordIdList);
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(AIChatRecordObj.OBJECT_API_NAME);
        List<AIChatRecordObj> recordObjs = AIChatRecordObj.wrap(aiChatObjManager.listAllByFilter(ea, paasQueryFilterArg));
        List<AiChatRecordResult> records = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(recordObjs)) {
            recordObjs.forEach(e -> records.add(ObjectDataUtil.objectDataResultMapping(e))
            );
        }
        result.setRecords(records);
        return Result.newSuccess(result);
    }

    @Override
    public Result<String> getCurrentUserAvatar(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        String avatar = null;
        Map<Integer, FsAddressBookManager.FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getFsEmployeeInfoByUserIds(ea, Collections.singletonList(fsUserId), true);
        List<String> npaths = Lists.newArrayList();
        FsAddressBookManager.FSEmployeeMsg data = fsEmployeeMsgMap.get(fsUserId);
        if (data.getProfileImage() != null && (data.getProfileImage().startsWith("N_") || data.getProfileImage().startsWith("A_"))) {
            npaths.add(data.getProfileImage());
        }
        Map<String, String> pathMap = fileV2Manager.batchGetUrlByPath(npaths, ea, false);
        //挂个头像
        if (fsEmployeeMsgMap.get(fsUserId) != null && fsEmployeeMsgMap.get(fsUserId).getProfileImage() != null) {
            if (pathMap != null && (fsEmployeeMsgMap.get(fsUserId).getProfileImage().startsWith("N_") || fsEmployeeMsgMap.get(fsUserId).getProfileImage().startsWith("A_"))) {
                avatar = pathMap.get(fsEmployeeMsgMap.get(fsUserId).getProfileImage());
            } else {
                avatar = fsEmployeeMsgMap.get(fsUserId).getProfileImage();
            }
        }
        return Result.newSuccess(avatar);
    }

    @Override
    public Result<Boolean> deleteSession(String ea, Integer fsUserId, String id) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        aiChatObjManager.bulkInvalid(ea, AIChatSessionObj.OBJECT_API_NAME, Lists.newArrayList(id));

        ThreadPoolUtils.execute(() -> {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("session_id", OperatorConstants.EQ, Collections.singletonList(id));
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatRecordObj.OBJECT_API_NAME);
            List<ObjectData> objectData = aiChatObjManager.listAllByFilter(ea, paasQueryFilterArg);
            if (!objectData.isEmpty()) {
                List<String> recordIds = objectData.stream().map(ObjectData::getId).collect(Collectors.toList());
                aiChatObjManager.bulkInvalid(ea, AIChatRecordObj.OBJECT_API_NAME, recordIds);
            }
        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return Result.newSuccess();
    }

    @Override
    public Result createRecord(String ea, Integer fsUserId, AiChatRecordCreateVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        return this.getRecord(ea, fsUserId, aiChatManager.createRecord(ea, fsUserId, vo));
    }

    @Override
    public Result<Boolean> deleteRecord(String ea, Integer fsUserId, String id) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        aiChatObjManager.bulkInvalid(ea, AIChatRecordObj.OBJECT_API_NAME, Lists.newArrayList(id));
        return Result.newSuccess();
    }

    @Override
    public Result editSession(String ea, Integer fsUserId, String id, String title) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isEmpty(title)) {
            return com.facishare.marketing.common.result.Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        if (StringUtils.length(title) > 100) {
            return com.facishare.marketing.common.result.Result.newError(SHErrorCode.AI_CHAT_QUESTION_TOO_LONG);
        }
        AIChatSessionObj aiChatSessionObj = AIChatSessionObj.wrap(aiChatObjManager.getById(ea, AIChatSessionObj.OBJECT_API_NAME, null, id));
        if (aiChatSessionObj.isEmpty()) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        aiChatSessionObj.setTitle(title);
        aiChatObjManager.edit(ea, AIChatSessionObj.OBJECT_API_NAME, aiChatSessionObj);

        return com.facishare.marketing.common.result.Result.newSuccess();
    }

    @Override
    public Result setSessionTop(String ea, Integer fsUserId, String id, boolean isTop) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }

        AIChatSessionObj aiChatSessionObj = AIChatSessionObj.wrap(aiChatObjManager.getById(ea, AIChatSessionObj.OBJECT_API_NAME, null, id));
        if (aiChatSessionObj.isEmpty()) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        aiChatSessionObj.setTop(isTop);
        aiChatObjManager.edit(ea, AIChatSessionObj.OBJECT_API_NAME, aiChatSessionObj);

        return com.facishare.marketing.common.result.Result.newSuccess();
    }

    @Override
    public Result editRecord(String ea, Integer fsUserId, String id, String content, int actionStatus) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        AIChatRecordObj aiChatRecordObj = AIChatRecordObj.wrap(aiChatObjManager.getById(ea, AIChatRecordObj.OBJECT_API_NAME, null, id));
        if (aiChatRecordObj.isEmpty()) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        aiChatRecordObj.setContent(content);
        aiChatRecordObj.setActionStatus(actionStatus);
        aiChatObjManager.edit(ea, AIChatRecordObj.OBJECT_API_NAME, aiChatRecordObj);

        return this.getRecord(ea, fsUserId, id);
    }

    @Override
    public Result editRecordV2(String ea, Integer fsUserId, AiChatRecordArg arg) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isBlank(arg.getId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AIChatRecordObj aiChatRecordObj = AIChatRecordObj.wrap(aiChatObjManager.getById(ea, AIChatRecordObj.OBJECT_API_NAME, null, arg.getId()));
        if (aiChatRecordObj.isEmpty()) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        aiChatRecordObj.setTips(arg.getTips());
        aiChatRecordObj.setContent(arg.getContent());
        aiChatRecordObj.setAction(arg.getAction());
        aiChatRecordObj.setActions(arg.getActions());
        aiChatRecordObj.setData(arg.getData());
        aiChatRecordObj.setActionStatus(arg.getActionStatus());
        aiChatObjManager.edit(ea, AIChatRecordObj.OBJECT_API_NAME, aiChatRecordObj);

        return this.getRecord(ea, fsUserId, arg.getId());
    }

    @Override
    public Result getRecord(String ea, Integer fsUserId, String id) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return Result.newSuccess(aiChatManager.getRecord(ea, fsUserId, id));
    }

    @Override
    public Result report(String ea, Integer fsUserId, AiChatRecordArg arg) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isBlank(arg.getId()) || StringUtils.isBlank(arg.getOperator())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AIChatRecordObj aiChatRecordObj = AIChatRecordObj.wrap(aiChatObjManager.getById(ea, AIChatRecordObj.OBJECT_API_NAME, null, arg.getId()));
        if (aiChatRecordObj.isEmpty()) {
            return Result.newError(SHErrorCode.NO_DATA);
        }
        try {
            // 埋点上报平台
            String apiName = aiChatRecordObj.getAgentName();
            String instanceId = aiChatRecordObj.getInstanceId();
            String businessName = aiChatRecordObj.getProperty().getBusinessName();
            if (StringUtils.isNotEmpty(apiName) && StringUtils.isNotEmpty(instanceId)) {
                AgentReportRequest agentReportRequest = new AgentReportRequest();
                agentReportRequest.setApiName(apiName);
                agentReportRequest.setInstanceId(instanceId);
                agentReportRequest.setOperator(arg.getOperator());
                paaSAgentManager.agentReport(ea, fsUserId, arg.getBusinessName(), agentReportRequest);
            }
        } catch (Exception e) {
            log.warn("上报用户操作失败:{}", arg, e);
        }

        //1-踩, 2-赞
        try {
            int op = 0;
            switch (arg.getOperator()) {
                case "cai":
                    op = 1;
                    break;
                case "zan":
                    op = 2;
                    break;
            }
            aiChatRecordObj.setLikeStatus(op);
            aiChatObjManager.edit(ea, AIChatRecordObj.OBJECT_API_NAME, aiChatRecordObj);
        } catch (Exception e) {
            log.warn("点踩状态更新失败:{}", arg, e);
        }

        return com.facishare.marketing.common.result.Result.newSuccess();
    }

    @Override
    public Result getAgentWelcome(String ea, Integer fsUserId, AgentWelcomeVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isBlank(vo.getBizSessionId()) && StringUtils.isBlank(vo.getApiName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("biz_session_id", OperatorConstants.EQ, Collections.singletonList(vo.getBizSessionId()));
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(AIChatSessionObj.OBJECT_API_NAME);
        ObjectData objectData = aiChatObjManager.getByFilter(ea, paasQueryFilterArg);
        if (objectData == null) {
            AgentWelcomeRequest req = new AgentWelcomeRequest();
            req.setApiName(vo.getApiName());
            AgentResponse<AgentResponse.AgentWelcomeResult> agentWelcomeResultAgentResponse = paaSAgentManager.agentWelcome(ea, fsUserId, vo.getBusinessName(), req);
            if (agentWelcomeResultAgentResponse != null && agentWelcomeResultAgentResponse.getResult() != null) {
                return Result.newSuccess(agentWelcomeResultAgentResponse.getResult().getMessage());
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<PageResult<AiChatSessionResult>> pageQuerySessions(String ea, Integer fsUserId, AiChatSessionVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        PageResult<AiChatSessionResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        List<AiChatSessionResult> result = Lists.newArrayList();
        pageResult.setResult(result);
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        if (StringUtils.isNotBlank(vo.getSceneId())) {
            query.addFilter("scene_id", OperatorConstants.EQ, Collections.singletonList(vo.getSceneId()));
        }
        query.addFilter("biz_session_id", OperatorConstants.IS, Collections.emptyList());
        query.addFilter("sender_id", OperatorConstants.EQ, Collections.singletonList(String.valueOf(fsUserId)));
        query.addOrderByAsc("is_top", false);
        query.addOrderByAsc("last_modified_time", false);
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(AIChatSessionObj.OBJECT_API_NAME);
        InnerPage<ObjectData> objectDataInnerPage = aiChatObjManager.listByFilter(ea, paasQueryFilterArg, vo.getPageNum(), vo.getPageSize());
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            AIChatSessionObj.wrap(objectDataInnerPage.getDataList()).forEach(e -> {
                result.add(AiChatSessionResult.builder()
                        .id(e.getId())
                        .title(e.getTitle())
                        .updateTime(e.getLastModifiedTime())
                        .isTop(e.isTop())
                        .build()
                );
            });
            pageResult.setTotalCount(objectDataInnerPage.getTotalCount());
        }
        return Result.newSuccess(pageResult);
    }

    @Override
    @FilterLog
    public Result<PageResult<AiChatRecordResult>> pageQueryRecords(String ea, Integer fsUserId, AiChatRecordVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isBlank(vo.getSessionId()) && StringUtils.isBlank(vo.getBizSessionId())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        PageResult<AiChatRecordResult> pageResult = new PageResult<>();
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        List<AiChatRecordResult> result = Lists.newArrayList();
        pageResult.setResult(result);
        String sessionId = vo.getSessionId();
        if (StringUtils.isBlank(sessionId) && StringUtils.isNotBlank(vo.getBizSessionId())) {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("biz_session_id", OperatorConstants.EQ, Collections.singletonList(vo.getBizSessionId()));
            query.addOrderByAsc("create_time", false);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatSessionObj.OBJECT_API_NAME);
            AIChatSessionObj aiChatSessionObj = AIChatSessionObj.wrap(aiChatObjManager.getByFilter(ea, paasQueryFilterArg));
            if (!aiChatSessionObj.isEmpty()) {
                sessionId = aiChatSessionObj.getId();
            }
        }
        if (StringUtils.isBlank(sessionId)) {
            return Result.newSuccess(pageResult);
        }
        PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
        PaasQueryArg query = new PaasQueryArg(0, 1);
        query.addFilter("session_id", OperatorConstants.EQ, Collections.singletonList(sessionId));
        if (StringUtils.isNotBlank(vo.getNextCursor())) {
            query.addFilter("_id", OperatorConstants.GT, Collections.singletonList(vo.getNextCursor()));
        }
        query.addOrderByAsc("_id", false);
        paasQueryFilterArg.setQuery(query);
        paasQueryFilterArg.setObjectAPIName(AIChatRecordObj.OBJECT_API_NAME);
        InnerPage<ObjectData> objectDataInnerPage = aiChatObjManager.listByFilter(ea, paasQueryFilterArg, vo.getPageNum(), vo.getPageSize());
        if (objectDataInnerPage != null && CollectionUtils.isNotEmpty(objectDataInnerPage.getDataList())) {
            List<AIChatRecordObj> recordObjs = AIChatRecordObj.wrap(objectDataInnerPage.getDataList());
            recordObjs.forEach(e -> result.add(ObjectDataUtil.objectDataResultMapping(e))
            );
        }
        pageResult.setTotalCount(objectDataInnerPage.getTotalCount());
        return Result.newSuccess(pageResult);
    }

    @Override
    public Result<List<AiChatRecordResult>> querySessionRecordsByButtonApiName(String ea, Integer fsUserId, AiChatRecordVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        if (StringUtils.isBlank(vo.getButtonApiName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<AiChatRecordResult> result = Lists.newArrayList();
        String sessionId = vo.getSessionId();
        if (StringUtils.isBlank(sessionId) && StringUtils.isNotBlank(vo.getBizSessionId())) {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("biz_session_id", OperatorConstants.EQ, Collections.singletonList(vo.getBizSessionId()));
            query.addOrderByAsc("create_time", false);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatSessionObj.OBJECT_API_NAME);
            AIChatSessionObj aiChatSessionObj = AIChatSessionObj.wrap(aiChatObjManager.getByFilter(ea, paasQueryFilterArg));
            if (!aiChatSessionObj.isEmpty()) {
                sessionId = aiChatSessionObj.getId();
            }
        }
        if (StringUtils.isBlank(sessionId)) {
            return Result.newSuccess(result);
        }
        List<String> promptRecordIds = Lists.newArrayList();
        {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("session_id", OperatorConstants.EQ, Collections.singletonList(sessionId));
            query.addFilter("property", OperatorConstants.LIKE, Collections.singletonList("buttonApiName"));
            query.addOrderByAsc("_id", false);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatRecordObj.OBJECT_API_NAME);
            List<AIChatRecordObj> recordObjs = AIChatRecordObj.wrap(aiChatObjManager.listAllByFilter(ea, paasQueryFilterArg));
            if (CollectionUtils.isNotEmpty(recordObjs)) {
                recordObjs.forEach(e -> promptRecordIds.add(e.getId()));
            }
        }
        if (CollectionUtils.isNotEmpty(promptRecordIds)) {
            PaasQueryFilterArg paasQueryFilterArg = new PaasQueryFilterArg();
            PaasQueryArg query = new PaasQueryArg(0, 1);
            query.addFilter("prompt_record_id", OperatorConstants.IN, promptRecordIds);
            query.addOrderByAsc("_id", false);
            paasQueryFilterArg.setQuery(query);
            paasQueryFilterArg.setObjectAPIName(AIChatRecordObj.OBJECT_API_NAME);
            List<ObjectData> objectDatas = aiChatObjManager.listAllByFilter(ea, paasQueryFilterArg);
            if (CollectionUtils.isNotEmpty(objectDatas)) {
                List<AIChatRecordObj> recordObjs = AIChatRecordObj.wrap(objectDatas);
                recordObjs.forEach(e -> result.add(ObjectDataUtil.objectDataResultMapping(e))
                );
            }
        }
        return Result.newSuccess(result);
    }

    @Override
    public Result chatCompleteWithSession(String ea, Integer fsUserId, ChatCompleteVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        return aiChatManager.chatCompleteWithSession(ea, fsUserId, vo);
    }

    @Override
    public Result chatCompleteWithoutSession(String ea, Integer fsUserId, ChatCompleteVO vo) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        return aiChatManager.chatCompleteWithoutSession(ea, fsUserId, vo);
    }

    @Override
    public Result getChatCompleteResult(String ea, Integer fsUserId, String id) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        return aiChatManager.getChatCompleteResult(ea, fsUserId, id);
    }

    @Override
    public Result isOpenShareGPT(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea) || fsUserId == null) {
            return Result.newError(SHErrorCode.INVALID_FS_LOGIN);
        }
        return Result.newSuccess(appVersionManager.checkSpecialVersionOrders(ea, "sharegpt_app"));
    }

    @Override
    public Result promptCompletions(String ea, Integer fsUserId, PromptCompletionsArg arg) {
        return paaSPromptManager.asyncPromptCompletions(arg.getTaskId(), () -> paaSPromptManager.generatePromotional(ea, fsUserId, arg));
    }
}
