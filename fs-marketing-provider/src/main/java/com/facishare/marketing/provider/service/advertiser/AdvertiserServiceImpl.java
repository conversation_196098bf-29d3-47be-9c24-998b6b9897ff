/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service.advertiser;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.advertiser.AdBigScreenSettingDetailArg;
import com.facishare.marketing.api.arg.advertiser.AdBigScreenSettingUpdateArg;
import com.facishare.marketing.api.arg.advertiser.AdLeadDataArg;
import com.facishare.marketing.api.arg.advertiser.ExportUserMarketingGroupArg;
import com.facishare.marketing.api.result.advertiser.AdBigScreenResult;
import com.facishare.marketing.api.result.advertiser.AdBigScreenSettingDetailResult;
import com.facishare.marketing.api.result.advertiser.AdBigScreenSettingResult;
import com.facishare.marketing.api.result.advertiser.AdLeadDataResult;
import com.facishare.marketing.api.service.advertiser.AdvertiserService;
import com.facishare.marketing.common.enums.ExportAdEncryptionEnum;
import com.facishare.marketing.common.enums.advertiser.BigScreenTimeRangeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.bo.advertise.AdBigScreenSettingDetailBO;
import com.facishare.marketing.provider.dao.MarketingUserGroupDao;
import com.facishare.marketing.provider.dao.MarketingUserGroupToUserRelationDao;
import com.facishare.marketing.provider.entity.MarketingUserGroupEntity;
import com.facishare.marketing.provider.entity.advertiser.AdBigScreenSettingEntity;
import com.facishare.marketing.provider.entity.advertiser.AdLeadDataEntity;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.manager.MarketingUserGroupManager;
import com.facishare.marketing.provider.manager.PushSessionManager;
import com.facishare.marketing.provider.manager.advertiser.*;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.github.mybatis.pagination.Page;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

@Service("advertiserService")
@Slf4j
public class AdvertiserServiceImpl implements AdvertiserService {

    @Resource(name = "adBigScreenManager")
    private AdBigScreenManager adBigScreenManager;

    @Resource(name = "adAccountManager")
    private AdAccountManager adAccountManager;

    @Resource(name = "adLeadDataManager")
    private AdLeadDataManager adLeadDataManager;

    @Autowired
    private AdMarketingHandlerActionManager adMarketingHandlerActionManager;

    @Autowired
    private AdCommonManager adCommonManager;

    @Autowired
    private MarketingUserGroupDao marketingUserGroupDao;

    @Autowired
    private MarketingUserGroupToUserRelationDao marketingUserGroupToUserRelationDao;

    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;

    @Autowired
    private PushSessionManager pushSessionManager;

    @Override
    public Result<AdBigScreenResult> bigScreen(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        List<AdAccountEntity> adAccountEntityList = adAccountManager.queryAccountByEa(ea, true);
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return Result.newError(SHErrorCode.BAIDU_ACCOUNT_NOT_BIND);
        }
        AdBigScreenResult adBigScreenResult = adBigScreenManager.bigScreen(ea);
        return Result.newSuccess(adBigScreenResult);
    }

    @Override
    public Result<AdBigScreenSettingResult> getBigScreenSetting(String ea, Integer fsUserId) {
        if (StringUtils.isBlank(ea)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AdBigScreenSettingEntity entity = adBigScreenManager.getBigScreenSetting(ea);
        AdBigScreenSettingResult result = BeanUtil.copy(entity, AdBigScreenSettingResult.class);
        result.setSetting(JSONObject.parseObject(entity.getSetting(), AdBigScreenSettingDetailResult.class));
        result.setSelectableTimeList(adBigScreenManager.getSelectableTimeList());
        return Result.newSuccess(result);
    }

    @Override
    public Result<Void> updateBigScreenSetting(AdBigScreenSettingUpdateArg arg) {
        if (!arg.checkParam()) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        AdBigScreenSettingDetailArg.TimeSetting timeSetting = arg.getSetting().getTimeSetting();

        if (!timeSetting.checkParam()) {
            return Result.newError(SHErrorCode.TIME_SETTING_ERROR);
        }
        String compareTimeType = timeSetting.getCompareTimeType();

        if (!AdBigScreenManager.M_O_M.equals(compareTimeType) && !AdBigScreenManager.Y_O_Y.equals(compareTimeType) && !AdBigScreenManager.CUSTOMIZE.equals(compareTimeType)) {
            return Result.newError(SHErrorCode.COMPARE_TIME_TYPE_ERROR);
        }
        // 如果是自定义时间 时间间隔要一致
        if (timeSetting.getTimeRange().equals(BigScreenTimeRangeEnum.CUSTOMIZE.getName())) {
            long day = DateUtil.getDaysBetween(timeSetting.getBeginTime().getTime(), timeSetting.getEndTime().getTime());
            long day2 = DateUtil.getDaysBetween(timeSetting.getCompareBeginTime().getTime(), timeSetting.getCompareEndTime().getTime());
            if (day > 365 || day2 > 365) {
                return Result.newError(SHErrorCode.TIME_DIFF_TOO_LONG);
            }
            if (day != day2) {
                return Result.newError(SHErrorCode.CUSTOMIZE_TIME_BETWEEN_DAY_ERROR);
            }
        }

        if (arg.getTitle().length() > 15) {
            return Result.newError(SHErrorCode.TITLE_TOO_LONG);
        }

        // 默认的设置
        AdBigScreenSettingEntity defaultBigScreenSetting = adBigScreenManager.getDefaultBigScreenSetting(arg.getEa());
        AdBigScreenSettingDetailBO defaultSettingDetailBO = JSONObject.parseObject(defaultBigScreenSetting.getSetting(), AdBigScreenSettingDetailBO.class);
        // 要配置的设置
        AdBigScreenSettingDetailBO adBigScreenSettingDetailBO = JSONObject.parseObject(JSON.toJSONString(arg.getSetting()), AdBigScreenSettingDetailBO.class);

        // 判断获客成本设置
        AdBigScreenSettingDetailBO.Setting setting = adBigScreenSettingDetailBO.getCustomerAcquisitionCost();
        if (setting == null || StringUtils.isBlank(setting.getName()) || CollectionUtils.isEmpty(setting.getFieldList())) {
            return Result.newError(SHErrorCode.CUSTOMER_ACQUISITION_COST_ARG_ERROR);
        }
        // 判断字段的名字是否为空
        if (!checkFieldName(setting.getFieldList())) {
            return Result.newError(SHErrorCode.FIELD_NAME_IS_NULL);
        }
        // 判断是否修改了不可编辑的字段
        AdBigScreenSettingDetailBO.Setting defaultSetting = defaultSettingDetailBO.getCustomerAcquisitionCost();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }

        // 广告投放效果趋势
        setting = adBigScreenSettingDetailBO.getLaunchEffectTrend();
        if (setting == null || StringUtils.isBlank(setting.getName()) || CollectionUtils.isEmpty(setting.getFieldList())) {
            return Result.newError(SHErrorCode.LAUNCH_EFFECT_TREND_ARG_ERROR);
        }
        // 判断字段的名字是否为空
        if (!checkFieldName(setting.getFieldList())) {
            return Result.newError(SHErrorCode.FIELD_NAME_IS_NULL);
        }
        // 判断是否修改了不可编辑的字段
        defaultSetting = defaultSettingDetailBO.getLaunchEffectTrend();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }

        // 广告获客转化漏斗
        setting = adBigScreenSettingDetailBO.getAcquisitionCustomerConvertFunnel();
        if (setting == null || StringUtils.isBlank(setting.getName()) || CollectionUtils.isEmpty(setting.getFieldList())) {
            return Result.newError(SHErrorCode.LAUNCH_EFFECT_TREND_ARG_ERROR);
        }
        // 判断字段的名字是否为空
        if (!checkFieldName(setting.getFieldList())) {
            return Result.newError(SHErrorCode.FIELD_NAME_IS_NULL);
        }
        // 判断是否修改了不可编辑的字段
        defaultSetting = defaultSettingDetailBO.getAcquisitionCustomerConvertFunnel();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }
        for (AdBigScreenSettingDetailBO.Field field : setting.getFieldList()) {
            Result<Void> checkResult = checkSelectFieldValueList(field);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }
        // 广告账户获客对比
        setting = adBigScreenSettingDetailBO.getAccountAcquisitionCustomerCompare();
        if (setting == null || StringUtils.isBlank(setting.getName()) || CollectionUtils.isEmpty(setting.getFieldList())) {
            return Result.newError(SHErrorCode.ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR);
        }
        // 判断字段的名字是否为空
        if (!checkFieldName(setting.getFieldList())) {
            return Result.newError(SHErrorCode.FIELD_NAME_IS_NULL);
        }
        // 判断字段id是否为空
        if (setting.getFieldList().stream().anyMatch(e -> StringUtils.isBlank(e.getId()))) {
            return Result.newError(SHErrorCode.FIELD_ID_IS_NULL);
        }

        // 广告账户投入产出分析
        setting = adBigScreenSettingDetailBO.getAccountInputOutputAnalysis();
        if (setting == null || StringUtils.isBlank(setting.getName()) || CollectionUtils.isEmpty(setting.getFieldList())) {
            return Result.newError(SHErrorCode.ACCOUNT_ACQUISITION_CUSTOMER_COMPARE_ARG_ERROR);
        }
        for (AdBigScreenSettingDetailBO.Field field : setting.getFieldList()) {
            Result<Void> checkResult = checkSelectFieldValueList(field);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }
        // 判断字段的名是否为空
        if (!checkFieldName(setting.getFieldList())) {
            return Result.newError(SHErrorCode.FIELD_NAME_IS_NULL);
        }
        // 判断是否修改了不可编辑的字段
        defaultSetting = defaultSettingDetailBO.getAccountInputOutputAnalysis();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }

        // 判断模块位置信息
        if (CollectionUtils.isEmpty(arg.getSetting().getModulePositions())) {
            return Result.newError(SHErrorCode.MODULE_POSITION_NOT_FOUND);
        }

        setting = adBigScreenSettingDetailBO.getOverView();
        if (setting == null) {
            return Result.newError(SHErrorCode.OVER_VIEW_SETTING_NOT_FOUND);
        }
        // 判断是否修改了不可编辑的字段
        defaultSetting = defaultSettingDetailBO.getOverView();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }

        setting = adBigScreenSettingDetailBO.getLeadsArealDistributions();
        if (setting == null) {
            return Result.newError(SHErrorCode.AREAL_DISTRIBUTE_SETTING_NOT_FOUND);
        }
        // 判断是否修改了不可编辑的字段
        defaultSetting = defaultSettingDetailBO.getLeadsArealDistributions();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }
        for (AdBigScreenSettingDetailBO.Field field : setting.getFieldList()) {
            Result<Void> checkResult = checkSelectFieldValueList(field);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }
        }

        setting = adBigScreenSettingDetailBO.getConvertPeriod();
        if (setting == null) {
            return Result.newError(SHErrorCode.CONVERT_PERIOD_SETTING_NOT_FOUND);
        }
        // 判断是否修改了不可编辑的字段
        defaultSetting = defaultSettingDetailBO.getConvertPeriod();
        if (isChangeCannotEditField(defaultSetting, setting)) {
            return Result.newError(SHErrorCode.FORBID_EDIT_FIELD);
        }

        AdBigScreenSettingEntity adBigScreenSettingEntity = BeanUtil.copy(arg, AdBigScreenSettingEntity.class);
        // 初始化字段是否能编辑
        initFieldCanEdit(defaultSettingDetailBO, adBigScreenSettingDetailBO);
        adBigScreenSettingEntity.setSetting(JSON.toJSONString(adBigScreenSettingDetailBO));
        adBigScreenSettingEntity.setTimeRange(BigScreenTimeRangeEnum.THIS_SEASON.getName());
        adBigScreenSettingEntity.setRelativeTimeRange(BigScreenTimeRangeEnum.LAST_SEASON.getName());
        adBigScreenManager.updateSetting(adBigScreenSettingEntity);

        return Result.newSuccess();
    }

    private Result<Void> checkSelectFieldValueList(AdBigScreenSettingDetailBO.Field field) {
        String fieldType = field.getType();
        if (!AdBigScreenManager.SELECT_MANY.equals(fieldType) && !AdBigScreenManager.SELECT_ONE.equals(fieldType)) {
            return Result.newSuccess();
        }
        if (CollectionUtils.isEmpty(field.getFieldValueList())) {
            return Result.newError(SHErrorCode.FIELD_VALUE_LIST_NOT_FOUND);
        }
        long selectedCount = field.getFieldValueList().stream().filter(e -> BooleanUtils.isTrue(e.getSelected())).count();
        if (selectedCount <= 0) {
            return Result.newError(SHErrorCode.SELECT_FIELD_VALUE_IS_EMPTY);
        }
        if (AdBigScreenManager.SELECT_ONE.equals(fieldType) && selectedCount > 1) {
            return Result.newError(SHErrorCode.SELECT_ONE_FIELD_VALUE_ERROR);
        }
        return Result.newSuccess();
    }

    @Override
    public Result<List<AdLeadDataResult>> getAdLeadData(AdLeadDataArg adLeadDataArg) {

        if (adLeadDataArg.getPageNum() == null || adLeadDataArg.getPageSize() == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        List<AdLeadDataResult> resultList = Lists.newArrayList();
        String ea = adLeadDataArg.getEa();

        AdBigScreenSettingEntity entity = adBigScreenManager.getBigScreenSetting(ea);
        AdBigScreenSettingDetailBO settingDetailBO = JSONObject.parseObject(entity.getSetting(), AdBigScreenSettingDetailBO.class);
        AdBigScreenManager.TimePeriod timePeriod = AdBigScreenManager.buildTimePeriod(settingDetailBO.getTimeSetting());
        String province = getLeadAreaDistributionProvince(settingDetailBO);
        if (StringUtils.isNotBlank(province)) {
            province = province.replace("省", "");
        }
        Page<AdLeadDataEntity> page = new Page<>(adLeadDataArg.getPageNum(), adLeadDataArg.getPageSize());
        List<AdLeadDataEntity> adLeadDataEntityList = adLeadDataManager.getByLeadCreateTimePage(ea, timePeriod.getBeginTime(), timePeriod.getEndTime(), province, page);

        if (CollectionUtils.isEmpty(adLeadDataEntityList)) {
            return Result.newSuccess(resultList);
        }
        for (AdLeadDataEntity adLeadDataEntity : adLeadDataEntityList) {
            AdLeadDataResult ad = new AdLeadDataResult();
            ad.setMobile(ReplaceUtil.phoneNumberStrSensitive(adLeadDataEntity.getMobile()));
            ad.setProvince(adLeadDataEntity.getMobileProvince());
            ad.setCity(adLeadDataEntity.getMobileCity());
            resultList.add(ad);
        }
        return Result.newSuccess(resultList);
    }

    private String getLeadAreaDistributionProvince(AdBigScreenSettingDetailBO settingDetailBO) {
        for (AdBigScreenSettingDetailBO.Field field : settingDetailBO.getLeadsArealDistributions().getFieldList()) {
            List<AdBigScreenSettingDetailBO.FieldValue> fieldValueList = field.getFieldValueList();
            return fieldValueList.stream().filter(e -> !"china".equals(e.getValue()) && BooleanUtils.isTrue(e.getSelected()))
                    .findFirst().map(AdBigScreenSettingDetailBO.FieldValue::getLabel).orElse(null);
        }
        return null;
    }

    // 判断是否修改了不可编辑的字段
    private boolean isChangeCannotEditField(AdBigScreenSettingDetailBO.Setting defaultSetting, AdBigScreenSettingDetailBO.Setting validateSetting) {
        Map<String, AdBigScreenSettingDetailBO.Field> cannotEditFieldMap = defaultSetting.getFieldList().stream().filter(e -> !e.isCanEdit())
                .collect(Collectors.toMap(AdBigScreenSettingDetailBO.Field::getName, e -> e, (v1, v2) -> v1));
        return validateSetting.getFieldList().stream().anyMatch(e ->
                cannotEditFieldMap.containsKey(e.getName()) && e.isHidden() != cannotEditFieldMap.get(e.getName()).isHidden());
    }

    // 填充canEdit字段
    private static void initFieldCanEdit(AdBigScreenSettingDetailBO defaultSettingDetailBO, AdBigScreenSettingDetailBO adBigScreenSettingDetailBO) {
        fillFieldCanEdit(defaultSettingDetailBO.getCustomerAcquisitionCost().getFieldList(), adBigScreenSettingDetailBO.getCustomerAcquisitionCost().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getLaunchEffectTrend().getFieldList(), adBigScreenSettingDetailBO.getLaunchEffectTrend().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getAcquisitionCustomerConvertFunnel().getFieldList(), adBigScreenSettingDetailBO.getAcquisitionCustomerConvertFunnel().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getAccountAcquisitionCustomerCompare().getFieldList(), adBigScreenSettingDetailBO.getAccountAcquisitionCustomerCompare().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getAccountInputOutputAnalysis().getFieldList(), adBigScreenSettingDetailBO.getAccountInputOutputAnalysis().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getOverView().getFieldList(), adBigScreenSettingDetailBO.getOverView().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getLeadsArealDistributions().getFieldList(), adBigScreenSettingDetailBO.getLeadsArealDistributions().getFieldList());
        fillFieldCanEdit(defaultSettingDetailBO.getConvertPeriod().getFieldList(), adBigScreenSettingDetailBO.getConvertPeriod().getFieldList());

    }

    private static void fillFieldCanEdit(List<AdBigScreenSettingDetailBO.Field> defaultFieldList, List<AdBigScreenSettingDetailBO.Field> settingFieldList) {
        Map<String, AdBigScreenSettingDetailBO.Field> canEditFieldMap = defaultFieldList.stream().filter(AdBigScreenSettingDetailBO.Field::isCanEdit)
                .collect(Collectors.toMap(AdBigScreenSettingDetailBO.Field::getName, e -> e, (v1, v2) -> v1));
        settingFieldList.forEach(e -> e.setCanEdit(canEditFieldMap.containsKey(e.getName())));
    }

    private boolean checkFieldName(List<AdBigScreenSettingDetailBO.Field> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            return false;
        }
        return fieldList.stream().allMatch(e -> StringUtils.isNotBlank(e.getName()));
    }

    @Override
    public Result<Void> refreshPrototypeRoomAccountData() {
        TraceContext context = TraceContext.get();
        context.setTraceId(UUIDUtil.getUUID());
        ThreadPoolUtils.execute(() -> {
            refreshPrototypeRoomAccountDataInner();
        }, ThreadPoolUtils.ThreadPoolTypeEnums.AD);
        return Result.newSuccess();
    }

    public void refreshPrototypeRoomAccountDataInner() {
        List<AdAccountEntity> adAccountEntityList = adAccountManager.getAllAdPrototypeRoomAccount();
        if (CollectionUtils.isEmpty(adAccountEntityList)) {
            return;
        }
        Map<String, List<AdAccountEntity>> eaToAccountList = adAccountEntityList.stream().collect(Collectors.groupingBy(AdAccountEntity::getEa));
        // 所有账号每日消费数 1000~5000
        Random random = new Random();
        for (Map.Entry<String, List<AdAccountEntity>> entry : eaToAccountList.entrySet()) {
            String ea = entry.getKey();
            log.info("开始刷 ea: {} 样板间账号", ea);
            int cost = random.nextInt(4000);
            List<AdAccountEntity> accountEntities = entry.getValue();
            int avgCost = cost / accountEntities.size();
            // 刷新每个样板间账号的展点消
            for (AdAccountEntity adAccountEntity : accountEntities) {
                avgCost += random.nextInt(1000);
                adMarketingHandlerActionManager.getAdMarketingActionManager(AdSourceEnum.getValueBySource(adAccountEntity.getSource())).refreshPrototypeRoomAccountData(ea, avgCost);
            }
            // 随机创建一定数量的线索
            adCommonManager.createAdPrototypeRoomLeadData(ea);
        }
    }

    @Override
    public Result<Void> exportUserMarketingGroup(ExportUserMarketingGroupArg arg) {
        if (StringUtils.isBlank(arg.getMarketingUserGroupId()) || StringUtils.isBlank(arg.getEncryptionType())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }

        ExportAdEncryptionEnum exportAdEncryptionEnum = ExportAdEncryptionEnum.getByType(arg.getEncryptionType());
        if (exportAdEncryptionEnum == null) {
            return Result.newError(SHErrorCode.AD_ENCRYPTION_TYE_NOT_EXIST);
        }

        MarketingUserGroupEntity marketingUserGroupEntity = marketingUserGroupDao.getById(arg.getMarketingUserGroupId());
        if (marketingUserGroupEntity == null) {
            return Result.newError(SHErrorCode.MARKETING_USER_GROUP_NOT_EXIST);
        }
        String ea = arg.getEa();
        List<String> userMarketingIdList = marketingUserGroupToUserRelationDao.pageListMarketingUserIdsV2(ea, arg.getMarketingUserGroupId(), null, 1);
        if (CollectionUtils.isEmpty(userMarketingIdList)) {
            return Result.newError(SHErrorCode.MARKETING_USER_GROUP_IS_EMPTY);
        }
        ThreadPoolUtils.execute(() -> pushUserMarketingPhoneToFileAssistant(arg, exportAdEncryptionEnum), ThreadPoolUtils.ThreadPoolTypeEnums.AD);
        return Result.newSuccess();
    }

    private void pushUserMarketingPhoneToFileAssistant(ExportUserMarketingGroupArg arg, ExportAdEncryptionEnum exportAdEncryptionEnum) {
        String lastUserMarketingId = null;
        String ea = arg.getEa();
        int limit = 2000;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String lineBreak = "\n";
        int totalCount = 0;
        boolean pushToFileAssistant = false;
        try (Writer writer = new BufferedWriter(new OutputStreamWriter(byteArrayOutputStream, StandardCharsets.UTF_8))) {
            while (totalCount < MarketingUserGroupManager.MAX_MAX_MARKETING_USER_IN_GROUP) {
                // 分页查询营销用户id
                List<String> userMarketingIdList = marketingUserGroupToUserRelationDao.pageListMarketingUserIdsV2(ea, arg.getMarketingUserGroupId(), lastUserMarketingId, limit);
                if (CollectionUtils.isEmpty(userMarketingIdList)) {
                    break;
                }
                totalCount += userMarketingIdList.size();
                lastUserMarketingId = userMarketingIdList.get(userMarketingIdList.size() - 1);
                // 获取营销用户的号码
                Map<String, String> userMarketingIdToPhoneMap = userMarketingAccountManager.getMarketingUserPhonesV2(arg.getEa(), userMarketingIdList);
                if (MapUtils.isEmpty(userMarketingIdToPhoneMap)) {
                    log.info("exportUserMarketingGroup 营销用户号码为空, arg: {}, userMarketingIdList: {}", arg, userMarketingIdList);
                    continue;
                }
                //对手机号进行加密处理
                List<String> phoneList = Lists.newArrayList(userMarketingIdToPhoneMap.values());
                List<String> encryptionPhoneList = phoneList.stream().map(e -> encryptPhone(arg.getPhonePrefix(), e, exportAdEncryptionEnum)).collect(Collectors.toList());
                for (String phone : encryptionPhoneList) {
                    phone += lineBreak;
                    // 一行一行的写
                    writer.write(phone);
                    // 更新标识，有号码就得发消息
                    pushToFileAssistant = true;
                }
            }
            writer.flush();
            byteArrayOutputStream.flush();
            if (pushToFileAssistant) {
                String fileName = I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADVERTISERSERVICEIMPL_462) + DateUtil.dateMillis2String(System.currentTimeMillis(), "yyyyMMddHHmm") + ".txt";
                pushSessionManager.pushFileToFileAssistant(byteArrayOutputStream, fileName, arg.getEa(), arg.getFsUserId(), "txt");
            }
        } catch (IOException e) {
            log.error("pushUserMarketingPhoneToFileAssistant error, arg: {}", arg, e);
        }
    }

    private String encryptPhone(String prefix, String phone, ExportAdEncryptionEnum encryptionTypeEnum) {
        if (StringUtils.isNotBlank(prefix)) {
            phone = prefix + phone;
        }
        if (encryptionTypeEnum == ExportAdEncryptionEnum.MD5) {
            return MD5Util.md5String(phone);
        }
        return Sha256Util.getSHA256Str(phone);
    }

}