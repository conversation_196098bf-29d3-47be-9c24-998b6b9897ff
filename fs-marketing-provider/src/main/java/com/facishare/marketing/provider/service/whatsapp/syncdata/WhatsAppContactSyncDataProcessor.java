package com.facishare.marketing.provider.service.whatsapp.syncdata;

import com.facishare.marketing.api.arg.whatsapp.SyncDataArg;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class WhatsAppContactSyncDataProcessor extends AbstractWhatsAppSyncDataProcessor {
    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    @Override
    public String getObjectApiName() {
        return CrmObjectApiNameEnum.WECHAT_WORK_EXTERNAL_USER_OBJ.getName();
    }

    @Override
    protected String getIdKeyFieldName() {
        return "phone";
    }

    @Override
    protected ObjectData convert2CrmObjectData(ObjectData sourceData) {
        ObjectData data = super.convert2CrmObjectData(sourceData);
        data.put("external_user_id", sourceData.getId());
        String phone = sourceData.getString("phoneNumber");
        phone = StringUtils.isBlank(phone) ? sourceData.getId() : phone;
        if(StringUtils.isNotBlank(phone)) {
            phone = phone.split("@")[0];
            data.put("phone", phone);
        }
        String name = sourceData.getString("name");
        if(StringUtils.isBlank(name)) {
            name = sourceData.getString("pushname");
        }
        name = StringUtils.isBlank(name) ? phone : name;
        name = StringUtils.isBlank(name) ? sourceData.getId() : name;
        data.put("name", name);
        data.put("type", "3");
        data.put("sex", "unknown");
        return data;
    }

    @Override
    protected void afterCreateObject(SyncDataArg syncDataArg, List<ObjectData> objectDataList) {
        if(CollectionUtils.isEmpty(objectDataList)) {
            return;
        }
        List<ObjectData> friendRecords = Lists.newArrayList();
        String wechatEmployeeId = getWechatEmployeeId(syncDataArg.getEa(), syncDataArg.getWhatsAppUserId());
        objectDataList.forEach(data -> {
            AssociationArg associationArg = new AssociationArg();
            associationArg.setPhone(data.getString("phone"));
            associationArg.setAssociationId(data.getId());
            associationArg.setUserName(data.getName());
            associationArg.setType(ChannelEnum.CRM_WX_WORK_EXTERNAL_USER.getType());
            userMarketingAccountAssociationManager.associate(associationArg);
            ObjectData objectData = new ObjectData();
            objectData.put("_id", UUIDUtil.getUUID());
            objectData.setTenantId(data.getTenantId().intValue());
            objectData.put("object_describe_api_name", CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName());
            objectData.put("record_type", WHATSAPP_RECORD_TYPE);
            objectData.put("friend_status", "0");
            objectData.put("phone", data.getString("phone"));
            objectData.setOwner(syncDataArg.getFsUserId());
            objectData.setCreateBy(syncDataArg.getFsUserId());
            objectData.put("external_user_id", data.getId());
            objectData.put("add_time", data.getLong("phoneNumberCreatedAt") == null ? System.currentTimeMillis() : data.getLong("phoneNumberCreatedAt") * 1000);
            objectData.put("wechat_employee_id", wechatEmployeeId);
            objectData.put("user_id", String.valueOf(syncDataArg.getFsUserId()));
            friendRecords.add(objectData);
        });

        bulkCreateData(syncDataArg.getEa(), CrmObjectApiNameEnum.WECHAT_FRIENDS_RECORD_OBJ.getName(), friendRecords);
    }
}
