/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.service;

import com.facishare.marketing.api.arg.spreadTask.GetNoticeByUserArg;
import com.facishare.marketing.api.result.spreadTask.GetNoticeByUserResult;
import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.*;
import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.mankeep.api.outService.service.OutFileService;
import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.PromotionContentData;
import com.facishare.marketing.api.result.NoticeDetailResult;
import com.facishare.marketing.api.result.NoticeDetailResult.ContentDetail;
import com.facishare.marketing.api.result.NoticeResult;
import com.facishare.marketing.api.result.ObjectUserMarketingStatisticsResult;
import com.facishare.marketing.api.result.dingding.DingSendByTemplateMessageResult;
import com.facishare.marketing.api.service.DingMiniAppStaffService;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.api.service.StatisticService;
import com.facishare.marketing.api.vo.DingDingMarketingSpreadVO;
import com.facishare.marketing.api.vo.ExtraOutsideChannelInfoVO;
import com.facishare.marketing.api.vo.NoticeStatusVO;
import com.facishare.marketing.common.enums.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.kis.SpreadTaskStatusEnum;
import com.facishare.marketing.common.enums.qywx.QywxSpreadTypeEnum;
import com.facishare.marketing.common.model.RedisKISApplyInfoEntity;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.typehandlers.value.DataCount;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils.ThreadPoolTypeEnums;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceDAO;
import com.facishare.marketing.provider.dao.emailMaterial.EmailMaterialDao;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeStatisticDAO;
import com.facishare.marketing.provider.dao.kis.MarketingActivityStatisticDao;
import com.facishare.marketing.provider.dao.kis.SpreadTaskDAO;
import com.facishare.marketing.provider.dao.photoLibrary.PhotoLibraryDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO;
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao;
import com.facishare.marketing.provider.dao.yxzs.YxzsAllSpreadWxTemplateNoticeSettingDAO;
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.emailMaterial.EmailMaterialEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.kis.MarketingActivityStatisticEntity;
import com.facishare.marketing.provider.entity.kis.SpreadTaskEntity;
import com.facishare.marketing.provider.entity.marketingAssistant.YxzsAllSpreadWxTemplateNoticeSettingEntity;
import com.facishare.marketing.provider.entity.marketingactivity.MarketingActivityExternalConfigEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity;
import com.facishare.marketing.provider.entity.user.UserRelationEntity;
import com.facishare.marketing.provider.manager.*;
import com.facishare.marketing.provider.manager.FsAddressBookManager.FSEmployeeMsg;
import com.facishare.marketing.provider.manager.ding.DingManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager;
import com.facishare.marketing.provider.manager.kis.SpreadTaskManager;
import com.facishare.marketing.provider.manager.marketingAssistant.YxzsNoticeSendManager;
import com.facishare.marketing.provider.manager.marketingactivity.MarketingActivityAuditManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.manager.qywx.QywxUserManager;
import com.facishare.marketing.provider.manager.user.UserRelationManager;
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager;
import com.facishare.marketing.provider.mq.sender.DingDingMarketingSpreadSender;
import com.facishare.marketing.provider.mq.sender.NoticeStatusSender;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.MarketingActivityRemoteManager;
import com.facishare.marketing.provider.remote.MarketingCrmManager;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryMarketingActivityArg;
import com.facishare.marketing.provider.remote.rest.QyweixinAccountBindManager;
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager;
import com.facishare.marketing.provider.util.Constant;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.facishare.wechat.dubborestouterapi.data.EaBindInfoData;
import com.facishare.wechat.dubborestouterapi.service.union.EaBindInfoRestService;
import com.fxiaoke.api.CRMNotifyService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.model.*;
import com.fxiaoke.model.crmNotify.AddRemindRecordArg;
import com.fxiaoke.paasauthrestapi.service.PaasTenantGroupService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;

/**
 * Created By tianh, 2018/7/2.
 **/
@Service("noticeService")
@Slf4j
public class NoticeServiceImpl implements NoticeService {
    @Autowired
    private NoticeDAO noticeDAO;
    @Autowired
    private AuthManager authManager;
    @Autowired
    private FsBindManager fsBindManager;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;
    @Autowired
    private PhotoSelectorDAO photoSelectorDAO;
    @Autowired
    private PhotoLibraryDAO photoLibraryDAO;
    @Autowired
    private EmailMaterialDao emailMaterialDao;
    @Value("${host}")
    private String host;
    @Autowired
    private EnterpriseStatisticManager enterpriseStatisticManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FsMessageManager fsMessageManager;
    @Autowired
    private FsAddressBookManager fsAddressBookManager;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private NoticeStatusSender noticeStatusSender;
    @Autowired
    private MarketingActivityExternalConfigDao marketingActivityExternalConfigDao;
    @Autowired
    private MarketingActivityStatisticDao marketingActivityStatisticDao;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private MarketingCrmManager marketingCrmManager;
    @Autowired
    private CustomizeFormClueManager customizeFormClueManager;
    @Autowired
    private OutFileService outFileService;
    @Autowired
    private QyweixinAccountBindManager qyweixinAccountBindManager;
    @Autowired
    private GroupSendMessageManager groupSendMessageManager;
    @Autowired
    private MarketingActivityManager marketingActivityManager;
    @Autowired
    private SpreadTaskManager spreadTaskManager;
    @Autowired
    private SpreadTaskDAO spreadTaskDAO;
    @Autowired
    private MarketingActivityEmployeeStatisticDAO marketingActivityEmployeeStatisticDao;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private MetadataControllerServiceManager metadataControllerServiceManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingEventManager marketingEventManager;
    @Autowired
    private ConferenceDAO conferenceDAO;
    @Autowired
    private UserRoleManager userRoleManager;
    @Autowired
    private CRMNotifyService crmNotifyService;
    @Autowired
    private SaveClueFailNoticeConfigDAO saveClueFailNoticeConfigDAO;

    @Autowired
    private PaasTenantGroupService paasTenantGroupService;
    @Autowired
    private HttpManager httpManager;

    @Autowired
    private DingMiniAppStaffService dingMiniAppStaffService;

    @Autowired
    private MarketingActivityRemoteManager marketingActivityRemoteManager;
    @Autowired
    private AppVersionManager appVersionManager;
    @Autowired
    private CrmPaasOrgDataManager crmPaasOrgDataManager;

    @ReloadableProperty("enterprise.spread.imagetext.power")
    private Boolean enterpriseSpreadImagetextPower;

    @Value("${marketing_appid}")
    private String appId;
    @Value("${partner.appid}")
    private String partnerAppId;

    @Value("${partner.notice.template}")
    private String partnerNoticeTemplate;

    @Value("${qywx.crm.appid}")
    private String qywxCrmAppid;

    @ReloadableProperty("open.host")
    private String openHost;

    @ReloadableProperty("imagetext.cover.default")
    private String imagetextDefaultCoverApath;

    @ReloadableProperty("default_conference_cover")
    private String defaultConferenceCover;

    @Autowired
    private NoticeManager noticeManager;

    @Autowired
    private QywxMiniappConfigDAO qywxMiniappConfigDAO;

    @Autowired
    private QywxUserManager qywxUserManager;

    @Autowired
    private WechatAccountManager wechatAccountManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private CustomizeFormDataUserDAO customizeFormDataUserDAO;

    @Autowired
    private PublicEmployeeService publicEmployeeService;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EaBindInfoRestService eaBindInfoRestService;
    @Autowired
    private UserPrivilegeRestService userPrivilegeRestService;
    @Autowired
    private MarketingNoticeSettingDAO marketingNoticeSettingDAO;
    @Autowired
    private OutLinkDAO outLinkDAO;
    @Autowired
    private DingDingMarketingSpreadSender dingDingMarketingSpreadSender;
    @Autowired
    private RedisManager redisManager;

    private Gson gson = new Gson();
    @Autowired
    private DingAuthService dingAuthService;
    //发送推广通知的莫办
    @ReloadableProperty("dingding.notice.template")
    private String dingdingNoticeTemplate;
    //发送推广通知的莫办
    @ReloadableProperty("ks.ding.ea")
    private String ksDingEa;

    //发送推广通知的莫办
    @ReloadableProperty("partner.partition.size")
    private int partitionSize;

    @Autowired
    private StatisticService statisticService;
    @Autowired
    private MarketingActivityAuditManager marketingActivityAuditManager;
    @Autowired
    private QywxManager qywxManager;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private SettingManager settingManager;

    @Autowired
    private MemberMarketingManager memberMarketingManager;

    @Autowired
    private YxzsAllSpreadWxTemplateNoticeSettingDAO yxzsAllSpreadWxTemplateNoticeSettingDAO;
    @Autowired
    private YxzsNoticeSendManager yxzsNoticeSendManager;
    @Autowired
    private EaWechatAccountBindDao eaWechatAccountBindDao;
    @Value("${enterprise.environment}")
    private String enterpriseEnvironment;

    @Autowired
    private UserRelationManager userRelationManager;
    @Override
    public Result<String> sendNotice(String ea, Integer fsUserId, NoticeSendArg vo) {
        NoticeEntity noticeEntity = doSaveNoticeEntity(ea, fsUserId, vo);

        // 新增推广次数
        enterpriseStatisticManager.incrementSpreadCount(ea, new Date());
        if (!Strings.isNullOrEmpty(noticeEntity.getContent())) {
            Integer objectType = NoticeContentTypeEnum.fromType(noticeEntity.getContentType()).toObjectType();
            enterpriseStatisticManager.insertEnterpriseObjectDataIgnore(ea, objectType, noticeEntity.getContent());
        }
        if (noticeEntity.getType() != null && noticeEntity.getType() == SendNoticeTypeEnum.MEMBER_NOTICE.getType()) {
            // 如果是会员营销全部走定时任务，避免查询paas超时导致接口报错
            return Result.newSuccess(noticeEntity.getId());
        }

        NoticeSendArg.NoticeVisibilityVO visibilityVO = gson.fromJson(noticeEntity.getSendScope(), NoticeSendArg.NoticeVisibilityVO.class);
        //立即发送
        if (vo.getSendType() == NoticeSendTypeEnum.NORMAL.getType() && !marketingActivityAuditManager.isNeedAudit(ea)) {
            if (StringUtils.isNotBlank(ksDingEa) && Arrays.asList(ksDingEa.split(",")).contains(ea)) {
                List<Integer> userIds = getCommonUserIdList(ea,visibilityVO);
                List<String> userIdList = Lists.newArrayList();
                userIds.forEach(userId ->{
                    userIdList.add(String.valueOf(userId));
                });
                if(CollectionUtils.isNotEmpty(userIdList)){
                    sendToDingdingSpreadSendMq(noticeEntity.getId(), vo.getMarketingActivityId(), vo.getMarketingEventId(),userIdList);
                }
                spreadTaskManager.createSpreadTask(ea, vo.getMarketingActivityId(), userIds);
                return Result.newSuccess(noticeEntity.getId());
            }
            if (2==vo.getAddressBookType()) {
                List<String> userIdList = dingManager.getDingUserIdsByDepartmentList(vo.getNoticeVisibilityVO().getOutUserIds(),vo.getNoticeVisibilityVO().getDepartmentIds(),noticeEntity.getFsEa());
                if(CollectionUtils.isNotEmpty(userIdList)){
                    sendToDingding(noticeEntity.getId(), vo.getMarketingActivityId(), vo,userIdList);
                    spreadTaskManager.createSpreadTaskForDingding(vo, ea, fsUserId, vo.getMarketingActivityId(), userIdList);
                }
            } else {
                List<Integer> userIdList = getCommonUserIdList(ea,visibilityVO);
                boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
                List<Integer> accesibleUserIds = Lists.newArrayList();
                if (isOpen) {
                    List<String> fsUserIds = CollectionUtils.isNotEmpty(vo.getNoticeVisibilityVO().getUserIds()) ? vo.getNoticeVisibilityVO().getUserIds().stream().map(String::valueOf).collect(Collectors.toList()) : null;
                    List<Integer> departmentIds = vo.getNoticeVisibilityVO().getDepartmentIds();
                    List<String> roleCodes = CollectionUtils.isEmpty(vo.getNoticeVisibilityVO().getRoles()) ? null : vo.getNoticeVisibilityVO().getRoles().stream().map(NoticeSendArg.roleItem::getRoleCode).collect(Collectors.toList());
                    List<String> userGroupIds = CollectionUtils.isEmpty(vo.getNoticeVisibilityVO().getUserGroups()) ? null : vo.getNoticeVisibilityVO().getUserGroups().stream().map(NoticeSendArg.UserGroup::getUserGroupId).collect(Collectors.toList());
                    accesibleUserIds.addAll(qywxManager.handleEmployeeUserIdWithOpenDataPermission(ea, fsUserId, fsUserIds, departmentIds, roleCodes, userGroupIds,false));
                    userIdList = new ArrayList<>(CollectionUtils.intersection(userIdList, accesibleUserIds));
                }
                boolean isMemberMarketing = noticeEntity.getType() != null && noticeEntity.getType() == SendNoticeTypeEnum.MEMBER_NOTICE.getType();
                // 会员营销不需要发消息
                if (!isMemberMarketing) {
                    if (BooleanUtils.isTrue(enterpriseSpreadImagetextPower)) {
                        sendImageTextNotice(noticeEntity.getId(), vo.getMarketingActivityId(),vo.getMarketingEventId(), userIdList);
                    } else {
                        sendTextNotice(noticeEntity.getId(), vo.getMarketingActivityId(),vo.getMarketingEventId(), userIdList);
                    }
                }
                //创建推广任务
                spreadTaskManager.createSpreadTask(ea, vo.getMarketingActivityId(), userIdList);
            }

        }
        return Result.newSuccess(noticeEntity.getId());
    }

    @Override
    public Result<Integer> sendNoticeCount(String ea, Integer fsUserId, NoticeSendArg vo) {
        Integer count = 0;
        if (2 == vo.getAddressBookType()) {
            List<String> userIdList = dingManager.getDingUserIdsByDepartmentList(vo.getNoticeVisibilityVO().getOutUserIds(), vo.getNoticeVisibilityVO().getDepartmentIds(), ea);
            count = userIdList.size();
        } else {
            boolean isOpen = dataPermissionManager.getDataPermissionSetting(ea);
            List<Integer> accesibleUserIds = Lists.newArrayList();
            if (isOpen) {
                List<Integer> userIds = vo.getNoticeVisibilityVO().getUserIds();
                List<Integer> departmentIds = vo.getNoticeVisibilityVO().getDepartmentIds();
                accesibleUserIds.addAll(qywxManager.handleEmployeeUserIdWithOpenDataPermission(ea, fsUserId, CollectionUtils.isNotEmpty(userIds) ? userIds.stream().map(String::valueOf).collect(Collectors.toList()) : null, departmentIds));
            }
            NoticeSendArg.NoticeVisibilityVO visibilityVO = vo.getNoticeVisibilityVO();
            List<Integer> userIds = getCommonUserIdList(ea, visibilityVO);
            if (isOpen) {
                userIds = Lists.newArrayList(CollectionUtils.intersection(userIds, accesibleUserIds));
            }
            List<String> userIdList = Lists.newArrayList();
            userIds.forEach(userId -> userIdList.add(String.valueOf(userId)));
            count = userIdList.size();
        }
        return Result.newSuccess(count);
    }

    private List<Integer> getCommonUserIdList(String ea,NoticeSendArg.NoticeVisibilityVO visibilityVO) {
        List<Integer> userIdList = Lists.newArrayList();
        List<Integer> departmentIds = visibilityVO.getDepartmentIds();
        List<Integer> userIds = visibilityVO.getUserIds();
        List<NoticeSendArg.roleItem> roles = visibilityVO.getRoles();
        List<NoticeSendArg.UserGroup> userGroups = visibilityVO.getUserGroups();
        Set<Integer> allUserIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            allUserIds.addAll(userIds);
        }
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            if (departmentIds.contains(Constant.WHOLE_COMPANY_ID)) {
                List<Integer> corpUserIds = fsAddressBookManager.getEmployeeIdsByEa(ea);
                if (CollectionUtils.isNotEmpty(corpUserIds)) {
                    allUserIds.addAll(corpUserIds);
                }
            } else {
                List<Integer> userIdsByDepartmentId = fsAddressBookManager.getEmployeeIdsByCircleIds(ea, departmentIds);
                if (CollectionUtils.isNotEmpty(userIdsByDepartmentId)) {
                    allUserIds.addAll(userIdsByDepartmentId);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(roles)){
            List<String> roleCodes = roles.stream().map(roleItem -> roleItem.getRoleCode()).collect(Collectors.toList());
            List<Integer> employeeIds = userRoleManager.getEmployeeIdsByRoles(ea, roleCodes);
            if (CollectionUtils.isNotEmpty(employeeIds)){
                allUserIds.addAll(employeeIds);
            }
        }
        if (CollectionUtils.isNotEmpty(userGroups)){
            List<String> userGroupIds = userGroups.stream().map(group -> group.getUserGroupId()).collect(Collectors.toList());
            List<Integer> userIdsByGroups = crmPaasOrgDataManager.queryUserIdsByUserGroups(ea, userGroupIds);
            if (CollectionUtils.isNotEmpty(userIdsByGroups)){
                allUserIds.addAll(userIdsByGroups);
            }
        }
        userIdList.addAll(allUserIds);
        return userIdList;
    }

    private void sendToDingdingSpreadSendMq(String noticeID, String marketingActivityId,String marketingEventId, List<String> userIdList) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeID);
        NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(noticeEntity.getContentType());
        int objectType = noticeContentTypeEnum.toObjectType();
        Map<String,String> userPageUrlMap = Maps.newHashMap();
        for (String userId : userIdList) {
//            Map<String, String> param = Maps.newHashMap();
//            param.put("applyInfoKey", generateApplyInfoKey(noticeEntity.getFsEa(),userId));
//            param.put("objectId", vo.getContent());
//            param.put("objectType", objectType + "");
//            param.put("marketingEventId", vo.getMarketingEventId());
//            param.put("spreadFsUid", userId);
            boolean isApply = false;
            boolean isMultiple = false;
            String materialInfoList = noticeEntity.getMaterialInfoList();
            if (StringUtils.isNotBlank(materialInfoList)) {
                List<AddMaterialsArg.MaterialInfo> materialInfos = gson.fromJson(materialInfoList, new TypeToken<List<AddMaterialsArg.MaterialInfo>>() {
                }.getType());
                if (CollectionUtils.isNotEmpty(materialInfos) && materialInfos.size() > 0) {
                    isMultiple = true;
                }
            }
            String uid = fsBindManager.queryByFsEaAndUserId(noticeEntity.getFsEa(), Integer.valueOf(userId));
            if (StringUtils.isNotBlank(uid)) {
                isApply = true;
            }
            String miniprogramPath = "/pages/apply/apply?" + "isApply="+isApply+"&objectId="+noticeEntity.getContent()+"&objectType="
                    + objectType +"&marketingEventId="+marketingEventId+"&spreadFsUid="+userId+"&marketingActivityId="+marketingActivityId+"&showShareMask=true"+"&isMultiple="+isMultiple;
            try {
                miniprogramPath = URLEncoder.encode(miniprogramPath,"UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String pageUrl = host + "/proj/page/marketing-mplink?ea=" + noticeEntity.getFsEa() + "&miniprogramPath="+miniprogramPath;
            userPageUrlMap.put(userId, pageUrl);
        }
        DingDingMarketingSpreadVO marketingSpreadVO = new DingDingMarketingSpreadVO();
        marketingSpreadVO.setEa(noticeEntity.getFsEa());
        marketingSpreadVO.setUserSpreadUrlMap(userPageUrlMap);
        marketingSpreadVO.setContent(noticeEntity.getDescription());
        marketingSpreadVO.setTitle(noticeEntity.getTitle());
        marketingSpreadVO.setSingleTitle(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2780));
        dingDingMarketingSpreadSender.send(marketingSpreadVO);
        noticeDAO.updateStatusById(noticeID, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
        marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FINISHED.getStatus()));
    }

    /**
     * 生成applyInfoKey
     * @author: mingqiao
     * @date: 2022/8/24 19:06
     * @param userId:
     * @return: java.lang.String
     */
    private String generateApplyInfoKey(String ea,String userId) {
        String applyInfoKey = MD5Util.md5String(userId + ea);
        RedisKISApplyInfoEntity redisFSApplyInfoEntity = new RedisKISApplyInfoEntity(ea, Integer.valueOf(userId), eieaConverter.enterpriseAccountToId(ea), null);
        redisManager.setKISApplyInfoToRedis(applyInfoKey, redisFSApplyInfoEntity);
        return applyInfoKey;
    }

    /**
     * 伙伴营销发送通知
     * @param ea
     * @param fsUserId
     * @param vo
     * @return
     */
    @Override
    public Result<String> sendPartnerNotice(String ea, Integer fsUserId, PartnerNoticeSendArg vo) throws UnsupportedEncodingException {
        PartnerNoticeSendArg.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = vo.getPartnerNoticeVisibilityVO();
        Map<Integer, String> outUserIdToOutTannetIdMap = new HashMap<>();
        List<Integer> dockUserIDList = fsAddressBookManager.getDockUserId(partnerNoticeVisibilityVO.getEaList(), partnerNoticeVisibilityVO.getTenantGroupIdList(), ea, fsUserId, outUserIdToOutTannetIdMap);
//        dockUserIDList.add(*********);
        List<String> partnerWXAppIdList = getBIndWxByEa(ea);
        //保存通知
        NoticeEntity noticeEntity = doSavePartnerNoticeEntity(ea, fsUserId, vo, dockUserIDList);
        // TODO：新增推广次数 后面要改
        enterpriseStatisticManager.incrementSpreadCount(ea, new Date());
        if (!Strings.isNullOrEmpty(noticeEntity.getContent())) {
            Integer objectType = NoticeContentTypeEnum.fromType(noticeEntity.getContentType()).toObjectType();
            enterpriseStatisticManager.insertEnterpriseObjectDataIgnore(ea, objectType, noticeEntity.getContent());
        }
        //立即发送
        if (vo.getSendType() == NoticeSendTypeEnum.NORMAL.getType() && !marketingActivityAuditManager.isNeedAudit(ea)) {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
            enterpriseDataArg.setEnterpriseAccount(ea);
            enterpriseDataArg.setEnterpriseId(ei);
            GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
            String enterpriseName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
            //调用互联发送通知的接口
            AddRemindRecordArg arg = new AddRemindRecordArg();
            arg.setEi(ei);
            arg.setUuid(UUIDUtil.getUUID());
            RemindRecordItem remindRecordItem = new RemindRecordItem();
            arg.setRemindRecordItem(remindRecordItem);
            //crm通知唯一标识，防止重复发送
            remindRecordItem.setSourceId("HBYX"+noticeEntity.getId());
            //发送人id
            remindRecordItem.setSenderId(fsUserId);
            //CRM通知类型 201代表推广任务通知
            remindRecordItem.setType(201);
            //接收人id
            remindRecordItem.setReceiverIDs(dockUserIDList);
            //是否通知发送人
            remindRecordItem.setRemindSender(true);
            //crm通知内容
            remindRecordItem.setFullContent(noticeEntity.getTitle());
            remindRecordItem.setTitle("推广任务通知");
            remindRecordItem.setTitleInfo(new InternationalItem("qx.ot.mark.partner.notice.title"));
            //CRM跳转URL类型：0 无跳转; 1 跳转详情;2 跳转业务流; 3 对象任务
            remindRecordItem.setUrlType(0);
            remindRecordItem.setAppId(partnerAppId);

            List<KeyValueItem> bodyForm = new ArrayList<>();
            //任务号
            KeyValueItem taskNumber = new KeyValueItem(new TextCardElement("任务号", new InternationalItem("qx.ot.mark.partner.notice.num"), null, null), new TextCardElement(String.valueOf(eieaConverter.enterpriseAccountToId(ea)) + new Date().getTime(), null, null, null));
            //任务类型
            KeyValueItem taskType = new KeyValueItem(new TextCardElement("任务类型", new InternationalItem("qx.ot.mark.partner.notice.type"), null, null), new TextCardElement("伙伴推广任务", new InternationalItem("qx.ot.mark.partner.notice.typeval"), null, null));
            //执行人
            KeyValueItem executor = new KeyValueItem(new TextCardElement("执行人", new InternationalItem("qx.ot.mark.partner.notice.invoker"), null, null), new TextCardElement("我", new InternationalItem("qx.ot.mark.partner.notice.me"), null, null));
//            //分派人
            KeyValueItem assigner = new KeyValueItem(new TextCardElement("分派人", new InternationalItem("qx.ot.mark.partner.notice.dispatcher"), null, null), new TextCardElement(enterpriseName, null, null, null));
//            //分派时间
            KeyValueItem assignTime = new KeyValueItem(new TextCardElement("分派时间", new InternationalItem("qx.ot.mark.partner.notice.time"), null, null), new TextCardElement(DateFormatUtils.format(Instant.now().toEpochMilli(), "yyyy-MM-dd"), null, null, null));
            bodyForm.add(taskNumber);
            bodyForm.add(taskType);
            bodyForm.add(executor);
            bodyForm.add(assigner);
            bodyForm.add(assignTime);
            remindRecordItem.setBodyForm(bodyForm);

            //通知跳转url
            String marketingActivityId = vo.getMarketingActivityId();
            String noticeEntityId = noticeEntity.getId();
            List<String> extraChannelList = new ArrayList<>();
            if (NoticeContentTypeEnum.ACTIVITY.equalsType(vo.getContentType())) {
                ActivityEntity activityEntity = activityDAO.getById(vo.getContent());
                vo.setActivityDetailSiteId(activityEntity == null ? "" : activityEntity.getActivityDetailSiteId());
            }
            //换地址发互联应用
            if (null != remindRecordItem.getExtraChannelList()) {
                remindRecordItem.getExtraChannelList().clear();
            }
            extraChannelList.clear();
            String appUrl = getOubPlatformUrlApp(vo, 2, ea,null);
            remindRecordItem.setOutPlatformUrl(appUrl);
            log.info("send partner app notice url:{}", appUrl);
            //企业微信通道信息 2代表默认的纷享通道，发企信也用这个
            ExtraOutsideChannelInfoVO businessWeChatChannelInfo = new ExtraOutsideChannelInfoVO();
            businessWeChatChannelInfo.setAppId(partnerAppId);
            businessWeChatChannelInfo.setOutChannelType(2);
            businessWeChatChannelInfo.setLinkType("1");
            businessWeChatChannelInfo.setOutChannelData(partnerAppId);
            businessWeChatChannelInfo.setTemplateIdForOut(partnerNoticeTemplate);
            extraChannelList.add(gson.toJson(businessWeChatChannelInfo));
            remindRecordItem.setExtraChannelList(extraChannelList);
            List<Integer> receiverIDs = arg.getRemindRecordItem().getReceiverIDs();
            if (CollectionUtils.isEmpty(receiverIDs)) {
                return Result.newError(SHErrorCode.EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER);
            }
            List<List<Integer>> receiverIdPartition = Lists.partition(receiverIDs, partitionSize);
            int sendCount = receiverIdPartition.size();
            int successCount = 0;
            try {
                //分片发送处理
                for (List<Integer> receiverIds : receiverIdPartition) {
                    arg.getRemindRecordItem().setReceiverIDs(receiverIds);
                    MessageResponse appResponse = crmNotifyService.addRemindRecord(arg);
                    if (null != appResponse && appResponse.getCode() == 200 && "Success".equals(appResponse.getMsg())) {
                        successCount++;
                    }
                }
            } catch (FRestClientException e) {
                log.warn("sendPartnerNotice appResponse  crmNotifyService.addRemindRecord fail", e);
            }
            log.info("send sendPartnerNotice receive sendCount:{},successCount:{}",sendCount,successCount);
            if (Objects.equals(sendCount,successCount)) {
                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId, String.valueOf(SendStatusEnum.FINISHED.getStatus()));
            } else {
                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId, String.valueOf(SendStatusEnum.FAIL.getStatus()));
            }
            //创建推广任务
            spreadTaskManager.createPartnerSpreadTask(dockUserIDList, ea, vo.getMarketingActivityId(),outUserIdToOutTannetIdMap);
        }
        return Result.newSuccess(noticeEntity.getId());
    }

    private List<String> getBIndWxByEa(String ea) {
        List<String> res = new ArrayList<>();
        com.facishare.wechat.union.common.result.Result<List<EaBindInfoData>> infoByEa = eaBindInfoRestService.getInfoByEa(ea, 2);
        if (infoByEa.isSuccess() && null != infoByEa.getData()) {
            infoByEa.getData().stream().forEach(l -> res.add(l.getAppId()));
        }
        return res;
    }

    private String getOubPlatformUrl(PartnerNoticeSendArg vo, int channalType, String ea,String appid) throws UnsupportedEncodingException {
        String baseUrl = "";
        if (2 == channalType) {
            //企信
            baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + ea + "&upstreamEa=" + ea + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
        } else {
            //微信公众号
            baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + appid + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
        }
        String resourceUrlBase = host + "/ec/cml-marketing/release/web/cml-marketing.html?ispartner=true&";
        String resourceUrl = "";
        if (channalType == 4) {
            if (NoticeContentTypeEnum.HEXAGON.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getContent() + "&wxAppId=" + appid + "&type=1&marketingActivityId=" + vo.getMarketingActivityId() + "&_hash=/cml/h5/hexagon_detail";
            } else if (NoticeContentTypeEnum.ACTIVITY.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getActivityDetailSiteId() + "&wxAppId=" + appid + "&type=1&marketingActivityId=" + vo.getMarketingActivityId() + "&targetObjectType=13&targetObjectId=" + vo.getContent() + "&_hash=/cml/h5/hexagon_detail";
            } else if (NoticeContentTypeEnum.PRODUCT.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getContent() + "&wxAppId=" + appid + "&marketingActivityId=" + vo.getMarketingActivityId() + "&_hash=/cml/h5/spread_product_detail";
            } else if (NoticeContentTypeEnum.ARTICLE.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getContent() + "&wxAppId=" + appid + "&marketingActivityId=" + vo.getMarketingActivityId() + "&_hash=/cml/h5/article_detail";
            } else if (NoticeContentTypeEnum.QR_POSTER.equalsType(vo.getContentType())) {
                resourceUrlBase = host + "/proj/page/marketing-poster?type=3&ispartner=1";
                resourceUrl = resourceUrlBase + "&posterid="+vo.getContent()+"&marketingActivityId="+vo.getMarketingActivityId();
            }
        } else {
            if (NoticeContentTypeEnum.HEXAGON.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getContent() + "&type=1&marketingActivityId=" + vo.getMarketingActivityId() + "&_hash=/cml/h5/hexagon_detail";
            } else if (NoticeContentTypeEnum.ACTIVITY.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getActivityDetailSiteId() + "&type=1&marketingActivityId=" + vo.getMarketingActivityId() + "&targetObjectType=13&targetObjectId=" + vo.getContent() + "&_hash=/cml/h5/hexagon_detail";
            } else if (NoticeContentTypeEnum.PRODUCT.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getContent() + "&marketingActivityId=" + vo.getMarketingActivityId() + "&_hash=/cml/h5/spread_product_detail";
            } else if (NoticeContentTypeEnum.ARTICLE.equalsType(vo.getContentType())) {
                resourceUrl = resourceUrlBase + "id=" + vo.getContent() + "&marketingActivityId=" + vo.getMarketingActivityId() + "&_hash=/cml/h5/article_detail";
            } else if (NoticeContentTypeEnum.QR_POSTER.equalsType(vo.getContentType())) {
                resourceUrlBase = host + "/proj/page/marketing-poster?type=3&ispartner=1";
                resourceUrl = resourceUrlBase + "&posterid="+vo.getContent()+"&marketingActivityId="+vo.getMarketingActivityId();
            }
        }
        if (CollectionUtils.isNotEmpty(vo.getMaterialInfoList())) {
            String platformUrl = host + "/proj/page/marketing/" + ea + "#/pkgs/pkg-spread/pages/multi-material/multi-material?ispartner=true&marketingActivityId="+vo.getMarketingActivityId()+"&ea="+ea;
            String outPlatformUrl = baseUrl + URLEncoder.encode(platformUrl, "UTF-8");
            log.info("NoticeServiceImpl.getOubPlatformUrl,mutilate url:{}", outPlatformUrl);
            return outPlatformUrl;
        }

        String outPlatformUrl = baseUrl + URLEncoder.encode(resourceUrl, "UTF-8");
        log.info("NoticeServiceImpl.getOubPlatformUrl,channalType:" + channalType + "url:" + outPlatformUrl);
        return outPlatformUrl;
    }

    private String getCouponOubPlatformUrl(PartnerNoticeSendArg vo, int channalType, String ea,String appid) throws UnsupportedEncodingException {
        String baseUrl = "";
        if (2 == channalType) {
            //企信
            baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + ea + "&upstreamEa=" + ea + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
        } else {
            //微信公众号
            baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + appid + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
        }
        String resourceUrlBase = host + "/proj/page/marketing-coupon?ispartner=true&ea=" + ea + "&";
        String resourceUrl = "";
        if (NoticeContentTypeEnum.SEND_COUPON.equalsType(vo.getContentType())) {
            resourceUrl = resourceUrlBase + "objectId=" + vo.getContent() + "&from=partner&_hash=activity-detail";
        }
        String outPlatformUrl = baseUrl + URLEncoder.encode(resourceUrl, "UTF-8");
        log.info("NoticeServiceImpl.getOubPlatformUrl,channalType:" + channalType + "url:" + outPlatformUrl);
        return outPlatformUrl;
    }

    /**处理纷享小程序的推广地址**/
    private String getOubPlatformUrlApp(PartnerNoticeSendArg vo, int channalType, String ea,String appid) throws UnsupportedEncodingException {
        String baseUrl = "";
        if (2 == channalType) {
            //企信
            baseUrl = "ava://marketing_app/";
        } else {
            //微信公众号
            baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + appid + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
        }
        String resourceUrl = "";
        if (channalType == 2) {
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("upstreamEa",ea);
            paramMap.put("appId",partnerAppId);
            paramMap.put("partner",1);
            paramMap.put("objectId",vo.getContent());
            paramMap.put("marketingActivityId",vo.getMarketingActivityId());
            if (vo.getStaffInfoShow() != null) {
                paramMap.put("staffInfoShow",vo.getStaffInfoShow());
            }
            if (NoticeContentTypeEnum.HEXAGON.equalsType(vo.getContentType())) {
                paramMap.put("objectType","26");
                resourceUrl = "pkgs/pkg-hexagon/pages/detail/hexagon-detail.html?"+ GsonUtil.getGson().toJson(paramMap);
            } else if (NoticeContentTypeEnum.ACTIVITY.equalsType(vo.getContentType())) {
                paramMap.put("objectType","13");
                resourceUrl = "pkgs/pkg-conference/pages/detail/conference-detail.html?"+ GsonUtil.getGson().toJson(paramMap);
            } else if (NoticeContentTypeEnum.PRODUCT.equalsType(vo.getContentType())) {
                paramMap.put("objectType","4");
                resourceUrl = "pkgs/pkg-product/pages/detail/product-detail.html?"+ GsonUtil.getGson().toJson(paramMap);
            } else if (NoticeContentTypeEnum.ARTICLE.equalsType(vo.getContentType())) {
                paramMap.put("objectType","6");
                resourceUrl = "pkgs/pkg-article/pages/detail/article-detail.html?"+ GsonUtil.getGson().toJson(paramMap);
            } else if (NoticeContentTypeEnum.QR_POSTER.equalsType(vo.getContentType())) {
                baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + ea + "&upstreamEa=" + ea + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
                String resourceUrlBase = host + "/proj/page/marketing-poster/"+ ea +"?type=3&ispartner=1&ea=" + ea;
                resourceUrl = resourceUrlBase + "&posterid=" + vo.getContent() + "&marketingActivityId=" + vo.getMarketingActivityId();
                if (vo.getStaffInfoShow() != null) {
                    resourceUrl = resourceUrl + "&staffInfoShow=" + vo.getStaffInfoShow();
                }
                resourceUrl = URLEncoder.encode(resourceUrl, "UTF-8");
            } else if (NoticeContentTypeEnum.OUT_LINK.equalsType(vo.getContentType())) {
                paramMap.put("objectType",NoticeContentTypeEnum.fromType(vo.getContentType()).toObjectType());
                resourceUrl = "pkgs/pkg-external-content/pages/detail/external-content-detail";
                resourceUrl = HttpUtil.joinParamters(resourceUrl,paramMap);
            }
            if (CollectionUtils.isNotEmpty(vo.getMaterialInfoList())) {
                baseUrl = "ava://marketing_app/";
                resourceUrl = "pkgs/pkg-spread/pages/multi-material/multi-material?"+ GsonUtil.getGson().toJson(paramMap);
            }
        }
        String outPlatformUrl = baseUrl + resourceUrl;
        log.info("NoticeServiceImpl.getOubPlatformUrlApp,channalType:" + channalType + "url:" + outPlatformUrl);
        return outPlatformUrl;
    }

    /**处理下发纷享优惠券**/
    private String getCouponOubPlatformUrlApp(PartnerNoticeSendArg vo, int channalType, String ea,String appid) throws UnsupportedEncodingException {
        String baseUrl = "";
        if (2 == channalType) {
            //企信
            baseUrl = "ava://marketing_app/";
        } else {
            //微信公众号
            baseUrl = host + "/fs-er-biz/er/auth/connect?authType=" + channalType + "&context=" + appid + "&fsAppId=" + partnerAppId + "&isRedirect=true&resourceUrl=";
        }
        String resourceUrl = "";
        if (channalType == 2) {
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("upstreamEa",ea);
            paramMap.put("appId",partnerAppId);
            paramMap.put("partner",1);
            paramMap.put("objectId",vo.getContent());
            if (NoticeContentTypeEnum.SEND_COUPON.equalsType(vo.getContentType())) {
                resourceUrl = "pkgs/pkg-coupon/pages/coupon-activity-detail/coupon-activity-detail.html?"+ GsonUtil.getGson().toJson(paramMap);
            }
        }
        String outPlatformUrl = baseUrl + resourceUrl;
        log.info("NoticeServiceImpl.getCouponOubPlatformUrlApp,channalType:" + channalType + "url:" + outPlatformUrl);
        return outPlatformUrl;
    }



    private List<String> getDingUserIdList(NoticeSendArg noticeSendArg) {
        NoticeSendArg.NoticeVisibilityVO noticeVisibilityVO = noticeSendArg.getNoticeVisibilityVO();
        if(null==noticeVisibilityVO){
            return new ArrayList<>();
        }
        List<String> userIds = noticeVisibilityVO.getOutUserIds();
        List<Integer> departmentIds = noticeVisibilityVO.getDepartmentIds();

        return null;
    }

    private void sendToDingding(String noticeID, String marketingActivityId, NoticeSendArg vo, List<String> userIdList) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeID);
        NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(vo.getContentType());
        int objectType = noticeContentTypeEnum.toObjectType();
        Map<String, String> param = Maps.newHashMap();
        param.put("objectId", vo.getContent());
        param.put("objectType",  objectType +"");
        param.put("isGroupSend", 1 + "");
        param.put("marketingActivityId", vo.getMarketingActivityId());
        param.put("spreadType", QywxSpreadTypeEnum.ALL_SPREAD.getType() + "");
        String page = "eapp://pages/share/share?" + httpManager.transformUrlParams(param);
        Map<String, Object> dingContentMap = new HashMap<>();
        dingContentMap.put("title",vo.getTitle() );
        dingContentMap.put("content", vo.getDescription());
        dingContentMap.put("single_url", page);
        dingContentMap.put("single_title", I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2780));
        com.facishare.open.ding.common.result.Result<String> corpIdResult = dingAuthService.EAtoDingCorpId(noticeEntity.getFsEa());
        String corpId = "";
        if (corpIdResult.isSuccess() && org.apache.commons.lang.StringUtils.isNotBlank(corpIdResult.getData())) {
            corpId = corpIdResult.getData();
        }
        Result<DingSendByTemplateMessageResult> res = dingMiniAppStaffService.dingSendMessage(null, dingdingNoticeTemplate, dingContentMap, userIdList, corpId);

        if (null != res && res.isSuccess() && null != res.getData() && null != res.getData().getTaskId()) {
            noticeDAO.updateStatusById(noticeID, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
            marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FINISHED.getStatus()));
        } else {
            noticeDAO.updateStatusById(noticeID, NoticeStatusEnum.FAIL.getStatus(), new Date());
            marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FAIL.getStatus()));
        }
        log.info("result:{}" + res);
    }

    @Override
    public Result<Void> sendNoticeById(String noticeId, String marketingActivityId,String marketingEventId) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null) {
            return Result.newSuccess();
        }
        boolean isNormal = marketingActivityAuditManager.checkAudtStatus(noticeEntity.getFsEa(), marketingActivityId);
        if (!isNormal) {
            return Result.newSuccess();
        }
        if (marketingActivityRemoteManager.enterpriseStop(noticeEntity.getFsEa()) || appVersionManager.getCurrentAppVersion(noticeEntity.getFsEa()) == null) {
            log.info("NoticeServiceImpl.sendNoticeById failed enterprise stop or license expire ea:{}", noticeEntity.getFsEa());
            return Result.newSuccess();
        }
        if (noticeEntity.getType() != null && noticeEntity.getType() == SendNoticeTypeEnum.MEMBER_NOTICE.getType()) {
            // 处理会员营销
            memberMarketingManager.sendNotice(noticeEntity.getFsEa(), noticeEntity,marketingEventId);
            return Result.newSuccess();
        }
        //创建推广任务
        NoticeSendArg.NoticeVisibilityVO visibilityVO = gson.fromJson(noticeEntity.getSendScope(),
                NoticeSendArg.NoticeVisibilityVO.class);

        List<Integer> allUserIds = getCommonUserIdList(noticeEntity.getFsEa(), visibilityVO);
        //单独处理旷视
        if (StringUtils.isNotBlank(ksDingEa) && Arrays.asList(ksDingEa.split(",")).contains(noticeEntity.getFsEa())) {
            List<String> userIdList = Lists.newArrayList();
            allUserIds.forEach(userId ->{
                userIdList.add(String.valueOf(userId));
            });
            if(CollectionUtils.isNotEmpty(userIdList)){
                sendToDingdingSpreadSendMq(noticeEntity.getId(), marketingActivityId, marketingEventId,userIdList);
            }
            spreadTaskManager.createSpreadTask(noticeEntity.getFsEa(), marketingActivityId, allUserIds);
            return Result.newSuccess();
        }
        boolean isOpen = dataPermissionManager.getDataPermissionSetting(noticeEntity.getFsEa());
        if (isOpen) {
            List<String> fsUserIds = CollectionUtils.isNotEmpty(visibilityVO.getUserIds()) ? visibilityVO.getUserIds().stream().map(String::valueOf).collect(Collectors.toList()) : null;
            List<Integer> departmentIds = visibilityVO.getDepartmentIds();
            List<String> roleCodes = CollectionUtils.isEmpty(visibilityVO.getRoles()) ? null : visibilityVO.getRoles().stream().map(NoticeSendArg.roleItem::getRoleCode).collect(Collectors.toList());
            List<String> userGroupIds = CollectionUtils.isEmpty(visibilityVO.getUserGroups()) ? null : visibilityVO.getUserGroups().stream().map(NoticeSendArg.UserGroup::getUserGroupId).collect(Collectors.toList());
            List<Integer> accesibleUserIds = qywxManager.handleEmployeeUserIdWithOpenDataPermission(noticeEntity.getFsEa(), noticeEntity.getFsUserId(), fsUserIds, departmentIds, roleCodes, userGroupIds,false);
            allUserIds = new ArrayList<>(CollectionUtils.intersection(allUserIds, accesibleUserIds));
        }

        if (BooleanUtils.isTrue(enterpriseSpreadImagetextPower)) {
            sendImageTextNotice(noticeEntity.getId(), marketingActivityId, marketingEventId, allUserIds);
        } else {
            sendTextNotice(noticeEntity.getId(), marketingActivityId, marketingEventId, allUserIds);
        }
        // 需审核的真正执行才更新执行时间
        if (noticeEntity.getTimingTime() == null) {
            noticeDAO.updateTimingDateById(noticeEntity.getId(), DateUtil.parse(DateUtil.format(new Date())));
        }
        spreadTaskManager.createSpreadTask(noticeEntity.getFsEa(), marketingActivityId, new ArrayList<>(allUserIds));

        return Result.newSuccess();
    }

    @Override
    public Result<Void> sendPartnerNoticeById(String noticeId, String marketingActivityId) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (null != noticeEntity) {
            boolean isNormall = marketingActivityAuditManager.checkAudtStatus(noticeEntity.getFsEa(), marketingActivityId);
            if (isNormall) {
                //过滤掉停用企业、营销通配额过期企业
                if (marketingActivityRemoteManager.enterpriseStop(noticeEntity.getFsEa()) || appVersionManager.getCurrentAppVersion(noticeEntity.getFsEa()) == null) {
                    log.info("NoticeServiceImpl.sendPartnerNoticeById failed enterprise stop or license expire ea:{}", noticeEntity.getFsEa());
                    return Result.newSuccess();
                }

                //创建推广任务
                NoticeSendArg.NoticeVisibilityVO visibilityVO = gson.fromJson(noticeEntity.getSendScope(),
                        NoticeSendArg.NoticeVisibilityVO.class);
                MarketingActivityExternalConfigEntity marketingActivityEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivityId);
                if (visibilityVO != null) {
                    String ea = marketingActivityEntity.getEa();
                    List<String> eaList = visibilityVO.getEaList();
                    List<String> tenantGroupIdList = visibilityVO.getTenantGroupIdList();
                    Map<Integer, String> outUserIdToOutTannetIdMap = new HashMap<>();
                    List<Integer> dockUserIDList = fsAddressBookManager.getDockUserId(eaList, tenantGroupIdList, marketingActivityEntity.getEa(), null, outUserIdToOutTannetIdMap);
                    List<String> partnerWXAppIdList = getBIndWxByEa(ea);
                    enterpriseStatisticManager.incrementSpreadCount(ea, new Date());
                    if (!Strings.isNullOrEmpty(noticeEntity.getContent())) {
                        Integer objectType = NoticeContentTypeEnum.fromType(noticeEntity.getContentType()).toObjectType();
                        enterpriseStatisticManager.insertEnterpriseObjectDataIgnore(ea, objectType, noticeEntity.getContent());
                    }
                    //立即发送
                    int ei = eieaConverter.enterpriseAccountToId(ea);
                    GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
                    enterpriseDataArg.setEnterpriseAccount(ea);
                    enterpriseDataArg.setEnterpriseId(ei);
                    GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
                    String enterpriseName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
                    //调用互联发送通知的接口
                    AddRemindRecordArg arg = new AddRemindRecordArg();
                    arg.setEi(ei);
                    arg.setUuid(UUIDUtil.getUUID());
                    RemindRecordItem remindRecordItem = new RemindRecordItem();
                    arg.setRemindRecordItem(remindRecordItem);
                    //crm通知唯一标识，防止重复发送
                    remindRecordItem.setSourceId("HBYX" + noticeEntity.getId());
                    //发送人id
                    remindRecordItem.setSenderId(-10000);
                    //CRM通知类型 201代表推广任务通知
                    remindRecordItem.setType(201);
                    //接收人id
                    remindRecordItem.setReceiverIDs(dockUserIDList);
                    //是否通知发送人
                    remindRecordItem.setRemindSender(true);
                    //crm通知内容
                    remindRecordItem.setFullContent(noticeEntity.getTitle());
                    remindRecordItem.setTitle("推广任务通知");
                    remindRecordItem.setTitleInfo(new InternationalItem("qx.ot.mark.partner.notice.title"));
                    //CRM跳转URL类型：0 无跳转; 1 跳转详情;2 跳转业务流; 3 对象任务
                    remindRecordItem.setUrlType(0);
                    remindRecordItem.setAppId(partnerAppId);

                    List<KeyValueItem> bodyForm = new ArrayList<>();
                    //任务号
                    KeyValueItem taskNumber = new KeyValueItem(new TextCardElement("任务号", new InternationalItem("qx.ot.mark.partner.notice.num"), null, null), new TextCardElement(String.valueOf(eieaConverter.enterpriseAccountToId(ea)) + new Date().getTime(), null, null, null));
                    //任务类型
                    KeyValueItem taskType = new KeyValueItem(new TextCardElement("任务类型", new InternationalItem("qx.ot.mark.partner.notice.type"), null, null), new TextCardElement("伙伴推广任务", new InternationalItem("qx.ot.mark.partner.notice.typeval"), null, null));
                    //执行人
                    KeyValueItem executor = new KeyValueItem(new TextCardElement("执行人", new InternationalItem("qx.ot.mark.partner.notice.invoker"), null, null), new TextCardElement("我", new InternationalItem("qx.ot.mark.partner.notice.me"), null, null));
//                  //分派人
                    KeyValueItem assigner = new KeyValueItem(new TextCardElement("分派人", new InternationalItem("qx.ot.mark.partner.notice.dispatcher"), null, null), new TextCardElement(enterpriseName, null, null, null));
//                  //分派时间
                    KeyValueItem assignTime = new KeyValueItem(new TextCardElement("分派时间", new InternationalItem("qx.ot.mark.partner.notice.time"), null, null), new TextCardElement(DateFormatUtils.format(Instant.now().toEpochMilli(), "yyyy-MM-dd"), null, null, null));
                    bodyForm.add(taskNumber);
                    bodyForm.add(taskType);
                    bodyForm.add(executor);
                    bodyForm.add(assigner);
                    bodyForm.add(assignTime);
                    remindRecordItem.setBodyForm(bodyForm);

                    //通知跳转url
                    String webUrl;
                    Integer noticeContentType = noticeEntity.getContentType();
                    List<String> extraChannelList = new ArrayList<>();
                    boolean wxSendSuccess = false;
                    PartnerNoticeSendArg vo = new PartnerNoticeSendArg();
                    vo.setContentType(noticeEntity.getContentType());
                    vo.setContent(noticeEntity.getContent());
                    vo.setMarketingActivityId(marketingActivityId);
                    if (NoticeContentTypeEnum.ACTIVITY.equalsType(noticeEntity.getContentType())) {
                        ActivityEntity activityEntity = activityDAO.getById(noticeEntity.getContent());
                        vo.setActivityDetailSiteId(activityEntity == null ? "" : activityEntity.getActivityDetailSiteId());
                    }
                    if (StringUtils.isNotBlank(noticeEntity.getMaterialInfoList())) {
                        List<AddMarketingActivityArg.MaterialInfo> materialInfos = gson.fromJson(noticeEntity.getMaterialInfoList(), new TypeToken<List<AddMarketingActivityArg.MaterialInfo>>() {
                        }.getType());
                        vo.setMaterialInfoList(materialInfos);
                    }
                    try {
                        //换地址发互联应用
                        if (null != remindRecordItem.getExtraChannelList()) {
                            remindRecordItem.getExtraChannelList().clear();
                        }
                        extraChannelList.clear();
                        String appUrl = getOubPlatformUrlApp(vo, 2, ea, null);
                        remindRecordItem.setOutPlatformUrl(appUrl);
                        log.info("send partner app notice url:{}", appUrl);
                        //企业微信通道信息 2代表默认的纷享通道，发企信也用这个
                        ExtraOutsideChannelInfoVO businessWeChatChannelInfo = new ExtraOutsideChannelInfoVO();
                        businessWeChatChannelInfo.setAppId(partnerAppId);
                        businessWeChatChannelInfo.setOutChannelType(2);
                        businessWeChatChannelInfo.setLinkType("1");
                        businessWeChatChannelInfo.setOutChannelData(partnerAppId);
                        businessWeChatChannelInfo.setTemplateIdForOut(partnerNoticeTemplate);
                        extraChannelList.add(gson.toJson(businessWeChatChannelInfo));
                        remindRecordItem.setExtraChannelList(extraChannelList);
                        List<Integer> receiverIDs = arg.getRemindRecordItem().getReceiverIDs();
                        if (CollectionUtils.isEmpty(receiverIDs)) {
                            return Result.newError(SHErrorCode.EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER);
                        }
                        List<List<Integer>> receiverIdPartition = Lists.partition(receiverIDs, partitionSize);
                        int sendCount = receiverIdPartition.size();
                        int successCount = 0;
                        try {
                            //分片发送处理
                            for (List<Integer> receiverIds : receiverIdPartition) {
                                arg.getRemindRecordItem().setReceiverIDs(receiverIds);
                                MessageResponse appResponse = crmNotifyService.addRemindRecord(arg);
                                if (null != appResponse && appResponse.getCode() == 200 && "Success".equals(appResponse.getMsg())) {
                                    successCount ++;
                                }
                            }
                        } catch (FRestClientException e) {
                            log.warn("sendPartnerNoticeById appResponse  crmNotifyService.addRemindRecord fail", e);
                        }
                        log.info("send sendPartnerNoticeById receive sendCount:{},successCount:{}",sendCount,successCount);
                        if (Objects.equals(sendCount,successCount)) {
                            noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
                            marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId, String.valueOf(SendStatusEnum.FINISHED.getStatus()));
                        } else {
                            noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                            marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId, String.valueOf(SendStatusEnum.FAIL.getStatus()));
                        }
                        //创建推广任务
                        spreadTaskManager.createPartnerSpreadTask(dockUserIDList, ea, marketingActivityId, outUserIdToOutTannetIdMap);
                    } catch (UnsupportedEncodingException e) {
                        log.warn("exception:",  e);
                    }
                }
            }

        }

        return Result.newSuccess();
    }

    private NoticeEntity doSaveNoticeEntity(String ea, Integer fsUserId, NoticeSendArg vo) {
        //String username = fsAddressBookManager.getEmployeeInfo(ea, fsUserId).getFullName();
        String username = fsAddressBookManager.getEmployeeInfo(ea, fsUserId) == null ? "" : fsAddressBookManager.getEmployeeInfo(ea, fsUserId).getFullName();
        NoticeEntity noticeEntity = new NoticeEntity();
        noticeEntity.setId(UUIDUtil.getUUID());
        noticeEntity.setTitle(vo.getTitle());
        noticeEntity.setDescription(vo.getDescription());
        noticeEntity.setSendType(vo.getSendType());
        if (marketingActivityAuditManager.isNeedAudit(ea)) {
            noticeEntity.setSendType(2);
        }
        noticeEntity.setCreateTime(new Date());
        noticeEntity.setUpdateTime(new Date());
        noticeEntity.setFsEa(ea);
        noticeEntity.setFsUserId(fsUserId);
        noticeEntity.setUsername(username);
        noticeEntity.setStartTime(DateUtil.parse(DateUtil.format(vo.getStartTime())));
        noticeEntity.setEndTime(DateUtil.parse(DateUtil.format(vo.getEndTime())));
        String content = vo.getContent();
        noticeEntity.setContent(content);
        noticeEntity.setContentType(vo.getContentType());
        noticeEntity.setCoverApath(vo.getCoverPath());
        if (vo.getType() != null) {
            noticeEntity.setType(vo.getType());
        }
        noticeEntity.setStatus(NoticeStatusEnum.UN_SEND.getStatus());
        if (vo.getSendType() == NoticeSendTypeEnum.NORMAL.getType()) {
//            noticeEntity.setStatus(NoticeStatusEnum.SENDING.getStatus());
            noticeEntity.setSendTime(new Date());
        } else {
//            noticeEntity.setStatus(NoticeStatusEnum.UN_SEND.getStatus());
            noticeEntity.setTimingTime(DateUtil.parse(DateUtil.format(vo.getTimingDate())));
        }

        String sendScope = gson.toJson(vo.getNoticeVisibilityVO());
        noticeEntity.setSendScope(sendScope);
        if (CollectionUtils.isNotEmpty(vo.getMaterialInfoList())) {
            String materialInfo = gson.toJson(vo.getMaterialInfoList());
            noticeEntity.setMaterialInfoList(materialInfo);
        }
        //保存伙伴营销通知信息
        noticeDAO.addNotice(noticeEntity);
        if (noticeEntity.getStatus() == NoticeStatusEnum.SENDING.getStatus()){
            marketingActivityManager.updateMarketingActivityStatus(ea, vo.getMarketingActivityId(),String.valueOf(SendStatusEnum.PROCESSING.getStatus()));
        }
        if (noticeEntity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus()){
            marketingActivityManager.updateMarketingActivityStatus(ea, vo.getMarketingActivityId(),String.valueOf(SendStatusEnum.WAIT_SEND.getStatus()));
        }

        return noticeEntity;
    }

    private NoticeEntity doSavePartnerNoticeEntity(String ea, Integer fsUserId, PartnerNoticeSendArg vo, List<Integer> dockUserIDList) {
        String username = fsAddressBookManager.getEmployeeInfo(ea, fsUserId).getFullName();
        NoticeEntity noticeEntity = new NoticeEntity();
        noticeEntity.setId(UUIDUtil.getUUID());
        noticeEntity.setTitle(vo.getTitle());
        noticeEntity.setDescription(vo.getDescription());
        noticeEntity.setSendType(vo.getSendType());
        if (marketingActivityAuditManager.isNeedAudit(ea)) {
            noticeEntity.setSendType(2);
        }
        noticeEntity.setCreateTime(new Date());
        noticeEntity.setUpdateTime(new Date());
        noticeEntity.setFsEa(ea);
        //营销类型
        noticeEntity.setType(7);
        noticeEntity.setFsUserId(fsUserId);
        noticeEntity.setUsername(username);
        noticeEntity.setStartTime(DateUtil.parse(DateUtil.format(vo.getStartTime())));
        noticeEntity.setEndTime(DateUtil.parse(DateUtil.format(vo.getEndTime())));
        String content = vo.getContent();
        noticeEntity.setContent(content);
        noticeEntity.setContentType(vo.getContentType());
        noticeEntity.setCoverApath(vo.getCoverPath());

        if (noticeEntity.getSendType() == NoticeSendTypeEnum.NORMAL.getType()) {
            noticeEntity.setStatus(NoticeStatusEnum.SENDING.getStatus());
            noticeEntity.setSendTime(new Date());
        } else {
            noticeEntity.setStatus(NoticeStatusEnum.UN_SEND.getStatus());
            Date timingTime = vo.getTimingDate() == null ? new Date() : DateUtil.parse(DateUtil.format(vo.getTimingDate()));
            noticeEntity.setTimingTime(timingTime);
        }
        NoticeSendArg.NoticeVisibilityVO noticeVisibilityVO = new  NoticeSendArg.NoticeVisibilityVO();
        noticeVisibilityVO.setUserIds(dockUserIDList);
        noticeVisibilityVO.setEaList(vo.getPartnerNoticeVisibilityVO().getEaList());
        noticeVisibilityVO.setTenantGroupIdList(vo.getPartnerNoticeVisibilityVO().getTenantGroupIdList());
        String sendScope = gson.toJson(noticeVisibilityVO);
        noticeEntity.setSendScope(sendScope);
        if (CollectionUtils.isNotEmpty(vo.getMaterialInfoList())) {
            String materialInfo = gson.toJson(vo.getMaterialInfoList());
            noticeEntity.setMaterialInfoList(materialInfo);
        }
        //保存通知
        noticeDAO.addNotice(noticeEntity);
        if (noticeEntity.getStatus() == NoticeStatusEnum.SENDING.getStatus()){
            marketingActivityManager.updateMarketingActivityStatus(ea, vo.getMarketingActivityId(),String.valueOf(SendStatusEnum.PROCESSING.getStatus()));
        }
        if (noticeEntity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus()){
            marketingActivityManager.updateMarketingActivityStatus(ea, vo.getMarketingActivityId(),String.valueOf(SendStatusEnum.WAIT_SEND.getStatus()));
        }

        return noticeEntity;
    }

    // 文本消息
    private void sendTextNotice(String noticeId, String marketingActivityId, String marketingEventId, List<Integer> allUserIds) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity != null) {
            try {
                Integer noticeContentType = noticeEntity.getContentType();
                String contentTitle;
                if (NoticeContentTypeEnum.SAVE_CRM_FAIL.getType() != noticeEntity.getContentType()) {
                    contentTitle = getPromotionContent(noticeEntity.getContentType(), noticeEntity.getContent()).getTitle();
                } else {
                    contentTitle = noticeEntity.getTitle();
                }
                boolean multiple = false;
                String materialInfoList = noticeEntity.getMaterialInfoList();
                if (StringUtils.isNotBlank(materialInfoList)) {
                    List<AddMaterialsArg.MaterialInfo> materialInfos = gson.fromJson(materialInfoList, new TypeToken<List<AddMaterialsArg.MaterialInfo>>() {
                    }.getType());
                    if (CollectionUtils.isNotEmpty(materialInfos) && materialInfos.size() > 0) {
                        multiple = true;
                    }
                }
                boolean finalMultiple = multiple;
                ThreadPoolUtils.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            String title = noticeEntity.getTitle();
                            String description = noticeEntity.getDescription();
                            String noticeId = noticeEntity.getId();
                            String url;
                            if (finalMultiple) {
                                url = host + "/ec/kemai/release/notice.html?_hash=notice&?marketingActivityId="+marketingActivityId+"&noticeId="+noticeId+ "&useNoticeCenterPage=true";
                            } else {
                                final String encodePage = URLEncoder.encode("pages/noticeCenter/noticeCenter", "UTF-8");
                                final String qrUrl = host + "/fssharehelper/file/showQRCode?scene=" + noticeId + "&page=" + encodePage + "&width=860&type=notice";
                                final String encodeQRUrl = URLEncoder.encode(qrUrl, "UTF-8");
                                if (marketingActivityId != null) {
                                    url = host + "/ec/kemai/release/notice.html?_hash=notice&title=" + URLEncoder.encode(contentTitle, "UTF-8") + "&noticeId=" + noticeId + "&qrUrl=" + encodeQRUrl
                                            + "&useNoticeCenterPage=true" + "&contentType=" + noticeContentType + "&marketingActivityId=" + marketingActivityId;
                                } else if (noticeContentType == NoticeContentTypeEnum.SAVE_CRM_FAIL.getType()) {
                                    url = null;
                                } else {
                                    url = host + "/ec/kemai/release/notice.html?_hash=notice&title=" + URLEncoder.encode(contentTitle, "UTF-8") + "&noticeId=" + noticeId + "&qrUrl=" + encodeQRUrl
                                            + "&useNoticeCenterPage=true" + "&contentType=" + noticeContentType;
                                }
                                //判断marketingEventId
                                if (marketingEventId != null) {
                                    url += "&marketingEventId=" + marketingEventId;
                                }
                            }
                            log.info("sendTextNotice url:{}", url);

                            SendFXMessageArg sendFXMessageArg = new SendFXMessageArg();
                            sendFXMessageArg.setUserIds(allUserIds);
                            sendFXMessageArg.setFsEa(noticeEntity.getFsEa());
                            sendFXMessageArg.setUserId(noticeEntity.getFsUserId());
                            sendFXMessageArg.setTitle(title);
                            sendFXMessageArg.setDescription(description);
                            sendFXMessageArg.setContent(contentTitle);
                            sendFXMessageArg.setUrl(url);
                            sendFXMessageArg.setContentType(noticeEntity.getContentType());
                            sendFXMessageArg.setContentId(noticeEntity.getContent());
                            sendFXMessageArg.setNoticeId(noticeEntity.getId());

                            if (noticeEntity.getStartTime() != null && noticeEntity.getEndTime() != null) {
                                String[] strStart = new SimpleDateFormat("MM-dd").format(noticeEntity.getStartTime()).split("-");
                                String[] strEnd = new SimpleDateFormat("MM-dd").format(noticeEntity.getEndTime()).split("-");
                                Integer startMonth = Integer.parseInt(strStart[0]);
                                Integer startDay = Integer.parseInt(strStart[1]);
                                Integer endMonth = Integer.parseInt(strEnd[0]);
                                Integer endDay = Integer.parseInt(strEnd[1]);

                                String limitDate = new StringBuilder().append(startMonth).append(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972)).append(startDay).append(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1)).append(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3965)).append(endMonth).append(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972)).append(endDay).append(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1))
                                        .toString();
                                sendFXMessageArg.setLimitDate(limitDate);
                            }
                            Integer status;
                            boolean result = fsMessageManager.sendFXMessage(sendFXMessageArg);
                            if (result) {
                                noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
                                status = SendStatusEnum.FINISHED.getStatus();
                            } else {
                                noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                                status = SendStatusEnum.FAIL.getStatus();
                            }

                            doNoticeStatusSenderMq(noticeEntity.getFsEa(), status, noticeId);
                        } catch (Exception e) {
                            log.error("Error: fail sendMq notice ! noticeId:{}", noticeId, e);
                            noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                        }
                    }
                }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);
            } catch (Exception e) {
                log.error("NoticeServiceImpl sendTextNotice Error, e:{}", e);
            }
        }
    }

    // 图文消息
    public void sendImageTextNotice(String noticeId, String marketingActivityId, String marketingEventId, List<Integer> allUserIds) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity != null) {
            try {
                boolean multiple = false;
                String materialInfoList = noticeEntity.getMaterialInfoList();
                if (StringUtils.isNotBlank(materialInfoList)) {
                    List<AddMaterialsArg.MaterialInfo> materialInfos = gson.fromJson(materialInfoList, new TypeToken<List<AddMaterialsArg.MaterialInfo>>() {
                    }.getType());
                    if (CollectionUtils.isNotEmpty(materialInfos) && materialInfos.size() > 0) {
                        multiple = true;
                    }
                }
                Integer noticeContentType = noticeEntity.getContentType();
                String contentTitle;
                if (!multiple) {
                    contentTitle = Objects.requireNonNull(getPromotionContent(noticeEntity.getContentType(), noticeEntity.getContent())).getTitle();
                } else {
                    contentTitle = noticeEntity.getTitle();
                }

                boolean finalMultiple = multiple;
                ThreadPoolUtils.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {

                            String title = noticeEntity.getTitle();
                            String description = noticeEntity.getDescription();
                            String noticeEntityId = noticeEntity.getId();

                            //获取域名--支持用户自定义域名
                            String url;
                            String domain = host;
                            Optional<String> domainOpt = settingManager.getEnterpriseDomain(noticeEntity.getFsEa());
                            if (domainOpt.isPresent()){
                                domain = domainOpt.get();
                            }

                            if (finalMultiple) {
                                url = domain+"/ec/kemai/release/notice.html?_hash=notice&marketingActivityId="+marketingActivityId+"&noticeId="+noticeId+"&useNoticeCenterPage=true";
                            } else if (Objects.equals(noticeEntity.getContentType(), NoticeContentTypeEnum.EMAIL_MATERIAL.getType())) {
                                // 邮件物料推广
                                String fsEa = noticeEntity.getFsEa();
                                url = domain + "/proj/page/marketing/" + fsEa + "/#/mail-notice?noticeId=" + noticeEntityId + "&ea=" + fsEa + "&marketingEventId=" + marketingEventId + "&marketingActivityId=" + marketingActivityId;
                            } else {
                                final String encodePage = URLEncoder.encode("pages/noticeCenter/noticeCenter", "UTF-8");
                                final String qrUrl = domain + "/appmarketing/web/file/showQRCode?scene=" + noticeEntityId + "&page=" + encodePage + "&width=860&type=notice";
                                final String encodeQRUrl = URLEncoder.encode(qrUrl, "UTF-8");
                                if (marketingActivityId != null) {
                                    url = domain + "/ec/kemai/release/notice.html?_hash=notice&title=" + URLEncoder.encode(contentTitle, "UTF-8") + "&noticeId=" + noticeEntityId + "&qrUrl=" + encodeQRUrl
                                            + "&useNoticeCenterPage=true" + "&contentType=" + noticeContentType + "&marketingActivityId=" + marketingActivityId;
                                } else {
                                    url = domain + "/ec/kemai/release/notice.html?_hash=notice&title=" + URLEncoder.encode(contentTitle, "UTF-8") + "&noticeId=" + noticeEntityId + "&qrUrl=" + encodeQRUrl
                                            + "&useNoticeCenterPage=true" + "&contentType=" + noticeContentType;
                                }

                                //判断marketingEventId
                                if (marketingEventId != null) {
                                    url += "&marketingEventId=" + marketingEventId;
                                }
                            }

                            log.info("sendImageTextNotice url:{}", url);

                            String imageUrl = "";
                            String apath = null;
                            apath = getCoverPathByNotice(noticeEntity);
                            if (StringUtils.isBlank(apath)) {
                                apath = imagetextDefaultCoverApath;
                            }

                            imageUrl = fileV2Manager.getSpliceUrl(noticeEntity.getFsEa(), apath);
                            log.info("spliceUrlFromApathResultModelResult url:{} contentUr:{}", imageUrl, url);
                            if (StringUtils.isBlank(imageUrl)) {
                                log.error("NoticeService.sendEnterpriseSpreadNotice getSpliceUrl failed, imageUrl is null, apath={", apath );
                                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                                marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FAIL.getStatus()));
                                return;
                            }

                            String buttonText = "立即去推广";
                            Map<String, Object> paramMap = new HashMap<>();
                            paramMap.put("createTime", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
                            paramMap.put("messageId", com.facishare.mankeep.common.util.UUIDUtil.getUUID());
                            List<Map<String, Object>> imageTextListMap = new ArrayList<>();
                            Map<String, Object> imageTextMap = new HashMap<>();
                            imageTextMap.put("summary", description);
                            imageTextMap.put("contentUrl", url);
                            imageTextMap.put("imageUrl", imageUrl);
                            imageTextMap.put("title", "您有一个新的推广任务");
                            imageTextMap.put("internationalTitle", "qx.ot.mark.promotion_task_notification");
                            imageTextMap.put("buttonText", buttonText);
                            imageTextMap.put("internationalButtonText", "qx.ot.mark.promote_immediately");
                            imageTextMap.put("buttonUrl", url);
                            imageTextMap.put("contentType", "1");
                            imageTextMap.put("messageType", "1");
                            imageTextMap.put("contentTitle", title);
                            imageTextMap.put("description", description);
                            List<Map<String, Object>> contentListMap = new ArrayList<>();
                            Map<String, Object> conductMap = new HashMap<>();
                            conductMap.put("宣传语", description);
                            contentListMap.add(conductMap);
                            Map<String, Object> timeMap = new HashMap<>();
                            timeMap.put("推广时间", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
                            contentListMap.add(timeMap);
                            imageTextMap.put("contentListMap", contentListMap);
                            List<Map<String, Object>> internationalContentListMap = new ArrayList<>();
                            Map<String, Object> i18conductMap = new HashMap<>();
                            i18conductMap.put("qx.ot.mark.promotion_slogan", description);
                            internationalContentListMap.add(i18conductMap);
                            Map<String, Object> i18timeMap = new HashMap<>();
                            i18timeMap.put("qx.ot.mark.promotion_time", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
                            internationalContentListMap.add(i18timeMap);
                            imageTextMap.put("internationalContentListMap", internationalContentListMap);
                            String customParamJson = noticeManager.urlDispatcher(noticeEntity.getFsEa(), noticeEntity.getContentType(), noticeEntity.getContent(), noticeEntity.getId(), marketingActivityId);
                            if (Objects.equals(noticeEntity.getContentType(), NoticeContentTypeEnum.EMAIL_MATERIAL.getType())) {
                                // 邮件物料特殊处理，只能在pc端访问，统一下发pc端地址
                                customParamJson = url;
                            }
                            if (finalMultiple) {
                                NoticeManager.ParamResult paramResult = new NoticeManager.ParamResult();
                                String sb = "ava://marketing_app/pkgs/pkg-spread/pages/multi-material/multi-material" +
                                        "?marketingActivityId=" +
                                        marketingActivityId;
                                paramResult.setUrl(sb);
                                customParamJson = GsonUtil.toJson(paramResult);
                            }
                            imageTextMap.put("customParamJson", customParamJson);
                            if (noticeEntity.getStartTime() != null && noticeEntity.getEndTime() != null) {
                                String[] strStart = new SimpleDateFormat("MM-dd").format(noticeEntity.getStartTime()).split("-");
                                String[] strEnd = new SimpleDateFormat("MM-dd").format(noticeEntity.getEndTime()).split("-");
                                Integer startMonth = Integer.parseInt(strStart[0]);
                                Integer startDay = Integer.parseInt(strStart[1]);
                                Integer endMonth = Integer.parseInt(strEnd[0]);
                                Integer endDay = Integer.parseInt(strEnd[1]);
                                String limitDate = new StringBuilder().append(startMonth).append(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972)).append(startDay).append(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1)).append(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3965)).append(endMonth).append(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972)).append(endDay).append(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1))
                                        .toString();
                                imageTextMap.put("limitDate", limitDate);
                            }

                            imageTextListMap.add(imageTextMap);
                            paramMap.put("imageTextList", imageTextListMap);

                            //增加市场活动id
                            if (marketingEventId != null) {
                                paramMap.put("marketingEvenId", marketingEventId);
                            }
                            log.info("NoticeService.sendEnterpriseSpreadNotice paramMap={}", paramMap);
                            sendNoticeType(new HashSet<>(allUserIds), title, description, noticeEntityId, apath, paramMap, noticeEntity, marketingActivityId, imageUrl,finalMultiple);
                        } catch (Exception e) {
                            log.error("Error: fail sendMq notice ! noticeId:{}", noticeId, e);
                            noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                            marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(),marketingActivityId,String.valueOf(SendStatusEnum.FAIL.getStatus()));
                        }
                    }
                }, ThreadPoolTypeEnums.SEND_NOTCIE);
            } catch (Exception e) {
                log.warn("Error", e);
            }
        }
    }

    private void sendNoticeType(Set<Integer> allUserIds, String title, String description, String noticeEntityId, String apath, Map<String, Object> paramMap, NoticeEntity noticeEntity, String marketingActivityId,String imageUrl,boolean finalMultiple) {
        MarketingNoticeSettingEntity noticeSettingEntity = marketingNoticeSettingDAO.queryByEa(noticeEntity.getFsEa());
        //默认发fs-app端和企微小程序
        if(noticeSettingEntity == null){
            //发送fs-app
            boolean result = fsMessageManager.sendOpenMessage(appId, noticeEntity.getFsEa(), new ArrayList<>(allUserIds), title, description, paramMap);
            if (result) {
                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FINISHED.getStatus()));
            } else {
                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FAIL.getStatus()));
            }
            //发企微小程序
            QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(noticeEntity.getFsEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(noticeEntity.getFsEa()));
            if (qywxMiniappConfigEntity == null) {
                // 发送企业微信消息,企业微信CRM营销频道
                Result<String> accountResult = qyweixinAccountBindManager.fsEaToOutEa( noticeEntity.getFsEa());
                // 判断当前企业是否有开通企业微信
                if (accountResult != null && accountResult.isSuccess() && StringUtils.isNotBlank(accountResult.getData())) {
                    boolean sendQywxMsgRet = sendQywxMessage(noticeEntity, marketingActivityId, apath, new ArrayList<>(allUserIds));
                    if (!sendQywxMsgRet) {
                        log.warn("sendEnterpriseSpreadNotice send qywx message failed marketingActivityId:{}", marketingActivityId);
                    }
                } else {
                    log.info("sendEnterpriseSpreadNotice accountResult is null ea:{}, accountResult:{}", noticeEntity.getFsEa(), accountResult);
                }
            }else {
                //企业微信小程序消息
                groupSendMessageManager.sendQywxSpreadMiniAppMessage(noticeEntity.getFsEa(), new ArrayList<>(allUserIds), noticeEntity.getTitle(),
                        noticeEntity.getDescription(), noticeEntity.getStartTime(), noticeEntity.getContentType(), noticeEntity.getContent(), marketingActivityId,finalMultiple);
            }
        }else {
            if(!Strings.isNullOrEmpty(noticeSettingEntity.getNoticeType())){
                List<String> stringList = JSON.parseArray(noticeSettingEntity.getNoticeType(), String.class);
                //推广消息到fs-app
                boolean flag = true;
                if(stringList.contains("app")){
                    flag = fsMessageManager.sendOpenMessage(appId, noticeEntity.getFsEa(), new ArrayList<>(allUserIds), title, description, paramMap);
                }
                if(stringList.contains("qywx")){
                    QywxMiniappConfigEntity qywxMiniappConfigEntity = qywxMiniappConfigDAO.getByEa(noticeEntity.getFsEa(), wechatAccountManager.getNotEmptyWxAppIdByEa(noticeEntity.getFsEa()));
                    if (qywxMiniappConfigEntity == null) {
                        // 发送企业微信消息,企业微信CRM营销频道
                        Result<String> accountResult = qyweixinAccountBindManager.fsEaToOutEa( noticeEntity.getFsEa());
                        // 判断当前企业是否有开通企业微信
                        if (accountResult != null && accountResult.isSuccess() && StringUtils.isNotBlank(accountResult.getData())) {
                            boolean sendQywxMsgRet = sendQywxMessage(noticeEntity, marketingActivityId, apath, new ArrayList<>(allUserIds));
                            if (!sendQywxMsgRet) {
                                log.warn("sendEnterpriseSpreadNotice send qywx message failed marketingActivityId:{}", marketingActivityId);
                            }
                        } else {
                            log.info("sendEnterpriseSpreadNotice accountResult is null ea:{}, accountResult:{}", noticeEntity.getFsEa(), accountResult);
                        }
                    }else {
                        //企业微信小程序消息
                        groupSendMessageManager.sendQywxSpreadMiniAppMessage(noticeEntity.getFsEa(), new ArrayList<>(allUserIds), noticeEntity.getTitle(),
                                noticeEntity.getDescription(), noticeEntity.getStartTime(), noticeEntity.getContentType(), noticeEntity.getContent(), marketingActivityId,finalMultiple);
                    }
                }
                //只支持代开发，不支持自建
                if(stringList.contains("qywx_h5")){
                    //代开发消息目标推送
                    groupSendMessageManager.sendQywxAgentAppMessage(noticeEntity.getId(),noticeEntity.getFsEa(), new ArrayList<>(allUserIds), noticeEntity.getTitle(),
                            noticeEntity.getDescription(), noticeEntity.getStartTime(), noticeEntity.getContentType(), noticeEntity.getContent(), marketingActivityId,imageUrl,finalMultiple);
                }
                if (flag) {
                    noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
                    marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FINISHED.getStatus()));
                } else {
                    noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.FAIL.getStatus(), new Date());
                    marketingActivityManager.updateMarketingActivityStatus(noticeEntity.getFsEa(), marketingActivityId,String.valueOf(SendStatusEnum.FAIL.getStatus()));
                }
            }
        }

        //发模板通知
        YxzsAllSpreadWxTemplateNoticeSettingEntity entity = yxzsAllSpreadWxTemplateNoticeSettingDAO.getByEa(noticeEntity.getFsEa());
        if(entity!=null && entity.getOpenWxNotice() !=null && entity.getOpenWxNotice()==1 && entity.getWxTemplateMsg()!=null && StringUtils.isNotEmpty(entity.getWxAppId())){
            yxzsNoticeSendManager.sendAllSpreardWxTemplateMsg(title, description, noticeEntity, marketingActivityId, finalMultiple, entity,Lists.newArrayList(allUserIds), String.valueOf(paramMap.get("marketingEvenId")));
        }
    }



    private void sendImageTextNoticeAgainByEmployees(String noticeId, String marketingActivityId, List<Integer> userIds){
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null){
            log.error("sendImageTextNoticeAgainByEmployees failed noticeEntity not exist noticeId:{} marketingActivityId:{}", noticeEntity, marketingActivityId);
            return;
        }

        Integer noticeContentType = noticeEntity.getContentType();
        String contentTitle = getPromotionContent(noticeEntity.getContentType(), noticeEntity.getContent()).getTitle();
        try{
            String title = noticeEntity.getTitle();
            String description = noticeEntity.getDescription();
            String noticeEntityId = noticeEntity.getId();
            String url;
            boolean multiple = false;
            String materialInfoList = noticeEntity.getMaterialInfoList();
            if (StringUtils.isNotBlank(materialInfoList)) {
                List<AddMaterialsArg.MaterialInfo> materialInfos = gson.fromJson(materialInfoList, new TypeToken<List<AddMaterialsArg.MaterialInfo>>() {
                }.getType());
                if (CollectionUtils.isNotEmpty(materialInfos) && materialInfos.size() > 0) {
                    multiple = true;
                }
            }
            if (multiple) {
                url = host + "/ec/kemai/release/notice.html?_hash=notice&?marketingActivityId="+marketingActivityId+"&noticeId="+noticeId+ "&useNoticeCenterPage=true";
            } else if (Objects.equals(noticeEntity.getContentType(), NoticeContentTypeEnum.EMAIL_MATERIAL.getType())) {
                // 邮件物料推广
                String fsEa = noticeEntity.getFsEa();
                MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndMarketingActivityId(fsEa, marketingActivityId);
                String marketingEventId = configEntity.getMarketingEventId() == null ? "" : configEntity.getMarketingEventId();
                url = host + "/proj/page/marketing/" + fsEa + "/#/mail-notice?noticeId=" + noticeEntityId + "&ea=" + fsEa + "&marketingEventId=" + marketingEventId + "&marketingActivityId=" + marketingActivityId;
            } else {
                final String encodePage = URLEncoder.encode("pages/noticeCenter/noticeCenter", "UTF-8");
                final String qrUrl = host + "/appmarketing/web/file/showQRCode?scene=" + noticeEntityId + "&page=" + encodePage + "&width=860&type=notice";
                final String encodeQRUrl = URLEncoder.encode(qrUrl, "UTF-8");
                if (marketingActivityId != null) {
                    url = host + "/ec/kemai/release/notice.html?_hash=notice&title=" + URLEncoder.encode(contentTitle, "UTF-8") + "&noticeId=" + noticeEntityId + "&qrUrl=" + encodeQRUrl
                            + "&useNoticeCenterPage=true" + "&contentType=" + noticeContentType + "&marketingActivityId=" + marketingActivityId;
                } else {
                    url = host + "/ec/kemai/release/notice.html?_hash=notice&title=" + URLEncoder.encode(contentTitle, "UTF-8") + "&noticeId=" + noticeEntityId + "&qrUrl=" + encodeQRUrl
                            + "&useNoticeCenterPage=true" + "&contentType=" + noticeContentType;
                }
            }

            log.info("sendImageTextNoticeAgainByEmployees url:{}", url);
            String imageUrl = "";
            String apath = null;
            apath = getCoverPathByNotice(noticeEntity);
            if (StringUtils.isBlank(apath)) {
                apath = imagetextDefaultCoverApath;
            }

            imageUrl = fileV2Manager.getSpliceUrl(noticeEntity.getFsEa(), apath);
            log.info("sendImageTextNoticeAgainByEmployees url:{} contentUr:{}", imageUrl, url);
            if (StringUtils.isBlank(imageUrl)) {
                log.error("NoticeService.sendImageTextNoticeAgainByEmployees getSpliceUrl failed, imageUrl is null, apath={", apath );
                return;
            }

            String buttonText = "立即去推广";
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("createTime", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
            paramMap.put("messageId", com.facishare.mankeep.common.util.UUIDUtil.getUUID());
            List<Map<String, Object>> imageTextListMap = new ArrayList<>();
            Map<String, Object> imageTextMap = new HashMap<>();
            imageTextMap.put("summary", description);
            imageTextMap.put("contentUrl", url);
            imageTextMap.put("imageUrl", imageUrl);
            imageTextMap.put("title", "您有一个新的推广任务");
            imageTextMap.put("internationalTitle", "qx.ot.mark.promotion_task_notification");
            imageTextMap.put("buttonText", buttonText);
            imageTextMap.put("internationalButtonText", "qx.ot.mark.promote_immediately");
            imageTextMap.put("buttonUrl", url);
            imageTextMap.put("contentType", "1");
            imageTextMap.put("messageType", "1");
            imageTextMap.put("contentTitle", title);
            imageTextMap.put("description", description);
            List<Map<String, Object>> contentListMap = new ArrayList<>();
            Map<String, Object> conductMap = new HashMap<>();
            conductMap.put("宣传语", description);
            contentListMap.add(conductMap);
            Map<String, Object> timeMap = new HashMap<>();
            timeMap.put("推广时间", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
            contentListMap.add(timeMap);
            imageTextMap.put("contentListMap", contentListMap);
            List<Map<String, Object>> i18ncontentListMap = new ArrayList<>();
            Map<String, Object> i18nconductMap = new HashMap<>();
            i18nconductMap.put("qx.ot.mark.promotion_slogan", description);
            i18ncontentListMap.add(i18nconductMap);
            Map<String, Object> i18ntimeMap = new HashMap<>();
            i18ntimeMap.put("qx.ot.mark.promotion_time", DateFormatUtils.format(Instant.now().toEpochMilli(), "MM-dd"));
            i18ncontentListMap.add(i18ntimeMap);
            imageTextMap.put("internationalContentListMap", i18ncontentListMap);
            if (multiple) {
                NoticeManager.ParamResult paramResult = new NoticeManager.ParamResult();
                String sb = "ava://marketing_app/pkgs/pkg-spread/pages/multi-material/multi-material" +
                        "?marketingActivityId=" +
                        marketingActivityId;
                paramResult.setUrl(sb);
                imageTextMap.put("customParamJson", GsonUtil.toJson(paramResult));
            } else if (Objects.equals(noticeEntity.getContentType(), NoticeContentTypeEnum.EMAIL_MATERIAL.getType())) {
                // 邮件物料特殊处理，只能在pc端访问，统一下发pc端地址
                imageTextMap.put("customParamJson", url);
            } else {
                imageTextMap.put("customParamJson", noticeManager.urlDispatcher(noticeEntity.getFsEa(), noticeEntity.getContentType(), noticeEntity.getContent(), noticeEntity.getId(), marketingActivityId));
            }
            if (noticeEntity.getStartTime() != null && noticeEntity.getEndTime() != null) {
                String[] strStart = new SimpleDateFormat("MM-dd").format(noticeEntity.getStartTime()).split("-");
                String[] strEnd = new SimpleDateFormat("MM-dd").format(noticeEntity.getEndTime()).split("-");
                Integer startMonth = Integer.parseInt(strStart[0]);
                Integer startDay = Integer.parseInt(strStart[1]);
                Integer endMonth = Integer.parseInt(strEnd[0]);
                Integer endDay = Integer.parseInt(strEnd[1]);
                String limitDate = new StringBuilder().append(startMonth).append(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972)).append(startDay).append(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1)).append(I18nUtil.get(I18nKeyEnum.MARK_CONFERENCE_CONFERENCESERVICEIMPL_3965)).append(endMonth).append(I18nUtil.get(I18nKeyEnum.MARK_ADVERTISER_ADBIGSCREENMANAGER_972)).append(endDay).append(I18nUtil.get(I18nKeyEnum.MARK_QYWX_GROUPSENDMESSAGEMANAGER_2774_1))
                        .toString();
                imageTextMap.put("limitDate", limitDate);
            }

            imageTextListMap.add(imageTextMap);
            paramMap.put("imageTextList", imageTextListMap);
            log.info("NoticeService.sendEnterpriseSpreadNotice paramMap={}", paramMap);
            sendNoticeType(Sets.newHashSet(userIds), title, description, noticeEntityId, apath, paramMap, noticeEntity, marketingActivityId, imageUrl,multiple);
        } catch (Exception e) {
            log.error("Error: fail sendMq notice ! noticeId:{}", noticeId, e);
        }
    }

    public String getCoverPathByNotice(NoticeEntity noticeEntity) {
        String apath = null;
        if (StringUtils.isNotBlank(noticeEntity.getCoverApath()) && (noticeEntity.getCoverApath().startsWith("A_") || noticeEntity.getCoverApath().startsWith("C_"))) {
            apath = noticeEntity.getCoverApath();
        } else {
            ObjectTypeEnum objectTypeEnum = ObjectTypeEnum.fromContentType(noticeEntity.getContentType());
            if (null != objectTypeEnum) {
                PhotoEntity photoEntity = null;
                if (objectTypeEnum == ObjectTypeEnum.ARTICLE) {
                    photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), noticeEntity.getContent(),noticeEntity.getFsEa());
                } else if (objectTypeEnum == ObjectTypeEnum.ACTIVITY) {
                    photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), noticeEntity.getContent(),noticeEntity.getFsEa());
                } else if (objectTypeEnum == ObjectTypeEnum.PRODUCT) {
                    photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), noticeEntity.getContent(),noticeEntity.getFsEa());
                } else if (objectTypeEnum == ObjectTypeEnum.QR_POSTER) {
                    QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(noticeEntity.getContent());
                    if (null != qrPosterEntity) {
                        apath = qrPosterEntity.getApath();
                    }
                } else if (objectTypeEnum == ObjectTypeEnum.HEXAGON_SITE) {
                    HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(noticeEntity.getContent());
                    if (null != hexagonSiteEntity) {
                        // 判断是否为会议微页面
                        ActivityEntity activityEntity = conferenceDAO.getActivityByDetailSiteId(noticeEntity.getContent());
                        if (activityEntity != null) {
                            photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), noticeEntity.getContent(),noticeEntity.getFsEa());
                        } else {
                            Map<String, String> coverApathMap = new HashMap<>();
                            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(Arrays.asList(noticeEntity.getContent()));
                            if (null != hexagonSiteCoverListDTOList && CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                                for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteCoverListDTOList) {
                                    coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
                                }
                            }
                            apath = coverApathMap.get(hexagonSiteEntity.getId());
                        }
                    }
                }
                if (null != photoEntity) {
                    apath = photoEntity.getPath();
                }
            }
        }
        return apath;
    }

    //发送企业微信消息
    private boolean sendQywxMessage(NoticeEntity noticeEntity, String marketingActivityId, String coverPath, List<Integer> userIds){
        NoticeContentTypeEnum noticeContentTypeEnum = NoticeContentTypeEnum.fromType(noticeEntity.getContentType());
        Integer objectType = noticeContentTypeEnum.toObjectType();
        String objectId = noticeEntity.getContent();
        String qywxUrl =  Base64.encodeBase64String((host + "/hcrm/wechat/function/marketdetail?" + "objectType=" + objectType + "&id=" + objectId + "&marketingActivityId=" + marketingActivityId).getBytes());
        String messageUrl = openHost + "/qyweixin/doFunction?param=" + qywxUrl + "&appID=" + qywxCrmAppid;
        return fsMessageManager.sendQywxMessage(noticeEntity.getFsEa(), noticeEntity.getTitle(), noticeEntity.getDescription(), userIds,
            messageUrl, coverPath);
    }

    /**
     * 发送通知crm营销活动更改状态
     */
    private void doNoticeStatusSenderMq(String ea, Integer status, String taskId) {
        try {
            NoticeStatusVO noticeStatusVO = new NoticeStatusVO();
            noticeStatusVO.setEa(ea);
            noticeStatusVO.setStatus(status);
            noticeStatusVO.setTaskId(taskId);
            noticeStatusSender.send(noticeStatusVO);
            log.info("doNoticeStatusSenderMq noticeStatusVO,{}", noticeStatusVO);
        } catch (Exception e) {
            log.error("doSendGroupSmsStatusMq error:{}", e);
        }
    }

    private PromotionContentData getPromotionContent(Integer contentType, String content) {
        if (contentType == NoticeContentTypeEnum.ARTICLE.getType()) {
            ArticleEntity articleEntity = articleDAO.queryArticleDetail(content);
            if (articleEntity == null) {
                return null;
            }
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId(),articleEntity.getFsEa());
            PromotionContentData promotionContentData = new PromotionContentData();

            if (photoEntity != null) {
                String photoPath = StringUtils.isNotBlank(photoEntity.getThumbnailPath()) ? photoEntity.getThumbnailPath() : photoEntity.getPath();
                if (StringUtils.isNotBlank(photoPath)){
                    promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(photoPath, articleEntity.getFsEa(), false));
                }
            }

            promotionContentData.setTitle(articleEntity.getTitle());
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.ACTIVITY.getType()) {
            ActivityEntity activityEntity = activityDAO.getById(content);
            if (activityEntity == null) {
                return null;
            }
            PromotionContentData promotionContentData = new PromotionContentData();
           /* PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId());
            if (photoEntity != null){
                String photoPath = photoEntity.getThumbnailPath();
                if (photoPath == null || photoPath.trim().length() == 0){
                    photoPath = photoEntity.getPath();
                }
                promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(photoPath, null, false));
            }else {
                promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(defaultConferenceCover, activityEntity.getEa(), false));
            }
            promotionContentData.setTitle(activityEntity.getTitle());*/
            buildConferencePromotionContent(activityEntity, promotionContentData);
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.PRODUCT.getType()) {
            ProductEntity productEntity = productDAO.queryProductDetail(content);
            if (productEntity == null) {
                return null;
            }
            PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), productEntity.getId(),productEntity.getFsEa());
            PromotionContentData promotionContentData = new PromotionContentData();
            String photoPath = photoEntity.getThumbnailPath();
            if (photoPath == null || photoPath.trim().length() == 0){
                photoPath = photoEntity.getPath();
            }
            promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(photoPath, productEntity.getFsEa(), false));
            promotionContentData.setTitle(productEntity.getName());
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.QR_POSTER.getType()) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(content);
            if (qrPosterEntity == null) {
                return null;
            }
            PromotionContentData promotionContentData = new PromotionContentData();
            promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(qrPosterEntity.getThumbnailApath(), qrPosterEntity.getEa(), false));
            promotionContentData.setTitle(qrPosterEntity.getTitle());
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.HEXAGON.getType()) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(content);
            if (hexagonSiteEntity == null) {
                return null;
            }
            // 判断是否为会议微页面
            ActivityEntity activityEntity = conferenceDAO.getActivityByDetailSiteId(content);
            if (activityEntity != null) {
                PromotionContentData promotionContentData = new PromotionContentData();
                buildConferencePromotionContent(activityEntity, promotionContentData);
                return promotionContentData;
            }
            Map<String, String> coverApathMap = new HashMap<>();
            List<String> apathList = new ArrayList<>();
            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(Arrays.asList(content));
            if (null != hexagonSiteCoverListDTOList && CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteCoverListDTOList) {
                    apathList.add(hexagonSiteListDTO.getSharePicH5Apath());
                    coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
                }
            }

            Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, hexagonSiteEntity.getEa(), false);

            PromotionContentData promotionContentData = new PromotionContentData();
            String apath = coverApathMap.get(hexagonSiteEntity.getId());
            if (StringUtils.isNotBlank(apath)) {
                promotionContentData.setThumbnail(coverUrlMap.get(apath));
            }
            promotionContentData.setTitle(hexagonSiteEntity.getName());
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.OUT_LINK.getType()) {
            PromotionContentData promotionContentData = new PromotionContentData();
            OutLinkEntity entity = null;
            List<OutLinkEntity> entities = outLinkDAO.getByIds(Collections.singletonList(content));
            if(entities != null && !entities.isEmpty()) {
                entity = entities.get(0);
            }
            if (entity == null) {
                return null;
            }
            promotionContentData.setThumbnail(entity.getCover());
            promotionContentData.setTitle(entity.getName());
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.EMAIL_MATERIAL.getType()) {
            PromotionContentData promotionContentData = new PromotionContentData();
            EmailMaterialEntity entity = null;
            List<EmailMaterialEntity> entities = emailMaterialDao.getByIds(Collections.singletonList(content));
            if(entities != null && !entities.isEmpty()) {
                entity = entities.get(0);
            }
            if (entity == null) {
                return null;
            }
            //promotionContentData.setThumbnail(entity.getCover());
            promotionContentData.setTitle(entity.getTitle());
            return promotionContentData;
        } else if (contentType == NoticeContentTypeEnum.PICTURE.getType()) {
            PromotionContentData promotionContentData = new PromotionContentData();
            PhotoSelectorEntity photoSelectorEntity = photoSelectorDAO.getById(content);
            if (photoSelectorEntity == null) {
                PhotoLibraryEntity libraryEntity = photoLibraryDAO.getById(content);
                if (libraryEntity == null) {
                    return null;
                }
                promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(libraryEntity.getPhotoPath(),libraryEntity.getEa(),false));
                promotionContentData.setTitle(libraryEntity.getPhotoName());
                return promotionContentData;
            }
            promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(photoSelectorEntity.getPhotoPath(),photoSelectorEntity.getEa(),false));
            promotionContentData.setTitle(photoSelectorEntity.getPhotoName());
            return promotionContentData;
        }
        return null;
    }

    /**
     * 分组批量获取推广内容
     *
     * @param contentGroupMap
     * @return
     */
    private Map<String, PromotionContentData> batchGetPromotionContent(String ea,Map<Integer, List<String>> contentGroupMap) {
        Map<String, PromotionContentData> res = Maps.newHashMap();
        if (MapUtils.isEmpty(contentGroupMap)) {
            return res;
        }
        Map<String, String> contentTitleMap = Maps.newHashMap();
        Map<String, String> contentPhotoPathMap = Maps.newHashMap();
        contentGroupMap.forEach((contentType, contents) -> {
            if (CollectionUtils.isNotEmpty(contents)) {
                if (contentType == NoticeContentTypeEnum.ARTICLE.getType()) {
                    // content:title
                    List<ArticleEntity> articleEntities = articleDAO.getNameByIds(contents);
                    if (CollectionUtils.isNotEmpty(articleEntities)) {
                        contentTitleMap.putAll(articleEntities.stream().collect(Collectors.toMap(ArticleEntity::getId, ArticleEntity::getTitle)));
                    }
                    // content:photoPath
                    Map<String, PhotoEntity> contentPhotoEntity = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), contents);
                    if (MapUtils.isNotEmpty(contentPhotoEntity)) {
                        contentPhotoPathMap.putAll(contentPhotoEntity.entrySet().stream()
                                .collect(Collectors.toMap(Map.Entry::getKey,
                                        e -> StringUtils.isNotBlank(e.getValue().getThumbnailPath()) ? e.getValue().getThumbnailPath() : e.getValue().getPath())));
                    }
                } else if (contentType == NoticeContentTypeEnum.ACTIVITY.getType()) {
                    // content:title
                    List<ActivityEntity> activityEntities = activityDAO.getByIds(contents);
                    if (CollectionUtils.isNotEmpty(activityEntities)) {
                        contentTitleMap.putAll(activityEntities.stream().collect(Collectors.toMap(ActivityEntity::getId, ActivityEntity::getTitle)));
                    }
                    // content:photoPath/defaultConferenceCover
                    Map<String, PhotoEntity> contentPhotoEntity = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), contents);
                    for (String content : contents) {
                        PhotoEntity photoEntity = contentPhotoEntity.get(content);
                        if (photoEntity != null) {
                            String photoPath = photoEntity.getThumbnailPath();
                            if (photoPath == null || photoPath.trim().isEmpty()) {
                                photoPath = photoEntity.getPath();
                            }
                            contentPhotoPathMap.put(content, photoPath);
                        } else {
                            contentPhotoPathMap.put(content, defaultConferenceCover);
                        }
                    }
                } else if (contentType == NoticeContentTypeEnum.PRODUCT.getType()) {
                    // content:title
                    List<ProductEntity> productEntities = productDAO.getByIds(contents);
                    if (CollectionUtils.isNotEmpty(productEntities)) {
                        contentTitleMap.putAll(productEntities.stream().collect(Collectors.toMap(ProductEntity::getId, ProductEntity::getName)));
                    }
                    // content:photoPath
                    Map<String, PhotoEntity> contentPhotoEntity = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), contents);
                    if (MapUtils.isNotEmpty(contentPhotoEntity)) {
                        contentPhotoPathMap.putAll(contentPhotoEntity.entrySet().stream()
                                .collect(Collectors.toMap(Map.Entry::getKey,
                                        e -> StringUtils.isNotBlank(e.getValue().getThumbnailPath()) ? e.getValue().getThumbnailPath() : e.getValue().getPath())));
                    }
                } else if (contentType == NoticeContentTypeEnum.QR_POSTER.getType()) {
                    // content:title
                    // content:photoPath
                    List<QRPosterEntity> qrPosterEntities = qrPosterDAO.getByIds(contents);
                    if (CollectionUtils.isNotEmpty(qrPosterEntities)) {
                        contentTitleMap.putAll(qrPosterEntities.stream().collect(Collectors.toMap(QRPosterEntity::getId, QRPosterEntity::getTitle)));
                        contentPhotoPathMap.putAll(qrPosterEntities.stream().collect(Collectors.toMap(QRPosterEntity::getId, QRPosterEntity::getThumbnailApath)));
                    }
                } else if (contentType == NoticeContentTypeEnum.HEXAGON.getType()) {
                    List<String> tempContent = Lists.newArrayList(contents);
                    List<ActivityEntity> activityEntities = conferenceDAO.getActivityByDetailSiteIds(tempContent);
                    // 判断是否为会议微页面
                    if (CollectionUtils.isNotEmpty(activityEntities)) {
                        // content:title
                        contentTitleMap.putAll(activityEntities.stream().collect(Collectors.toMap(ActivityEntity::getId, ActivityEntity::getTitle)));
                        // content:photoPath
                        Map<String, String> contentActivityIdMap = activityEntities.stream().collect(Collectors.toMap(ActivityEntity::getActivityDetailSiteId, ActivityEntity::getId));
                        Map<String, PhotoEntity> contentPhotoEntity = photoManager.batchQueryPhotoByTargetIds(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), Lists.newArrayList(contentActivityIdMap.values()));
                        for (String content : contentActivityIdMap.keySet()) {
                            PhotoEntity photoEntity = contentPhotoEntity.get(content);
                            if (photoEntity != null) {
                                String photoPath = photoEntity.getThumbnailPath();
                                if (photoPath == null || photoPath.trim().isEmpty()) {
                                    photoPath = photoEntity.getPath();
                                }
                                contentPhotoPathMap.put(content, photoPath);
                            } else {
                                contentPhotoPathMap.put(content, defaultConferenceCover);
                            }
                        }
                        tempContent.removeAll(contentActivityIdMap.keySet());
                    }
                    if (CollectionUtils.isNotEmpty(tempContent)) {
                        // content:title
                        List<HexagonSiteEntity> hexagonSiteEntities = hexagonSiteDAO.getByIds(tempContent);
                        if (CollectionUtils.isNotEmpty(hexagonSiteEntities)) {
                            contentTitleMap.putAll(hexagonSiteEntities.stream().collect(Collectors.toMap(HexagonSiteEntity::getId, HexagonSiteEntity::getName)));
                        }
                        // content:photoPath
                        List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(tempContent);
                        if (CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                            contentPhotoPathMap.putAll(hexagonSiteCoverListDTOList.stream().collect(Collectors.toMap(HexagonSiteListDTO::getHexagonSiteId, HexagonSiteListDTO::getSharePicH5Apath)));
                        }
                    }
                } else if (contentType == NoticeContentTypeEnum.OUT_LINK.getType()) {
                    // content:title
                    // content:photoPath
                    List<OutLinkEntity> entities = outLinkDAO.getByIds(contents);
                    if (CollectionUtils.isNotEmpty(entities)) {
                        contentTitleMap.putAll(entities.stream().collect(Collectors.toMap(OutLinkEntity::getId, OutLinkEntity::getName)));
                        contentPhotoPathMap.putAll(entities.stream().collect(Collectors.toMap(OutLinkEntity::getId, OutLinkEntity::getCover)));
                    }
                } else if (contentType == NoticeContentTypeEnum.EMAIL_MATERIAL.getType()) {
                    // content:title
                    // content:photoPath
                    List<EmailMaterialEntity> entities = emailMaterialDao.getByIds(contents);
                    if (CollectionUtils.isNotEmpty(entities)) {
                        contentTitleMap.putAll(entities.stream().collect(Collectors.toMap(EmailMaterialEntity::getId, EmailMaterialEntity::getTitle)));
                    }
                } else if (contentType == NoticeContentTypeEnum.PICTURE.getType()) {
                    // content:title
                    // content:photoPath
                    List<String> tempContent = Lists.newArrayList(contents);
                    List<PhotoSelectorEntity> photoSelectorEntities = photoSelectorDAO.getByIdList(tempContent);
                    if (CollectionUtils.isNotEmpty(photoSelectorEntities)) {
                        contentTitleMap.putAll(photoSelectorEntities.stream().collect(Collectors.toMap(PhotoSelectorEntity::getId, PhotoSelectorEntity::getPhotoName)));
                        contentPhotoPathMap.putAll(photoSelectorEntities.stream().collect(Collectors.toMap(PhotoSelectorEntity::getId, PhotoSelectorEntity::getPhotoPath)));
                        tempContent.removeAll(photoSelectorEntities.stream().map(PhotoSelectorEntity::getId).collect(Collectors.toList()));
                    }
                    if (CollectionUtils.isNotEmpty(tempContent)) {
                        List<PhotoLibraryEntity> libraryEntities = photoLibraryDAO.getByIdList(tempContent);
                        if (CollectionUtils.isNotEmpty(libraryEntities)) {
                            contentTitleMap.putAll(libraryEntities.stream().collect(Collectors.toMap(PhotoLibraryEntity::getId, PhotoLibraryEntity::getPhotoName)));
                            contentPhotoPathMap.putAll(libraryEntities.stream().collect(Collectors.toMap(PhotoLibraryEntity::getId, PhotoLibraryEntity::getPhotoPath)));
                        }
                    }
                }
            }
        });
        Map<String, String> urls = fileV2Manager.batchGetUrlByPath(Lists.newArrayList(contentPhotoPathMap.values()), ea, false);
        List<String> allContent = contentGroupMap.values().stream().filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
        for (String content : allContent) {
            if (StringUtils.isNotBlank(contentTitleMap.get(content))) {
                PromotionContentData promotionContentData = new PromotionContentData();
                promotionContentData.setTitle(contentTitleMap.get(content));
                String thumbnailPath = contentPhotoPathMap.get(content);
                if (StringUtils.isNotBlank(thumbnailPath)) {
                    promotionContentData.setThumbnail(urls.get(thumbnailPath));
                }
                res.put(content, promotionContentData);
            }
        }
        return res;
    }

    private void buildConferencePromotionContent(ActivityEntity activityEntity, PromotionContentData promotionContentData) {
        PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId(),activityEntity.getEa());
        if (photoEntity != null){
            String photoPath = photoEntity.getThumbnailPath();
            if (photoPath == null || photoPath.trim().length() == 0){
                photoPath = photoEntity.getPath();
            }
            promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(photoPath, activityEntity.getEa(), false));
        }else {
            promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(defaultConferenceCover, activityEntity.getEa(), false));
        }
        promotionContentData.setTitle(activityEntity.getTitle());
    }

    @Override
    public Result<PageResult<NoticeResult>> listNotices(String ea, Integer fsUserId, ListNoticeArg vo) {
        List<NoticeResult> noticeResultList = Lists.newArrayList();
        PageResult<NoticeResult> pageResult = new PageResult<>();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(noticeResultList);

        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (StringUtils.isNotEmpty(vo.getTitle())){
            marketingActivityArg.setName(vo.getTitle());
        }
        int  spreadType = vo.getSpreadType() == null ? MarketingActivitySpreadTypeEnum.ALL_SPREAD.getSpreadType() : vo.getSpreadType();
        MarketingActivitySpreadTypeEnum marketingActivitySpreadTypeEnum = MarketingActivitySpreadTypeEnum.getBySpreadType(spreadType);
        if (marketingActivitySpreadTypeEnum == null) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        marketingActivityArg.setSpreadType(spreadType);
        //从营销活动对象获取数据
        marketingActivityArg.setPageSize(vo.getPageSize());
        marketingActivityArg.setPageNumber((vo.getPageNum() - 1) * vo.getPageSize());
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageMarketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (pageMarketingActivityList == null || CollectionUtils.isEmpty(pageMarketingActivityList.getDataList())){
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(pageMarketingActivityList.getTotal());
        List<ObjectData> marketingActivityList = pageMarketingActivityList.getDataList();
        List<String> marketingActivityIds = marketingActivityList.stream().map(ObjectData::getId).collect(Collectors.toList());
        Map<String, ObjectData> idToMarketingActivityDataMap = marketingActivityList.stream().collect(Collectors.toMap(ObjectData::getId, Function.identity(), (v1,v2)->v2));
        List<String> marketingEventIds = marketingActivityList.stream().map(objectData -> objectData.getString("marketing_event_id")).collect(Collectors.toList());
        List<MarketingEventData> eventDataList = null;
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            eventDataList = marketingEventManager.listMarketingEventData(ea, fsUserId, marketingEventIds);
        }
        Map<String, MarketingEventData> marketingEventDataMap = null;
        if (CollectionUtils.isNotEmpty(eventDataList)) {
            marketingEventDataMap = eventDataList.stream().collect(Collectors.toMap(MarketingEventData::getId, Function.identity(), (k1, k2) -> k1));
        }
        Map<String, String> activityEntityMap = null;
        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIds);
        if (CollectionUtils.isNotEmpty(activityEntityList)){
            activityEntityMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, ActivityEntity::getId, (k1, k2)->k1));
        }

        List<NoticeEntity> noticeEntityList = noticeDAO.listNoticesByMarketingActivityIds(ea, vo.getTitle(), marketingActivityIds);
        if (CollectionUtils.isEmpty(noticeEntityList)) {
            return Result.newSuccess(pageResult);
        }
        List<String> noticeIds = noticeEntityList.stream().map(NoticeEntity::getId).collect(Collectors.toList());
        List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigList = marketingActivityExternalConfigDao.getByAssociateIds(noticeIds);
        Map<String, String> noticeIdToMarketingActivityIdMap = marketingActivityExternalConfigList.stream()
                .collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getAssociateId, MarketingActivityExternalConfigEntity::getMarketingActivityId));

        //获取统计结果，获取线索数，访问人次，转发人次，员工推广次数
        List<MarketingActivityStatisticEntity> countDataList;
        Map<String, Integer> marketingActivityClueNum = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(marketingActivityIds)) {
            countDataList = marketingActivityStatisticDao.countDataByMarketingActivityIds(ea, marketingActivityIds);
            // 线索从表单获取
            marketingActivityClueNum = customizeFormClueManager.batchCountClueNumByMarketingActivityIds(ea, marketingActivityIds, false,0);
        } else {
            countDataList = new ArrayList<>();
        }
        for (MarketingActivityStatisticEntity marketingActivityStatisticEntity : countDataList) {
            marketingActivityStatisticEntity.setLeadAccumulationCount(marketingActivityClueNum.get(marketingActivityStatisticEntity.getMarketingActivityId()));
        }
        //将统计结果转为营销活动ID对应的Map
        Map<String, MarketingActivityStatisticEntity> marketingActivityIdToCountResultMap = countDataList.stream()
                .collect(Collectors.toMap(MarketingActivityStatisticEntity::getMarketingActivityId, Function.identity()));

        List<Integer> userIds = noticeEntityList.stream().map(NoticeEntity::getFsUserId).collect(Collectors.toList());
        // 获取操作人姓名
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);
        BatchObjectUserMarketingStatisticsArg statisticsArg = new BatchObjectUserMarketingStatisticsArg();
        statisticsArg.setEa(ea);
        statisticsArg.setUserId(fsUserId);
        statisticsArg.setMarketingActivityIdList(marketingActivityIds);
        Result<List<ObjectUserMarketingStatisticsResult>> statisticsResult = statisticService.batchGetObjectMarketingActivityStatistics(statisticsArg);
        Map<String, ObjectUserMarketingStatisticsResult> marketingActivityIdToStatisticsMap = Maps.newHashMap();
        if (statisticsResult.isSuccess() && CollectionUtils.isNotEmpty(statisticsResult.getData())) {
            statisticsResult.getData().forEach(e -> marketingActivityIdToStatisticsMap.put(e.getMarketingActivityId(), e));
        }
        Map<Integer, List<String>> contentGroups = noticeEntityList.stream().collect(Collectors.groupingBy(NoticeEntity::getContentType, Collectors.mapping(NoticeEntity::getContent, Collectors.toList())));
        Map<String, PromotionContentData> promotionContentDataMap = batchGetPromotionContent(ea,contentGroups);
        for (NoticeEntity entity : noticeEntityList) {
            String marketingActivityId = noticeIdToMarketingActivityIdMap.get(entity.getId());
            ObjectData objectData = idToMarketingActivityDataMap.get(marketingActivityId);

            MarketingActivityStatisticEntity countData = marketingActivityIdToCountResultMap.get(marketingActivityId);
            NoticeResult noticeResult = BeanUtil.copy(entity, NoticeResult.class);
            noticeResult.setStartTime(DateUtil.getTimeStamp(entity.getStartTime()));
            noticeResult.setEndTime(DateUtil.getTimeStamp(entity.getEndTime()));
            noticeResult.setCreateTime(DateUtil.getTimeStamp(entity.getCreateTime()));
            noticeResult.setUsername(fsEmployeeMsgMap.get(entity.getFsUserId()) != null ? fsEmployeeMsgMap.get(entity.getFsUserId()).getName() : entity.getUsername());


            if (NoticeSendTypeEnum.TIMING.getType() == entity.getSendType()) {
                noticeResult.setSendTime(DateUtil.getTimeStamp(entity.getTimingTime()));
            } else {
                noticeResult.setSendTime(DateUtil.getTimeStamp(entity.getSendTime()));
            }
            if (objectData != null) {
                //从营销活动信息中取 关联的市场活动信息
                MarketingActivityData marketingActivityData = MarketingActivityData.wrap(objectData);
                noticeResult.setMarketingEventId(marketingActivityData.getMarketingEventId());
                noticeResult.setMarketingEventName(marketingActivityData.getMarketingEventIdName());
                if (marketingEventDataMap != null && marketingEventDataMap.get(marketingActivityData.getMarketingEventId()) != null) {
                    noticeResult.setMarketingEventType(marketingEventDataMap.get(marketingActivityData.getMarketingEventId()).getEventType());
                    if (activityEntityMap != null && activityEntityMap.get(marketingActivityData.getMarketingEventId()) != null) {
                        noticeResult.setMarketingObjectId(activityEntityMap.get(marketingActivityData.getMarketingEventId()));
                    }
                }
                noticeResult.setStatus(Integer.parseInt((String)objectData.getString("status")));
                noticeResult.setAuditStatus(objectData.getLifeStatus());
            }


            if (entity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus() && entity.getSendType() == NoticeSendTypeEnum.TIMING.getType()){
                noticeResult.setSendCancelable(true);
            }else{
                noticeResult.setSendCancelable(false);
            }

            // 推广状态 是否可撤回
            this.setStatusAndButton(entity, noticeResult);

            PromotionContentData promotionContentData = promotionContentDataMap.get(entity.getContent());
            if (StringUtils.isNotBlank(entity.getMaterialInfoList()) && promotionContentData == null) {
                List<AddMarketingActivityArg.MaterialInfo> materialInfos = gson.fromJson(entity.getMaterialInfoList(),new TypeToken<List<AddMarketingActivityArg.MaterialInfo>>(){}.getType());
                AddMarketingActivityArg.MaterialInfo materialInfo = materialInfos.get(0);
                promotionContentData.setThumbnail(fileV2Manager.getUrlByPath(materialInfo.getCoverPath(),ea,false));
                //promotionContentData.setTitle(materialInfo.getObjectId());
            }
            noticeResult.setPromotionContent(promotionContentData);
//            ObjectUserMarketingStatisticsArg statisticsArg = new ObjectUserMarketingStatisticsArg();
//            statisticsArg.setEa(ea);
//            statisticsArg.setUserId(fsUserId);
//            statisticsArg.setMarketingActivityId(noticeResult.getMarketingActivityId());
////            //TODO 这里会有性能问题，如果批量去查用不到索引、单个去查询时间会拉长调用链路，这里要优化得等到营销动态优化上了之后才能优化
//            Result<ObjectUserMarketingStatisticsResult> statisticsResult = statisticService.getObjectMarketingActivityStatistics(statisticsArg);
//            ObjectUserMarketingStatisticsResult statisticsData = statisticsResult.getData();
            ObjectUserMarketingStatisticsResult statisticsData = marketingActivityIdToStatisticsMap.get(entity.getMarketingActivityId());
            noticeResult.setLookUpCount(statisticsData != null ? statisticsData.getLookUpCount() : 0);
            noticeResult.setForwardCount(statisticsData != null ? statisticsData.getForwardCount() : 0);
            //从统计结果中取值，填充
            if (countData != null) {
                //noticeResult.setLookUpCount(countData.getLookUpCount());
                //noticeResult.setForwardCount(countData.getForwardCount());
                noticeResult.setSaveToCrmLeadCount(countData.getLeadAccumulationCount());
                noticeResult.setSpreadEmployeeCount(countData.getSpreadCount());
            }
            if (MapUtils.isNotEmpty(marketingActivityClueNum)) {
                noticeResult.setLeadCount(marketingActivityClueNum.get(marketingActivityId) != null ? marketingActivityClueNum.get(marketingActivityId) : 0);
            }
            noticeResultList.add(noticeResult);
        }

        pageResult.setResult(noticeResultList);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    @Override
    public Result<PageResult<NoticeResult>> listPartnerNotices(String ea, Integer fsUserId, ListNoticeArg vo) {
        List<NoticeResult> noticeResultList = Lists.newArrayList();
        PageResult<NoticeResult> pageResult = new PageResult<>();
        pageResult.setTotalCount(0);
        pageResult.setPageNum(vo.getPageNum());
        pageResult.setPageSize(vo.getPageSize());
        pageResult.setResult(noticeResultList);

        PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
        marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
        if (StringUtils.isNotEmpty(vo.getTitle())){
            marketingActivityArg.setName(vo.getTitle());
        }
        marketingActivityArg.setSpreadType( MarketingActivitySpreadTypeEnum.PARTNER_GROUP_SEND.getSpreadType());
        //从营销活动对象获取数据
        marketingActivityArg.setPageSize(vo.getPageSize());
        marketingActivityArg.setPageNumber((vo.getPageNum() - 1) * vo.getPageSize());
        com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageMarketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg);
        if (pageMarketingActivityList == null || CollectionUtils.isEmpty(pageMarketingActivityList.getDataList())){
            return Result.newSuccess(pageResult);
        }
        pageResult.setTotalCount(pageMarketingActivityList.getTotal());
        List<ObjectData> marketingActivityList = pageMarketingActivityList.getDataList();
        List<String> marketingActivityIds = marketingActivityList.stream().map(ObjectData::getId).collect(Collectors.toList());
        Map<String, ObjectData> idToMarketingActivityDataMap = marketingActivityList.stream().collect(Collectors.toMap(ObjectData::getId, Function.identity(), (v1,v2)->v2));
        List<String> marketingEventIds = marketingActivityList.stream().map(objectData -> objectData.getString("marketing_event_id")).collect(Collectors.toList());
        List<MarketingEventData> eventDataList = null;
        if (CollectionUtils.isNotEmpty(marketingEventIds)) {
            eventDataList = marketingEventManager.listMarketingEventData(ea, fsUserId, marketingEventIds);
        }
        Map<String, MarketingEventData> marketingEventDataMap = null;
        if (CollectionUtils.isNotEmpty(eventDataList)) {
            marketingEventDataMap = eventDataList.stream().collect(Collectors.toMap(MarketingEventData::getId, Function.identity(), (k1, k2) -> k1));
        }
        Map<String, String> activityEntityMap = null;
        List<ActivityEntity> activityEntityList = conferenceDAO.getActivityByEaAndMarketingEventIds(ea, marketingEventIds);
        if (CollectionUtils.isNotEmpty(activityEntityList)){
            activityEntityMap = activityEntityList.stream().collect(Collectors.toMap(ActivityEntity::getMarketingEventId, ActivityEntity::getId, (k1, k2)->k1));
        }

        List<NoticeEntity> noticeEntityList = noticeDAO.listNoticesByMarketingActivityIds(ea, vo.getTitle(), marketingActivityIds);
        if (CollectionUtils.isEmpty(noticeEntityList)) {
            return Result.newSuccess(pageResult);
        }
        List<String> noticeIds = noticeEntityList.stream().map(NoticeEntity::getId).collect(Collectors.toList());
        List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigList = marketingActivityExternalConfigDao.getByAssociateIds(noticeIds);
        Map<String, String> noticeIdToMarketingActivityIdMap = marketingActivityExternalConfigList.stream()
                .collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getAssociateId, MarketingActivityExternalConfigEntity::getMarketingActivityId));

        //获取统计结果，获取线索数，访问人次，转发人次，员工推广次数
        List<MarketingActivityStatisticEntity> countDataList;
        Map<String, Integer> marketingActivityClueNum = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(marketingActivityIds)) {
            countDataList = marketingActivityStatisticDao.countDataByMarketingActivityIdsForPartner(ea, marketingActivityIds);
            // 线索从表单获取
            marketingActivityClueNum = customizeFormClueManager.batchCountClueNumByMarketingActivityIds(ea, marketingActivityIds, false,0);
        } else {
            countDataList = new ArrayList<>();
        }
        for (MarketingActivityStatisticEntity marketingActivityStatisticEntity : countDataList) {
            marketingActivityStatisticEntity.setLeadAccumulationCount(marketingActivityClueNum.get(marketingActivityStatisticEntity.getMarketingActivityId()));
        }
        //将统计结果转为营销活动ID对应的Map
        Map<String, MarketingActivityStatisticEntity> marketingActivityIdToCountResultMap = countDataList.stream()
                .collect(Collectors.toMap(MarketingActivityStatisticEntity::getMarketingActivityId, Function.identity()));

        List<Integer> userIds = noticeEntityList.stream().map(NoticeEntity::getFsUserId).collect(Collectors.toList());
        Map<String, Integer> allPartnerMap=null;
        Map<String, Integer> haveSpreadPartnerMap=null;
        Map<String, Integer> spreadNumberMap=null;
        if (null != marketingActivityIds && marketingActivityIds.size() > 0) {
            //需要推广的伙伴
            List<DataCount> allPartner = spreadTaskDAO.batchGetPartnerSpreadStatistic(ea, marketingActivityIds);
            allPartnerMap = allPartner.stream().filter(e -> e.getCount() != null).collect(Collectors.toMap(DataCount::getId, DataCount::getCount, (k1, k2) -> k1));
            haveSpreadPartnerMap = allPartner.stream().filter(e -> e.getSpreadCount() != null).collect(Collectors.toMap(DataCount::getId, DataCount::getSpreadCount, (k1, k2) -> k1));
            //员工推广数量
            List<DataCount> spreadNumber = marketingActivityStatisticDao.countPartnerSpreadNumByMarketingActivityIds(ea, marketingActivityIds);
            spreadNumberMap = spreadNumber.stream().collect(Collectors.toMap(DataCount::getId, DataCount::getCount, (k1, k2) -> k1));

        }
        // 获取操作人姓名
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);
        for (NoticeEntity entity : noticeEntityList) {
            String marketingActivityId = noticeIdToMarketingActivityIdMap.get(entity.getId());
            ObjectData objectData = idToMarketingActivityDataMap.get(marketingActivityId);

            MarketingActivityStatisticEntity countData = marketingActivityIdToCountResultMap.get(marketingActivityId);
            NoticeResult noticeResult = BeanUtil.copy(entity, NoticeResult.class);
            noticeResult.setStartTime(DateUtil.getTimeStamp(entity.getStartTime()));
            noticeResult.setEndTime(DateUtil.getTimeStamp(entity.getEndTime()));
            noticeResult.setCreateTime(DateUtil.getTimeStamp(entity.getCreateTime()));
            noticeResult.setUsername(fsEmployeeMsgMap.get(entity.getFsUserId()) != null ? fsEmployeeMsgMap.get(entity.getFsUserId()).getName() : entity.getUsername());


            if (NoticeSendTypeEnum.TIMING.getType() == entity.getSendType()) {
                noticeResult.setSendTime(DateUtil.getTimeStamp(entity.getTimingTime()));
            } else {
                noticeResult.setSendTime(DateUtil.getTimeStamp(entity.getSendTime()));
            }
            if (objectData != null) {
                //从营销活动信息中取 关联的市场活动信息
                MarketingActivityData marketingActivityData = MarketingActivityData.wrap(objectData);
                noticeResult.setMarketingEventId(marketingActivityData.getMarketingEventId());
                noticeResult.setMarketingEventName(marketingActivityData.getMarketingEventIdName());
                if (marketingEventDataMap != null && marketingEventDataMap.get(marketingActivityData.getMarketingEventId()) != null) {
                    noticeResult.setMarketingEventType(marketingEventDataMap.get(marketingActivityData.getMarketingEventId()).getEventType());
                    if (activityEntityMap != null && activityEntityMap.get(marketingActivityData.getMarketingEventId()) != null) {
                        noticeResult.setMarketingObjectId(activityEntityMap.get(marketingActivityData.getMarketingEventId()));
                    }
                }
                noticeResult.setStatus(Integer.parseInt((String)objectData.getString("status")));
                noticeResult.setAuditStatus(objectData.getLifeStatus());
            }


            if (entity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus() && entity.getSendType() == NoticeSendTypeEnum.TIMING.getType()){
                noticeResult.setSendCancelable(true);
            }else{
                noticeResult.setSendCancelable(false);
            }

            // 推广状态 是否可撤回
            this.setStatusAndButton(entity, noticeResult);

            PromotionContentData promotionContentData = getPromotionContent(entity.getContentType(), entity.getContent());
            noticeResult.setPromotionContent(promotionContentData);
            //从统计结果中取值，填充
            if (countData != null) {
                noticeResult.setLookUpCount(countData.getLookUpCount());
                noticeResult.setForwardCount(countData.getForwardCount());
                noticeResult.setSaveToCrmLeadCount(countData.getLeadAccumulationCount());
                noticeResult.setSpreadEmployeeCount(countData.getSpreadCount());
            }
            int allPartner = 0;
            int spreadPartner = 0;
            int spreadNumber = 0;
            if (allPartnerMap != null && allPartnerMap.containsKey(entity.getMarketingActivityId())) {
                allPartner = allPartnerMap.get(entity.getMarketingActivityId());
            }
            if (haveSpreadPartnerMap != null && haveSpreadPartnerMap.containsKey(entity.getMarketingActivityId())) {
                spreadPartner = haveSpreadPartnerMap.get(entity.getMarketingActivityId());
            }
            if(spreadNumberMap!=null && spreadNumberMap.containsKey(entity.getMarketingActivityId())){
                spreadNumber = spreadNumberMap.get(entity.getMarketingActivityId());
            }
            noticeResult.setSpreadPartner(spreadPartner);
            noticeResult.setUnSpreadPartner(allPartner-spreadPartner);
            noticeResult.setSpreadNumber(spreadNumber);
            if (MapUtils.isNotEmpty(marketingActivityClueNum)) {
                noticeResult.setLeadCount(marketingActivityClueNum.get(marketingActivityId) != null ? marketingActivityClueNum.get(marketingActivityId) : 0);
            }
            noticeResultList.add(noticeResult);
        }

        pageResult.setResult(noticeResultList);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }

    private void setStatusAndButton(NoticeEntity entity, NoticeResult noticeResult) {
        noticeResult.setSendRevocable(true);
        Date nowDate = new Date();
        if (entity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus()){
            noticeResult.setSpreadStatus(SpreadStatusEnum.UN_SEND.getStatus());
        } else if (entity.getStatus() == NoticeStatusEnum.SENDING.getStatus()) {
            noticeResult.setSpreadStatus(SpreadStatusEnum.SENDING.getStatus());
        } else if (entity.getStartTime().after(nowDate) && entity.getStatus() == NoticeStatusEnum.SUCCESS.getStatus()) {
            noticeResult.setSpreadStatus(SpreadStatusEnum.UN_STARTED.getStatus());
        } else if (entity.getStartTime().before(nowDate) && entity.getEndTime().after(nowDate) && entity.getStatus() != NoticeStatusEnum.REVOCATION.getStatus()) {
            noticeResult.setSpreadStatus(SpreadStatusEnum.SUCCESS.getStatus());
        } else if (entity.getStatus() == NoticeStatusEnum.REVOCATION.getStatus()) {
            noticeResult.setSendRevocable(false);
            noticeResult.setSpreadStatus(SpreadStatusEnum.REVOCATION.getStatus());
        } else if (entity.getEndTime().before(nowDate) && entity.getStatus() != NoticeStatusEnum.REVOCATION.getStatus()) {
            noticeResult.setSpreadStatus(SpreadStatusEnum.END.getStatus());
        }
    }
    /*
    public Result<PageResult<NoticeResult>> listNoticesOld(String ea, Integer fsUserId, ListNoticeArg vo) {
        Page page = new Page(vo.getPageNum(), vo.getPageSize(), true);
        List<NoticeResult> noticeResultList = Lists.newArrayList();
        List<NoticeEntity> noticeEntityList = noticeDAO.listNotices(ea, vo.getTitle(), page);
        List<String> noticeIds = noticeEntityList.stream().map(NoticeEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noticeIds)) {
            PageResult<NoticeResult> pageResult = new PageResult<>();
            pageResult.setTotalCount(0);
            pageResult.setResult(Lists.newArrayList());
            return new Result<>(SHErrorCode.SUCCESS, pageResult);
        }
        List<MarketingActivityExternalConfigEntity> marketingActivityExternalConfigList = marketingActivityExternalConfigDao.getByAssociateIds(noticeIds);
        Map<String, String> noticeIdToMarketingActivityIdMap = marketingActivityExternalConfigList.stream()
                .collect(Collectors.toMap(MarketingActivityExternalConfigEntity::getAssociateId, MarketingActivityExternalConfigEntity::getMarketingActivityId));

        List<String> marketingActivityIds = Lists.newArrayList(noticeIdToMarketingActivityIdMap.values());
        //获取营销活动信息
        List<ObjectData> marketingActivityList = Lists.newArrayList();
        if (marketingActivityIds.size() != 0) {
            PaasQueryMarketingActivityArg marketingActivityArg = new PaasQueryMarketingActivityArg();
            marketingActivityArg.setObjectApiName(CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName());
            marketingActivityArg.setIds(Lists.newArrayList(marketingActivityIds));
            //这里pageNumber实际是offset， pageSize是limit
            marketingActivityArg.setPageSize(vo.getPageSize());
            marketingActivityArg.setPageNumber(0);
            marketingActivityList = marketingCrmManager.listMarketingActivity(ea, fsUserId, marketingActivityArg).getDataList();
        }
        Map<String, ObjectData> idToMarketingActivityDataMap = new HashMap<>();
        if (marketingActivityList != null && CollectionUtils.isNotEmpty(marketingActivityList)) {
            idToMarketingActivityDataMap = marketingActivityList.stream().collect(Collectors.toMap(ObjectData::getId, Function.identity()));
        }
        //获取统计结果，获取线索数，访问人次，转发人次，员工推广次数
        List<MarketingActivityStatisticEntity> countDataList;
        Map<String, Integer> marketingActivityClueNum = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(marketingActivityIds)) {
            countDataList = marketingActivityStatisticDao.countDataByMarketingActivityIds(ea, marketingActivityIds);
            // 线索从表单获取
            marketingActivityClueNum = customizeFormClueManager.batchCountClueNumByMarketingActivityIds(ea, marketingActivityIds, false,0);
        } else {
            countDataList = new ArrayList<>();
        }
        for (MarketingActivityStatisticEntity marketingActivityStatisticEntity : countDataList) {
            marketingActivityStatisticEntity.setLeadAccumulationCount(marketingActivityClueNum.get(marketingActivityStatisticEntity.getMarketingActivityId()));
        }
        //将统计结果转为营销活动ID对应的Map
        Map<String, MarketingActivityStatisticEntity> marketingActivityIdToCountResultMap = countDataList.stream()
                .collect(Collectors.toMap(MarketingActivityStatisticEntity::getMarketingActivityId, Function.identity()));

        List<Integer> userIds = noticeEntityList.stream().map(NoticeEntity::getFsUserId).collect(Collectors.toList());
        // 获取操作人姓名
        Map<Integer, FSEmployeeMsg> fsEmployeeMsgMap = fsAddressBookManager.getEmployeeInfoByUserIds(ea, userIds, true);
        for (NoticeEntity entity : noticeEntityList) {
            String marketingActivityId = noticeIdToMarketingActivityIdMap.get(entity.getId());
            ObjectData objectData = idToMarketingActivityDataMap.get(marketingActivityId);

            MarketingActivityStatisticEntity countData = marketingActivityIdToCountResultMap.get(marketingActivityId);
            NoticeResult noticeResult = BeanUtil.copy(entity, NoticeResult.class);
            noticeResult.setStartTime(DateUtil.getTimeStamp(entity.getStartTime()));
            noticeResult.setEndTime(DateUtil.getTimeStamp(entity.getEndTime()));
            noticeResult.setCreateTime(DateUtil.getTimeStamp(entity.getCreateTime()));
            noticeResult.setUsername(fsEmployeeMsgMap.get(entity.getFsUserId()) != null ? fsEmployeeMsgMap.get(entity.getFsUserId()).getName() : entity.getUsername());


            if (NoticeSendTypeEnum.TIMING.getType() == entity.getSendType()) {
                noticeResult.setSendTime(DateUtil.getTimeStamp(entity.getTimingTime()));
            } else {
                noticeResult.setSendTime(DateUtil.getTimeStamp(entity.getSendTime()));
            }
            if (objectData != null) {
                //从营销活动信息中取 关联的市场活动信息
                MarketingActivityData marketingActivityData = MarketingActivityData.wrap(objectData);
                noticeResult.setMarketingEventId(marketingActivityData.getMarketingEventId());
                noticeResult.setMarketingEventName(marketingActivityData.getMarketingEventIdName());
                noticeResult.setStatus(Integer.parseInt((String)objectData.getString("status")));
            }

            if (entity.getStatus() == NoticeStatusEnum.UN_SEND.getStatus() && entity.getSendType() == NoticeSendTypeEnum.TIMING.getType()){
                noticeResult.setSendCancelable(true);
            }else{
                noticeResult.setSendCancelable(false);
            }

            PromotionContentData promotionContentData = getPromotionContent(entity.getContentType(), entity.getContent());
            noticeResult.setPromotionContent(promotionContentData);
            //从统计结果中取值，填充
            if (countData != null) {
                noticeResult.setLookUpCount(countData.getLookUpCount());
                noticeResult.setForwardCount(countData.getForwardCount());
                noticeResult.setSaveToCrmLeadCount(countData.getLeadAccumulationCount());
                noticeResult.setSpreadEmployeeCount(countData.getSpreadCount());
            }
            if (MapUtils.isNotEmpty(marketingActivityClueNum)) {
                noticeResult.setLeadCount(marketingActivityClueNum.get(marketingActivityId) != null ? marketingActivityClueNum.get(marketingActivityId) : 0);
            }
            noticeResultList.add(noticeResult);
        }
        PageResult<NoticeResult> pageResult = new PageResult<>();
        pageResult.setTotalCount(page.getTotalNum());
        pageResult.setResult(noticeResultList);
        return new Result<>(SHErrorCode.SUCCESS, pageResult);
    }
    */

    private int queryObjDataCount(String ea, Integer fsUserId, String apiName) {
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(apiName);
        params.setIncludeLayout(false);
        params.setIncludeDescribe(false);
        params.setIncludeButtonInfo(false);
        Integer total = metadataControllerServiceManager.getTotal(new HeaderObj(eieaConverter.enterpriseAccountToId(ea), fsUserId), apiName, params);
        return total == null ? 0 : total;
    }

    private List<String> batchQueryObjdataIds(String ea, Integer fsUserId, String apiName){
        try {
            Integer totalCount = queryObjDataCount(ea, fsUserId, apiName);
            if (totalCount == 0) {
                return null;
            }
            int pageSize = 1000;
            int totalPageCount = totalCount / pageSize;
            if (totalCount % pageSize != 0) {
                totalPageCount++;
            }

            int offset = 0;
            int endOffset = offset + pageSize;
            List<String> fieldsProjection = Lists.newArrayList("_id");
            List<String> ids = Lists.newArrayList();
            for (int i = 0; i < totalPageCount; i++) {
                SearchQuery searchQuery = new SearchQuery();
                searchQuery.setLimit(endOffset - offset <= pageSize ? endOffset - offset : pageSize);
                searchQuery.setOffset(offset);
                com.fxiaoke.crmrestapi.common.data.Page<ObjectData> pageResult = crmV2Manager.getList(ea, fsUserId, apiName, searchQuery, fieldsProjection);
                if (pageResult != null && CollectionUtils.isNotEmpty(pageResult.getDataList())) {
                    pageResult.getDataList().forEach(objectData -> ids.add(objectData.getId()));
                }

                offset += pageSize;
                endOffset += pageSize;
            }
            return ids;
        }catch (Exception e){
            log.info("NoticeServiceImpl.batchQueryObjdataIds ea:{} fsUserId:{} apiName:{} e:", ea, fsUserId, apiName, e);
            return null;
        }
    }

    public com.fxiaoke.crmrestapi.common.data.Page<ObjectData> listMarketingActivityByMarketingEventIds(String ea, Integer fsUserId,
                                                            String title, List<String> marketingEventIds, PageArg pageArg) {

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("marketing_event_id", marketingEventIds, FilterOperatorEnum.IN);
        if (StringUtils.isNotEmpty(title)) {
            searchQuery.addFilter("name", Lists.newArrayList(title), FilterOperatorEnum.LIKE);
        }
        searchQuery.addFilter("spread_type", Lists.newArrayList("1"), FilterOperatorEnum.EQ);
        searchQuery.setLimit(pageArg.getPageSize());
        searchQuery.setOffset((pageArg.getPageNo() - 1) * pageArg.getPageSize());
        searchQuery.addOrderBy("create_time", false);

        return crmV2Manager.getList(ea, fsUserId, CrmObjectApiNameEnum.MARKETING_ACTIVITY.getName(), searchQuery);
    }


    @Override
    public Result<NoticeDetailResult> getDetail(GetNoticeDetailArg vo) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(vo.getNoticeId());
        if (noticeEntity == null) {
            log.info("notice is not exist noticeId:{}", vo.getNoticeId());
            return new Result<>(SHErrorCode.NOTICE_NOT_EXIST);
        }

        NoticeDetailResult noticeDetailResult = new NoticeDetailResult();
        Integer contentType = noticeEntity.getContentType();
        String content = noticeEntity.getContent();
        ContentDetail contentDetail = new ContentDetail();
        if (NoticeContentTypeEnum.ARTICLE.equalsType(contentType)) {
            ArticleEntity articleEntity = articleDAO.getById(content);
            if (articleEntity != null) {
                PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleEntity.getId(),articleEntity.getFsEa());
                noticeDetailResult.setUid(articleEntity.getUid());
                contentDetail.setImage(fileV2Manager.getUrlByPath(photoEntity.getThumbnailPath(), articleEntity.getFsEa(), false));
                contentDetail.setTitle(articleEntity.getTitle());
            }
        } else if (NoticeContentTypeEnum.PRODUCT.equalsType(contentType)) {
            ProductEntity productEntity = productDAO.getById(content);
            if (productEntity != null) {
                PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.PRODUCT_HEAD.getType(), productEntity.getId(),productEntity.getFsEa());
                contentDetail.setTitle(productEntity.getName());
                contentDetail.setImage(fileV2Manager.getUrlByPath(photoEntity.getThumbnailPath(), productEntity.getFsEa(), false));
            }
        } else if (NoticeContentTypeEnum.ACTIVITY.equalsType(contentType)) {
            ActivityEntity activityEntity = activityDAO.getById(content);
            if (activityEntity != null) {
                PhotoEntity photoEntity = photoManager.querySinglePhoto(PhotoTargetTypeEnum.ACTIVITY_COVER.getType(), activityEntity.getId(),activityEntity.getEa());
                contentDetail.setTitle(activityEntity.getTitle());
                if (photoEntity != null) {
                    contentDetail.setImage(fileV2Manager.getUrlByPath(photoEntity.getThumbnailPath(), activityEntity.getEa(), false));
                }else{
                    contentDetail.setImage(fileV2Manager.getUrlByPath(defaultConferenceCover, activityEntity.getEa(), false));
                }
            }
        } else if (NoticeContentTypeEnum.QR_POSTER.equalsType(contentType)) {
            QRPosterEntity qrPosterEntity = qrPosterDAO.queryById(content);
            if (qrPosterEntity != null) {
                contentDetail.setTitle(qrPosterEntity.getTitle());
                contentDetail.setImage(fileV2Manager.getUrlByPath(qrPosterEntity.getThumbnailApath(), qrPosterEntity.getEa(), false));
            }
        } else if (NoticeContentTypeEnum.HEXAGON.equalsType(contentType)) {
            HexagonSiteEntity hexagonSiteEntity = hexagonSiteDAO.getById(content);
            if (hexagonSiteEntity == null) {
                return Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND);
            }

            contentDetail.setTitle(hexagonSiteEntity.getName());

            Map<String, String> coverApathMap = new HashMap<>();
            List<String> apathList = new ArrayList<>();
            List<HexagonSiteListDTO> hexagonSiteCoverListDTOList = hexagonSiteDAO.getCoverBySiteIds(Arrays.asList(content));
            if (null != hexagonSiteCoverListDTOList && CollectionUtils.isNotEmpty(hexagonSiteCoverListDTOList)) {
                for (HexagonSiteListDTO hexagonSiteListDTO : hexagonSiteCoverListDTOList) {
                    apathList.add(hexagonSiteListDTO.getSharePicH5Apath());
                    coverApathMap.put(hexagonSiteListDTO.getHexagonSiteId(), hexagonSiteListDTO.getSharePicH5Apath());
                }
            }

            Map<String, String> coverUrlMap = fileV2Manager.batchGetUrlByPath(apathList, hexagonSiteEntity.getEa(), false);
            if (null != coverUrlMap) {
                String apath = coverApathMap.get(hexagonSiteEntity.getId());
                if (StringUtils.isNotBlank(apath)) {
                    contentDetail.setImage(coverUrlMap.get(apath));
                }
            }
        } else if (NoticeContentTypeEnum.CUSTOMIZE_FORM_DATA.equalsType(contentType)) {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormDataDAO.getCustomizeFormDataById(content);
            if (customizeFormDataEntity != null) {
                contentDetail.setTitle(customizeFormDataEntity.getFormHeadSetting().getName());
                if (customizeFormDataEntity.getFormHeadSetting() != null && CollectionUtils.isNotEmpty(customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath())) {
                    String path = customizeFormDataEntity.getFormHeadSetting().getHeadPhotoPath().get(0);
                    contentDetail.setImage(fileV2Manager.getUrlByPath(path, customizeFormDataEntity.getEa(), false));
                }
            }
        } else if (NoticeContentTypeEnum.OUT_LINK.equalsType(contentType)) {
            OutLinkEntity entity = null;
            List<OutLinkEntity> entities = outLinkDAO.getByIds(Collections.singletonList(content));
            if (entities != null && !entities.isEmpty()) {
                entity = entities.get(0);
                contentDetail.setTitle(entity.getName());
                contentDetail.setImage(entity.getCover());
            }
        } else if (NoticeContentTypeEnum.EMAIL_MATERIAL.equalsType(contentType)) {
            List<EmailMaterialEntity> entities = emailMaterialDao.getByIds(Collections.singletonList(content));
            if (entities != null && !entities.isEmpty()) {
                EmailMaterialEntity entity = entities.get(0);
                contentDetail.setTitle(entity.getTitle());
            }
        }
        noticeDetailResult.setContentDetail(contentDetail);
        noticeDetailResult.setTitle(noticeEntity.getTitle());
        noticeDetailResult.setContentId(noticeEntity.getContent());
        noticeDetailResult.setContentType(noticeEntity.getContentType());
        noticeDetailResult.setDescription(noticeEntity.getDescription());
        noticeDetailResult.setStatus(noticeEntity.getStatus());
        noticeDetailResult.setContent(noticeEntity.getContent());
        noticeDetailResult.setSendType(noticeEntity.getSendType());
        noticeDetailResult.setStartTime(DateUtil.getTimeStamp(noticeEntity.getStartTime()));
        noticeDetailResult.setEndTime(DateUtil.getTimeStamp(noticeEntity.getEndTime()));
        noticeDetailResult.setTimingDate(DateUtil.getTimeStamp(noticeEntity.getTimingTime()));
        if (StringUtils.isNotEmpty(noticeEntity.getCoverApath())) {
            noticeDetailResult.setCoverPath(noticeEntity.getCoverApath());
            noticeDetailResult.setCoverUrl(fileV2Manager.getUrlByPath(noticeEntity.getCoverApath(), noticeEntity.getFsEa(), false));
        }else {
            noticeEntity.setCoverApath(defaultConferenceCover);
            noticeDetailResult.setCoverPath(defaultConferenceCover);
            noticeDetailResult.setCoverUrl(fileV2Manager.getUrlByPath(defaultConferenceCover, noticeEntity.getFsEa(), false));
        }
        if (NoticeSendTypeEnum.TIMING.getType() == noticeEntity.getSendType()) {
            noticeDetailResult.setSendTime(DateUtil.getTimeStamp(noticeEntity.getTimingTime()));
        } else {
            noticeDetailResult.setSendTime(DateUtil.getTimeStamp(noticeEntity.getSendTime()));
        }
        noticeDetailResult.setCreateTime(DateUtil.getTimeStamp(noticeEntity.getCreateTime()));
        return new Result<>(SHErrorCode.SUCCESS, noticeDetailResult);
    }

    @Override
    @Transactional
    public Result sendNoticeInvite(String ea, Integer fsUserId) {
        Integer fsEi = eieaConverter.enterpriseAccountToId(ea);
        List<EmployeeDto> employeeDtoList = authManager.getAllEmployee(fsEi, fsUserId);
        if (org.springframework.util.CollectionUtils.isEmpty(employeeDtoList)) {
            log.error("employeeDtoList is null, fsEi={}, fsUserId={}", fsEi, fsUserId);
            return new Result<>(SHErrorCode.SYSTEM_ERROR);
        }

        Set<Integer> userIdsSet = new HashSet<>();

        Set<Integer> employeeIdsSet = new HashSet<>();
        Map<Integer, EmployeeDto> phoneEmployeeDTO = new HashMap<>();
        for (EmployeeDto employeeDto : employeeDtoList) {
            if (StringUtils.isNotBlank(String.valueOf(employeeDto.getEmployeeId()))) {
                employeeIdsSet.add(employeeDto.getEmployeeId());
                phoneEmployeeDTO.put(employeeDto.getEmployeeId(), employeeDto);
                userIdsSet.add(employeeDto.getEmployeeId());
            }
        }
        List<Integer> employeeIdsList = new ArrayList<>(employeeIdsSet);
        List<FSBindEntity> fsBindEntityList = qywxUserManager.getFsBindByFsUserInfos(ea, employeeIdsList);
        if (CollectionUtils.isNotEmpty(fsBindEntityList)) {
            for (FSBindEntity fsBindEntity1 : fsBindEntityList) {
                EmployeeDto employeeDto = phoneEmployeeDTO.get(fsBindEntity1.getFsUserId());
                if (null != employeeDto) {
                    if ((employeeDto.getEnterpriseId() == fsBindEntity1.getFsCorpId()) && (employeeDto.getEmployeeId() == fsBindEntity1.getFsUserId())) {
                        userIdsSet.remove(employeeDto.getEmployeeId());
                    }
                }
            }
        }

        List<Integer> userIds = new ArrayList<>(userIdsSet);

        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                fsMessageManager.sendInviteMessage(ea, userIds, fsUserId);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result sendfsUserIdNoticeInvite(String ea, Integer fsUserId, Integer sendFsUserId) {
        List<Integer> userIds = Arrays.asList(sendFsUserId);

        ThreadPoolUtils.execute(new Runnable() {
            @Override
            public void run() {
                fsMessageManager.sendInviteMessage(ea, userIds, fsUserId);
            }
        }, ThreadPoolTypeEnums.MEDIUM_BUSINESS);

        return new Result(SHErrorCode.SUCCESS);
    }

    @Override
    public Result<Boolean> cancelSendSpread(String noticeId, String ea) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null){
            return Result.newSuccess(false);
        }

        if (noticeEntity.getStatus() != NoticeStatusEnum.UN_SEND.getStatus()){
            log.info("NoticeServiceImpl.cancelSendSpread failed status != UN_SEND noticeId:{} status:{}", noticeId, noticeEntity.getStatus());
            return Result.newSuccess(false);
        }

        noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.CANCLE.getStatus(),  new Date());
        return Result.newSuccess(true);
    }

    @Override
    public Result<Boolean> revokeSendSpread(String noticeId, String ea, String marketingActivityId) {
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null) {
            return Result.newSuccess(false);
        }

        if (noticeEntity.getStatus() == NoticeStatusEnum.REVOCATION.getStatus()) {
            log.info("NoticeServiceImpl.revokeSendSpread failed status == REVOCATION noticeId:{} status:{}", noticeId, noticeEntity.getStatus());
            return Result.newSuccess(false);
        }

        noticeDAO.updateStatusById(noticeId, NoticeStatusEnum.REVOCATION.getStatus(), new Date());

        spreadTaskDAO.updateById(marketingActivityId, SpreadTaskStatusEnum.DELETED.getStatus());

        return Result.newSuccess(true);
    }

    @Override
    public Result<Void> spreadMarketingActivityToSpecialEmployeeAgain(String ea, Integer userId, String noticeId, String marketingActivity) {
        List<Integer> allTaskUserIds = spreadTaskDAO.getTotalUserId(ea, marketingActivity);
        if (CollectionUtils.isEmpty(allTaskUserIds)){
            return Result.newSuccess();
        }
        List<Integer> spreadUserIds = marketingActivityEmployeeStatisticDao.getAllSpreadEmployeeUserIds(ea, marketingActivity);
        if (!CollectionUtils.isEmpty(spreadUserIds)){
            allTaskUserIds.removeAll(spreadUserIds);
        }
        //单独处理全员推广<钉钉通知>
        if (StringUtils.isNotBlank(ksDingEa) && Arrays.asList(ksDingEa.split(",")).contains(ea)) {
            MarketingActivityExternalConfigEntity externalConfigEntity = marketingActivityExternalConfigDao.getByMarketingActivityId(marketingActivity);
            List<String> userIdList = Lists.newArrayList();
            allTaskUserIds.forEach(taskUserId ->{
                userIdList.add(String.valueOf(taskUserId));
            });
            if(CollectionUtils.isNotEmpty(userIdList)){
                sendToDingdingSpreadSendMq(noticeId, marketingActivity, externalConfigEntity.getMarketingEventId(),userIdList);
            }
            return Result.newSuccess();
        }

        sendImageTextNoticeAgainByEmployees(noticeId, marketingActivity, allTaskUserIds);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> partnerNoticeAgain(String ea, Integer userId, String noticeId, String marketingActivityId) throws UnsupportedEncodingException {
        List<Integer> allTaskUserIds = spreadTaskDAO.getTotalUserId(ea, marketingActivityId);
        if (CollectionUtils.isEmpty(allTaskUserIds)){
            return Result.newSuccess();
        }
        List<Integer> spreadUserIds = marketingActivityEmployeeStatisticDao.getAllSpreadEmployeeUserIds(ea, marketingActivityId);
        if (!CollectionUtils.isEmpty(spreadUserIds)){
            allTaskUserIds.removeAll(spreadUserIds);
        }
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null){
            log.error("partnerNoticeAgain failed noticeEntity not exist noticeId:{} marketingActivityId:{}", noticeEntity, marketingActivityId);
            return Result.newSuccess();
        }
        List<UserRelationEntity> userRelationEntityList = userRelationManager.getByFsUserIdList(ea, allTaskUserIds);
        if (CollectionUtils.isEmpty(userRelationEntityList)) {
            return Result.newError(SHErrorCode.PARTNER_NOT_EXIST_USER_RELATION);
        }
        List<Integer> noticePublicEmployeeIdList = userRelationEntityList.stream().filter(e -> e.getOuterUid() != null).map(e -> e.getOuterUid().intValue()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noticePublicEmployeeIdList)) {
            return Result.newError(SHErrorCode.PARTNER_NOT_EXIST_USER_RELATION);
        }
        int ei = eieaConverter.enterpriseAccountToId(ea);
        GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
        enterpriseDataArg.setEnterpriseAccount(ea);
        enterpriseDataArg.setEnterpriseId(ei);
        GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
        String enterpriseName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_529);
        if (enterpriseDataResult != null && enterpriseDataResult.getEnterpriseData() != null) {
            enterpriseName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
        }
        PartnerNoticeSendArg vo = new PartnerNoticeSendArg();
        vo.setMarketingActivityId(marketingActivityId);
        vo.setContent(noticeEntity.getContent());
        vo.setContentType(noticeEntity.getContentType());
        if (StringUtils.isNotBlank(noticeEntity.getMaterialInfoList())) {
            List<AddMarketingActivityArg.MaterialInfo> materialInfos = gson.fromJson(noticeEntity.getMaterialInfoList(), new TypeToken<List<AddMarketingActivityArg.MaterialInfo>>() {
            }.getType());
            vo.setMaterialInfoList(materialInfos);
        }
        //调用互联发送通知的接口
        AddRemindRecordArg arg = new AddRemindRecordArg();
        arg.setEi(ei);
        arg.setUuid(UUIDUtil.getUUID());
        RemindRecordItem remindRecordItem = new RemindRecordItem();
        arg.setRemindRecordItem(remindRecordItem);
        //crm通知唯一标识，防止重复发送
        remindRecordItem.setSourceId("HBYX"+noticeEntity.getId());
        //发送人id
        remindRecordItem.setSenderId(userId);
        //CRM通知类型 201代表推广任务通知
        remindRecordItem.setType(201);
        //接收人id
        remindRecordItem.setReceiverIDs(noticePublicEmployeeIdList);
        //是否通知发送人
        remindRecordItem.setRemindSender(true);
        //crm通知内容
        remindRecordItem.setFullContent(noticeEntity.getTitle());
        remindRecordItem.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_551));
        //CRM跳转URL类型：0 无跳转; 1 跳转详情;2 跳转业务流; 3 对象任务
        remindRecordItem.setUrlType(0);
//            remindRecordItem.setUrlParameter();
        remindRecordItem.setAppId(partnerAppId);

        List<KeyValueItem> bodyForm = new ArrayList<>();
//            KeyValueItem keyValueItem = new KeyValueItem(new TextCardElement("宣传语", null, null, null), new TextCardElement(noticeEntity.getDescription(), null, null, null));
        //任务号
        KeyValueItem taskNumber = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_560), null, null, null), new TextCardElement(String.valueOf(eieaConverter.enterpriseAccountToId(ea)) + new Date().getTime(), null, null, null));
        //任务类型
        KeyValueItem taskType = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_562), null, null, null), new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_562_1), null, null, null));
        //执行人
        KeyValueItem executor = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_564), null, null, null), new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_564_1), null, null, null));
//            //分派人
        KeyValueItem assigner = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_566), null, null, null), new TextCardElement(enterpriseName, null, null, null));
//            //分派时间
        KeyValueItem assignTime = new KeyValueItem(new TextCardElement("分派时间", null, null, null), new TextCardElement(DateFormatUtils.format(Instant.now().toEpochMilli(), "yyyy年MM月dd日"), null, null, null));
        bodyForm.add(taskNumber);
        bodyForm.add(taskType);
        bodyForm.add(executor);
        bodyForm.add(assigner);
        bodyForm.add(assignTime);
        remindRecordItem.setBodyForm(bodyForm);

        //通知跳转url
        String webUrl;
        String noticeEntityId = noticeEntity.getId();
        Integer noticeContentType = noticeEntity.getContentType();
//        String contentTitle = getPromotionContent(noticeEntity.getContentType(), noticeEntity.getContent()).getTitle();
//        final String encodePage = URLEncoder.encode("pages/noticeCenter/noticeCenter", "UTF-8");
//        //  final String qrUrl = host + "/fssharehelper/file/showQRCode?scene=" + noticeEntityId + "&page=" + encodePage + "&width=860&type=notice";
//        final String qrUrl = host + "/appmarketing/web/file/showQRCode?scene=" + noticeEntityId + "&page=" + encodePage + "&width=860&type=notice";
//        final String encodeQRUrl = URLEncoder.encode(qrUrl, "UTF-8");
        List<String> extraChannelList = new ArrayList<>();
        boolean wxsuccess =false;
        // TODO: 2023/6/5 暂时去掉公众号发送
//        for (String wxappid : partnerWXAppIdList) {
//            String wxUrl = getOubPlatformUrl(vo, 4, ea,wxappid);
//            remindRecordItem.setOutPlatformUrl(wxUrl);
//            log.info("send partner wx notice url:{}", wxUrl);
//            //先发微信公众号
//            ExtraOutsideChannelInfoVO weChatServiceNumberChannelInfo = new ExtraOutsideChannelInfoVO();
//            weChatServiceNumberChannelInfo.setAppId(partnerAppId);
//            weChatServiceNumberChannelInfo.setOutChannelType(4);
//            weChatServiceNumberChannelInfo.setOutChannelData(wxappid);
//            weChatServiceNumberChannelInfo.setTemplateIdForOut(partnerNoticeTemplate);
//            extraChannelList.add(gson.toJson(weChatServiceNumberChannelInfo));
//            remindRecordItem.setExtraChannelList(extraChannelList);
//            MessageResponse wxResponse = null;
//            try {
//                wxResponse = crmNotifyService.addRemindRecord(arg);
//            } catch (FRestClientException e) {
//                log.warn("sendPartnerNotice wxResponse  crmNotifyService.addRemindRecord fail", e);
//            }
//            if(null!= remindRecordItem.getExtraChannelList()){
//                remindRecordItem.getExtraChannelList().clear();
//            }
//            extraChannelList.clear();
//        }
        //换地址发互联应用
        if(null!= remindRecordItem.getExtraChannelList()){
            remindRecordItem.getExtraChannelList().clear();
        }
        extraChannelList.clear();
        String appUrl = getOubPlatformUrlApp(vo, 2, ea,null);
        remindRecordItem.setOutPlatformUrl(appUrl);
        log.info("send partner app notice url:{}", appUrl);
        //企业微信通道信息 2代表默认的纷享通道，发企信也用这个
        ExtraOutsideChannelInfoVO businessWeChatChannelInfo = new ExtraOutsideChannelInfoVO();
        businessWeChatChannelInfo.setAppId(partnerAppId);
        businessWeChatChannelInfo.setOutChannelType(2);
        businessWeChatChannelInfo.setLinkType("1");
        businessWeChatChannelInfo.setOutChannelData(partnerAppId);
        businessWeChatChannelInfo.setTemplateIdForOut(partnerNoticeTemplate);
        extraChannelList.add(gson.toJson(businessWeChatChannelInfo));
        remindRecordItem.setExtraChannelList(extraChannelList);
        List<Integer> receiverIDs = arg.getRemindRecordItem().getReceiverIDs();
        if (CollectionUtils.isEmpty(receiverIDs)) {
            return Result.newError(SHErrorCode.EMPTY_MARKETING_ACTIVITY_SEND_USER_AFTER_FILTER);
        }
        List<List<Integer>> receiverIdPartition = Lists.partition(receiverIDs, partitionSize);
        int sendCount = receiverIdPartition.size();
        int successCount = 0;
        try {
            //分片发送处理
            for (List<Integer> receiverIds : receiverIdPartition) {
                arg.getRemindRecordItem().setReceiverIDs(receiverIds);
                MessageResponse appResponse = crmNotifyService.addRemindRecord(arg);
                if (null != appResponse && appResponse.getCode() == 200 && "Success".equals(appResponse.getMsg())) {
                    successCount ++;
                }
            }
        } catch (FRestClientException e) {
            log.warn("partnerNoticeAgain appResponse  crmNotifyService.addRemindRecord fail", e);
        }
        log.info("send partnerNoticeAgain receive sendCount:{},successCount:{}",sendCount,successCount);
        return Result.newSuccess();
    }

    @Override
    public Result<String> sendCouponNotice(String ea, Integer fsUserId, PartnerNoticeSendArg vo) throws UnsupportedEncodingException {
        PartnerNoticeSendArg.PartnerNoticeVisibilityVO partnerNoticeVisibilityVO = vo.getPartnerNoticeVisibilityVO();
        Map<Integer, String> outUserIdToOutTannetIdMap = new HashMap<>();
        List<Integer> dockUserIDList = fsAddressBookManager.getCouponDockUserId(partnerNoticeVisibilityVO.getEaList(), partnerNoticeVisibilityVO.getTenantGroupIdList(), ea, fsUserId, outUserIdToOutTannetIdMap);
        List<String> partnerWXAppIdList = getBIndWxByEa(ea);
        //保存通知
        NoticeEntity noticeEntity = doSaveCouponPartnerNoticeEntity(ea, fsUserId, vo, dockUserIDList);
        //立即发送
        if (vo.getSendType() == NoticeSendTypeEnum.NORMAL.getType()) {
            int ei = eieaConverter.enterpriseAccountToId(ea);
            GetEnterpriseDataArg enterpriseDataArg = new GetEnterpriseDataArg();
            enterpriseDataArg.setEnterpriseAccount(ea);
            enterpriseDataArg.setEnterpriseId(ei);
            GetEnterpriseDataResult enterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterpriseDataArg);
            String enterpriseName = I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_529);
            if (enterpriseDataResult != null && enterpriseDataResult.getEnterpriseData() != null) {
                enterpriseName = enterpriseDataResult.getEnterpriseData().getEnterpriseName();
            }
            //调用互联发送通知的接口
            AddRemindRecordArg arg = new AddRemindRecordArg();
            arg.setEi(ei);
            arg.setUuid(UUIDUtil.getUUID());
            RemindRecordItem remindRecordItem = new RemindRecordItem();
            arg.setRemindRecordItem(remindRecordItem);
            //crm通知唯一标识，防止重复发送
            remindRecordItem.setSourceId("HBYX"+noticeEntity.getId());
            //发送人id
            remindRecordItem.setSenderId(fsUserId);
            //CRM通知类型 201代表推广任务通知
            remindRecordItem.setType(201);
            //接收人id
            remindRecordItem.setReceiverIDs(dockUserIDList);
            //是否通知发送人
            remindRecordItem.setRemindSender(true);
            //crm通知内容
            remindRecordItem.setFullContent(noticeEntity.getTitle());
            remindRecordItem.setTitle(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_2946));
            //CRM跳转URL类型：0 无跳转; 1 跳转详情;2 跳转业务流; 3 对象任务
            remindRecordItem.setUrlType(0);
//            remindRecordItem.setUrlParameter();
            remindRecordItem.setAppId(partnerAppId);

            List<KeyValueItem> bodyForm = new ArrayList<>();
            //任务号
            KeyValueItem taskNumber = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_560), null, null, null), new TextCardElement(String.valueOf(eieaConverter.enterpriseAccountToId(ea)) + new Date().getTime(), null, null, null));
            //任务类型
            KeyValueItem taskType = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_562), null, null, null), new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_2956_1), null, null, null));
            //执行人
            KeyValueItem executor = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_564), null, null, null), new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_564_1), null, null, null));
//            //分派人
            KeyValueItem assigner = new KeyValueItem(new TextCardElement(I18nUtil.get(I18nKeyEnum.MARK_SERVICE_NOTICESERVICEIMPL_566), null, null, null), new TextCardElement(enterpriseName, null, null, null));
//            //分派时间
            KeyValueItem assignTime = new KeyValueItem(new TextCardElement("分派时间", null, null, null), new TextCardElement(DateFormatUtils.format(Instant.now().toEpochMilli(), "yyyy年MM月dd日"), null, null, null));
            bodyForm.add(taskNumber);
            bodyForm.add(taskType);
            bodyForm.add(executor);
            bodyForm.add(assigner);
            bodyForm.add(assignTime);
            remindRecordItem.setBodyForm(bodyForm);

            //通知跳转url
            String webUrl;
            String marketingActivityId = vo.getMarketingActivityId();
            String noticeEntityId = noticeEntity.getId();
            Integer noticeContentType = noticeEntity.getContentType();
            List<String> extraChannelList = new ArrayList<>();
            boolean wxSendSuccess = false;
            if (NoticeContentTypeEnum.ACTIVITY.equalsType(vo.getContentType())) {
                ActivityEntity activityEntity = activityDAO.getById(vo.getContent());
                vo.setActivityDetailSiteId(activityEntity == null ? "" : activityEntity.getActivityDetailSiteId());
            }
            for (String wxappid : partnerWXAppIdList) {
                String wxUrl = getCouponOubPlatformUrl(vo, 4, ea,wxappid);
                remindRecordItem.setOutPlatformUrl(wxUrl);
                log.info("send partner wx notice url:{}", wxUrl);
                //先发微信公众号
                ExtraOutsideChannelInfoVO weChatServiceNumberChannelInfo = new ExtraOutsideChannelInfoVO();
                weChatServiceNumberChannelInfo.setAppId(partnerAppId);
                weChatServiceNumberChannelInfo.setOutChannelType(4);
                weChatServiceNumberChannelInfo.setOutChannelData(wxappid);
//                weChatServiceNumberChannelInfo.setTemplateIdForOut("OPENTM201511150");
                weChatServiceNumberChannelInfo.setTemplateIdForOut(partnerNoticeTemplate);
                extraChannelList.add(gson.toJson(weChatServiceNumberChannelInfo));
                remindRecordItem.setExtraChannelList(extraChannelList);
                MessageResponse wxResponse = null;
                try {
                    wxResponse = crmNotifyService.addRemindRecord(arg);
                } catch (FRestClientException e) {
                    log.warn("sendPartnerNotice wxResponse  crmNotifyService.addRemindRecord fail", e);
                }
                if(null != wxResponse && wxResponse.getCode() == 200 && "Success".equals(wxResponse.getMsg())){
                    wxSendSuccess = true;
                }
                if (null != remindRecordItem.getExtraChannelList()) {
                    remindRecordItem.getExtraChannelList().clear();
                }
                extraChannelList.clear();
            }


            //换地址发互联应用
            if (null != remindRecordItem.getExtraChannelList()) {
                remindRecordItem.getExtraChannelList().clear();
            }
            extraChannelList.clear();
            String appUrl = getCouponOubPlatformUrlApp(vo, 2, ea,null);
            String innerWebUrl = getCouponOubPlatformUrl(vo,2,ea,null);
            remindRecordItem.setOutPlatformUrl(innerWebUrl);
            remindRecordItem.setInnerPlatformWebUrl(innerWebUrl);
            log.info("send partner web notice url:{}", innerWebUrl);
            remindRecordItem.setInnerPlatformMobileUrl(appUrl);
            log.info("send partner app notice url:{}", appUrl);
            //企业微信通道信息 2代表默认的纷享通道，发企信也用这个
            ExtraOutsideChannelInfoVO businessWeChatChannelInfo = new ExtraOutsideChannelInfoVO();
            businessWeChatChannelInfo.setAppId(partnerAppId);
            businessWeChatChannelInfo.setOutChannelType(2);
            businessWeChatChannelInfo.setLinkType("1");
            businessWeChatChannelInfo.setOutChannelData(partnerAppId);
            businessWeChatChannelInfo.setTemplateIdForOut(partnerNoticeTemplate);
            extraChannelList.add(gson.toJson(businessWeChatChannelInfo));
            remindRecordItem.setExtraChannelList(extraChannelList);
            MessageResponse appResponse = null;
            try {
                appResponse = crmNotifyService.addRemindRecord(arg);
            } catch (FRestClientException e) {
                log.warn("sendPartnerNotice appResponse  crmNotifyService.addRemindRecord fail", e);
            }

            if (wxSendSuccess || (null != appResponse && appResponse.getCode() == 200 && "Success".equals(appResponse.getMsg()))) {
                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.SUCCESS.getStatus(), new Date());
            } else {
                noticeDAO.updateStatusById(noticeEntityId, NoticeStatusEnum.FAIL.getStatus(), new Date());
            }
            //创建推广任务
            //spreadTaskManager.createPartnerSpreadTask(dockUserIDList, ea, vo.getMarketingActivityId(),outUserIdToOutTannetIdMap);
        }
        return Result.newSuccess(noticeEntity.getId());
    }

    @Override
    public Result<Void> sendAdDataSyncNotice(String ea, Integer userId) {
        return null;
    }

    private NoticeEntity doSaveCouponPartnerNoticeEntity(String ea, Integer fsUserId, PartnerNoticeSendArg vo, List<Integer> dockUserIDList) {
        String username = fsAddressBookManager.getEmployeeInfo(ea, fsUserId).getFullName();
        NoticeEntity noticeEntity = new NoticeEntity();
        noticeEntity.setId(UUIDUtil.getUUID());
        noticeEntity.setTitle(vo.getTitle());
        noticeEntity.setDescription(vo.getDescription());
        noticeEntity.setSendType(vo.getSendType());
        noticeEntity.setCreateTime(new Date());
        noticeEntity.setUpdateTime(new Date());
        noticeEntity.setFsEa(ea);
        //营销类型
        noticeEntity.setType(7);
        noticeEntity.setFsUserId(fsUserId);
        noticeEntity.setUsername(username);
        //noticeEntity.setStartTime(DateUtil.parse(DateUtil.format(vo.getStartTime())));
        //noticeEntity.setEndTime(DateUtil.parse(DateUtil.format(vo.getEndTime())));
        String content = vo.getContent();
        noticeEntity.setContent(content);
        noticeEntity.setContentType(vo.getContentType());
        noticeEntity.setCoverApath(vo.getCoverPath());

        if (vo.getSendType() == NoticeSendTypeEnum.NORMAL.getType()) {
            noticeEntity.setStatus(NoticeStatusEnum.SENDING.getStatus());
            noticeEntity.setSendTime(new Date());
        } else {
            noticeEntity.setStatus(NoticeStatusEnum.UN_SEND.getStatus());
            noticeEntity.setTimingTime(DateUtil.parse(DateUtil.format(vo.getTimingDate())));
        }
        NoticeSendArg noticeSendArg = new NoticeSendArg();
        NoticeSendArg.NoticeVisibilityVO noticeVisibilityVO = new  NoticeSendArg.NoticeVisibilityVO();
        noticeVisibilityVO.setUserIds(dockUserIDList);
        noticeVisibilityVO.setEaList(vo.getPartnerNoticeVisibilityVO().getEaList());
        noticeVisibilityVO.setTenantGroupIdList(vo.getPartnerNoticeVisibilityVO().getTenantGroupIdList());
        String sendScope = gson.toJson(noticeVisibilityVO);
        noticeEntity.setSendScope(sendScope);
        //保存通知
        noticeDAO.addNotice(noticeEntity);
        return noticeEntity;
    }


    private NoticeEntity doSendSaveClueFailNoticeEntity(String ea, Integer fsUserId, NoticeSendArg vo) {
        // 活动线索发送给市场活动和负责人，官网线索发送给场景负责人
        String username = fsAddressBookManager.getEmployeeInfo(ea, fsUserId) == null ? "" : fsAddressBookManager.getEmployeeInfo(ea, fsUserId).getFullName();
        NoticeEntity noticeEntity = new NoticeEntity();
        noticeEntity.setId(UUIDUtil.getUUID());
        noticeEntity.setTitle(vo.getTitle());
        noticeEntity.setDescription(vo.getDescription());
        noticeEntity.setSendType(vo.getSendType());
        noticeEntity.setCreateTime(new Date());
        noticeEntity.setUpdateTime(new Date());
        noticeEntity.setFsEa(ea);
        noticeEntity.setFsUserId(fsUserId);
        noticeEntity.setUsername(username);
//        noticeEntity.setStartTime(DateUtil.parse(DateUtil.format(vo.getStartTime())));
//        noticeEntity.setEndTime(DateUtil.parse(DateUtil.format(vo.getEndTime())));
        String content = vo.getContent();
        noticeEntity.setContent(content);
        noticeEntity.setContentType(NoticeContentTypeEnum.SAVE_CRM_FAIL.getType());
        noticeEntity.setCoverApath(vo.getCoverPath());
        String sendScope = gson.toJson(vo.getNoticeVisibilityVO());
        noticeEntity.setSendScope(sendScope);
        noticeEntity.setType(8);

        if (vo.getSendType() == NoticeSendTypeEnum.NORMAL.getType()) {
            noticeEntity.setStatus(NoticeStatusEnum.SENDING.getStatus());
            noticeEntity.setSendTime(new Date());
        } else {
            noticeEntity.setStatus(NoticeStatusEnum.UN_SEND.getStatus());
            noticeEntity.setTimingTime(DateUtil.parse(DateUtil.format(vo.getTimingDate())));
        }


        noticeDAO.addNotice(noticeEntity);
        return noticeEntity;
    }

    @Override
    public Result<GetNoticeByUserResult> getNoticeByUser(GetNoticeByUserArg arg) {
        GetNoticeByUserResult result = new GetNoticeByUserResult();
        String ea = arg.getEa();
        Integer userId = arg.getFsUserId();
        String noticeId = arg.getNoticeId();
        NoticeEntity noticeEntity = noticeDAO.getNoticeById(noticeId);
        if (noticeEntity == null) {
            return Result.newError(SHErrorCode.NO_DATA);
        }

        result.setNoticeId(noticeEntity.getId());
        result.setTitle(noticeEntity.getTitle());
        result.setDescription(noticeEntity.getDescription());
        result.setStartTime(noticeEntity.getStartTime() != null ? noticeEntity.getStartTime().getTime() : null);
        result.setEndTime(noticeEntity.getEndTime() != null ? noticeEntity.getEndTime().getTime() : null);

        String content = noticeEntity.getContent();
        EmailMaterialEntity emailMaterialEntity = emailMaterialDao.getById(content);
        if (emailMaterialEntity != null) {
            GetNoticeByUserResult.EmailItem emailItem = BeanUtil.copy(emailMaterialEntity, GetNoticeByUserResult.EmailItem.class);
            result.setEmailItem(emailItem);
        }

        MarketingActivityExternalConfigEntity configEntity = marketingActivityExternalConfigDao.getByEaAndAssociateMsg(ea, AssociateIdTypeEnum.MANKEEP_SPREAD_NOTICE.getType(), noticeId);
        if (configEntity != null) {
            result.setMarketingEventId(configEntity.getMarketingEventId());
            result.setMarketingActivityId(configEntity.getMarketingActivityId());
        }

        SpreadTaskEntity spreadTaskEntity = spreadTaskDAO.getUserSpreadTaskByNoticeId(ea, userId, noticeId);
        if (spreadTaskEntity != null) {
            result.setSpreadTaskId(spreadTaskEntity.getId());
        }

        // 更新员工查看任务状态
        spreadTaskManager.updateSpreadLookUpById(ea, userId, result.getMarketingActivityId());
        return Result.newSuccess(result);
    }
}