package com.facishare.marketing.provider.dao.distribution;

import com.facishare.marketing.provider.dto.distribution.DistributorSubmitDTO;
import com.facishare.marketing.provider.entity.distribution.DistributorFormSubmitEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Created by ranluch on 2019/5/8.
 */
public interface DistributorFormSubmitDao {
    @Select(" SELECT * FROM distributor_form_submit WHERE distributor_id = #{distributorId}")
    DistributorFormSubmitEntity querySubmitDataByDistributorId(@Param("distributorId") String distributorId);

    @Select("<script>"
        + " SELECT * FROM distributor_form_submit WHERE distributor_id IN "
        + 		"<foreach collection = 'distributorIds' item = 'item' open = '(' separator = ',' close = ')'>"
        + 			"#{item}"
        + 		"</foreach>"
        + "</script>")
    List<DistributorFormSubmitEntity> querySubmitDataByDistributorIds(@Param("distributorIds") List<String> distributorIds);

    @Select("<script>"
            + "SELECT s.*, d.uid FROM distributor_form_submit s INNER JOIN distributor d ON s.distributor_id = d.id WHERE d.uid in\n"
            + 		"<foreach collection = 'uids' item = 'item' open = '(' separator = ',' close = ')'>"
            + 			"#{item}"
            + 		"</foreach>"
          + "</script>")
    List<DistributorSubmitDTO> querySubmitListByUids(@Param("uids")List<String> uids);
}
