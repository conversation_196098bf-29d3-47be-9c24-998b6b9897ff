package com.facishare.marketing.provider.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.live.ListMuduEventArg;
import com.facishare.marketing.api.result.live.GetMuduAccountResult;
import com.facishare.marketing.api.vo.live.BindMuduAccountVO;
import com.facishare.marketing.common.contstant.live.MuduApiConstant;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjApiNameEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMembersObjMemberStatusEnum;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.live.LiveUserStatusEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.*;
import com.facishare.marketing.common.util.threadpool.ThreadPoolUtils;
import com.facishare.marketing.provider.dao.campaign.CampaignMergeDataDAO;
import com.facishare.marketing.provider.dao.live.LiveUserStatusDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO;
import com.facishare.marketing.provider.dao.live.MuduAccountDAO;
import com.facishare.marketing.provider.entity.CampaignMergeDataEntity;
import com.facishare.marketing.provider.entity.live.LiveUserStatusEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics;
import com.facishare.marketing.provider.entity.live.MuduAccountEntity;
import com.facishare.marketing.provider.innerData.live.mudu.*;
import com.facishare.marketing.provider.manager.qywx.HttpManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.training.outer.api.enums.LiveStatusEnum;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/5/6
 **/
@Component
@Slf4j
public class MuduManager {

    @Autowired
    private MuduAccountDAO muduAccountDAO;

    @Autowired
    private HttpManager httpManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;

    @Autowired
    private LiveUserStatusDAO liveUserStatusDAO;

    @Autowired
    private CampaignMergeDataDAO campaignMergeDataDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;


    /**
     * 目睹接口-post请求
     * @param body
     * @param url
     * @param typeToken
     * @param accessToken
     * @param <T>
     * @return
     */
    public <T> T executePost(String accessToken, Object body, String url, TypeToken typeToken) {
        RequestBody requestBody = RequestBody.create(null, GsonUtil.getGson().toJson(body));
        Map<String, String> headerMap = new HashMap();
        headerMap.put("Authorization", accessToken);
        return httpManager.executePostHttpWithRequestBodyAndHeaderV2(requestBody, url, typeToken, headerMap);
    }

    /**
     * 目睹接口-get请求
     * @param url
     * @param typeToken
     * @param accessToken
     * @param <T>
     * @return
     */
    public <T> T executeGet(String accessToken, String url, TypeToken typeToken) {
        Map<String, String> headerMap = new HashMap();
        headerMap.put("Authorization", accessToken);
        return httpManager.executeGetHttpWithHeaderV2(url, typeToken, headerMap);
    }

    /**
     * 绑定目睹账号
     * @param vo
     * @return
     */
    public Result<Void> bindMuduAccount(BindMuduAccountVO vo){
        MuduAccountEntity muduAccountEntity = muduAccountDAO.getByAppId(vo.getAppId());
        if (muduAccountEntity != null && !Objects.equals(muduAccountEntity.getEa(), vo.getEa())) {
            return Result.newError(SHErrorCode.MUDU_ACCOUNT_EXISTS_BIND);
        }
        MuduAccountEntity entity = muduAccountDAO.getByEa(vo.getEa());
        if (entity != null){
            entity.setAppId(vo.getAppId());
            entity.setSecretKey(vo.getSecretKey());
            muduAccountDAO.update(entity);
        } else {
            entity = new MuduAccountEntity();
            entity.setId(UUIDUtil.getUUID());
            entity.setEa(vo.getEa());
            entity.setAppId(vo.getAppId());
            entity.setSecretKey(vo.getSecretKey());
            muduAccountDAO.insert(entity);
        }
        return Result.newSuccess();
    }

    /**
     * 获取目睹账号
     * @param ea
     * @return
     */
    public Result<GetMuduAccountResult> getMuduAccount(String ea){
        MuduAccountEntity entity = muduAccountDAO.getByEa(ea);
        GetMuduAccountResult result = new GetMuduAccountResult();
        if (Objects.nonNull(entity)) {
            result.setAppId(entity.getAppId());
            result.setSecretKey(entity.getSecretKey());
        }
        return Result.newSuccess(result);
    }

    /**
     * 测试参数是否配置正确
     * @return
     */
    public Result testApi(String ea){
        String accessToken = getAccessToken(ea);
        if (StringUtils.isNotBlank(accessToken)) {
            return Result.newSuccess();
        }
        return Result.newError(SHErrorCode.MUDU_NOT_BIND_ACCOUNT);
    }


    public String getAccessToken(String ea){
        MuduAccountEntity entity = muduAccountDAO.getByEa(ea);
        if (entity == null) {
            log.info("not bind mudu account");
            return null;
        }
        String appId = entity.getAppId();
        String secretKey = entity.getSecretKey();

        // 读缓存
        String muduAccessToken = redisManager.getMuduAccessToken(ea, appId);
        if (StringUtils.isNotBlank(muduAccessToken)) {
            return muduAccessToken;
        }

        // 缓存不存在，通过目睹接口获取Token
        Map paramMap = new HashMap();
        paramMap.put("accessKeyId", appId);
        paramMap.put("secretAccessKey", secretKey);
        paramMap.put("grantType", "client_credential");
        MuduApiBaseResult<MuduApiGetAccessTokenResult> apiBaseResult = httpManager.executePostHttp(paramMap, MuduApiConstant.GET_ACCESS_TOKEN, new TypeToken<MuduApiBaseResult<MuduApiGetAccessTokenResult>>(){});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            String accessToken = apiBaseResult.getData().getAccessToken();
            redisManager.setMuduAccessToken(ea, appId, accessToken, null);
            return accessToken;
        } else {
            log.info("MuduManager -> getAccessToken failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 活动准入方式设置
     * @param accessToken
     * @param eventId
     * @param customAddress
     * @return
     */
    public boolean setAccessConfig(String accessToken, String eventId, String customAddress){
        if (StringUtils.isBlank(accessToken)) {
            log.info("setAccessConfig accessToken is null");
            return false;
        }

        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        String finalUrl = TextUtil.replaceVarByMap(MuduApiConstant.SET_ACCESS_CONFIG, replaceMap);
        Map paramMap = new HashMap();
        paramMap.put("accessType", 4);
        paramMap.put("customAddress", customAddress);
        MuduApiBaseResult apiBaseResult = executePost(accessToken, paramMap, finalUrl, new TypeToken<MuduApiBaseResult>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.isSuccess();
        } else {
            log.info("MuduManager -> setAccessConfig failed, result:{}", apiBaseResult);
            return false;
        }
    }

    /**
     * 活动准入方式查询
     * @param accessToken
     * @param eventId
     * @return
     */
    public MuduApiGetAccessConfigResult getAccessConfig(String accessToken, String eventId){
        if (StringUtils.isBlank(accessToken)) {
            log.info("getAccessConfig accessToken is null");
            return null;
        }

        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        String finalUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_ACCESS_CONFIG, replaceMap);
        MuduApiBaseResult<MuduApiGetAccessConfigResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetAccessConfigResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("MuduManager -> getAccessConfig failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 场所用户统计
     * @param accessToken
     * @param roomId
     * @return
     */
    public MuduApiGetRoomUserResult getRoomUserStatistic(String accessToken, String roomId){
        if (StringUtils.isBlank(accessToken)) {
            log.info("getRoomUserStatistic accessToken is null");
            return null;
        }
        MuduApiGetRoomUserResult muduApiGetRoomUserResult = new MuduApiGetRoomUserResult();
        List<MuduApiGetRoomUserResult.Item> items = Lists.newArrayList();

        // 先查一次，得到总数
        Map replaceMap = Maps.newHashMap();
        replaceMap.put("roomId", roomId);
        String replaceUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_ROOM_USER_STATISTIC, replaceMap);
        Map paramMap = Maps.newHashMap();
        paramMap.put("page", 1);
        paramMap.put("perPage", 1);
        String finalUrl = httpManager.transformUrlParamsV2(replaceUrl, paramMap);
        MuduApiBaseResult<MuduApiGetRoomUserResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetRoomUserResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            MuduApiGetRoomUserResult data = apiBaseResult.getData();
            Integer total = data.getTotal();
            for (int i = 0; i < (total / 100) + 1; i++) {
                paramMap = Maps.newHashMap();
                paramMap.put("page", i+1);
                paramMap.put("perPage", 100);
                String url = httpManager.transformUrlParamsV2(replaceUrl, paramMap);
                MuduApiBaseResult<MuduApiGetRoomUserResult> singleResult = executeGet(accessToken, url, new TypeToken<MuduApiBaseResult<MuduApiGetRoomUserResult>>() {});
                if (singleResult != null && singleResult.isSuccess()) {
                    items.addAll(singleResult.getData().getItems());
                } else {
                    log.info("MuduManager -> getRoomUserStatistic failed, arg:{}, result:{}", paramMap, apiBaseResult);
                    return null;
                }
            }
            muduApiGetRoomUserResult.setItems(items);
            log.info("MuduManager -> getRoomUserStatistic success, roomId:{}, size:{}", roomId, items.size());
            return muduApiGetRoomUserResult;
        } else {
            log.info("MuduManager -> getRoomUserStatistic first failed, arg:{}, result:{}", replaceMap, apiBaseResult);
            return null;
        }
    }

    public MuduApiGetEventUserResult getEventUserStatistic(String accessToken, String eventId) {
        if (StringUtils.isBlank(accessToken)) {
            log.info("getEventUserStatistic accessToken is null");
            return null;
        }
        MuduApiGetEventUserResult muduApiGetEventUserResult = new MuduApiGetEventUserResult();
        List<MuduApiGetEventUserResult.Item> items = Lists.newArrayList();

        // 先查一次，得到总数
        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        String replaceUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_EVENT_USER_STATISTIC, replaceMap);
        Map paramMap = Maps.newHashMap();
        paramMap.put("page", 1);
        paramMap.put("perPage", 1);
        String finalUrl = httpManager.transformUrlParamsV2(replaceUrl, paramMap);
        MuduApiBaseResult<MuduApiGetEventUserResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetEventUserResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            MuduApiGetEventUserResult data = apiBaseResult.getData();
            Integer total = data.getTotal();
            for (int i = 0; i < (total / 100) + 1; i++) {
                paramMap = Maps.newHashMap();
                paramMap.put("page", i+1);
                paramMap.put("perPage", 100);
                String url = httpManager.transformUrlParamsV2(replaceUrl, paramMap);
                MuduApiBaseResult<MuduApiGetEventUserResult> singleResult = executeGet(accessToken, url, new TypeToken<MuduApiBaseResult<MuduApiGetEventUserResult>>() {});
                if (singleResult != null && singleResult.isSuccess() && CollectionUtils.isNotEmpty(singleResult.getData().getItems())) {
                    items.addAll(singleResult.getData().getItems());
                } else {
                    log.info("MuduManager -> getEventUserStatistic failed, arg:{}, result:{}", paramMap, apiBaseResult);
                    return null;
                }
            }
            muduApiGetEventUserResult.setItems(items);
            log.info("MuduManager -> getEventUserStatistic success, eventId:{}, size:{}", eventId, items.size());
            return muduApiGetEventUserResult;
        } else {
            log.info("MuduManager -> getEventUserStatistic first failed, arg:{}, result:{}", replaceMap, apiBaseResult);
            return null;
        }
    }


    /**
     * 场所数据统计
     * @param accessToken
     * @param eventId
     * @param roomId
     * @return
     */
    public MuduApiGetRoomStatisticResult getRoomStatistic(String accessToken, String eventId, String roomId){
        if (StringUtils.isBlank(accessToken)) {
            log.info("getRoomStatistic accessToken is null");
            return null;
        }

        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        replaceMap.put("roomId", roomId);
        String finalUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_ROOM_STATISTIC, replaceMap);
        MuduApiBaseResult<MuduApiGetRoomStatisticResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetRoomStatisticResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            log.info("MuduManager -> getRoomStatistic success, eventId:{}, roomId:{}, result:{}", eventId, roomId, apiBaseResult.getData());
            return apiBaseResult.getData();
        } else {
            log.info("MuduManager -> getRoomStatistic failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 获取目睹活动列表
     * @param accessToken
     * @param arg
     * @return
     */
    public MuduApiGetEventListResult getEventList(String accessToken, ListMuduEventArg arg) {
        if (StringUtils.isBlank(accessToken)) {
            log.info("getEventList accessToken is null");
            return null;
        }

        Map paramMap = Maps.newHashMap();
        paramMap.put("keyword", arg.getKeyword());
        paramMap.put("page", arg.getPageNum());
        paramMap.put("perPage", arg.getPageSize());
        String finalUrl = httpManager.transformUrlParamsV2(MuduApiConstant.GET_EVENT_LIST, paramMap);
        MuduApiBaseResult<MuduApiGetEventListResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetEventListResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("MuduManager -> getEventList failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 查询活动详情，（当前目睹没有提供查询活动详情的接口，考虑到目睹活动的数据量不会很大，暂时通过查询列表去匹配，后续这里要优化）
     * @param accessToken
     * @param eventId
     * @return
     */
    public MuduApiGetEventListResult.EventInfo getEventDetail(String accessToken, String eventId) {
        ListMuduEventArg listMuduEventArg = new ListMuduEventArg();
        for (int i = 1; i < 10; i++) {
            listMuduEventArg.setPageNum(i);
            listMuduEventArg.setPageSize(100);
            MuduApiGetEventListResult eventList = getEventList(accessToken, listMuduEventArg);
            if (eventList != null && CollectionUtils.isNotEmpty(eventList.getItems())) {
                List<MuduApiGetEventListResult.EventInfo> items = eventList.getItems();
                for (MuduApiGetEventListResult.EventInfo item : items) {
                    if (Objects.equals(item.getEventId(), eventId)) {
                        return item;
                    }
                }
            } else {
                break;
            }
        }
        return null;
    }

    /**
     * 获取场所列表
     * @param accessToken
     * @param eventId
     * @return
     */
    public MuduApiGetRoomListResult getRoomList(String accessToken, String eventId) {
        if (StringUtils.isBlank(accessToken)) {
            log.info("getRoomList accessToken is null");
            return null;
        }

        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        String replaceUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_ROOM_LIST, replaceMap);
        Map paramMap = Maps.newHashMap();
        paramMap.put("page", 1);
        paramMap.put("perPage", 20);
        String finalUrl = httpManager.transformUrlParamsV2(replaceUrl, paramMap);
        MuduApiBaseResult<MuduApiGetRoomListResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetRoomListResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("MuduManager -> getRoomList failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 用户免密登录
     * @param accessToken
     * @param eventId
     * @param thirdOpenId
     * @param userName
     * @return
     */
    public MuduApiPasswordLessLoginResult passwordLessLogin(String accessToken, String eventId, String thirdOpenId, String userName, String phone){
        if (StringUtils.isBlank(accessToken)) {
            log.info("passwordLessLogin accessToken is null");
            return null;
        }

        Map paramMap = new HashMap();
        paramMap.put("scope", "audience");
        paramMap.put("thirdOpenId", thirdOpenId);
        paramMap.put("username", userName);
        paramMap.put("eventId", eventId);
        paramMap.put("mobile", phone);
        MuduApiBaseResult<MuduApiPasswordLessLoginResult> apiBaseResult = executePost(accessToken, paramMap, MuduApiConstant.PASSWORD_LESS_LOGIN, new TypeToken<MuduApiBaseResult<MuduApiPasswordLessLoginResult>>() {});
        if (apiBaseResult != null && apiBaseResult.isSuccess()) {
            return apiBaseResult.getData();
        } else {
            log.info("MuduManager -> passwordLessLogin failed, result:{}", apiBaseResult);
            return null;
        }
    }

    /**
     * 查询活动链接
     * @param accessToken
     * @param eventId
     * @return
     */
    public MuduApiBaseResult<MuduApiGetEventUrlResult> getEventUrl(String accessToken, String eventId){
        if (StringUtils.isBlank(accessToken)) {
            log.info("getEventUrl accessToken is null");
            return null;
        }

        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        String finalUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_EVENT_URL, replaceMap);
        MuduApiBaseResult<MuduApiGetEventUrlResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetEventUrlResult>>() {});
        return apiBaseResult;
    }

    /**
     * 查看场所详情
     * @param accessToken
     * @param eventId
     * @param roomId
     * @return
     */
    public MuduApiBaseResult<MuduApiGetRoomDetailResult> getRoomDetail(String accessToken, String eventId, String roomId){
        if (StringUtils.isBlank(accessToken)) {
            log.info("getRoomDetail accessToken is null");
            return null;
        }

        Map replaceMap = Maps.newHashMap();
        replaceMap.put("eventId", eventId);
        replaceMap.put("roomId", roomId);
        String finalUrl = TextUtil.replaceVarByMap(MuduApiConstant.GET_ROOM_DETAIL, replaceMap);
        MuduApiBaseResult<MuduApiGetRoomDetailResult> apiBaseResult = executeGet(accessToken, finalUrl, new TypeToken<MuduApiBaseResult<MuduApiGetRoomDetailResult>>() {});
        return apiBaseResult;
    }

    /**
     * 查询主会场直播地址
     * @param ea
     * @param eventId
     * @return
     */
    public String getMainRoomUrl(String ea, String eventId){
        MuduApiGetRoomListResult roomListResult = getRoomList(getAccessToken(ea), eventId);
        if (roomListResult != null) {
            List<MuduApiGetRoomListResult.RoomInfo> items = roomListResult.getItems();
            if (CollectionUtils.isNotEmpty(items)) {
                for (MuduApiGetRoomListResult.RoomInfo item : items) {
                    if (item.getMainVenueFlag() == 1) {
                        return item.getRoomWatchUrl();
                    }
                }
            }
        }
        return null;
    }


    public void syncMuduLiveStatistic(String ea, String marketingEventId) {
        int ei = eieaConverter.enterpriseAccountToId(ea);
        MarketingLiveEntity marketingLiveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(ei, marketingEventId);
        if (marketingLiveEntity == null) {
            return;
        }

        Integer subEvent = marketingLiveEntity.getSubEvent();
        String xiaoetongLiveId = marketingLiveEntity.getXiaoetongLiveId();
        // 查询已有直播数据
        List<LiveUserStatusEntity> oldLiveUserStatusList = liveUserStatusDAO.queryXiaoetongLiveUserStatusByLiveId(xiaoetongLiveId);
        Map<String, LiveUserStatusEntity> oldLiveUserStatusMap = oldLiveUserStatusList.stream().collect(Collectors.toMap(LiveUserStatusEntity::getPhone, Function.identity(), (v1, v2) -> v1));

        List<MarketingLiveStatistics> marketingLiveStatistics = marketingLiveStatisticsDAO.getByXiaoetongLiveId(ei, xiaoetongLiveId);
        String accessToken = getAccessToken(ea);
        if (Objects.equals(subEvent, 1)) {
            // 子活动
            // 处理用户观看数据
            MarketingLiveEntity parentLive = marketingLiveDAO.getMarketingLiveByXiaoetongId(marketingLiveEntity.getMuduParentId());
            List<LiveUserStatusEntity> insertViewUserList = Lists.newArrayList();
            List<LiveUserStatusEntity> updateViewUserList = Lists.newArrayList();
            MuduApiGetRoomUserResult roomUserStatistic = getRoomUserStatistic(accessToken, xiaoetongLiveId);
            if (roomUserStatistic != null && CollectionUtils.isNotEmpty(roomUserStatistic.getItems())) {
                List<MuduApiGetRoomUserResult.Item> items = roomUserStatistic.getItems();
                for (MuduApiGetRoomUserResult.Item item : items) {
                    String phone = item.getMobile();
                    int liveWatchDuration = convertInteger(item.getLiveWatchDuration());
                    int vodWatchDuration = convertInteger(item.getVodWatchDuration());
                    int chatTimes = convertInteger(item.getChatTimes());
                    if (oldLiveUserStatusMap.containsKey(phone)) {
                        // 更新
                        LiveUserStatusEntity liveUserStatusEntity = oldLiveUserStatusMap.get(phone);
                        // 直播观看时长
                        liveUserStatusEntity.setViewTime(liveWatchDuration);
                        if (liveWatchDuration > 0) {
                            liveUserStatusEntity.setViewStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                        }
                        // 点播观看时长
                        liveUserStatusEntity.setReplayTime(vodWatchDuration);
                        if (vodWatchDuration > 0) {
                            liveUserStatusEntity.setReplayStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                        }
                        // 聊天次数
                        liveUserStatusEntity.setInteractiveCount(chatTimes);
                        liveUserStatusEntity.setLastViewTime(DateUtil.parse(item.getLastOnlineTime()));
                        updateViewUserList.add(liveUserStatusEntity);
                    } else {
                        // 新增
                        LiveUserStatusEntity liveUserStatusEntity = new LiveUserStatusEntity();
                        liveUserStatusEntity.setId(UUIDUtil.getUUID());
                        liveUserStatusEntity.initStatus(Lists.newArrayList());
                        if (liveWatchDuration > 0) {
                            liveUserStatusEntity.setViewStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                        }
                        if (vodWatchDuration > 0) {
                            liveUserStatusEntity.setReplayStatus(LiveUserStatusEnum.ACTION_PERFORMED.getStatus());
                        }
                        liveUserStatusEntity.setViewTime(liveWatchDuration);
                        liveUserStatusEntity.setReplayTime(vodWatchDuration);
                        liveUserStatusEntity.setInteractiveCount(chatTimes);
                        liveUserStatusEntity.setLastViewTime(DateUtil.parse(item.getLastOnlineTime()));
                        liveUserStatusEntity.setXiaoetongLiveId(xiaoetongLiveId);
                        liveUserStatusEntity.setType(LivePlatformEnum.MUDU.getType());
                        liveUserStatusEntity.setPhone(phone);
                        liveUserStatusEntity.setOuterUserId(Objects.toString(item.getUserId()));
                        insertViewUserList.add(liveUserStatusEntity);
                        // 生成参会人员
                        ThreadPoolUtils.execute(() -> {
                            // 查询父级活动该手机号对应的线索，在父级活动报名过，才能生成子活动的活动成员
                            List<CampaignMergeDataEntity> campaignMergeDataByPhone = campaignMergeDataDAO.getCampaignMergeDataByPhone(ea, parentLive.getMarketingEventId(), phone, false);
                            if (CollectionUtils.isNotEmpty(campaignMergeDataByPhone)) {
                                CampaignMergeDataEntity campaignMergeDataEntity = campaignMergeDataByPhone.get(0);
                                CampaignMergeDataEntity subCampaignMergeDataEntity = BeanUtil.copyProperties(campaignMergeDataEntity, CampaignMergeDataEntity.class);
                                subCampaignMergeDataEntity.setMarketingEventId(marketingEventId);
                                Map<String, Object> dataMap = campaignMergeDataEntityToCampaignMergeObjMap(ea, subCampaignMergeDataEntity, item.getNickname());
                                crmV2Manager.addCampaignMembersObjWithoutLock(ea, dataMap, subCampaignMergeDataEntity.getBindCrmObjectType(), subCampaignMergeDataEntity.getBindCrmObjectId(), marketingEventId);
                            }
                        }, ThreadPoolUtils.ThreadPoolTypeEnums.MEDIUM_BUSINESS);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(insertViewUserList)){
                PageUtil page = new PageUtil(insertViewUserList, 200);
                for (int i = 0; i < page.getPageCount(); i++){
                    List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                    liveUserStatusDAO.batchInsert(currentPage);
                }
            }
            if (CollectionUtils.isNotEmpty(updateViewUserList)){
                PageUtil page = new PageUtil(updateViewUserList, 200);
                for (int i = 0; i < page.getPageCount(); i++){
                    List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                    liveUserStatusDAO.batchUpdatePolyvData(currentPage);
                }
            }

            // 处理统计数据
            MuduApiGetRoomStatisticResult roomStatistic = getRoomStatistic(accessToken, marketingLiveEntity.getMuduParentId(), xiaoetongLiveId);
            if (CollectionUtils.isNotEmpty(marketingLiveStatistics)) {
                MarketingLiveStatistics statistics = marketingLiveStatistics.get(0);
                statistics.setTotalViewUsers(convertInteger(roomStatistic.getWatcherCount()));
                statistics.setViewTimes(convertInteger(roomStatistic.getWatchTimes()));
                statistics.setViewDuration(convertInteger(roomStatistic.getWatchDuration()));
                statistics.setTotalRecordUsers(convertInteger(roomStatistic.getVodWatcherCount()));
                statistics.setRecordTimes(convertInteger(roomStatistic.getVodWatchTimes()));
                statistics.setPerViewDuration(convertInteger(roomStatistic.getPerCapitaDuration()));
                statistics.setPerRecordDuration(convertInteger(roomStatistic.getVodPerCapitaDuration()));
                marketingLiveStatisticsDAO.update(statistics);
            }
        } else {
            // 主活动
            // 处理用户观看数据
            int totalViewUsers = 0; //驻留人数
            int viewDuration = 0; //驻留总时长
            int viewTimes = 0; //驻留次数
            int totalChatUser = 0; //互动人数
            int chatTimes = 0; //互动次数

            List<LiveUserStatusEntity> insertViewUserList = Lists.newArrayList();
            List<LiveUserStatusEntity> updateViewUserList = Lists.newArrayList();
            MuduApiGetEventUserResult eventUserStatistic = getEventUserStatistic(accessToken, xiaoetongLiveId);
            if (eventUserStatistic != null && CollectionUtils.isNotEmpty(eventUserStatistic.getItems())) {
                List<MuduApiGetEventUserResult.Item> items = eventUserStatistic.getItems();
                for (MuduApiGetEventUserResult.Item item : items) {
                    String phone = item.getMobile();
                    int roomOnlineDuration = convertInteger(item.getRoomOnlineDuration());
                    viewDuration += roomOnlineDuration;
                    if (roomOnlineDuration > 0) {
                        totalViewUsers ++;
                    }
                    int roomOnlineNum = convertInteger(item.getRoomOnlineNum());//驻留次数
                    viewTimes += roomOnlineNum;

                    int singleChatTimes = eventUserStatistic.getChatTimes(item);
                    log.info("singleChatTimes:{}", singleChatTimes);
                    if (singleChatTimes > 0) {
                        totalChatUser ++;
                    }
                    chatTimes += singleChatTimes;
                    LiveUserStatusEntity liveUserStatusEntity;
                    if (oldLiveUserStatusMap.containsKey(phone)) {
                        // 更新
                        liveUserStatusEntity = oldLiveUserStatusMap.get(phone);
                        liveUserStatusEntity.setInteractiveCount(singleChatTimes);
                        updateViewUserList.add(liveUserStatusEntity);
                    } else {
                        // 新增
                        liveUserStatusEntity = new LiveUserStatusEntity();
                        liveUserStatusEntity.setId(UUIDUtil.getUUID());
                        liveUserStatusEntity.initStatus(Lists.newArrayList());
                        liveUserStatusEntity.setXiaoetongLiveId(xiaoetongLiveId);
                        liveUserStatusEntity.setType(LivePlatformEnum.MUDU.getType());
                        liveUserStatusEntity.setPhone(phone);
                        liveUserStatusEntity.setInteractiveCount(singleChatTimes);
                        insertViewUserList.add(liveUserStatusEntity);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(insertViewUserList)){
                PageUtil page = new PageUtil(insertViewUserList, 200);
                for (int i = 0; i < page.getPageCount(); i++){
                    List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                    liveUserStatusDAO.batchInsert(currentPage);
                }
            }
            if (CollectionUtils.isNotEmpty(updateViewUserList)){
                PageUtil page = new PageUtil(updateViewUserList, 200);
                for (int i = 0; i < page.getPageCount(); i++){
                    List<LiveUserStatusEntity> currentPage = page.getPagedList(i + 1);
                    liveUserStatusDAO.batchUpdatePolyvData(currentPage);
                }
            }

            // 处理统计数据(由于目睹没有提供活动级别的统计数据接口，只能通过用户统计去计算)
            if (CollectionUtils.isNotEmpty(marketingLiveStatistics)) {
                MarketingLiveStatistics statistics = marketingLiveStatistics.get(0);
                statistics.setTotalViewUsers(totalViewUsers);
                statistics.setViewDuration(viewDuration);
                statistics.setViewTimes(viewTimes);
                statistics.setTotalChatUser(totalChatUser);
                statistics.setChatTimes(chatTimes);
                marketingLiveStatisticsDAO.update(statistics);
            }
        }
    }

    /**
     * 更新目睹活动状态（包含子活动）
     * @param ea
     */
    public void syncMuduLiveStatus(String ea){
        int ei = eieaConverter.enterpriseAccountToId(ea);
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, -1);   //最多更新一年内的直播状态
        Date limitDate = c.getTime();
        List<MarketingLiveEntity> marketingLiveEntityList = marketingLiveDAO.getSyncMuduLive(limitDate);
        if (CollectionUtils.isEmpty(marketingLiveEntityList)){
            return;
        }

        String accessToken = getAccessToken(ea);
        ListMuduEventArg arg = new ListMuduEventArg();
        arg.setPageNum(1);
        arg.setPageSize(100);
        MuduApiGetEventListResult eventListResult = getEventList(accessToken, arg);
        log.info("syncMuduLiveStatus getEventList arg:{}, result:{}", arg, eventListResult);
        if (eventListResult == null) {
            return;
        }
        List<MuduApiGetEventListResult.EventInfo> items = eventListResult.getItems();
        Map<String, MuduApiGetEventListResult.EventInfo> eventInfoMap = items.stream().collect(Collectors.toMap(MuduApiGetEventListResult.EventInfo::getEventId, Function.identity(), (v1, v2) -> v1));
        for (MarketingLiveEntity marketingLiveEntity : marketingLiveEntityList) {
            String xiaoetongLiveId = marketingLiveEntity.getXiaoetongLiveId();
            if (!eventInfoMap.containsKey(xiaoetongLiveId)) {
                break;
            }
            MuduApiGetEventListResult.EventInfo eventInfo = eventInfoMap.get(xiaoetongLiveId);
            // 主活动状态更新
            Integer eventStatus = eventInfo.getEventStatus();
            Integer status = LiveStatusEnum.NOT_START.getStatus();
            if (Objects.equals(eventStatus, 0)) {
                status = LiveStatusEnum.NOT_START.getStatus();
            } else if (Objects.equals(eventStatus, 1)) {
                status = LiveStatusEnum.PROCESSING.getStatus();
            } else if (Objects.equals(eventStatus, 2)) {
                status = LiveStatusEnum.FINISH.getStatus();
            }

            marketingLiveDAO.updateXiaoetongLiveStatus(ei, xiaoetongLiveId, status);
            marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(ei, xiaoetongLiveId, status);
            // 子活动状态更新
            MuduApiGetRoomListResult roomListResult = getRoomList(accessToken, xiaoetongLiveId);
            if (roomListResult == null) {
                break;
            }
            List<MuduApiGetRoomListResult.RoomInfo> roomInfos = roomListResult.getItems();
            if (CollectionUtils.isEmpty(roomInfos)) {
                return;
            }
            for (MuduApiGetRoomListResult.RoomInfo roomInfo : roomInfos) {
                Integer liveStatus = roomInfo.getLiveStatus();
                Integer subStatus = LiveStatusEnum.PROCESSING.getStatus();
                if (Objects.equals(liveStatus, 1)) {
                    subStatus = LiveStatusEnum.NOT_START.getStatus();
                } else if (Objects.equals(liveStatus, 2)) {
                    subStatus = LiveStatusEnum.PROCESSING.getStatus();
                } else if (Objects.equals(liveStatus, 3)) {
                    subStatus = LiveStatusEnum.PROCESSING.getStatus();
                }
                marketingLiveDAO.updateXiaoetongLiveStatus(ei, roomInfo.getRoomId(), subStatus);
                marketingLiveStatisticsDAO.updateXiaoetongLiveStatus(ei, roomInfo.getRoomId(), subStatus);
            }
        }
    }

    public int convertInteger(Integer num){
        if (num == null) {
            return 0;
        }
        return num.intValue();
    }

    private Map<String, Object> campaignMergeDataEntityToCampaignMergeObjMap(String ea, CampaignMergeDataEntity campaignMergeDataEntity, String name) {
        Map<String, Object> dataMap = Maps.newHashMap();
        CampaignMergeDataObjectTypeEnum campaignMergeDataObjectTypeEnum = CampaignMergeDataObjectTypeEnum.getByType(campaignMergeDataEntity.getBindCrmObjectType());
        if (campaignMergeDataObjectTypeEnum == null) {
            return Maps.newHashMap();
        }
        ObjectData objectData = null;
        try {
            objectData = crmV2Manager.getDetail(ea, -10000, campaignMergeDataObjectTypeEnum.getApiName(), campaignMergeDataEntity.getBindCrmObjectId());
        } catch (Exception e) {
            log.warn("MarketingEventServiceImpl -> campaignMergeDataEntityToCampaignMergeObjMap error e:{}", e);
        }
        if (objectData == null) {
            return Maps.newHashMap();
        }
        String companyName = objectData.getString("company");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(companyName)) {
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_COM_NAME.getApiName(), companyName);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(name)) {
            dataMap.put("name", name);
            dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_NAME.getApiName(), name);
        }
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.REGISTERED.getValue());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_TYPE.getApiName(), campaignMergeDataObjectTypeEnum.getApiName());
        dataMap.put(campaignMergeDataObjectTypeEnum.getBingObjectId(), campaignMergeDataEntity.getBindCrmObjectId());
        dataMap.put(CampaignMembersObjApiNameEnum.MARKETING_EVENT_ID.getApiName(), campaignMergeDataEntity.getMarketingEventId());
        dataMap.put(CampaignMembersObjApiNameEnum.CAMPAIGN_MEMBERS_STATUS.getApiName(), CampaignMembersObjMemberStatusEnum.OTHER.getValue());

        return dataMap;
    }
}
