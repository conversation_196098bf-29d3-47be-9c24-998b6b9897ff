<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:c="http://www.springframework.org/schema/c"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <bean id="userMarketingAccountMergeMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs_user_marketing_account_merge"/>

  <bean id="miniappDelaySubscribeMessageMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs-marketing-miniapp-delay-message-mq-config"/>

  <bean id="groupSmsStatusSenderMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="group_sms_status_change"/>

  <bean id="noticeStatusMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="notice_status_change"/>

  <bean id="wxUserMkActionMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="wx_user_mk_action_sender"/>

  <bean id="crmBehaviorSendEventSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="behavior_send_event_sender"/>

  <bean id="marketingStatisticsMqSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs-mankeep-marketing-statistics-mq-provider"/>
  <bean id="marketingActionRecordMqSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs-marketing-action-record-provider"/>
  <bean id="qywxAddExtenalUserSenderMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="qywx_callback_msg_mq"/>
  <bean id="officialWebsiteThirdMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="official_website_third_mq"/>
  <bean id="requestBufferMQSender" class="com.facishare.marketing.common.mq.sender.RequestBufferSender" init-method="init" c:_0="request_buffer_sender"/>
  <bean id="delayQueueMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="delay_queue_sender"/>
  <bean id="smsEventMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing-sms-sender"/>
  <bean id="qywxContactChangeMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing-qywx-contact-change-sender"/>
  <bean id="qywxCallbackMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing_qywx_sender"/>
  <bean id="advertiseCallbackMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing_advertise_sender"/>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="crmMqNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,paasObjectAction"/>
    <constructor-arg index="2" ref="crmMqNewMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="appAdminChangePushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,appAdminChange"/>
    <constructor-arg index="2" ref="appAdminItemMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="bpmNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,BpmNew"/>
    <constructor-arg index="2" ref="bpmNewMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="industryConfigPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,industrySetAction"/>
    <constructor-arg index="2" ref="industryConfigConsumer"/>
  </bean>


  <!--旷世钉钉推送 发送MQ消息-->
  <bean class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer" name="dingDingMarketingSpreadMqSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,dingSpreadAction"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="saleLeadTransferNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,saleLeadTransfer"/>
    <constructor-arg index="2" ref="saleLeadTransferMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="objTagChangeConsumerNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,objTagChange"/>
    <constructor-arg index="2" ref="objTagChangeMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingRequestBufferConsumerNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,marketingRequestBuffer"/>
    <constructor-arg index="2" ref="requestBufferMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="qywxBindEventPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,qywxBindEvent"/>
    <constructor-arg index="2" ref="qywxBindEventMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingDelayQueueConsumerNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,marketingDelayQueue"/>
    <constructor-arg index="2" ref="delayQueueMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="smsEventNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,smsQueue"/>
    <constructor-arg index="2" ref="smsEventMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="openQywxChangeNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,openQywxChangeConsumer"/>
    <constructor-arg index="2" ref="openQywxChangeMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="crmQywxDataSyncNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,crmQywxDataSyncConsumer"/>
    <constructor-arg index="2" ref="crmQywxDataSyncMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="wxUserActionMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,WechatUserActionEventMessage"/>
    <constructor-arg index="2" ref="wxUserActionMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="licenseEventMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,PaasLicense2"/>
    <constructor-arg index="2" ref="licenseEventMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="roupSmsStatusMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GROUP_SMS_STATUS_CHANGE"/>
    <constructor-arg index="2" ref="groupSmsStatusMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="mankeepActionMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,FS_MARKETING_ACTION_RECORD"/>
    <constructor-arg index="2" ref="mankeepActionMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="liveMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,LIVE_MESSAGE_TOPIC"/>
    <constructor-arg index="2" ref="liveMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="employeeChangeMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,employeeChangeTopic"/>
    <constructor-arg index="2" ref="employeeChangeMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="noticeStatusMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,NOTICE_STATUS_CHANGE"/>
    <constructor-arg index="2" ref="noticeStatusMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="qywxAddExtenalUserConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,QYWX_ADD_EXTENAL_USER"/>
    <constructor-arg index="2" ref="qywxAddExtenalUserMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="officialWebsiteThirdConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,OFFICIAL_WEBSITE_THIRD"/>
    <constructor-arg index="2" ref="officialWebsiteThirdMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="bindWxAppMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,BIND_UNBIND_WX_APP_MQ"/>
    <constructor-arg index="2" ref="bindWxAppMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="wxServiceStatusChangeMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,WECHAT_SERVICE_STATUS_CHANGE"/>
    <constructor-arg index="2" ref="wxServiceStatusChangeMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="wechatUserMessageStatusConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,WECHAT_USER_MESSAGE_STATUS"/>
    <constructor-arg index="2" ref="wechatUserMessageStatusHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="fsOpenWhatsappMsgConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,OPEN-WHATSAPP-MSG"/>
    <constructor-arg index="2" ref="openWhatsappMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="qywxCallbackConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,QYWX_CALL_BACK"/>
    <constructor-arg index="2" ref="qywxCallbackMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="advertiseConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,ADVERTISE_BACK"/>
    <constructor-arg index="2" ref="advertiseCallbackMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="fsFileProcessParseConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,FS-FILE-PROCESS-PARSE"/>
    <constructor-arg index="2" ref="fsFileProcessParseMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="crmAppViewScopeChangeNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,crmAppViewChange"/>
    <constructor-arg index="2" ref="crmAppViewChangeHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="fxEmailSendEventConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,fxEmailSendEvent"/>
    <constructor-arg index="2" ref="fxEmailSendEventHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="fsEmployeeChangeNewPushConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,fsEmployeeChange"/>
    <constructor-arg index="2" ref="fsEmployeeChangeHandler"/>
  </bean>

  <!--企微回调消息解密队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="qywxDecryptMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_QYWX_DECRYPT"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="qywxDecryptMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_QYWX_DECRYPT"/>
    <constructor-arg index="2" ref="qywxDecryptMessageHandler"/>
  </bean>

  <!--企微回调自动回复队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="qywxReplyMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_QYWX_REPLY"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="qywxReplyMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_QYWX_REPLY"/>
    <constructor-arg index="2" ref="qywxReplyMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="objMessageRebalancedSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,OBJ_MESSAGE_REBALANCED"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="objectMessageRebalancedConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,OBJ_MESSAGE_REBALANCED"/>
    <constructor-arg index="2" ref="crmMqNewMessageHandler"/>
  </bean>


  <!--==================================================gray环境生产消费bean satrt============================================-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayLightMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_LIGHT_MSG_QUEUE"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_MESSAGE_QUEUE"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="objectGrayMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_OBJ_GRAY_MESSAGE_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayLightMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_LIGHT_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_MESSAGE_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="objectGrayMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_OBJ_GRAY_MESSAGE_QUEUE"/>
    <constructor-arg index="2" ref="crmMqNewMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayActionMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_ACTION_MSG_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayActionMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_ACTION_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayLeadMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_LEAD_MSG_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayLeadMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_LEAD_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayDelayMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_DELAY_MSG_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayDelayMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_DELAY_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <!--企微回调自动回复灰度队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayQywxReplyMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_QYWX_REPLY"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayQywxReplyMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_QYWX_REPLY"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayQywxCallbackMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GRAY_QYWX_REQUEST_BUFFER"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayQywxCallbackMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GRAY_QYWX_REQUEST_BUFFER"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <!--集成平台企微消息灰度队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayOpenQywxMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GRAY_OPEN_QYWX_CALLBACK"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayOpenQywxMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GRAY_OPEN_QYWX_CALLBACK"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <!--CRM企微消息灰度队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayCrmQywxMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GRAY_QYWX_DATA_SYNC"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="grayCrmQywxMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,GRAY_QYWX_DATA_SYNC"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>
  <!--==================================================gray环境生产消费bean end============================================-->


  <!--==================================================stage环境生产消费bean satrt============================================-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageLightMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_LIGHT_MSG_QUEUE"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_MESSAGE_QUEUE"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="objectStageMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_OBJ_STAGE_MESSAGE_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageLightMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_LIGHT_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_MESSAGE_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="objectStageMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_OBJ_STAGE_MESSAGE_QUEUE"/>
    <constructor-arg index="2" ref="crmMqNewMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageActionMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_ACTION_MSG_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageActionMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_ACTION_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageLeadMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_LEAD_MSG_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageLeadMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_LEAD_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageDelayMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_DELAY_MSG_QUEUE"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageDelayMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_DELAY_MSG_QUEUE"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <!--企微回调自动回复灰度队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageQywxReplyMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_QYWX_REPLY"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageQywxReplyMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_STAGE_QYWX_REPLY"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageQywxCallbackMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,STAGE_QYWX_REQUEST_BUFFER"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageQywxCallbackMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,STAGE_QYWX_REQUEST_BUFFER"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <!--集成平台企微消息灰度队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageOpenQywxMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,STAGE_OPEN_QYWX_CALLBACK"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageOpenQywxMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,STAGE_OPEN_QYWX_CALLBACK"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>

  <!--CRM企微消息灰度队列-->
  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="stageCrmQywxMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,STAGE_QYWX_DATA_SYNC"/>
  </bean>
  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="stageCrmQywxMessageConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,STAGE_QYWX_DATA_SYNC"/>
    <constructor-arg index="2" ref="grayMessageHandler"/>
  </bean>
  <!--==================================================stage环境生产消费bean end============================================-->

</beans>