<?xml version="1.0" encoding="UTF-8"?>
<beans default-lazy-init="true" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.springframework.org/schema/beans" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

  <dubbo:application name="${dubbo.application.name}"/>
  <dubbo:registry address="${dubbo.registry.address}" register="false" check="false" />
  <dubbo:consumer check="false" filter="tracerpc" timeout="100" retries="0" connections="1"/>

  <dubbo:reference id="netDiskFolderService" interface="com.facishare.netdisk.api.service.NetDiskFolderService" timeout="100" retries="0" cache = "true" reconnect="0"/>

  <dubbo:reference id="permissionService" interface="com.facishare.organization.adapter.api.permission.service.PermissionService" timeout="100" retries="0" version="5.7"  cache = "true" reconnect="0"/>

<!--  <dubbo:reference id="employeeService" interface="com.facishare.organization.adapter.api.service.EmployeeService" retries="0" timeout="100" retries="0" cache = "true"/>-->

<!--  <dubbo:reference id="organizationService" interface="com.facishare.organization.adapter.api.business.service.OrganizationService" retries="0" timeout="100" retries="0" cache = "true"/>-->

  <!--员工 -->
  <dubbo:reference id="enterpriseEditionService" interface="com.facishare.uc.api.service.EnterpriseEditionService" protocol="dubbo" timeout="100" retries="0" cache = "true" reconnect="0" />

  <dubbo:reference id="employeeEditionService" interface="com.facishare.uc.api.service.EmployeeEditionService" protocol="dubbo" timeout="100" retries="0" cache = "true" reconnect="0" />

  <dubbo:reference id="departmentService" interface="com.facishare.organization.adapter.api.service.DepartmentService" protocol="dubbo" timeout="100" retries="0" cache = "true" reconnect="0" />

  <dubbo:reference id="departmentProviderService" interface="com.facishare.organization.api.service.DepartmentProviderService" protocol="dubbo" version="5.7" timeout="3000" retries="0" cache = "true" reconnect="0" />

  <!-- 文件加密服务 -->
  <dubbo:reference check="false" id="sharedFileService" interface="com.facishare.fsc.api.service.SharedFileService" timeout="100" retries="0" cache = "true" reconnect="0" />

  <dubbo:reference check="false" id="ActiveSessionAuthorizeService" interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService" timeout="100" retries="0" cache = "true" reconnect="0" />

  <dubbo:reference check="false" id="openAppAdminService" interface="com.facishare.open.app.center.api.service.OpenAppAdminService" timeout="2000" retries="0" version="1.3" cache = "true" reconnect="0" />

  <dubbo:reference check="false" id="contactsSearchService" interface="com.facishare.search.api.service.ContactsSearchService" timeout="100" retries="0" cache = "true" reconnect="0" />

<!--  <dubbo:reference id="employeeProviderService" interface="com.facishare.organization.api.service.EmployeeProviderService" protocol="dubbo" version="5.7" timeout="100" retries="0" cache = "true"/>-->
  <!-- 开平消息服务 -->
  <dubbo:reference id="sendMessageService" interface="com.facishare.open.msg.service.SendMessageService" timeout="100" retries="0" version="1.0" cache = "true" reconnect="0" />

  <!--部门 -->
  <dubbo:reference id="appAdminService" interface="com.facishare.open.app.center.api.service.QueryAppAdminService" protocol="dubbo" timeout="100" retries="0" version="1.0" reconnect="0" cache = "true"/>

  <!-- 客脉 -->
  <dubbo:reference check="false" id="kmUserService" interface="com.facishare.mankeep.api.service.UserService" timeout="100" retries="0" version="1.0" reconnect="0" cache = "true"/>
  <dubbo:reference check="false" id="outQRCodeService" interface="com.facishare.mankeep.api.outService.service.OutQRCodeService" timeout="100"  retries="0" reconnect="0" version="1.0" cache = "true"/>
  <dubbo:reference check="false" id="outCardService" interface="com.facishare.mankeep.api.outService.service.OutCardService" timeout="100" retries="0" reconnect="0" version="1.0"/>
  <dubbo:reference check="false" id="mankeepProductService" interface="com.facishare.mankeep.api.service.ProductService" timeout="100" retries="0" reconnect="0" version="1.0"/>
  <dubbo:reference check="false" id="mankeepArticleService" interface="com.facishare.mankeep.api.service.ArticleService" timeout="100" retries="0" reconnect="0" version="1.0"/>
  <dubbo:reference check="false" id="outEnterpriseFeedService" interface="com.facishare.mankeep.api.outService.service.OutEnterpriseFeedService" reconnect="0" timeout="100" retries="0" version="1.0" cache = "true"/>
  <dubbo:reference check="false" id="mankeepFSbindService" interface="com.facishare.mankeep.api.service.FSBindService" timeout="100" retries="0" reconnect="0" version="1.0" cache = "true"/>
  <dubbo:reference check="false" id="mankeepCustomerService" interface="com.facishare.mankeep.api.service.CustomerService" timeout="100" reconnect="0" retries="0" version="1.0" cache = "true"/>
  <dubbo:reference check="false" id="mankeepTagService" interface="com.facishare.mankeep.api.service.TagService" timeout="100" retries="0" reconnect="0" version="1.0" cache = "true"/>

  <dubbo:reference id="outerServiceWechatService" interface="com.facishare.wechat.union.core.api.service.OuterServiceWechatService" timeout="1000" reconnect="0" retries="0" version="1.0" cache = "true"/>
  <dubbo:reference id="wechatUnionService" interface="com.facishare.wechat.union.core.api.service.WechatUnionService" timeout="100" retries="0" reconnect="0" version="1.0" cache = "true"/>

  <dubbo:reference version="1.3" id="openFsUserAppViewService" interface="com.facishare.open.app.center.api.service.OpenFsUserAppViewService"  reconnect="0" timeout="100" retries="0" cache = "true"/>

  <dubbo:reference check="false" id="outCoverService" interface="com.facishare.mankeep.api.outService.service.OutCoverService" timeout="100" reconnect="0" retries="0" version="1.0" cache = "true"/>

  <dubbo:reference check="false" id="sendOpenMessageService" interface="com.facishare.open.msg.service.SendOpenMessageService" timeout="100" reconnect="0" retries="0" version="2.0" cache = "true"/>

  <dubbo:reference check="false" id="outFileService" interface="com.facishare.mankeep.api.outService.service.OutFileService" timeout="100" reconnect="0" retries="0" version="1.0" cache = "true"/>
  <!-- 微联服务号 -->
  <dubbo:reference check="false" id="weChatNoticeService" interface="com.facishare.wechat.sender.api.service.NoticeService" timeout="100"  reconnect="0" retries="0" version="1.0" cache = "true"/>

  <dubbo:reference check="false" id="fanTagService" interface="com.facishare.wechat.union.core.api.service.FanTagService" timeout="15000" reconnect="0" retries="0" version="1.0" cache = "true"/>

  <dubbo:reference check="false" id="feedService" interface="com.facishare.mankeep.api.service.FeedService" timeout="100" retries="0" reconnect="0" version="1.0" cache = "true"/>
  <dubbo:reference check="false" id="actionService" interface="com.facishare.mankeep.api.service.ActionService" timeout="100" retries="100" reconnect="0" version="1.0" cache = "true"/>

  <!-- 企业员工信息 -->
<!--  <dubbo:reference id="providerEmployeeProviderService" interface="com.facishare.organization.api.service.EmployeeProviderService" protocol="dubbo" version="5.7" cache = "true"/>-->
  <dubbo:reference id="payQuotaService" interface="com.facishare.open.app.pay.api.service.QuotaService" protocol="dubbo" version="1.0" timeout="100" reconnect="0" retries="0" cache = "true"/>

  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OutBridgeService" id="outBridgeService" version="${dubbo.provider.version}" reconnect="0" timeout="100" retries="0" cache = "true"/>

  <dubbo:reference interface="com.facishare.mankeep.api.service.IndexService" id="mankeepIndexService" version="${dubbo.provider.version}" timeout="100" reconnect="0" retries="0" />

  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OutSensorsService" id="outSensorsService" version="${dubbo.provider.version}" cache = "true" reconnect="0" timeout="100" retries="0" />

  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OutConferenceService" id="outConferenceService" version="${dubbo.provider.version}" cache = "true" reconnect="0" timeout="100" retries="0" />

  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OutDistributeService" id="outDistributeService" version="${dubbo.provider.version}" cache = "true" reconnect="0" timeout="100" retries="0"/>

  <dubbo:reference id="accountBindService" interface="com.facishare.open.webhook.accountbind.service.AccountBindService" protocol="dubbo" version="1.0" cache = "true" reconnect="0" timeout="100" retries="0"/>

  <dubbo:reference id="wxCustomerServiceMsgService" interface="com.facishare.wechat.proxy.service.WxCustomerServiceMsgService" protocol="dubbo" version="1.0" cache = "true" reconnect="0" timeout="100" retries="0" />

  <dubbo:reference id="openAppService" interface="com.facishare.open.app.center.api.service.OpenAppService"  protocol="dubbo" version="1.3"  cache = "true" timeout="100" reconnect="0" retries="0" />

  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OutArticleService" id="outArticleService"  timeout="100" reconnect="0" version="${dubbo.provider.version}" cache = "true"/>

  <!-- CRM订单接口 -->
  <dubbo:reference id="versionRegisterService" interface="com.facishare.webhook.api.service.VersionRegisterService"  timeout="100" reconnect="0" cache = "true"/>
  <dubbo:reference interface="com.facishare.mankeep.api.outService.service.OuterPhoneService" id="outerPhoneService" version="${dubbo.provider.version}" cache = "true"/>

  <dubbo:reference id="wxQrCodeService" interface="com.facishare.wechat.proxy.service.QrCodeService" version="${dubbo.provider.version}" cache = "true"  timeout="100" reconnect="0" />
  <dubbo:reference id="wechatFanService" interface="com.facishare.wechat.union.core.api.service.WechatFanService" version="${dubbo.provider.version}" cache = "true"  timeout="100" reconnect="0" />

  <dubbo:reference id="statisticMigrateService" interface="com.facishare.marketing.statistic.outapi.service.MigrateService" version="1.0" retries="0" cache = "true"  timeout="100" reconnect="0" />

  <dubbo:reference id="marketingActivityStatisticService" interface="com.facishare.marketing.statistic.outapi.service.MarketingActivityStatisticService" version="${dubbo.provider.version}" cache = "true"  timeout="100" reconnect="0"/>
  <dubbo:reference id="outMarketingObjectStatisticService" interface="com.facishare.marketing.statistic.outapi.service.ObjectStatisticService" version="${dubbo.provider.version}" cache = "true"  timeout="100" reconnect="0"/>
  <dubbo:reference id="userMarketingStatisticService" interface="com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService" version="${dubbo.provider.version}" cache = "true"  timeout="2000" reconnect="0" />
  <dubbo:reference id="contentMarketingEventStatisticService" interface="com.facishare.marketing.statistic.outapi.service.ContentMarketingEventStatisticService" version="${dubbo.provider.version}" cache = "true"  timeout="1000" reconnect="0" />

  <dubbo:reference id="openServiceForKeMai" interface="com.facishare.qixin.api.open.OpenServiceForKeMai" protocol="dubbo" timeout="100" retries="0" cache = "true"  reconnect="0"/>

  <dubbo:reference id="wechatCardService" interface="com.facishare.wechat.proxy.service.CardService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference id="wechatAuthService" interface="com.facishare.wechat.proxy.service.WechatAuthService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <!--  服务业务组-->
  <dubbo:reference id="marketingSettingBizService" interface="com.facishare.consult.out.api.service.marketing.MarketingSettingBizService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>
  <dubbo:reference id="consultantRIBizService" interface="com.facishare.consult.out.api.service.marketing.ConsultantRIBizService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0" />
  <dubbo:reference id="liveCommonService" interface="com.facishare.training.outer.api.service.live.LiveCommonService" protocol="dubbo"  version="1.0" cache = "true"  timeout="100" reconnect="0" />


  <dubbo:reference id="ssoLoginService" interface="com.facishare.userlogin.api.service.SSOLoginService" protocol="dubbo" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference id="mankeepCardService" interface="com.facishare.mankeep.api.service.CardService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference id="mankeepCustomizeFormDataService" interface="com.facishare.mankeep.api.service.CustomizeFormDataService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>
  <dubbo:reference id="mankeepActivityService" interface="com.facishare.mankeep.api.service.ActivityService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference id="outEnterpriseDefaultCardService" interface="com.facishare.mankeep.api.outService.service.OutEnterpriseDefaultCardService" protocol="dubbo" version="1.0" timeout="100" retries="0" cache = "true"  reconnect="0"/>

  <dubbo:reference id="kmVideoService" interface="com.facishare.mankeep.api.service.KmVideoService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference interface="com.facishare.wechat.proxy.service.TagService" id="wxFanTService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference interface="com.facishare.wechat.proxy.service.WechatUserTagService" id="wechatUserTagService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference interface="com.facishare.mankeep.api.service.OperatorService" id="mankeepOperatorService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0" />

  <dubbo:reference id="mankeepPrivateMessageService" interface="com.facishare.mankeep.api.service.PrivateMessageService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference id="mankeepMessageService" interface="com.facishare.mankeep.api.service.MessageService" protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference interface="com.facishare.wechat.proxy.service.WechatMessageService" id="wechatMessageService"  protocol="dubbo"  version="1.0"  cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference interface="com.facishare.marketing.statistic.outapi.service.UVStatisticService" id="uvStatisticService" version="${dubbo.provider.version}" cache = "true"  timeout="100" reconnect="0">
    <dubbo:method name="mergeKey" retries="0" timeout="30000"/>
  </dubbo:reference>

  <dubbo:reference interface="com.facishare.marketing.statistic.outapi.service.MarketingEventStatisticService" id="marketingEventStatisticService" version="${dubbo.provider.version}" cache = "true"  timeout="1000" reconnect="0"/>

  <dubbo:reference interface="com.facishare.wechat.dubborestouterapi.service.union.WeChatCustomerMenuRestService" id="weChatCustomerMenuRestService" protocol="dubbo" version="1.0"  cache = "true"  timeout="100" reconnect="0"/>

  <dubbo:reference interface="com.facishare.wechat.dubborestouterapi.service.proxy.WechatRequestDispatchService" id="wechatRequestDispatchService" protocol="dubbo" version="1.0"  cache = "true"  timeout="100" reconnect="0"/>
  <dubbo:reference version="1.3" id="appEaVisibleService" interface="com.facishare.open.app.center.api.service.AppEaVisibleService" cache = "true"  timeout="100" reconnect="0" />
  <dubbo:reference interface="com.facishare.open.app.center.api.service.QueryAppAdminService" id="queryAppAdminService"  protocol="dubbo" version="1.0" cache = "true"  timeout="100" reconnect="0" />
  <dubbo:reference id="eaPayService" interface="com.facishare.pay.enterprise.service.EAPayService" timeout="100" retries="0" version="1.0" check="false"  protocol="dubbo" cache = "true"  reconnect="0" />
  <dubbo:reference id="pushSessionService" interface="com.facishare.qixin.api.service.PushSessionService" cache = "true"  timeout="100" reconnect="0"/>
  <dubbo:reference id="userMarketingActionStatisticService" interface="com.facishare.marketing.statistic.outapi.service.UserMarketingActionStatisticService" version="1.0" cache = "true"  timeout="100" reconnect="0" />
  <dubbo:reference id="yxtAuthenticationService" interface="com.facishare.marketing.api.service.AuthenticationService" version="${dubbo.provider.version}"  timeout="100" reconnect="0" />

</beans>
