<?xml version="1.0" encoding="UTF-8"?>
<beans default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <bean id="userMarketingAccountMergeMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs_user_marketing_account_merge"/>

  <bean id="miniappDelaySubscribeMessageMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs-marketing-miniapp-delay-message-mq-config"/>

  <bean id="groupSmsStatusSenderMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="group_sms_status_change"/>

  <bean id="noticeStatusMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="notice_status_change"/>

  <bean id="wxUserMkActionMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="wx_user_mk_action_sender"/>

  <bean id="crmBehaviorSendEventSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="behavior_send_event_sender"/>

  <bean id="marketingStatisticsMqSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs-mankeep-marketing-statistics-mq-provider"/>
  <bean id="marketingActionRecordMqSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="fs-marketing-action-record-provider"/>
  <bean id="qywxAddExtenalUserSenderMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="qywx_callback_msg_mq"/>
  <bean id="officialWebsiteThirdMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="official_website_third_mq"/>
  <bean id="requestBufferMQSender" class="com.facishare.marketing.common.mq.sender.RequestBufferSender" init-method="init" c:_0="request_buffer_sender"/>
  <bean id="delayQueueMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="delay_queue_sender"/>
  <bean id="smsEventMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing-sms-sender"/>
  <bean id="qywxContactChangeMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing-qywx-contact-change-sender"/>
  <bean id="qywxCallbackMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing_qywx_sender"/>
  <bean id="advertiseCallbackMQSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init" c:_0="marketing_advertise_sender"/>

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="crmMqNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,paasObjectAction"/>-->
<!--    <constructor-arg index="2" ref="crmMqNewConsumer"/>-->
<!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="industryConfigPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,industrySetAction"/>-->
<!--    <constructor-arg index="2" ref="industryConfigConsumer"/>-->
<!--  </bean>-->

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="grayMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_GRAY_MESSAGE_QUEUE"/>
  </bean>

  <bean class="com.facishare.marketing.provider.mq.sender.MarketingMessageSender" name="objectGrayMessageSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,MARKETING_OBJ_GRAY_MESSAGE_QUEUE"/>
  </bean>

  <!--旷世钉钉推送 发送MQ消息-->
  <bean class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer" name="dingDingMarketingSpreadMqSender"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,dingSpreadAction"/>
  </bean>

  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="fsOpenWhatsappMsgConsumer"
        init-method="start" destroy-method="close">
    <constructor-arg index="0" value="fs-marketing-mq"/>
    <constructor-arg index="1" value="common,OPEN-WHATSAPP-MSG"/>
    <constructor-arg index="2" ref="openWhatsappMessageHandler"/>
  </bean>
<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="saleLeadTransferNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,saleLeadTransfer"/>-->
<!--    <constructor-arg index="2" ref="saleLeadTransferConsumer"/>-->
<!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="objTagChangeConsumerNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,objTagChange"/>-->
<!--    <constructor-arg index="2" ref="objTagChangeConsumer"/>-->
<!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingRequestBufferConsumerNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,marketingRequestBuffer"/>-->
<!--    <constructor-arg index="2" ref="requestBufferConsumer"/>-->
<!--  </bean>-->

  <!--  &lt;!&ndash; 邮件消费组 &ndash;&gt;-->
  <!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingRequestBufferForMail"-->
  <!--        init-method="start" destroy-method="close">-->
  <!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
  <!--    <constructor-arg index="1" value="common,marketingRequestBufferForMail"/>-->
  <!--    <constructor-arg index="2" ref="requestBufferConsumer"/>-->
  <!--  </bean>-->

  <!--  &lt;!&ndash; 广告消费组 &ndash;&gt;-->
  <!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingRequestBufferForAD"-->
  <!--        init-method="start" destroy-method="close">-->
  <!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
  <!--    <constructor-arg index="1" value="common,marketingRequestBufferForAD"/>-->
  <!--    <constructor-arg index="2" ref="requestBufferConsumer"/>-->
  <!--  </bean>-->

  <!--  &lt;!&ndash; 客户群消费组 &ndash;&gt;-->
  <!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingRequestBufferForWechatGroup"-->
  <!--        init-method="start" destroy-method="close">-->
  <!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
  <!--    <constructor-arg index="1" value="common,marketingRequestBufferForWechatGroup"/>-->
  <!--    <constructor-arg index="2" ref="requestBufferConsumer"/>-->
  <!--  </bean>-->

  <!--  &lt;!&ndash; 企微回调消费组 &ndash;&gt;-->
  <!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingRequestBufferForQywxCallback"-->
  <!--        init-method="start" destroy-method="close">-->
  <!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
  <!--    <constructor-arg index="1" value="common,marketingRequestBufferForQywxCallback"/>-->
  <!--    <constructor-arg index="2" ref="requestBufferConsumer"/>-->
  <!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="qywxBindEventPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,qywxBindEvent"/>-->
<!--    <constructor-arg index="2" ref="qywxBindEventConsumer"/>-->
<!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingDelayQueueConsumerNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,marketingDelayQueue"/>-->
<!--    <constructor-arg index="2" ref="delayQueueConsumer"/>-->
<!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="marketingMdsConsumerNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,mdsQueue"/>-->
<!--    <constructor-arg index="2" ref="mdsRocketMqConsumer"/>-->
<!--  </bean>-->

<!--  <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer" name="smsEventNewPushConsumer"-->
<!--        init-method="start" destroy-method="close">-->
<!--    <constructor-arg index="0" value="fs-marketing-mq"/>-->
<!--    <constructor-arg index="1" value="common,smsQueue"/>-->
<!--    <constructor-arg index="2" ref="smsEventConsumer"/>-->
<!--  </bean>-->
</beans>