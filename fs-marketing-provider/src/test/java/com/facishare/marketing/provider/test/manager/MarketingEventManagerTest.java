/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.arg.ListBriefMarketingEventsArg;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.material.AbstractMaterialData;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.result.ActionEditResult;
import com.google.common.collect.Lists;
import groovy.transform.AutoClone;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by zhengh on 2020/4/14.
 */
public class MarketingEventManagerTest extends BaseTest{

    private final static String EA = "74164";
    private final static Integer EI = 74164;
    private final static Integer FS_USERID = 1164;

    @Autowired
    private MarketingEventManager marketingEventManager;

    @Test
    public void calMarketingEventsPV(){
        String ea = "74164";
        List<String> marketingEventIds = Lists.newArrayList();
        marketingEventIds.add("5e9527cdc241340001fb543c");
        Map<String, Integer> result = marketingEventManager.calMarketingEventsPV(ea, marketingEventIds);
        System.out.print("calMarketingEventsPV result:"+result);
    }

    @Test
    public void calMarketingEventUV(){
        String ea = "74164";
        String marketingEventId = "5e9527cdc241340001fb543c";

        int uv = marketingEventManager.calMarketingEventUV(ea, marketingEventId);
        System.out.print("calMarketingEventUV uv:"+uv);
    }

    @Test
    public void listMaterialsByMarketingEvent(){
        String ea = "74164";
        Integer objectType = ObjectTypeEnum.HEXAGON_SITE.getType();
        String marketingEventId = "62e8de87b258aa0001e642b4";
        Integer pageNo = 1;
        Integer pageSize = 10;
        //String title = "精";
        com.facishare.marketing.common.result.PageResult<AbstractMaterialData>  result =marketingEventManager.listMaterialsByMarketingEvent(ea, objectType, marketingEventId, null, pageNo, pageSize);
        System.out.print("listMaterialsByMarketingEvent result:"+result);
    }

    @Test
    @Ignore
    public void syncContentMarketingEventMaterialRelationEventType(){
        marketingEventManager.syncContentMarketingEventMaterialRelationEventType();
    }

    @Test
    public void  getMarketingEventData(){
        String ea = "88146";
        Integer fsUserId = 1000;
        String id = "654234c25a97b30001379217";
        MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(ea, fsUserId, id);
        System.out.println("yes" + JSONObject.toJSONString(marketingEventData));
    }

    @Test
    public void testListMarketingEventData(){
        List<MarketingEventData> result = marketingEventManager.listMarketingEventData(EA, FS_USERID, Lists.newArrayList("62f263566617f10001c96068"));
        System.out.println("yes:" + result);
    }

    @Test
    public void testListMarketingEventDataByParentId(){
        List<MarketingEventData> result = marketingEventManager.listMarketingEventDataByParentId(EA, FS_USERID, "62f263566617f10001c96068");
        System.out.println("yes:" + result);
    }

    @Test
    public void testUpdate(){
        String parentMarketingEventId = "62f496586617f10001d615a1";
        String subMarketingEventId = "62f25e6e6617f10001c28cf9";
        Result<ActionEditResult> result = marketingEventManager.updateParentIdToCrm(EA, parentMarketingEventId, subMarketingEventId);
        System.out.println("yes:" + result);
    }

    @Test
    public void listMarketingEventsTest() {
        Integer ei = 83668;
        Integer fsUserId = -10000;
        ListBriefMarketingEventsArg arg = new ListBriefMarketingEventsArg();
        arg.setBegin(DateUtil.minusDay(new Date(), 300).getTime());
        arg.setEnd(new Date().getTime());
        arg.setQueryLimitCount(207);
        PageArg pageArg = new PageArg();
        pageArg.setPageNo(1);
        pageArg.setPageSize(20);
        PageResult<MarketingEventsBriefResult> result = marketingEventManager.listMarketingEvents(ei, fsUserId, arg, null);
        System.out.println("结果： " + result.getData().size());
    }

    @Test
    public void testBatchGetSubTotalByParentIds(){
        ArrayList<String> ids = Lists.newArrayList("64e6d4a5f4c8020001d3c211", "64e56ee74e8f5400017462ed", "64d598270b983400017eb9f9", "64d595c80b983400017e95e5",
                "64d210cc0b983400016cec9f", "64d210ae0b983400016ce8b0", "64cca550d8686d00016143ef", "64cb508bd8686d00015b0d29", "64c8b288af43d30001723243", "64be2a2450762000015b4a45");
        Map<String, Long> result = marketingEventManager.batchGetSubTotalByParentIds("88146", 1000, ids);
        System.out.printf("yes:" + result);
    }

    @Test
    public void addOrUpdateAdSourceFieldTest() {
        marketingEventManager.addOrUpdateAdSourceField("74164");
    }

    @Test
    public void addOrUpdateAdSourceFieldWithLinkedin() {
        marketingEventManager.addOrUpdateAdSourceFieldWithLinkedin("88146");
    }


    @Test
    public void addOCPCLaunchFieldTest() {
        marketingEventManager.addOCPCLaunchField("88146");
    }

    @Test
    public void addAdCampaignId() {
        marketingEventManager.addAdCampaignId("74164");
    }

    @Test
    public void addAdvertisingTypeOption() {
        marketingEventManager.addAdvertisingTypeOption("83668");
    }
}
