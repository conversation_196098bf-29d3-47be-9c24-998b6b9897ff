package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.ListActivityListArg;
import com.facishare.marketing.api.arg.ListContentListArg;
import com.facishare.marketing.api.arg.ListFileListArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.ListActivityResult;
import com.facishare.marketing.api.result.ListContentResult;
import com.facishare.marketing.api.result.fileLibrary.ListFileListResult;
import com.facishare.marketing.api.service.CustomizeContentService;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

@Slf4j
public class CustomizeContentServiceTest extends BaseTest{
    @Autowired
    private CustomizeContentService customizeContentService;

    @Test
    public void listActivityList(){
        /*
        String a = "{\n" +
                "    \"isAllActivityType\": false,\n" +
                "    \"materialTagFilter\": {\n" +
                "        \"type\": 1,\n" +
                "        \"materialTagIds\": [\n" +
                "            \"924d7bbe11534c50b997a731499daaf5\"\n" +
                "        ]\n" +
                "    },\n" +
                "    \"objectId\": \"6451f8a6762b4a50a5946d38a6872370\",\n" +
                "    \"objectType\": 26,\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 12,\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\",\n" +
                "    \"referer\": \"https://crm.ceshi112.com/proj/page/marketing-page?ea=88146&id=6451f8a6762b4a50a5946d38a6872370&type=1\",\n" +
                "    \"timestamp\": 1698301689574,\n" +
                "    \"nonce\": \"e852382a04581890\",\n" +
                "    \"sign\": \"fb6cf57760309449cba409e37605774f\"\n" +
                "}";
        ListActivityListArg arg = JSON.parseObject(a, ListActivityListArg.class);

         */
        ListActivityListArg arg = new ListActivityListArg();
        arg.setActivityTypes(Lists.newArrayList("live_marketing"));
        arg.setIsAllActivityType(true);
        arg.setPageSize(10);
        arg.setPageNum(1);
//        arg.setObjectId("70d33322e9264d59895313b43a58bfcc");
//        arg.setObjectType(26);
//        arg.setPageNum(1);
//        arg.setPageSize(10);
//        arg.setIsAllActivityType(false);
//        arg.setOrderByIds(true);
//        arg.setMarketingEventIds(Lists.newArrayList());
        Result<PageResult<ListActivityResult>>  result = customizeContentService.listActivityList("88146", arg);
        log.info("listActivityList result:{}", result);
    }

    @Test
    public void listContentList(){
        ListContentListArg arg = new ListContentListArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
////        ArrayList<String> objects = Lists.newArrayList(
////
////                "f9926658207f4021947d94c0f4fb6d7d",
////                "ed5296aa75ea4f3e99d11392eba19c42",
////                "0cfeba15f7514eebaaf87c4a27cc4280",
////                "6e8f15f575bf47a6935e01b172e6bc19",
////                "cbe181c1b0914a4fb679a62537a571fa",
////                "825c24130ec444acb18b249f72415111",
////                "754ab14c6b8140908ce4da9ee4b3a058",
////                "ed6364141b7f4e1d9e1629ab687276fb",
////                "544f9a5dbe114286b6d193d665a8185f",
////                "231d7022b79b497788789eb07eed34ac",
////                "df710ae7483948648d97b0b4a0f459d6",
////                "9852ee3d144e4096be6b001a48f9a8ee",
////                "c35cd597b44e4371804cd3ca3c8a33ff"
////
////);
////
////        arg.setContentObjectIds(objects);
//        arg.setContentObjectIds(Lists.newArrayList(
//                "c147be6d6e85444b8897a3b"
//        ));
        arg.setContentObjectType(6);
        arg.setGroupId("-1");
    //    arg.setOrderByIds(true);
        Result<PageResult<ListContentResult>> result = customizeContentService.listContentList("88146", arg);
        log.info("listContentList result:{}", result);
    }

    @Test
    public void listFileList(){
        ListFileListArg arg = new ListFileListArg();
        arg.setPageNum(1);
        arg.setPageSize(10);
//        arg.setGroupId("-1");
//        Result<PageResult<ListFileListResult>> result = customizeContentService.listFileList("88146", arg);
//        log.info("listContentList result:{}", result);
//        arg.setGroupId("5c402e7ee0d74ecfbec54369540937d2");
        arg.setGroupId("-1");
//        Result<PageResult<ListFileListResult>> result1 = customizeContentService.listFileList("88146", arg);
//        log.info("listContentList result1:{}", result1);
//        MaterialTagFilterArg arg1 = new MaterialTagFilterArg();
//        arg1.setType(1);
//        arg1.setMaterialTagIds(Lists.newArrayList("428c83d5da334962acbfcfe4ff3baf38", "7f2ce321525a43d88dfb5ceafb71a401", "94b5a6005ea7483187ab9b18c281d4d0",
//                "4a9b724e0d6d493e9e0934c62828f5c0", "c32b778660a84cb0b8a1adb3397a9a98"));
//        arg.setMaterialTagFilter(arg1);
//        arg.setContentObjectIds(Lists.newArrayList("a2aa2dbc7f634e3fa4e38c436a7c3b71","f9eac4fdbff44819b434abe0781602c7","bffad86d46ac42baa0afe3ec5a91925c","8fb641a6d21d46ee85dc9113f8937fb4","c7fbcd9c2f0d4fa8970338608593ba23","2a3f4c68d2924a3296cd6ecf4e4f1ae3","cfd717fa06044ec8ae416e6278fd1622","be74deb5e6124e59ac6c78ebb93d0318"));
//        arg.setOrderByIds(true);
        Result<PageResult<ListFileListResult>> result1 = customizeContentService.listFileList("88146", arg);
        log.info("listContentList result1:{}", result1);
    }



}
