package com.facishare.marketing.provider.test.service.marketingplugin;

import com.facishare.marketing.api.result.marketingplugin.MarketingPluginInfoResult;
import com.facishare.marketing.api.service.marketingplugin.MarketingPluginService;
import com.facishare.marketing.api.vo.marketingplugin.MarketingPluginConfigVo;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.manager.MarketingPluginConfigManager;
import com.facishare.marketing.provider.manager.crmobjectcreator.CouponInstanceObjDescribeManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.Cookie;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/9/7 17:52
 */
@Slf4j
public class MarketingPluginConfigServiceTest extends BaseTest {

    @Autowired
    private MarketingPluginService marketingPluginService;

    @Autowired
    private CouponInstanceObjDescribeManager couponInstanceObjDescribeManager;


    @Test
    public void savePluginConfig(){
        MarketingPluginConfigVo vo = new MarketingPluginConfigVo();
        vo.setEa("74164");
        vo.setPluginType(1);
        vo.setStatus(true);
        Result<Void> voidResult = marketingPluginService.saveMarketingPluginConfig(vo);
        log.info("== result:{}",voidResult);
    }

    @Test
    public void queryMarketingPlugin(){
        Result<MarketingPluginInfoResult> marketingPluginInfoResultResult = marketingPluginService.queryMarketingPluginInfo("7416674", 1);
        log.info("=== result : {}",marketingPluginInfoResultResult);
    }

    @Test
    public void queryMarketingPluginByEa(){
        Result<List<MarketingPluginInfoResult>> result = marketingPluginService.queryMarketingPluginByEa("74164");
        log.info("=== result : {}",result);
    }

    @Test
    public void updateMarketingPlugin(){
        String ea = "74164";
        Integer pluginType = 14;
        Boolean status = true;
        Result<Void> voidResult = marketingPluginService.saveOrUpdateMarketingPlugin(ea, pluginType, status);
        log.info("=== result : {}",voidResult);
    }

    @Test
    public void refreshCouponInstanceObj(){
        boolean flag = couponInstanceObjDescribeManager.addCouponInstanceFieldDescribe("84126");
        log.info("flag="+flag);
    }

    @Test
    public void updateMemberMarketingPlugin(){
        String ea = "88146";
        Integer pluginType = 63;
        Boolean status = true;
        Result<Void> voidResult = marketingPluginService.saveOrUpdateMarketingPlugin(ea, pluginType, status);
        log.info("=== result : {}",voidResult);
    }
}
