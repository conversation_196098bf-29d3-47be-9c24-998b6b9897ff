package com.facishare.marketing.provider.test;

/**
 * Created by zhengh on 2018/5/3.
 */

import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by yuanj on 2017/3/25.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:applicationContext-zfy-test.xml")
public abstract class BaseTest {
    @BeforeClass
    public static void setUp() {
        System.setProperty("process.profile", "fstest-gray");
        System.setProperty("process.profile.candidates", "fstest-gray,fstest");
        System.setProperty("process.name", "fs-marketing-provider");
    }
}