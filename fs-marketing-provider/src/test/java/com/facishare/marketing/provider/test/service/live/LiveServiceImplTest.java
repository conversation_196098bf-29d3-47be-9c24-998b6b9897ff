/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.live;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.CreateObjectDataModel;
import com.facishare.marketing.api.result.QueryFormUserDataResult;
import com.facishare.marketing.api.result.live.*;
import com.facishare.marketing.api.service.live.LiveService;
import com.facishare.marketing.api.vo.live.*;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.live.LivePlatformEnum;
import com.facishare.marketing.common.enums.live.LiveRoleEnum;
import com.facishare.marketing.common.enums.live.LiveUserStatusEnum;
import com.facishare.marketing.common.enums.live.ViewLiveEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.manager.CampaignMergeDataManager;
import com.facishare.marketing.provider.manager.XiaoetongManager;
import com.facishare.marketing.provider.manager.kis.AppVersionManager;
import com.facishare.marketing.provider.manager.live.ChannelsManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.gson.Gson;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by zhengh on 2020/3/20.
 */
@Slf4j
public class LiveServiceImplTest extends BaseTest{

    private final static String EA = "74164";

    @Autowired
    private LiveService liveService;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;
    @Autowired
    private XiaoetongManager xiaoetongManager;
    @Autowired
    private CampaignMergeDataManager campaignMergeDataManager;
    @Autowired
    private AppVersionManager appVersionManager;

    @Test
    public void createLive(){
        String a = "{\n" +
                "  \"title\": \"视频号直播asd355\",\n" +
                "  \"startTime\": *************,\n" +
                "  \"endTime\": *************,\n" +
                "  \"coverTaPath\": \"A_202311_06_49d508d5b3b948c48f807dbaef2964e3.jpg\",\n" +
                "  \"ext\": \"\",\n" +
                "  \"lectureUserName\": \"视频号直播asd355\",\n" +
                "  \"lecturePassword\": \"0000\",\n" +
                "  \"livePlatform\": 5,\n" +
                "  \"chatOn\": 0,\n" +
                "  \"liveLimit\": 1,\n" +
                "  \"autoRecord\": 1,\n" +
                "  \"tagNames\": [],\n" +
                "  \"otherPlatformLiveUrl\": \"\",\n" +
                "  \"showActivityList\": false,\n" +
                "  \"marketingTemplateId\": \"82a6d8611fca4bf99d6ff8208d16f287\",\n" +
                "  \"originalImageAPath\": \"A_202310_26_3f9416d9f375483b8d00894e25cf969d.webp\",\n" +
                "  \"cutOffsetList\": [\n" +
                "    {\n" +
                "      \"width\": 1038,\n" +
                "      \"height\": 577,\n" +
                "      \"top\": 0,\n" +
                "      \"left\": 57,\n" +
                "      \"photoTargetType\": 45,\n" +
                "      \"image\": \"https://crm.ceshi112.com/image/extract?ea=7&path=C_202311_06_0b33c74f8884456b866401fdffc88769&left=57&top=0&width=1038&height=577\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"width\": 721,\n" +
                "      \"height\": 577,\n" +
                "      \"top\": 0,\n" +
                "      \"left\": 216,\n" +
                "      \"photoTargetType\": 43,\n" +
                "      \"image\": \"https://crm.ceshi112.com/image/extract?ea=7&path=C_202311_06_0b33c74f8884456b866401fdffc88769&left=216&top=0&width=721&height=577\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"width\": 577,\n" +
                "      \"height\": 577,\n" +
                "      \"top\": 0,\n" +
                "      \"left\": 288,\n" +
                "      \"photoTargetType\": 44,\n" +
                "      \"image\": \"https://crm.ceshi112.com/image/extract?ea=7&path=C_202311_06_0b33c74f8884456b866401fdffc88769&left=288&top=0&width=577&height=577\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"isMobileDisplay\": true,\n" +
                "  \"associatedAccountId\": \"e38f42f002c3419f8c9333dc969d6f77\",\n" +
                "  \"desc\": \"视频号直播asd视频号直播asd视频号直播asd\",\n" +
                "  \"createObjectDataModel\": {\n" +
                "    \"objectData\": {\n" +
                "      \"object_describe_api_name\": \"MarketingEventObj\",\n" +
                "      \"object_describe_id\": \"63e5ecaf7fbcad0001c32f36\",\n" +
                "      \"expected_cost\": \"120\",\n" +
                "      \"data_own_organization\": [\n" +
                "        \"1020\"\n" +
                "      ],\n" +
                "      \"begin_time\": *************,\n" +
                "      \"end_time\": *************,\n" +
                "      \"name\": \"视频号直播asd\",\n" +
                "      \"event_type\": \"live_marketing\",\n" +
                "      \"owner\": [\n" +
                "        \"1000\"\n" +
                "      ],\n" +
                "      \"is_mobile_display\": \"0\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"parentId\": \"\"\n" +
                "}";
        CreateLiveVO vo = JSON.parseObject(a,CreateLiveVO.class);
        vo.setEa("88146");
        vo.setFsUserId(1000);
        Result<CreateMarketingLiveResult> result = liveService.createLive(vo);
        System.out.println("createLive Result:"+ result);
    }

    @Test
    public void createLive2(){
        CreateLiveVO vo = new CreateLiveVO();
        vo.setEa("74164");
        vo.setCorpId(74164);
        vo.setFsUserId(1071);
        vo.setTitle("营销直播3");
        vo.setStartTime(System.currentTimeMillis());
        vo.setEndTime(System.currentTimeMillis() + 3600 * 1000);
        vo.setLecturePassword("1234");
        vo.setLectureUserName("harry");
        vo.setChatOn(1);
        vo.setAutoRecord(1);
        vo.setMaxLiveCount(1000);
        vo.setOtherPlatformLiveUrl("http://www.baidu.com");
        vo.setLivePlatform(LivePlatformEnum.FS_COOPERATION_PLATFORM.getType());
        vo.setCoverTaPath("TA_d9f8e7bcdc9f4a4784e651f3e10f5506");
        vo.setExt("jpg");
        TagNameList tagNames = new TagNameList();
        TagName tagName = new TagName();
        tagName.setFirstTagName("first tag");
        tagName.setSecondTagName("second tag");
        tagNames.add(tagName);
        vo.setTagNames(tagNames);


        Result<CreateMarketingLiveResult> result = liveService.createLive(vo);
        System.out.println("createLive Result:"+ result);
    }

    @Test
    public void list(){
        ListVO arg = new ListVO();
        arg.setEi(74164);
        arg.setEa("74164");
        arg.setFsUserId(1167);
        arg.setPageNum(1);
        arg.setPageSize(10);
     //   arg.setKeyword("zhifuzhibo");
        Result<PageResult<ListResult>> result = liveService.list(arg);
        System.out.print("list result:"+ result);
    }

    @Test
    public void appList(){
        ListVO arg = new ListVO();
        arg.setEi(88146);
        arg.setEa("88146");
        arg.setFsUserId(1030);
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setMenuId("abb48fed5f4e4adc8c4d67da44092784");
        //   arg.setKeyword("zhifuzhibo");
        Result<PageResult<ListResult>> result = liveService.appList(arg);
        log.info("结果： {}", JsonUtil.toJson(result));
    }

    @Test
    public void testIsFxCloud(){
        boolean fxCloud = appVersionManager.isFxCloud();
        System.out.println("yes:" + fxCloud);
    }

    @Test
    public void getDetail(){
        String id = "6006cddfc389670001bd3b04";
        //Result<GetLiveDetailResult> result = liveService.getDetail(id, LiveRoleEnum.LECTURE_ROLE.getRole(), null, null, null, null);
        //System.out.print("getDetail result:"+result);
    }

    @Test
    public void getLectureUrl(){
        String id = "9840679b61374927bf87bef7507e1613";
        String lecturePassword = "0000";
        Result<LiveLectureUrlResult> result = liveService.getLectureUrl(id, lecturePassword,null);
        System.out.print("getLectureUrl result:"+ result);
    }


    @Test
    public void updateLive1(){
        CreateLiveVO vo = new CreateLiveVO();
        vo.setId("b43f9e1e13204882aef71a7c95faaa10");
        vo.setTitle("update live");
        vo.setOtherPlatformLiveUrl("http://www.sohu.com");
        vo.setCoverTaPath("TA_111111");
        vo.setStartTime(new Date().getTime());
        vo.setEndTime(new Date().getTime());
        vo.setLivePlatform(2);
        Result<CreateMarketingLiveResult> result = liveService.updateLive(vo);
        System.out.print("updateLive1 result:"+result);
    }

    @Test
    public void update2(){
        CreateLiveVO vo = new CreateLiveVO();
        vo.setId("9840679b61374927bf87bef7507e1613");
        vo.setTitle("update live");
        vo.setOtherPlatformLiveUrl("http://www.sohu.com");
        vo.setStartTime(new Date().getTime());
        vo.setEndTime(new Date().getTime());
        vo.setLivePlatform(1);
        vo.setChatOn(1);
        vo.setAutoRecord(1);
        vo.setMaxLiveCount(1000);
        vo.setCoverTaPath("http://www.baidu");
        Result<CreateMarketingLiveResult> result = liveService.updateLive(vo);
        System.out.print("update2 result:"+result);
    }

    @Test
    public void liveUpdate3(){

    }

    @Test
    public void getLiveLeadDetail(){
        //{"pageNum":1,"pageSize":10,"totalCount":0,"viewLiveStatus":2,"marketingEventId":"5f5f550d9d328b00012269d3"}
        LiveLeadDetailVO vo = new LiveLeadDetailVO();
        vo.setEa("74164");
        vo.setFsUserId(1112);
        vo.setMarketingEventId("5fbe0c2cfd39f40001626929");
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setViewLiveStatus(ViewLiveEnum.ALL.getStatus());
        Result<PageResult<QueryFormUserDataResult>> result = liveService.getLiveLeadDetail(vo);
        System.out.println("getLiveLeadDetail result:"+result);
    }

    @Test
    public void getViewUrl(){
        GetLiveViewUrlVO vo = new GetLiveViewUrlVO();
        vo.setId("b9d222b07a9d4600a5c194edfd7f45d8");
        vo.setName("zhenghui");
        vo.setPhone("15162255036");
        Result<GetViewUrlResult> result = liveService.getViewUrl(vo);
        System.out.println("getViewUrl result:"+result);
    }

    @Test
    public void  checkUserInMarketingLive(){
        Result<Boolean> result = liveService.checkUserInMarketingLive("52a8b8c49bd14323832a06451e68c34d", "4f76d050f0ba4669a657daf3f75d657a");
        System.out.println("getViewUrl result:"+result);
    }


    @Test
    public void queryMarketingEvent(){
        String marketingEventId = "5e914d0c54c16b000100bf82";
        ObjectData objectData = crmV2Manager.getDetail("74164", -10000, CrmObjectApiNameEnum.MARKETING_EVENT.getName(), marketingEventId);
        if (objectData == null) {
            System.out.print("xxxxxx");
        }
        System.out.print("queryMarketingEvent:"+objectData);
    }

    @Test
    public void getTags(){
        Gson  gson = new Gson();
         MarketingLiveEntity entity = marketingLiveDAO.getByMarketingEventId("5e9527cdc241340001fb543c");
        String tags = entity.getTags();
        TagNameList list = gson.fromJson(tags, TagNameList.class);
        System.out.print("list:"+list);
    }

    @Test
    public void copyHexagon(){
        CreateLiveVO vo = new CreateLiveVO();
        vo.setEa("74164");
        vo.setFsUserId(1071);
        String marketingEventId = "5e981d1f626b490001ffb60e";
        String apath = "A_202004_16_8678c92a9bce43768dd55238c676c193.jpg";
      //  String a  = liveService.copyHexagon(vo, marketingEventId, apath);
    }

    @Test
    public void getLiveBriefStatistics() {
        String ea = "74164";
        String marketingEventId = "5fbfd7866e956200014a2f25";
        List<Integer> objectTypes = Lists.newArrayList();
        objectTypes.add(4);
        objectTypes.add(6);
        objectTypes.add(26);
        Result<LiveBriefStatisticsResult>  result = liveService.getLiveBriefStatistics(ea, marketingEventId, objectTypes);
        System.out.print("getLiveBriefStatistics:"+  result);
    }

    @Test
    public void queryLiveEnrollList() {
        QueryLiveEnrollListVO vo = new QueryLiveEnrollListVO();
        vo.setEa("88146");
        vo.setMarketingEventId("65965df5c6f30e0007ed1d5b");
        vo.setFsUserId(1000);
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setViewLiveStatus(Lists.newArrayList(ViewLiveEnum.HAS_VIEWED_LIVE.getStatus()));
 //       vo.setLiveReplayStatus(Lists.newArrayList(LiveUserStatusEnum.ACTION_NOT_PERFORMED.getStatus()));
    //    vo.setInteractiveStatus(Lists.newArrayList(LiveUserStatusEnum.ACTION_NOT_PERFORMED.getStatus()));
     //   vo.setSaveCrmStatus(Lists.newArrayList(0,1,99));
    //    vo.setChannelValue(Lists.newArrayList("wechat", "sms"));
        Result<PageResult<QueryLiveEnrollListResult>>  result = liveService.queryLiveEnrollList(vo);
        log.info("queryLiveEnrollList result:{}", result);
    }


    @Test
    public void getDetailLiveTest() {
        Result<GetLiveDetailResult> live = liveService.getDetailLive("22318a60ed2c4529b88f767c1ae6a2c0");
        System.out.println("channelLive"+ live);
    }

    @Test
    public void exportLiveEnrollList() throws InterruptedException {
        QueryLiveEnrollListVO vo = new QueryLiveEnrollListVO();
        vo.setEa("74164");
        vo.setMarketingEventId("632bca853477690001d6c63c");
        vo.setFsUserId(1177);
        liveService.exportLiveEnrollList(vo);
    //    Thread.sleep(10000L);
    }


    @Test
    public void checkAndSyncUserToXiaoetong(){
        String liveId = "a7bde242d2dd4e448e11632079a5e477";
        String phone = "15989463965";
        liveService.checkAndSyncUserToXiaoetong(liveId, phone, null);
    }

    @Test
    public void getMarketingEventIdByXiaoetongId(){
        String xiaoetongId = "l_60dc11f4e4b06d36718b9b24";
        Result<GetMarketingLiveByXiaoetongIdResult>  result = liveService.getMarketingLiveByXiaoetongId(xiaoetongId);
        log.info("getMarketingEventIdByXiaoetongId result:{}", result);
    }

    @Test
    public void polyvLiveList(){
        ListVO arg = new ListVO();
        arg.setEa("74164");
        arg.setFsUserId(1145);
        arg.setPageNum(1);
        arg.setPageSize(10);
        liveService.polyvLiveList(arg);
    }

    @Test
    public void testBindChannelsAccount(){
        BindChannelsAccountVo vo = new BindChannelsAccountVo();
        vo.setEa(EA);
        vo.setChannelsId("id123");
        vo.setChannelsName("name123");
        vo.setChannelsAvatar("avatar123");
        Result<Void> result = liveService.bindChannelsAccount(vo);
        System.out.println(result);
    }

    @Test
    public void testGetChannelsAccount(){
        Result<ChannelsAccountResult> result = liveService.getChannelsAccount(EA,"");
        System.out.println(result);
    }

    @Test
    public void testGetAccountByMaterials(){
        LiveMaterialsVO vo = new LiveMaterialsVO();
        vo.setObjectId("b2ae721115d241f58fc93008462d7644");
        vo.setObjectType(6);
        Result<ChannelsAccountResult> accountByMaterials = liveService.getAccountByMaterials(vo);
        System.out.println(accountByMaterials);
    }

    @Test
    public void testGetData(){
        String ea = "74164";
        ArrayList<String> ids = Lists.newArrayList("6397e31e25ccd000012a50c6");
        List<ObjectData> result = campaignMergeDataManager.getCampaignMembersObjByMaketingEventIdsWithFields(ea, ids);
        System.out.println("yes:" + result);
        List<ObjectData> result2 = campaignMergeDataManager.getCampaignMembersObjByMaketingEventIds(ea, ids);
        System.out.println("yes2:" + result2);
    }
    @Test
    public void getChannelsAccountByEa(){
        String ea = "88146";
        Result<List<ChannelsAccountResult>> channelsAccountByEa = liveService.getChannelsAccountByEa(ea);
        System.out.println("yes2:" + channelsAccountByEa);
    }

    @Test
    public void testCheckPolyvSubmit(){
        String liveId = "44303690f17243dc8412a52aed1415d5";
        String phone = "***********";
        Result<CheckAndSyncUserToXiaoetongResult> result = liveService.checkPolyvSubmit(liveId, phone);
        System.out.println("yes:" + result);
    }
}
