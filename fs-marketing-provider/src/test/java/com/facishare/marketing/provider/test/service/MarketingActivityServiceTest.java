/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg.MarketingActivityGroupSenderVO;
import com.facishare.marketing.api.arg.marketingactivity.AddMarketingActivityArg.MaterialInfo;
import com.facishare.marketing.api.arg.marketingactivity.BatchGetSendNotificationStatisticArg;
import com.facishare.marketing.api.arg.marketingactivity.GetMarketingActivityArg;
import com.facishare.marketing.api.arg.marketingactivity.UpdateMarketingActivityBaseInfoArg;
import com.facishare.marketing.api.result.marketingactivity.AddMarketingActivityResult;
import com.facishare.marketing.api.result.marketingactivity.BatchGetSendNotificationStatisticResult;
import com.facishare.marketing.api.result.marketingactivity.GetMarketingActivityResult;
import com.facishare.marketing.api.service.marketingactivity.MarketingActivityService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;

import java.util.*;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class MarketingActivityServiceTest extends BaseTest {
    @Autowired
    private MarketingActivityService marketingActivityService;

    @Test
    public void listWxMarketingActivity() {
        String string1 = "74164";
        Integer integer1 = 1145;
        String string2 = "FSAID_10b07a9b";
        Integer integer2 = 1;
        Integer integer3 = 5;
        Result result = marketingActivityService.listWxMarketingActivity(string1, integer1, "", string2, integer2, integer3);

    }

    @Test
    public void getMarketingActivity() {
        GetMarketingActivityArg getMarketingActivityArg = new GetMarketingActivityArg();
        getMarketingActivityArg.setId("66b47dc863ef5b00014fa3e5");
        getMarketingActivityArg.setPartner(false);
        Result<GetMarketingActivityResult> result = marketingActivityService.getMarketingActivity("88146", 1000, getMarketingActivityArg);
        System.out.println("--------------");
        System.out.println(result);
        System.out.println("sendRangeDetail:" + JsonUtil.toJson(result.getData()));
    }

    @Test
    public void batchGetSendNotificationStatistic() {
        BatchGetSendNotificationStatisticArg arg = new BatchGetSendNotificationStatisticArg();
        arg.setCampaignIds(Lists.newArrayList("4dc990ed700a416c8cc67ef735c261af"));
        arg.setSpreadTypes(Lists.newArrayList(2,3,5,6));
        Result<BatchGetSendNotificationStatisticResult> batchGetSendNotificationStatisticResultResult = marketingActivityService.batchGetSendNotificationStatistic("74164", 1112, arg);
        System.out.println(batchGetSendNotificationStatisticResultResult);
    }

    @Test
    public void addMarketingActivity() {
        AddMarketingActivityArg arg = new AddMarketingActivityArg();
        MarketingActivityGroupSenderVO marketingActivityGroupSenderVO = new MarketingActivityGroupSenderVO();
        marketingActivityGroupSenderVO.setEventType(1);
        List<String> marketingUserGroupIds = new ArrayList<>();
        marketingUserGroupIds.add("dc2f43117c4d4e5982905a6299b9eec9");
        marketingActivityGroupSenderVO.setMarketingUserGroupIds(marketingUserGroupIds);
        marketingActivityGroupSenderVO.setSceneType(3);
        marketingActivityGroupSenderVO.setSendRange(2);
        Map<String, String> shortUrlMap = new HashMap<>();
        shortUrlMap.put("https://fs8.ceshi112.com/q883Vq", "https://www.ceshi112.com/ec/cml-marketing/release/web/cml-marketing.html?byshare=1&_hash=/cml/h5/conference_detail&id=eb56b31abd874d17bae0a148d8a72918&channelType=6&ea=74164&marketingActivityId=");
        shortUrlMap.put("https://wxaurl.cn/7zE7il6yESe", "/pages/share/share?objectType=13&objectId=eb56b31abd874d17bae0a148d8a72918&channelType=6&ea=74164&marketingActivityId=");
        marketingActivityGroupSenderVO.setShortUrlMap(shortUrlMap);
        marketingActivityGroupSenderVO.setTemplateContent("{location}  点击https://wxaurl.cn/7zE7il6yESe  点击https://fs8.ceshi112.com/q883Vq");
        marketingActivityGroupSenderVO.setTemplateName("testtest");
        marketingActivityGroupSenderVO.setType(3);
        arg.setMarketingActivityGroupSenderVO(marketingActivityGroupSenderVO);
        arg.setMarketingEventId("60dc55fbc45ed50001f984e9");
        List<MaterialInfo> materialInfos = new ArrayList<>();
        MaterialInfo m0 = new MaterialInfo("eb56b31abd874d17bae0a148d8a72918", 3);
        MaterialInfo m1 = new MaterialInfo("eb56b31abd874d17bae0a148d8a72918", 3);
        materialInfos.add(m0);
        materialInfos.add(m1);
        arg.setMaterialInfos(materialInfos);
        arg.setSpreadType(3);
        marketingActivityService.addMarketingActivity("74164", 1128, arg, true);
    }

    /**
     * 创建伙伴营销
     */
    @Test
    public void addPartnerMarketingActivity(){
//        AddMarketingActivityArg arg = new AddMarketingActivityArg();
//        AddMarketingActivityArg.MarketingActivityPartnerNoticeSendVO partnerNoticeSendVO = new AddMarketingActivityArg.MarketingActivityPartnerNoticeSendVO();
//        partnerNoticeSendVO.setContent("164efdb2de74457cbaf8c68fa5f802d2");
//        partnerNoticeSendVO.setContentType(10);
//        partnerNoticeSendVO.setCoverPath("A_202110_08_83c7b7dbcff448b4a5ed35488dc03ac9.jpg");
//        partnerNoticeSendVO.setDescription("1234");
//        partnerNoticeSendVO.setEndTime(1633920489568L);
//        partnerNoticeSendVO.setStartTime(1633661289568L);
//        partnerNoticeSendVO.setSendType(1);
//        partnerNoticeSendVO.setTitle("test");
//        AddMarketingActivityArg.PartnerNoticeVisibilityArg visibilityArg = new AddMarketingActivityArg.PartnerNoticeVisibilityArg();
//        visibilityArg.setEaList(Arrays.asList("300016575"));
//        visibilityArg.setTenantGroupIdList(Arrays.asList("300016575"));
//        partnerNoticeSendVO.setPartnerNoticeVisibilityArg(visibilityArg);
//        arg.setMarketingActivityPartnerNoticeSendVO(partnerNoticeSendVO);
//        arg.setMarketingEventId("615562ef1513ad000121e240");
//        arg.setSpreadType(7);
//        arg.setMaterialInfos(Arrays.asList(new MaterialInfo("164efdb2de74457cbaf8c68fa5f802d2", 10)));
//        marketingActivityService.addMarketingActivity("74164", 1140, arg, true);

        String a="{\n" +
                "    \"spreadType\": 7,\n" +
                "    \"marketingEventId\": \"64dee5d24fbb62000147219c\",\n" +
                "    \"marketingActivityAuditData\": {\n" +
                "        \"sendLink\": \"https://crm.ceshi112.com/hcrm/web/h5-marketing/index.html#/article/detail?marketingActivityId=&wxAppId=&ea=88146&objectId=2a81952d83ae4d1fa714c7e58a67aa32\"\n" +
                "    },\n" +
                "    \"marketingActivityPartnerNoticeSendVO\": {\n" +
                "        \"title\": \"我推文章3\",\n" +
                "        \"content\": \"2a81952d83ae4d1fa714c7e58a67aa32\",\n" +
                "        \"contentType\": 1,\n" +
                "        \"sendType\": 1,\n" +
                "        \"startTime\": 1692341514274,\n" +
                "        \"endTime\": 1692600714274,\n" +
                "        \"description\": \"我推文章3\",\n" +
                "        \"staffInfoShow\": 0,\n" +
                "        \"partnerNoticeVisibilityArg\": {\n" +
                "            \"eaList\": [\n" +
                "                \"300016575\"\n" +
                "            ]\n" +
                "        },\n" +
                "        \"coverPath\": \"A_202308_18_863eaabec93b48a296d7044e1e76e9df.jpg\"\n" +
                "    },\n" +
                "    \"materialInfos\": [\n" +
                "        {\n" +
                "            \"objectId\": \"2a81952d83ae4d1fa714c7e58a67aa32\",\n" +
                "            \"contentType\": 1\n" +
                "        }\n" +
                "    ],\n" +
                "    \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/partner-marketing/staff/create?marketingActivityId=64dde7b4e1eac00001ace254\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\"\n" +
                "}";
        AddMarketingActivityArg arg1 = JSON.parseObject(a,AddMarketingActivityArg.class);
        marketingActivityService.addMarketingActivity("88146", 1000, arg1, true);
    }

    @Test
    public void addWeChatMarketingActivity() {
        AddMarketingActivityArg arg = new AddMarketingActivityArg();
        AddMarketingActivityArg.WeChatServiceMarketingActivityVO weChatServiceMarketingActivityVO = new AddMarketingActivityArg.WeChatServiceMarketingActivityVO();
        arg.setSpreadType(2);
        arg.setWechatMessageType(1001);
        arg.setWeChatServiceMarketingActivityVO(weChatServiceMarketingActivityVO);
        arg.setMarketingEventId("60dadcb3bd43ab0001614ca3");

        weChatServiceMarketingActivityVO.setType(1);
        weChatServiceMarketingActivityVO.setAppId("FSAID_10b07a9b");
        weChatServiceMarketingActivityVO.setTitle("斑布1001-99");
        weChatServiceMarketingActivityVO.setSendRange(4);
        weChatServiceMarketingActivityVO.setMsgType(2);
        weChatServiceMarketingActivityVO.setContent("你好，今天下午四点，养鸡场showcase，请及时参加99");
        List<String> marketingUserGroupIds = new ArrayList<>();
        marketingUserGroupIds.add("2580c1b9fb774738b59943f9662fbe0e");
        weChatServiceMarketingActivityVO.setMarketingUserGroupIds(marketingUserGroupIds);
        marketingActivityService.addMarketingActivity("74164", 1145, arg, true);
    }

    @Test
    public void jsonAddMarketingActivity() {
        String json = "{\"marketingEventId\":\"60e84a40160a1e0001259b3f\",\"materialInfos\":[],\"spreadType\":2,\"weChatTemplateMessageVO\":{\"appId\":\"FSAID_10b07a85\",\"title\":\"一个推广标题\",\"weChatOfficialTemplateId\":\"hdwKiBjg-_JdB3KEFopOGgniSXqOJFKiXm32OghTOH4\",\"templateMessageDatas\":{\"title\":\"工单处理进度通知\",\"dataList\":[{\"key\":\"first\",\"value\":\"{微信用户昵称}\",\"title\":\"描述（选填）\"},{\"key\":\"keyword1\",\"value\":\"{直播链接}\",\"title\":\"工单编号\"},{\"key\":\"keyword2\",\"value\":\"{直播开始时间}\",\"title\":\"工单标题\"},{\"key\":\"keyword3\",\"value\":\"{讲师}\",\"title\":\"工单状态\"},{\"key\":\"keyword4\",\"value\":\"{直播结束时间}\",\"title\":\"工单处理人\"},{\"key\":\"remark\",\"value\":\"{直播标题}-----{微信用户昵称}\",\"title\":\"备注（选填）\"}]},\"redirectType\":5,\"sendRange\":6,\"conferenceEnrollIds\":[\"3e9815929f7143b4b81274a2d34b277b\"],\"campaignIds\":[\"3e9815929f7143b4b81274a2d34b277b\"],\"type\":1,\"scheduleTime\":\"\"},\"wechatMessageType\":1002}";
        String json1 = "{\"marketingEventId\":\"60e84a40160a1e0001259b3f\",\"materialInfos\":[],\"spreadType\":2,\"weChatTemplateMessageVO\":{\"appId\":\"FSAID_10b07a85\",\"title\":\"asfd\",\"weChatOfficialTemplateId\":\"hdwKiBjg-_JdB3KEFopOGgniSXqOJFKiXm32OghTOH4\",\"templateMessageDatas\":{\"title\":\"工单处理进度通知\",\"dataList\":[{\"key\":\"first\",\"value\":\"111\",\"title\":\"描述（选填）\"},{\"key\":\"keyword1\",\"value\":\"222\",\"title\":\"工单编号\"},{\"key\":\"keyword2\",\"value\":\"333\",\"title\":\"工单标题\"},{\"key\":\"keyword3\",\"value\":\"444\",\"title\":\"工单状态\"},{\"key\":\"keyword4\",\"value\":\"555\",\"title\":\"工单处理人\"},{\"key\":\"remark\",\"value\":\"666\",\"title\":\"备注（选填）\"}]},\"redirectType\":1,\"materialType\":10,\"redirectUrl\":\"https://www.ceshi112.com/ec/h5-landing/release/index.html?id=5a08f3b0dd164b3c9daab897694a4527&marketingActivityId=!!marketingActivityId!!&wxAppId=!!wxAppId!!&type=1\",\"materialId\":\"5a08f3b0dd164b3c9daab897694a4527\",\"sendRange\":6,\"conferenceEnrollIds\":[\"3e9815929f7143b4b81274a2d34b277b\"],\"campaignIds\":[\"3e9815929f7143b4b81274a2d34b277b\"],\"type\":1,\"scheduleTime\":\"\"},\"wechatMessageType\":1002}";
        String json2 = "{\n" +
                "    \"spreadType\": 8,\n" +
                "    \"marketingEventId\": \"63563fc70cba8800019c1418\",\n" +
                "    \"marketingActivityId\": \"6357addc5de8cf000123bc3c\",\n" +
                "    \"momentMessageVO\": {\n" +
                "        \"name\": \"真的好了吗\",\n" +
                "        \"type\": 2,\n" +
                "        \"fixedTime\": 1667145600000,\n" +
                "        \"text\": \"真的好了吗\",\n" +
                "        \"msgType\": \"2\",\n" +
                "        \"image\": [\n" +
                "            {\n" +
                "                \"name\": \"未命名\",\n" +
                "                \"size\": 191360,\n" +
                "                \"downLoadUrl\": \"https://crm.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=691E2897781CE5E0BBC7A2EADF5C57912B60AD298269CCE4BCA011B7491C9F2774C0146A0F67EF4B6043DCF10FE8C112FDF5AA6A8B2E532B549C719E8451ADCCCBAC4812F1812D65\",\n" +
                "                \"imagePath\": \"A_202207_14_354675ce0c1d4dc29be89d14fcffba65.png\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"name\": \"微信图片jpg\",\n" +
                "                \"size\": 107075,\n" +
                "                \"downLoadUrl\": \"https://crm.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=691E2897781CE5E0BBC7A2EADF5C57912B60AD298269CCE4DF000800179A2D4E87918381C809976E1AB43CDB3CD5853E27A98D58FE39CE2DC8B94EAC173E638FC83D198EF458C4E7\",\n" +
                "                \"imagePath\": \"A_202208_08_0521f5b9d35a4df8abc09a8429a1794e.png\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"name\": \"d104fcbd942d730d674e69ced07e1dc3\",\n" +
                "                \"size\": 17970,\n" +
                "                \"downLoadUrl\": \"https://crm.ceshi112.com/FSC/N/FileShare/ShowImage?fileId=691E2897781CE5E0BBC7A2EADF5C57910ED24F9078BF257195EB3E686A7F80BC16B234F96A30D3539C61BF0116F2158F63984EDCF9A8635D28413AA568622337AB545BC3F9C57B59\",\n" +
                "                \"imagePath\": \"A_202210_19_4d60ab6b0c674421826a6f79f423f1b4.png\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"departmentIds\": [\n" +
                "            10003,\n" +
                "            10007\n" +
                "        ],\n" +
                "        \"userIdList\": [\n" +
                "            \"wowx1mDAAAo4iB6I_48wL8lQ_9_ebZ6w\",\n" +
                "            \"wowx1mDAAAmpQCb2GMCkjgtYvURUAOWA\"\n" +
                "        ],\n" +
                "        \"tags\": [\n" +
                "            {\n" +
                "                \"firstTagName\": \"客户意向\",\n" +
                "                \"secondTagName\": \"不可合作\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"firstTagName\": \"客户等级\",\n" +
                "                \"secondTagName\": \"核心\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";

        String json3 = "{\n" +
                "  \"marketingEventId\": \"65b083e4ffc9ec000779efac\",\n" +
                "  \"spreadType\": 12,\n" +
                "  \"whatsAppMarketingActivityArg\": {\n" +
                "    \"name\": \"定时发送复制测试2\",\n" +
                "    \"businessPhone\": \"8613520614030\",\n" +
                "    \"sendType\": \"SCHEDULED\",\n" +
                "    \"sendTarget\": \"PHONE_LIST\",\n" +
                "    \"parameterList\": [\n" +
                "      \"1\",\n" +
                "      \"2\",\n" +
                "      \"3\"\n" +
                "    ],\n" +
                "    \"templateLanguage\": \"zh_CN\",\n" +
                "    \"templateName\": \"share_crm_inviate\",\n" +
                "    \"sendTime\": 1706174258000,\n" +
                "    \"targetIdList\": [\n" +
                "      \"8615302608074\"\n" +
                "    ]\n" +
                "  },\n" +
                "  \"marketingActivityId\": \"65b1eefa4c262e0001ddd674\",\n" +
                "  \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/whatsapp/create?type=edit&marketingActivityId=65b1eefa4c262e0001ddd674\",\n" +
                "  \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36\"\n" +
                "}";
        AddMarketingActivityArg arg = JSON.parseObject(json3, AddMarketingActivityArg.class);
       // Result<AddMarketingActivityResult> result = marketingActivityService.updateMarketingActivityDetail("83668", 1000, arg, true);
        Result<AddMarketingActivityResult> result = marketingActivityService.updateMarketingActivityDetail("88146", 1000, arg, true);

        System.out.println(result);
    }

    @Test
    public void testUpdateBaseInfo(){
        UpdateMarketingActivityBaseInfoArg arg = new UpdateMarketingActivityBaseInfoArg();
        arg.setMarketingActivityId("67c7bccb955bb500010ff1d7");
        arg.setTitle("测试推广0305-2");
        arg.setDescription("测试推广0305-宣传语-2");
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setStartTime(1741536000000L);
        arg.setEndTime(1742400000000L);
        arg.setTimingDate(1741968000000L);
        Result<Void> result = marketingActivityService.updateBaseInfo(arg);
        System.out.println("yes:" + result);
    }
}
