/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.*;
import com.facishare.marketing.api.arg.hexagon.*;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.result.FileToHexagonResult;
import com.facishare.marketing.api.result.HexagonQrCodeResult;
import com.facishare.marketing.api.result.OfficialWebsiteWxQrCodeResult;
import com.facishare.marketing.api.result.hexagon.*;
import com.facishare.marketing.api.service.hexagon.HexagonService;
import com.facishare.marketing.api.vo.GetMarketingContentSiteVO;
import com.facishare.marketing.api.vo.SimpleHexagonVO;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum;
import com.facishare.marketing.common.enums.hexagon.MiniAppSiteSpreadInfoEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.BeanUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO;
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO;
import com.facishare.marketing.provider.dao.live.MarketingLiveDAO;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity;
import com.facishare.marketing.provider.manager.HexagonManager;
import com.facishare.marketing.provider.remote.restapi.MetadataTagManager;
import com.facishare.marketing.provider.service.hexagon.HexagonServiceImpl;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class HexagonServiceTest extends BaseTest {

    private final static String EA = "74164";
    private final static Integer USER_ID = 1164;

    @Autowired
    private HexagonService hexagonService;
    @Autowired
    private MergeJedisCmd jedisCmd;

    @Autowired
    private HexagonSiteDAO hexagonSiteDAO;

    @Autowired
    private HexagonServiceImpl  hexagonServiceImpl;

    @Autowired
    private HexagonPageDAO hexagonPageDAO;
    @Autowired
    private HexagonManager hexagonManager;
    @Autowired
    private MarketingLiveDAO marketingLiveDAO;

    @Test
    public void editSiteTest() {
        CreateSiteArg createSiteArg = new CreateSiteArg();
        createSiteArg.setId("56fa4745447840cf89215fc725765e71");
//        createSiteArg.setName("不可见站点测试666");
//        createSiteArg.setStatus(5);
        Result<CreateSiteResult> result = hexagonService.editSite("74164", 1057, createSiteArg);
    }

    @Test
    public void getSiteByEaTest() {
        Result<PageResult<GetSiteByEaUnitResult>> result = hexagonService.getSiteByEa("88146", 1000, 10, 1, null, null, null, null);
    }

    @Test
    public void getSiteByIdTest() {
        //Result<GetSiteByEaUnitResult> result = hexagonService.getSiteById("55487", "ad65c4e2167a4d63ab18a440114e83d6");
    }

    @Test
    public void sitePreviewTest() {
        SitePreviewArg arg = new SitePreviewArg();
        arg.setType(3);
        arg.setId("baab1c412dc34b869ac591ab290c66a2");
        Result<SitePreviewResult> result = hexagonService.sitePreview("74164", arg);
    }

    @Test
    public void deleteSiteTest() {
        Result result = hexagonService.deleteSite("2", 1000,"dd5b2fae84a74c4eaaf94693c21476f5");
    }

    @Test
    public void changeSiteStatusTest() {
        Result result = hexagonService.changeSiteStatus("2", 1177,"30c2a42a225b483a9314191d351958db", HexagonStatusEnum.NORMAL.getType());
    }

    @Test
    public void editPageTest() {
        CreatePageArg createPageArg = new CreatePageArg();
        createPageArg.setId("b7a11dfa0fff4d63b900e7ca3d8098db");
        createPageArg.setName("首页");
        createPageArg.setHexagonSiteId("e77ebf06479b498f9f3096e318ac3346");
        createPageArg.setStatus(1);
        createPageArg.setIsHomepage(1);
        //createPageArg.setShareTitle("");
        //createPageArg.setShareDesc("法式风格造型华丽大气，优雅浪漫，让你的轻奢人生从定制开始。");
        createPageArg.setShareUrl(null);
        createPageArg.setFormId("d476adcd25554fa19d1392778ce7eac4");
        createPageArg.setTemplateSiteId("053615d8f45345c0b34063a202a167e4");
        createPageArg.setContent("{\"id\":\"b7a11dfa0fff4d63b900e7ca3d8098db\",\"type\":\"page\",\"name\":\"首页\",\"title\":\"\",\"version\":\"1.0.0\",\"cover\":\"\",\"shareOpts\":{\"title\":\"\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"\"},\"style\":{\"width\":375,\"backgroundColor\":\"#fff\",\"backgroundSize\":\"100%\",\"backgroundRepeat\":\"no-repeat\",\"backgroundImage\":\"\"},\"backgroundFillType\":\"filling\",\"dataSourceAction\":{},\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\"},\"components\":[{\"id\":1612171241641,\"name\":\"表单\",\"key\":\"form-container\",\"type\":\"container\",\"typeValue\":\"form\",\"components\":[{\"id\":\"1612170788641\",\"label\":\"提交\",\"name\":\"提交\",\"tip\":\"提交成功\",\"type\":\"button\",\"position\":\"none\",\"required\":false,\"isFormComp\":true,\"wrapStyle\":{\"position\":\"none\",\"background\":\"rgba(255,255,255,.9)\"},\"style\":{\"height\":45,\"width\":345,\"fontSize\":16,\"background\":\"#409EFF\",\"borderRadius\":0,\"color\":\"#fff\",\"letterSpacing\":0,\"lineHeight\":45,\"textAlign\":\"center\",\"margin\":\"0 auto\",\"borderWidth\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\",\"position\":\"absolute\",\"left\":10,\"top\":511},\"sort\":0},{\"id\":1612171267707,\"label\":\"多选\",\"name\":\"多选\",\"title\":\"1.  在一点点售卖中，你最担忧的问题？\",\"type\":\"checkbox\",\"typeValue\":\"select_manny\",\"fieldName\":\"texts1_1612170788766\",\"required\":false,\"placeholder\":\"请选择\",\"isFormComp\":true,\"options\":[{\"label\":\"不知道那些是目标客户群体\",\"value\":1},{\"label\":\"不知道如何更好的介绍一点点优势买点是什么\",\"value\":2},{\"label\":\"不知道与客户如何切入，问什么样问题才能了解客户通电\",\"value\":\"1612170788803\"},{\"label\":\"对TOB营销不熟悉，无法回复CMO的问题\",\"value\":\"1612170788804\"},{\"label\":\"担心实施交付问题\",\"value\":\"1612170788805\"},{\"label\":\"没有最佳客户实践案例\",\"value\":\"1612170788827\"},{\"label\":\"其他：\",\"value\":\"1612170788828\"}],\"components\":[],\"titleStyle\":{\"color\":\"#181C25\",\"fontSize\":14,\"lineHeight\":16,\"paddingBottom\":6,\"paddingTop\":6,\"whiteSpace\":\"normal\",\"fontFamily\":\"Microsoft YaHei, Hiragino Sans GB, STHeiti\",\"background\":\"none\",\"textAlign\":\"left\",\"fontWeight\":\"bold\",\"letterSpacing\":0,\"textDecoration\":\"none\",\"fontStyle\":\"normal\"},\"style\":{\"width\":295,\"borderRadius\":0,\"fontSize\":14,\"paddingBottom\":0,\"paddingLeft\":0,\"paddingRight\":0,\"paddingTop\":0,\"color\":\"#181C25\",\"lineHeight\":20,\"borderWidth\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\",\"left\":40,\"top\":69,\"position\":\"absolute\"},\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\",\"background\":\"#fff\"},\"sort\":1},{\"id\":1612171493695,\"label\":\"单行文本\",\"name\":\"单行文本\",\"title\":\"\",\"type\":\"input\",\"typeValue\":\"text\",\"fieldName\":\"text5_1612170788836\",\"defaultValue\":\"\",\"defaultValueOpen\":false,\"globalCacheField\":\"\",\"defaultValueType\":\"manual\",\"required\":false,\"placeholder\":\"请输入\",\"isFormComp\":true,\"titleStyle\":{\"color\":\"#181C25\",\"fontSize\":14,\"lineHeight\":16,\"paddingBottom\":6,\"paddingTop\":6,\"whiteSpace\":\"normal\"},\"style\":{\"color\":\"#181C25\",\"width\":284,\"height\":39,\"lineHeight\":45,\"fontSize\":14,\"paddingBottom\":0,\"paddingTop\":0,\"paddingLeft\":12,\"paddingRight\":12,\"borderStyle\":\"solid\",\"borderWidth\":1,\"borderRadius\":3,\"borderColor\":\"#e9edf5\",\"left\":40,\"top\":343,\"position\":\"absolute\"},\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\",\"background\":\"#fff\"},\"sort\":2,\"itemStyle\":{\"height\":39,\"lineHeight\":45}}],\"current\":0,\"slideIndex\":0,\"layout\":\"single\",\"fillType\":\"color\",\"fillMethod\":\"filling\",\"style\":{\"width\":375,\"height\":600,\"overflow\":\"hidden\",\"position\":\"relative\",\"backgroundColor\":\"\",\"backgroundImage\":\"\"},\"sort\":0}]}");
        hexagonService.editPage("74164", 1000, createPageArg);
    }

    @Test
    public void deletePageTest() {
        Result result = hexagonService.deletePage("74164", 1000,"d354f77376564aebba4e9965081d43f3");
    }

    @Test
    public void getPagesBySiteIdTest() {
        Result<List<GetPagesBySiteIdUnitResult>> result = hexagonService.getPagesBySiteId("55487", "********************************");
    }

    @Test
    public void getPageDetailTest() {
        hexagonService.getPageDetail(1, "b594a682fb534a559d6b8ae7259e19f8");
    }

    @Test
    public void getHomepageDetailBySiteIdTest() {
        HexagonhomepageDetailArg arg = new HexagonhomepageDetailArg();
        arg.setSiteId("5417b14adc1744bd8a24e8c19585b5bf");
        arg.setType(1);
        arg.setUid("e2dff31091f54d09afad543f9ab087fd");
        hexagonService.getHomepageDetailBySiteId(arg);
        arg.setUid("e2dff31091f54d09afad543f9ab087fd");
        arg.setSiteId("ce864d4e7a954386af8bd4b3b868bac5");
        Result<GetPageDetailResult> homepageDetailBySiteId = hexagonService.getHomepageDetailBySiteId(arg);
        System.out.println(homepageDetailBySiteId);
    }

    @Test
    public void editTemplateSiteTest() {
        CreateTemplateSiteEaArg createTemplateSiteArg = new CreateTemplateSiteEaArg();
      // createTemplateSiteArg.setId("b828ad9f832f490b97f9f6b72a948f76");
        createTemplateSiteArg.setName("站点模板1234");
        createTemplateSiteArg.setContent("模板内容：纷享销客001");
        Result<CreateTemplateSiteResult> result = hexagonService.editTemplateEaSite("2", 1126, createTemplateSiteArg);
    }

    @Test
    public void changeTemplateSiteStatusTest(){
        Result result=hexagonService.changeTemplateSiteStatus(HexagonStatusEnum.NORMAL.getType(),"ae134a9fd4cf4ad7834074178695abf6");
    }

    @Test
    public void editTemplatePageTest(){
        CreateTemplatePageArg createTemplatePageArg=new CreateTemplatePageArg();
//        模板站点的ID   b828ad9f832f490b97f9f6b72a948f76
//        createTemplatePageArg.setId("11");
        createTemplatePageArg.setId("4b67611d38934514aa4745d74c326a8f");
        createTemplatePageArg.setName("模版创建测试");
        createTemplatePageArg.setShareTitle("");
        createTemplatePageArg.setShareDesc("");
        createTemplatePageArg.setContent("{\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\"},\"style\":{\"width\":375\n" +
                ",\"backgroundColor\":\"#fff\",\"backgroundSize\":\"100%\",\"backgroundRepeat\":\"no-repeat\",\"backgroundImage\":\"\"},\"id\":\"4b67611d38934514aa4745d74c326a8f\",\"type\":\"page\",\"name\":\"模版创建测试\",\"title\":\"\",\"version\":\"1.0.0\",\"cover\":\"\",\"shareOpts\":{\"title\":\"\",\"desc\":\"\",\n" +
                "\"link\":\"\",\"imgUrl\":\"\"},\"backgroundFillType\":\"filling\",\"dataSourceAction\":{},\"components\":[{\"id\":1617008663106,\"name\":\"文本\",\"type\":\"text\",\"value\":\"<p>模版创建测试</p>\",\"style\":{\"paddingBottom\":6,\"paddingLeft\":12,\"paddingRight\":12,\"paddingTop\":6,\"backgro\n" +
                "und\":\"rgba(255, 255, 255, 0)\",\"fontSize\":14,\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"sort\":\"0\",\"components\":[],\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\"}}]}");
        createTemplatePageArg.setHexagonTemplateSiteId("7366a90bb5054f158f3b5ea470a6c5fe");
        Result<CreateTemplatePageResult> result=hexagonService.editTemplatePage("74164",1126,createTemplatePageArg);


    }
    @Test
    public void deleteTemplateSiteTest() {
        //Result result = hexagonService.deleteTemplateSite("b828ad9f832f490b97f9f6b72a948f76");
    }

    @Test
    public void getTemplateSite(){
//        单元测试获取系统站点
        Result<PageResult<GetTemplateSiteResult>> result=hexagonService.getTemplateSite(null,null,10,1,null,1);
       System.out.println("输出coverurl"+result.getData());
    }
    @Test
    public void GetPagesByTemplateSiteIdResultTest(){
        Result<List<GetPagesByTemplateSiteIdResult>> result=hexagonService.getPagesByTemplateSiteId("74164","8effbf3d1af742b296ef708976bbb58e");
        System.out.println("输出coverurl"+result.getData());
    }

    @Test
    public void redisTest() {
//        String result = jedisCmd.set("HEXAGON_SITE_INFO_56fa4745447840cf89215fc725765e722222", "{\"id\":\"56fa4745447840cf89215fc725765e71\",\"ea\":\"74164\",\"name\":\"模板创建测试\",\"status\":1,\"createBy\":1057,\"updateBy\":1057,\"createTime\":\"Dec 10, 2019 4:38:16 PM\",\"updateTime\":\"Dec 10, 2019 8:28:05 PM\"}");
        String value = jedisCmd.get("HEXAGON_SITE_INFO_56fa4745447840cf89215fc725765e722222");
        System.out.println(value);
    }

    @Test
    public void copySite(){
        String ea="88146";
        Integer userId=1000;
        MarketingCopyArg copyArg=new MarketingCopyArg();
        copyArg.setId("c90015314d254349845ffea787a86eb7");
        copyArg.setMarketingEventId("5e04920a480df40001428653");
        copyArg.setName("测试微页面跳转_新副本");
        HexagonCopyArg arg=new HexagonCopyArg();
        arg.setId("eca366461dc74109a93812c0b8bb68b7");
        arg.setName("307cdn微页面-复制品2");
        arg.setGroupId("f542a57ed6f840f383fce54bcb4d6286");
        //微页面的复制站点
        Result<CreateSiteResult> createSiteResultResult = hexagonService.hexagonCopySite(ea, userId, arg);
        System.out.println(createSiteResultResult.getData().getId());
        //内容营销的复制站点
//       hexagonService.marketingCopySite(ea,userId,copyArg);
    }



    @Test
    public void testReplaceAll(){
        String conta="{\"id\":\"a8dc9b86547e4064a08041c6352d8e4b\",a8dc9b86547e4064a08041c6352d8e4b\"type\":\"page\",\"name\":\"首页\",\"title\":\"\",\"version\":\"1.0.0\",\"cover\":\"\",\"";
//        String contb="{\"id\":\"a8dc9b86547e4064a08041c6352d8e4b\",\"type\":\"page\",\"name\":\"首页\",\"title\":\"\",\"version\":\"1.0.0\",\"cover\":\"\",\""
        String b=conta.replace("a8dc9b86547e4064a08041c6352d8e4b","ac2b283923ad42c5a2fca9fec2265dc1");
        System.out.println(b);
    }

    @Test
    public void deletePage(){
        hexagonService.deletePage(null,null,null);
    }

    @Test
    public void deleteSite(){
        hexagonService.deleteSite(null, null , null);
    }


    @Test
    public void listHexagonByGroup(){
        String ea = "88146";
        Integer fsUserId = 1000;
        ListHexagonByGroupArg arg = new ListHexagonByGroupArg();
        arg.setGroupId("-1");
        arg.setPageNum(1);
        arg.setPageSize(10);
        //arg.setKeyword("DXX19");
        Result<PageResult<GetSiteByEaUnitResult>> result = hexagonService.listHexagonByGroup(ea, fsUserId, arg);
        log.info("result 1 : {}", result);
//        arg.setGroupId("-2");
//        result = hexagonService.listHexagonByGroup(ea, fsUserId, arg);
//        log.info("result 2 : {}", result);
//        arg.setGroupId("-3");
//        result = hexagonService.listHexagonByGroup(ea, fsUserId, arg);
//        log.info("result 3 : {}", result);

    }

    @Test
    public void editHexagonGroup(){
        String ea = "74164";
        Integer fsUserId = 1123;
        EditHexagonGroupArg arg = new EditHexagonGroupArg();
        arg.setName("微页面分组1");
        arg.setGroupId("08a97b44b78049519c374d6afa67c194");
        hexagonService.editHexagonGroup(ea, fsUserId, arg);
    }

    @Test
    public void listHexagonGroup(){
        String ea = "74164";
        Integer fsUserId = 1177;
        ListHexagonGroupArg arg = new ListHexagonGroupArg();
        arg.setUseType(0);
     //   arg.setStatus(0);
        arg.setTime(0l);
        Result<ListHexagonTemplateGroupResult> result = hexagonService.listHexagonGroup(ea, fsUserId, arg);
        log.info("listHexagonGroup size: {} result1 : {}",result.getData().getObjectGroupList().size(), result);
        arg.setUseType(1);
        result = hexagonService.listHexagonGroup(ea, fsUserId, arg);
        log.info("listHexagonGroup size: {} result2 : {}",result.getData().getObjectGroupList().size(), result);
    }

    @Test
    public void getMarketingContentSite(){
        String ea = "74164";
        Integer fsUserId = 1033;
        GetMarketingContentSiteVO vo = new GetMarketingContentSiteVO();
        vo.setPageNum(1);
        vo.setPageSize(10);
        vo.setSearchFitter("test");
        Result<PageResult<GetSiteByEaUnitResult>> result = hexagonService.getMarketingContentSite(ea, fsUserId, vo);
    }

    @Test
    public void bindMarketing() {
        Result result = hexagonService
            .bindMarketing("74164", 1128, ObjectTypeEnum.HEXAGON_SITE.getType(), null, "d1346ba5e5b04de18458d8cf194f06c4", "60f91322a6db7b0001fe8919");
    }


    @Test
    public void testCopyAndReplaceParam() {
        MarketingLiveEntity liveEntity = marketingLiveDAO.getByCorpIdAndMarketingEventId(74164, "60f91322a6db7b0001fe8919");
        String tempSideId = UUIDUtil.getUUID();
        HexagonPageEntity templateFirstPage = hexagonPageDAO.getHomePage("20e7b27e19834c5ca41fa43b3ce0dea3");
        HexagonPageArg templateFirstPageArg = BeanUtil.copy(templateFirstPage, HexagonPageArg.class);
        String firstPageId = hexagonService.copyPage(templateFirstPageArg, "74164", tempSideId, 1128).getData();
        HexagonPageEntity firstPage = hexagonPageDAO.getById(firstPageId);
        hexagonManager.updateLiveHexagonParam(firstPage, liveEntity, "74164", tempSideId, "假装有个标题", "假装有详细内容", "A_202107_22_7b0791c879c24bcd836a0a96e24003b6.jpg", new Date(), false);
        System.out.println("tempSideId = " + tempSideId);
    }

    @Test
    public void queryEnterpriseCommerceInfo(){
        String objectId = "4cdc9a04e23e4afb89ed6510d80aff6a";
        Integer objectType = 26;
        String keyword = "海口海豚口腔门诊有限责任公司";
        hexagonService.queryEnterpriseCommerceInfo(objectId, objectType, keyword);
    }

    @Test
    public void getMiniAppSiteSpreadInfoTest() {
        Result<GetMiniAppSiteSpreadInfoResult> miniAppSiteSpreadInfo = hexagonService.getMiniAppSiteSpreadInfo("74164", 1135, MiniAppSiteSpreadInfoEnum.PRODUCT_SPREAD.getType(), true);
        System.out.println(miniAppSiteSpreadInfo);
    }

    @Test
    public void setHexagonGroupBatch() {
        String ea = "74164";
        int userId = 1177;
        SetObjectGroupArg arg = new SetObjectGroupArg();
        arg.setGroupId("3ef72d057ea74eb0bab2dc1564afc8c4");
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("47779a9ca95f4b27a0baf56c37d83214");
        objectIdList.add("e07456a1161046329f8315626ecbbb9a");
        arg.setObjectIdList(objectIdList);
        Result<Void> result = hexagonService.setHexagonGroupBatch(ea, userId, arg);
        log.info("setHexagonGroupBatch result: {}", result);
    }

    @Test
    public void deleteSiteBatch() {
        String ea = "74164";
        int userId = 1177;
        DeleteMaterialArg arg = new DeleteMaterialArg();
        List<String> idList = new ArrayList<>();
      //  idList.add("aa47195edc564c6d9be73983dbbd2441");
      //  idList.add("591a5a6420c14c39ac608ec8a63c6474");
        idList.add("aa47195edc564c6d9be73983dbbd2441");
        arg.setIdList(idList);
        Result<Void> result = hexagonService.deleteSiteBatch(ea, userId, arg);
        log.info("deleteSiteBatch result : {}", result);
    }
    @Test
    public void topHexagonSite() {
        String ea = "74164";
        int userId = 1177;
        TopMaterialArg arg = new TopMaterialArg();
        arg.setObjectId("264a8466af50412c84fe04d0a70dc213");
        Result<Void> result = hexagonService.topHexagonSite(ea, userId, arg);
        log.info("deleteSiteBatch result : {}", result);
    }

    @Test
    public void addHexagonGroupRole() {
        String ea = "74164";
        int userId = 1177;
        SaveObjectGroupVisibleArg roleArg = new SaveObjectGroupVisibleArg();
        roleArg.setGroupId("589b4906ca484494b0f871a896d40955");
        List<String> roleIdList = new ArrayList<>();
        roleIdList.add("wwww");
        roleIdList.add("qqq");
        roleArg.setRoleIdList(roleIdList);
        Result<Void> result = hexagonService.addHexagonGroupRole(ea, userId, roleArg);
        log.info("addHexagonGroupRole result : {}", result);
    }

    @Test
    public void queryAccessibleHexagonSiteCount() {
        String ea = "74164";
        int userId = 1177;
        List<String> groupIdList = new ArrayList<>();
        groupIdList.add("3ef72d057ea74eb0bab2dc1564afc8c4");
        int result = hexagonSiteDAO.queryAccessibleHexagonSiteCount(ea, null, userId,null, groupIdList);
        log.info("queryAccessibleHexagonSiteCount result : {}", result);
    }

    @Test
    public void testGetProductSpreadInfo(){
        Result<GetProductSpreadInfoResult> result = hexagonService.getProductSpreadInfo(EA, USER_ID, true);
        System.out.printf("yes:" + result);
    }


    @Test
    public void createHexagonWxQrCode(){
        CreateHexagonWxQrCodeArg arg = new CreateHexagonWxQrCodeArg();
//        CreateHexagonWxQrCodeArg.WxQrCode qrcode = new CreateHexagonWxQrCodeArg.WxQrCode();
//        qrcode.setSceneId("1148");
//        qrcode.setWxAppId("wx2e8a14a356c46c27");
//        arg.setWxQrCodes(Lists.newArrayList(qrcode));
        arg.setUserMarketingId("fae6315bb7be45c39929340242f94c1c");
        arg.setEa("88162");
        CreateHexagonWxQrCodeArg.QywxQrCode qywxQrCode = new CreateHexagonWxQrCodeArg.QywxQrCode();
        qywxQrCode.setSceneId("9528e1bc0d9e42669ffe9f4d76fcf7b9");
        arg.setQywxQrCodes(Lists.newArrayList(qywxQrCode));
        Result<HexagonQrCodeResult> result = hexagonService.createHexagonQrCode(arg);
        System.out.printf("yes:" + result);
    }
    @Test
    public void fileToHexagon(){
        FileToHexagonArg arg = new FileToHexagonArg();
//        arg.setHexagonSiteId("fb4a352cb1bc42578d250f061c09636f");
        arg.setEa("88146");
        arg.setUserId(1000);
        arg.setFilePath("TN_491762e995ff4e989e56f6ed36a3ee6f.pdf");
        //arg.setFilePath("TN_f501ff37ae394caf9695d8d5424e9ec9.pdf");
        arg.setFileName("【对外】零样本接口文档 (2)");
        arg.setFileType("pdf");
        //String content = "{\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\"},\"style\":{\"width\":375,\"backgroundColor\":\"#f9f9f9\",\"backgroundSize\":\"100%\",\"backgroundRepeat\":\"no-repeat\",\"backgroundImage\":\"\"},\"id\":\"{{pageId}}\",\"type\":\"page\",\"name\":\"\",\"title\":\"数字化活动平台开放接口接入文档\",\"version\":\"10.1.1\",\"cover\":\"\",\"pcAdaptation\":false,\"shareOpts\":{\"title\":\"数字化活动平台开放接口接入文档\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"\",\"sharePosterUrl\":\"\",\"sharePosterAPath\":\"\"},\"popupOpts\":{\"show\":0,\"coverUrl\":\"\",\"mode\":1,\"action\":{}},\"backgroundOpts\":{\"mode\":1,\"bgColor\":\"#f9f9f9\",\"bgImage\":\"\",\"fillType\":\"horizontal-filling\",\"carouselImgs\":[],\"carouselPointType\":5,\"carouselPointBottom\":24,\"carouselPointIsShow\":false,\"carouselPointAutoPlay\":true,\"playDuration\":200,\"carouselPointColor\":\"rgba(255, 255, 255, 0.5)\",\"carouselPointActiveColor\":\"#fff\",\"carouselPointActiveIndex\":0,\"carouselPlayInterval\":3000,\"circlePointSize\":6,\"circlePointMargin\":5,\"dashPointWidth\":12,\"dashPointHeight\":2,\"dashPointMargin\":4,\"slidePointWidth\":180,\"slidePointHeight\":1,\"indicatorLeft\":187,\"indicatorTop\":150},\"headerOpts\":{\"titleColor\":\"#000\",\"headerBackgroundColor\":\"#fff\",\"fontColor\":1,\"isCustomFp\":false,\"fpFontColor\":1,\"fpHideTitle\":true,\"fpHideHeaderBg\":true},\"backgroundFillType\":\"horizontal-filling\",\"dataSourceAction\":{},\"components\":\"{{components}}\",\"componentsReplaceTmp\":\"!!tmpStart!!{id:'{{imgId}}',name:'图片',type:'image',images:[{url:'{{imgUrl}}',action:{},uploadType:'upload'}],imageGap:4,style:{display:'flex',width:375,height:210,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,borderRadius:0,background:'rgba(255,255,255,0)',backgroundRepeat:'no-repeat',backgroundSize:'cover',backgroundPosition:'centercenter',borderWidth:0,borderStyle:'none',borderColor:'#e9edf5',opacity:100,},filterConfig:{brightness:100,grayscale:0,opacity:100},sort:0,components:[],placeholderStyle:{color:'#cbcccf'},inputStyle:{color:'#181c25'}},{id:'{{blankId}}',name:'辅助留白',type:'blank',style:{width:375,height:6,background:'rgba(255,255,255,0)',borderWidth:0,borderRadius:0,borderStyle:'none',borderColor:'#e9edf5',},sort:5,components:[],placeholderStyle:{color:'#cbcccf'},inputStyle:{color:'#181c25'}}!!tmpEnd!!\",\"language\":\"zh-CN\"}";
        String content1 = "{\"placeholderStyle\":{\"color\":\"#cbcccf\"},\"inputStyle\":{\"color\":\"#181c25\"},\"style\":{\"width\":375,\"backgroundColor\":\"#f9f9f9\",\"backgroundSize\":\"100%\",\"backgroundRepeat\":\"no-repeat\",\"backgroundImage\":\"\"},\"id\":\"{{pageId}}\",\"type\":\"page\",\"name\":\"数字化活动平台开放接口接入文档.pdf\",\"title\":\"数字化活动平台开放接口接入文档\",\"version\":\"10.1.1\",\"cover\":\"\",\"pcAdaptation\":false,\"shareOpts\":{\"title\":\"数字化活动平台开放接口接入文档\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"\",\"sharePosterUrl\":\"\",\"sharePosterAPath\":\"\"},\"popupOpts\":{\"show\":0,\"coverUrl\":\"\",\"mode\":1,\"action\":{}},\"backgroundOpts\":{\"mode\":1,\"bgColor\":\"#f9f9f9\",\"bgImage\":\"\",\"fillType\":\"horizontal-filling\",\"carouselImgs\":[],\"carouselPointType\":5,\"carouselPointBottom\":24,\"carouselPointIsShow\":false,\"carouselPointAutoPlay\":true,\"playDuration\":200,\"carouselPointColor\":\"rgba(255, 255, 255, 0.5)\",\"carouselPointActiveColor\":\"#fff\",\"carouselPointActiveIndex\":0,\"carouselPlayInterval\":3000,\"circlePointSize\":6,\"circlePointMargin\":5,\"dashPointWidth\":12,\"dashPointHeight\":2,\"dashPointMargin\":4,\"slidePointWidth\":180,\"slidePointHeight\":1,\"indicatorLeft\":187,\"indicatorTop\":150},\"headerOpts\":{\"titleColor\":\"#000\",\"headerBackgroundColor\":\"#fff\",\"fontColor\":1,\"isCustomFp\":false,\"fpFontColor\":1,\"fpHideTitle\":true,\"fpHideHeaderBg\":true},\"backgroundFillType\":\"horizontal-filling\",\"dataSourceAction\":{},\"components\":\"{{components}}\",\"isPdf\":true,\"componentsReplaceTmp\":\"!!tmpStart!!{\\\"id\\\":\\\"{{imgId}}\\\",\\\"name\\\":\\\"图片\\\",\\\"type\\\":\\\"image\\\",\\\"images\\\":[{\\\"url\\\":\\\"{{imgUrl}}\\\",\\\"action\\\":{},\\\"uploadType\\\":\\\"upload\\\"}],\\\"imageGap\\\":4,\\\"style\\\":{\\\"display\\\":\\\"flex\\\",\\\"width\\\":375,\\\"height\\\":210,\\\"paddingBottom\\\":0,\\\"paddingLeft\\\":0,\\\"paddingRight\\\":0,\\\"paddingTop\\\":0,\\\"borderRadius\\\":0,\\\"background\\\":\\\"rgba(255, 255, 255, 0)\\\",\\\"backgroundRepeat\\\":\\\"no-repeat\\\",\\\"backgroundSize\\\":\\\"cover\\\",\\\"backgroundPosition\\\":\\\"center center\\\",\\\"borderWidth\\\":0,\\\"borderStyle\\\":\\\"none\\\",\\\"borderColor\\\":\\\"#e9edf5\\\",\\\"opacity\\\":100},\\\"filterConfig\\\":{\\\"brightness\\\":100,\\\"grayscale\\\":0,\\\"opacity\\\":100},\\\"sort\\\":0,\\\"isPdf\\\":true,\\\"components\\\":[],\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"}},{\\\"id\\\":\\\"{{blankId}}\\\",\\\"name\\\":\\\"辅助留白\\\",\\\"type\\\":\\\"blank\\\",\\\"style\\\":{\\\"width\\\":375,\\\"height\\\":6,\\\"background\\\":\\\"rgba(255, 255, 255, 0)\\\",\\\"borderWidth\\\":0,\\\"borderRadius\\\":0,\\\"borderStyle\\\":\\\"none\\\",\\\"borderColor\\\":\\\"#e9edf5\\\"},\\\"sort\\\":5,\\\"components\\\":[],\\\"placeholderStyle\\\":{\\\"color\\\":\\\"#cbcccf\\\"},\\\"inputStyle\\\":{\\\"color\\\":\\\"#181c25\\\"}}!!tmpEnd!!\",\"language\":\"zh-CN\"}";
        arg.setPageContent(content1);
        arg.setSiteName("数字化活动平台开放接口接入文档");
        arg.setShareTitle("数字化活动平台开放接口接入文档");
        arg.setShareDesc("我是的事实上无敌的");
        arg.setSystemSite("0");
        arg.setPath("C_202412_25_5a8d49dc0e6a478190623a52acb3bc60");
        TagNameList list = TagNameList.convert(Lists.newArrayList(new TagName("用户二","会员")));
        arg.setTagNameList(list);
        PhotoCutOffset offset = new PhotoCutOffset();
        offset.setTop("100");
        offset.setLeft("100");
        offset.setWidth("200");
        offset.setHeight("150");
        offset.setPhotoTargetType(43);
        PhotoCutOffset offset1 = new PhotoCutOffset();
        offset1.setTop("100");
        offset1.setLeft("100");
        offset1.setWidth("200");
        offset1.setHeight("200");
        offset1.setPhotoTargetType(44);
        offset1.setPath("C_202412_20_4ec8cf28eb1a44e7a5bd44e0d46779a4");
        PhotoCutOffset offset2 = new PhotoCutOffset();
        offset2.setTop("100");
        offset2.setLeft("100");
        offset2.setWidth("900");
        offset2.setHeight("500");
        offset2.setPhotoTargetType(45);
        offset2.setPath("C_202412_26_24ca23dba1da4334990f91164bca7402");
        arg.setCutOffsetList( Lists.newArrayList(offset, offset1, offset2));
        arg.setGroupId("58246371f0a7437da8fb0f4b7ad50dcd");
        Result<FileToHexagonResult> result = hexagonService.fileToHexagon(arg);
        System.out.printf("yes:" + result);
    }
    @Test
    public void updateFileToHexagonStatus(){

        List<FileToHexagonDataArg> list = Lists.newArrayList();
        FileToHexagonDataArg arg = new FileToHexagonDataArg();
        arg.setPath("C_202404_25_3330112bbc4c43d9b28660abb3fc2d6f");
        arg.setWidth(1236);
        arg.setHeight(1756);
        list.add(arg);
        arg = new FileToHexagonDataArg();
        arg.setPath("C_202404_25_3330112bbc4c43d9b28660abb3fc2d6f");
        arg.setWidth(1236);
        arg.setHeight(1756);
        list.add(arg);
        arg = new FileToHexagonDataArg();
        arg.setPath("C_202404_25_3330112bbc4c43d9b28660abb3fc2d6f");
        arg.setWidth(1236);
        arg.setHeight(1756);
        list.add(arg);
        arg = new FileToHexagonDataArg();
        arg.setPath("C_202404_25_6c695c92cb3149a3846aff74169fda12");
        arg.setWidth(0);
        arg.setHeight(1756);
        list.add(arg);
        hexagonServiceImpl.updateFileToHexagonStatus("6488bd1b88b34c5aa6eefb9a81ef86e8", 0, "",list);
//        hexagonServiceImpl.updateFileToHexagonStatus("6488bd1b88b34c5aa6eefb9a81ef86e8", 0, "",Lists.newArrayList("https://a2.ceshi112.com/image/88146/C_202404_25_3330112bbc4c43d9b28660abb3fc2d6f","https://a2.ceshi112.com/image/88146/C_202403_08_a03dd9ff638f4094908c5e6416830cd3","https://a2.ceshi112.com/image/88146/C_202404_25_6c695c92cb3149a3846aff74169fda12",
//                "https://a2.ceshi112.com/image/88146/C_202404_25_3330112bbc4c43d9b28660abb3fc2d6f"),"C_202404_25_3330112bbc4c43d9b28660abb3fc2d6f");
        System.out.println("1");
    }
    @Test
    public void getSiteById(){
        Result<GetSiteByEaUnitResult> siteById = hexagonServiceImpl.getSiteById("88146", 1000, "6488bd1b88b34c5aa6eefb9a81ef86e8");
        System.out.println(siteById);
    }
    @Test
    public void marketingCopySite(){
        MarketingCopyArg arg = new MarketingCopyArg();
        arg.setId("fa83af6b1b544cb9be69878990750356");
        arg.setName("1128微页面-副本2");
        arg.setMarketingEventId("67468b8663191c00078cae9d");
        Result<CreateSiteResult> createSiteResultResult = hexagonServiceImpl.marketingCopySite("88146", 1000, arg, false);
        System.out.println(createSiteResultResult);
    }

    @Test
    public void simpleHexagonListTest(){
        SimpleHexagonListArg arg = new SimpleHexagonListArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setPageNum(1);
        arg.setPageSize(10);
        arg.setMenuId("d6b3d3c5644646a6888937988c76b313");
        MaterialTagFilterArg materialTagFilterArg = new MaterialTagFilterArg();
        materialTagFilterArg.setType(1);
        materialTagFilterArg.setMaterialTagIds(Lists.newArrayList("7f2ce321525a43d88dfb5ceafb71a401"));
        arg.setMaterialTagFilter(materialTagFilterArg);
        Result<PageResult<SimpleHexagonVO>> result = hexagonServiceImpl.simpleHexagonList(arg);
        log.info("结果: {}", JsonUtil.toJson(result));
    }

    @Test
    public void updateHexagonData(){
        UpdateHexagonBackgroudColorArg arg = new UpdateHexagonBackgroudColorArg();
        List<UpdateHexagonBackgroudColorArg.ColorConfig> list = Lists.newArrayList();
        UpdateHexagonBackgroudColorArg.ColorConfig colorConfig = new UpdateHexagonBackgroudColorArg.ColorConfig();
        colorConfig.setColor("#ff0000");
        colorConfig.setType(1);
        list.add(colorConfig);
        UpdateHexagonBackgroudColorArg.ColorConfig colorConfig1 = new UpdateHexagonBackgroudColorArg.ColorConfig();
        colorConfig1.setColor("#000001");
        colorConfig1.setType(0);
        list.add(colorConfig1);
        arg.setColorConfigs(list);
        hexagonService.updateHexagonBackgroundColorSetting("88146",arg);
    }

}
