/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.usermarketingaccount;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.PageUserMarketingActionStatisticArg;
import com.facishare.marketing.api.arg.PageUserMarketingActionStatisticByCrmObjectArg;
import com.facishare.marketing.api.arg.UserMarketingDetailsByAssociationIdArg;
import com.facishare.marketing.api.arg.usermarketingaccount.*;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.data.usermarketingaccount.GetInfoByIdentifyVO;
import com.facishare.marketing.api.data.usermarketingaccount.UserMarketingAccountData;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.UserMarketingActionResult;
import com.facishare.marketing.api.result.usermarketingaccount.GetStatisticalDatasResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountDetailsResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingAccountIdResult;
import com.facishare.marketing.api.result.usermarketingaccount.UserMarketingLookUpStatisticByObjectResult;
import com.facishare.marketing.api.service.usermarketingaccount.UserMarketingAccountService;
import com.facishare.marketing.common.contstant.MarketingUserSearchTypeEnum;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.result.FunctionResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.Filter;
import com.facishare.marketing.common.typehandlers.value.SearchTemplateQuery;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.innerData.GlobalCRMStatisticalData;
import com.facishare.marketing.provider.manager.UserTagManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 01/04/2019
 */
@Slf4j

public class UserMarketingAccountServiceTest extends BaseTest {
    @Autowired
    private UserMarketingAccountService userMarketingAccountService;
    @Autowired
    private UserMarketingAccountManager userMarketingAccountManager;
    @Autowired
    private UserTagManager userTagManager;

    @Test
    public void listUserMarketingAccountByFilter() {
        ListByFilterArg arg = new ListByFilterArg();
        arg.setPageNo(1);
        arg.setPageSize(40);
        arg.setTagOperator("IN");
        List<TagName> tagNames = Lists.newArrayList();
        TagName tagName1 = new TagName("太阳", "太阳");
        tagNames.add(tagName1);
        arg.setTagNames(tagNames);
        // me\":\"LeadsObj\",\"query\":{\"filters\":[],\"tagOperator\":\"ISN\",\"tagNames\":[]}}],\"pageNo\":2,\"pageSize\":15}",ListByFilterArg.class);
        Result<List<UserMarketingAccountData>> result = userMarketingAccountService.listUserMarketingAccount("74164", 1071, arg);
        log.info("=== result: {}", result);
    }

    @Test
    public void getStatisticalDatas() {
        Result<GetStatisticalDatasResult> result = userMarketingAccountService.getStatisticalDatas("88146", 1000);
        log.info("=== result: {}", result);
    }

    @Test
    public void getUserMarketingAccountDetails() {
        Result<UserMarketingAccountDetailsResult> result = userMarketingAccountService.getUserMarketingAccountDetails("88146", *********, "7521791895414409b770c5867cd705b1");
        log.info("=== result: {}", result);
    }


    @Test
    public void getChannelObjectByUserMarketingId() {
        Result<List<String>> result = userMarketingAccountService.getChannelObjectIdByUserMarketingId("74164", -10000, LeadsFieldContants.API_NAME, "cd66eefbfcee4df4b5fff0cabbbf1846");
    }


    @Test
    public void getUserMarketingDetailsByAssociationId() {
        UserMarketingDetailsByAssociationIdArg arg = new UserMarketingDetailsByAssociationIdArg();
        arg.setAssociationId("5d3599b6a5083dc41efcd4b1");
        arg.setType(4);
        Result<UserMarketingAccountDetailsResult> result = userMarketingAccountService.getUserMarketingDetailsByAssociationId("74164", 1057, arg);
//        if (result.isSuccess() && result.getData() != null && CollectionUtils.isEmpty(result.getData().getTags())){
//            result.getData().setTags(Lists.newArrayList());
//        }
//        log.info("=== result : " + result);
    }

    @Test
    public void batchMergeUserMarketingAccountsFromTags() {
        BatchMergeUserMarketingAccountsFromTagsArg arg = new BatchMergeUserMarketingAccountsFromTagsArg();
        arg.setUserMarketingAccountId("540536625579412a9a99fd53040948fe");
//        arg.setTagIds(Lists.newArrayList("5cc151bec0700b000130a9d2","5cc12288c0700b000130a8ab"));
//        Result result = userMarketingAccountService.batchMergeUserMarketingAccountsFromTags("74164", 1057, arg);
//        log.info("=== result : " + result);
    }

    @Test
    public void batchDeleteTagsFromUserMarketings() {
        TagNameList tagNameList = new TagNameList();
        tagNameList.add(new TagName("互斥功能", "b"));
        Result result = userMarketingAccountService.batchDeleteTagsFromUserMarketings("74164", 1167, Lists.newArrayList(), Lists.newArrayList("6dd69674bd874eb588f6b9b55bedac44"), tagNameList);
        log.info("=== result : " + result);
    }

    @Test
    public void getLeadWeChatAvatarMap() {
        Map<String, String> result = userMarketingAccountManager.getLeadAndWeChatAvatarUrlMap("74164", 1063, Lists.newArrayList("60e51ff24199ad0001149e4a"));
        log.info("=== result : " + result);
    }

    @Test
    public void getTagByUserMarketingAccountIds() {
        Result<TagNameList> result = userMarketingAccountService.getTagsByUserMarketingId("74164", 1000, "e550c7df90b74795aeb75f7e781ed478");
        log.info("=== result : " + result);
    }

    @Test
    public void batchAddTagsToUserMarketings() throws Exception {
        Result<Boolean> result = userMarketingAccountService.batchAddTagsToUserMarketings("74164", 1000, null, Lists.newArrayList("e18ff260620e4f908bf95ee13e27e2c4"), TagNameList.newTagNameList(new TagName("客户等级", "超级VIP")));
        log.info("=== result : " + result);
        //    Thread.sleep(********);
    }

    @Test
    public void testAddTagToCrmData() {
        AddTagToCrmDataArg addTagToCrmDataArg = new AddTagToCrmDataArg();
        addTagToCrmDataArg.setTenantId(2);
        addTagToCrmDataArg.setCrmObjectDescribeApiName(CrmObjectApiNameEnum.CUSTOMER.getName());
        addTagToCrmDataArg.setCrmObjectId("5d390bf4b3bcaa000191fb56");
        addTagToCrmDataArg.setTagGroupName("TG");
        addTagToCrmDataArg.setTagName("TQ");
        Result<Void> result = userMarketingAccountService.addTagToCrmData(addTagToCrmDataArg);
        System.out.println(result);
    }

    @Test
    public void checkUserMarketingAccount() {
        AddTagToCrmDataArg addTagToCrmDataArg = new AddTagToCrmDataArg();
        addTagToCrmDataArg.setTenantId(2);
        addTagToCrmDataArg.setCrmObjectDescribeApiName(CrmObjectApiNameEnum.CUSTOMER.getName());
        addTagToCrmDataArg.setCrmObjectId("5d390bf4b3bcaa000191fb56");
        addTagToCrmDataArg.setTagGroupName("TG");
        addTagToCrmDataArg.setTagName("TQ");
        Result result = userMarketingAccountService.checkUserMarketingAccount("74164", 1000, "3f9380c17b934e4a9bd9f72b20830f4b", ChannelEnum.getAllChannelApiName());
        System.out.println(result);
    }

    @Test
    public void batchMergeUserMarketingAccountsFromTags1() {
        Result result = userMarketingAccountService.batchMergeUserMarketingAccountsFromTags("77991", 1000, GsonUtil.fromJsonSerializingNull("{\"tagNameList\":[{\"firstTagName\":\"测试12\",\"secondTagName\":\"K歌达人\"},{\"firstTagName\":\"测试12\",\"secondTagName\":\"测试张娟\"}],\"userMarketingAccountId\":\"8d9a5ccedb4e42bb8166d080b974374d\"}", BatchMergeUserMarketingAccountsFromTagsArg.class));
        System.out.println(result);
    }

    @Test
    public void listBySecondGradeTagName() {
        List<TagName> result = userTagManager.listBySecondGradeTagName("74164", 1000, Lists.newArrayList("华北区", "忠实粉丝"));
        userMarketingAccountService.batchAddTagsToUserMarketings("74164", 1000, ChannelEnum.getAllChannelApiName(), Lists.newArrayList("bb8b04b713d44e3e9b0f4479742f779c"), TagNameList.convert(result));
        System.out.println(result);
    }

    @Test
    public void pageAction() {
        PageUserMarketingActionStatisticArg pageUserMarketingActionStatisticArg = new PageUserMarketingActionStatisticArg();
        pageUserMarketingActionStatisticArg.setPageNo(1);
        pageUserMarketingActionStatisticArg.setPageSize(20);
//        pageUserMarketingActionStatisticArg.setUserMarketingId("4d8f472ee42041e9a18e5e34976e3eaa");
//        pageUserMarketingActionStatisticArg.setUserMarketingId("2771c63fb0324c6b93b35636f3fa1df2");
        //  pageUserMarketingActionStatisticArg.setSpreadMemberId("member_test");
        Result<PageResult<UserMarketingActionResult>> result = userMarketingAccountService.pageUserMarketingActionStatistic("88146", *********, pageUserMarketingActionStatisticArg);
//        pageUserMarketingActionStatisticArg.setUserMarketingId("10b9fde2b32345339fa0e87173daa720");
        //  Result<PageResult<UserMarketingActionResult>> result1 = userMarketingAccountService.pageUserMarketingActionStatistic("83668", 1000, pageUserMarketingActionStatisticArg);
        log.info("结果： {}", JSON.toJSONString(result));
    }

    @Test
    public void pageAction2() {
        PageUserMarketingActionStatisticByCrmObjectArg arg = new PageUserMarketingActionStatisticByCrmObjectArg();
        arg.setPageNo(1);
        arg.setPageSize(2);
        arg.setObjectId("64ddc88c4fbb62000142de40");
        arg.setObjectApiName("LeadsObj");
        Result<PageResult<UserMarketingActionResult>> result = userMarketingAccountService.pageUserMarketingActionStatisticByCrmObject("88146", -10000, arg);
        System.out.println(result);
    }

    @Test
    public void listUserMarketingAccount() {
        String string1 = "88146";
        Integer integer1 = 1000;
        ListByFilterArg listByFilterArg1 = new ListByFilterArg();
        List arrayList1 = new ArrayList();
        listByFilterArg1.setFilterDatas(arrayList1);
        listByFilterArg1.setPageNo(1);
        listByFilterArg1.setPageSize(40);
        listByFilterArg1.setTagOperator("IN");
//
//        List<FilterData> filterDatas = Lists.newArrayList();
//        FilterData filterData = new FilterData();
//        filterData.setObjectAPIName("MemberObj");
//        SearchTemplateQuery templateQuery = new SearchTemplateQuery();
//      //  Filter filter = new Filter("_id", "ISN",Lists.newArrayList(""));
//
//        Filter filter = new Filter("_id", "IN", Lists.newArrayList("01e4a38b42e448e8a128df753052c1c1" , "945ebf5967514597bcc9d161af60e256" , "9b863bed2f6a4e76a3e79d7522ac6e3b" , "c836484167414cf8b523d1a11450977d" ));
//        filter.setFieldType("1");
//        templateQuery.getFilters().add(filter);
//        filterData.setQuery(templateQuery);
//        filterDatas.add(filterData);
//        listByFilterArg1.setFilterDatas(filterDatas);
        Result result = userMarketingAccountService.listUserMarketingAccount(string1, integer1, listByFilterArg1);
        System.out.println(result);
    }

    @Test
    public void listUserMarketingAccount2() {
        String ea = "78060";
        Integer fsUserId = -10000;
        FilterData filterData = new FilterData();
        filterData.setObjectAPIName("EnterpriseInfoObj");
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilter("name", OperatorConstants.ISN, new ArrayList<>(0));
        filterData.setQuery(searchTemplateQuery);
        ListByFilterArg listByFilterArg = new ListByFilterArg();
        listByFilterArg.setFilterDatas(ImmutableList.of(filterData));
        listByFilterArg.setSearchType(MarketingUserSearchTypeEnum.BY_ENTERPRISE_LIBRARY.getSearchType());
        listByFilterArg.setPageNo(1);
        listByFilterArg.setPageSize(1);
        Result result = userMarketingAccountService.listUserMarketingAccount(ea, fsUserId, listByFilterArg);
        System.out.println(result);

    }

    @Test
    public void getUserMarketingAccountByObject1() {
        String ea = "74164";
        Integer fsUserId = 1071;
        GetUserMarketingAccountByObjectArg arg = new GetUserMarketingAccountByObjectArg();
        arg.setCrmObjectApiName("LeadsObj");
        arg.setCrmObjectId("62b288d5ded24300019c9afc");
        Result<UserMarketingAccountIdResult> result = userMarketingAccountService.getUserMarketingAccountByObject(ea, fsUserId, arg);
        log.info("getUserMarketingAccountByObject result:{}", result);
    }

    @Test
    public void getUserMarketingAccountByObject() {
        String ea = "88146";
        Integer fsUserId = 1000;
        GetUserMarketingAccountByObjectArg arg = new GetUserMarketingAccountByObjectArg();
        arg.setCrmObjectApiName("LeadsObj");
        arg.setCrmObjectId("65a8f043b1e5ec0007e255ee");
        Result<UserMarketingAccountIdResult> result = userMarketingAccountService.getUserMarketingAccountByObject(ea, fsUserId, arg);
        log.info("getUserMarketingAccountByObject result:{}", result);
    }

    @Test
    public void queryTagByCrmObjectId() {
        String ea = "74164";
        Integer fsUserId = 1071;
        QueryTagByCrmObjectIdArg arg = new QueryTagByCrmObjectIdArg();
        arg.setCrmObjectApiName("LeadsObj");
        arg.setCrmObjectId("62b288d5ded24300019c9afc");
        Result<TagNameList> result = userMarketingAccountService.queryTagByCrmObjectId(ea, fsUserId, arg);
        log.info("result:{}", result);
    }

    @Test
    public void pageUserMarketingLookUpStatisticByObject() {
        String ea = "83668";
        Integer fsUserId = 1000;
        UserMarketingLookUpStatisticByObjectArg arg = new UserMarketingLookUpStatisticByObjectArg();
        arg.setObjectType(26);
        arg.setActionType(64);
        arg.setObjectId("a4f87182a59d4acead711f0d7da2ba89");
        arg.setPageNum(1);
        arg.setPageSize(10);
        Result<PageResult<UserMarketingLookUpStatisticByObjectResult>> result = userMarketingAccountService.pageUserMarketingLookUpStatisticByObject(ea, arg);
        log.info("result:{}", result);
    }

    @Test
    public void exportUserMarketingLookUpStatisticByObject() {
        UserMarketingLookUpStatisticByObjectArg arg = new UserMarketingLookUpStatisticByObjectArg();
        arg.setObjectType(26);
        arg.setActionType(64);
        arg.setObjectId("a4f87182a59d4acead711f0d7da2ba89");
        arg.setPageNum(1);
        arg.setPageSize(10);
        userMarketingAccountService.exportUserMarketingLookUpStatisticByObject("83668", 1000, arg);
        System.out.println("111");
    }

    @Test
    public void getCustomerContactTest() {
        userMarketingAccountManager.getCustomerContactData("88146", Lists.newArrayList("64141dbc5146f40001e39b46"));
    }

    @Test
    public void listMiniAppUserMarketingAccount() {


        ListByFilterArg listByFilterArg1 = new ListByFilterArg();
        List arrayList1 = new ArrayList();
        listByFilterArg1.setFilterDatas(arrayList1);
        listByFilterArg1.setPageNo(1);
        listByFilterArg1.setPageSize(10);
//        listByFilterArg1.setKeyword("无");
        Result<PageResult<UserMarketingAccountData>> pageResultResult = userMarketingAccountService.listMiniAppUserMarketingAccount("88146", 1000, listByFilterArg1);
        System.out.println(pageResultResult);
    }

    @Test
    public void getInfoByIdentify() {
        GetInfoByIdentifyArg getInfoByIdentifyArg = new GetInfoByIdentifyArg();
        getInfoByIdentifyArg.setTenantId(88146);
        getInfoByIdentifyArg.setFsUserId(1000);
        getInfoByIdentifyArg.setIdentifyType(0);
        getInfoByIdentifyArg.setIdentifyValue("f5873e0a9ff84d00af84e729ccab37f1");
        FunctionResult<GetInfoByIdentifyVO> voResult = userMarketingAccountService.getInfoByIdentify(getInfoByIdentifyArg);
        log.info("getInfoByUserMarketingIdTest 结果： {}", JsonUtil.toJson(voResult));

        getInfoByIdentifyArg.setIdentifyType(1);
        getInfoByIdentifyArg.setIdentifyValue("***********");
        FunctionResult<GetInfoByIdentifyVO> voResult2 = userMarketingAccountService.getInfoByIdentify(getInfoByIdentifyArg);
        log.info("getInfoByPhoneTest 结果： {}", JsonUtil.toJson(voResult2));

        getInfoByIdentifyArg.setIdentifyType(2);
        getInfoByIdentifyArg.setIdentifyValue("AccountObj:653231b8562db90001224b7e");
        FunctionResult<GetInfoByIdentifyVO> voResult3 = userMarketingAccountService.getInfoByIdentify(getInfoByIdentifyArg);
        log.info("getInfoByCustomerTest 结果： {}", JsonUtil.toJson(voResult3));
    }

    @Test
    public void bulkHangTagWithCrmNotice(){
        String ea = "88146";
        Integer fsUserId = 1000;
        BulkHangTagWithCrmNoticeArg arg = new BulkHangTagWithCrmNoticeArg();
        arg.setObjectApiName("WechatWorkExternalUserObj");
        arg.setTagIds(Lists.newArrayList("6683716afbbb1600012cfb33"));
        arg.setDataIds(Lists.newArrayList("649bff4cdefe440001bdbef9", "63eb4f6629be59000197f36e"));
        List<TagName> tagNames = Lists.newArrayList();
        tagNames.add(new TagName("汽车", "汽车"));
        arg.setTagNames(tagNames);
        userMarketingAccountService.bulkHangTagWithCrmNotice(ea, fsUserId, arg);
    }
}
