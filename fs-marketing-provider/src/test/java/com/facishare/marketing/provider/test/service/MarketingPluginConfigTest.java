package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.api.arg.plugin.BatchGetPluginStatusArg;
import com.facishare.marketing.api.arg.plugin.UpdatePluginSettingArg;
import com.facishare.marketing.api.result.PluginStatusResult;
import com.facishare.marketing.api.result.QueryScenaryResult;
import com.facishare.marketing.api.service.marketingplugin.MarketingPluginService;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 11:37
 */
@Slf4j
public class MarketingPluginConfigTest extends BaseTest {

    @Autowired
    private MarketingPluginService marketingPluginService;

    @Test
    public void savePartnerMarketing(){
        String ea = "74164";
        int type = 30;
        boolean status = true;
        Result<Void> result = marketingPluginService.saveOrUpdateMarketingPlugin(ea, type, status);
        System.out.println(result);
    }

    @Test
    public void saveLivePlugin(){
        String ea = "74164";
        Integer type = MarketingPluginTypeEnum.VHALL_LIVE.getType();
        Boolean status = true;
        Result<Void> result = marketingPluginService.saveOrUpdateMarketingPlugin(ea, type, status);
        System.out.println(result);
        status = false;
        result = marketingPluginService.saveOrUpdateMarketingPlugin(ea, type, status);
        type = MarketingPluginTypeEnum.POLYV_LIVE.getType();
        status = true;
        result = marketingPluginService.saveOrUpdateMarketingPlugin(ea, type, status);
        status = false;
        result = marketingPluginService.saveOrUpdateMarketingPlugin(ea, type, status);
    }

    @Test
    public void zhishikuPlugin(){
        String ea = "88146";
        Result<List<QueryScenaryResult>> result = marketingPluginService.queryServiceKnowledgeScenaryList(ea);
        log.info("结果： {}", result);
        Result<QueryScenaryResult> result1 = marketingPluginService.queryServiceKnowledgeScenarySetting(ea);
        System.out.println(result1);
        marketingPluginService.insertOrUpdateServiceKnowledgeScenary(ea, "wechat");
        marketingPluginService.insertOrUpdateServiceKnowledgeScenary(ea, "knowledgeaTnszs");
        result1 = marketingPluginService.queryServiceKnowledgeScenarySetting(ea);
        System.out.println(result1);
    }

    @Test
    public void googlePlugin(){
        String ea = "83354";
        marketingPluginService.saveOrUpdateMarketingPlugin(ea, 59, true);
    }

    @Test
    public void sdrPlugin(){
        String ea = "88146";
        marketingPluginService.saveOrUpdateMarketingPlugin(ea, 60, true);
    }

    @Test
    public void checkLicense() {
        marketingPluginService.checkLicense("88146", "marketing_overseas_plugin_app");
    }


    @Test
    public void batchGetMarketingPluginStaus() {
        BatchGetPluginStatusArg arg = new BatchGetPluginStatusArg();
        arg.setEa("88146");
        Result<PluginStatusResult> result = marketingPluginService.batchGetMarketingPluginStaus(arg);
        log.info("结果1， result: {}", JsonUtil.toJson(result));
        List<Integer> typeList = new ArrayList<>();
        typeList.add(62);
        arg.setPluginTypeList(typeList);
        result = marketingPluginService.batchGetMarketingPluginStaus(arg);
        log.info("结果2， result: {}", JsonUtil.toJson(result));
        typeList = new ArrayList<>();
        typeList.add(11111111);
        arg.setPluginTypeList(typeList);
        result = marketingPluginService.batchGetMarketingPluginStaus(arg);
        log.info("结果3， result: {}", JsonUtil.toJson(result));
    }

}
