package com.facishare.marketing.provider.dao;

import com.beust.jcommander.internal.Lists;
import com.facishare.marketing.common.enums.advertiser.tencent.TencentAdStatusEnum;
import com.facishare.marketing.common.enums.advertiser.tencent.TencentBidModEnum;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.advertiser.tencent.TencentAdGroupDAO;
import com.facishare.marketing.provider.dao.baidu.AdKeywordDAO;
import com.facishare.marketing.provider.dao.mail.MailSendTaskDAO;
import com.facishare.marketing.provider.dao.qywx.QyWxAddressBookDAO;
import com.facishare.marketing.provider.dao.sms.mw.MwSmsTemplateDao;
import com.facishare.marketing.provider.entity.QyWxAddressBookEntity;
import com.facishare.marketing.provider.entity.advertiser.tencent.TencentAdGroupEntity;
import com.facishare.marketing.provider.test.BaseTest;
import com.github.mybatis.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class AdKeywordDAOTest extends BaseTest {
    @Autowired
    private AdKeywordDAO adKeywordDAO;

    @Autowired
    private MwSmsTemplateDao mwSmsTemplateDao;

    @Autowired
    private QyWxAddressBookDAO addressBookDAO;

    @Autowired
    private TencentAdGroupDAO tencentAdGroupDAO;

    @Autowired
    private MailSendTaskDAO mailSendTaskDAO;

    @Test
    public void batchInsertHeadlinesKeyword() {
        adKeywordDAO.batchInsertHeadlinesKeyword(Lists.newArrayList());
    }

    @Test
    public void tes() {
        Page page = new Page();
        page.setPageSize(10);
        page.setPageNo(1);
        mwSmsTemplateDao.queryTemplateByEa("", "74164",null, null, null, null, page);
        mwSmsTemplateDao.listSmsTemplateForChannel("", "74164", null, null, page);
    }

    @Test
    public void test() {
        QyWxAddressBookEntity qyWxAddressBookEntity = new QyWxAddressBookEntity();
        qyWxAddressBookEntity.setId(UUIDUtil.getUUID());
        qyWxAddressBookEntity.setEa("74164");
        qyWxAddressBookEntity.setUserId("wowx1mDAAAT3D1Ri80QvulUVACiyy9Lg-v5");
        qyWxAddressBookEntity.setDepartment("[8]");
        qyWxAddressBookEntity.setOrder("[0]");
        qyWxAddressBookEntity.setStatus(4);
        qyWxAddressBookEntity.setName("Hickey2");
        addressBookDAO.upsertQyWxAddressBookEntity(qyWxAddressBookEntity);
    }

    @Test
    public void batchUpdateTencentAdGroup() {
        List<TencentAdGroupEntity> adGroupEntityList = new ArrayList<>();
        TencentAdGroupEntity entity = new TencentAdGroupEntity();
        entity.setCampaignId(5417161444L);
        entity.setAdgroupId(5417309816L);
        entity.setAdgroupName("hhhhh");
        Integer status = TencentAdStatusEnum.ADGROUP_STATUS_ACTIVE.getType();
        entity.setStatus(status == null ? 0 : status);
        entity.setBidAmount(1D);
        entity.setTotalBudget(1D);
        entity.setDailyBudget(2D);
        Integer bigMode = TencentBidModEnum.BID_MODE_CPA.getType();
        entity.setBidMode(bigMode == null ? 0 : bigMode);
        entity.setSiteSet(Lists.newArrayList(""));
        entity.setIsDeleted(false);
        entity.setUpdateTime(new Date());
        adGroupEntityList.add(entity);
        tencentAdGroupDAO.batchUpdateTencentAdGroup("83883", "84d929dac8674c21a27928fde636e939", adGroupEntityList);
    }
}
