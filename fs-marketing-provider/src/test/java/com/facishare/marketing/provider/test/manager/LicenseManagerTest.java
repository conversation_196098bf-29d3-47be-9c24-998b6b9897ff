package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.manager.LicenseManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created  By zhoux 2020/05/09
 **/
public class LicenseManagerTest extends BaseTest {

    @Autowired
    private LicenseManager licenseManager;

    @Test
    public void listProductVersion() {
        licenseManager.listProductVersion("74164", "CRM");
    }

    @Test
    public void listModule() {
        licenseManager.listModule("74164", "CRM");
    }

    @Test
    public void queryModulePara() {
        List<ModuleParaPojo> crm = licenseManager.queryModulePara("88146", "CRM", "marketing_strategy_dynamic_target_number_app", Sets.newHashSet("marketing_strategy_dynamic_target_number_limit"));
        System.out.println(GsonUtil.toJson(crm));
        if (CollectionUtils.isNotEmpty(crm)) {
            int sum = crm.stream().map(ModuleParaPojo::getParaValue).mapToInt(Integer::valueOf).sum();
            System.out.println(sum);
        }
    }

}
