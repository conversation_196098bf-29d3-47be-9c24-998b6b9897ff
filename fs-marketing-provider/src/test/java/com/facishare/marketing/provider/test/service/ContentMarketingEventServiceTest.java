package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg;
import com.facishare.marketing.api.arg.ListMarketingActivityInContentMarketingEventArg;
import com.facishare.marketing.api.arg.contentmarketing.ListContentMarketingEventArg;
import com.facishare.marketing.api.arg.hexagon.HexagonStatisticArg;
import com.facishare.marketing.api.result.GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult;
import com.facishare.marketing.api.result.PageResult;
import com.facishare.marketing.api.result.calendar.MarketingEventsBriefResult;
import com.facishare.marketing.api.result.contentmaketing.MarketingContentVO;
import com.facishare.marketing.api.result.kis.TempListAllMarketingActivityResult;
import com.facishare.marketing.api.service.ContentMarketingEventService;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ContentMarketingEventServiceTest extends BaseTest {

    @Autowired
    private ContentMarketingEventService contentMarketingEventService;

    @Test
    public void getListTest() {
        ListContentMarketingEventArg arg = new ListContentMarketingEventArg();
        arg.setPageNo(1);
        arg.setPageSize(20);
        //arg.setEventType("content_marketing");
        Result<PageResult<MarketingEventsBriefResult>>  result = contentMarketingEventService.listContentMarketingEvent("zhanghui0916",1000,arg);
        log.info("=== result : {}",result);
    }
    @Test
    public void listMarketingContent() {
        Result<com.facishare.marketing.common.result.PageResult<MarketingContentVO>>   result = contentMarketingEventService.listMarketingContent("88146",1000,"662791aab3daca0007d97799", Lists.newArrayList(4,6,26,16,9999),1,10000);
        log.info("=== result : {}",result);
    }
    @Test
    public void listMarketingActivityInContentMarketingEvent() {
        ListMarketingActivityInContentMarketingEventArg arg = new ListMarketingActivityInContentMarketingEventArg();
        arg.setPageNum(1);
        arg.setPageSize(20);
        arg.setMarketingEventId("66a70e4ebae6d70007da937f");
        arg.setObjectId("b82f0c23b5b44e0ab000844aca128c9f");
        arg.setObjectType(6);
        Result<com.facishare.marketing.common.result.PageResult<TempListAllMarketingActivityResult>>  result = contentMarketingEventService.listMarketingActivityInContentMarketingEvent("88146",1000,arg);
        log.info("=== result : {}",result);
    }

    @Test
    public void getOrCreateQrCodeByMarketingEventIdAndObjectInfo() {
        GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg arg = JSON.parseObject("{\"h5QrCodeType\":\"19999\",\"h5QrCodeValue\":\"{\\\"byshare\\\":1,\\\"id\\\":\\\"ae7a87f864d241c788ee17b0a2f81ecd\\\",\\\"marketingActivityId\\\":\\\"\\\",\\\"marketingEventId\\\":\\\"62ff29d7c081ee00013660f0\\\"}\",\"marketingEventId\":\"62ff29d7c081ee00013660f0\",\"objectType\":9999,\"objectId\":\"ae7a87f864d241c788ee17b0a2f81ecd\",\"feedKey\":\"936b79vq2aqc788ee17b02695\",\"miniappQrCodeValue\":\"{\\\"marketingActivityId\\\":\\\"\\\",\\\"marketingEventId\\\":\\\"62ff29d7c081ee00013660f0\\\"}\"}",GetOrCreateQrCodeByMarketingEventIdAndObjectInfoArg.class);
        Result<GetOrCreateQrCodeByMarketingEventIdAndObjectInfoResult> orCreateQrCodeByMarketingEventIdAndObjectInfo = contentMarketingEventService.getOrCreateQrCodeByMarketingEventIdAndObjectInfo("83668", 1001, arg);
        log.info("=== result : {}", orCreateQrCodeByMarketingEventIdAndObjectInfo);
    }
    @Test
    public void getHexagonContent() {
        HexagonStatisticArg arg = new HexagonStatisticArg();
        arg.setSiteId("");
        arg.setMarketingEvenId("651240e192b8e4000132917c");
        Result<MarketingContentVO> result = contentMarketingEventService.getHexagonContent("89193", 1000, arg);
        log.info("=== result : {}", result);
    }

}
