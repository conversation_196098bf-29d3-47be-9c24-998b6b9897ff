/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.facishare.common.UidUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.innerArg.CreateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.innerArg.UpdateEmailSendRecordDetailObjArg;
import com.facishare.marketing.provider.manager.crmobjectcreator.EmailSendRecordDetailObjManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.UUID;


public class EmailSendRecordObjManagerTest extends BaseTest {

    @Autowired
    private EmailSendRecordDetailObjManager emailSendRecordObjManager;

    @Test
    public void testCreateObj() {
        CreateEmailSendRecordDetailObjArg arg = new CreateEmailSendRecordDetailObjArg();
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setStatusCode("112");
        arg.setClickCount(2);
        arg.setOpenCount(3);
        arg.setTaskId("30c3bb69f7c84adf8388f790e3d12dc9");
        arg.setSendStatus("sent");
        arg.setReceiver("<EMAIL>");
        arg.setSendTime(new Date());
        arg.setPreviewUrl("www.fxiaoke.com/yxt?taskid=testId");
        arg.setIsUnSubscribe(true);
        arg.setSpamReport(true);
        arg.setFailReason("邮件无效");
        emailSendRecordObjManager.tryCreateOrUpdateObj(arg);

        arg.setTaskId("30c3bb69f7c84adf8388fttttttttt");
        arg.setReceiver("<EMAIL>");
        arg.setSendTime(new Date());
        emailSendRecordObjManager.tryCreateOrUpdateObj(arg);
        emailSendRecordObjManager.tryCreateOrUpdateObj(arg);

        arg.setTaskId(UUIDUtil.getUUID());
        arg.setReceiver("<EMAIL>");
        arg.setSendTime(new Date());
        emailSendRecordObjManager.tryCreateOrUpdateObj(arg);
        emailSendRecordObjManager.tryCreateOrUpdateObj(arg);
    }

    @Test
    public void createDescribeTest() {
        emailSendRecordObjManager.getOrCreateObjDescribe("zhenju0111");
    }

    @Test
    public void updateReceiverFieldDescribeTest() {
        emailSendRecordObjManager.updateReceiverFieldDescribe("88146");
    }


    @Test
    public void invalidRepeatObj() {
        emailSendRecordObjManager.invalidRepeatObj("88146,2024-01-01 00:00:00,2024-05-31 23:59:59");
    }

    @Test
    public void fixSendStatistic() {
        emailSendRecordObjManager.fixSendStatistic("88146,18eaa92ec29844a4b0d6863bc666e85c,true,true");
    }
}
