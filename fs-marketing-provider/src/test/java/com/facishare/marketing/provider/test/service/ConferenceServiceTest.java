/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.marketing.api.GetCampaignDataArg;
import com.facishare.marketing.api.UpdateSignInSuccessSettingArg;
import com.facishare.marketing.api.arg.usermarketingaccounttag.MaterialTagFilterArg;
import com.facishare.marketing.api.data.usermarketingaccount.FilterData;
import com.facishare.marketing.api.result.ticket.QueryTicketListResult;
import com.facishare.marketing.api.service.conference.ConferenceService;
import com.facishare.marketing.api.vo.conference.QueryConferenceListVO;
import com.facishare.marketing.api.vo.conference.VerifyEnrollPhoneVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

import java.util.List;
import java.util.Map;

/**
 * Created by zhengh on 2020/10/19.
 */
public class ConferenceServiceTest extends BaseTest {

    @Autowired
    private ConferenceService conferenceService;


    @Test
    public void test() {
        GetCampaignDataArg getCampaignDataArg = new GetCampaignDataArg();
        getCampaignDataArg.setConferenceId("3f6608aadcdb4effa32724fcc08b4e38");
        getCampaignDataArg.setMarketingEventId("604096665beb22000141f459");
        getCampaignDataArg.setPhone("15216146853");
        Result campaignData = conferenceService.getCampaignData(getCampaignDataArg);
        System.out.println(campaignData);
    }

    @Test
    public void test1() {
        UpdateSignInSuccessSettingArg arg = JSONObject.parseObject("{\"conferenceId\":\"3f6608aadcdb4effa32724fcc08b4e38\",\"firstSignSuccess\":\"1\",\"secondSignSuccess\":\"2\",\"isShowNotice\":true,\"noticeSetting\":\"3\",\"customFieldSettings\":\"[{\\\"field_5Iim2__c\\\":\\\"1\\\"}]\"}", UpdateSignInSuccessSettingArg.class);
        Result result = conferenceService.updateSignInSuccessSetting("74164", arg);
        System.out.println(result);
    }

    @Test
    public void saveSignInSuccessSetting() {
        UpdateSignInSuccessSettingArg arg = new UpdateSignInSuccessSettingArg();
        arg.setConferenceId("05b265e139314376b38b150e5c8b30d8");
        arg.setFirstSignSuccess("签到成功啦");
        arg.setSecondSignSuccess("您已完成签到，无需再次验证");
        arg.setIsShowNotice(true);
        arg.setNoticeSetting("温馨提示：防止后期遗忘，您可以截图保存相关信息");
        arg.setEmailSign(true);
        arg.setPhoneSign(false);
        arg.setAuditingTip("审核中,请先等待");
        arg.setAuditingContent("审核中,联系工作人员处理");
        arg.setAuditingQrcodeType(1);
        arg.setAuditingQrcodeId("3949495959599555");
        arg.setAuditFailTip("审核未通过,请查看");
        arg.setAuditFailContent("审核未通过,联系工作人员处理");
        arg.setAuditFailQrcodeType(2);
        arg.setAuditFailQrcodeId("33444343333333");
        Result result = conferenceService.updateSignInSuccessSetting("88146", arg);
        System.out.println(result);
    }

    @Test
    public void queryConferenceList() {
        QueryConferenceListVO queryConferenceListVO1 = new QueryConferenceListVO();
        queryConferenceListVO1.setEa("88146");
        queryConferenceListVO1.setFsUserId(1000);
        queryConferenceListVO1.setKeyword("");
        queryConferenceListVO1.setPageSize(30);
        queryConferenceListVO1.setPageNum(1);
        queryConferenceListVO1.setSpreadSearch(false);
        queryConferenceListVO1.setFilterData(new FilterData());
        queryConferenceListVO1.setNeedMarketingActivityResult(false);
        MaterialTagFilterArg filterArg = new MaterialTagFilterArg();
        filterArg.setType(1);
        filterArg.setMaterialTagIds(Lists.newArrayList("bfbe5d27b8034f0dac1c4310ff4c8313"));
        queryConferenceListVO1.setMaterialTagFilter(filterArg);
        Result result = conferenceService.queryConferenceList(queryConferenceListVO1);
        System.out.printf("yes:" + result);
    }

    @Test
    public void queryCustomizeTicket(){
        VerifyEnrollPhoneVO vo = new VerifyEnrollPhoneVO();
        vo.setAssociationId("1503a334c66d40b5a6e4aa77853c4aab");
        vo.setEa("88146");
        vo.setCode("1100");
        vo.setFsUserId(1000);
        Result<List<QueryTicketListResult>> listResult = conferenceService.queryTicketCampaignList(vo);
        System.out.println(listResult);
    }

    @Test
    public void queryCustomizeTicketCampaignData(){
        VerifyEnrollPhoneVO vo = new VerifyEnrollPhoneVO();
        vo.setAssociationId("038736ae7f894d1ab1dd6aae7693223a");
        vo.setEa("88146");
        vo.setCode("23547116");
        vo.setFsUserId(1000);
        Result<List<Map<String, Object>>> listResult = conferenceService.queryCustomizeTicketCampaignData(vo);
        System.out.println(listResult);
    }

}