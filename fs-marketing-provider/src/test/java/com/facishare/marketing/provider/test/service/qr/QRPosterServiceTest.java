/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.qr;

import com.facishare.marketing.api.arg.DeleteMaterialArg;
import com.facishare.marketing.api.arg.TopMaterialArg;
import com.facishare.marketing.api.arg.objectgroup.DeleteObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.EditObjectGroupArg;
import com.facishare.marketing.api.arg.objectgroup.SetObjectGroupArg;
import com.facishare.marketing.api.result.EditObjectGroupResult;
import com.facishare.marketing.api.result.qr.*;
import com.facishare.marketing.api.service.qr.QRPosterService;
import com.facishare.marketing.api.vo.CreateQRPosterVO;
import com.facishare.marketing.api.vo.QueryCreateChuangKeTieJsSdkOptionVO;
import com.facishare.marketing.common.enums.AccountTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.FileUtil;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.result.QrCodeResult;
import com.facishare.wechat.union.core.api.model.result.BatchGetByWxAppIdsResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class QRPosterServiceTest extends BaseTest {

    @Autowired
    private QRPosterService qrPosterService;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Autowired
    private com.facishare.wechat.proxy.service.QrCodeService qrCodeService;
    @Autowired
    private com.facishare.wechat.union.core.api.service.OuterServiceWechatService outerServiceWechatService;

    @Test
    public void createQRPosterTest() {

        //String bgTAPath = fileV2Manager.uploadToTApath(FileUtil.file2Bytes("/Users/<USER>/images/bg.jpg"), "jpg", "2", 1126);
        //String finalTAPath = fileV2Manager.uploadToTApath(FileUtil.file2Bytes("/Users/<USER>/images/final.jpg"), "jpg", "2", 1126);

        /*{"marketingEventId":"5faa83469259100001b18940","forwardType":3,"title":"邀约海报","bgTAPath":"TA_69e92858997242318c739ef0e03c048d.jpg","finalTAPath":"TA_369b4a34c7f1405b90763914b8d51d41.jpg","qrStyle":"{\"width\":200,\"height\":200,\"top\":1100,\"left\":275,\"cutXY\":{\"left\":63,\"top\":0},\"scale\":5.558333333333334,\"textInfo\":{\"text\":\"姓名\",\"x\":208,\"y\":800,\"font\":66.7,\"color\":\"#fff\",\"width\":333.5,\"align\":\"center\"}}","type":2,"qrCodeId":46193,"qrCodeUrl":null,"targetId":"09ae503015914c22bea19d051343e4d8","appId":null,"wxAppId":null}*/

       /* ["-null-","74164",1057,"5faa83469259100001b18940",3,46193,"-null-","09ae503015914c22bea19d051343e4d8","-null-","邀约海报","TA_69e92858997242318c739ef0e03c048d.jpg","TA_369b4a34c7f1405b90763914b8d51d41.jpg","{\"width\":200,\"height\":200,\"top\":1100,\"left\":275,\"cutXY\":{\"left\":63,\"top\":0},\"scale\":5.558333333333334,\"textInf
            o\":{\"text\":\"姓名\",\"x\":208,\"y\":800,\"font\":66.7,\"color\":\"#fff\",\"width\":333.5,\"align\":\"center\"}}",2], result:[{"errCode":-3,"errMsg":"系统错误"}]*/
        CreateQRPosterVO vo = new CreateQRPosterVO();
        vo.setEa("89196");
        vo.setUserId(1000);
        vo.setMarketingEventId("652f408704fff60001e5ff06");
        vo.setForwardType(QRPosterForwardTypeEnum.QYWX_QR_CODE.getType());
        vo.setTargetId("75be26fbefe3465ab16ce6717ef7a94c");
        vo.setQrCodeUrl("https://wework.qpic.cn/wwpic3az/584249_x2BtPvFRQ8yTwOo_1699607052/0");
        vo.setTitle("新的海报");
        vo.setBgTAPath("TA_57e89e445e8b4191b9a88f0fee19c1e8.png");
        vo.setFinalTAPath("TA_00c6a930fdbb489bab9c293d464476e7.png");
        vo.setQrStyle("{\"width\":200,\"height\":200,\"top\":1100,\"left\":275,\"cutXY\":{\"left\":0,\"top\":0},\"scale\":5.558333333333334,\"textInfo\":{\"text\":\"请输入\",\"x\":206.3083333333334,\"y\":null,\"font\":66.7,\"color\":\"#fff\",\"width\":333.5,\"align\":\"center\"}}");
        vo.setType(1);
        vo.setUserAddSettings(2);
        vo.setFailedOperation(1);
        Result<QueryQRPosterByEaListUnitResult> result = qrPosterService.createQRPoster(vo);
        QueryQRPosterByEaListUnitResult queryQRPosterByEaListUnitResult = result.getData();
        System.out.println("qrPosterId: "+queryQRPosterByEaListUnitResult.getQrPosterId());
        System.out.println("finalUrl: "+queryQRPosterByEaListUnitResult.getQrPosterUrl());
    }

    @Test
    public void spreadQRPosterTest() {

        String finalTAPath = fileV2Manager.uploadToTApath(FileUtil.file2Bytes("/Users/<USER>/images/final.jpg"), "jpg", "55487", 1004);

        Result<SpreadQRPosterResult> result = qrPosterService.spreadQRPoster("05c3dafc05034b8dadbcd551252d45a6", 1584, "55487", 1004, finalTAPath, null, null, null, null);
        SpreadQRPosterResult spreadQRPosterResult = result.getData();
        System.out.println("posterFinalApath: "+spreadQRPosterResult.getPosterFinalApath());
        System.out.println("posterFinalUrl: "+spreadQRPosterResult.getPosterFinalUrl());
    }

    @Test
    public void queryListByEaTest() {
        Result<PageResult<QueryQRPosterByEaListUnitResult>> result = qrPosterService.queryListByEa("88146",1000,10,1,null,null,null,true,1,null);
        log.info("queryListByEaTest result 1 : {}", result);

    }

    @Test
    public void queryDeatilTest() {
        Result<QueryQRPosterDetailResult> result = qrPosterService.queryDetail("74164", 100, "2cc2edbabb3242b9b4a03925989dc2ae", null, false);
        QueryQRPosterDetailResult queryQRPosterDetailResult = result.getData();
        System.out.println("queryQRPosterDetailResult: "+queryQRPosterDetailResult);
    }


    //["74164",1089,10,1,"-null-","-null-",[1,2,3,4,5,7,8,9,10],"2c67381ffa494621823440653cca96be",2,true]
    @Test
    public void queryQRPosterByForwardTypeAndTargetIdTest() {
        Result<PageResult<QueryQRPosterByEaListUnitResult>> result = qrPosterService.queryListByForwardTypeAndTargetId("74164", 1089, 10, 1, null,null, Arrays.asList(1,2,3,4,5,7,8,9,10), "2c67381ffa494621823440653cca96be", 2, true);
        PageResult<QueryQRPosterByEaListUnitResult> pageResult = result.getData();
    }

    @Test
    public void deleteQRPosterTest() {
        Result result = qrPosterService.deleteQRPoster("55487", 1004, "05c3dafc05034b8dadbcd551252d45a6");
        System.out.println("result: "+result);
    }

    @Test
    public void wxAccountTest() {
        String ea = "74164";

        // 渠道二维码信息
        ModelResult<List<QrCodeResult>> modelResult = qrCodeService.queryQrCodeByIds(ea, Arrays.asList(Long.valueOf(572)));

        ModelResult<List<BatchGetByWxAppIdsResult>> modelResult2 = outerServiceWechatService.batchGetByWxAppIds(Arrays.asList("wx4ae350c91df02705"));
    }

    @Test
    public void queryChuangKeTieJsSdkOption(){
        QueryCreateChuangKeTieJsSdkOptionVO vo = new QueryCreateChuangKeTieJsSdkOptionVO();
        vo.setKindId(128);
        vo.setEa("74164");
        Result<QueryCreateChuangKeTieJsSdkOptionResult> result = qrPosterService.queryChuangKeTieJsSdkOption(vo);
        System.out.print("queryChuangKeTieJsSdkOption result:" + result);
    }

    @Test
    public void syncChuangKeTiePoster(){
        String ea = "74164";
        Integer userId = 1000;
        String designId = "ce8593ba-2e65-445f-bb43-f4689eeb291a";
        String url = "http://prod-storage-rainbow-rainbow-oss-cdn.ckt.cn/image_single_page_convert_result/2020/12/03/8995f2d2-19dd-4f8d-a190-5957d2df1533?Expires=1607243050&OSSAccessKeyId=LTAIIwFk1MGs2BL3&Signature=%2BGPgPACFbg%2FSOHUB%2B9AMup1y%2Bao%3D";
        Result<SyncChuangKeTiePosterResult> result = qrPosterService.syncChuangKeTiePoster(ea, userId, designId, url);
        System.out.print("syncChuangKeTiePoster result:"+result);
    }

    @Test
    public void editQRPosterGroup() {
        String ea = "74164";
        int userId = 1177;
        EditObjectGroupArg arg = new EditObjectGroupArg();
        arg.setName("海报分组");
        Result<EditObjectGroupResult> result = qrPosterService.editQRPosterGroup(ea, userId, arg);
        log.info("== editQRPosterGroup result:{}", result);
        arg.setId(result.getData().getId());
        arg.setName("海报分组2");
        result = qrPosterService.editQRPosterGroup(ea, userId, arg);
        log.info("== editQRPosterGroup result:{}", result);
    }

    @Test
    public void deleteQRPosterGroup() {
        String ea = "74164";
        int userId = 1177;

        EditObjectGroupArg editObjectGroupArg = new EditObjectGroupArg();
        editObjectGroupArg.setName("海报模板分组qq");
        Result<EditObjectGroupResult> addResult = qrPosterService.editQRPosterGroup(ea, userId, editObjectGroupArg);
        log.info("== deleteQRPosterGroup result:{}", addResult);
        DeleteObjectGroupArg arg = new DeleteObjectGroupArg();
        arg.setId(addResult.getData().getId());

        Result<Void> result = qrPosterService.deleteQRPosterGroup(ea, userId, arg);
        log.info("== deleteQRPosterGroup result:{}", result);
    }

    @Test
    public void setQRPosterGroup() {
        String ea = "74164";
        int userId = 1177;

        EditObjectGroupArg arg = new EditObjectGroupArg();
        arg.setName("微信哈哈");
        Result<EditObjectGroupResult> result = qrPosterService.editQRPosterGroup(ea, userId, arg);
        log.info("== setQRPosterGroup result:{}", result);

        SetObjectGroupArg setObjectGroupArg = new SetObjectGroupArg();
        setObjectGroupArg.setGroupId(result.getData().getId());
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("0ae02becbdd1486f9f08658c890afbc3");
        objectIdList.add("4621d5ffb99640618eba54f5dccb9c93");
        setObjectGroupArg.setObjectIdList(objectIdList);
        Result<Void> result1 = qrPosterService.setQRPosterGroup(ea, userId, setObjectGroupArg);
        log.info("setQRPosterGroup result: {}", result1);
    }

    @Test
    public void deleteQRPosterBatch() {
        String ea = "74164";
        int userId = 1177;
        DeleteMaterialArg arg = new DeleteMaterialArg();
        List<String> objectIdList = new ArrayList<>();
        objectIdList.add("de075230837f42119cfc4b2d3a79e302");
        objectIdList.add("0ae02becbdd1486f9f08658c890afbc3");
        arg.setIdList(objectIdList);
        Result<Void> result1 = qrPosterService.deleteQRPosterBatch(ea, userId, arg);
        log.info("deleteQRPosterBatch result: {}", result1);
    }

    @Test
    public void topQRPost() {
        String ea = "74164";
        int userId = 1177;
        TopMaterialArg arg = new TopMaterialArg();
        arg.setObjectId("4621d5ffb99640618eba54f5dccb9c93");
        Result<Void> result = qrPosterService.topQRPoster(ea, userId, arg);
        log.info("topQRPost result : {}", result);
    }
}
