/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.marketingEvent.UpdateMarketingEventAnalysisSettingArg;
import com.facishare.marketing.api.arg.marketingEventCommonSetting.UpdateMarketingEventCommonSettingArg;
import com.facishare.marketing.api.result.MemberEnrollMarketingEventDisplayTypeConfigItem;
import com.facishare.marketing.api.service.MarketingEventCommonSettingService;
import com.facishare.marketing.api.vo.marketingevent.MarketingEventAnalysisSettingVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.typehandlers.value.marketingEventCommonSetting.ActivityTypeMapping;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * Created  By zhoux 2021/03/08
 **/
@Slf4j
public class MarketingEventCommonSettingServiceTest extends BaseTest {

    @Autowired
    private MarketingEventCommonSettingService marketingEventCommonSettingService;

    @Test
    public void getMarketingEventCommonSetting() {
        marketingEventCommonSettingService.getMarketingEventCommonSetting(0,"74164");
    }

    @Test
    public void updateMarketingEventCommonSetting() {
        UpdateMarketingEventCommonSettingArg arg = new UpdateMarketingEventCommonSettingArg();
        arg.setEa("74164");
        arg.setFsUserId(1000);
        ActivityTypeMapping.ActivityTypeMappingDetail activityTypeMappingDetail = new ActivityTypeMapping.ActivityTypeMappingDetail();
        activityTypeMappingDetail.setActivityType(0);
        ActivityTypeMapping.MappingDetail mappingDetail = new ActivityTypeMapping.MappingDetail();
        mappingDetail.setApiName("content_marketing");
        ActivityTypeMapping.MappingDetail mappingDetail2 = new ActivityTypeMapping.MappingDetail();
        mappingDetail2.setApiName("3");
        activityTypeMappingDetail.setMapping(Lists.newArrayList(mappingDetail, mappingDetail2));
        ActivityTypeMapping activityTypeMapping = new ActivityTypeMapping();
        activityTypeMapping.add(activityTypeMappingDetail);
        arg.setActivityTypeMapping(activityTypeMapping);
        System.out.println(GsonUtil.toJson(arg));
        // {"activityTypeMapping":[{"activityType":0,"mapping":[{"apiName":"content_marketing"},{"apiName":"3"}]}]}
        marketingEventCommonSettingService.updateMarketingEventCommonSetting(arg);
    }

    @Test
    public void getMarketingEventTypeField() {
        marketingEventCommonSettingService.getMarketingEventTypeField("74164");
    }

    @Test
    public void getSceneTriggerTemplates() {
        marketingEventCommonSettingService.getSceneTriggerTemplates("74164");
    }

    @Test
    public void getSceneHexagonTemplates() {
        marketingEventCommonSettingService.getSceneHexagonTemplates("83668");
    }

    @Test
    public void setDefaultTemplate(){
        marketingEventCommonSettingService.setDefaultTemplate("83668","d67151bd538f4603ab1cfeb6953c53b1");
        getSceneHexagonTemplates();
    }

    @Test
    public void getMemberEnrollMarketingEventDisplayTypeConfig(){
        String ea = "88146";
        Integer fsUserId = 1000;
        Result<List<MemberEnrollMarketingEventDisplayTypeConfigItem>> result = marketingEventCommonSettingService.getMemberEnrollMarketingEventDisplayTypeConfig(ea, fsUserId);
        Assert.assertTrue(result.isSuccess() || CollectionUtils.isNotEmpty(result.getData()));
    }

    @Test
    public void setMemberEnrollMarketingEventDisplayTypeConfig(){
        String ea = "88146";
        Integer fsUserId = 1000;
        List<MemberEnrollMarketingEventDisplayTypeConfigItem> configItemList = Lists.newArrayList();
        MemberEnrollMarketingEventDisplayTypeConfigItem item1 = new MemberEnrollMarketingEventDisplayTypeConfigItem("在线会议", "3");
        MemberEnrollMarketingEventDisplayTypeConfigItem item2 = new MemberEnrollMarketingEventDisplayTypeConfigItem("直播", "live_marketing");
        MemberEnrollMarketingEventDisplayTypeConfigItem item3 = new MemberEnrollMarketingEventDisplayTypeConfigItem("活动营销", "content_marketing");
        MemberEnrollMarketingEventDisplayTypeConfigItem item4 = new MemberEnrollMarketingEventDisplayTypeConfigItem("促销活动", "1");
        configItemList.add(item3);
        configItemList.add(item2);
        configItemList.add(item1);
        configItemList.add(item4);
        marketingEventCommonSettingService.setMemberEnrollMarketingEventDisplayTypeConfig(ea, fsUserId, configItemList);
    }

    @Test
    public void analysisSettingTest() {
        String ea = "83668";
        Result<MarketingEventAnalysisSettingVO> result = marketingEventCommonSettingService.getAnalysisSetting(ea);
        log.info("getAnalysisSetting 结果 ： {}", JsonUtil.toJson(result));

        UpdateMarketingEventAnalysisSettingArg updateArg = new UpdateMarketingEventAnalysisSettingArg();
        updateArg.setEa(ea);
        updateArg.setMqlDefinition(Lists.newArrayList(result.getData().getMqlDefinition().get(0), result.getData().getMqlDefinition().get(1)));
        updateArg.setSqlDefinition(Lists.newArrayList(result.getData().getSqlDefinition().get(0)));
        marketingEventCommonSettingService.updateAnalysisSetting(updateArg);
        updateArg.setMqlDefinition(Lists.newArrayList(result.getData().getMqlDefinition().get(0)));
        updateArg.setEa(ea);
        marketingEventCommonSettingService.updateAnalysisSetting(updateArg);
        result = marketingEventCommonSettingService.getAnalysisSetting(ea);
        log.info("getAnalysisSetting 结果2 ： {}", JsonUtil.toJson(result));
    }
}
