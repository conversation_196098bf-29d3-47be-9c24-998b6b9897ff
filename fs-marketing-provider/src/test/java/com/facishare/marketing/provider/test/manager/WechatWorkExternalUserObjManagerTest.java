/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager;

import com.beust.jcommander.internal.Sets;
import com.facishare.marketing.common.typehandlers.value.TagName;
import com.facishare.marketing.provider.manager.qywx.GroupSendMessageManager;
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager;
import com.facishare.marketing.provider.test.BaseTest;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

public class WechatWorkExternalUserObjManagerTest extends BaseTest {
    @Autowired
    private GroupSendMessageManager groupSendMessageManager;

    @Autowired
    private WechatWorkExternalUserObjManager wechatWorkExternalUserObjManager;

    @Test
    public void test(){
        groupSendMessageManager.handlerSopQywxMsgTaskResult("msgo1z7JgAA_ABl2pJ9Gd53MjRghUhETg","88146");
    }

    @Test
    public void updateUserMarketingTagByExternalUserObjectId() {
        Set<TagName> latestTagNames = Sets.newHashSet();
        TagName tagName = new TagName();
        tagName.setFirstTagName("客户等级");
        tagName.setSecondTagName("一般");
        latestTagNames.add(tagName);
//        wechatWorkExternalUserObjManager.updateUserMarketingTagByExternalUserObjectId("90364", "65dff6d60ce02800018e093d", latestTagNames);

        wechatWorkExternalUserObjManager.appendUserMarketingTagByExternalUserObjectId("90364", "65dff6d60ce02800018e093d", latestTagNames);

//        tagName = new TagName();
//        tagName.setFirstTagName("客户等级");
//        tagName.setSecondTagName("核心");
//        latestTagNames.add(tagName);
//        wechatWorkExternalUserObjManager.updateUserMarketingTagByExternalUserObjectId("90364", "65dff6d60ce02800018e093d", latestTagNames);
//
//        wechatWorkExternalUserObjManager.updateUserMarketingTagByExternalUserObjectId("90364", "65dff6d60ce02800018e093d", Sets.newHashSet());


    }
}
