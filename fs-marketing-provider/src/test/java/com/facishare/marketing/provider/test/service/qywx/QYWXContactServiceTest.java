/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service.qywx;

import com.alibaba.fastjson.JSON;
import com.facishare.marketing.api.arg.CreateFanQrCodeArg;
import com.facishare.marketing.api.arg.objectgroup.ListGroupArg;
import com.facishare.marketing.api.arg.qywx.BindQywxQrCodeWithWebsiteArg;
import com.facishare.marketing.api.arg.qywx.wxContact.*;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeOwnerResult;
import com.facishare.marketing.api.result.qywx.customerGroup.EmployeeTagResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryAddfanQrCodeResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactDetailResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryContactMeConfigResult;
import com.facishare.marketing.api.result.qywx.wxContact.QueryWxUserMarketingUserStatusResult;
import com.facishare.marketing.api.service.qywx.QYWXContactService;
import com.facishare.marketing.api.vo.QueryWebsiteBindFanQrCodeVO;
import com.facishare.marketing.api.vo.qywx.CreateOrUpdateWebSiteFanQrCodeVO;
import com.facishare.marketing.api.vo.qywx.QywxCreateAddFanQrCodeVO;
import com.facishare.marketing.api.vo.qywx.UpdateQywxCustomerRemarkVO;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.qywx.QywxAddFanQrCodeDAO;
import com.facishare.marketing.provider.innerData.qywx.ExternalUserMagCallBackData;
import com.facishare.marketing.provider.manager.qywx.QywxContactManager;
import com.facishare.marketing.provider.manager.qywx.QywxManager;
import com.facishare.marketing.provider.service.qywx.QYWXContactServiceImplSpec;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@Slf4j
public class QYWXContactServiceTest extends BaseTest {

    @Autowired
    private QYWXContactService qywxContactService;

    @Autowired
    private QYWXContactServiceImplSpec qywxContactServiceImplSpec;

    @Autowired
    private QywxAddFanQrCodeDAO qywxAddFanQrCodeDAO;

    @Autowired
    private QywxContactManager qywxContactManager;

    @Autowired
    private QywxManager qywxManager;

    @Test
    public void queryContactList() {
        QueryContactListArg arg = JSON.parseObject("{\n" +
                "    \"name\": \"\",\n" +
                "    \"pageNum\": 1,\n" +
                "    \"pageSize\": 10,\n" +
                "    \"tagNames\": [\n" +
                "        {\n" +
                "            \"firstTagName\": \"客户等级\",\n" +
                "            \"secondTagName\": \"一般\",\n" +
                "            \"nameid\": \"客户等级:一般\",\n" +
                "            \"combineName\": \"客户等级:一般\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"firstTagName\": \"客户等级\",\n" +
                "            \"secondTagName\": \"中等\",\n" +
                "            \"nameid\": \"客户等级:中等\",\n" +
                "            \"combineName\": \"客户等级:中等\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"tagOperator\": \"IN\",\n" +
                "    \"referer\": \"https://crm.ceshi112.com/XV/UI/Home#/app/marketing/index/=/customers/index\",\n" +
                "    \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36\"\n" +
                "}",QueryContactListArg.class);
        arg.setEa("88146");
        arg.setFsUserId(1000);
        arg.setPageNum(1);
        arg.setPageSize(10);
//        FilterData filterData = new FilterData();
//        filterData.setObjectAPIName("WechatWorkExternalUserObj");
//        TagName tagName1 = new TagName();
//        tagName1.setFirstTagName("帅0122422233");
//        tagName1.setSecondTagName("帅");

    //    TagName tagName = new TagName();
     //   tagName.setFirstTagName("营销标签");
    //    tagName.setSecondTagName("注入数据913");
     //   searchTemplateQuery.setTagNames(Lists.newArrayList(tagName));
//        arg.setFilterData(filterData);
//        arg.setTagNames(Lists.newArrayList(tagName1));
//        arg.setTagOperator("IN");
        /*arg.setQyUserId("YiGeRenDeHuaLuo");
        arg.setUid("622fee9bcdf84e2f9cbc71b78552e692");
        arg.setName("刘益云");*/
        qywxContactService.queryContactList(arg);
    }

    @Test
    public void queryContactDetail() {
        AssociateContactArg queryContactDetailArg = new AssociateContactArg();
        queryContactDetailArg.setEa("76301");
        queryContactDetailArg.setFsUserId(1000);
        queryContactDetailArg.setId("5e43b7dd1a483c000193a7f0");
        qywxContactService.associateContact(queryContactDetailArg);
    }

    @Test
    public void setContactConfigByEmployee(){
        String uid = "622fee9bcdf84e2f9cbc71b78552e692";
        String ea = "74164";
        Integer fsUserId = 1032;
        qywxContactService.setContactConfigByEmployee(uid, ea, fsUserId);
    }

    @Test
    public void queryWxUserMarketingUserStatus(){
        String ea = "74164";
        String uid = "7a301850c6d748b7a3a0771744175626";
        String qywxUserId = null;
        Result<QueryWxUserMarketingUserStatusResult> result = qywxContactService.queryWxUserMarketingUserStatus(ea, uid, qywxUserId);
        System.out.print("queryWxUserMarketingUserStatus result:" + result);
    }

    @Test
    public void queryContactMeConfigId() {
        String uid = "7a301850c6d748b7a3a0771744175626";
        Result<QueryContactMeConfigResult> result = qywxContactService.queryContactMeConfig(uid, null, null, null);
    }

    @Test
    public void queryActionAndFollowUp() {
        QueryActionAndFollowUpArg arg = new QueryActionAndFollowUpArg();
        arg.setCrmContactId("5e60b3827176cf00016169f2");
        arg.setUserMarketingId("6e38bb058cad476bb18d9722d2cd3fe6");
        arg.setFsEa("74164");
        qywxContactService.queryActionAndFollowUp(arg);
    }

    @Test
    public void checkMyCustomer(){
        String uid = "a9e2fdbb925744a7bd1f12ac4c1397b8";
        String fsUserUid = "121100d6a2414893af8a77bd2b33be14";
        Result<Boolean> result = qywxContactService.checkMyCustomer(uid, fsUserUid);
    }

    @Test
    public void getQywxObjectIdByQywxExternUserId(){
        String ea = "74164";
        String qywxExternUserId = "wmjbhFCwAAwyy752UVX3isnVp2TDFPHw";
        Result<String> result = qywxContactService.getQywxObjectIdByQywxExternUserId(ea, qywxExternUserId);
        System.out.print("getQywxObjectIdByQywxExternUserId result:"+ result);
    }

    @Test
    public void createAddfanQrCode(){
        QywxCreateAddFanQrCodeVO vo = new QywxCreateAddFanQrCodeVO();
        vo.setEa("74164");
        vo.setCreateUserId(1000);
        vo.setQrCodeName("吸粉二维码sln");
        vo.setType(2);
        List<String> userId = Lists.newArrayList();
        userId.add("JuBeiSongLiuNian");
        vo.setUserId(userId);
        vo.setSkipVerify(1);
        vo.setMarketingWebsiteUserId(UUIDUtil.get24UUID());
        String str = "{\n" +
                "\t\"qrCodeName\": \"测试添加部门粉丝二维码\",\n" +
                "\t\"type\": 2,\n" +
                "\t\"departmentIds\": [\"3\"],\n" +
                "\t\"skipVerify\": 0,\n" +
                "\t\"channelDesc\": \"\",\n" +
                "\t\"remark\": \"\",\n" +
                "\t\"tag\": [],\n" +
                "\t\"ea\": \"74164\",\n" +
                "\t\"welcomeContent\": \"hi~\",\n" +
                "\t\"welcomeImagePath\": null,\n" +
                "\t\"welcomeImageTitle\": null,\n" +
                "\t\"welcomeMiniprogramTitle\": null,\n" +
                "\t\"welcomeMiniprogramMediaPath\": null,\n" +
                "\t\"welcomeMinigramPage\": null,\n" +
                "\t\"channelValue\": \"cfc15535dac541cf9d90eb7303eb35c1\",\n" +
                "    \"marketingWebsiteUserId\": \"6475601c61204c10879d743201810b69\"\n" +
                "}";

        QywxCreateAddFanQrCodeVO qywxCreateAddFanQrCodeVO = GsonUtil.fromJson(str, QywxCreateAddFanQrCodeVO.class);

        Result<String> result = qywxContactService.createAddfanQrCode(qywxCreateAddFanQrCodeVO);
        System.out.print("createAddfanQrCode result:"+result);
    }

    @Test
    public void customizeCreateFanQrCode() {
        CreateFanQrCodeArg arg = new CreateFanQrCodeArg();
        arg.setQrCodeName("测试添加单个活码");
        arg.setUserIds(Lists.newArrayList(1000));
        arg.setSkipVerify(0);
        arg.setWelcomeContent("测试添加单个活码");
        arg.setBindApiName("AccountObj");
        arg.setBindObjectId("65dffcb4bada2400013f8032");
        Result<String> qrCord = qywxContactService.customizeCreateFanQrCode(88146, 1000, arg);
        System.out.print("customizeCreateFanQrCode qrCord:" + qrCord);
        arg.setQrCodeName("测试添加单个活码111");
        qrCord = qywxContactService.customizeCreateFanQrCode(88146, 1000, arg);
        System.out.print("customizeCreateFanQrCode qrCord:" + qrCord);
    }

    @Test
    public void updateAddfanQrCode(){
        String id = "f7c7f4af416440d4955551f457b36a0c";
        QywxCreateAddFanQrCodeVO vo = new QywxCreateAddFanQrCodeVO();
        vo.setId(id);
        vo.setEa("74164");
        vo.setCreateUserId(1000);
        vo.setQrCodeName("吸粉二维码sln");
        vo.setType(2);
        List<String> userId = Lists.newArrayList();
        userId.add("JuBeiSongLiuNian");
        vo.setUserId(userId);
        vo.setSkipVerify(1);
        Result<String> result = qywxContactService.updateAddfanQrCode(vo);
        System.out.print("updateAddfanQrCode result:"+result);
    }

    @Test
    public void queryAddfanQrCode(){
        String id = "e15f6bd0838645128b37f5d931173557";
        Result<QueryAddfanQrCodeResult> result = qywxContactService.queryAddfanQrCode(id);
        System.out.print("queryAddfanQrCode result:"+result);
    }

    @Test
    public void queryAddfanQrCodeList(){
        String ea = "88146";
        String keyword = null;
        Integer pageNum = 1;
        Integer pageSize = 20;
        Result<PageResult<QueryAddfanQrCodeResult>> result = qywxContactService.queryAddfanQrCodeList(ea, keyword, pageNum, pageSize,1030,"aac329e0d45d44fd96cd579097611d8c","",1);
        System.out.print("queryAddfanQrCodeList result:"+ result);
    }

    @Test
    public void updateQywxCustomerRemark(){
        UpdateQywxCustomerRemarkVO vo = new UpdateQywxCustomerRemarkVO();
        vo.setFsUserId(1089);
        vo.setEa("74164");
        vo.setPhone("15625053121");
        vo.setDescription("测试");
        vo.setCompanyName("纷享销客");
        vo.setName("hayley");
        vo.setUserId("bigbear");
        Result<Boolean> result = qywxContactService.updateQywxCustomerRemark(vo);
    }

    @Test
    public void queryContactDetail1(){
        QueryContactDetailArg arg = new QueryContactDetailArg();
        arg.setId("624fa31a2b75db00013ea1a1");
        arg.setFsEa("74164");
        Result<QueryContactDetailResult> result = qywxContactService.queryContactDetail(arg);
        System.out.print("queryContactDetail1 result:"+result);
    }
    @Test
    public void queryCrmUserIdByExternalUserid(){
        QueryCrmUserIdArg arg = new QueryCrmUserIdArg();
        arg.setExtendUserId("wmjbhFCwAAj_TdzA04VLK0t5z88bRBHQ");
        arg.setFsEa("74164");
        Result<List<String>> listResult = qywxContactService.queryCrmUserIdByExternalUserid(arg);
        System.out.print("queryContactDetail1 result:"+listResult.getData());
    }

    @Test
    public void createOrUpdateWebsiteFanQrCodeTest() {
        CreateOrUpdateWebSiteFanQrCodeVO vo = new CreateOrUpdateWebSiteFanQrCodeVO();
        vo.setFanQrCodeId("11ebd808392044209e50ef4cc7a12f10");
        vo.setMarketingWebsiteUserId("2af231a629ec4eb9932b224fc2f50f56");
        Result<QueryAddfanQrCodeResult> res = qywxContactService.createOrUpdateWebsiteFanQrCode(vo);
        System.out.println(res);
    }

    @Test
    public void queryWebsiteBindFanQrCodeTest() {
        QueryWebsiteBindFanQrCodeVO vo = new QueryWebsiteBindFanQrCodeVO();
        vo.setEa("83668");
        Result<List<QueryAddfanQrCodeResult>> listResult = qywxContactService.queryWebsiteBindFanQrCode(vo);
        System.out.println(listResult);
    }

    @Test
    public void bindQywxQrCodeWithWebsite() {
        String ea = "74164";
        Integer fsUserId = 1071;
        BindQywxQrCodeWithWebsiteArg arg = new BindQywxQrCodeWithWebsiteArg();
        arg.setQywxQrCodeId("430c7df7d5414d6a8b18b6f00145086d");
        Result<String> result = qywxContactService.bindQywxQrCodeWithWebsite(ea, fsUserId, arg);
        System.out.println(result);
    }

    @Test
    public void websiteBindFanQrCodeTest() {
        int res = qywxAddFanQrCodeDAO.updateFanQrCodeWebsiteBindStatus("ee201f75cc6a4b288fbbf3264eb1dae6", 0, 1135, "'王振懿Justice'");
        System.out.println(res);
    }

    @Test
    public void recvNewCustomerCallBackTest() {
        ExternalUserMagCallBackData data = GsonUtil.fromJson("{\"ea\":\"74164\",\"toUserName\":\"wwa546502834a79a0a\",\"fromUserName\":\"sys\",\"createTime\":1647850192,\"msgType\":\"event\",\"event\":\"change_external_contact\",\"changeType\":\"add_external_contact\",\"userID\":\"JuBeiSongLiuNian\",\"externalUserID\":\"wmjbhFCwAAFvICoP9pa_kqRcWlBHUmyQ\",\"state\":\"99ec8479f99d0d42880e9ed2\",\"welcomeCode\":\"y5vomxwIoH_vIH1oW1oHTbI8jM4vFWcM_gw_5YsSEDU\"}", ExternalUserMagCallBackData.class);
//        qywxContactManager.recvNewCustomerCallBack(data);
    }

    @Test
    public void deleteTemplateWebFanQrCodeByDays(){
        qywxContactService.deleteTemplateWebFanQrCodeByDays(new Date());
    }

    @Test
    public void queryFanCode(){
        ListGroupArg arg = new ListGroupArg();
        arg.setUseType(0);
        qywxContactService.listFanCodeGroup("74164",1142,arg);
    }

    @Test
    public void queryTags(){
        Result<List<EmployeeTagResult>> listResult = qywxContactService.queryQywxTagInfo("74164");
        System.out.println(listResult);
    }

    @Test
    public void queryUserIdsByTags(){
        List<String> strings = qywxManager.batchGetEmployeeByTags("74164", Lists.newArrayList(1, 2, 3));
        System.out.println(strings);
    }

    @Test
    public void queryQywxEmployeeBaseInfo(){
        Result<List<EmployeeOwnerResult>> info = qywxContactService.queryQywxEmployeeBaseInfo("88146", 1, 1000);
        System.out.println(info);
    }

}

