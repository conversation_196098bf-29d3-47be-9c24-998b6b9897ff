package com.facishare.marketing.provider.test.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.expcetion.ServiceException;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.sdr.SdrActionArg;
import com.facishare.marketing.provider.manager.sdr.SdrActionManager;
import com.facishare.marketing.provider.manager.sdr.SdrSalesRecordManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.marketing.statistic.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

@Slf4j
public class SdrActionServiceTest extends BaseTest {

    @Autowired
    private EIEAConverter converter;
    @Autowired
    private SdrActionManager sdrActionManager;
    @Autowired
    private SdrSalesRecordManager sdrSalesRecordManager;

    @Test
    public void profileInsightOverview() throws ServiceException {
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = sdrActionManager.marketingInsightsDeepOptimized("88146", "6848e05f16549e0007f5c8e1", "6848e0e616549e0007f5e2f4");
        log.info("耗时:{}, 结果:{}", System.currentTimeMillis() - startTime, JsonUtil.toJson(result));
    }

    @Test
    public void scoreModelResult() throws ServiceException {
        List<Map<String, Object>> result = sdrActionManager.scoreModelResult("88146", "681c201b8462de00016b504a", "681c20275f1df60001ccd071");
        log.info("结果:{}", JsonUtil.toJson(result));
    }

    @Test
    public void batchQuerySDRRelatedScripts() throws ServiceException {
        String argStr = "{\"tenantId\":88146,\"fsUserId\":-10000,\"communicationActions\":\"\\\"question_answer,demand_digging\\\"\",\"intentionTypes\":\"\\\"purchase_process_progress\\\"\",\"modelId\":\"6821bacf55bac9000115a6c1\"}";
        SdrActionArg arg = JSONObject.parseObject(argStr, SdrActionArg.class);
        String s = sdrActionManager.batchQuerySDRRelatedScripts("88146", arg.getCommunicationActions(), arg.getIntentionTypes(), arg.getModelId());
        log.info("结果:{}", JsonUtil.toJson(s));
    }

    @Test
    public void salesRecordGeneration() {
        sdrSalesRecordManager.salesRecordGeneration("88146", "[2025051317386 2025-05-13 11:57:18] 你好，怎么报价", "6822c31f5f1df600018a9dc5");
    }

}
