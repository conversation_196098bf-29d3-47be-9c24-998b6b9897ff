package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.api.UpdateSignInSuccessSettingArg;
import com.facishare.marketing.api.result.ExportEnrollsDataResult;
import com.facishare.marketing.api.result.conference.GetSignInSettingResult;
import com.facishare.marketing.api.result.conference.GetSimpleConferenceDetail;
import com.facishare.marketing.api.vo.conference.QueryConferenceParticipantsVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.provider.manager.ActivityManager;
import com.facishare.marketing.provider.manager.conference.ConferenceManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.google.common.collect.Lists;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created by ranluch on 2019/8/5.
 */
@Slf4j
public class ConferenceManagerTest extends BaseTest {
    @Autowired
    private ConferenceManager conferenceManager;

    @Autowired
    private ActivityManager activityManager;

    @Test
    public void sendNotificationByEnrollIds(){
        conferenceManager.sendNotificationByEnrollIds("5dce61b90b834c9089915f93d2b0a79d", Lists.newArrayList("1ef25a9b4eea42c088220ec626ec00a6"), 1, null);
    }


    @Test
    public void getConferenceDetailsByPath() {
        System.out.println(conferenceManager.getConferenceDetailsByPath("A_201908_09_4fec3ac4a4b24ca9894baf13211e81d1.html"));
    }

    @Test
    public void sendReviewMessage(){
        String old = "{\"id\":\"3b6ae5d9638b4565a81741c3c8c5d2b3\",\"type\":\"page\",\"name\":\"{直播标题}报名预约\",\"title\":\"\",\"version\":\"1.0.0\",\"cover\":\"\",\"shareOpts\":{\"title\":\"{直播标题}\",\"desc\":\"{直播简介}\",\"link\":\"\",\"imgUrl\":\"https://www.fxiaoke.com/fssharehelper/file/getFileBySpliceUrl?path=A_202004_15_bc0eb8e2f5574982a0f5baa375343590.png\"},\"style\":{\"width\":375,\"backgroundColor\":\"#fff\",\"backgroundSize\":\"100%\",\"backgroundRepeat\":\"no-repeat\",\"backgroundImage\":\"\"},\"backgroundFillType\":\"filling\",\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://www.fxiaoke.com/fssharehelper/file/getFileBySpliceUrl?path=A_202004_15_13159da2e39743eeb5846705074ce2bd.png\",\"action\":{}}],\"imageGap\":4,\"style\":{\"display\":\"flex\",\"width\":375,\"height\":197,\"paddingBottom\":0,\"paddingLeft\":0,\"paddingRight\":0,\"paddingTop\":0,\"borderRadius\":0,\"background\":\"rgba(255, 255, 255, 0)\",\"backgroundRepeat\":\"no-repeat\",\"backgroundSize\":\"cover\",\"backgroundPosition\":\"center center\",\"borderWidth\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"sort\":0,\"id\":1586854409546,\"components\":[]},{\"id\":1586854484341,\"name\":\"文本\",\"type\":\"text\",\"value\":\"<p><span style=\\\"font-size: 14px;\\\">{直播标题}</span></p>\",\"style\":{\"paddingBottom\":6,\"paddingLeft\":12,\"paddingRight\":12,\"paddingTop\":6,\"background\":\"rgba(255, 255, 255, 0)\",\"fontSize\":14,\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"sort\":1,\"components\":[]},{\"id\":1586854505011,\"name\":\"文本\",\"type\":\"text\",\"value\":\"<p><span style=\\\"font-size: 14px;\\\">{直播开始时间}</span></p>\",\"style\":{\"paddingBottom\":6,\"paddingLeft\":12,\"paddingRight\":12,\"paddingTop\":0,\"background\":\"rgba(255, 255, 255, 0)\",\"fontSize\":14,\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"sort\":2,\"components\":[]},{\"id\":1586854587677,\"name\":\"辅助留白\",\"type\":\"blank\",\"style\":{\"width\":375,\"height\":10,\"background\":\"rgba(233, 237, 245, 1)\",\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"sort\":3,\"components\":[]},{\"id\":1586854599692,\"name\":\"自定义布局\",\"type\":\"container\",\"components\":[{\"id\":1586854602526,\"name\":\"文本\",\"type\":\"text\",\"value\":\"<p><span style=\\\"font-size: 14px; font-family: &quot;Microsoft YaHei&quot;, &quot;Hiragino Sans GB&quot;, STHeiti; line-height: 24px; color: rgb(145, 149, 158); letter-spacing: 0px;\\\">详情</span></p>\",\"style\":{\"paddingBottom\":6,\"paddingLeft\":0,\"paddingRight\":0,\"paddingTop\":6,\"background\":\"rgba(255, 255, 255, 0)\",\"fontSize\":14,\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\",\"left\":12,\"top\":11,\"position\":\"absolute\",\"width\":95},\"sort\":\"0\"},{\"id\":1586945492901,\"name\":\"文本\",\"type\":\"text\",\"value\":\"<p><span style=\\\"font-size: 14px;\\\">{直播简介}</span></p>\",\"style\":{\"paddingBottom\":6,\"paddingLeft\":0,\"paddingRight\":0,\"paddingTop\":6,\"background\":\"rgba(255, 255, 255, 0)\",\"fontSize\":14,\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\",\"left\":12,\"top\":50,\"position\":\"absolute\",\"width\":95},\"sort\":\"1\"}],\"style\":{\"width\":375,\"height\":500,\"x\":479,\"y\":153,\"overflow\":\"hidden\",\"position\":\"relative\"},\"sort\":4,\"key\":\"auto-container\",\"typeValue\":\"auto\"},{\"id\":1605145799308,\"name\":\"辅助留白\",\"type\":\"blank\",\"style\":{\"width\":375,\"height\":50,\"background\":\"rgba(255, 255, 255, 0)\",\"borderWidth\":0,\"borderRadius\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"sort\":5,\"components\":[]},{\"id\":1604548214212,\"label\":\"直播预约按钮\",\"name\":\"直播预约\",\"tip\":\"提交成功\",\"type\":\"livebutton\",\"position\":\"fixed-bottom\",\"required\":false,\"isFormComp\":false,\"wrapStyle\":{\"position\":\"fixed\",\"left\":0,\"bottom\":0,\"right\":0,\"background\":\"rgba(255,255,255,.9)\",\"zIndex\":1,\"padding\":\"6px 0\"},\"style\":{\"height\":45,\"width\":345,\"fontSize\":16,\"background\":\"#409EFF\",\"borderRadius\":0,\"color\":\"#fff\",\"letterSpacing\":0,\"lineHeight\":45,\"textAlign\":\"center\",\"margin\":\"0 auto\",\"borderWidth\":0,\"borderStyle\":\"none\",\"borderColor\":\"#e9edf5\"},\"memberAutoSignup\":true,\"liveId\":\"{liveId}\",\"marketingEventId\":\"{marketingEventId}\",\"liveTitle\":\"{直播标题}\",\"liveStatus\":\"before\",\"live\":{\"before\":{\"yes\":{\"name\":\"已预约\",\"style\":{},\"action\":{}},\"no\":{\"name\":\"立即预约\",\"style\":{},\"action\":{\"type\":\"inside\",\"id\":\"{SignupPageId}\",\"url\":\"\",\"miniprogram\":{\"wechat\":{\"appId\":\"\",\"originalId\":\"\",\"path\":\"\"},\"baidu\":{\"appId\":\"\",\"path\":\"\"}},\"content\":{},\"name\":\"报名预约\"}}},\"processing\":{\"yes\":{\"name\":\"观看直播\",\"style\":{},\"action\":{\"type\":\"outside\",\"url\":\"{liveUrl}\"}},\"no\":{\"name\":\"报名并观看\",\"style\":{},\"action\":{\"type\":\"inside\",\"id\":\"{SignupPageId}\",\"url\":\"\",\"miniprogram\":{\"wechat\":{\"appId\":\"\",\"originalId\":\"\",\"path\":\"\"},\"baidu\":{\"appId\":\"\",\"path\":\"\"}},\"content\":{},\"name\":\"报名预约\"}}},\"after\":{\"yes\":{\"name\":\"观看回放\",\"style\":{},\"action\":{\"type\":\"outside\",\"url\":\"{liveUrl}\"}},\"no\":{\"name\":\"观看回放\",\"style\":{},\"action\":{\"type\":\"memberPage\",\"id\":\"{registerSiteId}\",\"url\":\"\",\"miniprogram\":{\"wechat\":{\"appId\":\"\",\"originalId\":\"\",\"path\":\"\"},\"baidu\":{\"appId\":\"\",\"path\":\"\"}},\"content\":{},\"name\":\"会员注册页\"}}}},\"sort\":6,\"components\":[]}],\"isHomepage\":2}";
        String ea = "2";
        Integer userId =2990;
        String conferenceId = "45a7bf30cc774ede84f7c95e671ed741";
        List<Integer> toUserIds = Lists.newArrayList();
        toUserIds.add(2990);
        boolean ret = conferenceManager.sendReviewMessage(conferenceId);
        Assert.assertEquals(ret, true);
    }

    @Test
    public void sendConferenceEnrollNoticeTask(){
        conferenceManager.sendConferenceEnrollNoticeTask();
    }

    @Test
    public void updateInviteStatusAfterInviting(){
        String id = "8f8b21aa1868462f83f60bb2a4d12acd";
        conferenceManager.updateInviteStatusAfterInviting(id);
    }

    @Test
    public void checkViewMarketingEvenObjectAuth() {
        activityManager.checkViewMarketingEvenObjectAuth("74164", 1070, "5df1df5964f84600011ef53e");
    }

    @Test
    public void a(){
        String a = "nieh{registerSiteId}xxxx{registerSiteId}yyy{loginSiteId}zzz{loginSiteId}ttt";
        String registerSiteIdReg = "\\{" + "registerSiteId" + "}";
        String loginSiteIdReg = "\\{" + "loginSiteId" + "}";
        a=a.replaceAll(registerSiteIdReg, "hello");
        a=a.replaceAll(loginSiteIdReg, "world");
        System.out.print("a:"+ a);

    }

    @Test
    public void createDefaultConferenceSite() {
        conferenceManager.createDefaultConferenceSite("496918bcc4a54b32a2d9ef1e0d19be27");
    }

    @Test
    public void resetConferenceIndexPage() {
        //conferenceManager.resetConferenceIndexPage("911083e8dd894549a805771d551f2c07");
    }

    @Test
    public void  buildExportParticipantsData(){
        QueryConferenceParticipantsVO vo = new QueryConferenceParticipantsVO();
        vo.setConferenceId("bb42caae65684919b97da97897ec8d87");
        vo.setEa("88146");
        ExportEnrollsDataResult result = conferenceManager.buildExportParticipantsData(vo);
        log.info("buildExportParticipantsData  result:{}", result);
    }

    @Test
    public void getSignInSetting1(){
        String conferenceId = "e4bc7705718f4d7998094dc6bc37ad7d";
        Result<GetSignInSettingResult>  result = conferenceManager.getSignInSetting(conferenceId);
        Assert.assertEquals(result.isSuccess(), true);
        Assert.assertEquals(result.getData().getJumpObjectId(), conferenceId);
    }

    @Test
    public void getSignInSetting2(){
        String conferenceId = "not found";
        Result<GetSignInSettingResult>  result = conferenceManager.getSignInSetting(conferenceId);
        Assert.assertEquals(result.isSuccess(), false);
        Assert.assertEquals(result.getErrCode(), SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode());
    }

    @Test
    public void getSignInSetting3(){
        String conferenceId = "e73c28cfaa6249a981cc07dd70c87c70";
        Result<GetSignInSettingResult>  result = conferenceManager.getSignInSetting(conferenceId);
        Assert.assertEquals(result.isSuccess(), true);
        Assert.assertFalse(result.getData().getJumpObjectId().equals(conferenceId));
    }

    @Test
    public void getSignInSuccessSetting(){
        String conferenceId = "e73c28cfaa6249a981cc07dd70c87c70";
        Result<UpdateSignInSuccessSettingArg> result = conferenceManager.getSignInSuccessSetting(conferenceId);
        Assert.assertEquals(result.isSuccess(), true);
        Assert.assertEquals(result.getData().getConferenceId(), conferenceId);
    }

    @Test
    public void getSimpleDetail1(){
        String conferenceId = "e73c28cfaa6249a981cc07dd70c87c70";
        Result<GetSimpleConferenceDetail> result = conferenceManager.getSimpleDetail(conferenceId);
        Assert.assertEquals(result.isSuccess(), true);
    }
    @Test
    public void getSimpleDetail2(){
        String conferenceId = "not found";
        Result<GetSimpleConferenceDetail> result = conferenceManager.getSimpleDetail(conferenceId);
        Assert.assertEquals(result.isSuccess(), false);
        Assert.assertEquals(result.getErrCode(), SHErrorCode.ACTIVITY_NOT_EXIST.getErrorCode());
    }
}
