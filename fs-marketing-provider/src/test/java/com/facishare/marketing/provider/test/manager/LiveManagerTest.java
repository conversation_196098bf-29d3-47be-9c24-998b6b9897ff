package com.facishare.marketing.provider.test.manager;

import com.facishare.marketing.common.enums.live.LiveUserActionTypeEnum;
import com.facishare.marketing.provider.dao.live.MarketingLiveStatisticsDAO;
import com.facishare.marketing.provider.entity.live.MarketingLiveStatistics;
import com.facishare.marketing.provider.manager.LiveManager;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.training.common.result.Result;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created by zhengh on 2020/12/24.
 */
public class LiveManagerTest extends BaseTest {
    @Autowired
    private LiveManager liveManager;
    @Autowired
    private MarketingLiveStatisticsDAO marketingLiveStatisticsDAO;

    @Test
    public void syncLiveStatisticsData(){
        liveManager.syncVhallLiveStatisticsData();
    }

    @Test
    public void upsertUserStatus() {
        liveManager.upsertUserStatus(123, Lists.newArrayList("e1","e2", "e3", "e4", "e5"), LiveUserActionTypeEnum.REPLAY);
    }

    @Test
    public void queryLiveUserByStatus() {
        liveManager.queryLiveUserByStatus(123, LiveUserActionTypeEnum.REPLAY);
    }
    @Test
    public void scheduleSetDefaultRecord(){
        liveManager.scheduleSetDefaultRecord();
    }

    @Test
    public void syncXiaoetongLiveStatisticsData(){
        liveManager.syncXiaoetongLiveStatisticsData();
    }
}
