/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.service;

import com.facishare.marketing.api.arg.GetNoticeDetailArg;
import com.facishare.marketing.api.arg.ListNoticeArg;
import com.facishare.marketing.api.arg.NoticeSendArg;
import com.facishare.marketing.api.result.NoticeDetailResult;
import com.facishare.marketing.api.result.NoticeResult;
import com.facishare.marketing.api.service.NoticeService;
import com.facishare.marketing.common.enums.NoticeContentTypeEnum;
import com.facishare.marketing.common.enums.NoticeSendTypeEnum;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.provider.test.BaseTest;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Date;

@Slf4j

public class NoticeServiceTest extends BaseTest {
    private final static String ea = "83668";
    private final static Integer fsUserId = 1000;
    @Autowired
    private NoticeService noticeService;

    @Test
    public void sendNotice() {
        NoticeSendArg vo = new NoticeSendArg();
        vo.setTitle("Ceshi");
        vo.setDescription("描发生的发生述");
        vo.setStartTime(new Date().getTime());
        vo.setEndTime(new Date().getTime());
        vo.setSendType(NoticeSendTypeEnum.NORMAL.getType());
        vo.setContentType(NoticeContentTypeEnum.ACTIVITY.getType());
        vo.setContent("4a2620a264814bb48a05adc61af4fd52");
        vo.setMarketingActivityId("62f1dafa69e67a0001a66a45");
        vo.setMarketingEventId("62edbf97fec3e30001ddaa01");
        vo.setAddressBookType(1);
        NoticeSendArg.NoticeVisibilityVO noticeVisibilityVO = new NoticeSendArg.NoticeVisibilityVO();
        noticeVisibilityVO.setUserIds(Arrays.asList(100000007));
        vo.setNoticeVisibilityVO(noticeVisibilityVO);
        Result result = noticeService.sendNotice(ea, fsUserId, vo);
    }

    @Test
    public void sendNoticeById() {
        String noticeId = "e81031ef177a48538d6279bd81c25ba2";
        String marketingActivityId = "66ac5c49676ca400013c529e";
        String marketingEventId = "66ac552dd83cb800075c037b";
        Result<Void> result = noticeService.sendNoticeById(noticeId, marketingActivityId,marketingEventId);
        System.out.println(result);
    }

    @Test
    public void listNotices() {
        ListNoticeArg vo = new ListNoticeArg();
        vo.setPageNum(1);
        vo.setPageSize(40);
        vo.setSpreadType(13);
        Result<PageResult<NoticeResult>> result = noticeService.listNotices("88146", 1000, vo);
        log.info("listNotices:{}", result);
    }

    @Test
    public void sendNoticeInvite() {
        Result result = noticeService.sendNoticeInvite(ea, fsUserId);
    }

    @Test
    public void sendfsUserIdNoticeInvite() {
        Integer sendFsUserId = 1000;
        Result result = noticeService.sendfsUserIdNoticeInvite(ea, fsUserId, sendFsUserId);
    }

    @Test
    public void getDetailTest() {
        String noticeId = "43cc02f74591474dab98ccda674572e3";
        GetNoticeDetailArg vo = new GetNoticeDetailArg();
        vo.setNoticeId(noticeId);
        Result<NoticeDetailResult> result = noticeService.getDetail(vo);
        NoticeDetailResult data = result.getData();
    }

    @Test
    public void partnerNoticeAgain() throws UnsupportedEncodingException {
        noticeService.partnerNoticeAgain("88146", 1000, "12434de34471400388f239ffbff9e97e", "677dee67f45a1900016575ed");
    }

    @Test public void listPartnerNoticesTest() {
        ListNoticeArg arg = new ListNoticeArg();
        arg.setPageNum(1);
        arg.setPageSize(1);
        arg.setTitle("伙伴推广专用产品12301");
        Result<PageResult<NoticeResult>> result = noticeService.listPartnerNotices("88146", 1000, arg);
        log.info("結果: {}", JsonUtil.toJson(result));
    }
}
