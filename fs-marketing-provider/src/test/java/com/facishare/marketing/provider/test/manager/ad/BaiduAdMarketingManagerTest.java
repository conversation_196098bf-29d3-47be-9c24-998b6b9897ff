/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.provider.test.manager.ad;

import com.facishare.marketing.api.result.baidu.GetDataOverviewResult;
import com.facishare.marketing.api.vo.baidu.GetDataOverviewVO;
import com.facishare.marketing.common.enums.advertiser.headlines.TypeEnum;
import com.facishare.marketing.common.enums.baidu.AdSourceEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.util.DateUtil;
import com.facishare.marketing.provider.entity.baidu.AdAccountEntity;
import com.facishare.marketing.provider.manager.RedisManager;
import com.facishare.marketing.provider.manager.advertiser.AdAccountManager;
import com.facishare.marketing.provider.manager.baidu.BaiduAdMarketingManager;
import com.facishare.marketing.provider.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
public class BaiduAdMarketingManagerTest extends BaseTest {


    @Autowired
    private BaiduAdMarketingManager baiduAdMarketingManager;

    @Autowired
    private RedisManager redisManager;

    @Autowired
    private AdAccountManager adAccountManager;


    @Test
    public void refreshBaiduAllDataTest() {
        String ea = "88146";
        baiduAdMarketingManager.refreshAllData(ea, "db0661b1ea2d4b528099ac6bfd7d2425", "百度");
    }

    @Test
    public void refreshMarketingEventOOCPCLaunchByEa() {
        String ea = "88146";
        baiduAdMarketingManager.refreshMarketingEventOOCPCLaunchByEa(ea);
    }

    @Test
    public void refreshMarketingEventOOCPCLaunchByAccountId() {
        String ea = "88146";
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById("8b524863784b46f9a46b3b7f6557qqq");
        baiduAdMarketingManager.refreshMarketingEventOOCPCLaunchByAccountId(ea, adAccountEntity);
    }

    @Test
    public void refreshCampaignDataTest() {
        String ea = "88146";
        String token = "eyJhbGciOiJIUzM4NCJ9.eyJzdWIiOiJhY2MiLCJhdWQiOiLnurfkuqvokKXplIDpgJoiLCJ1aWQiOjQxNzg1MTE3LCJhcHBJZCI6IjllNzY4OGZkMjg5ZGVkZGM2YzQwMzFmYTJlOTVmNDNjIiwiaXNzIjoi5ZWG5Lia5byA5Y-R6ICF5Lit5b-DIiwicGxhdGZvcm1JZCI6IjQ5NjAzNDU5NjU5NTg1NjE3OTQiLCJleHAiOjE3MTIxNzYxMjYsImp0aSI6IjgyMjQ4ODk0NDczMjI1MDExMjcifQ.gnEkzBJmc7lf8238ZdbLDrNal7lCwfyRIRSZiWC3QRCQSl63XTuwaZXpSEW2f0iG";
        redisManager.setAdvertiseAccessToken(ea, String.valueOf(********), AdSourceEnum.SOURCE_BAIDU.getValue(), token);
        AdAccountEntity adAccountEntity = adAccountManager.queryAccountById("8b524863784b46f9a46b3b7f6557qqq");
        baiduAdMarketingManager.refreshCampaignData(adAccountEntity, 3);
    }

    @Test
    public void refreshProjectFeedTest() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("test_id");
        adAccountEntity.setUsername("bj合思信息");
        baiduAdMarketingManager.refreshProjectFeed(adAccountEntity);
    }

    @Test
    public void syncProjectFeedToMarketingEventObjTest() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        baiduAdMarketingManager.syncProjectFeedToMarketingEventObj(adAccountEntity);
    }

    @Test
    public void refreshCampaignFeedTest() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        baiduAdMarketingManager.refreshCampaignFeed(adAccountEntity);
    }

    @Test
    public void syncCampaignFeedToMarketingEventObj() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        adAccountEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        baiduAdMarketingManager.syncCampaignFeedToMarketingEventObj(adAccountEntity);
    }

    @Test
    public void refreshAdGroupFeedTest() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("test_id");
        adAccountEntity.setUsername("bj合思信息");
        baiduAdMarketingManager.refreshAdGroupFeed(adAccountEntity);
    }

    @Test
    public void syncAdGroupFeedToMarketingEventObj() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        adAccountEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        baiduAdMarketingManager.syncAdGroupFeedToMarketingEventObj(adAccountEntity);
    }

    @Test
    public void syncAdGroupToMarketingEventObjTest() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        adAccountEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        baiduAdMarketingManager.syncAdGroupToMarketingEventObj(adAccountEntity);
    }

    @Test
    public void refreshAdGroupData() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        adAccountEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        baiduAdMarketingManager.refreshAdGroupData(adAccountEntity, 1);
    }

    @Test
    public void refreshAdGroupFeedData() {
        AdAccountEntity adAccountEntity = new AdAccountEntity();
        adAccountEntity.setEa("83668");
        adAccountEntity.setId("8b524863784b46f9a46b3b7f6557befb");
        adAccountEntity.setUsername("bj-fxiaoke");
        adAccountEntity.setSource(AdSourceEnum.SOURCE_BAIDU.getSource());
        baiduAdMarketingManager.refreshAdGroupFeedData(adAccountEntity, 1);
    }

    @Test
    public void getDataOverviewTest() {
        GetDataOverviewVO vo = new GetDataOverviewVO();
        vo.setAdAccountId("8b524863784b46f9a46b3b7f6557befb");
        vo.setSource("百度");
        vo.setStartTime(DateUtil.minusDay(new Date(), 100));
        vo.setEndTime(new Date());
        vo.setDataType(TypeEnum.BAIDU_SEARCH_CAMPAIGN.getCode());
        vo.setKeyword("词");
        Result<GetDataOverviewResult>  result = baiduAdMarketingManager.getDataOverview(vo);
        log.info("结果1： {}", result);

        vo.setDataType(TypeEnum.BAIDU_SEARCH_AD_GROUP.getCode());
        result = baiduAdMarketingManager.getDataOverview(vo);
        log.info("结果2： {}", result);

        vo.setDataType(TypeEnum.BAIDU_FEED_AD_GROUP.getCode());
        result = baiduAdMarketingManager.getDataOverview(vo);
        log.info("结果2： {}", result);
    }

    @Test
    public void syncLeadROIFieldAndGetMarketingEventIdTest() {
        baiduAdMarketingManager.syncCampaignMember("88146", "67ac50b5f5ccb80007f844bf",
                AdSourceEnum.SOURCE_JULIANG.getSource(), "本地推-测试-上海_广告_测试", null, false, null, null, null, "676a7000ba9120000766b009");
    }
}
