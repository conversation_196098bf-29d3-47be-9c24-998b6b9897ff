package com.facishare.marketing.provider.service

import com.facishare.converter.EIEAConverter
import com.facishare.mankeep.api.outService.service.OutSensorsService
import com.facishare.mankeep.api.result.RadarListUnitResult
import com.facishare.mankeep.api.service.CustomerService
import com.facishare.mankeep.api.service.IndexService
import com.facishare.mankeep.common.result.ModelResult
import com.facishare.mankeep.common.result.PageObject
import com.facishare.marketing.api.arg.ListMarketingActivityEnterpriseEmployeeRankingArg
import com.facishare.marketing.api.arg.ListMarketingActivityRankingArg
import com.facishare.marketing.api.arg.MarketingReportArg
import com.facishare.marketing.api.arg.kis.*
import com.facishare.marketing.api.data.MarketingReportAddCountData
import com.facishare.marketing.api.result.qywx.customerGroup.QueryGroupMemberListResult
import com.facishare.marketing.api.service.qywx.QYWXContactService
import com.facishare.marketing.api.service.qywx.QYWXCustomerGroupService
import com.facishare.marketing.provider.dao.*
import com.facishare.marketing.provider.dao.kis.MarketingActivityDayStatisticDAO
import com.facishare.marketing.provider.dao.kis.MarketingActivityEmployeeDayStatisticDAO
import com.facishare.marketing.provider.dao.kis.MarketingEnterpriseDayStatisticDAO
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO
import com.facishare.marketing.provider.dto.CustomizeFormClueNumDTO
import com.facishare.marketing.provider.entity.MarketingUserGroupCustomizeObjectMappingEntity
import com.facishare.marketing.provider.entity.UserEntity
import com.facishare.marketing.provider.entity.kis.GetActivityRankingEntity
import com.facishare.marketing.provider.entity.kis.MarketingActivityEmployeeDayStatisticEntity
import com.facishare.marketing.provider.entity.kis.MarketingEnterpriseDayStatisticEntity
import com.facishare.marketing.provider.entity.qywx.miniapp.QywxMiniappConfigEntity
import com.facishare.marketing.provider.manager.*
import com.facishare.marketing.provider.manager.permission.DataPermissionManager
import com.facishare.marketing.provider.manager.qywx.QywxManager
import com.facishare.marketing.provider.manager.qywx.QywxUserManager
import com.facishare.marketing.provider.manager.qywx.WechatWorkExternalUserObjManager
import com.facishare.marketing.provider.manager.user.UserManager
import com.facishare.marketing.provider.manager.wxthirdplatform.WechatAccountManager
import com.facishare.marketing.provider.remote.CrmMetadataManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.MarketingCrmManager
import com.facishare.marketing.provider.remote.restapi.MetadataControllerServiceManager
import com.facishare.marketing.statistic.outapi.result.CountMarketingSceneResult
import com.facishare.marketing.statistic.outapi.service.UserMarketingStatisticService
import com.fxiaoke.crmrestapi.common.data.InnerPage
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.service.ObjectDescribeService
import spock.lang.Specification
import spock.lang.Unroll

class MarketingReportServiceImplSpec extends Specification {

    // 模拟依赖项
    def marketingEnterpriseDayStatisticDAO = Mock(MarketingEnterpriseDayStatisticDAO)
    def marketingActivityEmployeeDayStatisticDAO = Mock(MarketingActivityEmployeeDayStatisticDAO)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def marketingActivityCrmManager = Mock(MarketingCrmManager)
    def customizeFormClueManager = Mock(CustomizeFormClueManager)
    def marketingActivityDayStatisticDAO = Mock(MarketingActivityDayStatisticDAO)
    def indexService = Mock(IndexService)
    def sensorsService = Mock(OutSensorsService)
    def customerService = Mock(CustomerService)
    def marketingEventManager = Mock(MarketingEventManager)
    def eieaConverter = Mock(EIEAConverter)
    def qywxUserManager = Mock(QywxUserManager)
    def redisManager = Mock(RedisManager)
    def qywxContactService = Mock(QYWXContactService)
    def qywxCustomerGroupService = Mock(QYWXCustomerGroupService)
    def userManager = Mock(UserManager)
    def qywxMiniappConfigDAO = Mock(QywxMiniappConfigDAO)
    def wechatAccountManager = Mock(WechatAccountManager)
    def wechatWorkExternalUserObjManager = Mock(WechatWorkExternalUserObjManager)
    def fileV2Manager = Mock(FileV2Manager)
    def objectDescribeService = Mock(ObjectDescribeService)
    def dataPermissionManager = Mock(DataPermissionManager)
    def crmMetadataManager = Mock(CrmMetadataManager)
    def customizeFormDataUserDAO = Mock(CustomizeFormDataUserDAO)
    def crmV2Manager = Mock(CrmV2Manager)
    def qywxManager = Mock(QywxManager)
    def metadataControllerServiceManager = Mock(MetadataControllerServiceManager)
    def marketingActivityExternalConfigDao = Mock(MarketingActivityExternalConfigDao)
    def marketingUserGroupDao = Mock(MarketingUserGroupDao)
    def userMarketingStatisticService = Mock(UserMarketingStatisticService)
    def marketingUserGroupCustomizeObjectMappingDao = Mock(MarketingUserGroupCustomizeObjectMappingDao)
    def marketingActivityObjectRelationDAO = Mock(MarketingActivityObjectRelationDAO)
    def objectDao = Mock(ObjectDao)
    def marketingReportSendEaList = "[\"1\",\"2\"]"
    def TopCaseExampleList = "[]"


    // 待测系统
    def marketingReportService = new MarketingReportServiceImpl(
            marketingEnterpriseDayStatisticDAO: marketingEnterpriseDayStatisticDAO,
            marketingActivityEmployeeDayStatisticDAO: marketingActivityEmployeeDayStatisticDAO,
            fsAddressBookManager: fsAddressBookManager,
            marketingActivityCrmManager: marketingActivityCrmManager,
            customizeFormClueManager: customizeFormClueManager,
            marketingActivityDayStatisticDAO: marketingActivityDayStatisticDAO,
            indexService: indexService,
            sensorsService: sensorsService,
            customerService: customerService,
            marketingEventManager: marketingEventManager,
            eieaConverter: eieaConverter,
            qywxUserManager: qywxUserManager,
            redisManager: redisManager,
            qywxContactService: qywxContactService,
            qywxCustomerGroupService: qywxCustomerGroupService,
            userManager: userManager,
            qywxMiniappConfigDAO: qywxMiniappConfigDAO,
            wechatAccountManager: wechatAccountManager,
            wechatWorkExternalUserObjManager: wechatWorkExternalUserObjManager,
            fileV2Manager: fileV2Manager,
            objectDescribeService: objectDescribeService,
            dataPermissionManager: dataPermissionManager,
            crmMetadataManager: crmMetadataManager,
            customizeFormDataUserDAO: customizeFormDataUserDAO,
            crmV2Manager: crmV2Manager,
            qywxManager: qywxManager,
            metadataControllerServiceManager: metadataControllerServiceManager,
            marketingActivityExternalConfigDao: marketingActivityExternalConfigDao,
            marketingUserGroupDao: marketingUserGroupDao,
            userMarketingStatisticService: userMarketingStatisticService,
            marketingUserGroupCustomizeObjectMappingDao: marketingUserGroupCustomizeObjectMappingDao,
            marketingActivityObjectRelationDAO: marketingActivityObjectRelationDAO,
            objectDao: objectDao,
            marketingReportSendEaList: marketingReportSendEaList,
            TopCaseExampleList: TopCaseExampleList
    )


    @Unroll
    def "getEnterpriseStatisticSumUp"() {
        given:
        dataPermissionManager.getDataPermissionSetting(*_) >> isOpen
        dataPermissionManager.getDataPermission(*_) >> dataPermission
        marketingEnterpriseDayStatisticDAO.getActivityIdByEaAndDateSpan(*_) >> ["id"]
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 11
        customizeFormDataUserDAO.getClueIdByEaWithMarketingActivity(*_) >> ["id"]
        marketingEnterpriseDayStatisticDAO.listByEaAndDateSpan(*_) >> [new MarketingEnterpriseDayStatisticEntity(lookUpCount: 1, forwardCount: 1)]
        marketingActivityEmployeeDayStatisticDAO.countSpreadEmployee(*_) >> 1
        marketingEnterpriseDayStatisticDAO.countByEaAndDateSpan(*_) >> 1
        customizeFormClueManager.countClueByEaWithMarketingActivity(*_) >> 1
        marketingEventManager.getMarketingEventsTotalCount(*_) >> 1
        when:
        marketingReportService.getEnterpriseStatisticSumUp(new GetEnterpriseStatisticSumUpArg(recentDay: 2))
        then:
        noExceptionThrown()
        where:
        isOpen | dataPermission
        true   | []
        true   | [1]
        false  | [1]
    }

    @Unroll
    def "addSensorsData"() {
        given:
        sensorsService.addSensorsData(*_) >> null
        when:
        marketingReportService.addSensorsData(new GetEnterpriseStatisticSumUpArg(recentDay: 2))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getEnterpriseStatisticTrend"() {
        given:
        marketingEnterpriseDayStatisticDAO.listByEaAndDateSpan(*_) >> [new MarketingEnterpriseDayStatisticEntity(date: new Date(), forwardCount: 1, lookUpCount: 1, spreadCount: 1, leadIncrementCount: 1, customerIncrementCount: 1)]
        when:
        marketingReportService.getEnterpriseStatisticTrend(new GetEnterpriseStatisticTrendArg(recentDay: 2))
        then:
        noExceptionThrown()
    }

    @Unroll
    def "getEmployeeRanking"() {
        given:
        customizeFormClueManager.countFsUserIdClueNumByEa(*_) >> [1: 1]
        marketingActivityEmployeeDayStatisticDAO.orderByCountType(*_) >> [new MarketingActivityEmployeeDayStatisticEntity(fsUserId: 1, leadIncrementCount: 1, spreadCount: 1)]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg(employeeId: 1)]
        when:
        marketingReportService.getEmployeeRanking(vo)
        then:
        noExceptionThrown()
        where:
        vo << [new GetEmployeeRankingArg(recentDay: 1, searchType: 5, topRankingCount: 1), new GetEmployeeRankingArg(recentDay: 1, searchType: 4, topRankingCount: 1)]
    }

    @Unroll
    def "listMarketingActivityEnterpriseEmployeeRanking"() {
        given:
        dataPermissionManager.getDataPermissionSetting(*_) >> open
        dataPermissionManager.getDataPermission(*_) >> dataPermission
        crmV2Manager.countCrmObjectByFilterV3(*_) >> objectCount
        def objectData = new ObjectData();
        objectData.put("_id", "id")
        crmV2Manager.listCrmObjectByFilterV3(*_) >> new InnerPage(dataList: [objectData])
        qywxManager.getCurrentAccountAccessibleFsUserIds(*_) >> [1]
        customizeFormClueManager.countFsUidClueNumByEaAndType(*_) >> [new CustomizeFormClueNumDTO(fsUid: 1, count: 1)]
        marketingActivityEmployeeDayStatisticDAO.listMarketingEmployeeActivityDataPermissionEmployeeRanking(*_) >> [new MarketingActivityEmployeeDayStatisticEntity(fsUserId: 1)]
        marketingActivityEmployeeDayStatisticDAO.listMarketingEmployeeActivityEmployeeRanking(*_) >> [new MarketingActivityEmployeeDayStatisticEntity(fsUserId: 1, leadIncrementCount: 1, spreadCount: 1, lookUpCount: 1)]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1: new FsAddressBookManager.FSEmployeeMsg(employeeId: 1, profileImage: profileImage)]
        fileV2Manager.batchGetUrlByPath(*_) >> ["N_111": "path"]
        fsAddressBookManager.getOutUserInfoByUpstreamAndUserId(*_) >> [1: "name"]
        fileV2Manager.getUrlByPath(*_) >> "aaa"
        when:
        marketingReportService.listMarketingActivityEnterpriseEmployeeRanking("ea", 1, arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                                          | open  | dataPermission | objectCount | profileImage
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 5, pageNum: 1, pageSize: 10, recentDay: 1) | true  | []             | 0           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 5, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 0           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 5, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 1           | "N_111"
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 5, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 1           | "xxxx"
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 5, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 1           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 2, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 1           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 1, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 1           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 1, pageNum: 1, pageSize: 10, recentDay: 1) | false | [1]            | 1           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 1, pageNum: 1, pageSize: 10, recentDay: 1) | true  | []             | 1           | null
        new ListMarketingActivityEnterpriseEmployeeRankingArg(searchType: 1, pageNum: 1, pageSize: 10, recentDay: 1) | true  | [1]            | 0           | null
    }

    @Unroll
    def "getRadarsInfo"() {
        given:
        qywxUserManager.getUidByFsUserInfo(*_) >> uid
        indexService.radars(*_) >> radarResult
        when:
        marketingReportService.getRadarsInfo(vo)
        then:
        noExceptionThrown()
        where:
        vo                               | uid   | radarResult
        new GetRadarsInfoArg(uid: "uid") | null  | new ModelResult(errCode: -1)
        new GetRadarsInfoArg(uid: "uid") | null  | new ModelResult(errCode: 0, data: new PageObject(result: [new RadarListUnitResult(nickName: "客脉访客111")]))
        new GetRadarsInfoArg()           | null  | new ModelResult(errCode: 0, data: new PageObject(result: [new RadarListUnitResult(nickName: "客脉访客111")]))
        new GetRadarsInfoArg()           | "uid" | new ModelResult(errCode: 0, data: new PageObject(result: [new RadarListUnitResult(nickName: "客脉访客111")]))
    }

    @Unroll
    def "getRadarsInfoForCustomer"() {
        given:
        qywxUserManager.getUidByFsUserInfo(*_) >> uid
        indexService.radars(*_) >> radarResult
        qywxMiniappConfigDAO.getByEa(*_) >> new QywxMiniappConfigEntity(appid: "miniappId")
        userManager.queryByQYUserIdAndAppid(*_) >> [new UserEntity(uid: "uid")]
        qywxCustomerGroupService.queryGroupMemberList(*_) >> new com.facishare.marketing.common.result.Result(data: [new QueryGroupMemberListResult(memberType: 2, groupMemberUserId: "id")])
        qywxMiniappConfigDAO.getByEa(*_) >> new QywxMiniappConfigEntity()
        when:
        marketingReportService.getRadarsInfoForCustomer(vo)
        then:
        noExceptionThrown()
        where:
        vo                                                                      | uid   | radarResult
        new GetRadarsInfoArg(uid: "uid", chatType: 1, pageNum: 1, pageSize: 11) | null  | new ModelResult(errCode: -1, data: new PageObject(totalCount: 1))
        new GetRadarsInfoArg(uid: "uid", chatType: 1, pageNum: 1, pageSize: 11) | null  | new ModelResult(errCode: 0, data: new PageObject(totalCount: 1, result: [new RadarListUnitResult(friendUid: "uid", nickName: "客脉访客111")]))
        new GetRadarsInfoArg(uid: "uid", chatType: 2, pageNum: 1, pageSize: 11) | null  | new ModelResult(errCode: 0, data: new PageObject(totalCount: 1, result: [new RadarListUnitResult(friendUid: "uid", nickName: "客脉访客111")]))
        new GetRadarsInfoArg(chatType: 2, pageNum: 1, pageSize: 11)             | null  | new ModelResult(errCode: 0, data: new PageObject(totalCount: 1, result: [new RadarListUnitResult(friendUid: "uid", nickName: "客脉访客111")]))
        new GetRadarsInfoArg(chatType: 2, pageNum: 1, pageSize: 11)             | "uid" | new ModelResult(errCode: 0, data: new PageObject(totalCount: 1, result: [new RadarListUnitResult(friendUid: "uid", nickName: "客脉访客111")]))
    }

    @Unroll
    def "getActivityRanking"() {
        given:
        customizeFormClueManager.countMarketingActivityClueNumByEa(*_) >> ["id": 1]
        marketingActivityDayStatisticDAO.getActivityRanking(*_) >> list
        def objectData = new ObjectData()
        objectData.put("_id", "id")
        marketingActivityCrmManager.listMarketingActivity(*_) >> new com.fxiaoke.crmrestapi.common.data.Page(dataList: [objectData])

        when:
        marketingReportService.getActivityRanking(vo)
        then:
        noExceptionThrown()
        where:
        vo                                                                         | list
        new GetActivityRankingArg(searchType: 5, recentDay: 1, topRankingCount: 1) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new GetActivityRankingArg(searchType: 1, recentDay: 1, topRankingCount: 1) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new GetActivityRankingArg(searchType: 1, recentDay: 1, topRankingCount: 1) | [new GetActivityRankingEntity(count: 0, marketingActivityId: "id")]
    }

    @Unroll
    def "listActivityRanking"() {
        given:
        dataPermissionManager.getDataPermissionSetting(*_) >> isOpen
        dataPermissionManager.getDataPermission(*_) >> dataPermission
        crmV2Manager.countCrmObjectByFilterV3(*_) >> objectCount
        crmV2Manager.listCrmObjectByFilterV3(*_) >> objectResult
        customizeFormClueManager.countMarketingActivityClueNumByEaAndType(*_) >> [new CustomizeFormClueNumDTO(count: 1, marketingActivityId: "id")]
        marketingActivityDayStatisticDAO.listDataPermissionActivityRanking(*_) >> list
        marketingActivityDayStatisticDAO.listActivityRanking(*_) >> list
        def objectData = new ObjectData()
        objectData.put("_id", "id")
        marketingActivityCrmManager.listMarketingActivity(*_) >> new com.fxiaoke.crmrestapi.common.data.Page(dataList: [objectData])
        when:
        marketingReportService.listActivityRanking("ea", 1, arg)
        then:
        noExceptionThrown()
        where:
        arg                                                                                       | isOpen | dataPermission | objectCount | objectResult                                | list
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 5) | true   | []             | 0           | null                                        | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 5) | true   | [1]            | 0           | null                                        | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 5) | true   | [1]            | 1           | new InnerPage(dataList: [])                 | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 5) | true   | [1]            | 1           | new InnerPage(dataList: [new ObjectData()]) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 1) | true   | []             | 1           | new InnerPage(dataList: [new ObjectData()]) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 1) | true   | [1]            | 0           | new InnerPage(dataList: [new ObjectData()]) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 1) | true   | [1]            | 1           | new InnerPage(dataList: [])                 | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 1) | true   | [1]            | 1           | new InnerPage(dataList: [new ObjectData()]) | [new GetActivityRankingEntity(count: 0, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 1) | true   | [1]            | 1           | new InnerPage(dataList: [new ObjectData()]) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
        new ListMarketingActivityRankingArg(pageNum: 1, pageSize: 1, recentDay: 1, searchType: 1) | false  | [1]            | 1           | new InnerPage(dataList: [new ObjectData()]) | [new GetActivityRankingEntity(count: 1, marketingActivityId: "id")]
    }

    @Unroll
    def "marketingReportForAddCount"() {
        given:
        redisManager.get(*_) >> cache
        def spy = Spy(marketingReportService)
        spy.marketingReportForAddCountByTime(*_) >> new MarketingReportAddCountData()
        redisManager.set(*_) >> true
        when:
        spy.marketingReportForAddCount(arg)
        then:
        noExceptionThrown()
        where:
        arg                             | cache
        new MarketingReportArg(ea: "3") | ""
        new MarketingReportArg(ea: "1") | "{}"
        new MarketingReportArg(ea: "1") | ""
    }

    @Unroll
    def "marketingStatisticInfo"() {
        given:
        redisManager.get(*_) >> cache
        def spy = Spy(marketingReportService)
        spy.marketingReportForAddCountByTime(*_) >> new MarketingReportAddCountData()
        userMarketingStatisticService.getMarketingStatisticCountByScene(*_) >>> [objectActionFromUserMarketingList, objectActionFromUserMarketingList2]
        redisManager.set(*_) >> true
        when:
        spy.marketingStatisticInfo(arg)
        then:
        noExceptionThrown()
        where:
        arg                             | cache | objectActionFromUserMarketingList                                                                 | objectActionFromUserMarketingList2
        new MarketingReportArg(ea: "3") | ""    | null                                                                                              | null
        new MarketingReportArg(ea: "1") | "{}"  | null                                                                                              | null
        new MarketingReportArg(ea: "1") | ""    | null                                                                                              | null
        new MarketingReportArg(ea: "1") | ""    | new com.facishare.marketing.statistic.common.result.Result(data: new CountMarketingSceneResult()) | null
        new MarketingReportArg(ea: "1") | ""    | new com.facishare.marketing.statistic.common.result.Result(data: new CountMarketingSceneResult()) | new com.facishare.marketing.statistic.common.result.Result(data: new CountMarketingSceneResult())
    }

    @Unroll
    def "companyAssets"() {
        given:
        redisManager.get(*_) >> cache
        crmV2Manager.countCrmObjectByFilterV3(*_) >> 1
        marketingUserGroupDao.countGroupCountByTime(*_) >> 1
        objectDao.countAllObject(*_) >> 1
        redisManager.set(*_) >> true
        marketingUserGroupCustomizeObjectMappingDao.getByEa(*_) >> [new MarketingUserGroupCustomizeObjectMappingEntity(objectApiName: "name")]
        when:
        marketingReportService.companyAssets(arg)
        then:
        noExceptionThrown()
        where:
        arg                             | cache
        new MarketingReportArg(ea: "3") | ""
        new MarketingReportArg(ea: "1") | "{}"
        new MarketingReportArg(ea: "1") | ""
    }

    @Unroll
    def "companyAssetsOtherData"() {
        given:
        redisManager.get(*_) >> cache
        marketingActivityExternalConfigDao.countMarketingActivityCountByTime(*_) >> 1
        metadataControllerServiceManager.getTotal(*_) >> 1
        redisManager.set(*_) >> true
        when:
        marketingReportService.companyAssetsOtherData(arg)
        then:
        noExceptionThrown()
        where:
        arg                             | cache
        new MarketingReportArg(ea: "3") | ""
        new MarketingReportArg(ea: "1") | "{}"
        new MarketingReportArg(ea: "1") | ""
    }

    @Unroll
    def "queryTopCaseExample"() {
        given:
        when:
        marketingReportService.queryTopCaseExample(ea)
        then:
        noExceptionThrown()
        where:
        ea << [null, "3", "1"]
    }

    @Unroll
    def "marketingReportForAddCountByTime"() {
        given:
        crmV2Manager.isExistObject(*_) >> existObject
        metadataControllerServiceManager.getTotal(*_) >> 1
        eieaConverter.enterpriseAccountToId(*_) >> 1
        objectDao.countAllObject(*_) >> 1
        when:
        marketingReportService.marketingReportForAddCountByTime("ea", 1L, 2L)
        then:
        noExceptionThrown()
        where:
        existObject << [false, true]
    }
}
