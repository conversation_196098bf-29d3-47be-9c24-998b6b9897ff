package com.facishare.marketing.provider.manager


import spock.lang.Specification
import spock.lang.Unroll

class BusinessCardHolderManagerSpec extends Specification {

    def BusinessCardHolderDAO = Mock(com.facishare.marketing.provider.dao.BusinessCardHolderDAO)

    def businessCardHolderManager = new BusinessCardHolderManager(
            businessCardHolderDAO: businessCardHolderDAO
    )

    @Unroll
    def "addOrUpdateCardHolder"() {
        given:
        businessCardHolderDAO.upsertBusinessCardHolder(*_) >> { printf "dd" }
        when:
        businessCardHolderManager.addOrUpdateCardHolder("uid", "uid", 1)
        then:
        noExceptionThrown()

    }


}