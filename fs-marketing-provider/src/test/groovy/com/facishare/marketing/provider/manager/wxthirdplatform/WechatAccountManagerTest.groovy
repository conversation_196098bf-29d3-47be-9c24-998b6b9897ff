package com.facishare.marketing.provider.manager.wxthirdplatform

import com.facishare.marketing.api.arg.UnAuthWxAppArg
import com.facishare.marketing.api.result.WxThirdComponentResult
import com.facishare.marketing.common.enums.WxAppInfoEnum
import com.facishare.marketing.provider.dao.qywx.QywxMiniappConfigDAO
import com.facishare.marketing.provider.dao.wxthirdplatform.EaWechatAccountBindDao
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatAccountConfigDao
import com.facishare.marketing.provider.dao.wxthirdplatform.WechatEaBindDao
import com.facishare.marketing.provider.entity.wxthirdplatform.WechatAccountConfigEntity
import com.facishare.marketing.provider.manager.RedisManager
import com.facishare.marketing.provider.manager.kis.AppVersionManager
import com.facishare.marketing.provider.manager.qywx.HttpManager
import com.facishare.wechat.dubborestouterapi.arg.WechatRequestDispatchArg
import com.facishare.wechat.dubborestouterapi.result.WechatRequestDispatchResult
import com.facishare.wechat.dubborestouterapi.service.proxy.WechatRequestDispatchService
import com.facishare.wechat.proxy.common.result.ModelResult
import com.fxiaoke.wechatrestapi.data.GetAuthorizerInfoResult
import com.fxiaoke.wechatrestapi.data.RefreshAuthorizationTokenResult
import com.fxiaoke.wechatrestapi.data.ServiceTypeInfo
import com.fxiaoke.wechatrestapi.data.VerifyTypeInfo
import com.fxiaoke.wechatrestapi.service.WechatAuthRestService
import spock.lang.*

class WechatAccountManagerTest extends Specification {

    def wechatAccountManager = new WechatAccountManager()

    def wechatAccountConfigDao = Mock(WechatAccountConfigDao)
    def eaWechatAccountBindDao = Mock(EaWechatAccountBindDao)
    def wechatThirdPlatformManager = Mock(WechatThirdPlatformManager)
    def wechatAuthRestService = Mock(WechatAuthRestService)
    def httpManager = Mock(HttpManager)
    def redisManager = Mock(RedisManager)
    def wxCloudRestManager = Mock(WxCloudRestManager)
    def wechatRequestDispatchService = Mock(WechatRequestDispatchService)
    def appVersionManager = Mock(AppVersionManager)
    def wechatEaBindDao = Mock(WechatEaBindDao)
    def qywxMiniappConfigDAO = Mock(QywxMiniappConfigDAO)

    def setup() {
        wechatAccountManager.wechatAccountConfigDao = wechatAccountConfigDao
        wechatAccountManager.eaWechatAccountBindDao = eaWechatAccountBindDao
        wechatAccountManager.wechatThirdPlatformManager = wechatThirdPlatformManager
        wechatAccountManager.wechatAuthRestService = wechatAuthRestService
        wechatAccountManager.httpManager = httpManager
        wechatAccountManager.redisManager = redisManager
        wechatAccountManager.wxCloudRestManager = wxCloudRestManager
        wechatAccountManager.wechatRequestDispatchService = wechatRequestDispatchService
        wechatAccountManager.appVersionManager = appVersionManager
        wechatAccountManager.wechatEaBindDao = wechatEaBindDao
        wechatAccountManager.qywxMiniappConfigDAO = qywxMiniappConfigDAO
        wechatAccountManager.enterpriseEnvironment = "2"
        wechatAccountManager.host = "www.fxiaoke.com"
    }


    def "getAccessTokenByWxAppIdTest"() {
        given:
        wechatAccountConfigDao.getByWxAppId(*_) >> getByWxAppIdMock
        wechatAccountConfigDao.updateAccessToken(*_) >> 0
        wechatThirdPlatformManager.getComponentAppId(*_) >> "getComponentAppIdResponse"
        wechatThirdPlatformManager.getThirdPlatformAccessToken(*_) >> "getThirdPlatformAccessTokenResponse"
        wechatAuthRestService.refreshAuthorizationToken(*_) >> new RefreshAuthorizationTokenResult("authorizerAccessToken", 0, "authorizerRefreshToken")
        httpManager.executeGetHttp(*_) >> null
        httpManager.transformUrlParams(*_) >> "transformUrlParamsResponse"
        redisManager.getValueByKey(*_) >> "getValueByKeyResponse"
        redisManager.setValueWithExpiredTime(*_) >> true
        wxCloudRestManager.getMankeepproAccessToken(*_) >> "111"
        def spy = Spy(wechatAccountManager)
        spy.doGetAndUpdateAccessTokenFromWechat(*_) >> "aaa"

        when:
        String result = spy.getAccessTokenByWxAppId(wxAppIdArg)
        then:
        result == resultMock
        where:
        wxAppIdArg                          | getByWxAppIdMock                                        | resultMock
        WxAppInfoEnum.MankeepPro.getAppId() | null                                                    | "111"
        "appid"                             | new WechatAccountConfigEntity(accessTokenExpireTime: 0) | "aaa"
    }


    def "batchGetAccessTokenTest"() {
        given:
        wechatAccountConfigDao.batchGetYXTConfig(*_) >> [new WechatAccountConfigEntity()]
        wechatAccountConfigDao.updateAccessToken(*_) >> 0
        wechatThirdPlatformManager.getComponentAppId(*_) >> "getComponentAppIdResponse"
        wechatThirdPlatformManager.getThirdPlatformAccessToken(*_) >> "getThirdPlatformAccessTokenResponse"
        wechatAuthRestService.refreshAuthorizationToken(*_) >> new RefreshAuthorizationTokenResult("authorizerAccessToken", 0, "authorizerRefreshToken")
        httpManager.executeGetHttp(*_) >> null
        httpManager.transformUrlParams(*_) >> "transformUrlParamsResponse"
        redisManager.getValueByKey(*_) >> "getValueByKeyResponse"
        redisManager.setValueWithExpiredTime(*_) >> true

        when:
        Map<String, String> result = wechatAccountManager.batchGetAccessToken(["appIds"] as Set<String>)
        then:
        result == ["replaceMeWithExpectedResult": "replaceMeWithExpectedResult"]
    }


    def "batchGetWechatAccountConfigTest"() {
        given:
        wechatAccountConfigDao.batchGetYXTConfig(*_) >> [new WechatAccountConfigEntity(accessTokenExpireTime: 1, wxAppId: "aaa")]
        wechatAccountConfigDao.updateAccessToken(*_) >> 0
        wechatThirdPlatformManager.getComponentAppId(*_) >> "getComponentAppIdResponse"
        wechatThirdPlatformManager.getThirdPlatformAccessToken(*_) >> "getThirdPlatformAccessTokenResponse"
        wechatAuthRestService.refreshAuthorizationToken(*_) >> new RefreshAuthorizationTokenResult("authorizerAccessToken", 0, "authorizerRefreshToken")
        httpManager.executeGetHttp(*_) >> null
        httpManager.transformUrlParams(*_) >> "transformUrlParamsResponse"
        redisManager.getValueByKey(*_) >> "getValueByKeyResponse"
        redisManager.setValueWithExpiredTime(*_) >> true
        def spy = Spy(wechatAccountManager)
        spy.doGetAndUpdateAccessTokenFromWechat(*_) >> "bbb"
        when:
        Map<String, WechatAccountConfigEntity> result = spy.batchGetWechatAccountConfig(appIds)
        then:
        result == resultMock
        where:
        appIds                                                     | resultMock
        new HashSet<String>([WxAppInfoEnum.MankeepPro.getAppId()]) | ["aaa": new WechatAccountConfigEntity(accessTokenExpireTime: 1, wxAppId: "aaa", accessToken: "bbb")]
    }


    def "getSystemAppAccessByKeyTest"() {
        given:
        httpManager.executeGetHttp(*_) >> executeGetHttpMock
        httpManager.transformUrlParams(*_) >> "transformUrlParamsResponse"
        redisManager.getValueByKey(*_) >> null
        redisManager.setValueWithExpiredTime(*_) >> true

        when:
        String result = wechatAccountManager.getSystemAppAccessByKey(WxAppInfoEnum.Mankeep, "key")
        then:
        result == resultMock
        where:
        executeGetHttpMock      | resultMock
        null                    | null
        ["access_token": "111"] | "111"
    }


    def "doGetAndUpdateAccessTokenFromWechatTest"() {
        given:
        wechatAccountConfigDao.updateAccessToken(*_) >> 1
        wechatThirdPlatformManager.getComponentAppId(*_) >> "getComponentAppIdResponse"
        wechatThirdPlatformManager.getThirdPlatformAccessToken(*_) >> "getThirdPlatformAccessTokenResponse"
        wechatAuthRestService.refreshAuthorizationToken(*_) >> new RefreshAuthorizationTokenResult("111", 0, "authorizerRefreshToken")

        when:
        String result = wechatAccountManager.doGetAndUpdateAccessTokenFromWechat(new WechatAccountConfigEntity())
        then:
        result == "111"
    }


    def "getWxAppIdByEaTest"() {
        given:
        wechatAccountConfigDao.getByWxAppId(*_) >> getByWxAppIdMock
        eaWechatAccountBindDao.getWxAppIdByEa(*_) >> getWxAppIdByEaMock
        redisManager.getPreBoundWxAppId(*_) >> "bbb"

        when:
        Optional<String> result = wechatAccountManager.getWxAppIdByEa("ea", platformId)
        then:
        result == resultMock
        where:
        platformId | getByWxAppIdMock                              | getWxAppIdByEaMock | resultMock
        "DHT"      | null                                          | "aaa"              | Optional.of("aaa")
        "YXT"      | new WechatAccountConfigEntity(wxAppId: "aaa") | "aaa"              | Optional.of("bbb")

    }


    def "listEaByPlatformIdAndWxAppIdTest"() {
        given:
        eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(*_) >> ["aaa"]

        when:
        Collection<String> result = wechatAccountManager.listEaByPlatformIdAndWxAppId("platformId", "wxAppId")
        then:
        result == ["aaa"]
    }


    def "getNotEmptyWxAppIdByEaTest"() {
        given:
        wechatAccountConfigDao.getByWxAppId(*_) >> new WechatAccountConfigEntity()
        eaWechatAccountBindDao.getWxAppIdByEa(*_) >> "getWxAppIdByEaResponse"
        eaWechatAccountBindDao.insert(*_) >> 0
        redisManager.getPreBoundWxAppId(*_) >> "getPreBoundWxAppIdResponse"
        def spy = Spy(wechatAccountManager)
        spy.getWxAppIdByEa(*_) >>> getWxAppIdByEaMock
        when:
        String result = spy.getNotEmptyWxAppIdByEa("ea")
        then:
        result == resultMock
        where:
        getWxAppIdByEaMock                     | resultMock
        [Optional.of("aaa")]                   | "aaa"
        [Optional.empty(), Optional.of("bbb")] | "bbb"
    }


    def "updateWechatAccountInfoTest"() {
        given:
        wechatAccountConfigDao.updateAppInfo(*_) >> 0
        wechatThirdPlatformManager.getComponentAccessTokenAndAppId(*_) >> new WxThirdComponentResult("aaa", "bbb")
        wechatAuthRestService.getAuthorizerInfo(*_) >> new GetAuthorizerInfoResult(errCode: 0, appInfo: new com.fxiaoke.wechatrestapi.data.AppInfo(serviceTypeInfo: new ServiceTypeInfo(1), verifyTypeInfo: new VerifyTypeInfo()))

        when:
        wechatAccountManager.updateWechatAccountInfo("platformId", "wxAppId")
        then:
        noExceptionThrown() // todo - validate something

    }


    def "bindWxAppIdAndEaTest"() {
        given:
        wxCloudRestManager.bindWxAppIdAndEa(*_) >> true
        appVersionManager.isFxCloud(*_) >> false
        appVersionManager.isVpnDisconnectCloud(*_) >> isVpnDisconnectCloudMock
        wechatEaBindDao.add(*_) >> 1

        when:
        boolean result = wechatAccountManager.bindWxAppIdAndEa("wxAppId", "ea")
        then:
        result == resultMock
        where:
        isVpnDisconnectCloudMock | resultMock
        true                     | true
        false                    | true
    }


    def "isOpenPayTest"() {
        given:
        wechatAccountConfigDao.getByWxAppId(*_) >> getByWxAppIdMock

        when:
        boolean result = wechatAccountManager.isOpenPay("wxAppId")
        then:
        result == resultMock
        where:
        getByWxAppIdMock                                                     | resultMock
        new WechatAccountConfigEntity()                                      | false
        new WechatAccountConfigEntity(businessInfo: "{\"openPay\":\"aaa\"}") | false
    }


    def "handleUnauthorizedEventTest"() {
        given:
        wechatAccountConfigDao.deleteByWxAppId(*_) >> 0
        eaWechatAccountBindDao.insert(*_) >> 0
        eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(*_) >> ["listEaByPlatformIdAndWxAppIdResponse"]
        eaWechatAccountBindDao.delete(*_) >> 0
        wxCloudRestManager.unbindWxAppIdAndEa(*_) >> true
        def spy = Spy(wechatAccountManager)
        spy.listEaByPlatformIdAndWxAppId(*_) >> ["aaa"]
        spy.unAuthWxAppId(*_) >> true

        when:
        wechatAccountManager.handleUnauthorizedEvent("YXT", "authorizerAppId", "unauthorized")
        then:
        noExceptionThrown() // todo - validate something

    }


    def "unAuthWxAppIdTest"() {
        given:
        wechatAccountConfigDao.deleteByWxAppId(*_) >> 0
        eaWechatAccountBindDao.insert(*_) >> 0
        eaWechatAccountBindDao.listEaByPlatformIdAndWxAppId(*_) >> []
        eaWechatAccountBindDao.delete(*_) >> 0
        wxCloudRestManager.unbindWxAppIdAndEa(*_) >> true

        when:
        boolean result = wechatAccountManager.unAuthWxAppId("ea", arg)
        then:
        result == resultMock
        where:
        arg                                                                         | resultMock
        new UnAuthWxAppArg(platformId: "YXT", wxAppId: WxAppInfoEnum.Mankeep.appId) | false
        new UnAuthWxAppArg(platformId: "YXT", wxAppId: "aa")                        | true
    }


    def "unbindWxAppIdAndEaTest"() {
        given:
        wxCloudRestManager.unbindWxAppIdAndEa(*_) >> true
        appVersionManager.isFxCloud(*_) >> false
        appVersionManager.isVpnDisconnectCloud(*_) >> isVpnDisconnectCloudMock
        wechatEaBindDao.getEaByAppId(*_) >> getEaByAppIdMock
        wechatEaBindDao.delete(*_) >> 1

        when:
        boolean result = wechatAccountManager.unbindWxAppIdAndEa("ea", "wxAppId")
        then:
        result == resultMock
        where:
        isVpnDisconnectCloudMock | getEaByAppIdMock | resultMock
        true                     | "ea"             | true
        true                     | "ea1"            | false
        false                    | "ea1"            | true
    }

}