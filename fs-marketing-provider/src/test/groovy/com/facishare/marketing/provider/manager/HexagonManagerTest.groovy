package com.facishare.marketing.provider.manager

import com.facishare.marketing.api.arg.HexagonhomepageDetailArg
import com.facishare.marketing.api.arg.hexagon.HexagonCopyArg
import com.facishare.marketing.api.result.AddCustomizeFormDataResult
import com.facishare.marketing.api.result.CustomizeFormDataDetailResult
import com.facishare.marketing.api.result.LeadPoolResult
import com.facishare.marketing.api.result.hexagon.CreateSiteResult
import com.facishare.marketing.api.result.hexagon.GetPageDetailResult
import com.facishare.marketing.api.result.hexagon.GetSiteByEaUnitResult
import com.facishare.marketing.api.result.hexagon.SitePreviewResult
import com.facishare.marketing.api.result.live.ChannelsAccountResult
import com.facishare.marketing.api.service.CrmService
import com.facishare.marketing.api.service.CustomizeFormDataService
import com.facishare.marketing.common.enums.hexagon.HexagonPreviewEnum
import com.facishare.marketing.common.enums.hexagon.HexagonStatusEnum
import com.facishare.marketing.common.result.Result
import com.facishare.marketing.common.result.SHErrorCode
import com.facishare.marketing.provider.dao.ContentMarketingEventMaterialRelationDAO
import com.facishare.marketing.provider.dao.MarketingObjectActivityStatisticDAO
import com.facishare.marketing.provider.dao.MaterialRelationDao
import com.facishare.marketing.provider.dao.MemberAccessibleObjectDao
import com.facishare.marketing.provider.dao.MemberConfigDao
import com.facishare.marketing.provider.dao.hexagon.HexagonPageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonSiteObjectDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplatePageDAO
import com.facishare.marketing.provider.dao.hexagon.HexagonTemplateSiteDAO
import com.facishare.marketing.provider.dao.manager.HexagonSiteDAOManager
import com.facishare.marketing.provider.dao.statistic.MarketingObjectAmountStatisticDao
import com.facishare.marketing.provider.dto.ObjectStatisticData
import com.facishare.marketing.provider.dto.TargetPhotoPathDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonBaseInfoDTO
import com.facishare.marketing.provider.dto.hexagon.HexagonSiteListDTO
import com.facishare.marketing.provider.dto.kis.MarketingActivityObjectInfoDTO
import com.facishare.marketing.provider.entity.ContentMarketingEventMaterialRelationEntity
import com.facishare.marketing.provider.entity.MaterialRelationEntity
import com.facishare.marketing.provider.entity.MemberConfigEntity
import com.facishare.marketing.provider.entity.PhotoEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplatePageEntity
import com.facishare.marketing.provider.entity.hexagon.HexagonTemplateSiteEntity
import com.facishare.marketing.provider.entity.live.MarketingLiveEntity
import com.facishare.marketing.provider.manager.kis.MarketingActivityManager
import com.facishare.marketing.provider.manager.live.ChannelsManager
import com.facishare.marketing.provider.manager.qr.QRCodeManager
import com.facishare.marketing.provider.remote.CrmV2Manager
import com.facishare.marketing.provider.remote.CrmV2MappingManager
import com.facishare.marketing.provider.remote.IntegralServiceManager
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.google.common.collect.Lists
import spock.lang.*

class HexagonManagerTest extends Specification {

    def hexagonManager = new HexagonManager()

    def hexagonSiteDAO = Mock(HexagonSiteDAO)
    def crmService = Mock(CrmService)
    def customizeFormDataService = Mock(CustomizeFormDataService)
    def fileV2Manager = Mock(FileV2Manager)
    def photoManager = Mock(PhotoManager)
    def qrCodeManager = Mock(QRCodeManager)
    def marketingActivityManager = Mock(MarketingActivityManager)
    def customizeFormClueManager = Mock(CustomizeFormClueManager)
    def memberConfigDao = Mock(MemberConfigDao)
    def memberAccessibleObjectDao = Mock(MemberAccessibleObjectDao)
    def fsAddressBookManager = Mock(FsAddressBookManager)
    def crmV2MappingManager = Mock(CrmV2MappingManager)
    def crmV2Manager = Mock(CrmV2Manager)
    def materialRelationDao = Mock(MaterialRelationDao)
    def contentMarketingEventMaterialRelationDAO = Mock(ContentMarketingEventMaterialRelationDAO)
    def marketingObjectActivityStatisticDAO = Mock(MarketingObjectActivityStatisticDAO)
    def customizeFormDataManager = Mock(CustomizeFormDataManager)
    def marketingObjectAmountStatisticDao = Mock(MarketingObjectAmountStatisticDao)
    def memberManager = Mock(MemberManager)
    def hexagonSiteObjectDAO = Mock(HexagonSiteObjectDAO)
    def redisManager = Mock(RedisManager)
    def hexagonSiteDAOManager = Mock(HexagonSiteDAOManager)
    def hexagonTemplatePageDAO = Mock(HexagonTemplatePageDAO)
    def channelsManager = Mock(ChannelsManager)
    def hexagonPageDAO = Mock(HexagonPageDAO)
    def integralServiceManager = Mock(IntegralServiceManager)
    def hexagonTemplateSiteDAO = Mock(HexagonTemplateSiteDAO)
    def host = "https://crm.ceshi112.com"
    def groupMessageDefaultCoverPath = "https://crm.ceshi112.com/groupMessageDefaultCover"
    def cdnSharePath = "https://a2.ceshi112.com"

    def setup() {
        hexagonManager.hexagonSiteDAO = hexagonSiteDAO
        hexagonManager.crmService = crmService
        hexagonManager.customizeFormDataService = customizeFormDataService
        hexagonManager.fileV2Manager = fileV2Manager
        hexagonManager.photoManager = photoManager
        hexagonManager.qrCodeManager = qrCodeManager
        hexagonManager.marketingActivityManager = marketingActivityManager
        hexagonManager.customizeFormClueManager = customizeFormClueManager
        hexagonManager.memberConfigDao = memberConfigDao
        hexagonManager.memberAccessibleObjectDao = memberAccessibleObjectDao
        hexagonManager.fsAddressBookManager = fsAddressBookManager
        hexagonManager.crmV2MappingManager = crmV2MappingManager
        hexagonManager.crmV2Manager = crmV2Manager
        hexagonManager.materialRelationDao = materialRelationDao
        hexagonManager.contentMarketingEventMaterialRelationDAO = contentMarketingEventMaterialRelationDAO
        hexagonManager.marketingObjectActivityStatisticDAO = marketingObjectActivityStatisticDAO
        hexagonManager.customizeFormDataManager = customizeFormDataManager
        hexagonManager.marketingObjectAmountStatisticDao = marketingObjectAmountStatisticDao
        hexagonManager.memberManager = memberManager
        hexagonManager.hexagonSiteObjectDAO = hexagonSiteObjectDAO
        hexagonManager.redisManager = redisManager
        hexagonManager.hexagonSiteDAOManager = hexagonSiteDAOManager
        hexagonManager.hexagonTemplatePageDAO = hexagonTemplatePageDAO
        hexagonManager.channelsManager = channelsManager
        hexagonManager.hexagonPageDAO = hexagonPageDAO
        hexagonManager.integralServiceManager = integralServiceManager
        hexagonManager.hexagonTemplateSiteDAO = hexagonTemplateSiteDAO
        hexagonManager.host = host
        hexagonManager.groupMessageDefaultCoverPath = groupMessageDefaultCoverPath
        hexagonManager.cdnSharePath = cdnSharePath
    }

    def "getGetSiteByEaUnitResultsTest"() {
        given:
        marketingActivityManager.getActivityIdsByObject(*_) >> ["111": [new MarketingActivityObjectInfoDTO.ActivityObjectInfo()]]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [1000: new FsAddressBookManager.FSEmployeeMsg(employeeId: 1000)]
        groupMessageDefaultCoverPath = "path"
        hexagonSiteDAO.getCoverBySiteIds(*_) >> getCoverBySiteIdsMock
        fileV2Manager.batchGetUrlByPath(*_) >> batchGetUrlByPathMock
        hexagonSiteDAO.getFormBySiteIds(*_) >> [new HexagonSiteListDTO(hexagonSiteId: "111", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc", formId: "formId1")]
        crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(*_) >> ["form1": true]
        customizeFormClueManager.queryHexagonSiteClueCount(*_) >> queryHexagonSiteClueCountMock
        contentMarketingEventMaterialRelationDAO.getContentMarketingByEaAndObjectTypeAndObjectIds(*_) >> getContentMarketingByEaAndObjectTypeAndObjectIdsMock
        marketingObjectAmountStatisticDao.listStatisticData(*_) >> listStatisticDataMock
        hexagonPageDAO.listHomePageBySiteIds(*_) >> [new HexagonPageEntity(id: "222", hexagonSiteId: "111")]
        photoManager.queryPhotosByTypeAndTargetIdsNoReset(*_) >>> queryPhotoByTypeAndTargetIdsNoResetMock
        customizeFormDataManager.batchGetFormUsageByFormIds(*_) >> batchGetFormUsageByFormIdsMock
        crmV2Manager.getList(*_) >> getListMock
        crmV2Manager.getListByCrmObjectIds(*_) >> getListByCrmObjectIdsMock
        when:
        List<GetSiteByEaUnitResult> result = hexagonManager.getGetSiteByEaUnitResults("ea", 1000, hexagonSiteEntityListArg)
        then:
        result.size() == resultMock.size()
        where:
        hexagonSiteEntityListArg                                                                                           | resultMock                                                                                                                                                                                                        | queryPhotoByTypeAndTargetIdsNoResetMock                                                                                          | getListByCrmObjectIdsMock | batchGetUrlByPathMock | getCoverBySiteIdsMock                                                                                           | batchGetFormUsageByFormIdsMock | queryHexagonSiteClueCountMock | listStatisticDataMock                      | getContentMarketingByEaAndObjectTypeAndObjectIdsMock | getListMock
        [new HexagonSiteEntity(id: "111", createBy: 1000, updateBy: 1000, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path1": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "111", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | ["111": 1]                    | [new ObjectStatisticData()]                | [new ContentMarketingEventMaterialRelationEntity(id: "333", objectId: "111", marketingEventId: "222")] | null
        [new HexagonSiteEntity(id: "111", createBy: 1001, updateBy: 1001, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path1": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "111", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | ["111": 1]                    | [new ObjectStatisticData()]                | [new ContentMarketingEventMaterialRelationEntity(id: "333", objectId: "111", marketingEventId: "222")] | null
        [new HexagonSiteEntity(id: "111", createBy: 1001, updateBy: 1001, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path2": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "333", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | ["111": 1]                    | [new ObjectStatisticData()]                | [new ContentMarketingEventMaterialRelationEntity(id: "333", objectId: "111", marketingEventId: "222")] | null
        [new HexagonSiteEntity(id: "111", createBy: 1001, updateBy: 1001, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path2": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "333", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | ["111": 1]                    | [new ObjectStatisticData()]                | [new ContentMarketingEventMaterialRelationEntity(id: "333", objectId: "111", marketingEventId: "222")] | null
        [new HexagonSiteEntity(id: "111", createBy: 1001, updateBy: 1001, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path2": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "333", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | null                          | [new ObjectStatisticData()]                | null | null
        [new HexagonSiteEntity(id: "111", createBy: 1001, updateBy: 1001, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path2": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "333", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | null                          | [new ObjectStatisticData(objectId: "111")] | null | null
        [new HexagonSiteEntity(id: "111", createBy: 1001, updateBy: 1001, createTime: new Date(), updateTime: new Date())] | [Lists.newArrayList(new GetSiteByEaUnitResult(id: "111", coverUrl: "url1", coverAPath: "path", leadCount: 1, createTime: System.currentTimeMillis(), updateTime: System.currentTimeMillis(), formId: "formId1"))] | [Lists.newArrayList(new PhotoEntity(targetId: "222", path: "path1", url: "url1", thumbnailPath: "path1", thumbnailUrl: "url1"))] | null                      | ["path2": "url1"]     | [new HexagonSiteListDTO(hexagonSiteId: "333", sharePicH5Apath: "path", shareTitle: "title", shareDesc: "desc")] | ["form1": 1]                   | null                          | [new ObjectStatisticData(objectId: "111")] | null | new Page<ObjectData>(dataList: Lists.newArrayList(new ObjectData(id: "111", marketing_event_id: "222")))

        //    [new HexagonSiteEntity(id: "111", createBy: 1000, updateBy: 1000)] | [Lists.newArrayList()] | [Lists.newArrayList(new PhotoEntity(targetId: "111", path: "path1"))] | [new com.fxiaoke.crmrestapi.common.data.Page<ObjectData>(dataList: Lists.newArrayList(new ObjectData(id: "111", marketing_event_id: "222")))]

    }

    def "batchGetHexagonSiteQRCodeURLTest"() {
        given:
        photoManager.querySinglePhoto(*_) >> new PhotoEntity()
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> true
        qrCodeManager.createQRCode(*_) >> new QRCodeManager.CreateQRCodeResult()
        qrCodeManager.createCustomQRCode(*_) >> new QRCodeManager.CreateQRCodeResult()
        def spy = Spy(hexagonManager)
        spy.getHexagonSiteQRCodeURL(*_) >> Result.newSuccess(new SitePreviewResult(h5QRUrl: "url1"))

        when:
        Result<Map<String, SitePreviewResult>> result = spy.batchGetHexagonSiteQRCodeURL(ids, "ea")
        then:
        result == resultMock
        where:
        ids                       | resultMock
        null                      | Result.newSuccess(new HashMap<String, SitePreviewResult>())
        Lists.newArrayList("111") | Result.newSuccess("111": new SitePreviewResult(h5QRUrl: "url1"))
    }


    def "batchGetHexagonSiteH5PreviewQRCodeURLTest"() {
        given:
        fileV2Manager.batchGetUrlByPath(*_) >> batchGetUrlByPathMock
        photoManager.listPathByTargetTypeAndIds(*_) >> listPathByTargetTypeAndIdsMock

        when:
        Map<String, SitePreviewResult> result = hexagonManager.batchGetHexagonSiteH5PreviewQRCodeURL("ea", ids)
        then:
        result == resultMock
        where:
        ids                       | resultMock                                                                     | listPathByTargetTypeAndIdsMock                                      | batchGetUrlByPathMock
        null                      | null                                                                           | null                                                                | null
        Lists.newArrayList("111") | null                                                                           | [new TargetPhotoPathDTO(path: "111", targetId: "222", url: "url1")] | null
        Lists.newArrayList("222") | new HashMap(["222": new SitePreviewResult(h5QRUrl: "url1", h5QRAPath: "111")]) | [new TargetPhotoPathDTO(path: "111", targetId: "222", url: "url1")] | new HashMap("111": "url1")
    }


    def "getHexagonSiteQRCodeURLTest"() {
        given:
        photoManager.querySinglePhoto(*_) >> querySinglePhotoMock
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >>> addOrUpdatePhotoByPhotoTargetTypeMock
        qrCodeManager.createQRCode(*_) >>> createQRCodeMock
        qrCodeManager.createCustomQRCode(*_) >> createCustomQRCodeMock

        when:
        Result<SitePreviewResult> result = hexagonManager.getHexagonSiteQRCodeURL("id", "ea", channelValue, extraParam)
        then:
        result == resultMock
        where:
        channelValue | extraParam | resultMock                                                                                                                  | createQRCodeMock                                                                                                       | addOrUpdatePhotoByPhotoTargetTypeMock | querySinglePhotoMock                        | createCustomQRCodeMock
        "1"          | null       | Result.newError(SHErrorCode.QRCODE_CREATE_FAILED)                                                                           | [null, null]                                                                                                           | [false, false, false]                 | null                                        | null
        "1"          | null       | Result.newError(SHErrorCode.OPERATE_DB_FAIL)                                                                                | [new QRCodeManager.CreateQRCodeResult(), null]                                                                         | [false, false, false]                 | null                                        | null
        "1"          | null       | Result.newError(SHErrorCode.QRCODE_CREATE_FAILED)                                                                           | [new QRCodeManager.CreateQRCodeResult(), null]                                                                         | [true, false, false]                  | null                                        | null
        "1"          | null       | Result.newError(SHErrorCode.OPERATE_DB_FAIL)                                                                                | [new QRCodeManager.CreateQRCodeResult(), new QRCodeManager.CreateQRCodeResult(qrCodeApath: "path", qrCodeUrl: "url1")] | [true, false, false]                  | null                                        | null
        "1"          | null       | Result.newError(SHErrorCode.QRCODE_CREATE_FAILED)                                                                           | [new QRCodeManager.CreateQRCodeResult(), new QRCodeManager.CreateQRCodeResult(qrCodeApath: "path", qrCodeUrl: "url1")] | [true, true, false]                   | null                                        | null
        "1"          | null       | Result.newError(SHErrorCode.OPERATE_DB_FAIL)                                                                                | [new QRCodeManager.CreateQRCodeResult(), new QRCodeManager.CreateQRCodeResult(qrCodeApath: "path", qrCodeUrl: "url1")] | [true, true, false]                   | null                                        | new QRCodeManager.CreateQRCodeResult(qrCodeUrl: "url2")
        "1"          | null       | Result.newSuccess(new SitePreviewResult(miniappQRUrl: "url1", bdQRUrl: "url2", miniappQRAPath: "path", bdQRAPath: "path2")) | [new QRCodeManager.CreateQRCodeResult(), new QRCodeManager.CreateQRCodeResult(qrCodeApath: "path", qrCodeUrl: "url1")] | [true, true, true]                    | new PhotoEntity(url: "url2", path: "path2") | new QRCodeManager.CreateQRCodeResult(qrCodeUrl: "url2")

    }


    def "getHexagonTemplateSiteQRCodeURLTest"() {
        given:
        photoManager.querySinglePhoto(*_) >> querySinglePhotoMock
        photoManager.addOrUpdatePhotoByPhotoTargetType(*_) >> addOrUpdatePhotoByPhotoTargetTypeMock
        qrCodeManager.createQRCode(*_) >> createQRCodeMock
        qrCodeManager.createCustomQRCode(*_) >> createCustomQRCodeMock

        when:
        Result<SitePreviewResult> result = hexagonManager.getHexagonTemplateSiteQRCodeURL("id", "88146")
        then:
        result == resultMock
        where:
        createQRCodeMock                                        | resultMock                                                                                       | querySinglePhotoMock | createCustomQRCodeMock                                  | addOrUpdatePhotoByPhotoTargetTypeMock
        null                                                    | Result.newError(SHErrorCode.QRCODE_CREATE_FAILED)                                                | null                 | null                                                    | true
        new QRCodeManager.CreateQRCodeResult(qrCodeUrl: "url1") | Result.newError(SHErrorCode.OPERATE_DB_FAIL)                                                     | null                 | null                                                    | false
        new QRCodeManager.CreateQRCodeResult(qrCodeUrl: "url1") | Result.newError(SHErrorCode.QRCODE_CREATE_FAILED)                                                | null                 | null                                                    | true
        new QRCodeManager.CreateQRCodeResult(qrCodeUrl: "url1") | Result.newSuccess(new SitePreviewResult(h5QRUrl: "url1", bdQRUrl: "url2", miniappQRUrl: "url1")) | null                 | new QRCodeManager.CreateQRCodeResult(qrCodeUrl: "url2") | true
    }

/*
    def "getGetSiteByEaUnitResultsTest"() {
        given:
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO()]
        hexagonSiteDAO.getFormBySiteIds(*_) >> [new HexagonSiteListDTO()]
        fileV2Manager.batchGetUrlByPath(*_) >> ["batchGetUrlByPathResponse": "batchGetUrlByPathResponse"]
        photoManager.queryPhotosByTypeAndTargetIdsNoReset(*_) >> [new PhotoEntity()]
        marketingActivityManager.getActivityIdsByObject(*_) >> ["getActivityIdsByObjectResponse": [new MarketingActivityObjectInfoDTO.ActivityObjectInfo()]]
        customizeFormClueManager.queryHexagonSiteClueCount(*_) >> ["queryHexagonSiteClueCountResponse": 0]
        fsAddressBookManager.getEmployeeInfoByUserIds(*_) >> [(0): new FsAddressBookManager.FSEmployeeMsg()]
        crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(*_) >> ["branchVerifyMankeepCrmObjectFieldMappingResponse": Boolean.TRUE]
        crmV2Manager.getList(*_) >> new Page<ObjectData>()
        contentMarketingEventMaterialRelationDAO.getContentMarketingByEaAndObjectTypeAndObjectIds(*_) >> [new ContentMarketingEventMaterialRelationEntity()]
        customizeFormDataManager.batchGetFormUsageByFormIds(*_) >> ["batchGetFormUsageByFormIdsResponse": 0]
        marketingObjectAmountStatisticDao.listStatisticData(*_) >> [new ObjectStatisticData()]
        hexagonPageDAO.listHomePageBySiteIds(*_) >> [new HexagonPageEntity()]

        when:
        List<GetSiteByEaUnitResult> result = hexagonManager.getGetSiteByEaUnitResults("ea", 0, [new HexagonSiteEntity()])
        then:
        result == [new GetSiteByEaUnitResult()]
    }
*/

    def "resetContentCpathTest"() {
        given:
        fileV2Manager.copyCFileToCFile(*_) >> copyCFileToCFileMock

        when:
        String result = hexagonManager.resetContentCpath(content, "88146", "88146")
        then:
        result == resultMock
        where:
        content                                     | resultMock                                   | copyCFileToCFileMock
        "content"                                   | "content"                                    | null
        "https://a2.ceshi112.com/image/88146/cpath" | "https://a2.ceshi112.com/image/88146/cpath"  | Optional.empty()
        "https://a2.ceshi112.com/image/88146/cpath" | "https://a2.ceshi112.com/image/88146/cpath2" | Optional.of("cpath2")
        "https://a2.ceshi112.com/image/88146/cpath" | "https://a2.ceshi112.com/image/88146/cpath"  | Optional.empty()
    }


    def "resetContentApathTest"() {
        given:
        fileV2Manager.uploadToApath(*_) >> "path2"
        fileV2Manager.getApathByUrl(*_) >> "path1"
        fileV2Manager.downloadAFile(*_) >> new byte[10]
        fileV2Manager.getSpliceUrl(*_) >> "url1"

        when:
        String result = hexagonManager.resetContentApath("imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg", "88146")
        then:
        result == "imgUrl\":\"url1"
    }


    def "resetShareApathTest"() {
        given:
        fileV2Manager.uploadToApath(*_) >> uploadToApathMock
        fileV2Manager.downloadAFile(*_) >> downloadAFileMock

        when:
        String result = hexagonManager.resetShareApath("shareApath", "ea")
        then:
        result == resultMock
        where:
        uploadToApathMock | downloadAFileMock | resultMock
        null              | null              | "shareApath"
        "path1"           | new byte[10]      | "path1"
    }


    def "getHexagonBaseInfoByIdTest"() {
        given:
        hexagonSiteDAO.getAllByIds(*_) >> getAllByIdsMock
        hexagonSiteDAO.getCoverBySiteIds(*_) >> [new HexagonSiteListDTO(hexagonSiteId: "111", sharePicH5Apath: "path1", shareTitle: "title", shareDesc: "desc")]
        fileV2Manager.batchGetUrlByPath(*_) >> ["path1": "url1"]

        when:
        Map<String, HexagonBaseInfoDTO> result = hexagonManager.getHexagonBaseInfoById(ids, "88146")
        then:
        result == resultMock
        where:
        ids     | resultMock                                                                                                                                    | getAllByIdsMock
        null    | null                                                                                                                                          | null
        ["123"] | null                                                                                                                                          | null
        ["123"] | new HashMap<String, HexagonBaseInfoDTO>(["111": new HexagonBaseInfoDTO(id: "111", coverUrl: "url1", shareTitle: "title", shareDesc: "desc")]) | [new HexagonSiteEntity(id: "111")]
        ["123"] | new HashMap<String, HexagonBaseInfoDTO>(["222": new HexagonBaseInfoDTO(id: "222")])                                                           | [new HexagonSiteEntity(id: "222")]
    }


    def "resetContentActionIdTest"() {
        given:
        hexagonPageDAO.getByHexagonSiteId(*_) >> [new HexagonPageEntity(id: "111", name: "111", content: "{\"id\":\"bfd1bcd405ee41328024aeadf3b7ee4a\",\"type\":\"page\",\"name\":\"子直播\",\"title\":\"\",\"version\":\"4.2.0-8\",\"cover\":\"\",\"shareOpts\":{\"title\":\"子直播\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"},\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"}]}]}")]

        when:
        List<HexagonPageEntity> result = hexagonManager.resetContentActionId("preSiteId", "newSiteId")
        then:
        result == [new HexagonPageEntity(id: "111", name: "111", content: "{\"id\":\"bfd1bcd405ee41328024aeadf3b7ee4a\",\"type\":\"page\",\"name\":\"子直播\",\"title\":\"\",\"version\":\"4.2.0-8\",\"cover\":\"\",\"shareOpts\":{\"title\":\"子直播\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"},\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"}]}]}")]
    }


    def "updateCopiedLiveHexagonTest"() {
        given:
        hexagonSiteDAO.getById(*_) >> getByIdMock
        hexagonSiteDAO.updateHexagonSiteSystemStatus(*_) >> 0
        hexagonPageDAO.getHomePage(*_) >> new HexagonPageEntity()
        hexagonPageDAO.updateContent(*_) >> 0

        when:
        hexagonManager.updateCopiedLiveHexagon(new MarketingLiveEntity(), ea, "siteId", "title", "desc", "coverPath", new GregorianCalendar(2024, Calendar.APRIL, 19, 18, 5).getTime(), true)
        then:
        noExceptionThrown() // todo - validate something
        where:
        getByIdMock             | ea
        null                    | null
        new HexagonSiteEntity() | null

    }


    def "updateLiveHexagonParamTest"() {
        given:
        fileV2Manager.getUrlByPath(*_) >> "getUrlByPathResponse"
        memberManager.isOpenMember(*_) >> isOpenMemberMock
        memberManager.tryInitMemberConfig(*_) >> tryInitMemberConfigMock
        channelsManager.getAccount(*_) >> new Result<ChannelsAccountResult>(0, "errMsg", new ChannelsAccountResult(channelsAvatar: "11111", channelsName: "11111"))
        hexagonPageDAO.update(*_) >> 0
        hexagonPageDAO.getByHexagonSiteId(*_) >> getByHexagonSiteIdMock
        hexagonPageDAO.updateContent(*_) >> 0

        when:
        HexagonPageEntity result = hexagonManager.updateLiveHexagonParam(hexagonPageEntity, new MarketingLiveEntity(id: "111", platform: 5, shortViewUrl: "shortViewUrl", marketingEventId: "marketingEventId", associatedAccountId: "111222"), "88146", siteId, "title", desc, "path1", new GregorianCalendar(2024, Calendar.APRIL, 19, 18, 5).getTime(), true)
        then:
        result == resultMock
        where:
        hexagonPageEntity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | siteId | desc                                                      | resultMock | tryInitMemberConfigMock | getByHexagonSiteIdMock | isOpenMemberMock
        null                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | null   | null                                                      | null | null | null | true
        new HexagonPageEntity()                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | null   | null                                                      | null | null | null | true
        new HexagonPageEntity(content: "{\"id\":\"bfd1bcd405ee41328024aeadf3b7ee4a\",\"type\":\"page\",\"name\":\"子直播\",\"title\":\"\",\"version\":\"4.2.0-8\",\"cover\":\"\",\"shareOpts\":{\"title\":\"子直播\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"},\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"}]}]}") | null   | "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" | new HexagonPageEntity(shareTitle: "title", shareDesc: "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", name: "title", sharePicH5Apath: "path1", sharePicMpApath: "path1", content: "{\"id\":\"bfd1bcd405ee41328024aeadf3b7ee4a\",\"type\":\"page\",\"name\":\"title\",\"title\":\"\",\"version\":\"4.2.0-8\",\"cover\":\"\",\"shareOpts\":{\"title\":\"title\",\"desc\":\"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\",\"link\":\"\",\"imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=path1\"},\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=path1\"}]}]}") | new MemberConfigEntity() | [new HexagonPageEntity(id: "111", name: "预约报名", formId: "aaa", content: "aaaa"), new HexagonPageEntity(id: "222", name: "提交成功", formId: "bbb", content: "bbbb")] | true
        new HexagonPageEntity(content: "{\"id\":\"bfd1bcd405ee41328024aeadf3b7ee4a\",\"type\":\"page\",\"name\":\"子直播\",\"title\":\"\",\"version\":\"4.2.0-8\",\"cover\":\"\",\"shareOpts\":{\"title\":\"子直播\",\"desc\":\"\",\"link\":\"\",\"imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"},\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=A_202303_01_d4a2c642acd541c4a95ed0b8065ec2c9.jpg\"}]}]}") | null   | "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" | new HexagonPageEntity(shareTitle: "title", shareDesc: "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", name: "title", sharePicH5Apath: "path1", sharePicMpApath: "path1", content: "{\"id\":\"bfd1bcd405ee41328024aeadf3b7ee4a\",\"type\":\"page\",\"name\":\"title\",\"title\":\"\",\"version\":\"4.2.0-8\",\"cover\":\"\",\"shareOpts\":{\"title\":\"title\",\"desc\":\"AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\",\"link\":\"\",\"imgUrl\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=path1\"},\"components\":[{\"name\":\"图片\",\"type\":\"image\",\"images\":[{\"url\":\"https://crm.ceshi112.com/fssharehelper/file/getFileBySpliceUrl?path=path1\"}]}]}") | new MemberConfigEntity() | [new HexagonPageEntity(id: "111", name: "预约报名", content: "aaaa"), new HexagonPageEntity(id: "222", name: "提交成功", content: "bbbb")] | false

    }


    def "copySiteTest"() {
        given:
        hexagonSiteDAO.insert(*_) >> hexagonSiteInsertMock
        crmService.listLeadPools(*_) >> new Result<List<LeadPoolResult>>(0, "errMsg", [new LeadPoolResult()])
        customizeFormDataService.addCustomizeFormData(*_) >> new Result<AddCustomizeFormDataResult>(0, "errMsg", new AddCustomizeFormDataResult())
        fileV2Manager.uploadToApath(*_) >> "uploadToApathResponse"
        fileV2Manager.getApathByUrl(*_) >> "getApathByUrlResponse"
        fileV2Manager.downloadAFile(*_) >> null
        fileV2Manager.getSpliceUrl(*_) >> "getSpliceUrlResponse"
        fileV2Manager.copyCFileToCFile(*_) >> null
        customizeFormDataManager.getCustomizeFormDataById(*_) >> new Result<CustomizeFormDataDetailResult>(0, "errMsg", new CustomizeFormDataDetailResult())
        redisManager.setHexagonSiteToRedis(*_) >> hexagonSiteToRedisMock
        redisManager.setHexagonPageToRedis(*_) >> true
        hexagonTemplatePageDAO.getById(*_) >> new HexagonTemplatePageEntity()
        hexagonTemplatePageDAO.getBySiteId(*_) >> hexagonTemplatePageBySiteIdMock
        hexagonPageDAO.getById(*_) >> new HexagonPageEntity()
        hexagonPageDAO.insert(*_) >> 0
        hexagonPageDAO.update(*_) >> 0
        hexagonPageDAO.getByHexagonSiteId(*_) >> getByHexagonSiteIdMock
        hexagonPageDAO.updatePageActionId(*_) >> 1
        def spy = Spy(hexagonManager)
        spy.copyPage(*_) >> copyPageMock
        spy.resetContentActionId(*_) >> null

        when:
        Result<CreateSiteResult> result = spy.copySite("88146", 0, hexagonCopyArg, copyFrom)
        then:
        result.errCode == resultMock.errCode
        where:
        hexagonCopyArg                                                | copyFrom | resultMock                                         | hexagonSiteToRedisMock | hexagonSiteInsertMock | getByHexagonSiteIdMock                                                                                                                                           | copyPageMock             | hexagonTemplatePageBySiteIdMock
        new HexagonCopyArg(id: "111")                                 | 1        | Result.newError(SHErrorCode.OPERATE_DB_FAIL)       | false                  | -1                    | Lists.newArrayList(new HexagonPageEntity(hexagonSiteId: "111", id: "222"))                                                                                       | Result.newSuccess("111") | null
        new HexagonCopyArg(id: "111")                                 | 1        | Result.newError(SHErrorCode.OPERATE_DB_FAIL)       | true                   | -1                    | Lists.newArrayList(new HexagonPageEntity(hexagonSiteId: "111", id: "222"))                                                                                       | Result.newSuccess("111") | null
        new HexagonCopyArg(id: "111", registerIntegralMaterial: true) | 1        | Result.newSuccess(new CreateSiteResult(id: "111")) | 1                      | 1                     | Lists.newArrayList(new HexagonPageEntity(hexagonSiteId: "111", id: "222", isHomepage: 1), new HexagonPageEntity(hexagonSiteId: "111", id: "333", isHomepage: 2)) | Result.newSuccess("111") | null
        new HexagonCopyArg(id: "111", registerIntegralMaterial: true) | 2        | Result.newSuccess(new CreateSiteResult(id: "111")) | 1                      | 1                     | Lists.newArrayList(new HexagonPageEntity(hexagonSiteId: "111", id: "222", isHomepage: 1), new HexagonPageEntity(hexagonSiteId: "111", id: "333", isHomepage: 2)) | Result.newSuccess("111") | Lists.newArrayList(new HexagonTemplatePageEntity(id: "222", hexagonTemplateSiteId: "111", isHomepage: 1), new HexagonTemplatePageEntity(id: "333", hexagonTemplateSiteId: "111", isHomepage: 2))
    }


    def "copyPageTest"() {
        given:
        crmService.listLeadPools(*_) >> listLeadPoolsMock
        customizeFormDataService.addCustomizeFormData(*_) >> addCustomizeFormDataMock
        fileV2Manager.uploadToApath(*_) >> "uploadToApathResponse"
        fileV2Manager.getApathByUrl(*_) >> "getApathByUrlResponse"
        fileV2Manager.downloadAFile(*_) >> null
        fileV2Manager.getSpliceUrl(*_) >> "getSpliceUrlResponse"
        fileV2Manager.copyCFileToCFile(*_) >> null
        customizeFormDataManager.getCustomizeFormDataById(*_) >> getCustomizeFormDataByIdMock
        redisManager.setHexagonPageToRedis(*_) >> setHexagonPageToRedisMock
        hexagonTemplatePageDAO.getById(*_) >> new HexagonTemplatePageEntity()
        hexagonPageDAO.getById(*_) >> hexagonPageEntityMock
        hexagonPageDAO.insert(*_) >> hexagonPageInsertMock
        hexagonPageDAO.update(*_) >> updateMock
        def spy = Spy(hexagonManager)
        spy.resetShareApath(*_) >> "url1"
        spy.resetContentCpath(*_) >> "cpathUrl1"
        spy.resetContentApath(*_) >> "content"
        spy.bindCustomizeFormDataObject(*_) >> null
        when:
        Result<String> result = spy.copyPage("222", "88146", "88146", "111", 1000, copyFrom)
        then:
        result == resultMock
        where:
        copyFrom | resultMock                                   | hexagonPageInsertMock | hexagonPageEntityMock                                  | getCustomizeFormDataByIdMock                                              | setHexagonPageToRedisMock | listLeadPoolsMock                                                                           | addCustomizeFormDataMock                                     | updateMock
        3        | Result.newError(SHErrorCode.PARAMS_ERROR)    | 0                     | new HexagonPageEntity(hexagonSiteId: "111", id: "222") | null                                                                      | true                      | null                                                                                        | Result.newError(SHErrorCode.PARAMS_ERROR)                    | 0
        1        | Result.newError(SHErrorCode.OPERATE_DB_FAIL) | 0                     | new HexagonPageEntity(hexagonSiteId: "111", id: "222") | null                                                                      | true                      | null                                                                                        | Result.newError(SHErrorCode.PARAMS_ERROR)                    | 0
        2        | Result.newError(SHErrorCode.OPERATE_DB_FAIL) | 1                     | new HexagonPageEntity(hexagonSiteId: "111", id: "222") | Result.newSuccess(new CustomizeFormDataDetailResult(crmPoolId: "poolId")) | false                     | null                                                                                        | Result.newError(SHErrorCode.PARAMS_ERROR)                    | 0
        2        | Result.newError(SHErrorCode.OPERATE_DB_FAIL) | 1                     | new HexagonPageEntity(hexagonSiteId: "111", id: "222") | Result.newSuccess(new CustomizeFormDataDetailResult(crmPoolId: "poolId")) | false                     | Result.newSuccess(Lists.newArrayList(new LeadPoolResult(id: "poolId1", name: "poolName1"))) | Result.newError(SHErrorCode.PARAMS_ERROR)                    | 0
        2        | Result.newError(SHErrorCode.OPERATE_DB_FAIL) | 1                     | new HexagonPageEntity(hexagonSiteId: "111", id: "222") | Result.newSuccess(new CustomizeFormDataDetailResult(crmPoolId: "poolId")) | false                     | Result.newSuccess(Lists.newArrayList(new LeadPoolResult(id: "poolId1", name: "poolName1"))) | Result.newSuccess(new AddCustomizeFormDataResult(id: "111")) | 0
        2        | Result.newError(SHErrorCode.OPERATE_DB_FAIL) | 1                     | new HexagonPageEntity(hexagonSiteId: "111", id: "222") | Result.newSuccess(new CustomizeFormDataDetailResult(crmPoolId: "poolId")) | false                     | Result.newSuccess(Lists.newArrayList(new LeadPoolResult(id: "poolId1", name: "poolName1"))) | Result.newSuccess(new AddCustomizeFormDataResult(id: "111")) | 1
    }


    def "hexagonCopySiteTest"() {
        given:
        def spy = Spy(hexagonManager)
        spy.copySite(*_) >> Result.newSuccess(new CreateSiteResult(id: "111", formId: "222", hadCrmMapping: true))
        spy.formMapping(*_) >> Result.newSuccess(new CreateSiteResult(id: "111", formId: "222", hadCrmMapping: true))
        when:
        Result<CreateSiteResult> result = spy.hexagonCopySite("ea", 0, new HexagonCopyArg(), 0)
        then:
        result == new Result<CreateSiteResult>(0, "成功", new CreateSiteResult(id: "111", formId: "222", hadCrmMapping: true))
    }


    def "formMappingTest"() {
        given:
        hexagonSiteDAO.getFormBySiteIds(*_) >> getFormBySiteIdsMock
        crmV2MappingManager.branchVerifyMankeepCrmObjectFieldMapping(*_) >> branchVerifyMankeepCrmObjectFieldMappingMock
        when:
        Result<CreateSiteResult> result = hexagonManager.formMapping(siteResult, "88146")
        then:
        result == resultMock
        where:
        siteResult                      | resultMock                                                                              | getFormBySiteIdsMock                                                            | branchVerifyMankeepCrmObjectFieldMappingMock
        new CreateSiteResult(id: "111") | Result.newSuccess(new CreateSiteResult(id: "111", formId: "222", hadCrmMapping: true))  | Lists.newArrayList(new HexagonSiteListDTO(hexagonSiteId: "111", formId: "222")) | new HashMap<String, Boolean>("222": true)
        new CreateSiteResult(id: "111") | Result.newSuccess(new CreateSiteResult(id: "111", formId: "222", hadCrmMapping: false)) | Lists.newArrayList(new HexagonSiteListDTO(hexagonSiteId: "111", formId: "222")) | null
    }


    def "getHexagonSiteHomepageDetailTest"() {
        given:
        hexagonSiteDAO.getInclueDeletedById(*_) >> inlcludeDeletetedHexagonSiteEntityMock
        fileV2Manager.getUrlByPath(*_) >> "url1"
        fileV2Manager.batchGetUrlByPath(*_) >> ["path1": "url1"]
        photoManager.querySinglePhoto(*_) >> new PhotoEntity()
        materialRelationDao.queryMaterialRelationByObjectId(*_) >> queryMaterialRelationByObjectIdMock
        redisManager.setHexagonSiteToRedis(*_) >> true
        redisManager.getHexagonSite(*_) >> redisHexagonSiteEntityMock
        redisManager.setHexagonHomePageToRedis(*_) >> true
        redisManager.getHexagonHomePage(*_) >> redisHexagonHomePageEntityMock
        hexagonPageDAO.getHomePage(*_) >> getHomePageMock

        when:
        Result<GetPageDetailResult> result = hexagonManager.getHexagonSiteHomepageDetail(siteId)
        then:
        result == resultMock
        where:
        siteId | resultMock                                                                                                           | redisHexagonSiteEntityMock                                    | inlcludeDeletetedHexagonSiteEntityMock                        | queryMaterialRelationByObjectIdMock                   | redisHexagonHomePageEntityMock                                                          | getHomePageMock
        "11"   | Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND)                                                                  | null                                                          | null                                                          | null                                                  | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_SITE_DELETED)                                                                    | null                                                          | new HexagonSiteEntity(status: HexagonStatusEnum.DELETED.type) | null                                                  | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_SITE_STOPED)                                                                     | null                                                          | new HexagonSiteEntity(status: HexagonStatusEnum.STOPED.type)  | null                                                  | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND)                                                                  | null                                                          | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | null                                                  | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_SITE_STOPED)                                                                     | new HexagonSiteEntity(status: HexagonStatusEnum.STOPED.type)  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | null                                                  | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_SITE_DELETED)                                                                    | new HexagonSiteEntity(status: HexagonStatusEnum.DELETED.type) | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | null                                                  | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_SITE_DELETED)                                                                    | new HexagonSiteEntity(status: HexagonStatusEnum.DELETED.type) | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | null                                                  | null                                                                                    | null
        "11"   | Result.newSuccess(new GetPageDetailResult(sharePosterAPath: "path1", sharePosterUrl: "url1", sharePicH5Url: "url1")) | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new MaterialRelationEntity(sharePosterAPath: "path1") | new HexagonPageEntity(sharePicH5Apath: "path1", status: HexagonStatusEnum.NORMAL.type)  | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_PAGE_DELETED)                                                                    | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new MaterialRelationEntity(sharePosterAPath: "path1") | new HexagonPageEntity(sharePicH5Apath: "path1", status: HexagonStatusEnum.DELETED.type) | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND)                                                                  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new MaterialRelationEntity(sharePosterAPath: "path1") | null                                                                                    | null
        "11"   | Result.newError(SHErrorCode.HEXAGON_PAGE_DELETED)                                                                    | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new MaterialRelationEntity(sharePosterAPath: "path1") | null                                                                                    | new HexagonPageEntity(sharePicH5Apath: "path1", status: HexagonStatusEnum.DELETED.type)
        "11"   | Result.newSuccess(new GetPageDetailResult(sharePosterAPath: "path1", sharePosterUrl: "url1", sharePicH5Url: "url1")) | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new HexagonSiteEntity(status: HexagonStatusEnum.NORMAL.type)  | new MaterialRelationEntity(sharePosterAPath: "path1") | null                                                                                    | new HexagonPageEntity(sharePicH5Apath: "path1", status: HexagonStatusEnum.NORMAL.type, sharePicMpApath: "apath1")
    }


    def "getTemplateSiteHomepageDetailTest"() {
        given:
        fileV2Manager.batchGetUrlByPath(*_) >> ["path1": "url1", "path2": "url2"]
        hexagonTemplatePageDAO.getHomePage(*_) >> hexagonTemplateSiteEntityMock
        hexagonTemplateSiteDAO.queryByIdIgnoreStatus(*_) >> queryByIdIgnoreStatusMock

        when:
        Result<GetPageDetailResult> result = hexagonManager.getTemplateSiteHomepageDetail(arg)
        then:
        result == resultMock
        where:
        arg   | resultMock                                                                               | queryByIdIgnoreStatusMock                                            | hexagonTemplateSiteEntityMock
        "111" | Result.newError(SHErrorCode.HEXAGON_TEMPLATE_SITE_NOT_FOUND)                             | null                                                                 | null
        "111" | Result.newError(SHErrorCode.HEXAGON_SITE_STOPED)                                         | new HexagonTemplateSiteEntity(status: HexagonStatusEnum.STOPED.type) | null
        "111" | Result.newError(SHErrorCode.HEXAGON_TEMPLATE_PAGE_NOT_FOUND)                             | new HexagonTemplateSiteEntity(status: HexagonStatusEnum.NORMAL.type) | null
        "111" | Result.newSuccess(new GetPageDetailResult(sharePicH5Url: "url1", sharePicMpUrl: "url2")) | new HexagonTemplateSiteEntity(status: HexagonStatusEnum.NORMAL.type) | new HexagonTemplatePageEntity(sharePicH5Apath: "path1", sharePicMpApath: "path2")
    }


    def "getHomepageDetailBySiteIdTest"() {
        given:
        hexagonSiteDAO.getById(*_) >> hexagonSiteEntityMock
        hexagonSiteDAO.getInclueDeletedById(*_) >> new HexagonSiteEntity()
        fileV2Manager.getUrlByPath(*_) >> "http://www.baidu.com"
        fileV2Manager.batchGetUrlByPath(*_) >> ["batchGetUrlByPathResponse": "batchGetUrlByPathResponse"]
        photoManager.querySinglePhoto(*_) >> new PhotoEntity()
        photoManager.querySingleCpathPhoto(*_) >> new PhotoEntity(thumbnailUrl: "http://www.baidu.com")
        memberConfigDao.getByEa(*_) >> memberConfigEntityMock
        materialRelationDao.queryMaterialRelationByObjectId(*_) >> new MaterialRelationEntity()
        customizeFormDataManager.batchGetFormUsageByFormIds(*_) >> ["batchGetFormUsageByFormIdsResponse": 0]
        memberManager.getMemberApproveStatus(*_) >> getMemberApproveStatusMock
        redisManager.setHexagonSiteToRedis(*_) >> true
        redisManager.getHexagonSite(*_) >> new HexagonSiteEntity()
        redisManager.setHexagonHomePageToRedis(*_) >> true
        redisManager.getHexagonHomePage(*_) >> new HexagonPageEntity()
        hexagonTemplatePageDAO.getHomePage(*_) >> new HexagonTemplatePageEntity()
        hexagonPageDAO.getHomePage(*_) >> new HexagonPageEntity()
        hexagonTemplateSiteDAO.queryByIdIgnoreStatus(*_) >> new HexagonTemplateSiteEntity()
        def spy = Spy(hexagonManager)
        spy.getHexagonSiteHomepageDetail(*_) >> getHexagonSiteHomepageDetailMock
        spy.getTemplateSiteHomepageDetail(*_) >> new Result<GetPageDetailResult>(0, "errMsg", new GetPageDetailResult())
        when:
        Result<GetPageDetailResult> result = spy.getHomepageDetailBySiteId(arg)
        then:
        result == resultMock
        where:
        arg                                                                            | resultMock                                                                                                                                                                                                                                                                                      | hexagonSiteEntityMock   | getHexagonSiteHomepageDetailMock                                                                                                                                                                                                            | memberConfigEntityMock                          | getMemberApproveStatusMock
        new HexagonhomepageDetailArg(type: HexagonPreviewEnum.SITE.getType())          | Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND)                                                                                                                                                                                                                                             | null                    | new Result<GetPageDetailResult>(data: new GetPageDetailResult())                                                                                                                                                                            | null                                            | Optional.empty()
        new HexagonhomepageDetailArg(type: HexagonPreviewEnum.TEMPLATE_SITE.getType()) | Result.newError(SHErrorCode.HEXAGON_PAGE_NOT_FOUND)                                                                                                                                                                                                                                             | null                    | new Result<GetPageDetailResult>(data: new GetPageDetailResult())                                                                                                                                                                            | null                                            | Optional.empty()
        new HexagonhomepageDetailArg(type: HexagonPreviewEnum.SITE.getType())          | Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND)                                                                                                                                                                                                                                             | null                    | new Result<GetPageDetailResult>(data: new GetPageDetailResult(hexagonSiteId: "111", formId: "222"))                                                                                                                                         | null                                            | Optional.empty()
        new HexagonhomepageDetailArg(type: HexagonPreviewEnum.SITE.getType())          | Result.newError(SHErrorCode.HEXAGON_SITE_NOT_FOUND)                                                                                                                                                                                                                                             | null                    | new Result<GetPageDetailResult>(data: new GetPageDetailResult(hexagonSiteId: "111", formId: "222"))                                                                                                                                         | null                                            | Optional.empty()
        new HexagonhomepageDetailArg(type: HexagonPreviewEnum.SITE.getType())          | Result.newSuccess(new GetPageDetailResult(updateMemberInfoSite: false, hexagonSiteId: "111", formId: "222", sharePicMpCutUrl: "http://www.baidu.com", sharePicMiniAppCutUrl: "http://www.baidu.com", sharePicOrdinaryCutUrl: "http://www.baidu.com", sharePicH5CutUrl: "http://www.baidu.com")) | new HexagonSiteEntity() | new Result<GetPageDetailResult>(data: new GetPageDetailResult(hexagonSiteId: "111", formId: "222", /*sharePicMpCutUrl: "http://www.baidu.com",*/ sharePicH5CutUrl: "http://www.baidu.com", sharePicOrdinaryCutUrl: "http://www.baidu.com")) | null                                            | Optional.empty()
        new HexagonhomepageDetailArg(type: HexagonPreviewEnum.SITE.getType())          | Result.newSuccess(new GetPageDetailResult(updateMemberInfoSite: true, hexagonSiteId: "111", formId: "222", sharePicMpCutUrl: "http://www.baidu.com", sharePicMiniAppCutUrl: "http://www.baidu.com", sharePicOrdinaryCutUrl: "http://www.baidu.com", sharePicH5CutUrl: "http://www.baidu.com"))  | new HexagonSiteEntity() | new Result<GetPageDetailResult>(data: new GetPageDetailResult(hexagonSiteId: "111", formId: "222", /*sharePicMpCutUrl: "http://www.baidu.com",*/ sharePicH5CutUrl: "http://www.baidu.com", sharePicOrdinaryCutUrl: "http://www.baidu.com")) | new MemberConfigEntity(updateInfoSiteId: "111") | Optional.empty()
    }
}